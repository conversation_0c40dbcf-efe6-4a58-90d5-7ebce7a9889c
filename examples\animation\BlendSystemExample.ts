/**
 * 动画混合系统示例
 * 展示如何使用动画混合系统的高级功能
 */
import { Engine } from '../../engine/src/Engine';
import { Scene } from '../../engine/src/Scene';
import { Entity } from '../../engine/src/core/Entity';
import { AnimationSystem } from '../../engine/src/animation/AnimationSystem';
import { AnimationBlender, BlendMode, BlendCurveType } from '../../engine/src/animation/AnimationBlender';
import { AnimationClip } from '../../engine/src/animation/AnimationClip';
import { Animator } from '../../engine/src/animation/Animator';
import { AnimationMask, MaskType, MaskWeightType } from '../../engine/src/animation/AnimationMask';
import { SubClip } from '../../engine/src/animation/SubClip';
import { AnimationSubClip } from '../../engine/src/animation/AnimationSubClip';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import * as THREE from 'three';

/**
 * 动画混合系统示例类
 */
export class BlendSystemExample {
  private engine: Engine;
  private scene: Scene;
  private animationSystem: AnimationSystem;
  private character: Entity | null = null;
  private animator: Animator | null = null;
  private blender: AnimationBlender | null = null;
  private clips: Map<string, AnimationClip> = new Map();
  private gui: any; // dat.GUI实例

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    this.scene = this.engine.getScene();
    this.animationSystem = this.engine.getSystem(AnimationSystem) as AnimationSystem;

    // 初始化场景
    this.initScene();

    // 加载角色模型
    this.loadCharacter();

    // 创建GUI
    this.createGUI();
  }

  /**
   * 初始化场景
   */
  private initScene(): void {
    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 2);
    this.scene.add(ambientLight);

    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 1, 1);
    this.scene.add(directionalLight);

    // 添加地面
    const ground = new THREE.Mesh(
      new THREE.PlaneGeometry(10, 10),
      new THREE.MeshStandardMaterial({ color: 0x808080 })
    );
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);
  }

  /**
   * 加载角色模型
   */
  private loadCharacter(): void {
    const loader = new GLTFLoader();
    loader.load('/models/character.glb', (gltf) => {
      // 创建角色实体
      this.character = new Entity('character');
      this.character.add(gltf.scene);

      // 添加到场景
      this.scene.add(this.character);

      // 创建动画控制器
      this.animator = new Animator(this.character);

      // 创建混合器
      this.blender = new AnimationBlender(this.animator);

      // 启用性能优化
      this.blender.setCacheConfig(true, 1000);
      this.blender.setObjectPoolConfig(true);
      this.blender.setBatchProcessingConfig(true);

      // 加载动画片段
      this.loadAnimationClips(gltf.animations);

      // 创建混合示例
      this.setupBlendingExamples();
    });
  }

  /**
   * 加载动画片段
   * @param animations 动画数组
   */
  private loadAnimationClips(animations: THREE.AnimationClip[]): void {
    if (!this.animator) return;

    // 加载所有动画片段
    for (const clip of animations) {
      const animationClip = new AnimationClip(clip);
      this.clips.set(clip.name, animationClip);
      this.animator.addClip(clip.name, animationClip);
    }

    // 创建子片段
    this.createSubClips();

    // 创建遮罩
    this.createMasks();
  }

  /**
   * 创建子片段
   */
  private createSubClips(): void {
    if (!this.blender || !this.animator) return;

    // 假设有一个"走路"动画
    if (this.clips.has('walk')) {
      // 创建子片段：快速走路
      const fastWalk = this.blender.createSubClip(
        'fastWalk',
        'walk',
        0,
        1,
        true
      );
      fastWalk.setTimeScale(1.5);

      // 创建子片段：慢速走路
      const slowWalk = this.blender.createSubClip(
        'slowWalk',
        'walk',
        0,
        1,
        true
      );
      slowWalk.setTimeScale(0.7);

      // 创建高级子片段：走路循环
      const walkLoop = new AnimationSubClip({
        name: 'walkLoop',
        sourceClipName: 'walk',
        startTime: 0.2,
        endTime: 0.8,
        loop: true,
        timeScale: 1.0,
        blendMode: BlendMode.OVERRIDE
      });
      this.blender.addAdvancedSubClip(walkLoop);
    }
  }

  /**
   * 创建遮罩
   */
  private createMasks(): void {
    if (!this.blender) return;

    // 创建上半身遮罩
    const upperBodyMask = this.blender.createUpperBodyMask();

    // 创建下半身遮罩
    const lowerBodyMask = this.blender.createLowerBodyMask();

    // 创建左手遮罩
    const leftHandMask = this.blender.createLeftHandMask();

    // 创建右手遮罩
    const rightHandMask = this.blender.createRightHandMask();

    // 创建自定义遮罩
    const customMask = this.blender.createMask(
      'customMask',
      MaskType.INCLUDE,
      ['spine', 'neck', 'head'],
      MaskWeightType.SMOOTH
    );
  }

  /**
   * 设置混合示例
   */
  private setupBlendingExamples(): void {
    if (!this.blender) return;

    // 示例1：基本混合
    this.setupBasicBlending();

    // 示例2：高级混合
    this.setupAdvancedBlending();

    // 示例3：遮罩混合
    this.setupMaskBlending();

    // 示例4：曲线混合
    this.setupCurveBlending();

    // 示例5：子片段混合
    this.setupSubClipBlending();
  }

  /**
   * 设置基本混合示例
   */
  private setupBasicBlending(): void {
    if (!this.blender) return;

    // 添加基本混合层
    if (this.clips.has('idle') && this.clips.has('walk')) {
      // 添加空闲动画层
      const idleLayerIndex = this.blender.addLayer('idle', 1.0, BlendMode.OVERRIDE);

      // 添加走路动画层
      const walkLayerIndex = this.blender.addLayer('walk', 0.0, BlendMode.OVERRIDE);

      // 创建混合控制函数
      const blendIdleToWalk = (weight: number, time: number = 1.0) => {
        this.blender?.setLayerWeight(idleLayerIndex, 1.0 - weight, time);
        this.blender?.setLayerWeight(walkLayerIndex, weight, time);
      };

      // 添加到GUI
      if (this.gui) {
        const basicFolder = this.gui.addFolder('基本混合');
        basicFolder.add({ weight: 0 }, 'weight', 0, 1, 0.01).onChange((value: number) => {
          blendIdleToWalk(value, 0.5);
        });
        basicFolder.open();
      }
    }
  }

  /**
   * 设置高级混合示例
   */
  private setupAdvancedBlending(): void {
    if (!this.blender) return;

    // 添加高级混合层
    if (this.clips.has('run') && this.clips.has('jump')) {
      // 添加跑步动画层
      const runLayerIndex = this.blender.addLayer('run', 0.0, BlendMode.ADDITIVE);

      // 添加跳跃动画层
      const jumpLayerIndex = this.blender.addLayer('jump', 0.0, BlendMode.ADDITIVE);

      // 创建混合控制函数
      const blendRunAndJump = (runWeight: number, jumpWeight: number, time: number = 1.0) => {
        this.blender?.setLayerWeight(runLayerIndex, runWeight, time);
        this.blender?.setLayerWeight(jumpLayerIndex, jumpWeight, time);
      };

      // 添加到GUI
      if (this.gui) {
        const advancedFolder = this.gui.addFolder('高级混合');
        const params = { run: 0, jump: 0 };
        advancedFolder.add(params, 'run', 0, 1, 0.01).onChange(() => {
          blendRunAndJump(params.run, params.jump, 0.3);
        });
        advancedFolder.add(params, 'jump', 0, 1, 0.01).onChange(() => {
          blendRunAndJump(params.run, params.jump, 0.3);
        });
        advancedFolder.open();
      }
    }
  }

  /**
   * 设置遮罩混合示例
   */
  private setupMaskBlending(): void {
    if (!this.blender) return;

    // 添加遮罩混合层
    if (this.clips.has('walk') && this.clips.has('wave')) {
      // 添加走路动画层（下半身）
      const walkLayerIndex = this.blender.addLayer('walk', 1.0, BlendMode.OVERRIDE, 1.0, ['lowerBody']);

      // 添加挥手动画层（上半身）
      const waveLayerIndex = this.blender.addLayer('wave', 0.0, BlendMode.OVERRIDE, 1.0, ['upperBody']);

      // 创建混合控制函数
      const blendWithMasks = (waveWeight: number, time: number = 1.0) => {
        this.blender?.setLayerWeight(waveLayerIndex, waveWeight, time);
      };

      // 添加到GUI
      if (this.gui) {
        const maskFolder = this.gui.addFolder('遮罩混合');
        maskFolder.add({ wave: 0 }, 'wave', 0, 1, 0.01).onChange((value: number) => {
          blendWithMasks(value, 0.3);
        });
        maskFolder.open();
      }
    }
  }

  /**
   * 设置曲线混合示例
   */
  private setupCurveBlending(): void {
    if (!this.blender) return;

    // 设置混合曲线
    const curveTypes = [
      { name: '线性', type: BlendCurveType.LINEAR },
      { name: '缓入', type: BlendCurveType.EASE_IN },
      { name: '缓出', type: BlendCurveType.EASE_OUT },
      { name: '缓入缓出', type: BlendCurveType.EASE_IN_OUT },
      { name: '弹性', type: BlendCurveType.ELASTIC },
      { name: '弹跳', type: BlendCurveType.BOUNCE },
      { name: '正弦', type: BlendCurveType.SINE }
    ];

    // 添加到GUI
    if (this.gui) {
      const curveFolder = this.gui.addFolder('曲线混合');
      const params = { curve: '线性' };
      curveFolder.add(params, 'curve', curveTypes.map(c => c.name)).onChange((value: string) => {
        const curveType = curveTypes.find(c => c.name === value)?.type || BlendCurveType.LINEAR;
        this.blender?.setBlendCurveType(curveType);
      });
      curveFolder.open();
    }
  }

  /**
   * 设置子片段混合示例
   */
  private setupSubClipBlending(): void {
    if (!this.blender) return;

    // 添加子片段混合层
    const subClips = ['fastWalk', 'slowWalk', 'walkLoop'];
    const subClipLayers: number[] = [];

    for (const clipName of subClips) {
      const layerIndex = this.blender.addLayer(clipName, 0.0, BlendMode.OVERRIDE);
      subClipLayers.push(layerIndex);
    }

    // 创建混合控制函数
    const blendSubClips = (clipIndex: number, weight: number, time: number = 1.0) => {
      // 重置所有子片段权重
      for (let i = 0; i < subClipLayers.length; i++) {
        this.blender?.setLayerWeight(subClipLayers[i], i === clipIndex ? weight : 0, time);
      }
    };

    // 添加到GUI
    if (this.gui) {
      const subClipFolder = this.gui.addFolder('子片段混合');
      const params = { clip: '无', weight: 0 };
      subClipFolder.add(params, 'clip', ['无', ...subClips]).onChange((value: string) => {
        const clipIndex = subClips.indexOf(value);
        if (clipIndex >= 0) {
          blendSubClips(clipIndex, params.weight, 0.5);
        } else {
          // 重置所有子片段权重
          for (const layerIndex of subClipLayers) {
            this.blender?.setLayerWeight(layerIndex, 0, 0.5);
          }
        }
      });
      subClipFolder.add(params, 'weight', 0, 1, 0.01).onChange((value: number) => {
        const clipIndex = subClips.indexOf(params.clip);
        if (clipIndex >= 0) {
          blendSubClips(clipIndex, value, 0.5);
        }
      });
      subClipFolder.open();
    }
  }

  /**
   * 创建GUI
   */
  private createGUI(): void {
    // 使用dat.GUI创建界面
    // 注意：这里需要导入dat.GUI库
    try {
      const dat = require('dat.gui');
      this.gui = new dat.GUI();
    } catch (e) {
      console.warn('dat.GUI not available');
    }
  }

  /**
   * 启动示例
   */
  public start(): void {
    this.engine.start();
  }

  /**
   * 停止示例
   */
  public stop(): void {
    this.engine.stop();
  }
}
