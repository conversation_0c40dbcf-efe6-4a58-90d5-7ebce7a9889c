/**
 * 角色环境交互示例
 * 
 * 本示例展示了如何使用DL（Digital Learning）引擎的角色环境交互功能，包括环境感知、物理交互、动作录制和回放等功能。
 */

import * as THREE from 'three';
import {
  Engine,
  World,
  Scene,
  Entity,
  Camera,
  Renderer,
  Transform,
  Light,
  MeshRenderer,
  Material,
  Texture,
  Input,
  Debug
} from '../../engine/src/core';

import {
  AdvancedCharacterController,
  ActionControlSystem,
  ActionType,
  ActionPriority,
  ActionData
} from '../../engine/src/avatar/controllers';

import {
  EnvironmentAwarenessComponent,
  EnvironmentResponseComponent,
  EnvironmentAwarenessSystem,
  ResponseType,
  ResponsePriority,
  EnvironmentType,
  WeatherType,
  TerrainType
} from '../../engine/src/environment';

import {
  PhysicsSystem,
  PhysicsBodyComponent,
  PhysicsColliderComponent,
  PhysicsInteractionComponent
} from '../../engine/src/physics';

import {
  AnimationSystem,
  AnimationComponent,
  AnimationClip
} from '../../engine/src/animation';

import {
  ActionRecorder,
  ActionPlayback
} from '../../engine/src/avatar/recording';

/**
 * 角色环境交互示例类
 */
class CharacterEnvironmentInteractionExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: Scene;
  /** 相机 */
  private camera: Camera;
  /** 渲染器 */
  private renderer: Renderer;
  /** 角色实体 */
  private character: Entity;
  /** 角色控制器 */
  private characterController: AdvancedCharacterController;
  /** 环境感知组件 */
  private awarenessComponent: EnvironmentAwarenessComponent;
  /** 环境响应组件 */
  private responseComponent: EnvironmentResponseComponent;
  /** 物理交互组件 */
  private interactionComponent: PhysicsInteractionComponent;
  /** 动作控制系统 */
  private actionControlSystem: ActionControlSystem;
  /** 环境感知系统 */
  private environmentAwarenessSystem: EnvironmentAwarenessSystem;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 动画系统 */
  private animationSystem: AnimationSystem;
  /** 当前录制 */
  private currentRecording: any = null;
  /** 是否正在录制 */
  private isRecording: boolean = false;
  /** 是否正在回放 */
  private isPlaying: boolean = false;
  /** UI元素 */
  private ui: {
    recordButton: HTMLButtonElement;
    playButton: HTMLButtonElement;
    stopButton: HTMLButtonElement;
    saveButton: HTMLButtonElement;
    loadButton: HTMLButtonElement;
    statusText: HTMLDivElement;
    recordingsList: HTMLSelectElement;
  };

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();

    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);

    // 创建场景
    this.scene = new Scene();
    this.world.setScene(this.scene);

    // 创建相机
    this.camera = new Camera();
    this.camera.position.set(0, 5, 10);
    this.camera.lookAt(0, 0, 0);
    this.world.addEntity(this.camera);

    // 创建渲染器
    this.renderer = new Renderer({
      antialias: true,
      shadows: true
    });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    document.body.appendChild(this.renderer.domElement);

    // 创建系统
    this.createSystems();

    // 创建场景
    this.createScene();

    // 创建角色
    this.createCharacter();

    // 创建UI
    this.createUI();

    // 添加事件监听器
    this.addEventListeners();

    // 开始渲染循环
    this.engine.start();
  }

  /**
   * 创建系统
   */
  private createSystems(): void {
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem(this.world, {
      gravity: new THREE.Vector3(0, -9.8, 0),
      debug: true
    });
    this.world.addSystem(this.physicsSystem);

    // 创建动画系统
    this.animationSystem = new AnimationSystem(this.world, {
      debug: true
    });
    this.world.addSystem(this.animationSystem);

    // 创建动作控制系统
    this.actionControlSystem = new ActionControlSystem(this.world, {
      debug: true,
      enableActionRecording: true,
      enableActionPlayback: true
    });
    this.world.addSystem(this.actionControlSystem);

    // 创建环境感知系统
    this.environmentAwarenessSystem = new EnvironmentAwarenessSystem(this.world, {
      debug: true,
      autoDetect: true,
      enableResponse: true
    });
    this.world.addSystem(this.environmentAwarenessSystem);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建地面
    const ground = new Entity(this.world);
    ground.name = '地面';
    
    // 添加变换组件
    const groundTransform = new Transform({
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Quaternion(),
      scale: new THREE.Vector3(20, 1, 20)
    });
    ground.addComponent(groundTransform);
    
    // 添加网格渲染器组件
    const groundMeshRenderer = new MeshRenderer({
      geometry: 'box',
      material: new Material({
        color: 0x888888,
        roughness: 0.8,
        metalness: 0.2
      })
    });
    ground.addComponent(groundMeshRenderer);
    
    // 添加物理碰撞器组件
    const groundCollider = new PhysicsColliderComponent({
      type: 'box',
      size: new THREE.Vector3(20, 1, 20),
      isTrigger: false,
      material: 'default'
    });
    ground.addComponent(groundCollider);
    
    // 添加到场景
    this.scene.addEntity(ground);
    
    // 创建光源
    const light = new Entity(this.world);
    light.name = '光源';
    
    // 添加变换组件
    const lightTransform = new Transform({
      position: new THREE.Vector3(5, 10, 5),
      rotation: new THREE.Quaternion(),
      scale: new THREE.Vector3(1, 1, 1)
    });
    light.addComponent(lightTransform);
    
    // 添加光源组件
    const lightComponent = new Light({
      type: 'directional',
      color: 0xffffff,
      intensity: 1.0,
      castShadow: true
    });
    light.addComponent(lightComponent);
    
    // 添加到场景
    this.scene.addEntity(light);
    
    // 创建环境对象
    this.createEnvironmentObjects();
  }

  /**
   * 创建环境对象
   */
  private createEnvironmentObjects(): void {
    // 创建一些环境对象，如树木、岩石等
    // 这里简化为几个立方体
    
    // 创建可交互的箱子
    const box = new Entity(this.world);
    box.name = '箱子';
    
    // 添加变换组件
    const boxTransform = new Transform({
      position: new THREE.Vector3(3, 0.5, 0),
      rotation: new THREE.Quaternion(),
      scale: new THREE.Vector3(1, 1, 1)
    });
    box.addComponent(boxTransform);
    
    // 添加网格渲染器组件
    const boxMeshRenderer = new MeshRenderer({
      geometry: 'box',
      material: new Material({
        color: 0xaa8844,
        roughness: 0.5,
        metalness: 0.3
      })
    });
    box.addComponent(boxMeshRenderer);
    
    // 添加物理碰撞器组件
    const boxCollider = new PhysicsColliderComponent({
      type: 'box',
      size: new THREE.Vector3(1, 1, 1),
      isTrigger: false,
      material: 'wood'
    });
    box.addComponent(boxCollider);
    
    // 添加物理刚体组件
    const boxBody = new PhysicsBodyComponent({
      type: 'dynamic',
      mass: 10,
      linearDamping: 0.1,
      angularDamping: 0.1
    });
    box.addComponent(boxBody);
    
    // 添加到场景
    this.scene.addEntity(box);
  }

  /**
   * 创建角色
   */
  private createCharacter(): void {
    // 创建角色实体
    this.character = new Entity(this.world);
    this.character.name = '角色';
    
    // 添加变换组件
    const characterTransform = new Transform({
      position: new THREE.Vector3(0, 1, 0),
      rotation: new THREE.Quaternion(),
      scale: new THREE.Vector3(1, 1, 1)
    });
    this.character.addComponent(characterTransform);
    
    // 添加网格渲染器组件
    const characterMeshRenderer = new MeshRenderer({
      geometry: 'capsule',
      material: new Material({
        color: 0x4488cc,
        roughness: 0.7,
        metalness: 0.3
      })
    });
    this.character.addComponent(characterMeshRenderer);
    
    // 添加物理碰撞器组件
    const characterCollider = new PhysicsColliderComponent({
      type: 'capsule',
      radius: 0.5,
      height: 1.0,
      isTrigger: false,
      material: 'character'
    });
    this.character.addComponent(characterCollider);
    
    // 添加物理刚体组件
    const characterBody = new PhysicsBodyComponent({
      type: 'dynamic',
      mass: 70,
      linearDamping: 0.1,
      angularDamping: 0.1
    });
    this.character.addComponent(characterBody);
    
    // 添加高级角色控制器
    this.characterController = new AdvancedCharacterController(this.character, {
      walkSpeed: 2.0,
      runSpeed: 5.0,
      jumpForce: 5.0,
      gravity: 9.8,
      usePhysics: true,
      useStateMachine: true,
      useBlendSpace: true,
      useIK: true,
      useEnvironmentAwareness: true
    });
    this.character.addComponent(this.characterController);
    
    // 添加环境感知组件
    this.awarenessComponent = new EnvironmentAwarenessComponent(this.character, {
      awarenessRange: 50,
      updateFrequency: 1000,
      debug: true,
      autoDetect: true
    });
    this.character.addComponent(this.awarenessComponent);
    
    // 添加环境响应组件
    this.responseComponent = new EnvironmentResponseComponent(this.character, {
      autoRespond: true,
      debug: true
    });
    this.character.addComponent(this.responseComponent);
    
    // 添加物理交互组件
    this.interactionComponent = new PhysicsInteractionComponent(this.character, {
      interactionRange: 2.0,
      maxForce: 1000,
      debug: true
    });
    this.character.addComponent(this.interactionComponent);
    
    // 添加到场景
    this.scene.addEntity(this.character);
    
    // 注册角色控制器到动作控制系统
    this.actionControlSystem.registerController(this.character, this.characterController);
    
    // 注册一些动作
    this.registerActions();
  }

  /**
   * 注册动作
   */
  private registerActions(): void {
    // 注册一些基本动作
    
    // 待机动作
    this.actionControlSystem.registerAction({
      id: 'idle',
      name: '待机',
      type: ActionType.BASIC,
      priority: ActionPriority.LOW,
      animationName: 'idle',
      interruptible: true,
      loop: true,
      transitionTime: 0.3,
      duration: 0
    });
    
    // 行走动作
    this.actionControlSystem.registerAction({
      id: 'walk',
      name: '行走',
      type: ActionType.BASIC,
      priority: ActionPriority.LOW,
      animationName: 'walk',
      interruptible: true,
      loop: true,
      transitionTime: 0.3,
      duration: 0
    });
    
    // 跑步动作
    this.actionControlSystem.registerAction({
      id: 'run',
      name: '跑步',
      type: ActionType.BASIC,
      priority: ActionPriority.LOW,
      animationName: 'run',
      interruptible: true,
      loop: true,
      transitionTime: 0.3,
      duration: 0
    });
    
    // 跳跃动作
    this.actionControlSystem.registerAction({
      id: 'jump',
      name: '跳跃',
      type: ActionType.BASIC,
      priority: ActionPriority.MEDIUM,
      animationName: 'jump',
      interruptible: false,
      loop: false,
      transitionTime: 0.1,
      duration: 1.0
    });
    
    // 推动动作
    this.actionControlSystem.registerAction({
      id: 'push',
      name: '推动',
      type: ActionType.PHYSICS_INTERACTION,
      priority: ActionPriority.MEDIUM,
      animationName: 'push',
      interruptible: true,
      loop: true,
      transitionTime: 0.3,
      duration: 0
    });
    
    // 拉动动作
    this.actionControlSystem.registerAction({
      id: 'pull',
      name: '拉动',
      type: ActionType.PHYSICS_INTERACTION,
      priority: ActionPriority.MEDIUM,
      animationName: 'pull',
      interruptible: true,
      loop: true,
      transitionTime: 0.3,
      duration: 0
    });
    
    // 雨天反应动作
    this.actionControlSystem.registerAction({
      id: 'rain_reaction',
      name: '雨天反应',
      type: ActionType.ENVIRONMENT_RESPONSE,
      priority: ActionPriority.LOW,
      animationName: 'rain_reaction',
      interruptible: true,
      loop: true,
      transitionTime: 0.5,
      duration: 0
    });
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    uiContainer.style.color = 'white';
    uiContainer.style.borderRadius = '5px';
    document.body.appendChild(uiContainer);
    
    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '角色环境交互示例';
    title.style.margin = '0 0 10px 0';
    uiContainer.appendChild(title);
    
    // 创建状态文本
    const statusText = document.createElement('div');
    statusText.textContent = '就绪';
    statusText.style.margin = '0 0 10px 0';
    uiContainer.appendChild(statusText);
    this.ui = { statusText } as any;
    
    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.gap = '5px';
    buttonContainer.style.marginBottom = '10px';
    uiContainer.appendChild(buttonContainer);
    
    // 创建录制按钮
    const recordButton = document.createElement('button');
    recordButton.textContent = '录制';
    recordButton.style.padding = '5px 10px';
    buttonContainer.appendChild(recordButton);
    this.ui.recordButton = recordButton;
    
    // 创建播放按钮
    const playButton = document.createElement('button');
    playButton.textContent = '播放';
    playButton.style.padding = '5px 10px';
    buttonContainer.appendChild(playButton);
    this.ui.playButton = playButton;
    
    // 创建停止按钮
    const stopButton = document.createElement('button');
    stopButton.textContent = '停止';
    stopButton.style.padding = '5px 10px';
    buttonContainer.appendChild(stopButton);
    this.ui.stopButton = stopButton;
    
    // 创建保存按钮
    const saveButton = document.createElement('button');
    saveButton.textContent = '保存';
    saveButton.style.padding = '5px 10px';
    buttonContainer.appendChild(saveButton);
    this.ui.saveButton = saveButton;
    
    // 创建加载按钮
    const loadButton = document.createElement('button');
    loadButton.textContent = '加载';
    loadButton.style.padding = '5px 10px';
    buttonContainer.appendChild(loadButton);
    this.ui.loadButton = loadButton;
    
    // 创建录制列表
    const recordingsListLabel = document.createElement('div');
    recordingsListLabel.textContent = '录制列表:';
    recordingsListLabel.style.margin = '10px 0 5px 0';
    uiContainer.appendChild(recordingsListLabel);
    
    const recordingsList = document.createElement('select');
    recordingsList.style.width = '100%';
    recordingsList.style.padding = '5px';
    uiContainer.appendChild(recordingsList);
    this.ui.recordingsList = recordingsList;
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 录制按钮点击事件
    this.ui.recordButton.addEventListener('click', () => {
      if (this.isRecording) {
        // 停止录制
        this.stopRecording();
      } else {
        // 开始录制
        this.startRecording();
      }
    });
    
    // 播放按钮点击事件
    this.ui.playButton.addEventListener('click', () => {
      if (this.isPlaying) {
        // 暂停播放
        this.pausePlayback();
      } else {
        // 开始播放
        this.startPlayback();
      }
    });
    
    // 停止按钮点击事件
    this.ui.stopButton.addEventListener('click', () => {
      if (this.isRecording) {
        // 停止录制
        this.stopRecording();
      } else if (this.isPlaying) {
        // 停止播放
        this.stopPlayback();
      }
    });
    
    // 保存按钮点击事件
    this.ui.saveButton.addEventListener('click', () => {
      this.saveRecording();
    });
    
    // 加载按钮点击事件
    this.ui.loadButton.addEventListener('click', () => {
      this.loadRecording();
    });
  }

  /**
   * 开始录制
   */
  private startRecording(): void {
    // 开始录制
    const success = this.actionControlSystem.startRecording(this.character, '录制_' + new Date().toLocaleTimeString());
    
    if (success) {
      this.isRecording = true;
      this.ui.recordButton.textContent = '暂停';
      this.ui.statusText.textContent = '正在录制...';
      console.log('开始录制');
    } else {
      console.error('无法开始录制');
    }
  }

  /**
   * 停止录制
   */
  private stopRecording(): void {
    // 停止录制
    this.currentRecording = this.actionControlSystem.stopRecording(this.character);
    
    if (this.currentRecording) {
      this.isRecording = false;
      this.ui.recordButton.textContent = '录制';
      this.ui.statusText.textContent = '录制完成';
      console.log('录制完成:', this.currentRecording);
      
      // 添加到录制列表
      this.addRecordingToList(this.currentRecording);
    } else {
      console.error('无法停止录制');
    }
  }

  /**
   * 开始播放
   */
  private startPlayback(): void {
    // 获取选中的录制
    const selectedIndex = this.ui.recordingsList.selectedIndex;
    if (selectedIndex === -1) {
      console.error('未选择录制');
      return;
    }
    
    const recordingId = this.ui.recordingsList.options[selectedIndex].value;
    
    // 开始播放
    const success = this.actionControlSystem.playRecording(this.character, recordingId, 1.0);
    
    if (success) {
      this.isPlaying = true;
      this.ui.playButton.textContent = '暂停';
      this.ui.statusText.textContent = '正在播放...';
      console.log('开始播放');
    } else {
      console.error('无法开始播放');
    }
  }

  /**
   * 暂停播放
   */
  private pausePlayback(): void {
    // 暂停播放
    const success = this.actionControlSystem.pausePlayback(this.character);
    
    if (success) {
      this.isPlaying = false;
      this.ui.playButton.textContent = '继续';
      this.ui.statusText.textContent = '播放暂停';
      console.log('播放暂停');
    } else {
      console.error('无法暂停播放');
    }
  }

  /**
   * 停止播放
   */
  private stopPlayback(): void {
    // 停止播放
    const success = this.actionControlSystem.stopPlayback(this.character);
    
    if (success) {
      this.isPlaying = false;
      this.ui.playButton.textContent = '播放';
      this.ui.statusText.textContent = '播放停止';
      console.log('播放停止');
    } else {
      console.error('无法停止播放');
    }
  }

  /**
   * 保存录制
   */
  private saveRecording(): void {
    if (!this.currentRecording) {
      console.error('没有可保存的录制');
      return;
    }
    
    // 保存录制到文件
    ActionRecorder.saveToFile(this.currentRecording, `${this.currentRecording.name}.json`);
    console.log('录制已保存');
  }

  /**
   * 加载录制
   */
  private loadRecording(): void {
    // 创建文件输入元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.json';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);
    
    // 监听文件选择
    fileInput.addEventListener('change', async () => {
      if (fileInput.files && fileInput.files.length > 0) {
        try {
          // 加载录制
          const recording = await ActionRecorder.loadFromFile(fileInput.files[0]);
          
          // 添加到动作控制系统
          this.actionControlSystem.addRecording(recording);
          
          // 添加到录制列表
          this.addRecordingToList(recording);
          
          console.log('录制已加载:', recording);
        } catch (error) {
          console.error('加载录制失败:', error);
        }
      }
      
      // 移除文件输入元素
      document.body.removeChild(fileInput);
    });
    
    // 触发文件选择
    fileInput.click();
  }

  /**
   * 添加录制到列表
   * @param recording 录制数据
   */
  private addRecordingToList(recording: any): void {
    // 创建选项
    const option = document.createElement('option');
    option.value = recording.id;
    option.textContent = `${recording.name} (${this.formatDuration(recording.endTimestamp - recording.startTimestamp)})`;
    
    // 添加到列表
    this.ui.recordingsList.appendChild(option);
    
    // 选中新添加的选项
    this.ui.recordingsList.selectedIndex = this.ui.recordingsList.options.length - 1;
  }

  /**
   * 格式化持续时间
   * @param ms 毫秒
   * @returns 格式化后的字符串
   */
  private formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

// 创建示例实例
new CharacterEnvironmentInteractionExample();
