/**
 * 高度图导入导出
 * 用于处理高度图的导入和导出
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';
import { Debug } from '../../utils/Debug';
import { HeightMapFormat } from './TerrainImportExportService';

/**
 * 高度图导出选项
 */
export interface HeightMapExportOptions {
  /** 格式 */
  format: HeightMapFormat;
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
  /** 是否反转Y轴 */
  flipY?: boolean;
  /** 是否应用归一化 */
  normalize?: boolean;
  /** 位深度 (8, 16, 32) */
  bitDepth?: number;
  /** 自定义元数据 */
  metadata?: Record<string, any>;
}

/**
 * 高度图导入选项
 */
export interface HeightMapImportOptions {
  /** 是否反转Y轴 */
  flipY?: boolean;
  /** 高度缩放 */
  heightScale?: number;
  /** 是否应用平滑 */
  applySmoothing?: boolean;
  /** 平滑强度 */
  smoothingStrength?: number;
  /** 自定义元数据 */
  metadata?: Record<string, any>;
}

/**
 * 高度图导入导出
 */
export class HeightMapImportExport {
  /**
   * 从高度图导入地形
   * @param terrain 地形组件
   * @param heightMap 高度图（URL、File、Blob、ImageData、Canvas、Image）
   * @param options 导入选项
   * @returns Promise，解析为是否导入成功
   */
  public async importHeightMap(
    terrain: TerrainComponent,
    heightMap: string | File | Blob | ImageData | HTMLCanvasElement | HTMLImageElement,
    options: HeightMapImportOptions = {}
  ): Promise<boolean> {
    try {
      // 加载图像
      const imageData = await this.loadImageData(heightMap);
      if (!imageData) {
        Debug.error('HeightMapImportExport', '加载高度图失败');
        return false;
      }

      // 提取高度数据
      const heightData = this.extractHeightData(imageData, options);
      if (!heightData) {
        Debug.error('HeightMapImportExport', '提取高度数据失败');
        return false;
      }

      // 更新地形分辨率
      if (terrain.resolution !== heightData.width) {
        terrain.resolution = heightData.width;
      }

      // 更新高度数据
      terrain.heightData = heightData.data;

      // 应用平滑
      if (options.applySmoothing && options.smoothingStrength) {
        this.smoothHeightData(terrain.heightData, terrain.resolution, options.smoothingStrength);
      }

      // 标记需要更新
      terrain.needsUpdate = true;
      terrain.needsPhysicsUpdate = true;

      return true;
    } catch (error) {
      Debug.error('HeightMapImportExport', '从高度图导入地形失败:', error);
      return false;
    }
  }

  /**
   * 导出地形为高度图
   * @param terrain 地形组件
   * @param options 导出选项
   * @returns Promise，解析为Blob
   */
  public async exportHeightMap(
    terrain: TerrainComponent,
    options: HeightMapExportOptions
  ): Promise<Blob> {
    try {
      // 确定导出尺寸
      const width = options.width || terrain.resolution;
      const height = options.height || terrain.resolution;

      // 创建Canvas
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('无法创建Canvas 2D上下文');
      }

      // 创建ImageData
      const imageData = ctx.createImageData(width, height);
      const data = imageData.data;

      // 填充高度数据
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          // 计算源索引（可能需要重采样）
          const sourceX = Math.floor(x * terrain.resolution / width);
          const sourceY = Math.floor(y * terrain.resolution / height);
          const sourceIndex = (options.flipY ? (terrain.resolution - 1 - sourceY) : sourceY) * terrain.resolution + sourceX;
          
          // 获取高度值
          const heightValue = terrain.heightData[sourceIndex];
          
          // 归一化高度值到0-255范围
          const normalizedValue = options.normalize 
            ? Math.floor(heightValue * 255)
            : Math.floor(heightValue * terrain.maxHeight / 255);
          
          // 设置像素值
          const destIndex = (y * width + x) * 4;
          data[destIndex] = normalizedValue;     // R
          data[destIndex + 1] = normalizedValue; // G
          data[destIndex + 2] = normalizedValue; // B
          data[destIndex + 3] = 255;             // A
        }
      }

      // 将ImageData绘制到Canvas
      ctx.putImageData(imageData, 0, 0);

      // 根据格式导出
      let mimeType = 'image/png';
      let quality = 1.0;
      
      switch (options.format) {
        case HeightMapFormat.JPEG:
          mimeType = 'image/jpeg';
          quality = 0.95;
          break;
        case HeightMapFormat.PNG:
          mimeType = 'image/png';
          break;
        case HeightMapFormat.RAW:
        case HeightMapFormat.R16:
        case HeightMapFormat.R32:
          // 对于原始格式，直接返回二进制数据
          return this.exportRawHeightMap(terrain, options);
        case HeightMapFormat.ASC:
          // 对于ASC格式，返回文本数据
          return this.exportASCHeightMap(terrain, options);
        case HeightMapFormat.HGT:
        case HeightMapFormat.TER:
        case HeightMapFormat.BT:
          // 对于其他格式，返回特定格式的二进制数据
          return this.exportSpecialHeightMap(terrain, options);
        default:
          mimeType = 'image/png';
      }

      // 将Canvas转换为Blob
      return new Promise<Blob>((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('无法创建Blob'));
          }
        }, mimeType, quality);
      });
    } catch (error) {
      Debug.error('HeightMapImportExport', '导出地形为高度图失败:', error);
      throw error;
    }
  }

  /**
   * 加载图像数据
   * @param source 图像源
   * @returns Promise，解析为ImageData
   */
  private async loadImageData(
    source: string | File | Blob | ImageData | HTMLCanvasElement | HTMLImageElement
  ): Promise<ImageData | null> {
    try {
      // 如果已经是ImageData，直接返回
      if (source instanceof ImageData) {
        return source;
      }

      // 创建Canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        Debug.error('HeightMapImportExport', '无法创建Canvas 2D上下文');
        return null;
      }

      // 加载图像
      let img: HTMLImageElement;
      if (source instanceof HTMLImageElement) {
        img = source;
      } else if (source instanceof HTMLCanvasElement) {
        canvas.width = source.width;
        canvas.height = source.height;
        ctx.drawImage(source, 0, 0);
        return ctx.getImageData(0, 0, canvas.width, canvas.height);
      } else {
        // 创建URL
        const url = source instanceof File || source instanceof Blob
          ? URL.createObjectURL(source)
          : source;

        // 加载图像
        img = await this.loadImage(url);

        // 如果创建了URL，释放它
        if (source instanceof File || source instanceof Blob) {
          URL.revokeObjectURL(url);
        }
      }

      // 设置Canvas尺寸
      canvas.width = img.width;
      canvas.height = img.height;

      // 绘制图像
      ctx.drawImage(img, 0, 0);

      // 获取ImageData
      return ctx.getImageData(0, 0, canvas.width, canvas.height);
    } catch (error) {
      Debug.error('HeightMapImportExport', '加载图像数据失败:', error);
      return null;
    }
  }

  /**
   * 加载图像
   * @param url 图像URL
   * @returns Promise，解析为HTMLImageElement
   */
  private loadImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = (error) => reject(error);
      img.src = url;
    });
  }

  /**
   * 提取高度数据
   * @param imageData 图像数据
   * @param options 导入选项
   * @returns 高度数据
   */
  private extractHeightData(
    imageData: ImageData,
    options: HeightMapImportOptions
  ): { data: Float32Array, width: number, height: number } | null {
    try {
      const { width, height, data } = imageData;
      const heightData = new Float32Array(width * height);

      // 提取高度数据
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          // 计算源索引
          const sourceY = options.flipY ? (height - 1 - y) : y;
          const sourceIndex = (sourceY * width + x) * 4;
          
          // 使用红色通道作为高度值
          const heightValue = data[sourceIndex] / 255;
          
          // 应用高度缩放
          const scaledHeight = options.heightScale ? heightValue * options.heightScale : heightValue;
          
          // 存储高度值
          heightData[y * width + x] = scaledHeight;
        }
      }

      return { data: heightData, width, height };
    } catch (error) {
      Debug.error('HeightMapImportExport', '提取高度数据失败:', error);
      return null;
    }
  }

  /**
   * 平滑高度数据
   * @param heightData 高度数据
   * @param resolution 分辨率
   * @param strength 平滑强度
   */
  private smoothHeightData(heightData: Float32Array, resolution: number, strength: number): void {
    const tempData = new Float32Array(heightData.length);

    // 复制高度数据
    heightData.forEach((height, i) => {
      tempData[i] = height;
    });

    // 应用平滑
    for (let z = 1; z < resolution - 1; z++) {
      for (let x = 1; x < resolution - 1; x++) {
        const index = z * resolution + x;
        
        // 获取相邻点
        const neighbors = [
          (z - 1) * resolution + x,      // 上
          z * resolution + (x + 1),      // 右
          (z + 1) * resolution + x,      // 下
          z * resolution + (x - 1),      // 左
          (z - 1) * resolution + (x - 1), // 左上
          (z - 1) * resolution + (x + 1), // 右上
          (z + 1) * resolution + (x + 1), // 右下
          (z + 1) * resolution + (x - 1)  // 左下
        ];

        // 计算平均高度
        let sum = heightData[index];
        let count = 1;

        for (const neighborIndex of neighbors) {
          if (neighborIndex >= 0 && neighborIndex < heightData.length) {
            sum += heightData[neighborIndex];
            count++;
          }
        }

        const average = sum / count;
        
        // 应用平滑
        tempData[index] = heightData[index] * (1 - strength) + average * strength;
      }
    }

    // 更新高度数据
    tempData.forEach((height, i) => {
      heightData[i] = height;
    });
  }

  /**
   * 导出原始高度图
   * @param terrain 地形组件
   * @param options 导出选项
   * @returns Blob
   */
  private exportRawHeightMap(terrain: TerrainComponent, options: HeightMapExportOptions): Blob {
    // 这里实现RAW、R16、R32格式的导出
    // 暂时返回一个空的Blob
    return new Blob([], { type: 'application/octet-stream' });
  }

  /**
   * 导出ASC高度图
   * @param terrain 地形组件
   * @param options 导出选项
   * @returns Blob
   */
  private exportASCHeightMap(terrain: TerrainComponent, options: HeightMapExportOptions): Blob {
    // 这里实现ASC格式的导出
    // 暂时返回一个空的Blob
    return new Blob([], { type: 'text/plain' });
  }

  /**
   * 导出特殊格式高度图
   * @param terrain 地形组件
   * @param options 导出选项
   * @returns Blob
   */
  private exportSpecialHeightMap(terrain: TerrainComponent, options: HeightMapExportOptions): Blob {
    // 这里实现HGT、TER、BT格式的导出
    // 暂时返回一个空的Blob
    return new Blob([], { type: 'application/octet-stream' });
  }
}
