/**
 * 变量监视器
 * 负责监视视觉脚本变量的变化
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Graph } from '../graph/Graph';
/**
 * 变量监视信息
 */
export interface VariableWatch {
    /** 变量名 */
    name: string;
    /** 图ID */
    graphId: string;
    /** 是否启用 */
    enabled: boolean;
    /** 条件表达式 */
    condition?: string;
    /** 变量类型 */
    type?: string;
    /** 格式化表达式 */
    format?: string;
    /** 监视标签 */
    tags?: string[];
    /** 监视描述 */
    description?: string;
    /** 创建时间 */
    createdAt?: number;
    /** 最后更新时间 */
    lastUpdatedAt?: number;
    /** 更新次数 */
    updateCount?: number;
    /** 是否记录历史 */
    recordHistory?: boolean;
    /** 是否在值变化时通知 */
    notifyOnChange?: boolean;
    /** 是否在值满足条件时通知 */
    notifyOnCondition?: boolean;
}
/**
 * 变量变化信息
 */
export interface VariableChangeInfo {
    /** 变量名 */
    name: string;
    /** 图ID */
    graphId: string;
    /** 旧值 */
    oldValue: any;
    /** 新值 */
    newValue: any;
    /** 变化时间 */
    timestamp: number;
    /** 变化类型 */
    changeType?: 'create' | 'update' | 'delete';
    /** 变化描述 */
    description?: string;
    /** 节点ID */
    nodeId?: string;
    /** 变化来源 */
    source?: string;
}
/**
 * 变量监视器
 */
export declare class VariableWatcher extends EventEmitter {
    /** 监视的变量映射 */
    private watches;
    /** 变量历史记录 */
    private history;
    /** 最大历史记录数量 */
    private maxHistorySize;
    /**
     * 创建变量监视器
     * @param maxHistorySize 最大历史记录数量
     */
    constructor(maxHistorySize?: number);
    /**
     * 添加变量监视
     * @param name 变量名
     * @param graphId 图ID
     * @param options 监视选项
     * @returns 是否成功
     */
    addWatch(name: string, graphId: string, options?: {
        condition?: string;
        type?: string;
        format?: string;
        tags?: string[];
        description?: string;
        recordHistory?: boolean;
        notifyOnChange?: boolean;
        notifyOnCondition?: boolean;
        enabled?: boolean;
    }): boolean;
    /**
     * 移除变量监视
     * @param name 变量名
     * @param graphId 图ID
     * @returns 是否成功
     */
    removeWatch(name: string, graphId: string): boolean;
    /**
     * 更新变量监视
     * @param name 变量名
     * @param graphId 图ID
     * @param updates 更新内容
     * @returns 是否成功
     */
    updateWatch(name: string, graphId: string, updates: Partial<Omit<VariableWatch, 'name' | 'graphId'>>): boolean;
    /**
     * 启用变量监视
     * @param name 变量名
     * @param graphId 图ID
     * @returns 是否成功
     */
    enableWatch(name: string, graphId: string): boolean;
    /**
     * 禁用变量监视
     * @param name 变量名
     * @param graphId 图ID
     * @returns 是否成功
     */
    disableWatch(name: string, graphId: string): boolean;
    /**
     * 获取变量监视
     * @param name 变量名
     * @param graphId 图ID
     * @returns 变量监视
     */
    getWatch(name: string, graphId: string): VariableWatch | undefined;
    /**
     * 获取所有变量监视
     * @returns 变量监视列表
     */
    getAllWatches(): VariableWatch[];
    /**
     * 获取图的所有变量监视
     * @param graphId 图ID
     * @returns 变量监视列表
     */
    getGraphWatches(graphId: string): VariableWatch[];
    /**
     * 清除所有变量监视
     */
    clearAllWatches(): void;
    /**
     * 清除图的所有变量监视
     * @param graphId 图ID
     */
    clearGraphWatches(graphId: string): void;
    /**
     * 检查变量是否被监视
     * @param name 变量名
     * @param graphId 图ID
     * @returns 是否被监视
     */
    isWatched(name: string, graphId: string): boolean;
    /**
     * 检查变量是否被启用监视
     * @param name 变量名
     * @param graphId 图ID
     * @returns 是否被启用监视
     */
    isWatchEnabled(name: string, graphId: string): boolean;
    /**
     * 检查变量监视是否应该触发
     * @param name 变量名
     * @param graphId 图ID
     * @param value 变量值
     * @param graph 图
     * @returns 是否应该触发
     */
    shouldTrigger(name: string, graphId: string, value: any, graph: Graph): boolean;
    /**
     * 记录变量变化
     * @param name 变量名
     * @param graphId 图ID
     * @param oldValue 旧值
     * @param newValue 新值
     * @param options 记录选项
     */
    recordChange(name: string, graphId: string, oldValue: any, newValue: any, options?: {
        changeType?: 'create' | 'update' | 'delete';
        description?: string;
        nodeId?: string;
        source?: string;
    }): void;
    /**
     * 比较两个值是否相等
     * @param a 第一个值
     * @param b 第二个值
     * @returns 是否相等
     */
    private areValuesEqual;
    /**
     * 根据ID获取图
     * @param graphId 图ID
     * @returns 图
     */
    private getGraphById;
    /**
     * 获取变量历史记录
     * @param name 变量名
     * @param graphId 图ID
     * @returns 历史记录
     */
    getHistory(name: string, graphId: string): VariableChangeInfo[];
    /**
     * 清除变量历史记录
     * @param name 变量名
     * @param graphId 图ID
     */
    clearHistory(name: string, graphId: string): void;
    /**
     * 清除所有历史记录
     */
    clearAllHistory(): void;
    /**
     * 按标签查找变量监视
     * @param tag 标签
     * @returns 变量监视列表
     */
    findWatchesByTag(tag: string): VariableWatch[];
    /**
     * 搜索变量监视
     * @param query 搜索查询
     * @returns 变量监视列表
     */
    searchWatches(query: string): VariableWatch[];
    /**
     * 导出变量监视
     * @returns 变量监视数据
     */
    exportWatches(): string;
    /**
     * 导入变量监视
     * @param data 变量监视数据
     * @returns 是否成功
     */
    importWatches(data: string): boolean;
    /**
     * 格式化变量值
     * @param value 变量值
     * @param format 格式化表达式
     * @returns 格式化后的值
     */
    formatValue(value: any, format?: string): string;
}
