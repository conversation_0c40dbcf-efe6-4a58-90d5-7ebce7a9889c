/**
 * 水体GPU实例化渲染
 * 使用GPU实例化技术渲染水体特效（如泡沫、水花等）
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
/**
 * 水体特效类型
 */
export declare enum WaterEffectType {
    /** 泡沫 */
    FOAM = "foam",
    /** 水花 */
    SPLASH = "splash",
    /** 水滴 */
    DROPLET = "droplet",
    /** 波纹 */
    RIPPLE = "ripple",
    /** 水雾 */
    MIST = "mist"
}
/**
 * 水体特效实例
 */
export interface WaterEffectInstance {
    /** 唯一ID */
    id: string;
    /** 类型 */
    type: WaterEffectType;
    /** 位置 */
    position: THREE.Vector3;
    /** 旋转 */
    rotation: THREE.Euler;
    /** 缩放 */
    scale: THREE.Vector3;
    /** 颜色 */
    color: THREE.Color;
    /** 不透明度 */
    opacity: number;
    /** 生命周期 */
    lifetime: number;
    /** 已存在时间 */
    age: number;
    /** 是否活跃 */
    active: boolean;
    /** 用户数据 */
    userData: any;
}
/**
 * 水体实例化渲染配置
 */
export interface WaterInstancedRendererConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 最大实例数 */
    maxInstances?: number;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 是否使用GPU剔除 */
    useGPUCulling?: boolean;
    /** 是否使用实例化阴影 */
    useInstancedShadows?: boolean;
}
/**
 * 水体实例化渲染系统事件类型
 */
export declare enum WaterInstancedRendererEventType {
    /** 实例创建 */
    INSTANCE_CREATED = "instanceCreated",
    /** 实例销毁 */
    INSTANCE_DESTROYED = "instanceDestroyed",
    /** 实例更新 */
    INSTANCE_UPDATED = "instanceUpdated"
}
/**
 * 水体实例化渲染系统
 */
export declare class WaterInstancedRenderer extends System {
    /** 系统类型 */
    static readonly TYPE = "WaterInstancedRenderer";
    /** 配置 */
    private config;
    /** 水体实体映射 */
    private waterEntities;
    /** 特效实例映射 */
    private effectInstances;
    /** 特效类型映射 */
    private effectTypeMap;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 调试对象 */
    private debugObjects;
    /** 临时对象 */
    private tempObject;
    /** 临时颜色 */
    private tempColor;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: WaterInstancedRendererConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化特效类型
     */
    private initializeEffectTypes;
    /**
     * 创建特效类型
     * @param type 特效类型
     * @param geometry 几何体
     * @param material 材质
     */
    private createEffectType;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加水体实体
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterEntity(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体实体
     * @param entity 实体
     */
    removeWaterEntity(entity: Entity): void;
    /**
     * 更新特效实例
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateEffectInstances;
    /**
     * 更新特效实例
     * @param instance 特效实例
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateEffectInstance;
    /**
     * 更新泡沫实例
     * @param instance 特效实例
     * @param progress 生命周期进度
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateFoamInstance;
    /**
     * 更新水花实例
     * @param instance 特效实例
     * @param progress 生命周期进度
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateSplashInstance;
    /**
     * 更新水滴实例
     * @param instance 特效实例
     * @param progress 生命周期进度
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateDropletInstance;
    /**
     * 更新波纹实例
     * @param instance 特效实例
     * @param progress 生命周期进度
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateRippleInstance;
    /**
     * 更新水雾实例
     * @param instance 特效实例
     * @param progress 生命周期进度
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateMistInstance;
    /**
     * 更新实例矩阵
     * @param instance 特效实例
     * @param index 实例索引
     */
    private updateInstanceMatrix;
    /**
     * 更新实例颜色
     * @param instance 特效实例
     * @param index 实例索引
     */
    private updateInstanceColor;
    /**
     * 创建特效实例
     * @param type 特效类型
     * @param position 位置
     * @param options 选项
     * @returns 特效实例ID
     */
    createEffectInstance(type: WaterEffectType, position: THREE.Vector3, options?: {
        rotation?: THREE.Euler;
        scale?: THREE.Vector3;
        color?: THREE.Color;
        opacity?: number;
        lifetime?: number;
        userData?: any;
    }): string;
    /**
     * 销毁特效实例
     * @param id 特效实例ID
     */
    destroyEffectInstance(id: string): void;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
}
