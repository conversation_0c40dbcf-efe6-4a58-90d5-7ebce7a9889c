/**
 * 风场系统
 * 用于管理风区域和风场
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { WindZone, WindFieldType } from './PhysicalWindSystem';
/**
 * 风场系统配置
 */
export interface WindFieldSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 是否使用风场网格 */
    useWindFieldGrid?: boolean;
    /** 风场网格分辨率 */
    windFieldGridResolution?: number;
    /** 是否使用风场粒子 */
    useWindFieldParticles?: boolean;
    /** 风场粒子数量 */
    windFieldParticlesCount?: number;
}
/**
 * 风场系统事件类型
 */
export declare enum WindFieldSystemEventType {
    /** 风场更新 */
    WIND_FIELD_UPDATED = "wind_field_updated",
    /** 风区域添加 */
    WIND_ZONE_ADDED = "wind_zone_added",
    /** 风区域移除 */
    WIND_ZONE_REMOVED = "wind_zone_removed",
    /** 风区域更新 */
    WIND_ZONE_UPDATED = "wind_zone_updated"
}
/**
 * 风场系统
 */
export declare class WindFieldSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 配置 */
    private config;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 帧计数器 */
    private frameCount;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 是否使用风场网格 */
    private useWindFieldGrid;
    /** 风场网格分辨率 */
    private windFieldGridResolution;
    /** 是否使用风场粒子 */
    private useWindFieldParticles;
    /** 风场粒子数量 */
    private windFieldParticlesCount;
    /** 物理风效果系统 */
    private physicalWindSystem;
    /** 风区域列表 */
    private windZones;
    /** 风场网格 */
    private windFieldGrid;
    /** 风场粒子系统 */
    private windFieldParticles;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监控器 */
    private performanceMonitor;
    /** 当前时间 */
    private time;
    /** 调试网格列表 */
    private debugMeshes;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: WindFieldSystemConfig);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 销毁系统
     */
    destroy(): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加风区域
     * @param id 风区域ID
     * @param zone 风区域
     */
    addWindZone(id: string, zone: WindZone): void;
    /**
     * 移除风区域
     * @param id 风区域ID
     */
    removeWindZone(id: string): void;
    /**
     * 更新风区域
     * @param id 风区域ID
     * @param zone 风区域
     */
    updateWindZone(id: string, zone: Partial<WindZone>): void;
    /**
     * 创建球形风区域
     * @param id 风区域ID
     * @param position 位置
     * @param radius 半径
     * @param strength 强度
     * @param direction 方向
     * @param fieldType 风场类型
     * @returns 风区域
     */
    createSphereWindZone(id: string, position: THREE.Vector3, radius: number, strength: number, direction: THREE.Vector3, fieldType?: WindFieldType): WindZone;
    /**
     * 创建盒形风区域
     * @param id 风区域ID
     * @param position 位置
     * @param size 大小
     * @param rotation 旋转
     * @param strength 强度
     * @param direction 方向
     * @param fieldType 风场类型
     * @returns 风区域
     */
    createBoxWindZone(id: string, position: THREE.Vector3, size: THREE.Vector3, rotation: THREE.Euler, strength: number, direction: THREE.Vector3, fieldType?: WindFieldType): WindZone;
    /**
     * 创建圆柱形风区域
     * @param id 风区域ID
     * @param position 位置
     * @param radius 半径
     * @param height 高度
     * @param rotation 旋转
     * @param strength 强度
     * @param direction 方向
     * @param fieldType 风场类型
     * @returns 风区域
     */
    createCylinderWindZone(id: string, position: THREE.Vector3, radius: number, height: number, rotation: THREE.Euler, strength: number, direction: THREE.Vector3, fieldType?: WindFieldType): WindZone;
    /**
     * 初始化风场网格
     */
    private initWindFieldGrid;
    /**
     * 更新风场网格
     */
    private updateWindFieldGrid;
    /**
     * 初始化风场粒子
     */
    private initWindFieldParticles;
    /**
     * 更新风场粒子
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWindFieldParticles;
    /**
     * 计算风力
     * @param position 位置
     * @returns 风力向量
     */
    private calculateWindForce;
    /**
     * 初始化调试可视化
     */
    private initDebugVisualization;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 清除调试网格
     */
    private clearDebugMeshes;
    /**
     * 创建风场网格可视化
     */
    private createWindFieldGridVisualization;
    /**
     * 更新风场网格可视化
     */
    private updateWindFieldGridVisualization;
}
