import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { HealthCheckEntity, HealthCheckStatus } from './health-check.entity';

@Entity('health_check_history')
export class HealthCheckHistoryEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  healthCheckId: string;

  @ManyToOne(() => HealthCheckEntity)
  @JoinColumn({ name: 'healthCheckId' })
  healthCheck: HealthCheckEntity;

  @Column({
    type: 'enum',
    enum: HealthCheckStatus,
  })
  @Index()
  status: HealthCheckStatus;

  @Column('int')
  responseTime: number;

  @Column('text', { nullable: true })
  details: string;

  @Column('json', { nullable: true })
  metadata: Record<string, any>;

  @Column('boolean', { default: false })
  autoRecoveryTriggered: boolean;

  @Column('text', { nullable: true })
  autoRecoveryAction: string;

  @Column('text', { nullable: true })
  autoRecoveryResult: string;

  @CreateDateColumn()
  @Index()
  createdAt: Date;
}
