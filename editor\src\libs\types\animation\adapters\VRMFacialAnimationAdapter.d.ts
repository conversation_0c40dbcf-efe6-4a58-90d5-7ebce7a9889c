import { FacialExpressionType, VisemeType } from '../FacialAnimation';
import { FacialAnimationModelAdapterComponent } from './FacialAnimationModelAdapter';
/**
 * VRM面部动画适配器配置
 */
export interface VRMFacialAnimationAdapterConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否自动检测混合形状 */
    autoDetectBlendShapes?: boolean;
}
/**
 * VRM面部动画适配器组件
 */
export declare class VRMFacialAnimationAdapter extends FacialAnimationModelAdapterComponent {
    /** 组件类型 */
    static readonly type = "FacialAnimationModelAdapter";
    /** VRM模型 */
    private vrmModel;
    /** 混合形状代理 */
    private blendShapeProxy;
    /** 是否启用调试 */
    private vrmDebug;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: VRMFacialAnimationAdapterConfig);
    /**
     * 设置VRM模型
     * @param vrm VRM模型
     */
    setVRMModel(vrm: any): void;
    /**
     * 查找骨骼网格
     * @param object 对象
     * @returns 骨骼网格
     */
    private findSkinnedMesh;
    /**
     * 应用表情
     * @param expression 表情类型
     * @param weight 权重
     * @returns 是否成功应用
     */
    applyExpression(expression: FacialExpressionType, weight: number): boolean;
    /**
     * 应用口型
     * @param viseme 口型类型
     * @param weight 权重
     * @returns 是否成功应用
     */
    applyViseme(viseme: VisemeType, weight: number): boolean;
    /**
     * 重置所有表情和口型
     */
    resetAll(): void;
    /**
     * 将表情映射到VRM表情
     * @param expression 表情类型
     * @returns VRM表情名称
     */
    private mapExpressionToVRM;
    /**
     * 将口型映射到VRM表情
     * @param viseme 口型类型
     * @returns VRM表情名称
     */
    private mapVisemeToVRM;
}
