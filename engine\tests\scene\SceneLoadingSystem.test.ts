/**
 * 场景加载系统测试
 */
import * as THREE from 'three';
import { 
  SceneLoadingSystem, 
  ResourceType, 
  ResourcePriority, 
  ResourceState,
  SceneRegion
} from '../../src/scene/SceneLoadingSystem';
import { EntityManager } from '../../src/core/EntityManager';
import { Entity } from '../../src/core/Entity';
import { Camera } from '../../src/rendering/Camera';
import { Scene } from '../../src/scene/Scene';
import { Transform } from '../../src/scene/Transform';

// 模拟THREE.WebGLRenderer
jest.mock('three', () => {
  const originalThree = jest.requireActual('three');
  return {
    ...originalThree,
    TextureLoader: jest.fn().mockImplementation(() => ({
      load: jest.fn((url, onLoad, onProgress, onError) => {
        // 模拟异步加载
        setTimeout(() => {
          onLoad({ 
            image: { width: 512, height: 512 },
            dispose: jest.fn()
          });
        }, 10);
        return { dispose: jest.fn() };
      })
    })),
    ObjectLoader: jest.fn().mockImplementation(() => ({
      load: jest.fn((url, onLoad, onProgress, onError) => {
        // 模拟异步加载
        setTimeout(() => {
          onLoad({ 
            isObject3D: true,
            dispose: jest.fn()
          });
        }, 10);
        return { dispose: jest.fn() };
      })
    })),
    AudioLoader: jest.fn().mockImplementation(() => ({
      load: jest.fn((url, onLoad, onProgress, onError) => {
        // 模拟异步加载
        setTimeout(() => {
          onLoad({ 
            isAudioBuffer: true,
            duration: 10,
            dispose: jest.fn()
          });
        }, 10);
        return { dispose: jest.fn() };
      })
    })),
    FileLoader: jest.fn().mockImplementation(() => ({
      load: jest.fn((url, onLoad, onProgress, onError) => {
        // 模拟异步加载
        setTimeout(() => {
          onLoad('file content');
        }, 10);
        return { dispose: jest.fn() };
      })
    })),
    LoadingManager: jest.fn().mockImplementation(() => ({
      onProgress: null
    })),
  };
});

describe('SceneLoadingSystem', () => {
  let system: SceneLoadingSystem;
  let entityManager: EntityManager;
  let camera: Entity;
  let scene: Entity;

  beforeEach(() => {
    // 创建实体管理器
    entityManager = new EntityManager();

    // 创建相机实体
    camera = new Entity();
    const cameraComponent = new Camera();
    const cameraTransform = new Transform();
    camera.addComponent(cameraComponent);
    camera.addComponent(cameraTransform);
    entityManager.addEntity(camera);

    // 创建场景实体
    scene = new Entity();
    const sceneComponent = new Scene();
    scene.addComponent(sceneComponent);
    entityManager.addEntity(scene);

    // 创建系统
    system = new SceneLoadingSystem({
      maxConcurrentLoads: 5,
      maxMemoryUsage: 1024 * 1024 * 1024, // 1GB
      preloadDistance: 100,
      unloadDistance: 200,
      usePredictiveLoading: true,
      useCache: true,
      useCompression: false,
      useDebugVisualization: false,
      loadTimeout: 30000,
      retryCount: 3,
      retryDelay: 1000,
    });
    system.setEntityManager(entityManager);
    system.initialize();

    // 设置相机位置
    const cameraObject = cameraComponent.getObject3D();
    cameraObject.position.set(0, 0, 0);
    cameraObject.lookAt(0, 0, -1);
    cameraObject.updateMatrixWorld();
  });

  afterEach(() => {
    system.destroy();
  });

  test('应该正确初始化系统', () => {
    expect(system['initialized']).toBe(true);
    expect(system['maxConcurrentLoads']).toBe(5);
    expect(system['maxMemoryUsage']).toBe(1024 * 1024 * 1024);
    expect(system['preloadDistance']).toBe(100);
    expect(system['unloadDistance']).toBe(200);
    expect(system['usePredictiveLoading']).toBe(true);
    expect(system['useCache']).toBe(true);
    expect(system['useCompression']).toBe(false);
    expect(system['loadTimeout']).toBe(30000);
    expect(system['retryCount']).toBe(3);
    expect(system['retryDelay']).toBe(1000);
  });

  test('应该正确添加资源', () => {
    // 添加资源
    const resourceId = system.addResource({
      url: 'textures/test.jpg',
      type: ResourceType.TEXTURE,
      priority: ResourcePriority.MEDIUM,
      persistent: false,
      preload: true,
    });

    // 验证资源已添加
    const resource = system.getResource(resourceId);
    expect(resource).toBeDefined();
    expect(resource!.url).toBe('textures/test.jpg');
    expect(resource!.type).toBe(ResourceType.TEXTURE);
    expect(resource!.priority).toBe(ResourcePriority.MEDIUM);
    expect(resource!.persistent).toBe(false);
    expect(resource!.preload).toBe(true);
    expect(resource!.state).toBe(ResourceState.UNLOADED);
  });

  test('应该正确添加场景区域', () => {
    // 添加场景区域
    const regionId = system.addRegion({
      name: 'Test Region',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(-10, -10, -10),
        new THREE.Vector3(10, 10, 10)
      ),
      resources: [],
      priority: ResourcePriority.MEDIUM,
    });

    // 验证场景区域已添加
    const region = system.getRegion(regionId);
    expect(region).toBeDefined();
    expect(region!.name).toBe('Test Region');
    expect(region!.boundingBox).toBeDefined();
    expect(region!.priority).toBe(ResourcePriority.MEDIUM);
    expect(region!.visible).toBe(false);
    expect(region!.loaded).toBe(false);
  });

  test('应该正确加载区域', () => {
    // 添加资源
    const resourceId = system.addResource({
      url: 'textures/test.jpg',
      type: ResourceType.TEXTURE,
      priority: ResourcePriority.MEDIUM,
    });

    // 添加场景区域
    const regionId = system.addRegion({
      name: 'Test Region',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(-10, -10, -10),
        new THREE.Vector3(10, 10, 10)
      ),
      resources: [resourceId],
      priority: ResourcePriority.MEDIUM,
    });

    // 获取场景区域
    const region = system.getRegion(regionId);
    expect(region).toBeDefined();

    // 调用私有方法加载区域
    system['loadRegion'](region!);

    // 验证区域已加载
    expect(region!.loaded).toBe(true);
    expect(region!.visible).toBe(true);

    // 验证资源已加载
    const resource = system.getResource(resourceId);
    expect(resource).toBeDefined();
    expect(resource!.state).toBe(ResourceState.LOADING);
  });

  test('应该正确卸载区域', () => {
    // 添加资源
    const resourceId = system.addResource({
      url: 'textures/test.jpg',
      type: ResourceType.TEXTURE,
      priority: ResourcePriority.MEDIUM,
    });

    // 添加场景区域
    const regionId = system.addRegion({
      name: 'Test Region',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(-10, -10, -10),
        new THREE.Vector3(10, 10, 10)
      ),
      resources: [resourceId],
      priority: ResourcePriority.MEDIUM,
    });

    // 获取场景区域
    const region = system.getRegion(regionId);
    expect(region).toBeDefined();

    // 调用私有方法加载区域
    system['loadRegion'](region!);

    // 验证区域已加载
    expect(region!.loaded).toBe(true);
    expect(region!.visible).toBe(true);

    // 调用私有方法卸载区域
    system['unloadRegion'](region!);

    // 验证区域已卸载
    expect(region!.loaded).toBe(false);
    expect(region!.visible).toBe(false);
  });

  test('应该正确更新区域', () => {
    // 添加资源
    const resourceId = system.addResource({
      url: 'textures/test.jpg',
      type: ResourceType.TEXTURE,
      priority: ResourcePriority.MEDIUM,
    });

    // 添加场景区域（在相机附近）
    const nearRegionId = system.addRegion({
      name: 'Near Region',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(-10, -10, -10),
        new THREE.Vector3(10, 10, 10)
      ),
      resources: [resourceId],
      priority: ResourcePriority.MEDIUM,
    });

    // 添加场景区域（在相机远处）
    const farRegionId = system.addRegion({
      name: 'Far Region',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(-300, -300, -300),
        new THREE.Vector3(-250, -250, -250)
      ),
      resources: [resourceId],
      priority: ResourcePriority.MEDIUM,
    });

    // 获取相机组件
    const cameraComponent = camera.getComponent<Camera>('Camera');
    expect(cameraComponent).toBeDefined();

    // 调用私有方法更新区域
    system['updateRegions'](cameraComponent!);

    // 获取场景区域
    const nearRegion = system.getRegion(nearRegionId);
    const farRegion = system.getRegion(farRegionId);
    expect(nearRegion).toBeDefined();
    expect(farRegion).toBeDefined();

    // 验证近处区域已加载，远处区域未加载
    expect(nearRegion!.loaded).toBe(true);
    expect(farRegion!.loaded).toBe(false);
  });

  test('应该正确预测和预加载区域', () => {
    // 添加资源
    const resourceId = system.addResource({
      url: 'textures/test.jpg',
      type: ResourceType.TEXTURE,
      priority: ResourcePriority.MEDIUM,
      preload: true,
    });

    // 添加场景区域（在相机前方）
    const forwardRegionId = system.addRegion({
      name: 'Forward Region',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(0, 0, -150),
        new THREE.Vector3(50, 50, -100)
      ),
      resources: [resourceId],
      priority: ResourcePriority.MEDIUM,
    });

    // 获取相机组件
    const cameraComponent = camera.getComponent<Camera>('Camera');
    expect(cameraComponent).toBeDefined();

    // 设置相机移动历史
    system['cameraPositionHistory'] = [
      new THREE.Vector3(0, 0, 50),
      new THREE.Vector3(0, 0, 25),
      new THREE.Vector3(0, 0, 0),
    ];
    system['cameraSpeed'] = 1;

    // 调用私有方法预测和预加载区域
    system['predictAndPreload'](cameraComponent!);

    // 获取场景区域
    const forwardRegion = system.getRegion(forwardRegionId);
    expect(forwardRegion).toBeDefined();

    // 验证前方区域已预加载
    // 注意：由于预加载是基于预测的，这个测试可能不稳定
    // 实际上，我们只能验证预测逻辑被执行了
    expect(system['cameraDirection']).toBeDefined();
  });

  test('应该正确获取加载状态', () => {
    // 添加资源
    const resourceId = system.addResource({
      url: 'textures/test.jpg',
      type: ResourceType.TEXTURE,
      priority: ResourcePriority.MEDIUM,
    });

    // 添加场景区域
    const regionId = system.addRegion({
      name: 'Test Region',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(-10, -10, -10),
        new THREE.Vector3(10, 10, 10)
      ),
      resources: [resourceId],
      priority: ResourcePriority.MEDIUM,
    });

    // 获取场景区域
    const region = system.getRegion(regionId);
    expect(region).toBeDefined();

    // 调用私有方法加载区域
    system['loadRegion'](region!);

    // 获取加载状态
    const status = system.getLoadingStatus();

    // 验证加载状态
    expect(status).toBeDefined();
    expect(status.totalResources).toBe(1);
    expect(status.loadingResources).toBe(1);
    expect(status.totalRegions).toBe(1);
    expect(status.loadedRegions).toBe(1);
    expect(status.memoryUsage).toBe(0);
    expect(status.maxMemoryUsage).toBe(1024 * 1024 * 1024);
    expect(status.activeLoadTasks).toBe(0);
    expect(status.queuedLoadTasks).toBe(1);
  });

  test('应该正确销毁系统', () => {
    // 添加资源
    const resourceId = system.addResource({
      url: 'textures/test.jpg',
      type: ResourceType.TEXTURE,
      priority: ResourcePriority.MEDIUM,
    });

    // 添加场景区域
    const regionId = system.addRegion({
      name: 'Test Region',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(-10, -10, -10),
        new THREE.Vector3(10, 10, 10)
      ),
      resources: [resourceId],
      priority: ResourcePriority.MEDIUM,
    });

    // 销毁系统
    system.destroy();

    // 验证系统已清空
    expect(system['resources'].size).toBe(0);
    expect(system['regions'].size).toBe(0);
    expect(system['loadQueue']).toHaveLength(0);
    expect(system['activeLoadTasks'].size).toBe(0);
    expect(system['cache'].size).toBe(0);
    expect(system['debugMeshes']).toHaveLength(0);
    expect(system['initialized']).toBe(false);
  });
});
