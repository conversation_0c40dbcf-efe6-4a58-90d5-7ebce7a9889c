/**
 * Avatar动画组件
 * 用于管理角色动画
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { Animator } from '../../animation/Animator';
import { type EventCallback } from '../../utils/EventEmitter';
/**
 * 动画图节点类型
 */
export declare enum AnimationGraphNodeType {
    /** 单一动画 */
    SINGLE = "single",
    /** 混合空间 */
    BLEND_SPACE = "blendSpace",
    /** 状态机 */
    STATE_MACHINE = "stateMachine"
}
/**
 * 动画图节点
 */
export interface AnimationGraphNode {
    /** 节点类型 */
    type: AnimationGraphNodeType;
    /** 节点名称 */
    name: string;
    /** 节点数据 */
    data: any;
}
/**
 * 动画图
 */
export interface AnimationGraph {
    /** 节点映射 */
    nodes: Map<string, AnimationGraphNode>;
    /** 当前节点 */
    currentNode: AnimationGraphNode | null;
    /** 是否正在混合 */
    blending: boolean;
    /** 混合强度 */
    blendStrength: number;
    /** 混合目标节点 */
    blendTargetNode: AnimationGraphNode | null;
}
/**
 * Avatar动画组件
 */
export declare class AvatarAnimationComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 动画控制器 */
    animator: Animator;
    /** 动画图 */
    private animationGraph;
    /** 运动向量 */
    private locomotion;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param animator 动画控制器
     */
    constructor(animator?: Animator);
    /**
     * 设置动画控制器
     * @param animator 动画控制器
     */
    setAnimator(animator: Animator): void;
    /**
     * 获取动画控制器
     * @returns 动画控制器
     */
    getAnimator(): Animator;
    /**
     * 添加动画图节点
     * @param node 动画图节点
     */
    addGraphNode(node: AnimationGraphNode): void;
    /**
     * 移除动画图节点
     * @param name 节点名称
     */
    removeGraphNode(name: string): void;
    /**
     * 设置当前节点
     * @param name 节点名称
     * @param blendTime 混合时间（秒）
     */
    setCurrentNode(name: string, blendTime?: number): void;
    /**
     * 播放节点动画
     * @param node 动画图节点
     */
    private playNodeAnimation;
    /**
     * 播放混合动画
     * @param fromNode 源节点
     * @param toNode 目标节点
     * @param blendTime 混合时间（秒）
     */
    private playBlendAnimation;
    /**
     * 播放混合空间动画
     * @param node 动画图节点
     */
    private playBlendSpaceAnimation;
    /**
     * 播放状态机动画
     * @param node 动画图节点
     */
    private playStateMachineAnimation;
    /**
     * 设置运动向量
     * @param x X分量
     * @param y Y分量
     * @param z Z分量
     */
    setLocomotion(x: number, y: number, z: number): void;
    /**
     * 获取运动向量
     * @returns 运动向量
     */
    getLocomotion(): THREE.Vector3;
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新节点
     * @param node 动画图节点
     * @param deltaTime 时间增量（秒）
     */
    private updateNode;
    /**
     * 更新混合空间
     * @param node 动画图节点
     * @param deltaTime 时间增量（秒）
     */
    private updateBlendSpace;
    /**
     * 更新状态机
     * @param node 动画图节点
     * @param deltaTime 时间增量（秒）
     */
    private updateStateMachine;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    addEventListener(type: string, callback: EventCallback): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    removeEventListener(type: string, callback: EventCallback): void;
    /**
     * 克隆组件
     * @returns 克隆的组件
     */
    clone(): AvatarAnimationComponent;
}
