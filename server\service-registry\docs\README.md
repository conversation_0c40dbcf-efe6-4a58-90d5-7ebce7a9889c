# 服务注册中心文档

## 概述

服务注册中心是DL（Digital Learning）引擎微服务架构的核心组件，负责服务的注册、发现和负载均衡。它提供了以下主要功能：

1. **服务注册与发现**：允许服务实例注册自己，并允许客户端发现可用的服务实例
2. **健康检查**：定期检查服务实例的健康状态，自动剔除不健康的实例
3. **负载均衡**：提供多种负载均衡策略，包括随机、轮询、加权轮询、最少响应时间、一致性哈希和区域感知
4. **服务缓存**：支持多级缓存（内存和Redis），提高服务发现的性能
5. **监控和告警**：提供服务注册中心的监控指标和告警功能

## 安装和配置

### 环境要求

- Node.js 14+
- TypeScript 4.5+
- MySQL 8.0+
- Redis 6.0+（可选，用于分布式缓存）

### 安装依赖

```bash
cd newsystem/server/service-registry
npm install
```

### 配置

服务注册中心使用环境变量进行配置。创建一个`.env`文件，包含以下配置项：

```env
# 基本配置
NODE_ENV=development
SERVICE_REGISTRY_PORT=3010
SERVICE_REGISTRY_HTTP_PORT=4010

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=ir_service_registry

# Redis配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 服务缓存配置
SERVICE_CACHE_ENABLED=true
SERVICE_CACHE_LEVELS=memory,redis
SERVICE_CACHE_MEMORY_TTL=60000
SERVICE_CACHE_REDIS_TTL=300000
SERVICE_CACHE_MAX_ENTRIES=1000

# 监控配置
MONITORING_MAX_METRICS_HISTORY=1440
MONITORING_MAX_ALERTS_HISTORY=1000
MONITORING_CPU_THRESHOLD=0.8
MONITORING_MEMORY_THRESHOLD=0.8
MONITORING_UNHEALTHY_INSTANCE_THRESHOLD=0.2
MONITORING_CACHE_HIT_RATE_THRESHOLD=0.5
MONITORING_LOAD_BALANCER_ERROR_RATE_THRESHOLD=0.05
```

### 启动服务

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## API参考

### 服务注册

#### 注册服务实例

```http
POST /registry/services
Content-Type: application/json

{
  "name": "user-service",
  "instanceId": "user-service-1",
  "host": "localhost",
  "port": 3001,
  "httpPort": 4001,
  "metadata": {
    "version": "1.0.0",
    "weight": 1,
    "zone": "zone-1"
  }
}
```

**响应**:

```json
{
  "id": "instance-1",
  "serviceId": "service-1",
  "instanceId": "user-service-1",
  "host": "localhost",
  "port": 3001,
  "httpPort": 4001,
  "status": "UP",
  "metadata": {
    "version": "1.0.0",
    "weight": 1,
    "zone": "zone-1"
  },
  "registeredAt": "2023-05-01T12:00:00Z",
  "lastHeartbeat": "2023-05-01T12:00:00Z",
  "isHealthy": true
}
```

#### 发送心跳

```http
PUT /registry/services/{serviceName}/instances/{instanceId}/heartbeat
Content-Type: application/json

{
  "status": "UP"
}
```

**响应**:

```json
{
  "success": true
}
```

#### 注销服务实例

```http
DELETE /registry/services/{serviceName}/instances/{instanceId}
```

**响应**:

```json
{
  "success": true
}
```

### 服务发现

#### 获取所有服务

```http
GET /registry/services
```

**响应**:

```json
[
  {
    "id": "service-1",
    "name": "user-service",
    "description": "用户服务",
    "instances": [
      {
        "id": "instance-1",
        "instanceId": "user-service-1",
        "host": "localhost",
        "port": 3001,
        "status": "UP",
        "isHealthy": true
      }
    ]
  }
]
```

#### 获取服务实例

```http
GET /registry/services/{serviceName}/instances?onlyHealthy=true&algorithm=random
```

**响应**:

```json
[
  {
    "id": "instance-1",
    "instanceId": "user-service-1",
    "host": "localhost",
    "port": 3001,
    "status": "UP",
    "isHealthy": true
  }
]
```

### 负载均衡

#### 获取负载均衡策略

```http
GET /registry/load-balancer/strategies
```

**响应**:

```json
[
  {
    "name": "random",
    "config": {
      "algorithm": "random",
      "enableStickySession": false
    }
  },
  {
    "name": "round-robin",
    "config": {
      "algorithm": "round-robin",
      "enableStickySession": false
    }
  }
]
```

#### 设置服务负载均衡配置

```http
PUT /registry/services/{serviceName}/load-balancer
Content-Type: application/json

{
  "algorithm": "consistent-hash",
  "enableStickySession": true,
  "stickySessionTimeout": 1800000,
  "options": {
    "virtualNodeCount": 200
  }
}
```

**响应**:

```json
{
  "success": true
}
```

### 服务缓存

#### 获取缓存统计信息

```http
GET /registry/cache/stats
```

**响应**:

```json
{
  "hits": 1000,
  "misses": 200,
  "hitRate": 0.83,
  "size": 50,
  "memoryUsage": 1024000
}
```

#### 清除缓存

```http
POST /registry/cache/clear
```

**响应**:

```json
{
  "success": true
}
```

### 监控和告警

#### 获取监控指标

```http
GET /monitoring/metrics?limit=10
```

**响应**:

```json
[
  {
    "timestamp": 1683025200000,
    "cpuUsage": 0.25,
    "memoryUsage": 0.4,
    "totalMemory": **********,
    "freeMemory": **********,
    "uptime": 3600,
    "loadAverage": [0.5, 0.3, 0.2],
    "serviceCount": 5,
    "instanceCount": 10,
    "healthyInstanceCount": 9,
    "unhealthyInstanceCount": 1,
    "cacheStats": {
      "hits": 1000,
      "misses": 200,
      "hitRate": 0.83,
      "size": 50,
      "memoryUsage": 1024000
    },
    "loadBalancerStats": {
      "requestCount": 5000,
      "errorCount": 10,
      "avgResponseTime": 5.2
    }
  }
]
```

#### 获取告警

```http
GET /monitoring/alerts?onlyUnresolved=true
```

**响应**:

```json
[
  {
    "id": "cpu-1683025200000",
    "type": "cpu",
    "severity": "warning",
    "message": "CPU使用率过高: 85.5%",
    "timestamp": 1683025200000,
    "value": 0.855,
    "threshold": 0.8,
    "resolved": false
  }
]
```

## 负载均衡策略

服务注册中心支持以下负载均衡策略：

### 1. 随机（Random）

随机选择一个服务实例。适用于服务实例性能相近且负载均匀的场景。

### 2. 轮询（Round Robin）

按顺序轮流选择服务实例。确保每个实例被均匀访问。

### 3. 加权轮询（Weighted Round Robin）

根据实例的权重进行轮询选择。权重越高的实例被选中的概率越大。

### 4. 最少响应时间（Least Response Time）

选择响应时间最短的实例。适用于对延迟敏感的场景。

### 5. 一致性哈希（Consistent Hash）

根据请求的特定属性（如会话ID、客户端IP）将请求映射到固定的实例。适用于需要会话粘性的场景。

### 6. 区域感知（Zone Aware）

优先选择与客户端在同一区域的实例。适用于多区域部署的场景，可以减少跨区域访问的延迟。

## 服务缓存机制

服务注册中心支持多级缓存，提高服务发现的性能：

### 1. 内存缓存

- 默认TTL：60秒
- 适用于单实例部署

### 2. Redis缓存

- 默认TTL：300秒
- 适用于多实例部署，支持缓存共享

## 监控和告警机制

服务注册中心提供以下监控指标和告警：

### 监控指标

- CPU使用率
- 内存使用率
- 服务和实例数量
- 健康/不健康实例比例
- 缓存命中率和内存使用
- 负载均衡请求统计

### 告警类型

- CPU使用率过高
- 内存使用率过高
- 不健康实例比例过高
- 缓存命中率过低
- 负载均衡错误率过高

## 最佳实践

### 高可用部署

为确保服务注册中心的高可用性，建议：

1. 部署多个服务注册中心实例
2. 使用Redis作为共享缓存
3. 配置数据库主从复制
4. 使用负载均衡器分发请求

### 性能优化

1. 启用服务缓存
2. 适当调整缓存TTL
3. 根据服务特性选择合适的负载均衡策略
4. 定期清理不活跃的服务实例

### 安全建议

1. 启用HTTPS
2. 配置访问控制
3. 限制敏感API的访问
4. 定期审计服务注册记录
