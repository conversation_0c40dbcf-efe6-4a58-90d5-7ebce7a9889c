/**
 * 水体分块系统
 * 用于大规模水体的高效渲染
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import type { Camera   } from '../Camera';

import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';
import { Octree } from '../optimization/spatial/Octree';

/**
 * 水体块
 */
export interface WaterChunk {
  /** 唯一ID */
  id: string;
  /** 名称 */
  name: string;
  /** 包围盒 */
  boundingBox: THREE.Box3;
  /** 包围球 */
  boundingSphere: THREE.Sphere;
  /** 中心点 */
  center: THREE.Vector3;
  /** 尺寸 */
  size: THREE.Vector3;
  /** LOD级别 */
  level: number;
  /** 可见性 */
  visible: boolean;
  /** 是否已加载 */
  loaded: boolean;
  /** 是否正在加载 */
  loading: boolean;
  /** 加载进度 */
  progress: number;
  /** 网格 */
  mesh?: THREE.Mesh;
  /** 父块 */
  parent: WaterChunk | null;
  /** 子块 */
  children: WaterChunk[];
  /** 相邻块 */
  neighbors: WaterChunk[];
  /** 用户数据 */
  userData: any;
}

/**
 * 水体分块系统配置
 */
export interface WaterChunkSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 块大小 */
  chunkSize?: number;
  /** 最大LOD级别 */
  maxLODLevel?: number;
  /** 是否使用LOD */
  useLOD?: boolean;
  /** LOD距离 */
  lodDistances?: number[];
  /** 是否使用视锥体剔除 */
  useFrustumCulling?: boolean;
  /** 是否使用八叉树 */
  useOctree?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 加载距离 */
  loadDistance?: number;
  /** 卸载距离 */
  unloadDistance?: number;
}

/**
 * 水体分块系统事件类型
 */
export enum WaterChunkSystemEventType {
  /** 块创建 */
  CHUNK_CREATED = 'chunkCreated',
  /** 块加载 */
  CHUNK_LOADED = 'chunkLoaded',
  /** 块卸载 */
  CHUNK_UNLOADED = 'chunkUnloaded',
  /** 块可见性变化 */
  CHUNK_VISIBILITY_CHANGED = 'chunkVisibilityChanged',
  /** 块LOD级别变化 */
  CHUNK_LOD_CHANGED = 'chunkLODChanged'
}

/**
 * 水体分块系统
 */
export class WaterChunkSystem extends System {
  /** 系统类型 */
  public static readonly TYPE = 'WaterChunkSystem';

  /** 配置 */
  private config: Required<WaterChunkSystemConfig>;
  /** 水体实体映射 */
  private waterEntities: Map<Entity, WaterBodyComponent> = new Map();
  /** 实体到块映射 */
  private entityToChunks: Map<string, Set<string>> = new Map();
  /** 水体块映射 */
  private waterChunks: Map<string, WaterChunk> = new Map();
  /** 八叉树 */
  private octree?: Octree;
  /** 视锥体 */
  private frustum: THREE.Frustum = new THREE.Frustum();
  /** 临时矩阵 */
  private tempMatrix: THREE.Matrix4 = new THREE.Matrix4();

  /** 帧计数器 */
  private frameCount: number = 0;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 调试对象 */
  private debugObjects: THREE.Object3D[] = [];

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: WaterChunkSystemConfig = {}) {
    super();

    // 设置默认配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      chunkSize: config.chunkSize || 100,
      maxLODLevel: config.maxLODLevel || 4,
      useLOD: config.useLOD !== undefined ? config.useLOD : true,
      lodDistances: config.lodDistances || [100, 300, 600, 1000],
      useFrustumCulling: config.useFrustumCulling !== undefined ? config.useFrustumCulling : true,
      useOctree: config.useOctree !== undefined ? config.useOctree : true,
      useDebugVisualization: config.useDebugVisualization !== undefined ? config.useDebugVisualization : false,
      loadDistance: config.loadDistance || 1500,
      unloadDistance: config.unloadDistance || 2000
    };

    // 初始化八叉树
    if (this.config.useOctree) {
      this.initializeOctree();
    }

    // 初始化调试可视化
    if (this.config.useDebugVisualization) {
      this.initializeDebugVisualization();
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    Debug.log('WaterChunkSystem', '水体分块系统初始化');
  }

  /**
   * 检查系统是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * 初始化八叉树
   */
  private initializeOctree(): void {
    this.octree = new Octree({
      maxDepth: 8,
      maxObjectsPerNode: 16,
      minNodeSize: this.config.chunkSize / 2
    });
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试容器
    const debugContainer = new THREE.Object3D();
    debugContainer.name = 'WaterChunkDebug';

    // 添加到场景
    const activeScene = this.world.getActiveScene();
    if (activeScene) {
      const threeScene = (activeScene as any).getThreeScene();
      if (threeScene) {
        threeScene.add(debugContainer);
      }
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    if (!this.isEnabled() || !this.config.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.useDebugVisualization) {
      this.performanceMonitor.beginMeasure('waterChunkUpdate');
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 更新视锥体
    if (this.config.useFrustumCulling) {
      this.updateFrustum(camera);
    }

    // 更新所有块
    this.updateChunks(camera);

    // 更新调试可视化
    if (this.config.useDebugVisualization) {
      this.updateDebugVisualization();
      this.performanceMonitor.endMeasure('waterChunkUpdate');
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // 简化实现：返回null，让调用者处理
    // 在实际使用中，应该通过其他方式获取相机
    return null;
  }

  /**
   * 更新视锥体
   * @param camera 相机
   */
  private updateFrustum(camera: Camera): void {
    // 获取相机的投影矩阵和视图矩阵
    const projectionMatrix = camera.getThreeCamera().projectionMatrix;
    const matrixWorldInverse = camera.getThreeCamera().matrixWorldInverse;

    // 计算投影视图矩阵
    this.tempMatrix.multiplyMatrices(projectionMatrix, matrixWorldInverse);

    // 更新视锥体
    this.frustum.setFromProjectionMatrix(this.tempMatrix);
  }

  /**
   * 添加水体实体
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterEntity(entity: Entity, component: WaterBodyComponent): void {
    this.waterEntities.set(entity, component);
    this.entityToChunks.set(entity.id, new Set());
    this.createChunksForWater(entity, component);
  }

  /**
   * 移除水体实体
   * @param entity 实体
   */
  public removeWaterEntity(entity: Entity): void {
    // 获取实体的块
    const chunks = this.entityToChunks.get(entity.id);
    if (chunks) {
      // 移除所有块
      for (const chunkId of chunks) {
        this.removeChunk(chunkId);
      }
    }

    // 移除实体
    this.waterEntities.delete(entity);
    this.entityToChunks.delete(entity.id);
  }

  /**
   * 为水体创建块
   * @param entity 实体
   * @param component 水体组件
   */
  private createChunksForWater(entity: Entity, component: WaterBodyComponent): void {
    // 根据水体组件的尺寸创建包围盒
    const waterSize = component.getSize();
    const position = component.getPosition();

    const boundingBox = new THREE.Box3(
      new THREE.Vector3(
        position.x - waterSize.width / 2,
        position.y - waterSize.height / 2,
        position.z - waterSize.depth / 2
      ),
      new THREE.Vector3(
        position.x + waterSize.width / 2,
        position.y + waterSize.height / 2,
        position.z + waterSize.depth / 2
      )
    );

    // 计算块的数量
    const min = boundingBox.min;
    const max = boundingBox.max;
    const size = new THREE.Vector3().subVectors(max, min);

    const chunkSize = this.config.chunkSize;
    const chunksX = Math.ceil(size.x / chunkSize);
    const chunksZ = Math.ceil(size.z / chunkSize);

    Debug.log('WaterChunkSystem', `为水体实体 ${entity.id} 创建 ${chunksX}x${chunksZ} 个块`);

    // 创建块
    for (let x = 0; x < chunksX; x++) {
      for (let z = 0; z < chunksZ; z++) {
        // 计算块的位置和大小
        const chunkMin = new THREE.Vector3(
          min.x + x * chunkSize,
          min.y,
          min.z + z * chunkSize
        );
        const chunkMax = new THREE.Vector3(
          Math.min(chunkMin.x + chunkSize, max.x),
          max.y,
          Math.min(chunkMin.z + chunkSize, max.z)
        );
        const chunkBoundingBox = new THREE.Box3(chunkMin, chunkMax);

        // 创建块
        this.createChunk(entity, component, chunkBoundingBox, x, z);
      }
    }
  }

  /**
   * 创建块
   * @param entity 实体
   * @param component 水体组件
   * @param boundingBox 包围盒
   * @param x X坐标
   * @param z Z坐标
   */
  private createChunk(
    entity: Entity,
    _component: WaterBodyComponent,
    boundingBox: THREE.Box3,
    x: number,
    z: number
  ): void {
    // 生成块ID
    const chunkId = `${entity.id}_${x}_${z}`;

    // 计算中心点和大小
    const center = new THREE.Vector3();
    boundingBox.getCenter(center);
    const size = new THREE.Vector3();
    boundingBox.getSize(size);

    // 创建包围球
    const boundingSphere = new THREE.Sphere(center, Math.max(size.x, size.z) / 2);

    // 创建块
    const chunk: WaterChunk = {
      id: chunkId,
      name: `WaterChunk_${x}_${z}`,
      boundingBox,
      boundingSphere,
      center,
      size,
      level: 0,
      visible: false,
      loaded: false,
      loading: false,
      progress: 0,
      parent: null,
      children: [],
      neighbors: [],
      userData: {
        entityId: entity.id,
        x,
        z
      }
    };

    // 添加到映射
    this.waterChunks.set(chunkId, chunk);
    this.entityToChunks.get(entity.id)?.add(chunkId);

    // 如果使用八叉树，添加到八叉树
    if (this.config.useOctree && this.octree) {
      // 创建一个虚拟实体对象用于八叉树
      const dummyEntity = { id: chunkId } as Entity;
      this.octree.insert(dummyEntity, center, boundingSphere.radius);
    }

    // 发出事件
    this.eventEmitter.emit(WaterChunkSystemEventType.CHUNK_CREATED, chunk);

    Debug.log('WaterChunkSystem', `创建水体块: ${chunkId}`);
  }

  /**
   * 移除块
   * @param chunkId 块ID
   */
  private removeChunk(chunkId: string): void {
    // 获取块
    const chunk = this.waterChunks.get(chunkId);
    if (!chunk) {
      return;
    }

    // 如果块已加载，卸载块
    if (chunk.loaded) {
      this.unloadChunk(chunk);
    }

    // 如果使用八叉树，从八叉树中移除
    if (this.config.useOctree && this.octree) {
      const dummyEntity = { id: chunkId } as Entity;
      this.octree.remove(dummyEntity);
    }

    // 从映射中移除
    this.waterChunks.delete(chunkId);
    const entityId = chunk.userData.entityId;
    this.entityToChunks.get(entityId)?.delete(chunkId);

    // 发出事件
    this.eventEmitter.emit(WaterChunkSystemEventType.CHUNK_UNLOADED, chunk);

    Debug.log('WaterChunkSystem', `移除水体块: ${chunkId}`);
  }

  /**
   * 更新所有块
   * @param camera 相机
   */
  private updateChunks(camera: Camera): void {
    // 如果使用八叉树，使用八叉树更新块
    if (this.config.useOctree && this.octree) {
      this.updateChunksWithOctree(camera);
    } else {
      // 否则，遍历所有块
      this.updateChunksWithBruteForce(camera);
    }
  }

  /**
   * 使用八叉树更新块
   * @param camera 相机
   */
  private updateChunksWithOctree(camera: Camera): void {
    if (!this.octree) {
      return;
    }

    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 获取视锥体内的实体
    const visibleEntities = this.octree.queryFrustum(this.frustum);

    // 遍历可见实体
    for (const entity of visibleEntities) {
      const chunkId = entity.id;
      const chunk = this.waterChunks.get(chunkId);
      if (!chunk) {
        continue;
      }

      // 计算相机到块中心的距离
      const distance = cameraPosition.distanceTo(chunk.center);

      // 更新块的LOD级别
      if (this.config.useLOD) {
        this.updateChunkLOD(chunk, distance);
      }

      // 如果距离小于加载距离，则加载块
      if (distance < this.config.loadDistance) {
        this.loadChunk(chunk);
      }
      // 如果距离大于卸载距离，则卸载块
      else if (distance > this.config.unloadDistance) {
        this.unloadChunk(chunk);
      }

      // 设置块可见
      this.setChunkVisible(chunk, true);
    }

    // 隐藏不在视锥体内的块
    for (const chunk of this.waterChunks.values()) {
      if (chunk.visible) {
        // 检查块是否在视锥体内
        if (!this.frustum.intersectsSphere(chunk.boundingSphere)) {
          this.setChunkVisible(chunk, false);
        }
      }
    }
  }

  /**
   * 使用暴力方法更新块
   * @param camera 相机
   */
  private updateChunksWithBruteForce(camera: Camera): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有块
    for (const chunk of this.waterChunks.values()) {
      // 计算相机到块中心的距离
      const distance = cameraPosition.distanceTo(chunk.center);

      // 检查块是否在视锥体内
      const isVisible = !this.config.useFrustumCulling || this.frustum.intersectsSphere(chunk.boundingSphere);

      // 设置块可见性
      this.setChunkVisible(chunk, isVisible);

      // 更新块的LOD级别
      if (this.config.useLOD && isVisible) {
        this.updateChunkLOD(chunk, distance);
      }

      // 如果距离小于加载距离，则加载块
      if (distance < this.config.loadDistance) {
        this.loadChunk(chunk);
      }
      // 如果距离大于卸载距离，则卸载块
      else if (distance > this.config.unloadDistance) {
        this.unloadChunk(chunk);
      }
    }
  }

  /**
   * 更新块的LOD级别
   * @param chunk 块
   * @param distance 距离
   */
  private updateChunkLOD(chunk: WaterChunk, distance: number): void {
    // 确定LOD级别
    let newLevel = 0;
    for (let i = 0; i < this.config.lodDistances.length; i++) {
      if (distance > this.config.lodDistances[i]) {
        newLevel = i + 1;
      }
    }

    // 如果LOD级别发生变化，更新块
    if (chunk.level !== newLevel) {
      const oldLevel = chunk.level;
      chunk.level = newLevel;

      // 发出事件
      this.eventEmitter.emit(WaterChunkSystemEventType.CHUNK_LOD_CHANGED, chunk, oldLevel, newLevel);

      // 如果块已加载，更新块的网格
      if (chunk.loaded && chunk.mesh) {
        this.updateChunkMesh(chunk);
      }
    }
  }

  /**
   * 加载块
   * @param chunk 块
   */
  private loadChunk(chunk: WaterChunk): void {
    // 如果块已加载或正在加载，则跳过
    if (chunk.loaded || chunk.loading) {
      return;
    }

    // 标记为正在加载
    chunk.loading = true;
    chunk.progress = 0;

    // 获取水体组件
    const entityId = chunk.userData.entityId;
    const entity = this.world.getEntity(entityId);
    if (!entity) {
      chunk.loading = false;
      return;
    }

    const component = this.waterEntities.get(entity);
    if (!component) {
      chunk.loading = false;
      return;
    }

    // 创建水体网格
    this.createWaterMesh(chunk, component);

    // 标记为已加载
    chunk.loaded = true;
    chunk.loading = false;
    chunk.progress = 1;

    // 发出事件
    this.eventEmitter.emit(WaterChunkSystemEventType.CHUNK_LOADED, chunk);

    Debug.log('WaterChunkSystem', `加载水体块: ${chunk.id}`);
  }

  /**
   * 卸载块
   * @param chunk 块
   */
  private unloadChunk(chunk: WaterChunk): void {
    // 如果块未加载，则跳过
    if (!chunk.loaded) {
      return;
    }

    // 移除网格
    if (chunk.mesh) {
      // 从场景中移除
      const activeScene = this.world.getActiveScene();
      if (activeScene) {
        const threeScene = (activeScene as any).getThreeScene();
        if (threeScene) {
          threeScene.remove(chunk.mesh);
        }
      }
      // 释放资源
      (chunk.mesh.geometry as any).dispose();
      (chunk.mesh.material as THREE.Material).dispose();
      chunk.mesh = undefined;
    }

    // 标记为未加载
    chunk.loaded = false;
    chunk.loading = false;
    chunk.progress = 0;

    // 发出事件
    this.eventEmitter.emit(WaterChunkSystemEventType.CHUNK_UNLOADED, chunk);

    Debug.log('WaterChunkSystem', `卸载水体块: ${chunk.id}`);
  }

  /**
   * 设置块可见性
   * @param chunk 块
   * @param visible 是否可见
   */
  private setChunkVisible(chunk: WaterChunk, visible: boolean): void {
    // 如果可见性未变化，则跳过
    if (chunk.visible === visible) {
      return;
    }

    // 更新可见性
    chunk.visible = visible;

    // 更新网格可见性
    if (chunk.mesh) {
      chunk.mesh.visible = visible;
    }

    // 发出事件
    this.eventEmitter.emit(WaterChunkSystemEventType.CHUNK_VISIBILITY_CHANGED, chunk, visible);
  }

  /**
   * 创建水体网格
   * @param chunk 块
   * @param component 水体组件
   */
  private createWaterMesh(chunk: WaterChunk, component: WaterBodyComponent): void {
    // 创建几何体
    const geometry = this.createWaterGeometry(chunk, component);

    // 创建材质
    const waterMaterial = component.getWaterMaterial();
    const material = waterMaterial ? waterMaterial.clone() : new THREE.MeshStandardMaterial({
      color: 0x0055ff,
      transparent: true,
      opacity: 0.8
    });

    // 根据LOD级别调整材质质量
    this.adjustMaterialQuality(material, chunk.level);

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);
    mesh.name = chunk.name;
    mesh.position.copy(chunk.center);
    mesh.position.y = component.getPosition().y;
    mesh.visible = chunk.visible;

    // 设置网格
    chunk.mesh = mesh;

    // 添加到场景
    const activeScene = this.world.getActiveScene();
    if (activeScene) {
      const threeScene = (activeScene as any).getThreeScene();
      if (threeScene) {
        threeScene.add(mesh);
      }
    }
  }

  /**
   * 创建水体几何体
   * @param chunk 块
   * @param component 水体组件
   * @returns 几何体
   */
  private createWaterGeometry(chunk: WaterChunk, _component: WaterBodyComponent): THREE.BufferGeometry {
    // 根据LOD级别确定分辨率
    const resolution = this.getResolutionForLOD(chunk.level);

    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(
      chunk.size.x,
      chunk.size.z,
      resolution,
      resolution
    );

    // 旋转几何体，使其水平
    geometry.rotateX(-Math.PI / 2);

    return geometry;
  }

  /**
   * 根据LOD级别获取分辨率
   * @param level LOD级别
   * @returns 分辨率
   */
  private getResolutionForLOD(level: number): number {
    // 根据LOD级别返回不同的分辨率
    switch (level) {
      case 0: return 64; // 最高质量
      case 1: return 32;
      case 2: return 16;
      case 3: return 8;
      default: return 4; // 最低质量
    }
  }

  /**
   * 调整材质质量
   * @param material 材质
   * @param level LOD级别
   */
  private adjustMaterialQuality(material: THREE.Material, level: number): void {
    if (material instanceof THREE.ShaderMaterial) {
      // 根据LOD级别调整着色器参数
      const uniforms = material.uniforms;
      if (uniforms.waveStrength) {
        uniforms.waveStrength.value *= Math.max(0.1, 1.0 - level * 0.2);
      }
      if (uniforms.reflectionStrength) {
        uniforms.reflectionStrength.value *= Math.max(0.1, 1.0 - level * 0.25);
      }
      if (uniforms.refractionStrength) {
        uniforms.refractionStrength.value *= Math.max(0.1, 1.0 - level * 0.25);
      }
    }
  }

  /**
   * 更新块网格
   * @param chunk 块
   */
  private updateChunkMesh(chunk: WaterChunk): void {
    // 如果块未加载或没有网格，则跳过
    if (!chunk.loaded || !chunk.mesh) {
      return;
    }

    // 获取水体组件
    const entityId = chunk.userData.entityId;
    const entity = this.world.getEntity(entityId);
    if (!entity) {
      return;
    }

    const component = this.waterEntities.get(entity);
    if (!component) {
      return;
    }

    // 更新几何体
    const oldGeometry = chunk.mesh.geometry;
    const newGeometry = this.createWaterGeometry(chunk, component);
    chunk.mesh.geometry = newGeometry;
    (oldGeometry as any).dispose();

    // 更新材质
    const material = chunk.mesh.material as THREE.Material;
    this.adjustMaterialQuality(material, chunk.level);
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    const activeScene = this.world.getActiveScene();
    if (!activeScene) {
      return;
    }

    const threeScene = (activeScene as any).getThreeScene();
    if (!threeScene) {
      return;
    }

    // 清除旧的调试对象
    for (const obj of this.debugObjects) {
      threeScene.remove(obj);
    }
    this.debugObjects = [];

    // 创建新的调试对象
    for (const chunk of this.waterChunks.values()) {
      if (chunk.visible) {
        // 创建包围盒辅助对象
        const boxHelper = new THREE.Box3Helper(chunk.boundingBox, new THREE.Color(0, 1, 1));
        threeScene.add(boxHelper);
        this.debugObjects.push(boxHelper);

        // 创建文本标签
        // 这里需要使用TextSprite或类似的库来创建3D文本
        // 暂时省略
      }
    }
  }

}
