/**
 * 项目案例教程面板组件
 * 展示项目案例教程
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  List, 
  Tag, 
  Button, 
  Typography, 
  Tooltip, 
  Empty, 
  Skeleton, 
  Badge,
  Modal,
  Input,
  Select,
  Form,
  message,
  Tabs } from 'antd';
import { 
  CodeOutlined, 
  DownloadOutlined, 
  EyeOutlined, 
  StarOutlined, 
  StarFilled,
  ClockCircleOutlined,
  TagOutlined,
  SearchOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  FireOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

import { ExampleProjectService, ExampleProject } from '../../services/ExampleProjectService';
import './ProjectTutorialPanel.less';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

/**
 * 项目案例教程面板属性接口
 */
interface ProjectTutorialPanelProps {
  onProjectSelect?: (project: ExampleProject) => void;
}

/**
 * 项目案例教程面板组件
 */
export const ProjectTutorialPanel: React.FC<ProjectTutorialPanelProps> = ({
  onProjectSelect}) => {
  const { t } = useTranslation();
  const [projects, setProjects] = useState<ExampleProject[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<ExampleProject[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [searchText, setSearchText] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('popularity');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<ExampleProject | null>(null);
  const [projectName, setProjectName] = useState<string>('');
  const [importLoading, setImportLoading] = useState<boolean>(false);
  const [categories, setCategories] = useState<string[]>([]);

  // 初始化
  useEffect(() => {
    loadProjects();

    // 监听项目查看事件
    const handleProjectViewed = () => {
      loadProjects();
    };

    ExampleProjectService.getInstance().on('exampleViewed', handleProjectViewed);
    ExampleProjectService.getInstance().on('exampleFavorited', handleProjectViewed);
    ExampleProjectService.getInstance().on('exampleUnfavorited', handleProjectViewed);

    return () => {
      ExampleProjectService.getInstance().off('exampleViewed', handleProjectViewed);
      ExampleProjectService.getInstance().off('exampleFavorited', handleProjectViewed);
      ExampleProjectService.getInstance().off('exampleUnfavorited', handleProjectViewed);
    };
  }, []);

  // 过滤和排序项目
  useEffect(() => {
    filterAndSortProjects();
  }, [projects, searchText, selectedCategory, selectedDifficulty, sortBy, activeTab]);

  /**
   * 加载项目数据
   */
  const loadProjects = () => {
    setLoading(true);
    
    // 获取所有示例项目
    const allProjects = ExampleProjectService.getInstance().getExampleProjects();
    setProjects(allProjects);
    
    // 提取所有类别
    const categorySet = new Set<string>();
    allProjects.forEach(project => {
      categorySet.add(project.category);
    });
    setCategories(Array.from(categorySet).sort());
    
    setLoading(false);
  };

  /**
   * 过滤和排序项目
   */
  const filterAndSortProjects = () => {
    let filtered = [...projects];
    
    // 根据标签页过滤
    if (activeTab === 'favorites') {
      filtered = filtered.filter(project => 
        ExampleProjectService.getInstance().isExampleFavorited(project.id)
      );
    } else if (activeTab === 'recent') {
      const recentProjects = ExampleProjectService.getInstance().getRecentlyViewedExampleProjects(20);
      filtered = recentProjects;
    }
    
    // 根据搜索文本过滤
    if (searchText) {
      const lowerSearchText = searchText.toLowerCase();
      filtered = filtered.filter(project => 
        project.title.toLowerCase().includes(lowerSearchText) ||
        project.description.toLowerCase().includes(lowerSearchText) ||
        project.tags?.some(tag => tag.toLowerCase().includes(lowerSearchText))
      );
    }
    
    // 根据类别过滤
    if (selectedCategory) {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }
    
    // 根据难度过滤
    if (selectedDifficulty) {
      filtered = filtered.filter(project => project.difficulty === selectedDifficulty);
    }
    
    // 排序
    switch (sortBy) {
      case 'title':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'difficulty':
        const difficultyOrder = {
          'beginner': 0,
          'intermediate': 1,
          'advanced': 2,
          'expert': 3
        };
        filtered.sort((a, b) => 
          (difficultyOrder[a.difficulty as keyof typeof difficultyOrder] || 0) - 
          (difficultyOrder[b.difficulty as keyof typeof difficultyOrder] || 0)
        );
        break;
      case 'rating':
        filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      case 'popularity':
      default:
        filtered.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
        break;
    }
    
    setFilteredProjects(filtered);
  };

  /**
   * 处理项目点击
   */
  const handleProjectClick = (project: ExampleProject) => {
    if (onProjectSelect) {
      onProjectSelect(project);
      return;
    }

    // 标记项目为已查看
    ExampleProjectService.getInstance().markExampleAsViewed(project.id);
    
    // 打开项目页面
    window.open(`/examples/${project.id}/`, '_blank');
  };

  /**
   * 处理导入项目
   */
  const handleImportProject = (project: ExampleProject) => {
    setSelectedProject(project);
    setProjectName(project.title);
    setImportModalVisible(true);
  };

  /**
   * 确认导入项目
   */
  const confirmImportProject = async () => {
    if (!selectedProject) return;
    
    setImportLoading(true);
    
    try {
      const success = await ExampleProjectService.getInstance().importExampleProject(
        selectedProject.id,
        projectName
      );
      
      if (success) {
        message.success(t('projects.importSuccess', { name: projectName }));
        setImportModalVisible(false);
      } else {
        message.error(t('projects.importError'));
      }
    } catch (error) {
      console.error('Error importing project:', error);
      message.error(t('projects.importError'));
    } finally {
      setImportLoading(false);
    }
  };

  /**
   * 处理收藏项目
   */
  const handleFavoriteProject = (e: React.MouseEvent, project: ExampleProject) => {
    e.stopPropagation();
    
    if (ExampleProjectService.getInstance().isExampleFavorited(project.id)) {
      ExampleProjectService.getInstance().unfavoriteExample(project.id);
      message.success(t('projects.unfavoriteSuccess', { name: project.title }));
    } else {
      ExampleProjectService.getInstance().favoriteExample(project.id);
      message.success(t('projects.favoriteSuccess', { name: project.title }));
    }
  };

  /**
   * 获取难度标签颜色
   */
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'green';
      case 'intermediate':
        return 'blue';
      case 'advanced':
        return 'orange';
      case 'expert':
        return 'red';
      default:
        return 'default';
    }
  };

  /**
   * 获取难度文本
   */
  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return t('tutorials.difficulty.beginner');
      case 'intermediate':
        return t('tutorials.difficulty.intermediate');
      case 'advanced':
        return t('tutorials.difficulty.advanced');
      case 'expert':
        return t('tutorials.difficulty.expert');
      default:
        return t('tutorials.difficulty.unknown');
    }
  };

  /**
   * 渲染项目卡片（网格视图）
   */
  const renderProjectCard = (project: ExampleProject) => {
    const isFavorited = ExampleProjectService.getInstance().isExampleFavorited(project.id);
    const isViewed = ExampleProjectService.getInstance().isExampleViewed(project.id);
    
    return (
      <Card 
        className="project-card"
        hoverable
        onClick={() => handleProjectClick(project)}
        cover={
          project.thumbnailUrl ? (
            <div className="project-cover">
              <img alt={project.title} src={project.thumbnailUrl} />
              {isViewed && (
                <div className="project-viewed-badge">
                  <EyeOutlined />
                </div>
              )}
            </div>
          ) : null
        }
        actions={[
          <Tooltip title={isFavorited ? t('projects.unfavorite') : t('projects.favorite')}>
            <Button 
              type="text" 
              icon={isFavorited ? <StarFilled className="favorited" /> : <StarOutlined />}
              onClick={(e) => handleFavoriteProject(e, project)}
            />
          </Tooltip>,
          <Tooltip title={t('projects.view')}>
            <Button 
              type="text" 
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleProjectClick(project);
              }}
            />
          </Tooltip>,
          <Tooltip title={t('projects.import')}>
            <Button 
              type="text" 
              icon={<DownloadOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleImportProject(project);
              }}
            />
          </Tooltip>,
        ]}
      >
        <Card.Meta
          title={
            <div className="project-title">
              <span>{project.title}</span>
              {project.popularity && project.popularity > 80 && (
                <Tooltip title={t('projects.popular')}>
                  <FireOutlined className="popular-icon" />
                </Tooltip>
              )}
            </div>
          }
          description={
            <div className="project-description">
              <Paragraph ellipsis={{ rows: 2 }}>{project.description}</Paragraph>
              
              <div className="project-meta">
                <Tooltip title={t('tutorials.difficulty.title')}>
                  <Tag color={getDifficultyColor(project.difficulty)}>
                    {getDifficultyText(project.difficulty)}
                  </Tag>
                </Tooltip>
                
                <Tooltip title={t('projects.category')}>
                  <Tag>
                    {project.category}
                  </Tag>
                </Tooltip>
                
                {project.estimatedTime && (
                  <Tooltip title={t('tutorials.duration')}>
                    <Tag icon={<ClockCircleOutlined />}>
                      {project.estimatedTime} {t('tutorials.minutes')}
                    </Tag>
                  </Tooltip>
                )}
              </div>
              
              {project.tags && project.tags.length > 0 && (
                <div className="project-tags">
                  <TagOutlined />
                  {project.tags.slice(0, 3).map((tag, index) => (
                    <Tag key={index} className="project-tag">{tag}</Tag>
                  ))}
                  {project.tags.length > 3 && (
                    <Tooltip title={project.tags.slice(3).join(', ')}>
                      <Tag>+{project.tags.length - 3}</Tag>
                    </Tooltip>
                  )}
                </div>
              )}
            </div>
          }
        />
      </Card>
    );
  };

  /**
   * 渲染项目列表项（列表视图）
   */
  const renderProjectListItem = (project: ExampleProject) => {
    const isFavorited = ExampleProjectService.getInstance().isExampleFavorited(project.id);
    const isViewed = ExampleProjectService.getInstance().isExampleViewed(project.id);
    
    return (
      <div 
        className="project-list-item"
        onClick={() => handleProjectClick(project)}
      >
        <div className="project-list-thumbnail">
          {project.thumbnailUrl ? (
            <img alt={project.title} src={project.thumbnailUrl} />
          ) : (
            <div className="project-list-thumbnail-placeholder">
              <CodeOutlined />
            </div>
          )}
          {isViewed && (
            <div className="project-viewed-badge list-badge">
              <EyeOutlined />
            </div>
          )}
        </div>
        
        <div className="project-list-content">
          <div className="project-list-header">
            <Title level={5} className="project-list-title">
              {project.title}
              {project.popularity && project.popularity > 80 && (
                <Tooltip title={t('projects.popular')}>
                  <FireOutlined className="popular-icon" />
                </Tooltip>
              )}
            </Title>
            
            <div className="project-list-tags">
              <Tooltip title={t('tutorials.difficulty.title')}>
                <Tag color={getDifficultyColor(project.difficulty)}>
                  {getDifficultyText(project.difficulty)}
                </Tag>
              </Tooltip>
              
              <Tooltip title={t('projects.category')}>
                <Tag>
                  {project.category}
                </Tag>
              </Tooltip>
              
              {project.estimatedTime && (
                <Tooltip title={t('tutorials.duration')}>
                  <Tag icon={<ClockCircleOutlined />}>
                    {project.estimatedTime} {t('tutorials.minutes')}
                  </Tag>
                </Tooltip>
              )}
            </div>
          </div>
          
          <Paragraph ellipsis={{ rows: 2 }} className="project-list-description">
            {project.description}
          </Paragraph>
          
          {project.tags && project.tags.length > 0 && (
            <div className="project-list-meta-tags">
              <TagOutlined />
              {project.tags.slice(0, 5).map((tag, index) => (
                <Tag key={index} className="project-tag">{tag}</Tag>
              ))}
              {project.tags.length > 5 && (
                <Tooltip title={project.tags.slice(5).join(', ')}>
                  <Tag>+{project.tags.length - 5}</Tag>
                </Tooltip>
              )}
            </div>
          )}
        </div>
        
        <div className="project-list-actions">
          <Tooltip title={isFavorited ? t('projects.unfavorite') : t('projects.favorite')}>
            <Button 
              type="text" 
              icon={isFavorited ? <StarFilled className="favorited" /> : <StarOutlined />}
              onClick={(e) => handleFavoriteProject(e, project)}
            />
          </Tooltip>
          <Tooltip title={t('projects.view')}>
            <Button 
              type="text" 
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleProjectClick(project);
              }}
            />
          </Tooltip>
          <Tooltip title={t('projects.import')}>
            <Button 
              type="text" 
              icon={<DownloadOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleImportProject(project);
              }}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  /**
   * 渲染导入项目模态框
   */
  const renderImportModal = () => {
    if (!selectedProject) return null;
    
    return (
      <Modal
        title={t('projects.importTitle')}
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        onOk={confirmImportProject}
        confirmLoading={importLoading}
        okText={t('projects.import')}
        cancelText={t('common.cancel')}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('projects.projectName')}
            required
            help={t('projects.projectNameHelp')}
          >
            <Input
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              placeholder={t('projects.projectNamePlaceholder') || ''}
            />
          </Form.Item>
          
          <div className="import-project-info">
            <Title level={5}>{t('projects.importingProject')}:</Title>
            <div className="import-project-details">
              <div className="import-project-thumbnail">
                {selectedProject.thumbnailUrl ? (
                  <img alt={selectedProject.title} src={selectedProject.thumbnailUrl} />
                ) : (
                  <div className="import-project-thumbnail-placeholder">
                    <CodeOutlined />
                  </div>
                )}
              </div>
              <div className="import-project-meta">
                <Text strong>{selectedProject.title}</Text>
                <Text type="secondary">{selectedProject.description}</Text>
                <div className="import-project-tags">
                  <Tag color={getDifficultyColor(selectedProject.difficulty)}>
                    {getDifficultyText(selectedProject.difficulty)}
                  </Tag>
                  <Tag>{selectedProject.category}</Tag>
                </div>
              </div>
            </div>
          </div>
        </Form>
      </Modal>
    );
  };

  return (
    <div className="project-tutorial-panel">
      <div className="project-panel-header">
        <Title level={3}>{t('projects.title')}</Title>
        
        <div className="project-panel-toolbar">
          <div className="search-filter-group">
            <Input
              placeholder={t('projects.searchPlaceholder') || ''}
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
              className="search-input"
            />
            
            <Select
              placeholder={t('projects.categoryFilter')}
              value={selectedCategory}
              onChange={setSelectedCategory}
              allowClear
              className="category-select"
              suffixIcon={<FilterOutlined />}
            >
              {categories.map(category => (
                <Option key={category} value={category}>{category}</Option>
              ))}
            </Select>
            
            <Select
              placeholder={t('projects.difficultyFilter')}
              value={selectedDifficulty}
              onChange={setSelectedDifficulty}
              allowClear
              className="difficulty-select"
              suffixIcon={<FilterOutlined />}
            >
              <Option value="beginner">{t('tutorials.difficulty.beginner')}</Option>
              <Option value="intermediate">{t('tutorials.difficulty.intermediate')}</Option>
              <Option value="advanced">{t('tutorials.difficulty.advanced')}</Option>
              <Option value="expert">{t('tutorials.difficulty.expert')}</Option>
            </Select>
            
            <Select
              placeholder={t('projects.sortBy')}
              value={sortBy}
              onChange={setSortBy}
              className="sort-select"
              suffixIcon={<SortAscendingOutlined />}
            >
              <Option value="popularity">{t('projects.sortByPopularity')}</Option>
              <Option value="title">{t('projects.sortByTitle')}</Option>
              <Option value="difficulty">{t('projects.sortByDifficulty')}</Option>
              <Option value="rating">{t('projects.sortByRating')}</Option>
            </Select>
          </div>
          
          <div className="view-mode-group">
            <Tooltip title={t('projects.gridView')}>
              <Button
                type={viewMode === 'grid' ? 'primary' : 'default'}
                icon={<AppstoreOutlined />}
                onClick={() => setViewMode('grid')}
              />
            </Tooltip>
            <Tooltip title={t('projects.listView')}>
              <Button
                type={viewMode === 'list' ? 'primary' : 'default'}
                icon={<UnorderedListOutlined />}
                onClick={() => setViewMode('list')}
              />
            </Tooltip>
          </div>
        </div>
      </div>
      
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'all',
            label: t('projects.allProjects')
          },
          {
            key: 'favorites',
            label: (
              <Badge count={ExampleProjectService.getInstance().getFavoriteExampleProjects().length} style={{ backgroundColor: '#1890ff' }}>
                {t('projects.favorites')}
              </Badge>
            )
          },
          {
            key: 'recent',
            label: (
              <Badge count={ExampleProjectService.getInstance().getRecentlyViewedExampleProjects().length} style={{ backgroundColor: '#52c41a' }}>
                {t('projects.recent')}
              </Badge>
            )
          }
        ]}
      />
      
      <div className="project-panel-content">
        {loading ? (
          <div className="project-skeleton">
            <Skeleton active avatar paragraph={{ rows: 3 }} />
            <Skeleton active avatar paragraph={{ rows: 3 }} />
            <Skeleton active avatar paragraph={{ rows: 3 }} />
          </div>
        ) : filteredProjects.length > 0 ? (
          viewMode === 'grid' ? (
            <List
              grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }}
              dataSource={filteredProjects}
              renderItem={project => (
                <List.Item>
                  {renderProjectCard(project)}
                </List.Item>
              )}
            />
          ) : (
            <List
              dataSource={filteredProjects}
              renderItem={project => (
                <List.Item>
                  {renderProjectListItem(project)}
                </List.Item>
              )}
            />
          )
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t('projects.noProjectsFound')}
          />
        )}
      </div>
      
      {renderImportModal()}
    </div>
  );
};
