apiVersion: "agones.dev/v1"
kind: GameServer
metadata:
  name: "ir-game-server"
  namespace: default
  labels:
    app: ir-game-server
spec:
  ports:
  - name: default
    portPolicy: Dynamic
    containerPort: 3030
    protocol: TCP
  - name: microservice
    portPolicy: Dynamic
    containerPort: 3003
    protocol: TCP
  - name: webrtc-udp
    portPolicy: Dynamic
    containerPort: 10000
    protocol: UDP
  - name: webrtc-tcp
    portPolicy: Dynamic
    containerPort: 10000
    protocol: TCP
  template:
    spec:
      containers:
      - name: ir-game-server
        image: ir-game-server:latest
        imagePullPolicy: Always
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        env:
        - name: AGONES_ENABLED
          value: "true"
        - name: KUBERNETES_ENABLED
          value: "true"
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: MEDIASOUP_ANNOUNCED_IP
          valueFrom:
            fieldRef:
              fieldPath: status.address
        ports:
        - name: default
          containerPort: 3030
        - name: microservice
          containerPort: 3003
        - name: webrtc-udp
          containerPort: 10000
          protocol: UDP
        - name: webrtc-tcp
          containerPort: 10000
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /api/health
            port: default
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: default
          initialDelaySeconds: 15
          periodSeconds: 5
