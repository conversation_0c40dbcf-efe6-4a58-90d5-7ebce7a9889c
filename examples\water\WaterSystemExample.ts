/**
 * 水体系统示例
 * 展示如何使用水体系统创建各种水体效果
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { WaterBodyComponent } from '../../engine/src/physics/water/WaterBodyComponent';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../engine/src/physics/water/WaterInteractionSystem';
import { UnderwaterParticleSystem } from '../../engine/src/rendering/water/UnderwaterParticleSystem';
import { WaterLightingSystem } from '../../engine/src/rendering/water/WaterLightingSystem';
import { WaterPresets, WaterPresetType } from '../../engine/src/physics/water/WaterPresets';
import { PhysicsSystem } from '../../engine/src/physics/PhysicsSystem';
import { RenderingSystem } from '../../engine/src/rendering/RenderingSystem';
import { CameraSystem } from '../../engine/src/rendering/CameraSystem';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { TransformComponent } from '../../engine/src/core/TransformComponent';
import { MeshComponent } from '../../engine/src/rendering/MeshComponent';
import { PhysicsBodyComponent } from '../../engine/src/physics/PhysicsBodyComponent';

/**
 * 水体系统示例类
 */
export class WaterSystemExample {
  /** 世界 */
  private world: World;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水体交互系统 */
  private waterInteractionSystem: WaterInteractionSystem;
  /** 水下粒子系统 */
  private underwaterParticleSystem: UnderwaterParticleSystem;
  /** 水体光照系统 */
  private waterLightingSystem: WaterLightingSystem;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 渲染系统 */
  private renderingSystem: RenderingSystem;
  /** 相机系统 */
  private cameraSystem: CameraSystem;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 水体实体 */
  private waterEntities: Entity[] = [];
  /** 物理实体 */
  private physicsEntities: Entity[] = [];
  /** 当前示例索引 */
  private currentExampleIndex: number = 0;
  /** 示例名称 */
  private exampleNames: string[] = [
    '湖泊',
    '河流',
    '海洋',
    '游泳池',
    '温泉',
    '地下湖泊',
    '地下河流',
    '瀑布',
    '浅滩',
    '沼泽',
    '冰湖',
    '熔岩'
  ];

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 创建系统
    this.createSystems();

    // 创建场景
    this.createScene();

    // 创建UI
    this.createUI();

    // 注册事件
    this.registerEvents();
  }

  /**
   * 创建系统
   */
  private createSystems(): void {
    // 创建渲染系统
    this.renderingSystem = new RenderingSystem(this.world);
    this.world.addSystem(this.renderingSystem);

    // 创建相机系统
    this.cameraSystem = new CameraSystem(this.world);
    this.world.addSystem(this.cameraSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world);
    this.world.addSystem(this.inputSystem);

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem(this.world);
    this.world.addSystem(this.physicsSystem);

    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水体交互系统
    this.waterInteractionSystem = new WaterInteractionSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true
    });
    this.world.addSystem(this.waterInteractionSystem);

    // 创建水下粒子系统
    this.underwaterParticleSystem = new UnderwaterParticleSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      maxParticles: 1000
    });
    this.world.addSystem(this.underwaterParticleSystem);

    // 创建水体光照系统
    this.waterLightingSystem = new WaterLightingSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableVolumetricLight: true,
      enableUnderwaterFog: true,
      reflectionMapResolution: 512,
      refractionMapResolution: 512,
      causticsMapResolution: 512,
      volumetricLightMapResolution: 256
    });
    this.world.addSystem(this.waterLightingSystem);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建地面
    this.createGround();

    // 创建光源
    this.createLights();

    // 创建相机
    this.createCamera();

    // 创建水体
    this.createWaterExample(this.currentExampleIndex);

    // 创建物理测试对象
    this.createPhysicsObjects();
  }

  /**
   * 创建地面
   */
  private createGround(): void {
    // 创建地面实体
    const groundEntity = new Entity();
    groundEntity.name = 'Ground';

    // 添加变换组件
    const transformComponent = new TransformComponent();
    transformComponent.setPosition(new THREE.Vector3(0, -10, 0));
    transformComponent.setRotation(new THREE.Euler(0, 0, 0));
    transformComponent.setScale(new THREE.Vector3(100, 1, 100));
    groundEntity.addComponent(transformComponent);

    // 添加网格组件
    const meshComponent = new MeshComponent();
    meshComponent.setGeometry(new THREE.BoxGeometry(1, 1, 1));
    meshComponent.setMaterial(new THREE.MeshStandardMaterial({ color: 0x888888 }));
    groundEntity.addComponent(meshComponent);

    // 添加物理组件
    const physicsComponent = new PhysicsBodyComponent();
    physicsComponent.setMass(0); // 静态物体
    physicsComponent.setShape('box');
    physicsComponent.setSize(new THREE.Vector3(100, 1, 100));
    groundEntity.addComponent(physicsComponent);

    // 添加到世界
    this.world.addEntity(groundEntity);
  }

  /**
   * 创建光源
   */
  private createLights(): void {
    // 创建环境光
    const ambientLightEntity = new Entity();
    ambientLightEntity.name = 'AmbientLight';
    const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
    this.renderingSystem.addLight(ambientLightEntity.id, ambientLight);
    this.world.addEntity(ambientLightEntity);

    // 创建方向光
    const directionalLightEntity = new Entity();
    directionalLightEntity.name = 'DirectionalLight';
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(10, 20, 10);
    directionalLight.castShadow = true;
    this.renderingSystem.addLight(directionalLightEntity.id, directionalLight);
    this.world.addEntity(directionalLightEntity);
  }

  /**
   * 创建相机
   */
  private createCamera(): void {
    // 创建相机实体
    const cameraEntity = new Entity();
    cameraEntity.name = 'Camera';

    // 添加变换组件
    const transformComponent = new TransformComponent();
    transformComponent.setPosition(new THREE.Vector3(0, 10, 30));
    transformComponent.setRotation(new THREE.Euler(-0.3, 0, 0));
    cameraEntity.addComponent(transformComponent);

    // 创建相机
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.cameraSystem.setActiveCamera(cameraEntity.id, camera);

    // 添加到世界
    this.world.addEntity(cameraEntity);
  }

  /**
   * 创建水体示例
   * @param index 示例索引
   */
  private createWaterExample(index: number): void {
    // 清除现有水体
    this.clearWaterEntities();

    // 创建水体
    let waterBody: WaterBodyComponent;

    switch (index) {
      case 0: // 湖泊
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.LAKE,
          size: { width: 50, height: 5, depth: 50 },
          position: new THREE.Vector3(0, 0, 0)
        });
        break;
      case 1: // 河流
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.RIVER,
          size: { width: 10, height: 3, depth: 50 },
          position: new THREE.Vector3(0, 0, 0),
          rotation: new THREE.Euler(0, Math.PI / 4, 0)
        });
        break;
      case 2: // 海洋
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.OCEAN,
          size: { width: 100, height: 10, depth: 100 },
          position: new THREE.Vector3(0, 0, 0)
        });
        break;
      case 3: // 游泳池
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.POOL,
          size: { width: 20, height: 3, depth: 10 },
          position: new THREE.Vector3(0, 0, 0)
        });
        break;
      case 4: // 温泉
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.HOT_SPRING,
          size: { width: 10, height: 2, depth: 10 },
          position: new THREE.Vector3(0, 0, 0)
        });
        break;
      case 5: // 地下湖泊
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.UNDERGROUND_LAKE,
          size: { width: 40, height: 4, depth: 40 },
          position: new THREE.Vector3(0, -5, 0)
        });
        break;
      case 6: // 地下河流
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.UNDERGROUND_RIVER,
          size: { width: 8, height: 2, depth: 40 },
          position: new THREE.Vector3(0, -5, 0),
          rotation: new THREE.Euler(0, Math.PI / 4, 0)
        });
        break;
      case 7: // 瀑布
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.WATERFALL,
          size: { width: 10, height: 15, depth: 3 },
          position: new THREE.Vector3(0, 5, -10),
          rotation: new THREE.Euler(-Math.PI / 4, 0, 0)
        });
        break;
      case 8: // 浅滩
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.SHALLOW,
          size: { width: 30, height: 1, depth: 30 },
          position: new THREE.Vector3(0, -1, 0)
        });
        break;
      case 9: // 沼泽
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.SWAMP,
          size: { width: 30, height: 2, depth: 30 },
          position: new THREE.Vector3(0, -1, 0)
        });
        break;
      case 10: // 冰湖
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.ICE_LAKE,
          size: { width: 40, height: 3, depth: 40 },
          position: new THREE.Vector3(0, 0, 0)
        });
        break;
      case 11: // 熔岩
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.LAVA,
          size: { width: 30, height: 3, depth: 30 },
          position: new THREE.Vector3(0, 0, 0)
        });
        break;
      default:
        waterBody = WaterPresets.createPreset(this.world, {
          type: WaterPresetType.LAKE,
          size: { width: 50, height: 5, depth: 50 },
          position: new THREE.Vector3(0, 0, 0)
        });
        break;
    }

    // 保存水体实体
    const entity = this.world.getEntity(waterBody.getEntityId());
    if (entity) {
      this.waterEntities.push(entity);
    }
  }

  /**
   * 创建物理测试对象
   */
  private createPhysicsObjects(): void {
    // 创建几个不同形状的物理对象
    this.createPhysicsBox(new THREE.Vector3(-5, 10, 0), new THREE.Vector3(1, 1, 1), 1);
    this.createPhysicsSphere(new THREE.Vector3(0, 10, 0), 0.5, 1);
    this.createPhysicsCylinder(new THREE.Vector3(5, 10, 0), 0.5, 1, 0.5, 1);
  }

  /**
   * 创建物理盒子
   * @param position 位置
   * @param size 尺寸
   * @param mass 质量
   */
  private createPhysicsBox(position: THREE.Vector3, size: THREE.Vector3, mass: number): void {
    // 创建实体
    const entity = new Entity();
    entity.name = 'PhysicsBox';

    // 添加变换组件
    const transformComponent = new TransformComponent();
    transformComponent.setPosition(position);
    transformComponent.setRotation(new THREE.Euler(0, 0, 0));
    transformComponent.setScale(size);
    entity.addComponent(transformComponent);

    // 添加网格组件
    const meshComponent = new MeshComponent();
    meshComponent.setGeometry(new THREE.BoxGeometry(1, 1, 1));
    meshComponent.setMaterial(new THREE.MeshStandardMaterial({ color: 0xff0000 }));
    entity.addComponent(meshComponent);

    // 添加物理组件
    const physicsComponent = new PhysicsBodyComponent();
    physicsComponent.setMass(mass);
    physicsComponent.setShape('box');
    physicsComponent.setSize(size);
    entity.addComponent(physicsComponent);

    // 添加到世界
    this.world.addEntity(entity);
    this.physicsEntities.push(entity);
  }

  /**
   * 创建物理球体
   * @param position 位置
   * @param radius 半径
   * @param mass 质量
   */
  private createPhysicsSphere(position: THREE.Vector3, radius: number, mass: number): void {
    // 创建实体
    const entity = new Entity();
    entity.name = 'PhysicsSphere';

    // 添加变换组件
    const transformComponent = new TransformComponent();
    transformComponent.setPosition(position);
    transformComponent.setRotation(new THREE.Euler(0, 0, 0));
    transformComponent.setScale(new THREE.Vector3(radius * 2, radius * 2, radius * 2));
    entity.addComponent(transformComponent);

    // 添加网格组件
    const meshComponent = new MeshComponent();
    meshComponent.setGeometry(new THREE.SphereGeometry(0.5, 32, 32));
    meshComponent.setMaterial(new THREE.MeshStandardMaterial({ color: 0x00ff00 }));
    entity.addComponent(meshComponent);

    // 添加物理组件
    const physicsComponent = new PhysicsBodyComponent();
    physicsComponent.setMass(mass);
    physicsComponent.setShape('sphere');
    physicsComponent.setRadius(radius);
    entity.addComponent(physicsComponent);

    // 添加到世界
    this.world.addEntity(entity);
    this.physicsEntities.push(entity);
  }

  /**
   * 创建物理圆柱体
   * @param position 位置
   * @param radius 半径
   * @param height 高度
   * @param radiusTop 顶部半径
   * @param mass 质量
   */
  private createPhysicsCylinder(position: THREE.Vector3, radius: number, height: number, radiusTop: number, mass: number): void {
    // 创建实体
    const entity = new Entity();
    entity.name = 'PhysicsCylinder';

    // 添加变换组件
    const transformComponent = new TransformComponent();
    transformComponent.setPosition(position);
    transformComponent.setRotation(new THREE.Euler(0, 0, 0));
    transformComponent.setScale(new THREE.Vector3(1, 1, 1));
    entity.addComponent(transformComponent);

    // 添加网格组件
    const meshComponent = new MeshComponent();
    meshComponent.setGeometry(new THREE.CylinderGeometry(radiusTop, radius, height, 32));
    meshComponent.setMaterial(new THREE.MeshStandardMaterial({ color: 0x0000ff }));
    entity.addComponent(meshComponent);

    // 添加物理组件
    const physicsComponent = new PhysicsBodyComponent();
    physicsComponent.setMass(mass);
    physicsComponent.setShape('cylinder');
    physicsComponent.setRadius(radius);
    physicsComponent.setHeight(height);
    entity.addComponent(physicsComponent);

    // 添加到世界
    this.world.addEntity(entity);
    this.physicsEntities.push(entity);
  }

  /**
   * 清除水体实体
   */
  private clearWaterEntities(): void {
    // 移除所有水体实体
    for (const entity of this.waterEntities) {
      this.world.removeEntity(entity.id);
    }
    this.waterEntities = [];
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    uiContainer.style.color = 'white';
    uiContainer.style.fontFamily = 'Arial, sans-serif';
    uiContainer.style.borderRadius = '5px';
    document.body.appendChild(uiContainer);

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '水体系统示例';
    title.style.margin = '0 0 10px 0';
    uiContainer.appendChild(title);

    // 创建当前示例显示
    const exampleDisplay = document.createElement('div');
    exampleDisplay.id = 'example-display';
    exampleDisplay.textContent = `当前示例：${this.exampleNames[this.currentExampleIndex]}`;
    exampleDisplay.style.marginBottom = '10px';
    uiContainer.appendChild(exampleDisplay);

    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.gap = '10px';
    uiContainer.appendChild(buttonContainer);

    // 创建上一个示例按钮
    const prevButton = document.createElement('button');
    prevButton.textContent = '上一个';
    prevButton.style.padding = '5px 10px';
    prevButton.style.cursor = 'pointer';
    prevButton.addEventListener('click', () => this.showPreviousExample());
    buttonContainer.appendChild(prevButton);

    // 创建下一个示例按钮
    const nextButton = document.createElement('button');
    nextButton.textContent = '下一个';
    nextButton.style.padding = '5px 10px';
    nextButton.style.cursor = 'pointer';
    nextButton.addEventListener('click', () => this.showNextExample());
    buttonContainer.appendChild(nextButton);

    // 创建重置物理对象按钮
    const resetButton = document.createElement('button');
    resetButton.textContent = '重置物理对象';
    resetButton.style.padding = '5px 10px';
    resetButton.style.cursor = 'pointer';
    resetButton.addEventListener('click', () => this.resetPhysicsObjects());
    buttonContainer.appendChild(resetButton);

    // 创建添加物理对象按钮
    const addButton = document.createElement('button');
    addButton.textContent = '添加物理对象';
    addButton.style.padding = '5px 10px';
    addButton.style.cursor = 'pointer';
    addButton.addEventListener('click', () => this.addRandomPhysicsObject());
    buttonContainer.appendChild(addButton);
  }

  /**
   * 注册事件
   */
  private registerEvents(): void {
    // 注册窗口大小变化事件
    window.addEventListener('resize', () => this.onResize());

    // 注册键盘事件
    window.addEventListener('keydown', (event) => this.onKeyDown(event));
  }

  /**
   * 窗口大小变化事件处理
   */
  private onResize(): void {
    // 更新相机宽高比
    const camera = this.cameraSystem.getActiveCamera();
    if (camera) {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
    }

    // 更新渲染器大小
    this.renderingSystem.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 键盘事件处理
   * @param event 键盘事件
   */
  private onKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'ArrowLeft':
        this.showPreviousExample();
        break;
      case 'ArrowRight':
        this.showNextExample();
        break;
      case 'r':
        this.resetPhysicsObjects();
        break;
      case 'a':
        this.addRandomPhysicsObject();
        break;
    }
  }

  /**
   * 显示上一个示例
   */
  private showPreviousExample(): void {
    this.currentExampleIndex = (this.currentExampleIndex - 1 + this.exampleNames.length) % this.exampleNames.length;
    this.createWaterExample(this.currentExampleIndex);
    this.updateExampleDisplay();
  }

  /**
   * 显示下一个示例
   */
  private showNextExample(): void {
    this.currentExampleIndex = (this.currentExampleIndex + 1) % this.exampleNames.length;
    this.createWaterExample(this.currentExampleIndex);
    this.updateExampleDisplay();
  }

  /**
   * 更新示例显示
   */
  private updateExampleDisplay(): void {
    const exampleDisplay = document.getElementById('example-display');
    if (exampleDisplay) {
      exampleDisplay.textContent = `当前示例：${this.exampleNames[this.currentExampleIndex]}`;
    }
  }

  /**
   * 重置物理对象
   */
  private resetPhysicsObjects(): void {
    // 移除所有物理实体
    for (const entity of this.physicsEntities) {
      this.world.removeEntity(entity.id);
    }
    this.physicsEntities = [];

    // 重新创建物理对象
    this.createPhysicsObjects();
  }

  /**
   * 添加随机物理对象
   */
  private addRandomPhysicsObject(): void {
    // 随机位置
    const x = Math.random() * 20 - 10;
    const y = 10 + Math.random() * 5;
    const z = Math.random() * 20 - 10;
    const position = new THREE.Vector3(x, y, z);

    // 随机类型
    const type = Math.floor(Math.random() * 3);
    switch (type) {
      case 0:
        // 随机盒子
        const boxSize = new THREE.Vector3(
          0.5 + Math.random() * 1,
          0.5 + Math.random() * 1,
          0.5 + Math.random() * 1
        );
        this.createPhysicsBox(position, boxSize, 1);
        break;
      case 1:
        // 随机球体
        const radius = 0.3 + Math.random() * 0.7;
        this.createPhysicsSphere(position, radius, 1);
        break;
      case 2:
        // 随机圆柱体
        const cylinderRadius = 0.3 + Math.random() * 0.7;
        const height = 0.5 + Math.random() * 1.5;
        const radiusTop = cylinderRadius * (0.5 + Math.random() * 0.5);
        this.createPhysicsCylinder(position, cylinderRadius, height, radiusTop, 1);
        break;
    }
  }

  /**
   * 启动示例
   */
  public start(): void {
    // 启动世界
    this.world.start();

    // 开始渲染循环
    this.renderingSystem.startRenderLoop();
  }

  /**
   * 停止示例
   */
  public stop(): void {
    // 停止世界
    this.world.stop();

    // 停止渲染循环
    this.renderingSystem.stopRenderLoop();
  }
}
