/**
 * 网络实体
 * 定义网络实体的结构
 */

/**
 * 网络实体类型
 */
export enum NetworkEntityType {
  /** 静态实体 */
  STATIC = 'static',
  /** 动态实体 */
  DYNAMIC = 'dynamic',
  /** 玩家实体 */
  PLAYER = 'player',
  /** NPC实体 */
  NPC = 'npc',
  /** 物品实体 */
  ITEM = 'item',
  /** 触发器实体 */
  TRIGGER = 'trigger',
  /** 相机实体 */
  CAMERA = 'camera',
  /** 光源实体 */
  LIGHT = 'light',
  /** 音源实体 */
  AUDIO = 'audio',
  /** 粒子实体 */
  PARTICLE = 'particle',
  /** UI实体 */
  UI = 'ui',
  /** 自定义实体 */
  CUSTOM = 'custom',
}

/**
 * 网络实体同步模式
 */
export enum NetworkEntitySyncMode {
  /** 不同步 */
  NONE = 'none',
  /** 变换同步 */
  TRANSFORM = 'transform',
  /** 物理同步 */
  PHYSICS = 'physics',
  /** 完全同步 */
  FULL = 'full',
  /** 自定义同步 */
  CUSTOM = 'custom',
}

/**
 * 网络实体所有权模式
 */
export enum NetworkEntityOwnershipMode {
  /** 固定所有权 */
  FIXED = 'fixed',
  /** 请求所有权 */
  REQUEST = 'request',
  /** 抢占所有权 */
  TAKEOVER = 'takeover',
  /** 共享所有权 */
  SHARED = 'shared',
}

/**
 * 网络实体
 */
export interface NetworkEntity {
  /** 实体ID */
  entityId: string;
  
  /** 实体类型 */
  type?: NetworkEntityType;
  
  /** 实体名称 */
  name?: string;
  
  /** 实体标签 */
  tags?: string[];
  
  /** 实体层级 */
  layer?: number;
  
  /** 实体所有者ID */
  ownerId: string;
  
  /** 实体同步模式 */
  syncMode?: NetworkEntitySyncMode;
  
  /** 实体所有权模式 */
  ownershipMode?: NetworkEntityOwnershipMode;
  
  /** 实体数据 */
  data: any;
  
  /** 实体创建时间 */
  createTime: number;
  
  /** 实体更新时间 */
  updateTime: number;
  
  /** 实体父级ID */
  parentId?: string;
  
  /** 实体子级ID列表 */
  childIds?: string[];
  
  /** 实体位置 */
  position?: {
    x: number;
    y: number;
    z: number;
  };
  
  /** 实体旋转 */
  rotation?: {
    x: number;
    y: number;
    z: number;
    w: number;
  };
  
  /** 实体缩放 */
  scale?: {
    x: number;
    y: number;
    z: number;
  };
  
  /** 实体速度 */
  velocity?: {
    x: number;
    y: number;
    z: number;
  };
  
  /** 实体加速度 */
  acceleration?: {
    x: number;
    y: number;
    z: number;
  };
  
  /** 实体角速度 */
  angularVelocity?: {
    x: number;
    y: number;
    z: number;
  };
  
  /** 实体是否可见 */
  visible?: boolean;
  
  /** 实体是否启用 */
  enabled?: boolean;
  
  /** 实体是否静态 */
  isStatic?: boolean;
  
  /** 实体是否触发器 */
  isTrigger?: boolean;
  
  /** 实体是否物理实体 */
  isPhysics?: boolean;
  
  /** 实体碰撞器数据 */
  collider?: {
    type: string;
    size?: {
      x: number;
      y: number;
      z: number;
    };
    radius?: number;
    height?: number;
    center?: {
      x: number;
      y: number;
      z: number;
    };
  };
  
  /** 实体渲染器数据 */
  renderer?: {
    type: string;
    material?: string;
    mesh?: string;
    color?: {
      r: number;
      g: number;
      b: number;
      a: number;
    };
    castShadow?: boolean;
    receiveShadow?: boolean;
  };
  
  /** 实体动画数据 */
  animation?: {
    clip?: string;
    speed?: number;
    loop?: boolean;
    playing?: boolean;
    time?: number;
  };
  
  /** 实体音频数据 */
  audio?: {
    clip?: string;
    volume?: number;
    pitch?: number;
    loop?: boolean;
    playing?: boolean;
    spatial?: boolean;
    minDistance?: number;
    maxDistance?: number;
  };
  
  /** 实体脚本数据 */
  script?: {
    [key: string]: any;
  };
  
  /** 实体自定义数据 */
  customData?: {
    [key: string]: any;
  };
  
  /** 实体元数据 */
  metadata?: {
    [key: string]: any;
  };
}
