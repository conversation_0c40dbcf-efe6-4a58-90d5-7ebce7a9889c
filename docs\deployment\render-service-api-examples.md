# DL引擎渲染服务API使用示例

## 概述

本文档提供DL引擎渲染服务的详细API使用示例，包括各种渲染任务的创建、管理和结果获取方法。

## 1. 认证和基础配置

### 1.1 获取访问令牌
```javascript
// 用户登录获取JWT令牌
const loginResponse = await fetch('http://api-gateway:3000/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const { access_token } = await loginResponse.json();

// 设置默认请求头
const defaultHeaders = {
  'Authorization': `Bearer ${access_token}`,
  'Content-Type': 'application/json'
};
```

### 1.2 API基础配置
```javascript
const API_BASE_URL = 'http://api-gateway:3000/api';
const RENDER_API_URL = `${API_BASE_URL}/render`;

// 通用请求函数
async function apiRequest(url, options = {}) {
  const response = await fetch(url, {
    headers: defaultHeaders,
    ...options
  });
  
  if (!response.ok) {
    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
}
```

## 2. 图像渲染示例

### 2.1 创建静态图像渲染任务
```javascript
// 高质量静态图像渲染
async function createImageRenderJob() {
  const renderJobData = {
    name: '产品展示图渲染',
    description: '高质量产品3D展示图',
    type: 'image',
    projectId: 'project-uuid-123',
    sceneId: 'scene-uuid-456',
    settings: {
      width: 3840,        // 4K分辨率
      height: 2160,
      quality: 95,        // 高质量
      format: 'png',      // PNG格式保持透明度
      camera: 'main-camera',
      lighting: 'studio', // 工作室光照
      postProcessing: true // 启用后期处理
    }
  };

  try {
    const job = await apiRequest(`${RENDER_API_URL}/jobs`, {
      method: 'POST',
      body: JSON.stringify(renderJobData)
    });
    
    console.log('渲染任务创建成功:', job);
    return job;
  } catch (error) {
    console.error('创建渲染任务失败:', error);
    throw error;
  }
}

// 使用示例
const imageJob = await createImageRenderJob();
console.log('任务ID:', imageJob.id);
```

### 2.2 批量图像渲染
```javascript
// 批量创建多角度产品图
async function createMultiAngleRender(projectId, sceneId, angles) {
  const jobs = [];
  
  for (let i = 0; i < angles.length; i++) {
    const angle = angles[i];
    const jobData = {
      name: `产品图-角度${i + 1}`,
      description: `${angle.name}角度渲染`,
      type: 'image',
      projectId,
      sceneId,
      settings: {
        width: 1920,
        height: 1080,
        quality: 85,
        format: 'jpg',
        camera: angle.cameraId,
        lighting: 'natural',
        postProcessing: true
      }
    };
    
    try {
      const job = await apiRequest(`${RENDER_API_URL}/jobs`, {
        method: 'POST',
        body: JSON.stringify(jobData)
      });
      jobs.push(job);
      
      // 避免过快请求
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.error(`角度${i + 1}渲染任务创建失败:`, error);
    }
  }
  
  return jobs;
}

// 使用示例
const angles = [
  { name: '正面', cameraId: 'front-camera' },
  { name: '侧面', cameraId: 'side-camera' },
  { name: '背面', cameraId: 'back-camera' },
  { name: '俯视', cameraId: 'top-camera' }
];

const multiAngleJobs = await createMultiAngleRender(
  'project-uuid-123',
  'scene-uuid-456',
  angles
);
```

## 3. 视频渲染示例

### 3.1 创建产品展示视频
```javascript
// 360度旋转展示视频
async function createRotationVideo() {
  const videoJobData = {
    name: '360度产品展示视频',
    description: '产品360度旋转展示',
    type: 'video',
    projectId: 'project-uuid-123',
    sceneId: 'scene-uuid-456',
    settings: {
      width: 1920,
      height: 1080,
      quality: 80,
      format: 'mp4',
      frames: 300,        // 10秒视频 (30fps)
      fps: 30,
      camera: 'orbit-camera',
      lighting: 'studio',
      postProcessing: true,
      // 自定义动画设置
      animation: {
        type: 'rotation',
        axis: 'y',
        duration: 10,     // 10秒
        easing: 'linear'
      }
    }
  };

  try {
    const job = await apiRequest(`${RENDER_API_URL}/jobs`, {
      method: 'POST',
      body: JSON.stringify(videoJobData)
    });
    
    console.log('视频渲染任务创建成功:', job);
    return job;
  } catch (error) {
    console.error('创建视频渲染任务失败:', error);
    throw error;
  }
}
```

### 3.2 创建高质量宣传视频
```javascript
// 高质量宣传视频渲染
async function createPromoVideo() {
  const promoJobData = {
    name: '产品宣传视频',
    description: '4K高质量产品宣传视频',
    type: 'video',
    projectId: 'project-uuid-123',
    sceneId: 'promo-scene-uuid',
    settings: {
      width: 3840,        // 4K分辨率
      height: 2160,
      quality: 95,        // 高质量
      format: 'mp4',
      frames: 900,        // 30秒视频 (30fps)
      fps: 30,
      camera: 'cinematic-camera',
      lighting: 'cinematic',
      postProcessing: true,
      // 高级渲染设置
      advanced: {
        motionBlur: true,
        depthOfField: true,
        antiAliasing: 'FXAA',
        shadowQuality: 'high',
        reflectionQuality: 'high'
      }
    }
  };

  return await apiRequest(`${RENDER_API_URL}/jobs`, {
    method: 'POST',
    body: JSON.stringify(promoJobData)
  });
}
```

## 4. 动画渲染示例

### 4.1 创建产品组装动画
```javascript
// 产品组装过程动画
async function createAssemblyAnimation() {
  const animationJobData = {
    name: '产品组装动画',
    description: '展示产品组装过程的动画',
    type: 'animation',
    projectId: 'project-uuid-123',
    sceneId: 'assembly-scene-uuid',
    settings: {
      width: 1920,
      height: 1080,
      quality: 85,
      format: 'gif',      // GIF格式便于网页展示
      frames: 150,        // 5秒动画 (30fps)
      fps: 30,
      camera: 'assembly-camera',
      lighting: 'technical',
      postProcessing: true,
      // 动画特定设置
      animation: {
        type: 'sequence',
        steps: [
          { action: 'show_part_1', duration: 1 },
          { action: 'show_part_2', duration: 1 },
          { action: 'assemble_parts', duration: 2 },
          { action: 'final_rotation', duration: 1 }
        ]
      }
    }
  };

  return await apiRequest(`${RENDER_API_URL}/jobs`, {
    method: 'POST',
    body: JSON.stringify(animationJobData)
  });
}
```

## 5. 任务管理示例

### 5.1 查询渲染任务状态
```javascript
// 查询所有渲染任务
async function getAllRenderJobs(filters = {}) {
  const params = new URLSearchParams();
  
  if (filters.status) params.append('status', filters.status);
  if (filters.type) params.append('type', filters.type);
  
  const url = `${RENDER_API_URL}/jobs?${params.toString()}`;
  
  try {
    const jobs = await apiRequest(url);
    return jobs;
  } catch (error) {
    console.error('获取渲染任务失败:', error);
    throw error;
  }
}

// 查询特定任务详情
async function getRenderJobDetails(jobId) {
  try {
    const job = await apiRequest(`${RENDER_API_URL}/jobs/${jobId}`);
    return job;
  } catch (error) {
    console.error('获取任务详情失败:', error);
    throw error;
  }
}

// 使用示例
const allJobs = await getAllRenderJobs();
const processingJobs = await getAllRenderJobs({ status: 'processing' });
const imageJobs = await getAllRenderJobs({ type: 'image' });
```

### 5.2 任务进度监控
```javascript
// 监控任务进度
async function monitorJobProgress(jobId, onProgress, onComplete) {
  const checkInterval = 2000; // 2秒检查一次
  
  const checkProgress = async () => {
    try {
      const job = await getRenderJobDetails(jobId);
      
      // 调用进度回调
      if (onProgress) {
        onProgress(job.progress, job.status, job);
      }
      
      // 检查是否完成
      if (job.status === 'completed') {
        if (onComplete) {
          onComplete(job);
        }
        return;
      }
      
      // 检查是否失败
      if (job.status === 'failed') {
        throw new Error(`渲染任务失败: ${job.errorMessage}`);
      }
      
      // 继续监控
      if (job.status === 'processing' || job.status === 'pending') {
        setTimeout(checkProgress, checkInterval);
      }
      
    } catch (error) {
      console.error('监控任务进度失败:', error);
      throw error;
    }
  };
  
  // 开始监控
  checkProgress();
}

// 使用示例
const job = await createImageRenderJob();

monitorJobProgress(
  job.id,
  (progress, status) => {
    console.log(`任务进度: ${progress}% (${status})`);
  },
  (completedJob) => {
    console.log('任务完成:', completedJob);
    console.log('渲染结果:', completedJob.results);
  }
);
```

### 5.3 取消和删除任务
```javascript
// 取消正在进行的任务
async function cancelRenderJob(jobId) {
  try {
    const result = await apiRequest(`${RENDER_API_URL}/jobs/${jobId}/cancel`, {
      method: 'POST'
    });
    console.log('任务取消成功:', result);
    return result;
  } catch (error) {
    console.error('取消任务失败:', error);
    throw error;
  }
}

// 删除已完成的任务
async function deleteRenderJob(jobId) {
  try {
    await apiRequest(`${RENDER_API_URL}/jobs/${jobId}`, {
      method: 'DELETE'
    });
    console.log('任务删除成功');
  } catch (error) {
    console.error('删除任务失败:', error);
    throw error;
  }
}
```

## 6. 结果处理示例

### 6.1 获取渲染结果
```javascript
// 获取渲染结果详情
async function getRenderResult(resultId) {
  try {
    const result = await apiRequest(`${RENDER_API_URL}/results/${resultId}`);
    return result;
  } catch (error) {
    console.error('获取渲染结果失败:', error);
    throw error;
  }
}

// 下载渲染文件
async function downloadRenderFile(fileUrl, filename) {
  try {
    const response = await fetch(fileUrl, {
      headers: { 'Authorization': defaultHeaders.Authorization }
    });
    
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status}`);
    }
    
    const blob = await response.blob();
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    console.log('文件下载完成:', filename);
  } catch (error) {
    console.error('下载文件失败:', error);
    throw error;
  }
}

// 使用示例
const job = await getRenderJobDetails('job-uuid-123');
if (job.status === 'completed' && job.results.length > 0) {
  const result = job.results[0];
  await downloadRenderFile(result.fileUrl, `render_${job.id}.${result.format}`);
}
```

### 6.2 批量处理结果
```javascript
// 批量下载渲染结果
async function batchDownloadResults(jobIds) {
  for (const jobId of jobIds) {
    try {
      const job = await getRenderJobDetails(jobId);
      
      if (job.status === 'completed') {
        for (const result of job.results) {
          const filename = `${job.name}_${result.id}.${result.format}`;
          await downloadRenderFile(result.fileUrl, filename);
          
          // 避免过快下载
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    } catch (error) {
      console.error(`处理任务${jobId}失败:`, error);
    }
  }
}
```

## 7. 错误处理和重试

### 7.1 通用错误处理
```javascript
// 带重试的API请求
async function apiRequestWithRetry(url, options = {}, maxRetries = 3) {
  let lastError;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiRequest(url, options);
    } catch (error) {
      lastError = error;
      
      // 如果是认证错误，不重试
      if (error.message.includes('401')) {
        throw error;
      }
      
      // 等待后重试
      if (i < maxRetries - 1) {
        const delay = Math.pow(2, i) * 1000; // 指数退避
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError;
}

// 错误分类处理
function handleRenderError(error, jobData) {
  if (error.message.includes('权限')) {
    console.error('权限错误，请检查项目访问权限');
    // 可以尝试重新获取令牌
  } else if (error.message.includes('场景')) {
    console.error('场景数据错误，请检查场景ID和数据完整性');
  } else if (error.message.includes('资源')) {
    console.error('资源不足，请稍后重试或降低渲染质量');
  } else {
    console.error('未知错误:', error);
  }
}
```

---

*本文档提供了DL引擎渲染服务的完整API使用示例，涵盖了各种渲染场景和最佳实践。*
