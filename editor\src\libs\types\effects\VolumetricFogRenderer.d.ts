/**
 * 体积雾效果渲染器
 * 用于在地下环境中创建体积雾效果
 */
import * as THREE from 'three';
/**
 * 体积雾参数
 */
export interface VolumetricFogParams {
    /** 雾密度 */
    density: number;
    /** 雾颜色 */
    color: THREE.Color;
    /** 雾高度 */
    height: number;
    /** 雾范围 */
    range: number;
    /** 雾散射系数 */
    scattering: number;
    /** 雾吸收系数 */
    absorption: number;
    /** 雾相位函数参数 */
    phase: number;
    /** 雾噪声缩放 */
    noiseScale: number;
    /** 雾噪声速度 */
    noiseSpeed: number;
    /** 雾噪声强度 */
    noiseStrength: number;
    /** 是否启用性能优化 */
    enableOptimization: boolean;
    /** 是否启用自适应质量 */
    enableAdaptiveQuality: boolean;
    /** 最大采样步数 */
    maxSteps: number;
}
/**
 * 体积雾区域
 */
export interface VolumetricFogVolume {
    /** 位置 */
    position: THREE.Vector3;
    /** 大小 */
    size: THREE.Vector3;
    /** 密度 */
    density: number;
    /** 颜色 */
    color: THREE.Color;
}
/**
 * 体积雾渲染器
 */
export declare class VolumetricFogRenderer {
    private renderer;
    private scene;
    private camera;
    /** 效果合成器 */
    private composer;
    /** 渲染通道 */
    private renderPass;
    /** 体积雾通道 */
    private fogPass;
    /** 性能监视器 */
    private performanceMonitor;
    /** 雾体积列表 */
    private fogVolumes;
    /** 时间 */
    private time;
    /** 是否已初始化 */
    private initialized;
    /** 参数 */
    private params;
    /**
     * 构造函数
     * @param renderer 渲染器
     * @param scene 场景
     * @param camera 相机
     * @param params 体积雾参数
     */
    constructor(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.PerspectiveCamera, params: VolumetricFogParams);
    /**
     * 初始化
     */
    private initialize;
    /**
     * 更新体积雾参数
     * @param params 体积雾参数
     */
    updateFogParams(params: VolumetricFogParams): void;
    /**
     * 添加雾体积
     * @param volume 雾体积
     */
    addFogVolume(volume: VolumetricFogVolume): void;
    /**
     * 移除雾体积
     * @param volume 雾体积
     */
    removeFogVolume(volume: VolumetricFogVolume): void;
    /**
     * 清除所有雾体积
     */
    clearFogVolumes(): void;
    /**
     * 更新雾体积
     */
    private updateFogVolumes;
    /**
     * 创建洞穴体积雾
     * @param position 位置
     * @param size 大小
     * @param color 颜色
     * @param density 密度
     * @returns 体积雾体积
     */
    createCaveFog(position: THREE.Vector3, size: THREE.Vector3, color: THREE.Color, density: number): VolumetricFogVolume;
    /**
     * 创建地下河流体积雾
     * @param riverPath 河流路径点
     * @param width 宽度
     * @param height 高度
     * @param color 颜色
     * @param density 密度
     * @returns 体积雾体积数组
     */
    createUndergroundRiverFog(riverPath: THREE.Vector3[], width: number, height: number, color: THREE.Color, density: number): VolumetricFogVolume[];
    /**
     * 渲染
     * @param delta 时间增量
     */
    render(delta: number): void;
    /**
     * 自适应质量
     */
    private adaptQuality;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
    /**
     * 销毁
     */
    dispose(): void;
}
