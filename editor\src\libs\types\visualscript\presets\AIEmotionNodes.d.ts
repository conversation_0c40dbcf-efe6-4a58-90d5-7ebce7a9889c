import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 情感分析节点
 * 分析文本的情感
 */
export declare class EmotionAnalysisNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 情感驱动动画节点
 * 根据情感分析结果驱动角色动画
 */
export declare class EmotionDrivenAnimationNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
    /**
     * 根据情感类型获取表情类型
     * @param emotion 情感类型
     * @returns 表情类型
     */
    private getExpressionForEmotion;
}
/**
 * 注册AI情感节点
 * @param registry 节点注册表
 */
export declare function registerAIEmotionNodes(registry: NodeRegistry): void;
