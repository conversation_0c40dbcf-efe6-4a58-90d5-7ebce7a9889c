#!/usr/bin/env pwsh
# DL Engine Windows 备份脚本
# 使用方法: .\backup-windows.ps1 [选项]

param(
    [string]$BackupDir = ".\backups",     # 备份目录
    [switch]$Database,                    # 仅备份数据库
    [switch]$Files,                       # 仅备份文件
    [switch]$Config,                      # 仅备份配置
    [switch]$Compress,                    # 压缩备份
    [switch]$Clean,                       # 清理旧备份
    [int]$RetentionDays = 30,            # 保留天数
    [switch]$Help                         # 显示帮助
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "💾 $message"
    Write-Host "=" * 60
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
DL Engine Windows 备份脚本

用法: .\backup-windows.ps1 [选项]

选项:
  -BackupDir <路径>     备份目录（默认: .\backups）
  -Database            仅备份数据库
  -Files               仅备份文件数据
  -Config              仅备份配置文件
  -Compress            压缩备份文件
  -Clean               清理旧备份文件
  -RetentionDays <天数> 备份保留天数（默认: 30天）
  -Help                显示此帮助信息

示例:
  .\backup-windows.ps1                           # 完整备份
  .\backup-windows.ps1 -Database                 # 仅备份数据库
  .\backup-windows.ps1 -Compress                 # 压缩备份
  .\backup-windows.ps1 -Clean -RetentionDays 7   # 清理7天前的备份
  .\backup-windows.ps1 -BackupDir "D:\Backups"   # 指定备份目录

备份内容:
  - 数据库: MySQL数据库导出
  - 文件数据: MinIO存储、上传文件、模型文件等
  - 配置文件: 环境配置、Docker配置等
"@
}

# 检查Docker服务状态
function Test-DockerService {
    try {
        docker info | Out-Null
        return $true
    } catch {
        return $false
    }
}

# 创建备份目录
function New-BackupDirectory($path) {
    if (-not (Test-Path $path)) {
        New-Item -ItemType Directory -Path $path -Force | Out-Null
        Write-Info "创建备份目录: $path"
    }
    return $path
}

# 备份数据库
function Backup-Database($backupPath) {
    Write-Header "备份数据库"
    
    $dbBackupPath = Join-Path $backupPath "database"
    New-Item -ItemType Directory -Path $dbBackupPath -Force | Out-Null
    
    try {
        # 检查MySQL容器是否运行
        $mysqlContainer = docker ps --filter "name=dl-engine-mysql-win" --format "{{.Names}}"
        
        if (-not $mysqlContainer) {
            Write-Warning "MySQL容器未运行，跳过数据库备份"
            return
        }
        
        Write-Info "备份MySQL数据库..."
        
        # 获取数据库密码
        $envFile = ".\.env"
        if (Test-Path $envFile) {
            $envContent = Get-Content $envFile
            $passwordLine = $envContent | Where-Object { $_ -match "^MYSQL_ROOT_PASSWORD=" }
            if ($passwordLine) {
                $password = $passwordLine.Split("=")[1]
            } else {
                $password = "DLEngine2024!@#"
            }
        } else {
            $password = "DLEngine2024!@#"
        }
        
        # 备份所有数据库
        $timestamp = Get-Date -Format "yyyy-MM-dd-HH-mm-ss"
        $backupFile = Join-Path $dbBackupPath "mysql-all-databases-$timestamp.sql"
        
        $backupCmd = "mysqldump -u root -p$password --all-databases --single-transaction --routines --triggers"
        docker exec dl-engine-mysql-win sh -c $backupCmd > $backupFile
        
        if (Test-Path $backupFile) {
            $fileSize = (Get-Item $backupFile).Length / 1MB
            Write-Success "数据库备份完成: $backupFile (${fileSize:F2} MB)"
        } else {
            Write-Error "数据库备份失败"
        }
        
        # 备份各个数据库
        $databases = @(
            "ir_engine",
            "ir_engine_registry", 
            "ir_engine_users",
            "ir_engine_projects",
            "ir_engine_assets",
            "ir_engine_render",
            "ir_engine_knowledge",
            "ir_engine_ai"
        )
        
        foreach ($db in $databases) {
            try {
                $dbFile = Join-Path $dbBackupPath "$db-$timestamp.sql"
                $dbCmd = "mysqldump -u root -p$password --single-transaction --routines --triggers $db"
                docker exec dl-engine-mysql-win sh -c $dbCmd > $dbFile
                
                if (Test-Path $dbFile) {
                    Write-Info "备份数据库 $db 完成"
                }
            } catch {
                Write-Warning "备份数据库 $db 失败: $($_.Exception.Message)"
            }
        }
        
    } catch {
        Write-Error "数据库备份失败: $($_.Exception.Message)"
    }
}

# 备份文件数据
function Backup-Files($backupPath) {
    Write-Header "备份文件数据"
    
    $fileBackupPath = Join-Path $backupPath "files"
    New-Item -ItemType Directory -Path $fileBackupPath -Force | Out-Null
    
    $dataDirs = @(
        @{Source=".\data\minio"; Target="minio"},
        @{Source=".\data\uploads"; Target="uploads"},
        @{Source=".\data\models"; Target="models"},
        @{Source=".\data\chroma"; Target="chroma"},
        @{Source=".\data\outputs"; Target="outputs"}
    )
    
    foreach ($dir in $dataDirs) {
        if (Test-Path $dir.Source) {
            try {
                $targetPath = Join-Path $fileBackupPath $dir.Target
                Write-Info "备份 $($dir.Source) 到 $targetPath"
                
                Copy-Item -Path $dir.Source -Destination $targetPath -Recurse -Force
                
                $itemCount = (Get-ChildItem -Path $targetPath -Recurse -File).Count
                $totalSize = (Get-ChildItem -Path $targetPath -Recurse -File | Measure-Object -Property Length -Sum).Sum / 1MB
                
                Write-Success "备份 $($dir.Target) 完成: $itemCount 个文件, ${totalSize:F2} MB"
            } catch {
                Write-Warning "备份 $($dir.Source) 失败: $($_.Exception.Message)"
            }
        } else {
            Write-Warning "目录不存在，跳过: $($dir.Source)"
        }
    }
}

# 备份配置文件
function Backup-Config($backupPath) {
    Write-Header "备份配置文件"
    
    $configBackupPath = Join-Path $backupPath "config"
    New-Item -ItemType Directory -Path $configBackupPath -Force | Out-Null
    
    $configFiles = @(
        ".env",
        ".env.windows",
        "docker-compose.yml",
        "docker-compose.windows.yml",
        "docker-compose.production.yml",
        "start-windows.ps1",
        "stop-windows.ps1",
        "health-check-windows.ps1",
        "backup-windows.ps1"
    )
    
    foreach ($file in $configFiles) {
        if (Test-Path $file) {
            try {
                $targetFile = Join-Path $configBackupPath (Split-Path $file -Leaf)
                Copy-Item -Path $file -Destination $targetFile -Force
                Write-Info "备份配置文件: $file"
            } catch {
                Write-Warning "备份配置文件 $file 失败: $($_.Exception.Message)"
            }
        }
    }
    
    # 备份nginx配置
    if (Test-Path "nginx") {
        try {
            $nginxPath = Join-Path $configBackupPath "nginx"
            Copy-Item -Path "nginx" -Destination $nginxPath -Recurse -Force
            Write-Info "备份nginx配置完成"
        } catch {
            Write-Warning "备份nginx配置失败: $($_.Exception.Message)"
        }
    }
    
    # 备份监控配置
    if (Test-Path "monitoring") {
        try {
            $monitoringPath = Join-Path $configBackupPath "monitoring"
            Copy-Item -Path "monitoring" -Destination $monitoringPath -Recurse -Force
            Write-Info "备份监控配置完成"
        } catch {
            Write-Warning "备份监控配置失败: $($_.Exception.Message)"
        }
    }
    
    Write-Success "配置文件备份完成"
}

# 压缩备份
function Compress-Backup($backupPath) {
    Write-Header "压缩备份文件"
    
    try {
        $timestamp = Split-Path $backupPath -Leaf
        $parentDir = Split-Path $backupPath -Parent
        $zipFile = Join-Path $parentDir "$timestamp.zip"
        
        Write-Info "压缩备份到: $zipFile"
        
        # 使用.NET压缩
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::CreateFromDirectory($backupPath, $zipFile)
        
        # 检查压缩结果
        if (Test-Path $zipFile) {
            $originalSize = (Get-ChildItem -Path $backupPath -Recurse -File | Measure-Object -Property Length -Sum).Sum / 1MB
            $compressedSize = (Get-Item $zipFile).Length / 1MB
            $compressionRatio = [math]::Round((1 - $compressedSize / $originalSize) * 100, 2)
            
            Write-Success "压缩完成: ${compressedSize:F2} MB (压缩率: ${compressionRatio}%)"
            
            # 删除原始备份目录
            Remove-Item -Path $backupPath -Recurse -Force
            Write-Info "删除原始备份目录"
            
            return $zipFile
        } else {
            Write-Error "压缩失败"
            return $backupPath
        }
    } catch {
        Write-Error "压缩失败: $($_.Exception.Message)"
        return $backupPath
    }
}

# 清理旧备份
function Remove-OldBackups($backupDir, $retentionDays) {
    Write-Header "清理旧备份"
    
    try {
        $cutoffDate = (Get-Date).AddDays(-$retentionDays)
        $oldBackups = Get-ChildItem -Path $backupDir | Where-Object { 
            $_.CreationTime -lt $cutoffDate -and ($_.Extension -eq ".zip" -or $_.PSIsContainer)
        }
        
        if ($oldBackups.Count -eq 0) {
            Write-Info "没有需要清理的旧备份"
            return
        }
        
        Write-Info "发现 $($oldBackups.Count) 个超过 $retentionDays 天的备份"
        
        foreach ($backup in $oldBackups) {
            try {
                if ($backup.PSIsContainer) {
                    Remove-Item -Path $backup.FullName -Recurse -Force
                } else {
                    Remove-Item -Path $backup.FullName -Force
                }
                Write-Info "删除旧备份: $($backup.Name)"
            } catch {
                Write-Warning "删除备份失败: $($backup.Name) - $($_.Exception.Message)"
            }
        }
        
        Write-Success "旧备份清理完成"
    } catch {
        Write-Error "清理旧备份失败: $($_.Exception.Message)"
    }
}

# 生成备份报告
function New-BackupReport($backupPath, $startTime, $endTime) {
    $duration = $endTime - $startTime
    $reportPath = Join-Path $backupPath "backup-report.txt"
    
    $report = @"
DL Engine 备份报告
==================

备份时间: $($startTime.ToString("yyyy-MM-dd HH:mm:ss"))
完成时间: $($endTime.ToString("yyyy-MM-dd HH:mm:ss"))
耗时: $($duration.ToString("hh\:mm\:ss"))

备份路径: $backupPath

备份内容:
"@
    
    if (Test-Path (Join-Path $backupPath "database")) {
        $dbFiles = Get-ChildItem -Path (Join-Path $backupPath "database") -File
        $report += "`n- 数据库: $($dbFiles.Count) 个文件"
    }
    
    if (Test-Path (Join-Path $backupPath "files")) {
        $fileCount = (Get-ChildItem -Path (Join-Path $backupPath "files") -Recurse -File).Count
        $report += "`n- 文件数据: $fileCount 个文件"
    }
    
    if (Test-Path (Join-Path $backupPath "config")) {
        $configCount = (Get-ChildItem -Path (Join-Path $backupPath "config") -Recurse -File).Count
        $report += "`n- 配置文件: $configCount 个文件"
    }
    
    $totalSize = (Get-ChildItem -Path $backupPath -Recurse -File | Measure-Object -Property Length -Sum).Sum / 1MB
    $report += "`n`n总大小: ${totalSize:F2} MB"
    
    $report | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Info "备份报告已生成: $reportPath"
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    $startTime = Get-Date
    Write-Header "DL Engine 数据备份"
    Write-Info "备份开始时间: $($startTime.ToString("yyyy-MM-dd HH:mm:ss"))"
    
    # 检查Docker服务
    if (-not (Test-DockerService)) {
        Write-Warning "Docker服务未运行，某些备份可能无法完成"
    }
    
    # 创建备份目录
    $timestamp = Get-Date -Format "yyyy-MM-dd-HH-mm-ss"
    $currentBackupPath = Join-Path $BackupDir $timestamp
    New-BackupDirectory $currentBackupPath | Out-Null
    
    # 执行备份
    $backupAll = -not ($Database -or $Files -or $Config)
    
    if ($Database -or $backupAll) {
        Backup-Database $currentBackupPath
    }
    
    if ($Files -or $backupAll) {
        Backup-Files $currentBackupPath
    }
    
    if ($Config -or $backupAll) {
        Backup-Config $currentBackupPath
    }
    
    $endTime = Get-Date
    
    # 生成备份报告
    New-BackupReport $currentBackupPath $startTime $endTime
    
    # 压缩备份
    if ($Compress) {
        $finalPath = Compress-Backup $currentBackupPath
    } else {
        $finalPath = $currentBackupPath
    }
    
    # 清理旧备份
    if ($Clean) {
        Remove-OldBackups $BackupDir $RetentionDays
    }
    
    $duration = $endTime - $startTime
    Write-Success "🎉 备份完成！"
    Write-Info "备份路径: $finalPath"
    Write-Info "耗时: $($duration.ToString("hh\:mm\:ss"))"
    
    # 显示备份大小
    if (Test-Path $finalPath) {
        if ((Get-Item $finalPath).PSIsContainer) {
            $totalSize = (Get-ChildItem -Path $finalPath -Recurse -File | Measure-Object -Property Length -Sum).Sum / 1MB
        } else {
            $totalSize = (Get-Item $finalPath).Length / 1MB
        }
        Write-Info "备份大小: ${totalSize:F2} MB"
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "备份失败: $($_.Exception.Message)"
    exit 1
}
