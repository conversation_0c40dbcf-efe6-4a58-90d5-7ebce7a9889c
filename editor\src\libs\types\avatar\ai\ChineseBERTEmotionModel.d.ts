/**
 * 中文BERT情感分析模型
 * 专门针对中文文本的情感分析
 */
import { EmotionAnalysisResult } from './EmotionBasedAnimationGenerator';
import { BERTEmotionModel, EmotionAnalysisRequest } from './BERTEmotionModel';
/**
 * 中文分词器类型
 */
export declare enum ChineseTokenizerType {
    /** 简单分词 */
    SIMPLE = "simple",
    /** 基于词典的分词 */
    DICTIONARY = "dictionary",
    /** 基于规则的分词 */
    RULE = "rule",
    /** 基于统计的分词 */
    STATISTICAL = "statistical",
    /** 基于深度学习的分词 */
    DEEP_LEARNING = "deep_learning"
}
/**
 * 中文方言类型
 */
export declare enum ChineseDialectType {
    /** 普通话 */
    MANDARIN = "mandarin",
    /** 粤语 */
    CANTONESE = "cantonese",
    /** 闽南语 */
    MIN_NAN = "min_nan",
    /** 上海话 */
    SHANGHAINESE = "shanghainese",
    /** 四川话 */
    SICHUANESE = "sichuanese"
}
/**
 * 中文BERT情感分析模型配置
 */
export interface ChineseBERTModelConfig {
    /** 是否使用远程API */
    useRemoteAPI?: boolean;
    /** 远程API URL */
    remoteAPIUrl?: string;
    /** API密钥 */
    apiKey?: string;
    /** 模型路径 */
    modelPath?: string;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存大小 */
    cacheSize?: number;
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用量化模型 */
    useQuantized?: boolean;
    /** 量化位数 */
    quantizationBits?: 8 | 16 | 32;
    /** 批处理大小 */
    batchSize?: number;
    /** 模型变体 */
    modelVariant?: 'base' | 'large' | 'distilled';
    /** 情感类别 */
    emotionCategories?: string[];
    /** 是否使用多标签分类 */
    useMultiLabel?: boolean;
    /** 置信度阈值 */
    confidenceThreshold?: number;
    /** 是否使用中文分词 */
    useChineseTokenizer?: boolean;
    /** 分词器路径 */
    tokenizerPath?: string;
    /** 分词器类型 */
    tokenizerType?: ChineseTokenizerType;
    /** 是否使用情感词典增强 */
    useDictionaryEnhancement?: boolean;
    /** 情感词典路径 */
    emotionDictionaryPath?: string;
    /** 方言类型 */
    dialectType?: ChineseDialectType;
    /** 是否使用上下文分析 */
    useContextAnalysis?: boolean;
    /** 上下文窗口大小 */
    contextWindowSize?: number;
}
/**
 * 中文BERT情感分析模型
 * 专门针对中文文本的情感分析
 */
export declare class ChineseBERTEmotionModel extends BERTEmotionModel {
    /** 默认中文情感类别 */
    static readonly DEFAULT_CHINESE_EMOTION_CATEGORIES: string[];
    /** 英文情感映射到中文 */
    private englishToChineseEmotionMap;
    /** 中文情感词典 */
    private chineseEmotionDictionary;
    /** 中文分词器 */
    private tokenizer;
    /** 方言处理器 */
    private dialectProcessor;
    /** 上下文历史 */
    private contextHistory;
    /** 上下文窗口大小 */
    private contextWindowSize;
    /** 中文BERT配置 */
    private chineseConfig;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: ChineseBERTModelConfig);
    /**
     * 初始化分词器
     * @param type 分词器类型
     * @param path 分词器路径
     */
    private initTokenizer;
    /**
     * 初始化方言处理器
     * @param dialectType 方言类型
     */
    private initDialectProcessor;
    /**
     * 初始化中文情感词典
     */
    private initChineseEmotionDictionary;
    /**
     * 分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    analyzeEmotion(text: string, options?: Partial<Omit<EmotionAnalysisRequest, 'text'>>): Promise<EmotionAnalysisResult>;
    /**
     * 合并两个分析结果
     * @param result1 结果1
     * @param result2 结果2
     * @param weight1 权重1
     * @param weight2 权重2
     * @returns 合并后的结果
     */
    private combineResults;
    /**
     * 使用词典分析情感
     * @param text 文本
     * @returns 情感分析结果
     */
    private analyzeEmotionWithDictionary;
    /**
     * 应用上下文分析
     * @param emotionScores 情感分数
     */
    private applyContextAnalysis;
    /**
     * 更新上下文历史
     * @param text 文本
     * @param emotion 情感
     */
    private updateContextHistory;
    /**
     * 中文分词
     * @param text 文本
     * @returns 分词结果
     */
    private tokenizeChineseText;
    /**
     * 简单的中文分词
     * @param text 文本
     * @returns 分词结果
     */
    private simpleChineseTokenize;
    /**
     * 判断是否为四字成语
     * @param phrase 短语
     * @returns 是否为四字成语
     */
    private isFourCharacterIdiom;
}
