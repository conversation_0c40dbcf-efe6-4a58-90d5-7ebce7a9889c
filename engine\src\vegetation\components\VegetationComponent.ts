/**
 * 植被组件
 * 用于管理地形上的植被分布
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';


/**
 * 植被项配置接口
 */
export interface VegetationItemConfig {
  /** 模型路径 */
  model: string;
  /** 密度 (0-1) */
  density: number;
  /** 最小缩放 */
  minScale: number;
  /** 最大缩放 */
  maxScale: number;
  /** 最小高度 (地形高度) */
  minHeight: number;
  /** 最大高度 (地形高度) */
  maxHeight: number;
  /** 最小坡度 (度) */
  slopeMin: number;
  /** 最大坡度 (度) */
  slopeMax: number;
  /** 随机旋转 (是否随机旋转Y轴) */
  randomRotation?: boolean;
  /** 随机偏移 (随机位置偏移量) */
  randomOffset?: number;
  /** 避开水体 */
  avoidWater?: boolean;
  /** 颜色变化 */
  colorVariation?: {
    enabled: boolean;
    hue: number;
    saturation: number;
    lightness: number;
  };
  /** 季节影响 */
  seasonalEffect?: boolean;
  /** 风力影响 */
  windEffect?: boolean;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 植被组件选项接口
 */
export interface VegetationComponentOptions {
  /** 地形实体ID */
  terrainEntity: string;
  /** 植被项列表 */
  items: VegetationItemConfig[];
  /** 是否自动生成 */
  autoGenerate?: boolean;
  /** 种子 */
  seed?: number;
  /** 是否使用实例化渲染 */
  useInstancing?: boolean;
  /** 是否使用LOD */
  useLOD?: boolean;
  /** LOD距离 */
  lodDistances?: number[];
  /** 是否使用视锥体剔除 */
  useFrustumCulling?: boolean;
  /** 是否使用八叉树 */
  useOctree?: boolean;
  /** 是否使用GPU实例化 */
  useGPUInstancing?: boolean;
  /** 是否使用阴影 */
  useShadow?: boolean;
  /** 是否使用风效果 */
  useWind?: boolean;
  /** 风力参数 */
  windParams?: {
    strength: number;
    direction: THREE.Vector2;
    frequency: number;
    turbulence: number;
  };
  /** 是否使用季节效果 */
  useSeasonal?: boolean;
  /** 季节参数 */
  seasonalParams?: {
    season: 'spring' | 'summer' | 'autumn' | 'winter';
    intensity: number;
  };
  /** 是否使用分布图 */
  useDistributionMap?: boolean;
  /** 分布图 */
  distributionMap?: string | THREE.Texture;
  /** 是否使用密度图 */
  useDensityMap?: boolean;
  /** 密度图 */
  densityMap?: string | THREE.Texture;
}

/**
 * 植被实例接口
 */
export interface VegetationInstance {
  /** 实例ID */
  id: string;
  /** 植被项索引 */
  itemIndex: number;
  /** 位置 */
  position: THREE.Vector3;
  /** 旋转 */
  rotation: THREE.Euler;
  /** 缩放 */
  scale: THREE.Vector3;
  /** 颜色 */
  color: THREE.Color;
  /** 可见性 */
  visible: boolean;
  /** LOD级别 */
  lodLevel: number;
  /** 自定义数据 */
  userData: any;
}

/**
 * 植被组件类
 */
export class VegetationComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'VegetationComponent';

  /** 地形实体ID */
  public terrainEntity: string;

  /** 植被项列表 */
  public items: VegetationItemConfig[];

  /** 植被实例列表 */
  public instances: Map<string, VegetationInstance>;

  /** 是否自动生成 */
  public autoGenerate: boolean;

  /** 种子 */
  public seed: number;

  /** 是否使用实例化渲染 */
  public useInstancing: boolean;

  /** 是否使用LOD */
  public useLOD: boolean;

  /** LOD距离 */
  public lodDistances: number[];

  /** 是否使用视锥体剔除 */
  public useFrustumCulling: boolean;

  /** 是否使用八叉树 */
  public useOctree: boolean;

  /** 是否使用GPU实例化 */
  public useGPUInstancing: boolean;

  /** 是否使用阴影 */
  public useShadow: boolean;

  /** 是否使用风效果 */
  public useWind: boolean;

  /** 风力参数 */
  public windParams: {
    strength: number;
    direction: THREE.Vector2;
    frequency: number;
    turbulence: number;
  };

  /** 是否使用季节效果 */
  public useSeasonal: boolean;

  /** 季节参数 */
  public seasonalParams: {
    season: 'spring' | 'summer' | 'autumn' | 'winter';
    intensity: number;
  };

  /** 是否使用分布图 */
  public useDistributionMap: boolean;

  /** 分布图 */
  public distributionMap: THREE.Texture | null;

  /** 是否使用密度图 */
  public useDensityMap: boolean;

  /** 密度图 */
  public densityMap: THREE.Texture | null;

  /** 是否已初始化 */
  public initialized: boolean;

  /** 是否需要更新 */
  public needsUpdate: boolean;

  /**
   * 创建植被组件
   * @param options 选项
   */
  constructor(options: VegetationComponentOptions) {
    super(VegetationComponent.TYPE);
    this.terrainEntity = options.terrainEntity;
    this.items = options.items || [];
    this.instances = new Map();
    this.autoGenerate = options.autoGenerate !== undefined ? options.autoGenerate : true;
    this.seed = options.seed !== undefined ? options.seed : Math.floor(Math.random() * 1000000);
    this.useInstancing = options.useInstancing !== undefined ? options.useInstancing : true;
    this.useLOD = options.useLOD !== undefined ? options.useLOD : true;
    this.lodDistances = options.lodDistances || [50, 100, 200, 400];
    this.useFrustumCulling = options.useFrustumCulling !== undefined ? options.useFrustumCulling : true;
    this.useOctree = options.useOctree !== undefined ? options.useOctree : true;
    this.useGPUInstancing = options.useGPUInstancing !== undefined ? options.useGPUInstancing : true;
    this.useShadow = options.useShadow !== undefined ? options.useShadow : true;
    this.useWind = options.useWind !== undefined ? options.useWind : false;
    this.windParams = options.windParams || {
      strength: 0.1,
      direction: new THREE.Vector2(1, 0),
      frequency: 0.2,
      turbulence: 0.1
    };
    this.useSeasonal = options.useSeasonal !== undefined ? options.useSeasonal : false;
    this.seasonalParams = options.seasonalParams || {
      season: 'summer',
      intensity: 1.0
    };
    this.useDistributionMap = options.useDistributionMap !== undefined ? options.useDistributionMap : false;
    this.distributionMap = null;
    this.useDensityMap = options.useDensityMap !== undefined ? options.useDensityMap : false;
    this.densityMap = null;
    this.initialized = false;
    this.needsUpdate = true;
  }

  /**
   * 获取组件类型
   * @returns 组件类型
   */
  public getType(): string {
    return VegetationComponent.TYPE;
  }
}
