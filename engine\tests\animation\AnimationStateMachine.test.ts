/**
 * AnimationStateMachine单元测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AnimationStateMachine, AnimationStateMachineEventType } from '../../src/animation/AnimationStateMachine';
import { Animator } from '../../src/animation/Animator';
import { AnimationClip, LoopMode } from '../../src/animation/AnimationClip';

// 模拟Animator类
vi.mock('../../src/animation/Animator', () => {
  return {
    Animator: vi.fn().mockImplementation(() => {
      return {
        play: vi.fn(),
        setLoop: vi.fn(),
        update: vi.fn(),
        getTime: vi.fn().mockReturnValue(0),
        getClips: vi.fn().mockReturnValue([])
      };
    })
  };
});

describe('AnimationStateMachine', () => {
  let stateMachine: AnimationStateMachine;
  let animator: Animator;

  beforeEach(() => {
    // 创建模拟的Animator
    animator = new Animator();
    
    // 创建测试用的状态机
    stateMachine = new AnimationStateMachine(animator);
  });

  it('应该正确创建状态机', () => {
    expect(stateMachine).toBeDefined();
  });

  it('应该能够添加状态', () => {
    // 添加状态
    stateMachine.addState({
      name: 'Idle',
      type: 'SingleAnimationState',
      clipName: 'idle',
      loop: true,
      clamp: false
    });

    stateMachine.addState({
      name: 'Walk',
      type: 'SingleAnimationState',
      clipName: 'walk',
      loop: true,
      clamp: false
    });

    // 设置当前状态
    stateMachine.setCurrentState('Idle');
    
    // 验证animator.play被调用
    expect(animator.play).toHaveBeenCalledWith('idle', 0);
  });

  it('应该能够添加和处理转换规则', () => {
    // 添加状态
    stateMachine.addState({
      name: 'Idle',
      type: 'SingleAnimationState',
      clipName: 'idle',
      loop: true,
      clamp: false
    });

    stateMachine.addState({
      name: 'Walk',
      type: 'SingleAnimationState',
      clipName: 'walk',
      loop: true,
      clamp: false
    });

    // 添加转换规则
    stateMachine.addTransition({
      from: 'Idle',
      to: 'Walk',
      condition: () => true, // 始终满足条件
      duration: 0.3,
      canInterrupt: true
    });

    // 设置当前状态
    stateMachine.setCurrentState('Idle');
    
    // 更新状态机
    stateMachine.update(0.1);
    
    // 验证animator.play被调用两次
    // 一次是设置初始状态，一次是开始转换
    expect(animator.play).toHaveBeenCalledTimes(2);
    expect(animator.play).toHaveBeenLastCalledWith('walk', 0.3);
  });

  it('应该能够设置和获取参数', () => {
    // 设置参数
    stateMachine.setParameter('speed', 5);
    stateMachine.setParameter('jumping', true);
    
    // 获取参数
    expect(stateMachine.getParameter('speed')).toBe(5);
    expect(stateMachine.getParameter('jumping')).toBe(true);
    expect(stateMachine.getParameter('nonexistent')).toBeUndefined();
  });

  it('应该能够触发事件', () => {
    // 添加事件监听器
    const stateEnterHandler = vi.fn();
    const stateExitHandler = vi.fn();
    const transitionStartHandler = vi.fn();
    const transitionEndHandler = vi.fn();
    
    stateMachine.addEventListener(AnimationStateMachineEventType.STATE_ENTER, stateEnterHandler);
    stateMachine.addEventListener(AnimationStateMachineEventType.STATE_EXIT, stateExitHandler);
    stateMachine.addEventListener(AnimationStateMachineEventType.TRANSITION_START, transitionStartHandler);
    stateMachine.addEventListener(AnimationStateMachineEventType.TRANSITION_END, transitionEndHandler);
    
    // 添加状态
    stateMachine.addState({
      name: 'Idle',
      type: 'SingleAnimationState',
      clipName: 'idle',
      loop: true,
      clamp: false
    });

    stateMachine.addState({
      name: 'Walk',
      type: 'SingleAnimationState',
      clipName: 'walk',
      loop: true,
      clamp: false
    });
    
    // 设置当前状态
    stateMachine.setCurrentState('Idle');
    
    // 验证STATE_ENTER事件被触发
    expect(stateEnterHandler).toHaveBeenCalledTimes(1);
    expect(stateEnterHandler).toHaveBeenCalledWith({
      name: 'Idle',
      type: 'SingleAnimationState',
      clipName: 'idle',
      loop: true,
      clamp: false
    });
    
    // 添加转换规则
    stateMachine.addTransition({
      from: 'Idle',
      to: 'Walk',
      condition: () => true, // 始终满足条件
      duration: 0.3,
      canInterrupt: true
    });
    
    // 更新状态机
    stateMachine.update(0.1);
    
    // 验证TRANSITION_START事件被触发
    expect(transitionStartHandler).toHaveBeenCalledTimes(1);
    expect(transitionStartHandler).toHaveBeenCalledWith({
      from: 'Idle',
      to: 'Walk',
      duration: 0.3
    });
    
    // 模拟转换完成
    vi.mocked(animator.getTime).mockReturnValue(0.5);
    stateMachine.update(0.1);
    
    // 验证STATE_EXIT和STATE_ENTER事件被触发
    expect(stateExitHandler).toHaveBeenCalledTimes(1);
    expect(stateEnterHandler).toHaveBeenCalledTimes(2);
    
    // 验证TRANSITION_END事件被触发
    expect(transitionEndHandler).toHaveBeenCalledTimes(1);
    expect(transitionEndHandler).toHaveBeenCalledWith({
      from: 'Idle',
      to: 'Walk'
    });
  });

  it('应该能够移除事件监听器', () => {
    // 添加事件监听器
    const stateEnterHandler = vi.fn();
    stateMachine.addEventListener(AnimationStateMachineEventType.STATE_ENTER, stateEnterHandler);
    
    // 添加状态
    stateMachine.addState({
      name: 'Idle',
      type: 'SingleAnimationState',
      clipName: 'idle',
      loop: true,
      clamp: false
    });
    
    // 设置当前状态
    stateMachine.setCurrentState('Idle');
    
    // 验证事件被触发
    expect(stateEnterHandler).toHaveBeenCalledTimes(1);
    
    // 移除事件监听器
    stateMachine.removeEventListener(AnimationStateMachineEventType.STATE_ENTER, stateEnterHandler);
    
    // 再次设置当前状态
    stateMachine.setCurrentState('Idle');
    
    // 验证事件不再被触发
    expect(stateEnterHandler).toHaveBeenCalledTimes(1);
  });
});
