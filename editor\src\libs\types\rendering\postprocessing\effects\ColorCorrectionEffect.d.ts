/**
 * 颜色校正效果
 * 提供更精确的颜色控制
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 颜色校正效果选项
 */
export interface ColorCorrectionEffectOptions extends PostProcessingEffectOptions {
    /** 亮度 */
    brightness?: number;
    /** 对比度 */
    contrast?: number;
    /** 饱和度 */
    saturation?: number;
    /** 色相偏移 */
    hue?: number;
    /** 伽马 */
    gamma?: number;
    /** 色温 */
    temperature?: number;
    /** 色调 */
    tint?: number;
    /** 阴影色调 */
    shadowTint?: THREE.Color;
    /** 高光色调 */
    highlightTint?: THREE.Color;
    /** 是否使用LUT */
    useLUT?: boolean;
    /** LUT纹理 */
    lutTexture?: THREE.Texture;
    /** LUT强度 */
    lutIntensity?: number;
}
/**
 * 颜色校正效果
 */
export declare class ColorCorrectionEffect extends PostProcessingEffect {
    /** 亮度 */
    private brightness;
    /** 对比度 */
    private contrast;
    /** 饱和度 */
    private saturation;
    /** 色相偏移 */
    private hue;
    /** 伽马 */
    private gamma;
    /** 色温 */
    private temperature;
    /** 色调 */
    private tint;
    /** 阴影色调 */
    private shadowTint;
    /** 高光色调 */
    private highlightTint;
    /** 是否使用LUT */
    private useLUT;
    /** LUT纹理 */
    private lutTexture;
    /** LUT强度 */
    private lutIntensity;
    /** 颜色校正通道 */
    private colorCorrectionPass;
    /**
     * 创建颜色校正效果
     * @param options 颜色校正效果选项
     */
    constructor(options?: ColorCorrectionEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 设置亮度
     * @param brightness 亮度
     */
    setBrightness(brightness: number): void;
    /**
     * 获取亮度
     * @returns 亮度
     */
    getBrightness(): number;
    /**
     * 设置对比度
     * @param contrast 对比度
     */
    setContrast(contrast: number): void;
    /**
     * 获取对比度
     * @returns 对比度
     */
    getContrast(): number;
    /**
     * 设置饱和度
     * @param saturation 饱和度
     */
    setSaturation(saturation: number): void;
    /**
     * 获取饱和度
     * @returns 饱和度
     */
    getSaturation(): number;
    /**
     * 设置色相偏移
     * @param hue 色相偏移
     */
    setHue(hue: number): void;
    /**
     * 获取色相偏移
     * @returns 色相偏移
     */
    getHue(): number;
    /**
     * 设置伽马
     * @param gamma 伽马
     */
    setGamma(gamma: number): void;
    /**
     * 获取伽马
     * @returns 伽马
     */
    getGamma(): number;
    /**
     * 设置色温
     * @param temperature 色温
     */
    setTemperature(temperature: number): void;
    /**
     * 获取色温
     * @returns 色温
     */
    getTemperature(): number;
    /**
     * 设置色调
     * @param tint 色调
     */
    setTint(tint: number): void;
    /**
     * 获取色调
     * @returns 色调
     */
    getTint(): number;
    /**
     * 设置阴影色调
     * @param color 阴影色调
     */
    setShadowTint(color: THREE.Color): void;
    /**
     * 获取阴影色调
     * @returns 阴影色调
     */
    getShadowTint(): THREE.Color;
    /**
     * 设置高光色调
     * @param color 高光色调
     */
    setHighlightTint(color: THREE.Color): void;
    /**
     * 获取高光色调
     * @returns 高光色调
     */
    getHighlightTint(): THREE.Color;
    /**
     * 设置是否使用LUT
     * @param use 是否使用
     */
    setUseLUT(use: boolean): void;
    /**
     * 获取是否使用LUT
     * @returns 是否使用
     */
    isUseLUT(): boolean;
    /**
     * 设置LUT纹理
     * @param texture LUT纹理
     */
    setLUTTexture(texture: THREE.Texture): void;
    /**
     * 获取LUT纹理
     * @returns LUT纹理
     */
    getLUTTexture(): THREE.Texture | null;
    /**
     * 设置LUT强度
     * @param intensity LUT强度
     */
    setLUTIntensity(intensity: number): void;
    /**
     * 获取LUT强度
     * @returns LUT强度
     */
    getLUTIntensity(): number;
    /**
     * 更新效果
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
}
