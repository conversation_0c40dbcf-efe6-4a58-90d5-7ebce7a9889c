import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 故障效果选项
 */
export interface GlitchEffectOptions extends PostProcessingEffectOptions {
    /** 是否使用随机故障 */
    goWild?: boolean;
    /** 故障强度 */
    amount?: number;
    /** 故障间隔（秒） */
    interval?: number;
    /** 故障持续时间（秒） */
    duration?: number;
}
/**
 * 故障效果
 */
export declare class GlitchEffect extends PostProcessingEffect {
    /** 是否使用随机故障 */
    private goWild;
    /** 故障强度 */
    private amount;
    /** 故障间隔（秒） */
    private interval;
    /** 故障持续时间（秒） */
    private duration;
    /** 故障通道 */
    private glitchPass;
    /** 计时器 */
    private timer;
    /** 是否正在故障 */
    private isGlitching;
    /** 故障计时器 */
    private glitchTimer;
    /**
     * 创建故障效果
     * @param options 故障效果选项
     */
    constructor(options?: GlitchEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 更新效果
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 开始故障
     */
    startGlitch(): void;
    /**
     * 停止故障
     */
    stopGlitch(): void;
    /**
     * 设置是否使用随机故障
     * @param goWild 是否使用随机故障
     */
    setGoWild(goWild: boolean): void;
    /**
     * 获取是否使用随机故障
     * @returns 是否使用随机故障
     */
    isGoWild(): boolean;
    /**
     * 设置故障强度
     * @param amount 故障强度
     */
    setAmount(amount: number): void;
    /**
     * 获取故障强度
     * @returns 故障强度
     */
    getAmount(): number;
    /**
     * 设置故障间隔
     * @param interval 故障间隔（秒）
     */
    setInterval(interval: number): void;
    /**
     * 获取故障间隔
     * @returns 故障间隔（秒）
     */
    getInterval(): number;
    /**
     * 设置故障持续时间
     * @param duration 故障持续时间（秒）
     */
    setDuration(duration: number): void;
    /**
     * 获取故障持续时间
     * @returns 故障持续时间（秒）
     */
    getDuration(): number;
}
