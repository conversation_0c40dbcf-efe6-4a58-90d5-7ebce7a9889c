import { Scene } from '../scene/Scene';
import type { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { LODSystem } from '../rendering/optimization/LODSystem';
import { BatchingSystem } from '../rendering/optimization/BatchingSystem';
import { FrustumCullingSystem } from '../rendering/optimization/FrustumCullingSystem';
import { InstancedRenderingSystem } from '../rendering/optimization/InstancedRenderingSystem';
/**
 * 优化类型
 */
export declare enum OptimizationType {
    /** LOD优化 */
    LOD = "lod",
    /** 视锥体剔除优化 */
    FRUSTUM_CULLING = "frustumCulling",
    /** 实例化渲染优化 */
    INSTANCED_RENDERING = "instancedRendering",
    /** 批处理优化 */
    BATCHING = "batching",
    /** 材质优化 */
    MATERIAL_OPTIMIZATION = "materialOptimization",
    /** 纹理优化 */
    TEXTURE_OPTIMIZATION = "textureOptimization",
    /** 几何体优化 */
    GEOMETRY_OPTIMIZATION = "geometryOptimization",
    /** 灯光优化 */
    LIGHT_OPTIMIZATION = "lightOptimization",
    /** 阴影优化 */
    SHADOW_OPTIMIZATION = "shadowOptimization",
    /** 内存优化 */
    MEMORY_OPTIMIZATION = "memoryOptimization",
    /** 遮挡剔除优化 */
    OCCLUSION_CULLING = "occlusionCulling",
    /** 细节纹理优化 */
    DETAIL_TEXTURE = "detailTexture",
    /** 网格简化优化 */
    MESH_SIMPLIFICATION = "meshSimplification",
    /** 粒子系统优化 */
    PARTICLE_OPTIMIZATION = "particleOptimization",
    /** 动画优化 */
    ANIMATION_OPTIMIZATION = "animationOptimization"
}
/**
 * 优化严重程度
 */
export declare enum OptimizationSeverity {
    /** 低 */
    LOW = "low",
    /** 中 */
    MEDIUM = "medium",
    /** 高 */
    HIGH = "high"
}
/**
 * 优化建议
 */
export interface OptimizationSuggestion {
    /** 类型 */
    type: OptimizationType;
    /** 严重程度 */
    severity: OptimizationSeverity;
    /** 标题 */
    title: string;
    /** 描述 */
    description: string;
    /** 提示 */
    tips: string[];
    /** 分数 */
    score: number;
    /** 目标实体 */
    targetEntities?: Entity[];
    /** 自动优化函数 */
    autoOptimize?: () => Promise<boolean>;
}
/**
 * 场景分析结果
 */
export interface SceneAnalysisResult {
    /** 场景ID */
    sceneId: string;
    /** 场景名称 */
    sceneName: string;
    /** 实体数量 */
    entityCount: number;
    /** 可渲染对象数量 */
    renderableCount: number;
    /** 三角形数量 */
    triangleCount: number;
    /** 顶点数量 */
    vertexCount: number;
    /** 材质数量 */
    materialCount: number;
    /** 纹理数量 */
    textureCount: number;
    /** 纹理内存 (MB) */
    textureMemory: number;
    /** 灯光数量 */
    lightCount: number;
    /** 绘制调用数量 */
    drawCalls: number;
    /** 内存使用 (MB) */
    memoryUsage: number;
    /** 优化建议 */
    suggestions: OptimizationSuggestion[];
    /** 总体得分 */
    overallScore: number;
    /** 分析时间戳 */
    timestamp: number;
}
/**
 * 场景优化器配置
 */
export interface SceneOptimizerConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否启用自动LOD生成 */
    enableAutoLOD?: boolean;
    /** 是否启用自动实例化 */
    enableAutoInstancing?: boolean;
    /** 是否启用自动批处理 */
    enableAutoBatching?: boolean;
    /** 是否启用自动材质优化 */
    enableAutoMaterialOptimization?: boolean;
    /** 是否启用自动纹理优化 */
    enableAutoTextureOptimization?: boolean;
    /** 是否启用自动几何体优化 */
    enableAutoGeometryOptimization?: boolean;
    /** 是否启用自动灯光优化 */
    enableAutoLightOptimization?: boolean;
    /** 是否启用自动阴影优化 */
    enableAutoShadowOptimization?: boolean;
    /** 是否启用自动内存优化 */
    enableAutoMemoryOptimization?: boolean;
    /** 是否启用遮挡剔除优化 */
    enableOcclusionCulling?: boolean;
    /** 是否启用细节纹理优化 */
    enableDetailTexture?: boolean;
    /** 是否启用网格简化优化 */
    enableMeshSimplification?: boolean;
    /** 是否启用粒子系统优化 */
    enableParticleOptimization?: boolean;
    /** 是否启用动画优化 */
    enableAnimationOptimization?: boolean;
    /** 优化阈值 */
    thresholds?: {
        /** 三角形数量阈值 */
        triangles?: {
            /** 低严重度阈值 */
            low: number;
            /** 中严重度阈值 */
            medium: number;
            /** 高严重度阈值 */
            high: number;
        };
        /** 绘制调用阈值 */
        drawCalls?: {
            /** 低严重度阈值 */
            low: number;
            /** 中严重度阈值 */
            medium: number;
            /** 高严重度阈值 */
            high: number;
        };
        /** 内存使用阈值 (MB) */
        memory?: {
            /** 低严重度阈值 */
            low: number;
            /** 中严重度阈值 */
            medium: number;
            /** 高严重度阈值 */
            high: number;
        };
        /** 粒子数量阈值 */
        particles?: {
            /** 低严重度阈值 */
            low: number;
            /** 中严重度阈值 */
            medium: number;
            /** 高严重度阈值 */
            high: number;
        };
        /** 动画数量阈值 */
        animations?: {
            /** 低严重度阈值 */
            low: number;
            /** 中严重度阈值 */
            medium: number;
            /** 高严重度阈值 */
            high: number;
        };
        /** 纹理分辨率阈值 */
        textureResolution?: {
            /** 低严重度阈值 */
            low: number;
            /** 中严重度阈值 */
            medium: number;
            /** 高严重度阈值 */
            high: number;
        };
    };
}
/**
 * 场景优化器事件类型
 */
export declare enum SceneOptimizerEventType {
    /** 分析开始 */
    ANALYSIS_START = "analysisStart",
    /** 分析完成 */
    ANALYSIS_COMPLETE = "analysisComplete",
    /** 优化开始 */
    OPTIMIZATION_START = "optimizationStart",
    /** 优化进度 */
    OPTIMIZATION_PROGRESS = "optimizationProgress",
    /** 优化完成 */
    OPTIMIZATION_COMPLETE = "optimizationComplete",
    /** 优化失败 */
    OPTIMIZATION_FAILED = "optimizationFailed"
}
/**
 * 场景优化器
 */
export declare class SceneOptimizer extends EventEmitter {
    /** 单例实例 */
    private static instance;
    /** 配置 */
    private config;
    /** 最后一次分析结果 */
    private lastAnalysisResult;
    /** LOD系统 */
    private lodSystem;
    /** 批处理系统 */
    private batchingSystem;
    /** 视锥体剔除系统 */
    private frustumCullingSystem;
    /** 实例化渲染系统 */
    private instancedRenderingSystem;
    /**
     * 获取单例实例
     */
    static getInstance(): SceneOptimizer;
    /**
     * 构造函数
     * @param config 配置
     */
    private constructor();
    /**
     * 配置优化器
     * @param config 配置
     */
    configure(config: SceneOptimizerConfig): void;
    /**
     * 设置优化系统
     * @param lodSystem LOD系统
     * @param batchingSystem 批处理系统
     * @param frustumCullingSystem 视锥体剔除系统
     * @param instancedRenderingSystem 实例化渲染系统
     */
    setOptimizationSystems(lodSystem: LODSystem | null, batchingSystem: BatchingSystem | null, frustumCullingSystem: FrustumCullingSystem | null, instancedRenderingSystem: InstancedRenderingSystem | null): void;
    /**
     * 分析场景
     * @param scene 场景
     * @returns 分析结果Promise
     */
    analyzeScene(scene: Scene): Promise<SceneAnalysisResult>;
    /**
     * 收集场景统计信息
     * @param scene 场景
     * @returns 场景统计信息
     */
    private collectSceneStats;
    /**
     * 收集纹理信息
     * @param material 材质
     * @param textureSet 纹理集合
     * @param callback 回调函数
     */
    private collectTextureInfo;
    /**
     * 估算几何体内存
     * @param geometry 几何体
     * @returns 内存大小（字节）
     */
    private estimateGeometryMemory;
    /**
     * 估算材质内存
     * @param material 材质
     * @returns 内存大小（字节）
     */
    private estimateMaterialMemory;
    /**
     * 估算纹理内存
     * @param texture 纹理
     * @returns 内存大小（字节）
     */
    private estimateTextureMemory;
    /**
     * 生成优化建议
     * @param scene 场景
     * @param stats 统计信息
     * @returns 优化建议数组
     */
    private generateOptimizationSuggestions;
    /**
     * 计算分数
     * @param value 当前值
     * @param max 最大值（对应0分）
     * @param min 最小值（对应100分）
     * @returns 0-100的分数
     */
    private calculateScore;
    /**
     * 计算总体得分
     * @param stats 统计信息
     * @param suggestions 优化建议
     * @returns 总体得分
     */
    private calculateOverallScore;
    /**
     * 优化场景
     * @param scene 场景
     * @param description 优化描述（可选）
     * @returns 优化结果Promise
     */
    optimizeScene(scene: Scene, description?: string): Promise<boolean>;
    /**
     * 优化LOD
     * @param scene 场景
     * @returns 优化结果Promise
     */
    private optimizeLOD;
    /**
     * 优化批处理
     * @param scene 场景
     * @returns 优化结果Promise
     */
    private optimizeBatching;
    /**
     * 优化内存
     * @param scene 场景
     * @returns 优化结果Promise
     */
    private optimizeMemory;
    /**
     * 优化纹理
     * @param scene 场景
     * @returns 优化结果Promise
     */
    private optimizeTextures;
    /**
     * 优化灯光
     * @param scene 场景
     * @returns 优化结果Promise
     */
    private optimizeLights;
    /**
     * 优化遮挡剔除
     * @param scene 场景
     * @returns 优化结果Promise
     */
    private optimizeOcclusionCulling;
    /**
     * 优化细节纹理
     * @param scene 场景
     * @returns 优化结果Promise
     */
    private optimizeDetailTexture;
    /**
     * 优化网格简化
     * @param scene 场景
     * @returns 优化结果Promise
     */
    private optimizeMeshSimplification;
    /**
     * 优化粒子系统
     * @param scene 场景
     * @returns 优化结果Promise
     */
    private optimizeParticleSystem;
    /**
     * 优化动画系统
     * @param scene 场景
     * @returns 优化结果Promise
     */
    private optimizeAnimationSystem;
    /**
     * 获取最后一次分析结果
     * @returns 分析结果
     */
    getLastAnalysisResult(): SceneAnalysisResult | null;
}
