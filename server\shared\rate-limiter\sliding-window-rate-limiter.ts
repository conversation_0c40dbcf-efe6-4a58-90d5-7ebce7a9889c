/**
 * 滑动窗口限流器
 */
import { Injectable, Logger } from '@nestjs/common';
import {
  IRateLimiter,
  RateLimiterOptions,
  RateLimiterStats,
  RateLimiterType,
} from './rate-limiter.interface';

/**
 * 滑动窗口
 */
interface SlidingWindow {
  /**
   * 请求时间戳数组
   */
  timestamps: number[];

  /**
   * 当前窗口开始时间
   */
  windowStart: number;
}

/**
 * 滑动窗口限流器
 * 实现了滑动窗口算法，用于限制请求速率
 */
@Injectable()
export class SlidingWindowRateLimiter implements IRateLimiter {
  private readonly logger = new Logger(SlidingWindowRateLimiter.name);
  private readonly windows = new Map<string, SlidingWindow>();
  private totalRequests = 0;
  private limitedRequests = 0;
  private passedRequests = 0;
  private lastResetTime = new Date();

  /**
   * 创建滑动窗口限流器
   * @param options 限流器配置
   */
  constructor(private readonly options: RateLimiterOptions) {
    this.logger.log(`滑动窗口限流器 ${options.name} 已创建，窗口大小: ${options.windowMs}ms, 最大请求数: ${options.maxRequests}`);
  }

  /**
   * 消费令牌
   * @param key 键
   * @param tokens 令牌数，默认为1
   */
  async consume(key: string, tokens: number = 1): Promise<boolean> {
    this.totalRequests++;
    
    // 获取或创建滑动窗口
    const window = this.getOrCreateWindow(key);
    
    // 清理过期的请求
    this.cleanupWindow(window);
    
    // 检查是否超过限制
    if (window.timestamps.length < this.options.maxRequests) {
      // 添加新请求
      for (let i = 0; i < tokens; i++) {
        window.timestamps.push(Date.now());
      }
      
      this.passedRequests++;
      return true;
    } else {
      // 超过限制，限流
      this.limitedRequests++;
      
      // 触发限流回调
      if (this.options.onLimitReached) {
        this.options.onLimitReached(key, this.options.maxRequests);
      }
      
      return false;
    }
  }

  /**
   * 获取剩余令牌数
   * @param key 键
   */
  async getRemainingTokens(key: string): Promise<number> {
    // 获取或创建滑动窗口
    const window = this.getOrCreateWindow(key);
    
    // 清理过期的请求
    this.cleanupWindow(window);
    
    return this.options.maxRequests - window.timestamps.length;
  }

  /**
   * 获取限流器统计信息
   */
  getStats(): RateLimiterStats {
    return {
      name: this.options.name,
      type: RateLimiterType.SLIDING_WINDOW,
      totalRequests: this.totalRequests,
      limitedRequests: this.limitedRequests,
      passedRequests: this.passedRequests,
      currentRate: this.calculateCurrentRate(),
      lastResetTime: this.lastResetTime,
    };
  }

  /**
   * 重置限流器
   */
  async reset(): Promise<void> {
    this.windows.clear();
    this.totalRequests = 0;
    this.limitedRequests = 0;
    this.passedRequests = 0;
    this.lastResetTime = new Date();
  }

  /**
   * 获取或创建滑动窗口
   * @param key 键
   */
  private getOrCreateWindow(key: string): SlidingWindow {
    if (!this.windows.has(key)) {
      this.windows.set(key, {
        timestamps: [],
        windowStart: Date.now(),
      });
    }
    
    return this.windows.get(key);
  }

  /**
   * 清理过期的请求
   * @param window 滑动窗口
   */
  private cleanupWindow(window: SlidingWindow): void {
    const now = Date.now();
    const windowEnd = now;
    const windowStart = windowEnd - this.options.windowMs;
    
    // 更新窗口开始时间
    window.windowStart = windowStart;
    
    // 移除过期的请求
    window.timestamps = window.timestamps.filter(timestamp => timestamp >= windowStart);
  }

  /**
   * 计算当前请求速率
   */
  private calculateCurrentRate(): number {
    const now = new Date();
    const elapsedSeconds = (now.getTime() - this.lastResetTime.getTime()) / 1000;
    
    if (elapsedSeconds <= 0) {
      return 0;
    }
    
    return this.passedRequests / elapsedSeconds;
  }
}
