/**
 * 移动设备性能优化面板
 * 用于配置和监控移动设备性能优化
 */
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Switch, Select, Button, Tooltip, Divider, Space, Tag, Collapse, Statistic, Slider, Radio } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  ThunderboltOutlined,
  DashboardOutlined,
  SettingOutlined,
  FireOutlined,
  BarsOutlined,
  WifiOutlined,
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  ReloadOutlined,
  LineChartOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import MobilePerformanceService, { 
  PerformanceLevel, 
  PerformanceConfig,
  MobilePerformanceEventType,
  PerformanceMonitorConfigConfigData
} from '../../services/MobilePerformanceService';
import MobileDeviceService, { DeviceType } from '../../services/MobileDeviceService';
import './MobilePerformancePanel.less';

const { Panel } = Collapse;
const { Option } = Select;

/**
 * 移动设备性能优化面板属性
 */
interface MobilePerformancePanelProps {
  /** 是否显示标题 */
  showTitle?: boolean;
}

/**
 * 移动设备性能优化面板
 */
const MobilePerformancePanel: React.FC<MobilePerformancePanelProps> = ({
  showTitle = true
}) => {
  const { t } = useTranslation();
  
  // 状态
  const [performanceLevel, setPerformanceLevel] = useState<PerformanceLevel>(
    MobilePerformanceService.getPerformanceLevel()
  );
  const [currentConfig, setCurrentConfig] = useState<PerformanceConfig>(
    MobilePerformanceService.getCurrentConfig()
  );
  const [monitorData, setMonitorData] = useState<PerformanceMonitorConfigConfigData>(
    MobilePerformanceService.getMonitorData()
  );
  const [autoOptimization, setAutoOptimization] = useState<boolean>(true);
  const [batteryOptimization, setBatteryOptimization] = useState<boolean>(true);
  const [temperatureOptimization, setTemperatureOptimization] = useState<boolean>(true);
  const [networkOptimization, setNetworkOptimization] = useState<boolean>(true);
  const [backgroundPause, setBackgroundPause] = useState<boolean>(true);
  const [customizing, setCustomizing] = useState<boolean>(false);
  const [deviceType, setDeviceType] = useState<DeviceType>(
    MobileDeviceService.getDeviceInfo().type
  );
  
  // 初始化
  useEffect(() => {
    // 监听性能级别变更事件
    const handlePerformanceLevelChanged = (data: any) => {
      setPerformanceLevel(data.newLevel);
    };
    
    // 监听性能配置变更事件
    const handlePerformanceConfigChanged = (data: any) => {
      setCurrentConfig(data.newConfig);
    };
    
    // 监听性能监控更新事件
    const handlePerformanceMonitorConfigConfigUpdate = (data: any) => {
      setMonitorData(data);
    };
    
    // 监听设备变更事件
    const handleDeviceChanged = (data: any) => {
      setDeviceType(data.type);
    };
    
    // 添加事件监听
    MobilePerformanceService.on(MobilePerformanceEventType.PERFORMANCE_LEVEL_CHANGED, handlePerformanceLevelChanged);
    MobilePerformanceService.on(MobilePerformanceEventType.PERFORMANCE_CONFIG_CHANGED, handlePerformanceConfigChanged);
    MobilePerformanceService.on(MobilePerformanceEventType.PERFORMANCE_MONITOR_UPDATE, handlePerformanceMonitorConfigConfigUpdate);
    MobileDeviceService.on('deviceChanged', handleDeviceChanged);
    
    // 启动性能监控
    MobilePerformanceService.startMonitoring();
    
    // 清理函数
    return () => {
      MobilePerformanceService.off(MobilePerformanceEventType.PERFORMANCE_LEVEL_CHANGED, handlePerformanceLevelChanged);
      MobilePerformanceService.off(MobilePerformanceEventType.PERFORMANCE_CONFIG_CHANGED, handlePerformanceConfigChanged);
      MobilePerformanceService.off(MobilePerformanceEventType.PERFORMANCE_MONITOR_UPDATE, handlePerformanceMonitorConfigConfigUpdate);
      MobileDeviceService.off('deviceChanged', handleDeviceChanged);
    };
  }, []);
  
  // 处理性能级别变更
  const handlePerformanceLevelChange = (value: PerformanceLevel) => {
    MobilePerformanceService.setPerformanceLevel(value);
  };
  
  // 处理自动优化开关
  const handleAutoOptimizationChange = (checked: boolean) => {
    setAutoOptimization(checked);
    MobilePerformanceService.configure({ enableAutoOptimization: checked });
  };
  
  // 处理电池优化开关
  const handleBatteryOptimizationChange = (checked: boolean) => {
    setBatteryOptimization(checked);
    MobilePerformanceService.configure({ enableBatteryOptimization: checked });
  };
  
  // 处理温度优化开关
  const handleTemperatureOptimizationChange = (checked: boolean) => {
    setTemperatureOptimization(checked);
    MobilePerformanceService.configure({ enableTemperatureOptimization: checked });
  };
  
  // 处理网络优化开关
  const handleNetworkOptimizationChange = (checked: boolean) => {
    setNetworkOptimization(checked);
    MobilePerformanceService.configure({ enableNetworkOptimization: checked });
  };
  
  // 处理后台暂停开关
  const handleBackgroundPauseChange = (checked: boolean) => {
    setBackgroundPause(checked);
    MobilePerformanceService.configure({ enableBackgroundPause: checked });
  };
  
  // 处理自定义配置
  const handleCustomConfigChange = (key: keyof PerformanceConfig, value: any) => {
    const newConfig = { ...currentConfig, [key]: value };
    setCurrentConfig(newConfig);
    
    // 自定义当前性能级别的配置
    MobilePerformanceService.customizePerformanceConfig(performanceLevel, { [key]: value });
  };
  
  // 重置性能配置
  const resetPerformanceConfig = () => {
    // 重新设置当前性能级别以重置为默认配置
    MobilePerformanceService.setPerformanceLevel(performanceLevel);
  };
  
  // 渲染设备图标
  const renderDeviceIcon = () => {
    switch (deviceType) {
      case DeviceType.MOBILE:
        return <MobileOutlined />;
      case DeviceType.TABLET:
        return <TabletOutlined />;
      default:
        return <DesktopOutlined />;
    }
  };
  
  // 渲染性能级别标签
  const renderPerformanceLevelTag = (level: PerformanceLevel) => {
    switch (level) {
      case PerformanceLevel.LOW:
        return <Tag color="green">低</Tag>;
      case PerformanceLevel.MEDIUM:
        return <Tag color="blue">中</Tag>;
      case PerformanceLevel.HIGH:
        return <Tag color="orange">高</Tag>;
      case PerformanceLevel.ULTRA:
        return <Tag color="red">超高</Tag>;
      case PerformanceLevel.AUTO:
        return <Tag color="purple">自动</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };
  
  // 渲染性能监控数据
  const renderMonitorData = () => {
    return (
      <div className="monitor-data">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic
              title={t('performance.fps')}
              value={monitorData.fps.toFixed(1)}
              suffix="FPS"
              valueStyle={{ color: monitorData.fps < 30 ? '#cf1322' : monitorData.fps < 45 ? '#faad14' : '#3f8600' }}
              prefix={<DashboardOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title={t('performance.memory')}
              value={monitorData.memoryUsage.toFixed(1)}
              suffix="MB"
              valueStyle={{ color: monitorData.memoryUsage > 500 ? '#cf1322' : '#3f8600' }}
              prefix={<ThunderboltOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title={t('performance.battery')}
              value={monitorData.batteryLevel}
              suffix="%"
              valueStyle={{ color: monitorData.batteryLevel < 20 ? '#cf1322' : monitorData.batteryLevel < 50 ? '#faad14' : '#3f8600' }}
              prefix={<BarsOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title={t('performance.temperature')}
              value={monitorData.temperature}
              suffix="°C"
              valueStyle={{ color: monitorData.temperature > 40 ? '#cf1322' : monitorData.temperature > 35 ? '#faad14' : '#3f8600' }}
              prefix={<FireOutlined />}
            />
          </Col>
        </Row>
      </div>
    );
  };
  
  // 渲染自定义配置面板
  const renderCustomConfigPanel = () => {
    if (!customizing) return null;
    
    return (
      <div className="custom-config-panel">
        <Divider>{t('performance.customConfig')}</Divider>
        
        <Collapse defaultActiveKey={['rendering']}>
          <Panel header={t('performance.renderingSettings')} key="rendering">
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <div className="setting-label">{t('performance.renderScale')}</div>
                <Slider
                  min={0.25}
                  max={1.5}
                  step={0.05}
                  value={currentConfig.renderScale}
                  onChange={(value) => handleCustomConfigChange('renderScale', value)}
                  marks={{
                    0.25: '25%',
                    0.5: '50%',
                    0.75: '75%',
                    1: '100%',
                    1.5: '150%'
                  }}
                />
              </Col>
              
              <Col span={24}>
                <div className="setting-label">{t('performance.maxTextureSize')}</div>
                <Select
                  value={currentConfig.maxTextureSize}
                  onChange={(value) => handleCustomConfigChange('maxTextureSize', value)}
                  style={{ width: '100%' }}
                >
                  <Option value={512}>512 x 512</Option>
                  <Option value={1024}>1024 x 1024</Option>
                  <Option value={2048}>2048 x 2048</Option>
                  <Option value={4096}>4096 x 4096</Option>
                  <Option value={8192}>8192 x 8192</Option>
                </Select>
              </Col>
              
              <Col span={24}>
                <div className="setting-label">{t('performance.shadowQuality')}</div>
                <Radio.Group
                  value={currentConfig.shadowQuality}
                  onChange={(e) => handleCustomConfigChange('shadowQuality', e.target.value)}
                >
                  <Radio.Button value="off">{t('performance.off')}</Radio.Button>
                  <Radio.Button value="low">{t('performance.low')}</Radio.Button>
                  <Radio.Button value="medium">{t('performance.medium')}</Radio.Button>
                  <Radio.Button value="high">{t('performance.high')}</Radio.Button>
                </Radio.Group>
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('performance.postProcessing')}</div>
                <Switch
                  checked={currentConfig.postProcessing}
                  onChange={(checked) => handleCustomConfigChange('postProcessing', checked)}
                />
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('performance.antialiasing')}</div>
                <Switch
                  checked={currentConfig.antialiasing}
                  onChange={(checked) => handleCustomConfigChange('antialiasing', checked)}
                />
              </Col>
            </Row>
          </Panel>
          
          <Panel header={t('performance.optimizationSettings')} key="optimization">
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <div className="setting-label">{t('performance.maxVisibleDistance')}</div>
                <Slider
                  min={50}
                  max={1000}
                  step={50}
                  value={currentConfig.maxVisibleDistance}
                  onChange={(value) => handleCustomConfigChange('maxVisibleDistance', value)}
                  marks={{
                    50: '50m',
                    200: '200m',
                    500: '500m',
                    1000: '1000m'
                  }}
                />
              </Col>
              
              <Col span={24}>
                <div className="setting-label">{t('performance.lodDistanceScale')}</div>
                <Slider
                  min={0.25}
                  max={2}
                  step={0.25}
                  value={currentConfig.lodDistanceScale}
                  onChange={(value) => handleCustomConfigChange('lodDistanceScale', value)}
                  marks={{
                    0.25: '0.25x',
                    0.5: '0.5x',
                    1: '1x',
                    1.5: '1.5x',
                    2: '2x'
                  }}
                />
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('performance.enableInstancing')}</div>
                <Switch
                  checked={currentConfig.enableInstancing}
                  onChange={(checked) => handleCustomConfigChange('enableInstancing', checked)}
                />
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('performance.enableGPUCulling')}</div>
                <Switch
                  checked={currentConfig.enableGPUCulling}
                  onChange={(checked) => handleCustomConfigChange('enableGPUCulling', checked)}
                />
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('performance.enableTextureCompression')}</div>
                <Switch
                  checked={currentConfig.enableTextureCompression}
                  onChange={(checked) => handleCustomConfigChange('enableTextureCompression', checked)}
                />
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('performance.enableDynamicResolution')}</div>
                <Switch
                  checked={currentConfig.enableDynamicResolution}
                  onChange={(checked) => handleCustomConfigChange('enableDynamicResolution', checked)}
                />
              </Col>
            </Row>
          </Panel>
        </Collapse>
        
        <div className="custom-config-actions">
          <Button
            icon={<ReloadOutlined />}
            onClick={resetPerformanceConfig}
          >
            {t('performance.resetConfig')}
          </Button>
          <Button
            type="primary"
            onClick={() => setCustomizing(false)}
          >
            {t('performance.doneCustomizing')}
          </Button>
        </div>
      </div>
    );
  };
  
  return (
    <div className="mobile-performance-panel">
      <Card
        title={showTitle ? (
          <Space>
            <ThunderboltOutlined />
            {t('performance.title')}
          </Space>
        ) : undefined}
        extra={
          <Space>
            <Tooltip title={t('performance.customize')}>
              <Button
                icon={<SettingOutlined />}
                onClick={() => setCustomizing(!customizing)}
                type={customizing ? 'primary' : 'default'}
                size="small"
              />
            </Tooltip>
            <Tooltip title={t('performance.monitor')}>
              <Button
                icon={<LineChartOutlined />}
                onClick={() => {/* 打开性能监控面板 */}}
                size="small"
              />
            </Tooltip>
          </Space>
        }
      >
        <div className="device-info">
          <Space>
            {renderDeviceIcon()}
            <span>{MobileDeviceService.getDeviceInfo().type}</span>
          </Space>
        </div>
        
        {renderMonitorData()}
        
        <Divider />
        
        <div className="performance-level-selector">
          <div className="setting-label">{t('performance.performanceLevel')}</div>
          <Select
            value={performanceLevel}
            onChange={handlePerformanceLevelChange}
            style={{ width: '100%' }}
          >
            <Option value={PerformanceLevel.AUTO}>
              <Space>
                <ThunderboltOutlined />
                {t('performance.auto')}
                {renderPerformanceLevelTag(PerformanceLevel.AUTO)}
              </Space>
            </Option>
            <Option value={PerformanceLevel.LOW}>
              <Space>
                <BarsOutlined />
                {t('performance.low')}
                {renderPerformanceLevelTag(PerformanceLevel.LOW)}
              </Space>
            </Option>
            <Option value={PerformanceLevel.MEDIUM}>
              <Space>
                <MobileOutlined />
                {t('performance.medium')}
                {renderPerformanceLevelTag(PerformanceLevel.MEDIUM)}
              </Space>
            </Option>
            <Option value={PerformanceLevel.HIGH}>
              <Space>
                <TabletOutlined />
                {t('performance.high')}
                {renderPerformanceLevelTag(PerformanceLevel.HIGH)}
              </Space>
            </Option>
            <Option value={PerformanceLevel.ULTRA}>
              <Space>
                <DesktopOutlined />
                {t('performance.ultra')}
                {renderPerformanceLevelTag(PerformanceLevel.ULTRA)}
              </Space>
            </Option>
          </Select>
        </div>
        
        <Divider />
        
        <div className="optimization-settings">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <div className="setting-item">
                <div className="setting-label">
                  <ThunderboltOutlined /> {t('performance.autoOptimization')}
                </div>
                <Switch
                  checked={autoOptimization}
                  onChange={handleAutoOptimizationChange}
                />
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  <BarsOutlined /> {t('performance.batteryOptimization')}
                </div>
                <Switch
                  checked={batteryOptimization}
                  onChange={handleBatteryOptimizationChange}
                  disabled={!autoOptimization}
                />
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  <FireOutlined /> {t('performance.temperatureOptimization')}
                </div>
                <Switch
                  checked={temperatureOptimization}
                  onChange={handleTemperatureOptimizationChange}
                  disabled={!autoOptimization}
                />
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  <WifiOutlined /> {t('performance.networkOptimization')}
                </div>
                <Switch
                  checked={networkOptimization}
                  onChange={handleNetworkOptimizationChange}
                  disabled={!autoOptimization}
                />
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  <InfoCircleOutlined /> {t('performance.backgroundPause')}
                </div>
                <Switch
                  checked={backgroundPause}
                  onChange={handleBackgroundPauseChange}
                />
              </div>
            </Col>
          </Row>
        </div>
        
        {renderCustomConfigPanel()}
      </Card>
    </div>
  );
};

export default MobilePerformancePanel;
