/**
 * 角色控制器模块
 *
 * 提供角色控制器相关的类和接口
 */

// 导出高级角色控制器
export {
  AdvancedCharacterController,
  CharacterMovementMode,
  CharacterState
} from './AdvancedCharacterController';
export type { AdvancedCharacterControllerConfig } from './AdvancedCharacterController';

// 导出动作控制系统
export {
  ActionControlSystem,
  ActionType,
  ActionPriority
} from './ActionControlSystem';
export type {
  ActionControlSystemConfig,
  ActionData,
  ActionEvent,
  ActionInstance
} from './ActionControlSystem';

// 导出角色控制器预设管理器
export {
  CharacterControllerPresetManager,
  ControllerPresetType
} from './CharacterControllerPresetManager';
export type {
  CharacterControllerPresetManagerConfig,
  ControllerPresetData,
  ControllerTemplateData,
  ControllerTemplateParameter
} from './CharacterControllerPresetManager';

// 导出情感混合控制器
export { EmotionBlendController } from './EmotionBlendController';
export type {
  EmotionBlendControllerConfig,
  EmotionExpressionData
} from './EmotionBlendController';
