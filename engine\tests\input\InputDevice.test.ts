/**
 * 输入设备单元测试
 */
import { BaseInputDevice } from '../../src/input/InputDevice';

// 创建测试用的输入设备类
class TestInputDevice extends BaseInputDevice {
  constructor() {
    super('test');
  }
}

describe('InputDevice', () => {
  let device: TestInputDevice;

  beforeEach(() => {
    device = new TestInputDevice();
    device.initialize();
  });

  afterEach(() => {
    device.destroy();
  });

  test('应该正确初始化设备', () => {
    expect(device.getName()).toBe('test');
    expect(device['initialized']).toBe(true);
    expect(device['destroyed']).toBe(false);
  });

  test('应该正确设置和获取值', () => {
    device.setValue('key1', 'value1');
    device.setValue('key2', 123);
    device.setValue('key3', true);

    expect(device.getValue('key1')).toBe('value1');
    expect(device.getValue('key2')).toBe(123);
    expect(device.getValue('key3')).toBe(true);
    expect(device.getValue('key4')).toBeUndefined();
  });

  test('应该正确检查键是否存在', () => {
    device.setValue('key1', 'value1');

    expect(device.hasKey('key1')).toBe(true);
    expect(device.hasKey('key2')).toBe(false);
  });

  test('应该正确获取所有键', () => {
    device.setValue('key1', 'value1');
    device.setValue('key2', 123);
    device.setValue('key3', true);

    const keys = device.getKeys();
    expect(keys).toContain('key1');
    expect(keys).toContain('key2');
    expect(keys).toContain('key3');
    expect(keys.length).toBe(3);
  });

  test('应该正确触发值变化事件', () => {
    const mockCallback = jest.fn();
    device.on('key1Changed', mockCallback);

    device.setValue('key1', 'value1');
    expect(mockCallback).toHaveBeenCalledWith({ key: 'key1', value: 'value1', oldValue: undefined });

    device.setValue('key1', 'value2');
    expect(mockCallback).toHaveBeenCalledWith({ key: 'key1', value: 'value2', oldValue: 'value1' });

    // 值没有变化，不应该触发事件
    device.setValue('key1', 'value2');
    expect(mockCallback).toHaveBeenCalledTimes(2);
  });

  test('应该正确移除事件监听器', () => {
    const mockCallback = jest.fn();
    device.on('key1Changed', mockCallback);

    device.setValue('key1', 'value1');
    expect(mockCallback).toHaveBeenCalledTimes(1);

    device.off('key1Changed', mockCallback);
    device.setValue('key1', 'value2');
    expect(mockCallback).toHaveBeenCalledTimes(1);
  });

  test('应该正确销毁设备', () => {
    device.destroy();
    expect(device['destroyed']).toBe(true);

    // 销毁后不应该更新值
    device.setValue('key1', 'value1');
    expect(device.getValue('key1')).toBeUndefined();
  });
});
