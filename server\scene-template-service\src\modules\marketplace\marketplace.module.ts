import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SceneTemplate } from '../templates/entities/scene-template.entity';
import { TemplateRating } from '../ratings/entities/template-rating.entity';
import { TemplateShare } from '../sharing/entities/template-share.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SceneTemplate, TemplateRating, TemplateShare])],
  exports: [TypeOrmModule],
})
export class MarketplaceModule {}
