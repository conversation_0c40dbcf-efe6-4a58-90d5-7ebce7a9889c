/**
 * 湖泊水体组件
 * 用于模拟湖泊水体，包括波动、反射等特性
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyConfig } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
/**
 * 湖泊形状类型
 */
export declare enum LakeShapeType {
    /** 圆形 */
    CIRCLE = "circle",
    /** 椭圆形 */
    ELLIPSE = "ellipse",
    /** 不规则形状 */
    IRREGULAR = "irregular",
    /** 自定义多边形 */
    CUSTOM_POLYGON = "custom_polygon"
}
/**
 * 湖泊水体配置
 */
export interface LakeWaterConfig extends WaterBodyConfig {
    /** 湖泊形状类型 */
    shapeType?: LakeShapeType;
    /** 湖泊宽度 */
    width?: number;
    /** 湖泊长度 */
    length?: number;
    /** 湖泊深度 */
    depth?: number;
    /** 湖泊边缘点（用于不规则形状或自定义多边形） */
    edgePoints?: THREE.Vector2[];
    /** 湖泊分辨率（几何体细分数） */
    resolution?: number;
    /** 湖床高度变化 */
    bedHeightVariation?: number;
    /** 是否生成湖岸 */
    generateShore?: boolean;
    /** 湖岸高度 */
    shoreHeight?: number;
    /** 湖岸宽度 */
    shoreWidth?: number;
}
/**
 * 湖泊水体组件
 */
export declare class LakeWaterComponent extends WaterBodyComponent {
    /** 湖泊形状类型 */
    private shapeType;
    /** 湖泊宽度 */
    private width;
    /** 湖泊长度 */
    private length;
    /** 湖泊深度 */
    private depth;
    /** 湖泊边缘点 */
    private edgePoints;
    /** 湖泊分辨率 */
    private resolution;
    /** 湖床高度变化 */
    private bedHeightVariation;
    /** 是否生成湖岸 */
    private generateShore;
    /** 湖岸高度 */
    private shoreHeight;
    /** 湖岸宽度 */
    private shoreWidth;
    /** 湖泊几何体 */
    private lakeGeometry;
    /** 湖岸几何体 */
    private shoreGeometry;
    /** 湖床几何体 */
    private bedGeometry;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: LakeWaterConfig);
    /**
     * 检查粒子系统是否启用
     * @returns 是否启用粒子系统
     */
    protected isParticleSystemEnabled(): boolean;
    /**
     * 创建默认边缘点
     */
    private createDefaultEdgePoints;
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 创建湖泊几何体
     */
    private createLakeGeometry;
    /**
     * 创建圆形湖泊几何体
     */
    private createCircleLakeGeometry;
    /**
     * 创建椭圆形湖泊几何体
     */
    private createEllipseLakeGeometry;
    /**
     * 创建不规则湖泊几何体
     */
    private createIrregularLakeGeometry;
    /**
     * 创建湖岸几何体
     */
    private createShoreGeometry;
    /**
     * 创建湖床几何体
     */
    private createBedGeometry;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新湖泊波动效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateLakeWaves;
    /**
     * 设置湖泊形状类型
     * @param shapeType 形状类型
     */
    setShapeType(shapeType: LakeShapeType): void;
    /**
     * 设置湖泊尺寸
     * @param width 宽度
     * @param length 长度
     */
    setLakeSize(width: number, length?: number): void;
    /**
     * 重写父类的 setSize 方法
     * @param size 尺寸
     */
    setSize(size: {
        width: number;
        height: number;
        depth: number;
    }): void;
    /**
     * 设置湖泊深度
     * @param depth 深度
     */
    setDepth(depth: number): void;
    /**
     * 设置湖泊边缘点
     * @param points 边缘点
     */
    setEdgePoints(points: THREE.Vector2[]): void;
    /**
     * 设置湖岸参数
     * @param generateShore 是否生成湖岸
     * @param shoreHeight 湖岸高度
     * @param shoreWidth 湖岸宽度
     */
    setShoreParameters(generateShore: boolean, shoreHeight?: number, shoreWidth?: number): void;
    /**
     * 设置湖床高度变化
     * @param variation 高度变化
     */
    setBedHeightVariation(variation: number): void;
}
