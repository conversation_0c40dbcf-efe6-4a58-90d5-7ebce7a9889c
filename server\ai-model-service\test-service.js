/**
 * AI模型服务测试脚本
 */
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动AI模型服务测试...\n');

// 设置环境变量
process.env.NODE_ENV = 'development';
process.env.PORT = '3008';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '3306';
process.env.DB_USERNAME = 'root';
process.env.DB_PASSWORD = 'password';
process.env.DB_DATABASE = 'ai_model_service';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';

console.log('📋 环境配置:');
console.log(`- 端口: ${process.env.PORT}`);
console.log(`- 数据库: ${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_DATABASE}`);
console.log(`- Redis: ${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`);
console.log('');

// 启动服务
const service = spawn('npm', ['run', 'start:dev'], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
});

service.on('error', (error) => {
  console.error('❌ 服务启动失败:', error);
});

service.on('close', (code) => {
  console.log(`\n🔚 服务已停止，退出码: ${code}`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n⏹️  正在停止服务...');
  service.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n⏹️  正在停止服务...');
  service.kill('SIGTERM');
});

console.log('💡 提示:');
console.log('- 服务启动后访问: http://localhost:3008/api/docs');
console.log('- 按 Ctrl+C 停止服务');
console.log('- 确保 MySQL 和 Redis 服务正在运行');
console.log('');
