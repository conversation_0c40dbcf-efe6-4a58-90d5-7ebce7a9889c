/**
 * 后处理效果单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass';
import { SSAOPass } from 'three/examples/jsm/postprocessing/SSAOPass';
import { BokehPass } from 'three/examples/jsm/postprocessing/BokehPass';
import { PostProcessingSystem } from '../../src/rendering/postprocessing/PostProcessingSystem';
import { BloomEffect } from '../../src/rendering/postprocessing/effects/BloomEffect';
import { SSAOEffect } from '../../src/rendering/postprocessing/effects/SSAOEffect';
import { DepthOfFieldEffect } from '../../src/rendering/postprocessing/effects/DepthOfFieldEffect';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import { Camera, CameraType } from '../../src/rendering/Camera';
import { Transform } from '../../src/scene/Transform';
import { Scene } from '../../src/scene/Scene';

describe('PostProcessing', () => {
  let engine: Engine;
  let world: World;
  let renderSystem: RenderSystem;
  let postProcessingSystem: PostProcessingSystem;
  let scene: Scene;
  let cameraEntity: Entity;
  let camera: Camera;
  
  // 在每个测试前创建一个新的后处理系统实例
  beforeEach(() => {
    // 创建模拟的canvas元素
    const canvas = document.createElement('canvas');
    
    // 创建引擎和世界
    engine = new Engine({
      canvas,
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建渲染系统
    renderSystem = new RenderSystem({
      canvas,
      antialias: true,
      shadows: true,
      pixelRatio: 1
    });
    
    // 添加渲染系统到引擎
    engine.addSystem(renderSystem);
    
    // 创建后处理系统
    postProcessingSystem = new PostProcessingSystem({
      renderSystem,
      enabled: true
    });
    
    // 添加后处理系统到引擎
    engine.addSystem(postProcessingSystem);
    
    // 创建场景
    scene = new Scene('测试场景');
    world.setActiveScene(scene);
    
    // 创建相机实体
    cameraEntity = new Entity(world);
    cameraEntity.name = '相机';
    
    // 添加变换组件
    const cameraTransform = new Transform({
      position: { x: 0, y: 5, z: 10 },
      rotation: { x: -0.2, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    cameraEntity.addComponent(cameraTransform);
    
    // 创建相机组件
    camera = new Camera({
      type: CameraType.PERSPECTIVE,
      fov: 75,
      aspect: 1,
      near: 0.1,
      far: 1000
    });
    cameraEntity.addComponent(camera);
    
    // 添加相机实体到场景
    scene.addEntity(cameraEntity);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试后处理系统初始化
  it('应该正确初始化后处理系统', () => {
    expect(postProcessingSystem).toBeDefined();
    expect(postProcessingSystem['renderSystem']).toBe(renderSystem);
    expect(postProcessingSystem['composer']).toBeDefined();
    expect(postProcessingSystem['effects'].size).toBe(0);
    expect(postProcessingSystem.isEnabled()).toBe(true);
  });
  
  // 测试添加泛光效果
  it('应该能够添加泛光效果', () => {
    // 创建泛光效果
    const bloomEffect = new BloomEffect({
      strength: 1.5,
      radius: 0.4,
      threshold: 0.85
    });
    
    // 添加泛光效果
    postProcessingSystem.addEffect(bloomEffect);
    
    // 验证效果已添加
    expect(postProcessingSystem['effects'].has(bloomEffect.getName())).toBe(true);
    expect(postProcessingSystem['effects'].get(bloomEffect.getName())).toBe(bloomEffect);
    
    // 验证效果已添加到EffectComposer
    const pass = bloomEffect.getPass();
    expect(postProcessingSystem['composer']['passes']).toContain(pass);
  });
  
  // 测试添加SSAO效果
  it('应该能够添加SSAO效果', () => {
    // 创建SSAO效果
    const ssaoEffect = new SSAOEffect({
      radius: 4,
      minDistance: 0.005,
      maxDistance: 0.1,
      kernelSize: 32
    });
    
    // 添加SSAO效果
    postProcessingSystem.addEffect(ssaoEffect);
    
    // 验证效果已添加
    expect(postProcessingSystem['effects'].has(ssaoEffect.getName())).toBe(true);
    expect(postProcessingSystem['effects'].get(ssaoEffect.getName())).toBe(ssaoEffect);
    
    // 验证效果已添加到EffectComposer
    const pass = ssaoEffect.getPass();
    expect(postProcessingSystem['composer']['passes']).toContain(pass);
  });
  
  // 测试添加景深效果
  it('应该能够添加景深效果', () => {
    // 创建景深效果
    const depthOfFieldEffect = new DepthOfFieldEffect({
      focus: 10,
      aperture: 0.0002,
      maxBlur: 0.01
    });
    
    // 添加景深效果
    postProcessingSystem.addEffect(depthOfFieldEffect);
    
    // 验证效果已添加
    expect(postProcessingSystem['effects'].has(depthOfFieldEffect.getName())).toBe(true);
    expect(postProcessingSystem['effects'].get(depthOfFieldEffect.getName())).toBe(depthOfFieldEffect);
    
    // 验证效果已添加到EffectComposer
    const pass = depthOfFieldEffect.getPass();
    expect(postProcessingSystem['composer']['passes']).toContain(pass);
  });
  
  // 测试移除效果
  it('应该能够移除效果', () => {
    // 创建泛光效果
    const bloomEffect = new BloomEffect({
      strength: 1.5,
      radius: 0.4,
      threshold: 0.85
    });
    
    // 添加泛光效果
    postProcessingSystem.addEffect(bloomEffect);
    
    // 验证效果已添加
    expect(postProcessingSystem['effects'].has(bloomEffect.getName())).toBe(true);
    
    // 获取效果的Pass
    const pass = bloomEffect.getPass();
    
    // 移除效果
    postProcessingSystem.removeEffect(bloomEffect.getName());
    
    // 验证效果已移除
    expect(postProcessingSystem['effects'].has(bloomEffect.getName())).toBe(false);
    
    // 验证效果已从EffectComposer移除
    expect(postProcessingSystem['composer']['passes']).not.toContain(pass);
  });
  
  // 测试启用/禁用效果
  it('应该能够启用和禁用效果', () => {
    // 创建泛光效果
    const bloomEffect = new BloomEffect({
      strength: 1.5,
      radius: 0.4,
      threshold: 0.85
    });
    
    // 添加泛光效果
    postProcessingSystem.addEffect(bloomEffect);
    
    // 验证效果已启用
    expect(bloomEffect.isEnabled()).toBe(true);
    
    // 禁用效果
    postProcessingSystem.enableEffect(bloomEffect.getName(), false);
    
    // 验证效果已禁用
    expect(bloomEffect.isEnabled()).toBe(false);
    
    // 启用效果
    postProcessingSystem.enableEffect(bloomEffect.getName(), true);
    
    // 验证效果已启用
    expect(bloomEffect.isEnabled()).toBe(true);
  });
  
  // 测试启用/禁用后处理系统
  it('应该能够启用和禁用后处理系统', () => {
    // 验证后处理系统已启用
    expect(postProcessingSystem.isEnabled()).toBe(true);
    
    // 禁用后处理系统
    postProcessingSystem.setEnabled(false);
    
    // 验证后处理系统已禁用
    expect(postProcessingSystem.isEnabled()).toBe(false);
    
    // 启用后处理系统
    postProcessingSystem.setEnabled(true);
    
    // 验证后处理系统已启用
    expect(postProcessingSystem.isEnabled()).toBe(true);
  });
  
  // 测试后处理系统更新
  it('应该能够更新后处理系统', () => {
    // 创建composer.render方法的模拟
    const renderSpy = vi.spyOn(postProcessingSystem['composer'], 'render');
    
    // 更新后处理系统
    postProcessingSystem.update(0.016);
    
    // 验证composer.render方法被调用
    expect(renderSpy).toHaveBeenCalled();
  });
  
  // 测试后处理系统调整大小
  it('应该能够调整后处理系统大小', () => {
    // 创建composer.setSize方法的模拟
    const setSizeSpy = vi.spyOn(postProcessingSystem['composer'], 'setSize');
    
    // 调整后处理系统大小
    postProcessingSystem.resize(800, 600);
    
    // 验证composer.setSize方法被调用
    expect(setSizeSpy).toHaveBeenCalledWith(800, 600);
  });
});
