import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { KnowledgeDocument } from '../../entities/knowledge-document.entity';
import { CreateDocumentDto } from './dto/create-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { QueryDocumentDto } from './dto/query-document.dto';

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(KnowledgeDocument)
    private readonly documentRepository: Repository<KnowledgeDocument>,
  ) {}

  async uploadDocument(
    knowledgeBaseId: string,
    file: Express.Multer.File,
    createDocumentDto: CreateDocumentDto,
    userId: string,
  ): Promise<KnowledgeDocument> {
    const documentData = {
      ...createDocumentDto,
      knowledgeBaseId,
      uploadedBy: userId,
      filePath: file.path,
      fileType: file.mimetype,
      fileSize: file.size,
      originalFilename: file.originalname,
      filename: createDocumentDto.filename || file.originalname,
    };

    const document = this.documentRepository.create(documentData);
    return await this.documentRepository.save(document);
  }

  async create(
    knowledgeBaseId: string,
    createDocumentDto: CreateDocumentDto,
    userId: string,
  ): Promise<KnowledgeDocument> {
    const documentData = {
      ...createDocumentDto,
      knowledgeBaseId,
      uploadedBy: userId,
    };

    const document = this.documentRepository.create(documentData);
    return await this.documentRepository.save(document);
  }

  async findAll(
    knowledgeBaseId: string,
    queryDto: QueryDocumentDto,
    userId: string,
  ): Promise<KnowledgeDocument[]> {
    const queryBuilder = this.documentRepository.createQueryBuilder('document');

    queryBuilder.andWhere('document.knowledgeBaseId = :knowledgeBaseId', {
      knowledgeBaseId,
    });

    if (queryDto.processingStatus) {
      queryBuilder.andWhere('document.processingStatus = :processingStatus', {
        processingStatus: queryDto.processingStatus,
      });
    }

    if (queryDto.search) {
      queryBuilder.andWhere(
        '(document.filename ILIKE :search OR document.originalFilename ILIKE :search)',
        { search: `%${queryDto.search}%` },
      );
    }

    if (queryDto.fileType) {
      queryBuilder.andWhere('document.fileType = :fileType', {
        fileType: queryDto.fileType,
      });
    }

    return await queryBuilder.getMany();
  }

  async findOne(knowledgeBaseId: string, id: string, userId: string): Promise<KnowledgeDocument> {
    const document = await this.documentRepository.findOne({
      where: { id, knowledgeBaseId }
    });
    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }
    return document;
  }

  async update(
    knowledgeBaseId: string,
    id: string,
    updateDocumentDto: UpdateDocumentDto,
    userId: string,
  ): Promise<KnowledgeDocument> {
    const document = await this.findOne(knowledgeBaseId, id, userId);
    Object.assign(document, updateDocumentDto);
    return await this.documentRepository.save(document);
  }

  async remove(knowledgeBaseId: string, id: string, userId: string): Promise<void> {
    const document = await this.findOne(knowledgeBaseId, id, userId);
    await this.documentRepository.remove(document);
  }

  async reprocessDocument(
    knowledgeBaseId: string,
    id: string,
    userId: string,
  ): Promise<KnowledgeDocument> {
    const document = await this.findOne(knowledgeBaseId, id, userId);
    document.processingStatus = 'pending';
    return await this.documentRepository.save(document);
  }
}
