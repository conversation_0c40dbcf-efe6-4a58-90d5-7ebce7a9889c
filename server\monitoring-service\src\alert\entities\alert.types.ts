/**
 * 告警相关的枚举类型定义
 */

export enum AlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

export enum AlertStatus {
  ACTIVE = 'active',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
}

export enum AlertRuleType {
  THRESHOLD = 'threshold',
  ANOMALY = 'anomaly',
  ABSENCE = 'absence',
  RATE_OF_CHANGE = 'rate_of_change',
  COMPOSITE = 'composite',
}

export enum AlertRuleStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
}

export enum AlertRuleOperator {
  GT = '>',
  GTE = '>=',
  LT = '<',
  LTE = '<=',
  EQ = '==',
  NEQ = '!=',
}
