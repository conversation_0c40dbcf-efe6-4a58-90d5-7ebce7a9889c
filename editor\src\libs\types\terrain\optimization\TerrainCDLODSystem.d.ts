import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { TerrainComponent } from '../components/TerrainComponent';
import { TerrainCDLODOptions } from './TerrainCDLOD';
/**
 * 地形CDLOD系统事件类型
 */
export declare enum TerrainCDLODSystemEventType {
    /** 节点可见性变更 */
    NODE_VISIBILITY_CHANGED = "node_visibility_changed",
    /** 节点过渡因子变更 */
    NODE_MORPH_FACTOR_CHANGED = "node_morph_factor_changed",
    /** 四叉树更新 */
    QUADTREE_UPDATED = "quadtree_updated"
}
/**
 * 地形CDLOD系统
 */
export declare class TerrainCDLODSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** CDLOD实现 */
    private cdlod;
    /** 活跃相机 */
    private activeCamera;
    /** 活跃场景 */
    private activeScene;
    /** 事件发射器 */
    private eventEmitter;
    /** 地形实体映射 */
    private terrainEntities;
    /**
     * 创建地形CDLOD系统
     * @param options CDLOD选项
     */
    constructor(options?: TerrainCDLODOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 注册CDLOD事件
     */
    private registerCDLODEvents;
    /**
     * 查找活跃相机
     */
    private findActiveCamera;
    /**
     * 查找活跃场景
     */
    private findActiveScene;
    /**
     * 查找地形实体
     */
    private findTerrainEntities;
    /**
     * 添加地形实体
     * @param entity 实体
     * @param component 地形组件
     */
    addTerrainEntity(entity: Entity, component: TerrainComponent): void;
    /**
     * 移除地形实体
     * @param entity 实体
     */
    removeTerrainEntity(entity: Entity): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
