/**
 * HighlightEffectsExample.ts
 * 
 * 高亮效果示例，展示不同类型的高亮效果
 */

import { World, Entity, Scene, Transform, Camera } from '../../engine/src/core';
import { 
  InteractionSystem, 
  InteractableComponent, 
  InteractionType,
  InteractionEventComponent,
  InteractionEventType,
  InteractionPromptComponent,
  PromptPositionType,
  InteractionHighlightComponent,
  HighlightType
} from '../../engine/src/interaction';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { Debug } from '../../engine/src/utils/Debug';
import { Vector3, BoxGeometry, SphereGeometry, CylinderGeometry, MeshBasicMaterial, Mesh, Color } from 'three';

/**
 * 高亮效果示例类
 */
export class HighlightEffectsExample {
  /** 世界 */
  private world: World;

  /** 场景 */
  private scene: Scene;

  /** 输入系统 */
  private inputSystem: InputSystem;

  /** 交互系统 */
  private interactionSystem: InteractionSystem;

  /** 可交互对象列表 */
  private interactableObjects: Entity[] = [];

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 创建输入系统
    this.inputSystem = new InputSystem({
      enableKeyboard: true,
      enableMouse: true,
      enableTouch: true
    });

    // 添加输入系统到世界
    this.world.addSystem(this.inputSystem);

    // 创建交互系统
    this.interactionSystem = new InteractionSystem(this.world, {
      debug: true,
      maxInteractionDistance: 10,
      enableFrustumCheck: true,
      enableHighlight: true,
      enablePrompt: true
    });

    // 添加交互系统到世界
    this.world.addSystem(this.interactionSystem);

    // 创建场景
    this.scene = new Scene(this.world, {
      name: '高亮效果示例场景'
    });

    // 初始化场景
    this.initializeScene();

    // 创建可交互对象
    this.createInteractableObjects();

    // 启动游戏循环
    this.startGameLoop();
  }

  /**
   * 初始化场景
   */
  private initializeScene(): void {
    // 创建相机
    const camera = new Camera(this.world, {
      fov: 60,
      aspect: window.innerWidth / window.innerHeight,
      near: 0.1,
      far: 1000,
      position: new Vector3(0, 5, 10),
      lookAt: new Vector3(0, 0, 0)
    });

    // 设置为主相机
    this.world.setMainCamera(camera);

    // 添加相机到场景
    this.scene.addEntity(camera.entity);

    // 创建地面
    const ground = new Entity(this.world);
    ground.name = '地面';
    ground.addComponent(new Transform({
      position: new Vector3(0, -1, 0),
      scale: new Vector3(20, 0.1, 20)
    }));

    // 添加地面到场景
    this.scene.addEntity(ground);
  }

  /**
   * 创建可交互对象
   */
  private createInteractableObjects(): void {
    // 创建不同高亮效果的可交互对象
    this.createInteractableWithHighlight(
      new Vector3(-6, 0, 0),
      new BoxGeometry(2, 2, 2),
      new Color(0xff0000),
      '轮廓高亮立方体',
      HighlightType.OUTLINE
    );

    this.createInteractableWithHighlight(
      new Vector3(-2, 0, 0),
      new SphereGeometry(1, 32, 32),
      new Color(0x00ff00),
      '发光高亮球体',
      HighlightType.GLOW
    );

    this.createInteractableWithHighlight(
      new Vector3(2, 0, 0),
      new CylinderGeometry(1, 1, 2, 32),
      new Color(0x0000ff),
      '颜色高亮圆柱',
      HighlightType.COLOR
    );

    this.createInteractableWithHighlight(
      new Vector3(6, 0, 0),
      new BoxGeometry(2, 2, 2),
      new Color(0xffff00),
      '自定义高亮立方体',
      HighlightType.CUSTOM
    );
  }

  /**
   * 创建带有高亮效果的可交互对象
   * @param position 位置
   * @param geometry 几何体
   * @param color 颜色
   * @param label 标签
   * @param highlightType 高亮类型
   */
  private createInteractableWithHighlight(
    position: Vector3,
    geometry: BoxGeometry | SphereGeometry | CylinderGeometry,
    color: Color,
    label: string,
    highlightType: HighlightType
  ): void {
    // 创建实体
    const entity = new Entity(this.world);
    entity.name = label;

    // 添加变换组件
    entity.addComponent(new Transform({
      position,
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(1, 1, 1)
    }));

    // 创建几何体和材质
    const material = new MeshBasicMaterial({ color });
    const mesh = new Mesh(geometry, material);

    // 添加网格到实体（这里简化处理，实际应该使用渲染组件）
    (entity as any).mesh = mesh;

    // 添加可交互组件
    const interactable = new InteractableComponent(entity, {
      interactionType: InteractionType.CLICK,
      label,
      prompt: `按E键与${label}交互`,
      interactionDistance: 10,
      onInteract: (e) => {
        Debug.log('高亮效果示例', `与${label}交互`);
        // 在这里可以添加交互逻辑
      }
    });
    entity.addComponent(interactable);

    // 添加交互事件组件
    const interactionEvent = new InteractionEventComponent(entity);
    entity.addComponent(interactionEvent);

    // 监听交互事件
    interactionEvent.addEventListener(InteractionEventType.INTERACTION_START, (event) => {
      Debug.log('高亮效果示例', `开始与${label}交互`);
    });

    interactionEvent.addEventListener(InteractionEventType.INTERACTION_END, (event) => {
      Debug.log('高亮效果示例', `结束与${label}交互`);
    });

    // 添加交互提示组件
    const interactionPrompt = new InteractionPromptComponent(entity, {
      text: `按E键与${label}交互`,
      positionType: PromptPositionType.FOLLOW,
      offset: new Vector3(0, 2, 0),
      visible: false
    });
    entity.addComponent(interactionPrompt);

    // 添加交互高亮组件
    const interactionHighlight = new InteractionHighlightComponent(entity, {
      highlightType,
      highlightColor: color.clone().multiplyScalar(1.5),
      pulse: true,
      highlighted: false
    });
    entity.addComponent(interactionHighlight);

    // 将实体添加到场景
    this.scene.addEntity(entity);

    // 将实体添加到可交互对象列表
    this.interactableObjects.push(entity);

    // 注册组件到交互系统
    this.interactionSystem.registerInteractableComponent(entity, interactable);
    this.interactionSystem.registerInteractionEventComponent(entity, interactionEvent);
  }

  /**
   * 启动游戏循环
   */
  private startGameLoop(): void {
    // 上一帧时间
    let lastTime = 0;

    // 游戏循环函数
    const gameLoop = (time: number) => {
      // 计算时间增量（秒）
      const deltaTime = (time - lastTime) / 1000;
      lastTime = time;

      // 更新世界
      this.world.update(deltaTime);

      // 请求下一帧
      requestAnimationFrame(gameLoop);
    };

    // 开始游戏循环
    requestAnimationFrame(gameLoop);
  }
}

// 创建并运行示例
new HighlightEffectsExample();
