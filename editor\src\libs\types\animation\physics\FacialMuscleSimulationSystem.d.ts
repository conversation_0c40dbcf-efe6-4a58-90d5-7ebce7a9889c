/**
 * 面部肌肉模拟系统
 * 管理面部肌肉模拟组件
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { CannonPhysicsEngine } from './CannonPhysicsEngine';
import { FacialMuscleSimulationComponent, FacialMuscleSimulationConfig } from './FacialMuscleSimulation';
import { FacialExpressionType } from '../FacialAnimation';
/**
 * 面部肌肉模拟系统配置
 */
export interface FacialMuscleSimulationSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 重力 */
    gravity?: THREE.Vector3;
    /** 迭代次数 */
    iterations?: number;
    /** 是否使用软体 */
    useSoftBodies?: boolean;
}
/**
 * 面部肌肉模拟系统
 */
export declare class FacialMuscleSimulationSystem extends System {
    /** 系统类型 */
    static readonly type = "FacialMuscleSimulation";
    /** 面部肌肉模拟组件 */
    private components;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 物理引擎 */
    private physicsEngine;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<FacialMuscleSimulationSystemConfig>);
    /**
     * 创建面部肌肉模拟组件
     * @param entity 实体
     * @param config 配置
     * @returns 面部肌肉模拟组件
     */
    createFacialMuscleSimulation(entity: Entity, config?: Partial<FacialMuscleSimulationConfig>): FacialMuscleSimulationComponent;
    /**
     * 移除面部肌肉模拟组件
     * @param entity 实体
     */
    removeFacialMuscleSimulation(entity: Entity): void;
    /**
     * 获取面部肌肉模拟组件
     * @param entity 实体
     * @returns 面部肌肉模拟组件，如果不存在则返回null
     */
    getFacialMuscleSimulation(entity: Entity): FacialMuscleSimulationComponent | null;
    /**
     * 创建默认肌肉配置
     * @param entity 实体
     * @returns 是否成功创建
     */
    createDefaultMuscles(entity: Entity): boolean;
    /**
     * 应用表情
     * @param entity 实体
     * @param expression 表情类型
     * @param weight 权重
     * @returns 是否成功应用
     */
    applyExpression(entity: Entity, expression: FacialExpressionType, weight: number): boolean;
    /**
     * 获取物理引擎
     * @returns 物理引擎
     */
    getPhysicsEngine(): CannonPhysicsEngine;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
