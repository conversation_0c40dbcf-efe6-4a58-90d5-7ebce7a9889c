import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { NotificationChannelEntity } from '../entities/notification-channel.entity';
import { NotificationHistoryEntity } from '../entities/notification-history.entity';
import { AlertEntity, AlertSeverity } from '../../alert/entities/alert.entity';

@Injectable()
export class EmailNotifierService {
  private readonly logger = new Logger(EmailNotifierService.name);

  constructor(
    private readonly configService: ConfigService,
  ) {}

  /**
   * 发送邮件通知
   */
  async send(
    channel: NotificationChannelEntity,
    alert: AlertEntity,
    notification: NotificationHistoryEntity,
  ): Promise<void> {
    try {
      // 获取邮件配置
      const config = channel.config;
      
      if (!config.recipients || config.recipients.length === 0) {
        throw new Error('未配置收件人');
      }
      
      // 创建邮件传输器
      const transporter = this.createTransporter(config);
      
      // 构建邮件内容
      const mailOptions = {
        from: config.from || this.configService.get<string>('MAIL_FROM', '<EMAIL>'),
        to: config.recipients.join(','),
        subject: notification.subject,
        text: notification.content,
        html: this.formatHtmlContent(notification.content, alert),
      };
      
      // 发送邮件
      const info = await transporter.sendMail(mailOptions);
      
      this.logger.debug(`邮件已发送: ${info.messageId}`);
    } catch (error) {
      this.logger.error(`发送邮件通知失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 创建邮件传输器
   */
  private createTransporter(config: any): nodemailer.Transporter {
    // 使用配置中的SMTP设置，如果没有则使用环境变量
    const host = config.smtp?.host || this.configService.get<string>('MAIL_HOST');
    const port = config.smtp?.port || this.configService.get<number>('MAIL_PORT', 587);
    const secure = config.smtp?.secure || this.configService.get<boolean>('MAIL_SECURE', false);
    const user = config.smtp?.user || this.configService.get<string>('MAIL_USER');
    const pass = config.smtp?.pass || this.configService.get<string>('MAIL_PASS');
    
    if (!host || !user || !pass) {
      throw new Error('邮件配置不完整');
    }
    
    return nodemailer.createTransport({
      host,
      port,
      secure,
      auth: {
        user,
        pass,
      },
    });
  }

  /**
   * 格式化HTML内容
   */
  private formatHtmlContent(content: string, alert: AlertEntity): string {
    // 获取告警颜色
    const color = this.getSeverityColor(alert.severity);
    
    // 将纯文本转换为HTML
    const htmlContent = content
      .replace(/\n/g, '<br>')
      .replace(/告警:/g, '<strong>告警:</strong>')
      .replace(/严重性:/g, '<strong>严重性:</strong>')
      .replace(/状态:/g, '<strong>状态:</strong>')
      .replace(/时间:/g, '<strong>时间:</strong>')
      .replace(/描述:/g, '<strong>描述:</strong>')
      .replace(/服务类型:/g, '<strong>服务类型:</strong>')
      .replace(/主机:/g, '<strong>主机:</strong>')
      .replace(/标签:/g, '<strong>标签:</strong>')
      .replace(/值:/g, '<strong>值:</strong>');
    
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <div style="background-color: ${color}; color: white; padding: 10px; border-radius: 5px; margin-bottom: 20px;">
          <h2 style="margin: 0;">${alert.name}</h2>
        </div>
        <div style="line-height: 1.6;">
          ${htmlContent}
        </div>
        <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #777;">
          此邮件由监控系统自动发送，请勿回复。
        </div>
      </div>
    `;
  }

  /**
   * 获取严重性对应的颜色
   */
  private getSeverityColor(severity: AlertSeverity): string {
    switch (severity) {
      case AlertSeverity.INFO:
        return '#2196F3'; // 蓝色
      case AlertSeverity.WARNING:
        return '#FF9800'; // 橙色
      case AlertSeverity.ERROR:
        return '#F44336'; // 红色
      case AlertSeverity.CRITICAL:
        return '#9C27B0'; // 紫色
      default:
        return '#757575'; // 灰色
    }
  }
}
