/**
 * 网络模块类型定义文件
 */

import type { Entity } from '../core/Entity';

/**
 * 网络状态枚举
 */
export enum NetworkState {
  /** 未连接 */
  DISCONNECTED = 'disconnected',
  /** 正在连接 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 正在断开连接 */
  DISCONNECTING = 'disconnecting',
  /** 错误 */
  ERROR = 'error',
}

/**
 * WebRTC连接状态枚举
 */
export enum WebRTCConnectionState {
  /** 新建 */
  NEW = 'new',
  /** 正在连接 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 正在断开连接 */
  DISCONNECTING = 'disconnecting',
  /** 已断开连接 */
  DISCONNECTED = 'disconnected',
  /** 失败 */
  FAILED = 'failed',
  /** 关闭 */
  CLOSED = 'closed',
}

/**
 * 网络实体同步模式枚举
 */
export enum NetworkEntitySyncMode {
  /** 变换同步 */
  TRANSFORM = 'transform',
  /** 属性同步 */
  PROPERTIES = 'properties',
  /** 完全同步 */
  FULL = 'full',
  /** 自定义同步 */
  CUSTOM = 'custom',
}

/**
 * 用户角色枚举
 */
export enum UserRole {
  /** 访客 */
  GUEST = 'guest',
  /** 用户 */
  USER = 'user',
  /** 管理员 */
  ADMIN = 'admin',
  /** 超级管理员 */
  SUPER_ADMIN = 'super_admin',
}

/**
 * 用户权限枚举
 */
export enum UserPermission {
  /** 查看 */
  VIEW = 'view',
  /** 编辑 */
  EDIT = 'edit',
  /** 创建 */
  CREATE = 'create',
  /** 删除 */
  DELETE = 'delete',
  /** 管理用户 */
  MANAGE_USERS = 'manage_users',
  /** 管理权限 */
  MANAGE_PERMISSIONS = 'manage_permissions',
  /** 管理系统 */
  MANAGE_SYSTEM = 'manage_system',
}

/**
 * 带宽控制策略枚举
 */
export enum BandwidthControlStrategy {
  /** 固定 */
  FIXED = 'fixed',
  /** 自适应 */
  ADAPTIVE = 'adaptive',
  /** 优先级 */
  PRIORITY = 'priority',
}

/**
 * 数据优先级枚举
 */
export enum DataPriority {
  /** 最高 */
  HIGHEST = 0,
  /** 高 */
  HIGH = 1,
  /** 中 */
  MEDIUM = 2,
  /** 低 */
  LOW = 3,
  /** 最低 */
  LOWEST = 4,
}

/**
 * 网络质量级别枚举
 */
export enum NetworkQualityLevel {
  /** 未知 */
  UNKNOWN = 'unknown',
  /** 极好 */
  EXCELLENT = 'excellent',
  /** 良好 */
  GOOD = 'good',
  /** 中等 */
  MEDIUM = 'medium',
  /** 差 */
  BAD = 'bad',
  /** 极差 */
  POOR = 'poor',
}

/**
 * 同步区域类型枚举
 */
export enum SyncAreaType {
  /** 全局 */
  GLOBAL = 'global',
  /** 区域 */
  AREA = 'area',
  /** 网格 */
  GRID = 'grid',
  /** 自定义 */
  CUSTOM = 'custom',
}

/**
 * 媒体流类型枚举
 */
export enum MediaStreamType {
  /** 音频 */
  AUDIO = 'audio',
  /** 视频 */
  VIDEO = 'video',
  /** 屏幕共享 */
  SCREEN_SHARE = 'screen_share',
}

/**
 * 媒体流质量枚举
 */
export enum MediaStreamQuality {
  /** 低 */
  LOW = 'low',
  /** 中 */
  MEDIUM = 'medium',
  /** 高 */
  HIGH = 'high',
  /** 超高 */
  ULTRA = 'ultra',
}

/**
 * 压缩算法枚举
 */
export enum CompressionAlgorithm {
  /** 无压缩 */
  NONE = 'none',
  /** LZ字符串 */
  LZ_STRING = 'lz_string',
  /** MessagePack */
  MSGPACK = 'msgpack',
  /** 自定义 */
  CUSTOM = 'custom',
}

/**
 * 压缩级别枚举
 */
export enum CompressionLevel {
  /** 无压缩 */
  NONE = 'none',
  /** 低压缩 */
  LOW = 'low',
  /** 中压缩 */
  MEDIUM = 'medium',
  /** 高压缩 */
  HIGH = 'high',
}

/**
 * 网络用户接口
 */
export interface NetworkUser {
  /** 用户ID */
  userId: string;
  /** 用户名 */
  username: string;
  /** 角色 */
  role: UserRole;
  /** 是否在线 */
  isOnline: boolean;
  /** 最后活动时间 */
  lastActivityTime: number;
  /** 客户端信息 */
  clientInfo?: any;
}

/**
 * 网络实体接口
 */
export interface NetworkEntity {
  /** 实体ID */
  entityId: string;
  /** 所有者ID */
  ownerId: string;
  /** 实体类型 */
  type: string;
  /** 同步模式 */
  syncMode: NetworkEntitySyncMode;
  /** 同步间隔 */
  syncInterval: number;
  /** 同步优先级 */
  syncPriority: DataPriority;
  /** 实体引用 */
  entity: Entity;
}

/**
 * 网络事件接口
 */
export interface NetworkEvent {
  /** 事件类型 */
  type: string;
  /** 事件数据 */
  data: any;
  /** 发送者ID */
  senderId?: string;
  /** 接收者ID */
  receiverId?: string;
  /** 事件ID */
  id?: string;
  /** 时间戳 */
  timestamp?: number;
  /** 优先级 */
  priority?: DataPriority;
}

/**
 * 网络质量数据接口
 */
export interface NetworkQualityData {
  /** 往返时间（毫秒） */
  rtt: number;
  /** 丢包率（0-1） */
  packetLoss: number;
  /** 抖动（毫秒） */
  jitter: number;
  /** 带宽（字节/秒） */
  bandwidth: number;
  /** 质量级别 */
  level: NetworkQualityLevel;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 用户会话接口
 */
export interface UserSession {
  /** 用户ID */
  userId: string;
  /** 用户名 */
  username: string;
  /** 角色 */
  role: UserRole;
  /** 权限集合 */
  permissions: Set<UserPermission>;
  /** 连接时间 */
  connectionTime: number;
  /** 最后活动时间 */
  lastActivityTime: number;
  /** 是否在线 */
  isOnline: boolean;
  /** 是否已验证 */
  isAuthenticated: boolean;
  /** 会话令牌 */
  sessionToken?: string;
  /** 客户端信息 */
  clientInfo?: any;
}
