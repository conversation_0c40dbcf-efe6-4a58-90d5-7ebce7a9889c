/**
 * LOD系统性能测试示例
 * 用于测试LOD系统在不同场景复杂度下的性能
 */
import * as THREE from 'three';
import { Engine } from '../../../src/core/Engine';
import { World } from '../../../src/core/World';
import { Entity } from '../../../src/core/Entity';
import { Transform } from '../../../src/scene/Transform';
import { Camera } from '../../../src/rendering/Camera';
import { LODComponent, LODLevel } from '../../../src/rendering/optimization/LODComponent';
import { LODSystem } from '../../../src/rendering/optimization/LODSystem';
import { LODGenerator } from '../../../src/rendering/optimization/LODGenerator';
import { PerformanceMonitor } from '../../../src/utils/PerformanceMonitor';
import { Debug } from '../../../src/utils/Debug';

/**
 * LOD性能测试配置
 */
interface LODPerformanceTestConfig {
  /** 测试名称 */
  name: string;
  /** 测试描述 */
  description: string;
  /** 对象数量 */
  objectCount: number;
  /** 是否启用LOD */
  enableLOD: boolean;
  /** 是否使用八叉树 */
  useOctree: boolean;
  /** 是否使用自动LOD生成 */
  useAutoLODGeneration: boolean;
  /** 测试持续时间（毫秒） */
  duration: number;
  /** 相机移动速度 */
  cameraSpeed: number;
  /** 相机移动路径 */
  cameraPath: 'circle' | 'zigzag' | 'random';
}

/**
 * LOD性能测试结果
 */
interface LODPerformanceTestResult {
  /** 测试配置 */
  config: LODPerformanceTestConfig;
  /** 平均FPS */
  averageFPS: number;
  /** 最低FPS */
  minFPS: number;
  /** 最高FPS */
  maxFPS: number;
  /** 平均渲染时间 */
  averageRenderTime: number;
  /** 平均CPU使用率 */
  averageCPUUsage: number;
  /** 平均内存使用 */
  averageMemoryUsage: number;
  /** 可见对象数量 */
  visibleObjectCount: number;
  /** 剔除对象数量 */
  culledObjectCount: number;
  /** LOD级别统计 */
  lodLevelStats: {
    [key in LODLevel]: number;
  };
}

/**
 * LOD性能测试类
 */
export class LODPerformanceTest {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 相机 */
  private camera: Camera;
  /** LOD系统 */
  private lodSystem: LODSystem;
  /** 实体列表 */
  private entities: Entity[] = [];
  /** 测试配置 */
  private config: LODPerformanceTestConfig;
  /** 测试开始时间 */
  private startTime: number = 0;
  /** 测试结束时间 */
  private endTime: number = 0;
  /** 帧计数 */
  private frameCount: number = 0;
  /** FPS历史 */
  private fpsHistory: number[] = [];
  /** 渲染时间历史 */
  private renderTimeHistory: number[] = [];
  /** CPU使用率历史 */
  private cpuUsageHistory: number[] = [];
  /** 内存使用历史 */
  private memoryUsageHistory: number[] = [];
  /** 可见对象数量历史 */
  private visibleObjectCountHistory: number[] = [];
  /** 剔除对象数量历史 */
  private culledObjectCountHistory: number[] = [];
  /** LOD级别统计历史 */
  private lodLevelStatsHistory: { [key in LODLevel]: number[] } = {
    [LODLevel.HIGH]: [],
    [LODLevel.MEDIUM]: [],
    [LODLevel.LOW]: [],
    [LODLevel.VERY_LOW]: []
  };
  /** 是否正在运行 */
  private running: boolean = false;
  /** 结果回调 */
  private onResultCallback?: (result: LODPerformanceTestResult) => void;

  /**
   * 创建LOD性能测试
   * @param config 测试配置
   */
  constructor(config: LODPerformanceTestConfig) {
    this.config = config;

    // 创建引擎
    this.engine = new Engine();
    
    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);

    // 创建相机
    this.camera = new Camera();
    this.camera.setPosition(new THREE.Vector3(0, 10, 20));
    this.camera.lookAt(new THREE.Vector3(0, 0, 0));
    this.world.addSystem(this.camera);

    // 创建LOD系统
    this.lodSystem = new LODSystem({
      enabled: this.config.enableLOD,
      useOctree: this.config.useOctree,
      useAutoLODGeneration: this.config.useAutoLODGeneration
    });
    this.world.addSystem(this.lodSystem);

    // 设置LOD系统的活跃相机
    this.lodSystem.setActiveCamera(this.camera);
  }

  /**
   * 初始化测试
   */
  public initialize(): void {
    // 创建测试场景
    this.createTestScene();

    // 初始化性能监控
    PerformanceMonitor.start();
  }

  /**
   * 创建测试场景
   */
  private createTestScene(): void {
    // 创建地面
    const groundEntity = new Entity();
    const groundTransform = new Transform();
    groundEntity.addComponent(groundTransform);
    
    const groundGeometry = new THREE.PlaneGeometry(1000, 1000);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x888888 });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.rotation.x = -Math.PI / 2;
    groundMesh.receiveShadow = true;
    groundTransform.getObject3D().add(groundMesh);
    
    this.world.addEntity(groundEntity);

    // 创建测试对象
    const objectGeometry = new THREE.BoxGeometry(1, 1, 1, 32, 32, 32);
    const objectMaterial = new THREE.MeshStandardMaterial({ color: 0x0088ff });
    
    // 创建LOD生成器
    const lodGenerator = new LODGenerator();

    // 创建多个测试对象
    for (let i = 0; i < this.config.objectCount; i++) {
      // 创建实体
      const entity = new Entity();
      
      // 创建变换组件
      const transform = new Transform();
      entity.addComponent(transform);
      
      // 设置随机位置
      const x = (Math.random() - 0.5) * 100;
      const z = (Math.random() - 0.5) * 100;
      transform.setPosition(new THREE.Vector3(x, 0.5, z));
      
      // 创建网格
      const mesh = new THREE.Mesh(objectGeometry, objectMaterial.clone());
      mesh.castShadow = true;
      mesh.receiveShadow = true;
      transform.getObject3D().add(mesh);
      
      // 如果启用LOD，则创建LOD组件
      if (this.config.enableLOD) {
        // 如果使用自动LOD生成，则使用LOD生成器
        if (this.config.useAutoLODGeneration) {
          this.lodSystem.autoGenerateLOD(entity, mesh);
        } else {
          // 手动创建LOD组件
          const lodComponent = new LODComponent();
          
          // 添加高细节级别
          lodComponent.addLevel({
            level: LODLevel.HIGH,
            distance: 10,
            mesh: mesh,
            visible: true
          });
          
          // 添加中细节级别
          const mediumMesh = new THREE.Mesh(
            new THREE.BoxGeometry(1, 1, 1, 16, 16, 16),
            objectMaterial.clone()
          );
          mediumMesh.castShadow = true;
          mediumMesh.receiveShadow = true;
          lodComponent.addLevel({
            level: LODLevel.MEDIUM,
            distance: 30,
            mesh: mediumMesh,
            visible: false
          });
          
          // 添加低细节级别
          const lowMesh = new THREE.Mesh(
            new THREE.BoxGeometry(1, 1, 1, 8, 8, 8),
            objectMaterial.clone()
          );
          lowMesh.castShadow = true;
          lowMesh.receiveShadow = true;
          lodComponent.addLevel({
            level: LODLevel.LOW,
            distance: 60,
            mesh: lowMesh,
            visible: false
          });
          
          // 添加极低细节级别
          const veryLowMesh = new THREE.Mesh(
            new THREE.BoxGeometry(1, 1, 1, 4, 4, 4),
            objectMaterial.clone()
          );
          veryLowMesh.castShadow = true;
          veryLowMesh.receiveShadow = true;
          lodComponent.addLevel({
            level: LODLevel.VERY_LOW,
            distance: 100,
            mesh: veryLowMesh,
            visible: false
          });
          
          // 添加LOD组件到实体
          entity.addComponent(lodComponent);
          
          // 注册LOD组件
          this.lodSystem.registerLODComponent(entity, lodComponent);
        }
      }
      
      // 添加实体到世界
      this.world.addEntity(entity);
      
      // 添加到实体列表
      this.entities.push(entity);
    }
  }

  /**
   * 运行测试
   * @param onResult 结果回调
   */
  public run(onResult?: (result: LODPerformanceTestResult) => void): void {
    if (this.running) {
      return;
    }

    this.onResultCallback = onResult;
    this.running = true;
    this.startTime = performance.now();
    this.frameCount = 0;
    this.fpsHistory = [];
    this.renderTimeHistory = [];
    this.cpuUsageHistory = [];
    this.memoryUsageHistory = [];
    this.visibleObjectCountHistory = [];
    this.culledObjectCountHistory = [];
    this.lodLevelStatsHistory = {
      [LODLevel.HIGH]: [],
      [LODLevel.MEDIUM]: [],
      [LODLevel.LOW]: [],
      [LODLevel.VERY_LOW]: []
    };

    // 初始化测试
    this.initialize();

    // 启动引擎
    this.engine.start();

    // 设置更新回调
    this.engine.on('update', this.update.bind(this));

    Debug.log('LOD性能测试', `开始运行测试: ${this.config.name}`);
  }

  /**
   * 停止测试
   */
  public stop(): void {
    if (!this.running) {
      return;
    }

    this.running = false;
    this.endTime = performance.now();
    this.engine.off('update', this.update.bind(this));
    this.engine.stop();

    // 停止性能监控
    PerformanceMonitor.stop();

    // 生成测试结果
    const result = this.generateResult();

    // 调用结果回调
    if (this.onResultCallback) {
      this.onResultCallback(result);
    }

    Debug.log('LOD性能测试', `测试完成: ${this.config.name}`);
    Debug.log('LOD性能测试', `平均FPS: ${result.averageFPS.toFixed(2)}`);
    Debug.log('LOD性能测试', `平均渲染时间: ${result.averageRenderTime.toFixed(2)}ms`);
  }

  /**
   * 更新测试
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    if (!this.running) {
      return;
    }

    // 增加帧计数
    this.frameCount++;

    // 更新相机位置
    this.updateCameraPosition(deltaTime);

    // 收集性能数据
    this.collectPerformanceData();

    // 检查是否应该结束测试
    const currentTime = performance.now();
    if (currentTime - this.startTime >= this.config.duration) {
      this.stop();
    }
  }

  /**
   * 更新相机位置
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateCameraPosition(deltaTime: number): void {
    const time = performance.now() - this.startTime;
    const speed = this.config.cameraSpeed;
    
    switch (this.config.cameraPath) {
      case 'circle':
        // 圆形路径
        const angle = (time / 1000) * speed;
        const radius = 50;
        const x = Math.cos(angle) * radius;
        const z = Math.sin(angle) * radius;
        this.camera.setPosition(new THREE.Vector3(x, 10, z));
        this.camera.lookAt(new THREE.Vector3(0, 0, 0));
        break;
        
      case 'zigzag':
        // 之字形路径
        const zigzagX = Math.sin((time / 1000) * speed) * 50;
        const zigzagZ = ((time / 1000) * speed * 10) % 100 - 50;
        this.camera.setPosition(new THREE.Vector3(zigzagX, 10, zigzagZ));
        this.camera.lookAt(new THREE.Vector3(0, 0, 0));
        break;
        
      case 'random':
        // 随机路径
        if (Math.random() < 0.01) {
          const randomX = (Math.random() - 0.5) * 100;
          const randomZ = (Math.random() - 0.5) * 100;
          this.camera.setPosition(new THREE.Vector3(randomX, 10, randomZ));
          this.camera.lookAt(new THREE.Vector3(0, 0, 0));
        }
        break;
    }
  }

  /**
   * 收集性能数据
   */
  private collectPerformanceData(): void {
    // 获取性能数据
    const fps = this.engine.getFPS();
    const renderTime = this.engine.getLastFrameTime();
    const cpuUsage = PerformanceMonitor.getMetric(PerformanceMonitor.PerformanceMetricType.CPU_USAGE)?.value || 0;
    const memoryUsage = PerformanceMonitor.getMetric(PerformanceMonitor.PerformanceMetricType.MEMORY_USAGE)?.value || 0;
    
    // 记录性能数据
    this.fpsHistory.push(fps);
    this.renderTimeHistory.push(renderTime);
    this.cpuUsageHistory.push(cpuUsage);
    this.memoryUsageHistory.push(memoryUsage);
    
    // 统计可见对象和剔除对象
    let visibleCount = 0;
    let culledCount = 0;
    const lodLevelStats = {
      [LODLevel.HIGH]: 0,
      [LODLevel.MEDIUM]: 0,
      [LODLevel.LOW]: 0,
      [LODLevel.VERY_LOW]: 0
    };
    
    for (const entity of this.entities) {
      const lodComponent = entity.getComponent('LODComponent') as LODComponent;
      if (lodComponent) {
        // 检查是否可见
        const isVisible = lodComponent.getLevels().some(level => level.visible);
        if (isVisible) {
          visibleCount++;
          
          // 统计LOD级别
          const currentLevel = lodComponent.getCurrentLevel();
          if (currentLevel) {
            lodLevelStats[currentLevel]++;
          }
        } else {
          culledCount++;
        }
      }
    }
    
    // 记录对象统计
    this.visibleObjectCountHistory.push(visibleCount);
    this.culledObjectCountHistory.push(culledCount);
    
    // 记录LOD级别统计
    for (const level in lodLevelStats) {
      this.lodLevelStatsHistory[level as LODLevel].push(lodLevelStats[level as LODLevel]);
    }
  }

  /**
   * 生成测试结果
   * @returns 测试结果
   */
  private generateResult(): LODPerformanceTestResult {
    // 计算平均值
    const averageFPS = this.calculateAverage(this.fpsHistory);
    const minFPS = Math.min(...this.fpsHistory);
    const maxFPS = Math.max(...this.fpsHistory);
    const averageRenderTime = this.calculateAverage(this.renderTimeHistory);
    const averageCPUUsage = this.calculateAverage(this.cpuUsageHistory);
    const averageMemoryUsage = this.calculateAverage(this.memoryUsageHistory);
    const visibleObjectCount = Math.round(this.calculateAverage(this.visibleObjectCountHistory));
    const culledObjectCount = Math.round(this.calculateAverage(this.culledObjectCountHistory));
    
    // 计算LOD级别统计
    const lodLevelStats = {
      [LODLevel.HIGH]: Math.round(this.calculateAverage(this.lodLevelStatsHistory[LODLevel.HIGH])),
      [LODLevel.MEDIUM]: Math.round(this.calculateAverage(this.lodLevelStatsHistory[LODLevel.MEDIUM])),
      [LODLevel.LOW]: Math.round(this.calculateAverage(this.lodLevelStatsHistory[LODLevel.LOW])),
      [LODLevel.VERY_LOW]: Math.round(this.calculateAverage(this.lodLevelStatsHistory[LODLevel.VERY_LOW]))
    };
    
    // 返回结果
    return {
      config: this.config,
      averageFPS,
      minFPS,
      maxFPS,
      averageRenderTime,
      averageCPUUsage,
      averageMemoryUsage,
      visibleObjectCount,
      culledObjectCount,
      lodLevelStats
    };
  }

  /**
   * 计算平均值
   * @param array 数组
   * @returns 平均值
   */
  private calculateAverage(array: number[]): number {
    if (array.length === 0) {
      return 0;
    }
    const sum = array.reduce((a, b) => a + b, 0);
    return sum / array.length;
  }
}
