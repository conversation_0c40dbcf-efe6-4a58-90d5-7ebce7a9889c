import { <PERSON>tity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { LogLevel } from './log.entity';

@Entity('log_queries')
export class LogQueryEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('text')
  query: string;

  @Column('simple-array', { nullable: true })
  levels: LogLevel[];

  @Column({ nullable: true })
  @Index()
  serviceType: string;

  @Column('timestamp', { nullable: true })
  startTime: Date;

  @Column('timestamp', { nullable: true })
  endTime: Date;

  @Column('int', { default: 100 })
  limit: number;

  @Column('json', { nullable: true })
  filters: Record<string, any>;

  @Column('boolean', { default: false })
  isFavorite: boolean;

  @Column({ nullable: true })
  createdBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
