/**
 * 批处理器服务
 * 提供批处理功能，将多个请求合并为一个批处理请求
 */
import { Injectable, Logger } from '@nestjs/common';
import { BatchConfig, BatchStats } from './interfaces/batch-config.interface';

/**
 * 批处理请求
 */
interface BatchRequest<T, R> {
  /** 请求ID */
  id: string;
  /** 输入数据 */
  input: T;
  /** 选项 */
  options?: any;
  /** 解析函数 */
  resolve: (result: R) => void;
  /** 拒绝函数 */
  reject: (error: any) => void;
  /** 提交时间 */
  submitTime: number;
}

@Injectable()
export class BatchProcessorService<T, R> {
  private readonly logger = new Logger(BatchProcessorService.name);
  private readonly queue: BatchRequest<T, R>[] = [];
  private processingTimer: NodeJS.Timeout | null = null;
  private processingBatch = false;
  private requestCount = 0;
  private batchCount = 0;
  private totalBatchTime = 0;
  private totalWaitTime = 0;
  private queueFullCount = 0;
  private errorCount = 0;

  // 默认配置
  private readonly defaultConfig: Required<BatchConfig> = {
    enabled: true,
    maxBatchSize: 100,
    maxWaitTime: 50,
    maxQueueSize: 1000,
    enableStats: true,
    debug: false,
  };

  // 合并后的配置
  private readonly config: Required<BatchConfig>;

  // 批处理函数
  private readonly batchProcessor: (inputs: T[], options?: any[]) => Promise<R[]>;

  /**
   * 构造函数
   * @param batchProcessor 批处理函数
   * @param config 批处理配置
   */
  constructor(
    batchProcessor: (inputs: T[], options?: any[]) => Promise<R[]>,
    config?: Partial<BatchConfig>,
  ) {
    this.batchProcessor = batchProcessor;
    this.config = {
      ...this.defaultConfig,
      ...config,
    };
  }

  /**
   * 提交请求
   * @param input 输入数据
   * @param options 选项
   * @returns 处理结果
   */
  public async process(input: T, options?: any): Promise<R> {
    // 检查是否启用批处理
    if (!this.config.enabled) {
      // 直接处理单个请求
      return (await this.batchProcessor([input], options ? [options] : undefined))[0];
    }

    // 检查队列是否已满
    if (this.queue.length >= this.config.maxQueueSize) {
      if (this.config.enableStats) {
        this.queueFullCount++;
      }
      throw new Error('批处理队列已满');
    }

    // 创建请求
    return new Promise<R>((resolve, reject) => {
      const request: BatchRequest<T, R> = {
        id: `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        input,
        options,
        resolve,
        reject,
        submitTime: Date.now()
      };

      // 添加到队列
      this.queue.push(request);
      this.requestCount++;

      // 如果队列达到最大批处理大小，立即处理
      if (this.queue.length >= this.config.maxBatchSize) {
        this.processBatch();
      }
      // 否则，设置定时器
      else if (!this.processingTimer && !this.processingBatch) {
        this.processingTimer = setTimeout(() => {
          this.processBatch();
        }, this.config.maxWaitTime);
      }
    });
  }

  /**
   * 处理批次
   */
  private async processBatch(): Promise<void> {
    // 清除定时器
    if (this.processingTimer) {
      clearTimeout(this.processingTimer);
      this.processingTimer = null;
    }

    // 如果队列为空或正在处理批次，直接返回
    if (this.queue.length === 0 || this.processingBatch) {
      return;
    }

    // 设置处理标志
    this.processingBatch = true;

    // 获取批次
    const batchSize = Math.min(this.queue.length, this.config.maxBatchSize);
    const batch = this.queue.splice(0, batchSize);
    const inputs = batch.map(request => request.input);
    const options = batch.map(request => request.options);
    const now = Date.now();

    // 计算等待时间
    const waitTimes = batch.map(request => now - request.submitTime);
    const totalWaitTime = waitTimes.reduce((sum, time) => sum + time, 0);
    const avgWaitTime = totalWaitTime / batch.length;

    if (this.config.debug) {
      this.logger.debug(`处理批次: ${batch.length} 项, 平均等待时间: ${avgWaitTime.toFixed(2)}ms`);
    }

    try {
      // 处理批次
      const startTime = Date.now();
      const results = await this.batchProcessor(inputs, options);
      const batchTime = Date.now() - startTime;

      // 更新统计信息
      if (this.config.enableStats) {
        this.batchCount++;
        this.totalBatchTime += batchTime;
        this.totalWaitTime += totalWaitTime;
      }

      // 检查结果数量
      if (results.length !== batch.length) {
        throw new Error(`批处理结果数量不匹配: 预期 ${batch.length}, 实际 ${results.length}`);
      }

      // 分发结果
      for (let i = 0; i < batch.length; i++) {
        batch[i].resolve(results[i]);
      }

      if (this.config.debug) {
        this.logger.debug(`批次处理完成: ${batch.length} 项, 处理时间: ${batchTime}ms`);
      }
    } catch (error) {
      // 更新统计信息
      if (this.config.enableStats) {
        this.errorCount++;
      }

      this.logger.error(`批处理失败: ${error.message}`, error.stack);

      // 拒绝所有请求
      for (const request of batch) {
        request.reject(error);
      }
    } finally {
      // 清除处理标志
      this.processingBatch = false;

      // 如果队列中还有请求，继续处理
      if (this.queue.length > 0) {
        // 如果队列达到最大批处理大小，立即处理
        if (this.queue.length >= this.config.maxBatchSize) {
          this.processBatch();
        }
        // 否则，设置定时器
        else {
          this.processingTimer = setTimeout(() => {
            this.processBatch();
          }, this.config.maxWaitTime);
        }
      }
    }
  }

  /**
   * 获取批处理统计信息
   * @returns 批处理统计信息
   */
  public getStats(): BatchStats {
    return {
      batchCount: this.batchCount,
      itemCount: this.requestCount,
      avgBatchSize: this.batchCount > 0 ? this.requestCount / this.batchCount : 0,
      avgBatchTime: this.batchCount > 0 ? this.totalBatchTime / this.batchCount : 0,
      avgWaitTime: this.requestCount > 0 ? this.totalWaitTime / this.requestCount : 0,
      queueSize: this.queue.length,
      queueFullCount: this.queueFullCount,
      errorCount: this.errorCount,
    };
  }

  /**
   * 重置批处理统计信息
   */
  public resetStats(): void {
    this.requestCount = 0;
    this.batchCount = 0;
    this.totalBatchTime = 0;
    this.totalWaitTime = 0;
    this.queueFullCount = 0;
    this.errorCount = 0;
  }

  /**
   * 清空队列
   */
  public clearQueue(): void {
    // 拒绝所有请求
    for (const request of this.queue) {
      request.reject(new Error('批处理队列已清空'));
    }

    // 清空队列
    this.queue.length = 0;
  }
}
