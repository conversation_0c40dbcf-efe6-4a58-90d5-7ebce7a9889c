/**
 * 射线检测兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 射线检测兼容性测试
 */
export const raycastTest: TestCase = {
  name: '射线检测兼容性测试',
  description: '测试射线检测功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目物理系统实例
      const originalPhysicsSystem = new original.PhysicsSystem();
      originalPhysicsSystem.initialize();
      
      // 创建重构后项目物理系统实例
      const refactoredPhysicsSystem = new refactored.PhysicsSystem();
      refactoredPhysicsSystem.initialize();
      
      // 创建原有项目实体
      const originalEntity = new original.Entity('TestEntity');
      
      // 创建重构后项目实体
      const refactoredEntity = new refactored.Entity('TestEntity');
      
      // 创建原有项目刚体组件
      const originalBodyComponent = new original.PhysicsBodyComponent(originalEntity, {
        mass: 0,
        type: 'static'
      });
      
      // 创建重构后项目刚体组件
      const refactoredBodyComponent = new refactored.PhysicsBodyComponent(refactoredEntity, {
        mass: 0,
        type: 'static'
      });
      
      // 创建原有项目碰撞体组件
      const originalColliderComponent = new original.PhysicsColliderComponent(originalEntity, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      });
      
      // 创建重构后项目碰撞体组件
      const refactoredColliderComponent = new refactored.PhysicsColliderComponent(refactoredEntity, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      });
      
      // 添加组件到实体
      originalEntity.addComponent(originalBodyComponent);
      originalEntity.addComponent(originalColliderComponent);
      
      refactoredEntity.addComponent(refactoredBodyComponent);
      refactoredEntity.addComponent(refactoredColliderComponent);
      
      // 注册组件到物理系统
      originalPhysicsSystem.registerPhysicsBodyComponent(originalEntity, originalBodyComponent);
      originalPhysicsSystem.registerPhysicsColliderComponent(originalEntity, originalColliderComponent);
      
      refactoredPhysicsSystem.registerPhysicsBodyComponent(refactoredEntity, refactoredBodyComponent);
      refactoredPhysicsSystem.registerPhysicsColliderComponent(refactoredEntity, refactoredColliderComponent);
      
      // 设置实体位置
      originalBodyComponent.setPosition({ x: 0, y: 0, z: 0 });
      refactoredBodyComponent.setPosition({ x: 0, y: 0, z: 0 });
      
      // 更新物理系统
      originalPhysicsSystem.update(0.016);
      refactoredPhysicsSystem.update(0.016);
      
      // 执行射线检测
      const rayOrigin = { x: 0, y: 5, z: 0 };
      const rayDirection = { x: 0, y: -1, z: 0 };
      const maxDistance = 10;
      
      const originalResults = originalPhysicsSystem.raycast(rayOrigin, rayDirection, maxDistance);
      const refactoredResults = refactoredPhysicsSystem.raycast(rayOrigin, rayDirection, maxDistance);
      
      // 检查射线检测结果
      if (originalResults.length !== refactoredResults.length) {
        return {
          name: '射线检测兼容性测试',
          passed: false,
          errorMessage: `射线检测结果数量不一致: 原有项目=${originalResults.length}, 重构后项目=${refactoredResults.length}`,
          details: {
            originalResults,
            refactoredResults
          }
        };
      }
      
      if (originalResults.length === 0 && refactoredResults.length === 0) {
        return {
          name: '射线检测兼容性测试',
          passed: false,
          errorMessage: '两个系统都没有检测到射线碰撞',
          details: {
            originalResults,
            refactoredResults
          }
        };
      }
      
      // 检查射线检测结果的实体
      const originalHitEntity = originalResults[0].entity;
      const refactoredHitEntity = refactoredResults[0].entity;
      
      if (
        (originalHitEntity === originalEntity && refactoredHitEntity !== refactoredEntity) ||
        (originalHitEntity !== originalEntity && refactoredHitEntity === refactoredEntity)
      ) {
        return {
          name: '射线检测兼容性测试',
          passed: false,
          errorMessage: '射线检测结果的实体不一致',
          details: {
            originalHitEntity,
            refactoredHitEntity
          }
        };
      }
      
      // 检查射线检测结果的距离
      const originalDistance = originalResults[0].distance;
      const refactoredDistance = refactoredResults[0].distance;
      
      // 注意：由于物理引擎的实现差异，距离可能不完全相同，所以我们允许一定的误差
      if (Math.abs(originalDistance - refactoredDistance) > 0.5) {
        return {
          name: '射线检测兼容性测试',
          passed: false,
          errorMessage: `射线检测结果的距离相差过大: 原有项目=${originalDistance}, 重构后项目=${refactoredDistance}`,
          details: {
            originalDistance,
            refactoredDistance
          }
        };
      }
      
      // 检查射线检测结果的碰撞点
      const originalPoint = originalResults[0].point;
      const refactoredPoint = refactoredResults[0].point;
      
      // 注意：由于物理引擎的实现差异，碰撞点可能不完全相同，所以我们允许一定的误差
      if (
        Math.abs(originalPoint.x - refactoredPoint.x) > 0.5 ||
        Math.abs(originalPoint.y - refactoredPoint.y) > 0.5 ||
        Math.abs(originalPoint.z - refactoredPoint.z) > 0.5
      ) {
        return {
          name: '射线检测兼容性测试',
          passed: false,
          errorMessage: `射线检测结果的碰撞点相差过大: 原有项目=${JSON.stringify(originalPoint)}, 重构后项目=${JSON.stringify(refactoredPoint)}`,
          details: {
            originalPoint,
            refactoredPoint
          }
        };
      }
      
      // 销毁物理系统
      originalPhysicsSystem.destroy();
      refactoredPhysicsSystem.destroy();
      
      return {
        name: '射线检测兼容性测试',
        passed: true,
        details: {
          originalResults,
          refactoredResults
        }
      };
    } catch (error) {
      return {
        name: '射线检测兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
