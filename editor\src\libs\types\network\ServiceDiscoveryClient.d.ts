/**
 * 服务发现客户端
 * 负责与服务注册中心通信，发现和注册服务
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 服务发现客户端配置
 */
export interface ServiceDiscoveryClientConfig {
    /** 服务注册中心URL */
    registryUrl?: string;
    /** 心跳间隔（毫秒） */
    heartbeatInterval?: number;
    /** 服务发现缓存时间（毫秒） */
    discoveryCache?: number;
    /** 是否启用自动心跳 */
    enableAutoHeartbeat?: boolean;
    /** 是否启用服务发现缓存 */
    enableDiscoveryCache?: boolean;
    /** 重试次数 */
    retryCount?: number;
    /** 重试间隔（毫秒） */
    retryInterval?: number;
}
/**
 * 服务实例接口
 */
export interface ServiceInstance {
    /** 服务名称 */
    serviceName: string;
    /** 实例ID */
    instanceId: string;
    /** 主机名 */
    host: string;
    /** 端口 */
    port: number;
    /** 是否安全连接 */
    secure: boolean;
    /** 元数据 */
    metadata?: Record<string, any>;
    /** 健康状态 */
    status: 'UP' | 'DOWN' | 'UNKNOWN';
    /** 注册时间 */
    registrationTime: number;
    /** 最后心跳时间 */
    lastHeartbeatTime: number;
}
/**
 * 服务发现客户端
 * 负责与服务注册中心通信，发现和注册服务
 */
export declare class ServiceDiscoveryClient extends EventEmitter {
    /** 配置 */
    private config;
    /** 服务实例缓存 */
    private serviceCache;
    /** 已注册的服务实例 */
    private registeredInstances;
    /** 心跳定时器ID */
    private heartbeatTimerId;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建服务发现客户端
     * @param config 配置
     */
    constructor(config?: ServiceDiscoveryClientConfig);
    /**
     * 初始化客户端
     */
    initialize(): void;
    /**
     * 注册服务实例
     * @param serviceName 服务名称
     * @param host 主机名
     * @param port 端口
     * @param secure 是否安全连接
     * @param metadata 元数据
     * @returns 服务实例
     */
    registerService(serviceName: string, host: string, port: number, secure?: boolean, metadata?: Record<string, any>): Promise<ServiceInstance>;
    /**
     * 发送心跳
     * @param instanceId 实例ID
     */
    sendHeartbeat(instanceId: string): Promise<void>;
    /**
     * 发现服务实例
     * @param serviceName 服务名称
     * @returns 服务实例列表
     */
    discoverService(serviceName: string): Promise<ServiceInstance[]>;
    /**
     * 注销服务实例
     * @param instanceId 实例ID
     */
    deregisterService(instanceId: string): Promise<void>;
    /**
     * 启动心跳
     */
    private startHeartbeat;
    /**
     * 停止心跳
     */
    stopHeartbeat(): void;
    /**
     * 销毁客户端
     */
    destroy(): void;
}
