/**
 * 视觉脚本值类型注册表
 * 用于注册和管理值类型
 */
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 值类型创建函数
 */
export type ValueTypeCreator = () => any;

/**
 * 值类型验证函数
 */
export type ValueTypeValidator = (value: any) => boolean;

/**
 * 值类型转换函数
 */
export type ValueTypeConverter = (value: any) => any;

/**
 * 值类型信息
 */
export interface ValueTypeInfo {
  /** 值类型名称 */
  type: string;
  /** 值类型标签 */
  label?: string;
  /** 值类型描述 */
  description?: string;
  /** 值类型图标 */
  icon?: string;
  /** 值类型颜色 */
  color?: string;
  /** 默认值创建函数 */
  creator: ValueTypeCreator;
  /** 值类型验证函数 */
  validator: ValueTypeValidator;
  /** 值类型转换函数 */
  converter?: ValueTypeConverter;
  /** 是否为基本类型 */
  primitive?: boolean;
  /** 是否为复合类型 */
  composite?: boolean;
  /** 是否为自定义类型 */
  custom?: boolean;
  /** 是否已弃用 */
  deprecated?: boolean;
  /** 弃用原因 */
  deprecatedReason?: string;
  /** 是否实验性 */
  experimental?: boolean;
  /** 标签列表 */
  tags?: string[];
  /** 示例值 */
  examples?: any[];
  /** 文档链接 */
  documentationUrl?: string;
  /** 版本 */
  version?: string;
  /** 作者 */
  author?: string;
  /** 许可证 */
  license?: string;
  /** 依赖项 */
  dependencies?: string[];
  /** 自定义元数据 */
  [key: string]: any;
}

/**
 * 值类型注册表
 * 用于注册和管理值类型
 */
export class ValueTypeRegistry extends EventEmitter {
  /** 值类型映射 */
  private valueTypes: Map<string, ValueTypeInfo> = new Map();
  
  /** 值类型标签映射 */
  private tags: Map<string, Set<string>> = new Map();
  
  /**
   * 创建值类型注册表
   */
  constructor() {
    super();
    
    // 注册内置值类型
    this.registerBuiltinValueTypes();
  }
  
  /**
   * 注册内置值类型
   */
  private registerBuiltinValueTypes(): void {
    // 注册布尔类型
    this.registerValueType({
      type: 'boolean',
      label: '布尔值',
      description: '布尔值类型，表示真或假',
      icon: 'boolean',
      color: '#4CAF50',
      creator: () => false,
      validator: (value) => typeof value === 'boolean',
      converter: (value) => Boolean(value),
      primitive: true,
      tags: ['primitive', 'logic']
    });
    
    // 注册数字类型
    this.registerValueType({
      type: 'number',
      label: '数字',
      description: '数字类型，表示整数或浮点数',
      icon: 'number',
      color: '#2196F3',
      creator: () => 0,
      validator: (value) => typeof value === 'number' && !isNaN(value),
      converter: (value) => Number(value),
      primitive: true,
      tags: ['primitive', 'math']
    });
    
    // 注册整数类型
    this.registerValueType({
      type: 'integer',
      label: '整数',
      description: '整数类型，表示整数',
      icon: 'integer',
      color: '#1976D2',
      creator: () => 0,
      validator: (value) => typeof value === 'number' && !isNaN(value) && Number.isInteger(value),
      converter: (value) => Math.floor(Number(value)),
      primitive: true,
      tags: ['primitive', 'math']
    });
    
    // 注册字符串类型
    this.registerValueType({
      type: 'string',
      label: '字符串',
      description: '字符串类型，表示文本',
      icon: 'string',
      color: '#FF9800',
      creator: () => '',
      validator: (value) => typeof value === 'string',
      converter: (value) => String(value),
      primitive: true,
      tags: ['primitive', 'text']
    });
    
    // 注册数组类型
    this.registerValueType({
      type: 'array',
      label: '数组',
      description: '数组类型，表示一组值',
      icon: 'array',
      color: '#9C27B0',
      creator: () => [],
      validator: (value) => Array.isArray(value),
      converter: (value) => Array.isArray(value) ? value : [value],
      composite: true,
      tags: ['composite', 'collection']
    });
    
    // 注册对象类型
    this.registerValueType({
      type: 'object',
      label: '对象',
      description: '对象类型，表示键值对集合',
      icon: 'object',
      color: '#F44336',
      creator: () => ({}),
      validator: (value) => typeof value === 'object' && value !== null && !Array.isArray(value),
      converter: (value) => typeof value === 'object' && value !== null ? value : { value },
      composite: true,
      tags: ['composite', 'collection']
    });
    
    // 注册向量2类型
    this.registerValueType({
      type: 'vector2',
      label: '向量2',
      description: '二维向量类型，表示二维空间中的点或向量',
      icon: 'vector2',
      color: '#00BCD4',
      creator: () => ({ x: 0, y: 0 }),
      validator: (value) => 
        typeof value === 'object' && 
        value !== null && 
        'x' in value && 
        'y' in value && 
        typeof value.x === 'number' && 
        typeof value.y === 'number',
      converter: (value) => {
        if (typeof value === 'object' && value !== null) {
          return { 
            x: typeof value.x === 'number' ? value.x : 0, 
            y: typeof value.y === 'number' ? value.y : 0 
          };
        }
        return { x: 0, y: 0 };
      },
      composite: true,
      tags: ['composite', 'math', 'vector']
    });
    
    // 注册向量3类型
    this.registerValueType({
      type: 'vector3',
      label: '向量3',
      description: '三维向量类型，表示三维空间中的点或向量',
      icon: 'vector3',
      color: '#009688',
      creator: () => ({ x: 0, y: 0, z: 0 }),
      validator: (value) => 
        typeof value === 'object' && 
        value !== null && 
        'x' in value && 
        'y' in value && 
        'z' in value && 
        typeof value.x === 'number' && 
        typeof value.y === 'number' && 
        typeof value.z === 'number',
      converter: (value) => {
        if (typeof value === 'object' && value !== null) {
          return { 
            x: typeof value.x === 'number' ? value.x : 0, 
            y: typeof value.y === 'number' ? value.y : 0,
            z: typeof value.z === 'number' ? value.z : 0
          };
        }
        return { x: 0, y: 0, z: 0 };
      },
      composite: true,
      tags: ['composite', 'math', 'vector']
    });
    
    // 注册颜色类型
    this.registerValueType({
      type: 'color',
      label: '颜色',
      description: '颜色类型，表示RGBA颜色',
      icon: 'color',
      color: '#E91E63',
      creator: () => ({ r: 1, g: 1, b: 1, a: 1 }),
      validator: (value) => 
        typeof value === 'object' && 
        value !== null && 
        'r' in value && 
        'g' in value && 
        'b' in value && 
        typeof value.r === 'number' && 
        typeof value.g === 'number' && 
        typeof value.b === 'number',
      converter: (value) => {
        if (typeof value === 'object' && value !== null) {
          return { 
            r: typeof value.r === 'number' ? value.r : 1, 
            g: typeof value.g === 'number' ? value.g : 1,
            b: typeof value.b === 'number' ? value.b : 1,
            a: typeof value.a === 'number' ? value.a : 1
          };
        }
        return { r: 1, g: 1, b: 1, a: 1 };
      },
      composite: true,
      tags: ['composite', 'visual']
    });
  }
  
  /**
   * 注册值类型
   * @param info 值类型信息
   * @returns 是否注册成功
   */
  public registerValueType(info: ValueTypeInfo): boolean {
    // 检查是否已存在
    if (this.valueTypes.has(info.type)) {
      console.warn(`值类型已存在: ${info.type}`);
      return false;
    }
    
    // 注册值类型
    this.valueTypes.set(info.type, info);
    
    // 添加到标签映射
    if (info.tags) {
      for (const tag of info.tags) {
        const tagSet = this.tags.get(tag) || new Set();
        tagSet.add(info.type);
        this.tags.set(tag, tagSet);
      }
    }
    
    // 触发注册事件
    this.emit('valueTypeRegistered', info);
    
    return true;
  }
  
  /**
   * 注销值类型
   * @param type 值类型名称
   * @returns 是否注销成功
   */
  public unregisterValueType(type: string): boolean {
    // 检查是否存在
    const info = this.valueTypes.get(type);
    
    if (!info) {
      return false;
    }
    
    // 从标签映射中移除
    if (info.tags) {
      for (const tag of info.tags) {
        const tagSet = this.tags.get(tag);
        
        if (tagSet) {
          tagSet.delete(type);
          
          // 如果标签集合为空，移除标签
          if (tagSet.size === 0) {
            this.tags.delete(tag);
          }
        }
      }
    }
    
    // 从值类型映射中移除
    this.valueTypes.delete(type);
    
    // 触发注销事件
    this.emit('valueTypeUnregistered', info);
    
    return true;
  }
  
  /**
   * 获取值类型信息
   * @param type 值类型名称
   * @returns 值类型信息
   */
  public getValueTypeInfo(type: string): ValueTypeInfo | undefined {
    return this.valueTypes.get(type);
  }
  
  /**
   * 获取所有值类型
   * @returns 值类型信息列表
   */
  public getAllValueTypes(): ValueTypeInfo[] {
    return Array.from(this.valueTypes.values());
  }
  
  /**
   * 获取指定标签的值类型
   * @param tag 标签
   * @returns 值类型信息列表
   */
  public getValueTypesByTag(tag: string): ValueTypeInfo[] {
    const tagSet = this.tags.get(tag);
    
    if (!tagSet) {
      return [];
    }
    
    return Array.from(tagSet).map(type => this.valueTypes.get(type)!);
  }
  
  /**
   * 获取所有标签
   * @returns 标签列表
   */
  public getAllTags(): string[] {
    return Array.from(this.tags.keys());
  }
  
  /**
   * 创建值
   * @param type 值类型名称
   * @returns 创建的值
   */
  public createValue(type: string): any {
    const info = this.valueTypes.get(type);
    
    if (!info) {
      throw new Error(`未知值类型: ${type}`);
    }
    
    return info.creator();
  }
  
  /**
   * 验证值
   * @param type 值类型名称
   * @param value 要验证的值
   * @returns 是否有效
   */
  public validateValue(type: string, value: any): boolean {
    const info = this.valueTypes.get(type);
    
    if (!info) {
      throw new Error(`未知值类型: ${type}`);
    }
    
    return info.validator(value);
  }
  
  /**
   * 转换值
   * @param type 值类型名称
   * @param value 要转换的值
   * @returns 转换后的值
   */
  public convertValue(type: string, value: any): any {
    const info = this.valueTypes.get(type);
    
    if (!info) {
      throw new Error(`未知值类型: ${type}`);
    }
    
    if (info.converter) {
      return info.converter(value);
    }
    
    return value;
  }
  
  /**
   * 清空注册表
   */
  public clear(): void {
    // 清空值类型映射
    this.valueTypes.clear();
    
    // 清空标签映射
    this.tags.clear();
    
    // 重新注册内置值类型
    this.registerBuiltinValueTypes();
    
    // 触发清空事件
    this.emit('cleared');
  }
}
