/**
 * HTTP健康检查策略
 */
import { Injectable, Logger } from '@nestjs/common';
import { 
  HealthCheckStrategy, 
  HealthCheckConfig, 
  HealthCheckResult, 
  HealthCheckStatus,
  HttpHealthCheckConfig
} from './health-check.interface';
import fetch, { RequestInit, Response } from 'node-fetch';

@Injectable()
export class HttpHealthCheckStrategy implements HealthCheckStrategy {
  private readonly logger = new Logger(HttpHealthCheckStrategy.name);

  /**
   * 执行HTTP健康检查
   * @param config 健康检查配置
   */
  async check(config: HealthCheckConfig): Promise<HealthCheckResult> {
    const httpConfig = config as HttpHealthCheckConfig;
    const startTime = Date.now();
    
    try {
      // 设置请求选项
      const options: RequestInit = {
        method: httpConfig.method || 'GET',
        headers: httpConfig.headers || {},
        body: httpConfig.body,
        timeout: httpConfig.timeout,
      };
      
      // 发送请求
      const response = await fetch(httpConfig.target, options);
      const responseTime = Date.now() - startTime;
      
      // 检查状态码
      const isStatusValid = this.checkStatus(response, httpConfig);
      
      // 检查响应内容
      const isContentValid = await this.checkContent(response, httpConfig);
      
      // 判断健康状态
      const isHealthy = isStatusValid && isContentValid;
      
      const result: HealthCheckResult = {
        status: isHealthy ? HealthCheckStatus.HEALTHY : HealthCheckStatus.UNHEALTHY,
        details: isHealthy 
          ? `HTTP检查成功: ${response.status}` 
          : `HTTP检查失败: ${response.status}, 期望: ${httpConfig.expectedStatus?.join(', ')}`,
        timestamp: new Date(),
        responseTime,
        metadata: {
          statusCode: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
        },
      };
      
      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      this.logger.error(`HTTP健康检查失败: ${error.message}`, error.stack);
      
      return {
        status: HealthCheckStatus.UNHEALTHY,
        details: `HTTP检查异常: ${error.message}`,
        timestamp: new Date(),
        responseTime,
        metadata: {
          error: error.message,
        },
      };
    }
  }
  
  /**
   * 检查HTTP状态码
   * @param response HTTP响应
   * @param config 健康检查配置
   */
  private checkStatus(response: Response, config: HttpHealthCheckConfig): boolean {
    // 如果未指定期望的状态码，则默认2xx为健康
    const expectedStatus = config.expectedStatus || [200, 201, 202, 203, 204, 205, 206];
    
    return expectedStatus.includes(response.status);
  }
  
  /**
   * 检查HTTP响应内容
   * @param response HTTP响应
   * @param config 健康检查配置
   */
  private async checkContent(response: Response, config: HttpHealthCheckConfig): Promise<boolean> {
    // 如果未指定期望的响应内容，则不检查
    if (!config.expectedContent) {
      return true;
    }
    
    try {
      const text = await response.text();
      return text.includes(config.expectedContent);
    } catch (error) {
      this.logger.error(`检查HTTP响应内容失败: ${error.message}`, error.stack);
      return false;
    }
  }
}
