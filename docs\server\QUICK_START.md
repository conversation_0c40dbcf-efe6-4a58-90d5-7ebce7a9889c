# DL引擎服务端快速启动指南

本指南帮助您快速启动DL引擎的所有服务端组件。

## 🚀 一键启动（推荐）

### 前提条件
- Docker和Docker Compose已安装
- 系统内存至少4GB
- 磁盘空间至少10GB
- 端口3000-4010, 6379, 3306, 80未被占用

### 快速启动步骤

1. **克隆项目并进入目录**
```bash
git clone <repository-url>
cd newsystem
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件（可选，默认配置可直接使用）
# 建议修改以下关键配置：
# - MYSQL_ROOT_PASSWORD（数据库密码）
# - JWT_SECRET（JWT密钥）
```

3. **一键启动所有服务**
```bash
# 使用启动脚本（推荐）
./scripts/start-services.sh

# 或使用Docker Compose
docker-compose up -d
```

4. **等待服务启动完成**
启动脚本会自动检查服务健康状态，大约需要2-3分钟。

5. **验证服务状态**
```bash
# 检查所有服务状态
./scripts/check-services.sh

# 或查看容器状态
docker-compose ps
```

## 🌐 访问地址

启动完成后，您可以通过以下地址访问各个服务：

### 主要服务
- **前端编辑器**: http://localhost
- **API网关**: http://localhost:3000
- **API文档**: http://localhost:3000/api/docs

### 微服务健康检查
- **服务注册中心**: http://localhost:4010/health
- **用户服务**: http://localhost:4001/health
- **项目服务**: http://localhost:4002/health
- **资产服务**: http://localhost:4003/health
- **渲染服务**: http://localhost:4004/health
- **协作服务**: http://localhost:3007/health

### 监控服务（可选）
```bash
# 启动监控服务
docker-compose -f docker-compose.monitoring.yml up -d
```

- **Grafana监控**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Kibana日志**: http://localhost:5601

## 🔧 常用命令

### 服务管理
```bash
# 启动所有服务
./scripts/start-services.sh

# 启动服务并包含监控
./scripts/start-services.sh --with-monitoring

# 停止所有服务
./scripts/stop-services.sh

# 优雅停止服务
./scripts/stop-services.sh --graceful

# 强制停止并清理
./scripts/stop-services.sh --force --with-volumes

# 检查服务状态
./scripts/check-services.sh

# 检查特定服务
./scripts/check-services.sh services
```

### Docker Compose命令
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service-name]

# 重启特定服务
docker-compose restart [service-name]

# 停止所有服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs api-gateway
docker-compose logs user-service
docker-compose logs mysql

# 实时查看日志
docker-compose logs -f api-gateway

# 查看最近的日志
docker-compose logs --tail=100 api-gateway
```

## 🛠️ 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
netstat -tulpn | grep :3000

# Windows系统
netstat -ano | findstr :3000

# 停止占用端口的进程
kill -9 <PID>  # Linux/Mac
taskkill /PID <PID> /F  # Windows
```

#### 2. 服务启动失败
```bash
# 查看失败服务的日志
docker-compose logs [service-name]

# 重启失败的服务
docker-compose restart [service-name]

# 重新构建服务
docker-compose build [service-name]
docker-compose up -d [service-name]
```

#### 3. 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose logs mysql

# 进入MySQL容器检查
docker exec -it dl-engine-mysql mysql -u root -p

# 重启MySQL服务
docker-compose restart mysql
```

#### 4. 内存不足
```bash
# 检查系统资源
free -h  # Linux
docker stats  # Docker资源使用

# 清理Docker资源
docker system prune -a
docker volume prune
```

### 重置服务
如果遇到严重问题，可以完全重置：

```bash
# 停止所有服务
docker-compose down

# 删除所有容器和网络
docker-compose down --remove-orphans

# 删除数据卷（注意：会丢失数据）
docker-compose down -v

# 清理Docker资源
docker system prune -a

# 重新启动
docker-compose up -d
```

## 📋 启动检查清单

### 启动前检查
- [ ] Docker和Docker Compose已安装并运行
- [ ] 系统资源充足（内存4GB+，磁盘10GB+）
- [ ] 必要端口未被占用
- [ ] 环境变量文件已配置

### 启动后验证
- [ ] 所有容器状态为"Up"
- [ ] 数据库连接正常
- [ ] 服务注册中心健康检查通过
- [ ] API网关响应正常
- [ ] 前端页面可访问

### 功能测试
- [ ] 用户注册登录功能正常
- [ ] 项目创建和管理功能正常
- [ ] 文件上传功能正常
- [ ] 实时协作功能正常

## 🔄 开发模式

如果您需要进行开发调试，可以使用开发模式：

```bash
# 启动基础设施
docker-compose up -d mysql redis

# 手动启动各个微服务进行开发
cd server/service-registry && npm run start:dev
cd server/user-service && npm run start:dev
cd server/project-service && npm run start:dev
cd server/asset-service && npm run start:dev
cd server/render-service && npm run start:dev
cd server/collaboration-service && npm run start:dev
cd server/api-gateway && npm run start:dev

# 启动前端开发服务器
cd editor && npm run dev
```

## 📞 获取帮助

如果遇到问题：

1. 查看服务日志：`docker-compose logs [service-name]`
2. 检查服务状态：`./scripts/check-services.sh`
3. 查看详细文档：`docs/server/README.md`
4. 重置服务：按照上述重置步骤操作

## 🎯 下一步

服务启动成功后，您可以：

1. 访问前端编辑器开始使用：http://localhost
2. 查看API文档了解接口：http://localhost:3000/api/docs
3. 配置监控服务：启动监控组件并访问Grafana
4. 进行功能测试：创建项目、上传资产、测试协作功能

祝您使用愉快！🎉
