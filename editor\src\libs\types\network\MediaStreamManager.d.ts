/**
 * 媒体流管理器
 * 负责管理WebRTC媒体流的获取、处理和释放
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 媒体流类型
 */
export declare enum MediaStreamType {
    /** 音频 */
    AUDIO = "audio",
    /** 视频 */
    VIDEO = "video",
    /** 屏幕共享 */
    SCREEN_SHARE = "screen_share",
    /** 音频和视频 */
    AUDIO_VIDEO = "audio_video"
}
/**
 * 媒体流质量
 */
export declare enum MediaStreamQuality {
    /** 低质量 */
    LOW = "low",
    /** 中等质量 */
    MEDIUM = "medium",
    /** 高质量 */
    HIGH = "high",
    /** 超高质量 */
    ULTRA = "ultra"
}
/**
 * 媒体流配置
 */
export interface MediaStreamConfig {
    /** 音频约束 */
    audioConstraints?: MediaTrackConstraints;
    /** 视频约束 */
    videoConstraints?: MediaTrackConstraints;
    /** 屏幕共享约束 */
    screenShareConstraints?: MediaTrackConstraints;
    /** 是否启用回声消除 */
    echoCancellation?: boolean;
    /** 是否启用噪声抑制 */
    noiseSuppression?: boolean;
    /** 是否启用自动增益控制 */
    autoGainControl?: boolean;
    /** 是否启用立体声 */
    stereo?: boolean;
    /** 是否启用自动播放 */
    autoPlay?: boolean;
    /** 是否启用静音 */
    muted?: boolean;
    /** 是否启用视频镜像 */
    mirror?: boolean;
    /** 视频质量 */
    videoQuality?: MediaStreamQuality;
    /** 视频帧率 */
    frameRate?: number;
    /** 视频宽度 */
    width?: number;
    /** 视频高度 */
    height?: number;
    /** 视频比特率（bps） */
    videoBitrate?: number;
    /** 音频比特率（bps） */
    audioBitrate?: number;
}
/**
 * 媒体流信息
 */
export interface MediaStreamInfo {
    /** 流ID */
    id: string;
    /** 流类型 */
    type: MediaStreamType;
    /** 流质量 */
    quality: MediaStreamQuality;
    /** 媒体流 */
    stream: MediaStream;
    /** 音频轨道 */
    audioTrack?: MediaStreamTrack;
    /** 视频轨道 */
    videoTrack?: MediaStreamTrack;
    /** 是否启用 */
    enabled: boolean;
    /** 是否静音 */
    muted: boolean;
    /** 是否暂停 */
    paused: boolean;
    /** 创建时间 */
    createdAt: number;
    /** 配置 */
    config: MediaStreamConfig;
    /** 用户ID */
    userId?: string;
    /** 自定义数据 */
    customData?: Record<string, any>;
}
/**
 * 媒体流管理器配置
 */
export interface MediaStreamManagerConfig {
    /** 默认音频约束 */
    defaultAudioConstraints?: MediaTrackConstraints;
    /** 默认视频约束 */
    defaultVideoConstraints?: MediaTrackConstraints;
    /** 默认屏幕共享约束 */
    defaultScreenShareConstraints?: MediaTrackConstraints;
    /** 是否启用设备枚举 */
    enableDeviceEnumeration?: boolean;
    /** 是否启用设备变更检测 */
    enableDeviceChangeDetection?: boolean;
    /** 是否启用音频处理 */
    enableAudioProcessing?: boolean;
    /** 是否启用视频处理 */
    enableVideoProcessing?: boolean;
    /** 是否启用自动播放 */
    enableAutoPlay?: boolean;
    /** 是否启用音频电平监测 */
    enableAudioLevelMonitoring?: boolean;
    /** 音频电平监测间隔（毫秒） */
    audioLevelMonitoringInterval?: number;
}
/**
 * 媒体设备信息
 */
export interface MediaDeviceInfo {
    /** 设备ID */
    deviceId: string;
    /** 设备标签 */
    label: string;
    /** 设备类型 */
    kind: MediaDeviceKind;
    /** 设备分组ID */
    groupId: string;
}
/**
 * 媒体流管理器
 * 负责管理WebRTC媒体流的获取、处理和释放
 */
export declare class MediaStreamManager extends EventEmitter {
    /** 配置 */
    private config;
    /** 本地媒体流映射表 */
    private localStreams;
    /** 远程媒体流映射表 */
    private remoteStreams;
    /** 音频输入设备列表 */
    private audioInputDevices;
    /** 音频输出设备列表 */
    private audioOutputDevices;
    /** 视频输入设备列表 */
    private videoInputDevices;
    /** 当前音频输入设备ID */
    private currentAudioInputDeviceId;
    /** 当前音频输出设备ID */
    private currentAudioOutputDeviceId;
    /** 当前视频输入设备ID */
    private currentVideoInputDeviceId;
    /** 音频电平监测定时器ID */
    private audioLevelMonitoringTimerId;
    /** 音频电平分析器映射表 */
    private audioAnalysers;
    /** 音频上下文 */
    private audioContext;
    /**
     * 创建媒体流管理器
     * @param config 配置
     */
    constructor(config?: MediaStreamManagerConfig);
    /**
     * 初始化管理器
     */
    private initialize;
    /**
     * 枚举媒体设备
     */
    private enumerateDevices;
    /**
     * 处理设备变更事件
     */
    private handleDeviceChange;
    /**
     * 启动音频电平监测
     */
    private startAudioLevelMonitoring;
    /**
     * 停止音频电平监测
     */
    private stopAudioLevelMonitoring;
    /**
     * 更新音频电平
     */
    private updateAudioLevels;
    /**
     * 创建音频分析器
     * @param stream 媒体流
     * @returns 音频分析器
     */
    private createAudioAnalyser;
    /**
     * 获取本地媒体流
     * @param type 媒体流类型
     * @param config 媒体流配置
     * @returns 媒体流信息
     */
    getLocalStream(type: MediaStreamType, config?: MediaStreamConfig): Promise<MediaStreamInfo>;
    /**
     * 获取屏幕共享流
     * @param config 媒体流配置
     * @returns 媒体流信息
     */
    private getScreenShareStream;
    /**
     * 构建音频约束
     * @param config 媒体流配置
     * @returns 音频约束
     */
    private buildAudioConstraints;
    /**
     * 构建视频约束
     * @param config 媒体流配置
     * @returns 视频约束
     */
    private buildVideoConstraints;
    /**
     * 停止本地媒体流
     * @param streamId 流ID
     * @returns 是否成功停止
     */
    stopLocalStream(streamId: string): boolean;
    /**
     * 停止所有本地媒体流
     */
    stopAllLocalStreams(): void;
    /**
     * 添加远程媒体流
     * @param stream 媒体流
     * @param userId 用户ID
     * @param type 媒体流类型
     * @param config 媒体流配置
     * @returns 媒体流信息
     */
    addRemoteStream(stream: MediaStream, userId: string, type?: MediaStreamType, config?: MediaStreamConfig): MediaStreamInfo;
    /**
     * 移除远程媒体流
     * @param streamId 流ID
     * @returns 是否成功移除
     */
    removeRemoteStream(streamId: string): boolean;
    /**
     * 获取本地媒体流信息
     * @param streamId 流ID
     * @returns 媒体流信息
     */
    getLocalStreamInfo(streamId: string): MediaStreamInfo | undefined;
    /**
     * 获取远程媒体流信息
     * @param streamId 流ID
     * @returns 媒体流信息
     */
    getRemoteStreamInfo(streamId: string): MediaStreamInfo | undefined;
    /**
     * 获取所有本地媒体流
     * @returns 媒体流信息列表
     */
    getAllLocalStreams(): MediaStreamInfo[];
    /**
     * 获取所有远程媒体流
     * @returns 媒体流信息列表
     */
    getAllRemoteStreams(): MediaStreamInfo[];
    /**
     * 获取用户的远程媒体流
     * @param userId 用户ID
     * @returns 媒体流信息列表
     */
    getUserRemoteStreams(userId: string): MediaStreamInfo[];
    /**
     * 设置音频输入设备
     * @param deviceId 设备ID
     */
    setAudioInputDevice(deviceId: string): void;
    /**
     * 设置音频输出设备
     * @param deviceId 设备ID
     */
    setAudioOutputDevice(deviceId: string): void;
    /**
     * 设置视频输入设备
     * @param deviceId 设备ID
     */
    setVideoInputDevice(deviceId: string): void;
    /**
     * 获取音频输入设备列表
     * @returns 设备列表
     */
    getAudioInputDevices(): MediaDeviceInfo[];
    /**
     * 获取音频输出设备列表
     * @returns 设备列表
     */
    getAudioOutputDevices(): MediaDeviceInfo[];
    /**
     * 获取视频输入设备列表
     * @returns 设备列表
     */
    getVideoInputDevices(): MediaDeviceInfo[];
    /**
     * 销毁管理器
     */
    dispose(): void;
}
