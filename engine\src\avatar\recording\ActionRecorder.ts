/**
 * 动作录制器
 * 用于录制角色动作序列并保存为可回放的数据
 */
import type { Entity } from '../../core/Entity';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { ActionControlSystem, ActionData, ActionInstance } from '../controllers/ActionControlSystem';
import { AdvancedCharacterController } from '../controllers/AdvancedCharacterController';

/**
 * 录制的动作事件
 */
export interface RecordedActionEvent {
  /** 动作ID */
  actionId: string;
  /** 动作数据 */
  actionData: ActionData;
  /** 事件类型 */
  eventType: 'start' | 'stop';
  /** 时间戳 */
  timestamp: number;
  /** 参数 */
  params?: Record<string, any>;
}

/**
 * 录制的输入事件
 */
export interface RecordedInputEvent {
  /** 输入类型 */
  inputType: string;
  /** 输入值 */
  value: any;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 录制的变换事件
 */
export interface RecordedTransformEvent {
  /** 位置 */
  position: { x: number; y: number; z: number };
  /** 旋转 */
  rotation: { x: number; y: number; z: number; w: number };
  /** 时间戳 */
  timestamp: number;
}

/**
 * 动作录制数据
 */
export interface ActionRecording {
  /** 录制ID */
  id: string;
  /** 录制名称 */
  name: string;
  /** 开始时间戳 */
  startTimestamp: number;
  /** 结束时间戳 */
  endTimestamp: number;
  /** 动作事件 */
  actionEvents: RecordedActionEvent[];
  /** 输入事件 */
  inputEvents: RecordedInputEvent[];
  /** 变换事件 */
  transformEvents: RecordedTransformEvent[];
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 动作录制器配置
 */
export interface ActionRecorderConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否记录输入 */
  recordInput?: boolean;
  /** 是否记录变换 */
  recordTransform?: boolean;
  /** 变换记录频率（毫秒） */
  transformRecordFrequency?: number;
  /** 是否自动开始录制 */
  autoStart?: boolean;
}

/**
 * 动作录制器
 */
export class ActionRecorder {
  /** 实体 */
  private entity: Entity;
  /** 动作控制系统 */
  private actionControlSystem: ActionControlSystem;
  /** 角色控制器 */
  private characterController: AdvancedCharacterController | null = null;
  /** 配置 */
  private config: ActionRecorderConfig;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否正在录制 */
  private isRecording: boolean = false;
  /** 当前录制 */
  private currentRecording: ActionRecording | null = null;
  /** 变换记录定时器 */
  private transformRecordTimer: number | null = null;
  /** 动作开始事件处理器 */
  private actionStartHandler: ((data: { entity: Entity; action: ActionData }) => void) | null = null;
  /** 动作结束事件处理器 */
  private actionEndHandler: ((data: { entity: Entity; action: ActionData }) => void) | null = null;
  /** 输入事件处理器 */
  private inputHandler: ((data: any) => void) | null = null;

  /**
   * 构造函数
   * @param entity 实体
   * @param actionControlSystem 动作控制系统
   * @param config 配置
   * @param characterController 角色控制器（可选）
   */
  constructor(
    entity: Entity,
    actionControlSystem: ActionControlSystem,
    config: ActionRecorderConfig = {},
    characterController?: AdvancedCharacterController
  ) {
    this.entity = entity;
    this.actionControlSystem = actionControlSystem;
    this.config = {
      debug: false,
      recordInput: true,
      recordTransform: true,
      transformRecordFrequency: 100, // 默认100毫秒记录一次变换
      autoStart: false,
      ...config
    };

    // 设置角色控制器
    this.characterController = characterController || null;

    // 如果配置为自动开始，则开始录制
    if (this.config.autoStart) {
      this.startRecording();
    }
  }

  /**
   * 设置角色控制器
   * @param characterController 角色控制器
   */
  public setCharacterController(characterController: AdvancedCharacterController | null): void {
    this.characterController = characterController;
  }

  /**
   * 获取角色控制器
   * @returns 角色控制器
   */
  public getCharacterController(): AdvancedCharacterController | null {
    return this.characterController;
  }

  /**
   * 开始录制
   * @param name 录制名称
   * @returns 是否成功开始录制
   */
  public startRecording(name: string = '未命名录制'): boolean {
    if (this.isRecording) {
      if (this.config.debug) {
        Debug.warn('动作录制器已经在录制中');
      }
      return false;
    }

    this.isRecording = true;
    this.currentRecording = {
      id: `recording_${Date.now()}`,
      name,
      startTimestamp: Date.now(),
      endTimestamp: 0,
      actionEvents: [],
      inputEvents: [],
      transformEvents: []
    };

    // 添加事件监听器
    this.addEventListeners();

    // 开始记录变换
    if (this.config.recordTransform) {
      this.startRecordingTransform();
    }

    if (this.config.debug) {
      Debug.log(`开始录制动作: ${name}`);
    }

    // 触发录制开始事件
    this.eventEmitter.emit('recordingStart', this.currentRecording);

    return true;
  }

  /**
   * 停止录制
   * @returns 录制数据
   */
  public stopRecording(): ActionRecording | null {
    if (!this.isRecording || !this.currentRecording) {
      if (this.config.debug) {
        Debug.warn('没有正在进行的录制');
      }
      return null;
    }

    this.isRecording = false;
    this.currentRecording.endTimestamp = Date.now();

    // 移除事件监听器
    this.removeEventListeners();

    // 停止记录变换
    if (this.transformRecordTimer !== null) {
      clearInterval(this.transformRecordTimer);
      this.transformRecordTimer = null;
    }

    const recording = this.currentRecording;
    this.currentRecording = null;

    if (this.config.debug) {
      Debug.log('ActionRecorder', `停止录制动作: ${recording.name}`);
    }

    // 触发录制结束事件
    this.eventEmitter.emit('recordingEnd', recording);

    return recording;
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 监听动作开始事件
    this.actionStartHandler = (data: { entity: Entity; action: ActionData }) => {
      if (!this.isRecording || !this.currentRecording || data.entity !== this.entity) return;

      const event: RecordedActionEvent = {
        actionId: data.action.id,
        actionData: { ...data.action },
        eventType: 'start',
        timestamp: Date.now(),
        params: data.action.params
      };

      this.currentRecording.actionEvents.push(event);

      if (this.config.debug) {
        Debug.log(`记录动作开始: ${data.action.id}`);
      }
    };

    // 监听动作结束事件
    this.actionEndHandler = (data: { entity: Entity; action: ActionData }) => {
      if (!this.isRecording || !this.currentRecording || data.entity !== this.entity) return;

      const event: RecordedActionEvent = {
        actionId: data.action.id,
        actionData: { ...data.action },
        eventType: 'stop',
        timestamp: Date.now(),
        params: data.action.params
      };

      this.currentRecording.actionEvents.push(event);

      if (this.config.debug) {
        Debug.log(`记录动作结束: ${data.action.id}`);
      }
    };

    // 添加事件监听器
    this.actionControlSystem.on('actionStart', this.actionStartHandler);
    this.actionControlSystem.on('actionEnd', this.actionEndHandler);

    // 如果需要记录输入，添加输入事件监听器
    if (this.config.recordInput && this.characterController) {
      // 实现输入事件监听
    }
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    if (this.actionStartHandler) {
      this.actionControlSystem.off('actionStart', this.actionStartHandler);
      this.actionStartHandler = null;
    }

    if (this.actionEndHandler) {
      this.actionControlSystem.off('actionEnd', this.actionEndHandler);
      this.actionEndHandler = null;
    }

    if (this.inputHandler && this.characterController) {
      // 移除输入事件监听器
      this.inputHandler = null;
    }
  }

  /**
   * 开始记录变换
   */
  private startRecordingTransform(): void {
    if (this.transformRecordTimer !== null) {
      clearInterval(this.transformRecordTimer);
    }

    this.transformRecordTimer = window.setInterval(() => {
      this.recordTransform();
    }, this.config.transformRecordFrequency);
  }

  /**
   * 记录变换
   */
  private recordTransform(): void {
    if (!this.isRecording || !this.currentRecording) return;

    const transform = this.entity.getTransform();
    if (!transform) return;

    const position = transform.getPosition();
    const rotation = transform.getRotationQuaternion();

    const event: RecordedTransformEvent = {
      position: {
        x: position.x,
        y: position.y,
        z: position.z
      },
      rotation: {
        x: rotation.x,
        y: rotation.y,
        z: rotation.z,
        w: rotation.w
      },
      timestamp: Date.now()
    };

    this.currentRecording.transformEvents.push(event);
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public on(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public off(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 保存录制到文件
   * @param recording 录制数据
   * @param filename 文件名
   */
  public static saveToFile(recording: ActionRecording, filename: string = 'action-recording.json'): void {
    const json = JSON.stringify(recording, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 从文件加载录制
   * @param file 文件
   * @returns Promise<ActionRecording>
   */
  public static loadFromFile(file: File): Promise<ActionRecording> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const json = e.target?.result as string;
          const recording = JSON.parse(json) as ActionRecording;
          resolve(recording);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = (error) => reject(error);
      reader.readAsText(file);
    });
  }
}
