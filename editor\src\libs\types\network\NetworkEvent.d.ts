/**
 * 网络事件
 * 定义网络事件的结构和类型
 */
/**
 * 网络事件类型
 */
export declare enum NetworkEventType {
    /** 连接成功 */
    CONNECTED = "connected",
    /** 连接失败 */
    CONNECTION_FAILED = "connection_failed",
    /** 断开连接 */
    DISCONNECTED = "disconnected",
    /** 重新连接 */
    RECONNECTED = "reconnected",
    /** 重新连接失败 */
    RECONNECTION_FAILED = "reconnection_failed",
    /** 连接错误 */
    CONNECTION_ERROR = "connection_error",
    /** 加入房间 */
    ROOM_JOINED = "room_joined",
    /** 离开房间 */
    ROOM_LEFT = "room_left",
    /** 房间创建 */
    ROOM_CREATED = "room_created",
    /** 房间关闭 */
    ROOM_CLOSED = "room_closed",
    /** 房间更新 */
    ROOM_UPDATED = "room_updated",
    /** 用户加入 */
    USER_JOINED = "user_joined",
    /** 用户离开 */
    USER_LEFT = "user_left",
    /** 用户更新 */
    USER_UPDATED = "user_updated",
    /** 实体创建 */
    ENTITY_CREATED = "entity_created",
    /** 实体更新 */
    ENTITY_UPDATED = "entity_updated",
    /** 实体删除 */
    ENTITY_DELETED = "entity_deleted",
    /** 实体所有权变更 */
    ENTITY_OWNERSHIP_CHANGED = "entity_ownership_changed",
    /** 消息接收 */
    MESSAGE_RECEIVED = "message_received",
    /** 消息发送 */
    MESSAGE_SENT = "message_sent",
    /** 消息确认 */
    MESSAGE_ACKNOWLEDGED = "message_acknowledged",
    /** 消息错误 */
    MESSAGE_ERROR = "message_error",
    /** WebRTC连接成功 */
    WEBRTC_CONNECTED = "webrtc_connected",
    /** WebRTC连接失败 */
    WEBRTC_CONNECTION_FAILED = "webrtc_connection_failed",
    /** WebRTC断开连接 */
    WEBRTC_DISCONNECTED = "webrtc_disconnected",
    /** WebRTC连接错误 */
    WEBRTC_ERROR = "webrtc_error",
    /** WebRTC媒体流添加 */
    WEBRTC_STREAM_ADDED = "webrtc_stream_added",
    /** WebRTC媒体流移除 */
    WEBRTC_STREAM_REMOVED = "webrtc_stream_removed",
    /** 系统错误 */
    SYSTEM_ERROR = "system_error",
    /** 系统警告 */
    SYSTEM_WARNING = "system_warning",
    /** 系统信息 */
    SYSTEM_INFO = "system_info",
    /** 自定义事件 */
    CUSTOM = "custom"
}
/**
 * 网络事件
 */
export interface NetworkEvent {
    /** 事件类型 */
    type: NetworkEventType | string;
    /** 事件数据 */
    data?: any;
    /** 事件发送者ID */
    senderId?: string;
    /** 事件接收者ID */
    receiverId?: string;
    /** 事件时间戳 */
    timestamp: number;
    /** 事件ID */
    id?: string;
    /** 事件优先级 */
    priority?: number;
    /** 事件是否已处理 */
    handled?: boolean;
    /** 事件处理结果 */
    result?: any;
    /** 事件元数据 */
    metadata?: Record<string, any>;
}
