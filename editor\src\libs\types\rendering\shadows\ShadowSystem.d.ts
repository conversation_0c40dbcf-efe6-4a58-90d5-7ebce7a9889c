/**
 * 阴影系统
 * 管理场景中的阴影效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
/**
 * 阴影系统配置接口
 */
export interface ShadowSystemOptions {
    /** 是否启用阴影 */
    enabled?: boolean;
    /** 是否启用级联阴影映射 */
    useCSM?: boolean;
    /** 级联数量 */
    cascades?: number;
    /** 阴影贴图大小 */
    shadowMapSize?: number;
    /** 阴影偏移 */
    shadowBias?: number;
    /** 最大阴影距离 */
    maxShadowDistance?: number;
    /** 是否启用阴影淡入淡出 */
    fade?: boolean;
}
/**
 * 阴影组件接口
 */
export interface ShadowComponent {
    /** 是否投射阴影 */
    castShadow: boolean;
    /** 是否接收阴影 */
    receiveShadow: boolean;
}
/**
 * 阴影系统类
 */
export declare class ShadowSystem extends System {
    /** 系统类型 */
    static readonly type: string;
    /** 是否启用阴影 */
    private shadowEnabled;
    /** 是否启用级联阴影映射 */
    private useCSM;
    /** 级联阴影映射实例 */
    private csm;
    /** 级联数量 */
    private cascades;
    /** 阴影贴图大小 */
    private shadowMapSize;
    /** 阴影偏移 */
    private shadowBias;
    /** 最大阴影距离 */
    private maxShadowDistance;
    /** 是否启用阴影淡入淡出 */
    private fade;
    /** 活跃相机 */
    private activeCamera;
    /** 活跃场景 */
    private activeScene;
    /** 阴影实体列表 */
    private shadowEntities;
    /** 方向光实体列表 */
    private directionalLights;
    /**
     * 创建阴影系统
     * @param options 阴影系统配置
     */
    constructor(options?: ShadowSystemOptions);
    /**
     * 设置渲染器
     * @param renderer Three.js渲染器
     */
    setRenderer(renderer: THREE.WebGLRenderer): void;
    /**
     * 设置活跃相机
     * @param camera 相机
     */
    setActiveCamera(camera: Camera): void;
    /**
     * 设置活跃场景
     * @param scene 场景
     */
    setActiveScene(scene: Scene): void;
    /**
     * 添加阴影组件
     * @param entity 实体
     * @param component 阴影组件
     */
    addShadowComponent(entity: Entity, component: ShadowComponent): void;
    /**
     * 移除阴影组件
     * @param entity 实体
     */
    removeShadowComponent(entity: Entity): void;
    /**
     * 添加方向光
     * @param entity 实体
     */
    addDirectionalLight(entity: Entity): void;
    /**
     * 移除方向光
     * @param entity 实体
     */
    removeDirectionalLight(entity: Entity): void;
    /**
     * 更新CSM
     */
    private updateCSM;
    /**
     * 获取主方向光
     * @returns Three.js方向光
     */
    private getMainDirectionalLight;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
