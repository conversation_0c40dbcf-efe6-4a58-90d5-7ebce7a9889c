/**
 * 环境预设
 *
 * 提供一系列预定义的环境响应规则，可以快速应用到角色上。
 * 这些预设涵盖了常见的环境交互场景，如雨天、雪天、炎热天气等。
 */

import type { Entity } from '../../core/Entity';
import {
  EnvironmentType,
  WeatherType,
  EnvironmentAwarenessData
} from '../components/EnvironmentAwarenessComponent';
import {
  EnvironmentResponseRule,
  ResponseType,
  ResponsePriority,
  EnvironmentCondition,
  EnvironmentAction
} from '../components/EnvironmentResponseComponent';

/**
 * 创建雨天响应规则
 * @returns 响应规则
 */
export function createRainyWeatherResponse(): EnvironmentResponseRule {
  // 雨天条件
  const rainyCondition: EnvironmentCondition = {
    type: 'weather',
    params: { weatherType: WeatherType.RAINY },
    evaluate: (data: EnvironmentAwarenessData) => data.weatherType === WeatherType.RAINY
  };

  // 室外条件
  const outdoorCondition: EnvironmentCondition = {
    type: 'environment',
    params: { environmentType: EnvironmentType.OUTDOOR },
    evaluate: (data: EnvironmentAwarenessData) => data.environmentType === EnvironmentType.OUTDOOR
  };

  // 播放雨天动画动作
  const playRainyAnimation: EnvironmentAction = {
    type: ResponseType.ANIMATION,
    params: { animationName: 'character_rainy_idle', blendTime: 0.5 },
    execute: (entity: Entity) => {
      // 通过组件系统获取动画组件
      const animatorComponent = entity.getComponent('AnimatorComponent') as any;
      if (animatorComponent && (animatorComponent as any).playAnimation) {
        (animatorComponent as any).playAnimation('character_rainy_idle', { blendTime: 0.5, loop: true });
      }
    },
    stop: (entity: Entity) => {
      const animatorComponent = entity.getComponent('AnimatorComponent') as any;
      if (animatorComponent && (animatorComponent as any).stopAnimation) {
        (animatorComponent as any).stopAnimation('character_rainy_idle', { blendTime: 0.5 });
      }
    }
  };

  // 播放雨天音效动作
  const playRainySound: EnvironmentAction = {
    type: ResponseType.SOUND,
    params: { soundName: 'character_rain_reaction', volume: 0.8 },
    execute: (entity: Entity) => {
      // 通过组件系统获取音频组件
      const audioComponent = entity.getComponent('AudioSourceComponent') as any;
      if (audioComponent && (audioComponent as any).play) {
        (audioComponent as any).play('character_rain_reaction', { volume: 0.8, loop: false });
      }
    },
    stop: (entity: Entity) => {
      const audioComponent = entity.getComponent('AudioSourceComponent') as any;
      if (audioComponent && (audioComponent as any).stop) {
        (audioComponent as any).stop('character_rain_reaction');
      }
    }
  };

  // 创建雨天响应规则
  return {
    id: 'rainy_weather_response',
    name: '雨天响应',
    description: '角色在雨天的响应行为',
    responseType: ResponseType.ANIMATION,
    priority: ResponsePriority.MEDIUM,
    conditions: [rainyCondition, outdoorCondition],
    actions: [playRainyAnimation, playRainySound],
    cooldown: 10000, // 10秒冷却时间
    enabled: true
  };
}

/**
 * 创建雪天响应规则
 * @returns 响应规则
 */
export function createSnowyWeatherResponse(): EnvironmentResponseRule {
  // 雪天条件
  const snowyCondition: EnvironmentCondition = {
    type: 'weather',
    params: { weatherType: WeatherType.SNOWY },
    evaluate: (data: EnvironmentAwarenessData) => data.weatherType === WeatherType.SNOWY
  };

  // 室外条件
  const outdoorCondition: EnvironmentCondition = {
    type: 'environment',
    params: { environmentType: EnvironmentType.OUTDOOR },
    evaluate: (data: EnvironmentAwarenessData) => data.environmentType === EnvironmentType.OUTDOOR
  };

  // 低温条件
  const coldCondition: EnvironmentCondition = {
    type: 'temperature',
    params: { maxTemperature: 0 },
    evaluate: (data: EnvironmentAwarenessData) => data.temperature <= 0
  };

  // 播放雪天动画动作
  const playSnowyAnimation: EnvironmentAction = {
    type: ResponseType.ANIMATION,
    params: { animationName: 'character_cold_idle', blendTime: 0.5 },
    execute: (entity: Entity) => {
      const animatorComponent = entity.getComponent('AnimatorComponent') as any;
      if (animatorComponent && (animatorComponent as any).playAnimation) {
        (animatorComponent as any).playAnimation('character_cold_idle', { blendTime: 0.5, loop: true });
      }
    },
    stop: (entity: Entity) => {
      const animatorComponent = entity.getComponent('AnimatorComponent') as any;
      if (animatorComponent && (animatorComponent as any).stopAnimation) {
        (animatorComponent as any).stopAnimation('character_cold_idle', { blendTime: 0.5 });
      }
    }
  };

  // 播放雪天音效动作
  const playSnowySound: EnvironmentAction = {
    type: ResponseType.SOUND,
    params: { soundName: 'character_cold_reaction', volume: 0.8 },
    execute: (entity: Entity) => {
      const audioComponent = entity.getComponent('AudioSourceComponent') as any;
      if (audioComponent && (audioComponent as any).play) {
        (audioComponent as any).play('character_cold_reaction', { volume: 0.8, loop: false });
      }
    },
    stop: (entity: Entity) => {
      const audioComponent = entity.getComponent('AudioSourceComponent') as any;
      if (audioComponent && (audioComponent as any).stop) {
        (audioComponent as any).stop('character_cold_reaction');
      }
    }
  };

  // 创建雪天响应规则
  return {
    id: 'snowy_weather_response',
    name: '雪天响应',
    description: '角色在雪天的响应行为',
    responseType: ResponseType.ANIMATION,
    priority: ResponsePriority.MEDIUM,
    conditions: [snowyCondition, outdoorCondition, coldCondition],
    actions: [playSnowyAnimation, playSnowySound],
    cooldown: 15000, // 15秒冷却时间
    enabled: true
  };
}

/**
 * 创建炎热天气响应规则
 * @returns 响应规则
 */
export function createHotWeatherResponse(): EnvironmentResponseRule {
  // 高温条件
  const hotCondition: EnvironmentCondition = {
    type: 'temperature',
    params: { minTemperature: 30 },
    evaluate: (data: EnvironmentAwarenessData) => data.temperature >= 30
  };

  // 室外条件
  const outdoorCondition: EnvironmentCondition = {
    type: 'environment',
    params: { environmentType: EnvironmentType.OUTDOOR },
    evaluate: (data: EnvironmentAwarenessData) => data.environmentType === EnvironmentType.OUTDOOR
  };

  // 晴天条件
  const clearWeatherCondition: EnvironmentCondition = {
    type: 'weather',
    params: { weatherType: WeatherType.CLEAR },
    evaluate: (data: EnvironmentAwarenessData) => data.weatherType === WeatherType.CLEAR
  };

  // 播放炎热天气动画动作
  const playHotAnimation: EnvironmentAction = {
    type: ResponseType.ANIMATION,
    params: { animationName: 'character_hot_idle', blendTime: 0.5 },
    execute: (entity: Entity) => {
      const animatorComponent = entity.getComponent('AnimatorComponent') as any;
      if (animatorComponent && (animatorComponent as any).playAnimation) {
        (animatorComponent as any).playAnimation('character_hot_idle', { blendTime: 0.5, loop: true });
      }
    },
    stop: (entity: Entity) => {
      const animatorComponent = entity.getComponent('AnimatorComponent') as any;
      if (animatorComponent && (animatorComponent as any).stopAnimation) {
        (animatorComponent as any).stopAnimation('character_hot_idle', { blendTime: 0.5 });
      }
    }
  };

  // 播放炎热天气音效动作
  const playHotSound: EnvironmentAction = {
    type: ResponseType.SOUND,
    params: { soundName: 'character_hot_reaction', volume: 0.8 },
    execute: (entity: Entity) => {
      const audioComponent = entity.getComponent('AudioSourceComponent') as any;
      if (audioComponent && (audioComponent as any).play) {
        (audioComponent as any).play('character_hot_reaction', { volume: 0.8, loop: false });
      }
    },
    stop: (entity: Entity) => {
      const audioComponent = entity.getComponent('AudioSourceComponent') as any;
      if (audioComponent && (audioComponent as any).stop) {
        (audioComponent as any).stop('character_hot_reaction');
      }
    }
  };

  // 创建炎热天气响应规则
  return {
    id: 'hot_weather_response',
    name: '炎热天气响应',
    description: '角色在炎热天气的响应行为',
    responseType: ResponseType.ANIMATION,
    priority: ResponsePriority.MEDIUM,
    conditions: [hotCondition, outdoorCondition, clearWeatherCondition],
    actions: [playHotAnimation, playHotSound],
    cooldown: 20000, // 20秒冷却时间
    enabled: true
  };
}

/**
 * 创建黑暗环境响应规则
 * @returns 响应规则
 */
export function createDarkEnvironmentResponse(): EnvironmentResponseRule {
  // 低光照条件
  const lowLightCondition: EnvironmentCondition = {
    type: 'light',
    params: { maxIntensity: 0.3 },
    evaluate: (data: EnvironmentAwarenessData) => data.lightIntensity <= 0.3
  };

  // 夜晚条件
  const nightCondition: EnvironmentCondition = {
    type: 'timeOfDay',
    params: { minTime: 19, maxTime: 5 },
    evaluate: (data: EnvironmentAwarenessData) => data.timeOfDay >= 19 || data.timeOfDay <= 5
  };

  // 播放黑暗环境动画动作
  const playDarkAnimation: EnvironmentAction = {
    type: ResponseType.ANIMATION,
    params: { animationName: 'character_dark_idle', blendTime: 0.5 },
    execute: (entity: Entity) => {
      const animatorComponent = entity.getComponent('AnimatorComponent') as any;
      if (animatorComponent && (animatorComponent as any).playAnimation) {
        (animatorComponent as any).playAnimation('character_dark_idle', { blendTime: 0.5, loop: true });
      }
    },
    stop: (entity: Entity) => {
      const animatorComponent = entity.getComponent('AnimatorComponent') as any;
      if (animatorComponent && (animatorComponent as any).stopAnimation) {
        (animatorComponent as any).stopAnimation('character_dark_idle', { blendTime: 0.5 });
      }
    }
  };

  // 播放黑暗环境音效动作
  const playDarkSound: EnvironmentAction = {
    type: ResponseType.SOUND,
    params: { soundName: 'character_dark_reaction', volume: 0.8 },
    execute: (entity: Entity) => {
      const audioComponent = entity.getComponent('AudioSourceComponent') as any;
      if (audioComponent && (audioComponent as any).play) {
        (audioComponent as any).play('character_dark_reaction', { volume: 0.8, loop: false });
      }
    },
    stop: (entity: Entity) => {
      const audioComponent = entity.getComponent('AudioSourceComponent') as any;
      if (audioComponent && (audioComponent as any).stop) {
        (audioComponent as any).stop('character_dark_reaction');
      }
    }
  };

  // 创建黑暗环境响应规则
  return {
    id: 'dark_environment_response',
    name: '黑暗环境响应',
    description: '角色在黑暗环境的响应行为',
    responseType: ResponseType.ANIMATION,
    priority: ResponsePriority.HIGH,
    conditions: [lowLightCondition, nightCondition],
    actions: [playDarkAnimation, playDarkSound],
    cooldown: 30000, // 30秒冷却时间
    enabled: true
  };
}

/**
 * 创建水中环境响应规则
 * @returns 响应规则
 */
export function createUnderwaterResponse(): EnvironmentResponseRule {
  // 水下环境条件
  const underwaterCondition: EnvironmentCondition = {
    type: 'environment',
    params: { environmentType: EnvironmentType.UNDERWATER },
    evaluate: (data: EnvironmentAwarenessData) => data.environmentType === EnvironmentType.UNDERWATER
  };

  // 水位条件
  const waterLevelCondition: EnvironmentCondition = {
    type: 'waterLevel',
    params: { minWaterLevel: 1.0 },
    evaluate: (data: EnvironmentAwarenessData) => data.waterLevel >= 1.0
  };

  // 播放水下动画动作
  const playUnderwaterAnimation: EnvironmentAction = {
    type: ResponseType.ANIMATION,
    params: { animationName: 'character_swim', blendTime: 0.5 },
    execute: (entity: Entity) => {
      const animatorComponent = entity.getComponent('AnimatorComponent') as any;
      if (animatorComponent && (animatorComponent as any).playAnimation) {
        (animatorComponent as any).playAnimation('character_swim', { blendTime: 0.5, loop: true });
      }
    },
    stop: (entity: Entity) => {
      const animatorComponent = entity.getComponent('AnimatorComponent') as any;
      if (animatorComponent && (animatorComponent as any).stopAnimation) {
        (animatorComponent as any).stopAnimation('character_swim', { blendTime: 0.5 });
      }
    }
  };

  // 播放水下音效动作
  const playUnderwaterSound: EnvironmentAction = {
    type: ResponseType.SOUND,
    params: { soundName: 'character_underwater', volume: 0.8 },
    execute: (entity: Entity) => {
      const audioComponent = entity.getComponent('AudioSourceComponent') as any;
      if (audioComponent && (audioComponent as any).play) {
        (audioComponent as any).play('character_underwater', { volume: 0.8, loop: true });
      }
    },
    stop: (entity: Entity) => {
      const audioComponent = entity.getComponent('AudioSourceComponent') as any;
      if (audioComponent && (audioComponent as any).stop) {
        (audioComponent as any).stop('character_underwater');
      }
    }
  };

  // 创建水下环境响应规则
  return {
    id: 'underwater_response',
    name: '水下环境响应',
    description: '角色在水下环境的响应行为',
    responseType: ResponseType.ANIMATION,
    priority: ResponsePriority.CRITICAL,
    conditions: [underwaterCondition, waterLevelCondition],
    actions: [playUnderwaterAnimation, playUnderwaterSound],
    cooldown: 0, // 无冷却时间，立即响应
    enabled: true
  };
}

/**
 * 获取所有预设响应规则
 * @returns 响应规则数组
 */
export function getAllEnvironmentPresets(): EnvironmentResponseRule[] {
  return [
    createRainyWeatherResponse(),
    createSnowyWeatherResponse(),
    createHotWeatherResponse(),
    createDarkEnvironmentResponse(),
    createUnderwaterResponse()
  ];
}
