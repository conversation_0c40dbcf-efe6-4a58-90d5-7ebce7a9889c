/**
 * 带宽控制器
 * 负责管理网络带宽使用
 */
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkQualityData } from './NetworkQualityMonitor';
/**
 * 带宽控制策略
 */
export declare enum BandwidthControlStrategy {
    /** 固定带宽 */
    FIXED = "fixed",
    /** 自适应带宽 */
    ADAPTIVE = "adaptive",
    /** 质量优先 */
    QUALITY_FIRST = "quality_first",
    /** 性能优先 */
    PERFORMANCE_FIRST = "performance_first"
}
/**
 * 数据优先级
 */
export declare enum DataPriority {
    /** 最高优先级 */
    HIGHEST = 0,
    /** 高优先级 */
    HIGH = 1,
    /** 中等优先级 */
    MEDIUM = 2,
    /** 低优先级 */
    LOW = 3,
    /** 最低优先级 */
    LOWEST = 4
}
/**
 * 带宽控制器配置
 */
export interface BandwidthControllerConfig {
    /** 最大上行带宽（字节/秒） */
    maxUploadBandwidth?: number;
    /** 最大下行带宽（字节/秒） */
    maxDownloadBandwidth?: number;
    /** 带宽控制策略 */
    strategy?: BandwidthControlStrategy;
    /** 带宽使用目标（0-1，表示最大带宽的使用比例） */
    targetUsage?: number;
    /** 带宽分配比例（按优先级） */
    priorityAllocation?: Record<DataPriority, number>;
    /** 是否启用自动调整 */
    autoAdjust?: boolean;
    /** 调整间隔（毫秒） */
    adjustInterval?: number;
}
/**
 * 带宽使用数据
 */
export interface BandwidthUsageData {
    /** 上行带宽使用（字节/秒） */
    upload: number;
    /** 下行带宽使用（字节/秒） */
    download: number;
    /** 上行带宽限制（字节/秒） */
    uploadLimit: number;
    /** 下行带宽限制（字节/秒） */
    downloadLimit: number;
    /** 上行带宽使用率（0-1） */
    uploadUsageRatio: number;
    /** 下行带宽使用率（0-1） */
    downloadUsageRatio: number;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 带宽控制器
 * 负责管理网络带宽使用
 */
export declare class BandwidthController extends EventEmitter {
    /** 配置 */
    private config;
    /** 当前上行带宽限制（字节/秒） */
    private currentUploadLimit;
    /** 当前下行带宽限制（字节/秒） */
    private currentDownloadLimit;
    /** 当前上行带宽使用（字节/秒） */
    private currentUploadUsage;
    /** 当前下行带宽使用（字节/秒） */
    private currentDownloadUsage;
    /** 上行数据计数（当前周期） */
    private uploadByteCount;
    /** 下行数据计数（当前周期） */
    private downloadByteCount;
    /** 上次计数重置时间 */
    private lastResetTime;
    /** 调整定时器ID */
    private adjustTimerId;
    /** 优先级队列（按优先级存储待发送数据） */
    private priorityQueues;
    /** 优先级带宽分配 */
    private priorityBandwidthAllocation;
    /** 最近的网络质量数据 */
    private latestNetworkQuality;
    /**
     * 创建带宽控制器
     * @param config 配置
     */
    constructor(config?: BandwidthControllerConfig);
    /**
     * 初始化优先级队列
     */
    private initPriorityQueues;
    /**
     * 更新优先级带宽分配
     */
    private updatePriorityBandwidthAllocation;
    /**
     * 启动自动调整
     */
    startAutoAdjust(): void;
    /**
     * 停止自动调整
     */
    stopAutoAdjust(): void;
    /**
     * 调整带宽
     */
    private adjustBandwidth;
    /**
     * 自适应调整带宽
     */
    private adjustAdaptive;
    /**
     * 质量优先调整带宽
     */
    private adjustQualityFirst;
    /**
     * 性能优先调整带宽
     */
    private adjustPerformanceFirst;
    /**
     * 重置计数器
     */
    private resetCounters;
    /**
     * 记录上行数据
     * @param bytes 字节数
     */
    recordUpload(bytes: number): void;
    /**
     * 记录下行数据
     * @param bytes 字节数
     */
    recordDownload(bytes: number): void;
    /**
     * 获取带宽使用数据
     * @returns 带宽使用数据
     */
    getBandwidthUsage(): BandwidthUsageData;
    /**
     * 设置网络质量数据
     * @param quality 网络质量数据
     */
    setNetworkQuality(quality: NetworkQualityData): void;
    /**
     * 设置带宽控制策略
     * @param strategy 带宽控制策略
     */
    setStrategy(strategy: BandwidthControlStrategy): void;
    /**
     * 获取带宽控制策略
     * @returns 带宽控制策略
     */
    getStrategy(): BandwidthControlStrategy;
    /**
     * 设置最大上行带宽
     * @param bandwidth 带宽（字节/秒）
     */
    setMaxUploadBandwidth(bandwidth: number): void;
    /**
     * 设置最大下行带宽
     * @param bandwidth 带宽（字节/秒）
     */
    setMaxDownloadBandwidth(bandwidth: number): void;
    /**
     * 销毁控制器
     */
    dispose(): void;
}
