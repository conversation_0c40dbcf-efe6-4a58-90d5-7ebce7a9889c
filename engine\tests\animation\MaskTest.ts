/**
 * 遮罩测试
 */
import { AnimationMask, MaskType, MaskWeightType, BoneGroupType } from '../../src/animation/AnimationMask';

describe('遮罩测试', () => {
  // 测试基本遮罩功能
  test('基本遮罩功能', () => {
    // 创建包含遮罩
    const includeMask = new AnimationMask({
      name: 'includeMask',
      type: MaskType.INCLUDE,
      bones: ['head', 'neck', 'spine']
    });

    // 验证骨骼权重
    expect(includeMask.getBoneWeight('head')).toBe(1.0);
    expect(includeMask.getBoneWeight('neck')).toBe(1.0);
    expect(includeMask.getBoneWeight('spine')).toBe(1.0);
    expect(includeMask.getBoneWeight('arm')).toBe(0.0);

    // 创建排除遮罩
    const excludeMask = new AnimationMask({
      name: 'excludeMask',
      type: MaskType.EXCLUDE,
      bones: ['head', 'neck', 'spine']
    });

    // 验证骨骼权重
    expect(excludeMask.getBoneWeight('head')).toBe(0.0);
    expect(excludeMask.getBoneWeight('neck')).toBe(0.0);
    expect(excludeMask.getBoneWeight('spine')).toBe(0.0);
    expect(excludeMask.getBoneWeight('arm')).toBe(1.0);
  });

  // 测试层级遮罩功能
  test('层级遮罩功能', () => {
    // 创建层级遮罩
    const hierarchyMask = new AnimationMask({
      name: 'hierarchyMask',
      type: MaskType.HIERARCHY,
      bones: ['spine']
    });

    // 验证骨骼权重
    expect(hierarchyMask.getBoneWeight('spine')).toBe(1.0);
    expect(hierarchyMask.getBoneWeight('head')).toBe(1.0); // 假设head是spine的子骨骼
    expect(hierarchyMask.getBoneWeight('arm')).toBe(1.0); // 假设arm是spine的子骨骼
    expect(hierarchyMask.getBoneWeight('leg')).toBe(0.0); // 假设leg不是spine的子骨骼

    // 创建反层级遮罩
    const inverseHierarchyMask = new AnimationMask({
      name: 'inverseHierarchyMask',
      type: MaskType.INVERSE_HIERARCHY,
      bones: ['spine']
    });

    // 验证骨骼权重
    expect(inverseHierarchyMask.getBoneWeight('spine')).toBe(0.0);
    expect(inverseHierarchyMask.getBoneWeight('head')).toBe(0.0); // 假设head是spine的子骨骼
    expect(inverseHierarchyMask.getBoneWeight('arm')).toBe(0.0); // 假设arm是spine的子骨骼
    expect(inverseHierarchyMask.getBoneWeight('leg')).toBe(1.0); // 假设leg不是spine的子骨骼
  });

  // 测试骨骼组遮罩功能
  test('骨骼组遮罩功能', () => {
    // 创建骨骼组遮罩
    const groupMask = AnimationMask.createBoneGroupMask(BoneGroupType.HEAD);

    // 验证骨骼权重
    expect(groupMask.getBoneWeight('head')).toBe(1.0);
    expect(groupMask.getBoneWeight('neck')).toBe(1.0);
    expect(groupMask.getBoneWeight('face')).toBe(1.0);
    expect(groupMask.getBoneWeight('arm')).toBe(0.0);
  });

  // 测试遮罩混合功能
  test('遮罩混合功能', () => {
    // 创建两个遮罩
    const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);
    const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);

    // 创建混合遮罩
    const blendMask = AnimationMask.createBlendMask(
      [upperBodyMask, lowerBodyMask],
      [0.7, 0.3],
      true
    );

    // 验证骨骼权重
    expect(blendMask.getBoneWeight('head')).toBeCloseTo(0.7);
    expect(blendMask.getBoneWeight('spine')).toBeCloseTo(0.7);
    expect(blendMask.getBoneWeight('leftLeg')).toBeCloseTo(0.3);
    expect(blendMask.getBoneWeight('rightLeg')).toBeCloseTo(0.3);
  });

  // 测试遮罩过渡功能
  test('遮罩过渡功能', () => {
    // 创建两个遮罩
    const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);
    const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);

    // 创建过渡遮罩
    const transitionMask = AnimationMask.createTransitionMask(
      upperBodyMask,
      lowerBodyMask,
      0.5
    );

    // 验证骨骼权重
    expect(transitionMask.getBoneWeight('head')).toBeCloseTo(0.5);
    expect(transitionMask.getBoneWeight('spine')).toBeCloseTo(0.5);
    expect(transitionMask.getBoneWeight('leftLeg')).toBeCloseTo(0.5);
    expect(transitionMask.getBoneWeight('rightLeg')).toBeCloseTo(0.5);

    // 更新过渡进度
    transitionMask.updateTransitionProgress(0.8);

    // 验证骨骼权重
    expect(transitionMask.getBoneWeight('head')).toBeCloseTo(0.2);
    expect(transitionMask.getBoneWeight('spine')).toBeCloseTo(0.2);
    expect(transitionMask.getBoneWeight('leftLeg')).toBeCloseTo(0.8);
    expect(transitionMask.getBoneWeight('rightLeg')).toBeCloseTo(0.8);
  });

  // 测试不同权重类型
  test('不同权重类型', () => {
    // 创建二进制权重遮罩
    const binaryMask = new AnimationMask({
      name: 'binaryMask',
      type: MaskType.INCLUDE,
      weightType: MaskWeightType.BINARY,
      bones: ['head', 'neck', 'spine']
    });

    // 验证骨骼权重
    expect(binaryMask.getBoneWeight('head')).toBe(1.0);
    expect(binaryMask.getBoneWeight('arm')).toBe(0.0);

    // 创建平滑权重遮罩
    const smoothMask = new AnimationMask({
      name: 'smoothMask',
      type: MaskType.INCLUDE,
      weightType: MaskWeightType.SMOOTH,
      bones: ['head', 'neck', 'spine']
    });

    // 设置平滑权重
    smoothMask.setBoneWeight('head', 0.8);
    smoothMask.setBoneWeight('neck', 0.6);
    smoothMask.setBoneWeight('spine', 0.4);

    // 验证骨骼权重
    expect(smoothMask.getBoneWeight('head')).toBe(0.8);
    expect(smoothMask.getBoneWeight('neck')).toBe(0.6);
    expect(smoothMask.getBoneWeight('spine')).toBe(0.4);
    expect(smoothMask.getBoneWeight('arm')).toBe(0.0);
  });
});
