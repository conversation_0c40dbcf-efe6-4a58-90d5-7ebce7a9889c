/**
 * 喷泉示例
 * 展示如何使用喷泉系统创建各种喷泉效果
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { FountainComponent, FountainType, FountainMode } from '../../engine/src/physics/water/FountainComponent';
import { FountainPresets, FountainPresetType } from '../../engine/src/physics/water/FountainPresets';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../engine/src/physics/water/WaterInteractionSystem';
import { UnderwaterParticleSystem } from '../../engine/src/rendering/water/UnderwaterParticleSystem';
import { WaterLightingSystem } from '../../engine/src/rendering/water/WaterLightingSystem';
import { AudioSystem } from '../../engine/src/audio/AudioSystem';
import { TransformComponent } from '../../engine/src/core/TransformComponent';
import { CameraComponent } from '../../engine/src/rendering/CameraComponent';
import { LightComponent } from '../../engine/src/rendering/LightComponent';
import { MeshComponent } from '../../engine/src/rendering/MeshComponent';
import { MaterialComponent } from '../../engine/src/rendering/MaterialComponent';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { Debug } from '../../engine/src/utils/Debug';
import { GUI } from 'dat.gui';

/**
 * 喷泉示例
 */
export class FountainExample {
  /** 世界 */
  private world: World;
  /** 喷泉实体 */
  private fountainEntity: Entity | null = null;
  /** 喷泉组件 */
  private fountainComponent: FountainComponent | null = null;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem | null = null;
  /** 水体交互系统 */
  private waterInteractionSystem: WaterInteractionSystem | null = null;
  /** 水下粒子系统 */
  private underwaterParticleSystem: UnderwaterParticleSystem | null = null;
  /** 水体光照系统 */
  private waterLightingSystem: WaterLightingSystem | null = null;
  /** 音频系统 */
  private audioSystem: AudioSystem | null = null;
  /** 输入系统 */
  private inputSystem: InputSystem | null = null;
  /** GUI */
  private gui: GUI | null = null;
  /** 当前喷泉类型 */
  private currentFountainType: FountainPresetType = FountainPresetType.STANDARD;
  /** 喷泉类型列表 */
  private fountainTypes: FountainPresetType[] = [
    FountainPresetType.STANDARD,
    FountainPresetType.HIGH,
    FountainPresetType.WIDE,
    FountainPresetType.MULTI_JET,
    FountainPresetType.DANCING,
    FountainPresetType.MUSICAL,
    FountainPresetType.PULSE,
    FountainPresetType.ALTERNATING,
    FountainPresetType.SEQUENCE,
    FountainPresetType.RANDOM
  ];

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  private initialize(): void {
    // 初始化相机
    this.initializeCamera();

    // 初始化灯光
    this.initializeLights();

    // 初始化地面
    this.initializeGround();

    // 初始化系统
    this.initializeSystems();

    // 初始化喷泉
    this.initializeFountain();

    // 初始化GUI
    this.initializeGUI();

    // 启动
    this.start();
  }

  /**
   * 初始化相机
   */
  private initializeCamera(): void {
    // 创建相机实体
    const cameraEntity = new Entity();
    cameraEntity.setName('camera');

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(new THREE.Vector3(0, 10, 30));
    transform.setRotation(new THREE.Euler(-Math.PI / 6, 0, 0));
    cameraEntity.addComponent(transform);

    // 添加相机组件
    const camera = new CameraComponent();
    camera.setFov(60);
    camera.setNear(0.1);
    camera.setFar(1000);
    cameraEntity.addComponent(camera);

    // 添加到世界
    this.world.addEntity(cameraEntity);
    this.world.setActiveCamera(camera);
  }

  /**
   * 初始化灯光
   */
  private initializeLights(): void {
    // 创建环境光实体
    const ambientLightEntity = new Entity();
    ambientLightEntity.setName('ambient_light');

    // 添加环境光组件
    const ambientLight = new LightComponent();
    ambientLight.setType('ambient');
    ambientLight.setColor(new THREE.Color(0x404040));
    ambientLight.setIntensity(0.5);
    ambientLightEntity.addComponent(ambientLight);

    // 添加到世界
    this.world.addEntity(ambientLightEntity);

    // 创建方向光实体
    const directionalLightEntity = new Entity();
    directionalLightEntity.setName('directional_light');

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(new THREE.Vector3(10, 20, 10));
    transform.lookAt(new THREE.Vector3(0, 0, 0));
    directionalLightEntity.addComponent(transform);

    // 添加方向光组件
    const directionalLight = new LightComponent();
    directionalLight.setType('directional');
    directionalLight.setColor(new THREE.Color(0xffffff));
    directionalLight.setIntensity(1.0);
    directionalLight.setCastShadow(true);
    directionalLightEntity.addComponent(directionalLight);

    // 添加到世界
    this.world.addEntity(directionalLightEntity);
  }

  /**
   * 初始化地面
   */
  private initializeGround(): void {
    // 创建地面实体
    const groundEntity = new Entity();
    groundEntity.setName('ground');

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(new THREE.Vector3(0, -0.5, 0));
    groundEntity.addComponent(transform);

    // 添加网格组件
    const mesh = new MeshComponent();
    mesh.setGeometry(new THREE.PlaneGeometry(100, 100));
    mesh.setReceiveShadow(true);
    groundEntity.addComponent(mesh);

    // 添加材质组件
    const material = new MaterialComponent();
    material.setType('standard');
    material.setColor(new THREE.Color(0x888888));
    material.setRoughness(0.8);
    material.setMetalness(0.2);
    groundEntity.addComponent(material);

    // 添加到世界
    this.world.addEntity(groundEntity);
  }

  /**
   * 初始化系统
   */
  private initializeSystems(): void {
    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水体交互系统
    this.waterInteractionSystem = new WaterInteractionSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true
    });
    this.world.addSystem(this.waterInteractionSystem);

    // 创建水下粒子系统
    this.underwaterParticleSystem = new UnderwaterParticleSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      maxParticles: 1000
    });
    this.world.addSystem(this.underwaterParticleSystem);

    // 创建水体光照系统
    this.waterLightingSystem = new WaterLightingSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableUnderwaterFog: true
    });
    this.world.addSystem(this.waterLightingSystem);

    // 创建音频系统
    this.audioSystem = new AudioSystem(this.world, {
      enabled: true,
      autoUpdate: true
    });
    this.world.addSystem(this.audioSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world, {
      enabled: true,
      autoUpdate: true
    });
    this.world.addSystem(this.inputSystem);
  }

  /**
   * 初始化喷泉
   */
  private initializeFountain(): void {
    // 创建喷泉
    this.fountainEntity = FountainPresets.createPreset(this.world, {
      type: this.currentFountainType,
      position: new THREE.Vector3(0, 0, 0)
    });

    // 获取喷泉组件
    this.fountainComponent = this.fountainEntity.getComponent(FountainComponent);
  }

  /**
   * 初始化GUI
   */
  private initializeGUI(): void {
    // 创建GUI
    this.gui = new GUI();

    // 添加喷泉类型选择
    const fountainTypeController = this.gui.add(
      { fountainType: this.getFountainTypeName(this.currentFountainType) },
      'fountainType',
      this.fountainTypes.map(type => this.getFountainTypeName(type))
    );
    fountainTypeController.name('喷泉类型');
    fountainTypeController.onChange((value: string) => {
      // 查找对应的喷泉类型
      const type = this.fountainTypes.find(t => this.getFountainTypeName(t) === value);
      if (type) {
        this.changeFountainType(type);
      }
    });

    // 如果有喷泉组件，添加喷泉属性控制
    if (this.fountainComponent) {
      // 添加喷泉高度控制
      const jetHeightController = this.gui.add(
        { jetHeight: this.fountainComponent.getJetHeight() },
        'jetHeight',
        5,
        30
      );
      jetHeightController.name('喷射高度');
      jetHeightController.onChange((value: number) => {
        if (this.fountainComponent) {
          this.fountainComponent.setJetHeight(value);
        }
      });

      // 添加喷泉角度控制
      const jetAngleController = this.gui.add(
        { jetAngle: this.fountainComponent.getJetAngle() },
        'jetAngle',
        0,
        45
      );
      jetAngleController.name('喷射角度');
      jetAngleController.onChange((value: number) => {
        if (this.fountainComponent) {
          this.fountainComponent.setJetAngle(value);
        }
      });

      // 添加喷泉数量控制
      const jetCountController = this.gui.add(
        { jetCount: this.fountainComponent.getJetCount() },
        'jetCount',
        1,
        20,
        1
      );
      jetCountController.name('喷射数量');
      jetCountController.onChange((value: number) => {
        if (this.fountainComponent) {
          this.fountainComponent.setJetCount(value);
        }
      });
    }
  }

  /**
   * 更改喷泉类型
   * @param type 喷泉类型
   */
  private changeFountainType(type: FountainPresetType): void {
    // 保存当前类型
    this.currentFountainType = type;

    // 如果有喷泉实体，移除它
    if (this.fountainEntity) {
      this.world.removeEntity(this.fountainEntity);
      this.fountainEntity = null;
      this.fountainComponent = null;
    }

    // 创建新的喷泉
    this.fountainEntity = FountainPresets.createPreset(this.world, {
      type: this.currentFountainType,
      position: new THREE.Vector3(0, 0, 0)
    });

    // 获取喷泉组件
    this.fountainComponent = this.fountainEntity.getComponent(FountainComponent);

    // 更新GUI
    this.updateGUI();
  }

  /**
   * 更新GUI
   */
  private updateGUI(): void {
    // 如果有GUI，移除它
    if (this.gui) {
      this.gui.destroy();
    }

    // 重新初始化GUI
    this.initializeGUI();
  }

  /**
   * 获取喷泉类型名称
   * @param type 喷泉类型
   * @returns 喷泉类型名称
   */
  private getFountainTypeName(type: FountainPresetType): string {
    switch (type) {
      case FountainPresetType.STANDARD:
        return '标准喷泉';
      case FountainPresetType.HIGH:
        return '高喷泉';
      case FountainPresetType.WIDE:
        return '宽喷泉';
      case FountainPresetType.MULTI_JET:
        return '多喷头喷泉';
      case FountainPresetType.DANCING:
        return '舞蹈喷泉';
      case FountainPresetType.MUSICAL:
        return '音乐喷泉';
      case FountainPresetType.PULSE:
        return '脉冲喷泉';
      case FountainPresetType.ALTERNATING:
        return '交替喷泉';
      case FountainPresetType.SEQUENCE:
        return '序列喷泉';
      case FountainPresetType.RANDOM:
        return '随机喷泉';
      default:
        return type;
    }
  }

  /**
   * 启动
   */
  private start(): void {
    // 启动世界
    this.world.start();
  }
}
