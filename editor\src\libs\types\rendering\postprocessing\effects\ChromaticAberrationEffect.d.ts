/**
 * 色差效果
 * 模拟镜头色差
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 色差效果选项
 */
export interface ChromaticAberrationEffectOptions extends PostProcessingEffectOptions {
    /** 偏移量 */
    offset?: THREE.Vector2;
    /** 径向模式 */
    radialMode?: boolean;
    /** 强度 */
    intensity?: number;
}
/**
 * 色差效果
 */
export declare class ChromaticAberrationEffect extends PostProcessingEffect {
    /** 偏移量 */
    private offset;
    /** 径向模式 */
    private radialMode;
    /** 强度 */
    private intensity;
    /** 色差通道 */
    private chromaticAberrationPass;
    /**
     * 创建色差效果
     * @param options 色差效果选项
     */
    constructor(options?: ChromaticAberrationEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 设置偏移量
     * @param offset 偏移量
     */
    setOffset(offset: THREE.Vector2): void;
    /**
     * 获取偏移量
     * @returns 偏移量
     */
    getOffset(): THREE.Vector2;
    /**
     * 设置径向模式
     * @param enabled 是否启用径向模式
     */
    setRadialMode(enabled: boolean): void;
    /**
     * 获取径向模式
     * @returns 是否启用径向模式
     */
    isRadialMode(): boolean;
    /**
     * 设置强度
     * @param intensity 强度
     */
    setIntensity(intensity: number): void;
    /**
     * 获取强度
     * @returns 强度
     */
    getIntensity(): number;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
}
