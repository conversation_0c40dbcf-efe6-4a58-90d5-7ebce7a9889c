/**
 * 性能基准测试
 * 用于测试和比较不同优化技术的性能效果
 */
import * as THREE from 'three';
import { AnalysisReport } from './GPUPerformanceAnalyzer';
/**
 * 基准测试事件类型
 */
export declare enum BenchmarkEventType {
    /** 测试开始 */
    TEST_STARTED = "test_started",
    /** 测试完成 */
    TEST_COMPLETED = "test_completed",
    /** 测试错误 */
    TEST_ERROR = "test_error",
    /** 测试进度更新 */
    TEST_PROGRESS = "test_progress",
    /** 所有测试完成 */
    ALL_TESTS_COMPLETED = "all_tests_completed"
}
/**
 * 基准测试配置
 */
export interface BenchmarkConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 每个测试的持续时间（毫秒） */
    testDuration?: number;
    /** 每个测试的预热时间（毫秒） */
    warmupDuration?: number;
    /** 每个测试的采样间隔（毫秒） */
    sampleInterval?: number;
    /** 是否自动运行所有测试 */
    autoRunAll?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 测试场景
 */
export interface TestScene {
    /** 场景ID */
    id: string;
    /** 场景名称 */
    name: string;
    /** 场景描述 */
    description: string;
    /** 场景创建函数 */
    create: () => THREE.Scene;
    /** 场景清理函数 */
    cleanup: () => void;
    /** 场景更新函数 */
    update?: (deltaTime: number) => void;
    /** 场景配置 */
    config?: Record<string, any>;
}
/**
 * 测试结果
 */
export interface TestResult {
    /** 测试ID */
    id: string;
    /** 场景ID */
    sceneId: string;
    /** 优化技术ID */
    techniqueId: string;
    /** 分析报告 */
    report: AnalysisReport;
    /** 平均FPS */
    averageFps: number;
    /** 最小FPS */
    minFps: number;
    /** 最大FPS */
    maxFps: number;
    /** 平均渲染时间（毫秒） */
    averageRenderTime: number;
    /** 平均内存使用（MB） */
    averageMemoryUsage: number;
    /** 平均绘制调用次数 */
    averageDrawCalls: number;
    /** 平均三角形数量 */
    averageTriangles: number;
    /** 测试持续时间（毫秒） */
    duration: number;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 优化技术
 */
export interface OptimizationTechnique {
    /** 技术ID */
    id: string;
    /** 技术名称 */
    name: string;
    /** 技术描述 */
    description: string;
    /** 应用函数 */
    apply: (scene: THREE.Scene, renderer: THREE.WebGLRenderer, config?: Record<string, any>) => void;
    /** 清理函数 */
    cleanup: (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => void;
    /** 技术配置 */
    config?: Record<string, any>;
}
/**
 * 比较结果
 */
export interface ComparisonResult {
    /** 基准测试ID */
    baselineId: string;
    /** 比较测试ID */
    comparisonId: string;
    /** FPS改进（百分比） */
    fpsImprovement: number;
    /** 渲染时间改进（百分比） */
    renderTimeImprovement: number;
    /** 内存使用改进（百分比） */
    memoryUsageImprovement: number;
    /** 绘制调用改进（百分比） */
    drawCallsImprovement: number;
    /** 三角形数量改进（百分比） */
    trianglesImprovement: number;
    /** 总体改进（百分比） */
    overallImprovement: number;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 性能基准测试
 */
export declare class PerformanceBenchmark {
    /** 单例实例 */
    private static instance;
    /** 配置 */
    private config;
    /** 是否运行中 */
    private running;
    /** 渲染器 */
    private renderer;
    /** 相机 */
    private camera;
    /** 性能分析器 */
    private analyzer;
    /** 事件发射器 */
    private eventEmitter;
    /** 测试场景 */
    private scenes;
    /** 优化技术 */
    private techniques;
    /** 测试结果 */
    private results;
    /** 当前测试ID */
    private currentTestId;
    /** 当前场景 */
    private currentScene;
    /** 当前场景配置 */
    private currentSceneConfig;
    /** 当前技术 */
    private currentTechnique;
    /** 测试开始时间 */
    private testStartTime;
    /** 测试结束时间 */
    private testEndTime;
    /** 动画帧ID */
    private animationFrameId;
    /** 上一帧时间 */
    private lastFrameTime;
    /** 测试队列 */
    private testQueue;
    /** 是否在预热阶段 */
    private isWarmup;
    /** 是否在采样阶段 */
    private isSampling;
    /** 采样定时器ID */
    private sampleTimerId;
    /**
     * 获取单例实例
     * @returns 性能基准测试实例
     */
    static getInstance(): PerformanceBenchmark;
    /**
     * 私有构造函数
     */
    private constructor();
    /**
     * 配置基准测试
     * @param config 配置
     */
    configure(config: BenchmarkConfig): void;
    /**
     * 设置渲染器
     * @param renderer 渲染器
     */
    setRenderer(renderer: THREE.WebGLRenderer): void;
    /**
     * 设置相机
     * @param camera 相机
     */
    setCamera(camera: THREE.Camera): void;
    /**
     * 注册测试场景
     * @param scene 测试场景
     */
    registerScene(scene: TestScene): void;
    /**
     * 注册优化技术
     * @param technique 优化技术
     */
    registerTechnique(technique: OptimizationTechnique): void;
    /**
     * 运行测试
     * @param sceneId 场景ID
     * @param techniqueId 技术ID
     * @returns 测试ID
     */
    runTest(sceneId: string, techniqueId: string): string;
    /**
     * 启动渲染循环
     */
    private startRenderLoop;
    /**
     * 启动采样
     */
    private startSampling;
    /**
     * 停止采样
     */
    private stopSampling;
    /**
     * 结束测试
     */
    private endTest;
    /**
     * 运行所有测试
     */
    runAllTests(): void;
    /**
     * 获取测试结果
     * @param testId 测试ID
     * @returns 测试结果
     */
    getTestResult(testId: string): TestResult | null;
    /**
     * 获取所有测试结果
     * @returns 所有测试结果
     */
    getAllTestResults(): TestResult[];
    /**
     * 获取场景的测试结果
     * @param sceneId 场景ID
     * @returns 场景的测试结果
     */
    getSceneResults(sceneId: string): TestResult[];
    /**
     * 获取技术的测试结果
     * @param techniqueId 技术ID
     * @returns 技术的测试结果
     */
    getTechniqueResults(techniqueId: string): TestResult[];
    /**
     * 比较测试结果
     * @param baselineId 基准测试ID
     * @param comparisonId 比较测试ID
     * @returns 比较结果
     */
    compareResults(baselineId: string, comparisonId: string): ComparisonResult | null;
    /**
     * 添加事件监听器
     * @param eventType 事件类型
     * @param listener 监听器
     */
    addEventListener(eventType: BenchmarkEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param eventType 事件类型
     * @param listener 监听器
     */
    removeEventListener(eventType: BenchmarkEventType, listener: (...args: any[]) => void): void;
    /**
     * 清除所有数据
     */
    clearData(): void;
}
