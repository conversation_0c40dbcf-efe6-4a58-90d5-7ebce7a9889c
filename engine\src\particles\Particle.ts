/**
 * 粒子类
 * 表示单个粒子的属性和行为
 */
import * as THREE from 'three';

export class Particle {
  /** 是否活跃 */
  public active: boolean = false;

  /** 位置 */
  public position: THREE.Vector3 = new THREE.Vector3();

  /** 上一帧位置 */
  public previousPosition: THREE.Vector3 = new THREE.Vector3();

  /** 速度 */
  public velocity: THREE.Vector3 = new THREE.Vector3();

  /** 加速度 */
  public acceleration: THREE.Vector3 = new THREE.Vector3();

  /** 旋转 */
  public rotation: number = 0;

  /** 旋转速度 */
  public rotationSpeed: number = 0;

  /** 缩放 */
  public scale: THREE.Vector2 = new THREE.Vector2(1, 1);

  /** 缩放速度 */
  public scaleSpeed: THREE.Vector2 = new THREE.Vector2(0, 0);

  /** 颜色 */
  public color: THREE.Color = new THREE.Color().setRGB(1, 1, 1);

  /** 起始颜色 */
  public startColor: THREE.Color = new THREE.Color().setRGB(1, 1, 1);

  /** 结束颜色 */
  public endColor: THREE.Color = new THREE.Color().setRGB(1, 1, 1);

  /** 透明度 */
  public opacity: number = 1;

  /** 起始透明度 */
  public startOpacity: number = 1;

  /** 结束透明度 */
  public endOpacity: number = 0;

  /** 生命周期（秒） */
  public lifetime: number = 1;

  /** 已存活时间（秒） */
  public age: number = 0;

  /** 归一化年龄（0-1） */
  public normalizedAge: number = 0;

  /** 质量 */
  public mass: number = 1;

  /** 阻力 */
  public drag: number = 0;

  /** 重力缩放 */
  public gravityScale: number = 1;

  /** 弹性 */
  public restitution: number = 0.5;

  /** 纹理索引 */
  public textureIndex: number = 0;

  /** 自定义数据 - 可以存储任何与粒子相关的额外信息 */
  public userData: Record<string, unknown> = {};

  /**
   * 创建粒子实例
   */
  constructor() {
    this.reset();
  }

  /**
   * 重置粒子状态
   */
  public reset(): void {
    this.active = false;
    this.position.set(0, 0, 0);
    this.previousPosition.set(0, 0, 0);
    this.velocity.set(0, 0, 0);
    this.acceleration.set(0, 0, 0);
    this.rotation = 0;
    this.rotationSpeed = 0;
    this.scale.set(1, 1);
    this.scaleSpeed.set(0, 0);
    this.color.setRGB(1, 1, 1);
    this.startColor.setRGB(1, 1, 1);
    this.endColor.setRGB(1, 1, 1);
    this.opacity = 1;
    this.startOpacity = 1;
    this.endOpacity = 0;
    this.lifetime = 1;
    this.age = 0;
    this.normalizedAge = 0;
    this.mass = 1;
    this.drag = 0;
    this.gravityScale = 1;
    this.restitution = 0.5;
    this.textureIndex = 0;
    this.userData = {} as Record<string, unknown>;
  }

  /**
   * 更新粒子
   * @param deltaTime 帧间隔时间（秒）
   * @returns 是否仍然活跃
   */
  public update(deltaTime: number): boolean {
    if (!this.active) {
      return false;
    }

    // 更新年龄
    this.age += deltaTime;
    this.normalizedAge = Math.min(this.age / this.lifetime, 1);

    // 检查生命周期
    if (this.age >= this.lifetime) {
      this.active = false;
      return false;
    }

    // 保存上一帧位置
    this.previousPosition.copy(this.position);

    // 应用阻力
    if (this.drag > 0) {
      const dragForce = this.velocity.clone().multiplyScalar(-this.drag);
      this.acceleration.add(dragForce);
    }

    // 更新速度
    this.velocity.add(this.acceleration.clone().multiplyScalar(deltaTime));

    // 更新位置
    this.position.add(this.velocity.clone().multiplyScalar(deltaTime));

    // 更新旋转
    this.rotation += this.rotationSpeed * deltaTime;

    // 更新缩放
    this.scale.add(this.scaleSpeed.clone().multiplyScalar(deltaTime));

    // 更新颜色
    this.color.copy(this.startColor).lerp(this.endColor, this.normalizedAge);

    // 更新透明度
    this.opacity = this.startOpacity + (this.endOpacity - this.startOpacity) * this.normalizedAge;

    // 重置加速度
    this.acceleration.set(0, 0, 0);

    return true;
  }

  /**
   * 应用力
   * @param force 力向量
   */
  public applyForce(force: THREE.Vector3): void {
    // F = ma, a = F/m
    const acceleration = force.clone().divideScalar(this.mass);
    this.acceleration.add(acceleration);
  }

  /**
   * 应用重力
   * @param gravity 重力向量
   */
  public applyGravity(gravity: THREE.Vector3): void {
    if (this.gravityScale !== 0) {
      const gravityForce = gravity.clone().multiplyScalar(this.mass * this.gravityScale);
      this.applyForce(gravityForce);
    }
  }

  /**
   * 处理碰撞
   * @param normal 碰撞法线
   * @param penetration 穿透深度
   */
  public handleCollision(normal: THREE.Vector3, penetration: number): void {
    // 调整位置
    this.position.add(normal.clone().multiplyScalar(penetration));

    // 计算反弹速度
    const dot = this.velocity.dot(normal);
    if (dot < 0) {
      const reflection = normal.clone().multiplyScalar(-2 * dot);
      this.velocity.add(reflection).multiplyScalar(this.restitution);
    }
  }

  /**
   * 获取粒子的变换矩阵
   * @returns 变换矩阵
   */
  public getTransformMatrix(): THREE.Matrix4 {
    // 创建一个新的矩阵
    const matrix = new THREE.Matrix4();

    // 按照缩放 -> 旋转 -> 平移的顺序构建变换矩阵
    // 注意：在Three.js中，矩阵乘法顺序是从右到左

    // 我们使用Three.js的compose方法，它会自动按正确的顺序应用变换

    // 组合变换：先缩放，再旋转，最后平移
    matrix.compose(
      this.position,
      new THREE.Quaternion().setFromAxisAngle(new THREE.Vector3(0, 0, 1), this.rotation),
      new THREE.Vector3(this.scale.x, this.scale.y, 1)
    );

    return matrix;
  }

  /**
   * 获取粒子的颜色，包括透明度
   * @returns 颜色，包括透明度
   */
  public getColorWithOpacity(): THREE.Vector4 {
    return new THREE.Vector4(
      this.color.r,
      this.color.g,
      this.color.b,
      this.opacity
    );
  }

  /**
   * 计算与相机的距离
   * @param cameraPosition 相机位置
   * @returns 距离
   */
  public distanceToCamera(cameraPosition: THREE.Vector3): number {
    return this.position.distanceTo(cameraPosition);
  }
}
