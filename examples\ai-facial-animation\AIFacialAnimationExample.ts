/**
 * AI面部动画生成示例
 * 展示如何使用AI面部动画生成功能
 */
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { FacialAnimationComponent } from '../../engine/src/avatar/components/FacialAnimationComponent';
import { AIFacialAnimationAdapterSystem } from '../../engine/src/avatar/adapters/AIFacialAnimationAdapter';
import { AdvancedEmotionBasedAnimationGenerator } from '../../engine/src/avatar/ai/AdvancedEmotionBasedAnimationGenerator';
import { BERTEmotionModel } from '../../engine/src/avatar/ai/BERTEmotionModel';

/**
 * AI面部动画示例
 */
export class AIFacialAnimationExample {
  /** 世界 */
  private world: World;

  /** AI面部动画适配器系统 */
  private aiAnimationSystem: AIFacialAnimationAdapterSystem;

  /** 角色实体 */
  private character: Entity;

  /** 面部动画组件 */
  private facialAnimation: FacialAnimationComponent;

  /** 高级情感动画生成器 */
  private advancedGenerator: AdvancedEmotionBasedAnimationGenerator;

  /** BERT情感模型 */
  private bertModel: BERTEmotionModel;

  /**
   * 构造函数
   * @param world 世界
   */
  constructor(world: World) {
    this.world = world;

    // 创建AI面部动画适配器系统
    this.aiAnimationSystem = new AIFacialAnimationAdapterSystem({
      debug: true,
      useLocalModel: true,
      modelPath: 'models/bert-emotion',
      useGPU: false,
      enableAdvancedFeatures: true
    });

    // 注册系统
    world.registerSystem(this.aiAnimationSystem);

    // 创建高级情感动画生成器
    this.advancedGenerator = new AdvancedEmotionBasedAnimationGenerator({
      debug: true,
      useLocalModel: true,
      modelPath: 'models/emotion',
      useGPU: false,
      keyframeDensity: 6,
      enableExpressionBlending: true,
      enableMicroExpressions: true,
      enableEmotionTransitions: true,
      enableNaturalVariation: true
    });

    // 创建BERT情感模型
    this.bertModel = new BERTEmotionModel({
      debug: true,
      useLocalModel: true,
      modelPath: 'models/bert-emotion',
      useGPU: false
    });

    // 创建角色实体
    this.character = world.createEntity('character');

    // 添加面部动画组件
    this.facialAnimation = new FacialAnimationComponent();
    this.character.addComponent(this.facialAnimation);

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  private async initialize(): Promise<void> {
    try {
      // 初始化高级情感动画生成器
      await this.advancedGenerator.initialize();

      // 初始化BERT情感模型
      await this.bertModel.initialize();

      // 监听生成完成事件
      this.aiAnimationSystem.addEventListener('generationComplete', this.handleGenerationComplete);

      console.log('AI面部动画示例初始化成功');
    } catch (error) {
      console.error('初始化AI面部动画示例失败:', error);
    }
  }

  /**
   * 处理生成完成事件
   * @param result 生成结果
   */
  private handleGenerationComplete = (result: any): void => {
    if (result.success) {
      console.log('动画生成成功:', result.clipId);

      // 应用生成的动画
      this.aiAnimationSystem.applyGeneratedAnimation(this.character, result.clipId);
    } else {
      console.error('动画生成失败:', result.error);
    }
  };

  /**
   * 运行基本示例
   */
  public runBasicExample(): void {
    console.log('运行基本AI面部动画示例');

    // 生成AI面部动画
    const request = {
      id: 'animation1',
      prompt: '角色感到非常开心，然后突然惊讶，最后回到平静状态',
      duration: 5.0,
      loop: true,
      style: 'natural',
      intensity: 0.8,
      modelType: 'advanced',
      useAdvancedFeatures: true
    };

    // 生成动画
    this.aiAnimationSystem.generateAIFacialAnimation(this.character, request);
  }

  /**
   * 运行BERT模型示例
   */
  public runBERTExample(): void {
    console.log('运行BERT模型AI面部动画示例');

    // 使用BERT模型生成动画
    const request = {
      id: 'animation2',
      prompt: '角色先是平静地思考，然后逐渐变得兴奋，最后露出满意的微笑',
      duration: 8.0,
      loop: false,
      style: 'natural',
      intensity: 0.7,
      modelType: 'bert',
      useAdvancedFeatures: true
    };

    // 生成动画
    this.aiAnimationSystem.generateAIFacialAnimation(this.character, request);
  }

  /**
   * 运行自定义生成器示例
   */
  public async runCustomGeneratorExample(): Promise<void> {
    console.log('运行自定义生成器示例');

    // 创建生成请求
    const request = {
      id: 'custom1',
      prompt: '角色表现出复杂的情感变化，从悲伤到愤怒再到释然',
      duration: 10.0,
      loop: false,
      intensity: 0.9
    };

    // 生成动画
    const result = await this.advancedGenerator.generateFacialAnimation(request);

    if (result.success && result.clip) {
      console.log('使用自定义生成器生成动画成功');

      // 使用生成的动画片段
      this.facialAnimation.addClip(result.clip);
      this.facialAnimation.playClip(result.clip.name);
    } else {
      console.error('使用自定义生成器生成动画失败:', result.error);
    }
  }

  /**
   * 运行情感分析示例
   */
  public async runEmotionAnalysisExample(): Promise<void> {
    console.log('运行情感分析示例');

    // 分析文本
    const text = '我感到非常高兴，但同时也有一点担忧';

    // 使用BERT模型分析情感
    const result = await this.bertModel.analyzeEmotion(text, {
      detail: 'high',
      includeSecondary: true,
      includeChanges: true
    });

    console.log('情感分析结果:', result);

    // 使用分析结果生成动画
    if (result) {
      // 创建生成请求
      const request = {
        id: 'emotion1',
        prompt: text,
        duration: 6.0,
        loop: false,
        intensity: 0.8
      };

      // 生成动画
      const animationResult = await this.advancedGenerator.generateFacialAnimation(request);

      if (animationResult.success && animationResult.clip) {
        console.log('使用情感分析结果生成动画成功');

        // 使用生成的动画片段
        this.facialAnimation.addClip(animationResult.clip);
        this.facialAnimation.playClip(animationResult.clip.name);
      }
    }
  }

  /**
   * 销毁
   */
  public dispose(): void {
    // 移除事件监听器
    this.aiAnimationSystem.removeEventListener('generationComplete', this.handleGenerationComplete);

    // 销毁系统
    this.aiAnimationSystem.dispose();
  }
}

/**
 * 创建并运行示例
 * @param world 世界
 */
export function createAIFacialAnimationExample(world: World): AIFacialAnimationExample {
  const example = new AIFacialAnimationExample(world);

  // 运行基本示例
  example.runBasicExample();

  // 延迟运行其他示例
  setTimeout(() => {
    example.runBERTExample();
  }, 6000);

  setTimeout(() => {
    example.runCustomGeneratorExample();
  }, 15000);

  setTimeout(() => {
    example.runEmotionAnalysisExample();
  }, 26000);

  return example;
}
