/**
 * 地形生成工作线程
 * 用于在后台线程中执行地形生成算法，提高性能
 */
import { TerrainFeatureType } from '../utils/TerrainGenerationAlgorithms';
/**
 * 工作线程消息类型
 */
export declare enum TerrainWorkerMessageType {
    /** 初始化 */
    INIT = "init",
    /** 生成地形 */
    GENERATE = "generate",
    /** 应用侵蚀 */
    APPLY_EROSION = "apply_erosion",
    /** 生成特征 */
    GENERATE_FEATURE = "generate_feature",
    /** 生成特征组合 */
    GENERATE_FEATURE_COMBINATION = "generate_feature_combination",
    /** 进度更新 */
    PROGRESS = "progress",
    /** 完成 */
    COMPLETE = "complete",
    /** 错误 */
    ERROR = "error"
}
/**
 * 工作线程消息数据
 */
export interface TerrainWorkerMessageData {
    /** 消息类型 */
    type: TerrainWorkerMessageType;
    /** 数据 */
    data: any;
}
/**
 * 地形生成参数
 */
export interface TerrainGenerationWorkerParams {
    /** 分辨率 */
    resolution: number;
    /** 算法类型 */
    algorithm: string;
    /** 算法参数 */
    params: any;
    /** 高度数据（如果是修改现有地形） */
    heightData?: Float32Array;
}
/**
 * 热侵蚀参数
 */
export interface ThermalErosionWorkerParams {
    /** 侵蚀迭代次数 */
    iterations: number;
    /** 侵蚀强度 */
    strength: number;
    /** 坡度阈值 */
    slopeThreshold: number;
    /** 沉积系数 */
    depositionRate: number;
}
/**
 * 水侵蚀参数
 */
export interface HydraulicErosionWorkerParams {
    /** 侵蚀迭代次数 */
    iterations: number;
    /** 雨滴数量 */
    droplets: number;
    /** 雨滴容量 */
    capacity: number;
    /** 侵蚀强度 */
    erosionStrength: number;
    /** 沉积强度 */
    depositionStrength: number;
    /** 蒸发率 */
    evaporationRate: number;
    /** 惯性 */
    inertia: number;
    /** 最小坡度 */
    minSlope: number;
    /** 重力 */
    gravity: number;
}
/**
 * 地形特征组合生成参数
 */
export interface TerrainFeatureCombinationWorkerParams {
    /** 基础地形类型 */
    baseTerrainType: TerrainFeatureType;
    /** 基础地形参数 */
    baseTerrainParams: any;
    /** 特征列表 */
    features: {
        /** 特征类型 */
        type: TerrainFeatureType;
        /** 特征参数 */
        params: any;
        /** 权重 */
        weight: number;
    }[];
    /** 随机种子 */
    seed: number;
}
export declare function createTerrainGenerationWorker(): Worker;
