/**
 * AI模型服务
 * 提供AI模型管理和推理的核心业务逻辑
 */
import { Injectable, Logger, NotFoundException, BadRequestException, InternalServerErrorException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, In } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';

import { AIModel, ModelType, ModelStatus, ModelPurpose } from '../entities/ai-model.entity';
import { ModelVersion } from '../entities/model-version.entity';
import { InferenceLog, InferenceStatus } from '../entities/inference-log.entity';
import { ModelMetrics } from '../entities/model-metrics.entity';

// DTO
import { CreateModelDto, UpdateModelDto, InferenceRequestDto, ModelQueryDto } from '../dto';

// 接口
export interface LoadedModel {
  id: string;
  instance: any;
  loadedAt: Date;
  lastUsed: Date;
  memoryUsage: number;
  config: any;
  version?: string;
}

export interface InferenceResult {
  result: any;
  confidence: number;
  processingTime: number;
  modelVersion: string;
  metadata: {
    requestId: string;
    timestamp: number;
    tokens?: number;
    inputSize?: number;
    outputSize?: number;
  };
}

export interface ModelHealthStatus {
  modelId: string;
  status: 'healthy' | 'warning' | 'critical';
  issues: string[];
  metrics: {
    responseTime: number;
    errorRate: number;
    memoryUsage: number;
    lastUsed: Date;
  };
}

@Injectable()
export class AIModelService {
  private readonly logger = new Logger(AIModelService.name);
  
  // 已加载的模型缓存
  private loadedModels: Map<string, LoadedModel> = new Map();
  
  // 推理队列
  private inferenceQueue: Array<{
    id: string;
    modelId: string;
    request: InferenceRequestDto;
    resolve: (value: InferenceResult) => void;
    reject: (error: Error) => void;
    timestamp: number;
  }> = [];

  // 性能监控
  private performanceMetrics: Map<string, {
    requestCount: number;
    totalResponseTime: number;
    errorCount: number;
    lastReset: Date;
  }> = new Map();

  constructor(
    @InjectRepository(AIModel)
    private readonly modelRepository: Repository<AIModel>,
    
    @InjectRepository(ModelVersion)
    private readonly versionRepository: Repository<ModelVersion>,
    
    @InjectRepository(InferenceLog)
    private readonly inferenceLogRepository: Repository<InferenceLog>,
    
    @InjectRepository(ModelMetrics)
    private readonly metricsRepository: Repository<ModelMetrics>,
    
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    
    private readonly configService: ConfigService
  ) {
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService() {
    try {
      // 预加载默认模型
      await this.preloadDefaultModels();
      
      // 启动性能监控
      this.startPerformanceMonitoring();
      
      this.logger.log('AI模型服务初始化完成');
    } catch (error) {
      this.logger.error('AI模型服务初始化失败:', error);
    }
  }

  /**
   * 创建模型
   */
  async createModel(createModelDto: CreateModelDto): Promise<AIModel> {
    try {
      // 检查模型名称是否已存在
      const existingModel = await this.modelRepository.findOne({
        where: { name: createModelDto.name }
      });

      if (existingModel) {
        throw new BadRequestException(`模型名称 "${createModelDto.name}" 已存在`);
      }

      // 验证模型文件
      await this.validateModelFile(createModelDto.filePath);

      // 计算文件哈希
      const fileHash = await this.calculateFileHash(createModelDto.filePath);

      // 获取文件大小
      const stats = await fs.stat(createModelDto.filePath);
      const fileSize = Math.round(stats.size / (1024 * 1024)); // MB

      // 创建模型实体
      const model = this.modelRepository.create({
        ...createModelDto,
        fileSize,
        fileHash,
        status: ModelStatus.LOADING,
        usageCount: 0,
        errorCount: 0
      });

      // 保存到数据库
      const savedModel = await this.modelRepository.save(model);

      // 创建初始版本
      const version = this.versionRepository.create({
        modelId: savedModel.id,
        version: createModelDto.currentVersion,
        filePath: createModelDto.filePath,
        fileSize,
        fileHash,
        isCurrent: true,
        isStable: true,
        changelog: '初始版本'
      });

      await this.versionRepository.save(version);

      // 异步加载模型
      this.loadModelAsync(savedModel.id);

      this.logger.log(`模型创建成功: ${savedModel.name} (${savedModel.id})`);
      return savedModel;

    } catch (error) {
      this.logger.error('创建模型失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID查找模型
   */
  async findById(id: string): Promise<AIModel> {
    const model = await this.modelRepository.findOne({
      where: { id },
      relations: ['versions', 'metrics']
    });

    if (!model) {
      throw new NotFoundException(`模型 ${id} 不存在`);
    }

    return model;
  }

  /**
   * 创建模型 (别名方法)
   */
  async create(createModelDto: CreateModelDto): Promise<AIModel> {
    return this.createModel(createModelDto);
  }

  /**
   * 获取模型列表
   */
  async findModels(query: ModelQueryDto): Promise<{
    models: AIModel[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const {
        page = 1,
        pageSize = 20,
        type,
        purpose,
        status,
        isActive,
        search
      } = query;

      const queryBuilder = this.modelRepository.createQueryBuilder('model');

      // 构建查询条件
      if (type) {
        queryBuilder.andWhere('model.type = :type', { type });
      }

      if (purpose) {
        queryBuilder.andWhere('model.purpose = :purpose', { purpose });
      }

      if (status) {
        queryBuilder.andWhere('model.status = :status', { status });
      }

      if (isActive !== undefined) {
        queryBuilder.andWhere('model.isActive = :isActive', { isActive });
      }

      if (search) {
        queryBuilder.andWhere(
          '(model.name LIKE :search OR model.displayName LIKE :search OR model.description LIKE :search)',
          { search: `%${search}%` }
        );
      }

      // 排序
      queryBuilder.orderBy('model.priority', 'DESC')
                  .addOrderBy('model.usageCount', 'DESC')
                  .addOrderBy('model.createdAt', 'DESC');

      // 分页
      const offset = (page - 1) * pageSize;
      queryBuilder.skip(offset).take(pageSize);

      // 执行查询
      const [models, total] = await queryBuilder.getManyAndCount();

      return {
        models,
        total,
        page,
        pageSize
      };

    } catch (error) {
      this.logger.error('获取模型列表失败:', error);
      throw new InternalServerErrorException('获取模型列表失败');
    }
  }

  /**
   * 获取单个模型
   */
  async findModel(id: string): Promise<AIModel> {
    try {
      const model = await this.modelRepository.findOne({
        where: { id },
        relations: ['versions', 'metrics']
      });

      if (!model) {
        throw new NotFoundException(`模型不存在: ${id}`);
      }

      return model;

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('获取模型失败:', error);
      throw new InternalServerErrorException('获取模型失败');
    }
  }

  /**
   * 更新模型
   */
  async updateModel(id: string, updateModelDto: UpdateModelDto): Promise<AIModel> {
    try {
      const model = await this.findModel(id);

      // 更新模型属性
      Object.assign(model, updateModelDto);

      // 如果更新了文件路径，需要重新验证和计算哈希
      if (updateModelDto.filePath && updateModelDto.filePath !== model.filePath) {
        await this.validateModelFile(updateModelDto.filePath);
        model.fileHash = await this.calculateFileHash(updateModelDto.filePath);
        
        const stats = await fs.stat(updateModelDto.filePath);
        model.fileSize = Math.round(stats.size / (1024 * 1024));

        // 卸载旧模型
        await this.unloadModel(id);
        
        // 重新加载模型
        model.status = ModelStatus.LOADING;
        this.loadModelAsync(id);
      }

      const updatedModel = await this.modelRepository.save(model);

      this.logger.log(`模型更新成功: ${updatedModel.name} (${updatedModel.id})`);
      return updatedModel;

    } catch (error) {
      this.logger.error('更新模型失败:', error);
      throw error;
    }
  }

  /**
   * 更新模型 (别名方法)
   */
  async update(id: string, updateModelDto: UpdateModelDto): Promise<AIModel> {
    return this.updateModel(id, updateModelDto);
  }

  /**
   * 删除模型
   */
  async deleteModel(id: string): Promise<void> {
    try {
      const model = await this.findModel(id);

      // 卸载模型
      await this.unloadModel(id);

      // 删除数据库记录
      await this.modelRepository.remove(model);

      this.logger.log(`模型删除成功: ${model.name} (${id})`);

    } catch (error) {
      this.logger.error('删除模型失败:', error);
      throw error;
    }
  }

  /**
   * 删除模型 (别名方法)
   */
  async delete(id: string): Promise<void> {
    return this.deleteModel(id);
  }

  /**
   * 上传模型文件
   */
  async uploadModelFile(id: string, file: Express.Multer.File): Promise<AIModel> {
    try {
      const model = await this.findById(id);

      // 验证文件格式
      const allowedFormats = ['bin', 'onnx', 'pb', 'pth', 'h5', 'tflite', 'safetensors'];
      const fileExtension = file.originalname.split('.').pop()?.toLowerCase();

      if (!fileExtension || !allowedFormats.includes(fileExtension)) {
        throw new BadRequestException(`不支持的文件格式: ${fileExtension}`);
      }

      // 保存文件到存储目录
      const storagePath = this.configService.get<string>('ai.modelStorage.basePath', './storage/models');
      const fileName = `${model.id}_${Date.now()}.${fileExtension}`;
      const filePath = path.join(storagePath, fileName);

      // 确保目录存在
      await fs.mkdir(path.dirname(filePath), { recursive: true });

      // 保存文件
      await fs.writeFile(filePath, file.buffer);

      // 计算文件哈希
      const fileHash = await this.calculateFileHash(filePath);
      const fileSize = Math.round(file.size / (1024 * 1024)); // MB

      // 更新模型信息
      model.filePath = filePath;
      model.fileSize = fileSize;
      model.fileHash = fileHash;
      model.status = ModelStatus.LOADING;

      const updatedModel = await this.modelRepository.save(model);

      // 异步加载模型
      this.loadModelAsync(id);

      this.logger.log(`模型文件上传成功: ${model.name} (${filePath})`);
      return updatedModel;

    } catch (error) {
      this.logger.error('上传模型文件失败:', error);
      throw error;
    }
  }

  /**
   * 加载模型
   */
  async loadModel(id: string): Promise<LoadedModel> {
    try {
      // 检查是否已加载
      if (this.loadedModels.has(id)) {
        const loadedModel = this.loadedModels.get(id)!;
        loadedModel.lastUsed = new Date();
        return loadedModel;
      }

      // 获取模型信息
      const model = await this.findModel(id);

      if (!model.isAvailable()) {
        throw new BadRequestException(`模型不可用: ${model.name}`);
      }

      // 加载模型实例
      const instance = await this.loadModelInstance(model);

      const loadedModel: LoadedModel = {
        id: model.id,
        instance,
        loadedAt: new Date(),
        lastUsed: new Date(),
        memoryUsage: await this.calculateMemoryUsage(instance),
        config: model.config
      };

      // 缓存已加载的模型
      this.loadedModels.set(id, loadedModel);

      // 更新模型状态
      model.status = ModelStatus.READY;
      await this.modelRepository.save(model);

      this.logger.log(`模型加载成功: ${model.name} (${id})`);
      return loadedModel;

    } catch (error) {
      this.logger.error(`模型加载失败: ${id}`, error);
      
      // 更新模型状态为错误
      try {
        await this.modelRepository.update(id, { status: ModelStatus.ERROR });
      } catch (updateError) {
        this.logger.error('更新模型状态失败:', updateError);
      }

      throw error;
    }
  }

  /**
   * 执行推理
   */
  async inference(modelId: string, request: InferenceRequestDto): Promise<InferenceResult> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();

    try {
      // 获取模型
      const loadedModel = await this.loadModel(modelId);

      // 预处理输入
      const preprocessedInput = await this.preprocessInput(request.inputData, loadedModel.config);

      // 执行推理
      const rawOutput = await this.executeInference(loadedModel.instance, preprocessedInput);

      // 后处理输出
      const processedOutput = await this.postprocessOutput(rawOutput, loadedModel.config);

      const processingTime = Date.now() - startTime;

      // 构建结果
      const result: InferenceResult = {
        result: processedOutput,
        confidence: rawOutput.confidence || 1.0,
        processingTime,
        modelVersion: loadedModel.version || 'latest',
        metadata: {
          requestId,
          timestamp: Date.now(),
          tokens: rawOutput.tokens,
          inputSize: JSON.stringify(request.inputData || request.input).length,
          outputSize: JSON.stringify(processedOutput).length
        }
      };

      // 记录推理日志
      await this.logInference(modelId, request, result, true);

      // 更新性能指标
      await this.updatePerformanceMetrics(modelId, processingTime, false);

      // 更新模型使用统计
      const model = await this.findModel(modelId);
      model.updateUsageStats(processingTime, false);
      await this.modelRepository.save(model);

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      this.logger.error(`推理失败: ${modelId}`, error);

      // 记录错误日志
      await this.logInference(modelId, request, null, false, error instanceof Error ? error.message : String(error));

      // 更新性能指标
      await this.updatePerformanceMetrics(modelId, processingTime, true);

      // 更新模型错误统计
      try {
        const model = await this.findModel(modelId);
        model.updateUsageStats(processingTime, true);
        await this.modelRepository.save(model);
      } catch (updateError) {
        this.logger.error('更新模型统计失败:', updateError);
      }

      throw error;
    }
  }

  /**
   * 获取模型状态
   */
  async getModelStatus(id: string): Promise<any> {
    try {
      const model = await this.findById(id);
      const isLoaded = this.loadedModels.has(id);
      const loadedModel = isLoaded ? this.loadedModels.get(id) : null;

      return {
        id: model.id,
        name: model.name,
        status: model.status,
        isLoaded,
        loadedAt: loadedModel?.loadedAt,
        lastUsed: loadedModel?.lastUsed,
        memoryUsage: loadedModel?.memoryUsage || 0,
        usageCount: model.usageCount,
        errorCount: model.errorCount,
        averageResponseTime: model.averageResponseTime,
        lastUsedAt: model.lastUsedAt
      };
    } catch (error) {
      this.logger.error('获取模型状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取模型性能指标
   */
  async getModelMetrics(id: string, startDate?: string, endDate?: string): Promise<any> {
    try {
      const model = await this.findById(id);

      const queryBuilder = this.metricsRepository.createQueryBuilder('metrics')
        .where('metrics.modelId = :modelId', { modelId: id });

      if (startDate) {
        queryBuilder.andWhere('metrics.metricDate >= :startDate', { startDate });
      }

      if (endDate) {
        queryBuilder.andWhere('metrics.metricDate <= :endDate', { endDate });
      }

      const metrics = await queryBuilder
        .orderBy('metrics.metricDate', 'DESC')
        .getMany();

      return {
        modelId: id,
        modelName: model.name,
        metrics,
        summary: {
          totalRequests: model.usageCount,
          totalErrors: model.errorCount,
          averageResponseTime: model.averageResponseTime,
          lastUsed: model.lastUsedAt
        }
      };
    } catch (error) {
      this.logger.error('获取模型指标失败:', error);
      throw error;
    }
  }

  /**
   * 获取推理日志
   */
  async getInferenceLogs(id: string, page: number = 1, limit: number = 20): Promise<any> {
    try {
      const model = await this.findById(id);

      const [logs, total] = await this.inferenceLogRepository.findAndCount({
        where: { modelId: id },
        order: { createdAt: 'DESC' },
        skip: (page - 1) * limit,
        take: limit
      });

      return {
        modelId: id,
        modelName: model.name,
        logs,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      this.logger.error('获取推理日志失败:', error);
      throw error;
    }
  }

  /**
   * 异步加载模型
   */
  private async loadModelAsync(modelId: string): Promise<void> {
    try {
      await this.loadModel(modelId);
    } catch (error) {
      this.logger.error(`异步加载模型失败: ${modelId}`, error);
    }
  }

  /**
   * 卸载模型
   */
  async unloadModel(modelId: string): Promise<void> {
    try {
      if (this.loadedModels.has(modelId)) {
        const loadedModel = this.loadedModels.get(modelId)!;

        // 释放模型资源
        if (loadedModel.instance && typeof loadedModel.instance.destroy === 'function') {
          await loadedModel.instance.destroy();
        }

        this.loadedModels.delete(modelId);
        this.logger.log(`模型卸载成功: ${modelId}`);
      }
    } catch (error) {
      this.logger.error(`模型卸载失败: ${modelId}`, error);
    }
  }

  /**
   * 验证模型文件
   */
  private async validateModelFile(filePath: string): Promise<void> {
    try {
      await fs.access(filePath, fs.constants.F_OK);
      const stats = await fs.stat(filePath);

      if (!stats.isFile()) {
        throw new BadRequestException('指定路径不是文件');
      }

      if (stats.size === 0) {
        throw new BadRequestException('模型文件为空');
      }

    } catch (error) {
      if (error instanceof Error && (error as any).code === 'ENOENT') {
        throw new BadRequestException('模型文件不存在');
      }
      throw error;
    }
  }

  /**
   * 计算文件哈希
   */
  private async calculateFileHash(filePath: string): Promise<string> {
    try {
      const fileBuffer = await fs.readFile(filePath);
      return crypto.createHash('sha256').update(fileBuffer).digest('hex');
    } catch (error) {
      this.logger.error('计算文件哈希失败:', error);
      throw new InternalServerErrorException('计算文件哈希失败');
    }
  }

  /**
   * 加载模型实例
   */
  private async loadModelInstance(model: AIModel): Promise<any> {
    // 这里应该根据模型类型加载相应的模型实例
    // 示例实现，实际应该根据具体的AI框架进行实现

    switch (model.type) {
      case ModelType.BERT:
        return await this.loadBertModel(model);
      case ModelType.GPT:
        return await this.loadGptModel(model);
      case ModelType.STABLE_DIFFUSION:
        return await this.loadStableDiffusionModel(model);
      default:
        throw new BadRequestException(`不支持的模型类型: ${model.type}`);
    }
  }

  /**
   * 加载BERT模型
   */
  private async loadBertModel(model: AIModel): Promise<any> {
    // 实现BERT模型加载逻辑
    return {
      type: 'bert',
      modelPath: model.filePath,
      config: model.config,
      predict: async (input: any) => {
        // 模拟推理
        return {
          output: `BERT推理结果: ${JSON.stringify(input)}`,
          confidence: 0.95,
          tokens: 100
        };
      },
      destroy: async () => {
        // 清理资源
      }
    };
  }

  /**
   * 加载GPT模型
   */
  private async loadGptModel(model: AIModel): Promise<any> {
    // 实现GPT模型加载逻辑
    return {
      type: 'gpt',
      modelPath: model.filePath,
      config: model.config,
      predict: async (input: any) => {
        // 模拟推理
        return {
          output: `GPT推理结果: ${JSON.stringify(input)}`,
          confidence: 0.92,
          tokens: 150
        };
      },
      destroy: async () => {
        // 清理资源
      }
    };
  }

  /**
   * 加载Stable Diffusion模型
   */
  private async loadStableDiffusionModel(model: AIModel): Promise<any> {
    // 实现Stable Diffusion模型加载逻辑
    return {
      type: 'stable_diffusion',
      modelPath: model.filePath,
      config: model.config,
      predict: async (input: any) => {
        // 模拟推理
        return {
          output: `图像生成结果: ${JSON.stringify(input)}`,
          confidence: 0.88,
          tokens: 0
        };
      },
      destroy: async () => {
        // 清理资源
      }
    };
  }

  /**
   * 计算内存使用量
   */
  private async calculateMemoryUsage(instance: any): Promise<number> {
    // 实现内存使用量计算
    // 这里返回模拟值，实际应该根据具体实现计算
    return Math.floor(Math.random() * 1000) + 500; // 500-1500MB
  }

  /**
   * 预处理输入
   */
  private async preprocessInput(input: any, config: any): Promise<any> {
    // 实现输入预处理逻辑
    return input;
  }

  /**
   * 执行推理
   */
  private async executeInference(instance: any, input: any): Promise<any> {
    if (!instance || typeof instance.predict !== 'function') {
      throw new InternalServerErrorException('模型实例无效');
    }

    return await instance.predict(input);
  }

  /**
   * 后处理输出
   */
  private async postprocessOutput(output: any, config: any): Promise<any> {
    // 实现输出后处理逻辑
    return output.output || output;
  }

  /**
   * 记录推理日志
   */
  private async logInference(
    modelId: string,
    request: InferenceRequestDto,
    result: InferenceResult | null,
    success: boolean,
    errorMessage?: string
  ): Promise<void> {
    try {
      const log = this.inferenceLogRepository.create({
        modelId,
        requestId: result?.metadata?.requestId || this.generateRequestId(),
        inputData: request.inputData || request.input,
        outputData: result?.result,
        responseTime: result?.processingTime || 0,
        status: success ? InferenceStatus.COMPLETED : InferenceStatus.FAILED,
        errorMessage,
        startTime: new Date(),
        endTime: new Date()
      });

      await this.inferenceLogRepository.save(log);
    } catch (error) {
      this.logger.error('记录推理日志失败:', error);
    }
  }

  /**
   * 更新性能指标
   */
  private async updatePerformanceMetrics(
    modelId: string,
    responseTime: number,
    isError: boolean
  ): Promise<void> {
    try {
      if (!this.performanceMetrics.has(modelId)) {
        this.performanceMetrics.set(modelId, {
          requestCount: 0,
          totalResponseTime: 0,
          errorCount: 0,
          lastReset: new Date()
        });
      }

      const metrics = this.performanceMetrics.get(modelId)!;
      metrics.requestCount++;
      metrics.totalResponseTime += responseTime;

      if (isError) {
        metrics.errorCount++;
      }

    } catch (error) {
      this.logger.error('更新性能指标失败:', error);
    }
  }

  /**
   * 预加载默认模型
   */
  private async preloadDefaultModels(): Promise<void> {
    try {
      const defaultModels = await this.modelRepository.find({
        where: { isDefault: true, isActive: true }
      });

      for (const model of defaultModels) {
        this.loadModelAsync(model.id);
      }

      this.logger.log(`开始预加载 ${defaultModels.length} 个默认模型`);
    } catch (error) {
      this.logger.error('预加载默认模型失败:', error);
    }
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    // 每分钟检查一次性能指标
    setInterval(() => {
      this.checkPerformanceMetrics();
    }, 60000);
  }

  /**
   * 检查性能指标
   */
  private checkPerformanceMetrics(): void {
    for (const [modelId, metrics] of this.performanceMetrics) {
      const errorRate = metrics.requestCount > 0 ? (metrics.errorCount / metrics.requestCount) * 100 : 0;
      const avgResponseTime = metrics.requestCount > 0 ? metrics.totalResponseTime / metrics.requestCount : 0;

      if (errorRate > 10) { // 错误率超过10%
        this.logger.warn(`模型 ${modelId} 错误率过高: ${errorRate.toFixed(2)}%`);
      }

      if (avgResponseTime > 5000) { // 平均响应时间超过5秒
        this.logger.warn(`模型 ${modelId} 响应时间过长: ${avgResponseTime.toFixed(2)}ms`);
      }
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `inf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 定时清理任务
   */
  @Cron(CronExpression.EVERY_HOUR)
  async cleanupTask(): Promise<void> {
    try {
      // 清理长时间未使用的模型
      await this.cleanupUnusedModels();

      // 清理旧的推理日志
      await this.cleanupOldLogs();

      // 重置性能指标
      await this.resetPerformanceMetrics();

    } catch (error) {
      this.logger.error('定时清理任务失败:', error);
    }
  }

  /**
   * 清理未使用的模型
   */
  private async cleanupUnusedModels(): Promise<void> {
    const unusedThreshold = 2 * 60 * 60 * 1000; // 2小时
    const now = new Date();

    for (const [modelId, loadedModel] of this.loadedModels) {
      const timeSinceLastUsed = now.getTime() - loadedModel.lastUsed.getTime();

      if (timeSinceLastUsed > unusedThreshold) {
        await this.unloadModel(modelId);
        this.logger.log(`清理未使用模型: ${modelId}`);
      }
    }
  }

  /**
   * 清理旧日志
   */
  private async cleanupOldLogs(): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 7); // 保留7天

    await this.inferenceLogRepository
      .createQueryBuilder()
      .delete()
      .where('timestamp < :cutoffDate', { cutoffDate })
      .execute();
  }

  /**
   * 重置性能指标
   */
  private async resetPerformanceMetrics(): Promise<void> {
    for (const [modelId, metrics] of this.performanceMetrics) {
      const now = new Date();
      const timeSinceReset = now.getTime() - metrics.lastReset.getTime();

      // 每24小时重置一次
      if (timeSinceReset > 24 * 60 * 60 * 1000) {
        metrics.requestCount = 0;
        metrics.totalResponseTime = 0;
        metrics.errorCount = 0;
        metrics.lastReset = now;
      }
    }
  }

  /**
   * 获取模型健康状态
   */
  async getModelHealth(modelId: string): Promise<ModelHealthStatus> {
    try {
      const model = await this.findModel(modelId);
      const loadedModel = this.loadedModels.get(modelId);
      const metrics = this.performanceMetrics.get(modelId);

      const issues: string[] = [];
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';

      // 检查模型状态
      if (model.status !== ModelStatus.READY) {
        issues.push(`模型状态异常: ${model.status}`);
        status = 'critical';
      }

      // 检查错误率
      const errorRate = model.getErrorRate();
      if (errorRate > 10) {
        issues.push(`错误率过高: ${errorRate.toFixed(2)}%`);
        status = status === 'critical' ? 'critical' : 'warning';
      }

      // 检查响应时间
      if (model.averageResponseTime && model.averageResponseTime > 5000) {
        issues.push(`响应时间过长: ${model.averageResponseTime.toFixed(2)}ms`);
        status = status === 'critical' ? 'critical' : 'warning';
      }

      // 检查内存使用
      if (loadedModel && loadedModel.memoryUsage > 2048) {
        issues.push(`内存使用过高: ${loadedModel.memoryUsage}MB`);
        status = status === 'critical' ? 'critical' : 'warning';
      }

      return {
        modelId,
        status,
        issues,
        metrics: {
          responseTime: model.averageResponseTime || 0,
          errorRate,
          memoryUsage: loadedModel?.memoryUsage || 0,
          lastUsed: model.lastUsedAt || new Date()
        }
      };

    } catch (error) {
      this.logger.error(`获取模型健康状态失败: ${modelId}`, error);
      return {
        modelId,
        status: 'critical',
        issues: ['无法获取模型状态'],
        metrics: {
          responseTime: 0,
          errorRate: 100,
          memoryUsage: 0,
          lastUsed: new Date()
        }
      };
    }
  }
}
