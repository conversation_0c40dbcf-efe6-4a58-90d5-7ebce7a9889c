# 角色环境交互示例

## 简介

本示例展示了如何使用DL（Digital Learning）引擎的角色环境交互功能，包括环境感知、物理交互、动作录制和回放等功能。通过本示例，您可以了解如何设置角色控制器、创建环境响应规则、使用预设以及录制和回放角色动作。

## 功能特性

- **环境感知**：角色可以感知周围的环境类型、天气、光照、温度等参数
- **物理交互**：角色可以与环境中的物理对象进行交互，如推动、拉动、抓取等
- **环境响应**：角色可以根据环境变化做出相应的反应，如播放动画、音效等
- **动作录制**：可以录制角色的动作序列，包括动画、输入和变换
- **动作回放**：可以回放录制的动作序列，支持速度调整、暂停和循环

## 使用说明

1. 打开示例项目
2. 在场景中选择角色实体
3. 添加高级角色控制器组件和环境感知组件
4. 配置环境感知参数和响应规则
5. 使用动作录制功能录制角色动作
6. 使用动作回放功能回放录制的动作

## 步骤详解

### 步骤1：添加高级角色控制器

首先，我们需要为角色添加高级角色控制器，使其能够执行各种动作。

```typescript
// 导入高级角色控制器
import { AdvancedCharacterController } from '../../engine/src/avatar/controllers';

// 获取角色实体
const character = scene.getEntityByName('Character');

// 添加高级角色控制器
const controller = new AdvancedCharacterController(character, {
  walkSpeed: 2.0,
  runSpeed: 5.0,
  jumpForce: 5.0,
  gravity: 9.8,
  usePhysics: true,
  useStateMachine: true,
  useBlendSpace: true,
  useIK: true,
  useEnvironmentAwareness: true
});

// 添加控制器到角色
character.addComponent(controller);
```

### 步骤2：添加环境感知组件

接下来，我们需要添加环境感知组件，使角色能够感知周围的环境。

```typescript
// 导入环境感知组件
import { EnvironmentAwarenessComponent } from '../../engine/src/environment';

// 添加环境感知组件
const awarenessComponent = new EnvironmentAwarenessComponent(character, {
  awarenessRange: 50, // 感知范围（米）
  updateFrequency: 1000, // 更新频率（毫秒）
  debug: true, // 启用调试模式
  autoDetect: true // 自动检测环境
});

// 添加组件到角色
character.addComponent(awarenessComponent);
```

### 步骤3：添加环境响应组件

然后，我们需要添加环境响应组件，定义角色如何响应环境变化。

```typescript
// 导入环境响应组件和相关类型
import { 
  EnvironmentResponseComponent, 
  ResponseType, 
  ResponsePriority 
} from '../../engine/src/environment';

// 创建环境响应组件
const responseComponent = new EnvironmentResponseComponent(character, {
  autoRespond: true, // 自动响应
  debug: true // 启用调试模式
});

// 添加组件到角色
character.addComponent(responseComponent);
```

### 步骤4：录制角色动作

现在，我们可以使用动作录制功能录制角色的动作序列。

```typescript
// 导入动作控制系统
import { ActionControlSystem } from '../../engine/src/avatar/controllers';

// 获取动作控制系统
const actionControlSystem = world.getSystem(ActionControlSystem.type);

// 开始录制
actionControlSystem.startRecording(character, '行走动作');

// 执行一些动作...
// 例如，让角色行走、跳跃等

// 停止录制
const recording = actionControlSystem.stopRecording(character);

// 保存录制
if (recording) {
  console.log('录制完成:', recording);
  
  // 可以将录制保存到文件
  // ActionRecorder.saveToFile(recording, 'walking_action.json');
}
```

### 步骤5：回放角色动作

最后，我们可以回放录制的动作序列。

```typescript
// 回放录制
if (recording) {
  // 播放录制
  actionControlSystem.playRecording(character, recording.id, 1.0);
  
  // 也可以从文件加载录制
  // const loadedRecording = await ActionRecorder.loadFromFile(file);
  // actionControlSystem.addRecording(loadedRecording);
  // actionControlSystem.playRecording(character, loadedRecording.id);
}
```

## 高级功能

### 物理交互

本示例还展示了如何使用物理交互功能，使角色能够与环境中的物理对象进行交互。

```typescript
// 导入物理交互组件
import { PhysicsInteractionComponent } from '../../engine/src/physics';

// 添加物理交互组件
const interactionComponent = new PhysicsInteractionComponent(character, {
  interactionRange: 2.0, // 交互范围（米）
  maxForce: 1000, // 最大力（牛顿）
  debug: true // 启用调试模式
});

// 添加组件到角色
character.addComponent(interactionComponent);

// 与物体交互
interactionComponent.grabObject(targetObject);
interactionComponent.pushObject(targetObject, direction, force);
interactionComponent.pullObject(targetObject, force);
```

### 环境预设

本示例还提供了一系列环境预设，可以快速应用到场景中。

```typescript
// 导入环境预设
import { 
  createRainyWeatherPreset, 
  createSnowyWeatherPreset, 
  createHotWeatherPreset 
} from '../../engine/src/environment/presets';

// 应用雨天预设
const rainyPreset = createRainyWeatherPreset();
scene.applyEnvironmentPreset(rainyPreset);

// 应用雪天预设
const snowyPreset = createSnowyWeatherPreset();
scene.applyEnvironmentPreset(snowyPreset);

// 应用炎热天气预设
const hotPreset = createHotWeatherPreset();
scene.applyEnvironmentPreset(hotPreset);
```

## 示例代码

完整的示例代码可以在 `examples/character-environment-interaction/index.ts` 文件中找到。
