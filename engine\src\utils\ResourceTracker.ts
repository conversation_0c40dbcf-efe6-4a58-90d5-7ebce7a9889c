/**
 * 资源跟踪器
 * 用于跟踪Three.js资源的内存占用
 */
import * as THREE from 'three';
import { MemoryAnalyzer, ResourceType } from './MemoryAnalyzer';
import { Debug } from './Debug';

/**
 * 资源跟踪器配置接口
 */
export interface ResourceTrackerConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用调试输出 */
  debug?: boolean;
  /** 是否自动跟踪所有资源 */
  autoTrackAll?: boolean;
  /** 是否自动计算资源大小 */
  autoCalculateSize?: boolean;
}

/**
 * 资源跟踪器类
 * 用于跟踪Three.js资源的内存占用
 */
export class ResourceTracker {
  private static instance: ResourceTracker;
  
  /** 配置 */
  private config: ResourceTrackerConfig = {
    enabled: false,
    debug: false,
    autoTrackAll: true,
    autoCalculateSize: true
  };
  
  /** 内存分析器 */
  private memoryAnalyzer: MemoryAnalyzer;
  
  /** 已跟踪的资源映射 */
  private trackedResources: Map<string, THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry> = new Map();
  
  /** 资源ID计数器 */
  private idCounter: number = 0;
  
  /**
   * 获取单例实例
   */
  public static getInstance(): ResourceTracker {
    if (!ResourceTracker.instance) {
      ResourceTracker.instance = new ResourceTracker();
    }
    return ResourceTracker.instance;
  }
  
  /**
   * 私有构造函数
   */
  private constructor() {
    this.memoryAnalyzer = MemoryAnalyzer.getInstance();
  }
  
  /**
   * 配置资源跟踪器
   * @param config 配置
   */
  public configure(config: ResourceTrackerConfig): void {
    this.config = {
      ...this.config,
      ...config
    };
    
    if (this.config.debug) {
      Debug.log('资源跟踪器', '配置已更新', this.config);
    }
  }
  
  /**
   * 启动资源跟踪器
   */
  public start(): void {
    if (!this.config.enabled) {
      return;
    }
    
    // 确保内存分析器已启动
    this.memoryAnalyzer.configure({
      enabled: true,
      enableLeakDetection: true
    });
    this.memoryAnalyzer.start();
    
    if (this.config.debug) {
      Debug.log('资源跟踪器', '已启动');
    }
    
    // 如果配置为自动跟踪所有资源，则覆盖Three.js的创建方法
    if (this.config.autoTrackAll) {
      this.overrideThreeMethods();
    }
  }
  
  /**
   * 停止资源跟踪器
   */
  public stop(): void {
    if (this.config.debug) {
      Debug.log('资源跟踪器', '已停止');
    }
  }
  
  /**
   * 跟踪资源
   * @param resource Three.js资源
   * @param name 资源名称（可选）
   * @returns 资源ID
   */
  public track(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry, name?: string): string {
    if (!this.config.enabled) {
      return '';
    }
    
    // 生成资源ID
    const resourceId = this.generateResourceId(resource);
    
    // 如果已经跟踪了该资源，则更新访问时间
    if (this.trackedResources.has(resourceId)) {
      const existingResource = this.memoryAnalyzer.getResource(resourceId);
      if (existingResource) {
        this.memoryAnalyzer.updateResource(resourceId, {
          refCount: existingResource.refCount + 1
        });
      }
      return resourceId;
    }
    
    // 添加到跟踪映射
    this.trackedResources.set(resourceId, resource);
    
    // 确定资源类型
    const resourceType = this.getResourceType(resource);
    
    // 计算资源大小
    const resourceSize = this.calculateResourceSize(resource);
    
    // 确定资源名称
    const resourceName = name || this.getResourceName(resource);
    
    // 注册到内存分析器
    this.memoryAnalyzer.registerResource({
      id: resourceId,
      name: resourceName,
      type: resourceType,
      size: resourceSize,
      refCount: 1
    });
    
    if (this.config.debug) {
      Debug.log('资源跟踪器', `已跟踪资源: ${resourceId} (${resourceType}, ${this.formatBytes(resourceSize)})`);
    }
    
    return resourceId;
  }
  
  /**
   * 取消跟踪资源
   * @param resourceId 资源ID
   */
  public untrack(resourceId: string): void {
    if (!this.config.enabled || !this.trackedResources.has(resourceId)) {
      return;
    }
    
    // 从跟踪映射中移除
    this.trackedResources.delete(resourceId);
    
    // 标记为已释放
    this.memoryAnalyzer.disposeResource(resourceId);
    
    if (this.config.debug) {
      Debug.log('资源跟踪器', `已取消跟踪资源: ${resourceId}`);
    }
  }
  
  /**
   * 取消跟踪资源
   * @param resource Three.js资源
   */
  public untrackResource(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): void {
    const resourceId = this.getResourceId(resource);
    if (resourceId) {
      this.untrack(resourceId);
    }
  }
  
  /**
   * 获取资源ID
   * @param resource Three.js资源
   * @returns 资源ID，如果未跟踪则返回空字符串
   */
  public getResourceId(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): string {
    for (const [id, trackedResource] of this.trackedResources.entries()) {
      if (trackedResource === resource) {
        return id;
      }
    }
    return '';
  }
  
  /**
   * 生成资源ID
   * @param resource Three.js资源
   * @returns 资源ID
   */
  private generateResourceId(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): string {
    // 尝试使用资源的UUID
    if ('uuid' in resource && typeof resource.uuid === 'string') {
      return resource.uuid;
    }
    
    // 如果没有UUID，则使用递增计数器
    return `resource-${++this.idCounter}`;
  }
  
  /**
   * 获取资源类型
   * @param resource Three.js资源
   * @returns 资源类型
   */
  private getResourceType(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): ResourceType {
    if (resource instanceof THREE.Texture) {
      return ResourceType.TEXTURE;
    } else if (resource instanceof THREE.BufferGeometry) {
      return ResourceType.GEOMETRY;
    } else if (resource instanceof THREE.Material) {
      return ResourceType.MATERIAL;
    } else if (resource instanceof THREE.Object3D) {
      if (resource instanceof THREE.Mesh || resource instanceof THREE.SkinnedMesh) {
        return ResourceType.MODEL;
      }
      return ResourceType.OTHER;
    }
    return ResourceType.OTHER;
  }
  
  /**
   * 获取资源名称
   * @param resource Three.js资源
   * @returns 资源名称
   */
  private getResourceName(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): string {
    if ('name' in resource && typeof resource.name === 'string' && resource.name) {
      return resource.name;
    }
    
    const type = this.getResourceType(resource);
    return `${type}-${this.idCounter}`;
  }
  
  /**
   * 计算资源大小
   * @param resource Three.js资源
   * @returns 资源大小（字节）
   */
  private calculateResourceSize(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): number {
    if (!this.config.autoCalculateSize) {
      return 0;
    }
    
    try {
      if (resource instanceof THREE.Texture) {
        return this.calculateTextureSize(resource);
      } else if (resource instanceof THREE.BufferGeometry) {
        return this.calculateGeometrySize(resource);
      } else if (resource instanceof THREE.Material) {
        return this.calculateMaterialSize(resource);
      } else if (resource instanceof THREE.Object3D) {
        return this.calculateObject3DSize(resource);
      }
    } catch (error) {
      if (this.config.debug) {
        Debug.warn('资源跟踪器', `计算资源大小时出错: ${error}`);
      }
    }
    
    return 0;
  }
  
  /**
   * 计算纹理大小
   * @param texture 纹理
   * @returns 纹理大小（字节）
   */
  private calculateTextureSize(texture: THREE.Texture): number {
    if (!texture.image) {
      return 0;
    }
    
    let width = 0;
    let height = 0;
    
    if (texture.image instanceof HTMLImageElement || texture.image instanceof HTMLCanvasElement || texture.image instanceof HTMLVideoElement) {
      width = texture.image.width;
      height = texture.image.height;
    } else if (texture.image instanceof ImageData) {
      width = texture.image.width;
      height = texture.image.height;
    } else if (Array.isArray(texture.image)) {
      // 立方体贴图
      const firstImage = texture.image[0];
      if (firstImage) {
        width = firstImage.width || 0;
        height = firstImage.height || 0;
        return width * height * 4 * 6; // 6个面
      }
      return 0;
    } else {
      return 0;
    }
    
    // 估算纹理内存（RGBA，每像素4字节）
    return width * height * 4;
  }
  
  /**
   * 计算几何体大小
   * @param geometry 几何体
   * @returns 几何体大小（字节）
   */
  private calculateGeometrySize(geometry: THREE.BufferGeometry): number {
    let totalSize = 0;
    
    // 计算所有属性的大小
    for (const name in geometry.attributes) {
      const attribute = geometry.attributes[name];
      if (attribute && attribute.array) {
        // 检查是否是 TypedArray
        if ('byteLength' in attribute.array) {
          totalSize += (attribute.array as any).byteLength;
        } else {
          // 如果不是 TypedArray，估算大小
          totalSize += attribute.array.length * 4; // 假设每个元素4字节
        }
      }
    }

    // 计算索引的大小
    if (geometry.index && geometry.index.array) {
      // 检查是否是 TypedArray
      if ('byteLength' in geometry.index.array) {
        totalSize += (geometry.index.array as any).byteLength;
      } else {
        // 如果不是 TypedArray，估算大小
        totalSize += geometry.index.array.length * 4; // 假设每个元素4字节
      }
    }
    
    return totalSize;
  }
  
  /**
   * 计算材质大小
   * @param material 材质
   * @returns 材质大小（字节）
   */
  private calculateMaterialSize(material: THREE.Material): number {
    // 材质本身的大小很难精确计算，这里使用估算值
    let size = 1024; // 基础大小
    
    // 如果是标准材质，检查其纹理
    if (material instanceof THREE.MeshStandardMaterial) {
      if (material.map) size += this.calculateTextureSize(material.map);
      if (material.normalMap) size += this.calculateTextureSize(material.normalMap);
      if (material.roughnessMap) size += this.calculateTextureSize(material.roughnessMap);
      if (material.metalnessMap) size += this.calculateTextureSize(material.metalnessMap);
      if (material.aoMap) size += this.calculateTextureSize(material.aoMap);
      if (material.emissiveMap) size += this.calculateTextureSize(material.emissiveMap);
      if (material.displacementMap) size += this.calculateTextureSize(material.displacementMap);
    } else if (material instanceof THREE.MeshBasicMaterial || material instanceof THREE.MeshLambertMaterial || material instanceof THREE.MeshPhongMaterial) {
      if ('map' in material && material.map) size += this.calculateTextureSize(material.map);
    }
    
    return size;
  }
  
  /**
   * 计算3D对象大小
   * @param object 3D对象
   * @returns 3D对象大小（字节）
   */
  private calculateObject3DSize(object: THREE.Object3D): number {
    let size = 1024; // 基础大小
    
    // 如果是网格，计算几何体和材质的大小
    if (object instanceof THREE.Mesh || object instanceof THREE.SkinnedMesh) {
      if (object.geometry) {
        size += this.calculateGeometrySize(object.geometry);
      }
      
      if (object.material) {
        if (Array.isArray(object.material)) {
          for (const material of object.material) {
            size += this.calculateMaterialSize(material);
          }
        } else {
          size += this.calculateMaterialSize(object.material);
        }
      }
    }
    
    return size;
  }
  
  /**
   * 覆盖Three.js方法以自动跟踪资源
   */
  private overrideThreeMethods(): void {
    // 这里可以覆盖Three.js的创建和销毁方法，以自动跟踪资源
    // 例如，覆盖TextureLoader.load方法，在加载纹理后自动跟踪
    // 覆盖Object3D.dispose方法，在销毁对象时自动取消跟踪
    // 这部分实现较为复杂，需要根据具体需求定制
  }
  
  /**
   * 格式化字节数
   * @param bytes 字节数
   * @returns 格式化后的字符串
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
