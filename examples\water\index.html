<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>水体系统示例</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    }
    canvas {
      display: block;
      width: 100%;
      height: 100%;
    }
    #loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #000;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      z-index: 1000;
    }
    #loading h1 {
      font-size: 2em;
      margin-bottom: 20px;
    }
    #loading .progress {
      width: 300px;
      height: 20px;
      background-color: #333;
      border-radius: 10px;
      overflow: hidden;
    }
    #loading .progress-bar {
      height: 100%;
      background-color: #0088ff;
      width: 0%;
      transition: width 0.3s ease;
    }
    #instructions {
      position: fixed;
      bottom: 10px;
      right: 10px;
      padding: 10px;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      border-radius: 5px;
      font-size: 14px;
      max-width: 300px;
    }
    #instructions h3 {
      margin-top: 0;
      margin-bottom: 10px;
    }
    #instructions ul {
      margin: 0;
      padding-left: 20px;
    }
    #instructions li {
      margin-bottom: 5px;
    }
  </style>
</head>
<body>
  <div id="loading">
    <h1>水体系统示例</h1>
    <div class="progress">
      <div class="progress-bar" id="progress-bar"></div>
    </div>
    <p id="loading-text">加载中...</p>
  </div>

  <div id="instructions">
    <h3>操作说明</h3>
    <ul>
      <li><strong>左右箭头键</strong>：切换水体示例</li>
      <li><strong>R 键</strong>：重置物理对象</li>
      <li><strong>A 键</strong>：添加随机物理对象</li>
      <li><strong>鼠标拖动</strong>：旋转视角</li>
      <li><strong>鼠标滚轮</strong>：缩放视角</li>
    </ul>
  </div>

  <script type="module">
    import { WaterSystemExample } from './WaterSystemExample.ts';

    // 模拟加载进度
    let progress = 0;
    const progressBar = document.getElementById('progress-bar');
    const loadingText = document.getElementById('loading-text');
    const loadingElement = document.getElementById('loading');

    const updateProgress = () => {
      progress += Math.random() * 10;
      if (progress > 100) progress = 100;
      progressBar.style.width = `${progress}%`;
      loadingText.textContent = `加载中... ${Math.floor(progress)}%`;

      if (progress < 100) {
        setTimeout(updateProgress, 200);
      } else {
        loadingText.textContent = '加载完成！';
        setTimeout(() => {
          loadingElement.style.opacity = '0';
          loadingElement.style.transition = 'opacity 0.5s ease';
          setTimeout(() => {
            loadingElement.style.display = 'none';
            // 启动示例
            startExample();
          }, 500);
        }, 500);
      }
    };

    // 启动示例
    const startExample = () => {
      try {
        const example = new WaterSystemExample();
        example.start();

        // 添加窗口关闭事件
        window.addEventListener('beforeunload', () => {
          example.stop();
        });
      } catch (error) {
        console.error('启动示例时出错:', error);
        alert(`启动示例时出错: ${error.message}`);
      }
    };

    // 开始加载
    updateProgress();
  </script>
</body>
</html>
