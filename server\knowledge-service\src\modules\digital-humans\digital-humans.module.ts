import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DigitalHuman } from '../../entities/digital-human.entity';
import { DigitalHumansController } from './digital-humans.controller';
import { DigitalHumansService } from './digital-humans.service';

@Module({
  imports: [TypeOrmModule.forFeature([DigitalHuman])],
  controllers: [DigitalHumansController],
  providers: [DigitalHumansService],
  exports: [DigitalHumansService],
})
export class DigitalHumansModule {}
