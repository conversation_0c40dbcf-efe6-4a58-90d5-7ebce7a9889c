/**
 * 面部动画预设系统
 * 管理面部动画预设和模板
 */
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { FacialExpressionType } from '../components/FacialAnimationComponent';
/**
 * 面部动画预设类型
 */
export declare enum FacialAnimationPresetType {
    /** 标准表情 */
    STANDARD = "standard",
    /** 文化特定表情 */
    CULTURAL = "cultural",
    /** 情感组合 */
    EMOTION_COMBO = "emotion_combo",
    /** 动画序列 */
    ANIMATION_SEQUENCE = "animation_sequence",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 面部动画预设配置
 */
export interface FacialAnimationPresetConfig {
    /** 预设名称 */
    name: string;
    /** 预设类型 */
    type: FacialAnimationPresetType;
    /** 表情类型 */
    expression?: FacialExpressionType;
    /** 表情权重 */
    weight?: number;
    /** 表情组合 */
    expressionCombos?: {
        expression: FacialExpressionType;
        weight: number;
    }[];
    /** 动画序列 */
    animationSequence?: {
        expression: FacialExpressionType;
        weight: number;
        duration: number;
    }[];
    /** 文化标识 */
    culture?: string;
    /** 标签 */
    tags?: string[];
    /** 描述 */
    description?: string;
    /** 作者 */
    author?: string;
    /** 创建日期 */
    createdAt?: Date;
    /** 更新日期 */
    updatedAt?: Date;
    /** 自定义数据 */
    customData?: any;
}
/**
 * 面部动画预设系统配置
 */
export interface FacialAnimationPresetSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否自动加载预设 */
    autoLoadPresets?: boolean;
    /** 预设路径 */
    presetsPath?: string;
    /** 默认文化 */
    defaultCulture?: string;
}
/**
 * 面部动画预设系统
 */
export declare class FacialAnimationPresetSystem extends System {
    /** 系统类型 */
    static readonly type = "FacialAnimationPreset";
    /** 预设 */
    private presets;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<FacialAnimationPresetSystemConfig>);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 加载默认预设
     */
    private loadDefaultPresets;
    /**
     * 注册标准表情预设
     */
    private registerStandardPresets;
    /**
     * 注册文化特定表情预设
     */
    private registerCulturalPresets;
    /**
     * 注册情感组合预设
     */
    private registerEmotionComboPresets;
    /**
     * 注册动画序列预设
     */
    private registerAnimationSequencePresets;
    /**
     * 注册预设
     * @param preset 预设配置
     * @returns 是否成功注册
     */
    registerPreset(preset: FacialAnimationPresetConfig): boolean;
    /**
     * 更新预设
     * @param name 预设名称
     * @param preset 预设配置
     * @returns 是否成功更新
     */
    updatePreset(name: string, preset: Partial<FacialAnimationPresetConfig>): boolean;
    /**
     * 删除预设
     * @param name 预设名称
     * @returns 是否成功删除
     */
    deletePreset(name: string): boolean;
    /**
     * 获取预设
     * @param name 预设名称
     * @returns 预设配置
     */
    getPreset(name: string): FacialAnimationPresetConfig | null;
    /**
     * 获取所有预设
     * @returns 预设配置数组
     */
    getAllPresets(): FacialAnimationPresetConfig[];
    /**
     * 获取指定类型的预设
     * @param type 预设类型
     * @returns 预设配置数组
     */
    getPresetsByType(type: FacialAnimationPresetType): FacialAnimationPresetConfig[];
    /**
     * 获取指定文化的预设
     * @param culture 文化标识
     * @returns 预设配置数组
     */
    getPresetsByCulture(culture: string): FacialAnimationPresetConfig[];
    /**
     * 获取指定标签的预设
     * @param tag 标签
     * @returns 预设配置数组
     */
    getPresetsByTag(tag: string): FacialAnimationPresetConfig[];
    /**
     * 应用预设到实体
     * @param entity 实体
     * @param presetName 预设名称
     * @returns 是否成功应用
     */
    applyPreset(entity: Entity, presetName: string): boolean;
    /**
     * 播放动画序列
     * @param entity 实体
     * @param sequence 动画序列
     */
    private playAnimationSequence;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
