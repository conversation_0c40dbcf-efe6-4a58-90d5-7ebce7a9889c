#node
node_modules/

#vim
*.swp
*.swo

# npm / yarn / pnpm
package-lock.json
yarn.lock
yarn-error.log
.yarn/
pnpm-lock.yaml

#IDEA project
*.iml

#local files
.env*
!.env.local.default

#config
config/*
!config/default.json
!config/server.json
!config/custom-environment-variables.json
!config/development.json
!config/dev.json

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output
coverage/

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

**/dl-engine-secrets.env
**/dl-engine-dev-secrets.env
lib/
bin/

# JetBrains WebStorm/PhpStorm
.idea

.history
.history/*
packages/server/upload/
packages/server/upload_test/

.dccache
.DS_Store
*/.DS_Store

*.blend1
*.ts.bak

# ignore all npm dist cti outputs
/packages/client-core/src/index.ts
/packages/common/src/index.ts
/packages/editor/src/index.ts
/packages/projects/index.ts
/packages/server-core/src/index.ts
/packages/client/public/manifest.webmanifest
/packages/client/public/root-cookie-accessor.html
/packages/client/public/service-*
/packages/client/public/workbox-*
/packages/client/public/projects/*
/packages/projects/projects/*

# Visual Studio Code settings
project-package-jsons/*

feathers/*
packages/ui/build/*
packages/ui/storybook-static*
/packages/ui/tests/jest-test-results.json
packages/ui/tests/jest-test-results.json

/.temp

# tailscale cert/key
certs/tailscale

**/credentials
.aider*
