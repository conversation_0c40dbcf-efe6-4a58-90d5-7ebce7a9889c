# AI模型服务

AI模型管理和推理服务，提供完整的AI模型生命周期管理功能。

## 功能特性

- 🤖 **模型管理**: 支持多种AI模型类型的上传、存储和版本管理
- 🚀 **推理服务**: 高性能的模型推理API，支持批量处理
- 📊 **性能监控**: 实时监控模型性能指标和系统资源使用
- 🔄 **缓存优化**: 智能缓存策略，提升推理响应速度
- 📈 **指标收集**: 详细的推理日志和性能指标收集
- 🛡️ **安全保障**: 输入验证、模型验证和访问控制
- 🔧 **微服务架构**: 支持分布式部署和服务发现

## 支持的模型类型

- BERT (文本理解)
- GPT (文本生成)
- T5 (文本转换)
- RoBERTa (文本分类)
- DistilBERT (轻量级文本理解)
- ALBERT (高效文本理解)
- XLNet (双向文本理解)
- BART (文本生成和理解)
- Stable Diffusion (图像生成)
- Whisper (语音识别)
- CLIP (多模态理解)
- 自定义模型

## 快速开始

### 1. 环境要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- Redis >= 6.0
- TypeScript >= 5.0

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境

复制环境配置文件并修改配置：

```bash
cp .env.example .env
```

### 4. 数据库初始化

确保MySQL和Redis服务正在运行，然后启动应用：

```bash
npm run start:dev
```

### 5. API文档

启动服务后，访问 Swagger API 文档：

```
http://localhost:3008/api/docs
```

## API 接口

### 模型管理

- `GET /api/v1/models` - 获取模型列表
- `GET /api/v1/models/:id` - 获取模型详情
- `POST /api/v1/models` - 创建新模型
- `PUT /api/v1/models/:id` - 更新模型信息
- `DELETE /api/v1/models/:id` - 删除模型

### 模型操作

- `POST /api/v1/models/:id/upload` - 上传模型文件
- `POST /api/v1/models/:id/load` - 加载模型到内存
- `POST /api/v1/models/:id/unload` - 卸载模型
- `POST /api/v1/models/:id/inference` - 模型推理

### 监控和日志

- `GET /api/v1/models/:id/status` - 获取模型状态
- `GET /api/v1/models/:id/metrics` - 获取性能指标
- `GET /api/v1/models/:id/logs` - 获取推理日志

## 配置说明

### 数据库配置

```env
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=ai_model_service
```

### Redis配置

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

### AI模型配置

```env
AI_MODEL_STORAGE_PATH=./storage/models
AI_MAX_CONCURRENT_REQUESTS=10
AI_INFERENCE_TIMEOUT=30000
AI_ENABLE_GPU=false
```

## 开发指南

### 项目结构

```
src/
├── common/           # 公共组件
│   ├── filters/      # 异常过滤器
│   └── interceptors/ # 拦截器
├── config/           # 配置文件
├── controllers/      # 控制器
├── entities/         # 数据库实体
├── modules/          # 功能模块
└── services/         # 业务服务
```

### 运行命令

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run start:prod

# 构建
npm run build

# 测试
npm run test

# 代码检查
npm run lint
```

## 部署说明

### Docker 部署

```bash
# 构建镜像
docker build -t ai-model-service .

# 运行容器
docker run -p 3008:3008 ai-model-service
```

### 环境变量

生产环境需要设置以下关键环境变量：

- `NODE_ENV=production`
- `DB_HOST`, `DB_USERNAME`, `DB_PASSWORD`
- `REDIS_HOST`, `REDIS_PASSWORD`
- `AI_MODEL_STORAGE_PATH`

## 故障排除

### 常见问题

1. **模型加载失败**: 检查模型文件路径和格式
2. **推理超时**: 调整 `AI_INFERENCE_TIMEOUT` 配置
3. **内存不足**: 减少 `AI_MAX_CONCURRENT_REQUESTS` 或增加系统内存
4. **数据库连接失败**: 检查数据库配置和网络连接

### 日志查看

应用日志包含详细的错误信息和性能指标，可以通过以下方式查看：

- 控制台输出
- 数据库日志表
- 外部日志系统（如ELK）

## 许可证

MIT License
