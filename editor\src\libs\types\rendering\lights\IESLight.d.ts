/**
 * IES光源
 * 基于IES文件的光源，用于模拟真实世界的灯具光照分布
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
/**
 * IES光源类型枚举
 */
export declare enum IESLightType {
    POINT = "point",
    SPOT = "spot"
}
/**
 * IES光源选项接口
 */
export interface IESLightOptions {
    /** 光源类型 */
    type?: IESLightType;
    /** 光源颜色 */
    color?: THREE.ColorRepresentation;
    /** 光源强度 */
    intensity?: number;
    /** 是否投射阴影 */
    castShadow?: boolean;
    /** 光源距离 */
    distance?: number;
    /** 光源衰减 */
    decay?: number;
    /** IES文件路径 */
    iesFilePath?: string;
    /** IES文件数据 */
    iesData?: string;
    /** IES纹理 */
    iesTexture?: THREE.Texture;
    /** 是否使用IES纹理 */
    useIESTexture?: boolean;
    /** 阴影贴图大小 */
    shadowMapSize?: number;
    /** 阴影相机近平面 */
    shadowCameraNear?: number;
    /** 阴影相机远平面 */
    shadowCameraFar?: number;
    /** 阴影偏移 */
    shadowBias?: number;
    /** 阴影半径 */
    shadowRadius?: number;
}
/**
 * IES光源组件类
 */
export declare class IESLight extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 光源类型 */
    private lightType;
    /** Three.js光源 */
    private light;
    /** IES加载器 */
    private iesLoader;
    /** IES纹理 */
    private iesTexture;
    /** 是否使用IES纹理 */
    private useIESTexture;
    /** 是否已加载IES纹理 */
    private iesTextureLoaded;
    /** 加载完成回调 */
    private onLoadCallbacks;
    /**
     * 创建IES光源组件
     * @param options IES光源选项
     */
    constructor(options?: IESLightOptions);
    /**
     * 创建点光源
     * @param options IES光源选项
     * @returns Three.js点光源
     */
    private createPointLight;
    /**
     * 创建聚光灯
     * @param options IES光源选项
     * @returns Three.js聚光灯
     */
    private createSpotLight;
    /**
     * 加载IES文件
     * @param filePath IES文件路径
     */
    loadIESFile(filePath: string): void;
    /**
     * 解析IES数据
     * @param iesData IES文件数据
     */
    parseIESData(iesData: string): void;
    /**
     * 应用IES纹理
     */
    private applyIESTexture;
    /**
     * 当组件附加到实体时调用
     */
    protected onAttach(): void;
    /**
     * 当组件从实体分离时调用
     */
    protected onDetach(): void;
    /**
     * 获取光源类型
     * @returns 光源类型
     */
    getType(): IESLightType;
    /**
     * 获取Three.js光源
     * @returns Three.js光源
     */
    getThreeLight(): THREE.Light;
    /**
     * 获取IES纹理
     * @returns IES纹理
     */
    getIESTexture(): THREE.Texture | null;
    /**
     * 设置IES纹理
     * @param texture IES纹理
     */
    setIESTexture(texture: THREE.Texture): void;
    /**
     * 设置是否使用IES纹理
     * @param use 是否使用
     */
    setUseIESTexture(use: boolean): void;
    /**
     * 设置光源颜色
     * @param color 颜色
     */
    setColor(color: THREE.ColorRepresentation): void;
    /**
     * 设置光源强度
     * @param intensity 强度
     */
    setIntensity(intensity: number): void;
    /**
     * 设置是否投射阴影
     * @param castShadow 是否投射阴影
     */
    setCastShadow(castShadow: boolean): void;
    /**
     * 设置光源距离
     * @param distance 距离
     */
    setDistance(distance: number): void;
    /**
     * 设置光源衰减
     * @param decay 衰减
     */
    setDecay(decay: number): void;
    /**
     * 当IES纹理加载完成时调用回调
     * @param callback 回调函数
     */
    onLoad(callback: () => void): void;
}
