/**
 * Avatar骨骼组件
 * 用于管理角色骨骼映射和姿势
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
/**
 * 人形骨骼名称
 */
export declare enum HumanBoneName {
    Hips = "hips",
    Spine = "spine",
    Chest = "chest",
    UpperChest = "upperChest",
    Neck = "neck",
    Head = "head",
    LeftShoulder = "leftShoulder",
    LeftUpperArm = "leftUpperArm",
    LeftLowerArm = "leftLowerArm",
    LeftHand = "leftHand",
    RightShoulder = "rightShoulder",
    RightUpperArm = "rightUpperArm",
    RightLowerArm = "rightLowerArm",
    RightHand = "rightHand",
    LeftUpperLeg = "leftUpperLeg",
    LeftLowerLeg = "leftLowerLeg",
    LeftFoot = "leftFoot",
    LeftToes = "leftToes",
    RightUpperLeg = "rightUpperLeg",
    RightLowerLeg = "rightLowerLeg",
    RightFoot = "rightFoot",
    RightToes = "rightToes",
    LeftThumbProximal = "leftThumbProximal",
    LeftThumbIntermediate = "leftThumbIntermediate",
    LeftThumbDistal = "leftThumbDistal",
    LeftIndexProximal = "leftIndexProximal",
    LeftIndexIntermediate = "leftIndexIntermediate",
    LeftIndexDistal = "leftIndexDistal",
    LeftMiddleProximal = "leftMiddleProximal",
    LeftMiddleIntermediate = "leftMiddleIntermediate",
    LeftMiddleDistal = "leftMiddleDistal",
    LeftRingProximal = "leftRingProximal",
    LeftRingIntermediate = "leftRingIntermediate",
    LeftRingDistal = "leftRingDistal",
    LeftLittleProximal = "leftLittleProximal",
    LeftLittleIntermediate = "leftLittleIntermediate",
    LeftLittleDistal = "leftLittleDistal",
    RightThumbProximal = "rightThumbProximal",
    RightThumbIntermediate = "rightThumbIntermediate",
    RightThumbDistal = "rightThumbDistal",
    RightIndexProximal = "rightIndexProximal",
    RightIndexIntermediate = "rightIndexIntermediate",
    RightIndexDistal = "rightIndexDistal",
    RightMiddleProximal = "rightMiddleProximal",
    RightMiddleIntermediate = "rightMiddleIntermediate",
    RightMiddleDistal = "rightMiddleDistal",
    RightRingProximal = "rightRingProximal",
    RightRingIntermediate = "rightRingIntermediate",
    RightRingDistal = "rightRingDistal",
    RightLittleProximal = "rightLittleProximal",
    RightLittleIntermediate = "rightLittleIntermediate",
    RightLittleDistal = "rightLittleDistal",
    LeftEye = "leftEye",
    RightEye = "rightEye"
}
/**
 * 骨骼信息
 */
export interface BoneInfo {
    /** 骨骼实体 */
    entity: Entity;
    /** 骨骼对象 */
    bone: THREE.Bone;
    /** 初始本地旋转 */
    initialLocalRotation: THREE.Quaternion;
    /** 初始世界旋转 */
    initialWorldRotation: THREE.Quaternion;
    /** 初始世界旋转逆 */
    initialWorldRotationInverse: THREE.Quaternion;
    /** 父骨骼世界旋转 */
    parentWorldRotation: THREE.Quaternion;
    /** 父骨骼世界旋转逆 */
    parentWorldRotationInverse: THREE.Quaternion;
}
/**
 * Avatar骨骼组件
 */
export declare class AvatarRigComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 骨骼名称到实体的映射 */
    private bonesToEntities;
    /** 实体到骨骼名称的映射 */
    private entitiesToBones;
    /** 骨骼信息 */
    private boneInfos;
    /** 骨骼数组 */
    private bones;
    /** 是否已初始化 */
    private initialized;
    /**
     * 构造函数
     */
    constructor();
    /**
     * 设置骨骼
     * @param boneName 骨骼名称
     * @param entity 实体
     * @param bone 骨骼对象
     */
    setBone(boneName: HumanBoneName, entity: Entity, bone?: THREE.Bone): void;
    /**
     * 获取骨骼
     * @param boneName 骨骼名称
     * @returns 骨骼实体
     */
    getBone(boneName: HumanBoneName): Entity | null;
    /**
     * 获取骨骼名称
     * @param entity 实体
     * @returns 骨骼名称
     */
    getBoneName(entity: Entity): HumanBoneName | null;
    /**
     * 获取所有骨骼
     * @returns 骨骼数组
     */
    getBones(): THREE.Bone[];
    /**
     * 获取骨骼信息
     * @param boneName 骨骼名称
     * @returns 骨骼信息
     */
    getBoneInfo(boneName: HumanBoneName): BoneInfo | null;
    /**
     * 更新骨骼信息
     * @param boneName 骨骼名称
     * @param entity 实体
     * @param bone 骨骼对象
     */
    private updateBoneInfo;
    /**
     * 初始化骨骼
     */
    private initializeBones;
    /**
     * 获取骨骼对象
     * @param entity 实体
     * @returns 骨骼对象
     */
    private getBoneObject;
    /**
     * 设置姿势
     * @param boneName 骨骼名称
     * @param rotation 旋转
     */
    setPose(boneName: HumanBoneName, rotation: THREE.Quaternion): void;
    /**
     * 重置姿势
     */
    resetPose(): void;
    /**
     * 克隆组件
     * @returns 克隆的组件
     */
    clone(): AvatarRigComponent;
}
