/**
 * 地形性能基准测试
 * 用于测试和比较地形渲染性能
 */
import * as THREE from 'three';
import type { Camera } from '../../rendering/Camera';
import { Scene } from '../../scene/Scene';
import { PerformanceData } from './TerrainPerformanceMonitor';
/**
 * 基准测试事件类型
 */
export declare enum BenchmarkEventType {
    /** 基准测试开始 */
    BENCHMARK_STARTED = "benchmark_started",
    /** 基准测试完成 */
    BENCHMARK_COMPLETED = "benchmark_completed",
    /** 基准测试进度 */
    BENCHMARK_PROGRESS = "benchmark_progress",
    /** 基准测试错误 */
    BENCHMARK_ERROR = "benchmark_error"
}
/**
 * 基准测试配置
 */
export interface BenchmarkConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 测试持续时间（毫秒） */
    testDuration?: number;
    /** 预热时间（毫秒） */
    warmupDuration?: number;
    /** 采样间隔（毫秒） */
    sampleInterval?: number;
    /** 相机路径类型 */
    cameraPathType?: CameraPathType;
    /** 是否测试LOD */
    testLOD?: boolean;
    /** 是否测试视锥体剔除 */
    testFrustumCulling?: boolean;
    /** 是否测试纹理流式加载 */
    testTextureStreaming?: boolean;
    /** 是否测试虚拟纹理 */
    testVirtualTexturing?: boolean;
    /** 是否测试几何体压缩 */
    testGeometryCompression?: boolean;
    /** 是否测试物理LOD */
    testPhysicsLOD?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 相机路径类型
 */
export declare enum CameraPathType {
    /** 静态 */
    STATIC = "static",
    /** 圆形 */
    CIRCULAR = "circular",
    /** 飞越 */
    FLYOVER = "flyover",
    /** 随机 */
    RANDOM = "random",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 基准测试结果
 */
export interface BenchmarkResult {
    /** 测试名称 */
    name: string;
    /** 测试持续时间（毫秒） */
    duration: number;
    /** 平均帧率 */
    averageFPS: number;
    /** 最小帧率 */
    minFPS: number;
    /** 最大帧率 */
    maxFPS: number;
    /** 帧率标准差 */
    fpsStdDev: number;
    /** 平均帧时间（毫秒） */
    averageFrameTime: number;
    /** 平均CPU使用率 */
    averageCPUUsage: number;
    /** 平均GPU使用率 */
    averageGPUUsage: number;
    /** 平均内存使用量（字节） */
    averageMemoryUsage: number;
    /** 平均渲染时间（毫秒） */
    averageRenderTime: number;
    /** 平均物理时间（毫秒） */
    averagePhysicsTime: number;
    /** 平均可见地形块数 */
    averageVisibleTerrainChunks: number;
    /** 平均地形三角形数 */
    averageTerrainTriangles: number;
    /** 平均地形顶点数 */
    averageTerrainVertices: number;
    /** 平均地形纹理内存（字节） */
    averageTerrainTextureMemory: number;
    /** 平均地形几何体内存（字节） */
    averageTerrainGeometryMemory: number;
    /** 平均地形物理内存（字节） */
    averageTerrainPhysicsMemory: number;
    /** 性能数据样本 */
    samples: PerformanceData[];
    /** 测试配置 */
    config: BenchmarkConfig;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 地形性能基准测试类
 */
export declare class TerrainPerformanceBenchmark {
    /** 是否启用 */
    private enabled;
    /** 测试持续时间（毫秒） */
    private testDuration;
    /** 预热时间（毫秒） */
    private warmupDuration;
    /** 采样间隔（毫秒） */
    private sampleInterval;
    /** 相机路径类型 */
    private cameraPathType;
    /** 是否测试LOD */
    private testLOD;
    /** 是否测试视锥体剔除 */
    private testFrustumCulling;
    /** 是否测试纹理流式加载 */
    private testTextureStreaming;
    /** 是否测试虚拟纹理 */
    private testVirtualTexturing;
    /** 是否测试几何体压缩 */
    private testGeometryCompression;
    /** 是否测试物理LOD */
    private testPhysicsLOD;
    /** 是否启用调试 */
    private debug;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监控器 */
    private performanceMonitor;
    /** 是否正在测试 */
    private isTesting;
    /** 当前测试 */
    private currentTest;
    /** 测试结果 */
    private testResults;
    /** 测试定时器ID */
    private testTimerId;
    /** 采样定时器ID */
    private sampleTimerId;
    /** 相机原始位置 */
    private originalCameraPosition;
    /** 相机原始旋转 */
    private originalCameraRotation;
    /** 相机路径点 */
    private cameraPathPoints;
    /** 相机路径时间 */
    private cameraPathTime;
    /** 测试开始时间 */
    private testStartTime;
    /** 性能数据样本 */
    private performanceSamples;
    /** 渲染器 */
    private renderer;
    /** 相机 */
    private camera;
    /** 场景 */
    private scene;
    /** 性能基准测试实例 */
    private static instance;
    /**
     * 获取性能基准测试实例
     * @returns 性能基准测试实例
     */
    static getInstance(): TerrainPerformanceBenchmark;
    /**
     * 创建地形性能基准测试
     * @param config 配置
     */
    constructor(config?: BenchmarkConfig);
    /**
     * 设置渲染器
     * @param renderer 渲染器
     */
    setRenderer(renderer: THREE.WebGLRenderer): void;
    /**
     * 设置相机
     * @param camera 相机
     */
    setCamera(camera: Camera): void;
    /**
     * 设置场景
     * @param scene 场景
     */
    setScene(scene: Scene): void;
    /**
     * 运行基准测试
     * @param testName 测试名称
     * @param config 测试配置
     * @returns 测试结果
     */
    runBenchmark(testName: string, config?: BenchmarkConfig): Promise<BenchmarkResult>;
    /**
     * 保存相机原始状态
     */
    private saveOriginalCameraState;
    /**
     * 恢复相机原始状态
     */
    private restoreOriginalCameraState;
    /**
     * 生成相机路径
     * @param pathType 路径类型
     */
    private generateCameraPath;
    /**
     * 预热
     * @param duration 预热时间（毫秒）
     */
    private warmup;
    /**
     * 收集样本
     */
    private collectSample;
    /**
     * 更新相机位置
     */
    private updateCameraPosition;
    /**
     * 完成测试
     */
    private finishTest;
    /**
     * 计算结果
     * @returns 基准测试结果
     */
    private calculateResult;
    /**
     * 计算平均值
     * @param values 值数组
     * @returns 平均值
     */
    private calculateAverage;
    /**
     * 计算标准差
     * @param values 值数组
     * @returns 标准差
     */
    private calculateStandardDeviation;
    /**
     * 清理
     */
    private cleanup;
    /**
     * 获取测试结果
     * @param testName 测试名称
     * @returns 测试结果
     */
    getTestResult(testName: string): BenchmarkResult | null;
    /**
     * 获取所有测试结果
     * @returns 测试结果映射
     */
    getAllTestResults(): Map<string, BenchmarkResult>;
    /**
     * 清除测试结果
     * @param testName 测试名称
     */
    clearTestResult(testName: string): void;
    /**
     * 清除所有测试结果
     */
    clearAllTestResults(): void;
    /**
     * 注册事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    on(event: BenchmarkEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    off(event: BenchmarkEventType, listener: (...args: any[]) => void): void;
}
