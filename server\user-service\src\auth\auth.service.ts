/**
 * 认证服务
 */
import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { User } from '../users/entities/user.entity';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * 验证用户
   */
  async validateUser(usernameOrEmail: string, password: string): Promise<User> {
    return this.usersService.validateUser(usernameOrEmail, password);
  }

  /**
   * 登录
   */
  async login(user: User) {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    };

    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }

  /**
   * 注册
   */
  async register(username: string, email: string, password: string, displayName?: string) {
    const user = await this.usersService.create({
      username,
      email,
      password,
      displayName,
    });

    return this.login(user);
  }

  /**
   * 验证JWT
   */
  async validateJwt(payload: any): Promise<User> {
    return this.usersService.findOne(payload.sub);
  }
}
