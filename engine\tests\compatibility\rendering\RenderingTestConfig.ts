/**
 * 渲染系统兼容性测试配置
 */
import { TestConfig } from '../CompatibilityTestFramework';
import { renderSystemTest } from './RenderSystemTest';
import { cameraSystemTest } from './CameraSystemTest';
import { lightSystemTest } from './LightSystemTest';
import { materialSystemTest } from './MaterialSystemTest';
import { postProcessingTest } from './PostProcessingTest';
import { shadowSystemTest } from './ShadowSystemTest';

/**
 * 渲染系统兼容性测试配置
 */
export const renderingTestConfig: TestConfig = {
  name: '渲染系统兼容性测试',
  description: '测试渲染系统的功能兼容性，包括基础渲染、相机系统、光照系统、材质系统、后处理效果和阴影系统',
  originalModulePath: '../../../src/rendering',
  refactoredModulePath: '../../../../newsystem/engine/src/rendering',
  testCases: [
    renderSystemTest,
    cameraSystemTest,
    lightSystemTest,
    materialSystemTest,
    postProcessingTest,
    shadowSystemTest
  ]
};
