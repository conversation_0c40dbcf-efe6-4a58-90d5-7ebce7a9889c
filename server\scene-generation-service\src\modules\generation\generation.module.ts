import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GenerationService } from './generation.service';
import { GenerationController } from './generation.controller';
import { GenerationTask } from '../tasks/entities/generation-task.entity';
import { Scene } from '../scenes/entities/scene.entity';
import { TextAnalysisService } from './services/text-analysis.service';
import { VoiceProcessingService } from './services/voice-processing.service';
import { LayoutGenerationService } from './services/layout-generation.service';
import { AssetSelectionService } from './services/asset-selection.service';
import { SceneBuildingService } from './services/scene-building.service';
import { SceneOptimizationService } from './services/scene-optimization.service';
import { WebsocketModule } from '../websocket/websocket.module';
import { LoggerService } from '../../common/services/logger.service';
import { CacheService } from '../../common/services/cache.service';
import { StorageService } from '../../common/services/storage.service';
import { HttpClientService } from '../../common/services/http-client.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([GenerationTask, Scene]),
    WebsocketModule,
  ],
  providers: [
    GenerationService,
    LoggerService,
    CacheService,
    StorageService,
    HttpClientService,
    TextAnalysisService,
    VoiceProcessingService,
    LayoutGenerationService,
    AssetSelectionService,
    SceneBuildingService,
    SceneOptimizationService,
  ],
  controllers: [GenerationController],
  exports: [GenerationService],
})
export class GenerationModule {}
