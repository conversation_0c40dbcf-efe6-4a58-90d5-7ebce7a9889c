import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import { VisemeType } from './FacialAnimation';
/**
 * 口型同步配置
 */
export interface LipSyncConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** FFT大小 */
    fftSize?: number;
    /** 采样频率 */
    sampleRate?: number;
    /** 音量阈值 */
    volumeThreshold?: number;
    /** 平滑因子 */
    smoothingFactor?: number;
    /** 是否使用AI预测 */
    useAIPrediction?: boolean;
    /** 分析间隔（毫秒），控制分析频率，降低CPU使用率 */
    analysisInterval?: number;
    /** 是否使用工作线程进行音频分析 */
    useWorker?: boolean;
    /** 是否使用GPU加速 */
    useGPU?: boolean;
    /** 频率带数量 */
    numFrequencyBands?: number;
    /** 是否使用高级分析器 */
    useAdvancedAnalyzer?: boolean;
    /** 是否使用MFCC分析 */
    useMFCC?: boolean;
    /** 是否使用LPC分析 */
    useLPC?: boolean;
    /** 是否使用上下文预测 */
    useContextPrediction?: boolean;
    /** 上下文窗口大小 */
    contextWindowSize?: number;
    /** 梅尔滤波器数量 */
    numMelFilters?: number;
    /** 倒谱系数数量 */
    numCepstralCoeffs?: number;
    /** 是否使用频谱图分析 */
    useSpectrogram?: boolean;
    /** 是否使用音素识别 */
    usePhonemeRecognition?: boolean;
    /** 是否使用语音模式识别 */
    useSpeechPatternRecognition?: boolean;
    /** 是否使用在线学习 */
    useOnlineLearning?: boolean;
    /** AI模型路径 */
    aiModelPath?: string;
}
/**
 * 口型同步组件
 */
export declare class LipSyncComponent extends Component {
    /** 组件类型 */
    static readonly type = "LipSync";
    /** 当前口型 */
    private currentViseme;
    /** 口型权重 */
    private visemeWeight;
    /** 口型混合速度 */
    private visemeBlendSpeed;
    /** 口型混合映射 */
    private visemeBlendMap;
    /** 是否启用 */
    protected enabled: boolean;
    /** 是否使用平滑 */
    private useSmoothing;
    /** 平滑因子 */
    private smoothingFactor;
    /** 音频源 */
    private audioSource;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     */
    constructor();
    /**
     * 设置口型
     * @param viseme 口型类型
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     */
    setViseme(viseme: VisemeType, weight?: number, blendTime?: number): void;
    /**
     * 获取当前口型
     * @returns 当前口型和权重
     */
    getCurrentViseme(): {
        viseme: VisemeType;
        weight: number;
    };
    /**
     * 获取口型混合映射
     * @returns 口型混合映射
     */
    getVisemeBlendMap(): Map<string, number>;
    /**
     * 设置音频源
     * @param audio 音频元素
     */
    setAudioSource(audio: HTMLAudioElement): void;
    /**
     * 获取音频源
     * @returns 音频元素
     */
    getAudioSource(): HTMLAudioElement | null;
    /**
     * 重置口型
     */
    resetViseme(): void;
    /**
     * 启用组件
     */
    enable(): void;
    /**
     * 禁用组件
     */
    disable(): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
/**
 * 口型同步系统
 */
export declare class LipSyncSystem extends System {
    /** 系统类型 */
    static readonly type = "LipSync";
    /** 口型同步组件 */
    private components;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 音频上下文 */
    private audioContext;
    /** 音频分析器 */
    private audioAnalyser;
    /** 频谱数据 */
    private spectrum;
    /** 是否正在跟踪口型 */
    private lipsyncTracking;
    /** 音频处理器 */
    private audioProcessor;
    /** 工作线程 */
    private worker;
    /** 上次分析时间 */
    private lastAnalysisTime;
    /** 频率带能量 */
    private frequencyBands;
    /** 频率带边界 */
    private frequencyBandBoundaries;
    /** 分析定时器ID */
    private analysisTimerId;
    /** 是否支持GPU加速 */
    private supportsGPU;
    /** GPU计算着色器 */
    private gpuComputeShader;
    /** 高级分析器 */
    private advancedAnalyzer;
    /** AI预测器 */
    private aiPredictor;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<LipSyncConfig>);
    /**
     * 初始化高级分析器
     */
    private initAdvancedAnalyzer;
    /**
     * 初始化AI预测器
     */
    private initAIPredictor;
    /**
     * 初始化频率带
     */
    private initFrequencyBands;
    /**
     * 检查GPU支持
     */
    private checkGPUSupport;
    /**
     * 创建口型同步组件
     * @param entity 实体
     * @returns 口型同步组件
     */
    createLipSync(entity: Entity): LipSyncComponent;
    /**
     * 移除口型同步组件
     * @param entity 实体
     */
    removeLipSync(entity: Entity): void;
    /**
     * 获取口型同步组件
     * @param entity 实体
     * @returns 口型同步组件，如果不存在则返回null
     */
    getLipSync(entity: Entity): LipSyncComponent | null;
    /**
     * 启动口型同步跟踪
     * @param audioElement 音频元素
     */
    startTracking(audioElement?: HTMLAudioElement): void;
    /**
     * 停止口型同步跟踪
     */
    stopTracking(): void;
    /**
     * 处理音频数据
     */
    private processAudio;
    /**
     * 使用CPU处理音频数据
     */
    private processAudioWithCPU;
    /**
     * 使用GPU处理音频数据
     */
    private processAudioWithGPU;
    /**
     * 工作线程消息处理
     * @param event 消息事件
     */
    private onWorkerMessage;
    /**
     * 计算频率带能量
     */
    private calculateFrequencyBands;
    /**
     * 计算RMS（均方根）
     * @param spectrum 频谱数据
     * @returns RMS值
     */
    private calculateRMS;
    /**
     * 分析频率带并确定口型
     * @returns 口型类型
     */
    private analyzeFrequencyBands;
    /**
     * 分析频谱并确定口型（旧方法，保留用于兼容）
     * @param spectrum 频谱数据
     * @returns 口型类型
     */
    private analyzeSpectrum;
    /**
     * 获取频段平均能量
     * @param spectrum 频谱数据
     * @param minFreq 最小频率
     * @param maxFreq 最大频率
     * @returns 平均能量
     */
    private getAverageEnergy;
    /**
     * 为所有实体设置口型
     * @param viseme 口型类型
     * @param weight 权重
     */
    private setVisemeForAllEntities;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
