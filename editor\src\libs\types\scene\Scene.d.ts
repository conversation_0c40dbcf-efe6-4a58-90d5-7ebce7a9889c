/**
 * 场景类
 * 表示3D场景，包含实体、光照和环境
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { Skybox } from './Skybox';
import { SceneGraph } from './SceneGraph';
import { SceneLayerManager } from './SceneLayerManager';
export declare class Scene extends EventEmitter {
    /** 场景ID */
    id: string;
    /** 场景名称 */
    name: string;
    /** Three.js场景 */
    private threeScene;
    /** 实体列表 */
    private entities;
    /** 天空盒 */
    private skybox;
    /** 环境光 */
    private ambientLight;
    /** 是否启用雾效 */
    private fogEnabled;
    /** 场景图 */
    private sceneGraph;
    /** 场景图层管理器 */
    private layerManager;
    /**
     * 创建场景实例
     * @param name 场景名称
     */
    constructor(name?: string);
    /**
     * 更新场景
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 固定时间步长更新
     * @param fixedDeltaTime 固定帧间隔时间（秒）
     */
    fixedUpdate(_fixedDeltaTime: number): void;
    /**
     * 添加实体
     * @param entity 实体实例
     * @returns 添加的实体
     */
    addEntity(entity: Entity): Entity;
    /**
     * 移除实体
     * @param entity 实体实例
     * @returns 是否成功移除
     */
    removeEntity(entity: Entity): boolean;
    /**
     * 获取所有实体
     * @returns 实体数组
     */
    getEntities(): Entity[];
    /**
     * 根据名称查找实体
     * @param name 实体名称
     * @returns 匹配的实体数组
     */
    findEntitiesByName(name: string): Entity[];
    /**
     * 根据标签查找实体
     * @param tag 实体标签
     * @returns 匹配的实体数组
     */
    findEntitiesByTag(tag: string): Entity[];
    /**
     * 设置天空盒
     * @param skybox 天空盒实例
     */
    setSkybox(skybox: Skybox | null): void;
    /**
     * 获取天空盒
     * @returns 天空盒实例
     */
    getSkybox(): Skybox | null;
    /**
     * 设置环境光
     * @param color 颜色
     * @param intensity 强度
     */
    setAmbientLight(color: THREE.ColorRepresentation, intensity: number): void;
    /**
     * 获取环境光
     * @returns 环境光实例
     */
    getAmbientLight(): THREE.AmbientLight;
    /**
     * 设置雾效
     * @param color 颜色
     * @param near 近距离
     * @param far 远距离
     */
    setFog(color: THREE.ColorRepresentation, near: number, far: number): void;
    /**
     * 设置指数雾效
     * @param color 颜色
     * @param density 密度
     */
    setExponentialFog(color: THREE.ColorRepresentation, density: number): void;
    /**
     * 清除雾效
     */
    clearFog(): void;
    /**
     * 是否启用雾效
     * @returns 是否启用雾效
     */
    isFogEnabled(): boolean;
    /**
     * 获取Three.js场景
     * @returns Three.js场景实例
     */
    getThreeScene(): THREE.Scene;
    /**
     * 清空场景
     */
    clear(): void;
    /**
     * 销毁场景
     */
    dispose(): void;
    /**
     * 获取场景图
     * @returns 场景图实例
     */
    getSceneGraph(): SceneGraph;
    /**
     * 获取场景图层管理器
     * @returns 场景图层管理器实例
     */
    getLayerManager(): SceneLayerManager;
    /**
     * 获取根实体
     * @returns 根实体
     */
    getRootEntity(): Entity | null;
    /**
     * 查找实体
     * @param predicate 查询函数
     * @returns 匹配的实体数组
     */
    findEntities(predicate: (entity: Entity) => boolean): Entity[];
    /**
     * 查找第一个实体
     * @param predicate 查询函数
     * @returns 匹配的实体，如果不存在则返回null
     */
    findEntity(predicate: (entity: Entity) => boolean): Entity | null;
    /**
     * 根据ID查找实体
     * @param id 实体ID
     * @returns 实体实例，如果不存在则返回null
     */
    findEntityById(id: string): Entity | null;
    /**
     * 是否可见
     * @returns 是否可见
     */
    isVisible(): boolean;
    /**
     * 是否锁定
     * @returns 是否锁定
     */
    isLocked(): boolean;
    /**
     * 获取场景ID
     * @returns 场景ID
     */
    getId(): string;
    /**
     * 设置场景ID
     * @param id 场景ID
     */
    setId(id: string): void;
    /**
     * 获取场景名称
     * @returns 场景名称
     */
    getName(): string;
    /**
     * 设置场景名称
     * @param name 场景名称
     */
    setName(name: string): void;
    /**
     * 获取活跃相机
     * @returns 活跃相机
     */
    getActiveCamera(): any;
}
