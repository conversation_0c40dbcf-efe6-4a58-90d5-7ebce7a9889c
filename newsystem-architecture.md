# DL（Digital Learning）引擎项目架构图

## 1. 整体架构

DL（Digital Learning）引擎项目采用模块化设计，分为三大核心部分：底层引擎(Engine)、编辑器(Editor)和服务器(Server)。整体架构如下：

```
+---------------------------------------------+
|                  DL（Digital Learning）引擎项目                  |
+---------------------------------------------+
|                                             |
|  +-------------+  +-------------+  +------+ |
|  |    Engine   |  |    Editor   |  |Server| |
|  | (底层引擎)   |  |   (编辑器)   |  |(服务器)|
|  +-------------+  +-------------+  +------+ |
|         |               |              |    |
|         v               v              v    |
|  +-------------+  +-------------+  +------+ |
|  |   核心系统   |  |    UI组件    |  |微服务 | |
|  +-------------+  +-------------+  +------+ |
|                                             |
+---------------------------------------------+
```

## 2. 底层引擎(Engine)架构

底层引擎基于TypeScript和Three.js开发，采用实体组件系统(ECS)架构，提供了强大的3D渲染能力、物理模拟、粒子系统、动画系统等功能。

```
+----------------------------------------------------------+
|                       Engine (底层引擎)                    |
+----------------------------------------------------------+
|                                                          |
|  +---------------+  +----------------+  +---------------+ |
|  |   Core (核心)  |  | Rendering (渲染) |  | Physics (物理) | |
|  +---------------+  +----------------+  +---------------+ |
|  | - Engine      |  | - Renderer     |  | - PhysicsSystem| |
|  | - World       |  | - Camera       |  | - RigidBody    | |
|  | - Entity      |  | - Light        |  | - Collider     | |
|  | - Component   |  | - Material     |  | - Constraints  | |
|  | - System      |  | - Mesh         |  | - RayDetection | |
|  +---------------+  +----------------+  +---------------+ |
|                                                          |
|  +---------------+  +----------------+  +---------------+ |
|  |Animation (动画)|  | Scene (场景)    |  | Network (网络) | |
|  +---------------+  +----------------+  +---------------+ |
|  | - Animation   |  | - Scene        |  | - NetworkSystem| |
|  | - Skeleton    |  | - SceneManager |  | - WebRTC       | |
|  | - StateMachine|  | - Transform    |  | - Synchronize  | |
|  | - Blending    |  | - Skybox       |  | - MicroService | |
|  | - FacialAnim  |  | - Environment  |  | - Discovery    | |
|  +---------------+  +----------------+  +---------------+ |
|                                                          |
|  +---------------+  +----------------+  +---------------+ |
|  | Input (输入)   |  | Particles (粒子) |  |Interaction(交互)|
|  +---------------+  +----------------+  +---------------+ |
|  | - InputSystem |  | - ParticleSystem| | - Interactable | |
|  | - InputMapping|  | - ParticleEmitter| | - Highlighting| |
|  | - InputAction |  | - Particle     | | - EventSystem  | |
|  +---------------+  +----------------+  +---------------+ |
|                                                          |
|  +---------------+  +----------------+  +---------------+ |
|  | Avatar (头像)  |  | VisualScript   |  | Terrain (地形) | |
|  +---------------+  +----------------+  +---------------+ |
|  | - AvatarSystem|  | - NodeSystem   |  | - TerrainSystem| |
|  | - Controller  |  | - NodeGraph    |  | - Vegetation  | |
|  | - Animation   |  | - NodeEditor   |  | - Water       | |
|  +---------------+  +----------------+  +---------------+ |
|                                                          |
+----------------------------------------------------------+
```

## 3. 编辑器(Editor)架构

编辑器基于React、Redux和Ant Design开发，提供了直观的可视化界面，让用户可以轻松创建和编辑3D场景、模型、材质、动画等内容。

```
+----------------------------------------------------------+
|                     Editor (编辑器)                       |
+----------------------------------------------------------+
|                                                          |
|  +---------------+  +----------------+  +---------------+ |
|  |   UI层 (UI)    |  | 状态管理(State) |  | 服务层(Services)| |
|  +---------------+  +----------------+  +---------------+ |
|  | - 布局组件     |  | - Redux Store  |  | - EngineService| |
|  | - 面板组件     |  | - authSlice    |  | - ProjectService|
|  | - 视口组件     |  | - projectSlice |  | - AssetService | |
|  | - 工具组件     |  | - sceneSlice   |  | - AuthService  | |
|  | - 对话框组件   |  | - entitySlice  |  | - CollabService| |
|  | - 通用组件     |  | - assetSlice   |  | - HistoryService|
|  +---------------+  +----------------+  +---------------+ |
|                                                          |
|  +---------------+  +----------------+  +---------------+ |
|  | 工具层(Tools)  |  | 引擎接口层      |  | 扩展系统      | |
|  +---------------+  +----------------+  +---------------+ |
|  | - 数学工具     |  | - 场景管理     |  | - 面板扩展    | |
|  | - 文件工具     |  | - 实体管理     |  | - 工具扩展    | |
|  | - UI工具       |  | - 组件管理     |  | - 组件编辑器  | |
|  | - 调试工具     |  | - 资源管理     |  | - 资产类型    | |
|  | - 验证工具     |  | - 渲染控制     |  | - 命令扩展    | |
|  +---------------+  +----------------+  +---------------+ |
|                                                          |
|  +---------------+  +----------------+                   |
|  | 面板系统      |  | 协作编辑系统    |                   |
|  +---------------+  +----------------+                   |
|  | - ScenePanel  |  | - 实时同步     |                   |
|  | - HierarchyPanel| | - 冲突解决    |                   |
|  | - InspectorPanel| | - 权限管理    |                   |
|  | - AssetsPanel |  | - 版本控制     |                   |
|  | - ConsolePanel|  | - 操作广播     |                   |
|  +---------------+  +----------------+                   |
|                                                          |
+----------------------------------------------------------+
```

## 4. 服务器(Server)架构

服务器端基于Nest.js和MySQL开发，采用微服务架构，包括用户服务、项目服务、资产服务、渲染服务等多个微服务。

```
+----------------------------------------------------------+
|                     Server (服务器)                       |
+----------------------------------------------------------+
|                                                          |
|  +---------------+  +----------------+  +---------------+ |
|  | API网关       |  | 服务注册中心    |  | 用户服务      | |
|  +---------------+  +----------------+  +---------------+ |
|  | - 路由转发    |  | - 服务注册     |  | - 用户管理    | |
|  | - 负载均衡    |  | - 服务发现     |  | - 认证授权    | |
|  | - 请求过滤    |  | - 健康检查     |  | - 权限管理    | |
|  | - 熔断器      |  | - 负载均衡     |  | - 用户配置    | |
|  | - 限流器      |  | - 服务缓存     |  | - 社交功能    | |
|  +---------------+  +----------------+  +---------------+ |
|                                                          |
|  +---------------+  +----------------+  +---------------+ |
|  | 项目服务      |  | 资产服务       |  | 渲染服务      | |
|  +---------------+  +----------------+  +---------------+ |
|  | - 项目管理    |  | - 资产管理     |  | - 渲染任务    | |
|  | - 场景管理    |  | - 资产上传     |  | - 渲染队列    | |
|  | - 版本控制    |  | - 资产转换     |  | - 渲染节点    | |
|  | - 协作管理    |  | - 资产优化     |  | - 渲染监控    | |
|  | - 项目分享    |  | - CDN分发      |  | - 渲染统计    | |
|  +---------------+  +----------------+  +---------------+ |
|                                                          |
|  +---------------+  +----------------+  +---------------+ |
|  | 游戏服务器     |  | 实例服务器     |  | 监控服务      | |
|  +---------------+  +----------------+  +---------------+ |
|  | - 游戏逻辑    |  | - 实例管理     |  | - 系统监控    | |
|  | - 游戏状态    |  | - 负载均衡     |  | - 服务监控    | |
|  | - 游戏事件    |  | - 自动扩缩容   |  | - 日志收集    | |
|  | - 游戏AI      |  | - 资源分配     |  | - 告警系统    | |
|  | - 游戏数据    |  | - 会话管理     |  | - 性能分析    | |
|  +---------------+  +----------------+  +---------------+ |
|                                                          |
+----------------------------------------------------------+
```

## 5. 系统间通信

DL（Digital Learning）引擎项目中，Engine、Editor和Server之间通过多种方式进行通信：

```
+---------------+                +---------------+
|    Editor     |                |    Server     |
|   (编辑器)     | <-----------> |   (服务器)     |
+---------------+                +---------------+
       ^                                ^
       |                                |
       v                                v
+---------------+                +---------------+
|    Engine     | <-----------> |  WebRTC/WebSocket |
|  (底层引擎)    |                |   (网络通信)   |
+---------------+                +---------------+
```

1. **Editor与Engine通信**：
   - 通过EngineService直接调用Engine API
   - 通过事件系统进行状态同步
   - 通过Redux状态管理共享数据

2. **Engine与Server通信**：
   - 通过WebSocket进行实时通信
   - 通过WebRTC进行P2P通信
   - 通过HTTP API进行资源加载和数据交换
   - 通过微服务客户端访问后端服务

3. **Editor与Server通信**：
   - 通过HTTP API进行数据交换
   - 通过WebSocket进行实时协作
   - 通过服务发现客户端访问微服务

## 6. 微服务架构

```
+------------------+
|    API网关       |
+------------------+
         |
         v
+------------------+
|  服务注册中心     |
+------------------+
         |
         v
+---------------------------------------+
|                                       |
|  +----------+  +----------+  +-----+  |
|  |用户服务  |  |项目服务  |  |更多  |  |
|  +----------+  +----------+  +-----+  |
|                                       |
+---------------------------------------+
```

1. **服务注册与发现**：
   - 服务实例启动时向注册中心注册
   - 客户端通过注册中心发现服务
   - 支持健康检查和自动剔除不健康实例

2. **负载均衡**：
   - 支持多种负载均衡策略
   - 包括随机、轮询、加权轮询、最少响应时间、一致性哈希和区域感知

3. **服务缓存**：
   - 支持多级缓存（内存和Redis）
   - 提高服务发现的性能

4. **API网关**：
   - 统一入口点
   - 路由转发
   - 请求过滤
   - 熔断器和限流器
