import { Scene } from '../../scene/Scene';
import { PhysicsSystem } from '../PhysicsSystem';
/**
 * 物理场景导出选项
 */
export interface PhysicsSceneExportOptions {
    /** 是否包含物理体 */
    includeBodies?: boolean;
    /** 是否包含碰撞器 */
    includeColliders?: boolean;
    /** 是否包含约束 */
    includeConstraints?: boolean;
    /** 是否包含物理世界 */
    includeWorld?: boolean;
    /** 是否包含材质 */
    includeMaterials?: boolean;
    /** 是否美化JSON输出 */
    prettyPrint?: boolean;
    /** 是否包含元数据 */
    includeMetadata?: boolean;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 物理场景导出数据
 */
export interface PhysicsSceneExportData {
    /** 版本 */
    version: string;
    /** 元数据 */
    metadata?: Record<string, any>;
    /** 物理世界数据 */
    world?: any;
    /** 物理体数据 */
    bodies?: any[];
    /** 碰撞器数据 */
    colliders?: any[];
    /** 约束数据 */
    constraints?: any[];
    /** 材质数据 */
    materials?: any[];
}
/**
 * 物理场景导出器
 */
export declare class PhysicsSceneExporter {
    /** 物理系统 */
    private physicsSystem;
    /**
     * 创建物理场景导出器
     * @param physicsSystem 物理系统
     */
    constructor(physicsSystem: PhysicsSystem);
    /**
     * 导出场景
     * @param scene 场景
     * @param options 导出选项
     * @returns 导出数据
     */
    export(scene: Scene, options?: PhysicsSceneExportOptions): PhysicsSceneExportData;
    /**
     * 导出为JSON
     * @param scene 场景
     * @param options 导出选项
     * @returns JSON字符串
     */
    exportToJSON(scene: Scene, options?: PhysicsSceneExportOptions): string;
    /**
     * 导出物理世界
     * @param scene 场景
     * @returns 物理世界数据
     */
    private exportWorld;
    /**
     * 导出物理体
     * @param scene 场景
     * @returns 物理体数据数组
     */
    private exportBodies;
    /**
     * 导出碰撞器
     * @param scene 场景
     * @returns 碰撞器数据数组
     */
    private exportColliders;
    /**
     * 导出约束
     * @param scene 场景
     * @returns 约束数据数组
     */
    private exportConstraints;
    /**
     * 导出材质
     * @returns 材质数据数组
     */
    private exportMaterials;
}
