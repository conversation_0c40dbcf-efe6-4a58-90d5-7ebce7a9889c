/**
 * InteractionSystem.ts
 *
 * 交互系统，用于处理3D场景中的对象交互
 */
import { System } from '../core/System';
import type { World } from '../core/World';
import type { Entity } from '../core/Entity';
import { InteractableComponent } from './components/InteractableComponent';
import { InteractionEventComponent } from './components/InteractionEventComponent';
/**
 * 交互系统配置
 */
export interface InteractionSystemConfig {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 最大交互距离 */
    maxInteractionDistance?: number;
    /** 是否启用视锥体检测 */
    enableFrustumCheck?: boolean;
    /** 是否启用高亮效果 */
    enableHighlight?: boolean;
    /** 是否启用交互提示 */
    enablePrompt?: boolean;
    /** 是否启用交互声音 */
    enableSound?: boolean;
}
/**
 * 交互系统
 * 用于处理3D场景中的对象交互
 */
export declare class InteractionSystem extends System {
    /** 系统名称 */
    static readonly NAME: string;
    /** 输入系统引用 */
    private inputSystem?;
    /** 可交互组件列表 */
    private interactableComponents;
    /** 交互事件组件列表 */
    private interactionEventComponents;
    /** 当前可用的交互对象列表（按距离排序） */
    private availableInteractables;
    /** 当前最近的交互对象 */
    private closestInteractable?;
    /** 当前高亮的交互对象 */
    private highlightedInteractable?;
    /** 射线投射器 */
    private raycaster;
    /** 配置 */
    private config;
    /** 交互检测计时器 */
    private interactionCheckTimer;
    /** 交互检测间隔（秒） */
    private readonly interactionCheckInterval;
    /**
     * 构造函数
     * @param world 世界实例
     * @param config 交互系统配置
     */
    constructor(world: World, config?: InteractionSystemConfig);
    /**
     * 注册可交互组件
     * @param entity 实体
     * @param component 可交互组件
     */
    registerInteractableComponent(entity: Entity, component: InteractableComponent): void;
    /**
     * 注销可交互组件
     * @param entity 实体
     */
    unregisterInteractableComponent(entity: Entity): void;
    /**
     * 注册交互事件组件
     * @param entity 实体
     * @param component 交互事件组件
     */
    registerInteractionEventComponent(entity: Entity, component: InteractionEventComponent): void;
    /**
     * 注销交互事件组件
     * @param entity 实体
     */
    unregisterInteractionEventComponent(entity: Entity): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 收集可用的交互对象
     */
    private gatherAvailableInteractables;
    /**
     * 检查对象是否在视锥体内
     * @param entity 实体
     * @param camera 相机
     * @returns 是否在视锥体内
     */
    private isInFrustum;
    /**
     * 按距离排序交互对象
     * @param cameraPosition 相机位置
     */
    private sortInteractablesByDistance;
    /**
     * 处理输入
     */
    private handleInput;
    /**
     * 与最近的交互对象交互
     */
    private interactWithClosestInteractable;
    /**
     * 更新高亮效果
     */
    private updateHighlight;
    /**
     * 获取对象的3D表示
     * @param entity 实体
     * @returns 3D对象
     */
    private getObject3D;
    /**
     * 查找主相机
     * @param scene 场景
     * @returns 相机对象
     */
    private findMainCamera;
    /**
     * 销毁系统
     */
    dispose(): void;
}
