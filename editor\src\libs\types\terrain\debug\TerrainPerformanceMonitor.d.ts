/**
 * 性能监控事件类型
 */
export declare enum PerformanceMonitorEventType {
    /** 性能数据更新 */
    PERFORMANCE_DATA_UPDATED = "performance_data_updated",
    /** 性能警告 */
    PERFORMANCE_WARNING = "performance_warning",
    /** 性能瓶颈检测 */
    BOTTLENECK_DETECTED = "bottleneck_detected"
}
/**
 * 性能警告级别
 */
export declare enum PerformanceWarningLevel {
    /** 信息 */
    INFO = "info",
    /** 警告 */
    WARNING = "warning",
    /** 错误 */
    ERROR = "error"
}
/**
 * 性能瓶颈类型
 */
export declare enum PerformanceBottleneckType {
    /** CPU瓶颈 */
    CPU = "cpu",
    /** GPU瓶颈 */
    GPU = "gpu",
    /** 内存瓶颈 */
    MEMORY = "memory",
    /** 渲染瓶颈 */
    RENDERING = "rendering",
    /** 物理瓶颈 */
    PHYSICS = "physics",
    /** 纹理瓶颈 */
    TEXTURES = "textures",
    /** 几何体瓶颈 */
    GEOMETRY = "geometry"
}
/**
 * 性能数据
 */
export interface PerformanceData {
    /** 帧率 */
    fps: number;
    /** 帧时间（毫秒） */
    frameTime: number;
    /** CPU使用率 */
    cpuUsage: number;
    /** GPU使用率 */
    gpuUsage: number;
    /** 内存使用量（MB） */
    memoryUsage: number;
    /** 渲染时间（毫秒） */
    renderTime: number;
    /** 物理时间（毫秒） */
    physicsTime: number;
    /** 地形渲染时间（毫秒） */
    terrainRenderTime: number;
    /** 地形物理时间（毫秒） */
    terrainPhysicsTime: number;
    /** 可见地形块数量 */
    visibleTerrainChunks: number;
    /** 地形三角形数量 */
    terrainTriangles: number;
    /** 地形顶点数量 */
    terrainVertices: number;
    /** 地形纹理内存（MB） */
    terrainTextureMemory: number;
    /** 地形几何体内存（MB） */
    terrainGeometryMemory: number;
    /** 地形物理内存（MB） */
    terrainPhysicsMemory: number;
    /** 地形LOD级别分布 */
    terrainLODDistribution: Record<number, number>;
    /** 地形物理LOD级别分布 */
    terrainPhysicsLODDistribution: Record<number, number>;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 性能监控配置
 */
export interface PerformanceMonitorConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 更新频率（毫秒） */
    updateFrequency?: number;
    /** 采样大小 */
    sampleSize?: number;
    /** 是否记录历史数据 */
    recordHistory?: boolean;
    /** 历史数据最大长度 */
    maxHistoryLength?: number;
    /** 是否启用警告 */
    enableWarnings?: boolean;
    /** 是否启用瓶颈检测 */
    enableBottleneckDetection?: boolean;
    /** 警告阈值 */
    warningThresholds?: {
        /** 最小帧率 */
        minFps?: number;
        /** 最大帧时间 */
        maxFrameTime?: number;
        /** 最大CPU使用率 */
        maxCpuUsage?: number;
        /** 最大GPU使用率 */
        maxGpuUsage?: number;
        /** 最大内存使用量 */
        maxMemoryUsage?: number;
        /** 最大渲染时间 */
        maxRenderTime?: number;
        /** 最大物理时间 */
        maxPhysicsTime?: number;
    };
}
/**
 * 地形性能监控
 */
export declare class TerrainPerformanceMonitor {
    /** 是否启用 */
    private enabled;
    /** 更新频率（毫秒） */
    private updateFrequency;
    /** 采样大小 */
    private sampleSize;
    /** 是否记录历史数据 */
    private recordHistory;
    /** 历史数据最大长度 */
    private maxHistoryLength;
    /** 是否启用警告 */
    private enableWarnings;
    /** 是否启用瓶颈检测 */
    private enableBottleneckDetection;
    /** 警告阈值 */
    private warningThresholds;
    /** 性能数据 */
    private performanceData;
    /** 历史性能数据 */
    private performanceHistory;
    /** 帧时间样本 */
    private frameTimeSamples;
    /** 渲染时间样本 */
    private renderTimeSamples;
    /** 物理时间样本 */
    private physicsTimeSamples;
    /** 地形渲染时间样本 */
    private terrainRenderTimeSamples;
    /** 地形物理时间样本 */
    private terrainPhysicsTimeSamples;
    /** 事件发射器 */
    private eventEmitter;
    /** 更新定时器ID */
    private updateTimerId;
    /** 上一帧时间 */
    private lastFrameTime;
    /** 帧计数器 */
    private frameCount;
    /** 开始时间 */
    private startTime;
    /** 性能监控实例 */
    private static instance;
    /**
     * 获取性能监控实例
     * @returns 性能监控实例
     */
    static getInstance(): TerrainPerformanceMonitor;
    /**
     * 创建地形性能监控
     * @param config 配置
     */
    constructor(config?: PerformanceMonitorConfig);
    /**
     * 创建空性能数据
     * @returns 空性能数据
     */
    private createEmptyPerformanceData;
    /**
     * 开始监控
     */
    start(): void;
    /**
     * 停止监控
     */
    stop(): void;
    /**
     * 更新性能数据
     */
    private updatePerformanceData;
    /**
     * 添加到历史记录
     * @param data 性能数据
     */
    private addToHistory;
    /**
     * 计算平均值
     * @param samples 样本数组
     * @returns 平均值
     */
    private calculateAverage;
    /**
     * 获取内存使用情况
     * @returns 内存使用量（MB）
     */
    private getMemoryUsage;
    /**
     * 获取CPU和GPU使用情况
     * @returns CPU和GPU使用率
     */
    private getCPUAndGPUUsage;
    /**
     * 获取地形统计信息
     * @returns 地形统计信息
     */
    private getTerrainStats;
    /**
     * 检查性能警告
     * @param data 性能数据
     */
    private checkPerformanceWarnings;
    /**
     * 发出警告
     * @param level 警告级别
     * @param message 警告消息
     */
    private emitWarning;
    /**
     * 检测性能瓶颈
     * @param data 性能数据
     */
    private detectBottlenecks;
    /**
     * 发出瓶颈事件
     * @param type 瓶颈类型
     * @param message 瓶颈消息
     */
    private emitBottleneck;
    /**
     * 记录帧时间
     * @param frameTime 帧时间（毫秒）
     */
    recordFrameTime(frameTime: number): void;
    /**
     * 记录渲染时间
     * @param renderTime 渲染时间（毫秒）
     */
    recordRenderTime(renderTime: number): void;
    /**
     * 记录物理时间
     * @param physicsTime 物理时间（毫秒）
     */
    recordPhysicsTime(physicsTime: number): void;
    /**
     * 记录地形渲染时间
     * @param terrainRenderTime 地形渲染时间（毫秒）
     */
    recordTerrainRenderTime(terrainRenderTime: number): void;
    /**
     * 记录地形物理时间
     * @param terrainPhysicsTime 地形物理时间（毫秒）
     */
    recordTerrainPhysicsTime(terrainPhysicsTime: number): void;
    /**
     * 获取当前性能数据
     * @returns 性能数据
     */
    getPerformanceData(): PerformanceData;
    /**
     * 获取性能历史数据
     * @returns 性能历史数据
     */
    getPerformanceHistory(): PerformanceData[];
    /**
     * 清除性能历史数据
     */
    clearPerformanceHistory(): void;
    /**
     * 设置配置
     * @param config 配置
     */
    setConfig(config: PerformanceMonitorConfig): void;
    /**
     * 获取配置
     * @returns 配置
     */
    getConfig(): PerformanceMonitorConfig;
    /**
     * 注册事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    on(event: PerformanceMonitorEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    off(event: PerformanceMonitorEventType, listener: (...args: any[]) => void): void;
    /**
     * 销毁
     */
    dispose(): void;
}
