/**
 * 四元数类
 * 表示三维空间中的旋转
 */
export declare class Quaternion {
    /** X分量 */
    x: number;
    /** Y分量 */
    y: number;
    /** Z分量 */
    z: number;
    /** W分量 */
    w: number;
    /**
     * 创建四元数
     * @param x X分量
     * @param y Y分量
     * @param z Z分量
     * @param w W分量
     */
    constructor(x?: number, y?: number, z?: number, w?: number);
    /**
     * 设置四元数分量
     * @param x X分量
     * @param y Y分量
     * @param z Z分量
     * @param w W分量
     * @returns 当前四元数
     */
    set(x: number, y: number, z: number, w: number): Quaternion;
    /**
     * 复制另一个四元数的值
     * @param q 要复制的四元数
     * @returns 当前四元数
     */
    copy(q: Quaternion): Quaternion;
    /**
     * 克隆四元数
     * @returns 新的四元数实例
     */
    clone(): Quaternion;
    /**
     * 四元数乘法
     * @param q 要乘的四元数
     * @returns 当前四元数
     */
    multiply(q: Quaternion): Quaternion;
    /**
     * 四元数点积
     * @param q 另一个四元数
     * @returns 点积结果
     */
    dot(q: Quaternion): number;
    /**
     * 计算四元数长度
     * @returns 四元数长度
     */
    length(): number;
    /**
     * 归一化四元数
     * @returns 当前四元数
     */
    normalize(): Quaternion;
    /**
     * 四元数求逆
     * @returns 当前四元数
     */
    inverse(): Quaternion;
    /**
     * 四元数共轭
     * @returns 当前四元数
     */
    conjugate(): Quaternion;
    /**
     * 从欧拉角设置四元数
     * @param x X轴旋转（弧度）
     * @param y Y轴旋转（弧度）
     * @param z Z轴旋转（弧度）
     * @returns 当前四元数
     */
    setFromEuler(x: number, y: number, z: number): Quaternion;
    /**
     * 从轴角设置四元数
     * @param axis 旋转轴
     * @param angle 旋转角度（弧度）
     * @returns 当前四元数
     */
    setFromAxisAngle(axis: {
        x: number;
        y: number;
        z: number;
    }, angle: number): Quaternion;
    /**
     * 判断四元数是否约等于另一个四元数
     * @param q 另一个四元数
     * @param epsilon 误差范围
     * @returns 是否约等于
     */
    equals(q: Quaternion, epsilon?: number): boolean;
}
