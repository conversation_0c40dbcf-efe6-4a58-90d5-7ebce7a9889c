/**
 * 调试状态切片
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 调试状态接口
export interface DebugState {
  // 是否启用性能监控
  performanceMonitoringEnabled: boolean;
  // 是否启用内存监控
  memoryMonitoringEnabled: boolean;
  // 是否启用内存泄漏检测
  memoryLeakDetectionEnabled: boolean;
  // 是否启用自动内存快照
  autoMemorySnapshotEnabled: boolean;
  // 是否显示FPS
  showFPS: boolean;
  // 是否显示统计信息
  showStats: boolean;
  // 是否显示网格线
  showGrid: boolean;
  // 是否显示坐标轴
  showAxes: boolean;
  // 是否显示边界框
  showBoundingBoxes: boolean;
  // 是否显示骨骼
  showSkeleton: boolean;
  // 是否显示物理调试
  showPhysicsDebug: boolean;
  // 是否显示光源辅助
  showLightHelpers: boolean;
  // 是否显示相机辅助
  showCameraHelpers: boolean;
}

// 初始状态
const initialState: DebugState = {
  performanceMonitoringEnabled: false,
  memoryMonitoringEnabled: false,
  memoryLeakDetectionEnabled: true,
  autoMemorySnapshotEnabled: false,
  showFPS: false,
  showStats: false,
  showGrid: true,
  showAxes: true,
  showBoundingBoxes: false,
  showSkeleton: false,
  showPhysicsDebug: false,
  showLightHelpers: false,
  showCameraHelpers: false
};

// 创建切片
const debugSlice = createSlice({
  name: 'debug',
  initialState,
  reducers: {
    // 设置性能监控启用状态
    setPerformanceMonitorConfigConfigingEnabled: (state, action: PayloadAction<boolean>) => {
      state.performanceMonitoringEnabled = action.payload;
    },
    // 设置内存监控启用状态
    setMemoryMonitoringEnabled: (state, action: PayloadAction<boolean>) => {
      state.memoryMonitoringEnabled = action.payload;
    },
    // 设置内存泄漏检测启用状态
    setMemoryLeakDetectionEnabled: (state, action: PayloadAction<boolean>) => {
      state.memoryLeakDetectionEnabled = action.payload;
    },
    // 设置自动内存快照启用状态
    setAutoMemorySnapshotEnabled: (state, action: PayloadAction<boolean>) => {
      state.autoMemorySnapshotEnabled = action.payload;
    },
    // 设置显示FPS
    setShowFPS: (state, action: PayloadAction<boolean>) => {
      state.showFPS = action.payload;
    },
    // 设置显示统计信息
    setShowStats: (state, action: PayloadAction<boolean>) => {
      state.showStats = action.payload;
    },
    // 设置显示网格线
    setShowGrid: (state, action: PayloadAction<boolean>) => {
      state.showGrid = action.payload;
    },
    // 设置显示坐标轴
    setShowAxes: (state, action: PayloadAction<boolean>) => {
      state.showAxes = action.payload;
    },
    // 设置显示边界框
    setShowBoundingBoxes: (state, action: PayloadAction<boolean>) => {
      state.showBoundingBoxes = action.payload;
    },
    // 设置显示骨骼
    setShowSkeleton: (state, action: PayloadAction<boolean>) => {
      state.showSkeleton = action.payload;
    },
    // 设置显示物理调试
    setShowPhysicsDebug: (state, action: PayloadAction<boolean>) => {
      state.showPhysicsDebug = action.payload;
    },
    // 设置显示光源辅助
    setShowLightHelpers: (state, action: PayloadAction<boolean>) => {
      state.showLightHelpers = action.payload;
    },
    // 设置显示相机辅助
    setShowCameraHelpers: (state, action: PayloadAction<boolean>) => {
      state.showCameraHelpers = action.payload;
    },
    // 重置调试状态
    resetDebugState: () => {
      return initialState;
    }
  }
});

// 导出动作创建器
export const {
  setPerformanceMonitorConfigConfigingEnabled,
  setMemoryMonitoringEnabled,
  setMemoryLeakDetectionEnabled,
  setAutoMemorySnapshotEnabled,
  setShowFPS,
  setShowStats,
  setShowGrid,
  setShowAxes,
  setShowBoundingBoxes,
  setShowSkeleton,
  setShowPhysicsDebug,
  setShowLightHelpers,
  setShowCameraHelpers,
  resetDebugState
} = debugSlice.actions;

// 导出reducer
export default debugSlice.reducer;
