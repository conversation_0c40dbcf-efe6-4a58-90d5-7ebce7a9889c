/**
 * 物理世界组件
 * 为场景提供物理世界
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { Component } from '../../core/Component';
/**
 * 物理世界选项
 */
export interface PhysicsWorldOptions {
    /** 重力 */
    gravity?: THREE.Vector3;
    /** 是否允许休眠 */
    allowSleep?: boolean;
    /** 迭代次数 */
    iterations?: number;
    /** 宽相检测算法 */
    broadphase?: 'naive' | 'sap' | 'grid';
    /** 网格宽相检测的单元格大小 */
    gridBroadphaseSize?: number;
    /** 默认接触材质 */
    defaultContactMaterial?: CANNON.ContactMaterial;
    /** 默认摩擦力 */
    defaultFriction?: number;
    /** 默认恢复系数 */
    defaultRestitution?: number;
}
/**
 * 物理世界组件
 */
export declare class PhysicsWorldComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 物理世界 */
    private world;
    /** 重力 */
    private gravity;
    /** 是否允许休眠 */
    private allowSleep;
    /** 迭代次数 */
    private iterations;
    /** 宽相检测算法 */
    private broadphase;
    /** 网格宽相检测的单元格大小 */
    private gridBroadphaseSize;
    /** 默认接触材质 */
    private defaultContactMaterial;
    /** 默认摩擦力 */
    private defaultFriction;
    /** 默认恢复系数 */
    private defaultRestitution;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /**
     * 创建物理世界组件
     * @param options 物理世界选项
     */
    constructor(options?: PhysicsWorldOptions);
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 设置宽相检测算法
     * @param broadphase 宽相检测算法
     */
    private setBroadphase;
    /**
     * 更新物理世界
     * @param deltaTime 帧间隔时间（秒）
     * @param maxSubSteps 最大子步长
     */
    update(deltaTime: number, maxSubSteps?: number): void;
    /**
     * 获取物理世界
     * @returns 物理世界
     */
    getWorld(): CANNON.World;
    /**
     * 设置重力
     * @param gravity 重力向量
     */
    setGravity(gravity: THREE.Vector3): void;
    /**
     * 获取重力
     * @returns 重力向量
     */
    getGravity(): THREE.Vector3;
    /**
     * 设置是否允许休眠
     * @param allowSleep 是否允许休眠
     */
    setAllowSleep(allowSleep: boolean): void;
    /**
     * 获取是否允许休眠
     * @returns 是否允许休眠
     */
    getAllowSleep(): boolean;
    /**
     * 设置迭代次数
     * @param iterations 迭代次数
     */
    setIterations(iterations: number): void;
    /**
     * 获取迭代次数
     * @returns 迭代次数
     */
    getIterations(): number;
    /**
     * 设置宽相检测算法
     * @param broadphase 宽相检测算法
     * @param gridSize 网格宽相检测的单元格大小（仅适用于grid算法）
     */
    setBroadphaseAlgorithm(broadphase: 'naive' | 'sap' | 'grid', gridSize?: number): void;
    /**
     * 获取宽相检测算法
     * @returns 宽相检测算法
     */
    getBroadphaseAlgorithm(): string;
    /**
     * 创建材质
     * @param name 材质名称
     * @returns 材质
     */
    createMaterial(name: string): CANNON.Material;
    /**
     * 创建接触材质
     * @param materialA 材质A
     * @param materialB 材质B
     * @param options 接触材质选项
     * @returns 接触材质
     */
    createContactMaterial(materialA: CANNON.Material, materialB: CANNON.Material, options?: {
        friction?: number;
        restitution?: number;
        contactEquationStiffness?: number;
        contactEquationRelaxation?: number;
        frictionEquationStiffness?: number;
        frictionEquationRelaxation?: number;
    }): CANNON.ContactMaterial;
    /**
     * 射线检测
     * @param from 起点
     * @param to 终点
     * @param options 射线检测选项
     * @returns 射线检测结果
     */
    raycast(from: THREE.Vector3, to: THREE.Vector3, options?: {
        skipBackfaces?: boolean;
        collisionFilterMask?: number;
        collisionFilterGroup?: number;
    }): CANNON.RaycastResult | null;
    /**
     * 射线检测（多个结果）
     * @param from 起点
     * @param to 终点
     * @param options 射线检测选项
     * @returns 射线检测结果数组
     */
    raycastAll(from: THREE.Vector3, to: THREE.Vector3, options?: {
        skipBackfaces?: boolean;
        collisionFilterMask?: number;
        collisionFilterGroup?: number;
    }): CANNON.RaycastResult[];
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (event: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (event: any) => void): void;
    /**
     * 销毁组件
     */
    dispose(): void;
}
