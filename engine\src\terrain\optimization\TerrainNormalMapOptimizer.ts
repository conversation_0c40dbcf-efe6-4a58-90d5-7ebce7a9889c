/**
 * 地形法线贴图优化器
 * 提供地形法线贴图优化功能，减少内存使用和提高渲染性能
 */
import * as THREE from 'three';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { TerrainComponent } from '../components/TerrainComponent';
import type { Entity } from '../../core/Entity';
import { TerrainTextureLayer } from '../components/TerrainComponent';

/**
 * 法线贴图优化事件类型
 */
export enum NormalMapOptimizationEventType {
  /** 优化开始 */
  OPTIMIZATION_STARTED = 'optimization_started',
  /** 优化完成 */
  OPTIMIZATION_COMPLETED = 'optimization_completed',
  /** 优化进度 */
  OPTIMIZATION_PROGRESS = 'optimization_progress',
  /** 优化错误 */
  OPTIMIZATION_ERROR = 'optimization_error'
}

/**
 * 法线贴图优化配置
 */
export interface NormalMapOptimizationConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否使用压缩 */
  useCompression?: boolean;
  /** 压缩质量 (0-1) */
  compressionQuality?: number;
  /** 是否使用法线贴图合并 */
  useNormalMapMerging?: boolean;
  /** 是否使用法线贴图缩放 */
  useNormalMapScaling?: boolean;
  /** 最大纹理大小 */
  maxTextureSize?: number;
  /** 是否使用法线贴图生成 */
  useNormalMapGeneration?: boolean;
  /** 法线贴图生成强度 */
  normalMapGenerationStrength?: number;
  /** 是否使用GPU加速 */
  useGPUAcceleration?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 优化结果
 */
export interface OptimizationResult {
  /** 原始纹理 */
  originalTexture: THREE.Texture;
  /** 优化后的纹理 */
  optimizedTexture: THREE.Texture;
  /** 原始内存使用量（字节） */
  originalSize: number;
  /** 优化后内存使用量（字节） */
  optimizedSize: number;
  /** 优化比率 */
  optimizationRatio: number;
  /** 应用的优化 */
  appliedOptimizations: string[];
  /** 处理时间（毫秒） */
  processingTime: number;
}

/**
 * 地形法线贴图优化器类
 */
export class TerrainNormalMapOptimizer {
  /** 是否启用 */
  private enabled: boolean;
  /** 是否使用压缩 */
  private useCompression: boolean;
  /** 压缩质量 */
  private compressionQuality: number;
  /** 是否使用法线贴图合并 */
  private useNormalMapMerging: boolean;
  /** 是否使用法线贴图缩放 */
  private useNormalMapScaling: boolean;
  /** 最大纹理大小 */
  private maxTextureSize: number;
  /** 是否使用法线贴图生成 */
  private useNormalMapGeneration: boolean;
  /** 法线贴图生成强度 */
  private normalMapGenerationStrength: number;
  /** 是否使用GPU加速 */
  private useGPUAcceleration: boolean;
  /** 是否启用调试 */
  private debug: boolean;
  /** 缩放因子 */
  private scaleFactor: number;
  /** 压缩格式 */
  private compressionFormat: string;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 工作线程 */
  private _worker: Worker | null;
  /** 是否支持工作线程 */
  private supportsWorkers: boolean;
  /** 是否支持GPU加速 */
  private _supportsGPUAcceleration: boolean;
  /** 渲染器 */
  private _renderer: THREE.WebGLRenderer | null;

  /**
   * 创建地形法线贴图优化器
   * @param config 配置
   */
  constructor(config: NormalMapOptimizationConfig = {}) {
    // 初始化配置
    this.enabled = config.enabled !== undefined ? config.enabled : true;
    this.useCompression = config.useCompression !== undefined ? config.useCompression : true;
    this.compressionQuality = config.compressionQuality !== undefined ? config.compressionQuality : 0.8;
    this.useNormalMapMerging = config.useNormalMapMerging !== undefined ? config.useNormalMapMerging : true;
    this.useNormalMapScaling = config.useNormalMapScaling !== undefined ? config.useNormalMapScaling : true;
    this.maxTextureSize = config.maxTextureSize || 1024;
    this.useNormalMapGeneration = config.useNormalMapGeneration !== undefined ? config.useNormalMapGeneration : true;
    this.normalMapGenerationStrength = config.normalMapGenerationStrength !== undefined ? config.normalMapGenerationStrength : 1.0;
    this.useGPUAcceleration = config.useGPUAcceleration !== undefined ? config.useGPUAcceleration : true;
    this.debug = config.debug !== undefined ? config.debug : false;
    this.scaleFactor = 0.5; // 默认缩放因子
    this.compressionFormat = 'DXT5'; // 默认压缩格式

    // 初始化属性
    this.eventEmitter = new EventEmitter();
    this._worker = null;
    this.supportsWorkers = typeof Worker !== 'undefined';
    this._supportsGPUAcceleration = this.checkGPUAccelerationSupport();
    this._renderer = null;

    // 初始化工作线程
    if (this.supportsWorkers) {
      this.initWorker();
    }
  }

  /**
   * 检查GPU加速支持
   * @returns 是否支持GPU加速
   */
  private checkGPUAccelerationSupport(): boolean {
    // 检查WebGL2支持
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2');
      return !!gl;
    } catch (error) {
      return false;
    }
  }

  /**
   * 初始化工作线程
   */
  private initWorker(): void {
    try {
      // 创建工作线程
      // 注意：实际实现需要创建一个工作线程脚本
      // this.worker = new Worker('path/to/normal-map-optimizer-worker.js');

      // 注册消息处理器
      // this.worker.onmessage = this.handleWorkerMessage.bind(this);
    } catch (error) {
      Debug.warn('TerrainNormalMapOptimizer', '初始化工作线程失败:', error);
      this.supportsWorkers = false;
    }
  }

  /**
   * 处理工作线程消息
   * @param event 消息事件
   */
  private _handleWorkerMessage(event: MessageEvent): void {
    const { type, data } = event.data;

    switch (type) {
      case 'progress':
        this.eventEmitter.emit(NormalMapOptimizationEventType.OPTIMIZATION_PROGRESS, data.progress);
        break;
      case 'complete':
        this.eventEmitter.emit(NormalMapOptimizationEventType.OPTIMIZATION_COMPLETED, data.result);
        break;
      case 'error':
        this.eventEmitter.emit(NormalMapOptimizationEventType.OPTIMIZATION_ERROR, data.error);
        break;
    }
  }

  /**
   * 设置渲染器
   * @param renderer 渲染器
   */
  public setRenderer(renderer: THREE.WebGLRenderer): void {
    this._renderer = renderer;
  }

  /**
   * 优化法线贴图
   * @param texture 纹理
   * @returns 优化结果
   */
  public async optimizeNormalMap(texture: THREE.Texture): Promise<OptimizationResult> {
    if (!this.enabled || !texture) {
      return this.createDummyResult(texture);
    }

    try {
      // 发出优化开始事件
      this.eventEmitter.emit(NormalMapOptimizationEventType.OPTIMIZATION_STARTED, texture);

      // 记录开始时间
      const startTime = performance.now();

      // 创建结果对象
      const result: OptimizationResult = {
        originalTexture: texture,
        optimizedTexture: texture.clone(),
        originalSize: this.calculateTextureSize(texture),
        optimizedSize: 0,
        optimizationRatio: 1,
        appliedOptimizations: [],
        processingTime: 0
      };

      // 应用优化
      if (this.useNormalMapScaling) {
        await this.applyNormalMapScaling(result);
        result.appliedOptimizations.push('scaling');
      }

      if (this.useCompression) {
        await this.applyCompression(result);
        result.appliedOptimizations.push('compression');
      }

      // 计算优化后大小
      result.optimizedSize = this.calculateTextureSize(result.optimizedTexture);
      result.optimizationRatio = result.originalSize / result.optimizedSize;
      result.processingTime = performance.now() - startTime;

      // 发出优化完成事件
      this.eventEmitter.emit(NormalMapOptimizationEventType.OPTIMIZATION_COMPLETED, result);

      return result;
    } catch (error) {
      // 发出优化错误事件
      this.eventEmitter.emit(NormalMapOptimizationEventType.OPTIMIZATION_ERROR, error);
      Debug.error('TerrainNormalMapOptimizer', '优化法线贴图失败:', error);
      return this.createDummyResult(texture);
    }
  }

  /**
   * 创建虚拟结果
   * @param texture 纹理
   * @returns 优化结果
   */
  private createDummyResult(texture: THREE.Texture): OptimizationResult {
    const size = this.calculateTextureSize(texture);
    return {
      originalTexture: texture,
      optimizedTexture: texture.clone(),
      originalSize: size,
      optimizedSize: size,
      optimizationRatio: 1,
      appliedOptimizations: [],
      processingTime: 0
    };
  }

  /**
   * 计算纹理大小
   * @param texture 纹理
   * @returns 大小（字节）
   */
  private calculateTextureSize(texture: THREE.Texture): number {
    if (!texture.image) {
      return 0;
    }

    const width = texture.image.width || 0;
    const height = texture.image.height || 0;

    // 假设每个像素4字节（RGBA）
    return width * height * 4;
  }

  /**
   * 应用法线贴图缩放
   * @param result 优化结果
   */
  private async applyNormalMapScaling(result: OptimizationResult): Promise<void> {
    const texture = result.originalTexture;

    // 检查纹理是否有效
    if (!texture || !texture.image) {
      Debug.warn('TerrainNormalMapOptimizer', '无效的法线贴图');
      return;
    }

    // 获取原始尺寸
    const originalWidth = texture.image.width;
    const originalHeight = texture.image.height;

    // 计算新尺寸
    const newWidth = Math.max(1, Math.floor(originalWidth * this.scaleFactor));
    const newHeight = Math.max(1, Math.floor(originalHeight * this.scaleFactor));

    // 如果尺寸没有变化，则跳过
    if (newWidth === originalWidth && newHeight === originalHeight) {
      result.optimizedTexture = texture;
      return;
    }

    // 创建画布
    const canvas = document.createElement('canvas');
    canvas.width = newWidth;
    canvas.height = newHeight;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      Debug.error('TerrainNormalMapOptimizer', '无法创建2D上下文');
      result.optimizedTexture = texture;
      return;
    }

    // 绘制缩放后的图像
    ctx.drawImage(texture.image, 0, 0, newWidth, newHeight);

    // 获取像素数据
    const imageData = ctx.getImageData(0, 0, newWidth, newHeight);
    const pixels = imageData.data;

    // 重新归一化法线
    for (let i = 0; i < pixels.length; i += 4) {
      // 从RGB获取法线向量
      const nx = pixels[i] / 127.5 - 1;
      const ny = pixels[i + 1] / 127.5 - 1;
      const nz = pixels[i + 2] / 127.5 - 1;

      // 归一化
      const length = Math.sqrt(nx * nx + ny * ny + nz * nz);
      const scale = length > 0 ? 1 / length : 1;

      // 转换回RGB
      pixels[i] = Math.round((nx * scale * 0.5 + 0.5) * 255);
      pixels[i + 1] = Math.round((ny * scale * 0.5 + 0.5) * 255);
      pixels[i + 2] = Math.round((nz * scale * 0.5 + 0.5) * 255);
      // 保持Alpha不变
    }

    // 更新画布
    ctx.putImageData(imageData, 0, 0);

    // 创建新纹理
    const newTexture = new THREE.CanvasTexture(canvas);
    newTexture.wrapS = texture.wrapS;
    newTexture.wrapT = texture.wrapT;
    newTexture.minFilter = texture.minFilter;
    newTexture.magFilter = texture.magFilter;
    newTexture.colorSpace = texture.colorSpace;

    // 更新结果
    result.optimizedTexture = newTexture;
    result.optimizedSize = this.calculateTextureSize(newTexture);
    result.optimizationRatio = result.originalSize / result.optimizedSize;
    result.appliedOptimizations.push('scaling');

    Debug.log('TerrainNormalMapOptimizer', `法线贴图缩放完成: ${originalWidth}x${originalHeight} -> ${newWidth}x${newHeight}`);
  }

  /**
   * 应用压缩
   * @param result 优化结果
   */
  private async applyCompression(result: OptimizationResult): Promise<void> {
    const texture = result.optimizedTexture || result.originalTexture;

    // 检查纹理是否有效
    if (!texture || !texture.image) {
      Debug.warn('TerrainNormalMapOptimizer', '无效的法线贴图');
      return;
    }

    // 如果不使用压缩，则跳过
    if (!this.useCompression) {
      return;
    }

    try {
      // 简单的纹理压缩实现
      // 注意：这是一个简化的实现，实际项目中可能需要更复杂的压缩算法
      const compressedTexture = await this.simpleTextureCompression(texture);

      // 更新结果
      result.optimizedTexture = compressedTexture;
      result.optimizedSize = this.calculateTextureSize(compressedTexture);
      result.optimizationRatio = result.originalSize / result.optimizedSize;
      result.appliedOptimizations.push('compression');

      Debug.log('TerrainNormalMapOptimizer', `法线贴图压缩完成: 格式=${this.compressionFormat}, 质量=${this.compressionQuality}`);
    } catch (error) {
      Debug.error('TerrainNormalMapOptimizer', `法线贴图压缩失败: ${error}`);
    }
  }

  /**
   * 简单的纹理压缩
   * @param texture 纹理
   * @returns 压缩后的纹理
   */
  private async simpleTextureCompression(texture: THREE.Texture): Promise<THREE.Texture> {
    // 简化的压缩实现：通过降低质量来模拟压缩
    if (!texture || !texture.image) {
      return texture;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      return texture;
    }

    // 获取原始尺寸
    const width = texture.image.width;
    const height = texture.image.height;

    canvas.width = width;
    canvas.height = height;

    // 绘制原始图像
    ctx.drawImage(texture.image, 0, 0);

    // 应用质量压缩（通过重新编码为JPEG来模拟）
    const dataURL = canvas.toDataURL('image/jpeg', this.compressionQuality);

    // 创建新的图像
    const img = new Image();
    img.src = dataURL;

    // 等待图像加载
    await new Promise<void>((resolve) => {
      img.onload = () => resolve();
    });

    // 创建新纹理
    const compressedTexture = new THREE.Texture(img);
    compressedTexture.wrapS = texture.wrapS;
    compressedTexture.wrapT = texture.wrapT;
    compressedTexture.minFilter = texture.minFilter;
    compressedTexture.magFilter = texture.magFilter;
    compressedTexture.needsUpdate = true;

    return compressedTexture;
  }

  /**
   * 从高度图生成法线贴图
   * @param heightMap 高度图
   * @param strength 强度
   * @returns 法线贴图
   */
  public async generateNormalMapFromHeightMap(heightMap: THREE.Texture, strength: number = 1.0): Promise<THREE.Texture> {
    // 检查高度图是否有效
    if (!heightMap || !heightMap.image) {
      Debug.warn('TerrainNormalMapOptimizer', '无效的高度图');
      return new THREE.Texture();
    }

    // 获取高度图尺寸
    const width = heightMap.image.width;
    const height = heightMap.image.height;

    // 创建画布
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      Debug.error('TerrainNormalMapOptimizer', '无法创建2D上下文');
      return heightMap;
    }

    // 绘制高度图
    ctx.drawImage(heightMap.image, 0, 0);

    // 获取像素数据
    const imageData = ctx.getImageData(0, 0, width, height);
    const pixels = imageData.data;

    // 创建法线贴图数据
    const normalMapData = new Uint8ClampedArray(width * height * 4);

    // 计算法线
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        // 获取相邻像素的高度值
        const left = x > 0 ? this.getHeightValue(pixels, x - 1, y, width) : this.getHeightValue(pixels, x, y, width);
        const right = x < width - 1 ? this.getHeightValue(pixels, x + 1, y, width) : this.getHeightValue(pixels, x, y, width);
        const top = y > 0 ? this.getHeightValue(pixels, x, y - 1, width) : this.getHeightValue(pixels, x, y, width);
        const bottom = y < height - 1 ? this.getHeightValue(pixels, x, y + 1, width) : this.getHeightValue(pixels, x, y, width);

        // 计算法线向量
        const dx = (left - right) * strength;
        const dy = (top - bottom) * strength;
        const dz = 1.0;

        // 归一化
        const length = Math.sqrt(dx * dx + dy * dy + dz * dz);
        const nx = dx / length;
        const ny = dy / length;
        const nz = dz / length;

        // 转换为RGB（法线贴图格式）
        const index = (y * width + x) * 4;
        normalMapData[index] = Math.round((nx * 0.5 + 0.5) * 255);     // R
        normalMapData[index + 1] = Math.round((ny * 0.5 + 0.5) * 255); // G
        normalMapData[index + 2] = Math.round((nz * 0.5 + 0.5) * 255); // B
        normalMapData[index + 3] = 255;                                // A
      }
    }

    // 创建法线贴图
    const normalMapImageData = new ImageData(normalMapData, width, height);
    ctx.putImageData(normalMapImageData, 0, 0);

    // 创建纹理
    const normalMap = new THREE.CanvasTexture(canvas);
    normalMap.wrapS = heightMap.wrapS;
    normalMap.wrapT = heightMap.wrapT;
    normalMap.minFilter = THREE.LinearFilter;
    normalMap.magFilter = THREE.LinearFilter;

    Debug.log('TerrainNormalMapOptimizer', `从高度图生成法线贴图完成: ${width}x${height}, 强度=${strength}`);

    return normalMap;
  }

  /**
   * 获取高度值
   * @param pixels 像素数据
   * @param x X坐标
   * @param y Y坐标
   * @param width 宽度
   * @returns 高度值
   */
  private getHeightValue(pixels: Uint8ClampedArray, x: number, y: number, width: number): number {
    const index = (y * width + x) * 4;
    // 使用灰度值作为高度
    return (pixels[index] + pixels[index + 1] + pixels[index + 2]) / 3;
  }

  /**
   * 应用到地形组件
   * @param entity 实体
   * @param component 地形组件
   * @returns 优化结果数组
   */
  public async applyToTerrainComponent(entity: Entity, component: TerrainComponent): Promise<OptimizationResult[]> {
    if (!entity || !component) {
      Debug.warn('TerrainNormalMapOptimizer', '无效的实体或地形组件');
      return [];
    }

    try {
      // 获取地形纹理层
      const textureLayers = component.layers;
      if (!textureLayers || textureLayers.length === 0) {
        Debug.warn('TerrainNormalMapOptimizer', '无法获取地形纹理层');
        return [];
      }

      // 优化结果数组
      const results: OptimizationResult[] = [];

      // 优化每个纹理层的法线贴图
      for (const layer of textureLayers) {
        if (layer.normalMap) {
          // 如果是字符串，则跳过
          if (typeof layer.normalMap === 'string') {
            continue;
          }

          // 优化法线贴图
          const result = await this.optimizeNormalMap(layer.normalMap);
          results.push(result);

          // 更新纹理层的法线贴图
          layer.normalMap = result.optimizedTexture;
        } else if (this.useNormalMapGeneration && layer.texture && typeof layer.texture !== 'string') {
          // 如果没有法线贴图但启用了生成，则从纹理生成
          const normalMap = await this.generateNormalMapFromHeightMap(layer.texture, this.normalMapGenerationStrength);
          layer.normalMap = normalMap;

          // 创建结果
          const result: OptimizationResult = {
            originalTexture: layer.texture,
            optimizedTexture: normalMap,
            originalSize: 0,
            optimizedSize: this.calculateTextureSize(normalMap),
            optimizationRatio: 0,
            appliedOptimizations: ['generation'],
            processingTime: 0
          };

          results.push(result);
        }
      }

      return results;
    } catch (error) {
      Debug.error('TerrainNormalMapOptimizer', '应用到地形组件失败:', error);
      return [];
    }
  }

  /**
   * 注册事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: NormalMapOptimizationEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: NormalMapOptimizationEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
