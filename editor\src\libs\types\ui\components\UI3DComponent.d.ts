/**
 * UI3DComponent.ts
 *
 * 3D UI元素组件，用于创建和管理3D空间中的界面元素
 */
import { Vector2, Vector3, Mesh, Group, Texture, Object3D, Euler } from 'three';
import { UIComponent, UIComponentProps } from './UIComponent';
/**
 * 3D UI元素属性
 */
export interface UI3DComponentProps extends UIComponentProps {
    mesh?: Mesh;
    texture?: Texture;
    canvas?: HTMLCanvasElement;
    group?: Group;
    rotation?: Vector3 | Euler;
    scale?: Vector3;
    lookAt?: Vector3;
    billboardMode?: BillboardMode;
    transparent?: boolean;
    opacity?: number;
    color?: string;
    emissive?: string;
    emissiveIntensity?: number;
    textContent?: string;
    fontSize?: number;
    fontFamily?: string;
    fontColor?: string;
    textAlign?: 'left' | 'center' | 'right';
    textBaseline?: 'top' | 'middle' | 'bottom';
    interactionDistance?: number;
    hoverColor?: string;
    activeColor?: string;
}
/**
 * 广告牌模式枚举
 */
export declare enum BillboardMode {
    NONE = "none",
    FULL = "full",
    Y_AXIS = "y-axis"
}
/**
 * 3D UI元素组件
 * 用于创建和管理3D空间中的界面元素
 */
export declare class UI3DComponent extends UIComponent {
    mesh?: Mesh;
    texture?: Texture;
    canvas?: HTMLCanvasElement;
    context?: CanvasRenderingContext2D;
    group?: Group;
    rotation: Vector3 | Euler;
    scale: Vector3;
    lookAt?: Vector3;
    billboardMode: BillboardMode;
    transparent: boolean;
    color?: string;
    emissive?: string;
    emissiveIntensity: number;
    textContent: string;
    fontSize: number;
    fontFamily: string;
    fontColor: string;
    textAlign: 'left' | 'center' | 'right';
    textBaseline: 'top' | 'middle' | 'bottom';
    interactionDistance: number;
    hoverColor?: string;
    activeColor?: string;
    isHovered: boolean;
    isActive: boolean;
    needsUpdate: boolean;
    /**
     * 构造函数
     * @param props 3D UI元素属性
     */
    constructor(props?: UI3DComponentProps);
    /**
     * 创建网格
     */
    private createMesh;
    /**
     * 更新画布内容
     */
    private updateCanvas;
    /**
     * 更新UI元素
     * @param deltaTime 时间增量
     * @param camera 相机对象（用于广告牌模式）
     */
    update(deltaTime: number, camera?: Object3D): void;
    /**
     * 渲染UI元素
     */
    render(): void;
    /**
     * 销毁UI元素
     */
    dispose(): void;
    /**
     * 设置文本内容
     * @param text 文本内容
     */
    setText(text: string): void;
    /**
     * 设置字体大小
     * @param size 字体大小
     */
    setFontSize(size: number): void;
    /**
     * 设置字体颜色
     * @param color 字体颜色
     */
    setFontColor(color: string): void;
    /**
     * 设置悬停状态
     * @param hovered 是否悬停
     */
    setHovered(hovered: boolean): void;
    /**
     * 设置激活状态
     * @param active 是否激活
     */
    setActive(active: boolean): void;
    /**
     * 获取3D对象
     * @returns 3D对象（Group）
     */
    getObject3D(): Object3D | undefined;
    /**
     * 设置位置
     * @param position 新位置
     */
    setPosition(position: Vector3 | Vector2): void;
    /**
     * 设置旋转
     * @param rotation 新旋转
     */
    setRotation(rotation: Vector3 | Euler): void;
    /**
     * 设置缩放
     * @param scale 新缩放
     */
    setScale(scale: Vector3): void;
}
