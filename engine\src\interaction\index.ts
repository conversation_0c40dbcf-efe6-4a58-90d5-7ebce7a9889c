/**
 * interaction/index.ts
 *
 * 导出交互系统的所有组件和类
 */

// 导出交互系统
export { InteractionSystem } from './InteractionSystem';
export type { InteractionSystemConfig } from './InteractionSystem';

// 导出可交互组件
export { InteractableComponent, InteractionType } from './components/InteractableComponent';
export type { InteractableComponentConfig, InteractionCallback } from './components/InteractableComponent';

// 导出交互事件组件
export { InteractionEventComponent, InteractionEventType, InteractionEvent } from './components/InteractionEventComponent';
export type { InteractionEventComponentConfig, InteractionEventData, InteractionEventListener } from './components/InteractionEventComponent';

// 导出交互提示组件
export { InteractionPromptComponent, PromptPositionType } from './components/InteractionPromptComponent';
export type { InteractionPromptComponentConfig } from './components/InteractionPromptComponent';

// 导出交互高亮组件
export { InteractionHighlightComponent, HighlightType } from './components/InteractionHighlightComponent';
export type { InteractionHighlightComponentConfig } from './components/InteractionHighlightComponent';

// 导出抓取系统
export { GrabSystem } from './systems/GrabSystem';
export type { GrabSystemConfig } from './systems/GrabSystem';

// 导出可抓取组件
export { GrabbableComponent, GrabType, Hand } from './components/GrabbableComponent';
export type { GrabbableComponentConfig } from './components/GrabbableComponent';

// 导出抓取者组件
export { GrabberComponent } from './components/GrabberComponent';
export type { GrabberComponentConfig } from './components/GrabberComponent';

// 导出被抓取组件
export { GrabbedComponent } from './components/GrabbedComponent';
export type { GrabbedComponentConfig } from './components/GrabbedComponent';

// 导出物理抓取组件
export { PhysicsGrabComponent } from './components/PhysicsGrabComponent';
export type { PhysicsGrabComponentConfig } from './components/PhysicsGrabComponent';

// 导出抓取状态
export { GrabState, GrabEventType } from './state/GrabState';
export type { GrabEventData } from './state/GrabState';
