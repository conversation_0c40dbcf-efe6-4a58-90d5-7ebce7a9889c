/**
 * 事务探索服务
 * 用于自动发现和注册事务参与者
 */
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DiscoveryService, MetadataScanner, Reflector } from '@nestjs/core';
import { InstanceWrapper } from '@nestjs/core/injector/instance-wrapper';
import { TRANSACTION_PARTICIPANT_METADATA } from './transaction.constants';
import { TransactionCoordinatorService } from './transaction-coordinator.service';
import { TransactionParticipantHandler } from './transaction.interface';

@Injectable()
export class TransactionExplorerService implements OnModuleInit {
  private readonly logger = new Logger(TransactionExplorerService.name);

  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
    private readonly reflector: Reflector,
    private readonly transactionCoordinatorService: TransactionCoordinatorService,
  ) {}

  /**
   * 模块初始化时探索和注册所有事务参与者
   */
  onModuleInit() {
    this.explore();
  }

  /**
   * 探索所有事务参与者
   */
  explore() {
    const providers = this.discoveryService.getProviders();
    
    // 探索提供者中的事务参与者
    providers.forEach((wrapper) => {
      this.exploreTransactionParticipantsInProvider(wrapper);
    });
    
    this.logger.log('事务参与者探索完成');
  }

  /**
   * 在提供者中探索事务参与者
   * @param wrapper 实例包装器
   */
  private exploreTransactionParticipantsInProvider(wrapper: InstanceWrapper) {
    const { instance } = wrapper;
    
    if (!instance || typeof instance !== 'object') {
      return;
    }
    
    // 获取类上的事务参与者元数据
    const participantMetadata = this.reflector.get(TRANSACTION_PARTICIPANT_METADATA, instance.constructor);
    
    if (!participantMetadata) {
      return;
    }
    
    const { participantId } = participantMetadata;
    
    // 检查实例是否实现了事务参与者接口
    if (
      typeof instance.prepare === 'function' &&
      typeof instance.commit === 'function' &&
      typeof instance.rollback === 'function'
    ) {
      // 注册事务参与者
      this.transactionCoordinatorService.registerParticipantHandler(
        participantId,
        instance as TransactionParticipantHandler,
      );
      
      this.logger.debug(`已注册事务参与者: ${participantId}`);
    } else {
      this.logger.warn(
        `类 ${instance.constructor.name} 被标记为事务参与者，但未实现所需的方法`,
      );
    }
  }
}
