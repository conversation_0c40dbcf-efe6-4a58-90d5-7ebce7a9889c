/**
 * 视觉脚本系统
 * 负责管理和执行视觉脚本
 */
import { System } from '../core/System';
import type { World } from '../core/World';
import { NodeRegistry } from './nodes/NodeRegistry';
import { ValueTypeRegistry } from './values/ValueTypeRegistry';
/**
 * 视觉脚本系统配置
 */
export interface VisualScriptSystemOptions {
    /** 是否自动初始化 */
    autoInit?: boolean;
    /** 默认脚本域 */
    defaultDomain?: string;
}
/**
 * 视觉脚本系统
 * 负责管理和执行视觉脚本
 */
export declare class VisualScriptSystem extends System {
    /** 系统优先级 */
    static readonly PRIORITY = 200;
    /** 节点注册表 */
    private nodeRegistry;
    /** 值类型注册表 */
    private valueTypeRegistry;
    /** 视觉脚本引擎实例映射 */
    private engineInstances;
    /** 默认脚本域 */
    private defaultDomain;
    /** 脚本域注册表 */
    private domainRegistries;
    /** 事件发射器 */
    private eventEmitter;
    /** 世界引用 */
    protected world: World | null;
    /**
     * 创建视觉脚本系统
     * @param options 系统选项
     */
    constructor(options?: VisualScriptSystemOptions);
    /**
     * 初始化系统
     * @param world 世界实例
     */
    initialize(world?: World): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 注册脚本域
     * @param domain 脚本域名称
     * @param nodeRegistry 节点注册表
     * @param valueTypeRegistry 值类型注册表
     */
    registerDomain(domain: string, nodeRegistry: NodeRegistry, valueTypeRegistry: ValueTypeRegistry): void;
    /**
     * 获取脚本域注册表
     * @param domain 脚本域名称
     * @returns 脚本域注册表
     */
    getDomainRegistry(domain: string): {
        nodes: NodeRegistry;
        values: ValueTypeRegistry;
    } | undefined;
    /**
     * 获取默认脚本域
     * @returns 默认脚本域名称
     */
    getDefaultDomain(): string;
    /**
     * 设置默认脚本域
     * @param domain 默认脚本域名称
     */
    setDefaultDomain(domain: string): void;
    /**
     * 注册核心节点和值类型
     */
    private registerCoreNodesAndValueTypes;
    /**
     * 实体添加事件处理
     * @param entity 添加的实体
     */
    private onEntityAdded;
    /**
     * 实体移除事件处理
     * @param entity 移除的实体
     */
    private onEntityRemoved;
    /**
     * 初始化实体脚本
     * @param entity 实体
     */
    private initializeEntityScript;
    /**
     * 清理实体脚本
     * @param entity 实体
     */
    private cleanupEntityScript;
    /**
     * 销毁系统
     */
    dispose(): void;
}
