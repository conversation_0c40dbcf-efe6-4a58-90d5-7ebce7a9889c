#!/bin/bash

# DL引擎服务状态检查脚本
# 用于检查所有微服务的健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[===]${NC} $1"
}

# 检查HTTP服务健康状态
check_http_health() {
    local service_name=$1
    local url=$2
    local timeout=${3:-5}
    
    if curl -f -s --max-time $timeout "$url" > /dev/null 2>&1; then
        log_success "$service_name 健康检查通过"
        return 0
    else
        log_error "$service_name 健康检查失败"
        return 1
    fi
}

# 检查容器状态
check_container_status() {
    local container_name=$1
    local service_name=$2
    
    if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        local status=$(docker inspect --format='{{.State.Status}}' $container_name 2>/dev/null)
        if [ "$status" = "running" ]; then
            log_success "$service_name 容器运行正常"
            return 0
        else
            log_error "$service_name 容器状态异常: $status"
            return 1
        fi
    else
        log_error "$service_name 容器未运行"
        return 1
    fi
}

# 检查端口是否开放
check_port() {
    local port=$1
    local service_name=$2
    
    if nc -z localhost $port 2>/dev/null; then
        log_success "$service_name 端口 $port 开放"
        return 0
    else
        log_error "$service_name 端口 $port 未开放"
        return 1
    fi
}

# 检查数据库连接
check_mysql() {
    log_header "检查MySQL数据库"
    
    if check_container_status "dl-engine-mysql" "MySQL"; then
        # 检查MySQL连接
        if docker exec dl-engine-mysql mysqladmin ping -h localhost -u root -p${MYSQL_ROOT_PASSWORD:-dl_engine_password_2024} --silent 2>/dev/null; then
            log_success "MySQL数据库连接正常"
            
            # 检查数据库
            local databases=$(docker exec dl-engine-mysql mysql -u root -p${MYSQL_ROOT_PASSWORD:-dl_engine_password_2024} -e "SHOW DATABASES;" 2>/dev/null | grep ir_engine || true)
            if [ -n "$databases" ]; then
                log_success "业务数据库存在"
                echo "$databases" | while read db; do
                    echo "  - $db"
                done
            else
                log_warning "业务数据库不存在"
            fi
        else
            log_error "MySQL数据库连接失败"
        fi
    fi
    
    check_port 3306 "MySQL"
}

# 检查Redis缓存
check_redis() {
    log_header "检查Redis缓存"
    
    if check_container_status "dl-engine-redis" "Redis"; then
        if docker exec dl-engine-redis redis-cli ping > /dev/null 2>&1; then
            log_success "Redis缓存连接正常"
            
            # 检查Redis信息
            local redis_info=$(docker exec dl-engine-redis redis-cli info server 2>/dev/null | grep redis_version || true)
            if [ -n "$redis_info" ]; then
                echo "  - $redis_info"
            fi
        else
            log_error "Redis缓存连接失败"
        fi
    fi
    
    check_port 6379 "Redis"
}

# 检查服务注册中心
check_service_registry() {
    log_header "检查服务注册中心"
    
    check_container_status "dl-engine-service-registry" "服务注册中心"
    check_port 3010 "服务注册中心微服务"
    check_port 4010 "服务注册中心HTTP"
    check_http_health "服务注册中心" "http://localhost:4010/health"
}

# 检查用户服务
check_user_service() {
    log_header "检查用户服务"
    
    check_container_status "dl-engine-user-service" "用户服务"
    check_port 3001 "用户服务微服务"
    check_port 4001 "用户服务HTTP"
    check_http_health "用户服务" "http://localhost:4001/health"
}

# 检查项目服务
check_project_service() {
    log_header "检查项目服务"
    
    check_container_status "dl-engine-project-service" "项目服务"
    check_port 3002 "项目服务微服务"
    check_port 4002 "项目服务HTTP"
    check_http_health "项目服务" "http://localhost:4002/health"
}

# 检查资产服务
check_asset_service() {
    log_header "检查资产服务"
    
    check_container_status "dl-engine-asset-service" "资产服务"
    check_port 3003 "资产服务微服务"
    check_port 4003 "资产服务HTTP"
    check_http_health "资产服务" "http://localhost:4003/health"
}

# 检查渲染服务
check_render_service() {
    log_header "检查渲染服务"
    
    check_container_status "dl-engine-render-service" "渲染服务"
    check_port 3004 "渲染服务微服务"
    check_port 4004 "渲染服务HTTP"
    check_http_health "渲染服务" "http://localhost:4004/health"
}

# 检查协作服务
check_collaboration_service() {
    log_header "检查协作服务"
    
    check_container_status "dl-engine-collaboration-service-1" "协作服务实例1"
    check_container_status "dl-engine-collaboration-service-2" "协作服务实例2"
    check_container_status "dl-engine-collaboration-lb" "协作服务负载均衡"
    
    check_port 3005 "协作服务实例1"
    check_port 3006 "协作服务实例2"
    check_port 3007 "协作服务负载均衡"
    
    check_http_health "协作服务" "http://localhost:3007/health"
}

# 检查API网关
check_api_gateway() {
    log_header "检查API网关"
    
    check_container_status "dl-engine-api-gateway" "API网关"
    check_port 3000 "API网关"
    check_http_health "API网关" "http://localhost:3000/api/health"
}

# 检查前端编辑器
check_editor() {
    log_header "检查前端编辑器"
    
    check_container_status "dl-engine-editor" "前端编辑器"
    check_port 80 "前端编辑器"
    check_http_health "前端编辑器" "http://localhost" 10
}

# 检查监控服务
check_monitoring() {
    log_header "检查监控服务"
    
    # 检查监控服务
    if docker ps --format "{{.Names}}" | grep -q "monitoring-service"; then
        check_container_status "monitoring-service" "监控服务"
        check_port 3100 "监控服务"
        check_http_health "监控服务" "http://localhost:3100/api/v1/health"
    else
        log_warning "监控服务未启动"
    fi
    
    # 检查Prometheus
    if docker ps --format "{{.Names}}" | grep -q "prometheus"; then
        check_container_status "prometheus" "Prometheus"
        check_port 9090 "Prometheus"
        check_http_health "Prometheus" "http://localhost:9090/-/healthy"
    else
        log_warning "Prometheus未启动"
    fi
    
    # 检查Grafana
    if docker ps --format "{{.Names}}" | grep -q "grafana"; then
        check_container_status "grafana" "Grafana"
        check_port 3000 "Grafana"
        check_http_health "Grafana" "http://localhost:3000/api/health"
    else
        log_warning "Grafana未启动"
    fi
    
    # 检查Elasticsearch
    if docker ps --format "{{.Names}}" | grep -q "elasticsearch"; then
        check_container_status "elasticsearch" "Elasticsearch"
        check_port 9200 "Elasticsearch"
        check_http_health "Elasticsearch" "http://localhost:9200/_cluster/health"
    else
        log_warning "Elasticsearch未启动"
    fi
    
    # 检查Kibana
    if docker ps --format "{{.Names}}" | grep -q "kibana"; then
        check_container_status "kibana" "Kibana"
        check_port 5601 "Kibana"
        check_http_health "Kibana" "http://localhost:5601/api/status"
    else
        log_warning "Kibana未启动"
    fi
}

# 检查系统资源
check_system_resources() {
    log_header "检查系统资源"
    
    # 检查内存使用
    local memory_info=$(free -h | grep Mem)
    echo "内存使用情况: $memory_info"
    
    # 检查磁盘使用
    local disk_info=$(df -h / | tail -1)
    echo "磁盘使用情况: $disk_info"
    
    # 检查Docker资源使用
    echo
    echo "Docker容器资源使用："
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# 生成状态报告
generate_report() {
    local total_checks=0
    local passed_checks=0
    local failed_checks=0
    local warning_checks=0
    
    echo
    log_header "服务状态总结"
    
    # 统计检查结果（这里简化处理，实际应该在每个检查函数中计数）
    echo "详细状态报告已生成，请查看上述检查结果"
    
    echo
    log_info "快速访问链接："
    echo "  前端编辑器: http://localhost"
    echo "  API文档: http://localhost:3000/api/docs"
    echo "  服务注册中心: http://localhost:4010/api/docs"
    echo "  Grafana监控: http://localhost:3000 (admin/admin)"
    echo "  Prometheus: http://localhost:9090"
    echo "  Kibana日志: http://localhost:5601"
}

# 主函数
main() {
    echo "=========================================="
    echo "    DL引擎服务状态检查"
    echo "=========================================="
    
    # 检查Docker是否运行
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker未运行，请先启动Docker"
        exit 1
    fi
    
    # 根据参数决定检查范围
    case "${1:-all}" in
        "infrastructure"|"infra")
            check_mysql
            check_redis
            ;;
        "services")
            check_service_registry
            check_user_service
            check_project_service
            check_asset_service
            check_render_service
            check_collaboration_service
            check_api_gateway
            ;;
        "frontend")
            check_editor
            ;;
        "monitoring")
            check_monitoring
            ;;
        "system")
            check_system_resources
            ;;
        "all"|*)
            check_mysql
            check_redis
            check_service_registry
            check_user_service
            check_project_service
            check_asset_service
            check_render_service
            check_collaboration_service
            check_api_gateway
            check_editor
            check_monitoring
            check_system_resources
            generate_report
            ;;
    esac
}

# 显示帮助信息
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "DL引擎服务状态检查脚本"
    echo
    echo "用法: $0 [检查范围]"
    echo
    echo "检查范围:"
    echo "  all           检查所有服务（默认）"
    echo "  infrastructure 检查基础设施（MySQL, Redis）"
    echo "  services      检查业务微服务"
    echo "  frontend      检查前端编辑器"
    echo "  monitoring    检查监控服务"
    echo "  system        检查系统资源"
    echo "  --help, -h    显示帮助信息"
    exit 0
fi

main "$@"
