/**
 * 物理材质工厂
 * 用于创建和管理物理材质
 */
import * as CANNON from 'cannon-es';
/**
 * 物理材质工厂
 */
export declare class PhysicsMaterialFactory {
    /** 材质映射 */
    private static materials;
    /** 接触材质映射 */
    private static contactMaterials;
    /** 默认材质 */
    private static defaultMaterial;
    /**
     * 初始化物理材质工厂
     */
    static initialize(): void;
    /**
     * 创建材质
     * @param name 材质名称
     * @param friction 摩擦力
     * @param restitution 恢复系数
     * @returns 材质
     */
    static createMaterial(name: string, friction?: number, restitution?: number): CANNON.Material;
    /**
     * 获取材质
     * @param name 材质名称
     * @returns 材质
     */
    static getMaterial(name: string): CANNON.Material;
    /**
     * 创建接触材质
     * @param materialA 材质A
     * @param materialB 材质B
     * @param friction 摩擦力
     * @param restitution 恢复系数
     * @param options 其他选项
     * @returns 接触材质
     */
    static createContactMaterial(materialA: CANNON.Material, materialB: CANNON.Material, friction?: number, restitution?: number, options?: {
        contactEquationStiffness?: number;
        contactEquationRelaxation?: number;
        frictionEquationStiffness?: number;
        frictionEquationRelaxation?: number;
    }): CANNON.ContactMaterial;
    /**
     * 获取接触材质
     * @param materialA 材质A
     * @param materialB 材质B
     * @returns 接触材质
     */
    static getContactMaterial(materialA: CANNON.Material, materialB: CANNON.Material): CANNON.ContactMaterial | null;
    /**
     * 添加接触材质到物理世界
     * @param world 物理世界
     * @param contactMaterial 接触材质
     */
    static addContactMaterialToWorld(world: CANNON.World, contactMaterial: CANNON.ContactMaterial): void;
    /**
     * 添加所有接触材质到物理世界
     * @param world 物理世界
     */
    static addAllContactMaterialsToWorld(world: CANNON.World): void;
    /**
     * 获取默认材质
     * @returns 默认材质
     */
    static getDefaultMaterial(): CANNON.Material;
    /**
     * 清除所有材质
     */
    static clear(): void;
}
