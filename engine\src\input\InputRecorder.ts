/**
 * 输入录制器
 * 用于录制和回放输入
 */
import { InputManager } from './InputManager';
import { InputDevice } from './InputDevice';

/**
 * 输入事件
 */
export interface InputEvent {
  /** 时间戳 */
  timestamp: number;
  /** 设备名称 */
  deviceName: string;
  /** 键名 */
  key: string;
  /** 值 */
  value: any;
}

/**
 * 输入录制
 */
export interface InputRecording {
  /** 开始时间戳 */
  startTimestamp: number;
  /** 结束时间戳 */
  endTimestamp: number;
  /** 输入事件列表 */
  events: InputEvent[];
}

/**
 * 输入录制器选项
 */
export interface InputRecorderOptions {
  /** 是否自动开始录制 */
  autoStart?: boolean;
  /** 是否记录所有设备 */
  recordAllDevices?: boolean;
  /** 要记录的设备名称列表 */
  deviceNames?: string[];
}

/**
 * 输入录制器
 */
export class InputRecorder {
  /** 输入管理器 */
  private inputManager: InputManager;

  /** 是否正在录制 */
  private recording: boolean = false;

  /** 是否正在回放 */
  private playing: boolean = false;

  /** 当前录制 */
  private currentRecording: InputRecording | null = null;

  /** 回放开始时间戳 */
  private playbackStartTimestamp: number = 0;

  /** 回放事件索引 */
  private playbackEventIndex: number = 0;

  /** 回放回调 */
  private playbackCallback: (() => void) | null = null;

  /** 是否记录所有设备 */
  private recordAllDevices: boolean;

  /** 要记录的设备名称列表 */
  private deviceNames: string[];

  /** 设备值变化处理器 */
  private deviceValueChangeHandlers: Map<string, (event: any) => void> = new Map();

  /**
   * 创建输入录制器
   * @param options 选项
   */
  constructor(options: InputRecorderOptions = {}) {
    this.inputManager = InputManager.getInstance();
    this.recordAllDevices = options.recordAllDevices !== undefined ? options.recordAllDevices : true;
    this.deviceNames = options.deviceNames || [];

    if (options.autoStart) {
      this.startRecording();
    }
  }

  /**
   * 开始录制
   */
  public startRecording(): void {
    if (this.recording) return;

    this.recording = true;
    this.currentRecording = {
      startTimestamp: Date.now(),
      endTimestamp: 0,
      events: []
    };

    // 添加设备值变化事件监听器
    if (this.recordAllDevices) {
      // 获取所有设备
      const devices = this.getAllDevices();
      for (const device of devices) {
        this.addDeviceValueChangeHandler(device);
      }
    } else {
      // 获取指定设备
      for (const deviceName of this.deviceNames) {
        const device = this.inputManager.getDevice(deviceName);
        if (device) {
          this.addDeviceValueChangeHandler(device);
        }
      }
    }
  }

  /**
   * 停止录制
   * @returns 录制结果
   */
  public stopRecording(): InputRecording | null {
    if (!this.recording || !this.currentRecording) return null;

    this.recording = false;
    this.currentRecording.endTimestamp = Date.now();

    // 移除设备值变化事件监听器
    this.removeAllDeviceValueChangeHandlers();

    const recording = this.currentRecording;
    this.currentRecording = null;
    return recording;
  }

  /**
   * 开始回放
   * @param recording 录制
   * @param callback 回放完成回调
   */
  public startPlayback(recording: InputRecording, callback?: () => void): void {
    if (this.playing) return;

    this.playing = true;
    this.playbackStartTimestamp = Date.now();
    this.playbackEventIndex = 0;
    this.playbackCallback = callback || null;

    // 开始回放
    this.playbackNextEvent(recording);
  }

  /**
   * 停止回放
   */
  public stopPlayback(): void {
    this.playing = false;
    this.playbackCallback = null;
  }

  /**
   * 回放下一个事件
   * @param recording 录制
   */
  private playbackNextEvent(recording: InputRecording): void {
    if (!this.playing || this.playbackEventIndex >= recording.events.length) {
      // 回放完成
      this.playing = false;
      if (this.playbackCallback) {
        this.playbackCallback();
        this.playbackCallback = null;
      }
      return;
    }

    const event = recording.events[this.playbackEventIndex];
    const currentTime = Date.now() - this.playbackStartTimestamp;
    const eventTime = event.timestamp - recording.startTimestamp;

    if (currentTime >= eventTime) {
      // 回放事件
      const device = this.inputManager.getDevice(event.deviceName);
      if (device) {
        device.setValue(event.key, event.value);
      }

      // 下一个事件
      this.playbackEventIndex++;
      this.playbackNextEvent(recording);
    } else {
      // 等待下一个事件
      setTimeout(() => {
        this.playbackNextEvent(recording);
      }, eventTime - currentTime);
    }
  }

  /**
   * 添加设备值变化处理器
   * @param device 设备
   */
  private addDeviceValueChangeHandler(device: InputDevice): void {
    const deviceName = device.getName();
    const handler = (event: any) => {
      if (!this.recording || !this.currentRecording) return;

      // 记录事件
      this.currentRecording.events.push({
        timestamp: Date.now(),
        deviceName,
        key: event.key,
        value: event.value
      });
    };

    // 添加事件监听器
    for (const key of device.getKeys()) {
      device.on(`${key}Changed`, handler);
    }

    // 保存处理器
    this.deviceValueChangeHandlers.set(deviceName, handler);
  }

  /**
   * 移除设备值变化处理器
   * @param device 设备
   */
  private removeDeviceValueChangeHandler(device: InputDevice): void {
    const deviceName = device.getName();
    const handler = this.deviceValueChangeHandlers.get(deviceName);
    if (!handler) return;

    // 移除事件监听器
    for (const key of device.getKeys()) {
      device.off(`${key}Changed`, handler);
    }

    // 移除处理器
    this.deviceValueChangeHandlers.delete(deviceName);
  }

  /**
   * 移除所有设备值变化处理器
   */
  private removeAllDeviceValueChangeHandlers(): void {
    if (this.recordAllDevices) {
      // 获取所有设备
      const devices = this.getAllDevices();
      for (const device of devices) {
        this.removeDeviceValueChangeHandler(device);
      }
    } else {
      // 获取指定设备
      for (const deviceName of this.deviceNames) {
        const device = this.inputManager.getDevice(deviceName);
        if (device) {
          this.removeDeviceValueChangeHandler(device);
        }
      }
    }
  }

  /**
   * 获取所有设备
   * @returns 设备列表
   */
  private getAllDevices(): InputDevice[] {
    const devices: InputDevice[] = [];
    
    // 获取键盘设备
    const keyboard = this.inputManager.getDevice('keyboard');
    if (keyboard) devices.push(keyboard);
    
    // 获取鼠标设备
    const mouse = this.inputManager.getDevice('mouse');
    if (mouse) devices.push(mouse);
    
    // 获取触摸设备
    const touch = this.inputManager.getDevice('touch');
    if (touch) devices.push(touch);
    
    // 获取游戏手柄设备
    const gamepad = this.inputManager.getDevice('gamepad');
    if (gamepad) devices.push(gamepad);
    
    // 获取XR设备
    const xr = this.inputManager.getDevice('xr');
    if (xr) devices.push(xr);
    
    return devices;
  }

  /**
   * 保存录制到文件
   * @param recording 录制
   * @param filename 文件名
   */
  public static saveToFile(recording: InputRecording, filename: string = 'input-recording.json'): void {
    const json = JSON.stringify(recording);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    
    URL.revokeObjectURL(url);
  }

  /**
   * 从文件加载录制
   * @param file 文件
   * @returns Promise<InputRecording>
   */
  public static loadFromFile(file: File): Promise<InputRecording> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        try {
          const json = event.target?.result as string;
          const recording = JSON.parse(json) as InputRecording;
          resolve(recording);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = (error) => {
        reject(error);
      };
      
      reader.readAsText(file);
    });
  }
}
