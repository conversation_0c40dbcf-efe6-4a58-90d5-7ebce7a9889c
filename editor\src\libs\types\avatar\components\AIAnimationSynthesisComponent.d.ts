/**
 * AI动画合成组件
 * 用于管理AI生成的动画
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { type EventCallback } from '../../utils/EventEmitter';
import { AnimationGenerationRequest, AnimationGenerationResult } from '../ai/AnimationGenerationTypes';
/**
 * AI动画合成组件类型
 */
export declare const AIAnimationSynthesisComponentType = "AIAnimationSynthesisComponent";
/**
 * AI动画合成组件
 */
export declare class AIAnimationSynthesisComponent extends Component {
    /** 组件类型 */
    static readonly TYPE = "AIAnimationSynthesisComponent";
    /** 事件发射器 */
    private eventEmitter;
    /** 生成回调 */
    private generationCallback;
    /** 当前请求 */
    private currentRequest;
    /** 当前结果 */
    private currentResult;
    /** 是否正在生成 */
    private generating;
    /** 请求队列 */
    private requestQueue;
    /** 结果历史 */
    private resultHistory;
    /** 历史长度 */
    private historyLength;
    /**
     * 构造函数
     */
    constructor();
    /**
     * 获取组件类型
     */
    getType(): string;
    /**
     * 设置生成回调
     * @param callback 回调函数
     */
    setGenerationCallback(callback: (entity: Entity, request: AnimationGenerationRequest) => void): void;
    /**
     * 请求动画生成
     * @param request 请求
     * @returns 请求ID
     */
    requestAnimation(request: Omit<AnimationGenerationRequest, 'id'>): string;
    /**
     * 处理下一个请求
     */
    private processNextRequest;
    /**
     * 设置生成结果
     * @param result 结果
     */
    setGenerationResult(result: AnimationGenerationResult): void;
    /**
     * 取消请求
     * @param id 请求ID
     * @returns 是否成功取消
     */
    cancelRequest(id: string): boolean;
    /**
     * 清空队列
     */
    clearQueue(): void;
    /**
     * 获取当前请求
     */
    getCurrentRequest(): AnimationGenerationRequest | null;
    /**
     * 获取当前结果
     */
    getCurrentResult(): AnimationGenerationResult | null;
    /**
     * 是否正在生成
     */
    isGenerating(): boolean;
    /**
     * 获取队列长度
     */
    getQueueLength(): number;
    /**
     * 获取结果历史
     */
    getResultHistory(): AnimationGenerationResult[];
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: EventCallback): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: EventCallback): void;
}
