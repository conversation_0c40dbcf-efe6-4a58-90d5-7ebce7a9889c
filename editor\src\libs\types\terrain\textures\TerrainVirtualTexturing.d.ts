/**
 * 地形虚拟纹理
 * 实现虚拟纹理技术，用于高效管理大型地形纹理
 */
import * as THREE from 'three';
import { TextureCompressionOptions } from './TerrainTextureCompression';
import { TerrainComponent } from '../components/TerrainComponent';
/**
 * 虚拟纹理事件类型
 */
export declare enum VirtualTextureEventType {
    /** 页面加载 */
    PAGE_LOADED = "page_loaded",
    /** 页面卸载 */
    PAGE_UNLOADED = "page_unloaded",
    /** 反馈纹理更新 */
    FEEDBACK_TEXTURE_UPDATED = "feedback_texture_updated",
    /** 内存使用变化 */
    MEMORY_USAGE_CHANGED = "memory_usage_changed",
    /** 缓存状态变化 */
    CACHE_STATE_CHANGED = "cache_state_changed",
    /** 错误 */
    ERROR = "error"
}
/**
 * 虚拟纹理配置
 */
export interface VirtualTextureConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 物理纹理大小 */
    physicalTextureSize?: number;
    /** 页面大小 */
    pageSize?: number;
    /** 页面边框大小 */
    pageBorderSize?: number;
    /** 最大内存使用量（MB） */
    maxMemoryUsage?: number;
    /** 最大页面数量 */
    maxPageCount?: number;
    /** 最大MIP级别 */
    maxMipLevels?: number;
    /** 是否使用纹理压缩 */
    useCompression?: boolean;
    /** 纹理压缩选项 */
    compressionOptions?: TextureCompressionOptions;
    /** 是否使用GPU加速 */
    useGPUAcceleration?: boolean;
    /** 是否使用预测加载 */
    usePredictiveLoading?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 是否启用日志 */
    debug?: boolean;
}
/**
 * 虚拟纹理页面
 */
export interface VirtualTexturePage {
    /** 页面ID */
    id: string;
    /** MIP级别 */
    mipLevel: number;
    /** X坐标 */
    x: number;
    /** Y坐标 */
    y: number;
    /** 纹理 */
    texture: THREE.Texture | null;
    /** 是否已加载 */
    loaded: boolean;
    /** 最后使用时间 */
    lastUsedTime: number;
    /** 优先级 */
    priority: number;
    /** 大小（字节） */
    size: number;
}
/**
 * 地形虚拟纹理类
 */
export declare class TerrainVirtualTexturing {
    /** 是否启用 */
    private enabled;
    /** 物理纹理大小 */
    private physicalTextureSize;
    /** 页面大小 */
    private pageSize;
    /** 页面边框大小 */
    private pageBorderSize;
    /** 最大内存使用量（字节） */
    private maxMemoryUsage;
    /** 最大页面数量 */
    private maxPageCount;
    /** 最大MIP级别 */
    private maxMipLevels;
    /** 是否使用纹理压缩 */
    private useCompression;
    /** 纹理压缩选项 */
    private compressionOptions;
    /** 是否使用GPU加速 */
    private useGPUAcceleration;
    /** 是否使用预测加载 */
    private usePredictiveLoading;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 是否启用日志 */
    private debug;
    /** 物理纹理 */
    private physicalTexture;
    /** 反馈纹理 */
    private feedbackTexture;
    /** 反馈材质 */
    private feedbackMaterial;
    /** 页面表 */
    private pageTable;
    /** 页面表纹理 */
    private pageTableTexture;
    /** 页面缓存 */
    private pageCache;
    /** 页面加载队列 */
    private pageLoadQueue;
    /** 当前内存使用量（字节） */
    private currentMemoryUsage;
    /** 纹理压缩 */
    private textureCompression;
    /** 事件发射器 */
    private eventEmitter;
    /** 渲染器 */
    private renderer;
    /** 是否初始化 */
    private initialized;
    /** 源纹理 */
    private sourceTextures;
    /**
     * 创建地形虚拟纹理
     * @param config 配置
     */
    constructor(config?: VirtualTextureConfig);
    /**
     * 初始化
     */
    private initialize;
    /**
     * 创建物理纹理
     */
    private createPhysicalTexture;
    /**
     * 创建反馈纹理
     */
    private createFeedbackTexture;
    /**
     * 创建反馈材质
     */
    private createFeedbackMaterial;
    /**
     * 设置渲染器
     * @param renderer 渲染器
     */
    setRenderer(renderer: THREE.WebGLRenderer): void;
    /**
     * 更新
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 处理页面加载队列
     */
    private processPageLoadQueue;
    /**
     * 加载页面
     * @param page 页面
     */
    private loadPage;
    /**
     * 完成页面加载
     * @param page 页面
     */
    private finalizePageLoad;
    /**
     * 更新物理纹理
     * @param page 页面
     */
    private updatePhysicalTexture;
    /**
     * 更新页面表
     * @param page 页面
     */
    private updatePageTable;
    /**
     * 创建页面表纹理
     */
    private createPageTableTexture;
    /**
     * 更新页面缓存
     */
    private updatePageCache;
    /**
     * 驱逐页面
     */
    private evictPages;
    /**
     * 卸载页面
     * @param page 页面
     */
    private unloadPage;
    /**
     * 读取反馈纹理
     */
    readFeedbackTexture(): void;
    /**
     * 处理反馈数据
     * @param data 像素数据
     * @param width 宽度
     * @param height 高度
     */
    private processFeedbackData;
    /**
     * 添加预测页面
     */
    private addPredictivePages;
    /**
     * 获取物理纹理
     * @returns 物理纹理
     */
    getPhysicalTexture(): THREE.Texture | null;
    /**
     * 获取反馈纹理
     * @returns 反馈纹理
     */
    getFeedbackTexture(): THREE.WebGLRenderTarget | null;
    /**
     * 获取反馈材质
     * @returns 反馈材质
     */
    getFeedbackMaterial(): THREE.ShaderMaterial | null;
    /**
     * 注册事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    on(event: VirtualTextureEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    off(event: VirtualTextureEventType, listener: (...args: any[]) => void): void;
    /**
     * 获取指定MIP级别的源纹理
     * @param mipLevel MIP级别
     * @returns 源纹理
     */
    private getSourceTextureForMipLevel;
    /**
     * 设置源纹理
     * @param texture 纹理
     * @param mipLevel MIP级别
     */
    setSourceTexture(texture: THREE.Texture, mipLevel?: number): void;
    /**
     * 生成MIP链
     * @param baseTexture 基础纹理
     */
    private generateMipChain;
    /**
     * 计算纹理大小
     * @param texture 纹理
     * @returns 大小（字节）
     */
    private calculateTextureSize;
    /**
     * 应用到地形组件
     * @param component 地形组件
     */
    applyToTerrainComponent(component: TerrainComponent): void;
}
