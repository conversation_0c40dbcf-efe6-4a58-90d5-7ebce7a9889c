/**
 * 更新用户DTO
 */
import { IsString, IsEmail, IsOptional, Min<PERSON>ength, IsBoolean, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../entities/user.entity';

export class UpdateUserDto {
  @ApiProperty({ description: '用户名', required: false })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({ description: '电子邮箱', required: false })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: '密码', required: false })
  @IsString()
  @IsOptional()
  @MinLength(6)
  password?: string;

  @ApiProperty({ description: '显示名称', required: false })
  @IsString()
  @IsOptional()
  displayName?: string;

  @ApiProperty({ description: '是否已验证', required: false })
  @IsBoolean()
  @IsOptional()
  isVerified?: boolean;

  @ApiProperty({ description: '是否访客', required: false })
  @IsBoolean()
  @IsOptional()
  isGuest?: boolean;

  @ApiProperty({ description: '用户角色', required: false, enum: UserRole })
  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole;

  @ApiProperty({ description: '邀请码', required: false })
  @IsString()
  @IsOptional()
  inviteCode?: string;
}
