/**
 * 断点管理器
 * 负责管理视觉脚本断点
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Node } from '../nodes/Node';
import { Graph } from '../graph/Graph';
/**
 * 断点类型
 */
export declare enum BreakpointType {
    /** 普通断点 */
    NORMAL = "normal",
    /** 条件断点 */
    CONDITIONAL = "conditional",
    /** 日志断点 */
    LOG = "log",
    /** 数据断点 */
    DATA = "data",
    /** 异常断点 */
    EXCEPTION = "exception",
    /** 计数断点 */
    COUNT = "count"
}
/**
 * 断点信息
 */
export interface Breakpoint {
    /** 断点ID */
    id: string;
    /** 节点ID */
    nodeId: string;
    /** 图ID */
    graphId: string;
    /** 断点类型 */
    type: BreakpointType;
    /** 条件表达式（对于条件断点） */
    condition?: string;
    /** 日志消息（对于日志断点） */
    logMessage?: string;
    /** 变量名（对于数据断点） */
    variableName?: string;
    /** 变量值条件（对于数据断点） */
    variableCondition?: string;
    /** 异常类型（对于异常断点） */
    exceptionType?: string;
    /** 计数器（对于计数断点） */
    counter?: number;
    /** 计数阈值（对于计数断点） */
    countThreshold?: number;
    /** 标签 */
    tags?: string[];
    /** 描述 */
    description?: string;
    /** 创建时间 */
    createdAt?: number;
    /** 最后命中时间 */
    lastHitAt?: number;
    /** 命中次数 */
    hitCount?: number;
    /** 是否启用 */
    enabled: boolean;
}
/**
 * 断点命中信息
 */
export interface BreakpointHitInfo {
    /** 断点 */
    breakpoint: Breakpoint;
    /** 节点 */
    node: Node;
    /** 图 */
    graph: Graph;
}
/**
 * 断点管理器
 */
export declare class BreakpointManager extends EventEmitter {
    /** 断点映射 */
    private breakpoints;
    /**
     * 创建断点管理器
     */
    constructor();
    /**
     * 添加断点
     * @param nodeId 节点ID
     * @param graphId 图ID
     * @param type 断点类型
     * @param options 断点选项
     * @returns 断点ID
     */
    addBreakpoint(nodeId: string, graphId: string, type?: BreakpointType, options?: {
        condition?: string;
        logMessage?: string;
        variableName?: string;
        variableCondition?: string;
        exceptionType?: string;
        countThreshold?: number;
        tags?: string[];
        description?: string;
        enabled?: boolean;
    }): string;
    /**
     * 移除断点
     * @param id 断点ID
     * @returns 是否成功
     */
    removeBreakpoint(id: string): boolean;
    /**
     * 更新断点
     * @param id 断点ID
     * @param updates 更新内容
     * @returns 是否成功
     */
    updateBreakpoint(id: string, updates: Partial<Omit<Breakpoint, 'id' | 'nodeId' | 'graphId'>>): boolean;
    /**
     * 启用断点
     * @param id 断点ID
     * @returns 是否成功
     */
    enableBreakpoint(id: string): boolean;
    /**
     * 禁用断点
     * @param id 断点ID
     * @returns 是否成功
     */
    disableBreakpoint(id: string): boolean;
    /**
     * 获取断点
     * @param id 断点ID
     * @returns 断点
     */
    getBreakpoint(id: string): Breakpoint | undefined;
    /**
     * 获取节点断点
     * @param nodeId 节点ID
     * @param graphId 图ID
     * @returns 断点
     */
    getNodeBreakpoint(nodeId: string, graphId: string): Breakpoint | undefined;
    /**
     * 获取所有断点
     * @returns 断点列表
     */
    getAllBreakpoints(): Breakpoint[];
    /**
     * 获取图的所有断点
     * @param graphId 图ID
     * @returns 断点列表
     */
    getGraphBreakpoints(graphId: string): Breakpoint[];
    /**
     * 清除所有断点
     */
    clearAllBreakpoints(): void;
    /**
     * 清除图的所有断点
     * @param graphId 图ID
     */
    clearGraphBreakpoints(graphId: string): void;
    /**
     * 检查节点是否有断点
     * @param nodeId 节点ID
     * @param graphId 图ID
     * @returns 是否有断点
     */
    hasBreakpoint(nodeId: string, graphId: string): boolean;
    /**
     * 检查节点是否有启用的断点
     * @param nodeId 节点ID
     * @param graphId 图ID
     * @returns 是否有启用的断点
     */
    hasEnabledBreakpoint(nodeId: string, graphId: string): boolean;
    /**
     * 检查断点是否应该触发
     * @param nodeId 节点ID
     * @param graphId 图ID
     * @param node 节点
     * @param graph 图
     * @param error 错误对象（用于异常断点）
     * @returns 是否应该触发
     */
    shouldBreak(nodeId: string, graphId: string, node: Node, graph: Graph, error?: Error): boolean;
    /**
     * 按标签查找断点
     * @param tag 标签
     * @returns 断点列表
     */
    findBreakpointsByTag(tag: string): Breakpoint[];
    /**
     * 导出断点
     * @returns 断点数据
     */
    exportBreakpoints(): string;
    /**
     * 导入断点
     * @param data 断点数据
     * @returns 是否成功
     */
    importBreakpoints(data: string): boolean;
}
