/**
 * AI情感分析系统
 * 提供情感分析功能
 */
import { System } from '../core/System';
import { EmotionAnalysisResult } from '../avatar/ai/EmotionBasedAnimationGenerator';
/**
 * AI情感分析系统配置
 */
export interface AIEmotionAnalysisSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用本地模型 */
    useLocalModel?: boolean;
    /** 模型路径 */
    modelPath?: string;
}
/**
 * AI情感分析系统
 */
export declare class AIEmotionAnalysisSystem extends System {
    /** 系统名称 */
    static readonly NAME = "AIEmotionAnalysisSystem";
    /** 配置 */
    private config;
    /** 是否已初始化 */
    private initialized;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: AIEmotionAnalysisSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): Promise<void>;
    /**
     * 分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    analyzeEmotion(text: string, options?: {
        detailed?: boolean;
    }): Promise<EmotionAnalysisResult | null>;
    /**
     * 简单的情感分析实现
     * @param text 文本
     * @returns 情感分析结果
     */
    private simpleEmotionAnalysis;
    /**
     * 计算关键词数量
     * @param text 文本
     * @param keywords 关键词列表
     * @returns 关键词数量
     */
    private countKeywords;
    /**
     * 更新系统
     * @param deltaTime 时间间隔
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
