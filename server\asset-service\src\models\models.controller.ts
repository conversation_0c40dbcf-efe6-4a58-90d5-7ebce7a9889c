/**
 * 模型控制器
 */
import { Controller, Get, UseGuards, Request, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ModelsService } from './models.service';
import { Asset } from '../assets/entities/asset.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('模型')
@Controller('models')
export class ModelsController {
  constructor(private readonly modelsService: ModelsService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有模型' })
  @ApiResponse({ status: 200, description: '返回所有模型', type: [Asset] })
  async findAll(
    @Request() req,
    @Query('projectId') projectId?: string,
    @Query('tags') tags?: string,
  ) {
    const tagArray = tags ? tags.split(',') : undefined;
    return this.modelsService.findAll(req.user.id, projectId, tagArray);
  }

  @Get('search')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索模型' })
  @ApiResponse({ status: 200, description: '返回搜索结果', type: [Asset] })
  async search(@Request() req, @Query('query') query: string) {
    return this.modelsService.search(query, req.user.id);
  }
}
