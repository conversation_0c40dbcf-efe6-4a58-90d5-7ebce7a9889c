{"id": "data-visualization", "title": "数据可视化示例", "description": "展示如何使用DL（Digital Learning）引擎进行数据可视化，包括3D柱状图、散点图、热力图和地理数据可视化等。", "category": "advanced", "tags": ["数据可视化", "3D图表", "交互", "热力图", "地理数据"], "previewImage": "/examples/assets/images/previews/data-visualization.jpg", "images": ["/examples/assets/images/previews/data-visualization.jpg", "/examples/assets/images/previews/data-visualization-2.jpg", "/examples/assets/images/previews/data-visualization-3.jpg"], "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-05-15", "updatedAt": "2023-06-20", "popularity": 85, "difficulty": "intermediate", "features": [{"title": "3D柱状图", "description": "将数据以3D柱状图形式展示，支持多维数据比较"}, {"title": "3D散点图", "description": "以3D空间中的点展示多维数据关系"}, {"title": "热力图", "description": "使用颜色渐变展示数据密度和分布"}, {"title": "地理数据可视化", "description": "在3D地图上展示地理相关数据"}, {"title": "时间序列动画", "description": "展示数据随时间变化的动态效果"}, {"title": "交互式数据探索", "description": "支持缩放、旋转、筛选和查询数据"}], "files": [{"name": "index.html", "size": "8.5KB", "updatedAt": "2023-06-20"}, {"name": "README.md", "size": "5.2KB", "updatedAt": "2023-06-20"}, {"name": "scripts/main.js", "size": "12.7KB", "updatedAt": "2023-06-20"}, {"name": "scripts/visualizations.js", "size": "10.8KB", "updatedAt": "2023-06-20"}, {"name": "styles/main.css", "size": "7.8KB", "updatedAt": "2023-06-20"}], "tutorials": [{"title": "创建基本数据可视化", "description": "学习如何创建基本的3D数据可视化"}, {"title": "添加交互功能", "description": "学习如何添加交互功能以增强用户体验"}, {"title": "自定义可视化样式", "description": "学习如何自定义可视化的颜色、大小和形状"}], "requirements": {"engineVersion": "1.0.0+", "editorVersion": "1.0.0+", "dependencies": ["d3.js"]}, "relatedExamples": [{"id": "ui-design", "title": "UI设计最佳实践", "description": "展示2D/3D UI创建和交互设计，包括响应式布局和自定义控件。", "previewImage": "/examples/assets/images/previews/ui-design.jpg"}, {"id": "performance-optimization", "title": "性能优化最佳实践", "description": "展示场景优化技术，如LOD、实例化、合并等，以及性能分析和瓶颈检测。", "previewImage": "/examples/assets/images/previews/performance-optimization.jpg"}], "previewUrl": "/examples/data-visualization/index.html", "favorited": false}