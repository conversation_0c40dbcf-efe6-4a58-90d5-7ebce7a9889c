/**
 * 增强版喷泉组件
 * 提供更真实的喷泉物理模拟和渲染效果
 */
import * as THREE from 'three';
import { FountainComponent, FountainConfig, FountainType } from './FountainComponent';
import type { Entity } from '../../core/Entity';
/**
 * 增强版喷泉类型
 */
export declare enum EnhancedFountainType {
    /** 标准喷泉 */
    STANDARD = "standard",
    /** 高喷泉 */
    HIGH = "high",
    /** 宽喷泉 */
    WIDE = "wide",
    /** 多喷头喷泉 */
    MULTI_JET = "multi_jet",
    /** 舞蹈喷泉 */
    DANCING = "dancing",
    /** 音乐喷泉 */
    MUSICAL = "musical",
    /** 脉冲喷泉 */
    PULSE = "pulse",
    /** 交替喷泉 */
    ALTERNATING = "alternating",
    /** 序列喷泉 */
    SEQUENCE = "sequence",
    /** 随机喷泉 */
    RANDOM = "random",
    /** 螺旋喷泉 */
    SPIRAL = "spiral",
    /** 圆锥喷泉 */
    CONE = "cone",
    /** 花朵喷泉 */
    FLOWER = "flower",
    /** 彩虹喷泉 */
    RAINBOW = "rainbow",
    /** 交互式喷泉 */
    INTERACTIVE = "interactive"
}
/**
 * 喷泉喷射形状
 */
export declare enum FountainJetShape {
    /** 圆柱形 */
    CYLINDER = "cylinder",
    /** 圆锥形 */
    CONE = "cone",
    /** 球形 */
    SPHERE = "sphere",
    /** 扁平 */
    FLAT = "flat",
    /** 螺旋 */
    SPIRAL = "spiral",
    /** 花朵 */
    FLOWER = "flower",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 增强版喷泉配置
 */
export interface EnhancedFountainConfig extends FountainConfig {
    /** 喷泉喷射形状 */
    jetShape?: FountainJetShape;
    /** 喷泉喷射高度变化速度 */
    jetHeightChangeSpeed?: number;
    /** 喷泉喷射宽度变化速度 */
    jetWidthChangeSpeed?: number;
    /** 喷泉喷射角度变化速度 */
    jetAngleChangeSpeed?: number;
    /** 喷泉喷射密度 */
    jetDensity?: number;
    /** 喷泉喷射粒子大小 */
    jetParticleSize?: number;
    /** 喷泉喷射粒子大小变化范围 */
    jetParticleSizeVariation?: number;
    /** 喷泉喷射粒子颜色 */
    jetParticleColor?: THREE.Color;
    /** 喷泉喷射粒子颜色变化范围 */
    jetParticleColorVariation?: number;
    /** 喷泉喷射粒子寿命 */
    jetParticleLifetime?: number;
    /** 喷泉喷射粒子寿命变化范围 */
    jetParticleLifetimeVariation?: number;
    /** 喷泉喷射粒子速度 */
    jetParticleSpeed?: number;
    /** 喷泉喷射粒子速度变化范围 */
    jetParticleSpeedVariation?: number;
    /** 喷泉喷射粒子重力系数 */
    jetParticleGravityFactor?: number;
    /** 喷泉喷射粒子阻力系数 */
    jetParticleDragFactor?: number;
    /** 喷泉喷射粒子旋转速度 */
    jetParticleRotationSpeed?: number;
    /** 喷泉喷射粒子旋转速度变化范围 */
    jetParticleRotationSpeedVariation?: number;
    /** 是否启用喷泉喷射粒子轨迹 */
    enableJetParticleTrails?: boolean;
    /** 喷泉喷射粒子轨迹长度 */
    jetParticleTrailLength?: number;
    /** 是否启用喷泉喷射粒子光散射 */
    enableJetParticleScattering?: boolean;
    /** 喷泉喷射粒子光散射强度 */
    jetParticleScatteringStrength?: number;
    /** 是否启用喷泉喷射粒子反射 */
    enableJetParticleReflections?: boolean;
    /** 喷泉喷射粒子反射强度 */
    jetParticleReflectionStrength?: number;
    /** 是否启用喷泉喷射粒子折射 */
    enableJetParticleRefractions?: boolean;
    /** 喷泉喷射粒子折射强度 */
    jetParticleRefractionStrength?: number;
    /** 是否启用喷泉喷射粒子声音 */
    enableJetParticleSounds?: boolean;
    /** 喷泉喷射粒子声音音量 */
    jetParticleSoundVolume?: number;
    /** 是否启用喷泉喷射粒子性能优化 */
    enableJetParticleOptimization?: boolean;
    /** 喷泉喷射粒子LOD距离 */
    jetParticleLODDistances?: number[];
    /** 是否启用GPU加速 */
    enableGPUAcceleration?: boolean;
    /** 是否启用彩色照明 */
    enableColoredLighting?: boolean;
    /** 彩色照明颜色 */
    coloredLightingColors?: THREE.Color[];
    /** 彩色照明变化速度 */
    coloredLightingChangeSpeed?: number;
    /** 是否启用音乐同步 */
    enableMusicSync?: boolean;
    /** 音乐同步灵敏度 */
    musicSyncSensitivity?: number;
    /** 音乐同步频率范围 */
    musicSyncFrequencyRange?: [number, number];
    /** 是否启用交互式控制 */
    enableInteractiveControl?: boolean;
    /** 交互式控制灵敏度 */
    interactiveControlSensitivity?: number;
}
/**
 * 增强版喷泉组件
 */
export declare class EnhancedFountainComponent extends FountainComponent {
    /** 喷泉喷射形状 */
    private jetShape;
    /** 喷泉喷射高度变化速度 */
    private jetHeightChangeSpeed;
    /** 喷泉喷射宽度变化速度 */
    private jetWidthChangeSpeed;
    /** 喷泉喷射角度变化速度 */
    private jetAngleChangeSpeed;
    /** 喷泉喷射密度 */
    private jetDensity;
    /** 喷泉喷射粒子大小 */
    private jetParticleSize;
    /** 喷泉喷射粒子大小变化范围 */
    private jetParticleSizeVariation;
    /** 喷泉喷射粒子颜色 */
    private jetParticleColor;
    /** 喷泉喷射粒子颜色变化范围 */
    private jetParticleColorVariation;
    /** 喷泉喷射粒子寿命 */
    private jetParticleLifetime;
    /** 喷泉喷射粒子寿命变化范围 */
    private jetParticleLifetimeVariation;
    /** 喷泉喷射粒子速度 */
    private jetParticleSpeed;
    /** 喷泉喷射粒子速度变化范围 */
    private jetParticleSpeedVariation;
    /** 喷泉喷射粒子重力系数 */
    private jetParticleGravityFactor;
    /** 喷泉喷射粒子阻力系数 */
    private jetParticleDragFactor;
    /** 喷泉喷射粒子旋转速度 */
    private jetParticleRotationSpeed;
    /** 喷泉喷射粒子旋转速度变化范围 */
    private jetParticleRotationSpeedVariation;
    /** 是否启用喷泉喷射粒子轨迹 */
    private enableJetParticleTrails;
    /** 喷泉喷射粒子轨迹长度 */
    private jetParticleTrailLength;
    /** 是否启用喷泉喷射粒子光散射 */
    private enableJetParticleScattering;
    /** 喷泉喷射粒子光散射强度 */
    private jetParticleScatteringStrength;
    /** 是否启用喷泉喷射粒子反射 */
    private enableJetParticleReflections;
    /** 喷泉喷射粒子反射强度 */
    private jetParticleReflectionStrength;
    /** 是否启用喷泉喷射粒子折射 */
    private enableJetParticleRefractions;
    /** 喷泉喷射粒子折射强度 */
    private jetParticleRefractionStrength;
    /** 是否启用喷泉喷射粒子声音 */
    private enableJetParticleSounds;
    /** 喷泉喷射粒子声音音量 */
    private jetParticleSoundVolume;
    /** 是否启用喷泉喷射粒子性能优化 */
    private enableJetParticleOptimization;
    /** 喷泉喷射粒子LOD距离 */
    private jetParticleLODDistances;
    /** 是否启用GPU加速 */
    private enableGPUAcceleration;
    /** 是否启用彩色照明 */
    private enableColoredLighting;
    /** 彩色照明颜色 */
    private coloredLightingColors;
    /** 彩色照明变化速度 */
    private coloredLightingChangeSpeed;
    /** 是否启用音乐同步 */
    private enableMusicSync;
    /** 音乐同步灵敏度 */
    private musicSyncSensitivity;
    /** 音乐同步频率范围 */
    private musicSyncFrequencyRange;
    /** 是否启用交互式控制 */
    private enableInteractiveControl;
    /** 交互式控制灵敏度 */
    private interactiveControlSensitivity;
    /** 性能监控器 */
    private performanceMonitor;
    /** 喷泉粒子列表 */
    private particles;
    /** 喷泉喷射点列表 */
    private jets;
    /** 水体实例化渲染器 */
    private waterInstancedRenderer;
    /** 水下粒子系统 */
    private underwaterParticleSystem;
    /** 音频系统 */
    private audioSystem;
    /** 音频源ID */
    private audioSourceId;
    /** 彩色照明灯光列表 */
    private coloredLights;
    /** 彩色照明当前颜色索引 */
    private coloredLightingCurrentColorIndex;
    /** 彩色照明计时器 */
    private coloredLightingTimer;
    /** 粒子生成计时器 */
    private particleGenerationTimer;
    /** 粒子生成间隔 */
    private particleGenerationInterval;
    /** 粒子最大数量 */
    private maxParticles;
    /** 当前粒子数量 */
    private currentParticleCount;
    /** 喷泉高度变化计时器 */
    private heightChangeTimer;
    /** 喷泉宽度变化计时器 */
    private widthChangeTimer;
    /** 喷泉角度变化计时器 */
    private angleChangeTimer;
    /** 喷泉高度目标值 */
    private targetHeight;
    /** 喷泉宽度目标值 */
    private targetWidth;
    /** 喷泉角度目标值 */
    private targetAngle;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: EnhancedFountainConfig);
    /**
     * 应用增强配置
     * @param config 配置
     */
    private applyEnhancedConfig;
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 获取喷泉类型
     * @returns 喷泉类型
     */
    private getFountainType;
    /**
     * 初始化喷泉喷射点
     */
    private initializeJets;
    /**
     * 创建标准喷射点
     * @param position 位置
     */
    private createStandardJet;
    /**
     * 创建高喷射点
     * @param position 位置
     */
    private createHighJet;
    /**
     * 创建宽喷射点
     * @param position 位置
     */
    private createWideJet;
    /**
     * 创建多喷头喷射点
     * @param position 位置
     */
    private createMultiJet;
    /**
     * 创建舞蹈喷射点
     * @param position 位置
     */
    private createDancingJet;
    /**
     * 创建音乐喷射点
     * @param position 位置
     */
    private createMusicalJet;
    /**
     * 创建脉冲喷射点
     * @param position 位置
     */
    private createPulseJet;
    /**
     * 创建交替喷射点
     * @param position 位置
     */
    private createAlternatingJet;
    /**
     * 创建序列喷射点
     * @param position 位置
     */
    private createSequenceJet;
    /**
     * 创建随机喷射点
     * @param position 位置
     */
    private createRandomJet;
    /**
     * 创建交互式喷射点
     * @param position 位置
     */
    private createInteractiveJet;
    /**
     * 初始化彩色照明
     */
    private initializeColoredLighting;
    /**
     * 初始化增强音频
     */
    private initializeEnhancedAudio;
    /**
     * 获取随机颜色
     * @returns 随机颜色
     */
    private getRandomColor;
    /**
     * 根据频率获取颜色
     * @param normalizedFrequency 归一化频率（0-1）
     * @returns 颜色
     */
    private getColorFromFrequency;
    /**
     * 根据索引获取颜色
     * @param index 索引
     * @param total 总数
     * @returns 颜色
     */
    private getColorFromIndex;
    /**
     * 获取喷射高度
     * @returns 喷射高度
     */
    private getJetHeight;
    /**
     * 获取喷射宽度
     * @returns 喷射宽度
     */
    private getJetWidth;
    /**
     * 获取喷射角度
     * @returns 喷射角度
     */
    private getJetAngle;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新增强喷射点
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateEnhancedJets;
    /**
     * 更新目标值
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateTargetValues;
    /**
     * 更新多喷头喷泉
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateMultiJet;
    /**
     * 更新舞蹈喷泉
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateDancingJet;
    /**
     * 更新音乐喷泉
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateMusicalJet;
    /**
     * 更新脉冲喷泉
     * @param deltaTime 帧间隔时间（秒）
     */
    private updatePulseJet;
    /**
     * 更新交替喷泉
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateAlternatingJet;
    /**
     * 更新序列喷泉
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateSequenceJet;
    /**
     * 更新随机喷泉
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateRandomJet;
    /**
     * 更新交互式喷泉
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateInteractiveJet;
    /**
     * 更新彩色照明
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateColoredLighting;
    /**
     * 更新粒子生成
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateParticleGeneration;
    /**
     * 生成粒子
     */
    private generateParticles;
    /**
     * 计算粒子数量
     * @param jet 喷射点
     * @returns 粒子数量
     */
    private calculateParticleCount;
    /**
     * 创建粒子
     * @param jet 喷射点
     */
    private createParticle;
    /**
     * 更新粒子
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateParticles;
    /**
     * 更新粒子轨迹
     * @param instance 粒子实例
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateParticleTrail;
    /**
     * 移除粒子
     * @param index 粒子索引
     */
    private removeParticle;
    /**
     * 平滑过渡
     * @param current 当前值
     * @param target 目标值
     * @param speed 速度
     * @returns 新值
     */
    private smoothStep;
    /**
     * 清理彩色灯光
     */
    private clearColoredLights;
    /**
     * 设置喷泉类型
     * @param type 喷泉类型
     */
    setFountainType(type: FountainType): void;
    /**
     * 设置喷射形状
     * @param shape 喷射形状
     */
    setJetShape(shape: FountainJetShape): void;
    /**
     * 设置喷射高度变化速度
     * @param speed 变化速度
     */
    setJetHeightChangeSpeed(speed: number): void;
    /**
     * 设置喷射宽度变化速度
     * @param speed 变化速度
     */
    setJetWidthChangeSpeed(speed: number): void;
    /**
     * 设置喷射角度变化速度
     * @param speed 变化速度
     */
    setJetAngleChangeSpeed(speed: number): void;
    /**
     * 设置喷射密度
     * @param density 密度
     */
    setJetDensity(density: number): void;
    /**
     * 设置是否启用彩色照明
     * @param enable 是否启用
     */
    setEnableColoredLighting(enable: boolean): void;
    /**
     * 设置彩色照明颜色
     * @param colors 颜色数组
     */
    setColoredLightingColors(colors: THREE.Color[]): void;
    /**
     * 设置彩色照明变化速度
     * @param speed 变化速度
     */
    setColoredLightingChangeSpeed(speed: number): void;
    /**
     * 设置是否启用音乐同步
     * @param enable 是否启用
     */
    setEnableMusicSync(enable: boolean): void;
    /**
     * 设置音乐同步灵敏度
     * @param sensitivity 灵敏度
     */
    setMusicSyncSensitivity(sensitivity: number): void;
    /**
     * 设置是否启用交互式控制
     * @param enable 是否启用
     */
    setEnableInteractiveControl(enable: boolean): void;
    /**
     * 设置交互式控制灵敏度
     * @param sensitivity 灵敏度
     */
    setInteractiveControlSensitivity(sensitivity: number): void;
    /**
     * 销毁组件
     */
    destroy(): void;
    /**
     * 清理粒子
     */
    private clearParticles;
    /**
     * 清理音频
     */
    private clearAudio;
}
