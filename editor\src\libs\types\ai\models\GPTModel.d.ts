/**
 * GPT模型
 * 用于文本生成
 */
import { AIModelType } from '../AIModelType';
import { AIModelConfig } from '../AIModelConfig';
import { IAIModel, TextGenerationOptions } from './IAIModel';
/**
 * GPT模型
 */
export declare class GPTModel implements IAIModel {
    /** 模型类型 */
    private readonly type;
    /** 模型配置 */
    private config;
    /** 全局配置 */
    private globalConfig;
    /** 是否已初始化 */
    private initialized;
    /** 事件发射器 */
    private eventEmitter;
    /** 模型实例 */
    private model;
    /** 模型加载进度 */
    private loadProgress;
    /** 默认文本生成选项 */
    private static readonly DEFAULT_TEXT_OPTIONS;
    /**
     * 构造函数
     * @param config 模型配置
     * @param globalConfig 全局配置
     */
    constructor(config?: AIModelConfig, globalConfig?: AIModelConfig);
    /**
     * 获取模型类型
     * @returns 模型类型
     */
    getType(): AIModelType;
    /**
     * 获取模型配置
     * @returns 模型配置
     */
    getConfig(): AIModelConfig;
    /**
     * 初始化模型
     * @returns 是否成功
     */
    initialize(): Promise<boolean>;
    /**
     * 生成文本
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 生成的文本
     */
    generateText(prompt: string, options?: TextGenerationOptions): Promise<string>;
    /**
     * 流式生成文本
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 生成的文本
     */
    private generateTextStream;
    /**
     * 模拟生成文本
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 生成的文本
     */
    private mockGenerate;
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    on(event: string, listener: (...args: any[]) => void): void;
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    off(event: string, listener: (...args: any[]) => void): void;
    /**
     * 释放资源
     */
    dispose(): void;
}
