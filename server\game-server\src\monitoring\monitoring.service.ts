import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import * as os from 'os';
import { InstanceService } from '../instance/instance.service';
import { WebRTCService } from '../webrtc/webrtc.service';
import { AgonesService } from '../agones/agones.service';

/**
 * 监控指标类型
 */
export interface MonitoringMetrics {
  timestamp: number;
  cpuUsage: number;
  memoryUsage: number;
  totalMemory: number;
  freeMemory: number;
  uptime: number;
  loadAverage: number[];
  networkConnections: number;
  webrtcStats: {
    workers: number;
    routers: number;
    transports: number;
    producers: number;
    consumers: number;
    dataProducers: number;
    dataConsumers: number;
  };
  instanceStats: {
    total: number;
    active: number;
    users: number;
  };
}

/**
 * 告警类型
 */
export interface Alert {
  id: string;
  type: 'cpu' | 'memory' | 'network' | 'instance' | 'webrtc' | 'system';
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  value: number;
  threshold: number;
  resolved: boolean;
  resolvedAt?: number;
}

/**
 * 监控服务
 * 负责收集和分析游戏服务器的性能指标
 */
@Injectable()
export class MonitoringService implements OnModuleInit {
  private readonly logger = new Logger(MonitoringService.name);
  private metrics: MonitoringMetrics[] = [];
  private alerts: Alert[] = [];
  private readonly maxMetricsHistory: number;
  private readonly maxAlertsHistory: number;
  private readonly cpuThreshold: number;
  private readonly memoryThreshold: number;
  private readonly networkConnectionsThreshold: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly instanceService: InstanceService,
    private readonly webrtcService: WebRTCService,
    private readonly agonesService: AgonesService,
  ) {
    this.maxMetricsHistory = this.configService.get<number>('MONITORING_MAX_METRICS_HISTORY', 1440); // 默认保存24小时的数据（每分钟一次）
    this.maxAlertsHistory = this.configService.get<number>('MONITORING_MAX_ALERTS_HISTORY', 1000);
    this.cpuThreshold = this.configService.get<number>('MONITORING_CPU_THRESHOLD', 0.8); // 80%
    this.memoryThreshold = this.configService.get<number>('MONITORING_MEMORY_THRESHOLD', 0.8); // 80%
    this.networkConnectionsThreshold = this.configService.get<number>('MONITORING_NETWORK_CONNECTIONS_THRESHOLD', 1000);
  }

  /**
   * 模块初始化
   */
  async onModuleInit() {
    // 初始化时收集一次指标
    await this.collectMetrics();
    
    // 触发监控服务启动事件
    this.eventEmitter.emit('monitoring.started');
  }

  /**
   * 定时收集指标
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async collectMetrics() {
    try {
      // 收集系统指标
      const cpuUsage = await this.getCpuUsage();
      const { totalMemory, freeMemory, memoryUsage } = this.getMemoryUsage();
      const uptime = os.uptime();
      const loadAverage = os.loadavg();
      const networkConnections = await this.getNetworkConnections();
      
      // 收集WebRTC指标
      const webrtcStats = this.getWebRTCStats();
      
      // 收集实例指标
      const instanceStats = this.getInstanceStats();
      
      // 创建指标对象
      const metrics: MonitoringMetrics = {
        timestamp: Date.now(),
        cpuUsage,
        memoryUsage,
        totalMemory,
        freeMemory,
        uptime,
        loadAverage,
        networkConnections,
        webrtcStats,
        instanceStats,
      };
      
      // 添加到历史数据
      this.metrics.push(metrics);
      
      // 如果历史数据超过最大值，删除最旧的数据
      if (this.metrics.length > this.maxMetricsHistory) {
        this.metrics.splice(0, this.metrics.length - this.maxMetricsHistory);
      }
      
      // 检查告警
      this.checkAlerts(metrics);
      
      // 触发指标更新事件
      this.eventEmitter.emit('monitoring.metrics', metrics);
      
      // 更新实例指标
      this.updateInstanceMetrics(metrics);
    } catch (error) {
      this.logger.error(`收集指标失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取CPU使用率
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startMeasure = this.getCpuInfo();
      
      // 等待100毫秒再次测量
      setTimeout(() => {
        const endMeasure = this.getCpuInfo();
        
        let idleDifference = 0;
        let totalDifference = 0;
        
        // 计算差值
        for (let i = 0; i < startMeasure.length; i++) {
          const startCpu = startMeasure[i];
          const endCpu = endMeasure[i];
          
          idleDifference += endCpu.idle - startCpu.idle;
          totalDifference += (endCpu.user + endCpu.nice + endCpu.sys + endCpu.idle + endCpu.irq) - 
                            (startCpu.user + startCpu.nice + startCpu.sys + startCpu.idle + startCpu.irq);
        }
        
        // 计算CPU使用率
        const cpuUsage = 1 - (idleDifference / totalDifference);
        resolve(cpuUsage);
      }, 100);
    });
  }

  /**
   * 获取CPU信息
   */
  private getCpuInfo(): { user: number; nice: number; sys: number; idle: number; irq: number }[] {
    return os.cpus().map(cpu => ({
      user: cpu.times.user,
      nice: cpu.times.nice,
      sys: cpu.times.sys,
      idle: cpu.times.idle,
      irq: cpu.times.irq,
    }));
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): { totalMemory: number; freeMemory: number; memoryUsage: number } {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const memoryUsage = 1 - (freeMemory / totalMemory);
    
    return { totalMemory, freeMemory, memoryUsage };
  }

  /**
   * 获取网络连接数
   */
  private async getNetworkConnections(): Promise<number> {
    // 这里简化实现，实际应该使用系统命令获取
    return 0;
  }

  /**
   * 获取WebRTC统计信息
   */
  private getWebRTCStats(): MonitoringMetrics['webrtcStats'] {
    const workers = this.webrtcService.getWorkers().length;
    const routers = this.webrtcService.getRouters().length;
    const transports = this.webrtcService.getAllTransports().size;
    
    // 这里简化实现，实际应该从WebRTC服务获取
    const producers = 0;
    const consumers = 0;
    const dataProducers = 0;
    const dataConsumers = 0;
    
    return {
      workers,
      routers,
      transports,
      producers,
      consumers,
      dataProducers,
      dataConsumers,
    };
  }

  /**
   * 获取实例统计信息
   */
  private getInstanceStats(): MonitoringMetrics['instanceStats'] {
    const instances = this.instanceService.getAllInstances();
    const activeInstances = this.instanceService.getAvailableInstances();
    
    // 计算总用户数
    let totalUsers = 0;
    for (const instance of instances) {
      totalUsers += instance.currentUsers;
    }
    
    return {
      total: instances.length,
      active: activeInstances.length,
      users: totalUsers,
    };
  }

  /**
   * 检查告警
   */
  private checkAlerts(metrics: MonitoringMetrics): void {
    // 检查CPU使用率
    if (metrics.cpuUsage > this.cpuThreshold) {
      this.createAlert('cpu', 'warning', `CPU使用率过高: ${(metrics.cpuUsage * 100).toFixed(2)}%`, metrics.cpuUsage, this.cpuThreshold);
    }
    
    // 检查内存使用率
    if (metrics.memoryUsage > this.memoryThreshold) {
      this.createAlert('memory', 'warning', `内存使用率过高: ${(metrics.memoryUsage * 100).toFixed(2)}%`, metrics.memoryUsage, this.memoryThreshold);
    }
    
    // 检查网络连接数
    if (metrics.networkConnections > this.networkConnectionsThreshold) {
      this.createAlert('network', 'warning', `网络连接数过高: ${metrics.networkConnections}`, metrics.networkConnections, this.networkConnectionsThreshold);
    }
    
    // 解决已解决的告警
    this.resolveAlerts(metrics);
  }

  /**
   * 创建告警
   */
  private createAlert(
    type: Alert['type'],
    severity: Alert['severity'],
    message: string,
    value: number,
    threshold: number,
  ): void {
    // 检查是否已存在相同类型的未解决告警
    const existingAlert = this.alerts.find(alert => alert.type === type && !alert.resolved);
    
    if (existingAlert) {
      // 更新现有告警
      existingAlert.message = message;
      existingAlert.value = value;
      existingAlert.timestamp = Date.now();
      
      // 如果严重程度提高，更新严重程度
      if (this.getSeverityLevel(severity) > this.getSeverityLevel(existingAlert.severity)) {
        existingAlert.severity = severity;
      }
    } else {
      // 创建新告警
      const alert: Alert = {
        id: `${type}-${Date.now()}`,
        type,
        severity,
        message,
        timestamp: Date.now(),
        value,
        threshold,
        resolved: false,
      };
      
      this.alerts.push(alert);
      
      // 如果告警数量超过最大值，删除最旧的已解决告警
      if (this.alerts.length > this.maxAlertsHistory) {
        const resolvedAlerts = this.alerts.filter(a => a.resolved);
        if (resolvedAlerts.length > 0) {
          // 按解决时间排序，删除最旧的
          resolvedAlerts.sort((a, b) => (a.resolvedAt || 0) - (b.resolvedAt || 0));
          const alertToRemove = resolvedAlerts[0];
          const index = this.alerts.indexOf(alertToRemove);
          if (index !== -1) {
            this.alerts.splice(index, 1);
          }
        } else {
          // 如果没有已解决的告警，删除最旧的未解决告警
          this.alerts.shift();
        }
      }
      
      // 触发告警事件
      this.eventEmitter.emit('monitoring.alert', alert);
    }
  }

  /**
   * 解决告警
   */
  private resolveAlerts(metrics: MonitoringMetrics): void {
    // 获取未解决的告警
    const unresolvedAlerts = this.alerts.filter(alert => !alert.resolved);
    
    for (const alert of unresolvedAlerts) {
      let resolved = false;
      
      switch (alert.type) {
        case 'cpu':
          resolved = metrics.cpuUsage < this.cpuThreshold * 0.9; // 低于阈值的90%
          break;
        case 'memory':
          resolved = metrics.memoryUsage < this.memoryThreshold * 0.9; // 低于阈值的90%
          break;
        case 'network':
          resolved = metrics.networkConnections < this.networkConnectionsThreshold * 0.9; // 低于阈值的90%
          break;
      }
      
      if (resolved) {
        alert.resolved = true;
        alert.resolvedAt = Date.now();
        
        // 触发告警解决事件
        this.eventEmitter.emit('monitoring.alert.resolved', alert);
      }
    }
  }

  /**
   * 获取严重程度级别
   */
  private getSeverityLevel(severity: Alert['severity']): number {
    switch (severity) {
      case 'info':
        return 1;
      case 'warning':
        return 2;
      case 'error':
        return 3;
      case 'critical':
        return 4;
      default:
        return 0;
    }
  }

  /**
   * 更新实例指标
   */
  private updateInstanceMetrics(metrics: MonitoringMetrics): void {
    // 获取所有实例
    const instances = this.instanceService.getAllInstances();
    
    // 为每个实例分配指标
    for (const instance of instances) {
      // 简单地将系统指标平均分配给每个实例
      // 实际应该根据实例的实际资源使用情况进行分配
      const instanceMetrics = {
        cpuUsage: metrics.cpuUsage / instances.length,
        memoryUsage: metrics.memoryUsage / instances.length,
        networkUsage: metrics.networkConnections / instances.length,
      };
      
      // 更新实例指标
      this.instanceService.updateInstanceMetrics(instance.id, instanceMetrics);
    }
  }

  /**
   * 获取最新指标
   */
  getLatestMetrics(): MonitoringMetrics | null {
    if (this.metrics.length === 0) {
      return null;
    }
    
    return this.metrics[this.metrics.length - 1];
  }

  /**
   * 获取历史指标
   */
  getMetricsHistory(limit: number = 60): MonitoringMetrics[] {
    if (limit <= 0 || this.metrics.length === 0) {
      return [];
    }
    
    return this.metrics.slice(-Math.min(limit, this.metrics.length));
  }

  /**
   * 获取活跃告警
   */
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * 获取所有告警
   */
  getAllAlerts(limit: number = 100): Alert[] {
    if (limit <= 0 || this.alerts.length === 0) {
      return [];
    }
    
    // 按时间降序排序
    const sortedAlerts = [...this.alerts].sort((a, b) => b.timestamp - a.timestamp);
    
    return sortedAlerts.slice(0, Math.min(limit, sortedAlerts.length));
  }
}
