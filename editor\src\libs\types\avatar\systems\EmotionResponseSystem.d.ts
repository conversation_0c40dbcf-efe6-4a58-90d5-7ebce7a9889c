/**
 * 情感响应系统
 * 用于处理角色对环境和事件的情感反应
 */
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { FacialExpressionType } from '../components/FacialAnimationComponent';
import { EmotionBlendController } from '../controllers/EmotionBlendController';
import { Vector3 } from 'three';
/**
 * 情感响应系统配置
 */
export interface EmotionResponseSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否自动响应事件 */
    autoRespondToEvents?: boolean;
    /** 响应范围 */
    responseRange?: number;
    /** 最小响应强度 */
    minResponseIntensity?: number;
    /** 最大响应强度 */
    maxResponseIntensity?: number;
    /** 情感衰减速率 */
    emotionDecayRate?: number;
    /** 是否启用情感混合 */
    enableEmotionBlending?: boolean;
    /** 是否启用情感记忆 */
    enableEmotionMemory?: boolean;
    /** 情感记忆持续时间（秒） */
    emotionMemoryDuration?: number;
    /** 是否启用情感传染 */
    enableEmotionContagion?: boolean;
    /** 情感传染范围 */
    emotionContagionRange?: number;
    /** 情感传染强度 */
    emotionContagionStrength?: number;
}
/**
 * 情感事件类型
 */
export declare enum EmotionEventType {
    /** 惊吓 */
    STARTLE = "startle",
    /** 惊喜 */
    SURPRISE = "surprise",
    /** 威胁 */
    THREAT = "threat",
    /** 喜悦 */
    JOY = "joy",
    /** 悲伤 */
    SADNESS = "sadness",
    /** 愤怒 */
    ANGER = "anger",
    /** 恐惧 */
    FEAR = "fear",
    /** 厌恶 */
    DISGUST = "disgust",
    /** 中性 */
    NEUTRAL = "neutral",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 情感事件数据
 */
export interface EmotionEventData {
    /** 事件类型 */
    type: EmotionEventType;
    /** 事件源 */
    source?: Entity;
    /** 事件位置 */
    position?: Vector3;
    /** 事件强度 */
    intensity?: number;
    /** 事件范围 */
    range?: number;
    /** 事件持续时间 */
    duration?: number;
    /** 事件描述 */
    description?: string;
    /** 自定义数据 */
    customData?: any;
}
/**
 * 情感响应数据
 */
export interface EmotionResponseData {
    /** 响应实体 */
    entity: Entity;
    /** 事件类型 */
    eventType: EmotionEventType;
    /** 响应强度 */
    intensity: number;
    /** 响应持续时间 */
    duration: number;
    /** 响应表情 */
    expression: FacialExpressionType;
    /** 响应描述 */
    description: string;
    /** 响应时间戳 */
    timestamp: number;
}
/**
 * 情感记忆
 */
export interface EmotionMemory {
    /** 事件类型 */
    eventType: EmotionEventType;
    /** 事件强度 */
    intensity: number;
    /** 事件时间戳 */
    timestamp: number;
    /** 事件源 */
    source?: Entity;
    /** 事件描述 */
    description?: string;
}
/**
 * 情感响应系统
 */
export declare class EmotionResponseSystem extends System {
    /** 系统名称 */
    static readonly systemName: string;
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 面部动画系统 */
    private facialAnimationSystem;
    /** AI动画合成系统 */
    private aiAnimationSystem;
    /** 情感混合控制器映射 */
    private emotionBlendControllers;
    /** 情感记忆映射 */
    private emotionMemories;
    /** 活跃的情感响应 */
    private activeResponses;
    /** 情感事件队列 */
    private emotionEventQueue;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: EmotionResponseSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 触发情感事件
     * @param eventData 事件数据
     */
    triggerEmotionEvent(eventData: EmotionEventData): void;
    /**
     * 处理情感事件队列
     */
    private processEmotionEventQueue;
    /**
     * 处理情感事件
     * @param eventData 事件数据
     */
    private processEmotionEvent;
    /**
     * 获取受影响的实体
     * @param eventData 事件数据
     * @returns 受影响的实体数组
     */
    private getAffectedEntities;
    /**
     * 获取实体位置
     * @param entity 实体
     * @returns 实体位置
     */
    private getEntityPosition;
    /**
     * 计算响应强度
     * @param entity 实体
     * @param eventData 事件数据
     * @returns 响应强度
     */
    private calculateResponseIntensity;
    /**
     * 应用情感记忆效果
     * @param entity 实体
     * @param eventData 事件数据
     * @param baseIntensity 基础强度
     * @returns 调整后的强度
     */
    private applyEmotionMemoryEffect;
    /**
     * 生成情感响应
     * @param entity 实体
     * @param eventData 事件数据
     * @param intensity 响应强度
     */
    private generateEmotionResponse;
    /**
     * 映射事件类型到表情类型
     * @param eventType 事件类型
     * @returns 表情类型
     */
    private mapEventTypeToExpression;
    /**
     * 计算响应持续时间
     * @param eventType 事件类型
     * @param intensity 响应强度
     * @returns 响应持续时间（秒）
     */
    private calculateResponseDuration;
    /**
     * 应用情感表情
     * @param entity 实体
     * @param responseData 响应数据
     */
    private applyEmotionExpression;
    /**
     * 生成情感提示
     * @param responseData 响应数据
     * @returns 情感提示
     */
    private generateEmotionPrompt;
    /**
     * 更新活跃的情感响应
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateActiveResponses;
    /**
     * 更新情感混合控制器
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateEmotionBlendControllers;
    /**
     * 更新情感记忆
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateEmotionMemories;
    /**
     * 添加情感记忆
     * @param entity 实体
     * @param memory 情感记忆
     */
    private addEmotionMemory;
    /**
     * 处理情感传染
     * @param deltaTime 帧间隔时间（秒）
     */
    private processEmotionContagion;
    /**
     * 获取情感混合控制器
     * @param entity 实体
     * @returns 情感混合控制器
     */
    getEmotionBlendController(entity: Entity): EmotionBlendController | null;
    /**
     * 获取活跃的情感响应
     * @param entity 实体
     * @returns 情感响应数据
     */
    getActiveResponse(entity: Entity): EmotionResponseData | null;
    /**
     * 获取情感记忆
     * @param entity 实体
     * @returns 情感记忆数组
     */
    getEmotionMemories(entity: Entity): EmotionMemory[];
    /**
     * 清除情感记忆
     * @param entity 实体
     */
    clearEmotionMemories(entity: Entity): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    addEventListener(event: string, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    removeEventListener(event: string, listener: (...args: any[]) => void): void;
}
