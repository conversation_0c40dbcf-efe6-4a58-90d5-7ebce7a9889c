import { Controller, Get, Query, Param, Post, Body, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { MonitoringService, MonitoringMetrics, Alert } from './monitoring.service';

@Controller('monitoring')
export class MonitoringController {
  private readonly logger = new Logger(MonitoringController.name);

  constructor(private readonly monitoringService: MonitoringService) {}

  /**
   * 获取最新指标
   */
  @Get('metrics/latest')
  getLatestMetrics(): MonitoringMetrics | { message: string } {
    const metrics = this.monitoringService.getLatestMetrics();
    
    if (!metrics) {
      return { message: '没有可用的指标数据' };
    }
    
    return metrics;
  }

  /**
   * 获取历史指标
   */
  @Get('metrics/history')
  getMetricsHistory(@Query('limit') limit: string): MonitoringMetrics[] {
    const limitNumber = limit ? parseInt(limit, 10) : 60;
    return this.monitoringService.getMetricsHistory(limitNumber);
  }

  /**
   * 获取活跃告警
   */
  @Get('alerts/active')
  getActiveAlerts(): Alert[] {
    return this.monitoringService.getActiveAlerts();
  }

  /**
   * 获取所有告警
   */
  @Get('alerts/all')
  getAllAlerts(@Query('limit') limit: string): Alert[] {
    const limitNumber = limit ? parseInt(limit, 10) : 100;
    return this.monitoringService.getAllAlerts(limitNumber);
  }

  /**
   * 获取特定告警
   */
  @Get('alerts/:id')
  getAlert(@Param('id') id: string): Alert | { message: string } {
    const alerts = this.monitoringService.getAllAlerts(1000);
    const alert = alerts.find(a => a.id === id);
    
    if (!alert) {
      throw new HttpException(`告警 ${id} 不存在`, HttpStatus.NOT_FOUND);
    }
    
    return alert;
  }

  /**
   * 手动触发指标收集
   */
  @Post('collect')
  async collectMetrics(): Promise<{ success: boolean; timestamp: number }> {
    try {
      await this.monitoringService.collectMetrics();
      return {
        success: true,
        timestamp: Date.now(),
      };
    } catch (error) {
      this.logger.error(`手动触发指标收集失败: ${error.message}`, error.stack);
      throw new HttpException(`手动触发指标收集失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取系统状态摘要
   */
  @Get('summary')
  getSystemSummary(): any {
    const metrics = this.monitoringService.getLatestMetrics();
    const activeAlerts = this.monitoringService.getActiveAlerts();
    
    if (!metrics) {
      return {
        status: 'unknown',
        message: '没有可用的指标数据',
        timestamp: Date.now(),
        alerts: {
          total: activeAlerts.length,
          critical: activeAlerts.filter(a => a.severity === 'critical').length,
          error: activeAlerts.filter(a => a.severity === 'error').length,
          warning: activeAlerts.filter(a => a.severity === 'warning').length,
          info: activeAlerts.filter(a => a.severity === 'info').length,
        },
      };
    }
    
    // 确定系统状态
    let status = 'healthy';
    let message = '系统运行正常';
    
    if (activeAlerts.some(a => a.severity === 'critical')) {
      status = 'critical';
      message = '系统存在严重问题';
    } else if (activeAlerts.some(a => a.severity === 'error')) {
      status = 'error';
      message = '系统存在错误';
    } else if (activeAlerts.some(a => a.severity === 'warning')) {
      status = 'warning';
      message = '系统存在警告';
    }
    
    return {
      status,
      message,
      timestamp: metrics.timestamp,
      uptime: metrics.uptime,
      resources: {
        cpu: {
          usage: metrics.cpuUsage,
          loadAverage: metrics.loadAverage,
        },
        memory: {
          usage: metrics.memoryUsage,
          total: metrics.totalMemory,
          free: metrics.freeMemory,
        },
        network: {
          connections: metrics.networkConnections,
        },
      },
      webrtc: metrics.webrtcStats,
      instances: metrics.instanceStats,
      alerts: {
        total: activeAlerts.length,
        critical: activeAlerts.filter(a => a.severity === 'critical').length,
        error: activeAlerts.filter(a => a.severity === 'error').length,
        warning: activeAlerts.filter(a => a.severity === 'warning').length,
        info: activeAlerts.filter(a => a.severity === 'info').length,
      },
    };
  }

  /**
   * 获取CPU使用率历史
   */
  @Get('metrics/cpu')
  getCpuUsageHistory(@Query('limit') limit: string): { timestamp: number; usage: number }[] {
    const limitNumber = limit ? parseInt(limit, 10) : 60;
    const metrics = this.monitoringService.getMetricsHistory(limitNumber);
    
    return metrics.map(metric => ({
      timestamp: metric.timestamp,
      usage: metric.cpuUsage,
    }));
  }

  /**
   * 获取内存使用率历史
   */
  @Get('metrics/memory')
  getMemoryUsageHistory(@Query('limit') limit: string): { timestamp: number; usage: number; total: number; free: number }[] {
    const limitNumber = limit ? parseInt(limit, 10) : 60;
    const metrics = this.monitoringService.getMetricsHistory(limitNumber);
    
    return metrics.map(metric => ({
      timestamp: metric.timestamp,
      usage: metric.memoryUsage,
      total: metric.totalMemory,
      free: metric.freeMemory,
    }));
  }

  /**
   * 获取实例统计历史
   */
  @Get('metrics/instances')
  getInstanceStatsHistory(@Query('limit') limit: string): { timestamp: number; total: number; active: number; users: number }[] {
    const limitNumber = limit ? parseInt(limit, 10) : 60;
    const metrics = this.monitoringService.getMetricsHistory(limitNumber);
    
    return metrics.map(metric => ({
      timestamp: metric.timestamp,
      total: metric.instanceStats.total,
      active: metric.instanceStats.active,
      users: metric.instanceStats.users,
    }));
  }

  /**
   * 获取WebRTC统计历史
   */
  @Get('metrics/webrtc')
  getWebRTCStatsHistory(@Query('limit') limit: string): { timestamp: number; workers: number; routers: number; transports: number }[] {
    const limitNumber = limit ? parseInt(limit, 10) : 60;
    const metrics = this.monitoringService.getMetricsHistory(limitNumber);
    
    return metrics.map(metric => ({
      timestamp: metric.timestamp,
      workers: metric.webrtcStats.workers,
      routers: metric.webrtcStats.routers,
      transports: metric.webrtcStats.transports,
    }));
  }
}
