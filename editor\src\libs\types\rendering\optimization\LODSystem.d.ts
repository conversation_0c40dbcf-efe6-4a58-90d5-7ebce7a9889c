/**
 * LOD（细节层次）系统
 * 根据与相机的距离动态调整模型的细节层次
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
import { LODComponent, LODLevel } from './LODComponent';
import { LODGenerator } from './LODGenerator';
/**
 * LOD系统配置接口
 */
export interface LODSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率（帧） */
    updateFrequency?: number;
    /** 是否使用视锥体检查 */
    useFrustumCheck?: boolean;
    /** 是否使用遮挡检查 */
    useOcclusionCheck?: boolean;
    /** 是否使用距离检查 */
    useDistanceCheck?: boolean;
    /** 是否使用屏幕大小检查 */
    useScreenSizeCheck?: boolean;
    /** 是否使用自动LOD生成 */
    useAutoLODGeneration?: boolean;
}
/**
 * LOD系统事件类型
 */
export declare enum LODSystemEventType {
    /** LOD级别变更 */
    LEVEL_CHANGED = "level_changed",
    /** LOD组件添加 */
    COMPONENT_ADDED = "component_added",
    /** LOD组件移除 */
    COMPONENT_REMOVED = "component_removed"
}
/**
 * LOD系统类
 */
export declare class LODSystem extends System {
    /** 系统类型 */
    private static readonly TYPE;
    /** LOD生成器 */
    private lodGenerator;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率（帧） */
    private updateFrequency;
    /** 当前帧计数 */
    private frameCount;
    /** 是否使用视锥体检查 */
    private useFrustumCheck;
    /** 是否使用遮挡检查 */
    protected useOcclusionCheck: boolean;
    /** 是否使用距离检查 */
    protected useDistanceCheck: boolean;
    /** 是否使用屏幕大小检查 */
    protected useScreenSizeCheck: boolean;
    /** 是否使用自动LOD生成 */
    private useAutoLODGeneration;
    /** 活跃相机 */
    private activeCamera;
    /** 活跃场景 */
    private activeScene;
    /** LOD组件列表 */
    protected lodComponents: Map<Entity, LODComponent>;
    /** 视锥体 */
    private frustum;
    /** 事件发射器 */
    private eventEmitter;
    /** 临时向量 */
    private tempVector;
    /** 临时矩阵 */
    private tempMatrix;
    /**
     * 创建LOD系统
     * @param options LOD系统配置
     */
    constructor(options?: LODSystemOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 设置活跃相机
     * @param camera 相机
     */
    setActiveCamera(camera: Camera): void;
    /**
     * 获取活跃相机
     * @returns 活跃相机
     */
    getActiveCamera(): Camera | null;
    /**
     * 设置活跃场景
     * @param scene 场景
     */
    setActiveScene(scene: Scene): void;
    /**
     * 获取活跃场景
     * @returns 活跃场景
     */
    getActiveScene(): Scene | null;
    /**
     * 注册LOD组件
     * @param entity 实体
     * @param component LOD组件
     */
    registerLODComponent(entity: Entity, component: LODComponent): void;
    /**
     * 注销LOD组件
     * @param entity 实体
     */
    unregisterLODComponent(entity: Entity): void;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 更新视锥体
     */
    private updateFrustum;
    /**
     * 更新所有LOD组件
     */
    private updateLODComponents;
    /**
     * 确定LOD级别
     * @param lodComponent LOD组件
     * @param distance 与相机的距离
     * @returns LOD级别
     */
    private determineLODLevel;
    /**
     * 手动更新LOD
     */
    manualUpdate(): void;
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 设置是否自动更新
     * @param autoUpdate 是否自动更新
     */
    setAutoUpdate(autoUpdate: boolean): void;
    /**
     * 获取是否自动更新
     * @returns 是否自动更新
     */
    isAutoUpdate(): boolean;
    /**
     * 设置更新频率
     * @param frequency 更新频率
     */
    setUpdateFrequency(frequency: number): void;
    /**
     * 获取更新频率
     * @returns 更新频率
     */
    getUpdateFrequency(): number;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: LODSystemEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: LODSystemEventType, listener: (...args: any[]) => void): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 自动生成LOD
     * @param entity 实体
     * @param mesh 网格
     * @param distances 距离配置
     * @returns 是否成功生成
     */
    autoGenerateLOD(entity: Entity, mesh: THREE.Mesh, distances?: number[]): boolean;
    /**
     * 自动生成LOD（自定义配置）
     * @param entity 实体
     * @param mesh 网格
     * @param levels LOD级别配置
     * @returns 是否成功生成
     */
    autoGenerateCustomLOD(entity: Entity, mesh: THREE.Mesh, levels: {
        level: LODLevel;
        distance: number;
        ratio: number;
    }[]): boolean;
    /**
     * 设置是否使用自动LOD生成
     * @param useAutoLODGeneration 是否使用自动LOD生成
     */
    setUseAutoLODGeneration(useAutoLODGeneration: boolean): void;
    /**
     * 获取是否使用自动LOD生成
     * @returns 是否使用自动LOD生成
     */
    isUseAutoLODGeneration(): boolean;
    /**
     * 获取LOD生成器
     * @returns LOD生成器
     */
    getLODGenerator(): LODGenerator | null;
    /**
     * 设置LOD生成器
     * @param generator LOD生成器
     */
    setLODGenerator(generator: LODGenerator): void;
}
