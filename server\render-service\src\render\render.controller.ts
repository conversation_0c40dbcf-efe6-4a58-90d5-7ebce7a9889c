/**
 * 渲染控制器
 */
import { Controller, Get, Post, Body, Param, Delete, UseGuards, Request, Query, Res, StreamableFile } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import * as fs from 'fs';
import { RenderService } from './render.service';
import { CreateRenderJobDto } from './dto/create-render-job.dto';
import { RenderJob, RenderJobStatus, RenderJobType } from './entities/render-job.entity';
import { RenderResult } from './entities/render-result.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('渲染')
@Controller('render')
export class RenderController {
  constructor(private readonly renderService: RenderService) {}

  @Post('jobs')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建渲染任务' })
  @ApiResponse({ status: 201, description: '渲染任务创建成功', type: RenderJob })
  async create(@Request() req, @Body() createRenderJobDto: CreateRenderJobDto): Promise<RenderJob> {
    return this.renderService.create(req.user.id, createRenderJobDto);
  }

  @Get('jobs')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有渲染任务' })
  @ApiResponse({ status: 200, description: '返回所有渲染任务', type: [RenderJob] })
  async findAll(
    @Request() req,
    @Query('status') status?: RenderJobStatus,
    @Query('type') type?: RenderJobType,
  ): Promise<RenderJob[]> {
    return this.renderService.findAll(req.user.id, status, type);
  }

  @Get('jobs/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取渲染任务' })
  @ApiResponse({ status: 200, description: '返回渲染任务信息', type: RenderJob })
  async findOne(@Param('id') id: string, @Request() req): Promise<RenderJob> {
    return this.renderService.findOne(id, req.user.id);
  }

  @Post('jobs/:id/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '取消渲染任务' })
  @ApiResponse({ status: 200, description: '渲染任务取消成功', type: RenderJob })
  async cancel(@Param('id') id: string, @Request() req): Promise<RenderJob> {
    return this.renderService.cancel(id, req.user.id);
  }

  @Delete('jobs/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除渲染任务' })
  @ApiResponse({ status: 204, description: '渲染任务删除成功' })
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    return this.renderService.remove(id, req.user.id);
  }

  @Get('results/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取渲染结果' })
  @ApiResponse({ status: 200, description: '返回渲染结果信息', type: RenderResult })
  async getResult(@Param('id') id: string, @Request() req): Promise<RenderResult> {
    return this.renderService.getResult(id, req.user.id);
  }

  @Get('results/:id/download')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '下载渲染结果文件' })
  @ApiResponse({ status: 200, description: '返回渲染结果文件' })
  async downloadResult(
    @Param('id') id: string,
    @Request() req,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    const result = await this.renderService.getResult(id, req.user.id);
    
    const file = fs.createReadStream(result.fileUrl);
    const fileName = result.fileUrl.split('/').pop();
    
    res.set({
      'Content-Disposition': `attachment; filename="${fileName}"`,
      'Content-Type': result.format === 'png' ? 'image/png' : 
                     result.format === 'jpg' || result.format === 'jpeg' ? 'image/jpeg' : 
                     result.format === 'mp4' ? 'video/mp4' : 'application/octet-stream',
    });
    
    return new StreamableFile(file);
  }
}
