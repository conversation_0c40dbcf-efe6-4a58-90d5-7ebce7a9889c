/**
 * 水体渲染优化系统
 * 用于优化水体渲染性能
 */
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
/**
 * 设备性能级别
 */
export declare enum DevicePerformanceLevel {
    /** 低 */
    LOW = 0,
    /** 中 */
    MEDIUM = 1,
    /** 高 */
    HIGH = 2
}
/**
 * 水体渲染优化系统配置接口
 */
export interface WaterRenderOptimizationConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
    /** 是否启用自动优化 */
    enableAutoOptimization?: boolean;
    /** 是否启用电池优化 */
    enableBatteryOptimization?: boolean;
    /** 是否启用温度优化 */
    enableTemperatureOptimization?: boolean;
    /** 是否启用网络优化 */
    enableNetworkOptimization?: boolean;
    /** 是否启用内存优化 */
    enableMemoryOptimization?: boolean;
    /** 是否启用GPU优化 */
    enableGPUOptimization?: boolean;
    /** 是否启用CPU优化 */
    enableCPUOptimization?: boolean;
    /** 是否启用移动设备优化 */
    enableMobileOptimization?: boolean;
    /** 是否启用桌面设备优化 */
    enableDesktopOptimization?: boolean;
    /** 是否启用VR设备优化 */
    enableVROptimization?: boolean;
    /** 是否启用AR设备优化 */
    enableAROptimization?: boolean;
    /** 是否启用调试可视化 */
    enableDebugVisualization?: boolean;
    /** 默认性能级别 */
    defaultPerformanceLevel?: DevicePerformanceLevel;
}
/**
 * 水体渲染优化系统
 */
export declare class WaterRenderOptimizationSystem extends System {
    /** 配置 */
    private config;
    /** 水体组件映射 */
    private waterBodies;
    /** 水体材质映射 */
    private waterMaterials;
    /** 水体渲染器 */
    private waterRenderer;
    /** 水体分块系统 */
    private waterChunkSystem;
    /** 水体LOD系统 */
    private waterLODSystem;
    /** 水体表面渲染器 */
    private waterSurfaceRenderer;
    /** 水体实例化渲染器 */
    private waterInstancedRenderer;
    /** 性能监控器 */
    private performanceMonitor;
    /** 事件发射器 */
    private eventEmitter;
    /** 帧计数器 */
    private frameCount;
    /** 当前性能级别 */
    private currentPerformanceLevel;
    /** 监控数据 */
    private monitorData;
    /** 性能配置 */
    private performanceConfigs;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: WaterRenderOptimizationConfig);
    /**
     * 初始化监控数据
     */
    private initializeMonitorData;
    /**
     * 查找相关系统
     */
    private findRelatedSystems;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 更新监控数据
     */
    private updateMonitorData;
    /**
     * 获取电池电量
     * @returns 电池电量（0-100）
     */
    private getBatteryLevel;
    /**
     * 检查电池是否正在充电
     * @returns 是否正在充电
     */
    private isBatteryCharging;
    /**
     * 获取设备温度
     * @returns 设备温度（摄氏度）
     */
    private getDeviceTemperature;
    /**
     * 获取网络延迟
     * @returns 网络延迟（毫秒）
     */
    private getNetworkLatency;
    /**
     * 获取网络带宽
     * @returns 网络带宽（Mbps）
     */
    private getNetworkBandwidth;
    /**
     * 获取水体顶点数量
     * @returns 水体顶点数量
     */
    private getWaterVerticesCount;
    /**
     * 获取水体绘制调用次数
     * @returns 水体绘制调用次数
     */
    private getWaterDrawCalls;
    /**
     * 获取水体纹理内存使用量
     * @returns 水体纹理内存使用量（MB）
     */
    private getWaterTextureMemory;
    /**
     * 获取水体几何体内存使用量
     * @returns 水体几何体内存使用量（MB）
     */
    private getWaterGeometryMemory;
    /**
     * 获取水体渲染时间
     * @returns 水体渲染时间（毫秒）
     */
    private getWaterRenderTime;
    /**
     * 获取水体物理模拟时间
     * @returns 水体物理模拟时间（毫秒）
     */
    private getWaterPhysicsTime;
    /**
     * 获取水体更新时间
     * @returns 水体更新时间（毫秒）
     */
    private getWaterUpdateTime;
    /**
     * 检查是否为移动设备
     * @returns 是否为移动设备
     */
    private isMobileDevice;
    /**
     * 检查是否为VR设备
     * @returns 是否为VR设备
     */
    private isVRDevice;
    /**
     * 检查是否为AR设备
     * @returns 是否为AR设备
     */
    private isARDevice;
    /**
     * 调整性能
     */
    private adjustPerformance;
    /**
     * 根据电池电量调整性能
     */
    private adjustPerformanceByBattery;
    /**
     * 根据设备温度调整性能
     */
    private adjustPerformanceByTemperature;
    /**
     * 根据网络状况调整性能
     */
    private adjustPerformanceByNetwork;
    /**
     * 根据内存使用情况调整性能
     */
    private adjustPerformanceByMemory;
    /**
     * 根据GPU使用情况调整性能
     */
    private adjustPerformanceByGPU;
    /**
     * 根据CPU使用情况调整性能
     */
    private adjustPerformanceByCPU;
    /**
     * 为移动设备调整性能
     */
    private adjustPerformanceForMobile;
    /**
     * 为桌面设备调整性能
     */
    private adjustPerformanceForDesktop;
    /**
     * 为VR设备调整性能
     */
    private adjustPerformanceForVR;
    /**
     * 为AR设备调整性能
     */
    private adjustPerformanceForAR;
    /**
     * 更新水体材质
     */
    private updateWaterMaterials;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 设置性能级别
     * @param level 性能级别
     */
    setPerformanceLevel(level: DevicePerformanceLevel): void;
    /**
     * 提高性能级别
     */
    private increasePerformanceLevel;
    /**
     * 降低性能级别
     */
    private decreasePerformanceLevel;
    /**
     * 应用性能配置
     * @param level 性能级别
     */
    private applyPerformanceConfig;
    /**
     * 更新水体
     * @param level 性能级别
     */
    private updateWaterBodies;
    /**
     * 添加水体组件
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterBody(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体组件
     * @param entity 实体
     */
    removeWaterBody(entity: Entity): void;
    /**
     * 获取当前性能级别
     * @returns 当前性能级别
     */
    getCurrentPerformanceLevel(): DevicePerformanceLevel;
    /**
     * 获取监控数据
     * @returns 监控数据
     */
    getMonitorData(): any;
}
