/**
 * 用户管理服务
 * 处理用户认证、授权、个人偏好和权限管理
 */
import { EventEmitter } from '../utils/EventEmitter';
// import { DatabaseManager } from '../database/DatabaseManager';

// 临时类型定义，实际应该从相应模块导入
interface DatabaseManager {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  query(sql: string, params?: any[]): Promise<any>;
  insert(table: string, data: any): Promise<any>;
  update(table: string, data: any, where: any): Promise<any>;
  delete(table: string, where: any): Promise<any>;
  findUserByUsernameOrEmail(username: string, email: string): Promise<any>;
  createUser(user: any, password: string): Promise<void>;
  getUserPassword(userId: string): Promise<string>;
  deleteSession(sessionId: string): Promise<void>;
  getUser(userId: string): Promise<any>;
  updateUser(userId: string, data: any): Promise<void>;
  createSession(session: any): Promise<void>;
  getActiveSessions(): Promise<any[]>;
}

// 简单的实现类
class MockDatabaseManager implements DatabaseManager {
  async connect(): Promise<void> { }
  async disconnect(): Promise<void> { }
  async query(sql: string, params?: any[]): Promise<any> { return []; }
  async insert(table: string, data: any): Promise<any> { return { id: 'mock-id' }; }
  async update(table: string, data: any, where: any): Promise<any> { return {}; }
  async delete(table: string, where: any): Promise<any> { return {}; }
  async findUserByUsernameOrEmail(username: string, email: string): Promise<any> { return null; }
  async createUser(user: any, password: string): Promise<void> { }
  async getUserPassword(userId: string): Promise<string> { return 'mock-hash'; }
  async deleteSession(sessionId: string): Promise<void> { }
  async getUser(userId: string): Promise<any> { return null; }
  async updateUser(userId: string, data: any): Promise<void> { }
  async createSession(session: any): Promise<void> { }
  async getActiveSessions(): Promise<any[]> { return []; }
}
import { CacheService } from './CacheService';

/**
 * 用户信息
 */
export interface User {
  /** 用户ID */
  id: string;
  /** 用户名 */
  username: string;
  /** 邮箱 */
  email: string;
  /** 显示名称 */
  displayName: string;
  /** 头像URL */
  avatar?: string;
  /** 用户角色 */
  role: 'admin' | 'premium' | 'standard' | 'guest';
  /** 用户状态 */
  status: 'active' | 'inactive' | 'suspended';
  /** 个人偏好 */
  preferences: UserPreferences;
  /** 权限列表 */
  permissions: string[];
  /** 创建时间 */
  createdAt: Date;
  /** 最后登录时间 */
  lastLoginAt?: Date;
  /** 用户统计 */
  stats: UserStats;
}

/**
 * 用户偏好设置
 */
export interface UserPreferences {
  /** 语言设置 */
  language: string;
  /** 主题设置 */
  theme: 'light' | 'dark' | 'auto';
  /** 场景生成偏好 */
  sceneGeneration: {
    /** 默认风格 */
    defaultStyle: string;
    /** 默认质量级别 */
    defaultQuality: 'fast' | 'balanced' | 'high';
    /** 是否启用实时预览 */
    enableRealTimePreview: boolean;
    /** 是否启用语音指导 */
    enableVoiceGuidance: boolean;
  };
  /** 界面设置 */
  ui: {
    /** 是否显示高级选项 */
    showAdvancedOptions: boolean;
    /** 是否自动保存 */
    autoSave: boolean;
    /** 自动保存间隔（秒） */
    autoSaveInterval: number;
  };
  /** 通知设置 */
  notifications: {
    /** 是否启用邮件通知 */
    email: boolean;
    /** 是否启用浏览器通知 */
    browser: boolean;
    /** 通知类型 */
    types: string[];
  };
}

/**
 * 用户统计信息
 */
export interface UserStats {
  /** 场景生成次数 */
  sceneGenerationCount: number;
  /** 语音交互次数 */
  voiceInteractionCount: number;
  /** 模板使用次数 */
  templateUsageCount: number;
  /** 资产下载次数 */
  assetDownloadCount: number;
  /** 总使用时长（分钟） */
  totalUsageTime: number;
  /** 最喜欢的风格 */
  favoriteStyle?: string;
  /** 平均场景复杂度 */
  averageSceneComplexity: number;
}

/**
 * 用户会话
 */
export interface UserSession {
  /** 会话ID */
  sessionId: string;
  /** 用户ID */
  userId: string;
  /** 创建时间 */
  createdAt: Date;
  /** 过期时间 */
  expiresAt: Date;
  /** 设备信息 */
  deviceInfo: {
    userAgent: string;
    ip: string;
    platform: string;
  };
  /** 是否活跃 */
  isActive: boolean;
}

/**
 * 登录凭据
 */
export interface LoginCredentials {
  /** 用户名或邮箱 */
  identifier: string;
  /** 密码 */
  password: string;
  /** 是否记住登录 */
  rememberMe?: boolean;
}

/**
 * 注册信息
 */
export interface RegisterInfo {
  /** 用户名 */
  username: string;
  /** 邮箱 */
  email: string;
  /** 密码 */
  password: string;
  /** 显示名称 */
  displayName: string;
  /** 邀请码（可选） */
  inviteCode?: string;
}

/**
 * 用户管理服务
 */
export class UserManagementService extends EventEmitter {
  private dbManager: DatabaseManager;
  private cacheService: CacheService;
  private activeSessions: Map<string, UserSession> = new Map();
  private sessionTimeout: number = 24 * 60 * 60 * 1000; // 24小时

  constructor() {
    super();
    this.dbManager = new MockDatabaseManager();
    this.cacheService = new CacheService();
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    await this.dbManager.connect();
    await this.cacheService.initialize();
    await this.loadActiveSessions();
    
    // 启动会话清理定时器
    setInterval(() => this.cleanupExpiredSessions(), 60 * 60 * 1000); // 每小时清理一次
    
    console.log('用户管理服务初始化完成');
  }

  /**
   * 用户注册
   */
  async register(registerInfo: RegisterInfo): Promise<User> {
    try {
      // 验证注册信息
      await this.validateRegisterInfo(registerInfo);

      // 检查用户名和邮箱是否已存在
      const existingUser = await this.dbManager.findUserByUsernameOrEmail(
        registerInfo.username,
        registerInfo.email
      );
      if (existingUser) {
        throw new Error('用户名或邮箱已存在');
      }

      // 创建用户
      const user: User = {
        id: this.generateUserId(),
        username: registerInfo.username,
        email: registerInfo.email,
        displayName: registerInfo.displayName,
        role: 'standard',
        status: 'active',
        preferences: this.getDefaultPreferences(),
        permissions: this.getDefaultPermissions('standard'),
        createdAt: new Date(),
        stats: this.getDefaultStats()
      };

      // 加密密码
      const hashedPassword = await this.hashPassword(registerInfo.password);

      // 保存到数据库
      await this.dbManager.createUser(user, hashedPassword);

      // 发出事件
      this.emit('userRegistered', user);

      return user;
    } catch (error) {
      console.error('用户注册失败:', error);
      throw error;
    }
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials, deviceInfo: any): Promise<{
    user: User;
    sessionId: string;
    token: string;
  }> {
    try {
      // 查找用户
      const user = await this.dbManager.findUserByUsernameOrEmail(
        credentials.identifier,
        credentials.identifier
      );
      if (!user) {
        throw new Error('用户不存在');
      }

      // 验证密码
      const isValidPassword = await this.verifyPassword(
        credentials.password,
        await this.dbManager.getUserPassword(user.id)
      );
      if (!isValidPassword) {
        throw new Error('密码错误');
      }

      // 检查用户状态
      if (user.status !== 'active') {
        throw new Error('用户账户已被禁用');
      }

      // 创建会话
      const session = await this.createSession(user.id, deviceInfo, credentials.rememberMe);

      // 生成访问令牌
      const token = await this.generateAccessToken(user.id, session.sessionId);

      // 更新最后登录时间
      await this.updateLastLoginTime(user.id);

      // 发出事件
      this.emit('userLoggedIn', { user, sessionId: session.sessionId });

      return {
        user,
        sessionId: session.sessionId,
        token
      };
    } catch (error) {
      console.error('用户登录失败:', error);
      throw error;
    }
  }

  /**
   * 用户登出
   */
  async logout(sessionId: string): Promise<void> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (session) {
        // 从活跃会话中移除
        this.activeSessions.delete(sessionId);

        // 从数据库中删除
        await this.dbManager.deleteSession(sessionId);

        // 从缓存中移除
        await this.cacheService.delete(`session:${sessionId}`);

        // 发出事件
        this.emit('userLoggedOut', { sessionId, userId: session.userId });
      }
    } catch (error) {
      console.error('用户登出失败:', error);
      throw error;
    }
  }

  /**
   * 验证会话
   */
  async validateSession(sessionId: string): Promise<User | null> {
    try {
      // 先从缓存获取
      const cachedUser = await this.cacheService.get(`session:${sessionId}`);
      if (cachedUser) {
        return JSON.parse(cachedUser);
      }

      // 从活跃会话获取
      const session = this.activeSessions.get(sessionId);
      if (!session || !session.isActive || session.expiresAt < new Date()) {
        return null;
      }

      // 获取用户信息
      const user = await this.dbManager.getUser(session.userId);
      if (user) {
        // 缓存用户信息
        await this.cacheService.set(
          `session:${sessionId}`,
          JSON.stringify(user),
          300 // 5分钟缓存
        );
      }

      return user;
    } catch (error) {
      console.error('验证会话失败:', error);
      return null;
    }
  }

  /**
   * 获取用户信息
   */
  async getUser(userId: string): Promise<User | null> {
    try {
      return await this.dbManager.getUser(userId);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 更新用户信息
   */
  async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    try {
      const user = await this.dbManager.getUser(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      const updatedUser: User = {
        ...user,
        ...updates
      };

      await this.dbManager.updateUser(userId, updatedUser);

      // 清除相关缓存
      await this.clearUserCache(userId);

      // 发出事件
      this.emit('userUpdated', updatedUser);

      return updatedUser;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户偏好
   */
  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<void> {
    try {
      const user = await this.dbManager.getUser(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      const updatedPreferences: UserPreferences = {
        ...user.preferences,
        ...preferences
      };

      await this.updateUser(userId, { preferences: updatedPreferences });

      // 发出事件
      this.emit('userPreferencesUpdated', { userId, preferences: updatedPreferences });
    } catch (error) {
      console.error('更新用户偏好失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户统计
   */
  async updateUserStats(userId: string, action: string, data?: any): Promise<void> {
    try {
      const user = await this.dbManager.getUser(userId);
      if (!user) {
        return;
      }

      const updatedStats = { ...user.stats };

      switch (action) {
        case 'scene_generation':
          updatedStats.sceneGenerationCount++;
          break;
        case 'voice_interaction':
          updatedStats.voiceInteractionCount++;
          break;
        case 'template_usage':
          updatedStats.templateUsageCount++;
          break;
        case 'asset_download':
          updatedStats.assetDownloadCount++;
          break;
        case 'usage_time':
          updatedStats.totalUsageTime += data?.duration || 0;
          break;
      }

      await this.updateUser(userId, { stats: updatedStats });
    } catch (error) {
      console.error('更新用户统计失败:', error);
    }
  }

  /**
   * 检查用户权限
   */
  async checkPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const user = await this.dbManager.getUser(userId);
      if (!user) {
        return false;
      }

      return user.permissions.includes(permission) || user.permissions.includes('*');
    } catch (error) {
      console.error('检查权限失败:', error);
      return false;
    }
  }

  /**
   * 创建会话
   */
  private async createSession(userId: string, deviceInfo: any, rememberMe?: boolean): Promise<UserSession> {
    const sessionId = this.generateSessionId();
    const expirationTime = rememberMe ? 30 * 24 * 60 * 60 * 1000 : this.sessionTimeout; // 30天或24小时
    
    const session: UserSession = {
      sessionId,
      userId,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + expirationTime),
      deviceInfo,
      isActive: true
    };

    // 保存到数据库
    await this.dbManager.createSession(session);

    // 添加到活跃会话
    this.activeSessions.set(sessionId, session);

    return session;
  }

  /**
   * 生成用户ID
   */
  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成访问令牌
   */
  private async generateAccessToken(userId: string, sessionId: string): Promise<string> {
    // 这里应该使用JWT或其他安全的令牌生成方式
    return Buffer.from(`${userId}:${sessionId}:${Date.now()}`).toString('base64');
  }

  /**
   * 密码加密
   */
  private async hashPassword(password: string): Promise<string> {
    // 这里应该使用bcrypt或其他安全的密码加密方式
    return Buffer.from(password).toString('base64');
  }

  /**
   * 密码验证
   */
  private async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    const hashed = await this.hashPassword(password);
    return hashed === hashedPassword;
  }

  /**
   * 验证注册信息
   */
  private async validateRegisterInfo(info: RegisterInfo): Promise<void> {
    if (!info.username || info.username.length < 3) {
      throw new Error('用户名至少需要3个字符');
    }

    if (!info.email || !this.isValidEmail(info.email)) {
      throw new Error('邮箱格式不正确');
    }

    if (!info.password || info.password.length < 6) {
      throw new Error('密码至少需要6个字符');
    }
  }

  /**
   * 验证邮箱格式
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 获取默认偏好设置
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      language: 'zh-CN',
      theme: 'light',
      sceneGeneration: {
        defaultStyle: 'modern',
        defaultQuality: 'balanced',
        enableRealTimePreview: true,
        enableVoiceGuidance: true
      },
      ui: {
        showAdvancedOptions: false,
        autoSave: true,
        autoSaveInterval: 300
      },
      notifications: {
        email: true,
        browser: true,
        types: ['system', 'updates']
      }
    };
  }

  /**
   * 获取默认权限
   */
  private getDefaultPermissions(role: string): string[] {
    const permissions: Record<string, string[]> = {
      admin: ['*'],
      premium: [
        'scene.generate',
        'scene.export',
        'template.create',
        'template.share',
        'asset.upload',
        'asset.download'
      ],
      standard: [
        'scene.generate',
        'template.use',
        'asset.download'
      ],
      guest: [
        'scene.generate'
      ]
    };

    return permissions[role] || permissions.guest;
  }

  /**
   * 获取默认统计信息
   */
  private getDefaultStats(): UserStats {
    return {
      sceneGenerationCount: 0,
      voiceInteractionCount: 0,
      templateUsageCount: 0,
      assetDownloadCount: 0,
      totalUsageTime: 0,
      averageSceneComplexity: 0
    };
  }

  /**
   * 更新最后登录时间
   */
  private async updateLastLoginTime(userId: string): Promise<void> {
    await this.dbManager.updateUser(userId, { lastLoginAt: new Date() });
  }

  /**
   * 清除用户缓存
   */
  private async clearUserCache(userId: string): Promise<void> {
    // 清除所有相关的缓存键
    const cacheKeys = [
      `user:${userId}`,
      `user_permissions:${userId}`,
      `user_preferences:${userId}`
    ];

    for (const key of cacheKeys) {
      await this.cacheService.delete(key);
    }
  }

  /**
   * 加载活跃会话
   */
  private async loadActiveSessions(): Promise<void> {
    try {
      const sessions = await this.dbManager.getActiveSessions();
      sessions.forEach(session => {
        if (session.expiresAt > new Date()) {
          this.activeSessions.set(session.sessionId, session);
        }
      });
      console.log(`加载了 ${sessions.length} 个活跃会话`);
    } catch (error) {
      console.error('加载活跃会话失败:', error);
    }
  }

  /**
   * 清理过期会话
   */
  private async cleanupExpiredSessions(): Promise<void> {
    const now = new Date();
    const expiredSessions: string[] = [];

    this.activeSessions.forEach((session, sessionId) => {
      if (session.expiresAt < now) {
        expiredSessions.push(sessionId);
      }
    });

    for (const sessionId of expiredSessions) {
      await this.logout(sessionId);
    }

    if (expiredSessions.length > 0) {
      console.log(`清理了 ${expiredSessions.length} 个过期会话`);
    }
  }

  /**
   * 销毁服务
   */
  async destroy(): Promise<void> {
    await this.dbManager.disconnect();
    await this.cacheService.destroy();
    this.activeSessions.clear();
    this.removeAllListeners();
  }
}
