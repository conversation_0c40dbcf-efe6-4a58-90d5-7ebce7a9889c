/**
 * 场景实例面板
 * 用于管理场景实例
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  List,
  Button,
  Input,
  InputNumber,
  Space,
  Tooltip,
  Switch,
  Dropdown,
  Modal,
  Form,
  Select,
  Tag,
  message,
  Card,
  Collapse
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  MoreOutlined,
  EditOutlined,
  SearchOutlined,
  CopyOutlined,
  SettingOutlined
} from '@ant-design/icons';
import EngineService from '../../services/EngineService';
import './InstancesPanel.less';

const { Panel } = Collapse;

// 场景模板数据接口
interface SceneTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  createdAt: string;
  updatedAt: string;
}

// 场景实例数据接口
interface SceneInstance {
  id: string;
  name: string;
  templateId: string;
  visible: boolean;
  position: [number, number, number];
  rotation: [number, number, number];
  scale: [number, number, number];
  parameters: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

const InstancesPanel: React.FC = () => {
  const { t } = useTranslation();
  
  // 状态
  const [templates, setTemplates] = useState<SceneTemplate[]>([]);
  const [instances, setInstances] = useState<SceneInstance[]>([]);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  const [selectedInstanceId, setSelectedInstanceId] = useState<string | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const [isCreateTemplateModalVisible, setIsCreateTemplateModalVisible] = useState(false);
  const [isCreateInstanceModalVisible, setIsCreateInstanceModalVisible] = useState(false);
  const [isEditInstanceModalVisible, setIsEditInstanceModalVisible] = useState(false);
  const [isParametersModalVisible, setIsParametersModalVisible] = useState(false);
  const [templateForm] = Form.useForm();
  const [instanceForm] = Form.useForm();
  const [editInstanceForm] = Form.useForm();
  const [parametersForm] = Form.useForm();
  
  // 加载数据
  useEffect(() => {
    loadTemplatesAndInstances();
  }, []);
  
  // 加载模板和实例
  const loadTemplatesAndInstances = () => {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) {
        setTemplates([]);
        setInstances([]);
        return;
      }
      
      // 这里应该从场景实例管理器获取数据
      // 由于我们还没有实现场景实例管理器，这里使用模拟数据
      
      // 模拟模板数据
      const mockTemplates: SceneTemplate[] = [
        {
          id: 'template1',
          name: '基础房间',
          description: '一个基础的房间模板，包含墙壁、地板和天花板',
          thumbnail: 'assets/thumbnails/room.jpg',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'template2',
          name: '办公桌',
          description: '办公桌模板，包含桌子、椅子和电脑',
          thumbnail: 'assets/thumbnails/desk.jpg',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'template3',
          name: '路灯',
          description: '街道路灯模板，包含灯柱和灯光',
          thumbnail: 'assets/thumbnails/streetlight.jpg',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];
      
      // 模拟实例数据
      const mockInstances: SceneInstance[] = [
        {
          id: 'instance1',
          name: '主房间',
          templateId: 'template1',
          visible: true,
          position: [0, 0, 0],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          parameters: {
            wallColor: '#ffffff',
            floorColor: '#cccccc',
            ceilingColor: '#eeeeee',
            windowCount: 2
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'instance2',
          name: '办公区1',
          templateId: 'template2',
          visible: true,
          position: [5, 0, 5],
          rotation: [0, 45, 0],
          scale: [1, 1, 1],
          parameters: {
            deskColor: '#8b4513',
            chairColor: '#000000',
            hasComputer: true
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'instance3',
          name: '办公区2',
          templateId: 'template2',
          visible: true,
          position: [-5, 0, 5],
          rotation: [0, -45, 0],
          scale: [1, 1, 1],
          parameters: {
            deskColor: '#8b4513',
            chairColor: '#000000',
            hasComputer: true
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];
      
      setTemplates(mockTemplates);
      setInstances(mockInstances);
    } catch (error) {
      console.error('加载模板和实例失败:', error);
      message.error('加载模板和实例失败');
    }
  };
  
  // 过滤模板
  const filteredTemplates = templates.filter(template => 
    template.name.toLowerCase().includes(searchValue.toLowerCase()) ||
    template.description.toLowerCase().includes(searchValue.toLowerCase())
  );
  
  // 过滤实例
  const filteredInstances = instances.filter(instance => 
    instance.name.toLowerCase().includes(searchValue.toLowerCase())
  );
  
  // 创建模板
  const handleCreateTemplate = () => {
    templateForm.validateFields().then(values => {
      try {
        const scene = EngineService.getActiveScene();
        if (!scene) {
          message.error('没有活动场景');
          return;
        }
        
        // 这里应该调用场景实例管理器创建模板
        // 由于我们还没有实现场景实例管理器，这里只是模拟创建
        
        const newTemplate: SceneTemplate = {
          id: `template_${Date.now()}`,
          name: values.name,
          description: values.description || '',
          thumbnail: values.thumbnail || 'assets/thumbnails/default.jpg',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        // 更新状态
        setTemplates(prevTemplates => [...prevTemplates, newTemplate]);
        
        // 关闭对话框
        setIsCreateTemplateModalVisible(false);
        templateForm.resetFields();
        
        message.success(`模板 "${values.name}" 已创建`);
      } catch (error) {
        console.error('创建模板失败:', error);
        message.error('创建模板失败');
      }
    });
  };
  
  // 创建实例
  const handleCreateInstance = () => {
    instanceForm.validateFields().then(values => {
      try {
        const scene = EngineService.getActiveScene();
        if (!scene) {
          message.error('没有活动场景');
          return;
        }
        
        // 这里应该调用场景实例管理器创建实例
        // 由于我们还没有实现场景实例管理器，这里只是模拟创建
        
        const newInstance: SceneInstance = {
          id: `instance_${Date.now()}`,
          name: values.name,
          templateId: values.templateId,
          visible: true,
          position: values.position || [0, 0, 0],
          rotation: values.rotation || [0, 0, 0],
          scale: values.scale || [1, 1, 1],
          parameters: values.parameters || {},
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        // 更新状态
        setInstances(prevInstances => [...prevInstances, newInstance]);
        
        // 关闭对话框
        setIsCreateInstanceModalVisible(false);
        instanceForm.resetFields();
        
        message.success(`实例 "${values.name}" 已创建`);
      } catch (error) {
        console.error('创建实例失败:', error);
        message.error('创建实例失败');
      }
    });
  };
  
  // 编辑实例
  const handleEditInstance = () => {
    if (!selectedInstanceId) return;
    
    editInstanceForm.validateFields().then(values => {
      try {
        const scene = EngineService.getActiveScene();
        if (!scene) {
          message.error('没有活动场景');
          return;
        }
        
        // 这里应该调用场景实例管理器更新实例
        // 由于我们还没有实现场景实例管理器，这里只是模拟更新
        
        // 更新状态
        setInstances(prevInstances => 
          prevInstances.map(instance => 
            instance.id === selectedInstanceId
              ? {
                  ...instance,
                  name: values.name,
                  position: values.position,
                  rotation: values.rotation,
                  scale: values.scale,
                  updatedAt: new Date().toISOString()
                }
              : instance
          )
        );
        
        // 关闭对话框
        setIsEditInstanceModalVisible(false);
        editInstanceForm.resetFields();
        
        message.success(`实例 "${values.name}" 已更新`);
      } catch (error) {
        console.error('编辑实例失败:', error);
        message.error('编辑实例失败');
      }
    });
  };
  
  // 更新实例参数
  const handleUpdateParameters = () => {
    if (!selectedInstanceId) return;
    
    parametersForm.validateFields().then(values => {
      try {
        const scene = EngineService.getActiveScene();
        if (!scene) {
          message.error('没有活动场景');
          return;
        }
        
        // 这里应该调用场景实例管理器更新实例参数
        // 由于我们还没有实现场景实例管理器，这里只是模拟更新
        
        // 更新状态
        setInstances(prevInstances => 
          prevInstances.map(instance => 
            instance.id === selectedInstanceId
              ? {
                  ...instance,
                  parameters: values,
                  updatedAt: new Date().toISOString()
                }
              : instance
          )
        );
        
        // 关闭对话框
        setIsParametersModalVisible(false);
        
        message.success('实例参数已更新');
      } catch (error) {
        console.error('更新实例参数失败:', error);
        message.error('更新实例参数失败');
      }
    });
  };
  
  // 删除模板
  const handleDeleteTemplate = (templateId: string) => {
    try {
      // 获取模板名称
      const template = templates.find(t => t.id === templateId);
      const templateName = template ? template.name : templateId;
      
      // 检查是否有实例使用此模板
      const hasInstances = instances.some(instance => instance.templateId === templateId);
      
      if (hasInstances) {
        Modal.confirm({
          title: t('editor.instances.cannotDeleteTemplate'),
          content: t('editor.instances.templateInUse'),
          okText: t('common.ok'),
          cancelButtonProps: { style: { display: 'none' } }
        });
        return;
      }
      
      // 确认删除
      Modal.confirm({
        title: t('editor.instances.confirmDeleteTemplate'),
        content: t('editor.instances.confirmDeleteTemplateContent', { name: templateName }),
        okText: t('common.yes'),
        cancelText: t('common.no'),
        onOk: () => {
          // 删除模板
          setTemplates(prevTemplates => prevTemplates.filter(t => t.id !== templateId));
          
          // 如果删除的是当前选中的模板，清除选择
          if (selectedTemplateId === templateId) {
            setSelectedTemplateId(null);
          }
          
          message.success(`模板 "${templateName}" 已删除`);
        }
      });
    } catch (error) {
      console.error('删除模板失败:', error);
      message.error('删除模板失败');
    }
  };
  
  // 删除实例
  const handleDeleteInstance = (instanceId: string) => {
    try {
      // 获取实例名称
      const instance = instances.find(i => i.id === instanceId);
      const instanceName = instance ? instance.name : instanceId;
      
      // 确认删除
      Modal.confirm({
        title: t('editor.instances.confirmDeleteInstance'),
        content: t('editor.instances.confirmDeleteInstanceContent', { name: instanceName }),
        okText: t('common.yes'),
        cancelText: t('common.no'),
        onOk: () => {
          // 删除实例
          setInstances(prevInstances => prevInstances.filter(i => i.id !== instanceId));
          
          // 如果删除的是当前选中的实例，清除选择
          if (selectedInstanceId === instanceId) {
            setSelectedInstanceId(null);
          }
          
          message.success(`实例 "${instanceName}" 已删除`);
        }
      });
    } catch (error) {
      console.error('删除实例失败:', error);
      message.error('删除实例失败');
    }
  };
  
  // 切换实例可见性
  const handleToggleInstanceVisibility = (instanceId: string, visible: boolean) => {
    try {
      // 更新状态
      setInstances(prevInstances => 
        prevInstances.map(instance => 
          instance.id === instanceId
            ? { ...instance, visible: !visible }
            : instance
        )
      );
    } catch (error) {
      console.error('切换实例可见性失败:', error);
    }
  };
  
  // 打开编辑实例对话框
  const handleOpenEditInstanceModal = (instanceId: string) => {
    try {
      const instance = instances.find(i => i.id === instanceId);
      if (!instance) return;
      
      // 设置表单初始值
      editInstanceForm.setFieldsValue({
        name: instance.name,
        position: instance.position,
        rotation: instance.rotation,
        scale: instance.scale
      });
      
      setSelectedInstanceId(instanceId);
      setIsEditInstanceModalVisible(true);
    } catch (error) {
      console.error('打开编辑实例对话框失败:', error);
    }
  };
  
  // 打开参数对话框
  const handleOpenParametersModal = (instanceId: string) => {
    try {
      const instance = instances.find(i => i.id === instanceId);
      if (!instance) return;
      
      // 设置表单初始值
      parametersForm.setFieldsValue(instance.parameters);
      
      setSelectedInstanceId(instanceId);
      setIsParametersModalVisible(true);
    } catch (error) {
      console.error('打开参数对话框失败:', error);
    }
  };
  
  return (
    <div className="instances-panel">
      <div className="panel-header">
        <h3>{t('editor.panels.instances')}</h3>
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateTemplateModalVisible(true)}
          >
            {t('editor.instances.addTemplate')}
          </Button>
          <Button
            type="primary"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => setIsCreateInstanceModalVisible(true)}
          >
            {t('editor.instances.addInstance')}
          </Button>
        </Space>
      </div>
      
      <Input
        placeholder={t('editor.instances.search') as string}
        prefix={<SearchOutlined />}
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        className="search-input"
      />
      
      <Collapse defaultActiveKey={['templates', 'instances']}>
        <Panel header={t('editor.instances.templates')} key="templates">
          <List
            className="templates-list"
            grid={{ gutter: 16, column: 2 }}
            dataSource={filteredTemplates}
            renderItem={(template) => (
              <List.Item>
                <Card
                  hoverable
                  cover={<img alt={template.name} src={template.thumbnail} />}
                  className={`template-card ${selectedTemplateId === template.id ? 'selected' : ''}`}
                  onClick={() => setSelectedTemplateId(template.id)}
                  actions={[
                    <Tooltip title={t('editor.instances.edit')}>
                      <EditOutlined key="edit" />
                    </Tooltip>,
                    <Tooltip title={t('editor.instances.delete')}>
                      <DeleteOutlined 
                        key="delete" 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteTemplate(template.id);
                        }}
                      />
                    </Tooltip>,
                  ]}
                >
                  <Card.Meta
                    title={template.name}
                    description={template.description}
                  />
                </Card>
              </List.Item>
            )}
          />
        </Panel>
        
        <Panel header={t('editor.instances.instances')} key="instances">
          <List
            className="instances-list"
            dataSource={filteredInstances}
            renderItem={(instance) => (
              <List.Item
                key={instance.id}
                className={`instance-item ${selectedInstanceId === instance.id ? 'selected' : ''}`}
                onClick={() => setSelectedInstanceId(instance.id)}
              >
                <div className="instance-info">
                  <div className="instance-name">{instance.name}</div>
                  <div className="instance-meta">
                    <Tag>
                      {templates.find(t => t.id === instance.templateId)?.name || instance.templateId}
                    </Tag>
                    <span className="instance-position">
                      位置: ({instance.position.map(v => v.toFixed(1)).join(', ')})
                    </span>
                  </div>
                </div>
                <div className="instance-actions">
                  <Button
                    type="text"
                    size="small"
                    icon={instance.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleToggleInstanceVisibility(instance.id, instance.visible);
                    }}
                  />
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: 'edit',
                          icon: <EditOutlined />,
                          label: t('editor.instances.edit'),
                          onClick: () => handleOpenEditInstanceModal(instance.id)
                        },
                        {
                          key: 'parameters',
                          icon: <SettingOutlined />,
                          label: t('editor.instances.parameters'),
                          onClick: () => handleOpenParametersModal(instance.id)
                        },
                        {
                          type: 'divider' as const
                        },
                        {
                          key: 'delete',
                          icon: <DeleteOutlined />,
                          label: t('editor.instances.delete'),
                          danger: true,
                          onClick: () => handleDeleteInstance(instance.id)
                        }
                      ]
                    }}
                    trigger={['click']}
                  >
                    <Button
                      type="text"
                      size="small"
                      icon={<MoreOutlined />}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Dropdown>
                </div>
              </List.Item>
            )}
          />
        </Panel>
      </Collapse>
      
      {/* 创建模板对话框 */}
      <Modal
        title={t('editor.instances.createTemplate')}
        open={isCreateTemplateModalVisible}
        onOk={handleCreateTemplate}
        onCancel={() => setIsCreateTemplateModalVisible(false)}
      >
        <Form form={templateForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('editor.instances.templateName')}
            rules={[{ required: true, message: t('editor.instances.templateNameRequired') as string }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label={t('editor.instances.templateDescription')}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 创建实例对话框 */}
      <Modal
        title={t('editor.instances.createInstance')}
        open={isCreateInstanceModalVisible}
        onOk={handleCreateInstance}
        onCancel={() => setIsCreateInstanceModalVisible(false)}
      >
        <Form form={instanceForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('editor.instances.instanceName')}
            rules={[{ required: true, message: t('editor.instances.instanceNameRequired') as string }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="templateId"
            label={t('editor.instances.template')}
            rules={[{ required: true, message: t('editor.instances.templateRequired') as string }]}
          >
            <Select>
              {templates.map(template => (
                <Select.Option key={template.id} value={template.id}>
                  {template.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="position"
            label={t('editor.instances.position')}
            initialValue={[0, 0, 0]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 编辑实例对话框 */}
      <Modal
        title={t('editor.instances.editInstance')}
        open={isEditInstanceModalVisible}
        onOk={handleEditInstance}
        onCancel={() => setIsEditInstanceModalVisible(false)}
      >
        <Form form={editInstanceForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('editor.instances.instanceName')}
            rules={[{ required: true, message: t('editor.instances.instanceNameRequired') as string }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="position"
            label={t('editor.instances.position')}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="rotation"
            label={t('editor.instances.rotation')}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="scale"
            label={t('editor.instances.scale')}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 参数对话框 */}
      <Modal
        title={t('editor.instances.parameters')}
        open={isParametersModalVisible}
        onOk={handleUpdateParameters}
        onCancel={() => setIsParametersModalVisible(false)}
        width={600}
      >
        <Form form={parametersForm} layout="vertical">
          {selectedInstanceId && instances.find(i => i.id === selectedInstanceId)?.templateId === 'template1' && (
            <>
              <Form.Item
                name="wallColor"
                label={t('editor.instances.wallColor')}
              >
                <Input type="color" />
              </Form.Item>
              <Form.Item
                name="floorColor"
                label={t('editor.instances.floorColor')}
              >
                <Input type="color" />
              </Form.Item>
              <Form.Item
                name="ceilingColor"
                label={t('editor.instances.ceilingColor')}
              >
                <Input type="color" />
              </Form.Item>
              <Form.Item
                name="windowCount"
                label={t('editor.instances.windowCount')}
              >
                <InputNumber min={0} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </>
          )}
          
          {selectedInstanceId && instances.find(i => i.id === selectedInstanceId)?.templateId === 'template2' && (
            <>
              <Form.Item
                name="deskColor"
                label={t('editor.instances.deskColor')}
              >
                <Input type="color" />
              </Form.Item>
              <Form.Item
                name="chairColor"
                label={t('editor.instances.chairColor')}
              >
                <Input type="color" />
              </Form.Item>
              <Form.Item
                name="hasComputer"
                label={t('editor.instances.hasComputer')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default InstancesPanel;
