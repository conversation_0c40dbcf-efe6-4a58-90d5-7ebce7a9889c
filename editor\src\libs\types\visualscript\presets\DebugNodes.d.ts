/**
 * 视觉脚本调试节点
 * 提供调试、性能分析和监控功能
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 断点节点
 * 在执行到该节点时暂停执行
 */
export declare class BreakpointNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 日志节点
 * 输出日志信息
 */
export declare class LogNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 性能计时节点
 * 测量代码执行时间
 */
export declare class PerformanceTimerNode extends FlowNode {
    /** 开始时间 */
    private startTime;
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
    /**
     * 检查输入是否被触发
     * @param inputName 输入名称
     * @returns 是否被触发
     */
    private isInputTriggered;
}
/**
 * 变量监视节点
 * 监视变量的变化
 */
export declare class VariableWatchNode extends FlowNode {
    /** 上一个值 */
    private previousValue;
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
    /**
     * 比较两个值是否相等
     * @param a 第一个值
     * @param b 第二个值
     * @returns 是否相等
     */
    private areValuesEqual;
    /**
     * 克隆值
     * @param value 要克隆的值
     * @returns 克隆后的值
     */
    private cloneValue;
}
/**
 * 断言节点
 * 验证条件是否为真
 */
export declare class AssertNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 注册调试节点
 * @param registry 节点注册表
 */
export declare function registerDebugNodes(registry: NodeRegistry): void;
