/**
 * GLTF动画组件
 * 用于管理GLTF模型的动画
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
/**
 * 动画状态
 */
export declare enum AnimationState {
    STOPPED = "stopped",
    PLAYING = "playing",
    PAUSED = "paused",
    FINISHED = "finished"
}
/**
 * 动画循环模式
 */
export declare enum AnimationLoopMode {
    ONCE,
    REPEAT,
    PINGPONG
}
/**
 * 动画混合模式
 */
export declare enum AnimationBlendMode {
    NORMAL = "normal",
    ADDITIVE = "additive"
}
/**
 * 动画事件类型
 */
export declare enum AnimationEventType {
    START = "start",
    STOP = "stop",
    PAUSE = "pause",
    RESUME = "resume",
    LOOP = "loop",
    FINISHED = "finished",
    TIME_UPDATE = "timeUpdate"
}
/**
 * GLTF动画组件
 */
export declare class GLTFAnimationComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 动画剪辑 */
    private clips;
    /** 动画混合器 */
    private mixer;
    /** 活跃动作 */
    private actions;
    /** 当前状态 */
    private state;
    /** 事件发射器 */
    private emitter;
    /** 时间比例 */
    private timeScale;
    /** 默认循环模式 */
    private defaultLoopMode;
    /** 默认混合模式 */
    private defaultBlendMode;
    /** 默认交叉淡入时间 */
    private defaultCrossFadeTime;
    /**
     * 创建GLTF动画组件
     * @param clips 动画剪辑
     */
    constructor(clips: THREE.AnimationClip[]);
    /**
     * 初始化动画混合器
     * @param root 根对象
     */
    initMixer(root: THREE.Object3D): void;
    /**
     * 更新动画
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 播放动画
     * @param name 动画名称
     * @param options 播放选项
     * @returns 动画动作
     */
    play(name: string, options?: {
        loopMode?: AnimationLoopMode;
        blendMode?: AnimationBlendMode;
        crossFadeTime?: number;
        timeScale?: number;
        clampWhenFinished?: boolean;
    }): THREE.AnimationAction | null;
    /**
     * 停止动画
     * @param name 动画名称，如果未提供则停止所有动画
     */
    stop(name?: string): void;
    /**
     * 暂停动画
     * @param name 动画名称，如果未提供则暂停所有动画
     */
    pause(name?: string): void;
    /**
     * 恢复动画
     * @param name 动画名称，如果未提供则恢复所有动画
     */
    resume(name?: string): void;
    /**
     * 获取动画剪辑
     * @returns 动画剪辑数组
     */
    getClips(): THREE.AnimationClip[];
    /**
     * 获取动画混合器
     * @returns 动画混合器
     */
    getMixer(): THREE.AnimationMixer | null;
    /**
     * 获取动作
     * @param name 动画名称
     * @returns 动画动作
     */
    getAction(name: string): THREE.AnimationAction | undefined;
    /**
     * 获取所有动作
     * @returns 动作映射
     */
    getActions(): Map<string, THREE.AnimationAction>;
    /**
     * 获取当前状态
     * @returns 动画状态
     */
    getState(): AnimationState;
    /**
     * 设置时间比例
     * @param scale 时间比例
     */
    setTimeScale(scale: number): void;
    /**
     * 获取时间比例
     * @returns 时间比例
     */
    getTimeScale(): number;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: AnimationEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: AnimationEventType, listener: (...args: any[]) => void): void;
    /**
     * 销毁组件
     */
    dispose(): void;
}
