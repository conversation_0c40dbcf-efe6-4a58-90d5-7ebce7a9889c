/**
 * 物理体组件
 * 为实体提供物理属性和行为
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
/**
 * 物理体类型
 */
export declare enum BodyType {
    /** 静态物体 */
    STATIC = "static",
    /** 动态物体 */
    DYNAMIC = "dynamic",
    /** 运动学物体 */
    KINEMATIC = "kinematic"
}
/**
 * 物理体组件选项
 */
export interface PhysicsBodyOptions {
    /** 物理体类型 */
    type?: BodyType;
    /** 质量 */
    mass?: number;
    /** 位置 */
    position?: THREE.Vector3;
    /** 旋转 */
    quaternion?: THREE.Quaternion;
    /** 线性阻尼 */
    linearDamping?: number;
    /** 角阻尼 */
    angularDamping?: number;
    /** 是否允许休眠 */
    allowSleep?: boolean;
    /** 休眠速度阈值 */
    sleepSpeedLimit?: number;
    /** 休眠时间阈值 */
    sleepTimeLimit?: number;
    /** 碰撞过滤组 */
    collisionFilterGroup?: number;
    /** 碰撞过滤掩码 */
    collisionFilterMask?: number;
    /** 物理材质 */
    material?: CANNON.Material;
    /** 是否固定旋转 */
    fixedRotation?: boolean;
    /** 是否自动更新变换 */
    autoUpdateTransform?: boolean;
}
/**
 * 物理体组件
 */
export declare class PhysicsBodyComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 物理体类型 */
    bodyType: BodyType;
    /** 质量 */
    mass: number;
    /** 位置 */
    position: THREE.Vector3;
    /** 旋转 */
    quaternion: THREE.Quaternion;
    /** 线性阻尼 */
    linearDamping: number;
    /** 角阻尼 */
    angularDamping: number;
    /** 是否允许休眠 */
    allowSleep: boolean;
    /** 休眠速度阈值 */
    sleepSpeedLimit: number;
    /** 休眠时间阈值 */
    sleepTimeLimit: number;
    /** 碰撞过滤组 */
    collisionFilterGroup: number;
    /** 碰撞过滤掩码 */
    collisionFilterMask: number;
    /** 物理材质 */
    material: CANNON.Material | null;
    /** 是否固定旋转 */
    fixedRotation: boolean;
    /** 是否自动更新变换 */
    autoUpdateTransform: boolean;
    /** CANNON.js物理体 */
    private body;
    /** 物理世界 */
    private world;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /**
     * 创建物理体组件
     * @param options 物理体选项
     */
    constructor(options?: PhysicsBodyOptions);
    /**
     * 初始化物理体
     * @param world 物理世界
     */
    initialize(world: CANNON.World): void;
    /**
     * 获取CANNON.js物理体类型
     * @returns CANNON.js物理体类型
     */
    private getBodyType;
    /**
     * 更新实体变换
     */
    updateTransform(): void;
    /**
     * 应用力
     * @param force 力向量
     * @param worldPoint 世界坐标中的作用点（可选）
     */
    applyForce(force: THREE.Vector3, worldPoint?: THREE.Vector3): void;
    /**
     * 应用冲量
     * @param impulse 冲量向量
     * @param worldPoint 世界坐标中的作用点（可选）
     */
    applyImpulse(impulse: THREE.Vector3, worldPoint?: THREE.Vector3): void;
    /**
     * 应用扭矩
     * @param torque 扭矩向量
     */
    applyTorque(torque: THREE.Vector3): void;
    /**
     * 设置线性速度
     * @param velocity 线性速度向量
     */
    setLinearVelocity(velocity: THREE.Vector3): void;
    /**
     * 获取线性速度
     * @returns 线性速度向量
     */
    getLinearVelocity(): THREE.Vector3;
    /**
     * 设置角速度
     * @param angularVelocity 角速度向量
     */
    setAngularVelocity(angularVelocity: THREE.Vector3): void;
    /**
     * 获取角速度
     * @returns 角速度向量
     */
    getAngularVelocity(): THREE.Vector3;
    /**
     * 设置物理体类型
     * @param type 物理体类型
     */
    setBodyType(type: BodyType): void;
    /**
     * 获取CANNON.js物理体
     * @returns CANNON.js物理体
     */
    getCannonBody(): CANNON.Body | null;
    /**
     * 碰撞开始回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞接触点
     */
    onCollisionStart(_otherEntity: Entity, _contact: CANNON.ContactEquation): void;
    /**
     * 碰撞结束回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞接触点
     */
    onCollisionEnd(_otherEntity: Entity, _contact: CANNON.ContactEquation): void;
    /**
     * 销毁物理体
     */
    dispose(): void;
}
