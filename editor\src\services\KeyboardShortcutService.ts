/**
 * 键盘快捷键服务
 * 管理全局键盘快捷键的注册、监听和执行
 */

export interface KeyboardShortcut {
  id: string;
  name: string;
  description: string;
  keys: string[];
  category: string;
  action: () => void;
  enabled: boolean;
  global?: boolean;
  context?: string;
}

export interface ShortcutCategory {
  id: string;
  name: string;
  description: string;
  shortcuts: KeyboardShortcut[];
}

class KeyboardShortcutService {
  private static instance: KeyboardShortcutService;
  private shortcuts: Map<string, KeyboardShortcut> = new Map();
  private categories: Map<string, ShortcutCategory> = new Map();
  private pressedKeys: Set<string> = new Set();
  private isListening: boolean = false;

  constructor() {
    this.initializeDefaultShortcuts();
    this.loadUserCustomizations();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): KeyboardShortcutService {
    if (!KeyboardShortcutService.instance) {
      KeyboardShortcutService.instance = new KeyboardShortcutService();
    }
    return KeyboardShortcutService.instance;
  }

  /**
   * 初始化默认快捷键
   */
  private initializeDefaultShortcuts(): void {
    // 文件操作快捷键
    this.addCategory({
      id: 'file',
      name: '文件操作',
      description: '文件相关的快捷键',
      shortcuts: []
    });

    this.registerShortcut({
      id: 'file.new',
      name: '新建文件',
      description: '创建新的脚本文件',
      keys: ['Ctrl', 'N'],
      category: 'file',
      action: () => this.executeAction('file.new'),
      enabled: true,
      global: true
    });

    this.registerShortcut({
      id: 'file.save',
      name: '保存文件',
      description: '保存当前文件',
      keys: ['Ctrl', 'S'],
      category: 'file',
      action: () => this.executeAction('file.save'),
      enabled: true,
      global: true
    });

    this.registerShortcut({
      id: 'file.saveAs',
      name: '另存为',
      description: '将文件另存为新文件',
      keys: ['Ctrl', 'Shift', 'S'],
      category: 'file',
      action: () => this.executeAction('file.saveAs'),
      enabled: true,
      global: true
    });

    // 编辑操作快捷键
    this.addCategory({
      id: 'edit',
      name: '编辑操作',
      description: '编辑相关的快捷键',
      shortcuts: []
    });

    this.registerShortcut({
      id: 'edit.undo',
      name: '撤销',
      description: '撤销上一步操作',
      keys: ['Ctrl', 'Z'],
      category: 'edit',
      action: () => this.executeAction('edit.undo'),
      enabled: true,
      global: true
    });

    this.registerShortcut({
      id: 'edit.redo',
      name: '重做',
      description: '重做上一步操作',
      keys: ['Ctrl', 'Y'],
      category: 'edit',
      action: () => this.executeAction('edit.redo'),
      enabled: true,
      global: true
    });

    this.registerShortcut({
      id: 'edit.copy',
      name: '复制',
      description: '复制选中内容',
      keys: ['Ctrl', 'C'],
      category: 'edit',
      action: () => this.executeAction('edit.copy'),
      enabled: true,
      global: true
    });

    this.registerShortcut({
      id: 'edit.paste',
      name: '粘贴',
      description: '粘贴剪贴板内容',
      keys: ['Ctrl', 'V'],
      category: 'edit',
      action: () => this.executeAction('edit.paste'),
      enabled: true,
      global: true
    });

    // 脚本编辑器快捷键
    this.addCategory({
      id: 'script',
      name: '脚本编辑',
      description: '脚本编辑器相关的快捷键',
      shortcuts: []
    });

    this.registerShortcut({
      id: 'script.run',
      name: '运行脚本',
      description: '执行当前脚本',
      keys: ['F5'],
      category: 'script',
      action: () => this.executeAction('script.run'),
      enabled: true,
      context: 'script-editor'
    });

    this.registerShortcut({
      id: 'script.debug',
      name: '调试脚本',
      description: '以调试模式运行脚本',
      keys: ['F9'],
      category: 'script',
      action: () => this.executeAction('script.debug'),
      enabled: true,
      context: 'script-editor'
    });

    this.registerShortcut({
      id: 'script.stop',
      name: '停止脚本',
      description: '停止当前运行的脚本',
      keys: ['Shift', 'F5'],
      category: 'script',
      action: () => this.executeAction('script.stop'),
      enabled: true,
      context: 'script-editor'
    });

    this.registerShortcut({
      id: 'script.format',
      name: '格式化代码',
      description: '格式化当前代码',
      keys: ['Shift', 'Alt', 'F'],
      category: 'script',
      action: () => this.executeAction('script.format'),
      enabled: true,
      context: 'script-editor'
    });

    // 可视化脚本编辑器快捷键
    this.addCategory({
      id: 'visual-script',
      name: '可视化脚本',
      description: '可视化脚本编辑器相关的快捷键',
      shortcuts: []
    });

    this.registerShortcut({
      id: 'visual.addNode',
      name: '添加节点',
      description: '打开节点选择器',
      keys: ['Space'],
      category: 'visual-script',
      action: () => this.executeAction('visual.addNode'),
      enabled: true,
      context: 'visual-script-editor'
    });

    this.registerShortcut({
      id: 'visual.deleteNode',
      name: '删除节点',
      description: '删除选中的节点',
      keys: ['Delete'],
      category: 'visual-script',
      action: () => this.executeAction('visual.deleteNode'),
      enabled: true,
      context: 'visual-script-editor'
    });

    this.registerShortcut({
      id: 'visual.duplicateNode',
      name: '复制节点',
      description: '复制选中的节点',
      keys: ['Ctrl', 'D'],
      category: 'visual-script',
      action: () => this.executeAction('visual.duplicateNode'),
      enabled: true,
      context: 'visual-script-editor'
    });

    // 视图控制快捷键
    this.addCategory({
      id: 'view',
      name: '视图控制',
      description: '视图和界面相关的快捷键',
      shortcuts: []
    });

    this.registerShortcut({
      id: 'view.toggleFullscreen',
      name: '切换全屏',
      description: '切换编辑器全屏模式',
      keys: ['F11'],
      category: 'view',
      action: () => this.executeAction('view.toggleFullscreen'),
      enabled: true,
      global: true
    });

    this.registerShortcut({
      id: 'view.toggleTheme',
      name: '切换主题',
      description: '在亮色和暗色主题间切换',
      keys: ['Ctrl', 'Shift', 'T'],
      category: 'view',
      action: () => this.executeAction('view.toggleTheme'),
      enabled: true,
      global: true
    });

    this.registerShortcut({
      id: 'view.zoomIn',
      name: '放大',
      description: '放大编辑器内容',
      keys: ['Ctrl', '='],
      category: 'view',
      action: () => this.executeAction('view.zoomIn'),
      enabled: true,
      global: true
    });

    this.registerShortcut({
      id: 'view.zoomOut',
      name: '缩小',
      description: '缩小编辑器内容',
      keys: ['Ctrl', '-'],
      category: 'view',
      action: () => this.executeAction('view.zoomOut'),
      enabled: true,
      global: true
    });
  }

  /**
   * 注册快捷键
   */
  public registerShortcut(shortcut: KeyboardShortcut): void {
    this.shortcuts.set(shortcut.id, shortcut);
    
    // 添加到对应分类
    const category = this.categories.get(shortcut.category);
    if (category) {
      category.shortcuts.push(shortcut);
    }
  }

  /**
   * 添加分类
   */
  public addCategory(category: ShortcutCategory): void {
    this.categories.set(category.id, category);
  }

  /**
   * 开始监听键盘事件
   */
  public startListening(): void {
    if (this.isListening) return;
    
    this.isListening = true;
    document.addEventListener('keydown', this.handleKeyDown);
    document.addEventListener('keyup', this.handleKeyUp);
  }

  /**
   * 停止监听键盘事件
   */
  public stopListening(): void {
    if (!this.isListening) return;
    
    this.isListening = false;
    document.removeEventListener('keydown', this.handleKeyDown);
    document.removeEventListener('keyup', this.handleKeyUp);
  }

  /**
   * 处理按键按下事件
   */
  private handleKeyDown = (event: KeyboardEvent): void => {
    const key = this.normalizeKey(event);
    this.pressedKeys.add(key);
    
    // 检查是否匹配任何快捷键
    this.checkShortcuts(event);
  };

  /**
   * 处理按键释放事件
   */
  private handleKeyUp = (event: KeyboardEvent): void => {
    const key = this.normalizeKey(event);
    this.pressedKeys.delete(key);
  };

  /**
   * 标准化按键名称
   */
  private normalizeKey(event: KeyboardEvent): string {
    if (event.ctrlKey && event.key !== 'Control') return 'Ctrl';
    if (event.shiftKey && event.key !== 'Shift') return 'Shift';
    if (event.altKey && event.key !== 'Alt') return 'Alt';
    if (event.metaKey && event.key !== 'Meta') return 'Meta';
    
    return event.key;
  }

  /**
   * 检查快捷键匹配
   */
  private checkShortcuts(event: KeyboardEvent): void {
    const currentKeys = Array.from(this.pressedKeys).sort();
    
    for (const shortcut of this.shortcuts.values()) {
      if (!shortcut.enabled) continue;
      
      const shortcutKeys = shortcut.keys.slice().sort();
      
      if (this.arraysEqual(currentKeys, shortcutKeys)) {
        // 检查上下文
        if (shortcut.context && !this.isInContext(shortcut.context)) {
          continue;
        }
        
        event.preventDefault();
        event.stopPropagation();
        
        try {
          shortcut.action();
        } catch (error) {
          console.error(`执行快捷键 ${shortcut.id} 时出错:`, error);
        }
        
        break;
      }
    }
  }

  /**
   * 检查数组是否相等
   */
  private arraysEqual(a: string[], b: string[]): boolean {
    return a.length === b.length && a.every((val, i) => val === b[i]);
  }

  /**
   * 检查是否在指定上下文中
   */
  private isInContext(context: string): boolean {
    // 这里可以根据当前活动的编辑器类型来判断上下文
    const activeElement = document.activeElement;
    
    switch (context) {
      case 'script-editor':
        return activeElement?.closest('.script-editor') !== null;
      case 'visual-script-editor':
        return activeElement?.closest('.visual-script-editor') !== null;
      default:
        return true;
    }
  }

  /**
   * 执行动作
   */
  private executeAction(actionId: string): void {
    // 这里可以通过事件系统或回调来执行具体的动作
    console.log(`执行动作: ${actionId}`);
    
    // 触发自定义事件
    const event = new CustomEvent('shortcut-action', {
      detail: { actionId }
    });
    document.dispatchEvent(event);
  }

  /**
   * 加载用户自定义设置
   */
  private loadUserCustomizations(): void {
    try {
      const saved = localStorage.getItem('keyboardShortcuts');
      if (saved) {
        const customizations = JSON.parse(saved);
        // 应用用户自定义设置
        this.applyCustomizations(customizations);
      }
    } catch (error) {
      console.error('加载快捷键自定义设置失败:', error);
    }
  }

  /**
   * 应用自定义设置
   */
  private applyCustomizations(customizations: any): void {
    for (const [shortcutId, config] of Object.entries(customizations)) {
      const shortcut = this.shortcuts.get(shortcutId);
      if (shortcut && config) {
        Object.assign(shortcut, config);
      }
    }
  }

  /**
   * 保存用户自定义设置
   */
  public saveCustomizations(): void {
    try {
      const customizations: any = {};
      
      for (const [id, shortcut] of this.shortcuts.entries()) {
        customizations[id] = {
          keys: shortcut.keys,
          enabled: shortcut.enabled
        };
      }
      
      localStorage.setItem('keyboardShortcuts', JSON.stringify(customizations));
    } catch (error) {
      console.error('保存快捷键自定义设置失败:', error);
    }
  }

  /**
   * 获取所有快捷键
   */
  public getAllShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values());
  }

  /**
   * 获取所有分类
   */
  public getAllCategories(): ShortcutCategory[] {
    return Array.from(this.categories.values());
  }

  /**
   * 获取指定分类的快捷键
   */
  public getShortcutsByCategory(categoryId: string): KeyboardShortcut[] {
    const category = this.categories.get(categoryId);
    return category ? category.shortcuts : [];
  }

  /**
   * 更新快捷键
   */
  public updateShortcut(shortcutId: string, updates: Partial<KeyboardShortcut>): void {
    const shortcut = this.shortcuts.get(shortcutId);
    if (shortcut) {
      Object.assign(shortcut, updates);
      this.saveCustomizations();
    }
  }

  /**
   * 启用/禁用快捷键
   */
  public toggleShortcut(shortcutId: string): void {
    const shortcut = this.shortcuts.get(shortcutId);
    if (shortcut) {
      shortcut.enabled = !shortcut.enabled;
      this.saveCustomizations();
    }
  }

  /**
   * 重置为默认设置
   */
  public resetToDefaults(): void {
    localStorage.removeItem('keyboardShortcuts');
    this.shortcuts.clear();
    this.categories.clear();
    this.initializeDefaultShortcuts();
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.stopListening();
    this.shortcuts.clear();
    this.categories.clear();
  }
}

export default KeyboardShortcutService;
