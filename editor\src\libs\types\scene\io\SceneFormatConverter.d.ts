import { Scene } from '../Scene';
import { SceneSerializer } from '../SceneSerializer';
import { EventEmitter } from '../../utils/EventEmitter';
/**
 * 支持的场景格式
 */
export declare enum SceneFormat {
    /** JSON格式 */
    JSON = "json",
    /** GLTF格式 */
    GLTF = "gltf",
    /** GLB格式 */
    GLB = "glb",
    /** FBX格式 */
    FBX = "fbx",
    /** OBJ格式 */
    OBJ = "obj",
    /** DAE格式（Collada） */
    DAE = "dae"
}
/**
 * 场景导入选项
 */
export interface SceneImportOptions {
    /** 是否合并到现有场景 */
    mergeWithExisting?: boolean;
    /** 是否保留现有实体 */
    keepExistingEntities?: boolean;
    /** 是否导入材质 */
    importMaterials?: boolean;
    /** 是否导入纹理 */
    importTextures?: boolean;
    /** 是否导入动画 */
    importAnimations?: boolean;
    /** 是否导入相机 */
    importCameras?: boolean;
    /** 是否导入灯光 */
    importLights?: boolean;
    /** 是否自动计算法线 */
    computeNormals?: boolean;
    /** 是否自动计算切线 */
    computeTangents?: boolean;
    /** 是否优化几何体 */
    optimizeGeometry?: boolean;
    /** 是否转换为实例化渲染 */
    convertToInstanced?: boolean;
    /** 是否生成LOD级别 */
    generateLOD?: boolean;
    /** 是否应用变换 */
    applyTransforms?: boolean;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 场景导出选项
 */
export interface SceneExportOptions {
    /** 导出格式 */
    format?: SceneFormat;
    /** 是否导出材质 */
    exportMaterials?: boolean;
    /** 是否导出纹理 */
    exportTextures?: boolean;
    /** 是否导出动画 */
    exportAnimations?: boolean;
    /** 是否导出相机 */
    exportCameras?: boolean;
    /** 是否导出灯光 */
    exportLights?: boolean;
    /** 是否二进制格式 */
    binary?: boolean;
    /** 是否压缩 */
    compressed?: boolean;
    /** 是否美化输出 */
    prettyPrint?: boolean;
    /** 是否嵌入资源 */
    embedResources?: boolean;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 场景格式转换器
 */
export declare class SceneFormatConverter extends EventEmitter {
    /** 场景序列化器 */
    private sceneSerializer;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建场景格式转换器
     * @param sceneSerializer 场景序列化器
     */
    constructor(sceneSerializer: SceneSerializer);
    /**
     * 初始化转换器
     */
    initialize(): void;
    /**
     * 导入场景
     * @param data 场景数据
     * @param format 场景格式
     * @param targetScene 目标场景
     * @param options 导入选项
     * @returns Promise，解析为导入的场景
     */
    importScene(data: string | ArrayBuffer, format: SceneFormat, targetScene: Scene, options?: SceneImportOptions): Promise<Scene>;
    /**
     * 导出场景
     * @param scene 场景
     * @param options 导出选项
     * @returns Promise，解析为导出的数据
     */
    exportScene(scene: Scene, options?: SceneExportOptions): Promise<string | ArrayBuffer>;
    /**
     * 从GLTF导入场景
     * @param data 场景数据
     * @param targetScene 目标场景
     * @param options 导入选项
     * @returns Promise，解析为导入的场景
     */
    private importFromGLTF;
    /**
     * 从FBX导入场景
     * @param data 场景数据
     * @param targetScene 目标场景
     * @param options 导入选项
     * @returns Promise，解析为导入的场景
     */
    private importFromFBX;
    /**
     * 从OBJ导入场景
     * @param data 场景数据
     * @param targetScene 目标场景
     * @param options 导入选项
     * @returns Promise，解析为导入的场景
     */
    private importFromOBJ;
    /**
     * 从DAE导入场景
     * @param data 场景数据
     * @param targetScene 目标场景
     * @param options 导入选项
     * @returns Promise，解析为导入的场景
     */
    private importFromDAE;
    /**
     * 从JSON导入场景
     * @param json JSON字符串
     * @param targetScene 目标场景
     * @param options 导入选项
     * @returns 导入的场景
     */
    private importFromJSON;
    /**
     * 导出场景为JSON
     * @param scene 场景
     * @param options 导出选项
     * @returns JSON字符串
     */
    private exportToJSON;
    /**
     * 导出场景为GLTF
     * @param scene 场景
     * @param options 导出选项
     * @returns Promise，解析为导出的数据
     */
    private exportToGLTF;
    /**
     * 导出场景为FBX
     * @param scene 场景
     * @param options 导出选项
     * @returns Promise，解析为导出的数据
     */
    private exportToFBX;
    /**
     * 导出场景为OBJ
     * @param scene 场景
     * @param options 导出选项
     * @returns Promise，解析为导出的数据
     */
    private exportToOBJ;
    /**
     * 导出场景为DAE
     * @param scene 场景
     * @param options 导出选项
     * @returns Promise，解析为导出的数据
     */
    private exportToDAE;
    /**
     * 处理GLTF场景
     * @param gltf GLTF数据
     * @param targetScene 目标场景
     * @param options 导入选项
     */
    private processGLTFScene;
    /**
     * 处理FBX场景
     * @param fbx FBX数据
     * @param targetScene 目标场景
     * @param options 导入选项
     */
    private processFBXScene;
    /**
     * 处理OBJ场景
     * @param obj OBJ数据
     * @param targetScene 目标场景
     * @param options 导入选项
     */
    private processOBJScene;
}
