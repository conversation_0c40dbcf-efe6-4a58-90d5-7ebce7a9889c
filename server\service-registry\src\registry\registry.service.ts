/**
 * 服务注册服务
 */
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { ServiceEntity } from './entities/service.entity';
import { ServiceInstanceEntity } from './entities/service-instance.entity';
import { RegisterServiceInstanceDto } from './dto/register-service.dto';
import { HeartbeatDto } from './dto/heartbeat.dto';
import { DiscoverServiceDto } from './dto/discover-service.dto';

@Injectable()
export class RegistryService {
  private readonly logger = new Logger(RegistryService.name);

  constructor(
    @InjectRepository(ServiceEntity)
    private readonly serviceRepository: Repository<ServiceEntity>,
    @InjectRepository(ServiceInstanceEntity)
    private readonly instanceRepository: Repository<ServiceInstanceEntity>,
  ) {}

  /**
   * 注册服务实例
   */
  async registerService(dto: RegisterServiceInstanceDto): Promise<ServiceInstanceEntity> {
    this.logger.log(`注册服务: ${dto.name}, 实例ID: ${dto.instanceId}`);
    
    // 查找或创建服务
    let service = await this.serviceRepository.findOne({ where: { name: dto.name } });
    if (!service) {
      service = this.serviceRepository.create({
        name: dto.name,
        description: dto.description,
      });
      await this.serviceRepository.save(service);
    }
    
    // 查找或创建实例
    let instance = await this.instanceRepository.findOne({
      where: {
        serviceId: service.id,
        instanceId: dto.instanceId,
      },
    });
    
    if (instance) {
      // 更新实例
      instance.host = dto.host;
      instance.port = dto.port;
      instance.httpPort = dto.httpPort;
      instance.metadata = dto.metadata;
      instance.lastHeartbeat = new Date();
      instance.isHealthy = true;
    } else {
      // 创建新实例
      instance = this.instanceRepository.create({
        serviceId: service.id,
        instanceId: dto.instanceId,
        host: dto.host,
        port: dto.port,
        httpPort: dto.httpPort,
        metadata: dto.metadata,
        lastHeartbeat: new Date(),
        isHealthy: true,
      });
    }
    
    await this.instanceRepository.save(instance);
    return instance;
  }

  /**
   * 处理服务心跳
   */
  async heartbeat(dto: HeartbeatDto): Promise<void> {
    this.logger.debug(`接收心跳: ${dto.name}, 实例ID: ${dto.instanceId}`);
    
    const service = await this.serviceRepository.findOne({ where: { name: dto.name } });
    if (!service) {
      throw new NotFoundException(`服务 ${dto.name} 未注册`);
    }
    
    const instance = await this.instanceRepository.findOne({
      where: {
        serviceId: service.id,
        instanceId: dto.instanceId,
      },
    });
    
    if (!instance) {
      throw new NotFoundException(`服务实例 ${dto.instanceId} 未注册`);
    }
    
    // 更新心跳时间和状态
    instance.lastHeartbeat = new Date();
    instance.isHealthy = true;
    if (dto.status) {
      instance.metadata = { ...instance.metadata, status: dto.status };
    }
    
    await this.instanceRepository.save(instance);
  }

  /**
   * 发现服务实例
   */
  async discoverService(dto: DiscoverServiceDto): Promise<ServiceInstanceEntity[]> {
    const service = await this.serviceRepository.findOne({ 
      where: { name: dto.name },
      relations: ['instances'],
    });
    
    if (!service) {
      throw new NotFoundException(`服务 ${dto.name} 未注册`);
    }
    
    // 获取所有实例
    let instances = await this.instanceRepository.find({
      where: { serviceId: service.id },
    });
    
    // 过滤健康实例
    if (dto.onlyHealthy) {
      instances = instances.filter(instance => instance.isHealthy);
    }
    
    return instances;
  }

  /**
   * 获取所有服务
   */
  async getAllServices(): Promise<ServiceEntity[]> {
    return this.serviceRepository.find({
      relations: ['instances'],
    });
  }

  /**
   * 获取服务详情
   */
  async getServiceDetails(name: string): Promise<ServiceEntity> {
    const service = await this.serviceRepository.findOne({
      where: { name },
      relations: ['instances'],
    });
    
    if (!service) {
      throw new NotFoundException(`服务 ${name} 未注册`);
    }
    
    return service;
  }

  /**
   * 注销服务实例
   */
  async deregisterInstance(name: string, instanceId: string): Promise<void> {
    this.logger.log(`注销服务实例: ${name}, 实例ID: ${instanceId}`);
    
    const service = await this.serviceRepository.findOne({ where: { name } });
    if (!service) {
      throw new NotFoundException(`服务 ${name} 未注册`);
    }
    
    const instance = await this.instanceRepository.findOne({
      where: {
        serviceId: service.id,
        instanceId,
      },
    });
    
    if (!instance) {
      throw new NotFoundException(`服务实例 ${instanceId} 未注册`);
    }
    
    await this.instanceRepository.remove(instance);
  }

  /**
   * 定时检查服务实例健康状态
   * 每30秒执行一次
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async checkInstancesHealth() {
    this.logger.debug('检查服务实例健康状态');
    
    const instances = await this.instanceRepository.find();
    const now = new Date();
    const unhealthyInstances = [];
    
    for (const instance of instances) {
      // 如果超过60秒没有心跳，标记为不健康
      if (instance.lastHeartbeat && now.getTime() - instance.lastHeartbeat.getTime() > 60000) {
        instance.isHealthy = false;
        unhealthyInstances.push(instance);
      }
    }
    
    if (unhealthyInstances.length > 0) {
      this.logger.warn(`发现 ${unhealthyInstances.length} 个不健康的服务实例`);
      await this.instanceRepository.save(unhealthyInstances);
    }
  }

  /**
   * 清理不健康的服务实例
   * 每5分钟执行一次
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async cleanupUnhealthyInstances() {
    this.logger.debug('清理不健康的服务实例');
    
    const instances = await this.instanceRepository.find({ where: { isHealthy: false } });
    const now = new Date();
    const instancesToRemove = [];
    
    for (const instance of instances) {
      // 如果超过5分钟没有心跳，删除实例
      if (instance.lastHeartbeat && now.getTime() - instance.lastHeartbeat.getTime() > 300000) {
        instancesToRemove.push(instance);
      }
    }
    
    if (instancesToRemove.length > 0) {
      this.logger.warn(`清理 ${instancesToRemove.length} 个不健康的服务实例`);
      await this.instanceRepository.remove(instancesToRemove);
    }
  }
}
