import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { MicroserviceClientService } from './microservice-client.service';
import { EventBusService } from '../event-bus/event-bus.service';
import { CircuitBreakerService } from '../circuit-breaker/circuit-breaker.service';
import { DistributedLockService } from '../distributed-lock/distributed-lock.service';
import { CacheService } from '../cache/cache.service';

@Global()
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
  ],
  providers: [
    MicroserviceClientService,
    EventBusService,
    CircuitBreakerService,
    DistributedLockService,
    CacheService,
  ],
  exports: [
    MicroserviceClientService,
    EventBusService,
    CircuitBreakerService,
    DistributedLockService,
    CacheService,
  ],
})
export class MicroserviceCommunicationModule {}
