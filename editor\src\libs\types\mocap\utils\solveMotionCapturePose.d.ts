import type { Entity } from '../../core/Entity';
import { LandmarkData, WorldLandmarkData } from '../types/LandmarkData';
/**
 * 解算动作捕捉姿势
 * @param entity 实体
 * @param worldLandmarks 世界坐标系关键点数据
 * @param screenLandmarks 屏幕坐标系关键点数据
 * @param smoothingFactor 平滑系数
 * @param visibilityThreshold 可见度阈值
 */
export declare function solveMotionCapturePose(entity: Entity, worldLandmarks: WorldLandmarkData[], screenLandmarks: LandmarkData[], smoothingFactor?: number, visibilityThreshold?: number): void;
