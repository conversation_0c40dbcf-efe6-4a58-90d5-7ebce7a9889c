/**
 * LOD过渡控制器
 * 用于实现LOD级别之间的平滑过渡
 */
import * as THREE from 'three';
import { LODLevel } from './LODComponent';
import { Debug } from '../../utils/Debug';

/**
 * 过渡类型枚举
 */
export enum TransitionType {
  /** 透明度过渡 */
  OPACITY = 'opacity',
  /** 顶点混合过渡 */
  VERTEX_BLEND = 'vertex_blend',
  /** 交叉淡入淡出过渡 */
  CROSS_FADE = 'cross_fade',
  /** 形态过渡 */
  MORPH = 'morph'
}

/**
 * LOD过渡控制器配置接口
 */
export interface LODTransitionControllerOptions {
  /** 过渡持续时间（毫秒） */
  duration?: number;
  /** 过渡类型 */
  type?: TransitionType;
  /** 是否使用缓动 */
  useEasing?: boolean;
  /** 缓动函数 */
  easingFunction?: (t: number) => number;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 过渡状态接口
 */
export interface TransitionState {
  /** 起始网格 */
  fromMesh: THREE.Mesh;
  /** 目标网格 */
  toMesh: THREE.Mesh;
  /** 起始级别 */
  fromLevel: LODLevel;
  /** 目标级别 */
  toLevel: LODLevel;
  /** 开始时间 */
  startTime: number;
  /** 持续时间 */
  duration: number;
  /** 过渡类型 */
  type: TransitionType;
  /** 过渡进度（0-1） */
  progress: number;
  /** 是否完成 */
  completed: boolean;
  /** 完成回调 */
  onComplete?: () => void;
  /** 过渡网格 */
  transitionMesh?: THREE.Mesh;
}

/**
 * LOD过渡控制器类
 */
export class LODTransitionController {
  /** 默认过渡持续时间（毫秒） */
  private static readonly DEFAULT_DURATION: number = 500;

  /** 过渡持续时间（毫秒） */
  private duration: number;

  /** 过渡类型 */
  private type: TransitionType;

  /** 是否使用缓动 */
  private useEasing: boolean;

  /** 缓动函数 */
  private easingFunction: (t: number) => number;

  /** 是否启用调试 */
  private debug: boolean;

  /** 活跃过渡列表 */
  private activeTransitions: Map<THREE.Object3D, TransitionState> = new Map();

  /** 是否支持顶点混合 */
  private supportsVertexBlending: boolean;

  /** 是否支持形态目标 */
  private supportsMorphTargets: boolean;

  /**
   * 创建LOD过渡控制器
   * @param options LOD过渡控制器配置
   */
  constructor(options: LODTransitionControllerOptions = {}) {
    this.duration = options.duration !== undefined ? options.duration : LODTransitionController.DEFAULT_DURATION;
    this.type = options.type !== undefined ? options.type : TransitionType.OPACITY;
    this.useEasing = options.useEasing !== undefined ? options.useEasing : true;
    this.easingFunction = options.easingFunction || this.defaultEasingFunction;
    this.debug = options.debug !== undefined ? options.debug : false;

    // 检查是否支持顶点混合和形态目标
    this.supportsVertexBlending = this.checkVertexBlendingSupport();
    this.supportsMorphTargets = this.checkMorphTargetsSupport();

    // 如果选择的过渡类型不受支持，则回退到透明度过渡
    if ((this.type === TransitionType.VERTEX_BLEND && !this.supportsVertexBlending) ||
        (this.type === TransitionType.MORPH && !this.supportsMorphTargets)) {
      if (this.debug) {
        Debug.warn('LODTransitionController', `过渡类型 ${this.type} 不受支持，回退到透明度过渡`);
      }
      this.type = TransitionType.OPACITY;
    }
  }

  /**
   * 检查是否支持顶点混合
   * @returns 是否支持顶点混合
   */
  private checkVertexBlendingSupport(): boolean {
    try {
      // 检查是否支持WebGL2
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2');
      return !!gl;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查是否支持形态目标
   * @returns 是否支持形态目标
   */
  private checkMorphTargetsSupport(): boolean {
    return true; // Three.js支持形态目标
  }

  /**
   * 默认缓动函数（缓入缓出）
   * @param t 时间（0-1）
   * @returns 缓动值（0-1）
   */
  private defaultEasingFunction(t: number): number {
    // 缓入缓出
    return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
  }

  /**
   * 开始LOD级别过渡
   * @param parent 父对象
   * @param fromMesh 起始网格
   * @param toMesh 目标网格
   * @param fromLevel 起始级别
   * @param toLevel 目标级别
   * @param onComplete 完成回调
   * @returns 是否成功开始过渡
   */
  public startTransition(
    parent: THREE.Object3D,
    fromMesh: THREE.Mesh,
    toMesh: THREE.Mesh,
    fromLevel: LODLevel,
    toLevel: LODLevel,
    onComplete?: () => void
  ): boolean {
    // 如果已经有活跃过渡，则取消它
    if (this.activeTransitions.has(parent)) {
      this.cancelTransition(parent);
    }

    // 创建过渡状态
    const transitionState: TransitionState = {
      fromMesh,
      toMesh,
      fromLevel,
      toLevel,
      startTime: performance.now(),
      duration: this.duration,
      type: this.type,
      progress: 0,
      completed: false,
      onComplete
    };

    // 根据过渡类型初始化过渡
    switch (this.type) {
      case TransitionType.OPACITY:
        this.initializeOpacityTransition(parent, transitionState);
        break;
      case TransitionType.VERTEX_BLEND:
        this.initializeVertexBlendTransition(parent, transitionState);
        break;
      case TransitionType.CROSS_FADE:
        this.initializeCrossFadeTransition(parent, transitionState);
        break;
      case TransitionType.MORPH:
        this.initializeMorphTransition(parent, transitionState);
        break;
    }

    // 添加到活跃过渡列表
    this.activeTransitions.set(parent, transitionState);

    return true;
  }

  /**
   * 初始化透明度过渡
   * @param _parent 父对象
   * @param state 过渡状态
   */
  private initializeOpacityTransition(_parent: THREE.Object3D, state: TransitionState): void {
    // 确保材质支持透明度
    this.ensureMaterialTransparency(state.fromMesh);
    this.ensureMaterialTransparency(state.toMesh);

    // 设置初始透明度
    this.setMeshOpacity(state.fromMesh, 1.0);
    this.setMeshOpacity(state.toMesh, 0.0);

    // 确保两个网格都可见
    state.fromMesh.visible = true;
    state.toMesh.visible = true;
  }

  /**
   * 确保材质支持透明度
   * @param mesh 网格
   */
  private ensureMaterialTransparency(mesh: THREE.Mesh): void {
    if (Array.isArray(mesh.material)) {
      for (const material of mesh.material) {
        material.transparent = true;
        material.needsUpdate = true;
      }
    } else if (mesh.material) {
      mesh.material.transparent = true;
      mesh.material.needsUpdate = true;
    }
  }

  /**
   * 设置网格透明度
   * @param mesh 网格
   * @param opacity 透明度（0-1）
   */
  private setMeshOpacity(mesh: THREE.Mesh, opacity: number): void {
    if (Array.isArray(mesh.material)) {
      for (const material of mesh.material) {
        material.opacity = opacity;
      }
    } else if (mesh.material) {
      mesh.material.opacity = opacity;
    }
  }
  /**
   * 初始化顶点混合过渡
   * @param parent 父对象
   * @param state 过渡状态
   */
  private initializeVertexBlendTransition(parent: THREE.Object3D, state: TransitionState): void {
    // 创建顶点混合着色器材质
    const material = this.createVertexBlendMaterial(state.fromMesh, state.toMesh);

    // 创建过渡网格
    const geometry = this.createVertexBlendGeometry(state.fromMesh.geometry, state.toMesh.geometry);
    const transitionMesh = new THREE.Mesh(geometry, material);

    // 复制网格属性
    transitionMesh.name = `${state.fromMesh.name}_to_${state.toMesh.name}`;
    transitionMesh.castShadow = state.fromMesh.castShadow;
    transitionMesh.receiveShadow = state.fromMesh.receiveShadow;

    // 添加到父对象
    parent.add(transitionMesh);

    // 隐藏原始网格
    state.fromMesh.visible = false;
    state.toMesh.visible = false;

    // 保存过渡网格
    state.transitionMesh = transitionMesh;
  }

  /**
   * 创建顶点混合几何体
   * @param fromGeometry 起始几何体
   * @param toGeometry 目标几何体
   * @returns 混合几何体
   */
  private createVertexBlendGeometry(fromGeometry: THREE.BufferGeometry, toGeometry: THREE.BufferGeometry): THREE.BufferGeometry {
    // 创建新几何体
    const geometry = new THREE.BufferGeometry();

    // 复制起始几何体的属性
    for (const name in fromGeometry.attributes) {
      geometry.setAttribute(name, fromGeometry.attributes[name].clone());
    }

    // 添加目标几何体的位置属性
    if (toGeometry.attributes.position) {
      geometry.setAttribute('targetPosition', toGeometry.attributes.position.clone());
    }

    // 添加目标几何体的法线属性
    if (toGeometry.attributes.normal) {
      geometry.setAttribute('targetNormal', toGeometry.attributes.normal.clone());
    }

    // 复制索引
    if (fromGeometry.index) {
      geometry.setIndex(fromGeometry.index.clone());
    }

    return geometry;
  }

  /**
   * 创建顶点混合材质
   * @param fromMesh 起始网格
   * @param toMesh 目标网格
   * @returns 混合材质
   */
  private createVertexBlendMaterial(fromMesh: THREE.Mesh, toMesh: THREE.Mesh): THREE.ShaderMaterial {
    // 获取材质
    const fromMaterial = Array.isArray(fromMesh.material) ? fromMesh.material[0] : fromMesh.material;
    const toMaterial = Array.isArray(toMesh.material) ? toMesh.material[0] : toMesh.material;

    // 创建着色器材质
    const material = new THREE.ShaderMaterial({
      uniforms: {
        blendFactor: { value: 0.0 },
        fromMap: { value: (fromMaterial as THREE.MeshStandardMaterial).map },
        toMap: { value: (toMaterial as THREE.MeshStandardMaterial).map },
        fromColor: { value: (fromMaterial as THREE.MeshStandardMaterial).color },
        toColor: { value: (toMaterial as THREE.MeshStandardMaterial).color }
      },
      vertexShader: `
        attribute vec3 targetPosition;
        attribute vec3 targetNormal;
        uniform float blendFactor;

        varying vec2 vUv;

        void main() {
          // 混合位置
          vec3 blendedPosition = mix(position, targetPosition, blendFactor);

          // 混合法线
          vec3 blendedNormal = mix(normal, targetNormal, blendFactor);

          // 设置UV
          vUv = uv;

          // 设置位置
          gl_Position = projectionMatrix * modelViewMatrix * vec4(blendedPosition, 1.0);
        }
      `,
      fragmentShader: `
        uniform sampler2D fromMap;
        uniform sampler2D toMap;
        uniform vec3 fromColor;
        uniform vec3 toColor;
        uniform float blendFactor;

        varying vec2 vUv;

        void main() {
          // 采样纹理
          vec4 fromColor = texture2D(fromMap, vUv);
          vec4 toColor = texture2D(toMap, vUv);

          // 混合颜色
          vec4 color = mix(fromColor, toColor, blendFactor);

          gl_FragColor = color;
        }
      `,
      transparent: true
    });

    return material;
  }

  /**
   * 初始化交叉淡入淡出过渡
   * @param _parent 父对象
   * @param state 过渡状态
   */
  private initializeCrossFadeTransition(_parent: THREE.Object3D, state: TransitionState): void {
    // 确保材质支持透明度
    this.ensureMaterialTransparency(state.fromMesh);
    this.ensureMaterialTransparency(state.toMesh);

    // 设置初始透明度
    this.setMeshOpacity(state.fromMesh, 1.0);
    this.setMeshOpacity(state.toMesh, 0.0);

    // 确保两个网格都可见
    state.fromMesh.visible = true;
    state.toMesh.visible = true;

    // 设置渲染顺序，确保正确的混合
    state.fromMesh.renderOrder = 0;
    state.toMesh.renderOrder = 1;
  }

  /**
   * 初始化形态过渡
   * @param parent 父对象
   * @param state 过渡状态
   */
  private initializeMorphTransition(parent: THREE.Object3D, state: TransitionState): void {
    // 创建形态目标
    const morphGeometry = this.createMorphGeometry(state.fromMesh.geometry, state.toMesh.geometry);

    // 创建形态网格
    const material = (Array.isArray(state.fromMesh.material) ? state.fromMesh.material[0] : state.fromMesh.material).clone();
    const morphMesh = new THREE.Mesh(morphGeometry, material);

    // 启用形态目标
    morphMesh.morphTargetInfluences = [0.0];

    // 复制网格属性
    morphMesh.name = `${state.fromMesh.name}_morph`;
    morphMesh.castShadow = state.fromMesh.castShadow;
    morphMesh.receiveShadow = state.fromMesh.receiveShadow;

    // 添加到父对象
    parent.add(morphMesh);

    // 隐藏原始网格
    state.fromMesh.visible = false;
    state.toMesh.visible = false;

    // 保存过渡网格
    state.transitionMesh = morphMesh;
  }

  /**
   * 创建形态几何体
   * @param fromGeometry 起始几何体
   * @param toGeometry 目标几何体
   * @returns 形态几何体
   */
  private createMorphGeometry(fromGeometry: THREE.BufferGeometry, toGeometry: THREE.BufferGeometry): THREE.BufferGeometry {
    // 克隆起始几何体
    const geometry = fromGeometry.clone();

    // 添加形态目标
    const positions = toGeometry.attributes.position;
    geometry.morphAttributes.position = [positions];

    // 添加法线形态目标
    if (toGeometry.attributes.normal) {
      const normals = toGeometry.attributes.normal;
      geometry.morphAttributes.normal = [normals];
    }

    return geometry;
  }

  /**
   * 更新过渡
   * @param _deltaTime 时间增量（秒）
   */
  public update(_deltaTime: number): void {
    const currentTime = performance.now();

    // 更新所有活跃过渡
    for (const [parent, state] of this.activeTransitions.entries()) {
      // 如果已完成，则跳过
      if (state.completed) {
        continue;
      }

      // 计算过渡进度
      const elapsed = currentTime - state.startTime;
      let progress = Math.min(1.0, elapsed / state.duration);

      // 应用缓动
      if (this.useEasing) {
        progress = this.easingFunction(progress);
      }

      // 更新过渡状态
      state.progress = progress;

      // 根据过渡类型更新过渡
      switch (state.type) {
        case TransitionType.OPACITY:
          this.updateOpacityTransition(state);
          break;
        case TransitionType.VERTEX_BLEND:
          this.updateVertexBlendTransition(state);
          break;
        case TransitionType.CROSS_FADE:
          this.updateCrossFadeTransition(state);
          break;
        case TransitionType.MORPH:
          this.updateMorphTransition(state);
          break;
      }

      // 检查是否完成
      if (progress >= 1.0) {
        this.completeTransition(parent, state);
      }
    }
  }

  /**
   * 更新透明度过渡
   * @param state 过渡状态
   */
  private updateOpacityTransition(state: TransitionState): void {
    // 更新透明度
    this.setMeshOpacity(state.fromMesh, 1.0 - state.progress);
    this.setMeshOpacity(state.toMesh, state.progress);
  }

  /**
   * 更新顶点混合过渡
   * @param state 过渡状态
   */
  private updateVertexBlendTransition(state: TransitionState): void {
    if (!state.transitionMesh) {
      return;
    }

    // 更新混合因子
    const material = state.transitionMesh.material as THREE.ShaderMaterial;
    material.uniforms.blendFactor.value = state.progress;
  }

  /**
   * 更新交叉淡入淡出过渡
   * @param state 过渡状态
   */
  private updateCrossFadeTransition(state: TransitionState): void {
    // 更新透明度
    this.setMeshOpacity(state.fromMesh, 1.0 - state.progress);
    this.setMeshOpacity(state.toMesh, state.progress);
  }

  /**
   * 更新形态过渡
   * @param state 过渡状态
   */
  private updateMorphTransition(state: TransitionState): void {
    if (!state.transitionMesh) {
      return;
    }

    // 更新形态目标影响
    state.transitionMesh.morphTargetInfluences![0] = state.progress;
  }

  /**
   * 完成过渡
   * @param parent 父对象
   * @param state 过渡状态
   */
  private completeTransition(parent: THREE.Object3D, state: TransitionState): void {
    // 标记为已完成
    state.completed = true;

    // 根据过渡类型完成过渡
    switch (state.type) {
      case TransitionType.OPACITY:
      case TransitionType.CROSS_FADE:
        // 隐藏起始网格
        state.fromMesh.visible = false;
        // 确保目标网格可见且不透明
        state.toMesh.visible = true;
        this.setMeshOpacity(state.toMesh, 1.0);
        break;
      case TransitionType.VERTEX_BLEND:
      case TransitionType.MORPH:
        // 移除过渡网格
        if (state.transitionMesh) {
          parent.remove(state.transitionMesh);
        }
        // 显示目标网格
        state.toMesh.visible = true;
        break;
    }

    // 调用完成回调
    if (state.onComplete) {
      state.onComplete();
    }

    // 从活跃过渡列表中移除
    this.activeTransitions.delete(parent);
  }

  /**
   * 取消过渡
   * @param parent 父对象
   */
  public cancelTransition(parent: THREE.Object3D): void {
    const state = this.activeTransitions.get(parent);
    if (!state) {
      return;
    }

    // 根据过渡类型取消过渡
    switch (state.type) {
      case TransitionType.OPACITY:
      case TransitionType.CROSS_FADE:
        // 恢复网格可见性和透明度
        state.fromMesh.visible = false;
        state.toMesh.visible = true;
        this.setMeshOpacity(state.fromMesh, 1.0);
        this.setMeshOpacity(state.toMesh, 1.0);
        break;
      case TransitionType.VERTEX_BLEND:
      case TransitionType.MORPH:
        // 移除过渡网格
        if (state.transitionMesh) {
          parent.remove(state.transitionMesh);
        }
        // 显示目标网格
        state.toMesh.visible = true;
        break;
    }

    // 从活跃过渡列表中移除
    this.activeTransitions.delete(parent);
  }

  /**
   * 获取活跃过渡数量
   * @returns 活跃过渡数量
   */
  public getActiveTransitionCount(): number {
    return this.activeTransitions.size;
  }

  /**
   * 清除所有过渡
   */
  public clearAllTransitions(): void {
    for (const parent of this.activeTransitions.keys()) {
      this.cancelTransition(parent);
    }
  }
}