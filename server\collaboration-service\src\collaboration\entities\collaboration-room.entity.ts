/**
 * 协作房间实体
 */
import { WebSocket } from 'ws';
import { CollaborationUser, Operation, UserInterestArea } from '../dto/message.dto';

/**
 * 房间分区接口
 */
export interface RoomPartition {
  /** 分区ID */
  id: string;

  /** 分区名称 */
  name: string;

  /** 分区类型 */
  type: 'area' | 'component' | 'custom';

  /** 分区边界 */
  bounds?: {
    minX: number;
    minY: number;
    minZ: number;
    maxX: number;
    maxY: number;
    maxZ: number;
  };

  /** 分区用户 */
  users: Set<string>;

  /** 分区操作历史 */
  operations: Operation[];

  /** 分区元数据 */
  metadata: Record<string, any>;

  /** 创建时间 */
  createdAt: Date;

  /** 最后活动时间 */
  lastActivityAt: Date;
}

/**
 * 房间资源使用统计
 */
export interface RoomResourceStats {
  /** 内存使用（字节） */
  memoryUsage: number;

  /** CPU使用（百分比） */
  cpuUsage: number;

  /** 网络带宽使用（字节/秒） */
  networkUsage: number;

  /** 操作频率（操作/秒） */
  operationRate: number;

  /** 消息队列长度 */
  messageQueueLength: number;

  /** 最后更新时间 */
  lastUpdated: Date;
}

/**
 * 协作房间配置
 */
export interface CollaborationRoomConfig {
  /** 最大操作历史数量 */
  maxOperationHistory: number;

  /** 最大用户数量 */
  maxUsers: number;

  /** 是否启用分区 */
  enablePartitioning: boolean;

  /** 是否启用资源监控 */
  enableResourceMonitoring: boolean;

  /** 资源监控间隔（毫秒） */
  resourceMonitoringInterval: number;

  /** 是否启用操作压缩 */
  enableOperationCompression: boolean;

  /** 是否启用操作批处理 */
  enableOperationBatching: boolean;

  /** 是否启用增量同步 */
  enableIncrementalSync: boolean;

  /** 是否启用自动清理 */
  enableAutoCleanup: boolean;

  /** 自动清理间隔（毫秒） */
  autoCleanupInterval: number;

  /** 不活跃超时（毫秒） */
  inactivityTimeout: number;
}

/**
 * 协作房间实体
 */
export class CollaborationRoom {
  /** 项目ID */
  projectId: string;

  /** 场景ID */
  sceneId: string;

  /** 创建时间 */
  createdAt: Date;

  /** 最后活动时间 */
  lastActivityAt: Date;

  /** 用户映射表 */
  users: Map<string, CollaborationUser>;

  /** 客户端连接映射表 */
  clients: Map<string, WebSocket>;

  /** 操作历史 */
  operations: Operation[];

  /** 房间配置 */
  config: CollaborationRoomConfig;

  /** 房间分区 */
  partitions: Map<string, RoomPartition>;

  /** 用户兴趣区域 */
  userInterestAreas: Map<string, UserInterestArea>;

  /** 资源使用统计 */
  resourceStats: RoomResourceStats;

  /** 资源监控定时器 */
  private resourceMonitorTimer: NodeJS.Timeout | null = null;

  /** 自动清理定时器 */
  private autoCleanupTimer: NodeJS.Timeout | null = null;

  /** 操作计数器（用于计算操作频率） */
  private operationCounter: number = 0;

  /** 上次操作计数时间 */
  private lastOperationCountTime: Date = new Date();

  /**
   * 构造函数
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @param config 房间配置
   */
  constructor(
    projectId: string,
    sceneId: string,
    config?: Partial<CollaborationRoomConfig>
  ) {
    this.projectId = projectId;
    this.sceneId = sceneId;
    this.createdAt = new Date();
    this.lastActivityAt = new Date();
    this.users = new Map<string, CollaborationUser>();
    this.clients = new Map<string, WebSocket>();
    this.operations = [];
    this.partitions = new Map<string, RoomPartition>();
    this.userInterestAreas = new Map<string, UserInterestArea>();

    // 默认配置
    this.config = {
      maxOperationHistory: 1000,
      maxUsers: 50,
      enablePartitioning: true,
      enableResourceMonitoring: true,
      resourceMonitoringInterval: 30000, // 30秒
      enableOperationCompression: true,
      enableOperationBatching: true,
      enableIncrementalSync: true,
      enableAutoCleanup: true,
      autoCleanupInterval: 300000, // 5分钟
      inactivityTimeout: 3600000, // 1小时
      ...config
    };

    // 初始化资源统计
    this.resourceStats = {
      memoryUsage: 0,
      cpuUsage: 0,
      networkUsage: 0,
      operationRate: 0,
      messageQueueLength: 0,
      lastUpdated: new Date()
    };

    // 启动资源监控
    if (this.config.enableResourceMonitoring) {
      this.startResourceMonitoring();
    }

    // 启动自动清理
    if (this.config.enableAutoCleanup) {
      this.startAutoCleanup();
    }
  }

  /**
   * 添加用户
   * @param user 用户
   * @param client WebSocket客户端
   */
  addUser(user: CollaborationUser, client: WebSocket): void {
    this.users.set(user.id, user);
    this.clients.set(user.id, client);
    this.updateLastActivity();
  }

  /**
   * 移除用户
   * @param userId 用户ID
   */
  removeUser(userId: string): void {
    this.users.delete(userId);
    this.clients.delete(userId);
    this.updateLastActivity();
  }

  /**
   * 获取用户
   * @param userId 用户ID
   * @returns 用户
   */
  getUser(userId: string): CollaborationUser | undefined {
    return this.users.get(userId);
  }

  /**
   * 获取所有用户
   * @returns 用户数组
   */
  getAllUsers(): CollaborationUser[] {
    return Array.from(this.users.values());
  }

  /**
   * 获取客户端
   * @param userId 用户ID
   * @returns WebSocket客户端
   */
  getClient(userId: string): WebSocket | undefined {
    return this.clients.get(userId);
  }

  /**
   * 获取所有客户端
   * @returns WebSocket客户端数组
   */
  getAllClients(): WebSocket[] {
    return Array.from(this.clients.values());
  }

  /**
   * 添加操作
   * @param operation 操作
   */
  addOperation(operation: Operation): void {
    this.operations.push(operation);
    this.operationCounter++;

    // 限制操作历史大小
    if (this.operations.length > this.config.maxOperationHistory) {
      this.operations = this.operations.slice(this.operations.length - this.config.maxOperationHistory);
    }

    // 如果启用了分区，将操作添加到相应分区
    if (this.config.enablePartitioning) {
      this.addOperationToPartitions(operation);
    }

    this.updateLastActivity();
  }

  /**
   * 获取操作历史
   * @param options 选项
   * @returns 操作历史数组
   */
  getOperationHistory(options?: {
    limit?: number;
    offset?: number;
    userId?: string;
    types?: string[];
    startTime?: Date;
    endTime?: Date;
    partitionId?: string;
  }): Operation[] {
    // 如果没有选项，返回所有操作历史
    if (!options) {
      return [...this.operations];
    }

    // 如果指定了分区，返回分区操作历史
    if (options.partitionId) {
      const partition = this.partitions.get(options.partitionId);
      if (partition) {
        return this.filterOperations(partition.operations, options);
      }
      return [];
    }

    // 否则返回过滤后的操作历史
    return this.filterOperations(this.operations, options);
  }

  /**
   * 过滤操作
   * @param operations 操作数组
   * @param options 过滤选项
   * @returns 过滤后的操作数组
   */
  private filterOperations(operations: Operation[], options: {
    limit?: number;
    offset?: number;
    userId?: string;
    types?: string[];
    startTime?: Date;
    endTime?: Date;
  }): Operation[] {
    let result = [...operations];

    // 按用户ID过滤
    if (options.userId) {
      result = result.filter(op => op.userId === options.userId);
    }

    // 按操作类型过滤
    if (options.types && options.types.length > 0) {
      result = result.filter(op => options.types!.includes(op.type));
    }

    // 按时间范围过滤
    if (options.startTime) {
      const startTime = options.startTime.getTime();
      result = result.filter(op => op.timestamp >= startTime);
    }

    if (options.endTime) {
      const endTime = options.endTime.getTime();
      result = result.filter(op => op.timestamp <= endTime);
    }

    // 应用分页
    if (options.offset !== undefined || options.limit !== undefined) {
      const offset = options.offset || 0;
      const limit = options.limit || result.length;
      result = result.slice(offset, offset + limit);
    }

    return result;
  }

  /**
   * 更新最后活动时间
   */
  updateLastActivity(): void {
    this.lastActivityAt = new Date();
  }

  /**
   * 获取用户数量
   * @returns 用户数量
   */
  getUserCount(): number {
    return this.users.size;
  }

  /**
   * 检查是否为空房间
   * @returns 是否为空
   */
  isEmpty(): boolean {
    return this.users.size === 0;
  }

  /**
   * 更新用户状态
   * @param userId 用户ID
   * @param updates 更新数据
   */
  updateUserStatus(userId: string, updates: Partial<CollaborationUser>): void {
    const user = this.users.get(userId);

    if (user) {
      Object.assign(user, updates);
      this.updateLastActivity();

      // 如果更新包含兴趣区域，更新用户分区
      if (updates.interestArea) {
        this.updateUserInterestArea(userId, updates.interestArea);
      }
    }
  }

  /**
   * 更新用户兴趣区域
   * @param userId 用户ID
   * @param interestArea 兴趣区域
   */
  updateUserInterestArea(userId: string, interestArea: UserInterestArea): void {
    // 保存用户兴趣区域
    this.userInterestAreas.set(userId, interestArea);

    // 如果启用了分区，更新用户分区
    if (this.config.enablePartitioning) {
      this.updateUserPartitions(userId, interestArea);
    }
  }

  /**
   * 创建分区
   * @param partitionData 分区数据
   * @returns 分区
   */
  createPartition(partitionData: {
    id?: string;
    name: string;
    type: 'area' | 'component' | 'custom';
    bounds?: {
      minX: number;
      minY: number;
      minZ: number;
      maxX: number;
      maxY: number;
      maxZ: number;
    };
    metadata?: Record<string, any>;
  }): RoomPartition {
    const id = partitionData.id || this.generateId();

    // 创建分区
    const partition: RoomPartition = {
      id,
      name: partitionData.name,
      type: partitionData.type,
      bounds: partitionData.bounds,
      users: new Set<string>(),
      operations: [],
      metadata: partitionData.metadata || {},
      createdAt: new Date(),
      lastActivityAt: new Date()
    };

    // 添加到分区映射表
    this.partitions.set(id, partition);

    // 如果是区域分区，将相关用户添加到分区
    if (partition.type === 'area' && partition.bounds) {
      this.assignUsersToAreaPartition(partition);
    }

    return partition;
  }

  /**
   * 获取分区
   * @param partitionId 分区ID
   * @returns 分区
   */
  getPartition(partitionId: string): RoomPartition | undefined {
    return this.partitions.get(partitionId);
  }

  /**
   * 获取所有分区
   * @returns 分区数组
   */
  getAllPartitions(): RoomPartition[] {
    return Array.from(this.partitions.values());
  }

  /**
   * 删除分区
   * @param partitionId 分区ID
   * @returns 是否成功
   */
  deletePartition(partitionId: string): boolean {
    return this.partitions.delete(partitionId);
  }

  /**
   * 将用户添加到分区
   * @param partitionId 分区ID
   * @param userId 用户ID
   * @returns 是否成功
   */
  addUserToPartition(partitionId: string, userId: string): boolean {
    const partition = this.partitions.get(partitionId);

    if (partition && this.users.has(userId)) {
      partition.users.add(userId);
      partition.lastActivityAt = new Date();
      return true;
    }

    return false;
  }

  /**
   * 从分区中移除用户
   * @param partitionId 分区ID
   * @param userId 用户ID
   * @returns 是否成功
   */
  removeUserFromPartition(partitionId: string, userId: string): boolean {
    const partition = this.partitions.get(partitionId);

    if (partition) {
      const result = partition.users.delete(userId);
      partition.lastActivityAt = new Date();
      return result;
    }

    return false;
  }

  /**
   * 获取用户所在的分区
   * @param userId 用户ID
   * @returns 分区数组
   */
  getUserPartitions(userId: string): RoomPartition[] {
    const result: RoomPartition[] = [];

    for (const partition of this.partitions.values()) {
      if (partition.users.has(userId)) {
        result.push(partition);
      }
    }

    return result;
  }

  /**
   * 将操作添加到分区
   * @param operation 操作
   */
  private addOperationToPartitions(operation: Operation): void {
    // 获取操作用户所在的分区
    const userPartitions = this.getUserPartitions(operation.userId);

    // 将操作添加到每个分区
    for (const partition of userPartitions) {
      partition.operations.push(operation);

      // 限制分区操作历史大小
      if (partition.operations.length > this.config.maxOperationHistory) {
        partition.operations = partition.operations.slice(
          partition.operations.length - this.config.maxOperationHistory
        );
      }

      partition.lastActivityAt = new Date();
    }
  }

  /**
   * 更新用户分区
   * @param userId 用户ID
   * @param interestArea 兴趣区域
   */
  private updateUserPartitions(userId: string, interestArea: UserInterestArea): void {
    // 遍历所有区域分区
    for (const partition of this.partitions.values()) {
      if (partition.type === 'area' && partition.bounds) {
        // 检查用户兴趣区域是否与分区重叠
        const isOverlapping = this.isInterestAreaOverlappingBounds(interestArea, partition.bounds);

        // 如果重叠，将用户添加到分区
        if (isOverlapping) {
          partition.users.add(userId);
          partition.lastActivityAt = new Date();
        }
        // 否则，从分区中移除用户
        else {
          partition.users.delete(userId);
        }
      }
    }
  }

  /**
   * 将用户分配到区域分区
   * @param partition 分区
   */
  private assignUsersToAreaPartition(partition: RoomPartition): void {
    // 遍历所有用户
    for (const [userId, interestArea] of this.userInterestAreas.entries()) {
      // 检查用户兴趣区域是否与分区重叠
      if (partition.bounds && this.isInterestAreaOverlappingBounds(interestArea, partition.bounds)) {
        // 将用户添加到分区
        partition.users.add(userId);
      }
    }
  }

  /**
   * 检查兴趣区域是否与边界重叠
   * @param interestArea 兴趣区域
   * @param bounds 边界
   * @returns 是否重叠
   */
  private isInterestAreaOverlappingBounds(
    interestArea: UserInterestArea,
    bounds: {
      minX: number;
      minY: number;
      minZ: number;
      maxX: number;
      maxY: number;
      maxZ: number;
    }
  ): boolean {
    // 计算兴趣区域的边界
    const areaMinX = interestArea.position.x - interestArea.radius;
    const areaMaxX = interestArea.position.x + interestArea.radius;
    const areaMinY = interestArea.position.y - interestArea.radius;
    const areaMaxY = interestArea.position.y + interestArea.radius;
    const areaMinZ = interestArea.position.z - interestArea.radius;
    const areaMaxZ = interestArea.position.z + interestArea.radius;

    // 检查是否重叠
    return !(
      areaMaxX < bounds.minX ||
      areaMinX > bounds.maxX ||
      areaMaxY < bounds.minY ||
      areaMinY > bounds.maxY ||
      areaMaxZ < bounds.minZ ||
      areaMinZ > bounds.maxZ
    );
  }

  /**
   * 启动资源监控
   */
  private startResourceMonitoring(): void {
    if (this.resourceMonitorTimer !== null) {
      clearInterval(this.resourceMonitorTimer);
    }

    this.resourceMonitorTimer = setInterval(() => {
      this.updateResourceStats();
    }, this.config.resourceMonitoringInterval);
  }

  /**
   * 停止资源监控
   */
  private stopResourceMonitoring(): void {
    if (this.resourceMonitorTimer !== null) {
      clearInterval(this.resourceMonitorTimer);
      this.resourceMonitorTimer = null;
    }
  }

  /**
   * 更新资源统计
   */
  private updateResourceStats(): void {
    // 计算操作频率
    const now = new Date();
    const timeDiff = (now.getTime() - this.lastOperationCountTime.getTime()) / 1000; // 秒

    if (timeDiff > 0) {
      this.resourceStats.operationRate = this.operationCounter / timeDiff;
    }

    // 重置计数器
    this.operationCounter = 0;
    this.lastOperationCountTime = now;

    // 更新其他资源统计
    // 注意：这些是示例实现，实际应用中需要根据具体环境获取真实数据
    this.resourceStats.memoryUsage = this.estimateMemoryUsage();
    this.resourceStats.messageQueueLength = 0; // 需要从消息队列获取
    this.resourceStats.lastUpdated = now;
  }

  /**
   * 估算内存使用
   * @returns 内存使用（字节）
   */
  private estimateMemoryUsage(): number {
    // 这是一个简单的估算，实际应用中需要更准确的计算
    const usersSize = this.users.size * 1000; // 假设每个用户对象约1KB
    const operationsSize = this.operations.length * 500; // 假设每个操作约500字节
    const partitionsSize = this.partitions.size * 2000; // 假设每个分区约2KB

    return usersSize + operationsSize + partitionsSize;
  }

  /**
   * 启动自动清理
   */
  private startAutoCleanup(): void {
    if (this.autoCleanupTimer !== null) {
      clearInterval(this.autoCleanupTimer);
    }

    this.autoCleanupTimer = setInterval(() => {
      this.performAutoCleanup();
    }, this.config.autoCleanupInterval);
  }

  /**
   * 停止自动清理
   */
  private stopAutoCleanup(): void {
    if (this.autoCleanupTimer !== null) {
      clearInterval(this.autoCleanupTimer);
      this.autoCleanupTimer = null;
    }
  }

  /**
   * 执行自动清理
   */
  private performAutoCleanup(): void {
    const now = Date.now();
    const inactivityThreshold = now - this.config.inactivityTimeout;

    // 清理不活跃的用户
    for (const [userId, user] of this.users.entries()) {
      if (user.lastActivity < inactivityThreshold) {
        // 移除用户
        this.removeUser(userId);
      }
    }

    // 清理空分区
    for (const [partitionId, partition] of this.partitions.entries()) {
      if (partition.users.size === 0) {
        // 移除分区
        this.partitions.delete(partitionId);
      }
    }
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 9);
  }

  /**
   * 销毁房间
   */
  dispose(): void {
    // 停止所有定时器
    this.stopResourceMonitoring();
    this.stopAutoCleanup();

    // 清空所有集合
    this.users.clear();
    this.clients.clear();
    this.operations = [];
    this.partitions.clear();
    this.userInterestAreas.clear();
  }
}
