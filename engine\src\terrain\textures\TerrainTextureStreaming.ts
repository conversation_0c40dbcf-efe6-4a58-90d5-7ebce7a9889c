/**
 * 地形纹理流式加载
 * 提供地形纹理的流式加载和管理功能
 */
import * as THREE from 'three';
import { TerrainTextureCompression, TextureCompressionOptions } from './TerrainTextureCompression';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 纹理加载优先级
 */
export enum TextureLoadPriority {
  /** 非常高 */
  VERY_HIGH = 0,
  /** 高 */
  HIGH = 1,
  /** 中 */
  MEDIUM = 2,
  /** 低 */
  LOW = 3,
  /** 非常低 */
  VERY_LOW = 4
}

/**
 * 纹理加载状态
 */
export enum TextureLoadState {
  /** 未加载 */
  UNLOADED = 'unloaded',
  /** 加载中 */
  LOADING = 'loading',
  /** 已加载 */
  LOADED = 'loaded',
  /** 加载失败 */
  FAILED = 'failed',
  /** 已卸载 */
  UNLOADING = 'unloading'
}

/**
 * 纹理流式加载事件类型
 */
export enum TextureStreamingEventType {
  /** 纹理加载开始 */
  TEXTURE_LOAD_START = 'texture_load_start',
  /** 纹理加载完成 */
  TEXTURE_LOAD_COMPLETE = 'texture_load_complete',
  /** 纹理加载失败 */
  TEXTURE_LOAD_FAILED = 'texture_load_failed',
  /** 纹理卸载 */
  TEXTURE_UNLOADED = 'texture_unloaded',
  /** 纹理加载进度 */
  TEXTURE_LOAD_PROGRESS = 'texture_load_progress',
  /** 内存使用变化 */
  MEMORY_USAGE_CHANGED = 'memory_usage_changed'
}

/**
 * 纹理加载请求
 */
interface TextureLoadRequest {
  /** 纹理URL */
  url: string;
  /** 纹理ID */
  id: string;
  /** 优先级 */
  priority: TextureLoadPriority;
  /** 压缩选项 */
  compressionOptions?: TextureCompressionOptions;
  /** 加载状态 */
  state: TextureLoadState;
  /** 纹理 */
  texture?: THREE.Texture;
  /** 加载开始时间 */
  startTime?: number;
  /** 加载完成时间 */
  endTime?: number;
  /** 纹理大小（字节） */
  size?: number;
  /** 是否为占位符 */
  isPlaceholder?: boolean;
  /** 是否为低分辨率版本 */
  isLowRes?: boolean;
  /** 高分辨率版本URL */
  highResUrl?: string;
  /** 低分辨率版本URL */
  lowResUrl?: string;
}

/**
 * 纹理流式加载选项
 */
export interface TextureStreamingOptions {
  /** 最大并发加载数 */
  maxConcurrentLoads?: number;
  /** 最大内存使用量（MB） */
  maxMemoryUsage?: number;
  /** 是否使用纹理压缩 */
  useCompression?: boolean;
  /** 纹理压缩选项 */
  compressionOptions?: TextureCompressionOptions;
  /** 是否使用低分辨率占位符 */
  useLowResPlaceholders?: boolean;
  /** 低分辨率纹理大小 */
  lowResTextureSize?: number;
  /** 是否使用预测加载 */
  usePredictiveLoading?: boolean;
  /** 是否自动卸载 */
  autoUnload?: boolean;
  /** 卸载检查间隔（毫秒） */
  unloadCheckInterval?: number;
  /** 纹理未使用卸载时间（毫秒） */
  textureUnusedTime?: number;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 地形纹理流式加载类
 */
export class TerrainTextureStreaming {
  /** 默认选项 */
  private static readonly DEFAULT_OPTIONS: TextureStreamingOptions = {
    maxConcurrentLoads: 4,
    maxMemoryUsage: 512, // 512MB
    useCompression: true,
    useLowResPlaceholders: true,
    lowResTextureSize: 64,
    usePredictiveLoading: true,
    autoUnload: true,
    unloadCheckInterval: 10000, // 10秒
    textureUnusedTime: 30000, // 30秒
    debug: false
  };

  /** 选项 */
  private options: TextureStreamingOptions;

  /** 纹理压缩 */
  private textureCompression: TerrainTextureCompression;

  /** 纹理加载器 */
  private textureLoader: THREE.TextureLoader;

  /** 加载请求映射 */
  private loadRequests: Map<string, TextureLoadRequest>;

  /** 活跃加载请求 */
  private activeLoads: Set<string>;

  /** 加载队列 */
  private loadQueue: TextureLoadRequest[];

  /** 已加载纹理映射 */
  private loadedTextures: Map<string, THREE.Texture>;

  /** 纹理最后使用时间映射 */
  private textureLastUsed: Map<string, number>;

  /** 纹理大小映射（字节） */
  private textureSizes: Map<string, number>;

  /** 当前内存使用量（字节） */
  private currentMemoryUsage: number;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 卸载检查定时器ID */
  private unloadCheckTimerId: number | null;

  /** 默认占位符纹理 */
  private defaultPlaceholder: THREE.Texture;

  /**
   * 创建地形纹理流式加载
   * @param options 选项
   */
  constructor(options: TextureStreamingOptions = {}) {
    this.options = { ...TerrainTextureStreaming.DEFAULT_OPTIONS, ...options };

    // 创建纹理压缩
    this.textureCompression = new TerrainTextureCompression(this.options.compressionOptions);

    // 创建纹理加载器
    this.textureLoader = new THREE.TextureLoader();

    // 初始化映射和队列
    this.loadRequests = new Map();
    this.activeLoads = new Set();
    this.loadQueue = [];
    this.loadedTextures = new Map();
    this.textureLastUsed = new Map();
    this.textureSizes = new Map();
    this.currentMemoryUsage = 0;

    // 创建事件发射器
    this.eventEmitter = new EventEmitter();

    // 创建默认占位符纹理
    this.defaultPlaceholder = this.createDefaultPlaceholder();

    // 如果启用自动卸载，启动卸载检查
    if (this.options.autoUnload) {
      this.startUnloadCheck();
    }
  }

  /**
   * 创建默认占位符纹理
   * @returns 默认占位符纹理
   */
  private createDefaultPlaceholder(): THREE.Texture {
    // 创建画布
    const canvas = document.createElement('canvas');
    const size = this.options.lowResTextureSize || 64;
    canvas.width = size;
    canvas.height = size;

    // 获取上下文
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      Debug.warn('TerrainTextureStreaming', '无法创建2D上下文');
      return new THREE.Texture();
    }

    // 绘制灰色背景
    ctx.fillStyle = '#888888';
    ctx.fillRect(0, 0, size, size);

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.generateMipmaps = false;
    texture.needsUpdate = true;

    return texture;
  }

  /**
   * 启动卸载检查
   */
  private startUnloadCheck(): void {
    // 清除现有定时器
    if (this.unloadCheckTimerId !== null) {
      window.clearInterval(this.unloadCheckTimerId);
    }

    // 创建新定时器
    this.unloadCheckTimerId = window.setInterval(() => {
      this.checkForUnload();
    }, this.options.unloadCheckInterval);
  }

  /**
   * 停止卸载检查
   */
  private stopUnloadCheck(): void {
    if (this.unloadCheckTimerId !== null) {
      window.clearInterval(this.unloadCheckTimerId);
      this.unloadCheckTimerId = null;
    }
  }

  /**
   * 检查卸载
   */
  private checkForUnload(): void {
    const now = Date.now();
    const unusedTime = this.options.textureUnusedTime || 30000;

    // 检查每个已加载的纹理
    for (const [id, lastUsed] of this.textureLastUsed.entries()) {
      // 如果纹理未使用时间超过阈值，则卸载
      if (now - lastUsed > unusedTime) {
        this.unloadTexture(id);
      }
    }
  }

  /**
   * 加载纹理
   * @param url 纹理URL
   * @param options 加载选项
   * @returns 纹理ID
   */
  public loadTexture(
    url: string,
    options: {
      priority?: TextureLoadPriority;
      compressionOptions?: TextureCompressionOptions;
      id?: string;
      useLowRes?: boolean;
    } = {}
  ): string {
    // 生成纹理ID
    const id = options.id || `texture_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // 如果纹理已加载，则更新最后使用时间并返回
    if (this.loadedTextures.has(id)) {
      this.textureLastUsed.set(id, Date.now());
      return id;
    }

    // 创建加载请求
    const request: TextureLoadRequest = {
      url,
      id,
      priority: options.priority || TextureLoadPriority.MEDIUM,
      compressionOptions: options.compressionOptions || this.options.compressionOptions,
      state: TextureLoadState.UNLOADED,
      isLowRes: options.useLowRes
    };

    // 如果使用低分辨率占位符
    if (this.options.useLowResPlaceholders && !options.useLowRes) {
      // 创建低分辨率URL
      const urlParts = url.split('.');
      const ext = urlParts.pop();
      const baseUrl = urlParts.join('.');
      const lowResUrl = `${baseUrl}_lowres.${ext}`;

      // 设置高低分辨率URL
      request.highResUrl = url;
      request.lowResUrl = lowResUrl;

      // 加载低分辨率版本
      this.loadTexture(lowResUrl, {
        priority: TextureLoadPriority.VERY_HIGH,
        compressionOptions: {
          ...options.compressionOptions,
          maxTextureSize: this.options.lowResTextureSize
        },
        id: `${id}_lowres`,
        useLowRes: true
      });
    }

    // 存储加载请求
    this.loadRequests.set(id, request);

    // 添加到加载队列
    this.addToLoadQueue(request);

    // 处理加载队列
    this.processLoadQueue();

    return id;
  }

  /**
   * 添加到加载队列
   * @param request 加载请求
   */
  private addToLoadQueue(request: TextureLoadRequest): void {
    // 更新请求状态
    request.state = TextureLoadState.UNLOADED;

    // 添加到队列
    this.loadQueue.push(request);

    // 按优先级排序
    this.loadQueue.sort((a, b) => a.priority - b.priority);
  }

  /**
   * 处理加载队列
   */
  private processLoadQueue(): void {
    // 如果没有队列中的请求，则返回
    if (this.loadQueue.length === 0) {
      return;
    }

    // 如果活跃加载数已达到最大值，则返回
    if (this.activeLoads.size >= (this.options.maxConcurrentLoads || 4)) {
      return;
    }

    // 获取下一个请求
    const request = this.loadQueue.shift();
    if (!request) {
      return;
    }

    // 开始加载
    this.startLoading(request);
  }

  /**
   * 开始加载
   * @param request 加载请求
   */
  private startLoading(request: TextureLoadRequest): void {
    // 更新请求状态
    request.state = TextureLoadState.LOADING;
    request.startTime = Date.now();

    // 添加到活跃加载
    this.activeLoads.add(request.id);

    // 发出加载开始事件
    this.eventEmitter.emit(TextureStreamingEventType.TEXTURE_LOAD_START, request);

    // 加载纹理
    this.textureLoader.load(
      request.url,
      // 成功回调
      async (texture) => {
        // 如果启用压缩，则压缩纹理
        if (this.options.useCompression) {
          try {
            texture = await this.textureCompression.compressTexture(texture, request.compressionOptions);
          } catch (error) {
            Debug.warn('TerrainTextureStreaming', `压缩纹理失败: ${request.url}`, error);
          }
        }

        // 完成加载
        this.completeLoading(request, texture);
      },
      // 进度回调
      (event) => {
        if (event.lengthComputable) {
          const progress = event.loaded / event.total;
          this.eventEmitter.emit(TextureStreamingEventType.TEXTURE_LOAD_PROGRESS, request, progress);
        }
      },
      // 错误回调
      (error) => {
        this.failLoading(request, error);
      }
    );
  }

  /**
   * 完成加载
   * @param request 加载请求
   * @param texture 纹理
   */
  private completeLoading(request: TextureLoadRequest, texture: THREE.Texture): void {
    // 更新请求状态
    request.state = TextureLoadState.LOADED;
    request.endTime = Date.now();
    request.texture = texture;

    // 估算纹理大小
    const size = this.estimateTextureSize(texture);
    request.size = size;

    // 更新内存使用量
    this.updateMemoryUsage(request.id, size);

    // 从活跃加载中移除
    this.activeLoads.delete(request.id);

    // 存储已加载纹理
    this.loadedTextures.set(request.id, texture);

    // 更新最后使用时间
    this.textureLastUsed.set(request.id, Date.now());

    // 发出加载完成事件
    this.eventEmitter.emit(TextureStreamingEventType.TEXTURE_LOAD_COMPLETE, request);

    // 处理下一个加载请求
    this.processLoadQueue();
  }

  /**
   * 加载失败
   * @param request 加载请求
   * @param error 错误
   */
  private failLoading(request: TextureLoadRequest, error: any): void {
    // 更新请求状态
    request.state = TextureLoadState.FAILED;
    request.endTime = Date.now();

    // 从活跃加载中移除
    this.activeLoads.delete(request.id);

    // 发出加载失败事件
    this.eventEmitter.emit(TextureStreamingEventType.TEXTURE_LOAD_FAILED, request, error);

    // 记录错误
    Debug.error('TerrainTextureStreaming', `加载纹理失败: ${request.url}`, error);

    // 处理下一个加载请求
    this.processLoadQueue();
  }

  /**
   * 估算纹理大小
   * @param texture 纹理
   * @returns 估算大小（字节）
   */
  private estimateTextureSize(texture: THREE.Texture): number {
    // 获取图像尺寸
    const width = texture.image?.width || 0;
    const height = texture.image?.height || 0;

    // 计算像素数
    const pixelCount = width * height;

    // 估算每个像素的字节数（RGBA = 4字节）
    const bytesPerPixel = 4;

    // 如果生成mipmap，则额外增加1/3的大小
    const mipmapFactor = texture.generateMipmaps ? 1.33 : 1;

    // 计算总大小
    return Math.ceil(pixelCount * bytesPerPixel * mipmapFactor);
  }

  /**
   * 更新内存使用量
   * @param id 纹理ID
   * @param size 大小（字节）
   */
  private updateMemoryUsage(id: string, size: number): void {
    // 获取旧大小
    const oldSize = this.textureSizes.get(id) || 0;

    // 更新大小
    this.textureSizes.set(id, size);

    // 更新总内存使用量
    this.currentMemoryUsage = this.currentMemoryUsage - oldSize + size;

    // 发出内存使用变化事件
    this.eventEmitter.emit(TextureStreamingEventType.MEMORY_USAGE_CHANGED, this.currentMemoryUsage);

    // 检查是否超过最大内存使用量
    this.checkMemoryUsage();
  }

  /**
   * 检查内存使用量
   */
  private checkMemoryUsage(): void {
    // 获取最大内存使用量（字节）
    const maxMemoryUsage = (this.options.maxMemoryUsage || 512) * 1024 * 1024;

    // 如果当前内存使用量超过最大值，则卸载一些纹理
    if (this.currentMemoryUsage > maxMemoryUsage) {
      this.unloadLeastRecentlyUsedTextures();
    }
  }

  /**
   * 卸载最近最少使用的纹理
   */
  private unloadLeastRecentlyUsedTextures(): void {
    // 如果没有已加载的纹理，则返回
    if (this.loadedTextures.size === 0) {
      return;
    }

    // 获取最大内存使用量（字节）
    const maxMemoryUsage = (this.options.maxMemoryUsage || 512) * 1024 * 1024;

    // 计算需要释放的内存
    const memoryToFree = this.currentMemoryUsage - maxMemoryUsage * 0.8; // 释放到80%

    // 如果不需要释放内存，则返回
    if (memoryToFree <= 0) {
      return;
    }

    // 按最后使用时间排序
    const sortedTextures = Array.from(this.textureLastUsed.entries())
      .sort((a, b) => a[1] - b[1]);

    // 释放内存
    let freedMemory = 0;
    for (const [id, lastUsed] of sortedTextures) {
      // 如果已经释放足够的内存，则停止
      if (freedMemory >= memoryToFree) {
        break;
      }

      // 卸载纹理
      const textureSize = this.unloadTexture(id);

      // 更新已释放的内存
      freedMemory += textureSize;
    }
  }

  /**
   * 卸载纹理
   * @param id 纹理ID
   * @returns 释放的内存大小（字节）
   */
  public unloadTexture(id: string): number {
    // 获取纹理
    const texture = this.loadedTextures.get(id);
    if (!texture) {
      return 0;
    }

    // 获取纹理大小
    const size = this.textureSizes.get(id) || 0;

    // 更新请求状态
    const request = this.loadRequests.get(id);
    if (request) {
      request.state = TextureLoadState.UNLOADING;
      request.texture = undefined;
    }

    // 释放纹理资源
    (texture as any).dispose();

    // 从映射中移除
    this.loadedTextures.delete(id);
    this.textureLastUsed.delete(id);
    this.textureSizes.delete(id);

    // 更新内存使用量
    this.currentMemoryUsage -= size;

    // 发出纹理卸载事件
    this.eventEmitter.emit(TextureStreamingEventType.TEXTURE_UNLOADED, id);

    // 发出内存使用变化事件
    this.eventEmitter.emit(TextureStreamingEventType.MEMORY_USAGE_CHANGED, this.currentMemoryUsage);

    return size;
  }

  /**
   * 获取纹理
   * @param id 纹理ID
   * @returns 纹理
   */
  public getTexture(id: string): THREE.Texture | null {
    // 更新最后使用时间
    if (this.loadedTextures.has(id)) {
      this.textureLastUsed.set(id, Date.now());
      return this.loadedTextures.get(id) || null;
    }

    // 检查是否有低分辨率版本
    const lowResId = `${id}_lowres`;
    if (this.loadedTextures.has(lowResId)) {
      return this.loadedTextures.get(lowResId) || null;
    }

    // 返回默认占位符
    return this.defaultPlaceholder;
  }

  /**
   * 获取纹理加载状态
   * @param id 纹理ID
   * @returns 加载状态
   */
  public getTextureLoadState(id: string): TextureLoadState {
    const request = this.loadRequests.get(id);
    return request ? request.state : TextureLoadState.UNLOADED;
  }

  /**
   * 获取当前内存使用量
   * @returns 内存使用量（字节）
   */
  public getMemoryUsage(): number {
    return this.currentMemoryUsage;
  }

  /**
   * 获取当前内存使用量（MB）
   * @returns 内存使用量（MB）
   */
  public getMemoryUsageMB(): number {
    return this.currentMemoryUsage / (1024 * 1024);
  }

  /**
   * 获取最大内存使用量（MB）
   * @returns 最大内存使用量（MB）
   */
  public getMaxMemoryUsageMB(): number {
    return this.options.maxMemoryUsage || 512;
  }

  /**
   * 获取已加载纹理数量
   * @returns 已加载纹理数量
   */
  public getLoadedTextureCount(): number {
    return this.loadedTextures.size;
  }

  /**
   * 获取活跃加载数量
   * @returns 活跃加载数量
   */
  public getActiveLoadCount(): number {
    return this.activeLoads.size;
  }

  /**
   * 获取队列中的加载数量
   * @returns 队列中的加载数量
   */
  public getQueuedLoadCount(): number {
    return this.loadQueue.length;
  }

  /**
   * 清除所有纹理
   */
  public clearAll(): void {
    // 卸载所有纹理
    for (const id of this.loadedTextures.keys()) {
      this.unloadTexture(id);
    }

    // 清除加载队列
    this.loadQueue = [];

    // 清除加载请求
    this.loadRequests.clear();

    // 重置内存使用量
    this.currentMemoryUsage = 0;

    // 发出内存使用变化事件
    this.eventEmitter.emit(TextureStreamingEventType.MEMORY_USAGE_CHANGED, this.currentMemoryUsage);
  }

  /**
   * 设置选项
   * @param options 选项
   */
  public setOptions(options: TextureStreamingOptions): void {
    this.options = { ...this.options, ...options };

    // 更新纹理压缩选项
    if (options.compressionOptions) {
      this.textureCompression.setOptions(options.compressionOptions);
    }

    // 如果启用自动卸载，启动卸载检查
    if (this.options.autoUnload) {
      this.startUnloadCheck();
    } else {
      this.stopUnloadCheck();
    }

    // 检查内存使用量
    this.checkMemoryUsage();
  }

  /**
   * 注册事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: TextureStreamingEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: TextureStreamingEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 销毁
   */
  public dispose(): void {
    // 停止卸载检查
    this.stopUnloadCheck();

    // 清除所有纹理
    this.clearAll();

    // 清除事件监听器
    this.eventEmitter.removeAllListeners();
  }
}
