/**
 * API网关入口文件
 */
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as compression from 'compression';
import * as helmet from 'helmet';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
// import { RateLimiterGuard } from '@shared/rate-limiter';
// import { CircuitBreakerInterceptor } from '@shared/circuit-breaker';

async function bootstrap() {
  // 创建Nest应用实例
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 全局前缀
  app.setGlobalPrefix('api');

  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // 全局过滤器
  app.useGlobalFilters(new HttpExceptionFilter());

  // 全局拦截器
  app.useGlobalInterceptors(
    new LoggingInterceptor(),
    new TransformInterceptor(),
    // new CircuitBreakerInterceptor(app.get('Reflector'), app.get('CircuitBreakerService')),
  );

  // 全局守卫 - 暂时禁用
  // app.useGlobalGuards(new RateLimiterGuard(app.get('Reflector'), app.get('RateLimiterService')));

  // 启用CORS
  app.enableCors();

  // 启用压缩
  app.use(compression());

  // 启用安全头
  app.use(helmet.default());

  // Swagger文档
  const config = new DocumentBuilder()
    .setTitle('DL（Digital Learning）引擎API')
    .setDescription('DL（Digital Learning）引擎API文档')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // 启动HTTP服务
  const port = configService.get<number>('API_GATEWAY_PORT', 8080);
  await app.listen(port);
  console.log(`API网关已启动，端口: ${port}`);
}

bootstrap();
