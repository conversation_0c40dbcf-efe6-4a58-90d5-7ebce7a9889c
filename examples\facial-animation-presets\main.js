/**
 * 面部动画预设示例
 */
(function() {
  // 引擎实例
  let engine;
  let world;
  let characterEntity;
  let facialAnimationSystem;
  let facialAnimationPresetSystem;
  let facialAnimationModelAdapterSystem;
  
  // 预设和模板数据
  let presets = [];
  let templates = [];
  
  // 当前选中的项
  let selectedItem = null;
  let selectedItemType = null;
  
  // DOM元素
  const previewElement = document.getElementById('preview');
  const presetsGrid = document.getElementById('presetsGrid');
  const templatesGrid = document.getElementById('templatesGrid');
  const presetTypeSelect = document.getElementById('presetTypeSelect');
  const cultureSelect = document.getElementById('cultureSelect');
  const cultureFilterGroup = document.getElementById('cultureFilterGroup');
  const categorySelect = document.getElementById('categorySelect');
  const searchPresetsInput = document.getElementById('searchPresets');
  const searchTemplatesInput = document.getElementById('searchTemplates');
  const tabButtons = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');
  const previewModal = document.getElementById('previewModal');
  const modalTitle = document.getElementById('modalTitle');
  const modalItemName = document.getElementById('modalItemName');
  const modalItemDescription = document.getElementById('modalItemDescription');
  const modalItemTags = document.getElementById('modalItemTags');
  const modalParameters = document.getElementById('modalParameters');
  const modalPreview = document.getElementById('modalPreview');
  const applyBtn = document.getElementById('applyBtn');
  const closeBtn = document.querySelector('.close-btn');
  const cancelBtn = document.querySelector('.cancel-btn');
  const loadModelBtn = document.getElementById('loadModelBtn');
  const modelSelect = document.getElementById('modelSelect');
  const playBtn = document.getElementById('playBtn');
  const pauseBtn = document.getElementById('pauseBtn');
  const resetBtn = document.getElementById('resetBtn');
  
  // 初始化
  function init() {
    // 初始化引擎
    initEngine();
    
    // 加载预设和模板
    loadPresets();
    loadTemplates();
    
    // 绑定事件
    bindEvents();
    
    // 初始化UI
    updateUI();
  }
  
  // 初始化引擎
  function initEngine() {
    // 创建引擎实例
    engine = new Engine({
      debug: true,
      antialias: true
    });
    
    // 创建世界
    world = engine.createWorld();
    
    // 创建渲染器
    const renderer = engine.createRenderer({
      container: previewElement,
      clearColor: 0x1a1a1a,
      shadows: true
    });
    
    // 创建相机
    const camera = engine.createCamera({
      type: 'perspective',
      fov: 50,
      near: 0.1,
      far: 1000,
      position: [0, 1.6, 2],
      target: [0, 1.6, 0]
    });
    
    // 创建灯光
    const light = engine.createLight({
      type: 'directional',
      color: 0xffffff,
      intensity: 1,
      position: [1, 2, 1],
      castShadow: true
    });
    
    // 创建环境光
    const ambientLight = engine.createLight({
      type: 'ambient',
      color: 0x404040,
      intensity: 1
    });
    
    // 创建地面
    const ground = engine.createMesh({
      geometry: {
        type: 'plane',
        width: 10,
        height: 10
      },
      material: {
        type: 'standard',
        color: 0x808080,
        roughness: 0.7,
        metalness: 0.1
      },
      position: [0, 0, 0],
      rotation: [-Math.PI / 2, 0, 0],
      receiveShadow: true
    });
    
    // 创建角色实体
    characterEntity = world.createEntity();
    
    // 创建面部动画系统
    facialAnimationSystem = new FacialAnimationSystem(world, {
      debug: true,
      autoDetectAudio: true
    });
    
    // 创建面部动画模型适配器系统
    facialAnimationModelAdapterSystem = new FacialAnimationModelAdapterSystem(world, {
      debug: true,
      defaultModelType: 'gltf',
      autoDetectBlendShapes: true
    });
    
    // 创建面部动画预设系统
    facialAnimationPresetSystem = new FacialAnimationPresetSystem(world, {
      debug: true,
      autoLoadPresets: true,
      defaultCulture: 'chinese'
    });
    
    // 添加系统到世界
    world.addSystem(facialAnimationSystem);
    world.addSystem(facialAnimationModelAdapterSystem);
    world.addSystem(facialAnimationPresetSystem);
    
    // 启动引擎
    engine.start();
  }
  
  // 加载模型
  function loadModel(modelType = 'default') {
    // 模型路径
    let modelPath;
    
    switch (modelType) {
      case 'female':
        modelPath = '../../assets/models/characters/female.glb';
        break;
      case 'male':
        modelPath = '../../assets/models/characters/male.glb';
        break;
      case 'cartoon':
        modelPath = '../../assets/models/characters/cartoon.glb';
        break;
      default:
        modelPath = '../../assets/models/characters/default.glb';
        break;
    }
    
    // 加载模型
    engine.loadModel(modelPath, {
      position: [0, 0, 0],
      scale: [1, 1, 1],
      castShadow: true,
      receiveShadow: true
    }).then(model => {
      // 绑定模型到角色实体
      world.addComponent(characterEntity, model);
      
      // 创建面部动画组件
      const facialAnimation = facialAnimationSystem.createFacialAnimation(characterEntity);
      
      // 将面部动画组件与模型绑定
      facialAnimationSystem.linkToModel(characterEntity, model);
      
      console.log('模型加载完成，已绑定面部动画组件');
    }).catch(error => {
      console.error('加载模型失败:', error);
    });
  }
  
  // 加载预设
  function loadPresets() {
    // 模拟从服务器加载预设
    // 实际应用中应该从引擎或服务器获取
    presets = [
      {
        id: 'happy',
        name: '开心',
        type: 'standard',
        description: '标准开心表情',
        tags: ['基础', '积极'],
        expression: 'happy',
        weight: 1.0,
        culture: 'global',
        author: '系统',
        thumbnail: '../../assets/thumbnails/expressions/happy.jpg'
      },
      {
        id: 'sad',
        name: '悲伤',
        type: 'standard',
        description: '标准悲伤表情',
        tags: ['基础', '消极'],
        expression: 'sad',
        weight: 1.0,
        culture: 'global',
        author: '系统',
        thumbnail: '../../assets/thumbnails/expressions/sad.jpg'
      },
      {
        id: 'angry',
        name: '愤怒',
        type: 'standard',
        description: '标准愤怒表情',
        tags: ['基础', '消极'],
        expression: 'angry',
        weight: 1.0,
        culture: 'global',
        author: '系统',
        thumbnail: '../../assets/thumbnails/expressions/angry.jpg'
      },
      {
        id: 'surprised',
        name: '惊讶',
        type: 'standard',
        description: '标准惊讶表情',
        tags: ['基础', '中性'],
        expression: 'surprised',
        weight: 1.0,
        culture: 'global',
        author: '系统',
        thumbnail: '../../assets/thumbnails/expressions/surprised.jpg'
      },
      {
        id: 'chinese_smile',
        name: '含蓄微笑',
        type: 'cultural',
        description: '中国传统含蓄微笑',
        tags: ['文化', '微笑'],
        culture: 'chinese',
        expressionCombos: [
          { expression: 'happy', weight: 0.5 },
          { expression: 'neutral', weight: 0.5 }
        ],
        author: '系统',
        thumbnail: '../../assets/thumbnails/expressions/chinese_smile.jpg'
      },
      {
        id: 'laugh_sequence',
        name: '笑声序列',
        type: 'animation_sequence',
        description: '从微笑到大笑的序列',
        tags: ['序列', '积极'],
        culture: 'global',
        animationSequence: [
          { expression: 'happy', weight: 0.5, duration: 0.3 },
          { expression: 'happy', weight: 0.8, duration: 0.2 },
          { expression: 'happy', weight: 1.0, duration: 0.5 },
          { expression: 'happy', weight: 0.8, duration: 0.3 },
          { expression: 'happy', weight: 0.5, duration: 0.2 },
          { expression: 'neutral', weight: 0.3, duration: 0.2 }
        ],
        author: '系统',
        thumbnail: '../../assets/thumbnails/expressions/laugh_sequence.jpg'
      }
    ];
    
    // 渲染预设
    renderPresets();
  }
  
  // 加载模板
  function loadTemplates() {
    // 模拟从服务器加载模板
    // 实际应用中应该从引擎或服务器获取
    templates = [
      {
        id: 'idle',
        name: '待机',
        description: '待机动画模板',
        category: '面部',
        tags: ['待机', '面部', '眨眼', '呼吸'],
        parameters: [
          {
            id: 'duration',
            name: '持续时间',
            type: 'number',
            defaultValue: 15.0,
            min: 5,
            max: 60,
            step: 0.1
          },
          {
            id: 'expression',
            name: '表情',
            type: 'enum',
            defaultValue: 'neutral',
            options: [
              { value: 'neutral', label: '中性' },
              { value: 'happy', label: '开心' },
              { value: 'sad', label: '悲伤' }
            ]
          },
          {
            id: 'expressionIntensity',
            name: '表情强度',
            type: 'number',
            defaultValue: 0.3,
            min: 0,
            max: 1,
            step: 0.01
          },
          {
            id: 'loop',
            name: '循环',
            type: 'boolean',
            defaultValue: true
          }
        ],
        author: '系统',
        thumbnail: '../../assets/thumbnails/templates/idle.jpg'
      },
      {
        id: 'talk',
        name: '说话',
        description: '说话动画模板',
        category: '口型',
        tags: ['口型', '说话', '对话'],
        parameters: [
          {
            id: 'duration',
            name: '持续时间',
            type: 'number',
            defaultValue: 5.0,
            min: 1,
            max: 30,
            step: 0.1
          },
          {
            id: 'visemeIntensity',
            name: '口型强度',
            type: 'number',
            defaultValue: 1.0,
            min: 0,
            max: 1,
            step: 0.01
          },
          {
            id: 'speed',
            name: '速度',
            type: 'number',
            defaultValue: 1.0,
            min: 0.5,
            max: 2,
            step: 0.1
          },
          {
            id: 'randomness',
            name: '随机性',
            type: 'number',
            defaultValue: 0.3,
            min: 0,
            max: 1,
            step: 0.01
          },
          {
            id: 'loop',
            name: '循环',
            type: 'boolean',
            defaultValue: true
          }
        ],
        author: '系统',
        thumbnail: '../../assets/thumbnails/templates/talk.jpg'
      },
      {
        id: 'emotional_talk',
        name: '情感对话',
        description: '带情感的说话动画模板',
        category: '组合',
        tags: ['口型', '说话', '情感', '组合'],
        parameters: [
          {
            id: 'expression',
            name: '表情',
            type: 'enum',
            defaultValue: 'happy',
            options: [
              { value: 'happy', label: '开心' },
              { value: 'sad', label: '悲伤' },
              { value: 'angry', label: '生气' },
              { value: 'surprised', label: '惊讶' }
            ]
          },
          {
            id: 'expressionIntensity',
            name: '表情强度',
            type: 'number',
            defaultValue: 0.7,
            min: 0,
            max: 1,
            step: 0.01
          },
          {
            id: 'duration',
            name: '持续时间',
            type: 'number',
            defaultValue: 5.0,
            min: 1,
            max: 30,
            step: 0.1
          },
          {
            id: 'visemeIntensity',
            name: '口型强度',
            type: 'number',
            defaultValue: 1.0,
            min: 0,
            max: 1,
            step: 0.01
          },
          {
            id: 'speed',
            name: '速度',
            type: 'number',
            defaultValue: 1.0,
            min: 0.5,
            max: 2,
            step: 0.1
          },
          {
            id: 'randomness',
            name: '随机性',
            type: 'number',
            defaultValue: 0.3,
            min: 0,
            max: 1,
            step: 0.01
          },
          {
            id: 'loop',
            name: '循环',
            type: 'boolean',
            defaultValue: true
          }
        ],
        author: '系统',
        thumbnail: '../../assets/thumbnails/templates/emotional_talk.jpg'
      }
    ];
    
    // 渲染模板
    renderTemplates();
  }
  
  // 绑定事件
  function bindEvents() {
    // 标签切换
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tab = button.dataset.tab;
        
        // 更新活动标签按钮
        tabButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        // 更新活动标签内容
        tabContents.forEach(content => content.classList.remove('active'));
        document.getElementById(`${tab}-tab`).classList.add('active');
      });
    });
    
    // 预设类型变更
    presetTypeSelect.addEventListener('change', () => {
      const type = presetTypeSelect.value;
      
      // 显示/隐藏文化过滤器
      if (type === 'cultural') {
        cultureFilterGroup.style.display = 'flex';
      } else {
        cultureFilterGroup.style.display = 'none';
      }
      
      // 过滤预设
      renderPresets();
    });
    
    // 文化变更
    cultureSelect.addEventListener('change', () => {
      renderPresets();
    });
    
    // 类别变更
    categorySelect.addEventListener('change', () => {
      renderTemplates();
    });
    
    // 搜索预设
    searchPresetsInput.addEventListener('input', () => {
      renderPresets();
    });
    
    // 搜索模板
    searchTemplatesInput.addEventListener('input', () => {
      renderTemplates();
    });
    
    // 关闭模态框
    closeBtn.addEventListener('click', () => {
      previewModal.classList.remove('active');
    });
    
    // 取消按钮
    cancelBtn.addEventListener('click', () => {
      previewModal.classList.remove('active');
    });
    
    // 应用按钮
    applyBtn.addEventListener('click', () => {
      if (selectedItem && selectedItemType) {
        if (selectedItemType === 'preset') {
          applyPreset(selectedItem);
        } else if (selectedItemType === 'template') {
          applyTemplate(selectedItem);
        }
      }
      
      previewModal.classList.remove('active');
    });
    
    // 加载模型按钮
    loadModelBtn.addEventListener('click', () => {
      loadModel(modelSelect.value);
    });
    
    // 播放按钮
    playBtn.addEventListener('click', () => {
      if (facialAnimationSystem) {
        facialAnimationSystem.play();
      }
    });
    
    // 暂停按钮
    pauseBtn.addEventListener('click', () => {
      if (facialAnimationSystem) {
        facialAnimationSystem.pause();
      }
    });
    
    // 重置按钮
    resetBtn.addEventListener('click', () => {
      if (facialAnimationSystem) {
        facialAnimationSystem.reset();
      }
    });
  }
  
  // 更新UI
  function updateUI() {
    // 初始隐藏文化过滤器
    cultureFilterGroup.style.display = 'none';
  }
  
  // 渲染预设
  function renderPresets() {
    // 清空预设网格
    presetsGrid.innerHTML = '';
    
    // 获取过滤条件
    const type = presetTypeSelect.value;
    const culture = cultureSelect.value;
    const searchText = searchPresetsInput.value.toLowerCase();
    
    // 过滤预设
    const filteredPresets = presets.filter(preset => {
      // 按类型过滤
      if (type && preset.type !== type) {
        return false;
      }
      
      // 按文化过滤
      if (type === 'cultural' && culture && preset.culture !== culture) {
        return false;
      }
      
      // 按搜索文本过滤
      if (searchText) {
        return (
          preset.name.toLowerCase().includes(searchText) ||
          preset.description.toLowerCase().includes(searchText) ||
          preset.tags.some(tag => tag.toLowerCase().includes(searchText))
        );
      }
      
      return true;
    });
    
    // 渲染预设卡片
    filteredPresets.forEach(preset => {
      const presetCard = document.createElement('div');
      presetCard.className = 'preset-card';
      presetCard.innerHTML = `
        <div class="card-image" style="background-image: url('${preset.thumbnail || '../../assets/thumbnails/placeholder.jpg'}')"></div>
        <div class="card-content">
          <h3 class="card-title">${preset.name}</h3>
          <p class="card-description">${preset.description || ''}</p>
          <div class="tags-container">
            ${preset.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
          </div>
        </div>
      `;
      
      // 点击预设卡片
      presetCard.addEventListener('click', () => {
        openPresetPreview(preset);
      });
      
      presetsGrid.appendChild(presetCard);
    });
  }
  
  // 渲染模板
  function renderTemplates() {
    // 清空模板网格
    templatesGrid.innerHTML = '';
    
    // 获取过滤条件
    const category = categorySelect.value;
    const searchText = searchTemplatesInput.value.toLowerCase();
    
    // 过滤模板
    const filteredTemplates = templates.filter(template => {
      // 按类别过滤
      if (category && template.category !== category) {
        return false;
      }
      
      // 按搜索文本过滤
      if (searchText) {
        return (
          template.name.toLowerCase().includes(searchText) ||
          template.description.toLowerCase().includes(searchText) ||
          template.tags.some(tag => tag.toLowerCase().includes(searchText))
        );
      }
      
      return true;
    });
    
    // 渲染模板卡片
    filteredTemplates.forEach(template => {
      const templateCard = document.createElement('div');
      templateCard.className = 'template-card';
      templateCard.innerHTML = `
        <div class="card-image" style="background-image: url('${template.thumbnail || '../../assets/thumbnails/placeholder.jpg'}')"></div>
        <div class="card-content">
          <h3 class="card-title">${template.name}</h3>
          <p class="card-description">${template.description || ''}</p>
          <div class="tags-container">
            ${template.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
          </div>
        </div>
      `;
      
      // 点击模板卡片
      templateCard.addEventListener('click', () => {
        openTemplatePreview(template);
      });
      
      templatesGrid.appendChild(templateCard);
    });
  }
  
  // 打开预设预览
  function openPresetPreview(preset) {
    selectedItem = preset;
    selectedItemType = 'preset';
    
    // 设置模态框标题
    modalTitle.textContent = '预览预设';
    
    // 设置预设信息
    modalItemName.textContent = preset.name;
    modalItemDescription.textContent = preset.description || '';
    
    // 设置标签
    modalItemTags.innerHTML = '';
    preset.tags.forEach(tag => {
      const tagElement = document.createElement('span');
      tagElement.className = 'tag';
      tagElement.textContent = tag;
      modalItemTags.appendChild(tagElement);
    });
    
    // 清空参数容器
    modalParameters.innerHTML = '';
    
    // 显示模态框
    previewModal.classList.add('active');
    
    // 预览预设
    previewPreset(preset);
  }
  
  // 打开模板预览
  function openTemplatePreview(template) {
    selectedItem = template;
    selectedItemType = 'template';
    
    // 设置模态框标题
    modalTitle.textContent = '预览模板';
    
    // 设置模板信息
    modalItemName.textContent = template.name;
    modalItemDescription.textContent = template.description || '';
    
    // 设置标签
    modalItemTags.innerHTML = '';
    template.tags.forEach(tag => {
      const tagElement = document.createElement('span');
      tagElement.className = 'tag';
      tagElement.textContent = tag;
      modalItemTags.appendChild(tagElement);
    });
    
    // 设置参数控件
    modalParameters.innerHTML = '<h3>参数</h3>';
    
    template.parameters.forEach(param => {
      const paramItem = document.createElement('div');
      paramItem.className = 'parameter-item';
      
      const paramLabel = document.createElement('div');
      paramLabel.className = 'parameter-label';
      paramLabel.textContent = param.name;
      
      const paramControl = document.createElement('div');
      paramControl.className = 'parameter-control';
      
      // 根据参数类型创建不同的控件
      switch (param.type) {
        case 'number':
          const numberInput = document.createElement('input');
          numberInput.type = 'range';
          numberInput.min = param.min || 0;
          numberInput.max = param.max || 1;
          numberInput.step = param.step || 0.01;
          numberInput.value = param.defaultValue;
          paramControl.appendChild(numberInput);
          break;
          
        case 'boolean':
          const checkbox = document.createElement('input');
          checkbox.type = 'checkbox';
          checkbox.checked = param.defaultValue;
          paramControl.appendChild(checkbox);
          break;
          
        case 'enum':
          const select = document.createElement('select');
          param.options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.label;
            select.appendChild(optionElement);
          });
          select.value = param.defaultValue;
          paramControl.appendChild(select);
          break;
          
        case 'string':
          const textInput = document.createElement('input');
          textInput.type = 'text';
          textInput.value = param.defaultValue || '';
          paramControl.appendChild(textInput);
          break;
      }
      
      paramItem.appendChild(paramLabel);
      paramItem.appendChild(paramControl);
      modalParameters.appendChild(paramItem);
    });
    
    // 显示模态框
    previewModal.classList.add('active');
    
    // 预览模板
    previewTemplate(template);
  }
  
  // 预览预设
  function previewPreset(preset) {
    if (!facialAnimationSystem || !characterEntity) return;
    
    // 应用预设到预览
    try {
      // 这里应该调用引擎的预览功能
      // 示例代码，实际实现需要与引擎集成
      console.log('预览预设:', preset.name);
    } catch (error) {
      console.error('预览预设失败:', error);
    }
  }
  
  // 预览模板
  function previewTemplate(template) {
    if (!facialAnimationSystem || !characterEntity) return;
    
    // 应用模板到预览
    try {
      // 这里应该调用引擎的预览功能
      // 示例代码，实际实现需要与引擎集成
      console.log('预览模板:', template.name);
    } catch (error) {
      console.error('预览模板失败:', error);
    }
  }
  
  // 应用预设
  function applyPreset(preset) {
    if (!facialAnimationSystem || !characterEntity) return;
    
    // 应用预设到角色
    try {
      // 这里应该调用引擎的应用预设功能
      // 示例代码，实际实现需要与引擎集成
      facialAnimationPresetSystem.applyPreset(characterEntity, preset.id);
      console.log('应用预设:', preset.name);
    } catch (error) {
      console.error('应用预设失败:', error);
    }
  }
  
  // 应用模板
  function applyTemplate(template) {
    if (!facialAnimationSystem || !characterEntity) return;
    
    // 收集参数值
    const parameters = {};
    
    template.parameters.forEach(param => {
      // 获取参数控件
      const paramItems = modalParameters.querySelectorAll('.parameter-item');
      
      for (let i = 0; i < paramItems.length; i++) {
        const paramItem = paramItems[i];
        const paramLabel = paramItem.querySelector('.parameter-label');
        
        if (paramLabel.textContent === param.name) {
          const control = paramItem.querySelector('.parameter-control').firstChild;
          
          switch (param.type) {
            case 'number':
              parameters[param.id] = parseFloat(control.value);
              break;
              
            case 'boolean':
              parameters[param.id] = control.checked;
              break;
              
            case 'enum':
              parameters[param.id] = control.value;
              break;
              
            case 'string':
              parameters[param.id] = control.value;
              break;
          }
          
          break;
        }
      }
    });
    
    // 应用模板到角色
    try {
      // 这里应该调用引擎的应用模板功能
      // 示例代码，实际实现需要与引擎集成
      console.log('应用模板:', template.name, '参数:', parameters);
    } catch (error) {
      console.error('应用模板失败:', error);
    }
  }
  
  // 初始化
  document.addEventListener('DOMContentLoaded', init);
})();
