import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';

// 临时模拟 Agones SDK
class MockAgonesSDK {
  async connect() {
    return Promise.resolve();
  }

  async ready() {
    return Promise.resolve();
  }

  async allocate() {
    return Promise.resolve();
  }

  async getGameServer() {
    return Promise.resolve({
      metadata: { name: 'mock-gameserver' },
      status: {
        state: 'Ready',
        address: 'localhost',
        ports: [{ port: 3030 }]
      }
    });
  }

  async shutdown() {
    return Promise.resolve();
  }

  health(callback: (err?: any) => void) {
    callback();
  }
}

/**
 * Agones游戏服务器管理服务
 * 负责与Agones SDK交互，管理游戏服务器的生命周期
 */
@Injectable()
export class AgonesService implements OnModuleInit {
  private readonly logger = new Logger(AgonesService.name);
  private agonesSDK: any;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private isConnected = false;
  private gameServerInfo: any = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 模块初始化时连接Agones SDK
   */
  async onModuleInit() {
    const enabled = this.configService.get<boolean>('AGONES_ENABLED', false);
    
    if (!enabled) {
      this.logger.warn('Agones集成未启用，跳过初始化');
      return;
    }
    
    try {
      this.agonesSDK = new MockAgonesSDK();
      await this.connect();
      this.startHealthCheck();
      this.logger.log('Agones SDK初始化成功');
    } catch (error) {
      this.logger.error(`Agones SDK初始化失败: ${error.message}`, error.stack);
      
      // 在本地开发环境中，Agones可能未运行，这不应该阻止服务启动
      if (this.configService.get<string>('NODE_ENV') === 'development') {
        this.logger.warn('在开发环境中继续运行，但Agones功能将不可用');
      } else {
        throw error;
      }
    }
  }

  /**
   * 连接到Agones SDK
   */
  async connect(): Promise<boolean> {
    if (!this.agonesSDK) {
      this.logger.error('Agones SDK未初始化');
      return false;
    }

    try {
      await this.agonesSDK.connect();
      this.isConnected = true;
      this.logger.log('已连接到Agones SDK');
      
      // 标记服务器为就绪状态
      await this.ready();
      
      return true;
    } catch (error) {
      this.isConnected = false;
      this.logger.error(`连接Agones SDK失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 启动健康检查
   */
  startHealthCheck(intervalMs: number = 1000): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(() => {
      if (this.isConnected && this.agonesSDK) {
        this.agonesSDK.health((err: any) => {
          if (err) {
            this.logger.error(`Agones健康检查失败: ${err.message}`, err.stack);
          }
        });
      }
    }, intervalMs);
    
    this.logger.log(`Agones健康检查已启动，间隔: ${intervalMs}ms`);
  }

  /**
   * 停止健康检查
   */
  stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      this.logger.log('Agones健康检查已停止');
    }
  }

  /**
   * 标记服务器为就绪状态
   */
  async ready(): Promise<boolean> {
    if (!this.isConnected || !this.agonesSDK) {
      this.logger.error('无法标记为就绪：Agones SDK未连接');
      return false;
    }

    try {
      await this.agonesSDK.ready();
      this.logger.log('游戏服务器已标记为就绪状态');
      
      // 获取游戏服务器信息
      this.gameServerInfo = await this.getGameServer();
      
      // 触发就绪事件
      this.eventEmitter.emit('agones.ready', this.gameServerInfo);
      
      return true;
    } catch (error) {
      this.logger.error(`标记游戏服务器为就绪状态失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 分配游戏服务器
   */
  async allocate(): Promise<boolean> {
    if (!this.isConnected || !this.agonesSDK) {
      this.logger.error('无法分配：Agones SDK未连接');
      return false;
    }

    try {
      await this.agonesSDK.allocate();
      this.logger.log('游戏服务器已分配');
      
      // 触发分配事件
      this.eventEmitter.emit('agones.allocated', this.gameServerInfo);
      
      return true;
    } catch (error) {
      this.logger.error(`分配游戏服务器失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 获取游戏服务器信息
   */
  async getGameServer(): Promise<any> {
    if (!this.isConnected || !this.agonesSDK) {
      this.logger.error('无法获取游戏服务器信息：Agones SDK未连接');
      return null;
    }

    try {
      const gameServer = await this.agonesSDK.getGameServer();
      return gameServer;
    } catch (error) {
      this.logger.error(`获取游戏服务器信息失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 关闭游戏服务器
   */
  async shutdown(): Promise<boolean> {
    if (!this.isConnected || !this.agonesSDK) {
      this.logger.error('无法关闭：Agones SDK未连接');
      return false;
    }

    try {
      await this.agonesSDK.shutdown();
      this.logger.log('游戏服务器已关闭');
      
      // 触发关闭事件
      this.eventEmitter.emit('agones.shutdown');
      
      return true;
    } catch (error) {
      this.logger.error(`关闭游戏服务器失败: ${error.message}`, error.stack);
      return false;
    }
  }
}
