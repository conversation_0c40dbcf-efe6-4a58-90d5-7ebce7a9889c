# DL引擎监控配置
# 包含 Prometheus、Grafana 和告警规则

# Prometheus 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: dl-engine-monitoring
  labels:
    app: prometheus
    component: config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      # Kubernetes API Server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
        - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https
      
      # Kubernetes Nodes
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
        - role: node
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)
        - target_label: __address__
          replacement: kubernetes.default.svc:443
        - source_labels: [__meta_kubernetes_node_name]
          regex: (.+)
          target_label: __metrics_path__
          replacement: /api/v1/nodes/${1}/proxy/metrics
      
      # DL引擎微服务
      - job_name: 'dl-engine-services'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - dl-engine
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
          target_label: __address__
        - action: labelmap
          regex: __meta_kubernetes_service_label_(.+)
        - source_labels: [__meta_kubernetes_namespace]
          action: replace
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_service_name]
          action: replace
          target_label: kubernetes_name
      
      # MySQL 监控
      - job_name: 'mysql'
        static_configs:
        - targets: ['mysql-exporter:9104']
        scrape_interval: 30s
      
      # Redis 监控
      - job_name: 'redis'
        static_configs:
        - targets: ['redis-exporter:9121']
        scrape_interval: 30s
      
      # MinIO 监控
      - job_name: 'minio'
        static_configs:
        - targets: ['minio:9000']
        metrics_path: /minio/v2/metrics/cluster
        scrape_interval: 30s

---
# Prometheus 告警规则
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: dl-engine-monitoring
  labels:
    app: prometheus
    component: rules
data:
  dl-engine-alerts.yml: |
    groups:
    - name: dl-engine-alerts
      rules:
      # 服务可用性告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务 {{ $labels.instance }} 已停止"
          description: "服务 {{ $labels.instance }} 已停止超过1分钟"
      
      # CPU 使用率告警
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "容器 {{ $labels.name }} CPU使用率过高"
          description: "容器 {{ $labels.name }} CPU使用率超过80%"
      
      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "容器 {{ $labels.name }} 内存使用率过高"
          description: "容器 {{ $labels.name }} 内存使用率超过90%"
      
      # 数据库连接告警
      - alert: DatabaseConnectionError
        expr: mysql_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "数据库连接失败"
          description: "MySQL数据库连接失败超过1分钟"
      
      # Redis 连接告警
      - alert: RedisConnectionError
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis连接失败"
          description: "Redis缓存连接失败超过1分钟"
      
      # 磁盘空间告警
      - alert: DiskSpaceHigh
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "磁盘空间不足"
          description: "节点 {{ $labels.instance }} 磁盘使用率超过85%"
      
      # API 响应时间告警
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "API响应时间过长"
          description: "API 95%分位响应时间超过2秒"
      
      # 错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "错误率过高"
          description: "服务错误率超过10%"

---
# Grafana 数据源配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: dl-engine-monitoring
  labels:
    app: grafana
    component: datasources
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus:9090
      isDefault: true
      editable: true
    - name: Elasticsearch
      type: elasticsearch
      access: proxy
      url: http://elasticsearch:9200
      database: "logstash-*"
      jsonData:
        interval: Daily
        timeField: "@timestamp"
        esVersion: 70

---
# Grafana 仪表板配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards-config
  namespace: dl-engine-monitoring
  labels:
    app: grafana
    component: dashboards
data:
  dashboards.yaml: |
    apiVersion: 1
    providers:
    - name: 'default'
      orgId: 1
      folder: ''
      type: file
      disableDeletion: false
      updateIntervalSeconds: 10
      allowUiUpdates: true
      options:
        path: /var/lib/grafana/dashboards

---
# AlertManager 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: dl-engine-monitoring
  labels:
    app: alertmanager
    component: config
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'localhost:587'
      smtp_from: '<EMAIL>'
    
    route:
      group_by: ['alertname']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'web.hook'
    
    receivers:
    - name: 'web.hook'
      email_configs:
      - to: '<EMAIL>'
        subject: 'DL引擎告警: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          标签: {{ .Labels }}
          {{ end }}
      webhook_configs:
      - url: 'http://webhook-service:5000/alerts'
        send_resolved: true

---
# ServiceMonitor 用于自动发现服务
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: dl-engine-services
  namespace: dl-engine-monitoring
  labels:
    app: dl-engine
    component: monitoring
spec:
  namespaceSelector:
    matchNames:
    - dl-engine
  selector:
    matchLabels:
      monitoring: enabled
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
