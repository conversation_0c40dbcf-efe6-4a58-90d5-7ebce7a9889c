/**
 * 水体材质
 * 用于渲染水体，包括反射、折射、波动等效果
 */
import * as THREE from 'three';
import { WaterBodyType } from '../../physics/water/WaterBodyComponent';
/**
 * 水体材质配置
 */
export interface WaterMaterialConfig {
    /** 水体类型 */
    type?: WaterBodyType;
    /** 水体颜色 */
    color?: THREE.Color | number | string;
    /** 水体透明度 */
    opacity?: number;
    /** 水体反射率 */
    reflectivity?: number;
    /** 水体折射率 */
    refractionRatio?: number;
    /** 水体波动幅度 */
    waveStrength?: number;
    /** 水体波动速度 */
    waveSpeed?: number;
    /** 水体波动尺寸 */
    waveScale?: number;
    /** 水体波动方向 */
    waveDirection?: THREE.Vector2;
    /** 水体深度 */
    depth?: number;
    /** 水体深度颜色 */
    depthColor?: THREE.Color | number | string;
    /** 水体浅水颜色 */
    shallowColor?: THREE.Color | number | string;
    /** 水体法线贴图 */
    normalMap?: THREE.Texture;
    /** 水体反射贴图 */
    reflectionMap?: THREE.Texture;
    /** 水体折射贴图 */
    refractionMap?: THREE.Texture;
    /** 水体深度贴图 */
    depthMap?: THREE.Texture;
    /** 水体因果波纹贴图 */
    causticsMap?: THREE.Texture;
    /** 水体泡沫贴图 */
    foamMap?: THREE.Texture;
    /** 是否启用反射 */
    enableReflection?: boolean;
    /** 是否启用折射 */
    enableRefraction?: boolean;
    /** 是否启用因果波纹 */
    enableCaustics?: boolean;
    /** 是否启用泡沫 */
    enableFoam?: boolean;
    /** 是否启用水下雾效 */
    enableUnderwaterFog?: boolean;
    /** 是否启用水下扭曲 */
    enableUnderwaterDistortion?: boolean;
}
/**
 * 水体材质
 */
export declare class WaterMaterial extends THREE.ShaderMaterial {
    /** 水体类型 */
    private waterBodyType;
    /** 时间 */
    private time;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: WaterMaterialConfig);
    /**
     * 获取水体类型
     * @returns 水体类型
     */
    getWaterBodyType(): WaterBodyType;
    /**
     * 更新材质
     * @param deltaTime 帧间隔时间（秒）
     * @param camera 相机
     */
    update(deltaTime: number, camera: THREE.Camera): void;
    /**
     * 设置反射贴图
     * @param texture 反射贴图
     */
    setReflectionMap(texture: THREE.Texture): void;
    /**
     * 设置折射贴图
     * @param texture 折射贴图
     */
    setRefractionMap(texture: THREE.Texture): void;
    /**
     * 设置深度贴图
     * @param texture 深度贴图
     */
    setDepthMap(texture: THREE.Texture): void;
    /**
     * 设置因果波纹贴图
     * @param texture 因果波纹贴图
     */
    setCausticsMap(texture: THREE.Texture): void;
    /**
     * 设置法线贴图
     * @param texture 法线贴图
     */
    setNormalMap(texture: THREE.Texture): void;
    /**
     * 设置泡沫贴图
     * @param texture 泡沫贴图
     */
    setFoamMap(texture: THREE.Texture): void;
    /**
     * 设置高度图
     * @param texture 高度图
     * @param scale 缩放系数
     */
    setHeightMap(texture: THREE.Texture, scale?: number): void;
    /**
     * 设置因果波纹强度
     * @param intensity 强度
     */
    setCausticsIntensity(intensity: number): void;
    /**
     * 设置泡沫强度
     * @param intensity 强度
     */
    setFoamIntensity(intensity: number): void;
    /**
     * 设置水下雾效参数
     * @param density 密度
     * @param color 颜色
     */
    setUnderwaterFog(density: number, color?: THREE.Color): void;
    /**
     * 设置水体颜色
     * @param color 颜色
     */
    setColor(color: THREE.Color | number | string): void;
    /**
     * 设置透明度
     * @param opacity 透明度
     */
    setOpacity(opacity: number): void;
    /**
     * 设置反射率
     * @param reflectivity 反射率
     */
    setReflectivity(reflectivity: number): void;
    /**
     * 设置折射率
     * @param refractionRatio 折射率
     */
    setRefractionRatio(refractionRatio: number): void;
    /**
     * 设置波动强度
     * @param waveStrength 波动强度
     */
    setWaveStrength(waveStrength: number): void;
    /**
     * 设置波动速度
     * @param waveSpeed 波动速度
     */
    setWaveSpeed(waveSpeed: number): void;
    /**
     * 设置波动尺寸
     * @param waveScale 波动尺寸
     */
    setWaveScale(waveScale: number): void;
    /**
     * 设置波动方向
     * @param waveDirection 波动方向
     */
    setWaveDirection(waveDirection: THREE.Vector2): void;
    /**
     * 设置深度
     * @param depth 深度
     */
    setDepth(depth: number): void;
    /**
     * 设置深度颜色
     * @param depthColor 深度颜色
     */
    setDepthColor(depthColor: THREE.Color | number | string): void;
    /**
     * 设置浅水颜色
     * @param shallowColor 浅水颜色
     */
    setShallowColor(shallowColor: THREE.Color | number | string): void;
    /**
     * 启用/禁用因果波纹
     * @param enabled 是否启用
     */
    enableCaustics(enabled: boolean): void;
    /**
     * 启用/禁用泡沫
     * @param enabled 是否启用
     */
    enableFoam(enabled: boolean): void;
    /**
     * 启用/禁用水下雾效
     * @param enabled 是否启用
     */
    enableUnderwaterFog(enabled: boolean): void;
    /**
     * 启用/禁用水下扭曲
     * @param enabled 是否启用
     */
    enableUnderwaterDistortion(enabled: boolean): void;
    /**
     * 创建着色器
     * @param _type 水体类型（保留用于未来扩展）
     * @param enableReflection 是否启用反射
     * @param enableRefraction 是否启用折射
     * @param enableCaustics 是否启用因果波纹
     * @param enableFoam 是否启用泡沫
     * @param enableUnderwaterFog 是否启用水下雾效
     * @param enableUnderwaterDistortion 是否启用水下扭曲
     * @returns 着色器
     */
    private static createShader;
}
