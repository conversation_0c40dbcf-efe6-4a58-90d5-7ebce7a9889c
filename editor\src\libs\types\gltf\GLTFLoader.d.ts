import { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { GLTF } from './components/GLTFModelComponent';
/**
 * GLTF加载器选项
 */
export interface GLTFLoaderOptions {
    /** 是否使用Draco压缩 */
    useDraco?: boolean;
    /** Draco解码器路径 */
    dracoDecoderPath?: string;
    /** 是否使用KTX2纹理 */
    useKTX2?: boolean;
    /** KTX2解码器路径 */
    ktx2DecoderPath?: string;
    /** 是否加载动画 */
    loadAnimations?: boolean;
    /** 是否加载相机 */
    loadCameras?: boolean;
    /** 是否加载灯光 */
    loadLights?: boolean;
    /** 是否优化几何体 */
    optimizeGeometry?: boolean;
}
/**
 * GLTF加载器
 */
export declare class GLTFLoader extends EventEmitter {
    /** Three.js GLTF加载器 */
    private loader;
    /** Draco加载器 */
    private dracoLoader;
    /** KTX2加载器 */
    private ktx2Loader;
    /** 加载选项 */
    private options;
    /**
     * 创建GLTF加载器
     * @param options 加载选项
     */
    constructor(options?: GLTFLoaderOptions);
    /**
     * 加载GLTF模型
     * @param url 模型URL
     * @returns Promise，解析为加载的GLTF模型
     */
    load(url: string): Promise<GLTF>;
    /**
     * 优化几何体
     * @param gltf GLTF模型
     */
    private optimizeGeometry;
    /**
     * 创建实体
     * @param gltf GLTF模型
     * @param entity 父实体
     * @returns 创建的根实体
     */
    createEntity(gltf: GLTF, entity: Entity): Entity;
    /**
     * 处理节点
     * @param node Three.js对象
     * @param parentEntity 父实体
     * @param gltf GLTF模型
     */
    private processNode;
    /**
     * 设置基础路径
     * @param path 基础路径
     */
    setPath(path: string): void;
    /**
     * 设置资源路径
     * @param path 资源路径
     */
    setResourcePath(path: string): void;
    /**
     * 设置跨域
     * @param crossOrigin 跨域设置
     */
    setCrossOrigin(crossOrigin: string): void;
    /**
     * 销毁加载器
     */
    dispose(): void;
}
