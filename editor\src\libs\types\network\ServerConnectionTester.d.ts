/**
 * 服务器连接测试器
 * 用于测试与服务器的连接状态和质量
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 服务器连接测试配置
 */
export interface ServerConnectionTestConfig {
    /** 服务器URL */
    serverUrl: string;
    /** 测试超时时间（毫秒） */
    timeout?: number;
    /** 测试重试次数 */
    retries?: number;
    /** 是否启用详细日志 */
    detailedLogging?: boolean;
    /** 是否测试WebSocket连接 */
    testWebSocket?: boolean;
    /** 是否测试HTTP连接 */
    testHttp?: boolean;
    /** 是否测试服务可用性 */
    testAvailability?: boolean;
    /** 是否测试服务响应时间 */
    testResponseTime?: boolean;
    /** 是否测试服务器状态 */
    testServerStatus?: boolean;
    /** 是否测试DNS解析 */
    testDnsResolution?: boolean;
    /** 是否测试SSL证书 */
    testSslCertificate?: boolean;
    /** 是否测试服务器地理位置 */
    testGeoLocation?: boolean;
    /** 是否测试路由跟踪 */
    testRouteTrace?: boolean;
}
/**
 * 服务器连接测试结果
 */
export interface ServerConnectionTestResult {
    /** 测试是否成功 */
    success: boolean;
    /** 错误信息 */
    error?: string;
    /** 测试开始时间 */
    startTime: number;
    /** 测试结束时间 */
    endTime: number;
    /** 测试持续时间（毫秒） */
    duration: number;
    /** 服务器是否可用 */
    available?: boolean;
    /** 服务器响应时间（毫秒） */
    responseTime?: number;
    /** WebSocket连接测试结果 */
    webSocketTest?: {
        /** 是否成功 */
        success: boolean;
        /** 连接时间（毫秒） */
        connectionTime: number;
        /** 错误信息 */
        error?: string;
    };
    /** HTTP连接测试结果 */
    httpTest?: {
        /** 是否成功 */
        success: boolean;
        /** 响应时间（毫秒） */
        responseTime: number;
        /** 状态码 */
        statusCode: number;
        /** 错误信息 */
        error?: string;
    };
    /** 服务器状态测试结果 */
    serverStatusTest?: {
        /** 是否成功 */
        success: boolean;
        /** 服务器状态 */
        status: string;
        /** 服务器版本 */
        version?: string;
        /** 服务器负载 */
        load?: number;
        /** 错误信息 */
        error?: string;
    };
    /** DNS解析测试结果 */
    dnsResolutionTest?: {
        /** 是否成功 */
        success: boolean;
        /** 解析时间（毫秒） */
        resolutionTime: number;
        /** IP地址 */
        ipAddress?: string;
        /** 错误信息 */
        error?: string;
    };
    /** SSL证书测试结果 */
    sslCertificateTest?: {
        /** 是否成功 */
        success: boolean;
        /** 证书是否有效 */
        valid: boolean;
        /** 证书过期时间 */
        expiryDate?: Date;
        /** 证书颁发者 */
        issuer?: string;
        /** 错误信息 */
        error?: string;
    };
    /** 服务器地理位置测试结果 */
    geoLocationTest?: {
        /** 是否成功 */
        success: boolean;
        /** 国家 */
        country?: string;
        /** 城市 */
        city?: string;
        /** 经度 */
        longitude?: number;
        /** 纬度 */
        latitude?: number;
        /** 错误信息 */
        error?: string;
    };
    /** 路由跟踪测试结果 */
    routeTraceTest?: {
        /** 是否成功 */
        success: boolean;
        /** 跳数 */
        hops: number;
        /** 路由节点 */
        nodes: Array<{
            /** 节点IP */
            ip: string;
            /** 节点响应时间（毫秒） */
            responseTime: number;
            /** 节点位置 */
            location?: string;
        }>;
        /** 错误信息 */
        error?: string;
    };
}
/**
 * 服务器连接测试器
 * 用于测试与服务器的连接状态和质量
 */
export declare class ServerConnectionTester extends EventEmitter {
    /** 配置 */
    private config;
    /** 当前测试结果 */
    private currentResult;
    /** 测试是否正在进行 */
    private testing;
    /** WebSocket连接 */
    private ws;
    /** 测试超时定时器ID */
    private timeoutId;
    /**
     * 创建服务器连接测试器
     * @param config 配置
     */
    constructor(config: ServerConnectionTestConfig);
    /**
     * 开始测试
     * @returns 测试结果Promise
     */
    startTest(): Promise<ServerConnectionTestResult>;
    /**
     * 取消测试
     */
    cancelTest(): void;
    /**
     * 设置测试超时
     */
    private setTestTimeout;
    /**
     * 清除测试超时
     */
    private clearTestTimeout;
    /**
     * 重置测试结果
     */
    private resetTestResult;
    /**
     * 获取测试结果
     * @returns 测试结果
     */
    getTestResult(): ServerConnectionTestResult;
    /**
     * 测试服务器可用性
     */
    private testAvailability;
    /**
     * 测试WebSocket连接
     */
    private testWebSocketConnection;
    /**
     * 测试HTTP连接
     */
    private testHttpConnection;
    /**
     * 测试服务器状态
     */
    private testServerStatus;
    /**
     * 测试DNS解析
     */
    private testDnsResolution;
    /**
     * 测试SSL证书
     */
    private testSslCertificate;
    /**
     * 测试服务器地理位置
     */
    private testGeoLocation;
    /**
     * 测试路由跟踪
     */
    private testRouteTrace;
    /**
     * 销毁测试器
     */
    dispose(): void;
}
