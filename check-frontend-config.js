/**
 * 前端配置检查脚本
 * 检查前端项目的配置是否正确设置为连接后端服务
 */

const fs = require('fs');
const path = require('path');

// 检查结果
const checkResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  issues: []
};

/**
 * 记录检查结果
 */
function logResult(type, message, details = null) {
  const symbols = { pass: '✅', fail: '❌', warn: '⚠️' };
  console.log(`${symbols[type]} ${message}`);
  
  if (details) {
    console.log(`   ${details}`);
  }
  
  checkResults[type === 'pass' ? 'passed' : type === 'fail' ? 'failed' : 'warnings']++;
  
  if (type !== 'pass') {
    checkResults.issues.push({ type, message, details });
  }
}

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    logResult('pass', `${description} 存在`);
    return true;
  } else {
    logResult('fail', `${description} 不存在`, filePath);
    return false;
  }
}

/**
 * 检查文件内容
 */
function checkFileContent(filePath, pattern, description, shouldMatch = true) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const matches = pattern.test(content);
    
    if (matches === shouldMatch) {
      logResult('pass', description);
      return true;
    } else {
      logResult('fail', description);
      return false;
    }
  } catch (error) {
    logResult('fail', `读取文件失败: ${description}`, error.message);
    return false;
  }
}

/**
 * 检查环境配置
 */
function checkEnvironmentConfig() {
  console.log('\n🔧 检查环境配置...');
  
  const envPath = 'editor/src/config/environment.ts';
  
  if (!checkFileExists(envPath, '环境配置文件')) {
    return;
  }
  
  // 检查开发环境配置
  checkFileContent(
    envPath,
    /enableMockData:\s*false/,
    '开发环境已禁用Mock数据'
  );
  
  checkFileContent(
    envPath,
    /apiUrl:\s*['"]http:\/\/localhost:3000\/api['"]/,
    '开发环境API地址配置正确'
  );
  
  checkFileContent(
    envPath,
    /userService:\s*['"]http:\/\/localhost:3000\/api['"]/,
    '用户服务地址配置正确'
  );
  
  checkFileContent(
    envPath,
    /projectService:\s*['"]http:\/\/localhost:3000\/api['"]/,
    '项目服务地址配置正确'
  );
  
  checkFileContent(
    envPath,
    /assetService:\s*['"]http:\/\/localhost:3000\/api['"]/,
    '资产服务地址配置正确'
  );
  
  checkFileContent(
    envPath,
    /renderService:\s*['"]http:\/\/localhost:3000\/api['"]/,
    '渲染服务地址配置正确'
  );
}

/**
 * 检查API客户端配置
 */
function checkApiClientConfig() {
  console.log('\n🌐 检查API客户端配置...');
  
  const apiClientPath = 'editor/src/services/ApiClient.ts';
  
  if (!checkFileExists(apiClientPath, 'API客户端文件')) {
    return;
  }
  
  checkFileContent(
    apiClientPath,
    /return\s+['"]http:\/\/localhost:3000\/api['"]/,
    'API客户端开发环境URL配置正确'
  );
}

/**
 * 检查认证服务配置
 */
function checkAuthServiceConfig() {
  console.log('\n🔐 检查认证服务配置...');
  
  const authSlicePath = 'editor/src/store/auth/authSlice.ts';
  
  if (!checkFileExists(authSlicePath, '认证状态切片文件')) {
    return;
  }
  
  checkFileContent(
    authSlicePath,
    /http:\/\/localhost:3000\/api\/auth\/login/,
    '登录API地址配置正确'
  );
  
  checkFileContent(
    authSlicePath,
    /http:\/\/localhost:3000\/api\/auth\/register/,
    '注册API地址配置正确'
  );
  
  checkFileContent(
    authSlicePath,
    /http:\/\/localhost:3000\/api\/auth\/profile/,
    '用户信息API地址配置正确'
  );
}

/**
 * 检查项目服务配置
 */
function checkProjectServiceConfig() {
  console.log('\n📁 检查项目服务配置...');
  
  const projectSlicePath = 'editor/src/store/project/projectSlice.ts';
  
  if (!checkFileExists(projectSlicePath, '项目状态切片文件')) {
    return;
  }
  
  checkFileContent(
    projectSlicePath,
    /http:\/\/localhost:3000\/api\/projects/,
    '项目API地址配置正确'
  );
}

/**
 * 检查资产服务配置
 */
function checkAssetServiceConfig() {
  console.log('\n🎨 检查资产服务配置...');
  
  const assetServicePath = 'editor/src/services/AssetService.ts';
  
  if (!checkFileExists(assetServicePath, '资产服务文件')) {
    return;
  }
  
  checkFileContent(
    assetServicePath,
    /http:\/\/localhost:3000\/api\/assets/,
    '资产API地址配置正确'
  );
  
  const materialServicePath = 'editor/src/services/materialService.ts';
  
  if (checkFileExists(materialServicePath, '材质服务文件')) {
    checkFileContent(
      materialServicePath,
      /http:\/\/localhost:3000\/api\/materials/,
      '材质API地址配置正确'
    );
  }
}

/**
 * 检查示例服务配置
 */
function checkExampleServiceConfig() {
  console.log('\n📚 检查示例服务配置...');
  
  const exampleServicePath = 'editor/src/services/exampleService.ts';
  
  if (!checkFileExists(exampleServicePath, '示例服务文件')) {
    return;
  }
  
  checkFileContent(
    exampleServicePath,
    /http:\/\/localhost:3000\/api\/examples/,
    '示例API地址配置正确'
  );
  
  // 检查是否有后备机制
  checkFileContent(
    exampleServicePath,
    /使用模拟数据作为后备/,
    '示例服务有后备机制'
  );
}

/**
 * 检查包依赖
 */
function checkDependencies() {
  console.log('\n📦 检查包依赖...');
  
  const packageJsonPath = 'editor/package.json';
  
  if (!checkFileExists(packageJsonPath, 'package.json文件')) {
    return;
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const requiredDeps = ['axios', 'antd', '@reduxjs/toolkit'];
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        logResult('pass', `依赖 ${dep} 已安装`);
      } else {
        logResult('fail', `缺少依赖 ${dep}`);
      }
    });
  } catch (error) {
    logResult('fail', '解析package.json失败', error.message);
  }
}

/**
 * 主检查函数
 */
function runChecks() {
  console.log('🔍 开始检查前端配置...\n');
  
  // 执行各项检查
  checkEnvironmentConfig();
  checkApiClientConfig();
  checkAuthServiceConfig();
  checkProjectServiceConfig();
  checkAssetServiceConfig();
  checkExampleServiceConfig();
  checkDependencies();
  
  // 输出检查结果
  console.log('\n📊 检查结果汇总:');
  console.log(`✅ 通过: ${checkResults.passed}`);
  console.log(`❌ 失败: ${checkResults.failed}`);
  console.log(`⚠️  警告: ${checkResults.warnings}`);
  
  const total = checkResults.passed + checkResults.failed + checkResults.warnings;
  if (total > 0) {
    console.log(`📈 通过率: ${((checkResults.passed / total) * 100).toFixed(1)}%`);
  }
  
  if (checkResults.issues.length > 0) {
    console.log('\n❌ 发现的问题:');
    checkResults.issues.forEach((issue, index) => {
      console.log(`${index + 1}. [${issue.type.toUpperCase()}] ${issue.message}`);
      if (issue.details) {
        console.log(`   ${issue.details}`);
      }
    });
  }
  
  console.log('\n🏁 检查完成!');
  
  if (checkResults.failed > 0) {
    console.log('\n💡 建议: 请修复上述配置问题后再进行前后端联调测试');
    return false;
  } else {
    console.log('\n✨ 前端配置看起来正确，可以进行前后端联调测试');
    return true;
  }
}

// 运行检查
if (require.main === module) {
  const success = runChecks();
  process.exit(success ? 0 : 1);
}

module.exports = { runChecks, checkResults };
