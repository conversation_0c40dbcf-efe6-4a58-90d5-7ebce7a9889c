import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { GatewayController } from './gateway.controller';
import { GatewayService } from './gateway.service';
import { ServiceDiscoveryService } from './service-discovery.service';
import { LoadBalancerService } from './load-balancer.service';
import { HealthCheckService } from './health-check.service';

@Module({
  imports: [ConfigModule],
  controllers: [GatewayController],
  providers: [
    GatewayService,
    ServiceDiscoveryService,
    LoadBalancerService,
    HealthCheckService,
  ],
  exports: [
    GatewayService,
    ServiceDiscoveryService,
    LoadBalancerService,
    HealthCheckService,
  ],
})
export class GatewayModule {}
