events {
    worker_connections 1024;
}

http {
    upstream scene_generation {
        server scene-generation-service:3004;
    }

    upstream rag_service {
        server rag-service:3001;
    }

    upstream digital_human {
        server digital-human-service:3002;
    }

    # 场景生成服务
    server {
        listen 80;
        server_name scene.localhost;

        location / {
            proxy_pass http://scene_generation;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket支持
        location /socket.io/ {
            proxy_pass http://scene_generation;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # RAG服务
    server {
        listen 80;
        server_name rag.localhost;

        location / {
            proxy_pass http://rag_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # 数字人服务
    server {
        listen 80;
        server_name digital.localhost;

        location / {
            proxy_pass http://digital_human;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # 默认服务（场景生成）
    server {
        listen 80 default_server;

        location / {
            proxy_pass http://scene_generation;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
