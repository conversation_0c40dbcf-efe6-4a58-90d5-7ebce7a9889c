/**
 * 脚本缓存服务
 * 提供脚本编译结果缓存、智能预加载和性能优化功能
 */

export interface CacheEntry {
  id: string;
  scriptId: string;
  content: string;
  contentHash: string;
  compiledResult: any;
  compiledAt: Date;
  lastAccessed: Date;
  accessCount: number;
  size: number;
  dependencies?: string[];
  metadata?: Record<string, any>;
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  totalHits: number;
  totalMisses: number;
  averageCompileTime: number;
  oldestEntry?: Date;
  newestEntry?: Date;
}

export interface CacheConfig {
  maxSize: number; // 最大缓存大小（字节）
  maxEntries: number; // 最大缓存条目数
  ttl: number; // 生存时间（毫秒）
  cleanupInterval: number; // 清理间隔（毫秒）
  preloadThreshold: number; // 预加载阈值
  compressionEnabled: boolean; // 是否启用压缩
  persistToDisk: boolean; // 是否持久化到磁盘
}

class ScriptCacheService {
  private static instance: ScriptCacheService;
  private cache: Map<string, CacheEntry> = new Map();
  private stats: CacheStats;
  private config: CacheConfig;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private compressionWorker: Worker | null = null;

  constructor() {
    this.stats = this.initializeStats();
    this.config = this.getDefaultConfig();
    this.loadConfig();
    this.loadCacheFromStorage();
    this.startCleanupTimer();
    this.initializeCompressionWorker();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): ScriptCacheService {
    if (!ScriptCacheService.instance) {
      ScriptCacheService.instance = new ScriptCacheService();
    }
    return ScriptCacheService.instance;
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): CacheStats {
    return {
      totalEntries: 0,
      totalSize: 0,
      hitRate: 0,
      missRate: 0,
      totalHits: 0,
      totalMisses: 0,
      averageCompileTime: 0
    };
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): CacheConfig {
    return {
      maxSize: 50 * 1024 * 1024, // 50MB
      maxEntries: 1000,
      ttl: 24 * 60 * 60 * 1000, // 24小时
      cleanupInterval: 5 * 60 * 1000, // 5分钟
      preloadThreshold: 3, // 访问3次后预加载
      compressionEnabled: true,
      persistToDisk: true
    };
  }

  /**
   * 加载配置
   */
  private loadConfig(): void {
    try {
      const saved = localStorage.getItem('scriptCacheConfig');
      if (saved) {
        const config = JSON.parse(saved);
        this.config = { ...this.config, ...config };
      }
    } catch (error) {
      console.error('加载缓存配置失败:', error);
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      localStorage.setItem('scriptCacheConfig', JSON.stringify(this.config));
    } catch (error) {
      console.error('保存缓存配置失败:', error);
    }
  }

  /**
   * 从存储加载缓存
   */
  private loadCacheFromStorage(): void {
    if (!this.config.persistToDisk) return;

    try {
      const saved = localStorage.getItem('scriptCache');
      if (saved) {
        const cacheData = JSON.parse(saved);
        
        for (const [key, entry] of Object.entries(cacheData)) {
          const cacheEntry = entry as CacheEntry;
          cacheEntry.compiledAt = new Date(cacheEntry.compiledAt);
          cacheEntry.lastAccessed = new Date(cacheEntry.lastAccessed);
          
          // 检查是否过期
          if (this.isExpired(cacheEntry)) {
            continue;
          }
          
          this.cache.set(key, cacheEntry);
        }
        
        this.updateStats();
      }
    } catch (error) {
      console.error('从存储加载缓存失败:', error);
    }
  }

  /**
   * 保存缓存到存储
   */
  private saveCacheToStorage(): void {
    if (!this.config.persistToDisk) return;

    try {
      const cacheData: Record<string, CacheEntry> = {};
      
      for (const [key, entry] of this.cache.entries()) {
        cacheData[key] = entry;
      }
      
      localStorage.setItem('scriptCache', JSON.stringify(cacheData));
    } catch (error) {
      console.error('保存缓存到存储失败:', error);
    }
  }

  /**
   * 初始化压缩工作线程
   */
  private initializeCompressionWorker(): void {
    if (!this.config.compressionEnabled || typeof Worker === 'undefined') {
      return;
    }

    try {
      // 这里可以创建一个Web Worker来处理压缩
      // 由于示例中没有Worker文件，这里只是占位
      console.log('压缩工作线程已初始化');
    } catch (error) {
      console.error('初始化压缩工作线程失败:', error);
    }
  }

  /**
   * 开始清理定时器
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanupExpired();
    }, this.config.cleanupInterval);
  }

  /**
   * 生成内容哈希
   */
  private generateHash(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }

  /**
   * 检查缓存条目是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    const now = new Date();
    return (now.getTime() - entry.compiledAt.getTime()) > this.config.ttl;
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.totalEntries = this.cache.size;
    this.stats.totalSize = Array.from(this.cache.values())
      .reduce((total, entry) => total + entry.size, 0);
    
    if (this.stats.totalHits + this.stats.totalMisses > 0) {
      this.stats.hitRate = this.stats.totalHits / (this.stats.totalHits + this.stats.totalMisses);
      this.stats.missRate = this.stats.totalMisses / (this.stats.totalHits + this.stats.totalMisses);
    }

    const entries = Array.from(this.cache.values());
    if (entries.length > 0) {
      this.stats.oldestEntry = entries.reduce((oldest, entry) => 
        entry.compiledAt < oldest ? entry.compiledAt : oldest, entries[0].compiledAt);
      this.stats.newestEntry = entries.reduce((newest, entry) => 
        entry.compiledAt > newest ? entry.compiledAt : newest, entries[0].compiledAt);
    }
  }

  /**
   * 获取缓存键
   */
  private getCacheKey(scriptId: string, content: string): string {
    const hash = this.generateHash(content);
    return `${scriptId}:${hash}`;
  }

  /**
   * 检查是否需要清理空间
   */
  private needsCleanup(): boolean {
    return this.cache.size >= this.config.maxEntries || 
           this.stats.totalSize >= this.config.maxSize;
  }

  /**
   * 清理过期和最少使用的缓存条目
   */
  private cleanupExpired(): void {
    const now = new Date();
    const entriesToRemove: string[] = [];

    // 移除过期条目
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        entriesToRemove.push(key);
      }
    }

    // 如果仍然需要清理空间，移除最少使用的条目
    if (this.needsCleanup()) {
      const entries = Array.from(this.cache.entries())
        .filter(([key]) => !entriesToRemove.includes(key))
        .sort(([, a], [, b]) => {
          // 按访问频率和最后访问时间排序
          const scoreA = a.accessCount / (now.getTime() - a.lastAccessed.getTime());
          const scoreB = b.accessCount / (now.getTime() - b.lastAccessed.getTime());
          return scoreA - scoreB;
        });

      const removeCount = Math.max(
        this.cache.size - this.config.maxEntries + entriesToRemove.length,
        Math.ceil(this.cache.size * 0.1) // 至少清理10%
      );

      for (let i = 0; i < removeCount && i < entries.length; i++) {
        entriesToRemove.push(entries[i][0]);
      }
    }

    // 执行清理
    entriesToRemove.forEach(key => this.cache.delete(key));

    if (entriesToRemove.length > 0) {
      console.log(`缓存清理完成，移除了 ${entriesToRemove.length} 个条目`);
      this.updateStats();
      this.saveCacheToStorage();
    }
  }

  /**
   * 获取缓存条目
   */
  public get(scriptId: string, content: string): any | null {
    const key = this.getCacheKey(scriptId, content);
    const entry = this.cache.get(key);

    if (entry && !this.isExpired(entry)) {
      // 更新访问信息
      entry.lastAccessed = new Date();
      entry.accessCount++;
      
      this.stats.totalHits++;
      this.updateStats();
      
      // 检查是否需要预加载相关脚本
      if (entry.accessCount >= this.config.preloadThreshold) {
        this.preloadDependencies(entry);
      }
      
      return entry.compiledResult;
    }

    this.stats.totalMisses++;
    this.updateStats();
    return null;
  }

  /**
   * 设置缓存条目
   */
  public set(scriptId: string, content: string, compiledResult: any, dependencies?: string[]): void {
    const key = this.getCacheKey(scriptId, content);
    const contentHash = this.generateHash(content);
    const size = this.estimateSize(compiledResult) + content.length * 2; // 估算大小

    const entry: CacheEntry = {
      id: key,
      scriptId,
      content,
      contentHash,
      compiledResult,
      compiledAt: new Date(),
      lastAccessed: new Date(),
      accessCount: 1,
      size,
      dependencies,
      metadata: {}
    };

    // 检查是否需要清理空间
    if (this.needsCleanup()) {
      this.cleanupExpired();
    }

    this.cache.set(key, entry);
    this.updateStats();
    this.saveCacheToStorage();
  }

  /**
   * 估算对象大小
   */
  private estimateSize(obj: any): number {
    const jsonString = JSON.stringify(obj);
    return jsonString.length * 2; // 假设每个字符占2字节
  }

  /**
   * 预加载依赖项
   */
  private preloadDependencies(entry: CacheEntry): void {
    if (!entry.dependencies || entry.dependencies.length === 0) {
      return;
    }

    // 这里可以实现依赖项的预加载逻辑
    console.log(`预加载脚本 ${entry.scriptId} 的依赖项:`, entry.dependencies);
  }

  /**
   * 删除缓存条目
   */
  public delete(scriptId: string, content?: string): boolean {
    if (content) {
      const key = this.getCacheKey(scriptId, content);
      const result = this.cache.delete(key);
      if (result) {
        this.updateStats();
        this.saveCacheToStorage();
      }
      return result;
    } else {
      // 删除所有相关的缓存条目
      let deleted = false;
      for (const [key, entry] of this.cache.entries()) {
        if (entry.scriptId === scriptId) {
          this.cache.delete(key);
          deleted = true;
        }
      }
      if (deleted) {
        this.updateStats();
        this.saveCacheToStorage();
      }
      return deleted;
    }
  }

  /**
   * 清空缓存
   */
  public clear(): void {
    this.cache.clear();
    this.stats = this.initializeStats();
    this.saveCacheToStorage();
  }

  /**
   * 获取缓存统计信息
   */
  public getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 获取配置
   */
  public getConfig(): CacheConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(updates: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...updates };
    this.saveConfig();
    
    // 重新启动清理定时器
    if (updates.cleanupInterval) {
      this.startCleanupTimer();
    }
    
    // 重新初始化压缩工作线程
    if (updates.compressionEnabled !== undefined) {
      this.initializeCompressionWorker();
    }
  }

  /**
   * 获取所有缓存条目
   */
  public getAllEntries(): CacheEntry[] {
    return Array.from(this.cache.values());
  }

  /**
   * 获取指定脚本的缓存条目
   */
  public getEntriesForScript(scriptId: string): CacheEntry[] {
    return Array.from(this.cache.values())
      .filter(entry => entry.scriptId === scriptId);
  }

  /**
   * 预热缓存
   */
  public async warmup(scripts: Array<{ id: string; content: string }>): Promise<void> {
    console.log(`开始预热缓存，共 ${scripts.length} 个脚本`);
    
    for (const script of scripts) {
      try {
        // 这里可以调用编译服务来预编译脚本
        // const compiled = await compileScript(script.content);
        // this.set(script.id, script.content, compiled);
        console.log(`预热脚本: ${script.id}`);
      } catch (error) {
        console.error(`预热脚本 ${script.id} 失败:`, error);
      }
    }
    
    console.log('缓存预热完成');
  }

  /**
   * 导出缓存数据
   */
  public exportCache(): string {
    const data = {
      config: this.config,
      stats: this.stats,
      entries: Array.from(this.cache.entries())
    };
    return JSON.stringify(data, null, 2);
  }

  /**
   * 导入缓存数据
   */
  public importCache(data: string): boolean {
    try {
      const imported = JSON.parse(data);
      
      if (imported.entries) {
        this.cache.clear();
        for (const [key, entry] of imported.entries) {
          const cacheEntry = entry as CacheEntry;
          cacheEntry.compiledAt = new Date(cacheEntry.compiledAt);
          cacheEntry.lastAccessed = new Date(cacheEntry.lastAccessed);
          this.cache.set(key, cacheEntry);
        }
      }
      
      if (imported.config) {
        this.config = { ...this.config, ...imported.config };
      }
      
      this.updateStats();
      this.saveCacheToStorage();
      this.saveConfig();
      
      return true;
    } catch (error) {
      console.error('导入缓存数据失败:', error);
      return false;
    }
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    
    if (this.compressionWorker) {
      this.compressionWorker.terminate();
      this.compressionWorker = null;
    }
    
    this.saveCacheToStorage();
  }
}

export default ScriptCacheService;
