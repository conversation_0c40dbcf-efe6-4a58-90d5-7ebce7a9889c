import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { DocumentsService } from './documents.service';
import { CreateDocumentDto } from './dto/create-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { QueryDocumentDto } from './dto/query-document.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('documents')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('knowledge-bases/:knowledgeBaseId/documents')
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post('upload')
  @ApiOperation({ summary: '上传文档' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: '文档上传成功' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocument(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() createDocumentDto: CreateDocumentDto,
    @Request() req: any,
  ) {
    return this.documentsService.uploadDocument(
      knowledgeBaseId,
      file,
      createDocumentDto,
      req.user.id,
    );
  }

  @Post()
  @ApiOperation({ summary: '创建文档' })
  @ApiResponse({ status: 201, description: '文档创建成功' })
  async create(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Body() createDocumentDto: CreateDocumentDto,
    @Request() req: any,
  ) {
    return this.documentsService.create(knowledgeBaseId, createDocumentDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: '获取文档列表' })
  @ApiResponse({ status: 200, description: '获取文档列表成功' })
  async findAll(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Query() query: QueryDocumentDto,
    @Request() req: any,
  ) {
    return this.documentsService.findAll(knowledgeBaseId, query, req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取文档详情' })
  @ApiResponse({ status: 200, description: '获取文档详情成功' })
  async findOne(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Param('id') id: string,
    @Request() req: any,
  ) {
    return this.documentsService.findOne(knowledgeBaseId, id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新文档' })
  @ApiResponse({ status: 200, description: '文档更新成功' })
  async update(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Param('id') id: string,
    @Body() updateDocumentDto: UpdateDocumentDto,
    @Request() req: any,
  ) {
    return this.documentsService.update(knowledgeBaseId, id, updateDocumentDto, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除文档' })
  @ApiResponse({ status: 200, description: '文档删除成功' })
  async remove(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Param('id') id: string,
    @Request() req: any,
  ) {
    return this.documentsService.remove(knowledgeBaseId, id, req.user.id);
  }

  @Post(':id/reprocess')
  @ApiOperation({ summary: '重新处理文档' })
  @ApiResponse({ status: 200, description: '文档重新处理成功' })
  async reprocessDocument(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Param('id') id: string,
    @Request() req: any,
  ) {
    return this.documentsService.reprocessDocument(knowledgeBaseId, id, req.user.id);
  }
}
