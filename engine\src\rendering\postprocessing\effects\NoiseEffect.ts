/**
 * 噪点效果
 * 添加胶片颗粒或电子噪声
 */
import * as THREE from 'three';
// 使用类型断言导入 ShaderPass
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 噪点效果选项
 */
export interface NoiseEffectOptions extends PostProcessingEffectOptions {
  /** 强度 */
  intensity?: number;
  /** 是否使用彩色噪点 */
  colored?: boolean;
  /** 是否使用动态噪点 */
  animated?: boolean;
  /** 是否预乘 */
  premultiply?: boolean;
}

/**
 * 噪点效果
 */
export class NoiseEffect extends PostProcessingEffect {
  /** 强度 */
  private intensity: number;

  /** 是否使用彩色噪点 */
  private colored: boolean;

  /** 是否使用动态噪点 */
  private animated: boolean;

  /** 是否预乘 */
  private premultiply: boolean;

  /** 噪点通道 */
  private noisePass: ShaderPass | null = null;

  /** 时间 */
  private time: number = 0;

  /**
   * 创建噪点效果
   * @param options 噪点效果选项
   */
  constructor(options: NoiseEffectOptions = { name: 'Noise' }) {
    super(options);

    this.intensity = options.intensity !== undefined ? options.intensity : 0.5;
    this.colored = options.colored !== undefined ? options.colored : false;
    this.animated = options.animated !== undefined ? options.animated : true;
    this.premultiply = options.premultiply !== undefined ? options.premultiply : false;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 创建噪点着色器
    const noiseShader = {
      uniforms: {
        'tDiffuse': { value: null },
        'intensity': { value: this.intensity },
        'colored': { value: this.colored ? 1 : 0 },
        'time': { value: 0 },
        'premultiply': { value: this.premultiply ? 1 : 0 }
      },
      vertexShader: /* glsl */`
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: /* glsl */`
        uniform sampler2D tDiffuse;
        uniform float intensity;
        uniform int colored;
        uniform float time;
        uniform int premultiply;
        
        varying vec2 vUv;
        
        // 随机函数
        float random(vec2 co) {
          return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);
        }
        
        void main() {
          vec4 texel = texture2D(tDiffuse, vUv);
          
          // 生成噪点
          vec3 noise;
          if (colored == 1) {
            // 彩色噪点
            noise = vec3(
              random(vUv + vec2(0.0, time)),
              random(vUv + vec2(1.0, time)),
              random(vUv + vec2(2.0, time))
            );
          } else {
            // 灰度噪点
            float n = random(vUv + vec2(time, 0.0));
            noise = vec3(n, n, n);
          }
          
          // 应用噪点
          vec3 result;
          if (premultiply == 1) {
            // 预乘模式
            result = mix(texel.rgb, texel.rgb * noise, intensity);
          } else {
            // 叠加模式
            result = mix(texel.rgb, noise, intensity);
          }
          
          gl_FragColor = vec4(result, texel.a);
        }
      `
    };

    // 创建噪点通道
    this.noisePass = new ShaderPass(noiseShader);

    // 设置通道
    this.pass = this.noisePass;
  }

  /**
   * 更新效果
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.animated || !this.noisePass) return;

    // 更新时间
    this.time += deltaTime;

    // 更新时间 uniform
    const uniforms = this.noisePass.uniforms;
    if (uniforms.time) {
      uniforms.time.value = this.time;
    }
  }

  /**
   * 设置强度
   * @param intensity 强度
   */
  public setIntensity(intensity: number): void {
    this.intensity = intensity;

    if (this.noisePass) {
      const uniforms = this.noisePass.uniforms;
      if (uniforms.intensity) {
        uniforms.intensity.value = intensity;
      }
    }
  }

  /**
   * 获取强度
   * @returns 强度
   */
  public getIntensity(): number {
    return this.intensity;
  }

  /**
   * 设置是否使用彩色噪点
   * @param colored 是否使用彩色噪点
   */
  public setColored(colored: boolean): void {
    this.colored = colored;

    if (this.noisePass) {
      const uniforms = this.noisePass.uniforms;
      if (uniforms.colored) {
        uniforms.colored.value = colored ? 1 : 0;
      }
    }
  }

  /**
   * 获取是否使用彩色噪点
   * @returns 是否使用彩色噪点
   */
  public isColored(): boolean {
    return this.colored;
  }

  /**
   * 设置是否使用动态噪点
   * @param animated 是否使用动态噪点
   */
  public setAnimated(animated: boolean): void {
    this.animated = animated;
  }

  /**
   * 获取是否使用动态噪点
   * @returns 是否使用动态噪点
   */
  public isAnimated(): boolean {
    return this.animated;
  }

  /**
   * 设置是否预乘
   * @param premultiply 是否预乘
   */
  public setPremultiply(premultiply: boolean): void {
    this.premultiply = premultiply;

    if (this.noisePass) {
      const uniforms = this.noisePass.uniforms;
      if (uniforms.premultiply) {
        uniforms.premultiply.value = premultiply ? 1 : 0;
      }
    }
  }

  /**
   * 获取是否预乘
   * @returns 是否预乘
   */
  public isPremultiply(): boolean {
    return this.premultiply;
  }
}
