/**
 * UIComponent.ts
 *
 * UI组件类，用于管理UI元素的基本属性和行为
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector2, Vector3 } from 'three';
import { IUIElement, UILayoutType } from '../interfaces/IUIElement';
/**
 * UI组件类型
 */
export declare enum UIComponentType {
    BASE = "base",
    CONTAINER = "container",
    BUTTON = "button",
    TEXT = "text",
    IMAGE = "image",
    INPUT = "input",
    SLIDER = "slider",
    CHECKBOX = "checkbox",
    DROPDOWN = "dropdown",
    PANEL = "panel",
    WINDOW = "window",
    CUSTOM = "custom"
}
/**
 * UI组件属性
 */
export interface UIComponentProps {
    id?: string;
    type?: UIComponentType;
    visible?: boolean;
    interactive?: boolean;
    position?: Vector3 | Vector2;
    size?: Vector2;
    opacity?: number;
    zIndex?: number;
    layoutType?: UILayoutType;
    layoutParams?: any;
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    padding?: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
    margin?: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
    onClick?: (event: any) => void;
    onHover?: (event: any) => void;
    onDragStart?: (event: any) => void;
    onDrag?: (event: any) => void;
    onDragEnd?: (event: any) => void;
    data?: any;
    tags?: string[];
    is3D?: boolean;
}
/**
 * UI组件类
 * 用于管理UI元素的基本属性和行为
 */
export declare class UIComponent extends Component implements IUIElement {
    id: string;
    entity: Entity;
    parent?: IUIElement;
    children: IUIElement[];
    visible: boolean;
    interactive: boolean;
    position: Vector3 | Vector2;
    size: Vector2;
    opacity: number;
    uiType: UIComponentType;
    zIndex: number;
    layoutType: UILayoutType;
    layoutParams: any;
    backgroundColor?: string;
    borderColor?: string;
    borderWidth: number;
    borderRadius: number;
    padding: {
        top: number;
        right: number;
        bottom: number;
        left: number;
    };
    margin: {
        top: number;
        right: number;
        bottom: number;
        left: number;
    };
    data: any;
    tags: string[];
    is3D: boolean;
    private eventHandlers;
    /**
     * 构造函数
     * @param props UI组件属性
     */
    constructor(props?: UIComponentProps);
    /**
     * 当组件附加到实体时调用
     */
    protected onAttach(): void;
    /**
     * 添加子元素
     * @param child 要添加的子元素
     */
    addChild(child: IUIElement): void;
    /**
     * 移除子元素
     * @param child 要移除的子元素
     */
    removeChild(child: IUIElement): void;
    /**
     * 更新UI元素
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 渲染UI元素
     */
    render(): void;
    /**
     * 销毁UI元素
     */
    dispose(): void;
    /**
     * 添加事件监听器
     * @param eventType 事件类型
     * @param handler 事件处理函数
     */
    addEventListener(eventType: string, handler: (event: any) => void): void;
    /**
     * 移除事件监听器
     * @param eventType 事件类型
     */
    removeEventListener(eventType: string): void;
    /**
     * 触发事件
     * @param eventType 事件类型
     * @param eventData 事件数据
     */
    triggerEvent(eventType: string, eventData: any): void;
    /**
     * 设置位置
     * @param position 新位置
     */
    setPosition(position: Vector3 | Vector2): void;
    /**
     * 设置尺寸
     * @param size 新尺寸
     */
    setSize(size: Vector2): void;
    /**
     * 设置可见性
     * @param visible 是否可见
     */
    setVisible(visible: boolean): void;
    /**
     * 设置交互性
     * @param interactive 是否可交互
     */
    setInteractive(interactive: boolean): void;
    /**
     * 设置透明度
     * @param opacity 透明度值
     */
    setOpacity(opacity: number): void;
    /**
     * 添加标签
     * @param tag 要添加的标签
     */
    addTag(tag: string): void;
    /**
     * 移除标签
     * @param tag 要移除的标签
     */
    removeTag(tag: string): void;
    /**
     * 检查是否有指定标签
     * @param tag 要检查的标签
     */
    hasTag(tag: string): boolean;
}
