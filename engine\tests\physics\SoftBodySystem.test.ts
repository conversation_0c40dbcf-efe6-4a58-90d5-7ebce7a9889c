/**
 * SoftBodySystem类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { SoftBodySystem } from '../../src/physics/softbody/SoftBodySystem';
import { SoftBodyComponent } from '../../src/physics/softbody/SoftBodyComponent';
import { PhysicsSystem } from '../../src/physics/PhysicsSystem';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { SoftBodyType } from '../../src/physics/softbody/types';

describe('SoftBodySystem', () => {
  let engine: Engine;
  let world: World;
  let physicsSystem: PhysicsSystem;
  let softBodySystem: SoftBodySystem;
  let entity: Entity;
  let transform: Transform;
  
  // 在每个测试前创建一个新的软体系统实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建物理系统
    physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: false
    });
    
    // 添加物理系统到引擎
    engine.addSystem(physicsSystem);
    
    // 创建软体系统
    softBodySystem = new SoftBodySystem({
      physicsSystem: physicsSystem,
      debug: true
    });
    
    // 添加软体系统到引擎
    engine.addSystem(softBodySystem);
    
    // 初始化引擎
    engine.initialize();
    
    // 创建实体
    entity = new Entity(world);
    
    // 添加变换组件
    transform = new Transform({
      position: { x: 0, y: 5, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity.addComponent(transform);
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试软体系统初始化
  it('应该正确初始化软体系统', () => {
    expect(softBodySystem).toBeDefined();
    expect(softBodySystem['physicsSystem']).toBe(physicsSystem);
    expect(softBodySystem['debug']).toBe(true);
    expect(softBodySystem['softBodies'].size).toBe(0);
  });
  
  // 测试注册软体组件
  it('应该能够注册软体组件', () => {
    // 创建软体组件
    const softBodyComponent = new SoftBodyComponent(entity, {
      type: SoftBodyType.CLOTH,
      resolution: { x: 10, y: 10 },
      size: { x: 5, y: 5, z: 0 },
      position: { x: 0, y: 5, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      mass: 1,
      stiffness: 0.8,
      damping: 0.3,
      pressure: 0,
      margin: 0.1,
      fixedPoints: [0, 9, 90, 99] // 固定四个角点
    });
    
    // 添加组件到实体
    entity.addComponent(softBodyComponent);
    
    // 注册软体组件
    softBodySystem.registerSoftBodyComponent(entity, softBodyComponent);
    
    // 验证组件已注册
    expect(softBodySystem['softBodies'].has(entity.getId())).toBe(true);
    expect(softBodySystem['softBodies'].get(entity.getId())).toBe(softBodyComponent);
  });
  
  // 测试取消注册软体组件
  it('应该能够取消注册软体组件', () => {
    // 创建软体组件
    const softBodyComponent = new SoftBodyComponent(entity, {
      type: SoftBodyType.CLOTH,
      resolution: { x: 10, y: 10 },
      size: { x: 5, y: 5, z: 0 }
    });
    
    // 添加组件到实体
    entity.addComponent(softBodyComponent);
    
    // 注册软体组件
    softBodySystem.registerSoftBodyComponent(entity, softBodyComponent);
    
    // 验证组件已注册
    expect(softBodySystem['softBodies'].has(entity.getId())).toBe(true);
    
    // 取消注册软体组件
    softBodySystem.unregisterSoftBodyComponent(entity);
    
    // 验证组件已取消注册
    expect(softBodySystem['softBodies'].has(entity.getId())).toBe(false);
  });
  
  // 测试创建布料软体
  it('应该能够创建布料软体', () => {
    // 创建软体组件
    const softBodyComponent = new SoftBodyComponent(entity, {
      type: SoftBodyType.CLOTH,
      resolution: { x: 10, y: 10 },
      size: { x: 5, y: 5, z: 0 },
      position: { x: 0, y: 5, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      mass: 1,
      stiffness: 0.8,
      damping: 0.3,
      fixedPoints: [0, 9, 90, 99] // 固定四个角点
    });
    
    // 添加组件到实体
    entity.addComponent(softBodyComponent);
    
    // 注册软体组件
    softBodySystem.registerSoftBodyComponent(entity, softBodyComponent);
    
    // 创建布料软体
    softBodySystem.createCloth(softBodyComponent);
    
    // 验证布料软体已创建
    expect(softBodyComponent['particles'].length).toBeGreaterThan(0);
    expect(softBodyComponent['springs'].length).toBeGreaterThan(0);
    expect(softBodyComponent['mesh']).toBeDefined();
  });
  
  // 测试创建绳索软体
  it('应该能够创建绳索软体', () => {
    // 创建软体组件
    const softBodyComponent = new SoftBodyComponent(entity, {
      type: SoftBodyType.ROPE,
      resolution: { x: 10, y: 1 },
      size: { x: 5, y: 0, z: 0 },
      position: { x: 0, y: 5, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      mass: 1,
      stiffness: 0.8,
      damping: 0.3,
      fixedPoints: [0] // 固定一端
    });
    
    // 添加组件到实体
    entity.addComponent(softBodyComponent);
    
    // 注册软体组件
    softBodySystem.registerSoftBodyComponent(entity, softBodyComponent);
    
    // 创建绳索软体
    softBodySystem.createRope(softBodyComponent);
    
    // 验证绳索软体已创建
    expect(softBodyComponent['particles'].length).toBeGreaterThan(0);
    expect(softBodyComponent['springs'].length).toBeGreaterThan(0);
    expect(softBodyComponent['mesh']).toBeDefined();
  });
  
  // 测试创建体积软体
  it('应该能够创建体积软体', () => {
    // 创建软体组件
    const softBodyComponent = new SoftBodyComponent(entity, {
      type: SoftBodyType.VOLUME,
      resolution: { x: 5, y: 5, z: 5 },
      size: { x: 2, y: 2, z: 2 },
      position: { x: 0, y: 5, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      mass: 1,
      stiffness: 0.8,
      damping: 0.3,
      pressure: 100
    });
    
    // 添加组件到实体
    entity.addComponent(softBodyComponent);
    
    // 注册软体组件
    softBodySystem.registerSoftBodyComponent(entity, softBodyComponent);
    
    // 创建体积软体
    softBodySystem.createVolume(softBodyComponent);
    
    // 验证体积软体已创建
    expect(softBodyComponent['particles'].length).toBeGreaterThan(0);
    expect(softBodyComponent['springs'].length).toBeGreaterThan(0);
    expect(softBodyComponent['mesh']).toBeDefined();
  });
  
  // 测试软体更新
  it('应该能够更新软体', () => {
    // 创建软体组件
    const softBodyComponent = new SoftBodyComponent(entity, {
      type: SoftBodyType.CLOTH,
      resolution: { x: 5, y: 5 },
      size: { x: 2, y: 2, z: 0 }
    });
    
    // 添加组件到实体
    entity.addComponent(softBodyComponent);
    
    // 注册软体组件
    softBodySystem.registerSoftBodyComponent(entity, softBodyComponent);
    
    // 创建布料软体
    softBodySystem.createCloth(softBodyComponent);
    
    // 创建更新方法的模拟
    const updateMock = vi.spyOn(softBodyComponent, 'update');
    
    // 更新软体系统
    softBodySystem.update(0.016);
    
    // 验证软体组件的更新方法被调用
    expect(updateMock).toHaveBeenCalledWith(0.016);
  });
  
  // 测试软体固定更新
  it('应该能够固定更新软体', () => {
    // 创建软体组件
    const softBodyComponent = new SoftBodyComponent(entity, {
      type: SoftBodyType.CLOTH,
      resolution: { x: 5, y: 5 },
      size: { x: 2, y: 2, z: 0 }
    });
    
    // 添加组件到实体
    entity.addComponent(softBodyComponent);
    
    // 注册软体组件
    softBodySystem.registerSoftBodyComponent(entity, softBodyComponent);
    
    // 创建布料软体
    softBodySystem.createCloth(softBodyComponent);
    
    // 创建固定更新方法的模拟
    const fixedUpdateMock = vi.spyOn(softBodyComponent, 'fixedUpdate');
    
    // 固定更新软体系统
    softBodySystem.fixedUpdate(0.02);
    
    // 验证软体组件的固定更新方法被调用
    expect(fixedUpdateMock).toHaveBeenCalledWith(0.02);
  });
  
  // 测试软体切割
  it('应该能够切割软体', () => {
    // 创建软体组件
    const softBodyComponent = new SoftBodyComponent(entity, {
      type: SoftBodyType.CLOTH,
      resolution: { x: 10, y: 10 },
      size: { x: 5, y: 5, z: 0 }
    });
    
    // 添加组件到实体
    entity.addComponent(softBodyComponent);
    
    // 注册软体组件
    softBodySystem.registerSoftBodyComponent(entity, softBodyComponent);
    
    // 创建布料软体
    softBodySystem.createCloth(softBodyComponent);
    
    // 获取初始弹簧数量
    const initialSpringsCount = softBodyComponent['springs'].length;
    
    // 切割软体
    const startPoint = new THREE.Vector3(0, 5, -1);
    const endPoint = new THREE.Vector3(0, 5, 1);
    const cutPlane = new THREE.Plane().setFromCoplanarPoints(
      startPoint,
      endPoint,
      new THREE.Vector3(1, 5, 0)
    );
    
    softBodySystem.cutSoftBody(softBodyComponent, cutPlane);
    
    // 验证弹簧数量减少
    expect(softBodyComponent['springs'].length).toBeLessThan(initialSpringsCount);
  });
});
