/**
 * LOD生成器
 * 用于自动生成不同细节级别的模型
 */
import * as THREE from 'three';
import { LODLevel } from './LODComponent';
/**
 * LOD生成器配置接口
 */
export interface LODGeneratorOptions {
    /** 高细节级别的简化比例 */
    highDetailRatio?: number;
    /** 中细节级别的简化比例 */
    mediumDetailRatio?: number;
    /** 低细节级别的简化比例 */
    lowDetailRatio?: number;
    /** 极低细节级别的简化比例 */
    veryLowDetailRatio?: number;
    /** 是否保留UV */
    preserveUVs?: boolean;
    /** 是否保留法线 */
    preserveNormals?: boolean;
    /** 是否保留颜色 */
    preserveColors?: boolean;
}
/**
 * LOD生成结果接口
 */
export interface LODGeneratorResult {
    /** 原始网格 */
    original: THREE.Mesh;
    /** 高细节网格 */
    high: THREE.Mesh;
    /** 中细节网格 */
    medium: THREE.Mesh;
    /** 低细节网格 */
    low: THREE.Mesh;
    /** 极低细节网格 */
    veryLow: THREE.Mesh;
}
/**
 * LOD生成器类
 */
export declare class LODGenerator {
    /** 高细节级别的简化比例 */
    private highDetailRatio;
    /** 中细节级别的简化比例 */
    private mediumDetailRatio;
    /** 低细节级别的简化比例 */
    private lowDetailRatio;
    /** 极低细节级别的简化比例 */
    private veryLowDetailRatio;
    /** 是否保留UV */
    private preserveUVs;
    /** 是否保留法线 */
    private preserveNormals;
    /** 是否保留颜色 */
    private preserveColors;
    /**
     * 创建LOD生成器
     * @param options LOD生成器配置
     */
    constructor(options?: LODGeneratorOptions);
    /**
     * 生成LOD
     * @param mesh 原始网格
     * @returns LOD生成结果
     */
    generate(mesh: THREE.Mesh): LODGeneratorResult;
    /**
     * 生成指定细节级别的网格
     * @param mesh 原始网格
     * @param ratio 简化比例
     * @returns 简化后的网格
     */
    generateLevel(mesh: THREE.Mesh, ratio: number): THREE.Mesh;
    /**
     * 简化几何体
     * @param geometry 原始几何体
     * @param ratio 简化比例
     * @returns 简化后的几何体
     */
    private simplifyGeometry;
    /**
     * 简化网格
     * @param geometry 原始几何体
     * @param targetVertexCount 目标顶点数
     * @returns 简化后的几何体
     */
    private simplifyMesh;
    /**
     * 计算三角形的重要性
     * @param geometry 几何体
     * @returns 三角形重要性数组
     */
    private calculateTriangleImportance;
    /**
     * 为非索引几何体生成索引
     * @param geometry 几何体
     * @returns 索引数组
     */
    private generateIndices;
    /**
     * 生成指定级别的LOD
     * @param mesh 原始网格
     * @param level 级别
     * @returns 简化后的网格
     */
    generateLOD(mesh: THREE.Mesh, level: LODLevel): THREE.Mesh;
    /**
     * 设置高细节级别的简化比例
     * @param ratio 简化比例
     */
    setHighDetailRatio(ratio: number): void;
    /**
     * 获取高细节级别的简化比例
     * @returns 简化比例
     */
    getHighDetailRatio(): number;
    /**
     * 设置中细节级别的简化比例
     * @param ratio 简化比例
     */
    setMediumDetailRatio(ratio: number): void;
    /**
     * 获取中细节级别的简化比例
     * @returns 简化比例
     */
    getMediumDetailRatio(): number;
    /**
     * 设置低细节级别的简化比例
     * @param ratio 简化比例
     */
    setLowDetailRatio(ratio: number): void;
    /**
     * 获取低细节级别的简化比例
     * @returns 简化比例
     */
    getLowDetailRatio(): number;
    /**
     * 设置极低细节级别的简化比例
     * @param ratio 简化比例
     */
    setVeryLowDetailRatio(ratio: number): void;
    /**
     * 获取极低细节级别的简化比例
     * @returns 简化比例
     */
    getVeryLowDetailRatio(): number;
}
