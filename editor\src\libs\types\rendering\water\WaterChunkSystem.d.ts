/**
 * 水体分块系统
 * 用于大规模水体的高效渲染
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
/**
 * 水体块
 */
export interface WaterChunk {
    /** 唯一ID */
    id: string;
    /** 名称 */
    name: string;
    /** 包围盒 */
    boundingBox: THREE.Box3;
    /** 包围球 */
    boundingSphere: THREE.Sphere;
    /** 中心点 */
    center: THREE.Vector3;
    /** 尺寸 */
    size: THREE.Vector3;
    /** LOD级别 */
    level: number;
    /** 可见性 */
    visible: boolean;
    /** 是否已加载 */
    loaded: boolean;
    /** 是否正在加载 */
    loading: boolean;
    /** 加载进度 */
    progress: number;
    /** 网格 */
    mesh?: THREE.Mesh;
    /** 父块 */
    parent: WaterChunk | null;
    /** 子块 */
    children: WaterChunk[];
    /** 相邻块 */
    neighbors: WaterChunk[];
    /** 用户数据 */
    userData: any;
}
/**
 * 水体分块系统配置
 */
export interface WaterChunkSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 块大小 */
    chunkSize?: number;
    /** 最大LOD级别 */
    maxLODLevel?: number;
    /** 是否使用LOD */
    useLOD?: boolean;
    /** LOD距离 */
    lodDistances?: number[];
    /** 是否使用视锥体剔除 */
    useFrustumCulling?: boolean;
    /** 是否使用八叉树 */
    useOctree?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 加载距离 */
    loadDistance?: number;
    /** 卸载距离 */
    unloadDistance?: number;
}
/**
 * 水体分块系统事件类型
 */
export declare enum WaterChunkSystemEventType {
    /** 块创建 */
    CHUNK_CREATED = "chunkCreated",
    /** 块加载 */
    CHUNK_LOADED = "chunkLoaded",
    /** 块卸载 */
    CHUNK_UNLOADED = "chunkUnloaded",
    /** 块可见性变化 */
    CHUNK_VISIBILITY_CHANGED = "chunkVisibilityChanged",
    /** 块LOD级别变化 */
    CHUNK_LOD_CHANGED = "chunkLODChanged"
}
/**
 * 水体分块系统
 */
export declare class WaterChunkSystem extends System {
    /** 系统类型 */
    static readonly TYPE = "WaterChunkSystem";
    /** 配置 */
    private config;
    /** 水体实体映射 */
    private waterEntities;
    /** 实体到块映射 */
    private entityToChunks;
    /** 水体块映射 */
    private waterChunks;
    /** 八叉树 */
    private octree?;
    /** 视锥体 */
    private frustum;
    /** 临时矩阵 */
    private tempMatrix;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监视器 */
    private performanceMonitor;
    /** 调试对象 */
    private debugObjects;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: WaterChunkSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 检查系统是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 初始化八叉树
     */
    private initializeOctree;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 更新视锥体
     * @param camera 相机
     */
    private updateFrustum;
    /**
     * 添加水体实体
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterEntity(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体实体
     * @param entity 实体
     */
    removeWaterEntity(entity: Entity): void;
    /**
     * 为水体创建块
     * @param entity 实体
     * @param component 水体组件
     */
    private createChunksForWater;
    /**
     * 创建块
     * @param entity 实体
     * @param component 水体组件
     * @param boundingBox 包围盒
     * @param x X坐标
     * @param z Z坐标
     */
    private createChunk;
    /**
     * 移除块
     * @param chunkId 块ID
     */
    private removeChunk;
    /**
     * 更新所有块
     * @param camera 相机
     */
    private updateChunks;
    /**
     * 使用八叉树更新块
     * @param camera 相机
     */
    private updateChunksWithOctree;
    /**
     * 使用暴力方法更新块
     * @param camera 相机
     */
    private updateChunksWithBruteForce;
    /**
     * 更新块的LOD级别
     * @param chunk 块
     * @param distance 距离
     */
    private updateChunkLOD;
    /**
     * 加载块
     * @param chunk 块
     */
    private loadChunk;
    /**
     * 卸载块
     * @param chunk 块
     */
    private unloadChunk;
    /**
     * 设置块可见性
     * @param chunk 块
     * @param visible 是否可见
     */
    private setChunkVisible;
    /**
     * 创建水体网格
     * @param chunk 块
     * @param component 水体组件
     */
    private createWaterMesh;
    /**
     * 创建水体几何体
     * @param chunk 块
     * @param component 水体组件
     * @returns 几何体
     */
    private createWaterGeometry;
    /**
     * 根据LOD级别获取分辨率
     * @param level LOD级别
     * @returns 分辨率
     */
    private getResolutionForLOD;
    /**
     * 调整材质质量
     * @param material 材质
     * @param level LOD级别
     */
    private adjustMaterialQuality;
    /**
     * 更新块网格
     * @param chunk 块
     */
    private updateChunkMesh;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
}
