/**
 * 限流守卫
 */
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class ThrottleGuard implements CanActivate {
  private storage: Map<string, { lastTime: number; count: number }> = new Map();
  private readonly ttl = 60000; // 1分钟
  private readonly limit = 100; // 每分钟最多100个请求

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const req = context.switchToHttp().getRequest();
    const ip = req.ip;
    const key = `${ip}:${req.method}:${req.url}`;

    const now = Date.now();
    const record = this.storage.get(key) || { lastTime: now, count: 0 };

    // 如果超过了TTL，重置计数
    if (now - record.lastTime > this.ttl) {
      record.count = 0;
      record.lastTime = now;
    }

    // 增加计数
    record.count++;
    this.storage.set(key, record);

    // 如果超过了限制，抛出异常
    if (record.count > this.limit) {
      throw new HttpException('请求过于频繁，请稍后再试', HttpStatus.TOO_MANY_REQUESTS);
    }

    // 清理过期的记录
    this.cleanupStorage();

    return true;
  }

  private cleanupStorage() {
    const now = Date.now();
    for (const [key, record] of this.storage.entries()) {
      if (now - record.lastTime > this.ttl) {
        this.storage.delete(key);
      }
    }
  }
}
