/**
 * 口型同步组件
 * 用于控制角色的口型同步
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { type EventCallback } from '../../utils/EventEmitter';
import { VisemeType } from './FacialAnimationComponent';
/**
 * 口型同步组件类型
 */
export declare const LipSyncComponentType = "LipSyncComponent";
/**
 * 口型同步组件
 */
export declare class LipSyncComponent extends Component {
    /** 组件类型 */
    static readonly TYPE = "LipSyncComponent";
    /** 事件发射器 */
    private eventEmitter;
    /** 当前口型 */
    private currentViseme;
    /** 口型权重 */
    private visemeWeight;
    /** 口型混合速度 */
    private visemeBlendSpeed;
    /** 口型混合映射 */
    private visemeBlendMap;
    /** 口型历史 */
    private visemeHistory;
    /** 历史长度 */
    private historyLength;
    /** 音频源 */
    private audioSource;
    /** 音频上下文 */
    private audioContext;
    /** 音频分析器 */
    private audioAnalyser;
    /** 音频源节点 */
    private sourceNode;
    /** 是否自动检测音频 */
    private autoDetectAudio;
    /**
     * 构造函数
     * @param entity 实体
     */
    constructor(entity: Entity);
    /**
     * 获取组件类型
     */
    getType(): string;
    /**
     * 设置口型
     * @param viseme 口型类型
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     */
    setViseme(viseme: VisemeType, weight?: number, blendTime?: number): void;
    /**
     * 重置口型
     */
    resetViseme(): void;
    /**
     * 获取当前口型
     */
    getCurrentViseme(): VisemeType;
    /**
     * 获取当前口型权重
     */
    getCurrentVisemeWeight(): number;
    /**
     * 获取口型混合映射
     */
    getVisemeBlendMap(): Map<VisemeType, number>;
    /**
     * 获取口型历史
     */
    getVisemeHistory(): {
        viseme: VisemeType;
        time: number;
    }[];
    /**
     * 设置音频源
     * @param audio 音频元素
     */
    setAudioSource(audio: HTMLAudioElement): void;
    /**
     * 初始化音频分析
     */
    private initAudioAnalysis;
    /**
     * 分析音频
     */
    private analyzeAudio;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新口型混合
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateVisemeBlending;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: EventCallback): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: EventCallback): void;
}
