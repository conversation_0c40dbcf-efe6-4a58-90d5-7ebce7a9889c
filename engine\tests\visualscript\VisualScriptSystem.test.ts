/**
 * VisualScriptSystem类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { VisualScriptSystem } from '../../src/visualscript/VisualScriptSystem';
import { VisualScriptComponent } from '../../src/visualscript/VisualScriptComponent';
import { Node } from '../../src/visualscript/Node';
import { NodeRegistry } from '../../src/visualscript/NodeRegistry';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';

// 创建一个测试节点类
class TestNode extends Node {
  public executed: boolean = false;
  public inputValue: any = null;
  
  constructor() {
    super('TestNode');
    
    // 添加输入端口
    this.addInputPort('input', 'any');
    
    // 添加输出端口
    this.addOutputPort('output', 'any');
  }
  
  public execute(inputs: Map<string, any>): Map<string, any> {
    this.executed = true;
    this.inputValue = inputs.get('input');
    
    const outputs = new Map<string, any>();
    outputs.set('output', this.inputValue);
    
    return outputs;
  }
}

// 创建一个数学节点类
class MathNode extends Node {
  public operation: string;
  
  constructor(operation: string = 'add') {
    super('MathNode');
    this.operation = operation;
    
    // 添加输入端口
    this.addInputPort('a', 'number');
    this.addInputPort('b', 'number');
    
    // 添加输出端口
    this.addOutputPort('result', 'number');
  }
  
  public execute(inputs: Map<string, any>): Map<string, any> {
    const a = inputs.get('a') || 0;
    const b = inputs.get('b') || 0;
    let result = 0;
    
    switch (this.operation) {
      case 'add':
        result = a + b;
        break;
      case 'subtract':
        result = a - b;
        break;
      case 'multiply':
        result = a * b;
        break;
      case 'divide':
        result = b !== 0 ? a / b : 0;
        break;
      default:
        result = a + b;
    }
    
    const outputs = new Map<string, any>();
    outputs.set('result', result);
    
    return outputs;
  }
}

describe('VisualScriptSystem', () => {
  let engine: Engine;
  let world: World;
  let visualScriptSystem: VisualScriptSystem;
  let entity: Entity;
  let visualScriptComponent: VisualScriptComponent;
  
  // 在每个测试前创建一个新的视觉脚本系统实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建视觉脚本系统
    visualScriptSystem = new VisualScriptSystem();
    
    // 添加视觉脚本系统到引擎
    engine.addSystem(visualScriptSystem);
    
    // 注册测试节点
    NodeRegistry.registerNodeType('TestNode', TestNode);
    NodeRegistry.registerNodeType('MathNode', MathNode);
    
    // 创建实体
    entity = new Entity(world);
    entity.name = '脚本实体';
    
    // 添加变换组件
    const transform = new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity.addComponent(transform);
    
    // 创建视觉脚本组件
    visualScriptComponent = new VisualScriptComponent(entity);
    entity.addComponent(visualScriptComponent);
    
    // 添加实体到世界
    world.addEntity(entity);
    
    // 注册视觉脚本组件
    visualScriptSystem.registerVisualScriptComponent(entity, visualScriptComponent);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
    
    // 清理节点注册表
    NodeRegistry.clear();
  });
  
  // 测试视觉脚本系统初始化
  it('应该正确初始化视觉脚本系统', () => {
    expect(visualScriptSystem).toBeDefined();
    expect(visualScriptSystem['visualScripts'].size).toBe(1);
    expect(visualScriptSystem['visualScripts'].get(entity.getId())).toBe(visualScriptComponent);
  });
  
  // 测试注册和取消注册视觉脚本组件
  it('应该能够注册和取消注册视觉脚本组件', () => {
    // 创建另一个实体
    const entity2 = new Entity(world);
    entity2.name = '脚本实体2';
    
    // 创建视觉脚本组件
    const visualScriptComponent2 = new VisualScriptComponent(entity2);
    entity2.addComponent(visualScriptComponent2);
    
    // 添加实体到世界
    world.addEntity(entity2);
    
    // 注册视觉脚本组件
    visualScriptSystem.registerVisualScriptComponent(entity2, visualScriptComponent2);
    
    // 验证组件已注册
    expect(visualScriptSystem['visualScripts'].has(entity2.getId())).toBe(true);
    expect(visualScriptSystem['visualScripts'].get(entity2.getId())).toBe(visualScriptComponent2);
    
    // 取消注册视觉脚本组件
    visualScriptSystem.unregisterVisualScriptComponent(entity2);
    
    // 验证组件已取消注册
    expect(visualScriptSystem['visualScripts'].has(entity2.getId())).toBe(false);
  });
  
  // 测试创建和执行简单脚本
  it('应该能够创建和执行简单脚本', () => {
    // 创建测试节点
    const testNode = new TestNode();
    testNode.id = 'node1';
    
    // 添加节点到脚本
    visualScriptComponent.addNode(testNode);
    
    // 设置节点输入
    visualScriptComponent.setNodeInput(testNode.id, 'input', 'Hello, World!');
    
    // 执行脚本
    visualScriptComponent.execute();
    
    // 验证节点已执行
    expect(testNode.executed).toBe(true);
    expect(testNode.inputValue).toBe('Hello, World!');
    
    // 获取节点输出
    const output = visualScriptComponent.getNodeOutput(testNode.id, 'output');
    
    // 验证输出
    expect(output).toBe('Hello, World!');
  });
  
  // 测试创建和执行带连接的脚本
  it('应该能够创建和执行带连接的脚本', () => {
    // 创建数学节点
    const mathNode1 = new MathNode('add');
    mathNode1.id = 'math1';
    
    const mathNode2 = new MathNode('multiply');
    mathNode2.id = 'math2';
    
    // 添加节点到脚本
    visualScriptComponent.addNode(mathNode1);
    visualScriptComponent.addNode(mathNode2);
    
    // 设置节点输入
    visualScriptComponent.setNodeInput(mathNode1.id, 'a', 5);
    visualScriptComponent.setNodeInput(mathNode1.id, 'b', 3);
    
    // 连接节点
    visualScriptComponent.connectNodes(mathNode1.id, 'result', mathNode2.id, 'a');
    visualScriptComponent.setNodeInput(mathNode2.id, 'b', 2);
    
    // 执行脚本
    visualScriptComponent.execute();
    
    // 获取最终输出
    const output = visualScriptComponent.getNodeOutput(mathNode2.id, 'result');
    
    // 验证输出 (5 + 3) * 2 = 16
    expect(output).toBe(16);
  });
  
  // 测试脚本事件
  it('应该能够触发和监听脚本事件', () => {
    // 创建事件监听器
    const startSpy = vi.fn();
    const endSpy = vi.fn();
    
    // 添加事件监听器
    visualScriptComponent.on('executionStart', startSpy);
    visualScriptComponent.on('executionEnd', endSpy);
    
    // 创建测试节点
    const testNode = new TestNode();
    testNode.id = 'node1';
    
    // 添加节点到脚本
    visualScriptComponent.addNode(testNode);
    
    // 执行脚本
    visualScriptComponent.execute();
    
    // 验证事件被触发
    expect(startSpy).toHaveBeenCalled();
    expect(endSpy).toHaveBeenCalled();
  });
  
  // 测试脚本变量
  it('应该能够设置和获取脚本变量', () => {
    // 设置脚本变量
    visualScriptComponent.setVariable('count', 10);
    visualScriptComponent.setVariable('name', 'Test Script');
    
    // 获取脚本变量
    const count = visualScriptComponent.getVariable('count');
    const name = visualScriptComponent.getVariable('name');
    
    // 验证变量值
    expect(count).toBe(10);
    expect(name).toBe('Test Script');
  });
  
  // 测试脚本序列化和反序列化
  it('应该能够序列化和反序列化脚本', () => {
    // 创建数学节点
    const mathNode = new MathNode('add');
    mathNode.id = 'math1';
    
    // 添加节点到脚本
    visualScriptComponent.addNode(mathNode);
    
    // 设置节点输入
    visualScriptComponent.setNodeInput(mathNode.id, 'a', 5);
    visualScriptComponent.setNodeInput(mathNode.id, 'b', 3);
    
    // 序列化脚本
    const data = visualScriptComponent.serialize();
    
    // 创建新的视觉脚本组件
    const newEntity = new Entity(world);
    const newVisualScriptComponent = new VisualScriptComponent(newEntity);
    
    // 反序列化脚本
    newVisualScriptComponent.deserialize(data);
    
    // 执行新脚本
    newVisualScriptComponent.execute();
    
    // 获取输出
    const output = newVisualScriptComponent.getNodeOutput('math1', 'result');
    
    // 验证输出
    expect(output).toBe(8);
  });
});
