/**
 * 媒体流管理器
 * 负责管理WebRTC媒体流的获取、处理和释放
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';

/**
 * 媒体流类型
 */
export enum MediaStreamType {
  /** 音频 */
  AUDIO = 'audio',
  /** 视频 */
  VIDEO = 'video',
  /** 屏幕共享 */
  SCREEN_SHARE = 'screen_share',
  /** 音频和视频 */
  AUDIO_VIDEO = 'audio_video',
}

/**
 * 媒体流质量
 */
export enum MediaStreamQuality {
  /** 低质量 */
  LOW = 'low',
  /** 中等质量 */
  MEDIUM = 'medium',
  /** 高质量 */
  HIGH = 'high',
  /** 超高质量 */
  ULTRA = 'ultra',
}

/**
 * 媒体流配置
 */
export interface MediaStreamConfig {
  /** 音频约束 */
  audioConstraints?: MediaTrackConstraints;
  /** 视频约束 */
  videoConstraints?: MediaTrackConstraints;
  /** 屏幕共享约束 */
  screenShareConstraints?: MediaTrackConstraints;
  /** 是否启用回声消除 */
  echoCancellation?: boolean;
  /** 是否启用噪声抑制 */
  noiseSuppression?: boolean;
  /** 是否启用自动增益控制 */
  autoGainControl?: boolean;
  /** 是否启用立体声 */
  stereo?: boolean;
  /** 是否启用自动播放 */
  autoPlay?: boolean;
  /** 是否启用静音 */
  muted?: boolean;
  /** 是否启用视频镜像 */
  mirror?: boolean;
  /** 视频质量 */
  videoQuality?: MediaStreamQuality;
  /** 视频帧率 */
  frameRate?: number;
  /** 视频宽度 */
  width?: number;
  /** 视频高度 */
  height?: number;
  /** 视频比特率（bps） */
  videoBitrate?: number;
  /** 音频比特率（bps） */
  audioBitrate?: number;
}

/**
 * 媒体流信息
 */
export interface MediaStreamInfo {
  /** 流ID */
  id: string;
  /** 流类型 */
  type: MediaStreamType;
  /** 流质量 */
  quality: MediaStreamQuality;
  /** 媒体流 */
  stream: MediaStream;
  /** 音频轨道 */
  audioTrack?: MediaStreamTrack;
  /** 视频轨道 */
  videoTrack?: MediaStreamTrack;
  /** 是否启用 */
  enabled: boolean;
  /** 是否静音 */
  muted: boolean;
  /** 是否暂停 */
  paused: boolean;
  /** 创建时间 */
  createdAt: number;
  /** 配置 */
  config: MediaStreamConfig;
  /** 用户ID */
  userId?: string;
  /** 自定义数据 */
  customData?: Record<string, any>;
}

/**
 * 媒体流管理器配置
 */
export interface MediaStreamManagerConfig {
  /** 默认音频约束 */
  defaultAudioConstraints?: MediaTrackConstraints;
  /** 默认视频约束 */
  defaultVideoConstraints?: MediaTrackConstraints;
  /** 默认屏幕共享约束 */
  defaultScreenShareConstraints?: MediaTrackConstraints;
  /** 是否启用设备枚举 */
  enableDeviceEnumeration?: boolean;
  /** 是否启用设备变更检测 */
  enableDeviceChangeDetection?: boolean;
  /** 是否启用音频处理 */
  enableAudioProcessing?: boolean;
  /** 是否启用视频处理 */
  enableVideoProcessing?: boolean;
  /** 是否启用自动播放 */
  enableAutoPlay?: boolean;
  /** 是否启用音频电平监测 */
  enableAudioLevelMonitoring?: boolean;
  /** 音频电平监测间隔（毫秒） */
  audioLevelMonitoringInterval?: number;
}

/**
 * 媒体设备信息
 */
export interface MediaDeviceInfo {
  /** 设备ID */
  deviceId: string;
  /** 设备标签 */
  label: string;
  /** 设备类型 */
  kind: MediaDeviceKind;
  /** 设备分组ID */
  groupId: string;
}

/**
 * 媒体流管理器
 * 负责管理WebRTC媒体流的获取、处理和释放
 */
export class MediaStreamManager extends EventEmitter {
  /** 配置 */
  private config: Required<MediaStreamManagerConfig>;
  
  /** 本地媒体流映射表 */
  private localStreams: Map<string, MediaStreamInfo> = new Map();
  
  /** 远程媒体流映射表 */
  private remoteStreams: Map<string, MediaStreamInfo> = new Map();
  
  /** 音频输入设备列表 */
  private audioInputDevices: MediaDeviceInfo[] = [];
  
  /** 音频输出设备列表 */
  private audioOutputDevices: MediaDeviceInfo[] = [];
  
  /** 视频输入设备列表 */
  private videoInputDevices: MediaDeviceInfo[] = [];
  
  /** 当前音频输入设备ID */
  private currentAudioInputDeviceId: string | null = null;
  
  /** 当前音频输出设备ID */
  private currentAudioOutputDeviceId: string | null = null;
  
  /** 当前视频输入设备ID */
  private currentVideoInputDeviceId: string | null = null;
  
  /** 音频电平监测定时器ID */
  private audioLevelMonitoringTimerId: number | null = null;
  
  /** 音频电平分析器映射表 */
  private audioAnalysers: Map<string, AnalyserNode> = new Map();
  
  /** 音频上下文 */
  private audioContext: AudioContext | null = null;
  
  /**
   * 创建媒体流管理器
   * @param config 配置
   */
  constructor(config: MediaStreamManagerConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      defaultAudioConstraints: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
      defaultVideoConstraints: {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        frameRate: { ideal: 30 },
      },
      defaultScreenShareConstraints: {
        width: { ideal: 1920 },
        height: { ideal: 1080 },
        frameRate: { ideal: 15 },
      },
      enableDeviceEnumeration: true,
      enableDeviceChangeDetection: true,
      enableAudioProcessing: true,
      enableVideoProcessing: true,
      enableAutoPlay: true,
      enableAudioLevelMonitoring: true,
      audioLevelMonitoringInterval: 100,
      ...config,
    };
    
    // 初始化
    this.initialize();
  }
  
  /**
   * 初始化管理器
   */
  private async initialize(): Promise<void> {
    // 创建音频上下文
    if (this.config.enableAudioProcessing || this.config.enableAudioLevelMonitoring) {
      this.audioContext = new AudioContext();
    }
    
    // 枚举媒体设备
    if (this.config.enableDeviceEnumeration) {
      await this.enumerateDevices();
    }
    
    // 监听设备变更事件
    if (this.config.enableDeviceChangeDetection) {
      navigator.mediaDevices.addEventListener('devicechange', this.handleDeviceChange.bind(this));
    }
    
    // 启动音频电平监测
    if (this.config.enableAudioLevelMonitoring) {
      this.startAudioLevelMonitoring();
    }
  }
  
  /**
   * 枚举媒体设备
   */
  private async enumerateDevices(): Promise<void> {
    try {
      // 获取设备列表
      const devices = await navigator.mediaDevices.enumerateDevices();
      
      // 清空设备列表
      this.audioInputDevices = [];
      this.audioOutputDevices = [];
      this.videoInputDevices = [];
      
      // 分类设备
      for (const device of devices) {
        const deviceInfo: MediaDeviceInfo = {
          deviceId: device.deviceId,
          label: device.label,
          kind: device.kind,
          groupId: device.groupId,
        };
        
        switch (device.kind) {
          case 'audioinput':
            this.audioInputDevices.push(deviceInfo);
            break;
            
          case 'audiooutput':
            this.audioOutputDevices.push(deviceInfo);
            break;
            
          case 'videoinput':
            this.videoInputDevices.push(deviceInfo);
            break;
        }
      }
      
      // 如果没有设备标签，可能需要请求权限
      if (devices.length > 0 && !devices[0].label) {
        // 请求临时媒体流以获取权限
        const tempStream = await navigator.mediaDevices.getUserMedia({ audio: true, video: true });
        
        // 停止所有轨道
        tempStream.getTracks().forEach(track => track.stop());
        
        // 重新枚举设备
        await this.enumerateDevices();
        return;
      }
      
      // 设置默认设备
      if (this.audioInputDevices.length > 0 && !this.currentAudioInputDeviceId) {
        this.currentAudioInputDeviceId = this.audioInputDevices[0].deviceId;
      }
      
      if (this.audioOutputDevices.length > 0 && !this.currentAudioOutputDeviceId) {
        this.currentAudioOutputDeviceId = this.audioOutputDevices[0].deviceId;
      }
      
      if (this.videoInputDevices.length > 0 && !this.currentVideoInputDeviceId) {
        this.currentVideoInputDeviceId = this.videoInputDevices[0].deviceId;
      }
      
      // 触发设备枚举事件
      this.emit('devicesEnumerated', {
        audioInputDevices: this.audioInputDevices,
        audioOutputDevices: this.audioOutputDevices,
        videoInputDevices: this.videoInputDevices,
      });
    } catch (error) {
      Debug.error('MediaStreamManager', 'Failed to enumerate devices:', error);
      this.emit('error', 'deviceEnumeration', error);
    }
  }
  
  /**
   * 处理设备变更事件
   */
  private async handleDeviceChange(): Promise<void> {
    // 重新枚举设备
    await this.enumerateDevices();
    
    // 触发设备变更事件
    this.emit('deviceChange', {
      audioInputDevices: this.audioInputDevices,
      audioOutputDevices: this.audioOutputDevices,
      videoInputDevices: this.videoInputDevices,
    });
  }
  
  /**
   * 启动音频电平监测
   */
  private startAudioLevelMonitoring(): void {
    if (this.audioLevelMonitoringTimerId !== null) {
      return;
    }
    
    this.audioLevelMonitoringTimerId = window.setInterval(() => {
      this.updateAudioLevels();
    }, this.config.audioLevelMonitoringInterval);
  }
  
  /**
   * 停止音频电平监测
   */
  private stopAudioLevelMonitoring(): void {
    if (this.audioLevelMonitoringTimerId !== null) {
      clearInterval(this.audioLevelMonitoringTimerId);
      this.audioLevelMonitoringTimerId = null;
    }
  }
  
  /**
   * 更新音频电平
   */
  private updateAudioLevels(): void {
    // 遍历所有音频分析器
    for (const [streamId, analyser] of this.audioAnalysers.entries()) {
      // 获取音频数据
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      analyser.getByteFrequencyData(dataArray);
      
      // 计算平均音量
      let sum = 0;
      for (const value of dataArray) {
        sum += value;
      }
      const average = sum / dataArray.length;
      
      // 归一化到0-1范围
      const level = average / 255;
      
      // 触发音频电平事件
      this.emit('audioLevel', streamId, level);
    }
  }
  
  /**
   * 创建音频分析器
   * @param stream 媒体流
   * @returns 音频分析器
   */
  private createAudioAnalyser(stream: MediaStream): AnalyserNode | null {
    if (!this.audioContext) {
      return null;
    }
    
    // 获取音频轨道
    const audioTrack = stream.getAudioTracks()[0];
    if (!audioTrack) {
      return null;
    }
    
    try {
      // 创建媒体流源
      const source = this.audioContext.createMediaStreamSource(stream);
      
      // 创建分析器
      const analyser = this.audioContext.createAnalyser();
      analyser.fftSize = 256;
      analyser.smoothingTimeConstant = 0.5;
      
      // 连接源到分析器
      source.connect(analyser);
      
      return analyser;
    } catch (error) {
      Debug.error('MediaStreamManager', 'Failed to create audio analyser:', error);
      return null;
    }
  }
  
  /**
   * 获取本地媒体流
   * @param type 媒体流类型
   * @param config 媒体流配置
   * @returns 媒体流信息
   */
  public async getLocalStream(type: MediaStreamType, config: MediaStreamConfig = {}): Promise<MediaStreamInfo> {
    try {
      // 合并配置
      const mergedConfig: MediaStreamConfig = {
        audioConstraints: this.config.defaultAudioConstraints,
        videoConstraints: this.config.defaultVideoConstraints,
        screenShareConstraints: this.config.defaultScreenShareConstraints,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        stereo: false,
        autoPlay: this.config.enableAutoPlay,
        muted: false,
        mirror: true,
        videoQuality: MediaStreamQuality.HIGH,
        ...config,
      };
      
      // 构建媒体约束
      const constraints: MediaStreamConstraints = {};
      
      switch (type) {
        case MediaStreamType.AUDIO:
          constraints.audio = this.buildAudioConstraints(mergedConfig);
          break;
          
        case MediaStreamType.VIDEO:
          constraints.video = this.buildVideoConstraints(mergedConfig);
          break;
          
        case MediaStreamType.AUDIO_VIDEO:
          constraints.audio = this.buildAudioConstraints(mergedConfig);
          constraints.video = this.buildVideoConstraints(mergedConfig);
          break;
          
        case MediaStreamType.SCREEN_SHARE:
          return this.getScreenShareStream(mergedConfig);
      }
      
      // 获取媒体流
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      
      // 创建媒体流信息
      const streamInfo: MediaStreamInfo = {
        id: stream.id,
        type,
        quality: mergedConfig.videoQuality || MediaStreamQuality.HIGH,
        stream,
        audioTrack: stream.getAudioTracks()[0],
        videoTrack: stream.getVideoTracks()[0],
        enabled: true,
        muted: mergedConfig.muted || false,
        paused: false,
        createdAt: Date.now(),
        config: mergedConfig,
      };
      
      // 添加到本地媒体流映射表
      this.localStreams.set(stream.id, streamInfo);
      
      // 如果启用音频电平监测，则创建音频分析器
      if (this.config.enableAudioLevelMonitoring && streamInfo.audioTrack) {
        const analyser = this.createAudioAnalyser(stream);
        if (analyser) {
          this.audioAnalysers.set(stream.id, analyser);
        }
      }
      
      // 触发本地流添加事件
      this.emit('localStreamAdded', streamInfo);
      
      return streamInfo;
    } catch (error) {
      Debug.error('MediaStreamManager', `Failed to get local ${type} stream:`, error);
      this.emit('error', 'getLocalStream', error);
      throw error;
    }
  }
  
  /**
   * 获取屏幕共享流
   * @param config 媒体流配置
   * @returns 媒体流信息
   */
  private async getScreenShareStream(config: MediaStreamConfig): Promise<MediaStreamInfo> {
    try {
      // 获取屏幕共享流
      const stream = await (navigator.mediaDevices as any).getDisplayMedia({
        video: config.screenShareConstraints || this.config.defaultScreenShareConstraints,
        audio: config.audioConstraints || false,
      });
      
      // 创建媒体流信息
      const streamInfo: MediaStreamInfo = {
        id: stream.id,
        type: MediaStreamType.SCREEN_SHARE,
        quality: config.videoQuality || MediaStreamQuality.HIGH,
        stream,
        audioTrack: stream.getAudioTracks()[0],
        videoTrack: stream.getVideoTracks()[0],
        enabled: true,
        muted: config.muted || false,
        paused: false,
        createdAt: Date.now(),
        config,
      };
      
      // 添加到本地媒体流映射表
      this.localStreams.set(stream.id, streamInfo);
      
      // 监听屏幕共享结束事件
      if (streamInfo.videoTrack) {
        streamInfo.videoTrack.addEventListener('ended', () => {
          this.stopLocalStream(stream.id);
        });
      }
      
      // 触发本地流添加事件
      this.emit('localStreamAdded', streamInfo);
      
      return streamInfo;
    } catch (error) {
      Debug.error('MediaStreamManager', 'Failed to get screen share stream:', error);
      this.emit('error', 'getScreenShareStream', error);
      throw error;
    }
  }
  
  /**
   * 构建音频约束
   * @param config 媒体流配置
   * @returns 音频约束
   */
  private buildAudioConstraints(config: MediaStreamConfig): MediaTrackConstraints {
    const constraints: MediaTrackConstraints = {
      ...config.audioConstraints,
      echoCancellation: config.echoCancellation,
      noiseSuppression: config.noiseSuppression,
      autoGainControl: config.autoGainControl,
    };
    
    // 如果指定了设备ID，则使用指定的设备
    if (this.currentAudioInputDeviceId) {
      constraints.deviceId = { exact: this.currentAudioInputDeviceId };
    }
    
    return constraints;
  }
  
  /**
   * 构建视频约束
   * @param config 媒体流配置
   * @returns 视频约束
   */
  private buildVideoConstraints(config: MediaStreamConfig): MediaTrackConstraints {
    const constraints: MediaTrackConstraints = {
      ...config.videoConstraints,
    };
    
    // 根据质量设置分辨率和帧率
    switch (config.videoQuality) {
      case MediaStreamQuality.LOW:
        constraints.width = { ideal: config.width || 640 };
        constraints.height = { ideal: config.height || 360 };
        constraints.frameRate = { ideal: config.frameRate || 15 };
        break;
        
      case MediaStreamQuality.MEDIUM:
        constraints.width = { ideal: config.width || 960 };
        constraints.height = { ideal: config.height || 540 };
        constraints.frameRate = { ideal: config.frameRate || 24 };
        break;
        
      case MediaStreamQuality.HIGH:
        constraints.width = { ideal: config.width || 1280 };
        constraints.height = { ideal: config.height || 720 };
        constraints.frameRate = { ideal: config.frameRate || 30 };
        break;
        
      case MediaStreamQuality.ULTRA:
        constraints.width = { ideal: config.width || 1920 };
        constraints.height = { ideal: config.height || 1080 };
        constraints.frameRate = { ideal: config.frameRate || 60 };
        break;
    }
    
    // 如果指定了设备ID，则使用指定的设备
    if (this.currentVideoInputDeviceId) {
      constraints.deviceId = { exact: this.currentVideoInputDeviceId };
    }
    
    return constraints;
  }
  
  /**
   * 停止本地媒体流
   * @param streamId 流ID
   * @returns 是否成功停止
   */
  public stopLocalStream(streamId: string): boolean {
    const streamInfo = this.localStreams.get(streamId);
    if (!streamInfo) {
      return false;
    }
    
    // 停止所有轨道
    streamInfo.stream.getTracks().forEach(track => track.stop());
    
    // 从本地媒体流映射表中移除
    this.localStreams.delete(streamId);
    
    // 移除音频分析器
    this.audioAnalysers.delete(streamId);
    
    // 触发本地流移除事件
    this.emit('localStreamRemoved', streamInfo);
    
    return true;
  }
  
  /**
   * 停止所有本地媒体流
   */
  public stopAllLocalStreams(): void {
    for (const streamId of this.localStreams.keys()) {
      this.stopLocalStream(streamId);
    }
  }
  
  /**
   * 添加远程媒体流
   * @param stream 媒体流
   * @param userId 用户ID
   * @param type 媒体流类型
   * @param config 媒体流配置
   * @returns 媒体流信息
   */
  public addRemoteStream(
    stream: MediaStream,
    userId: string,
    type: MediaStreamType = MediaStreamType.AUDIO_VIDEO,
    config: MediaStreamConfig = {}
  ): MediaStreamInfo {
    // 创建媒体流信息
    const streamInfo: MediaStreamInfo = {
      id: stream.id,
      type,
      quality: config.videoQuality || MediaStreamQuality.HIGH,
      stream,
      audioTrack: stream.getAudioTracks()[0],
      videoTrack: stream.getVideoTracks()[0],
      enabled: true,
      muted: config.muted || false,
      paused: false,
      createdAt: Date.now(),
      config,
      userId,
    };
    
    // 添加到远程媒体流映射表
    this.remoteStreams.set(stream.id, streamInfo);
    
    // 如果启用音频电平监测，则创建音频分析器
    if (this.config.enableAudioLevelMonitoring && streamInfo.audioTrack) {
      const analyser = this.createAudioAnalyser(stream);
      if (analyser) {
        this.audioAnalysers.set(stream.id, analyser);
      }
    }
    
    // 触发远程流添加事件
    this.emit('remoteStreamAdded', streamInfo);
    
    return streamInfo;
  }
  
  /**
   * 移除远程媒体流
   * @param streamId 流ID
   * @returns 是否成功移除
   */
  public removeRemoteStream(streamId: string): boolean {
    const streamInfo = this.remoteStreams.get(streamId);
    if (!streamInfo) {
      return false;
    }
    
    // 从远程媒体流映射表中移除
    this.remoteStreams.delete(streamId);
    
    // 移除音频分析器
    this.audioAnalysers.delete(streamId);
    
    // 触发远程流移除事件
    this.emit('remoteStreamRemoved', streamInfo);
    
    return true;
  }
  
  /**
   * 获取本地媒体流信息
   * @param streamId 流ID
   * @returns 媒体流信息
   */
  public getLocalStreamInfo(streamId: string): MediaStreamInfo | undefined {
    return this.localStreams.get(streamId);
  }
  
  /**
   * 获取远程媒体流信息
   * @param streamId 流ID
   * @returns 媒体流信息
   */
  public getRemoteStreamInfo(streamId: string): MediaStreamInfo | undefined {
    return this.remoteStreams.get(streamId);
  }
  
  /**
   * 获取所有本地媒体流
   * @returns 媒体流信息列表
   */
  public getAllLocalStreams(): MediaStreamInfo[] {
    return Array.from(this.localStreams.values());
  }
  
  /**
   * 获取所有远程媒体流
   * @returns 媒体流信息列表
   */
  public getAllRemoteStreams(): MediaStreamInfo[] {
    return Array.from(this.remoteStreams.values());
  }
  
  /**
   * 获取用户的远程媒体流
   * @param userId 用户ID
   * @returns 媒体流信息列表
   */
  public getUserRemoteStreams(userId: string): MediaStreamInfo[] {
    return Array.from(this.remoteStreams.values()).filter(stream => stream.userId === userId);
  }
  
  /**
   * 设置音频输入设备
   * @param deviceId 设备ID
   */
  public setAudioInputDevice(deviceId: string): void {
    this.currentAudioInputDeviceId = deviceId;
    this.emit('audioInputDeviceChanged', deviceId);
  }
  
  /**
   * 设置音频输出设备
   * @param deviceId 设备ID
   */
  public setAudioOutputDevice(deviceId: string): void {
    this.currentAudioOutputDeviceId = deviceId;
    this.emit('audioOutputDeviceChanged', deviceId);
  }
  
  /**
   * 设置视频输入设备
   * @param deviceId 设备ID
   */
  public setVideoInputDevice(deviceId: string): void {
    this.currentVideoInputDeviceId = deviceId;
    this.emit('videoInputDeviceChanged', deviceId);
  }
  
  /**
   * 获取音频输入设备列表
   * @returns 设备列表
   */
  public getAudioInputDevices(): MediaDeviceInfo[] {
    return this.audioInputDevices;
  }
  
  /**
   * 获取音频输出设备列表
   * @returns 设备列表
   */
  public getAudioOutputDevices(): MediaDeviceInfo[] {
    return this.audioOutputDevices;
  }
  
  /**
   * 获取视频输入设备列表
   * @returns 设备列表
   */
  public getVideoInputDevices(): MediaDeviceInfo[] {
    return this.videoInputDevices;
  }
  
  /**
   * 销毁管理器
   */
  public dispose(): void {
    // 停止所有本地媒体流
    this.stopAllLocalStreams();
    
    // 清空远程媒体流
    this.remoteStreams.clear();
    
    // 停止音频电平监测
    this.stopAudioLevelMonitoring();
    
    // 关闭音频上下文
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    // 移除设备变更事件监听器
    if (this.config.enableDeviceChangeDetection) {
      navigator.mediaDevices.removeEventListener('devicechange', this.handleDeviceChange.bind(this));
    }
    
    // 移除所有监听器
    this.removeAllListeners();
  }
}
