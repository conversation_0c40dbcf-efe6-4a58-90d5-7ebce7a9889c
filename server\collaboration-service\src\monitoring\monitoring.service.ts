/**
 * 协作监控服务
 * 用于监控协作服务的性能和状态
 */
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as os from 'os';
import { CollaborationGateway } from '../collaboration/collaboration.gateway';

/**
 * 监控指标接口
 */
export interface MonitoringMetrics {
  /**
   * 时间戳
   */
  timestamp: number;

  /**
   * CPU使用率（0-1）
   */
  cpuUsage: number;

  /**
   * 内存使用（MB）
   */
  memoryUsage: number;

  /**
   * 总内存（MB）
   */
  totalMemory: number;

  /**
   * 活跃连接数
   */
  activeConnections: number;

  /**
   * 消息队列长度
   */
  messageQueueLength: number;

  /**
   * 每秒消息数
   */
  messagesPerSecond: number;

  /**
   * 每秒操作数
   */
  operationsPerSecond: number;

  /**
   * 冲突数
   */
  conflictCount: number;

  /**
   * 冲突率（0-1）
   */
  conflictRate: number;

  /**
   * 平均响应时间（毫秒）
   */
  averageResponseTime: number;

  /**
   * 服务器负载
   */
  serverLoad: number[];
}

/**
 * 告警级别枚举
 */
export enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

/**
 * 告警接口
 */
export interface Alert {
  /**
   * 告警ID
   */
  id: string;

  /**
   * 告警级别
   */
  level: AlertLevel;

  /**
   * 告警消息
   */
  message: string;

  /**
   * 告警时间
   */
  timestamp: number;

  /**
   * 告警指标
   */
  metric?: string;

  /**
   * 告警值
   */
  value?: number;

  /**
   * 告警阈值
   */
  threshold?: number;
}

/**
 * 监控配置接口
 */
export interface MonitoringConfig {
  /**
   * 采样间隔（毫秒）
   */
  sampleInterval?: number;

  /**
   * 历史数据保留时间（毫秒）
   */
  historyDuration?: number;

  /**
   * 告警阈值
   */
  alertThresholds?: {
    /**
     * CPU使用率阈值（0-1）
     */
    cpuUsage?: number;

    /**
     * 内存使用率阈值（0-1）
     */
    memoryUsage?: number;

    /**
     * 活跃连接数阈值
     */
    activeConnections?: number;

    /**
     * 消息队列长度阈值
     */
    messageQueueLength?: number;

    /**
     * 冲突率阈值（0-1）
     */
    conflictRate?: number;

    /**
     * 平均响应时间阈值（毫秒）
     */
    averageResponseTime?: number;
  };
}

/**
 * 协作监控服务
 */
@Injectable()
export class MonitoringService {
  private readonly logger = new Logger(MonitoringService.name);
  private metrics: MonitoringMetrics[] = [];
  private alerts: Alert[] = [];
  private sampleInterval: NodeJS.Timeout;
  private messageCount: number = 0;
  private operationCount: number = 0;
  private conflictCount: number = 0;
  private lastMessageCount: number = 0;
  private lastOperationCount: number = 0;
  private responseTimes: number[] = [];
  private config: Required<MonitoringConfig>;

  /**
   * 创建监控服务
   * @param eventEmitter 事件发射器
   * @param collaborationGateway 协作网关
   */
  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly collaborationGateway: CollaborationGateway,
  ) {
    // 默认配置
    this.config = {
      sampleInterval: 5000, // 5秒
      historyDuration: 3600000, // 1小时
      alertThresholds: {
        cpuUsage: 0.8, // 80%
        memoryUsage: 0.8, // 80%
        activeConnections: 1000,
        messageQueueLength: 1000,
        conflictRate: 0.2, // 20%
        averageResponseTime: 500, // 500毫秒
      },
    };

    // 监听事件
    this.eventEmitter.on('collaboration.message', this.handleMessage.bind(this));
    this.eventEmitter.on('collaboration.operation', this.handleOperation.bind(this));
    this.eventEmitter.on('collaboration.conflict', this.handleConflict.bind(this));
    this.eventEmitter.on('collaboration.response', this.handleResponse.bind(this));

    // 启动采样
    this.startSampling();
  }

  /**
   * 启动采样
   */
  private startSampling(): void {
    this.sampleInterval = setInterval(() => {
      this.sampleMetrics();
    }, this.config.sampleInterval);
  }

  /**
   * 停止采样
   */
  public stopSampling(): void {
    clearInterval(this.sampleInterval);
  }

  /**
   * 采样指标
   */
  private async sampleMetrics(): Promise<void> {
    try {
      // 计算CPU使用率
      const cpuUsage = await this.getCpuUsage();

      // 计算内存使用
      const memoryUsage = process.memoryUsage();
      const totalMemory = os.totalmem();
      const usedMemory = memoryUsage.rss;

      // 计算每秒消息数和操作数
      const messagesPerSecond = (this.messageCount - this.lastMessageCount) / (this.config.sampleInterval / 1000);
      const operationsPerSecond = (this.operationCount - this.lastOperationCount) / (this.config.sampleInterval / 1000);
      this.lastMessageCount = this.messageCount;
      this.lastOperationCount = this.operationCount;

      // 计算平均响应时间
      const averageResponseTime = this.responseTimes.length > 0
        ? this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length
        : 0;
      this.responseTimes = [];

      // 计算冲突率
      const conflictRate = this.operationCount > 0 ? this.conflictCount / this.operationCount : 0;

      // 创建指标对象
      const metrics: MonitoringMetrics = {
        timestamp: Date.now(),
        cpuUsage,
        memoryUsage: usedMemory / (1024 * 1024), // MB
        totalMemory: totalMemory / (1024 * 1024), // MB
        activeConnections: this.collaborationGateway.getActiveConnections(),
        messageQueueLength: this.collaborationGateway.getMessageQueueLength(),
        messagesPerSecond,
        operationsPerSecond,
        conflictCount: this.conflictCount,
        conflictRate,
        averageResponseTime,
        serverLoad: os.loadavg(),
      };

      // 添加到历史数据
      this.metrics.push(metrics);

      // 清理过期数据
      this.cleanupHistory();

      // 检查告警
      this.checkAlerts(metrics);

      // 发出指标更新事件
      this.eventEmitter.emit('monitoring.metrics', metrics);
    } catch (error) {
      this.logger.error(`采样指标时出错: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取当前指标
   * @returns 当前指标
   */
  public getCurrentMetrics(): MonitoringMetrics | null {
    if (this.metrics.length === 0) {
      return null;
    }

    return this.metrics[this.metrics.length - 1];
  }

  /**
   * 获取历史指标
   * @param duration 时间范围（毫秒），默认为配置的历史数据保留时间
   * @returns 历史指标
   */
  public getHistoryMetrics(duration: number = this.config.historyDuration): MonitoringMetrics[] {
    const now = Date.now();
    const startTime = now - duration;

    return this.metrics.filter(metric => metric.timestamp >= startTime);
  }

  /**
   * 获取活跃告警
   * @returns 活跃告警
   */
  public getActiveAlerts(): Alert[] {
    return this.alerts;
  }

  /**
   * 清空历史数据
   */
  public clearHistory(): void {
    this.metrics = [];
    this.alerts = [];
    this.logger.log('历史数据已清空');
  }

  /**
   * 处理消息事件
   */
  private handleMessage(): void {
    this.messageCount++;
  }

  /**
   * 处理操作事件
   */
  private handleOperation(): void {
    this.operationCount++;
  }

  /**
   * 处理冲突事件
   */
  private handleConflict(): void {
    this.conflictCount++;
  }

  /**
   * 处理响应事件
   * @param responseTime 响应时间
   */
  private handleResponse(responseTime: number): void {
    this.responseTimes.push(responseTime);
  }

  /**
   * 清理历史数据
   */
  private cleanupHistory(): void {
    const now = Date.now();
    const cutoffTime = now - this.config.historyDuration;

    // 清理过期指标
    this.metrics = this.metrics.filter(metric => metric.timestamp >= cutoffTime);
  }

  /**
   * 检查告警
   * @param metrics 监控指标
   */
  private checkAlerts(metrics: MonitoringMetrics): void {
    const { alertThresholds } = this.config;

    // 检查CPU使用率
    if (metrics.cpuUsage > alertThresholds.cpuUsage) {
      this.createAlert(
        AlertLevel.WARNING,
        `CPU使用率过高: ${(metrics.cpuUsage * 100).toFixed(2)}%`,
        'cpuUsage',
        metrics.cpuUsage,
        alertThresholds.cpuUsage
      );
    }

    // 检查内存使用率
    const memoryUsageRatio = metrics.memoryUsage / metrics.totalMemory;
    if (memoryUsageRatio > alertThresholds.memoryUsage) {
      this.createAlert(
        AlertLevel.WARNING,
        `内存使用率过高: ${(memoryUsageRatio * 100).toFixed(2)}%`,
        'memoryUsage',
        memoryUsageRatio,
        alertThresholds.memoryUsage
      );
    }

    // 检查活跃连接数
    if (metrics.activeConnections > alertThresholds.activeConnections) {
      this.createAlert(
        AlertLevel.WARNING,
        `活跃连接数过多: ${metrics.activeConnections}`,
        'activeConnections',
        metrics.activeConnections,
        alertThresholds.activeConnections
      );
    }

    // 检查消息队列长度
    if (metrics.messageQueueLength > alertThresholds.messageQueueLength) {
      this.createAlert(
        AlertLevel.WARNING,
        `消息队列过长: ${metrics.messageQueueLength}`,
        'messageQueueLength',
        metrics.messageQueueLength,
        alertThresholds.messageQueueLength
      );
    }

    // 检查冲突率
    if (metrics.conflictRate > alertThresholds.conflictRate) {
      this.createAlert(
        AlertLevel.WARNING,
        `冲突率过高: ${(metrics.conflictRate * 100).toFixed(2)}%`,
        'conflictRate',
        metrics.conflictRate,
        alertThresholds.conflictRate
      );
    }

    // 检查平均响应时间
    if (metrics.averageResponseTime > alertThresholds.averageResponseTime) {
      this.createAlert(
        AlertLevel.WARNING,
        `平均响应时间过长: ${metrics.averageResponseTime.toFixed(2)}ms`,
        'averageResponseTime',
        metrics.averageResponseTime,
        alertThresholds.averageResponseTime
      );
    }

    // 检查服务器负载
    if (metrics.serverLoad[0] > os.cpus().length) {
      this.createAlert(
        AlertLevel.WARNING,
        `服务器负载过高: ${metrics.serverLoad[0].toFixed(2)}`,
        'serverLoad',
        metrics.serverLoad[0],
        os.cpus().length
      );
    }
  }

  /**
   * 创建告警
   * @param level 告警级别
   * @param message 告警消息
   * @param metric 告警指标
   * @param value 告警值
   * @param threshold 告警阈值
   */
  private createAlert(
    level: AlertLevel,
    message: string,
    metric?: string,
    value?: number,
    threshold?: number
  ): void {
    // 创建告警对象
    const alert: Alert = {
      id: this.generateId(),
      level,
      message,
      timestamp: Date.now(),
      metric,
      value,
      threshold,
    };

    // 添加到告警列表
    this.alerts.push(alert);

    // 发出告警事件
    this.eventEmitter.emit('monitoring.alert', alert);

    this.logger.warn(`监控告警: [${level}] ${message}`);
  }

  /**
   * 获取CPU使用率
   * @returns CPU使用率（0-1）
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise<number>((resolve) => {
      const startMeasure = this.getCpuInfo();

      // 等待100毫秒再次测量
      setTimeout(() => {
        const endMeasure = this.getCpuInfo();

        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;

        // 计算CPU使用率
        const cpuUsage = 1 - (idleDifference / totalDifference);
        resolve(cpuUsage);
      }, 100);
    });
  }

  /**
   * 获取CPU信息
   * @returns CPU信息
   */
  private getCpuInfo(): { idle: number; total: number } {
    const cpus = os.cpus();
    let idle = 0;
    let total = 0;

    for (const cpu of cpus) {
      for (const type in cpu.times) {
        total += cpu.times[type];
      }
      idle += cpu.times.idle;
    }

    return { idle, total };
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
}
