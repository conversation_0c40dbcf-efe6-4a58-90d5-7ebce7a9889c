/**
 * XR抓取组件
 * 用于处理VR/AR环境中的抓取交互
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventCallback } from '../../utils/EventEmitter';
import { Hand } from './GrabbableComponent';
/**
 * XR控制器类型
 */
export declare enum XRControllerType {
    /** 左手控制器 */
    LEFT = "left",
    /** 右手控制器 */
    RIGHT = "right",
    /** 头戴设备 */
    HEAD = "head",
    /** 手部追踪 */
    HAND = "hand"
}
/**
 * XR抓取组件配置
 */
export interface XRGrabComponentConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 控制器类型 */
    controllerType?: XRControllerType;
    /** 抓取按钮索引 */
    grabButtonIndex?: number;
    /** 释放按钮索引 */
    releaseButtonIndex?: number;
    /** 抓取距离 */
    grabDistance?: number;
    /** 是否使用射线检测 */
    useRaycasting?: boolean;
    /** 是否使用触觉反馈 */
    useHapticFeedback?: boolean;
    /** 触觉反馈强度 */
    hapticFeedbackIntensity?: number;
    /** 触觉反馈持续时间（毫秒） */
    hapticFeedbackDuration?: number;
    /** 是否使用手部姿势识别 */
    useHandPoseDetection?: boolean;
    /** 抓取回调 */
    onGrab?: (entity: Entity, hand: Hand) => void;
    /** 释放回调 */
    onRelease?: (entity: Entity, hand: Hand) => void;
}
/**
 * XR抓取组件
 */
export declare class XRGrabComponent extends Component {
    /** 组件类型 */
    static readonly TYPE: string;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否启用 */
    private _enabled;
    /** 控制器类型 */
    private _controllerType;
    /** 抓取按钮索引 */
    private _grabButtonIndex;
    /** 释放按钮索引 */
    private _releaseButtonIndex;
    /** 抓取距离 - 预留功能 */
    private _grabDistance;
    /** 是否使用射线检测 */
    private _useRaycasting;
    /** 是否使用触觉反馈 */
    private _useHapticFeedback;
    /** 触觉反馈强度 */
    private _hapticFeedbackIntensity;
    /** 触觉反馈持续时间（毫秒） */
    private _hapticFeedbackDuration;
    /** 是否使用手部姿势识别 - 预留功能 */
    private _useHandPoseDetection;
    /** 抓取回调 */
    private _onGrab?;
    /** 释放回调 */
    private _onRelease?;
    /** 当前抓取的实体 */
    private _grabbedEntity?;
    /** 控制器变换 */
    private _controllerTransform?;
    /** 控制器矩阵 */
    private _controllerMatrix;
    /** 抓取偏移矩阵 */
    private _grabOffsetMatrix;
    /** 是否正在抓取 */
    private _isGrabbing;
    /** 上一帧按钮状态 */
    private _prevButtonStates;
    /** 关联的抓取者组件 */
    private _grabberComponent?;
    /** XR会话 */
    private _xrSession?;
    /** XR输入源 */
    private _xrInputSource?;
    /** XR参考空间 - 预留功能 */
    private _xrReferenceSpace?;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: XRGrabComponentConfig);
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 设置XR会话
     * @param session XR会话
     */
    setXRSession(session: any): void;
    /**
     * 设置XR参考空间
     * @param referenceSpace XR参考空间
     */
    setXRReferenceSpace(referenceSpace: any): void;
    /**
     * 处理输入源变化
     * @param event 输入源变化事件
     */
    private handleInputSourcesChange;
    /**
     * 检查输入源是否匹配控制器类型
     * @param inputSource XR输入源
     * @returns 是否匹配
     */
    private matchesControllerType;
    /**
     * 更新组件
     * @param frame XR帧
     * @param referenceSpace XR参考空间
     */
    updateXR(frame: any, referenceSpace: any): void;
    /**
     * 更新控制器姿态
     * @param frame XR帧
     * @param referenceSpace XR参考空间
     */
    private updateControllerPose;
    /**
     * 更新按钮状态
     */
    private updateButtonStates;
    /**
     * 处理抓取按钮按下
     */
    private handleGrabButtonPressed;
    /**
     * 处理释放按钮按下
     */
    private handleReleaseButtonPressed;
    /**
     * 查找可抓取的实体
     * @returns 可抓取的实体
     */
    private findGrabbableEntity;
    /**
     * 抓取对象
     * @param entity 要抓取的实体
     */
    private grabObject;
    /**
     * 释放对象
     */
    private releaseObject;
    /**
     * 计算抓取偏移
     * @param entity 被抓取的实体
     */
    private calculateGrabOffset;
    /**
     * 更新被抓取对象的姿态
     */
    private updateGrabbedObjectPose;
    /**
     * 触发触觉反馈
     * @param intensity 强度（默认为配置的强度）
     */
    private triggerHapticFeedback;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    on(event: string, listener: EventCallback): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    off(event: string, listener?: EventCallback): this;
    /**
     * 销毁组件
     */
    destroy(): void;
}
