/**
 * World类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { System } from '../../src/core/System';
import { Scene } from '../../src/scene/Scene';
import { Transform } from '../../src/scene/Transform';

// 创建一个测试系统类
class TestSystem extends System {
  public initialized: boolean = false;
  public updated: boolean = false;
  public fixedUpdated: boolean = false;
  public disposed: boolean = false;
  
  constructor(priority: number = 0) {
    super(priority);
  }
  
  public initialize(): void {
    this.initialized = true;
  }
  
  public update(deltaTime: number): void {
    this.updated = true;
  }
  
  public fixedUpdate(fixedDeltaTime: number): void {
    this.fixedUpdated = true;
  }
  
  public dispose(): void {
    this.disposed = true;
  }
}

describe('World', () => {
  let engine: Engine;
  let world: World;
  
  // 在每个测试前创建一个新的世界实例
  beforeEach(() => {
    // 创建引擎实例
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    
    // 获取世界实例
    world = engine.getWorld();
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试世界初始化
  it('应该正确初始化世界', () => {
    expect(world).toBeDefined();
    expect(world.getEngine()).toBe(engine);
    expect(world.getEntities().size).toBe(0);
    expect(world.getSystems().length).toBe(0);
    expect(world.getActiveScene()).toBeNull();
  });
  
  // 测试实体管理
  it('应该能够创建和管理实体', () => {
    // 创建实体
    const entity = world.createEntity();
    
    // 验证实体已创建
    expect(entity).toBeDefined();
    expect(entity.getWorld()).toBe(world);
    expect(world.getEntities().size).toBe(1);
    expect(world.getEntities().has(entity.getId())).toBe(true);
    
    // 通过ID获取实体
    const retrievedEntity = world.getEntity(entity.getId());
    expect(retrievedEntity).toBe(entity);
    
    // 移除实体
    world.removeEntity(entity);
    
    // 验证实体已移除
    expect(world.getEntities().size).toBe(0);
    expect(world.getEntities().has(entity.getId())).toBe(false);
    expect(world.getEntity(entity.getId())).toBeUndefined();
  });
  
  // 测试系统管理
  it('应该能够添加和管理系统', () => {
    // 创建测试系统
    const system1 = new TestSystem(1);
    const system2 = new TestSystem(0);
    const system3 = new TestSystem(2);
    
    // 添加系统到世界
    world.addSystem(system1);
    world.addSystem(system2);
    world.addSystem(system3);
    
    // 验证系统已添加
    expect(world.getSystems().length).toBe(3);
    
    // 验证系统已按优先级排序
    expect(world.getSystems()[0]).toBe(system2);
    expect(world.getSystems()[1]).toBe(system1);
    expect(world.getSystems()[2]).toBe(system3);
    
    // 通过类型获取系统
    const retrievedSystem = world.getSystem(TestSystem);
    expect(retrievedSystem).toBe(system2); // 应该返回优先级最高的系统
    
    // 移除系统
    world.removeSystem(system1);
    
    // 验证系统已移除
    expect(world.getSystems().length).toBe(2);
    expect(world.getSystems().includes(system1)).toBe(false);
  });
  
  // 测试系统更新
  it('应该能够更新系统', () => {
    // 创建测试系统
    const system = new TestSystem();
    
    // 添加系统到世界
    world.addSystem(system);
    
    // 更新世界
    world.update(0.016); // 模拟16.6ms的帧时间
    
    // 验证系统已更新
    expect(system.updated).toBe(true);
    
    // 固定更新世界
    world.fixedUpdate(0.02); // 模拟20ms的固定更新时间
    
    // 验证系统已固定更新
    expect(system.fixedUpdated).toBe(true);
  });
  
  // 测试场景管理
  it('应该能够管理场景', () => {
    // 创建场景
    const scene1 = new Scene('场景1');
    const scene2 = new Scene('场景2');
    
    // 设置活跃场景
    world.setActiveScene(scene1);
    
    // 验证活跃场景
    expect(world.getActiveScene()).toBe(scene1);
    
    // 更改活跃场景
    world.setActiveScene(scene2);
    
    // 验证活跃场景已更改
    expect(world.getActiveScene()).toBe(scene2);
    
    // 清除活跃场景
    world.setActiveScene(null);
    
    // 验证活跃场景已清除
    expect(world.getActiveScene()).toBeNull();
  });
  
  // 测试查询实体
  it('应该能够查询实体', () => {
    // 创建实体
    const entity1 = world.createEntity();
    entity1.name = '实体1';
    entity1.addComponent(new Transform());
    
    const entity2 = world.createEntity();
    entity2.name = '实体2';
    
    const entity3 = world.createEntity();
    entity3.name = '实体3';
    entity3.addComponent(new Transform());
    
    // 通过名称查询实体
    const entitiesByName = world.findEntitiesByName('实体1');
    expect(entitiesByName.length).toBe(1);
    expect(entitiesByName[0]).toBe(entity1);
    
    // 通过组件类型查询实体
    const entitiesByComponent = world.findEntitiesByComponent(Transform);
    expect(entitiesByComponent.length).toBe(2);
    expect(entitiesByComponent.includes(entity1)).toBe(true);
    expect(entitiesByComponent.includes(entity3)).toBe(true);
    expect(entitiesByComponent.includes(entity2)).toBe(false);
  });
  
  // 测试事件系统
  it('应该能够发射和监听事件', () => {
    // 创建事件监听器
    const listener = vi.fn();
    
    // 添加事件监听器
    world.on('test', listener);
    
    // 发射事件
    world.emit('test', { data: 'test' });
    
    // 验证事件监听器被调用
    expect(listener).toHaveBeenCalledWith({ data: 'test' });
    
    // 移除事件监听器
    world.off('test', listener);
    
    // 再次发射事件
    world.emit('test', { data: 'test2' });
    
    // 验证事件监听器未被再次调用
    expect(listener).toHaveBeenCalledTimes(1);
  });
  
  // 测试实体层级结构
  it('应该能够管理实体层级结构', () => {
    // 创建父实体
    const parent = world.createEntity();
    parent.name = '父实体';
    
    // 创建子实体
    const child1 = world.createEntity();
    child1.name = '子实体1';
    
    const child2 = world.createEntity();
    child2.name = '子实体2';
    
    // 设置父子关系
    child1.setParent(parent);
    child2.setParent(parent);
    
    // 验证父子关系
    expect(child1.getParent()).toBe(parent);
    expect(child2.getParent()).toBe(parent);
    expect(parent.getChildren().length).toBe(2);
    expect(parent.getChildren().includes(child1)).toBe(true);
    expect(parent.getChildren().includes(child2)).toBe(true);
    
    // 移除父子关系
    child1.setParent(null);
    
    // 验证父子关系已移除
    expect(child1.getParent()).toBeNull();
    expect(parent.getChildren().length).toBe(1);
    expect(parent.getChildren().includes(child1)).toBe(false);
    expect(parent.getChildren().includes(child2)).toBe(true);
  });
  
  // 测试世界销毁
  it('应该能够正确销毁世界', () => {
    // 创建实体
    const entity = world.createEntity();
    
    // 创建系统
    const system = new TestSystem();
    world.addSystem(system);
    
    // 销毁世界
    world.dispose();
    
    // 验证实体和系统已清理
    expect(world.getEntities().size).toBe(0);
    expect(world.getSystems().length).toBe(0);
    expect(system.disposed).toBe(true);
  });
});
