/**
 * 认证服务
 */
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
  ) {}

  /**
   * 验证用户
   */
  async validateUser(usernameOrEmail: string, password: string): Promise<any> {
    try {
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'validateUser' }, { usernameOrEmail, password }),
      );
      return user;
    } catch (error) {
      this.logger.error('用户验证失败', error);
      throw new UnauthorizedException('用户名/邮箱或密码错误');
    }
  }

  /**
   * 登录
   */
  async login(user: any) {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    };

    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }

  /**
   * 注册
   */
  async register(username: string, email: string, password: string, displayName?: string) {
    try {
      const result = await firstValueFrom(
        this.userService.send({ cmd: 'register' }, { username, email, password, displayName }),
      );
      return result;
    } catch (error) {
      this.logger.error('用户注册失败', error);
      throw error;
    }
  }

  /**
   * 验证JWT
   */
  async validateJwt(payload: any): Promise<any> {
    try {
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'findUserById' }, payload.sub),
      );
      return user;
    } catch (error) {
      this.logger.error('JWT验证失败', error);
      throw new UnauthorizedException('无效的认证令牌');
    }
  }
}
