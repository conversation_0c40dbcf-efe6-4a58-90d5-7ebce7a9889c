import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';

@Injectable()
export class RAGService {
  private readonly ragEngineUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.ragEngineUrl = this.configService.get(
      'RAG_ENGINE_URL',
      'http://localhost:3002',
    );
  }

  /**
   * 执行RAG查询
   */
  async query(data: any): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.post(`${this.ragEngineUrl}/api/rag/query`, data),
    );
    return response.data;
  }

  /**
   * 流式RAG查询
   */
  async queryStream(data: any): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.post(`${this.ragEngineUrl}/api/rag/query/stream`, data, {
        responseType: 'stream',
      }),
    );
    return response.data;
  }

  /**
   * 向量搜索（不生成回答）
   */
  async search(data: any): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.post(`${this.ragEngineUrl}/api/rag/search`, data),
    );
    return response.data;
  }

  /**
   * 获取数字人绑定的知识库
   */
  async getDigitalHumanKnowledgeBases(digitalHumanId: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.get(
        `${this.ragEngineUrl}/api/rag/digital-humans/${digitalHumanId}/knowledge-bases`,
      ),
    );
    return response.data;
  }

  /**
   * 获取会话历史
   */
  async getSessionHistory(sessionId: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.get(`${this.ragEngineUrl}/api/rag/sessions/${sessionId}`),
    );
    return response.data;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.get(`${this.ragEngineUrl}/api/rag/health`),
    );
    return response.data;
  }

  /**
   * 获取RAG引擎统计信息
   */
  async getStats(): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.get(`${this.ragEngineUrl}/api/rag/stats`),
    );
    return response.data;
  }

  /**
   * 清除会话缓存
   */
  async clearSessionCache(sessionId: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.delete(`${this.ragEngineUrl}/api/rag/sessions/${sessionId}/cache`),
    );
    return response.data;
  }

  /**
   * 批量查询
   */
  async batchQuery(queries: any[]): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.post(`${this.ragEngineUrl}/api/rag/batch-query`, { queries }),
    );
    return response.data;
  }

  /**
   * 获取查询建议
   */
  async getQuerySuggestions(digitalHumanId: string, partialQuery: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.get(
        `${this.ragEngineUrl}/api/rag/digital-humans/${digitalHumanId}/suggestions`,
        {
          params: { q: partialQuery },
        },
      ),
    );
    return response.data;
  }

  /**
   * 评价回答质量
   */
  async rateAnswer(sessionId: string, rating: number, feedback?: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.post(`${this.ragEngineUrl}/api/rag/sessions/${sessionId}/rating`, {
        rating,
        feedback,
      }),
    );
    return response.data;
  }
}
