/**
 * 输入相关的可视化脚本节点
 */
import { VisualScriptNode } from '../VisualScriptNode';
/**
 * 键盘输入节点
 */
export declare class KeyboardInputNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 鼠标输入节点
 */
export declare class MouseInputNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 触摸输入节点
 */
export declare class TouchInputNode extends VisualScriptNode {
    constructor();
    execute(): any;
}
/**
 * 游戏手柄输入节点
 */
export declare class GamepadInputNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 注册输入节点
 */
export declare function registerInputNodes(): void;
