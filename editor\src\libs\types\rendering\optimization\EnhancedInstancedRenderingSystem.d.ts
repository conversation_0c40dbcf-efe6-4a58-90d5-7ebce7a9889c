/**
 * 增强的实例化渲染系统
 * 支持更多的实例属性和更高效的实例更新
 */
import * as THREE from 'three';
import { InstanceData } from './InstancedComponent';
import { InstancedRenderingSystem, InstancedRenderingSystemOptions } from './InstancedRenderingSystem';
/**
 * 增强的实例数据接口
 */
export interface EnhancedInstanceData extends InstanceData {
    /** 实例ID */
    id?: string;
    /** 旋转（四元数） - 为了兼容性添加rotation别名 */
    rotation?: THREE.Quaternion;
    /** 自定义属性 */
    customAttributes?: Map<string, number | number[] | THREE.Vector2 | THREE.Vector3 | THREE.Vector4 | THREE.Color>;
    /** 动画状态 */
    animationState?: {
        clipName: string;
        time: number;
        weight: number;
    };
    /** 物理属性 */
    physicsProperties?: {
        velocity: THREE.Vector3;
        angularVelocity: THREE.Vector3;
        mass: number;
    };
    /** 是否需要更新 */
    needsUpdate?: boolean;
    /** 是否可见 */
    visible?: boolean;
    /** 是否投射阴影 */
    castShadow?: boolean;
    /** 是否接收阴影 */
    receiveShadow?: boolean;
    /** 渲染顺序 */
    renderOrder?: number;
    /** 层级 */
    layer?: number;
    /** 用户数据 */
    userData?: any;
}
/**
 * 增强的实例化渲染系统配置接口
 */
export interface EnhancedInstancedRenderingSystemOptions extends InstancedRenderingSystemOptions {
    /** 是否使用GPU实例更新 */
    useGPUInstanceUpdate?: boolean;
    /** 是否使用实例批处理 */
    useInstanceBatching?: boolean;
    /** 是否使用实例合并 */
    useInstanceMerging?: boolean;
    /** 是否使用实例LOD */
    useInstanceLOD?: boolean;
    /** 是否使用实例剔除 */
    useInstanceCulling?: boolean;
    /** 是否使用实例缓存 */
    useInstanceCache?: boolean;
    /** 是否使用实例排序 */
    useInstanceSorting?: boolean;
    /** 是否使用实例分组 */
    useInstanceGrouping?: boolean;
    /** 是否使用实例动画 */
    useInstanceAnimation?: boolean;
    /** 是否使用实例物理 */
    useInstancePhysics?: boolean;
    /** 是否使用实例阴影 */
    useInstanceShadow?: boolean;
    /** 是否使用实例材质变体 */
    useInstanceMaterialVariants?: boolean;
    /** 最大批处理大小 */
    maxBatchSize?: number;
    /** 最大实例数量 */
    maxInstanceCount?: number;
    /** 更新间隔（帧） */
    updateInterval?: number;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 实例批处理组接口
 */
export interface InstanceBatchGroup {
    /** 批处理组ID */
    id: string;
    /** 几何体 */
    geometry: THREE.BufferGeometry;
    /** 材质 */
    material: THREE.Material;
    /** 实例化网格 */
    instancedMesh: THREE.InstancedMesh;
    /** 实例数据列表 */
    instances: EnhancedInstanceData[];
    /** 实例到索引的映射 */
    instanceToIndex: Map<string, number>;
    /** 可用索引列表 */
    availableIndices: number[];
    /** 是否需要更新 */
    needsUpdate: boolean;
    /** 是否可见 */
    visible: boolean;
    /** 是否投射阴影 */
    castShadow: boolean;
    /** 是否接收阴影 */
    receiveShadow: boolean;
    /** 渲染顺序 */
    renderOrder: number;
    /** 层级 */
    layer: number;
    /** 用户数据 */
    userData: any;
    /** 自定义属性缓冲区 */
    customAttributeBuffers: Map<string, THREE.InstancedBufferAttribute>;
}
/**
 * 增强的实例化渲染系统类
 */
export declare class EnhancedInstancedRenderingSystem extends InstancedRenderingSystem {
    /** 系统类型 */
    protected static readonly TYPE: string;
    /** 是否使用GPU实例更新 */
    private useGPUInstanceUpdate;
    /** 是否使用实例批处理 */
    private useInstanceBatching;
    /** 是否使用实例合并 */
    private useInstanceMerging;
    /** 是否使用实例LOD */
    private useInstanceLOD;
    /** 是否使用实例剔除 */
    private useInstanceCulling;
    /** 是否使用实例缓存 */
    private useInstanceCache;
    /** 是否使用实例排序 */
    private useInstanceSorting;
    /** 是否使用实例分组 */
    private useInstanceGrouping;
    /** 是否使用实例动画 */
    private useInstanceAnimation;
    /** 是否使用实例物理 */
    private useInstancePhysics;
    /** 是否使用实例阴影 */
    private useInstanceShadow;
    /** 是否使用实例材质变体 */
    private useInstanceMaterialVariants;
    /** 最大批处理大小 */
    private maxBatchSize;
    /** 最大实例数量 */
    private maxInstanceCount;
    /** 更新间隔（帧） */
    private updateInterval;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 当前帧计数 */
    private enhancedFrameCount;
    /** 实例批处理组列表 */
    private batchGroups;
    /** 几何体到批处理组的映射 */
    private geometryToBatchGroup;
    /** 材质到批处理组的映射 */
    private materialToBatchGroup;
    /** 实例到批处理组的映射 */
    private instanceToBatchGroup;
    /** 实例到实例数据的映射 */
    private instanceToData;
    /** 实例计数器 */
    private instanceCounter;
    /** 批处理组计数器 */
    private batchGroupCounter;
    /** 自定义属性定义列表 */
    private customAttributeDefinitions;
    /** 实例更新器 */
    private instanceUpdater;
    /** 调试可视化材质 */
    private debugMaterial;
    /** 调试可视化网格 */
    private debugMeshes;
    /**
     * 创建增强的实例化渲染系统
     * @param options 增强的实例化渲染系统配置
     */
    constructor(options?: EnhancedInstancedRenderingSystemOptions);
    /**
     * 初始化自定义属性定义
     */
    private initializeCustomAttributeDefinitions;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 获取场景
     * @returns 场景
     */
    private getScene;
    /**
     * 更新批处理组
     * @param camera 相机
     */
    private updateBatchGroups;
    /**
     * 更新批处理组
     * @param batchGroup 批处理组
     * @param camera 相机
     */
    private updateBatchGroup;
    /**
     * 使用CPU更新实例
     * @param batchGroup 批处理组
     * @param camera 相机
     */
    private updateInstancesWithCPU;
    /**
     * 检查实例是否在视锥体内
     * @param instance 实例数据
     * @param camera 相机
     * @returns 是否在视锥体内
     */
    private isInstanceInFrustum;
    /**
     * 更新自定义属性
     * @param batchGroup 批处理组
     */
    private updateCustomAttributes;
    /**
     * 创建批处理组
     * @param geometry 几何体
     * @param material 材质
     * @param maxInstances 最大实例数
     * @returns 批处理组
     */
    createBatchGroup(geometry: THREE.BufferGeometry, material: THREE.Material, maxInstances?: number): InstanceBatchGroup;
    /**
     * 获取或创建批处理组
     * @param geometry 几何体
     * @param material 材质
     * @returns 批处理组
     */
    getOrCreateBatchGroup(geometry: THREE.BufferGeometry, material: THREE.Material): InstanceBatchGroup;
    /**
     * 添加增强实例
     * @param geometry 几何体
     * @param material 材质
     * @param instanceData 实例数据
     * @returns 实例ID
     */
    addEnhancedInstance(geometry: THREE.BufferGeometry, material: THREE.Material, instanceData: EnhancedInstanceData): string;
    /**
     * 更新增强实例
     * @param instanceId 实例ID
     * @param instanceData 实例数据
     */
    updateEnhancedInstance(instanceId: string, instanceData: Partial<EnhancedInstanceData>): void;
    /**
     * 移除增强实例
     * @param instanceId 实例ID
     */
    removeEnhancedInstance(instanceId: string): void;
    /**
     * 创建自定义实例属性
     * @param name 属性名
     * @param size 属性大小
     * @param type 属性类型
     */
    createInstanceAttribute(name: string, size: number, type?: number): void;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 销毁系统
     */
    destroy(): void;
}
