import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsObject,
  IsArray,
  MaxLength,
  MinLength,
  IsNotEmpty,
} from 'class-validator';
import { ParameterType, ParameterCategory } from '../entities/template-parameter.entity';

export class CreateTemplateParameterDto {
  @ApiProperty({
    description: '参数名称',
    example: '桌子颜色',
    minLength: 1,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: '参数键名',
    example: 'deskColor',
    minLength: 1,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(100)
  key: string;

  @ApiPropertyOptional({
    description: '参数描述',
    example: '设置办公桌的颜色',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;

  @ApiProperty({
    description: '参数类型',
    enum: ParameterType,
    example: ParameterType.COLOR,
  })
  @IsEnum(ParameterType)
  type: ParameterType;

  @ApiPropertyOptional({
    description: '参数分类',
    enum: ParameterCategory,
    example: ParameterCategory.MATERIALS,
  })
  @IsOptional()
  @IsEnum(ParameterCategory)
  category?: ParameterCategory;

  @ApiPropertyOptional({
    description: '默认值',
    example: '#8B4513',
  })
  @IsOptional()
  defaultValue?: any;

  @ApiPropertyOptional({
    description: '最小值（用于数值类型）',
    example: 0,
  })
  @IsOptional()
  minValue?: any;

  @ApiPropertyOptional({
    description: '最大值（用于数值类型）',
    example: 100,
  })
  @IsOptional()
  maxValue?: any;

  @ApiPropertyOptional({
    description: '步长（用于数值类型）',
    example: 0.1,
  })
  @IsOptional()
  step?: any;

  @ApiPropertyOptional({
    description: '选项列表（用于枚举类型）',
    type: [String],
    example: ['木质', '金属', '玻璃', '塑料'],
  })
  @IsOptional()
  @IsArray()
  options?: any[];

  @ApiPropertyOptional({
    description: '验证规则',
    example: {
      required: true,
      pattern: '^#[0-9A-Fa-f]{6}$',
      message: '请输入有效的颜色值',
    },
  })
  @IsOptional()
  @IsObject()
  validation?: Record<string, any>;

  @ApiPropertyOptional({
    description: '显示标签',
    example: '桌子颜色',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  label?: string;

  @ApiPropertyOptional({
    description: '提示信息',
    example: '选择办公桌的颜色，支持十六进制颜色值',
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  tooltip?: string;

  @ApiPropertyOptional({
    description: '单位',
    example: 'px',
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  unit?: string;

  @ApiPropertyOptional({
    description: '参数分组',
    example: '材质设置',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  group?: string;

  @ApiPropertyOptional({
    description: '排序顺序',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  sortOrder?: number;

  @ApiPropertyOptional({
    description: '是否必填',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isRequired?: boolean;

  @ApiPropertyOptional({
    description: '是否为高级参数',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isAdvanced?: boolean;

  @ApiPropertyOptional({
    description: '是否可见',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isVisible?: boolean;

  @ApiPropertyOptional({
    description: '是否只读',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isReadonly?: boolean;

  @ApiPropertyOptional({
    description: '显示条件',
    example: {
      parameter: 'enableCustomColor',
      value: true,
    },
  })
  @IsOptional()
  @IsObject()
  showCondition?: Record<string, any>;

  @ApiPropertyOptional({
    description: '隐藏条件',
    example: {
      parameter: 'useDefaultColor',
      value: true,
    },
  })
  @IsOptional()
  @IsObject()
  hideCondition?: Record<string, any>;
}

export class UpdateTemplateParameterDto {
  @ApiPropertyOptional({
    description: '参数名称',
    minLength: 1,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({
    description: '参数键名',
    minLength: 1,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  key?: string;

  @ApiPropertyOptional({
    description: '参数描述',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;

  @ApiPropertyOptional({
    description: '参数类型',
    enum: ParameterType,
  })
  @IsOptional()
  @IsEnum(ParameterType)
  type?: ParameterType;

  @ApiPropertyOptional({
    description: '参数分类',
    enum: ParameterCategory,
  })
  @IsOptional()
  @IsEnum(ParameterCategory)
  category?: ParameterCategory;

  @ApiPropertyOptional({
    description: '默认值',
  })
  @IsOptional()
  defaultValue?: any;

  @ApiPropertyOptional({
    description: '最小值',
  })
  @IsOptional()
  minValue?: any;

  @ApiPropertyOptional({
    description: '最大值',
  })
  @IsOptional()
  maxValue?: any;

  @ApiPropertyOptional({
    description: '步长',
  })
  @IsOptional()
  step?: any;

  @ApiPropertyOptional({
    description: '选项列表',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  options?: any[];

  @ApiPropertyOptional({
    description: '验证规则',
  })
  @IsOptional()
  @IsObject()
  validation?: Record<string, any>;

  @ApiPropertyOptional({
    description: '显示标签',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  label?: string;

  @ApiPropertyOptional({
    description: '提示信息',
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  tooltip?: string;

  @ApiPropertyOptional({
    description: '单位',
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  unit?: string;

  @ApiPropertyOptional({
    description: '参数分组',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  group?: string;

  @ApiPropertyOptional({
    description: '排序顺序',
  })
  @IsOptional()
  @IsNumber()
  sortOrder?: number;

  @ApiPropertyOptional({
    description: '是否必填',
  })
  @IsOptional()
  @IsBoolean()
  isRequired?: boolean;

  @ApiPropertyOptional({
    description: '是否为高级参数',
  })
  @IsOptional()
  @IsBoolean()
  isAdvanced?: boolean;

  @ApiPropertyOptional({
    description: '是否可见',
  })
  @IsOptional()
  @IsBoolean()
  isVisible?: boolean;

  @ApiPropertyOptional({
    description: '是否只读',
  })
  @IsOptional()
  @IsBoolean()
  isReadonly?: boolean;

  @ApiPropertyOptional({
    description: '显示条件',
  })
  @IsOptional()
  @IsObject()
  showCondition?: Record<string, any>;

  @ApiPropertyOptional({
    description: '隐藏条件',
  })
  @IsOptional()
  @IsObject()
  hideCondition?: Record<string, any>;
}
