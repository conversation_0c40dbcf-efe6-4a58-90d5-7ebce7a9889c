/**
 * 面部动画预设面板样式
 */
.facial-animation-preset-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  // 标签页样式
  .ant-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;

    .ant-tabs-tab {
      .ant-badge {
        .ant-badge-count {
          font-size: 10px;
          height: 16px;
          line-height: 16px;
          min-width: 16px;
          padding: 0 4px;
        }
      }
    }

    .ant-tabs-content {
      flex: 1;
      height: 0;

      .ant-tabs-tabpane {
        height: 100%;
        padding: 0;
      }
    }

    .ant-tabs-extra-content {
      .ant-space {
        align-items: center;
      }
    }
  }

  // 统计信息卡片样式
  .ant-card {
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &.statistics-card {
      .ant-statistic {
        .ant-statistic-title {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }

        .ant-statistic-content {
          .ant-statistic-content-value {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
          }

          .ant-statistic-content-prefix {
            margin-right: 4px;
            color: #1890ff;
          }
        }
      }
    }

    &.quick-actions-card {
      .ant-space {
        width: 100%;
        flex-wrap: wrap;
      }

      .ant-input-affix-wrapper {
        border-radius: 6px;

        .ant-input-prefix {
          color: #bfbfbf;
        }
      }

      .ant-select {
        .ant-select-selector {
          border-radius: 6px;
        }
      }

      .ant-btn {
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.ant-btn-primary {
          background: #1890ff;
          border-color: #1890ff;
        }
      }
    }
  }

  // 历史记录模态框样式
  .ant-modal {
    &.history-modal {
      .ant-card {
        border-radius: 8px;
        transition: all 0.3s ease;
        margin-bottom: 8px;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transform: translateY(-1px);
        }

        .ant-card-meta {
          .ant-card-meta-title {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
          }

          .ant-card-meta-description {
            font-size: 12px;
            color: #666;
          }
        }

        .ant-card-actions {
          background: #fafafa;
          border-top: 1px solid #f0f0f0;

          li {
            margin: 0;

            .ant-btn-link {
              color: #1890ff;
              font-size: 12px;
            }
          }
        }
      }
    }

    &.quick-preview-modal {
      .preview-content {
        .preview-header {
          margin-bottom: 16px;

          h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
          }

          p {
            margin: 4px 0 0 0;
            color: #666;
            font-size: 14px;
          }
        }

        .preview-placeholder {
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          color: #666;

          .anticon {
            margin-bottom: 16px;
            animation: pulse 2s infinite;
          }

          @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
          }
        }
      }
    }
  }

  // 最近使用标签页样式
  .recent-tab {
    padding: 16px;

    .ant-card {
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .ant-card-meta {
        .ant-card-meta-title {
          font-size: 13px;
          font-weight: 500;
          color: #262626;
        }

        .ant-card-meta-description {
          font-size: 11px;
          color: #8c8c8c;
        }
      }

      .ant-card-actions {
        background: #fafafa;

        li {
          .ant-btn-link {
            font-size: 11px;
            padding: 0;
          }
        }
      }
    }

    .view-all-button {
      text-align: center;
      margin-top: 16px;

      .ant-btn {
        border-radius: 20px;
        font-size: 13px;
      }
    }
  }

  // 批量操作样式
  &.batch-mode {
    .batch-indicator {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      background: #1890ff;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 12px;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-card.quick-actions-card {
      .ant-space .ant-space-item {
        margin-bottom: 8px;
        width: 100%;

        .ant-input-affix-wrapper,
        .ant-select,
        .ant-btn {
          width: 100%;
        }
      }
    }

    .recent-tab .ant-row .ant-col {
      margin-bottom: 16px;
    }
  }

  // 动画效果
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
