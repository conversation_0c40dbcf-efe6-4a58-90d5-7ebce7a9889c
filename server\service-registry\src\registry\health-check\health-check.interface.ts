/**
 * 健康检查接口和类型定义
 */

/**
 * 健康检查类型
 */
export enum HealthCheckType {
  /** HTTP健康检查 */
  HTTP = 'http',
  /** TCP健康检查 */
  TCP = 'tcp',
  /** 脚本健康检查 */
  SCRIPT = 'script',
  /** 自定义健康检查 */
  CUSTOM = 'custom',
}

/**
 * 健康检查结果状态
 */
export enum HealthCheckStatus {
  /** 健康 */
  HEALTHY = 'healthy',
  /** 不健康 */
  UNHEALTHY = 'unhealthy',
  /** 降级 */
  DEGRADED = 'degraded',
  /** 未知 */
  UNKNOWN = 'unknown',
}

/**
 * 健康检查结果
 */
export interface HealthCheckResult {
  /** 状态 */
  status: HealthCheckStatus;
  /** 详情 */
  details?: string;
  /** 检查时间 */
  timestamp: Date;
  /** 响应时间（毫秒） */
  responseTime?: number;
  /** 额外信息 */
  metadata?: Record<string, any>;
}

/**
 * 健康检查配置
 */
export interface HealthCheckConfig {
  /** 健康检查类型 */
  type: HealthCheckType;
  /** 健康检查URL或地址 */
  target: string;
  /** 健康检查间隔（毫秒） */
  interval: number;
  /** 健康检查超时（毫秒） */
  timeout: number;
  /** 健康阈值（连续成功次数） */
  healthyThreshold: number;
  /** 不健康阈值（连续失败次数） */
  unhealthyThreshold: number;
  /** 是否启用 */
  enabled: boolean;
  /** 额外配置 */
  options?: Record<string, any>;
}

/**
 * HTTP健康检查配置
 */
export interface HttpHealthCheckConfig extends HealthCheckConfig {
  /** 请求方法 */
  method?: 'GET' | 'POST' | 'HEAD';
  /** 请求头 */
  headers?: Record<string, string>;
  /** 请求体 */
  body?: string;
  /** 期望的状态码 */
  expectedStatus?: number[];
  /** 期望的响应内容 */
  expectedContent?: string;
}

/**
 * TCP健康检查配置
 */
export interface TcpHealthCheckConfig extends HealthCheckConfig {
  /** 发送数据 */
  send?: string;
  /** 期望的响应数据 */
  expect?: string;
}

/**
 * 脚本健康检查配置
 */
export interface ScriptHealthCheckConfig extends HealthCheckConfig {
  /** 脚本内容 */
  script: string;
  /** 脚本参数 */
  args?: string[];
  /** 脚本超时（毫秒） */
  scriptTimeout?: number;
  /** 期望的退出码 */
  expectedExitCode?: number[];
}

/**
 * 健康检查策略接口
 */
export interface HealthCheckStrategy {
  /**
   * 执行健康检查
   * @param config 健康检查配置
   */
  check(config: HealthCheckConfig): Promise<HealthCheckResult>;
}
