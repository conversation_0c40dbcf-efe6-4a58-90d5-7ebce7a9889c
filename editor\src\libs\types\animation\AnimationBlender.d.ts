import { Animator } from './Animator';
import type { Entity } from '../core/Entity';
import { AnimationMask } from './utils/AnimationMask';
import { AnimationSubClip } from './utils/AnimationSubClip';
import { SubClip } from './SubClip';
import { PhysicsAnimationIntegration } from './PhysicsAnimationIntegration';
import { InputAnimationIntegration } from './InputAnimationIntegration';
/**
 * 混合模式
 */
export declare enum BlendMode {
    /** 覆盖 - 完全替换之前的动画 */
    OVERRIDE = "override",
    /** 叠加 - 在之前的动画基础上叠加 */
    ADDITIVE = "additive",
    /** 乘法 - 将当前动画与之前的动画相乘 */
    MULTIPLY = "multiply",
    /** 差值 - 计算当前动画与之前动画的差值 */
    DIFFERENCE = "difference",
    /** 平均 - 计算当前动画与之前动画的平均值 */
    AVERAGE = "average",
    /** 最大值 - 取当前动画与之前动画中的最大值 */
    MAX = "max",
    /** 最小值 - 取当前动画与之前动画中的最小值 */
    MIN = "min",
    /** 交叉淡入淡出 - 在两个动画之间平滑过渡 */
    CROSS_FADE = "crossFade",
    /** 混合树 - 基于参数混合多个动画 */
    BLEND_TREE = "blendTree",
    /** 加权 - 根据自定义权重混合多个动画 */
    WEIGHTED = "weighted",
    /** 分层 - 按层次混合动画 */
    LAYERED = "layered",
    /** 序列 - 按顺序播放多个动画 */
    SEQUENTIAL = "sequential"
}
/**
 * 混合曲线类型
 */
export declare enum BlendCurveType {
    /** 线性 */
    LINEAR = "linear",
    /** 缓入 */
    EASE_IN = "easeIn",
    /** 缓出 */
    EASE_OUT = "easeOut",
    /** 缓入缓出 */
    EASE_IN_OUT = "easeInOut",
    /** 弹性 */
    ELASTIC = "elastic",
    /** 弹跳 */
    BOUNCE = "bounce",
    /** 正弦 */
    SINE = "sine",
    /** 指数 */
    EXPONENTIAL = "exponential",
    /** 圆形 */
    CIRCULAR = "circular",
    /** 二次方 */
    QUADRATIC = "quadratic",
    /** 三次方 */
    CUBIC = "cubic",
    /** 四次方 */
    QUARTIC = "quartic",
    /** 五次方 */
    QUINTIC = "quintic",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 混合层
 */
export interface BlendLayer {
    /** 动画片段名称 */
    clipName: string;
    /** 权重 */
    weight: number;
    /** 时间缩放 */
    timeScale: number;
    /** 混合模式 */
    blendMode: BlendMode;
    /** 遮罩 */
    mask?: string[];
    /** 交叉淡入淡出时间（秒） */
    crossFadeTime?: number;
    /** 混合树参数 */
    blendTreeParams?: BlendTreeParams;
    /** 权重参数 */
    weightParams?: WeightedBlendParams;
    /** 层次参数 */
    layerParams?: LayeredBlendParams;
    /** 序列参数 */
    sequenceParams?: SequentialBlendParams;
}
/**
 * 混合树参数
 */
export interface BlendTreeParams {
    /** 参数名称 */
    paramName: string;
    /** 参数值 */
    paramValue: number;
    /** 节点列表 */
    nodes: BlendTreeNode[];
    /** 混合类型 */
    blendType: '1D' | '2D' | 'Direct';
}
/**
 * 混合树节点
 */
export interface BlendTreeNode {
    /** 动画片段名称 */
    clipName: string;
    /** 阈值 */
    threshold: number | {
        x: number;
        y: number;
    };
    /** 权重 */
    weight: number;
}
/**
 * 加权混合参数
 */
export interface WeightedBlendParams {
    /** 权重列表 */
    weights: {
        [clipName: string]: number;
    };
    /** 归一化 */
    normalize: boolean;
}
/**
 * 分层混合参数
 */
export interface LayeredBlendParams {
    /** 层次列表 */
    layers: {
        /** 动画片段名称 */
        clipName: string;
        /** 层次索引 */
        layerIndex: number;
        /** 权重 */
        weight: number;
        /** 遮罩 */
        mask?: string[];
    }[];
}
/**
 * 序列混合参数
 */
export interface SequentialBlendParams {
    /** 序列列表 */
    sequence: {
        /** 动画片段名称 */
        clipName: string;
        /** 持续时间（秒） */
        duration: number;
        /** 过渡时间（秒） */
        transitionTime: number;
    }[];
    /** 当前索引 */
    currentIndex: number;
    /** 是否循环 */
    loop: boolean;
}
/**
 * 混合事件类型
 */
export declare enum BlendEventType {
    /** 混合开始 */
    BLEND_START = "blendStart",
    /** 混合更新 */
    BLEND_UPDATE = "blendUpdate",
    /** 混合结束 */
    BLEND_END = "blendEnd",
    /** 混合空间更新 */
    BLEND_SPACE_UPDATE = "blendSpaceUpdate",
    /** 遮罩应用 */
    MASK_APPLIED = "maskApplied",
    /** 子片段创建 */
    SUBCLIP_CREATED = "subclipCreated",
    /** 物理集成 */
    PHYSICS_INTEGRATED = "physicsIntegrated",
    /** 输入集成 */
    INPUT_INTEGRATED = "inputIntegrated"
}
/**
 * 动画混合器
 */
export declare class AnimationBlender {
    /** 动画控制器 */
    private animator;
    /** 混合层列表 */
    private layers;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否启用 */
    private enabled;
    /** 当前混合时间 */
    private blendTime;
    /** 目标混合时间 */
    private targetBlendTime;
    /** 是否正在混合 */
    private isBlending;
    /** 混合曲线类型 */
    private blendCurveType;
    /** 自定义混合曲线 */
    private customBlendCurve?;
    /** 遮罩映射 */
    private masks;
    /** 子片段映射 */
    private subClips;
    /** 高级子片段映射 */
    private advancedSubClips;
    /** 物理动画集成 */
    private physicsIntegration?;
    /** 输入动画集成 */
    private inputIntegration?;
    /** 曲线计算缓存 */
    private curveCache;
    /** 混合权重缓存 */
    private weightCache;
    /** 对象池 */
    private objectPool;
    /** 是否启用缓存 */
    private cacheEnabled;
    /** 缓存精度 */
    private cacheResolution;
    /** 是否启用批处理 */
    private batchProcessingEnabled;
    /** 是否启用对象池 */
    private objectPoolEnabled;
    /**
     * 构造函数
     * @param animator 动画控制器
     */
    constructor(animator: Animator);
    /**
     * 添加混合层
     * @param clipName 动画片段名称
     * @param weight 权重
     * @param blendMode 混合模式
     * @param timeScale 时间缩放
     * @param mask 遮罩
     * @returns 混合层索引
     */
    addLayer(clipName: string, weight?: number, blendMode?: BlendMode, timeScale?: number, mask?: string[]): number;
    /**
     * 移除混合层
     * @param index 混合层索引
     * @returns 是否成功移除
     */
    removeLayer(index: number): boolean;
    /**
     * 设置混合层权重
     * @param index 混合层索引
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     * @returns 是否成功设置
     */
    setLayerWeight(index: number, weight: number, blendTime?: number): boolean;
    /**
     * 设置混合层时间缩放
     * @param index 混合层索引
     * @param timeScale 时间缩放
     * @returns 是否成功设置
     */
    setLayerTimeScale(index: number, timeScale: number): boolean;
    /**
     * 设置混合层混合模式
     * @param index 混合层索引
     * @param blendMode 混合模式
     * @returns 是否成功设置
     */
    setLayerBlendMode(index: number, blendMode: BlendMode): boolean;
    /**
     * 设置混合层遮罩
     * @param index 混合层索引
     * @param mask 遮罩
     * @returns 是否成功设置
     */
    setLayerMask(index: number, mask?: string[]): boolean;
    /**
     * 获取混合层
     * @param index 混合层索引
     * @returns 混合层
     */
    getLayer(index: number): BlendLayer | null;
    /**
     * 获取所有混合层
     * @returns 混合层列表
     */
    getLayers(): BlendLayer[];
    /**
     * 清空所有混合层
     */
    clearLayers(): void;
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 获取是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 设置混合曲线类型
     * @param type 混合曲线类型
     * @param customCurve 自定义混合曲线
     */
    setBlendCurveType(type: BlendCurveType, customCurve?: (t: number) => number): void;
    /**
     * 使用预设曲线
     * @param presetName 预设名称
     */
    usePresetCurve(presetName: string): void;
    /**
     * 获取混合曲线类型
     * @returns 混合曲线类型
     */
    getBlendCurveType(): BlendCurveType;
    /**
     * 设置自定义混合曲线
     * @param curve 自定义混合曲线函数
     */
    setCustomBlendCurve(curve: (t: number) => number): void;
    /**
     * 获取自定义混合曲线
     * @returns 自定义混合曲线函数
     */
    getCustomBlendCurve(): ((t: number) => number) | undefined;
    /**
     * 创建贝塞尔曲线混合函数
     * @param x1 第一个控制点的x坐标
     * @param y1 第一个控制点的y坐标
     * @param x2 第二个控制点的x坐标
     * @param y2 第二个控制点的y坐标
     * @returns 贝塞尔曲线混合函数
     */
    createBezierCurve(x1: number, y1: number, x2: number, y2: number): (t: number) => number;
    /**
     * 创建预设混合曲线
     * @param name 预设名称
     * @returns 混合曲线函数
     */
    createPresetCurve(name: string): (t: number) => number;
    /**
     * 开始混合
     * @param blendTime 混合时间（秒）
     */
    private startBlending;
    /**
     * 更新混合器
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 应用混合曲线
     * @param t 混合因子（0-1）
     * @returns 应用曲线后的混合因子
     */
    private applyBlendCurve;
    /**
     * 计算曲线值
     * @param t 混合因子（0-1）
     * @returns 曲线值
     */
    private calculateCurveValue;
    /**
     * 应用混合层
     */
    private applyLayers;
    /**
     * 应用遮罩到动作
     * @param action 动作
     * @param maskBones 遮罩骨骼列表
     */
    private applyMaskToAction;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    addEventListener(type: BlendEventType, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    removeEventListener(type: BlendEventType, callback: (data: any) => void): void;
    /**
     * 销毁混合器
     */
    dispose(): void;
    /**
     * 设置缓存配置
     * @param enabled 是否启用缓存
     * @param resolution 缓存精度
     */
    setCacheConfig(enabled: boolean, resolution?: number): void;
    /**
     * 清空缓存
     */
    clearCache(): void;
    /**
     * 设置对象池配置
     * @param enabled 是否启用对象池
     */
    setObjectPoolConfig(enabled: boolean): void;
    /**
     * 清空对象池
     */
    clearObjectPool(): void;
    /**
     * 从对象池获取对象
     * @param type 对象类型
     * @returns 对象
     */
    private getFromPool;
    /**
     * 将对象放回对象池
     * @param type 对象类型
     * @param obj 对象
     */
    private returnToPool;
    /**
     * 设置批处理配置
     * @param enabled 是否启用批处理
     */
    setBatchProcessingConfig(enabled: boolean): void;
    /**
     * 获取性能统计信息
     * @returns 性能统计信息
     */
    getPerformanceStats(): any;
    /**
     * 应用混合树
     * @param action 动作
     * @param params 混合树参数
     */
    private applyBlendTree;
    /**
     * 应用1D混合树
     * @param action 动作
     * @param params 混合树参数
     */
    private applyBlendTree1D;
    /**
     * 应用2D混合树
     * @param action 动作
     * @param params 混合树参数
     */
    private applyBlendTree2D;
    /**
     * 应用直接混合树
     * @param action 动作
     * @param params 混合树参数
     */
    private applyBlendTreeDirect;
    /**
     * 计算三角形
     * @param nodes 节点列表
     * @returns 三角形列表
     */
    private calculateTriangles;
    /**
     * 判断点是否在三角形内
     * @param px 点的x坐标
     * @param py 点的y坐标
     * @param ax 三角形顶点A的x坐标
     * @param ay 三角形顶点A的y坐标
     * @param bx 三角形顶点B的x坐标
     * @param by 三角形顶点B的y坐标
     * @param cx 三角形顶点C的x坐标
     * @param cy 三角形顶点C的y坐标
     * @returns 是否在三角形内
     */
    private isPointInTriangle;
    /**
     * 计算重心坐标
     * @param px 点的x坐标
     * @param py 点的y坐标
     * @param ax 三角形顶点A的x坐标
     * @param ay 三角形顶点A的y坐标
     * @param bx 三角形顶点B的x坐标
     * @param by 三角形顶点B的y坐标
     * @param cx 三角形顶点C的x坐标
     * @param cy 三角形顶点C的y坐标
     * @returns 重心坐标
     */
    private calculateBarycentricWeights;
    /**
     * 应用加权混合
     * @param action 动作
     * @param params 加权混合参数
     */
    private applyWeightedBlend;
    /**
     * 应用分层混合
     * @param action 动作
     * @param params 分层混合参数
     */
    private applyLayeredBlend;
    /**
     * 应用序列混合
     * @param action 动作
     * @param params 序列混合参数
     */
    private applySequentialBlend;
    /**
     * 获取动画控制器
     * @returns 动画控制器
     */
    getAnimator(): Animator;
    /**
     * 创建遮罩
     * @param name 遮罩名称
     * @param type 遮罩类型
     * @param bones 骨骼列表
     * @param weightType 权重类型
     * @returns 遮罩
     */
    createMask(name: string, bones?: string[]): AnimationMask;
    /**
     * 获取遮罩
     * @param name 遮罩名称
     * @returns 遮罩
     */
    getMask(name: string): AnimationMask | undefined;
    /**
     * 移除遮罩
     * @param name 遮罩名称
     * @returns 是否成功移除
     */
    removeMask(name: string): boolean;
    /**
     * 应用遮罩到动画片段
     * @param clipName 动画片段名称
     * @param maskName 遮罩名称
     * @returns 是否成功应用
     */
    applyMaskToClip(clipName: string, maskName: string): boolean;
    /**
     * 创建上半身遮罩
     * @returns 遮罩
     */
    createUpperBodyMask(): AnimationMask;
    /**
     * 创建下半身遮罩
     * @returns 遮罩
     */
    createLowerBodyMask(): AnimationMask;
    /**
     * 创建左手遮罩
     * @returns 遮罩
     */
    createLeftHandMask(): AnimationMask;
    /**
     * 创建右手遮罩
     * @returns 遮罩
     */
    createRightHandMask(): AnimationMask;
    /**
     * 创建子片段
     * @param name 子片段名称
     * @param sourceName 源片段名称
     * @param startTime 开始时间（秒）
     * @param endTime 结束时间（秒）
     * @param loop 是否循环
     * @returns 子片段
     */
    createSubClip(name: string, sourceName: string, startTime: number, endTime: number, loop?: boolean): SubClip;
    /**
     * 获取子片段
     * @param name 子片段名称
     * @returns 子片段
     */
    getSubClip(name: string): SubClip | undefined;
    /**
     * 移除子片段
     * @param name 子片段名称
     * @returns 是否成功移除
     */
    removeSubClip(name: string): boolean;
    /**
     * 创建动作子片段
     * @param sourceClipName 源片段名称
     * @param actionName 动作名称
     * @param startTime 开始时间（秒）
     * @param endTime 结束时间（秒）
     * @param loop 是否循环
     * @returns 子片段名称
     */
    createActionSubClip(sourceClipName: string, actionName: string, startTime: number, endTime: number, loop?: boolean): string;
    /**
     * 创建循环子片段
     * @param sourceClipName 源片段名称
     * @param loopStartTime 循环开始时间（秒）
     * @param loopEndTime 循环结束时间（秒）
     * @returns 子片段名称
     */
    createLoopSubClip(sourceClipName: string, loopStartTime: number, loopEndTime: number): string;
    /**
     * 创建入场子片段
     * @param sourceClipName 源片段名称
     * @param entryEndTime 入场结束时间（秒）
     * @returns 子片段名称
     */
    createEntrySubClip(sourceClipName: string, entryEndTime: number): string;
    /**
     * 创建退场子片段
     * @param sourceClipName 源片段名称
     * @param exitStartTime 退场开始时间（秒）
     * @returns 子片段名称
     */
    createExitSubClip(sourceClipName: string, exitStartTime: number): string;
    /**
     * 从混合空间更新混合层
     * @param blendSpaceId 混合空间ID
     * @param position 位置
     * @param nodes 节点列表
     */
    updateFromBlendSpace(blendSpaceId: string, position: number | {
        x: number;
        y: number;
    }, nodes: Array<{
        clipName: string;
        weight: number;
    }>): void;
    /**
     * 从1D混合空间更新混合层
     * @param blendSpaceId 混合空间ID
     * @param position 位置
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param clips 动画片段列表
     * @param positions 位置列表
     */
    updateFromBlendSpace1D(blendSpaceId: string, position: number, minValue: number, maxValue: number, clips: string[], positions: number[]): void;
    /**
     * 从2D混合空间更新混合层
     * @param blendSpaceId 混合空间ID
     * @param position 位置
     * @param minX 最小X值
     * @param maxX 最大X值
     * @param minY 最小Y值
     * @param maxY 最大Y值
     * @param clips 动画片段列表
     * @param positions 位置列表
     * @param useTriangulation 是否使用三角形混合
     */
    updateFromBlendSpace2D(blendSpaceId: string, position: {
        x: number;
        y: number;
    }, minX: number, maxX: number, minY: number, maxY: number, clips: string[], positions: Array<{
        x: number;
        y: number;
    }>, useTriangulation?: boolean): void;
    /**
     * 集成物理系统
     * @param entity 实体
     * @param physicsSystem 物理系统
     * @param config 配置
     * @returns 物理动画集成
     */
    integratePhysics(entity: Entity, physicsSystem: any, config?: any): PhysicsAnimationIntegration;
    /**
     * 集成输入系统
     * @param entity 实体
     * @param inputSystem 输入系统
     * @param config 配置
     * @returns 输入动画集成
     */
    integrateInput(entity: Entity, inputSystem: any, config?: any): InputAnimationIntegration;
    /**
     * 获取物理动画集成
     * @returns 物理动画集成
     */
    getPhysicsIntegration(): PhysicsAnimationIntegration | undefined;
    /**
     * 获取输入动画集成
     * @returns 输入动画集成
     */
    getInputIntegration(): InputAnimationIntegration | undefined;
    /**
     * 创建高级子片段
     * @param name 子片段名称
     * @param originalClipName 原始剪辑名称
     * @param startTime 开始时间（秒）
     * @param endTime 结束时间（秒）
     * @param loop 是否循环
     * @param reverse 是否反向播放
     * @param timeScale 播放速度
     * @returns 高级子片段
     */
    createAdvancedSubClip(name: string, originalClipName: string, startTime?: number, endTime?: number, loop?: boolean, reverse?: boolean, timeScale?: number): AnimationSubClip;
    /**
     * 获取高级子片段
     * @param name 子片段名称
     * @returns 高级子片段
     */
    getAdvancedSubClip(name: string): AnimationSubClip | undefined;
    /**
     * 移除高级子片段
     * @param name 子片段名称
     * @returns 是否成功移除
     */
    removeAdvancedSubClip(name: string): boolean;
    /**
     * 创建嵌套子片段
     * @param name 子片段名称
     * @param parentName 父子片段名称
     * @param startTime 开始时间（秒）
     * @param endTime 结束时间（秒）
     * @param loop 是否循环
     * @param reverse 是否反向播放
     * @param timeScale 播放速度
     * @returns 嵌套子片段
     */
    createNestedSubClip(name: string, parentName: string, startTime?: number, endTime?: number, loop?: boolean, reverse?: boolean, timeScale?: number): AnimationSubClip | null;
    /**
     * 混合子片段
     * @param name 混合后的子片段名称
     * @param subClipNames 子片段名称列表
     * @param weights 权重列表
     * @returns 混合后的子片段
     */
    blendSubClips(name: string, subClipNames: string[], weights?: number[]): AnimationSubClip | null;
    /**
     * 设置缓存启用状态
     */
    setCacheEnabled(enabled: boolean): void;
    /**
     * 设置对象池启用状态
     */
    setObjectPoolEnabled(enabled: boolean): void;
    /**
     * 设置批处理启用状态
     */
    setBatchProcessingEnabled(enabled: boolean): void;
    /**
     * 设置层级
     */
    setLayers(layers: any[]): void;
    /**
     * 获取遮罩列表
     */
    getMasks(): AnimationMask[];
}
