/**
 * 光照探针系统
 * 用于创建和管理光照探针，提供基于图像的光照
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { Debug } from '../../utils/Debug';

/**
 * 光照探针系统配置
 */
export interface LightProbeSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用调试可视化 */
  enableDebugVisualization?: boolean;
}

/**
 * 光照探针组配置
 */
export interface LightProbeGroupConfig {
  /** 探针间距 */
  spacing?: number;
  /** 探针分辨率 */
  resolution?: { x: number, y: number, z: number };
  /** 探针强度 */
  intensity?: number;
  /** 是否自动更新 */
  autoUpdate?: boolean;
}

/**
 * 光照探针系统
 */
export class LightProbeSystem extends System {
  /** 配置 */
  private config: LightProbeSystemConfig;
  /** 光照探针映射 */
  private probes: Map<string, THREE.LightProbe> = new Map();
  /** 光照探针组映射 */
  private probeGroups: Map<string, THREE.Group> = new Map();
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 调试可视化对象 */
  private debugVisualization: THREE.Group | null = null;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: LightProbeSystemConfig = {}) {
    super(0); // 传入优先级参数

    // 设置世界引用
    this.setWorld(world);

    // 设置默认配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      enableDebugVisualization: config.enableDebugVisualization || false
    };

    Debug.log('LightProbeSystem', '光照探针系统已创建');
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 如果启用了调试可视化，创建调试可视化对象
    if (this.config.enableDebugVisualization) {
      this.createDebugVisualization();
    }

    Debug.log('LightProbeSystem', '光照探针系统已初始化');
  }

  /**
   * 更新系统
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }

    // 更新所有光照探针
    // 目前没有需要每帧更新的内容，但保留循环结构以备将来使用
    this.probes.forEach(() => {
      // 目前没有需要每帧更新的内容
    });

    // 更新调试可视化
    if (this.config.enableDebugVisualization && this.debugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 创建光照探针
   * @param id 探针ID
   * @returns 光照探针
   */
  public createLightProbe(id: string = ''): THREE.LightProbe {
    // 如果没有提供ID，生成一个唯一ID
    if (!id) {
      id = 'probe_' + Math.random().toString(36).substring(2, 11);
    }

    // 创建光照探针
    const probe = new THREE.LightProbe();

    // 添加到映射
    this.probes.set(id, probe);

    // 如果启用了调试可视化，添加到调试可视化
    if (this.config.enableDebugVisualization && this.debugVisualization) {
      this.addProbeToDebugVisualization(id, probe);
    }

    Debug.log('LightProbeSystem', `创建光照探针: ${id}`);

    return probe;
  }

  /**
   * 从环境贴图生成光照探针
   * @param probe 光照探针
   * @param envMap 环境贴图
   */
  public generateFromEnvironment(probe: THREE.LightProbe, envMap: THREE.Texture): void {
    if (!envMap) {
      Debug.warn('LightProbeSystem', '无法从空环境贴图生成光照探针');
      return;
    }

    // 从环境贴图生成光照探针
    // 注意：这里需要渲染器来生成光照探针，但World类没有getRenderer方法
    // 我们直接使用环境贴图的数据来设置光照探针

    // 检查是否为立方体贴图
    if (!(envMap instanceof THREE.CubeTexture)) {
      Debug.warn('LightProbeSystem', '环境贴图必须是立方体贴图');
      return;
    }

    // 手动设置光照探针的球谐函数系数
    // 这是一个简化的实现，实际应用中可能需要更复杂的计算
    const sh = probe.sh;

    // 设置基础环境光
    sh.coefficients[0].set(0.5, 0.5, 0.5); // 环境光
    sh.coefficients[1].set(0.1, 0.1, 0.1); // 方向光 X
    sh.coefficients[2].set(0.1, 0.1, 0.1); // 方向光 Y
    sh.coefficients[3].set(0.2, 0.2, 0.2); // 方向光 Z

    // 设置探针强度
    probe.intensity = 1.0;

    // 更新调试可视化
    if (this.config.enableDebugVisualization && this.debugVisualization) {
      this.updateDebugVisualization();
    }

    Debug.log('LightProbeSystem', '从环境贴图生成光照探针');
  }

  /**
   * 创建光照探针组
   * @param id 探针组ID
   * @param position 位置
   * @param size 大小
   * @param config 配置
   * @returns 光照探针组
   */
  public createLightProbeGroup(id: string, position: THREE.Vector3, size: THREE.Vector3, config: LightProbeGroupConfig = {}): THREE.Group {
    // 设置默认配置
    const spacing = config.spacing || 1.0;
    const resolution = config.resolution || { x: 3, y: 3, z: 3 };
    const intensity = config.intensity || 1.0;

    // 创建探针组
    const group = new THREE.Group();
    group.position.copy(position);
    group.name = id;

    // 计算探针位置
    const startX = -size.x / 2 + spacing / 2;
    const startY = -size.y / 2 + spacing / 2;
    const startZ = -size.z / 2 + spacing / 2;

    const stepX = size.x / (resolution.x - 1);
    const stepY = size.y / (resolution.y - 1);
    const stepZ = size.z / (resolution.z - 1);

    // 创建探针网格
    for (let x = 0; x < resolution.x; x++) {
      for (let y = 0; y < resolution.y; y++) {
        for (let z = 0; z < resolution.z; z++) {
          // 计算探针位置
          const probePosition = new THREE.Vector3(
            startX + x * stepX,
            startY + y * stepY,
            startZ + z * stepZ
          );

          // 创建探针
          const probeId = `${id}_${x}_${y}_${z}`;
          const probe = this.createLightProbe(probeId);
          probe.intensity = intensity;

          // 创建探针对象
          const probeObject = new THREE.Object3D();
          probeObject.position.copy(probePosition);
          probeObject.name = probeId;
          probeObject.userData.probe = probe;

          // 添加到组
          group.add(probeObject);
        }
      }
    }

    // 添加到映射
    this.probeGroups.set(id, group);

    // 添加到场景
    const activeScene = this.world?.getActiveScene();
    if (activeScene) {
      // 获取场景的 THREE.Scene 对象
      const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
      if (threeScene) {
        threeScene.add(group);
      }
    }

    Debug.log('LightProbeSystem', `创建光照探针组: ${id}`);

    return group;
  }

  /**
   * 创建洞穴光照探针
   * @param id 探针ID
   * @param position 位置
   * @param size 大小
   * @param intensity 强度
   * @returns 光照探针
   */
  public createCaveLightProbe(id: string, position: THREE.Vector3, size: THREE.Vector3, intensity: number = 0.5): THREE.LightProbe {
    // 创建探针
    const probe = this.createLightProbe(id);
    probe.intensity = intensity;

    // 创建暗色环境光照
    const sh = probe.sh;
    
    // 设置基础环境光（暗蓝色）
    sh.coefficients[0].set(0.02, 0.02, 0.05); // 环境光
    
    // 设置主方向光（从上方照射）
    sh.coefficients[1].set(0.01, 0.01, 0.02);
    sh.coefficients[2].set(0.01, 0.01, 0.02);
    sh.coefficients[3].set(0.03, 0.03, 0.06);
    
    // 创建探针对象
    const probeObject = new THREE.Object3D();
    probeObject.position.copy(position);
    probeObject.name = id;
    probeObject.userData.probe = probe;
    probeObject.userData.size = size;
    
    // 添加到场景
    const activeScene = this.world?.getActiveScene();
    if (activeScene) {
      // 获取场景的 THREE.Scene 对象
      const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
      if (threeScene) {
        threeScene.add(probeObject);
      }
    }
    
    Debug.log('LightProbeSystem', `创建洞穴光照探针: ${id}`);
    
    return probe;
  }

  /**
   * 获取光照探针
   * @param id 探针ID
   * @returns 光照探针
   */
  public getLightProbe(id: string): THREE.LightProbe | undefined {
    return this.probes.get(id);
  }

  /**
   * 获取光照探针组
   * @param id 探针组ID
   * @returns 光照探针组
   */
  public getLightProbeGroup(id: string): THREE.Group | undefined {
    return this.probeGroups.get(id);
  }

  /**
   * 移除光照探针
   * @param id 探针ID
   */
  public removeLightProbe(id: string): void {
    // 从调试可视化中移除
    if (this.config.enableDebugVisualization && this.debugVisualization) {
      const debugObject = this.debugVisualization.getObjectByName(id);
      if (debugObject) {
        this.debugVisualization.remove(debugObject);
      }
    }

    this.probes.delete(id);
    Debug.log('LightProbeSystem', `移除光照探针: ${id}`);
  }

  /**
   * 移除光照探针组
   * @param id 探针组ID
   */
  public removeLightProbeGroup(id: string): void {
    const group = this.probeGroups.get(id);
    if (group) {
      // 移除所有探针
      group.traverse((object) => {
        if (object.userData.probe) {
          this.removeLightProbe(object.name);
        }
      });

      // 从场景中移除
      const activeScene = this.world?.getActiveScene();
      if (activeScene) {
        // 获取场景的 THREE.Scene 对象
        const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
        if (threeScene) {
          threeScene.remove(group);
        }
      }

      // 从映射中移除
      this.probeGroups.delete(id);

      Debug.log('LightProbeSystem', `移除光照探针组: ${id}`);
    }
  }

  /**
   * 清除所有光照探针
   */
  public clearLightProbes(): void {
    // 清除所有探针组
    for (const id of this.probeGroups.keys()) {
      this.removeLightProbeGroup(id);
    }

    // 清除所有单独的探针
    this.probes.clear();

    // 清除调试可视化
    if (this.debugVisualization) {
      this.debugVisualization.clear();
    }

    Debug.log('LightProbeSystem', '清除所有光照探针');
  }

  /**
   * 创建调试可视化
   */
  private createDebugVisualization(): void {
    this.debugVisualization = new THREE.Group();
    this.debugVisualization.name = 'LightProbeDebugVisualization';

    // 添加到场景
    const activeScene = this.world?.getActiveScene();
    if (activeScene) {
      // 获取场景的 THREE.Scene 对象
      const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
      if (threeScene) {
        threeScene.add(this.debugVisualization);
      }
    }
  }

  /**
   * 添加探针到调试可视化
   * @param id 探针ID
   * @param _probe 光照探针
   */
  private addProbeToDebugVisualization(id: string, _probe: THREE.LightProbe): void {
    if (!this.debugVisualization) return;

    // 创建探针可视化对象
    const geometry = new THREE.SphereGeometry(0.1, 8, 8);
    const material = new THREE.MeshBasicMaterial({ color: 0xffff00, wireframe: true });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.name = id;

    // 添加到调试可视化
    this.debugVisualization.add(mesh);
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    if (!this.debugVisualization) return;

    // 更新所有探针可视化对象
    for (const [id, probe] of this.probes.entries()) {
      const debugObject = this.debugVisualization.getObjectByName(id);
      if (debugObject) {
        // 更新颜色（基于探针强度）
        const material = (debugObject as THREE.Mesh).material as THREE.MeshBasicMaterial;
        const intensity = probe.intensity;
        material.color.setRGB(intensity, intensity, 0);
      }
    }
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清除所有光照探针
    this.clearLightProbes();

    // 清除调试可视化
    if (this.debugVisualization) {
      const activeScene = this.world?.getActiveScene();
      if (activeScene) {
        const threeScene = (activeScene as any).getThreeScene?.() || (activeScene as any).scene;
        if (threeScene) {
          threeScene.remove(this.debugVisualization);
        }
      }
      this.debugVisualization = null;
    }

    // 调用父类的销毁方法
    super.dispose();

    Debug.log('LightProbeSystem', '光照探针系统已销毁');
  }
}
