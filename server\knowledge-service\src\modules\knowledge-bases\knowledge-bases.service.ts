import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { KnowledgeBase } from '../../entities/knowledge-base.entity';
import { KnowledgeDocument } from '../../entities/knowledge-document.entity';
import { CreateKnowledgeBaseDto } from './dto/create-knowledge-base.dto';
import { UpdateKnowledgeBaseDto } from './dto/update-knowledge-base.dto';
import { QueryKnowledgeBaseDto } from './dto/query-knowledge-base.dto';
import { CacheService } from '../../cache/cache.service';
import { StorageService } from '../../storage/storage.service';

@Injectable()
export class KnowledgeBasesService {
  constructor(
    @InjectRepository(KnowledgeBase)
    private readonly knowledgeBaseRepository: Repository<KnowledgeBase>,
    @InjectRepository(KnowledgeDocument)
    private readonly documentRepository: Repository<KnowledgeDocument>,
    private readonly cacheService: CacheService,
    private readonly storageService: StorageService,
  ) {}

  async create(createKnowledgeBaseDto: CreateKnowledgeBaseDto, userId: string) {
    const knowledgeBase = this.knowledgeBaseRepository.create({
      ...createKnowledgeBaseDto,
      ownerId: userId,
    });

    const savedKnowledgeBase = await this.knowledgeBaseRepository.save(knowledgeBase);
    
    // 清除缓存
    await this.cacheService.del(`user:${userId}:knowledge-bases`);
    
    return savedKnowledgeBase;
  }

  async findAll(query: QueryKnowledgeBaseDto, userId: string) {
    const cacheKey = `user:${userId}:knowledge-bases:${JSON.stringify(query)}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const queryBuilder = this.knowledgeBaseRepository.createQueryBuilder('kb');
    
    // 只返回用户拥有的知识库
    queryBuilder.where('kb.ownerId = :userId', { userId });
    
    if (query.search) {
      queryBuilder.andWhere(
        '(kb.name LIKE :search OR kb.description LIKE :search)',
        { search: `%${query.search}%` }
      );
    }
    
    if (query.category) {
      queryBuilder.andWhere('kb.category = :category', { category: query.category });
    }
    
    if (query.language) {
      queryBuilder.andWhere('kb.language = :language', { language: query.language });
    }

    const page = query.page || 1;
    const limit = query.limit || 20;
    const skip = (page - 1) * limit;

    queryBuilder.skip(skip).take(limit);
    queryBuilder.orderBy('kb.updatedAt', 'DESC');

    const [knowledgeBases, total] = await queryBuilder.getManyAndCount();

    const result = {
      data: knowledgeBases,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };

    // 缓存结果
    await this.cacheService.set(cacheKey, result, 300); // 5分钟缓存

    return result;
  }

  async findOne(id: string, userId: string): Promise<KnowledgeBase> {
    const cacheKey = `knowledge-base:${id}:${userId}`;
    const cached = await this.cacheService.get<KnowledgeBase>(cacheKey);
    if (cached) {
      return cached;
    }

    const knowledgeBase = await this.knowledgeBaseRepository.findOne({
      where: { id, ownerId: userId },
      relations: ['documents'],
    });

    if (!knowledgeBase) {
      throw new NotFoundException('知识库不存在');
    }

    // 缓存结果
    await this.cacheService.set(cacheKey, knowledgeBase, 300);

    return knowledgeBase;
  }

  async update(id: string, updateKnowledgeBaseDto: UpdateKnowledgeBaseDto, userId: string) {
    const knowledgeBase = await this.findOne(id, userId);
    
    Object.assign(knowledgeBase, updateKnowledgeBaseDto);
    const updatedKnowledgeBase = await this.knowledgeBaseRepository.save(knowledgeBase);
    
    // 清除缓存
    await this.cacheService.del(`knowledge-base:${id}:${userId}`);
    await this.cacheService.del(`user:${userId}:knowledge-bases`);
    
    return updatedKnowledgeBase;
  }

  async remove(id: string, userId: string) {
    const knowledgeBase = await this.findOne(id, userId);
    
    // 删除相关文档
    await this.documentRepository.delete({ knowledgeBaseId: id });
    
    // 删除知识库
    await this.knowledgeBaseRepository.remove(knowledgeBase);
    
    // 清除缓存
    await this.cacheService.del(`knowledge-base:${id}:${userId}`);
    await this.cacheService.del(`user:${userId}:knowledge-bases`);
    
    return { message: '知识库删除成功' };
  }

  async getStatistics(id: string, userId: string) {
    const knowledgeBase = await this.findOne(id, userId);
    
    const documentCount = await this.documentRepository.count({
      where: { knowledgeBaseId: id },
    });
    
    const totalSize = await this.documentRepository
      .createQueryBuilder('doc')
      .select('SUM(doc.fileSize)', 'totalSize')
      .where('doc.knowledgeBaseId = :id', { id })
      .getRawOne();
    
    return {
      id: knowledgeBase.id,
      name: knowledgeBase.name,
      documentCount,
      totalSize: totalSize?.totalSize || 0,
      createdAt: knowledgeBase.createdAt,
      updatedAt: knowledgeBase.updatedAt,
    };
  }
}
