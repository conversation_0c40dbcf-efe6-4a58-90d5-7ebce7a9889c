/**
 * 增强资源管理系统
 * 整合资源加载、缓存、依赖和预加载功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetType } from './ResourceManager';
import { EnhancedResourceManagerOptions } from './EnhancedResourceManager';
import { DependencyType, EnhancedResourceDependencyManagerOptions } from './EnhancedResourceDependencyManager';
import { EnhancedResourcePreloaderOptions, PreloadGroupInfo, PreloadProgressInfo } from './EnhancedResourcePreloader';
/**
 * 增强资源管理系统选项
 */
export interface EnhancedResourceSystemOptions {
    /** 资源管理器选项 */
    resourceManagerOptions?: EnhancedResourceManagerOptions;
    /** 依赖管理器选项 */
    dependencyManagerOptions?: EnhancedResourceDependencyManagerOptions;
    /** 预加载器选项 */
    preloaderOptions?: Omit<EnhancedResourcePreloaderOptions, 'resourceManager' | 'dependencyManager'>;
    /** 是否启用调试模式 */
    debug?: boolean;
}
/**
 * 增强资源管理系统
 */
export declare class EnhancedResourceSystem extends EventEmitter {
    /** 资源管理器 */
    private resourceManager;
    /** 依赖管理器 */
    private dependencyManager;
    /** 预加载器 */
    private preloader;
    /** 是否启用调试模式 */
    private debug;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建增强资源管理系统实例
     * @param options 资源管理系统选项
     */
    constructor(options?: EnhancedResourceSystemOptions);
    /**
     * 转发子组件事件
     */
    private forwardEvents;
    /**
     * 初始化资源管理系统
     */
    initialize(): void;
    /**
     * 加载资源
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @param priority 资源优先级
     * @returns Promise，解析为资源数据
     */
    load(id: string, type: AssetType, url: string, priority?: number): Promise<any>;
    /**
     * 释放资源
     * @param id 资源ID
     * @returns 是否成功释放
     */
    release(id: string): boolean;
    /**
     * 获取资源数据
     * @param id 资源ID
     * @returns 资源数据
     */
    getResource(id: string): any;
    /**
     * 添加依赖关系
     * @param resourceId 资源ID
     * @param dependencyId 依赖资源ID
     * @param type 依赖类型
     * @param priority 依赖优先级
     * @returns 是否成功添加
     */
    addDependency(resourceId: string, dependencyId: string, type?: DependencyType, priority?: number): boolean;
    /**
     * 移除依赖关系
     * @param resourceId 资源ID
     * @param dependencyId 依赖资源ID
     * @returns 是否成功移除
     */
    removeDependency(resourceId: string, dependencyId: string): boolean;
    /**
     * 获取资源的依赖
     * @param resourceId 资源ID
     * @returns 依赖信息数组
     */
    getDependencies(resourceId: string): any[];
    /**
     * 添加预加载组
     * @param group 预加载组信息
     * @returns 是否成功添加
     */
    addPreloadGroup(group: PreloadGroupInfo): boolean;
    /**
     * 移除预加载组
     * @param name 组名
     * @returns 是否成功移除
     */
    removePreloadGroup(name: string): boolean;
    /**
     * 加载预加载组
     * @param name 组名
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    loadPreloadGroup(name: string, onProgress?: (progress: PreloadProgressInfo) => void): Promise<PreloadProgressInfo>;
    /**
     * 暂停预加载
     * @returns 是否成功暂停
     */
    pausePreload(): boolean;
    /**
     * 恢复预加载
     * @returns 是否成功恢复
     */
    resumePreload(): boolean;
    /**
     * 取消预加载
     * @returns 是否成功取消
     */
    cancelPreload(): boolean;
    /**
     * 获取预加载组
     * @param name 组名
     * @returns 预加载组信息
     */
    getPreloadGroup(name: string): PreloadGroupInfo | null;
    /**
     * 获取预加载进度
     * @param name 组名
     * @returns 预加载进度信息
     */
    getPreloadProgress(name: string): PreloadProgressInfo | null;
    /**
     * 清理缓存
     */
    cleanupCache(): void;
    /**
     * 清空缓存
     */
    clearCache(): void;
    /**
     * 优化依赖关系
     * @returns 是否成功优化
     */
    optimizeDependencies(): boolean;
    /**
     * 清除所有依赖关系
     */
    clearDependencies(): void;
    /**
     * 清除所有预加载组
     */
    clearPreloadGroups(): void;
    /**
     * 获取缓存统计信息
     * @returns 缓存统计信息
     */
    getCacheStats(): any;
    /**
     * 销毁资源管理系统
     */
    dispose(): void;
}
