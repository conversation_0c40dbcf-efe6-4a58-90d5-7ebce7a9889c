import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as Minio from 'minio';
import { StorageService } from './storage.service';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'MINIO_CLIENT',
      useFactory: (configService: ConfigService): Minio.Client => {
        try {
          const endpoint = configService.get('MINIO_ENDPOINT', 'localhost:9000');
          const accessKey = configService.get('MINIO_ACCESS_KEY', 'minioadmin');
          const secretKey = configService.get('MINIO_SECRET_KEY', 'minioadmin123');

          // 解析 endpoint，分离主机名和端口
          const cleanEndpoint = endpoint.replace(/^https?:\/\//, '');
          const [host, portStr] = cleanEndpoint.split(':');
          const port = portStr ? parseInt(portStr) : 9000;

          return new Minio.Client({
            endPoint: host,
            port: port,
            useSSL: false,
            accessKey: accessKey,
            secretKey: secretKey,
          });
        } catch (error) {
          console.warn('MinIO connection failed, using mock client:', error.message);
          // 返回一个模拟的 MinIO 客户端
          return {
            bucketExists: async () => false,
            makeBucket: async () => {},
            putObject: async () => ({ etag: 'mock-etag' }),
            getObject: async () => null,
            removeObject: async () => {},
            listObjects: () => ({ on: () => {}, pipe: () => {} }),
          } as any;
        }
      },
      inject: [ConfigService],
    },
    StorageService,
  ],
  exports: ['MINIO_CLIENT', StorageService],
})
export class StorageModule {}
