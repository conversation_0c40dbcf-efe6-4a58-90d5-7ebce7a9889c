import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { WebRTCService } from './webrtc.service';

// 临时类型定义，替代 mediasoup
interface DataProducer {
  id: string;
  closed: boolean;
  close(): void;
  send(data: Buffer): void;
  on(event: string, listener: (...args: any[]) => void): void;
}

interface DataConsumer {
  id: string;
  closed: boolean;
  dataProducerId: string;
  label: string;
  protocol: string;
  close(): void;
  on(event: string, listener: (...args: any[]) => void): void;
}

/**
 * WebRTC数据通道服务
 * 负责管理WebRTC数据通道的创建、连接和消息传递
 */
@Injectable()
export class DataChannelService {
  private readonly logger = new Logger(DataChannelService.name);
  private dataProducers: Map<string, DataProducer> = new Map();
  private dataConsumers: Map<string, DataConsumer> = new Map();
  private channelStats: Map<string, {
    messagesReceived: number;
    messagesSent: number;
    bytesReceived: number;
    bytesSent: number;
    lastActivity: Date;
  }> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly webrtcService: WebRTCService,
  ) {}

  /**
   * 创建数据生产者
   * @param transportId 传输ID
   * @param options 选项
   * @returns 数据生产者
   */
  async createDataProducer(
    transportId: string,
    options: {
      label?: string;
      protocol?: string;
      appData?: any;
    } = {},
  ): Promise<DataProducer | null> {
    try {
      const transport = this.webrtcService.getTransport(transportId);
      
      if (!transport) {
        this.logger.error(`创建数据生产者失败: 找不到传输 ${transportId}`);
        return null;
      }
      
      const dataProducer = await transport.produceData({
        label: options.label || '',
        protocol: options.protocol || '',
        appData: options.appData || {},
      });
      
      // 存储数据生产者
      this.dataProducers.set(dataProducer.id, dataProducer);
      
      // 初始化统计信息
      this.channelStats.set(dataProducer.id, {
        messagesReceived: 0,
        messagesSent: 0,
        bytesReceived: 0,
        bytesSent: 0,
        lastActivity: new Date(),
      });
      
      // 设置事件监听器
      this.setupDataProducerListeners(dataProducer);
      
      this.logger.log(`已创建数据生产者: ${dataProducer.id}, 标签: ${options.label || '无标签'}`);
      
      // 触发事件
      this.eventEmitter.emit('webrtc.dataProducer.created', {
        dataProducerId: dataProducer.id,
        transportId,
        label: options.label,
      });
      
      return dataProducer;
    } catch (error) {
      this.logger.error(`创建数据生产者失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 创建数据消费者
   * @param transportId 传输ID
   * @param dataProducerId 数据生产者ID
   * @param options 选项
   * @returns 数据消费者
   */
  async createDataConsumer(
    transportId: string,
    dataProducerId: string,
    options: {
      appData?: any;
    } = {},
  ): Promise<DataConsumer | null> {
    try {
      const transport = this.webrtcService.getTransport(transportId);
      
      if (!transport) {
        this.logger.error(`创建数据消费者失败: 找不到传输 ${transportId}`);
        return null;
      }
      
      const dataProducer = this.dataProducers.get(dataProducerId);
      
      if (!dataProducer) {
        this.logger.error(`创建数据消费者失败: 找不到数据生产者 ${dataProducerId}`);
        return null;
      }
      
      const dataConsumer = await transport.consumeData({
        dataProducerId,
        appData: options.appData || {},
      });
      
      // 存储数据消费者
      this.dataConsumers.set(dataConsumer.id, dataConsumer);
      
      // 初始化统计信息
      this.channelStats.set(dataConsumer.id, {
        messagesReceived: 0,
        messagesSent: 0,
        bytesReceived: 0,
        bytesSent: 0,
        lastActivity: new Date(),
      });
      
      // 设置事件监听器
      this.setupDataConsumerListeners(dataConsumer);
      
      this.logger.log(`已创建数据消费者: ${dataConsumer.id}, 生产者: ${dataProducerId}`);
      
      // 触发事件
      this.eventEmitter.emit('webrtc.dataConsumer.created', {
        dataConsumerId: dataConsumer.id,
        dataProducerId,
        transportId,
      });
      
      return dataConsumer;
    } catch (error) {
      this.logger.error(`创建数据消费者失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 设置数据生产者事件监听器
   * @param dataProducer 数据生产者
   */
  private setupDataProducerListeners(dataProducer: DataProducer): void {
    dataProducer.on('transportclose', () => {
      this.logger.log(`数据生产者传输关闭: ${dataProducer.id}`);
      this.dataProducers.delete(dataProducer.id);
      this.channelStats.delete(dataProducer.id);
      
      // 触发事件
      this.eventEmitter.emit('webrtc.dataProducer.closed', {
        dataProducerId: dataProducer.id,
      });
    });
    
    dataProducer.on('close', () => {
      this.logger.log(`数据生产者关闭: ${dataProducer.id}`);
      this.dataProducers.delete(dataProducer.id);
      this.channelStats.delete(dataProducer.id);
      
      // 触发事件
      this.eventEmitter.emit('webrtc.dataProducer.closed', {
        dataProducerId: dataProducer.id,
      });
    });
  }

  /**
   * 设置数据消费者事件监听器
   * @param dataConsumer 数据消费者
   */
  private setupDataConsumerListeners(dataConsumer: DataConsumer): void {
    dataConsumer.on('transportclose', () => {
      this.logger.log(`数据消费者传输关闭: ${dataConsumer.id}`);
      this.dataConsumers.delete(dataConsumer.id);
      this.channelStats.delete(dataConsumer.id);
      
      // 触发事件
      this.eventEmitter.emit('webrtc.dataConsumer.closed', {
        dataConsumerId: dataConsumer.id,
      });
    });
    
    dataConsumer.on('close', () => {
      this.logger.log(`数据消费者关闭: ${dataConsumer.id}`);
      this.dataConsumers.delete(dataConsumer.id);
      this.channelStats.delete(dataConsumer.id);
      
      // 触发事件
      this.eventEmitter.emit('webrtc.dataConsumer.closed', {
        dataConsumerId: dataConsumer.id,
      });
    });
    
    dataConsumer.on('message', (message) => {
      // 更新统计信息
      const stats = this.channelStats.get(dataConsumer.id);
      if (stats) {
        stats.messagesReceived++;
        stats.bytesReceived += message.byteLength;
        stats.lastActivity = new Date();
        this.channelStats.set(dataConsumer.id, stats);
      }
      
      // 触发事件
      this.eventEmitter.emit('webrtc.dataConsumer.message', {
        dataConsumerId: dataConsumer.id,
        message,
      });
    });
  }

  /**
   * 发送消息到数据生产者
   * @param dataProducerId 数据生产者ID
   * @param message 消息
   * @returns 是否成功
   */
  async sendMessageToDataProducer(dataProducerId: string, message: any): Promise<boolean> {
    const dataProducer = this.dataProducers.get(dataProducerId);
    
    if (!dataProducer) {
      this.logger.error(`发送消息失败: 找不到数据生产者 ${dataProducerId}`);
      return false;
    }
    
    try {
      // 将消息转换为Buffer
      const buffer = Buffer.from(JSON.stringify(message));
      
      // 发送消息
      dataProducer.send(buffer);
      
      // 更新统计信息
      const stats = this.channelStats.get(dataProducerId);
      if (stats) {
        stats.messagesSent++;
        stats.bytesSent += buffer.byteLength;
        stats.lastActivity = new Date();
        this.channelStats.set(dataProducerId, stats);
      }
      
      return true;
    } catch (error) {
      this.logger.error(`发送消息失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 获取数据通道统计信息
   * @returns 统计信息
   */
  getChannelStats(): any {
    const stats: any = {
      dataProducers: this.dataProducers.size,
      dataConsumers: this.dataConsumers.size,
      channels: [],
    };
    
    // 添加每个通道的统计信息
    for (const [id, channelStat] of this.channelStats.entries()) {
      const isProducer = this.dataProducers.has(id);
      const isConsumer = this.dataConsumers.has(id);
      
      stats.channels.push({
        id,
        type: isProducer ? 'producer' : (isConsumer ? 'consumer' : 'unknown'),
        messagesReceived: channelStat.messagesReceived,
        messagesSent: channelStat.messagesSent,
        bytesReceived: channelStat.bytesReceived,
        bytesSent: channelStat.bytesSent,
        lastActivity: channelStat.lastActivity,
      });
    }
    
    return stats;
  }
}
