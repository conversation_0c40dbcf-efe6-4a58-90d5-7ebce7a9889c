/**
 * 增强的LOD生成器
 * 用于高质量、高效率地生成不同细节级别的模型
 */
import * as THREE from 'three';
import { LODLevel } from './LODComponent';
import { LODGenerator, LODGeneratorOptions, LODGeneratorResult } from './LODGenerator';
/**
 * 增强的LOD生成器配置接口
 */
export interface EnhancedLODGeneratorOptions extends LODGeneratorOptions {
    /** 使用的简化算法 */
    algorithm?: SimplificationAlgorithm;
    /** 是否使用GPU加速 */
    useGPU?: boolean;
    /** 是否保留重要特征 */
    preserveFeatures?: boolean;
    /** 是否使用渐进式网格 */
    useProgressiveMesh?: boolean;
    /** 是否自适应简化 */
    adaptiveSimplification?: boolean;
    /** 是否使用纹理LOD */
    useTextureLOD?: boolean;
}
/**
 * 简化算法枚举
 */
export declare enum SimplificationAlgorithm {
    /** 简单抽取 */
    SIMPLE_DECIMATION = "simple_decimation",
    /** 四边形边缘折叠 */
    QUADRIC_EDGE_COLLAPSE = "quadric_edge_collapse",
    /** 渐进式网格 */
    PROGRESSIVE_MESH = "progressive_mesh",
    /** 体素化 */
    VOXELIZATION = "voxelization"
}
/**
 * 增强的LOD生成结果接口
 */
export interface EnhancedLODGeneratorResult extends LODGeneratorResult {
    /** 渐进式网格 */
    progressiveMesh?: THREE.Mesh;
    /** 纹理LOD */
    textureLODs?: Map<LODLevel, THREE.Texture>;
    /** 简化统计信息 */
    stats: {
        /** 原始顶点数 */
        originalVertexCount: number;
        /** 简化后顶点数 */
        simplifiedVertexCounts: Map<LODLevel, number>;
        /** 简化时间（毫秒） */
        simplificationTime: number;
        /** 内存使用（字节） */
        memoryUsage: number;
    };
}
/**
 * 增强的LOD生成器类
 */
export declare class EnhancedLODGenerator extends LODGenerator {
    /** 使用的简化算法 */
    private algorithm;
    /** 是否使用GPU加速 */
    private useGPU;
    /** 是否保留重要特征 */
    private preserveFeatures;
    /** 是否使用渐进式网格 */
    private useProgressiveMesh;
    /** 是否自适应简化 */
    private adaptiveSimplification;
    /** 是否使用纹理LOD */
    private useTextureLOD;
    /** 是否支持GPU加速 */
    private supportsGPU;
    /** WebGL渲染器 */
    private renderer;
    /**
     * 创建增强的LOD生成器
     * @param options 增强的LOD生成器配置
     */
    constructor(options?: EnhancedLODGeneratorOptions);
    /**
     * 检查是否支持GPU加速
     * @returns 是否支持GPU加速
     */
    private checkGPUSupport;
    /**
     * 初始化GPU
     */
    private initializeGPU;
    /**
     * 生成增强的LOD
     * @param mesh 原始网格
     * @returns 增强的LOD生成结果
     */
    generateEnhanced(mesh: THREE.Mesh): EnhancedLODGeneratorResult;
    /**
     * 使用四边形边缘折叠算法生成LOD
     * @param mesh 原始网格
     * @param result LOD生成结果
     */
    private generateWithQuadricEdgeCollapse;
    /**
     * 使用四边形边缘折叠算法生成指定级别的LOD
     * @param mesh 原始网格
     * @param ratio 简化比例
     * @returns 简化后的网格
     */
    private generateLevelWithQuadricEdgeCollapse;
    /**
     * 使用渐进式网格算法生成LOD
     * @param mesh 原始网格
     * @param result LOD生成结果
     */
    private generateWithProgressiveMesh;
    /**
     * 创建渐进式网格
     * @param mesh 原始网格
     * @returns 渐进式网格
     */
    private createProgressiveMesh;
    /**
     * 从渐进式网格中提取指定级别的LOD
     * @param progressiveMesh 渐进式网格
     * @param ratio 简化比例
     * @returns 简化后的网格
     */
    private extractLevelFromProgressiveMesh;
    /**
     * 使用体素化算法生成LOD
     * @param mesh 原始网格
     * @param result LOD生成结果
     */
    private generateWithVoxelization;
    /**
     * 使用体素化算法生成指定级别的LOD
     * @param mesh 原始网格
     * @param ratio 简化比例
     * @returns 简化后的网格
     */
    private generateLevelWithVoxelization;
    /**
     * 使用简单抽取算法生成LOD
     * @param mesh 原始网格
     * @param result LOD生成结果
     */
    private generateWithSimpleDecimation;
    /**
     * 生成纹理LOD
     * @param mesh 原始网格
     * @returns 纹理LOD映射
     */
    private generateTextureLODs;
    /**
     * 生成纹理LOD
     * @param texture 原始纹理
     * @param ratio 简化比例
     * @returns 简化后的纹理
     */
    private generateTextureLOD;
    /**
     * 计算内存使用
     * @param result LOD生成结果
     * @returns 内存使用（字节）
     */
    private calculateMemoryUsage;
    /**
     * 计算几何体内存使用
     * @param geometry 几何体
     * @returns 内存使用（字节）
     */
    private calculateGeometryMemoryUsage;
    /**
     * 计算纹理内存使用
     * @param texture 纹理
     * @returns 内存使用（字节）
     */
    private calculateTextureMemoryUsage;
    /**
     * 内部几何体简化方法
     * @param geometry 原始几何体
     * @param ratio 简化比例
     * @returns 简化后的几何体
     */
    private simplifyGeometryInternal;
    /**
     * 自适应生成LOD
     * @param mesh 原始网格
     * @returns LOD生成结果
     */
    generateAdaptive(mesh: THREE.Mesh): EnhancedLODGeneratorResult;
    /**
     * 分析模型复杂度
     * @param mesh 网格
     * @returns 复杂度评分（0-1）
     */
    private analyzeModelComplexity;
    /**
     * 根据复杂度选择算法
     * @param complexity 复杂度评分（0-1）
     * @returns 简化算法
     */
    private selectAlgorithmByComplexity;
}
