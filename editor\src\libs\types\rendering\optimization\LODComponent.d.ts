/**
 * LOD（细节层次）组件
 * 用于为实体添加多个细节级别
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
/**
 * LOD级别枚举
 */
export declare enum LODLevel {
    /** 高细节 */
    HIGH = "high",
    /** 中细节 */
    MEDIUM = "medium",
    /** 低细节 */
    LOW = "low",
    /** 极低细节 */
    VERY_LOW = "very_low"
}
/**
 * LOD级别配置接口
 */
export interface LODLevelConfig {
    /** 级别 */
    level: LODLevel;
    /** 距离阈值 */
    distance: number;
    /** 原始距离阈值（用于动态LOD系统） */
    originalDistance?: number;
    /** 网格 */
    mesh: THREE.Mesh | THREE.Group;
    /** 是否可见 */
    visible?: boolean;
}
/**
 * LOD组件选项接口
 */
export interface LODComponentOptions {
    /** 级别配置列表 */
    levels?: LODLevelConfig[];
    /** 包围半径 */
    boundingRadius?: number;
    /** 是否自动计算包围半径 */
    autoComputeBoundingRadius?: boolean;
    /** 是否自动排序级别 */
    autoSortLevels?: boolean;
}
/**
 * LOD组件类
 */
export declare class LODComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 级别配置列表 */
    private levels;
    /** 当前级别 */
    private currentLevel;
    /** 包围半径 */
    private boundingRadius;
    /** 是否自动计算包围半径 */
    private autoComputeBoundingRadius;
    /** 是否自动排序级别 */
    private autoSortLevels;
    /**
     * 创建LOD组件
     * @param options LOD组件选项
     */
    constructor(options?: LODComponentOptions);
    /**
     * 当组件附加到实体时调用
     */
    protected onAttach(): void;
    /**
     * 当组件从实体分离时调用
     */
    protected onDetach(): void;
    /**
     * 添加级别
     * @param level 级别配置
     */
    addLevel(level: LODLevelConfig): void;
    /**
     * 移除级别
     * @param level 级别
     */
    removeLevel(level: LODLevel): void;
    /**
     * 获取级别
     * @param level 级别
     * @returns 级别配置
     */
    getLevel(level: LODLevel): LODLevelConfig | null;
    /**
     * 获取所有级别
     * @returns 级别配置列表
     */
    getLevels(): LODLevelConfig[];
    /**
     * 设置当前级别
     * @param level 级别
     */
    setCurrentLevel(level: LODLevel | null): void;
    /**
     * 获取当前级别
     * @returns 当前级别
     */
    getCurrentLevel(): LODLevel | null;
    /**
     * 设置包围半径
     * @param radius 半径
     */
    setBoundingRadius(radius: number): void;
    /**
     * 获取包围半径
     * @returns 包围半径
     */
    getBoundingRadius(): number;
    /**
     * 计算包围半径
     */
    computeBoundingRadius(): void;
    /**
     * 更新级别可见性
     */
    private updateLevelsVisibility;
    /**
     * 设置所有级别的可见性
     * @param visible 是否可见
     */
    setAllLevelsVisible(visible: boolean): void;
    /**
     * 按距离排序级别
     */
    private sortLevels;
    /**
     * 设置是否自动计算包围半径
     * @param auto 是否自动计算
     */
    setAutoComputeBoundingRadius(auto: boolean): void;
    /**
     * 获取是否自动计算包围半径
     * @returns 是否自动计算
     */
    isAutoComputeBoundingRadius(): boolean;
    /**
     * 设置是否自动排序级别
     * @param auto 是否自动排序
     */
    setAutoSortLevels(auto: boolean): void;
    /**
     * 获取是否自动排序级别
     * @returns 是否自动排序
     */
    isAutoSortLevels(): boolean;
}
