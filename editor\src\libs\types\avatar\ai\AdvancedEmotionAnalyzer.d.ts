/**
 * 高级情感分析器
 * 用于分析文本中的复杂情感和情感变化
 */
import { AIModelType } from '../../ai/AIModelType';
/**
 * 情感分析请求
 */
export interface EmotionAnalysisRequest {
    /** 文本 */
    text: string;
    /** 是否包含次要情感 */
    includeSecondary?: boolean;
    /** 是否包含情感变化 */
    includeChanges?: boolean;
    /** 详细程度 */
    detail?: 'low' | 'medium' | 'high';
    /** 情感类别 */
    emotionCategories?: string[];
    /** 是否使用上下文 */
    useContext?: boolean;
    /** 上下文文本 */
    contextText?: string;
    /** 语言 */
    language?: string;
    /** 自定义选项 */
    customOptions?: Record<string, any>;
}
/**
 * 情感分析结果
 */
export interface EmotionAnalysisResult {
    /** 主要情感 */
    primaryEmotion: string;
    /** 主要情感强度 */
    primaryIntensity: number;
    /** 次要情感 */
    secondaryEmotion?: string;
    /** 次要情感强度 */
    secondaryIntensity?: number;
    /** 情感分数 */
    scores: Record<string, number>;
    /** 情感变化 */
    emotionChanges?: EmotionChangeResult;
    /** 详细情感 */
    detailedEmotions?: DetailedEmotionResult;
    /** 分析时间（毫秒） */
    analysisTime?: number;
    /** 置信度 */
    confidence?: number;
}
/**
 * 情感变化结果
 */
export interface EmotionChangeResult {
    /** 情感转换 */
    transitions: EmotionTransition[];
    /** 情感时间线 */
    timeline: EmotionTimePoint[];
}
/**
 * 情感转换
 */
export interface EmotionTransition {
    /** 起始情感 */
    from: string;
    /** 目标情感 */
    to: string;
    /** 概率 */
    probability: number;
}
/**
 * 情感时间点
 */
export interface EmotionTimePoint {
    /** 时间（0-1） */
    time: number;
    /** 情感 */
    emotion: string;
    /** 强度 */
    intensity: number;
}
/**
 * 详细情感结果
 */
export interface DetailedEmotionResult {
    /** 主要情感 */
    primary: {
        /** 类别 */
        category: string;
        /** 子类别 */
        subcategories: Record<string, string[]>;
    };
    /** 强度 */
    intensity: {
        /** 整体强度 */
        overall: number;
        /** 唤醒度 */
        arousal: number;
        /** 效价 */
        valence: number;
    };
    /** 置信度 */
    confidence: number;
}
/**
 * 高级情感分析器配置
 */
export interface AdvancedEmotionAnalyzerConfig {
    /** 模型类型 */
    modelType?: AIModelType;
    /** 模型变体 */
    modelVariant?: string;
    /** 是否使用本地模型 */
    useLocalModel?: boolean;
    /** 模型路径 */
    modelPath?: string;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 是否使用量化模型 */
    useQuantized?: boolean;
    /** 量化位数 */
    quantizationBits?: 8 | 16 | 32;
    /** 批处理大小 */
    batchSize?: number;
    /** 情感类别 */
    emotionCategories?: string[];
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存大小 */
    cacheSize?: number;
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用批处理 */
    useBatchProcessing?: boolean;
    /** 最大等待时间（毫秒） */
    maxWaitTime?: number;
    /** 是否使用上下文 */
    useContext?: boolean;
    /** 上下文窗口大小 */
    contextWindowSize?: number;
}
/**
 * 高级情感分析器
 */
export declare class AdvancedEmotionAnalyzer {
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 模型工厂 */
    private modelFactory;
    /** 模型 */
    private model;
    /** 是否已初始化 */
    private initialized;
    /** 是否正在初始化 */
    private initializing;
    /** 缓存 */
    private cache;
    /** 批处理器 */
    private batchProcessor;
    /** 事件发射器 */
    private eventEmitter;
    /** 上下文历史 */
    private contextHistory;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: AdvancedEmotionAnalyzerConfig);
    /**
     * 初始化
     * @returns 是否成功初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 分析情感
     * @param request 请求
     * @returns 分析结果
     */
    analyzeEmotion(request: EmotionAnalysisRequest): Promise<EmotionAnalysisResult>;
    /**
     * 处理请求
     * @param request 请求
     * @returns 分析结果
     */
    private processRequest;
    /**
     * 处理批次
     * @param requests 请求批次
     * @returns 分析结果批次
     */
    private processBatch;
    /**
     * 处理模型结果
     * @param modelResult 模型结果
     * @param request 请求
     * @returns 分析结果
     */
    private processModelResult;
    /**
     * 生成情感变化
     * @param result 基本结果
     * @returns 情感变化结果
     */
    private generateEmotionChanges;
    /**
     * 生成详细情感
     * @param result 基本结果
     * @returns 详细情感结果
     */
    private generateDetailedEmotions;
    /**
     * 计算唤醒度
     * @param result 分析结果
     * @returns 唤醒度
     */
    private calculateArousal;
    /**
     * 计算效价
     * @param result 分析结果
     * @returns 效价
     */
    private calculateValence;
    /**
     * 创建默认结果
     * @returns 默认结果
     */
    private createDefaultResult;
    /**
     * 规范化文本
     * @param text 文本
     * @returns 规范化后的文本
     */
    private normalizeText;
    /**
     * 生成缓存键
     * @param request 请求
     * @returns 缓存键
     */
    private generateCacheKey;
    /**
     * 更新上下文历史
     * @param text 文本
     */
    private updateContextHistory;
    /**
     * 添加上下文到文本
     * @param text 文本
     * @param contextText 额外上下文文本
     * @returns 带上下文的文本
     */
    private addContextToText;
    /**
     * 清空上下文历史
     */
    clearContextHistory(): void;
    /**
     * 获取上下文历史
     * @returns 上下文历史
     */
    getContextHistory(): string[];
    /**
     * 销毁
     */
    dispose(): void;
}
