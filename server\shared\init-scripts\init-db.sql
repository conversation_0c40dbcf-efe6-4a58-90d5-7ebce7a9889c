-- 创建数据库
CREATE DATABASE IF NOT EXISTS ir_engine_registry;
CREATE DATABASE IF NOT EXISTS ir_engine_users;
CREATE DATABASE IF NOT EXISTS ir_engine_projects;
CREATE DATABASE IF NOT EXISTS ir_engine_assets;
CREATE DATABASE IF NOT EXISTS ir_engine_render;

-- 使用用户数据库
USE ir_engine_users;

-- 创建管理员用户
INSERT INTO users (id, username, email, password, displayName, isVerified, role, createdAt, updatedAt)
VALUES (
  UUID(),
  'admin',
  '<EMAIL>',
  '$2b$10$3euPcmQFCiblsZeEu5s7p.9MbGcD3wlf2Op6YO8yvbfEE9eaYasXi', -- 密码: admin123
  '管理员',
  1,
  'admin',
  NOW(),
  NOW()
);
