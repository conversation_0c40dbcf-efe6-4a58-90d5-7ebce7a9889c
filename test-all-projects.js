#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 开始测试所有项目...\n');

const projects = [
  { name: 'Editor', path: './editor' },
  { name: 'Engine', path: './engine' },
  { name: 'Game Server', path: './server/game-server' },
  { name: 'Shared', path: './server/shared' },
  { name: 'API Gateway', path: './server/api-gateway' },
  { name: 'User Service', path: './server/user-service' },
  { name: 'Project Service', path: './server/project-service' },
  { name: 'Asset Service', path: './server/asset-service' },
  { name: 'Collaboration Service', path: './server/collaboration-service' },
  { name: 'Render Service', path: './server/render-service' },
  { name: 'Service Registry', path: './server/service-registry' },
];

let totalTests = 0;
let passedTests = 0;
let failedTests = 0;

function testProject(project) {
  console.log(`📦 测试 ${project.name}...`);
  
  const projectPath = path.resolve(project.path);
  
  if (!fs.existsSync(projectPath)) {
    console.log(`   ❌ 项目目录不存在: ${projectPath}`);
    failedTests++;
    return;
  }
  
  const packageJsonPath = path.join(projectPath, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.log(`   ❌ package.json 不存在`);
    failedTests++;
    return;
  }
  
  try {
    // 检查 TypeScript 编译
    console.log(`   🔍 检查 TypeScript 编译...`);
    execSync('npx tsc --noEmit', { 
      cwd: projectPath, 
      stdio: 'pipe',
      timeout: 60000 
    });
    console.log(`   ✅ TypeScript 编译检查通过`);
    
    // 尝试构建
    console.log(`   🔨 尝试构建...`);
    execSync('npm run build', { 
      cwd: projectPath, 
      stdio: 'pipe',
      timeout: 120000 
    });
    console.log(`   ✅ 构建成功`);
    
    passedTests++;
    console.log(`   🎉 ${project.name} 测试通过!\n`);
    
  } catch (error) {
    console.log(`   ❌ ${project.name} 测试失败:`);
    console.log(`   错误: ${error.message}`);
    failedTests++;
    console.log('');
  }
  
  totalTests++;
}

// 测试所有项目
projects.forEach(testProject);

// 输出总结
console.log('📊 测试总结:');
console.log(`   总项目数: ${totalTests}`);
console.log(`   通过: ${passedTests}`);
console.log(`   失败: ${failedTests}`);
console.log(`   成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (failedTests === 0) {
  console.log('\n🎉 所有项目测试通过！');
  process.exit(0);
} else {
  console.log('\n⚠️  有项目测试失败，请检查上述错误信息。');
  process.exit(1);
}
