/**
 * 季节性植物类
 * 用于实现季节性植物类型
 */
import * as THREE from 'three';
import { VegetationType, VegetationGrowthStage, SeasonType } from '../ecosystem/EcosystemSimulationSystem';
import { VegetationInstanceData } from '../growth/VegetationGrowthSystem';

/**
 * 季节性植物类型
 */
export enum SeasonalVegetationType {
  /** 春季花卉 */
  SPRING_FLOWER = 'spring_flower',
  /** 夏季花卉 */
  SUMMER_FLOWER = 'summer_flower',
  /** 秋季花卉 */
  AUTUMN_FLOWER = 'autumn_flower',
  /** 冬季花卉 */
  WINTER_FLOWER = 'winter_flower',
  /** 落叶乔木 */
  DECIDUOUS_TREE = 'deciduous_tree',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 季节性植物配置
 */
export interface SeasonalVegetationConfig {
  /** 类型 */
  type: SeasonalVegetationType;
  /** 最大高度 */
  maxHeight?: number;
  /** 最大宽度 */
  maxWidth?: number;
  /** 最大寿命（天） */
  maxAge?: number;
  /** 生长速度 */
  growthRate?: number;
  /** 活跃季节 */
  activeSeasons?: SeasonType[];
  /** 休眠季节 */
  dormantSeasons?: SeasonType[];
  /** 开花季节 */
  floweringSeason?: SeasonType[];
  /** 结果季节 */
  fruitingSeason?: SeasonType[];
  /** 落叶季节 */
  leafFallSeason?: SeasonType[];
  /** 是否常绿 */
  isEvergreen?: boolean;
  /** 温度范围 */
  temperatureRange?: {
    min: number;
    max: number;
  };
  /** 光照要求 */
  lightRequirement?: number;
  /** 水分要求 */
  moistureRequirement?: number;
  /** 营养要求 */
  nutrientRequirement?: number;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 季节性植物类
 */
export class SeasonalVegetation {
  /** 配置 */
  private config: SeasonalVegetationConfig;

  /** 默认配置映射 */
  private static readonly DEFAULT_CONFIGS: { [key in SeasonalVegetationType]: SeasonalVegetationConfig } = {
    [SeasonalVegetationType.SPRING_FLOWER]: {
      type: SeasonalVegetationType.SPRING_FLOWER,
      maxHeight: 0.3,
      maxWidth: 0.2,
      maxAge: 365 * 1, // 1年
      growthRate: 1.5,
      activeSeasons: [SeasonType.SPRING, SeasonType.SUMMER],
      dormantSeasons: [SeasonType.AUTUMN, SeasonType.WINTER],
      floweringSeason: [SeasonType.SPRING],
      fruitingSeason: [SeasonType.SUMMER],
      leafFallSeason: [SeasonType.AUTUMN],
      isEvergreen: false,
      temperatureRange: {
        min: 5,
        max: 25
      },
      lightRequirement: 0.7,
      moistureRequirement: 0.6,
      nutrientRequirement: 0.5
    },
    [SeasonalVegetationType.SUMMER_FLOWER]: {
      type: SeasonalVegetationType.SUMMER_FLOWER,
      maxHeight: 0.5,
      maxWidth: 0.3,
      maxAge: 365 * 1, // 1年
      growthRate: 1.8,
      activeSeasons: [SeasonType.SPRING, SeasonType.SUMMER],
      dormantSeasons: [SeasonType.AUTUMN, SeasonType.WINTER],
      floweringSeason: [SeasonType.SUMMER],
      fruitingSeason: [SeasonType.AUTUMN],
      leafFallSeason: [SeasonType.AUTUMN],
      isEvergreen: false,
      temperatureRange: {
        min: 15,
        max: 35
      },
      lightRequirement: 0.8,
      moistureRequirement: 0.7,
      nutrientRequirement: 0.6
    },
    [SeasonalVegetationType.AUTUMN_FLOWER]: {
      type: SeasonalVegetationType.AUTUMN_FLOWER,
      maxHeight: 0.4,
      maxWidth: 0.3,
      maxAge: 365 * 1, // 1年
      growthRate: 1.2,
      activeSeasons: [SeasonType.SUMMER, SeasonType.AUTUMN],
      dormantSeasons: [SeasonType.WINTER, SeasonType.SPRING],
      floweringSeason: [SeasonType.AUTUMN],
      fruitingSeason: [SeasonType.AUTUMN],
      leafFallSeason: [SeasonType.WINTER],
      isEvergreen: false,
      temperatureRange: {
        min: 10,
        max: 30
      },
      lightRequirement: 0.6,
      moistureRequirement: 0.5,
      nutrientRequirement: 0.5
    },
    [SeasonalVegetationType.WINTER_FLOWER]: {
      type: SeasonalVegetationType.WINTER_FLOWER,
      maxHeight: 0.2,
      maxWidth: 0.2,
      maxAge: 365 * 1, // 1年
      growthRate: 0.8,
      activeSeasons: [SeasonType.WINTER, SeasonType.SPRING],
      dormantSeasons: [SeasonType.SUMMER, SeasonType.AUTUMN],
      floweringSeason: [SeasonType.WINTER],
      fruitingSeason: [SeasonType.SPRING],
      leafFallSeason: [],
      isEvergreen: true,
      temperatureRange: {
        min: -5,
        max: 15
      },
      lightRequirement: 0.5,
      moistureRequirement: 0.4,
      nutrientRequirement: 0.4
    },
    [SeasonalVegetationType.DECIDUOUS_TREE]: {
      type: SeasonalVegetationType.DECIDUOUS_TREE,
      maxHeight: 10.0,
      maxWidth: 5.0,
      maxAge: 365 * 50, // 50年
      growthRate: 0.5,
      activeSeasons: [SeasonType.SPRING, SeasonType.SUMMER],
      dormantSeasons: [SeasonType.WINTER],
      floweringSeason: [SeasonType.SPRING],
      fruitingSeason: [SeasonType.AUTUMN],
      leafFallSeason: [SeasonType.AUTUMN],
      isEvergreen: false,
      temperatureRange: {
        min: -10,
        max: 35
      },
      lightRequirement: 0.7,
      moistureRequirement: 0.6,
      nutrientRequirement: 0.5
    },
    [SeasonalVegetationType.CUSTOM]: {
      type: SeasonalVegetationType.CUSTOM,
      maxHeight: 1.0,
      maxWidth: 1.0,
      maxAge: 365 * 1, // 1年
      growthRate: 1.0,
      activeSeasons: [SeasonType.SPRING, SeasonType.SUMMER, SeasonType.AUTUMN],
      dormantSeasons: [SeasonType.WINTER],
      floweringSeason: [SeasonType.SPRING, SeasonType.SUMMER],
      fruitingSeason: [SeasonType.AUTUMN],
      leafFallSeason: [SeasonType.AUTUMN],
      isEvergreen: false,
      temperatureRange: {
        min: 0,
        max: 30
      },
      lightRequirement: 0.5,
      moistureRequirement: 0.5,
      nutrientRequirement: 0.5
    }
  };

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: SeasonalVegetationConfig) {
    // 合并配置
    this.config = { ...SeasonalVegetation.DEFAULT_CONFIGS[config.type], ...config };
  }

  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): SeasonalVegetationConfig {
    return this.config;
  }

  /**
   * 创建实例数据
   * @param id 实例ID
   * @param position 位置
   * @param rotation 旋转
   * @param scale 缩放
   * @returns 实例数据
   */
  public createInstanceData(
    id: string,
    position: THREE.Vector3,
    rotation: THREE.Euler,
    scale: THREE.Vector3
  ): VegetationInstanceData {
    return {
      id,
      type: VegetationType.SEASONAL,
      growthStage: VegetationGrowthStage.SEED,
      age: 0,
      health: 1.0,
      maxAge: this.config.maxAge || 365,
      growthRate: this.config.growthRate || 1.0,
      currentHeight: 0,
      maxHeight: this.config.maxHeight || 1.0,
      currentWidth: 0,
      maxWidth: this.config.maxWidth || 1.0,
      currentScale: new THREE.Vector3(0.05, 0.05, 0.05),
      maxScale: scale.clone(),
      position: position.clone(),
      rotation: rotation.clone(),
      evergreen: this.config.isEvergreen || false,
      flowering: false,
      floweringSeason: this.config.floweringSeason || [],
      fruiting: false,
      fruitingSeason: this.config.fruitingSeason || [],
      deciduous: !this.config.isEvergreen,
      leafFallSeason: this.config.leafFallSeason || [SeasonType.AUTUMN],
      userData: {
        seasonalType: this.config.type,
        activeSeasons: this.config.activeSeasons,
        dormantSeasons: this.config.dormantSeasons,
        temperatureRange: this.config.temperatureRange,
        lightRequirement: this.config.lightRequirement,
        moistureRequirement: this.config.moistureRequirement,
        nutrientRequirement: this.config.nutrientRequirement
      }
    };
  }

  /**
   * 更新实例数据
   * @param instanceData 实例数据
   * @param season 当前季节
   * @param temperature 温度
   * @param lightIntensity 光照强度
   * @param moisture 水分
   * @param nutrient 营养
   * @param deltaTime 帧间隔时间（秒）
   */
  public updateInstanceData(
    instanceData: VegetationInstanceData,
    season: SeasonType,
    temperature: number,
    lightIntensity: number,
    moisture: number,
    nutrient: number,
    deltaTime: number
  ): void {
    // 获取季节性数据
    const activeSeasons = this.config.activeSeasons || [SeasonType.SPRING, SeasonType.SUMMER, SeasonType.AUTUMN];
    const dormantSeasons = this.config.dormantSeasons || [SeasonType.WINTER];

    // 检查是否处于活跃季节
    const isActiveSeason = activeSeasons.includes(season);
    const isDormantSeason = dormantSeasons.includes(season);

    // 检查温度是否在范围内
    const temperatureRange = this.config.temperatureRange || { min: 0, max: 30 };
    const isTemperatureSuitable = temperature >= temperatureRange.min && temperature <= temperatureRange.max;

    // 检查光照强度是否满足要求
    const lightRequirement = this.config.lightRequirement || 0.5;
    const isLightIntensitySuitable = lightIntensity >= lightRequirement;

    // 检查水分是否满足要求
    const moistureRequirement = this.config.moistureRequirement || 0.5;
    const isMoistureSuitable = moisture >= moistureRequirement;

    // 检查营养是否满足要求
    const nutrientRequirement = this.config.nutrientRequirement || 0.5;
    const isNutrientSuitable = nutrient >= nutrientRequirement;

    // 计算环境适应度
    const environmentSuitability = (
      (isTemperatureSuitable ? 1 : 0) +
      (isLightIntensitySuitable ? 1 : 0) +
      (isMoistureSuitable ? 1 : 0) +
      (isNutrientSuitable ? 1 : 0)
    ) / 4;

    // 更新健康度
    if (isActiveSeason) {
      // 活跃季节，健康度缓慢上升
      instanceData.health = Math.max(0, Math.min(1, instanceData.health + (environmentSuitability - 0.5) * 0.01 * deltaTime));
    } else if (isDormantSeason) {
      // 休眠季节，健康度缓慢下降
      instanceData.health = Math.max(0, Math.min(1, instanceData.health - 0.001 * deltaTime));
    } else {
      // 其他季节，健康度保持不变
    }

    // 更新生长速度
    if (isActiveSeason) {
      // 活跃季节，正常生长
      instanceData.growthRate = this.config.growthRate || 1.0;
      instanceData.growthRate *= environmentSuitability;
    } else if (isDormantSeason) {
      // 休眠季节，停止生长
      instanceData.growthRate = 0;
    } else {
      // 其他季节，缓慢生长
      instanceData.growthRate = (this.config.growthRate || 1.0) * 0.1;
    }

    // 更新季节性特征
    this.updateSeasonalFeatures(instanceData, season);
  }

  /**
   * 更新季节性特征
   * @param instanceData 实例数据
   * @param season 当前季节
   */
  private updateSeasonalFeatures(instanceData: VegetationInstanceData, season: SeasonType): void {
    // 更新开花状态
    instanceData.flowering = instanceData.floweringSeason.includes(season);

    // 更新结果状态
    instanceData.fruiting = instanceData.fruitingSeason.includes(season);

    // 更新落叶状态
    if (!instanceData.evergreen) {
      instanceData.deciduous = instanceData.leafFallSeason.includes(season);
    }

    // 获取季节性数据
    const activeSeasons = this.config.activeSeasons || [SeasonType.SPRING, SeasonType.SUMMER, SeasonType.AUTUMN];
    const dormantSeasons = this.config.dormantSeasons || [SeasonType.WINTER];

    // 检查是否处于活跃季节
    const isActiveSeason = activeSeasons.includes(season);
    const isDormantSeason = dormantSeasons.includes(season);

    // 更新外观
    if (isActiveSeason) {
      // 活跃季节，正常外观
      // 这里可以添加更多的外观更新逻辑
    } else if (isDormantSeason) {
      // 休眠季节，休眠外观
      instanceData.deciduous = true;
      instanceData.flowering = false;
      instanceData.fruiting = false;
    }
  }

  /**
   * 创建季节性植物模型
   * @param type 季节性植物类型
   * @param season 当前季节
   * @returns 模型
   */
  public static createModel(type: SeasonalVegetationType, season: SeasonType): THREE.Object3D {
    let model: THREE.Object3D;

    switch (type) {
      case SeasonalVegetationType.SPRING_FLOWER:
        model = SeasonalVegetation.createSpringFlowerModel(season);
        break;
      case SeasonalVegetationType.SUMMER_FLOWER:
        model = SeasonalVegetation.createSummerFlowerModel(season);
        break;
      case SeasonalVegetationType.AUTUMN_FLOWER:
        model = SeasonalVegetation.createAutumnFlowerModel(season);
        break;
      case SeasonalVegetationType.WINTER_FLOWER:
        model = SeasonalVegetation.createWinterFlowerModel(season);
        break;
      case SeasonalVegetationType.DECIDUOUS_TREE:
        model = SeasonalVegetation.createDeciduousTreeModel(season);
        break;
      case SeasonalVegetationType.CUSTOM:
      default:
        model = SeasonalVegetation.createDefaultModel(season);
        break;
    }

    return model;
  }

  /**
   * 创建春季花卉模型
   * @param season 当前季节
   * @returns 春季花卉模型
   */
  private static createSpringFlowerModel(season: SeasonType): THREE.Object3D {
    const group = new THREE.Group();

    // 创建茎
    const stemGeometry = new THREE.CylinderGeometry(0.01, 0.01, 0.3, 8);
    const stemMaterial = new THREE.MeshBasicMaterial({
      color: 0x00aa00
    });
    const stem = new THREE.Mesh(stemGeometry, stemMaterial);
    stem.position.y = 0.15;
    group.add(stem);

    // 创建叶子
    const leafGeometry = new THREE.PlaneGeometry(0.1, 0.05);
    const leafMaterial = new THREE.MeshBasicMaterial({
      color: 0x00aa00,
      side: THREE.DoubleSide
    });

    // 添加多个叶子
    for (let i = 0; i < 2; i++) {
      const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
      leaf.position.y = 0.1 + i * 0.1;
      leaf.rotation.x = Math.PI / 2;
      leaf.rotation.z = Math.PI / 2 * i;
      group.add(leaf);
    }

    // 如果是春季，添加花朵
    if (season === SeasonType.SPRING) {
      const flowerGeometry = new THREE.SphereGeometry(0.05, 8, 8);
      const flowerMaterial = new THREE.MeshBasicMaterial({
        color: 0xffaaff
      });
      const flower = new THREE.Mesh(flowerGeometry, flowerMaterial);
      flower.position.y = 0.3;
      group.add(flower);
    }

    return group;
  }

  /**
   * 创建夏季花卉模型
   * @param season 当前季节
   * @returns 夏季花卉模型
   */
  private static createSummerFlowerModel(season: SeasonType): THREE.Object3D {
    const group = new THREE.Group();

    // 创建茎
    const stemGeometry = new THREE.CylinderGeometry(0.01, 0.01, 0.5, 8);
    const stemMaterial = new THREE.MeshBasicMaterial({
      color: 0x00aa00
    });
    const stem = new THREE.Mesh(stemGeometry, stemMaterial);
    stem.position.y = 0.25;
    group.add(stem);

    // 创建叶子
    const leafGeometry = new THREE.PlaneGeometry(0.15, 0.08);
    const leafMaterial = new THREE.MeshBasicMaterial({
      color: 0x00aa00,
      side: THREE.DoubleSide
    });

    // 添加多个叶子
    for (let i = 0; i < 3; i++) {
      const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
      leaf.position.y = 0.1 + i * 0.15;
      leaf.rotation.x = Math.PI / 2;
      leaf.rotation.z = Math.PI * 2 / 3 * i;
      group.add(leaf);
    }

    // 如果是夏季，添加花朵
    if (season === SeasonType.SUMMER) {
      const flowerGeometry = new THREE.SphereGeometry(0.08, 8, 8);
      const flowerMaterial = new THREE.MeshBasicMaterial({
        color: 0xffff00
      });
      const flower = new THREE.Mesh(flowerGeometry, flowerMaterial);
      flower.position.y = 0.5;
      group.add(flower);
    }

    return group;
  }

  /**
   * 创建秋季花卉模型
   * @param season 当前季节
   * @returns 秋季花卉模型
   */
  private static createAutumnFlowerModel(season: SeasonType): THREE.Object3D {
    const group = new THREE.Group();

    // 创建茎
    const stemGeometry = new THREE.CylinderGeometry(0.01, 0.01, 0.4, 8);
    const stemMaterial = new THREE.MeshBasicMaterial({
      color: 0x008800
    });
    const stem = new THREE.Mesh(stemGeometry, stemMaterial);
    stem.position.y = 0.2;
    group.add(stem);

    // 创建叶子
    const leafGeometry = new THREE.PlaneGeometry(0.12, 0.06);
    const leafMaterial = new THREE.MeshBasicMaterial({
      color: season === SeasonType.AUTUMN ? 0xdd8800 : 0x00aa00,
      side: THREE.DoubleSide
    });

    // 添加多个叶子
    for (let i = 0; i < 3; i++) {
      const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
      leaf.position.y = 0.1 + i * 0.12;
      leaf.rotation.x = Math.PI / 2;
      leaf.rotation.z = Math.PI * 2 / 3 * i;
      group.add(leaf);
    }

    // 如果是秋季，添加花朵
    if (season === SeasonType.AUTUMN) {
      const flowerGeometry = new THREE.SphereGeometry(0.07, 8, 8);
      const flowerMaterial = new THREE.MeshBasicMaterial({
        color: 0xff8800
      });
      const flower = new THREE.Mesh(flowerGeometry, flowerMaterial);
      flower.position.y = 0.4;
      group.add(flower);
    }

    return group;
  }

  /**
   * 创建冬季花卉模型
   * @param season 当前季节
   * @returns 冬季花卉模型
   */
  private static createWinterFlowerModel(season: SeasonType): THREE.Object3D {
    const group = new THREE.Group();

    // 创建茎
    const stemGeometry = new THREE.CylinderGeometry(0.01, 0.01, 0.2, 8);
    const stemMaterial = new THREE.MeshBasicMaterial({
      color: 0x006600
    });
    const stem = new THREE.Mesh(stemGeometry, stemMaterial);
    stem.position.y = 0.1;
    group.add(stem);

    // 创建叶子
    const leafGeometry = new THREE.PlaneGeometry(0.08, 0.04);
    const leafMaterial = new THREE.MeshBasicMaterial({
      color: 0x006600,
      side: THREE.DoubleSide
    });

    // 添加多个叶子
    for (let i = 0; i < 2; i++) {
      const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
      leaf.position.y = 0.05 + i * 0.08;
      leaf.rotation.x = Math.PI / 2;
      leaf.rotation.z = Math.PI * i;
      group.add(leaf);
    }

    // 如果是冬季，添加花朵
    if (season === SeasonType.WINTER) {
      const flowerGeometry = new THREE.SphereGeometry(0.04, 8, 8);
      const flowerMaterial = new THREE.MeshBasicMaterial({
        color: 0xffffff
      });
      const flower = new THREE.Mesh(flowerGeometry, flowerMaterial);
      flower.position.y = 0.2;
      group.add(flower);
    }

    return group;
  }

  /**
   * 创建落叶乔木模型
   * @param season 当前季节
   * @returns 落叶乔木模型
   */
  private static createDeciduousTreeModel(season: SeasonType): THREE.Object3D {
    const group = new THREE.Group();

    // 创建树干
    const trunkGeometry = new THREE.CylinderGeometry(0.1, 0.15, 1.0, 8);
    const trunkMaterial = new THREE.MeshBasicMaterial({
      color: 0x8B4513
    });
    const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
    trunk.position.y = 0.5;
    group.add(trunk);

    // 如果不是秋季或冬季，添加树冠
    if (season !== SeasonType.AUTUMN && season !== SeasonType.WINTER) {
      const crownGeometry = new THREE.SphereGeometry(0.5, 8, 8);
      const crownMaterial = new THREE.MeshBasicMaterial({
        color: 0x00aa00
      });
      const crown = new THREE.Mesh(crownGeometry, crownMaterial);
      crown.position.y = 1.2;
      group.add(crown);
    }
    // 如果是秋季，添加黄色树冠
    else if (season === SeasonType.AUTUMN) {
      const crownGeometry = new THREE.SphereGeometry(0.5, 8, 8);
      const crownMaterial = new THREE.MeshBasicMaterial({
        color: 0xdd8800
      });
      const crown = new THREE.Mesh(crownGeometry, crownMaterial);
      crown.position.y = 1.2;
      group.add(crown);
    }
    // 如果是冬季，只有树干

    // 如果是春季，添加花朵
    if (season === SeasonType.SPRING) {
      const flowerGeometry = new THREE.SphereGeometry(0.05, 8, 8);
      const flowerMaterial = new THREE.MeshBasicMaterial({
        color: 0xffaaff
      });

      // 添加多个花朵
      for (let i = 0; i < 5; i++) {
        const flower = new THREE.Mesh(flowerGeometry, flowerMaterial);
        flower.position.x = (Math.random() - 0.5) * 0.8;
        flower.position.y = 1.2 + (Math.random() - 0.5) * 0.4;
        flower.position.z = (Math.random() - 0.5) * 0.8;
        group.add(flower);
      }
    }

    return group;
  }

  /**
   * 创建默认模型
   * @param season 当前季节
   * @returns 默认模型
   */
  private static createDefaultModel(season: SeasonType): THREE.Object3D {
    const group = new THREE.Group();

    // 创建简单的几何体
    const geometry = new THREE.SphereGeometry(0.2, 8, 8);
    const material = new THREE.MeshBasicMaterial({
      color: season === SeasonType.AUTUMN ? 0xdd8800 : 0x00aa00
    });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.y = 0.2;
    group.add(mesh);

    return group;
  }
}