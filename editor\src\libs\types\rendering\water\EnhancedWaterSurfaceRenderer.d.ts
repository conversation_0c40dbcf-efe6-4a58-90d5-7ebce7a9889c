/**
 * 增强水面渲染器
 * 用于渲染高质量的水面效果，包括反射、折射、焦散等
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
/**
 * 增强水面渲染器配置
 */
export interface EnhancedWaterSurfaceRendererConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 反射贴图分辨率 */
    reflectionMapResolution?: number;
    /** 折射贴图分辨率 */
    refractionMapResolution?: number;
    /** 是否启用反射 */
    enableReflection?: boolean;
    /** 是否启用折射 */
    enableRefraction?: boolean;
    /** 是否启用焦散 */
    enableCaustics?: boolean;
    /** 是否启用水下雾效 */
    enableUnderwaterFog?: boolean;
    /** 是否启用水下扭曲 */
    enableUnderwaterDistortion?: boolean;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
    /** 是否使用低质量模式（用于低性能设备） */
    useLowQualityMode?: boolean;
}
/**
 * 增强水面渲染器
 */
export declare class EnhancedWaterSurfaceRenderer extends System {
    /** 配置 */
    private config;
    /** 水体组件映射 */
    private waterBodies;
    /** 水体材质映射 */
    private waterMaterials;
    /** 是否启用渲染器 */
    private rendererEnabled;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 帧计数器 */
    private frameCount;
    /** 渲染器 */
    private renderer;
    /** 活动相机 */
    private activeCamera;
    /** 活动场景 */
    private activeScene;
    /** 反射渲染目标 */
    private reflectionRenderTarget;
    /** 折射渲染目标 */
    private refractionRenderTarget;
    /** 焦散渲染目标 */
    private causticsRenderTarget;
    /** 反射相机 */
    private reflectionCamera;
    /** 折射相机 */
    private refractionCamera;
    /** 反射平面 */
    private reflectionPlane;
    /** 折射平面 */
    private refractionPlane;
    /** 性能监控器 */
    private performanceMonitor;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: EnhancedWaterSurfaceRendererConfig);
    /**
     * 初始化渲染器
     */
    private initializeRenderer;
    /**
     * 创建渲染目标
     */
    private createRenderTargets;
    /**
     * 创建相机
     */
    private createCameras;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新活动相机
     */
    private updateActiveCamera;
    /**
     * 更新活动场景
     */
    private updateActiveScene;
    /**
     * 更新水体渲染
     * @param entityId 实体ID
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterRendering;
    /**
     * 更新反射贴图
     * @param waterBody 水体组件
     */
    private updateReflectionMap;
    /**
     * 更新折射贴图
     * @param waterBody 水体组件
     */
    private updateRefractionMap;
    /**
     * 更新焦散贴图
     * @param waterBody 水体组件
     */
    private updateCausticsMap;
    /**
     * 创建焦散材质
     */
    private causticsMaterial;
    private causticsScene;
    private causticsCamera;
    private causticsQuad;
    private createCausticsMaterial;
    /**
     * 渲染焦散
     * @param waterBody 水体组件
     */
    private renderCaustics;
    /**
     * 添加水体组件
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterBody(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体组件
     * @param entity 实体
     */
    removeWaterBody(entity: Entity): void;
    /**
     * 创建水体材质
     * @param component 水体组件
     * @returns 水体材质
     */
    private createWaterMaterial;
    /**
     * 设置配置
     * @param config 配置
     */
    setConfig(config: Partial<EnhancedWaterSurfaceRendererConfig>): void;
    /**
     * 设置渲染器
     * @param renderer 渲染器
     */
    setRenderer(renderer: THREE.WebGLRenderer): void;
    /**
     * 设置活动相机
     * @param camera 相机
     */
    setActiveCamera(camera: Camera): void;
    /**
     * 设置活动场景
     * @param scene 场景
     */
    setActiveScene(scene: Scene): void;
    /**
     * 销毁渲染器
     */
    dispose(): void;
}
