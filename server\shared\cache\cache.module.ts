/**
 * 缓存模块
 * 提供通用的缓存功能，支持多级缓存（内存和Redis）
 */
import { Module, DynamicModule, Global } from '@nestjs/common';
import { CacheService } from './cache.service';
import { CacheConfig } from './interfaces/cache-config.interface';

@Global()
@Module({})
export class CacheModule {
  /**
   * 注册缓存模块
   * @param options 缓存配置
   * @returns 动态模块
   */
  static register(options: CacheConfig = {}): DynamicModule {
    return {
      module: CacheModule,
      providers: [
        {
          provide: 'CACHE_OPTIONS',
          useValue: options,
        },
        CacheService,
      ],
      exports: [CacheService],
    };
  }
}
