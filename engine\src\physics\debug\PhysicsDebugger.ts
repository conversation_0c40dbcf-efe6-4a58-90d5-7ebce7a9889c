/**
 * 物理调试器
 * 用于可视化物理世界
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { PhysicsSystem } from '../PhysicsSystem';
import type { PhysicsBody } from '../PhysicsBody';
import { PhysicsCollider } from '../PhysicsCollider';
import { PhysicsConstraint } from '../constraints/PhysicsConstraint';
import { PointToPointConstraint } from '../constraints/PointToPointConstraint';
import { HingeConstraint } from '../constraints/HingeConstraint';
import { DistanceConstraint } from '../constraints/DistanceConstraint';
import { SpringConstraint } from '../constraints/SpringConstraint';
import { ConeTwistConstraint } from '../constraints/ConeTwistConstraint';

/**
 * 物理调试器选项
 */
export interface PhysicsDebuggerOptions {
  /** 是否显示物理体 */
  showBodies?: boolean;
  /** 是否显示约束 */
  showConstraints?: boolean;
  /** 是否显示碰撞点 */
  showContactPoints?: boolean;
  /** 是否显示AABB */
  showAABBs?: boolean;
  /** 物理体颜色 */
  bodyColor?: THREE.Color | number;
  /** 静态物理体颜色 */
  staticBodyColor?: THREE.Color | number;
  /** 运动学物理体颜色 */
  kinematicBodyColor?: THREE.Color | number;
  /** 约束颜色 */
  constraintColor?: THREE.Color | number;
  /** 碰撞点颜色 */
  contactPointColor?: THREE.Color | number;
  /** AABB颜色 */
  aabbColor?: THREE.Color | number;
  /** 线宽 */
  lineWidth?: number;
  /** 透明度 */
  opacity?: number;
}

/**
 * 物理调试器
 */
export class PhysicsDebugger {
  /** 物理系统 */
  protected physicsSystem: PhysicsSystem;

  /** 调试场景 */
  protected scene: THREE.Scene;

  /** 物理体网格映射 */
  private bodyMeshes: Map<CANNON.Body, THREE.Object3D> = new Map();

  /** 约束网格映射 */
  private constraintMeshes: Map<CANNON.Constraint, THREE.Object3D> = new Map();

  /** 碰撞点网格 */
  private contactPointMeshes: THREE.Object3D[] = [];

  /** AABB网格映射 */
  private aabbMeshes: Map<CANNON.Body, THREE.LineSegments> = new Map();

  /** 是否显示物理体 */
  private showBodies: boolean;

  /** 是否显示约束 */
  private showConstraints: boolean;

  /** 是否显示碰撞点 */
  private showContactPoints: boolean;

  /** 是否显示AABB */
  private showAABBs: boolean;

  /** 物理体颜色 */
  private bodyColor: THREE.Color;

  /** 静态物理体颜色 */
  private staticBodyColor: THREE.Color;

  /** 运动学物理体颜色 */
  private kinematicBodyColor: THREE.Color;

  /** 约束颜色 */
  private constraintColor: THREE.Color;

  /** 碰撞点颜色 */
  private contactPointColor: THREE.Color;

  /** AABB颜色 */
  private aabbColor: THREE.Color;

  /** 线宽 */
  private lineWidth: number;

  /** 透明度 */
  private opacity: number;

  /** 是否已初始化 */
  protected initialized: boolean = false;

  /**
   * 创建物理调试器
   * @param physicsSystem 物理系统
   * @param options 调试器选项
   */
  constructor(physicsSystem: PhysicsSystem, options: PhysicsDebuggerOptions = {}) {
    this.physicsSystem = physicsSystem;
    this.scene = new THREE.Scene();

    // 设置选项
    this.showBodies = options.showBodies !== undefined ? options.showBodies : true;
    this.showConstraints = options.showConstraints !== undefined ? options.showConstraints : true;
    this.showContactPoints = options.showContactPoints !== undefined ? options.showContactPoints : false;
    this.showAABBs = options.showAABBs !== undefined ? options.showAABBs : false;

    // 设置颜色
    this.bodyColor = options.bodyColor instanceof THREE.Color
      ? options.bodyColor
      : new THREE.Color(options.bodyColor !== undefined ? options.bodyColor : 0x00ff00);

    this.staticBodyColor = options.staticBodyColor instanceof THREE.Color
      ? options.staticBodyColor
      : new THREE.Color(options.staticBodyColor !== undefined ? options.staticBodyColor : 0xff0000);

    this.kinematicBodyColor = options.kinematicBodyColor instanceof THREE.Color
      ? options.kinematicBodyColor
      : new THREE.Color(options.kinematicBodyColor !== undefined ? options.kinematicBodyColor : 0x0000ff);

    this.constraintColor = options.constraintColor instanceof THREE.Color
      ? options.constraintColor
      : new THREE.Color(options.constraintColor !== undefined ? options.constraintColor : 0xffff00);

    this.contactPointColor = options.contactPointColor instanceof THREE.Color
      ? options.contactPointColor
      : new THREE.Color(options.contactPointColor !== undefined ? options.contactPointColor : 0xff00ff);

    this.aabbColor = options.aabbColor instanceof THREE.Color
      ? options.aabbColor
      : new THREE.Color(options.aabbColor !== undefined ? options.aabbColor : 0xffffff);

    // 设置其他属性
    this.lineWidth = options.lineWidth !== undefined ? options.lineWidth : 1;
    this.opacity = options.opacity !== undefined ? options.opacity : 0.5;
  }

  /**
   * 初始化调试器
   */
  public initialize(): void {
    if (this.initialized) return;

    // 清空场景
    this.clear();

    // 获取物理世界
    const world = this.physicsSystem.getPhysicsWorld();

    // 创建物理体网格
    if (this.showBodies) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];
        const mesh = this.createBodyMesh(body);
        this.bodyMeshes.set(body, mesh);
        this.scene.add(mesh);
      }
    }

    // 创建约束网格
    if (this.showConstraints) {
      for (let i = 0; i < world.constraints.length; i++) {
        const constraint = world.constraints[i];
        const mesh = this.createConstraintMesh(constraint);
        if (mesh) {
          this.constraintMeshes.set(constraint, mesh);
          this.scene.add(mesh);
        }
      }
    }

    // 创建AABB网格
    if (this.showAABBs) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];
        const mesh = this.createAABBMesh(body);
        this.aabbMeshes.set(body, mesh);
        this.scene.add(mesh);
      }
    }

    this.initialized = true;
  }

  /**
   * 更新调试器
   */
  public update(): void {
    if (!this.initialized) return;

    // 获取物理世界
    const world = this.physicsSystem.getPhysicsWorld();

    // 更新物理体网格
    if (this.showBodies) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];

        // 如果物理体没有网格，创建一个
        if (!this.bodyMeshes.has(body)) {
          const mesh = this.createBodyMesh(body);
          this.bodyMeshes.set(body, mesh);
          this.scene.add(mesh);
        }

        // 更新网格位置和旋转
        const mesh = this.bodyMeshes.get(body);
        mesh.position.copy(new THREE.Vector3(body.position.x, body.position.y, body.position.z));
        mesh.quaternion.copy(new THREE.Quaternion(body.quaternion.x, body.quaternion.y, body.quaternion.z, body.quaternion.w));
      }

      // 移除不存在的物理体网格
      for (const [body, mesh] of this.bodyMeshes.entries()) {
        if (!world.bodies.includes(body)) {
          this.scene.remove(mesh);
          this.bodyMeshes.delete(body);
        }
      }
    }

    // 更新约束网格
    if (this.showConstraints) {
      for (let i = 0; i < world.constraints.length; i++) {
        const constraint = world.constraints[i];

        // 如果约束没有网格，创建一个
        if (!this.constraintMeshes.has(constraint)) {
          const mesh = this.createConstraintMesh(constraint);
          if (mesh) {
            this.constraintMeshes.set(constraint, mesh);
            this.scene.add(mesh);
          }
        }

        // 更新约束网格
        const mesh = this.constraintMeshes.get(constraint);
        if (mesh) {
          this.updateConstraintMesh(constraint, mesh);
        }
      }

      // 移除不存在的约束网格
      for (const [constraint, mesh] of this.constraintMeshes.entries()) {
        if (!world.constraints.includes(constraint)) {
          this.scene.remove(mesh);
          this.constraintMeshes.delete(constraint);
        }
      }
    }

    // 更新碰撞点网格
    if (this.showContactPoints) {
      // 移除所有碰撞点网格
      for (const mesh of this.contactPointMeshes) {
        this.scene.remove(mesh);
      }
      this.contactPointMeshes = [];

      // 创建新的碰撞点网格
      for (let i = 0; i < world.contacts.length; i++) {
        const contact = world.contacts[i];
        // 在 cannon-es 中，world.contacts 中的每个元素就是 ContactEquation
        const mesh = this.createContactPointMesh(contact);
        this.contactPointMeshes.push(mesh);
        this.scene.add(mesh);
      }
    }

    // 更新AABB网格
    if (this.showAABBs) {
      for (let i = 0; i < world.bodies.length; i++) {
        const body = world.bodies[i];

        // 如果物理体没有AABB网格，创建一个
        if (!this.aabbMeshes.has(body)) {
          const mesh = this.createAABBMesh(body);
          this.aabbMeshes.set(body, mesh);
          this.scene.add(mesh);
        }

        // 更新AABB网格
        const mesh = this.aabbMeshes.get(body);
        this.updateAABBMesh(body, mesh);
      }

      // 移除不存在的AABB网格
      for (const [body, mesh] of this.aabbMeshes.entries()) {
        if (!world.bodies.includes(body)) {
          this.scene.remove(mesh);
          this.aabbMeshes.delete(body);
        }
      }
    }
  }

  /**
   * 创建物理体网格
   * @param body 物理体
   * @returns 物理体网格
   */
  private createBodyMesh(body: CANNON.Body): THREE.Object3D {
    const group = new THREE.Group();

    // 根据物理体类型选择颜色
    let color: THREE.Color;
    if (body.type === CANNON.Body.STATIC) {
      color = this.staticBodyColor;
    } else if (body.type === CANNON.Body.KINEMATIC) {
      color = this.kinematicBodyColor;
    } else {
      color = this.bodyColor;
    }

    // 创建材质
    const material = new THREE.MeshBasicMaterial({
      color: color,
      wireframe: true,
      transparent: true,
      opacity: this.opacity,
      depthTest: true,
    });

    // 为每个形状创建网格
    for (let i = 0; i < body.shapes.length; i++) {
      const shape = body.shapes[i];
      let geometry: THREE.BufferGeometry;
      let mesh: THREE.Mesh;

      // 根据形状类型创建几何体
      switch (shape.type) {
        case CANNON.Shape.types.BOX:
          const boxShape = shape as CANNON.Box;
          geometry = new THREE.BoxGeometry(
            boxShape.halfExtents.x * 2,
            boxShape.halfExtents.y * 2,
            boxShape.halfExtents.z * 2
          );
          mesh = new THREE.Mesh(geometry, material);
          break;

        case CANNON.Shape.types.SPHERE:
          const sphereShape = shape as CANNON.Sphere;
          geometry = new THREE.SphereGeometry(sphereShape.radius, 16, 16);
          mesh = new THREE.Mesh(geometry, material);
          break;

        case CANNON.Shape.types.CYLINDER:
          const cylinderShape = shape as CANNON.Cylinder;
          geometry = new THREE.CylinderGeometry(
            cylinderShape.radiusTop,
            cylinderShape.radiusBottom,
            cylinderShape.height,
            16
          );
          mesh = new THREE.Mesh(geometry, material);
          // 旋转几何体以匹配CANNON.js的坐标系
          mesh.rotation.x = Math.PI / 2;
          break;

        case CANNON.Shape.types.PLANE:
          geometry = new THREE.PlaneGeometry(10, 10, 10, 10);
          mesh = new THREE.Mesh(geometry, material);
          break;

        case CANNON.Shape.types.CONVEXPOLYHEDRON:
          const convexShape = shape as CANNON.ConvexPolyhedron;
          const vertices = [];
          for (let j = 0; j < convexShape.vertices.length; j++) {
            const v = convexShape.vertices[j];
            vertices.push(new THREE.Vector3(v.x, v.y, v.z));
          }

          const faces = [];
          for (let j = 0; j < convexShape.faces.length; j++) {
            const face = convexShape.faces[j];
            // 三角化面
            for (let k = 1; k < face.length - 1; k++) {
              faces.push(face[0], face[k], face[k + 1]);
            }
          }

          geometry = new THREE.BufferGeometry();
          const positionArray = new Float32Array(faces.length * 3 * 3);
          for (let j = 0; j < faces.length; j += 3) {
            const v1 = vertices[faces[j]];
            const v2 = vertices[faces[j + 1]];
            const v3 = vertices[faces[j + 2]];

            positionArray[j * 3] = v1.x;
            positionArray[j * 3 + 1] = v1.y;
            positionArray[j * 3 + 2] = v1.z;

            positionArray[j * 3 + 3] = v2.x;
            positionArray[j * 3 + 4] = v2.y;
            positionArray[j * 3 + 5] = v2.z;

            positionArray[j * 3 + 6] = v3.x;
            positionArray[j * 3 + 7] = v3.y;
            positionArray[j * 3 + 8] = v3.z;
          }

          geometry.setAttribute('position', new THREE.BufferAttribute(positionArray, 3));
          geometry.computeVertexNormals();
          mesh = new THREE.Mesh(geometry, material);
          break;

        default:
          // 对于不支持的形状，创建一个小球作为占位符
          geometry = new THREE.SphereGeometry(0.1, 8, 8);
          mesh = new THREE.Mesh(geometry, material);
          console.warn(`不支持的物理形状类型: ${shape.type}`);
          break;
      }

      // 应用形状的位置和旋转
      if (body.shapeOffsets[i]) {
        mesh.position.copy(new THREE.Vector3(
          body.shapeOffsets[i].x,
          body.shapeOffsets[i].y,
          body.shapeOffsets[i].z
        ));
      }

      if (body.shapeOrientations[i]) {
        mesh.quaternion.copy(new THREE.Quaternion(
          body.shapeOrientations[i].x,
          body.shapeOrientations[i].y,
          body.shapeOrientations[i].z,
          body.shapeOrientations[i].w
        ));
      }

      group.add(mesh);
    }

    return group;
  }

  /**
   * 创建约束网格
   * @param constraint 约束
   * @returns 约束网格
   */
  protected createConstraintMesh(constraint: CANNON.Constraint): THREE.Object3D | null {
    // 创建约束可视化的基本实现
    const material = new THREE.LineBasicMaterial({ color: this.constraintColor });
    const geometry = new THREE.BufferGeometry();

    // 获取约束的两个物理体
    const bodyA = constraint.bodyA;
    const bodyB = constraint.bodyB;

    if (!bodyA || !bodyB) return null;

    // 创建连接线
    const points = [
      new THREE.Vector3(bodyA.position.x, bodyA.position.y, bodyA.position.z),
      new THREE.Vector3(bodyB.position.x, bodyB.position.y, bodyB.position.z)
    ];

    geometry.setFromPoints(points);
    return new THREE.Line(geometry, material);
  }

  /**
   * 更新约束网格
   * @param constraint 约束
   * @param mesh 网格
   */
  protected updateConstraintMesh(constraint: CANNON.Constraint, mesh: THREE.Object3D): void {
    const line = mesh as THREE.Line;
    const geometry = line.geometry as THREE.BufferGeometry;

    const bodyA = constraint.bodyA;
    const bodyB = constraint.bodyB;

    if (!bodyA || !bodyB) return;

    const points = [
      new THREE.Vector3(bodyA.position.x, bodyA.position.y, bodyA.position.z),
      new THREE.Vector3(bodyB.position.x, bodyB.position.y, bodyB.position.z)
    ];

    geometry.setFromPoints(points);
  }

  /**
   * 创建碰撞点网格
   * @param contact 碰撞接触点
   * @returns 碰撞点网格
   */
  protected createContactPointMesh(contact: CANNON.ContactEquation): THREE.Object3D {
    const geometry = new THREE.SphereGeometry(0.05, 8, 8);
    const material = new THREE.MeshBasicMaterial({ color: this.contactPointColor });
    const mesh = new THREE.Mesh(geometry, material);

    // 设置位置 - 在 cannon-es 中使用正确的属性
    mesh.position.set(
      contact.ri.x + contact.bi.position.x,
      contact.ri.y + contact.bi.position.y,
      contact.ri.z + contact.bi.position.z
    );

    return mesh;
  }

  /**
   * 创建AABB网格
   * @param body 物理体
   * @returns AABB网格
   */
  protected createAABBMesh(body: CANNON.Body): THREE.LineSegments {
    const geometry = new THREE.EdgesGeometry(new THREE.BoxGeometry(1, 1, 1));
    const material = new THREE.LineBasicMaterial({ color: this.aabbColor });
    const mesh = new THREE.LineSegments(geometry, material);

    this.updateAABBMesh(body, mesh);
    return mesh;
  }

  /**
   * 更新AABB网格
   * @param body 物理体
   * @param mesh 网格
   */
  protected updateAABBMesh(body: CANNON.Body, mesh: THREE.LineSegments): void {
    // 在 cannon-es 中，AABB 通过 body.aabb 访问，但需要先更新
    // 使用类型断言来访问可能存在的方法
    if ((body as any).computeAABB) {
      (body as any).computeAABB();
    }

    const aabb = body.aabb;
    if (!aabb) return;

    // 更新网格大小和位置
    const size = new THREE.Vector3(
      aabb.upperBound.x - aabb.lowerBound.x,
      aabb.upperBound.y - aabb.lowerBound.y,
      aabb.upperBound.z - aabb.lowerBound.z
    );

    const center = new THREE.Vector3(
      (aabb.upperBound.x + aabb.lowerBound.x) / 2,
      (aabb.upperBound.y + aabb.lowerBound.y) / 2,
      (aabb.upperBound.z + aabb.lowerBound.z) / 2
    );

    mesh.scale.copy(size);
    mesh.position.copy(center);
  }

  /**
   * 清空调试器
   */
  protected clear(): void {
    // 清空所有网格
    this.scene.clear();
    this.bodyMeshes.clear();
    this.constraintMeshes.clear();
    this.contactPointMeshes = [];
    this.aabbMeshes.clear();
  }

  /**
   * 销毁调试器
   */
  public dispose(): void {
    this.clear();
    this.initialized = false;
  }
}
