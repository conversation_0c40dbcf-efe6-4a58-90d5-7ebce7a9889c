import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { exec } from 'child_process';
import { promisify } from 'util';
import { HealthCheckEntity } from './entities/health-check.entity';

const execAsync = promisify(exec);

export enum RecoveryActionType {
  RESTART_SERVICE = 'restart_service',
  RESTART_CONTAINER = 'restart_container',
  EXECUTE_COMMAND = 'execute_command',
  CALL_WEBHOOK = 'call_webhook',
  SCALE_UP = 'scale_up',
  FAILOVER = 'failover',
}

@Injectable()
export class AutoRecoveryService {
  private readonly logger = new Logger(AutoRecoveryService.name);

  constructor(
    private readonly httpService: HttpService,
  ) {}

  /**
   * 执行自动恢复
   */
  async recover(healthCheck: HealthCheckEntity): Promise<{
    action: string;
    result: string;
    success: boolean;
  }> {
    try {
      // 获取恢复配置
      const config = healthCheck.autoRecoveryConfig || {};
      const actionType = config.actionType || RecoveryActionType.RESTART_SERVICE;
      
      // 根据恢复类型执行不同的恢复操作
      switch (actionType) {
        case RecoveryActionType.RESTART_SERVICE:
          return this.restartService(healthCheck, config);
        case RecoveryActionType.RESTART_CONTAINER:
          return this.restartContainer(healthCheck, config);
        case RecoveryActionType.EXECUTE_COMMAND:
          return this.executeCommand(healthCheck, config);
        case RecoveryActionType.CALL_WEBHOOK:
          return this.callWebhook(healthCheck, config);
        case RecoveryActionType.SCALE_UP:
          return this.scaleUp(healthCheck, config);
        case RecoveryActionType.FAILOVER:
          return this.failover(healthCheck, config);
        default:
          throw new Error(`不支持的恢复操作类型: ${actionType}`);
      }
    } catch (error) {
      this.logger.error(`执行自动恢复失败: ${error.message}`, error.stack);
      
      return {
        action: '自动恢复失败',
        result: `错误: ${error.message}`,
        success: false,
      };
    }
  }

  /**
   * 重启服务
   */
  private async restartService(
    healthCheck: HealthCheckEntity,
    config: Record<string, any>,
  ): Promise<{
    action: string;
    result: string;
    success: boolean;
  }> {
    try {
      // 获取服务注册中心URL
      const serviceRegistryUrl = config.serviceRegistryUrl || 'http://localhost:3000';
      
      // 调用服务注册中心的重启API
      const response = await firstValueFrom(
        this.httpService.post(`${serviceRegistryUrl}/registry/instances/${healthCheck.instanceId}/restart`, {
          reason: '自动恢复',
          triggeredBy: 'auto-recovery',
        })
      );
      
      if (response.status >= 200 && response.status < 300) {
        return {
          action: '重启服务',
          result: `成功: ${response.data.message || '服务重启请求已发送'}`,
          success: true,
        };
      } else {
        return {
          action: '重启服务',
          result: `失败: ${response.status} ${response.statusText}`,
          success: false,
        };
      }
    } catch (error) {
      this.logger.error(`重启服务失败: ${error.message}`, error.stack);
      
      return {
        action: '重启服务',
        result: `错误: ${error.message}`,
        success: false,
      };
    }
  }

  /**
   * 重启容器
   */
  private async restartContainer(
    healthCheck: HealthCheckEntity,
    config: Record<string, any>,
  ): Promise<{
    action: string;
    result: string;
    success: boolean;
  }> {
    try {
      // 获取容器ID或名称
      const containerId = config.containerId || healthCheck.instanceId;
      
      if (!containerId) {
        throw new Error('未指定容器ID');
      }
      
      // 执行Docker重启命令
      const { stdout, stderr } = await execAsync(`docker restart ${containerId}`);
      
      return {
        action: '重启容器',
        result: `成功: ${stdout.trim() || '容器已重启'}`,
        success: true,
      };
    } catch (error) {
      this.logger.error(`重启容器失败: ${error.message}`, error.stack);
      
      return {
        action: '重启容器',
        result: `错误: ${error.message}`,
        success: false,
      };
    }
  }

  /**
   * 执行命令
   */
  private async executeCommand(
    healthCheck: HealthCheckEntity,
    config: Record<string, any>,
  ): Promise<{
    action: string;
    result: string;
    success: boolean;
  }> {
    try {
      // 获取要执行的命令
      const command = config.command;
      
      if (!command) {
        throw new Error('未指定要执行的命令');
      }
      
      // 执行命令
      const { stdout, stderr } = await execAsync(command);
      
      return {
        action: '执行命令',
        result: `成功: ${stdout.trim()}`,
        success: true,
      };
    } catch (error) {
      this.logger.error(`执行命令失败: ${error.message}`, error.stack);
      
      return {
        action: '执行命令',
        result: `错误: ${error.message}`,
        success: false,
      };
    }
  }

  /**
   * 调用Webhook
   */
  private async callWebhook(
    healthCheck: HealthCheckEntity,
    config: Record<string, any>,
  ): Promise<{
    action: string;
    result: string;
    success: boolean;
  }> {
    try {
      // 获取Webhook URL
      const url = config.webhookUrl;
      
      if (!url) {
        throw new Error('未指定Webhook URL');
      }
      
      // 构建请求头
      const headers = config.headers || {
        'Content-Type': 'application/json',
      };
      
      // 构建请求体
      const payload = {
        event: 'auto_recovery',
        healthCheck: {
          id: healthCheck.id,
          name: healthCheck.name,
          serviceId: healthCheck.serviceId,
          serviceType: healthCheck.serviceType,
          instanceId: healthCheck.instanceId,
          hostname: healthCheck.hostname,
          status: healthCheck.status,
        },
        timestamp: new Date().toISOString(),
        ...config.additionalData,
      };
      
      // 发送请求
      const response = await firstValueFrom(
        this.httpService.post(url, payload, { headers })
      );
      
      if (response.status >= 200 && response.status < 300) {
        return {
          action: '调用Webhook',
          result: `成功: ${response.status} ${response.statusText}`,
          success: true,
        };
      } else {
        return {
          action: '调用Webhook',
          result: `失败: ${response.status} ${response.statusText}`,
          success: false,
        };
      }
    } catch (error) {
      this.logger.error(`调用Webhook失败: ${error.message}`, error.stack);
      
      return {
        action: '调用Webhook',
        result: `错误: ${error.message}`,
        success: false,
      };
    }
  }

  /**
   * 扩容
   */
  private async scaleUp(
    healthCheck: HealthCheckEntity,
    config: Record<string, any>,
  ): Promise<{
    action: string;
    result: string;
    success: boolean;
  }> {
    try {
      // 获取服务注册中心URL
      const serviceRegistryUrl = config.serviceRegistryUrl || 'http://localhost:3000';
      
      // 获取扩容参数
      const replicas = config.replicas || 1;
      
      // 调用服务注册中心的扩容API
      const response = await firstValueFrom(
        this.httpService.post(`${serviceRegistryUrl}/registry/services/${healthCheck.serviceId}/scale`, {
          replicas,
          reason: '自动恢复',
          triggeredBy: 'auto-recovery',
        })
      );
      
      if (response.status >= 200 && response.status < 300) {
        return {
          action: '扩容服务',
          result: `成功: ${response.data.message || `服务已扩容至${replicas}个实例`}`,
          success: true,
        };
      } else {
        return {
          action: '扩容服务',
          result: `失败: ${response.status} ${response.statusText}`,
          success: false,
        };
      }
    } catch (error) {
      this.logger.error(`扩容服务失败: ${error.message}`, error.stack);
      
      return {
        action: '扩容服务',
        result: `错误: ${error.message}`,
        success: false,
      };
    }
  }

  /**
   * 故障转移
   */
  private async failover(
    healthCheck: HealthCheckEntity,
    config: Record<string, any>,
  ): Promise<{
    action: string;
    result: string;
    success: boolean;
  }> {
    try {
      // 获取服务注册中心URL
      const serviceRegistryUrl = config.serviceRegistryUrl || 'http://localhost:3000';
      
      // 调用服务注册中心的故障转移API
      const response = await firstValueFrom(
        this.httpService.post(`${serviceRegistryUrl}/registry/services/${healthCheck.serviceId}/failover`, {
          instanceId: healthCheck.instanceId,
          reason: '自动恢复',
          triggeredBy: 'auto-recovery',
        })
      );
      
      if (response.status >= 200 && response.status < 300) {
        return {
          action: '故障转移',
          result: `成功: ${response.data.message || '故障转移请求已发送'}`,
          success: true,
        };
      } else {
        return {
          action: '故障转移',
          result: `失败: ${response.status} ${response.statusText}`,
          success: false,
        };
      }
    } catch (error) {
      this.logger.error(`故障转移失败: ${error.message}`, error.stack);
      
      return {
        action: '故障转移',
        result: `错误: ${error.message}`,
        success: false,
      };
    }
  }
}
