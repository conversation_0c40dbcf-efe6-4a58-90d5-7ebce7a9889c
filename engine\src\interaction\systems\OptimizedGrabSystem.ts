/**
 * 优化的抓取系统
 * 用于处理大量对象同时被抓取的情况
 */
import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { Vector3, Quaternion, Matrix4, Euler } from 'three';
import { InputSystem } from '../../input/InputSystem';
import { PhysicsSystem } from '../../physics/PhysicsSystem';
import { GrabbableComponent, GrabType, Hand } from '../components/GrabbableComponent';
import { GrabberComponent } from '../components/GrabberComponent';
import { GrabbedComponent } from '../components/GrabbedComponent';
import { ThrowableComponent } from '../components/ThrowableComponent';
import { GestureGrabComponent } from '../components/GestureGrabComponent';
import { XRGrabComponent } from '../components/XRGrabComponent';
import { GrabNetworkComponent } from '../components/GrabNetworkComponent';
import { Debug } from '../../utils/Debug';
import type { Transform } from '../../scene/Transform';
import { PhysicsBodyComponent   } from '../../physics/components/PhysicsBodyComponent';
import { BodyType } from '../../physics/types/BodyType';
import { ObjectPool } from '../../utils/ObjectPool';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 空间分区网格单元
 */
interface SpatialGridCell {
  /** 实体列表 */
  entities: Set<Entity>;
}

/**
 * 优化的抓取系统配置
 */
export interface OptimizedGrabSystemConfig {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 是否启用物理抓取 */
  enablePhysicsGrab?: boolean;
  /** 是否启用网络同步 */
  enableNetworkSync?: boolean;
  /** 是否启用手势抓取 */
  enableGestureGrab?: boolean;
  /** 是否启用XR抓取 */
  enableXRGrab?: boolean;
  /** 默认抓取类型 */
  defaultGrabType?: GrabType;
  /** 是否启用空间分区 */
  enableSpatialPartitioning?: boolean;
  /** 空间分区网格大小 */
  spatialGridSize?: number;
  /** 是否启用对象池 */
  enableObjectPool?: boolean;
  /** 是否启用多线程 */
  enableMultithreading?: boolean;
  /** 是否启用LOD */
  enableLOD?: boolean;
  /** 更新频率（帧数） */
  updateFrequency?: number;
}

/**
 * 优化的抓取系统
 */
export class OptimizedGrabSystem extends System {
  /** 系统名称 */
  public static readonly NAME: string = 'OptimizedGrabSystem';

  // 移除重复的world声明，使用基类的world属性

  /** 系统配置 */
  private config: Required<OptimizedGrabSystemConfig>;

  /** 输入系统引用 */
  private inputSystem?: InputSystem;

  /** 物理系统引用 */
  private physicsSystem?: PhysicsSystem;

  /** 可抓取组件映射 */
  private grabbableComponents: Map<Entity, GrabbableComponent> = new Map();

  /** 抓取者组件映射 */
  private grabberComponents: Map<Entity, GrabberComponent> = new Map();

  /** 被抓取组件映射 */
  private grabbedComponents: Map<Entity, GrabbedComponent> = new Map();

  /** 可抛掷组件映射 */
  private throwableComponents: Map<Entity, ThrowableComponent> = new Map();

  /** 手势抓取组件映射 */
  private gestureGrabComponents: Map<Entity, GestureGrabComponent> = new Map();

  /** XR抓取组件映射 */
  private xrGrabComponents: Map<Entity, XRGrabComponent> = new Map();

  /** 网络抓取组件映射 */
  private grabNetworkComponents: Map<Entity, GrabNetworkComponent> = new Map();

  /** 空间分区网格 */
  private spatialGrid: Map<string, SpatialGridCell> = new Map();

  /** 临时向量 - 用于计算 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private tempVector: Vector3 = new Vector3();

  /** 临时四元数 - 用于计算 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private tempQuaternion: Quaternion = new Quaternion();

  /** 临时矩阵 - 用于计算 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private tempMatrix: Matrix4 = new Matrix4();

  /** 原始物理体类型映射 - 用于恢复物理体类型 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private originalBodyTypes: Map<Entity, BodyType> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 帧计数器 */
  private frameCount: number = 0;

  /** 工作线程 */
  private worker?: Worker;

  /** 对象池 */
  private vectorPool?: ObjectPool<Vector3>;
  private quaternionPool?: ObjectPool<Quaternion>;
  private matrixPool?: ObjectPool<Matrix4>;

  /** LOD级别映射 */
  private lodLevels: Map<Entity, number> = new Map();

  /**
   * 构造函数
   * @param world 世界引用
   * @param config 系统配置
   */
  constructor(world: World, config: OptimizedGrabSystemConfig = {}) {
    super(100); // 设置优先级
    this.setWorld(world);
    this.config = {
      debug: config.debug !== undefined ? config.debug : false,
      enablePhysicsGrab: config.enablePhysicsGrab !== undefined ? config.enablePhysicsGrab : true,
      enableNetworkSync: config.enableNetworkSync !== undefined ? config.enableNetworkSync : true,
      enableGestureGrab: config.enableGestureGrab !== undefined ? config.enableGestureGrab : true,
      enableXRGrab: config.enableXRGrab !== undefined ? config.enableXRGrab : true,
      defaultGrabType: config.defaultGrabType || GrabType.DIRECT,
      enableSpatialPartitioning: config.enableSpatialPartitioning !== undefined ? config.enableSpatialPartitioning : true,
      spatialGridSize: config.spatialGridSize || 5.0,
      enableObjectPool: config.enableObjectPool !== undefined ? config.enableObjectPool : true,
      enableMultithreading: config.enableMultithreading !== undefined ? config.enableMultithreading : false,
      enableLOD: config.enableLOD !== undefined ? config.enableLOD : true,
      updateFrequency: config.updateFrequency || 1
    };

    // 初始化对象池
    if (this.config.enableObjectPool) {
      this.initObjectPools();
    }

    // 初始化多线程
    if (this.config.enableMultithreading) {
      this.initWorker();
    }
  }

  /**
   * 初始化系统
   */
  initialize(): void {
    // 获取输入系统
    this.inputSystem = this.world!.getSystem(InputSystem);
    if (!this.inputSystem && this.config.debug) {
      Debug.warn('OptimizedGrabSystem', 'InputSystem not found');
    }

    // 获取物理系统
    this.physicsSystem = this.world!.getSystem(PhysicsSystem);
    if (!this.physicsSystem && this.config.enablePhysicsGrab && this.config.debug) {
      Debug.warn('OptimizedGrabSystem', 'PhysicsSystem not found, physics grab will be disabled');
    }

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', 'Initialized');
    }
  }

  /**
   * 初始化对象池
   */
  private initObjectPools(): void {
    // 创建向量对象池
    this.vectorPool = new ObjectPool<Vector3>({
      create: () => new Vector3(),
      reset: (v) => v.set(0, 0, 0),
      initialCapacity: 100,
      maxCapacity: 1000
    });

    // 创建四元数对象池
    this.quaternionPool = new ObjectPool<Quaternion>({
      create: () => new Quaternion(),
      reset: (q) => q.set(0, 0, 0, 1),
      initialCapacity: 100,
      maxCapacity: 1000
    });

    // 创建矩阵对象池
    this.matrixPool = new ObjectPool<Matrix4>({
      create: () => new Matrix4(),
      reset: (m) => m.identity(),
      initialCapacity: 50,
      maxCapacity: 500
    });
  }

  /**
   * 初始化工作线程
   */
  private initWorker(): void {
    // 创建工作线程
    try {
      // 这里应该创建一个专门用于处理抓取计算的工作线程
      // 实际项目中应该实现具体的工作线程逻辑
      // this.worker = new Worker('path/to/grab-worker.js');
    } catch (error) {
      Debug.error('OptimizedGrabSystem', `Failed to create worker: ${error}`);
      this.config.enableMultithreading = false;
    }
  }

  /**
   * 注册可抓取组件
   * @param entity 实体
   * @param component 可抓取组件
   */
  registerGrabbableComponent(entity: Entity, component: GrabbableComponent): void {
    this.grabbableComponents.set(entity, component);

    // 如果启用了空间分区，将实体添加到空间网格
    if (this.config.enableSpatialPartitioning) {
      this.addEntityToSpatialGrid(entity);
    }

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', `Registered grabbable component for entity ${entity.id}`);
    }
  }

  /**
   * 注册抓取者组件
   * @param entity 实体
   * @param component 抓取者组件
   */
  registerGrabberComponent(entity: Entity, component: GrabberComponent): void {
    this.grabberComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', `Registered grabber component for entity ${entity.id}`);
    }
  }

  /**
   * 注册被抓取组件
   * @param entity 实体
   * @param component 被抓取组件
   */
  registerGrabbedComponent(entity: Entity, component: GrabbedComponent): void {
    this.grabbedComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', `Registered grabbed component for entity ${entity.id}`);
    }
  }

  /**
   * 注册可抛掷组件
   * @param entity 实体
   * @param component 可抛掷组件
   */
  registerThrowableComponent(entity: Entity, component: ThrowableComponent): void {
    this.throwableComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', `Registered throwable component for entity ${entity.id}`);
    }
  }

  /**
   * 注册手势抓取组件
   * @param entity 实体
   * @param component 手势抓取组件
   */
  registerGestureGrabComponent(entity: Entity, component: GestureGrabComponent): void {
    this.gestureGrabComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', `Registered gesture grab component for entity ${entity.id}`);
    }
  }

  /**
   * 注册XR抓取组件
   * @param entity 实体
   * @param component XR抓取组件
   */
  registerXRGrabComponent(entity: Entity, component: XRGrabComponent): void {
    this.xrGrabComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', `Registered XR grab component for entity ${entity.id}`);
    }
  }

  /**
   * 注册网络抓取组件
   * @param entity 实体
   * @param component 网络抓取组件
   */
  registerGrabNetworkComponent(entity: Entity, component: GrabNetworkComponent): void {
    this.grabNetworkComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', `Registered grab network component for entity ${entity.id}`);
    }
  }

  /**
   * 取消注册可抓取组件
   * @param entity 实体
   */
  unregisterGrabbableComponent(entity: Entity): void {
    this.grabbableComponents.delete(entity);

    // 如果启用了空间分区，从空间网格中移除实体
    if (this.config.enableSpatialPartitioning) {
      this.removeEntityFromSpatialGrid(entity);
    }

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', `Unregistered grabbable component for entity ${entity.id}`);
    }
  }

  /**
   * 取消注册抓取者组件
   * @param entity 实体
   */
  unregisterGrabberComponent(entity: Entity): void {
    this.grabberComponents.delete(entity);

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', `Unregistered grabber component for entity ${entity.id}`);
    }
  }

  /**
   * 取消注册被抓取组件
   * @param entity 实体
   */
  unregisterGrabbedComponent(entity: Entity): void {
    this.grabbedComponents.delete(entity);

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', `Unregistered grabbed component for entity ${entity.id}`);
    }
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量（秒）
   */
  update(_deltaTime: number): void {
    // 增加帧计数器
    this.frameCount++;

    // 根据更新频率决定是否更新
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }

    // 更新空间分区
    if (this.config.enableSpatialPartitioning) {
      this.updateSpatialGrid();
    }

    // 更新LOD级别
    if (this.config.enableLOD) {
      this.updateLODLevels();
    }

    // 处理输入
    this.handleInput();

    // 更新被抓取对象的位置和旋转
    this.updateGrabbedEntities();

    // 更新网络同步
    if (this.config.enableNetworkSync) {
      this.updateNetworkSync();
    }
  }

  /**
   * 处理输入
   */
  private handleInput(): void {
    // 如果没有输入系统，则返回
    if (!this.inputSystem) return;

    // 处理手势抓取组件
    if (this.config.enableGestureGrab) {
      this.updateGestureGrabComponents();
    }

    // 处理XR抓取组件
    if (this.config.enableXRGrab) {
      this.updateXRGrabComponents();
    }
  }

  /**
   * 更新被抓取实体
   */
  private updateGrabbedEntities(): void {
    // 使用多线程处理
    if (this.config.enableMultithreading && this.worker) {
      // 发送数据到工作线程
      // 实际项目中应该实现具体的数据传输逻辑
    } else {
      // 单线程处理
      for (const [entity, component] of this.grabbedComponents.entries()) {
        // 获取抓取者和抓取手
        const grabber = component.grabber;
        const hand = component.hand;
        if (!grabber) continue;

        // 获取可抓取组件
        const grabbableComponent = this.grabbableComponents.get(entity);
        if (!grabbableComponent) continue;

        // 根据抓取类型更新位置和旋转
        switch (grabbableComponent.grabType) {
          case GrabType.DIRECT:
            this.updateDirectGrab(entity, grabber, hand);
            break;
          case GrabType.DISTANCE:
            this.updateDistanceGrab(entity, grabber, hand);
            break;
          case GrabType.SPRING:
            this.updateSpringGrab(entity, grabber, hand);
            break;
        }
      }
    }
  }

  /**
   * 更新直接抓取
   * @param entity 被抓取实体
   * @param grabber 抓取者
   * @param hand 抓取手
   */
  private updateDirectGrab(entity: Entity, grabber: Entity, hand: Hand): void {
    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) return;

    // 获取实体的变换组件
    const entityTransform = entity.getComponent<Transform>('Transform');
    if (!entityTransform) return;

    // 计算手的位置和旋转
    const handPosition = this.getHandPosition(grabber, hand);
    const handRotation = this.getHandRotation(grabber, hand);

    // 设置实体的位置和旋转
    entityTransform.setWorldPosition(handPosition);
    // 将四元数转换为欧拉角
    const euler = new Euler().setFromQuaternion(handRotation);
    entityTransform.setWorldRotation(euler);
  }

  /**
   * 更新距离抓取
   * @param entity 被抓取实体
   * @param grabber 抓取者
   * @param hand 抓取手
   */
  private updateDistanceGrab(entity: Entity, grabber: Entity, hand: Hand): void {
    // 获取可抓取组件
    const grabbableComponent = this.grabbableComponents.get(entity);
    if (!grabbableComponent) return;

    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) return;

    // 获取实体的变换组件
    const entityTransform = entity.getComponent<Transform>('Transform');
    if (!entityTransform) return;

    // 计算手的位置和旋转
    const handPosition = this.getHandPosition(grabber, hand);
    const handRotation = this.getHandRotation(grabber, hand);

    // 计算抓取方向
    const grabDirection = this.getGrabDirection(grabber, hand);

    // 计算目标位置（在抓取方向上的一定距离）
    const targetPosition = new Vector3().copy(handPosition).add(
      grabDirection.multiplyScalar(grabbableComponent.grabDistance)
    );

    // 设置实体的位置和旋转
    entityTransform.setWorldPosition(targetPosition);
    // 将四元数转换为欧拉角
    const euler = new Euler().setFromQuaternion(handRotation);
    entityTransform.setWorldRotation(euler);
  }

  /**
   * 更新弹簧抓取
   * @param entity 被抓取实体
   * @param grabber 抓取者
   * @param hand 抓取手
   */
  private updateSpringGrab(entity: Entity, grabber: Entity, hand: Hand): void {
    // 如果没有物理系统，则使用直接抓取
    if (!this.physicsSystem) {
      this.updateDirectGrab(entity, grabber, hand);
      return;
    }

    // 获取物理体组件
    const physicsBody = entity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.type);
    if (!physicsBody) {
      this.updateDirectGrab(entity, grabber, hand);
      return;
    }

    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) return;

    // 计算手的位置
    const handPosition = this.getHandPosition(grabber, hand);

    // 获取当前位置
    const entityTransform = entity.getComponent<Transform>('Transform');
    if (!entityTransform) return;
    const currentPosition = entityTransform.getPosition();

    // 计算方向和距离
    const direction = new Vector3().subVectors(handPosition, currentPosition);
    const distance = direction.length();
    direction.normalize();

    // 应用弹簧力
    const springStrength = 10.0; // 弹簧强度
    const force = direction.multiplyScalar(distance * springStrength);
    physicsBody.applyForce(force);
  }

  /**
   * 获取手的位置
   * @param grabber 抓取者
   * @param hand 抓取手
   * @returns 手的位置
   */
  private getHandPosition(grabber: Entity, hand: Hand): Vector3 {
    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) return new Vector3();

    // 获取世界位置
    const position = grabberTransform.getWorldPosition();

    // 根据手的类型调整位置
    if (hand === Hand.LEFT) {
      // 左手位置（相对于抓取者）
      position.x -= 0.2;
    } else if (hand === Hand.RIGHT) {
      // 右手位置（相对于抓取者）
      position.x += 0.2;
    }

    return position;
  }

  /**
   * 获取手的旋转
   * @param grabber 抓取者
   * @param hand 抓取手
   * @returns 手的旋转
   */
  private getHandRotation(grabber: Entity, hand: Hand): Quaternion {
    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) return new Quaternion();

    // 获取世界旋转四元数
    const rotation = grabberTransform.getWorldRotationQuaternion();

    // 根据手的类型调整旋转
    if (hand === Hand.LEFT) {
      // 左手旋转（相对于抓取者）
      // 可以在这里添加特定的旋转调整
    } else if (hand === Hand.RIGHT) {
      // 右手旋转（相对于抓取者）
      // 可以在这里添加特定的旋转调整
    }

    return rotation;
  }

  /**
   * 获取抓取方向
   * @param grabber 抓取者
   * @param hand 抓取手
   * @returns 抓取方向
   */
  private getGrabDirection(grabber: Entity, _hand: Hand): Vector3 {
    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent<Transform>('Transform');
    if (!grabberTransform) return new Vector3(0, 0, -1);

    // 计算前方向
    const direction = new Vector3(0, 0, -1);
    direction.applyQuaternion(grabberTransform.getWorldRotationQuaternion());

    return direction;
  }

  /**
   * 添加实体到空间网格
   * @param entity 实体
   */
  private addEntityToSpatialGrid(entity: Entity): void {
    // 获取实体的变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) return;

    // 计算网格坐标
    const position = transform.getWorldPosition();
    const gridCoords = this.getGridCoordinates(position);
    const gridKey = this.getGridKey(gridCoords.x, gridCoords.y, gridCoords.z);

    // 获取或创建网格单元
    let cell = this.spatialGrid.get(gridKey);
    if (!cell) {
      cell = { entities: new Set<Entity>() };
      this.spatialGrid.set(gridKey, cell);
    }

    // 添加实体到网格单元
    cell.entities.add(entity);
  }

  /**
   * 从空间网格中移除实体
   * @param entity 实体
   */
  private removeEntityFromSpatialGrid(entity: Entity): void {
    // 遍历所有网格单元
    for (const [gridKey, cell] of this.spatialGrid.entries()) {
      // 如果单元包含实体，则移除
      if (cell.entities.has(entity)) {
        cell.entities.delete(entity);

        // 如果单元为空，则移除单元
        if (cell.entities.size === 0) {
          this.spatialGrid.delete(gridKey);
        }
      }
    }
  }

  /**
   * 更新空间网格
   */
  private updateSpatialGrid(): void {
    // 更新所有可抓取实体的位置
    for (const [entity] of this.grabbableComponents.entries()) {
      // 获取实体的变换组件
      const transform = entity.getComponent<Transform>('Transform');
      if (!transform) continue;

      // 移除实体从旧的网格单元
      this.removeEntityFromSpatialGrid(entity);

      // 添加实体到新的网格单元
      this.addEntityToSpatialGrid(entity);
    }
  }

  /**
   * 获取网格坐标
   * @param position 位置
   * @returns 网格坐标
   */
  private getGridCoordinates(position: Vector3): { x: number, y: number, z: number } {
    const gridSize = this.config.spatialGridSize;
    return {
      x: Math.floor(position.x / gridSize),
      y: Math.floor(position.y / gridSize),
      z: Math.floor(position.z / gridSize)
    };
  }

  /**
   * 获取网格键
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @returns 网格键
   */
  private getGridKey(x: number, y: number, z: number): string {
    return `${x},${y},${z}`;
  }

  /**
   * 更新LOD级别
   */
  private updateLODLevels(): void {
    // 如果没有启用LOD，则返回
    if (!this.config.enableLOD) return;

    // 获取相机位置
    const cameraPosition = this.getCameraPosition();
    if (!cameraPosition) return;

    // 更新所有可抓取实体的LOD级别
    for (const [entity] of this.grabbableComponents.entries()) {
      // 获取实体的变换组件
      const transform = entity.getComponent<Transform>('Transform');
      if (!transform) continue;

      // 计算到相机的距离
      const position = transform.getWorldPosition();
      const distance = position.distanceTo(cameraPosition);

      // 根据距离设置LOD级别
      let lodLevel = 0;
      if (distance > 50) {
        lodLevel = 2; // 远距离 - 低细节
      } else if (distance > 20) {
        lodLevel = 1; // 中距离 - 中细节
      }

      // 更新LOD级别
      this.lodLevels.set(entity, lodLevel);
    }
  }

  /**
   * 获取相机位置
   * @returns 相机位置
   */
  private getCameraPosition(): Vector3 | null {
    // 这里应该获取当前活动相机的位置
    // 简单起见，这里返回null，实际项目中应该实现具体的获取逻辑
    return null;
  }

  /**
   * 更新手势抓取组件
   */
  private updateGestureGrabComponents(): void {
    // 如果没有启用手势抓取，则返回
    if (!this.config.enableGestureGrab) return;

    // 更新所有手势抓取组件
    for (const [, component] of this.gestureGrabComponents.entries()) {
      component.update(0); // 传入0作为deltaTime，实际应该传入真实的deltaTime
    }
  }

  /**
   * 更新XR抓取组件
   */
  private updateXRGrabComponents(): void {
    // 如果没有启用XR抓取，则返回
    if (!this.config.enableXRGrab) return;

    // 更新所有XR抓取组件
    // 注意：XR抓取组件的更新通常在XR帧回调中进行，这里只是示例
    for (const [,] of this.xrGrabComponents.entries()) {
      // 这里应该在XR帧回调中调用component.updateXR(frame, referenceSpace)
    }
  }

  /**
   * 更新网络同步
   */
  private updateNetworkSync(): void {
    // 如果没有启用网络同步，则返回
    if (!this.config.enableNetworkSync) return;

    // 更新所有网络抓取组件
    for (const [, component] of this.grabNetworkComponents.entries()) {
      component.update(0); // 传入0作为deltaTime，实际应该传入真实的deltaTime
    }
  }

  /**
   * 销毁系统
   */
  destroy(): void {
    // 清空组件映射
    this.grabbableComponents.clear();
    this.grabberComponents.clear();
    this.grabbedComponents.clear();
    this.throwableComponents.clear();
    this.gestureGrabComponents.clear();
    this.xrGrabComponents.clear();
    this.grabNetworkComponents.clear();

    // 清空空间网格
    this.spatialGrid.clear();

    // 清空LOD级别映射
    this.lodLevels.clear();

    // 终止工作线程
    if (this.worker) {
      this.worker.terminate();
      this.worker = undefined;
    }

    // 清空对象池
    if (this.vectorPool) {
      this.vectorPool.clear();
    }
    if (this.quaternionPool) {
      this.quaternionPool.clear();
    }
    if (this.matrixPool) {
      this.matrixPool.clear();
    }

    // 清空事件监听器
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      Debug.log('OptimizedGrabSystem', 'Destroyed');
    }
  }
}
