/**
 * 负载均衡模块
 */
import { Module } from '@nestjs/common';
import { LoadBalancerService } from './load-balancer.service';
import { RandomLoadBalancerStrategy } from './random.strategy';
import { RoundRobinLoadBalancerStrategy } from './round-robin.strategy';
import { WeightedRoundRobinLoadBalancerStrategy } from './weighted-round-robin.strategy';
import { LeastResponseTimeLoadBalancerStrategy } from './least-response-time.strategy';
import { ConsistentHashLoadBalancerStrategy } from './consistent-hash.strategy';
import { ZoneAwareLoadBalancerStrategy } from './zone-aware.strategy';

@Module({
  providers: [
    LoadBalancerService,
    RandomLoadBalancerStrategy,
    RoundRobinLoadBalancerStrategy,
    WeightedRoundRobinLoadBalancerStrategy,
    LeastResponseTimeLoadBalancerStrategy,
    ConsistentHashLoadBalancerStrategy,
    ZoneAwareLoadBalancerStrategy,
  ],
  exports: [
    LoadBalancerService,
  ],
})
export class LoadBalancerModule {}
