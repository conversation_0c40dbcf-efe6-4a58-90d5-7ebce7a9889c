/**
 * 地下环境光照系统
 * 用于模拟地下环境的光照效果，包括洞穴光照、钟乳石反射光、水面反射光等
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { World } from '../../core/World';
/**
 * 地下环境光照系统配置
 */
export interface UndergroundLightingSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用洞穴光照 */
    enableCaveLighting?: boolean;
    /** 是否启用钟乳石反射光 */
    enableStalactiteReflection?: boolean;
    /** 是否启用水面反射光 */
    enableWaterReflection?: boolean;
    /** 是否启用体积光 */
    enableVolumetricLight?: boolean;
    /** 是否启用体积雾 */
    enableVolumetricFog?: boolean;
    /** 是否启用调试可视化 */
    enableDebugVisualization?: boolean;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
}
/**
 * 洞穴光照配置
 */
export interface CaveLightingConfig {
    /** 位置 */
    position: THREE.Vector3;
    /** 大小 */
    size: THREE.Vector3;
    /** 强度 */
    intensity: number;
    /** 颜色 */
    color: THREE.Color;
    /** 衰减 */
    decay: number;
    /** 是否产生阴影 */
    castShadow: boolean;
}
/**
 * 钟乳石反射光配置
 */
export interface StalactiteReflectionConfig {
    /** 位置 */
    position: THREE.Vector3;
    /** 大小 */
    size: number;
    /** 强度 */
    intensity: number;
    /** 颜色 */
    color: THREE.Color;
    /** 闪烁频率 */
    flickerFrequency: number;
    /** 闪烁强度 */
    flickerIntensity: number;
}
/**
 * 水面反射光配置
 */
export interface WaterReflectionConfig {
    /** 水面位置 */
    position: THREE.Vector3;
    /** 水面大小 */
    size: {
        width: number;
        depth: number;
    };
    /** 反射强度 */
    intensity: number;
    /** 反射颜色 */
    color: THREE.Color;
    /** 波动频率 */
    waveFrequency: number;
    /** 波动强度 */
    waveIntensity: number;
}
/**
 * 地下环境光照系统
 */
export declare class UndergroundLightingSystem extends System {
    /** 配置 */
    private config;
    /** 光照探针系统 */
    private lightProbeSystem;
    /** 洞穴光照列表 */
    private caveLights;
    /** 钟乳石反射光列表 */
    private stalactiteReflections;
    /** 水面反射光列表 */
    private waterReflections;
    /** 帧计数器 */
    private frameCount;
    /** 性能监视器 */
    private performanceMonitor;
    /** 调试渲染器 */
    private debugRenderer;
    /** 时间 */
    private time;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: UndergroundLightingSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 创建调试渲染器
     */
    private createDebugRenderer;
    /**
     * 更新调试渲染器
     */
    private updateDebugRenderer;
    /**
     * 添加洞穴光照
     * @param id 光照ID
     * @param config 洞穴光照配置
     * @returns 光照ID
     */
    addCaveLight(id: string, config: CaveLightingConfig): string;
    /**
     * 移除洞穴光照
     * @param id 光照ID
     */
    removeCaveLight(id: string): void;
    /**
     * 更新洞穴光照
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateCaveLights;
    /**
     * 添加钟乳石反射光
     * @param id 光照ID
     * @param config 钟乳石反射光配置
     * @returns 光照ID
     */
    addStalactiteReflection(id: string, config: StalactiteReflectionConfig): string;
    /**
     * 移除钟乳石反射光
     * @param id 光照ID
     */
    removeStalactiteReflection(id: string): void;
    /**
     * 更新钟乳石反射光
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateStalactiteReflections;
    /**
     * 添加水面反射光
     * @param id 光照ID
     * @param config 水面反射光配置
     * @returns 光照ID
     */
    addWaterReflection(id: string, config: WaterReflectionConfig): string;
    /**
     * 移除水面反射光
     * @param id 光照ID
     */
    removeWaterReflection(id: string): void;
    /**
     * 更新水面反射光
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterReflections;
}
