/**
 * 区域光
 * 物理精确的区域光基类
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
/**
 * 区域光类型枚举
 */
export declare enum AreaLightType {
    RECT = "rect",
    SPHERE = "sphere",
    DISK = "disk",
    TUBE = "tube"
}
/**
 * 区域光基础选项接口
 */
export interface AreaLightOptions {
    /** 光源类型 */
    type: AreaLightType;
    /** 光源颜色 */
    color?: THREE.ColorRepresentation;
    /** 光源强度 */
    intensity?: number;
    /** 是否投射阴影 */
    castShadow?: boolean;
    /** 是否显示辅助对象 */
    showHelper?: boolean;
    /** 辅助对象颜色 */
    helperColor?: THREE.ColorRepresentation;
}
/**
 * 区域光组件基类
 */
export declare abstract class AreaLight extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 光源类型 */
    protected lightType: AreaLightType;
    /** Three.js光源 */
    protected light: THREE.Light | THREE.Group;
    /** 辅助对象 */
    protected helper: THREE.Object3D | null;
    /** 是否显示辅助对象 */
    protected showHelper: boolean;
    /** 辅助对象颜色 */
    protected helperColor: THREE.Color;
    /**
     * 创建区域光组件
     * @param options 区域光选项
     */
    constructor(options: AreaLightOptions);
    /**
     * 创建光源
     * @param options 区域光选项
     * @returns Three.js光源
     */
    protected abstract createLight(options: AreaLightOptions): THREE.Light | THREE.Group;
    /**
     * 创建辅助对象
     * @returns Three.js辅助对象
     */
    protected abstract createHelper(): THREE.Object3D;
    /**
     * 当组件附加到实体时调用
     */
    protected onAttach(): void;
    /**
     * 当组件从实体分离时调用
     */
    protected onDetach(): void;
    /**
     * 获取光源类型
     * @returns 光源类型
     */
    getType(): AreaLightType;
    /**
     * 获取Three.js光源
     * @returns Three.js光源
     */
    getThreeLight(): THREE.Light | THREE.Group;
    /**
     * 获取辅助对象
     * @returns Three.js辅助对象
     */
    getHelper(): THREE.Object3D | null;
    /**
     * 设置是否显示辅助对象
     * @param show 是否显示
     */
    setShowHelper(show: boolean): void;
    /**
     * 设置辅助对象颜色
     * @param color 颜色
     */
    setHelperColor(color: THREE.ColorRepresentation): void;
    /**
     * 更新辅助对象颜色
     */
    protected abstract updateHelperColor(): void;
    /**
     * 设置光源颜色
     * @param color 颜色
     */
    setColor(color: THREE.ColorRepresentation): void;
    /**
     * 设置光源强度
     * @param intensity 强度
     */
    setIntensity(intensity: number): void;
    /**
     * 设置是否投射阴影
     * @param castShadow 是否投射阴影
     */
    setCastShadow(castShadow: boolean): void;
    /**
     * 更新组件
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
}
