/**
 * FacialAnimationSystem类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import { FacialAnimationSystem } from '../../src/avatar/facial/FacialAnimationSystem';
import { FacialAnimationComponent } from '../../src/avatar/facial/FacialAnimationComponent';
import { LipSyncComponent } from '../../src/avatar/facial/LipSyncComponent';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { AvatarComponent } from '../../src/avatar/AvatarComponent';
import { AudioSystem } from '../../src/audio/AudioSystem';

describe('FacialAnimationSystem', () => {
  let engine: Engine;
  let world: World;
  let audioSystem: AudioSystem;
  let facialAnimationSystem: FacialAnimationSystem;
  let entity: Entity;
  let avatarComponent: AvatarComponent;
  let facialAnimationComponent: FacialAnimationComponent;
  let lipSyncComponent: LipSyncComponent;
  
  // 在每个测试前创建一个新的面部动画系统实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建音频系统
    audioSystem = new AudioSystem();
    
    // 添加音频系统到引擎
    engine.addSystem(audioSystem);
    
    // 创建面部动画系统
    facialAnimationSystem = new FacialAnimationSystem();
    
    // 添加面部动画系统到引擎
    engine.addSystem(facialAnimationSystem);
    
    // 创建实体
    entity = new Entity(world);
    entity.name = '头像实体';
    
    // 添加变换组件
    const transform = new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity.addComponent(transform);
    
    // 创建头像组件
    avatarComponent = new AvatarComponent(entity, {
      modelUrl: 'models/avatar.glb'
    });
    entity.addComponent(avatarComponent);
    
    // 创建面部动画组件
    facialAnimationComponent = new FacialAnimationComponent(entity, {
      blendShapeMapping: {
        browDownLeft: 'browDown_L',
        browDownRight: 'browDown_R',
        browUpLeft: 'browUp_L',
        browUpRight: 'browUp_R',
        eyeCloseLeft: 'eyeClose_L',
        eyeCloseRight: 'eyeClose_R',
        mouthOpen: 'mouthOpen',
        mouthSmile: 'mouthSmile',
        mouthFrown: 'mouthFrown'
      }
    });
    entity.addComponent(facialAnimationComponent);
    
    // 创建口型同步组件
    lipSyncComponent = new LipSyncComponent(entity, {
      audioUrl: 'audio/speech.mp3',
      visemeMapping: {
        A: 'viseme_A',
        B: 'viseme_B',
        C: 'viseme_C',
        D: 'viseme_D',
        E: 'viseme_E',
        F: 'viseme_F',
        G: 'viseme_G',
        H: 'viseme_H'
      }
    });
    entity.addComponent(lipSyncComponent);
    
    // 添加实体到世界
    world.addEntity(entity);
    
    // 注册面部动画组件
    facialAnimationSystem.registerFacialAnimationComponent(entity, facialAnimationComponent);
    
    // 注册口型同步组件
    facialAnimationSystem.registerLipSyncComponent(entity, lipSyncComponent);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试面部动画系统初始化
  it('应该正确初始化面部动画系统', () => {
    expect(facialAnimationSystem).toBeDefined();
    expect(facialAnimationSystem['facialAnimations'].size).toBe(1);
    expect(facialAnimationSystem['facialAnimations'].get(entity.getId())).toBe(facialAnimationComponent);
    expect(facialAnimationSystem['lipSyncs'].size).toBe(1);
    expect(facialAnimationSystem['lipSyncs'].get(entity.getId())).toBe(lipSyncComponent);
  });
  
  // 测试注册和取消注册面部动画组件
  it('应该能够注册和取消注册面部动画组件', () => {
    // 创建另一个实体
    const entity2 = new Entity(world);
    entity2.name = '头像实体2';
    
    // 添加变换组件
    const transform2 = new Transform({
      position: { x: 5, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity2.addComponent(transform2);
    
    // 创建面部动画组件
    const facialAnimationComponent2 = new FacialAnimationComponent(entity2, {
      blendShapeMapping: {
        browDownLeft: 'browDown_L',
        browDownRight: 'browDown_R'
      }
    });
    entity2.addComponent(facialAnimationComponent2);
    
    // 添加实体到世界
    world.addEntity(entity2);
    
    // 注册面部动画组件
    facialAnimationSystem.registerFacialAnimationComponent(entity2, facialAnimationComponent2);
    
    // 验证组件已注册
    expect(facialAnimationSystem['facialAnimations'].has(entity2.getId())).toBe(true);
    expect(facialAnimationSystem['facialAnimations'].get(entity2.getId())).toBe(facialAnimationComponent2);
    
    // 取消注册面部动画组件
    facialAnimationSystem.unregisterFacialAnimationComponent(entity2);
    
    // 验证组件已取消注册
    expect(facialAnimationSystem['facialAnimations'].has(entity2.getId())).toBe(false);
  });
  
  // 测试设置混合形状权重
  it('应该能够设置混合形状权重', () => {
    // 模拟头像模型
    const mockMesh = new THREE.Mesh(
      new THREE.BufferGeometry(),
      new THREE.MeshStandardMaterial()
    );
    
    // 添加混合形状
    mockMesh.morphTargetDictionary = {
      'browDown_L': 0,
      'browDown_R': 1,
      'browUp_L': 2,
      'browUp_R': 3,
      'eyeClose_L': 4,
      'eyeClose_R': 5,
      'mouthOpen': 6,
      'mouthSmile': 7,
      'mouthFrown': 8
    };
    
    mockMesh.morphTargetInfluences = [0, 0, 0, 0, 0, 0, 0, 0, 0];
    
    // 设置头像模型
    (avatarComponent as any).model = mockMesh;
    (facialAnimationComponent as any).mesh = mockMesh;
    
    // 设置混合形状权重
    facialAnimationComponent.setBlendShapeWeight('browDownLeft', 0.5);
    facialAnimationComponent.setBlendShapeWeight('mouthOpen', 0.8);
    
    // 更新面部动画系统
    facialAnimationSystem.update(0.016);
    
    // 验证混合形状权重已设置
    expect(mockMesh.morphTargetInfluences[0]).toBe(0.5); // browDown_L
    expect(mockMesh.morphTargetInfluences[6]).toBe(0.8); // mouthOpen
  });
  
  // 测试播放面部动画
  it('应该能够播放面部动画', () => {
    // 模拟头像模型
    const mockMesh = new THREE.Mesh(
      new THREE.BufferGeometry(),
      new THREE.MeshStandardMaterial()
    );
    
    // 添加混合形状
    mockMesh.morphTargetDictionary = {
      'browDown_L': 0,
      'browDown_R': 1,
      'browUp_L': 2,
      'browUp_R': 3,
      'eyeClose_L': 4,
      'eyeClose_R': 5,
      'mouthOpen': 6,
      'mouthSmile': 7,
      'mouthFrown': 8
    };
    
    mockMesh.morphTargetInfluences = [0, 0, 0, 0, 0, 0, 0, 0, 0];
    
    // 设置头像模型
    (avatarComponent as any).model = mockMesh;
    (facialAnimationComponent as any).mesh = mockMesh;
    
    // 创建面部动画
    const animation = {
      name: 'smile',
      duration: 1.0,
      loop: false,
      keyframes: [
        {
          time: 0.0,
          blendShapes: {
            mouthSmile: 0.0
          }
        },
        {
          time: 0.5,
          blendShapes: {
            mouthSmile: 1.0
          }
        },
        {
          time: 1.0,
          blendShapes: {
            mouthSmile: 0.0
          }
        }
      ]
    };
    
    // 添加面部动画
    facialAnimationComponent.addAnimation('smile', animation);
    
    // 播放面部动画
    facialAnimationComponent.playAnimation('smile');
    
    // 更新面部动画系统 - 0.5秒后
    (facialAnimationComponent as any).animationTime = 0.5;
    facialAnimationSystem.update(0.016);
    
    // 验证混合形状权重已设置
    expect(mockMesh.morphTargetInfluences[7]).toBe(1.0); // mouthSmile
  });
  
  // 测试口型同步
  it('应该能够进行口型同步', () => {
    // 模拟头像模型
    const mockMesh = new THREE.Mesh(
      new THREE.BufferGeometry(),
      new THREE.MeshStandardMaterial()
    );
    
    // 添加混合形状
    mockMesh.morphTargetDictionary = {
      'viseme_A': 0,
      'viseme_B': 1,
      'viseme_C': 2,
      'viseme_D': 3,
      'viseme_E': 4,
      'viseme_F': 5,
      'viseme_G': 6,
      'viseme_H': 7
    };
    
    mockMesh.morphTargetInfluences = [0, 0, 0, 0, 0, 0, 0, 0];
    
    // 设置头像模型
    (avatarComponent as any).model = mockMesh;
    (lipSyncComponent as any).mesh = mockMesh;
    
    // 模拟音频分析结果
    const visemes = [
      { viseme: 'A', weight: 0.8, time: 0.0 },
      { viseme: 'B', weight: 0.6, time: 0.1 },
      { viseme: 'C', weight: 0.4, time: 0.2 }
    ];
    
    // 设置音频分析结果
    (lipSyncComponent as any).visemes = visemes;
    (lipSyncComponent as any).currentTime = 0.0;
    (lipSyncComponent as any).isPlaying = true;
    
    // 更新面部动画系统
    facialAnimationSystem.update(0.016);
    
    // 验证混合形状权重已设置
    expect(mockMesh.morphTargetInfluences[0]).toBe(0.8); // viseme_A
  });
  
  // 测试面部表情混合
  it('应该能够混合面部表情', () => {
    // 模拟头像模型
    const mockMesh = new THREE.Mesh(
      new THREE.BufferGeometry(),
      new THREE.MeshStandardMaterial()
    );
    
    // 添加混合形状
    mockMesh.morphTargetDictionary = {
      'browDown_L': 0,
      'browDown_R': 1,
      'mouthOpen': 2,
      'mouthSmile': 3,
      'viseme_A': 4
    };
    
    mockMesh.morphTargetInfluences = [0, 0, 0, 0, 0];
    
    // 设置头像模型
    (avatarComponent as any).model = mockMesh;
    (facialAnimationComponent as any).mesh = mockMesh;
    (lipSyncComponent as any).mesh = mockMesh;
    
    // 设置面部表情
    facialAnimationComponent.setBlendShapeWeight('browDownLeft', 0.5);
    facialAnimationComponent.setBlendShapeWeight('mouthSmile', 0.7);
    
    // 模拟音频分析结果
    const visemes = [
      { viseme: 'A', weight: 0.8, time: 0.0 }
    ];
    
    // 设置音频分析结果
    (lipSyncComponent as any).visemes = visemes;
    (lipSyncComponent as any).currentTime = 0.0;
    (lipSyncComponent as any).isPlaying = true;
    
    // 更新面部动画系统
    facialAnimationSystem.update(0.016);
    
    // 验证混合形状权重已设置
    expect(mockMesh.morphTargetInfluences[0]).toBe(0.5); // browDown_L
    expect(mockMesh.morphTargetInfluences[3]).toBe(0.7); // mouthSmile
    expect(mockMesh.morphTargetInfluences[4]).toBe(0.8); // viseme_A
  });
});
