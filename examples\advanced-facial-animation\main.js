/**
 * 高级面部动画示例
 * 主脚本文件
 */
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { RenderSystem } from '../../engine/src/rendering/RenderSystem';
import { FacialAnimationSystem } from '../../engine/src/avatar/systems/FacialAnimationSystem';
import { AIAnimationSynthesisSystem } from '../../engine/src/avatar/systems/AIAnimationSynthesisSystem';
import { AdvancedEmotionBasedAnimationGenerator } from '../../engine/src/avatar/ai/AdvancedEmotionBasedAnimationGenerator';
import { AIModelType } from '../../engine/src/ai/AIModelType';
import { loadModel } from '../../engine/src/utils/ModelLoader';

// 创建世界
const world = new World();

// 添加渲染系统
const renderSystem = new RenderSystem(world);
world.addSystem(renderSystem);

// 添加面部动画系统
const facialAnimationSystem = new FacialAnimationSystem(world, {
  debug: false,
  autoDetectAudio: true
});
world.addSystem(facialAnimationSystem);

// 添加AI动画合成系统
const aiAnimationSystem = new AIAnimationSynthesisSystem(world, {
  debug: false,
  useLocalModel: false
});
world.addSystem(aiAnimationSystem);

// 创建高级情感动画生成器
const advancedGenerator = new AdvancedEmotionBasedAnimationGenerator({
  debug: false,
  useLocalModel: false,
  enableExpressionBlending: true,
  enableMicroExpressions: true,
  enableEmotionTransitions: true,
  enableNaturalVariation: true
});

// 初始化生成器
advancedGenerator.initialize().then(() => {
  console.log('高级情感动画生成器初始化成功');
}).catch(error => {
  console.error('高级情感动画生成器初始化失败:', error);
});

// 创建角色实体
const characterEntity = world.createEntity();

// 加载角色模型
let characterModel = null;
let facialAnimation = null;

// 加载模型
async function loadCharacterModel() {
  try {
    // 显示加载中
    document.getElementById('basic-loading').style.display = 'block';
    document.getElementById('advanced-loading').style.display = 'block';
    
    // 加载模型
    characterModel = await loadModel('../../assets/models/character/character.glb');
    
    // 添加到场景
    renderSystem.addToScene(characterModel);
    
    // 创建面部动画组件
    facialAnimation = facialAnimationSystem.createFacialAnimation(characterEntity);
    
    // 将面部动画组件与模型绑定
    facialAnimationSystem.linkToModel(characterEntity, characterModel);
    
    // 隐藏加载中
    document.getElementById('basic-loading').style.display = 'none';
    document.getElementById('advanced-loading').style.display = 'none';
    
    console.log('角色模型加载成功');
  } catch (error) {
    console.error('加载角色模型失败:', error);
    
    // 显示错误信息
    document.getElementById('basic-loading').innerHTML = '<p>加载模型失败</p>';
    document.getElementById('advanced-loading').innerHTML = '<p>加载模型失败</p>';
  }
}

// 生成基础面部动画
async function generateBasicFacialAnimation() {
  // 获取输入
  const prompt = document.getElementById('basic-prompt').value;
  const duration = parseFloat(document.getElementById('basic-duration').value);
  const loop = document.getElementById('basic-loop').checked;
  
  // 验证输入
  if (!prompt) {
    alert('请输入情感描述');
    return;
  }
  
  try {
    // 显示加载中
    document.getElementById('basic-loading').style.display = 'block';
    
    // 生成动画
    const result = await aiAnimationSystem.generateFacialAnimation(
      characterEntity,
      prompt,
      duration,
      {
        loop,
        style: 'natural',
        intensity: 0.8
      }
    );
    
    // 隐藏加载中
    document.getElementById('basic-loading').style.display = 'none';
    
    // 显示结果
    if (result.success) {
      console.log('基础面部动画生成成功');
      
      // 显示情感分析结果
      displayEmotionResults('basic-emotion-results', result.emotionResult);
    } else {
      console.error('基础面部动画生成失败:', result.error);
      alert('生成动画失败: ' + result.error);
    }
  } catch (error) {
    console.error('生成基础面部动画失败:', error);
    alert('生成动画失败: ' + error.message);
    
    // 隐藏加载中
    document.getElementById('basic-loading').style.display = 'none';
  }
}

// 生成高级面部动画
async function generateAdvancedFacialAnimation() {
  // 获取输入
  const prompt = document.getElementById('advanced-prompt').value;
  const duration = parseFloat(document.getElementById('advanced-duration').value);
  const style = document.getElementById('advanced-style').value;
  const intensity = parseFloat(document.getElementById('advanced-intensity').value);
  const loop = document.getElementById('advanced-loop').checked;
  const enableMicroExpressions = document.getElementById('advanced-micro-expressions').checked;
  const modelType = document.getElementById('advanced-model-type').value;
  const seed = document.getElementById('advanced-seed').value ? parseInt(document.getElementById('advanced-seed').value) : undefined;
  
  // 验证输入
  if (!prompt) {
    alert('请输入情感描述');
    return;
  }
  
  try {
    // 显示加载中
    document.getElementById('advanced-loading').style.display = 'block';
    
    // 创建请求
    const request = {
      id: `animation-${Date.now()}`,
      prompt,
      duration,
      loop,
      style,
      intensity,
      enableMicroExpressions,
      seed,
      includeSecondary: true,
      includeChanges: true,
      detail: 'high'
    };
    
    // 设置模型类型
    advancedGenerator.config.modelType = modelType;
    
    // 生成动画
    const result = await advancedGenerator.generateAnimation(request);
    
    // 隐藏加载中
    document.getElementById('advanced-loading').style.display = 'none';
    
    // 显示结果
    if (result.success) {
      console.log('高级面部动画生成成功');
      
      // 应用动画
      if (facialAnimation && result.animationData) {
        facialAnimation.addClip(result.animationData.expressionData);
        facialAnimation.playClip(result.animationData.name);
      }
      
      // 显示情感分析结果
      displayEmotionResults('advanced-emotion-results', result.emotionResult);
    } else {
      console.error('高级面部动画生成失败:', result.error);
      alert('生成动画失败: ' + result.error);
    }
  } catch (error) {
    console.error('生成高级面部动画失败:', error);
    alert('生成动画失败: ' + error.message);
    
    // 隐藏加载中
    document.getElementById('advanced-loading').style.display = 'none';
  }
}

// 分析情感对比
async function analyzeEmotionComparison() {
  // 获取输入
  const prompt = document.getElementById('comparison-prompt').value;
  
  // 验证输入
  if (!prompt) {
    alert('请输入情感描述');
    return;
  }
  
  // 获取选中的模型
  const selectedModels = [];
  if (document.getElementById('compare-bert').checked) selectedModels.push('bert');
  if (document.getElementById('compare-roberta').checked) selectedModels.push('roberta');
  if (document.getElementById('compare-distilbert').checked) selectedModels.push('distilbert');
  if (document.getElementById('compare-albert').checked) selectedModels.push('albert');
  if (document.getElementById('compare-xlnet').checked) selectedModels.push('xlnet');
  
  if (selectedModels.length === 0) {
    alert('请至少选择一个模型');
    return;
  }
  
  try {
    // 清空结果
    document.getElementById('comparison-charts').innerHTML = '';
    
    // 创建结果容器
    const resultsContainer = document.createElement('div');
    resultsContainer.className = 'comparison-results-container';
    
    // 分析每个模型
    for (const modelType of selectedModels) {
      // 创建模型结果容器
      const modelContainer = document.createElement('div');
      modelContainer.className = 'model-result';
      modelContainer.innerHTML = `<h4>${modelType.toUpperCase()}</h4><div class="loading"><div class="spinner"></div><p>分析中...</p></div>`;
      resultsContainer.appendChild(modelContainer);
      
      // 创建情感分析器
      const analyzer = new AdvancedEmotionAnalyzer({
        modelType: modelType,
        debug: false
      });
      
      // 初始化分析器
      await analyzer.initialize();
      
      // 分析情感
      const result = await analyzer.analyzeEmotion({
        text: prompt,
        includeSecondary: true,
        includeChanges: true,
        detail: 'high'
      });
      
      // 显示结果
      modelContainer.innerHTML = `<h4>${modelType.toUpperCase()}</h4>`;
      displayModelEmotionResults(modelContainer, result);
    }
    
    // 添加结果
    document.getElementById('comparison-charts').appendChild(resultsContainer);
  } catch (error) {
    console.error('情感分析对比失败:', error);
    alert('情感分析失败: ' + error.message);
  }
}

// 显示情感分析结果
function displayEmotionResults(containerId, result) {
  const container = document.getElementById(containerId);
  container.innerHTML = '';
  
  if (!result) {
    container.innerHTML = '<p>无情感分析结果</p>';
    return;
  }
  
  // 创建主要情感
  const primaryEmotionContainer = document.createElement('div');
  primaryEmotionContainer.innerHTML = `<p>主要情感: ${result.primaryEmotion} (${(result.primaryIntensity * 100).toFixed(0)}%)</p>`;
  
  const primaryEmotionBar = document.createElement('div');
  primaryEmotionBar.className = 'emotion-bar';
  primaryEmotionBar.style.width = `${result.primaryIntensity * 100}%`;
  primaryEmotionBar.style.backgroundColor = getEmotionColor(result.primaryEmotion);
  
  primaryEmotionContainer.appendChild(primaryEmotionBar);
  container.appendChild(primaryEmotionContainer);
  
  // 创建次要情感
  if (result.secondaryEmotion) {
    const secondaryEmotionContainer = document.createElement('div');
    secondaryEmotionContainer.innerHTML = `<p>次要情感: ${result.secondaryEmotion} (${(result.secondaryIntensity * 100).toFixed(0)}%)</p>`;
    
    const secondaryEmotionBar = document.createElement('div');
    secondaryEmotionBar.className = 'emotion-bar';
    secondaryEmotionBar.style.width = `${result.secondaryIntensity * 100}%`;
    secondaryEmotionBar.style.backgroundColor = getEmotionColor(result.secondaryEmotion);
    
    secondaryEmotionContainer.appendChild(secondaryEmotionBar);
    container.appendChild(secondaryEmotionContainer);
  }
  
  // 创建情感分数
  if (result.scores) {
    const scoresContainer = document.createElement('div');
    scoresContainer.innerHTML = '<p>情感分数:</p>';
    
    for (const [emotion, score] of Object.entries(result.scores)) {
      const scoreContainer = document.createElement('div');
      scoreContainer.innerHTML = `<small>${emotion}: ${(score * 100).toFixed(0)}%</small>`;
      
      const scoreBar = document.createElement('div');
      scoreBar.className = 'emotion-bar';
      scoreBar.style.width = `${score * 100}%`;
      scoreBar.style.backgroundColor = getEmotionColor(emotion);
      scoreBar.style.height = '10px';
      
      scoreContainer.appendChild(scoreBar);
      scoresContainer.appendChild(scoreContainer);
    }
    
    container.appendChild(scoresContainer);
  }
}

// 显示模型情感分析结果
function displayModelEmotionResults(container, result) {
  if (!result) {
    container.innerHTML += '<p>无情感分析结果</p>';
    return;
  }
  
  // 创建主要情感
  const primaryEmotionContainer = document.createElement('div');
  primaryEmotionContainer.innerHTML = `<p>主要情感: ${result.primaryEmotion} (${(result.primaryIntensity * 100).toFixed(0)}%)</p>`;
  
  const primaryEmotionBar = document.createElement('div');
  primaryEmotionBar.className = 'emotion-bar';
  primaryEmotionBar.style.width = `${result.primaryIntensity * 100}%`;
  primaryEmotionBar.style.backgroundColor = getEmotionColor(result.primaryEmotion);
  
  primaryEmotionContainer.appendChild(primaryEmotionBar);
  container.appendChild(primaryEmotionContainer);
  
  // 创建次要情感
  if (result.secondaryEmotion) {
    const secondaryEmotionContainer = document.createElement('div');
    secondaryEmotionContainer.innerHTML = `<p>次要情感: ${result.secondaryEmotion} (${(result.secondaryIntensity * 100).toFixed(0)}%)</p>`;
    
    const secondaryEmotionBar = document.createElement('div');
    secondaryEmotionBar.className = 'emotion-bar';
    secondaryEmotionBar.style.width = `${result.secondaryIntensity * 100}%`;
    secondaryEmotionBar.style.backgroundColor = getEmotionColor(result.secondaryEmotion);
    
    secondaryEmotionContainer.appendChild(secondaryEmotionBar);
    container.appendChild(secondaryEmotionContainer);
  }
  
  // 创建置信度
  if (result.confidence) {
    const confidenceContainer = document.createElement('div');
    confidenceContainer.innerHTML = `<p>置信度: ${(result.confidence * 100).toFixed(0)}%</p>`;
    container.appendChild(confidenceContainer);
  }
  
  // 创建分析时间
  if (result.analysisTime) {
    const timeContainer = document.createElement('div');
    timeContainer.innerHTML = `<p>分析时间: ${result.analysisTime}ms</p>`;
    container.appendChild(timeContainer);
  }
}

// 获取情感颜色
function getEmotionColor(emotion) {
  const emotionColors = {
    'happy': '#52c41a',
    'sad': '#1890ff',
    'angry': '#f5222d',
    'surprised': '#faad14',
    'fear': '#722ed1',
    'disgust': '#eb2f96',
    'neutral': '#bfbfbf',
    'excited': '#fa541c',
    'anxious': '#13c2c2',
    'content': '#52c41a',
    'bored': '#8c8c8c',
    'confused': '#fadb14',
    'disappointed': '#1890ff'
  };
  
  return emotionColors[emotion] || '#bfbfbf';
}

// 初始化标签页
function initTabs() {
  const tabs = document.querySelectorAll('.tab');
  
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // 移除所有活动标签
      tabs.forEach(t => t.classList.remove('active'));
      
      // 移除所有活动内容
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });
      
      // 添加活动标签
      tab.classList.add('active');
      
      // 添加活动内容
      const tabId = tab.getAttribute('data-tab');
      document.getElementById(`${tabId}-tab`).classList.add('active');
    });
  });
}

// 初始化示例提示
function initExamplePrompts() {
  document.querySelectorAll('.example-prompt').forEach(prompt => {
    prompt.addEventListener('click', () => {
      // 获取父容器
      const container = prompt.closest('.controls');
      
      // 获取文本区域
      const textarea = container.querySelector('textarea');
      
      // 设置文本
      textarea.value = prompt.textContent;
    });
  });
}

// 初始化事件监听器
function initEventListeners() {
  // 基础生成按钮
  document.getElementById('basic-generate').addEventListener('click', generateBasicFacialAnimation);
  
  // 高级生成按钮
  document.getElementById('advanced-generate').addEventListener('click', generateAdvancedFacialAnimation);
  
  // 对比分析按钮
  document.getElementById('comparison-analyze').addEventListener('click', analyzeEmotionComparison);
}

// 初始化
async function init() {
  // 初始化标签页
  initTabs();
  
  // 初始化示例提示
  initExamplePrompts();
  
  // 初始化事件监听器
  initEventListeners();
  
  // 加载角色模型
  await loadCharacterModel();
}

// 启动
init();
