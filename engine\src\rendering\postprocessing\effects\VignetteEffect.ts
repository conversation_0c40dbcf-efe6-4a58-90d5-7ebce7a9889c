/**
 * 暗角效果
 * 模拟镜头边缘暗角
 */
import * as THREE from 'three';
// 使用类型断言导入 ShaderPass
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 暗角效果选项
 */
export interface VignetteEffectOptions extends PostProcessingEffectOptions {
  /** 偏移 */
  offset?: number;
  /** 暗化程度 */
  darkness?: number;
  /** 是否使用椭圆形 */
  eskil?: boolean;
  /** 暗角颜色 */
  color?: THREE.Color;
}

/**
 * 暗角效果
 */
export class VignetteEffect extends PostProcessingEffect {
  /** 偏移 */
  private offset: number;

  /** 暗化程度 */
  private darkness: number;

  /** 是否使用椭圆形 */
  private eskil: boolean;

  /** 暗角颜色 */
  private color: THREE.Color;

  /** 暗角通道 */
  private vignettePass: ShaderPass | null = null;

  /**
   * 创建暗角效果
   * @param options 暗角效果选项
   */
  constructor(options: VignetteEffectOptions = { name: 'Vignette' }) {
    super(options);

    this.offset = options.offset !== undefined ? options.offset : 1.0;
    this.darkness = options.darkness !== undefined ? options.darkness : 1.0;
    this.eskil = options.eskil !== undefined ? options.eskil : false;
    this.color = options.color || new THREE.Color(0x000000);
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 创建暗角着色器
    const vignetteShader = {
      uniforms: {
        'tDiffuse': { value: null },
        'offset': { value: this.offset },
        'darkness': { value: this.darkness },
        'eskil': { value: this.eskil ? 1 : 0 },
        'vignetteColor': { value: this.color }
      },
      vertexShader: /* glsl */`
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: /* glsl */`
        uniform sampler2D tDiffuse;
        uniform float offset;
        uniform float darkness;
        uniform int eskil;
        uniform vec3 vignetteColor;
        
        varying vec2 vUv;
        
        void main() {
          // 获取原始颜色
          vec4 texel = texture2D(tDiffuse, vUv);
          
          // 计算暗角
          vec2 uv = (vUv - 0.5) * 2.0;
          float vignette;
          
          if (eskil == 1) {
            // 椭圆形暗角
            float d = length(uv);
            vignette = smoothstep(0.8, offset * 0.799, d * (darkness + offset));
          } else {
            // 标准暗角
            uv *= uv;
            vignette = 1.0 - uv.x * uv.y * darkness * 16.0;
            vignette = clamp(pow(vignette, offset), 0.0, 1.0);
          }
          
          // 混合暗角颜色
          vec3 result = mix(vignetteColor, texel.rgb, vignette);
          
          gl_FragColor = vec4(result, texel.a);
        }
      `
    };

    // 创建暗角通道
    this.vignettePass = new ShaderPass(vignetteShader);

    // 设置通道
    this.pass = this.vignettePass;
  }

  /**
   * 设置偏移
   * @param offset 偏移
   */
  public setOffset(offset: number): void {
    this.offset = offset;

    if (this.vignettePass) {
      const uniforms = this.vignettePass.uniforms;
      if (uniforms.offset) {
        uniforms.offset.value = offset;
      }
    }
  }

  /**
   * 获取偏移
   * @returns 偏移
   */
  public getOffset(): number {
    return this.offset;
  }

  /**
   * 设置暗化程度
   * @param darkness 暗化程度
   */
  public setDarkness(darkness: number): void {
    this.darkness = darkness;

    if (this.vignettePass) {
      const uniforms = this.vignettePass.uniforms;
      if (uniforms.darkness) {
        uniforms.darkness.value = darkness;
      }
    }
  }

  /**
   * 获取暗化程度
   * @returns 暗化程度
   */
  public getDarkness(): number {
    return this.darkness;
  }

  /**
   * 设置是否使用椭圆形
   * @param eskil 是否使用椭圆形
   */
  public setEskil(eskil: boolean): void {
    this.eskil = eskil;

    if (this.vignettePass) {
      const uniforms = this.vignettePass.uniforms;
      if (uniforms.eskil) {
        uniforms.eskil.value = eskil ? 1 : 0;
      }
    }
  }

  /**
   * 获取是否使用椭圆形
   * @returns 是否使用椭圆形
   */
  public isEskil(): boolean {
    return this.eskil;
  }

  /**
   * 设置暗角颜色
   * @param color 暗角颜色
   */
  public setColor(color: THREE.Color): void {
    this.color.copy(color);

    if (this.vignettePass) {
      const uniforms = this.vignettePass.uniforms;
      if (uniforms.vignetteColor) {
        uniforms.vignetteColor.value = this.color;
      }
    }
  }

  /**
   * 获取暗角颜色
   * @returns 暗角颜色
   */
  public getColor(): THREE.Color {
    return this.color.clone();
  }
}
