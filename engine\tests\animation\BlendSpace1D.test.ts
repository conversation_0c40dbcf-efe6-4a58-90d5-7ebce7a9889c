/**
 * BlendSpace1D单元测试
 */
import { describe, it, expect, beforeEach } from 'vitest';
import { BlendSpace1D } from '../../src/animation/BlendSpace1D';
import { AnimationClip, LoopMode } from '../../src/animation/AnimationClip';

describe('BlendSpace1D', () => {
  let blendSpace: BlendSpace1D;
  let clip1: AnimationClip;
  let clip2: AnimationClip;
  let clip3: AnimationClip;

  beforeEach(() => {
    // 创建测试用的混合空间
    blendSpace = new BlendSpace1D({
      minValue: -1,
      maxValue: 1
    });

    // 创建测试用的动画片段
    clip1 = new AnimationClip('clip1', 1.0);
    clip2 = new AnimationClip('clip2', 1.0);
    clip3 = new AnimationClip('clip3', 1.0);
  });

  it('应该正确创建混合空间', () => {
    expect(blendSpace).toBeDefined();
    expect(blendSpace.getPosition()).toBe(0); // 默认位置
    expect(blendSpace.getNodes().length).toBe(0);
  });

  it('应该能够添加和移除节点', () => {
    // 添加节点
    const node1 = blendSpace.addNode(clip1, -1);
    const node2 = blendSpace.addNode(clip2, 0);
    const node3 = blendSpace.addNode(clip3, 1);

    // 验证节点添加成功
    expect(blendSpace.getNodes().length).toBe(3);
    expect(blendSpace.getNodes()[0]).toBe(node1);
    expect(blendSpace.getNodes()[1]).toBe(node2);
    expect(blendSpace.getNodes()[2]).toBe(node3);

    // 移除节点
    const result = blendSpace.removeNode(node2);
    expect(result).toBe(true);
    expect(blendSpace.getNodes().length).toBe(2);
    expect(blendSpace.getNodes()[0]).toBe(node1);
    expect(blendSpace.getNodes()[1]).toBe(node3);

    // 尝试移除不存在的节点
    const fakeNode = { clip: clip1, position: 0, weight: 0 };
    const result2 = blendSpace.removeNode(fakeNode as any);
    expect(result2).toBe(false);
  });

  it('应该能够设置和获取位置', () => {
    // 设置位置
    blendSpace.setPosition(0.5);
    expect(blendSpace.getPosition()).toBe(0.5);

    // 设置超出范围的位置
    blendSpace.setPosition(2);
    expect(blendSpace.getPosition()).toBe(1); // 应该被限制在最大值

    blendSpace.setPosition(-2);
    expect(blendSpace.getPosition()).toBe(-1); // 应该被限制在最小值
  });

  it('应该能够正确更新混合权重', () => {
    // 添加节点
    const node1 = blendSpace.addNode(clip1, -1);
    const node2 = blendSpace.addNode(clip2, 0);
    const node3 = blendSpace.addNode(clip3, 1);

    // 设置位置并更新
    blendSpace.setPosition(0.5);
    blendSpace.update();

    // 验证权重
    expect(node1.weight).toBe(0); // 不在活跃区间
    expect(node2.weight).toBeCloseTo(0.5);
    expect(node3.weight).toBeCloseTo(0.5);

    // 设置另一个位置并更新
    blendSpace.setPosition(-0.5);
    blendSpace.update();

    // 验证权重
    expect(node1.weight).toBeCloseTo(0.5);
    expect(node2.weight).toBeCloseTo(0.5);
    expect(node3.weight).toBe(0); // 不在活跃区间
  });

  it('应该能够处理边界情况', () => {
    // 没有节点的情况
    blendSpace.setPosition(0.5);
    blendSpace.update();
    expect(blendSpace.getActiveNodes().length).toBe(0);

    // 只有一个节点的情况
    const node1 = blendSpace.addNode(clip1, 0);
    blendSpace.update();
    expect(node1.weight).toBe(1);
    expect(blendSpace.getActiveNodes().length).toBe(1);

    // 位置在最小值的情况
    const node2 = blendSpace.addNode(clip2, -1);
    blendSpace.setPosition(-1);
    blendSpace.update();
    expect(node1.weight).toBe(0);
    expect(node2.weight).toBe(1);
    expect(blendSpace.getActiveNodes().length).toBe(1);

    // 位置在最大值的情况
    const node3 = blendSpace.addNode(clip3, 1);
    blendSpace.setPosition(1);
    blendSpace.update();
    expect(node1.weight).toBe(0);
    expect(node2.weight).toBe(0);
    expect(node3.weight).toBe(1);
    expect(blendSpace.getActiveNodes().length).toBe(1);
  });

  it('应该能够获取活跃节点', () => {
    // 添加节点
    const node1 = blendSpace.addNode(clip1, -1);
    const node2 = blendSpace.addNode(clip2, 0);
    const node3 = blendSpace.addNode(clip3, 1);

    // 设置位置并更新
    blendSpace.setPosition(0);
    blendSpace.update();

    // 验证活跃节点
    const activeNodes = blendSpace.getActiveNodes();
    expect(activeNodes.length).toBe(1);
    expect(activeNodes[0]).toBe(node2);

    // 设置另一个位置并更新
    blendSpace.setPosition(0.5);
    blendSpace.update();

    // 验证活跃节点
    const activeNodes2 = blendSpace.getActiveNodes();
    expect(activeNodes2.length).toBe(2);
    expect(activeNodes2).toContain(node2);
    expect(activeNodes2).toContain(node3);
  });
});
