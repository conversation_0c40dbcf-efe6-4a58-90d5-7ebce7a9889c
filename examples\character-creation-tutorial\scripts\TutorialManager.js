/**
 * 角色创建教程管理器
 * 负责管理教程流程、步骤和UI交互
 */
import { CharacterImporter } from './CharacterImporter.js';
import { SkeletonSetup } from './SkeletonSetup.js';
import { AnimationManager } from './AnimationManager.js';
import { StateMachineEditor } from './StateMachineEditor.js';
import { CharacterController } from './CharacterController.js';
import { InteractionManager } from './InteractionManager.js';

export class TutorialManager {
  /**
   * 构造函数
   */
  constructor() {
    // 当前步骤
    this.currentStep = 1;
    // 总步骤数
    this.totalSteps = 7;
    
    // 初始化各个管理器
    this.characterImporter = new CharacterImporter();
    this.skeletonSetup = new SkeletonSetup();
    this.animationManager = new AnimationManager();
    this.stateMachineEditor = new StateMachineEditor();
    this.characterController = new CharacterController();
    this.interactionManager = new InteractionManager();
    
    // 步骤内容
    this.steps = [
      {
        title: '步骤 1: 准备角色模型',
        description: `
          <p>在本步骤中，我们将准备用于导入的角色模型。</p>
          <ol>
            <li>查看示例角色模型的要求</li>
            <li>确保模型包含正确的骨骼结构</li>
            <li>检查UV映射和材质设置</li>
            <li>确认多边形数量适合实时渲染</li>
          </ol>
          <div class="tip">
            <strong>提示：</strong> 本教程提供了预设的角色模型，您可以直接使用它们进行学习。
          </div>
        `,
        actions: `
          <button id="use-sample-model-btn" class="action-button">使用示例模型</button>
          <button id="use-custom-model-btn" class="action-button">使用自定义模型</button>
        `,
        onEnter: () => {
          document.getElementById('use-sample-model-btn').addEventListener('click', this.useSampleModel.bind(this));
          document.getElementById('use-custom-model-btn').addEventListener('click', this.useCustomModel.bind(this));
        },
        onExit: () => {
          document.getElementById('use-sample-model-btn').removeEventListener('click', this.useSampleModel.bind(this));
          document.getElementById('use-custom-model-btn').removeEventListener('click', this.useCustomModel.bind(this));
        }
      },
      {
        title: '步骤 2: 导入角色模型',
        description: `
          <p>在本步骤中，我们将导入角色模型到编辑器中。</p>
          <ol>
            <li>点击"导入模型"按钮</li>
            <li>选择角色模型文件</li>
            <li>设置导入选项</li>
            <li>点击"导入"按钮</li>
          </ol>
          <div class="tip">
            <strong>提示：</strong> 导入时注意设置正确的比例和方向，这将影响后续的动画设置。
          </div>
        `,
        actions: `
          <button id="import-model-btn" class="action-button">导入模型</button>
        `,
        onEnter: () => {
          document.getElementById('import-model-btn').addEventListener('click', this.importModel.bind(this));
        },
        onExit: () => {
          document.getElementById('import-model-btn').removeEventListener('click', this.importModel.bind(this));
        }
      },
      // 更多步骤...
    ];
    
    // 初始化UI
    this.initUI();
  }
  
  /**
   * 初始化UI
   */
  initUI() {
    // 获取UI元素
    this.stepTitleElement = document.getElementById('step-title');
    this.stepDescriptionElement = document.getElementById('step-description');
    this.stepActionsElement = document.getElementById('step-actions');
    this.prevButton = document.getElementById('prev-btn');
    this.nextButton = document.getElementById('next-btn');
    this.resetButton = document.getElementById('reset-btn');
    
    // 设置按钮事件
    this.prevButton.addEventListener('click', this.prevStep.bind(this));
    this.nextButton.addEventListener('click', this.nextStep.bind(this));
    this.resetButton.addEventListener('click', this.resetTutorial.bind(this));
    
    // 更新UI
    this.updateUI();
    
    // 进入第一步
    this.enterStep(this.currentStep);
  }
  
  /**
   * 更新UI
   */
  updateUI() {
    // 更新步骤标题和描述
    const step = this.steps[this.currentStep - 1];
    this.stepTitleElement.innerHTML = step.title;
    this.stepDescriptionElement.innerHTML = step.description;
    this.stepActionsElement.innerHTML = step.actions || '';
    
    // 更新按钮状态
    this.prevButton.disabled = this.currentStep === 1;
    this.nextButton.disabled = this.currentStep === this.totalSteps;
    
    // 更新进度指示器
    for (let i = 1; i <= this.totalSteps; i++) {
      const indicator = document.getElementById(`step-indicator-${i}`);
      if (i === this.currentStep) {
        indicator.classList.add('active');
      } else {
        indicator.classList.remove('active');
      }
    }
  }
  
  /**
   * 进入步骤
   */
  enterStep(stepNumber) {
    const step = this.steps[stepNumber - 1];
    if (step.onEnter) {
      step.onEnter();
    }
  }
  
  /**
   * 退出步骤
   */
  exitStep(stepNumber) {
    const step = this.steps[stepNumber - 1];
    if (step.onExit) {
      step.onExit();
    }
  }
  
  /**
   * 上一步
   */
  prevStep() {
    if (this.currentStep > 1) {
      this.exitStep(this.currentStep);
      this.currentStep--;
      this.updateUI();
      this.enterStep(this.currentStep);
    }
  }
  
  /**
   * 下一步
   */
  nextStep() {
    if (this.currentStep < this.totalSteps) {
      this.exitStep(this.currentStep);
      this.currentStep++;
      this.updateUI();
      this.enterStep(this.currentStep);
    }
  }
  
  /**
   * 重置教程
   */
  resetTutorial() {
    this.exitStep(this.currentStep);
    this.currentStep = 1;
    this.updateUI();
    this.enterStep(this.currentStep);
  }
  
  /**
   * 使用示例模型
   */
  useSampleModel() {
    this.characterImporter.useSampleModel();
    this.nextStep();
  }
  
  /**
   * 使用自定义模型
   */
  useCustomModel() {
    this.characterImporter.openModelSelector();
  }
  
  /**
   * 导入模型
   */
  importModel() {
    this.characterImporter.importModel();
    this.nextStep();
  }
}
