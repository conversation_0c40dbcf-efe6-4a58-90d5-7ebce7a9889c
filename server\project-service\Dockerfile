FROM node:22-alpine AS builder

WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制项目服务代码
COPY project-service/package*.json ./project-service/
WORKDIR /app/project-service
RUN npm install

# 复制项目服务源代码
COPY project-service/ ./

RUN npm run build

FROM node:22-alpine

WORKDIR /app

COPY --from=builder /app/project-service/package*.json ./
COPY --from=builder /app/project-service/dist ./dist

RUN npm install --only=production

EXPOSE 3002 4002

CMD ["node", "dist/main.js"]
