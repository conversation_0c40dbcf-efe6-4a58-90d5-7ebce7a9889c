# 用户服务功能详细分析

## 概述

用户服务（User Service）是本项目服务端的核心微服务之一，负责处理用户认证、用户管理、权限控制等功能。该服务基于NestJS框架构建，采用微服务架构模式，提供RESTful API和微服务通信接口。

## 服务架构

### 技术栈
- **框架**: NestJS 10.x
- **数据库**: MySQL 8.x
- **ORM**: TypeORM 0.3.x
- **认证**: JWT + Passport
- **密码加密**: bcrypt
- **API文档**: Swagger/OpenAPI
- **容器化**: Docker
- **微服务通信**: TCP传输

### 服务端口配置
- **微服务端口**: 3001 (TCP通信)
- **HTTP API端口**: 4001 (RESTful API)

## 核心功能模块

### 1. 用户认证模块 (AuthModule)

#### 1.1 功能概述
负责用户的登录、注册、JWT令牌管理等认证相关功能。

#### 1.2 主要组件

**AuthService (认证服务)**
- 用户登录验证
- 用户注册处理
- JWT令牌生成和验证
- 密码验证

**AuthController (认证控制器)**
- `/api/auth/login` - 用户登录
- `/api/auth/register` - 用户注册

**认证策略**
- `LocalStrategy` - 本地用户名/密码认证
- `JwtStrategy` - JWT令牌验证

#### 1.3 认证流程

**登录流程:**
1. 用户提交用户名/邮箱和密码
2. LocalAuthGuard验证用户凭据
3. AuthService调用UsersService验证用户
4. 验证成功后生成JWT令牌
5. 返回令牌和用户信息

**注册流程:**
1. 用户提交注册信息
2. 验证用户名和邮箱唯一性
3. 使用bcrypt加密密码
4. 创建用户记录
5. 自动登录并返回令牌

### 2. 用户管理模块 (UsersModule)

#### 2.1 功能概述
负责用户的CRUD操作、用户信息管理、用户设置等功能。

#### 2.2 数据模型

**User实体 (用户主表)**
```typescript
- id: string (UUID主键)
- username: string (用户名，唯一)
- email: string (邮箱，唯一)
- password: string (加密密码)
- displayName: string (显示名称)
- isVerified: boolean (邮箱验证状态)
- isGuest: boolean (访客用户标识)
- role: UserRole (用户角色: USER/ADMIN)
- inviteCode: string (邀请码)
- lastLoginAt: Date (最后登录时间)
- createdAt: Date (创建时间)
- updatedAt: Date (更新时间)
```

**UserAvatar实体 (用户头像表)**
```typescript
- id: string (UUID主键)
- url: string (头像URL)
- thumbnailUrl: string (缩略图URL)
- modelUrl: string (3D模型URL)
- type: string (头像类型)
- userId: string (关联用户ID)
```

**UserSetting实体 (用户设置表)**
```typescript
- id: string (UUID主键)
- key: string (设置键)
- value: string (设置值)
- userId: string (关联用户ID)
```

#### 2.3 主要功能

**UsersService (用户服务)**
- `create()` - 创建用户
- `findAll()` - 查询所有用户 (管理员权限)
- `findOne()` - 根据ID查询用户
- `findByUsername()` - 根据用户名查询
- `findByEmail()` - 根据邮箱查询
- `update()` - 更新用户信息
- `remove()` - 删除用户
- `validateUser()` - 验证用户密码

**UsersController (用户控制器)**
- `POST /api/users` - 创建用户
- `GET /api/users` - 获取用户列表 (管理员)
- `GET /api/users/:id` - 获取用户详情
- `PATCH /api/users/:id` - 更新用户信息
- `DELETE /api/users/:id` - 删除用户

### 3. 权限控制系统

#### 3.1 角色定义
- **USER**: 普通用户，基础权限
- **ADMIN**: 管理员，完整权限

#### 3.2 权限守卫
- `JwtAuthGuard` - JWT令牌验证
- `RolesGuard` - 角色权限验证
- `LocalAuthGuard` - 本地认证验证

#### 3.3 权限装饰器
- `@Roles()` - 指定接口所需角色
- `@ApiBearerAuth()` - API文档认证标识

### 4. 健康检查模块 (HealthModule)

#### 4.1 功能概述
提供服务健康状态监控，包括数据库连接状态、内存使用情况等。

#### 4.2 检查项目
- 数据库连接状态
- 内存使用情况
- 磁盘空间状态

## 微服务集成

### 1. 服务注册
用户服务启动时自动向服务注册中心注册，提供服务发现功能。

### 2. 微服务通信
通过TCP传输协议提供以下微服务接口：
- `validateUser` - 验证用户凭据
- `register` - 用户注册
- `findUserById` - 根据ID查找用户
- `validateJwt` - 验证JWT令牌

### 3. API网关集成
API网关通过微服务客户端调用用户服务：
```typescript
// API网关中的用户服务调用示例
const user = await firstValueFrom(
  this.userService.send({ cmd: 'validateUser' }, { usernameOrEmail, password })
);
```

## 数据库设计

### 1. 数据库配置
- **类型**: MySQL
- **主机**: 可配置 (默认: localhost)
- **端口**: 可配置 (默认: 3306)
- **数据库名**: ir_engine_users
- **连接池**: 自动管理
- **同步模式**: 开发环境自动同步表结构

### 2. 表关系
- User (1) -> UserAvatar (1) - 一对一关系
- User (1) -> UserSetting (N) - 一对多关系

### 3. 索引优化
- username字段唯一索引
- email字段唯一索引
- userId外键索引

## 安全机制

### 1. 密码安全
- 使用bcrypt进行密码哈希
- 盐值轮数: 10
- 密码字段默认不查询 (select: false)

### 2. JWT安全
- 可配置密钥
- 可配置过期时间 (默认: 1天)
- 支持Bearer Token认证

### 3. 输入验证
- 使用class-validator进行数据验证
- 全局ValidationPipe过滤非法字段
- 防止SQL注入和XSS攻击

### 4. 安全头设置
- 使用helmet中间件
- 启用CORS跨域保护
- 启用gzip压缩

## 环境配置

### 1. 环境变量
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=ir_engine_users

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=1d

# 服务配置
USER_SERVICE_HOST=localhost
USER_SERVICE_PORT=3001
USER_SERVICE_HTTP_PORT=4001

# 服务注册中心
SERVICE_REGISTRY_HOST=localhost
SERVICE_REGISTRY_PORT=3010

# 运行环境
NODE_ENV=development
```

### 2. Docker部署
- 基于Node.js 18 Alpine镜像
- 多阶段构建优化镜像大小
- 暴露3001和4001端口
- 生产环境仅安装必要依赖

## 性能优化

### 1. 数据库优化
- 连接池管理
- 查询优化
- 索引优化
- 分页查询

### 2. 缓存策略
- JWT令牌缓存
- 用户信息缓存
- 查询结果缓存

### 3. 响应优化
- gzip压缩
- 数据传输优化
- 异步处理

## 监控与日志

### 1. 健康检查
- `/health` 端点监控
- 数据库连接监控
- 内存使用监控

### 2. 日志记录
- 请求日志
- 错误日志
- 性能日志
- 安全日志

### 3. 指标收集
- 响应时间
- 请求量统计
- 错误率统计
- 用户活跃度

## 扩展性设计

### 1. 水平扩展
- 无状态服务设计
- 支持多实例部署
- 负载均衡支持

### 2. 功能扩展
- 插件化架构
- 模块化设计
- 接口标准化

### 3. 数据扩展
- 灵活的用户设置系统
- 可扩展的用户属性
- 多租户支持预留

## 测试策略

### 1. 单元测试
- Service层测试
- Controller层测试
- 工具函数测试

### 2. 集成测试
- API接口测试
- 数据库集成测试
- 微服务通信测试

### 3. 端到端测试
- 完整业务流程测试
- 性能测试
- 安全测试

## 客户端集成

### 1. 前端集成
项目前端编辑器通过AuthService与用户服务集成：

**AuthService功能:**
- 用户登录认证
- 用户注册
- 令牌管理
- 自动登录
- 登出处理

**权限管理:**
- PermissionService提供细粒度权限控制
- 支持角色继承
- 动态权限分配
- 权限变更日志

### 2. 网络用户管理
引擎层面的NetworkUser接口支持：
- 实时用户状态同步
- 用户位置信息
- 设备信息管理
- 多用户协作

## 业务流程

### 1. 用户注册流程
```
用户填写注册信息 → 前端验证 → 发送注册请求 →
后端验证唯一性 → 密码加密 → 创建用户记录 →
生成JWT令牌 → 返回用户信息和令牌
```

### 2. 用户登录流程
```
用户输入凭据 → 前端验证 → 发送登录请求 →
后端验证用户存在 → 验证密码 → 更新登录时间 →
生成JWT令牌 → 返回用户信息和令牌
```

### 3. 权限验证流程
```
客户端请求 → 提取JWT令牌 → 验证令牌有效性 →
获取用户信息 → 检查用户权限 → 允许/拒绝访问
```

## 错误处理

### 1. 常见错误类型
- `ConflictException` - 用户名/邮箱已存在
- `NotFoundException` - 用户不存在
- `BadRequestException` - 密码错误
- `UnauthorizedException` - 认证失败

### 2. 错误响应格式
```json
{
  "statusCode": 400,
  "message": "用户名已存在",
  "error": "Bad Request"
}
```

### 3. 错误处理策略
- 统一错误格式
- 详细错误日志
- 用户友好提示
- 安全信息过滤

## 总结

用户服务作为系统的核心服务，提供了完整的用户管理和认证功能。其设计具有以下特点：

1. **高安全性**: 采用JWT认证、密码加密、输入验证等多重安全机制
2. **高可用性**: 微服务架构、健康检查、容器化部署
3. **高性能**: 数据库优化、缓存策略、响应压缩
4. **高扩展性**: 模块化设计、插件化架构、水平扩展支持
5. **易维护性**: 标准化接口、完善文档、测试覆盖

该服务为整个系统提供了稳定可靠的用户管理基础，支持系统的长期发展和扩展需求。通过微服务架构和标准化接口，能够很好地与其他服务集成，为多媒体/游戏引擎项目提供强大的用户管理能力。
