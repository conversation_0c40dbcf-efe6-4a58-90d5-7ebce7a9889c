/**
 * 场景实例管理器
 * 负责场景模板和实例的创建、管理和参数化配置
 */
import { Scene } from './Scene';
import type { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { SceneSerializer, SceneSerializedData } from './SceneSerializer';
import * as THREE from 'three';
/**
 * 模板变体
 */
export interface TemplateVariant {
    /** 变体ID */
    id: string;
    /** 变体名称 */
    name: string;
    /** 变体描述 */
    description?: string;
    /** 缩略图URL */
    thumbnailUrl?: string;
    /** 参数值 */
    parameterValues: Record<string, any>;
    /** 创建时间 */
    createdAt: Date;
    /** 更新时间 */
    updatedAt: Date;
    /** 自定义数据 */
    userData?: Record<string, any>;
}
/**
 * 场景模板数据
 */
export interface SceneTemplate {
    /** 模板ID */
    id: string;
    /** 模板名称 */
    name: string;
    /** 模板描述 */
    description?: string;
    /** 缩略图URL */
    thumbnailUrl?: string;
    /** 序列化数据 */
    serializedData: SceneSerializedData;
    /** 参数定义 */
    parameters: TemplateParameter[];
    /** 变体列表 */
    variants?: TemplateVariant[];
    /** 默认变体ID */
    defaultVariantId?: string;
    /** 分类 */
    category?: string;
    /** 标签 */
    tags?: string[];
    /** 版本 */
    version?: string;
    /** 作者 */
    author?: string;
    /** 许可证 */
    license?: string;
    /** 创建时间 */
    createdAt: Date;
    /** 更新时间 */
    updatedAt: Date;
    /** 自定义数据 */
    userData?: Record<string, any>;
}
/**
 * 模板参数类型
 */
export declare enum TemplateParameterType {
    /** 字符串 */
    STRING = "string",
    /** 数字 */
    NUMBER = "number",
    /** 整数 */
    INTEGER = "integer",
    /** 布尔值 */
    BOOLEAN = "boolean",
    /** 颜色 */
    COLOR = "color",
    /** 二维向量 */
    VECTOR2 = "vector2",
    /** 三维向量 */
    VECTOR3 = "vector3",
    /** 四维向量 */
    VECTOR4 = "vector4",
    /** 选择 */
    SELECT = "select",
    /** 多选 */
    MULTI_SELECT = "multi_select",
    /** 滑块 */
    SLIDER = "slider",
    /** 范围滑块 */
    RANGE_SLIDER = "range_slider",
    /** 纹理 */
    TEXTURE = "texture",
    /** 材质 */
    MATERIAL = "material",
    /** 网格 */
    MESH = "mesh",
    /** 动画 */
    ANIMATION = "animation",
    /** 音频 */
    AUDIO = "audio",
    /** 实体 */
    ENTITY = "entity",
    /** 变换 */
    TRANSFORM = "transform",
    /** 路径 */
    PATH = "path",
    /** 曲线 */
    CURVE = "curve",
    /** 脚本 */
    SCRIPT = "script",
    /** JSON */
    JSON = "json",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 模板参数
 */
export interface TemplateParameter {
    /** 参数ID */
    id: string;
    /** 参数名称 */
    name: string;
    /** 参数描述 */
    description?: string;
    /** 参数类型 */
    type: TemplateParameterType | string;
    /** 默认值 */
    defaultValue: any;
    /** 最小值（数值类型） */
    min?: number;
    /** 最大值（数值类型） */
    max?: number;
    /** 步长（数值类型） */
    step?: number;
    /** 选项（select类型） */
    options?: {
        value: any;
        label: string;
    }[];
    /** 是否必需 */
    required?: boolean;
    /** 是否只读 */
    readonly?: boolean;
    /** 是否隐藏 */
    hidden?: boolean;
    /** 条件显示表达式 */
    showIf?: string;
    /** 验证规则 */
    validation?: {
        /** 正则表达式 */
        pattern?: string;
        /** 自定义验证函数 */
        validator?: string;
        /** 错误消息 */
        message?: string;
    };
    /** 分组 */
    group?: string;
    /** 排序 */
    order?: number;
    /** 单位（数值类型） */
    unit?: string;
    /** 自定义渲染器 */
    renderer?: string;
    /** 自定义编辑器 */
    editor?: string;
    /** 自定义数据 */
    userData?: Record<string, any>;
}
/**
 * 场景实例数据
 */
export interface SceneInstance {
    /** 实例ID */
    id: string;
    /** 实例名称 */
    name: string;
    /** 模板ID */
    templateId: string;
    /** 变体ID */
    variantId?: string;
    /** 是否可见 */
    visible: boolean;
    /** 位置 */
    position: THREE.Vector3;
    /** 旋转 */
    rotation: THREE.Euler;
    /** 缩放 */
    scale: THREE.Vector3;
    /** 参数值 */
    parameters: Record<string, any>;
    /** 根实体ID */
    rootEntityId?: string;
    /** 是否锁定 */
    locked?: boolean;
    /** 是否静态 */
    isStatic?: boolean;
    /** 层级 */
    layer?: string;
    /** 标签 */
    tags?: string[];
    /** 创建时间 */
    createdAt: Date;
    /** 更新时间 */
    updatedAt: Date;
    /** 自定义数据 */
    userData?: Record<string, any>;
}
/**
 * 场景实例管理器选项
 */
export interface SceneInstanceManagerOptions {
    /** 场景序列化器 */
    sceneSerializer?: SceneSerializer;
    /** 是否启用资源共享 */
    enableResourceSharing?: boolean;
    /** 是否启用调试模式 */
    debug?: boolean;
}
/**
 * 场景实例管理器
 */
export declare class SceneInstanceManager extends EventEmitter {
    /** 场景序列化器 */
    private sceneSerializer;
    /** 是否启用资源共享 */
    private enableResourceSharing;
    /** 是否启用调试模式 */
    private debug;
    /** 模板映射 */
    private templates;
    /** 实例映射 */
    private instances;
    /** 实例实体映射 */
    private instanceEntities;
    /** 当前活动场景 */
    private activeScene;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建场景实例管理器
     * @param options 选项
     */
    constructor(options?: SceneInstanceManagerOptions);
    /**
     * 初始化实例管理器
     */
    initialize(): void;
    /**
     * 设置活动场景
     * @param scene 场景
     */
    setActiveScene(scene: Scene | null): void;
    /**
     * 创建模板
     * @param scene 场景
     * @param name 模板名称
     * @param description 模板描述
     * @param parameters 参数定义
     * @returns 模板ID
     */
    createTemplate(scene: Scene, name: string, description?: string, parameters?: TemplateParameter[]): string;
    /**
     * 更新模板
     * @param templateId 模板ID
     * @param updates 更新内容
     * @returns 是否成功更新
     */
    updateTemplate(templateId: string, updates: Partial<Omit<SceneTemplate, 'id' | 'createdAt'>>): boolean;
    /**
     * 删除模板
     * @param templateId 模板ID
     * @returns 是否成功删除
     */
    deleteTemplate(templateId: string): boolean;
    /**
     * 获取模板
     * @param templateId 模板ID
     * @returns 模板
     */
    getTemplate(templateId: string): SceneTemplate | null;
    /**
     * 获取所有模板
     * @returns 模板数组
     */
    getAllTemplates(): SceneTemplate[];
    /**
     * 创建实例
     * @param templateId 模板ID
     * @param options 实例选项
     * @returns 实例ID
     */
    createInstance(templateId: string, options?: {
        name?: string;
        variantId?: string;
        position?: THREE.Vector3;
        rotation?: THREE.Euler;
        scale?: THREE.Vector3;
        parameters?: Record<string, any>;
        visible?: boolean;
        locked?: boolean;
        isStatic?: boolean;
        layer?: string;
        tags?: string[];
        userData?: Record<string, any>;
    }): string;
    /**
     * 实例化模板
     * @param template 模板
     * @param instance 实例
     * @returns 根实体
     */
    private instantiateTemplate;
    /**
     * 应用参数
     * @param rootEntity 根实体
     * @param template 模板
     * @param parameters 参数值
     */
    private applyParameters;
    /**
     * 应用参数值
     * @param entity 目标实体
     * @param componentType 组件类型
     * @param property 属性路径
     * @param value 参数值
     * @param paramType 参数类型
     */
    private applyParameterValue;
    /**
     * 根据参数类型转换值
     * @param value 原始值
     * @param paramType 参数类型
     * @returns 转换后的值
     */
    private convertValueByType;
    /**
     * 处理特殊组件类型
     * @param entity 实体
     * @param component 组件
     * @param componentType 组件类型
     * @param property 属性路径
     * @param value 参数值
     * @param paramType 参数类型
     */
    private handleSpecialComponentTypes;
    /**
     * 更新实例
     * @param instanceId 实例ID
     * @param updates 更新内容
     * @returns 是否成功更新
     */
    updateInstance(instanceId: string, updates: Partial<Omit<SceneInstance, 'id' | 'templateId' | 'createdAt'>>): boolean;
    /**
     * 删除实例
     * @param instanceId 实例ID
     * @returns 是否成功删除
     */
    deleteInstance(instanceId: string): boolean;
    /**
     * 获取实例
     * @param instanceId 实例ID
     * @returns 实例
     */
    getInstance(instanceId: string): SceneInstance | null;
    /**
     * 获取所有实例
     * @returns 实例数组
     */
    getAllInstances(): SceneInstance[];
    /**
     * 获取实例实体
     * @param instanceId 实例ID
     * @returns 实体
     */
    getInstanceEntity(instanceId: string): Entity | null;
    /**
     * 创建模板变体
     * @param templateId 模板ID
     * @param variantOptions 变体选项
     * @returns 变体ID
     */
    createTemplateVariant(templateId: string, variantOptions: {
        name: string;
        description?: string;
        thumbnailUrl?: string;
        parameterValues: Record<string, any>;
        userData?: Record<string, any>;
    }): string;
    /**
     * 更新模板变体
     * @param templateId 模板ID
     * @param variantId 变体ID
     * @param updates 更新内容
     * @returns 是否成功更新
     */
    updateTemplateVariant(templateId: string, variantId: string, updates: Partial<Omit<TemplateVariant, 'id' | 'createdAt'>>): boolean;
    /**
     * 删除模板变体
     * @param templateId 模板ID
     * @param variantId 变体ID
     * @returns 是否成功删除
     */
    deleteTemplateVariant(templateId: string, variantId: string): boolean;
    /**
     * 设置默认变体
     * @param templateId 模板ID
     * @param variantId 变体ID
     * @returns 是否成功设置
     */
    setDefaultVariant(templateId: string, variantId: string): boolean;
    /**
     * 获取模板变体
     * @param templateId 模板ID
     * @param variantId 变体ID
     * @returns 变体
     */
    getTemplateVariant(templateId: string, variantId: string): TemplateVariant | null;
    /**
     * 获取模板的所有变体
     * @param templateId 模板ID
     * @returns 变体数组
     */
    getTemplateVariants(templateId: string): TemplateVariant[];
    /**
     * 销毁实例管理器
     */
    dispose(): void;
}
