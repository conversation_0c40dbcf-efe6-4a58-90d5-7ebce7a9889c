/**
 * 最少响应时间负载均衡策略
 */
import { Injectable } from '@nestjs/common';
import { BaseLoadBalancerStrategy } from './base-load-balancer.strategy';
import { LoadBalancerContext, LoadBalancerAlgorithm } from './load-balancer.interface';
import { ServiceInstanceEntity } from '../entities/service-instance.entity';

interface ResponseTimeRecord {
  instanceId: string;
  responseTime: number;
  timestamp: number;
  count: number;
}

@Injectable()
export class LeastResponseTimeLoadBalancerStrategy extends BaseLoadBalancerStrategy {
  // 响应时间记录，格式：{ serviceName: { instanceId: { responseTime, timestamp, count } } }
  private responseTimeRecords = new Map<string, Map<string, ResponseTimeRecord>>();
  
  // 响应时间记录过期时间（毫秒）
  private readonly recordExpireTime = 60 * 1000; // 1分钟
  
  constructor() {
    super(LoadBalancerAlgorithm.LEAST_RESPONSE_TIME);
    this.config.algorithm = LoadBalancerAlgorithm.LEAST_RESPONSE_TIME;
  }
  
  /**
   * 选择响应时间最少的服务实例
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  protected async doSelect(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): Promise<ServiceInstanceEntity | null> {
    if (!instances || instances.length === 0) {
      return null;
    }
    
    // 获取服务的响应时间记录
    const serviceKey = context.serviceName;
    let serviceRecords = this.responseTimeRecords.get(serviceKey);
    
    if (!serviceRecords) {
      serviceRecords = new Map<string, ResponseTimeRecord>();
      this.responseTimeRecords.set(serviceKey, serviceRecords);
    }
    
    // 清理过期记录
    this.cleanExpiredRecords(serviceRecords);
    
    // 如果没有响应时间记录，则随机选择
    if (serviceRecords.size === 0) {
      const index = Math.floor(Math.random() * instances.length);
      return instances[index];
    }
    
    // 计算每个实例的平均响应时间
    const instanceResponseTimes = new Map<string, number>();
    
    for (const instance of instances) {
      const record = serviceRecords.get(instance.id);
      
      if (record) {
        instanceResponseTimes.set(instance.id, record.responseTime);
      } else {
        // 如果没有记录，则使用默认值
        instanceResponseTimes.set(instance.id, Number.MAX_SAFE_INTEGER);
      }
    }
    
    // 选择响应时间最少的实例
    let minResponseTime = Number.MAX_SAFE_INTEGER;
    let selectedInstance: ServiceInstanceEntity | null = null;
    
    for (const instance of instances) {
      const responseTime = instanceResponseTimes.get(instance.id) || Number.MAX_SAFE_INTEGER;
      
      if (responseTime < minResponseTime) {
        minResponseTime = responseTime;
        selectedInstance = instance;
      }
    }
    
    return selectedInstance;
  }
  
  /**
   * 更新实例响应时间
   * @param serviceName 服务名称
   * @param instanceId 实例ID
   * @param responseTime 响应时间（毫秒）
   */
  updateResponseTime(serviceName: string, instanceId: string, responseTime: number): void {
    // 获取服务的响应时间记录
    let serviceRecords = this.responseTimeRecords.get(serviceName);
    
    if (!serviceRecords) {
      serviceRecords = new Map<string, ResponseTimeRecord>();
      this.responseTimeRecords.set(serviceName, serviceRecords);
    }
    
    // 获取实例的响应时间记录
    const record = serviceRecords.get(instanceId);
    
    if (record) {
      // 更新记录
      const newCount = record.count + 1;
      const newResponseTime = (record.responseTime * record.count + responseTime) / newCount;
      
      serviceRecords.set(instanceId, {
        instanceId,
        responseTime: newResponseTime,
        timestamp: Date.now(),
        count: newCount,
      });
    } else {
      // 创建新记录
      serviceRecords.set(instanceId, {
        instanceId,
        responseTime,
        timestamp: Date.now(),
        count: 1,
      });
    }
  }
  
  /**
   * 清理过期记录
   * @param serviceRecords 服务响应时间记录
   */
  private cleanExpiredRecords(serviceRecords: Map<string, ResponseTimeRecord>): void {
    const now = Date.now();
    
    for (const [instanceId, record] of serviceRecords.entries()) {
      if (now - record.timestamp > this.recordExpireTime) {
        serviceRecords.delete(instanceId);
      }
    }
  }
  
  /**
   * 重置策略状态
   */
  override reset(): void {
    this.responseTimeRecords.clear();
  }
}
