/**
 * 场景合并器
 * 用于合并多个场景
 */
import * as THREE from 'three';
import { Scene } from '../Scene';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

import type { Transform } from '../../scene/Transform';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { generateUUID } from '../../utils/UUID';

/**
 * 场景合并选项
 */
export interface SceneMergeOptions {
  /** 是否保留原始场景 */
  preserveOriginalScenes?: boolean;
  /** 是否合并材质 */
  mergeMaterials?: boolean;
  /** 是否合并几何体 */
  mergeGeometries?: boolean;
  /** 是否合并纹理 */
  mergeTextures?: boolean;
  /** 是否优化合并后的场景 */
  optimizeMergedScene?: boolean;
  /** 是否应用变换 */
  applyTransforms?: boolean;
  /** 是否重新生成UUID */
  regenerateUUIDs?: boolean;
  /** 是否保留场景层次结构 */
  preserveHierarchy?: boolean;
  /** 是否使用实例化渲染 */
  useInstancing?: boolean;
  /** 自定义元数据 */
  metadata?: Record<string, any>;
}

/**
 * 场景拆分选项
 */
export interface SceneSplitOptions {
  /** 拆分方式 */
  splitMethod?: 'hierarchy' | 'spatial' | 'material' | 'custom';
  /** 是否保留原始场景 */
  preserveOriginalScene?: boolean;
  /** 是否优化拆分后的场景 */
  optimizeSplitScenes?: boolean;
  /** 是否重新生成UUID */
  regenerateUUIDs?: boolean;
  /** 自定义拆分函数 */
  customSplitFunction?: (entity: Entity) => number;
  /** 空间拆分的网格大小 */
  spatialGridSize?: number;
  /** 最大场景数量 */
  maxSceneCount?: number;
  /** 自定义元数据 */
  metadata?: Record<string, any>;
}

/**
 * 场景合并结果
 */
export interface SceneMergeResult {
  /** 合并后的场景 */
  mergedScene: Scene;
  /** 实体映射（原始实体ID -> 新实体ID） */
  entityMap: Map<string, string>;
  /** 组件映射（原始组件ID -> 新组件ID） */
  componentMap: Map<string, string>;
  /** 材质映射（原始材质ID -> 新材质ID） */
  materialMap: Map<string, string>;
  /** 几何体映射（原始几何体ID -> 新几何体ID） */
  geometryMap: Map<string, string>;
  /** 纹理映射（原始纹理ID -> 新纹理ID） */
  textureMap: Map<string, string>;
}

/**
 * 场景拆分结果
 */
export interface SceneSplitResult {
  /** 拆分后的场景列表 */
  splitScenes: Scene[];
  /** 实体映射（原始实体ID -> 新实体ID） */
  entityMap: Map<string, string[]>;
  /** 组件映射（原始组件ID -> 新组件ID） */
  componentMap: Map<string, string[]>;
  /** 材质映射（原始材质ID -> 新材质ID） */
  materialMap: Map<string, string[]>;
  /** 几何体映射（原始几何体ID -> 新几何体ID） */
  geometryMap: Map<string, string[]>;
  /** 纹理映射（原始纹理ID -> 新纹理ID） */
  textureMap: Map<string, string[]>;
}

/**
 * 场景合并器
 */
export class SceneMerger extends EventEmitter {
  /** 世界实例 */
  private world: World;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景合并器
   * @param world 世界实例
   */
  constructor(world: World) {
    super();
    this.world = world;
  }

  /**
   * 初始化合并器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    this.initialized = true;
  }

  /**
   * 合并场景
   * @param scenes 要合并的场景列表
   * @param options 合并选项
   * @returns 合并结果
   */
  public mergeScenes(scenes: Scene[], options: SceneMergeOptions = {}): SceneMergeResult {
    if (!this.initialized) {
      this.initialize();
    }

    if (scenes.length === 0) {
      throw new Error('没有要合并的场景');
    }

    // 合并选项
    const mergeOptions: SceneMergeOptions = {
      preserveOriginalScenes: options.preserveOriginalScenes !== undefined ? options.preserveOriginalScenes : true,
      mergeMaterials: options.mergeMaterials !== undefined ? options.mergeMaterials : true,
      mergeGeometries: options.mergeGeometries !== undefined ? options.mergeGeometries : false,
      mergeTextures: options.mergeTextures !== undefined ? options.mergeTextures : true,
      optimizeMergedScene: options.optimizeMergedScene !== undefined ? options.optimizeMergedScene : true,
      applyTransforms: options.applyTransforms !== undefined ? options.applyTransforms : false,
      regenerateUUIDs: options.regenerateUUIDs !== undefined ? options.regenerateUUIDs : true,
      preserveHierarchy: options.preserveHierarchy !== undefined ? options.preserveHierarchy : true,
      useInstancing: options.useInstancing !== undefined ? options.useInstancing : false,
      metadata: options.metadata || {}
    };

    // 创建新场景
    const mergedScene = this.world.createScene('合并场景');

    // 创建映射
    const entityMap = new Map<string, string>();
    const componentMap = new Map<string, string>();
    const materialMap = new Map<string, string>();
    const geometryMap = new Map<string, string>();
    const textureMap = new Map<string, string>();

    // 合并场景
    for (const scene of scenes) {
      this.mergeSceneInto(scene, mergedScene, mergeOptions, entityMap, componentMap, materialMap, geometryMap, textureMap);
    }

    // 优化合并后的场景
    if (mergeOptions.optimizeMergedScene) {
      this.optimizeScene(mergedScene);
    }

    return {
      mergedScene,
      entityMap,
      componentMap,
      materialMap,
      geometryMap,
      textureMap
    };
  }

  /**
   * 拆分场景
   * @param scene 要拆分的场景
   * @param options 拆分选项
   * @returns 拆分结果
   */
  public splitScene(scene: Scene, options: SceneSplitOptions = {}): SceneSplitResult {
    if (!this.initialized) {
      this.initialize();
    }

    // 拆分选项
    const splitOptions: SceneSplitOptions = {
      splitMethod: options.splitMethod || 'hierarchy',
      preserveOriginalScene: options.preserveOriginalScene !== undefined ? options.preserveOriginalScene : true,
      optimizeSplitScenes: options.optimizeSplitScenes !== undefined ? options.optimizeSplitScenes : true,
      regenerateUUIDs: options.regenerateUUIDs !== undefined ? options.regenerateUUIDs : true,
      customSplitFunction: options.customSplitFunction,
      spatialGridSize: options.spatialGridSize || 10,
      maxSceneCount: options.maxSceneCount || 10,
      metadata: options.metadata || {}
    };

    // 根据拆分方式拆分场景
    let splitScenes: Scene[] = [];
    let entityMap = new Map<string, string[]>();
    let componentMap = new Map<string, string[]>();
    let materialMap = new Map<string, string[]>();
    let geometryMap = new Map<string, string[]>();
    let textureMap = new Map<string, string[]>();

    switch (splitOptions.splitMethod) {
      case 'hierarchy':
        ({ splitScenes, entityMap, componentMap, materialMap, geometryMap, textureMap } = 
          this.splitSceneByHierarchy(scene, splitOptions));
        break;
      case 'spatial':
        ({ splitScenes, entityMap, componentMap, materialMap, geometryMap, textureMap } = 
          this.splitSceneBySpatial(scene, splitOptions));
        break;
      case 'material':
        ({ splitScenes, entityMap, componentMap, materialMap, geometryMap, textureMap } = 
          this.splitSceneByMaterial(scene, splitOptions));
        break;
      case 'custom':
        ({ splitScenes, entityMap, componentMap, materialMap, geometryMap, textureMap } = 
          this.splitSceneByCustom(scene, splitOptions));
        break;
      default:
        throw new Error(`不支持的拆分方式: ${splitOptions.splitMethod}`);
    }

    // 优化拆分后的场景
    if (splitOptions.optimizeSplitScenes) {
      for (const splitScene of splitScenes) {
        this.optimizeScene(splitScene);
      }
    }

    return {
      splitScenes,
      entityMap,
      componentMap,
      materialMap,
      geometryMap,
      textureMap
    };
  }

  /**
   * 将场景合并到目标场景中
   * @param sourceScene 源场景
   * @param targetScene 目标场景
   * @param options 合并选项
   * @param entityMap 实体映射
   * @param componentMap 组件映射
   * @param materialMap 材质映射
   * @param geometryMap 几何体映射
   * @param textureMap 纹理映射
   */
  private mergeSceneInto(
    sourceScene: Scene,
    targetScene: Scene,
    options: SceneMergeOptions,
    entityMap: Map<string, string>,
    componentMap: Map<string, string>,
    materialMap: Map<string, string>,
    geometryMap: Map<string, string>,
    textureMap: Map<string, string>
  ): void {
    try {
      // 获取源场景的所有实体
      const sourceEntities = sourceScene.getEntities();

      // 合并实体
      for (const entity of sourceEntities) {
        this.mergeEntity(entity, targetScene, options, entityMap, componentMap);
      }

      // 合并场景设置
      this.mergeSceneSettings(sourceScene, targetScene);

      Debug.log(`成功将场景 ${sourceScene.name} 合并到 ${targetScene.name}`);
    } catch (error) {
      Debug.error(`合并场景失败: ${error}`);
      throw error;
    }
  }

  /**
   * 合并实体
   * @param sourceEntity 源实体
   * @param targetScene 目标场景
   * @param options 合并选项
   * @param entityMap 实体映射
   * @param componentMap 组件映射
   */
  private mergeEntity(
    sourceEntity: Entity,
    targetScene: Scene,
    options: SceneMergeOptions,
    entityMap: Map<string, string>,
    componentMap: Map<string, string>
  ): void {
    // 创建新实体
    const newEntity = this.world.createEntity(sourceEntity.name);

    // 生成新ID或保留原ID
    if (options.regenerateUUIDs) {
      newEntity.id = generateUUID();
    } else {
      newEntity.id = sourceEntity.id;
    }

    // 记录实体映射
    entityMap.set(sourceEntity.id, newEntity.id);

    // 复制变换组件
    const sourceTransform = sourceEntity.getTransform();
    const newTransform = newEntity.getTransform();
    newTransform.setPosition(sourceTransform.getPosition().clone());
    newTransform.setRotation(sourceTransform.getRotation().clone());
    newTransform.setScale(sourceTransform.getScale().clone());

    // 添加到目标场景
    targetScene.addEntity(newEntity);

    // 处理子实体
    const children = sourceEntity.getChildren();
    for (const child of children) {
      this.mergeEntity(child, targetScene, options, entityMap, componentMap);
    }
  }

  /**
   * 合并场景设置
   * @param sourceScene 源场景
   * @param targetScene 目标场景
   */
  private mergeSceneSettings(sourceScene: Scene, targetScene: Scene): void {
    // 合并环境光
    const sourceAmbientLight = sourceScene.getAmbientLight();
    if (sourceAmbientLight) {
      const targetAmbientLight = targetScene.getAmbientLight();
      if (targetAmbientLight) {
        // 混合环境光颜色和强度
        const sourceColor = sourceAmbientLight.color;
        const targetColor = targetAmbientLight.color;
        const blendedColor = new THREE.Color().lerpColors(targetColor, sourceColor, 0.5);
        targetAmbientLight.color = blendedColor;
        targetAmbientLight.intensity = Math.max(targetAmbientLight.intensity, sourceAmbientLight.intensity);
      }
    }

    // 合并雾效设置（简化处理）
    if (sourceScene.isFogEnabled() && !targetScene.isFogEnabled()) {
      // 启用目标场景的雾效
      targetScene.setFog(0x404040, 1, 1000);
    }
  }

  /**
   * 优化场景
   * @param scene 要优化的场景
   */
  private optimizeScene(scene: Scene): void {
    try {
      // 移除重复的材质
      this.optimizeMaterials(scene);

      // 合并相似的几何体
      this.optimizeGeometries(scene);

      // 优化纹理
      this.optimizeTextures(scene);

      // 优化场景图
      this.optimizeSceneGraph(scene);

      Debug.log(`场景 ${scene.name} 优化完成`);
    } catch (error) {
      Debug.error(`场景优化失败: ${error}`);
    }
  }

  /**
   * 优化材质
   * @param scene 场景
   */
  private optimizeMaterials(scene: Scene): void {
    // 收集所有材质（暂时跳过具体实现）
    const entities = scene.getEntities();
    Debug.log(`正在优化 ${entities.length} 个实体的材质`);
  }

  /**
   * 优化几何体
   * @param scene 场景
   */
  private optimizeGeometries(scene: Scene): void {
    // 收集所有几何体（暂时跳过具体实现）
    const entities = scene.getEntities();
    Debug.log(`正在优化 ${entities.length} 个实体的几何体`);
  }

  /**
   * 优化纹理
   * @param scene 场景
   */
  private optimizeTextures(scene: Scene): void {
    // 收集所有纹理（暂时跳过具体实现）
    const entities = scene.getEntities();
    Debug.log(`正在优化 ${entities.length} 个实体的纹理`);
  }

  /**
   * 优化场景图
   * @param scene 场景
   */
  private optimizeSceneGraph(scene: Scene): void {
    // 移除空的实体
    const entities = scene.getEntities();
    const entitiesToRemove: Entity[] = [];

    for (const entity of entities) {
      if (entity.getChildren().length === 0) {
        // 没有子实体的实体可以考虑移除
        entitiesToRemove.push(entity);
      }
    }

    for (const entity of entitiesToRemove) {
      scene.removeEntity(entity);
    }
  }

  /**
   * 按层次结构拆分场景
   * @param scene 要拆分的场景
   * @param options 拆分选项
   * @returns 拆分结果
   */
  private splitSceneByHierarchy(scene: Scene, options: SceneSplitOptions): {
    splitScenes: Scene[];
    entityMap: Map<string, string[]>;
    componentMap: Map<string, string[]>;
    materialMap: Map<string, string[]>;
    geometryMap: Map<string, string[]>;
    textureMap: Map<string, string[]>;
  } {
    const splitScenes: Scene[] = [];
    const entityMap = new Map<string, string[]>();
    const componentMap = new Map<string, string[]>();
    const materialMap = new Map<string, string[]>();
    const geometryMap = new Map<string, string[]>();
    const textureMap = new Map<string, string[]>();

    // 获取根实体
    const rootEntities = scene.getEntities().filter(entity => !entity.getParent());

    // 为每个根实体创建一个场景
    for (let i = 0; i < rootEntities.length && i < (options.maxSceneCount || 10); i++) {
      const rootEntity = rootEntities[i];
      const newScene = this.world.createScene(`${scene.name}_分割_${i + 1}`);

      // 复制根实体及其子实体到新场景
      this.copyEntityHierarchy(rootEntity, newScene, entityMap, componentMap);

      splitScenes.push(newScene);
    }

    return {
      splitScenes,
      entityMap,
      componentMap,
      materialMap,
      geometryMap,
      textureMap
    };
  }

  /**
   * 按空间拆分场景
   * @param scene 要拆分的场景
   * @param options 拆分选项
   * @returns 拆分结果
   */
  private splitSceneBySpatial(scene: Scene, options: SceneSplitOptions): {
    splitScenes: Scene[];
    entityMap: Map<string, string[]>;
    componentMap: Map<string, string[]>;
    materialMap: Map<string, string[]>;
    geometryMap: Map<string, string[]>;
    textureMap: Map<string, string[]>;
  } {
    const splitScenes: Scene[] = [];
    const entityMap = new Map<string, string[]>();
    const componentMap = new Map<string, string[]>();
    const materialMap = new Map<string, string[]>();
    const geometryMap = new Map<string, string[]>();
    const textureMap = new Map<string, string[]>();

    const gridSize = options.spatialGridSize || 10;
    const entities = scene.getEntities();
    const spatialGroups = new Map<string, Entity[]>();

    // 根据空间位置分组实体
    for (const entity of entities) {
      const transform = entity.getTransform();
      const position = transform.getPosition();

      const gridX = Math.floor(position.x / gridSize);
      const gridZ = Math.floor(position.z / gridSize);
      const gridKey = `${gridX}_${gridZ}`;

      if (!spatialGroups.has(gridKey)) {
        spatialGroups.set(gridKey, []);
      }
      spatialGroups.get(gridKey)!.push(entity);
    }

    // 为每个空间组创建场景
    let sceneIndex = 0;
    for (const [gridKey, groupEntities] of spatialGroups) {
      if (sceneIndex >= (options.maxSceneCount || 10)) break;

      const newScene = this.world.createScene(`${scene.name}_空间_${gridKey}`);

      for (const entity of groupEntities) {
        this.copyEntityToScene(entity, newScene, entityMap, componentMap);
      }

      splitScenes.push(newScene);
      sceneIndex++;
    }

    return {
      splitScenes,
      entityMap,
      componentMap,
      materialMap,
      geometryMap,
      textureMap
    };
  }

  /**
   * 按材质拆分场景
   * @param scene 要拆分的场景
   * @param options 拆分选项
   * @returns 拆分结果
   */
  private splitSceneByMaterial(scene: Scene, options: SceneSplitOptions): {
    splitScenes: Scene[];
    entityMap: Map<string, string[]>;
    componentMap: Map<string, string[]>;
    materialMap: Map<string, string[]>;
    geometryMap: Map<string, string[]>;
    textureMap: Map<string, string[]>;
  } {
    const splitScenes: Scene[] = [];
    const entityMap = new Map<string, string[]>();
    const componentMap = new Map<string, string[]>();
    const materialMap = new Map<string, string[]>();
    const geometryMap = new Map<string, string[]>();
    const textureMap = new Map<string, string[]>();

    // 按材质分组实体（这里需要根据实际的渲染组件来实现）
    const materialGroups = new Map<string, Entity[]>();
    const entities = scene.getEntities();

    for (const entity of entities) {
      // 暂时使用实体名称作为材质标识
      const materialKey = entity.name || 'default';

      if (!materialGroups.has(materialKey)) {
        materialGroups.set(materialKey, []);
      }
      materialGroups.get(materialKey)!.push(entity);
    }

    // 为每个材质组创建场景
    let sceneIndex = 0;
    for (const [materialKey, groupEntities] of materialGroups) {
      if (sceneIndex >= (options.maxSceneCount || 10)) break;

      const newScene = this.world.createScene(`${scene.name}_材质_${materialKey}`);

      for (const entity of groupEntities) {
        this.copyEntityToScene(entity, newScene, entityMap, componentMap);
      }

      splitScenes.push(newScene);
      sceneIndex++;
    }

    return {
      splitScenes,
      entityMap,
      componentMap,
      materialMap,
      geometryMap,
      textureMap
    };
  }

  /**
   * 按自定义方式拆分场景
   * @param scene 要拆分的场景
   * @param options 拆分选项
   * @returns 拆分结果
   */
  private splitSceneByCustom(scene: Scene, options: SceneSplitOptions): {
    splitScenes: Scene[];
    entityMap: Map<string, string[]>;
    componentMap: Map<string, string[]>;
    materialMap: Map<string, string[]>;
    geometryMap: Map<string, string[]>;
    textureMap: Map<string, string[]>;
  } {
    const splitScenes: Scene[] = [];
    const entityMap = new Map<string, string[]>();
    const componentMap = new Map<string, string[]>();
    const materialMap = new Map<string, string[]>();
    const geometryMap = new Map<string, string[]>();
    const textureMap = new Map<string, string[]>();

    if (!options.customSplitFunction) {
      throw new Error('自定义拆分需要提供拆分函数');
    }

    // 按自定义函数分组实体
    const customGroups = new Map<number, Entity[]>();
    const entities = scene.getEntities();

    for (const entity of entities) {
      const groupIndex = options.customSplitFunction(entity);

      if (!customGroups.has(groupIndex)) {
        customGroups.set(groupIndex, []);
      }
      customGroups.get(groupIndex)!.push(entity);
    }

    // 为每个自定义组创建场景
    let sceneIndex = 0;
    for (const [groupIndex, groupEntities] of customGroups) {
      if (sceneIndex >= (options.maxSceneCount || 10)) break;

      const newScene = this.world.createScene(`${scene.name}_自定义_${groupIndex}`);

      for (const entity of groupEntities) {
        this.copyEntityToScene(entity, newScene, entityMap, componentMap);
      }

      splitScenes.push(newScene);
      sceneIndex++;
    }

    return {
      splitScenes,
      entityMap,
      componentMap,
      materialMap,
      geometryMap,
      textureMap
    };
  }

  /**
   * 复制实体层次结构到场景
   * @param entity 要复制的实体
   * @param targetScene 目标场景
   * @param entityMap 实体映射
   * @param componentMap 组件映射
   */
  private copyEntityHierarchy(
    entity: Entity,
    targetScene: Scene,
    entityMap: Map<string, string[]>,
    componentMap: Map<string, string[]>
  ): void {
    // 复制实体本身
    this.copyEntityToScene(entity, targetScene, entityMap, componentMap);

    // 递归复制子实体
    const children = entity.getChildren();
    for (const child of children) {
      this.copyEntityHierarchy(child, targetScene, entityMap, componentMap);
    }
  }

  /**
   * 复制实体到场景
   * @param entity 要复制的实体
   * @param targetScene 目标场景
   * @param entityMap 实体映射
   * @param _componentMap 组件映射（暂未使用）
   */
  private copyEntityToScene(
    entity: Entity,
    targetScene: Scene,
    entityMap: Map<string, string[]>,
    _componentMap: Map<string, string[]>
  ): void {
    // 创建新实体
    const newEntity = this.world.createEntity(entity.name);
    newEntity.id = generateUUID();

    // 记录实体映射
    if (!entityMap.has(entity.id)) {
      entityMap.set(entity.id, []);
    }
    entityMap.get(entity.id)!.push(newEntity.id);

    // 复制变换组件
    const transform = entity.getTransform();
    const newTransform = newEntity.getTransform();
    newTransform.setPosition(transform.getPosition().clone());
    newTransform.setRotation(transform.getRotation().clone());
    newTransform.setScale(transform.getScale().clone());

    // 添加到目标场景
    targetScene.addEntity(newEntity);
  }
}
