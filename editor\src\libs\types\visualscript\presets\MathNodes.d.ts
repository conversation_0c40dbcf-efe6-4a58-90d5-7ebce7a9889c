/**
 * 视觉脚本数学节点
 * 提供数学运算相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 加法节点
 * 计算两个数的和
 */
export declare class AddNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 减法节点
 * 计算两个数的差
 */
export declare class SubtractNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 乘法节点
 * 计算两个数的积
 */
export declare class MultiplyNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 除法节点
 * 计算两个数的商
 */
export declare class DivideNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 取模节点
 * 计算两个数的模
 */
export declare class ModuloNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 幂运算节点
 * 计算一个数的幂
 */
export declare class PowerNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 平方根节点
 * 计算一个数的平方根
 */
export declare class SquareRootNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 三角函数节点
 * 计算三角函数值
 */
export declare class TrigonometricNode extends FunctionNode {
    /** 三角函数类型 */
    private trigType;
    /**
     * 创建三角函数节点
     * @param options 节点选项
     */
    constructor(options: any);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 注册数学节点
 * @param registry 节点注册表
 */
export declare function registerMathNodes(registry: NodeRegistry): void;
