/**
 * 环境响应组件
 *
 * 该组件定义实体如何响应环境变化，包括动画、特效、行为等。
 * 可以附加到角色或其他需要对环境做出响应的实体上。
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EnvironmentAwarenessData } from './EnvironmentAwarenessComponent';
/**
 * 响应类型枚举
 */
export declare enum ResponseType {
    /** 动画响应 */
    ANIMATION = "animation",
    /** 特效响应 */
    EFFECT = "effect",
    /** 声音响应 */
    SOUND = "sound",
    /** 行为响应 */
    BEHAVIOR = "behavior",
    /** 自定义响应 */
    CUSTOM = "custom",
    /** 物理响应 */
    PHYSICS = "physics",
    /** 姿势响应 */
    POSE = "pose",
    /** 面部表情响应 */
    FACIAL = "facial",
    /** 对话响应 */
    DIALOGUE = "dialogue",
    /** 移动响应 */
    LOCOMOTION = "locomotion",
    /** 状态变化响应 */
    STATE_CHANGE = "state_change",
    /** 属性变化响应 */
    PROPERTY_CHANGE = "property_change",
    /** 材质变化响应 */
    MATERIAL_CHANGE = "material_change",
    /** 粒子响应 */
    PARTICLE = "particle",
    /** 光照响应 */
    LIGHTING = "lighting"
}
/**
 * 响应优先级枚举
 */
export declare enum ResponsePriority {
    LOW = 0,
    MEDIUM = 1,
    HIGH = 2,
    CRITICAL = 3
}
/**
 * 环境响应规则接口
 */
export interface EnvironmentResponseRule {
    id: string;
    name: string;
    description?: string;
    responseType: ResponseType;
    priority: ResponsePriority;
    conditions: EnvironmentCondition[];
    actions: EnvironmentAction[];
    cooldown?: number;
    lastTriggeredTime?: number;
    enabled: boolean;
}
/**
 * 环境条件接口
 */
export interface EnvironmentCondition {
    type: string;
    params: any;
    evaluate: (data: EnvironmentAwarenessData) => boolean;
}
/**
 * 环境动作接口
 */
export interface EnvironmentAction {
    type: string;
    params: any;
    execute: (entity: Entity) => void;
    stop?: (entity: Entity) => void;
}
/**
 * 环境响应组件配置接口
 */
export interface EnvironmentResponseComponentConfig {
    autoRespond?: boolean;
    debug?: boolean;
    rules?: EnvironmentResponseRule[];
}
/**
 * 环境响应组件
 */
export declare class EnvironmentResponseComponent extends Component {
    config: EnvironmentResponseComponentConfig;
    rules: EnvironmentResponseRule[];
    private activeResponses;
    private onResponseChangeCallbacks;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: EnvironmentResponseComponentConfig);
    /**
     * 添加响应规则
     * @param rule 响应规则
     */
    addRule(rule: EnvironmentResponseRule): void;
    /**
     * 移除响应规则
     * @param ruleId 规则ID
     */
    removeRule(ruleId: string): void;
    /**
     * 启用响应规则
     * @param ruleId 规则ID
     */
    enableRule(ruleId: string): void;
    /**
     * 禁用响应规则
     * @param ruleId 规则ID
     */
    disableRule(ruleId: string): void;
    /**
     * 评估环境并触发响应
     * @param environmentData 环境数据
     */
    evaluateAndRespond(environmentData: EnvironmentAwarenessData): void;
    /**
     * 触发响应
     * @param rule 响应规则
     */
    private triggerResponse;
    /**
     * 停止响应
     * @param ruleId 规则ID
     */
    private stopResponse;
    /**
     * 注册响应变化回调
     * @param callback 回调函数
     */
    onResponseChange(callback: (activeResponses: Map<string, EnvironmentAction[]>) => void): void;
    /**
     * 移除响应变化回调
     * @param callback 回调函数
     */
    removeResponseChangeCallback(callback: (activeResponses: Map<string, EnvironmentAction[]>) => void): void;
    /**
     * 通知响应变化
     */
    private notifyResponseChange;
    /**
     * 获取当前活动的响应
     * @returns 活动的响应
     */
    getActiveResponses(): Map<string, EnvironmentAction[]>;
}
