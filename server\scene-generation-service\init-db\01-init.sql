-- 场景生成服务数据库初始化脚本

-- 创建数据库（如果不存在）
-- CREATE DATABASE IF NOT EXISTS digital_human_rag;

-- 使用数据库
-- \c digital_human_rag;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VA<PERSON>HAR(100),
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建场景模板表
CREATE TABLE IF NOT EXISTS scene_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    template_data JSONB NOT NULL,
    thumbnail_url VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建资源表
CREATE TABLE IF NOT EXISTS assets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    thumbnail_path VARCHAR(255),
    metadata JSONB,
    file_size INTEGER DEFAULT 0,
    mime_type VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建AI模型表
CREATE TABLE IF NOT EXISTS ai_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    provider VARCHAR(255) NOT NULL,
    model_id VARCHAR(255) NOT NULL,
    config JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建生成任务表
CREATE TABLE IF NOT EXISTS generation_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(50) DEFAULT 'normal',
    user_id UUID REFERENCES users(id),
    text_input TEXT,
    voice_file_url VARCHAR(255),
    image_file_url VARCHAR(255),
    input_metadata JSONB,
    generation_config JSONB,
    template_id VARCHAR(255),
    style_preferences JSONB,
    scene_constraints JSONB,
    analysis_result JSONB,
    layout_data JSONB,
    selected_assets JSONB,
    scene_data JSONB,
    progress FLOAT DEFAULT 0,
    total_steps INTEGER DEFAULT 0,
    current_step INTEGER DEFAULT 0,
    error_message TEXT,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建场景表
CREATE TABLE IF NOT EXISTS scenes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'draft',
    user_id UUID REFERENCES users(id),
    generation_task_id UUID REFERENCES generation_tasks(id),
    scene_data JSONB NOT NULL,
    metadata JSONB,
    thumbnail_url VARCHAR(255),
    version INTEGER DEFAULT 1,
    is_public BOOLEAN DEFAULT false,
    tags TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_generation_tasks_status_created_at ON generation_tasks(status, created_at);
CREATE INDEX IF NOT EXISTS idx_generation_tasks_user_id_created_at ON generation_tasks(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_generation_tasks_type_status ON generation_tasks(type, status);
CREATE INDEX IF NOT EXISTS idx_scenes_user_id_created_at ON scenes(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_scenes_status ON scenes(status);
CREATE INDEX IF NOT EXISTS idx_scenes_is_public ON scenes(is_public);

-- 插入默认数据
INSERT INTO scene_templates (name, description, category, template_data) VALUES
('基础室内场景', '简单的室内场景模板', 'indoor', '{"type": "indoor", "elements": ["floor", "walls", "ceiling", "lighting"]}'),
('户外自然场景', '自然环境场景模板', 'outdoor', '{"type": "outdoor", "elements": ["terrain", "sky", "vegetation", "lighting"]}'),
('商业展示场景', '产品展示用场景模板', 'commercial', '{"type": "commercial", "elements": ["platform", "backdrop", "lighting", "camera"]}')
ON CONFLICT DO NOTHING;

INSERT INTO ai_models (name, type, provider, model_id, config) VALUES
('GPT-4 文本分析', 'text_analysis', 'openai', 'gpt-4', '{"max_tokens": 2048, "temperature": 0.7}'),
('DALL-E 图像生成', 'image_generation', 'openai', 'dall-e-3', '{"size": "1024x1024", "quality": "standard"}'),
('Whisper 语音识别', 'speech_to_text', 'openai', 'whisper-1', '{"language": "zh"}')
ON CONFLICT DO NOTHING;
