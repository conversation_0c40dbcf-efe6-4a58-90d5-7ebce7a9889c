/**
 * 世界类
 * 负责管理实体和场景
 */
import { Engine } from './Engine';
import { Entity } from './Entity';
import { Scene } from '../scene/Scene';
import { System } from './System';
import { EventEmitter } from '../utils/EventEmitter';
export declare class World extends EventEmitter {
    /** 引擎实例 */
    private engine;
    /** 实体映射 */
    private entities;
    /** 当前场景 */
    private activeScene;
    /** 场景映射 */
    private scenes;
    /** 系统列表 */
    private systems;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建世界实例
     * @param engine 引擎实例
     */
    constructor(engine: Engine);
    /**
     * 初始化世界
     */
    initialize(): void;
    /**
     * 更新世界
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 固定时间步长更新
     * @param fixedDeltaTime 固定帧间隔时间（秒）
     */
    fixedUpdate(fixedDeltaTime: number): void;
    /**
     * 创建实体
     * @param name 实体名称
     * @returns 创建的实体
     */
    createEntity(name?: string): Entity;
    /**
     * 添加实体
     * @param entity 实体实例
     * @returns 添加的实体
     */
    addEntity(entity: Entity): Entity;
    /**
     * 获取实体
     * @param id 实体ID
     * @returns 实体实例，如果不存在则返回null
     */
    getEntity(id: string): Entity | null;
    /**
     * 移除实体
     * @param entity 实体实例或ID
     * @returns 是否成功移除
     */
    removeEntity(entity: Entity | string): boolean;
    /**
     * 获取所有实体
     * @returns 实体数组
     */
    getAllEntities(): Entity[];
    /**
     * 根据名称查找实体
     * @param name 实体名称
     * @returns 匹配的实体数组
     */
    findEntitiesByName(name: string): Entity[];
    /**
     * 根据标签查找实体
     * @param tag 实体标签
     * @returns 匹配的实体数组
     */
    findEntitiesByTag(tag: string): Entity[];
    /**
     * 创建场景
     * @param name 场景名称
     * @returns 创建的场景
     */
    createScene(name?: string): Scene;
    /**
     * 添加场景
     * @param scene 场景实例
     * @returns 添加的场景
     */
    addScene(scene: Scene): Scene;
    /**
     * 获取场景
     * @param id 场景ID
     * @returns 场景实例，如果不存在则返回null
     */
    getScene(id: string): Scene | null;
    /**
     * 移除场景
     * @param scene 场景实例或ID
     * @returns 是否成功移除
     */
    removeScene(scene: Scene | string): boolean;
    /**
     * 获取所有场景
     * @returns 场景数组
     */
    getAllScenes(): Scene[];
    /**
     * 设置活跃场景
     * @param scene 场景实例或ID
     * @returns 是否成功设置
     */
    setActiveScene(scene: Scene | string): boolean;
    /**
     * 获取活跃场景
     * @returns 活跃场景实例
     */
    getActiveScene(): Scene | null;
    /**
     * 获取引擎实例
     * @returns 引擎实例
     */
    getEngine(): Engine;
    /**
     * 添加系统
     * @param system 系统实例
     * @returns 添加的系统
     */
    addSystem(system: System): System;
    /**
     * 获取系统
     * @param systemClass 系统类
     * @returns 系统实例，如果不存在则返回null
     */
    getSystem<T extends System>(systemClass: new (...args: any[]) => T): T | null;
    /**
     * 移除系统
     * @param system 系统实例或类
     * @returns 是否成功移除
     */
    removeSystem(system: System | (new (...args: any[]) => System)): boolean;
    /**
     * 获取所有系统
     * @returns 系统数组
     */
    getSystems(): System[];
    /**
     * 获取所有实体
     * @returns 实体映射
     */
    getEntities(): Map<string, Entity>;
    /**
     * 清空世界
     */
    clear(): void;
    /**
     * 销毁世界
     */
    dispose(): void;
}
