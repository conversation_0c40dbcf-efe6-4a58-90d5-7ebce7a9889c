/**
 * 视觉脚本优化器
 * 提供视觉脚本的性能优化功能，包括节点缓存、懒加载和批处理
 */
import { Graph } from '../graph/Graph';
/**
 * 缓存策略
 */
export declare enum CacheStrategy {
    /**
     * 不缓存
     */
    NONE = "none",
    /**
     * 缓存所有节点
     */
    ALL = "all",
    /**
     * 智能缓存（根据节点类型和使用频率）
     */
    SMART = "smart"
}
/**
 * 视觉脚本优化器配置
 */
export interface VisualScriptOptimizerConfig {
    /**
     * 缓存策略
     */
    cacheStrategy: CacheStrategy;
    /**
     * 最大缓存项数量
     */
    maxCacheItems: number;
    /**
     * 缓存过期时间（毫秒）
     */
    cacheExpirationTime: number;
    /**
     * 是否启用懒加载
     */
    enableLazyLoading: boolean;
    /**
     * 懒加载视图范围
     */
    lazyLoadingViewRange: number;
    /**
     * 是否启用批处理
     */
    enableBatching: boolean;
    /**
     * 批处理大小
     */
    batchSize: number;
}
/**
 * 视觉脚本优化器
 */
export declare class VisualScriptOptimizer {
    /**
     * 配置
     */
    private config;
    /**
     * 节点缓存
     */
    private nodeCache;
    /**
     * 批处理组
     */
    private batchGroups;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<VisualScriptOptimizerConfig>);
    /**
     * 优化图
     * @param graph 视觉脚本图
     */
    optimizeGraph(graph: Graph): void;
    /**
     * 设置节点缓存
     * @param graph 视觉脚本图
     */
    private setupNodeCaching;
    /**
     * 设置批处理
     * @param graph 视觉脚本图
     */
    private setupBatching;
    /**
     * 设置懒加载
     * @param graph 视觉脚本图
     */
    private setupLazyLoading;
    /**
     * 判断是否应该缓存节点
     * @param node 节点
     * @returns 是否应该缓存
     */
    private shouldCacheNode;
    /**
     * 计算输入哈希
     * @param node 节点
     * @returns 输入哈希
     */
    private calculateInputHash;
    /**
     * 判断缓存是否过期
     * @param cacheItem 缓存项
     * @returns 是否过期
     */
    private isCacheExpired;
    /**
     * 缓存节点结果
     * @param cacheKey 缓存键
     * @param nodeId 节点ID
     * @param value 结果值
     * @param inputHash 输入哈希
     */
    private cacheNodeResult;
    /**
     * 清理缓存
     */
    private cleanCache;
    /**
     * 分析依赖关系
     * @param graph 视觉脚本图
     * @returns 依赖关系
     */
    private analyzeDependencies;
    /**
     * 创建批处理组
     * @param graph 视觉脚本图
     * @param dependencies 依赖关系
     */
    private createBatchGroups;
    /**
     * 计算节点层级
     * @param graph 视觉脚本图
     * @param dependencies 依赖关系
     * @returns 节点层级
     */
    private calculateNodeLevels;
    /**
     * 执行批处理
     */
    private executeBatches;
    /**
     * 获取视图范围内的节点
     * @param graph 视觉脚本图
     * @param viewport 视图范围
     * @returns 视图范围内的节点
     */
    private getNodesInViewport;
    /**
     * 加载节点
     * @param nodeIds 节点ID列表
     */
    private loadNodes;
    /**
     * 卸载节点
     * @param graph 视觉脚本图
     * @param visibleNodeIds 可见节点ID列表
     */
    private unloadNodes;
    /**
     * 获取性能统计信息
     * @returns 性能统计信息
     */
    getPerformanceStats(): any;
    /**
     * 计算缓存命中率
     * @returns 缓存命中率
     */
    private calculateCacheHitRate;
}
