/**
 * 网络模拟器
 * 用于模拟各种网络条件，如延迟、丢包、抖动等
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 网络模拟配置
 */
export interface NetworkSimulatorConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 延迟（毫秒） */
    latency?: number;
    /** 延迟抖动（毫秒） */
    latencyJitter?: number;
    /** 丢包率（0-1） */
    packetLoss?: number;
    /** 带宽限制（字节/秒） */
    bandwidthLimit?: number;
    /** 是否启用随机断线 */
    enableRandomDisconnect?: boolean;
    /** 随机断线概率（0-1） */
    disconnectProbability?: number;
    /** 断线后自动重连时间（毫秒） */
    reconnectTime?: number;
    /** 是否启用详细日志 */
    detailedLogging?: boolean;
}
/**
 * 网络模拟器
 * 用于模拟各种网络条件，如延迟、丢包、抖动等
 */
export declare class NetworkSimulator extends EventEmitter {
    /** 配置 */
    private config;
    /** 待处理的消息队列 */
    private messageQueue;
    /** 处理队列的定时器ID */
    private processTimerId;
    /** 当前是否断线 */
    private disconnected;
    /** 重连定时器ID */
    private reconnectTimerId;
    /** 发送的字节数 */
    private sentBytes;
    /** 上次重置带宽计数的时间 */
    private lastBandwidthResetTime;
    /**
     * 创建网络模拟器
     * @param config 配置
     */
    constructor(config?: NetworkSimulatorConfig);
    /**
     * 初始化
     */
    private initialize;
    /**
     * 启动处理队列
     */
    private startProcessing;
    /**
     * 停止处理队列
     */
    private stopProcessing;
    /**
     * 处理消息队列
     */
    private processQueue;
    /**
     * 模拟发送消息
     * @param data 消息数据
     * @param size 消息大小（字节）
     */
    send(data: any, size?: number): void;
    /**
     * 模拟断线
     */
    simulateDisconnect(): void;
    /**
     * 模拟重连
     */
    simulateReconnect(): void;
    /**
     * 更新配置
     * @param config 新配置
     */
    updateConfig(config: Partial<NetworkSimulatorConfig>): void;
    /**
     * 清空消息队列
     */
    clearQueue(): void;
    /**
     * 销毁模拟器
     */
    dispose(): void;
}
