{"id": "entity-movement-example", "name": "实体移动示例", "description": "一个实体移动示例，展示了如何使用实体节点和数学节点控制实体的移动。", "version": "1.0.0", "nodes": [{"id": "start-node", "type": "core/event/onStart", "position": {"x": 100, "y": 100}, "data": {"label": "开始"}}, {"id": "get-entity", "type": "entity/get", "position": {"x": 300, "y": 100}, "data": {"label": "获取实体"}}, {"id": "get-transform", "type": "entity/component/get", "position": {"x": 500, "y": 100}, "data": {"label": "获取Transform组件", "componentType": "TransformComponent"}}, {"id": "get-position", "type": "core/object/getProperty", "position": {"x": 700, "y": 100}, "data": {"label": "获取位置", "propertyName": "position"}}, {"id": "get-input", "type": "core/input/getAxis", "position": {"x": 300, "y": 300}, "data": {"label": "获取输入", "axisName": "horizontal"}}, {"id": "get-delta-time", "type": "core/time/getDeltaTime", "position": {"x": 300, "y": 400}, "data": {"label": "获取时间增量"}}, {"id": "get-speed", "type": "core/variable/get", "position": {"x": 300, "y": 500}, "data": {"label": "获取速度", "variableName": "speed"}}, {"id": "multiply-input-speed", "type": "math/basic/multiply", "position": {"x": 500, "y": 400}, "data": {"label": "输入 * 速度"}}, {"id": "multiply-delta-time", "type": "math/basic/multiply", "position": {"x": 700, "y": 400}, "data": {"label": "* 时间增量"}}, {"id": "create-vector", "type": "core/vector/create", "position": {"x": 900, "y": 400}, "data": {"label": "创建移动向量", "y": 0, "z": 0}}, {"id": "add-vectors", "type": "core/vector/add", "position": {"x": 900, "y": 200}, "data": {"label": "位置 + 移动"}}, {"id": "set-position", "type": "core/object/setProperty", "position": {"x": 1100, "y": 200}, "data": {"label": "设置位置", "propertyName": "position"}}, {"id": "loop-node", "type": "core/flow/loop", "position": {"x": 1300, "y": 200}, "data": {"label": "循环"}}], "edges": [{"id": "edge-start-get-entity", "source": "start-node", "target": "get-entity", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-get-entity-get-transform", "source": "get-entity", "target": "get-transform", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-get-transform-get-position", "source": "get-transform", "target": "get-position", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-get-position-add-vectors", "source": "get-position", "target": "add-vectors", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-add-vectors-set-position", "source": "add-vectors", "target": "set-position", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-set-position-loop", "source": "set-position", "target": "loop-node", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-loop-get-input", "source": "loop-node", "target": "get-input", "sourceHandle": "loop", "targetHandle": "flow", "type": "flow"}, {"id": "edge-get-input-multiply-input-speed", "source": "get-input", "target": "multiply-input-speed", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-multiply-input-speed-multiply-delta-time", "source": "multiply-input-speed", "target": "multiply-delta-time", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-multiply-delta-time-create-vector", "source": "multiply-delta-time", "target": "create-vector", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-create-vector-add-vectors", "source": "create-vector", "target": "add-vectors", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-entity-id-get-entity", "source": "start-node", "target": "get-entity", "sourceHandle": "entityId", "targetHandle": "entityId", "type": "data"}, {"id": "edge-entity-get-transform", "source": "get-entity", "target": "get-transform", "sourceHandle": "entity", "targetHandle": "entity", "type": "data"}, {"id": "edge-transform-get-position", "source": "get-transform", "target": "get-position", "sourceHandle": "component", "targetHandle": "object", "type": "data"}, {"id": "edge-position-add-vectors", "source": "get-position", "target": "add-vectors", "sourceHandle": "value", "targetHandle": "a", "type": "data"}, {"id": "edge-input-multiply-input-speed", "source": "get-input", "target": "multiply-input-speed", "sourceHandle": "value", "targetHandle": "a", "type": "data"}, {"id": "edge-speed-multiply-input-speed", "source": "get-speed", "target": "multiply-input-speed", "sourceHandle": "value", "targetHandle": "b", "type": "data"}, {"id": "edge-multiply-result-multiply-delta-time", "source": "multiply-input-speed", "target": "multiply-delta-time", "sourceHandle": "result", "targetHandle": "a", "type": "data"}, {"id": "edge-delta-time-multiply-delta-time", "source": "get-delta-time", "target": "multiply-delta-time", "sourceHandle": "value", "targetHandle": "b", "type": "data"}, {"id": "edge-multiply-result-create-vector", "source": "multiply-delta-time", "target": "create-vector", "sourceHandle": "result", "targetHandle": "x", "type": "data"}, {"id": "edge-vector-add-vectors", "source": "create-vector", "target": "add-vectors", "sourceHandle": "vector", "targetHandle": "b", "type": "data"}, {"id": "edge-add-result-set-position", "source": "add-vectors", "target": "set-position", "sourceHandle": "result", "targetHandle": "value", "type": "data"}, {"id": "edge-transform-set-position", "source": "get-transform", "target": "set-position", "sourceHandle": "component", "targetHandle": "object", "type": "data"}], "variables": [{"id": "var-entity-id", "name": "entityId", "valueTypeName": "number", "initialValue": 1}, {"id": "var-speed", "name": "speed", "valueTypeName": "number", "initialValue": 5}]}