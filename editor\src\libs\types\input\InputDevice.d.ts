/**
 * 输入设备接口
 * 定义输入设备的基本功能
 */
import { EventEmitter, EventCallback } from '../utils/EventEmitter';
/**
 * 输入设备接口
 */
export interface InputDevice {
    /**
     * 获取设备名称
     * @returns 设备名称
     */
    getName(): string;
    /**
     * 初始化设备
     */
    initialize(): void;
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁设备
     */
    destroy(): void;
    /**
     * 获取设备值
     * @param key 键名
     * @returns 设备值
     */
    getValue(key: string): any;
    /**
     * 设置设备值
     * @param key 键名
     * @param value 设备值
     */
    setValue(key: string, value: any): void;
    /**
     * 检查设备是否支持指定键
     * @param key 键名
     * @returns 是否支持
     */
    hasKey(key: string): boolean;
    /**
     * 获取所有键名
     * @returns 键名列表
     */
    getKeys(): string[];
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    on(event: string, callback: EventCallback): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    off(event: string, callback: EventCallback): void;
}
/**
 * 输入设备基类
 * 实现输入设备接口的基本功能
 */
export declare abstract class BaseInputDevice implements InputDevice {
    /** 设备名称 */
    protected name: string;
    /** 事件发射器 */
    protected eventEmitter: EventEmitter;
    /** 设备值映射 */
    protected values: Map<string, any>;
    /** 是否已初始化 */
    protected initialized: boolean;
    /** 是否已销毁 */
    protected destroyed: boolean;
    /**
     * 创建输入设备
     * @param name 设备名称
     */
    constructor(name: string);
    /**
     * 获取设备名称
     * @returns 设备名称
     */
    getName(): string;
    /**
     * 初始化设备
     */
    initialize(): void;
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁设备
     */
    destroy(): void;
    /**
     * 获取设备值
     * @param key 键名
     * @returns 设备值
     */
    getValue(key: string): any;
    /**
     * 设置设备值
     * @param key 键名
     * @param value 设备值
     */
    setValue(key: string, value: any): void;
    /**
     * 检查设备是否支持指定键
     * @param key 键名
     * @returns 是否支持
     */
    hasKey(key: string): boolean;
    /**
     * 获取所有键名
     * @returns 键名列表
     */
    getKeys(): string[];
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    on(event: string, callback: EventCallback): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    off(event: string, callback: EventCallback): void;
}
