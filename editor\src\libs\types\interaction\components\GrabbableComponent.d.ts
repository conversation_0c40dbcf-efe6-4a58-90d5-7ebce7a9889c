/**
 * 可抓取组件
 * 用于标记可被抓取的对象
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventCallback } from '../../utils/EventEmitter';
/**
 * 抓取类型
 */
export declare enum GrabType {
    /** 直接抓取 - 物体直接附加到抓取者手上 */
    DIRECT = "direct",
    /** 距离抓取 - 物体保持一定距离 */
    DISTANCE = "distance",
    /** 弹簧抓取 - 物体通过弹簧约束连接 */
    SPRING = "spring"
}
/**
 * 抓取手
 */
export declare enum Hand {
    /** 左手 */
    LEFT = "left",
    /** 右手 */
    RIGHT = "right",
    /** 任意手 */
    ANY = "any"
}
/**
 * 可抓取组件配置
 */
export interface GrabbableComponentConfig {
    /** 抓取类型 */
    grabType?: GrabType;
    /** 允许的抓取手 */
    allowedHands?: Hand[];
    /** 是否可抓取 */
    grabbable?: boolean;
    /** 抓取距离 */
    grabDistance?: number;
    /** 抓取声音 */
    grabSound?: string;
    /** 释放声音 */
    releaseSound?: string;
    /** 抓取回调 */
    onGrab?: (entity: Entity, grabber: Entity) => void;
    /** 释放回调 */
    onRelease?: (entity: Entity, grabber: Entity) => void;
}
/**
 * 可抓取组件
 */
export declare class GrabbableComponent extends Component {
    /** 组件类型 */
    static readonly TYPE: string;
    /** 抓取类型 */
    private _grabType;
    /** 允许的抓取手 */
    private _allowedHands;
    /** 是否可抓取 */
    private _grabbable;
    /** 抓取距离 */
    private _grabDistance;
    /** 抓取声音 */
    private _grabSound?;
    /** 释放声音 */
    private _releaseSound?;
    /** 是否被抓取 */
    private _isGrabbed;
    /** 抓取者 */
    private _grabber?;
    /** 抓取手 */
    private _grabbedHand?;
    /** 原始父实体 */
    private _originalParent?;
    /** 原始位置 */
    private _originalPosition;
    /** 原始旋转 */
    private _originalRotation;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param config 组件配置
     */
    constructor(entity: Entity, config?: GrabbableComponentConfig);
    /**
     * 获取抓取类型
     */
    get grabType(): GrabType;
    /**
     * 设置抓取类型
     */
    set grabType(value: GrabType);
    /**
     * 获取允许的抓取手
     */
    get allowedHands(): Hand[];
    /**
     * 设置允许的抓取手
     */
    set allowedHands(value: Hand[]);
    /**
     * 获取是否可抓取
     */
    get grabbable(): boolean;
    /**
     * 设置是否可抓取
     */
    set grabbable(value: boolean);
    /**
     * 获取抓取距离
     */
    get grabDistance(): number;
    /**
     * 设置抓取距离
     */
    set grabDistance(value: number);
    /**
     * 获取是否被抓取
     */
    get isGrabbed(): boolean;
    /**
     * 获取抓取者
     */
    get grabber(): Entity | undefined;
    /**
     * 获取抓取手
     */
    get grabbedHand(): Hand | undefined;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    on(event: string, callback: EventCallback): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    off(event: string, callback?: EventCallback): this;
    /**
     * 被抓取
     * @param grabber 抓取者
     * @param hand 抓取手
     * @returns 是否抓取成功
     */
    grab(grabber: Entity, hand?: Hand): boolean;
    /**
     * 释放
     * @returns 是否释放成功
     */
    release(): boolean;
}
