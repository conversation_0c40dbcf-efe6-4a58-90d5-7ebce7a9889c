# @dl-engine/types

DL引擎的TypeScript类型声明文件。

## 安装

```bash
npm install @dl-engine/types
```

## 使用

```typescript
import type { QuantizationBits, ExpressionStyle } from '@dl-engine/types';
```

## 包含的类型

- **AI模型相关**: `QuantizationBits`, `ExpressionStyle`
- **数字人相关**: `DigitalHumanCreationType`, `FacialExpression`, `Viseme`
- **交互相关**: `InteractionCallback`, `ConstraintType`
- **网络相关**: `EventHandler`, `EventFilter`
- **UI相关**: `AnimationTargetProperty`, `UIEasingFunction`
- **可视化脚本**: `NodeConstructor`, `ValueTypeCreator`

## 版本

当前版本: 1.0.0

包含 991 个类型定义。
