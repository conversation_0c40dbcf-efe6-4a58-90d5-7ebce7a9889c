<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DL（Digital Learning）引擎 - 简单场景示例</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
    }
    #canvas-container {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    #info {
      position: absolute;
      top: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-family: Arial, sans-serif;
    }
  </style>
</head>
<body>
  <div id="canvas-container"></div>
  <div id="info">
    <h2>DL（Digital Learning）引擎 - 简单场景示例</h2>
    <p>使用WASD键移动相机</p>
    <p>使用鼠标拖动旋转视角</p>
    <p>使用鼠标滚轮缩放</p>
  </div>

  <script type="module">
    import { Engine, World, Entity, Scene, Renderer, Camera, Transform, Mesh, Material, Light, PhysicsSystem } from '/engine/dist/index.js';

    // 初始化引擎
    const engine = new Engine({
      container: document.getElementById('canvas-container'),
      antialias: true,
      shadows: true,
    });

    // 创建世界
    const world = new World(engine);

    // 创建场景
    const scene = new Scene(world, {
      name: '简单场景示例',
      background: { type: 'color', value: '#87CEEB' },
    });

    // 创建相机
    const camera = new Entity(world)
      .addComponent(new Camera({
        type: 'perspective',
        fov: 60,
        near: 0.1,
        far: 1000,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 2, z: 5 },
        rotation: { x: -0.2, y: 0, z: 0 },
      }));

    // 创建地面
    const ground = new Entity(world)
      .addComponent(new Transform({
        position: { x: 0, y: 0, z: 0 },
        scale: { x: 20, y: 0.1, z: 20 },
      }))
      .addComponent(new Mesh({
        geometry: { type: 'box' },
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#8BC34A',
        roughness: 0.8,
        metalness: 0.2,
      }))
      .addComponent(new PhysicsSystem.components.PhysicsBody({
        type: 'static',
        shape: 'box',
      }));

    // 创建立方体
    const createCube = (x, y, z, color) => {
      return new Entity(world)
        .addComponent(new Transform({
          position: { x, y, z },
          rotation: { x: 0, y: Math.random() * Math.PI, z: 0 },
        }))
        .addComponent(new Mesh({
          geometry: { type: 'box' },
        }))
        .addComponent(new Material({
          type: 'standard',
          color,
          roughness: 0.3,
          metalness: 0.7,
        }))
        .addComponent(new PhysicsSystem.components.PhysicsBody({
          type: 'dynamic',
          shape: 'box',
          mass: 1,
          restitution: 0.7,
        }));
    };

    // 创建多个立方体
    createCube(-2, 3, 0, '#F44336');
    createCube(0, 5, 0, '#2196F3');
    createCube(2, 7, 0, '#FFC107');

    // 创建球体
    const createSphere = (x, y, z, radius, color) => {
      return new Entity(world)
        .addComponent(new Transform({
          position: { x, y, z },
        }))
        .addComponent(new Mesh({
          geometry: { type: 'sphere', radius },
        }))
        .addComponent(new Material({
          type: 'standard',
          color,
          roughness: 0.1,
          metalness: 0.9,
        }))
        .addComponent(new PhysicsSystem.components.PhysicsBody({
          type: 'dynamic',
          shape: 'sphere',
          mass: 1,
          restitution: 0.9,
        }));
    };

    // 创建多个球体
    createSphere(-3, 6, -2, 0.5, '#9C27B0');
    createSphere(0, 8, -2, 0.7, '#4CAF50');
    createSphere(3, 10, -2, 0.6, '#FF5722');

    // 创建方向光
    const directionalLight = new Entity(world)
      .addComponent(new Transform({
        position: { x: 5, y: 10, z: 5 },
        rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
      }))
      .addComponent(new Light({
        type: 'directional',
        color: '#FFFFFF',
        intensity: 1,
        castShadow: true,
        shadowMapSize: 2048,
      }));

    // 创建环境光
    const ambientLight = new Entity(world)
      .addComponent(new Light({
        type: 'ambient',
        color: '#FFFFFF',
        intensity: 0.3,
      }));

    // 添加实体到场景
    scene.addEntity(camera);
    scene.addEntity(ground);
    scene.addEntity(directionalLight);
    scene.addEntity(ambientLight);

    // 设置当前场景和相机
    world.setActiveScene(scene);
    world.setActiveCamera(camera);

    // 启动物理系统
    world.addSystem(new PhysicsSystem());

    // 添加相机控制
    const cameraControl = {
      moveSpeed: 0.1,
      rotateSpeed: 0.003,
      keys: { w: false, a: false, s: false, d: false },
      mouse: { down: false, x: 0, y: 0 },
    };

    // 键盘事件
    window.addEventListener('keydown', (e) => {
      if (e.key.toLowerCase() in cameraControl.keys) {
        cameraControl.keys[e.key.toLowerCase()] = true;
      }
    });

    window.addEventListener('keyup', (e) => {
      if (e.key.toLowerCase() in cameraControl.keys) {
        cameraControl.keys[e.key.toLowerCase()] = false;
      }
    });

    // 鼠标事件
    window.addEventListener('mousedown', (e) => {
      cameraControl.mouse.down = true;
      cameraControl.mouse.x = e.clientX;
      cameraControl.mouse.y = e.clientY;
    });

    window.addEventListener('mouseup', () => {
      cameraControl.mouse.down = false;
    });

    window.addEventListener('mousemove', (e) => {
      if (cameraControl.mouse.down) {
        const dx = e.clientX - cameraControl.mouse.x;
        const dy = e.clientY - cameraControl.mouse.y;
        
        const transform = camera.getComponent(Transform);
        transform.rotation.y -= dx * cameraControl.rotateSpeed;
        transform.rotation.x -= dy * cameraControl.rotateSpeed;
        transform.rotation.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, transform.rotation.x));
        
        cameraControl.mouse.x = e.clientX;
        cameraControl.mouse.y = e.clientY;
      }
    });

    // 滚轮事件
    window.addEventListener('wheel', (e) => {
      const transform = camera.getComponent(Transform);
      const direction = { x: 0, y: 0, z: -1 };
      
      // 根据相机旋转计算前进方向
      const cosY = Math.cos(transform.rotation.y);
      const sinY = Math.sin(transform.rotation.y);
      direction.x = -sinY;
      direction.z = -cosY;
      
      const delta = e.deltaY > 0 ? -1 : 1;
      transform.position.x += direction.x * delta * cameraControl.moveSpeed * 5;
      transform.position.z += direction.z * delta * cameraControl.moveSpeed * 5;
    });

    // 更新循环
    engine.onUpdate((deltaTime) => {
      // 处理相机移动
      if (Object.values(cameraControl.keys).some(key => key)) {
        const transform = camera.getComponent(Transform);
        const direction = { x: 0, y: 0, z: 0 };
        
        // 根据相机旋转计算前进方向
        const cosY = Math.cos(transform.rotation.y);
        const sinY = Math.sin(transform.rotation.y);
        
        if (cameraControl.keys.w) {
          direction.x -= sinY;
          direction.z -= cosY;
        }
        if (cameraControl.keys.s) {
          direction.x += sinY;
          direction.z += cosY;
        }
        if (cameraControl.keys.a) {
          direction.x -= cosY;
          direction.z += sinY;
        }
        if (cameraControl.keys.d) {
          direction.x += cosY;
          direction.z -= sinY;
        }
        
        // 归一化方向向量
        const length = Math.sqrt(direction.x * direction.x + direction.z * direction.z);
        if (length > 0) {
          direction.x /= length;
          direction.z /= length;
        }
        
        transform.position.x += direction.x * cameraControl.moveSpeed * deltaTime;
        transform.position.z += direction.z * cameraControl.moveSpeed * deltaTime;
      }
    });

    // 启动引擎
    engine.start();
  </script>
</body>
</html>
