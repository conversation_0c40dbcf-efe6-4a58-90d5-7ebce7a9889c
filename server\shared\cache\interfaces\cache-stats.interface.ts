/**
 * 缓存统计接口
 */
export interface CacheStats {
  /**
   * 缓存命中次数
   */
  hits: number;

  /**
   * 缓存未命中次数
   */
  misses: number;

  /**
   * 缓存条目数量
   */
  size: number;

  /**
   * 缓存命中率
   */
  hitRate: number;

  /**
   * 平均访问时间（毫秒）
   */
  avgAccessTime: number;

  /**
   * 缓存使用内存（字节）
   */
  memoryUsage: number;

  /**
   * 缓存过期次数
   */
  expirations: number;

  /**
   * 缓存驱逐次数
   */
  evictions: number;

  /**
   * 缓存写入次数
   */
  writes: number;

  /**
   * 缓存删除次数
   */
  deletes: number;

  /**
   * 缓存清空次数
   */
  clears: number;
}
