/**
 * 地下环境光照编辑器组件
 * 用于编辑地下环境光照属性
 */
import React, { useState, useEffect } from 'react';
import { Form, InputNumber, Switch, Slider, Button, Card, Tabs, Row, Col, Space, Divider, List } from 'antd';
import {
  BulbOutlined,
  SettingOutlined,
  FireOutlined,
  HighlightOutlined,
  CloudOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  DropboxOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../../store';
import Vector3Input from '../../common/Vector3Input';
import ColorPicker from '../../common/ColorPicker';
import {
  CaveLightingConfig,
  StalactiteReflectionConfig,
  WaterReflectionConfig,
  VolumetricLightConfig,
  VolumetricFogConfig,
  updateUndergroundLightingProperties
} from '../../../store/rendering/undergroundLightingSlice';

const { TabPane } = Tabs;

/**
 * 地下环境光照编辑器属性
 */
interface UndergroundLightingEditorProps {
  /** 实体ID */
  entityId: string;
}

/**
 * 地下环境光照编辑器组件
 */
const UndergroundLightingEditor: React.FC<UndergroundLightingEditorProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const dispatch = useDispatch<AppDispatch>();

  // 状态
  const [activeTab, setActiveTab] = useState<string>('system');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editingLightId, setEditingLightId] = useState<string | null>(null);
  const [editingLightType, setEditingLightType] = useState<string | null>(null);

  // 从Redux获取地下环境光照数据
  const undergroundLighting = useSelector((state: RootState) =>
    state.undergroundLighting.undergroundLighting.find(ul => ul.entityId === entityId)
  );
  
  // 初始化表单
  useEffect(() => {
    if (undergroundLighting) {
      form.setFieldsValue({
        // 系统属性
        enabled: undergroundLighting.enabled,
        autoUpdate: undergroundLighting.autoUpdate,
        updateFrequency: undergroundLighting.updateFrequency,
        enableCaveLighting: undergroundLighting.enableCaveLighting,
        enableStalactiteReflection: undergroundLighting.enableStalactiteReflection,
        enableWaterReflection: undergroundLighting.enableWaterReflection,
        enableVolumetricLight: undergroundLighting.enableVolumetricLight,
        enableVolumetricFog: undergroundLighting.enableVolumetricFog,
        enableDebugVisualization: undergroundLighting.enableDebugVisualization,
        enablePerformanceMonitorConfigConfiging: undergroundLighting.enablePerformanceMonitorConfigConfiging});
    }
  }, [undergroundLighting, form]);
  
  // 处理表单变更
  const handleValuesChange = (changedValues: any) => {
    // 更新Redux状态
    if (entityId) {
      dispatch(updateUndergroundLightingProperties(entityId, changedValues));
    }
  };

  // 处理保存
  const handleSave = () => {
    form.validateFields().then(values => {
      // 保存到Redux
      dispatch(updateUndergroundLightingProperties(entityId, values));
      setIsEditing(false);
    });
  };
  
  // 处理取消
  const handleCancel = () => {
    // 重置表单
    if (undergroundLighting) {
      form.setFieldsValue({
        // 重置为原始值
        // ...
      });
    }
    setIsEditing(false);
  };
  
  // 处理添加光源
  const handleAddLight = (type: string) => {
    // 根据类型创建不同的光源
    console.log(`Adding ${type} light for entity ${entityId}`);
    // TODO: 实现具体的添加逻辑
    // switch (type) {
    //   case 'cave':
    //     dispatch(addCaveLight(entityId, { /* 默认配置 */ }));
    //     break;
    //   case 'stalactite':
    //     dispatch(addStalactiteReflection(entityId, { /* 默认配置 */ }));
    //     break;
    //   case 'water':
    //     dispatch(addWaterReflection(entityId, { /* 默认配置 */ }));
    //     break;
    //   case 'volumetricLight':
    //     dispatch(addVolumetricLight(entityId, { /* 默认配置 */ }));
    //     break;
    //   case 'volumetricFog':
    //     dispatch(addVolumetricFog(entityId, { /* 默认配置 */ }));
    //     break;
    // }
  };
  
  // 处理编辑光源
  const handleEditLight = (type: string, id: string) => {
    setEditingLightType(type);
    setEditingLightId(id);
    
    // 根据类型获取不同的光源数据
    let lightData;
    switch (type) {
      case 'cave':
        lightData = undergroundLighting?.caveLights.find(light => light.id === id);
        break;
      case 'stalactite':
        lightData = undergroundLighting?.stalactiteReflections.find(light => light.id === id);
        break;
      case 'water':
        lightData = undergroundLighting?.waterReflections.find(light => light.id === id);
        break;
      case 'volumetricLight':
        lightData = undergroundLighting?.volumetricLights.find(light => light.id === id);
        break;
      case 'volumetricFog':
        lightData = undergroundLighting?.volumetricFogs.find(light => light.id === id);
        break;
    }
    
    if (lightData) {
      // 设置表单值
      form.setFieldsValue(lightData);
    }
  };
  
  // 处理删除光源
  const handleDeleteLight = (type: string, id: string) => {
    // 根据类型删除不同的光源
    console.log(`Deleting ${type} light ${id} for entity ${entityId}`);
    // TODO: 实现具体的删除逻辑
    // switch (type) {
    //   case 'cave':
    //     dispatch(removeCaveLight(entityId, id));
    //     break;
    //   case 'stalactite':
    //     dispatch(removeStalactiteReflection(entityId, id));
    //     break;
    //   case 'water':
    //     dispatch(removeWaterReflection(entityId, id));
    //     break;
    //   case 'volumetricLight':
    //     dispatch(removeVolumetricLight(entityId, id));
    //     break;
    //   case 'volumetricFog':
    //     dispatch(removeVolumetricFog(entityId, id));
    //     break;
    // }
  };
  
  // 渲染系统属性标签页
  const renderSystemTab = () => {
    return (
      <div className="system-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enabled"
                label={t('editor.rendering.undergroundLighting.enabled')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="autoUpdate"
                label={t('editor.rendering.undergroundLighting.autoUpdate')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="updateFrequency"
            label={t('editor.rendering.undergroundLighting.updateFrequency')}
            tooltip={t('editor.rendering.undergroundLighting.updateFrequencyTooltip')}
          >
            <InputNumber min={1} max={60} step={1} style={{ width: '100%' }} />
          </Form.Item>
          
          <Divider>{t('editor.rendering.undergroundLighting.features')}</Divider>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableCaveLighting"
                label={t('editor.rendering.undergroundLighting.enableCaveLighting')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enableStalactiteReflection"
                label={t('editor.rendering.undergroundLighting.enableStalactiteReflection')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableWaterReflection"
                label={t('editor.rendering.undergroundLighting.enableWaterReflection')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enableVolumetricLight"
                label={t('editor.rendering.undergroundLighting.enableVolumetricLight')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableVolumetricFog"
                label={t('editor.rendering.undergroundLighting.enableVolumetricFog')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enableDebugVisualization"
                label={t('editor.rendering.undergroundLighting.enableDebugVisualization')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="enablePerformanceMonitorConfigConfiging"
            label={t('editor.rendering.undergroundLighting.enablePerformanceMonitorConfigConfiging')}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </div>
    );
  };
  
  // 渲染洞穴光照标签页
  const renderCaveLightingTab = () => {
    return (
      <div className="cave-lighting-tab">
        <div className="light-list-header">
          <h3>{t('editor.rendering.undergroundLighting.caveLights')}</h3>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => handleAddLight('cave')}
            disabled={!isEditing}
          >
            {t('editor.rendering.undergroundLighting.addCaveLight')}
          </Button>
        </div>
        
        <List
          dataSource={undergroundLighting?.caveLights || []}
          renderItem={(item: CaveLightingConfig) => (
            <List.Item
              actions={[
                <Button
                  key="edit"
                  icon={<EditOutlined />}
                  onClick={() => handleEditLight('cave', item.id)}
                  disabled={!isEditing}
                />,
                <Button
                  key="delete"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDeleteLight('cave', item.id)}
                  disabled={!isEditing}
                />
              ]}
            >
              <List.Item.Meta
                title={`${t('editor.rendering.undergroundLighting.caveLight')} ${item.id}`}
                description={`${t('editor.rendering.undergroundLighting.intensity')}: ${item.intensity}, ${t('editor.rendering.undergroundLighting.color')}: ${item.color}`}
              />
            </List.Item>
          )}
        />
        
        {editingLightType === 'cave' && editingLightId && (
          <Card title={t('editor.rendering.undergroundLighting.editCaveLight')} style={{ marginTop: 16 }}>
            <Form
              form={form}
              layout="vertical"
              onValuesChange={handleValuesChange}
              disabled={!isEditing}
            >
              <Form.Item
                name="position"
                label={t('editor.rendering.undergroundLighting.position')}
              >
                <Vector3Input />
              </Form.Item>
              
              <Form.Item
                name="color"
                label={t('editor.rendering.undergroundLighting.color')}
              >
                <ColorPicker />
              </Form.Item>
              
              <Form.Item
                name="intensity"
                label={t('editor.rendering.undergroundLighting.intensity')}
              >
                <Slider min={0} max={10} step={0.1} />
              </Form.Item>
              
              <Form.Item
                name="size"
                label={t('editor.rendering.undergroundLighting.size')}
              >
                <Vector3Input />
              </Form.Item>
              
              <Form.Item
                name="decay"
                label={t('editor.rendering.undergroundLighting.decay')}
              >
                <Slider min={0} max={2} step={0.1} />
              </Form.Item>
              
              <Form.Item
                name="castShadow"
                label={t('editor.rendering.undergroundLighting.castShadow')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Button type="primary" onClick={() => setEditingLightId(null)}>
                {t('editor.common.done')}
              </Button>
            </Form>
          </Card>
        )}
      </div>
    );
  };
  
  // 渲染钟乳石反射光标签页
  const renderStalactiteReflectionTab = () => {
    return (
      <div className="stalactite-reflection-tab">
        <div className="light-list-header">
          <h3>{t('editor.rendering.undergroundLighting.stalactiteReflections')}</h3>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => handleAddLight('stalactite')}
            disabled={!isEditing}
          >
            {t('editor.rendering.undergroundLighting.addStalactiteReflection')}
          </Button>
        </div>
        
        <List
          dataSource={undergroundLighting?.stalactiteReflections || []}
          renderItem={(item: StalactiteReflectionConfig) => (
            <List.Item
              actions={[
                <Button
                  key="edit"
                  icon={<EditOutlined />}
                  onClick={() => handleEditLight('stalactite', item.id)}
                  disabled={!isEditing}
                />,
                <Button
                  key="delete"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDeleteLight('stalactite', item.id)}
                  disabled={!isEditing}
                />
              ]}
            >
              <List.Item.Meta
                title={`${t('editor.rendering.undergroundLighting.stalactiteReflection')} ${item.id}`}
                description={`${t('editor.rendering.undergroundLighting.intensity')}: ${item.intensity}, ${t('editor.rendering.undergroundLighting.color')}: ${item.color}`}
              />
            </List.Item>
          )}
        />
        
        {editingLightType === 'stalactite' && editingLightId && (
          <Card title={t('editor.rendering.undergroundLighting.editStalactiteReflection')} style={{ marginTop: 16 }}>
            <Form
              form={form}
              layout="vertical"
              onValuesChange={handleValuesChange}
              disabled={!isEditing}
            >
              <Form.Item
                name="position"
                label={t('editor.rendering.undergroundLighting.position')}
              >
                <Vector3Input />
              </Form.Item>
              
              <Form.Item
                name="color"
                label={t('editor.rendering.undergroundLighting.color')}
              >
                <ColorPicker />
              </Form.Item>
              
              <Form.Item
                name="intensity"
                label={t('editor.rendering.undergroundLighting.intensity')}
              >
                <Slider min={0} max={10} step={0.1} />
              </Form.Item>
              
              <Form.Item
                name="size"
                label={t('editor.rendering.undergroundLighting.size')}
              >
                <InputNumber min={0.1} max={10} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="flickerSpeed"
                label={t('editor.rendering.undergroundLighting.flickerSpeed')}
              >
                <Slider min={0} max={5} step={0.1} />
              </Form.Item>
              
              <Form.Item
                name="flickerIntensity"
                label={t('editor.rendering.undergroundLighting.flickerIntensity')}
              >
                <Slider min={0} max={1} step={0.01} />
              </Form.Item>
              
              <Button type="primary" onClick={() => setEditingLightId(null)}>
                {t('editor.common.done')}
              </Button>
            </Form>
          </Card>
        )}
      </div>
    );
  };
  
  // 渲染水面反射光标签页
  const renderWaterReflectionTab = () => {
    return (
      <div className="water-reflection-tab">
        <div className="light-list-header">
          <h3>{t('editor.rendering.undergroundLighting.waterReflections')}</h3>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAddLight('water')}
            disabled={!isEditing}
          >
            {t('editor.rendering.undergroundLighting.addWaterReflection')}
          </Button>
        </div>

        <List
          dataSource={undergroundLighting?.waterReflections || []}
          renderItem={(item: WaterReflectionConfig) => (
            <List.Item
              actions={[
                <Button
                  key="edit"
                  icon={<EditOutlined />}
                  onClick={() => handleEditLight('water', item.id)}
                  disabled={!isEditing}
                />,
                <Button
                  key="delete"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDeleteLight('water', item.id)}
                  disabled={!isEditing}
                />
              ]}
            >
              <List.Item.Meta
                title={`${t('editor.rendering.undergroundLighting.waterReflection')} ${item.id}`}
                description={`${t('editor.rendering.undergroundLighting.intensity')}: ${item.intensity}, ${t('editor.rendering.undergroundLighting.color')}: ${item.color}`}
              />
            </List.Item>
          )}
        />
      </div>
    );
  };

  // 渲染体积光标签页
  const renderVolumetricLightTab = () => {
    return (
      <div className="volumetric-light-tab">
        <div className="light-list-header">
          <h3>{t('editor.rendering.undergroundLighting.volumetricLights')}</h3>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAddLight('volumetricLight')}
            disabled={!isEditing}
          >
            {t('editor.rendering.undergroundLighting.addVolumetricLight')}
          </Button>
        </div>

        <List
          dataSource={undergroundLighting?.volumetricLights || []}
          renderItem={(item: VolumetricLightConfig) => (
            <List.Item
              actions={[
                <Button
                  key="edit"
                  icon={<EditOutlined />}
                  onClick={() => handleEditLight('volumetricLight', item.id)}
                  disabled={!isEditing}
                />,
                <Button
                  key="delete"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDeleteLight('volumetricLight', item.id)}
                  disabled={!isEditing}
                />
              ]}
            >
              <List.Item.Meta
                title={`${t('editor.rendering.undergroundLighting.volumetricLight')} ${item.id}`}
                description={`${t('editor.rendering.undergroundLighting.intensity')}: ${item.intensity}, ${t('editor.rendering.undergroundLighting.color')}: ${item.color}`}
              />
            </List.Item>
          )}
        />
      </div>
    );
  };

  // 渲染体积雾标签页
  const renderVolumetricFogTab = () => {
    return (
      <div className="volumetric-fog-tab">
        <div className="light-list-header">
          <h3>{t('editor.rendering.undergroundLighting.volumetricFogs')}</h3>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAddLight('volumetricFog')}
            disabled={!isEditing}
          >
            {t('editor.rendering.undergroundLighting.addVolumetricFog')}
          </Button>
        </div>

        <List
          dataSource={undergroundLighting?.volumetricFogs || []}
          renderItem={(item: VolumetricFogConfig) => (
            <List.Item
              actions={[
                <Button
                  key="edit"
                  icon={<EditOutlined />}
                  onClick={() => handleEditLight('volumetricFog', item.id)}
                  disabled={!isEditing}
                />,
                <Button
                  key="delete"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDeleteLight('volumetricFog', item.id)}
                  disabled={!isEditing}
                />
              ]}
            >
              <List.Item.Meta
                title={`${t('editor.rendering.undergroundLighting.volumetricFog')} ${item.id}`}
                description={`${t('editor.rendering.undergroundLighting.density')}: ${item.density}, ${t('editor.rendering.undergroundLighting.color')}: ${item.color}`}
              />
            </List.Item>
          )}
        />
      </div>
    );
  };
  
  return (
    <div className="component-editor underground-lighting-editor">
      <Card 
        title={
          <Space>
            <BulbOutlined />
            {t('editor.rendering.undergroundLighting.title')}
          </Space>
        }
        extra={
          <Space>
            {isEditing ? (
              <>
                <Button onClick={handleCancel}>{t('editor.common.cancel')}</Button>
                <Button type="primary" onClick={handleSave}>{t('editor.common.save')}</Button>
              </>
            ) : (
              <Button type="primary" onClick={() => setIsEditing(true)}>{t('editor.common.edit')}</Button>
            )}
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <SettingOutlined />
                {t('editor.rendering.undergroundLighting.systemTab')}
              </span>
            } 
            key="system"
          >
            {renderSystemTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <FireOutlined />
                {t('editor.rendering.undergroundLighting.caveLightingTab')}
              </span>
            } 
            key="caveLighting"
          >
            {renderCaveLightingTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <HighlightOutlined />
                {t('editor.rendering.undergroundLighting.stalactiteReflectionTab')}
              </span>
            } 
            key="stalactiteReflection"
          >
            {renderStalactiteReflectionTab()}
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <DropboxOutlined />
                {t('editor.rendering.undergroundLighting.waterReflectionTab')}
              </span>
            }
            key="waterReflection"
          >
            {renderWaterReflectionTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <BulbOutlined />
                {t('editor.rendering.undergroundLighting.volumetricLightTab')}
              </span>
            } 
            key="volumetricLight"
          >
            {renderVolumetricLightTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <CloudOutlined />
                {t('editor.rendering.undergroundLighting.volumetricFogTab')}
              </span>
            } 
            key="volumetricFog"
          >
            {renderVolumetricFogTab()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default UndergroundLightingEditor;
