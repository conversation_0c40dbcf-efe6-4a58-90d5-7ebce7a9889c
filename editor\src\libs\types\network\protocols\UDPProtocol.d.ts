/**
 * UDP协议实现
 * 使用WebRTC数据通道的无序、不可靠模式模拟UDP行为
 */
import { NetworkProtocol, NetworkProtocolOptions } from './NetworkProtocol';
/**
 * UDP协议配置选项
 */
export interface UDPProtocolOptions extends NetworkProtocolOptions {
    /** ICE服务器配置 */
    iceServers?: RTCIceServer[];
    /** 信令服务器URL */
    signalingServerUrl?: string;
    /** 最大消息大小（字节） */
    maxMessageSize?: number;
    /** 是否启用有序传输 */
    ordered?: boolean;
    /** 最大重传次数 */
    maxRetransmits?: number;
}
/**
 * UDP协议实现
 * 使用WebRTC数据通道的无序、不可靠模式模拟UDP行为
 */
export declare class UDPProtocol extends NetworkProtocol {
    /** 对等连接 */
    private peerConnection;
    /** 数据通道 */
    private dataChannel;
    /** 信令WebSocket */
    private signalingSocket;
    /** 远程对等ID */
    private remotePeerId;
    /** 本地对等ID */
    private localPeerId;
    /** 服务器URL */
    private serverUrl;
    /** 待发送的消息队列 */
    private messageQueue;
    /** 是否正在协商 */
    private isNegotiating;
    /** 是否是发起方 */
    private isInitiator;
    /**
     * 创建UDP协议
     * @param options 配置选项
     */
    constructor(options?: UDPProtocolOptions);
    /**
     * 连接到服务器
     * @param url 服务器URL（信令服务器URL）
     * @returns 连接Promise
     */
    connect(url: string): Promise<void>;
    /**
     * 断开连接
     * @returns 断开连接Promise
     */
    disconnect(): Promise<void>;
    /**
     * 发送数据
     * @param data 要发送的数据
     * @returns 是否成功发送
     */
    send(data: any): boolean;
    /**
     * 连接到信令服务器
     * @returns 连接Promise
     */
    private connectToSignalingServer;
    /**
     * 发送信令消息
     * @param message 信令消息
     */
    private sendSignalingMessage;
    /**
     * 处理信令消息
     * @param data 消息数据
     */
    private handleSignalingMessage;
    /**
     * 创建对等连接
     */
    private createPeerConnection;
    /**
     * 创建数据通道
     */
    private createDataChannel;
    /**
     * 设置数据通道事件
     */
    private setupDataChannel;
    /**
     * 创建提议
     */
    private createOffer;
    /**
     * 创建应答
     */
    private createAnswer;
    /**
     * 设置远程描述
     * @param sdp 会话描述
     */
    private setRemoteDescription;
    /**
     * 添加ICE候选
     * @param candidate ICE候选
     */
    private addIceCandidate;
    /**
     * 更新RTT统计
     * @param rtt 往返时间（毫秒）
     */
    private updateRttStats;
    /**
     * 发送队列中的消息
     */
    private flushMessageQueue;
}
