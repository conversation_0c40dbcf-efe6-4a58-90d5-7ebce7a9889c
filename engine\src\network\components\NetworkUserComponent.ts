/**
 * 网络用户组件
 * 用于标识和管理网络用户
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { NetworkUserRole, NetworkUserState } from '../NetworkUser';

/**
 * 网络用户组件属性
 */
export interface NetworkUserComponentProps {
  /** 用户ID */
  userId: string;

  /** 用户名 */
  username: string;

  /** 显示名称 */
  displayName?: string;

  /** 头像URL */
  avatarUrl?: string;

  /** 用户状态 */
  state?: NetworkUserState;

  /** 用户角色 */
  role?: NetworkUserRole;

  /** 是否是本地用户 */
  isLocal?: boolean;

  /** 自定义数据 */
  customData?: Record<string, any>;
}

/**
 * 网络用户组件
 * 用于标识和管理网络用户
 */
export class NetworkUserComponent extends Component {
  /** 组件类型 */
  public static readonly type = 'NetworkUser';

  /** 用户ID */
  public userId: string;

  /** 用户名 */
  public username: string;

  /** 显示名称 */
  public displayName: string;

  /** 头像URL */
  public avatarUrl: string;

  /** 用户状态 */
  public state: NetworkUserState;

  /** 用户角色 */
  public role: NetworkUserRole;

  /** 是否是本地用户 */
  public isLocal: boolean;

  /** 自定义数据 */
  public customData: Record<string, any>;

  /** 加入时间 */
  public joinTime: number;

  /** 最后活动时间 */
  public lastActiveTime: number;

  /** 是否有待同步的更改 */
  private hasPendingChanges: boolean = false;

  /** 待同步的属性 */
  private pendingProperties: Set<string> = new Set();

  /**
   * 创建网络用户组件
   * @param entity 实体
   * @param props 组件属性
   */
  constructor(entity: Entity, props: NetworkUserComponentProps) {
    super(NetworkUserComponent.type);

    // 设置实体引用
    this.setEntity(entity);

    this.userId = props.userId;
    this.username = props.username;
    this.displayName = props.displayName || props.username;
    this.avatarUrl = props.avatarUrl || '';
    this.state = props.state || NetworkUserState.ONLINE;
    this.role = props.role || NetworkUserRole.USER;
    this.isLocal = props.isLocal || false;
    this.customData = props.customData || {};
    this.joinTime = Date.now();
    this.lastActiveTime = Date.now();
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    // 初始化逻辑
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新逻辑
  }

  /**
   * 设置用户状态
   * @param state 用户状态
   */
  public setState(state: NetworkUserState): void {
    if (this.state === state) {
      return;
    }

    this.state = state;
    this.lastActiveTime = Date.now();

    // 标记属性为待同步
    this.markPropertyDirty('state');

    // 触发状态变更事件
    this.entity?.emit('userStateChanged', {
      userId: this.userId,
      state,
    });
  }

  /**
   * 设置用户角色
   * @param role 用户角色
   */
  public setRole(role: NetworkUserRole): void {
    if (this.role === role) {
      return;
    }

    this.role = role;

    // 标记属性为待同步
    this.markPropertyDirty('role');

    // 触发角色变更事件
    this.entity?.emit('userRoleChanged', {
      userId: this.userId,
      role,
    });
  }

  /**
   * 设置自定义数据
   * @param key 键
   * @param value 值
   */
  public setCustomData(key: string, value: any): void {
    this.customData[key] = value;

    // 标记属性为待同步
    this.markPropertyDirty('customData');

    // 触发自定义数据变更事件
    this.entity?.emit('userCustomDataChanged', {
      userId: this.userId,
      key,
      value,
    });
  }

  /**
   * 获取自定义数据
   * @param key 键
   * @returns 值
   */
  public getCustomData(key: string): any {
    return this.customData[key];
  }

  /**
   * 更新活动时间
   */
  public updateActivityTime(): void {
    this.lastActiveTime = Date.now();
  }

  /**
   * 获取用户数据
   * @returns 用户数据
   */
  public getUserData(): any {
    return {
      userId: this.userId,
      username: this.username,
      displayName: this.displayName,
      avatarUrl: this.avatarUrl,
      state: this.state,
      role: this.role,
      isLocal: this.isLocal,
      customData: this.customData,
      joinTime: this.joinTime,
      lastActiveTime: this.lastActiveTime,
    };
  }

  /**
   * 应用用户数据
   * @param data 用户数据
   */
  public applyUserData(data: any): void {
    if (data.username) {
      this.username = data.username;
    }

    if (data.displayName) {
      this.displayName = data.displayName;
    }

    if (data.avatarUrl) {
      this.avatarUrl = data.avatarUrl;
    }

    if (data.state) {
      this.state = data.state;
    }

    if (data.role) {
      this.role = data.role;
    }

    if (data.customData) {
      this.customData = { ...this.customData, ...data.customData };
    }

    if (data.lastActiveTime) {
      this.lastActiveTime = data.lastActiveTime;
    }
  }

  /**
   * 标记属性为待同步
   * @param property 属性名
   */
  public markPropertyDirty(property: string): void {
    this.hasPendingChanges = true;
    this.pendingProperties.add(property);
  }

  /**
   * 标记所有属性为待同步
   */
  public markAllPropertiesDirty(): void {
    this.hasPendingChanges = true;
  }

  /**
   * 同步用户数据
   */
  public sync(): void {
    if (!this.hasPendingChanges) {
      return;
    }

    // 获取需要同步的数据
    const syncData = this.getUserData();

    // 触发同步事件
    this.entity?.emit('userSync', syncData);

    // 清除待同步标记
    this.hasPendingChanges = false;
    this.pendingProperties.clear();
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 销毁逻辑
  }
}
