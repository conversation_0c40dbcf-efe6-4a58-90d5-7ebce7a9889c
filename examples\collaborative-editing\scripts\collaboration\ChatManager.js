/**
 * 聊天管理器
 * 负责管理聊天消息和通知
 */
export class ChatManager {
  /**
   * 构造函数
   * @param {UserManager} userManager 用户管理器
   */
  constructor(userManager) {
    this.userManager = userManager;
    this.messages = []; // 消息历史
    this.maxMessages = 100; // 最大消息数量
    this.eventListeners = new Map(); // 事件类型 -> 监听器数组
    
    // 初始化聊天面板UI
    this.initializeChatPanelUI();
  }
  
  /**
   * 发送消息
   * @param {Object} user 用户对象
   * @param {string} content 消息内容
   */
  sendMessage(user, content) {
    if (!user || !content) {
      return;
    }
    
    const message = {
      id: this.generateMessageId(),
      userId: user.id,
      content: content,
      timestamp: Date.now()
    };
    
    // 添加消息到历史
    this.addMessage(message);
    
    // 触发消息事件
    this.emit('message', user, content);
  }
  
  /**
   * 接收消息
   * @param {Object} user 用户对象
   * @param {string} content 消息内容
   * @param {number} timestamp 时间戳
   */
  receiveMessage(user, content, timestamp) {
    if (!user || !content) {
      return;
    }
    
    const message = {
      id: this.generateMessageId(),
      userId: user.id,
      content: content,
      timestamp: timestamp || Date.now()
    };
    
    // 添加消息到历史
    this.addMessage(message);
  }
  
  /**
   * 添加消息到历史
   * @param {Object} message 消息对象
   */
  addMessage(message) {
    // 添加消息到历史
    this.messages.push(message);
    
    // 如果消息数量超过最大值，删除最早的消息
    if (this.messages.length > this.maxMessages) {
      this.messages.shift();
    }
    
    // 更新聊天面板UI
    this.updateChatPanelUI();
  }
  
  /**
   * 发送系统通知
   * @param {string} content 通知内容
   */
  sendSystemNotification(content) {
    const message = {
      id: this.generateMessageId(),
      userId: 'system',
      content: content,
      timestamp: Date.now(),
      isSystem: true
    };
    
    // 添加消息到历史
    this.addMessage(message);
  }
  
  /**
   * 清空消息历史
   */
  clearMessages() {
    this.messages = [];
    
    // 更新聊天面板UI
    this.updateChatPanelUI();
  }
  
  /**
   * 生成消息ID
   * @returns {string} 消息ID
   */
  generateMessageId() {
    return 'msg_' + Math.random().toString(36).substr(2, 9);
  }
  
  /**
   * 初始化聊天面板UI
   */
  initializeChatPanelUI() {
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
      chatMessages.innerHTML = '<div class="no-messages-message">没有消息</div>';
    }
  }
  
  /**
   * 更新聊天面板UI
   */
  updateChatPanelUI() {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) {
      return;
    }
    
    if (this.messages.length === 0) {
      chatMessages.innerHTML = '<div class="no-messages-message">没有消息</div>';
      return;
    }
    
    // 保存滚动位置
    const isScrolledToBottom = chatMessages.scrollHeight - chatMessages.clientHeight <= chatMessages.scrollTop + 1;
    
    // 清空消息列表
    chatMessages.innerHTML = '';
    
    // 添加消息项
    for (const message of this.messages) {
      const messageElement = document.createElement('div');
      messageElement.className = `chat-message ${message.isSystem ? 'system-message' : ''}`;
      
      if (message.isSystem) {
        // 系统消息
        messageElement.innerHTML = `
          <div class="message-content system-content">${message.content}</div>
        `;
      } else {
        // 用户消息
        const user = this.userManager.getUserById(message.userId);
        const userName = user ? user.username : '未知用户';
        const userAvatar = user ? user.avatar : 'assets/images/avatar-default.png';
        
        // 格式化时间
        const time = new Date(message.timestamp).toLocaleTimeString();
        
        messageElement.innerHTML = `
          <div class="message-header">
            <img src="${userAvatar}" alt="${userName}" class="message-avatar">
            <div class="message-sender">${userName}</div>
            <div class="message-time">${time}</div>
          </div>
          <div class="message-content">${this.formatMessageContent(message.content)}</div>
        `;
      }
      
      chatMessages.appendChild(messageElement);
    }
    
    // 如果之前滚动到底部，保持滚动到底部
    if (isScrolledToBottom) {
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }
  }
  
  /**
   * 格式化消息内容
   * @param {string} content 消息内容
   * @returns {string} 格式化后的HTML
   */
  formatMessageContent(content) {
    if (!content) {
      return '';
    }
    
    // 转义HTML
    let html = content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
    
    // 将URL转换为链接
    html = html.replace(
      /(https?:\/\/[^\s]+)/g,
      '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'
    );
    
    // 将换行符转换为<br>
    html = html.replace(/\n/g, '<br>');
    
    return html;
  }
  
  /**
   * 添加事件监听器
   * @param {string} event 事件类型
   * @param {Function} listener 监听器函数
   */
  on(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    
    this.eventListeners.get(event).push(listener);
  }
  
  /**
   * 移除事件监听器
   * @param {string} event 事件类型
   * @param {Function} listener 监听器函数
   */
  off(event, listener) {
    if (!this.eventListeners.has(event)) {
      return;
    }
    
    const listeners = this.eventListeners.get(event);
    const index = listeners.indexOf(listener);
    
    if (index !== -1) {
      listeners.splice(index, 1);
    }
  }
  
  /**
   * 触发事件
   * @param {string} event 事件类型
   * @param {...*} args 事件参数
   */
  emit(event, ...args) {
    if (!this.eventListeners.has(event)) {
      return;
    }
    
    const listeners = this.eventListeners.get(event);
    
    for (const listener of listeners) {
      listener(...args);
    }
  }
}
