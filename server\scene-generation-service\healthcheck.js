const http = require('http');

const options = {
  hostname: 'localhost',
  port: 3004,
  path: '/health',
  method: 'GET',
  timeout: 10000 // 增加超时时间到10秒
};

const req = http.request(options, (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      if (res.statusCode === 200) {
        const healthData = JSON.parse(data);
        // 接受 ok 和 degraded 状态，只有 error 才认为不健康
        if (healthData.status === 'ok' || healthData.status === 'degraded') {
          console.log(`Health check passed: ${healthData.status}`);
          process.exit(0);
        } else {
          console.log('Health check failed:', healthData);
          process.exit(1);
        }
      } else {
        console.log(`Health check failed with status: ${res.statusCode}`);
        process.exit(1);
      }
    } catch (error) {
      console.log('Failed to parse health check response:', error);
      process.exit(1);
    }
  });
});

req.on('error', (err) => {
  console.log(`Health check failed: ${err.message}`);
  process.exit(1);
});

req.on('timeout', () => {
  console.log('Health check timeout');
  req.destroy();
  process.exit(1);
});

req.end();
