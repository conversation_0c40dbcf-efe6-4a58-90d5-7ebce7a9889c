/**
 * 动画实例化系统
 * 用于优化大量相同动画的性能
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { AnimationClip } from './AnimationClip';
import { System } from '../core/System';
import type { World } from '../core/World';
/**
 * 动画实例配置
 */
export interface AnimationInstanceConfig {
    /** 动画片段 */
    clip: AnimationClip | THREE.AnimationClip;
    /** 时间偏移 */
    timeOffset?: number;
    /** 时间缩放 */
    timeScale?: number;
    /** 权重 */
    weight?: number;
    /** 是否循环 */
    loop?: boolean;
}
/**
 * 动画实例组件
 */
export declare class AnimationInstanceComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 实例组ID */
    private _groupId;
    /** 配置 */
    private _config;
    /** 当前时间 */
    private _time;
    /** 是否启用 */
    private _enabled;
    /** 是否暂停 */
    private _paused;
    /** 实例索引 */
    private _instanceIndex;
    /** 变换矩阵 */
    private _matrix;
    /** 骨骼矩阵 */
    private _boneMatrices;
    /** 骨骼数量 */
    private _boneCount;
    /**
     * 创建动画实例组件
     * @param groupId 实例组ID
     * @param config 配置
     */
    constructor(groupId: string, config: AnimationInstanceConfig);
    /**
     * 获取实例组ID
     * @returns 实例组ID
     */
    getGroupId(): string;
    /**
     * 获取配置
     * @returns 配置
     */
    getConfig(): AnimationInstanceConfig;
    /**
     * 设置配置
     * @param config 配置
     */
    setConfig(config: AnimationInstanceConfig): void;
    /**
     * 获取当前时间
     * @returns 当前时间
     */
    getTime(): number;
    /**
     * 设置当前时间
     * @param time 当前时间
     */
    setTime(time: number): void;
    /**
     * 是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 设置启用状态
     * @param enabled 启用状态
     */
    setEnabled(enabled: boolean): void;
    /**
     * 是否暂停
     * @returns 是否暂停
     */
    isPaused(): boolean;
    /**
     * 设置暂停状态
     * @param paused 暂停状态
     */
    setPaused(paused: boolean): void;
    /**
     * 获取实例索引
     * @returns 实例索引
     */
    getInstanceIndex(): number;
    /**
     * 设置实例索引
     * @param index 实例索引
     */
    setInstanceIndex(index: number): void;
    /**
     * 获取变换矩阵
     * @returns 变换矩阵
     */
    getMatrix(): THREE.Matrix4;
    /**
     * 设置变换矩阵
     * @param matrix 变换矩阵
     */
    setMatrix(matrix: THREE.Matrix4): void;
    /**
     * 获取骨骼矩阵
     * @returns 骨骼矩阵
     */
    getBoneMatrices(): Float32Array | null;
    /**
     * 设置骨骼矩阵
     * @param matrices 骨骼矩阵
     * @param count 骨骼数量
     */
    setBoneMatrices(matrices: Float32Array, count: number): void;
    /**
     * 获取骨骼数量
     * @returns 骨骼数量
     */
    getBoneCount(): number;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
/**
 * 动画实例组
 */
export interface AnimationInstanceGroup {
    /** 组ID */
    id: string;
    /** 动画片段 */
    clip: AnimationClip | THREE.AnimationClip;
    /** 实例列表 */
    instances: AnimationInstanceComponent[];
    /** 实例化网格 */
    instancedMesh?: THREE.InstancedMesh;
    /** 实例化骨骼 */
    instancedSkeleton?: THREE.InstancedBufferAttribute;
    /** 骨骼数量 */
    boneCount: number;
    /** 是否需要更新 */
    needsUpdate: boolean;
}
/**
 * 动画实例化系统配置
 */
export interface AnimationInstancingSystemConfig {
    /** 是否启用GPU蒙皮 */
    useGPUSkinning?: boolean;
    /** 是否使用实例化网格 */
    useInstancedMesh?: boolean;
    /** 最大实例数量 */
    maxInstances?: number;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 动画实例化系统
 * 用于管理和优化大量相同动画的性能
 */
export declare class AnimationInstancingSystem extends System {
    /** 配置 */
    private config;
    /** 实例组映射 */
    private instanceGroups;
    /** 实例组件列表 */
    private instanceComponents;
    /** 事件发射器 */
    private eventEmitter;
    /** 着色器材质映射 */
    private shaderMaterials;
    /** 是否支持GPU蒙皮 */
    private supportsGPUSkinning;
    /**
     * 创建动画实例化系统
     * @param world 世界
     * @param config 配置
     */
    constructor(_world: World, config?: AnimationInstancingSystemConfig);
    /**
     * 检查是否支持GPU蒙皮
     */
    private checkGPUSkinningSupport;
    /**
     * 创建实例组
     * @param id 组ID
     * @param clip 动画片段
     * @returns 实例组
     */
    createInstanceGroup(id: string, clip: AnimationClip | THREE.AnimationClip): AnimationInstanceGroup;
    /**
     * 创建实例组件
     * @param entity 实体
     * @param groupId 组ID
     * @param config 配置
     * @returns 实例组件
     */
    createInstance(entity: Entity, groupId: string, config: AnimationInstanceConfig): AnimationInstanceComponent;
    /**
     * 移除实例组件
     * @param entity 实体
     * @returns 是否成功移除
     */
    removeInstance(entity: Entity): boolean;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新实例组
     * @param group 实例组
     */
    private updateInstanceGroup;
}
