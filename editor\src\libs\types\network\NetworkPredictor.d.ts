/**
 * 网络预测器
 * 用于减少网络延迟影响，提供位置、旋转和其他属性的预测功能
 */
import * as THREE from 'three';
/**
 * 预测算法类型
 */
export declare enum PredictionAlgorithm {
    /** 线性预测 */
    LINEAR = "linear",
    /** 二阶预测 */
    QUADRATIC = "quadratic",
    /** 卡尔曼滤波 */
    KALMAN = "kalman",
    /** 自适应预测 */
    ADAPTIVE = "adaptive"
}
/**
 * 预测配置接口
 */
export interface PredictionConfig {
    /** 预测算法 */
    algorithm?: PredictionAlgorithm;
    /** 最大预测时间（毫秒） */
    maxPredictionTime?: number;
    /** 是否使用平滑 */
    useSmoothing?: boolean;
    /** 平滑因子（0-1） */
    smoothingFactor?: number;
    /** 是否使用自适应预测 */
    useAdaptivePrediction?: boolean;
    /** 是否使用抖动缓冲 */
    useJitterBuffer?: boolean;
    /** 抖动缓冲大小（毫秒） */
    jitterBufferSize?: number;
}
/**
 * 预测状态接口
 */
export interface PredictionState {
    /** 位置 */
    position: THREE.Vector3;
    /** 旋转 */
    rotation: THREE.Quaternion;
    /** 速度 */
    velocity: THREE.Vector3;
    /** 角速度 */
    angularVelocity: THREE.Vector3;
    /** 加速度 */
    acceleration: THREE.Vector3;
    /** 角加速度 */
    angularAcceleration: THREE.Vector3;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 网络预测器类
 */
export declare class NetworkPredictor {
    /** 配置 */
    private config;
    /** 历史状态 */
    private history;
    /** 最大历史记录数 */
    private readonly MAX_HISTORY_SIZE;
    /** 卡尔曼滤波器状态 - 位置 */
    private kalmanPosition;
    /** 卡尔曼滤波器状态 - 旋转 */
    private kalmanRotation;
    /**
     * 创建网络预测器
     * @param config 预测配置
     */
    constructor(config?: PredictionConfig);
    /**
     * 初始化卡尔曼滤波器
     */
    private initKalmanFilter;
    /**
     * 创建单位矩阵
     * @param size 矩阵大小
     * @param value 对角线值
     * @returns 单位矩阵
     */
    private createIdentityMatrix;
    /**
     * 创建状态转移矩阵
     * @returns 状态转移矩阵
     */
    private createStateTransitionMatrix;
    /**
     * 创建测量矩阵
     * @returns 测量矩阵
     */
    private createMeasurementMatrix;
    /**
     * 创建旋转状态转移矩阵
     * @returns 旋转状态转移矩阵
     */
    private createRotationStateTransitionMatrix;
    /**
     * 创建旋转测量矩阵
     * @returns 旋转测量矩阵
     */
    private createRotationMeasurementMatrix;
    /**
     * 矩阵乘法
     * @param A 矩阵A
     * @param b 向量b
     * @returns 结果向量
     */
    private matrixMultiply;
    /**
     * 计算速度
     * @param position 当前位置
     * @param timestamp 时间戳
     * @returns 计算的速度
     */
    private calculateVelocity;
    /**
     * 计算角速度
     * @param rotation 当前旋转
     * @param timestamp 时间戳
     * @returns 计算的角速度
     */
    private calculateAngularVelocity;
    /**
     * 计算加速度
     * @param velocity 当前速度
     * @param timestamp 时间戳
     * @returns 计算的加速度
     */
    private calculateAcceleration;
    /**
     * 计算角加速度
     * @param angularVelocity 当前角速度
     * @param timestamp 时间戳
     * @returns 计算的角加速度
     */
    private calculateAngularAcceleration;
    /**
     * 更新卡尔曼滤波器
     * @param state 预测状态
     */
    private updateKalmanFilter;
    /**
     * 更新位置卡尔曼滤波器
     * @param state 预测状态
     */
    private updatePositionKalmanFilter;
    /**
     * 更新旋转卡尔曼滤波器
     * @param state 预测状态
     */
    private updateRotationKalmanFilter;
    /**
     * 线性预测位置
     * @param state 预测状态
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测位置
     */
    private linearPredictPosition;
    /**
     * 二阶预测位置
     * @param state 预测状态
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测位置
     */
    private quadraticPredictPosition;
    /**
     * 卡尔曼预测位置
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测位置
     */
    private kalmanPredictPosition;
    /**
     * 自适应预测位置
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测位置
     */
    private adaptivePredictPosition;
    /**
     * 线性预测旋转
     * @param state 预测状态
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测旋转
     */
    private linearPredictRotation;
    /**
     * 二阶预测旋转
     * @param state 预测状态
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测旋转
     */
    private quadraticPredictRotation;
    /**
     * 卡尔曼预测旋转
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测旋转
     */
    private kalmanPredictRotation;
    /**
     * 自适应预测旋转
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测旋转
     */
    private adaptivePredictRotation;
    /**
     * 更新预测状态
     * @param position 当前位置
     * @param rotation 当前旋转
     * @param velocity 当前速度
     * @param angularVelocity 当前角速度
     * @param timestamp 时间戳
     */
    update(position: THREE.Vector3, rotation: THREE.Quaternion, velocity?: THREE.Vector3, angularVelocity?: THREE.Vector3, timestamp?: number): void;
    /**
     * 预测位置
     * @param latency 延迟（毫秒）
     * @returns 预测位置
     */
    predictPosition(latency: number): THREE.Vector3;
    /**
     * 预测旋转
     * @param latency 延迟（毫秒）
     * @returns 预测旋转
     */
    predictRotation(latency: number): THREE.Quaternion;
}
