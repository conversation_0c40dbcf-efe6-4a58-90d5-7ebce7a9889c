/**
 * 输入组件单元测试
 */
import { Entity } from '../../src/core/Entity';
import { InputComponent } from '../../src/input/components/InputComponent';
import { ButtonInputAction } from '../../src/input/InputAction';
import { ButtonInputMapping } from '../../src/input/InputMapping';
import { InputBinding } from '../../src/input/InputBinding';
import { InputManager } from '../../src/input/InputManager';
import { InputDevice } from '../../src/input/InputDevice';

// 模拟实体
class MockEntity extends Entity {
  constructor(id: string = 'test') {
    super(id);
  }
}

// 模拟输入设备
class MockInputDevice implements InputDevice {
  private values: Map<string, any> = new Map();

  constructor(private name: string) {}

  getName(): string {
    return this.name;
  }

  initialize(): void {}
  update(deltaTime: number): void {}
  destroy(): void {}

  getValue(key: string): any {
    return this.values.get(key);
  }

  setValue(key: string, value: any): void {
    this.values.set(key, value);
  }

  hasKey(key: string): boolean {
    return this.values.has(key);
  }

  getKeys(): string[] {
    return Array.from(this.values.keys());
  }

  on(event: string, callback: Function): void {}
  off(event: string, callback: Function): void {}
}

// 模拟输入管理器
jest.mock('../../src/input/InputManager', () => {
  const mockDevice = new MockInputDevice('test');
  
  return {
    InputManager: {
      getInstance: jest.fn().mockImplementation(() => ({
        getDevice: jest.fn().mockImplementation((name: string) => {
          if (name === 'test') {
            return mockDevice;
          }
          return undefined;
        })
      }))
    }
  };
});

describe('InputComponent', () => {
  let entity: MockEntity;
  let component: InputComponent;
  let action: ButtonInputAction;
  let mapping: ButtonInputMapping;
  let binding: InputBinding;

  beforeEach(() => {
    entity = new MockEntity();
    action = new ButtonInputAction('testAction');
    mapping = new ButtonInputMapping('testMapping', 'test', 'button1');
    binding = new InputBinding('testAction', 'testMapping');

    component = new InputComponent(entity, {
      actions: [action],
      mappings: [mapping],
      bindings: [binding]
    });
  });

  test('应该正确初始化输入组件', () => {
    expect(component.isEnabled()).toBe(true);
    expect(component.getAction('testAction')).toBe(action);
    expect(component.getMapping('testMapping')).toBe(mapping);
    expect(component.getBinding('testAction')).toBe(binding);
  });

  test('应该正确添加和获取动作', () => {
    const newAction = new ButtonInputAction('newAction');
    component.addAction(newAction);
    expect(component.getAction('newAction')).toBe(newAction);
    expect(component.getActions()).toContain(newAction);
  });

  test('应该正确添加和获取映射', () => {
    const newMapping = new ButtonInputMapping('newMapping', 'test', 'button2');
    component.addMapping(newMapping);
    expect(component.getMapping('newMapping')).toBe(newMapping);
    expect(component.getMappings()).toContain(newMapping);
  });

  test('应该正确添加和获取绑定', () => {
    const newBinding = new InputBinding('newBinding', 'newMapping');
    component.addBinding(newBinding);
    expect(component.getBinding('newBinding')).toBe(newBinding);
    expect(component.getBindings()).toContain(newBinding);
  });

  test('应该正确启用和禁用组件', () => {
    expect(component.isEnabled()).toBe(true);
    
    component.setEnabled(false);
    expect(component.isEnabled()).toBe(false);
    
    component.setEnabled(true);
    expect(component.isEnabled()).toBe(true);
  });

  test('应该正确清除动作、映射和绑定', () => {
    component.clearActions();
    expect(component.getActions().length).toBe(0);
    
    component.addAction(action);
    expect(component.getActions().length).toBe(1);
    
    component.clearMappings();
    expect(component.getMappings().length).toBe(0);
    
    component.addMapping(mapping);
    expect(component.getMappings().length).toBe(1);
    
    component.clearBindings();
    expect(component.getBindings().length).toBe(0);
    
    component.addBinding(binding);
    expect(component.getBindings().length).toBe(1);
    
    component.clearAll();
    expect(component.getActions().length).toBe(0);
    expect(component.getMappings().length).toBe(0);
    expect(component.getBindings().length).toBe(0);
  });
});
