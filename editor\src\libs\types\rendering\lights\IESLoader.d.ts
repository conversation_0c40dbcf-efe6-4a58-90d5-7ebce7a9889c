/**
 * IES光源加载器
 * 用于加载和解析IES文件
 */
import * as THREE from 'three';
/**
 * IES光源加载器类
 */
export declare class IESLoader {
    /** 加载管理器 */
    private manager;
    /** 文件加载器 */
    private fileLoader;
    /** 纹理大小 */
    private size;
    /**
     * 创建IES光源加载器
     * @param manager 加载管理器
     * @param size 纹理大小
     */
    constructor(manager?: THREE.LoadingManager, size?: number);
    /**
     * 加载IES文件
     * @param url IES文件路径
     * @param onLoad 加载完成回调
     * @param onProgress 加载进度回调
     * @param onError 加载错误回调
     */
    load(url: string, onLoad?: (texture: THREE.Texture) => void, onProgress?: (event: ProgressEvent) => void, onError?: (event: ErrorEvent) => void): void;
    /**
     * 解析IES数据
     * @param text IES文件数据
     * @returns IES纹理
     */
    parse(text: string): THREE.Texture;
    /**
     * 解析IES数据
     * @param text IES文件数据
     * @returns IES数据
     */
    private parseIESData;
    /**
     * 创建IES纹理
     * @param iesData IES数据
     * @returns IES纹理
     */
    private createIESTexture;
    /**
     * 绘制Type C光度数据
     * @param context 画布上下文
     * @param maxCandela 最大光强
     * @param vertical 垂直角度
     * @param horizontal 水平角度
     * @param candelaValues 光强值
     */
    private drawTypeC;
    /**
     * 绘制Type B光度数据
     * @param context 画布上下文
     * @param maxCandela 最大光强
     * @param vertical 垂直角度
     * @param horizontal 水平角度
     * @param candelaValues 光强值
     */
    private drawTypeB;
    /**
     * 绘制Type A光度数据
     * @param context 画布上下文
     * @param maxCandela 最大光强
     * @param vertical 垂直角度
     * @param horizontal 水平角度
     * @param candelaValues 光强值
     */
    private drawTypeA;
    /**
     * 插值获取Type C光强
     * @param phi 水平角度
     * @param theta 垂直角度
     * @param horizontal 水平角度数组
     * @param vertical 垂直角度数组
     * @param candelaValues 光强值数组
     * @param maxCandela 最大光强
     * @returns 归一化光强
     */
    private interpolateCandelaTypeC;
    /**
     * 设置纹理大小
     * @param size 纹理大小
     */
    setSize(size: number): void;
    /**
     * 获取纹理大小
     * @returns 纹理大小
     */
    getSize(): number;
}
