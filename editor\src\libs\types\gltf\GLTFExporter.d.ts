/**
 * GLTF导出器
 * 用于导出场景或对象为GLTF格式
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { Scene } from '../scene/Scene';
import { EventEmitter } from '../utils/EventEmitter';
/**
 * GLTF导出选项
 */
export interface GLTFExportOptions {
    /** 是否导出动画 */
    animations?: THREE.AnimationClip[];
    /** 是否二进制格式 */
    binary?: boolean;
    /** 是否包含自定义属性 */
    includeCustomExtensions?: boolean;
    /** 是否仅导出可见对象 */
    onlyVisible?: boolean;
    /** 是否压缩几何体 */
    truncateDrawRange?: boolean;
    /** 是否强制索引 */
    forceIndices?: boolean;
    /** 最大UV数量 */
    maxTextureSize?: number;
    /** 是否导出变换 */
    exportTransforms?: boolean;
}
/**
 * GLTF导出器
 */
export declare class GLTFExporter extends EventEmitter {
    /** Three.js GLTF导出器 */
    private exporter;
    /**
     * 创建GLTF导出器
     */
    constructor();
    /**
     * 导出场景
     * @param scene 场景
     * @param options 导出选项
     * @returns Promise，解析为导出的GLTF数据
     */
    exportScene(scene: Scene, options?: GLTFExportOptions): Promise<ArrayBuffer | object>;
    /**
     * 导出实体
     * @param entity 实体
     * @param options 导出选项
     * @returns Promise，解析为导出的GLTF数据
     */
    exportEntity(entity: Entity, options?: GLTFExportOptions): Promise<ArrayBuffer | object>;
    /**
     * 导出对象
     * @param input 输入对象（Three.js对象、场景或数组）
     * @param options 导出选项
     * @returns Promise，解析为导出的GLTF数据
     */
    export(input: THREE.Object3D | THREE.Object3D[] | THREE.Scene, options?: GLTFExportOptions): Promise<ArrayBuffer | object>;
    /**
     * 将GLTF数据保存为文件
     * @param gltf GLTF数据
     * @param filename 文件名
     */
    saveAs(gltf: ArrayBuffer | object, filename: string): void;
}
