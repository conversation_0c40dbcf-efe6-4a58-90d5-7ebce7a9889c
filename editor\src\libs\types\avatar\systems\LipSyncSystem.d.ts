import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { LipSyncComponent } from '../components/LipSyncComponent';
/**
 * 口型同步系统配置
 */
export interface LipSyncSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** FFT大小 */
    fftSize?: number;
    /** 音量阈值 */
    volumeThreshold?: number;
    /** 分析间隔（毫秒） */
    analysisInterval?: number;
    /** 是否使用Web Worker */
    useWorker?: boolean;
    /** 频率带数量 */
    numFrequencyBands?: number;
    /** 是否使用高级分析器 */
    useAdvancedAnalyzer?: boolean;
    /** 是否使用AI预测 */
    useAIPrediction?: boolean;
    /** 是否使用GPU */
    useGPU?: boolean;
}
/**
 * 口型同步系统
 */
export declare class LipSyncSystem extends System {
    /** 系统名称 */
    static readonly NAME = "LipSyncSystem";
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 口型同步组件映射 */
    private components;
    /** 音频上下文 */
    private audioContext;
    /** 音频分析器 */
    private audioAnalyser;
    /** 音频源 */
    private audioSource;
    /** 频谱数据 */
    private spectrum;
    /** 频率带 */
    private frequencyBands;
    /** 分析计时器 */
    private analysisTimer;
    /** 是否支持GPU */
    private supportsGPU;
    /** GPU计算着色器 */
    private gpuComputeShader;
    /** 高级分析器 */
    private advancedAnalyzer;
    /** AI预测器 */
    private aiPredictor;
    /** Web Worker */
    private worker;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: LipSyncSystemConfig);
    /**
     * 初始化音频上下文
     */
    private initAudioContext;
    /**
     * 初始化频率带
     */
    private initFrequencyBands;
    /**
     * 检查GPU支持
     */
    private checkGPUSupport;
    /**
     * 初始化高级分析器
     */
    private initAdvancedAnalyzer;
    /**
     * 初始化AI预测器
     */
    private initAIPredictor;
    /**
     * 初始化Web Worker
     */
    private initWorker;
    /**
     * 创建口型同步组件
     * @param entity 实体
     * @returns 口型同步组件
     */
    createLipSync(entity: Entity): LipSyncComponent;
    /**
     * 获取口型同步组件
     * @param entity 实体
     * @returns 口型同步组件
     */
    getLipSync(entity: Entity): LipSyncComponent | null;
    /**
     * 移除口型同步组件
     * @param entity 实体
     */
    removeLipSync(entity: Entity): void;
    /**
     * 设置所有实体的口型
     * @param viseme 口型类型
     * @param weight 权重
     */
    private setVisemeForAllEntities;
    /**
     * 计算RMS（均方根）
     * @param spectrum 频谱数据
     * @returns RMS值
     */
    private calculateRMS;
    /**
     * 计算频率带能量
     */
    private calculateFrequencyBands;
    /**
     * 分析频率带并确定口型
     * @returns 口型类型
     */
    private analyzeFrequencyBands;
    /**
     * 处理音频
     */
    private processAudio;
    /**
     * 使用CPU处理音频数据
     */
    private processAudioWithCPU;
    /**
     * 使用GPU处理音频数据
     */
    private processAudioWithGPU;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
}
