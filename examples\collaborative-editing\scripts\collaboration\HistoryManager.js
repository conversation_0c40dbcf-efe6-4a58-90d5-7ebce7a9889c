/**
 * 历史管理器
 * 负责管理操作历史记录和撤销/重做功能
 */
export class HistoryManager {
  /**
   * 构造函数
   * @param {OperationManager} operationManager 操作管理器
   */
  constructor(operationManager) {
    this.operationManager = operationManager;
    this.history = []; // 操作历史
    this.currentIndex = -1; // 当前历史索引
    this.maxHistorySize = 100; // 最大历史记录数量
    
    // 绑定方法
    this.addOperation = this.addOperation.bind(this);
    this.undo = this.undo.bind(this);
    this.redo = this.redo.bind(this);
    
    // 设置事件监听器
    this.operationManager.on('operationApplied', this.addOperation);
    
    // 初始化历史面板UI
    this.initializeHistoryPanelUI();
  }
  
  /**
   * 添加操作到历史记录
   * @param {string} userId 用户ID
   * @param {Object} operation 操作对象
   */
  addOperation(operation) {
    // 如果当前索引不是最后一个，删除后面的历史记录
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1);
    }
    
    // 添加操作到历史记录
    this.history.push(operation);
    this.currentIndex = this.history.length - 1;
    
    // 如果历史记录超过最大数量，删除最早的记录
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
      this.currentIndex--;
    }
    
    // 更新历史面板UI
    this.updateHistoryPanelUI();
    
    // 更新撤销/重做按钮状态
    this.updateUndoRedoButtonState();
  }
  
  /**
   * 撤销操作
   */
  undo() {
    if (this.currentIndex < 0) {
      console.warn('没有可撤销的操作');
      return;
    }
    
    // 获取当前操作
    const operation = this.history[this.currentIndex];
    
    // 创建反向操作
    const inverseOperation = this.createInverseOperation(operation);
    
    if (inverseOperation) {
      // 应用反向操作
      this.operationManager.applyOperation(inverseOperation);
      
      // 更新当前索引
      this.currentIndex--;
      
      // 更新历史面板UI
      this.updateHistoryPanelUI();
      
      // 更新撤销/重做按钮状态
      this.updateUndoRedoButtonState();
    }
  }
  
  /**
   * 重做操作
   */
  redo() {
    if (this.currentIndex >= this.history.length - 1) {
      console.warn('没有可重做的操作');
      return;
    }
    
    // 更新当前索引
    this.currentIndex++;
    
    // 获取要重做的操作
    const operation = this.history[this.currentIndex];
    
    // 应用操作
    this.operationManager.applyOperation(operation);
    
    // 更新历史面板UI
    this.updateHistoryPanelUI();
    
    // 更新撤销/重做按钮状态
    this.updateUndoRedoButtonState();
  }
  
  /**
   * 创建反向操作
   * @param {Object} operation 操作对象
   * @returns {Object|null} 反向操作对象
   */
  createInverseOperation(operation) {
    switch (operation.type) {
      case 'create':
        // 创建操作的反向是删除
        return {
          id: this.generateOperationId(),
          type: 'delete',
          data: {
            entityId: operation.data.entityData.id
          },
          timestamp: Date.now(),
          userId: operation.userId
        };
      
      case 'delete':
        // 删除操作的反向是创建
        // 需要保存实体数据才能撤销
        if (operation.data.entityData) {
          return {
            id: this.generateOperationId(),
            type: 'create',
            data: {
              entityData: operation.data.entityData
            },
            timestamp: Date.now(),
            userId: operation.userId
          };
        }
        return null;
      
      case 'transform':
        // 变换操作的反向是应用原始变换
        if (operation.data.originalValue) {
          return {
            id: this.generateOperationId(),
            type: 'transform',
            data: {
              entityId: operation.data.entityId,
              transformType: operation.data.transformType,
              value: operation.data.originalValue
            },
            timestamp: Date.now(),
            userId: operation.userId
          };
        }
        return null;
      
      case 'property':
        // 属性操作的反向是应用原始属性值
        if (operation.data.originalValue !== undefined) {
          return {
            id: this.generateOperationId(),
            type: 'property',
            data: {
              entityId: operation.data.entityId,
              propertyPath: operation.data.propertyPath,
              value: operation.data.originalValue
            },
            timestamp: Date.now(),
            userId: operation.userId
          };
        }
        return null;
      
      default:
        return null;
    }
  }
  
  /**
   * 生成操作ID
   * @returns {string} 操作ID
   */
  generateOperationId() {
    return 'op_' + Math.random().toString(36).substr(2, 9);
  }
  
  /**
   * 初始化历史面板UI
   */
  initializeHistoryPanelUI() {
    const historyList = document.getElementById('history-list');
    if (historyList) {
      historyList.innerHTML = '<div class="no-history-message">没有操作历史</div>';
    }
    
    // 更新撤销/重做按钮状态
    this.updateUndoRedoButtonState();
  }
  
  /**
   * 更新历史面板UI
   */
  updateHistoryPanelUI() {
    const historyList = document.getElementById('history-list');
    if (!historyList) {
      return;
    }
    
    if (this.history.length === 0) {
      historyList.innerHTML = '<div class="no-history-message">没有操作历史</div>';
      return;
    }
    
    // 清空历史列表
    historyList.innerHTML = '';
    
    // 添加历史项
    for (let i = 0; i < this.history.length; i++) {
      const operation = this.history[i];
      const isCurrent = i === this.currentIndex;
      
      const historyItem = document.createElement('div');
      historyItem.className = `history-item ${isCurrent ? 'current' : ''}`;
      historyItem.setAttribute('data-index', i);
      
      // 格式化时间
      const time = new Date(operation.timestamp).toLocaleTimeString();
      
      // 获取用户名
      const userName = operation.userId || '未知用户';
      
      // 格式化操作描述
      const actionDescription = this.formatOperationDescription(operation);
      
      historyItem.innerHTML = `
        <div class="history-time">${time}</div>
        <div class="history-user">${userName}</div>
        <div class="history-action">${actionDescription}</div>
      `;
      
      // 添加点击事件
      historyItem.addEventListener('click', () => {
        this.jumpToHistoryIndex(i);
      });
      
      historyList.appendChild(historyItem);
    }
    
    // 滚动到当前项
    const currentItem = historyList.querySelector('.history-item.current');
    if (currentItem) {
      currentItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
  }
  
  /**
   * 更新撤销/重做按钮状态
   */
  updateUndoRedoButtonState() {
    const undoButton = document.getElementById('undo-button');
    const redoButton = document.getElementById('redo-button');
    
    if (undoButton) {
      undoButton.disabled = this.currentIndex < 0;
    }
    
    if (redoButton) {
      redoButton.disabled = this.currentIndex >= this.history.length - 1;
    }
  }
  
  /**
   * 跳转到历史索引
   * @param {number} index 历史索引
   */
  jumpToHistoryIndex(index) {
    if (index < 0 || index >= this.history.length) {
      return;
    }
    
    if (index === this.currentIndex) {
      return;
    }
    
    if (index < this.currentIndex) {
      // 向后撤销
      while (this.currentIndex > index) {
        this.undo();
      }
    } else {
      // 向前重做
      while (this.currentIndex < index) {
        this.redo();
      }
    }
  }
  
  /**
   * 格式化操作描述
   * @param {Object} operation 操作对象
   * @returns {string} 操作描述
   */
  formatOperationDescription(operation) {
    switch (operation.type) {
      case 'create':
        return `创建了 ${operation.data.entityData.name || '实体'}`;
      
      case 'delete':
        return `删除了 实体`;
      
      case 'transform':
        const transformType = this.getTransformTypeName(operation.data.transformType);
        return `修改了 实体 的${transformType}`;
      
      case 'property':
        return `修改了 实体 的属性 ${operation.data.propertyPath}`;
      
      default:
        return `执行了 ${operation.type} 操作`;
    }
  }
  
  /**
   * 获取变换类型名称
   * @param {string} type 变换类型
   * @returns {string} 类型名称
   */
  getTransformTypeName(type) {
    switch (type) {
      case 'position':
        return '位置';
      case 'rotation':
        return '旋转';
      case 'scale':
        return '缩放';
      default:
        return type;
    }
  }
}
