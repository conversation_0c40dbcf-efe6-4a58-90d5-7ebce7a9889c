/**
 * 服务注册中心主控制器
 */
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('服务注册中心')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: '获取服务注册中心信息' })
  getInfo() {
    return this.appService.getInfo();
  }
}
