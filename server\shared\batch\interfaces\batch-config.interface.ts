/**
 * 批处理配置
 */
export interface BatchConfig {
  /**
   * 是否启用批处理
   * @default true
   */
  enabled?: boolean;

  /**
   * 最大批处理大小
   * @default 100
   */
  maxBatchSize?: number;

  /**
   * 最大等待时间（毫秒）
   * @default 50
   */
  maxWaitTime?: number;

  /**
   * 最大队列大小
   * @default 1000
   */
  maxQueueSize?: number;

  /**
   * 是否启用统计
   * @default true
   */
  enableStats?: boolean;

  /**
   * 是否启用调试
   * @default false
   */
  debug?: boolean;
}

/**
 * 批处理统计
 */
export interface BatchStats {
  /**
   * 处理的批次数
   */
  batchCount: number;

  /**
   * 处理的项目数
   */
  itemCount: number;

  /**
   * 平均批处理大小
   */
  avgBatchSize: number;

  /**
   * 平均批处理时间（毫秒）
   */
  avgBatchTime: number;

  /**
   * 平均等待时间（毫秒）
   */
  avgWaitTime: number;

  /**
   * 当前队列大小
   */
  queueSize: number;

  /**
   * 队列满次数
   */
  queueFullCount: number;

  /**
   * 错误次数
   */
  errorCount: number;
}
