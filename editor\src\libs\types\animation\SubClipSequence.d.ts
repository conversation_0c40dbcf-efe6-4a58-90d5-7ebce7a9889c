import { SubClip } from './SubClip';
import { AnimationSubClip } from './AnimationSubClip';
/**
 * 子片段序列事件类型
 */
export declare enum SubClipSequenceEventType {
    /** 序列开始 */
    SEQUENCE_START = "sequenceStart",
    /** 序列结束 */
    SEQUENCE_END = "sequenceEnd",
    /** 子片段开始 */
    CLIP_START = "clipStart",
    /** 子片段结束 */
    CLIP_END = "clipEnd",
    /** 序列更新 */
    SEQUENCE_UPDATE = "sequenceUpdate",
    /** 序列循环 */
    SEQUENCE_LOOP = "sequenceLoop"
}
/**
 * 子片段序列项
 */
export interface SubClipSequenceItem {
    /** 子片段 */
    subClip: SubClip | AnimationSubClip;
    /** 持续时间（秒） */
    duration: number;
    /** 过渡时间（秒） */
    transitionTime: number;
    /** 权重 */
    weight: number;
    /** 自定义数据 */
    userData?: any;
}
/**
 * 子片段序列配置
 */
export interface SubClipSequenceConfig {
    /** 序列名称 */
    name?: string;
    /** 是否循环 */
    loop?: boolean;
    /** 是否自动播放 */
    autoPlay?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 子片段序列
 */
export declare class SubClipSequence {
    /** 序列名称 */
    private name;
    /** 是否循环 */
    private loop;
    /** 是否自动播放 */
    private autoPlay;
    /** 是否启用调试 */
    private debug;
    /** 事件发射器 */
    private eventEmitter;
    /** 序列项列表 */
    private items;
    /** 当前索引 */
    private currentIndex;
    /** 是否正在播放 */
    private isPlaying;
    /** 当前时间 */
    private currentTime;
    /** 当前项的开始时间 */
    private currentItemStartTime;
    /** 总持续时间 */
    private totalDuration;
    /** 当前动作 */
    private currentAction;
    /** 下一个动作 */
    private nextAction;
    /**
     * 创建子片段序列
     * @param config 配置
     */
    constructor(config?: SubClipSequenceConfig);
    /**
     * 获取序列名称
     * @returns 序列名称
     */
    getName(): string;
    /**
     * 设置序列名称
     * @param name 序列名称
     */
    setName(name: string): void;
    /**
     * 获取是否循环
     * @returns 是否循环
     */
    getLoop(): boolean;
    /**
     * 设置是否循环
     * @param loop 是否循环
     */
    setLoop(loop: boolean): void;
    /**
     * 获取是否自动播放
     * @returns 是否自动播放
     */
    getAutoPlay(): boolean;
    /**
     * 设置是否自动播放
     * @param autoPlay 是否自动播放
     */
    setAutoPlay(autoPlay: boolean): void;
    /**
     * 添加子片段
     * @param subClip 子片段
     * @param duration 持续时间（秒）
     * @param transitionTime 过渡时间（秒）
     * @param weight 权重
     * @param userData 自定义数据
     * @returns 序列项索引
     */
    addSubClip(subClip: SubClip | AnimationSubClip, duration?: number, transitionTime?: number, weight?: number, userData?: any): number;
    /**
     * 移除子片段
     * @param index 序列项索引
     */
    removeSubClip(index: number): void;
    /**
     * 清空序列
     */
    clear(): void;
    /**
     * 获取序列项列表
     * @returns 序列项列表
     */
    getItems(): SubClipSequenceItem[];
    /**
     * 获取序列项数量
     * @returns 序列项数量
     */
    getItemCount(): number;
    /**
     * 获取当前索引
     * @returns 当前索引
     */
    getCurrentIndex(): number;
    /**
     * 获取当前项
     * @returns 当前项
     */
    getCurrentItem(): SubClipSequenceItem | null;
    /**
     * 获取总持续时间
     * @returns 总持续时间（秒）
     */
    getTotalDuration(): number;
    /**
     * 更新总持续时间
     */
    private updateTotalDuration;
    /**
     * 开始播放序列
     */
    play(): void;
    /**
     * 停止播放序列
     */
    stop(): void;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: SubClipSequenceEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: SubClipSequenceEventType, listener: (data: any) => void): void;
}
