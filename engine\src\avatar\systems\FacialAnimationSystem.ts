/**
 * 面部动画系统
 * 用于管理角色的面部表情和口型同步
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationComponent } from '../components/FacialAnimationComponent';

/**
 * 面部动画系统配置
 */
export interface FacialAnimationSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否自动检测音频 */
  autoDetectAudio?: boolean;
  /** 是否使用摄像头 */
  useWebcam?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
}

/**
 * 面部动画系统
 */
export class FacialAnimationSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'FacialAnimationSystem';

  /** 配置 */
  private config: FacialAnimationSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 面部动画组件映射 */
  private components: Map<Entity, FacialAnimationComponent> = new Map();

  /** 模型适配器系统 */
  private modelAdapterSystem: any = null;

  /** 音频上下文 */
  private audioContext: AudioContext | null = null;

  /** 音频分析器 */
  private audioAnalyser: AnalyserNode | null = null;

  /** 音频源 */
  private audioSource: MediaStreamAudioSourceNode | null = null;

  /** 摄像头视频元素 */
  private videoElement: HTMLVideoElement | null = null;

  /** 摄像头流 */
  private webcamStream: MediaStream | null = null;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: FacialAnimationSystemConfig = {}) {
    super(250); // 设置优先级

    this.config = {
      debug: false,
      autoDetectAudio: false,
      useWebcam: false,
      updateFrequency: 1/30, // 默认30fps
      ...config
    };

    if (this.config.debug) {
      console.log('面部动画系统初始化');
    }

    // 如果启用自动检测音频，初始化音频上下文
    if (this.config.autoDetectAudio) {
      this.initAudioContext();
    }

    // 如果启用摄像头，初始化摄像头
    if (this.config.useWebcam) {
      this.initWebcam();
    }
  }

  /**
   * 初始化音频上下文
   */
  private initAudioContext(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.audioAnalyser = this.audioContext.createAnalyser();
      this.audioAnalyser.fftSize = 1024;
      this.audioAnalyser.smoothingTimeConstant = 0.8;

      // 获取麦克风
      navigator.mediaDevices.getUserMedia({ audio: true, video: false })
        .then((stream) => {
          if (this.audioContext) {
            this.audioSource = this.audioContext.createMediaStreamSource(stream);
            this.audioSource.connect(this.audioAnalyser);

            if (this.config.debug) {
              console.log('音频上下文初始化成功');
            }
          }
        })
        .catch((error) => {
          console.error('获取麦克风失败:', error);
        });
    } catch (error) {
      console.error('初始化音频上下文失败:', error);
    }
  }

  /**
   * 初始化摄像头
   */
  private initWebcam(): void {
    try {
      this.videoElement = document.createElement('video');
      this.videoElement.style.display = 'none';
      document.body.appendChild(this.videoElement);

      navigator.mediaDevices.getUserMedia({ video: true })
        .then((stream) => {
          if (this.videoElement) {
            this.webcamStream = stream;
            this.videoElement.srcObject = stream;
            this.videoElement.play();

            if (this.config.debug) {
              console.log('摄像头初始化成功');
            }
          }
        })
        .catch((error) => {
          console.error('获取摄像头失败:', error);
        });
    } catch (error) {
      console.error('初始化摄像头失败:', error);
    }
  }

  /**
   * 设置模型适配器系统
   * @param system 模型适配器系统
   */
  public setModelAdapterSystem(system: any): void {
    this.modelAdapterSystem = system;
  }

  /**
   * 创建面部动画组件
   * @param entity 实体
   * @returns 面部动画组件
   */
  public createFacialAnimation(entity: Entity): FacialAnimationComponent {
    // 检查实体是否已有面部动画组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建面部动画组件
    const component = new FacialAnimationComponent(entity);

    // 添加组件到实体
    entity.addComponent(component);

    // 添加组件到映射
    this.components.set(entity, component);

    if (this.config.debug) {
      console.log('创建面部动画组件', entity);
    }

    return component;
  }

  /**
   * 获取面部动画组件
   * @param entity 实体
   * @returns 面部动画组件
   */
  public getFacialAnimation(entity: Entity): FacialAnimationComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 移除面部动画组件
   * @param entity 实体
   */
  public removeFacialAnimation(entity: Entity): void {
    const component = this.components.get(entity);
    if (component) {
      entity.removeComponent(component);
      this.components.delete(entity);

      if (this.config.debug) {
        console.log('移除面部动画组件', entity);
      }
    }
  }

  /**
   * 将面部动画组件与模型绑定
   * @param entity 实体
   * @param mesh 骨骼网格
   * @returns 是否成功绑定
   */
  public linkToModel(entity: Entity, mesh: THREE.SkinnedMesh): boolean {
    if (!this.modelAdapterSystem) {
      console.warn('未设置模型适配器系统');
      return false;
    }

    return this.modelAdapterSystem.createModelAdapter(entity, mesh);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有面部动画组件
    for (const [entity, component] of this.components.entries()) {
      // 更新组件
      component.update(deltaTime);

      // 如果有模型适配器系统，应用表情和口型到模型
      if (this.modelAdapterSystem) {
        const adapter = this.modelAdapterSystem.getModelAdapter(entity);
        if (adapter) {
          // 获取当前表情
          const expressionType = component.getCurrentExpression().expression;
          const expressionWeight = component.getCurrentExpression().weight;

          // 获取当前口型
          const visemeType = component.getCurrentViseme().viseme;
          const visemeWeight = component.getCurrentViseme().weight;

          // 应用表情和口型
          adapter.applyExpression(expressionType, expressionWeight);
          adapter.applyViseme(visemeType, visemeWeight);
        }
      }
    }
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 关闭音频上下文
    if (this.audioSource) {
      this.audioSource.disconnect();
      this.audioSource = null;
    }

    if (this.audioAnalyser) {
      this.audioAnalyser = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    // 关闭摄像头
    if (this.webcamStream) {
      this.webcamStream.getTracks().forEach(track => track.stop());
      this.webcamStream = null;
    }

    if (this.videoElement) {
      document.body.removeChild(this.videoElement);
      this.videoElement = null;
    }

    // 清除组件映射
    this.components.clear();

    if (this.config.debug) {
      console.log('面部动画系统销毁');
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }
}
