/**
 * 物理系统兼容性测试配置
 */
import { TestConfig } from '../CompatibilityTestFramework';
import { physicsWorldTest } from './PhysicsWorldTest';
import { rigidBodyTest } from './RigidBodyTest';
import { collisionDetectionTest } from './CollisionDetectionTest';
import { raycastTest } from './RaycastTest';
import { constraintTest } from './ConstraintTest';
import { characterControllerTest } from './CharacterControllerTest';

/**
 * 物理系统兼容性测试配置
 */
export const physicsTestConfig: TestConfig = {
  name: '物理系统兼容性测试',
  description: '测试物理系统的功能兼容性，包括物理世界、刚体、碰撞检测、射线检测、约束和角色控制器',
  originalModulePath: '../../../src/physics',
  refactoredModulePath: '../../../../newsystem/engine/src/physics',
  testCases: [
    physicsWorldTest,
    rigidBodyTest,
    collisionDetectionTest,
    raycastTest,
    constraintTest,
    characterControllerTest
  ]
};
