/**
 * 用户响应DTO
 */
import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
  @ApiProperty({
    description: '用户ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: '用户名',
    example: 'zhangsan',
  })
  username: string;

  @ApiProperty({
    description: '电子邮箱',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: '用户是否激活',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: '用户角色',
    example: ['user'],
    isArray: true,
  })
  roles: string[];

  @ApiProperty({
    description: '创建时间',
    example: '2023-01-01T00:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2023-01-01T00:00:00Z',
  })
  updatedAt: Date;
}
