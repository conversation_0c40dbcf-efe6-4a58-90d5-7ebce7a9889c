/**
 * 网络连接
 * 定义网络连接的抽象接口
 */
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkMessage } from './NetworkMessage';

/**
 * 网络连接状态
 */
export enum NetworkConnectionState {
  /** 已断开连接 */
  DISCONNECTED = 'disconnected',
  /** 正在连接 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 正在断开连接 */
  DISCONNECTING = 'disconnecting',
  /** 连接错误 */
  ERROR = 'error',
}

/**
 * 网络连接
 * 定义网络连接的抽象接口
 */
export abstract class NetworkConnection extends EventEmitter {
  /** 连接状态 */
  protected state: NetworkConnectionState = NetworkConnectionState.DISCONNECTED;
  
  /** 连接ID */
  protected id: string;
  
  /** 远程地址 */
  protected remoteAddress: string;
  
  /** 连接时间 */
  protected connectTime: number = 0;
  
  /** 最后活动时间 */
  protected lastActivityTime: number = 0;
  
  /** 发送的消息数量 */
  protected sentMessages: number = 0;
  
  /** 接收的消息数量 */
  protected receivedMessages: number = 0;
  
  /** 发送的字节数 */
  protected sentBytes: number = 0;
  
  /** 接收的字节数 */
  protected receivedBytes: number = 0;

  /**
   * 创建网络连接
   * @param id 连接ID
   * @param remoteAddress 远程地址
   */
  constructor(id: string, remoteAddress: string) {
    super();
    
    this.id = id;
    this.remoteAddress = remoteAddress;
  }

  /**
   * 连接到远程地址
   * @returns Promise
   */
  public abstract connect(): Promise<void>;

  /**
   * 断开连接
   * @returns Promise
   */
  public abstract disconnect(): Promise<void>;

  /**
   * 发送消息
   * @param type 消息类型
   * @param message 消息对象
   */
  public abstract send(type: string, message: NetworkMessage): void;

  /**
   * 获取连接状态
   * @returns 连接状态
   */
  public getState(): NetworkConnectionState {
    return this.state;
  }

  /**
   * 是否已连接
   * @returns 是否已连接
   */
  public isConnected(): boolean {
    return this.state === NetworkConnectionState.CONNECTED;
  }

  /**
   * 获取连接ID
   * @returns 连接ID
   */
  public getId(): string {
    return this.id;
  }

  /**
   * 获取远程地址
   * @returns 远程地址
   */
  public getRemoteAddress(): string {
    return this.remoteAddress;
  }

  /**
   * 获取连接时间
   * @returns 连接时间
   */
  public getConnectTime(): number {
    return this.connectTime;
  }

  /**
   * 获取最后活动时间
   * @returns 最后活动时间
   */
  public getLastActivityTime(): number {
    return this.lastActivityTime;
  }

  /**
   * 获取发送的消息数量
   * @returns 发送的消息数量
   */
  public getSentMessages(): number {
    return this.sentMessages;
  }

  /**
   * 获取接收的消息数量
   * @returns 接收的消息数量
   */
  public getReceivedMessages(): number {
    return this.receivedMessages;
  }

  /**
   * 获取发送的字节数
   * @returns 发送的字节数
   */
  public getSentBytes(): number {
    return this.sentBytes;
  }

  /**
   * 获取接收的字节数
   * @returns 接收的字节数
   */
  public getReceivedBytes(): number {
    return this.receivedBytes;
  }

  /**
   * 获取连接统计信息
   * @returns 连接统计信息
   */
  public getStats(): {
    id: string;
    remoteAddress: string;
    state: NetworkConnectionState;
    connectTime: number;
    lastActivityTime: number;
    sentMessages: number;
    receivedMessages: number;
    sentBytes: number;
    receivedBytes: number;
    uptime: number;
  } {
    return {
      id: this.id,
      remoteAddress: this.remoteAddress,
      state: this.state,
      connectTime: this.connectTime,
      lastActivityTime: this.lastActivityTime,
      sentMessages: this.sentMessages,
      receivedMessages: this.receivedMessages,
      sentBytes: this.sentBytes,
      receivedBytes: this.receivedBytes,
      uptime: this.connectTime > 0 ? Date.now() - this.connectTime : 0,
    };
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.sentMessages = 0;
    this.receivedMessages = 0;
    this.sentBytes = 0;
    this.receivedBytes = 0;
  }

  /**
   * 更新最后活动时间
   */
  protected updateLastActivityTime(): void {
    this.lastActivityTime = Date.now();
  }

  /**
   * 更新发送统计信息
   * @param messageSize 消息大小（字节）
   */
  protected updateSentStats(messageSize: number): void {
    this.sentMessages++;
    this.sentBytes += messageSize;
    this.updateLastActivityTime();
  }

  /**
   * 更新接收统计信息
   * @param messageSize 消息大小（字节）
   */
  protected updateReceivedStats(messageSize: number): void {
    this.receivedMessages++;
    this.receivedBytes += messageSize;
    this.updateLastActivityTime();
  }
}
