/**
 * 屏幕空间反射效果
 * 实现逼真的反射效果
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * SSR效果选项
 */
export interface SSREffectOptions extends PostProcessingEffectOptions {
    /** 反射强度 */
    intensity?: number;
    /** 最大步数 */
    maxSteps?: number;
    /** 最大距离 */
    maxDistance?: number;
    /** 步长 */
    stride?: number;
    /** 粗糙度 */
    roughness?: number;
    /** 厚度 */
    thickness?: number;
    /** 是否使用法线贴图 */
    useNormalMap?: boolean;
    /** 是否使用粗糙度贴图 */
    useRoughnessMap?: boolean;
    /** 去噪迭代次数 */
    denoiseIterations?: number;
    /** 去噪核大小 */
    denoiseKernel?: number;
    /** 去噪漫反射强度 */
    denoiseDiffuse?: number;
    /** 去噪高光强度 */
    denoiseSpecular?: number;
    /** 半径 */
    radius?: number;
    /** Phi值 */
    phi?: number;
    /** 亮度Phi值 */
    lumaPhi?: number;
    /** 深度Phi值 */
    depthPhi?: number;
    /** 法线Phi值 */
    normalPhi?: number;
    /** 粗糙度Phi值 */
    roughnessPhi?: number;
    /** 高光Phi值 */
    specularPhi?: number;
    /** 环境模糊 */
    envBlur?: number;
    /** 是否使用重要性采样 */
    importanceSampling?: boolean;
    /** 精细步数 */
    refineSteps?: number;
    /** 分辨率缩放 */
    resolutionScale?: number;
    /** 是否显示丢失的光线 */
    missedRays?: boolean;
}
/**
 * 屏幕空间反射效果
 */
export declare class SSREffect extends PostProcessingEffect {
    /** 反射强度 */
    private intensity;
    /** 最大步数 */
    private maxSteps;
    /** 最大距离 */
    private maxDistance;
    /** 步长 */
    private stride;
    /** 粗糙度 */
    private roughness;
    /** 厚度 */
    private thickness;
    /** 是否使用法线贴图 */
    private useNormalMap;
    /** 是否使用粗糙度贴图 */
    private useRoughnessMap;
    /** 去噪迭代次数 */
    private denoiseIterations;
    /** 去噪核大小 */
    private denoiseKernel;
    /** 去噪漫反射强度 */
    private denoiseDiffuse;
    /** 去噪高光强度 */
    private denoiseSpecular;
    /** 半径 */
    private radius;
    /** Phi值 */
    private phi;
    /** 亮度Phi值 */
    private lumaPhi;
    /** 深度Phi值 */
    private depthPhi;
    /** 法线Phi值 */
    private normalPhi;
    /** 粗糙度Phi值 */
    private roughnessPhi;
    /** 高光Phi值 */
    private specularPhi;
    /** 环境模糊 */
    private envBlur;
    /** 是否使用重要性采样 */
    private importanceSampling;
    /** 精细步数 */
    private refineSteps;
    /** 分辨率缩放 */
    private resolutionScale;
    /** 是否显示丢失的光线 */
    private missedRays;
    /** SSR通道 */
    private ssrPass;
    /** 场景 */
    private scene;
    /** 相机 */
    private camera;
    /** 深度纹理 */
    private depthTexture;
    /** 法线纹理 */
    private normalTexture;
    /** 粗糙度纹理 */
    private roughnessTexture;
    /** 渲染目标 */
    private renderTarget;
    /**
     * 创建SSR效果
     * @param options SSR效果选项
     */
    constructor(options?: SSREffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 设置场景和相机
     * @param scene 场景
     * @param camera 相机
     */
    setSceneAndCamera(scene: THREE.Scene, camera: THREE.PerspectiveCamera | THREE.OrthographicCamera): void;
    /**
     * 设置法线纹理
     * @param texture 法线纹理
     */
    setNormalTexture(texture: THREE.Texture): void;
    /**
     * 设置粗糙度纹理
     * @param texture 粗糙度纹理
     */
    setRoughnessTexture(texture: THREE.Texture): void;
    /**
     * 设置反射强度
     * @param intensity 反射强度
     */
    setIntensity(intensity: number): void;
    /**
     * 获取反射强度
     * @returns 反射强度
     */
    getIntensity(): number;
    /**
     * 设置最大步数
     * @param steps 最大步数
     */
    setMaxSteps(steps: number): void;
    /**
     * 获取最大步数
     * @returns 最大步数
     */
    getMaxSteps(): number;
    /**
     * 设置最大距离
     * @param distance 最大距离
     */
    setMaxDistance(distance: number): void;
    /**
     * 获取最大距离
     * @returns 最大距离
     */
    getMaxDistance(): number;
    /**
     * 设置步长
     * @param stride 步长
     */
    setStride(stride: number): void;
    /**
     * 获取步长
     * @returns 步长
     */
    getStride(): number;
    /**
     * 设置粗糙度
     * @param roughness 粗糙度
     */
    setRoughness(roughness: number): void;
    /**
     * 获取粗糙度
     * @returns 粗糙度
     */
    getRoughness(): number;
    /**
     * 设置厚度
     * @param thickness 厚度
     */
    setThickness(thickness: number): void;
    /**
     * 获取厚度
     * @returns 厚度
     */
    getThickness(): number;
    /**
     * 设置是否使用法线贴图
     * @param use 是否使用
     */
    setUseNormalMap(use: boolean): void;
    /**
     * 获取是否使用法线贴图
     * @returns 是否使用
     */
    isUseNormalMap(): boolean;
    /**
     * 设置是否使用粗糙度贴图
     * @param use 是否使用
     */
    setUseRoughnessMap(use: boolean): void;
    /**
     * 获取是否使用粗糙度贴图
     * @returns 是否使用
     */
    isUseRoughnessMap(): boolean;
    /**
     * 更新效果
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
    /**
     * 销毁效果
     */
    dispose(): void;
}
