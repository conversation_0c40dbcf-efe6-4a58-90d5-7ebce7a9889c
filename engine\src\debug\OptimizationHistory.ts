/**
 * 优化历史记录
 * 用于记录和比较场景优化历史
 */
import { SceneAnalysisResult, OptimizationSuggestion, OptimizationType } from './SceneOptimizer';
import { Debug } from '../utils/Debug';

/**
 * 优化历史记录项
 */
export interface OptimizationHistoryEntry {
  /** 记录ID */
  id: string;
  /** 场景ID */
  sceneId: string;
  /** 场景名称 */
  sceneName: string;
  /** 分析结果 */
  analysisResult: SceneAnalysisResult;
  /** 应用的优化类型 */
  appliedOptimizations: OptimizationType[];
  /** 优化前的总体得分 */
  scoreBefore: number;
  /** 优化后的总体得分 */
  scoreAfter: number;
  /** 时间戳 */
  timestamp: number;
  /** 描述 */
  description: string;
}

/**
 * 优化比较结果
 */
export interface OptimizationComparisonResult {
  /** 基准记录 */
  baseEntry: OptimizationHistoryEntry;
  /** 比较记录 */
  compareEntry: OptimizationHistoryEntry;
  /** 得分变化 */
  scoreDifference: number;
  /** 得分变化百分比 */
  scorePercentChange: number;
  /** 统计数据变化 */
  statsChanges: {
    /** 字段名称 */
    field: string;
    /** 字段显示名称 */
    displayName: string;
    /** 基准值 */
    baseValue: number;
    /** 比较值 */
    compareValue: number;
    /** 差值 */
    difference: number;
    /** 变化百分比 */
    percentChange: number;
  }[];
  /** 应用的优化 */
  appliedOptimizations: OptimizationType[];
}

/**
 * 优化历史记录
 */
export class OptimizationHistory {
  /** 单例实例 */
  private static instance: OptimizationHistory;

  /** 历史记录 */
  private history: OptimizationHistoryEntry[] = [];

  /** 最大历史记录数量 */
  private maxHistoryEntries: number = 50;

  /**
   * 获取单例实例
   */
  public static getInstance(): OptimizationHistory {
    if (!OptimizationHistory.instance) {
      OptimizationHistory.instance = new OptimizationHistory();
    }
    return OptimizationHistory.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    // 尝试从本地存储加载历史记录
    this.loadFromStorage();
  }

  /**
   * 添加历史记录
   * @param entry 历史记录项
   */
  public addEntry(entry: Omit<OptimizationHistoryEntry, 'id'>): OptimizationHistoryEntry {
    // 生成唯一ID
    const id = `opt-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    
    // 创建完整记录
    const fullEntry: OptimizationHistoryEntry = {
      ...entry,
      id
    };
    
    // 添加到历史记录
    this.history.unshift(fullEntry);
    
    // 限制历史记录数量
    if (this.history.length > this.maxHistoryEntries) {
      this.history = this.history.slice(0, this.maxHistoryEntries);
    }
    
    // 保存到本地存储
    this.saveToStorage();
    
    return fullEntry;
  }

  /**
   * 获取历史记录
   * @param sceneId 可选的场景ID过滤
   * @returns 历史记录数组
   */
  public getHistory(sceneId?: string): OptimizationHistoryEntry[] {
    if (sceneId) {
      return this.history.filter(entry => entry.sceneId === sceneId);
    }
    return [...this.history];
  }

  /**
   * 获取历史记录项
   * @param id 记录ID
   * @returns 历史记录项
   */
  public getEntry(id: string): OptimizationHistoryEntry | undefined {
    return this.history.find(entry => entry.id === id);
  }

  /**
   * 删除历史记录项
   * @param id 记录ID
   * @returns 是否成功删除
   */
  public deleteEntry(id: string): boolean {
    const initialLength = this.history.length;
    this.history = this.history.filter(entry => entry.id !== id);
    
    // 如果长度变化，说明删除成功
    const success = initialLength !== this.history.length;
    
    if (success) {
      // 保存到本地存储
      this.saveToStorage();
    }
    
    return success;
  }

  /**
   * 清空历史记录
   */
  public clearHistory(): void {
    this.history = [];
    this.saveToStorage();
  }

  /**
   * 比较两个历史记录
   * @param baseEntryId 基准记录ID
   * @param compareEntryId 比较记录ID
   * @returns 比较结果
   */
  public compareEntries(baseEntryId: string, compareEntryId: string): OptimizationComparisonResult | null {
    // 获取记录
    const baseEntry = this.getEntry(baseEntryId);
    const compareEntry = this.getEntry(compareEntryId);
    
    if (!baseEntry || !compareEntry) {
      return null;
    }
    
    // 计算得分变化
    const scoreDifference = compareEntry.scoreAfter - baseEntry.scoreAfter;
    const scorePercentChange = (scoreDifference / baseEntry.scoreAfter) * 100;
    
    // 计算统计数据变化
    const statsChanges = this.calculateStatsChanges(baseEntry.analysisResult, compareEntry.analysisResult);
    
    // 创建比较结果
    const result: OptimizationComparisonResult = {
      baseEntry,
      compareEntry,
      scoreDifference,
      scorePercentChange,
      statsChanges,
      appliedOptimizations: compareEntry.appliedOptimizations
    };
    
    return result;
  }

  /**
   * 计算统计数据变化
   * @param baseResult 基准分析结果
   * @param compareResult 比较分析结果
   * @returns 统计数据变化
   */
  private calculateStatsChanges(baseResult: SceneAnalysisResult, compareResult: SceneAnalysisResult): {
    field: string;
    displayName: string;
    baseValue: number;
    compareValue: number;
    difference: number;
    percentChange: number;
  }[] {
    const changes: {
      field: string;
      displayName: string;
      baseValue: number;
      compareValue: number;
      difference: number;
      percentChange: number;
    }[] = [];
    
    // 定义要比较的字段
    const fieldsToCompare = [
      { field: 'entityCount', displayName: '实体数量' },
      { field: 'renderableCount', displayName: '可渲染对象数量' },
      { field: 'triangleCount', displayName: '三角形数量' },
      { field: 'vertexCount', displayName: '顶点数量' },
      { field: 'materialCount', displayName: '材质数量' },
      { field: 'textureCount', displayName: '纹理数量' },
      { field: 'textureMemory', displayName: '纹理内存 (MB)' },
      { field: 'lightCount', displayName: '灯光数量' },
      { field: 'drawCalls', displayName: '绘制调用数量' },
      { field: 'memoryUsage', displayName: '内存使用 (MB)' },
      { field: 'overallScore', displayName: '总体得分' }
    ];
    
    // 计算每个字段的变化
    for (const { field, displayName } of fieldsToCompare) {
      const baseValue = (baseResult as any)[field] || 0;
      const compareValue = (compareResult as any)[field] || 0;
      const difference = compareValue - baseValue;
      const percentChange = baseValue !== 0 ? (difference / baseValue) * 100 : 0;
      
      changes.push({
        field,
        displayName,
        baseValue,
        compareValue,
        difference,
        percentChange
      });
    }
    
    return changes;
  }

  /**
   * 从本地存储加载历史记录
   */
  private loadFromStorage(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const storedHistory = localStorage.getItem('optimizationHistory');
        if (storedHistory) {
          this.history = JSON.parse(storedHistory);
        }
      }
    } catch (error) {
      Debug.warn('OptimizationHistory', '从本地存储加载历史记录失败:', error);
    }
  }

  /**
   * 保存历史记录到本地存储
   */
  private saveToStorage(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('optimizationHistory', JSON.stringify(this.history));
      }
    } catch (error) {
      Debug.warn('OptimizationHistory', '保存历史记录到本地存储失败:', error);
    }
  }
}
