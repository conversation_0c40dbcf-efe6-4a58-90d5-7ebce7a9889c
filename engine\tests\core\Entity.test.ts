/**
 * Entity类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Component } from '../../src/core/Component';
import { Transform } from '../../src/scene/Transform';
import { EventEmitter } from '../../src/utils/EventEmitter';

// 创建一个测试组件类
class TestComponent extends Component {
  public initialized: boolean = false;
  public enabled: boolean = true;
  public disposed: boolean = false;
  public value: number = 0;
  
  constructor(entity: Entity, value: number = 0) {
    super(entity);
    this.value = value;
  }
  
  public static readonly TYPE: string = 'TestComponent';
  
  public getType(): string {
    return TestComponent.TYPE;
  }
  
  public initialize(): void {
    this.initialized = true;
  }
  
  public dispose(): void {
    this.disposed = true;
    super.dispose();
  }
  
  public isEnabled(): boolean {
    return this.enabled;
  }
  
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
  
  public setValue(value: number): void {
    this.value = value;
    this.emit('valueChanged', { value });
  }
  
  public getValue(): number {
    return this.value;
  }
}

describe('Entity', () => {
  let engine: Engine;
  let world: World;
  let entity: Entity;
  
  // 在每个测试前创建一个新的实体实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建实体
    entity = new Entity(world);
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试实体初始化
  it('应该正确初始化实体', () => {
    expect(entity).toBeDefined();
    expect(entity.getWorld()).toBe(world);
    expect(entity.getId()).toBeDefined();
    expect(entity.getName()).toBe('');
    expect(entity.isActive()).toBe(true);
    expect(entity.getParent()).toBeNull();
    expect(entity.getChildren().length).toBe(0);
    expect(entity.getComponents().size).toBe(0);
  });
  
  // 测试实体名称
  it('应该能够设置和获取实体名称', () => {
    const name = '测试实体';
    entity.setName(name);
    expect(entity.getName()).toBe(name);
  });
  
  // 测试实体激活状态
  it('应该能够设置和获取实体激活状态', () => {
    expect(entity.isActive()).toBe(true);
    
    entity.setActive(false);
    expect(entity.isActive()).toBe(false);
    
    entity.setActive(true);
    expect(entity.isActive()).toBe(true);
  });
  
  // 测试组件管理
  it('应该能够添加、获取和移除组件', () => {
    // 创建测试组件
    const component = new TestComponent(entity, 42);
    
    // 添加组件到实体
    entity.addComponent(component);
    
    // 验证组件已添加
    expect(entity.getComponents().size).toBe(1);
    expect(entity.getComponents().has(TestComponent.TYPE)).toBe(true);
    
    // 通过类型获取组件
    const retrievedComponent = entity.getComponent<TestComponent>(TestComponent.TYPE);
    expect(retrievedComponent).toBe(component);
    expect(retrievedComponent?.getValue()).toBe(42);
    
    // 检查组件是否存在
    expect(entity.hasComponent(TestComponent.TYPE)).toBe(true);
    expect(entity.hasComponent('NonExistentComponent')).toBe(false);
    
    // 移除组件
    entity.removeComponent(TestComponent.TYPE);
    
    // 验证组件已移除
    expect(entity.getComponents().size).toBe(0);
    expect(entity.hasComponent(TestComponent.TYPE)).toBe(false);
    expect(entity.getComponent(TestComponent.TYPE)).toBeUndefined();
  });
  
  // 测试组件初始化
  it('应该在添加组件时初始化组件', () => {
    // 创建测试组件
    const component = new TestComponent(entity);
    
    // 添加组件到实体
    entity.addComponent(component);
    
    // 验证组件已初始化
    expect(component.initialized).toBe(true);
  });
  
  // 测试组件启用/禁用
  it('应该能够启用和禁用组件', () => {
    // 创建测试组件
    const component = new TestComponent(entity);
    
    // 添加组件到实体
    entity.addComponent(component);
    
    // 验证组件已启用
    expect(component.isEnabled()).toBe(true);
    
    // 禁用组件
    component.setEnabled(false);
    
    // 验证组件已禁用
    expect(component.isEnabled()).toBe(false);
    
    // 启用组件
    component.setEnabled(true);
    
    // 验证组件已启用
    expect(component.isEnabled()).toBe(true);
  });
  
  // 测试组件事件
  it('应该能够发射和监听组件事件', () => {
    // 创建测试组件
    const component = new TestComponent(entity);
    
    // 添加组件到实体
    entity.addComponent(component);
    
    // 创建事件监听器
    const listener = vi.fn();
    
    // 添加事件监听器
    component.on('valueChanged', listener);
    
    // 更改组件值
    component.setValue(100);
    
    // 验证事件监听器被调用
    expect(listener).toHaveBeenCalledWith({ value: 100 });
  });
  
  // 测试实体层级结构
  it('应该能够管理实体层级结构', () => {
    // 创建父实体
    const parent = new Entity(world);
    parent.setName('父实体');
    
    // 创建另一个子实体
    const sibling = new Entity(world);
    sibling.setName('兄弟实体');
    
    // 设置父子关系
    entity.setName('子实体');
    entity.setParent(parent);
    sibling.setParent(parent);
    
    // 验证父子关系
    expect(entity.getParent()).toBe(parent);
    expect(sibling.getParent()).toBe(parent);
    expect(parent.getChildren().length).toBe(2);
    expect(parent.getChildren()[0]).toBe(entity);
    expect(parent.getChildren()[1]).toBe(sibling);
    
    // 移除父子关系
    entity.setParent(null);
    
    // 验证父子关系已移除
    expect(entity.getParent()).toBeNull();
    expect(parent.getChildren().length).toBe(1);
    expect(parent.getChildren()[0]).toBe(sibling);
  });
  
  // 测试实体克隆
  it('应该能够克隆实体', () => {
    // 设置实体属性
    entity.setName('原始实体');
    entity.addComponent(new TestComponent(entity, 42));
    
    // 克隆实体
    const clonedEntity = entity.clone();
    
    // 验证克隆的实体属性
    expect(clonedEntity.getName()).toBe(entity.getName());
    expect(clonedEntity.isActive()).toBe(entity.isActive());
    expect(clonedEntity.getWorld()).toBe(entity.getWorld());
    
    // 验证克隆的组件
    expect(clonedEntity.hasComponent(TestComponent.TYPE)).toBe(true);
    const clonedComponent = clonedEntity.getComponent<TestComponent>(TestComponent.TYPE);
    expect(clonedComponent).toBeDefined();
    expect(clonedComponent?.getValue()).toBe(42);
    
    // 验证克隆的实体与原始实体是不同的实例
    expect(clonedEntity).not.toBe(entity);
    expect(clonedEntity.getId()).not.toBe(entity.getId());
    expect(clonedComponent).not.toBe(entity.getComponent<TestComponent>(TestComponent.TYPE));
  });
  
  // 测试实体销毁
  it('应该能够正确销毁实体', () => {
    // 添加组件
    const component = new TestComponent(entity);
    entity.addComponent(component);
    
    // 创建子实体
    const child = new Entity(world);
    child.setParent(entity);
    
    // 销毁实体
    entity.dispose();
    
    // 验证组件已销毁
    expect(component.disposed).toBe(true);
    
    // 验证子实体的父实体已清除
    expect(child.getParent()).toBeNull();
    
    // 验证实体已从世界中移除
    expect(world.getEntity(entity.getId())).toBeUndefined();
  });
  
  // 测试实体标签
  it('应该能够设置和获取实体标签', () => {
    // 设置标签
    entity.setTag('player');
    
    // 验证标签
    expect(entity.getTag()).toBe('player');
    
    // 更改标签
    entity.setTag('enemy');
    
    // 验证标签已更改
    expect(entity.getTag()).toBe('enemy');
  });
  
  // 测试实体层级
  it('应该能够设置和获取实体层级', () => {
    // 设置层级
    entity.setLayer(5);
    
    // 验证层级
    expect(entity.getLayer()).toBe(5);
    
    // 更改层级
    entity.setLayer(10);
    
    // 验证层级已更改
    expect(entity.getLayer()).toBe(10);
  });
});
