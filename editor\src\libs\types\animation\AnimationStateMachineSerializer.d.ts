/**
 * 动画状态机序列化器
 * 用于序列化和反序列化动画状态机数据
 */
import { AnimationStateMachine } from './AnimationStateMachine';
import { Animator } from './Animator';
/**
 * 状态机序列化数据
 */
export interface AnimationStateMachineData {
    /** 状态列表 */
    states: AnimationStateData[];
    /** 转换规则列表 */
    transitions: TransitionRuleData[];
    /** 参数列表 */
    parameters: ParameterData[];
    /** 当前状态名称 */
    currentState?: string;
}
/**
 * 状态序列化数据
 */
export interface AnimationStateData {
    /** 状态名称 */
    name: string;
    /** 状态类型 */
    type: string;
    /** 状态位置（编辑器中的位置） */
    position?: {
        x: number;
        y: number;
    };
    /** 状态颜色（编辑器中的颜色） */
    color?: string;
    /** 状态数据 */
    [key: string]: any;
}
/**
 * 转换规则序列化数据
 */
export interface TransitionRuleData {
    /** 源状态名称 */
    from: string;
    /** 目标状态名称 */
    to: string;
    /** 转换条件表达式 */
    conditionExpression: string;
    /** 转换持续时间（秒） */
    duration: number;
    /** 是否可以中断 */
    canInterrupt: boolean;
    /** 转换曲线类型 */
    curveType?: string;
    /** 优先级 */
    priority?: number;
}
/**
 * 参数数据
 */
export interface ParameterData {
    /** 参数名称 */
    name: string;
    /** 参数类型 */
    type: 'number' | 'boolean' | 'string' | 'vector2' | 'vector3';
    /** 参数默认值 */
    defaultValue: any;
    /** 参数最小值（仅适用于数值类型） */
    minValue?: number;
    /** 参数最大值（仅适用于数值类型） */
    maxValue?: number;
}
/**
 * 动画状态机序列化器
 */
export declare class AnimationStateMachineSerializer {
    /**
     * 序列化状态机
     * @param stateMachine 状态机
     * @returns 序列化数据
     */
    static serialize(stateMachine: AnimationStateMachine): AnimationStateMachineData;
    /**
     * 反序列化状态机
     * @param data 序列化数据
     * @param animator 动画控制器
     * @returns 状态机
     */
    static deserialize(data: AnimationStateMachineData, animator: Animator): AnimationStateMachine;
}
