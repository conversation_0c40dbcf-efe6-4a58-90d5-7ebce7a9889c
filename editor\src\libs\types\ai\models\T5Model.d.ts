/**
 * T5模型
 * Text-to-Text Transfer Transformer模型
 */
import { AIModelType } from '../AIModelType';
import { AIModelConfig } from '../AIModelConfig';
import { IAIModel, TextGenerationOptions, TextSummaryResult, TranslationResult } from './IAIModel';
/**
 * T5模型配置
 */
export interface T5ModelConfig extends AIModelConfig {
    /** 模型变体 */
    variant?: 'small' | 'base' | 'large' | 'xl' | 'xxl';
    /** 最小生成长度 */
    minLength?: number;
    /** 最大生成长度 */
    maxLength?: number;
    /** 是否使用束搜索 */
    useBeamSearch?: boolean;
    /** 束大小 */
    beamSize?: number;
    /** 早停策略 */
    earlyStoppingStrategy?: 'none' | 'length' | 'probability';
}
/**
 * T5模型
 */
export declare class T5Model implements IAIModel {
    /** 模型类型 */
    private readonly modelType;
    /** 模型配置 */
    private config;
    /** 全局配置 */
    private globalConfig;
    /** 是否已初始化 */
    private initialized;
    /** 是否正在初始化 */
    private initializing;
    /** 模型 */
    private model;
    /** 分词器 */
    private tokenizer;
    /** 事件发射器 */
    private eventEmitter;
    /** 任务前缀映射 */
    private static readonly TASK_PREFIXES;
    /**
     * 构造函数
     * @param config 模型配置
     * @param globalConfig 全局配置
     */
    constructor(config?: T5ModelConfig, globalConfig?: any);
    /**
     * 获取模型类型
     * @returns 模型类型
     */
    getType(): AIModelType;
    /**
     * 获取模型配置
     * @returns 模型配置
     */
    getConfig(): AIModelConfig;
    /**
     * 初始化模型
     * @returns 是否成功初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 生成文本
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 生成的文本
     */
    generateText(prompt: string, options?: TextGenerationOptions): Promise<string>;
    /**
     * 翻译文本
     * @param text 要翻译的文本
     * @param targetLanguage 目标语言
     * @param sourceLanguage 源语言（可选）
     * @returns 翻译结果
     */
    translateText(text: string, targetLanguage: string, sourceLanguage?: string): Promise<TranslationResult>;
    /**
     * 生成文本摘要
     * @param text 要摘要的文本
     * @param maxLength 最大摘要长度
     * @returns 摘要结果
     */
    summarizeText(text: string, maxLength?: number): Promise<TextSummaryResult>;
    /**
     * 销毁模型
     */
    dispose(): void;
    /**
     * 模拟生成
     * @returns 生成结果
     */
    private mockGenerate;
    /**
     * 模拟翻译
     * @returns 翻译结果
     */
    private mockTranslate;
    /**
     * 模拟摘要
     * @returns 摘要结果
     */
    private mockSummarize;
}
