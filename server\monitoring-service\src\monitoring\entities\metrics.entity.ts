import { En<PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, Index } from 'typeorm';

@Entity('metrics')
export class MetricsEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  serviceId: string;

  @Column()
  @Index()
  serviceType: string;

  @Column()
  @Index()
  instanceId: string;

  @Column()
  @Index()
  hostname: string;

  @Column('json')
  metrics: Record<string, any>;

  @Column('timestamp')
  @Index()
  timestamp: Date;

  @CreateDateColumn()
  createdAt: Date;
}
