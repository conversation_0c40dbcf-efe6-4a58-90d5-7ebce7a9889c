/**
 * 物理调试器
 * 用于可视化物理世界
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { PhysicsSystem } from '../PhysicsSystem';
/**
 * 物理调试器选项
 */
export interface PhysicsDebuggerOptions {
    /** 是否显示物理体 */
    showBodies?: boolean;
    /** 是否显示约束 */
    showConstraints?: boolean;
    /** 是否显示碰撞点 */
    showContactPoints?: boolean;
    /** 是否显示AABB */
    showAABBs?: boolean;
    /** 物理体颜色 */
    bodyColor?: THREE.Color | number;
    /** 静态物理体颜色 */
    staticBodyColor?: THREE.Color | number;
    /** 运动学物理体颜色 */
    kinematicBodyColor?: THREE.Color | number;
    /** 约束颜色 */
    constraintColor?: THREE.Color | number;
    /** 碰撞点颜色 */
    contactPointColor?: THREE.Color | number;
    /** AABB颜色 */
    aabbColor?: THREE.Color | number;
    /** 线宽 */
    lineWidth?: number;
    /** 透明度 */
    opacity?: number;
}
/**
 * 物理调试器
 */
export declare class PhysicsDebugger {
    /** 物理系统 */
    protected physicsSystem: PhysicsSystem;
    /** 调试场景 */
    protected scene: THREE.Scene;
    /** 物理体网格映射 */
    private bodyMeshes;
    /** 约束网格映射 */
    private constraintMeshes;
    /** 碰撞点网格 */
    private contactPointMeshes;
    /** AABB网格映射 */
    private aabbMeshes;
    /** 是否显示物理体 */
    private showBodies;
    /** 是否显示约束 */
    private showConstraints;
    /** 是否显示碰撞点 */
    private showContactPoints;
    /** 是否显示AABB */
    private showAABBs;
    /** 物理体颜色 */
    private bodyColor;
    /** 静态物理体颜色 */
    private staticBodyColor;
    /** 运动学物理体颜色 */
    private kinematicBodyColor;
    /** 约束颜色 */
    private constraintColor;
    /** 碰撞点颜色 */
    private contactPointColor;
    /** AABB颜色 */
    private aabbColor;
    /** 线宽 */
    private lineWidth;
    /** 透明度 */
    private opacity;
    /** 是否已初始化 */
    protected initialized: boolean;
    /**
     * 创建物理调试器
     * @param physicsSystem 物理系统
     * @param options 调试器选项
     */
    constructor(physicsSystem: PhysicsSystem, options?: PhysicsDebuggerOptions);
    /**
     * 初始化调试器
     */
    initialize(): void;
    /**
     * 更新调试器
     */
    update(): void;
    /**
     * 创建物理体网格
     * @param body 物理体
     * @returns 物理体网格
     */
    private createBodyMesh;
    /**
     * 创建约束网格
     * @param constraint 约束
     * @returns 约束网格
     */
    protected createConstraintMesh(constraint: CANNON.Constraint): THREE.Object3D | null;
    /**
     * 更新约束网格
     * @param constraint 约束
     * @param mesh 网格
     */
    protected updateConstraintMesh(constraint: CANNON.Constraint, mesh: THREE.Object3D): void;
    /**
     * 创建碰撞点网格
     * @param contact 碰撞接触点
     * @returns 碰撞点网格
     */
    protected createContactPointMesh(contact: CANNON.ContactEquation): THREE.Object3D;
    /**
     * 创建AABB网格
     * @param body 物理体
     * @returns AABB网格
     */
    protected createAABBMesh(body: CANNON.Body): THREE.LineSegments;
    /**
     * 更新AABB网格
     * @param body 物理体
     * @param mesh 网格
     */
    protected updateAABBMesh(body: CANNON.Body, mesh: THREE.LineSegments): void;
    /**
     * 清空调试器
     */
    protected clear(): void;
    /**
     * 销毁调试器
     */
    dispose(): void;
}
