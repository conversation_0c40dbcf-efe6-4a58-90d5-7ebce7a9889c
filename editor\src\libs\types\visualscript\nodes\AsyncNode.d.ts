/**
 * 视觉脚本异步节点
 * 异步节点用于执行异步操作，如定时器、网络请求等
 */
import { FlowNode, FlowNodeOptions } from './FlowNode';
import { NodeCategory, NodeType } from './Node';
/**
 * 异步节点选项
 */
export interface AsyncNodeOptions extends FlowNodeOptions {
    /** 超时时间（毫秒） */
    timeout?: number;
}
/**
 * 异步节点状态
 */
export declare enum AsyncNodeState {
    /** 空闲 */
    IDLE = "idle",
    /** 运行中 */
    RUNNING = "running",
    /** 已完成 */
    COMPLETED = "completed",
    /** 已取消 */
    CANCELED = "canceled",
    /** 出错 */
    ERROR = "error"
}
/**
 * 异步节点基类
 */
export declare class AsyncNode extends FlowNode {
    /** 节点类型 */
    readonly nodeType: NodeType;
    /** 节点类别 */
    readonly category: NodeCategory;
    /** 超时时间（毫秒） */
    protected timeout: number;
    /** 当前状态 */
    protected state: AsyncNodeState;
    /** 开始时间 */
    protected startTime: number;
    /** 完成回调 */
    protected completeCallback: (() => void) | null;
    /** 错误回调 */
    protected errorCallback: ((error: any) => void) | null;
    /** 取消回调 */
    protected cancelCallback: (() => void) | null;
    /** 超时定时器ID */
    protected timeoutId: any;
    /**
     * 创建异步节点
     * @param options 节点选项
     */
    constructor(options: AsyncNodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
    /**
     * 开始异步操作
     * @param inputs 输入值
     */
    protected start(inputs: Record<string, any>): void;
    /**
     * 执行异步操作
     * @param inputs 输入值
     * @returns Promise对象
     */
    protected executeAsync(inputs: Record<string, any>): Promise<any>;
    /**
     * 处理完成
     * @param result 结果
     */
    protected handleComplete(result: any): void;
    /**
     * 处理错误
     * @param error 错误
     */
    protected handleError(error: any): void;
    /**
     * 取消操作
     */
    cancel(): void;
    /**
     * 重置状态
     */
    reset(): void;
    /**
     * 设置完成回调
     * @param callback 回调函数
     */
    onComplete(callback: () => void): void;
    /**
     * 设置错误回调
     * @param callback 回调函数
     */
    onError(callback: (error: any) => void): void;
    /**
     * 设置取消回调
     * @param callback 回调函数
     */
    onCancel(callback: () => void): void;
    /**
     * 获取当前状态
     * @returns 当前状态
     */
    getState(): AsyncNodeState;
    /**
     * 获取运行时间（毫秒）
     * @returns 运行时间
     */
    getRunningTime(): number;
}
