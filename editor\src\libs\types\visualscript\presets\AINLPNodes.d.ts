/**
 * 视觉脚本AI自然语言处理节点
 * 提供文本分类、命名实体识别、文本摘要等功能
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 文本分类节点
 * 对文本进行分类
 */
export declare class TextClassificationNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 命名实体识别节点
 * 识别文本中的命名实体
 */
export declare class NamedEntityRecognitionNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 文本摘要节点
 * 生成文本摘要
 */
export declare class TextSummaryNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 语言翻译节点
 * 将文本从一种语言翻译为另一种语言
 */
export declare class LanguageTranslationNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 注册AI自然语言处理节点
 * @param registry 节点注册表
 */
export declare function registerAINLPNodes(registry: NodeRegistry): void;
