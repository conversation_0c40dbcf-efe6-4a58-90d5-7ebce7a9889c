/**
 * InteractableComponent.ts
 *
 * 可交互组件，用于标记可交互的对象
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector3 } from 'three';
import { EventCallback } from '../../utils/EventEmitter';
/**
 * 交互类型枚举
 */
export declare enum InteractionType {
    /** 点击交互 */
    CLICK = "click",
    /** 接近交互 */
    PROXIMITY = "proximity",
    /** 悬停交互 */
    HOVER = "hover"
}
/**
 * 交互回调函数类型
 */
export type InteractionCallback = (entity: Entity) => void;
/**
 * 可交互组件配置
 */
export interface InteractableComponentConfig {
    /** 交互类型 */
    interactionType?: InteractionType;
    /** 是否可见 */
    visible?: boolean;
    /** 是否可交互 */
    interactive?: boolean;
    /** 交互距离 */
    interactionDistance?: number;
    /** 交互标签 */
    label?: string;
    /** 交互提示 */
    prompt?: string;
    /** 交互声音 */
    interactionSound?: string;
    /** 高亮颜色 */
    highlightColor?: string;
    /** 交互回调 */
    onInteract?: InteractionCallback;
}
/**
 * 可交互组件
 * 用于标记可交互的对象
 */
export declare class InteractableComponent extends Component {
    /** 交互类型 */
    private _interactionType;
    /** 是否可见 */
    private _visible;
    /** 是否可交互 */
    private _interactive;
    /** 交互距离 */
    private _interactionDistance;
    /** 交互标签 */
    private _label;
    /** 交互提示 */
    private _prompt;
    /** 交互声音 */
    private _interactionSound?;
    /** 高亮颜色 */
    private _highlightColor;
    /** 是否高亮 */
    private _highlighted;
    /** 事件发射器 */
    private eventEmitter;
    /** 交互回调 */
    private onInteractCallback?;
    /** 世界位置缓存 */
    private _worldPosition;
    /** 上次更新时间 */
    private lastUpdateTime;
    /**
     * 构造函数
     * @param config 组件配置
     */
    constructor(config?: InteractableComponentConfig);
    /**
     * 获取交互类型
     */
    get interactionType(): InteractionType;
    /**
     * 设置交互类型
     */
    set interactionType(value: InteractionType);
    /**
     * 获取是否可见
     */
    get visible(): boolean;
    /**
     * 设置是否可见
     */
    set visible(value: boolean);
    /**
     * 获取是否可交互
     */
    get interactive(): boolean;
    /**
     * 设置是否可交互
     */
    set interactive(value: boolean);
    /**
     * 获取交互距离
     */
    get interactionDistance(): number;
    /**
     * 设置交互距离
     */
    set interactionDistance(value: number);
    /**
     * 获取交互标签
     */
    get label(): string;
    /**
     * 设置交互标签
     */
    set label(value: string);
    /**
     * 获取交互提示
     */
    get prompt(): string;
    /**
     * 设置交互提示
     */
    set prompt(value: string);
    /**
     * 获取交互声音
     */
    get interactionSound(): string | undefined;
    /**
     * 设置交互声音
     */
    set interactionSound(value: string | undefined);
    /**
     * 获取高亮颜色
     */
    get highlightColor(): string;
    /**
     * 设置高亮颜色
     */
    set highlightColor(value: string);
    /**
     * 获取是否高亮
     */
    get highlighted(): boolean;
    /**
     * 设置高亮状态
     * @param highlighted 是否高亮
     */
    setHighlighted(highlighted: boolean): void;
    /**
     * 更新高亮效果
     */
    private updateHighlight;
    /**
     * 获取世界位置
     * @returns 世界位置
     */
    getWorldPosition(): Vector3;
    /**
     * 交互
     */
    interact(): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    on(event: string, callback: EventCallback): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    off(event: string, callback?: EventCallback): this;
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁组件
     */
    dispose(): void;
}
