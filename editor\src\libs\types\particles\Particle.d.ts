/**
 * 粒子类
 * 表示单个粒子的属性和行为
 */
import * as THREE from 'three';
export declare class Particle {
    /** 是否活跃 */
    active: boolean;
    /** 位置 */
    position: THREE.Vector3;
    /** 上一帧位置 */
    previousPosition: THREE.Vector3;
    /** 速度 */
    velocity: THREE.Vector3;
    /** 加速度 */
    acceleration: THREE.Vector3;
    /** 旋转 */
    rotation: number;
    /** 旋转速度 */
    rotationSpeed: number;
    /** 缩放 */
    scale: THREE.Vector2;
    /** 缩放速度 */
    scaleSpeed: THREE.Vector2;
    /** 颜色 */
    color: THREE.Color;
    /** 起始颜色 */
    startColor: THREE.Color;
    /** 结束颜色 */
    endColor: THREE.Color;
    /** 透明度 */
    opacity: number;
    /** 起始透明度 */
    startOpacity: number;
    /** 结束透明度 */
    endOpacity: number;
    /** 生命周期（秒） */
    lifetime: number;
    /** 已存活时间（秒） */
    age: number;
    /** 归一化年龄（0-1） */
    normalizedAge: number;
    /** 质量 */
    mass: number;
    /** 阻力 */
    drag: number;
    /** 重力缩放 */
    gravityScale: number;
    /** 弹性 */
    restitution: number;
    /** 纹理索引 */
    textureIndex: number;
    /** 自定义数据 - 可以存储任何与粒子相关的额外信息 */
    userData: Record<string, unknown>;
    /**
     * 创建粒子实例
     */
    constructor();
    /**
     * 重置粒子状态
     */
    reset(): void;
    /**
     * 更新粒子
     * @param deltaTime 帧间隔时间（秒）
     * @returns 是否仍然活跃
     */
    update(deltaTime: number): boolean;
    /**
     * 应用力
     * @param force 力向量
     */
    applyForce(force: THREE.Vector3): void;
    /**
     * 应用重力
     * @param gravity 重力向量
     */
    applyGravity(gravity: THREE.Vector3): void;
    /**
     * 处理碰撞
     * @param normal 碰撞法线
     * @param penetration 穿透深度
     */
    handleCollision(normal: THREE.Vector3, penetration: number): void;
    /**
     * 获取粒子的变换矩阵
     * @returns 变换矩阵
     */
    getTransformMatrix(): THREE.Matrix4;
    /**
     * 获取粒子的颜色，包括透明度
     * @returns 颜色，包括透明度
     */
    getColorWithOpacity(): THREE.Vector4;
    /**
     * 计算与相机的距离
     * @param cameraPosition 相机位置
     * @returns 距离
     */
    distanceToCamera(cameraPosition: THREE.Vector3): number;
}
