/**
 * 水体移动设备优化系统
 * 用于优化水体系统在移动设备上的性能
 */
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
import { WaterPhysicsSystem } from '../../physics/water/WaterPhysicsSystem';
import { WaterRenderer } from './WaterRenderer';
import { WaterChunkSystem } from './WaterChunkSystem';
import { WaterLODSystem } from './WaterLODSystem';
import { WaterSurfaceRenderer } from './WaterSurfaceRenderer';
import { WaterInstancedRenderer } from './WaterInstancedRenderer';
import { WaterLightingSystem } from './WaterLightingSystem';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { DeviceCapabilities, DevicePerformanceLevel } from '../../utils/DeviceCapabilities';

/**
 * 水体移动设备优化系统配置
 */
export interface WaterMobileOptimizationSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用调试可视化 */
  enableDebugVisualization?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 是否启用自动性能优化 */
  enableAutoOptimization?: boolean;
  /** 是否启用电池优化 */
  enableBatteryOptimization?: boolean;
  /** 是否启用温度优化 */
  enableTemperatureOptimization?: boolean;
  /** 目标帧率 */
  targetFPS?: number;
  /** 最小可接受帧率 */
  minAcceptableFPS?: number;
  /** 默认性能级别 */
  defaultPerformanceLevel?: DevicePerformanceLevel;
  /** 低电量阈值（百分比） */
  lowBatteryThreshold?: number;
  /** 高温阈值（摄氏度） */
  highTemperatureThreshold?: number;
}

/**
 * 水体移动设备优化系统
 */
export class WaterMobileOptimizationSystem extends System {
  /** 配置 */
  private config: WaterMobileOptimizationSystemConfig;
  /** 水体组件映射 */
  private waterBodies: Map<string, WaterBodyComponent> = new Map();
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem | null = null;
  /** 水体渲染器 */
  private waterRenderer: WaterRenderer | null = null;
  /** 水体分块系统 */
  private waterChunkSystem: WaterChunkSystem | null = null;
  /** 水体LOD系统 */
  private waterLODSystem: WaterLODSystem | null = null;
  /** 水体表面渲染器 */
  private waterSurfaceRenderer: WaterSurfaceRenderer | null = null;
  /** 水体实例化渲染器 */
  private waterInstancedRenderer: WaterInstancedRenderer | null = null;
  /** 水体光照系统 */
  private waterLightingSystem: WaterLightingSystem | null = null;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor;
  /** 设备能力 */
  private deviceCapabilities: DeviceCapabilities;
  /** 当前性能级别 */
  private currentPerformanceLevel: DevicePerformanceLevel;
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 监控数据 */
  private monitorData: {
    fps: number;
    cpuUsage: number;
    gpuUsage: number;
    memoryUsage: number;
    batteryLevel: number;
    temperature: number;
  } = {
    fps: 60,
    cpuUsage: 0,
    gpuUsage: 0,
    memoryUsage: 0,
    batteryLevel: 100,
    temperature: 25
  };
  /** 性能配置 */
  private performanceConfigs: Record<DevicePerformanceLevel, any> = {
    [DevicePerformanceLevel.LOW]: {
      waterResolution: 32,
      waterChunkSize: 32,
      waterLODLevels: 2,
      waterUpdateFrequency: 3,
      waterPhysicsUpdateFrequency: 4,
      enableWaterReflection: false,
      enableWaterRefraction: false,
      enableWaterCaustics: false,
      enableWaterFoam: false,
      enableWaterParticles: false,
      enableWaterVolumetricLighting: false,
      maxWaterParticles: 100,
      waterWaveQuality: 'low',
      waterTextureSize: 512,
      waterRenderScale: 0.5
    },
    [DevicePerformanceLevel.MEDIUM]: {
      waterResolution: 64,
      waterChunkSize: 64,
      waterLODLevels: 3,
      waterUpdateFrequency: 2,
      waterPhysicsUpdateFrequency: 2,
      enableWaterReflection: true,
      enableWaterRefraction: false,
      enableWaterCaustics: true,
      enableWaterFoam: true,
      enableWaterParticles: true,
      enableWaterVolumetricLighting: false,
      maxWaterParticles: 500,
      waterWaveQuality: 'medium',
      waterTextureSize: 1024,
      waterRenderScale: 0.75
    },
    [DevicePerformanceLevel.HIGH]: {
      waterResolution: 128,
      waterChunkSize: 128,
      waterLODLevels: 4,
      waterUpdateFrequency: 1,
      waterPhysicsUpdateFrequency: 1,
      enableWaterReflection: true,
      enableWaterRefraction: true,
      enableWaterCaustics: true,
      enableWaterFoam: true,
      enableWaterParticles: true,
      enableWaterVolumetricLighting: true,
      maxWaterParticles: 2000,
      waterWaveQuality: 'high',
      waterTextureSize: 2048,
      waterRenderScale: 1.0
    }
  };

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: WaterMobileOptimizationSystemConfig = {}) {
    super();

    // 设置世界引用
    this.world = world;

    // 设置默认配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 60,
      enableDebugVisualization: config.enableDebugVisualization || false,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring || false,
      enableAutoOptimization: config.enableAutoOptimization !== undefined ? config.enableAutoOptimization : true,
      enableBatteryOptimization: config.enableBatteryOptimization !== undefined ? config.enableBatteryOptimization : true,
      enableTemperatureOptimization: config.enableTemperatureOptimization !== undefined ? config.enableTemperatureOptimization : true,
      targetFPS: config.targetFPS || 60,
      minAcceptableFPS: config.minAcceptableFPS || 30,
      defaultPerformanceLevel: config.defaultPerformanceLevel || DevicePerformanceLevel.MEDIUM,
      lowBatteryThreshold: config.lowBatteryThreshold || 20,
      highTemperatureThreshold: config.highTemperatureThreshold || 40
    };

    // 获取性能监控器
    this.performanceMonitor = PerformanceMonitor.getInstance();

    // 获取设备能力
    this.deviceCapabilities = DeviceCapabilities.getInstance();

    // 设置当前性能级别
    this.currentPerformanceLevel = this.config.defaultPerformanceLevel!;

    // 初始化系统
    this.initialize();

    Debug.log('WaterMobileOptimizationSystem', '水体移动设备优化系统已创建');
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 获取水体相关系统
    // 注意：由于这些系统可能不存在或有不同的构造函数签名，我们使用更安全的方式获取
    this.findWaterSystems();

    // 应用初始性能配置
    this.applyPerformanceConfig(this.currentPerformanceLevel);
  }

  /**
   * 查找水体相关系统
   */
  private findWaterSystems(): void {
    if (!this.world) {
      return;
    }

    // 获取所有系统
    const systems = this.world.getSystems();

    // 遍历系统查找水体相关系统
    for (const system of systems) {
      const systemType = system.constructor.name;

      switch (systemType) {
        case 'WaterPhysicsSystem':
          this.waterPhysicsSystem = system as unknown as WaterPhysicsSystem;
          break;
        case 'WaterRenderer':
          this.waterRenderer = system as unknown as WaterRenderer;
          break;
        case 'WaterChunkSystem':
          this.waterChunkSystem = system as unknown as WaterChunkSystem;
          break;
        case 'WaterLODSystem':
          this.waterLODSystem = system as unknown as WaterLODSystem;
          break;
        case 'WaterSurfaceRenderer':
          this.waterSurfaceRenderer = system as unknown as WaterSurfaceRenderer;
          break;
        case 'WaterInstancedRenderer':
          this.waterInstancedRenderer = system as unknown as WaterInstancedRenderer;
          break;
        case 'WaterLightingSystem':
          this.waterLightingSystem = system as unknown as WaterLightingSystem;
          break;
      }
    }
  }

  /**
   * 更新系统
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency! !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.beginMeasure('waterMobileOptimizationUpdate');
    }

    // 更新监控数据
    this.updateMonitorData();

    // 如果启用了自动优化，根据监控数据调整性能
    if (this.config.enableAutoOptimization) {
      this.adjustPerformance();
    }

    // 如果启用了电池优化，根据电池电量调整性能
    if (this.config.enableBatteryOptimization) {
      this.adjustPerformanceByBattery();
    }

    // 如果启用了温度优化，根据设备温度调整性能
    if (this.config.enableTemperatureOptimization) {
      this.adjustPerformanceByTemperature();
    }

    // 如果启用了性能监控，结束计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.endMeasure('waterMobileOptimizationUpdate');
    }
  }

  /**
   * 更新监控数据
   */
  private updateMonitorData(): void {
    // 获取性能报告
    const report = this.performanceMonitor.getReport();

    // 更新FPS
    this.monitorData.fps = report.metrics.fps?.value || 60;

    // 更新CPU使用率
    this.monitorData.cpuUsage = report.metrics.cpuUsage?.value || 0;

    // 更新GPU使用率
    this.monitorData.gpuUsage = report.metrics.gpuUsage?.value || 0;

    // 更新内存使用率
    this.monitorData.memoryUsage = report.metrics.memoryUsage?.value || 0;

    // 更新电池电量
    this.monitorData.batteryLevel = this.deviceCapabilities.getBatteryLevel();

    // 更新设备温度
    this.monitorData.temperature = this.deviceCapabilities.getTemperature();
  }

  /**
   * 根据性能调整性能级别
   */
  private adjustPerformance(): void {
    // 如果帧率低于最小可接受帧率，降低性能级别
    if (this.monitorData.fps < this.config.minAcceptableFPS!) {
      this.decreasePerformanceLevel();
    }
    // 如果帧率高于目标帧率的1.2倍，提高性能级别
    else if (this.monitorData.fps > this.config.targetFPS! * 1.2) {
      this.increasePerformanceLevel();
    }
  }

  /**
   * 根据电池电量调整性能级别
   */
  private adjustPerformanceByBattery(): void {
    // 如果电池电量低于阈值，降低性能级别
    if (this.monitorData.batteryLevel < this.config.lowBatteryThreshold!) {
      // 如果当前性能级别高于低性能级别，降低到低性能级别
      if (this.currentPerformanceLevel > DevicePerformanceLevel.LOW) {
        this.setPerformanceLevel(DevicePerformanceLevel.LOW);
      }
    }
  }

  /**
   * 根据设备温度调整性能级别
   */
  private adjustPerformanceByTemperature(): void {
    // 如果设备温度高于阈值，降低性能级别
    if (this.monitorData.temperature > this.config.highTemperatureThreshold!) {
      this.decreasePerformanceLevel();
    }
  }

  /**
   * 提高性能级别
   */
  private increasePerformanceLevel(): void {
    // 如果当前性能级别已经是最高级别，则不再提高
    if (this.currentPerformanceLevel >= DevicePerformanceLevel.HIGH) {
      return;
    }

    // 提高性能级别
    const newLevel = this.currentPerformanceLevel + 1;
    this.setPerformanceLevel(newLevel);
  }

  /**
   * 降低性能级别
   */
  private decreasePerformanceLevel(): void {
    // 如果当前性能级别已经是最低级别，则不再降低
    if (this.currentPerformanceLevel <= DevicePerformanceLevel.LOW) {
      return;
    }

    // 降低性能级别
    const newLevel = this.currentPerformanceLevel - 1;
    this.setPerformanceLevel(newLevel);
  }

  /**
   * 设置性能级别
   * @param level 性能级别
   */
  public setPerformanceLevel(level: DevicePerformanceLevel): void {
    // 如果性能级别没有变化，则不需要更新
    if (level === this.currentPerformanceLevel) {
      return;
    }

    // 更新当前性能级别
    this.currentPerformanceLevel = level;

    // 应用性能配置
    this.applyPerformanceConfig(level);

    Debug.log('WaterMobileOptimizationSystem', `设置性能级别: ${DevicePerformanceLevel[level]}`);
  }

  /**
   * 应用性能配置
   * @param level 性能级别
   */
  private applyPerformanceConfig(level: DevicePerformanceLevel): void {
    // 获取性能配置
    const config = this.performanceConfigs[level];

    // 应用水体物理系统配置
    if (this.waterPhysicsSystem) {
      // 检查是否有 setConfig 方法
      if (typeof (this.waterPhysicsSystem as any).setConfig === 'function') {
        (this.waterPhysicsSystem as any).setConfig({
          updateFrequency: config.waterPhysicsUpdateFrequency,
          enableMultithreading: level > DevicePerformanceLevel.LOW,
          workerCount: level === DevicePerformanceLevel.HIGH ? 4 : 2,
          enableSpatialPartitioning: true,
          spatialGridSize: config.waterChunkSize,
          enableAdaptiveUpdate: true,
          minUpdateFrequency: config.waterPhysicsUpdateFrequency,
          maxUpdateFrequency: config.waterPhysicsUpdateFrequency * 2
        });
      } else {
        Debug.log('WaterMobileOptimizationSystem', '水体物理系统不支持 setConfig 方法');
      }
    }

    // 应用水体渲染器配置
    if (this.waterRenderer) {
      // 检查是否有 setConfig 方法
      if (typeof (this.waterRenderer as any).setConfig === 'function') {
        (this.waterRenderer as any).setConfig({
          updateFrequency: config.waterUpdateFrequency
        });
      } else {
        Debug.log('WaterMobileOptimizationSystem', '水体渲染器不支持 setConfig 方法');
      }
    }

    // 应用水体分块系统配置
    if (this.waterChunkSystem) {
      // 检查是否有 setConfig 方法
      if (typeof (this.waterChunkSystem as any).setConfig === 'function') {
        (this.waterChunkSystem as any).setConfig({
          updateFrequency: config.waterUpdateFrequency,
          chunkSize: config.waterChunkSize,
          useLOD: true,
          useFrustumCulling: true,
          useOctree: level > DevicePerformanceLevel.LOW
        });
      } else {
        Debug.log('WaterMobileOptimizationSystem', '水体分块系统不支持 setConfig 方法');
      }
    }

    // 应用水体LOD系统配置
    if (this.waterLODSystem) {
      // 检查是否有 setConfig 方法
      if (typeof (this.waterLODSystem as any).setConfig === 'function') {
        (this.waterLODSystem as any).setConfig({
          updateFrequency: config.waterUpdateFrequency,
          maxLODLevel: config.waterLODLevels,
          useSmoothTransition: level > DevicePerformanceLevel.LOW
        });
      } else {
        Debug.log('WaterMobileOptimizationSystem', '水体LOD系统不支持 setConfig 方法');
      }
    }

    // 应用水体表面渲染器配置
    if (this.waterSurfaceRenderer) {
      // 检查是否有 setConfig 方法
      if (typeof (this.waterSurfaceRenderer as any).setConfig === 'function') {
        (this.waterSurfaceRenderer as any).setConfig({
          updateFrequency: config.waterUpdateFrequency,
          enableReflection: config.enableWaterReflection,
          enableRefraction: config.enableWaterRefraction,
          enableCaustics: config.enableWaterCaustics,
          enableUnderwaterFog: level > DevicePerformanceLevel.LOW,
          enableUnderwaterDistortion: level > DevicePerformanceLevel.LOW,
          reflectionMapResolution: config.waterTextureSize,
          refractionMapResolution: config.waterTextureSize,
          useHighQualityWaves: level === DevicePerformanceLevel.HIGH
        });
      } else {
        Debug.log('WaterMobileOptimizationSystem', '水体表面渲染器不支持 setConfig 方法');
      }
    }

    // 应用水体实例化渲染器配置
    if (this.waterInstancedRenderer) {
      // 检查是否有 setConfig 方法
      if (typeof (this.waterInstancedRenderer as any).setConfig === 'function') {
        (this.waterInstancedRenderer as any).setConfig({
          updateFrequency: config.waterUpdateFrequency,
          maxInstances: config.maxWaterParticles
        });
      } else {
        Debug.log('WaterMobileOptimizationSystem', '水体实例化渲染器不支持 setConfig 方法');
      }
    }

    // 应用水体光照系统配置
    if (this.waterLightingSystem) {
      // 检查是否有 setConfig 方法
      if (typeof (this.waterLightingSystem as any).setConfig === 'function') {
        (this.waterLightingSystem as any).setConfig({
          updateFrequency: config.waterUpdateFrequency,
          enableCaustics: config.enableWaterCaustics,
          enableVolumetricLight: config.enableWaterVolumetricLighting,
          enableUnderwaterFog: level > DevicePerformanceLevel.LOW
        });
      } else {
        Debug.log('WaterMobileOptimizationSystem', '水体光照系统不支持 setConfig 方法');
      }
    }

    // 更新所有水体组件
    for (const [, waterBody] of this.waterBodies) {
      this.updateWaterBodyForMobile(waterBody, level);
    }
  }

  /**
   * 更新水体组件以适应移动设备
   * @param waterBody 水体组件
   * @param level 性能级别
   */
  private updateWaterBodyForMobile(waterBody: WaterBodyComponent, level: DevicePerformanceLevel): void {
    // 获取性能配置
    const config = this.performanceConfigs[level];

    // 设置水体分辨率
    waterBody.setResolution(config.waterResolution);

    // 设置水体波动质量
    waterBody.setWaveQuality(config.waterWaveQuality);

    // 设置水体特效
    waterBody.setEnableReflection(config.enableWaterReflection);
    waterBody.setEnableRefraction(config.enableWaterRefraction);
    waterBody.setEnableCaustics(config.enableWaterCaustics);
    waterBody.setEnableFoam(config.enableWaterFoam);
    waterBody.setEnableUnderwaterFog(level > DevicePerformanceLevel.LOW);
    waterBody.setEnableUnderwaterDistortion(level > DevicePerformanceLevel.LOW);
  }

  /**
   * 添加水体组件
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterBody(entity: Entity, component: WaterBodyComponent): void {
    this.waterBodies.set(entity.id, component);

    // 更新水体组件以适应移动设备
    this.updateWaterBodyForMobile(component, this.currentPerformanceLevel);

    Debug.log('WaterMobileOptimizationSystem', `添加水体组件: ${entity.id}`);
  }

  /**
   * 移除水体组件
   * @param entity 实体
   */
  public removeWaterBody(entity: Entity): void {
    this.waterBodies.delete(entity.id);

    Debug.log('WaterMobileOptimizationSystem', `移除水体组件: ${entity.id}`);
  }

  /**
   * 获取当前性能级别
   * @returns 当前性能级别
   */
  public getCurrentPerformanceLevel(): DevicePerformanceLevel {
    return this.currentPerformanceLevel;
  }

  /**
   * 获取监控数据
   * @returns 监控数据
   */
  public getMonitorData(): any {
    return { ...this.monitorData };
  }

  /**
   * 获取性能配置
   * @param level 性能级别
   * @returns 性能配置
   */
  public getPerformanceConfig(level: DevicePerformanceLevel): any {
    return { ...this.performanceConfigs[level] };
  }

  /**
   * 设置性能配置
   * @param level 性能级别
   * @param config 性能配置
   */
  public setPerformanceConfig(level: DevicePerformanceLevel, config: any): void {
    this.performanceConfigs[level] = { ...this.performanceConfigs[level], ...config };

    // 如果当前性能级别是被修改的级别，则应用新配置
    if (this.currentPerformanceLevel === level) {
      this.applyPerformanceConfig(level);
    }
  }
}
