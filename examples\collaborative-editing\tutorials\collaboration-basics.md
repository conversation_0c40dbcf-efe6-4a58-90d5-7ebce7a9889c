# 协作编辑入门

本教程将指导您如何使用DL（Digital Learning）引擎的协作编辑功能，包括连接到协作会话、实时编辑、聊天交流和冲突解决等基本操作。

## 目录

- [概述](#概述)
- [连接到协作会话](#连接到协作会话)
- [用户界面](#用户界面)
- [实时编辑](#实时编辑)
- [聊天和交流](#聊天和交流)
- [冲突解决](#冲突解决)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 概述

DL（Digital Learning）引擎的协作编辑功能允许多个用户同时编辑同一个场景或项目，实现实时协作开发。主要功能包括：

- 多人同时编辑
- 实时操作同步
- 用户状态显示
- 冲突检测和解决
- 操作历史记录
- 权限管理
- 实时聊天

## 连接到协作会话

### 创建协作会话

1. 打开DL（Digital Learning）引擎编辑器
2. 选择"文件 > 协作 > 创建协作会话"
3. 设置会话名称和权限
4. 点击"创建"按钮
5. 系统会生成一个会话ID，您可以将此ID分享给其他用户

### 加入协作会话

1. 打开DL（Digital Learning）引擎编辑器
2. 选择"文件 > 协作 > 加入协作会话"
3. 输入会话ID
4. 点击"加入"按钮
5. 系统会连接到协作会话并同步当前场景

### 用户身份和权限

加入协作会话时，您需要使用自己的账号登录。根据会话创建者设置的权限，您可能拥有以下角色之一：

- **所有者**：拥有所有权限，可以管理用户和权限
- **管理员**：可以编辑场景、管理用户，但不能管理权限
- **编辑者**：可以编辑场景，但不能管理用户和权限
- **查看者**：只能查看场景，不能进行任何编辑操作

## 用户界面

### 协作面板

协作面板位于编辑器右侧，包含以下标签页：

- **用户**：显示当前在线用户列表和状态
- **聊天**：实时聊天功能
- **历史**：操作历史记录
- **权限**：用户权限管理（仅对所有者和管理员可见）

### 用户状态指示器

在场景中，您可以看到其他用户的光标位置和当前选中的对象，这些都用不同的颜色标记，以便区分不同的用户。

### 操作历史

操作历史面板显示所有用户的编辑操作，您可以查看操作详情，并在需要时回滚到特定版本。

## 实时编辑

### 基本编辑操作

在协作模式下，基本编辑操作与单人模式相同：

1. 选择对象：点击场景中的对象
2. 移动对象：使用移动工具拖动对象
3. 旋转对象：使用旋转工具旋转对象
4. 缩放对象：使用缩放工具调整对象大小
5. 修改属性：在属性面板中修改对象属性

所有这些操作都会实时同步给其他用户。

### 锁定对象

为了避免冲突，您可以锁定正在编辑的对象：

1. 选择对象
2. 右键点击并选择"锁定"
3. 锁定后，其他用户将无法编辑该对象，直到您解锁或离开会话

### 查看其他用户的编辑

您可以看到其他用户正在编辑的对象，它们会用用户特定的颜色高亮显示。您也可以看到其他用户的光标位置，以便了解他们正在关注的区域。

## 聊天和交流

### 发送消息

1. 切换到聊天标签页
2. 在输入框中输入消息
3. 按回车键或点击发送按钮

### 提及用户

您可以在消息中提及特定用户：

1. 在消息中输入@符号
2. 选择要提及的用户
3. 被提及的用户会收到通知

### 添加注释

您可以在场景中添加注释，以便与其他用户交流：

1. 选择对象
2. 右键点击并选择"添加注释"
3. 输入注释内容
4. 点击"保存"按钮

注释会显示在场景中，其他用户可以查看和回复。

## 冲突解决

### 冲突检测

当多个用户同时编辑同一个对象时，可能会发生冲突。系统会自动检测冲突并提示您解决。

### 解决冲突

当检测到冲突时，系统会显示冲突解决对话框，您可以选择以下选项之一：

1. **保留我的更改**：使用您的编辑覆盖其他用户的编辑
2. **采用他人的更改**：放弃您的编辑，使用其他用户的编辑
3. **手动合并**：手动选择要保留的部分

### 避免冲突

为了减少冲突，您可以：

1. 使用锁定功能锁定正在编辑的对象
2. 通过聊天功能协调编辑工作
3. 分配不同的编辑区域给不同的用户
4. 定期保存和同步场景

## 最佳实践

### 协作工作流

1. **规划**：在开始编辑前，与团队成员讨论并分配任务
2. **沟通**：使用聊天功能保持沟通，及时通知重要更改
3. **同步**：定期保存和同步场景，避免长时间的不同步状态
4. **锁定**：编辑重要对象时使用锁定功能
5. **检查**：完成编辑后检查变更，确保没有意外修改

### 性能优化

在多人协作时，为了保持良好的性能：

1. 避免同时编辑大量对象
2. 使用低细节模式进行编辑
3. 关闭不必要的视觉效果
4. 定期清理场景中的临时对象
5. 使用实例化技术减少重复对象

## 常见问题

### 为什么我看不到其他用户的编辑？

可能的原因：
- 网络连接问题
- 用户权限不足
- 场景同步延迟

解决方法：
- 检查网络连接
- 确认您有正确的权限
- 尝试手动同步场景（文件 > 协作 > 同步场景）

### 如何解决频繁的冲突？

- 与团队成员协调编辑区域
- 使用锁定功能
- 分配不同的任务给不同的用户
- 建立清晰的协作规则

### 如何恢复之前的版本？

1. 打开历史标签页
2. 找到要恢复的版本
3. 右键点击并选择"恢复到此版本"
4. 确认恢复操作

### 如何离开协作会话？

1. 选择"文件 > 协作 > 断开协作"
2. 确认断开连接
3. 如果您有未保存的更改，系统会提示您保存
