/**
 * 共享事件定义
 */

// 导出事件类型
export * from './event-types';

// 用户相关事件
export const USER_CREATED = 'user.created';
export const USER_UPDATED = 'user.updated';
export const USER_DELETED = 'user.deleted';
export const USER_LOGGED_IN = 'user.logged_in';
export const USER_LOGGED_OUT = 'user.logged_out';

// 项目相关事件
export const PROJECT_CREATED = 'project.created';
export const PROJECT_UPDATED = 'project.updated';
export const PROJECT_DELETED = 'project.deleted';
export const PROJECT_SHARED = 'project.shared';
export const PROJECT_UNSHARED = 'project.unshared';
export const SCENE_CREATED = 'scene.created';
export const SCENE_UPDATED = 'scene.updated';
export const SCENE_DELETED = 'scene.deleted';

// 资产相关事件
export const ASSET_UPLOADED = 'asset.uploaded';
export const ASSET_UPDATED = 'asset.updated';
export const ASSET_DELETED = 'asset.deleted';
export const ASSET_PROCESSED = 'asset.processed';
export const ASSET_PROCESSING_FAILED = 'asset.processing_failed';

// 渲染相关事件
export const RENDER_REQUESTED = 'render.requested';
export const RENDER_STARTED = 'render.started';
export const RENDER_COMPLETED = 'render.completed';
export const RENDER_FAILED = 'render.failed';

// 系统事件
export const SERVICE_REGISTERED = 'service.registered';
export const SERVICE_DEREGISTERED = 'service.deregistered';
export const SERVICE_HEALTH_CHANGED = 'service.health_changed';

// 事务相关事件
export const TRANSACTION_STARTED = 'transaction.started';
export const TRANSACTION_PREPARED = 'transaction.prepared';
export const TRANSACTION_COMMITTED = 'transaction.committed';
export const TRANSACTION_ABORTED = 'transaction.aborted';
export const TRANSACTION_COMPLETED = 'transaction.completed';
