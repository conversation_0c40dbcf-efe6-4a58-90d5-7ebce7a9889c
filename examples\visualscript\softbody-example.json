{"id": "softbody-example", "name": "软体物理节点示例", "description": "展示如何使用视觉脚本系统中的软体物理节点", "version": "1.0.0", "nodes": [{"id": "start-node", "type": "core/events/onStart", "position": {"x": 100, "y": 100}, "data": {"label": "开始"}, "flows": {"flow": {"nodeId": "create-floor", "socket": "flow"}}}, {"id": "create-floor", "type": "entity/create", "position": {"x": 300, "y": 100}, "data": {"label": "创建地面"}, "parameters": {"name": {"value": "地面"}}, "flows": {"flow": {"nodeId": "add-floor-mesh", "socket": "flow"}}}, {"id": "add-floor-mesh", "type": "entity/component/add", "position": {"x": 500, "y": 100}, "data": {"label": "添加地面网格"}, "parameters": {"componentType": {"value": "MeshComponent"}, "meshType": {"value": "plane"}, "width": {"value": 20}, "height": {"value": 20}}, "inputs": {"entity": {"nodeId": "create-floor", "socket": "entity"}}, "flows": {"flow": {"nodeId": "add-floor-physics", "socket": "flow"}}}, {"id": "add-floor-physics", "type": "entity/component/add", "position": {"x": 700, "y": 100}, "data": {"label": "添加地面物理组件"}, "parameters": {"componentType": {"value": "PhysicsBodyComponent"}, "bodyType": {"value": "static"}}, "inputs": {"entity": {"nodeId": "create-floor", "socket": "entity"}}, "flows": {"flow": {"nodeId": "add-floor-collider", "socket": "flow"}}}, {"id": "add-floor-collider", "type": "entity/component/add", "position": {"x": 900, "y": 100}, "data": {"label": "添加地面碰撞体"}, "parameters": {"componentType": {"value": "PhysicsColliderComponent"}, "colliderType": {"value": "box"}, "size": {"value": {"x": 20, "y": 0.1, "z": 20}}}, "inputs": {"entity": {"nodeId": "create-floor", "socket": "entity"}}, "flows": {"flow": {"nodeId": "create-cloth", "socket": "flow"}}}, {"id": "create-cloth", "type": "physics/softbody/create<PERSON>loth", "position": {"x": 300, "y": 300}, "data": {"label": "创建布料"}, "parameters": {"width": {"value": 5}, "height": {"value": 5}, "segments": {"value": 20}, "position": {"value": {"x": 0, "y": 5, "z": 0}}, "fixedCorners": {"value": true}, "mass": {"value": 1}, "stiffness": {"value": 0.9}}, "flows": {"flow": {"nodeId": "create-rope", "socket": "flow"}}}, {"id": "create-rope", "type": "physics/softbody/createRope", "position": {"x": 600, "y": 300}, "data": {"label": "创建绳索"}, "parameters": {"start": {"value": {"x": -5, "y": 8, "z": 0}}, "end": {"value": {"x": -5, "y": 0, "z": 0}}, "segments": {"value": 20}, "fixedEnds": {"value": true}, "mass": {"value": 1}, "stiffness": {"value": 0.8}}, "flows": {"flow": {"nodeId": "create-balloon", "socket": "flow"}}}, {"id": "create-balloon", "type": "physics/softbody/createBalloon", "position": {"x": 900, "y": 300}, "data": {"label": "创建气球"}, "parameters": {"radius": {"value": 1.5}, "position": {"value": {"x": 5, "y": 5, "z": 0}}, "segments": {"value": 16}, "pressure": {"value": 100}, "mass": {"value": 0.5}, "stiffness": {"value": 0.9}}, "flows": {"flow": {"nodeId": "create-jelly", "socket": "flow"}}}, {"id": "create-jelly", "type": "physics/softbody/createJelly", "position": {"x": 300, "y": 500}, "data": {"label": "创建果冻"}, "parameters": {"size": {"value": {"x": 2, "y": 2, "z": 2}}, "position": {"value": {"x": 0, "y": 10, "z": 5}}, "resolution": {"value": 8}, "mass": {"value": 2}, "stiffness": {"value": 0.8}, "damping": {"value": 0.3}}, "flows": {"flow": {"nodeId": "create-obstacle", "socket": "flow"}}}, {"id": "create-obstacle", "type": "entity/create", "position": {"x": 600, "y": 500}, "data": {"label": "创建障碍物"}, "parameters": {"name": {"value": "障碍物"}}, "flows": {"flow": {"nodeId": "add-obstacle-mesh", "socket": "flow"}}}, {"id": "add-obstacle-mesh", "type": "entity/component/add", "position": {"x": 900, "y": 500}, "data": {"label": "添加障碍物网格"}, "parameters": {"componentType": {"value": "MeshComponent"}, "meshType": {"value": "box"}, "size": {"value": 2}}, "inputs": {"entity": {"nodeId": "create-obstacle", "socket": "entity"}}, "flows": {"flow": {"nodeId": "add-obstacle-transform", "socket": "flow"}}}, {"id": "add-obstacle-transform", "type": "entity/component/add", "position": {"x": 1200, "y": 500}, "data": {"label": "添加障碍物变换"}, "parameters": {"componentType": {"value": "TransformComponent"}, "position": {"value": {"x": 0, "y": 1, "z": 5}}}, "inputs": {"entity": {"nodeId": "create-obstacle", "socket": "entity"}}, "flows": {"flow": {"nodeId": "add-obstacle-physics", "socket": "flow"}}}, {"id": "add-obstacle-physics", "type": "entity/component/add", "position": {"x": 300, "y": 700}, "data": {"label": "添加障碍物物理组件"}, "parameters": {"componentType": {"value": "PhysicsBodyComponent"}, "bodyType": {"value": "dynamic"}, "mass": {"value": 10}}, "inputs": {"entity": {"nodeId": "create-obstacle", "socket": "entity"}}, "flows": {"flow": {"nodeId": "add-obstacle-collider", "socket": "flow"}}}, {"id": "add-obstacle-collider", "type": "entity/component/add", "position": {"x": 600, "y": 700}, "data": {"label": "添加障碍物碰撞体"}, "parameters": {"componentType": {"value": "PhysicsColliderComponent"}, "colliderType": {"value": "box"}, "size": {"value": {"x": 2, "y": 2, "z": 2}}}, "inputs": {"entity": {"nodeId": "create-obstacle", "socket": "entity"}}, "flows": {"flow": {"nodeId": "setup-cut-event", "socket": "flow"}}}, {"id": "setup-cut-event", "type": "core/events/onKeyDown", "position": {"x": 900, "y": 700}, "data": {"label": "设置切割事件"}, "parameters": {"key": {"value": "Space"}}, "flows": {"flow": {"nodeId": "cut-cloth", "socket": "flow"}}}, {"id": "cut-cloth", "type": "physics/softbody/cut", "position": {"x": 1200, "y": 700}, "data": {"label": "切割布料"}, "parameters": {"cutPoint": {"value": {"x": 0, "y": 5, "z": 0}}, "cutNormal": {"value": {"x": 1, "y": 0, "z": 0}}}, "inputs": {"entity": {"nodeId": "create-cloth", "socket": "entity"}}, "flows": {"flow": {"nodeId": "debug-cut", "socket": "flow"}}}, {"id": "debug-cut", "type": "core/debug/print", "position": {"x": 1500, "y": 700}, "data": {"label": "输出切割信息"}, "parameters": {"message": {"value": "布料已切割！"}}}], "variables": [], "customEvents": []}