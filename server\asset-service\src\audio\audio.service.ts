/**
 * 音频服务
 */
import { Injectable } from '@nestjs/common';
import { AssetsService } from '../assets/assets.service';
import { AssetType } from '../assets/enums/asset.enums';

@Injectable()
export class AudioService {
  constructor(private readonly assetsService: AssetsService) {}

  /**
   * 查找所有音频
   */
  async findAll(userId: string, projectId?: string, tags?: string[]) {
    return this.assetsService.findAll(userId, projectId, AssetType.AUDIO, tags);
  }

  /**
   * 搜索音频
   */
  async search(query: string, userId: string) {
    return this.assetsService.search(query, userId, AssetType.AUDIO);
  }
}
