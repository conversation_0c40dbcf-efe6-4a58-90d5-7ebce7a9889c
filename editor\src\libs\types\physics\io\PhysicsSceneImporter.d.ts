import { Scene } from '../../scene/Scene';
import { PhysicsSceneExportData } from './PhysicsSceneExporter';
/**
 * 物理场景导入选项
 */
export interface PhysicsSceneImportOptions {
    /** 是否导入物理体 */
    importBodies?: boolean;
    /** 是否导入碰撞器 */
    importColliders?: boolean;
    /** 是否导入约束 */
    importConstraints?: boolean;
    /** 是否导入物理世界 */
    importWorld?: boolean;
    /** 是否导入材质 */
    importMaterials?: boolean;
    /** 是否清除现有物理组件 */
    clearExisting?: boolean;
    /** 实体ID映射 */
    entityIdMap?: Map<string, string>;
}
/**
 * 物理场景导入器
 */
export declare class PhysicsSceneImporter {
    /**
     * 创建物理场景导入器
     */
    constructor();
    /**
     * 导入场景
     * @param scene 场景
     * @param data 导入数据
     * @param options 导入选项
     * @returns 是否导入成功
     */
    import(scene: Scene, data: PhysicsSceneExportData, options?: PhysicsSceneImportOptions): boolean;
    /**
     * 从JSON导入
     * @param scene 场景
     * @param json JSON字符串
     * @param options 导入选项
     * @returns 是否导入成功
     */
    importFromJSON(scene: Scene, json: string, options?: PhysicsSceneImportOptions): boolean;
    /**
     * 清除物理组件
     * @param scene 场景
     */
    private clearPhysicsComponents;
    /**
     * 导入材质
     * @param materials 材质数据
     */
    private importMaterials;
    /**
     * 导入物理世界
     * @param scene 场景
     * @param worldData 物理世界数据
     * @param entityIdMap 实体ID映射
     */
    private importWorld;
    /**
     * 导入物理体
     * @param scene 场景
     * @param bodiesData 物理体数据
     * @param entityIdMap 实体ID映射
     */
    private importBodies;
    /**
     * 导入碰撞器
     * @param scene 场景
     * @param collidersData 碰撞器数据
     * @param entityIdMap 实体ID映射
     */
    private importColliders;
    /**
     * 导入约束
     * @param scene 场景
     * @param constraintsData 约束数据
     * @param entityIdMap 实体ID映射
     */
    private importConstraints;
}
