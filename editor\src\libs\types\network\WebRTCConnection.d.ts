/**
 * WebRTC连接
 * 负责管理与其他用户的WebRTC点对点连接
 */
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkMessage } from './NetworkMessage';
/**
 * WebRTC连接状态
 */
export declare enum WebRTCConnectionState {
    /** 新建 */
    NEW = "new",
    /** 正在连接 */
    CONNECTING = "connecting",
    /** 已连接 */
    CONNECTED = "connected",
    /** 正在断开连接 */
    DISCONNECTING = "disconnecting",
    /** 已断开连接 */
    DISCONNECTED = "disconnected",
    /** 连接失败 */
    FAILED = "failed",
    /** 连接关闭 */
    CLOSED = "closed"
}
/**
 * WebRTC连接配置
 */
export interface WebRTCConnectionConfig {
    /** 是否启用数据通道 */
    enableDataChannel?: boolean;
    /** 是否启用音频 */
    enableAudio?: boolean;
    /** 是否启用视频 */
    enableVideo?: boolean;
    /** 是否启用屏幕共享 */
    enableScreenShare?: boolean;
    /** 数据通道配置 */
    dataChannelConfig?: RTCDataChannelInit;
    /** 音频约束 */
    audioConstraints?: MediaTrackConstraints;
    /** 视频约束 */
    videoConstraints?: MediaTrackConstraints;
    /** 是否使用压缩 */
    useCompression?: boolean;
}
/**
 * WebRTC连接
 * 负责管理与其他用户的WebRTC点对点连接
 */
export declare class WebRTCConnection extends EventEmitter {
    /** 对等连接 */
    private peerConnection;
    /** 数据通道 */
    private dataChannel;
    /** 远程用户ID */
    private userId;
    /** ICE服务器配置 */
    private iceServers;
    /** 连接状态 */
    private state;
    /** 配置选项 */
    private config;
    /** 本地媒体流 */
    private localStream;
    /** 远程媒体流 */
    private remoteStream;
    /** 消息序列化器 */
    private messageSerializer;
    /** 是否是发起方 */
    private isInitiator;
    /** 是否已交换SDP */
    private sdpExchanged;
    /** 待处理的ICE候选 */
    private pendingCandidates;
    /** 心跳间隔（毫秒） */
    private heartbeatInterval;
    /** 心跳定时器ID */
    private heartbeatTimerId;
    /** 最后一次接收消息的时间戳 */
    private lastReceivedTime;
    /** 消息队列 */
    private messageQueue;
    /**
     * 创建WebRTC连接
     * @param userId 远程用户ID
     * @param iceServers ICE服务器配置
     * @param config 连接配置
     */
    constructor(userId: string, iceServers?: RTCIceServer[], config?: WebRTCConnectionConfig);
    /**
     * 创建对等连接
     */
    createConnection(): void;
    /**
     * 创建提议
     */
    createOffer(): Promise<void>;
    /**
     * 处理提议
     * @param offer 提议
     */
    handleOffer(offer: RTCSessionDescriptionInit): Promise<void>;
    /**
     * 处理应答
     * @param answer 应答
     */
    handleAnswer(answer: RTCSessionDescriptionInit): Promise<void>;
    /**
     * 处理ICE候选
     * @param candidate ICE候选
     */
    handleIceCandidate(candidate: RTCIceCandidateInit): Promise<void>;
    /**
     * 发送消息
     * @param message 消息对象
     */
    send(message: NetworkMessage): Promise<void>;
    /**
     * 断开连接
     */
    disconnect(): Promise<void>;
    /**
     * 更新连接
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 获取远程用户ID
     * @returns 远程用户ID
     */
    getUserId(): string;
    /**
     * 获取连接状态
     * @returns 连接状态
     */
    getState(): WebRTCConnectionState;
    /**
     * 获取本地媒体流
     * @returns 本地媒体流
     */
    getLocalStream(): MediaStream | null;
    /**
     * 获取远程媒体流
     * @returns 远程媒体流
     */
    getRemoteStream(): MediaStream | null;
    /**
     * 设置心跳间隔
     * @param interval 心跳间隔（毫秒）
     */
    setHeartbeatInterval(interval: number): void;
    /**
     * 设置对等连接事件监听器
     */
    private setupPeerConnectionListeners;
    /**
     * 设置数据通道事件监听器
     */
    private setupDataChannelListeners;
    /**
     * 创建数据通道
     */
    private createDataChannel;
    /**
     * 获取本地媒体
     */
    private getLocalMedia;
    /**
     * 处理待处理的ICE候选
     */
    private processPendingCandidates;
    /**
     * 重新连接
     */
    private reconnect;
    /**
     * 启动心跳
     */
    private startHeartbeat;
    /**
     * 停止心跳
     */
    private stopHeartbeat;
    /**
     * 发送心跳消息
     */
    private sendHeartbeat;
    /**
     * 处理消息队列
     */
    private processMessageQueue;
}
