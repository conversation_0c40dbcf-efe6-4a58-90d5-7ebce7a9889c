/**
 * 材质工厂
 * 用于创建各种类型的材质
 */
import * as THREE from 'three';

/**
 * 材质类型枚举
 */
export enum MaterialType {
  /** 基础材质 */
  BASIC = 'basic',
  /** Lambert材质 */
  LAMBERT = 'lambert',
  /** Phong材质 */
  PHONG = 'phong',
  /** 标准材质 */
  STANDARD = 'standard',
  /** 物理材质 */
  PHYSICAL = 'physical',
  /** 卡通材质 */
  TOON = 'toon',
  /** 深度材质 */
  DEPTH = 'depth',
  /** 法线材质 */
  NORMAL = 'normal',
  /** 线条材质 */
  LINE_BASIC = 'lineBasic',
  /** 虚线材质 */
  LINE_DASHED = 'lineDashed',
  /** 点材质 */
  POINTS = 'points',
  /** 精灵材质 */
  SPRITE = 'sprite',
  /** 着色器材质 */
  SHADER = 'shader',
  /** 原始着色器材质 */
  RAW_SHADER = 'rawShader',
  /** 阴影材质 */
  SHADOW = 'shadow',
  /** Matcap材质 */
  MATCAP = 'matcap'
}

/**
 * 材质工厂类
 */
export class MaterialFactory {
  /** 材质创建函数映射 */
  private materialCreators: Map<string, (params: any) => THREE.Material> = new Map();

  /**
   * 创建材质工厂
   */
  constructor() {
    this.registerDefaultMaterials();
  }

  /**
   * 注册默认材质
   */
  private registerDefaultMaterials(): void {
    // 注册基础材质
    this.registerMaterial(MaterialType.BASIC, (params) => new THREE.MeshBasicMaterial(params));

    // 注册Lambert材质
    this.registerMaterial(MaterialType.LAMBERT, (params) => new THREE.MeshLambertMaterial(params));

    // 注册Phong材质
    this.registerMaterial(MaterialType.PHONG, (params) => new THREE.MeshPhongMaterial(params));

    // 注册标准材质
    this.registerMaterial(MaterialType.STANDARD, (params) => new THREE.MeshStandardMaterial(params));

    // 注册物理材质
    this.registerMaterial(MaterialType.PHYSICAL, (params) => new THREE.MeshPhysicalMaterial(params));

    // 注册卡通材质
    this.registerMaterial(MaterialType.TOON, (params) => new THREE.MeshToonMaterial(params));

    // 注册深度材质
    this.registerMaterial(MaterialType.DEPTH, (params) => new THREE.MeshDepthMaterial(params));

    // 注册法线材质
    this.registerMaterial(MaterialType.NORMAL, (params) => new THREE.MeshNormalMaterial(params));

    // 注册线条材质
    this.registerMaterial(MaterialType.LINE_BASIC, (params) => new THREE.LineBasicMaterial(params));

    // 注册虚线材质
    this.registerMaterial(MaterialType.LINE_DASHED, (params) => new THREE.LineDashedMaterial(params));

    // 注册点材质
    this.registerMaterial(MaterialType.POINTS, (params) => new THREE.PointsMaterial(params));

    // 注册精灵材质
    this.registerMaterial(MaterialType.SPRITE, (params) => new THREE.SpriteMaterial(params));

    // 注册着色器材质
    this.registerMaterial(MaterialType.SHADER, (params) => new THREE.ShaderMaterial(params));

    // 注册原始着色器材质
    this.registerMaterial(MaterialType.RAW_SHADER, (params) => new THREE.RawShaderMaterial(params));

    // 注册阴影材质
    this.registerMaterial(MaterialType.SHADOW, (params) => new THREE.ShadowMaterial(params));

    // 注册Matcap材质
    this.registerMaterial(MaterialType.MATCAP, (params) => new THREE.MeshMatcapMaterial(params));
  }

  /**
   * 注册材质
   * @param type 材质类型
   * @param creator 创建函数
   */
  public registerMaterial(type: string, creator: (params: any) => THREE.Material): void {
    this.materialCreators.set(type, creator);
  }

  /**
   * 创建材质
   * @param type 材质类型
   * @param params 材质参数
   * @returns 材质
   */
  public createMaterial(type: string, params: any = {}): THREE.Material {
    // 获取创建函数
    const creator = this.materialCreators.get(type);
    
    // 如果找不到创建函数，则使用默认材质
    if (!creator) {
      console.warn(`未知的材质类型: ${type}，使用基础材质代替`);
      return new THREE.MeshBasicMaterial(params);
    }
    
    // 创建材质
    return creator(params);
  }

  /**
   * 创建基础材质
   * @param params 材质参数
   * @returns 基础材质
   */
  public createBasicMaterial(params: THREE.MeshBasicMaterialParameters = {}): THREE.MeshBasicMaterial {
    return new THREE.MeshBasicMaterial(params);
  }

  /**
   * 创建Lambert材质
   * @param params 材质参数
   * @returns Lambert材质
   */
  public createLambertMaterial(params: THREE.MeshLambertMaterialParameters = {}): THREE.MeshLambertMaterial {
    return new THREE.MeshLambertMaterial(params);
  }

  /**
   * 创建Phong材质
   * @param params 材质参数
   * @returns Phong材质
   */
  public createPhongMaterial(params: THREE.MeshPhongMaterialParameters = {}): THREE.MeshPhongMaterial {
    return new THREE.MeshPhongMaterial(params);
  }

  /**
   * 创建标准材质
   * @param params 材质参数
   * @returns 标准材质
   */
  public createStandardMaterial(params: THREE.MeshStandardMaterialParameters = {}): THREE.MeshStandardMaterial {
    return new THREE.MeshStandardMaterial(params);
  }

  /**
   * 创建物理材质
   * @param params 材质参数
   * @returns 物理材质
   */
  public createPhysicalMaterial(params: THREE.MeshPhysicalMaterialParameters = {}): THREE.MeshPhysicalMaterial {
    return new THREE.MeshPhysicalMaterial(params);
  }

  /**
   * 创建卡通材质
   * @param params 材质参数
   * @returns 卡通材质
   */
  public createToonMaterial(params: THREE.MeshToonMaterialParameters = {}): THREE.MeshToonMaterial {
    return new THREE.MeshToonMaterial(params);
  }

  /**
   * 创建深度材质
   * @param params 材质参数
   * @returns 深度材质
   */
  public createDepthMaterial(params: THREE.MeshDepthMaterialParameters = {}): THREE.MeshDepthMaterial {
    return new THREE.MeshDepthMaterial(params);
  }

  /**
   * 创建法线材质
   * @param params 材质参数
   * @returns 法线材质
   */
  public createNormalMaterial(params: THREE.MeshNormalMaterialParameters = {}): THREE.MeshNormalMaterial {
    return new THREE.MeshNormalMaterial(params);
  }

  /**
   * 创建线条材质
   * @param params 材质参数
   * @returns 线条材质
   */
  public createLineBasicMaterial(params: THREE.LineBasicMaterialParameters = {}): THREE.LineBasicMaterial {
    return new THREE.LineBasicMaterial(params);
  }

  /**
   * 创建虚线材质
   * @param params 材质参数
   * @returns 虚线材质
   */
  public createLineDashedMaterial(params: THREE.LineDashedMaterialParameters = {}): THREE.LineDashedMaterial {
    return new THREE.LineDashedMaterial(params);
  }

  /**
   * 创建点材质
   * @param params 材质参数
   * @returns 点材质
   */
  public createPointsMaterial(params: THREE.PointsMaterialParameters = {}): THREE.PointsMaterial {
    return new THREE.PointsMaterial(params);
  }

  /**
   * 创建精灵材质
   * @param params 材质参数
   * @returns 精灵材质
   */
  public createSpriteMaterial(params: THREE.SpriteMaterialParameters = {}): THREE.SpriteMaterial {
    return new THREE.SpriteMaterial(params);
  }

  /**
   * 创建着色器材质
   * @param params 材质参数
   * @returns 着色器材质
   */
  public createShaderMaterial(params: THREE.ShaderMaterialParameters = {}): THREE.ShaderMaterial {
    return new THREE.ShaderMaterial(params);
  }

  /**
   * 创建原始着色器材质
   * @param params 材质参数
   * @returns 原始着色器材质
   */
  public createRawShaderMaterial(params: THREE.ShaderMaterialParameters = {}): THREE.RawShaderMaterial {
    return new THREE.RawShaderMaterial(params);
  }

  /**
   * 创建阴影材质
   * @param params 材质参数
   * @returns 阴影材质
   */
  public createShadowMaterial(params: THREE.ShadowMaterialParameters = {}): THREE.ShadowMaterial {
    return new THREE.ShadowMaterial(params);
  }

  /**
   * 创建Matcap材质
   * @param params 材质参数
   * @returns Matcap材质
   */
  public createMatcapMaterial(params: THREE.MeshMatcapMaterialParameters = {}): THREE.MeshMatcapMaterial {
    return new THREE.MeshMatcapMaterial(params);
  }
}
