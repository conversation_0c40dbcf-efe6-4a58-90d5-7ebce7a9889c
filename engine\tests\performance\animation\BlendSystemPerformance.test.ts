/**
 * 动画混合系统性能测试
 * 测试动画混合系统在不同条件下的性能表现
 */
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import * as THREE from 'three';
import { World } from '../../../src/core/World';
import { Entity } from '../../../src/core/Entity';
import { AnimationSystem } from '../../../src/animation/AnimationSystem';
import { Animator } from '../../../src/animation/Animator';
import { AnimationBlender } from '../../../src/animation/AnimationBlender';
import { BlendMode } from '../../../src/animation/BlendMode';
import { AnimationMask } from '../../../src/animation/AnimationMask';
import { BlendPerformanceMonitor } from '../../../src/animation/utils/BlendPerformanceMonitor';
import { BoneGroupType } from '../../../src/animation/BoneGroupType';

describe('动画混合系统性能测试', () => {
  let world: World;
  let entity: Entity;
  let animationSystem: AnimationSystem;
  let animator: Animator;
  let blender: AnimationBlender;
  let scene: THREE.Scene;
  let camera: THREE.PerspectiveCamera;
  let renderer: THREE.WebGLRenderer;
  let clock: THREE.Clock;
  let model: THREE.Group;
  let skeleton: THREE.SkeletonHelper;
  let mixer: THREE.AnimationMixer;
  let animations: THREE.AnimationClip[];
  let performanceMonitor: BlendPerformanceMonitor;

  // 在每个测试前设置场景
  beforeEach(() => {
    // 创建世界
    world = new World();
    
    // 添加系统
    animationSystem = new AnimationSystem(world);
    world.addSystem(animationSystem);
    
    // 创建场景
    scene = new THREE.Scene();
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    renderer = new THREE.WebGLRenderer({ antialias: true });
    clock = new THREE.Clock();
    
    // 创建实体
    entity = new Entity('testCharacter');
    world.addEntity(entity);
    
    // 创建模型和骨骼
    model = new THREE.Group();
    skeleton = createTestSkeleton();
    model.add(skeleton.bones[0]); // 添加根骨骼到模型
    
    // 创建动画
    animations = createTestAnimations(skeleton);
    
    // 创建混合器
    mixer = new THREE.AnimationMixer(model);
    
    // 创建动画控制器
    animator = new Animator(mixer, animations);
    entity.addComponent('animator', animator);
    
    // 创建混合器
    blender = new AnimationBlender(animator);
    entity.addComponent('blender', blender);
    
    // 启动世界
    world.start();
    
    // 初始化性能监控器
    performanceMonitor = BlendPerformanceMonitor.getInstance();
    performanceMonitor.clear();
    performanceMonitor.enable(10000);
  });

  // 在每个测试后清理场景
  afterEach(() => {
    world.stop();
    scene.clear();
    renderer.dispose();
    performanceMonitor.disable();
  });

  // 测试不同数量混合层的性能
  it('测试不同数量混合层的性能', () => {
    const layerCounts = [1, 5, 10, 20, 50, 100];
    const results: Record<number, number> = {};
    
    for (const count of layerCounts) {
      // 清除现有层
      blender.clearLayers();
      
      // 添加指定数量的层
      for (let i = 0; i < count; i++) {
        const clipName = i % animations.length;
        blender.addLayer(animations[clipName].name, Math.random(), BlendMode.OVERRIDE);
      }
      
      // 记录开始时间
      const startTime = performance.now();
      
      // 执行多次更新
      const iterations = 100;
      for (let i = 0; i < iterations; i++) {
        blender.update(0.016); // 约60fps
      }
      
      // 记录结束时间
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 记录结果
      results[count] = duration / iterations;
      
      // 输出结果
      console.log(`混合层数量: ${count}, 平均更新时间: ${results[count].toFixed(4)}ms`);
    }
    
    // 验证结果（性能应该随着层数增加而降低，但不应该线性降低）
    for (let i = 1; i < layerCounts.length; i++) {
      const prevCount = layerCounts[i - 1];
      const currCount = layerCounts[i];
      const ratio = results[currCount] / results[prevCount];
      
      // 性能降低不应该超过层数增加的比例
      expect(ratio).toBeLessThan(currCount / prevCount * 1.5);
    }
  });

  // 测试不同混合模式的性能
  it('测试不同混合模式的性能', () => {
    const blendModes = [
      BlendMode.OVERRIDE,
      BlendMode.ADDITIVE,
      BlendMode.MULTIPLY
    ];
    const results: Record<string, number> = {};
    
    for (const mode of blendModes) {
      // 清除现有层
      blender.clearLayers();
      
      // 添加10个使用指定混合模式的层
      for (let i = 0; i < 10; i++) {
        const clipName = i % animations.length;
        blender.addLayer(animations[clipName].name, Math.random(), mode);
      }
      
      // 记录开始时间
      const startTime = performance.now();
      
      // 执行多次更新
      const iterations = 100;
      for (let i = 0; i < iterations; i++) {
        blender.update(0.016); // 约60fps
      }
      
      // 记录结束时间
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 记录结果
      results[mode] = duration / iterations;
      
      // 输出结果
      console.log(`混合模式: ${mode}, 平均更新时间: ${results[mode].toFixed(4)}ms`);
    }
    
    // 验证结果（不同混合模式的性能可能有差异，但不应该相差太大）
    const baseMode = BlendMode.OVERRIDE;
    for (const mode of blendModes) {
      if (mode === baseMode) continue;
      
      const ratio = results[mode] / results[baseMode];
      
      // 性能差异不应该超过50%
      expect(ratio).toBeLessThan(1.5);
      expect(ratio).toBeGreaterThan(0.5);
    }
  });

  // 测试遮罩对性能的影响
  it('测试遮罩对性能的影响', () => {
    // 创建不同类型的遮罩
    const noMask = null;
    const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);
    const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);
    const customMask = new AnimationMask();
    customMask.setBoneWeight('head', 0.8);
    customMask.setBoneWeight('spine', 0.6);
    customMask.setBoneWeight('leftArm', 0.4);
    customMask.setBoneWeight('rightArm', 0.4);
    
    const masks = [noMask, upperBodyMask, lowerBodyMask, customMask];
    const results: Record<string, number> = {};
    
    for (let i = 0; i < masks.length; i++) {
      const mask = masks[i];
      const maskName = mask ? (i === 1 ? 'upperBody' : i === 2 ? 'lowerBody' : 'custom') : 'none';
      
      // 清除现有层
      blender.clearLayers();
      
      // 添加10个使用指定遮罩的层
      for (let j = 0; j < 10; j++) {
        const clipName = j % animations.length;
        blender.addLayer(animations[clipName].name, Math.random(), BlendMode.OVERRIDE, 1.0, mask);
      }
      
      // 记录开始时间
      const startTime = performance.now();
      
      // 执行多次更新
      const iterations = 100;
      for (let j = 0; j < iterations; j++) {
        blender.update(0.016); // 约60fps
      }
      
      // 记录结束时间
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 记录结果
      results[maskName] = duration / iterations;
      
      // 输出结果
      console.log(`遮罩类型: ${maskName}, 平均更新时间: ${results[maskName].toFixed(4)}ms`);
    }
    
    // 验证结果（使用遮罩应该比不使用遮罩慢，但不应该慢太多）
    const baseResult = results['none'];
    for (const maskName in results) {
      if (maskName === 'none') continue;
      
      const ratio = results[maskName] / baseResult;
      
      // 使用遮罩的性能不应该比不使用遮罩慢超过100%
      expect(ratio).toBeLessThan(2.0);
    }
  });

  // 测试缓存对性能的影响
  it('测试缓存对性能的影响', () => {
    // 清除现有层
    blender.clearLayers();
    
    // 添加20个层
    for (let i = 0; i < 20; i++) {
      const clipName = i % animations.length;
      blender.addLayer(animations[clipName].name, Math.random(), BlendMode.OVERRIDE);
    }
    
    // 不使用缓存的测试
    blender.setCacheConfig(false);
    
    // 记录开始时间
    let startTime = performance.now();
    
    // 执行多次更新
    const iterations = 100;
    for (let i = 0; i < iterations; i++) {
      blender.update(0.016); // 约60fps
    }
    
    // 记录结束时间
    let endTime = performance.now();
    const withoutCacheDuration = (endTime - startTime) / iterations;
    
    // 使用缓存的测试
    blender.setCacheConfig(true, 1000);
    
    // 记录开始时间
    startTime = performance.now();
    
    // 执行多次更新
    for (let i = 0; i < iterations; i++) {
      blender.update(0.016); // 约60fps
    }
    
    // 记录结束时间
    endTime = performance.now();
    const withCacheDuration = (endTime - startTime) / iterations;
    
    // 输出结果
    console.log(`不使用缓存: ${withoutCacheDuration.toFixed(4)}ms, 使用缓存: ${withCacheDuration.toFixed(4)}ms`);
    console.log(`性能提升: ${((withoutCacheDuration - withCacheDuration) / withoutCacheDuration * 100).toFixed(2)}%`);
    
    // 验证结果（使用缓存应该比不使用缓存快）
    expect(withCacheDuration).toBeLessThan(withoutCacheDuration);
  });

  // 创建测试骨骼
  function createTestSkeleton(): THREE.SkeletonHelper {
    // 创建骨骼
    const bones: THREE.Bone[] = [];
    
    // 创建根骨骼
    const root = new THREE.Bone();
    root.name = 'root';
    bones.push(root);
    
    // 创建脊柱骨骼
    const spine = new THREE.Bone();
    spine.name = 'spine';
    spine.position.y = 1;
    root.add(spine);
    bones.push(spine);
    
    // 创建头部骨骼
    const head = new THREE.Bone();
    head.name = 'head';
    head.position.y = 0.5;
    spine.add(head);
    bones.push(head);
    
    // 创建左臂骨骼
    const leftArm = new THREE.Bone();
    leftArm.name = 'leftArm';
    leftArm.position.x = 0.5;
    spine.add(leftArm);
    bones.push(leftArm);
    
    // 创建右臂骨骼
    const rightArm = new THREE.Bone();
    rightArm.name = 'rightArm';
    rightArm.position.x = -0.5;
    spine.add(rightArm);
    bones.push(rightArm);
    
    // 创建左腿骨骼
    const leftLeg = new THREE.Bone();
    leftLeg.name = 'leftLeg';
    leftLeg.position.set(0.2, -1, 0);
    root.add(leftLeg);
    bones.push(leftLeg);
    
    // 创建右腿骨骼
    const rightLeg = new THREE.Bone();
    rightLeg.name = 'rightLeg';
    rightLeg.position.set(-0.2, -1, 0);
    root.add(rightLeg);
    bones.push(rightLeg);
    
    // 创建骨骼助手
    return new THREE.SkeletonHelper(root);
  }

  // 创建测试动画
  function createTestAnimations(skeleton: THREE.SkeletonHelper): THREE.AnimationClip[] {
    const animations: THREE.AnimationClip[] = [];
    
    // 创建空闲动画
    const idleClip = new THREE.AnimationClip('idle', 1, [
      // 轻微呼吸动画
      new THREE.QuaternionKeyframeTrack(
        'spine.quaternion',
        [0, 0.5, 1],
        [
          0, 0, 0, 1, // 初始姿势
          0.01, 0, 0, 0.9999, // 轻微前倾
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(idleClip);
    
    // 创建行走动画
    const walkClip = new THREE.AnimationClip('walk', 1, [
      // 左腿动画
      new THREE.QuaternionKeyframeTrack(
        'leftLeg.quaternion',
        [0, 0.5, 1],
        [
          0, 0, 0, 1, // 初始姿势
          0.2, 0, 0, 0.98, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      ),
      // 右腿动画
      new THREE.QuaternionKeyframeTrack(
        'rightLeg.quaternion',
        [0, 0.5, 1],
        [
          0, 0, 0, 1, // 初始姿势
          -0.2, 0, 0, 0.98, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(walkClip);
    
    // 创建跑步动画
    const runClip = new THREE.AnimationClip('run', 0.6, [
      // 左腿动画
      new THREE.QuaternionKeyframeTrack(
        'leftLeg.quaternion',
        [0, 0.3, 0.6],
        [
          0, 0, 0, 1, // 初始姿势
          0.4, 0, 0, 0.92, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      ),
      // 右腿动画
      new THREE.QuaternionKeyframeTrack(
        'rightLeg.quaternion',
        [0, 0.3, 0.6],
        [
          0, 0, 0, 1, // 初始姿势
          -0.4, 0, 0, 0.92, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(runClip);
    
    // 创建挥手动画
    const waveClip = new THREE.AnimationClip('wave', 1.5, [
      // 右臂动画
      new THREE.QuaternionKeyframeTrack(
        'rightArm.quaternion',
        [0, 0.5, 1, 1.5],
        [
          0, 0, 0, 1, // 初始姿势
          0, 0, 0.4, 0.92, // 抬起手臂
          0, 0, -0.4, 0.92, // 挥动
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(waveClip);
    
    return animations;
  }
});
