# 前后端API接口定义文档

**文档版本**: 1.0  
**创建日期**: 2025年8月25日  
**系统名称**: 数字人RAG交互系统  

## 目录

1. [系统架构概述](#系统架构概述)
2. [前端服务配置](#前端服务配置)
3. [API网关接口](#api网关接口)
4. [微服务接口定义](#微服务接口定义)
5. [前端API调用示例](#前端api调用示例)
6. [数据模型定义](#数据模型定义)
7. [错误处理规范](#错误处理规范)
8. [认证授权机制](#认证授权机制)

## 系统架构概述

### 微服务架构
```
前端编辑器 (React/TypeScript)
    ↓
API网关 (NestJS) - 端口: 3000
    ↓
┌─────────────────────────────────────────────────┐
│                微服务集群                        │
├─────────────────┬───────────────────────────────┤
│ 用户服务        │ 端口: 3001                    │
│ 项目服务        │ 端口: 3002                    │
│ 资产服务        │ 端口: 3003                    │
│ 场景生成服务    │ 端口: 3004                    │
│ 场景模板服务    │ 端口: 8004                    │
│ 资产库服务      │ 端口: 3005                    │
│ 渲染服务        │ 端口: 3006                    │
│ 协作服务        │ 端口: 3007                    │
│ 知识服务        │ 端口: 3008                    │
│ RAG引擎         │ 端口: 3009                    │
│ AI模型服务      │ 端口: 3010                    │
│ 绑定服务        │ 端口: 3011                    │
│ 游戏服务器      │ 端口: 3012                    │
│ 监控服务        │ 端口: 3013                    │
│ 服务注册中心    │ 端口: 8761                    │
└─────────────────┴───────────────────────────────┘
```

### 技术栈
- **前端**: React 18 + TypeScript + Vite + Ant Design
- **API网关**: NestJS + Express + Swagger
- **微服务**: NestJS + TypeORM + PostgreSQL + Redis
- **消息队列**: Redis Pub/Sub
- **文件存储**: MinIO/本地存储
- **监控**: Prometheus + Grafana

## 前端服务配置

### 环境配置 (editor/src/config/environment.ts)

```typescript
export interface EnvironmentConfig {
  // API配置
  API_BASE_URL: string;
  API_TIMEOUT: number;
  
  // WebSocket配置
  WS_BASE_URL: string;
  WS_RECONNECT_INTERVAL: number;
  
  // 文件上传配置
  UPLOAD_MAX_SIZE: number;
  UPLOAD_ALLOWED_TYPES: string[];
  
  // 缓存配置
  CACHE_ENABLED: boolean;
  CACHE_TIMEOUT: number;
}

// 开发环境配置
export const developmentConfig: EnvironmentConfig = {
  API_BASE_URL: 'http://localhost:3000/api',
  API_TIMEOUT: 30000,
  WS_BASE_URL: 'ws://localhost:3000',
  WS_RECONNECT_INTERVAL: 5000,
  UPLOAD_MAX_SIZE: 100 * 1024 * 1024, // 100MB
  UPLOAD_ALLOWED_TYPES: ['.glb', '.gltf', '.fbx', '.obj', '.png', '.jpg', '.jpeg', '.mp3', '.wav'],
  CACHE_ENABLED: true,
  CACHE_TIMEOUT: 5 * 60 * 1000 // 5分钟
};

// 生产环境配置
export const productionConfig: EnvironmentConfig = {
  API_BASE_URL: 'https://api.yourdomain.com/api',
  API_TIMEOUT: 30000,
  WS_BASE_URL: 'wss://api.yourdomain.com',
  WS_RECONNECT_INTERVAL: 5000,
  UPLOAD_MAX_SIZE: 500 * 1024 * 1024, // 500MB
  UPLOAD_ALLOWED_TYPES: ['.glb', '.gltf', '.fbx', '.obj', '.png', '.jpg', '.jpeg', '.mp3', '.wav'],
  CACHE_ENABLED: true,
  CACHE_TIMEOUT: 10 * 60 * 1000 // 10分钟
};
```

### API客户端配置 (editor/src/services/ApiClient.ts)

```typescript
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  enableCache: boolean;
  cacheTimeout: number;
  retryCount: number;
  retryDelay: number;
}

export class ApiClient extends EventEmitter {
  private instance: AxiosInstance;
  private config: ApiConfig;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private authToken: string | null = null;

  constructor(config?: Partial<ApiConfig>) {
    super();
    
    this.config = {
      baseURL: 'http://localhost:3000/api',
      timeout: 30000,
      enableCache: true,
      cacheTimeout: 5 * 60 * 1000,
      retryCount: 3,
      retryDelay: 1000,
      ...config
    };

    this.instance = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  // 设置认证令牌
  setAuthToken(token: string) {
    this.authToken = token;
    this.instance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  // 通用请求方法
  async request<T>(options: RequestOptions): Promise<ApiResponse<T>> {
    const cacheKey = this.getCacheKey(options);
    
    // 检查缓存
    if (options.useCache !== false && this.config.enableCache) {
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;
    }

    try {
      const response = await this.instance.request(options);
      const result: ApiResponse<T> = {
        data: response.data,
        success: true,
        message: response.data.message,
        code: response.status
      };

      // 缓存结果
      if (options.useCache !== false && this.config.enableCache) {
        this.setCache(cacheKey, result);
      }

      return result;
    } catch (error) {
      throw this.handleError(error);
    }
  }
}

// 导出单例实例
export const apiClient = new ApiClient();
```

## API网关接口

### 基础路由配置

**基础URL**: `http://localhost:3000/api`

### 1. 认证接口 (/auth)

#### 1.1 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "usernameOrEmail": "johndoe",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "uuid-string",
      "username": "johndoe",
      "email": "<EMAIL>",
      "role": "user",
      "avatar": "https://example.com/avatar.jpg"
    }
  },
  "message": "登录成功"
}
```

#### 1.2 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "password123",
  "displayName": "John Doe"
}
```

#### 1.3 获取用户信息
```http
GET /api/auth/profile
Authorization: Bearer {token}
```

### 2. 项目管理接口 (/projects)

#### 2.1 创建项目
```http
POST /api/projects
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "我的项目",
  "description": "项目描述",
  "type": "scene",
  "settings": {
    "renderQuality": "high",
    "enablePhysics": true
  }
}
```

#### 2.2 获取项目列表
```http
GET /api/projects?page=1&limit=10&search=关键词
Authorization: Bearer {token}
```

#### 2.3 获取项目详情
```http
GET /api/projects/{projectId}
Authorization: Bearer {token}
```

#### 2.4 更新项目
```http
PATCH /api/projects/{projectId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "更新的项目名称",
  "description": "更新的描述"
}
```

#### 2.5 删除项目
```http
DELETE /api/projects/{projectId}
Authorization: Bearer {token}
```

### 3. 资产管理接口 (/assets)

#### 3.1 上传资产文件
```http
POST /api/assets/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [文件数据]
metadata: {
  "name": "资产名称",
  "type": "model",
  "category": "character",
  "tags": ["人物", "动画"]
}
```

#### 3.2 获取资产列表
```http
GET /api/assets?type=model&category=character&page=1&limit=20
Authorization: Bearer {token}
```

#### 3.3 获取资产详情
```http
GET /api/assets/{assetId}
Authorization: Bearer {token}
```

#### 3.4 下载资产文件
```http
GET /api/assets/{assetId}/download
Authorization: Bearer {token}
```

### 4. 场景管理接口 (/scenes)

#### 4.1 创建场景
```http
POST /api/projects/{projectId}/scenes
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "场景名称",
  "description": "场景描述",
  "sceneData": {
    "entities": [],
    "lighting": {},
    "camera": {},
    "environment": {}
  }
}
```

#### 4.2 保存场景
```http
PUT /api/projects/{projectId}/scenes/{sceneId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "sceneData": {
    "entities": [
      {
        "id": "entity-1",
        "type": "model",
        "position": [0, 0, 0],
        "rotation": [0, 0, 0],
        "scale": [1, 1, 1],
        "assetId": "asset-uuid"
      }
    ]
  }
}
```

### 5. 渲染接口 (/render)

#### 5.1 请求渲染
```http
POST /api/render/request
Authorization: Bearer {token}
Content-Type: application/json

{
  "sceneId": "scene-uuid",
  "renderSettings": {
    "quality": "high",
    "resolution": "1920x1080",
    "format": "mp4",
    "duration": 30
  }
}
```

#### 5.2 获取渲染状态
```http
GET /api/render/status/{taskId}
Authorization: Bearer {token}
```

## 微服务接口定义

### 1. 场景生成服务 (端口: 3004)

#### 1.1 创建生成任务
```http
POST /generation/tasks
Content-Type: application/json

{
  "name": "文本生成场景",
  "description": "根据文本描述生成3D场景",
  "type": "text_to_scene",
  "textInput": "一个现代办公室，有桌子、椅子和电脑",
  "config": {
    "style": "modern",
    "complexity": "medium"
  }
}
```

#### 1.2 获取任务状态
```http
GET /generation/tasks/{taskId}
```

**响应**:
```json
{
  "id": "task-uuid",
  "name": "文本生成场景",
  "status": "processing",
  "progress": 65,
  "totalSteps": 5,
  "currentStep": 3,
  "result": null,
  "createdAt": "2025-08-25T10:00:00Z",
  "updatedAt": "2025-08-25T10:05:00Z"
}
```

### 2. 场景模板服务 (端口: 8004)

#### 2.1 获取模板列表
```http
GET /templates?category=indoor&page=1&limit=10
```

#### 2.2 获取模板详情
```http
GET /templates/{templateId}
```

#### 2.3 创建模板
```http
POST /templates
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "现代办公室模板",
  "description": "现代风格的办公室场景模板",
  "category": "office",
  "templateData": {
    "entities": [],
    "lighting": {},
    "materials": {}
  },
  "parameters": [
    {
      "name": "roomSize",
      "type": "vector3",
      "default": [10, 3, 8],
      "min": [5, 2.5, 5],
      "max": [20, 4, 15]
    }
  ]
}
```

### 3. 资产库服务 (端口: 3005)

#### 3.1 搜索资产
```http
GET /assets/search?q=chair&type=model&format=glb
```

#### 3.2 获取资产分类
```http
GET /assets/categories
```

#### 3.3 批量下载资产
```http
POST /assets/batch-download
Content-Type: application/json

{
  "assetIds": ["asset-1", "asset-2", "asset-3"],
  "format": "zip"
}
```

### 4. AI模型服务 (端口: 3010)

#### 4.1 文本分析
```http
POST /ai/text/analyze
Content-Type: application/json

{
  "text": "创建一个温馨的客厅场景",
  "language": "zh-CN",
  "analysisType": "scene_generation"
}
```

#### 4.2 图像识别
```http
POST /ai/image/analyze
Content-Type: multipart/form-data

image: [图像文件]
options: {
  "detectObjects": true,
  "extractStyle": true,
  "generateDescription": true
}
```

### 5. 协作服务 (端口: 3007)

#### 5.1 加入协作会话
```http
POST /collaboration/sessions/{sessionId}/join
Authorization: Bearer {token}
```

#### 5.2 发送协作操作
```http
POST /collaboration/sessions/{sessionId}/operations
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "entity_transform",
  "entityId": "entity-uuid",
  "operation": {
    "position": [1, 0, 1],
    "rotation": [0, 45, 0]
  },
  "timestamp": 1692950400000
}
```

## 前端API调用示例

### 1. 认证服务调用

```typescript
// editor/src/services/AuthService.ts
class AuthService extends EventEmitter {
  // 登录
  async login(usernameOrEmail: string, password: string): Promise<User> {
    try {
      const response = await apiClient.request<AuthResponse>({
        method: 'POST',
        url: '/auth/login',
        data: { usernameOrEmail, password },
        skipAuth: true
      });

      if (response.success) {
        this.setAuthData(response.data.token, response.data.user);
        this.emit(AuthEventType.LOGIN_SUCCESS, response.data.user);
        return response.data.user;
      }
      
      throw new Error(response.message || '登录失败');
    } catch (error) {
      this.emit(AuthEventType.LOGIN_ERROR, error);
      throw error;
    }
  }

  // 注册
  async register(userData: RegisterData): Promise<User> {
    try {
      const response = await apiClient.request<AuthResponse>({
        method: 'POST',
        url: '/auth/register',
        data: userData,
        skipAuth: true
      });

      if (response.success) {
        this.emit(AuthEventType.REGISTER_SUCCESS, response.data.user);
        return response.data.user;
      }
      
      throw new Error(response.message || '注册失败');
    } catch (error) {
      this.emit(AuthEventType.REGISTER_ERROR, error);
      throw error;
    }
  }
}
```

### 2. 项目服务调用

```typescript
// editor/src/services/ProjectService.ts
export class ProjectService {
  // 创建项目
  async createProject(projectData: CreateProjectData): Promise<Project> {
    const response = await apiClient.request<Project>({
      method: 'POST',
      url: '/projects',
      data: projectData
    });

    if (response.success) {
      return response.data;
    }
    
    throw new Error(response.message || '创建项目失败');
  }

  // 获取项目列表
  async getProjects(params?: ProjectQueryParams): Promise<ProjectListResponse> {
    const response = await apiClient.request<ProjectListResponse>({
      method: 'GET',
      url: '/projects',
      params,
      useCache: true
    });

    return response.data;
  }

  // 保存项目
  async saveProject(projectId: string, projectData: Partial<Project>): Promise<Project> {
    const response = await apiClient.request<Project>({
      method: 'PATCH',
      url: `/projects/${projectId}`,
      data: projectData
    });

    return response.data;
  }
}
```

### 3. 资产服务调用

```typescript
// editor/src/services/AssetService.ts
export class AssetService {
  // 上传资产
  async uploadAsset(file: File, metadata: AssetMetadata): Promise<Asset> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('metadata', JSON.stringify(metadata));

    const response = await apiClient.request<Asset>({
      method: 'POST',
      url: '/assets/upload',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000 // 上传超时时间更长
    });

    return response.data;
  }

  // 搜索资产
  async searchAssets(query: AssetSearchQuery): Promise<AssetSearchResult> {
    const response = await apiClient.request<AssetSearchResult>({
      method: 'GET',
      url: '/assets/search',
      params: query,
      useCache: true
    });

    return response.data;
  }

  // 下载资产
  async downloadAsset(assetId: string): Promise<Blob> {
    const response = await apiClient.request<Blob>({
      method: 'GET',
      url: `/assets/${assetId}/download`,
      responseType: 'blob'
    });

    return response.data;
  }
}
```

### 4. 场景生成服务调用

```typescript
// editor/src/services/SceneGenerationService.ts
export class SceneGenerationService {
  // 创建生成任务
  async createGenerationTask(taskData: CreateGenerationTaskData): Promise<GenerationTask> {
    const response = await apiClient.request<GenerationTask>({
      method: 'POST',
      url: '/scene-generation/tasks',
      data: taskData
    });

    return response.data;
  }

  // 获取任务状态
  async getTaskStatus(taskId: string): Promise<GenerationTask> {
    const response = await apiClient.request<GenerationTask>({
      method: 'GET',
      url: `/scene-generation/tasks/${taskId}`,
      useCache: false // 状态查询不使用缓存
    });

    return response.data;
  }

  // 轮询任务状态
  async pollTaskStatus(taskId: string, onProgress?: (task: GenerationTask) => void): Promise<GenerationTask> {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const task = await this.getTaskStatus(taskId);
          
          if (onProgress) {
            onProgress(task);
          }

          if (task.status === 'completed') {
            resolve(task);
          } else if (task.status === 'failed') {
            reject(new Error(task.errorMessage || '生成任务失败'));
          } else {
            // 继续轮询
            setTimeout(poll, 2000);
          }
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }
}
```

### 5. WebSocket连接示例

```typescript
// editor/src/services/WebSocketService.ts
export class WebSocketService extends EventEmitter {
  private ws: WebSocket | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(token: string) {
    const wsUrl = `${environment.WS_BASE_URL}?token=${token}`;
    
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('WebSocket连接已建立');
      this.reconnectAttempts = 0;
      this.emit('connected');
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };

    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭');
      this.emit('disconnected');
      this.attemptReconnect(token);
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.emit('error', error);
    };
  }

  private handleMessage(message: any) {
    switch (message.type) {
      case 'generation_progress':
        this.emit('generationProgress', message.data);
        break;
      case 'collaboration_update':
        this.emit('collaborationUpdate', message.data);
        break;
      case 'render_status':
        this.emit('renderStatus', message.data);
        break;
      default:
        this.emit('message', message);
    }
  }

  private attemptReconnect(token: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      this.reconnectTimer = setTimeout(() => {
        console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect(token);
      }, environment.WS_RECONNECT_INTERVAL);
    }
  }

  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  disconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
```

## 数据模型定义

### 1. 用户模型
```typescript
export interface User {
  id: string;
  username: string;
  email: string;
  displayName?: string;
  avatar?: string;
  role: 'admin' | 'user' | 'guest';
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  language: string;
  theme: 'light' | 'dark';
  autoSave: boolean;
  renderQuality: 'low' | 'medium' | 'high';
}
```

### 2. 项目模型
```typescript
export interface Project {
  id: string;
  name: string;
  description?: string;
  type: 'scene' | 'animation' | 'character';
  ownerId: string;
  collaborators: string[];
  settings: ProjectSettings;
  scenes: Scene[];
  assets: Asset[];
  createdAt: string;
  updatedAt: string;
}

export interface ProjectSettings {
  renderQuality: 'low' | 'medium' | 'high';
  enablePhysics: boolean;
  enableLighting: boolean;
  enableShadows: boolean;
  targetFrameRate: number;
}
```

### 3. 场景模型
```typescript
export interface Scene {
  id: string;
  name: string;
  description?: string;
  projectId: string;
  sceneData: SceneData;
  thumbnail?: string;
  version: number;
  createdAt: string;
  updatedAt: string;
}

export interface SceneData {
  entities: Entity[];
  lighting: LightingConfig;
  camera: CameraConfig;
  environment: EnvironmentConfig;
  physics: PhysicsConfig;
}

export interface Entity {
  id: string;
  name: string;
  type: 'model' | 'light' | 'camera' | 'particle' | 'audio';
  transform: Transform;
  components: Component[];
  assetId?: string;
  visible: boolean;
  locked: boolean;
}

export interface Transform {
  position: [number, number, number];
  rotation: [number, number, number];
  scale: [number, number, number];
}
```

### 4. 资产模型
```typescript
export interface Asset {
  id: string;
  name: string;
  description?: string;
  type: 'model' | 'texture' | 'material' | 'audio' | 'animation';
  category: string;
  tags: string[];
  fileUrl: string;
  thumbnailUrl?: string;
  metadata: AssetMetadata;
  size: number;
  format: string;
  ownerId: string;
  isPublic: boolean;
  downloadCount: number;
  rating: number;
  createdAt: string;
  updatedAt: string;
}

export interface AssetMetadata {
  dimensions?: [number, number, number];
  polygonCount?: number;
  textureResolution?: [number, number];
  duration?: number; // 音频/动画时长
  frameRate?: number; // 动画帧率
  [key: string]: any;
}
```

### 5. 生成任务模型
```typescript
export interface GenerationTask {
  id: string;
  name: string;
  description?: string;
  type: 'text_to_scene' | 'voice_to_scene' | 'image_to_scene' | 'template_based';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number; // 0-100
  totalSteps: number;
  currentStep: number;
  userId: string;
  inputData: any;
  config: GenerationConfig;
  result?: GenerationResult;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface GenerationConfig {
  style?: string;
  complexity?: 'low' | 'medium' | 'high';
  quality?: 'draft' | 'standard' | 'high';
  includePhysics?: boolean;
  includeLighting?: boolean;
  [key: string]: any;
}

export interface GenerationResult {
  sceneData: SceneData;
  assets: Asset[];
  metadata: {
    generationTime: number;
    aiModel: string;
    confidence: number;
  };
}
```

## 错误处理规范

### 1. 错误响应格式
```typescript
export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    path: string;
  };
}
```

### 2. 错误代码定义
```typescript
export enum ErrorCode {
  // 认证错误 (1000-1099)
  UNAUTHORIZED = 'AUTH_001',
  INVALID_TOKEN = 'AUTH_002',
  TOKEN_EXPIRED = 'AUTH_003',
  INSUFFICIENT_PERMISSIONS = 'AUTH_004',

  // 验证错误 (1100-1199)
  VALIDATION_ERROR = 'VAL_001',
  INVALID_INPUT = 'VAL_002',
  MISSING_REQUIRED_FIELD = 'VAL_003',

  // 资源错误 (1200-1299)
  RESOURCE_NOT_FOUND = 'RES_001',
  RESOURCE_ALREADY_EXISTS = 'RES_002',
  RESOURCE_LOCKED = 'RES_003',

  // 文件错误 (1300-1399)
  FILE_TOO_LARGE = 'FILE_001',
  INVALID_FILE_TYPE = 'FILE_002',
  UPLOAD_FAILED = 'FILE_003',

  // 服务错误 (1400-1499)
  SERVICE_UNAVAILABLE = 'SVC_001',
  EXTERNAL_SERVICE_ERROR = 'SVC_002',
  RATE_LIMIT_EXCEEDED = 'SVC_003',

  // 系统错误 (1500-1599)
  INTERNAL_SERVER_ERROR = 'SYS_001',
  DATABASE_ERROR = 'SYS_002',
  NETWORK_ERROR = 'SYS_003'
}
```

### 3. 前端错误处理
```typescript
// editor/src/utils/errorHandler.ts
export class ErrorHandler {
  static handle(error: any): void {
    if (error.response) {
      // API错误响应
      const errorResponse: ErrorResponse = error.response.data;
      this.handleApiError(errorResponse);
    } else if (error.request) {
      // 网络错误
      this.handleNetworkError(error);
    } else {
      // 其他错误
      this.handleGenericError(error);
    }
  }

  private static handleApiError(errorResponse: ErrorResponse): void {
    const { error } = errorResponse;
    
    switch (error.code) {
      case ErrorCode.UNAUTHORIZED:
        // 跳转到登录页
        authService.logout();
        break;
      case ErrorCode.VALIDATION_ERROR:
        // 显示验证错误
        notification.error({
          message: '输入验证失败',
          description: error.message
        });
        break;
      case ErrorCode.RESOURCE_NOT_FOUND:
        // 显示资源未找到错误
        notification.error({
          message: '资源未找到',
          description: error.message
        });
        break;
      default:
        // 显示通用错误
        notification.error({
          message: '操作失败',
          description: error.message
        });
    }
  }

  private static handleNetworkError(error: any): void {
    notification.error({
      message: '网络连接失败',
      description: '请检查网络连接后重试'
    });
  }

  private static handleGenericError(error: any): void {
    console.error('未知错误:', error);
    notification.error({
      message: '系统错误',
      description: '发生了未知错误，请稍后重试'
    });
  }
}
```

## 认证授权机制

### 1. JWT令牌结构
```typescript
export interface JWTPayload {
  sub: string; // 用户ID
  username: string;
  email: string;
  role: string;
  permissions: string[];
  iat: number; // 签发时间
  exp: number; // 过期时间
}
```

### 2. 权限定义
```typescript
export enum Permission {
  // 项目权限
  PROJECT_CREATE = 'project:create',
  PROJECT_READ = 'project:read',
  PROJECT_UPDATE = 'project:update',
  PROJECT_DELETE = 'project:delete',

  // 资产权限
  ASSET_UPLOAD = 'asset:upload',
  ASSET_DOWNLOAD = 'asset:download',
  ASSET_DELETE = 'asset:delete',

  // 场景权限
  SCENE_CREATE = 'scene:create',
  SCENE_EDIT = 'scene:edit',
  SCENE_RENDER = 'scene:render',

  // 协作权限
  COLLABORATION_JOIN = 'collaboration:join',
  COLLABORATION_INVITE = 'collaboration:invite',

  // 管理权限
  ADMIN_USER_MANAGE = 'admin:user:manage',
  ADMIN_SYSTEM_CONFIG = 'admin:system:config'
}
```

### 3. 前端权限检查
```typescript
// editor/src/services/PermissionService.ts
export class PermissionService {
  private static permissions: Set<string> = new Set();

  static setPermissions(permissions: string[]): void {
    this.permissions = new Set(permissions);
  }

  static hasPermission(permission: Permission): boolean {
    return this.permissions.has(permission);
  }

  static hasAnyPermission(permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(permission));
  }

  static hasAllPermissions(permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(permission));
  }

  // React Hook
  static usePermission(permission: Permission): boolean {
    const [hasPermission, setHasPermission] = useState(false);

    useEffect(() => {
      setHasPermission(this.hasPermission(permission));
    }, [permission]);

    return hasPermission;
  }
}

// 权限组件
export const PermissionGuard: React.FC<{
  permission: Permission;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}> = ({ permission, fallback, children }) => {
  const hasPermission = PermissionService.usePermission(permission);

  if (!hasPermission) {
    return fallback || null;
  }

  return <>{children}</>;
};
```

## WebSocket事件定义

### 1. 连接事件
```typescript
// 连接建立
{
  "type": "connection_established",
  "data": {
    "sessionId": "session-uuid",
    "userId": "user-uuid",
    "timestamp": 1692950400000
  }
}

// 用户加入协作
{
  "type": "user_joined",
  "data": {
    "userId": "user-uuid",
    "username": "johndoe",
    "avatar": "https://example.com/avatar.jpg"
  }
}

// 用户离开协作
{
  "type": "user_left",
  "data": {
    "userId": "user-uuid"
  }
}
```

### 2. 场景生成事件
```typescript
// 生成进度更新
{
  "type": "generation_progress",
  "data": {
    "taskId": "task-uuid",
    "progress": 65,
    "currentStep": 3,
    "totalSteps": 5,
    "stepName": "资产选择",
    "estimatedTimeRemaining": 30000
  }
}

// 生成完成
{
  "type": "generation_completed",
  "data": {
    "taskId": "task-uuid",
    "result": {
      "sceneData": {},
      "assets": [],
      "metadata": {}
    }
  }
}

// 生成失败
{
  "type": "generation_failed",
  "data": {
    "taskId": "task-uuid",
    "error": "生成过程中发生错误",
    "details": {}
  }
}
```

### 3. 协作事件
```typescript
// 实体变换
{
  "type": "entity_transform",
  "data": {
    "entityId": "entity-uuid",
    "transform": {
      "position": [1, 0, 1],
      "rotation": [0, 45, 0],
      "scale": [1, 1, 1]
    },
    "userId": "user-uuid",
    "timestamp": 1692950400000
  }
}

// 实体创建
{
  "type": "entity_created",
  "data": {
    "entity": {
      "id": "entity-uuid",
      "name": "新实体",
      "type": "model",
      "transform": {},
      "components": []
    },
    "userId": "user-uuid"
  }
}

// 实体删除
{
  "type": "entity_deleted",
  "data": {
    "entityId": "entity-uuid",
    "userId": "user-uuid"
  }
}

// 场景保存
{
  "type": "scene_saved",
  "data": {
    "sceneId": "scene-uuid",
    "version": 2,
    "userId": "user-uuid",
    "timestamp": 1692950400000
  }
}
```

### 4. 渲染事件
```typescript
// 渲染开始
{
  "type": "render_started",
  "data": {
    "taskId": "render-task-uuid",
    "sceneId": "scene-uuid",
    "settings": {
      "quality": "high",
      "resolution": "1920x1080"
    }
  }
}

// 渲染进度
{
  "type": "render_progress",
  "data": {
    "taskId": "render-task-uuid",
    "progress": 45,
    "currentFrame": 450,
    "totalFrames": 1000,
    "estimatedTimeRemaining": 120000
  }
}

// 渲染完成
{
  "type": "render_completed",
  "data": {
    "taskId": "render-task-uuid",
    "outputUrl": "https://cdn.example.com/renders/video.mp4",
    "thumbnailUrl": "https://cdn.example.com/renders/thumbnail.jpg",
    "duration": 30,
    "fileSize": 52428800
  }
}
```

## 状态码定义

### HTTP状态码
```typescript
export enum HttpStatus {
  // 成功响应
  OK = 200,
  CREATED = 201,
  ACCEPTED = 202,
  NO_CONTENT = 204,

  // 重定向
  MOVED_PERMANENTLY = 301,
  FOUND = 302,
  NOT_MODIFIED = 304,

  // 客户端错误
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,

  // 服务器错误
  INTERNAL_SERVER_ERROR = 500,
  NOT_IMPLEMENTED = 501,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504
}
```

### 业务状态码
```typescript
export enum BusinessStatus {
  // 任务状态
  TASK_PENDING = 'pending',
  TASK_PROCESSING = 'processing',
  TASK_COMPLETED = 'completed',
  TASK_FAILED = 'failed',
  TASK_CANCELLED = 'cancelled',

  // 项目状态
  PROJECT_DRAFT = 'draft',
  PROJECT_ACTIVE = 'active',
  PROJECT_ARCHIVED = 'archived',
  PROJECT_DELETED = 'deleted',

  // 资产状态
  ASSET_UPLOADING = 'uploading',
  ASSET_PROCESSING = 'processing',
  ASSET_AVAILABLE = 'available',
  ASSET_UNAVAILABLE = 'unavailable',

  // 渲染状态
  RENDER_QUEUED = 'queued',
  RENDER_RENDERING = 'rendering',
  RENDER_COMPLETED = 'completed',
  RENDER_FAILED = 'failed'
}
```

## 分页和排序规范

### 1. 分页参数
```typescript
export interface PaginationParams {
  page?: number;        // 页码，从1开始
  limit?: number;       // 每页数量，默认20，最大100
  offset?: number;      // 偏移量，可选
}

export interface SortParams {
  sortBy?: string;      // 排序字段
  sortOrder?: 'asc' | 'desc'; // 排序方向
}

export interface FilterParams {
  search?: string;      // 搜索关键词
  category?: string;    // 分类筛选
  type?: string;        // 类型筛选
  status?: string;      // 状态筛选
  dateFrom?: string;    // 开始日期
  dateTo?: string;      // 结束日期
}
```

### 2. 分页响应格式
```typescript
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters?: FilterParams;
  sort?: SortParams;
}
```

### 3. 使用示例
```http
GET /api/projects?page=2&limit=10&sortBy=createdAt&sortOrder=desc&search=场景&category=indoor
```

## 文件上传规范

### 1. 支持的文件类型
```typescript
export const SUPPORTED_FILE_TYPES = {
  models: ['.glb', '.gltf', '.fbx', '.obj', '.dae', '.3ds'],
  textures: ['.png', '.jpg', '.jpeg', '.webp', '.tga', '.exr', '.hdr'],
  audio: ['.mp3', '.wav', '.ogg', '.m4a'],
  video: ['.mp4', '.webm', '.mov', '.avi'],
  documents: ['.pdf', '.doc', '.docx', '.txt'],
  archives: ['.zip', '.rar', '.7z', '.tar.gz']
};

export const FILE_SIZE_LIMITS = {
  models: 500 * 1024 * 1024,      // 500MB
  textures: 50 * 1024 * 1024,     // 50MB
  audio: 100 * 1024 * 1024,       // 100MB
  video: 1024 * 1024 * 1024,      // 1GB
  documents: 10 * 1024 * 1024,    // 10MB
  archives: 1024 * 1024 * 1024    // 1GB
};
```

### 2. 上传流程
```typescript
// 1. 预检查文件
POST /api/assets/upload/check
{
  "fileName": "model.glb",
  "fileSize": 52428800,
  "fileType": "model/gltf-binary",
  "checksum": "sha256-hash"
}

// 2. 获取上传URL（大文件分片上传）
POST /api/assets/upload/initiate
{
  "fileName": "model.glb",
  "fileSize": 52428800,
  "chunkSize": 5242880,
  "metadata": {
    "name": "角色模型",
    "category": "character"
  }
}

// 3. 上传文件块
PUT /api/assets/upload/chunk/{uploadId}/{chunkIndex}
Content-Type: application/octet-stream
Content-Length: 5242880

[文件块数据]

// 4. 完成上传
POST /api/assets/upload/complete/{uploadId}
{
  "chunks": [
    {"index": 0, "etag": "etag1"},
    {"index": 1, "etag": "etag2"}
  ]
}
```

### 3. 前端上传组件
```typescript
// editor/src/components/FileUpload.tsx
export const FileUpload: React.FC<FileUploadProps> = ({
  accept,
  maxSize,
  onUploadProgress,
  onUploadComplete,
  onUploadError
}) => {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSelect = async (file: File) => {
    if (!validateFile(file, accept, maxSize)) {
      return;
    }

    setIsUploading(true);

    try {
      // 检查文件
      await assetService.checkFile(file);

      // 开始上传
      const result = await assetService.uploadFile(file, {
        onProgress: (progress) => {
          setUploadProgress(progress);
          onUploadProgress?.(progress);
        }
      });

      onUploadComplete?.(result);
    } catch (error) {
      onUploadError?.(error);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <Upload.Dragger
      accept={accept.join(',')}
      beforeUpload={handleFileSelect}
      showUploadList={false}
      disabled={isUploading}
    >
      {isUploading ? (
        <div>
          <Progress percent={uploadProgress} />
          <p>上传中...</p>
        </div>
      ) : (
        <div>
          <InboxOutlined style={{ fontSize: 48 }} />
          <p>点击或拖拽文件到此区域上传</p>
        </div>
      )}
    </Upload.Dragger>
  );
};
```

## 缓存策略

### 1. 前端缓存配置
```typescript
export interface CacheConfig {
  // 内存缓存
  memory: {
    maxSize: number;        // 最大缓存大小（字节）
    ttl: number;           // 生存时间（毫秒）
    maxItems: number;      // 最大缓存项数
  };

  // 本地存储缓存
  localStorage: {
    prefix: string;        // 键前缀
    ttl: number;          // 生存时间
    maxSize: number;      // 最大存储大小
  };

  // IndexedDB缓存
  indexedDB: {
    dbName: string;       // 数据库名
    version: number;      // 版本号
    stores: string[];     // 存储对象名
  };
}

export const cacheConfig: CacheConfig = {
  memory: {
    maxSize: 100 * 1024 * 1024, // 100MB
    ttl: 5 * 60 * 1000,         // 5分钟
    maxItems: 1000
  },
  localStorage: {
    prefix: 'digital_human_',
    ttl: 24 * 60 * 60 * 1000,   // 24小时
    maxSize: 10 * 1024 * 1024   // 10MB
  },
  indexedDB: {
    dbName: 'DigitalHumanDB',
    version: 1,
    stores: ['assets', 'projects', 'scenes', 'cache']
  }
};
```

### 2. 缓存策略定义
```typescript
export enum CacheStrategy {
  // 缓存优先
  CACHE_FIRST = 'cache_first',

  // 网络优先
  NETWORK_FIRST = 'network_first',

  // 仅缓存
  CACHE_ONLY = 'cache_only',

  // 仅网络
  NETWORK_ONLY = 'network_only',

  // 最快响应
  FASTEST = 'fastest'
}

// API缓存配置
export const API_CACHE_CONFIG = {
  '/auth/profile': {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 30 * 60 * 1000 // 30分钟
  },
  '/projects': {
    strategy: CacheStrategy.NETWORK_FIRST,
    ttl: 5 * 60 * 1000 // 5分钟
  },
  '/assets/search': {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 10 * 60 * 1000 // 10分钟
  },
  '/templates': {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 60 * 60 * 1000 // 1小时
  }
};
```

## 性能优化建议

### 1. 前端优化
```typescript
// 1. 组件懒加载
const SceneEditor = lazy(() => import('./components/SceneEditor'));
const AssetLibrary = lazy(() => import('./components/AssetLibrary'));

// 2. 虚拟滚动
import { FixedSizeList as List } from 'react-window';

const AssetList: React.FC = ({ assets }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      <AssetItem asset={assets[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={assets.length}
      itemSize={120}
      width="100%"
    >
      {Row}
    </List>
  );
};

// 3. 防抖和节流
import { debounce, throttle } from 'lodash';

const SearchInput: React.FC = () => {
  const debouncedSearch = useMemo(
    () => debounce((query: string) => {
      // 执行搜索
      searchAssets(query);
    }, 300),
    []
  );

  return (
    <Input
      placeholder="搜索资产..."
      onChange={(e) => debouncedSearch(e.target.value)}
    />
  );
};

// 4. 图片懒加载
const LazyImage: React.FC<{ src: string; alt: string }> = ({ src, alt }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div ref={imgRef}>
      {isInView && (
        <img
          src={src}
          alt={alt}
          onLoad={() => setIsLoaded(true)}
          style={{ opacity: isLoaded ? 1 : 0 }}
        />
      )}
    </div>
  );
};
```

### 2. 后端优化
```typescript
// 1. 数据库查询优化
@Entity()
export class Asset {
  @Index()
  @Column()
  category: string;

  @Index()
  @Column()
  type: string;

  @Index()
  @Column()
  createdAt: Date;
}

// 2. 缓存装饰器
export function Cacheable(ttl: number = 300) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`;

      // 检查缓存
      const cached = await this.cacheService.get(cacheKey);
      if (cached) {
        return cached;
      }

      // 执行方法
      const result = await method.apply(this, args);

      // 缓存结果
      await this.cacheService.set(cacheKey, result, ttl);

      return result;
    };
  };
}

// 3. 分页查询优化
@Injectable()
export class AssetService {
  @Cacheable(600) // 10分钟缓存
  async findAssets(query: AssetSearchQuery): Promise<PaginatedResponse<Asset>> {
    const queryBuilder = this.assetRepository
      .createQueryBuilder('asset')
      .leftJoinAndSelect('asset.owner', 'owner')
      .where('asset.isPublic = :isPublic', { isPublic: true });

    // 添加搜索条件
    if (query.search) {
      queryBuilder.andWhere(
        '(asset.name ILIKE :search OR asset.description ILIKE :search)',
        { search: `%${query.search}%` }
      );
    }

    // 添加分类筛选
    if (query.category) {
      queryBuilder.andWhere('asset.category = :category', { category: query.category });
    }

    // 添加排序
    const sortBy = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder || 'DESC';
    queryBuilder.orderBy(`asset.${sortBy}`, sortOrder);

    // 分页
    const page = query.page || 1;
    const limit = Math.min(query.limit || 20, 100);
    const offset = (page - 1) * limit;

    const [assets, total] = await queryBuilder
      .skip(offset)
      .take(limit)
      .getManyAndCount();

    return {
      data: assets,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }
}
```

## 监控和日志

### 1. 前端监控
```typescript
// 性能监控
export class PerformanceMonitor {
  static trackPageLoad(pageName: string) {
    const startTime = performance.now();

    window.addEventListener('load', () => {
      const loadTime = performance.now() - startTime;
      this.sendMetric('page_load_time', loadTime, { page: pageName });
    });
  }

  static trackApiCall(url: string, method: string, duration: number, status: number) {
    this.sendMetric('api_call_duration', duration, {
      url,
      method,
      status: status.toString()
    });
  }

  static trackUserAction(action: string, metadata?: any) {
    this.sendMetric('user_action', 1, {
      action,
      ...metadata,
      timestamp: Date.now()
    });
  }

  private static sendMetric(name: string, value: number, tags: any) {
    // 发送到监控服务
    fetch('/api/metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name, value, tags, timestamp: Date.now() })
    }).catch(console.error);
  }
}

// 错误监控
window.addEventListener('error', (event) => {
  const errorInfo = {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    stack: event.error?.stack,
    userAgent: navigator.userAgent,
    url: window.location.href,
    timestamp: Date.now()
  };

  fetch('/api/errors', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(errorInfo)
  }).catch(console.error);
});
```

### 2. 后端日志
```typescript
// 日志配置
export const loggerConfig = {
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
};

// 请求日志中间件
export function requestLogger(req: Request, res: Response, next: NextFunction) {
  const startTime = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logData = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id
    };

    if (res.statusCode >= 400) {
      logger.error('HTTP Request Error', logData);
    } else {
      logger.info('HTTP Request', logData);
    }
  });

  next();
}
```

---

**文档结束**

本文档定义了数字人RAG交互系统的完整前后端API接口规范，包括：

1. **系统架构概述** - 微服务架构和技术栈
2. **前端服务配置** - 环境配置和API客户端设置
3. **API网关接口** - 统一的API入口和路由定义
4. **微服务接口定义** - 各个微服务的具体API接口
5. **前端API调用示例** - 完整的前端服务调用代码
6. **数据模型定义** - 系统中使用的数据结构
7. **错误处理规范** - 统一的错误处理机制
8. **认证授权机制** - JWT令牌和权限管理
9. **WebSocket事件定义** - 实时通信事件规范
10. **状态码定义** - HTTP和业务状态码
11. **分页和排序规范** - 数据查询规范
12. **文件上传规范** - 文件处理流程
13. **缓存策略** - 前后端缓存优化
14. **性能优化建议** - 系统性能优化方案
15. **监控和日志** - 系统监控和日志记录

开发团队可以基于此文档进行前后端开发、集成测试和系统部署工作。文档涵盖了从基础配置到高级优化的完整技术规范，确保系统的可维护性、可扩展性和高性能。
