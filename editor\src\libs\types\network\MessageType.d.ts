/**
 * 消息类型
 * 定义网络消息的类型常量
 */
export declare class MessageType {
    /** 系统消息 */
    static readonly SYSTEM = "system";
    /** 心跳消息 */
    static readonly HEARTBEAT = "heartbeat";
    /** 确认消息 */
    static readonly ACK = "ack";
    /** 错误消息 */
    static readonly ERROR = "error";
    /** 连接请求 */
    static readonly CONNECT = "connect";
    /** 连接成功 */
    static readonly CONNECT_SUCCESS = "connect_success";
    /** 连接失败 */
    static readonly CONNECT_FAILED = "connect_failed";
    /** 断开连接 */
    static readonly DISCONNECT = "disconnect";
    /** 加入房间 */
    static readonly JOIN_ROOM = "join_room";
    /** 加入房间成功 */
    static readonly JOIN_ROOM_SUCCESS = "join_room_success";
    /** 加入房间失败 */
    static readonly JOIN_ROOM_FAILED = "join_room_failed";
    /** 离开房间 */
    static readonly LEAVE_ROOM = "leave_room";
    /** 用户加入 */
    static readonly USER_JOINED = "user_joined";
    /** 用户离开 */
    static readonly USER_LEFT = "user_left";
    /** 房间列表 */
    static readonly ROOM_LIST = "room_list";
    /** 房间信息 */
    static readonly ROOM_INFO = "room_info";
    /** 用户列表 */
    static readonly USER_LIST = "user_list";
    /** 用户信息 */
    static readonly USER_INFO = "user_info";
    /** 用户状态 */
    static readonly USER_STATUS = "user_status";
    /** 聊天消息 */
    static readonly CHAT = "chat";
    /** 私聊消息 */
    static readonly PRIVATE_CHAT = "private_chat";
    /** 广播消息 */
    static readonly BROADCAST = "broadcast";
    /** 直接消息 */
    static readonly DIRECT = "direct";
    /** 创建实体 */
    static readonly ENTITY_CREATE = "entity_create";
    /** 更新实体 */
    static readonly ENTITY_UPDATE = "entity_update";
    /** 删除实体 */
    static readonly ENTITY_DELETE = "entity_delete";
    /** 实体列表 */
    static readonly ENTITY_LIST = "entity_list";
    /** 实体事件 */
    static readonly ENTITY_EVENT = "entity_event";
    /** 实体同步 */
    static readonly ENTITY_SYNC = "entity_sync";
    /** 实体所有权 */
    static readonly ENTITY_OWNERSHIP = "entity_ownership";
    /** 实体所有权请求 */
    static readonly ENTITY_OWNERSHIP_REQUEST = "entity_ownership_request";
    /** 实体所有权转移 */
    static readonly ENTITY_OWNERSHIP_TRANSFER = "entity_ownership_transfer";
    /** WebRTC提议 */
    static readonly WEBRTC_OFFER = "webrtc_offer";
    /** WebRTC应答 */
    static readonly WEBRTC_ANSWER = "webrtc_answer";
    /** WebRTC ICE候选 */
    static readonly WEBRTC_ICE_CANDIDATE = "webrtc_ice_candidate";
    /** WebRTC连接状态 */
    static readonly WEBRTC_CONNECTION_STATE = "webrtc_connection_state";
    /** 媒体流 */
    static readonly MEDIA_STREAM = "media_stream";
    /** 音频流 */
    static readonly AUDIO_STREAM = "audio_stream";
    /** 视频流 */
    static readonly VIDEO_STREAM = "video_stream";
    /** 屏幕共享流 */
    static readonly SCREEN_SHARE_STREAM = "screen_share_stream";
    /** 媒体控制 */
    static readonly MEDIA_CONTROL = "media_control";
    /** 游戏状态 */
    static readonly GAME_STATE = "game_state";
    /** 游戏事件 */
    static readonly GAME_EVENT = "game_event";
    /** 游戏命令 */
    static readonly GAME_COMMAND = "game_command";
    /** 游戏同步 */
    static readonly GAME_SYNC = "game_sync";
    /** 自定义消息 */
    static readonly CUSTOM = "custom";
}
