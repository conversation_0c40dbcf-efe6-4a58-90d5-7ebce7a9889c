import { SoftBodyComponent } from '../SoftBodyComponent';
import { PhysicsBodyComponent } from '../../components/PhysicsBodyComponent';
/**
 * 软体与刚体交互系统选项
 */
export interface SoftRigidInteractionOptions {
    /** 是否启用交互 */
    enabled?: boolean;
    /** 碰撞检测半径 */
    collisionRadius?: number;
    /** 碰撞响应强度 */
    collisionResponse?: number;
    /** 摩擦系数 */
    friction?: number;
    /** 是否使用空间分区 */
    useSpatialPartitioning?: boolean;
}
/**
 * 软体与刚体交互系统
 * 处理软体与刚体之间的碰撞和交互
 */
export declare class SoftRigidInteraction {
    /** 是否启用交互 */
    private enabled;
    /** 碰撞检测半径 */
    private collisionRadius;
    /** 碰撞响应强度 */
    private collisionResponse;
    /** 摩擦系数 */
    private friction;
    /** 是否使用空间分区 */
    private useSpatialPartitioning;
    /** 空间分区系统 */
    private spatialPartitioning;
    /** 软体组件列表 */
    private softBodies;
    /** 刚体组件列表 */
    private rigidBodies;
    /** 碰撞对列表 */
    private collisionPairs;
    /**
     * 创建软体与刚体交互系统
     * @param options 软体与刚体交互系统选项
     */
    constructor(options?: SoftRigidInteractionOptions);
    /**
     * 添加软体组件
     * @param softBody 软体组件
     */
    addSoftBody(softBody: SoftBodyComponent): void;
    /**
     * 移除软体组件
     * @param softBody 软体组件
     */
    removeSoftBody(softBody: SoftBodyComponent): void;
    /**
     * 添加刚体组件
     * @param rigidBody 刚体组件
     */
    addRigidBody(rigidBody: PhysicsBodyComponent): void;
    /**
     * 移除刚体组件
     * @param rigidBody 刚体组件
     */
    removeRigidBody(rigidBody: PhysicsBodyComponent): void;
    /**
     * 更新交互
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新空间分区
     */
    private updateSpatialPartitioning;
    /**
     * 检测碰撞
     */
    private detectCollisions;
    /**
     * 检测粒子与刚体的碰撞
     * @param particle 粒子
     * @param rigidBody 刚体
     */
    private checkParticleRigidBodyCollision;
    /**
     * 解决碰撞
     * @param deltaTime 帧间隔时间（秒）
     */
    private resolveCollisions;
    /**
     * 启用交互
     */
    enable(): void;
    /**
     * 禁用交互
     */
    disable(): void;
    /**
     * 设置碰撞检测半径
     * @param radius 碰撞检测半径
     */
    setCollisionRadius(radius: number): void;
    /**
     * 设置碰撞响应强度
     * @param response 碰撞响应强度
     */
    setCollisionResponse(response: number): void;
    /**
     * 设置摩擦系数
     * @param friction 摩擦系数
     */
    setFriction(friction: number): void;
}
