/**
 * 视觉脚本引擎
 * 负责执行视觉脚本
 */
import type { Entity } from '../core/Entity';
import type { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { GraphJSON } from './graph/GraphJSON';
import { NodeRegistry } from './nodes/NodeRegistry';
import { ValueTypeRegistry } from './values/ValueTypeRegistry';
import { Node } from './nodes/Node';
import { Fiber } from './execution/Fiber';
import { ExecutionContext } from './execution/ExecutionContext';
import { Graph } from './graph/Graph';
import { Variable } from './values/Variable';
import { CustomEvent } from './events/CustomEvent';
/**
 * 视觉脚本引擎配置
 */
export interface VisualScriptEngineOptions {
    /** 视觉脚本JSON数据 */
    script: GraphJSON;
    /** 节点注册表 */
    nodeRegistry: NodeRegistry;
    /** 值类型注册表 */
    valueTypeRegistry: ValueTypeRegistry;
    /** 所属实体 */
    entity: Entity;
    /** 所属世界 */
    world: World;
}
/**
 * 视觉脚本引擎
 * 负责执行视觉脚本
 */
export declare class VisualScriptEngine extends EventEmitter {
    /** 视觉脚本JSON数据 */
    private script;
    /** 节点注册表 */
    private nodeRegistry;
    /** 值类型注册表 */
    private valueTypeRegistry;
    /** 所属实体 */
    private entity;
    /** 所属世界 */
    private world;
    /** 图形实例 */
    private graph;
    /** 节点实例映射 */
    private nodes;
    /** 事件节点列表 */
    private eventNodes;
    /** 执行上下文 */
    private executionContext;
    /** 执行纤程队列 */
    private fiberQueue;
    /** 变量映射 */
    private variables;
    /** 自定义事件映射 */
    private customEvents;
    /** 是否正在运行 */
    private running;
    /** 执行步数 */
    private executionSteps;
    /** 最大执行步数限制 */
    private maxExecutionSteps;
    /** 最大执行时间限制（毫秒） */
    private maxExecutionTime;
    /**
     * 创建视觉脚本引擎
     * @param options 引擎选项
     */
    constructor(options: VisualScriptEngineOptions);
    /**
     * 初始化引擎
     */
    private initialize;
    /**
     * 初始化变量
     */
    private initializeVariables;
    /**
     * 初始化自定义事件
     */
    private initializeCustomEvents;
    /**
     * 初始化节点
     */
    private initializeNodes;
    /**
     * 连接节点
     */
    private connectNodes;
    /**
     * 初始化事件节点
     */
    private initializeEventNodes;
    /**
     * 开始执行
     */
    start(): void;
    /**
     * 停止执行
     */
    stop(): void;
    /**
     * 更新引擎
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 执行所有纤程
     */
    private executeAllFibers;
    /**
     * 添加纤程到队列
     * @param fiber 纤程
     */
    addFiber(fiber: Fiber): void;
    /**
     * 创建新纤程
     * @param sourceNode 源节点
     * @param outputName 输出名称
     * @param callback 完成回调
     */
    createFiber(sourceNode: Node, outputName: string, callback?: () => void): Fiber;
    /**
     * 获取节点
     * @param id 节点ID
     * @returns 节点实例
     */
    getNode(id: string): Node | undefined;
    /**
     * 获取变量
     * @param id 变量ID
     * @returns 变量实例
     */
    getVariable(id: string): Variable | undefined;
    /**
     * 获取自定义事件
     * @param id 事件ID
     * @returns 自定义事件实例
     */
    getCustomEvent(id: string): CustomEvent | undefined;
    /**
     * 获取执行上下文
     * @returns 执行上下文
     */
    getExecutionContext(): ExecutionContext;
    /**
     * 获取图形实例
     * @returns 图形实例
     */
    getGraph(): Graph;
    /**
     * 获取所属实体
     * @returns 所属实体
     */
    getEntity(): Entity;
    /**
     * 获取所属世界
     * @returns 所属世界
     */
    getWorld(): World;
    /**
     * 获取是否正在运行
     * @returns 是否正在运行
     */
    isRunning(): boolean;
    /**
     * 获取执行步数
     * @returns 执行步数
     */
    getExecutionSteps(): number;
    /**
     * 销毁引擎
     */
    dispose(): void;
}
