import { AnimationGenerationRequest, AnimationGenerationResult } from './AnimationGenerationTypes';
/**
 * 情感动画生成器配置
 */
export interface EmotionBasedAnimationGeneratorConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用本地模型 */
    useLocalModel?: boolean;
    /** 模型路径 */
    modelPath?: string;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 模型类型 */
    modelType?: 'bert' | 'roberta' | 'distilbert' | 'albert' | 'xlnet' | 'custom';
    /** 模型变体 */
    modelVariant?: string;
    /** 是否使用量化模型 */
    useQuantized?: boolean;
    /** 量化位数 */
    quantizationBits?: 8 | 16 | 32;
    /** 批处理大小 */
    batchSize?: number;
    /** 情感类别 */
    emotionCategories?: string[];
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存大小 */
    cacheSize?: number;
}
/**
 * 情感分析结果
 */
export interface EmotionAnalysisResult {
    /** 主要情感 */
    primaryEmotion: string;
    /** 主要情感强度 */
    primaryIntensity: number;
    /** 情感强度（兼容性属性） */
    intensity?: number;
    /** 次要情感 */
    secondaryEmotion?: string;
    /** 次要情感强度 */
    secondaryIntensity?: number;
    /** 情感分数 */
    scores?: Record<string, number>;
    /** 情感变化 */
    emotionChanges?: {
        emotion: string;
        intensity: number;
        time: number;
    }[];
    /** 情感持续时间 */
    emotionDuration?: number;
    /** 置信度 */
    confidence?: number;
    /** 详细情感数据 */
    detailedEmotions?: Record<string, any>;
    /** 详细信息 */
    details?: Record<string, any>;
    /** 方言类型 */
    dialect?: string;
    /** 时间戳 */
    timestamp?: number;
    /** 语言 */
    language?: string;
}
/**
 * 情感动画生成器
 */
export declare class EmotionBasedAnimationGenerator {
    /** 配置 */
    private config;
    /** AI模型 */
    private aiModel;
    /** 是否已初始化 */
    private initialized;
    /** 是否启用调试 */
    private debug;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: EmotionBasedAnimationGeneratorConfig);
    /**
     * 初始化
     */
    initialize(): Promise<void>;
    /**
     * 生成面部动画
     * @param request 生成请求
     * @returns 生成结果
     */
    generateFacialAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    /**
     * 创建基于情感的面部动画片段
     * @param request 请求
     * @param emotionResult 情感分析结果
     * @returns 面部动画片段
     */
    private createEmotionBasedFacialClip;
    /**
     * 获取情感对应的表情
     * @param emotion 情感
     * @returns 表情
     */
    private getExpressionForEmotion;
    /**
     * 获取随机口型
     * @returns 口型
     */
    private getRandomViseme;
}
