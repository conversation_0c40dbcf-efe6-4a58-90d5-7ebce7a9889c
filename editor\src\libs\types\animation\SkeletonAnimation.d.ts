/**
 * 骨骼动画系统
 * 用于处理骨骼动画的加载、播放和混合
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
import type { Entity } from '../core/Entity';
import { AnimationClip } from './AnimationClip';
import { Animator } from './Animator';
/**
 * 骨骼动画选项
 */
export interface SkeletonAnimationOptions {
    /** 目标实体 */
    entity?: Entity;
    /** 骨骼模型 */
    skinnedMesh?: THREE.SkinnedMesh;
    /** 动画片段 */
    clips?: AnimationClip[];
    /** 是否自动播放 */
    autoPlay?: boolean;
    /** 默认混合时间（秒） */
    defaultBlendTime?: number;
    /** 时间缩放 */
    timeScale?: number;
}
/**
 * 骨骼动画组件
 */
export declare class SkeletonAnimation extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 动画控制器 */
    private animator;
    /** 骨骼网格 */
    private skinnedMesh;
    /** 骨骼 */
    private skeleton;
    /** 骨骼映射（名称到骨骼索引） */
    private boneMap;
    /** 原始骨骼位置 */
    private originalPositions;
    /** 原始骨骼旋转 */
    private originalRotations;
    /** 原始骨骼缩放 */
    private originalScales;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建骨骼动画组件
     * @param options 骨骼动画选项
     */
    constructor(options?: SkeletonAnimationOptions);
    /**
     * 设置目标实体
     * @param entity 实体
     */
    setEntity(entity: Entity): void;
    /**
     * 获取目标实体
     * @returns 实体
     */
    getEntity(): Entity | null;
    /**
     * 设置骨骼网格
     * @param mesh 骨骼网格
     */
    setSkinnedMesh(mesh: THREE.SkinnedMesh): void;
    /**
     * 获取骨骼网格
     * @returns 骨骼网格
     */
    getSkinnedMesh(): THREE.SkinnedMesh | null;
    /**
     * 获取骨骼
     * @returns 骨骼
     */
    getSkeleton(): THREE.Skeleton | null;
    /**
     * 添加动画片段
     * @param clip 动画片段
     */
    addClip(clip: AnimationClip): void;
    /**
     * 移除动画片段
     * @param name 动画片段名称
     * @returns 是否成功移除
     */
    removeClip(name: string): boolean;
    /**
     * 获取动画片段
     * @param name 动画片段名称
     * @returns 动画片段，如果不存在则返回null
     */
    getClip(name: string): AnimationClip | null;
    /**
     * 获取所有动画片段
     * @returns 动画片段数组
     */
    getClips(): AnimationClip[];
    /**
     * 播放动画
     * @param name 动画片段名称
     * @param blendTime 混合时间（秒），如果为0则立即切换
     * @returns 是否成功开始播放
     */
    play(name: string, blendTime?: number): boolean;
    /**
     * 停止播放
     */
    stop(): void;
    /**
     * 暂停播放
     */
    pause(): void;
    /**
     * 恢复播放
     */
    resume(): void;
    /**
     * 设置播放时间
     * @param time 时间（秒）
     */
    setTime(time: number): void;
    /**
     * 获取播放时间
     * @returns 时间（秒）
     */
    getTime(): number;
    /**
     * 设置时间缩放
     * @param timeScale 时间缩放
     */
    setTimeScale(timeScale: number): void;
    /**
     * 获取时间缩放
     * @returns 时间缩放
     */
    getTimeScale(): number;
    /**
     * 设置循环模式
     * @param loop 是否循环
     */
    setLoop(loop: boolean): void;
    /**
     * 获取循环模式
     * @returns 是否循环
     */
    getLoop(): boolean;
    /**
     * 获取骨骼索引
     * @param name 骨骼名称
     * @returns 骨骼索引，如果不存在则返回-1
     */
    getBoneIndex(name: string): number;
    /**
     * 获取骨骼
     * @param name 骨骼名称
     * @returns 骨骼，如果不存在则返回null
     */
    getBone(name: string): THREE.Bone | null;
    /**
     * 获取骨骼位置
     * @param name 骨骼名称
     * @returns 骨骼位置，如果不存在则返回null
     */
    getBonePosition(name: string): THREE.Vector3 | null;
    /**
     * 获取骨骼旋转
     * @param name 骨骼名称
     * @returns 骨骼旋转，如果不存在则返回null
     */
    getBoneRotation(name: string): THREE.Quaternion | null;
    /**
     * 获取骨骼缩放
     * @param name 骨骼名称
     * @returns 骨骼缩放，如果不存在则返回null
     */
    getBoneScale(name: string): THREE.Vector3 | null;
    /**
     * 设置骨骼位置
     * @param name 骨骼名称
     * @param position 位置
     * @returns 是否成功设置
     */
    setBonePosition(name: string, position: THREE.Vector3): boolean;
    /**
     * 设置骨骼旋转
     * @param name 骨骼名称
     * @param rotation 旋转
     * @returns 是否成功设置
     */
    setBoneRotation(name: string, rotation: THREE.Quaternion): boolean;
    /**
     * 设置骨骼缩放
     * @param name 骨骼名称
     * @param scale 缩放
     * @returns 是否成功设置
     */
    setBoneScale(name: string, scale: THREE.Vector3): boolean;
    /**
     * 更新骨骼动画
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 初始化骨骼
     */
    private initializeBones;
    /**
     * 重置骨骼到原始状态
     */
    private resetBones;
    /**
     * 在场景对象中查找骨骼网格
     * @param object 场景对象
     * @returns 骨骼网格，如果不存在则返回null
     */
    private findSkinnedMesh;
    /**
     * 从GLTF模型加载动画
     * @param gltf GLTF模型
     * @returns 加载的动画片段数量
     */
    loadFromGLTF(gltf: any): number;
    /**
     * 从FBX模型加载动画
     * @param fbx FBX模型
     * @returns 加载的动画片段数量
     */
    loadFromFBX(fbx: any): number;
    /**
     * 获取动画控制器
     * @returns 动画控制器
     */
    getAnimator(): Animator;
    /**
     * 销毁组件
     */
    dispose(): void;
}
