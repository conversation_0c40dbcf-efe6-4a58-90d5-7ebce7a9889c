/**
 * 分块场景加载系统
 * 实现大型场景的分块加载和卸载
 */
import * as THREE from 'three';
import { SceneLoadingSystem, SceneLoadingSystemOptions, ResourcePriority } from './SceneLoadingSystem';
/**
 * 场景块接口
 */
export interface SceneChunk {
    /** 块ID */
    id: string;
    /** 块名称 */
    name: string;
    /** 块包围盒 */
    boundingBox: THREE.Box3;
    /** 块中心点 */
    center: THREE.Vector3;
    /** 块大小 */
    size: THREE.Vector3;
    /** 块级别 (LOD级别) */
    level: number;
    /** 块资源列表 */
    resources: string[];
    /** 块优先级 */
    priority: ResourcePriority;
    /** 是否可见 */
    visible: boolean;
    /** 是否已加载 */
    loaded: boolean;
    /** 是否正在加载 */
    loading: boolean;
    /** 加载进度 (0-1) */
    progress: number;
    /** 子块列表 */
    children: string[];
    /** 父块ID */
    parent: string | null;
    /** 邻居块列表 */
    neighbors: string[];
    /** 用户数据 */
    userData: any;
}
/**
 * 分块场景加载系统事件类型
 */
export declare enum ChunkedSceneLoadingSystemEventType {
    /** 块加载开始 */
    CHUNK_LOAD_START = "chunk_load_start",
    /** 块加载进度 */
    CHUNK_LOAD_PROGRESS = "chunk_load_progress",
    /** 块加载完成 */
    CHUNK_LOAD_COMPLETE = "chunk_load_complete",
    /** 块加载错误 */
    CHUNK_LOAD_ERROR = "chunk_load_error",
    /** 块卸载 */
    CHUNK_UNLOAD = "chunk_unload",
    /** 块可见性变更 */
    CHUNK_VISIBILITY_CHANGED = "chunk_visibility_changed"
}
/**
 * 分块场景加载系统配置接口
 */
export interface ChunkedSceneLoadingSystemOptions extends SceneLoadingSystemOptions {
    /** 是否使用四叉树 */
    useQuadtree?: boolean;
    /** 是否使用八叉树 */
    useOctree?: boolean;
    /** 是否使用LOD */
    useLOD?: boolean;
    /** 是否使用预测加载 */
    usePredictiveLoading?: boolean;
    /** 是否使用渐进式加载 */
    useProgressiveLoading?: boolean;
    /** 是否使用流式加载 */
    useStreamingLoading?: boolean;
    /** 是否使用内存管理 */
    useMemoryManagement?: boolean;
    /** 是否使用优先级队列 */
    usePriorityQueue?: boolean;
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 是否使用预加载 */
    usePreloading?: boolean;
    /** 是否使用后台加载 */
    useBackgroundLoading?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 块大小 */
    chunkSize?: number;
    /** 最大块级别 */
    maxChunkLevel?: number;
    /** 加载距离 */
    loadDistance?: number;
    /** 卸载距离 */
    unloadDistance?: number;
    /** 预加载距离 */
    preloadDistance?: number;
    /** 最大内存使用量（MB） */
    maxMemoryUsage?: number;
    /** 最大并发加载数 */
    maxConcurrentLoads?: number;
}
/**
 * 分块场景加载系统
 * 实现大型场景的分块加载和卸载
 */
export declare class ChunkedSceneLoadingSystem extends SceneLoadingSystem {
    /** 系统类型 */
    static readonly CHUNKED_TYPE: string;
    /** 是否使用四叉树 */
    private useQuadtree;
    /** 是否使用八叉树 */
    private useOctree;
    /** 是否使用LOD */
    private useLOD;
    /** 是否使用渐进式加载 */
    private useProgressiveLoading;
    /** 是否使用流式加载 */
    private useStreamingLoading;
    /** 是否使用内存管理 */
    private useMemoryManagement;
    /** 是否使用优先级队列 */
    private usePriorityQueue;
    /** 是否使用预加载 */
    private usePreloading;
    /** 是否使用后台加载 */
    private useBackgroundLoading;
    /** 块大小 */
    private chunkSize;
    /** 最大块级别 */
    private maxChunkLevel;
    /** 加载距离 */
    private loadDistance;
    /** 场景块映射 */
    private chunks;
    /** 块计数器 */
    private chunkCounter;
    /** 四叉树 */
    private quadtree;
    /** 八叉树 */
    private octree;
    /** 相机速度 */
    private cameraVelocity;
    /** 上一帧相机位置 */
    private previousCameraPosition;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 创建分块场景加载系统
     * @param options 系统选项
     */
    constructor(options?: ChunkedSceneLoadingSystemOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化八叉树
     */
    private initializeOctree;
    /**
     * 初始化四叉树
     */
    private initializeQuadtree;
    /**
     * 初始化分块调试可视化
     */
    private initializeChunkedDebugVisualization;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新分块相机信息
     * @param camera 相机
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateChunkedCameraInfo;
    /**
     * 更新场景块
     * @param camera 相机
     */
    private updateChunks;
    /**
     * 使用八叉树更新块
     * @param camera 相机
     */
    private updateChunksWithOctree;
    /**
     * 使用暴力方法更新块
     * @param camera 相机
     */
    private updateChunksWithBruteForce;
    /**
     * 预测和预加载块
     * @param camera 相机
     */
    private predictAndPreloadChunks;
    /**
     * 加载块
     * @param chunk 块
     */
    private loadChunk;
    /**
     * 预加载块
     * @param chunk 块
     */
    private preloadChunk;
    /**
     * 卸载块
     * @param chunk 块
     */
    private unloadChunk;
    /**
     * 加载块资源
     * @param chunk 块
     */
    private loadChunkResources;
    /**
     * 管理内存
     * @param camera 相机
     */
    private manageMemory;
    /**
     * 更新分块调试可视化
     */
    private updateChunkedDebugVisualization;
    /**
     * 创建场景块
     * @param options 块选项
     * @returns 块ID
     */
    createChunk(options: {
        name?: string;
        boundingBox?: THREE.Box3;
        level?: number;
        resources?: string[];
        priority?: ResourcePriority;
        parent?: string | null;
        userData?: any;
    }): string;
    /**
     * 获取场景块
     * @param chunkId 块ID
     * @returns 块
     */
    getChunk(chunkId: string): SceneChunk | null;
    /**
     * 获取所有场景块
     * @returns 所有块
     */
    getAllChunks(): SceneChunk[];
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: string, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: string, listener: (...args: any[]) => void): void;
}
