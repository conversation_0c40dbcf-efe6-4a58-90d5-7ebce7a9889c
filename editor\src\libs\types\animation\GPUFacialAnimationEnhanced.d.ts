/**
 * 增强版GPU面部动画系统
 * 提供更高性能的面部动画计算，支持更多平台和设备
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
import { FacialExpressionType, VisemeType } from './FacialAnimation';
/**
 * 增强版GPU面部动画配置
 */
export interface EnhancedGPUFacialAnimationConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用计算着色器 */
    useComputeShader?: boolean;
    /** 最大混合形状数量 */
    maxBlendShapes?: number;
    /** 纹理大小 */
    textureSize?: number;
    /** 是否使用WebGPU（如果可用） */
    useWebGPU?: boolean;
    /** 是否使用WebGL2（如果可用） */
    useWebGL2?: boolean;
    /** 是否使用WebGL1（如果不支持WebGL2） */
    useWebGL1Fallback?: boolean;
    /** 是否使用移动优化 */
    useMobileOptimization?: boolean;
    /** 是否使用实例化渲染 */
    useInstancing?: boolean;
    /** 是否使用自动LOD */
    useAutoLOD?: boolean;
    /** LOD距离阈值 */
    lodDistanceThresholds?: number[];
    /** 是否使用异步计算 */
    useAsyncCompute?: boolean;
    /** 是否使用共享内存 */
    useSharedMemory?: boolean;
}
/**
 * 增强版GPU面部动画组件
 */
export declare class EnhancedGPUFacialAnimationComponent extends Component {
    /** 组件类型 */
    static readonly type = "EnhancedGPUFacialAnimation";
    /** 原始材质 */
    private originalMaterials;
    /** GPU材质 */
    private gpuMaterials;
    /** 混合形状纹理 */
    private blendShapeTexture;
    /** 混合形状数据 */
    private blendShapeData;
    /** 混合形状名称到索引的映射 */
    private blendShapeMap;
    /** 表情到混合形状的映射 */
    private expressionMap;
    /** 口型到混合形状的映射 */
    private visemeMap;
    /** 是否初始化 */
    private initialized;
    /** 是否需要更新 */
    private needsUpdate;
    /** 纹理大小 */
    private textureSize;
    /** 是否使用WebGPU */
    private usingWebGPU;
    /** 是否使用WebGL2 */
    private usingWebGL2;
    /** 是否使用WebGL1 */
    private usingWebGL1;
    /** 是否使用移动优化 */
    private usingMobileOptimization;
    /** LOD级别 */
    private lodLevel;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     */
    constructor();
    /**
     * 初始化组件
     * @param mesh 骨骼网格
     * @param textureSize 纹理大小
     * @param useWebGPU 是否使用WebGPU
     * @param useWebGL2 是否使用WebGL2
     * @param useWebGL1Fallback 是否使用WebGL1回退
     * @param useMobileOptimization 是否使用移动优化
     */
    initialize(mesh: THREE.SkinnedMesh, textureSize?: number, useWebGPU?: boolean, useWebGL2?: boolean, useWebGL1Fallback?: boolean, useMobileOptimization?: boolean): void;
    /**
     * 创建混合形状映射
     * @param mesh 骨骼网格
     */
    private createBlendShapeMap;
    /**
     * 创建默认表情映射
     */
    private createDefaultExpressionMap;
    /**
     * 创建默认口型映射
     */
    private createDefaultVisemeMap;
    /**
     * 创建混合形状纹理
     */
    private createBlendShapeTexture;
    /**
     * 创建GPU材质
     * @param mesh 骨骼网格
     */
    private createGPUMaterial;
    /**
     * 创建WebGPU材质
     * @param mesh 骨骼网格
     * @returns WebGPU材质
     */
    private createWebGPUMaterial;
    /**
     * 创建WebGL2材质
     * @param mesh 骨骼网格
     * @returns WebGL2材质
     */
    private createWebGL2Material;
    /**
     * 创建WebGL1材质
     * @param mesh 骨骼网格
     * @returns WebGL1材质
     */
    private createWebGL1Material;
    /**
     * 获取WebGL2顶点着色器
     * @param mesh 骨骼网格
     * @returns 顶点着色器代码
     */
    private getWebGL2VertexShader;
    /**
     * 获取WebGL1顶点着色器
     * @param mesh 骨骼网格
     * @returns 顶点着色器代码
     */
    private getWebGL1VertexShader;
    /**
     * 获取默认片段着色器
     * @returns 片段着色器代码
     */
    private getDefaultFragmentShader;
    /**
     * 设置混合形状权重
     * @param name 混合形状名称
     * @param weight 权重
     */
    setBlendShapeWeight(name: string, weight: number): void;
    /**
     * 设置表情
     * @param expression 表情类型
     * @param weight 权重
     */
    setExpression(expression: FacialExpressionType, weight: number): void;
    /**
     * 设置口型
     * @param viseme 口型类型
     * @param weight 权重
     */
    setViseme(viseme: VisemeType, weight: number): void;
    /**
     * 设置表情映射
     * @param expression 表情类型
     * @param blendShapeName 混合形状名称
     */
    setExpressionMapping(expression: FacialExpressionType, blendShapeName: string): void;
    /**
     * 设置口型映射
     * @param viseme 口型类型
     * @param blendShapeName 混合形状名称
     */
    setVisemeMapping(viseme: VisemeType, blendShapeName: string): void;
    /**
     * 设置LOD级别
     * @param level LOD级别
     */
    setLODLevel(level: number): void;
    /**
     * 恢复原始材质
     * @param mesh 骨骼网格
     */
    restoreOriginalMaterial(mesh: THREE.Mesh): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
}
