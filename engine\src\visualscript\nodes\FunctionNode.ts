/**
 * 视觉脚本函数节点
 * 函数节点用于执行纯函数，不影响执行流程
 */
import { Node, NodeCategory, NodeOptions, NodeType, SocketDirection, SocketType } from './Node';

/**
 * 函数节点选项
 */
export interface FunctionNodeOptions extends NodeOptions {
  /** 函数名称 */
  functionName?: string;
}

/**
 * 函数节点基类
 */
export class FunctionNode extends Node {
  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.FUNCTION;
  
  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.FUNCTION;
  
  /** 函数名称 */
  protected functionName: string;
  
  /** 是否已执行 */
  protected executed: boolean = false;
  
  /** 执行结果 */
  protected result: any = null;
  
  /**
   * 创建函数节点
   * @param options 节点选项
   */
  constructor(options: FunctionNodeOptions) {
    super(options);
    
    this.functionName = options.functionName || '';
  }
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 函数节点没有流程插槽，只有数据输入和输出插槽
    // 子类需要添加具体的数据插槽
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 如果已经执行过，直接返回结果
    if (this.executed) {
      return this.result;
    }
    
    // 获取所有输入值
    const inputs: Record<string, any> = {};
    
    for (const [name, socket] of this.inputs.entries()) {
      inputs[name] = this.getInputValue(name);
    }
    
    // 执行函数
    this.result = this.compute(inputs);
    this.executed = true;
    
    // 设置输出值
    if (this.result !== null && this.result !== undefined) {
      // 如果结果是对象，分别设置各个输出
      if (typeof this.result === 'object' && !Array.isArray(this.result)) {
        for (const [key, value] of Object.entries(this.result)) {
          if (this.outputs.has(key)) {
            this.setOutputValue(key, value);
          }
        }
      } 
      // 如果只有一个输出，直接设置
      else if (this.outputs.size === 1) {
        const outputName = Array.from(this.outputs.keys())[0];
        this.setOutputValue(outputName, this.result);
      }
    }
    
    return this.result;
  }
  
  /**
   * 计算函数结果
   * @param inputs 输入值
   * @returns 计算结果
   */
  protected compute(inputs: Record<string, any>): any {
    // 子类实现
    return null;
  }
  
  /**
   * 重置执行状态
   */
  public reset(): void {
    this.executed = false;
    this.result = null;
  }
}
