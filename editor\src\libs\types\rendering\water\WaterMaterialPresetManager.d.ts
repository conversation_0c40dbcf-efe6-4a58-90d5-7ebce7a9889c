import { EventEmitter } from '../../utils/EventEmitter';
import { WaterMaterial, WaterMaterialConfig } from './WaterMaterial';
/**
 * 水体材质预设接口
 */
export interface WaterMaterialPreset {
    /** 预设ID */
    id: string;
    /** 预设名称 */
    name: string;
    /** 预设描述 */
    description: string;
    /** 预设类别 */
    category: string;
    /** 预设标签 */
    tags: string[];
    /** 预设缩略图 */
    thumbnail?: string;
    /** 预设作者 */
    author?: string;
    /** 水体材质配置 */
    config: WaterMaterialConfig;
}
/**
 * 水体材质预设管理器配置
 */
export interface WaterMaterialPresetManagerConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否从服务器加载预设 */
    loadFromServer?: boolean;
    /** 服务器URL */
    serverUrl?: string;
}
/**
 * 水体材质预设管理器
 */
export declare class WaterMaterialPresetManager extends EventEmitter {
    /** 预设映射 */
    private presets;
    /** 预设类别集合 */
    private categories;
    /** 预设标签集合 */
    private tags;
    /** 配置 */
    private config;
    /** 单例实例 */
    private static instance;
    /**
     * 构造函数
     * @param config 配置
     */
    private constructor();
    /**
     * 获取单例实例
     * @param config 配置
     * @returns 水体材质预设管理器实例
     */
    static getInstance(config?: WaterMaterialPresetManagerConfig): WaterMaterialPresetManager;
    /**
     * 初始化默认预设
     */
    private initializeDefaultPresets;
    /**
     * 从服务器加载预设
     */
    private loadFromServer;
    /**
     * 添加预设
     * @param preset 水体材质预设
     */
    addPreset(preset: WaterMaterialPreset): void;
    /**
     * 获取预设
     * @param id 预设ID
     * @returns 水体材质预设
     */
    getPreset(id: string): WaterMaterialPreset | undefined;
    /**
     * 获取所有预设
     * @returns 所有水体材质预设
     */
    getAllPresets(): WaterMaterialPreset[];
    /**
     * 获取指定类别的预设
     * @param category 预设类别
     * @returns 指定类别的水体材质预设
     */
    getPresetsByCategory(category: string): WaterMaterialPreset[];
    /**
     * 获取包含指定标签的预设
     * @param tag 预设标签
     * @returns 包含指定标签的水体材质预设
     */
    getPresetsByTag(tag: string): WaterMaterialPreset[];
    /**
     * 获取所有类别
     * @returns 所有预设类别
     */
    getAllCategories(): string[];
    /**
     * 获取所有标签
     * @returns 所有预设标签
     */
    getAllTags(): string[];
    /**
     * 应用预设到水体材质
     * @param material 水体材质
     * @param presetId 预设ID
     * @returns 是否应用成功
     */
    applyPreset(material: WaterMaterial, presetId: string): boolean;
    /**
     * 创建基于预设的水体材质
     * @param presetId 预设ID
     * @returns 水体材质
     */
    createMaterialFromPreset(presetId: string): WaterMaterial | null;
}
