/**
 * VRM人形骨骼名称
 * 基于VRM规范的人形骨骼名称枚举
 * @see https://github.com/vrm-c/vrm-specification/blob/master/specification/VRMC_vrm-1.0/humanoid.md
 */
export declare enum VRMHumanBoneName {
    /** 头部 */
    Head = "head",
    /** 颈部 */
    Neck = "neck",
    /** 胸部 */
    Chest = "chest",
    /** 上胸部 */
    UpperChest = "upperChest",
    /** 脊柱 */
    Spine = "spine",
    /** 髋部 */
    Hips = "hips",
    /** 左眼 */
    LeftEye = "leftEye",
    /** 右眼 */
    RightEye = "rightEye",
    /** 左肩膀 */
    LeftShoulder = "leftShoulder",
    /** 左上臂 */
    LeftUpperArm = "leftUpperArm",
    /** 左前臂 */
    LeftLowerArm = "leftLowerArm",
    /** 左手 */
    LeftHand = "leftHand",
    /** 右肩膀 */
    RightShoulder = "rightShoulder",
    /** 右上臂 */
    RightUpperArm = "rightUpperArm",
    /** 右前臂 */
    RightLowerArm = "rightLowerArm",
    /** 右手 */
    RightHand = "rightHand",
    /** 左大腿 */
    LeftUpperLeg = "leftUpperLeg",
    /** 左小腿 */
    LeftLowerLeg = "leftLowerLeg",
    /** 左脚 */
    LeftFoot = "leftFoot",
    /** 左脚趾 */
    LeftToes = "leftToes",
    /** 右大腿 */
    RightUpperLeg = "rightUpperLeg",
    /** 右小腿 */
    RightLowerLeg = "rightLowerLeg",
    /** 右脚 */
    RightFoot = "rightFoot",
    /** 右脚趾 */
    RightToes = "rightToes",
    /** 左拇指近端 */
    LeftThumbProximal = "leftThumbProximal",
    /** 左拇指中端 */
    LeftThumbIntermediate = "leftThumbIntermediate",
    /** 左拇指远端 */
    LeftThumbDistal = "leftThumbDistal",
    /** 左食指近端 */
    LeftIndexProximal = "leftIndexProximal",
    /** 左食指中端 */
    LeftIndexIntermediate = "leftIndexIntermediate",
    /** 左食指远端 */
    LeftIndexDistal = "leftIndexDistal",
    /** 左中指近端 */
    LeftMiddleProximal = "leftMiddleProximal",
    /** 左中指中端 */
    LeftMiddleIntermediate = "leftMiddleIntermediate",
    /** 左中指远端 */
    LeftMiddleDistal = "leftMiddleDistal",
    /** 左无名指近端 */
    LeftRingProximal = "leftRingProximal",
    /** 左无名指中端 */
    LeftRingIntermediate = "leftRingIntermediate",
    /** 左无名指远端 */
    LeftRingDistal = "leftRingDistal",
    /** 左小指近端 */
    LeftLittleProximal = "leftLittleProximal",
    /** 左小指中端 */
    LeftLittleIntermediate = "leftLittleIntermediate",
    /** 左小指远端 */
    LeftLittleDistal = "leftLittleDistal",
    /** 右拇指近端 */
    RightThumbProximal = "rightThumbProximal",
    /** 右拇指中端 */
    RightThumbIntermediate = "rightThumbIntermediate",
    /** 右拇指远端 */
    RightThumbDistal = "rightThumbDistal",
    /** 右食指近端 */
    RightIndexProximal = "rightIndexProximal",
    /** 右食指中端 */
    RightIndexIntermediate = "rightIndexIntermediate",
    /** 右食指远端 */
    RightIndexDistal = "rightIndexDistal",
    /** 右中指近端 */
    RightMiddleProximal = "rightMiddleProximal",
    /** 右中指中端 */
    RightMiddleIntermediate = "rightMiddleIntermediate",
    /** 右中指远端 */
    RightMiddleDistal = "rightMiddleDistal",
    /** 右无名指近端 */
    RightRingProximal = "rightRingProximal",
    /** 右无名指中端 */
    RightRingIntermediate = "rightRingIntermediate",
    /** 右无名指远端 */
    RightRingDistal = "rightRingDistal",
    /** 右小指近端 */
    RightLittleProximal = "rightLittleProximal",
    /** 右小指中端 */
    RightLittleIntermediate = "rightLittleIntermediate",
    /** 右小指远端 */
    RightLittleDistal = "rightLittleDistal"
}
/**
 * VRM人形骨骼列表
 * 所有VRM人形骨骼名称的数组
 */
export declare const VRMHumanBoneList: VRMHumanBoneName[];
/**
 * 骨骼分组
 * 将骨骼分为不同的组，用于处理不同部位
 */
export declare const VRMHumanBoneGroups: {
    /** 躯干骨骼 */
    TORSO: VRMHumanBoneName[];
    /** 头部骨骼 */
    HEAD: VRMHumanBoneName[];
    /** 左臂骨骼 */
    LEFT_ARM: VRMHumanBoneName[];
    /** 右臂骨骼 */
    RIGHT_ARM: VRMHumanBoneName[];
    /** 左腿骨骼 */
    LEFT_LEG: VRMHumanBoneName[];
    /** 右腿骨骼 */
    RIGHT_LEG: VRMHumanBoneName[];
    /** 左手指骨骼 */
    LEFT_FINGERS: VRMHumanBoneName[];
    /** 右手指骨骼 */
    RIGHT_FINGERS: VRMHumanBoneName[];
};
