/**
 * 性能测试运行器
 * 用于运行各种性能测试并生成报告
 */
import { PerformanceTest, PerformanceTestResult } from './PerformanceTestFramework';
import { RenderingPerformanceTest, RenderingPerformanceTestConfig } from './RenderingPerformanceTest';
import { PhysicsPerformanceTest, PhysicsPerformanceTestConfig } from './PhysicsPerformanceTest';
import { Debug } from '../../src/utils/Debug';

/**
 * 测试套件配置
 */
export interface TestSuiteConfig {
  /** 测试套件名称 */
  name: string;
  /** 测试套件描述 */
  description?: string;
  /** 渲染测试配置 */
  renderingTests?: RenderingPerformanceTestConfig[];
  /** 物理测试配置 */
  physicsTests?: PhysicsPerformanceTestConfig[];
  /** 其他测试配置 */
  [key: string]: any;
}

/**
 * 测试报告
 */
export interface TestReport {
  /** 测试套件名称 */
  name: string;
  /** 测试套件描述 */
  description?: string;
  /** 测试结果 */
  results: PerformanceTestResult[];
  /** 测试开始时间 */
  startTime: number;
  /** 测试结束时间 */
  endTime: number;
  /** 测试总时间（毫秒） */
  totalTime: number;
  /** 通过的测试数量 */
  passedCount: number;
  /** 失败的测试数量 */
  failedCount: number;
}

/**
 * 性能测试运行器
 */
export class PerformanceTestRunner {
  /** 测试套件配置 */
  private config: TestSuiteConfig;
  /** 测试列表 */
  private tests: PerformanceTest[] = [];
  /** 当前测试索引 */
  private currentTestIndex: number = 0;
  /** 测试结果 */
  private results: PerformanceTestResult[] = [];
  /** 测试开始时间 */
  private startTime: number = 0;
  /** 测试结束时间 */
  private endTime: number = 0;
  /** 测试完成回调 */
  private onCompleteCallback?: (report: TestReport) => void;

  /**
   * 创建性能测试运行器
   * @param config 测试套件配置
   */
  constructor(config: TestSuiteConfig) {
    this.config = config;
    this.initializeTests();
  }

  /**
   * 初始化测试
   */
  private initializeTests(): void {
    // 添加渲染测试
    if (this.config.renderingTests) {
      for (const config of this.config.renderingTests) {
        this.tests.push(new RenderingPerformanceTest(config));
      }
    }

    // 添加物理测试
    if (this.config.physicsTests) {
      for (const config of this.config.physicsTests) {
        this.tests.push(new PhysicsPerformanceTest(config));
      }
    }

    // 可以在这里添加其他类型的测试
  }

  /**
   * 运行测试套件
   * @param onComplete 测试完成回调
   */
  public run(onComplete?: (report: TestReport) => void): void {
    this.onCompleteCallback = onComplete;
    this.results = [];
    this.currentTestIndex = 0;
    this.startTime = performance.now();

    Debug.log('性能测试运行器', `开始运行测试套件: ${this.config.name}`);
    Debug.log('性能测试运行器', `共 ${this.tests.length} 个测试`);

    // 运行第一个测试
    this.runNextTest();
  }

  /**
   * 运行下一个测试
   */
  private runNextTest(): void {
    if (this.currentTestIndex >= this.tests.length) {
      // 所有测试完成
      this.completeTestSuite();
      return;
    }

    const test = this.tests[this.currentTestIndex];
    Debug.log('性能测试运行器', `运行测试 ${this.currentTestIndex + 1}/${this.tests.length}: ${test.getConfig().name}`);

    // 运行测试
    test.run((result) => {
      // 记录结果
      this.results.push(result);
      Debug.log('性能测试运行器', `测试 ${result.name} ${result.passed ? '通过' : '失败'}`);
      if (!result.passed && result.errorMessage) {
        Debug.error('性能测试运行器', result.errorMessage);
      }

      // 运行下一个测试
      this.currentTestIndex++;
      setTimeout(() => this.runNextTest(), 500);
    });
  }

  /**
   * 完成测试套件
   */
  private completeTestSuite(): void {
    this.endTime = performance.now();
    const totalTime = this.endTime - this.startTime;

    // 计算通过和失败的测试数量
    const passedCount = this.results.filter(result => result.passed).length;
    const failedCount = this.results.length - passedCount;

    // 创建测试报告
    const report: TestReport = {
      name: this.config.name,
      description: this.config.description,
      results: this.results,
      startTime: this.startTime,
      endTime: this.endTime,
      totalTime,
      passedCount,
      failedCount,
    };

    Debug.log('性能测试运行器', `测试套件完成: ${this.config.name}`);
    Debug.log('性能测试运行器', `总时间: ${(totalTime / 1000).toFixed(2)}秒`);
    Debug.log('性能测试运行器', `通过: ${passedCount}/${this.results.length}, 失败: ${failedCount}/${this.results.length}`);

    // 调用完成回调
    if (this.onCompleteCallback) {
      this.onCompleteCallback(report);
    }
  }

  /**
   * 生成HTML报告
   * @param report 测试报告
   * @returns HTML报告
   */
  public static generateHTMLReport(report: TestReport): string {
    const passedCount = report.passedCount;
    const totalCount = report.results.length;
    const passRate = totalCount > 0 ? (passedCount / totalCount) * 100 : 0;

    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>DL（Digital Learning）引擎性能测试报告 - ${report.name}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
          h1, h2, h3 { color: #0066cc; }
          .summary { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .test-result { margin-bottom: 15px; padding: 15px; border-radius: 5px; }
          .test-passed { background-color: #dff0d8; border: 1px solid #d6e9c6; }
          .test-failed { background-color: #f2dede; border: 1px solid #ebccd1; }
          .performance-data { margin-top: 10px; }
          table { border-collapse: collapse; width: 100%; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          .progress-bar { height: 20px; background-color: #e9ecef; border-radius: 5px; margin-top: 5px; }
          .progress-bar-fill { height: 100%; border-radius: 5px; }
          .progress-bar-pass { background-color: #28a745; }
          .progress-bar-fail { background-color: #dc3545; }
        </style>
      </head>
      <body>
        <h1>DL（Digital Learning）引擎性能测试报告</h1>
        <div class="summary">
          <h2>${report.name}</h2>
          <p>${report.description || ''}</p>
          <p>测试时间: ${new Date(report.startTime).toLocaleString()} - ${new Date(report.endTime).toLocaleString()}</p>
          <p>总时间: ${(report.totalTime / 1000).toFixed(2)}秒</p>
          <p>通过率: ${passedCount}/${totalCount} (${passRate.toFixed(2)}%)</p>
          <div class="progress-bar">
            <div class="progress-bar-fill progress-bar-pass" style="width: ${passRate}%"></div>
          </div>
        </div>
        
        <h2>测试结果</h2>
    `;

    // 添加每个测试结果
    for (const result of report.results) {
      const resultClass = result.passed ? 'test-passed' : 'test-failed';
      
      html += `
        <div class="test-result ${resultClass}">
          <h3>${result.name}</h3>
          <p>状态: ${result.passed ? '通过' : '失败'}</p>
          ${result.errorMessage ? `<p>错误: ${result.errorMessage}</p>` : ''}
          
          <div class="performance-data">
            <h4>性能数据</h4>
            <table>
              <tr>
                <th>指标</th>
                <th>值</th>
              </tr>
              <tr>
                <td>帧率 (FPS)</td>
                <td>${result.performanceData.fps.toFixed(2)}</td>
              </tr>
              <tr>
                <td>渲染时间 (ms)</td>
                <td>${result.performanceData.renderTime.toFixed(2)}</td>
              </tr>
              <tr>
                <td>内存使用 (MB)</td>
                <td>${result.performanceData.memoryUsage.toFixed(2)}</td>
              </tr>
      `;
      
      // 添加自定义性能数据
      for (const key in result.performanceData) {
        if (key !== 'fps' && key !== 'renderTime' && key !== 'memoryUsage' && !key.endsWith('History')) {
          const value = result.performanceData[key];
          html += `
              <tr>
                <td>${key}</td>
                <td>${typeof value === 'number' ? value.toFixed(2) : value}</td>
              </tr>
          `;
        }
      }
      
      html += `
            </table>
          </div>
        </div>
      `;
    }
    
    html += `
      </body>
      </html>
    `;
    
    return html;
  }

  /**
   * 保存HTML报告到文件
   * @param report 测试报告
   * @param filePath 文件路径
   */
  public static saveHTMLReport(report: TestReport, filePath: string): void {
    const html = PerformanceTestRunner.generateHTMLReport(report);
    
    // 在浏览器环境中
    if (typeof window !== 'undefined') {
      const blob = new Blob([html], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filePath;
      a.click();
      URL.revokeObjectURL(url);
    }
    // 在Node.js环境中
    else if (typeof require !== 'undefined') {
      const fs = require('fs');
      fs.writeFileSync(filePath, html);
    }
  }
}
