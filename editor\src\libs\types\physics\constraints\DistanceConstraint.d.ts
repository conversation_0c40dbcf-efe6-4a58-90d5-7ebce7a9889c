import type { Entity } from '../../core/Entity';
import { PhysicsConstraint } from './PhysicsConstraint';
/**
 * 距离约束选项
 */
export interface DistanceConstraintOptions {
    /** 距离 */
    distance?: number;
    /** 最大力 */
    maxForce?: number;
    /** 是否允许连接的物体之间碰撞 */
    collideConnected?: boolean;
}
/**
 * 距离约束
 */
export declare class DistanceConstraint extends PhysicsConstraint {
    /** 组件类型 */
    static readonly type: string;
    /** 距离 */
    private distance;
    /** 最大力 */
    private maxForce;
    /**
     * 创建距离约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    constructor(targetEntity?: Entity | null, options?: DistanceConstraintOptions);
    /**
     * 创建约束
     */
    protected createConstraint(): void;
    /**
     * 设置距离
     * @param distance 距离
     */
    setDistance(distance: number): void;
    /**
     * 获取距离
     * @returns 距离
     */
    getDistance(): number;
    /**
     * 设置最大力
     * @param maxForce 最大力
     */
    setMaxForce(maxForce: number): void;
    /**
     * 获取最大力
     * @returns 最大力
     */
    getMaxForce(): number;
    /**
     * 获取当前距离
     * @returns 当前距离
     */
    getCurrentDistance(): number;
}
