/**
 * 网络事件缓冲器
 * 负责管理网络事件的缓冲和优先级处理
 */
import { NetworkEvent } from './NetworkEvent';
/**
 * 事件优先级
 */
export declare enum EventPriority {
    /** 最高优先级 */
    HIGHEST = 0,
    /** 高优先级 */
    HIGH = 1,
    /** 中等优先级 */
    MEDIUM = 2,
    /** 低优先级 */
    LOW = 3,
    /** 最低优先级 */
    LOWEST = 4
}
/**
 * 网络事件缓冲器配置
 */
export interface NetworkEventBufferConfig {
    /** 最大缓冲事件数量 */
    maxBufferSize?: number;
    /** 事件处理间隔（毫秒） */
    processInterval?: number;
    /** 是否自动处理事件 */
    autoProcess?: boolean;
    /** 每次处理的最大事件数量 */
    maxEventsPerProcess?: number;
    /** 事件类型优先级映射 */
    eventTypePriorities?: Record<string, EventPriority>;
}
/**
 * 网络事件缓冲器
 * 负责管理网络事件的缓冲和优先级处理
 */
export declare class NetworkEventBuffer {
    /** 配置 */
    private config;
    /** 事件缓冲区 */
    private eventBuffer;
    /** 处理定时器ID */
    private processTimerId;
    /** 事件处理回调 */
    private eventHandler;
    /** 是否正在处理事件 */
    private isProcessing;
    /** 事件类型优先级映射 */
    private eventTypePriorities;
    /**
     * 创建网络事件缓冲器
     * @param config 配置
     */
    constructor(config?: NetworkEventBufferConfig);
    /**
     * 初始化事件类型优先级映射
     */
    private initEventTypePriorities;
    /**
     * 启动事件处理
     */
    startProcessing(): void;
    /**
     * 停止事件处理
     */
    stopProcessing(): void;
    /**
     * 设置事件处理回调
     * @param handler 事件处理回调
     */
    setEventHandler(handler: (event: NetworkEvent) => void): void;
    /**
     * 添加事件到缓冲区
     * @param event 网络事件
     * @returns 是否添加成功
     */
    addEvent(event: NetworkEvent): boolean;
    /**
     * 处理事件
     */
    processEvents(): void;
    /**
     * 获取事件优先级
     * @param eventType 事件类型
     * @returns 事件优先级
     */
    private getEventPriority;
    /**
     * 按优先级排序事件缓冲区
     */
    private sortEventBuffer;
    /**
     * 清空事件缓冲区
     */
    clearBuffer(): void;
    /**
     * 获取缓冲区中的事件数量
     * @returns 事件数量
     */
    getBufferSize(): number;
    /**
     * 设置事件类型的优先级
     * @param eventType 事件类型
     * @param priority 优先级
     */
    setEventTypePriority(eventType: string, priority: EventPriority): void;
    /**
     * 获取事件类型的优先级
     * @param eventType 事件类型
     * @returns 优先级
     */
    getEventTypePriority(eventType: string): EventPriority;
    /**
     * 销毁缓冲器
     */
    dispose(): void;
}
