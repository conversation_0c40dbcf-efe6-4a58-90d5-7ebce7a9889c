/**
 * 空间分区系统
 * 用于加速软体物理的碰撞检测
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';

/**
 * 空间分区单元格
 */
export class SpatialCell {
  /** 单元格索引 */
  public index: THREE.Vector3;
  
  /** 单元格中的粒子 */
  public particles: CANNON.Body[] = [];
  
  /**
   * 创建空间分区单元格
   * @param x X索引
   * @param y Y索引
   * @param z Z索引
   */
  constructor(x: number, y: number, z: number) {
    this.index = new THREE.Vector3(x, y, z);
  }
  
  /**
   * 添加粒子
   * @param particle 粒子
   */
  public addParticle(particle: CANNON.Body): void {
    if (!this.particles.includes(particle)) {
      this.particles.push(particle);
    }
  }
  
  /**
   * 移除粒子
   * @param particle 粒子
   */
  public removeParticle(particle: CANNON.Body): void {
    const index = this.particles.indexOf(particle);
    if (index !== -1) {
      this.particles.splice(index, 1);
    }
  }
  
  /**
   * 清空单元格
   */
  public clear(): void {
    this.particles.length = 0;
  }
}

/**
 * 空间分区系统选项
 */
export interface SpatialPartitioningOptions {
  /** 单元格大小 */
  cellSize?: number;
  /** 世界边界最小点 */
  worldMin?: THREE.Vector3;
  /** 世界边界最大点 */
  worldMax?: THREE.Vector3;
}

/**
 * 空间分区系统
 * 使用均匀网格进行空间分区
 */
export class SpatialPartitioning {
  /** 单元格大小 */
  private cellSize: number;
  
  /** 世界边界最小点 */
  private worldMin: THREE.Vector3;
  
  /** 世界边界最大点 */
  private worldMax: THREE.Vector3;
  
  /** 网格尺寸 */
  private gridSize: THREE.Vector3;
  
  /** 单元格映射 */
  private cells: Map<string, SpatialCell> = new Map();
  
  /** 粒子到单元格的映射 */
  private particleCells: Map<CANNON.Body, SpatialCell> = new Map();
  
  /**
   * 创建空间分区系统
   * @param options 空间分区系统选项
   */
  constructor(options: SpatialPartitioningOptions = {}) {
    this.cellSize = options.cellSize || 1.0;
    this.worldMin = options.worldMin || new THREE.Vector3(-100, -100, -100);
    this.worldMax = options.worldMax || new THREE.Vector3(100, 100, 100);
    
    // 计算网格尺寸
    this.gridSize = new THREE.Vector3(
      Math.ceil((this.worldMax.x - this.worldMin.x) / this.cellSize),
      Math.ceil((this.worldMax.y - this.worldMin.y) / this.cellSize),
      Math.ceil((this.worldMax.z - this.worldMin.z) / this.cellSize)
    );
  }
  
  /**
   * 获取单元格键
   * @param x X索引
   * @param y Y索引
   * @param z Z索引
   * @returns 单元格键
   */
  private getCellKey(x: number, y: number, z: number): string {
    return `${x},${y},${z}`;
  }
  
  /**
   * 获取位置所在的单元格索引
   * @param position 位置
   * @returns 单元格索引
   */
  private getCellIndex(position: CANNON.Vec3): THREE.Vector3 {
    const x = Math.floor((position.x - this.worldMin.x) / this.cellSize);
    const y = Math.floor((position.y - this.worldMin.y) / this.cellSize);
    const z = Math.floor((position.z - this.worldMin.z) / this.cellSize);
    
    // 确保索引在有效范围内
    return new THREE.Vector3(
      THREE.MathUtils.clamp(x, 0, this.gridSize.x - 1),
      THREE.MathUtils.clamp(y, 0, this.gridSize.y - 1),
      THREE.MathUtils.clamp(z, 0, this.gridSize.z - 1)
    );
  }
  
  /**
   * 获取或创建单元格
   * @param index 单元格索引
   * @returns 单元格
   */
  private getOrCreateCell(index: THREE.Vector3): SpatialCell {
    const key = this.getCellKey(index.x, index.y, index.z);
    let cell = this.cells.get(key);
    
    if (!cell) {
      cell = new SpatialCell(index.x, index.y, index.z);
      this.cells.set(key, cell);
    }
    
    return cell;
  }
  
  /**
   * 更新粒子位置
   * @param particle 粒子
   */
  public updateParticle(particle: CANNON.Body): void {
    // 获取粒子当前所在的单元格
    const currentCell = this.particleCells.get(particle);
    
    // 获取粒子应该在的单元格
    const newCellIndex = this.getCellIndex(particle.position);
    const newCellKey = this.getCellKey(newCellIndex.x, newCellIndex.y, newCellIndex.z);
    const newCell = this.getOrCreateCell(newCellIndex);
    
    // 如果粒子所在单元格发生变化，更新单元格
    if (!currentCell || this.getCellKey(currentCell.index.x, currentCell.index.y, currentCell.index.z) !== newCellKey) {
      // 从旧单元格中移除
      if (currentCell) {
        currentCell.removeParticle(particle);
      }
      
      // 添加到新单元格
      newCell.addParticle(particle);
      this.particleCells.set(particle, newCell);
    }
  }
  
  /**
   * 添加粒子
   * @param particle 粒子
   */
  public addParticle(particle: CANNON.Body): void {
    const cellIndex = this.getCellIndex(particle.position);
    const cell = this.getOrCreateCell(cellIndex);
    
    cell.addParticle(particle);
    this.particleCells.set(particle, cell);
  }
  
  /**
   * 移除粒子
   * @param particle 粒子
   */
  public removeParticle(particle: CANNON.Body): void {
    const cell = this.particleCells.get(particle);
    if (cell) {
      cell.removeParticle(particle);
      this.particleCells.delete(particle);
    }
  }
  
  /**
   * 更新所有粒子
   * @param particles 粒子数组
   */
  public updateAll(particles: CANNON.Body[]): void {
    for (const particle of particles) {
      this.updateParticle(particle);
    }
  }
  
  /**
   * 获取附近的粒子
   * @param position 位置
   * @param radius 半径
   * @returns 附近的粒子
   */
  public getNearbyParticles(position: CANNON.Vec3, radius: number): CANNON.Body[] {
    // 计算搜索范围（单元格索引）
    const cellRadius = Math.ceil(radius / this.cellSize);
    const centerIndex = this.getCellIndex(position);
    
    const nearbyParticles: CANNON.Body[] = [];
    const processedParticles = new Set<CANNON.Body>();
    
    // 遍历搜索范围内的所有单元格
    for (let x = centerIndex.x - cellRadius; x <= centerIndex.x + cellRadius; x++) {
      for (let y = centerIndex.y - cellRadius; y <= centerIndex.y + cellRadius; y++) {
        for (let z = centerIndex.z - cellRadius; z <= centerIndex.z + cellRadius; z++) {
          // 确保索引在有效范围内
          if (x < 0 || y < 0 || z < 0 || x >= this.gridSize.x || y >= this.gridSize.y || z >= this.gridSize.z) {
            continue;
          }
          
          const cellKey = this.getCellKey(x, y, z);
          const cell = this.cells.get(cellKey);
          
          if (cell) {
            // 检查单元格中的每个粒子
            for (const particle of cell.particles) {
              // 避免重复处理
              if (processedParticles.has(particle)) {
                continue;
              }
              
              processedParticles.add(particle);
              
              // 计算距离
              const dx = particle.position.x - position.x;
              const dy = particle.position.y - position.y;
              const dz = particle.position.z - position.z;
              const distanceSquared = dx * dx + dy * dy + dz * dz;
              
              // 如果在半径内，添加到结果
              if (distanceSquared <= radius * radius) {
                nearbyParticles.push(particle);
              }
            }
          }
        }
      }
    }
    
    return nearbyParticles;
  }
  
  /**
   * 清空所有单元格
   */
  public clear(): void {
    this.cells.clear();
    this.particleCells.clear();
  }
}
