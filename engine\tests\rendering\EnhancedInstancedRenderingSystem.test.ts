/**
 * 增强实例化渲染系统测试
 */
import * as THREE from 'three';
import { EnhancedInstancedRenderingSystem, EnhancedInstanceData } from '../../src/rendering/optimization/EnhancedInstancedRenderingSystem';
import { GPUInstanceUpdater } from '../../src/rendering/optimization/GPUInstanceUpdater';
import { EntityManager } from '../../src/core/EntityManager';
import { Entity } from '../../src/core/Entity';
import { Camera } from '../../src/rendering/Camera';
import { Scene } from '../../src/scene/Scene';
import { Transform } from '../../src/scene/Transform';

// 模拟THREE.WebGLRenderer
jest.mock('three', () => {
  const originalThree = jest.requireActual('three');
  return {
    ...originalThree,
    WebGLRenderer: jest.fn().mockImplementation(() => ({
      setRenderTarget: jest.fn(),
      render: jest.fn(),
      readRenderTargetPixels: jest.fn(),
      dispose: jest.fn(),
    })),
  };
});

describe('EnhancedInstancedRenderingSystem', () => {
  let system: EnhancedInstancedRenderingSystem;
  let entityManager: EntityManager;
  let camera: Entity;
  let scene: Entity;

  beforeEach(() => {
    // 创建实体管理器
    entityManager = new EntityManager();

    // 创建相机实体
    camera = new Entity();
    const cameraComponent = new Camera();
    camera.addComponent(cameraComponent);
    entityManager.addEntity(camera);

    // 创建场景实体
    scene = new Entity();
    const sceneComponent = new Scene();
    scene.addComponent(sceneComponent);
    entityManager.addEntity(scene);

    // 创建系统
    system = new EnhancedInstancedRenderingSystem({
      maxBatchSize: 100,
      useInstanceCulling: true,
      useInstanceLOD: true,
      useInstanceShadow: true,
      useGPUInstanceUpdate: false,
      useDebugVisualization: false,
    });
    system.setEntityManager(entityManager);
    system.initialize();
  });

  afterEach(() => {
    system.destroy();
  });

  test('应该正确创建批处理组', () => {
    // 创建几何体和材质
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    // 创建批处理组
    const batchGroup = system.createBatchGroup(geometry, material, 10);

    // 验证批处理组属性
    expect(batchGroup).toBeDefined();
    expect(batchGroup.geometry).toBe(geometry);
    expect(batchGroup.material).toBe(material);
    expect(batchGroup.instancedMesh).toBeDefined();
    expect(batchGroup.instancedMesh.count).toBe(10);
    expect(batchGroup.instances).toHaveLength(0);
    expect(batchGroup.availableIndices).toHaveLength(10);
    expect(batchGroup.needsUpdate).toBe(true);
  });

  test('应该正确添加实例', () => {
    // 创建几何体和材质
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    // 创建实例数据
    const instanceData: EnhancedInstanceData = {
      position: new THREE.Vector3(1, 2, 3),
      rotation: new THREE.Quaternion(),
      scale: new THREE.Vector3(1, 1, 1),
      visible: true,
      needsUpdate: true,
    };

    // 添加实例
    const instanceId = system.addInstance(geometry, material, instanceData);

    // 验证实例ID
    expect(instanceId).toBeDefined();
    expect(instanceId.startsWith('instance_')).toBe(true);

    // 获取批处理组
    const batchGroup = system['instanceToBatchGroup'].get(instanceId);
    expect(batchGroup).toBeDefined();

    // 验证实例数据
    const index = batchGroup!.instanceToIndex.get(instanceId);
    expect(index).toBeDefined();
    const instance = batchGroup!.instances[index!];
    expect(instance).toBeDefined();
    expect(instance.position.equals(instanceData.position)).toBe(true);
    expect(instance.rotation.equals(instanceData.rotation)).toBe(true);
    expect(instance.scale.equals(instanceData.scale)).toBe(true);
    expect(instance.visible).toBe(instanceData.visible);
    expect(instance.needsUpdate).toBe(instanceData.needsUpdate);
  });

  test('应该正确更新实例', () => {
    // 创建几何体和材质
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    // 创建实例数据
    const instanceData: EnhancedInstanceData = {
      position: new THREE.Vector3(1, 2, 3),
      rotation: new THREE.Quaternion(),
      scale: new THREE.Vector3(1, 1, 1),
      visible: true,
      needsUpdate: true,
    };

    // 添加实例
    const instanceId = system.addInstance(geometry, material, instanceData);

    // 更新实例数据
    const newPosition = new THREE.Vector3(4, 5, 6);
    system.updateInstance(instanceId, { position: newPosition });

    // 获取批处理组
    const batchGroup = system['instanceToBatchGroup'].get(instanceId);
    expect(batchGroup).toBeDefined();

    // 验证实例数据
    const index = batchGroup!.instanceToIndex.get(instanceId);
    expect(index).toBeDefined();
    const instance = batchGroup!.instances[index!];
    expect(instance).toBeDefined();
    expect(instance.position.equals(newPosition)).toBe(true);
    expect(instance.needsUpdate).toBe(true);
    expect(batchGroup!.needsUpdate).toBe(true);
  });

  test('应该正确移除实例', () => {
    // 创建几何体和材质
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    // 创建实例数据
    const instanceData: EnhancedInstanceData = {
      position: new THREE.Vector3(1, 2, 3),
      rotation: new THREE.Quaternion(),
      scale: new THREE.Vector3(1, 1, 1),
      visible: true,
      needsUpdate: true,
    };

    // 添加实例
    const instanceId = system.addInstance(geometry, material, instanceData);

    // 获取批处理组
    const batchGroup = system['instanceToBatchGroup'].get(instanceId);
    expect(batchGroup).toBeDefined();

    // 获取实例索引
    const index = batchGroup!.instanceToIndex.get(instanceId);
    expect(index).toBeDefined();

    // 移除实例
    system.removeInstance(instanceId);

    // 验证实例已移除
    expect(system['instanceToBatchGroup'].has(instanceId)).toBe(false);
    expect(system['instanceToData'].has(instanceId)).toBe(false);
    expect(batchGroup!.instanceToIndex.has(instanceId)).toBe(false);
    expect(batchGroup!.availableIndices).toContain(index);
    expect(batchGroup!.needsUpdate).toBe(true);
  });

  test('应该正确创建自定义实例属性', () => {
    // 创建几何体和材质
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    // 创建批处理组
    const batchGroup = system.createBatchGroup(geometry, material, 10);

    // 创建自定义实例属性
    system.createInstanceAttribute('color', 3, THREE.Float32BufferAttribute);

    // 验证自定义属性已创建
    expect(system['customAttributeDefinitions'].has('color')).toBe(true);
    expect(batchGroup.customAttributeBuffers.has('color')).toBe(true);
    expect(batchGroup.needsUpdate).toBe(true);
  });

  test('应该正确使用GPU实例更新', () => {
    // 创建带有GPU实例更新的系统
    const gpuSystem = new EnhancedInstancedRenderingSystem({
      maxBatchSize: 100,
      useInstanceCulling: true,
      useInstanceLOD: true,
      useInstanceShadow: true,
      useGPUInstanceUpdate: true,
      useDebugVisualization: false,
    });
    gpuSystem.setEntityManager(entityManager);
    gpuSystem.initialize();

    // 验证GPU实例更新器已创建
    expect(gpuSystem['instanceUpdater']).toBeDefined();
    expect(gpuSystem['instanceUpdater'] instanceof GPUInstanceUpdater).toBe(true);

    // 清理
    gpuSystem.destroy();
  });

  test('应该正确处理视锥体剔除', () => {
    // 创建几何体和材质
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    // 创建实例数据
    const instanceData: EnhancedInstanceData = {
      position: new THREE.Vector3(1000, 1000, 1000), // 远离相机的位置
      rotation: new THREE.Quaternion(),
      scale: new THREE.Vector3(1, 1, 1),
      visible: true,
      needsUpdate: true,
    };

    // 添加实例
    const instanceId = system.addInstance(geometry, material, instanceData);

    // 设置相机位置和方向
    const cameraComponent = camera.getComponent<Camera>('Camera');
    cameraComponent!.getObject3D().position.set(0, 0, 0);
    cameraComponent!.getObject3D().lookAt(0, 0, -1);
    cameraComponent!.getObject3D().updateMatrixWorld();

    // 更新系统
    system.update(0.016);

    // 获取批处理组
    const batchGroup = system['instanceToBatchGroup'].get(instanceId);
    expect(batchGroup).toBeDefined();

    // 验证实例是否被剔除（这里需要模拟isInstanceInFrustum方法）
    // 由于我们无法直接测试THREE.js的视锥体剔除，这里只是验证更新逻辑是否执行
    expect(batchGroup!.needsUpdate).toBe(false);
  });

  test('应该正确销毁系统', () => {
    // 创建几何体和材质
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    // 添加实例
    const instanceId = system.addInstance(geometry, material, {
      position: new THREE.Vector3(1, 2, 3),
      rotation: new THREE.Quaternion(),
      scale: new THREE.Vector3(1, 1, 1),
      visible: true,
      needsUpdate: true,
    });

    // 销毁系统
    system.destroy();

    // 验证系统已清空
    expect(system['batchGroups'].size).toBe(0);
    expect(system['geometryToBatchGroup'].size).toBe(0);
    expect(system['materialToBatchGroup'].size).toBe(0);
    expect(system['instanceToBatchGroup'].size).toBe(0);
    expect(system['instanceToData'].size).toBe(0);
    expect(system['debugMeshes']).toHaveLength(0);
  });
});
