/**
 * 性能监控示例
 * 展示如何使用真实的性能监控和场景优化模块
 */
import React, { useState } from 'react';
import { Card, Button, Space, Typography, Row, Col, Progress, Alert, Statistic } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined, ThunderboltOutlined } from '@ant-design/icons';
// 模拟性能监控相关类型和类
enum PerformanceMetricType {
  FPS = 'fps',
  MEMORY_USAGE = 'memoryUsage',
  DRAW_CALLS = 'drawCalls',
  TRIANGLES = 'triangles'
}

class PerformanceMonitorConfig {
  static getInstance() {
    return new PerformanceMonitorConfig();
  }

  configure(_config: any) {
    // 模拟配置方法
  }

  start() {
    // 模拟启动方法
  }

  stop() {
    // 模拟停止方法
  }

  getReport() {
    // 模拟获取报告方法
    return {
      metrics: {
        [PerformanceMetricType.FPS]: { value: 60 },
        [PerformanceMetricType.MEMORY_USAGE]: { value: 256 },
        [PerformanceMetricType.DRAW_CALLS]: { value: 150 },
        [PerformanceMetricType.TRIANGLES]: { value: 50000 }
      }
    };
  }
}

class SceneOptimizer {
  static getInstance() {
    return new SceneOptimizer();
  }

  configure(_config: any) {
    // 模拟配置方法
  }

  async analyzeScene(_config: any) {
    // 模拟场景分析方法
    return {
      suggestions: ['减少多边形数量', '优化纹理大小'],
      score: 75
    };
  }

  async optimizeScene() {
    // 模拟场景优化方法
    return true;
  }
}

const { Title, Paragraph } = Typography;

/**
 * 性能监控示例组件
 */
const PerformanceMonitoringExample: React.FC = () => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [performanceData, setPerformanceData] = useState<any>(null);
  const [optimizationResult, setOptimizationResult] = useState<any>(null);
  const [isOptimizing, setIsOptimizing] = useState(false);

  // 启动性能监控
  const startMonitoring = () => {
    const monitor = PerformanceMonitorConfig.getInstance();
    
    // 配置监控器
    monitor.configure({
      enabled: true,
      sampleInterval: 1000,
      historyLimit: 30,
      autoSample: true,
      debug: true,
      collectRenderMetrics: true,
      collectMemoryMetrics: true,
      collectSystemMetrics: true
    });

    monitor.start();
    setIsMonitoring(true);

    // 定期更新性能数据
    const updateInterval = setInterval(() => {
      const report = monitor.getReport();
      setPerformanceData(report);
    }, 1000);

    // 清理函数
    return () => {
      clearInterval(updateInterval);
    };
  };

  // 停止性能监控
  const stopMonitoring = () => {
    const monitor = PerformanceMonitorConfig.getInstance();
    monitor.stop();
    setIsMonitoring(false);
  };

  // 分析和优化场景
  const analyzeAndOptimizeScene = async () => {
    setIsOptimizing(true);
    
    try {
      const optimizer = SceneOptimizer.getInstance();
      
      // 配置优化器
      optimizer.configure({
        enableAutoLOD: true,
        enableAutoBatching: true,
        enableAutoTextureOptimization: true,
        enableAutoMemoryOptimization: true,
        debug: true,
        thresholds: {
          triangles: { low: 100000, medium: 500000, high: 1000000 },
          drawCalls: { low: 100, medium: 500, high: 1000 },
          memory: { low: 100, medium: 250, high: 500 }
        }
      });

      // 分析场景
      const analysisResult = await optimizer.analyzeScene({
        id: 'example-scene',
        name: '示例场景'
      });

      // 如果有优化建议，执行优化
      if (analysisResult.suggestions.length > 0) {
        const optimizeSuccess = await optimizer.optimizeScene();
        setOptimizationResult({
          ...analysisResult,
          optimizeSuccess
        });
      } else {
        setOptimizationResult(analysisResult);
      }
    } catch (error) {
      console.error('优化失败:', error);
      setOptimizationResult({
        error: '优化过程中发生错误'
      });
    } finally {
      setIsOptimizing(false);
    }
  };

  // 重置数据
  const resetData = () => {
    setPerformanceData(null);
    setOptimizationResult(null);
  };

  // 获取性能指标值
  const getMetricValue = (type: PerformanceMetricType): number => {
    if (!performanceData?.metrics) return 0;
    return performanceData.metrics.get(type)?.value || 0;
  };

  // 获取性能状态
  const getPerformanceStatus = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'normal';
    return 'exception';
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>性能监控和优化示例</Title>
      <Paragraph>
        这个示例展示了如何使用真实的性能监控器和场景优化器模块。
        点击"开始监控"来启动性能监控，点击"分析优化"来分析和优化场景。
      </Paragraph>

      {/* 控制面板 */}
      <Card title="控制面板" style={{ marginBottom: '16px' }}>
        <Space>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={startMonitoring}
            disabled={isMonitoring}
          >
            开始监控
          </Button>
          <Button
            icon={<PauseCircleOutlined />}
            onClick={stopMonitoring}
            disabled={!isMonitoring}
          >
            停止监控
          </Button>
          <Button
            icon={<ThunderboltOutlined />}
            onClick={analyzeAndOptimizeScene}
            loading={isOptimizing}
          >
            分析优化
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={resetData}
          >
            重置数据
          </Button>
        </Space>
      </Card>

      {/* 性能监控面板 */}
      {performanceData && (
        <Card title="性能监控数据" style={{ marginBottom: '16px' }}>
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Statistic
                title="帧率 (FPS)"
                value={getMetricValue(PerformanceMetricType.FPS)}
                precision={1}
                suffix="fps"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="内存使用"
                value={getMetricValue(PerformanceMetricType.MEMORY_USAGE)}
                precision={1}
                suffix="MB"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="绘制调用"
                value={getMetricValue(PerformanceMetricType.DRAW_CALLS)}
                precision={0}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="三角形数量"
                value={getMetricValue(PerformanceMetricType.TRIANGLES)}
                precision={0}
              />
            </Col>
          </Row>

          <div style={{ marginTop: '16px' }}>
            <Title level={4}>总体性能评分</Title>
            <Progress
              type="circle"
              percent={Math.round(performanceData.overallScore)}
              status={getPerformanceStatus(performanceData.overallScore)}
              size={120}
            />
          </div>

          {performanceData.bottlenecks.length > 0 && (
            <Alert
              message="检测到性能瓶颈"
              description={performanceData.bottlenecks.join(', ')}
              type="warning"
              style={{ marginTop: '16px' }}
            />
          )}

          {performanceData.suggestions.length > 0 && (
            <Alert
              message="优化建议"
              description={performanceData.suggestions.join(', ')}
              type="info"
              style={{ marginTop: '16px' }}
            />
          )}
        </Card>
      )}

      {/* 场景优化结果 */}
      {optimizationResult && (
        <Card title="场景优化结果">
          {optimizationResult.error ? (
            <Alert
              message="优化失败"
              description={optimizationResult.error}
              type="error"
            />
          ) : (
            <>
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Statistic
                    title="场景评分"
                    value={optimizationResult.overallScore}
                    precision={1}
                    suffix="/100"
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="优化建议数量"
                    value={optimizationResult.suggestions?.length || 0}
                    suffix="个"
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="优化状态"
                    value={optimizationResult.optimizeSuccess ? "成功" : "未执行"}
                    valueStyle={{ 
                      color: optimizationResult.optimizeSuccess ? '#3f8600' : '#cf1322' 
                    }}
                  />
                </Col>
              </Row>

              {optimizationResult.suggestions && optimizationResult.suggestions.length > 0 && (
                <div style={{ marginTop: '16px' }}>
                  <Title level={4}>优化建议</Title>
                  {optimizationResult.suggestions.map((suggestion: any, index: number) => (
                    <Alert
                      key={index}
                      message={suggestion.title}
                      description={suggestion.description}
                      type={suggestion.severity > 0.7 ? 'error' : suggestion.severity > 0.4 ? 'warning' : 'info'}
                      style={{ marginBottom: '8px' }}
                    />
                  ))}
                </div>
              )}

              {optimizationResult.optimizeSuccess && (
                <Alert
                  message="优化完成"
                  description="场景优化已成功执行，性能应该有所提升。"
                  type="success"
                  style={{ marginTop: '16px' }}
                />
              )}
            </>
          )}
        </Card>
      )}
    </div>
  );
};

export default PerformanceMonitoringExample;
