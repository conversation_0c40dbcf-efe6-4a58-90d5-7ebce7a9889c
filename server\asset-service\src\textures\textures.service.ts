/**
 * 纹理服务
 */
import { Injectable } from '@nestjs/common';
import { AssetsService } from '../assets/assets.service';
import { AssetType } from '../assets/enums/asset.enums';

@Injectable()
export class TexturesService {
  constructor(private readonly assetsService: AssetsService) {}

  /**
   * 查找所有纹理
   */
  async findAll(userId: string, projectId?: string, tags?: string[]) {
    return this.assetsService.findAll(userId, projectId, AssetType.TEXTURE, tags);
  }

  /**
   * 搜索纹理
   */
  async search(query: string, userId: string) {
    return this.assetsService.search(query, userId, AssetType.TEXTURE);
  }
}
