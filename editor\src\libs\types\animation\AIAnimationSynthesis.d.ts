import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { AnimationClip } from './AnimationClip';
import { FacialAnimationClip } from './FacialAnimationEditor';
import { IAIAnimationModel } from './ai/index';
/**
 * AI动画合成配置
 */
export interface AIAnimationSynthesisConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 模型URL */
    modelUrl?: string;
    /** 是否使用本地模型 */
    useLocalModel?: boolean;
    /** 批处理大小 */
    batchSize?: number;
    /** 采样率 */
    sampleRate?: number;
    /** 最大上下文长度 */
    maxContextLength?: number;
}
/**
 * 动画生成请求
 */
export interface AnimationGenerationRequest {
    /** 请求ID */
    id: string;
    /** 提示文本 */
    prompt: string;
    /** 动画类型 */
    type: 'body' | 'facial' | 'combined';
    /** 持续时间（秒） */
    duration: number;
    /** 是否循环 */
    loop: boolean;
    /** 参考动画 */
    referenceClip?: AnimationClip | FacialAnimationClip;
    /** 风格 */
    style?: string;
    /** 强度 */
    intensity?: number;
    /** 随机种子 */
    seed?: number;
    /** 用户数据 */
    userData?: any;
}
/**
 * 动画生成结果
 */
export interface AnimationGenerationResult {
    /** 请求ID */
    id: string;
    /** 是否成功 */
    success: boolean;
    /** 错误信息 */
    error?: string;
    /** 生成的动画片段 */
    clip?: AnimationClip | FacialAnimationClip;
    /** 生成时间（毫秒） */
    generationTime?: number;
    /** 用户数据 */
    userData?: any;
}
/**
 * AI动画合成组件
 */
export declare class AIAnimationSynthesisComponent extends Component {
    /** 组件类型 */
    static readonly type = "AIAnimationSynthesis";
    /** 请求队列 */
    private requestQueue;
    /** 结果缓存 */
    private resultCache;
    /** 是否正在处理 */
    private isProcessing;
    /** 事件发射器 */
    private eventEmitter;
    /** 模型是否已加载 */
    private modelLoaded;
    /** AI模型 */
    private aiModel;
    /**
     * 构造函数
     * @param entity 实体
     */
    constructor(entity: Entity);
    /**
     * 请求生成动画
     * @param request 生成请求
     * @returns 请求ID
     */
    requestAnimation(request: Omit<AnimationGenerationRequest, 'id'>): string;
    /**
     * 取消请求
     * @param id 请求ID
     * @returns 是否成功取消
     */
    cancelRequest(id: string): boolean;
    /**
     * 获取请求结果
     * @param id 请求ID
     * @returns 生成结果，如果不存在则返回null
     */
    getResult(id: string): AnimationGenerationResult | null;
    /**
     * 清除结果缓存
     * @param id 请求ID，如果不提供则清除所有缓存
     */
    clearCache(id?: string): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 处理请求队列
     */
    private processQueue;
    /**
     * 设置AI模型
     * @param model AI模型
     */
    setAIModel(model: IAIAnimationModel): void;
    /**
     * 生成身体动画
     * @param request 生成请求
     * @returns 生成的动画片段
     */
    private generateBodyAnimation;
    /**
     * 生成面部动画
     * @param request 生成请求
     * @returns 生成的面部动画片段
     */
    private generateFacialAnimation;
    /**
     * 生成组合动画
     * @param request 生成请求
     * @returns 生成的动画片段
     */
    private generateCombinedAnimation;
    /**
     * 更新组件
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
}
