import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
/**
 * 交互类型枚举
 */
export declare enum InteractionType {
    /** 推动 */
    PUSH = "push",
    /** 拉动 */
    PULL = "pull",
    /** 举起 */
    LIFT = "lift",
    /** 投掷 */
    THROW = "throw",
    /** 攀爬 */
    CLIMB = "climb",
    /** 悬挂 */
    HANG = "hang"
}
/**
 * 物理交互组件配置
 */
export interface PhysicsInteractionComponentConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 最大交互距离 */
    maxInteractionDistance?: number;
    /** 交互力大小 */
    interactionForce?: number;
    /** 交互力衰减系数 */
    interactionForceDamping?: number;
    /** 允许的交互类型 */
    allowedInteractionTypes?: InteractionType[];
    /** 是否可以被推动 */
    canBePushed?: boolean;
    /** 是否可以被拉动 */
    canBePulled?: boolean;
    /** 是否可以被举起 */
    canBeLifted?: boolean;
    /** 是否可以被投掷 */
    canBeThrown?: boolean;
    /** 是否可以被攀爬 */
    canBeClimbed?: boolean;
    /** 是否可以被悬挂 */
    canBeHanged?: boolean;
    /** 交互开始回调 */
    onInteractionStart?: (entity: Entity, interactor: Entity, type: InteractionType) => void;
    /** 交互结束回调 */
    onInteractionEnd?: (entity: Entity, interactor: Entity, type: InteractionType) => void;
}
/**
 * 物理交互组件
 * 用于实现角色与物理对象的交互
 */
export declare class PhysicsInteractionComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 是否启用 */
    private _enabled;
    /** 最大交互距离 */
    private _maxInteractionDistance;
    /** 交互力大小 */
    private _interactionForce;
    /** 交互力衰减系数 */
    private _interactionForceDamping;
    /** 允许的交互类型 */
    private _allowedInteractionTypes;
    /** 是否可以被推动 */
    private _canBePushed;
    /** 是否可以被拉动 */
    private _canBePulled;
    /** 是否可以被举起 */
    private _canBeLifted;
    /** 是否可以被投掷 */
    private _canBeThrown;
    /** 是否可以被攀爬 */
    private _canBeClimbed;
    /** 是否可以被悬挂 */
    private _canBeHanged;
    /** 当前交互者 */
    private _currentInteractors;
    /** 事件发射器 */
    private _eventEmitter;
    /** 交互开始回调 */
    private _onInteractionStart;
    /** 交互结束回调 */
    private _onInteractionEnd;
    /** 是否已初始化 */
    private _initialized;
    /** 是否已销毁 */
    private _destroyed;
    /**
     * 创建物理交互组件
     * @param config 组件配置
     */
    constructor(config?: PhysicsInteractionComponentConfig);
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新交互
     * @param interactor 交互者
     * @param interactionType 交互类型
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateInteraction;
    /**
     * 处理推动交互
     * @param interactor 交互者
     * @param physicsBody 物理体组件
     * @param interactorTransform 交互者变换组件
     * @param entityTransform 实体变换组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private handlePushInteraction;
    /**
     * 处理拉动交互
     * @param interactor 交互者
     * @param physicsBody 物理体组件
     * @param interactorTransform 交互者变换组件
     * @param entityTransform 实体变换组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private handlePullInteraction;
    /**
     * 处理举起交互
     * @param interactor 交互者
     * @param physicsBody 物理体组件
     * @param interactorTransform 交互者变换组件
     * @param entityTransform 实体变换组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private handleLiftInteraction;
    /**
     * 处理投掷交互
     * @param interactor 交互者
     * @param physicsBody 物理体组件
     * @param interactorTransform 交互者变换组件
     * @param entityTransform 实体变换组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private handleThrowInteraction;
    /**
     * 处理攀爬交互
     * @param interactor 交互者
     * @param physicsBody 物理体组件
     * @param interactorTransform 交互者变换组件
     * @param entityTransform 实体变换组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private handleClimbInteraction;
    /**
     * 处理悬挂交互
     * @param interactor 交互者
     * @param physicsBody 物理体组件
     * @param interactorTransform 交互者变换组件
     * @param entityTransform 实体变换组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private handleHangInteraction;
    /**
     * 开始交互
     * @param interactor 交互者
     * @param interactionType 交互类型
     * @returns 是否成功开始交互
     */
    startInteraction(interactor: Entity, interactionType: InteractionType): boolean;
    /**
     * 结束交互
     * @param interactor 交互者
     * @returns 是否成功结束交互
     */
    endInteraction(interactor: Entity): boolean;
    /**
     * 结束所有交互
     */
    endAllInteractions(): void;
    /**
     * 检查是否正在与指定实体交互
     * @param interactor 交互者
     * @returns 是否正在交互
     */
    isInteractingWith(interactor: Entity): boolean;
    /**
     * 获取与指定实体的交互类型
     * @param interactor 交互者
     * @returns 交互类型，如果没有则返回null
     */
    getInteractionTypeWith(interactor: Entity): InteractionType | null;
    /**
     * 获取当前所有交互者
     * @returns 交互者数组
     */
    getCurrentInteractors(): Entity[];
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 销毁组件
     */
    destroy(): void;
    /**
     * 获取是否启用
     */
    getEnabled(): boolean;
    /**
     * 设置是否启用
     */
    setEnabled(value: boolean): void;
    /**
     * 获取最大交互距离
     */
    get maxInteractionDistance(): number;
    /**
     * 设置最大交互距离
     */
    set maxInteractionDistance(value: number);
    /**
     * 获取交互力大小
     */
    get interactionForce(): number;
    /**
     * 设置交互力大小
     */
    set interactionForce(value: number);
    /**
     * 获取交互力衰减系数
     */
    get interactionForceDamping(): number;
    /**
     * 设置交互力衰减系数
     */
    set interactionForceDamping(value: number);
}
