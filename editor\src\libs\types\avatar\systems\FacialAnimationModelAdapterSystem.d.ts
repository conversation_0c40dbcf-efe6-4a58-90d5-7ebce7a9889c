/**
 * 面部动画模型适配器系统
 * 用于管理面部动画模型适配器
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { FacialAnimationModelAdapterComponent, FacialAnimationModelType } from '../adapters/FacialAnimationModelAdapter';
/**
 * 面部动画模型适配器系统配置
 */
export interface FacialAnimationModelAdapterSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 默认模型类型 */
    defaultModelType?: FacialAnimationModelType;
    /** 是否自动检测混合形状 */
    autoDetectBlendShapes?: boolean;
}
/**
 * 面部动画模型适配器系统
 */
export declare class FacialAnimationModelAdapterSystem extends System {
    /** 系统名称 */
    static readonly NAME = "FacialAnimationModelAdapterSystem";
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 模型适配器组件映射 */
    private adapters;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: FacialAnimationModelAdapterSystemConfig);
    /**
     * 创建模型适配器
     * @param entity 实体
     * @param mesh 骨骼网格
     * @param modelType 模型类型
     * @returns 是否成功创建
     */
    createModelAdapter(entity: Entity, mesh: THREE.SkinnedMesh, modelType?: FacialAnimationModelType): boolean;
    /**
     * 获取模型适配器
     * @param entity 实体
     * @returns 模型适配器
     */
    getModelAdapter(entity: Entity): FacialAnimationModelAdapterComponent | null;
    /**
     * 移除模型适配器
     * @param entity 实体
     */
    removeModelAdapter(entity: Entity): void;
    /**
     * 自动检测模型类型
     * @param mesh 骨骼网格
     * @returns 模型类型
     */
    detectModelType(mesh: THREE.SkinnedMesh): FacialAnimationModelType;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
}
