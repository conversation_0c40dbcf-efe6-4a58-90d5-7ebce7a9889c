/**
 * 事件发射器类
 * 提供事件订阅和发布功能
 */
export type EventCallback = (...args: any[]) => void;

export class EventEmitter {
  /** 事件监听器映射 */
  private _listeners: Map<string, EventCallback[]> = new Map();

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   * @returns 当前实例，用于链式调用
   */
  public on(event: string, callback: EventCallback): this {
    if (!this._listeners.has(event)) {
      this._listeners.set(event, []);
    }

    this._listeners.get(event)!.push(callback);
    return this;
  }

  /**
   * 添加一次性事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   * @returns 当前实例，用于链式调用
   */
  public once(event: string, callback: EventCallback): this {
    const onceCallback = (...args: any[]) => {
      this.off(event, onceCallback);
      callback(...args);
    };

    return this.on(event, onceCallback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数（可选，如果不提供则移除该事件的所有监听器）
   * @returns 当前实例，用于链式调用
   */
  public off(event: string, callback?: EventCallback): this {
    if (!this._listeners.has(event)) {
      return this;
    }

    if (!callback) {
      // 移除该事件的所有监听器
      this._listeners.delete(event);
      return this;
    }

    // 移除特定的回调函数
    const callbacks = this._listeners.get(event)!;
    const index = callbacks.indexOf(callback);

    if (index !== -1) {
      callbacks.splice(index, 1);

      // 如果没有监听器了，则删除该事件
      if (callbacks.length === 0) {
        this._listeners.delete(event);
      }
    }

    return this;
  }

  /**
   * 发射事件
   * @param event 事件名称
   * @param args 事件参数
   * @returns 是否有监听器处理了该事件
   */
  public emit(event: string, ...args: any[]): boolean {
    if (!this._listeners.has(event)) {
      return false;
    }

    const callbacks = this._listeners.get(event)!.slice();

    for (const callback of callbacks) {
      try {
        callback(...args);
      } catch (error) {
        console.error(`事件处理器错误: ${error}`);
      }
    }

    return callbacks.length > 0;
  }

  /**
   * 获取事件监听器数量
   * @param event 事件名称（可选，如果不提供则返回所有事件的监听器总数）
   * @returns 监听器数量
   */
  public listenerCount(event?: string): number {
    if (event) {
      return this._listeners.has(event) ? this._listeners.get(event)!.length : 0;
    }

    let count = 0;
    this._listeners.forEach((callbacks) => {
      count += callbacks.length;
    });

    return count;
  }

  /**
   * 获取事件名称列表
   * @returns 事件名称数组
   */
  public eventNames(): string[] {
    return Array.from(this._listeners.keys());
  }

  /**
   * 获取事件监听器列表
   * @param event 事件名称
   * @returns 监听器数组
   */
  public listeners(event: string): EventCallback[] {
    return this._listeners.has(event) ? this._listeners.get(event)!.slice() : [];
  }

  /**
   * 移除所有事件监听器
   * @returns 当前实例，用于链式调用
   */
  public removeAllListeners(): this {
    this._listeners.clear();
    return this;
  }
}
