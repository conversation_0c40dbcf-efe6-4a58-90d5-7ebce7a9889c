#!/usr/bin/env pwsh
# 修复微服务依赖问题的脚本

param(
    [switch]$Help
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔧 $message"
    Write-Host "=" * 60
}

# 显示帮助信息
function Show-Help {
    Write-Host "微服务依赖修复脚本"
    Write-Host ""
    Write-Host "用法: .\fix-microservices-dependencies.ps1"
    Write-Host ""
    Write-Host "此脚本将："
    Write-Host "  1. 为 rag-engine 和 monitoring-service 安装 @nestjs/microservices"
    Write-Host "  2. 重新构建受影响的服务"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -Help           显示此帮助信息"
}

# 安装依赖的函数
function Install-MicroservicesDependency($servicePath, $serviceName) {
    Write-Info "为 $serviceName 安装 @nestjs/microservices 依赖..."
    
    if (-not (Test-Path $servicePath)) {
        Write-Error "服务目录不存在: $servicePath"
        return $false
    }
    
    Push-Location $servicePath
    try {
        # 检查是否已经安装
        $packageJson = Get-Content "package.json" | ConvertFrom-Json
        if ($packageJson.dependencies."@nestjs/microservices") {
            Write-Success "$serviceName 已经安装了 @nestjs/microservices"
            return $true
        }
        
        # 安装依赖
        Write-Info "正在安装 @nestjs/microservices..."
        npm install @nestjs/microservices@^10.0.0
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "$serviceName 依赖安装成功"
            return $true
        } else {
            Write-Error "$serviceName 依赖安装失败"
            return $false
        }
    } catch {
        Write-Error "安装 $serviceName 依赖时出错: $_"
        return $false
    } finally {
        Pop-Location
    }
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Header "修复微服务依赖问题"
    
    # 检查是否在正确的目录
    if (-not (Test-Path "server")) {
        Write-Error "server目录不存在，请确保在项目根目录运行此脚本"
        exit 1
    }
    
    $services = @(
        @{
            Path = "server/rag-engine"
            Name = "rag-engine"
        },
        @{
            Path = "server/monitoring-service"
            Name = "monitoring-service"
        }
    )
    
    $allSuccess = $true
    
    foreach ($service in $services) {
        $success = Install-MicroservicesDependency $service.Path $service.Name
        if (-not $success) {
            $allSuccess = $false
        }
    }
    
    if ($allSuccess) {
        Write-Success "🎉 所有依赖安装完成！"
        Write-Info "现在可以重新运行构建命令："
        Write-Info "  .\start-windows.ps1 -Build"
    } else {
        Write-Error "❌ 部分依赖安装失败，请检查错误信息"
        exit 1
    }
}

# 运行主函数
Main
