# DL引擎 Ingress 配置
# 提供统一的外部访问入口
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dl-engine-ingress
  namespace: dl-engine
  labels:
    app: dl-engine
    component: ingress
  annotations:
    # Nginx Ingress 配置
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-buffering: "off"
    
    # WebSocket 支持（用于协作服务）
    nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    
    # 速率限制
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # CORS 配置
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    
    # 缓存配置
    nginx.ingress.kubernetes.io/server-snippet: |
      location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
      }
      
      location /api/ {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
      }
spec:
  rules:
  # 主域名配置
  - host: dl-engine.example.com  # 请替换为您的实际域名
    http:
      paths:
      # API 路由
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 3000
      
      # 协作服务 WebSocket 路由
      - path: /collaboration
        pathType: Prefix
        backend:
          service:
            name: collaboration-load-balancer
            port:
              number: 3007
      
      # MinIO API 路由
      - path: /minio
        pathType: Prefix
        backend:
          service:
            name: minio
            port:
              number: 9000
      
      # MinIO 控制台路由
      - path: /minio-console
        pathType: Prefix
        backend:
          service:
            name: minio
            port:
              number: 9001
      
      # 游戏服务器路由
      - path: /game
        pathType: Prefix
        backend:
          service:
            name: game-server
            port:
              number: 3030
      
      # 前端编辑器路由（默认路由，放在最后）
      - path: /
        pathType: Prefix
        backend:
          service:
            name: editor
            port:
              number: 80
  
  # 监控域名配置（可选）
  - host: monitoring.dl-engine.example.com
    http:
      paths:
      # Grafana 监控面板
      - path: /
        pathType: Prefix
        backend:
          service:
            name: prometheus-grafana
            port:
              number: 80
  
  # 开发环境域名配置（可选）
  - host: dev.dl-engine.example.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 3000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: editor
            port:
              number: 80

---
# HTTPS/TLS 配置（生产环境推荐）
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dl-engine-ingress-tls
  namespace: dl-engine
  labels:
    app: dl-engine
    component: ingress-tls
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    
    # 自动获取 Let's Encrypt 证书
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
    # 强制 HTTPS
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # HSTS 配置
    nginx.ingress.kubernetes.io/server-snippet: |
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
spec:
  tls:
  - hosts:
    - dl-engine.example.com
    - monitoring.dl-engine.example.com
    - dev.dl-engine.example.com
    secretName: dl-engine-tls-secret
  rules:
  - host: dl-engine.example.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 3000
      - path: /collaboration
        pathType: Prefix
        backend:
          service:
            name: collaboration-load-balancer
            port:
              number: 3007
      - path: /minio
        pathType: Prefix
        backend:
          service:
            name: minio
            port:
              number: 9000
      - path: /minio-console
        pathType: Prefix
        backend:
          service:
            name: minio
            port:
              number: 9001
      - path: /game
        pathType: Prefix
        backend:
          service:
            name: game-server
            port:
              number: 3030
      - path: /
        pathType: Prefix
        backend:
          service:
            name: editor
            port:
              number: 80

---
# 网络策略 - 控制 Ingress 流量
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ingress-netpol
  namespace: dl-engine
  labels:
    app: dl-engine
    component: ingress-policy
spec:
  podSelector: {}  # 应用到命名空间中的所有 Pod
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx  # Ingress 控制器命名空间
  - from:
    - namespaceSelector:
        matchLabels:
          name: dl-engine-monitoring  # 监控命名空间
  - from: []  # 允许集群内部通信
    ports:
    - protocol: TCP
      port: 3000  # API Gateway
    - protocol: TCP
      port: 80    # Editor
    - protocol: TCP
      port: 3007  # Collaboration
    - protocol: TCP
      port: 9000  # MinIO API
    - protocol: TCP
      port: 9001  # MinIO Console
    - protocol: TCP
      port: 3030  # Game Server
