/**
 * 动画混合系统集成测试
 * 测试动画混合系统在实际场景中的表现
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import { World } from '../../../src/core/World';
import { Entity } from '../../../src/core/Entity';
import { AnimationSystem } from '../../../src/animation/AnimationSystem';
import { Animator } from '../../../src/animation/Animator';
import { AnimationBlender } from '../../../src/animation/AnimationBlender';
import { BlendMode } from '../../../src/animation/BlendMode';
import { AnimationMask } from '../../../src/animation/AnimationMask';
import { SubClip } from '../../../src/animation/SubClip';
import { BlendSpace1D } from '../../../src/animation/BlendSpace1D';
import { BlendSpace2D } from '../../../src/animation/BlendSpace2D';
import { AnimationStateMachine } from '../../../src/animation/AnimationStateMachine';
import { PhysicsSystem } from '../../../src/physics/PhysicsSystem';
import { InputSystem } from '../../../src/input/InputSystem';
import { BoneGroupType } from '../../../src/animation/BoneGroupType';

describe('动画混合系统集成测试', () => {
  let world: World;
  let entity: Entity;
  let animationSystem: AnimationSystem;
  let physicsSystem: PhysicsSystem;
  let inputSystem: InputSystem;
  let animator: Animator;
  let blender: AnimationBlender;
  let scene: THREE.Scene;
  let camera: THREE.PerspectiveCamera;
  let renderer: THREE.WebGLRenderer;
  let clock: THREE.Clock;
  let model: THREE.Group;
  let skeleton: THREE.SkeletonHelper;
  let mixer: THREE.AnimationMixer;
  let animations: THREE.AnimationClip[];

  // 在每个测试前设置场景
  beforeEach(() => {
    // 创建世界
    world = new World();
    
    // 添加系统
    animationSystem = new AnimationSystem(world);
    physicsSystem = new PhysicsSystem(world);
    inputSystem = new InputSystem(world);
    
    world.addSystem(animationSystem);
    world.addSystem(physicsSystem);
    world.addSystem(inputSystem);
    
    // 创建场景
    scene = new THREE.Scene();
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    renderer = new THREE.WebGLRenderer({ antialias: true });
    clock = new THREE.Clock();
    
    // 创建实体
    entity = new Entity('testCharacter');
    world.addEntity(entity);
    
    // 创建模型和骨骼
    model = new THREE.Group();
    skeleton = createTestSkeleton();
    model.add(skeleton.bones[0]); // 添加根骨骼到模型
    
    // 创建动画
    animations = createTestAnimations(skeleton);
    
    // 创建混合器
    mixer = new THREE.AnimationMixer(model);
    
    // 创建动画控制器
    animator = new Animator(mixer, animations);
    entity.addComponent('animator', animator);
    
    // 创建混合器
    blender = new AnimationBlender(animator);
    entity.addComponent('blender', blender);
    
    // 启动世界
    world.start();
  });

  // 在每个测试后清理场景
  afterEach(() => {
    world.stop();
    scene.clear();
    renderer.dispose();
  });

  // 测试基本混合功能
  it('应该正确混合两个动画', () => {
    // 添加混合层
    const idleLayerIndex = blender.addLayer('idle', 1.0, BlendMode.OVERRIDE);
    const walkLayerIndex = blender.addLayer('walk', 0.0, BlendMode.OVERRIDE);
    
    // 更新混合器
    blender.update(0.1);
    
    // 验证初始状态
    expect(animator.getAction('idle')?.weight).toBe(1.0);
    expect(animator.getAction('walk')?.weight).toBe(0.0);
    
    // 设置混合权重
    blender.setLayerWeight(idleLayerIndex, 0.3, 0.5);
    blender.setLayerWeight(walkLayerIndex, 0.7, 0.5);
    
    // 模拟时间流逝
    for (let i = 0; i < 10; i++) {
      blender.update(0.1);
    }
    
    // 验证最终状态
    expect(animator.getAction('idle')?.weight).toBeCloseTo(0.3, 2);
    expect(animator.getAction('walk')?.weight).toBeCloseTo(0.7, 2);
  });

  // 测试遮罩混合功能
  it('应该正确应用动画遮罩', () => {
    // 创建上半身遮罩
    const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);
    
    // 创建下半身遮罩
    const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);
    
    // 添加混合层
    blender.addLayer('walk', 1.0, BlendMode.OVERRIDE, 1.0, lowerBodyMask);
    blender.addLayer('wave', 1.0, BlendMode.OVERRIDE, 1.0, upperBodyMask);
    
    // 更新混合器
    blender.update(0.1);
    
    // 验证动作存在
    expect(animator.getAction('walk')).toBeDefined();
    expect(animator.getAction('wave')).toBeDefined();
    
    // 验证遮罩应用
    // 这里需要检查骨骼的实际影响，但由于是模拟测试，我们只能检查动作是否创建
    expect(animator.getAction('walk')?.getMixer()).toBe(mixer);
    expect(animator.getAction('wave')?.getMixer()).toBe(mixer);
  });

  // 测试子片段功能
  it('应该正确创建和使用子片段', () => {
    // 创建子片段
    const walkLoop = blender.createSubClip('walkLoop', 'walk', 0.2, 0.8, true);
    
    // 添加子片段层
    const subClipLayerIndex = blender.addLayer('walkLoop', 1.0, BlendMode.OVERRIDE);
    
    // 更新混合器
    blender.update(0.1);
    
    // 验证子片段动作存在
    expect(animator.getAction('walkLoop')).toBeDefined();
    
    // 验证子片段权重
    expect(animator.getAction('walkLoop')?.weight).toBe(1.0);
  });

  // 测试混合空间功能
  it('应该正确使用1D混合空间', () => {
    // 创建1D混合空间
    const blendSpace = new BlendSpace1D({
      minValue: 0,
      maxValue: 2
    });
    
    // 添加节点
    blendSpace.addNode('idle', 0);
    blendSpace.addNode('walk', 1);
    blendSpace.addNode('run', 2);
    
    // 设置位置
    blendSpace.setPosition(0.5);
    
    // 更新混合空间
    blendSpace.update();
    
    // 获取活跃节点
    const activeNodes = blendSpace.getActiveNodes();
    
    // 验证活跃节点
    expect(activeNodes.length).toBe(2);
    expect(activeNodes[0].name).toBe('idle');
    expect(activeNodes[1].name).toBe('walk');
    expect(activeNodes[0].weight).toBeCloseTo(0.5, 2);
    expect(activeNodes[1].weight).toBeCloseTo(0.5, 2);
  });

  // 创建测试骨骼
  function createTestSkeleton(): THREE.SkeletonHelper {
    // 创建骨骼
    const bones: THREE.Bone[] = [];
    
    // 创建根骨骼
    const root = new THREE.Bone();
    root.name = 'root';
    bones.push(root);
    
    // 创建脊柱骨骼
    const spine = new THREE.Bone();
    spine.name = 'spine';
    spine.position.y = 1;
    root.add(spine);
    bones.push(spine);
    
    // 创建头部骨骼
    const head = new THREE.Bone();
    head.name = 'head';
    head.position.y = 0.5;
    spine.add(head);
    bones.push(head);
    
    // 创建左臂骨骼
    const leftArm = new THREE.Bone();
    leftArm.name = 'leftArm';
    leftArm.position.x = 0.5;
    spine.add(leftArm);
    bones.push(leftArm);
    
    // 创建右臂骨骼
    const rightArm = new THREE.Bone();
    rightArm.name = 'rightArm';
    rightArm.position.x = -0.5;
    spine.add(rightArm);
    bones.push(rightArm);
    
    // 创建左腿骨骼
    const leftLeg = new THREE.Bone();
    leftLeg.name = 'leftLeg';
    leftLeg.position.set(0.2, -1, 0);
    root.add(leftLeg);
    bones.push(leftLeg);
    
    // 创建右腿骨骼
    const rightLeg = new THREE.Bone();
    rightLeg.name = 'rightLeg';
    rightLeg.position.set(-0.2, -1, 0);
    root.add(rightLeg);
    bones.push(rightLeg);
    
    // 创建骨骼助手
    return new THREE.SkeletonHelper(root);
  }

  // 创建测试动画
  function createTestAnimations(skeleton: THREE.SkeletonHelper): THREE.AnimationClip[] {
    const animations: THREE.AnimationClip[] = [];
    
    // 创建空闲动画
    const idleClip = new THREE.AnimationClip('idle', 1, [
      // 轻微呼吸动画
      new THREE.QuaternionKeyframeTrack(
        'spine.quaternion',
        [0, 0.5, 1],
        [
          0, 0, 0, 1, // 初始姿势
          0.01, 0, 0, 0.9999, // 轻微前倾
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(idleClip);
    
    // 创建行走动画
    const walkClip = new THREE.AnimationClip('walk', 1, [
      // 左腿动画
      new THREE.QuaternionKeyframeTrack(
        'leftLeg.quaternion',
        [0, 0.5, 1],
        [
          0, 0, 0, 1, // 初始姿势
          0.2, 0, 0, 0.98, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      ),
      // 右腿动画
      new THREE.QuaternionKeyframeTrack(
        'rightLeg.quaternion',
        [0, 0.5, 1],
        [
          0, 0, 0, 1, // 初始姿势
          -0.2, 0, 0, 0.98, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(walkClip);
    
    // 创建跑步动画
    const runClip = new THREE.AnimationClip('run', 0.6, [
      // 左腿动画
      new THREE.QuaternionKeyframeTrack(
        'leftLeg.quaternion',
        [0, 0.3, 0.6],
        [
          0, 0, 0, 1, // 初始姿势
          0.4, 0, 0, 0.92, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      ),
      // 右腿动画
      new THREE.QuaternionKeyframeTrack(
        'rightLeg.quaternion',
        [0, 0.3, 0.6],
        [
          0, 0, 0, 1, // 初始姿势
          -0.4, 0, 0, 0.92, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(runClip);
    
    // 创建挥手动画
    const waveClip = new THREE.AnimationClip('wave', 1.5, [
      // 右臂动画
      new THREE.QuaternionKeyframeTrack(
        'rightArm.quaternion',
        [0, 0.5, 1, 1.5],
        [
          0, 0, 0, 1, // 初始姿势
          0, 0, 0.4, 0.92, // 抬起手臂
          0, 0, -0.4, 0.92, // 挥动
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(waveClip);
    
    return animations;
  }
});
