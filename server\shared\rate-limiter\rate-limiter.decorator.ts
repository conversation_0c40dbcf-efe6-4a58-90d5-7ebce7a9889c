/**
 * 限流器装饰器
 */
import { SetMetadata, applyDecorators } from '@nestjs/common';
import { RateLimiterOptions } from './rate-limiter.interface';

/**
 * 限流器元数据键
 */
export const RATE_LIMITER_METADATA = 'rate_limiter_metadata';

/**
 * 限流器装饰器元数据
 */
export interface RateLimiterMetadata {
  /**
   * 限流器名称
   */
  name: string;
  
  /**
   * 限流器配置
   */
  options?: Partial<RateLimiterOptions>;
  
  /**
   * 键生成器函数
   * 用于生成限流键，默认使用IP地址
   */
  keyGenerator?: (request: any) => string;
}

/**
 * 限流器装饰器
 * 用于在控制器或方法级别应用限流器
 * @param name 限流器名称
 * @param options 限流器配置
 * @param keyGenerator 键生成器函数
 */
export function UseRateLimiter(
  name: string,
  options?: Partial<RateLimiterOptions>,
  keyGenerator?: (request: any) => string,
): MethodDecorator & ClassDecorator {
  return applyDecorators(
    SetMetadata(RATE_LIMITER_METADATA, { name, options, keyGenerator }),
  );
}
