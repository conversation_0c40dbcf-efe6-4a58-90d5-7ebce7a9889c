/**
 * 动作回放器
 * 用于回放录制的角色动作序列
 */
import type { Entity } from '../../core/Entity';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { ActionControlSystem } from '../controllers/ActionControlSystem';
import { AdvancedCharacterController } from '../controllers/AdvancedCharacterController';
import { ActionRecording, RecordedActionEvent, RecordedInputEvent, RecordedTransformEvent   } from './ActionRecorder';

/**
 * 动作回放配置
 */
export interface ActionPlaybackConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 回放速度 (1.0 = 正常速度) */
  playbackSpeed?: number;
  /** 是否回放输入 */
  playbackInput?: boolean;
  /** 是否回放变换 */
  playbackTransform?: boolean;
  /** 是否循环回放 */
  loop?: boolean;
  /** 是否自动开始回放 */
  autoPlay?: boolean;
}

/**
 * 动作回放器
 */
export class ActionPlayback {
  /** 实体 */
  private entity: Entity;
  /** 动作控制系统 */
  private actionControlSystem: ActionControlSystem;
  /** 角色控制器 */
  private characterController: AdvancedCharacterController | null = null;
  /** 配置 */
  private config: ActionPlaybackConfig;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否正在回放 */
  private isPlaying: boolean = false;
  /** 是否暂停 */
  private isPaused: boolean = false;
  /** 当前录制 */
  private recording: ActionRecording | null = null;
  /** 回放开始时间戳 */
  private playbackStartTimestamp: number = 0;
  /** 回放暂停时间戳 */
  private playbackPauseTimestamp: number = 0;
  /** 回放暂停总时长 */
  private totalPauseDuration: number = 0;
  /** 动作事件索引 */
  private actionEventIndex: number = 0;
  /** 输入事件索引 */
  private inputEventIndex: number = 0;
  /** 变换事件索引 */
  private transformEventIndex: number = 0;
  /** 回放完成回调 */
  private onCompleteCallback: (() => void) | null = null;
  /** 动画帧请求ID */
  private animationFrameId: number | null = null;

  /**
   * 构造函数
   * @param entity 实体
   * @param actionControlSystem 动作控制系统
   * @param config 配置
   * @param characterController 角色控制器（可选）
   */
  constructor(
    entity: Entity,
    actionControlSystem: ActionControlSystem,
    config: ActionPlaybackConfig = {},
    characterController?: AdvancedCharacterController
  ) {
    this.entity = entity;
    this.actionControlSystem = actionControlSystem;
    this.config = {
      debug: false,
      playbackSpeed: 1.0,
      playbackInput: true,
      playbackTransform: true,
      loop: false,
      autoPlay: false,
      ...config
    };

    // 设置角色控制器
    this.characterController = characterController || null;
  }

  /**
   * 设置角色控制器
   * @param characterController 角色控制器
   */
  public setCharacterController(characterController: AdvancedCharacterController | null): void {
    this.characterController = characterController;
  }

  /**
   * 获取角色控制器
   * @returns 角色控制器
   */
  public getCharacterController(): AdvancedCharacterController | null {
    return this.characterController;
  }

  /**
   * 加载录制
   * @param recording 录制数据
   * @returns 是否成功加载
   */
  public loadRecording(recording: ActionRecording): boolean {
    if (this.isPlaying) {
      this.stopPlayback();
    }

    this.recording = recording;
    this.resetPlaybackState();

    if (this.config.debug) {
      Debug.log('ActionPlayback', `加载动作录制: ${recording.name}`);
    }

    // 触发录制加载事件
    this.eventEmitter.emit('recordingLoaded', recording);

    // 如果配置为自动播放，则开始回放
    if (this.config.autoPlay) {
      this.startPlayback();
    }

    return true;
  }

  /**
   * 开始回放
   * @param onComplete 完成回调
   * @returns 是否成功开始回放
   */
  public startPlayback(onComplete?: () => void): boolean {
    if (!this.recording) {
      if (this.config.debug) {
        Debug.warn('没有加载录制数据');
      }
      return false;
    }

    if (this.isPlaying && !this.isPaused) {
      if (this.config.debug) {
        Debug.warn('动作回放已经在进行中');
      }
      return false;
    }

    // 如果是从暂停状态恢复
    if (this.isPaused) {
      this.isPaused = false;
      this.totalPauseDuration += (Date.now() - this.playbackPauseTimestamp);

      if (this.config.debug) {
        Debug.log('恢复动作回放');
      }

      // 触发回放恢复事件
      this.eventEmitter.emit('playbackResumed', this.recording);

      // 继续回放循环
      this.startPlaybackLoop();

      return true;
    }

    // 重置回放状态
    this.resetPlaybackState();

    this.isPlaying = true;
    this.playbackStartTimestamp = Date.now();
    this.onCompleteCallback = onComplete || null;

    if (this.config.debug) {
      Debug.log(`开始回放动作: ${this.recording.name}`);
    }

    // 触发回放开始事件
    this.eventEmitter.emit('playbackStart', this.recording);

    // 开始回放循环
    this.startPlaybackLoop();

    return true;
  }

  /**
   * 暂停回放
   * @returns 是否成功暂停
   */
  public pausePlayback(): boolean {
    if (!this.isPlaying || this.isPaused) {
      return false;
    }

    this.isPaused = true;
    this.playbackPauseTimestamp = Date.now();

    // 停止回放循环
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    if (this.config.debug) {
      Debug.log('暂停动作回放');
    }

    // 触发回放暂停事件
    this.eventEmitter.emit('playbackPaused', this.recording);

    return true;
  }

  /**
   * 停止回放
   * @returns 是否成功停止
   */
  public stopPlayback(): boolean {
    if (!this.isPlaying) {
      return false;
    }

    this.isPlaying = false;
    this.isPaused = false;

    // 停止回放循环
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    if (this.config.debug) {
      Debug.log('停止动作回放');
    }

    // 触发回放停止事件
    this.eventEmitter.emit('playbackStop', this.recording);

    return true;
  }

  /**
   * 设置回放速度
   * @param speed 速度 (1.0 = 正常速度)
   */
  public setPlaybackSpeed(speed: number): void {
    this.config.playbackSpeed = Math.max(0.1, speed);

    if (this.config.debug) {
      Debug.log(`设置回放速度: ${speed}`);
    }

    // 触发速度变更事件
    this.eventEmitter.emit('playbackSpeedChanged', speed);
  }

  /**
   * 重置回放状态
   */
  private resetPlaybackState(): void {
    this.isPlaying = false;
    this.isPaused = false;
    this.playbackStartTimestamp = 0;
    this.playbackPauseTimestamp = 0;
    this.totalPauseDuration = 0;
    this.actionEventIndex = 0;
    this.inputEventIndex = 0;
    this.transformEventIndex = 0;
    this.onCompleteCallback = null;

    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * 开始回放循环
   */
  private startPlaybackLoop(): void {
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
    }

    const loop = () => {
      if (!this.isPlaying || this.isPaused || !this.recording) {
        return;
      }

      // 计算当前回放时间
      const currentTime = this.getCurrentPlaybackTime();

      // 处理动作事件
      this.processActionEvents(currentTime);

      // 处理输入事件
      if (this.config.playbackInput) {
        this.processInputEvents(currentTime);
      }

      // 处理变换事件
      if (this.config.playbackTransform) {
        this.processTransformEvents(currentTime);
      }

      // 检查是否完成回放
      if (currentTime >= (this.recording.endTimestamp - this.recording.startTimestamp)) {
        if (this.config.loop) {
          // 如果循环播放，重置状态并重新开始
          this.resetPlaybackIndices();
          this.playbackStartTimestamp = Date.now();
          this.totalPauseDuration = 0;

          // 触发循环事件
          this.eventEmitter.emit('playbackLoop', this.recording);
        } else {
          // 完成回放
          this.completePlayback();
          return;
        }
      }

      // 继续循环
      this.animationFrameId = requestAnimationFrame(loop);
    };

    // 开始循环
    this.animationFrameId = requestAnimationFrame(loop);
  }

  /**
   * 获取当前回放时间
   * @returns 当前回放时间（毫秒）
   */
  private getCurrentPlaybackTime(): number {
    if (!this.isPlaying || !this.recording) return 0;

    // 计算经过的时间，考虑暂停时间和播放速度
    const elapsedTime = Date.now() - this.playbackStartTimestamp - this.totalPauseDuration;
    return elapsedTime * (this.config.playbackSpeed || 1.0);
  }

  /**
   * 处理动作事件
   * @param currentTime 当前回放时间
   */
  private processActionEvents(currentTime: number): void {
    if (!this.recording || this.actionEventIndex >= this.recording.actionEvents.length) {
      return;
    }

    // 处理所有应该在当前时间触发的动作事件
    while (this.actionEventIndex < this.recording.actionEvents.length) {
      const event = this.recording.actionEvents[this.actionEventIndex];
      const eventTime = event.timestamp - this.recording.startTimestamp;

      if (eventTime <= currentTime) {
        this.playActionEvent(event);
        this.actionEventIndex++;
      } else {
        break;
      }
    }
  }

  /**
   * 播放动作事件
   * @param event 动作事件
   */
  private playActionEvent(event: RecordedActionEvent): void {
    if (event.eventType === 'start') {
      // 播放动作
      this.actionControlSystem.playAction(this.entity, event.actionId, event.params);

      if (this.config.debug) {
        Debug.log(`回放动作开始: ${event.actionId}`);
      }
    } else if (event.eventType === 'stop') {
      // 停止动作
      this.actionControlSystem.stopAction(this.entity, event.actionId);

      if (this.config.debug) {
        Debug.log(`回放动作结束: ${event.actionId}`);
      }
    }
  }

  /**
   * 处理输入事件
   * @param currentTime 当前回放时间
   */
  private processInputEvents(currentTime: number): void {
    if (!this.recording || !this.characterController ||
        this.inputEventIndex >= this.recording.inputEvents.length) {
      return;
    }

    // 处理所有应该在当前时间触发的输入事件
    while (this.inputEventIndex < this.recording.inputEvents.length) {
      const event = this.recording.inputEvents[this.inputEventIndex];
      const eventTime = event.timestamp - this.recording.startTimestamp;

      if (eventTime <= currentTime) {
        this.playInputEvent(event);
        this.inputEventIndex++;
      } else {
        break;
      }
    }
  }

  /**
   * 播放输入事件
   * @param event 输入事件
   */
  private playInputEvent(event: RecordedInputEvent): void {
    // 实现输入事件回放
    if (this.config.debug) {
      Debug.log(`回放输入事件: ${event.inputType}`);
    }
  }

  /**
   * 处理变换事件
   * @param currentTime 当前回放时间
   */
  private processTransformEvents(currentTime: number): void {
    if (!this.recording || this.transformEventIndex >= this.recording.transformEvents.length) {
      return;
    }

    // 找到当前时间应该显示的变换
    let targetIndex = this.transformEventIndex;

    // 查找最接近当前时间的两个变换事件进行插值
    while (targetIndex + 1 < this.recording.transformEvents.length) {
      const nextEvent = this.recording.transformEvents[targetIndex + 1];
      const nextEventTime = nextEvent.timestamp - this.recording.startTimestamp;

      if (nextEventTime <= currentTime) {
        targetIndex++;
      } else {
        break;
      }
    }

    // 更新索引
    this.transformEventIndex = targetIndex;

    // 应用变换
    if (targetIndex < this.recording.transformEvents.length) {
      const currentEvent = this.recording.transformEvents[targetIndex];

      // 如果有下一个事件，进行插值
      if (targetIndex + 1 < this.recording.transformEvents.length) {
        const nextEvent = this.recording.transformEvents[targetIndex + 1];
        const currentEventTime = currentEvent.timestamp - this.recording.startTimestamp;
        const nextEventTime = nextEvent.timestamp - this.recording.startTimestamp;

        // 计算插值因子
        const t = (currentTime - currentEventTime) / (nextEventTime - currentEventTime);

        // 应用插值变换
        this.applyInterpolatedTransform(currentEvent, nextEvent, t);
      } else {
        // 直接应用当前变换
        this.applyTransform(currentEvent);
      }
    }
  }

  /**
   * 应用变换
   * @param event 变换事件
   */
  private applyTransform(event: RecordedTransformEvent): void {
    const transform = this.entity.getTransform();
    if (!transform) return;

    // 设置位置
    transform.setPosition(event.position.x, event.position.y, event.position.z);

    // 设置旋转
    transform.setRotationQuaternion(event.rotation.x, event.rotation.y, event.rotation.z, event.rotation.w);
  }

  /**
   * 应用插值变换
   * @param event1 变换事件1
   * @param event2 变换事件2
   * @param t 插值因子 (0-1)
   */
  private applyInterpolatedTransform(event1: RecordedTransformEvent, event2: RecordedTransformEvent, t: number): void {
    const transform = this.entity.getTransform();
    if (!transform) return;

    // 线性插值位置
    const interpolatedX = event1.position.x + (event2.position.x - event1.position.x) * t;
    const interpolatedY = event1.position.y + (event2.position.y - event1.position.y) * t;
    const interpolatedZ = event1.position.z + (event2.position.z - event1.position.z) * t;

    transform.setPosition(interpolatedX, interpolatedY, interpolatedZ);

    // 线性插值旋转四元数
    // 注意：实际实现应该使用四元数的slerp方法，这里简化为线性插值
    const interpolatedQx = event1.rotation.x + (event2.rotation.x - event1.rotation.x) * t;
    const interpolatedQy = event1.rotation.y + (event2.rotation.y - event1.rotation.y) * t;
    const interpolatedQz = event1.rotation.z + (event2.rotation.z - event1.rotation.z) * t;
    const interpolatedQw = event1.rotation.w + (event2.rotation.w - event1.rotation.w) * t;

    // 归一化四元数
    const length = Math.sqrt(
      interpolatedQx * interpolatedQx +
      interpolatedQy * interpolatedQy +
      interpolatedQz * interpolatedQz +
      interpolatedQw * interpolatedQw
    );

    if (length > 0) {
      transform.setRotationQuaternion(
        interpolatedQx / length,
        interpolatedQy / length,
        interpolatedQz / length,
        interpolatedQw / length
      );
    }
  }

  /**
   * 重置回放索引
   */
  private resetPlaybackIndices(): void {
    this.actionEventIndex = 0;
    this.inputEventIndex = 0;
    this.transformEventIndex = 0;
  }

  /**
   * 完成回放
   */
  private completePlayback(): void {
    this.isPlaying = false;

    if (this.config.debug) {
      Debug.log('动作回放完成');
    }

    // 触发回放完成事件
    this.eventEmitter.emit('playbackComplete', this.recording);

    // 调用完成回调
    if (this.onCompleteCallback) {
      this.onCompleteCallback();
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public on(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public off(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }
}
