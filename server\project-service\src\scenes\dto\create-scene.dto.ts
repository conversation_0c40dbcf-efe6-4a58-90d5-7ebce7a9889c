/**
 * 创建场景DTO
 */
import { IsString, IsNotEmpty, IsOptional, IsBoolean, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateSceneDto {
  @ApiProperty({ description: '场景名称', example: '我的3D场景' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '场景描述', required: false, example: '这是一个3D场景' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '缩略图URL', required: false })
  @IsString()
  @IsOptional()
  thumbnailUrl?: string;

  @ApiProperty({ description: '是否为默认场景', required: false, default: false })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;

  @ApiProperty({ description: '元数据', required: false, type: 'object' })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
