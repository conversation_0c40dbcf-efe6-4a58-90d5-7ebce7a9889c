/**
 * 数字人编辑器主界面
 * 整合所有数字人编辑功能的主界面
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Engine } from '../core/Engine';
import { World } from '../core/World';
import { Entity } from '../core/Entity';
import { MinIOStorageService } from '../storage/MinIOStorageService';
import { 
  DigitalHumanSystem,
  DigitalHumanImportSystem,
  BIPIntegrationSystem,
  MultiActionFusionManager,
  PhotoTo3DPipeline
} from '../avatar';
import { MultiActionPanel } from './components/MultiActionPanel';
import { BIPUploadPanel } from './components/BIPUploadPanel';
import { DigitalHumanUploadPanel } from './components/DigitalHumanUploadPanel';

/**
 * 编辑器配置
 */
export interface DigitalHumanEditorConfig {
  /** 容器元素 */
  container: HTMLElement;
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 存储服务配置 */
  storageConfig?: any;
  /** 是否显示工具栏 */
  showToolbar?: boolean;
  /** 是否显示状态栏 */
  showStatusBar?: boolean;
}

/**
 * 编辑器标签页
 */
export enum EditorTab {
  OVERVIEW = 'overview',
  MULTI_ACTION = 'multi_action',
  BIP_UPLOAD = 'bip_upload',
  DIGITAL_HUMAN_UPLOAD = 'digital_human_upload',
  PHOTO_TO_3D = 'photo_to_3d',
  SETTINGS = 'settings'
}

/**
 * 数字人编辑器
 */
export class DigitalHumanEditor extends EventEmitter {
  /** 配置 */
  private config: DigitalHumanEditorConfig;

  /** 容器元素 */
  private container: HTMLElement;

  /** 引擎实例 */
  private engine: Engine;

  /** 世界实例 */
  private world: World;

  /** 存储服务 */
  private storageService: MinIOStorageService;

  /** 系统实例 */
  private systems: {
    digitalHuman?: DigitalHumanSystem;
    import?: DigitalHumanImportSystem;
    bipIntegration?: BIPIntegrationSystem;
    photoTo3D?: PhotoTo3DPipeline;
  } = {};

  /** UI面板 */
  private panels: {
    multiAction?: MultiActionPanel;
    bipUpload?: BIPUploadPanel;
    digitalHumanUpload?: DigitalHumanUploadPanel;
  } = {};

  /** 当前活动标签 */
  private activeTab: EditorTab = EditorTab.OVERVIEW;

  /** 当前选中的数字人 */
  private selectedDigitalHuman: Entity | null = null;

  /** UI元素 */
  private elements: {
    sidebar?: HTMLElement;
    mainContent?: HTMLElement;
    toolbar?: HTMLElement;
    statusBar?: HTMLElement;
  } = {};

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: DigitalHumanEditorConfig) {
    super();

    this.config = {
      debug: false,
      showToolbar: true,
      showStatusBar: true,
      ...config
    };

    this.container = config.container;
    this.initializeEngine();
    this.initializeServices();
    this.initializeUI();
    this.setupEventListeners();
  }

  /**
   * 初始化引擎
   */
  private initializeEngine(): void {
    this.engine = new Engine();
    this.world = this.engine.getWorld();

    // 初始化系统
    this.systems.digitalHuman = new DigitalHumanSystem(this.world, {
      debug: this.config.debug
    });

    this.systems.bipIntegration = new BIPIntegrationSystem(this.world, {
      debug: this.config.debug
    });

    // 添加系统到世界
    this.world.addSystem(this.systems.digitalHuman);
    this.world.addSystem(this.systems.bipIntegration);
  }

  /**
   * 初始化服务
   */
  private async initializeServices(): Promise<void> {
    // 初始化存储服务
    this.storageService = new MinIOStorageService(this.config.storageConfig || {
      provider: 'minio',
      config: {
        endpoint: 'localhost:9000',
        accessKey: 'minioadmin',
        secretKey: 'minioadmin',
        useSSL: false
      },
      debug: this.config.debug
    });

    await this.storageService.initialize();

    // 初始化导入系统
    this.systems.import = new DigitalHumanImportSystem(this.world, this.storageService, {
      debug: this.config.debug
    });

    this.world.addSystem(this.systems.import);

    // 初始化照片到3D管道
    this.systems.photoTo3D = new PhotoTo3DPipeline(this.world, this.storageService, {
      debug: this.config.debug,
      quality: 'high'
    });
  }

  /**
   * 初始化UI
   */
  private initializeUI(): void {
    this.container.className = 'digital-human-editor';
    this.container.innerHTML = `
      ${this.config.showToolbar ? `
      <div class="editor-toolbar" id="editor-toolbar">
        <div class="toolbar-left">
          <button class="btn btn-primary" id="new-digital-human-btn">
            <i class="icon-plus"></i>
            新建数字人
          </button>
          <button class="btn btn-secondary" id="save-btn">
            <i class="icon-save"></i>
            保存
          </button>
          <button class="btn btn-secondary" id="export-btn">
            <i class="icon-export"></i>
            导出
          </button>
        </div>
        <div class="toolbar-center">
          <div class="digital-human-selector">
            <select id="digital-human-select">
              <option value="">选择数字人...</option>
            </select>
          </div>
        </div>
        <div class="toolbar-right">
          <button class="btn btn-outline" id="settings-btn">
            <i class="icon-settings"></i>
            设置
          </button>
        </div>
      </div>
      ` : ''}

      <div class="editor-content">
        <div class="editor-sidebar" id="editor-sidebar">
          <div class="sidebar-tabs">
            <button class="tab-btn active" data-tab="${EditorTab.OVERVIEW}">
              <i class="icon-overview"></i>
              <span>概览</span>
            </button>
            <button class="tab-btn" data-tab="${EditorTab.MULTI_ACTION}">
              <i class="icon-animation"></i>
              <span>动作管理</span>
            </button>
            <button class="tab-btn" data-tab="${EditorTab.BIP_UPLOAD}">
              <i class="icon-skeleton"></i>
              <span>BIP上传</span>
            </button>
            <button class="tab-btn" data-tab="${EditorTab.DIGITAL_HUMAN_UPLOAD}">
              <i class="icon-upload"></i>
              <span>数字人导入</span>
            </button>
            <button class="tab-btn" data-tab="${EditorTab.PHOTO_TO_3D}">
              <i class="icon-camera"></i>
              <span>照片生成</span>
            </button>
          </div>
          <div class="sidebar-content" id="sidebar-content">
            <!-- 侧边栏内容将在这里动态生成 -->
          </div>
        </div>

        <div class="editor-main" id="editor-main">
          <div class="main-content" id="main-content">
            <!-- 主内容区域 -->
            <div class="welcome-screen" id="welcome-screen">
              <div class="welcome-content">
                <i class="icon-digital-human"></i>
                <h2>数字人制作系统</h2>
                <p>选择一个功能开始创建或编辑数字人</p>
                <div class="quick-actions">
                  <button class="btn btn-primary" data-action="photo-to-3d">
                    <i class="icon-camera"></i>
                    从照片生成
                  </button>
                  <button class="btn btn-secondary" data-action="upload-digital-human">
                    <i class="icon-upload"></i>
                    导入数字人
                  </button>
                  <button class="btn btn-secondary" data-action="upload-bip">
                    <i class="icon-skeleton"></i>
                    上传BIP文件
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      ${this.config.showStatusBar ? `
      <div class="editor-status-bar" id="editor-status-bar">
        <div class="status-left">
          <span class="status-text" id="status-text">就绪</span>
        </div>
        <div class="status-right">
          <span class="digital-human-count" id="digital-human-count">0 个数字人</span>
          <span class="memory-usage" id="memory-usage">内存: 0MB</span>
        </div>
      </div>
      ` : ''}
    `;

    // 获取UI元素引用
    this.elements.sidebar = this.container.querySelector('#editor-sidebar') as HTMLElement;
    this.elements.mainContent = this.container.querySelector('#main-content') as HTMLElement;
    this.elements.toolbar = this.container.querySelector('#editor-toolbar') as HTMLElement;
    this.elements.statusBar = this.container.querySelector('#editor-status-bar') as HTMLElement;

    // 初始化面板
    this.initializePanels();
  }

  /**
   * 初始化面板
   */
  private initializePanels(): void {
    // 创建面板容器
    const sidebarContent = this.container.querySelector('#sidebar-content') as HTMLElement;

    // 多动作管理面板
    const multiActionContainer = document.createElement('div');
    multiActionContainer.id = 'multi-action-panel-container';
    multiActionContainer.style.display = 'none';
    sidebarContent.appendChild(multiActionContainer);

    this.panels.multiAction = new MultiActionPanel({
      container: multiActionContainer,
      showDebugInfo: this.config.debug
    });

    // BIP上传面板
    const bipUploadContainer = document.createElement('div');
    bipUploadContainer.id = 'bip-upload-panel-container';
    bipUploadContainer.style.display = 'none';
    sidebarContent.appendChild(bipUploadContainer);

    this.panels.bipUpload = new BIPUploadPanel({
      container: bipUploadContainer,
      allowMultiple: true,
      maxFileSize: 50
    });

    // 数字人上传面板
    const digitalHumanUploadContainer = document.createElement('div');
    digitalHumanUploadContainer.id = 'digital-human-upload-panel-container';
    digitalHumanUploadContainer.style.display = 'none';
    sidebarContent.appendChild(digitalHumanUploadContainer);

    this.panels.digitalHumanUpload = new DigitalHumanUploadPanel({
      container: digitalHumanUploadContainer,
      allowBatch: true,
      maxFileSize: 200
    });

    // 设置面板的系统引用
    if (this.systems.bipIntegration) {
      this.panels.bipUpload.setBIPSystem(this.systems.bipIntegration);
    }

    if (this.systems.import) {
      this.panels.digitalHumanUpload.setImportSystem(this.systems.import);
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 标签切换
    const tabButtons = this.container.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tab = (e.currentTarget as HTMLElement).getAttribute('data-tab') as EditorTab;
        this.switchTab(tab);
      });
    });

    // 快速操作
    const quickActionButtons = this.container.querySelectorAll('[data-action]');
    quickActionButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = (e.currentTarget as HTMLElement).getAttribute('data-action');
        this.handleQuickAction(action!);
      });
    });

    // 工具栏按钮
    if (this.config.showToolbar) {
      const newBtn = this.container.querySelector('#new-digital-human-btn') as HTMLButtonElement;
      const saveBtn = this.container.querySelector('#save-btn') as HTMLButtonElement;
      const exportBtn = this.container.querySelector('#export-btn') as HTMLButtonElement;
      const settingsBtn = this.container.querySelector('#settings-btn') as HTMLButtonElement;

      if (newBtn) newBtn.addEventListener('click', () => this.createNewDigitalHuman());
      if (saveBtn) saveBtn.addEventListener('click', () => this.saveCurrentDigitalHuman());
      if (exportBtn) exportBtn.addEventListener('click', () => this.exportCurrentDigitalHuman());
      if (settingsBtn) settingsBtn.addEventListener('click', () => this.openSettings());
    }

    // 数字人选择器
    const digitalHumanSelect = this.container.querySelector('#digital-human-select') as HTMLSelectElement;
    if (digitalHumanSelect) {
      digitalHumanSelect.addEventListener('change', (e) => {
        const digitalHumanId = (e.target as HTMLSelectElement).value;
        this.selectDigitalHuman(digitalHumanId);
      });
    }

    // 面板事件
    this.setupPanelEvents();

    // 系统事件
    this.setupSystemEvents();
  }

  /**
   * 设置面板事件
   */
  private setupPanelEvents(): void {
    // 多动作面板事件
    if (this.panels.multiAction) {
      this.panels.multiAction.on('actionPlayed', (actionName: string) => {
        this.updateStatus(`播放动作: ${actionName}`);
      });

      this.panels.multiAction.on('actionDeleted', (actionName: string) => {
        this.updateStatus(`删除动作: ${actionName}`);
      });
    }

    // BIP上传面板事件
    if (this.panels.bipUpload) {
      this.panels.bipUpload.on('importCompleted', (result: any) => {
        this.updateStatus(`BIP导入完成: ${result.success ? '成功' : '失败'}`);
        this.refreshDigitalHumanList();
      });
    }

    // 数字人上传面板事件
    if (this.panels.digitalHumanUpload) {
      this.panels.digitalHumanUpload.on('importCompleted', (result: any) => {
        this.updateStatus(`数字人导入完成: ${result.success ? '成功' : '失败'}`);
        this.refreshDigitalHumanList();
      });
    }
  }

  /**
   * 设置系统事件
   */
  private setupSystemEvents(): void {
    // 数字人系统事件
    if (this.systems.digitalHuman) {
      this.systems.digitalHuman.on('digitalHumanCreated', (entity: Entity) => {
        this.updateStatus(`数字人创建完成: ${(entity as any).name}`);
        this.refreshDigitalHumanList();
      });
    }
  }

  /**
   * 切换标签
   * @param tab 标签
   */
  private switchTab(tab: EditorTab): void {
    this.activeTab = tab;

    // 更新标签按钮状态
    const tabButtons = this.container.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
      btn.classList.toggle('active', btn.getAttribute('data-tab') === tab);
    });

    // 显示对应的面板
    this.showPanel(tab);

    // 隐藏欢迎屏幕
    const welcomeScreen = this.container.querySelector('#welcome-screen') as HTMLElement;
    if (welcomeScreen) {
      welcomeScreen.style.display = 'none';
    }
  }

  /**
   * 显示面板
   * @param tab 标签
   */
  private showPanel(tab: EditorTab): void {
    // 隐藏所有面板
    const panelContainers = this.container.querySelectorAll('[id$="-panel-container"]');
    panelContainers.forEach(container => {
      (container as HTMLElement).style.display = 'none';
    });

    // 显示对应面板
    let containerId = '';
    switch (tab) {
      case EditorTab.MULTI_ACTION:
        containerId = 'multi-action-panel-container';
        // 设置融合管理器
        if (this.selectedDigitalHuman && this.systems.digitalHuman) {
          const fusionManager = this.systems.digitalHuman.getMultiActionFusionManager(this.selectedDigitalHuman);
          if (fusionManager && this.panels.multiAction) {
            this.panels.multiAction.setFusionManager(fusionManager);
          }
        }
        break;
      case EditorTab.BIP_UPLOAD:
        containerId = 'bip-upload-panel-container';
        break;
      case EditorTab.DIGITAL_HUMAN_UPLOAD:
        containerId = 'digital-human-upload-panel-container';
        break;
      case EditorTab.PHOTO_TO_3D:
        // TODO: 实现照片到3D面板
        break;
      case EditorTab.OVERVIEW:
        // TODO: 实现概览面板
        break;
    }

    if (containerId) {
      const container = this.container.querySelector(`#${containerId}`) as HTMLElement;
      if (container) {
        container.style.display = 'block';
      }
    }
  }

  /**
   * 处理快速操作
   * @param action 操作
   */
  private handleQuickAction(action: string): void {
    switch (action) {
      case 'photo-to-3d':
        this.switchTab(EditorTab.PHOTO_TO_3D);
        break;
      case 'upload-digital-human':
        this.switchTab(EditorTab.DIGITAL_HUMAN_UPLOAD);
        break;
      case 'upload-bip':
        this.switchTab(EditorTab.BIP_UPLOAD);
        break;
    }
  }

  /**
   * 创建新数字人
   */
  private async createNewDigitalHuman(): Promise<void> {
    if (!this.systems.digitalHuman) return;

    try {
      const request = {
        id: `digital-human-${Date.now()}`,
        userId: 'user-001',
        source: 'manual' as any,
        sourceData: {},
        options: {
          name: `数字人 ${Date.now()}`,
          tags: ['新建'],
          licenseType: 'private'
        }
      };

      const entity = await this.systems.digitalHuman.createDigitalHuman(request);
      this.selectDigitalHuman((entity as any).id);
      this.updateStatus('新数字人创建完成');

    } catch (error) {
      this.updateStatus(`创建失败: ${error.message}`);
    }
  }

  /**
   * 选择数字人
   * @param digitalHumanId 数字人ID
   */
  private selectDigitalHuman(digitalHumanId: string): void {
    if (!digitalHumanId) {
      this.selectedDigitalHuman = null;
      return;
    }

    const entity = this.world.getEntity(digitalHumanId);
    if (entity) {
      this.selectedDigitalHuman = entity;
      this.updateStatus(`选中数字人: ${(entity as any).name}`);
      
      // 更新选择器
      const select = this.container.querySelector('#digital-human-select') as HTMLSelectElement;
      if (select) {
        select.value = digitalHumanId;
      }

      // 如果当前在多动作标签，更新融合管理器
      if (this.activeTab === EditorTab.MULTI_ACTION) {
        this.showPanel(EditorTab.MULTI_ACTION);
      }
    }
  }

  /**
   * 保存当前数字人
   */
  private saveCurrentDigitalHuman(): void {
    if (!this.selectedDigitalHuman) {
      this.updateStatus('没有选中的数字人');
      return;
    }

    // TODO: 实现保存逻辑
    this.updateStatus(`保存数字人: ${this.selectedDigitalHuman.name}`);
  }

  /**
   * 导出当前数字人
   */
  private exportCurrentDigitalHuman(): void {
    if (!this.selectedDigitalHuman) {
      this.updateStatus('没有选中的数字人');
      return;
    }

    // TODO: 实现导出逻辑
    this.updateStatus(`导出数字人: ${this.selectedDigitalHuman.name}`);
  }

  /**
   * 打开设置
   */
  private openSettings(): void {
    this.switchTab(EditorTab.SETTINGS);
  }

  /**
   * 刷新数字人列表
   */
  private refreshDigitalHumanList(): void {
    const select = this.container.querySelector('#digital-human-select') as HTMLSelectElement;
    if (!select) return;

    // 获取所有数字人实体
    const digitalHumans = Array.from((this.world as any).entities.values()).filter((entity: any) =>
      entity.hasComponent('DigitalHumanComponent')
    );
    
    // 清空选项
    select.innerHTML = '<option value="">选择数字人...</option>';

    // 添加数字人选项
    digitalHumans.forEach(entity => {
      const option = document.createElement('option');
      const entityData = entity as any; // 类型断言
      option.value = entityData.id;
      option.textContent = entityData.name || entityData.id;
      select.appendChild(option);
    });

    // 更新计数
    this.updateDigitalHumanCount(digitalHumans.length);
  }

  /**
   * 更新数字人计数
   * @param count 数量
   */
  private updateDigitalHumanCount(count: number): void {
    const countElement = this.container.querySelector('#digital-human-count') as HTMLElement;
    if (countElement) {
      countElement.textContent = `${count} 个数字人`;
    }
  }

  /**
   * 更新状态
   * @param message 状态消息
   */
  private updateStatus(message: string): void {
    const statusText = this.container.querySelector('#status-text') as HTMLElement;
    if (statusText) {
      statusText.textContent = message;
    }

    // 3秒后恢复默认状态
    setTimeout(() => {
      if (statusText) {
        statusText.textContent = '就绪';
      }
    }, 3000);
  }

  /**
   * 启动编辑器
   */
  public async start(): Promise<void> {
    // 启动引擎更新循环
    let lastTime = performance.now();
    
    const update = (currentTime: number) => {
      const deltaTime = (currentTime - lastTime) / 1000;
      lastTime = currentTime;
      
      // 更新引擎
      (this.engine as any).update(deltaTime);
      
      // 更新内存使用情况
      this.updateMemoryUsage();
      
      // 继续下一帧
      requestAnimationFrame(update);
    };
    
    requestAnimationFrame(update);

    // 初始化数字人列表
    this.refreshDigitalHumanList();

    this.emit('editorStarted');
  }

  /**
   * 更新内存使用情况
   */
  private updateMemoryUsage(): void {
    const perfMemory = (performance as any).memory;
    if (perfMemory) {
      const memoryElement = this.container.querySelector('#memory-usage') as HTMLElement;
      if (memoryElement) {
        const usedMB = Math.round(perfMemory.usedJSHeapSize / 1024 / 1024);
        memoryElement.textContent = `内存: ${usedMB}MB`;
      }
    }
  }

  /**
   * 销毁编辑器
   */
  public dispose(): void {
    // 销毁面板
    Object.values(this.panels).forEach(panel => {
      if (panel && typeof panel.dispose === 'function') {
        panel.dispose();
      }
    });

    // 销毁系统
    Object.values(this.systems).forEach(system => {
      if (system && typeof system.dispose === 'function') {
        system.dispose();
      }
    });

    // 销毁服务
    this.storageService.dispose();

    // 销毁引擎
    this.engine.dispose();

    this.removeAllListeners();
  }
}
