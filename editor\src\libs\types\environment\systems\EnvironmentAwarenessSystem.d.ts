/**
 * 环境感知系统
 *
 * 该系统负责检测和更新场景中的环境信息，并将其传递给具有环境感知组件的实体。
 * 系统会分析场景中的光照、天气、地形等元素，计算环境参数，并触发相应的环境变化事件。
 */
import { System } from '../../core/System';
import type { World } from '../../core/World';
import { Vector3 } from 'three';
import { EnvironmentAwarenessData } from '../components/EnvironmentAwarenessComponent';
/**
 * 环境感知系统配置接口
 */
export interface EnvironmentAwarenessSystemConfig {
    debug?: boolean;
    updateFrequency?: number;
    autoDetect?: boolean;
    detectionRange?: number;
    enableResponse?: boolean;
    usePhysicsRaycasting?: boolean;
    enableVisualization?: boolean;
}
/**
 * 环境感知系统
 */
export declare class EnvironmentAwarenessSystem extends System {
    private config;
    private lastUpdateTime;
    private raycaster;
    private awarenessEntities;
    private responseEntities;
    private environmentZones;
    private globalEnvironmentData;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: EnvironmentAwarenessSystemConfig);
    /**
     * 创建默认环境数据
     * @returns 默认环境数据
     */
    private createDefaultEnvironmentData;
    /**
     * 初始化
     */
    initialize(): void;
    /**
     * 查找具有指定组件的实体
     * @param componentType 组件类型
     * @returns 实体数组
     */
    private findEntitiesWithComponent;
    /**
     * 更新
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 更新全局环境数据
     */
    private updateGlobalEnvironmentData;
    /**
     * 分析场景环境
     * @param scene 场景
     * @returns 环境数据
     */
    private analyzeSceneEnvironment;
    /**
     * 分析光照
     * @param scene 场景
     * @returns 光照强度
     */
    private analyzeLighting;
    /**
     * 分析天气
     * @param scene 场景
     * @returns 天气类型
     */
    private analyzeWeather;
    /**
     * 分析时间
     * @param scene 场景
     * @returns 时间 (24小时制)
     */
    private analyzeTimeOfDay;
    /**
     * 分析环境类型
     * @param scene 场景
     * @returns 环境类型
     */
    private analyzeEnvironmentType;
    /**
     * 分析地形类型
     * @param scene 场景
     * @returns 地形类型
     */
    private analyzeTerrainType;
    /**
     * 计算温度
     * @param environmentType 环境类型
     * @param timeOfDay 时间
     * @param weatherType 天气类型
     * @returns 温度 (摄氏度)
     */
    private calculateTemperature;
    /**
     * 计算湿度
     * @param weatherType 天气类型
     * @param environmentType 环境类型
     * @returns 湿度 (0-1)
     */
    private calculateHumidity;
    /**
     * 计算可见度
     * @param weatherType 天气类型
     * @param lightIntensity 光照强度
     * @returns 可见度 (米)
     */
    private calculateVisibility;
    /**
     * 更新实体的环境感知数据
     * @param entity 实体
     */
    private updateEntityEnvironmentData;
    /**
     * 处理环境响应
     */
    private processEnvironmentResponses;
    /**
     * 更新环境可视化
     */
    private updateVisualization;
    /**
     * 添加环境区域
     * @param id 区域ID
     * @param zone 环境区域
     */
    addEnvironmentZone(id: string, zone: EnvironmentZone): void;
    /**
     * 移除环境区域
     * @param id 区域ID
     */
    removeEnvironmentZone(id: string): void;
    /**
     * 获取环境区域
     * @param id 区域ID
     * @returns 环境区域
     */
    getEnvironmentZone(id: string): EnvironmentZone | undefined;
    /**
     * 获取全局环境数据
     * @returns 环境数据
     */
    getGlobalEnvironmentData(): EnvironmentAwarenessData;
    /**
     * 设置全局环境数据
     * @param data 环境数据
     */
    setGlobalEnvironmentData(data: Partial<EnvironmentAwarenessData>): void;
}
/**
 * 环境区域接口
 */
export interface EnvironmentZone {
    id: string;
    name: string;
    environmentData: Partial<EnvironmentAwarenessData>;
    containsPoint: (point: Vector3) => boolean;
}
