/**
 * 被抓取组件
 * 用于标记当前被抓取的对象
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Hand } from './GrabbableComponent';
/**
 * 被抓取组件配置
 */
export interface GrabbedComponentConfig {
    /** 抓取者 */
    grabber: Entity;
    /** 抓取手 */
    hand: Hand;
    /** 抓取偏移 */
    offset?: {
        x: number;
        y: number;
        z: number;
    };
}
/**
 * 被抓取组件
 * 当一个实体被抓取时，会添加此组件
 */
export declare class GrabbedComponent extends Component {
    /** 组件类型 */
    static readonly TYPE: string;
    /** 抓取者 */
    private _grabber;
    /** 抓取手 */
    private _hand;
    /** 抓取偏移 */
    private _offset;
    /** 抓取时间戳 */
    private _grabTime;
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param config 组件配置
     */
    constructor(entity: Entity, config: GrabbedComponentConfig);
    /**
     * 获取抓取者
     */
    get grabber(): Entity;
    /**
     * 获取抓取手
     */
    get hand(): Hand;
    /**
     * 获取抓取偏移
     */
    get offset(): {
        x: number;
        y: number;
        z: number;
    };
    /**
     * 设置抓取偏移
     */
    set offset(value: {
        x: number;
        y: number;
        z: number;
    });
    /**
     * 获取抓取时间戳
     */
    get grabTime(): number;
    /**
     * 获取抓取持续时间（毫秒）
     */
    getDuration(): number;
}
