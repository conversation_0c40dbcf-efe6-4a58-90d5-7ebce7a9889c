/**
 * 碰撞事件类
 * 用于表示物理碰撞事件
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';

/**
 * 碰撞事件类型
 */
export enum CollisionEventType {
  /** 碰撞开始 */
  BEGIN = 'collisionBegin',
  /** 碰撞持续 */
  STAY = 'collisionStay',
  /** 碰撞结束 */
  END = 'collisionEnd',
  /** 触发器进入 */
  TRIGGER_ENTER = 'triggerEnter',
  /** 触发器停留 */
  TRIGGER_STAY = 'triggerStay',
  /** 触发器离开 */
  TRIGGER_EXIT = 'triggerExit'
}

/**
 * 碰撞事件数据
 */
export interface CollisionEventData {
  /** 碰撞类型 */
  type: CollisionEventType;
  /** 碰撞的实体A */
  entityA: Entity;
  /** 碰撞的实体B */
  entityB: Entity;
  /** 碰撞点 */
  contactPoint: THREE.Vector3;
  /** 碰撞法线 */
  contactNormal: THREE.Vector3;
  /** 碰撞冲量 */
  impulse: number;
  /** 相对速度 */
  relativeVelocity: THREE.Vector3;
  /** 碰撞时间 */
  time: number;
  /** 原始CANNON碰撞事件 */
  cannonEvent?: any;
}

/**
 * 碰撞事件
 */
export class CollisionEvent {
  /** 碰撞类型 */
  public type: CollisionEventType;

  /** 碰撞的实体A */
  public entityA: Entity;

  /** 碰撞的实体B */
  public entityB: Entity;

  /** 碰撞点 */
  public contactPoint: THREE.Vector3;

  /** 碰撞法线 */
  public contactNormal: THREE.Vector3;

  /** 碰撞冲量 */
  public impulse: number;

  /** 相对速度 */
  public relativeVelocity: THREE.Vector3;

  /** 碰撞时间 */
  public time: number;

  /** 原始CANNON碰撞事件 */
  public cannonEvent?: any;

  /**
   * 创建碰撞事件
   * @param data 碰撞事件数据
   */
  constructor(data: CollisionEventData) {
    this.type = data.type;
    this.entityA = data.entityA;
    this.entityB = data.entityB;
    this.contactPoint = data.contactPoint;
    this.contactNormal = data.contactNormal;
    this.impulse = data.impulse;
    this.relativeVelocity = data.relativeVelocity;
    this.time = data.time;
    this.cannonEvent = data.cannonEvent;
  }

  /**
   * 获取碰撞的另一个实体
   * @param entity 当前实体
   * @returns 另一个实体
   */
  public getOtherEntity(entity: Entity): Entity {
    return this.entityA === entity ? this.entityB : this.entityA;
  }

  /**
   * 从CANNON碰撞事件创建碰撞事件
   * @param type 碰撞类型
   * @param event CANNON碰撞事件
   * @param entityA 实体A
   * @param entityB 实体B
   * @param time 碰撞时间
   * @returns 碰撞事件
   */
  public static fromCannonEvent(
    type: CollisionEventType,
    event: any,
    entityA: Entity,
    entityB: Entity,
    time: number
  ): CollisionEvent {
    // 获取第一个接触点
    const contact = event.contact;

    // 创建碰撞事件数据
    const data: CollisionEventData = {
      type,
      entityA,
      entityB,
      contactPoint: contact ? new THREE.Vector3(contact.bi.getPosition().x + contact.ri.x, contact.bi.getPosition().y + contact.ri.y, contact.bi.getPosition().z + contact.ri.z) : new THREE.Vector3(),
      contactNormal: contact ? new THREE.Vector3(contact.ni.x, contact.ni.y, contact.ni.z) : new THREE.Vector3(),
      impulse: contact ? (contact.impulse || 0) : 0,
      relativeVelocity: new THREE.Vector3(),
      time,
      cannonEvent: event
    };

    return new CollisionEvent(data);
  }
}
