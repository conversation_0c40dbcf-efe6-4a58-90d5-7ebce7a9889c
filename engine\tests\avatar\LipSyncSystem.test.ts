/**
 * LipSyncSystem类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import { LipSyncSystem } from '../../src/avatar/facial/LipSyncSystem';
import { LipSyncComponent } from '../../src/avatar/facial/LipSyncComponent';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { AvatarComponent } from '../../src/avatar/AvatarComponent';
import { AudioSystem } from '../../src/audio/AudioSystem';
import { AudioComponent } from '../../src/audio/AudioComponent';
import { AudioAnalyzer } from '../../src/audio/AudioAnalyzer';

describe('LipSyncSystem', () => {
  let engine: Engine;
  let world: World;
  let audioSystem: AudioSystem;
  let lipSyncSystem: LipSyncSystem;
  let entity: Entity;
  let avatarComponent: AvatarComponent;
  let audioComponent: AudioComponent;
  let lipSyncComponent: LipSyncComponent;
  
  // 在每个测试前创建一个新的口型同步系统实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建音频系统
    audioSystem = new AudioSystem();
    
    // 添加音频系统到引擎
    engine.addSystem(audioSystem);
    
    // 创建口型同步系统
    lipSyncSystem = new LipSyncSystem();
    
    // 添加口型同步系统到引擎
    engine.addSystem(lipSyncSystem);
    
    // 创建实体
    entity = new Entity(world);
    entity.name = '头像实体';
    
    // 添加变换组件
    const transform = new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity.addComponent(transform);
    
    // 创建头像组件
    avatarComponent = new AvatarComponent(entity, {
      modelUrl: 'models/avatar.glb'
    });
    entity.addComponent(avatarComponent);
    
    // 创建音频组件
    audioComponent = new AudioComponent(entity, {
      url: 'audio/speech.mp3',
      autoplay: false,
      loop: false,
      volume: 1.0
    });
    entity.addComponent(audioComponent);
    
    // 创建口型同步组件
    lipSyncComponent = new LipSyncComponent(entity, {
      audioUrl: 'audio/speech.mp3',
      visemeMapping: {
        A: 'viseme_A',
        B: 'viseme_B',
        C: 'viseme_C',
        D: 'viseme_D',
        E: 'viseme_E',
        F: 'viseme_F',
        G: 'viseme_G',
        H: 'viseme_H'
      }
    });
    entity.addComponent(lipSyncComponent);
    
    // 添加实体到世界
    world.addEntity(entity);
    
    // 注册口型同步组件
    lipSyncSystem.registerLipSyncComponent(entity, lipSyncComponent);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试口型同步系统初始化
  it('应该正确初始化口型同步系统', () => {
    expect(lipSyncSystem).toBeDefined();
    expect(lipSyncSystem['lipSyncs'].size).toBe(1);
    expect(lipSyncSystem['lipSyncs'].get(entity.getId())).toBe(lipSyncComponent);
  });
  
  // 测试注册和取消注册口型同步组件
  it('应该能够注册和取消注册口型同步组件', () => {
    // 创建另一个实体
    const entity2 = new Entity(world);
    entity2.name = '头像实体2';
    
    // 添加变换组件
    const transform2 = new Transform({
      position: { x: 5, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity2.addComponent(transform2);
    
    // 创建口型同步组件
    const lipSyncComponent2 = new LipSyncComponent(entity2, {
      audioUrl: 'audio/speech2.mp3',
      visemeMapping: {
        A: 'viseme_A',
        B: 'viseme_B'
      }
    });
    entity2.addComponent(lipSyncComponent2);
    
    // 添加实体到世界
    world.addEntity(entity2);
    
    // 注册口型同步组件
    lipSyncSystem.registerLipSyncComponent(entity2, lipSyncComponent2);
    
    // 验证组件已注册
    expect(lipSyncSystem['lipSyncs'].has(entity2.getId())).toBe(true);
    expect(lipSyncSystem['lipSyncs'].get(entity2.getId())).toBe(lipSyncComponent2);
    
    // 取消注册口型同步组件
    lipSyncSystem.unregisterLipSyncComponent(entity2);
    
    // 验证组件已取消注册
    expect(lipSyncSystem['lipSyncs'].has(entity2.getId())).toBe(false);
  });
  
  // 测试加载音频
  it('应该能够加载音频', async () => {
    // 模拟AudioSystem.loadAudio方法
    const mockAudio = {
      play: vi.fn(),
      pause: vi.fn(),
      stop: vi.fn(),
      isPlaying: false,
      duration: 10.0,
      currentTime: 0.0,
      volume: 1.0
    };
    
    const loadAudioSpy = vi.spyOn(AudioSystem.prototype, 'loadAudio').mockResolvedValue(mockAudio);
    
    // 加载音频
    await lipSyncComponent.loadAudio();
    
    // 验证loadAudio方法被调用
    expect(loadAudioSpy).toHaveBeenCalledWith('audio/speech.mp3');
    
    // 验证音频已加载
    expect(lipSyncComponent['audio']).toBe(mockAudio);
    
    // 恢复模拟
    loadAudioSpy.mockRestore();
  });
  
  // 测试分析音频
  it('应该能够分析音频', async () => {
    // 模拟AudioAnalyzer
    const mockAnalyzer = {
      analyzeAudio: vi.fn().mockResolvedValue([
        { viseme: 'A', weight: 0.8, time: 0.0 },
        { viseme: 'B', weight: 0.6, time: 0.1 },
        { viseme: 'C', weight: 0.4, time: 0.2 }
      ])
    };
    
    // 设置音频分析器
    (lipSyncComponent as any).analyzer = mockAnalyzer;
    
    // 模拟音频
    const mockAudio = {
      play: vi.fn(),
      pause: vi.fn(),
      stop: vi.fn(),
      isPlaying: false,
      duration: 10.0,
      currentTime: 0.0,
      volume: 1.0
    };
    
    // 设置音频
    (lipSyncComponent as any).audio = mockAudio;
    
    // 分析音频
    await lipSyncComponent.analyzeAudio();
    
    // 验证analyzeAudio方法被调用
    expect(mockAnalyzer.analyzeAudio).toHaveBeenCalled();
    
    // 验证音素已设置
    expect(lipSyncComponent['visemes'].length).toBe(3);
    expect(lipSyncComponent['visemes'][0].viseme).toBe('A');
    expect(lipSyncComponent['visemes'][1].viseme).toBe('B');
    expect(lipSyncComponent['visemes'][2].viseme).toBe('C');
  });
  
  // 测试播放口型同步
  it('应该能够播放口型同步', async () => {
    // 模拟头像模型
    const mockMesh = new THREE.Mesh(
      new THREE.BufferGeometry(),
      new THREE.MeshStandardMaterial()
    );
    
    // 添加混合形状
    mockMesh.morphTargetDictionary = {
      'viseme_A': 0,
      'viseme_B': 1,
      'viseme_C': 2,
      'viseme_D': 3,
      'viseme_E': 4,
      'viseme_F': 5,
      'viseme_G': 6,
      'viseme_H': 7
    };
    
    mockMesh.morphTargetInfluences = [0, 0, 0, 0, 0, 0, 0, 0];
    
    // 设置头像模型
    (avatarComponent as any).model = mockMesh;
    (lipSyncComponent as any).mesh = mockMesh;
    
    // 模拟音频
    const mockAudio = {
      play: vi.fn(),
      pause: vi.fn(),
      stop: vi.fn(),
      isPlaying: true,
      duration: 10.0,
      currentTime: 0.0,
      volume: 1.0
    };
    
    // 设置音频
    (lipSyncComponent as any).audio = mockAudio;
    
    // 设置音素
    (lipSyncComponent as any).visemes = [
      { viseme: 'A', weight: 0.8, time: 0.0 },
      { viseme: 'B', weight: 0.6, time: 0.1 },
      { viseme: 'C', weight: 0.4, time: 0.2 }
    ];
    
    // 播放口型同步
    lipSyncComponent.play();
    
    // 验证播放状态
    expect(lipSyncComponent['isPlaying']).toBe(true);
    
    // 验证音频播放方法被调用
    expect(mockAudio.play).toHaveBeenCalled();
    
    // 更新口型同步系统
    lipSyncSystem.update(0.016);
    
    // 验证混合形状权重已设置
    expect(mockMesh.morphTargetInfluences[0]).toBe(0.8); // viseme_A
  });
  
  // 测试停止口型同步
  it('应该能够停止口型同步', async () => {
    // 模拟音频
    const mockAudio = {
      play: vi.fn(),
      pause: vi.fn(),
      stop: vi.fn(),
      isPlaying: true,
      duration: 10.0,
      currentTime: 0.0,
      volume: 1.0
    };
    
    // 设置音频
    (lipSyncComponent as any).audio = mockAudio;
    (lipSyncComponent as any).isPlaying = true;
    
    // 停止口型同步
    lipSyncComponent.stop();
    
    // 验证停止状态
    expect(lipSyncComponent['isPlaying']).toBe(false);
    
    // 验证音频停止方法被调用
    expect(mockAudio.stop).toHaveBeenCalled();
  });
  
  // 测试口型同步更新
  it('应该能够更新口型同步', async () => {
    // 模拟头像模型
    const mockMesh = new THREE.Mesh(
      new THREE.BufferGeometry(),
      new THREE.MeshStandardMaterial()
    );
    
    // 添加混合形状
    mockMesh.morphTargetDictionary = {
      'viseme_A': 0,
      'viseme_B': 1,
      'viseme_C': 2
    };
    
    mockMesh.morphTargetInfluences = [0, 0, 0];
    
    // 设置头像模型
    (avatarComponent as any).model = mockMesh;
    (lipSyncComponent as any).mesh = mockMesh;
    
    // 模拟音频
    const mockAudio = {
      play: vi.fn(),
      pause: vi.fn(),
      stop: vi.fn(),
      isPlaying: true,
      duration: 10.0,
      currentTime: 0.1,
      volume: 1.0
    };
    
    // 设置音频
    (lipSyncComponent as any).audio = mockAudio;
    
    // 设置音素
    (lipSyncComponent as any).visemes = [
      { viseme: 'A', weight: 0.8, time: 0.0 },
      { viseme: 'B', weight: 0.6, time: 0.1 },
      { viseme: 'C', weight: 0.4, time: 0.2 }
    ];
    
    // 设置播放状态
    (lipSyncComponent as any).isPlaying = true;
    
    // 更新口型同步系统
    lipSyncSystem.update(0.016);
    
    // 验证混合形状权重已设置
    expect(mockMesh.morphTargetInfluences[1]).toBe(0.6); // viseme_B
  });
});
