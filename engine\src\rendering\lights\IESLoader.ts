/**
 * IES光源加载器
 * 用于加载和解析IES文件
 */
import * as THREE from 'three';

/**
 * IES光源加载器类
 */
export class IESLoader {
  /** 加载管理器 */
  private manager: THREE.LoadingManager;

  /** 文件加载器 */
  private fileLoader: THREE.FileLoader;

  /** 纹理大小 */
  private size: number;

  /**
   * 创建IES光源加载器
   * @param manager 加载管理器
   * @param size 纹理大小
   */
  constructor(manager?: THREE.LoadingManager, size: number = 256) {
    this.manager = manager || THREE.DefaultLoadingManager;
    this.fileLoader = new THREE.FileLoader(this.manager);
    this.size = size;
  }

  /**
   * 加载IES文件
   * @param url IES文件路径
   * @param onLoad 加载完成回调
   * @param onProgress 加载进度回调
   * @param onError 加载错误回调
   */
  public load(
    url: string,
    onLoad?: (texture: THREE.Texture) => void,
    onProgress?: (event: ProgressEvent) => void,
    onError?: (event: ErrorEvent) => void
  ): void {
    const scope = this;

    this.fileLoader.setResponseType('text');
    this.fileLoader.load(
      url,
      (text) => {
        if (onLoad) {
          onLoad(scope.parse(text as string));
        }
      },
      onProgress,
      onError
    );
  }

  /**
   * 解析IES数据
   * @param text IES文件数据
   * @returns IES纹理
   */
  public parse(text: string): THREE.Texture {
    // 解析IES文件
    const iesData = this.parseIESData(text);

    // 创建纹理
    const texture = this.createIESTexture(iesData);

    return texture;
  }

  /**
   * 解析IES数据
   * @param text IES文件数据
   * @returns IES数据
   */
  private parseIESData(text: string): {
    photometricType: number;
    maxCandela: number;
    angleData: { vertical: number[]; horizontal: number[] };
    candelaValues: number[][];
  } {
    // 移除回车符
    text = text.replace(/\r/g, '');

    // 按行分割
    const lines = text.split('\n');
    let lineIndex = 0;

    // 跳过文件头
    while (lineIndex < lines.length && !lines[lineIndex].includes('TILT=')) {
      lineIndex++;
    }

    // 检查TILT
    const tiltLine = lines[lineIndex++];
    const tiltMatch = tiltLine.match(/TILT=(.+)/);
    const tilt = tiltMatch ? tiltMatch[1].trim() : 'NONE';

    // 如果TILT不是NONE，则跳过TILT数据
    if (tilt !== 'NONE') {
      const tiltLineCount = parseInt(lines[lineIndex++], 10);
      lineIndex += tiltLineCount;
    }

    // 读取光度数据
    const values = lines[lineIndex++].trim().split(/\s+/).map(Number);
    const [lampCount, lumensPerLamp, multiplier, photometricType, unitType, width, length, height] = values;

    // 读取角度数据
    const angleData = {
      vertical: [] as number[],
      horizontal: [] as number[]
    };

    const verticalAngleCount = parseInt(lines[lineIndex++], 10);
    const horizontalAngleCount = parseInt(lines[lineIndex++], 10);

    // 读取垂直角度
    angleData.vertical = lines[lineIndex++].trim().split(/\s+/).map(Number);

    // 读取水平角度
    angleData.horizontal = lines[lineIndex++].trim().split(/\s+/).map(Number);

    // 读取光强数据
    const candelaValues: number[][] = [];
    let maxCandela = 0;

    for (let h = 0; h < horizontalAngleCount; h++) {
      const row: number[] = [];
      
      for (let v = 0; v < verticalAngleCount; v++) {
        if (lineIndex >= lines.length) break;
        
        // 读取光强值
        const candela = parseFloat(lines[lineIndex++].trim());
        row.push(candela);
        
        // 更新最大光强
        maxCandela = Math.max(maxCandela, candela);
      }
      
      candelaValues.push(row);
    }

    return {
      photometricType,
      maxCandela,
      angleData,
      candelaValues
    };
  }

  /**
   * 创建IES纹理
   * @param iesData IES数据
   * @returns IES纹理
   */
  private createIESTexture(iesData: {
    photometricType: number;
    maxCandela: number;
    angleData: { vertical: number[]; horizontal: number[] };
    candelaValues: number[][];
  }): THREE.Texture {
    // 创建画布
    const canvas = document.createElement('canvas');
    canvas.width = this.size;
    canvas.height = this.size;
    const context = canvas.getContext('2d')!;

    // 清除画布
    context.fillStyle = 'black';
    context.fillRect(0, 0, this.size, this.size);

    // 绘制IES数据
    const { photometricType, maxCandela, angleData, candelaValues } = iesData;
    const { vertical, horizontal } = angleData;

    // 根据光度类型处理数据
    switch (photometricType) {
      case 1: // C-Gamma (Type C)
        this.drawTypeC(context, maxCandela, vertical, horizontal, candelaValues);
        break;
      case 2: // B-Beta (Type B)
        this.drawTypeB(context, maxCandela, vertical, horizontal, candelaValues);
        break;
      case 3: // A-Alpha (Type A)
        this.drawTypeA(context, maxCandela, vertical, horizontal, candelaValues);
        break;
      default:
        console.warn(`不支持的光度类型: ${photometricType}`);
        this.drawTypeC(context, maxCandela, vertical, horizontal, candelaValues);
        break;
    }

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas);
    texture.mapping = THREE.EquirectangularReflectionMapping;
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;

    return texture;
  }

  /**
   * 绘制Type C光度数据
   * @param context 画布上下文
   * @param maxCandela 最大光强
   * @param vertical 垂直角度
   * @param horizontal 水平角度
   * @param candelaValues 光强值
   */
  private drawTypeC(
    context: CanvasRenderingContext2D,
    maxCandela: number,
    vertical: number[],
    horizontal: number[],
    candelaValues: number[][]
  ): void {
    const { width, height } = context.canvas;
    const imageData = context.getImageData(0, 0, width, height);
    const data = imageData.data;

    // 遍历每个像素
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        // 计算球面坐标
        const u = x / width;
        const v = y / height;
        
        // 转换为角度
        const phi = u * 2 * Math.PI; // 水平角度 [0, 2π]
        const theta = v * Math.PI; // 垂直角度 [0, π]
        
        // 插值获取光强
        const candela = this.interpolateCandelaTypeC(phi, theta, horizontal, vertical, candelaValues, maxCandela);
        
        // 设置像素值
        const index = (y * width + x) * 4;
        data[index] = candela * 255;
        data[index + 1] = candela * 255;
        data[index + 2] = candela * 255;
        data[index + 3] = 255;
      }
    }

    context.putImageData(imageData, 0, 0);
  }

  /**
   * 绘制Type B光度数据
   * @param context 画布上下文
   * @param maxCandela 最大光强
   * @param vertical 垂直角度
   * @param horizontal 水平角度
   * @param candelaValues 光强值
   */
  private drawTypeB(
    context: CanvasRenderingContext2D,
    maxCandela: number,
    vertical: number[],
    horizontal: number[],
    candelaValues: number[][]
  ): void {
    // Type B光度数据绘制（简化为Type C）
    this.drawTypeC(context, maxCandela, vertical, horizontal, candelaValues);
  }

  /**
   * 绘制Type A光度数据
   * @param context 画布上下文
   * @param maxCandela 最大光强
   * @param vertical 垂直角度
   * @param horizontal 水平角度
   * @param candelaValues 光强值
   */
  private drawTypeA(
    context: CanvasRenderingContext2D,
    maxCandela: number,
    vertical: number[],
    horizontal: number[],
    candelaValues: number[][]
  ): void {
    // Type A光度数据绘制（简化为Type C）
    this.drawTypeC(context, maxCandela, vertical, horizontal, candelaValues);
  }

  /**
   * 插值获取Type C光强
   * @param phi 水平角度
   * @param theta 垂直角度
   * @param horizontal 水平角度数组
   * @param vertical 垂直角度数组
   * @param candelaValues 光强值数组
   * @param maxCandela 最大光强
   * @returns 归一化光强
   */
  private interpolateCandelaTypeC(
    phi: number,
    theta: number,
    horizontal: number[],
    vertical: number[],
    candelaValues: number[][],
    maxCandela: number
  ): number {
    // 转换为度数
    let phiDegrees = (phi * 180) / Math.PI;
    let thetaDegrees = (theta * 180) / Math.PI;

    // 确保角度在有效范围内
    phiDegrees = phiDegrees % 360;
    if (phiDegrees < 0) phiDegrees += 360;

    // 查找最近的水平角度索引
    let h1 = 0;
    let h2 = 0;
    let hRatio = 0;

    if (horizontal.length === 1) {
      h1 = h2 = 0;
      hRatio = 0;
    } else {
      for (let i = 0; i < horizontal.length - 1; i++) {
        if (phiDegrees >= horizontal[i] && phiDegrees <= horizontal[i + 1]) {
          h1 = i;
          h2 = i + 1;
          hRatio = (phiDegrees - horizontal[i]) / (horizontal[i + 1] - horizontal[i]);
          break;
        }
      }
    }

    // 查找最近的垂直角度索引
    let v1 = 0;
    let v2 = 0;
    let vRatio = 0;

    for (let i = 0; i < vertical.length - 1; i++) {
      if (thetaDegrees >= vertical[i] && thetaDegrees <= vertical[i + 1]) {
        v1 = i;
        v2 = i + 1;
        vRatio = (thetaDegrees - vertical[i]) / (vertical[i + 1] - vertical[i]);
        break;
      }
    }

    // 双线性插值
    const c11 = candelaValues[h1][v1] / maxCandela;
    const c12 = candelaValues[h1][v2] / maxCandela;
    const c21 = candelaValues[h2][v1] / maxCandela;
    const c22 = candelaValues[h2][v2] / maxCandela;

    const c1 = c11 * (1 - vRatio) + c12 * vRatio;
    const c2 = c21 * (1 - vRatio) + c22 * vRatio;

    return c1 * (1 - hRatio) + c2 * hRatio;
  }

  /**
   * 设置纹理大小
   * @param size 纹理大小
   */
  public setSize(size: number): void {
    this.size = size;
  }

  /**
   * 获取纹理大小
   * @returns 纹理大小
   */
  public getSize(): number {
    return this.size;
  }
}
