/**
 * 视觉脚本AI和网络结合示例
 * 演示如何使用视觉脚本系统的AI节点和网络节点创建智能网络应用
 */
import { Engine, World, Entity, Scene, Transform, Camera, VisualScriptComponent, VisualScriptSystem } from '../../engine/src';
import { AIAnimationSynthesisSystem } from '../../engine/src/animation/AIAnimationSynthesisSystem';
import { AIEmotionAnalysisSystem } from '../../engine/src/ai/AIEmotionAnalysisSystem';
import { AINLPSystem } from '../../engine/src/ai/AINLPSystem';
import { AIModelManager } from '../../engine/src/ai/AIModelManager';
import { NetworkSystem } from '../../engine/src/network/NetworkSystem';
import { NetworkSecuritySystem } from '../../engine/src/network/NetworkSecuritySystem';
import { AnimatorComponent } from '../../engine/src/animation/AnimatorComponent';
import { ModelComponent } from '../../engine/src/rendering/ModelComponent';
import { GraphJSON } from '../../engine/src/visualscript/graph/GraphJSON';
import { NodeRegistry } from '../../engine/src/visualscript/nodes/NodeRegistry';
import { ValueTypeRegistry } from '../../engine/src/visualscript/values/ValueTypeRegistry';
import { registerCoreNodes } from '../../engine/src/visualscript/presets/CoreNodes';
import { registerAINodes } from '../../engine/src/visualscript/presets/AINodes';
import { registerNetworkNodes } from '../../engine/src/visualscript/presets/NetworkNodes';
import { registerLogicNodes } from '../../engine/src/visualscript/presets/LogicNodes';
import { registerEntityNodes } from '../../engine/src/visualscript/presets/EntityNodes';
import { BandwidthControlStrategy } from '../../engine/src/network/BandwidthControlStrategy';
import { UserRole } from '../../engine/src/network/UserRole';

/**
 * 视觉脚本AI和网络结合示例
 */
export class AINetworkExample {
  /** 引擎实例 */
  private engine: Engine;

  /** 世界实例 */
  private world: World;

  /** 场景实例 */
  private scene: Scene;

  /** 视觉脚本系统 */
  private visualScriptSystem: VisualScriptSystem;

  /** AI动画合成系统 */
  private aiAnimationSystem: AIAnimationSynthesisSystem;

  /** AI情感分析系统 */
  private aiEmotionSystem: AIEmotionAnalysisSystem;

  /** AI自然语言处理系统 */
  private aiNLPSystem: AINLPSystem;

  /** AI模型管理器 */
  private aiModelManager: AIModelManager;

  /** 网络系统 */
  private networkSystem: NetworkSystem;

  /** 网络安全系统 */
  private networkSecuritySystem: NetworkSecuritySystem;

  /** 角色实体 */
  private characterEntity: Entity;

  /** 服务器URL */
  private serverUrl: string = 'ws://localhost:8080';

  /** UI元素 */
  private ui: HTMLElement;

  /**
   * 创建示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine({
      debug: true
    });

    // 创建世界
    this.world = new World(this.engine);

    // 创建节点注册表
    const nodeRegistry = new NodeRegistry();

    // 注册核心节点
    registerCoreNodes(nodeRegistry);

    // 注册逻辑节点
    registerLogicNodes(nodeRegistry);

    // 注册实体节点
    registerEntityNodes(nodeRegistry);

    // 注册AI节点
    registerAINodes(nodeRegistry);

    // 注册网络节点
    registerNetworkNodes(nodeRegistry);

    // 创建值类型注册表
    const valueTypeRegistry = new ValueTypeRegistry();

    // 创建视觉脚本系统
    this.visualScriptSystem = new VisualScriptSystem(this.world, {
      defaultDomain: 'default'
    });

    // 注册脚本域
    this.visualScriptSystem.registerDomain('default', nodeRegistry, valueTypeRegistry);

    // 添加视觉脚本系统到世界
    this.world.addSystem(this.visualScriptSystem);

    // 创建AI动画合成系统
    this.aiAnimationSystem = new AIAnimationSynthesisSystem(this.world, {
      debug: true,
      useLocalModel: true
    });

    // 添加AI动画合成系统到世界
    this.world.addSystem(this.aiAnimationSystem);

    // 创建AI情感分析系统
    this.aiEmotionSystem = new AIEmotionAnalysisSystem(this.world, {
      debug: true
    });

    // 添加AI情感分析系统到世界
    this.world.addSystem(this.aiEmotionSystem);

    // 创建AI自然语言处理系统
    this.aiNLPSystem = new AINLPSystem(this.world, {
      debug: true
    });

    // 添加AI自然语言处理系统到世界
    this.world.addSystem(this.aiNLPSystem);

    // 创建AI模型管理器
    this.aiModelManager = new AIModelManager(this.world, {
      debug: true
    });

    // 添加AI模型管理器到世界
    this.world.addSystem(this.aiModelManager);

    // 创建网络系统
    this.networkSystem = new NetworkSystem({
      autoConnect: false,
      serverUrl: this.serverUrl,
      enableWebRTC: true,
      enableMediaStream: true,
      enableAudio: true,
      enableVideo: false,
      enableNetworkQualityMonitor: true,
      enableBandwidthControl: true,
      bandwidthControlStrategy: BandwidthControlStrategy.ADAPTIVE,
      enableEntitySync: true,
      enableUserSessionManagement: true,
      defaultUserRole: UserRole.USER,
      enablePermissionCheck: true
    });

    // 添加网络系统到世界
    this.world.addSystem(this.networkSystem);

    // 创建网络安全系统
    this.networkSecuritySystem = new NetworkSecuritySystem(this.world, {
      debug: true
    });

    // 添加网络安全系统到世界
    this.world.addSystem(this.networkSecuritySystem);

    // 创建场景
    this.scene = new Scene(this.world, {
      name: '视觉脚本AI和网络结合示例场景'
    });

    // 创建相机
    const camera = new Entity('相机');
    camera.addComponent(new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000
    }));
    camera.addComponent(new Transform({
      position: { x: 0, y: 1.6, z: 3 },
      rotation: { x: 0, y: 0, z: 0 }
    }));

    // 添加相机到场景
    this.scene.addEntity(camera);

    // 创建角色
    this.createCharacter();

    // 创建AI聊天机器人
    this.createAIChatbot();

    // 创建网络连接管理器
    this.createNetworkManager();

    // 创建UI
    this.createUI();

    // 启动引擎
    this.engine.start();
  }

  /**
   * 创建角色
   */
  private createCharacter(): void {
    // 创建角色实体
    this.characterEntity = new Entity('角色');

    // 添加变换组件
    this.characterEntity.addComponent(new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));

    // 添加模型组件
    this.characterEntity.addComponent(new ModelComponent({
      url: 'models/character.glb'
    }));

    // 添加动画器组件
    this.characterEntity.addComponent(new AnimatorComponent());

    // 添加到场景
    this.scene.addEntity(this.characterEntity);

    // 创建AI动画合成组件
    this.aiAnimationSystem.createAIAnimationSynthesis(this.characterEntity);
  }

  /**
   * 创建AI聊天机器人
   */
  private createAIChatbot(): void {
    // 创建AI聊天机器人实体
    const chatbotEntity = new Entity('AI聊天机器人');

    // 添加视觉脚本组件
    const chatbotScript = this.createChatbotScript();
    chatbotEntity.addComponent(new VisualScriptComponent({
      script: chatbotScript,
      running: true,
      domain: 'default'
    }));

    // 添加到场景
    this.scene.addEntity(chatbotEntity);
  }

  /**
   * 创建AI聊天机器人脚本
   * @returns 聊天机器人脚本
   */
  private createChatbotScript(): GraphJSON {
    // 创建聊天机器人脚本
    const script: GraphJSON = {
      nodes: [
        {
          id: 'start',
          type: 'core/events/onStart',
          metadata: {
            positionX: 100,
            positionY: 100
          },
          flows: {
            flow: {
              nodeId: 'loadAIModel',
              socket: 'flow'
            }
          }
        },
        {
          id: 'loadAIModel',
          type: 'ai/model/load',
          metadata: {
            positionX: 300,
            positionY: 100
          },
          parameters: {
            modelType: {
              value: 'gpt-3.5-turbo'
            },
            config: {
              value: {
                temperature: 0.7,
                maxTokens: 100
              }
            }
          },
          flows: {
            success: {
              nodeId: 'logModelLoaded',
              socket: 'flow'
            },
            fail: {
              nodeId: 'logModelLoadFailed',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logModelLoaded',
          type: 'core/debug/log',
          metadata: {
            positionX: 500,
            positionY: 50
          },
          parameters: {
            message: {
              value: 'AI模型加载成功'
            }
          },
          flows: {
            flow: {
              nodeId: 'setupChatHandler',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logModelLoadFailed',
          type: 'core/debug/log',
          metadata: {
            positionX: 500,
            positionY: 150
          },
          parameters: {
            message: {
              value: 'AI模型加载失败'
            }
          }
        },
        {
          id: 'setupChatHandler',
          type: 'core/dom/addEventListener',
          metadata: {
            positionX: 700,
            positionY: 100
          },
          parameters: {
            elementId: {
              value: 'chat-send-button'
            },
            eventType: {
              value: 'click'
            }
          },
          flows: {
            event: {
              nodeId: 'handleChatMessage',
              socket: 'flow'
            }
          }
        },
        {
          id: 'handleChatMessage',
          type: 'core/dom/getValue',
          metadata: {
            positionX: 900,
            positionY: 100
          },
          parameters: {
            elementId: {
              value: 'chat-text-input'
            }
          },
          flows: {
            flow: {
              nodeId: 'analyzeEmotion',
              socket: 'flow'
            }
          }
        },
        {
          id: 'analyzeEmotion',
          type: 'ai/emotion/analyze',
          metadata: {
            positionX: 1100,
            positionY: 100
          },
          parameters: {
            text: {
              reference: {
                nodeId: 'handleChatMessage',
                socket: 'value'
              }
            },
            detailed: {
              value: true
            }
          },
          flows: {
            success: {
              nodeId: 'generateResponse',
              socket: 'flow'
            },
            fail: {
              nodeId: 'logEmotionAnalysisFailed',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logEmotionAnalysisFailed',
          type: 'core/debug/log',
          metadata: {
            positionX: 1300,
            positionY: 200
          },
          parameters: {
            message: {
              value: '情感分析失败'
            }
          },
          flows: {
            flow: {
              nodeId: 'generateResponse',
              socket: 'flow'
            }
          }
        },
        {
          id: 'generateResponse',
          type: 'ai/model/generateText',
          metadata: {
            positionX: 1300,
            positionY: 100
          },
          parameters: {
            model: {
              reference: {
                nodeId: 'loadAIModel',
                socket: 'model'
              }
            },
            prompt: {
              reference: {
                nodeId: 'handleChatMessage',
                socket: 'value'
              }
            },
            maxTokens: {
              value: 100
            },
            temperature: {
              value: 0.7
            }
          },
          flows: {
            success: {
              nodeId: 'displayResponse',
              socket: 'flow'
            },
            fail: {
              nodeId: 'logResponseFailed',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logResponseFailed',
          type: 'core/debug/log',
          metadata: {
            positionX: 1500,
            positionY: 200
          },
          parameters: {
            message: {
              value: '生成回复失败'
            }
          }
        },
        {
          id: 'displayResponse',
          type: 'core/dom/createElement',
          metadata: {
            positionX: 1500,
            positionY: 100
          },
          parameters: {
            parentId: {
              value: 'chat-messages'
            },
            tagName: {
              value: 'div'
            },
            className: {
              value: 'chat-message ai'
            },
            textContent: {
              reference: {
                nodeId: 'generateResponse',
                socket: 'text'
              }
            }
          },
          flows: {
            flow: {
              nodeId: 'clearInput',
              socket: 'flow'
            }
          }
        },
        {
          id: 'clearInput',
          type: 'core/dom/setValue',
          metadata: {
            positionX: 1700,
            positionY: 100
          },
          parameters: {
            elementId: {
              value: 'chat-text-input'
            },
            value: {
              value: ''
            }
          },
          flows: {
            flow: {
              nodeId: 'animateCharacter',
              socket: 'flow'
            }
          }
        },
        {
          id: 'animateCharacter',
          type: 'ai/emotion/driveAnimation',
          metadata: {
            positionX: 1900,
            positionY: 100
          },
          parameters: {
            entity: {
              value: '角色'
            },
            emotion: {
              reference: {
                nodeId: 'analyzeEmotion',
                socket: 'emotion'
              }
            },
            intensity: {
              reference: {
                nodeId: 'analyzeEmotion',
                socket: 'intensity'
              }
            },
            duration: {
              value: 3.0
            }
          }
        }
      ],
      variables: []
    };

    return script;
  }

  /**
   * 创建网络连接管理器
   */
  private createNetworkManager(): void {
    // 创建网络连接管理器实体
    const networkManagerEntity = new Entity('网络连接管理器');

    // 添加视觉脚本组件
    const networkManagerScript = this.createNetworkManagerScript();
    networkManagerEntity.addComponent(new VisualScriptComponent({
      script: networkManagerScript,
      running: true,
      domain: 'default'
    }));

    // 添加到场景
    this.scene.addEntity(networkManagerEntity);
  }

  /**
   * 创建网络连接管理器脚本
   * @returns 网络连接管理器脚本
   */
  private createNetworkManagerScript(): GraphJSON {
    // 创建网络连接管理器脚本
    const script: GraphJSON = {
      nodes: [
        {
          id: 'start',
          type: 'core/events/onStart',
          metadata: {
            positionX: 100,
            positionY: 100
          },
          flows: {
            flow: {
              nodeId: 'connect',
              socket: 'flow'
            }
          }
        },
        {
          id: 'connect',
          type: 'network/connectToServer',
          metadata: {
            positionX: 300,
            positionY: 100
          },
          parameters: {
            serverUrl: {
              value: this.serverUrl
            }
          },
          flows: {
            success: {
              nodeId: 'logConnected',
              socket: 'flow'
            },
            fail: {
              nodeId: 'logConnectionFailed',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logConnected',
          type: 'core/debug/log',
          metadata: {
            positionX: 500,
            positionY: 50
          },
          parameters: {
            message: {
              value: '连接服务器成功'
            }
          },
          flows: {
            flow: {
              nodeId: 'setupMessageHandler',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logConnectionFailed',
          type: 'core/debug/log',
          metadata: {
            positionX: 500,
            positionY: 150
          },
          parameters: {
            message: {
              value: '连接服务器失败'
            }
          },
          flows: {
            flow: {
              nodeId: 'retryConnection',
              socket: 'flow'
            }
          }
        },
        {
          id: 'retryConnection',
          type: 'core/time/delay',
          metadata: {
            positionX: 700,
            positionY: 150
          },
          parameters: {
            duration: {
              value: 5
            }
          },
          flows: {
            flow: {
              nodeId: 'connect',
              socket: 'flow'
            }
          }
        },
        {
          id: 'setupMessageHandler',
          type: 'network/events/onMessage',
          metadata: {
            positionX: 700,
            positionY: 50
          },
          flows: {
            flow: {
              nodeId: 'handleNetworkMessage',
              socket: 'flow'
            }
          }
        },
        {
          id: 'handleNetworkMessage',
          type: 'core/debug/log',
          metadata: {
            positionX: 900,
            positionY: 50
          },
          parameters: {
            message: {
              reference: {
                nodeId: 'setupMessageHandler',
                socket: 'message'
              }
            }
          },
          flows: {
            flow: {
              nodeId: 'displayNetworkMessage',
              socket: 'flow'
            }
          }
        },
        {
          id: 'displayNetworkMessage',
          type: 'core/dom/createElement',
          metadata: {
            positionX: 1100,
            positionY: 50
          },
          parameters: {
            parentId: {
              value: 'chat-messages'
            },
            tagName: {
              value: 'div'
            },
            className: {
              value: 'chat-message network'
            },
            textContent: {
              reference: {
                nodeId: 'setupMessageHandler',
                socket: 'message'
              }
            }
          }
        }
      ],
      variables: []
    };

    return script;
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    this.ui = document.createElement('div');
    this.ui.className = 'ai-network-example-ui';
    document.body.appendChild(this.ui);

    // 创建聊天界面
    const chatContainer = document.createElement('div');
    chatContainer.className = 'chat-container';
    this.ui.appendChild(chatContainer);

    // 创建聊天消息区域
    const chatMessages = document.createElement('div');
    chatMessages.className = 'chat-messages';
    chatMessages.id = 'chat-messages';
    chatContainer.appendChild(chatMessages);

    // 创建聊天输入区域
    const chatInput = document.createElement('div');
    chatInput.className = 'chat-input';
    chatContainer.appendChild(chatInput);

    // 创建文本输入框
    const textInput = document.createElement('input');
    textInput.type = 'text';
    textInput.placeholder = '输入消息...';
    textInput.id = 'chat-text-input';
    chatInput.appendChild(textInput);

    // 创建发送按钮
    const sendButton = document.createElement('button');
    sendButton.textContent = '发送';
    sendButton.id = 'chat-send-button';
    chatInput.appendChild(sendButton);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      .ai-network-example-ui {
        position: absolute;
        bottom: 20px;
        right: 20px;
        width: 300px;
        height: 400px;
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 10px;
        color: white;
        font-family: Arial, sans-serif;
        overflow: hidden;
      }

      .chat-container {
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 10px;
      }

      .chat-input {
        display: flex;
        padding: 10px;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
      }

      .chat-input input {
        flex: 1;
        padding: 8px;
        border-radius: 4px;
        border: none;
        margin-right: 10px;
      }

      .chat-input button {
        padding: 8px 15px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }

      .chat-message {
        margin-bottom: 10px;
        padding: 8px;
        border-radius: 4px;
      }

      .chat-message.user {
        background-color: rgba(76, 175, 80, 0.3);
        align-self: flex-end;
      }

      .chat-message.ai {
        background-color: rgba(33, 150, 243, 0.3);
        align-self: flex-start;
      }
    `;
    document.head.appendChild(style);
  }
}
