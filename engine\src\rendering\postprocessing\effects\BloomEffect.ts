/**
 * 泛光效果
 */
import * as THREE from 'three';
// 使用类型断言导入 UnrealBloomPass
// @ts-ignore
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 泛光效果选项
 */
export interface BloomEffectOptions extends PostProcessingEffectOptions {
  /** 强度 */
  strength?: number;
  /** 半径 */
  radius?: number;
  /** 阈值 */
  threshold?: number;
  /** 是否使用选择性泛光 */
  selectiveBloom?: boolean;
}

/**
 * 泛光效果
 */
export class BloomEffect extends PostProcessingEffect {
  /** 强度 */
  private strength: number;

  /** 半径 */
  private radius: number;

  /** 阈值 */
  private threshold: number;

  /** 是否使用选择性泛光 */
  private selectiveBloom: boolean;

  /** 泛光通道 */
  private bloomPass: UnrealBloomPass | null = null;

  /** 泛光层 */
  private BLOOM_LAYER = 1;

  /** 原始场景 */
  private originalScene: THREE.Scene | null = null;

  /** 暗物体材质 */
  private darkMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });

  /** 材质缓存 */
  private materials: Map<THREE.Object3D, THREE.Material | THREE.Material[]> = new Map();

  /**
   * 创建泛光效果
   * @param options 泛光效果选项
   */
  constructor(options: BloomEffectOptions = { name: 'Bloom' }) {
    super(options);

    this.strength = options.strength || 1.0;
    this.radius = options.radius || 0.5;
    this.threshold = options.threshold || 0.6;
    this.selectiveBloom = options.selectiveBloom || false;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 创建泛光通道
    this.bloomPass = new UnrealBloomPass(
      new THREE.Vector2(this.width, this.height),
      this.strength,
      this.radius,
      this.threshold
    );

    // 设置通道
    this.pass = this.bloomPass;
  }

  /**
   * 更新效果
   * @param _deltaTime 帧间隔时间（秒）- 未使用，保留以符合接口
   */
  public update(_deltaTime: number): void {
    // 如果使用选择性泛光，则需要在渲染前处理场景
    if (this.selectiveBloom && this.originalScene) {
      this.darkenNonBloomObjects();
    }
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    super.resize(width, height);

    // 更新泛光通道大小
    if (this.bloomPass) {
      this.bloomPass.resolution.set(width, height);
    }
  }

  /**
   * 设置强度
   * @param strength 强度
   */
  public setStrength(strength: number): void {
    this.strength = strength;

    if (this.bloomPass) {
      this.bloomPass.strength = strength;
    }
  }

  /**
   * 获取强度
   * @returns 强度
   */
  public getStrength(): number {
    return this.strength;
  }

  /**
   * 设置半径
   * @param radius 半径
   */
  public setRadius(radius: number): void {
    this.radius = radius;

    if (this.bloomPass) {
      this.bloomPass.radius = radius;
    }
  }

  /**
   * 获取半径
   * @returns 半径
   */
  public getRadius(): number {
    return this.radius;
  }

  /**
   * 设置阈值
   * @param threshold 阈值
   */
  public setThreshold(threshold: number): void {
    this.threshold = threshold;

    if (this.bloomPass) {
      this.bloomPass.threshold = threshold;
    }
  }

  /**
   * 获取阈值
   * @returns 阈值
   */
  public getThreshold(): number {
    return this.threshold;
  }

  /**
   * 设置是否使用选择性泛光
   * @param selective 是否使用选择性泛光
   */
  public setSelectiveBloom(selective: boolean): void {
    this.selectiveBloom = selective;
  }

  /**
   * 获取是否使用选择性泛光
   * @returns 是否使用选择性泛光
   */
  public isSelectiveBloom(): boolean {
    return this.selectiveBloom;
  }

  /**
   * 设置场景
   * @param scene 场景
   */
  public setScene(scene: THREE.Scene): void {
    this.originalScene = scene;

    // 注意：泛光层已在构造函数中设置，这里不需要额外操作
  }

  /**
   * 将对象添加到泛光层
   * @param object 对象
   */
  public addToBloomLayer(object: THREE.Object3D): void {
    object.layers.enable(this.BLOOM_LAYER);
  }

  /**
   * 将对象从泛光层移除
   * @param object 对象
   */
  public removeFromBloomLayer(object: THREE.Object3D): void {
    object.layers.disable(this.BLOOM_LAYER);
  }

  /**
   * 使非泛光对象变暗
   */
  private darkenNonBloomObjects(): void {
    if (!this.originalScene) return;

    // 保存材质
    this.materials.clear();

    // 遍历场景中的所有对象
    this.originalScene.traverse((object: THREE.Object3D) => {
      if (object instanceof THREE.Mesh && object.material) {
        // 保存原始材质
        this.materials.set(object, object.material);

        // 如果对象不在泛光层，则使用暗材质
        const bloomLayer = new THREE.Layers();
        bloomLayer.set(this.BLOOM_LAYER);
        if (!object.layers.test(bloomLayer)) {
          object.material = this.darkMaterial;
        }
      }
    });
  }

  /**
   * 恢复原始材质
   */
  private restoreOriginalMaterials(): void {
    if (!this.originalScene) return;

    // 遍历材质缓存
    this.materials.forEach((material, object) => {
      if (object instanceof THREE.Mesh) {
        object.material = material;
      }
    });

    // 清空材质缓存
    this.materials.clear();
  }

  /**
   * 销毁效果
   */
  public dispose(): void {
    // 恢复原始材质
    if (this.selectiveBloom) {
      this.restoreOriginalMaterials();
    }

    // 销毁暗材质
    (this.darkMaterial as any).dispose();

    super.dispose();
  }
}
