/**
 * 水下体积光渲染系统
 * 创建更真实的水下光束效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import type { Camera   } from '../Camera';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 水下体积光配置
 */
export interface UnderwaterVolumetricLightConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 光线密度 */
  density?: number;
  /** 光线衰减 */
  decay?: number;
  /** 光线权重 */
  weight?: number;
  /** 光线曝光 */
  exposure?: number;
  /** 光线采样数 */
  samples?: number;
  /** 光线颜色 */
  color?: THREE.Color;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 水下体积光系统事件类型
 */
export enum UnderwaterVolumetricLightEventType {
  /** 体积光更新 */
  VOLUMETRIC_LIGHT_UPDATED = 'volumetricLightUpdated',
  /** 体积光参数变化 */
  VOLUMETRIC_LIGHT_PARAMS_CHANGED = 'volumetricLightParamsChanged'
}

/**
 * 水下体积光系统
 */
export class UnderwaterVolumetricLightSystem extends System {
  /** 系统类型 */
  public static readonly TYPE = 'UnderwaterVolumetricLightSystem';

  /** 配置 */
  private config: Required<UnderwaterVolumetricLightConfig>;
  /** 水体实体映射 */
  private waterEntities: Map<Entity, WaterBodyComponent> = new Map();
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 调试对象 */
  private debugObjects: THREE.Object3D[] = [];
  /** 体积光渲染目标 */
  private renderTarget: THREE.WebGLRenderTarget;
  /** 体积光场景 */
  private volumetricScene: THREE.Scene;
  /** 体积光相机 */
  private volumetricCamera: THREE.Camera;
  /** 体积光材质 */
  private volumetricMaterial: THREE.ShaderMaterial;
  /** 体积光网格 */
  private volumetricMesh: THREE.Mesh;
  /** 光源列表 */
  private lightSources: THREE.Light[] = [];
  /** 是否在水下 */
  private isUnderwater: boolean = false;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: UnderwaterVolumetricLightConfig = {}) {
    super();
    this.setWorld(world);

    // 设置默认配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      density: config.density || 0.1,
      decay: config.decay || 0.96,
      weight: config.weight || 0.4,
      exposure: config.exposure || 0.6,
      samples: config.samples || 50,
      color: config.color || new THREE.Color(0x88ccff),
      useDebugVisualization: config.useDebugVisualization !== undefined ? config.useDebugVisualization : false
    };

    // 创建渲染目标
    this.renderTarget = new THREE.WebGLRenderTarget(
      window.innerWidth,
      window.innerHeight,
      {
        minFilter: THREE.LinearFilter,
        magFilter: THREE.LinearFilter,
        format: THREE.RGBAFormat,
        type: THREE.FloatType
      }
    );

    // 创建体积光场景
    this.volumetricScene = new THREE.Scene();

    // 创建体积光相机
    this.volumetricCamera = new THREE.PerspectiveCamera(
      60,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );

    // 创建体积光材质
    this.volumetricMaterial = new THREE.ShaderMaterial({
      uniforms: {
        tDiffuse: { value: null },
        lightPosition: { value: new THREE.Vector3(0, 0, 0) },
        exposure: { value: this.config.exposure },
        decay: { value: this.config.decay },
        density: { value: this.config.density },
        weight: { value: this.config.weight },
        samples: { value: this.config.samples },
        color: { value: this.config.color }
      },
      vertexShader: this.getVolumetricLightVertexShader(),
      fragmentShader: this.getVolumetricLightFragmentShader(),
      blending: THREE.AdditiveBlending,
      transparent: true,
      depthWrite: false
    });

    // 创建体积光网格
    this.volumetricMesh = new THREE.Mesh(
      new THREE.PlaneGeometry(2, 2),
      this.volumetricMaterial
    );
    this.volumetricScene.add(this.volumetricMesh);

    // 初始化调试可视化
    if (this.config.useDebugVisualization) {
      this.initializeDebugVisualization();
    }

    // 监听窗口大小变化
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();
    Debug.log('UnderwaterVolumetricLightSystem', '水下体积光系统初始化');
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试容器
    const debugContainer = new THREE.Object3D();
    debugContainer.name = 'UnderwaterVolumetricLightDebug';

    // 添加到场景
    const activeScene = this.world.getActiveScene();
    if (activeScene) {
      const threeScene = (activeScene as any).getThreeScene();
      if (threeScene) {
        threeScene.add(debugContainer);
      }
    }

    // 创建调试平面
    const geometry = new THREE.PlaneGeometry(1, 1, 1, 1);
    const material = new THREE.MeshBasicMaterial({
      map: this.renderTarget.texture,
      transparent: true
    });
    const plane = new THREE.Mesh(geometry, material);
    plane.position.set(0.5, 0.5, 0);
    plane.scale.set(0.3, 0.3, 1);
    debugContainer.add(plane);
    this.debugObjects.push(plane);
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    // 更新渲染目标大小
    this.renderTarget.setSize(window.innerWidth, window.innerHeight);

    // 更新相机宽高比
    if (this.volumetricCamera instanceof THREE.PerspectiveCamera) {
      this.volumetricCamera.aspect = window.innerWidth / window.innerHeight;
      this.volumetricCamera.updateProjectionMatrix();
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    if (!this.isEnabled() || !this.config.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.useDebugVisualization) {
      this.performanceMonitor.beginMeasure('underwaterVolumetricLightUpdate');
    }

    // 检查是否在水下
    this.checkIfUnderwater();

    // 如果在水下，渲染体积光
    if (this.isUnderwater) {
      this.renderVolumetricLight();
    }

    // 更新调试可视化
    if (this.config.useDebugVisualization) {
      this.updateDebugVisualization();
      this.performanceMonitor.endMeasure('underwaterVolumetricLightUpdate');
    }
  }

  /**
   * 检查是否在水下
   */
  private checkIfUnderwater(): void {
    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      this.isUnderwater = false;
      return;
    }

    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 检查是否在任何水体内部
    let underwater = false;
    for (const [_entity, component] of this.waterEntities.entries()) {
      // 根据水体组件的尺寸创建包围盒
      const size = component.getSize();
      const position = component.getPosition();

      const boundingBox = new THREE.Box3(
        new THREE.Vector3(
          position.x - size.width / 2,
          position.y - size.height / 2,
          position.z - size.depth / 2
        ),
        new THREE.Vector3(
          position.x + size.width / 2,
          position.y + size.height / 2,
          position.z + size.depth / 2
        )
      );

      // 获取水面高度
      const waterHeight = position.y;

      // 检查相机是否在水体内部
      if (boundingBox.containsPoint(cameraPosition) && cameraPosition.y < waterHeight) {
        underwater = true;
        break;
      }
    }

    // 更新状态
    this.isUnderwater = underwater;
  }

  /**
   * 渲染体积光
   */
  private renderVolumetricLight(): void {
    // 获取渲染器
    const engine = this.world.getEngine();
    const renderer = engine ? engine.getRenderer() : null;
    if (!renderer) {
      return;
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 获取场景
    const activeScene = this.world.getActiveScene();
    if (!activeScene) {
      return;
    }

    const threeScene = (activeScene as any).getThreeScene();
    if (!threeScene) {
      return;
    }

    // 更新体积光相机
    this.updateVolumetricCamera(camera);

    // 更新光源
    this.updateLightSources(threeScene);

    // 渲染体积光
    for (const light of this.lightSources) {
      // 更新光源位置
      this.volumetricMaterial.uniforms.lightPosition.value.copy(light.position);

      // 获取THREE.js渲染器
      const threeRenderer = (renderer as any).getThreeRenderer();
      if (threeRenderer) {
        // 渲染体积光
        threeRenderer.setRenderTarget(this.renderTarget);
        threeRenderer.render(this.volumetricScene, this.volumetricCamera);
        threeRenderer.setRenderTarget(null);
      }
    }

    // 发出事件
    this.eventEmitter.emit(UnderwaterVolumetricLightEventType.VOLUMETRIC_LIGHT_UPDATED, {
      renderTarget: this.renderTarget,
      isUnderwater: this.isUnderwater
    });
  }

  /**
   * 更新体积光相机
   * @param camera 相机
   */
  private updateVolumetricCamera(camera: Camera): void {
    // 复制相机参数
    const threeCamera = camera.getThreeCamera();
    if (threeCamera instanceof THREE.PerspectiveCamera && this.volumetricCamera instanceof THREE.PerspectiveCamera) {
      this.volumetricCamera.fov = threeCamera.fov;
      this.volumetricCamera.aspect = threeCamera.aspect;
      this.volumetricCamera.near = threeCamera.near;
      this.volumetricCamera.far = threeCamera.far;
      this.volumetricCamera.updateProjectionMatrix();
    }

    // 复制相机位置和旋转
    this.volumetricCamera.position.copy(threeCamera.position);
    this.volumetricCamera.rotation.copy(threeCamera.rotation);
    this.volumetricCamera.quaternion.copy(threeCamera.quaternion);
  }

  /**
   * 更新光源
   * @param scene 场景
   */
  private updateLightSources(scene: THREE.Scene): void {
    // 清空光源列表
    this.lightSources = [];

    // 遍历场景中的所有光源
    scene.traverse((object) => {
      if (object instanceof THREE.DirectionalLight ||
          object instanceof THREE.PointLight ||
          object instanceof THREE.SpotLight) {
        // 检查光源是否可见
        if (object.visible) {
          this.lightSources.push(object);
        }
      }
    });
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // 简化实现：通过引擎获取活动相机
    const engine = this.world.getEngine();
    if (engine) {
      return engine.getActiveCamera();
    }
    return null;
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 更新调试平面的可见性
    if (this.debugObjects.length > 0) {
      this.debugObjects[0].visible = this.isUnderwater;
    }
  }

  /**
   * 添加水体实体
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterEntity(entity: Entity, component: WaterBodyComponent): void {
    this.waterEntities.set(entity, component);
  }

  /**
   * 移除水体实体
   * @param entity 实体
   */
  public removeWaterEntity(entity: Entity): void {
    this.waterEntities.delete(entity);
  }

  /**
   * 设置体积光参数
   * @param params 参数
   */
  public setVolumetricLightParams(params: Partial<UnderwaterVolumetricLightConfig>): void {
    // 更新配置
    if (params.density !== undefined) {
      this.config.density = params.density;
      this.volumetricMaterial.uniforms.density.value = params.density;
    }
    if (params.decay !== undefined) {
      this.config.decay = params.decay;
      this.volumetricMaterial.uniforms.decay.value = params.decay;
    }
    if (params.weight !== undefined) {
      this.config.weight = params.weight;
      this.volumetricMaterial.uniforms.weight.value = params.weight;
    }
    if (params.exposure !== undefined) {
      this.config.exposure = params.exposure;
      this.volumetricMaterial.uniforms.exposure.value = params.exposure;
    }
    if (params.samples !== undefined) {
      this.config.samples = params.samples;
      this.volumetricMaterial.uniforms.samples.value = params.samples;
    }
    if (params.color !== undefined) {
      this.config.color = params.color;
      this.volumetricMaterial.uniforms.color.value = params.color;
    }

    // 发出事件
    this.eventEmitter.emit(UnderwaterVolumetricLightEventType.VOLUMETRIC_LIGHT_PARAMS_CHANGED, this.config);
  }

  /**
   * 获取体积光顶点着色器
   * @returns 顶点着色器代码
   */
  private getVolumetricLightVertexShader(): string {
    return `
      varying vec2 vUv;

      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;
  }

  /**
   * 获取体积光片段着色器
   * @returns 片段着色器代码
   */
  private getVolumetricLightFragmentShader(): string {
    return `
      uniform sampler2D tDiffuse;
      uniform vec3 lightPosition;
      uniform float exposure;
      uniform float decay;
      uniform float density;
      uniform float weight;
      uniform int samples;
      uniform vec3 color;

      varying vec2 vUv;

      const int MAX_SAMPLES = 100;

      void main() {
        // 将光源位置从世界空间转换到屏幕空间
        vec2 lightPositionOnScreen = (lightPosition.xy + 1.0) * 0.5;

        // 计算从当前像素到光源的向量
        vec2 deltaTextCoord = vUv - lightPositionOnScreen;

        // 归一化向量
        deltaTextCoord *= 1.0 / float(samples) * density;

        // 初始位置
        vec2 coord = vUv;

        // 初始颜色
        vec3 result = vec3(0.0);

        // 初始权重
        float illuminationDecay = 1.0;

        // 采样光线
        for(int i=0; i<MAX_SAMPLES; i++) {
          if(i >= samples) {
            break;
          }

          // 更新位置
          coord -= deltaTextCoord;

          // 采样纹理
          vec3 texSample = texture2D(tDiffuse, coord).rgb;

          // 应用权重和衰减
          texSample *= illuminationDecay * weight;

          // 累加结果
          result += texSample;

          // 更新衰减
          illuminationDecay *= decay;
        }

        // 应用曝光和颜色
        result *= exposure * color;

        // 输出结果
        gl_FragColor = vec4(result, 1.0);
      }
    `;
  }

  /**
   * 获取渲染目标
   * @returns 渲染目标
   */
  public getRenderTarget(): THREE.WebGLRenderTarget {
    return this.renderTarget;
  }

  /**
   * 是否在水下
   * @returns 是否在水下
   */
  public isInUnderwater(): boolean {
    return this.isUnderwater;
  }
}
