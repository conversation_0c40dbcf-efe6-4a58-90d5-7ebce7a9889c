/**
 * 事务API服务
 */
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { TransactionCoordinatorService } from '../transaction-coordinator.service';
import { TransactionResult } from '../transaction.interface';

@Injectable()
export class TransactionApiService {
  private readonly logger = new Logger(TransactionApiService.name);
  
  constructor(
    private readonly transactionCoordinatorService: TransactionCoordinatorService,
  ) {}
  
  /**
   * 手动提交事务
   * @param transactionId 事务ID
   */
  async commitTransaction(transactionId: string): Promise<TransactionResult> {
    this.logger.log(`手动提交事务: ${transactionId}`);
    
    // 检查事务是否存在
    const transaction = await this.transactionCoordinatorService.getTransaction(transactionId);
    
    if (!transaction) {
      throw new NotFoundException(`事务 ${transactionId} 不存在`);
    }
    
    // 提交事务
    return this.transactionCoordinatorService.commitTransaction(transactionId);
  }
  
  /**
   * 手动回滚事务
   * @param transactionId 事务ID
   * @param reason 回滚原因
   */
  async rollbackTransaction(transactionId: string, reason: string): Promise<TransactionResult> {
    this.logger.log(`手动回滚事务: ${transactionId}, 原因: ${reason}`);
    
    // 检查事务是否存在
    const transaction = await this.transactionCoordinatorService.getTransaction(transactionId);
    
    if (!transaction) {
      throw new NotFoundException(`事务 ${transactionId} 不存在`);
    }
    
    // 回滚事务
    return this.transactionCoordinatorService.abortTransaction(transactionId, reason || '手动回滚');
  }
}
