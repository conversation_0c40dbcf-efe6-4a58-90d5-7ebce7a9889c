/**
 * Three.js 加载器类型定义
 */

declare module 'three/examples/jsm/loaders/GLTFLoader' {
  import { Object3D, LoadingManager } from 'three';

  export class GLTFLoader {
    constructor(manager?: LoadingManager);
    load(
      url: string,
      onLoad: (gltf: { scene: Object3D; animations: any[]; scenes: Object3D[]; cameras: any[]; asset: any }) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (error: ErrorEvent) => void
    ): void;
    setPath(path: string): GLTFLoader;
    setCrossOrigin(crossOrigin: string): GLTFLoader;
  }
}

declare module 'three/examples/jsm/loaders/FBXLoader' {
  import { Object3D, LoadingManager } from 'three';

  export class FBXLoader {
    constructor(manager?: LoadingManager);
    load(
      url: string,
      onLoad: (object: Object3D) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (error: ErrorEvent) => void
    ): void;
    setPath(path: string): FBXLoader;
    setCrossOrigin(crossOrigin: string): FBXLoader;
  }
}

declare module 'three/examples/jsm/loaders/OBJLoader' {
  import { Object3D, LoadingManager } from 'three';

  export class OBJLoader {
    constructor(manager?: LoadingManager);
    load(
      url: string,
      onLoad: (object: Object3D) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (error: ErrorEvent) => void
    ): void;
    setPath(path: string): OBJLoader;
    setCrossOrigin(crossOrigin: string): OBJLoader;
  }
}

declare module 'three/examples/jsm/loaders/FontLoader' {
  import { LoadingManager } from 'three';

  export class FontLoader {
    constructor(manager?: LoadingManager);
    load(
      url: string,
      onLoad: (font: any) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (error: ErrorEvent) => void
    ): void;
    setPath(path: string): FontLoader;
    setCrossOrigin(crossOrigin: string): FontLoader;
  }
}
