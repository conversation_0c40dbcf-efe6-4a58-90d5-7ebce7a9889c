/**
 * 空间分区系统
 * 用于加速软体物理的碰撞检测
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
/**
 * 空间分区单元格
 */
export declare class SpatialCell {
    /** 单元格索引 */
    index: THREE.Vector3;
    /** 单元格中的粒子 */
    particles: CANNON.Body[];
    /**
     * 创建空间分区单元格
     * @param x X索引
     * @param y Y索引
     * @param z Z索引
     */
    constructor(x: number, y: number, z: number);
    /**
     * 添加粒子
     * @param particle 粒子
     */
    addParticle(particle: CANNON.Body): void;
    /**
     * 移除粒子
     * @param particle 粒子
     */
    removeParticle(particle: CANNON.Body): void;
    /**
     * 清空单元格
     */
    clear(): void;
}
/**
 * 空间分区系统选项
 */
export interface SpatialPartitioningOptions {
    /** 单元格大小 */
    cellSize?: number;
    /** 世界边界最小点 */
    worldMin?: THREE.Vector3;
    /** 世界边界最大点 */
    worldMax?: THREE.Vector3;
}
/**
 * 空间分区系统
 * 使用均匀网格进行空间分区
 */
export declare class SpatialPartitioning {
    /** 单元格大小 */
    private cellSize;
    /** 世界边界最小点 */
    private worldMin;
    /** 世界边界最大点 */
    private worldMax;
    /** 网格尺寸 */
    private gridSize;
    /** 单元格映射 */
    private cells;
    /** 粒子到单元格的映射 */
    private particleCells;
    /**
     * 创建空间分区系统
     * @param options 空间分区系统选项
     */
    constructor(options?: SpatialPartitioningOptions);
    /**
     * 获取单元格键
     * @param x X索引
     * @param y Y索引
     * @param z Z索引
     * @returns 单元格键
     */
    private getCellKey;
    /**
     * 获取位置所在的单元格索引
     * @param position 位置
     * @returns 单元格索引
     */
    private getCellIndex;
    /**
     * 获取或创建单元格
     * @param index 单元格索引
     * @returns 单元格
     */
    private getOrCreateCell;
    /**
     * 更新粒子位置
     * @param particle 粒子
     */
    updateParticle(particle: CANNON.Body): void;
    /**
     * 添加粒子
     * @param particle 粒子
     */
    addParticle(particle: CANNON.Body): void;
    /**
     * 移除粒子
     * @param particle 粒子
     */
    removeParticle(particle: CANNON.Body): void;
    /**
     * 更新所有粒子
     * @param particles 粒子数组
     */
    updateAll(particles: CANNON.Body[]): void;
    /**
     * 获取附近的粒子
     * @param position 位置
     * @param radius 半径
     * @returns 附近的粒子
     */
    getNearbyParticles(position: CANNON.Vec3, radius: number): CANNON.Body[];
    /**
     * 清空所有单元格
     */
    clear(): void;
}
