/**
 * 虚拟纹理着色器
 * 用于实现虚拟纹理技术的着色器代码
 */

/**
 * 虚拟纹理顶点着色器
 */
export const virtualTextureVertexShader = `
// 输入属性
attribute vec3 position;
attribute vec2 uv;
attribute vec3 normal;

// 输出变量
varying vec2 vUv;
varying vec3 vNormal;
varying vec3 vPosition;
varying vec4 vViewPosition;

// 矩阵
uniform mat4 modelMatrix;
uniform mat4 viewMatrix;
uniform mat4 projectionMatrix;
uniform mat4 modelViewMatrix;
uniform mat3 normalMatrix;

void main() {
  // 计算顶点位置
  vec4 worldPosition = modelMatrix * vec4(position, 1.0);
  vec4 viewPosition = viewMatrix * worldPosition;

  // 传递变量
  vUv = uv;
  vNormal = normalize(normalMatrix * normal);
  vPosition = worldPosition.xyz;
  vViewPosition = viewPosition;

  // 输出裁剪空间位置
  gl_Position = projectionMatrix * viewPosition;
}
`;

/**
 * 虚拟纹理片段着色器
 */
export const virtualTextureFragmentShader = `
// 精度
precision highp float;
precision highp int;
precision highp sampler2D;

// 输入变量
varying vec2 vUv;
varying vec3 vNormal;
varying vec3 vPosition;
varying vec4 vViewPosition;

// 物理纹理
uniform sampler2D uPhysicalTexture;
uniform float uPhysicalTextureSize;

// 页面表
uniform sampler2D uPageTable;
uniform float uPageTableSize;

// 页面大小
uniform float uPageSize;
uniform float uPageBorderSize;

// MIP级别
uniform float uMaxMipLevel;
uniform float uMipBias;

// 调试
uniform bool uDebugMode;
uniform vec4 uDebugColor;

// 计算MIP级别
float calculateMipLevel(vec2 uv, vec2 textureSize) {
  // 计算纹理坐标的导数
  vec2 dx = dFdx(uv * textureSize);
  vec2 dy = dFdy(uv * textureSize);

  // 计算最大导数长度
  float maxLength = max(length(dx), length(dy));

  // 计算MIP级别
  float mipLevel = log2(maxLength);

  // 应用MIP偏移
  mipLevel += uMipBias;

  // 限制MIP级别范围
  mipLevel = clamp(mipLevel, 0.0, uMaxMipLevel);

  return mipLevel;
}

// 获取页面坐标
vec4 getPageCoord(vec2 uv, float mipLevel) {
  // 计算MIP级别的缩放因子
  float mipScale = pow(2.0, mipLevel);

  // 计算页面坐标
  float pageX = floor(uv.x * uPhysicalTextureSize / (uPageSize * mipScale));
  float pageY = floor(uv.y * uPhysicalTextureSize / (uPageSize * mipScale));

  // 计算页面内坐标
  float localX = fract(uv.x * uPhysicalTextureSize / (uPageSize * mipScale));
  float localY = fract(uv.y * uPhysicalTextureSize / (uPageSize * mipScale));

  return vec4(pageX, pageY, localX, localY);
}

// 获取页面表条目
vec4 getPageTableEntry(float pageX, float pageY, float mipLevel) {
  // 计算页面表坐标
  float u = (pageX + 0.5) / uPageTableSize;
  float v = (pageY + 0.5) / uPageTableSize;

  // 获取页面表条目
  return texture2D(uPageTable, vec2(u, v));
}

// 获取物理纹理坐标
vec2 getPhysicalTextureCoord(vec4 pageTableEntry, vec2 localUV) {
  // 解码页面表条目
  float physicalPageX = pageTableEntry.r * 255.0;
  float physicalPageY = pageTableEntry.g * 255.0;

  // 计算物理纹理坐标
  float u = (physicalPageX * uPageSize + localUV.x * (uPageSize - 2.0 * uPageBorderSize) + uPageBorderSize) / uPhysicalTextureSize;
  float v = (physicalPageY * uPageSize + localUV.y * (uPageSize - 2.0 * uPageBorderSize) + uPageBorderSize) / uPhysicalTextureSize;

  return vec2(u, v);
}

// 获取页面坐标
vec4 getPageCoord(vec2 uv, float mipLevel) {
  // 计算MIP级别的缩放因子
  float mipScale = pow(2.0, mipLevel);

  // 计算页面大小
  float scaledPageSize = uPageSize * mipScale;

  // 计算页面坐标
  float pageX = floor(uv.x * uPhysicalTextureSize / scaledPageSize);
  float pageY = floor(uv.y * uPhysicalTextureSize / scaledPageSize);

  // 计算页面内的局部坐标
  float localX = fract(uv.x * uPhysicalTextureSize / scaledPageSize);
  float localY = fract(uv.y * uPhysicalTextureSize / scaledPageSize);

  return vec4(pageX, pageY, localX, localY);
}

// 获取页面表条目
vec4 getPageTableEntry(float pageX, float pageY, float mipLevel) {
  // 计算页面表大小
  float pageTableSize = ceil(uPhysicalTextureSize / uPageSize);

  // 计算页面表坐标
  vec2 pageTableUV = vec2(
    (pageX + 0.5) / pageTableSize,
    (pageY + 0.5) / pageTableSize
  );

  // 采样页面表
  return texture2D(uPageTable, pageTableUV);
}

// 获取物理纹理坐标
vec2 getPhysicalTextureCoord(vec4 pageTableEntry, vec2 localUV) {
  // 解析页面表条目
  float physicalPageX = pageTableEntry.r;
  float physicalPageY = pageTableEntry.g;

  // 计算物理纹理坐标
  float borderSize = uPageBorderSize / uPageSize;
  vec2 physicalUV = vec2(
    (physicalPageX * uPageSize + localUV.x * uPageSize + uPageBorderSize) / uPhysicalTextureSize,
    (physicalPageY * uPageSize + localUV.y * uPageSize + uPageBorderSize) / uPhysicalTextureSize
  );

  return physicalUV;
}

// 采样虚拟纹理
vec4 sampleVirtualTexture(vec2 uv) {
  // 计算MIP级别
  float mipLevel = calculateMipLevel(uv, vec2(uPhysicalTextureSize));

  // 获取页面坐标
  vec4 pageCoord = getPageCoord(uv, mipLevel);
  float pageX = pageCoord.x;
  float pageY = pageCoord.y;
  vec2 localUV = pageCoord.zw;

  // 获取页面表条目
  vec4 pageTableEntry = getPageTableEntry(pageX, pageY, mipLevel);

  // 检查页面是否有效
  if (pageTableEntry.a < 0.5) {
    // 页面无效，尝试使用更低分辨率的MIP级别
    for (float i = 1.0; i <= uMaxMipLevel; i += 1.0) {
      float fallbackMip = mipLevel + i;
      if (fallbackMip > uMaxMipLevel) {
        break;
      }

      // 获取更低分辨率的页面坐标
      vec4 fallbackPageCoord = getPageCoord(uv, fallbackMip);
      float fallbackPageX = fallbackPageCoord.x;
      float fallbackPageY = fallbackPageCoord.y;
      vec2 fallbackLocalUV = fallbackPageCoord.zw;

      // 获取页面表条目
      vec4 fallbackPageTableEntry = getPageTableEntry(fallbackPageX, fallbackPageY, fallbackMip);

      // 如果找到有效页面，则使用它
      if (fallbackPageTableEntry.a >= 0.5) {
        // 获取物理纹理坐标
        vec2 fallbackPhysicalUV = getPhysicalTextureCoord(fallbackPageTableEntry, fallbackLocalUV);

        // 采样物理纹理
        return texture2D(uPhysicalTexture, fallbackPhysicalUV);
      }
    }

    // 如果所有MIP级别都无效，则返回紫色
    return vec4(1.0, 0.0, 1.0, 1.0); // 紫色表示缺失的页面
  }

  // 获取物理纹理坐标
  vec2 physicalUV = getPhysicalTextureCoord(pageTableEntry, localUV);

  // 采样物理纹理
  return texture2D(uPhysicalTexture, physicalUV);
}

void main() {
  // 采样虚拟纹理
  vec4 color = sampleVirtualTexture(vUv);

  // 调试模式
  if (uDebugMode) {
    // 计算MIP级别
    float mipLevel = calculateMipLevel(vUv, vec2(uPhysicalTextureSize));

    // 根据MIP级别着色
    float t = mipLevel / uMaxMipLevel;
    vec3 debugColor = mix(vec3(0.0, 1.0, 0.0), vec3(1.0, 0.0, 0.0), t);

    // 混合颜色
    color.rgb = mix(color.rgb, debugColor, 0.5);
  }

  // 输出颜色
  gl_FragColor = color;
}
`;

/**
 * 虚拟纹理反馈着色器
 */
export const virtualTextureFeedbackShader = `
// 精度
precision highp float;
precision highp int;
precision highp sampler2D;

// 输入变量
varying vec2 vUv;

// 页面大小
uniform float uPageSize;
uniform float uPhysicalTextureSize;
uniform float uMaxMipLevel;

// 计算MIP级别
float calculateMipLevel(vec2 uv, vec2 textureSize) {
  // 计算纹理坐标的导数
  vec2 dx = dFdx(uv * textureSize);
  vec2 dy = dFdy(uv * textureSize);

  // 计算最大导数长度
  float maxLength = max(length(dx), length(dy));

  // 计算MIP级别
  float mipLevel = log2(maxLength);

  // 应用MIP偏移
  mipLevel += uMipBias;

  // 限制MIP级别范围
  mipLevel = clamp(mipLevel, 0.0, uMaxMipLevel);

  return mipLevel;
}

void main() {
  // 计算MIP级别
  float mipLevel = calculateMipLevel(vUv, vec2(uPhysicalTextureSize));

  // 计算MIP级别的缩放因子
  float mipScale = pow(2.0, mipLevel);

  // 计算页面坐标
  float pageX = floor(vUv.x * uPhysicalTextureSize / (uPageSize * mipScale));
  float pageY = floor(vUv.y * uPhysicalTextureSize / (uPageSize * mipScale));

  // 输出页面请求信息
  gl_FragColor = vec4(
    pageX / 255.0,
    pageY / 255.0,
    mipLevel / uMaxMipLevel,
    1.0
  );
}
`;
