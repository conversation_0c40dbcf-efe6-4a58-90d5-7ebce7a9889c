/**
 * 动画状态机调试器
 * 用于调试和可视化动画状态机
 */
import { AnimationStateMachine } from './AnimationStateMachine';
/**
 * 调试事件类型
 */
export declare enum DebugEventType {
    /** 状态进入 */
    STATE_ENTER = "stateEnter",
    /** 状态退出 */
    STATE_EXIT = "stateExit",
    /** 状态转换开始 */
    TRANSITION_START = "transitionStart",
    /** 状态转换结束 */
    TRANSITION_END = "transitionEnd",
    /** 条件评估 */
    CONDITION_EVALUATE = "conditionEvaluate",
    /** 参数变化 */
    PARAMETER_CHANGE = "parameterChange"
}
/**
 * 调试事件
 */
export interface DebugEvent {
    /** 事件类型 */
    type: DebugEventType;
    /** 事件时间 */
    time: number;
    /** 事件数据 */
    data: any;
}
/**
 * 动画状态机调试器
 */
export declare class AnimationStateMachineDebugger {
    /** 状态机 */
    private stateMachine;
    /** 调试事件列表 */
    private events;
    /** 最大事件数量 */
    private maxEvents;
    /** 是否启用 */
    private enabled;
    /** 开始时间 */
    private startTime;
    /** 原始参数设置方法 */
    private originalSetParameter;
    /** 监听器映射 */
    private listeners;
    /**
     * 构造函数
     * @param stateMachine 状态机
     * @param maxEvents 最大事件数量
     */
    constructor(stateMachine: AnimationStateMachine, maxEvents?: number);
    /**
     * 设置事件监听器
     */
    private setupEventListeners;
    /**
     * 添加调试事件
     * @param type 事件类型
     * @param data 事件数据
     */
    private addEvent;
    /**
     * 触发监听器
     * @param event 事件
     */
    private triggerListeners;
    /**
     * 启用调试器
     */
    enable(): void;
    /**
     * 禁用调试器
     */
    disable(): void;
    /**
     * 获取是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 清空事件
     */
    clearEvents(): void;
    /**
     * 获取事件
     * @returns 事件列表
     */
    getEvents(): DebugEvent[];
    /**
     * 获取最近的事件
     * @param count 事件数量
     * @returns 事件列表
     */
    getRecentEvents(count?: number): DebugEvent[];
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: DebugEventType, listener: (event: DebugEvent) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: DebugEventType, listener: (event: DebugEvent) => void): void;
    /**
     * 销毁调试器
     */
    destroy(): void;
}
