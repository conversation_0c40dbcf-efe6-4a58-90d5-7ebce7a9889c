/**
 * WebRTC连接
 * 负责管理与其他用户的WebRTC点对点连接
 */
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkMessage } from './NetworkMessage';
import { Debug } from '../utils/Debug';
import { MessageSerializer } from './MessageSerializer';

/**
 * WebRTC连接状态
 */
export enum WebRTCConnectionState {
  /** 新建 */
  NEW = 'new',
  /** 正在连接 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 正在断开连接 */
  DISCONNECTING = 'disconnecting',
  /** 已断开连接 */
  DISCONNECTED = 'disconnected',
  /** 连接失败 */
  FAILED = 'failed',
  /** 连接关闭 */
  CLOSED = 'closed',
}

/**
 * WebRTC连接配置
 */
export interface WebRTCConnectionConfig {
  /** 是否启用数据通道 */
  enableDataChannel?: boolean;
  /** 是否启用音频 */
  enableAudio?: boolean;
  /** 是否启用视频 */
  enableVideo?: boolean;
  /** 是否启用屏幕共享 */
  enableScreenShare?: boolean;
  /** 数据通道配置 */
  dataChannelConfig?: RTCDataChannelInit;
  /** 音频约束 */
  audioConstraints?: MediaTrackConstraints;
  /** 视频约束 */
  videoConstraints?: MediaTrackConstraints;
  /** 是否使用压缩 */
  useCompression?: boolean;
}

/**
 * WebRTC连接
 * 负责管理与其他用户的WebRTC点对点连接
 */
export class WebRTCConnection extends EventEmitter {
  /** 对等连接 */
  private peerConnection: RTCPeerConnection | null = null;
  
  /** 数据通道 */
  private dataChannel: RTCDataChannel | null = null;
  
  /** 远程用户ID */
  private userId: string;
  
  /** ICE服务器配置 */
  private iceServers: RTCIceServer[];
  
  /** 连接状态 */
  private state: WebRTCConnectionState = WebRTCConnectionState.NEW;
  
  /** 配置选项 */
  private config: WebRTCConnectionConfig;
  
  /** 本地媒体流 */
  private localStream: MediaStream | null = null;
  
  /** 远程媒体流 */
  private remoteStream: MediaStream | null = null;
  
  /** 消息序列化器 */
  private messageSerializer: MessageSerializer;
  
  /** 是否是发起方 */
  private isInitiator: boolean = false;
  
  /** 是否已交换SDP */
  private sdpExchanged: boolean = false;
  
  /** 待处理的ICE候选 */
  private pendingCandidates: RTCIceCandidateInit[] = [];
  
  /** 心跳间隔（毫秒） */
  private heartbeatInterval: number = 10000;
  
  /** 心跳定时器ID */
  private heartbeatTimerId: number | null = null;
  
  /** 最后一次接收消息的时间戳 */
  private lastReceivedTime: number = 0;
  
  /** 消息队列 */
  private messageQueue: NetworkMessage[] = [];

  /**
   * 创建WebRTC连接
   * @param userId 远程用户ID
   * @param iceServers ICE服务器配置
   * @param config 连接配置
   */
  constructor(userId: string, iceServers: RTCIceServer[] = [], config: WebRTCConnectionConfig = {}) {
    super();
    
    this.userId = userId;
    this.iceServers = iceServers;
    
    // 默认配置
    this.config = {
      enableDataChannel: true,
      enableAudio: false,
      enableVideo: false,
      enableScreenShare: false,
      dataChannelConfig: {
        ordered: true,
      },
      audioConstraints: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
      videoConstraints: {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        frameRate: { ideal: 30 },
      },
      useCompression: true,
      ...config,
    };
    
    this.messageSerializer = new MessageSerializer(this.config.useCompression);
  }

  /**
   * 创建对等连接
   */
  public createConnection(): void {
    if (this.peerConnection) {
      return;
    }
    
    try {
      // 创建RTCPeerConnection
      this.peerConnection = new RTCPeerConnection({
        iceServers: this.iceServers,
      });
      
      // 设置事件监听器
      this.setupPeerConnectionListeners();
      
      // 如果启用数据通道，则创建数据通道
      if (this.config.enableDataChannel) {
        this.createDataChannel();
      }
      
      // 如果启用音频或视频，则获取媒体流
      if (this.config.enableAudio || this.config.enableVideo) {
        this.getLocalMedia();
      }
      
      this.state = WebRTCConnectionState.NEW;
      
      Debug.log('WebRTCConnection', `Created peer connection to user: ${this.userId}`);
    } catch (error) {
      Debug.error('WebRTCConnection', 'Failed to create peer connection:', error);
      this.state = WebRTCConnectionState.FAILED;
      this.emit('error', error);
    }
  }

  /**
   * 创建提议
   */
  public async createOffer(): Promise<void> {
    if (!this.peerConnection || this.state !== WebRTCConnectionState.NEW) {
      return;
    }
    
    this.isInitiator = true;
    this.state = WebRTCConnectionState.CONNECTING;
    
    try {
      // 创建提议
      const offer = await this.peerConnection.createOffer();
      
      // 设置本地描述
      await this.peerConnection.setLocalDescription(offer);
      
      // 触发提议事件
      this.emit('offer', offer);
      
      Debug.log('WebRTCConnection', `Created offer to user: ${this.userId}`);
    } catch (error) {
      Debug.error('WebRTCConnection', 'Failed to create offer:', error);
      this.state = WebRTCConnectionState.FAILED;
      this.emit('error', error);
    }
  }

  /**
   * 处理提议
   * @param offer 提议
   */
  public async handleOffer(offer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection) {
      return;
    }
    
    this.isInitiator = false;
    this.state = WebRTCConnectionState.CONNECTING;
    
    try {
      // 设置远程描述
      await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
      
      // 创建应答
      const answer = await this.peerConnection.createAnswer();
      
      // 设置本地描述
      await this.peerConnection.setLocalDescription(answer);
      
      // 触发应答事件
      this.emit('answer', answer);
      
      this.sdpExchanged = true;
      
      // 处理待处理的ICE候选
      this.processPendingCandidates();
      
      Debug.log('WebRTCConnection', `Handled offer from user: ${this.userId}`);
    } catch (error) {
      Debug.error('WebRTCConnection', 'Failed to handle offer:', error);
      this.state = WebRTCConnectionState.FAILED;
      this.emit('error', error);
    }
  }

  /**
   * 处理应答
   * @param answer 应答
   */
  public async handleAnswer(answer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection || !this.isInitiator) {
      return;
    }
    
    try {
      // 设置远程描述
      await this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
      
      this.sdpExchanged = true;
      
      // 处理待处理的ICE候选
      this.processPendingCandidates();
      
      Debug.log('WebRTCConnection', `Handled answer from user: ${this.userId}`);
    } catch (error) {
      Debug.error('WebRTCConnection', 'Failed to handle answer:', error);
      this.state = WebRTCConnectionState.FAILED;
      this.emit('error', error);
    }
  }

  /**
   * 处理ICE候选
   * @param candidate ICE候选
   */
  public async handleIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {
    if (!this.peerConnection) {
      return;
    }
    
    // 如果SDP尚未交换，则将候选添加到待处理列表
    if (!this.sdpExchanged) {
      this.pendingCandidates.push(candidate);
      return;
    }
    
    try {
      // 添加ICE候选
      await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
      
      Debug.log('WebRTCConnection', `Added ICE candidate from user: ${this.userId}`);
    } catch (error) {
      Debug.error('WebRTCConnection', 'Failed to add ICE candidate:', error);
      this.emit('error', error);
    }
  }

  /**
   * 发送消息
   * @param message 消息对象
   */
  public async send(message: NetworkMessage): Promise<void> {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
      // 添加到消息队列
      this.messageQueue.push(message);
      return;
    }

    try {
      // 序列化消息
      const data = await this.messageSerializer.serialize(message);

      // 发送消息
      if (typeof data === 'string') {
        this.dataChannel.send(data);
      } else {
        this.dataChannel.send(data);
      }
    } catch (error) {
      Debug.error('WebRTCConnection', 'Failed to send message:', error);
      this.emit('error', error);

      // 添加到消息队列
      this.messageQueue.push(message);
    }
  }

  /**
   * 断开连接
   */
  public async disconnect(): Promise<void> {
    this.state = WebRTCConnectionState.DISCONNECTING;
    
    // 停止心跳
    this.stopHeartbeat();
    
    // 关闭数据通道
    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }
    
    // 停止本地媒体流
    if (this.localStream) {
      this.localStream.getTracks().forEach((track) => track.stop());
      this.localStream = null;
    }
    
    // 关闭对等连接
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    
    this.state = WebRTCConnectionState.DISCONNECTED;
    this.emit('disconnected');
    
    Debug.log('WebRTCConnection', `Disconnected from user: ${this.userId}`);
  }

  /**
   * 更新连接
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 检查连接状态
    if (this.state === WebRTCConnectionState.CONNECTED) {
      // 检查是否长时间未收到消息
      const now = Date.now();
      const elapsed = now - this.lastReceivedTime;
      
      if (elapsed > this.heartbeatInterval * 3) {
        Debug.warn('WebRTCConnection', `No message received for ${elapsed}ms, connection may be lost`);
        
        // 尝试重新连接
        this.reconnect();
      }
    }
  }

  /**
   * 获取远程用户ID
   * @returns 远程用户ID
   */
  public getUserId(): string {
    return this.userId;
  }

  /**
   * 获取连接状态
   * @returns 连接状态
   */
  public getState(): WebRTCConnectionState {
    return this.state;
  }

  /**
   * 获取本地媒体流
   * @returns 本地媒体流
   */
  public getLocalStream(): MediaStream | null {
    return this.localStream;
  }

  /**
   * 获取远程媒体流
   * @returns 远程媒体流
   */
  public getRemoteStream(): MediaStream | null {
    return this.remoteStream;
  }

  /**
   * 设置心跳间隔
   * @param interval 心跳间隔（毫秒）
   */
  public setHeartbeatInterval(interval: number): void {
    this.heartbeatInterval = interval;
    
    // 如果已连接，则重新启动心跳
    if (this.state === WebRTCConnectionState.CONNECTED) {
      this.stopHeartbeat();
      this.startHeartbeat();
    }
  }

  /**
   * 设置对等连接事件监听器
   */
  private setupPeerConnectionListeners(): void {
    if (!this.peerConnection) {
      return;
    }
    
    // ICE候选事件
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        // 触发ICE候选事件
        this.emit('iceCandidate', event.candidate.toJSON());
      }
    };
    
    // ICE连接状态变化事件
    this.peerConnection.oniceconnectionstatechange = () => {
      Debug.log('WebRTCConnection', `ICE connection state changed to: ${this.peerConnection!.iceConnectionState}`);
      
      switch (this.peerConnection!.iceConnectionState) {
        case 'connected':
        case 'completed':
          if (this.state !== WebRTCConnectionState.CONNECTED) {
            this.state = WebRTCConnectionState.CONNECTED;
            this.lastReceivedTime = Date.now();
            
            // 启动心跳
            this.startHeartbeat();
            
            // 处理消息队列
            this.processMessageQueue();
            
            this.emit('connected');
          }
          break;
          
        case 'failed':
          this.state = WebRTCConnectionState.FAILED;
          this.emit('error', new Error('ICE connection failed'));
          
          // 尝试重新连接
          this.reconnect();
          break;
          
        case 'disconnected':
          // ICE连接可能暂时断开，等待重新连接
          Debug.warn('WebRTCConnection', 'ICE connection disconnected, waiting for reconnection');
          break;
          
        case 'closed':
          this.state = WebRTCConnectionState.CLOSED;
          this.emit('disconnected');
          break;
      }
    };
    
    // 连接状态变化事件
    this.peerConnection.onconnectionstatechange = () => {
      Debug.log('WebRTCConnection', `Connection state changed to: ${this.peerConnection!.connectionState}`);
      
      switch (this.peerConnection!.connectionState) {
        case 'connected':
          if (this.state !== WebRTCConnectionState.CONNECTED) {
            this.state = WebRTCConnectionState.CONNECTED;
            this.lastReceivedTime = Date.now();
            
            // 启动心跳
            this.startHeartbeat();
            
            // 处理消息队列
            this.processMessageQueue();
            
            this.emit('connected');
          }
          break;
          
        case 'failed':
          this.state = WebRTCConnectionState.FAILED;
          this.emit('error', new Error('Connection failed'));
          
          // 尝试重新连接
          this.reconnect();
          break;
          
        case 'closed':
          this.state = WebRTCConnectionState.CLOSED;
          this.emit('disconnected');
          break;
      }
    };
    
    // 数据通道事件
    this.peerConnection.ondatachannel = (event) => {
      this.dataChannel = event.channel;
      this.setupDataChannelListeners();
      
      Debug.log('WebRTCConnection', `Received data channel from user: ${this.userId}`);
    };
    
    // 媒体流事件
    this.peerConnection.ontrack = (event) => {
      if (!this.remoteStream) {
        this.remoteStream = new MediaStream();
      }
      
      // 添加轨道到远程媒体流
      event.streams[0].getTracks().forEach((track) => {
        this.remoteStream!.addTrack(track);
      });
      
      this.emit('track', event.track, this.remoteStream);
      
      Debug.log('WebRTCConnection', `Received ${event.track.kind} track from user: ${this.userId}`);
    };
  }

  /**
   * 设置数据通道事件监听器
   */
  private setupDataChannelListeners(): void {
    if (!this.dataChannel) {
      return;
    }
    
    // 打开事件
    this.dataChannel.onopen = () => {
      Debug.log('WebRTCConnection', `Data channel opened with user: ${this.userId}`);
      
      // 处理消息队列
      this.processMessageQueue();
    };
    
    // 关闭事件
    this.dataChannel.onclose = () => {
      Debug.log('WebRTCConnection', `Data channel closed with user: ${this.userId}`);
    };
    
    // 错误事件
    this.dataChannel.onerror = (event) => {
      Debug.error('WebRTCConnection', 'Data channel error:', event);
      this.emit('error', new Error('Data channel error'));
    };
    
    // 消息事件
    this.dataChannel.onmessage = async (event) => {
      this.lastReceivedTime = Date.now();

      try {
        // 反序列化消息
        const message = await this.messageSerializer.deserialize(event.data);

        // 如果是心跳消息，则不触发消息事件
        if (message.type === 'heartbeat') {
          return;
        }

        this.emit('message', message);
      } catch (error) {
        Debug.error('WebRTCConnection', 'Failed to deserialize message:', error);
        this.emit('error', error);
      }
    };
  }

  /**
   * 创建数据通道
   */
  private createDataChannel(): void {
    if (!this.peerConnection || this.dataChannel) {
      return;
    }
    
    try {
      // 创建数据通道
      this.dataChannel = this.peerConnection.createDataChannel('data', this.config.dataChannelConfig);
      
      // 设置数据通道事件监听器
      this.setupDataChannelListeners();
      
      Debug.log('WebRTCConnection', `Created data channel with user: ${this.userId}`);
    } catch (error) {
      Debug.error('WebRTCConnection', 'Failed to create data channel:', error);
      this.emit('error', error);
    }
  }

  /**
   * 获取本地媒体
   */
  private async getLocalMedia(): Promise<void> {
    try {
      const constraints: MediaStreamConstraints = {
        audio: this.config.enableAudio ? this.config.audioConstraints : false,
        video: this.config.enableVideo ? this.config.videoConstraints : false,
      };
      
      // 获取媒体流
      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
      
      // 添加轨道到对等连接
      this.localStream.getTracks().forEach((track) => {
        this.peerConnection!.addTrack(track, this.localStream!);
      });
      
      this.emit('localStream', this.localStream);
      
      Debug.log('WebRTCConnection', `Got local media stream with user: ${this.userId}`);
    } catch (error) {
      Debug.error('WebRTCConnection', 'Failed to get local media:', error);
      this.emit('error', error);
    }
  }

  /**
   * 处理待处理的ICE候选
   */
  private async processPendingCandidates(): Promise<void> {
    if (!this.peerConnection || !this.sdpExchanged) {
      return;
    }
    
    // 处理所有待处理的ICE候选
    for (const candidate of this.pendingCandidates) {
      try {
        await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
      } catch (error) {
        Debug.error('WebRTCConnection', 'Failed to add ICE candidate:', error);
      }
    }
    
    // 清空待处理列表
    this.pendingCandidates = [];
  }

  /**
   * 重新连接
   */
  private reconnect(): void {
    // 断开连接
    this.disconnect();
    
    // 创建新的连接
    this.createConnection();
    
    // 如果是发起方，则创建提议
    if (this.isInitiator) {
      this.createOffer();
    }
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (this.heartbeatTimerId !== null) {
      return;
    }
    
    this.heartbeatTimerId = window.setInterval(() => {
      this.sendHeartbeat();
    }, this.heartbeatInterval);
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimerId !== null) {
      clearInterval(this.heartbeatTimerId);
      this.heartbeatTimerId = null;
    }
  }

  /**
   * 发送心跳消息
   */
  private async sendHeartbeat(): Promise<void> {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
      return;
    }

    const message: NetworkMessage = {
      type: 'heartbeat',
      data: {
        timestamp: Date.now(),
      },
      timestamp: Date.now(),
    };

    try {
      // 序列化消息
      const data = await this.messageSerializer.serialize(message);

      // 发送消息
      if (typeof data === 'string') {
        this.dataChannel.send(data);
      } else {
        this.dataChannel.send(data);
      }
    } catch (error) {
      Debug.error('WebRTCConnection', 'Failed to send heartbeat:', error);
    }
  }

  /**
   * 处理消息队列
   */
  private async processMessageQueue(): Promise<void> {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open' || this.messageQueue.length === 0) {
      return;
    }

    // 复制消息队列
    const queue = [...this.messageQueue];
    this.messageQueue = [];

    // 发送队列中的消息
    for (const message of queue) {
      await this.send(message);
    }
  }
}
