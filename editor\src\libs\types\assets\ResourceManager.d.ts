/**
 * 资源管理器类
 * 负责资源的加载、缓存和释放
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 资产类型
 */
export declare enum AssetType {
    TEXTURE = "texture",
    MODEL = "model",
    MATERIAL = "material",
    AUDIO = "audio",
    FONT = "font",
    SHADER = "shader",
    JSON = "json",
    TEXT = "text",
    BINARY = "binary",
    CUBEMAP = "cubemap",
    UNKNOWN = "unknown"
}
/**
 * 资源状态
 */
export declare enum ResourceState {
    /** 未加载 */
    UNLOADED = "unloaded",
    /** 加载中 */
    LOADING = "loading",
    /** 已加载 */
    LOADED = "loaded",
    /** 加载失败 */
    ERROR = "error"
}
/**
 * 资源信息
 */
export interface ResourceInfo {
    /** 资源ID */
    id: string;
    /** 资源类型 */
    type: AssetType;
    /** 资源URL */
    url: string;
    /** 资源数据 */
    data: any;
    /** 资源状态 */
    state: ResourceState;
    /** 引用计数 */
    refCount: number;
    /** 上次访问时间 */
    lastAccessTime: number;
    /** 资源大小（字节） */
    size: number;
    /** 加载错误 */
    error?: Error;
}
/**
 * 资源管理器选项
 */
export interface ResourceManagerOptions {
    /** 最大缓存大小（字节） */
    maxCacheSize?: number;
    /** 缓存清理阈值（0-1） */
    cleanupThreshold?: number;
    /** 缓存清理间隔（毫秒） */
    cleanupInterval?: number;
    /** 是否启用自动清理 */
    autoCleanup?: boolean;
    /** 是否启用预加载 */
    enablePreload?: boolean;
    /** 最大并发加载数 */
    maxConcurrentLoads?: number;
}
/**
 * 资源管理器
 */
export declare class ResourceManager extends EventEmitter {
    /** 资源映射 */
    private resources;
    /** 加载中的资源 */
    private loadingResources;
    /** 最大缓存大小（字节） */
    private maxCacheSize;
    /** 当前缓存大小（字节） */
    private currentCacheSize;
    /** 缓存清理阈值（0-1） */
    private cleanupThreshold;
    /** 缓存清理间隔（毫秒） */
    private cleanupInterval;
    /** 是否启用自动清理 */
    private autoCleanup;
    /** 是否启用预加载 */
    private enablePreload;
    /** 最大并发加载数 */
    private maxConcurrentLoads;
    /** 当前并发加载数 */
    private currentConcurrentLoads;
    /** 加载队列 */
    private loadQueue;
    /** 清理定时器ID */
    private cleanupTimerId;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建资源管理器实例
     * @param options 资源管理器选项
     */
    constructor(options?: ResourceManagerOptions);
    /**
     * 初始化资源管理器
     */
    initialize(): void;
    /**
     * 加载资源
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @returns Promise，解析为资源数据
     */
    load(id: string, type: AssetType, url: string): Promise<any>;
    /**
     * 处理队列中的下一个加载请求
     */
    private processNextQueuedLoad;
    /**
     * 实际加载资源
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @returns Promise，解析为资源数据
     */
    private loadResource;
    /**
     * 更新资源状态
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @param state 资源状态
     * @param data 资源数据
     * @param error 加载错误
     */
    private updateResourceState;
    /**
     * 计算资源大小
     * @param data 资源数据
     * @returns 资源大小（字节）
     */
    private calculateResourceSize;
    /**
     * 释放资源
     * @param id 资源ID
     * @returns 是否成功释放
     */
    release(id: string): boolean;
    /**
     * 获取资源
     * @param id 资源ID
     * @returns 资源信息，如果不存在则返回null
     */
    getResource(id: string): ResourceInfo | null;
    /**
     * 获取资源数据
     * @param id 资源ID
     * @returns 资源数据，如果不存在或未加载则返回null
     */
    getResourceData(id: string): any;
    /**
     * 预加载资源
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @returns Promise，解析为资源数据
     */
    preload(id: string, type: AssetType, url: string): Promise<any>;
    /**
     * 批量预加载资源
     * @param resources 资源数组
     * @param onProgress 进度回调
     * @returns Promise，解析为资源数据映射
     */
    preloadBatch(resources: Array<{
        id: string;
        type: AssetType;
        url: string;
    }>, onProgress?: (loaded: number, total: number) => void): Promise<Map<string, any>>;
    /**
     * 清理缓存
     */
    cleanup(): void;
    /**
     * 启动清理定时器
     */
    private startCleanupTimer;
    /**
     * 停止清理定时器
     */
    private stopCleanupTimer;
    /**
     * 获取缓存统计信息
     * @returns 缓存统计信息
     */
    getCacheStats(): {
        totalResources: number;
        loadedResources: number;
        currentCacheSize: number;
        maxCacheSize: number;
        usagePercentage: number;
    };
    /**
     * 清空缓存
     */
    clearCache(): void;
    /**
     * 销毁资源管理器
     */
    dispose(): void;
}
