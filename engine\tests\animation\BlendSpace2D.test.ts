/**
 * BlendSpace2D单元测试
 */
import { describe, it, expect, beforeEach } from 'vitest';
import { BlendSpace2D } from '../../src/animation/BlendSpace2D';
import { AnimationClip, LoopMode } from '../../src/animation/AnimationClip';
import * as THREE from 'three';

describe('BlendSpace2D', () => {
  let blendSpace: BlendSpace2D;
  let clip1: AnimationClip;
  let clip2: AnimationClip;
  let clip3: AnimationClip;
  let clip4: AnimationClip;
  let clip5: AnimationClip;

  beforeEach(() => {
    // 创建测试用的混合空间
    blendSpace = new BlendSpace2D({
      minX: -1,
      maxX: 1,
      minY: -1,
      maxY: 1
    });

    // 创建测试用的动画片段
    clip1 = new AnimationClip('clip1', 1.0);
    clip2 = new AnimationClip('clip2', 1.0);
    clip3 = new AnimationClip('clip3', 1.0);
    clip4 = new AnimationClip('clip4', 1.0);
    clip5 = new AnimationClip('clip5', 1.0);
  });

  it('应该正确创建混合空间', () => {
    expect(blendSpace).toBeDefined();
    expect(blendSpace.getPosition().x).toBe(0);
    expect(blendSpace.getPosition().y).toBe(0);
    expect(blendSpace.getNodes().length).toBe(0);
  });

  it('应该能够添加和移除节点', () => {
    // 添加节点
    const node1 = blendSpace.addNode(clip1, new THREE.Vector2(-1, -1));
    const node2 = blendSpace.addNode(clip2, new THREE.Vector2(1, -1));
    const node3 = blendSpace.addNode(clip3, new THREE.Vector2(0, 1));
    const node4 = blendSpace.addNode(clip4, new THREE.Vector2(-0.5, 0.5));
    const node5 = blendSpace.addNode(clip5, new THREE.Vector2(0.5, 0.5));

    // 验证节点添加成功
    expect(blendSpace.getNodes().length).toBe(5);
    expect(blendSpace.getNodes()[0]).toBe(node1);
    expect(blendSpace.getNodes()[1]).toBe(node2);
    expect(blendSpace.getNodes()[2]).toBe(node3);
    expect(blendSpace.getNodes()[3]).toBe(node4);
    expect(blendSpace.getNodes()[4]).toBe(node5);

    // 移除节点
    const result = blendSpace.removeNode(node3);
    expect(result).toBe(true);
    expect(blendSpace.getNodes().length).toBe(4);
    expect(blendSpace.getNodes()[0]).toBe(node1);
    expect(blendSpace.getNodes()[1]).toBe(node2);
    expect(blendSpace.getNodes()[2]).toBe(node4);
    expect(blendSpace.getNodes()[3]).toBe(node5);

    // 尝试移除不存在的节点
    const fakeNode = { clip: clip1, position: new THREE.Vector2(0, 0), weight: 0 };
    const result2 = blendSpace.removeNode(fakeNode as any);
    expect(result2).toBe(false);
  });

  it('应该能够设置和获取位置', () => {
    // 设置位置
    blendSpace.setPosition(0.5, 0.7);
    const position = blendSpace.getPosition();
    expect(position.x).toBe(0.5);
    expect(position.y).toBe(0.7);

    // 设置超出范围的位置
    blendSpace.setPosition(2, 3);
    const clampedPosition = blendSpace.getPosition();
    expect(clampedPosition.x).toBe(1); // 应该被限制在最大值
    expect(clampedPosition.y).toBe(1); // 应该被限制在最大值

    blendSpace.setPosition(-2, -3);
    const clampedPosition2 = blendSpace.getPosition();
    expect(clampedPosition2.x).toBe(-1); // 应该被限制在最小值
    expect(clampedPosition2.y).toBe(-1); // 应该被限制在最小值
  });

  it('应该能够正确更新混合权重（使用三角形混合）', () => {
    // 添加三个节点形成三角形
    const node1 = blendSpace.addNode(clip1, new THREE.Vector2(-1, -1));
    const node2 = blendSpace.addNode(clip2, new THREE.Vector2(1, -1));
    const node3 = blendSpace.addNode(clip3, new THREE.Vector2(0, 1));

    // 设置位置在三角形内部并更新
    blendSpace.setPosition(0, 0);
    blendSpace.update();

    // 验证权重
    // 在三角形中心，三个节点权重应该大致相等
    expect(node1.weight).toBeGreaterThan(0);
    expect(node2.weight).toBeGreaterThan(0);
    expect(node3.weight).toBeGreaterThan(0);
    expect(node1.weight + node2.weight + node3.weight).toBeCloseTo(1);

    // 设置位置接近node1并更新
    blendSpace.setPosition(-0.8, -0.8);
    blendSpace.update();

    // 验证权重
    // 接近node1，node1的权重应该最大
    expect(node1.weight).toBeGreaterThan(node2.weight);
    expect(node1.weight).toBeGreaterThan(node3.weight);
    expect(node1.weight + node2.weight + node3.weight).toBeCloseTo(1);
  });

  it('应该能够正确更新混合权重（使用距离加权）', () => {
    // 创建不使用三角形混合的混合空间
    const distanceBlendSpace = new BlendSpace2D({
      minX: -1,
      maxX: 1,
      minY: -1,
      maxY: 1,
      useTriangulation: false
    });

    // 添加四个节点
    const node1 = distanceBlendSpace.addNode(clip1, new THREE.Vector2(-1, -1));
    const node2 = distanceBlendSpace.addNode(clip2, new THREE.Vector2(1, -1));
    const node3 = distanceBlendSpace.addNode(clip3, new THREE.Vector2(-1, 1));
    const node4 = distanceBlendSpace.addNode(clip4, new THREE.Vector2(1, 1));

    // 设置位置在中心并更新
    distanceBlendSpace.setPosition(0, 0);
    distanceBlendSpace.update();

    // 验证权重
    // 在中心，四个节点权重应该大致相等
    expect(node1.weight).toBeCloseTo(0.25, 1);
    expect(node2.weight).toBeCloseTo(0.25, 1);
    expect(node3.weight).toBeCloseTo(0.25, 1);
    expect(node4.weight).toBeCloseTo(0.25, 1);
    expect(node1.weight + node2.weight + node3.weight + node4.weight).toBeCloseTo(1);

    // 设置位置接近node1并更新
    distanceBlendSpace.setPosition(-0.8, -0.8);
    distanceBlendSpace.update();

    // 验证权重
    // 接近node1，node1的权重应该最大
    expect(node1.weight).toBeGreaterThan(node2.weight);
    expect(node1.weight).toBeGreaterThan(node3.weight);
    expect(node1.weight).toBeGreaterThan(node4.weight);
    expect(node1.weight + node2.weight + node3.weight + node4.weight).toBeCloseTo(1);
  });

  it('应该能够处理边界情况', () => {
    // 没有节点的情况
    blendSpace.setPosition(0.5, 0.5);
    blendSpace.update();
    expect(blendSpace.getActiveNodes().length).toBe(0);

    // 只有一个节点的情况
    const node1 = blendSpace.addNode(clip1, new THREE.Vector2(0, 0));
    blendSpace.update();
    expect(node1.weight).toBe(1);
    expect(blendSpace.getActiveNodes().length).toBe(1);

    // 位置在节点上的情况
    const node2 = blendSpace.addNode(clip2, new THREE.Vector2(1, 1));
    blendSpace.setPosition(1, 1);
    blendSpace.update();
    expect(node1.weight).toBe(0);
    expect(node2.weight).toBe(1);
    expect(blendSpace.getActiveNodes().length).toBe(1);
  });

  it('应该能够获取活跃节点', () => {
    // 添加节点
    const node1 = blendSpace.addNode(clip1, new THREE.Vector2(-1, -1));
    const node2 = blendSpace.addNode(clip2, new THREE.Vector2(1, -1));
    const node3 = blendSpace.addNode(clip3, new THREE.Vector2(0, 1));

    // 设置位置并更新
    blendSpace.setPosition(0, 0);
    blendSpace.update();

    // 验证活跃节点
    const activeNodes = blendSpace.getActiveNodes();
    expect(activeNodes.length).toBe(3);
    expect(activeNodes).toContain(node1);
    expect(activeNodes).toContain(node2);
    expect(activeNodes).toContain(node3);

    // 设置位置在一个节点上并更新
    blendSpace.setPosition(-1, -1);
    blendSpace.update();

    // 验证活跃节点
    const activeNodes2 = blendSpace.getActiveNodes();
    expect(activeNodes2.length).toBe(1);
    expect(activeNodes2[0]).toBe(node1);
  });

  it('应该能够处理点在三角形内的检测', () => {
    // 这个测试直接测试私有方法pointInTriangle
    // 由于是私有方法，我们通过测试其行为来间接验证
    
    // 添加三个节点形成三角形
    const node1 = blendSpace.addNode(clip1, new THREE.Vector2(-1, -1));
    const node2 = blendSpace.addNode(clip2, new THREE.Vector2(1, -1));
    const node3 = blendSpace.addNode(clip3, new THREE.Vector2(0, 1));

    // 点在三角形内
    blendSpace.setPosition(0, 0);
    blendSpace.update();
    expect(node1.weight).toBeGreaterThan(0);
    expect(node2.weight).toBeGreaterThan(0);
    expect(node3.weight).toBeGreaterThan(0);

    // 点在三角形外
    blendSpace.setPosition(0, -2);
    blendSpace.update();
    // 在三角形外，可能使用距离加权或其他方法
    // 我们只需验证权重总和为1
    expect(node1.weight + node2.weight + node3.weight).toBeCloseTo(1);
  });
});
