import { IsString, IsOptional, <PERSON><PERSON><PERSON>, Max<PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum KnowledgeBaseCategory {
  GENERAL = 'general',
  TECHNICAL = 'technical',
  BUSINESS = 'business',
  EDUCATION = 'education',
  ENTERTAINMENT = 'entertainment',
}

export class CreateKnowledgeBaseDto {
  @ApiProperty({ description: '知识库名称' })
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiProperty({ description: '知识库描述', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ 
    description: '知识库分类', 
    enum: KnowledgeBaseCategory,
    default: KnowledgeBaseCategory.GENERAL 
  })
  @IsOptional()
  @IsEnum(KnowledgeBaseCategory)
  category?: KnowledgeBaseCategory = KnowledgeBaseCategory.GENERAL;

  @ApiProperty({ description: '语言', default: 'zh-CN' })
  @IsOptional()
  @IsString()
  language?: string = 'zh-CN';

  @ApiProperty({ description: '是否公开', default: false })
  @IsOptional()
  isPublic?: boolean = false;
}
