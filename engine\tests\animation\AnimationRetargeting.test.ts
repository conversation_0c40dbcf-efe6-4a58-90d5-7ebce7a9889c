/**
 * AnimationRetargeting单元测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AnimationRetargeting } from '../../src/animation/AnimationRetargeting';
import * as THREE from 'three';
import { Entity } from '../../core/Entity';

// 创建模拟骨骼
function createMockBone(name: string, position: THREE.Vector3, parent?: THREE.Bone): THREE.Bone {
  const bone = new THREE.Bone();
  bone.name = name;
  bone.position.copy(position);
  
  if (parent) {
    parent.add(bone);
  }
  
  return bone;
}

// 创建模拟动画片段
function createMockAnimationClip(name: string, duration: number, tracks: THREE.KeyframeTrack[]): THREE.AnimationClip {
  return new THREE.AnimationClip(name, duration, tracks);
}

describe('AnimationRetargeting', () => {
  let sourceSkeleton: THREE.Bone[];
  let targetSkeleton: THREE.Bone[];
  let sourceClip: THREE.AnimationClip;
  
  beforeEach(() => {
    // 创建源骨骼
    const sourceRoot = createMockBone('sourceRoot', new THREE.Vector3(0, 0, 0));
    const sourceHips = createMockBone('sourceHips', new THREE.Vector3(0, 1, 0), sourceRoot);
    const sourceSpine = createMockBone('sourceSpine', new THREE.Vector3(0, 0.2, 0), sourceHips);
    const sourceChest = createMockBone('sourceChest', new THREE.Vector3(0, 0.2, 0), sourceSpine);
    const sourceNeck = createMockBone('sourceNeck', new THREE.Vector3(0, 0.2, 0), sourceChest);
    const sourceHead = createMockBone('sourceHead', new THREE.Vector3(0, 0.1, 0), sourceNeck);
    
    const sourceLeftShoulder = createMockBone('sourceLeftShoulder', new THREE.Vector3(-0.1, 0, 0), sourceChest);
    const sourceLeftArm = createMockBone('sourceLeftArm', new THREE.Vector3(-0.1, 0, 0), sourceLeftShoulder);
    const sourceLeftForeArm = createMockBone('sourceLeftForeArm', new THREE.Vector3(-0.2, 0, 0), sourceLeftArm);
    const sourceLeftHand = createMockBone('sourceLeftHand', new THREE.Vector3(-0.1, 0, 0), sourceLeftForeArm);
    
    const sourceRightShoulder = createMockBone('sourceRightShoulder', new THREE.Vector3(0.1, 0, 0), sourceChest);
    const sourceRightArm = createMockBone('sourceRightArm', new THREE.Vector3(0.1, 0, 0), sourceRightShoulder);
    const sourceRightForeArm = createMockBone('sourceRightForeArm', new THREE.Vector3(0.2, 0, 0), sourceRightArm);
    const sourceRightHand = createMockBone('sourceRightHand', new THREE.Vector3(0.1, 0, 0), sourceRightForeArm);
    
    sourceSkeleton = [
      sourceRoot, sourceHips, sourceSpine, sourceChest, sourceNeck, sourceHead,
      sourceLeftShoulder, sourceLeftArm, sourceLeftForeArm, sourceLeftHand,
      sourceRightShoulder, sourceRightArm, sourceRightForeArm, sourceRightHand
    ];
    
    // 创建目标骨骼
    const targetRoot = createMockBone('targetRoot', new THREE.Vector3(0, 0, 0));
    const targetHips = createMockBone('targetHips', new THREE.Vector3(0, 0.9, 0), targetRoot);
    const targetSpine = createMockBone('targetSpine', new THREE.Vector3(0, 0.15, 0), targetHips);
    const targetChest = createMockBone('targetChest', new THREE.Vector3(0, 0.15, 0), targetSpine);
    const targetNeck = createMockBone('targetNeck', new THREE.Vector3(0, 0.15, 0), targetChest);
    const targetHead = createMockBone('targetHead', new THREE.Vector3(0, 0.08, 0), targetNeck);
    
    const targetLeftShoulder = createMockBone('targetLeftShoulder', new THREE.Vector3(-0.08, 0, 0), targetChest);
    const targetLeftArm = createMockBone('targetLeftArm', new THREE.Vector3(-0.08, 0, 0), targetLeftShoulder);
    const targetLeftForeArm = createMockBone('targetLeftForeArm', new THREE.Vector3(-0.15, 0, 0), targetLeftArm);
    const targetLeftHand = createMockBone('targetLeftHand', new THREE.Vector3(-0.08, 0, 0), targetLeftForeArm);
    
    const targetRightShoulder = createMockBone('targetRightShoulder', new THREE.Vector3(0.08, 0, 0), targetChest);
    const targetRightArm = createMockBone('targetRightArm', new THREE.Vector3(0.08, 0, 0), targetRightShoulder);
    const targetRightForeArm = createMockBone('targetRightForeArm', new THREE.Vector3(0.15, 0, 0), targetRightArm);
    const targetRightHand = createMockBone('targetRightHand', new THREE.Vector3(0.08, 0, 0), targetRightForeArm);
    
    targetSkeleton = [
      targetRoot, targetHips, targetSpine, targetChest, targetNeck, targetHead,
      targetLeftShoulder, targetLeftArm, targetLeftForeArm, targetLeftHand,
      targetRightShoulder, targetRightArm, targetRightForeArm, targetRightHand
    ];
    
    // 更新骨骼矩阵
    sourceRoot.updateWorldMatrix(true, true);
    targetRoot.updateWorldMatrix(true, true);
    
    // 创建源动画片段
    const times = [0, 1, 2];
    const positions = [
      0, 1, 0,  // t=0
      0, 1.1, 0,  // t=1
      0, 1, 0   // t=2
    ];
    const rotations = [
      0, 0, 0, 1,  // t=0
      0.1, 0, 0, 0.99,  // t=1
      0, 0, 0, 1   // t=2
    ];
    
    const positionTrack = new THREE.VectorKeyframeTrack(
      'sourceHips.position',
      times,
      positions
    );
    
    const rotationTrack = new THREE.QuaternionKeyframeTrack(
      'sourceHips.quaternion',
      times,
      rotations
    );
    
    sourceClip = createMockAnimationClip('testAnimation', 2, [positionTrack, rotationTrack]);
  });
  
  it('应该正确重定向动画片段', () => {
    // 创建骨骼映射
    const boneMapping = [
      { source: 'sourceHips', target: 'targetHips' },
      { source: 'sourceSpine', target: 'targetSpine' },
      { source: 'sourceChest', target: 'targetChest' },
      { source: 'sourceNeck', target: 'targetNeck' },
      { source: 'sourceHead', target: 'targetHead' },
      { source: 'sourceLeftShoulder', target: 'targetLeftShoulder' },
      { source: 'sourceLeftArm', target: 'targetLeftArm' },
      { source: 'sourceLeftForeArm', target: 'targetLeftForeArm' },
      { source: 'sourceLeftHand', target: 'targetLeftHand' },
      { source: 'sourceRightShoulder', target: 'targetRightShoulder' },
      { source: 'sourceRightArm', target: 'targetRightArm' },
      { source: 'sourceRightForeArm', target: 'targetRightForeArm' },
      { source: 'sourceRightHand', target: 'targetRightHand' }
    ];
    
    // 重定向动画
    const retargetedClip = AnimationRetargeting.retargetClip(
      sourceClip,
      sourceSkeleton,
      targetSkeleton,
      {
        boneMapping,
        preservePositionTracks: true,
        preserveScaleTracks: false,
        normalizeRotations: true,
        adjustRootHeight: true,
        adjustBoneLength: true
      }
    );
    
    // 验证重定向结果
    expect(retargetedClip).toBeDefined();
    expect(retargetedClip.name).toBe(sourceClip.name);
    expect(retargetedClip.duration).toBe(sourceClip.duration);
    
    // 验证轨道
    expect(retargetedClip.tracks.length).toBe(2);
    
    // 验证位置轨道
    const positionTrack = retargetedClip.tracks.find(track => track.name === 'targetHips.position');
    expect(positionTrack).toBeDefined();
    expect(positionTrack instanceof THREE.VectorKeyframeTrack).toBe(true);
    
    // 验证旋转轨道
    const rotationTrack = retargetedClip.tracks.find(track => track.name === 'targetHips.quaternion');
    expect(rotationTrack).toBeDefined();
    expect(rotationTrack instanceof THREE.QuaternionKeyframeTrack).toBe(true);
  });
  
  it('应该正确处理骨骼长度调整', () => {
    // 创建骨骼映射
    const boneMapping = [
      { source: 'sourceLeftArm', target: 'targetLeftArm' },
      { source: 'sourceLeftForeArm', target: 'targetLeftForeArm' }
    ];
    
    // 创建源动画片段
    const times = [0, 1];
    const positions = [
      -0.1, 0, 0,  // t=0
      -0.2, 0, 0   // t=1
    ];
    
    const positionTrack = new THREE.VectorKeyframeTrack(
      'sourceLeftForeArm.position',
      times,
      positions
    );
    
    const sourceClip = createMockAnimationClip('armAnimation', 1, [positionTrack]);
    
    // 重定向动画，启用骨骼长度调整
    const retargetedClip = AnimationRetargeting.retargetClip(
      sourceClip,
      sourceSkeleton,
      targetSkeleton,
      {
        boneMapping,
        preservePositionTracks: true,
        preserveScaleTracks: false,
        normalizeRotations: true,
        adjustRootHeight: false,
        adjustBoneLength: true
      }
    );
    
    // 验证重定向结果
    expect(retargetedClip).toBeDefined();
    
    // 验证位置轨道
    const positionTrack2 = retargetedClip.tracks.find(track => track.name === 'targetLeftForeArm.position');
    expect(positionTrack2).toBeDefined();
    
    // 验证骨骼长度调整
    // 源骨骼长度比例为 0.2 / 0.15 = 1.33
    // 因此，调整后的位置应该按比例缩放
    const values = (positionTrack2 as THREE.VectorKeyframeTrack).values;
    
    // 由于骨骼长度调整是复杂的计算，我们只验证值是否在合理范围内
    expect(Math.abs(values[0])).toBeLessThan(Math.abs(positions[0]));
    expect(Math.abs(values[3])).toBeLessThan(Math.abs(positions[3]));
  });
  
  it('应该正确处理根骨骼高度调整', () => {
    // 创建骨骼映射
    const boneMapping = [
      { source: 'sourceHips', target: 'targetHips' }
    ];
    
    // 创建源动画片段
    const times = [0, 1];
    const positions = [
      0, 1, 0,  // t=0
      0, 1.1, 0   // t=1
    ];
    
    const positionTrack = new THREE.VectorKeyframeTrack(
      'sourceHips.position',
      times,
      positions
    );
    
    const sourceClip = createMockAnimationClip('hipsAnimation', 1, [positionTrack]);
    
    // 重定向动画，启用根骨骼高度调整
    const retargetedClip = AnimationRetargeting.retargetClip(
      sourceClip,
      sourceSkeleton,
      targetSkeleton,
      {
        boneMapping,
        preservePositionTracks: true,
        preserveScaleTracks: false,
        normalizeRotations: true,
        adjustRootHeight: true,
        adjustBoneLength: false
      }
    );
    
    // 验证重定向结果
    expect(retargetedClip).toBeDefined();
    
    // 验证位置轨道
    const positionTrack2 = retargetedClip.tracks.find(track => track.name === 'targetHips.position');
    expect(positionTrack2).toBeDefined();
    
    // 验证根骨骼高度调整
    // 源骨骼高度为1，目标骨骼高度为0.9
    // 因此，调整后的Y值应该减少0.1
    const values = (positionTrack2 as THREE.VectorKeyframeTrack).values;
    
    // 由于高度调整是复杂的计算，我们只验证值是否在合理范围内
    expect(values[1]).toBeLessThan(positions[1]);
    expect(values[4]).toBeLessThan(positions[4]);
  });
  
  it('应该从实体中获取骨骼映射', () => {
    // 模拟Entity和AvatarRigComponent
    const entity = new Entity();
    
    // 模拟AvatarRigComponent
    const rigComponent = {
      entitiesToBones: {
        'entity1': 'bone1',
        'entity2': 'bone2'
      }
    };
    
    // 模拟getComponent方法
    entity.getComponent = vi.fn().mockImplementation((type) => {
      if (type === 'AvatarRig') {
        return rigComponent;
      }
      return null;
    });
    
    // 获取骨骼映射
    const boneMapping = AnimationRetargeting.getBoneMappingFromEntity(entity);
    
    // 验证结果
    expect(boneMapping).toBeDefined();
    expect(boneMapping.size).toBe(2);
    expect(boneMapping.get('entity1')).toBe('bone1');
    expect(boneMapping.get('entity2')).toBe('bone2');
  });
});
