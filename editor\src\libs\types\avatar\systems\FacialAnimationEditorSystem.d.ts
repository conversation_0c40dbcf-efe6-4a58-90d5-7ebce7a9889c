/**
 * 面部动画编辑器系统
 * 用于编辑和管理面部动画
 */
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';
import { FacialAnimationEditorComponent } from '../components/FacialAnimationEditorComponent';
import { FacialAnimationClip } from '../animation/FacialAnimationClip';
/**
 * 面部动画编辑器系统配置
 */
export interface FacialAnimationEditorSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 自动保存间隔（毫秒） */
    autoSaveInterval?: number;
    /** 是否启用撤销/重做 */
    enableUndoRedo?: boolean;
    /** 最大撤销步数 */
    maxUndoSteps?: number;
}
/**
 * 面部动画编辑器系统
 */
export declare class FacialAnimationEditorSystem extends System {
    /** 系统名称 */
    static readonly NAME = "FacialAnimationEditorSystem";
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 编辑器组件映射 */
    private editors;
    /** 当前选中的编辑器 */
    private activeEditor;
    /** 自动保存计时器 */
    private autoSaveTimer;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: FacialAnimationEditorSystemConfig);
    /**
     * 创建编辑器
     * @param entity 实体
     * @returns 编辑器组件
     */
    createEditor(entity: Entity): FacialAnimationEditorComponent;
    /**
     * 获取编辑器
     * @param entity 实体
     * @returns 编辑器组件
     */
    getEditor(entity: Entity): FacialAnimationEditorComponent | null;
    /**
     * 移除编辑器
     * @param entity 实体
     */
    removeEditor(entity: Entity): void;
    /**
     * 设置活动编辑器
     * @param entity 实体
     * @returns 是否成功设置
     */
    setActiveEditor(entity: Entity): boolean;
    /**
     * 获取活动编辑器
     * @returns 活动编辑器
     */
    getActiveEditor(): FacialAnimationEditorComponent | null;
    /**
     * 创建新动画片段
     * @param entity 实体
     * @param name 名称
     * @param duration 持续时间（秒）
     * @returns 动画片段
     */
    createClip(entity: Entity, name: string, duration?: number): FacialAnimationClip | null;
    /**
     * 导入动画片段
     * @param entity 实体
     * @param json JSON对象
     * @returns 动画片段
     */
    importClipFromJSON(entity: Entity, json: any): FacialAnimationClip | null;
    /**
     * 导出动画片段为JSON
     * @param entity 实体
     * @param clipName 片段名称
     * @returns JSON对象
     */
    exportClipToJSON(entity: Entity, clipName: string): any | null;
    /**
     * 播放动画片段
     * @param entity 实体
     * @param clipName 片段名称
     * @returns 是否成功播放
     */
    playClip(entity: Entity, clipName?: string): boolean;
    /**
     * 停止播放
     * @param entity 实体
     * @returns 是否成功停止
     */
    stopClip(entity: Entity): boolean;
    /**
     * 暂停播放
     * @param entity 实体
     * @returns 是否成功暂停
     */
    pauseClip(entity: Entity): boolean;
    /**
     * 设置播放速度
     * @param entity 实体
     * @param speed 速度
     * @returns 是否成功设置
     */
    setPlaybackSpeed(entity: Entity, speed: number): boolean;
    /**
     * 设置当前时间
     * @param entity 实体
     * @param time 时间（秒）
     * @returns 是否成功设置
     */
    setCurrentTime(entity: Entity, time: number): boolean;
    /**
     * 添加表情关键帧
     * @param entity 实体
     * @param time 时间（秒）
     * @param expression 表情类型
     * @param weight 权重
     * @returns 是否成功添加
     */
    addExpressionKeyframe(entity: Entity, time: number, expression: FacialExpressionType, weight?: number): boolean;
    /**
     * 添加口型关键帧
     * @param entity 实体
     * @param time 时间（秒）
     * @param viseme 口型类型
     * @param weight 权重
     * @returns 是否成功添加
     */
    addVisemeKeyframe(entity: Entity, time: number, viseme: VisemeType, weight?: number): boolean;
    /**
     * 移除表情关键帧
     * @param entity 实体
     * @param time 时间（秒）
     * @returns 是否成功移除
     */
    removeExpressionKeyframe(entity: Entity, time: number): boolean;
    /**
     * 移除口型关键帧
     * @param entity 实体
     * @param time 时间（秒）
     * @returns 是否成功移除
     */
    removeVisemeKeyframe(entity: Entity, time: number): boolean;
    /**
     * 自动保存
     */
    private autoSave;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
}
