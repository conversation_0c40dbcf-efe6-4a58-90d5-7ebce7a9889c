<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>网络调试工具示例</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd/dist/antd.min.css">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
      padding: 20px;
      background-color: #f0f2f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .header {
      background-color: #fff;
      padding: 20px;
      border-radius: 4px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      margin-bottom: 20px;
    }
    .content {
      background-color: #fff;
      padding: 20px;
      border-radius: 4px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    .footer {
      text-align: center;
      margin-top: 20px;
      color: #888;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>网络调试工具示例</h1>
      <p>这个示例展示了如何使用网络调试工具来监控和模拟网络状态。</p>
    </div>
    
    <div class="content">
      <div id="app"></div>
    </div>
    
    <div class="footer">
      <p>DL（Digital Learning）引擎 - 网络调试工具示例</p>
    </div>
  </div>
  
  <script src="https://cdn.jsdelivr.net/npm/react/umd/react.production.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/react-dom/umd/react-dom.production.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/antd/dist/antd.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/moment/min/moment.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/moment/locale/zh-cn.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@ant-design/charts/dist/charts.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/i18next/dist/umd/i18next.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/react-i18next/dist/umd/react-i18next.min.js"></script>
  
  <script>
    // 模拟网络状态数据
    const generateMockNetworkData = (config) => {
      // 基础延迟加上抖动
      const jitterFactor = config.latencyJitter ? (Math.random() * 2 - 1) * config.latencyJitter : 0;
      const rtt = Math.max(1, config.latency + jitterFactor);
      
      // 丢包率加上随机波动
      const packetLossVariation = Math.random() * 0.01 - 0.005; // -0.5% to +0.5%
      const packetLoss = Math.max(0, Math.min(1, config.packetLoss + packetLossVariation));
      
      // 抖动值
      const jitter = config.latencyJitter * (0.5 + Math.random() * 0.5);
      
      // 带宽值
      let bandwidth = config.bandwidthLimit;
      if (bandwidth === 0) {
        bandwidth = 1000000 + Math.random() * 1000000; // 1-2 MB/s
      } else {
        // 带宽波动 ±10%
        const bandwidthVariation = (Math.random() * 0.2 - 0.1) * bandwidth;
        bandwidth = Math.max(1000, bandwidth + bandwidthVariation);
      }
      
      // 计算上行和下行带宽
      const uploadBandwidth = bandwidth * (0.3 + Math.random() * 0.2); // 30-50% of total
      const downloadBandwidth = bandwidth - uploadBandwidth;
      
      // 计算稳定性 (0-1)
      const stabilityBase = 1 - (packetLoss * 5) - (jitter / 200);
      const stability = Math.max(0, Math.min(1, stabilityBase + (Math.random() * 0.1 - 0.05)));
      
      // 计算拥塞程度 (0-1)
      const congestionBase = (rtt / 500) * 0.5 + packetLoss * 0.5;
      const congestion = Math.max(0, Math.min(1, congestionBase + (Math.random() * 0.1 - 0.05)));
      
      // 确定质量等级
      let level = 'excellent';
      if (rtt > 300 || packetLoss > 0.1 || jitter > 150 || bandwidth < 50000) {
        level = 'very_bad';
      } else if (rtt > 200 || packetLoss > 0.06 || jitter > 70 || bandwidth < 200000) {
        level = 'bad';
      } else if (rtt > 100 || packetLoss > 0.03 || jitter > 30 || bandwidth < 500000) {
        level = 'medium';
      } else if (rtt > 50 || packetLoss > 0.01 || jitter > 10 || bandwidth < 1000000) {
        level = 'good';
      }
      
      // 检测网络问题
      const issues = [];
      
      if (rtt > 200) {
        issues.push({
          type: 'high_latency',
          severity: Math.min(1, (rtt - 200) / 300),
          description: '网络延迟过高，可能导致操作响应缓慢',
          solution: '尝试连接到更近的服务器，检查本地网络连接，关闭其他占用带宽的应用',
          startTime: Date.now() - 60000,
          duration: 60000,
          resolved: false,
        });
      }
      
      if (packetLoss > 0.05) {
        issues.push({
          type: 'packet_loss',
          severity: Math.min(1, (packetLoss - 0.05) / 0.15),
          description: '网络丢包率过高，可能导致数据丢失或不完整',
          solution: '检查网络连接稳定性，避免使用无线网络，减少网络拥塞',
          startTime: Date.now() - 120000,
          duration: 120000,
          resolved: false,
        });
      }
      
      return {
        rtt,
        packetLoss,
        jitter,
        bandwidth,
        uploadBandwidth,
        downloadBandwidth,
        stability,
        congestion,
        level,
        timestamp: Date.now(),
        issues,
      };
    };
    
    // 初始化i18next
    i18next.init({
      lng: 'zh-CN',
      resources: {
        'zh-CN': {
          translation: {
            network: {
              debug: {
                title: '网络调试工具',
                refresh: '刷新',
                disconnect: '断开连接',
                reconnect: '重新连接',
                status: '网络状态',
                simulator: '网络模拟器',
                diagnostic: '网络诊断'
              },
              status: {
                title: '网络状态',
                connectionStatus: '连接状态',
                connected: '已连接',
                disconnected: '未连接',
                quality: '网络质量',
                latency: '延迟',
                packetLoss: '丢包率',
                bandwidth: '带宽',
                jitter: '抖动'
              },
              simulator: {
                title: '网络模拟器',
                warning: '警告',
                warningDescription: '网络模拟器会影响实际网络连接的行为，仅用于测试和调试目的。',
                latency: '延迟',
                packetLoss: '丢包率'
              }
            }
          }
        }
      }
    });
    
    // 渲染应用
    const { useState, useEffect } = React;
    const { Tabs, Card, Statistic, Badge, Button, Space, Alert, Slider, Switch, Form, Divider } = antd;
    const { TabPane } = Tabs;
    const { useTranslation } = ReactI18next;
    
    const App = () => {
      const { t } = useTranslation();
      const [activeTab, setActiveTab] = useState('status');
      const [connected, setConnected] = useState(true);
      const [loading, setLoading] = useState(false);
      const [currentQuality, setCurrentQuality] = useState(null);
      const [qualityHistory, setQualityHistory] = useState([]);
      const [simulatorConfig, setSimulatorConfig] = useState({
        enabled: true,
        latency: 50,
        latencyJitter: 10,
        packetLoss: 0.01,
        bandwidthLimit: 0,
      });
      
      // 更新网络数据
      const updateNetworkData = () => {
        if (!connected) {
          return;
        }
        
        const newData = generateMockNetworkData(simulatorConfig);
        setCurrentQuality(newData);
        setQualityHistory(prev => [...prev, newData].slice(-100));
      };
      
      // 初始化和定时更新
      useEffect(() => {
        // 初始更新
        updateNetworkData();
        
        // 设置定时更新
        const intervalId = setInterval(() => {
          updateNetworkData();
        }, 1000);
        
        return () => {
          clearInterval(intervalId);
        };
      }, [connected, simulatorConfig]);
      
      // 刷新数据
      const handleRefresh = () => {
        setLoading(true);
        
        setTimeout(() => {
          updateNetworkData();
          setLoading(false);
          antd.message.success('数据已刷新');
        }, 500);
      };
      
      // 模拟断线
      const handleSimulateDisconnect = () => {
        setConnected(false);
        antd.message.warning('已断开连接');
      };
      
      // 模拟重连
      const handleSimulateReconnect = () => {
        setConnected(true);
        antd.message.success('已重新连接');
      };
      
      // 更新模拟器配置
      const handleConfigChange = (config) => {
        setSimulatorConfig(config);
        antd.message.success('配置已更新');
      };
      
      // 渲染网络状态
      const renderNetworkStatus = () => {
        if (!currentQuality) {
          return (
            <Alert
              message="暂无数据"
              description="暂无网络状态数据，请确保已连接到服务器。"
              type="info"
              showIcon
            />
          );
        }
        
        return (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button 
                  icon={<i className="anticon">↻</i>} 
                  onClick={handleRefresh}
                  loading={loading}
                >
                  {t('network.debug.refresh')}
                </Button>
                {connected ? (
                  <Button 
                    danger
                    onClick={handleSimulateDisconnect}
                  >
                    {t('network.debug.disconnect')}
                  </Button>
                ) : (
                  <Button 
                    type="primary"
                    onClick={handleSimulateReconnect}
                  >
                    {t('network.debug.reconnect')}
                  </Button>
                )}
              </Space>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <Card>
                <Statistic
                  title={t('network.status.connectionStatus')}
                  value={connected ? t('network.status.connected') : t('network.status.disconnected')}
                  valueStyle={{ color: connected ? '#52c41a' : '#f5222d' }}
                />
              </Card>
            </div>
            
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16, marginBottom: 16 }}>
              <Card style={{ width: 200 }}>
                <Statistic
                  title={t('network.status.latency')}
                  value={currentQuality.rtt.toFixed(0)}
                  suffix="ms"
                  valueStyle={{ 
                    color: currentQuality.rtt > 200 ? '#f5222d' : 
                           currentQuality.rtt > 100 ? '#faad14' : '#52c41a' 
                  }}
                />
              </Card>
              
              <Card style={{ width: 200 }}>
                <Statistic
                  title={t('network.status.packetLoss')}
                  value={(currentQuality.packetLoss * 100).toFixed(1)}
                  suffix="%"
                  valueStyle={{ 
                    color: currentQuality.packetLoss > 0.1 ? '#f5222d' : 
                           currentQuality.packetLoss > 0.05 ? '#faad14' : '#52c41a' 
                  }}
                />
              </Card>
              
              <Card style={{ width: 200 }}>
                <Statistic
                  title={t('network.status.bandwidth')}
                  value={formatBandwidth(currentQuality.bandwidth)}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
              
              <Card style={{ width: 200 }}>
                <Statistic
                  title={t('network.status.jitter')}
                  value={currentQuality.jitter.toFixed(1)}
                  suffix="ms"
                  valueStyle={{ 
                    color: currentQuality.jitter > 50 ? '#f5222d' : 
                           currentQuality.jitter > 20 ? '#faad14' : '#52c41a' 
                  }}
                />
              </Card>
            </div>
          </div>
        );
      };
      
      // 渲染网络模拟器
      const renderNetworkSimulator = () => {
        return (
          <div>
            <Alert
              message={t('network.simulator.warning')}
              description={t('network.simulator.warningDescription')}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Form layout="vertical">
              <Form.Item label="启用模拟器">
                <Switch 
                  checked={simulatorConfig.enabled}
                  onChange={value => handleConfigChange({ ...simulatorConfig, enabled: value })}
                />
              </Form.Item>
              
              <Divider>延迟设置</Divider>
              
              <Form.Item label={t('network.simulator.latency')}>
                <Slider
                  min={0}
                  max={500}
                  step={10}
                  value={simulatorConfig.latency}
                  onChange={value => handleConfigChange({ ...simulatorConfig, latency: value })}
                  marks={{
                    0: '0ms',
                    100: '100ms',
                    200: '200ms',
                    500: '500ms',
                  }}
                  disabled={!simulatorConfig.enabled}
                />
              </Form.Item>
              
              <Form.Item label="延迟抖动">
                <Slider
                  min={0}
                  max={100}
                  step={5}
                  value={simulatorConfig.latencyJitter}
                  onChange={value => handleConfigChange({ ...simulatorConfig, latencyJitter: value })}
                  marks={{
                    0: '0ms',
                    25: '25ms',
                    50: '50ms',
                    100: '100ms',
                  }}
                  disabled={!simulatorConfig.enabled}
                />
              </Form.Item>
              
              <Divider>丢包设置</Divider>
              
              <Form.Item label={t('network.simulator.packetLoss')}>
                <Slider
                  min={0}
                  max={0.2}
                  step={0.01}
                  value={simulatorConfig.packetLoss}
                  onChange={value => handleConfigChange({ ...simulatorConfig, packetLoss: value })}
                  tipFormatter={value => `${(value * 100).toFixed(0)}%`}
                  marks={{
                    0: '0%',
                    0.05: '5%',
                    0.1: '10%',
                    0.2: '20%',
                  }}
                  disabled={!simulatorConfig.enabled}
                />
              </Form.Item>
            </Form>
          </div>
        );
      };
      
      // 格式化带宽显示
      const formatBandwidth = (bytes) => {
        if (bytes < 1024) {
          return `${bytes.toFixed(0)} B/s`;
        } else if (bytes < 1024 * 1024) {
          return `${(bytes / 1024).toFixed(1)} KB/s`;
        } else {
          return `${(bytes / (1024 * 1024)).toFixed(2)} MB/s`;
        }
      };
      
      return (
        <div>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab={t('network.debug.status')} key="status">
              {renderNetworkStatus()}
            </TabPane>
            <TabPane tab={t('network.debug.simulator')} key="simulator">
              {renderNetworkSimulator()}
            </TabPane>
          </Tabs>
        </div>
      );
    };
    
    // 渲染应用
    ReactDOM.render(
      React.createElement(
        ReactI18next.I18nextProvider,
        { i18n: i18next },
        React.createElement(App)
      ),
      document.getElementById('app')
    );
  </script>
</body>
</html>
