/**
 * 物理风效果系统
 * 用于模拟基于物理的风效果，使植被的摆动更加真实
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { VegetationComponent } from '../components/VegetationComponent';
/**
 * 风场类型
 */
export declare enum WindFieldType {
    /** 均匀风场 */
    UNIFORM = "uniform",
    /** 湍流风场 */
    TURBULENT = "turbulent",
    /** 旋转风场 */
    VORTEX = "vortex",
    /** 脉冲风场 */
    PULSE = "pulse",
    /** 自定义风场 */
    CUSTOM = "custom"
}
/**
 * 风区域类型
 */
export declare enum WindZoneType {
    /** 全局风区域 */
    GLOBAL = "global",
    /** 球形风区域 */
    SPHERE = "sphere",
    /** 盒形风区域 */
    BOX = "box",
    /** 圆柱形风区域 */
    CYLINDER = "cylinder",
    /** 自定义风区域 */
    CUSTOM = "custom"
}
/**
 * 风区域接口
 */
export interface WindZone {
    /** 区域类型 */
    type: WindZoneType;
    /** 区域位置 */
    position: THREE.Vector3;
    /** 区域大小 */
    size: THREE.Vector3;
    /** 区域旋转 */
    rotation: THREE.Euler;
    /** 风场类型 */
    fieldType: WindFieldType;
    /** 风力强度 */
    strength: number;
    /** 风力方向 */
    direction: THREE.Vector3;
    /** 风力频率 */
    frequency: number;
    /** 湍流强度 */
    turbulence: number;
    /** 衰减距离 */
    falloffDistance: number;
    /** 是否启用 */
    enabled: boolean;
    /** 自定义数据 */
    userData?: any;
}
/**
 * 物理风效果系统配置
 */
export interface PhysicalWindSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否使用物理模拟 */
    usePhysics?: boolean;
    /** 是否使用GPU加速 */
    useGPU?: boolean;
    /** 是否使用风区域 */
    useWindZones?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 全局风力参数 */
    globalWind?: {
        /** 风场类型 */
        fieldType?: WindFieldType;
        /** 风力强度 */
        strength?: number;
        /** 风力方向 */
        direction?: THREE.Vector3;
        /** 风力频率 */
        frequency?: number;
        /** 湍流强度 */
        turbulence?: number;
    };
}
/**
 * 物理风效果系统事件类型
 */
export declare enum PhysicalWindSystemEventType {
    /** 风区域添加 */
    WIND_ZONE_ADDED = "wind_zone_added",
    /** 风区域移除 */
    WIND_ZONE_REMOVED = "wind_zone_removed",
    /** 风区域更新 */
    WIND_ZONE_UPDATED = "wind_zone_updated",
    /** 全局风力更新 */
    GLOBAL_WIND_UPDATED = "global_wind_updated"
}
/**
 * 物理风效果系统
 */
export declare class PhysicalWindSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 配置 */
    private config;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 帧计数器 */
    private frameCount;
    /** 是否使用物理模拟 */
    private usePhysics;
    /** 是否使用GPU加速 */
    private useGPU;
    /** 是否使用风区域 */
    private useWindZones;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 是否使用实例化渲染 */
    private useInstancing;
    /** 全局风力参数 */
    private globalWind;
    /** 风区域列表 */
    private windZones;
    /** 植被组件列表 */
    private vegetationComponents;
    /** 实例化渲染系统 */
    private instancedRenderingSystem;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监控器 */
    private performanceMonitor;
    /** 当前时间 */
    private time;
    /** 调试网格列表 */
    private debugMeshes;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: PhysicalWindSystemConfig);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 销毁系统
     */
    destroy(): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加植被组件
     * @param entity 实体
     * @param component 植被组件
     */
    addVegetationComponent(entity: Entity, component: VegetationComponent): void;
    /**
     * 移除植被组件
     * @param entity 实体
     */
    removeVegetationComponent(entity: Entity): void;
    /**
     * 添加风区域
     * @param id 风区域ID
     * @param zone 风区域
     */
    addWindZone(id: string, zone: WindZone): void;
    /**
     * 移除风区域
     * @param id 风区域ID
     */
    removeWindZone(id: string): void;
    /**
     * 更新风区域
     * @param id 风区域ID
     * @param zone 风区域
     */
    updateWindZone(id: string, zone: Partial<WindZone>): void;
    /**
     * 设置全局风力参数
     * @param params 风力参数
     */
    setGlobalWind(params: Partial<typeof this.globalWind>): void;
    /**
     * 获取全局风力参数
     * @returns 全局风力参数
     */
    getGlobalWind(): typeof this.globalWind;
    /**
     * 更新所有植被组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateVegetationComponents;
    /**
     * 更新植被风效果
     * @param entity 实体
     * @param component 植被组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateVegetationWindEffect;
    /**
     * 计算风力
     * @param position 位置
     * @param deltaTime 帧间隔时间（秒）
     * @returns 风力向量
     */
    private calculateWindForce;
    /**
     * 计算全局风力
     * @param position 位置
     * @param _deltaTime 帧间隔时间（秒）
     * @returns 风力向量
     */
    private calculateGlobalWindForce;
    /**
     * 计算风区域风力
     * @param position 位置
     * @param zone 风区域
     * @param _deltaTime 帧间隔时间（秒）
     * @returns 风力向量
     */
    private calculateWindZoneForce;
    /**
     * 计算湍流
     * @param position 位置
     * @param time 时间
     * @param frequency 频率
     * @param strength 强度
     * @returns 湍流向量
     */
    private calculateTurbulence;
    /**
     * 计算旋转风场力
     * @param position 相对位置
     * @param strength 强度
     * @param angle 角度
     * @returns 旋转力向量
     */
    private calculateVortexForce;
    /**
     * 应用风力
     * @param _entity 实体
     * @param _component 植被组件
     * @param instanceId 实例ID
     * @param instance 实例
     * @param windForce 风力
     * @param _deltaTime 帧间隔时间（秒）
     */
    private applyWindForce;
    /**
     * 初始化调试可视化
     */
    private initDebugVisualization;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 清除调试网格
     */
    private clearDebugMeshes;
    /**
     * 创建全局风力调试网格
     */
    private createGlobalWindDebugMesh;
    /**
     * 更新全局风力调试网格
     */
    private updateGlobalWindDebugMesh;
    /**
     * 创建风区域调试网格
     * @param id 风区域ID
     * @param zone 风区域
     */
    private createWindZoneDebugMesh;
    /**
     * 更新风区域调试网格
     * @param id 风区域ID
     * @param zone 风区域
     */
    private updateWindZoneDebugMesh;
    /**
     * 移除风区域调试网格
     * @param id 风区域ID
     */
    private removeWindZoneDebugMesh;
}
