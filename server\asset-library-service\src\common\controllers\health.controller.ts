import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CacheService } from '../services/cache.service';
import { StorageService } from '../services/storage.service';

interface HealthStatus {
  status: 'ok' | 'degraded' | 'error';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: 'ok' | 'error';
    redis: 'ok' | 'degraded' | 'error';
    minio: 'ok' | 'degraded' | 'error';
    elasticsearch: 'ok' | 'error';
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
}

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly cacheService: CacheService,
    private readonly storageService: StorageService,
  ) {}

  @Get()
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({
    status: 200,
    description: '服务健康状态',
  })
  async getHealth(): Promise<HealthStatus> {
    const memoryUsage = process.memoryUsage();

    // 检查各个服务的状态
    const services = {
      database: 'ok' as 'ok' | 'error',
      redis: this.cacheService.getConnectionStatus() ? 'ok' as const : 'degraded' as const,
      minio: this.storageService.isStorageConnected() ? 'ok' as const : 'degraded' as const,
      elasticsearch: 'ok' as 'ok' | 'error', // 这里可以添加ES连接检查
    };

    // 判断整体状态 - 只有核心服务（数据库）出错才标记为error，其他服务降级不影响整体状态
    const hasCriticalError = services.database === 'error';
    const hasDegradedServices = Object.values(services).some(status => status === 'degraded');

    let overallStatus: 'ok' | 'degraded' | 'error';
    if (hasCriticalError) {
      overallStatus = 'error';
    } else if (hasDegradedServices) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'ok';
    }

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      services,
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
      },
    };
  }

  @Get('ready')
  @ApiOperation({ summary: '就绪检查' })
  @ApiResponse({
    status: 200,
    description: '服务就绪状态',
  })
  async getReady(): Promise<{ ready: boolean }> {
    // 服务就绪检查 - 只要核心功能可用就认为就绪
    // Redis和MinIO不可用时服务仍可提供基本功能
    return {
      ready: true, // 服务本身已启动，即使外部依赖不可用也认为就绪
    };
  }

  @Get('live')
  @ApiOperation({ summary: '存活检查' })
  @ApiResponse({
    status: 200,
    description: '服务存活状态',
  })
  async getLive(): Promise<{ alive: boolean }> {
    return {
      alive: true,
    };
  }
}
