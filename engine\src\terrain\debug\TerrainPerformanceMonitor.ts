/**
 * 地形性能监控
 * 监控和分析地形渲染和物理性能
 */
import * as THREE from 'three';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 性能监控事件类型
 */
export enum PerformanceMonitorEventType {
  /** 性能数据更新 */
  PERFORMANCE_DATA_UPDATED = 'performance_data_updated',
  /** 性能警告 */
  PERFORMANCE_WARNING = 'performance_warning',
  /** 性能瓶颈检测 */
  BOTTLENECK_DETECTED = 'bottleneck_detected'
}

/**
 * 性能警告级别
 */
export enum PerformanceWarningLevel {
  /** 信息 */
  INFO = 'info',
  /** 警告 */
  WARNING = 'warning',
  /** 错误 */
  ERROR = 'error'
}

/**
 * 性能瓶颈类型
 */
export enum PerformanceBottleneckType {
  /** CPU瓶颈 */
  CPU = 'cpu',
  /** GPU瓶颈 */
  GPU = 'gpu',
  /** 内存瓶颈 */
  MEMORY = 'memory',
  /** 渲染瓶颈 */
  RENDERING = 'rendering',
  /** 物理瓶颈 */
  PHYSICS = 'physics',
  /** 纹理瓶颈 */
  TEXTURES = 'textures',
  /** 几何体瓶颈 */
  GEOMETRY = 'geometry'
}

/**
 * 性能数据
 */
export interface PerformanceData {
  /** 帧率 */
  fps: number;
  /** 帧时间（毫秒） */
  frameTime: number;
  /** CPU使用率 */
  cpuUsage: number;
  /** GPU使用率 */
  gpuUsage: number;
  /** 内存使用量（MB） */
  memoryUsage: number;
  /** 渲染时间（毫秒） */
  renderTime: number;
  /** 物理时间（毫秒） */
  physicsTime: number;
  /** 地形渲染时间（毫秒） */
  terrainRenderTime: number;
  /** 地形物理时间（毫秒） */
  terrainPhysicsTime: number;
  /** 可见地形块数量 */
  visibleTerrainChunks: number;
  /** 地形三角形数量 */
  terrainTriangles: number;
  /** 地形顶点数量 */
  terrainVertices: number;
  /** 地形纹理内存（MB） */
  terrainTextureMemory: number;
  /** 地形几何体内存（MB） */
  terrainGeometryMemory: number;
  /** 地形物理内存（MB） */
  terrainPhysicsMemory: number;
  /** 地形LOD级别分布 */
  terrainLODDistribution: Record<number, number>;
  /** 地形物理LOD级别分布 */
  terrainPhysicsLODDistribution: Record<number, number>;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 性能监控配置
 */
export interface PerformanceMonitorConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 更新频率（毫秒） */
  updateFrequency?: number;
  /** 采样大小 */
  sampleSize?: number;
  /** 是否记录历史数据 */
  recordHistory?: boolean;
  /** 历史数据最大长度 */
  maxHistoryLength?: number;
  /** 是否启用警告 */
  enableWarnings?: boolean;
  /** 是否启用瓶颈检测 */
  enableBottleneckDetection?: boolean;
  /** 警告阈值 */
  warningThresholds?: {
    /** 最小帧率 */
    minFps?: number;
    /** 最大帧时间 */
    maxFrameTime?: number;
    /** 最大CPU使用率 */
    maxCpuUsage?: number;
    /** 最大GPU使用率 */
    maxGpuUsage?: number;
    /** 最大内存使用量 */
    maxMemoryUsage?: number;
    /** 最大渲染时间 */
    maxRenderTime?: number;
    /** 最大物理时间 */
    maxPhysicsTime?: number;
  };
}

/**
 * 地形性能监控
 */
export class TerrainPerformanceMonitor {
  /** 是否启用 */
  private enabled: boolean;
  /** 更新频率（毫秒） */
  private updateFrequency: number;
  /** 采样大小 */
  private sampleSize: number;
  /** 是否记录历史数据 */
  private recordHistory: boolean;
  /** 历史数据最大长度 */
  private maxHistoryLength: number;
  /** 是否启用警告 */
  private enableWarnings: boolean;
  /** 是否启用瓶颈检测 */
  private enableBottleneckDetection: boolean;
  /** 警告阈值 */
  private warningThresholds: {
    minFps: number;
    maxFrameTime: number;
    maxCpuUsage: number;
    maxGpuUsage: number;
    maxMemoryUsage: number;
    maxRenderTime: number;
    maxPhysicsTime: number;
  };

  /** 性能数据 */
  private performanceData: PerformanceData;
  /** 历史性能数据 */
  private performanceHistory: PerformanceData[];
  /** 帧时间样本 */
  private frameTimeSamples: number[];
  /** 渲染时间样本 */
  private renderTimeSamples: number[];
  /** 物理时间样本 */
  private physicsTimeSamples: number[];
  /** 地形渲染时间样本 */
  private terrainRenderTimeSamples: number[];
  /** 地形物理时间样本 */
  private terrainPhysicsTimeSamples: number[];

  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 更新定时器ID */
  private updateTimerId: number | null;
  /** 上一帧时间 */
  private lastFrameTime: number;
  /** 帧计数器 */
  private frameCount: number;
  /** 开始时间 */
  private startTime: number;

  /** 性能监控实例 */
  private static instance: TerrainPerformanceMonitor | null = null;

  /**
   * 获取性能监控实例
   * @returns 性能监控实例
   */
  public static getInstance(): TerrainPerformanceMonitor {
    if (!TerrainPerformanceMonitor.instance) {
      TerrainPerformanceMonitor.instance = new TerrainPerformanceMonitor();
    }
    return TerrainPerformanceMonitor.instance;
  }

  /**
   * 创建地形性能监控
   * @param config 配置
   */
  constructor(config: PerformanceMonitorConfig = {}) {
    this.enabled = config.enabled !== undefined ? config.enabled : true;
    this.updateFrequency = config.updateFrequency || 1000; // 1秒
    this.sampleSize = config.sampleSize || 60; // 60帧
    this.recordHistory = config.recordHistory !== undefined ? config.recordHistory : true;
    this.maxHistoryLength = config.maxHistoryLength || 60; // 60个数据点
    this.enableWarnings = config.enableWarnings !== undefined ? config.enableWarnings : true;
    this.enableBottleneckDetection = config.enableBottleneckDetection !== undefined ? config.enableBottleneckDetection : true;

    // 设置警告阈值
    const thresholds = config.warningThresholds || {};
    this.warningThresholds = {
      minFps: thresholds.minFps || 30,
      maxFrameTime: thresholds.maxFrameTime || 33.3, // 30fps
      maxCpuUsage: thresholds.maxCpuUsage || 80,
      maxGpuUsage: thresholds.maxGpuUsage || 90,
      maxMemoryUsage: thresholds.maxMemoryUsage || 1024, // 1GB
      maxRenderTime: thresholds.maxRenderTime || 16, // 60fps
      maxPhysicsTime: thresholds.maxPhysicsTime || 8 // 半帧
    };

    // 初始化性能数据
    this.performanceData = this.createEmptyPerformanceData();
    this.performanceHistory = [];

    // 初始化样本数组
    this.frameTimeSamples = [];
    this.renderTimeSamples = [];
    this.physicsTimeSamples = [];
    this.terrainRenderTimeSamples = [];
    this.terrainPhysicsTimeSamples = [];

    // 创建事件发射器
    this.eventEmitter = new EventEmitter();

    // 初始化计时器
    this.updateTimerId = null;
    this.lastFrameTime = 0;
    this.frameCount = 0;
    this.startTime = 0;

    // 如果启用，则开始监控
    if (this.enabled) {
      this.start();
    }
  }

  /**
   * 创建空性能数据
   * @returns 空性能数据
   */
  private createEmptyPerformanceData(): PerformanceData {
    return {
      fps: 0,
      frameTime: 0,
      cpuUsage: 0,
      gpuUsage: 0,
      memoryUsage: 0,
      renderTime: 0,
      physicsTime: 0,
      terrainRenderTime: 0,
      terrainPhysicsTime: 0,
      visibleTerrainChunks: 0,
      terrainTriangles: 0,
      terrainVertices: 0,
      terrainTextureMemory: 0,
      terrainGeometryMemory: 0,
      terrainPhysicsMemory: 0,
      terrainLODDistribution: {},
      terrainPhysicsLODDistribution: {},
      timestamp: Date.now()
    };
  }

  /**
   * 开始监控
   */
  public start(): void {
    if (this.updateTimerId !== null) {
      return;
    }

    this.enabled = true;
    this.startTime = performance.now();
    this.lastFrameTime = this.startTime;
    this.frameCount = 0;

    // 设置更新定时器
    this.updateTimerId = window.setInterval(() => {
      this.updatePerformanceData();
    }, this.updateFrequency);

    Debug.log('TerrainPerformanceMonitor', '开始性能监控');
  }

  /**
   * 停止监控
   */
  public stop(): void {
    if (this.updateTimerId === null) {
      return;
    }

    this.enabled = false;

    // 清除更新定时器
    window.clearInterval(this.updateTimerId);
    this.updateTimerId = null;

    Debug.log('TerrainPerformanceMonitor', '停止性能监控');
  }

  /**
   * 更新性能数据
   */
  private updatePerformanceData(): void {
    // 计算帧率
    const now = performance.now();
    const elapsed = now - this.startTime;
    const fps = this.frameCount / (elapsed / 1000);

    // 计算平均帧时间
    const avgFrameTime = this.calculateAverage(this.frameTimeSamples);

    // 计算平均渲染时间
    const avgRenderTime = this.calculateAverage(this.renderTimeSamples);

    // 计算平均物理时间
    const avgPhysicsTime = this.calculateAverage(this.physicsTimeSamples);

    // 计算平均地形渲染时间
    const avgTerrainRenderTime = this.calculateAverage(this.terrainRenderTimeSamples);

    // 计算平均地形物理时间
    const avgTerrainPhysicsTime = this.calculateAverage(this.terrainPhysicsTimeSamples);

    // 获取内存使用情况
    const memoryUsage = this.getMemoryUsage();

    // 获取CPU和GPU使用情况
    const { cpuUsage, gpuUsage } = this.getCPUAndGPUUsage();

    // 获取地形统计信息
    const terrainStats = this.getTerrainStats();

    // 更新性能数据
    this.performanceData = {
      fps,
      frameTime: avgFrameTime,
      cpuUsage,
      gpuUsage,
      memoryUsage,
      renderTime: avgRenderTime,
      physicsTime: avgPhysicsTime,
      terrainRenderTime: avgTerrainRenderTime,
      terrainPhysicsTime: avgTerrainPhysicsTime,
      visibleTerrainChunks: terrainStats.visibleChunks,
      terrainTriangles: terrainStats.triangles,
      terrainVertices: terrainStats.vertices,
      terrainTextureMemory: terrainStats.textureMemory,
      terrainGeometryMemory: terrainStats.geometryMemory,
      terrainPhysicsMemory: terrainStats.physicsMemory,
      terrainLODDistribution: terrainStats.lodDistribution,
      terrainPhysicsLODDistribution: terrainStats.physicsLODDistribution,
      timestamp: Date.now()
    };

    // 如果记录历史数据，则添加到历史记录
    if (this.recordHistory) {
      this.addToHistory(this.performanceData);
    }

    // 发出性能数据更新事件
    this.eventEmitter.emit(PerformanceMonitorEventType.PERFORMANCE_DATA_UPDATED, this.performanceData);

    // 检查性能警告
    if (this.enableWarnings) {
      this.checkPerformanceWarnings(this.performanceData);
    }

    // 检查性能瓶颈
    if (this.enableBottleneckDetection) {
      this.detectBottlenecks(this.performanceData);
    }

    // 重置帧计数器
    this.frameCount = 0;
    this.startTime = now;
  }

  /**
   * 添加到历史记录
   * @param data 性能数据
   */
  private addToHistory(data: PerformanceData): void {
    // 添加数据
    this.performanceHistory.push({ ...data });

    // 如果超过最大长度，则移除最旧的数据
    if (this.performanceHistory.length > this.maxHistoryLength) {
      this.performanceHistory.shift();
    }
  }

  /**
   * 计算平均值
   * @param samples 样本数组
   * @returns 平均值
   */
  private calculateAverage(samples: number[]): number {
    if (samples.length === 0) {
      return 0;
    }

    const sum = samples.reduce((a, b) => a + b, 0);
    return sum / samples.length;
  }

  /**
   * 获取内存使用情况
   * @returns 内存使用量（MB）
   */
  private getMemoryUsage(): number {
    // 在浏览器环境中，尝试使用performance.memory
    if (window.performance && (window.performance as any).memory) {
      const memory = (window.performance as any).memory;
      return memory.usedJSHeapSize / (1024 * 1024);
    }

    // 如果不可用，则返回估计值
    return 0;
  }

  /**
   * 获取CPU和GPU使用情况
   * @returns CPU和GPU使用率
   */
  private getCPUAndGPUUsage(): { cpuUsage: number; gpuUsage: number } {
    // 在浏览器环境中，无法直接获取CPU和GPU使用率
    // 这里返回估计值，实际应用中可能需要使用其他方法
    return {
      cpuUsage: 0,
      gpuUsage: 0
    };
  }

  /**
   * 获取地形统计信息
   * @returns 地形统计信息
   */
  private getTerrainStats(): {
    visibleChunks: number;
    triangles: number;
    vertices: number;
    textureMemory: number;
    geometryMemory: number;
    physicsMemory: number;
    lodDistribution: Record<number, number>;
    physicsLODDistribution: Record<number, number>;
  } {
    // 这里应该从地形系统获取实际统计信息
    // 简化实现，返回估计值
    return {
      visibleChunks: 0,
      triangles: 0,
      vertices: 0,
      textureMemory: 0,
      geometryMemory: 0,
      physicsMemory: 0,
      lodDistribution: {},
      physicsLODDistribution: {}
    };
  }

  /**
   * 检查性能警告
   * @param data 性能数据
   */
  private checkPerformanceWarnings(data: PerformanceData): void {
    // 检查帧率
    if (data.fps < this.warningThresholds.minFps) {
      this.emitWarning(
        PerformanceWarningLevel.WARNING,
        `帧率过低: ${data.fps.toFixed(1)} FPS (阈值: ${this.warningThresholds.minFps} FPS)`
      );
    }

    // 检查帧时间
    if (data.frameTime > this.warningThresholds.maxFrameTime) {
      this.emitWarning(
        PerformanceWarningLevel.WARNING,
        `帧时间过长: ${data.frameTime.toFixed(1)} ms (阈值: ${this.warningThresholds.maxFrameTime} ms)`
      );
    }

    // 检查CPU使用率
    if (data.cpuUsage > this.warningThresholds.maxCpuUsage) {
      this.emitWarning(
        PerformanceWarningLevel.WARNING,
        `CPU使用率过高: ${data.cpuUsage.toFixed(1)}% (阈值: ${this.warningThresholds.maxCpuUsage}%)`
      );
    }

    // 检查GPU使用率
    if (data.gpuUsage > this.warningThresholds.maxGpuUsage) {
      this.emitWarning(
        PerformanceWarningLevel.WARNING,
        `GPU使用率过高: ${data.gpuUsage.toFixed(1)}% (阈值: ${this.warningThresholds.maxGpuUsage}%)`
      );
    }

    // 检查内存使用量
    if (data.memoryUsage > this.warningThresholds.maxMemoryUsage) {
      this.emitWarning(
        PerformanceWarningLevel.WARNING,
        `内存使用量过高: ${data.memoryUsage.toFixed(1)} MB (阈值: ${this.warningThresholds.maxMemoryUsage} MB)`
      );
    }

    // 检查渲染时间
    if (data.renderTime > this.warningThresholds.maxRenderTime) {
      this.emitWarning(
        PerformanceWarningLevel.WARNING,
        `渲染时间过长: ${data.renderTime.toFixed(1)} ms (阈值: ${this.warningThresholds.maxRenderTime} ms)`
      );
    }

    // 检查物理时间
    if (data.physicsTime > this.warningThresholds.maxPhysicsTime) {
      this.emitWarning(
        PerformanceWarningLevel.WARNING,
        `物理时间过长: ${data.physicsTime.toFixed(1)} ms (阈值: ${this.warningThresholds.maxPhysicsTime} ms)`
      );
    }
  }

  /**
   * 发出警告
   * @param level 警告级别
   * @param message 警告消息
   */
  private emitWarning(level: PerformanceWarningLevel, message: string): void {
    // 发出警告事件
    this.eventEmitter.emit(PerformanceMonitorEventType.PERFORMANCE_WARNING, level, message);

    // 记录警告
    switch (level) {
      case PerformanceWarningLevel.INFO:
        Debug.log('TerrainPerformanceMonitor', message);
        break;
      case PerformanceWarningLevel.WARNING:
        Debug.warn('TerrainPerformanceMonitor', message);
        break;
      case PerformanceWarningLevel.ERROR:
        Debug.error('TerrainPerformanceMonitor', message);
        break;
    }
  }

  /**
   * 检测性能瓶颈
   * @param data 性能数据
   */
  private detectBottlenecks(data: PerformanceData): void {
    // 检查CPU瓶颈
    if (data.cpuUsage > 80 && data.gpuUsage < 50) {
      this.emitBottleneck(PerformanceBottleneckType.CPU, '检测到CPU瓶颈');
    }

    // 检查GPU瓶颈
    if (data.gpuUsage > 80 && data.cpuUsage < 50) {
      this.emitBottleneck(PerformanceBottleneckType.GPU, '检测到GPU瓶颈');
    }

    // 检查内存瓶颈
    if (data.memoryUsage > this.warningThresholds.maxMemoryUsage * 0.9) {
      this.emitBottleneck(PerformanceBottleneckType.MEMORY, '检测到内存瓶颈');
    }

    // 检查渲染瓶颈
    if (data.renderTime > data.frameTime * 0.7) {
      this.emitBottleneck(PerformanceBottleneckType.RENDERING, '检测到渲染瓶颈');
    }

    // 检查物理瓶颈
    if (data.physicsTime > data.frameTime * 0.5) {
      this.emitBottleneck(PerformanceBottleneckType.PHYSICS, '检测到物理瓶颈');
    }

    // 检查纹理瓶颈
    if (data.terrainTextureMemory > 500) {
      this.emitBottleneck(PerformanceBottleneckType.TEXTURES, '检测到纹理瓶颈');
    }

    // 检查几何体瓶颈
    if (data.terrainTriangles > 1000000) {
      this.emitBottleneck(PerformanceBottleneckType.GEOMETRY, '检测到几何体瓶颈');
    }
  }

  /**
   * 发出瓶颈事件
   * @param type 瓶颈类型
   * @param message 瓶颈消息
   */
  private emitBottleneck(type: PerformanceBottleneckType, message: string): void {
    // 发出瓶颈事件
    this.eventEmitter.emit(PerformanceMonitorEventType.BOTTLENECK_DETECTED, type, message);

    // 记录瓶颈
    Debug.warn('TerrainPerformanceMonitor', `${message} (${type})`);
  }

  /**
   * 记录帧时间
   * @param frameTime 帧时间（毫秒）
   */
  public recordFrameTime(frameTime: number): void {
    if (!this.enabled) {
      return;
    }

    // 添加到样本
    this.frameTimeSamples.push(frameTime);

    // 如果超过样本大小，则移除最旧的样本
    if (this.frameTimeSamples.length > this.sampleSize) {
      this.frameTimeSamples.shift();
    }

    // 增加帧计数
    this.frameCount++;
  }

  /**
   * 记录渲染时间
   * @param renderTime 渲染时间（毫秒）
   */
  public recordRenderTime(renderTime: number): void {
    if (!this.enabled) {
      return;
    }

    // 添加到样本
    this.renderTimeSamples.push(renderTime);

    // 如果超过样本大小，则移除最旧的样本
    if (this.renderTimeSamples.length > this.sampleSize) {
      this.renderTimeSamples.shift();
    }
  }

  /**
   * 记录物理时间
   * @param physicsTime 物理时间（毫秒）
   */
  public recordPhysicsTime(physicsTime: number): void {
    if (!this.enabled) {
      return;
    }

    // 添加到样本
    this.physicsTimeSamples.push(physicsTime);

    // 如果超过样本大小，则移除最旧的样本
    if (this.physicsTimeSamples.length > this.sampleSize) {
      this.physicsTimeSamples.shift();
    }
  }

  /**
   * 记录地形渲染时间
   * @param terrainRenderTime 地形渲染时间（毫秒）
   */
  public recordTerrainRenderTime(terrainRenderTime: number): void {
    if (!this.enabled) {
      return;
    }

    // 添加到样本
    this.terrainRenderTimeSamples.push(terrainRenderTime);

    // 如果超过样本大小，则移除最旧的样本
    if (this.terrainRenderTimeSamples.length > this.sampleSize) {
      this.terrainRenderTimeSamples.shift();
    }
  }

  /**
   * 记录地形物理时间
   * @param terrainPhysicsTime 地形物理时间（毫秒）
   */
  public recordTerrainPhysicsTime(terrainPhysicsTime: number): void {
    if (!this.enabled) {
      return;
    }

    // 添加到样本
    this.terrainPhysicsTimeSamples.push(terrainPhysicsTime);

    // 如果超过样本大小，则移除最旧的样本
    if (this.terrainPhysicsTimeSamples.length > this.sampleSize) {
      this.terrainPhysicsTimeSamples.shift();
    }
  }

  /**
   * 获取当前性能数据
   * @returns 性能数据
   */
  public getPerformanceData(): PerformanceData {
    return { ...this.performanceData };
  }

  /**
   * 获取性能历史数据
   * @returns 性能历史数据
   */
  public getPerformanceHistory(): PerformanceData[] {
    return [...this.performanceHistory];
  }

  /**
   * 清除性能历史数据
   */
  public clearPerformanceHistory(): void {
    this.performanceHistory = [];
  }

  /**
   * 设置配置
   * @param config 配置
   */
  public setConfig(config: PerformanceMonitorConfig): void {
    this.enabled = config.enabled !== undefined ? config.enabled : this.enabled;
    this.updateFrequency = config.updateFrequency || this.updateFrequency;
    this.sampleSize = config.sampleSize || this.sampleSize;
    this.recordHistory = config.recordHistory !== undefined ? config.recordHistory : this.recordHistory;
    this.maxHistoryLength = config.maxHistoryLength || this.maxHistoryLength;
    this.enableWarnings = config.enableWarnings !== undefined ? config.enableWarnings : this.enableWarnings;
    this.enableBottleneckDetection = config.enableBottleneckDetection !== undefined ? config.enableBottleneckDetection : this.enableBottleneckDetection;

    // 设置警告阈值
    if (config.warningThresholds) {
      const thresholds = config.warningThresholds;
      this.warningThresholds = {
        minFps: thresholds.minFps || this.warningThresholds.minFps,
        maxFrameTime: thresholds.maxFrameTime || this.warningThresholds.maxFrameTime,
        maxCpuUsage: thresholds.maxCpuUsage || this.warningThresholds.maxCpuUsage,
        maxGpuUsage: thresholds.maxGpuUsage || this.warningThresholds.maxGpuUsage,
        maxMemoryUsage: thresholds.maxMemoryUsage || this.warningThresholds.maxMemoryUsage,
        maxRenderTime: thresholds.maxRenderTime || this.warningThresholds.maxRenderTime,
        maxPhysicsTime: thresholds.maxPhysicsTime || this.warningThresholds.maxPhysicsTime
      };
    }

    // 如果启用，则开始监控
    if (this.enabled && this.updateTimerId === null) {
      this.start();
    } else if (!this.enabled && this.updateTimerId !== null) {
      this.stop();
    } else if (this.enabled && this.updateTimerId !== null) {
      // 重新启动定时器以应用新的更新频率
      this.stop();
      this.start();
    }
  }

  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): PerformanceMonitorConfig {
    return {
      enabled: this.enabled,
      updateFrequency: this.updateFrequency,
      sampleSize: this.sampleSize,
      recordHistory: this.recordHistory,
      maxHistoryLength: this.maxHistoryLength,
      enableWarnings: this.enableWarnings,
      enableBottleneckDetection: this.enableBottleneckDetection,
      warningThresholds: { ...this.warningThresholds }
    };
  }

  /**
   * 注册事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: PerformanceMonitorEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: PerformanceMonitorEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 销毁
   */
  public dispose(): void {
    // 停止监控
    this.stop();

    // 清除数据
    this.performanceHistory = [];
    this.frameTimeSamples = [];
    this.renderTimeSamples = [];
    this.physicsTimeSamples = [];
    this.terrainRenderTimeSamples = [];
    this.terrainPhysicsTimeSamples = [];

    // 清除事件监听器
    this.eventEmitter.removeAllListeners();

    // 清除单例实例
    TerrainPerformanceMonitor.instance = null;
  }
}
