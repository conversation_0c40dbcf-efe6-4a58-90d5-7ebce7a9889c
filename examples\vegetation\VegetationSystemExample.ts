/**
 * 植被系统示例
 * 
 * 本示例展示了如何使用植被系统在地形上生成和管理植被
 */
import * as THREE from 'three';
import {
  Engine,
  World,
  Entity,
  Camera,
  Scene,
  Transform,
  Light,
  LightType,
  RenderSystem,
  CameraSystem,
  InputSystem,
  Debug
} from '../../engine/src/core';

import {
  TerrainComponent,
  TerrainSystem,
  TerrainChunkSystem,
  TerrainInstancedRenderingSystem,
  TerrainGenerationAlgorithms,
  TerrainFeatureType
} from '../../engine/src/terrain';

import {
  VegetationComponent,
  VegetationSystem
} from '../../engine/src/vegetation';

/**
 * 植被系统示例类
 */
export class VegetationSystemExample {
  /** 引擎 */
  private engine: Engine;

  /** 世界 */
  private world: World;

  /** 渲染系统 */
  private renderSystem: RenderSystem;

  /** 相机系统 */
  private cameraSystem: CameraSystem;

  /** 输入系统 */
  private inputSystem: InputSystem;

  /** 地形系统 */
  private terrainSystem: TerrainSystem;

  /** 地形分块系统 */
  private terrainChunkSystem: TerrainChunkSystem;

  /** 地形实例化渲染系统 */
  private terrainInstancedRenderingSystem: TerrainInstancedRenderingSystem;

  /** 植被系统 */
  private vegetationSystem: VegetationSystem;

  /** 相机实体 */
  private cameraEntity: Entity;

  /** 地形实体 */
  private terrainEntity: Entity;

  /** 植被实体 */
  private vegetationEntity: Entity;

  /**
   * 创建植被系统示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();

    // 创建世界
    this.world = new World();
    this.engine.addWorld(this.world);

    // 创建系统
    this.createSystems();

    // 创建场景
    this.createScene();

    // 创建地形
    this.createTerrain();

    // 创建植被
    this.createVegetation();

    // 启动引擎
    this.engine.start();
  }

  /**
   * 创建系统
   */
  private createSystems(): void {
    // 创建渲染系统
    this.renderSystem = new RenderSystem(this.world);
    this.world.addSystem(this.renderSystem);

    // 创建相机系统
    this.cameraSystem = new CameraSystem(this.world);
    this.world.addSystem(this.cameraSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world);
    this.world.addSystem(this.inputSystem);

    // 创建地形系统
    this.terrainSystem = new TerrainSystem(this.world);
    this.world.addSystem(this.terrainSystem);

    // 创建地形分块系统
    this.terrainChunkSystem = new TerrainChunkSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      chunkSize: 64,
      chunkResolution: 32,
      useLOD: true,
      useFrustumCulling: true,
      useOctree: true
    });
    this.world.addSystem(this.terrainChunkSystem);

    // 创建地形实例化渲染系统
    this.terrainInstancedRenderingSystem = new TerrainInstancedRenderingSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      useFrustumCulling: true,
      useOctree: true,
      useInstanceLOD: true,
      useInstanceShadow: true
    });
    this.world.addSystem(this.terrainInstancedRenderingSystem);

    // 创建植被系统
    this.vegetationSystem = new VegetationSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      useInstancing: true,
      useLOD: true,
      useFrustumCulling: true,
      useOctree: true,
      useGPUInstancing: true,
      useShadow: true,
      useWind: true
    });
    this.world.addSystem(this.vegetationSystem);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建场景实体
    const sceneEntity = new Entity('scene');
    const scene = new Scene();
    sceneEntity.addComponent(scene);
    this.world.addEntity(sceneEntity);

    // 创建相机实体
    this.cameraEntity = new Entity('camera');
    this.cameraEntity.addComponent(new Transform({
      position: { x: 0, y: 100, z: 200 },
      rotation: { x: -0.3, y: 0, z: 0 }
    }));
    this.cameraEntity.addComponent(new Camera({
      fov: 60,
      near: 0.1,
      far: 1000,
      aspect: window.innerWidth / window.innerHeight
    }));
    this.world.addEntity(this.cameraEntity);

    // 创建光源实体
    const lightEntity = new Entity('light');
    lightEntity.addComponent(new Transform({
      position: { x: 100, y: 200, z: 100 },
      rotation: { x: -0.5, y: 0.5, z: 0 }
    }));
    lightEntity.addComponent(new Light({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1.0,
      castShadow: true
    }));
    this.world.addEntity(lightEntity);

    // 创建环境光实体
    const ambientLightEntity = new Entity('ambient_light');
    ambientLightEntity.addComponent(new Light({
      type: LightType.AMBIENT,
      color: 0x404040,
      intensity: 0.5
    }));
    this.world.addEntity(ambientLightEntity);
  }

  /**
   * 创建地形
   */
  private createTerrain(): void {
    // 创建地形实体
    this.terrainEntity = new Entity('terrain');
    this.terrainEntity.addComponent(new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));

    // 创建地形组件
    const terrainComponent = new TerrainComponent({
      width: 1000,
      height: 1000,
      resolution: 256,
      maxHeight: 100,
      useLOD: true,
      usePhysics: true,
      layers: [
        {
          texture: 'assets/textures/grass.jpg',
          normalMap: 'assets/textures/grass_normal.jpg',
          tiling: 20,
          minHeight: 0,
          maxHeight: 50,
          minSlope: 0,
          maxSlope: 30
        },
        {
          texture: 'assets/textures/rock.jpg',
          normalMap: 'assets/textures/rock_normal.jpg',
          tiling: 15,
          minHeight: 30,
          maxHeight: 100,
          minSlope: 20,
          maxSlope: 90
        },
        {
          texture: 'assets/textures/snow.jpg',
          normalMap: 'assets/textures/snow_normal.jpg',
          tiling: 10,
          minHeight: 70,
          maxHeight: 100,
          minSlope: 0,
          maxSlope: 40
        }
      ]
    });
    this.terrainEntity.addComponent(terrainComponent);

    // 生成地形
    this.generateTerrain(terrainComponent);

    // 添加到世界
    this.world.addEntity(this.terrainEntity);
  }

  /**
   * 生成地形
   * @param terrainComponent 地形组件
   */
  private async generateTerrain(terrainComponent: TerrainComponent): Promise<void> {
    try {
      // 生成山地地形
      await TerrainGenerationAlgorithms.generateTerrain(terrainComponent, {
        baseTerrainType: TerrainFeatureType.HILLS,
        baseTerrainParams: {
          scale: 100,
          persistence: 0.5,
          octaves: 6,
          frequency: 0.01,
          amplitude: 0.7
        },
        features: [
          {
            type: TerrainFeatureType.MOUNTAIN,
            params: {
              count: 5,
              height: 1.0,
              width: 0.5,
              roughness: 0.7,
              sharpness: 0.3,
              seed: 12345
            },
            weight: 1.0
          },
          {
            type: TerrainFeatureType.RIVER,
            params: {
              count: 3,
              depth: 0.3,
              width: 0.1,
              smoothness: 0.5,
              seed: 54321
            },
            weight: 0.8
          }
        ],
        seed: 123456
      });

      Debug.log('VegetationSystemExample', '地形生成完成');
    } catch (error) {
      Debug.error('VegetationSystemExample', '地形生成失败', error);
    }
  }

  /**
   * 创建植被
   */
  private createVegetation(): void {
    // 创建植被实体
    this.vegetationEntity = new Entity('vegetation');
    this.vegetationEntity.addComponent(new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));

    // 创建植被组件
    const vegetationComponent = new VegetationComponent({
      terrainEntity: this.terrainEntity.id,
      autoGenerate: true,
      useInstancing: true,
      useLOD: true,
      useWind: true,
      windParams: {
        strength: 0.1,
        direction: new THREE.Vector2(1, 0),
        frequency: 0.2,
        turbulence: 0.1
      },
      items: [
        {
          model: 'assets/models/tree1.glb',
          density: 0.0005,
          minScale: 0.8,
          maxScale: 1.2,
          minHeight: 10,
          maxHeight: 60,
          slopeMin: 0,
          slopeMax: 20,
          randomRotation: true,
          randomOffset: 2,
          windEffect: true,
          colorVariation: {
            enabled: true,
            hue: 0.05,
            saturation: 0.1,
            lightness: 0.1
          }
        },
        {
          model: 'assets/models/tree2.glb',
          density: 0.0003,
          minScale: 0.7,
          maxScale: 1.0,
          minHeight: 20,
          maxHeight: 70,
          slopeMin: 0,
          slopeMax: 30,
          randomRotation: true,
          randomOffset: 2,
          windEffect: true,
          colorVariation: {
            enabled: true,
            hue: 0.05,
            saturation: 0.1,
            lightness: 0.1
          }
        },
        {
          model: 'assets/models/grass1.glb',
          density: 0.01,
          minScale: 0.5,
          maxScale: 1.5,
          minHeight: 5,
          maxHeight: 50,
          slopeMin: 0,
          slopeMax: 40,
          randomRotation: true,
          randomOffset: 0.5,
          windEffect: true,
          colorVariation: {
            enabled: true,
            hue: 0.1,
            saturation: 0.2,
            lightness: 0.2
          }
        },
        {
          model: 'assets/models/rock1.glb',
          density: 0.0008,
          minScale: 0.3,
          maxScale: 1.0,
          minHeight: 5,
          maxHeight: 80,
          slopeMin: 10,
          slopeMax: 60,
          randomRotation: true,
          randomOffset: 1,
          windEffect: false
        }
      ]
    });
    this.vegetationEntity.addComponent(vegetationComponent);

    // 添加到世界
    this.world.addEntity(this.vegetationEntity);
  }
}

// 创建示例
new VegetationSystemExample();
