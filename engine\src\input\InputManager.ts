/**
 * 输入管理器
 * 用于管理输入设备和处理输入映射
 */
import { EventEmitter, EventCallback } from '../utils/EventEmitter';
import { InputDevice } from './InputDevice';
import { InputMapping } from './InputMapping';
import { InputAction } from './InputAction';
import { InputBinding } from './InputBinding';
import { KeyboardDevice } from './devices/KeyboardDevice';
import { MouseDevice } from './devices/MouseDevice';
import { GamepadDevice } from './devices/GamepadDevice';
import { TouchDevice } from './devices/TouchDevice';
import { XRDevice } from './devices/XRDevice';
import { GestureDevice } from './devices/GestureDevice';
import { VoiceDevice } from './devices/VoiceDevice';

/**
 * 输入管理器选项
 */
export interface InputManagerOptions {
  /** 目标元素 */
  element?: HTMLElement;
  /** 是否阻止默认行为 */
  preventDefault?: boolean;
  /** 是否阻止事件传播 */
  stopPropagation?: boolean;
  /** 是否启用键盘输入 */
  enableKeyboard?: boolean;
  /** 是否启用鼠标输入 */
  enableMouse?: boolean;
  /** 是否启用触摸输入 */
  enableTouch?: boolean;
  /** 是否启用游戏手柄输入 */
  enableGamepad?: boolean;
  /** 是否启用XR输入 */
  enableXR?: boolean;
  /** 是否启用手势输入 */
  enableGesture?: boolean;
  /** 是否启用语音输入 */
  enableVoice?: boolean;
  /** 手势设备选项 */
  gestureOptions?: any;
  /** 语音设备选项 */
  voiceOptions?: any;
}

/**
 * 输入管理器
 * 用于管理输入设备和处理输入映射
 */
export class InputManager {
  /** 单例实例 */
  private static instance: InputManager;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 输入设备列表 */
  private devices: Map<string, InputDevice> = new Map();

  /** 输入映射列表 */
  private mappings: Map<string, InputMapping> = new Map();

  /** 输入动作列表 */
  private actions: Map<string, InputAction> = new Map();

  /** 输入绑定列表 */
  private bindings: Map<string, InputBinding> = new Map();

  /** 目标元素 */
  private element: HTMLElement;

  /** 是否阻止默认行为 */
  private preventDefault: boolean;

  /** 是否阻止事件传播 */
  private stopPropagation: boolean;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /**
   * 创建输入管理器
   * @param options 输入管理器选项
   */
  constructor(options: InputManagerOptions = {}) {
    this.element = options.element || document.body;
    this.preventDefault = options.preventDefault !== undefined ? options.preventDefault : true;
    this.stopPropagation = options.stopPropagation !== undefined ? options.stopPropagation : false;

    // 初始化输入设备
    if (options.enableKeyboard !== false) {
      this.addDevice(new KeyboardDevice(this.element, this.preventDefault, this.stopPropagation));
    }

    if (options.enableMouse !== false) {
      this.addDevice(new MouseDevice(this.element, this.preventDefault, this.stopPropagation));
    }

    if (options.enableTouch !== false) {
      this.addDevice(new TouchDevice(this.element, this.preventDefault, this.stopPropagation));
    }

    if (options.enableGamepad !== false) {
      this.addDevice(new GamepadDevice());
    }

    if (options.enableXR !== false) {
      this.addDevice(new XRDevice());
    }

    if (options.enableGesture) {
      this.addDevice(new GestureDevice(options.gestureOptions));
    }

    if (options.enableVoice) {
      this.addDevice(new VoiceDevice(options.voiceOptions));
    }

    // 设置单例实例
    InputManager.instance = this;
  }

  /**
   * 获取单例实例
   * @returns 输入管理器实例
   */
  public static getInstance(): InputManager {
    if (!InputManager.instance) {
      InputManager.instance = new InputManager();
    }
    return InputManager.instance;
  }

  /**
   * 初始化
   */
  public initialize(): void {
    if (this.initialized) return;

    // 初始化所有设备
    for (const device of this.devices.values()) {
      device.initialize();
    }

    this.initialized = true;
  }

  /**
   * 更新
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || this.destroyed) return;

    // 更新所有设备
    for (const device of this.devices.values()) {
      device.update(deltaTime);
    }

    // 处理输入动作
    this.processActions();
  }

  /**
   * 销毁
   */
  public destroy(): void {
    if (this.destroyed) return;

    // 销毁所有设备
    for (const device of this.devices.values()) {
      device.destroy();
    }

    // 清空设备列表
    this.devices.clear();

    // 清空映射列表
    this.mappings.clear();

    // 清空动作列表
    this.actions.clear();

    // 清空绑定列表
    this.bindings.clear();

    this.destroyed = true;
  }

  /**
   * 添加设备
   * @param device 输入设备
   */
  public addDevice(device: InputDevice): void {
    this.devices.set(device.getName(), device);

    // 如果已初始化，则初始化设备
    if (this.initialized) {
      device.initialize();
    }
  }

  /**
   * 获取设备
   * @param name 设备名称
   * @returns 输入设备
   */
  public getDevice<T extends InputDevice>(name: string): T | undefined {
    return this.devices.get(name) as T | undefined;
  }

  /**
   * 添加映射
   * @param mapping 输入映射
   */
  public addMapping(mapping: InputMapping): void {
    this.mappings.set(mapping.getName(), mapping);
  }

  /**
   * 获取映射
   * @param name 映射名称
   * @returns 输入映射
   */
  public getMapping(name: string): InputMapping | undefined {
    return this.mappings.get(name);
  }

  /**
   * 添加动作
   * @param action 输入动作
   */
  public addAction(action: InputAction): void {
    this.actions.set(action.getName(), action);
  }

  /**
   * 获取动作
   * @param name 动作名称
   * @returns 输入动作
   */
  public getAction(name: string): InputAction | undefined {
    return this.actions.get(name);
  }

  /**
   * 添加绑定
   * @param binding 输入绑定
   */
  public addBinding(binding: InputBinding): void {
    this.bindings.set(binding.getName(), binding);
  }

  /**
   * 获取绑定
   * @param name 绑定名称
   * @returns 输入绑定
   */
  public getBinding(name: string): InputBinding | undefined {
    return this.bindings.get(name);
  }

  /**
   * 处理输入动作
   */
  private processActions(): void {
    // 处理所有动作
    for (const action of this.actions.values()) {
      // 获取动作的绑定
      const binding = this.bindings.get(action.getName());
      if (!binding) continue;

      // 获取绑定的映射
      const mapping = this.mappings.get(binding.getMappingName());
      if (!mapping) continue;

      // 获取映射的设备
      const device = this.devices.get(mapping.getDeviceName());
      if (!device) continue;

      // 检查设备输入是否满足映射条件
      const value = mapping.evaluate(device);

      // 更新动作状态
      action.update(value);

      // 如果动作状态发生变化，触发事件
      if (action.hasChanged()) {
        this.eventEmitter.emit(action.getName(), action);
      }
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   * @returns 当前实例，用于链式调用
   */
  public on(event: string, callback: EventCallback): this {
    this.eventEmitter.on(event, callback);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   * @returns 当前实例，用于链式调用
   */
  public off(event: string, callback: EventCallback): this {
    this.eventEmitter.off(event, callback);
    return this;
  }
}
