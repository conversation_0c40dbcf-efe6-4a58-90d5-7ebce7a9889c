/**
 * 动画子片段
 * 用于从动画剪辑中提取子片段
 */
import * as THREE from 'three';
/**
 * 子片段事件类型
 */
export declare enum SubClipEventType {
    /** 子片段创建 */
    CREATED = "created",
    /** 子片段更新 */
    UPDATED = "updated",
    /** 子片段删除 */
    DELETED = "deleted",
    /** 子片段播放 */
    PLAYED = "played",
    /** 子片段停止 */
    STOPPED = "stopped",
    /** 子片段循环 */
    LOOPED = "looped"
}
/**
 * 子片段配置
 */
export interface SubClipConfig {
    /** 子片段名称 */
    name?: string;
    /** 原始剪辑名称 */
    originalClipName?: string;
    /** 开始时间（秒） */
    startTime?: number;
    /** 结束时间（秒） */
    endTime?: number;
    /** 是否循环 */
    loop?: boolean;
    /** 是否反向播放 */
    reverse?: boolean;
    /** 播放速度 */
    timeScale?: number;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 动画子片段
 */
export declare class AnimationSubClip {
    /** 子片段名称 */
    private name;
    /** 原始剪辑名称 */
    private originalClipName;
    /** 开始时间（秒） */
    private startTime;
    /** 结束时间（秒） */
    private endTime;
    /** 是否循环 */
    private loop;
    /** 是否反向播放 */
    private reverse;
    /** 播放速度 */
    private timeScale;
    /** 是否启用调试 */
    private debug;
    /** 事件发射器 */
    private eventEmitter;
    /** 子片段 */
    private subClip;
    /** 原始剪辑 */
    private originalClip;
    /** 子片段列表 */
    private subClips;
    /**
     * 创建动画子片段
     * @param config 配置
     */
    constructor(config?: SubClipConfig);
    /**
     * 获取子片段名称
     * @returns 子片段名称
     */
    getName(): string;
    /**
     * 设置子片段名称
     * @param name 子片段名称
     */
    setName(name: string): void;
    /**
     * 获取原始剪辑名称
     * @returns 原始剪辑名称
     */
    getOriginalClipName(): string;
    /**
     * 设置原始剪辑名称
     * @param name 原始剪辑名称
     */
    setOriginalClipName(name: string): void;
    /**
     * 获取开始时间
     * @returns 开始时间（秒）
     */
    getStartTime(): number;
    /**
     * 设置开始时间
     * @param time 开始时间（秒）
     */
    setStartTime(time: number): void;
    /**
     * 获取结束时间
     * @returns 结束时间（秒）
     */
    getEndTime(): number;
    /**
     * 设置结束时间
     * @param time 结束时间（秒）
     */
    setEndTime(time: number): void;
    /**
     * 获取是否循环
     * @returns 是否循环
     */
    getLoop(): boolean;
    /**
     * 设置是否循环
     * @param loop 是否循环
     */
    setLoop(loop: boolean): void;
    /**
     * 获取是否反向播放
     * @returns 是否反向播放
     */
    getReverse(): boolean;
    /**
     * 设置是否反向播放
     * @param reverse 是否反向播放
     */
    setReverse(reverse: boolean): void;
    /**
     * 获取播放速度
     * @returns 播放速度
     */
    getTimeScale(): number;
    /**
     * 设置播放速度
     * @param timeScale 播放速度
     */
    setTimeScale(timeScale: number): void;
    /**
     * 获取子片段
     * @returns 子片段
     */
    getSubClip(): THREE.AnimationClip | null;
    /**
     * 设置原始剪辑
     * @param clip 原始剪辑
     */
    setOriginalClip(clip: THREE.AnimationClip): void;
    /**
     * 更新子片段
     */
    private updateSubClip;
    /**
     * 添加子片段
     * @param subClip 子片段
     */
    addSubClip(subClip: AnimationSubClip): void;
    /**
     * 移除子片段
     * @param subClip 子片段
     */
    removeSubClip(subClip: AnimationSubClip): void;
    /**
     * 获取子片段列表
     * @returns 子片段列表
     */
    getSubClips(): AnimationSubClip[];
    /**
     * 清空子片段列表
     */
    clearSubClips(): void;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: SubClipEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: SubClipEventType, listener: (data: any) => void): void;
    /**
     * 混合子片段
     * @param weight 权重
     * @returns 混合后的子片段
     */
    blendSubClips(weight?: number): THREE.AnimationClip | null;
}
