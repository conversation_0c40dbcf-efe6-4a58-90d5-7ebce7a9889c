/**
 * 数据库配置
 */
import { registerAs } from '@nestjs/config';
import type {  TypeOrmModuleOptions  } from '@nestjs/typeorm';
import { AIModel } from '../entities/ai-model.entity';
import { ModelVersion } from '../entities/model-version.entity';
import { InferenceLog } from '../entities/inference-log.entity';
import { ModelMetrics } from '../entities/model-metrics.entity';

export const databaseConfig = registerAs('database', (): TypeOrmModuleOptions => {
  const dbType = process.env.DB_TYPE || 'mysql';

  // 基础配置
  const baseConfig = {
    entities: [
      AIModel,
      ModelVersion,
      InferenceLog,
      ModelMetrics,
    ],
    synchronize: process.env.NODE_ENV === 'development',
    logging: process.env.NODE_ENV === 'development',
    retryAttempts: 3,
    retryDelay: 3000,
  };

  // 根据数据库类型返回不同配置
  if (dbType === 'sqlite') {
    return {
      ...baseConfig,
      type: 'sqlite',
      database: process.env.DB_DATABASE || './data/ai_model_service.db',
    } as TypeOrmModuleOptions;
  }

  // MySQL 配置
  return {
    ...baseConfig,
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306', 10),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_DATABASE || 'ai_model_service',
    timezone: '+08:00',
    charset: 'utf8mb4',
    extra: {
      connectionLimit: 10,
      ssl: false,
    },
    poolSize: 10,
    connectTimeout: 60000,
  } as TypeOrmModuleOptions;
});
