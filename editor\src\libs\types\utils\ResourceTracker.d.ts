/**
 * 资源跟踪器
 * 用于跟踪Three.js资源的内存占用
 */
import * as THREE from 'three';
/**
 * 资源跟踪器配置接口
 */
export interface ResourceTrackerConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否启用调试输出 */
    debug?: boolean;
    /** 是否自动跟踪所有资源 */
    autoTrackAll?: boolean;
    /** 是否自动计算资源大小 */
    autoCalculateSize?: boolean;
}
/**
 * 资源跟踪器类
 * 用于跟踪Three.js资源的内存占用
 */
export declare class ResourceTracker {
    private static instance;
    /** 配置 */
    private config;
    /** 内存分析器 */
    private memoryAnalyzer;
    /** 已跟踪的资源映射 */
    private trackedResources;
    /** 资源ID计数器 */
    private idCounter;
    /**
     * 获取单例实例
     */
    static getInstance(): ResourceTracker;
    /**
     * 私有构造函数
     */
    private constructor();
    /**
     * 配置资源跟踪器
     * @param config 配置
     */
    configure(config: ResourceTrackerConfig): void;
    /**
     * 启动资源跟踪器
     */
    start(): void;
    /**
     * 停止资源跟踪器
     */
    stop(): void;
    /**
     * 跟踪资源
     * @param resource Three.js资源
     * @param name 资源名称（可选）
     * @returns 资源ID
     */
    track(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry, name?: string): string;
    /**
     * 取消跟踪资源
     * @param resourceId 资源ID
     */
    untrack(resourceId: string): void;
    /**
     * 取消跟踪资源
     * @param resource Three.js资源
     */
    untrackResource(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): void;
    /**
     * 获取资源ID
     * @param resource Three.js资源
     * @returns 资源ID，如果未跟踪则返回空字符串
     */
    getResourceId(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): string;
    /**
     * 生成资源ID
     * @param resource Three.js资源
     * @returns 资源ID
     */
    private generateResourceId;
    /**
     * 获取资源类型
     * @param resource Three.js资源
     * @returns 资源类型
     */
    private getResourceType;
    /**
     * 获取资源名称
     * @param resource Three.js资源
     * @returns 资源名称
     */
    private getResourceName;
    /**
     * 计算资源大小
     * @param resource Three.js资源
     * @returns 资源大小（字节）
     */
    private calculateResourceSize;
    /**
     * 计算纹理大小
     * @param texture 纹理
     * @returns 纹理大小（字节）
     */
    private calculateTextureSize;
    /**
     * 计算几何体大小
     * @param geometry 几何体
     * @returns 几何体大小（字节）
     */
    private calculateGeometrySize;
    /**
     * 计算材质大小
     * @param material 材质
     * @returns 材质大小（字节）
     */
    private calculateMaterialSize;
    /**
     * 计算3D对象大小
     * @param object 3D对象
     * @returns 3D对象大小（字节）
     */
    private calculateObject3DSize;
    /**
     * 覆盖Three.js方法以自动跟踪资源
     */
    private overrideThreeMethods;
    /**
     * 格式化字节数
     * @param bytes 字节数
     * @returns 格式化后的字符串
     */
    private formatBytes;
}
