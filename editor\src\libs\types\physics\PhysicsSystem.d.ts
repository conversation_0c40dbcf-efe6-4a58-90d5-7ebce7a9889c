/**
 * 物理系统
 * 基于cannon.js实现物理模拟
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import type { PhysicsBody } from './PhysicsBody';
import { CharacterControllerComponent } from './components/CharacterControllerComponent';
import { PhysicsRaycastResult } from './PhysicsRaycastResult';
import { ContinuousCollisionDetection, CCDOptions } from './ccd/ContinuousCollisionDetection';
import { PhysicsDebugger } from './debug/PhysicsDebugger';
import { EnhancedPhysicsDebugger } from './debug/EnhancedPhysicsDebugger';
export interface PhysicsSystemOptions {
    /** 重力 */
    gravity?: {
        x: number;
        y: number;
        z: number;
    };
    /** 物理更新频率（Hz） */
    updateFrequency?: number;
    /** 是否启用休眠 */
    allowSleep?: boolean;
    /** 是否启用连续碰撞检测 */
    enableCCD?: boolean;
    /** 连续碰撞检测选项 */
    ccdOptions?: CCDOptions;
    /** 是否显示调试信息 */
    debug?: boolean;
    /** 是否使用增强型调试器 */
    useEnhancedDebugger?: boolean;
    /** 调试器选项 */
    debuggerOptions?: any;
    /** 迭代次数 */
    iterations?: number;
}
export declare class PhysicsSystem extends System {
    /** 系统类型 */
    static readonly type: string;
    /** 物理世界 */
    private physicsWorld;
    /** 物理更新频率（Hz） */
    private updateFrequency;
    /** 物理更新时间步长（秒） */
    private fixedTimeStep;
    /** 物理更新累积时间 */
    private accumulator;
    /** 物理体映射 */
    private bodies;
    /** 碰撞器映射 */
    private colliders;
    /** 约束映射 */
    private constraints;
    /** 角色控制器映射 */
    private characterControllers;
    /** 连续碰撞检测 */
    private ccd;
    /** 调试渲染器 */
    private debugRenderer;
    /** 是否启用调试 */
    private debug;
    /** 是否使用增强型调试器 */
    private useEnhancedDebugger;
    /** 物理调试器 */
    private physicsDebugger;
    /** 调试网格映射 */
    private debugMeshes;
    /** 碰撞检测器 */
    private collisionDetector;
    /**
     * 创建物理系统
     * @param options 物理系统选项
     */
    constructor(options?: PhysicsSystemOptions);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 处理实体创建事件
     * @param entity 创建的实体
     */
    private handleEntityCreated;
    /**
     * 处理实体移除事件
     * @param entity 移除的实体
     */
    private handleEntityRemoved;
    /**
     * 设置实体的物理属性
     * @param entity 实体
     */
    private setupEntityPhysics;
    /**
     * 移除实体的物理属性
     * @param entity 实体
     */
    private removeEntityPhysics;
    /**
     * 处理碰撞事件
     * @param event 碰撞事件
     */
    private handleCollisionEvent;
    /**
     * 固定时间步长更新
     * @param _fixedDeltaTime 固定帧间隔时间（秒）- 未使用，使用 this.fixedTimeStep 代替
     */
    fixedUpdate(_fixedDeltaTime: number): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新调试渲染器
     */
    private updateDebugRenderer;
    /**
     * 创建调试网格
     * @param body 物理体
     * @returns 调试网格
     */
    private createDebugMesh;
    /**
     * 获取调试渲染器场景
     * @returns 调试渲染器场景
     */
    getDebugRenderer(): THREE.Scene | null;
    /**
     * 设置重力
     * @param x X轴重力
     * @param y Y轴重力
     * @param z Z轴重力
     */
    setGravity(x: number, y: number, z: number): void;
    /**
     * 获取重力
     * @returns 重力向量
     */
    getGravity(): CANNON.Vec3;
    /**
     * 射线检测（最近的一个）
     * @param from 起点
     * @param to 终点
     * @param options 选项
     * @returns 射线检测结果
     */
    raycastClosest(from: THREE.Vector3 | {
        x: number;
        y: number;
        z: number;
    }, to: THREE.Vector3 | {
        x: number;
        y: number;
        z: number;
    }, options?: {
        skipBackfaces?: boolean;
        collisionFilterMask?: number;
        collisionFilterGroup?: number;
    }): PhysicsRaycastResult;
    /**
     * 射线检测（所有相交的）
     * @param from 起点
     * @param to 终点
     * @param options 选项
     * @returns 射线检测结果数组
     */
    raycastAll(from: THREE.Vector3 | {
        x: number;
        y: number;
        z: number;
    }, to: THREE.Vector3 | {
        x: number;
        y: number;
        z: number;
    }, options?: {
        skipBackfaces?: boolean;
        collisionFilterMask?: number;
        collisionFilterGroup?: number;
    }): PhysicsRaycastResult[];
    /**
     * 射线检测（任意一个）
     * @param from 起点
     * @param to 终点
     * @param options 选项
     * @returns 射线检测结果
     */
    raycast(from: THREE.Vector3 | {
        x: number;
        y: number;
        z: number;
    }, to: THREE.Vector3 | {
        x: number;
        y: number;
        z: number;
    }, options?: {
        skipBackfaces?: boolean;
        collisionFilterMask?: number;
        collisionFilterGroup?: number;
    }): PhysicsRaycastResult;
    /**
     * 获取物理世界
     * @returns 物理世界
     */
    getPhysicsWorld(): CANNON.World;
    /**
     * 获取实体的物理体
     * @param entity 实体
     * @returns 物理体
     */
    getPhysicsBody(entity: Entity): PhysicsBody | null;
    /**
     * 设置调试模式
     * @param debug 是否启用调试
     * @param useEnhancedDebugger 是否使用增强型调试器
     * @param debuggerOptions 调试器选项
     */
    setDebug(debug: boolean, useEnhancedDebugger?: boolean, debuggerOptions?: any): void;
    /**
     * 是否启用调试
     * @returns 是否启用调试
     */
    isDebug(): boolean;
    /**
     * 是否使用增强型调试器
     * @returns 是否使用增强型调试器
     */
    isUsingEnhancedDebugger(): boolean;
    /**
     * 获取物理调试器
     * @returns 物理调试器
     */
    getPhysicsDebugger(): PhysicsDebugger | EnhancedPhysicsDebugger | null;
    /**
     * 计算角色控制器移动
     * @param entity 实体
     * @param desiredTranslation 期望的移动向量
     * @param filterGroups 碰撞组过滤
     * @param filterPredicate 碰撞过滤谓词函数
     */
    computeCharacterControllerMovement(entity: Entity, desiredTranslation: THREE.Vector3, filterGroups?: number, filterPredicate?: (body: CANNON.Body) => boolean): void;
    /**
     * 获取角色控制器计算出的移动向量
     * @param entity 实体
     * @returns 计算出的移动向量
     */
    getCharacterControllerComputedMovement(entity: Entity): THREE.Vector3;
    /**
     * 检查实体是否有角色控制器
     * @param entity 实体
     * @returns 是否有角色控制器
     */
    hasCharacterController(entity: Entity): boolean;
    /**
     * 获取角色控制器
     * @param entity 实体
     * @returns 角色控制器
     */
    getCharacterController(entity: Entity): CharacterControllerComponent | null;
    /**
     * 启用实体的连续碰撞检测
     * @param entity 实体
     */
    enableEntityCCD(entity: Entity): void;
    /**
     * 禁用实体的连续碰撞检测
     * @param entity 实体
     */
    disableEntityCCD(entity: Entity): void;
    /**
     * 检查实体是否启用连续碰撞检测
     * @param entity 实体
     * @returns 是否启用连续碰撞检测
     */
    isEntityCCDEnabled(entity: Entity): boolean;
    /**
     * 获取连续碰撞检测
     * @returns 连续碰撞检测
     */
    getCCD(): ContinuousCollisionDetection | null;
    /**
     * 销毁系统
     */
    dispose(): void;
}
