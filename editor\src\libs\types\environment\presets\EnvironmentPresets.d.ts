/**
 * 环境预设
 *
 * 提供一系列预定义的环境响应规则，可以快速应用到角色上。
 * 这些预设涵盖了常见的环境交互场景，如雨天、雪天、炎热天气等。
 */
import { EnvironmentResponseRule } from '../components/EnvironmentResponseComponent';
/**
 * 创建雨天响应规则
 * @returns 响应规则
 */
export declare function createRainyWeatherResponse(): EnvironmentResponseRule;
/**
 * 创建雪天响应规则
 * @returns 响应规则
 */
export declare function createSnowyWeatherResponse(): EnvironmentResponseRule;
/**
 * 创建炎热天气响应规则
 * @returns 响应规则
 */
export declare function createHotWeatherResponse(): EnvironmentResponseRule;
/**
 * 创建黑暗环境响应规则
 * @returns 响应规则
 */
export declare function createDarkEnvironmentResponse(): EnvironmentResponseRule;
/**
 * 创建水中环境响应规则
 * @returns 响应规则
 */
export declare function createUnderwaterResponse(): EnvironmentResponseRule;
/**
 * 获取所有预设响应规则
 * @returns 响应规则数组
 */
export declare function getAllEnvironmentPresets(): EnvironmentResponseRule[];
