# 使用官方Node.js运行时作为基础镜像
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制绑定服务代码
COPY binding-service/package*.json ./binding-service/
WORKDIR /app/binding-service

# 安装依赖
RUN npm ci --only=production

# 复制绑定服务源代码
COPY binding-service/ ./

# 构建应用
RUN npm run build

# 生产环境镜像
FROM node:22-alpine AS production

# 安装必要的系统依赖
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 设置工作目录
WORKDIR /app

# 从builder阶段复制构建产物
COPY --from=builder --chown=nextjs:nodejs /app/binding-service/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/binding-service/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/binding-service/package.json ./package.json

# 切换到非root用户
USER nextjs

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# 使用dumb-init作为PID 1
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["node", "dist/main.js"]
