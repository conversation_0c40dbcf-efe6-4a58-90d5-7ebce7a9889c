/**
 * 场景实例管理器
 * 负责场景模板和实例的创建、管理和参数化配置
 */
import { Scene } from './Scene';
import type { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { SceneSerializer, SceneSerializedData } from './SceneSerializer';
import * as THREE from 'three';

/**
 * 模板变体
 */
export interface TemplateVariant {
  /** 变体ID */
  id: string;
  /** 变体名称 */
  name: string;
  /** 变体描述 */
  description?: string;
  /** 缩略图URL */
  thumbnailUrl?: string;
  /** 参数值 */
  parameterValues: Record<string, any>;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 自定义数据 */
  userData?: Record<string, any>;
}

/**
 * 场景模板数据
 */
export interface SceneTemplate {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description?: string;
  /** 缩略图URL */
  thumbnailUrl?: string;
  /** 序列化数据 */
  serializedData: SceneSerializedData;
  /** 参数定义 */
  parameters: TemplateParameter[];
  /** 变体列表 */
  variants?: TemplateVariant[];
  /** 默认变体ID */
  defaultVariantId?: string;
  /** 分类 */
  category?: string;
  /** 标签 */
  tags?: string[];
  /** 版本 */
  version?: string;
  /** 作者 */
  author?: string;
  /** 许可证 */
  license?: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 自定义数据 */
  userData?: Record<string, any>;
}

/**
 * 模板参数类型
 */
export enum TemplateParameterType {
  /** 字符串 */
  STRING = 'string',
  /** 数字 */
  NUMBER = 'number',
  /** 整数 */
  INTEGER = 'integer',
  /** 布尔值 */
  BOOLEAN = 'boolean',
  /** 颜色 */
  COLOR = 'color',
  /** 二维向量 */
  VECTOR2 = 'vector2',
  /** 三维向量 */
  VECTOR3 = 'vector3',
  /** 四维向量 */
  VECTOR4 = 'vector4',
  /** 选择 */
  SELECT = 'select',
  /** 多选 */
  MULTI_SELECT = 'multi_select',
  /** 滑块 */
  SLIDER = 'slider',
  /** 范围滑块 */
  RANGE_SLIDER = 'range_slider',
  /** 纹理 */
  TEXTURE = 'texture',
  /** 材质 */
  MATERIAL = 'material',
  /** 网格 */
  MESH = 'mesh',
  /** 动画 */
  ANIMATION = 'animation',
  /** 音频 */
  AUDIO = 'audio',
  /** 实体 */
  ENTITY = 'entity',
  /** 变换 */
  TRANSFORM = 'transform',
  /** 路径 */
  PATH = 'path',
  /** 曲线 */
  CURVE = 'curve',
  /** 脚本 */
  SCRIPT = 'script',
  /** JSON */
  JSON = 'json',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 模板参数
 */
export interface TemplateParameter {
  /** 参数ID */
  id: string;
  /** 参数名称 */
  name: string;
  /** 参数描述 */
  description?: string;
  /** 参数类型 */
  type: TemplateParameterType | string;
  /** 默认值 */
  defaultValue: any;
  /** 最小值（数值类型） */
  min?: number;
  /** 最大值（数值类型） */
  max?: number;
  /** 步长（数值类型） */
  step?: number;
  /** 选项（select类型） */
  options?: { value: any; label: string }[];
  /** 是否必需 */
  required?: boolean;
  /** 是否只读 */
  readonly?: boolean;
  /** 是否隐藏 */
  hidden?: boolean;
  /** 条件显示表达式 */
  showIf?: string;
  /** 验证规则 */
  validation?: {
    /** 正则表达式 */
    pattern?: string;
    /** 自定义验证函数 */
    validator?: string;
    /** 错误消息 */
    message?: string;
  };
  /** 分组 */
  group?: string;
  /** 排序 */
  order?: number;
  /** 单位（数值类型） */
  unit?: string;
  /** 自定义渲染器 */
  renderer?: string;
  /** 自定义编辑器 */
  editor?: string;
  /** 自定义数据 */
  userData?: Record<string, any>;
}

/**
 * 场景实例数据
 */
export interface SceneInstance {
  /** 实例ID */
  id: string;
  /** 实例名称 */
  name: string;
  /** 模板ID */
  templateId: string;
  /** 变体ID */
  variantId?: string;
  /** 是否可见 */
  visible: boolean;
  /** 位置 */
  position: THREE.Vector3;
  /** 旋转 */
  rotation: THREE.Euler;
  /** 缩放 */
  scale: THREE.Vector3;
  /** 参数值 */
  parameters: Record<string, any>;
  /** 根实体ID */
  rootEntityId?: string;
  /** 是否锁定 */
  locked?: boolean;
  /** 是否静态 */
  isStatic?: boolean;
  /** 层级 */
  layer?: string;
  /** 标签 */
  tags?: string[];
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 自定义数据 */
  userData?: Record<string, any>;
}

/**
 * 场景实例管理器选项
 */
export interface SceneInstanceManagerOptions {
  /** 场景序列化器 */
  sceneSerializer?: SceneSerializer;
  /** 是否启用资源共享 */
  enableResourceSharing?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 场景实例管理器
 */
export class SceneInstanceManager extends EventEmitter {
  /** 场景序列化器 */
  private sceneSerializer: SceneSerializer;

  /** 是否启用资源共享 */
  private enableResourceSharing: boolean;

  /** 是否启用调试模式 */
  private debug: boolean;

  /** 模板映射 */
  private templates: Map<string, SceneTemplate> = new Map();

  /** 实例映射 */
  private instances: Map<string, SceneInstance> = new Map();

  /** 实例实体映射 */
  private instanceEntities: Map<string, Entity> = new Map();

  /** 当前活动场景 */
  private activeScene: Scene | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景实例管理器
   * @param options 选项
   */
  constructor(options: SceneInstanceManagerOptions = {}) {
    super();

    this.sceneSerializer = options.sceneSerializer || new SceneSerializer();
    this.enableResourceSharing = options.enableResourceSharing !== undefined ? options.enableResourceSharing : true;
    this.debug = options.debug !== undefined ? options.debug : false;
  }

  /**
   * 初始化实例管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 设置活动场景
   * @param scene 场景
   */
  public setActiveScene(scene: Scene | null): void {
    this.activeScene = scene;
  }

  /**
   * 创建模板
   * @param scene 场景
   * @param name 模板名称
   * @param description 模板描述
   * @param parameters 参数定义
   * @returns 模板ID
   */
  public createTemplate(
    scene: Scene,
    name: string,
    description?: string,
    parameters: TemplateParameter[] = []
  ): string {
    // 序列化场景
    const serializedData = this.sceneSerializer.serializeToObject(scene);

    // 生成模板ID
    const templateId = `template_${Date.now()}`;

    // 创建模板
    const template: SceneTemplate = {
      id: templateId,
      name,
      description,
      serializedData,
      parameters,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 添加到模板映射
    this.templates.set(templateId, template);

    // 发出模板创建事件
    this.emit('templateCreated', template);

    if (this.debug) {
      console.log(`[SceneInstanceManager] 创建模板: ${name} (${templateId})`);
    }

    return templateId;
  }

  /**
   * 更新模板
   * @param templateId 模板ID
   * @param updates 更新内容
   * @returns 是否成功更新
   */
  public updateTemplate(
    templateId: string,
    updates: Partial<Omit<SceneTemplate, 'id' | 'createdAt'>>
  ): boolean {
    const template = this.templates.get(templateId);

    if (!template) {
      console.warn(`找不到模板: ${templateId}`);
      return false;
    }

    // 更新模板
    Object.assign(template, updates);

    // 更新时间
    template.updatedAt = new Date();

    // 发出模板更新事件
    this.emit('templateUpdated', template);

    if (this.debug) {
      console.log(`[SceneInstanceManager] 更新模板: ${template.name} (${templateId})`);
    }

    return true;
  }

  /**
   * 删除模板
   * @param templateId 模板ID
   * @returns 是否成功删除
   */
  public deleteTemplate(templateId: string): boolean {
    const template = this.templates.get(templateId);

    if (!template) {
      console.warn(`找不到模板: ${templateId}`);
      return false;
    }

    // 检查是否有实例使用此模板
    for (const instance of this.instances.values()) {
      if (instance.templateId === templateId) {
        console.warn(`无法删除模板 ${templateId}，因为它正在被实例使用`);
        return false;
      }
    }

    // 从模板映射中移除
    this.templates.delete(templateId);

    // 发出模板删除事件
    this.emit('templateDeleted', templateId);

    if (this.debug) {
      console.log(`[SceneInstanceManager] 删除模板: ${template.name} (${templateId})`);
    }

    return true;
  }

  /**
   * 获取模板
   * @param templateId 模板ID
   * @returns 模板
   */
  public getTemplate(templateId: string): SceneTemplate | null {
    return this.templates.get(templateId) || null;
  }

  /**
   * 获取所有模板
   * @returns 模板数组
   */
  public getAllTemplates(): SceneTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * 创建实例
   * @param templateId 模板ID
   * @param options 实例选项
   * @returns 实例ID
   */
  public createInstance(
    templateId: string,
    options: {
      name?: string;
      variantId?: string;
      position?: THREE.Vector3;
      rotation?: THREE.Euler;
      scale?: THREE.Vector3;
      parameters?: Record<string, any>;
      visible?: boolean;
      locked?: boolean;
      isStatic?: boolean;
      layer?: string;
      tags?: string[];
      userData?: Record<string, any>;
    } = {}
  ): string {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    const template = this.templates.get(templateId);

    if (!template) {
      throw new Error(`找不到模板: ${templateId}`);
    }

    // 生成实例ID
    const instanceId = `instance_${Date.now()}`;

    // 获取变体ID
    let variantId = options.variantId;

    // 如果没有指定变体ID，但模板有默认变体，则使用默认变体
    if (!variantId && template.defaultVariantId && template.variants) {
      variantId = template.defaultVariantId;
    }

    // 获取变体参数值
    let variantParameters: Record<string, any> = {};

    if (variantId && template.variants) {
      const variant = template.variants.find(v => v.id === variantId);

      if (variant) {
        variantParameters = variant.parameterValues;
      }
    }

    // 合并参数值（变体参数 + 用户指定参数）
    const mergedParameters = {
      ...variantParameters,
      ...options.parameters
    };

    // 创建实例
    const instance: SceneInstance = {
      id: instanceId,
      name: options.name || `${template.name} 实例`,
      templateId,
      variantId,
      visible: options.visible !== undefined ? options.visible : true,
      position: options.position ? options.position.clone() : new THREE.Vector3(),
      rotation: options.rotation ? options.rotation.clone() : new THREE.Euler(),
      scale: options.scale ? options.scale.clone() : new THREE.Vector3(1, 1, 1),
      parameters: mergedParameters,
      locked: options.locked !== undefined ? options.locked : false,
      isStatic: options.isStatic !== undefined ? options.isStatic : false,
      layer: options.layer,
      tags: options.tags || [],
      createdAt: new Date(),
      updatedAt: new Date(),
      userData: options.userData || {}
    };

    // 添加到实例映射
    this.instances.set(instanceId, instance);

    // 实例化模板
    const rootEntity = this.instantiateTemplate(template, instance);

    if (rootEntity) {
      instance.rootEntityId = rootEntity.id;
      this.instanceEntities.set(instanceId, rootEntity);
    }

    // 发出实例创建事件
    this.emit('instanceCreated', instance);

    if (this.debug) {
      console.log(`[SceneInstanceManager] 创建实例: ${instance.name} (${instanceId})`);
    }

    return instanceId;
  }

  /**
   * 实例化模板
   * @param template 模板
   * @param instance 实例
   * @returns 根实体
   */
  private instantiateTemplate(template: SceneTemplate, instance: SceneInstance): Entity | null {
    if (!this.activeScene) {
      return null;
    }

    try {
      // 反序列化模板
      const rootEntity = this.sceneSerializer.deserializeEntity(
        template.serializedData,
        this.activeScene,
        {
          keepExistingEntities: true,
          mergeWithExisting: false
        }
      );

      if (!rootEntity) {
        console.error('实例化模板失败: 无法创建根实体');
        return null;
      }

      // 设置实例名称
      rootEntity.name = instance.name;

      // 设置变换
      const transform = rootEntity.getTransform();
      transform.setPosition(instance.position);
      transform.setRotation(instance.rotation);
      transform.setScale(instance.scale);

      // 应用参数
      this.applyParameters(rootEntity, template, instance.parameters);

      return rootEntity;
    } catch (error) {
      console.error('实例化模板失败:', error);
      return null;
    }
  }

  /**
   * 应用参数
   * @param rootEntity 根实体
   * @param template 模板
   * @param parameters 参数值
   */
  private applyParameters(
    rootEntity: Entity,
    template: SceneTemplate,
    parameters: Record<string, any>
  ): void {
    // 遍历参数定义
    for (const paramDef of template.parameters) {
      // 获取参数值，如果没有提供则使用默认值
      const value = parameters[paramDef.id] !== undefined ? parameters[paramDef.id] : paramDef.defaultValue;

      // 如果参数是隐藏的或只读的，跳过应用
      if (paramDef.hidden || paramDef.readonly) {
        continue;
      }

      // 获取参数目标信息
      const targetInfo = paramDef.userData?.target;

      if (!targetInfo) {
        continue;
      }

      // 解析目标信息
      const { entityPath, componentType, property } = targetInfo;

      // 查找目标实体
      let targetEntity = rootEntity;

      if (entityPath) {
        const pathParts = entityPath.split('/');

        for (const part of pathParts) {
          if (!part) continue;

          const child = targetEntity.getChildByName(part);

          if (!child) {
            console.warn(`找不到实体路径: ${entityPath}`);
            break;
          }

          targetEntity = child;
        }
      }

      // 应用参数值
      try {
        this.applyParameterValue(targetEntity, componentType, property, value, paramDef.type);
      } catch (error) {
        console.error(`应用参数失败: ${paramDef.id}`, error);
      }
    }
  }

  /**
   * 应用参数值
   * @param entity 目标实体
   * @param componentType 组件类型
   * @param property 属性路径
   * @param value 参数值
   * @param paramType 参数类型
   */
  private applyParameterValue(
    entity: Entity,
    componentType: string,
    property: string,
    value: any,
    paramType: TemplateParameterType | string
  ): void {
    // 获取组件
    const component = entity.getComponent(componentType);

    if (!component) {
      console.warn(`找不到组件: ${componentType}`);
      return;
    }

    // 解析属性路径
    const propertyPath = property.split('.');

    // 递归设置属性值
    const setNestedProperty = (obj: any, path: string[], value: any, index = 0): void => {
      if (index === path.length - 1) {
        // 根据参数类型转换值
        obj[path[index]] = this.convertValueByType(value, paramType);
        return;
      }

      if (!obj[path[index]]) {
        obj[path[index]] = {};
      }

      setNestedProperty(obj[path[index]], path, value, index + 1);
    };

    // 设置属性值
    setNestedProperty(component, propertyPath, value);

    // 特殊处理某些组件类型
    this.handleSpecialComponentTypes(entity, component, componentType, property, value, paramType);
  }

  /**
   * 根据参数类型转换值
   * @param value 原始值
   * @param paramType 参数类型
   * @returns 转换后的值
   */
  private convertValueByType(value: any, paramType: TemplateParameterType | string): any {
    switch (paramType) {
      case TemplateParameterType.COLOR:
        // 转换颜色值为THREE.Color
        if (typeof value === 'string') {
          return new THREE.Color(value);
        } else if (Array.isArray(value) && value.length >= 3) {
          return new THREE.Color(value[0], value[1], value[2]);
        }
        return value;

      case TemplateParameterType.VECTOR2:
        // 转换为THREE.Vector2
        if (Array.isArray(value) && value.length >= 2) {
          return new THREE.Vector2(value[0], value[1]);
        } else if (typeof value === 'object' && 'x' in value && 'y' in value) {
          return new THREE.Vector2(value.x, value.y);
        }
        return value;

      case TemplateParameterType.VECTOR3:
        // 转换为THREE.Vector3
        if (Array.isArray(value) && value.length >= 3) {
          return new THREE.Vector3(value[0], value[1], value[2]);
        } else if (typeof value === 'object' && 'x' in value && 'y' in value && 'z' in value) {
          return new THREE.Vector3(value.x, value.y, value.z);
        }
        return value;

      case TemplateParameterType.VECTOR4:
        // 转换为THREE.Vector4
        if (Array.isArray(value) && value.length >= 4) {
          return new THREE.Vector4(value[0], value[1], value[2], value[3]);
        } else if (typeof value === 'object' && 'x' in value && 'y' in value && 'z' in value && 'w' in value) {
          return new THREE.Vector4(value.x, value.y, value.z, value.w);
        }
        return value;

      case TemplateParameterType.TEXTURE:
        // 处理纹理参数
        if (typeof value === 'string') {
          // 加载纹理
          const texture = new THREE.TextureLoader().load(value);
          return texture;
        }
        return value;

      case TemplateParameterType.MATERIAL:
        // 处理材质参数
        // 这里需要根据具体的材质系统实现
        return value;

      case TemplateParameterType.MESH:
        // 处理网格参数
        // 这里需要根据具体的网格系统实现
        return value;

      case TemplateParameterType.ANIMATION:
        // 处理动画参数
        // 这里需要根据具体的动画系统实现
        return value;

      case TemplateParameterType.AUDIO:
        // 处理音频参数
        // 这里需要根据具体的音频系统实现
        return value;

      case TemplateParameterType.TRANSFORM:
        // 处理变换参数
        // 这里需要根据具体的变换系统实现
        return value;

      case TemplateParameterType.JSON:
        // 处理JSON参数
        if (typeof value === 'string') {
          try {
            return JSON.parse(value);
          } catch (error) {
            console.error('解析JSON参数失败:', error);
          }
        }
        return value;

      default:
        // 其他类型直接返回原值
        return value;
    }
  }

  /**
   * 处理特殊组件类型
   * @param entity 实体
   * @param component 组件
   * @param componentType 组件类型
   * @param property 属性路径
   * @param value 参数值
   * @param paramType 参数类型
   */
  private handleSpecialComponentTypes(
    entity: Entity,
    component: any,
    componentType: string,
    property: string,
    value: any,
    paramType: TemplateParameterType | string
  ): void {
    // 处理MeshComponent
    if (componentType === 'MeshComponent') {
      if (property === 'material.color' && paramType === TemplateParameterType.COLOR) {
        // 更新材质颜色
        const mesh = component.getMesh();

        if (mesh && mesh.material) {
          if (Array.isArray(mesh.material)) {
            // 处理多材质
            for (const mat of mesh.material) {
              if (mat.color) {
                mat.color.copy(value);
              }
            }
          } else if (mesh.material.color) {
            // 处理单材质
            mesh.material.color.copy(value);
          }
        }
      } else if (property === 'material.map' && paramType === TemplateParameterType.TEXTURE) {
        // 更新材质贴图
        const mesh = component.getMesh();

        if (mesh && mesh.material) {
          if (Array.isArray(mesh.material)) {
            // 处理多材质
            for (const mat of mesh.material) {
              mat.map = value;
              mat.needsUpdate = true;
            }
          } else {
            // 处理单材质
            mesh.material.map = value;
            mesh.material.needsUpdate = true;
          }
        }
      }
    }

    // 处理LightComponent
    else if (componentType === 'LightComponent') {
      if (property === 'color' && paramType === TemplateParameterType.COLOR) {
        // 更新光源颜色
        const light = component.getLight();

        if (light && light.color) {
          light.color.copy(value);
        }
      }
    }

    // 处理AudioComponent
    else if (componentType === 'AudioComponent') {
      if (property === 'source' && paramType === TemplateParameterType.AUDIO) {
        // 更新音频源
        component.setAudioUrl(value);
      }
    }

    // 处理TransformComponent
    else if (componentType === 'TransformComponent') {
      if (property === 'position' && paramType === TemplateParameterType.VECTOR3) {
        // 更新位置
        entity.getTransform().setPosition(value);
      } else if (property === 'rotation' && paramType === TemplateParameterType.VECTOR3) {
        // 更新旋转
        entity.getTransform().setRotation(new THREE.Euler(value.x, value.y, value.z));
      } else if (property === 'scale' && paramType === TemplateParameterType.VECTOR3) {
        // 更新缩放
        entity.getTransform().setScale(value);
      }
    }
  }

  /**
   * 更新实例
   * @param instanceId 实例ID
   * @param updates 更新内容
   * @returns 是否成功更新
   */
  public updateInstance(
    instanceId: string,
    updates: Partial<Omit<SceneInstance, 'id' | 'templateId' | 'createdAt'>>
  ): boolean {
    const instance = this.instances.get(instanceId);

    if (!instance) {
      console.warn(`找不到实例: ${instanceId}`);
      return false;
    }

    // 保存旧值
    const oldPosition = instance.position.clone();
    const oldRotation = instance.rotation.clone();
    const oldScale = instance.scale.clone();
    const oldVisible = instance.visible;
    const oldVariantId = instance.variantId;
    const oldParameters = { ...instance.parameters };

    // 检查是否更新了变体
    if (updates.variantId !== undefined && updates.variantId !== oldVariantId) {
      const template = this.templates.get(instance.templateId);

      if (template && template.variants) {
        const variant = template.variants.find(v => v.id === updates.variantId);

        if (variant) {
          // 更新变体ID
          instance.variantId = updates.variantId;

          // 合并参数值（变体参数 + 现有参数）
          // 注意：这里我们优先使用现有参数，以保留用户的自定义设置
          instance.parameters = {
            ...variant.parameterValues,
            ...instance.parameters
          };
        }
      }
    }

    // 更新实例
    if (updates.name !== undefined) instance.name = updates.name;
    if (updates.visible !== undefined) instance.visible = updates.visible;
    if (updates.position !== undefined) instance.position.copy(updates.position);
    if (updates.rotation !== undefined) instance.rotation.copy(updates.rotation);
    if (updates.scale !== undefined) instance.scale.copy(updates.scale);
    if (updates.parameters !== undefined) instance.parameters = { ...updates.parameters };
    if (updates.locked !== undefined) instance.locked = updates.locked;
    if (updates.isStatic !== undefined) instance.isStatic = updates.isStatic;
    if (updates.layer !== undefined) instance.layer = updates.layer;
    if (updates.tags !== undefined) instance.tags = [...updates.tags];
    if (updates.userData !== undefined) instance.userData = { ...updates.userData };

    // 更新时间
    instance.updatedAt = new Date();

    // 更新实体
    const rootEntity = this.instanceEntities.get(instanceId);

    if (rootEntity) {
      // 更新名称
      if (updates.name !== undefined) {
        rootEntity.name = updates.name;
      }

      // 更新可见性
      if (updates.visible !== undefined && updates.visible !== oldVisible) {
        rootEntity.setActive(updates.visible);
      }

      // 更新变换
      const transform = rootEntity.getTransform();

      if (updates.position !== undefined && !updates.position.equals(oldPosition)) {
        transform.setPosition(updates.position);
      }

      if (updates.rotation !== undefined &&
          (updates.rotation.x !== oldRotation.x ||
           updates.rotation.y !== oldRotation.y ||
           updates.rotation.z !== oldRotation.z)) {
        transform.setRotation(updates.rotation);
      }

      if (updates.scale !== undefined && !updates.scale.equals(oldScale)) {
        transform.setScale(updates.scale);
      }

      // 更新参数
      if (updates.parameters !== undefined) {
        const template = this.templates.get(instance.templateId);

        if (template) {
          this.applyParameters(rootEntity, template, updates.parameters);
        }
      }
    }

    // 发出实例更新事件
    this.emit('instanceUpdated', instance);

    if (this.debug) {
      console.log(`[SceneInstanceManager] 更新实例: ${instance.name} (${instanceId})`);
    }

    return true;
  }

  /**
   * 删除实例
   * @param instanceId 实例ID
   * @returns 是否成功删除
   */
  public deleteInstance(instanceId: string): boolean {
    const instance = this.instances.get(instanceId);

    if (!instance) {
      console.warn(`找不到实例: ${instanceId}`);
      return false;
    }

    // 删除实体
    const rootEntity = this.instanceEntities.get(instanceId);

    if (rootEntity && this.activeScene) {
      this.activeScene.removeEntity(rootEntity);
    }

    // 从映射中移除
    this.instances.delete(instanceId);
    this.instanceEntities.delete(instanceId);

    // 发出实例删除事件
    this.emit('instanceDeleted', instanceId);

    if (this.debug) {
      console.log(`[SceneInstanceManager] 删除实例: ${instance.name} (${instanceId})`);
    }

    return true;
  }

  /**
   * 获取实例
   * @param instanceId 实例ID
   * @returns 实例
   */
  public getInstance(instanceId: string): SceneInstance | null {
    return this.instances.get(instanceId) || null;
  }

  /**
   * 获取所有实例
   * @returns 实例数组
   */
  public getAllInstances(): SceneInstance[] {
    return Array.from(this.instances.values());
  }

  /**
   * 获取实例实体
   * @param instanceId 实例ID
   * @returns 实体
   */
  public getInstanceEntity(instanceId: string): Entity | null {
    return this.instanceEntities.get(instanceId) || null;
  }

  /**
   * 创建模板变体
   * @param templateId 模板ID
   * @param variantOptions 变体选项
   * @returns 变体ID
   */
  public createTemplateVariant(
    templateId: string,
    variantOptions: {
      name: string;
      description?: string;
      thumbnailUrl?: string;
      parameterValues: Record<string, any>;
      userData?: Record<string, any>;
    }
  ): string {
    const template = this.templates.get(templateId);

    if (!template) {
      throw new Error(`找不到模板: ${templateId}`);
    }

    // 初始化变体数组
    if (!template.variants) {
      template.variants = [];
    }

    // 生成变体ID
    const variantId = `variant_${Date.now()}`;

    // 创建变体
    const variant: TemplateVariant = {
      id: variantId,
      name: variantOptions.name,
      description: variantOptions.description,
      thumbnailUrl: variantOptions.thumbnailUrl,
      parameterValues: { ...variantOptions.parameterValues },
      createdAt: new Date(),
      updatedAt: new Date(),
      userData: variantOptions.userData || {}
    };

    // 添加到变体数组
    template.variants.push(variant);

    // 如果是第一个变体，设为默认变体
    if (template.variants.length === 1 && !template.defaultVariantId) {
      template.defaultVariantId = variantId;
    }

    // 更新模板
    template.updatedAt = new Date();

    // 发出变体创建事件
    this.emit('variantCreated', templateId, variant);

    if (this.debug) {
      console.log(`[SceneInstanceManager] 创建变体: ${variant.name} (${variantId}) for template ${templateId}`);
    }

    return variantId;
  }

  /**
   * 更新模板变体
   * @param templateId 模板ID
   * @param variantId 变体ID
   * @param updates 更新内容
   * @returns 是否成功更新
   */
  public updateTemplateVariant(
    templateId: string,
    variantId: string,
    updates: Partial<Omit<TemplateVariant, 'id' | 'createdAt'>>
  ): boolean {
    const template = this.templates.get(templateId);

    if (!template || !template.variants) {
      return false;
    }

    // 查找变体
    const variantIndex = template.variants.findIndex(v => v.id === variantId);

    if (variantIndex === -1) {
      return false;
    }

    const variant = template.variants[variantIndex];

    // 更新变体
    if (updates.name !== undefined) variant.name = updates.name;
    if (updates.description !== undefined) variant.description = updates.description;
    if (updates.thumbnailUrl !== undefined) variant.thumbnailUrl = updates.thumbnailUrl;
    if (updates.parameterValues !== undefined) variant.parameterValues = { ...updates.parameterValues };
    if (updates.userData !== undefined) variant.userData = { ...updates.userData };

    // 更新时间
    variant.updatedAt = new Date();
    template.updatedAt = new Date();

    // 发出变体更新事件
    this.emit('variantUpdated', templateId, variant);

    if (this.debug) {
      console.log(`[SceneInstanceManager] 更新变体: ${variant.name} (${variantId}) for template ${templateId}`);
    }

    return true;
  }

  /**
   * 删除模板变体
   * @param templateId 模板ID
   * @param variantId 变体ID
   * @returns 是否成功删除
   */
  public deleteTemplateVariant(templateId: string, variantId: string): boolean {
    const template = this.templates.get(templateId);

    if (!template || !template.variants) {
      return false;
    }

    // 查找变体
    const variantIndex = template.variants.findIndex(v => v.id === variantId);

    if (variantIndex === -1) {
      return false;
    }

    // 检查是否有实例使用此变体
    for (const instance of this.instances.values()) {
      if (instance.templateId === templateId && instance.variantId === variantId) {
        console.warn(`无法删除变体 ${variantId}，因为它正在被实例使用`);
        return false;
      }
    }

    // 获取变体名称
    const variantName = template.variants[variantIndex].name;

    // 从变体数组中移除
    template.variants.splice(variantIndex, 1);

    // 如果删除的是默认变体，重新设置默认变体
    if (template.defaultVariantId === variantId) {
      template.defaultVariantId = template.variants.length > 0 ? template.variants[0].id : undefined;
    }

    // 更新模板
    template.updatedAt = new Date();

    // 发出变体删除事件
    this.emit('variantDeleted', templateId, variantId);

    if (this.debug) {
      console.log(`[SceneInstanceManager] 删除变体: ${variantName} (${variantId}) from template ${templateId}`);
    }

    return true;
  }

  /**
   * 设置默认变体
   * @param templateId 模板ID
   * @param variantId 变体ID
   * @returns 是否成功设置
   */
  public setDefaultVariant(templateId: string, variantId: string): boolean {
    const template = this.templates.get(templateId);

    if (!template || !template.variants) {
      return false;
    }

    // 检查变体是否存在
    const variantExists = template.variants.some(v => v.id === variantId);

    if (!variantExists) {
      return false;
    }

    // 设置默认变体
    template.defaultVariantId = variantId;

    // 更新模板
    template.updatedAt = new Date();

    // 发出默认变体变更事件
    this.emit('defaultVariantChanged', templateId, variantId);

    if (this.debug) {
      console.log(`[SceneInstanceManager] 设置默认变体: ${variantId} for template ${templateId}`);
    }

    return true;
  }

  /**
   * 获取模板变体
   * @param templateId 模板ID
   * @param variantId 变体ID
   * @returns 变体
   */
  public getTemplateVariant(templateId: string, variantId: string): TemplateVariant | null {
    const template = this.templates.get(templateId);

    if (!template || !template.variants) {
      return null;
    }

    return template.variants.find(v => v.id === variantId) || null;
  }

  /**
   * 获取模板的所有变体
   * @param templateId 模板ID
   * @returns 变体数组
   */
  public getTemplateVariants(templateId: string): TemplateVariant[] {
    const template = this.templates.get(templateId);

    if (!template || !template.variants) {
      return [];
    }

    return [...template.variants];
  }

  /**
   * 销毁实例管理器
   */
  public dispose(): void {
    // 删除所有实例
    for (const instanceId of this.instances.keys()) {
      this.deleteInstance(instanceId);
    }

    // 清空模板
    this.templates.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
