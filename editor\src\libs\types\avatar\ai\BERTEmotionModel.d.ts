/**
 * BERT情感分析模型
 * 使用BERT模型进行更精确的情感分析
 */
import { EmotionAnalysisResult } from './EmotionBasedAnimationGenerator';
/**
 * BERT模型配置
 */
export interface BERTModelConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 模型路径 */
    modelPath?: string;
    /** 词汇表路径 */
    vocabPath?: string;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 批处理大小 */
    batchSize?: number;
    /** 是否使用量化模型 */
    useQuantized?: boolean;
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存大小 */
    cacheSize?: number;
    /** 是否使用远程API */
    useRemoteAPI?: boolean;
    /** 远程API地址 */
    remoteAPIUrl?: string;
    /** API密钥 */
    apiKey?: string;
}
/**
 * 情感分析请求
 */
export interface EmotionAnalysisRequest {
    /** 文本 */
    text: string;
    /** 详细程度 */
    detail?: 'low' | 'medium' | 'high';
    /** 是否包含次要情感 */
    includeSecondary?: boolean;
    /** 是否包含情感变化 */
    includeChanges?: boolean;
    /** 是否使用高级分析 */
    useAdvancedAnalysis?: boolean;
    /** 是否使用上下文 */
    useContext?: boolean;
    /** 上下文文本 */
    contextText?: string;
    /** 上下文窗口大小 */
    contextWindowSize?: number;
    /** 语言 */
    language?: string;
}
/**
 * BERT情感分析模型
 */
export declare class BERTEmotionModel {
    /** 配置 */
    private config;
    /** 是否已初始化 */
    private initialized;
    /** 是否正在初始化 */
    private initializing;
    /** 模型实例 */
    private model;
    /** 词汇表（为将来的实现保留） */
    private vocab;
    /** 结果缓存 */
    private cache;
    /** 是否启用调试 */
    private debug;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: BERTModelConfig);
    /**
     * 初始化
     * @returns 是否成功初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 加载模型
     */
    private loadModel;
    /**
     * 加载词汇表
     */
    private loadVocab;
    /**
     * 分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    analyzeEmotion(text: string, options?: Partial<Omit<EmotionAnalysisRequest, 'text'>>): Promise<EmotionAnalysisResult>;
    /**
     * 使用本地模型分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    private analyzeEmotionWithLocalModel;
    /**
     * 使用远程API分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    private analyzeEmotionWithRemoteAPI;
    /**
     * 处理情感预测结果
     * @param prediction 预测结果
     * @param options 选项
     * @returns 情感分析结果
     */
    private processEmotionPrediction;
    /**
     * 生成情感变化点
     * @param primaryEmotion 主要情感
     * @param secondaryEmotion 次要情感
     * @param scores 情感分数
     * @returns 情感变化点数组
     */
    private generateEmotionChanges;
    /**
     * 生成详细情感数据
     * @param scores 情感分数
     * @returns 详细情感数据
     */
    private generateDetailedEmotions;
    /**
     * 计算总体情感强度
     * @param scores 情感分数
     * @returns 总体强度
     */
    private calculateOverallIntensity;
    /**
     * 计算情感强度变化
     * @param scores 情感分数
     * @returns 强度变化
     */
    private calculateIntensityVariation;
    /**
     * 计算情感复杂度
     * @param scores 情感分数
     * @returns 情感复杂度
     */
    private calculateEmotionalComplexity;
    /**
     * 计算情感稳定性
     * @param scores 情感分数
     * @returns 情感稳定性
     */
    private calculateEmotionalStability;
    /**
     * 计算置信度
     * @param scores 情感分数
     * @returns 置信度
     */
    private calculateConfidence;
    /**
     * 规范化文本
     * @param text 文本
     * @returns 规范化后的文本
     */
    private normalizeText;
    /**
     * 分词
     * @param text 文本
     * @returns 词元数组
     */
    private tokenize;
    /**
     * 模拟预测
     * @param input 输入
     * @returns 预测结果
     */
    private mockPredict;
}
