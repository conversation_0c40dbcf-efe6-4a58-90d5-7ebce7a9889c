/**
 * 消息数据传输对象
 */

// 消息类型枚举
export enum MessageType {
  JOIN = 'join',
  LEAVE = 'leave',
  USER_LIST = 'user_list',
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  USER_STATUS = 'user_status',
  OPERATION = 'operation',
  BATCH_OPERATION = 'batch_operation',
  OPERATION_HISTORY = 'operation_history',
  ERROR = 'error',
  HEARTBEAT = 'heartbeat',
  COMPRESSION_INFO = 'compression_info',
  INTEREST_AREA_UPDATE = 'interest_area_update',
  PARTITION_LIST = 'partition_list',
  PARTITION_CREATED = 'partition_created',
  PARTITION_UPDATED = 'partition_updated',
  PARTITION_DELETED = 'partition_deleted',
  RESOURCE_STATS = 'resource_stats',
}

// 操作类型枚举
export enum OperationType {
  ENTITY_CREATE = 'entity_create',
  ENTITY_UPDATE = 'entity_update',
  ENTITY_DELETE = 'entity_delete',
  COMPONENT_ADD = 'component_add',
  COMPONENT_UPDATE = 'component_update',
  COMPONENT_REMOVE = 'component_remove',
  SCENE_UPDATE = 'scene_update',
  CURSOR_MOVE = 'cursor_move',
  SELECTION_CHANGE = 'selection_change',
}

// 用户角色枚举
export enum CollaborationRole {
  VIEWER = 'viewer',
  EDITOR = 'editor',
  ADMIN = 'admin',
  OWNER = 'owner',
}

// 消息接口
export interface Message {
  type: MessageType;
  data: any;
}

// 加入消息数据
export interface JoinMessageData {
  userId: string;
  userName: string;
  projectId: string;
  sceneId: string;
}

// 用户状态数据
export interface UserStatusData {
  userId: string;
  isActive?: boolean;
  cursor?: {
    x: number;
    y: number;
    entityId?: string;
  };
}

// 操作数据
export interface Operation {
  id: string;
  type: OperationType;
  userId: string;
  timestamp: number;
  data: any;
}

/**
 * 3D位置接口
 */
export interface Position3D {
  x: number;
  y: number;
  z: number;
}

/**
 * 用户兴趣区域接口
 */
export interface UserInterestArea {
  /**
   * 中心位置
   */
  position: Position3D;

  /**
   * 半径
   */
  radius: number;

  /**
   * 最后更新时间
   */
  timestamp: number;

  /**
   * 优先级
   */
  priority?: number;

  /**
   * 元数据
   */
  metadata?: Record<string, any>;
}

/**
 * 分区数据接口
 */
export interface PartitionData {
  /**
   * 分区ID
   */
  id: string;

  /**
   * 分区名称
   */
  name: string;

  /**
   * 分区类型
   */
  type: 'area' | 'component' | 'custom';

  /**
   * 分区边界
   */
  bounds?: {
    minX: number;
    minY: number;
    minZ: number;
    maxX: number;
    maxY: number;
    maxZ: number;
  };

  /**
   * 用户数量
   */
  userCount: number;

  /**
   * 操作数量
   */
  operationCount: number;

  /**
   * 元数据
   */
  metadata?: Record<string, any>;

  /**
   * 创建时间
   */
  createdAt: number;

  /**
   * 最后活动时间
   */
  lastActivityAt: number;
}

/**
 * 资源统计数据接口
 */
export interface ResourceStatsData {
  /**
   * 内存使用（字节）
   */
  memoryUsage: number;

  /**
   * CPU使用（百分比）
   */
  cpuUsage: number;

  /**
   * 网络带宽使用（字节/秒）
   */
  networkUsage: number;

  /**
   * 操作频率（操作/秒）
   */
  operationRate: number;

  /**
   * 消息队列长度
   */
  messageQueueLength: number;

  /**
   * 最后更新时间
   */
  timestamp: number;
}

// 用户数据
export interface CollaborationUser {
  id: string;
  name: string;
  avatar?: string;
  role: CollaborationRole;
  color: string;
  isActive: boolean;
  lastActivity: number;
  cursor?: {
    x: number;
    y: number;
    entityId?: string;
  };
  interestArea?: UserInterestArea;
  partitions?: string[]; // 用户所在分区ID列表
  metadata?: Record<string, any>; // 用户元数据
}

// 批量操作数据
export interface BatchOperationData {
  operations: Operation[];
  batchId?: string;
  priority?: number;
  group?: string;
  metadata?: Record<string, any>;
}

// 压缩信息数据
export interface CompressionInfoData {
  supported: boolean;
  algorithms: string[];
  preferred: string;
  level?: number;
  threshold?: number;
}

// 兴趣区域更新数据
export interface InterestAreaUpdateData {
  userId: string;
  interestArea: UserInterestArea;
}

// 分区列表数据
export interface PartitionListData {
  partitions: PartitionData[];
}

// 分区创建/更新数据
export interface PartitionUpdateData {
  partition: PartitionData;
}

// 分区删除数据
export interface PartitionDeleteData {
  partitionId: string;
}
