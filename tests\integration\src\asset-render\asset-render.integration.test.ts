/**
 * 资产服务和渲染服务集成测试
 */
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import FormData from 'form-data';
import {
  TEST_CONFIG,
  createTestUser,
  loginTestUser,
  createTestProject,
  cleanupTestData,
} from '../setup';

describe('资产服务和渲染服务集成测试', () => {
  // 测试数据
  const testUser = {
    username: `test_user_${Date.now()}`,
    password: 'Test@123456',
    email: `test_user_${Date.now()}@example.com`,
  };
  
  const testProject = {
    name: `test_project_${Date.now()}`,
    description: '测试项目描述',
  };
  
  let authToken: string;
  let userId: string;
  let projectId: string;
  let assetId: string;
  
  // 在所有测试开始前创建测试用户和项目
  beforeAll(async () => {
    try {
      // 创建测试用户
      await createTestUser(
        testUser.username,
        testUser.password,
        testUser.email
      );
      
      // 登录获取令牌
      const loginData = await loginTestUser(testUser.username, testUser.password);
      authToken = loginData.accessToken;
      userId = loginData.user.id;
      
      // 创建测试项目
      const projectData = await createTestProject(
        authToken,
        testProject.name,
        testProject.description
      );
      projectId = projectData.id;
      
      expect(authToken).toBeDefined();
      expect(userId).toBeDefined();
      expect(projectId).toBeDefined();
    } catch (error) {
      console.error('测试准备失败:', error);
      throw error;
    }
  });
  
  // 在所有测试结束后清理测试数据
  afterAll(async () => {
    await cleanupTestData(authToken, projectId);
  });
  
  // 创建测试资源文件
  const createTestFile = (filename: string, content: string): string => {
    const testDir = path.join(__dirname, '../../temp');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    const filePath = path.join(testDir, filename);
    fs.writeFileSync(filePath, content);
    return filePath;
  };
  
  // 测试资产上传和管理
  describe('资产管理', () => {
    it('应该能够上传资产文件', async () => {
      // 创建测试文件
      const testFilePath = createTestFile('test-model.gltf', JSON.stringify({
        asset: {
          version: '2.0',
          generator: 'IR Engine Test',
        },
        scene: 0,
        scenes: [{ nodes: [0] }],
        nodes: [{ mesh: 0 }],
        meshes: [{ primitives: [{ attributes: { POSITION: 0 } }] }],
        accessors: [{ componentType: 5126, count: 3, type: 'VEC3' }],
      }));
      
      // 创建表单数据
      const formData = new FormData();
      formData.append('file', fs.createReadStream(testFilePath));
      formData.append('projectId', projectId);
      formData.append('type', 'model');
      
      const response = await axios.post(
        `${TEST_CONFIG.assetService.url}/assets/upload`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(response.status).toBe(201);
      expect(response.data.filename).toBeDefined();
      expect(response.data.url).toBeDefined();
      expect(response.data.projectId).toBe(projectId);
      expect(response.data.ownerId).toBe(userId);
      
      assetId = response.data.id;
      
      // 清理测试文件
      fs.unlinkSync(testFilePath);
    });
    
    it('应该能够获取项目的资产列表', async () => {
      const response = await axios.get(
        `${TEST_CONFIG.assetService.url}/assets`,
        {
          params: { projectId },
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      
      const uploadedAsset = response.data.find((a: any) => a.id === assetId);
      expect(uploadedAsset).toBeDefined();
      expect(uploadedAsset.projectId).toBe(projectId);
    });
    
    it('应该能够获取资产详情', async () => {
      const response = await axios.get(
        `${TEST_CONFIG.assetService.url}/assets/${assetId}`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(response.status).toBe(200);
      expect(response.data.id).toBe(assetId);
      expect(response.data.projectId).toBe(projectId);
      expect(response.data.ownerId).toBe(userId);
    });
    
    it('应该能够更新资产信息', async () => {
      const newName = '更新后的资产名称';
      
      const response = await axios.patch(
        `${TEST_CONFIG.assetService.url}/assets/${assetId}`,
        {
          name: newName,
        },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(response.status).toBe(200);
      expect(response.data.name).toBe(newName);
      
      // 验证更新是否持久化
      const getResponse = await axios.get(
        `${TEST_CONFIG.assetService.url}/assets/${assetId}`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(getResponse.data.name).toBe(newName);
    });
  });
  
  // 测试资产处理和渲染
  describe('资产处理和渲染', () => {
    it('应该能够处理上传的资产', async () => {
      // 触发资产处理
      const response = await axios.post(
        `${TEST_CONFIG.assetService.url}/assets/${assetId}/process`,
        {},
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(response.status).toBe(202);
      expect(response.data.message).toContain('处理中');
      
      // 等待处理完成（在实际测试中可能需要轮询或使用WebSocket）
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // 检查处理状态
      const statusResponse = await axios.get(
        `${TEST_CONFIG.assetService.url}/assets/${assetId}/status`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(statusResponse.status).toBe(200);
      expect(['processed', 'processing']).toContain(statusResponse.data.status);
    });
    
    it('应该能够创建渲染任务', async () => {
      // 创建场景（简化版，实际场景会更复杂）
      const sceneResponse = await axios.post(
        `${TEST_CONFIG.projectService.url}/projects/${projectId}/scenes`,
        {
          name: '测试场景',
          data: {
            entities: [
              {
                id: 'entity1',
                name: '测试实体',
                components: [
                  {
                    type: 'transform',
                    position: { x: 0, y: 0, z: 0 },
                    rotation: { x: 0, y: 0, z: 0 },
                    scale: { x: 1, y: 1, z: 1 },
                  },
                  {
                    type: 'model',
                    assetId: assetId,
                  },
                ],
              },
            ],
          },
        },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(sceneResponse.status).toBe(201);
      const sceneId = sceneResponse.data.id;
      
      // 创建渲染任务
      const renderResponse = await axios.post(
        `${TEST_CONFIG.assetService.url}/render`,
        {
          projectId,
          sceneId,
          settings: {
            width: 1280,
            height: 720,
            quality: 'medium',
            format: 'png',
          },
        },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(renderResponse.status).toBe(202);
      expect(renderResponse.data.id).toBeDefined();
      expect(renderResponse.data.status).toBe('pending');
      
      const renderId = renderResponse.data.id;
      
      // 等待渲染任务状态更新（在实际测试中可能需要轮询或使用WebSocket）
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // 检查渲染任务状态
      const statusResponse = await axios.get(
        `${TEST_CONFIG.assetService.url}/render/${renderId}`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(statusResponse.status).toBe(200);
      expect(['pending', 'processing', 'completed', 'failed']).toContain(statusResponse.data.status);
    });
  });
});
