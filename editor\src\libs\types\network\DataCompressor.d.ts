/**
 * 压缩算法
 */
export declare enum CompressionAlgorithm {
    /** 无压缩 */
    NONE = "none",
    /** LZ字符串压缩 */
    LZ_STRING = "lz_string",
    /** MessagePack */
    MSGPACK = "msgpack",
    /** CBOR (Concise Binary Object Representation) */
    CBOR = "cbor",
    /** Brotli压缩 */
    BROTLI = "brotli",
    /** Deflate压缩 */
    DEFLATE = "deflate",
    /** 增量压缩 */
    INCREMENTAL = "incremental",
    /** 自定义压缩 */
    CUSTOM = "custom"
}
/**
 * 压缩级别
 */
export declare enum CompressionLevel {
    /** 无压缩 */
    NONE = 0,
    /** 低压缩 */
    LOW = 1,
    /** 中等压缩 */
    MEDIUM = 2,
    /** 高压缩 */
    HIGH = 3,
    /** 最高压缩 */
    HIGHEST = 4
}
/**
 * 增量压缩选项
 */
export interface IncrementalCompressionOptions {
    /** 是否启用增量压缩 */
    enabled?: boolean;
    /** 最大递归深度 */
    maxDepth?: number;
    /** 是否包含路径信息 */
    includePathInfo?: boolean;
    /** 是否使用二进制差异 */
    useBinaryDiff?: boolean;
    /** 是否压缩增量数据 */
    compressIncrementalData?: boolean;
    /** 是否使用字段过滤 */
    useFieldFiltering?: boolean;
    /** 要包含的字段列表 */
    includedFields?: string[];
    /** 要排除的字段列表 */
    excludedFields?: string[];
}
/**
 * 压缩选项
 */
export interface CompressionOptions {
    /** 压缩算法 */
    algorithm?: CompressionAlgorithm;
    /** 压缩级别 */
    level?: CompressionLevel;
    /** 是否启用自适应压缩 */
    adaptive?: boolean;
    /** 最小压缩大小（字节） */
    minSize?: number;
    /** 是否使用二进制格式 */
    useBinaryFormat?: boolean;
    /** 是否使用类型化数组优化 */
    useTypedArrayOptimization?: boolean;
    /** 是否使用字典压缩 */
    useDictionaryCompression?: boolean;
    /** 压缩字典 */
    compressionDictionary?: Uint8Array;
    /** 增量压缩选项 */
    incremental?: IncrementalCompressionOptions;
    /** 自定义压缩函数 */
    customCompressFunction?: (data: any) => string | Uint8Array;
    /** 自定义解压缩函数 */
    customDecompressFunction?: (data: string | Uint8Array) => any;
}
/**
 * 增量压缩结果
 */
export interface IncrementalCompressionResult {
    /** 是否为增量数据 */
    isIncremental: boolean;
    /** 增量版本 */
    version: number;
    /** 是否为完整数据 */
    isComplete?: boolean;
    /** 是否为空增量 */
    isEmpty?: boolean;
    /** 变更路径 */
    paths?: string[];
    /** 变更字段数量 */
    changedFieldsCount?: number;
    /** 增量数据大小（字节） */
    incrementalSize?: number;
    /** 完整数据大小（字节） */
    fullSize?: number;
    /** 节省的字节数 */
    savedBytes?: number;
    /** 节省百分比 */
    savingsPercentage?: number;
}
/**
 * 压缩结果
 */
export interface CompressionResult {
    /** 压缩后的数据 */
    data: string | Uint8Array;
    /** 原始大小（字节） */
    originalSize: number;
    /** 压缩后大小（字节） */
    compressedSize: number;
    /** 压缩比率（0-1，越小表示压缩效果越好） */
    ratio: number;
    /** 压缩算法 */
    algorithm: CompressionAlgorithm;
    /** 压缩级别 */
    level: CompressionLevel;
    /** 压缩时间（毫秒） */
    time: number;
    /** 是否使用了二进制格式 */
    isBinary?: boolean;
    /** 是否使用了字典压缩 */
    usedDictionary?: boolean;
    /** 增量压缩结果 */
    incremental?: IncrementalCompressionResult;
}
/**
 * 数据压缩器
 * 负责压缩和解压缩网络传输的数据
 */
export declare class DataCompressor {
    /** 配置 */
    private options;
    /** 压缩统计信息 */
    private stats;
    /** 压缩字典 */
    private compressionDictionary?;
    /** 上次压缩的数据缓存 */
    private lastCompressedDataCache;
    /** 是否已加载依赖 */
    private dependenciesLoaded;
    /** 压缩库引用 */
    private compressionLibs;
    /**
     * 创建数据压缩器
     * @param options 压缩选项
     */
    constructor(options?: CompressionOptions);
    /**
     * 加载依赖
     */
    private loadDependencies;
    /**
     * 创建增量数据
     * @param newData 新数据
     * @param oldData 旧数据
     * @returns 增量数据
     */
    createIncrementalData(newData: any, oldData: any): Promise<any>;
    /**
     * 应用增量数据
     * @param incrementalData 增量数据
     * @param currentData 当前数据
     * @returns 更新后的数据
     */
    applyIncrementalData(incrementalData: any, currentData: any): Promise<any>;
    /**
     * 创建深度增量变更
     * @param newData 新数据
     * @param oldData 旧数据
     * @param path 当前路径
     * @param depth 当前深度
     * @param maxDepth 最大深度
     * @returns 增量变更
     */
    private createDeepIncrementalChanges;
    /**
     * 应用深度增量变更
     * @param target 目标对象
     * @param changes 变更对象
     */
    private applyDeepIncrementalChanges;
    /**
     * 收集变更路径
     * @param changes 变更对象
     * @param basePath 基础路径
     * @returns 变更路径列表
     */
    private collectChangePaths;
    /**
     * 压缩增量数据
     * @param data 增量数据
     * @returns 压缩后的数据
     */
    private compressIncrementalData;
    /**
     * 解压缩增量数据
     * @param data 压缩后的增量数据
     * @returns 解压缩后的数据
     */
    private decompressIncrementalData;
    /**
     * 压缩数据
     * @param data 要压缩的数据
     * @param options 压缩选项（可选，覆盖默认选项）
     * @returns 压缩结果
     */
    compress(data: any, options?: Partial<CompressionOptions>): Promise<CompressionResult>;
    /**
     * 检查是否可以使用类型化数组优化
     * @param data 数据
     * @returns 是否可以使用类型化数组优化
     */
    private canUseTypedArray;
    /**
     * 将数据转换为类型化数组
     * @param data 数据
     * @returns 类型化数组
     */
    private convertToTypedArray;
    /**
     * 将数据转换为二进制
     * @param data 数据
     * @returns 二进制数据
     */
    private convertToBinary;
    /**
     * 解压缩数据
     * @param compressedData 压缩后的数据
     * @param algorithm 压缩算法
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    decompress(compressedData: string | Uint8Array, algorithm?: CompressionAlgorithm, level?: CompressionLevel): Promise<any>;
    /**
     * 使用Brotli解压缩
     * @param data 要解压缩的数据
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    private decompressWithBrotli;
    /**
     * 使用Deflate解压缩
     * @param data 要解压缩的数据
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    private decompressWithDeflate;
    /**
     * 使用字典解压缩
     * @param data 要解压缩的数据
     * @param dictionary 压缩字典
     * @returns 解压缩后的数据
     */
    private decompressWithDictionary;
    /**
     * 选择自适应压缩算法和级别
     * @param dataSize 数据大小
     * @param options 压缩选项
     */
    private selectAdaptiveCompression;
    /**
     * 使用CBOR压缩
     * @param data 要压缩的数据
     * @param level 压缩级别
     * @returns 压缩后的数据
     */
    private compressWithCBOR;
    /**
     * 使用CBOR解压缩
     * @param data 要解压缩的数据
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    private decompressWithCBOR;
    /**
     * 使用Brotli压缩
     * @param data 要压缩的数据
     * @param level 压缩级别
     * @returns 压缩后的数据
     */
    private compressWithBrotli;
    /**
     * 使用Deflate压缩
     * @param data 要压缩的数据
     * @param level 压缩级别
     * @returns 压缩后的数据
     */
    private compressWithDeflate;
    /**
     * 使用RLE压缩
     * @param data 要压缩的数据
     * @returns 压缩后的数据
     */
    private compressWithRLE;
    /**
     * 使用字典压缩
     * @param data 要压缩的数据
     * @param dictionary 压缩字典
     * @returns 压缩后的数据
     */
    private compressWithDictionary;
    /**
     * 使用LZ-String压缩
     * @param data 要压缩的数据
     * @param level 压缩级别
     * @returns 压缩后的数据
     */
    private compressWithLZString;
    /**
     * 使用LZ-String解压缩
     * @param data 要解压缩的数据
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    private decompressWithLZString;
    /**
     * 使用MessagePack压缩
     * @param data 要压缩的数据
     * @param level 压缩级别
     * @returns 压缩后的数据
     */
    private compressWithMessagePack;
    /**
     * 使用MessagePack解压缩
     * @param data 要解压缩的数据
     * @param level 压缩级别
     * @returns 解压缩后的数据
     */
    private decompressWithMessagePack;
    /**
     * 简单的RLE压缩
     * @param data 要压缩的数据
     * @returns 压缩后的数据
     */
    private simpleRLECompress;
    /**
     * 简单的RLE解压缩
     * @param data 要解压缩的数据
     * @returns 解压缩后的数据
     */
    private simpleRLEDecompress;
    /**
     * 获取压缩统计信息
     * @returns 统计信息
     */
    getStats(): any;
    /**
     * 重置统计信息
     */
    resetStats(): void;
    /**
     * 设置压缩选项
     * @param options 压缩选项
     */
    setOptions(options: Partial<CompressionOptions>): void;
    /**
     * 获取压缩选项
     * @returns 压缩选项
     */
    getOptions(): Required<CompressionOptions>;
}
