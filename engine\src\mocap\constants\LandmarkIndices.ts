/**
 * 关键点索引常量
 * 基于MediaPipe Pose模型的关键点索引
 */
export const LandmarkIndices = {
  /** 鼻子 */
  NOSE: 0,
  /** 左眼内侧 */
  LEFT_EYE_INNER: 1,
  /** 左眼 */
  LEFT_EYE: 2,
  /** 左眼外侧 */
  LEFT_EYE_OUTER: 3,
  /** 右眼内侧 */
  RIGHT_EYE_INNER: 4,
  /** 右眼 */
  RIGHT_EYE: 5,
  /** 右眼外侧 */
  RIGHT_EYE_OUTER: 6,
  /** 左耳 */
  LEFT_EAR: 7,
  /** 右耳 */
  RIGHT_EAR: 8,
  /** 嘴左侧 */
  MOUTH_LEFT: 9,
  /** 嘴右侧 */
  MOUTH_RIGHT: 10,
  /** 左肩 */
  LEFT_SHOULDER: 11,
  /** 右肩 */
  RIGHT_SHOULDER: 12,
  /** 左肘 */
  LEFT_ELBOW: 13,
  /** 右肘 */
  RIGHT_ELBOW: 14,
  /** 左手腕 */
  LEFT_WRIST: 15,
  /** 右手腕 */
  RIGHT_WRIST: 16,
  /** 左小指 */
  LEFT_PINKY: 17,
  /** 右小指 */
  RIGHT_PINKY: 18,
  /** 左食指 */
  LEFT_INDEX: 19,
  /** 右食指 */
  RIGHT_INDEX: 20,
  /** 左拇指 */
  LEFT_THUMB: 21,
  /** 右拇指 */
  RIGHT_THUMB: 22,
  /** 左髋 */
  LEFT_HIP: 23,
  /** 右髋 */
  RIGHT_HIP: 24,
  /** 左膝 */
  LEFT_KNEE: 25,
  /** 右膝 */
  RIGHT_KNEE: 26,
  /** 左踝 */
  LEFT_ANKLE: 27,
  /** 右踝 */
  RIGHT_ANKLE: 28,
  /** 左脚跟 */
  LEFT_HEEL: 29,
  /** 右脚跟 */
  RIGHT_HEEL: 30,
  /** 左脚尖 */
  LEFT_FOOT_INDEX: 31,
  /** 右脚尖 */
  RIGHT_FOOT_INDEX: 32
};

/**
 * 关键点连接
 * 定义了关键点之间的连接关系，用于可视化
 */
export const LandmarkConnections = [
  // 面部连接
  { start: LandmarkIndices.NOSE, end: LandmarkIndices.LEFT_EYE_INNER },
  { start: LandmarkIndices.LEFT_EYE_INNER, end: LandmarkIndices.LEFT_EYE },
  { start: LandmarkIndices.LEFT_EYE, end: LandmarkIndices.LEFT_EYE_OUTER },
  { start: LandmarkIndices.LEFT_EYE_OUTER, end: LandmarkIndices.LEFT_EAR },
  { start: LandmarkIndices.NOSE, end: LandmarkIndices.RIGHT_EYE_INNER },
  { start: LandmarkIndices.RIGHT_EYE_INNER, end: LandmarkIndices.RIGHT_EYE },
  { start: LandmarkIndices.RIGHT_EYE, end: LandmarkIndices.RIGHT_EYE_OUTER },
  { start: LandmarkIndices.RIGHT_EYE_OUTER, end: LandmarkIndices.RIGHT_EAR },
  { start: LandmarkIndices.MOUTH_LEFT, end: LandmarkIndices.MOUTH_RIGHT },
  
  // 上半身连接
  { start: LandmarkIndices.LEFT_SHOULDER, end: LandmarkIndices.RIGHT_SHOULDER },
  { start: LandmarkIndices.LEFT_SHOULDER, end: LandmarkIndices.LEFT_ELBOW },
  { start: LandmarkIndices.RIGHT_SHOULDER, end: LandmarkIndices.RIGHT_ELBOW },
  { start: LandmarkIndices.LEFT_ELBOW, end: LandmarkIndices.LEFT_WRIST },
  { start: LandmarkIndices.RIGHT_ELBOW, end: LandmarkIndices.RIGHT_WRIST },
  { start: LandmarkIndices.LEFT_WRIST, end: LandmarkIndices.LEFT_PINKY },
  { start: LandmarkIndices.LEFT_WRIST, end: LandmarkIndices.LEFT_INDEX },
  { start: LandmarkIndices.LEFT_WRIST, end: LandmarkIndices.LEFT_THUMB },
  { start: LandmarkIndices.RIGHT_WRIST, end: LandmarkIndices.RIGHT_PINKY },
  { start: LandmarkIndices.RIGHT_WRIST, end: LandmarkIndices.RIGHT_INDEX },
  { start: LandmarkIndices.RIGHT_WRIST, end: LandmarkIndices.RIGHT_THUMB },
  { start: LandmarkIndices.LEFT_PINKY, end: LandmarkIndices.LEFT_INDEX },
  { start: LandmarkIndices.RIGHT_PINKY, end: LandmarkIndices.RIGHT_INDEX },
  
  // 躯干连接
  { start: LandmarkIndices.LEFT_SHOULDER, end: LandmarkIndices.LEFT_HIP },
  { start: LandmarkIndices.RIGHT_SHOULDER, end: LandmarkIndices.RIGHT_HIP },
  { start: LandmarkIndices.LEFT_HIP, end: LandmarkIndices.RIGHT_HIP },
  
  // 下半身连接
  { start: LandmarkIndices.LEFT_HIP, end: LandmarkIndices.LEFT_KNEE },
  { start: LandmarkIndices.RIGHT_HIP, end: LandmarkIndices.RIGHT_KNEE },
  { start: LandmarkIndices.LEFT_KNEE, end: LandmarkIndices.LEFT_ANKLE },
  { start: LandmarkIndices.RIGHT_KNEE, end: LandmarkIndices.RIGHT_ANKLE },
  { start: LandmarkIndices.LEFT_ANKLE, end: LandmarkIndices.LEFT_HEEL },
  { start: LandmarkIndices.RIGHT_ANKLE, end: LandmarkIndices.RIGHT_HEEL },
  { start: LandmarkIndices.LEFT_HEEL, end: LandmarkIndices.LEFT_FOOT_INDEX },
  { start: LandmarkIndices.RIGHT_HEEL, end: LandmarkIndices.RIGHT_FOOT_INDEX },
  { start: LandmarkIndices.LEFT_ANKLE, end: LandmarkIndices.LEFT_FOOT_INDEX },
  { start: LandmarkIndices.RIGHT_ANKLE, end: LandmarkIndices.RIGHT_FOOT_INDEX }
];

/**
 * 关键点分组
 * 将关键点分为不同的组，用于处理不同部位
 */
export const LandmarkGroups = {
  /** 面部关键点 */
  FACE: [
    LandmarkIndices.NOSE,
    LandmarkIndices.LEFT_EYE_INNER,
    LandmarkIndices.LEFT_EYE,
    LandmarkIndices.LEFT_EYE_OUTER,
    LandmarkIndices.RIGHT_EYE_INNER,
    LandmarkIndices.RIGHT_EYE,
    LandmarkIndices.RIGHT_EYE_OUTER,
    LandmarkIndices.LEFT_EAR,
    LandmarkIndices.RIGHT_EAR,
    LandmarkIndices.MOUTH_LEFT,
    LandmarkIndices.MOUTH_RIGHT
  ],
  
  /** 上半身关键点 */
  UPPER_BODY: [
    LandmarkIndices.LEFT_SHOULDER,
    LandmarkIndices.RIGHT_SHOULDER,
    LandmarkIndices.LEFT_ELBOW,
    LandmarkIndices.RIGHT_ELBOW,
    LandmarkIndices.LEFT_WRIST,
    LandmarkIndices.RIGHT_WRIST,
    LandmarkIndices.LEFT_PINKY,
    LandmarkIndices.RIGHT_PINKY,
    LandmarkIndices.LEFT_INDEX,
    LandmarkIndices.RIGHT_INDEX,
    LandmarkIndices.LEFT_THUMB,
    LandmarkIndices.RIGHT_THUMB
  ],
  
  /** 躯干关键点 */
  TORSO: [
    LandmarkIndices.LEFT_SHOULDER,
    LandmarkIndices.RIGHT_SHOULDER,
    LandmarkIndices.LEFT_HIP,
    LandmarkIndices.RIGHT_HIP
  ],
  
  /** 下半身关键点 */
  LOWER_BODY: [
    LandmarkIndices.LEFT_HIP,
    LandmarkIndices.RIGHT_HIP,
    LandmarkIndices.LEFT_KNEE,
    LandmarkIndices.RIGHT_KNEE,
    LandmarkIndices.LEFT_ANKLE,
    LandmarkIndices.RIGHT_ANKLE,
    LandmarkIndices.LEFT_HEEL,
    LandmarkIndices.RIGHT_HEEL,
    LandmarkIndices.LEFT_FOOT_INDEX,
    LandmarkIndices.RIGHT_FOOT_INDEX
  ],
  
  /** 左手关键点 */
  LEFT_HAND: [
    LandmarkIndices.LEFT_WRIST,
    LandmarkIndices.LEFT_PINKY,
    LandmarkIndices.LEFT_INDEX,
    LandmarkIndices.LEFT_THUMB
  ],
  
  /** 右手关键点 */
  RIGHT_HAND: [
    LandmarkIndices.RIGHT_WRIST,
    LandmarkIndices.RIGHT_PINKY,
    LandmarkIndices.RIGHT_INDEX,
    LandmarkIndices.RIGHT_THUMB
  ]
};
