/**
 * 断点管理器
 * 负责管理视觉脚本断点
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Node } from '../nodes/Node';
import { Graph } from '../graph/Graph';

/**
 * 断点类型
 */
export enum BreakpointType {
  /** 普通断点 */
  NORMAL = 'normal',
  /** 条件断点 */
  CONDITIONAL = 'conditional',
  /** 日志断点 */
  LOG = 'log',
  /** 数据断点 */
  DATA = 'data',
  /** 异常断点 */
  EXCEPTION = 'exception',
  /** 计数断点 */
  COUNT = 'count'
}

/**
 * 断点信息
 */
export interface Breakpoint {
  /** 断点ID */
  id: string;
  /** 节点ID */
  nodeId: string;
  /** 图ID */
  graphId: string;
  /** 断点类型 */
  type: BreakpointType;
  /** 条件表达式（对于条件断点） */
  condition?: string;
  /** 日志消息（对于日志断点） */
  logMessage?: string;
  /** 变量名（对于数据断点） */
  variableName?: string;
  /** 变量值条件（对于数据断点） */
  variableCondition?: string;
  /** 异常类型（对于异常断点） */
  exceptionType?: string;
  /** 计数器（对于计数断点） */
  counter?: number;
  /** 计数阈值（对于计数断点） */
  countThreshold?: number;
  /** 标签 */
  tags?: string[];
  /** 描述 */
  description?: string;
  /** 创建时间 */
  createdAt?: number;
  /** 最后命中时间 */
  lastHitAt?: number;
  /** 命中次数 */
  hitCount?: number;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 断点命中信息
 */
export interface BreakpointHitInfo {
  /** 断点 */
  breakpoint: Breakpoint;
  /** 节点 */
  node: Node;
  /** 图 */
  graph: Graph;
}

/**
 * 断点管理器
 */
export class BreakpointManager extends EventEmitter {
  /** 断点映射 */
  private breakpoints: Map<string, Breakpoint> = new Map();

  /**
   * 创建断点管理器
   */
  constructor() {
    super();
  }

  /**
   * 添加断点
   * @param nodeId 节点ID
   * @param graphId 图ID
   * @param type 断点类型
   * @param options 断点选项
   * @returns 断点ID
   */
  public addBreakpoint(
    nodeId: string,
    graphId: string,
    type: BreakpointType = BreakpointType.NORMAL,
    options: {
      condition?: string;
      logMessage?: string;
      variableName?: string;
      variableCondition?: string;
      exceptionType?: string;
      countThreshold?: number;
      tags?: string[];
      description?: string;
      enabled?: boolean;
    } = {}
  ): string {
    const id = `${graphId}:${nodeId}`;

    const breakpoint: Breakpoint = {
      id,
      nodeId,
      graphId,
      type,
      condition: options.condition,
      logMessage: options.logMessage,
      variableName: options.variableName,
      variableCondition: options.variableCondition,
      exceptionType: options.exceptionType,
      counter: 0,
      countThreshold: options.countThreshold,
      tags: options.tags || [],
      description: options.description,
      createdAt: Date.now(),
      lastHitAt: undefined,
      hitCount: 0,
      enabled: options.enabled !== undefined ? options.enabled : true
    };

    this.breakpoints.set(id, breakpoint);

    this.emit('breakpointAdded', breakpoint);

    return id;
  }

  /**
   * 移除断点
   * @param id 断点ID
   * @returns 是否成功
   */
  public removeBreakpoint(id: string): boolean {
    const breakpoint = this.breakpoints.get(id);

    if (breakpoint) {
      this.breakpoints.delete(id);
      this.emit('breakpointRemoved', breakpoint);
      return true;
    }

    return false;
  }

  /**
   * 更新断点
   * @param id 断点ID
   * @param updates 更新内容
   * @returns 是否成功
   */
  public updateBreakpoint(
    id: string,
    updates: Partial<Omit<Breakpoint, 'id' | 'nodeId' | 'graphId'>>
  ): boolean {
    const breakpoint = this.breakpoints.get(id);

    if (breakpoint) {
      // 更新断点
      Object.assign(breakpoint, updates);

      this.emit('breakpointUpdated', breakpoint);
      return true;
    }

    return false;
  }

  /**
   * 启用断点
   * @param id 断点ID
   * @returns 是否成功
   */
  public enableBreakpoint(id: string): boolean {
    return this.updateBreakpoint(id, { enabled: true });
  }

  /**
   * 禁用断点
   * @param id 断点ID
   * @returns 是否成功
   */
  public disableBreakpoint(id: string): boolean {
    return this.updateBreakpoint(id, { enabled: false });
  }

  /**
   * 获取断点
   * @param id 断点ID
   * @returns 断点
   */
  public getBreakpoint(id: string): Breakpoint | undefined {
    return this.breakpoints.get(id);
  }

  /**
   * 获取节点断点
   * @param nodeId 节点ID
   * @param graphId 图ID
   * @returns 断点
   */
  public getNodeBreakpoint(nodeId: string, graphId: string): Breakpoint | undefined {
    const id = `${graphId}:${nodeId}`;
    return this.breakpoints.get(id);
  }

  /**
   * 获取所有断点
   * @returns 断点列表
   */
  public getAllBreakpoints(): Breakpoint[] {
    return Array.from(this.breakpoints.values());
  }

  /**
   * 获取图的所有断点
   * @param graphId 图ID
   * @returns 断点列表
   */
  public getGraphBreakpoints(graphId: string): Breakpoint[] {
    return this.getAllBreakpoints().filter(bp => bp.graphId === graphId);
  }

  /**
   * 清除所有断点
   */
  public clearAllBreakpoints(): void {
    const breakpoints = this.getAllBreakpoints();
    this.breakpoints.clear();

    for (const breakpoint of breakpoints) {
      this.emit('breakpointRemoved', breakpoint);
    }

    this.emit('allBreakpointsCleared');
  }

  /**
   * 清除图的所有断点
   * @param graphId 图ID
   */
  public clearGraphBreakpoints(graphId: string): void {
    const graphBreakpoints = this.getGraphBreakpoints(graphId);

    for (const breakpoint of graphBreakpoints) {
      this.breakpoints.delete(breakpoint.id);
      this.emit('breakpointRemoved', breakpoint);
    }

    this.emit('graphBreakpointsCleared', graphId);
  }

  /**
   * 检查节点是否有断点
   * @param nodeId 节点ID
   * @param graphId 图ID
   * @returns 是否有断点
   */
  public hasBreakpoint(nodeId: string, graphId: string): boolean {
    const id = `${graphId}:${nodeId}`;
    return this.breakpoints.has(id);
  }

  /**
   * 检查节点是否有启用的断点
   * @param nodeId 节点ID
   * @param graphId 图ID
   * @returns 是否有启用的断点
   */
  public hasEnabledBreakpoint(nodeId: string, graphId: string): boolean {
    const breakpoint = this.getNodeBreakpoint(nodeId, graphId);
    return breakpoint ? breakpoint.enabled : false;
  }

  /**
   * 检查断点是否应该触发
   * @param nodeId 节点ID
   * @param graphId 图ID
   * @param node 节点
   * @param graph 图
   * @param error 错误对象（用于异常断点）
   * @returns 是否应该触发
   */
  public shouldBreak(nodeId: string, graphId: string, node: Node, graph: Graph, error?: Error): boolean {
    const breakpoint = this.getNodeBreakpoint(nodeId, graphId);

    if (!breakpoint || !breakpoint.enabled) {
      return false;
    }

    // 更新命中统计
    breakpoint.hitCount = (breakpoint.hitCount || 0) + 1;
    breakpoint.lastHitAt = Date.now();

    // 检查断点类型
    switch (breakpoint.type) {
      case BreakpointType.NORMAL:
        return true;

      case BreakpointType.CONDITIONAL:
        if (breakpoint.condition) {
          try {
            // 使用Function构造函数创建一个函数来评估条件
            // 这个函数可以访问图中的变量
            const conditionFunc = new Function('graph', 'node', `with (graph.getVariables()) { return ${breakpoint.condition}; }`);
            return conditionFunc(graph, node);
          } catch (error) {
            console.error(`条件断点表达式错误: ${error}`);
            return false;
          }
        }
        return false;

      case BreakpointType.LOG:
        if (breakpoint.logMessage) {
          try {
            // 使用Function构造函数创建一个函数来评估日志消息
            const messageFunc = new Function('graph', 'node', `with (graph.getVariables()) { return \`${breakpoint.logMessage}\`; }`);
            const message = messageFunc(graph, node);
            console.log(`[断点日志] ${message}`);
          } catch (error) {
            console.error(`日志断点表达式错误: ${error}`);
          }
        } else {
          console.log(`[断点日志] 节点执行: ${node.type} (${nodeId})`);
        }
        return false;

      case BreakpointType.DATA:
        if (breakpoint.variableName) {
          const variableValue = graph.getVariable(breakpoint.variableName);

          // 如果有条件表达式，检查条件
          if (breakpoint.variableCondition) {
            try {
              const conditionFunc = new Function('value', `return ${breakpoint.variableCondition};`);
              return conditionFunc(variableValue);
            } catch (error) {
              console.error(`数据断点条件表达式错误: ${error}`);
              return false;
            }
          } else {
            // 如果没有条件表达式，只要变量存在就触发
            return variableValue !== undefined;
          }
        }
        return false;

      case BreakpointType.EXCEPTION:
        if (error) {
          // 如果指定了异常类型，检查异常类型
          if (breakpoint.exceptionType) {
            return error.name === breakpoint.exceptionType;
          } else {
            // 如果没有指定异常类型，任何异常都触发
            return true;
          }
        }
        return false;

      case BreakpointType.COUNT:
        // 增加计数器
        breakpoint.counter = (breakpoint.counter || 0) + 1;

        // 检查是否达到阈值
        if (breakpoint.countThreshold && breakpoint.counter >= breakpoint.countThreshold) {
          // 重置计数器
          breakpoint.counter = 0;
          return true;
        }
        return false;

      default:
        return false;
    }
  }

  /**
   * 按标签查找断点
   * @param tag 标签
   * @returns 断点列表
   */
  public findBreakpointsByTag(tag: string): Breakpoint[] {
    return this.getAllBreakpoints().filter(bp => bp.tags && bp.tags.includes(tag));
  }

  /**
   * 导出断点
   * @returns 断点数据
   */
  public exportBreakpoints(): string {
    return JSON.stringify(this.getAllBreakpoints());
  }

  /**
   * 导入断点
   * @param data 断点数据
   * @returns 是否成功
   */
  public importBreakpoints(data: string): boolean {
    try {
      const breakpoints = JSON.parse(data) as Breakpoint[];

      // 清除现有断点
      this.clearAllBreakpoints();

      // 导入新断点
      for (const bp of breakpoints) {
        this.breakpoints.set(bp.id, bp);
        this.emit('breakpointAdded', bp);
      }

      return true;
    } catch (error) {
      console.error('导入断点失败:', error);
      return false;
    }
  }
}
