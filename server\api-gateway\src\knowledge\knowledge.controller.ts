import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  FileInterceptor,
  FilesInterceptor,
} from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { KnowledgeService } from './knowledge.service';

@ApiTags('知识库管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('knowledge-bases')
export class KnowledgeController {
  constructor(private readonly knowledgeService: KnowledgeService) {}

  @Post()
  @ApiOperation({ summary: '创建知识库' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', description: '知识库名称' },
        description: { type: 'string', description: '知识库描述' },
        category: { type: 'string', description: '知识库分类' },
        language: { type: 'string', description: '语言', default: 'zh-CN' },
      },
      required: ['name'],
    },
  })
  @ApiResponse({
    status: 201,
    description: '知识库创建成功',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        description: { type: 'string' },
        category: { type: 'string' },
        language: { type: 'string' },
        createdAt: { type: 'string' },
      },
    },
  })
  async createKnowledgeBase(@Body() body: any): Promise<any> {
    try {
      return await this.knowledgeService.createKnowledgeBase(body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '创建知识库失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @ApiOperation({ summary: '获取知识库列表' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'category', required: false, description: '分类筛选' })
  @ApiQuery({ name: 'language', required: false, description: '语言筛选' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiResponse({
    status: 200,
    description: '知识库列表',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              description: { type: 'string' },
              category: { type: 'string' },
              language: { type: 'string' },
              documentCount: { type: 'number' },
              totalChunks: { type: 'number' },
              createdAt: { type: 'string' },
            },
          },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
      },
    },
  })
  async getKnowledgeBases(@Query() query: any): Promise<any> {
    try {
      return await this.knowledgeService.getKnowledgeBases(query);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取知识库列表失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取知识库详情' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiResponse({
    status: 200,
    description: '知识库详情',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        description: { type: 'string' },
        category: { type: 'string' },
        language: { type: 'string' },
        vectorIndexName: { type: 'string' },
        documentCount: { type: 'number' },
        totalChunks: { type: 'number' },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
        documents: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              filename: { type: 'string' },
              fileSize: { type: 'number' },
              processingStatus: { type: 'string' },
              uploadedAt: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async getKnowledgeBase(@Param('id') id: string): Promise<any> {
    try {
      return await this.knowledgeService.getKnowledgeBase(id);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取知识库详情失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id')
  @ApiOperation({ summary: '更新知识库' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        description: { type: 'string' },
        category: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '知识库更新成功',
  })
  async updateKnowledgeBase(
    @Param('id') id: string,
    @Body() body: any,
  ): Promise<any> {
    try {
      return await this.knowledgeService.updateKnowledgeBase(id, body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '更新知识库失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除知识库' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiResponse({
    status: 200,
    description: '知识库删除成功',
  })
  async deleteKnowledgeBase(@Param('id') id: string): Promise<any> {
    try {
      return await this.knowledgeService.deleteKnowledgeBase(id);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '删除知识库失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/documents')
  @ApiOperation({ summary: '上传文档到知识库' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: '文档文件',
        },
        title: { type: 'string', description: '文档标题' },
        category: { type: 'string', description: '文档分类' },
        tags: { type: 'string', description: '文档标签（逗号分隔）' },
        description: { type: 'string', description: '文档描述' },
      },
      required: ['file', 'title'],
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocument(
    @Param('id') knowledgeBaseId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
  ): Promise<any> {
    try {
      const formData = new FormData();
      formData.append('file', new Blob([file.buffer]), file.originalname);
      formData.append('title', body.title);
      formData.append('category', body.category || 'default');
      formData.append('tags', body.tags || '');
      formData.append('description', body.description || '');

      return await this.knowledgeService.uploadDocument(knowledgeBaseId, formData);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '文档上传失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/documents/batch')
  @ApiOperation({ summary: '批量上传文档' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FilesInterceptor('files', 10))
  async batchUploadDocuments(
    @Param('id') knowledgeBaseId: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body() body: any,
  ): Promise<any> {
    try {
      const formData = new FormData();
      
      files.forEach((file, index) => {
        formData.append('files', new Blob([file.buffer]), file.originalname);
      });

      if (body.metadata) {
        formData.append('metadata', body.metadata);
      }

      return await this.knowledgeService.batchUploadDocuments(knowledgeBaseId, formData);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '批量上传失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/documents')
  @ApiOperation({ summary: '获取知识库文档列表' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'status', required: false, description: '处理状态筛选' })
  async getDocuments(
    @Param('id') knowledgeBaseId: string,
    @Query() query: any,
  ): Promise<any> {
    try {
      return await this.knowledgeService.getDocuments(knowledgeBaseId, query);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取文档列表失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id/documents/:documentId')
  @ApiOperation({ summary: '删除文档' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiParam({ name: 'documentId', description: '文档ID' })
  async deleteDocument(
    @Param('id') knowledgeBaseId: string,
    @Param('documentId') documentId: string,
  ): Promise<any> {
    try {
      return await this.knowledgeService.deleteDocument(knowledgeBaseId, documentId);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '删除文档失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/stats')
  @ApiOperation({ summary: '获取知识库统计信息' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  async getKnowledgeBaseStats(@Param('id') id: string): Promise<any> {
    try {
      return await this.knowledgeService.getKnowledgeBaseStats(id);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取统计信息失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/search')
  @ApiOperation({ summary: '搜索知识库内容' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        query: { type: 'string', description: '搜索查询' },
        maxResults: { type: 'number', description: '最大结果数量' },
        threshold: { type: 'number', description: '相似度阈值' },
      },
      required: ['query'],
    },
  })
  async searchKnowledgeBase(
    @Param('id') id: string,
    @Body() body: any,
  ): Promise<any> {
    try {
      return await this.knowledgeService.searchKnowledgeBase(id, body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '搜索失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
