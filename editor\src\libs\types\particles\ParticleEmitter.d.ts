/**
 * 粒子发射器
 * 用于控制粒子的生成和行为
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
import type { Entity } from '../core/Entity';
import { Particle } from './Particle';
import { ParticleSystem } from './ParticleSystem';
/**
 * 粒子发射器形状类型
 */
export declare enum EmitterShapeType {
    /** 点发射器 */
    POINT = "point",
    /** 球形发射器 */
    SPHERE = "sphere",
    /** 圆形发射器 */
    CIRCLE = "circle",
    /** 矩形发射器 */
    RECTANGLE = "rectangle",
    /** 圆锥发射器 */
    CONE = "cone",
    /** 圆环发射器 */
    TORUS = "torus",
    /** 网格发射器 */
    MESH = "mesh"
}
/**
 * 粒子发射器选项
 */
export interface ParticleEmitterOptions {
    /** 发射器名称 */
    name?: string;
    /** 发射器形状类型 */
    shapeType?: EmitterShapeType;
    /** 发射器形状参数 */
    shapeParams?: any;
    /** 粒子纹理 */
    texture?: THREE.Texture | string;
    /** 粒子材质 */
    material?: THREE.Material;
    /** 粒子大小 */
    particleSize?: number | [number, number];
    /** 粒子颜色 */
    particleColor?: THREE.Color | [THREE.Color, THREE.Color];
    /** 粒子透明度 */
    particleOpacity?: number | [number, number];
    /** 粒子生命周期（秒） */
    particleLifetime?: number | [number, number];
    /** 粒子速度 */
    particleVelocity?: number | [number, number];
    /** 粒子加速度 */
    particleAcceleration?: THREE.Vector3;
    /** 粒子旋转速度 */
    particleRotationSpeed?: number | [number, number];
    /** 粒子缩放速度 */
    particleScaleSpeed?: THREE.Vector2 | [THREE.Vector2, THREE.Vector2];
    /** 粒子发射率（每秒） */
    emissionRate?: number;
    /** 粒子发射角度 */
    emissionAngle?: number | [number, number];
    /** 粒子发射力度 */
    emissionForce?: number | [number, number];
    /** 粒子发射方向 */
    emissionDirection?: THREE.Vector3;
    /** 粒子发射扩散度 */
    emissionSpread?: number;
    /** 粒子重力 */
    gravity?: THREE.Vector3;
    /** 粒子阻力 */
    drag?: number;
    /** 是否启用碰撞 */
    enableCollision?: boolean;
    /** 是否启用粒子排序 */
    enableSorting?: boolean;
    /** 是否自动开始发射 */
    autoStart?: boolean;
    /** 发射持续时间（秒），0表示无限 */
    duration?: number;
    /** 最大粒子数量 */
    maxParticles?: number;
    /** 是否循环发射 */
    loop?: boolean;
    /** 爆发模式参数 */
    burst?: {
        /** 爆发数量 */
        count: number;
        /** 爆发间隔（秒） */
        interval: number;
        /** 爆发次数，0表示无限 */
        cycles: number;
    };
}
/**
 * 粒子发射器组件
 */
export declare class ParticleEmitter extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 发射器名称 */
    name: string;
    /** 发射器形状类型 */
    shapeType: EmitterShapeType;
    /** 发射器形状参数 */
    shapeParams: any;
    /** 粒子纹理 */
    texture: THREE.Texture | null;
    /** 粒子材质 */
    material: THREE.Material | null;
    /** 粒子大小范围 */
    particleSize: [number, number];
    /** 粒子颜色范围 */
    particleColor: [THREE.Color, THREE.Color];
    /** 粒子透明度范围 */
    particleOpacity: [number, number];
    /** 粒子生命周期范围（秒） */
    particleLifetime: [number, number];
    /** 粒子速度范围 */
    particleVelocity: [number, number];
    /** 粒子加速度 */
    particleAcceleration: THREE.Vector3;
    /** 粒子旋转速度范围 */
    particleRotationSpeed: [number, number];
    /** 粒子缩放速度范围 */
    particleScaleSpeed: [THREE.Vector2, THREE.Vector2];
    /** 粒子发射率（每秒） */
    emissionRate: number;
    /** 粒子发射角度范围 */
    emissionAngle: [number, number];
    /** 粒子发射力度范围 */
    emissionForce: [number, number];
    /** 粒子发射方向 */
    emissionDirection: THREE.Vector3;
    /** 粒子发射扩散度 */
    emissionSpread: number;
    /** 粒子重力 */
    gravity: THREE.Vector3;
    /** 粒子阻力 */
    drag: number;
    /** 是否启用碰撞 */
    enableCollision: boolean;
    /** 是否启用粒子排序 */
    enableSorting: boolean;
    /** 是否自动开始发射 */
    autoStart: boolean;
    /** 发射持续时间（秒），0表示无限 */
    duration: number;
    /** 最大粒子数量 */
    maxParticles: number;
    /** 是否循环发射 */
    loop: boolean;
    /** 爆发模式参数 */
    burst: {
        /** 爆发数量 */
        count: number;
        /** 爆发间隔（秒） */
        interval: number;
        /** 爆发次数，0表示无限 */
        cycles: number;
        /** 当前周期 */
        currentCycle: number;
        /** 上次爆发时间 */
        lastTime: number;
    } | null;
    /** 是否活跃 */
    private active;
    /** 已发射时间 */
    private elapsedTime;
    /** 上次发射时间 */
    private lastEmitTime;
    /** 活跃粒子列表 */
    private particles;
    /** 粒子系统引用 */
    private particleSystem;
    /** 所属实体 */
    protected entity: Entity | null;
    /** 世界变换矩阵 */
    private worldMatrix;
    /** 临时四元数 */
    private tempQuaternion;
    /**
     * 创建粒子发射器
     * @param options 发射器选项
     */
    constructor(options?: ParticleEmitterOptions);
    /**
     * 初始化发射器
     * @param particleSystem 粒子系统
     */
    initialize(particleSystem: ParticleSystem): void;
    /**
     * 设置所属实体
     * @param entity 实体
     */
    setEntity(entity: Entity): void;
    /**
     * 获取所属实体
     * @returns 实体
     */
    getEntity(): Entity | null;
    /**
     * 设置粒子大小
     * @param size 粒子大小或范围
     */
    setParticleSize(size: number | [number, number]): void;
    /**
     * 设置粒子颜色
     * @param color 粒子颜色或范围
     */
    setParticleColor(color: THREE.Color | [THREE.Color, THREE.Color]): void;
    /**
     * 设置粒子透明度
     * @param opacity 粒子透明度或范围
     */
    setParticleOpacity(opacity: number | [number, number]): void;
    /**
     * 设置粒子生命周期
     * @param lifetime 粒子生命周期或范围（秒）
     */
    setParticleLifetime(lifetime: number | [number, number]): void;
    /**
     * 设置粒子速度
     * @param velocity 粒子速度或范围
     */
    setParticleVelocity(velocity: number | [number, number]): void;
    /**
     * 设置粒子旋转速度
     * @param rotationSpeed 粒子旋转速度或范围
     */
    setParticleRotationSpeed(rotationSpeed: number | [number, number]): void;
    /**
     * 设置粒子缩放速度
     * @param scaleSpeed 粒子缩放速度或范围
     */
    setParticleScaleSpeed(scaleSpeed: THREE.Vector2 | [THREE.Vector2, THREE.Vector2]): void;
    /**
     * 设置发射角度
     * @param angle 发射角度或范围
     */
    setEmissionAngle(angle: number | [number, number]): void;
    /**
     * 设置发射力度
     * @param force 发射力度或范围
     */
    setEmissionForce(force: number | [number, number]): void;
    /**
     * 开始发射粒子
     */
    start(): void;
    /**
     * 停止发射粒子
     */
    stop(): void;
    /**
     * 清除所有粒子
     */
    clearParticles(): void;
    /**
     * 重置发射器
     */
    reset(): void;
    /**
     * 更新发射器
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新世界矩阵
     */
    private updateWorldMatrix;
    /**
     * 发射单个粒子
     */
    private emitParticle;
    /**
     * 初始化粒子属性
     * @param particle 粒子
     */
    private initializeParticle;
    /**
     * 设置粒子初始位置
     * @param particle 粒子
     */
    private setParticlePosition;
    /**
     * 设置粒子初始速度向量
     * @param particle 粒子
     */
    private setParticleVelocityVector;
    /**
     * 更新所有活跃粒子
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateParticles;
    /**
     * 对粒子进行排序
     * @param cameraPosition 相机位置
     */
    sortParticles(cameraPosition: THREE.Vector3): void;
    /**
     * 获取活跃粒子数量
     * @returns 活跃粒子数量
     */
    getActiveParticleCount(): number;
    /**
     * 获取所有活跃粒子
     * @returns 活跃粒子数组
     */
    getParticles(): Particle[];
    /**
     * 检查发射器是否活跃
     * @returns 是否活跃
     */
    isActive(): boolean;
    /**
     * 生成指定范围内的随机数
     * @param min 最小值
     * @param max 最大值
     * @returns 随机数
     */
    private randomRange;
}
