/**
 * 场景管理器
 * 负责场景的加载、卸载和切换
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Scene } from './Scene';
import type { World } from '../core/World';
import { AssetManager } from '../assets/AssetManager';
import { ScenePreloader, ScenePreloadProgressInfo } from './ScenePreloader';

/**
 * 场景过渡类型
 */
export enum SceneTransitionType {
  /** 无过渡 */
  NONE = 'none',
  /** 淡入淡出 */
  FADE = 'fade',
  /** 交叉淡入淡出 */
  CROSS_FADE = 'crossFade',
  /** 滑动 */
  SLIDE = 'slide',
  /** 缩放 */
  ZOOM = 'zoom',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 场景过渡选项
 */
export interface SceneTransitionOptions {
  /** 过渡类型 */
  type: SceneTransitionType;
  /** 过渡持续时间（毫秒） */
  duration?: number;
  /** 过渡方向（对于滑动过渡） */
  direction?: 'left' | 'right' | 'up' | 'down';
  /** 过渡缓动函数 */
  easing?: string;
  /** 自定义过渡函数 */
  customTransition?: (
    fromScene: Scene | null,
    toScene: Scene,
    progress: number,
    onComplete: () => void
  ) => void;
}

/**
 * 场景加载选项
 */
export interface SceneLoadOptions {
  /** 是否设置为活跃场景 */
  setActive?: boolean;
  /** 是否显示加载界面 */
  showLoadingScreen?: boolean;
  /** 是否预加载资源 */
  preloadAssets?: boolean;
  /** 是否初始化场景图 */
  initializeSceneGraph?: boolean;
  /** 过渡选项 */
  transition?: SceneTransitionOptions;
  /** 加载完成回调 */
  onLoaded?: (scene: Scene) => void;
  /** 加载进度回调 */
  onProgress?: (progress: number) => void;
  /** 加载错误回调 */
  onError?: (error: Error) => void;
}

/**
 * 场景管理器选项
 */
export interface SceneManagerOptions {
  /** 世界实例 */
  world: World;
  /** 资产管理器 */
  assetManager?: AssetManager;
  /** 默认过渡选项 */
  defaultTransition?: SceneTransitionOptions;
  /** 是否启用场景缓存 */
  enableSceneCache?: boolean;
  /** 最大场景缓存数量 */
  maxSceneCacheCount?: number;
}

/**
 * 场景管理器
 */
export class SceneManager extends EventEmitter {
  /** 世界实例 */
  private world: World;

  /** 资产管理器 */
  private assetManager: AssetManager | null = null;

  /** 场景预加载器 */
  private scenePreloader: ScenePreloader | null = null;

  /** 默认过渡选项 */
  private defaultTransition: SceneTransitionOptions;

  /** 是否启用场景缓存 */
  private enableSceneCache: boolean;

  /** 最大场景缓存数量 */
  private maxSceneCacheCount: number;

  /** 场景缓存 */
  private sceneCache: Map<string, Scene> = new Map();

  /** 场景访问时间 */
  private sceneAccessTimes: Map<string, number> = new Map();

  /** 当前场景 */
  private currentScene: Scene | null = null;

  /** 上一个场景 */
  private previousScene: Scene | null = null;

  /** 是否正在切换场景 */
  private isTransitioning: boolean = false;

  /** 是否正在加载场景 */
  private isLoading: boolean = false;

  /** 加载界面元素 */
  private loadingScreen: HTMLElement | null = null;

  /** 加载进度元素 */
  private loadingProgress: HTMLElement | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景管理器实例
   * @param options 场景管理器选项
   */
  constructor(options: SceneManagerOptions) {
    super();

    this.world = options.world;
    this.assetManager = options.assetManager || null;

    this.defaultTransition = options.defaultTransition || {
      type: SceneTransitionType.FADE,
      duration: 500
    };

    this.enableSceneCache = options.enableSceneCache !== undefined ? options.enableSceneCache : true;
    this.maxSceneCacheCount = options.maxSceneCacheCount || 5;

    // 如果有资产管理器，则创建场景预加载器
    if (this.assetManager) {
      this.scenePreloader = new ScenePreloader({
        assetManager: this.assetManager,
        autoAnalyzeResources: true,
        autoRegisterResources: true,
        debug: false
      });
    }

    // 创建加载界面
    this.createLoadingScreen();
  }

  /**
   * 初始化场景管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 获取当前场景
    this.currentScene = this.world.getActiveScene();

    // 监听世界场景变化事件
    this.world.on('sceneChanged', (newScene: Scene, oldScene: Scene) => {
      this.previousScene = oldScene;
      this.currentScene = newScene;

      // 更新场景访问时间
      if (newScene && newScene.id) {
        this.sceneAccessTimes.set(newScene.id, Date.now());
      }

      // 发出场景变化事件
      this.emit('sceneChanged', newScene, oldScene);
    });

    // 初始化场景预加载器
    if (this.scenePreloader) {
      this.scenePreloader.initialize();

      // 监听预加载器事件
      this.scenePreloader.on('loadProgress', (data) => {
        this.emit('resourceLoadProgress', data);
      });

      this.scenePreloader.on('loadComplete', (data) => {
        this.emit('resourceLoadComplete', data);
      });

      this.scenePreloader.on('loadError', (data) => {
        this.emit('resourceLoadError', data);
      });
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 创建加载界面
   */
  private createLoadingScreen(): void {
    // 创建加载界面容器
    this.loadingScreen = document.createElement('div');
    this.loadingScreen.style.position = 'fixed';
    this.loadingScreen.style.top = '0';
    this.loadingScreen.style.left = '0';
    this.loadingScreen.style.width = '100%';
    this.loadingScreen.style.height = '100%';
    this.loadingScreen.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    this.loadingScreen.style.display = 'flex';
    this.loadingScreen.style.flexDirection = 'column';
    this.loadingScreen.style.justifyContent = 'center';
    this.loadingScreen.style.alignItems = 'center';
    this.loadingScreen.style.zIndex = '9999';
    this.loadingScreen.style.opacity = '0';
    this.loadingScreen.style.transition = 'opacity 0.3s ease-in-out';
    this.loadingScreen.style.pointerEvents = 'none';

    // 创建加载文本
    const loadingText = document.createElement('div');
    loadingText.textContent = '加载中...';
    loadingText.style.color = 'white';
    loadingText.style.fontSize = '24px';
    loadingText.style.marginBottom = '20px';
    this.loadingScreen.appendChild(loadingText);

    // 创建加载进度条容器
    const progressContainer = document.createElement('div');
    progressContainer.style.width = '300px';
    progressContainer.style.height = '10px';
    progressContainer.style.backgroundColor = '#333';
    progressContainer.style.borderRadius = '5px';
    progressContainer.style.overflow = 'hidden';
    this.loadingScreen.appendChild(progressContainer);

    // 创建加载进度条
    this.loadingProgress = document.createElement('div');
    this.loadingProgress.style.width = '0%';
    this.loadingProgress.style.height = '100%';
    this.loadingProgress.style.backgroundColor = '#0095dd';
    this.loadingProgress.style.transition = 'width 0.3s ease-in-out';
    progressContainer.appendChild(this.loadingProgress);

    // 添加到文档
    document.body.appendChild(this.loadingScreen);
  }

  /**
   * 显示加载界面
   */
  private showLoadingScreen(): void {
    if (!this.loadingScreen) return;

    this.loadingScreen.style.opacity = '1';
    this.loadingScreen.style.pointerEvents = 'auto';

    // 重置进度条
    if (this.loadingProgress) {
      this.loadingProgress.style.width = '0%';
    }
  }

  /**
   * 隐藏加载界面
   */
  private hideLoadingScreen(): void {
    if (!this.loadingScreen) return;

    this.loadingScreen.style.opacity = '0';
    this.loadingScreen.style.pointerEvents = 'none';
  }

  /**
   * 更新加载进度
   * @param progress 进度（0-1）
   */
  private updateLoadingProgress(progress: number): void {
    if (!this.loadingProgress) return;

    this.loadingProgress.style.width = `${progress * 100}%`;
  }

  /**
   * 加载场景
   * @param sceneId 场景ID
   * @param options 加载选项
   * @returns Promise，解析为加载的场景
   */
  public async loadScene(sceneId: string, options: SceneLoadOptions = {}): Promise<Scene> {
    // 如果正在加载或切换场景，则抛出错误
    if (this.isLoading || this.isTransitioning) {
      throw new Error('已有场景正在加载或切换');
    }

    this.isLoading = true;

    // 合并选项
    const mergedOptions: SceneLoadOptions = {
      setActive: options.setActive !== undefined ? options.setActive : true,
      showLoadingScreen: options.showLoadingScreen !== undefined ? options.showLoadingScreen : true,
      preloadAssets: options.preloadAssets !== undefined ? options.preloadAssets : true,
      initializeSceneGraph: options.initializeSceneGraph !== undefined ? options.initializeSceneGraph : true,
      transition: options.transition || this.defaultTransition,
      onLoaded: options.onLoaded,
      onProgress: options.onProgress,
      onError: options.onError
    };

    try {
      // 显示加载界面
      if (mergedOptions.showLoadingScreen) {
        this.showLoadingScreen();
      }

      // 检查场景缓存
      if (this.enableSceneCache && this.sceneCache.has(sceneId)) {
        const cachedScene = this.sceneCache.get(sceneId)!;

        // 更新场景访问时间
        this.sceneAccessTimes.set(sceneId, Date.now());

        // 如果需要初始化场景图，则初始化
        if (mergedOptions.initializeSceneGraph) {
          const sceneGraph = cachedScene.getSceneGraph();
          if (sceneGraph) {
            sceneGraph.initialize();
          }
        }

        // 如果需要设置为活跃场景，则切换场景
        if (mergedOptions.setActive) {
          await this.transitionToScene(cachedScene, mergedOptions.transition);
        }

        // 调用加载完成回调
        if (mergedOptions.onLoaded) {
          mergedOptions.onLoaded(cachedScene);
        }

        // 隐藏加载界面
        if (mergedOptions.showLoadingScreen) {
          this.hideLoadingScreen();
        }

        this.isLoading = false;

        return cachedScene;
      }

      // 获取场景
      const scene = this.world.getScene(sceneId);

      if (!scene) {
        throw new Error(`找不到ID为 ${sceneId} 的场景`);
      }

      // 预加载场景资源
      if (mergedOptions.preloadAssets && this.scenePreloader) {
        try {
          // 使用场景预加载器预加载场景资源
          await this.scenePreloader.preloadScene(scene, (progressInfo: ScenePreloadProgressInfo) => {
            // 更新加载进度
            const progress = progressInfo.progress;

            if (mergedOptions.onProgress) {
              mergedOptions.onProgress(progress);
            }

            this.updateLoadingProgress(progress);
          });
        } catch (error) {
          console.error('预加载场景资源失败:', error);

          // 即使预加载失败，也继续加载场景
          // 但是可能会导致场景中的某些资源无法正常显示
        }
      }

      // 如果需要设置为活跃场景，则切换场景
      if (mergedOptions.setActive) {
        await this.transitionToScene(scene, mergedOptions.transition);
      }

      // 如果需要初始化场景图，则初始化
      if (mergedOptions.initializeSceneGraph) {
        const sceneGraph = scene.getSceneGraph();
        if (sceneGraph) {
          sceneGraph.initialize();
        }
      }

      // 如果启用场景缓存，则添加到缓存
      if (this.enableSceneCache) {
        this.addToSceneCache(scene);
      }

      // 调用加载完成回调
      if (mergedOptions.onLoaded) {
        mergedOptions.onLoaded(scene);
      }

      // 隐藏加载界面
      if (mergedOptions.showLoadingScreen) {
        this.hideLoadingScreen();
      }

      this.isLoading = false;

      return scene;
    } catch (error) {
      // 调用加载错误回调
      if (mergedOptions.onError) {
        mergedOptions.onError(error as Error);
      }

      // 隐藏加载界面
      if (mergedOptions.showLoadingScreen) {
        this.hideLoadingScreen();
      }

      this.isLoading = false;

      throw error;
    }
  }

  /**
   * 切换到场景
   * @param scene 目标场景
   * @param transitionOptions 过渡选项
   * @returns Promise，解析为切换后的场景
   */
  private async transitionToScene(
    scene: Scene,
    transitionOptions: SceneTransitionOptions = this.defaultTransition
  ): Promise<Scene> {
    // 如果正在切换场景，则抛出错误
    if (this.isTransitioning) {
      throw new Error('已有场景正在切换');
    }

    this.isTransitioning = true;

    // 获取当前场景
    const fromScene = this.currentScene;

    // 如果目标场景与当前场景相同，则不做任何操作
    if (fromScene === scene) {
      this.isTransitioning = false;
      return scene;
    }

    try {
      // 发出场景切换开始事件
      this.emit('sceneTransitionStart', fromScene, scene);

      // 执行场景过渡
      await this.executeTransition(fromScene, scene, transitionOptions);

      // 设置活跃场景
      this.world.setActiveScene(scene);

      // 发出场景切换完成事件
      this.emit('sceneTransitionComplete', scene, fromScene);

      this.isTransitioning = false;

      return scene;
    } catch (error) {
      this.isTransitioning = false;
      throw error;
    }
  }

  /**
   * 执行场景过渡
   * @param fromScene 源场景
   * @param toScene 目标场景
   * @param options 过渡选项
   * @returns Promise
   */
  private executeTransition(
    fromScene: Scene | null,
    toScene: Scene,
    options: SceneTransitionOptions
  ): Promise<void> {
    return new Promise<void>((resolve) => {
      const duration = options.duration || 500;

      // 根据过渡类型执行不同的过渡效果
      switch (options.type) {
        case SceneTransitionType.NONE:
          // 无过渡，直接完成
          resolve();
          break;

        case SceneTransitionType.FADE:
          // 淡入淡出过渡
          this.executeFadeTransition(duration, resolve);
          break;

        case SceneTransitionType.CROSS_FADE:
          // 交叉淡入淡出过渡
          this.executeCrossFadeTransition(fromScene, toScene, duration, resolve);
          break;

        case SceneTransitionType.SLIDE:
          // 滑动过渡
          this.executeSlideTransition(options.direction || 'right', duration, resolve);
          break;

        case SceneTransitionType.ZOOM:
          // 缩放过渡
          this.executeZoomTransition(duration, resolve);
          break;

        case SceneTransitionType.CUSTOM:
          // 自定义过渡
          if (options.customTransition) {
            let completed = false;

            const onComplete = () => {
              if (!completed) {
                completed = true;
                resolve();
              }
            };

            // 执行自定义过渡
            options.customTransition(fromScene, toScene, 0, onComplete);

            // 设置超时，确保过渡最终会完成
            setTimeout(() => {
              onComplete();
            }, duration + 100);
          } else {
            // 如果没有提供自定义过渡函数，则直接完成
            resolve();
          }
          break;

        default:
          // 默认无过渡
          resolve();
          break;
      }
    });
  }

  /**
   * 执行淡入淡出过渡
   * @param duration 持续时间
   * @param onComplete 完成回调
   */
  private executeFadeTransition(duration: number, onComplete: () => void): void {
    // 创建过渡遮罩
    const mask = document.createElement('div');
    mask.style.position = 'fixed';
    mask.style.top = '0';
    mask.style.left = '0';
    mask.style.width = '100%';
    mask.style.height = '100%';
    mask.style.backgroundColor = 'black';
    mask.style.opacity = '0';
    mask.style.transition = `opacity ${duration / 2}ms ease-in-out`;
    mask.style.zIndex = '9998';
    mask.style.pointerEvents = 'none';

    document.body.appendChild(mask);

    // 淡入
    setTimeout(() => {
      mask.style.opacity = '1';

      // 淡出
      setTimeout(() => {
        mask.style.opacity = '0';

        // 移除遮罩并完成过渡
        setTimeout(() => {
          document.body.removeChild(mask);
          onComplete();
        }, duration / 2);
      }, duration / 2);
    }, 0);
  }

  /**
   * 执行交叉淡入淡出过渡
   * @param fromScene 源场景
   * @param toScene 目标场景
   * @param duration 持续时间
   * @param onComplete 完成回调
   */
  private executeCrossFadeTransition(
    fromScene: Scene | null,
    toScene: Scene,
    duration: number,
    onComplete: () => void
  ): void {
    // 由于我们不能直接控制Three.js的渲染，这里只是模拟一个交叉淡入淡出效果
    // 在实际应用中，应该使用后处理效果或者渲染到纹理来实现

    // 创建过渡遮罩
    const mask = document.createElement('div');
    mask.style.position = 'fixed';
    mask.style.top = '0';
    mask.style.left = '0';
    mask.style.width = '100%';
    mask.style.height = '100%';
    mask.style.backgroundColor = 'black';
    mask.style.opacity = '0';
    mask.style.transition = `opacity ${duration}ms ease-in-out`;
    mask.style.zIndex = '9998';
    mask.style.pointerEvents = 'none';

    document.body.appendChild(mask);

    // 设置活跃场景
    this.world.setActiveScene(toScene);

    // 淡入淡出
    setTimeout(() => {
      mask.style.opacity = '0.5';

      setTimeout(() => {
        mask.style.opacity = '0';

        // 移除遮罩并完成过渡
        setTimeout(() => {
          document.body.removeChild(mask);
          onComplete();
        }, duration / 2);
      }, duration / 2);
    }, 0);
  }

  /**
   * 执行滑动过渡
   * @param direction 方向
   * @param duration 持续时间
   * @param onComplete 完成回调
   */
  private executeSlideTransition(
    direction: 'left' | 'right' | 'up' | 'down',
    duration: number,
    onComplete: () => void
  ): void {
    // 创建过渡遮罩
    const mask = document.createElement('div');
    mask.style.position = 'fixed';
    mask.style.top = '0';
    mask.style.left = '0';
    mask.style.width = '100%';
    mask.style.height = '100%';
    mask.style.backgroundColor = 'black';
    mask.style.transition = `transform ${duration}ms ease-in-out`;
    mask.style.zIndex = '9998';
    mask.style.pointerEvents = 'none';

    // 设置初始位置
    switch (direction) {
      case 'left':
        mask.style.transform = 'translateX(100%)';
        break;
      case 'right':
        mask.style.transform = 'translateX(-100%)';
        break;
      case 'up':
        mask.style.transform = 'translateY(100%)';
        break;
      case 'down':
        mask.style.transform = 'translateY(-100%)';
        break;
    }

    document.body.appendChild(mask);

    // 滑入
    setTimeout(() => {
      mask.style.transform = 'translate(0, 0)';

      // 滑出
      setTimeout(() => {
        switch (direction) {
          case 'left':
            mask.style.transform = 'translateX(-100%)';
            break;
          case 'right':
            mask.style.transform = 'translateX(100%)';
            break;
          case 'up':
            mask.style.transform = 'translateY(-100%)';
            break;
          case 'down':
            mask.style.transform = 'translateY(100%)';
            break;
        }

        // 移除遮罩并完成过渡
        setTimeout(() => {
          document.body.removeChild(mask);
          onComplete();
        }, duration);
      }, duration);
    }, 0);
  }

  /**
   * 执行缩放过渡
   * @param duration 持续时间
   * @param onComplete 完成回调
   */
  private executeZoomTransition(duration: number, onComplete: () => void): void {
    // 创建过渡遮罩
    const mask = document.createElement('div');
    mask.style.position = 'fixed';
    mask.style.top = '0';
    mask.style.left = '0';
    mask.style.width = '100%';
    mask.style.height = '100%';
    mask.style.backgroundColor = 'black';
    mask.style.opacity = '0';
    mask.style.transition = `all ${duration}ms ease-in-out`;
    mask.style.zIndex = '9998';
    mask.style.pointerEvents = 'none';
    mask.style.transform = 'scale(0)';

    document.body.appendChild(mask);

    // 缩放淡入
    setTimeout(() => {
      mask.style.opacity = '1';
      mask.style.transform = 'scale(1)';

      // 缩放淡出
      setTimeout(() => {
        mask.style.opacity = '0';
        mask.style.transform = 'scale(2)';

        // 移除遮罩并完成过渡
        setTimeout(() => {
          document.body.removeChild(mask);
          onComplete();
        }, duration);
      }, duration);
    }, 0);
  }

  /**
   * 添加场景到缓存
   * @param scene 场景实例
   */
  private addToSceneCache(scene: Scene): void {
    if (!this.enableSceneCache || !scene.id) return;

    // 如果已经在缓存中，则更新访问时间
    if (this.sceneCache.has(scene.id)) {
      this.sceneAccessTimes.set(scene.id, Date.now());
      return;
    }

    // 如果缓存已满，则移除最久未访问的场景
    if (this.sceneCache.size >= this.maxSceneCacheCount) {
      this.removeOldestSceneFromCache();
    }

    // 添加到缓存
    this.sceneCache.set(scene.id, scene);
    this.sceneAccessTimes.set(scene.id, Date.now());
  }

  /**
   * 从缓存中移除最久未访问的场景
   */
  private removeOldestSceneFromCache(): void {
    if (this.sceneCache.size === 0) return;

    let oldestSceneId: string | null = null;
    let oldestAccessTime = Infinity;

    // 查找最久未访问的场景
    for (const [sceneId, accessTime] of this.sceneAccessTimes.entries()) {
      // 跳过当前场景和上一个场景
      if (this.currentScene && this.currentScene.id === sceneId) continue;
      if (this.previousScene && this.previousScene.id === sceneId) continue;

      if (accessTime < oldestAccessTime) {
        oldestAccessTime = accessTime;
        oldestSceneId = sceneId;
      }
    }

    // 如果找到最久未访问的场景，则从缓存中移除
    if (oldestSceneId) {
      this.sceneCache.delete(oldestSceneId);
      this.sceneAccessTimes.delete(oldestSceneId);
    }
  }

  /**
   * 从缓存中移除场景
   * @param sceneId 场景ID
   * @returns 是否成功移除
   */
  public removeSceneFromCache(sceneId: string): boolean {
    if (!this.sceneCache.has(sceneId)) return false;

    // 从缓存中移除
    this.sceneCache.delete(sceneId);
    this.sceneAccessTimes.delete(sceneId);

    return true;
  }

  /**
   * 清空场景缓存
   */
  public clearSceneCache(): void {
    this.sceneCache.clear();
    this.sceneAccessTimes.clear();
  }

  /**
   * 获取当前场景
   * @returns 当前场景
   */
  public getCurrentScene(): Scene | null {
    return this.currentScene;
  }

  /**
   * 获取上一个场景
   * @returns 上一个场景
   */
  public getPreviousScene(): Scene | null {
    return this.previousScene;
  }

  /**
   * 是否正在加载场景
   * @returns 是否正在加载
   */
  public isSceneLoading(): boolean {
    return this.isLoading;
  }

  /**
   * 是否正在切换场景
   * @returns 是否正在切换
   */
  public isSceneTransitioning(): boolean {
    return this.isTransitioning;
  }

  /**
   * 销毁场景管理器
   */
  public dispose(): void {
    // 清空场景缓存
    this.clearSceneCache();

    // 销毁场景预加载器
    if (this.scenePreloader) {
      (this.scenePreloader as any).dispose();
      this.scenePreloader = null;
    }

    // 移除加载界面
    if (this.loadingScreen && this.loadingScreen.parentNode) {
      this.loadingScreen.parentNode.removeChild(this.loadingScreen);
    }

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
