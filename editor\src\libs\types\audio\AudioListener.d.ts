/**
 * 音频监听器
 * 用于处理3D音频的空间位置和方向
 */
import * as THREE from 'three';
/**
 * 音频监听器选项
 */
export interface AudioListenerOptions {
    /** 位置 */
    position?: THREE.Vector3;
    /** 前方向 */
    forward?: THREE.Vector3;
    /** 上方向 */
    up?: THREE.Vector3;
    /** 速度 */
    velocity?: THREE.Vector3;
}
/**
 * 扩展Web Audio API的AudioListener接口
 */
interface ExtendedAudioListener {
    setPosition(x: number, y: number, z: number): void;
    setOrientation(x: number, y: number, z: number, xUp: number, yUp: number, zUp: number): void;
    positionX?: AudioParam;
    positionY?: AudioParam;
    positionZ?: AudioParam;
    forwardX?: AudioParam;
    forwardY?: AudioParam;
    forwardZ?: AudioParam;
    upX?: AudioParam;
    upY?: AudioParam;
    upZ?: AudioParam;
    speedX?: AudioParam;
    speedY?: AudioParam;
    speedZ?: AudioParam;
}
/**
 * 音频监听器
 */
export declare class AudioListener {
    /** 音频上下文 */
    private context;
    /** 音频监听器节点 */
    private listener;
    /** 位置 */
    private position;
    /** 前方向 */
    private forward;
    /** 上方向 */
    private up;
    /** 速度 */
    private velocity;
    /** 上一次位置 */
    private previousPosition;
    /** 上一次更新时间 */
    private previousTime;
    /** 相机 */
    private camera;
    /** 是否已销毁 */
    private destroyed;
    /**
     * 创建音频监听器
     * @param context 音频上下文
     * @param options 音频监听器选项
     */
    constructor(context: AudioContext, options?: AudioListenerOptions);
    /**
     * 更新监听器
     */
    private updateListener;
    /**
     * 更新监听器
     */
    update(): void;
    /**
     * 从相机更新位置和方向
     */
    private updateFromCamera;
    /**
     * 设置位置
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     */
    setPosition(x: number, y: number, z: number): void;
    /**
     * 设置方向
     * @param forwardX 前方向X
     * @param forwardY 前方向Y
     * @param forwardZ 前方向Z
     * @param upX 上方向X
     * @param upY 上方向Y
     * @param upZ 上方向Z
     */
    setOrientation(forwardX: number, forwardY: number, forwardZ: number, upX: number, upY: number, upZ: number): void;
    /**
     * 设置速度
     * @param x X速度
     * @param y Y速度
     * @param z Z速度
     */
    setVelocity(x: number, y: number, z: number): void;
    /**
     * 设置相机
     * @param camera 相机
     */
    setCamera(camera: THREE.Camera): void;
    /**
     * 获取位置
     * @returns 位置
     */
    getPosition(): THREE.Vector3;
    /**
     * 获取前方向
     * @returns 前方向
     */
    getForward(): THREE.Vector3;
    /**
     * 获取上方向
     * @returns 上方向
     */
    getUp(): THREE.Vector3;
    /**
     * 获取速度
     * @returns 速度
     */
    getVelocity(): THREE.Vector3;
    /**
     * 获取相机
     * @returns 相机
     */
    getCamera(): THREE.Camera | null;
    /**
     * 获取音频上下文
     * @returns 音频上下文
     */
    getContext(): AudioContext;
    /**
     * 获取原生监听器
     * @returns 原生监听器
     */
    getNativeListener(): ExtendedAudioListener;
    /**
     * 销毁监听器
     */
    dispose(): void;
}
export {};
