/**
 * dl-engine 模拟实现
 * 用于测试环境
 */

// 模拟 EventEmitter 类
class MockEventEmitter {
  constructor() {
    this.events = {};
  }

  on(event, listener) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
  }

  off(event, listener) {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter(l => l !== listener);
    }
  }

  emit(event, ...args) {
    if (this.events[event]) {
      this.events[event].forEach(listener => listener(...args));
    }
  }

  addEventListener(event, listener) {
    this.on(event, listener);
  }

  removeEventListener(event, listener) {
    this.off(event, listener);
  }
}

// 模拟材质组件
class MockMaterial {
  constructor(config = {}) {
    this.config = config;
    this.properties = { ...config };
  }

  setProperty(key, value) {
    this.properties[key] = value;
  }

  getProperty(key) {
    return this.properties[key];
  }

  setTexture(type, texture) {
    this.properties[type] = texture;
  }
}

// 模拟网格组件
class MockMesh {
  constructor(config = {}) {
    this.config = config;
    this.components = {
      material: new MockMaterial(config.material)
    };
  }

  getComponent(type) {
    return this.components[type];
  }
}

// 模拟场景
class MockScene {
  constructor() {
    this.entities = [];
  }

  addEntity(entity) {
    this.entities.push(entity);
  }

  removeEntity(entity) {
    const index = this.entities.indexOf(entity);
    if (index > -1) {
      this.entities.splice(index, 1);
    }
  }

  removeAllEntities() {
    this.entities = [];
  }

  getEntities() {
    return this.entities;
  }
}

// 模拟纹理加载器
class MockTextureLoader {
  load(url) {
    return {
      url,
      image: new Image(),
      needsUpdate: true
    };
  }
}

// 模拟引擎类
class MockEngine {
  constructor(config = {}) {
    this.config = config;
    this.scene = new MockScene();
    this.textureLoader = new MockTextureLoader();
  }

  createScene() {
    this.scene = new MockScene();
    return this.scene;
  }

  getScene() {
    return this.scene;
  }

  createCamera(config) {
    return {
      type: config.type,
      position: config.position,
      lookAt: config.lookAt
    };
  }

  createLight(config) {
    return {
      type: config.type,
      intensity: config.intensity,
      position: config.position,
      color: config.color
    };
  }

  createGeometry(config) {
    return {
      type: config.type,
      ...config
    };
  }

  createMaterial(config) {
    return new MockMaterial(config);
  }

  createMesh(config) {
    const mesh = new MockMesh(config);
    this.scene.addEntity(mesh);
    return mesh;
  }

  getTextureLoader() {
    return this.textureLoader;
  }

  start() {
    // Mock start
  }

  dispose() {
    // Mock dispose
  }
}

// 模拟引擎模块
const mockEngine = {
  Engine: MockEngine,
  init: jest.fn().mockResolvedValue(true),
  createScene: jest.fn().mockReturnValue(new MockScene()),
  loadScene: jest.fn().mockResolvedValue(new MockScene()),
  saveScene: jest.fn().mockResolvedValue(true),
  addEntity: jest.fn().mockReturnValue({
    id: 'mock-entity',
    name: 'Mock Entity',
  }),
  removeEntity: jest.fn().mockReturnValue(true),
  updateEntity: jest.fn().mockReturnValue(true),
  render: jest.fn(),
  dispose: jest.fn(),
  EventEmitter: MockEventEmitter,
  Material: MockMaterial,
  Mesh: MockMesh,
  Scene: MockScene,
  TextureLoader: MockTextureLoader
};

module.exports = mockEngine;
