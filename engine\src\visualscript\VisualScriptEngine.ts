/**
 * 视觉脚本引擎
 * 负责执行视觉脚本
 */
import type { Entity } from '../core/Entity';
import type { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { GraphJSON } from './graph/GraphJSON';
import { NodeRegistry } from './nodes/NodeRegistry';
import { ValueTypeRegistry } from './values/ValueTypeRegistry';
import { Node } from './nodes/Node';
import { EventNode } from './nodes/EventNode';
import { Fiber } from './execution/Fiber';
import { ExecutionContext } from './execution/ExecutionContext';
import { Graph } from './graph/Graph';
import { Variable } from './values/Variable';
import { CustomEvent } from './events/CustomEvent';

/**
 * 视觉脚本引擎配置
 */
export interface VisualScriptEngineOptions {
  /** 视觉脚本JSON数据 */
  script: GraphJSON;
  /** 节点注册表 */
  nodeRegistry: NodeRegistry;
  /** 值类型注册表 */
  valueTypeRegistry: ValueTypeRegistry;
  /** 所属实体 */
  entity: Entity;
  /** 所属世界 */
  world: World;
}

/**
 * 视觉脚本引擎
 * 负责执行视觉脚本
 */
export class VisualScriptEngine extends EventEmitter {
  /** 视觉脚本JSON数据 */
  private script: GraphJSON;
  
  /** 节点注册表 */
  private nodeRegistry: NodeRegistry;
  
  /** 值类型注册表 */
  private valueTypeRegistry: ValueTypeRegistry;
  
  /** 所属实体 */
  private entity: Entity;
  
  /** 所属世界 */
  private world: World;
  
  /** 图形实例 */
  private graph: Graph;
  
  /** 节点实例映射 */
  private nodes: Map<string, Node> = new Map();
  
  /** 事件节点列表 */
  private eventNodes: EventNode[] = [];
  
  /** 执行上下文 */
  private executionContext: ExecutionContext;
  
  /** 执行纤程队列 */
  private fiberQueue: Fiber[] = [];
  
  /** 变量映射 */
  private variables: Map<string, Variable> = new Map();
  
  /** 自定义事件映射 */
  private customEvents: Map<string, CustomEvent> = new Map();
  
  /** 是否正在运行 */
  private running: boolean = false;
  
  /** 执行步数 */
  private executionSteps: number = 0;
  
  /** 最大执行步数限制 */
  private maxExecutionSteps: number = 1000000;
  
  /** 最大执行时间限制（毫秒） */
  private maxExecutionTime: number = 100;
  
  /**
   * 创建视觉脚本引擎
   * @param options 引擎选项
   */
  constructor(options: VisualScriptEngineOptions) {
    super();
    
    this.script = options.script;
    this.nodeRegistry = options.nodeRegistry;
    this.valueTypeRegistry = options.valueTypeRegistry;
    this.entity = options.entity;
    this.world = options.world;
    
    // 创建图形实例
    this.graph = new Graph({
      id: this.entity.id,
      name: this.entity.name
    });
    
    // 创建执行上下文
    this.executionContext = new ExecutionContext({
      engine: this,
      entity: this.entity,
      world: this.world
    });
    
    // 初始化引擎
    this.initialize();
  }
  
  /**
   * 初始化引擎
   */
  private initialize(): void {
    // 创建变量
    this.initializeVariables();
    
    // 创建自定义事件
    this.initializeCustomEvents();
    
    // 创建节点
    this.initializeNodes();
    
    // 连接节点
    this.connectNodes();
    
    // 初始化事件节点
    this.initializeEventNodes();
  }
  
  /**
   * 初始化变量
   */
  private initializeVariables(): void {
    // 清空变量映射
    this.variables.clear();
    
    // 创建变量
    if (this.script.variables) {
      for (const variableData of this.script.variables) {
        const variable = new Variable({
          id: variableData.id,
          name: variableData.name,
          type: variableData.type,
          value: variableData.value,
          description: variableData.description,
          constant: variableData.constant,
          global: variableData.global
        });
        
        this.variables.set(variable.id, variable);
        this.graph.addVariable(variable);
      }
    }
  }
  
  /**
   * 初始化自定义事件
   */
  private initializeCustomEvents(): void {
    // 清空自定义事件映射
    this.customEvents.clear();
    
    // 创建自定义事件
    if (this.script.customEvents) {
      for (const eventData of this.script.customEvents) {
        const customEvent = new CustomEvent({
          id: eventData.id,
          name: eventData.name,
          parameterTypes: eventData.parameterTypes,
          description: eventData.description
        });
        
        this.customEvents.set(customEvent.id, customEvent);
        this.graph.addCustomEvent(customEvent);
      }
    }
  }
  
  /**
   * 初始化节点
   */
  private initializeNodes(): void {
    // 清空节点映射和事件节点列表
    this.nodes.clear();
    this.eventNodes = [];
    
    // 创建节点
    if (this.script.nodes) {
      for (const nodeData of this.script.nodes) {
        // 获取节点类型
        const nodeType = this.nodeRegistry.getNodeType(nodeData.type);
        
        if (!nodeType) {
          console.warn(`未知节点类型: ${nodeData.type}`);
          continue;
        }
        
        // 创建节点实例
        const node = new nodeType({
          id: nodeData.id,
          type: nodeData.type,
          metadata: nodeData.metadata,
          graph: this.graph,
          context: this.executionContext
        });
        
        // 设置节点参数
        if (nodeData.parameters) {
          for (const [paramName, paramValue] of Object.entries(nodeData.parameters)) {
            if (paramValue.value !== undefined) {
              node.setParameterValue(paramName, paramValue.value);
            }
          }
        }
        
        // 添加到节点映射
        this.nodes.set(node.id, node);
        
        // 如果是事件节点，添加到事件节点列表
        if (node instanceof EventNode) {
          this.eventNodes.push(node);
        }
        
        // 添加到图形
        this.graph.addNode(node);
      }
    }
  }
  
  /**
   * 连接节点
   */
  private connectNodes(): void {
    // 连接节点参数
    if (this.script.nodes) {
      for (const nodeData of this.script.nodes) {
        const node = this.nodes.get(nodeData.id);
        
        if (!node) {
          continue;
        }
        
        // 连接参数链接
        if (nodeData.parameters) {
          for (const [paramName, paramValue] of Object.entries(nodeData.parameters)) {
            if (paramValue.link) {
              const sourceNode = this.nodes.get(paramValue.link.nodeId);
              
              if (sourceNode) {
                node.connectInput(paramName, sourceNode, paramValue.link.socket);
              }
            }
          }
        }
        
        // 连接流程
        if (nodeData.flows) {
          for (const [flowName, flowConnection] of Object.entries(nodeData.flows)) {
            const targetNode = this.nodes.get(flowConnection.nodeId);
            
            if (targetNode) {
              node.connectFlow(flowName, targetNode, flowConnection.socket);
            }
          }
        }
      }
    }
  }
  
  /**
   * 初始化事件节点
   */
  private initializeEventNodes(): void {
    // 初始化所有事件节点
    for (const eventNode of this.eventNodes) {
      eventNode.initialize();
    }
  }
  
  /**
   * 开始执行
   */
  public start(): void {
    if (this.running) {
      return;
    }
    
    this.running = true;
    
    // 触发开始事件
    this.emit('started');
    
    // 触发所有事件节点的开始事件
    for (const eventNode of this.eventNodes) {
      eventNode.onStart();
    }
  }
  
  /**
   * 停止执行
   */
  public stop(): void {
    if (!this.running) {
      return;
    }
    
    this.running = false;
    
    // 清空纤程队列
    this.fiberQueue = [];
    
    // 触发所有事件节点的停止事件
    for (const eventNode of this.eventNodes) {
      eventNode.onStop();
    }
    
    // 触发停止事件
    this.emit('stopped');
  }
  
  /**
   * 更新引擎
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.running) {
      return;
    }
    
    // 触发所有事件节点的更新事件
    for (const eventNode of this.eventNodes) {
      eventNode.onUpdate(deltaTime);
    }
    
    // 执行所有纤程
    this.executeAllFibers();
  }
  
  /**
   * 执行所有纤程
   */
  private executeAllFibers(): void {
    const startTime = performance.now();
    let executedSteps = 0;
    
    // 执行纤程队列中的所有纤程
    while (this.fiberQueue.length > 0 && 
           executedSteps < this.maxExecutionSteps && 
           performance.now() - startTime < this.maxExecutionTime) {
      
      // 获取第一个纤程
      const fiber = this.fiberQueue[0];
      
      // 执行一步
      const stepResult = fiber.executeStep();
      
      // 增加执行步数
      executedSteps++;
      this.executionSteps++;
      
      // 如果纤程已完成，从队列中移除
      if (stepResult.completed) {
        this.fiberQueue.shift();
      }
      
      // 如果需要暂停，跳出循环
      if (stepResult.pause) {
        break;
      }
    }
  }
  
  /**
   * 添加纤程到队列
   * @param fiber 纤程
   */
  public addFiber(fiber: Fiber): void {
    this.fiberQueue.push(fiber);
  }
  
  /**
   * 创建新纤程
   * @param sourceNode 源节点
   * @param outputName 输出名称
   * @param callback 完成回调
   */
  public createFiber(sourceNode: Node, outputName: string, callback?: () => void): Fiber {
    const fiber = new Fiber({
      engine: this,
      sourceNode: sourceNode,
      outputName: outputName,
      callback: callback
    });
    
    return fiber;
  }
  
  /**
   * 获取节点
   * @param id 节点ID
   * @returns 节点实例
   */
  public getNode(id: string): Node | undefined {
    return this.nodes.get(id);
  }
  
  /**
   * 获取变量
   * @param id 变量ID
   * @returns 变量实例
   */
  public getVariable(id: string): Variable | undefined {
    return this.variables.get(id);
  }
  
  /**
   * 获取自定义事件
   * @param id 事件ID
   * @returns 自定义事件实例
   */
  public getCustomEvent(id: string): CustomEvent | undefined {
    return this.customEvents.get(id);
  }
  
  /**
   * 获取执行上下文
   * @returns 执行上下文
   */
  public getExecutionContext(): ExecutionContext {
    return this.executionContext;
  }
  
  /**
   * 获取图形实例
   * @returns 图形实例
   */
  public getGraph(): Graph {
    return this.graph;
  }
  
  /**
   * 获取所属实体
   * @returns 所属实体
   */
  public getEntity(): Entity {
    return this.entity;
  }
  
  /**
   * 获取所属世界
   * @returns 所属世界
   */
  public getWorld(): World {
    return this.world;
  }
  
  /**
   * 获取是否正在运行
   * @returns 是否正在运行
   */
  public isRunning(): boolean {
    return this.running;
  }
  
  /**
   * 获取执行步数
   * @returns 执行步数
   */
  public getExecutionSteps(): number {
    return this.executionSteps;
  }
  
  /**
   * 销毁引擎
   */
  public dispose(): void {
    // 停止执行
    this.stop();
    
    // 清空节点映射
    this.nodes.clear();
    
    // 清空事件节点列表
    this.eventNodes = [];
    
    // 清空变量映射
    this.variables.clear();
    
    // 清空自定义事件映射
    this.customEvents.clear();
    
    // 清空纤程队列
    this.fiberQueue = [];
    
    // 清空图形
    this.graph.clear();
    
    // 移除所有事件监听
    this.removeAllListeners();
  }
}
