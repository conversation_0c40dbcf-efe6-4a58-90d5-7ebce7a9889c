/* 协作编辑示例项目样式 */

/* 基础样式 */
:root {
  --primary-color: #1890ff;
  --primary-color-dark: #096dd9;
  --secondary-color: #52c41a;
  --background-color: #f0f2f5;
  --panel-background: #ffffff;
  --text-color: #333333;
  --text-color-light: #666666;
  --border-color: #d9d9d9;
  --header-height: 60px;
  --footer-height: 30px;
  --panel-width: 300px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--background-color);
  overflow: hidden;
}

/* 按钮样式 */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: white;
  color: var(--text-color);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.button:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.button.primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.button.primary:hover {
  background-color: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

/* 应用容器 */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
}

/* 顶部导航栏 */
.app-header {
  height: var(--header-height);
  background-color: var(--panel-background);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 40px;
  margin-right: 10px;
}

.logo h1 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.user-info {
  display: flex;
  align-items: center;
}

.current-user {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.current-user img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

/* 主要内容区域 */
.app-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧面板 */
.left-panel {
  width: var(--panel-width);
  background-color: var(--panel-background);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 右侧面板 */
.right-panel {
  width: var(--panel-width);
  background-color: var(--panel-background);
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 面板通用样式 */
.panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-header h2 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 标签页样式 */
.panel-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  padding: 8px 16px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-color-light);
  border-bottom: 2px solid transparent;
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.tab-content.active {
  display: flex;
}

/* 视口容器 */
.viewport-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.viewport-header {
  padding: 8px 16px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.viewport-tools {
  display: flex;
  gap: 8px;
}

.tool-button {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.tool-button img {
  width: 20px;
  height: 20px;
}

.tool-button:hover {
  border-color: var(--primary-color);
}

.tool-button.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.tool-button.active img {
  filter: brightness(0) invert(1);
}

.viewport-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.action-button img {
  width: 20px;
  height: 20px;
}

.action-button:hover {
  border-color: var(--primary-color);
}

.viewport {
  flex: 1;
  position: relative;
  overflow: hidden;
}

#viewport-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.viewport-footer {
  padding: 4px 16px;
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-color-light);
}

/* 场景层级 */
.scene-hierarchy {
  font-size: 14px;
}

.hierarchy-item {
  padding: 4px 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.hierarchy-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.hierarchy-item.selected {
  background-color: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
}

.hierarchy-item .icon {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.hierarchy-item .user-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
}

/* 属性面板 */
.properties-panel {
  font-size: 14px;
}

.property-group {
  margin-bottom: 16px;
}

.property-group-title {
  font-weight: 600;
  margin-bottom: 8px;
}

.property-row {
  display: flex;
  margin-bottom: 8px;
}

.property-label {
  width: 80px;
  color: var(--text-color-light);
}

.property-value {
  flex: 1;
}

.property-input {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

.property-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.no-selection-message {
  color: var(--text-color-light);
  text-align: center;
  padding: 20px;
}

/* 用户列表 */
.users-list {
  margin-bottom: 20px;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
}

.user-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
}

.user-role {
  font-size: 12px;
  color: var(--text-color-light);
}

.user-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
}

.user-status.online {
  background-color: var(--secondary-color);
}

.user-status.offline {
  background-color: var(--text-color-light);
}

/* 聊天面板 */
.chat-messages {
  height: calc(100% - 40px);
  overflow-y: auto;
  margin-bottom: 10px;
}

.chat-message {
  margin-bottom: 12px;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.message-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
}

.message-sender {
  font-weight: 500;
  margin-right: 8px;
}

.message-time {
  font-size: 12px;
  color: var(--text-color-light);
}

.message-content {
  padding-left: 32px;
}

.chat-input {
  display: flex;
  height: 40px;
}

.chat-input input {
  flex: 1;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px 0 0 4px;
}

.chat-input button {
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

/* 历史面板 */
.history-list {
  height: calc(100% - 40px);
  overflow-y: auto;
  margin-bottom: 10px;
}

.history-item {
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
}

.history-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.history-item.current {
  background-color: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
}

.history-time {
  font-size: 12px;
  color: var(--text-color-light);
}

.history-user {
  font-weight: 500;
}

.history-action {
  margin-top: 4px;
}

.history-actions {
  display: flex;
  justify-content: space-between;
  height: 40px;
}

/* 底部状态栏 */
.app-footer {
  height: var(--footer-height);
  background-color: var(--panel-background);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  font-size: 12px;
  color: var(--text-color-light);
}

/* 对话框 */
.dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog.active {
  display: flex;
}

.dialog-content {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-header h2 {
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-color-light);
}

.dialog-body {
  padding: 16px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

/* 冲突对话框 */
.conflict-options {
  display: flex;
  gap: 16px;
  margin: 16px 0;
}

.conflict-option {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 16px;
}

.conflict-option h3 {
  font-size: 16px;
  margin-bottom: 8px;
}

.conflict-preview {
  height: 200px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  margin-bottom: 16px;
  overflow: auto;
  padding: 8px;
}

/* 用户光标指示器 */
.user-cursor {
  position: absolute;
  pointer-events: none;
  z-index: 100;
}

.cursor-pointer {
  width: 20px;
  height: 20px;
}

.cursor-label {
  background-color: var(--primary-color);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 4px;
  white-space: nowrap;
}
