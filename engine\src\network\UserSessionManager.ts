/**
 * 用户会话管理器
 * 负责管理用户会话和权限
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';

/**
 * 用户角色
 */
export enum UserRole {
  /** 访客 */
  GUEST = 'guest',
  /** 用户 */
  USER = 'user',
  /** 管理员 */
  ADMIN = 'admin',
  /** 超级管理员 */
  SUPER_ADMIN = 'super_admin',
}

/**
 * 用户权限
 */
export enum UserPermission {
  /** 查看 */
  VIEW = 'view',
  /** 编辑 */
  EDIT = 'edit',
  /** 创建 */
  CREATE = 'create',
  /** 删除 */
  DELETE = 'delete',
  /** 管理用户 */
  MANAGE_USERS = 'manage_users',
  /** 管理权限 */
  MANAGE_PERMISSIONS = 'manage_permissions',
  /** 管理系统 */
  MANAGE_SYSTEM = 'manage_system',
}

/**
 * 用户会话数据
 */
export interface UserSession {
  /** 用户ID */
  userId: string;
  /** 用户名 */
  username: string;
  /** 显示名称 */
  displayName?: string;
  /** 头像URL */
  avatarUrl?: string;
  /** 角色 */
  role: UserRole;
  /** 权限 */
  permissions: Set<UserPermission>;
  /** 自定义数据 */
  customData?: Record<string, any>;
  /** 连接时间 */
  connectionTime: number;
  /** 上次活动时间 */
  lastActivityTime: number;
  /** 是否在线 */
  isOnline: boolean;
  /** 是否已验证 */
  isAuthenticated: boolean;
  /** 会话令牌 */
  sessionToken?: string;
  /** 客户端信息 */
  clientInfo?: {
    /** IP地址 */
    ip?: string;
    /** 用户代理 */
    userAgent?: string;
    /** 设备类型 */
    deviceType?: string;
    /** 浏览器 */
    browser?: string;
    /** 操作系统 */
    os?: string;
  };
}

/**
 * 用户会话管理器配置
 */
export interface UserSessionManagerConfig {
  /** 会话超时时间（毫秒） */
  sessionTimeout?: number;
  /** 是否启用会话超时 */
  enableSessionTimeout?: boolean;
  /** 是否启用权限检查 */
  enablePermissionCheck?: boolean;
  /** 默认角色 */
  defaultRole?: UserRole;
  /** 角色权限映射 */
  rolePermissions?: Record<UserRole, UserPermission[]>;
  /** 是否允许匿名用户 */
  allowAnonymous?: boolean;
  /** 最大用户数量 */
  maxUsers?: number;
}

/**
 * 用户会话管理器
 * 负责管理用户会话和权限
 */
export class UserSessionManager extends EventEmitter {
  /** 配置 */
  private config: Required<UserSessionManagerConfig>;
  
  /** 用户会话映射表 */
  private sessions: Map<string, UserSession> = new Map();
  
  /** 角色权限映射表 */
  private rolePermissions: Map<UserRole, Set<UserPermission>> = new Map();
  
  /** 会话清理定时器ID */
  private cleanupTimerId: number | null = null;
  
  /**
   * 创建用户会话管理器
   * @param config 配置
   */
  constructor(config: UserSessionManagerConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      sessionTimeout: 30 * 60 * 1000, // 30分钟
      enableSessionTimeout: true,
      enablePermissionCheck: true,
      defaultRole: UserRole.USER,
      rolePermissions: {
        [UserRole.GUEST]: [UserPermission.VIEW],
        [UserRole.USER]: [UserPermission.VIEW, UserPermission.EDIT, UserPermission.CREATE],
        [UserRole.ADMIN]: [UserPermission.VIEW, UserPermission.EDIT, UserPermission.CREATE, UserPermission.DELETE, UserPermission.MANAGE_USERS],
        [UserRole.SUPER_ADMIN]: [UserPermission.VIEW, UserPermission.EDIT, UserPermission.CREATE, UserPermission.DELETE, UserPermission.MANAGE_USERS, UserPermission.MANAGE_PERMISSIONS, UserPermission.MANAGE_SYSTEM],
      },
      allowAnonymous: true,
      maxUsers: 100,
      ...config,
    };
    
    // 初始化角色权限映射
    this.initRolePermissions();
    
    // 启动会话清理定时器
    if (this.config.enableSessionTimeout) {
      this.startSessionCleanup();
    }
  }
  
  /**
   * 初始化角色权限映射
   */
  private initRolePermissions(): void {
    for (const [role, permissions] of Object.entries(this.config.rolePermissions)) {
      this.rolePermissions.set(role as UserRole, new Set(permissions));
    }
  }
  
  /**
   * 启动会话清理
   */
  private startSessionCleanup(): void {
    if (this.cleanupTimerId !== null) {
      return;
    }
    
    this.cleanupTimerId = window.setInterval(() => {
      this.cleanupSessions();
    }, 60000); // 每分钟检查一次
  }
  
  /**
   * 停止会话清理
   */
  private stopSessionCleanup(): void {
    if (this.cleanupTimerId !== null) {
      clearInterval(this.cleanupTimerId);
      this.cleanupTimerId = null;
    }
  }
  
  /**
   * 清理过期会话
   */
  private cleanupSessions(): void {
    const now = Date.now();
    const timeout = this.config.sessionTimeout;
    
    for (const [userId, session] of this.sessions.entries()) {
      if (session.isOnline && now - session.lastActivityTime > timeout) {
        // 会话超时，标记为离线
        session.isOnline = false;
        
        // 触发用户离线事件
        this.emit('userOffline', userId, session);
      }
    }
  }
  
  /**
   * 创建用户会话
   * @param userId 用户ID
   * @param username 用户名
   * @param role 角色
   * @param isAuthenticated 是否已验证
   * @param sessionToken 会话令牌
   * @param clientInfo 客户端信息
   * @returns 用户会话
   */
  public createSession(
    userId: string,
    username: string,
    role: UserRole = this.config.defaultRole,
    isAuthenticated: boolean = true,
    sessionToken?: string,
    clientInfo?: UserSession['clientInfo']
  ): UserSession {
    // 检查是否已存在会话
    if (this.sessions.has(userId)) {
      const existingSession = this.sessions.get(userId)!;
      
      // 更新会话
      existingSession.isOnline = true;
      existingSession.lastActivityTime = Date.now();
      existingSession.isAuthenticated = isAuthenticated;
      
      if (sessionToken) {
        existingSession.sessionToken = sessionToken;
      }
      
      if (clientInfo) {
        existingSession.clientInfo = { ...existingSession.clientInfo, ...clientInfo };
      }
      
      // 触发用户更新事件
      this.emit('userUpdated', userId, existingSession);
      
      return existingSession;
    }
    
    // 检查是否超出最大用户数量
    if (this.sessions.size >= this.config.maxUsers) {
      throw new Error(`Maximum number of users (${this.config.maxUsers}) reached`);
    }
    
    // 检查是否允许匿名用户
    if (!isAuthenticated && !this.config.allowAnonymous) {
      throw new Error('Anonymous users are not allowed');
    }
    
    // 创建新会话
    const now = Date.now();
    const session: UserSession = {
      userId,
      username,
      role,
      permissions: new Set(this.getPermissionsForRole(role)),
      connectionTime: now,
      lastActivityTime: now,
      isOnline: true,
      isAuthenticated,
      sessionToken,
      clientInfo,
    };
    
    // 添加到会话映射表
    this.sessions.set(userId, session);
    
    // 触发用户加入事件
    this.emit('userJoined', userId, session);
    
    return session;
  }
  
  /**
   * 获取用户会话
   * @param userId 用户ID
   * @returns 用户会话
   */
  public getSession(userId: string): UserSession | undefined {
    return this.sessions.get(userId);
  }
  
  /**
   * 更新用户会话
   * @param userId 用户ID
   * @param updates 更新数据
   * @returns 更新后的会话
   */
  public updateSession(userId: string, updates: Partial<UserSession>): UserSession | undefined {
    const session = this.sessions.get(userId);
    if (!session) {
      return undefined;
    }
    
    // 更新会话
    Object.assign(session, updates);
    
    // 更新活动时间
    session.lastActivityTime = Date.now();
    
    // 如果角色发生变化，则更新权限
    if (updates.role && updates.role !== session.role) {
      session.role = updates.role;
      session.permissions = new Set(this.getPermissionsForRole(updates.role));
    }
    
    // 触发用户更新事件
    this.emit('userUpdated', userId, session);
    
    return session;
  }
  
  /**
   * 移除用户会话
   * @param userId 用户ID
   * @returns 是否成功移除
   */
  public removeSession(userId: string): boolean {
    const session = this.sessions.get(userId);
    if (!session) {
      return false;
    }
    
    // 从会话映射表中移除
    this.sessions.delete(userId);
    
    // 触发用户离开事件
    this.emit('userLeft', userId, session);
    
    return true;
  }
  
  /**
   * 标记用户在线
   * @param userId 用户ID
   * @returns 是否成功
   */
  public markUserOnline(userId: string): boolean {
    const session = this.sessions.get(userId);
    if (!session) {
      return false;
    }
    
    const wasOffline = !session.isOnline;
    
    // 更新会话
    session.isOnline = true;
    session.lastActivityTime = Date.now();
    
    // 如果之前是离线状态，则触发用户上线事件
    if (wasOffline) {
      this.emit('userOnline', userId, session);
    }
    
    return true;
  }
  
  /**
   * 标记用户离线
   * @param userId 用户ID
   * @returns 是否成功
   */
  public markUserOffline(userId: string): boolean {
    const session = this.sessions.get(userId);
    if (!session) {
      return false;
    }
    
    const wasOnline = session.isOnline;
    
    // 更新会话
    session.isOnline = false;
    
    // 如果之前是在线状态，则触发用户离线事件
    if (wasOnline) {
      this.emit('userOffline', userId, session);
    }
    
    return true;
  }
  
  /**
   * 更新用户活动时间
   * @param userId 用户ID
   * @returns 是否成功
   */
  public updateUserActivity(userId: string): boolean {
    const session = this.sessions.get(userId);
    if (!session) {
      return false;
    }
    
    // 更新活动时间
    session.lastActivityTime = Date.now();
    
    return true;
  }
  
  /**
   * 检查用户是否有权限
   * @param userId 用户ID
   * @param permission 权限
   * @returns 是否有权限
   */
  public hasPermission(userId: string, permission: UserPermission): boolean {
    if (!this.config.enablePermissionCheck) {
      return true;
    }
    
    const session = this.sessions.get(userId);
    if (!session) {
      return false;
    }
    
    return session.permissions.has(permission);
  }
  
  /**
   * 授予用户权限
   * @param userId 用户ID
   * @param permission 权限
   * @returns 是否成功
   */
  public grantPermission(userId: string, permission: UserPermission): boolean {
    const session = this.sessions.get(userId);
    if (!session) {
      return false;
    }
    
    // 添加权限
    session.permissions.add(permission);
    
    // 触发权限更新事件
    this.emit('permissionUpdated', userId, session);
    
    return true;
  }
  
  /**
   * 撤销用户权限
   * @param userId 用户ID
   * @param permission 权限
   * @returns 是否成功
   */
  public revokePermission(userId: string, permission: UserPermission): boolean {
    const session = this.sessions.get(userId);
    if (!session) {
      return false;
    }
    
    // 移除权限
    const hadPermission = session.permissions.delete(permission);
    
    // 如果之前有该权限，则触发权限更新事件
    if (hadPermission) {
      this.emit('permissionUpdated', userId, session);
    }
    
    return hadPermission;
  }
  
  /**
   * 设置用户角色
   * @param userId 用户ID
   * @param role 角色
   * @returns 是否成功
   */
  public setUserRole(userId: string, role: UserRole): boolean {
    const session = this.sessions.get(userId);
    if (!session) {
      return false;
    }
    
    // 更新角色
    session.role = role;
    
    // 更新权限
    session.permissions = new Set(this.getPermissionsForRole(role));
    
    // 触发角色更新事件
    this.emit('roleUpdated', userId, session);
    
    return true;
  }
  
  /**
   * 获取角色的权限
   * @param role 角色
   * @returns 权限列表
   */
  public getPermissionsForRole(role: UserRole): UserPermission[] {
    const permissions = this.rolePermissions.get(role);
    return permissions ? Array.from(permissions) : [];
  }
  
  /**
   * 设置角色的权限
   * @param role 角色
   * @param permissions 权限列表
   */
  public setRolePermissions(role: UserRole, permissions: UserPermission[]): void {
    this.rolePermissions.set(role, new Set(permissions));
    
    // 更新所有具有该角色的用户的权限
    for (const [userId, session] of this.sessions.entries()) {
      if (session.role === role) {
        session.permissions = new Set(permissions);
        
        // 触发权限更新事件
        this.emit('permissionUpdated', userId, session);
      }
    }
  }
  
  /**
   * 获取所有在线用户
   * @returns 在线用户列表
   */
  public getOnlineUsers(): UserSession[] {
    return Array.from(this.sessions.values()).filter(session => session.isOnline);
  }
  
  /**
   * 获取所有用户
   * @returns 用户列表
   */
  public getAllUsers(): UserSession[] {
    return Array.from(this.sessions.values());
  }
  
  /**
   * 获取用户数量
   * @returns 用户数量
   */
  public getUserCount(): number {
    return this.sessions.size;
  }
  
  /**
   * 获取在线用户数量
   * @returns 在线用户数量
   */
  public getOnlineUserCount(): number {
    return this.getOnlineUsers().length;
  }
  
  /**
   * 清空所有会话
   */
  public clearSessions(): void {
    this.sessions.clear();
    
    // 触发会话清空事件
    this.emit('sessionCleared');
  }
  
  /**
   * 销毁管理器
   */
  public dispose(): void {
    this.stopSessionCleanup();
    this.clearSessions();
    this.removeAllListeners();
  }
}
