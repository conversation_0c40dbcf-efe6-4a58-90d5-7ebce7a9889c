/**
 * 缓存性能测试
 * 测试缓存服务的性能
 */
import { check } from 'k6';
import http from 'k6/http';
import { sleep } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';
import { CONFIG, randomSleep, getRandomItem } from './config.js';

// 性能指标
const cacheHits = new Counter('cache_hits');
const cacheMisses = new Counter('cache_misses');
const requestLatency = new Trend('request_latency');
const requestsPerSecond = new Rate('requests_per_second');
const errorRate = new Rate('error_rate');

// 测试数据
const testData = {
  keys: [],
};

// 初始化函数 - 在测试开始前运行一次
export function setup() {
  // 生成测试键
  for (let i = 0; i < CONFIG.uniqueKeysCount; i++) {
    testData.keys.push(`test-key-${i}`);
  }

  return testData;
}

// 默认函数 - 在测试期间为每个虚拟用户运行
export default function(data) {
  // 随机选择一个键
  const key = getRandomItem(data.keys);
  
  // 发送请求，测试缓存性能
  const startTime = Date.now();
  
  const res = http.get(`${CONFIG.apiGateway}/api/cache-test`, {
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'max-age=60',
    },
    params: {
      key: key, // 用于缓存键的生成
    },
  });
  
  const duration = Date.now() - startTime;
  
  // 记录请求延迟
  requestLatency.add(duration);
  
  // 记录请求成功/失败
  errorRate.add(res.status !== 200);
  
  // 记录每秒请求数
  requestsPerSecond.add(1);
  
  // 检查响应
  check(res, {
    '请求成功': (r) => r.status === 200,
  });
  
  // 检查缓存命中
  if (res.headers['X-Cache'] === 'HIT') {
    cacheHits.add(1);
  } else {
    cacheMisses.add(1);
  }
  
  // 短暂休息
  randomSleep(0.1, 0.5);
}

// 测试配置
export const options = {
  scenarios: {
    // 缓存预热测试
    cache_warmup: {
      executor: 'constant-vus',
      vus: 10,
      duration: '30s',
    },
    // 高并发测试
    high_concurrency: {
      executor: 'constant-vus',
      vus: 50,
      duration: '1m',
      startTime: '30s',
    },
    // 缓存过期测试
    cache_expiry: {
      executor: 'constant-vus',
      vus: 10,
      duration: '2m',
      startTime: '1m30s',
    },
  },
  thresholds: {
    'request_latency': ['p(95)<100'], // 95%的请求延迟小于100ms
    'requests_per_second': ['avg>200'], // 平均每秒请求数大于200
    'error_rate': ['rate<0.01'], // 错误率小于1%
    'cache_hits': ['count>1000'], // 缓存命中次数大于1000
  },
};
