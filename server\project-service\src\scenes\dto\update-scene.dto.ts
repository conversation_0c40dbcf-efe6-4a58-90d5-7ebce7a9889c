/**
 * 更新场景DTO
 */
import { IsString, IsOptional, IsBoolean, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateSceneDto {
  @ApiProperty({ description: '场景名称', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '场景描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '缩略图URL', required: false })
  @IsString()
  @IsOptional()
  thumbnailUrl?: string;

  @ApiProperty({ description: '是否为默认场景', required: false })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;

  @ApiProperty({ description: '元数据', required: false, type: 'object' })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
