/**
 * 数字人组件
 * 扩展AvatarComponent，添加数字人特有功能
 */
import * as THREE from 'three';
import { AvatarComponent, AvatarComponentConfig, AvatarType } from './AvatarComponent';
import type { Entity } from '../../core/Entity';
import type { MultiActionFusionManager } from '../animation/MultiActionFusionManager';

/**
 * 面部几何数据
 */
export interface FaceGeometry {
  /** 面部网格 */
  mesh: THREE.Mesh;
  /** 面部顶点 */
  vertices: Float32Array;
  /** 面部法线 */
  normals: Float32Array;
  /** UV坐标 */
  uvs: Float32Array;
  /** 面部特征点 */
  landmarks: THREE.Vector3[];
  /** 面部边界框 */
  boundingBox: THREE.Box3;
}

/**
 * 身体变形目标
 */
export interface BodyMorphTargets {
  /** 身高调整 */
  height: number;
  /** 体重调整 */
  weight: number;
  /** 肌肉量 */
  muscle: number;
  /** 胸围 */
  chest: number;
  /** 腰围 */
  waist: number;
  /** 臀围 */
  hips: number;
  /** 肩宽 */
  shoulders: number;
  /** 自定义变形目标 */
  custom: Map<string, number>;
}

/**
 * 服装插槽类型
 */
export enum ClothingSlotType {
  HEAD = 'head',
  HAIR = 'hair',
  FACE = 'face',
  UPPER_BODY = 'upperBody',
  LOWER_BODY = 'lowerBody',
  FEET = 'feet',
  HANDS = 'hands',
  ACCESSORIES = 'accessories',
  JEWELRY = 'jewelry'
}

/**
 * 服装插槽
 */
export interface ClothingSlot {
  /** 插槽类型 */
  type: ClothingSlotType;
  /** 当前服装项ID */
  currentItemId?: string;
  /** 服装项URL */
  itemUrl?: string;
  /** 服装项元数据 */
  metadata?: Record<string, any>;
  /** 是否启用 */
  enabled: boolean;
  /** 适配参数 */
  fittingParams?: {
    scale: THREE.Vector3;
    offset: THREE.Vector3;
    rotation: THREE.Euler;
  };
}

/**
 * 服装插槽集合
 */
export interface ClothingSlots {
  /** 插槽映射 */
  slots: Map<ClothingSlotType, ClothingSlot>;
  /** 当前服装组合ID */
  currentOutfitId?: string;
  /** 服装组合名称 */
  outfitName?: string;
}

/**
 * 个性特征
 */
export interface PersonalityTraits {
  /** 性格类型 */
  personalityType: string;
  /** 情绪状态 */
  emotionalState: string;
  /** 行为模式 */
  behaviorPattern: string;
  /** 语音特征 */
  voiceCharacteristics: {
    pitch: number;
    speed: number;
    tone: string;
  };
  /** 动作偏好 */
  movementPreferences: {
    walkingStyle: string;
    gestureFrequency: number;
    posture: string;
  };
  /** 自定义特征 */
  customTraits: Map<string, any>;
}

/**
 * 数字人创建来源
 */
export enum DigitalHumanSource {
  /** 照片生成 */
  PHOTO = 'photo',
  /** 文件上传 */
  UPLOAD = 'upload',
  /** 市场下载 */
  MARKETPLACE = 'marketplace',
  /** 手动创建 */
  MANUAL = 'manual'
}

/**
 * 数字人组件配置
 */
export interface DigitalHumanComponentConfig extends AvatarComponentConfig {
  /** 创建来源 */
  source?: DigitalHumanSource;
  /** 源照片URL */
  sourcePhotoUrl?: string;
  /** 源文件URL */
  sourceFileUrl?: string;
  /** 源数据 */
  sourceData?: {
    originalFile?: string;
    importData?: any;
    [key: string]: any;
  };
  /** 面部几何数据 */
  faceGeometry?: FaceGeometry;
  /** 身体变形目标 */
  bodyMorphTargets?: BodyMorphTargets;
  /** 服装插槽 */
  clothingSlots?: ClothingSlots;
  /** 个性特征 */
  personalityTraits?: PersonalityTraits;
  /** 数字人版本 */
  version?: string;
  /** 创建者ID */
  creatorId?: string;
  /** 许可类型 */
  licenseType?: string;
  /** 标签 */
  tags?: string[];
}

/**
 * 数字人组件
 * 扩展AvatarComponent，添加数字人特有功能
 */
export class DigitalHumanComponent extends AvatarComponent {
  /** 组件类型 */
  public static readonly TYPE = 'DigitalHumanComponent' as any;

  /** 创建来源 */
  public source: DigitalHumanSource;

  /** 源照片URL */
  public sourcePhotoUrl?: string;

  /** 源文件URL */
  public sourceFileUrl?: string;

  /** 面部几何数据 */
  public faceGeometry?: FaceGeometry;

  /** 身体变形目标 */
  public bodyMorphTargets: BodyMorphTargets;

  /** 服装插槽 */
  public clothingSlots: ClothingSlots;

  /** 个性特征 */
  public personalityTraits: PersonalityTraits;

  /** 数字人版本 */
  public version: string;

  /** 创建者ID */
  public creatorId?: string;

  /** 许可类型 */
  public licenseType: string;

  /** 标签 */
  public tags: string[];

  /** 是否正在生成 */
  public isGenerating: boolean = false;

  /** 生成进度 */
  public generationProgress: number = 0;

  /** 最后修改时间 */
  public lastModified: Date = new Date();

  /** 多动作融合管理器 */
  public multiActionFusionManager?: MultiActionFusionManager;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 组件配置
   */
  constructor(entity: Entity, config: DigitalHumanComponentConfig = {}) {
    // 调用父类构造函数，设置默认类型为NPC
    super(entity, {
      ...config,
      type: config.type || AvatarType.NPC
    });

    // 重新设置组件类型为DigitalHumanComponent
    (this as any).type = DigitalHumanComponent.TYPE;

    // 设置数字人特有属性
    this.source = config.source || DigitalHumanSource.MANUAL;
    this.sourcePhotoUrl = config.sourcePhotoUrl;
    this.sourceFileUrl = config.sourceFileUrl;
    this.faceGeometry = config.faceGeometry;
    this.version = config.version || '1.0.0';
    this.creatorId = config.creatorId;
    this.licenseType = config.licenseType || 'private';
    this.tags = config.tags || [];

    // 初始化身体变形目标
    this.bodyMorphTargets = config.bodyMorphTargets || {
      height: 0,
      weight: 0,
      muscle: 0,
      chest: 0,
      waist: 0,
      hips: 0,
      shoulders: 0,
      custom: new Map()
    };

    // 初始化服装插槽
    this.clothingSlots = config.clothingSlots || {
      slots: new Map(),
      currentOutfitId: undefined,
      outfitName: undefined
    };

    // 初始化默认服装插槽
    this.initializeDefaultClothingSlots();

    // 初始化个性特征
    this.personalityTraits = config.personalityTraits || {
      personalityType: 'neutral',
      emotionalState: 'calm',
      behaviorPattern: 'normal',
      voiceCharacteristics: {
        pitch: 0,
        speed: 1,
        tone: 'neutral'
      },
      movementPreferences: {
        walkingStyle: 'normal',
        gestureFrequency: 0.5,
        posture: 'upright'
      },
      customTraits: new Map()
    };
  }

  /**
   * 初始化默认服装插槽
   */
  private initializeDefaultClothingSlots(): void {
    const defaultSlots = [
      ClothingSlotType.HEAD,
      ClothingSlotType.HAIR,
      ClothingSlotType.FACE,
      ClothingSlotType.UPPER_BODY,
      ClothingSlotType.LOWER_BODY,
      ClothingSlotType.FEET,
      ClothingSlotType.HANDS,
      ClothingSlotType.ACCESSORIES,
      ClothingSlotType.JEWELRY
    ];

    for (const slotType of defaultSlots) {
      if (!this.clothingSlots.slots.has(slotType)) {
        this.clothingSlots.slots.set(slotType, {
          type: slotType,
          enabled: true
        });
      }
    }
  }

  /**
   * 设置面部几何数据
   * @param faceGeometry 面部几何数据
   */
  public setFaceGeometry(faceGeometry: FaceGeometry): void {
    this.faceGeometry = faceGeometry;
    this.lastModified = new Date();
  }

  /**
   * 获取面部几何数据
   * @returns 面部几何数据
   */
  public getFaceGeometry(): FaceGeometry | undefined {
    return this.faceGeometry;
  }

  /**
   * 设置身体变形目标
   * @param morphTargets 身体变形目标
   */
  public setBodyMorphTargets(morphTargets: Partial<BodyMorphTargets>): void {
    this.bodyMorphTargets = { ...this.bodyMorphTargets, ...morphTargets };
    this.lastModified = new Date();
  }

  /**
   * 获取身体变形目标
   * @returns 身体变形目标
   */
  public getBodyMorphTargets(): BodyMorphTargets {
    return this.bodyMorphTargets;
  }

  /**
   * 设置服装项
   * @param slotType 插槽类型
   * @param itemId 服装项ID
   * @param itemUrl 服装项URL
   * @param metadata 元数据
   */
  public setClothingItem(
    slotType: ClothingSlotType,
    itemId: string,
    itemUrl: string,
    metadata?: Record<string, any>
  ): void {
    const slot = this.clothingSlots.slots.get(slotType);
    if (slot) {
      slot.currentItemId = itemId;
      slot.itemUrl = itemUrl;
      slot.metadata = metadata;
      this.lastModified = new Date();
    }
  }

  /**
   * 移除服装项
   * @param slotType 插槽类型
   */
  public removeClothingItem(slotType: ClothingSlotType): void {
    const slot = this.clothingSlots.slots.get(slotType);
    if (slot) {
      slot.currentItemId = undefined;
      slot.itemUrl = undefined;
      slot.metadata = undefined;
      this.lastModified = new Date();
    }
  }

  /**
   * 获取服装项
   * @param slotType 插槽类型
   * @returns 服装插槽
   */
  public getClothingItem(slotType: ClothingSlotType): ClothingSlot | undefined {
    return this.clothingSlots.slots.get(slotType);
  }

  /**
   * 设置服装组合
   * @param outfitId 服装组合ID
   * @param outfitName 服装组合名称
   */
  public setOutfit(outfitId: string, outfitName?: string): void {
    this.clothingSlots.currentOutfitId = outfitId;
    this.clothingSlots.outfitName = outfitName;
    this.lastModified = new Date();
  }

  /**
   * 设置个性特征
   * @param traits 个性特征
   */
  public setPersonalityTraits(traits: Partial<PersonalityTraits>): void {
    this.personalityTraits = { ...this.personalityTraits, ...traits };
    this.lastModified = new Date();
  }

  /**
   * 获取个性特征
   * @returns 个性特征
   */
  public getPersonalityTraits(): PersonalityTraits {
    return this.personalityTraits;
  }

  /**
   * 设置生成状态
   * @param isGenerating 是否正在生成
   * @param progress 生成进度
   */
  public setGenerationState(isGenerating: boolean, progress: number = 0): void {
    this.isGenerating = isGenerating;
    this.generationProgress = Math.max(0, Math.min(1, progress));

    if (!isGenerating && progress >= 1) {
      this.setLoaded(true);
    }
  }

  /**
   * 添加标签
   * @param tag 标签
   */
  public addTag(tag: string): void {
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
      this.lastModified = new Date();
    }
  }

  /**
   * 移除标签
   * @param tag 标签
   */
  public removeTag(tag: string): void {
    const index = this.tags.indexOf(tag);
    if (index > -1) {
      this.tags.splice(index, 1);
      this.lastModified = new Date();
    }
  }

  /**
   * 检查是否有标签
   * @param tag 标签
   * @returns 是否有标签
   */
  public hasTag(tag: string): boolean {
    return this.tags.includes(tag);
  }

  /**
   * 获取数字人信息摘要
   * @returns 数字人信息摘要
   */
  public getSummary(): {
    id: string;
    name: string;
    source: DigitalHumanSource;
    version: string;
    creatorId?: string;
    licenseType: string;
    tags: string[];
    isLoaded: boolean;
    isGenerating: boolean;
    lastModified: Date;
  } {
    return {
      id: this.entity.id,
      name: this.name,
      source: this.source,
      version: this.version,
      creatorId: this.creatorId,
      licenseType: this.licenseType,
      tags: [...this.tags],
      isLoaded: this.isLoaded,
      isGenerating: this.isGenerating,
      lastModified: this.lastModified
    };
  }

  /**
   * 克隆组件
   * @param entity 目标实体
   * @returns 克隆的组件
   */
  public clone(entity: Entity): DigitalHumanComponent {
    return new DigitalHumanComponent(entity, {
      enabled: this.isEnabled(),
      type: this.avatarType,
      userId: this.userId,
      name: this.name,
      modelUrl: this.modelUrl,
      source: this.source,
      sourcePhotoUrl: this.sourcePhotoUrl,
      sourceFileUrl: this.sourceFileUrl,
      faceGeometry: this.faceGeometry,
      bodyMorphTargets: { ...this.bodyMorphTargets },
      clothingSlots: {
        slots: new Map(this.clothingSlots.slots),
        currentOutfitId: this.clothingSlots.currentOutfitId,
        outfitName: this.clothingSlots.outfitName
      },
      personalityTraits: { ...this.personalityTraits },
      version: this.version,
      creatorId: this.creatorId,
      licenseType: this.licenseType,
      tags: [...this.tags]
    });
  }
}
