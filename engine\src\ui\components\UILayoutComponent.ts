/**
 * UILayoutComponent.ts
 *
 * UI布局组件，用于管理UI元素的布局
 */

import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector2 } from 'three';
import { IUILayout, UILayoutType } from '../interfaces/IUIElement';
import { UIComponent } from './UIComponent';

/**
 * 网格布局参数
 */
export interface GridLayoutParams {
  columns: number;
  rows?: number;
  cellWidth: number;
  cellHeight: number;
  columnGap?: number;
  rowGap?: number;
  justifyItems?: 'start' | 'center' | 'end' | 'stretch';
  alignItems?: 'start' | 'center' | 'end' | 'stretch';
  autoFlow?: 'row' | 'column';
}

/**
 * 弹性布局参数
 */
export interface FlexLayoutParams {
  direction: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch';
  alignContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'stretch';
  gap?: number;
}

/**
 * 绝对布局参数
 */
export interface AbsoluteLayoutParams {
  // 没有特定参数，每个子元素使用自己的位置
}

/**
 * 相对布局参数
 */
export interface RelativeLayoutParams {
  spacing?: number;
  padding?: number | { top?: number, right?: number, bottom?: number, left?: number };
}

/**
 * 布局项参数
 */
export interface LayoutItemParams {
  // 网格布局项参数
  gridColumn?: string;
  gridRow?: string;
  gridArea?: string;

  // 弹性布局项参数
  flexGrow?: number;
  flexShrink?: number;
  flexBasis?: number | string;
  alignSelf?: 'auto' | 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch';
  order?: number;

  // 绝对布局项参数
  left?: number;
  top?: number;
  right?: number;
  bottom?: number;
  zIndex?: number;

  // 相对布局项参数
  margin?: number | { top?: number, right?: number, bottom?: number, left?: number };
}

/**
 * 网格布局
 */
export class GridLayout implements IUILayout {
  type: UILayoutType = UILayoutType.GRID;
  params: GridLayoutParams;

  /**
   * 构造函数
   * @param params 网格布局参数
   */
  constructor(params: GridLayoutParams) {
    this.params = {
      columns: params.columns,
      rows: params.rows,
      cellWidth: params.cellWidth,
      cellHeight: params.cellHeight,
      columnGap: params.columnGap || 0,
      rowGap: params.rowGap || 0,
      justifyItems: params.justifyItems || 'center',
      alignItems: params.alignItems || 'center',
      autoFlow: params.autoFlow || 'row'
    };
  }

  /**
   * 应用布局
   * @param container 容器UI元素
   */
  apply(container: UIComponent): void {
    const children = container.children.filter(child => child instanceof UIComponent) as UIComponent[];
    if (children.length === 0) return;

    const { columns, rows, cellWidth, cellHeight, columnGap, rowGap, autoFlow } = this.params;

    // 计算实际行数（如果没有指定）
    const actualRows = rows || Math.ceil(children.length / columns);

    // 计算总宽度和总高度
    const totalWidth = columns * cellWidth + (columns - 1) * columnGap!;
    const totalHeight = actualRows * cellHeight + (actualRows - 1) * rowGap!;

    // 更新容器尺寸（如果需要）
    if (container.size.x < totalWidth) {
      container.size.x = totalWidth;
    }
    if (container.size.y < totalHeight) {
      container.size.y = totalHeight;
    }

    // 计算起始位置（左上角）
    const startX = -totalWidth / 2 + cellWidth / 2;
    const startY = totalHeight / 2 - cellHeight / 2;

    // 应用布局到子元素
    for (let i = 0; i < children.length; i++) {
      const child = children[i];

      // 计算行和列索引
      let row, col;
      if (autoFlow === 'row') {
        row = Math.floor(i / columns);
        col = i % columns;
      } else {
        col = Math.floor(i / actualRows);
        row = i % actualRows;
      }

      // 计算位置
      const x = startX + col * (cellWidth + columnGap!);
      const y = startY - row * (cellHeight + rowGap!);

      // 设置子元素位置和尺寸
      child.setPosition(new Vector2(x, y));
      child.setSize(new Vector2(cellWidth, cellHeight));

      // 应用布局项参数（如果有）
      const layoutParams = (child as any).layoutParams as LayoutItemParams;
      if (layoutParams) {
        // 应用网格特定参数
        // 这里可以添加更多的网格布局项参数处理
      }
    }
  }
}

/**
 * 弹性布局
 */
export class FlexLayout implements IUILayout {
  type: UILayoutType = UILayoutType.FLEX;
  params: FlexLayoutParams;

  /**
   * 构造函数
   * @param params 弹性布局参数
   */
  constructor(params: FlexLayoutParams) {
    this.params = {
      direction: params.direction,
      wrap: params.wrap || 'nowrap',
      justifyContent: params.justifyContent || 'flex-start',
      alignItems: params.alignItems || 'center',
      alignContent: params.alignContent || 'flex-start',
      gap: params.gap || 0
    };
  }

  /**
   * 应用布局
   * @param container 容器UI元素
   */
  apply(container: UIComponent): void {
    const children = container.children.filter(child => child instanceof UIComponent) as UIComponent[];
    if (children.length === 0) return;

    const { direction, justifyContent, alignItems, gap } = this.params;
    // 注意：wrap 参数目前未使用，但保留在参数中以便将来实现换行功能
    const isRow = direction === 'row' || direction === 'row-reverse';
    const isReverse = direction === 'row-reverse' || direction === 'column-reverse';

    // 计算容器尺寸
    const containerWidth = container.size.x;
    const containerHeight = container.size.y;

    // 计算子元素的总尺寸和间隙
    let totalMainSize = 0;
    // 注意：totalCrossSize 目前未使用，但保留以便将来实现多行布局
    // let totalCrossSize = 0;
    let maxCrossSize = 0;

    for (const child of children) {
      if (isRow) {
        totalMainSize += child.size.x;
        maxCrossSize = Math.max(maxCrossSize, child.size.y);
      } else {
        totalMainSize += child.size.y;
        maxCrossSize = Math.max(maxCrossSize, child.size.x);
      }
    }

    // 添加间隙
    totalMainSize += gap! * (children.length - 1);

    // 计算主轴上的起始位置和步长
    let mainStart, mainStep;
    const mainSpace = (isRow ? containerWidth : containerHeight) - totalMainSize;

    switch (justifyContent) {
      case 'flex-start':
        mainStart = isRow ? -containerWidth / 2 + children[0].size.x / 2 : containerHeight / 2 - children[0].size.y / 2;
        mainStep = gap!;
        break;
      case 'flex-end':
        mainStart = isRow ?
          containerWidth / 2 - totalMainSize + children[0].size.x / 2 :
          -containerHeight / 2 + totalMainSize - children[0].size.y / 2;
        mainStep = gap!;
        break;
      case 'center':
        mainStart = isRow ?
          -totalMainSize / 2 + children[0].size.x / 2 :
          totalMainSize / 2 - children[0].size.y / 2;
        mainStep = gap!;
        break;
      case 'space-between':
        mainStart = isRow ? -containerWidth / 2 + children[0].size.x / 2 : containerHeight / 2 - children[0].size.y / 2;
        mainStep = mainSpace / (children.length - 1);
        break;
      case 'space-around':
        const spaceAround = mainSpace / (children.length * 2);
        mainStart = isRow ?
          -containerWidth / 2 + spaceAround + children[0].size.x / 2 :
          containerHeight / 2 - spaceAround - children[0].size.y / 2;
        mainStep = spaceAround * 2;
        break;
      case 'space-evenly':
        const spaceEvenly = mainSpace / (children.length + 1);
        mainStart = isRow ?
          -containerWidth / 2 + spaceEvenly + children[0].size.x / 2 :
          containerHeight / 2 - spaceEvenly - children[0].size.y / 2;
        mainStep = spaceEvenly;
        break;
      default:
        mainStart = isRow ? -containerWidth / 2 + children[0].size.x / 2 : containerHeight / 2 - children[0].size.y / 2;
        mainStep = gap!;
    }

    // 如果是反向，调整起始位置
    if (isReverse) {
      mainStart = isRow ?
        containerWidth / 2 - children[0].size.x / 2 :
        -containerHeight / 2 + children[0].size.y / 2;
    }

    // 计算交叉轴上的位置
    let crossPos;
    switch (alignItems) {
      case 'flex-start':
        crossPos = isRow ? containerHeight / 2 - children[0].size.y / 2 : -containerWidth / 2 + children[0].size.x / 2;
        break;
      case 'flex-end':
        crossPos = isRow ? -containerHeight / 2 + children[0].size.y / 2 : containerWidth / 2 - children[0].size.x / 2;
        break;
      case 'center':
        crossPos = 0;
        break;
      case 'stretch':
        // 拉伸子元素以填充交叉轴
        for (const child of children) {
          if (isRow) {
            child.setSize(new Vector2(child.size.x, containerHeight));
          } else {
            child.setSize(new Vector2(containerWidth, child.size.y));
          }
        }
        crossPos = 0;
        break;
      default:
        crossPos = 0;
    }

    // 应用布局到子元素
    let mainPos = mainStart;
    for (let i = 0; i < children.length; i++) {
      const child = children[i];

      // 计算位置
      let x, y;
      if (isRow) {
        x = isReverse ? mainPos - child.size.x * (i === 0 ? 0 : 1) : mainPos;
        y = crossPos;
      } else {
        x = crossPos;
        y = isReverse ? mainPos - child.size.y * (i === 0 ? 0 : 1) : mainPos;
      }

      // 设置子元素位置
      child.setPosition(new Vector2(x, y));

      // 更新主轴位置
      if (isRow) {
        mainPos += isReverse ? -(child.size.x + mainStep) : (child.size.x + mainStep);
      } else {
        mainPos += isReverse ? -(child.size.y + mainStep) : (child.size.y + mainStep);
      }

      // 应用布局项参数（如果有）
      const layoutParams = (child as any).layoutParams as LayoutItemParams;
      if (layoutParams) {
        // 应用弹性布局特定参数
        // 这里可以添加更多的弹性布局项参数处理
      }
    }
  }
}

/**
 * 绝对布局
 */
export class AbsoluteLayout implements IUILayout {
  type: UILayoutType = UILayoutType.ABSOLUTE;
  params: AbsoluteLayoutParams;

  /**
   * 构造函数
   * @param params 绝对布局参数
   */
  constructor(params: AbsoluteLayoutParams = {}) {
    this.params = params;
  }

  /**
   * 应用布局
   * @param container 容器UI元素
   */
  apply(container: UIComponent): void {
    const children = container.children.filter(child => child instanceof UIComponent) as UIComponent[];
    if (children.length === 0) return;

    // 绝对布局不改变子元素的位置，除非有特定的布局项参数
    for (const child of children) {
      const layoutParams = (child as any).layoutParams as LayoutItemParams;
      if (layoutParams) {
        // 应用绝对布局特定参数
        if (layoutParams.left !== undefined || layoutParams.right !== undefined ||
            layoutParams.top !== undefined || layoutParams.bottom !== undefined) {

          const containerWidth = container.size.x;
          const containerHeight = container.size.y;

          let x = child.position instanceof Vector2 ? child.position.x : (child.position as any).x;
          let y = child.position instanceof Vector2 ? child.position.y : (child.position as any).y;

          if (layoutParams.left !== undefined) {
            x = -containerWidth / 2 + layoutParams.left + child.size.x / 2;
          } else if (layoutParams.right !== undefined) {
            x = containerWidth / 2 - layoutParams.right - child.size.x / 2;
          }

          if (layoutParams.top !== undefined) {
            y = containerHeight / 2 - layoutParams.top - child.size.y / 2;
          } else if (layoutParams.bottom !== undefined) {
            y = -containerHeight / 2 + layoutParams.bottom + child.size.y / 2;
          }

          child.setPosition(new Vector2(x, y));
        }

        // 设置z-index
        if (layoutParams.zIndex !== undefined) {
          child.zIndex = layoutParams.zIndex;
        }
      }
    }
  }
}

/**
 * 相对布局
 */
export class RelativeLayout implements IUILayout {
  type: UILayoutType = UILayoutType.RELATIVE;
  params: RelativeLayoutParams;

  /**
   * 构造函数
   * @param params 相对布局参数
   */
  constructor(params: RelativeLayoutParams = {}) {
    this.params = {
      spacing: params.spacing || 0,
      padding: params.padding || 0
    };
  }

  /**
   * 应用布局
   * @param container 容器UI元素
   */
  apply(container: UIComponent): void {
    const children = container.children.filter(child => child instanceof UIComponent) as UIComponent[];
    if (children.length === 0) return;

    const { spacing, padding } = this.params;

    // 计算内边距
    let paddingTop = 0, paddingLeft = 0;

    if (typeof padding === 'number') {
      paddingTop = paddingLeft = padding;
      // 右边距和底部边距目前未使用
      // let paddingRight = padding, paddingBottom = padding;
    } else if (padding) {
      paddingTop = padding.top || 0;
      paddingLeft = padding.left || 0;
      // 右边距和底部边距目前未使用
      // let paddingRight = padding.right || 0;
      // let paddingBottom = padding.bottom || 0;
    }

    // 计算容器的内部尺寸 - 目前未使用，但保留以便将来实现更复杂的布局
    // const innerWidth = container.size.x - paddingLeft - paddingRight;
    // const innerHeight = container.size.y - paddingTop - paddingBottom;

    // 计算起始位置
    const startX = -container.size.x / 2 + paddingLeft;
    const startY = container.size.y / 2 - paddingTop;

    // 应用布局到子元素
    let currentY = startY;

    for (let i = 0; i < children.length; i++) {
      const child = children[i];

      // 应用布局项参数（如果有）
      const layoutParams = (child as any).layoutParams as LayoutItemParams;
      let marginTop = 0, marginBottom = 0, marginLeft = 0;
      // marginRight 目前在垂直布局中未使用，但保留在代码注释中以便将来实现水平布局
      // let marginRight = 0;

      if (layoutParams && layoutParams.margin) {
        if (typeof layoutParams.margin === 'number') {
          marginTop = marginBottom = marginLeft = layoutParams.margin;
          // 右边距目前未使用
          // let marginRight = layoutParams.margin;
        } else {
          marginTop = layoutParams.margin.top || 0;
          // 右边距目前未使用
          // let marginRight = layoutParams.margin.right || 0;
          marginBottom = layoutParams.margin.bottom || 0;
          marginLeft = layoutParams.margin.left || 0;
        }
      }

      // 计算位置
      const x = startX + marginLeft + child.size.x / 2;
      const y = currentY - marginTop - child.size.y / 2;

      // 设置子元素位置
      child.setPosition(new Vector2(x, y));

      // 更新当前Y位置
      currentY = y - child.size.y / 2 - marginBottom - spacing!;
    }
  }
}

/**
 * UI布局组件
 * 用于管理实体的UI布局
 */
export class UILayoutComponent extends Component {
  // 布局
  layout: IUILayout;

  // 布局项参数（用于子元素）
  layoutItemParams?: LayoutItemParams;

  /**
   * 构造函数
   * @param entity 关联的实体
   * @param layout 布局
   * @param layoutItemParams 布局项参数
   */
  constructor(entity: Entity, layout: IUILayout, layoutItemParams?: LayoutItemParams) {
    // 调用基类构造函数，传入组件类型名称
    super('UILayout');
    // 设置实体引用
    this.setEntity(entity);
    this.layout = layout;
    this.layoutItemParams = layoutItemParams;
  }

  /**
   * 应用布局
   * @param container 容器UI元素
   */
  applyLayout(container: UIComponent): void {
    this.layout.apply(container);
  }

  /**
   * 设置布局
   * @param layout 新布局
   */
  setLayout(layout: IUILayout): void {
    this.layout = layout;
  }

  /**
   * 设置布局项参数
   * @param params 布局项参数
   */
  setLayoutItemParams(params: LayoutItemParams): void {
    this.layoutItemParams = params;
  }
}
