/**
 * 抓取网络组件
 * 用于处理抓取系统的网络同步
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter, EventCallback } from '../../utils/EventEmitter';
import { Hand } from '../components/GrabbableComponent';
import { NetworkManager } from '../../network/NetworkManager';
import { NetworkQualityLevel } from '../../network/types';
import { Vector3, Quaternion } from 'three';
import { ObjectPool } from '../../utils/ObjectPool';

/**
 * 抓取网络事件类型
 */
export enum GrabNetworkEventType {
  /** 抓取请求 */
  GRAB_REQUEST = 'grabRequest',
  /** 抓取确认 */
  GRAB_CONFIRM = 'grabConfirm',
  /** 抓取拒绝 */
  GRAB_REJECT = 'grabReject',
  /** 释放请求 */
  RELEASE_REQUEST = 'releaseRequest',
  /** 释放确认 */
  RELEASE_CONFIRM = 'releaseConfirm',
  /** 状态同步 */
  STATE_SYNC = 'stateSync',
  /** 抛掷事件 */
  THROW_EVENT = 'throwEvent',
  /** 旋转事件 */
  ROTATE_EVENT = 'rotateEvent',
  /** 权限请求 */
  AUTHORITY_REQUEST = 'authorityRequest',
  /** 权限确认 */
  AUTHORITY_CONFIRM = 'authorityConfirm',
  /** 权限拒绝 */
  AUTHORITY_REJECT = 'authorityReject'
}

/**
 * 抓取网络事件数据
 */
export interface GrabNetworkEventData {
  /** 事件类型 */
  type: GrabNetworkEventType;
  /** 抓取者实体ID */
  grabberEntityId: string;
  /** 被抓取实体ID */
  grabbedEntityId: string;
  /** 抓取手 */
  hand: Hand;
  /** 时间戳 */
  timestamp: number;
  /** 用户ID */
  userId: string;
  /** 会话ID */
  sessionId: string;
  /** 状态数据 */
  state?: any;
  /** 速度向量（用于抛掷） */
  velocity?: { x: number, y: number, z: number };
  /** 角速度向量（用于抛掷） */
  angularVelocity?: { x: number, y: number, z: number };
  /** 旋转数据 */
  rotation?: { x: number, y: number, z: number, w: number };
}

/**
 * 抓取网络组件配置
 */
export interface GrabNetworkComponentConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 同步间隔（毫秒） */
  syncInterval?: number;
  /** 网络延迟（毫秒） - 仅用于模拟 */
  networkLatency?: number;
  /** 是否使用压缩 */
  useCompression?: boolean;
  /** 是否使用预测 */
  usePrediction?: boolean;
  /** 是否使用权限控制 */
  useAuthorityControl?: boolean;
  /** 是否使用自适应同步 */
  useAdaptiveSync?: boolean;
}

/**
 * 抓取网络组件
 */
export class GrabNetworkComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'GrabNetworkComponent';

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否启用 */
  private _enabled: boolean;

  /** 同步间隔（毫秒） */
  private _syncInterval: number;

  /** 网络延迟（毫秒） - 仅用于模拟 */
  private _networkLatency: number;

  /** 是否使用压缩 */
  private _useCompression: boolean;

  /** 是否使用预测 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private _usePrediction: boolean;

  /** 是否使用权限控制 */
  private _useAuthorityControl: boolean;

  /** 是否使用自适应同步 */
  private _useAdaptiveSync: boolean;

  /** 是否已连接 */
  private _isConnected: boolean = false;

  /** 用户ID */
  private _userId: string = '';

  /** 会话ID */
  private _sessionId: string = '';

  /** 最后同步时间 */
  private _lastSyncTime: number = 0;

  /** 当前网络质量 */
  private _networkQuality: NetworkQualityLevel = NetworkQualityLevel.GOOD;

  /** 待处理事件队列 */
  private _pendingEvents: GrabNetworkEventData[] = [];

  /** 网络管理器引用 */
  private _networkManager?: NetworkManager;

  /** 事件对象池 */
  private _eventPool: ObjectPool<GrabNetworkEventData>;

  /** 当前拥有权限的实体映射 */
  private _entityAuthorities: Map<string, string> = new Map();

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config: GrabNetworkComponentConfig = {}) {
    super(GrabNetworkComponent.TYPE);
    this.entity = entity;

    // 设置配置
    this._enabled = config.enabled !== undefined ? config.enabled : true;
    this._syncInterval = config.syncInterval || 100; // 默认100ms
    this._networkLatency = config.networkLatency || 0;
    this._useCompression = config.useCompression !== undefined ? config.useCompression : true;
    this._usePrediction = config.usePrediction !== undefined ? config.usePrediction : true;
    this._useAuthorityControl = config.useAuthorityControl !== undefined ? config.useAuthorityControl : true;
    this._useAdaptiveSync = config.useAdaptiveSync !== undefined ? config.useAdaptiveSync : true;

    // 创建事件对象池
    this._eventPool = new ObjectPool<GrabNetworkEventData>({
      create: () => ({
        type: GrabNetworkEventType.STATE_SYNC,
        grabberEntityId: '',
        grabbedEntityId: '',
        hand: Hand.RIGHT,
        timestamp: 0,
        userId: '',
        sessionId: '',
        state: null
      }),
      reset: (obj) => {
        obj.type = GrabNetworkEventType.STATE_SYNC;
        obj.grabberEntityId = '';
        obj.grabbedEntityId = '';
        obj.hand = Hand.RIGHT;
        obj.timestamp = 0;
        obj.userId = '';
        obj.sessionId = '';
        obj.state = null;
        obj.velocity = undefined;
        obj.angularVelocity = undefined;
        obj.rotation = undefined;
      },
      initialCapacity: 20,
      maxCapacity: 100
    });
  }

  /**
   * 设置网络管理器
   * @param networkManager 网络管理器
   */
  setNetworkManager(networkManager: NetworkManager): void {
    this._networkManager = networkManager;
    this._isConnected = (networkManager as any).connected || false;

    // 监听网络状态变化
    networkManager.on('connected', () => {
      this._isConnected = true;
    });

    networkManager.on('disconnected', () => {
      this._isConnected = false;
    });

    // 监听网络质量变化
    networkManager.on('qualityChange', (quality: NetworkQualityLevel) => {
      this._networkQuality = quality;
      this.updateSyncInterval();
    });
  }

  /**
   * 更新同步间隔
   * 根据网络质量调整同步频率
   */
  private updateSyncInterval(): void {
    if (!this._useAdaptiveSync) return;

    switch (this._networkQuality) {
      case NetworkQualityLevel.EXCELLENT:
        this._syncInterval = 50; // 20Hz
        break;
      case NetworkQualityLevel.GOOD:
        this._syncInterval = 100; // 10Hz
        break;
      case NetworkQualityLevel.MEDIUM:
        this._syncInterval = 200; // 5Hz
        break;
      case NetworkQualityLevel.BAD:
        this._syncInterval = 300; // 3.33Hz
        break;
      case NetworkQualityLevel.POOR:
        this._syncInterval = 500; // 2Hz
        break;
    }
  }

  /**
   * 设置用户信息
   * @param userId 用户ID
   * @param sessionId 会话ID
   */
  setUserInfo(userId: string, sessionId: string): void {
    this._userId = userId;
    this._sessionId = sessionId;
  }

  /**
   * 发送抓取请求
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @returns 是否发送成功
   */
  sendGrabRequest(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.GRAB_REQUEST;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = hand;
    eventData.timestamp = Date.now();
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送抓取确认
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @returns 是否发送成功
   */
  sendGrabConfirm(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.GRAB_CONFIRM;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = hand;
    eventData.timestamp = Date.now();
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送抓取拒绝
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @returns 是否发送成功
   */
  sendGrabReject(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.GRAB_REJECT;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = hand;
    eventData.timestamp = Date.now();
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送释放请求
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @returns 是否发送成功
   */
  sendReleaseRequest(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.RELEASE_REQUEST;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = hand;
    eventData.timestamp = Date.now();
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送释放确认
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @returns 是否发送成功
   */
  sendReleaseConfirm(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.RELEASE_CONFIRM;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = hand;
    eventData.timestamp = Date.now();
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送状态同步
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @param state 状态数据
   * @returns 是否发送成功
   */
  sendStateSync(grabberEntityId: string, grabbedEntityId: string, hand: Hand, state: any): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    // 检查是否需要同步
    const now = Date.now();
    if (now - this._lastSyncTime < this._syncInterval) {
      return false;
    }

    this._lastSyncTime = now;

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.STATE_SYNC;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = hand;
    eventData.timestamp = now;
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;
    eventData.state = state;

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送抛掷事件
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @param velocity 速度向量
   * @param angularVelocity 角速度向量
   * @returns 是否发送成功
   */
  sendThrowEvent(
    grabberEntityId: string,
    grabbedEntityId: string,
    hand: Hand,
    velocity: Vector3,
    angularVelocity: Vector3
  ): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.THROW_EVENT;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = hand;
    eventData.timestamp = Date.now();
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;
    eventData.velocity = { x: velocity.x, y: velocity.y, z: velocity.z };
    eventData.angularVelocity = { x: angularVelocity.x, y: angularVelocity.y, z: angularVelocity.z };

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送旋转事件
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @param rotation 旋转四元数
   * @returns 是否发送成功
   */
  sendRotateEvent(
    grabberEntityId: string,
    grabbedEntityId: string,
    hand: Hand,
    rotation: Quaternion
  ): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.ROTATE_EVENT;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = hand;
    eventData.timestamp = Date.now();
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;
    eventData.rotation = { x: rotation.x, y: rotation.y, z: rotation.z, w: rotation.w };

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送权限请求
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @returns 是否发送成功
   */
  sendAuthorityRequest(grabberEntityId: string, grabbedEntityId: string): boolean {
    if (!this._enabled || !this._isConnected || !this._useAuthorityControl) {
      return false;
    }

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.AUTHORITY_REQUEST;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = Hand.ANY; // 权限请求不需要指定手
    eventData.timestamp = Date.now();
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送权限确认
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @returns 是否发送成功
   */
  sendAuthorityConfirm(grabberEntityId: string, grabbedEntityId: string): boolean {
    if (!this._enabled || !this._isConnected || !this._useAuthorityControl) {
      return false;
    }

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.AUTHORITY_CONFIRM;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = Hand.ANY; // 权限确认不需要指定手
    eventData.timestamp = Date.now();
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送权限拒绝
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @returns 是否发送成功
   */
  sendAuthorityReject(grabberEntityId: string, grabbedEntityId: string): boolean {
    if (!this._enabled || !this._isConnected || !this._useAuthorityControl) {
      return false;
    }

    const eventData = this._eventPool.get();
    eventData.type = GrabNetworkEventType.AUTHORITY_REJECT;
    eventData.grabberEntityId = grabberEntityId;
    eventData.grabbedEntityId = grabbedEntityId;
    eventData.hand = Hand.ANY; // 权限拒绝不需要指定手
    eventData.timestamp = Date.now();
    eventData.userId = this._userId;
    eventData.sessionId = this._sessionId;

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送网络事件
   * @param eventData 事件数据
   * @private
   */
  private sendNetworkEvent(eventData: GrabNetworkEventData): void {
    // 如果有网络管理器，则使用网络管理器发送
    if (this._networkManager) {
      // 压缩数据（如果启用）
      const data = this._useCompression ? this.compressEventData(eventData) : eventData;

      // 发送数据
      this._networkManager.sendToAll('grab', data);
    } else {
      // 模拟网络延迟
      setTimeout(() => {
        // 这里应该是网络系统接收到事件后的回调
        // 在实际项目中，这部分应该由网络系统处理
        this.receiveNetworkEvent(eventData);
      }, this._networkLatency);
    }
  }

  /**
   * 压缩事件数据
   * @param eventData 事件数据
   * @returns 压缩后的数据
   * @private
   */
  private compressEventData(eventData: GrabNetworkEventData): any {
    // 简单实现，实际项目中应该使用更高效的压缩算法
    const compressed: any = {
      t: eventData.type,
      g: eventData.grabberEntityId,
      d: eventData.grabbedEntityId,
      h: eventData.hand,
      ts: eventData.timestamp,
      u: eventData.userId,
      s: eventData.sessionId
    };

    // 只包含必要的字段
    if (eventData.state) compressed.st = eventData.state;
    if (eventData.velocity) compressed.v = eventData.velocity;
    if (eventData.angularVelocity) compressed.av = eventData.angularVelocity;
    if (eventData.rotation) compressed.r = eventData.rotation;

    return compressed;
  }

  /**
   * 接收网络事件
   * @param eventData 事件数据
   */
  receiveNetworkEvent(eventData: GrabNetworkEventData): void {
    // 添加到待处理队列
    this._pendingEvents.push(eventData);
  }

  /**
   * 处理待处理事件
   * @private
   */
  private processPendingEvents(): void {
    // 处理所有待处理事件
    while (this._pendingEvents.length > 0) {
      const eventData = this._pendingEvents.shift()!;
      this.processEvent(eventData);
    }
  }

  /**
   * 处理事件
   * @param eventData 事件数据
   * @private
   */
  private processEvent(eventData: GrabNetworkEventData): void {
    // 根据事件类型处理
    switch (eventData.type) {
      case GrabNetworkEventType.GRAB_REQUEST:
        this.handleGrabRequest(eventData);
        break;
      case GrabNetworkEventType.GRAB_CONFIRM:
        this.handleGrabConfirm(eventData);
        break;
      case GrabNetworkEventType.GRAB_REJECT:
        this.handleGrabReject(eventData);
        break;
      case GrabNetworkEventType.RELEASE_REQUEST:
        this.handleReleaseRequest(eventData);
        break;
      case GrabNetworkEventType.RELEASE_CONFIRM:
        this.handleReleaseConfirm(eventData);
        break;
      case GrabNetworkEventType.STATE_SYNC:
        this.handleStateSync(eventData);
        break;
      case GrabNetworkEventType.THROW_EVENT:
        this.handleThrowEvent(eventData);
        break;
      case GrabNetworkEventType.ROTATE_EVENT:
        this.handleRotateEvent(eventData);
        break;
      case GrabNetworkEventType.AUTHORITY_REQUEST:
        this.handleAuthorityRequest(eventData);
        break;
      case GrabNetworkEventType.AUTHORITY_CONFIRM:
        this.handleAuthorityConfirm(eventData);
        break;
      case GrabNetworkEventType.AUTHORITY_REJECT:
        this.handleAuthorityReject(eventData);
        break;
    }

    // 回收事件对象
    this._eventPool.release(eventData);
  }

  /**
   * 处理抓取请求
   * @param eventData 事件数据
   * @private
   */
  private handleGrabRequest(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('grabRequest', eventData);
  }

  /**
   * 处理抓取确认
   * @param eventData 事件数据
   * @private
   */
  private handleGrabConfirm(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('grabConfirm', eventData);
  }

  /**
   * 处理抓取拒绝
   * @param eventData 事件数据
   * @private
   */
  private handleGrabReject(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('grabReject', eventData);
  }

  /**
   * 处理释放请求
   * @param eventData 事件数据
   * @private
   */
  private handleReleaseRequest(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('releaseRequest', eventData);
  }

  /**
   * 处理释放确认
   * @param eventData 事件数据
   * @private
   */
  private handleReleaseConfirm(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('releaseConfirm', eventData);
  }

  /**
   * 处理状态同步
   * @param eventData 事件数据
   * @private
   */
  private handleStateSync(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('stateSync', eventData);
  }

  /**
   * 处理抛掷事件
   * @param eventData 事件数据
   * @private
   */
  private handleThrowEvent(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('throwEvent', eventData);
  }

  /**
   * 处理旋转事件
   * @param eventData 事件数据
   * @private
   */
  private handleRotateEvent(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('rotateEvent', eventData);
  }

  /**
   * 处理权限请求
   * @param eventData 事件数据
   * @private
   */
  private handleAuthorityRequest(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('authorityRequest', eventData);
  }

  /**
   * 处理权限确认
   * @param eventData 事件数据
   * @private
   */
  private handleAuthorityConfirm(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('authorityConfirm', eventData);
  }

  /**
   * 处理权限拒绝
   * @param eventData 事件数据
   * @private
   */
  private handleAuthorityReject(eventData: GrabNetworkEventData): void {
    // 触发事件
    this.eventEmitter.emit('authorityReject', eventData);
  }

  /**
   * 检查实体权限
   * @param entityId 实体ID
   * @param userId 用户ID
   * @returns 是否有权限
   */
  hasAuthority(entityId: string, userId: string): boolean {
    if (!this._useAuthorityControl) {
      return true;
    }

    const authorityUserId = this._entityAuthorities.get(entityId);
    return authorityUserId === userId;
  }

  /**
   * 设置实体权限
   * @param entityId 实体ID
   * @param userId 用户ID
   */
  setAuthority(entityId: string, userId: string): void {
    if (!this._useAuthorityControl) {
      return;
    }

    this._entityAuthorities.set(entityId, userId);
  }

  /**
   * 更新组件
   * @param deltaTime 时间增量（秒）
   */
  update(_deltaTime: number): void {
    if (!this._enabled) {
      return;
    }

    // 处理待处理事件
    this.processPendingEvents();
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  on(event: string, listener: EventCallback): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  off(event: string, listener?: EventCallback): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 清空事件监听器
    this.eventEmitter.removeAllListeners();

    // 清空对象池
    this._eventPool.clear();

    // 清空待处理事件队列
    this._pendingEvents = [];

    // 清空实体权限映射
    this._entityAuthorities.clear();
  }
}
