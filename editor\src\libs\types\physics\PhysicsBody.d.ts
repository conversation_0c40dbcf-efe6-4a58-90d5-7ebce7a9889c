/**
 * 物理体组件
 * 为实体提供物理属性和行为
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Component } from '../core/Component';
import type { Entity } from '../core/Entity';
export declare enum BodyType {
    STATIC = "static",
    DYNAMIC = "dynamic",
    KINEMATIC = "kinematic"
}
export interface PhysicsBodyOptions {
    /** 物理体类型 */
    type?: BodyType;
    /** 质量 */
    mass?: number;
    /** 是否固定旋转 */
    fixedRotation?: boolean;
    /** 线性阻尼 */
    linearDamping?: number;
    /** 角阻尼 */
    angularDamping?: number;
    /** 是否允许休眠 */
    allowSleep?: boolean;
    /** 是否触发器 */
    isTrigger?: boolean;
    /** 碰撞组 */
    collisionGroup?: number;
    /** 碰撞掩码 */
    collisionMask?: number;
    /** 材质属性 */
    material?: {
        /** 摩擦系数 */
        friction?: number;
        /** 恢复系数 */
        restitution?: number;
    };
}
export declare class PhysicsBody extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** CANNON.js物理体 */
    private body;
    /** 物理体类型 */
    private bodyType;
    /** 质量 */
    private mass;
    /** 是否固定旋转 */
    private fixedRotation;
    /** 线性阻尼 */
    private linearDamping;
    /** 角阻尼 */
    private angularDamping;
    /** 是否允许休眠 */
    private allowSleep;
    /** 碰撞组 */
    private collisionGroup;
    /** 碰撞掩码 */
    private collisionMask;
    /** 物理材质 */
    private physicsMaterial;
    /** 物理世界 */
    private world;
    /** 是否已初始化 */
    private initialized;
    /** 碰撞回调 */
    private collisionCallbacks;
    /**
     * 创建物理体组件
     * @param options 物理体选项
     */
    constructor(options?: PhysicsBodyOptions);
    /**
     * 初始化物理体
     * @param world 物理世界
     */
    initialize(world: CANNON.World): void;
    /**
     * 获取CANNON.js物理体类型
     * @returns CANNON.js物理体类型
     */
    private getCannonBodyType;
    /**
     * 更新实体变换
     */
    updateTransform(): void;
    /**
     * 应用力（重载方法，支持分量参数）
     * @param x X分量或力向量
     * @param y Y分量
     * @param z Z分量
     */
    applyForce(x: number, y: number, z: number): void;
    applyForce(force: {
        x: number;
        y: number;
        z: number;
    }, worldPoint?: {
        x: number;
        y: number;
        z: number;
    }): void;
    /**
     * 应用冲量（重载方法，支持分量参数）
     * @param x X分量或冲量向量
     * @param y Y分量
     * @param z Z分量
     */
    applyImpulse(x: number, y: number, z: number): void;
    applyImpulse(impulse: {
        x: number;
        y: number;
        z: number;
    }, worldPoint?: {
        x: number;
        y: number;
        z: number;
    }): void;
    /**
     * 应用局部力（重载方法，支持分量参数）
     * @param x X分量或力向量
     * @param y Y分量
     * @param z Z分量
     */
    applyLocalForce(x: number, y: number, z: number): void;
    applyLocalForce(force: {
        x: number;
        y: number;
        z: number;
    }, localPoint?: {
        x: number;
        y: number;
        z: number;
    }): void;
    /**
     * 应用局部冲量（重载方法，支持分量参数）
     * @param x X分量或冲量向量
     * @param y Y分量
     * @param z Z分量
     */
    applyLocalImpulse(x: number, y: number, z: number): void;
    applyLocalImpulse(impulse: {
        x: number;
        y: number;
        z: number;
    }, localPoint?: {
        x: number;
        y: number;
        z: number;
    }): void;
    /**
     * 应用扭矩
     * @param x X分量或扭矩向量
     * @param y Y分量
     * @param z Z分量
     */
    applyTorque(x: number, y: number, z: number): void;
    applyTorque(torque: {
        x: number;
        y: number;
        z: number;
    }): void;
    /**
     * 设置线性速度
     * @param velocity 速度向量
     */
    setLinearVelocity(velocity: {
        x: number;
        y: number;
        z: number;
    }): void;
    /**
     * 获取线性速度
     * @returns 速度向量
     */
    getLinearVelocity(): {
        x: number;
        y: number;
        z: number;
    };
    /**
     * 设置角速度
     * @param velocity 角速度向量
     */
    setAngularVelocity(velocity: {
        x: number;
        y: number;
        z: number;
    }): void;
    /**
     * 获取角速度
     * @returns 角速度向量
     */
    getAngularVelocity(): {
        x: number;
        y: number;
        z: number;
    };
    /**
     * 获取速度（getLinearVelocity的别名）
     * @returns 速度向量
     */
    getVelocity(): THREE.Vector3;
    /**
     * 设置位置
     * @param position 位置向量
     */
    setPosition(position: {
        x: number;
        y: number;
        z: number;
    }): void;
    /**
     * 获取位置
     * @returns 位置向量
     */
    getPosition(): {
        x: number;
        y: number;
        z: number;
    };
    /**
     * 获取角速度（返回THREE.Vector3类型）
     * @returns 角速度向量
     */
    getAngularVelocityVector3(): THREE.Vector3;
    /**
     * 设置位置（接受THREE.Vector3类型）
     * @param position 位置向量
     */
    setPositionVector3(position: THREE.Vector3): void;
    /**
     * 获取位置（返回THREE.Vector3类型）
     * @returns 位置向量
     */
    getPositionVector3(): THREE.Vector3;
    /**
     * 设置碰撞回调
     * @param callbacks 碰撞回调函数
     */
    setCollisionCallbacks(callbacks: {
        start?: (entity: Entity, contact: any) => void;
        stay?: (entity: Entity, contact: any) => void;
        end?: (entity: Entity, contact: any) => void;
        triggerEnter?: (entity: Entity, contact: any) => void;
        triggerStay?: (entity: Entity, contact: any) => void;
        triggerExit?: (entity: Entity, contact: any) => void;
    }): void;
    /**
     * 碰撞开始回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    onCollisionStart(otherEntity: Entity, contact: any): void;
    /**
     * 碰撞持续回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    onCollisionStay(otherEntity: Entity, contact: any): void;
    /**
     * 碰撞结束回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    onCollisionEnd(otherEntity: Entity, contact: any): void;
    /**
     * 触发器进入回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    onTriggerEnter(otherEntity: Entity, contact: any): void;
    /**
     * 触发器停留回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    onTriggerStay(otherEntity: Entity, contact: any): void;
    /**
     * 触发器离开回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    onTriggerExit(otherEntity: Entity, contact: any): void;
    /**
     * 获取CANNON.js物理体
     * @returns CANNON.js物理体
     */
    getCannonBody(): CANNON.Body | null;
    /**
     * 获取物理体类型
     * @returns 物理体类型
     */
    getBodyType(): BodyType;
    /**
     * 设置物理体类型
     * @param type 物理体类型
     */
    setBodyType(type: BodyType): void;
    /**
     * 销毁物理体
     */
    dispose(): void;
}
