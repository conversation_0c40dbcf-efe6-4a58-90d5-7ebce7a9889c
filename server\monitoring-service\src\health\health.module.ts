import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { TerminusModule } from '@nestjs/terminus';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { HealthCheckEntity } from './entities/health-check.entity';
import { HealthCheckHistoryEntity } from './entities/health-check-history.entity';
import { AutoRecoveryService } from './auto-recovery.service';
import { ServiceHealthCheckService } from './service-health-check.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      HealthCheckEntity,
      HealthCheckHistoryEntity,
    ]),
    HttpModule,
    TerminusModule,
  ],
  controllers: [HealthController],
  providers: [
    HealthService,
    AutoRecoveryService,
    ServiceHealthCheckService,
  ],
  exports: [
    HealthService,
    AutoRecoveryService,
    ServiceHealthCheckService,
  ],
})
export class HealthModule {}
