/**
 * 地形虚拟纹理
 * 实现虚拟纹理技术，用于高效管理大型地形纹理
 */
import * as THREE from 'three';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { TerrainTextureCompression, TextureCompressionOptions, TextureCompressionFormat } from './TerrainTextureCompression';
import { TerrainComponent } from '../components/TerrainComponent';

/**
 * 虚拟纹理事件类型
 */
export enum VirtualTextureEventType {
  /** 页面加载 */
  PAGE_LOADED = 'page_loaded',
  /** 页面卸载 */
  PAGE_UNLOADED = 'page_unloaded',
  /** 反馈纹理更新 */
  FEEDBACK_TEXTURE_UPDATED = 'feedback_texture_updated',
  /** 内存使用变化 */
  MEMORY_USAGE_CHANGED = 'memory_usage_changed',
  /** 缓存状态变化 */
  CACHE_STATE_CHANGED = 'cache_state_changed',
  /** 错误 */
  ERROR = 'error'
}

/**
 * 虚拟纹理配置
 */
export interface VirtualTextureConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 物理纹理大小 */
  physicalTextureSize?: number;
  /** 页面大小 */
  pageSize?: number;
  /** 页面边框大小 */
  pageBorderSize?: number;
  /** 最大内存使用量（MB） */
  maxMemoryUsage?: number;
  /** 最大页面数量 */
  maxPageCount?: number;
  /** 最大MIP级别 */
  maxMipLevels?: number;
  /** 是否使用纹理压缩 */
  useCompression?: boolean;
  /** 纹理压缩选项 */
  compressionOptions?: TextureCompressionOptions;
  /** 是否使用GPU加速 */
  useGPUAcceleration?: boolean;
  /** 是否使用预测加载 */
  usePredictiveLoading?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 是否启用日志 */
  debug?: boolean;
}

/**
 * 虚拟纹理页面
 */
export interface VirtualTexturePage {
  /** 页面ID */
  id: string;
  /** MIP级别 */
  mipLevel: number;
  /** X坐标 */
  x: number;
  /** Y坐标 */
  y: number;
  /** 纹理 */
  texture: THREE.Texture | null;
  /** 是否已加载 */
  loaded: boolean;
  /** 最后使用时间 */
  lastUsedTime: number;
  /** 优先级 */
  priority: number;
  /** 大小（字节） */
  size: number;
}

/**
 * 地形虚拟纹理类
 */
export class TerrainVirtualTexturing {
  /** 是否启用 */
  private enabled: boolean;
  /** 物理纹理大小 */
  private physicalTextureSize: number;
  /** 页面大小 */
  private pageSize: number;
  /** 页面边框大小 */
  private pageBorderSize: number;
  /** 最大内存使用量（字节） */
  private maxMemoryUsage: number;
  /** 最大页面数量 */
  private maxPageCount: number;
  /** 最大MIP级别 */
  private maxMipLevels: number;
  /** 是否使用纹理压缩 */
  private useCompression: boolean;
  /** 纹理压缩选项 */
  private compressionOptions: TextureCompressionOptions;
  /** 是否使用GPU加速 */
  private useGPUAcceleration: boolean;
  /** 是否使用预测加载 */
  private usePredictiveLoading: boolean;
  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;
  /** 是否启用日志 */
  private debug: boolean;

  /** 物理纹理 */
  private physicalTexture: THREE.DataTexture | null;
  /** 反馈纹理 */
  private feedbackTexture: THREE.WebGLRenderTarget | null;
  /** 反馈材质 */
  private feedbackMaterial: THREE.ShaderMaterial | null;
  /** 页面表 */
  private pageTable: Map<string, VirtualTexturePage>;
  /** 页面表纹理 */
  private pageTableTexture: THREE.DataTexture | null;
  /** 页面缓存 */
  private pageCache: Map<string, VirtualTexturePage>;
  /** 页面加载队列 */
  private pageLoadQueue: string[];
  /** 当前内存使用量（字节） */
  private currentMemoryUsage: number;
  /** 纹理压缩 */
  private textureCompression: TerrainTextureCompression;
  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null;
  /** 是否初始化 */
  private initialized: boolean;
  /** 源纹理 */
  private sourceTextures: Map<number, THREE.Texture>;

  /**
   * 创建地形虚拟纹理
   * @param config 配置
   */
  constructor(config: VirtualTextureConfig = {}) {
    // 初始化配置
    this.enabled = config.enabled !== undefined ? config.enabled : true;
    this.physicalTextureSize = config.physicalTextureSize || 4096;
    this.pageSize = config.pageSize || 256;
    this.pageBorderSize = config.pageBorderSize || 4;
    this.maxMemoryUsage = (config.maxMemoryUsage || 512) * 1024 * 1024; // 转换为字节
    this.maxPageCount = config.maxPageCount || 1024;
    this.maxMipLevels = config.maxMipLevels || 8;
    this.useCompression = config.useCompression !== undefined ? config.useCompression : true;
    this.compressionOptions = config.compressionOptions || {
      format: TextureCompressionFormat.KTX2,
      quality: 0.8,
      generateMipmaps: false
    };
    this.useGPUAcceleration = config.useGPUAcceleration !== undefined ? config.useGPUAcceleration : true;
    this.usePredictiveLoading = config.usePredictiveLoading !== undefined ? config.usePredictiveLoading : true;
    this.useDebugVisualization = config.useDebugVisualization !== undefined ? config.useDebugVisualization : false;
    this.debug = config.debug !== undefined ? config.debug : false;

    // 初始化属性
    this.physicalTexture = null;
    this.feedbackTexture = null;
    this.feedbackMaterial = null;
    this.pageTable = new Map();
    this.pageTableTexture = null;
    this.pageCache = new Map();
    this.pageLoadQueue = [];
    this.currentMemoryUsage = 0;
    this.textureCompression = new TerrainTextureCompression(this.compressionOptions);
    this.eventEmitter = new EventEmitter();
    this.renderer = null;
    this.initialized = false;
    this.sourceTextures = new Map();

    // 如果启用，则初始化
    if (this.enabled) {
      this.initialize();
    }
  }

  /**
   * 初始化
   */
  private initialize(): void {
    if (this.initialized) {
      return;
    }

    try {
      // 创建物理纹理
      this.createPhysicalTexture();

      // 创建反馈纹理
      this.createFeedbackTexture();

      // 创建反馈材质
      this.createFeedbackMaterial();

      this.initialized = true;
      Debug.log('TerrainVirtualTexturing', '初始化成功');
    } catch (error) {
      Debug.error('TerrainVirtualTexturing', '初始化失败:', error);
      this.eventEmitter.emit(VirtualTextureEventType.ERROR, '初始化失败', error);
    }
  }

  /**
   * 创建物理纹理
   */
  private createPhysicalTexture(): void {
    // 创建空数据
    const data = new Uint8Array(this.physicalTextureSize * this.physicalTextureSize * 4);

    // 创建数据纹理
    this.physicalTexture = new THREE.DataTexture(
      data,
      this.physicalTextureSize,
      this.physicalTextureSize,
      THREE.RGBAFormat,
      THREE.UnsignedByteType,
      THREE.UVMapping,
      THREE.ClampToEdgeWrapping,
      THREE.ClampToEdgeWrapping,
      THREE.LinearFilter,
      THREE.LinearFilter,
      1
    );

    this.physicalTexture.generateMipmaps = false;
    this.physicalTexture.needsUpdate = true;
  }

  /**
   * 创建反馈纹理
   */
  private createFeedbackTexture(): void {
    // 创建渲染目标
    this.feedbackTexture = new THREE.WebGLRenderTarget(
      this.physicalTextureSize,
      this.physicalTextureSize,
      {
        format: THREE.RGBAFormat,
        type: THREE.UnsignedByteType,
        minFilter: THREE.NearestFilter,
        magFilter: THREE.NearestFilter,
        generateMipmaps: false
      }
    );
  }

  /**
   * 创建反馈材质
   */
  private createFeedbackMaterial(): void {
    // 创建着色器材质
    this.feedbackMaterial = new THREE.ShaderMaterial({
      uniforms: {
        uPhysicalTexture: { value: this.physicalTexture },
        uPageSize: { value: this.pageSize },
        uPhysicalTextureSize: { value: this.physicalTextureSize },
        uMaxMipLevel: { value: this.maxMipLevels }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform sampler2D uPhysicalTexture;
        uniform float uPageSize;
        uniform float uPhysicalTextureSize;
        uniform float uMaxMipLevel;
        varying vec2 vUv;

        void main() {
          // 计算所需的MIP级别
          float mipLevel = 0.0;
          // TODO: 实现MIP级别计算

          // 计算页面坐标
          float pageX = floor(vUv.x * uPhysicalTextureSize / uPageSize);
          float pageY = floor(vUv.y * uPhysicalTextureSize / uPageSize);

          // 输出页面请求信息
          gl_FragColor = vec4(pageX / 255.0, pageY / 255.0, mipLevel / uMaxMipLevel, 1.0);
        }
      `
    });
  }

  /**
   * 设置渲染器
   * @param renderer 渲染器
   */
  public setRenderer(renderer: THREE.WebGLRenderer): void {
    this.renderer = renderer;
  }

  /**
   * 更新
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.initialized) {
      return;
    }

    // 处理页面加载队列
    this.processPageLoadQueue();

    // 更新页面缓存
    this.updatePageCache();
  }

  /**
   * 处理页面加载队列
   */
  private processPageLoadQueue(): void {
    // 如果队列为空，则返回
    if (this.pageLoadQueue.length === 0) {
      return;
    }

    // 获取队列中的第一个页面ID
    const pageId = this.pageLoadQueue.shift();
    if (!pageId) {
      return;
    }

    // 获取页面
    const page = this.pageTable.get(pageId);
    if (!page) {
      return;
    }

    // 如果页面已加载，则返回
    if (page.loaded) {
      return;
    }

    // 加载页面
    this.loadPage(page);
  }

  /**
   * 加载页面
   * @param page 页面
   */
  private loadPage(page: VirtualTexturePage): void {
    if (!page || page.loaded) {
      return;
    }

    // 获取页面ID的组成部分
    const [mipLevel, x, y] = page.id.split('_').map(Number);

    // 计算页面在源纹理中的位置
    const mipScale = Math.pow(2, mipLevel);
    const sourceSize = this.physicalTextureSize * mipScale;
    const pageSize = this.pageSize;
    const pageBorderSize = this.pageBorderSize;

    // 计算源纹理中的区域
    const sourceX = x * pageSize;
    const sourceY = y * pageSize;
    const sourceWidth = Math.min(pageSize, sourceSize - sourceX);
    const sourceHeight = Math.min(pageSize, sourceSize - sourceY);

    // 如果页面超出源纹理范围，则跳过
    if (sourceX >= sourceSize || sourceY >= sourceSize || sourceWidth <= 0 || sourceHeight <= 0) {
      Debug.warn('TerrainVirtualTexturing', `页面超出源纹理范围: ${page.id}`);
      return;
    }

    // 创建临时画布
    const canvas = document.createElement('canvas');
    canvas.width = pageSize + pageBorderSize * 2;
    canvas.height = pageSize + pageBorderSize * 2;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      Debug.error('TerrainVirtualTexturing', '无法创建2D上下文');
      return;
    }

    // 从源纹理中提取数据
    // 这里假设我们有一个源纹理，实际实现中需要根据具体情况获取源纹理
    const sourceTexture = this.getSourceTextureForMipLevel(mipLevel);
    if (!sourceTexture || !sourceTexture.image) {
      Debug.warn('TerrainVirtualTexturing', `无法获取源纹理: ${mipLevel}`);
      return;
    }

    // 绘制页面内容（包括边框）
    try {
      // 绘制中心区域
      ctx.drawImage(
        sourceTexture.image,
        sourceX, sourceY, sourceWidth, sourceHeight,
        pageBorderSize, pageBorderSize, sourceWidth, sourceHeight
      );

      // 处理边框 - 复制边缘像素以避免过滤伪影
      // 左边框
      if (sourceX > 0) {
        ctx.drawImage(
          sourceTexture.image,
          sourceX - 1, sourceY, 1, sourceHeight,
          pageBorderSize - 1, pageBorderSize, 1, sourceHeight
        );
      }

      // 右边框
      if (sourceX + sourceWidth < sourceSize) {
        ctx.drawImage(
          sourceTexture.image,
          sourceX + sourceWidth, sourceY, 1, sourceHeight,
          pageBorderSize + sourceWidth, pageBorderSize, 1, sourceHeight
        );
      }

      // 上边框
      if (sourceY > 0) {
        ctx.drawImage(
          sourceTexture.image,
          sourceX, sourceY - 1, sourceWidth, 1,
          pageBorderSize, pageBorderSize - 1, sourceWidth, 1
        );
      }

      // 下边框
      if (sourceY + sourceHeight < sourceSize) {
        ctx.drawImage(
          sourceTexture.image,
          sourceX, sourceY + sourceHeight, sourceWidth, 1,
          pageBorderSize, pageBorderSize + sourceHeight, sourceWidth, 1
        );
      }

      // 创建纹理
      const texture = new THREE.CanvasTexture(canvas);
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      texture.generateMipmaps = false;

      // 如果启用压缩，则压缩纹理
      if (this.useCompression && this.textureCompression) {
        this.textureCompression.compressTexture(texture, this.compressionOptions)
          .then(compressedTexture => {
            // 更新页面
            page.texture = compressedTexture;
            this.finalizePageLoad(page);
          })
          .catch(error => {
            Debug.error('TerrainVirtualTexturing', `压缩纹理失败: ${error}`);
            page.texture = texture;
            this.finalizePageLoad(page);
          });
      } else {
        // 不压缩
        page.texture = texture;
        this.finalizePageLoad(page);
      }
    } catch (error) {
      Debug.error('TerrainVirtualTexturing', `加载页面失败: ${error}`);
      this.eventEmitter.emit(VirtualTextureEventType.ERROR, `加载页面失败: ${error}`);
    }
  }

  /**
   * 完成页面加载
   * @param page 页面
   */
  private finalizePageLoad(page: VirtualTexturePage): void {
    // 更新页面状态
    page.loaded = true;
    page.lastUsedTime = Date.now();

    // 计算页面大小（字节）
    if (page.texture) {
      page.size = this.calculateTextureSize(page.texture);

      // 更新内存使用量
      this.currentMemoryUsage += page.size;

      // 更新物理纹理
      this.updatePhysicalTexture(page);

      // 添加到缓存
      this.pageCache.set(page.id, page);

      // 发出内存使用变化事件
      this.eventEmitter.emit(VirtualTextureEventType.MEMORY_USAGE_CHANGED, this.currentMemoryUsage);
    }

    // 发出页面加载事件
    this.eventEmitter.emit(VirtualTextureEventType.PAGE_LOADED, page.id);
  }

  /**
   * 更新物理纹理
   * @param page 页面
   */
  private updatePhysicalTexture(page: VirtualTexturePage): void {
    if (!this.physicalTexture || !page.texture || !page.texture.image) {
      return;
    }

    // 获取页面ID的组成部分
    const [mipLevel, x, y] = page.id.split('_').map(Number);

    // 计算页面在物理纹理中的位置
    const pageSize = this.pageSize;
    const pageBorderSize = this.pageBorderSize;
    const effectivePageSize = pageSize + pageBorderSize * 2;

    // 计算物理纹理中的目标区域
    const targetX = x * pageSize;
    const targetY = y * pageSize;

    // 创建临时画布以获取像素数据
    const canvas = document.createElement('canvas');
    canvas.width = effectivePageSize;
    canvas.height = effectivePageSize;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      Debug.error('TerrainVirtualTexturing', '无法创建2D上下文');
      return;
    }

    // 绘制页面纹理到画布
    ctx.drawImage(page.texture.image, 0, 0, effectivePageSize, effectivePageSize);

    // 获取像素数据
    const imageData = ctx.getImageData(0, 0, effectivePageSize, effectivePageSize);
    const pixels = imageData.data;

    // 获取物理纹理数据
    const physicalTextureData = this.physicalTexture.image;
    const physicalTextureWidth = this.physicalTextureSize;

    // 复制像素数据到物理纹理
    for (let py = 0; py < effectivePageSize; py++) {
      for (let px = 0; px < effectivePageSize; px++) {
        // 源索引
        const sourceIndex = (py * effectivePageSize + px) * 4;

        // 目标位置（考虑边框）
        const tx = targetX + px - pageBorderSize;
        const ty = targetY + py - pageBorderSize;

        // 检查是否在物理纹理范围内
        if (tx >= 0 && tx < physicalTextureWidth && ty >= 0 && ty < physicalTextureWidth) {
          // 目标索引
          const targetIndex = (ty * physicalTextureWidth + tx) * 4;

          // 复制RGBA值
          physicalTextureData[targetIndex] = pixels[sourceIndex];         // R
          physicalTextureData[targetIndex + 1] = pixels[sourceIndex + 1]; // G
          physicalTextureData[targetIndex + 2] = pixels[sourceIndex + 2]; // B
          physicalTextureData[targetIndex + 3] = pixels[sourceIndex + 3]; // A
        }
      }
    }

    // 更新页面表
    this.updatePageTable(page);

    // 标记纹理需要更新
    this.physicalTexture.needsUpdate = true;

    if (this.debug) {
      Debug.log('TerrainVirtualTexturing', `更新物理纹理: ${page.id}`);
    }
  }

  /**
   * 更新页面表
   * @param page 页面
   */
  private updatePageTable(page: VirtualTexturePage): void {
    if (!this.pageTableTexture) {
      this.createPageTableTexture();
    }

    if (!this.pageTableTexture) {
      return;
    }

    // 获取页面ID的组成部分
    const [mipLevel, x, y] = page.id.split('_').map(Number);

    // 获取页面表纹理数据
    const pageTableData = this.pageTableTexture.image;
    const pageTableSize = Math.ceil(this.physicalTextureSize / this.pageSize);

    // 计算页面表中的索引
    const index = (y * pageTableSize + x) * 4;

    // 更新页面表数据
    // R: 页面X坐标
    // G: 页面Y坐标
    // B: MIP级别
    // A: 是否有效 (1 = 有效, 0 = 无效)
    pageTableData[index] = x;
    pageTableData[index + 1] = y;
    pageTableData[index + 2] = mipLevel;
    pageTableData[index + 3] = 255; // 有效

    // 标记纹理需要更新
    this.pageTableTexture.needsUpdate = true;
  }

  /**
   * 创建页面表纹理
   */
  private createPageTableTexture(): void {
    // 计算页面表大小
    const pageTableSize = Math.ceil(this.physicalTextureSize / this.pageSize);

    // 创建页面表数据
    const pageTableData = new Uint8Array(pageTableSize * pageTableSize * 4);

    // 初始化为无效
    for (let i = 0; i < pageTableData.length; i += 4) {
      pageTableData[i] = 0;     // X
      pageTableData[i + 1] = 0; // Y
      pageTableData[i + 2] = 0; // MIP
      pageTableData[i + 3] = 0; // 无效
    }

    // 创建数据纹理
    this.pageTableTexture = new THREE.DataTexture(
      pageTableData,
      pageTableSize,
      pageTableSize,
      THREE.RGBAFormat,
      THREE.UnsignedByteType,
      THREE.UVMapping,
      THREE.ClampToEdgeWrapping,
      THREE.ClampToEdgeWrapping,
      THREE.NearestFilter,
      THREE.NearestFilter,
      1
    );

    // 设置纹理属性
    this.pageTableTexture.generateMipmaps = false;
    this.pageTableTexture.needsUpdate = true;

    if (this.debug) {
      Debug.log('TerrainVirtualTexturing', `创建页面表纹理: ${pageTableSize}x${pageTableSize}`);
    }
  }

  /**
   * 更新页面缓存
   */
  private updatePageCache(): void {
    // 检查内存使用情况
    if (this.currentMemoryUsage > this.maxMemoryUsage) {
      this.evictPages();
    }
  }

  /**
   * 驱逐页面
   */
  private evictPages(): void {
    // 按最后使用时间排序
    const sortedPages = Array.from(this.pageCache.values())
      .sort((a, b) => a.lastUsedTime - b.lastUsedTime);

    // 计算需要释放的内存
    const memoryToFree = this.currentMemoryUsage - this.maxMemoryUsage * 0.8; // 释放到80%

    // 释放内存
    let freedMemory = 0;
    for (const page of sortedPages) {
      // 如果已经释放足够的内存，则停止
      if (freedMemory >= memoryToFree) {
        break;
      }

      // 卸载页面
      this.unloadPage(page);

      // 更新已释放的内存
      freedMemory += page.size;
    }
  }

  /**
   * 卸载页面
   * @param page 页面
   */
  private unloadPage(page: VirtualTexturePage): void {
    // 从缓存中移除
    this.pageCache.delete(page.id);

    // 更新页面状态
    page.loaded = false;
    page.texture = null;

    // 更新内存使用量
    this.currentMemoryUsage -= page.size;

    // 发出页面卸载事件
    this.eventEmitter.emit(VirtualTextureEventType.PAGE_UNLOADED, page.id);
  }

  /**
   * 读取反馈纹理
   */
  public readFeedbackTexture(): void {
    if (!this.renderer || !this.feedbackTexture) {
      return;
    }

    // 创建像素缓冲区
    const width = this.feedbackTexture.width;
    const height = this.feedbackTexture.height;
    const buffer = new Uint8Array(width * height * 4);

    // 读取像素
    const gl = this.renderer.getContext();

    // 设置当前渲染目标为反馈纹理
    this.renderer.setRenderTarget(this.feedbackTexture);

    // 读取像素
    gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, buffer);

    // 恢复渲染目标
    this.renderer.setRenderTarget(null);

    // 处理反馈数据
    this.processFeedbackData(buffer, width, height);

    // 发出反馈纹理更新事件
    this.eventEmitter.emit(VirtualTextureEventType.FEEDBACK_TEXTURE_UPDATED);
  }

  /**
   * 处理反馈数据
   * @param data 像素数据
   * @param width 宽度
   * @param height 高度
   */
  private processFeedbackData(data: Uint8Array, width: number, height: number): void {
    // 创建页面请求映射
    const pageRequests = new Map<string, number>();

    // 采样步长（不需要处理每个像素）
    const sampleStride = 4;

    // 遍历像素
    for (let y = 0; y < height; y += sampleStride) {
      for (let x = 0; x < width; x += sampleStride) {
        // 获取像素索引
        const index = (y * width + x) * 4;

        // 解析页面信息
        const pageX = data[index];     // R通道: 页面X坐标
        const pageY = data[index + 1]; // G通道: 页面Y坐标
        const mipLevel = Math.min(Math.floor(data[index + 2] / 255 * this.maxMipLevels), this.maxMipLevels - 1); // B通道: MIP级别
        const valid = data[index + 3] > 0; // A通道: 是否有效

        // 如果无效，则跳过
        if (!valid || (pageX === 0 && pageY === 0 && mipLevel === 0)) {
          continue;
        }

        // 创建页面ID
        const pageId = `${mipLevel}_${pageX}_${pageY}`;

        // 增加页面请求计数
        const count = pageRequests.get(pageId) || 0;
        pageRequests.set(pageId, count + 1);
      }
    }

    // 转换为数组并排序
    const sortedRequests = Array.from(pageRequests.entries())
      .sort((a, b) => b[1] - a[1]); // 按请求数量降序排序

    // 清空加载队列
    this.pageLoadQueue = [];

    // 添加到加载队列
    for (const [pageId, count] of sortedRequests) {
      // 检查页面是否已加载
      const page = this.pageTable.get(pageId);

      if (!page) {
        // 创建新页面
        const [mipLevel, x, y] = pageId.split('_').map(Number);

        const newPage: VirtualTexturePage = {
          id: pageId,
          mipLevel,
          x,
          y,
          texture: null,
          loaded: false,
          lastUsedTime: Date.now(),
          priority: count, // 使用请求数量作为优先级
          size: 0
        };

        // 添加到页面表
        this.pageTable.set(pageId, newPage);

        // 添加到加载队列
        this.pageLoadQueue.push(pageId);
      } else if (!page.loaded) {
        // 如果页面存在但未加载，则更新优先级并添加到队列
        page.priority = count;
        page.lastUsedTime = Date.now();
        this.pageLoadQueue.push(pageId);
      } else {
        // 如果页面已加载，则更新使用时间
        page.lastUsedTime = Date.now();
      }
    }

    // 如果启用预测加载，则添加相邻页面
    if (this.usePredictiveLoading && this.pageLoadQueue.length > 0) {
      this.addPredictivePages();
    }

    if (this.debug) {
      Debug.log('TerrainVirtualTexturing', `处理反馈数据: ${sortedRequests.length} 个页面请求`);
    }
  }

  /**
   * 添加预测页面
   */
  private addPredictivePages(): void {
    // 获取当前请求的页面
    const currentRequests = new Set(this.pageLoadQueue);
    const additionalRequests: string[] = [];

    // 遍历当前请求
    currentRequests.forEach(pageId => {
      const [mipLevel, x, y] = pageId.split('_').map(Number);

      // 添加相邻页面
      const neighbors = [
        `${mipLevel}_${x+1}_${y}`,
        `${mipLevel}_${x-1}_${y}`,
        `${mipLevel}_${x}_${y+1}`,
        `${mipLevel}_${x}_${y-1}`
      ];

      for (const neighborId of neighbors) {
        // 检查是否已在请求列表中
        if (currentRequests.has(neighborId)) {
          continue;
        }

        // 检查页面是否已加载
        const page = this.pageTable.get(neighborId);

        if (!page) {
          // 解析页面ID
          const [nMipLevel, nX, nY] = neighborId.split('_').map(Number);

          // 检查坐标是否有效
          if (nX < 0 || nY < 0) {
            continue;
          }

          // 创建新页面
          const newPage: VirtualTexturePage = {
            id: neighborId,
            mipLevel: nMipLevel,
            x: nX,
            y: nY,
            texture: null,
            loaded: false,
            lastUsedTime: Date.now(),
            priority: 0, // 低优先级
            size: 0
          };

          // 添加到页面表
          this.pageTable.set(neighborId, newPage);

          // 添加到额外请求
          additionalRequests.push(neighborId);
        } else if (!page.loaded) {
          // 如果页面存在但未加载，则添加到额外请求
          additionalRequests.push(neighborId);
        }
      }
    });

    // 添加额外请求到加载队列
    this.pageLoadQueue.push(...additionalRequests);

    if (this.debug && additionalRequests.length > 0) {
      Debug.log('TerrainVirtualTexturing', `添加预测页面: ${additionalRequests.length} 个`);
    }
  }

  /**
   * 获取物理纹理
   * @returns 物理纹理
   */
  public getPhysicalTexture(): THREE.Texture | null {
    return this.physicalTexture;
  }

  /**
   * 获取反馈纹理
   * @returns 反馈纹理
   */
  public getFeedbackTexture(): THREE.WebGLRenderTarget | null {
    return this.feedbackTexture;
  }

  /**
   * 获取反馈材质
   * @returns 反馈材质
   */
  public getFeedbackMaterial(): THREE.ShaderMaterial | null {
    return this.feedbackMaterial;
  }

  /**
   * 注册事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: VirtualTextureEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: VirtualTextureEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 获取指定MIP级别的源纹理
   * @param mipLevel MIP级别
   * @returns 源纹理
   */
  private getSourceTextureForMipLevel(mipLevel: number): THREE.Texture | null {
    // 检查缓存
    if (this.sourceTextures.has(mipLevel)) {
      return this.sourceTextures.get(mipLevel) || null;
    }

    // 如果没有缓存，则返回null
    return null;
  }

  /**
   * 设置源纹理
   * @param texture 纹理
   * @param mipLevel MIP级别
   */
  public setSourceTexture(texture: THREE.Texture, mipLevel: number = 0): void {
    if (!texture) {
      Debug.warn('TerrainVirtualTexturing', '无效的源纹理');
      return;
    }

    // 存储纹理
    this.sourceTextures.set(mipLevel, texture);

    // 如果是基础级别，则创建MIP链
    if (mipLevel === 0 && this.maxMipLevels > 1) {
      this.generateMipChain(texture);
    }

    Debug.log('TerrainVirtualTexturing', `设置源纹理: MIP级别 ${mipLevel}`);
  }

  /**
   * 生成MIP链
   * @param baseTexture 基础纹理
   */
  private generateMipChain(baseTexture: THREE.Texture): void {
    if (!baseTexture || !baseTexture.image) {
      return;
    }

    // 获取基础图像
    const baseImage = baseTexture.image;

    // 为每个MIP级别创建缩小版本
    for (let level = 1; level < this.maxMipLevels; level++) {
      const scale = 1.0 / Math.pow(2, level);
      const width = Math.max(1, Math.floor(baseImage.width * scale));
      const height = Math.max(1, Math.floor(baseImage.height * scale));

      // 创建画布
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        continue;
      }

      // 绘制缩小的图像
      ctx.drawImage(baseImage, 0, 0, width, height);

      // 创建纹理
      const mipTexture = new THREE.CanvasTexture(canvas);
      mipTexture.minFilter = THREE.LinearFilter;
      mipTexture.magFilter = THREE.LinearFilter;
      mipTexture.generateMipmaps = false;

      // 存储MIP级别
      this.sourceTextures.set(level, mipTexture);

      Debug.log('TerrainVirtualTexturing', `生成MIP级别 ${level}: ${width}x${height}`);
    }
  }

  /**
   * 计算纹理大小
   * @param texture 纹理
   * @returns 大小（字节）
   */
  private calculateTextureSize(texture: THREE.Texture | THREE.CompressedTexture): number {
    if (!texture) {
      return 0;
    }

    // 对于压缩纹理
    if (texture instanceof THREE.CompressedTexture) {
      let size = 0;

      // 累加所有mipmap级别的大小
      if (texture.mipmaps && texture.mipmaps.length > 0) {
        for (const mipmap of texture.mipmaps) {
          if (mipmap.data) {
            size += mipmap.data.byteLength;
          }
        }
      }

      return size;
    }

    // 对于普通纹理，估算大小
    if (texture.image) {
      const width = texture.image.width || 0;
      const height = texture.image.height || 0;

      // 假设每个像素4字节（RGBA）
      return width * height * 4;
    }

    return 0;
  }

  /**
   * 应用到地形组件
   * @param component 地形组件
   */
  public applyToTerrainComponent(component: TerrainComponent): void {
    // 检查组件是否有效
    if (!component) {
      Debug.warn('TerrainVirtualTexturing', '无效的地形组件');
      return;
    }

    // 获取地形材质
    const material = component.material;
    if (!material) {
      Debug.warn('TerrainVirtualTexturing', '地形组件没有材质');
      return;
    }

    // 确保物理纹理已创建
    if (!this.physicalTexture) {
      this.createPhysicalTexture();
    }

    // 更新材质的纹理
    // 检查是否为着色器材质
    if (material instanceof THREE.ShaderMaterial && material.uniforms) {
      // 设置物理纹理
      if (material.uniforms.uPhysicalTexture) {
        material.uniforms.uPhysicalTexture.value = this.physicalTexture;
      }

      // 设置页面表纹理
      if (material.uniforms.uPageTable && this.pageTableTexture) {
        material.uniforms.uPageTable.value = this.pageTableTexture;
      }

      // 设置其他参数
      if (material.uniforms.uPageSize) {
        material.uniforms.uPageSize.value = this.pageSize;
      }

      if (material.uniforms.uPageBorderSize) {
        material.uniforms.uPageBorderSize.value = this.pageBorderSize;
      }

      if (material.uniforms.uPhysicalTextureSize) {
        material.uniforms.uPhysicalTextureSize.value = this.physicalTextureSize;
      }

      if (material.uniforms.uMaxMipLevel) {
        material.uniforms.uMaxMipLevel.value = this.maxMipLevels;
      }
    }

    Debug.log('TerrainVirtualTexturing', '应用到地形组件成功');
  }
}
