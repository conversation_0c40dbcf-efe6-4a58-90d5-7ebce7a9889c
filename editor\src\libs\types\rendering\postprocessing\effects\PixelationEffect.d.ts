/**
 * 像素化效果
 * 模拟低分辨率像素风格
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 像素化效果选项
 */
export interface PixelationEffectOptions extends PostProcessingEffectOptions {
    /** 像素大小 */
    pixelSize?: number;
    /** 是否保持纵横比 */
    preserveAspect?: boolean;
    /** 是否使用像素边缘 */
    pixelBorder?: boolean;
    /** 像素边缘颜色 */
    borderColor?: THREE.Color;
    /** 像素边缘宽度 */
    borderWidth?: number;
}
/**
 * 像素化效果
 */
export declare class PixelationEffect extends PostProcessingEffect {
    /** 像素大小 */
    private pixelSize;
    /** 是否保持纵横比 */
    private preserveAspect;
    /** 是否使用像素边缘 */
    private pixelBorder;
    /** 像素边缘颜色 */
    private borderColor;
    /** 像素边缘宽度 */
    private borderWidth;
    /** 像素化通道 */
    private pixelationPass;
    /**
     * 创建像素化效果
     * @param options 像素化效果选项
     */
    constructor(options?: PixelationEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 设置像素大小
     * @param size 像素大小
     */
    setPixelSize(size: number): void;
    /**
     * 获取像素大小
     * @returns 像素大小
     */
    getPixelSize(): number;
    /**
     * 设置是否保持纵横比
     * @param preserve 是否保持纵横比
     */
    setPreserveAspect(preserve: boolean): void;
    /**
     * 获取是否保持纵横比
     * @returns 是否保持纵横比
     */
    isPreserveAspect(): boolean;
    /**
     * 设置是否使用像素边缘
     * @param border 是否使用像素边缘
     */
    setPixelBorder(border: boolean): void;
    /**
     * 获取是否使用像素边缘
     * @returns 是否使用像素边缘
     */
    isPixelBorder(): boolean;
    /**
     * 设置像素边缘颜色
     * @param color 像素边缘颜色
     */
    setBorderColor(color: THREE.Color): void;
    /**
     * 获取像素边缘颜色
     * @returns 像素边缘颜色
     */
    getBorderColor(): THREE.Color;
    /**
     * 设置像素边缘宽度
     * @param width 像素边缘宽度
     */
    setBorderWidth(width: number): void;
    /**
     * 获取像素边缘宽度
     * @returns 像素边缘宽度
     */
    getBorderWidth(): number;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
}
