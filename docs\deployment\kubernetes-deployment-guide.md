# DL引擎 Kubernetes 部署指南

本文档提供了在Kubernetes集群上部署DL（Digital Learning）引擎的完整指南。DL引擎是一个基于微服务架构的3D引擎和编辑器平台，支持大规模并发用户访问。

## 系统概述

DL引擎采用现代微服务架构，包含以下核心组件：

### 基础设施层
- **MySQL 8.0**：主数据存储，支持多数据库分离
- **Redis 7.0**：缓存和会话存储，支持实时协作
- **MinIO**：对象存储服务，用于资产文件管理

### 核心微服务层
- **服务注册中心**：服务发现和注册（端口：3010/4010）
- **API网关**：统一入口，路由和认证（端口：3000）
- **用户服务**：用户管理和认证（端口：3001/4001）
- **项目服务**：项目和场景管理（端口：3002/4002）
- **资产服务**：资产文件管理（端口：3003/4003）
- **渲染服务**：3D渲染和图像处理（端口：3004/4004）
- **协作服务**：实时协作功能（端口：3005-3007）
- **游戏服务器**：游戏实例管理（端口：3030）

### 前端应用层
- **编辑器**：基于React的可视化编辑器（端口：80）

### 监控和运维层
- **监控服务**：系统监控和告警（端口：3100）
- **Prometheus**：指标收集（端口：9090）
- **Grafana**：监控仪表板（端口：3000）
- **ELK Stack**：日志分析（Elasticsearch:9200, Kibana:5601）

## 系统要求

### 硬件要求
- **CPU**：4核心以上（推荐8核心）
- **内存**：16GB以上（推荐32GB）
- **存储**：100GB以上可用空间（推荐SSD）
- **网络**：稳定的网络连接，支持负载均衡

### 软件要求
- **Kubernetes集群**：v1.20+（推荐v1.25+）
- **Helm**：v3.0+
- **kubectl**：命令行工具
- **容器仓库**：Docker Hub、阿里云容器服务、Harbor等
- **持久化存储**：支持ReadWriteOnce和ReadWriteMany的存储类
- **Ingress控制器**：Nginx Ingress、Traefik、Istio等

## 快速部署

### 1. 使用自动化脚本部署

我们提供了自动化部署脚本来简化部署过程：

```bash
# 下载部署脚本
curl -O https://raw.githubusercontent.com/your-repo/dl-engine/main/docs/deployment/k8s-deploy.sh

# 给脚本执行权限
chmod +x k8s-deploy.sh

# 编辑脚本中的容器仓库地址
vim k8s-deploy.sh
# 修改 REGISTRY="your-registry" 为您的实际容器仓库地址

# 执行部署
./k8s-deploy.sh
```

### 2. 手动部署步骤

如果您需要更精细的控制，可以按照以下步骤手动部署：

#### 步骤1：创建命名空间

```bash
kubectl create namespace dl-engine
kubectl create namespace dl-engine-monitoring
```

#### 步骤2：构建和推送镜像

```bash
# 构建所有微服务镜像
cd newsystem

# 设置您的容器仓库地址
export REGISTRY="your-registry"

# 构建并推送所有镜像
docker build -t $REGISTRY/dl-engine-service-registry:latest ./server/service-registry
docker push $REGISTRY/dl-engine-service-registry:latest

docker build -t $REGISTRY/dl-engine-user-service:latest ./server/user-service
docker push $REGISTRY/dl-engine-user-service:latest

docker build -t $REGISTRY/dl-engine-project-service:latest ./server/project-service
docker push $REGISTRY/dl-engine-project-service:latest

docker build -t $REGISTRY/dl-engine-asset-service:latest ./server/asset-service
docker push $REGISTRY/dl-engine-asset-service:latest

docker build -t $REGISTRY/dl-engine-render-service:latest ./server/render-service
docker push $REGISTRY/dl-engine-render-service:latest

docker build -t $REGISTRY/dl-engine-collaboration-service:latest ./server/collaboration-service
docker push $REGISTRY/dl-engine-collaboration-service:latest

docker build -t $REGISTRY/dl-engine-api-gateway:latest ./server/api-gateway
docker push $REGISTRY/dl-engine-api-gateway:latest

docker build -t $REGISTRY/dl-engine-game-server:latest ./server/game-server
docker push $REGISTRY/dl-engine-game-server:latest

docker build -t $REGISTRY/dl-engine-editor:latest ./editor
docker push $REGISTRY/dl-engine-editor:latest

docker build -t $REGISTRY/dl-engine-monitoring-service:latest ./server/monitoring-service
docker push $REGISTRY/dl-engine-monitoring-service:latest
```

#### 步骤3：部署基础设施

```bash
# 添加Helm仓库
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo update

# 部署MySQL
helm install mysql bitnami/mysql \
  --namespace dl-engine \
  --set auth.rootPassword="your-mysql-password" \
  --set auth.database="ir_engine" \
  --set primary.persistence.size="50Gi"

# 部署Redis
helm install redis bitnami/redis \
  --namespace dl-engine \
  --set auth.password="your-redis-password" \
  --set master.persistence.size="10Gi"

# 部署MinIO
helm install minio bitnami/minio \
  --namespace dl-engine \
  --set auth.rootUser="admin" \
  --set auth.rootPassword="your-minio-password" \
  --set persistence.size="100Gi"
```

#### 步骤4：创建配置

```bash
# 创建ConfigMap
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: dl-engine-config
  namespace: dl-engine
data:
  NODE_ENV: "production"
  SERVICE_REGISTRY_HOST: "service-registry"
  SERVICE_REGISTRY_PORT: "3010"
  USER_SERVICE_HOST: "user-service"
  USER_SERVICE_PORT: "3001"
  PROJECT_SERVICE_HOST: "project-service"
  PROJECT_SERVICE_PORT: "3002"
  ASSET_SERVICE_HOST: "asset-service"
  ASSET_SERVICE_PORT: "3003"
  RENDER_SERVICE_HOST: "render-service"
  RENDER_SERVICE_PORT: "3004"
  DB_HOST: "mysql"
  DB_PORT: "3306"
  REDIS_HOST: "redis-master"
  REDIS_PORT: "6379"
  MINIO_HOST: "minio"
  MINIO_PORT: "9000"
EOF

# 创建Secret
kubectl create secret generic dl-engine-secrets \
  --namespace=dl-engine \
  --from-literal=MYSQL_ROOT_PASSWORD="your-mysql-password" \
  --from-literal=JWT_SECRET="your-jwt-secret" \
  --from-literal=REDIS_PASSWORD="your-redis-password" \
  --from-literal=MINIO_ROOT_USER="admin" \
  --from-literal=MINIO_ROOT_PASSWORD="your-minio-password"
```

#### 步骤5：部署微服务

使用提供的YAML文件部署各个微服务：

```bash
# 部署服务注册中心
kubectl apply -f k8s/service-registry.yaml

# 等待服务注册中心启动
kubectl wait --for=condition=ready pod -l app=service-registry -n dl-engine --timeout=300s

# 部署核心微服务
kubectl apply -f k8s/user-service.yaml
kubectl apply -f k8s/project-service.yaml
kubectl apply -f k8s/asset-service.yaml
kubectl apply -f k8s/render-service.yaml

# 部署协作服务
kubectl apply -f k8s/collaboration-service.yaml

# 部署API网关
kubectl apply -f k8s/api-gateway.yaml

# 部署前端编辑器
kubectl apply -f k8s/editor.yaml

# 部署游戏服务器（可选）
kubectl apply -f k8s/game-server.yaml
```

#### 步骤6：配置Ingress

```bash
kubectl apply -f - <<EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dl-engine-ingress
  namespace: dl-engine
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
spec:
  rules:
  - host: dl-engine.example.com  # 替换为您的域名
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 3000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: editor
            port:
              number: 80
EOF
```

## 验证部署

```bash
# 检查所有Pod状态
kubectl get pods -n dl-engine

# 检查服务状态
kubectl get svc -n dl-engine

# 检查Ingress状态
kubectl get ingress -n dl-engine

# 查看日志
kubectl logs -f deployment/api-gateway -n dl-engine
```

## 访问应用

部署完成后，您可以通过以下方式访问应用：

- **编辑器界面**：http://dl-engine.example.com
- **API文档**：http://dl-engine.example.com/api/docs
- **MinIO控制台**：http://dl-engine.example.com:9001

## 监控部署

```bash
# 部署Prometheus和Grafana
helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace dl-engine-monitoring \
  --set grafana.adminPassword="your-grafana-password"

# 访问Grafana
kubectl port-forward svc/prometheus-grafana 3000:80 -n dl-engine-monitoring
```

## 故障排除

### 常见问题

1. **Pod无法启动**
   ```bash
   kubectl describe pod <pod-name> -n dl-engine
   kubectl logs <pod-name> -n dl-engine
   ```

2. **服务无法访问**
   ```bash
   kubectl get endpoints -n dl-engine
   kubectl describe svc <service-name> -n dl-engine
   ```

3. **存储问题**
   ```bash
   kubectl get pvc -n dl-engine
   kubectl describe pvc <pvc-name> -n dl-engine
   ```

### 清理部署

```bash
# 删除所有资源
kubectl delete namespace dl-engine
kubectl delete namespace dl-engine-monitoring

# 删除Helm发布
helm uninstall mysql -n dl-engine
helm uninstall redis -n dl-engine
helm uninstall minio -n dl-engine
helm uninstall prometheus -n dl-engine-monitoring
```

## 生产环境建议

1. **高可用性**：部署多个副本，配置反亲和性
2. **资源限制**：为所有容器设置合适的资源请求和限制
3. **安全性**：使用RBAC，网络策略，Pod安全策略
4. **备份**：定期备份数据库和持久化存储
5. **监控**：部署完整的监控和告警系统
6. **日志**：配置集中化日志收集和分析

## 相关文档

- [Docker Compose部署指南](./docker-compose.md)
- [API文档](../api/README.md)
- [开发者指南](../developer/README.md)
- [用户手册](../user-manual/README.md)

## 技术支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 联系技术支持团队
4. 参考官方文档和社区论坛
