/**
 * 瀑布组件（简化版）
 * 用于表示瀑布及其基本物理属性
 */
import * as THREE from 'three';
import { WaterBodyComponent } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
/**
 * 瀑布类型
 */
export declare enum WaterfallType {
    /** 高瀑布 */
    HIGH = "high",
    /** 宽瀑布 */
    WIDE = "wide",
    /** 小瀑布 */
    SMALL = "small"
}
/**
 * 瀑布配置接口
 */
export interface WaterfallConfig {
    /** 瀑布类型 */
    type?: WaterfallType;
    /** 宽度 */
    width?: number;
    /** 高度 */
    height?: number;
    /** 深度 */
    depth?: number;
    /** 位置 */
    position?: THREE.Vector3;
    /** 旋转 */
    rotation?: THREE.Euler;
    /** 颜色 */
    color?: THREE.Color;
    /** 透明度 */
    opacity?: number;
    /** 流速 */
    flowSpeed?: number;
    /** 流向 */
    flowDirection?: {
        x: number;
        y: number;
        z: number;
    };
    /** 湍流强度 */
    turbulenceStrength?: number;
    /** 湍流频率 */
    turbulenceFrequency?: number;
    /** 湍流速度 */
    turbulenceSpeed?: number;
}
/**
 * 瀑布组件类
 * 继承自水体组件，添加瀑布特有的功能
 */
export declare class WaterfallComponent extends WaterBodyComponent {
    /** 瀑布类型 */
    private waterfallType;
    /** 水流路径点 */
    private flowPathPoints;
    /** 水流网格 */
    private flowMesh;
    /** 湍流强度 */
    private turbulenceStrength;
    /** 湍流频率 */
    private turbulenceFrequency;
    /** 湍流速度 */
    private turbulenceSpeed;
    /**
     * 创建瀑布组件
     * @param entity 实体
     * @param config 瀑布配置
     */
    constructor(entity: Entity, config?: WaterfallConfig);
    /**
     * 获取瀑布类型
     * @returns 瀑布类型
     */
    getWaterfallType(): WaterfallType;
    /**
     * 设置瀑布类型
     * @param type 瀑布类型
     */
    setWaterfallType(type: WaterfallType): void;
    /**
     * 应用配置
     * @param config 瀑布配置
     */
    private applyConfig;
    /**
     * 初始化瀑布组件
     */
    initialize(): void;
    /**
     * 计算水流路径
     * 根据瀑布的位置、旋转和尺寸计算水流的路径点
     */
    private calculateFlowPath;
    /**
     * 创建水流网格
     * 根据水流路径创建水流的网格
     */
    private createFlowMesh;
    /**
     * 更新瀑布组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新水流动力学
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateFluidDynamics;
    /**
     * 获取水流网格
     * @returns 水流网格
     */
    getFlowMesh(): THREE.Mesh | null;
    /**
     * 获取水流路径点
     * @returns 水流路径点数组
     */
    getFlowPathPoints(): THREE.Vector3[];
    /**
     * 获取湍流强度
     * @returns 湍流强度
     */
    getTurbulenceStrength(): number;
    /**
     * 设置湍流强度
     * @param strength 湍流强度
     */
    setTurbulenceStrength(strength: number): void;
    /**
     * 获取湍流频率
     * @returns 湍流频率
     */
    getTurbulenceFrequency(): number;
    /**
     * 设置湍流频率
     * @param frequency 湍流频率
     */
    setTurbulenceFrequency(frequency: number): void;
    /**
     * 获取湍流速度
     * @returns 湍流速度
     */
    getTurbulenceSpeed(): number;
    /**
     * 设置湍流速度
     * @param speed 湍流速度
     */
    setTurbulenceSpeed(speed: number): void;
}
