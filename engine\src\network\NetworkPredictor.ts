/**
 * 网络预测器
 * 用于减少网络延迟影响，提供位置、旋转和其他属性的预测功能
 */
import * as THREE from 'three';

/**
 * 预测算法类型
 */
export enum PredictionAlgorithm {
  /** 线性预测 */
  LINEAR = 'linear',
  /** 二阶预测 */
  QUADRATIC = 'quadratic',
  /** 卡尔曼滤波 */
  KALMAN = 'kalman',
  /** 自适应预测 */
  ADAPTIVE = 'adaptive',
}

/**
 * 预测配置接口
 */
export interface PredictionConfig {
  /** 预测算法 */
  algorithm?: PredictionAlgorithm;
  /** 最大预测时间（毫秒） */
  maxPredictionTime?: number;
  /** 是否使用平滑 */
  useSmoothing?: boolean;
  /** 平滑因子（0-1） */
  smoothingFactor?: number;
  /** 是否使用自适应预测 */
  useAdaptivePrediction?: boolean;
  /** 是否使用抖动缓冲 */
  useJitterBuffer?: boolean;
  /** 抖动缓冲大小（毫秒） */
  jitterBufferSize?: number;
}

/**
 * 预测状态接口
 */
export interface PredictionState {
  /** 位置 */
  position: THREE.Vector3;
  /** 旋转 */
  rotation: THREE.Quaternion;
  /** 速度 */
  velocity: THREE.Vector3;
  /** 角速度 */
  angularVelocity: THREE.Vector3;
  /** 加速度 */
  acceleration: THREE.Vector3;
  /** 角加速度 */
  angularAcceleration: THREE.Vector3;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 卡尔曼滤波器状态
 */
interface KalmanState {
  /** 状态向量 */
  x: number[];
  /** 协方差矩阵 */
  P: number[][];
  /** 过程噪声 */
  Q: number[][];
  /** 测量噪声 */
  R: number[][];
  /** 状态转移矩阵 */
  F: number[][];
  /** 测量矩阵 */
  H: number[][];
  /** 初始化标志 */
  initialized: boolean;
}

/**
 * 网络预测器类
 */
export class NetworkPredictor {
  /** 配置 */
  private config: Required<PredictionConfig>;

  /** 历史状态 */
  private history: PredictionState[] = [];

  /** 最大历史记录数 */
  private readonly MAX_HISTORY_SIZE = 10;

  /** 卡尔曼滤波器状态 - 位置 */
  private kalmanPosition: KalmanState;

  /** 卡尔曼滤波器状态 - 旋转 */
  private kalmanRotation: KalmanState;

  /**
   * 创建网络预测器
   * @param config 预测配置
   */
  constructor(config: PredictionConfig = {}) {
    // 默认配置
    this.config = {
      algorithm: PredictionAlgorithm.ADAPTIVE,
      maxPredictionTime: 500, // 最大预测500毫秒
      useSmoothing: true,
      smoothingFactor: 0.3,
      useAdaptivePrediction: true,
      useJitterBuffer: true,
      jitterBufferSize: 100,
      ...config,
    };

    // 初始化卡尔曼滤波器
    this.initKalmanFilter();
  }

  /**
   * 初始化卡尔曼滤波器
   */
  private initKalmanFilter(): void {
    // 位置卡尔曼滤波器 (x, y, z, vx, vy, vz, ax, ay, az)
    this.kalmanPosition = {
      x: [0, 0, 0, 0, 0, 0, 0, 0, 0],
      P: this.createIdentityMatrix(9, 100),
      Q: this.createIdentityMatrix(9, 0.01),
      R: this.createIdentityMatrix(3, 0.1),
      F: this.createStateTransitionMatrix(),
      H: this.createMeasurementMatrix(),
      initialized: false,
    };

    // 旋转卡尔曼滤波器 (qx, qy, qz, qw, wx, wy, wz)
    this.kalmanRotation = {
      x: [0, 0, 0, 1, 0, 0, 0],
      P: this.createIdentityMatrix(7, 100),
      Q: this.createIdentityMatrix(7, 0.01),
      R: this.createIdentityMatrix(4, 0.1),
      F: this.createRotationStateTransitionMatrix(),
      H: this.createRotationMeasurementMatrix(),
      initialized: false,
    };
  }

  /**
   * 创建单位矩阵
   * @param size 矩阵大小
   * @param value 对角线值
   * @returns 单位矩阵
   */
  private createIdentityMatrix(size: number, value: number = 1): number[][] {
    const matrix: number[][] = [];
    for (let i = 0; i < size; i++) {
      matrix[i] = [];
      for (let j = 0; j < size; j++) {
        matrix[i][j] = i === j ? value : 0;
      }
    }
    return matrix;
  }

  /**
   * 创建状态转移矩阵
   * @returns 状态转移矩阵
   */
  private createStateTransitionMatrix(): number[][] {
    // 状态: [x, y, z, vx, vy, vz, ax, ay, az]
    const dt = 1 / 60; // 假设60fps
    const F = this.createIdentityMatrix(9);

    // 位置更新: x += vx * dt + 0.5 * ax * dt^2
    F[0][3] = dt;
    F[0][6] = 0.5 * dt * dt;
    F[1][4] = dt;
    F[1][7] = 0.5 * dt * dt;
    F[2][5] = dt;
    F[2][8] = 0.5 * dt * dt;

    // 速度更新: vx += ax * dt
    F[3][6] = dt;
    F[4][7] = dt;
    F[5][8] = dt;

    return F;
  }

  /**
   * 创建测量矩阵
   * @returns 测量矩阵
   */
  private createMeasurementMatrix(): number[][] {
    // 测量: [x, y, z]
    const H = [];
    for (let i = 0; i < 3; i++) {
      H[i] = [];
      for (let j = 0; j < 9; j++) {
        H[i][j] = i === j ? 1 : 0;
      }
    }
    return H;
  }

  /**
   * 创建旋转状态转移矩阵
   * @returns 旋转状态转移矩阵
   */
  private createRotationStateTransitionMatrix(): number[][] {
    // 状态: [qx, qy, qz, qw, wx, wy, wz]
    return this.createIdentityMatrix(7);
    // 注意: 四元数的更新需要特殊处理，不能简单用线性模型
  }

  /**
   * 创建旋转测量矩阵
   * @returns 旋转测量矩阵
   */
  private createRotationMeasurementMatrix(): number[][] {
    // 测量: [qx, qy, qz, qw]
    const H = [];
    for (let i = 0; i < 4; i++) {
      H[i] = [];
      for (let j = 0; j < 7; j++) {
        H[i][j] = i === j ? 1 : 0;
      }
    }
    return H;
  }

  /**
   * 矩阵乘法
   * @param A 矩阵A
   * @param b 向量b
   * @returns 结果向量
   */
  private matrixMultiply(A: number[][], b: number[]): number[] {
    const result: number[] = [];
    for (let i = 0; i < A.length; i++) {
      let sum = 0;
      for (let j = 0; j < b.length; j++) {
        sum += A[i][j] * b[j];
      }
      result[i] = sum;
    }
    return result;
  }

  /**
   * 计算速度
   * @param position 当前位置
   * @param timestamp 时间戳
   * @returns 计算的速度
   */
  private calculateVelocity(position: THREE.Vector3, timestamp: number): THREE.Vector3 {
    // 如果没有历史记录，返回零速度
    if (this.history.length === 0) {
      return new THREE.Vector3(0, 0, 0);
    }

    // 获取最新状态
    const latestState = this.history[0];

    // 计算时间差（秒）
    const dt = (timestamp - latestState.timestamp) / 1000;

    // 如果时间差太小，返回上一次的速度
    if (dt < 0.001) {
      return latestState.velocity.clone();
    }

    // 计算位置差
    const posDiff = new THREE.Vector3().subVectors(position, latestState.position);

    // 计算速度
    return posDiff.divideScalar(dt);
  }

  /**
   * 计算角速度
   * @param rotation 当前旋转
   * @param timestamp 时间戳
   * @returns 计算的角速度
   */
  private calculateAngularVelocity(rotation: THREE.Quaternion, timestamp: number): THREE.Vector3 {
    // 如果没有历史记录，返回零角速度
    if (this.history.length === 0) {
      return new THREE.Vector3(0, 0, 0);
    }

    // 获取最新状态
    const latestState = this.history[0];

    // 计算时间差（秒）
    const dt = (timestamp - latestState.timestamp) / 1000;

    // 如果时间差太小，返回上一次的角速度
    if (dt < 0.001) {
      return latestState.angularVelocity.clone();
    }

    // 计算四元数差
    const q1 = latestState.rotation;
    const q2 = rotation;
    const qDiff = new THREE.Quaternion().copy(q2).multiply(q1.clone().invert());

    // 转换为角速度
    const angle = 2 * Math.acos(qDiff.w);
    const s = Math.sqrt(1 - qDiff.w * qDiff.w);

    if (s < 0.001) {
      return new THREE.Vector3(0, 0, 0);
    }

    const x = qDiff.x / s;
    const y = qDiff.y / s;
    const z = qDiff.z / s;

    return new THREE.Vector3(x, y, z).multiplyScalar(angle / dt);
  }

  /**
   * 计算加速度
   * @param velocity 当前速度
   * @param timestamp 时间戳
   * @returns 计算的加速度
   */
  private calculateAcceleration(velocity: THREE.Vector3, timestamp: number): THREE.Vector3 {
    // 如果没有历史记录，返回零加速度
    if (this.history.length === 0) {
      return new THREE.Vector3(0, 0, 0);
    }

    // 获取最新状态
    const latestState = this.history[0];

    // 计算时间差（秒）
    const dt = (timestamp - latestState.timestamp) / 1000;

    // 如果时间差太小，返回上一次的加速度
    if (dt < 0.001) {
      return latestState.acceleration.clone();
    }

    // 计算速度差
    const velDiff = new THREE.Vector3().subVectors(velocity, latestState.velocity);

    // 计算加速度
    return velDiff.divideScalar(dt);
  }

  /**
   * 计算角加速度
   * @param angularVelocity 当前角速度
   * @param timestamp 时间戳
   * @returns 计算的角加速度
   */
  private calculateAngularAcceleration(angularVelocity: THREE.Vector3, timestamp: number): THREE.Vector3 {
    // 如果没有历史记录，返回零角加速度
    if (this.history.length === 0) {
      return new THREE.Vector3(0, 0, 0);
    }

    // 获取最新状态
    const latestState = this.history[0];

    // 计算时间差（秒）
    const dt = (timestamp - latestState.timestamp) / 1000;

    // 如果时间差太小，返回上一次的角加速度
    if (dt < 0.001) {
      return latestState.angularAcceleration.clone();
    }

    // 计算角速度差
    const angVelDiff = new THREE.Vector3().subVectors(angularVelocity, latestState.angularVelocity);

    // 计算角加速度
    return angVelDiff.divideScalar(dt);
  }

  /**
   * 更新卡尔曼滤波器
   * @param state 预测状态
   */
  private updateKalmanFilter(state: PredictionState): void {
    // 更新位置卡尔曼滤波器
    this.updatePositionKalmanFilter(state);

    // 更新旋转卡尔曼滤波器
    this.updateRotationKalmanFilter(state);
  }

  /**
   * 更新位置卡尔曼滤波器
   * @param state 预测状态
   */
  private updatePositionKalmanFilter(state: PredictionState): void {
    const { position, velocity, acceleration } = state;

    // 如果未初始化，则初始化状态
    if (!this.kalmanPosition.initialized) {
      this.kalmanPosition.x = [
        position.x, position.y, position.z,
        velocity.x, velocity.y, velocity.z,
        acceleration.x, acceleration.y, acceleration.z
      ];
      this.kalmanPosition.initialized = true;
      return;
    }

    // 预测步骤
    const x = this.matrixMultiply(this.kalmanPosition.F, this.kalmanPosition.x);

    // 更新步骤
    const z = [position.x, position.y, position.z];
    const y = [
      z[0] - x[0],
      z[1] - x[1],
      z[2] - x[2]
    ];

    // 简化的卡尔曼增益计算
    const K = [0.5, 0.5, 0.5];

    // 更新状态
    this.kalmanPosition.x = [
      x[0] + K[0] * y[0],
      x[1] + K[1] * y[1],
      x[2] + K[2] * y[2],
      x[3], x[4], x[5], x[6], x[7], x[8]
    ];
  }

  /**
   * 更新旋转卡尔曼滤波器
   * @param state 预测状态
   */
  private updateRotationKalmanFilter(state: PredictionState): void {
    const { rotation, angularVelocity } = state;

    // 如果未初始化，则初始化状态
    if (!this.kalmanRotation.initialized) {
      this.kalmanRotation.x = [
        rotation.x, rotation.y, rotation.z, rotation.w,
        angularVelocity.x, angularVelocity.y, angularVelocity.z
      ];
      this.kalmanRotation.initialized = true;
      return;
    }

    // 预测步骤 - 四元数需要特殊处理
    // 这里使用简化的方法
    const x = [...this.kalmanRotation.x];

    // 更新步骤
    const z = [rotation.x, rotation.y, rotation.z, rotation.w];
    const y = [
      z[0] - x[0],
      z[1] - x[1],
      z[2] - x[2],
      z[3] - x[3]
    ];

    // 简化的卡尔曼增益计算
    const K = [0.3, 0.3, 0.3, 0.3];

    // 更新状态
    this.kalmanRotation.x = [
      x[0] + K[0] * y[0],
      x[1] + K[1] * y[1],
      x[2] + K[2] * y[2],
      x[3] + K[3] * y[3],
      x[4], x[5], x[6]
    ];

    // 归一化四元数
    const qNorm = Math.sqrt(
      this.kalmanRotation.x[0] * this.kalmanRotation.x[0] +
      this.kalmanRotation.x[1] * this.kalmanRotation.x[1] +
      this.kalmanRotation.x[2] * this.kalmanRotation.x[2] +
      this.kalmanRotation.x[3] * this.kalmanRotation.x[3]
    );

    this.kalmanRotation.x[0] /= qNorm;
    this.kalmanRotation.x[1] /= qNorm;
    this.kalmanRotation.x[2] /= qNorm;
    this.kalmanRotation.x[3] /= qNorm;
  }

  /**
   * 线性预测位置
   * @param state 预测状态
   * @param predictionTime 预测时间（毫秒）
   * @returns 预测位置
   */
  private linearPredictPosition(state: PredictionState, predictionTime: number): THREE.Vector3 {
    const seconds = predictionTime / 1000;
    const result = state.position.clone();

    // 位置 = 当前位置 + 速度 * 时间
    result.add(state.velocity.clone().multiplyScalar(seconds));

    return result;
  }

  /**
   * 二阶预测位置
   * @param state 预测状态
   * @param predictionTime 预测时间（毫秒）
   * @returns 预测位置
   */
  private quadraticPredictPosition(state: PredictionState, predictionTime: number): THREE.Vector3 {
    const seconds = predictionTime / 1000;
    const result = state.position.clone();

    // 位置 = 当前位置 + 速度 * 时间 + 0.5 * 加速度 * 时间^2
    result.add(state.velocity.clone().multiplyScalar(seconds));
    result.add(state.acceleration.clone().multiplyScalar(0.5 * seconds * seconds));

    return result;
  }

  /**
   * 卡尔曼预测位置
   * @param predictionTime 预测时间（毫秒）
   * @returns 预测位置
   */
  private kalmanPredictPosition(predictionTime: number): THREE.Vector3 {
    // 如果卡尔曼滤波器未初始化，使用线性预测
    if (!this.kalmanPosition.initialized) {
      return this.linearPredictPosition(this.history[0], predictionTime);
    }

    // 获取当前状态
    const x = this.kalmanPosition.x;
    const seconds = predictionTime / 1000;

    // 预测位置 = 当前位置 + 速度 * 时间 + 0.5 * 加速度 * 时间^2
    const predictedPosition = new THREE.Vector3(
      x[0] + x[3] * seconds + 0.5 * x[6] * seconds * seconds,
      x[1] + x[4] * seconds + 0.5 * x[7] * seconds * seconds,
      x[2] + x[5] * seconds + 0.5 * x[8] * seconds * seconds
    );

    return predictedPosition;
  }

  /**
   * 自适应预测位置
   * @param predictionTime 预测时间（毫秒）
   * @returns 预测位置
   */
  private adaptivePredictPosition(predictionTime: number): THREE.Vector3 {
    // 如果历史记录不足，使用线性预测
    if (this.history.length < 3) {
      return this.linearPredictPosition(this.history[0], predictionTime);
    }

    // 计算速度变化率
    const latestState = this.history[0];
    const prevState = this.history[1];
    const velocityChange = new THREE.Vector3().subVectors(latestState.velocity, prevState.velocity);
    const velocityChangeMagnitude = velocityChange.length();

    // 根据速度变化选择预测方法
    if (velocityChangeMagnitude > 1.0) {
      // 速度变化大，使用二阶预测
      return this.quadraticPredictPosition(latestState, predictionTime);
    } else {
      // 速度变化小，使用线性预测
      return this.linearPredictPosition(latestState, predictionTime);
    }
  }

  /**
   * 线性预测旋转
   * @param state 预测状态
   * @param predictionTime 预测时间（毫秒）
   * @returns 预测旋转
   */
  private linearPredictRotation(state: PredictionState, predictionTime: number): THREE.Quaternion {
    const seconds = predictionTime / 1000;
    const result = state.rotation.clone();

    // 创建角速度四元数
    const angVelMag = state.angularVelocity.length();
    if (angVelMag < 0.0001) {
      return result;
    }

    const axis = state.angularVelocity.clone().divideScalar(angVelMag);
    const angle = angVelMag * seconds;

    const rotationDelta = new THREE.Quaternion().setFromAxisAngle(axis, angle);

    // 应用旋转
    result.multiply(rotationDelta);

    return result;
  }

  /**
   * 二阶预测旋转
   * @param state 预测状态
   * @param predictionTime 预测时间（毫秒）
   * @returns 预测旋转
   */
  private quadraticPredictRotation(state: PredictionState, predictionTime: number): THREE.Quaternion {
    const seconds = predictionTime / 1000;

    // 计算角速度变化
    const angVel = state.angularVelocity.clone();
    angVel.add(state.angularAcceleration.clone().multiplyScalar(seconds * 0.5));

    // 创建角速度四元数
    const angVelMag = angVel.length();
    if (angVelMag < 0.0001) {
      return state.rotation.clone();
    }

    const axis = angVel.clone().divideScalar(angVelMag);
    const angle = angVelMag * seconds;

    const rotationDelta = new THREE.Quaternion().setFromAxisAngle(axis, angle);

    // 应用旋转
    const result = state.rotation.clone();
    result.multiply(rotationDelta);

    return result;
  }

  /**
   * 卡尔曼预测旋转
   * @param predictionTime 预测时间（毫秒）
   * @returns 预测旋转
   */
  private kalmanPredictRotation(predictionTime: number): THREE.Quaternion {
    // 如果卡尔曼滤波器未初始化，使用线性预测
    if (!this.kalmanRotation.initialized) {
      return this.linearPredictRotation(this.history[0], predictionTime);
    }

    // 获取当前状态
    const x = this.kalmanRotation.x;

    // 创建四元数
    const quat = new THREE.Quaternion(x[0], x[1], x[2], x[3]);

    // 创建角速度向量
    const angVel = new THREE.Vector3(x[4], x[5], x[6]);

    // 预测旋转
    const seconds = predictionTime / 1000;
    const angVelMag = angVel.length();

    if (angVelMag < 0.0001) {
      return quat;
    }

    const axis = angVel.clone().divideScalar(angVelMag);
    const angle = angVelMag * seconds;

    const rotationDelta = new THREE.Quaternion().setFromAxisAngle(axis, angle);

    // 应用旋转
    quat.multiply(rotationDelta);

    return quat;
  }

  /**
   * 自适应预测旋转
   * @param predictionTime 预测时间（毫秒）
   * @returns 预测旋转
   */
  private adaptivePredictRotation(predictionTime: number): THREE.Quaternion {
    // 如果历史记录不足，使用线性预测
    if (this.history.length < 3) {
      return this.linearPredictRotation(this.history[0], predictionTime);
    }

    // 计算角速度变化率
    const latestState = this.history[0];
    const prevState = this.history[1];
    const angVelChange = new THREE.Vector3().subVectors(latestState.angularVelocity, prevState.angularVelocity);
    const angVelChangeMagnitude = angVelChange.length();

    // 根据角速度变化选择预测方法
    if (angVelChangeMagnitude > 0.5) {
      // 角速度变化大，使用二阶预测
      return this.quadraticPredictRotation(latestState, predictionTime);
    } else {
      // 角速度变化小，使用线性预测
      return this.linearPredictRotation(latestState, predictionTime);
    }
  }

  /**
   * 更新预测状态
   * @param position 当前位置
   * @param rotation 当前旋转
   * @param velocity 当前速度
   * @param angularVelocity 当前角速度
   * @param timestamp 时间戳
   */
  public update(
    position: THREE.Vector3,
    rotation: THREE.Quaternion,
    velocity?: THREE.Vector3,
    angularVelocity?: THREE.Vector3,
    timestamp: number = Date.now()
  ): void {
    // 计算速度和加速度（如果未提供）
    const calculatedVelocity = velocity || this.calculateVelocity(position, timestamp);
    const calculatedAngularVelocity = angularVelocity || this.calculateAngularVelocity(rotation, timestamp);
    const acceleration = this.calculateAcceleration(calculatedVelocity, timestamp);
    const angularAcceleration = this.calculateAngularAcceleration(calculatedAngularVelocity, timestamp);

    // 创建新的预测状态
    const state: PredictionState = {
      position: position.clone(),
      rotation: rotation.clone(),
      velocity: calculatedVelocity.clone(),
      angularVelocity: calculatedAngularVelocity.clone(),
      acceleration: acceleration.clone(),
      angularAcceleration: angularAcceleration.clone(),
      timestamp,
    };

    // 添加到历史记录
    this.history.unshift(state);

    // 限制历史记录大小
    if (this.history.length > this.MAX_HISTORY_SIZE) {
      this.history.pop();
    }

    // 更新卡尔曼滤波器
    if (this.config.algorithm === PredictionAlgorithm.KALMAN) {
      this.updateKalmanFilter(state);
    }
  }

  /**
   * 预测位置
   * @param latency 延迟（毫秒）
   * @returns 预测位置
   */
  public predictPosition(latency: number): THREE.Vector3 {
    // 限制最大预测时间
    const predictionTime = Math.min(latency, this.config.maxPredictionTime);

    // 如果没有历史记录，返回最后已知位置
    if (this.history.length === 0) {
      return new THREE.Vector3();
    }

    // 获取最新状态
    const latestState = this.history[0];

    // 根据算法选择预测方法
    switch (this.config.algorithm) {
      case PredictionAlgorithm.LINEAR:
        return this.linearPredictPosition(latestState, predictionTime);
      case PredictionAlgorithm.QUADRATIC:
        return this.quadraticPredictPosition(latestState, predictionTime);
      case PredictionAlgorithm.KALMAN:
        return this.kalmanPredictPosition(predictionTime);
      case PredictionAlgorithm.ADAPTIVE:
        return this.adaptivePredictPosition(predictionTime);
      default:
        return this.linearPredictPosition(latestState, predictionTime);
    }
  }

  /**
   * 预测旋转
   * @param latency 延迟（毫秒）
   * @returns 预测旋转
   */
  public predictRotation(latency: number): THREE.Quaternion {
    // 限制最大预测时间
    const predictionTime = Math.min(latency, this.config.maxPredictionTime);

    // 如果没有历史记录，返回最后已知旋转
    if (this.history.length === 0) {
      return new THREE.Quaternion();
    }

    // 获取最新状态
    const latestState = this.history[0];

    // 根据算法选择预测方法
    switch (this.config.algorithm) {
      case PredictionAlgorithm.LINEAR:
        return this.linearPredictRotation(latestState, predictionTime);
      case PredictionAlgorithm.QUADRATIC:
        return this.quadraticPredictRotation(latestState, predictionTime);
      case PredictionAlgorithm.KALMAN:
        return this.kalmanPredictRotation(predictionTime);
      case PredictionAlgorithm.ADAPTIVE:
        return this.adaptivePredictRotation(predictionTime);
      default:
        return this.linearPredictRotation(latestState, predictionTime);
    }
  }
}
