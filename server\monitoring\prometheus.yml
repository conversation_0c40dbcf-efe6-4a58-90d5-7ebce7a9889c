global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 服务注册中心
  - job_name: 'service-registry'
    static_configs:
      - targets: ['service-registry:8761']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # API网关
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 用户服务
  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:4001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 项目服务
  - job_name: 'project-service'
    static_configs:
      - targets: ['project-service:3002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 资产服务
  - job_name: 'asset-service'
    static_configs:
      - targets: ['asset-service:4003']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 场景生成服务
  - job_name: 'scene-generation-service'
    static_configs:
      - targets: ['scene-generation-service:3004']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 资产库服务
  - job_name: 'asset-library-service'
    static_configs:
      - targets: ['asset-library-service:3005']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 渲染服务
  - job_name: 'render-service'
    static_configs:
      - targets: ['render-service:3006']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 协作服务
  - job_name: 'collaboration-service'
    static_configs:
      - targets: ['collaboration-service:3007']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 知识服务
  - job_name: 'knowledge-service'
    static_configs:
      - targets: ['knowledge-service:3008']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # RAG引擎
  - job_name: 'rag-engine'
    static_configs:
      - targets: ['rag-engine:3009']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # AI模型服务
  - job_name: 'ai-model-service'
    static_configs:
      - targets: ['ai-model-service:3010']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 绑定服务
  - job_name: 'binding-service'
    static_configs:
      - targets: ['binding-service:3011']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 游戏服务器
  - job_name: 'game-server'
    static_configs:
      - targets: ['game-server:3012']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 监控服务
  - job_name: 'monitoring-service'
    static_configs:
      - targets: ['monitoring-service:3013']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 场景模板服务
  - job_name: 'scene-template-service'
    static_configs:
      - targets: ['scene-template-service:8004']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 基础设施监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: '/metrics'
    scrape_interval: 60s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 60s

  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']
    metrics_path: '/_prometheus/metrics'
    scrape_interval: 60s

  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: '/minio/v2/metrics/cluster'
    scrape_interval: 60s
