/**
 * UI3DSystem.ts
 *
 * 3D UI系统，管理3D空间中的界面元素
 */
import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { Vector3, Euler, Group, Texture } from 'three';
import { UI3DComponent, UI3DComponentProps, BillboardMode } from '../components/UI3DComponent';
import { UIComponentType } from '../components/UIComponent';
import { UISystem } from './UISystem';
/**
 * 3D UI系统配置
 */
export interface UI3DSystemConfig {
    debug?: boolean;
    defaultFont?: string;
    defaultFontSize?: number;
    defaultFontColor?: string;
    defaultBackgroundColor?: string;
    defaultBorderColor?: string;
    defaultBorderWidth?: number;
    defaultBorderRadius?: number;
    defaultPadding?: number;
    defaultInteractionDistance?: number;
    defaultHoverColor?: string;
    defaultActiveColor?: string;
    defaultBillboardMode?: BillboardMode;
}
/**
 * 3D UI系统
 * 管理3D空间中的界面元素
 */
export declare class UI3DSystem extends System {
    private uiSystem;
    private config;
    private root;
    private scene?;
    /**
     * 构造函数
     * @param world 世界实例
     * @param uiSystem UI系统实例
     * @param config 3D UI系统配置
     */
    constructor(world: World, uiSystem: UISystem, config?: UI3DSystemConfig);
    /**
     * 创建3D UI元素
     * @param entity 实体
     * @param type UI元素类型
     * @param props UI元素属性
     * @returns 创建的3D UI组件
     */
    createUIElement(entity: Entity, type: UIComponentType, props?: UI3DComponentProps): UI3DComponent;
    /**
     * 创建3D按钮
     * @param entity 实体
     * @param text 按钮文本
     * @param props 按钮属性
     * @returns 创建的按钮组件
     */
    createButton(entity: Entity, text: string, props?: UI3DComponentProps): UI3DComponent;
    /**
     * 创建3D文本
     * @param entity 实体
     * @param text 文本内容
     * @param props 文本属性
     * @returns 创建的文本组件
     */
    createText(entity: Entity, text: string, props?: UI3DComponentProps): UI3DComponent;
    /**
     * 创建3D图像
     * @param entity 实体
     * @param texture 纹理
     * @param props 图像属性
     * @returns 创建的图像组件
     */
    createImage(entity: Entity, texture: Texture, props?: UI3DComponentProps): UI3DComponent;
    /**
     * 创建3D面板
     * @param entity 实体
     * @param props 面板属性
     * @returns 创建的面板组件
     */
    createPanel(entity: Entity, props?: UI3DComponentProps): UI3DComponent;
    /**
     * 创建3D窗口
     * @param entity 实体
     * @param title 窗口标题
     * @param props 窗口属性
     * @returns 创建的窗口组件
     */
    createWindow(entity: Entity, title?: string, props?: UI3DComponentProps): UI3DComponent;
    /**
     * 创建3D UI容器
     * @param entity 实体
     * @param position 位置
     * @param rotation 旋转
     * @param scale 缩放
     * @returns 创建的容器
     */
    createContainer(entity: Entity, position?: Vector3, rotation?: Vector3 | Euler, scale?: Vector3): Group;
    /**
     * 将UI元素添加到容器
     * @param component UI组件
     * @param container 容器
     * @param localPosition 本地位置
     */
    addToContainer(component: UI3DComponent, container: Group, localPosition?: Vector3): void;
    /**
     * 从容器中移除UI元素
     * @param component UI组件
     */
    removeFromContainer(component: UI3DComponent): void;
    /**
     * 创建3D UI布局
     * @param _entity 实体 - 未使用，但保留以便将来扩展
     * @param container 容器
     * @param elements UI元素列表
     * @param layout 布局类型
     * @param spacing 元素间距
     */
    createLayout(_entity: Entity, container: Group, elements: UI3DComponent[], layout?: 'horizontal' | 'vertical' | 'grid', spacing?: number): void;
    /**
     * 更新系统
     * @param _deltaTime 时间增量 - 未使用，因为UI系统会更新所有UI组件
     */
    update(_deltaTime: number): void;
    /**
     * 渲染系统
     */
    render(): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
