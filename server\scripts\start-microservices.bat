@echo off
setlocal enabledelayedexpansion

echo 🚀 启动文本语音场景生成系统微服务架构...

REM 检查Docker和Docker Compose是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未安装，请先安装Docker
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

REM 创建必要的目录
echo 📁 创建必要的目录...
if not exist logs mkdir logs
if not exist uploads mkdir uploads
if not exist uploads\images mkdir uploads\images
if not exist uploads\audio mkdir uploads\audio
if not exist uploads\models mkdir uploads\models
if not exist uploads\other mkdir uploads\other
if not exist monitoring mkdir monitoring

REM 复制环境配置文件
if not exist .env (
    echo 📋 复制环境配置文件...
    copy .env.microservices.example .env
    echo ⚠️  请编辑 .env 文件以配置您的环境变量
)

REM 创建Prometheus配置文件
if not exist monitoring\prometheus.yml (
    echo 📊 创建Prometheus配置文件...
    (
        echo global:
        echo   scrape_interval: 15s
        echo   evaluation_interval: 15s
        echo.
        echo rule_files:
        echo.
        echo scrape_configs:
        echo   - job_name: 'prometheus'
        echo     static_configs:
        echo       - targets: ['localhost:9090']
        echo.
        echo   - job_name: 'api-gateway'
        echo     static_configs:
        echo       - targets: ['api-gateway:8000']
        echo     metrics_path: '/metrics'
        echo.
        echo   - job_name: 'service-registry'
        echo     static_configs:
        echo       - targets: ['service-registry:8010']
        echo     metrics_path: '/metrics'
    ) > monitoring\prometheus.yml
)

REM 构建所有服务
echo 🔨 构建微服务镜像...
docker-compose -f docker-compose.microservices.yml build

REM 启动基础设施服务
echo 🏗️  启动基础设施服务...
docker-compose -f docker-compose.microservices.yml up -d postgres redis minio elasticsearch

REM 等待基础设施服务启动
echo ⏳ 等待基础设施服务启动...
timeout /t 30 /nobreak >nul

REM 启动监控服务
echo 📊 启动监控服务...
docker-compose -f docker-compose.microservices.yml up -d prometheus grafana jaeger

REM 启动服务注册中心
echo 🗂️  启动服务注册中心...
docker-compose -f docker-compose.microservices.yml up -d service-registry

REM 等待服务注册中心启动
echo ⏳ 等待服务注册中心启动...
timeout /t 15 /nobreak >nul

REM 启动核心微服务
echo 🚀 启动核心微服务...
docker-compose -f docker-compose.microservices.yml up -d user-service ai-model-service asset-library-service scene-template-service project-service render-service knowledge-service rag-engine

REM 等待核心服务启动
echo ⏳ 等待核心服务启动...
timeout /t 30 /nobreak >nul

REM 启动场景生成服务
echo 🎨 启动场景生成服务...
docker-compose -f docker-compose.microservices.yml up -d scene-generation-service

REM 等待场景生成服务启动
echo ⏳ 等待场景生成服务启动...
timeout /t 15 /nobreak >nul

REM 启动API网关
echo 🌐 启动API网关...
docker-compose -f docker-compose.microservices.yml up -d api-gateway

REM 等待API网关启动
echo ⏳ 等待API网关启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 🔍 检查服务状态...
docker-compose -f docker-compose.microservices.yml ps

REM 显示服务访问信息
echo.
echo ✅ 微服务架构启动完成！
echo.
echo 🌐 服务访问地址：
echo   API网关:           http://localhost:8000
echo   API文档:           http://localhost:8000/api/docs
echo   服务注册中心:       http://localhost:8010
echo   用户服务:          http://localhost:8001
echo   AI模型服务:        http://localhost:8002
echo   资源库服务:        http://localhost:8003
echo   场景模板服务:      http://localhost:8004
echo   场景生成服务:      http://localhost:8005
echo   项目服务:          http://localhost:8006
echo   渲染服务:          http://localhost:8007
echo   知识库服务:        http://localhost:8008
echo   RAG引擎:           http://localhost:8009
echo.
echo 📊 监控服务：
echo   Prometheus:        http://localhost:9090
echo   Grafana:           http://localhost:3000 (admin/admin)
echo   Jaeger:            http://localhost:16686
echo.
echo 🗄️  基础设施：
echo   PostgreSQL:        localhost:5432
echo   Redis:             localhost:6379
echo   MinIO:             http://localhost:9001 (minioadmin/minioadmin)
echo   Elasticsearch:     http://localhost:9200
echo.
echo 📝 查看日志：
echo   docker-compose -f docker-compose.microservices.yml logs -f [service-name]
echo.
echo 🛑 停止服务：
echo   scripts\stop-microservices.bat
echo.
echo 🎉 微服务架构启动完成！开始使用文本语音场景生成系统吧！

pause
