/**
 * 实体同步管理器
 * 负责管理网络实体的同步
 */
import { EventEmitter } from '../utils/EventEmitter';
import type { Entity } from '../core/Entity';
import { BandwidthController } from './BandwidthController';
/**
 * 同步区域类型
 */
export declare enum SyncAreaType {
    /** 全局 */
    GLOBAL = "global",
    /** 区域 */
    AREA = "area",
    /** 距离 */
    DISTANCE = "distance",
    /** 兴趣点 */
    INTEREST = "interest"
}
/**
 * 同步区域配置
 */
export interface SyncAreaConfig {
    /** 区域类型 */
    type: SyncAreaType;
    /** 区域ID */
    id: string;
    /** 区域中心位置 */
    position?: {
        x: number;
        y: number;
        z: number;
    };
    /** 区域半径 */
    radius?: number;
    /** 区域边界 */
    bounds?: {
        min: {
            x: number;
            y: number;
            z: number;
        };
        max: {
            x: number;
            y: number;
            z: number;
        };
    };
    /** 同步优先级 */
    priority?: number;
    /** 同步间隔（毫秒） */
    syncInterval?: number;
    /** 是否启用 */
    enabled?: boolean;
}
/**
 * 实体同步配置
 */
export interface EntitySyncConfig {
    /** 默认同步间隔（毫秒） */
    defaultSyncInterval?: number;
    /** 最小同步间隔（毫秒） */
    minSyncInterval?: number;
    /** 最大同步间隔（毫秒） */
    maxSyncInterval?: number;
    /** 同步距离 */
    syncDistance?: number;
    /** 是否使用空间分区 */
    useSpatialPartitioning?: boolean;
    /** 空间分区单元格大小 */
    spatialCellSize?: number;
    /** 是否使用插值 */
    useInterpolation?: boolean;
    /** 是否使用外推 */
    useExtrapolation?: boolean;
    /** 外推时间（毫秒） */
    extrapolationTime?: number;
    /** 是否使用压缩 */
    useCompression?: boolean;
    /** 是否使用增量同步 */
    useDeltaSync?: boolean;
    /** 是否使用优先级同步 */
    usePrioritySync?: boolean;
    /** 是否使用自适应同步 */
    useAdaptiveSync?: boolean;
}
/**
 * 实体同步状态
 */
export interface EntitySyncState {
    /** 实体ID */
    entityId: string;
    /** 实体所有者ID */
    ownerId: string;
    /** 上次同步时间 */
    lastSyncTime: number;
    /** 同步间隔（毫秒） */
    syncInterval: number;
    /** 同步优先级 */
    syncPriority: number;
    /** 同步区域ID */
    syncAreaId: string | null;
    /** 是否需要同步 */
    needsSync: boolean;
    /** 同步数据大小（字节） */
    syncDataSize: number;
    /** 同步次数 */
    syncCount: number;
    /** 同步失败次数 */
    syncFailCount: number;
}
/**
 * 实体同步管理器
 * 负责管理网络实体的同步
 */
export declare class EntitySyncManager extends EventEmitter {
    /** 配置 */
    private config;
    /** 实体映射表 */
    private entities;
    /** 实体同步状态映射表 */
    private entitySyncStates;
    /** 同步区域映射表 */
    private syncAreas;
    /** 空间分区网格 */
    private spatialGrid;
    /** 上次同步的实体数据缓存 */
    private lastSyncDataCache;
    /** 数据压缩器 */
    private dataCompressor;
    /** 同步优先级管理器 */
    private priorityManager;
    /** 本地用户ID */
    private localUserId;
    /** 带宽控制器 */
    private bandwidthController;
    /** 同步定时器ID */
    private syncTimerId;
    /** 是否正在同步 */
    private isSyncing;
    /** 待同步实体队列 */
    private syncQueue;
    /**
     * 创建实体同步管理器
     * @param config 配置
     */
    constructor(config?: EntitySyncConfig);
    /**
     * 初始化管理器
     * @param localUserId 本地用户ID
     * @param bandwidthController 带宽控制器
     */
    initialize(localUserId: string, bandwidthController?: BandwidthController): void;
    /**
     * 启动同步
     * @param interval 同步间隔（毫秒）
     */
    startSync(interval?: number): void;
    /**
     * 停止同步
     */
    stopSync(): void;
    /**
     * 添加实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    addEntity(entityId: string, entity: Entity): void;
    /**
     * 移除实体
     * @param entityId 实体ID
     */
    removeEntity(entityId: string): void;
    /**
     * 更新实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    updateEntity(entityId: string, entity: Entity): void;
    /**
     * 添加同步区域
     * @param area 同步区域配置
     */
    addSyncArea(area: SyncAreaConfig): void;
    /**
     * 移除同步区域
     * @param areaId 区域ID
     */
    removeSyncArea(areaId: string): void;
    /**
     * 更新
     * @param deltaTime 时间增量（毫秒）
     */
    update(deltaTime: number): void;
    /**
     * 同步实体
     */
    private syncEntities;
    /**
     * 更新同步队列
     * @param now 当前时间
     */
    private updateSyncQueue;
    /**
     * 处理同步队列
     * @param now 当前时间
     */
    private processSyncQueue;
    /**
     * 同步单个实体
     * @param entityId 实体ID
     * @param entity 实体
     * @param syncState 同步状态
     * @returns 同步结果
     */
    private syncEntity;
    /**
     * 手动创建增量数据
     * @param newData 新数据
     * @param oldData 旧数据
     * @returns 增量数据
     */
    private createIncrementalData;
    /**
     * 计算两个对象之间的差异
     * @param newData 新数据
     * @param oldData 旧数据
     * @returns 差异对象
     */
    private calculateDifferences;
    /**
     * 应用增量数据
     * @param incrementalData 增量数据
     * @param currentData 当前数据
     * @returns 更新后的数据
     */
    applyIncrementalData(incrementalData: any, currentData: any): any;
    /**
     * 调整实体同步间隔
     * @param entityId 实体ID
     * @param dataSize 数据大小
     */
    private adjustSyncInterval;
    /**
     * 更新实体空间单元格
     * @param entityId 实体ID
     * @param entity 实体
     */
    private updateEntitySpatialCell;
    /**
     * 从空间网格中移除实体
     * @param entityId 实体ID
     */
    private removeEntityFromSpatialGrid;
    /**
     * 获取实体
     * @param entityId 实体ID
     * @returns 实体
     */
    getEntity(entityId: string): Entity | undefined;
    /**
     * 获取实体同步状态
     * @param entityId 实体ID
     * @returns 同步状态
     */
    getEntitySyncState(entityId: string): EntitySyncState | undefined;
    /**
     * 获取同步区域
     * @param areaId 区域ID
     * @returns 同步区域
     */
    getSyncArea(areaId: string): SyncAreaConfig | undefined;
    /**
     * 设置实体同步优先级
     * @param entityId 实体ID
     * @param priority 优先级
     */
    setEntitySyncPriority(entityId: string, priority: number): void;
    /**
     * 设置实体同步间隔
     * @param entityId 实体ID
     * @param interval 同步间隔（毫秒）
     */
    setEntitySyncInterval(entityId: string, interval: number): void;
    /**
     * 标记实体需要同步
     * @param entityId 实体ID
     */
    markEntityForSync(entityId: string): void;
    /**
     * 销毁管理器
     */
    dispose(): void;
}
