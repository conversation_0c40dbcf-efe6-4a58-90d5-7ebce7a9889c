/**
 * 面部表情类型枚举
 * 定义了各种面部表情的类型
 */
export declare enum FacialExpressionType {
    /** 中性表情 */
    NEUTRAL = "neutral",
    /** 快乐 */
    HAPPY = "happy",
    /** 悲伤 */
    SAD = "sad",
    /** 愤怒 */
    ANGRY = "angry",
    /** 惊讶 */
    SURPRISED = "surprised",
    /** 恐惧 */
    FEAR = "fear",
    /** 厌恶 */
    DISGUSTED = "disgusted",
    /** 厌恶（别名） */
    DISGUST = "disgusted",
    /** 困惑 */
    CONFUSED = "confused",
    /** 兴奋 */
    EXCITED = "excited",
    /** 疲惫 */
    TIRED = "tired",
    /** 专注 */
    FOCUSED = "focused",
    /** 放松 */
    RELAXED = "relaxed",
    /** 紧张 */
    NERVOUS = "nervous",
    /** 自信 */
    CONFIDENT = "confident",
    /** 害羞 */
    SHY = "shy",
    /** 骄傲 */
    PROUD = "proud",
    /** 嫉妒 */
    JEALOUS = "jealous",
    /** 爱意 */
    LOVING = "loving",
    /** 痛苦 */
    PAIN = "pain",
    /** 思考 */
    THINKING = "thinking",
    /** 微笑 */
    SMILE = "smile",
    /** 大笑 */
    LAUGH = "laugh",
    /** 哭泣 */
    CRY = "cry",
    /** 眨眼 */
    BLINK = "blink",
    /** 眯眼 */
    SQUINT = "squint",
    /** 皱眉 */
    FROWN = "frown",
    /** 撅嘴 */
    POUT = "pout",
    /** 张嘴 */
    MOUTH_OPEN = "mouth_open",
    /** 闭嘴 */
    MOUTH_CLOSED = "mouth_closed"
}
/**
 * 面部表情强度枚举
 */
export declare enum FacialExpressionIntensity {
    /** 无 */
    NONE = 0,
    /** 轻微 */
    LIGHT = 0.25,
    /** 中等 */
    MEDIUM = 0.5,
    /** 强烈 */
    STRONG = 0.75,
    /** 极强 */
    EXTREME = 1
}
/**
 * 面部区域枚举
 */
export declare enum FacialRegion {
    /** 眼部 */
    EYES = "eyes",
    /** 眉毛 */
    EYEBROWS = "eyebrows",
    /** 嘴部 */
    MOUTH = "mouth",
    /** 鼻子 */
    NOSE = "nose",
    /** 脸颊 */
    CHEEKS = "cheeks",
    /** 下巴 */
    CHIN = "chin",
    /** 额头 */
    FOREHEAD = "forehead",
    /** 全脸 */
    FULL_FACE = "full_face"
}
/**
 * 面部表情配置接口
 */
export interface FacialExpressionConfig {
    /** 表情类型 */
    type: FacialExpressionType;
    /** 表情强度 */
    intensity: number;
    /** 影响区域 */
    regions: FacialRegion[];
    /** 持续时间（毫秒） */
    duration?: number;
    /** 是否循环 */
    loop?: boolean;
    /** 混合权重 */
    blendWeight?: number;
}
/**
 * 面部表情工具类
 */
export declare class FacialExpressionUtils {
    /**
     * 获取表情的默认强度
     */
    static getDefaultIntensity(type: FacialExpressionType): number;
    /**
     * 获取表情影响的主要区域
     */
    static getPrimaryRegions(type: FacialExpressionType): FacialRegion[];
    /**
     * 检查两个表情是否兼容（可以同时播放）
     */
    static areCompatible(type1: FacialExpressionType, type2: FacialExpressionType): boolean;
    /**
     * 创建表情配置
     */
    static createConfig(type: FacialExpressionType, intensity?: number, duration?: number): FacialExpressionConfig;
}
