/**
 * SimplexNoise 类
 * 实现 Simplex 噪声算法，用于生成自然的随机值
 */
export class SimplexNoise {
  private perm: Uint8Array;
  private permMod12: Uint8Array;
  
  // 梯度向量
  private static grad3 = [
    [1, 1, 0], [-1, 1, 0], [1, -1, 0], [-1, -1, 0],
    [1, 0, 1], [-1, 0, 1], [1, 0, -1], [-1, 0, -1],
    [0, 1, 1], [0, -1, 1], [0, 1, -1], [0, -1, -1]
  ];
  
  // F2 和 G2 是 2D 噪声的常量
  private static F2 = 0.5 * (Math.sqrt(3) - 1);
  private static G2 = (3 - Math.sqrt(3)) / 6;
  
  // F3 和 G3 是 3D 噪声的常量
  private static F3 = 1 / 3;
  private static G3 = 1 / 6;
  
  /**
   * 构造函数
   * @param seed 随机种子
   */
  constructor(seed: number = Math.random()) {
    // 初始化排列表
    this.perm = new Uint8Array(512);
    this.permMod12 = new Uint8Array(512);
    
    const p = new Uint8Array(256);
    
    // 填充 p 数组
    for (let i = 0; i < 256; i++) {
      p[i] = i;
    }
    
    // 使用种子打乱 p 数组
    let n: number;
    let q: number;
    let seedNum = Number(seed);
    seedNum = seedNum * 1664525 + 1013904223;
    seedNum = seedNum * 1664525 + 1013904223;
    seedNum = seedNum * 1664525 + 1013904223;

    for (let i = 255; i > 0; i--) {
      n = Math.floor((seedNum + 31) % (i + 1));
      if (n < 0) n += i + 1;
      q = p[i];
      p[i] = p[n];
      p[n] = q;
      seedNum = seedNum * 1664525 + 1013904223;
    }
    
    // 填充 perm 和 permMod12
    for (let i = 0; i < 512; i++) {
      this.perm[i] = p[i & 255];
      this.permMod12[i] = this.perm[i] % 12;
    }
  }
  
  /**
   * 2D Simplex 噪声
   * @param xin X 坐标
   * @param yin Y 坐标
   * @returns 噪声值 (-1 到 1 范围)
   */
  public noise2D(xin: number, yin: number): number {
    // 噪声贡献
    let n0: number, n1: number, n2: number;
    
    // 计算 simplex 单元格坐标
    const s = (xin + yin) * SimplexNoise.F2;
    const i = Math.floor(xin + s);
    const j = Math.floor(yin + s);
    
    const t = (i + j) * SimplexNoise.G2;
    const X0 = i - t;
    const Y0 = j - t;
    const x0 = xin - X0;
    const y0 = yin - Y0;
    
    // 确定 simplex 单元格中的位置
    let i1: number, j1: number;
    if (x0 > y0) {
      i1 = 1;
      j1 = 0;
    } else {
      i1 = 0;
      j1 = 1;
    }
    
    // 计算相对坐标
    const x1 = x0 - i1 + SimplexNoise.G2;
    const y1 = y0 - j1 + SimplexNoise.G2;
    const x2 = x0 - 1 + 2 * SimplexNoise.G2;
    const y2 = y0 - 1 + 2 * SimplexNoise.G2;
    
    // 计算哈希值
    const ii = i & 255;
    const jj = j & 255;
    const gi0 = this.permMod12[ii + this.perm[jj]];
    const gi1 = this.permMod12[ii + i1 + this.perm[jj + j1]];
    const gi2 = this.permMod12[ii + 1 + this.perm[jj + 1]];
    
    // 计算贡献
    let t0 = 0.5 - x0 * x0 - y0 * y0;
    if (t0 < 0) {
      n0 = 0;
    } else {
      t0 *= t0;
      n0 = t0 * t0 * this.dot2D(SimplexNoise.grad3[gi0], x0, y0);
    }
    
    let t1 = 0.5 - x1 * x1 - y1 * y1;
    if (t1 < 0) {
      n1 = 0;
    } else {
      t1 *= t1;
      n1 = t1 * t1 * this.dot2D(SimplexNoise.grad3[gi1], x1, y1);
    }
    
    let t2 = 0.5 - x2 * x2 - y2 * y2;
    if (t2 < 0) {
      n2 = 0;
    } else {
      t2 *= t2;
      n2 = t2 * t2 * this.dot2D(SimplexNoise.grad3[gi2], x2, y2);
    }
    
    // 将结果缩放到 [-1, 1] 范围
    return 70 * (n0 + n1 + n2);
  }
  
  /**
   * 3D Simplex 噪声
   * @param xin X 坐标
   * @param yin Y 坐标
   * @param zin Z 坐标
   * @returns 噪声值 (-1 到 1 范围)
   */
  public noise3D(xin: number, yin: number, zin: number): number {
    // 噪声贡献
    let n0: number, n1: number, n2: number, n3: number;
    
    // 计算 simplex 单元格坐标
    const s = (xin + yin + zin) * SimplexNoise.F3;
    const i = Math.floor(xin + s);
    const j = Math.floor(yin + s);
    const k = Math.floor(zin + s);
    
    const t = (i + j + k) * SimplexNoise.G3;
    const X0 = i - t;
    const Y0 = j - t;
    const Z0 = k - t;
    
    const x0 = xin - X0;
    const y0 = yin - Y0;
    const z0 = zin - Z0;
    
    // 确定 simplex 单元格中的位置
    let i1: number, j1: number, k1: number;
    let i2: number, j2: number, k2: number;
    
    if (x0 >= y0) {
      if (y0 >= z0) {
        i1 = 1; j1 = 0; k1 = 0; i2 = 1; j2 = 1; k2 = 0;
      } else if (x0 >= z0) {
        i1 = 1; j1 = 0; k1 = 0; i2 = 1; j2 = 0; k2 = 1;
      } else {
        i1 = 0; j1 = 0; k1 = 1; i2 = 1; j2 = 0; k2 = 1;
      }
    } else {
      if (y0 < z0) {
        i1 = 0; j1 = 0; k1 = 1; i2 = 0; j2 = 1; k2 = 1;
      } else if (x0 < z0) {
        i1 = 0; j1 = 1; k1 = 0; i2 = 0; j2 = 1; k2 = 1;
      } else {
        i1 = 0; j1 = 1; k1 = 0; i2 = 1; j2 = 1; k2 = 0;
      }
    }
    
    // 计算相对坐标
    const x1 = x0 - i1 + SimplexNoise.G3;
    const y1 = y0 - j1 + SimplexNoise.G3;
    const z1 = z0 - k1 + SimplexNoise.G3;
    
    const x2 = x0 - i2 + 2 * SimplexNoise.G3;
    const y2 = y0 - j2 + 2 * SimplexNoise.G3;
    const z2 = z0 - k2 + 2 * SimplexNoise.G3;
    
    const x3 = x0 - 1 + 3 * SimplexNoise.G3;
    const y3 = y0 - 1 + 3 * SimplexNoise.G3;
    const z3 = z0 - 1 + 3 * SimplexNoise.G3;
    
    // 计算哈希值
    const ii = i & 255;
    const jj = j & 255;
    const kk = k & 255;
    
    const gi0 = this.permMod12[ii + this.perm[jj + this.perm[kk]]];
    const gi1 = this.permMod12[ii + i1 + this.perm[jj + j1 + this.perm[kk + k1]]];
    const gi2 = this.permMod12[ii + i2 + this.perm[jj + j2 + this.perm[kk + k2]]];
    const gi3 = this.permMod12[ii + 1 + this.perm[jj + 1 + this.perm[kk + 1]]];
    
    // 计算贡献
    let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;
    if (t0 < 0) {
      n0 = 0;
    } else {
      t0 *= t0;
      n0 = t0 * t0 * this.dot3D(SimplexNoise.grad3[gi0], x0, y0, z0);
    }
    
    let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;
    if (t1 < 0) {
      n1 = 0;
    } else {
      t1 *= t1;
      n1 = t1 * t1 * this.dot3D(SimplexNoise.grad3[gi1], x1, y1, z1);
    }
    
    let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;
    if (t2 < 0) {
      n2 = 0;
    } else {
      t2 *= t2;
      n2 = t2 * t2 * this.dot3D(SimplexNoise.grad3[gi2], x2, y2, z2);
    }
    
    let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;
    if (t3 < 0) {
      n3 = 0;
    } else {
      t3 *= t3;
      n3 = t3 * t3 * this.dot3D(SimplexNoise.grad3[gi3], x3, y3, z3);
    }
    
    // 将结果缩放到 [-1, 1] 范围
    return 32 * (n0 + n1 + n2 + n3);
  }
  
  /**
   * 计算 2D 点积
   * @param g 梯度向量
   * @param x X 坐标
   * @param y Y 坐标
   * @returns 点积
   */
  private dot2D(g: number[], x: number, y: number): number {
    return g[0] * x + g[1] * y;
  }
  
  /**
   * 计算 3D 点积
   * @param g 梯度向量
   * @param x X 坐标
   * @param y Y 坐标
   * @param z Z 坐标
   * @returns 点积
   */
  private dot3D(g: number[], x: number, y: number, z: number): number {
    return g[0] * x + g[1] * y + g[2] * z;
  }
}
