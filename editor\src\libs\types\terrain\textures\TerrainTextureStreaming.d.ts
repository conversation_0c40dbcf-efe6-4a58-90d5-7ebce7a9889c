/**
 * 地形纹理流式加载
 * 提供地形纹理的流式加载和管理功能
 */
import * as THREE from 'three';
import { TextureCompressionOptions } from './TerrainTextureCompression';
/**
 * 纹理加载优先级
 */
export declare enum TextureLoadPriority {
    /** 非常高 */
    VERY_HIGH = 0,
    /** 高 */
    HIGH = 1,
    /** 中 */
    MEDIUM = 2,
    /** 低 */
    LOW = 3,
    /** 非常低 */
    VERY_LOW = 4
}
/**
 * 纹理加载状态
 */
export declare enum TextureLoadState {
    /** 未加载 */
    UNLOADED = "unloaded",
    /** 加载中 */
    LOADING = "loading",
    /** 已加载 */
    LOADED = "loaded",
    /** 加载失败 */
    FAILED = "failed",
    /** 已卸载 */
    UNLOADING = "unloading"
}
/**
 * 纹理流式加载事件类型
 */
export declare enum TextureStreamingEventType {
    /** 纹理加载开始 */
    TEXTURE_LOAD_START = "texture_load_start",
    /** 纹理加载完成 */
    TEXTURE_LOAD_COMPLETE = "texture_load_complete",
    /** 纹理加载失败 */
    TEXTURE_LOAD_FAILED = "texture_load_failed",
    /** 纹理卸载 */
    TEXTURE_UNLOADED = "texture_unloaded",
    /** 纹理加载进度 */
    TEXTURE_LOAD_PROGRESS = "texture_load_progress",
    /** 内存使用变化 */
    MEMORY_USAGE_CHANGED = "memory_usage_changed"
}
/**
 * 纹理流式加载选项
 */
export interface TextureStreamingOptions {
    /** 最大并发加载数 */
    maxConcurrentLoads?: number;
    /** 最大内存使用量（MB） */
    maxMemoryUsage?: number;
    /** 是否使用纹理压缩 */
    useCompression?: boolean;
    /** 纹理压缩选项 */
    compressionOptions?: TextureCompressionOptions;
    /** 是否使用低分辨率占位符 */
    useLowResPlaceholders?: boolean;
    /** 低分辨率纹理大小 */
    lowResTextureSize?: number;
    /** 是否使用预测加载 */
    usePredictiveLoading?: boolean;
    /** 是否自动卸载 */
    autoUnload?: boolean;
    /** 卸载检查间隔（毫秒） */
    unloadCheckInterval?: number;
    /** 纹理未使用卸载时间（毫秒） */
    textureUnusedTime?: number;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 地形纹理流式加载类
 */
export declare class TerrainTextureStreaming {
    /** 默认选项 */
    private static readonly DEFAULT_OPTIONS;
    /** 选项 */
    private options;
    /** 纹理压缩 */
    private textureCompression;
    /** 纹理加载器 */
    private textureLoader;
    /** 加载请求映射 */
    private loadRequests;
    /** 活跃加载请求 */
    private activeLoads;
    /** 加载队列 */
    private loadQueue;
    /** 已加载纹理映射 */
    private loadedTextures;
    /** 纹理最后使用时间映射 */
    private textureLastUsed;
    /** 纹理大小映射（字节） */
    private textureSizes;
    /** 当前内存使用量（字节） */
    private currentMemoryUsage;
    /** 事件发射器 */
    private eventEmitter;
    /** 卸载检查定时器ID */
    private unloadCheckTimerId;
    /** 默认占位符纹理 */
    private defaultPlaceholder;
    /**
     * 创建地形纹理流式加载
     * @param options 选项
     */
    constructor(options?: TextureStreamingOptions);
    /**
     * 创建默认占位符纹理
     * @returns 默认占位符纹理
     */
    private createDefaultPlaceholder;
    /**
     * 启动卸载检查
     */
    private startUnloadCheck;
    /**
     * 停止卸载检查
     */
    private stopUnloadCheck;
    /**
     * 检查卸载
     */
    private checkForUnload;
    /**
     * 加载纹理
     * @param url 纹理URL
     * @param options 加载选项
     * @returns 纹理ID
     */
    loadTexture(url: string, options?: {
        priority?: TextureLoadPriority;
        compressionOptions?: TextureCompressionOptions;
        id?: string;
        useLowRes?: boolean;
    }): string;
    /**
     * 添加到加载队列
     * @param request 加载请求
     */
    private addToLoadQueue;
    /**
     * 处理加载队列
     */
    private processLoadQueue;
    /**
     * 开始加载
     * @param request 加载请求
     */
    private startLoading;
    /**
     * 完成加载
     * @param request 加载请求
     * @param texture 纹理
     */
    private completeLoading;
    /**
     * 加载失败
     * @param request 加载请求
     * @param error 错误
     */
    private failLoading;
    /**
     * 估算纹理大小
     * @param texture 纹理
     * @returns 估算大小（字节）
     */
    private estimateTextureSize;
    /**
     * 更新内存使用量
     * @param id 纹理ID
     * @param size 大小（字节）
     */
    private updateMemoryUsage;
    /**
     * 检查内存使用量
     */
    private checkMemoryUsage;
    /**
     * 卸载最近最少使用的纹理
     */
    private unloadLeastRecentlyUsedTextures;
    /**
     * 卸载纹理
     * @param id 纹理ID
     * @returns 释放的内存大小（字节）
     */
    unloadTexture(id: string): number;
    /**
     * 获取纹理
     * @param id 纹理ID
     * @returns 纹理
     */
    getTexture(id: string): THREE.Texture | null;
    /**
     * 获取纹理加载状态
     * @param id 纹理ID
     * @returns 加载状态
     */
    getTextureLoadState(id: string): TextureLoadState;
    /**
     * 获取当前内存使用量
     * @returns 内存使用量（字节）
     */
    getMemoryUsage(): number;
    /**
     * 获取当前内存使用量（MB）
     * @returns 内存使用量（MB）
     */
    getMemoryUsageMB(): number;
    /**
     * 获取最大内存使用量（MB）
     * @returns 最大内存使用量（MB）
     */
    getMaxMemoryUsageMB(): number;
    /**
     * 获取已加载纹理数量
     * @returns 已加载纹理数量
     */
    getLoadedTextureCount(): number;
    /**
     * 获取活跃加载数量
     * @returns 活跃加载数量
     */
    getActiveLoadCount(): number;
    /**
     * 获取队列中的加载数量
     * @returns 队列中的加载数量
     */
    getQueuedLoadCount(): number;
    /**
     * 清除所有纹理
     */
    clearAll(): void;
    /**
     * 设置选项
     * @param options 选项
     */
    setOptions(options: TextureStreamingOptions): void;
    /**
     * 注册事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    on(event: TextureStreamingEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    off(event: TextureStreamingEventType, listener: (...args: any[]) => void): void;
    /**
     * 销毁
     */
    dispose(): void;
}
