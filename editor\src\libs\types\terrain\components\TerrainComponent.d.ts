/**
 * 地形组件
 * 用于存储和管理地形数据
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { TerrainExportOptions, TerrainImportOptions } from '../io/TerrainImportExportService';
import { HeightMapExportOptions, HeightMapImportOptions } from '../io/HeightMapImportExport';
import { ThirdPartyTerrainFormat, ThirdPartyTerrainExportOptions, ThirdPartyTerrainImportOptions } from '../io/ThirdPartyTerrainImportExport';
import { BatchImportResult, BatchExportResult } from '../io/BatchTerrainImportExport';
/**
 * 地形纹理层接口
 */
export interface TerrainTextureLayer {
    /** 纹理 */
    texture: string | THREE.Texture;
    /** 法线贴图 */
    normalMap?: string | THREE.Texture;
    /** 高光贴图 */
    roughnessMap?: string | THREE.Texture;
    /** 置换贴图 */
    displacementMap?: string | THREE.Texture;
    /** 环境光遮蔽贴图 */
    aoMap?: string | THREE.Texture;
    /** 纹理平铺系数 */
    tiling: number;
    /** 混合权重 */
    weight?: number;
    /** 最小高度 */
    minHeight?: number;
    /** 最大高度 */
    maxHeight?: number;
    /** 最小斜度 */
    minSlope?: number;
    /** 最大斜度 */
    maxSlope?: number;
}
/**
 * 地形组件选项
 */
export interface TerrainComponentOptions {
    /** 宽度 */
    width?: number;
    /** 高度 */
    height?: number;
    /** 分辨率 */
    resolution?: number;
    /** 最大高度 */
    maxHeight?: number;
    /** 高度图 */
    heightMap?: string | THREE.Texture | Float32Array;
    /** 纹理层 */
    layers?: TerrainTextureLayer[];
    /** 是否使用LOD */
    useLOD?: boolean;
    /** LOD级别 */
    lodLevels?: number;
    /** LOD距离 */
    lodDistances?: number[];
    /** 是否使用物理 */
    usePhysics?: boolean;
    /** 物理精度 */
    physicsResolution?: number;
}
/**
 * 地形组件
 */
export declare class TerrainComponent extends Component {
    /** 组件类型 */
    static readonly TYPE = "TerrainComponent";
    /** 宽度 */
    width: number;
    /** 高度 */
    height: number;
    /** 分辨率 */
    resolution: number;
    /** 最大高度 */
    maxHeight: number;
    /** 高度数据 */
    heightData: Float32Array;
    /** 法线数据 */
    normalData: Float32Array;
    /** 纹理层 */
    layers: TerrainTextureLayer[];
    /** 是否使用LOD */
    useLOD: boolean;
    /** LOD级别 */
    lodLevels: number;
    /** LOD距离 */
    lodDistances: number[];
    /** 是否使用物理 */
    usePhysics: boolean;
    /** 物理精度 */
    physicsResolution: number;
    /** 物理材质类型 */
    physicsMaterialType: string;
    /** 物理摩擦力 */
    physicsFriction: number;
    /** 物理弹性 */
    physicsRestitution: number;
    /** 物理密度 */
    physicsDensity: number;
    /** 是否显示物理调试 */
    showPhysicsDebug: boolean;
    /** 地形网格 */
    mesh: THREE.Mesh | null;
    /** 地形材质 */
    material: THREE.Material | null;
    /** 地形几何体 */
    geometry: THREE.BufferGeometry | null;
    /** 是否已初始化 */
    initialized: boolean;
    /** 是否需要更新 */
    needsUpdate: boolean;
    /** 是否需要更新物理 */
    needsPhysicsUpdate: boolean;
    /** 元数据 */
    metadata: Record<string, any>;
    /**
     * 创建地形组件
     * @param options 选项
     */
    constructor(options?: TerrainComponentOptions);
    /**
     * 获取组件类型
     * @returns 组件类型
     */
    getType(): string;
    /**
     * 加载高度图
     * @param heightMap 高度图
     */
    loadHeightMap(heightMap: string | THREE.Texture | Float32Array): void;
    /**
     * 获取指定位置的高度
     * @param x X坐标
     * @param z Z坐标
     * @returns 高度值
     */
    getHeight(x: number, z: number): number;
    /**
     * 设置指定位置的高度
     * @param x X坐标
     * @param z Z坐标
     * @param height 高度值
     */
    setHeight(x: number, z: number, height: number): void;
    /**
     * 修改指定位置的高度
     * @param x X坐标
     * @param z Z坐标
     * @param delta 高度变化量
     */
    modifyHeight(x: number, z: number, delta: number): void;
    /**
     * 获取地形几何体
     * @returns 地形几何体
     */
    getGeometry(): THREE.BufferGeometry | null;
    /**
     * 设置地形几何体
     * @param geometry 地形几何体
     */
    setGeometry(geometry: THREE.BufferGeometry): void;
    /**
     * 导出地形为JSON
     * @param options 导出选项
     * @returns JSON字符串
     */
    exportToJSON(options?: TerrainExportOptions): string;
    /**
     * 从JSON导入地形
     * @param json JSON字符串
     * @param options 导入选项
     * @returns 是否导入成功
     */
    importFromJSON(json: string, options?: TerrainImportOptions): boolean;
    /**
     * 导出地形为高度图
     * @param options 导出选项
     * @returns Promise，解析为Blob
     */
    exportToHeightMap(options: HeightMapExportOptions): Promise<Blob>;
    /**
     * 从高度图导入地形
     * @param heightMap 高度图（URL、File、Blob、ImageData、Canvas、Image）
     * @param options 导入选项
     * @returns Promise，解析为是否导入成功
     */
    importFromHeightMap(heightMap: string | File | Blob | ImageData | HTMLCanvasElement | HTMLImageElement, options?: HeightMapImportOptions): Promise<boolean>;
    /**
     * 导出地形为第三方格式
     * @param format 格式
     * @param options 导出选项
     * @returns Promise，解析为导出数据
     */
    exportToThirdPartyFormat(format: ThirdPartyTerrainFormat, options?: Omit<ThirdPartyTerrainExportOptions, 'format'>): Promise<ArrayBuffer | string>;
    /**
     * 从第三方格式导入地形
     * @param format 格式
     * @param data 地形数据
     * @param options 导入选项
     * @returns Promise，解析为是否导入成功
     */
    importFromThirdPartyFormat(format: ThirdPartyTerrainFormat, data: ArrayBuffer | string, options?: Omit<ThirdPartyTerrainImportOptions, 'format'>): Promise<boolean>;
    /**
     * 批量导入高度图
     * @param files 文件列表
     * @param options 导入选项
     * @returns Promise，解析为导入结果列表
     */
    batchImportHeightMaps(files: File[], options?: HeightMapImportOptions): Promise<BatchImportResult[]>;
    /**
     * 批量导入JSON
     * @param files 文件列表
     * @param options 导入选项
     * @returns Promise，解析为导入结果列表
     */
    batchImportJSON(files: File[], options?: TerrainImportOptions): Promise<BatchImportResult[]>;
    /**
     * 批量导入第三方格式
     * @param files 文件列表
     * @param format 格式
     * @param options 导入选项
     * @returns Promise，解析为导入结果列表
     */
    batchImportThirdPartyFormat(files: File[], format: ThirdPartyTerrainFormat, options?: Omit<ThirdPartyTerrainImportOptions, 'format'>): Promise<BatchImportResult[]>;
    /**
     * 批量导出高度图
     * @param formats 格式列表
     * @param baseFileName 基础文件名
     * @returns Promise，解析为导出结果列表
     */
    batchExportHeightMaps(formats: HeightMapExportOptions[], baseFileName?: string): Promise<BatchExportResult[]>;
    /**
     * 批量导出JSON
     * @param options 导出选项列表
     * @param baseFileName 基础文件名
     * @returns 导出结果列表
     */
    batchExportJSON(options: TerrainExportOptions[], baseFileName?: string): BatchExportResult[];
    /**
     * 批量导出第三方格式
     * @param formats 格式和选项列表
     * @param baseFileName 基础文件名
     * @returns Promise，解析为导出结果列表
     */
    batchExportThirdPartyFormat(formats: {
        format: ThirdPartyTerrainFormat;
        options?: Omit<ThirdPartyTerrainExportOptions, 'format'>;
    }[], baseFileName?: string): Promise<BatchExportResult[]>;
}
