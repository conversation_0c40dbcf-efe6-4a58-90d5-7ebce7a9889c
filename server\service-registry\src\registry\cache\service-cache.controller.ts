/**
 * 服务缓存控制器
 */
import { Controller, Get, Post, Delete, Param, Body, HttpStatus, HttpCode } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ServiceCacheService, CacheStats } from './service-cache.service';

@ApiTags('缓存')
@Controller('api/cache')
export class ServiceCacheController {
  constructor(
    private readonly cacheService: ServiceCacheService,
  ) {}
  
  /**
   * 获取缓存统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取缓存统计信息' })
  @ApiResponse({ status: HttpStatus.OK, description: '缓存统计信息' })
  async getStats(): Promise<CacheStats> {
    return this.cacheService.getStats();
  }
  
  /**
   * 清除所有缓存
   */
  @Delete()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '清除所有缓存' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: '缓存已清除' })
  async clearCache(): Promise<void> {
    await this.cacheService.clear();
  }
  
  /**
   * 清除服务缓存
   * @param serviceName 服务名称
   */
  @Delete('service/:name')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '清除服务缓存' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: '服务缓存已清除' })
  async clearServiceCache(@Param('name') serviceName: string): Promise<void> {
    await this.cacheService.invalidateServiceCache(serviceName);
  }
  
  /**
   * 清除特定缓存
   * @param key 缓存键
   */
  @Delete(':key')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '清除特定缓存' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: '缓存已清除' })
  async invalidateCache(@Param('key') key: string): Promise<void> {
    await this.cacheService.invalidate(key);
  }
}
