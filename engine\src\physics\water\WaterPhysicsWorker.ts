/**
 * 水体物理计算工作线程
 * 用于在独立线程中进行水体物理计算
 */

/**
 * 消息类型
 */
export enum WaterPhysicsWorkerMessageType {
  /** 初始化 */
  INIT = 'init',
  /** 更新水波 */
  UPDATE_WAVES = 'update_waves',
  /** 更新水流 */
  UPDATE_FLOW = 'update_flow',
  /** 更新浮力和阻力 */
  UPDATE_BUOYANCY_AND_DRAG = 'update_buoyancy_and_drag',
  /** 更新法线图 */
  UPDATE_NORMAL_MAP = 'update_normal_map',
  /** 完成 */
  COMPLETE = 'complete',
  /** 错误 */
  ERROR = 'error'
}

/**
 * 水波更新参数
 */
export interface WaterWavesUpdateParams {
  /** 高度图 */
  heightMap: Float32Array;
  /** 分辨率 */
  resolution: number;
  /** 波动参数 */
  waveParams: {
    /** 振幅 */
    amplitude: number;
    /** 频率 */
    frequency: number;
    /** 速度 */
    speed: number;
    /** 方向 */
    direction: { x: number; z: number };
  };
  /** 时间 */
  time: number;
  /** 时间增量 */
  deltaTime: number;
}

/**
 * 水流更新参数
 */
export interface WaterFlowUpdateParams {
  /** 高度图 */
  heightMap: Float32Array;
  /** 速度图 */
  velocityMap: Float32Array;
  /** 分辨率 */
  resolution: number;
  /** 流向 */
  flowDirection: { x: number; y: number; z: number };
  /** 流速 */
  flowSpeed: number;
  /** 时间增量 */
  deltaTime: number;
}

/**
 * 法线图更新参数
 */
export interface NormalMapUpdateParams {
  /** 高度图 */
  heightMap: Float32Array;
  /** 法线图 */
  normalMap: Float32Array;
  /** 分辨率 */
  resolution: number;
}

/**
 * 创建水体物理工作线程
 * @returns 工作线程
 */
export function createWaterPhysicsWorker(): Worker {
  // 创建工作线程URL
  const workerCode = `
    self.onmessage = function(e) {
      const { type, data } = e.data;
      
      try {
        switch (type) {
          case '${WaterPhysicsWorkerMessageType.INIT}':
            // 初始化工作线程
            self.postMessage({ 
              type: '${WaterPhysicsWorkerMessageType.COMPLETE}', 
              data: { success: true } 
            });
            break;
            
          case '${WaterPhysicsWorkerMessageType.UPDATE_WAVES}':
            // 更新水波
            const updatedHeightMap = updateWaves(data);
            self.postMessage({ 
              type: '${WaterPhysicsWorkerMessageType.COMPLETE}', 
              data: { heightMap: updatedHeightMap } 
            }, [updatedHeightMap.buffer]);
            break;
            
          case '${WaterPhysicsWorkerMessageType.UPDATE_FLOW}':
            // 更新水流
            const result = updateFlow(data);
            self.postMessage({ 
              type: '${WaterPhysicsWorkerMessageType.COMPLETE}', 
              data: { 
                heightMap: result.heightMap,
                velocityMap: result.velocityMap
              } 
            }, [result.heightMap.buffer, result.velocityMap.buffer]);
            break;
            
          case '${WaterPhysicsWorkerMessageType.UPDATE_NORMAL_MAP}':
            // 更新法线图
            const normalMap = updateNormalMap(data);
            self.postMessage({ 
              type: '${WaterPhysicsWorkerMessageType.COMPLETE}', 
              data: { normalMap } 
            }, [normalMap.buffer]);
            break;
            
          default:
            throw new Error('未知的消息类型: ' + type);
        }
      } catch (error) {
        self.postMessage({ 
          type: '${WaterPhysicsWorkerMessageType.ERROR}', 
          data: { message: error.message } 
        });
      }
    };
    
    // 更新水波
    function updateWaves(params) {
      const { heightMap, resolution, waveParams, time, deltaTime } = params;
      const result = new Float32Array(heightMap);
      
      // 波动计算
      for (let z = 0; z < resolution; z++) {
        for (let x = 0; x < resolution; x++) {
          const index = z * resolution + x;
          
          // 计算波动
          const nx = x / resolution;
          const nz = z / resolution;
          
          // 计算波动高度
          let waveHeight = 0;
          
          // 添加多个正弦波
          waveHeight += Math.sin(nx * waveParams.frequency + time * waveParams.speed) * 
                        Math.cos(nz * waveParams.frequency * 0.8 + time * waveParams.speed * 0.8) * 
                        waveParams.amplitude;
                        
          waveHeight += Math.sin(nx * waveParams.frequency * 0.6 + time * waveParams.speed * 1.2) * 
                        Math.sin(nz * waveParams.frequency * 0.5 + time * waveParams.speed * 0.7) * 
                        waveParams.amplitude * 0.3;
          
          // 应用波动方向
          const dirX = waveParams.direction.x;
          const dirZ = waveParams.direction.z;
          const dirFactor = (nx * dirX + nz * dirZ) * 5;
          waveHeight += Math.sin(dirFactor + time * waveParams.speed * 1.5) * waveParams.amplitude * 0.2;
          
          // 更新高度图
          result[index] = waveHeight;
        }
      }
      
      return result;
    }
    
    // 更新水流
    function updateFlow(params) {
      const { heightMap, velocityMap, resolution, flowDirection, flowSpeed, deltaTime } = params;
      const resultHeight = new Float32Array(heightMap);
      const resultVelocity = new Float32Array(velocityMap);
      
      // 水流计算
      for (let z = 0; z < resolution; z++) {
        for (let x = 0; x < resolution; x++) {
          const index = z * resolution + x;
          const vIndex = index * 3;
          
          // 更新速度
          resultVelocity[vIndex] = flowDirection.x * flowSpeed;
          resultVelocity[vIndex + 1] = flowDirection.y * flowSpeed;
          resultVelocity[vIndex + 2] = flowDirection.z * flowSpeed;
          
          // 计算水流对高度的影响
          // 这里使用简化的模型，实际应用中可能需要更复杂的流体动力学模型
          const nx = x / resolution;
          const nz = z / resolution;
          
          // 计算流动方向上的偏移
          const offsetX = Math.floor(x + flowDirection.x * flowSpeed * deltaTime * 10) % resolution;
          const offsetZ = Math.floor(z + flowDirection.z * flowSpeed * deltaTime * 10) % resolution;
          
          // 获取偏移位置的高度
          const offsetIndex = offsetZ * resolution + offsetX;
          if (offsetIndex >= 0 && offsetIndex < resolution * resolution) {
            // 混合原始高度和流动后的高度
            resultHeight[index] = heightMap[index] * 0.95 + heightMap[offsetIndex] * 0.05;
          }
        }
      }
      
      return { heightMap: resultHeight, velocityMap: resultVelocity };
    }
    
    // 更新法线图
    function updateNormalMap(params) {
      const { heightMap, normalMap, resolution } = params;
      const result = new Float32Array(normalMap.length);
      
      // 计算法线
      for (let z = 0; z < resolution; z++) {
        for (let x = 0; x < resolution; x++) {
          const index = z * resolution + x;
          const nIndex = index * 3;
          
          // 获取相邻点的高度
          const left = x > 0 ? heightMap[z * resolution + (x - 1)] : heightMap[index];
          const right = x < resolution - 1 ? heightMap[z * resolution + (x + 1)] : heightMap[index];
          const top = z > 0 ? heightMap[(z - 1) * resolution + x] : heightMap[index];
          const bottom = z < resolution - 1 ? heightMap[(z + 1) * resolution + x] : heightMap[index];
          
          // 计算法线
          const nx = (left - right) * 0.5;
          const nz = (top - bottom) * 0.5;
          const ny = 1.0;
          
          // 归一化
          const length = Math.sqrt(nx * nx + ny * ny + nz * nz);
          
          // 存储法线
          result[nIndex] = nx / length;
          result[nIndex + 1] = ny / length;
          result[nIndex + 2] = nz / length;
        }
      }
      
      return result;
    }
  `;
  
  // 创建Blob URL
  const blob = new Blob([workerCode], { type: 'application/javascript' });
  const url = URL.createObjectURL(blob);
  
  // 创建工作线程
  return new Worker(url);
}
