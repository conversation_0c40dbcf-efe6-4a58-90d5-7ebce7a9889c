/**
 * 场景预加载管理器
 * 负责场景资源的依赖分析、预加载和按需加载
 */
import * as THREE from 'three';
import { AssetManager } from '../assets/AssetManager';
import { ScenePreloader, ScenePreloadProgressInfo } from './ScenePreloader';
import { Scene } from './Scene';
import { EventEmitter } from '../utils/EventEmitter';
import type { Entity } from '../core/Entity';

/**
 * 预加载策略类型
 */
export enum PreloadStrategyType {
  /** 距离策略 - 基于相机距离预加载 */
  DISTANCE = 'distance',
  /** 视锥体策略 - 基于视锥体可见性预加载 */
  FRUSTUM = 'frustum',
  /** 方向策略 - 基于相机朝向预加载 */
  DIRECTION = 'direction',
  /** 层级策略 - 基于场景层级预加载 */
  HIERARCHY = 'hierarchy',
  /** 兴趣点策略 - 基于兴趣点预加载 */
  POINT_OF_INTEREST = 'poi',
  /** 预测策略 - 基于移动预测预加载 */
  PREDICTION = 'prediction',
  /** 自定义策略 */
  CUSTOM = 'custom'
}

/**
 * 预加载配置
 */
export interface PreloadConfig {
  /** 是否启用预加载 */
  enabled: boolean;
  /** 预加载优先级 */
  priority: number;
  /** 预加载策略类型 */
  strategyType: PreloadStrategyType;
  /** 预加载距离 */
  distance: number;
  /** 方向预加载角度（度） */
  directionAngle?: number;
  /** 预测时间（秒） */
  predictionTime?: number;
  /** 是否预加载子实体 */
  includeChildren: boolean;
  /** 是否预加载材质 */
  includeMaterials: boolean;
  /** 是否预加载纹理 */
  includeTextures: boolean;
  /** 是否预加载音频 */
  includeAudio: boolean;
  /** 是否预加载模型 */
  includeModels: boolean;
  /** 是否使用LOD */
  useLOD?: boolean;
  /** 是否使用纹理压缩 */
  useTextureCompression?: boolean;
  /** 是否使用几何体简化 */
  useGeometrySimplification?: boolean;
  /** 是否使用实例化 */
  useInstancing?: boolean;
  /** 最大同时加载资源数 */
  maxConcurrentLoads?: number;
  /** 内存限制（MB） */
  memoryLimit?: number;
  /** 自定义数据 */
  userData?: Record<string, any>;
}

/**
 * 资源依赖信息
 */
export interface ResourceDependencyInfo {
  /** 资源ID */
  id: string;
  /** 资源URL */
  url: string;
  /** 资源类型 */
  type: string;
  /** 依赖的资源ID列表 */
  dependencies: string[];
  /** 资源大小（字节） */
  size: number;
  /** 是否已加载 */
  loaded: boolean;
  /** 是否正在加载 */
  loading: boolean;
  /** 加载优先级 */
  priority: number;
}

/**
 * 预加载区域
 */
export interface PreloadRegion {
  /** 区域ID */
  id: string;
  /** 区域名称 */
  name: string;
  /** 区域中心位置 */
  position: THREE.Vector3;
  /** 区域半径 */
  radius: number;
  /** 预加载配置 */
  config: PreloadConfig;
  /** 区域内的实体ID列表 */
  entityIds: string[];
  /** 区域内的资源ID列表 */
  resourceIds: string[];
  /** 是否已加载 */
  loaded: boolean;
  /** 加载进度（0-1） */
  progress: number;
}

/**
 * 场景预加载管理器选项
 */
export interface ScenePreloadManagerOptions {
  /** 资产管理器 */
  assetManager: AssetManager;
  /** 场景预加载器 */
  scenePreloader?: ScenePreloader;
  /** 是否启用自动预加载 */
  enableAutoPreload?: boolean;
  /** 是否启用资源依赖分析 */
  enableDependencyAnalysis?: boolean;
  /** 是否启用按需加载 */
  enableOnDemandLoading?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 场景预加载管理器
 */
export class ScenePreloadManager extends EventEmitter {
  /** 资产管理器 */
  private assetManager: AssetManager;

  /** 场景预加载器 */
  private scenePreloader: ScenePreloader;

  /** 是否启用自动预加载 */
  private enableAutoPreload: boolean;

  /** 是否启用资源依赖分析 */
  private enableDependencyAnalysis: boolean;

  /** 是否启用按需加载 */
  private enableOnDemandLoading: boolean;

  /** 是否启用调试模式 */
  private debug: boolean;

  /** 预加载区域映射 */
  private regions: Map<string, PreloadRegion> = new Map();

  /** 资源依赖映射 */
  private resourceDependencies: Map<string, ResourceDependencyInfo> = new Map();

  /** 实体资源映射 */
  private entityResources: Map<string, Set<string>> = new Map();

  /** 当前活动场景 */
  private activeScene: Scene | null = null;

  /** 当前相机位置 */
  private cameraPosition: THREE.Vector3 = new THREE.Vector3();

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景预加载管理器
   * @param options 选项
   */
  constructor(options: ScenePreloadManagerOptions) {
    super();

    this.assetManager = options.assetManager;

    // 如果提供了场景预加载器，则使用它，否则创建一个新的
    this.scenePreloader = options.scenePreloader || new ScenePreloader({
      assetManager: this.assetManager,
      autoAnalyzeResources: true,
      autoRegisterResources: true
    });

    this.enableAutoPreload = options.enableAutoPreload !== undefined ? options.enableAutoPreload : true;
    this.enableDependencyAnalysis = options.enableDependencyAnalysis !== undefined ? options.enableDependencyAnalysis : true;
    this.enableOnDemandLoading = options.enableOnDemandLoading !== undefined ? options.enableOnDemandLoading : true;
    this.debug = options.debug !== undefined ? options.debug : false;
  }

  /**
   * 初始化预加载管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 初始化场景预加载器
    this.scenePreloader.initialize();

    // 监听场景预加载器事件
    this.scenePreloader.on('loadStart', (data) => {
      this.emit('loadStart', data);
    });

    this.scenePreloader.on('loadProgress', (data) => {
      this.emit('loadProgress', data);
    });

    this.scenePreloader.on('loadComplete', (data) => {
      this.emit('loadComplete', data);
    });

    this.scenePreloader.on('loadError', (data) => {
      this.emit('loadError', data);
    });

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 设置活动场景
   * @param scene 场景
   */
  public setActiveScene(scene: Scene | null): void {
    this.activeScene = scene;

    if (scene && this.enableAutoPreload) {
      // 分析场景资源
      this.analyzeSceneResources(scene);
    }
  }

  /**
   * 设置相机位置
   * @param position 位置
   */
  public setCameraPosition(position: THREE.Vector3): void {
    this.cameraPosition.copy(position);

    if (this.enableAutoPreload) {
      // 更新预加载区域
      this.updatePreloadRegions();
    }
  }

  /**
   * 分析场景资源
   * @param scene 场景
   */
  public analyzeSceneResources(scene: Scene): void {
    if (!scene.id) {
      console.warn('场景没有ID，无法分析资源');
      return;
    }

    if (this.debug) {
      console.log(`[ScenePreloadManager] 分析场景资源: ${scene.name} (${scene.id})`);
    }

    // 使用场景预加载器分析场景资源
    const resources = this.scenePreloader.analyzeSceneResources(scene);

    if (this.enableDependencyAnalysis) {
      // 分析资源依赖
      this.analyzeResourceDependencies(resources);
    }

    // 分析实体资源
    this.analyzeEntityResources(scene);

    // 发出资源分析完成事件
    this.emit('resourcesAnalyzed', { sceneId: scene.id, resources });
  }

  /**
   * 分析资源依赖
   * @param resources 资源信息数组
   */
  private analyzeResourceDependencies(resources: any[]): void {
    // 清空现有依赖
    this.resourceDependencies.clear();

    // 遍历资源
    for (const resource of resources) {
      // 创建依赖信息
      const dependencyInfo: ResourceDependencyInfo = {
        id: resource.id,
        url: resource.url,
        type: resource.type,
        dependencies: [],
        size: resource.size || 0,
        loaded: false,
        loading: false,
        priority: resource.priority || 0
      };

      // 添加到依赖映射
      this.resourceDependencies.set(resource.id, dependencyInfo);
    }

    // 分析依赖关系
    // 这里可以根据资源类型和URL分析依赖关系
    // 例如，材质依赖纹理，模型依赖材质等

    if (this.debug) {
      console.log(`[ScenePreloadManager] 分析资源依赖完成，共 ${this.resourceDependencies.size} 个资源`);
    }
  }

  /**
   * 分析实体资源
   * @param scene 场景
   */
  private analyzeEntityResources(scene: Scene): void {
    // 清空现有映射
    this.entityResources.clear();

    // 获取场景中的所有实体
    const entities = scene.getEntities();

    // 遍历实体
    for (const entity of entities) {
      this.analyzeEntityResourcesRecursive(entity);
    }

    if (this.debug) {
      console.log(`[ScenePreloadManager] 分析实体资源完成，共 ${this.entityResources.size} 个实体`);
    }
  }

  /**
   * 递归分析实体资源
   * @param entity 实体
   */
  private analyzeEntityResourcesRecursive(entity: Entity): void {
    // 创建实体资源集合
    const resourceIds = new Set<string>();

    // 获取实体的所有组件
    const components = entity.getAllComponents();

    // 遍历组件
    for (const component of components) {
      // 根据组件类型获取资源
      const componentType = component.getType();

      // 这里可以根据组件类型获取资源
      // 例如，MeshComponent 包含几何体和材质资源
      // AudioComponent 包含音频资源等

      // 将资源ID添加到集合中
      // resourceIds.add(resourceId);
    }

    // 添加到实体资源映射
    if (resourceIds.size > 0) {
      this.entityResources.set(entity.id, resourceIds);
    }

    // 递归处理子实体
    const children = entity.getChildren();
    for (const child of children) {
      this.analyzeEntityResourcesRecursive(child);
    }
  }

  /**
   * 创建预加载区域
   * @param options 区域选项
   * @returns 区域ID
   */
  public createPreloadRegion(options: Partial<PreloadRegion>): string {
    // 生成区域ID
    const regionId = options.id || `region_${Date.now()}`;

    // 创建区域
    const region: PreloadRegion = {
      id: regionId,
      name: options.name || `Region ${regionId}`,
      position: options.position || new THREE.Vector3(),
      radius: options.radius || 100,
      config: options.config || {
        enabled: true,
        priority: 1,
        strategyType: PreloadStrategyType.DISTANCE,
        distance: 100,
        includeChildren: true,
        includeMaterials: true,
        includeTextures: true,
        includeAudio: true,
        includeModels: true,
        useLOD: false,
        useTextureCompression: false,
        useGeometrySimplification: false,
        useInstancing: false,
        maxConcurrentLoads: 5,
        memoryLimit: 256
      },
      entityIds: options.entityIds || [],
      resourceIds: options.resourceIds || [],
      loaded: false,
      progress: 0
    };

    // 添加到区域映射
    this.regions.set(regionId, region);

    // 发出区域创建事件
    this.emit('regionCreated', region);

    return regionId;
  }

  /**
   * 更新预加载区域
   */
  private updatePreloadRegions(): void {
    if (!this.activeScene) {
      return;
    }

    // 获取相机
    const camera = this.activeScene.getActiveCamera();
    if (!camera) {
      return;
    }

    // 获取相机方向
    const cameraDirection = new THREE.Vector3(0, 0, -1);
    cameraDirection.applyQuaternion(camera.quaternion);

    // 获取视锥体
    const frustum = new THREE.Frustum();
    const projScreenMatrix = new THREE.Matrix4();
    projScreenMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
    frustum.setFromProjectionMatrix(projScreenMatrix);

    // 遍历所有区域
    for (const region of Array.from(this.regions.values())) {
      if (!region.config.enabled) {
        continue;
      }

      // 根据预加载策略类型选择不同的预加载方法
      switch (region.config.strategyType) {
        case PreloadStrategyType.DISTANCE:
          this.updateRegionByDistance(region, camera);
          break;

        case PreloadStrategyType.FRUSTUM:
          this.updateRegionByFrustum(region, frustum);
          break;

        case PreloadStrategyType.DIRECTION:
          this.updateRegionByDirection(region, camera, cameraDirection);
          break;

        case PreloadStrategyType.HIERARCHY:
          this.updateRegionByHierarchy(region);
          break;

        case PreloadStrategyType.POINT_OF_INTEREST:
          this.updateRegionByPOI(region);
          break;

        case PreloadStrategyType.PREDICTION:
          this.updateRegionByPrediction(region, camera);
          break;

        case PreloadStrategyType.CUSTOM:
          this.updateRegionByCustom(region);
          break;

        default:
          // 默认使用距离策略
          this.updateRegionByDistance(region, camera);
          break;
      }
    }
  }

  /**
   * 基于距离更新区域
   * @param region 区域
   * @param camera 相机
   */
  private updateRegionByDistance(region: PreloadRegion, camera: THREE.Camera): void {
    // 计算相机到区域中心的距离
    const distance = this.cameraPosition.distanceTo(region.position);

    // 如果距离小于预加载距离，则加载区域
    if (distance < region.config.distance) {
      this.loadRegion(region.id);
    } else if (distance > region.config.distance * 1.5) {
      // 如果距离超过预加载距离的1.5倍，则卸载区域
      this.unloadRegion(region.id);
    }
  }

  /**
   * 基于视锥体更新区域
   * @param region 区域
   * @param frustum 视锥体
   */
  private updateRegionByFrustum(region: PreloadRegion, frustum: THREE.Frustum): void {
    // 创建包围球
    const sphere = new THREE.Sphere(region.position, region.radius);

    // 检查包围球是否与视锥体相交
    const isIntersect = frustum.intersectsSphere(sphere);

    if (isIntersect) {
      this.loadRegion(region.id);
    } else {
      // 如果不在视锥体内，检查距离
      const distance = this.cameraPosition.distanceTo(region.position);

      // 如果距离小于预加载距离，也加载区域
      if (distance < region.config.distance) {
        this.loadRegion(region.id);
      } else {
        this.unloadRegion(region.id);
      }
    }
  }

  /**
   * 基于方向更新区域
   * @param region 区域
   * @param camera 相机
   * @param cameraDirection 相机方向
   */
  private updateRegionByDirection(
    region: PreloadRegion,
    camera: THREE.Camera,
    cameraDirection: THREE.Vector3
  ): void {
    // 计算相机到区域中心的方向
    const directionToRegion = new THREE.Vector3();
    directionToRegion.subVectors(region.position, this.cameraPosition).normalize();

    // 计算方向夹角（弧度）
    const angle = cameraDirection.angleTo(directionToRegion);

    // 转换为角度
    const angleDegrees = THREE.MathUtils.radToDeg(angle);

    // 获取方向角度阈值
    const directionAngle = region.config.directionAngle || 45;

    // 如果夹角小于阈值，则加载区域
    if (angleDegrees < directionAngle) {
      // 计算距离
      const distance = this.cameraPosition.distanceTo(region.position);

      // 如果距离也在范围内，则加载区域
      if (distance < region.config.distance) {
        this.loadRegion(region.id);
      }
    } else {
      this.unloadRegion(region.id);
    }
  }

  /**
   * 基于层级更新区域
   * @param region 区域
   */
  private updateRegionByHierarchy(region: PreloadRegion): void {
    if (!this.activeScene) {
      return;
    }

    // 获取场景中的所有实体
    const entities = this.activeScene.getEntities();

    // 检查区域内的实体是否有活动的父实体
    let hasActiveParent = false;

    for (const entityId of region.entityIds) {
      const entity = entities.find(e => e.id === entityId);

      if (entity) {
        // 检查父实体链是否有活动的实体
        let parent = entity.getParent();

        while (parent) {
          if (parent.isActive()) {
            hasActiveParent = true;
            break;
          }

          parent = parent.getParent();
        }

        if (hasActiveParent) {
          break;
        }
      }
    }

    // 如果有活动的父实体，则加载区域
    if (hasActiveParent) {
      this.loadRegion(region.id);
    } else {
      this.unloadRegion(region.id);
    }
  }

  /**
   * 基于兴趣点更新区域
   * @param region 区域
   */
  private updateRegionByPOI(region: PreloadRegion): void {
    // 获取兴趣点数据
    const poiData = region.config.userData?.poi;

    if (!poiData) {
      return;
    }

    // 检查兴趣点是否活动
    const isActive = poiData.active === true;

    // 如果兴趣点活动，则加载区域
    if (isActive) {
      this.loadRegion(region.id);
    } else {
      this.unloadRegion(region.id);
    }
  }

  /**
   * 基于预测更新区域
   * @param region 区域
   * @param camera 相机
   */
  private updateRegionByPrediction(region: PreloadRegion, camera: THREE.Camera): void {
    // 获取预测时间
    const predictionTime = region.config.predictionTime || 2.0; // 默认2秒

    // 获取相机速度（如果有）
    const cameraVelocity = (camera as any).velocity as THREE.Vector3 || new THREE.Vector3();

    // 预测未来位置
    const predictedPosition = new THREE.Vector3();
    predictedPosition.copy(this.cameraPosition);
    predictedPosition.addScaledVector(cameraVelocity, predictionTime);

    // 计算预测位置到区域中心的距离
    const distance = predictedPosition.distanceTo(region.position);

    // 如果预测位置距离小于预加载距离，则加载区域
    if (distance < region.config.distance) {
      this.loadRegion(region.id);
    } else {
      this.unloadRegion(region.id);
    }
  }

  /**
   * 基于自定义策略更新区域
   * @param region 区域
   */
  private updateRegionByCustom(region: PreloadRegion): void {
    // 获取自定义策略函数
    const customStrategy = region.config.userData?.customStrategy;

    if (typeof customStrategy === 'function') {
      // 调用自定义策略函数
      const shouldLoad = customStrategy(region, this.cameraPosition, this.activeScene);

      if (shouldLoad) {
        this.loadRegion(region.id);
      } else {
        this.unloadRegion(region.id);
      }
    }
  }

  /**
   * 加载区域
   * @param regionId 区域ID
   */
  public loadRegion(regionId: string): void {
    const region = this.regions.get(regionId);

    if (!region) {
      console.warn(`找不到预加载区域: ${regionId}`);
      return;
    }

    if (region.loaded) {
      return;
    }

    if (this.debug) {
      console.log(`[ScenePreloadManager] 加载区域: ${region.name} (${region.id})`);
    }

    // 标记为正在加载
    region.loaded = false;
    region.progress = 0;

    // 发出区域加载开始事件
    this.emit('regionLoadStart', region);

    // 加载区域资源
    this.loadRegionResources(region);
  }

  /**
   * 加载区域资源
   * @param region 区域
   */
  private loadRegionResources(region: PreloadRegion): void {
    // 获取区域内的所有资源ID
    const resourceIds = new Set<string>();

    // 添加区域直接关联的资源
    for (const resourceId of region.resourceIds) {
      resourceIds.add(resourceId);
    }

    // 添加区域内实体关联的资源
    for (const entityId of region.entityIds) {
      const entityResources = this.entityResources.get(entityId);

      if (entityResources) {
        for (const resourceId of Array.from(entityResources)) {
          resourceIds.add(resourceId);
        }
      }
    }

    // 如果启用依赖分析，添加资源依赖
    if (this.enableDependencyAnalysis) {
      const dependencyIds = new Set<string>();

      for (const resourceId of Array.from(resourceIds)) {
        const dependencyInfo = this.resourceDependencies.get(resourceId);

        if (dependencyInfo) {
          for (const dependencyId of dependencyInfo.dependencies) {
            dependencyIds.add(dependencyId);
          }
        }
      }

      // 添加依赖资源
      for (const dependencyId of Array.from(dependencyIds)) {
        resourceIds.add(dependencyId);
      }
    }

    // 应用优化选项
    const loadOptions: any = {
      priority: region.config.priority || 1
    };

    // 设置最大同时加载资源数
    if (region.config.maxConcurrentLoads) {
      loadOptions.maxConcurrentLoads = region.config.maxConcurrentLoads;
    }

    // 设置内存限制
    if (region.config.memoryLimit) {
      loadOptions.memoryLimit = region.config.memoryLimit;
    }

    // 设置LOD级别
    if (region.config.useLOD) {
      loadOptions.useLOD = true;

      // 根据距离计算LOD级别
      const distance = this.cameraPosition.distanceTo(region.position);
      const distanceRatio = distance / region.config.distance;

      // LOD级别：0（最高质量）到3（最低质量）
      if (distanceRatio < 0.3) {
        loadOptions.lodLevel = 0; // 最高质量
      } else if (distanceRatio < 0.6) {
        loadOptions.lodLevel = 1; // 高质量
      } else if (distanceRatio < 0.8) {
        loadOptions.lodLevel = 2; // 中等质量
      } else {
        loadOptions.lodLevel = 3; // 低质量
      }
    }

    // 设置纹理压缩
    if (region.config.useTextureCompression) {
      loadOptions.useTextureCompression = true;
    }

    // 设置几何体简化
    if (region.config.useGeometrySimplification) {
      loadOptions.useGeometrySimplification = true;
    }

    // 设置实例化
    if (region.config.useInstancing) {
      loadOptions.useInstancing = true;
    }

    // 加载资源
    const resourceArray = Array.from(resourceIds);

    if (resourceArray.length > 0) {
      // 发出区域加载开始事件
      this.emit('regionLoadStart', region);

      // 更新区域状态
      region.loaded = false;
      region.progress = 0;

      // 使用场景预加载器加载资源
      this.scenePreloader.loadResources(resourceArray, {
        onProgress: (progress) => {
          // 更新区域加载进度
          region.progress = progress.progress;

          // 发出区域加载进度事件
          this.emit('regionLoadProgress', region, progress);
        },
        onComplete: () => {
          // 更新区域状态
          region.loaded = true;
          region.progress = 1;

          // 发出区域加载完成事件
          this.emit('regionLoadComplete', region);
        },
        onError: (error) => {
          console.error(`加载区域 ${region.id} 资源失败:`, error);

          // 发出区域加载错误事件
          this.emit('regionLoadError', region, error);
        },
        ...loadOptions
      });
    } else {
      // 没有资源需要加载
      region.loaded = true;
      region.progress = 1;

      // 发出区域加载完成事件
      this.emit('regionLoadComplete', region);
    }
  }

  /**
   * 卸载区域
   * @param regionId 区域ID
   */
  public unloadRegion(regionId: string): void {
    const region = this.regions.get(regionId);

    if (!region) {
      console.warn(`找不到预加载区域: ${regionId}`);
      return;
    }

    if (!region.loaded) {
      return;
    }

    if (this.debug) {
      console.log(`[ScenePreloadManager] 卸载区域: ${region.name} (${region.id})`);
    }

    // 标记为未加载
    region.loaded = false;
    region.progress = 0;

    // 发出区域卸载事件
    this.emit('regionUnloaded', region);

    // 卸载区域资源
    // 这里可以使用资产管理器卸载资源
  }

  /**
   * 预加载场景
   * @param scene 场景
   * @param onProgress 进度回调
   * @returns Promise，解析为加载结果
   */
  public async preloadScene(
    scene: Scene,
    onProgress?: (progress: ScenePreloadProgressInfo) => void
  ): Promise<ScenePreloadProgressInfo> {
    return this.scenePreloader.preloadScene(scene, onProgress);
  }

  /**
   * 销毁预加载管理器
   */
  public dispose(): void {
    // 清空所有区域
    this.regions.clear();

    // 清空资源依赖
    this.resourceDependencies.clear();

    // 清空实体资源
    this.entityResources.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
