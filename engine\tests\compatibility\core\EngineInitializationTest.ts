/**
 * 引擎初始化兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 引擎初始化兼容性测试
 */
export const engineInitializationTest: TestCase = {
  name: '引擎初始化兼容性测试',
  description: '测试引擎初始化功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目引擎实例
      const originalEngine = new original.Engine();
      
      // 创建重构后项目引擎实例
      const refactoredEngine = new refactored.Engine();
      
      // 检查引擎实例是否创建成功
      if (!originalEngine || !refactoredEngine) {
        return {
          name: '引擎初始化兼容性测试',
          passed: false,
          errorMessage: '引擎实例创建失败'
        };
      }
      
      // 检查引擎实例的属性和方法
      const originalMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(originalEngine));
      const refactoredMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(refactoredEngine));
      
      // 检查重构后的引擎是否包含原有引擎的所有方法
      const missingMethods = originalMethods.filter(method => {
        // 忽略构造函数和私有方法
        if (method === 'constructor' || method.startsWith('_')) {
          return false;
        }
        
        return !refactoredMethods.includes(method);
      });
      
      if (missingMethods.length > 0) {
        return {
          name: '引擎初始化兼容性测试',
          passed: false,
          errorMessage: `重构后的引擎缺少以下方法: ${missingMethods.join(', ')}`,
          details: {
            originalMethods,
            refactoredMethods,
            missingMethods
          }
        };
      }
      
      // 检查引擎初始化参数
      const originalOptions = { width: 800, height: 600, antialias: true };
      const refactoredOptions = { width: 800, height: 600, antialias: true };
      
      // 使用相同的参数初始化引擎
      originalEngine.initialize(originalOptions);
      refactoredEngine.initialize(refactoredOptions);
      
      // 检查初始化后的状态
      const originalInitialized = originalEngine.isInitialized();
      const refactoredInitialized = refactoredEngine.isInitialized();
      
      if (originalInitialized !== refactoredInitialized) {
        return {
          name: '引擎初始化兼容性测试',
          passed: false,
          errorMessage: `初始化状态不一致: 原有项目=${originalInitialized}, 重构后项目=${refactoredInitialized}`,
          details: {
            originalInitialized,
            refactoredInitialized
          }
        };
      }
      
      return {
        name: '引擎初始化兼容性测试',
        passed: true,
        details: {
          originalMethods,
          refactoredMethods
        }
      };
    } catch (error) {
      return {
        name: '引擎初始化兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
