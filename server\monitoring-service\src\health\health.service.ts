import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { HealthCheckEntity, HealthCheckStatus, HealthCheckType } from './entities/health-check.entity';
import { HealthCheckHistoryEntity } from './entities/health-check-history.entity';
import { ServiceHealthCheckService } from './service-health-check.service';
import { AutoRecoveryService } from './auto-recovery.service';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly healthChecks = new Map<string, NodeJS.Timeout>();

  constructor(
    @InjectRepository(HealthCheckEntity)
    private readonly healthCheckRepository: Repository<HealthCheckEntity>,
    @InjectRepository(HealthCheckHistoryEntity)
    private readonly historyRepository: Repository<HealthCheckHistoryEntity>,
    private readonly serviceHealthCheckService: ServiceHealthCheckService,
    private readonly autoRecoveryService: AutoRecoveryService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 初始化健康检查
   */
  async initHealthChecks(): Promise<void> {
    try {
      // 清除所有现有的健康检查定时器
      for (const [id, timer] of this.healthChecks.entries()) {
        clearTimeout(timer);
        this.healthChecks.delete(id);
      }
      
      // 获取所有启用的健康检查
      const healthChecks = await this.healthCheckRepository.find({
        where: { enabled: true },
      });
      
      // 为每个健康检查设置定时器
      for (const healthCheck of healthChecks) {
        this.scheduleHealthCheck(healthCheck);
      }
      
      this.logger.log(`已初始化 ${healthChecks.length} 个健康检查`);
    } catch (error) {
      this.logger.error(`初始化健康检查失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 定时重新加载健康检查
   */
  @Cron(CronExpression.EVERY_HOUR)
  async reloadHealthChecks(): Promise<void> {
    await this.initHealthChecks();
  }

  /**
   * 安排健康检查
   */
  private scheduleHealthCheck(healthCheck: HealthCheckEntity): void {
    // 计算下次检查时间
    const interval = healthCheck.interval * 1000; // 转换为毫秒
    
    // 设置定时器
    const timer = setTimeout(async () => {
      try {
        await this.executeHealthCheck(healthCheck);
      } catch (error) {
        this.logger.error(`执行健康检查 ${healthCheck.id} 失败: ${error.message}`, error.stack);
      } finally {
        // 重新安排下一次检查
        this.scheduleHealthCheck(healthCheck);
      }
    }, interval);
    
    // 存储定时器
    this.healthChecks.set(healthCheck.id, timer);
  }

  /**
   * 执行健康检查
   */
  async executeHealthCheck(healthCheck: HealthCheckEntity): Promise<HealthCheckHistoryEntity> {
    try {
      const startTime = Date.now();
      let result: any;
      
      // 根据健康检查类型执行检查
      switch (healthCheck.type) {
        case HealthCheckType.HTTP:
          result = await this.serviceHealthCheckService.checkHttp(healthCheck);
          break;
        case HealthCheckType.TCP:
          result = await this.serviceHealthCheckService.checkTcp(healthCheck);
          break;
        case HealthCheckType.SCRIPT:
          result = await this.serviceHealthCheckService.checkScript(healthCheck);
          break;
        case HealthCheckType.CUSTOM:
          result = await this.serviceHealthCheckService.checkCustom(healthCheck);
          break;
        default:
          throw new Error(`不支持的健康检查类型: ${healthCheck.type}`);
      }
      
      const responseTime = Date.now() - startTime;
      
      // 创建健康检查历史记录
      const history = this.historyRepository.create({
        healthCheckId: healthCheck.id,
        status: result.status,
        responseTime,
        details: result.details,
        metadata: result.metadata,
      });
      
      await this.historyRepository.save(history);
      
      // 更新健康检查状态
      await this.updateHealthCheckStatus(healthCheck, result.status);
      
      // 如果状态为不健康，尝试自动恢复
      if (result.status === HealthCheckStatus.UNHEALTHY && healthCheck.autoRecoveryEnabled) {
        await this.triggerAutoRecovery(healthCheck, history);
      }
      
      return history;
    } catch (error) {
      this.logger.error(`执行健康检查 ${healthCheck.id} 失败: ${error.message}`, error.stack);
      
      // 创建失败的健康检查历史记录
      const history = this.historyRepository.create({
        healthCheckId: healthCheck.id,
        status: HealthCheckStatus.UNKNOWN,
        responseTime: 0,
        details: `健康检查执行失败: ${error.message}`,
      });
      
      await this.historyRepository.save(history);
      
      // 更新健康检查状态
      await this.updateHealthCheckStatus(healthCheck, HealthCheckStatus.UNKNOWN);
      
      return history;
    }
  }

  /**
   * 更新健康检查状态
   */
  private async updateHealthCheckStatus(
    healthCheck: HealthCheckEntity,
    status: HealthCheckStatus,
  ): Promise<void> {
    try {
      // 获取最新的健康检查信息
      const check = await this.healthCheckRepository.findOne({ where: { id: healthCheck.id } });
      
      if (!check) {
        throw new Error(`未找到健康检查: ${healthCheck.id}`);
      }
      
      // 更新成功/失败计数
      if (status === HealthCheckStatus.HEALTHY) {
        check.successCount++;
        check.failureCount = 0;
      } else if (status === HealthCheckStatus.UNHEALTHY) {
        check.failureCount++;
        check.successCount = 0;
      }
      
      // 根据阈值确定最终状态
      let finalStatus = check.status;
      
      if (check.successCount >= check.healthyThreshold) {
        finalStatus = HealthCheckStatus.HEALTHY;
      } else if (check.failureCount >= check.unhealthyThreshold) {
        finalStatus = HealthCheckStatus.UNHEALTHY;
      }
      
      // 检查状态是否变化
      const statusChanged = check.status !== finalStatus;
      
      // 更新健康检查
      check.status = finalStatus;
      check.lastCheckTime = new Date();
      check.lastCheckResult = status;
      
      await this.healthCheckRepository.save(check);
      
      // 如果状态发生变化，触发事件
      if (statusChanged) {
        this.eventEmitter.emit('health.status.changed', {
          id: check.id,
          name: check.name,
          serviceId: check.serviceId,
          serviceType: check.serviceType,
          instanceId: check.instanceId,
          hostname: check.hostname,
          previousStatus: healthCheck.status,
          currentStatus: finalStatus,
          timestamp: new Date(),
        });
      }
    } catch (error) {
      this.logger.error(`更新健康检查状态失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 触发自动恢复
   */
  private async triggerAutoRecovery(
    healthCheck: HealthCheckEntity,
    history: HealthCheckHistoryEntity,
  ): Promise<void> {
    try {
      // 检查是否启用自动恢复
      if (!healthCheck.autoRecoveryEnabled) {
        return;
      }
      
      // 获取最新的健康检查信息
      const check = await this.healthCheckRepository.findOne({ where: { id: healthCheck.id } });
      
      if (!check) {
        throw new Error(`未找到健康检查: ${healthCheck.id}`);
      }
      
      // 检查是否达到自动恢复条件
      if (check.status !== HealthCheckStatus.UNHEALTHY || check.failureCount < check.unhealthyThreshold) {
        return;
      }
      
      // 执行自动恢复
      const recoveryResult = await this.autoRecoveryService.recover(check);
      
      // 更新健康检查历史记录
      history.autoRecoveryTriggered = true;
      history.autoRecoveryAction = recoveryResult.action;
      history.autoRecoveryResult = recoveryResult.result;
      
      await this.historyRepository.save(history);
      
      // 更新健康检查
      check.autoRecoveryAttempts++;
      check.lastAutoRecoveryTime = new Date();
      
      await this.healthCheckRepository.save(check);
      
      // 触发自动恢复事件
      this.eventEmitter.emit('health.auto.recovery', {
        id: check.id,
        name: check.name,
        serviceId: check.serviceId,
        serviceType: check.serviceType,
        instanceId: check.instanceId,
        hostname: check.hostname,
        action: recoveryResult.action,
        result: recoveryResult.result,
        success: recoveryResult.success,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`触发自动恢复失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取所有健康检查
   */
  async getAllHealthChecks(filters: {
    status?: HealthCheckStatus;
    serviceId?: string;
    serviceType?: string;
    instanceId?: string;
    hostname?: string;
  }): Promise<HealthCheckEntity[]> {
    try {
      const query = this.healthCheckRepository.createQueryBuilder('check');
      
      if (filters.status) {
        query.andWhere('check.status = :status', { status: filters.status });
      }
      
      if (filters.serviceId) {
        query.andWhere('check.serviceId = :serviceId', { serviceId: filters.serviceId });
      }
      
      if (filters.serviceType) {
        query.andWhere('check.serviceType = :serviceType', { serviceType: filters.serviceType });
      }
      
      if (filters.instanceId) {
        query.andWhere('check.instanceId = :instanceId', { instanceId: filters.instanceId });
      }
      
      if (filters.hostname) {
        query.andWhere('check.hostname = :hostname', { hostname: filters.hostname });
      }
      
      query.orderBy('check.lastCheckTime', 'DESC');
      
      return query.getMany();
    } catch (error) {
      this.logger.error(`获取健康检查失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取健康检查历史记录
   */
  async getHealthCheckHistory(
    healthCheckId: string,
    limit: number = 100,
  ): Promise<HealthCheckHistoryEntity[]> {
    try {
      return this.historyRepository.find({
        where: { healthCheckId },
        order: { createdAt: 'DESC' },
        take: limit,
      });
    } catch (error) {
      this.logger.error(`获取健康检查历史记录失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取健康检查统计信息
   */
  async getHealthCheckStats(): Promise<{
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
    unknown: number;
    byService: Record<string, number>;
  }> {
    try {
      const [total, healthy, unhealthy, degraded, unknown] = await Promise.all([
        this.healthCheckRepository.count(),
        this.healthCheckRepository.count({ where: { status: HealthCheckStatus.HEALTHY } }),
        this.healthCheckRepository.count({ where: { status: HealthCheckStatus.UNHEALTHY } }),
        this.healthCheckRepository.count({ where: { status: HealthCheckStatus.DEGRADED } }),
        this.healthCheckRepository.count({ where: { status: HealthCheckStatus.UNKNOWN } }),
      ]);
      
      const byServiceResult = await this.healthCheckRepository
        .createQueryBuilder('check')
        .select('check.serviceType, COUNT(*) as count')
        .groupBy('check.serviceType')
        .getRawMany();
      
      const byService: Record<string, number> = {};
      
      for (const item of byServiceResult) {
        byService[item.serviceType || 'unknown'] = parseInt(item.count, 10);
      }
      
      return {
        total,
        healthy,
        unhealthy,
        degraded,
        unknown,
        byService,
      };
    } catch (error) {
      this.logger.error(`获取健康检查统计信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取系统健康状态
   */
  async getSystemHealth(): Promise<any> {
    try {
      const stats = await this.getHealthCheckStats();
      const overallStatus = this.calculateOverallStatus(stats);

      return {
        status: overallStatus,
        timestamp: new Date(),
        checks: stats,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version,
      };
    } catch (error) {
      this.logger.error(`获取系统健康状态失败: ${error.message}`, error.stack);
      return {
        status: 'unhealthy',
        timestamp: new Date(),
        error: error.message,
      };
    }
  }

  /**
   * 计算整体健康状态
   */
  private calculateOverallStatus(stats: {
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
    unknown: number;
  }): string {
    if (stats.total === 0) {
      return 'unknown';
    }

    const healthyPercentage = stats.healthy / stats.total;

    if (healthyPercentage >= 0.9) {
      return 'healthy';
    } else if (healthyPercentage >= 0.7) {
      return 'degraded';
    } else {
      return 'unhealthy';
    }
  }
}
