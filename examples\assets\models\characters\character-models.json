{"version": "1.0.0", "models": [{"id": "male_standard", "name": "标准男性角色", "path": "male_character.glb", "thumbnail": "thumbnails/male_character.jpg", "description": "标准男性角色模型，包含完整骨骼结构和面部混合形状", "tags": ["男性", "人类", "标准"], "features": ["面部动画", "口型同步"], "polyCount": 15000, "textureSize": 2048, "blendShapes": {"expressions": ["smile", "angry", "sad", "surprise", "fear", "disgust"], "visemes": ["aa", "ch", "dd", "e", "ff", "ih", "k", "nn", "oh", "ou", "pp", "r", "ss", "th"]}}, {"id": "female_standard", "name": "标准女性角色", "path": "female_character.glb", "thumbnail": "thumbnails/female_character.jpg", "description": "标准女性角色模型，包含完整骨骼结构和面部混合形状", "tags": ["女性", "人类", "标准"], "features": ["面部动画", "口型同步"], "polyCount": 15000, "textureSize": 2048, "blendShapes": {"expressions": ["smile", "angry", "sad", "surprise", "fear", "disgust"], "visemes": ["aa", "ch", "dd", "e", "ff", "ih", "k", "nn", "oh", "ou", "pp", "r", "ss", "th"]}}, {"id": "robot_character", "name": "机器人角色", "path": "robot_character.glb", "thumbnail": "thumbnails/robot_character.jpg", "description": "机器人角色模型，包含特殊关节结构和金属材质", "tags": ["机器人", "科幻", "非人类"], "features": ["面部动画", "特殊关节"], "polyCount": 12000, "textureSize": 2048, "blendShapes": {"expressions": ["happy", "alert", "idle", "scan", "power_down"], "visemes": ["aa", "ch", "dd", "e", "ff", "ih", "k", "nn", "oh", "ou", "pp", "r", "ss", "th"]}}, {"id": "cartoon_character", "name": "卡通角色", "path": "cartoon_character.glb", "thumbnail": "thumbnails/cartoon_character.jpg", "description": "卡通风格角色模型，适合儿童内容和游戏", "tags": ["卡通", "风格化", "低多边形"], "features": ["夸张表情", "简化骨骼"], "polyCount": 8000, "textureSize": 1024, "blendShapes": {"expressions": ["happy", "sad", "angry", "surprised", "wink", "laugh"], "visemes": ["aa", "ch", "dd", "e", "ff", "ih", "k", "nn", "oh", "ou", "pp", "r", "ss", "th"]}}, {"id": "fantasy_character", "name": "奇幻角色", "path": "fantasy_character.glb", "thumbnail": "thumbnails/fantasy_character.jpg", "description": "奇幻风格角色模型，包含非人类特征和华丽装饰", "tags": ["奇幻", "非人类", "精灵"], "features": ["特殊表情", "非人类骨骼"], "polyCount": 18000, "textureSize": 2048, "blendShapes": {"expressions": ["smile", "angry", "sad", "surprise", "magic", "meditate"], "visemes": ["aa", "ch", "dd", "e", "ff", "ih", "k", "nn", "oh", "ou", "pp", "r", "ss", "th"]}}]}