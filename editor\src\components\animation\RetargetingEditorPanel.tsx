/**
 * 动画重定向编辑器面板
 * 用于集成到编辑器中
 */
import React, { useState, useEffect, useRef } from 'react';
import { Layout, Button, Select, Upload, message, Space, Spin} from 'antd';
import {
  UploadOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CloseOutlined,
  ImportOutlined,
  ExportOutlined,
  SyncOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import * as THREE from 'three';
// 移除引擎直接导入
import RetargetingEditor from './RetargetingEditor';
import './AnimationEditor.less';

const { Header, Content } = Layout;
const { Option } = Select;

/**
 * 骨骼映射接口
 */
interface BoneMapping {
  /** 源骨骼名称 */
  source: string;
  /** 目标骨骼名称 */
  target: string;
}

/**
 * 重定向配置接口
 */
interface RetargetingConfig {
  /** 骨骼映射 */
  boneMapping: BoneMapping[];
  /** 保留位置轨道 */
  preservePositionTracks: boolean;
  /** 保留缩放轨道 */
  preserveScaleTracks: boolean;
  /** 标准化旋转 */
  normalizeRotations: boolean;
  /** 调整根骨骼高度 */
  adjustRootHeight: boolean;
  /** 调整骨骼长度 */
  adjustBoneLength: boolean;
}

/**
 * 重定向编辑器面板属性
 */
interface RetargetingEditorPanelProps {
  /** 是否可见 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 保存回调 */
  onSave?: (data: RetargetingResult) => void;
  /** 初始数据 */
  initialData?: RetargetingResult;
}

/**
 * 重定向结果
 */
export interface RetargetingResult {
  /** 骨骼映射 */
  boneMapping: BoneMapping[];
  /** 重定向配置 */
  config: RetargetingConfig;
  /** 源模型 */
  sourceModel?: THREE.Object3D;
  /** 目标模型 */
  targetModel?: THREE.Object3D;
  /** 源动画片段 */
  sourceClip?: THREE.AnimationClip;
  /** 重定向后的动画片段 */
  retargetedClip?: THREE.AnimationClip;
}

/**
 * 动画重定向编辑器面板组件
 */
const RetargetingEditorPanel: React.FC<RetargetingEditorPanelProps> = ({
  visible,
  onClose,
  onSave,
  initialData}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [sourceModel, setSourceModel] = useState<THREE.Object3D | null>(null);
  const [targetModel, setTargetModel] = useState<THREE.Object3D | null>(null);
  const [sourceSkeleton, setSourceSkeleton] = useState<THREE.Skeleton | null>(null);
  const [targetSkeleton, setTargetSkeleton] = useState<THREE.Skeleton | null>(null);
  const [sourceClip, setSourceClip] = useState<THREE.AnimationClip | null>(null);
  const [sourceClips, setSourceClips] = useState<THREE.AnimationClip[]>([]);
  const [selectedClipName, setSelectedClipName] = useState<string>('');
  const [boneMapping, setBoneMapping] = useState<BoneMapping[]>([]);
  const [config, setConfig] = useState<RetargetingConfig>({
    boneMapping: [],
    preservePositionTracks: true,
    preserveScaleTracks: false,
    normalizeRotations: true,
    adjustRootHeight: true,
    adjustBoneLength: true});
  const [isPlaying, setIsPlaying] = useState(false);
  const [retargetedClip, setRetargetedClip] = useState<THREE.AnimationClip | null>(null);
  const previewContainerRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const animationMixerRef = useRef<THREE.AnimationMixer | null>(null);
  const clockRef = useRef<THREE.Clock | null>(null);
  const animationRef = useRef<number | null>(null);

  // 初始化
  useEffect(() => {
    if (visible) {
      if (initialData) {
        // 加载初始数据
        setBoneMapping(initialData.boneMapping || []);
        setConfig(initialData.config || config);
        if (initialData.sourceModel) setSourceModel(initialData.sourceModel);
        if (initialData.targetModel) setTargetModel(initialData.targetModel);
        if (initialData.sourceClip) {
          setSourceClip(initialData.sourceClip);
          setSourceClips([initialData.sourceClip]);
          setSelectedClipName(initialData.sourceClip.name);
        }
        if (initialData.retargetedClip) setRetargetedClip(initialData.retargetedClip);
      }

      // 初始化预览
      if (previewContainerRef.current) {
        initPreview();
      }
    } else {
      // 清理预览
      cleanupPreview();
    }

    return () => {
      cleanupPreview();
    };
  }, [visible, initialData]);

  // 初始化预览
  const initPreview = () => {
    if (!previewContainerRef.current) return;

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x1e1e1e);
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(
      50,
      previewContainerRef.current.clientWidth / previewContainerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 1.5, 3);
    camera.lookAt(0, 1, 0);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(previewContainerRef.current.clientWidth, previewContainerRef.current.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.shadowMap.enabled = true;
    previewContainerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // 添加灯光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // 添加网格
    const gridHelper = new THREE.GridHelper(10, 10);
    scene.add(gridHelper);

    // 创建时钟
    clockRef.current = new THREE.Clock();

    // 添加模型
    if (sourceModel) scene.add(sourceModel.clone());
    if (targetModel) scene.add(targetModel.clone());

    // 开始动画循环
    animate();
  };

  // 动画循环
  const animate = () => {
    if (!rendererRef.current || !sceneRef.current || !cameraRef.current) return;

    animationRef.current = requestAnimationFrame(animate);

    // 更新动画混合器
    if (animationMixerRef.current && clockRef.current && isPlaying) {
      const delta = clockRef.current.getDelta();
      animationMixerRef.current.update(delta);
    }

    // 渲染场景
    rendererRef.current.render(sceneRef.current, cameraRef.current);
  };

  // 清理预览
  const cleanupPreview = () => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = null;
    }

    if (rendererRef.current && previewContainerRef.current) {
      previewContainerRef.current.removeChild(rendererRef.current.domElement);
      rendererRef.current.dispose();
      rendererRef.current = null;
    }

    if (animationMixerRef.current) {
      animationMixerRef.current = null;
    }

    sceneRef.current = null;
    cameraRef.current = null;
    clockRef.current = null;
  };

  // 加载源模型
  const handleLoadSourceModel = (file: File) => {
    setLoading(true);
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target?.result) {
        setLoading(false);
        return;
      }

      const extension = file.name.split('.').pop()?.toLowerCase();
      
      if (extension === 'glb' || extension === 'gltf') {
        loadGLTFModel(event.target.result as ArrayBuffer, true);
      } else if (extension === 'fbx') {
        loadFBXModel(event.target.result as ArrayBuffer, true);
      } else {
        message.error(t('editor.animation.retargeting.unsupportedFormat'));
        setLoading(false);
      }
    };

    reader.readAsArrayBuffer(file);
    return false; // 阻止默认上传行为
  };

  // 加载目标模型
  const handleLoadTargetModel = (file: File) => {
    setLoading(true);
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target?.result) {
        setLoading(false);
        return;
      }

      const extension = file.name.split('.').pop()?.toLowerCase();
      
      if (extension === 'glb' || extension === 'gltf') {
        loadGLTFModel(event.target.result as ArrayBuffer, false);
      } else if (extension === 'fbx') {
        loadFBXModel(event.target.result as ArrayBuffer, false);
      } else {
        message.error(t('editor.animation.retargeting.unsupportedFormat'));
        setLoading(false);
      }
    };

    reader.readAsArrayBuffer(file);
    return false; // 阻止默认上传行为
  };

  // 加载GLTF模型
  const loadGLTFModel = (_buffer: ArrayBuffer, isSource: boolean) => {
    // 模拟GLTF加载器功能
    // 在实际项目中应该使用引擎的模型加载功能
    try {
      // 创建一个更复杂的测试模型
      const geometry = new THREE.BoxGeometry(0.5, 1.8, 0.3);
      const material = new THREE.MeshLambertMaterial({ color: isSource ? 0x00ff00 : 0x0000ff });

      // 创建一个更真实的骨骼系统
      const bones: THREE.Bone[] = [];

      // 根骨骼
      const root = new THREE.Bone();
      root.name = 'root';
      root.position.set(0, 0, 0);
      bones.push(root);

      // 脊椎骨骼
      const spine = new THREE.Bone();
      spine.name = 'spine';
      spine.position.set(0, 0.5, 0);
      root.add(spine);
      bones.push(spine);

      const spine1 = new THREE.Bone();
      spine1.name = 'spine1';
      spine1.position.set(0, 0.3, 0);
      spine.add(spine1);
      bones.push(spine1);

      const spine2 = new THREE.Bone();
      spine2.name = 'spine2';
      spine2.position.set(0, 0.3, 0);
      spine1.add(spine2);
      bones.push(spine2);

      // 颈部和头部
      const neck = new THREE.Bone();
      neck.name = 'neck';
      neck.position.set(0, 0.2, 0);
      spine2.add(neck);
      bones.push(neck);

      const head = new THREE.Bone();
      head.name = 'head';
      head.position.set(0, 0.2, 0);
      neck.add(head);
      bones.push(head);

      // 左臂
      const leftShoulder = new THREE.Bone();
      leftShoulder.name = 'leftShoulder';
      leftShoulder.position.set(0.3, 0.1, 0);
      spine2.add(leftShoulder);
      bones.push(leftShoulder);

      const leftArm = new THREE.Bone();
      leftArm.name = 'leftArm';
      leftArm.position.set(0.3, 0, 0);
      leftShoulder.add(leftArm);
      bones.push(leftArm);

      const leftForearm = new THREE.Bone();
      leftForearm.name = 'leftForearm';
      leftForearm.position.set(0.3, 0, 0);
      leftArm.add(leftForearm);
      bones.push(leftForearm);

      const leftHand = new THREE.Bone();
      leftHand.name = 'leftHand';
      leftHand.position.set(0.2, 0, 0);
      leftForearm.add(leftHand);
      bones.push(leftHand);

      // 右臂（镜像）
      const rightShoulder = new THREE.Bone();
      rightShoulder.name = 'rightShoulder';
      rightShoulder.position.set(-0.3, 0.1, 0);
      spine2.add(rightShoulder);
      bones.push(rightShoulder);

      const rightArm = new THREE.Bone();
      rightArm.name = 'rightArm';
      rightArm.position.set(-0.3, 0, 0);
      rightShoulder.add(rightArm);
      bones.push(rightArm);

      const rightForearm = new THREE.Bone();
      rightForearm.name = 'rightForearm';
      rightForearm.position.set(-0.3, 0, 0);
      rightArm.add(rightForearm);
      bones.push(rightForearm);

      const rightHand = new THREE.Bone();
      rightHand.name = 'rightHand';
      rightHand.position.set(-0.2, 0, 0);
      rightForearm.add(rightHand);
      bones.push(rightHand);

      // 左腿
      const leftThigh = new THREE.Bone();
      leftThigh.name = 'leftThigh';
      leftThigh.position.set(0.1, -0.1, 0);
      root.add(leftThigh);
      bones.push(leftThigh);

      const leftShin = new THREE.Bone();
      leftShin.name = 'leftShin';
      leftShin.position.set(0, -0.4, 0);
      leftThigh.add(leftShin);
      bones.push(leftShin);

      const leftFoot = new THREE.Bone();
      leftFoot.name = 'leftFoot';
      leftFoot.position.set(0, -0.4, 0);
      leftShin.add(leftFoot);
      bones.push(leftFoot);

      // 右腿（镜像）
      const rightThigh = new THREE.Bone();
      rightThigh.name = 'rightThigh';
      rightThigh.position.set(-0.1, -0.1, 0);
      root.add(rightThigh);
      bones.push(rightThigh);

      const rightShin = new THREE.Bone();
      rightShin.name = 'rightShin';
      rightShin.position.set(0, -0.4, 0);
      rightThigh.add(rightShin);
      bones.push(rightShin);

      const rightFoot = new THREE.Bone();
      rightFoot.name = 'rightFoot';
      rightFoot.position.set(0, -0.4, 0);
      rightShin.add(rightFoot);
      bones.push(rightFoot);

      const skeleton = new THREE.Skeleton(bones);
      const skinnedMesh = new THREE.SkinnedMesh(geometry, material);
      skinnedMesh.add(root);
      skinnedMesh.bind(skeleton);

      const model = new THREE.Group();
      model.add(skinnedMesh);

      if (isSource) {
        setSourceModel(model);
        setSourceSkeleton(skeleton);

        // 创建一个简单的动画片段
        const clip = createTestAnimationClip(bones);
        setSourceClips([clip]);
        setSourceClip(clip);
        setSelectedClipName(clip.name);

        message.success(t('editor.animation.retargeting.sourceModelLoaded'));
      } else {
        setTargetModel(model);
        setTargetSkeleton(skeleton);
        message.success(t('editor.animation.retargeting.targetModelLoaded'));
      }

      // 更新预览
      updatePreview();
      setLoading(false);
    } catch (error) {
      console.error('加载GLTF模型失败:', error);
      message.error(t('editor.animation.retargeting.loadModelFailed'));
      setLoading(false);
    }
  };

  // 创建测试动画片段
  const createTestAnimationClip = (bones: THREE.Bone[]): THREE.AnimationClip => {
    const tracks: THREE.KeyframeTrack[] = [];
    const duration = 2.0; // 2秒动画

    // 为一些关键骨骼创建简单的旋转动画
    const animatedBones = ['leftArm', 'rightArm', 'leftThigh', 'rightThigh'];

    animatedBones.forEach(boneName => {
      const bone = bones.find(b => b.name === boneName);
      if (!bone) return;

      // 创建旋转关键帧
      const times = [0, duration / 2, duration];
      const values = [
        0, 0, 0, 1,  // 初始旋转
        0.5, 0, 0, 0.866,  // 中间旋转（30度）
        0, 0, 0, 1   // 结束旋转
      ];

      const track = new THREE.QuaternionKeyframeTrack(
        `${boneName}.quaternion`,
        times,
        values
      );

      tracks.push(track);
    });

    return new THREE.AnimationClip('test_animation', duration, tracks);
  };

  // 加载FBX模型
  const loadFBXModel = (_buffer: ArrayBuffer, isSource: boolean) => {
    // 模拟FBX加载器功能
    // 在实际项目中应该使用引擎的模型加载功能
    try {
      // 创建一个不同的测试模型（模拟FBX格式的差异）
      const geometry = new THREE.CylinderGeometry(0.3, 0.3, 1.8, 8);
      const material = new THREE.MeshLambertMaterial({ color: isSource ? 0xff0000 : 0x00ffff });

      // 创建一个稍微不同的骨骼系统（模拟不同的骨骼命名约定）
      const bones: THREE.Bone[] = [];

      // 使用不同的命名约定（模拟FBX的命名）
      const rootBone = new THREE.Bone();
      rootBone.name = 'Root';
      rootBone.position.set(0, 0, 0);
      bones.push(rootBone);

      const pelvis = new THREE.Bone();
      pelvis.name = 'Pelvis';
      pelvis.position.set(0, 0.3, 0);
      rootBone.add(pelvis);
      bones.push(pelvis);

      const spine01 = new THREE.Bone();
      spine01.name = 'Spine_01';
      spine01.position.set(0, 0.3, 0);
      pelvis.add(spine01);
      bones.push(spine01);

      const spine02 = new THREE.Bone();
      spine02.name = 'Spine_02';
      spine02.position.set(0, 0.3, 0);
      spine01.add(spine02);
      bones.push(spine02);

      const spine03 = new THREE.Bone();
      spine03.name = 'Spine_03';
      spine03.position.set(0, 0.3, 0);
      spine02.add(spine03);
      bones.push(spine03);

      // 颈部和头部
      const neckBone = new THREE.Bone();
      neckBone.name = 'Neck';
      neckBone.position.set(0, 0.2, 0);
      spine03.add(neckBone);
      bones.push(neckBone);

      const headBone = new THREE.Bone();
      headBone.name = 'Head';
      headBone.position.set(0, 0.2, 0);
      neckBone.add(headBone);
      bones.push(headBone);

      // 左臂（使用L_前缀）
      const lClavicle = new THREE.Bone();
      lClavicle.name = 'L_Clavicle';
      lClavicle.position.set(0.2, 0.1, 0);
      spine03.add(lClavicle);
      bones.push(lClavicle);

      const lUpperarm = new THREE.Bone();
      lUpperarm.name = 'L_Upperarm';
      lUpperarm.position.set(0.3, 0, 0);
      lClavicle.add(lUpperarm);
      bones.push(lUpperarm);

      const lForearm = new THREE.Bone();
      lForearm.name = 'L_Forearm';
      lForearm.position.set(0.3, 0, 0);
      lUpperarm.add(lForearm);
      bones.push(lForearm);

      const lHand = new THREE.Bone();
      lHand.name = 'L_Hand';
      lHand.position.set(0.2, 0, 0);
      lForearm.add(lHand);
      bones.push(lHand);

      // 右臂（使用R_前缀）
      const rClavicle = new THREE.Bone();
      rClavicle.name = 'R_Clavicle';
      rClavicle.position.set(-0.2, 0.1, 0);
      spine03.add(rClavicle);
      bones.push(rClavicle);

      const rUpperarm = new THREE.Bone();
      rUpperarm.name = 'R_Upperarm';
      rUpperarm.position.set(-0.3, 0, 0);
      rClavicle.add(rUpperarm);
      bones.push(rUpperarm);

      const rForearm = new THREE.Bone();
      rForearm.name = 'R_Forearm';
      rForearm.position.set(-0.3, 0, 0);
      rUpperarm.add(rForearm);
      bones.push(rForearm);

      const rHand = new THREE.Bone();
      rHand.name = 'R_Hand';
      rHand.position.set(-0.2, 0, 0);
      rForearm.add(rHand);
      bones.push(rHand);

      // 左腿
      const lThigh = new THREE.Bone();
      lThigh.name = 'L_Thigh';
      lThigh.position.set(0.1, -0.1, 0);
      pelvis.add(lThigh);
      bones.push(lThigh);

      const lCalf = new THREE.Bone();
      lCalf.name = 'L_Calf';
      lCalf.position.set(0, -0.4, 0);
      lThigh.add(lCalf);
      bones.push(lCalf);

      const lFoot = new THREE.Bone();
      lFoot.name = 'L_Foot';
      lFoot.position.set(0, -0.4, 0);
      lCalf.add(lFoot);
      bones.push(lFoot);

      // 右腿
      const rThigh = new THREE.Bone();
      rThigh.name = 'R_Thigh';
      rThigh.position.set(-0.1, -0.1, 0);
      pelvis.add(rThigh);
      bones.push(rThigh);

      const rCalf = new THREE.Bone();
      rCalf.name = 'R_Calf';
      rCalf.position.set(0, -0.4, 0);
      rThigh.add(rCalf);
      bones.push(rCalf);

      const rFoot = new THREE.Bone();
      rFoot.name = 'R_Foot';
      rFoot.position.set(0, -0.4, 0);
      rCalf.add(rFoot);
      bones.push(rFoot);

      const skeleton = new THREE.Skeleton(bones);
      const skinnedMesh = new THREE.SkinnedMesh(geometry, material);
      skinnedMesh.add(rootBone);
      skinnedMesh.bind(skeleton);

      const model = new THREE.Group();
      model.add(skinnedMesh);

      if (isSource) {
        setSourceModel(model);
        setSourceSkeleton(skeleton);

        // 创建一个不同的动画片段
        const clip = createFBXTestAnimationClip(bones);
        setSourceClips([clip]);
        setSourceClip(clip);
        setSelectedClipName(clip.name);

        message.success(t('editor.animation.retargeting.sourceModelLoaded'));
      } else {
        setTargetModel(model);
        setTargetSkeleton(skeleton);
        message.success(t('editor.animation.retargeting.targetModelLoaded'));
      }

      // 更新预览
      updatePreview();
      setLoading(false);
    } catch (error) {
      console.error('加载FBX模型失败:', error);
      message.error(t('editor.animation.retargeting.loadModelFailed'));
      setLoading(false);
    }
  };

  // 创建FBX测试动画片段
  const createFBXTestAnimationClip = (bones: THREE.Bone[]): THREE.AnimationClip => {
    const tracks: THREE.KeyframeTrack[] = [];
    const duration = 3.0; // 3秒动画

    // 为一些关键骨骼创建不同的动画
    const animatedBones = ['L_Upperarm', 'R_Upperarm', 'L_Thigh', 'R_Thigh', 'Spine_01'];

    animatedBones.forEach((boneName, index) => {
      const bone = bones.find(b => b.name === boneName);
      if (!bone) return;

      // 创建不同的旋转动画模式
      const times = [0, duration / 3, duration * 2 / 3, duration];
      const phase = index * Math.PI / 4; // 不同骨骼的相位差

      const values = [
        0, 0, 0, 1,  // 初始旋转
        Math.sin(phase), 0, 0, Math.cos(phase),  // 第一个关键帧
        Math.sin(phase + Math.PI / 2), 0, 0, Math.cos(phase + Math.PI / 2),  // 第二个关键帧
        0, 0, 0, 1   // 结束旋转
      ];

      const track = new THREE.QuaternionKeyframeTrack(
        `${boneName}.quaternion`,
        times,
        values
      );

      tracks.push(track);
    });

    return new THREE.AnimationClip('fbx_walk_animation', duration, tracks);
  };

  // 更新预览
  const updatePreview = () => {
    if (!sceneRef.current) return;

    // 清除现有模型
    const modelsToRemove: THREE.Object3D[] = [];
    sceneRef.current.traverse((object) => {
      if (object instanceof THREE.SkinnedMesh) {
        modelsToRemove.push(object.parent!);
      }
    });

    modelsToRemove.forEach((model) => {
      sceneRef.current!.remove(model);
    });

    // 添加源模型
    if (sourceModel) {
      const sourceModelClone = sourceModel.clone();
      sourceModelClone.position.set(-1, 0, 0);
      sceneRef.current.add(sourceModelClone);

      // 创建动画混合器
      if (sourceClip) {
        const mixer = new THREE.AnimationMixer(sourceModelClone);
        const action = mixer.clipAction(sourceClip);
        action.play();
        animationMixerRef.current = mixer;
      }
    }

    // 添加目标模型
    if (targetModel) {
      const targetModelClone = targetModel.clone();
      targetModelClone.position.set(1, 0, 0);
      sceneRef.current.add(targetModelClone);

      // 如果有重定向后的动画，应用到目标模型
      if (retargetedClip && targetModelClone) {
        const mixer = new THREE.AnimationMixer(targetModelClone);
        const action = mixer.clipAction(retargetedClip);
        action.play();

        // 如果已有混合器，则创建一个组合混合器
        if (animationMixerRef.current) {
          const oldMixer = animationMixerRef.current;
          // 创建一个包装对象来管理多个混合器
          const combinedMixer = {
            update: (delta: number) => {
              oldMixer.update(delta);
              mixer.update(delta);
            }
          };
          // 将包装对象赋值给ref（类型转换）
          animationMixerRef.current = combinedMixer as THREE.AnimationMixer;
        } else {
          animationMixerRef.current = mixer;
        }
      }
    }
  };

  // 选择动画片段
  const handleSelectClip = (clipName: string) => {
    const clip = sourceClips.find(c => c.name === clipName);
    if (clip) {
      setSourceClip(clip);
      setSelectedClipName(clipName);
      updatePreview();
    }
  };

  // 切换播放状态
  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  // 更新骨骼映射和配置
  const handleUpdate = (newBoneMapping: BoneMapping[], newConfig: RetargetingConfig) => {
    setBoneMapping(newBoneMapping);
    setConfig(newConfig);
  };

  // 自动生成骨骼映射
  const generateAutoBoneMapping = () => {
    if (!sourceSkeleton || !targetSkeleton) {
      message.warning(t('editor.animation.retargeting.missingSkeletons'));
      return;
    }

    const sourceBones = sourceSkeleton.bones.map(bone => bone.name);
    const targetBones = targetSkeleton.bones.map(bone => bone.name);

    const autoMapping: BoneMapping[] = [];

    // 定义常见的骨骼映射规则
    const mappingRules = [
      // 根骨骼映射
      { patterns: ['root', 'Root', 'ROOT'], priority: 1 },
      { patterns: ['pelvis', 'Pelvis', 'PELVIS', 'hips', 'Hips'], priority: 2 },

      // 脊椎映射
      { patterns: ['spine', 'Spine', 'SPINE', 'spine1', 'Spine_01'], priority: 3 },
      { patterns: ['spine1', 'Spine1', 'spine_01', 'Spine_01'], priority: 4 },
      { patterns: ['spine2', 'Spine2', 'spine_02', 'Spine_02'], priority: 5 },
      { patterns: ['spine3', 'Spine3', 'spine_03', 'Spine_03'], priority: 6 },

      // 颈部和头部
      { patterns: ['neck', 'Neck', 'NECK'], priority: 7 },
      { patterns: ['head', 'Head', 'HEAD'], priority: 8 },

      // 左臂
      { patterns: ['leftShoulder', 'LeftShoulder', 'L_Clavicle', 'l_clavicle'], priority: 9 },
      { patterns: ['leftArm', 'LeftArm', 'L_Upperarm', 'l_upperarm'], priority: 10 },
      { patterns: ['leftForearm', 'LeftForearm', 'L_Forearm', 'l_forearm'], priority: 11 },
      { patterns: ['leftHand', 'LeftHand', 'L_Hand', 'l_hand'], priority: 12 },

      // 右臂
      { patterns: ['rightShoulder', 'RightShoulder', 'R_Clavicle', 'r_clavicle'], priority: 13 },
      { patterns: ['rightArm', 'RightArm', 'R_Upperarm', 'r_upperarm'], priority: 14 },
      { patterns: ['rightForearm', 'RightForearm', 'R_Forearm', 'r_forearm'], priority: 15 },
      { patterns: ['rightHand', 'RightHand', 'R_Hand', 'r_hand'], priority: 16 },

      // 左腿
      { patterns: ['leftThigh', 'LeftThigh', 'L_Thigh', 'l_thigh'], priority: 17 },
      { patterns: ['leftShin', 'LeftShin', 'L_Calf', 'l_calf'], priority: 18 },
      { patterns: ['leftFoot', 'LeftFoot', 'L_Foot', 'l_foot'], priority: 19 },

      // 右腿
      { patterns: ['rightThigh', 'RightThigh', 'R_Thigh', 'r_thigh'], priority: 20 },
      { patterns: ['rightShin', 'RightShin', 'R_Calf', 'r_calf'], priority: 21 },
      { patterns: ['rightFoot', 'RightFoot', 'R_Foot', 'r_foot'], priority: 22 }
    ];

    // 为每个源骨骼找到最佳匹配的目标骨骼
    sourceBones.forEach(sourceBone => {
      let bestMatch = '';
      let bestScore = 0;

      targetBones.forEach(targetBone => {
        const score = calculateBoneMatchScore(sourceBone, targetBone, mappingRules);
        if (score > bestScore) {
          bestScore = score;
          bestMatch = targetBone;
        }
      });

      if (bestMatch && bestScore > 0.5) { // 只有当匹配度足够高时才添加映射
        autoMapping.push({
          source: sourceBone,
          target: bestMatch
        });
      }
    });

    setBoneMapping(autoMapping);
    message.success(t('editor.animation.retargeting.autoMappingGenerated', { count: autoMapping.length }));
  };

  // 计算骨骼匹配分数
  const calculateBoneMatchScore = (sourceBone: string, targetBone: string, rules: any[]): number => {
    let score = 0;

    // 完全匹配
    if (sourceBone === targetBone) {
      return 1.0;
    }

    // 忽略大小写匹配
    if (sourceBone.toLowerCase() === targetBone.toLowerCase()) {
      return 0.9;
    }

    // 基于规则的匹配
    for (const rule of rules) {
      const sourceInRule = rule.patterns.some((pattern: string) =>
        sourceBone.toLowerCase().includes(pattern.toLowerCase()) ||
        pattern.toLowerCase().includes(sourceBone.toLowerCase())
      );

      const targetInRule = rule.patterns.some((pattern: string) =>
        targetBone.toLowerCase().includes(pattern.toLowerCase()) ||
        pattern.toLowerCase().includes(targetBone.toLowerCase())
      );

      if (sourceInRule && targetInRule) {
        score = Math.max(score, 0.8);
      }
    }

    // 相似性匹配（基于编辑距离）
    const similarity = calculateStringSimilarity(sourceBone.toLowerCase(), targetBone.toLowerCase());
    score = Math.max(score, similarity * 0.7);

    return score;
  };

  // 计算字符串相似性
  const calculateStringSimilarity = (str1: string, str2: string): number => {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) {
      return 1.0;
    }

    const editDistance = calculateEditDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  };

  // 计算编辑距离
  const calculateEditDistance = (str1: string, str2: string): number => {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        if (str1[i - 1] === str2[j - 1]) {
          matrix[j][i] = matrix[j - 1][i - 1];
        } else {
          matrix[j][i] = Math.min(
            matrix[j - 1][i - 1] + 1, // substitution
            matrix[j][i - 1] + 1,     // insertion
            matrix[j - 1][i] + 1      // deletion
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  };

  // 重定向动画
  const handleRetarget = () => {
    if (!sourceClip || !sourceSkeleton || !targetSkeleton) {
      message.warning(t('editor.animation.retargeting.missingData'));
      return;
    }

    if (boneMapping.length === 0) {
      message.warning(t('editor.animation.retargeting.noBoneMapping'));
      return;
    }

    try {
      setLoading(true);

      // 实现基本的动画重定向算法
      const retargetedClip = retargetAnimationClip(sourceClip, sourceSkeleton, targetSkeleton, boneMapping, config);

      if (retargetedClip) {
        setRetargetedClip(retargetedClip);
        message.success(t('editor.animation.retargeting.retargetSuccess'));

        // 更新预览
        updatePreview();
      } else {
        message.error(t('editor.animation.retargeting.retargetFailed'));
      }

      setLoading(false);
    } catch (error) {
      console.error('重定向动画失败:', error);
      message.error(t('editor.animation.retargeting.retargetFailed'));
      setLoading(false);
    }
  };

  // 动画重定向核心算法
  const retargetAnimationClip = (
    sourceClip: THREE.AnimationClip,
    sourceSkeleton: THREE.Skeleton,
    targetSkeleton: THREE.Skeleton,
    boneMapping: BoneMapping[],
    config: RetargetingConfig
  ): THREE.AnimationClip | null => {
    try {
      // 创建骨骼映射表
      const mappingTable = new Map<string, string>();
      boneMapping.forEach(mapping => {
        mappingTable.set(mapping.source, mapping.target);
      });

      // 获取源骨骼和目标骨骼的映射
      const sourceBones = sourceSkeleton.bones;
      const targetBones = targetSkeleton.bones;

      // 创建目标骨骼名称到索引的映射
      const targetBoneIndexMap = new Map<string, number>();
      targetBones.forEach((bone, index) => {
        targetBoneIndexMap.set(bone.name, index);
      });

      // 处理动画轨道
      const newTracks: THREE.KeyframeTrack[] = [];

      sourceClip.tracks.forEach(track => {
        // 解析轨道名称
        const trackNameParts = track.name.split('.');
        if (trackNameParts.length < 2) return;

        const boneName = trackNameParts[0];
        const property = trackNameParts[1];

        // 查找对应的目标骨骼
        const targetBoneName = mappingTable.get(boneName);
        if (!targetBoneName) return;

        const targetBoneIndex = targetBoneIndexMap.get(targetBoneName);
        if (targetBoneIndex === undefined) return;

        // 根据配置决定是否保留某些轨道
        if (property === 'position' && !config.preservePositionTracks) {
          // 如果不保留位置轨道，跳过位置动画
          return;
        }

        if (property === 'scale' && !config.preserveScaleTracks) {
          // 如果不保留缩放轨道，跳过缩放动画
          return;
        }

        // 创建新的轨道名称
        const newTrackName = `${targetBoneName}.${property}`;

        // 复制轨道数据
        let newValues = [...track.values];

        // 如果是旋转轨道且需要标准化
        if (property === 'quaternion' && config.normalizeRotations) {
          // 标准化四元数
          for (let i = 0; i < newValues.length; i += 4) {
            const quat = new THREE.Quaternion(
              newValues[i],
              newValues[i + 1],
              newValues[i + 2],
              newValues[i + 3]
            );
            quat.normalize();
            newValues[i] = quat.x;
            newValues[i + 1] = quat.y;
            newValues[i + 2] = quat.z;
            newValues[i + 3] = quat.w;
          }
        }

        // 如果是位置轨道且需要调整根骨骼高度
        if (property === 'position' && config.adjustRootHeight && boneName === 'root') {
          // 计算高度差异并调整
          const sourceRootBone = sourceBones.find(bone => bone.name === boneName);
          const targetRootBone = targetBones.find(bone => bone.name === targetBoneName);

          if (sourceRootBone && targetRootBone) {
            const heightDiff = targetRootBone.position.y - sourceRootBone.position.y;

            for (let i = 1; i < newValues.length; i += 3) {
              newValues[i] += heightDiff; // 调整Y轴位置
            }
          }
        }

        // 创建新轨道
        let newTrack: THREE.KeyframeTrack;

        if (track instanceof THREE.VectorKeyframeTrack) {
          newTrack = new THREE.VectorKeyframeTrack(newTrackName, track.times, newValues);
        } else if (track instanceof THREE.QuaternionKeyframeTrack) {
          newTrack = new THREE.QuaternionKeyframeTrack(newTrackName, track.times, newValues);
        } else if (track instanceof THREE.NumberKeyframeTrack) {
          newTrack = new THREE.NumberKeyframeTrack(newTrackName, track.times, newValues);
        } else {
          // 通用轨道类型
          newTrack = new THREE.KeyframeTrack(newTrackName, track.times, newValues);
        }

        newTracks.push(newTrack);
      });

      // 创建新的动画片段
      const retargetedClip = new THREE.AnimationClip(
        `${sourceClip.name}_retargeted`,
        sourceClip.duration,
        newTracks
      );

      return retargetedClip;
    } catch (error) {
      console.error('动画重定向算法执行失败:', error);
      return null;
    }
  };

  // 保存结果
  const handleSave = () => {
    if (!onSave) return;

    const result: RetargetingResult = {
      boneMapping,
      config,
      sourceModel: sourceModel || undefined,
      targetModel: targetModel || undefined,
      sourceClip: sourceClip || undefined,
      retargetedClip: retargetedClip || undefined};

    onSave(result);
    onClose();
  };

  // 导出骨骼映射
  const handleExportMapping = () => {
    if (boneMapping.length === 0) {
      message.warning(t('editor.animation.retargeting.noBoneMapping'));
      return;
    }

    const data = JSON.stringify({ boneMapping, config }, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'bone_mapping.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 导入骨骼映射
  const handleImportMapping = (file: File) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target?.result) return;

      try {
        const data = JSON.parse(event.target.result as string);
        
        if (data.boneMapping && Array.isArray(data.boneMapping)) {
          setBoneMapping(data.boneMapping);
          
          if (data.config) {
            setConfig(data.config);
          }
          
          message.success(t('editor.animation.retargeting.importMappingSuccess'));
        } else {
          message.error(t('editor.animation.retargeting.invalidMappingFile'));
        }
      } catch (error) {
        console.error('导入骨骼映射失败:', error);
        message.error(t('editor.animation.retargeting.importMappingFailed'));
      }
    };

    reader.readAsText(file);
    return false; // 阻止默认上传行为
  };

  return (
    <Layout className="retargeting-editor-panel" style={{ display: visible ? 'flex' : 'none' }}>
      <Header className="retargeting-editor-header">
        <div className="retargeting-editor-title">{t('editor.animation.retargeting.title')}</div>
        <div className="retargeting-editor-controls">
          <Space>
            <Button
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={togglePlay}
              disabled={!sourceClip}
            />
            <Button
              type="primary"
              onClick={handleRetarget}
              disabled={!sourceClip || !sourceSkeleton || !targetSkeleton || boneMapping.length === 0}
            >
              {t('editor.animation.retargeting.retarget')}
            </Button>
            <Button icon={<SaveOutlined />} type="primary" onClick={handleSave}>
              {t('editor.animation.retargeting.save')}
            </Button>
            <Button icon={<CloseOutlined />} onClick={onClose}>
              {t('editor.animation.retargeting.close')}
            </Button>
          </Space>
        </div>
      </Header>

      <Content className="retargeting-editor-content">
        <Spin spinning={loading} tip={t('editor.animation.retargeting.loading')}>
          <div className="retargeting-editor-main">
            <div className="retargeting-editor-controls">
              <div className="model-controls">
                <div className="control-group">
                  <div className="control-label">{t('editor.animation.retargeting.sourceModel')}</div>
                  <Upload
                    beforeUpload={handleLoadSourceModel}
                    showUploadList={false}
                    accept=".glb,.gltf,.fbx"
                  >
                    <Button icon={<UploadOutlined />}>
                      {sourceModel ? t('editor.animation.retargeting.changeSourceModel') : t('editor.animation.retargeting.loadSourceModel')}
                    </Button>
                  </Upload>
                </div>

                <div className="control-group">
                  <div className="control-label">{t('editor.animation.retargeting.targetModel')}</div>
                  <Upload
                    beforeUpload={handleLoadTargetModel}
                    showUploadList={false}
                    accept=".glb,.gltf,.fbx"
                  >
                    <Button icon={<UploadOutlined />}>
                      {targetModel ? t('editor.animation.retargeting.changeTargetModel') : t('editor.animation.retargeting.loadTargetModel')}
                    </Button>
                  </Upload>
                </div>

                {sourceClips.length > 0 && (
                  <div className="control-group">
                    <div className="control-label">{t('editor.animation.retargeting.animation')}</div>
                    <Select
                      style={{ width: '100%' }}
                      value={selectedClipName}
                      onChange={handleSelectClip}
                    >
                      {sourceClips.map(clip => (
                        <Option key={clip.name} value={clip.name}>{clip.name}</Option>
                      ))}
                    </Select>
                  </div>
                )}

                <div className="control-group">
                  <Space wrap>
                    <Button
                      icon={<SyncOutlined />}
                      onClick={generateAutoBoneMapping}
                      disabled={!sourceSkeleton || !targetSkeleton}
                      type="primary"
                    >
                      {t('editor.animation.retargeting.autoMapping')}
                    </Button>
                    <Upload
                      beforeUpload={handleImportMapping}
                      showUploadList={false}
                      accept=".json"
                    >
                      <Button icon={<ImportOutlined />}>
                        {t('editor.animation.retargeting.importMapping')}
                      </Button>
                    </Upload>
                    <Button
                      icon={<ExportOutlined />}
                      onClick={handleExportMapping}
                      disabled={boneMapping.length === 0}
                    >
                      {t('editor.animation.retargeting.exportMapping')}
                    </Button>
                  </Space>
                </div>
              </div>

              <div className="preview-view" ref={previewContainerRef} />

              <div className="mapping-editor">
                <RetargetingEditor
                  sourceSkeleton={sourceSkeleton || undefined}
                  targetSkeleton={targetSkeleton || undefined}
                  boneMapping={boneMapping}
                  config={config}
                  onUpdate={handleUpdate}
                  onPreview={updatePreview}
                  showPreview={false}
                />
              </div>
            </div>
          </div>
        </Spin>
      </Content>
    </Layout>
  );
};

export default RetargetingEditorPanel;
