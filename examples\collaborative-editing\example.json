{"title": "协作编辑最佳实践", "description": "展示多人协作编辑功能和工作流程，包括权限管理、冲突解决等。", "category": "collaboration", "tags": ["协作", "多人", "权限", "冲突解决", "实时同步"], "version": "1.0.0", "author": "DL（Digital Learning）引擎团队", "license": "MIT", "preview": "assets/images/preview.jpg", "difficulty": "advanced", "features": [{"title": "多人同时编辑", "description": "多个用户可以同时编辑同一个场景或项目"}, {"title": "实时同步", "description": "编辑操作实时同步到所有协作者"}, {"title": "用户状态显示", "description": "显示当前在线用户及其编辑状态"}, {"title": "冲突解决", "description": "智能检测和解决编辑冲突"}, {"title": "操作历史记录", "description": "记录所有编辑操作，支持回滚和重做"}, {"title": "权限管理", "description": "基于角色的权限控制系统"}, {"title": "聊天和注释", "description": "支持实时聊天和场景注释功能"}], "requirements": {"engineVersion": ">=1.0.0", "editorVersion": ">=1.0.0", "dependencies": []}, "relatedExamples": ["editor-basics", "performance-optimization"], "tutorials": [{"title": "协作编辑入门", "description": "学习如何使用协作编辑功能", "url": "tutorials/collaboration-basics.md"}, {"title": "权限管理指南", "description": "学习如何管理用户权限", "url": "tutorials/permission-management.md"}, {"title": "冲突解决策略", "description": "学习如何解决编辑冲突", "url": "tutorials/conflict-resolution.md"}]}