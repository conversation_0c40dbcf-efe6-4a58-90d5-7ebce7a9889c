/**
 * 水生植物类
 * 用于实现水生植物类型
 */
import * as THREE from 'three';
import { SeasonType } from '../ecosystem/EcosystemSimulationSystem';
import { VegetationInstanceData } from '../growth/VegetationGrowthSystem';
/**
 * 水生植物类型
 */
export declare enum AquaticVegetationType {
    /** 睡莲 */
    WATER_LILY = "water_lily",
    /** 芦苇 */
    REED = "reed",
    /** 荷花 */
    LOTUS = "lotus",
    /** 水草 */
    SEAWEED = "seaweed",
    /** 浮萍 */
    DUCKWEED = "duckweed",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 水生植物配置
 */
export interface AquaticVegetationConfig {
    /** 类型 */
    type: AquaticVegetationType;
    /** 最大高度 */
    maxHeight?: number;
    /** 最大宽度 */
    maxWidth?: number;
    /** 最大寿命（天） */
    maxAge?: number;
    /** 生长速度 */
    growthRate?: number;
    /** 水深范围 */
    waterDepthRange?: {
        min: number;
        max: number;
    };
    /** 水流速度范围 */
    waterFlowSpeedRange?: {
        min: number;
        max: number;
    };
    /** 水质要求 */
    waterQualityRequirement?: number;
    /** 光照要求 */
    lightRequirement?: number;
    /** 温度范围 */
    temperatureRange?: {
        min: number;
        max: number;
    };
    /** 是否浮水 */
    isFloating?: boolean;
    /** 是否水下 */
    isSubmerged?: boolean;
    /** 是否挺水 */
    isEmergent?: boolean;
    /** 是否开花 */
    canFlower?: boolean;
    /** 开花季节 */
    floweringSeason?: SeasonType[];
    /** 是否结果 */
    canFruit?: boolean;
    /** 结果季节 */
    fruitingSeason?: SeasonType[];
    /** 是否常绿 */
    isEvergreen?: boolean;
    /** 自定义数据 */
    userData?: any;
}
/**
 * 水生植物类
 */
export declare class AquaticVegetation {
    /** 配置 */
    private config;
    /** 默认配置映射 */
    private static readonly DEFAULT_CONFIGS;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config: AquaticVegetationConfig);
    /**
     * 获取配置
     * @returns 配置
     */
    getConfig(): AquaticVegetationConfig;
    /**
     * 创建实例数据
     * @param id 实例ID
     * @param position 位置
     * @param rotation 旋转
     * @param scale 缩放
     * @returns 实例数据
     */
    createInstanceData(id: string, position: THREE.Vector3, rotation: THREE.Euler, scale: THREE.Vector3): VegetationInstanceData;
    /**
     * 更新实例数据
     * @param instanceData 实例数据
     * @param waterDepth 水深
     * @param waterFlowSpeed 水流速度
     * @param waterQuality 水质
     * @param lightIntensity 光照强度
     * @param temperature 温度
     * @param deltaTime 帧间隔时间（秒）
     */
    updateInstanceData(instanceData: VegetationInstanceData, waterDepth: number, waterFlowSpeed: number, waterQuality: number, lightIntensity: number, temperature: number, deltaTime: number): void;
    /**
     * 创建水生植物模型
     * @param type 水生植物类型
     * @returns 模型
     */
    static createModel(type: AquaticVegetationType): THREE.Object3D;
    /**
     * 创建睡莲模型
     * @returns 睡莲模型
     */
    private static createWaterLilyModel;
    /**
     * 创建芦苇模型
     * @returns 芦苇模型
     */
    private static createReedModel;
    /**
     * 创建荷花模型
     * @returns 荷花模型
     */
    private static createLotusModel;
    /**
     * 创建水草模型
     * @returns 水草模型
     */
    private static createSeaweedModel;
    /**
     * 创建浮萍模型
     * @returns 浮萍模型
     */
    private static createDuckweedModel;
    /**
     * 创建默认模型
     * @returns 默认模型
     */
    private static createDefaultModel;
}
