/**
 * UILayoutSystem.ts
 *
 * UI布局系统，管理UI元素的布局
 */
import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { UIComponent } from '../components/UIComponent';
import { UILayoutComponent, GridLayout, FlexLayout, AbsoluteLayout, RelativeLayout, GridLayoutParams, FlexLayoutParams, AbsoluteLayoutParams, RelativeLayoutParams, LayoutItemParams } from '../components/UILayoutComponent';
import { UISystem } from './UISystem';
/**
 * UI布局系统配置
 */
export interface UILayoutSystemConfig {
    debug?: boolean;
    autoApplyLayout?: boolean;
    autoApplyInterval?: number;
}
/**
 * UI布局系统
 * 管理UI元素的布局
 */
export declare class UILayoutSystem extends System {
    private uiSystem;
    private config;
    private layoutComponents;
    private lastApplyTime;
    /**
     * 构造函数
     * @param world 世界实例
     * @param uiSystem UI系统实例
     * @param config UI布局系统配置
     */
    constructor(world: World, uiSystem: UISystem, config?: UILayoutSystemConfig);
    /**
     * 注册布局组件
     * @param entity 实体
     * @param component 布局组件
     */
    registerLayoutComponent(entity: Entity, component: UILayoutComponent): void;
    /**
     * 注销布局组件
     * @param entity 实体
     */
    unregisterLayoutComponent(entity: Entity): void;
    /**
     * 获取或创建布局组件
     * @param entity 实体
     * @param layout 布局
     * @param layoutItemParams 布局项参数
     * @returns 布局组件
     */
    getOrCreateLayoutComponent(entity: Entity, layout: GridLayout | FlexLayout | AbsoluteLayout | RelativeLayout, layoutItemParams?: LayoutItemParams): UILayoutComponent;
    /**
     * 创建网格布局
     * @param entity 实体
     * @param uiComponent UI组件
     * @param params 网格布局参数
     * @returns 布局组件
     */
    createGridLayout(entity: Entity, uiComponent: UIComponent, params: GridLayoutParams): UILayoutComponent;
    /**
     * 创建弹性布局
     * @param entity 实体
     * @param uiComponent UI组件
     * @param params 弹性布局参数
     * @returns 布局组件
     */
    createFlexLayout(entity: Entity, uiComponent: UIComponent, params: FlexLayoutParams): UILayoutComponent;
    /**
     * 创建绝对布局
     * @param entity 实体
     * @param uiComponent UI组件
     * @param params 绝对布局参数
     * @returns 布局组件
     */
    createAbsoluteLayout(entity: Entity, uiComponent: UIComponent, params?: AbsoluteLayoutParams): UILayoutComponent;
    /**
     * 创建相对布局
     * @param entity 实体
     * @param uiComponent UI组件
     * @param params 相对布局参数
     * @returns 布局组件
     */
    createRelativeLayout(entity: Entity, uiComponent: UIComponent, params?: RelativeLayoutParams): UILayoutComponent;
    /**
     * 设置布局项参数
     * @param entity 实体
     * @param params 布局项参数
     */
    setLayoutItemParams(entity: Entity, params: LayoutItemParams): void;
    /**
     * 应用所有布局
     */
    applyAllLayouts(): void;
    /**
     * 创建居中布局
     * @param entity 实体
     * @param uiComponent UI组件
     * @returns 布局组件
     */
    createCenterLayout(entity: Entity, uiComponent: UIComponent): UILayoutComponent;
    /**
     * 创建网格布局项
     * @param entity 实体
     * @param column 列位置
     * @param row 行位置
     * @param columnSpan 列跨度
     * @param rowSpan 行跨度
     */
    createGridLayoutItem(entity: Entity, column: number, row: number, columnSpan?: number, rowSpan?: number): void;
    /**
     * 创建弹性布局项
     * @param entity 实体
     * @param flexGrow 增长系数
     * @param flexShrink 收缩系数
     * @param flexBasis 基础尺寸
     * @param alignSelf 自对齐方式
     * @param order 顺序
     */
    createFlexLayoutItem(entity: Entity, flexGrow?: number, flexShrink?: number, flexBasis?: number | string, alignSelf?: 'auto' | 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch', order?: number): void;
    /**
     * 创建绝对布局项
     * @param entity 实体
     * @param left 左边距
     * @param top 上边距
     * @param right 右边距
     * @param bottom 下边距
     * @param zIndex 层级
     */
    createAbsoluteLayoutItem(entity: Entity, left?: number, top?: number, right?: number, bottom?: number, zIndex?: number): void;
    /**
     * 创建相对布局项
     * @param entity 实体
     * @param margin 外边距
     */
    createRelativeLayoutItem(entity: Entity, margin: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    }): void;
    /**
     * 更新系统
     * @param _deltaTime 时间增量 - 未使用，因为使用Date.now()来计算时间间隔
     */
    update(_deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
