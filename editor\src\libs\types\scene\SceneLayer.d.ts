/**
 * 场景图层
 * 用于管理场景中的实体分组
 */
import type { Entity } from '../core/Entity';
import { Scene } from './Scene';
import { EventEmitter } from '../utils/EventEmitter';
import * as THREE from 'three';
/**
 * 场景图层类型
 */
export declare enum SceneLayerType {
    /** 普通图层 */
    NORMAL = "normal",
    /** 图层组 */
    GROUP = "group"
}
/**
 * 场景图层选项
 */
export interface SceneLayerOptions {
    /** 图层ID */
    id: string;
    /** 图层名称 */
    name: string;
    /** 图层类型 */
    type?: SceneLayerType;
    /** 是否可见 */
    visible?: boolean;
    /** 是否锁定 */
    locked?: boolean;
    /** 是否排除在渲染中 */
    excludeFromRender?: boolean;
    /** 是否排除在物理计算中 */
    excludeFromPhysics?: boolean;
    /** 是否排除在射线检测中 */
    excludeFromRaycast?: boolean;
    /** 图层顺序 */
    order?: number;
    /** 图层标签 */
    tags?: string[];
    /** 图层颜色 */
    color?: THREE.Color;
    /** 父图层ID */
    parentId?: string;
    /** 是否展开（仅用于UI显示） */
    expanded?: boolean;
    /** 自定义数据 */
    userData?: Record<string, any>;
}
/**
 * 场景图层
 */
export declare class SceneLayer extends EventEmitter {
    /** 图层ID */
    readonly id: string;
    /** 图层名称 */
    name: string;
    /** 图层类型 */
    private type;
    /** 图层中的实体 */
    private entities;
    /** 是否可见 */
    private visible;
    /** 是否锁定 */
    private locked;
    /** 图层顺序 */
    private order;
    /** 图层标签 */
    private tags;
    /** 图层颜色 */
    private color;
    /** 父图层ID */
    private parentId;
    /** 子图层ID列表 */
    private childrenIds;
    /** 是否展开（仅用于UI显示） */
    private expanded;
    /** 自定义数据 */
    private userData;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建场景图层
     * @param _scene 所属场景（未使用，避免循环引用）
     * @param options 图层选项
     */
    constructor(_scene: Scene, options: SceneLayerOptions);
    /**
     * 初始化图层
     */
    initialize(): void;
    /**
     * 添加实体
     * @param entity 实体
     * @returns 是否成功添加
     */
    addEntity(entity: Entity): boolean;
    /**
     * 移除实体
     * @param entity 实体
     * @returns 是否成功移除
     */
    removeEntity(entity: Entity): boolean;
    /**
     * 获取图层中的所有实体
     * @returns 实体数组
     */
    getEntities(): Entity[];
    /**
     * 获取图层中的实体数量
     * @returns 实体数量
     */
    getEntityCount(): number;
    /**
     * 清空图层
     */
    clear(): void;
    /**
     * 设置图层可见性
     * @param visible 是否可见
     */
    setVisible(visible: boolean): void;
    /**
     * 是否可见
     * @returns 是否可见
     */
    isVisible(): boolean;
    /**
     * 设置图层锁定状态
     * @param locked 是否锁定
     */
    setLocked(locked: boolean): void;
    /**
     * 是否锁定
     * @returns 是否锁定
     */
    isLocked(): boolean;
    /**
     * 设置图层顺序
     * @param order 图层顺序
     */
    setOrder(order: number): void;
    /**
     * 获取图层顺序
     * @returns 图层顺序
     */
    getOrder(): number;
    /**
     * 添加标签
     * @param tag 标签
     * @returns 是否成功添加
     */
    addTag(tag: string): boolean;
    /**
     * 移除标签
     * @param tag 标签
     * @returns 是否成功移除
     */
    removeTag(tag: string): boolean;
    /**
     * 是否有标签
     * @param tag 标签
     * @returns 是否有标签
     */
    hasTag(tag: string): boolean;
    /**
     * 获取所有标签
     * @returns 标签数组
     */
    getTags(): string[];
    /**
     * 设置图层颜色
     * @param color 颜色
     */
    setColor(color: THREE.Color): void;
    /**
     * 获取图层颜色
     * @returns 颜色
     */
    getColor(): THREE.Color;
    /**
     * 设置自定义数据
     * @param key 键
     * @param value 值
     */
    setUserData(key: string, value: any): void;
    /**
     * 获取自定义数据
     * @param key 键
     * @returns 值
     */
    getUserData(key: string): any;
    /**
     * 获取图层类型
     * @returns 图层类型
     */
    getType(): SceneLayerType;
    /**
     * 设置图层类型
     * @param type 图层类型
     */
    setType(type: SceneLayerType): void;
    /**
     * 是否为图层组
     * @returns 是否为图层组
     */
    isGroup(): boolean;
    /**
     * 获取父图层ID
     * @returns 父图层ID
     */
    getParentId(): string | null;
    /**
     * 设置父图层ID
     * @param parentId 父图层ID
     */
    setParentId(parentId: string | null): void;
    /**
     * 添加子图层
     * @param childId 子图层ID
     * @returns 是否成功添加
     */
    addChild(childId: string): boolean;
    /**
     * 移除子图层
     * @param childId 子图层ID
     * @returns 是否成功移除
     */
    removeChild(childId: string): boolean;
    /**
     * 获取所有子图层ID
     * @returns 子图层ID数组
     */
    getChildrenIds(): string[];
    /**
     * 是否有子图层
     * @returns 是否有子图层
     */
    hasChildren(): boolean;
    /**
     * 获取子图层数量
     * @returns 子图层数量
     */
    getChildCount(): number;
    /**
     * 设置展开状态
     * @param expanded 是否展开
     */
    setExpanded(expanded: boolean): void;
    /**
     * 是否展开
     * @returns 是否展开
     */
    isExpanded(): boolean;
    /**
     * 销毁图层
     */
    dispose(): void;
}
