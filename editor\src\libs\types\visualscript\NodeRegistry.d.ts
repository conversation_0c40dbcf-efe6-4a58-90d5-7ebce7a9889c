/**
 * 节点注册表
 * 用于管理和创建可视化脚本节点
 */
import { VisualScriptNode } from './VisualScriptNode';
export type NodeConstructor = new (...args: any[]) => VisualScriptNode;
export interface NodeDefinition {
    constructor: NodeConstructor;
    category: string;
    description: string;
    icon?: string;
    color?: string;
}
export declare class NodeRegistry {
    private static nodes;
    private static categories;
    /**
     * 注册节点类型
     */
    static register(nodeType: string, constructor: NodeConstructor, category?: string, description?: string, icon?: string, color?: string): void;
    /**
     * 创建节点实例
     */
    static createNode(nodeType: string, ...args: any[]): VisualScriptNode | null;
    /**
     * 获取节点定义
     */
    static getNodeDefinition(nodeType: string): NodeDefinition | null;
    /**
     * 获取所有注册的节点类型
     */
    static getRegisteredNodeTypes(): string[];
    /**
     * 获取所有类别
     */
    static getCategories(): string[];
    /**
     * 根据类别获取节点类型
     */
    static getNodeTypesByCategory(category: string): string[];
    /**
     * 检查节点类型是否已注册
     */
    static isRegistered(nodeType: string): boolean;
    /**
     * 注销节点类型
     */
    static unregister(nodeType: string): boolean;
    /**
     * 清空所有注册的节点
     */
    static clear(): void;
    /**
     * 获取节点信息列表
     */
    static getNodeInfoList(): Array<{
        nodeType: string;
        category: string;
        description: string;
        icon?: string;
        color?: string;
    }>;
    /**
     * 搜索节点
     */
    static searchNodes(query: string): string[];
}
