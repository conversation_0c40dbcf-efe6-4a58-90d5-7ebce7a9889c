/**
 * 增强水体交互系统测试
 */
import * as THREE from 'three';
import { World } from '../../../engine/src/core/World';
import { Entity } from '../../../engine/src/core/Entity';
import { WaterBodyComponent } from '../../../engine/src/physics/water/WaterBodyComponent';
import { WaterPhysicsSystem } from '../../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../../engine/src/physics/water/WaterInteractionSystem';
import { PhysicsSystem } from '../../../engine/src/physics/PhysicsSystem';
import { TransformComponent } from '../../../engine/src/core/TransformComponent';
import { PhysicsBodyComponent } from '../../../engine/src/physics/PhysicsBodyComponent';
import { expect } from 'chai';

describe('增强水体交互系统测试', () => {
  let world: World;
  let waterPhysicsSystem: WaterPhysicsSystem;
  let waterInteractionSystem: WaterInteractionSystem;
  let physicsSystem: PhysicsSystem;
  let waterBody: WaterBodyComponent;
  let boxEntity: Entity;
  let sphereEntity: Entity;
  let boatEntity: Entity;

  beforeEach(() => {
    // 创建世界
    world = new World();

    // 创建物理系统
    physicsSystem = new PhysicsSystem(world);
    world.addSystem(physicsSystem);

    // 创建水体物理系统
    waterPhysicsSystem = new WaterPhysicsSystem(world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: false // 测试中禁用多线程
    });
    world.addSystem(waterPhysicsSystem);

    // 创建水体交互系统
    waterInteractionSystem = new WaterInteractionSystem(world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true,
      useVoxelBuoyancy: true,
      voxelResolution: 3,
      enableBuoyancyStabilization: true,
      buoyancyStabilizationStrength: 0.5,
      useDirectionalDrag: true,
      enableTurbulenceDrag: true,
      enableRotationalDrag: true,
      rotationalDragStrength: 0.5,
      useAdvancedFlowInteraction: true,
      enableTurbulentFlow: true
    });
    world.addSystem(waterInteractionSystem);

    // 创建水体
    const waterEntity = new Entity('water');
    waterBody = new WaterBodyComponent({
      type: 'LAKE',
      shape: 'PLANE',
      size: { width: 10, height: 2, depth: 10 },
      position: new THREE.Vector3(0, 0, 0),
      density: 1000,
      viscosity: 1.0,
      flowDirection: { x: 0, y: 0, z: 0 },
      flowSpeed: 0,
      enableBuoyancy: true,
      enableDrag: true,
      enableWaves: true,
      enableFlow: true
    });
    waterEntity.addComponent(waterBody);
    world.addEntity(waterEntity);
    waterPhysicsSystem.addWaterBody(waterEntity, waterBody);

    // 创建盒子实体
    boxEntity = new Entity('box');
    const boxTransform = new TransformComponent();
    boxTransform.setPosition(new THREE.Vector3(0, 5, 0));
    boxEntity.addComponent(boxTransform);
    const boxPhysics = new PhysicsBodyComponent();
    boxPhysics.setMass(1);
    boxPhysics.setShape('BOX');
    boxPhysics.setSize(new THREE.Vector3(1, 1, 1));
    boxEntity.addComponent(boxPhysics);
    world.addEntity(boxEntity);

    // 创建球体实体
    sphereEntity = new Entity('sphere');
    const sphereTransform = new TransformComponent();
    sphereTransform.setPosition(new THREE.Vector3(2, 5, 0));
    sphereEntity.addComponent(sphereTransform);
    const spherePhysics = new PhysicsBodyComponent();
    spherePhysics.setMass(1);
    spherePhysics.setShape('SPHERE');
    spherePhysics.setRadius(0.5);
    sphereEntity.addComponent(spherePhysics);
    world.addEntity(sphereEntity);

    // 创建船形实体
    boatEntity = new Entity('boat');
    const boatTransform = new TransformComponent();
    boatTransform.setPosition(new THREE.Vector3(-2, 5, 0));
    boatEntity.addComponent(boatTransform);
    const boatPhysics = new PhysicsBodyComponent();
    boatPhysics.setMass(5);
    boatPhysics.setShape('CONVEX');
    // 简化的船形顶点
    const vertices = new Float32Array([
      -1.0, -0.2, -0.5,
      1.0, -0.2, -0.5,
      1.0, -0.2, 0.5,
      -1.0, -0.2, 0.5,
      -1.0, 0.0, -0.5,
      1.0, 0.0, -0.5,
      1.0, 0.0, 0.5,
      -1.0, 0.0, 0.5
    ]);
    boatPhysics.setConvexHull(vertices);
    boatEntity.addComponent(boatPhysics);
    world.addEntity(boatEntity);

    // 初始化世界
    world.initialize();
  });

  afterEach(() => {
    // 清理世界
    world.destroy();
  });

  it('应该正确计算体素浮力', () => {
    // 设置盒子位置在水中
    const boxTransform = boxEntity.getComponent('Transform') as TransformComponent;
    boxTransform.setPosition(new THREE.Vector3(0, 0, 0));

    // 更新世界
    world.update();

    // 获取盒子物理体
    const boxPhysics = boxEntity.getComponent('PhysicsBody') as PhysicsBodyComponent;
    const boxVelocity = boxPhysics.getVelocity();

    // 检查盒子是否受到向上的浮力（y方向速度为正）
    expect(boxVelocity.y).to.be.greaterThan(0);
  });

  it('应该正确计算方向性阻力', () => {
    // 设置盒子位置在水中
    const boxTransform = boxEntity.getComponent('Transform') as TransformComponent;
    boxTransform.setPosition(new THREE.Vector3(0, 0, 0));

    // 设置盒子初始速度
    const boxPhysics = boxEntity.getComponent('PhysicsBody') as PhysicsBodyComponent;
    boxPhysics.setVelocity(new THREE.Vector3(5, 0, 0));

    // 记录初始速度
    const initialVelocity = boxPhysics.getVelocity().clone();

    // 更新世界
    world.update();

    // 获取更新后的速度
    const updatedVelocity = boxPhysics.getVelocity();

    // 检查速度是否减小（受到阻力）
    expect(updatedVelocity.x).to.be.lessThan(initialVelocity.x);
  });

  it('应该正确计算旋转阻力', () => {
    // 设置盒子位置在水中
    const boxTransform = boxEntity.getComponent('Transform') as TransformComponent;
    boxTransform.setPosition(new THREE.Vector3(0, 0, 0));

    // 设置盒子初始角速度
    const boxPhysics = boxEntity.getComponent('PhysicsBody') as PhysicsBodyComponent;
    boxPhysics.setAngularVelocity(new THREE.Vector3(0, 5, 0));

    // 记录初始角速度
    const initialAngularVelocity = boxPhysics.getAngularVelocity().clone();

    // 更新世界
    world.update();

    // 获取更新后的角速度
    const updatedAngularVelocity = boxPhysics.getAngularVelocity();

    // 检查角速度是否减小（受到旋转阻力）
    expect(updatedAngularVelocity.y).to.be.lessThan(initialAngularVelocity.y);
  });

  it('应该正确计算水流力', () => {
    // 设置水流方向和速度
    waterBody.setFlowDirection(new THREE.Vector3(1, 0, 0));
    waterBody.setFlowSpeed(5.0);

    // 设置盒子位置在水中
    const boxTransform = boxEntity.getComponent('Transform') as TransformComponent;
    boxTransform.setPosition(new THREE.Vector3(0, 0, 0));

    // 更新世界
    world.update();

    // 获取盒子物理体
    const boxPhysics = boxEntity.getComponent('PhysicsBody') as PhysicsBodyComponent;
    const boxVelocity = boxPhysics.getVelocity();

    // 检查盒子是否受到水流力（x方向速度为正）
    expect(boxVelocity.x).to.be.greaterThan(0);
  });

  it('应该正确处理不同形状的物体', () => {
    // 设置所有物体位置在水中
    const boxTransform = boxEntity.getComponent('Transform') as TransformComponent;
    boxTransform.setPosition(new THREE.Vector3(0, 0, 0));

    const sphereTransform = sphereEntity.getComponent('Transform') as TransformComponent;
    sphereTransform.setPosition(new THREE.Vector3(2, 0, 0));

    const boatTransform = boatEntity.getComponent('Transform') as TransformComponent;
    boatTransform.setPosition(new THREE.Vector3(-2, 0, 0));

    // 更新世界
    world.update();

    // 获取所有物体的物理体
    const boxPhysics = boxEntity.getComponent('PhysicsBody') as PhysicsBodyComponent;
    const spherePhysics = sphereEntity.getComponent('PhysicsBody') as PhysicsBodyComponent;
    const boatPhysics = boatEntity.getComponent('PhysicsBody') as PhysicsBodyComponent;

    // 检查所有物体是否受到向上的浮力（y方向速度为正）
    expect(boxPhysics.getVelocity().y).to.be.greaterThan(0);
    expect(spherePhysics.getVelocity().y).to.be.greaterThan(0);
    expect(boatPhysics.getVelocity().y).to.be.greaterThan(0);

    // 检查船的浮力是否大于盒子和球体（由于形状和体积）
    expect(boatPhysics.getVelocity().y).to.be.greaterThan(boxPhysics.getVelocity().y);
    expect(boatPhysics.getVelocity().y).to.be.greaterThan(spherePhysics.getVelocity().y);
  });
});
