/**
 * 视觉脚本自定义事件
 * 用于在视觉脚本中定义和触发自定义事件
 */
import { EventEmitter } from '../../utils/EventEmitter';
/**
 * 自定义事件选项
 */
export interface CustomEventOptions {
    /** 事件ID */
    id: string;
    /** 事件名称 */
    name: string;
    /** 事件参数类型 */
    parameterTypes?: string[];
    /** 事件描述 */
    description?: string;
}
/**
 * 视觉脚本自定义事件
 * 用于在视觉脚本中定义和触发自定义事件
 */
export declare class CustomEvent extends EventEmitter {
    /** 事件ID */
    readonly id: string;
    /** 事件名称 */
    private _name;
    /** 事件参数类型 */
    private _parameterTypes;
    /** 事件描述 */
    private _description;
    /**
     * 创建自定义事件
     * @param options 事件选项
     */
    constructor(options: CustomEventOptions);
    /**
     * 获取事件名称
     * @returns 事件名称
     */
    get name(): string;
    /**
     * 设置事件名称
     * @param value 事件名称
     */
    set name(value: string);
    /**
     * 获取事件参数类型
     * @returns 事件参数类型
     */
    get parameterTypes(): string[];
    /**
     * 设置事件参数类型
     * @param value 事件参数类型
     */
    set parameterTypes(value: string[]);
    /**
     * 获取事件描述
     * @returns 事件描述
     */
    get description(): string;
    /**
     * 设置事件描述
     * @param value 事件描述
     */
    set description(value: string);
    /**
     * 添加参数类型
     * @param type 参数类型
     * @returns 是否添加成功
     */
    addParameterType(type: string): boolean;
    /**
     * 移除参数类型
     * @param type 参数类型
     * @returns 是否移除成功
     */
    removeParameterType(type: string): boolean;
    /**
     * 触发事件
     * @param args 事件参数
     */
    trigger(...args: any[]): void;
    /**
     * 克隆事件
     * @returns 克隆的事件
     */
    clone(): CustomEvent;
    /**
     * 序列化事件
     * @returns 序列化数据
     */
    serialize(): any;
    /**
     * 反序列化事件
     * @param data 序列化数据
     */
    deserialize(data: any): void;
}
