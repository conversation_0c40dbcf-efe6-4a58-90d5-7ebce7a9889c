import type { Entity } from '../../core/Entity';
import { TerrainComponent } from '../components/TerrainComponent';
import { PhysicsSystem } from '../../physics/PhysicsSystem';
/**
 * 地形物理材质属性
 */
export interface TerrainPhysicsMaterialProps {
    /** 摩擦力 */
    friction: number;
    /** 弹性 */
    restitution: number;
    /** 密度 */
    density: number;
}
/**
 * 地形物理助手
 */
export declare class TerrainPhysicsHelper {
    /** 物理系统 */
    private physicsSystem;
    /** 碰撞器映射 */
    private colliderMap;
    /** 调试可视化 */
    private debugVisuals;
    /**
     * 创建地形物理助手
     * @param physicsSystem 物理系统
     */
    constructor(physicsSystem: PhysicsSystem);
    /**
     * 创建地形物理
     * @param entity 实体
     * @param terrain 地形组件
     * @param materialProps 物理材质属性
     * @returns 是否创建成功
     */
    createTerrainPhysics(entity: Entity, terrain: TerrainComponent, materialProps: TerrainPhysicsMaterialProps): boolean;
    /**
     * 更新地形物理
     * @param entity 实体
     * @param terrain 地形组件
     * @param materialProps 物理材质属性
     * @returns 是否更新成功
     */
    updateTerrainPhysics(entity: Entity, terrain: TerrainComponent, materialProps: TerrainPhysicsMaterialProps): boolean;
    /**
     * 移除地形物理
     * @param entity 实体
     * @returns 是否移除成功
     */
    removeTerrainPhysics(entity: Entity): boolean;
    /**
     * 创建高度场碰撞器
     * @param entity 实体
     * @param terrain 地形组件
     * @param materialProps 物理材质属性
     * @returns 碰撞器
     */
    private createHeightfieldCollider;
    /**
     * 创建高度场数据
     * @param terrain 地形组件
     * @param resolution 分辨率
     * @returns 高度场数据
     */
    private createHeightfieldData;
    /**
     * 创建调试可视化
     * @param entity 实体
     * @param terrain 地形组件
     */
    private createDebugVisuals;
    /**
     * 更新调试可视化
     * @param entity 实体
     * @param terrain 地形组件
     * @param show 是否显示
     */
    updateDebugVisuals(entity: Entity, terrain: TerrainComponent, show: boolean): void;
}
