/**
 * 地形生成工作线程管理器
 * 负责创建和管理地形生成工作线程
 */
import { TerrainGenerationWorkerParams, ThermalErosionWorkerParams, HydraulicErosionWorkerParams, TerrainFeatureCombinationWorkerParams } from './TerrainGenerationWorker';
/**
 * 地形生成工作线程管理器配置
 */
export interface TerrainGenerationWorkerManagerConfig {
    /** 最大工作线程数量 */
    maxWorkers?: number;
    /** 是否启用多线程 */
    enableMultithreading?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 地形生成工作线程管理器
 */
export declare class TerrainGenerationWorkerManager {
    /** 工作线程池 */
    private workers;
    /** 工作线程状态 */
    private workerStatus;
    /** 工作线程任务队列 */
    private taskQueue;
    /** 事件发射器 */
    private eventEmitter;
    /** 配置 */
    private config;
    /** 是否支持Web Worker */
    private supportsWorkers;
    /**
     * 创建地形生成工作线程管理器
     * @param config 配置
     */
    constructor(config?: TerrainGenerationWorkerManagerConfig);
    /**
     * 检查Web Worker支持
     * @returns 是否支持Web Worker
     */
    private checkWorkerSupport;
    /**
     * 初始化工作线程
     */
    private initWorkers;
    /**
     * 处理工作线程消息
     * @param workerId 工作线程ID
     * @param message 消息
     */
    private handleWorkerMessage;
    /**
     * 处理下一个任务
     */
    private processNextTask;
    /**
     * 查找空闲工作线程
     * @returns 空闲工作线程ID，如果没有则返回-1
     */
    private findIdleWorker;
    /**
     * 获取可转移对象
     * @param data 数据
     * @returns 可转移对象数组
     */
    private getTransferables;
    /**
     * 添加任务到队列
     * @param type 任务类型
     * @param data 任务数据
     * @returns Promise
     */
    private addTask;
    /**
     * 在主线程中执行任务
     * @param type 任务类型
     * @param data 任务数据
     * @param resolve 解析函数
     * @param reject 拒绝函数
     */
    private executeTaskInMainThread;
    /**
     * 生成地形
     * @param params 参数
     * @returns Promise
     */
    generateTerrain(params: TerrainGenerationWorkerParams): Promise<{
        heightData: Float32Array;
    }>;
    /**
     * 应用侵蚀
     * @param params 参数
     * @returns Promise
     */
    applyErosion(params: ThermalErosionWorkerParams | HydraulicErosionWorkerParams): Promise<{
        heightData: Float32Array;
    }>;
    /**
     * 生成特征
     * @param params 参数
     * @returns Promise
     */
    generateFeature(params: any): Promise<{
        heightData: Float32Array;
    }>;
    /**
     * 生成特征组合
     * @param params 参数
     * @returns Promise
     */
    generateFeatureCombination(params: TerrainFeatureCombinationWorkerParams): Promise<{
        heightData: Float32Array;
    }>;
    /**
     * 销毁管理器
     */
    dispose(): void;
}
