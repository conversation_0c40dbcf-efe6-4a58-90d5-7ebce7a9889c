{
  "title": "角色创建教程",
  "description": "导入和设置3D角色模型，配置骨骼和动画系统，添加控制器和交互功能。",
  "category": "tutorial",
  "tags": ["角色", "动画", "骨骼", "控制器", "交互", "教程"],
  "version": "1.0.0",
  "author": "DL（Digital Learning）引擎团队",
  "license": "MIT",
  "preview": "assets/images/preview.jpg",
  "difficulty": "intermediate",
  "features": [
    {
      "title": "角色模型导入",
      "description": "导入和优化3D角色模型"
    },
    {
      "title": "骨骼设置和绑定",
      "description": "配置骨骼层次结构和蒙皮权重"
    },
    {
      "title": "动画导入和设置",
      "description": "导入和调整角色动画"
    },
    {
      "title": "动画状态机配置",
      "description": "创建和编辑动画状态机"
    },
    {
      "title": "角色控制器实现",
      "description": "实现角色移动和交互控制"
    },
    {
      "title": "角色交互功能",
      "description": "添加角色与环境和其他对象的交互"
    }
  ],
  "requirements": {
    "engineVersion": ">=1.0.0",
    "editorVersion": ">=1.0.0",
    "dependencies": []
  },
  "steps": [
    {
      "title": "准备角色模型",
      "description": "获取适合的角色模型，确保模型包含正确的骨骼结构和UV映射，检查模型的多边形数量和材质设置。"
    },
    {
      "title": "导入角色模型",
      "description": "打开DL（Digital Learning）引擎编辑器，选择"文件 > 导入 > 模型"，选择角色模型文件，设置导入选项，点击"导入"按钮。"
    },
    {
      "title": "设置骨骼和蒙皮",
      "description": "选择导入的角色模型，打开"骨骼编辑器"面板，检查骨骼层次结构，调整骨骼位置和方向，检查和编辑蒙皮权重，设置IK控制器。"
    },
    {
      "title": "导入和设置动画",
      "description": "选择"文件 > 导入 > 动画"，选择动画文件，设置动画导入选项，将动画绑定到角色骨骼，调整动画参数，预览和测试动画。"
    },
    {
      "title": "创建动画状态机",
      "description": "打开"动画状态机编辑器"，创建基本状态，添加动画剪辑到状态，创建状态之间的过渡，设置过渡条件和混合参数，添加参数控制，测试状态机功能。"
    },
    {
      "title": "实现角色控制器",
      "description": "创建角色控制器脚本，实现基本移动功能，添加输入控制，连接控制器与动画状态机，实现摄像机跟随功能，添加物理碰撞和重力。"
    },
    {
      "title": "添加角色交互功能",
      "description": "创建交互系统，实现物体拾取和使用，添加对话和UI交互，实现环境交互，添加特殊能力和效果，测试所有交互功能。"
    }
  ],
  "resources": [
    {
      "title": "角色模型资源",
      "url": "../assets/models/characters/",
      "type": "model"
    },
    {
      "title": "动画资源",
      "url": "../assets/animations/",
      "type": "animation"
    },
    {
      "title": "材质模板",
      "url": "../assets/materials/characters/",
      "type": "material"
    },
    {
      "title": "控制器脚本示例",
      "url": "../assets/scripts/controllers/",
      "type": "script"
    },
    {
      "title": "交互系统文档",
      "url": "../../docs/interaction-system.md",
      "type": "documentation"
    }
  ],
  "relatedExamples": [
    {
      "id": "animation-demo",
      "title": "动画系统演示",
      "description": "展示关键帧动画、骨骼动画和动画混合功能。"
    },
    {
      "id": "physics-demo",
      "title": "物理系统演示",
      "description": "展示刚体物理、碰撞检测、物理约束和物理材质等功能。"
    },
    {
      "id": "interaction-demo",
      "title": "交互系统演示",
      "description": "展示对象交互、事件处理和用户输入等功能。"
    }
  ]
}
