import { Controller, Get, Post, Put, Delete, Param, Body, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { GatewayService, ServiceRoute } from './gateway.service';
import { ServiceDiscoveryService } from './service-discovery.service';
import { LoadBalancerService } from './load-balancer.service';
import { HealthCheckService } from './health-check.service';

@ApiTags('网关管理')
@Controller('gateway')
export class GatewayController {
  constructor(
    private readonly gatewayService: GatewayService,
    private readonly serviceDiscovery: ServiceDiscoveryService,
    private readonly loadBalancer: LoadBalancerService,
    private readonly healthCheck: HealthCheckService,
  ) {}

  @Get('routes')
  @ApiOperation({ summary: '获取所有路由配置' })
  @ApiResponse({ status: 200, description: '路由配置列表' })
  getAllRoutes() {
    return {
      success: true,
      data: this.gatewayService.getAllRoutes(),
    };
  }

  @Post('routes')
  @ApiOperation({ summary: '添加新路由' })
  @ApiResponse({ status: 201, description: '路由添加成功' })
  addRoute(@Body() route: ServiceRoute) {
    this.gatewayService.addRoute(route);
    return {
      success: true,
      message: '路由添加成功',
      data: route,
    };
  }

  @Put('routes/:path')
  @ApiOperation({ summary: '更新路由配置' })
  @ApiParam({ name: 'path', description: '路由路径' })
  @ApiResponse({ status: 200, description: '路由更新成功' })
  updateRoute(
    @Param('path') path: string,
    @Body() route: Partial<ServiceRoute>
  ) {
    const updated = this.gatewayService.updateRoute(decodeURIComponent(path), route);
    
    if (updated) {
      return {
        success: true,
        message: '路由更新成功',
      };
    } else {
      return {
        success: false,
        message: '路由不存在',
      };
    }
  }

  @Delete('routes/:path')
  @ApiOperation({ summary: '删除路由' })
  @ApiParam({ name: 'path', description: '路由路径' })
  @ApiResponse({ status: 200, description: '路由删除成功' })
  removeRoute(@Param('path') path: string) {
    const removed = this.gatewayService.removeRoute(decodeURIComponent(path));
    
    if (removed) {
      return {
        success: true,
        message: '路由删除成功',
      };
    } else {
      return {
        success: false,
        message: '路由不存在',
      };
    }
  }

  @Get('services')
  @ApiOperation({ summary: '获取所有服务实例' })
  @ApiResponse({ status: 200, description: '服务实例列表' })
  getAllServices() {
    const services = this.serviceDiscovery.getAllServices();
    const serviceList = Array.from(services.entries()).map(([name, instances]) => ({
      name,
      instances: instances.map(instance => ({
        id: instance.id,
        address: instance.address,
        port: instance.port,
        health: instance.health,
        metadata: instance.metadata,
        lastSeen: instance.lastSeen,
      })),
    }));

    return {
      success: true,
      data: serviceList,
    };
  }

  @Get('services/:serviceName')
  @ApiOperation({ summary: '获取指定服务的实例' })
  @ApiParam({ name: 'serviceName', description: '服务名称' })
  @ApiResponse({ status: 200, description: '服务实例列表' })
  async getServiceInstances(@Param('serviceName') serviceName: string) {
    const instances = await this.serviceDiscovery.getServiceInstances(serviceName);
    
    return {
      success: true,
      data: {
        serviceName,
        instances,
      },
    };
  }

  @Get('services/:serviceName/select')
  @ApiOperation({ summary: '选择服务实例（负载均衡）' })
  @ApiParam({ name: 'serviceName', description: '服务名称' })
  @ApiResponse({ status: 200, description: '选中的服务实例URL' })
  async selectServiceInstance(@Param('serviceName') serviceName: string) {
    const instances = await this.serviceDiscovery.getServiceInstances(serviceName);
    const selectedUrl = this.loadBalancer.selectInstance(serviceName, instances);
    
    return {
      success: true,
      data: {
        serviceName,
        selectedUrl,
        totalInstances: instances.length,
      },
    };
  }

  @Get('health')
  @ApiOperation({ summary: '获取所有服务健康状态' })
  @ApiResponse({ status: 200, description: '健康状态统计' })
  getHealthStatus() {
    const stats = this.healthCheck.getHealthStats();
    
    return {
      success: true,
      data: stats,
    };
  }

  @Get('health/:serviceName')
  @ApiOperation({ summary: '获取指定服务健康状态' })
  @ApiParam({ name: 'serviceName', description: '服务名称' })
  @ApiResponse({ status: 200, description: '服务健康状态' })
  async getServiceHealth(@Param('serviceName') serviceName: string) {
    const healthResults = await this.healthCheck.getServiceHealth(serviceName);
    const isHealthy = await this.healthCheck.isServiceHealthy(serviceName);
    
    return {
      success: true,
      data: {
        serviceName,
        isHealthy,
        instances: healthResults,
      },
    };
  }

  @Post('health/:serviceName/check')
  @ApiOperation({ summary: '手动触发健康检查' })
  @ApiParam({ name: 'serviceName', description: '服务名称' })
  @ApiResponse({ status: 200, description: '健康检查已触发' })
  async triggerHealthCheck(@Param('serviceName') serviceName: string) {
    await this.healthCheck.triggerHealthCheck(serviceName);
    
    return {
      success: true,
      message: `已触发 ${serviceName} 的健康检查`,
    };
  }

  @Get('load-balancer/stats')
  @ApiOperation({ summary: '获取负载均衡统计信息' })
  @ApiResponse({ status: 200, description: '负载均衡统计' })
  getLoadBalancerStats() {
    const stats = this.loadBalancer.getLoadBalancerStats();
    
    return {
      success: true,
      data: stats,
    };
  }

  @Post('load-balancer/reset')
  @ApiOperation({ summary: '重置负载均衡统计' })
  @ApiResponse({ status: 200, description: '统计信息已重置' })
  resetLoadBalancerStats() {
    this.loadBalancer.resetStats();
    
    return {
      success: true,
      message: '负载均衡统计信息已重置',
    };
  }

  @Get('stats')
  @ApiOperation({ summary: '获取网关统计信息' })
  @ApiResponse({ status: 200, description: '网关统计信息' })
  async getGatewayStats() {
    const gatewayStats = await this.gatewayService.getGatewayStats();
    const serviceStats = this.serviceDiscovery.getServiceStats();
    const healthStats = this.healthCheck.getHealthStats();
    const loadBalancerStats = this.loadBalancer.getLoadBalancerStats();
    
    return {
      success: true,
      data: {
        gateway: gatewayStats,
        services: serviceStats,
        health: healthStats,
        loadBalancer: loadBalancerStats,
        timestamp: new Date(),
      },
    };
  }

  @Get('discovery/stats')
  @ApiOperation({ summary: '获取服务发现统计信息' })
  @ApiResponse({ status: 200, description: '服务发现统计' })
  getServiceDiscoveryStats() {
    const stats = this.serviceDiscovery.getServiceStats();
    
    return {
      success: true,
      data: stats,
    };
  }

  @Post('services/:serviceName/register')
  @ApiOperation({ summary: '注册服务实例' })
  @ApiParam({ name: 'serviceName', description: '服务名称' })
  @ApiResponse({ status: 201, description: '服务注册成功' })
  async registerService(
    @Param('serviceName') serviceName: string,
    @Body() serviceInfo: {
      address: string;
      port: number;
      metadata?: Record<string, any>;
    }
  ) {
    await this.serviceDiscovery.registerService({
      name: serviceName,
      address: serviceInfo.address,
      port: serviceInfo.port,
      health: true,
      metadata: serviceInfo.metadata || {},
    });
    
    return {
      success: true,
      message: '服务注册成功',
    };
  }

  @Delete('services/:serviceName/instances/:instanceId')
  @ApiOperation({ summary: '注销服务实例' })
  @ApiParam({ name: 'serviceName', description: '服务名称' })
  @ApiParam({ name: 'instanceId', description: '实例ID' })
  @ApiResponse({ status: 200, description: '服务注销成功' })
  async deregisterService(
    @Param('serviceName') serviceName: string,
    @Param('instanceId') instanceId: string
  ) {
    await this.serviceDiscovery.deregisterService(serviceName, instanceId);
    
    return {
      success: true,
      message: '服务注销成功',
    };
  }

  @Get('health/:serviceName/trend')
  @ApiOperation({ summary: '获取服务健康趋势' })
  @ApiParam({ name: 'serviceName', description: '服务名称' })
  @ApiQuery({ name: 'instanceId', description: '实例ID', required: false })
  @ApiResponse({ status: 200, description: '健康趋势数据' })
  getHealthTrend(
    @Param('serviceName') serviceName: string,
    @Query('instanceId') instanceId?: string
  ) {
    const trend = this.healthCheck.getHealthTrend(serviceName, instanceId);
    
    return {
      success: true,
      data: trend,
    };
  }
}
