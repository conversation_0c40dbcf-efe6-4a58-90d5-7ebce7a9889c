/**
 * 地形顶点着色器
 */
export declare const terrainVertexShader = "\n// \u57FA\u7840\u53C2\u6570\nuniform vec2 uTerrainSize;\nuniform float uMaxHeight;\n\n// \u7EB9\u7406\u53C2\u6570\nuniform int uLayerCount;\nuniform sampler2D uTextures[8];\nuniform sampler2D uNormalMaps[8];\nuniform sampler2D uRoughnessMaps[8];\nuniform sampler2D uDisplacementMaps[8];\nuniform sampler2D uAOMaps[8];\nuniform float uTilingFactors[8];\nuniform float uMinHeights[8];\nuniform float uMaxHeights[8];\nuniform float uMinSlopes[8];\nuniform float uMaxSlopes[8];\n\n// \u6DF7\u5408\u53C2\u6570\nuniform sampler2D uBlendMaps[8];\nuniform bool uUseBlendMaps;\n\n// \u5149\u7167\u53C2\u6570\nuniform vec3 uLightPosition;\nuniform vec3 uLightColor;\nuniform vec3 uAmbientColor;\n\n// \u96FE\u53C2\u6570\nuniform vec3 uFogColor;\nuniform float uFogNear;\nuniform float uFogFar;\nuniform bool uUseFog;\n\n// \u9876\u70B9\u5C5E\u6027\nattribute vec3 position;\nattribute vec3 normal;\nattribute vec2 uv;\nattribute vec4 tangent;\n\n// \u4F20\u9012\u7ED9\u7247\u6BB5\u7740\u8272\u5668\u7684\u53D8\u91CF\nvarying vec2 vUv;\nvarying vec3 vNormal;\nvarying vec3 vPosition;\nvarying vec3 vWorldPosition;\nvarying vec4 vTangent;\nvarying float vHeight;\nvarying float vSlope;\nvarying float vFogFactor;\n\n// \u8BA1\u7B97\u659C\u5EA6\nfloat calculateSlope(vec3 normal) {\n  // \u8BA1\u7B97\u6CD5\u7EBF\u4E0E\u5782\u76F4\u5411\u4E0A\u65B9\u5411\u7684\u5939\u89D2\n  float dotProduct = dot(normal, vec3(0.0, 1.0, 0.0));\n  float angle = acos(dotProduct) * 180.0 / 3.14159265359;\n  return angle;\n}\n\nvoid main() {\n  // \u8BA1\u7B97UV\u5750\u6807\n  vUv = uv;\n  \n  // \u8BA1\u7B97\u6CD5\u7EBF\u548C\u5207\u7EBF\n  vNormal = normalize(normalMatrix * normal);\n  vTangent = vec4(normalize(normalMatrix * tangent.xyz), tangent.w);\n  \n  // \u8BA1\u7B97\u9876\u70B9\u4F4D\u7F6E\n  vec4 worldPosition = modelMatrix * vec4(position, 1.0);\n  vWorldPosition = worldPosition.xyz;\n  vPosition = position;\n  \n  // \u8BA1\u7B97\u9AD8\u5EA6\u548C\u659C\u5EA6\n  vHeight = position.y / uMaxHeight;\n  vSlope = calculateSlope(vNormal);\n  \n  // \u8BA1\u7B97\u96FE\u56E0\u5B50\n  if (uUseFog) {\n    float fogDistance = length(worldPosition.xyz - cameraPosition);\n    vFogFactor = smoothstep(uFogNear, uFogFar, fogDistance);\n  } else {\n    vFogFactor = 0.0;\n  }\n  \n  // \u8F93\u51FA\u88C1\u526A\u7A7A\u95F4\u5750\u6807\n  gl_Position = projectionMatrix * viewMatrix * worldPosition;\n}\n";
