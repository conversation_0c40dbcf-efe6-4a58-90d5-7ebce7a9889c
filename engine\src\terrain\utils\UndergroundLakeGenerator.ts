/**
 * 地下湖泊生成器
 * 用于生成地下湖泊系统
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';
import { Debug } from '../../utils/Debug';

/**
 * 湖泊形状类型
 */
export enum LakeShapeType {
  /** 圆形 */
  CIRCULAR = 'circular',
  /** 不规则 */
  IRREGULAR = 'irregular',
  /** 分叉 */
  BRANCHING = 'branching',
  /** 连接 */
  CONNECTED = 'connected',
  /** 洞穴型 */
  CAVERN = 'cavern'
}

/**
 * 地下瀑布数据
 */
export interface UndergroundWaterfall {
  /** 起点X坐标 */
  startX: number;
  /** 起点Z坐标 */
  startZ: number;
  /** 终点X坐标 */
  endX: number;
  /** 终点Z坐标 */
  endZ: number;
  /** 高度 */
  height: number;
  /** 宽度 */
  width: number;
  /** 流量 */
  flowRate: number;
}

/**
 * 地下温泉数据
 */
export interface HotSpring {
  /** X坐标 */
  x: number;
  /** Z坐标 */
  z: number;
  /** 半径 */
  radius: number;
  /** 温度 */
  temperature: number;
  /** 气泡率 */
  bubbleRate: number;
}

/**
 * 钟乳石数据
 */
export interface Stalactite {
  /** X坐标 */
  x: number;
  /** Z坐标 */
  z: number;
  /** 高度 */
  height: number;
  /** 半径 */
  radius: number;
  /** 类型（钟乳石/石笋） */
  type: 'stalactite' | 'stalagmite';
}

/**
 * 地下湖泊生成参数
 */
export interface UndergroundLakeParams {
  /** 湖泊数量 */
  count: number;
  /** 湖泊最小半径 */
  minRadius: number;
  /** 湖泊最大半径 */
  maxRadius: number;
  /** 湖泊深度 */
  depth: number;
  /** 湖泊复杂度（形状不规则程度） */
  complexity: number;
  /** 湖泊深度变化（湖底不平整程度） */
  depthVariation: number;
  /** 连接到洞穴的概率 */
  caveProbability: number;
  /** 连接到地下河的概率 */
  riverProbability: number;
  /** 最小深度（相对于地形高度的比例） */
  minDepthRatio: number;
  /** 最大深度（相对于地形高度的比例） */
  maxDepthRatio: number;
  /** 湖泊形状类型 */
  shapeType?: LakeShapeType;
  /** 是否生成钟乳石 */
  generateStalactites?: boolean;
  /** 钟乳石密度 */
  stalactiteDensity?: number;
  /** 是否生成地下瀑布 */
  generateWaterfall?: boolean;
  /** 瀑布高度 */
  waterfallHeight?: number;
  /** 是否生成温泉 */
  generateHotSpring?: boolean;
  /** 温泉温度 */
  hotSpringTemperature?: number;
  /** 种子 */
  seed: number;
}

/**
 * 地下湖泊节点
 */
export interface UndergroundLakeNode {
  /** X坐标 */
  x: number;
  /** Z坐标 */
  z: number;
  /** 半径 */
  radius: number;
  /** 深度比例 */
  depthRatio: number;
  /** 形状控制点（用于不规则形状） */
  controlPoints?: { x: number, z: number, influence: number }[];
  /** 节点类型 */
  type?: 'main' | 'branch' | 'connection';
}

/**
 * 地下湖泊
 */
export interface UndergroundLake {
  /** 湖泊节点 */
  nodes: UndergroundLakeNode[];
  /** 连接的洞穴 */
  connectedCaves: any[];
  /** 连接的河流 */
  connectedRivers: any[];
  /** 湖泊形状类型 */
  shapeType?: LakeShapeType;
  /** 瀑布列表 */
  waterfalls?: UndergroundWaterfall[];
  /** 温泉列表 */
  hotSprings?: HotSpring[];
  /** 钟乳石列表 */
  stalactites?: Stalactite[];
}

/**
 * 地下湖泊生成器
 */
export class UndergroundLakeGenerator {
  /**
   * 生成地下湖泊系统
   * @param terrain 地形组件
   * @param params 地下湖泊生成参数
   */
  public static generateUndergroundLakes(terrain: TerrainComponent, params: UndergroundLakeParams): void {
    const {
      count,
      minRadius,
      maxRadius,
      depth,
      complexity,
      depthVariation,
      caveProbability,
      riverProbability,
      minDepthRatio,
      maxDepthRatio,
      shapeType = LakeShapeType.CIRCULAR,
      generateStalactites = false,
      stalactiteDensity = 0.5,
      generateWaterfall = false,
      waterfallHeight = 10,
      generateHotSpring = false,
      hotSpringTemperature = 80,
      seed
    } = params;

    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 生成多个地下湖泊
    const lakes: UndergroundLake[] = [];
    for (let i = 0; i < count; i++) {
      // 随机选择湖泊中心
      const centerX = Math.floor(random() * resolution);
      const centerZ = Math.floor(random() * resolution);

      // 随机湖泊半径
      const radius = minRadius + random() * (maxRadius - minRadius);

      // 随机选择深度
      const depthRatio = minDepthRatio + random() * (maxDepthRatio - minDepthRatio);

      // 生成湖泊
      const lake = this.generateLake(
        terrain,
        centerX,
        centerZ,
        radius,
        depth,
        complexity,
        depthVariation,
        depthRatio,
        shapeType,
        generateStalactites,
        stalactiteDensity,
        generateWaterfall,
        waterfallHeight,
        generateHotSpring,
        hotSpringTemperature,
        random
      );

      lakes.push(lake);
    }

    // 应用湖泊到地形
    this.applyLakesToTerrain(terrain, lakes, depth);

    // 连接湖泊到洞穴
    if (caveProbability > 0 && terrain.metadata && terrain.metadata.caves) {
      this.connectLakesToCaves(terrain, lakes, caveProbability, random);
    }

    // 连接湖泊到地下河
    if (riverProbability > 0 && terrain.metadata && terrain.metadata.undergroundRivers) {
      this.connectLakesToRivers(terrain, lakes, riverProbability, random);
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 生成单个地下湖泊
   * @param terrain 地形组件
   * @param centerX 中心X坐标
   * @param centerZ 中心Z坐标
   * @param radius 半径
   * @param depth 深度
   * @param complexity 复杂度
   * @param depthVariation 深度变化
   * @param depthRatio 深度比例
   * @param shapeType 湖泊形状类型
   * @param generateStalactites 是否生成钟乳石
   * @param stalactiteDensity 钟乳石密度
   * @param generateWaterfall 是否生成瀑布
   * @param waterfallHeight 瀑布高度
   * @param generateHotSpring 是否生成温泉
   * @param hotSpringTemperature 温泉温度
   * @param random 随机数生成器
   * @returns 地下湖泊
   */
  private static generateLake(
    terrain: TerrainComponent,
    centerX: number,
    centerZ: number,
    radius: number,
    depth: number,
    complexity: number,
    depthVariation: number,
    depthRatio: number,
    shapeType: LakeShapeType,
    generateStalactites: boolean,
    stalactiteDensity: number,
    generateWaterfall: boolean,
    waterfallHeight: number,
    generateHotSpring: boolean,
    hotSpringTemperature: number,
    random: () => number
  ): UndergroundLake {
    // 创建湖泊
    const lake: UndergroundLake = {
      nodes: [],
      connectedCaves: [],
      connectedRivers: [],
      shapeType: shapeType,
      waterfalls: [],
      hotSprings: [],
      stalactites: []
    };

    // 添加中心节点
    lake.nodes.push({
      x: centerX,
      z: centerZ,
      radius: radius,
      depthRatio: depthRatio,
      type: 'main'
    });

    // 根据形状类型生成不同的湖泊形状
    switch (shapeType) {
      case LakeShapeType.CIRCULAR:
        // 圆形湖泊只需要中心节点
        break;
      case LakeShapeType.IRREGULAR:
        // 不规则湖泊需要添加控制点
        this.generateIrregularLake(lake, centerX, centerZ, radius, depthRatio, complexity, random);
        break;
      case LakeShapeType.BRANCHING:
        // 分叉湖泊需要添加分支
        this.generateBranchingLake(lake, centerX, centerZ, radius, depthRatio, complexity, random);
        break;
      case LakeShapeType.CONNECTED:
        // 连接湖泊需要添加多个主要节点并连接它们
        this.generateConnectedLake(lake, centerX, centerZ, radius, depthRatio, complexity, random);
        break;
      case LakeShapeType.CAVERN:
        // 洞穴型湖泊需要更复杂的形状
        this.generateCavernLake(lake, centerX, centerZ, radius, depthRatio, complexity, random);
        break;
      default:
        // 默认情况下，添加一些随机节点使湖泊形状不规则
        if (complexity > 0) {
          const nodeCount = Math.floor(complexity * 5) + 1;
          for (let i = 0; i < nodeCount; i++) {
            // 随机选择与中心的距离和角度
            const distance = radius * (0.3 + random() * 0.7);
            const angle = random() * Math.PI * 2;

            // 计算节点位置
            const nodeX = centerX + Math.cos(angle) * distance;
            const nodeZ = centerZ + Math.sin(angle) * distance;

            // 随机节点半径
            const nodeRadius = radius * (0.3 + random() * 0.7);

            // 随机节点深度比例
            const nodeDepthRatio = depthRatio * (0.8 + random() * 0.4);

            // 添加节点
            lake.nodes.push({
              x: nodeX,
              z: nodeZ,
              radius: nodeRadius,
              depthRatio: nodeDepthRatio,
              type: 'branch'
            });
          }
        }
        break;
    }

    // 生成钟乳石
    if (generateStalactites) {
      this.generateStalactites(lake, stalactiteDensity, random);
    }

    // 生成瀑布
    if (generateWaterfall) {
      this.generateWaterfall(lake, waterfallHeight, random);
    }

    // 生成温泉
    if (generateHotSpring) {
      this.generateHotSpring(lake, hotSpringTemperature, random);
    }

    // 应用湖泊到地形
    this.applyLakeToTerrain(terrain, lake, depth, depthVariation, random);

    return lake;
  }

  /**
   * 应用湖泊到地形
   * @param terrain 地形组件
   * @param lake 湖泊
   * @param depth 深度
   * @param depthVariation 深度变化
   * @param random 随机数生成器
   */
  private static applyLakeToTerrain(
    terrain: TerrainComponent,
    lake: UndergroundLake,
    depth: number,
    depthVariation: number,
    random: () => number
  ): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 对于湖泊中的每个节点
    for (const node of lake.nodes) {
      // 计算节点范围
      const minX = Math.max(0, Math.floor(node.x - node.radius));
      const maxX = Math.min(resolution - 1, Math.ceil(node.x + node.radius));
      const minZ = Math.max(0, Math.floor(node.z - node.radius));
      const maxZ = Math.min(resolution - 1, Math.ceil(node.z + node.radius));

      // 对范围内的每个点应用湖泊深度
      for (let z = minZ; z <= maxZ; z++) {
        for (let x = minX; x <= maxX; x++) {
          // 计算到节点中心的距离
          const dx = x - node.x;
          const dz = z - node.z;
          const distance = Math.sqrt(dx * dx + dz * dz);

          // 如果在节点半径内
          if (distance <= node.radius) {
            const index = z * resolution + x;

            // 计算基础深度因子（基于距离）
            let depthFactor = Math.pow(1 - (distance / node.radius), 2);

            // 如果节点有控制点，使用它们调整形状
            if (node.controlPoints && node.controlPoints.length > 0) {
              // 计算控制点的影响
              let totalInfluence = 0;
              let influencedDepthFactor = 0;

              for (const controlPoint of node.controlPoints) {
                // 计算到控制点的距离
                const cdx = x - controlPoint.x;
                const cdz = z - controlPoint.z;
                const cDistance = Math.sqrt(cdx * cdx + cdz * cdz);

                // 计算控制点的影响（基于距离）
                const influence = Math.max(0, 1 - cDistance / (node.radius * 0.8)) * controlPoint.influence;

                // 累加影响
                totalInfluence += influence;
                influencedDepthFactor += depthFactor * (1 + influence);
              }

              // 应用控制点影响
              if (totalInfluence > 0) {
                depthFactor = influencedDepthFactor / (totalInfluence + 1);
              }
            }

            // 添加深度变化（噪声）
            if (depthVariation > 0) {
              const noiseValue = this.simplexNoise(x * 0.1, z * 0.1, Math.floor(random() * 1000)) * depthVariation;
              depthFactor *= (1 + noiseValue);
            }

            // 根据节点类型调整深度因子
            if (node.type === 'branch') {
              // 分支节点深度略浅
              depthFactor *= 0.9;
            } else if (node.type === 'connection') {
              // 连接节点深度更浅
              depthFactor *= 0.8;
            }

            // 应用湖泊深度
            heightData[index] -= depth * depthFactor * node.depthRatio;

            // 确保高度不会低于0
            heightData[index] = Math.max(0, heightData[index]);
          }
        }
      }
    }
  }

  /**
   * 应用湖泊到地形
   * @param terrain 地形组件
   * @param lakes 湖泊列表
   * @param depth 深度
   */
  private static applyLakesToTerrain(terrain: TerrainComponent, lakes: UndergroundLake[], depth: number): void {
    // 创建湖泊数据
    if (!terrain.metadata) {
      terrain.metadata = {};
    }

    // 存储湖泊数据
    terrain.metadata.undergroundLakes = lakes;
  }

  /**
   * 连接湖泊到洞穴
   * @param terrain 地形组件
   * @param lakes 湖泊列表
   * @param caveProbability 洞穴概率
   * @param random 随机数生成器
   */
  private static connectLakesToCaves(
    terrain: TerrainComponent,
    lakes: UndergroundLake[],
    caveProbability: number,
    random: () => number
  ): void {
    // 获取洞穴数据
    const caves = terrain.metadata.caves;

    // 遍历所有湖泊
    for (const lake of lakes) {
      // 根据概率决定是否连接到洞穴
      if (random() < caveProbability) {
        this.connectLakeToCave(terrain, lake, caves, random);
      }
    }
  }

  /**
   * 连接湖泊到洞穴
   * @param terrain 地形组件
   * @param lake 湖泊
   * @param caves 洞穴数据
   * @param random 随机数生成器
   */
  private static connectLakeToCave(
    terrain: TerrainComponent,
    lake: UndergroundLake,
    caves: any,
    random: () => number
  ): void {
    const resolution = terrain.resolution;

    // 如果洞穴数据是数组形式
    if (Array.isArray(caves)) {
      // 找到最近的洞穴
      let closestCave = null;
      let minDistance = Infinity;

      // 随机选择湖泊上的一个节点作为连接点
      const lakeNodeIndex = Math.floor(random() * lake.nodes.length);
      const lakeNode = lake.nodes[lakeNodeIndex];

      // 遍历所有洞穴，找到最近的洞穴
      for (const cave of caves) {
        // 假设洞穴数据包含中心点坐标
        if (cave.center) {
          const dx = cave.center.x - lakeNode.x;
          const dz = cave.center.z - lakeNode.z;
          const distance = Math.sqrt(dx * dx + dz * dz);

          if (distance < minDistance) {
            minDistance = distance;
            closestCave = cave;
          }
        }
      }

      // 如果找到了最近的洞穴，创建连接通道
      if (closestCave && minDistance < resolution * 0.3) { // 限制最大连接距离
        this.createConnectionTunnel(terrain, lakeNode, closestCave, random);
        lake.connectedCaves.push(closestCave);
      }
    }
  }

  /**
   * 连接湖泊到地下河
   * @param terrain 地形组件
   * @param lakes 湖泊列表
   * @param riverProbability 河流概率
   * @param random 随机数生成器
   */
  private static connectLakesToRivers(
    terrain: TerrainComponent,
    lakes: UndergroundLake[],
    riverProbability: number,
    random: () => number
  ): void {
    // 获取地下河数据
    const rivers = terrain.metadata.undergroundRivers;

    // 遍历所有湖泊
    for (const lake of lakes) {
      // 根据概率决定是否连接到地下河
      if (random() < riverProbability) {
        this.connectLakeToRiver(terrain, lake, rivers, random);
      }
    }
  }

  /**
   * 连接湖泊到地下河
   * @param terrain 地形组件
   * @param lake 湖泊
   * @param rivers 地下河数据
   * @param random 随机数生成器
   */
  private static connectLakeToRiver(
    terrain: TerrainComponent,
    lake: UndergroundLake,
    rivers: any[],
    random: () => number
  ): void {
    const resolution = terrain.resolution;

    // 找到最近的河流节点
    let closestRiverNode = null;
    let closestRiver = null;
    let minDistance = Infinity;

    // 随机选择湖泊上的一个节点作为连接点
    const lakeNodeIndex = Math.floor(random() * lake.nodes.length);
    const lakeNode = lake.nodes[lakeNodeIndex];

    // 遍历所有河流
    for (const river of rivers) {
      // 遍历河流上的所有节点
      for (const riverNode of river.nodes) {
        const dx = riverNode.x - lakeNode.x;
        const dz = riverNode.z - lakeNode.z;
        const distance = Math.sqrt(dx * dx + dz * dz);

        if (distance < minDistance) {
          minDistance = distance;
          closestRiverNode = riverNode;
          closestRiver = river;
        }
      }
    }

    // 如果找到了最近的河流节点，创建连接通道
    if (closestRiverNode && closestRiver && minDistance < resolution * 0.3) { // 限制最大连接距离
      this.createRiverConnection(terrain, lakeNode, closestRiverNode, random);
      lake.connectedRivers.push(closestRiver);
    }
  }

  /**
   * 创建连接通道
   * @param terrain 地形组件
   * @param lakeNode 湖泊节点
   * @param cave 洞穴
   * @param random 随机数生成器
   */
  private static createConnectionTunnel(
    terrain: TerrainComponent,
    lakeNode: UndergroundLakeNode,
    cave: any,
    random: () => number
  ): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 获取洞穴中心
    const caveCenter = cave.center;

    // 计算方向向量
    const dx = caveCenter.x - lakeNode.x;
    const dz = caveCenter.z - lakeNode.z;
    const distance = Math.sqrt(dx * dx + dz * dz);
    const dirX = dx / distance;
    const dirZ = dz / distance;

    // 通道宽度
    const tunnelWidth = lakeNode.radius * 0.3;

    // 创建通道
    for (let d = 0; d < distance; d += 0.5) {
      // 计算当前位置
      const x = Math.floor(lakeNode.x + dirX * d);
      const z = Math.floor(lakeNode.z + dirZ * d);

      // 如果位置在地形范围内
      if (x >= 0 && x < resolution && z >= 0 && z < resolution) {
        // 计算通道范围
        const minX = Math.max(0, Math.floor(x - tunnelWidth));
        const maxX = Math.min(resolution - 1, Math.ceil(x + tunnelWidth));
        const minZ = Math.max(0, Math.floor(z - tunnelWidth));
        const maxZ = Math.min(resolution - 1, Math.ceil(z + tunnelWidth));

        // 对范围内的每个点应用通道深度
        for (let tz = minZ; tz <= maxZ; tz++) {
          for (let tx = minX; tx <= maxX; tx++) {
            // 计算到通道中心的距离
            const tdx = tx - x;
            const tdz = tz - z;
            const tDistance = Math.sqrt(tdx * tdx + tdz * tdz);

            // 如果在通道宽度内
            if (tDistance <= tunnelWidth) {
              const index = tz * resolution + tx;

              // 计算深度因子（基于距离）
              const depthFactor = Math.pow(1 - (tDistance / tunnelWidth), 2);

              // 应用通道深度
              heightData[index] -= lakeNode.depthRatio * depthFactor;

              // 确保高度不会低于0
              heightData[index] = Math.max(0, heightData[index]);
            }
          }
        }
      }
    }
  }

  /**
   * 创建河流连接
   * @param terrain 地形组件
   * @param lakeNode 湖泊节点
   * @param riverNode 河流节点
   * @param random 随机数生成器
   */
  private static createRiverConnection(
    terrain: TerrainComponent,
    lakeNode: UndergroundLakeNode,
    riverNode: any,
    random: () => number
  ): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 计算方向向量
    const dx = riverNode.x - lakeNode.x;
    const dz = riverNode.z - lakeNode.z;
    const distance = Math.sqrt(dx * dx + dz * dz);
    const dirX = dx / distance;
    const dirZ = dz / distance;

    // 连接宽度
    const connectionWidth = Math.min(lakeNode.radius * 0.3, riverNode.width);

    // 创建连接
    for (let d = 0; d < distance; d += 0.5) {
      // 计算当前位置
      const x = Math.floor(lakeNode.x + dirX * d);
      const z = Math.floor(lakeNode.z + dirZ * d);

      // 如果位置在地形范围内
      if (x >= 0 && x < resolution && z >= 0 && z < resolution) {
        // 计算连接范围
        const minX = Math.max(0, Math.floor(x - connectionWidth));
        const maxX = Math.min(resolution - 1, Math.ceil(x + connectionWidth));
        const minZ = Math.max(0, Math.floor(z - connectionWidth));
        const maxZ = Math.min(resolution - 1, Math.ceil(z + connectionWidth));

        // 对范围内的每个点应用连接深度
        for (let tz = minZ; tz <= maxZ; tz++) {
          for (let tx = minX; tx <= maxX; tx++) {
            // 计算到连接中心的距离
            const tdx = tx - x;
            const tdz = tz - z;
            const tDistance = Math.sqrt(tdx * tdx + tdz * tdz);

            // 如果在连接宽度内
            if (tDistance <= connectionWidth) {
              const index = tz * resolution + tx;

              // 计算深度因子（基于距离）
              const depthFactor = Math.pow(1 - (tDistance / connectionWidth), 2);

              // 应用连接深度
              heightData[index] -= lakeNode.depthRatio * depthFactor;

              // 确保高度不会低于0
              heightData[index] = Math.max(0, heightData[index]);
            }
          }
        }
      }
    }
  }

  /**
   * 创建随机数生成器
   * @param seed 种子
   * @returns 随机数生成器函数
   */
  private static createRandomGenerator(seed: number): () => number {
    // 简单的伪随机数生成器
    let s = seed;
    return () => {
      s = (s * 9301 + 49297) % 233280;
      return s / 233280;
    };
  }

  /**
   * 简化版Simplex噪声函数
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @returns 噪声值
   */
  private static simplexNoise(x: number, y: number, z: number): number {
    // 简化版Simplex噪声，实际项目中应使用完整实现
    return Math.sin(x * 0.1 + y * 0.2 + z * 0.3) * 0.5 + 0.5;
  }

  /**
   * 生成不规则湖泊
   * @param lake 湖泊
   * @param centerX 中心X坐标
   * @param centerZ 中心Z坐标
   * @param radius 半径
   * @param depthRatio 深度比例
   * @param complexity 复杂度
   * @param random 随机数生成器
   */
  private static generateIrregularLake(
    lake: UndergroundLake,
    centerX: number,
    centerZ: number,
    radius: number,
    depthRatio: number,
    complexity: number,
    random: () => number
  ): void {
    // 为中心节点添加控制点
    const mainNode = lake.nodes[0];
    mainNode.controlPoints = [];

    // 添加控制点以创建不规则形状
    const controlPointCount = Math.floor(complexity * 12) + 8;
    for (let i = 0; i < controlPointCount; i++) {
      const angle = (i / controlPointCount) * Math.PI * 2;
      const distance = radius * (0.7 + random() * 0.6); // 控制点距离中心的距离变化
      const influence = 0.3 + random() * 0.7; // 控制点影响力

      mainNode.controlPoints.push({
        x: centerX + Math.cos(angle) * distance,
        z: centerZ + Math.sin(angle) * distance,
        influence: influence
      });
    }

    // 添加一些小的凹凸节点
    const bumpCount = Math.floor(complexity * 8) + 4;
    for (let i = 0; i < bumpCount; i++) {
      const angle = random() * Math.PI * 2;
      const distance = radius * (0.6 + random() * 0.4);
      const nodeX = centerX + Math.cos(angle) * distance;
      const nodeZ = centerZ + Math.sin(angle) * distance;
      const nodeRadius = radius * (0.1 + random() * 0.3);
      const nodeDepthRatio = depthRatio * (0.7 + random() * 0.6);

      lake.nodes.push({
        x: nodeX,
        z: nodeZ,
        radius: nodeRadius,
        depthRatio: nodeDepthRatio,
        type: 'branch'
      });
    }
  }

  /**
   * 生成分叉湖泊
   * @param lake 湖泊
   * @param centerX 中心X坐标
   * @param centerZ 中心Z坐标
   * @param radius 半径
   * @param depthRatio 深度比例
   * @param complexity 复杂度
   * @param random 随机数生成器
   */
  private static generateBranchingLake(
    lake: UndergroundLake,
    centerX: number,
    centerZ: number,
    radius: number,
    depthRatio: number,
    complexity: number,
    random: () => number
  ): void {
    // 确定分支数量
    const branchCount = Math.floor(complexity * 4) + 2;

    // 创建分支
    for (let i = 0; i < branchCount; i++) {
      const angle = (i / branchCount) * Math.PI * 2 + random() * 0.5;
      const branchLength = radius * (1.0 + random() * 1.5);
      const segmentCount = Math.floor(branchLength / (radius * 0.3)) + 2;

      // 创建分支上的节点
      let prevX = centerX;
      let prevZ = centerZ;
      let currentRadius = radius * 0.8;
      let currentDepthRatio = depthRatio * 0.9;

      for (let j = 0; j < segmentCount; j++) {
        // 添加一些随机性到方向
        const segmentAngle = angle + (random() * 0.6 - 0.3);
        const segmentLength = radius * (0.3 + random() * 0.2);

        // 计算新位置
        const nodeX = prevX + Math.cos(segmentAngle) * segmentLength;
        const nodeZ = prevZ + Math.sin(segmentAngle) * segmentLength;

        // 每个节点的半径和深度比例逐渐减小
        currentRadius *= 0.8 + random() * 0.1;
        currentDepthRatio *= 0.9 + random() * 0.1;

        // 添加节点
        lake.nodes.push({
          x: nodeX,
          z: nodeZ,
          radius: currentRadius,
          depthRatio: currentDepthRatio,
          type: 'branch'
        });

        // 更新前一个位置
        prevX = nodeX;
        prevZ = nodeZ;
      }
    }
  }

  /**
   * 生成连接湖泊
   * @param lake 湖泊
   * @param centerX 中心X坐标
   * @param centerZ 中心Z坐标
   * @param radius 半径
   * @param depthRatio 深度比例
   * @param complexity 复杂度
   * @param random 随机数生成器
   */
  private static generateConnectedLake(
    lake: UndergroundLake,
    centerX: number,
    centerZ: number,
    radius: number,
    depthRatio: number,
    complexity: number,
    random: () => number
  ): void {
    // 确定连接的湖泊数量
    const lakeCount = Math.floor(complexity * 2) + 2;

    // 创建多个连接的湖泊
    for (let i = 0; i < lakeCount; i++) {
      const angle = (i / lakeCount) * Math.PI * 2 + random() * 0.5;
      const distance = radius * (1.5 + random() * 1.0);

      // 计算新湖泊中心
      const newCenterX = centerX + Math.cos(angle) * distance;
      const newCenterZ = centerZ + Math.sin(angle) * distance;

      // 新湖泊半径和深度
      const newRadius = radius * (0.6 + random() * 0.8);
      const newDepthRatio = depthRatio * (0.8 + random() * 0.4);

      // 添加新湖泊中心节点
      lake.nodes.push({
        x: newCenterX,
        z: newCenterZ,
        radius: newRadius,
        depthRatio: newDepthRatio,
        type: 'main'
      });

      // 创建连接通道
      this.createConnectionBetweenNodes(
        lake,
        { x: centerX, z: centerZ },
        { x: newCenterX, z: newCenterZ },
        radius * 0.4,
        depthRatio * 0.9,
        random
      );
    }
  }

  /**
   * 生成洞穴型湖泊
   * @param lake 湖泊
   * @param centerX 中心X坐标
   * @param centerZ 中心Z坐标
   * @param radius 半径
   * @param depthRatio 深度比例
   * @param complexity 复杂度
   * @param random 随机数生成器
   */
  private static generateCavernLake(
    lake: UndergroundLake,
    centerX: number,
    centerZ: number,
    radius: number,
    depthRatio: number,
    complexity: number,
    random: () => number
  ): void {
    // 洞穴型湖泊是一个大的不规则形状，带有多个小洞穴和通道

    // 首先创建不规则形状
    this.generateIrregularLake(lake, centerX, centerZ, radius, depthRatio, complexity, random);

    // 添加一些小洞穴
    const cavernCount = Math.floor(complexity * 5) + 3;
    for (let i = 0; i < cavernCount; i++) {
      const angle = random() * Math.PI * 2;
      const distance = radius * (0.8 + random() * 1.2);

      const cavernX = centerX + Math.cos(angle) * distance;
      const cavernZ = centerZ + Math.sin(angle) * distance;
      const cavernRadius = radius * (0.3 + random() * 0.4);
      const cavernDepthRatio = depthRatio * (0.9 + random() * 0.3);

      // 添加洞穴节点
      lake.nodes.push({
        x: cavernX,
        z: cavernZ,
        radius: cavernRadius,
        depthRatio: cavernDepthRatio,
        type: 'main'
      });

      // 有时创建连接通道
      if (random() < 0.7) {
        this.createConnectionBetweenNodes(
          lake,
          { x: centerX, z: centerZ },
          { x: cavernX, z: cavernZ },
          radius * 0.25,
          depthRatio * 0.8,
          random
        );
      }
    }
  }

  /**
   * 在两个节点之间创建连接
   * @param lake 湖泊
   * @param start 起点
   * @param end 终点
   * @param width 宽度
   * @param depthRatio 深度比例
   * @param random 随机数生成器
   */
  private static createConnectionBetweenNodes(
    lake: UndergroundLake,
    start: { x: number, z: number },
    end: { x: number, z: number },
    width: number,
    depthRatio: number,
    random: () => number
  ): void {
    // 计算方向和距离
    const dx = end.x - start.x;
    const dz = end.z - start.z;
    const distance = Math.sqrt(dx * dx + dz * dz);

    // 确定连接点数量
    const pointCount = Math.floor(distance / (width * 1.5)) + 2;

    // 创建连接点
    for (let i = 1; i < pointCount; i++) {
      const t = i / pointCount;

      // 添加一些随机偏移
      const offsetX = (random() * 2 - 1) * width * 0.8;
      const offsetZ = (random() * 2 - 1) * width * 0.8;

      // 计算连接点位置
      const x = start.x + dx * t + offsetX;
      const z = start.z + dz * t + offsetZ;

      // 添加连接节点
      lake.nodes.push({
        x: x,
        z: z,
        radius: width,
        depthRatio: depthRatio,
        type: 'connection'
      });
    }
  }

  /**
   * 生成钟乳石
   * @param lake 湖泊
   * @param density 密度
   * @param random 随机数生成器
   */
  private static generateStalactites(
    lake: UndergroundLake,
    density: number,
    random: () => number
  ): void {
    // 计算钟乳石数量
    const totalArea = lake.nodes.reduce((sum, node) => sum + Math.PI * node.radius * node.radius, 0);
    const stalactiteCount = Math.floor(totalArea * density * 0.01);

    // 生成钟乳石
    for (let i = 0; i < stalactiteCount; i++) {
      // 随机选择一个节点
      const nodeIndex = Math.floor(random() * lake.nodes.length);
      const node = lake.nodes[nodeIndex];

      // 在节点范围内随机位置
      const angle = random() * Math.PI * 2;
      const distance = random() * node.radius * 0.8;

      const x = node.x + Math.cos(angle) * distance;
      const z = node.z + Math.sin(angle) * distance;

      // 随机高度和半径
      const height = 0.5 + random() * 2.0;
      const radius = 0.1 + random() * 0.3;

      // 随机类型（钟乳石或石笋）
      const type = random() < 0.7 ? 'stalactite' : 'stalagmite';

      // 添加钟乳石
      lake.stalactites.push({
        x: x,
        z: z,
        height: height,
        radius: radius,
        type: type
      });
    }
  }

  /**
   * 生成瀑布
   * @param lake 湖泊
   * @param height 高度
   * @param random 随机数生成器
   */
  private static generateWaterfall(
    lake: UndergroundLake,
    height: number,
    random: () => number
  ): void {
    // 随机选择一个节点作为瀑布起点
    const nodeIndex = Math.floor(random() * lake.nodes.length);
    const node = lake.nodes[nodeIndex];

    // 在节点边缘随机位置
    const angle = random() * Math.PI * 2;
    const startX = node.x + Math.cos(angle) * node.radius * 0.9;
    const startZ = node.z + Math.sin(angle) * node.radius * 0.9;

    // 计算终点（瀑布底部）
    const endX = startX + (random() * 2 - 1) * node.radius * 0.3;
    const endZ = startZ + (random() * 2 - 1) * node.radius * 0.3;

    // 随机宽度和流量
    const width = 0.5 + random() * 1.5;
    const flowRate = 0.5 + random() * 1.5;

    // 添加瀑布
    lake.waterfalls.push({
      startX: startX,
      startZ: startZ,
      endX: endX,
      endZ: endZ,
      height: height,
      width: width,
      flowRate: flowRate
    });
  }

  /**
   * 生成温泉
   * @param lake 湖泊
   * @param temperature 温度
   * @param random 随机数生成器
   */
  private static generateHotSpring(
    lake: UndergroundLake,
    temperature: number,
    random: () => number
  ): void {
    // 随机选择一个节点
    const nodeIndex = Math.floor(random() * lake.nodes.length);
    const node = lake.nodes[nodeIndex];

    // 在节点范围内随机位置
    const angle = random() * Math.PI * 2;
    const distance = random() * node.radius * 0.6;

    const x = node.x + Math.cos(angle) * distance;
    const z = node.z + Math.sin(angle) * distance;

    // 随机半径和气泡率
    const radius = 0.5 + random() * 1.5;
    const bubbleRate = 0.3 + random() * 0.7;

    // 添加温泉
    lake.hotSprings.push({
      x: x,
      z: z,
      radius: radius,
      temperature: temperature,
      bubbleRate: bubbleRate
    });
  }
}
