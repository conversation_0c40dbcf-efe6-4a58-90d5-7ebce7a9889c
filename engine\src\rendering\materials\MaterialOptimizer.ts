/**
 * 材质优化器
 * 用于优化材质性能
 */
import * as THREE from 'three';
import { DeviceCapabilities, DevicePerformanceLevel } from '../../utils/DeviceCapabilities';

/**
 * 材质优化器配置接口
 */
export interface MaterialOptimizerOptions {
  /** 设备能力检测 */
  deviceCapabilities?: DeviceCapabilities;
  /** 是否启用纹理压缩 */
  enableTextureCompression?: boolean;
  /** 是否启用纹理大小限制 */
  enableTextureSizeLimit?: boolean;
  /** 最大纹理大小 */
  maxTextureSize?: number;
  /** 是否启用各向异性过滤 */
  enableAnisotropy?: boolean;
  /** 最大各向异性级别 */
  maxAnisotropy?: number;
  /** 是否启用MIP映射 */
  enableMipmap?: boolean;
  /** 是否启用着色器优化 */
  enableShaderOptimization?: boolean;
}

/**
 * 材质优化器类
 */
export class MaterialOptimizer {
  /** 设备能力检测 */
  private deviceCapabilities: DeviceCapabilities;

  /** 是否启用纹理压缩 */
  private enableTextureCompression: boolean;

  /** 是否启用纹理大小限制 */
  private enableTextureSizeLimit: boolean;

  /** 最大纹理大小 */
  private maxTextureSize: number;

  /** 是否启用各向异性过滤 */
  private enableAnisotropy: boolean;

  /** 最大各向异性级别 */
  private maxAnisotropy: number;

  /** 是否启用MIP映射 */
  private enableMipmap: boolean;

  /** 是否启用着色器优化 */
  private enableShaderOptimization: boolean;

  /**
   * 创建材质优化器
   * @param options 材质优化器配置
   */
  constructor(options: MaterialOptimizerOptions = {}) {
    this.deviceCapabilities = options.deviceCapabilities || DeviceCapabilities.getInstance();
    this.enableTextureCompression = options.enableTextureCompression !== undefined ? options.enableTextureCompression : true;
    this.enableTextureSizeLimit = options.enableTextureSizeLimit !== undefined ? options.enableTextureSizeLimit : true;
    this.maxTextureSize = options.maxTextureSize || 2048;
    this.enableAnisotropy = options.enableAnisotropy !== undefined ? options.enableAnisotropy : true;
    this.maxAnisotropy = options.maxAnisotropy || 4;
    this.enableMipmap = options.enableMipmap !== undefined ? options.enableMipmap : true;
    this.enableShaderOptimization = options.enableShaderOptimization !== undefined ? options.enableShaderOptimization : true;
  }

  /**
   * 优化材质
   * @param material 材质
   * @returns 优化后的材质
   */
  public optimizeMaterial(material: THREE.Material): THREE.Material {
    // 根据设备性能级别优化材质
    const performanceLevel = this.deviceCapabilities.getPerformanceLevel();

    switch (performanceLevel) {
      case DevicePerformanceLevel.LOW:
        return this.optimizeForLowPerformance(material);
      case DevicePerformanceLevel.MEDIUM:
        return this.optimizeForMediumPerformance(material);
      case DevicePerformanceLevel.HIGH:
        return this.optimizeForHighPerformance(material);
      default:
        return material;
    }
  }

  /**
   * 为低性能设备优化材质
   * @param material 材质
   * @returns 优化后的材质
   */
  private optimizeForLowPerformance(material: THREE.Material): THREE.Material {
    // 禁用复杂特性（仅对支持的材质类型）
    if ('fog' in material) {
      (material as any).fog = false;
    }
    if ('lights' in material) {
      (material as any).lights = false;
    }

    // 优化纹理
    this.optimizeTextures(material, 1024, 1);

    // 优化着色器
    if (this.enableShaderOptimization) {
      this.optimizeShader(material, true);
    }

    return material;
  }

  /**
   * 为中等性能设备优化材质
   * @param material 材质
   * @returns 优化后的材质
   */
  private optimizeForMediumPerformance(material: THREE.Material): THREE.Material {
    // 优化纹理
    this.optimizeTextures(material, 2048, 2);

    // 优化着色器
    if (this.enableShaderOptimization) {
      this.optimizeShader(material, false);
    }

    return material;
  }

  /**
   * 为高性能设备优化材质
   * @param material 材质
   * @returns 优化后的材质
   */
  private optimizeForHighPerformance(material: THREE.Material): THREE.Material {
    // 优化纹理
    this.optimizeTextures(material, 4096, 8);

    return material;
  }

  /**
   * 优化纹理
   * @param material 材质
   * @param maxSize 最大纹理大小
   * @param anisotropyLevel 各向异性级别
   */
  private optimizeTextures(material: THREE.Material, maxSize: number, anisotropyLevel: number): void {
    // 获取材质中的所有纹理
    const textures = this.getTexturesFromMaterial(material);

    // 优化每个纹理
    for (const texture of textures) {
      this.optimizeTexture(texture, maxSize, anisotropyLevel);
    }
  }

  /**
   * 优化纹理
   * @param texture 纹理
   * @param maxSize 最大纹理大小
   * @param anisotropyLevel 各向异性级别
   */
  private optimizeTexture(texture: THREE.Texture, maxSize: number, anisotropyLevel: number): void {
    if (!texture) return;

    // 限制纹理大小
    if (this.enableTextureSizeLimit) {
      const size = Math.min(this.maxTextureSize, maxSize);
      if (texture.image && (texture.image.width > size || texture.image.height > size)) {
        // 在实际应用中，这里应该调整纹理大小
        // 但在Three.js中，我们通常在加载纹理前就处理好大小
        console.warn(`纹理大小超过限制: ${texture.image.width}x${texture.image.height}, 最大允许: ${size}x${size}`);
      }
    }

    // 设置各向异性过滤
    if (this.enableAnisotropy && this.deviceCapabilities.isAnisotropySupported()) {
      texture.anisotropy = Math.min(this.deviceCapabilities.getMaxAnisotropy(), this.maxAnisotropy, anisotropyLevel);
    } else {
      texture.anisotropy = 1;
    }

    // 设置MIP映射
    if (this.enableMipmap) {
      texture.generateMipmaps = true;
      texture.minFilter = THREE.LinearMipmapLinearFilter;
    } else {
      texture.generateMipmaps = false;
      texture.minFilter = THREE.LinearFilter;
    }

    // 设置纹理包装模式
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;

    // 标记纹理需要更新
    texture.needsUpdate = true;
  }

  /**
   * 优化着色器
   * @param material 材质
   * @param simplify 是否简化
   */
  private optimizeShader(material: THREE.Material, simplify: boolean): void {
    // 在实际应用中，这里应该修改着色器代码
    // 但在Three.js中，我们通常使用内置材质，很少直接修改着色器
    if (material instanceof THREE.ShaderMaterial || material instanceof THREE.RawShaderMaterial) {
      // 为着色器材质添加优化标记
      material.defines = material.defines || {};
      material.defines.USE_OPTIMIZED_SHADER = 1;

      if (simplify) {
        material.defines.USE_SIMPLIFIED_SHADER = 1;
      }

      // 标记材质需要更新
      material.needsUpdate = true;
    }
  }

  /**
   * 从材质中获取所有纹理
   * @param material 材质
   * @returns 纹理数组
   */
  private getTexturesFromMaterial(material: THREE.Material): THREE.Texture[] {
    const textures: THREE.Texture[] = [];

    // 检查常见的纹理属性
    const textureProperties = [
      'map', 'alphaMap', 'aoMap', 'bumpMap', 'displacementMap',
      'emissiveMap', 'envMap', 'lightMap', 'metalnessMap',
      'normalMap', 'roughnessMap', 'specularMap', 'gradientMap',
      'matcap', 'clearcoatMap', 'clearcoatNormalMap', 'clearcoatRoughnessMap',
      'sheenColorMap', 'sheenRoughnessMap', 'transmissionMap', 'thicknessMap'
    ];

    // 遍历所有可能的纹理属性
    for (const prop of textureProperties) {
      if (prop in material && (material as any)[prop] instanceof THREE.Texture) {
        textures.push((material as any)[prop]);
      }
    }

    return textures;
  }

  /**
   * 设置是否启用纹理压缩
   * @param enabled 是否启用
   */
  public setEnableTextureCompression(enabled: boolean): void {
    this.enableTextureCompression = enabled;
  }

  /**
   * 设置是否启用纹理大小限制
   * @param enabled 是否启用
   */
  public setEnableTextureSizeLimit(enabled: boolean): void {
    this.enableTextureSizeLimit = enabled;
  }

  /**
   * 设置最大纹理大小
   * @param size 最大纹理大小
   */
  public setMaxTextureSize(size: number): void {
    this.maxTextureSize = size;
  }

  /**
   * 设置是否启用各向异性过滤
   * @param enabled 是否启用
   */
  public setEnableAnisotropy(enabled: boolean): void {
    this.enableAnisotropy = enabled;
  }

  /**
   * 设置最大各向异性级别
   * @param level 最大各向异性级别
   */
  public setMaxAnisotropy(level: number): void {
    this.maxAnisotropy = level;
  }

  /**
   * 设置是否启用MIP映射
   * @param enabled 是否启用
   */
  public setEnableMipmap(enabled: boolean): void {
    this.enableMipmap = enabled;
  }

  /**
   * 设置是否启用着色器优化
   * @param enabled 是否启用
   */
  public setEnableShaderOptimization(enabled: boolean): void {
    this.enableShaderOptimization = enabled;
  }
}
