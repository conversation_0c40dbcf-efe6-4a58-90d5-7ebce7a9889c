/**
 * 优化的地形片段着色器
 * 用于提高地形渲染性能
 */
export declare const optimizedTerrainFragmentShader = "\n// \u7CBE\u5EA6\nprecision highp float;\nprecision highp int;\n\n// \u8F93\u5165\u53D8\u91CF\nvarying vec2 vUv;\nvarying vec3 vNormal;\nvarying vec4 vTangent;\nvarying vec3 vWorldPosition;\nvarying vec3 vPosition;\nvarying float vHeight;\nvarying float vSlope;\nvarying float vFogFactor;\nvarying float vLODBlendFactor;\n\n// \u5730\u5F62\u53C2\u6570\nuniform vec3 uAmbientColor;\nuniform vec3 uLightColor;\nuniform vec3 uLightPosition;\nuniform vec3 uFogColor;\nuniform bool uUseFog;\nuniform bool uUseBlendMaps;\nuniform int uLayerCount;\nuniform float uMaxHeight;\n\n// \u7EB9\u7406\u5C42\u53C2\u6570\nuniform sampler2D uTextures[8];\nuniform sampler2D uNormalMaps[8];\nuniform sampler2D uRoughnessMaps[8];\nuniform sampler2D uAOMaps[8];\nuniform sampler2D uBlendMaps[8];\nuniform float uTilingFactors[8];\nuniform float uMinHeights[8];\nuniform float uMaxHeights[8];\nuniform float uMinSlopes[8];\nuniform float uMaxSlopes[8];\n\n// LOD\u53C2\u6570\nuniform sampler2D uLowResTexture;\nuniform bool uUseLOD;\n\n// \u8BA1\u7B97\u6DF7\u5408\u6743\u91CD\nfloat calculateBlendWeight(float height, float slope, float minHeight, float maxHeight, float minSlope, float maxSlope) {\n  // \u8BA1\u7B97\u9AD8\u5EA6\u6743\u91CD\n  float heightWeight = smoothstep(minHeight, maxHeight, height);\n  if (height > maxHeight) {\n    heightWeight = 1.0 - smoothstep(maxHeight, maxHeight + 0.1, height);\n  }\n  \n  // \u8BA1\u7B97\u659C\u5EA6\u6743\u91CD\n  float slopeWeight = smoothstep(minSlope, maxSlope, slope);\n  if (slope > maxSlope) {\n    slopeWeight = 1.0 - smoothstep(maxSlope, maxSlope + 0.1, slope);\n  }\n  \n  // \u7EC4\u5408\u6743\u91CD\n  return heightWeight * slopeWeight;\n}\n\n// \u4ECE\u6CD5\u7EBF\u8D34\u56FE\u8BA1\u7B97\u6CD5\u7EBF\nvec3 calculateNormalFromMap(sampler2D normalMap, vec2 uv, vec3 normal, vec4 tangent) {\n  // \u83B7\u53D6\u6CD5\u7EBF\u8D34\u56FE\u503C\n  vec3 normalMapValue = texture2D(normalMap, uv).xyz * 2.0 - 1.0;\n  \n  // \u8BA1\u7B97\u5207\u7EBF\u7A7A\u95F4\u5230\u4E16\u754C\u7A7A\u95F4\u7684\u8F6C\u6362\n  vec3 tangentNormal = normalMapValue;\n  vec3 T = normalize(tangent.xyz);\n  vec3 B = normalize(cross(normal, T) * tangent.w);\n  mat3 TBN = mat3(T, B, normal);\n  \n  // \u8F6C\u6362\u6CD5\u7EBF\u5230\u4E16\u754C\u7A7A\u95F4\n  return normalize(TBN * tangentNormal);\n}\n\n// \u4F18\u5316\u7684\u7EB9\u7406\u91C7\u6837\nvec4 optimizedTextureSample(sampler2D tex, vec2 uv, float lodBias) {\n  #ifdef GL_EXT_shader_texture_lod\n    return texture2DLodEXT(tex, uv, lodBias);\n  #else\n    return texture2D(tex, uv, lodBias);\n  #endif\n}\n\nvoid main() {\n  // \u521D\u59CB\u5316\u989C\u8272\u548C\u6743\u91CD\n  vec4 finalColor = vec4(0.0);\n  vec3 finalNormal = normalize(vNormal);\n  float totalWeight = 0.0;\n  \n  // \u8BA1\u7B97LOD\u504F\u79FB\n  float lodBias = vLODBlendFactor * 4.0;\n  \n  // \u5982\u679C\u4F7F\u7528LOD\u5E76\u4E14LOD\u6DF7\u5408\u56E0\u5B50\u5927\u4E8E0.99\uFF0C\u76F4\u63A5\u4F7F\u7528\u4F4E\u5206\u8FA8\u7387\u7EB9\u7406\n  if (uUseLOD && vLODBlendFactor > 0.99) {\n    finalColor = texture2D(uLowResTexture, vUv);\n  } else {\n    // \u8BA1\u7B97\u6BCF\u5C42\u7684\u6DF7\u5408\u6743\u91CD\u548C\u989C\u8272\n    for (int i = 0; i < 8; i++) {\n      if (i >= uLayerCount) break;\n      \n      // \u8BA1\u7B97\u7EB9\u7406\u5750\u6807\n      vec2 tiledUv = vUv * uTilingFactors[i];\n      \n      // \u83B7\u53D6\u6DF7\u5408\u6743\u91CD\n      float weight = 0.0;\n      if (uUseBlendMaps) {\n        // \u4F7F\u7528\u6DF7\u5408\u8D34\u56FE\n        weight = texture2D(uBlendMaps[i], vUv).r;\n      } else {\n        // \u4F7F\u7528\u9AD8\u5EA6\u548C\u659C\u5EA6\u8BA1\u7B97\u6DF7\u5408\u6743\u91CD\n        weight = calculateBlendWeight(vHeight, vSlope, uMinHeights[i], uMaxHeights[i], uMinSlopes[i], uMaxSlopes[i]);\n      }\n      \n      if (weight > 0.0) {\n        // \u83B7\u53D6\u7EB9\u7406\u989C\u8272\uFF08\u4F7F\u7528LOD\u504F\u79FB\uFF09\n        vec4 textureColor = optimizedTextureSample(uTextures[i], tiledUv, lodBias);\n        \n        // \u83B7\u53D6\u6CD5\u7EBF\u8D34\u56FE\n        vec3 normalMapValue = calculateNormalFromMap(uNormalMaps[i], tiledUv, vNormal, vTangent);\n        \n        // \u83B7\u53D6\u7C97\u7CD9\u5EA6\u8D34\u56FE\n        float roughness = texture2D(uRoughnessMaps[i], tiledUv).r;\n        \n        // \u83B7\u53D6\u73AF\u5883\u5149\u906E\u853D\u8D34\u56FE\n        float ao = texture2D(uAOMaps[i], tiledUv).r;\n        \n        // \u7B80\u5355\u7684\u5149\u7167\u8BA1\u7B97\n        vec3 lightDir = normalize(uLightPosition - vWorldPosition);\n        float diffuse = max(dot(normalMapValue, lightDir), 0.0);\n        vec3 ambient = uAmbientColor * ao;\n        vec3 lighting = ambient + uLightColor * diffuse;\n        \n        // \u6DF7\u5408\u989C\u8272\n        finalColor += vec4(textureColor.rgb * lighting, textureColor.a) * weight;\n        \n        // \u6DF7\u5408\u6CD5\u7EBF\n        finalNormal = normalize(mix(finalNormal, normalMapValue, weight));\n        \n        totalWeight += weight;\n      }\n    }\n  }\n  \n  // \u6807\u51C6\u5316\u989C\u8272\n  if (totalWeight > 0.0) {\n    finalColor /= totalWeight;\n  }\n  \n  // \u5E94\u7528\u96FE\u6548\u679C\n  if (uUseFog) {\n    finalColor.rgb = mix(finalColor.rgb, uFogColor, vFogFactor);\n  }\n  \n  // \u8F93\u51FA\u6700\u7EC8\u989C\u8272\n  gl_FragColor = finalColor;\n}\n";
