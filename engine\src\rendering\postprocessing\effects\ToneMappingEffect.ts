/**
 * 色调映射效果
 * 提升HDR渲染的视觉效果
 */
import * as THREE from 'three';
// 使用类型断言导入 ShaderPass
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 色调映射类型
 */
export enum ToneMappingType {
  /** 线性 */
  Linear = 'Linear',
  /** Reinhard */
  Reinhard = 'Reinhard',
  /** Cineon */
  Cineon = 'Cineon',
  /** ACESFilmic */
  ACESFilmic = 'ACESFilmic',
  /** 自定义 */
  Custom = 'Custom'
}

/**
 * 色调映射效果选项
 */
export interface ToneMappingEffectOptions extends PostProcessingEffectOptions {
  /** 色调映射类型 */
  type?: ToneMappingType;
  /** 曝光 */
  exposure?: number;
  /** 伽马 */
  gamma?: number;
  /** 对比度 */
  contrast?: number;
  /** 饱和度 */
  saturation?: number;
  /** 亮度 */
  brightness?: number;
}

/**
 * 色调映射效果
 */
export class ToneMappingEffect extends PostProcessingEffect {
  /** 色调映射类型 */
  private type: ToneMappingType;

  /** 曝光 */
  private exposure: number;

  /** 伽马 */
  private gamma: number;

  /** 对比度 */
  private contrast: number;

  /** 饱和度 */
  private saturation: number;

  /** 亮度 */
  private brightness: number;

  /** 色调映射通道 */
  private toneMappingPass: ShaderPass | null = null;

  /**
   * 创建色调映射效果
   * @param options 色调映射效果选项
   */
  constructor(options: ToneMappingEffectOptions = { name: 'ToneMapping' }) {
    super(options);

    this.type = options.type || ToneMappingType.ACESFilmic;
    this.exposure = options.exposure !== undefined ? options.exposure : 1.0;
    this.gamma = options.gamma !== undefined ? options.gamma : 2.2;
    this.contrast = options.contrast !== undefined ? options.contrast : 1.0;
    this.saturation = options.saturation !== undefined ? options.saturation : 1.0;
    this.brightness = options.brightness !== undefined ? options.brightness : 1.0;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 创建色调映射着色器
    const toneMappingShader = {
      uniforms: {
        'tDiffuse': { value: null },
        'type': { value: this.getToneMappingTypeValue() },
        'exposure': { value: this.exposure },
        'gamma': { value: this.gamma },
        'contrast': { value: this.contrast },
        'saturation': { value: this.saturation },
        'brightness': { value: this.brightness }
      },
      vertexShader: /* glsl */`
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: /* glsl */`
        uniform sampler2D tDiffuse;
        uniform int type;
        uniform float exposure;
        uniform float gamma;
        uniform float contrast;
        uniform float saturation;
        uniform float brightness;
        
        varying vec2 vUv;
        
        // 线性色调映射
        vec3 linearToneMapping(vec3 color) {
          return color * exposure;
        }
        
        // Reinhard色调映射
        vec3 reinhardToneMapping(vec3 color) {
          color *= exposure;
          return color / (1.0 + color);
        }
        
        // Cineon色调映射
        vec3 cineonToneMapping(vec3 color) {
          color *= exposure;
          color = max(vec3(0.0), color - 0.004);
          return pow((color * (6.2 * color + 0.5)) / (color * (6.2 * color + 1.7) + 0.06), vec3(2.2));
        }
        
        // ACES Filmic色调映射
        vec3 acesFilmicToneMapping(vec3 color) {
          color *= exposure;
          const float a = 2.51;
          const float b = 0.03;
          const float c = 2.43;
          const float d = 0.59;
          const float e = 0.14;
          return clamp((color * (a * color + b)) / (color * (c * color + d) + e), 0.0, 1.0);
        }
        
        // 自定义色调映射
        vec3 customToneMapping(vec3 color) {
          // 应用曝光
          color *= exposure;
          
          // 应用对比度
          color = (color - 0.5) * contrast + 0.5;
          
          // 应用亮度
          color *= brightness;
          
          // 应用饱和度
          float luminance = dot(color, vec3(0.2126, 0.7152, 0.0722));
          color = mix(vec3(luminance), color, saturation);
          
          return color;
        }
        
        // 应用伽马校正
        vec3 gammaCorrection(vec3 color, float gamma) {
          return pow(color, vec3(1.0 / gamma));
        }
        
        void main() {
          vec4 texel = texture2D(tDiffuse, vUv);
          vec3 color = texel.rgb;
          
          // 应用色调映射
          if (type == 0) {
            color = linearToneMapping(color);
          } else if (type == 1) {
            color = reinhardToneMapping(color);
          } else if (type == 2) {
            color = cineonToneMapping(color);
          } else if (type == 3) {
            color = acesFilmicToneMapping(color);
          } else if (type == 4) {
            color = customToneMapping(color);
          }
          
          // 应用伽马校正
          color = gammaCorrection(color, gamma);
          
          gl_FragColor = vec4(color, texel.a);
        }
      `
    };

    // 创建色调映射通道
    this.toneMappingPass = new ShaderPass(toneMappingShader);

    // 设置通道
    this.pass = this.toneMappingPass;
  }

  /**
   * 获取色调映射类型值
   * @returns 色调映射类型值
   */
  private getToneMappingTypeValue(): number {
    switch (this.type) {
      case ToneMappingType.Linear:
        return 0;
      case ToneMappingType.Reinhard:
        return 1;
      case ToneMappingType.Cineon:
        return 2;
      case ToneMappingType.ACESFilmic:
        return 3;
      case ToneMappingType.Custom:
        return 4;
      default:
        return 3; // 默认使用ACESFilmic
    }
  }

  /**
   * 设置色调映射类型
   * @param type 色调映射类型
   */
  public setType(type: ToneMappingType): void {
    this.type = type;

    // 更新通道的类型
    if (this.toneMappingPass && this.toneMappingPass.uniforms.type) {
      this.toneMappingPass.uniforms.type.value = this.getToneMappingTypeValue();
    }
  }

  /**
   * 获取色调映射类型
   * @returns 色调映射类型
   */
  public getType(): ToneMappingType {
    return this.type;
  }

  /**
   * 设置曝光
   * @param exposure 曝光
   */
  public setExposure(exposure: number): void {
    this.exposure = exposure;

    // 更新通道的曝光
    if (this.toneMappingPass && this.toneMappingPass.uniforms.exposure) {
      this.toneMappingPass.uniforms.exposure.value = exposure;
    }
  }

  /**
   * 获取曝光
   * @returns 曝光
   */
  public getExposure(): number {
    return this.exposure;
  }

  /**
   * 设置伽马
   * @param gamma 伽马
   */
  public setGamma(gamma: number): void {
    this.gamma = gamma;

    // 更新通道的伽马
    if (this.toneMappingPass && this.toneMappingPass.uniforms.gamma) {
      this.toneMappingPass.uniforms.gamma.value = gamma;
    }
  }

  /**
   * 获取伽马
   * @returns 伽马
   */
  public getGamma(): number {
    return this.gamma;
  }

  /**
   * 设置对比度
   * @param contrast 对比度
   */
  public setContrast(contrast: number): void {
    this.contrast = contrast;

    // 更新通道的对比度
    if (this.toneMappingPass && this.toneMappingPass.uniforms.contrast) {
      this.toneMappingPass.uniforms.contrast.value = contrast;
    }
  }

  /**
   * 获取对比度
   * @returns 对比度
   */
  public getContrast(): number {
    return this.contrast;
  }

  /**
   * 设置饱和度
   * @param saturation 饱和度
   */
  public setSaturation(saturation: number): void {
    this.saturation = saturation;

    // 更新通道的饱和度
    if (this.toneMappingPass && this.toneMappingPass.uniforms.saturation) {
      this.toneMappingPass.uniforms.saturation.value = saturation;
    }
  }

  /**
   * 获取饱和度
   * @returns 饱和度
   */
  public getSaturation(): number {
    return this.saturation;
  }

  /**
   * 设置亮度
   * @param brightness 亮度
   */
  public setBrightness(brightness: number): void {
    this.brightness = brightness;

    // 更新通道的亮度
    if (this.toneMappingPass && this.toneMappingPass.uniforms.brightness) {
      this.toneMappingPass.uniforms.brightness.value = brightness;
    }
  }

  /**
   * 获取亮度
   * @returns 亮度
   */
  public getBrightness(): number {
    return this.brightness;
  }

  /**
   * 更新效果
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 色调映射效果不需要每帧更新
  }
}
