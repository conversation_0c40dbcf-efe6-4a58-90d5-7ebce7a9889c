/**
 * 面板适配器组件
 * 用于将现有面板组件适配为rc-dock面板
 */
import React, { ReactNode } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import {
  AppstoreOutlined,
  BarsOutlined,
  SettingOutlined,
  FolderOutlined,
  CodeOutlined,
  PlaySquareOutlined,
  ThunderboltOutlined,
  ApartmentOutlined,
  InteractionOutlined,
  UserOutlined,
  VideoCameraOutlined
} from '@ant-design/icons';
import PanelContainer from './PanelContainer';
import { PanelType } from '../../store/ui/uiSlice';

// 面板图标映射
const panelIconMap: Record<string, ReactNode> = {
  [PanelType.HIERARCHY]: <BarsOutlined />,
  [PanelType.INSPECTOR]: <SettingOutlined />,
  [PanelType.ASSETS]: <FolderOutlined />,
  [PanelType.SCENE]: <AppstoreOutlined />,
  [PanelType.CONSOLE]: <CodeOutlined />,
  [PanelType.ANIMATION]: <PlaySquareOutlined />,
  [PanelType.PHYSICS]: <ThunderboltOutlined />,
  [PanelType.PARTICLE]: <ApartmentOutlined />,
  'interaction': <InteractionOutlined />,
  'avatar': <UserOutlined />,
  'mocap': <VideoCameraOutlined />
};

interface PanelAdapterProps {
  panelType: string;
  children: ReactNode;
  onClose?: () => void;
  onMaximize?: () => void;
  onRestore?: () => void;
  onPin?: () => void;
  onUnpin?: () => void;
  onHide?: () => void;
  isMaximized?: boolean;
  isPinned?: boolean;
  isVisible?: boolean;
}

/**
 * 面板适配器组件
 */
const PanelAdapter: React.FC<PanelAdapterProps> = ({
  panelType,
  children,
  onClose,
  onMaximize,
  onRestore,
  onPin,
  onUnpin,
  onHide,
  isMaximized = false,
  isPinned = false,
  isVisible = true
}) => {
  const { t } = useTranslation();
  
  // 获取面板标题
  const getPanelTitle = (type: string): string => {
    switch (type) {
      case PanelType.HIERARCHY:
        return t('editor.hierarchyView');
      case PanelType.INSPECTOR:
        return t('editor.inspectorView');
      case PanelType.ASSETS:
        return t('editor.assetView');
      case PanelType.SCENE:
        return t('editor.sceneView');
      case PanelType.CONSOLE:
        return t('editor.consoleView');
      case PanelType.ANIMATION:
        return t('editor.animationView');
      case PanelType.PHYSICS:
        return t('editor.physicsView');
      case PanelType.PARTICLE:
        return t('editor.particleView');
      case 'interaction':
        return t('editor.interactionView');
      case 'avatar':
        return t('editor.avatarView');
      case 'mocap':
        return t('editor.mocapView');
      default:
        return t('editor.unknownView');
    }
  };
  
  // 获取面板图标
  const getPanelIcon = (type: string): ReactNode => {
    return panelIconMap[type] || <AppstoreOutlined />;
  };
  
  return (
    <PanelContainer
      title={getPanelTitle(panelType)}
      icon={getPanelIcon(panelType)}
      onClose={onClose}
      onMaximize={onMaximize}
      onRestore={onRestore}
      onPin={onPin}
      onUnpin={onUnpin}
      onHide={onHide}
      isMaximized={isMaximized}
      isPinned={isPinned}
      isVisible={isVisible}
    >
      {children}
    </PanelContainer>
  );
};

/**
 * 创建面板标签数据
 * @param id 面板ID
 * @param type 面板类型
 * @param content 面板内容组件
 * @param closable 是否可关闭
 * @returns 面板标签数据
 */
export const createPanelTab = (
  id: string,
  type: string,
  content: React.ReactElement,
  closable: boolean = true
): TabData => {
  // 注意：这个函数不能在组件外部使用useTranslation，需要在组件内部调用
  return {
    id,
    title: `${type}View`, // 临时使用简单字符串，实际应该在组件内部使用t函数
    content,
    closable,
    group: type
  };
};

export default PanelAdapter;
