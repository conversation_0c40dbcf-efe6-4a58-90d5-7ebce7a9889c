import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { VegetationComponent } from './components/VegetationComponent';
/**
 * 植被系统事件类型
 */
export declare enum VegetationSystemEventType {
    VEGETATION_GENERATED = "vegetation_generated",
    VEGETATION_UPDATED = "vegetation_updated",
    VEGETATION_CLEARED = "vegetation_cleared",
    INSTANCE_ADDED = "instance_added",
    INSTANCE_REMOVED = "instance_removed",
    INSTANCE_UPDATED = "instance_updated"
}
/**
 * 植被系统选项接口
 */
export interface VegetationSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否使用实例化渲染 */
    useInstancing?: boolean;
    /** 是否使用LOD */
    useLOD?: boolean;
    /** 是否使用视锥体剔除 */
    useFrustumCulling?: boolean;
    /** 是否使用八叉树 */
    useOctree?: boolean;
    /** 是否使用GPU实例化 */
    useGPUInstancing?: boolean;
    /** 是否使用阴影 */
    useShadow?: boolean;
    /** 是否使用风效果 */
    useWind?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 植被系统类
 */
export declare class VegetationSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 是否启用植被系统 */
    private vegetationEnabled;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 帧计数器 */
    private frameCount;
    /** 是否使用实例化渲染 */
    private useInstancing;
    /** 是否使用LOD */
    private useLOD;
    /** 是否使用视锥体剔除 */
    private useFrustumCulling;
    /** 是否使用八叉树 */
    private useOctree;
    /** 是否使用GPU实例化 */
    private useGPUInstancing;
    /** 是否使用阴影 */
    private useShadow;
    /** 是否使用风效果 */
    private useWind;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 是否使用季节效果 */
    private useSeasonal;
    /** 植被组件映射 */
    private vegetationEntities;
    /** 地形组件映射 */
    private terrainEntities;
    /** 模型缓存 */
    private modelCache;
    /** 实例化渲染系统 */
    private instancedRenderingSystem;
    /** 物理风效果系统 */
    private physicalWindSystem;
    /** 风场系统 */
    private windFieldSystem;
    /** 八叉树 */
    private octree;
    /** 视锥体 */
    private frustum;
    /** 模型加载器 */
    private modelLoader;
    /** 事件发射器 */
    private eventEmitter;
    /** 调试网格列表 */
    private debugMeshes;
    /** 临时矩阵 */
    private tempMatrix;
    /**
     * 创建植被系统
     * @param options 选项
     */
    constructor(options?: VegetationSystemOptions);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 实体添加事件处理
     * @param entity 实体
     */
    private onEntityAdded;
    /**
     * 实体移除事件处理
     * @param entity 实体
     */
    private onEntityRemoved;
    /**
     * 添加植被实体
     * @param entity 实体
     * @param component 植被组件
     */
    addVegetationEntity(entity: Entity, component: VegetationComponent): void;
    /**
     * 移除植被实体
     * @param entity 实体
     */
    removeVegetationEntity(entity: Entity): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 更新视锥体
     * @param camera 相机
     */
    private updateFrustum;
    /**
     * 生成植被
     * @param entity 实体
     * @param component 植被组件
     */
    generateVegetation(entity: Entity, component: VegetationComponent): void;
    /**
     * 生成植被项
     * @param entity 实体
     * @param component 植被组件
     * @param terrainComponent 地形组件
     * @param itemIndex 项索引
     * @param item 植被项
     * @param random 随机数生成器
     */
    private generateVegetationItem;
    /**
     * 创建植被实例
     * @param entity 实体
     * @param component 植被组件
     * @param itemIndex 项索引
     * @param model 模型
     * @param position 位置
     * @param rotation 旋转
     * @param scale 缩放
     * @param color 颜色
     * @returns 实例ID
     */
    private createVegetationInstance;
    /**
     * 更新植被
     * @param entity 实体
     * @param component 植被组件
     * @param camera 相机
     */
    private updateVegetation;
    /**
     * 清除植被
     * @param entity 实体
     * @param component 植被组件
     */
    clearVegetation(entity: Entity, component: VegetationComponent): void;
    /**
     * 更新LOD级别
     * @param entity 实体
     * @param component 植被组件
     * @param camera 相机
     */
    private updateLOD;
    /**
     * 更新风效果
     * @param entity 实体
     * @param component 植被组件
     */
    private updateWindEffect;
    /**
     * 更新简单的风效果（不使用物理风效果系统时）
     * @param entity 实体
     * @param component 植被组件
     */
    private updateSimpleWindEffect;
    /**
     * 更新季节效果
     * @param entity 实体
     * @param component 植被组件
     */
    private updateSeasonalEffect;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 获取活跃场景
     * @returns 场景
     */
    private getActiveScene;
    /**
     * 加载模型
     * @param url 模型URL
     * @returns 模型
     */
    private loadModel;
    /**
     * 查找第一个网格
     * @param object 对象
     * @returns 网格
     */
    private findFirstMesh;
    /**
     * 设置模型颜色
     * @param model 模型
     * @param color 颜色
     */
    private setModelColor;
    /**
     * 获取地形法线
     * @param terrainComponent 地形组件
     * @param x X坐标
     * @param z Z坐标
     * @returns 法线
     */
    private getTerrainNormal;
    /**
     * 计算坡度
     * @param normal 法线
     * @returns 坡度（度）
     */
    private calculateSlope;
    /**
     * 采样纹理
     * @param texture 纹理
     * @param u U坐标
     * @param v V坐标
     * @returns 采样值
     */
    private sampleTexture;
    /**
     * 创建随机数生成器
     * @param seed 种子
     * @returns 随机数生成器
     */
    private createRandomGenerator;
}
