import { OcclusionCullingSystem, OcclusionCullingAlgorithm, OcclusionCullingSystemOptions } from './OcclusionCullingSystem';
import { EventCallback } from '../../utils/EventEmitter';
/**
 * 增强的遮挡剔除系统事件类型
 */
export declare enum EnhancedOcclusionCullingSystemEventType {
    /** 实体被剔除 */
    ENTITY_CULLED = "entity_culled",
    /** 实体被恢复 */
    ENTITY_RESTORED = "entity_restored",
    /** 组件添加 */
    COMPONENT_ADDED = "component_added",
    /** 组件移除 */
    COMPONENT_REMOVED = "component_removed",
    /** 算法变更 */
    ALGORITHM_CHANGED = "algorithm_changed",
    /** 性能统计更新 */
    STATS_UPDATED = "stats_updated"
}
/**
 * 增强的遮挡剔除系统配置接口
 */
export interface EnhancedOcclusionCullingSystemOptions extends OcclusionCullingSystemOptions {
    /** 是否使用自适应算法选择 */
    useAdaptiveAlgorithm?: boolean;
    /** 是否使用多级遮挡剔除 */
    useMultiLevelCulling?: boolean;
    /** 是否使用预测剔除 */
    usePredictiveCulling?: boolean;
    /** 是否使用时间一致性 */
    useTemporalCoherence?: boolean;
    /** 是否使用GPU加速 */
    useGPUAcceleration?: boolean;
    /** 是否使用保守剔除 */
    useConservativeCulling?: boolean;
    /** 是否收集性能统计 */
    collectStats?: boolean;
    /** 是否使用自动优化 */
    useAutoOptimization?: boolean;
    /** 自动优化间隔（毫秒） */
    autoOptimizationInterval?: number;
}
/**
 * 遮挡剔除性能统计
 */
export interface OcclusionCullingStats {
    /** 总对象数量 */
    totalObjects: number;
    /** 剔除对象数量 */
    culledObjects: number;
    /** 剔除率 */
    cullingRate: number;
    /** 剔除时间（毫秒） */
    cullingTime: number;
    /** 渲染时间（毫秒） */
    renderTime: number;
    /** 总时间（毫秒） */
    totalTime: number;
    /** 算法类型 */
    algorithm: OcclusionCullingAlgorithm;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 增强的遮挡剔除系统事件类型
 */
export declare enum EnhancedOcclusionCullingSystemEventType {
    /** 算法变更事件 */
    ENHANCED_ALGORITHM_CHANGED = "enhancedAlgorithmChanged",
    /** 性能优化事件 */
    PERFORMANCE_OPTIMIZED = "performanceOptimized",
    /** 统计更新事件 */
    ENHANCED_STATS_UPDATED = "enhancedStatsUpdated"
}
/**
 * 增强的遮挡剔除系统
 * 提供高效的遮挡剔除算法，减少不可见对象的渲染
 */
export declare class EnhancedOcclusionCullingSystem extends OcclusionCullingSystem {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 是否使用自适应算法选择 */
    private useAdaptiveAlgorithm;
    /** 是否使用多级遮挡剔除 */
    private useMultiLevelCulling;
    /** 是否使用预测剔除 */
    private usePredictiveCulling;
    /** 是否使用时间一致性（重写基类属性） */
    protected useTemporalCoherence: boolean;
    /** 是否使用GPU加速 */
    private useGPUAcceleration;
    /** 是否使用保守剔除 */
    private useConservativeCulling;
    /** 是否收集性能统计 */
    private collectStats;
    /** 是否使用自动优化 */
    private useAutoOptimization;
    /** 自动优化间隔（毫秒） */
    private autoOptimizationInterval;
    /** 上次自动优化时间 */
    private lastAutoOptimizationTime;
    /** 性能统计历史 */
    private statsHistory;
    /** 最大历史记录数 */
    private maxHistoryLength;
    /** 上一帧剔除的实体 */
    private previousCulledEntities;
    /** 预测的相机位置 */
    private predictedCameraPosition;
    /** 相机速度 */
    private cameraVelocity;
    /** 上一帧相机位置 */
    private previousCameraPosition;
    /** 事件发射器 */
    private eventEmitter;
    /** 增强的层次Z缓冲区 */
    private enhancedHierarchicalZBuffer;
    /** 增强层次Z缓冲区宽度 */
    private enhancedHzbWidth;
    /** 增强层次Z缓冲区高度 */
    private enhancedHzbHeight;
    /** 增强层次Z缓冲区级别数 */
    private enhancedHzbLevels;
    /** 剔除时间 */
    private cullingTime;
    /** 剔除的实体集合 */
    private culledEntities;
    /** 可剔除组件映射 */
    private cullableComponents;
    /**
     * 创建增强的遮挡剔除系统
     * @param options 系统选项
     * @example
     * ```typescript
     * const cullingSystem = new EnhancedOcclusionCullingSystem({
     *   useAdaptiveAlgorithm: true,
     *   useMultiLevelCulling: true,
     *   collectStats: true
     * });
     * ```
     */
    constructor(options?: EnhancedOcclusionCullingSystemOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 获取活动相机
     * @returns 活动相机
     */
    private getActiveCamera;
    /**
     * 获取活动场景
     * @returns 活动场景
     */
    private getActiveScene;
    /**
     * 执行主要遮挡剔除
     * @param camera 相机
     * @param scene 场景
     */
    private performMainOcclusionCulling;
    /**
     * 执行基本遮挡剔除
     * @param camera 相机
     * @param _scene 场景（暂未使用）
     */
    private performBasicOcclusionCulling;
    /**
     * 检查是否有调试可视化
     * @returns 是否有调试可视化
     */
    private hasDebugVisualization;
    /**
     * 安全地更新调试可视化
     */
    private updateDebugVisualizationSafe;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新相机信息
     * @param camera 相机
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateCameraInfo;
    /**
     * 检查GPU支持
     */
    private checkGPUSupport;
    /**
     * 初始化增强层次Z缓冲区
     */
    private initializeEnhancedHierarchicalZBuffer;
    /**
     * 安全地获取渲染器
     * @returns 渲染器或null
     */
    private getRendererSafe;
    /**
     * 计算层次Z缓冲区总大小
     * @param width 宽度
     * @param height 高度
     * @param levels 级别数
     * @returns 总大小
     */
    private calculateTotalHZBSize;
    /**
     * 选择最佳算法
     */
    private selectBestAlgorithm;
    /**
     * 优化设置
     */
    private optimizeSettings;
    /**
     * 执行粗略剔除
     * @param camera 相机
     * @param _scene 场景（暂未使用）
     */
    private performCoarseCulling;
    /**
     * 安全地剔除实体
     * @param entity 实体
     */
    private cullEntitySafe;
    /**
     * 执行预测剔除
     * @param camera 相机
     * @param scene 场景
     * @param deltaTime 帧间隔时间（秒）
     */
    private performPredictiveCulling;
    /**
     * 更新统计信息
     * @param cullingTime 剔除时间（毫秒）
     */
    private updateStats;
    /**
     * 获取统计历史
     * @returns 统计历史
     */
    getStatsHistory(): OcclusionCullingStats[];
    /**
     * 获取最新统计
     * @returns 最新统计
     */
    getLatestStats(): OcclusionCullingStats | null;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: string, listener: EventCallback): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: string, listener?: EventCallback): void;
    /**
     * 获取当前剔除性能信息
     * @returns 性能信息
     */
    getCurrentPerformance(): {
        cullingTime: number;
        culledCount: number;
        totalCount: number;
    };
}
