/**
 * GPU加速的面部动画系统
 * 使用GPU加速面部动画计算，提高性能
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import { FacialExpressionType } from './FacialExpressionType';
import { VisemeType } from './FacialAnimation';
/**
 * GPU面部动画配置
 */
export interface GPUFacialAnimationConfig {
    /** 是否使用计算着色器 */
    useComputeShader?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
    /** 最大混合形状数量 */
    maxBlendShapes?: number;
    /** 纹理大小 */
    textureSize?: number;
}
/**
 * GPU面部动画组件
 */
export declare class GPUFacialAnimationComponent extends Component {
    /** 组件类型 */
    static readonly type = "GPUFacialAnimation";
    /** 原始材质 */
    private originalMaterial;
    /** GPU材质 */
    private gpuMaterial;
    /** 混合形状纹理 */
    private blendShapeTexture;
    /** 混合形状数据 */
    private blendShapeData;
    /** 混合形状映射 */
    private blendShapeMap;
    /** 是否初始化 */
    private initialized;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param entity 实体
     */
    constructor(entity: Entity);
    /**
     * 初始化
     * @param mesh 骨骼网格
     * @param textureSize 纹理大小
     * @returns 是否成功初始化
     */
    initialize(mesh: THREE.SkinnedMesh, textureSize?: number): boolean;
    /**
     * 创建混合形状映射
     * @param mesh 骨骼网格
     */
    private createBlendShapeMap;
    /**
     * 创建混合形状纹理
     * @param mesh 骨骼网格
     * @param textureSize 纹理大小
     */
    private createBlendShapeTexture;
    /**
     * 创建GPU材质
     * @param mesh 骨骼网格
     */
    private createGPUMaterial;
    /**
     * 设置混合形状权重
     * @param name 混合形状名称
     * @param weight 权重
     * @returns 是否成功设置
     */
    setBlendShapeWeight(name: string, weight: number): boolean;
    /**
     * 设置表情
     * @param expression 表情类型
     * @param weight 权重
     * @returns 是否成功设置
     */
    setExpression(expression: FacialExpressionType, weight: number): boolean;
    /**
     * 设置口型
     * @param viseme 口型类型
     * @param weight 权重
     * @returns 是否成功设置
     */
    setViseme(viseme: VisemeType, weight: number): boolean;
    /**
     * 重置所有混合形状
     */
    resetAllBlendShapes(): void;
    /**
     * 恢复原始材质
     * @param mesh 骨骼网格
     */
    restoreOriginalMaterial(mesh: THREE.SkinnedMesh): void;
    /**
     * 销毁GPU面部动画
     * @param mesh 骨骼网格
     */
    disposeGPU(mesh: THREE.SkinnedMesh): void;
    /**
     * 销毁组件
     */
    protected onDispose(): void;
}
/**
 * GPU面部动画系统
 */
export declare class GPUFacialAnimationSystem extends System {
    /** 系统类型 */
    static readonly type = "GPUFacialAnimation";
    /** GPU面部动画组件 */
    private components;
    /** 配置 */
    private config;
    /** 是否支持GPU加速 */
    private supportsGPU;
    /** 是否支持计算着色器 */
    private supportsComputeShader;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(config?: GPUFacialAnimationConfig);
    /**
     * 检查GPU支持
     */
    private checkGPUSupport;
    /**
     * 创建GPU面部动画组件
     * @param entity 实体
     * @param mesh 骨骼网格
     * @returns GPU面部动画组件
     */
    createGPUFacialAnimation(entity: Entity, mesh: THREE.SkinnedMesh): GPUFacialAnimationComponent;
    /**
     * 移除GPU面部动画组件
     * @param entity 实体
     * @param mesh 骨骼网格
     */
    removeGPUFacialAnimation(entity: Entity, mesh: THREE.SkinnedMesh): void;
    /**
     * 获取GPU面部动画组件
     * @param entity 实体
     * @returns GPU面部动画组件，如果不存在则返回null
     */
    getGPUFacialAnimation(entity: Entity): GPUFacialAnimationComponent | null;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
