/**
 * GPU蒙皮系统测试
 */
import * as THREE from 'three';
import { GPUSkinningSystem, AnimationInstanceData } from '../../src/animation/GPUSkinningSystem';
import { EntityManager } from '../../src/core/EntityManager';
import { Entity } from '../../src/core/Entity';
import { Camera } from '../../src/rendering/Camera';
import { Scene } from '../../src/scene/Scene';
import { Transform } from '../../src/scene/Transform';
import { AnimationComponent } from '../../src/animation/AnimationComponent';
import { SkinnedMeshComponent } from '../../src/animation/SkinnedMeshComponent';

// 模拟THREE.WebGLRenderer
jest.mock('three', () => {
  const originalThree = jest.requireActual('three');
  return {
    ...originalThree,
    WebGLRenderer: {
      instance: {
        setRenderTarget: jest.fn(),
        render: jest.fn(),
        readRenderTargetPixels: jest.fn(),
      },
    },
  };
});

describe('GPUSkinningSystem', () => {
  let system: GPUSkinningSystem;
  let entityManager: EntityManager;
  let camera: Entity;
  let scene: Entity;

  beforeEach(() => {
    // 创建实体管理器
    entityManager = new EntityManager();

    // 创建相机实体
    camera = new Entity();
    const cameraComponent = new Camera();
    camera.addComponent(cameraComponent);
    entityManager.addEntity(camera);

    // 创建场景实体
    scene = new Entity();
    const sceneComponent = new Scene();
    scene.addComponent(sceneComponent);
    entityManager.addEntity(scene);

    // 创建系统
    system = new GPUSkinningSystem({
      useGPUSkinning: true,
      useAnimationInstancing: true,
      useAnimationMerging: false,
      useAnimationLOD: false,
      useAnimationCache: true,
      useAnimationCompression: false,
      useDebugVisualization: false,
      maxBones: 200,
      maxInstances: 100,
      updateInterval: 1,
    });
    system.setEntityManager(entityManager);
    system.initialize();
  });

  afterEach(() => {
    system.destroy();
  });

  test('应该正确初始化系统', () => {
    expect(system['initialized']).toBe(true);
    expect(system['useGPUSkinning']).toBe(true);
    expect(system['useAnimationInstancing']).toBe(true);
    expect(system['maxBones']).toBe(200);
    expect(system['maxInstances']).toBe(100);
    expect(system['updateInterval']).toBe(1);
  });

  test('应该正确检查GPU蒙皮支持', () => {
    // 创建不支持GPU蒙皮的系统
    const mockDocument = {
      createElement: jest.fn().mockImplementation(() => ({
        getContext: jest.fn().mockReturnValue(null),
      })),
    };
    global.document = mockDocument as any;

    const fallbackSystem = new GPUSkinningSystem({
      useGPUSkinning: true,
    });

    // 验证系统回退到CPU蒙皮
    expect(fallbackSystem['supportsGPUSkinning']).toBe(false);
    expect(fallbackSystem['useGPUSkinning']).toBe(false);
  });

  test('应该正确创建批处理组', () => {
    // 创建骨骼
    const bones: THREE.Bone[] = [];
    for (let i = 0; i < 3; i++) {
      const bone = new THREE.Bone();
      bone.name = `bone_${i}`;
      bones.push(bone);
    }

    // 创建骨架
    const skeleton = new THREE.Skeleton(bones);

    // 创建蒙皮网格
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial();
    const skinnedMesh = new THREE.SkinnedMesh(geometry, material);
    skinnedMesh.skeleton = skeleton;

    // 创建实体
    const entity = new Entity();
    const transformComponent = new Transform();
    const animationComponent = new AnimationComponent();
    const skinnedMeshComponent = new SkinnedMeshComponent();
    entity.addComponent(transformComponent);
    entity.addComponent(animationComponent);
    entity.addComponent(skinnedMeshComponent);
    entityManager.addEntity(entity);

    // 设置蒙皮网格
    skinnedMeshComponent.setSkinnedMesh(skinnedMesh);

    // 调用私有方法创建批处理组
    const batchGroup = system['createBatchGroup'](bones.length);

    // 验证批处理组属性
    expect(batchGroup).toBeDefined();
    expect(batchGroup.boneCount).toBe(bones.length);
    expect(batchGroup.instanceCount).toBe(system['maxInstances']);
    expect(batchGroup.boneTexture).toBeDefined();
    expect(batchGroup.boneMatrices).toBeDefined();
    expect(batchGroup.instances).toHaveLength(system['maxInstances']);
    expect(batchGroup.availableIndices).toHaveLength(system['maxInstances']);
    expect(batchGroup.needsUpdate).toBe(true);
  });

  test('应该正确添加到批处理组', () => {
    // 创建骨骼
    const bones: THREE.Bone[] = [];
    for (let i = 0; i < 3; i++) {
      const bone = new THREE.Bone();
      bone.name = `bone_${i}`;
      bones.push(bone);
    }

    // 创建骨架
    const skeleton = new THREE.Skeleton(bones);

    // 创建蒙皮网格
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial();
    material.uniforms = {};
    const skinnedMesh = new THREE.SkinnedMesh(geometry, material);
    skinnedMesh.skeleton = skeleton;

    // 创建实体
    const entity = new Entity();
    const transformComponent = new Transform();
    const animationComponent = new AnimationComponent();
    const skinnedMeshComponent = new SkinnedMeshComponent();
    entity.addComponent(transformComponent);
    entity.addComponent(animationComponent);
    entity.addComponent(skinnedMeshComponent);
    entityManager.addEntity(entity);

    // 设置蒙皮网格
    skinnedMeshComponent.setSkinnedMesh(skinnedMesh);

    // 调用私有方法添加到批处理组
    system['addToBatchGroup'](entity, animationComponent, skinnedMeshComponent);

    // 验证实体已添加到批处理组
    expect(system['entityToBatchGroup'].has(entity)).toBe(true);
    expect(system['entityToInstanceData'].has(entity)).toBe(true);

    // 获取批处理组
    const batchGroup = system['entityToBatchGroup'].get(entity);
    expect(batchGroup).toBeDefined();

    // 获取实例数据
    const instanceData = system['entityToInstanceData'].get(entity);
    expect(instanceData).toBeDefined();
    expect(instanceData!.entity).toBe(entity);
    expect(instanceData!.animationComponent).toBe(animationComponent);
    expect(instanceData!.skinnedMeshComponent).toBe(skinnedMeshComponent);
    expect(instanceData!.boneMatrices).toBeDefined();
    expect(instanceData!.boneMatrices.length).toBe(bones.length * 16);
    expect(instanceData!.visible).toBe(true);
    expect(instanceData!.needsUpdate).toBe(true);

    // 验证蒙皮网格材质已更新
    expect(skinnedMesh.material.uniforms.boneTexture).toBeDefined();
    expect(skinnedMesh.material.uniforms.boneTextureSize).toBeDefined();
    expect(skinnedMesh.material.needsUpdate).toBe(true);
  });

  test('应该正确更新批处理组', () => {
    // 创建骨骼
    const bones: THREE.Bone[] = [];
    for (let i = 0; i < 3; i++) {
      const bone = new THREE.Bone();
      bone.name = `bone_${i}`;
      bones.push(bone);
    }

    // 创建骨架
    const skeleton = new THREE.Skeleton(bones);

    // 创建蒙皮网格
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial();
    material.uniforms = {};
    const skinnedMesh = new THREE.SkinnedMesh(geometry, material);
    skinnedMesh.skeleton = skeleton;

    // 创建实体
    const entity = new Entity();
    const transformComponent = new Transform();
    const animationComponent = new AnimationComponent();
    const skinnedMeshComponent = new SkinnedMeshComponent();
    entity.addComponent(transformComponent);
    entity.addComponent(animationComponent);
    entity.addComponent(skinnedMeshComponent);
    entityManager.addEntity(entity);

    // 设置蒙皮网格
    skinnedMeshComponent.setSkinnedMesh(skinnedMesh);

    // 调用私有方法添加到批处理组
    system['addToBatchGroup'](entity, animationComponent, skinnedMeshComponent);

    // 获取批处理组
    const batchGroup = system['entityToBatchGroup'].get(entity);
    expect(batchGroup).toBeDefined();

    // 设置批处理组需要更新
    batchGroup!.needsUpdate = true;

    // 调用私有方法更新批处理组
    system['updateBatchGroup'](batchGroup!, camera.getComponent<Camera>('Camera')!);

    // 验证批处理组已更新
    expect(batchGroup!.needsUpdate).toBe(false);
  });

  test('应该正确使用CPU批处理更新', () => {
    // 创建使用CPU蒙皮的系统
    const cpuSystem = new GPUSkinningSystem({
      useGPUSkinning: false,
      useAnimationInstancing: true,
    });
    cpuSystem.setEntityManager(entityManager);
    cpuSystem.initialize();

    // 创建骨骼
    const bones: THREE.Bone[] = [];
    for (let i = 0; i < 3; i++) {
      const bone = new THREE.Bone();
      bone.name = `bone_${i}`;
      bones.push(bone);
    }

    // 创建骨架
    const skeleton = new THREE.Skeleton(bones);

    // 创建蒙皮网格
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial();
    material.uniforms = {};
    const skinnedMesh = new THREE.SkinnedMesh(geometry, material);
    skinnedMesh.skeleton = skeleton;

    // 创建实体
    const entity = new Entity();
    const transformComponent = new Transform();
    const animationComponent = new AnimationComponent();
    const skinnedMeshComponent = new SkinnedMeshComponent();
    entity.addComponent(transformComponent);
    entity.addComponent(animationComponent);
    entity.addComponent(skinnedMeshComponent);
    entityManager.addEntity(entity);

    // 设置蒙皮网格
    skinnedMeshComponent.setSkinnedMesh(skinnedMesh);

    // 调用私有方法添加到批处理组
    cpuSystem['addToBatchGroup'](entity, animationComponent, skinnedMeshComponent);

    // 获取批处理组
    const batchGroup = cpuSystem['entityToBatchGroup'].get(entity);
    expect(batchGroup).toBeDefined();

    // 设置批处理组需要更新
    batchGroup!.needsUpdate = true;

    // 调用私有方法使用CPU批处理更新
    cpuSystem['updateWithCPUBatching'](batchGroup!);

    // 验证骨骼纹理已更新
    expect(batchGroup!.boneTexture!.needsUpdate).toBe(true);

    // 清理
    cpuSystem.destroy();
  });

  test('应该正确销毁系统', () => {
    // 创建骨骼
    const bones: THREE.Bone[] = [];
    for (let i = 0; i < 3; i++) {
      const bone = new THREE.Bone();
      bone.name = `bone_${i}`;
      bones.push(bone);
    }

    // 创建骨架
    const skeleton = new THREE.Skeleton(bones);

    // 创建蒙皮网格
    const geometry = new THREE.BoxBufferGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial();
    material.uniforms = {};
    const skinnedMesh = new THREE.SkinnedMesh(geometry, material);
    skinnedMesh.skeleton = skeleton;

    // 创建实体
    const entity = new Entity();
    const transformComponent = new Transform();
    const animationComponent = new AnimationComponent();
    const skinnedMeshComponent = new SkinnedMeshComponent();
    entity.addComponent(transformComponent);
    entity.addComponent(animationComponent);
    entity.addComponent(skinnedMeshComponent);
    entityManager.addEntity(entity);

    // 设置蒙皮网格
    skinnedMeshComponent.setSkinnedMesh(skinnedMesh);

    // 调用私有方法添加到批处理组
    system['addToBatchGroup'](entity, animationComponent, skinnedMeshComponent);

    // 销毁系统
    system.destroy();

    // 验证系统已清空
    expect(system['batchGroups'].size).toBe(0);
    expect(system['entityToBatchGroup'].size).toBe(0);
    expect(system['entityToInstanceData'].size).toBe(0);
    expect(system['debugMeshes']).toHaveLength(0);
    expect(system['initialized']).toBe(false);
  });
});
