/**
 * 压缩模块
 * 提供消息压缩和解压功能
 */
import { Module, DynamicModule, Global } from '@nestjs/common';
import { MessageCompressorService } from './message-compressor.service';
import { CompressionOptions } from './interfaces/compression-config.interface';

@Global()
@Module({})
export class CompressionModule {
  /**
   * 注册压缩模块
   * @param options 压缩选项
   * @returns 动态模块
   */
  static register(options?: Partial<CompressionOptions>): DynamicModule {
    return {
      module: CompressionModule,
      providers: [
        {
          provide: 'COMPRESSION_OPTIONS',
          useValue: options || {},
        },
        MessageCompressorService,
      ],
      exports: [MessageCompressorService],
    };
  }
}
