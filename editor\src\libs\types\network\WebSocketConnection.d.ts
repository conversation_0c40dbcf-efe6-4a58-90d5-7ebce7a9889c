/**
 * WebSocket连接
 * 负责管理与服务器的WebSocket连接
 */
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkMessage } from './NetworkMessage';
/**
 * WebSocket连接状态
 */
export declare enum WebSocketConnectionState {
    /** 已断开连接 */
    DISCONNECTED = "disconnected",
    /** 正在连接 */
    CONNECTING = "connecting",
    /** 已连接 */
    CONNECTED = "connected",
    /** 正在断开连接 */
    DISCONNECTING = "disconnecting",
    /** 连接错误 */
    ERROR = "error"
}
/**
 * WebSocket连接
 * 负责管理与服务器的WebSocket连接
 */
export declare class WebSocketConnection extends EventEmitter {
    /** WebSocket实例 */
    private socket;
    /** 服务器URL */
    private url;
    /** 连接状态 */
    private state;
    /** 消息序列化器 */
    private messageSerializer;
    /** 重连尝试次数 */
    private reconnectAttempts;
    /** 最大重连尝试次数 */
    private maxReconnectAttempts;
    /** 重连间隔（毫秒） */
    private reconnectInterval;
    /** 重连定时器ID */
    private reconnectTimerId;
    /** 心跳间隔（毫秒） */
    private heartbeatInterval;
    /** 心跳定时器ID */
    private heartbeatTimerId;
    /** 最后一次接收消息的时间戳 */
    private lastReceivedTime;
    /** 消息队列 */
    private messageQueue;
    /** 是否启用消息队列 */
    private useMessageQueue;
    /** 是否正在处理消息队列 */
    private processingQueue;
    /**
     * 创建WebSocket连接
     * @param url 服务器URL
     * @param useCompression 是否使用压缩
     */
    constructor(url: string, useCompression?: boolean);
    /**
     * 连接到服务器
     * @returns Promise
     */
    connect(): Promise<void>;
    /**
     * 断开连接
     * @returns Promise
     */
    disconnect(): Promise<void>;
    /**
     * 发送消息
     * @param type 消息类型
     * @param message 消息对象
     */
    send(type: string, message: NetworkMessage): Promise<void>;
    /**
     * 获取连接状态
     * @returns 连接状态
     */
    getState(): WebSocketConnectionState;
    /**
     * 是否已连接
     * @returns 是否已连接
     */
    isConnected(): boolean;
    /**
     * 设置重连参数
     * @param maxAttempts 最大重连尝试次数
     * @param interval 重连间隔（毫秒）
     */
    setReconnectParams(maxAttempts: number, interval: number): void;
    /**
     * 设置心跳间隔
     * @param interval 心跳间隔（毫秒）
     */
    setHeartbeatInterval(interval: number): void;
    /**
     * 设置是否使用消息队列
     * @param useQueue 是否使用消息队列
     */
    setUseMessageQueue(useQueue: boolean): void;
    /**
     * 清空消息队列
     */
    clearMessageQueue(): void;
    /**
     * 尝试重连
     */
    private attemptReconnect;
    /**
     * 停止重连
     */
    private stopReconnect;
    /**
     * 启动心跳
     */
    private startHeartbeat;
    /**
     * 停止心跳
     */
    private stopHeartbeat;
    /**
     * 发送心跳消息
     */
    private sendHeartbeat;
    /**
     * 处理消息队列
     */
    private processMessageQueue;
}
