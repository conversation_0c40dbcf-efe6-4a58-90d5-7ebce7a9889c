/**
 * 网络管理器
 * 负责管理网络连接、消息处理和数据同步
 */
import { EventEmitter } from '../utils/EventEmitter';
import type { NetworkSystemOptions } from './NetworkSystem';
/**
 * 网络管理器
 * 负责管理网络连接、消息处理和数据同步
 */
export declare class NetworkManager extends EventEmitter {
    /** 配置选项 */
    private options;
    /** WebSocket连接 */
    private wsConnection;
    /** WebRTC连接映射表 */
    private rtcConnections;
    /** 本地用户ID */
    private localUserId;
    /** 本地用户名 */
    private localUsername;
    /** 远程用户映射表 */
    private remoteUsers;
    /** 网络实体映射表 */
    private networkEntities;
    /** 是否已连接 */
    private connected;
    /** 房间ID */
    private roomId;
    /**
     * 创建网络管理器
     * @param options 配置选项
     */
    constructor(options: NetworkSystemOptions);
    /**
     * 连接到服务器
     * @param serverUrl 服务器URL
     * @param roomId 房间ID
     * @returns Promise
     */
    connect(serverUrl: string, roomId?: string): Promise<void>;
    /**
     * 断开连接
     * @returns Promise
     */
    disconnect(): Promise<void>;
    /**
     * 更新网络管理器
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 发送消息到所有用户
     * @param type 消息类型
     * @param data 消息数据
     */
    sendToAll(type: string, data: any): void;
    /**
     * 发送消息到特定用户
     * @param userId 用户ID
     * @param type 消息类型
     * @param data 消息数据
     */
    sendToUser(userId: string, type: string, data: any): void;
    /**
     * 发送实体更新
     * @param entityId 实体ID
     * @param data 实体数据
     */
    sendEntityUpdate(entityId: string, data: any): void;
    /**
     * 发送加入房间消息
     * @param roomId 房间ID
     */
    private sendJoinRoom;
    /**
     * 发送离开房间消息
     */
    private sendLeaveRoom;
    /**
     * 设置WebSocket事件监听器
     */
    private setupWebSocketListeners;
    /**
     * 处理接收到的消息
     * @param message 网络消息
     */
    private handleMessage;
    /**
     * 处理加入房间成功消息
     * @param message 网络消息
     */
    private handleJoinRoomSuccess;
    /**
     * 处理用户加入消息
     * @param message 网络消息
     */
    private handleUserJoined;
    /**
     * 处理用户离开消息
     * @param message 网络消息
     */
    private handleUserLeft;
    /**
     * 处理实体创建消息
     * @param message 网络消息
     */
    private handleEntityCreate;
    /**
     * 处理实体更新消息
     * @param message 网络消息
     */
    private handleEntityUpdate;
    /**
     * 处理实体删除消息
     * @param message 网络消息
     */
    private handleEntityDelete;
    /**
     * 处理WebRTC提议消息
     * @param message 网络消息
     */
    private handleWebRTCOffer;
    /**
     * 处理WebRTC应答消息
     * @param message 网络消息
     */
    private handleWebRTCAnswer;
    /**
     * 处理WebRTC ICE候选消息
     * @param message 网络消息
     */
    private handleWebRTCIceCandidate;
    /**
     * 添加远程用户
     * @param userId 用户ID
     * @param username 用户名
     */
    private addRemoteUser;
    /**
     * 移除远程用户
     * @param userId 用户ID
     */
    private removeRemoteUser;
    /**
     * 添加网络实体
     * @param entityId 实体ID
     * @param data 实体数据
     */
    private addNetworkEntity;
    /**
     * 更新网络实体
     * @param entityId 实体ID
     * @param data 实体数据
     */
    private updateNetworkEntity;
    /**
     * 移除网络实体
     * @param entityId 实体ID
     */
    private removeNetworkEntity;
    /**
     * 创建WebRTC连接
     * @param userId 用户ID
     */
    private createWebRTCConnection;
    /**
     * 移除WebRTC连接
     * @param userId 用户ID
     */
    private removeWebRTCConnection;
    /**
     * 设置WebRTC事件监听器
     * @param connection WebRTC连接
     */
    private setupWebRTCListeners;
    /**
     * 发送WebRTC提议
     * @param userId 用户ID
     * @param offer 提议
     */
    private sendWebRTCOffer;
    /**
     * 发送WebRTC应答
     * @param userId 用户ID
     * @param answer 应答
     */
    private sendWebRTCAnswer;
    /**
     * 发送WebRTC ICE候选
     * @param userId 用户ID
     * @param candidate ICE候选
     */
    private sendWebRTCIceCandidate;
    /**
     * 销毁网络管理器
     */
    dispose(): void;
}
