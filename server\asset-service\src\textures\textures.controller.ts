/**
 * 纹理控制器
 */
import { Controller, Get, UseGuards, Request, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TexturesService } from './textures.service';
import { Asset } from '../assets/entities/asset.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('纹理')
@Controller('textures')
export class TexturesController {
  constructor(private readonly texturesService: TexturesService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有纹理' })
  @ApiResponse({ status: 200, description: '返回所有纹理', type: [Asset] })
  async findAll(
    @Request() req,
    @Query('projectId') projectId?: string,
    @Query('tags') tags?: string,
  ) {
    const tagArray = tags ? tags.split(',') : undefined;
    return this.texturesService.findAll(req.user.id, projectId, tagArray);
  }

  @Get('search')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索纹理' })
  @ApiResponse({ status: 200, description: '返回搜索结果', type: [Asset] })
  async search(@Request() req, @Query('query') query: string) {
    return this.texturesService.search(query, req.user.id);
  }
}
