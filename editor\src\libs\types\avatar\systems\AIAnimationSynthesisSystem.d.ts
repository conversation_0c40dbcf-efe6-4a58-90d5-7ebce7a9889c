/**
 * AI动画合成系统
 * 用于基于文本描述生成面部动画
 */
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { AIAnimationSynthesisComponent } from '../components/AIAnimationSynthesisComponent';
import { AnimationGenerationRequest } from '../ai/AnimationGenerationTypes';
import { EmotionModelType, EmotionModelVariant } from '../ai/EmotionModelFactory';
/**
 * AI动画合成系统配置
 */
export interface AIAnimationSynthesisSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用本地模型 */
    useLocalModel?: boolean;
    /** 模型路径 */
    modelPath?: string;
    /** 是否使用Web Worker */
    useWorker?: boolean;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 模型类型 */
    modelType?: EmotionModelType;
    /** 模型变体 */
    modelVariant?: EmotionModelVariant;
    /** 是否使用高级生成器 */
    useAdvancedGenerator?: boolean;
    /** 是否使用中文模型 */
    useChineseModel?: boolean;
    /** 是否使用多语言模型 */
    useMultilingualModel?: boolean;
}
/**
 * AI动画合成系统
 */
export declare class AIAnimationSynthesisSystem extends System {
    /** 系统名称 */
    static readonly NAME = "AIAnimationSynthesisSystem";
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** AI动画合成组件映射 */
    private components;
    /** 情感动画生成器 */
    private emotionGenerator;
    /** 情感模型工厂 */
    private emotionModelFactory;
    /** 是否已初始化 */
    private initialized;
    /** 是否正在初始化 */
    private initializing;
    /** Web Worker */
    private worker;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: AIAnimationSynthesisSystemConfig);
    /**
     * 初始化情感动画生成器
     */
    private initEmotionGenerator;
    /**
     * 初始化Web Worker
     */
    private initWorker;
    /**
     * 创建AI动画合成组件
     * @param entity 实体
     * @returns AI动画合成组件
     */
    createAIAnimationSynthesis(entity: Entity): AIAnimationSynthesisComponent;
    /**
     * 获取AI动画合成组件
     * @param entity 实体
     * @returns AI动画合成组件
     */
    getAIAnimationSynthesis(entity: Entity): AIAnimationSynthesisComponent | null;
    /**
     * 移除AI动画合成组件
     * @param entity 实体
     */
    removeAIAnimationSynthesis(entity: Entity): void;
    /**
     * 生成面部动画
     * @param entity 实体
     * @param prompt 提示文本
     * @param duration 持续时间（秒）
     * @param options 其他选项
     * @returns 请求ID
     */
    generateFacialAnimation(entity: Entity, prompt: string, duration?: number, options?: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>>): string | null;
    /**
     * 处理生成请求
     * @param entity 实体
     * @param request 请求
     */
    private handleGenerationRequest;
    /**
     * 处理生成结果
     * @param entity 实体
     * @param result 结果
     */
    private handleGenerationResult;
    /**
     * 处理生成错误
     * @param entity 实体
     * @param request 请求
     * @param error 错误信息
     */
    private handleGenerationError;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
}
