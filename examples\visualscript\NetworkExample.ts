/**
 * 视觉脚本网络示例
 * 演示如何使用视觉脚本系统的网络节点
 */
import { Engine, World, Entity, Scene, Transform, Camera, VisualScriptComponent, VisualScriptSystem } from '../../engine/src';
import { NetworkSystem } from '../../engine/src/network/NetworkSystem';
import { GraphJSON } from '../../engine/src/visualscript/graph/GraphJSON';
import { NodeRegistry } from '../../engine/src/visualscript/nodes/NodeRegistry';
import { ValueTypeRegistry } from '../../engine/src/visualscript/values/ValueTypeRegistry';
import { registerCoreNodes } from '../../engine/src/visualscript/presets/CoreNodes';
import { registerNetworkNodes } from '../../engine/src/visualscript/presets/NetworkNodes';
import { registerLogicNodes } from '../../engine/src/visualscript/presets/LogicNodes';
import { UserRole } from '../../engine/src/network/UserRole';
import { BandwidthControlStrategy } from '../../engine/src/network/BandwidthControlStrategy';

/**
 * 视觉脚本网络示例
 */
export class NetworkExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 世界实例 */
  private world: World;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 视觉脚本系统 */
  private visualScriptSystem: VisualScriptSystem;
  
  /** 网络系统 */
  private networkSystem: NetworkSystem;
  
  /** 服务器URL */
  private serverUrl: string = 'ws://localhost:8080';
  
  /** UI元素 */
  private ui: HTMLElement;
  
  /**
   * 创建示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine({
      debug: true
    });
    
    // 创建世界
    this.world = new World(this.engine);
    
    // 创建节点注册表
    const nodeRegistry = new NodeRegistry();
    
    // 注册核心节点
    registerCoreNodes(nodeRegistry);
    
    // 注册逻辑节点
    registerLogicNodes(nodeRegistry);
    
    // 注册网络节点
    registerNetworkNodes(nodeRegistry);
    
    // 创建值类型注册表
    const valueTypeRegistry = new ValueTypeRegistry();
    
    // 创建视觉脚本系统
    this.visualScriptSystem = new VisualScriptSystem(this.world, {
      defaultDomain: 'default'
    });
    
    // 注册脚本域
    this.visualScriptSystem.registerDomain('default', nodeRegistry, valueTypeRegistry);
    
    // 添加视觉脚本系统到世界
    this.world.addSystem(this.visualScriptSystem);
    
    // 创建网络系统
    this.networkSystem = new NetworkSystem({
      autoConnect: false,
      serverUrl: this.serverUrl,
      enableWebRTC: true,
      enableMediaStream: true,
      enableAudio: true,
      enableVideo: false,
      enableNetworkQualityMonitor: true,
      enableBandwidthControl: true,
      bandwidthControlStrategy: BandwidthControlStrategy.ADAPTIVE,
      enableEntitySync: true,
      enableUserSessionManagement: true,
      defaultUserRole: UserRole.USER,
      enablePermissionCheck: true
    });
    
    // 添加网络系统到世界
    this.world.addSystem(this.networkSystem);
    
    // 创建场景
    this.scene = new Scene(this.world, {
      name: '视觉脚本网络示例场景'
    });
    
    // 创建相机
    const camera = new Entity('相机');
    camera.addComponent(new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000
    }));
    camera.addComponent(new Transform({
      position: { x: 0, y: 2, z: 5 },
      rotation: { x: -0.2, y: 0, z: 0 }
    }));
    
    // 添加相机到场景
    this.scene.addEntity(camera);
    
    // 创建客户端实体
    this.createClientEntity();
    
    // 创建服务器实体
    this.createServerEntity();
    
    // 创建UI
    this.createUI();
    
    // 启动引擎
    this.engine.start();
  }
  
  /**
   * 创建客户端实体
   */
  private createClientEntity(): void {
    // 创建客户端实体
    const clientEntity = new Entity('客户端');
    
    // 添加视觉脚本组件
    const clientScript = this.createClientScript();
    clientEntity.addComponent(new VisualScriptComponent({
      script: clientScript,
      running: true,
      domain: 'default'
    }));
    
    // 添加到场景
    this.scene.addEntity(clientEntity);
  }
  
  /**
   * 创建服务器实体
   */
  private createServerEntity(): void {
    // 创建服务器实体
    const serverEntity = new Entity('服务器');
    
    // 添加视觉脚本组件
    const serverScript = this.createServerScript();
    serverEntity.addComponent(new VisualScriptComponent({
      script: serverScript,
      running: true,
      domain: 'default'
    }));
    
    // 添加到场景
    this.scene.addEntity(serverEntity);
  }
  
  /**
   * 创建客户端脚本
   * @returns 客户端脚本
   */
  private createClientScript(): GraphJSON {
    // 创建客户端脚本
    const script: GraphJSON = {
      nodes: [
        {
          id: 'start',
          type: 'core/events/onStart',
          metadata: {
            positionX: 100,
            positionY: 100
          },
          flows: {
            flow: {
              nodeId: 'connect',
              socket: 'flow'
            }
          }
        },
        {
          id: 'connect',
          type: 'network/connectToServer',
          metadata: {
            positionX: 300,
            positionY: 100
          },
          parameters: {
            serverUrl: {
              value: this.serverUrl
            }
          },
          flows: {
            success: {
              nodeId: 'logSuccess',
              socket: 'flow'
            },
            fail: {
              nodeId: 'logFail',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logSuccess',
          type: 'core/debug/log',
          metadata: {
            positionX: 500,
            positionY: 50
          },
          parameters: {
            message: {
              value: '连接服务器成功'
            }
          }
        },
        {
          id: 'logFail',
          type: 'core/debug/log',
          metadata: {
            positionX: 500,
            positionY: 150
          },
          parameters: {
            message: {
              value: '连接服务器失败'
            }
          }
        },
        {
          id: 'onMessage',
          type: 'network/events/onMessage',
          metadata: {
            positionX: 100,
            positionY: 300
          },
          flows: {
            flow: {
              nodeId: 'logMessage',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logMessage',
          type: 'core/debug/log',
          metadata: {
            positionX: 300,
            positionY: 300
          },
          parameters: {
            message: {
              value: '收到消息: ${message}'
            }
          }
        }
      ],
      variables: [],
      customEvents: []
    };
    
    return script;
  }
  
  /**
   * 创建服务器脚本
   * @returns 服务器脚本
   */
  private createServerScript(): GraphJSON {
    // 创建服务器脚本
    const script: GraphJSON = {
      nodes: [
        {
          id: 'start',
          type: 'core/events/onStart',
          metadata: {
            positionX: 100,
            positionY: 100
          },
          flows: {
            flow: {
              nodeId: 'wait',
              socket: 'flow'
            }
          }
        },
        {
          id: 'wait',
          type: 'core/time/delay',
          metadata: {
            positionX: 300,
            positionY: 100
          },
          parameters: {
            duration: {
              value: 5
            }
          },
          flows: {
            flow: {
              nodeId: 'sendMessage',
              socket: 'flow'
            }
          }
        },
        {
          id: 'sendMessage',
          type: 'network/sendMessage',
          metadata: {
            positionX: 500,
            positionY: 100
          },
          parameters: {
            message: {
              value: {
                type: 'greeting',
                content: '你好，客户端！'
              }
            },
            reliable: {
              value: true
            }
          },
          flows: {
            flow: {
              nodeId: 'logSent',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logSent',
          type: 'core/debug/log',
          metadata: {
            positionX: 700,
            positionY: 100
          },
          parameters: {
            message: {
              value: '消息已发送'
            }
          },
          flows: {
            flow: {
              nodeId: 'wait',
              socket: 'flow'
            }
          }
        }
      ],
      variables: [],
      customEvents: []
    };
    
    return script;
  }
  
  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    this.ui = document.createElement('div');
    this.ui.style.position = 'absolute';
    this.ui.style.top = '10px';
    this.ui.style.left = '10px';
    this.ui.style.padding = '10px';
    this.ui.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    this.ui.style.color = 'white';
    this.ui.style.fontFamily = 'Arial, sans-serif';
    this.ui.style.borderRadius = '5px';
    
    // 添加标题
    const title = document.createElement('h2');
    title.textContent = '视觉脚本网络示例';
    this.ui.appendChild(title);
    
    // 添加说明
    const description = document.createElement('p');
    description.textContent = '这个示例演示了如何使用视觉脚本系统的网络节点进行网络通信。';
    this.ui.appendChild(description);
    
    // 添加状态显示
    const status = document.createElement('div');
    status.id = 'network-status';
    status.textContent = '网络状态: 未连接';
    this.ui.appendChild(status);
    
    // 添加消息列表
    const messagesTitle = document.createElement('h3');
    messagesTitle.textContent = '消息记录:';
    this.ui.appendChild(messagesTitle);
    
    const messagesList = document.createElement('ul');
    messagesList.id = 'messages-list';
    this.ui.appendChild(messagesList);
    
    // 添加到文档
    document.body.appendChild(this.ui);
    
    // 监听网络事件
    this.networkSystem.on('connected', () => {
      const statusElement = document.getElementById('network-status');
      if (statusElement) {
        statusElement.textContent = '网络状态: 已连接';
        statusElement.style.color = 'lightgreen';
      }
    });
    
    this.networkSystem.on('disconnected', () => {
      const statusElement = document.getElementById('network-status');
      if (statusElement) {
        statusElement.textContent = '网络状态: 已断开';
        statusElement.style.color = 'orange';
      }
    });
    
    this.networkSystem.on('error', (error) => {
      const statusElement = document.getElementById('network-status');
      if (statusElement) {
        statusElement.textContent = `网络状态: 错误 (${error.message})`;
        statusElement.style.color = 'red';
      }
    });
    
    this.networkSystem.on('message', (message) => {
      const messagesListElement = document.getElementById('messages-list');
      if (messagesListElement) {
        const item = document.createElement('li');
        item.textContent = `${new Date().toLocaleTimeString()}: ${JSON.stringify(message.data)}`;
        messagesListElement.appendChild(item);
        
        // 限制显示的消息数量
        if (messagesListElement.children.length > 10) {
          messagesListElement.removeChild(messagesListElement.firstChild);
        }
      }
    });
  }
  
  /**
   * 销毁示例
   */
  public dispose(): void {
    // 停止引擎
    this.engine.stop();
    
    // 移除UI
    if (this.ui && this.ui.parentNode) {
      this.ui.parentNode.removeChild(this.ui);
    }
    
    // 销毁引擎
    this.engine.dispose();
  }
}
