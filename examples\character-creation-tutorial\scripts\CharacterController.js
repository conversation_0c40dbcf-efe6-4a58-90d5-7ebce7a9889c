/**
 * 角色控制器
 * 负责处理角色的移动和交互控制
 */
export class CharacterController {
  /**
   * 构造函数
   */
  constructor() {
    this.character = null;
    this.stateMachine = null;
    this.animationManager = null;
    
    // 控制参数
    this.moveSpeed = 5.0;
    this.rotationSpeed = 120.0;
    this.jumpForce = 10.0;
    this.gravity = 20.0;
    
    // 运行时状态
    this.isGrounded = true;
    this.velocity = { x: 0, y: 0, z: 0 };
    this.moveDirection = { x: 0, y: 0, z: 0 };
    this.isJumping = false;
    this.jumpCooldown = 0;
    
    // 输入状态
    this.inputState = {
      forward: false,
      backward: false,
      left: false,
      right: false,
      jump: false,
      sprint: false,
      action: false
    };
    
    // 摄像机设置
    this.cameraMode = 'thirdPerson'; // 'firstPerson', 'thirdPerson', 'orbital'
    this.cameraDistance = 5.0;
    this.cameraHeight = 2.0;
    this.cameraLookAt = { x: 0, y: 1.5, z: 0 };
    
    // 物理设置
    this.usePhysics = true;
    this.collisionRadius = 0.5;
    this.collisionHeight = 2.0;
    this.stepOffset = 0.3;
    this.slopeLimit = 45.0;
  }
  
  /**
   * 初始化
   * @param {Object} character 角色对象
   * @param {Object} stateMachine 状态机对象
   * @param {AnimationManager} animationManager 动画管理器
   */
  initialize(character, stateMachine, animationManager) {
    this.character = character;
    this.stateMachine = stateMachine;
    this.animationManager = animationManager;
    
    // 设置输入事件监听
    this.setupInputListeners();
  }
  
  /**
   * 设置输入事件监听
   */
  setupInputListeners() {
    // 键盘按下事件
    document.addEventListener('keydown', (event) => {
      this.handleKeyDown(event);
    });
    
    // 键盘松开事件
    document.addEventListener('keyup', (event) => {
      this.handleKeyUp(event);
    });
    
    // 鼠标移动事件
    document.addEventListener('mousemove', (event) => {
      this.handleMouseMove(event);
    });
    
    // 鼠标按下事件
    document.addEventListener('mousedown', (event) => {
      this.handleMouseDown(event);
    });
    
    // 鼠标松开事件
    document.addEventListener('mouseup', (event) => {
      this.handleMouseUp(event);
    });
  }
  
  /**
   * 处理键盘按下事件
   * @param {KeyboardEvent} event 键盘事件
   */
  handleKeyDown(event) {
    switch (event.code) {
      case 'KeyW':
        this.inputState.forward = true;
        break;
      case 'KeyS':
        this.inputState.backward = true;
        break;
      case 'KeyA':
        this.inputState.left = true;
        break;
      case 'KeyD':
        this.inputState.right = true;
        break;
      case 'Space':
        this.inputState.jump = true;
        break;
      case 'ShiftLeft':
        this.inputState.sprint = true;
        break;
      case 'KeyE':
        this.inputState.action = true;
        break;
    }
  }
  
  /**
   * 处理键盘松开事件
   * @param {KeyboardEvent} event 键盘事件
   */
  handleKeyUp(event) {
    switch (event.code) {
      case 'KeyW':
        this.inputState.forward = false;
        break;
      case 'KeyS':
        this.inputState.backward = false;
        break;
      case 'KeyA':
        this.inputState.left = false;
        break;
      case 'KeyD':
        this.inputState.right = false;
        break;
      case 'Space':
        this.inputState.jump = false;
        break;
      case 'ShiftLeft':
        this.inputState.sprint = false;
        break;
      case 'KeyE':
        this.inputState.action = false;
        break;
    }
  }
  
  /**
   * 处理鼠标移动事件
   * @param {MouseEvent} event 鼠标事件
   */
  handleMouseMove(event) {
    // 在实际应用中，这里应该处理摄像机旋转
    // 这里使用模拟代码
    
    if (event.buttons & 1) { // 左键按下
      const rotationX = event.movementY * 0.1;
      const rotationY = event.movementX * 0.1;
      
      // 更新摄像机旋转
      this.updateCameraRotation(rotationX, rotationY);
    }
  }
  
  /**
   * 处理鼠标按下事件
   * @param {MouseEvent} event 鼠标事件
   */
  handleMouseDown(event) {
    if (event.button === 0) { // 左键
      // 在实际应用中，这里可以处理攻击或交互
    } else if (event.button === 2) { // 右键
      // 在实际应用中，这里可以处理瞄准或特殊能力
    }
  }
  
  /**
   * 处理鼠标松开事件
   * @param {MouseEvent} event 鼠标事件
   */
  handleMouseUp(event) {
    if (event.button === 0) { // 左键
      // 在实际应用中，这里可以处理攻击或交互结束
    } else if (event.button === 2) { // 右键
      // 在实际应用中，这里可以处理瞄准或特殊能力结束
    }
  }
  
  /**
   * 更新
   * @param {number} deltaTime 时间增量（秒）
   */
  update(deltaTime) {
    // 处理输入
    this.processInput(deltaTime);
    
    // 应用重力
    this.applyGravity(deltaTime);
    
    // 更新位置
    this.updatePosition(deltaTime);
    
    // 检测碰撞
    this.detectCollisions();
    
    // 更新状态机参数
    this.updateStateMachineParameters();
    
    // 更新摄像机
    this.updateCamera(deltaTime);
  }
  
  /**
   * 处理输入
   * @param {number} deltaTime 时间增量（秒）
   */
  processInput(deltaTime) {
    // 计算移动方向
    this.moveDirection = { x: 0, y: 0, z: 0 };
    
    if (this.inputState.forward) {
      this.moveDirection.z -= 1;
    }
    
    if (this.inputState.backward) {
      this.moveDirection.z += 1;
    }
    
    if (this.inputState.left) {
      this.moveDirection.x -= 1;
    }
    
    if (this.inputState.right) {
      this.moveDirection.x += 1;
    }
    
    // 归一化移动方向
    const length = Math.sqrt(
      this.moveDirection.x * this.moveDirection.x +
      this.moveDirection.z * this.moveDirection.z
    );
    
    if (length > 0) {
      this.moveDirection.x /= length;
      this.moveDirection.z /= length;
    }
    
    // 应用移动速度
    let speed = this.moveSpeed;
    
    if (this.inputState.sprint) {
      speed *= 2.0;
    }
    
    this.moveDirection.x *= speed;
    this.moveDirection.z *= speed;
    
    // 处理跳跃
    if (this.inputState.jump && this.isGrounded && this.jumpCooldown <= 0) {
      this.velocity.y = this.jumpForce;
      this.isGrounded = false;
      this.isJumping = true;
      this.jumpCooldown = 0.3; // 跳跃冷却时间（秒）
    }
    
    // 更新跳跃冷却
    if (this.jumpCooldown > 0) {
      this.jumpCooldown -= deltaTime;
    }
  }
  
  /**
   * 应用重力
   * @param {number} deltaTime 时间增量（秒）
   */
  applyGravity(deltaTime) {
    if (!this.isGrounded) {
      this.velocity.y -= this.gravity * deltaTime;
    } else {
      this.velocity.y = 0;
    }
  }
  
  /**
   * 更新位置
   * @param {number} deltaTime 时间增量（秒）
   */
  updatePosition(deltaTime) {
    // 在实际应用中，这里应该使用物理系统或角色控制器
    // 这里使用模拟代码
    
    // 更新水平速度
    this.velocity.x = this.moveDirection.x;
    this.velocity.z = this.moveDirection.z;
    
    // 更新位置
    if (this.character && this.character.position) {
      this.character.position.x += this.velocity.x * deltaTime;
      this.character.position.y += this.velocity.y * deltaTime;
      this.character.position.z += this.velocity.z * deltaTime;
    }
  }
  
  /**
   * 检测碰撞
   */
  detectCollisions() {
    // 在实际应用中，这里应该使用物理系统进行碰撞检测
    // 这里使用模拟代码
    
    // 模拟地面碰撞
    if (this.character && this.character.position) {
      if (this.character.position.y <= 0) {
        this.character.position.y = 0;
        this.isGrounded = true;
        this.isJumping = false;
      }
    }
  }
  
  /**
   * 更新状态机参数
   */
  updateStateMachineParameters() {
    // 在实际应用中，这里应该更新状态机参数
    // 这里使用模拟代码
    
    if (this.stateMachine) {
      // 计算移动速度
      const speed = Math.sqrt(
        this.velocity.x * this.velocity.x +
        this.velocity.z * this.velocity.z
      );
      
      // 更新状态机参数
      this.setStateMachineParameter('speed', speed);
      this.setStateMachineParameter('isJumping', this.isJumping);
    }
  }
  
  /**
   * 设置状态机参数
   * @param {string} paramName 参数名称
   * @param {any} value 参数值
   */
  setStateMachineParameter(paramName, value) {
    // 在实际应用中，这里应该设置状态机参数
    console.log('设置状态机参数:', paramName, value);
  }
  
  /**
   * 更新摄像机
   * @param {number} deltaTime 时间增量（秒）
   */
  updateCamera(deltaTime) {
    // 在实际应用中，这里应该更新摄像机位置和旋转
    // 这里使用模拟代码
    
    if (this.character && this.character.position) {
      // 根据摄像机模式更新摄像机
      switch (this.cameraMode) {
        case 'firstPerson':
          this.updateFirstPersonCamera();
          break;
        case 'thirdPerson':
          this.updateThirdPersonCamera();
          break;
        case 'orbital':
          this.updateOrbitalCamera(deltaTime);
          break;
      }
    }
  }
  
  /**
   * 更新第一人称摄像机
   */
  updateFirstPersonCamera() {
    // 在实际应用中，这里应该更新第一人称摄像机
    console.log('更新第一人称摄像机');
  }
  
  /**
   * 更新第三人称摄像机
   */
  updateThirdPersonCamera() {
    // 在实际应用中，这里应该更新第三人称摄像机
    console.log('更新第三人称摄像机');
  }
  
  /**
   * 更新轨道摄像机
   * @param {number} deltaTime 时间增量（秒）
   */
  updateOrbitalCamera(deltaTime) {
    // 在实际应用中，这里应该更新轨道摄像机
    console.log('更新轨道摄像机');
  }
  
  /**
   * 更新摄像机旋转
   * @param {number} rotationX X轴旋转（度）
   * @param {number} rotationY Y轴旋转（度）
   */
  updateCameraRotation(rotationX, rotationY) {
    // 在实际应用中，这里应该更新摄像机旋转
    console.log('更新摄像机旋转:', rotationX, rotationY);
  }
  
  /**
   * 设置摄像机模式
   * @param {string} mode 摄像机模式
   */
  setCameraMode(mode) {
    this.cameraMode = mode;
    console.log('设置摄像机模式:', mode);
  }
  
  /**
   * 设置移动速度
   * @param {number} speed 移动速度
   */
  setMoveSpeed(speed) {
    this.moveSpeed = speed;
    console.log('设置移动速度:', speed);
  }
  
  /**
   * 设置跳跃力度
   * @param {number} force 跳跃力度
   */
  setJumpForce(force) {
    this.jumpForce = force;
    console.log('设置跳跃力度:', force);
  }
  
  /**
   * 设置重力
   * @param {number} gravity 重力
   */
  setGravity(gravity) {
    this.gravity = gravity;
    console.log('设置重力:', gravity);
  }
}
