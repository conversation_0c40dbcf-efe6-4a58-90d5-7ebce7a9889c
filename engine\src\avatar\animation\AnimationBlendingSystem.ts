/**
 * 动画混合系统
 * 支持多个动画的混合、过渡和重定向
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { BIPSkeletonData, StandardSkeletonData, StandardBoneType } from '../bip';

/**
 * 动画关键帧
 */
export interface AnimationKeyframe {
  /** 时间戳 */
  time: number;
  /** 骨骼变换 */
  transforms: Map<StandardBoneType, {
    position: THREE.Vector3;
    rotation: THREE.Quaternion;
    scale: THREE.Vector3;
  }>;
}

/**
 * 动画轨道
 */
export interface AnimationTrack {
  /** 轨道ID */
  id: string;
  /** 轨道名称 */
  name: string;
  /** 目标骨骼 */
  targetBone: StandardBoneType;
  /** 关键帧列表 */
  keyframes: AnimationKeyframe[];
  /** 插值类型 */
  interpolation: 'linear' | 'cubic' | 'step';
  /** 是否循环 */
  loop: boolean;
}

/**
 * 动画剪辑
 */
export interface AnimationClip {
  /** 剪辑ID */
  id: string;
  /** 剪辑名称 */
  name: string;
  /** 持续时间 */
  duration: number;
  /** 帧率 */
  frameRate: number;
  /** 动画轨道 */
  tracks: AnimationTrack[];
  /** 动画类型 */
  type: 'idle' | 'walk' | 'run' | 'gesture' | 'expression' | 'custom';
  /** 优先级 */
  priority: number;
  /** 是否可混合 */
  blendable: boolean;
  /** 混合权重 */
  weight: number;
  /** 元数据 */
  metadata: {
    source?: string;
    author?: string;
    tags?: string[];
    description?: string;
  };
}

/**
 * 动画状态
 */
export interface AnimationState {
  /** 状态ID */
  id: string;
  /** 状态名称 */
  name: string;
  /** 关联的动画剪辑 */
  clip: AnimationClip;
  /** 当前时间 */
  currentTime: number;
  /** 播放速度 */
  speed: number;
  /** 权重 */
  weight: number;
  /** 是否启用 */
  enabled: boolean;
  /** 是否循环 */
  loop: boolean;
  /** 淡入时间 */
  fadeInTime: number;
  /** 淡出时间 */
  fadeOutTime: number;
  /** 当前淡入淡出状态 */
  fadeState: 'none' | 'fadeIn' | 'fadeOut';
  /** 淡入淡出进度 */
  fadeProgress: number;
}

/**
 * 动画过渡
 */
export interface AnimationTransition {
  /** 过渡ID */
  id: string;
  /** 源状态 */
  fromState: string;
  /** 目标状态 */
  toState: string;
  /** 过渡时间 */
  duration: number;
  /** 过渡曲线 */
  curve: 'linear' | 'easeIn' | 'easeOut' | 'easeInOut' | 'custom';
  /** 自定义曲线函数 */
  customCurve?: (t: number) => number;
  /** 过渡条件 */
  conditions: AnimationCondition[];
  /** 是否可中断 */
  canBeInterrupted: boolean;
}

/**
 * 动画条件
 */
export interface AnimationCondition {
  /** 条件类型 */
  type: 'parameter' | 'time' | 'event' | 'custom';
  /** 参数名称 */
  parameter?: string;
  /** 比较操作 */
  operator: 'equals' | 'notEquals' | 'greater' | 'less' | 'greaterEqual' | 'lessEqual';
  /** 比较值 */
  value: any;
  /** 自定义条件函数 */
  customCondition?: () => boolean;
}

/**
 * 动画混合器
 */
export interface AnimationBlender {
  /** 混合器ID */
  id: string;
  /** 混合器名称 */
  name: string;
  /** 混合类型 */
  type: 'additive' | 'override' | 'multiply';
  /** 输入状态列表 */
  inputStates: string[];
  /** 输出权重 */
  outputWeight: number;
  /** 混合参数 */
  blendParameters: Map<string, number>;
}

/**
 * 动画混合系统配置
 */
export interface AnimationBlendingSystemConfig {
  /** 最大同时播放动画数量 */
  maxConcurrentAnimations?: number;
  /** 默认过渡时间 */
  defaultTransitionDuration?: number;
  /** 是否启用动画压缩 */
  enableCompression?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 动画混合系统
 */
export class AnimationBlendingSystem extends System {
  /** 系统优先级 */
  public static readonly PRIORITY = 4;

  /** 配置 */
  private config: AnimationBlendingSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 动画剪辑库 */
  private animationClips: Map<string, AnimationClip> = new Map();

  /** 动画状态机 */
  private animationStates: Map<string, AnimationState> = new Map();

  /** 动画过渡 */
  private transitions: Map<string, AnimationTransition[]> = new Map();

  /** 动画混合器 */
  private blenders: Map<string, AnimationBlender> = new Map();

  /** 实体动画映射 */
  private entityAnimations: Map<string, {
    currentStates: Set<string>;
    activeTransitions: Map<string, {
      transition: AnimationTransition;
      startTime: number;
      progress: number;
    }>;
    parameters: Map<string, any>;
    skeleton: StandardSkeletonData;
  }> = new Map();

  /** 动画参数 */
  private globalParameters: Map<string, any> = new Map();

  /**
   * 构造函数
   * @param world 世界实例
   * @param config 系统配置
   */
  constructor(world: World, config: AnimationBlendingSystemConfig = {}) {
    super();

    this.config = {
      maxConcurrentAnimations: 5,
      defaultTransitionDuration: 0.3,
      enableCompression: true,
      updateFrequency: 60,
      debug: false,
      ...config
    };
  }

  /**
   * 系统初始化
   */
  public initialize(): void {
    if (this.config.debug) {
      console.log('[AnimationBlendingSystem] 系统初始化');
    }

    // 监听实体添加事件
    this.world.on('entityAdded', (entity: Entity) => {
      this.registerEntity(entity);
    });

    // 监听实体移除事件
    this.world.on('entityRemoved', (entity: Entity) => {
      this.unregisterEntity(entity);
    });
  }

  /**
   * 系统更新
   * @param deltaTime 帧间隔时间
   */
  public update(deltaTime: number): void {
    // 更新所有实体的动画
    for (const [entityId, animationData] of Array.from(this.entityAnimations.entries())) {
      this.updateEntityAnimation(entityId, animationData, deltaTime);
    }
  }

  /**
   * 注册实体
   * @param entity 实体
   */
  private registerEntity(entity: Entity): void {
    // TODO: 检查实体是否有动画组件
    // 这里简化处理，假设所有实体都可以有动画
    this.entityAnimations.set(entity.id, {
      currentStates: new Set(),
      activeTransitions: new Map(),
      parameters: new Map(),
      skeleton: {} as StandardSkeletonData // 应该从实体获取骨骼数据
    });

    if (this.config.debug) {
      console.log('[AnimationBlendingSystem] 注册实体动画', entity.id);
    }
  }

  /**
   * 注销实体
   * @param entity 实体
   */
  private unregisterEntity(entity: Entity): void {
    this.entityAnimations.delete(entity.id);

    if (this.config.debug) {
      console.log('[AnimationBlendingSystem] 注销实体动画', entity.id);
    }
  }

  /**
   * 添加动画剪辑
   * @param clip 动画剪辑
   */
  public addAnimationClip(clip: AnimationClip): void {
    this.animationClips.set(clip.id, clip);
    this.eventEmitter.emit('clipAdded', clip);

    if (this.config.debug) {
      console.log('[AnimationBlendingSystem] 添加动画剪辑', clip.name);
    }
  }

  /**
   * 移除动画剪辑
   * @param clipId 剪辑ID
   */
  public removeAnimationClip(clipId: string): void {
    const clip = this.animationClips.get(clipId);
    if (clip) {
      this.animationClips.delete(clipId);
      this.eventEmitter.emit('clipRemoved', clip);

      if (this.config.debug) {
        console.log('[AnimationBlendingSystem] 移除动画剪辑', clip.name);
      }
    }
  }

  /**
   * 创建动画状态
   * @param stateConfig 状态配置
   * @returns 动画状态
   */
  public createAnimationState(stateConfig: {
    id: string;
    name: string;
    clipId: string;
    speed?: number;
    weight?: number;
    loop?: boolean;
    fadeInTime?: number;
    fadeOutTime?: number;
  }): AnimationState {
    const clip = this.animationClips.get(stateConfig.clipId);
    if (!clip) {
      throw new Error(`动画剪辑不存在: ${stateConfig.clipId}`);
    }

    const state: AnimationState = {
      id: stateConfig.id,
      name: stateConfig.name,
      clip,
      currentTime: 0,
      speed: stateConfig.speed || 1,
      weight: stateConfig.weight || 1,
      enabled: true,
      loop: stateConfig.loop !== undefined ? stateConfig.loop : clip.type !== 'gesture',
      fadeInTime: stateConfig.fadeInTime || 0.2,
      fadeOutTime: stateConfig.fadeOutTime || 0.2,
      fadeState: 'none',
      fadeProgress: 0
    };

    this.animationStates.set(state.id, state);
    this.eventEmitter.emit('stateCreated', state);

    if (this.config.debug) {
      console.log('[AnimationBlendingSystem] 创建动画状态', state.name);
    }

    return state;
  }

  /**
   * 添加动画过渡
   * @param transition 过渡配置
   */
  public addTransition(transition: AnimationTransition): void {
    if (!this.transitions.has(transition.fromState)) {
      this.transitions.set(transition.fromState, []);
    }
    
    this.transitions.get(transition.fromState)!.push(transition);
    this.eventEmitter.emit('transitionAdded', transition);

    if (this.config.debug) {
      console.log('[AnimationBlendingSystem] 添加动画过渡', 
        `${transition.fromState} -> ${transition.toState}`);
    }
  }

  /**
   * 播放动画
   * @param entityId 实体ID
   * @param stateId 状态ID
   * @param fadeInTime 淡入时间
   */
  public playAnimation(entityId: string, stateId: string, fadeInTime?: number): void {
    const animationData = this.entityAnimations.get(entityId);
    const state = this.animationStates.get(stateId);

    if (!animationData || !state) {
      if (this.config.debug) {
        console.warn('[AnimationBlendingSystem] 播放动画失败', entityId, stateId);
      }
      return;
    }

    // 设置淡入
    if (fadeInTime !== undefined) {
      state.fadeInTime = fadeInTime;
    }
    
    state.fadeState = 'fadeIn';
    state.fadeProgress = 0;
    state.currentTime = 0;
    state.enabled = true;

    animationData.currentStates.add(stateId);
    this.eventEmitter.emit('animationStarted', entityId, stateId);

    if (this.config.debug) {
      console.log('[AnimationBlendingSystem] 播放动画', entityId, state.name);
    }
  }

  /**
   * 停止动画
   * @param entityId 实体ID
   * @param stateId 状态ID
   * @param fadeOutTime 淡出时间
   */
  public stopAnimation(entityId: string, stateId: string, fadeOutTime?: number): void {
    const animationData = this.entityAnimations.get(entityId);
    const state = this.animationStates.get(stateId);

    if (!animationData || !state || !animationData.currentStates.has(stateId)) {
      return;
    }

    // 设置淡出
    if (fadeOutTime !== undefined) {
      state.fadeOutTime = fadeOutTime;
    }
    
    state.fadeState = 'fadeOut';
    state.fadeProgress = 0;

    this.eventEmitter.emit('animationStopping', entityId, stateId);

    if (this.config.debug) {
      console.log('[AnimationBlendingSystem] 停止动画', entityId, state.name);
    }
  }

  /**
   * 设置动画参数
   * @param entityId 实体ID
   * @param parameterName 参数名称
   * @param value 参数值
   */
  public setAnimationParameter(entityId: string, parameterName: string, value: any): void {
    const animationData = this.entityAnimations.get(entityId);
    if (animationData) {
      animationData.parameters.set(parameterName, value);
      this.checkTransitionConditions(entityId);
    }
  }

  /**
   * 设置全局动画参数
   * @param parameterName 参数名称
   * @param value 参数值
   */
  public setGlobalParameter(parameterName: string, value: any): void {
    this.globalParameters.set(parameterName, value);
    
    // 检查所有实体的过渡条件
    for (const entityId of Array.from(this.entityAnimations.keys())) {
      this.checkTransitionConditions(entityId);
    }
  }

  /**
   * 更新实体动画
   * @param entityId 实体ID
   * @param animationData 动画数据
   * @param deltaTime 帧间隔时间
   */
  private updateEntityAnimation(
    entityId: string,
    animationData: any,
    deltaTime: number
  ): void {
    // 更新活动状态
    for (const stateId of animationData.currentStates) {
      this.updateAnimationState(entityId, stateId, deltaTime);
    }

    // 更新过渡
    this.updateTransitions(entityId, animationData, deltaTime);

    // 混合动画结果
    this.blendAnimations(entityId, animationData);
  }

  /**
   * 更新动画状态
   * @param entityId 实体ID
   * @param stateId 状态ID
   * @param deltaTime 帧间隔时间
   */
  private updateAnimationState(entityId: string, stateId: string, deltaTime: number): void {
    const state = this.animationStates.get(stateId);
    if (!state || !state.enabled) return;

    // 更新时间
    state.currentTime += deltaTime * state.speed;

    // 处理循环
    if (state.loop && state.currentTime >= state.clip.duration) {
      state.currentTime = state.currentTime % state.clip.duration;
    } else if (!state.loop && state.currentTime >= state.clip.duration) {
      state.currentTime = state.clip.duration;
      this.stopAnimation(entityId, stateId);
      return;
    }

    // 更新淡入淡出
    this.updateFadeState(state, deltaTime);
  }

  /**
   * 更新淡入淡出状态
   * @param state 动画状态
   * @param deltaTime 帧间隔时间
   */
  private updateFadeState(state: AnimationState, deltaTime: number): void {
    if (state.fadeState === 'fadeIn') {
      state.fadeProgress += deltaTime / state.fadeInTime;
      if (state.fadeProgress >= 1) {
        state.fadeProgress = 1;
        state.fadeState = 'none';
      }
    } else if (state.fadeState === 'fadeOut') {
      state.fadeProgress += deltaTime / state.fadeOutTime;
      if (state.fadeProgress >= 1) {
        state.fadeProgress = 1;
        state.fadeState = 'none';
        state.enabled = false;
      }
    }
  }

  /**
   * 更新过渡
   * @param entityId 实体ID
   * @param animationData 动画数据
   * @param deltaTime 帧间隔时间
   */
  private updateTransitions(entityId: string, animationData: any, deltaTime: number): void {
    // TODO: 实现过渡更新逻辑
  }

  /**
   * 检查过渡条件
   * @param entityId 实体ID
   */
  private checkTransitionConditions(entityId: string): void {
    // TODO: 实现过渡条件检查
  }

  /**
   * 混合动画
   * @param entityId 实体ID
   * @param animationData 动画数据
   */
  private blendAnimations(entityId: string, animationData: any): void {
    // TODO: 实现动画混合逻辑
  }

  // 移除了重写的EventEmitter方法，直接使用继承的方法

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.animationClips.clear();
    this.animationStates.clear();
    this.transitions.clear();
    this.blenders.clear();
    this.entityAnimations.clear();
    this.globalParameters.clear();
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('[AnimationBlendingSystem] 系统已销毁');
    }
  }
}
