import { Controller, Get, Query, Param } from '@nestjs/common';
import { MonitoringService } from './monitoring.service';

/**
 * 监控控制器
 * 提供监控数据的API接口
 */
@Controller('monitoring')
export class MonitoringController {
  constructor(private readonly monitoringService: MonitoringService) {}

  /**
   * 获取系统指标
   */
  @Get('metrics/system')
  async getSystemMetrics(
    @Query('hostname') hostname?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ) {
    const startTime = from ? new Date(from) : new Date(Date.now() - 24 * 60 * 60 * 1000);
    const endTime = to ? new Date(to) : new Date();
    const host = hostname || 'localhost';

    return await this.monitoringService.getSystemMetrics(host, startTime, endTime);
  }

  /**
   * 获取服务指标
   */
  @Get('metrics/services')
  async getServiceMetrics(
    @Query('serviceId') serviceId?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ) {
    const startTime = from ? new Date(from) : new Date(Date.now() - 24 * 60 * 60 * 1000);
    const endTime = to ? new Date(to) : new Date();
    const service = serviceId || 'default';

    return await this.monitoringService.getServiceMetrics(service, startTime, endTime);
  }

  /**
   * 获取特定服务的指标
   */
  @Get('metrics/service/:serviceName')
  async getServiceMetricsByName(
    @Param('serviceName') serviceName: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ) {
    const startTime = from ? new Date(from) : new Date(Date.now() - 24 * 60 * 60 * 1000);
    const endTime = to ? new Date(to) : new Date();

    return await this.monitoringService.getServiceMetrics(serviceName, startTime, endTime);
  }

  /**
   * 获取历史指标数据
   */
  @Get('metrics/history')
  async getHistoricalMetrics(
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('service') service?: string,
  ) {
    const startTime = from ? new Date(from) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const endTime = to ? new Date(to) : new Date();

    if (service) {
      return await this.monitoringService.getServiceMetrics(service, startTime, endTime);
    } else {
      return await this.monitoringService.getSystemMetrics('localhost', startTime, endTime);
    }
  }

  /**
   * 获取实时指标
   */
  @Get('metrics/realtime')
  async getRealtimeMetrics() {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    return await this.monitoringService.getSystemMetrics('localhost', fiveMinutesAgo, now);
  }
}
