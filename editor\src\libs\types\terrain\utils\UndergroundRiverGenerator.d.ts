import { TerrainComponent } from '../components/TerrainComponent';
/**
 * 地下河生成参数
 */
export interface UndergroundRiverParams {
    /** 河流数量 */
    count: number;
    /** 河流宽度 */
    width: number;
    /** 河流深度 */
    depth: number;
    /** 河流曲折度 */
    sinuosity: number;
    /** 河流分支概率 */
    branchProbability: number;
    /** 最小长度 */
    minLength: number;
    /** 最大长度 */
    maxLength: number;
    /** 最小深度（相对于地形高度的比例） */
    minDepthRatio: number;
    /** 最大深度（相对于地形高度的比例） */
    maxDepthRatio: number;
    /** 连接到洞穴的概率 */
    caveProbability: number;
    /** 种子 */
    seed: number;
}
/**
 * 地下河生成器
 */
export declare class UndergroundRiverGenerator {
    /**
     * 生成地下河系统
     * @param terrain 地形组件
     * @param params 地下河生成参数
     */
    static generateUndergroundRivers(terrain: TerrainComponent, params: UndergroundRiverParams): void;
    /**
     * 生成单条地下河
     * @param terrain 地形组件
     * @param startX 起点X坐标
     * @param startZ 起点Z坐标
     * @param dirX X方向
     * @param dirZ Z方向
     * @param width 宽度
     * @param depth 深度
     * @param sinuosity 曲折度
     * @param branchProbability 分支概率
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @param startDepthRatio 起始深度比例
     * @param minDepthRatio 最小深度比例
     * @param maxDepthRatio 最大深度比例
     * @param random 随机数生成器
     * @returns 地下河
     */
    private static generateRiver;
    /**
     * 应用地下河到地形
     * @param terrain 地形组件
     * @param rivers 地下河列表
     * @param depth 深度
     */
    private static applyRiversToTerrain;
    /**
     * 连接地下河到洞穴
     * @param terrain 地形组件
     * @param rivers 地下河列表
     * @param caveProbability 洞穴概率
     * @param random 随机数生成器
     */
    private static connectRiversToCaves;
    /**
     * 连接单条地下河到洞穴
     * @param terrain 地形组件
     * @param river 地下河
     * @param caves 洞穴数据
     * @param random 随机数生成器
     */
    private static connectRiverToCave;
    /**
     * 创建连接通道
     * @param terrain 地形组件
     * @param riverNode 河流节点
     * @param cave 洞穴
     * @param random 随机数生成器
     */
    private static createConnectionTunnel;
    /**
     * 创建随机数生成器
     * @param seed 种子
     * @returns 随机数生成器函数
     */
    private static createRandomGenerator;
}
