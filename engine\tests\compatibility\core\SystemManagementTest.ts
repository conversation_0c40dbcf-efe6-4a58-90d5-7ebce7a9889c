/**
 * 系统管理兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 系统管理兼容性测试
 */
export const systemManagementTest: TestCase = {
  name: '系统管理兼容性测试',
  description: '测试系统管理功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目世界实例
      const originalWorld = new original.World();
      
      // 创建重构后项目世界实例
      const refactoredWorld = new refactored.World();
      
      // 检查世界实例是否创建成功
      if (!originalWorld || !refactoredWorld) {
        return {
          name: '系统管理兼容性测试',
          passed: false,
          errorMessage: '世界实例创建失败'
        };
      }
      
      // 创建系统
      class OriginalTestSystem extends original.System {
        constructor() {
          super();
          this.priority = 10;
          this.updateCount = 0;
        }
        
        update(deltaTime: number) {
          this.updateCount++;
        }
      }
      
      class RefactoredTestSystem extends refactored.System {
        constructor() {
          super();
          this.priority = 10;
          this.updateCount = 0;
        }
        
        update(deltaTime: number) {
          this.updateCount++;
        }
      }
      
      // 创建系统实例
      const originalSystem = new OriginalTestSystem();
      const refactoredSystem = new RefactoredTestSystem();
      
      // 添加系统到世界
      originalWorld.addSystem(originalSystem);
      refactoredWorld.addSystem(refactoredSystem);
      
      // 检查系统是否添加成功
      const originalSystemCount = originalWorld.getSystemCount();
      const refactoredSystemCount = refactoredWorld.getSystemCount();
      
      if (originalSystemCount !== refactoredSystemCount) {
        return {
          name: '系统管理兼容性测试',
          passed: false,
          errorMessage: `系统数量不一致: 原有项目=${originalSystemCount}, 重构后项目=${refactoredSystemCount}`,
          details: {
            originalSystemCount,
            refactoredSystemCount
          }
        };
      }
      
      // 更新世界
      originalWorld.update(0.016);
      refactoredWorld.update(0.016);
      
      // 检查系统是否更新
      if (originalSystem.updateCount !== refactoredSystem.updateCount) {
        return {
          name: '系统管理兼容性测试',
          passed: false,
          errorMessage: `系统更新次数不一致: 原有项目=${originalSystem.updateCount}, 重构后项目=${refactoredSystem.updateCount}`,
          details: {
            originalUpdateCount: originalSystem.updateCount,
            refactoredUpdateCount: refactoredSystem.updateCount
          }
        };
      }
      
      // 移除系统
      originalWorld.removeSystem(originalSystem);
      refactoredWorld.removeSystem(refactoredSystem);
      
      // 检查系统是否移除成功
      const originalSystemCountAfterRemove = originalWorld.getSystemCount();
      const refactoredSystemCountAfterRemove = refactoredWorld.getSystemCount();
      
      if (originalSystemCountAfterRemove !== refactoredSystemCountAfterRemove) {
        return {
          name: '系统管理兼容性测试',
          passed: false,
          errorMessage: `移除系统后数量不一致: 原有项目=${originalSystemCountAfterRemove}, 重构后项目=${refactoredSystemCountAfterRemove}`,
          details: {
            originalSystemCountAfterRemove,
            refactoredSystemCountAfterRemove
          }
        };
      }
      
      return {
        name: '系统管理兼容性测试',
        passed: true,
        details: {
          originalSystemCount,
          refactoredSystemCount,
          originalSystemCountAfterRemove,
          refactoredSystemCountAfterRemove
        }
      };
    } catch (error) {
      return {
        name: '系统管理兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
