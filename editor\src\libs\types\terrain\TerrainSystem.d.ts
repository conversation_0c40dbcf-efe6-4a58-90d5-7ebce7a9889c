import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { TerrainComponent } from './components/TerrainComponent';
import { PhysicsSystem } from '../physics/PhysicsSystem';
/**
 * 地形系统
 */
export declare class TerrainSystem extends System {
    /** 系统类型 */
    static readonly TYPE = "TerrainSystem";
    /** 地形实体列表 */
    private terrainEntities;
    /** 地形工具 */
    private terrainUtils;
    /** 地形物理助手 */
    private terrainPhysicsHelper;
    /** 物理系统 */
    private physicsSystem;
    /**
     * 创建地形系统
     */
    constructor();
    /**
     * 设置物理系统
     * @param physicsSystem 物理系统
     */
    setPhysicsSystem(physicsSystem: PhysicsSystem): void;
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 添加地形实体
     * @param entity 实体
     * @param component 地形组件
     */
    addTerrainEntity(entity: Entity, component: TerrainComponent): void;
    /**
     * 移除地形实体
     * @param entity 实体
     */
    removeTerrainEntity(entity: Entity): void;
    /**
     * 初始化地形
     * @param entity 实体
     * @param component 地形组件
     */
    private initializeTerrain;
    /**
     * 创建地形几何体
     * @param component 地形组件
     * @returns 地形几何体
     */
    private createTerrainGeometry;
    /**
     * 创建地形材质
     * @param component 地形组件
     * @returns 地形材质
     */
    private createTerrainMaterial;
    /**
     * 初始化地形物理
     * @param entity 实体
     * @param component 地形组件
     */
    private initializeTerrainPhysics;
    /**
     * 更新地形
     * @param component 地形组件
     */
    private updateTerrain;
    /**
     * 更新地形物理
     * @param entity 实体
     * @param component 地形组件
     */
    private updateTerrainPhysics;
    /**
     * 释放地形资源
     * @param component 地形组件
     */
    private disposeTerrain;
    /**
     * 更新系统
     * @param _deltaTime 时间增量（当前未使用）
     */
    update(_deltaTime: number): void;
}
