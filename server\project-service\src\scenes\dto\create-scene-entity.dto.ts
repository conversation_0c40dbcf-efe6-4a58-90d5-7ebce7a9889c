/**
 * 创建场景实体DTO
 */
import { IsString, IsNotEmpty, IsOptional, IsObject, ValidateNested, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

class TransformDto {
  @ApiProperty({ description: '位置', example: [0, 0, 0] })
  @IsArray()
  position: [number, number, number];

  @ApiProperty({ description: '旋转', example: [0, 0, 0] })
  @IsArray()
  rotation: [number, number, number];

  @ApiProperty({ description: '缩放', example: [1, 1, 1] })
  @IsArray()
  scale: [number, number, number];
}

export class CreateSceneEntityDto {
  @ApiProperty({ description: '实体名称', example: '立方体' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '实体类型', required: false, example: 'mesh' })
  @IsString()
  @IsOptional()
  type?: string;

  @ApiProperty({ description: '变换', type: TransformDto })
  @ValidateNested()
  @Type(() => TransformDto)
  transform: TransformDto;

  @ApiProperty({ description: '组件', required: false, type: 'object' })
  @IsObject()
  @IsOptional()
  components?: Record<string, any>;

  @ApiProperty({ description: '父实体ID', required: false })
  @IsString()
  @IsOptional()
  parentId?: string;

  @ApiProperty({ description: '元数据', required: false, type: 'object' })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
