/**
 * 视觉脚本AI模型节点
 * 提供对不同AI模型的支持
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 加载AI模型节点
 * 加载指定类型的AI模型
 */
export declare class LoadAIModelNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 文本生成节点
 * 使用AI模型生成文本
 */
export declare class TextGenerationNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 图像生成节点
 * 使用AI模型生成图像
 */
export declare class ImageGenerationNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 情感分析节点
 * 分析文本情感
 */
export declare class EmotionAnalysisNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 注册AI模型节点
 * @param registry 节点注册表
 */
export declare function registerAIModelNodes(registry: NodeRegistry): void;
