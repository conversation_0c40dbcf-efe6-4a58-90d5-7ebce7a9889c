/**
 * 一维混合空间
 * 用于在一维参数空间中混合多个动画
 */
import type { AnimationClip } from './AnimationClip';
/**
 * 混合空间节点
 */
export interface BlendSpaceNode {
    /** 动画片段 */
    clip: AnimationClip;
    /** 位置 */
    position: number;
    /** 权重 */
    weight: number;
    /** 用户数据 */
    userData?: any;
}
/**
 * 混合空间配置
 */
export interface BlendSpace1DConfig {
    /** 最小值 */
    minValue: number;
    /** 最大值 */
    maxValue: number;
    /** 是否规范化输入值 */
    normalizeInput?: boolean;
    /** 是否使用平滑混合 */
    useSmoothing?: boolean;
    /** 平滑因子 (0-1) */
    smoothingFactor?: number;
    /** 是否启用外推 */
    enableExtrapolation?: boolean;
}
/**
 * 一维混合空间
 */
export declare class BlendSpace1D {
    /** 配置 */
    private config;
    /** 节点列表 */
    private nodes;
    /** 当前位置 */
    private position;
    /** 目标位置 */
    private targetPosition;
    /** 上一次更新时间 */
    private lastUpdateTime;
    /** 是否已初始化 */
    private initialized;
    /** 名称 */
    private name;
    /** 描述 */
    private description;
    /**
     * 构造函数
     * @param config 混合空间配置
     * @param name 名称
     * @param description 描述
     */
    constructor(config: BlendSpace1DConfig, name?: string, description?: string);
    /**
     * 添加节点
     * @param clip 动画片段
     * @param position 位置
     * @param userData 用户数据
     * @returns 添加的节点
     */
    addNode(clip: AnimationClip, position: number, userData?: any): BlendSpaceNode;
    /**
     * 移除节点
     * @param node 要移除的节点
     * @returns 是否成功移除
     */
    removeNode(node: BlendSpaceNode): boolean;
    /**
     * 设置当前位置
     * @param position 位置
     * @param immediate 是否立即更新（不使用平滑）
     */
    setPosition(position: number, immediate?: boolean): void;
    /**
     * 获取当前位置
     * @returns 当前位置
     */
    getPosition(): number;
    /**
     * 获取目标位置
     * @returns 目标位置
     */
    getTargetPosition(): number;
    /**
     * 更新混合权重
     * @param deltaTime 时间增量（秒），如果不提供则自动计算
     */
    update(deltaTime?: number): void;
    /**
     * 按位置排序节点
     */
    private sortNodes;
    /**
     * 找到最近的两个节点
     * @param position 位置
     * @returns 最近的两个节点
     */
    private findNearestNodes;
    /**
     * 获取所有节点
     * @returns 节点列表
     */
    getNodes(): BlendSpaceNode[];
    /**
     * 获取活跃节点（权重大于0的节点）
     * @returns 活跃节点列表
     */
    getActiveNodes(): BlendSpaceNode[];
    /**
     * 获取配置
     * @returns 混合空间配置
     */
    getConfig(): BlendSpace1DConfig;
    /**
     * 设置配置
     * @param config 混合空间配置
     */
    setConfig(config: Partial<BlendSpace1DConfig>): void;
    /**
     * 获取名称
     * @returns 名称
     */
    getName(): string;
    /**
     * 设置名称
     * @param name 名称
     */
    setName(name: string): void;
    /**
     * 获取描述
     * @returns 描述
     */
    getDescription(): string;
    /**
     * 设置描述
     * @param description 描述
     */
    setDescription(description: string): void;
    /**
     * 清空所有节点
     */
    clearNodes(): void;
    /**
     * 获取节点数量
     * @returns 节点数量
     */
    getNodeCount(): number;
    /**
     * 获取节点
     * @param index 节点索引
     * @returns 节点，如果不存在则返回null
     */
    getNode(index: number): BlendSpaceNode | null;
    /**
     * 更新节点位置
     * @param index 节点索引
     * @param position 新位置
     * @returns 是否成功更新
     */
    updateNodePosition(index: number, position: number): boolean;
    /**
     * 序列化为JSON
     * @returns JSON对象
     */
    toJSON(): any;
    /**
     * 从JSON创建混合空间
     * @param json JSON对象
     * @param getClip 获取动画片段的函数
     * @returns 混合空间
     */
    static fromJSON(json: any, getClip: (name: string) => AnimationClip): BlendSpace1D;
}
