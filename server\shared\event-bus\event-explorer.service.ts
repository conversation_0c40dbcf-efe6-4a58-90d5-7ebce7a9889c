/**
 * 事件探索服务
 * 用于自动发现和注册事件处理器
 */
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DiscoveryService, MetadataScanner, Reflector } from '@nestjs/core';
import { InstanceWrapper } from '@nestjs/core/injector/instance-wrapper';
import { EVENT_HANDLER_METADATA } from './event-bus.constants';
import { EventBusService } from './event-bus.service';
import { Event, EventHandler } from './event-bus.interface';

@Injectable()
export class EventExplorerService implements OnModuleInit {
  private readonly logger = new Logger(EventExplorerService.name);

  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
    private readonly reflector: Reflector,
    private readonly eventBusService: EventBusService,
  ) {}

  /**
   * 模块初始化时探索和注册所有事件处理器
   */
  onModuleInit() {
    this.explore();
  }

  /**
   * 探索所有事件处理器
   */
  explore() {
    const providers = this.discoveryService.getProviders();
    const controllers = this.discoveryService.getControllers();
    
    // 探索提供者中的事件处理器
    providers.forEach((wrapper) => {
      this.exploreEventHandlersInProvider(wrapper);
    });
    
    // 探索控制器中的事件处理器
    controllers.forEach((wrapper) => {
      this.exploreEventHandlersInProvider(wrapper);
    });
    
    this.logger.log('事件处理器探索完成');
  }

  /**
   * 在提供者中探索事件处理器
   * @param wrapper 实例包装器
   */
  private exploreEventHandlersInProvider(wrapper: InstanceWrapper) {
    const { instance } = wrapper;
    
    if (!instance || typeof instance !== 'object') {
      return;
    }
    
    // 获取实例的原型
    const prototype = Object.getPrototypeOf(instance);
    
    // 扫描原型中的所有方法
    this.metadataScanner.scanFromPrototype(instance, prototype, (methodName) => {
      this.subscribeToEventIfHandler(instance, methodName);
    });
  }

  /**
   * 如果方法是事件处理器，则订阅事件
   * @param instance 实例
   * @param methodName 方法名
   */
  private subscribeToEventIfHandler(instance: any, methodName: string) {
    const eventHandlerMetadata = this.reflector.get(EVENT_HANDLER_METADATA, instance[methodName]);
    
    if (!eventHandlerMetadata) {
      return;
    }
    
    const { event } = eventHandlerMetadata;
    
    // 确保处理器方法存在且是函数
    if (!instance[methodName] || typeof instance[methodName] !== 'function') {
      return;
    }
    
    // 订阅事件
    this.eventBusService.subscribe(event, async (eventData: Event) => {
      try {
        await instance[methodName].call(instance, eventData);
      } catch (error) {
        this.logger.error(
          `事件处理器执行失败: ${instance.constructor.name}.${methodName} 处理事件 ${event}`,
          error.stack,
        );
      }
    });
    
    this.logger.debug(
      `已注册事件处理器: ${instance.constructor.name}.${methodName} 处理事件 ${event}`,
    );
  }
}
