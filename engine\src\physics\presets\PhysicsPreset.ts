/**
 * 物理预设接口
 * 定义物理预设的数据结构
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { BodyType } from '../PhysicsBody';
import { ColliderType } from '../PhysicsCollider';

/**
 * 物理体预设接口
 */
export interface PhysicsBodyPreset {
  /** 物理体类型 */
  type: BodyType;
  /** 质量 */
  mass: number;
  /** 线性阻尼 */
  linearDamping: number;
  /** 角阻尼 */
  angularDamping: number;
  /** 是否允许休眠 */
  allowSleep: boolean;
  /** 是否固定旋转 */
  fixedRotation: boolean;
  /** 材质名称 */
  materialName: string;
  /** 是否启用连续碰撞检测 */
  enableCCD: boolean;
  /** 碰撞过滤组 */
  collisionFilterGroup: number;
  /** 碰撞过滤掩码 */
  collisionFilterMask: number;
}

/**
 * 碰撞器预设接口
 */
export interface PhysicsColliderPreset {
  /** 碰撞器类型 */
  type: ColliderType;
  /** 是否是触发器 */
  isTrigger: boolean;
  /** 参数 */
  params: any;
  /** 偏移位置 */
  position?: THREE.Vector3;
  /** 偏移旋转 */
  quaternion?: THREE.Quaternion;
  /** 材质名称 */
  materialName: string;
}

/**
 * 约束预设接口
 */
export interface PhysicsConstraintPreset {
  /** 约束类型 */
  type: string;
  /** 是否碰撞连接 */
  collideConnected: boolean;
  /** 参数 */
  params: any;
}

/**
 * 物理材质预设接口
 */
export interface PhysicsMaterialPreset {
  /** 材质名称 */
  name: string;
  /** 摩擦力 */
  friction: number;
  /** 恢复系数 */
  restitution: number;
  /** 接触方程刚度 */
  contactEquationStiffness?: number;
  /** 接触方程松弛 */
  contactEquationRelaxation?: number;
  /** 摩擦方程刚度 */
  frictionEquationStiffness?: number;
  /** 摩擦方程松弛 */
  frictionEquationRelaxation?: number;
}

/**
 * 物理世界预设接口
 */
export interface PhysicsWorldPreset {
  /** 重力 */
  gravity: THREE.Vector3;
  /** 是否允许休眠 */
  allowSleep: boolean;
  /** 迭代次数 */
  iterations: number;
  /** 宽相检测算法 */
  broadphase: 'naive' | 'sap' | 'grid';
  /** 网格宽相检测的单元格大小 */
  gridBroadphaseSize?: number;
  /** 默认摩擦力 */
  defaultFriction: number;
  /** 默认恢复系数 */
  defaultRestitution: number;
}

/**
 * 物理预设接口
 */
export interface PhysicsPreset {
  /** 预设名称 */
  name: string;
  /** 预设描述 */
  description: string;
  /** 预设类别 */
  category: string;
  /** 物理世界预设 */
  worldPreset?: PhysicsWorldPreset;
  /** 物理体预设 */
  bodyPreset?: PhysicsBodyPreset;
  /** 碰撞器预设 */
  colliderPreset?: PhysicsColliderPreset;
  /** 约束预设 */
  constraintPreset?: PhysicsConstraintPreset;
  /** 材质预设 */
  materialPresets?: PhysicsMaterialPreset[];
  /** 自定义数据 */
  customData?: any;
}
