/**
 * 媒体流约束定义
 */
export interface VideoConstraints extends MediaTrackConstraints {
    /** 视频宽度 */
    width?: ConstrainULong;
    /** 视频高度 */
    height?: ConstrainULong;
    /** 帧率 */
    frameRate?: ConstrainDouble;
    /** 设备ID */
    deviceId?: ConstrainDOMString;
    /** 面向模式 */
    facingMode?: ConstrainDOMString;
}
export interface AudioConstraints extends MediaTrackConstraints {
    /** 设备ID */
    deviceId?: ConstrainDOMString;
    /** 自动增益控制 */
    autoGainControl?: ConstrainBoolean;
    /** 回声消除 */
    echoCancellation?: ConstrainBoolean;
    /** 噪声抑制 */
    noiseSuppression?: ConstrainBoolean;
    /** 采样率 */
    sampleRate?: ConstrainULong;
    /** 采样大小 */
    sampleSize?: ConstrainULong;
    /** 声道数 */
    channelCount?: ConstrainULong;
}
export interface CustomMediaStreamConstraints {
    /** 音频约束 */
    audio?: boolean | AudioConstraints;
    /** 视频约束 */
    video?: boolean | VideoConstraints;
    /** 屏幕共享约束 */
    screen?: boolean | {
        video?: boolean | VideoConstraints;
        audio?: boolean | AudioConstraints;
    };
}
export declare class MediaStreamConstraintsBuilder {
    private constraints;
    setAudio(enabled: boolean, constraints?: AudioConstraints): this;
    setVideo(enabled: boolean, constraints?: VideoConstraints): this;
    setScreenShare(enabled: boolean, options?: {
        video?: boolean | VideoConstraints;
        audio?: boolean | AudioConstraints;
    }): this;
    build(): MediaStreamConstraints;
    static createDefault(): MediaStreamConstraintsBuilder;
    static createAudioOnly(): MediaStreamConstraintsBuilder;
    static createVideoOnly(): MediaStreamConstraintsBuilder;
}
