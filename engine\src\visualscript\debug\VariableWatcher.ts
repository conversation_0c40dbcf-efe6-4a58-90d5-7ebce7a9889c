/**
 * 变量监视器
 * 负责监视视觉脚本变量的变化
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Graph } from '../graph/Graph';

/**
 * 变量监视信息
 */
export interface VariableWatch {
  /** 变量名 */
  name: string;
  /** 图ID */
  graphId: string;
  /** 是否启用 */
  enabled: boolean;
  /** 条件表达式 */
  condition?: string;
  /** 变量类型 */
  type?: string;
  /** 格式化表达式 */
  format?: string;
  /** 监视标签 */
  tags?: string[];
  /** 监视描述 */
  description?: string;
  /** 创建时间 */
  createdAt?: number;
  /** 最后更新时间 */
  lastUpdatedAt?: number;
  /** 更新次数 */
  updateCount?: number;
  /** 是否记录历史 */
  recordHistory?: boolean;
  /** 是否在值变化时通知 */
  notifyOnChange?: boolean;
  /** 是否在值满足条件时通知 */
  notifyOnCondition?: boolean;
}

/**
 * 变量变化信息
 */
export interface VariableChangeInfo {
  /** 变量名 */
  name: string;
  /** 图ID */
  graphId: string;
  /** 旧值 */
  oldValue: any;
  /** 新值 */
  newValue: any;
  /** 变化时间 */
  timestamp: number;
  /** 变化类型 */
  changeType?: 'create' | 'update' | 'delete';
  /** 变化描述 */
  description?: string;
  /** 节点ID */
  nodeId?: string;
  /** 变化来源 */
  source?: string;
}

/**
 * 变量监视器
 */
export class VariableWatcher extends EventEmitter {
  /** 监视的变量映射 */
  private watches: Map<string, VariableWatch> = new Map();

  /** 变量历史记录 */
  private history: Map<string, VariableChangeInfo[]> = new Map();

  /** 最大历史记录数量 */
  private maxHistorySize: number = 100;

  /**
   * 创建变量监视器
   * @param maxHistorySize 最大历史记录数量
   */
  constructor(maxHistorySize: number = 100) {
    super();
    this.maxHistorySize = maxHistorySize;
  }

  /**
   * 添加变量监视
   * @param name 变量名
   * @param graphId 图ID
   * @param options 监视选项
   * @returns 是否成功
   */
  public addWatch(
    name: string,
    graphId: string,
    options: {
      condition?: string;
      type?: string;
      format?: string;
      tags?: string[];
      description?: string;
      recordHistory?: boolean;
      notifyOnChange?: boolean;
      notifyOnCondition?: boolean;
      enabled?: boolean;
    } = {}
  ): boolean {
    const id = `${graphId}:${name}`;

    // 检查是否已存在
    if (this.watches.has(id)) {
      return false;
    }

    const watch: VariableWatch = {
      name,
      graphId,
      condition: options.condition,
      type: options.type,
      format: options.format,
      tags: options.tags || [],
      description: options.description,
      createdAt: Date.now(),
      lastUpdatedAt: undefined,
      updateCount: 0,
      recordHistory: options.recordHistory !== undefined ? options.recordHistory : true,
      notifyOnChange: options.notifyOnChange !== undefined ? options.notifyOnChange : false,
      notifyOnCondition: options.notifyOnCondition !== undefined ? options.notifyOnCondition : false,
      enabled: options.enabled !== undefined ? options.enabled : true
    };

    this.watches.set(id, watch);

    this.emit('watchAdded', watch);

    return true;
  }

  /**
   * 移除变量监视
   * @param name 变量名
   * @param graphId 图ID
   * @returns 是否成功
   */
  public removeWatch(name: string, graphId: string): boolean {
    const id = `${graphId}:${name}`;

    const watch = this.watches.get(id);

    if (watch) {
      this.watches.delete(id);
      this.emit('watchRemoved', watch);
      return true;
    }

    return false;
  }

  /**
   * 更新变量监视
   * @param name 变量名
   * @param graphId 图ID
   * @param updates 更新内容
   * @returns 是否成功
   */
  public updateWatch(
    name: string,
    graphId: string,
    updates: Partial<Omit<VariableWatch, 'name' | 'graphId'>>
  ): boolean {
    const id = `${graphId}:${name}`;

    const watch = this.watches.get(id);

    if (watch) {
      // 更新监视
      Object.assign(watch, updates);

      this.emit('watchUpdated', watch);
      return true;
    }

    return false;
  }

  /**
   * 启用变量监视
   * @param name 变量名
   * @param graphId 图ID
   * @returns 是否成功
   */
  public enableWatch(name: string, graphId: string): boolean {
    return this.updateWatch(name, graphId, { enabled: true });
  }

  /**
   * 禁用变量监视
   * @param name 变量名
   * @param graphId 图ID
   * @returns 是否成功
   */
  public disableWatch(name: string, graphId: string): boolean {
    return this.updateWatch(name, graphId, { enabled: false });
  }

  /**
   * 获取变量监视
   * @param name 变量名
   * @param graphId 图ID
   * @returns 变量监视
   */
  public getWatch(name: string, graphId: string): VariableWatch | undefined {
    const id = `${graphId}:${name}`;
    return this.watches.get(id);
  }

  /**
   * 获取所有变量监视
   * @returns 变量监视列表
   */
  public getAllWatches(): VariableWatch[] {
    return Array.from(this.watches.values());
  }

  /**
   * 获取图的所有变量监视
   * @param graphId 图ID
   * @returns 变量监视列表
   */
  public getGraphWatches(graphId: string): VariableWatch[] {
    return this.getAllWatches().filter(watch => watch.graphId === graphId);
  }

  /**
   * 清除所有变量监视
   */
  public clearAllWatches(): void {
    const watches = this.getAllWatches();
    this.watches.clear();

    for (const watch of watches) {
      this.emit('watchRemoved', watch);
    }

    this.emit('allWatchesCleared');
  }

  /**
   * 清除图的所有变量监视
   * @param graphId 图ID
   */
  public clearGraphWatches(graphId: string): void {
    const graphWatches = this.getGraphWatches(graphId);

    for (const watch of graphWatches) {
      const id = `${graphId}:${watch.name}`;
      this.watches.delete(id);
      this.emit('watchRemoved', watch);
    }

    this.emit('graphWatchesCleared', graphId);
  }

  /**
   * 检查变量是否被监视
   * @param name 变量名
   * @param graphId 图ID
   * @returns 是否被监视
   */
  public isWatched(name: string, graphId: string): boolean {
    const id = `${graphId}:${name}`;
    return this.watches.has(id);
  }

  /**
   * 检查变量是否被启用监视
   * @param name 变量名
   * @param graphId 图ID
   * @returns 是否被启用监视
   */
  public isWatchEnabled(name: string, graphId: string): boolean {
    const watch = this.getWatch(name, graphId);
    return watch ? watch.enabled : false;
  }

  /**
   * 检查变量监视是否应该触发
   * @param name 变量名
   * @param graphId 图ID
   * @param value 变量值
   * @param graph 图
   * @returns 是否应该触发
   */
  public shouldTrigger(name: string, graphId: string, value: any, graph: Graph): boolean {
    const watch = this.getWatch(name, graphId);

    if (!watch || !watch.enabled) {
      return false;
    }

    // 如果有条件表达式，检查条件
    if (watch.condition) {
      try {
        // 使用Function构造函数创建一个函数来评估条件
        // 这个函数可以访问图中的变量和当前变量值
        const conditionFunc = new Function('graph', 'value', `with (graph.getVariables()) { return ${watch.condition}; }`);
        return conditionFunc(graph, value);
      } catch (error) {
        console.error(`变量监视条件表达式错误: ${error}`);
        return false;
      }
    }

    return true;
  }

  /**
   * 记录变量变化
   * @param name 变量名
   * @param graphId 图ID
   * @param oldValue 旧值
   * @param newValue 新值
   * @param options 记录选项
   */
  public recordChange(
    name: string,
    graphId: string,
    oldValue: any,
    newValue: any,
    options: {
      changeType?: 'create' | 'update' | 'delete';
      description?: string;
      nodeId?: string;
      source?: string;
    } = {}
  ): void {
    const id = `${graphId}:${name}`;

    // 获取监视
    const watch = this.getWatch(name, graphId);

    // 如果没有监视或监视被禁用，不记录变化
    if (!watch || !watch.enabled) {
      return;
    }

    // 更新监视统计
    watch.lastUpdatedAt = Date.now();
    watch.updateCount = (watch.updateCount || 0) + 1;

    // 创建变化记录
    const changeInfo: VariableChangeInfo = {
      name,
      graphId,
      oldValue,
      newValue,
      timestamp: Date.now(),
      changeType: options.changeType || 'update',
      description: options.description,
      nodeId: options.nodeId,
      source: options.source
    };

    // 如果监视配置为记录历史，则记录历史
    if (watch.recordHistory !== false) {
      // 获取历史记录
      let history = this.history.get(id);

      if (!history) {
        history = [];
        this.history.set(id, history);
      }

      // 添加到历史记录
      history.push(changeInfo);

      // 限制历史记录大小
      if (history.length > this.maxHistorySize) {
        history.shift();
      }
    }

    // 检查是否需要通知
    let shouldNotify = false;

    // 如果配置为在值变化时通知，且值确实发生了变化
    if (watch.notifyOnChange && !this.areValuesEqual(oldValue, newValue)) {
      shouldNotify = true;
    }

    // 如果配置为在满足条件时通知，且有条件表达式
    if (watch.notifyOnCondition && watch.condition) {
      try {
        // 获取图
        const graph = this.getGraphById(graphId);

        if (graph) {
          // 评估条件
          const conditionFunc = new Function('graph', 'value', `with (graph.getVariables()) { return ${watch.condition}; }`);
          if (conditionFunc(graph, newValue)) {
            shouldNotify = true;
          }
        }
      } catch (error) {
        console.error(`变量监视条件表达式错误: ${error}`);
      }
    }

    // 触发变化事件
    this.emit('variableChanged', changeInfo, shouldNotify);
  }

  /**
   * 比较两个值是否相等
   * @param a 第一个值
   * @param b 第二个值
   * @returns 是否相等
   */
  private areValuesEqual(a: any, b: any): boolean {
    // 如果两个值都是对象或数组，使用JSON.stringify比较
    if (typeof a === 'object' && a !== null && typeof b === 'object' && b !== null) {
      try {
        return JSON.stringify(a) === JSON.stringify(b);
      } catch (error) {
        // 如果JSON.stringify失败，使用简单比较
        return a === b;
      }
    }

    // 否则使用简单比较
    return a === b;
  }

  /**
   * 根据ID获取图
   * @param graphId 图ID
   * @returns 图
   */
  private getGraphById(graphId: string): Graph | null {
    // 这个方法需要根据实际情况实现
    // 可能需要从外部传入图的映射
    return null;
  }

  /**
   * 获取变量历史记录
   * @param name 变量名
   * @param graphId 图ID
   * @returns 历史记录
   */
  public getHistory(name: string, graphId: string): VariableChangeInfo[] {
    const id = `${graphId}:${name}`;
    return this.history.get(id) || [];
  }

  /**
   * 清除变量历史记录
   * @param name 变量名
   * @param graphId 图ID
   */
  public clearHistory(name: string, graphId: string): void {
    const id = `${graphId}:${name}`;
    this.history.delete(id);
    this.emit('historyCleared', { name, graphId });
  }

  /**
   * 清除所有历史记录
   */
  public clearAllHistory(): void {
    this.history.clear();
    this.emit('allHistoryCleared');
  }

  /**
   * 按标签查找变量监视
   * @param tag 标签
   * @returns 变量监视列表
   */
  public findWatchesByTag(tag: string): VariableWatch[] {
    return this.getAllWatches().filter(watch => watch.tags && watch.tags.includes(tag));
  }

  /**
   * 搜索变量监视
   * @param query 搜索查询
   * @returns 变量监视列表
   */
  public searchWatches(query: string): VariableWatch[] {
    if (!query) {
      return this.getAllWatches();
    }

    const lowerQuery = query.toLowerCase();

    return this.getAllWatches().filter(watch => {
      // 匹配变量名
      if (watch.name.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      // 匹配描述
      if (watch.description && watch.description.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      // 匹配标签
      if (watch.tags && watch.tags.some(tag => tag.toLowerCase().includes(lowerQuery))) {
        return true;
      }

      // 匹配条件
      if (watch.condition && watch.condition.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      return false;
    });
  }

  /**
   * 导出变量监视
   * @returns 变量监视数据
   */
  public exportWatches(): string {
    return JSON.stringify(this.getAllWatches());
  }

  /**
   * 导入变量监视
   * @param data 变量监视数据
   * @returns 是否成功
   */
  public importWatches(data: string): boolean {
    try {
      const watches = JSON.parse(data) as VariableWatch[];

      // 清除现有监视
      this.clearAllWatches();

      // 导入新监视
      for (const watch of watches) {
        const id = `${watch.graphId}:${watch.name}`;
        this.watches.set(id, watch);
        this.emit('watchAdded', watch);
      }

      return true;
    } catch (error) {
      console.error('导入变量监视失败:', error);
      return false;
    }
  }

  /**
   * 格式化变量值
   * @param value 变量值
   * @param format 格式化表达式
   * @returns 格式化后的值
   */
  public formatValue(value: any, format?: string): string {
    if (!format) {
      // 默认格式化
      if (typeof value === 'object' && value !== null) {
        try {
          return JSON.stringify(value, null, 2);
        } catch (error) {
          return String(value);
        }
      } else {
        return String(value);
      }
    }

    try {
      // 使用格式化表达式
      const formatFunc = new Function('value', `return ${format};`);
      return formatFunc(value);
    } catch (error) {
      console.error(`格式化表达式错误: ${error}`);
      return String(value);
    }
  }
}
