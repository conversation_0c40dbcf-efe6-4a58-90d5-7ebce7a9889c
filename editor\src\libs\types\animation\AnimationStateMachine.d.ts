import { Animator } from './Animator';
/**
 * 动画状态接口
 */
export interface AnimationState {
    /** 状态名称 */
    name: string;
    /** 状态类型 */
    type: string;
    /** 状态数据 */
    [key: string]: any;
}
/**
 * 单一动画状态
 */
export interface SingleAnimationState extends AnimationState {
    /** 状态类型 */
    type: 'SingleAnimationState';
    /** 动画片段名称 */
    clipName: string;
    /** 是否循环 */
    loop: boolean;
    /** 播放完成后是否保持最后一帧 */
    clamp: boolean;
}
/**
 * 混合动画状态
 */
export interface BlendAnimationState extends AnimationState {
    /** 状态类型 */
    type: 'BlendAnimationState';
    /** 混合参数名称 */
    parameterName: string;
    /** 混合空间类型 */
    blendSpaceType: '1D' | '2D';
    /** 混合空间配置 */
    blendSpaceConfig: any;
}
/**
 * 状态转换规则
 */
export interface TransitionRule {
    /** 源状态名称 */
    from: string;
    /** 目标状态名称 */
    to: string;
    /** 转换条件 */
    condition: () => boolean;
    /** 条件表达式（用于序列化） */
    conditionExpression?: string;
    /** 转换持续时间（秒） */
    duration: number;
    /** 是否可以中断 */
    canInterrupt: boolean;
    /** 转换曲线类型 */
    curveType?: string;
    /** 优先级 */
    priority?: number;
}
/**
 * 动画状态机事件类型
 */
export declare enum AnimationStateMachineEventType {
    /** 状态进入 */
    STATE_ENTER = "stateEnter",
    /** 状态退出 */
    STATE_EXIT = "stateExit",
    /** 状态转换开始 */
    TRANSITION_START = "transitionStart",
    /** 状态转换结束 */
    TRANSITION_END = "transitionEnd"
}
/**
 * 参数元数据
 */
export interface ParameterMetadata {
    /** 最小值（仅适用于数值类型） */
    minValue?: number;
    /** 最大值（仅适用于数值类型） */
    maxValue?: number;
    /** 枚举值（仅适用于枚举类型） */
    enumValues?: string[];
    /** 描述 */
    description?: string;
}
/**
 * 动画状态机
 */
export declare class AnimationStateMachine {
    /** 状态映射 */
    private states;
    /** 转换规则列表 */
    private transitions;
    /** 当前状态 */
    private currentState;
    /** 上一个状态 */
    private previousState;
    /** 是否正在转换 */
    private isTransitioning;
    /** 当前转换规则 */
    private currentTransition;
    /** 转换开始时间 */
    private transitionStartTime;
    /** 转换持续时间 */
    private transitionDuration;
    /** 动画控制器 */
    private animator;
    /** 事件发射器 */
    private eventEmitter;
    /** 参数映射 */
    private parameters;
    /** 参数元数据映射 */
    private parameterMetadata;
    /** 调试模式 */
    private debugMode;
    /**
     * 构造函数
     * @param animator 动画控制器
     */
    constructor(animator: Animator);
    /**
     * 添加状态
     * @param state 动画状态
     */
    addState(state: AnimationState): void;
    /**
     * 添加转换规则
     * @param rule 转换规则
     */
    addTransition(rule: TransitionRule): void;
    /**
     * 设置参数
     * @param name 参数名称
     * @param value 参数值
     */
    setParameter(name: string, value: any): void;
    /**
     * 获取参数
     * @param name 参数名称
     * @returns 参数值
     */
    getParameter(name: string): any;
    /**
     * 设置当前状态
     * @param stateName 状态名称
     */
    setCurrentState(stateName: string): void;
    /**
     * 更新状态机
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 开始转换
     * @param transition 转换规则
     */
    private startTransition;
    /**
     * 进入状态
     * @param state 动画状态
     */
    private enterState;
    /**
     * 更新状态
     * @param state 动画状态
     * @param deltaTime 时间增量（秒）
     */
    private updateState;
    /**
     * 更新混合状态
     * @param state 混合动画状态
     */
    private updateBlendState;
    /**
     * 混合到状态
     * @param state 目标状态
     * @param duration 混合持续时间（秒）
     */
    private blendToState;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    addEventListener(type: AnimationStateMachineEventType, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    removeEventListener(type: AnimationStateMachineEventType, callback: (data: any) => void): void;
    /**
     * 获取所有状态
     * @returns 状态数组
     */
    getStates(): AnimationState[];
    /**
     * 获取状态
     * @param name 状态名称
     * @returns 状态，如果不存在则返回null
     */
    getState(name: string): AnimationState | null;
    /**
     * 移除状态
     * @param name 状态名称
     * @returns 是否成功移除
     */
    removeState(name: string): boolean;
    /**
     * 获取所有转换规则
     * @returns 转换规则数组
     */
    getTransitions(): TransitionRule[];
    /**
     * 获取转换规则
     * @param fromState 源状态名称
     * @param toState 目标状态名称
     * @returns 转换规则，如果不存在则返回null
     */
    getTransition(fromState: string, toState: string): TransitionRule | null;
    /**
     * 移除转换规则
     * @param fromState 源状态名称
     * @param toState 目标状态名称
     * @returns 是否成功移除
     */
    removeTransition(fromState: string, toState: string): boolean;
    /**
     * 获取当前状态
     * @returns 当前状态，如果没有则返回null
     */
    getCurrentState(): AnimationState | null;
    /**
     * 获取上一个状态
     * @returns 上一个状态，如果没有则返回null
     */
    getPreviousState(): AnimationState | null;
    /**
     * 获取所有参数
     * @returns 参数映射
     */
    getParameters(): Map<string, any>;
    /**
     * 移除参数
     * @param name 参数名称
     * @returns 是否成功移除
     */
    removeParameter(name: string): boolean;
    /**
     * 设置参数元数据
     * @param name 参数名称
     * @param metadata 参数元数据
     */
    setParameterMetadata(name: string, metadata: ParameterMetadata): void;
    /**
     * 获取参数元数据
     * @param name 参数名称
     * @returns 参数元数据，如果不存在则返回null
     */
    getParameterMetadata(name: string): ParameterMetadata | null;
    /**
     * 设置调试模式
     * @param enabled 是否启用
     */
    setDebugMode(enabled: boolean): void;
    /**
     * 获取调试模式
     * @returns 是否启用调试模式
     */
    isDebugMode(): boolean;
    /**
     * 获取动画控制器
     * @returns 动画控制器
     */
    getAnimator(): Animator;
    /**
     * 重置状态机
     */
    reset(): void;
    /**
     * 清空状态机
     */
    clear(): void;
}
