/**
 * 输入映射
 * 用于将输入设备的原始输入映射到逻辑输入
 */
import { InputDevice } from './InputDevice';

/**
 * 输入映射类型
 */
export enum InputMappingType {
  /** 按钮映射 */
  BUTTON = 'button',
  /** 轴映射 */
  AXIS = 'axis',
  /** 向量映射 */
  VECTOR = 'vector',
  /** 组合映射 */
  COMPOSITE = 'composite'
}

/**
 * 输入映射接口
 */
export interface InputMapping {
  /**
   * 获取映射名称
   * @returns 映射名称
   */
  getName(): string;

  /**
   * 获取映射类型
   * @returns 映射类型
   */
  getType(): InputMappingType;

  /**
   * 获取设备名称
   * @returns 设备名称
   */
  getDeviceName(): string;

  /**
   * 评估映射
   * @param device 输入设备
   * @returns 映射值
   */
  evaluate(device: InputDevice): any;
}

/**
 * 输入映射基类
 */
export abstract class BaseInputMapping implements InputMapping {
  /** 映射名称 */
  protected name: string;

  /** 映射类型 */
  protected type: InputMappingType;

  /** 设备名称 */
  protected deviceName: string;

  /**
   * 创建输入映射
   * @param name 映射名称
   * @param type 映射类型
   * @param deviceName 设备名称
   */
  constructor(name: string, type: InputMappingType, deviceName: string) {
    this.name = name;
    this.type = type;
    this.deviceName = deviceName;
  }

  /**
   * 获取映射名称
   * @returns 映射名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 获取映射类型
   * @returns 映射类型
   */
  public getType(): InputMappingType {
    return this.type;
  }

  /**
   * 获取设备名称
   * @returns 设备名称
   */
  public getDeviceName(): string {
    return this.deviceName;
  }

  /**
   * 评估映射
   * @param device 输入设备
   * @returns 映射值
   */
  public abstract evaluate(device: InputDevice): any;
}

/**
 * 按钮映射
 */
export class ButtonInputMapping extends BaseInputMapping {
  /** 按钮键名 */
  private buttonKey: string;

  /**
   * 创建按钮映射
   * @param name 映射名称
   * @param deviceName 设备名称
   * @param buttonKey 按钮键名
   */
  constructor(name: string, deviceName: string, buttonKey: string) {
    super(name, InputMappingType.BUTTON, deviceName);
    this.buttonKey = buttonKey;
  }

  /**
   * 评估映射
   * @param device 输入设备
   * @returns 按钮状态
   */
  public evaluate(device: InputDevice): boolean {
    return !!device.getValue(this.buttonKey);
  }
}

/**
 * 轴映射
 */
export class AxisInputMapping extends BaseInputMapping {
  /** 轴键名 */
  private axisKey: string;

  /** 轴缩放 */
  private scale: number;

  /** 轴偏移 */
  private offset: number;

  /** 轴死区 */
  private deadZone: number;

  /**
   * 创建轴映射
   * @param name 映射名称
   * @param deviceName 设备名称
   * @param axisKey 轴键名
   * @param scale 轴缩放
   * @param offset 轴偏移
   * @param deadZone 轴死区
   */
  constructor(name: string, deviceName: string, axisKey: string, scale: number = 1, offset: number = 0, deadZone: number = 0.1) {
    super(name, InputMappingType.AXIS, deviceName);
    this.axisKey = axisKey;
    this.scale = scale;
    this.offset = offset;
    this.deadZone = deadZone;
  }

  /**
   * 评估映射
   * @param device 输入设备
   * @returns 轴值
   */
  public evaluate(device: InputDevice): number {
    const value = device.getValue(this.axisKey) || 0;
    
    // 应用死区
    const absValue = Math.abs(value);
    if (absValue < this.deadZone) {
      return 0;
    }

    // 应用缩放和偏移
    const normalizedValue = (absValue - this.deadZone) / (1 - this.deadZone);
    return (value > 0 ? normalizedValue : -normalizedValue) * this.scale + this.offset;
  }
}

/**
 * 向量映射
 */
export class VectorInputMapping extends BaseInputMapping {
  /** X轴键名 */
  private xAxisKey: string;

  /** Y轴键名 */
  private yAxisKey: string;

  /** 轴缩放 */
  private scale: number;

  /** 轴死区 */
  private deadZone: number;

  /**
   * 创建向量映射
   * @param name 映射名称
   * @param deviceName 设备名称
   * @param xAxisKey X轴键名
   * @param yAxisKey Y轴键名
   * @param scale 轴缩放
   * @param deadZone 轴死区
   */
  constructor(name: string, deviceName: string, xAxisKey: string, yAxisKey: string, scale: number = 1, deadZone: number = 0.1) {
    super(name, InputMappingType.VECTOR, deviceName);
    this.xAxisKey = xAxisKey;
    this.yAxisKey = yAxisKey;
    this.scale = scale;
    this.deadZone = deadZone;
  }

  /**
   * 评估映射
   * @param device 输入设备
   * @returns 向量值 [x, y]
   */
  public evaluate(device: InputDevice): [number, number] {
    const x = device.getValue(this.xAxisKey) || 0;
    const y = device.getValue(this.yAxisKey) || 0;
    
    // 计算向量长度
    const length = Math.sqrt(x * x + y * y);
    
    // 应用死区
    if (length < this.deadZone) {
      return [0, 0];
    }
    
    // 应用缩放
    const normalizedLength = (length - this.deadZone) / (1 - this.deadZone);
    const scale = normalizedLength / length * this.scale;
    
    return [x * scale, y * scale];
  }
}
