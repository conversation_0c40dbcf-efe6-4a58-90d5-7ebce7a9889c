/**
 * UISystem.ts
 *
 * UI系统，管理所有UI元素
 */
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { UIComponent } from '../components/UIComponent';
import { UIAnimationComponent } from '../components/UIAnimationComponent';
import { UILayoutComponent } from '../components/UILayoutComponent';
import { UIEventComponent } from '../components/UIEventComponent';
/**
 * UI系统配置
 */
export interface UISystemConfig {
    debug?: boolean;
    autoCreateContainer?: boolean;
    containerId?: string;
    enableEvents?: boolean;
    enableLayouts?: boolean;
    enableAnimations?: boolean;
}
/**
 * UI系统
 * 管理所有UI元素
 */
export declare class UISystem extends System {
    private uiComponents;
    private ui2DComponents;
    private ui3DComponents;
    private uiAnimationComponents;
    private uiLayoutComponents;
    private uiEventComponents;
    private container?;
    private config;
    private mousePosition;
    private keyStates;
    private modifiers;
    private performanceMetrics;
    private optimizationSettings;
    /**
     * 构造函数
     * @param world 世界实例
     * @param config UI系统配置
     */
    constructor(config?: UISystemConfig);
    /**
     * 创建HTML容器
     */
    private createContainer;
    /**
     * 设置事件监听器
     */
    private setupEventListeners;
    /**
     * 更新修饰键状态
     * @param event 键盘事件
     */
    private updateModifiers;
    /**
     * 处理鼠标移动事件
     * @param event 鼠标事件
     */
    private handleMouseMove;
    /**
     * 处理鼠标按下事件
     * @param event 鼠标事件
     */
    private handleMouseDown;
    /**
     * 处理鼠标释放事件
     * @param event 鼠标事件
     */
    private handleMouseUp;
    /**
     * 处理键盘按下事件
     * @param event 键盘事件
     */
    private handleKeyDown;
    /**
     * 处理键盘释放事件
     * @param event 键盘事件
     */
    private handleKeyUp;
    /**
     * 处理窗口大小改变事件
     */
    private handleResize;
    /**
     * 注册UI组件
     * @param entity 实体
     * @param component UI组件
     */
    registerUIComponent(entity: Entity, component: UIComponent): void;
    /**
     * 注册UI动画组件
     * @param entity 实体
     * @param component UI动画组件
     */
    registerUIAnimationComponent(entity: Entity, component: UIAnimationComponent): void;
    /**
     * 注册UI布局组件
     * @param entity 实体
     * @param component UI布局组件
     */
    registerUILayoutComponent(entity: Entity, component: UILayoutComponent): void;
    /**
     * 注册UI事件组件
     * @param entity 实体
     * @param component UI事件组件
     */
    registerUIEventComponent(entity: Entity, component: UIEventComponent): void;
    /**
     * 注销UI组件
     * @param entity 实体
     */
    unregisterUIComponent(entity: Entity): void;
    /**
     * 注销UI动画组件
     * @param entity 实体
     */
    unregisterUIAnimationComponent(entity: Entity): void;
    /**
     * 注销UI布局组件
     * @param entity 实体
     */
    unregisterUILayoutComponent(entity: Entity): void;
    /**
     * 注销UI事件组件
     * @param entity 实体
     */
    unregisterUIEventComponent(entity: Entity): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 渲染系统
     */
    render(): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 获取所有UI组件信息
     * @returns UI组件信息列表
     */
    getComponents(): any[];
    /**
     * 获取优化设置
     * @returns 优化设置
     */
    getOptimizationSettings(): any;
    /**
     * 更新优化设置
     * @param settings 新的优化设置
     */
    updateOptimizationSettings(settings: any): void;
    /**
     * 优化指定组件
     * @param componentIds 组件ID列表
     * @param options 优化选项
     * @returns 优化结果
     */
    optimizeComponents(componentIds: string[], options: any): Promise<any>;
    /**
     * 优化所有组件
     * @param options 优化选项
     * @returns 优化结果
     */
    optimizeAllComponents(options: any): Promise<any>;
    /**
     * 获取事件监听器数量
     * @param eventComponent 事件组件
     * @returns 事件监听器数量
     */
    private getEventListenerCount;
    /**
     * 估算组件内存使用量
     * @param component UI组件
     * @returns 内存使用量（字节）
     */
    private estimateComponentMemoryUsage;
    /**
     * 计算组件深度
     * @param component UI组件
     * @returns 组件深度
     */
    private calculateComponentDepth;
    /**
     * 根据ID查找实体
     * @param id 实体ID
     * @returns 实体或undefined
     */
    private findEntityById;
    /**
     * 测量组件渲染时间
     * @param component UI组件
     * @returns 渲染时间（毫秒）
     */
    private measureComponentRenderTime;
    /**
     * 优化单个组件
     * @param component UI组件
     * @param options 优化选项
     */
    private optimizeComponent;
    /**
     * 判断组件是否应该虚拟化
     * @param component UI组件
     * @returns 是否应该虚拟化
     */
    private shouldVirtualize;
    /**
     * 判断组件是否应该懒加载
     * @param component UI组件
     * @returns 是否应该懒加载
     */
    private shouldLazyLoad;
    /**
     * 判断组件是否应该剔除
     * @param component UI组件
     * @returns 是否应该剔除
     */
    private shouldCull;
    /**
     * 判断组件是否在视野外
     * @param component UI组件
     * @returns 是否在视野外
     */
    private isComponentOutOfView;
    /**
     * 优化事件监听器
     * @param componentIds 组件ID列表
     * @returns 优化结果
     */
    private optimizeEventListeners;
    /**
     * 应用优化设置
     */
    private applyOptimizationSettings;
    /**
     * 启用批量渲染
     */
    private enableBatchRendering;
    /**
     * 启用事件委托
     */
    private enableEventDelegation;
}
