import { System } from '../../core/System';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { DigitalHumanComponent, ClothingSlotType, ClothingSlot } from '../components/DigitalHumanComponent';
import { EventEmitter } from '../../utils/EventEmitter';
import * as THREE from 'three';

/**
 * 服装类别
 */
export enum ClothingCategory {
  CASUAL = 'casual',
  FORMAL = 'formal',
  SPORTS = 'sports',
  TRADITIONAL = 'traditional',
  FANTASY = 'fantasy',
  UNIFORM = 'uniform',
  SEASONAL = 'seasonal'
}

/**
 * 服装材质类型
 */
export enum ClothingMaterialType {
  COTTON = 'cotton',
  SILK = 'silk',
  LEATHER = 'leather',
  DENIM = 'denim',
  WOOL = 'wool',
  SYNTHETIC = 'synthetic',
  METAL = 'metal',
  PLASTIC = 'plastic'
}

/**
 * 服装物理属性
 */
export interface ClothingPhysics {
  /** 是否启用物理模拟 */
  enabled: boolean;
  /** 质量 */
  mass: number;
  /** 刚度 */
  stiffness: number;
  /** 阻尼 */
  damping: number;
  /** 弹性 */
  elasticity: number;
  /** 摩擦力 */
  friction: number;
  /** 风阻 */
  airResistance: number;
  /** 重力影响 */
  gravityScale: number;
}

/**
 * 服装适配参数
 */
export interface ClothingFittingParams {
  /** 缩放 */
  scale: THREE.Vector3;
  /** 偏移 */
  offset: THREE.Vector3;
  /** 旋转 */
  rotation: THREE.Euler;
  /** 骨骼权重调整 */
  boneWeights?: Map<string, number>;
  /** 变形目标权重 */
  morphTargetWeights?: Map<string, number>;
}

/**
 * 服装项
 */
export interface ClothingItem {
  /** 服装ID */
  id: string;
  /** 服装名称 */
  name: string;
  /** 服装类别 */
  category: ClothingCategory;
  /** 插槽类型 */
  slotType: ClothingSlotType;
  /** 几何体URL */
  geometryUrl: string;
  /** 纹理URLs */
  textureUrls: Map<string, string>;
  /** 材质类型 */
  materialType: ClothingMaterialType;
  /** 材质属性 */
  materialProperties: Record<string, any>;
  /** 物理属性 */
  physics: ClothingPhysics;
  /** 适配参数 */
  fittingParams: ClothingFittingParams;
  /** 兼容的身体类型 */
  compatibleBodyTypes: string[];
  /** 价格 */
  price?: number;
  /** 标签 */
  tags: string[];
  /** 创建者 */
  creator?: string;
  /** 版本 */
  version: string;
}

/**
 * 适配后的服装
 */
export interface FittedClothing {
  /** 原始服装项 */
  item: ClothingItem;
  /** 适配后的几何体 */
  geometry: THREE.BufferGeometry;
  /** 适配后的材质 */
  material: THREE.Material;
  /** 适配后的网格 */
  mesh: THREE.Mesh;
  /** 适配质量评分 */
  fittingScore: number;
  /** 适配参数 */
  appliedParams: ClothingFittingParams;
}

/**
 * 服装组合
 */
export interface ClothingOutfit {
  /** 组合ID */
  id: string;
  /** 组合名称 */
  name: string;
  /** 服装项映射 */
  items: Map<ClothingSlotType, string>;
  /** 组合标签 */
  tags: string[];
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 换装系统配置
 */
export interface ClothingSystemConfig {
  /** 是否启用物理模拟 */
  enablePhysics: boolean;
  /** 是否启用自动适配 */
  enableAutoFitting: boolean;
  /** 是否启用碰撞检测 */
  enableCollisionDetection: boolean;
  /** 适配质量阈值 */
  fittingQualityThreshold: number;
  /** 最大服装数量 */
  maxClothingItems: number;
  /** 是否启用调试 */
  debug: boolean;
}

/**
 * 高级换装系统
 * 支持多类型服装、自动适配、物理模拟和材质系统
 */
export class ClothingSystem extends System {
  /** 系统类型 */
  public static readonly TYPE = 'ClothingSystem';

  /** 系统优先级 */
  public static readonly PRIORITY = 50;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 系统配置 */
  private config: ClothingSystemConfig;

  /** 服装分类管理 */
  private clothingCategories: Map<ClothingCategory, ClothingItem[]>;

  /** 服装库 */
  private clothingLibrary: Map<string, ClothingItem>;

  /** 服装组合库 */
  private outfitLibrary: Map<string, ClothingOutfit>;

  /** 当前装备的服装 */
  private equippedClothing: Map<string, Map<ClothingSlotType, FittedClothing>>;

  /** 物理世界 */
  private physicsWorld?: any;

  /** 材质缓存 */
  private materialCache: Map<string, THREE.Material>;

  /** 几何体缓存 */
  private geometryCache: Map<string, THREE.BufferGeometry>;

  /**
   * 构造函数
   * @param world 世界实例
   * @param config 系统配置
   */
  constructor(world: World, config: Partial<ClothingSystemConfig> = {}) {
    super(ClothingSystem.PRIORITY);

    this.eventEmitter = new EventEmitter();
    this.config = {
      enablePhysics: true,
      enableAutoFitting: true,
      enableCollisionDetection: true,
      fittingQualityThreshold: 0.8,
      maxClothingItems: 1000,
      debug: false,
      ...config
    };

    this.clothingCategories = new Map();
    this.clothingLibrary = new Map();
    this.outfitLibrary = new Map();
    this.equippedClothing = new Map();
    this.materialCache = new Map();
    this.geometryCache = new Map();

    this.initializeDefaultCategories();
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return ClothingSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.config.debug) {
      console.log('ClothingSystem: 初始化换装系统');
    }

    // 初始化物理世界（如果启用）
    if (this.config.enablePhysics) {
      this.initializePhysicsWorld();
    }

    // 监听数字人组件添加事件
    this.world.on('componentAdded', this.onComponentAdded.bind(this));
    this.world.on('componentRemoved', this.onComponentRemoved.bind(this));
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    // 更新物理模拟
    if (this.config.enablePhysics && this.physicsWorld) {
      this.updatePhysicsSimulation(deltaTime);
    }

    // 更新装备的服装
    this.updateEquippedClothing(deltaTime);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清理资源
    this.materialCache.clear();
    this.geometryCache.clear();
    this.equippedClothing.clear();

    // 移除事件监听器
    this.world.off('componentAdded', this.onComponentAdded.bind(this));
    this.world.off('componentRemoved', this.onComponentRemoved.bind(this));

    if (this.config.debug) {
      console.log('ClothingSystem: 系统已销毁');
    }
  }

  /**
   * 初始化默认服装类别
   */
  private initializeDefaultCategories(): void {
    for (const category of Object.values(ClothingCategory)) {
      this.clothingCategories.set(category, []);
    }
  }

  /**
   * 初始化物理世界
   */
  private initializePhysicsWorld(): void {
    // 这里可以集成物理引擎，如Cannon.js或Ammo.js
    // 暂时使用简化的物理模拟
    this.physicsWorld = {
      gravity: new THREE.Vector3(0, -9.81, 0),
      objects: []
    };
  }

  /**
   * 组件添加事件处理
   * @param entity 实体
   * @param component 组件
   */
  private onComponentAdded(entity: Entity, component: any): void {
    if (component instanceof DigitalHumanComponent) {
      this.equippedClothing.set(entity.id, new Map());
      
      if (this.config.debug) {
        console.log(`ClothingSystem: 为数字人 ${entity.id} 初始化换装系统`);
      }
    }
  }

  /**
   * 组件移除事件处理
   * @param entity 实体
   * @param component 组件
   */
  private onComponentRemoved(entity: Entity, component: any): void {
    if (component instanceof DigitalHumanComponent) {
      this.equippedClothing.delete(entity.id);
      
      if (this.config.debug) {
        console.log(`ClothingSystem: 清理数字人 ${entity.id} 的换装数据`);
      }
    }
  }

  /**
   * 更新物理模拟
   * @param deltaTime 时间增量
   */
  private updatePhysicsSimulation(deltaTime: number): void {
    // 简化的物理模拟更新
    // 实际实现需要集成专业的物理引擎
    void deltaTime; // 避免未使用参数警告
  }

  /**
   * 更新装备的服装
   * @param deltaTime 时间增量
   */
  private updateEquippedClothing(deltaTime: number): void {
    // 更新所有装备的服装状态
    for (const [entityId, clothingMap] of this.equippedClothing) {
      for (const [slotType, fittedClothing] of clothingMap) {
        this.updateClothingAnimation(fittedClothing, deltaTime);
      }
    }
  }

  /**
   * 更新服装动画
   * @param fittedClothing 适配的服装
   * @param deltaTime 时间增量
   */
  private updateClothingAnimation(fittedClothing: FittedClothing, deltaTime: number): void {
    // 更新服装的动画状态
    // 这里可以实现布料物理、风效果等
    void fittedClothing;
    void deltaTime;
  }

  // ==================== 公共API方法 ====================

  /**
   * 添加服装项到库中
   * @param clothingItem 服装项
   */
  public addClothingItem(clothingItem: ClothingItem): void {
    // 检查是否超过最大数量限制
    if (this.clothingLibrary.size >= this.config.maxClothingItems) {
      throw new Error(`服装库已达到最大容量: ${this.config.maxClothingItems}`);
    }

    // 添加到服装库
    this.clothingLibrary.set(clothingItem.id, clothingItem);

    // 添加到分类
    const categoryItems = this.clothingCategories.get(clothingItem.category) || [];
    categoryItems.push(clothingItem);
    this.clothingCategories.set(clothingItem.category, categoryItems);

    // 触发事件
    this.eventEmitter.emit('clothingItemAdded', clothingItem);

    if (this.config.debug) {
      console.log(`ClothingSystem: 添加服装项 ${clothingItem.name} (${clothingItem.id})`);
    }
  }

  /**
   * 移除服装项
   * @param itemId 服装项ID
   */
  public removeClothingItem(itemId: string): boolean {
    const item = this.clothingLibrary.get(itemId);
    if (!item) {
      return false;
    }

    // 从服装库移除
    this.clothingLibrary.delete(itemId);

    // 从分类移除
    const categoryItems = this.clothingCategories.get(item.category) || [];
    const index = categoryItems.findIndex(i => i.id === itemId);
    if (index !== -1) {
      categoryItems.splice(index, 1);
    }

    // 触发事件
    this.eventEmitter.emit('clothingItemRemoved', item);

    if (this.config.debug) {
      console.log(`ClothingSystem: 移除服装项 ${item.name} (${itemId})`);
    }

    return true;
  }

  /**
   * 获取服装项
   * @param itemId 服装项ID
   * @returns 服装项
   */
  public getClothingItem(itemId: string): ClothingItem | undefined {
    return this.clothingLibrary.get(itemId);
  }

  /**
   * 按类别获取服装项
   * @param category 服装类别
   * @returns 服装项列表
   */
  public getClothingItemsByCategory(category: ClothingCategory): ClothingItem[] {
    return this.clothingCategories.get(category) || [];
  }

  /**
   * 搜索服装项
   * @param query 搜索条件
   * @returns 匹配的服装项
   */
  public searchClothingItems(query: {
    category?: ClothingCategory;
    slotType?: ClothingSlotType;
    materialType?: ClothingMaterialType;
    tags?: string[];
    priceRange?: [number, number];
    name?: string;
  }): ClothingItem[] {
    const results: ClothingItem[] = [];

    for (const item of this.clothingLibrary.values()) {
      let matches = true;

      // 检查类别
      if (query.category && item.category !== query.category) {
        matches = false;
      }

      // 检查插槽类型
      if (query.slotType && item.slotType !== query.slotType) {
        matches = false;
      }

      // 检查材质类型
      if (query.materialType && item.materialType !== query.materialType) {
        matches = false;
      }

      // 检查标签
      if (query.tags && query.tags.length > 0) {
        const hasAllTags = query.tags.every(tag => item.tags.includes(tag));
        if (!hasAllTags) {
          matches = false;
        }
      }

      // 检查价格范围
      if (query.priceRange && item.price !== undefined) {
        const [minPrice, maxPrice] = query.priceRange;
        if (item.price < minPrice || item.price > maxPrice) {
          matches = false;
        }
      }

      // 检查名称
      if (query.name && !item.name.toLowerCase().includes(query.name.toLowerCase())) {
        matches = false;
      }

      if (matches) {
        results.push(item);
      }
    }

    return results;
  }

  /**
   * 为数字人装备服装
   * @param entityId 实体ID
   * @param itemId 服装项ID
   * @returns 是否成功装备
   */
  public async equipClothing(entityId: string, itemId: string): Promise<boolean> {
    const entity = this.world.getEntity(entityId);
    if (!entity) {
      throw new Error(`实体不存在: ${entityId}`);
    }

    const digitalHuman = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHuman) {
      throw new Error(`实体不是数字人: ${entityId}`);
    }

    const clothingItem = this.clothingLibrary.get(itemId);
    if (!clothingItem) {
      throw new Error(`服装项不存在: ${itemId}`);
    }

    try {
      // 适配服装到数字人身体
      const fittedClothing = await this.fitClothingToBody(clothingItem, digitalHuman);

      // 检查适配质量
      if (fittedClothing.fittingScore < this.config.fittingQualityThreshold) {
        console.warn(`服装适配质量较低: ${fittedClothing.fittingScore}`);
      }

      // 装备服装
      const entityClothing = this.equippedClothing.get(entityId) || new Map();

      // 移除同插槽的现有服装
      const existingClothing = entityClothing.get(clothingItem.slotType);
      if (existingClothing) {
        this.unequipClothing(entityId, clothingItem.slotType);
      }

      // 装备新服装
      entityClothing.set(clothingItem.slotType, fittedClothing);
      this.equippedClothing.set(entityId, entityClothing);

      // 更新数字人组件
      digitalHuman.setClothingItem(
        clothingItem.slotType,
        itemId,
        clothingItem.geometryUrl,
        clothingItem.materialProperties
      );

      // 触发事件
      this.eventEmitter.emit('clothingEquipped', entityId, clothingItem, fittedClothing);

      if (this.config.debug) {
        console.log(`ClothingSystem: 为数字人 ${entityId} 装备服装 ${clothingItem.name}`);
      }

      return true;
    } catch (error) {
      console.error('装备服装失败:', error);
      return false;
    }
  }

  /**
   * 卸下服装
   * @param entityId 实体ID
   * @param slotType 插槽类型
   * @returns 是否成功卸下
   */
  public unequipClothing(entityId: string, slotType: ClothingSlotType): boolean {
    const entityClothing = this.equippedClothing.get(entityId);
    if (!entityClothing) {
      return false;
    }

    const fittedClothing = entityClothing.get(slotType);
    if (!fittedClothing) {
      return false;
    }

    // 移除服装
    entityClothing.delete(slotType);

    // 更新数字人组件
    const entity = this.world.getEntity(entityId);
    if (entity) {
      const digitalHuman = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
      if (digitalHuman) {
        digitalHuman.removeClothingItem(slotType);
      }
    }

    // 清理资源
    fittedClothing.geometry.dispose();
    if (fittedClothing.material instanceof THREE.Material) {
      fittedClothing.material.dispose();
    }

    // 触发事件
    this.eventEmitter.emit('clothingUnequipped', entityId, slotType, fittedClothing);

    if (this.config.debug) {
      console.log(`ClothingSystem: 为数字人 ${entityId} 卸下 ${slotType} 插槽的服装`);
    }

    return true;
  }

  // ==================== 服装适配算法 ====================

  /**
   * 将服装适配到数字人身体
   * @param clothingItem 服装项
   * @param digitalHuman 数字人组件
   * @returns 适配后的服装
   */
  public async fitClothingToBody(
    clothingItem: ClothingItem,
    digitalHuman: DigitalHumanComponent
  ): Promise<FittedClothing> {
    // 加载服装几何体
    const geometry = await this.loadGeometry(clothingItem.geometryUrl);

    // 创建材质
    const material = await this.createMaterial(clothingItem);

    // 计算适配参数
    const fittingParams = this.calculateFittingParams(clothingItem, digitalHuman);

    // 应用适配变换
    const fittedGeometry = this.applyFittingTransform(geometry, fittingParams);

    // 创建网格
    const mesh = new THREE.Mesh(fittedGeometry, material);

    // 计算适配质量评分
    const fittingScore = this.calculateFittingScore(clothingItem, digitalHuman, fittingParams);

    return {
      item: clothingItem,
      geometry: fittedGeometry,
      material,
      mesh,
      fittingScore,
      appliedParams: fittingParams
    };
  }

  /**
   * 计算适配参数
   * @param clothingItem 服装项
   * @param digitalHuman 数字人组件
   * @returns 适配参数
   */
  private calculateFittingParams(
    clothingItem: ClothingItem,
    digitalHuman: DigitalHumanComponent
  ): ClothingFittingParams {
    const bodyMorphTargets = digitalHuman.bodyMorphTargets;

    // 基础适配参数
    let scale = clothingItem.fittingParams.scale.clone();
    let offset = clothingItem.fittingParams.offset.clone();
    let rotation = clothingItem.fittingParams.rotation.clone();

    // 根据身体变形目标调整缩放
    scale.x *= (1 + bodyMorphTargets.shoulders * 0.1);
    scale.y *= (1 + bodyMorphTargets.height * 0.1);
    scale.z *= (1 + bodyMorphTargets.weight * 0.05);

    // 根据插槽类型进行特殊调整
    switch (clothingItem.slotType) {
      case ClothingSlotType.UPPER_BODY:
        scale.x *= (1 + bodyMorphTargets.chest * 0.15);
        scale.z *= (1 + bodyMorphTargets.muscle * 0.1);
        break;
      case ClothingSlotType.LOWER_BODY:
        scale.x *= (1 + bodyMorphTargets.hips * 0.12);
        scale.z *= (1 + bodyMorphTargets.waist * 0.08);
        break;
      case ClothingSlotType.FEET:
        // 脚部服装主要受身高影响
        scale.multiplyScalar(1 + bodyMorphTargets.height * 0.05);
        break;
    }

    return {
      scale,
      offset,
      rotation,
      boneWeights: clothingItem.fittingParams.boneWeights,
      morphTargetWeights: clothingItem.fittingParams.morphTargetWeights
    };
  }

  /**
   * 应用适配变换
   * @param geometry 原始几何体
   * @param params 适配参数
   * @returns 变换后的几何体
   */
  private applyFittingTransform(
    geometry: THREE.BufferGeometry,
    params: ClothingFittingParams
  ): THREE.BufferGeometry {
    const fittedGeometry = geometry.clone();

    // 应用缩放
    fittedGeometry.scale(params.scale.x, params.scale.y, params.scale.z);

    // 应用旋转
    fittedGeometry.rotateX(params.rotation.x);
    fittedGeometry.rotateY(params.rotation.y);
    fittedGeometry.rotateZ(params.rotation.z);

    // 应用偏移
    fittedGeometry.translate(params.offset.x, params.offset.y, params.offset.z);

    // 重新计算法线和边界框
    fittedGeometry.computeVertexNormals();
    fittedGeometry.computeBoundingBox();

    return fittedGeometry;
  }

  /**
   * 计算适配质量评分
   * @param clothingItem 服装项
   * @param digitalHuman 数字人组件
   * @param params 适配参数
   * @returns 质量评分 (0-1)
   */
  private calculateFittingScore(
    clothingItem: ClothingItem,
    digitalHuman: DigitalHumanComponent,
    params: ClothingFittingParams
  ): number {
    let score = 1.0;

    // 检查身体类型兼容性
    const bodyType = this.getBodyType(digitalHuman);
    if (!clothingItem.compatibleBodyTypes.includes(bodyType)) {
      score *= 0.7; // 不兼容的身体类型降低评分
    }

    // 检查缩放程度
    const scaleDeviation = Math.abs(params.scale.length() - Math.sqrt(3));
    score *= Math.max(0.5, 1 - scaleDeviation * 0.2);

    // 检查材质兼容性
    const materialCompatibility = this.checkMaterialCompatibility(clothingItem, digitalHuman);
    score *= materialCompatibility;

    return Math.max(0, Math.min(1, score));
  }

  /**
   * 获取身体类型
   * @param digitalHuman 数字人组件
   * @returns 身体类型
   */
  private getBodyType(digitalHuman: DigitalHumanComponent): string {
    const morphTargets = digitalHuman.bodyMorphTargets;

    // 简化的身体类型分类
    if (morphTargets.muscle > 0.5) {
      return 'muscular';
    } else if (morphTargets.weight > 0.3) {
      return 'heavy';
    } else if (morphTargets.height > 0.3) {
      return 'tall';
    } else if (morphTargets.height < -0.3) {
      return 'short';
    } else {
      return 'average';
    }
  }

  /**
   * 检查材质兼容性
   * @param clothingItem 服装项
   * @param digitalHuman 数字人组件
   * @returns 兼容性评分 (0-1)
   */
  private checkMaterialCompatibility(
    clothingItem: ClothingItem,
    digitalHuman: DigitalHumanComponent
  ): number {
    // 简化的材质兼容性检查
    // 实际实现可以考虑肤色、风格等因素
    void digitalHuman; // 避免未使用参数警告

    // 基于材质类型的基础兼容性
    switch (clothingItem.materialType) {
      case ClothingMaterialType.COTTON:
      case ClothingMaterialType.SYNTHETIC:
        return 1.0; // 通用材质
      case ClothingMaterialType.SILK:
      case ClothingMaterialType.WOOL:
        return 0.9; // 高质量材质
      case ClothingMaterialType.LEATHER:
      case ClothingMaterialType.DENIM:
        return 0.8; // 特殊材质
      case ClothingMaterialType.METAL:
      case ClothingMaterialType.PLASTIC:
        return 0.7; // 装饰性材质
      default:
        return 0.8;
    }
  }

  // ==================== 资源加载和管理 ====================

  /**
   * 加载几何体
   * @param url 几何体URL
   * @returns 几何体
   */
  private async loadGeometry(url: string): Promise<THREE.BufferGeometry> {
    // 检查缓存
    if (this.geometryCache.has(url)) {
      return this.geometryCache.get(url)!.clone();
    }

    // 加载几何体
    // 这里应该使用实际的加载器，如GLTFLoader
    const geometry = new THREE.BoxGeometry(1, 1, 1); // 占位符

    // 缓存几何体
    this.geometryCache.set(url, geometry);

    return geometry.clone();
  }

  /**
   * 创建材质
   * @param clothingItem 服装项
   * @returns 材质
   */
  private async createMaterial(clothingItem: ClothingItem): Promise<THREE.Material> {
    const materialId = `${clothingItem.id}_${clothingItem.materialType}`;

    // 检查缓存
    if (this.materialCache.has(materialId)) {
      return this.materialCache.get(materialId)!.clone();
    }

    // 创建材质
    const material = this.createMaterialByType(clothingItem);

    // 加载纹理
    await this.loadTextures(material, clothingItem.textureUrls);

    // 缓存材质
    this.materialCache.set(materialId, material);

    return material;
  }

  /**
   * 根据类型创建材质
   * @param clothingItem 服装项
   * @returns 材质
   */
  private createMaterialByType(clothingItem: ClothingItem): THREE.Material {
    const props = clothingItem.materialProperties;

    switch (clothingItem.materialType) {
      case ClothingMaterialType.COTTON:
      case ClothingMaterialType.SYNTHETIC:
        return new THREE.MeshStandardMaterial({
          color: props.color || 0xffffff,
          roughness: props.roughness || 0.8,
          metalness: props.metalness || 0.0
        });

      case ClothingMaterialType.SILK:
        return new THREE.MeshStandardMaterial({
          color: props.color || 0xffffff,
          roughness: props.roughness || 0.2,
          metalness: props.metalness || 0.1
        });

      case ClothingMaterialType.LEATHER:
        return new THREE.MeshStandardMaterial({
          color: props.color || 0x8B4513,
          roughness: props.roughness || 0.6,
          metalness: props.metalness || 0.0
        });

      case ClothingMaterialType.DENIM:
        return new THREE.MeshStandardMaterial({
          color: props.color || 0x4169E1,
          roughness: props.roughness || 0.9,
          metalness: props.metalness || 0.0
        });

      case ClothingMaterialType.METAL:
        return new THREE.MeshStandardMaterial({
          color: props.color || 0xC0C0C0,
          roughness: props.roughness || 0.1,
          metalness: props.metalness || 1.0
        });

      default:
        return new THREE.MeshStandardMaterial({
          color: props.color || 0xffffff
        });
    }
  }

  /**
   * 加载纹理
   * @param material 材质
   * @param textureUrls 纹理URL映射
   */
  private async loadTextures(
    material: THREE.Material,
    textureUrls: Map<string, string>
  ): Promise<void> {
    const loader = new THREE.TextureLoader();

    for (const [type, url] of textureUrls) {
      try {
        const texture = await new Promise<THREE.Texture>((resolve, reject) => {
          loader.load(url, resolve, undefined, reject);
        });

        // 应用纹理到材质
        this.applyTextureToMaterial(material, type, texture);
      } catch (error) {
        console.warn(`加载纹理失败: ${url}`, error);
      }
    }
  }

  /**
   * 将纹理应用到材质
   * @param material 材质
   * @param type 纹理类型
   * @param texture 纹理
   */
  private applyTextureToMaterial(
    material: THREE.Material,
    type: string,
    texture: THREE.Texture
  ): void {
    if (material instanceof THREE.MeshStandardMaterial) {
      switch (type) {
        case 'diffuse':
        case 'albedo':
          material.map = texture;
          break;
        case 'normal':
          material.normalMap = texture;
          break;
        case 'roughness':
          material.roughnessMap = texture;
          break;
        case 'metalness':
          material.metalnessMap = texture;
          break;
        case 'emissive':
          material.emissiveMap = texture;
          break;
        case 'ao':
        case 'occlusion':
          material.aoMap = texture;
          break;
      }
    }

    material.needsUpdate = true;
  }

  // ==================== 物理模拟 ====================

  /**
   * 模拟服装物理效果
   * @param fittedClothing 适配的服装
   * @param animation 动画数据
   */
  public simulateClothPhysics(fittedClothing: FittedClothing, animation?: any): void {
    if (!this.config.enablePhysics || !fittedClothing.item.physics.enabled) {
      return;
    }

    const physics = fittedClothing.item.physics;
    const mesh = fittedClothing.mesh;

    // 简化的布料物理模拟
    this.applyGravityEffect(mesh, physics);
    this.applyWindEffect(mesh, physics);
    this.applyAnimationInfluence(mesh, physics, animation);
  }

  /**
   * 应用重力效果
   * @param mesh 网格
   * @param physics 物理属性
   */
  private applyGravityEffect(mesh: THREE.Mesh, physics: ClothingPhysics): void {
    if (!this.physicsWorld) return;

    const gravity = this.physicsWorld.gravity;
    const gravityForce = gravity.clone().multiplyScalar(physics.mass * physics.gravityScale);

    // 应用重力到顶点
    const geometry = mesh.geometry;
    const positions = geometry.attributes.position;

    for (let i = 0; i < positions.count; i++) {
      const vertex = new THREE.Vector3(
        positions.getX(i),
        positions.getY(i),
        positions.getZ(i)
      );

      // 计算重力影响（简化）
      vertex.add(gravityForce.clone().multiplyScalar(0.001));

      positions.setXYZ(i, vertex.x, vertex.y, vertex.z);
    }

    positions.needsUpdate = true;
  }

  /**
   * 应用风效果
   * @param mesh 网格
   * @param physics 物理属性
   */
  private applyWindEffect(mesh: THREE.Mesh, physics: ClothingPhysics): void {
    // 简化的风效果模拟
    const windForce = new THREE.Vector3(
      Math.sin(Date.now() * 0.001) * 0.1,
      0,
      Math.cos(Date.now() * 0.001) * 0.1
    );

    windForce.multiplyScalar(physics.airResistance);

    // 应用风力到网格
    const geometry = mesh.geometry;
    const positions = geometry.attributes.position;

    for (let i = 0; i < positions.count; i++) {
      const vertex = new THREE.Vector3(
        positions.getX(i),
        positions.getY(i),
        positions.getZ(i)
      );

      // 应用风力（简化）
      vertex.add(windForce.clone().multiplyScalar(0.001));

      positions.setXYZ(i, vertex.x, vertex.y, vertex.z);
    }

    positions.needsUpdate = true;
  }

  /**
   * 应用动画影响
   * @param mesh 网格
   * @param physics 物理属性
   * @param animation 动画数据
   */
  private applyAnimationInfluence(mesh: THREE.Mesh, physics: ClothingPhysics, animation?: any): void {
    if (!animation) return;

    // 根据动画速度和类型调整物理效果
    const animationIntensity = this.calculateAnimationIntensity(animation);

    // 调整阻尼和刚度
    const dynamicDamping = physics.damping * (1 + animationIntensity * 0.5);
    const dynamicStiffness = physics.stiffness * (1 - animationIntensity * 0.3);

    // 应用动态物理属性
    this.applyDynamicPhysics(mesh, dynamicDamping, dynamicStiffness);
  }

  /**
   * 计算动画强度
   * @param animation 动画数据
   * @returns 动画强度 (0-1)
   */
  private calculateAnimationIntensity(animation: any): number {
    // 简化的动画强度计算
    // 实际实现需要分析动画的速度、加速度等
    void animation;
    return Math.random() * 0.5; // 占位符
  }

  /**
   * 应用动态物理属性
   * @param mesh 网格
   * @param damping 阻尼
   * @param stiffness 刚度
   */
  private applyDynamicPhysics(mesh: THREE.Mesh, damping: number, stiffness: number): void {
    // 简化的动态物理应用
    void mesh;
    void damping;
    void stiffness;
  }

  // ==================== 服装组合管理 ====================

  /**
   * 创建服装组合
   * @param name 组合名称
   * @param items 服装项映射
   * @param tags 标签
   * @returns 服装组合
   */
  public createOutfit(
    name: string,
    items: Map<ClothingSlotType, string>,
    tags: string[] = []
  ): ClothingOutfit {
    const outfit: ClothingOutfit = {
      id: this.generateOutfitId(),
      name,
      items: new Map(items),
      tags,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.outfitLibrary.set(outfit.id, outfit);

    this.eventEmitter.emit('outfitCreated', outfit);

    if (this.config.debug) {
      console.log(`ClothingSystem: 创建服装组合 ${name} (${outfit.id})`);
    }

    return outfit;
  }

  /**
   * 获取服装组合
   * @param outfitId 组合ID
   * @returns 服装组合
   */
  public getOutfit(outfitId: string): ClothingOutfit | undefined {
    return this.outfitLibrary.get(outfitId);
  }

  /**
   * 获取所有服装组合
   * @returns 服装组合列表
   */
  public getAllOutfits(): ClothingOutfit[] {
    return Array.from(this.outfitLibrary.values());
  }

  /**
   * 应用服装组合到数字人
   * @param entityId 实体ID
   * @param outfitId 组合ID
   * @returns 是否成功应用
   */
  public async applyOutfit(entityId: string, outfitId: string): Promise<boolean> {
    const outfit = this.outfitLibrary.get(outfitId);
    if (!outfit) {
      throw new Error(`服装组合不存在: ${outfitId}`);
    }

    const entity = this.world.getEntity(entityId);
    if (!entity) {
      throw new Error(`实体不存在: ${entityId}`);
    }

    const digitalHuman = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHuman) {
      throw new Error(`实体不是数字人: ${entityId}`);
    }

    try {
      // 先卸下所有现有服装
      const entityClothing = this.equippedClothing.get(entityId);
      if (entityClothing) {
        for (const slotType of entityClothing.keys()) {
          this.unequipClothing(entityId, slotType);
        }
      }

      // 装备组合中的所有服装
      const results: boolean[] = [];
      for (const [slotType, itemId] of outfit.items) {
        const result = await this.equipClothing(entityId, itemId);
        results.push(result);
      }

      // 更新数字人组件的组合信息
      digitalHuman.setOutfit(outfitId, outfit.name);

      // 触发事件
      this.eventEmitter.emit('outfitApplied', entityId, outfit);

      if (this.config.debug) {
        console.log(`ClothingSystem: 为数字人 ${entityId} 应用服装组合 ${outfit.name}`);
      }

      return results.every(r => r);
    } catch (error) {
      console.error('应用服装组合失败:', error);
      return false;
    }
  }

  /**
   * 删除服装组合
   * @param outfitId 组合ID
   * @returns 是否成功删除
   */
  public deleteOutfit(outfitId: string): boolean {
    const outfit = this.outfitLibrary.get(outfitId);
    if (!outfit) {
      return false;
    }

    this.outfitLibrary.delete(outfitId);

    this.eventEmitter.emit('outfitDeleted', outfit);

    if (this.config.debug) {
      console.log(`ClothingSystem: 删除服装组合 ${outfit.name} (${outfitId})`);
    }

    return true;
  }

  /**
   * 生成服装组合ID
   * @returns 组合ID
   */
  private generateOutfitId(): string {
    return `outfit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ==================== 事件和工具方法 ====================

  // 移除了重写的EventEmitter方法，直接使用继承的方法

  /**
   * 获取系统统计信息
   * @returns 统计信息
   */
  public getStats(): {
    clothingItems: number;
    outfits: number;
    equippedEntities: number;
    cacheSize: number;
  } {
    return {
      clothingItems: this.clothingLibrary.size,
      outfits: this.outfitLibrary.size,
      equippedEntities: this.equippedClothing.size,
      cacheSize: this.materialCache.size + this.geometryCache.size
    };
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    // 清理材质缓存
    for (const material of this.materialCache.values()) {
      material.dispose();
    }
    this.materialCache.clear();

    // 清理几何体缓存
    for (const geometry of this.geometryCache.values()) {
      geometry.dispose();
    }
    this.geometryCache.clear();

    if (this.config.debug) {
      console.log('ClothingSystem: 缓存已清理');
    }
  }
}
