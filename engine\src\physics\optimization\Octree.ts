/**
 * 八叉树空间分区
 * 用于加速碰撞检测
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { ISpatialPartitioning } from './SpatialPartitioning';
import { Debug } from '../../utils/Debug';

/**
 * 八叉树配置接口
 */
export interface OctreeOptions {
  /** 最大深度 */
  maxDepth?: number;
  /** 最大物体数量 */
  maxObjects?: number;
  /** 最小节点大小 */
  minNodeSize?: number;
  /** 世界大小 */
  worldSize?: number;
  /** 世界中心 */
  worldCenter?: CANNON.Vec3;
  /** 是否使用松散八叉树 */
  useLooseOctree?: boolean;
  /** 松散因子 */
  looseFactor?: number;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 八叉树节点接口
 */
export interface OctreeNode {
  /** 节点中心 */
  center: CANNON.Vec3;
  /** 节点半尺寸 */
  halfSize: number;
  /** 节点深度 */
  depth: number;
  /** 节点物体 */
  bodies: Set<CANNON.Body>;
  /** 子节点 */
  children: OctreeNode[] | null;
  /** 节点AABB */
  aabb: CANNON.AABB;
  /** 是否为叶子节点 */
  isLeaf: boolean;
}

/**
 * 八叉树类
 */
export class Octree implements ISpatialPartitioning {
  /** 最大深度 */
  private maxDepth: number;

  /** 最大物体数量 */
  private maxObjects: number;

  /** 最小节点大小 */
  private minNodeSize: number;

  /** 世界大小 */
  private worldSize: number;

  /** 世界中心 */
  private worldCenter: CANNON.Vec3;

  /** 是否使用松散八叉树 */
  private useLooseOctree: boolean;

  /** 松散因子 */
  private looseFactor: number;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 根节点 */
  private root: OctreeNode;

  /** 物体到节点的映射 */
  private bodyToNodes: Map<CANNON.Body, Set<OctreeNode>>;

  /** 物体列表 */
  private bodies: Set<CANNON.Body>;

  /** 调试网格 */
  private debugMesh: THREE.Object3D | null = null;

  /**
   * 创建八叉树
   * @param options 八叉树配置
   */
  constructor(options: OctreeOptions = {}) {
    this.maxDepth = options.maxDepth || 8;
    this.maxObjects = options.maxObjects || 8;
    this.minNodeSize = options.minNodeSize || 1;
    this.worldSize = options.worldSize || 100;
    this.worldCenter = options.worldCenter || new CANNON.Vec3(0, 0, 0);
    this.useLooseOctree = options.useLooseOctree !== undefined ? options.useLooseOctree : false;
    this.looseFactor = options.looseFactor || 1.5;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;

    this.bodyToNodes = new Map();
    this.bodies = new Set();

    // 创建根节点
    this.root = this.createNode(this.worldCenter, this.worldSize / 2, 0);

    if (this.useDebugVisualization) {
      this.createDebugMesh();
    }
  }

  /**
   * 创建节点
   * @param center 节点中心
   * @param halfSize 节点半尺寸
   * @param depth 节点深度
   * @returns 节点
   */
  private createNode(center: CANNON.Vec3, halfSize: number, depth: number): OctreeNode {
    // 计算节点AABB
    const min = new CANNON.Vec3(
      center.x - halfSize,
      center.y - halfSize,
      center.z - halfSize
    );
    const max = new CANNON.Vec3(
      center.x + halfSize,
      center.y + halfSize,
      center.z + halfSize
    );

    // 如果使用松散八叉树，则扩大AABB
    if (this.useLooseOctree) {
      const expansion = halfSize * (this.looseFactor - 1);
      min.x -= expansion;
      min.y -= expansion;
      min.z -= expansion;
      max.x += expansion;
      max.y += expansion;
      max.z += expansion;
    }

    const aabb = new CANNON.AABB();
    aabb.lowerBound.copy(min);
    aabb.upperBound.copy(max);

    return {
      center: center.clone(),
      halfSize,
      depth,
      bodies: new Set(),
      children: null,
      aabb,
      isLeaf: true
    };
  }

  /**
   * 创建调试网格
   */
  private createDebugMesh(): void {
    this.debugMesh = new THREE.Object3D();
    this.debugMesh.name = 'octree_debug';

    // 递归创建节点调试网格
    this.createNodeDebugMesh(this.root, this.debugMesh);
  }

  /**
   * 创建节点调试网格
   * @param node 节点
   * @param parent 父对象
   */
  private createNodeDebugMesh(node: OctreeNode, parent: THREE.Object3D): void {
    // 创建节点AABB线框
    const min = node.aabb.lowerBound;
    const max = node.aabb.upperBound;
    const size = new THREE.Vector3(
      max.x - min.x,
      max.y - min.y,
      max.z - min.z
    );
    const center = new THREE.Vector3(
      (min.x + max.x) / 2,
      (min.y + max.y) / 2,
      (min.z + max.z) / 2
    );

    const geometry = new THREE.BoxGeometry(size.x, size.y, size.z);
    const material = new THREE.MeshBasicMaterial({
      wireframe: true,
      color: node.isLeaf ? 0x00ff00 : 0xffff00,
      transparent: true,
      opacity: 0.5 - node.depth * 0.05
    });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.copy(center);
    parent.add(mesh);

    // 递归创建子节点调试网格
    if (node.children) {
      for (const child of node.children) {
        this.createNodeDebugMesh(child, parent);
      }
    }
  }

  /**
   * 更新调试网格
   */
  private updateDebugMesh(): void {
    if (!this.debugMesh) {
      return;
    }

    // 移除旧的调试网格
    while (this.debugMesh.children.length > 0) {
      const child = this.debugMesh.children[0];
      this.debugMesh.remove(child);
      if (child instanceof THREE.Mesh) {
        (child.geometry as any).dispose();
        (child.material as THREE.Material).dispose();
      }
    }

    // 创建新的调试网格
    this.createNodeDebugMesh(this.root, this.debugMesh);
  }

  /**
   * 添加物体
   * @param body 物体
   */
  public add(body: CANNON.Body): void {
    // 如果物体已经存在，则先移除
    if (this.bodies.has(body)) {
      this.remove(body);
    }

    // 添加物体
    this.bodies.add(body);

    // 获取物体的AABB
    const aabb = this.getBodyAABB(body);

    // 将物体添加到节点
    const nodes = new Set<OctreeNode>();
    this.insertBody(body, aabb, this.root, nodes);

    // 保存物体到节点的映射
    this.bodyToNodes.set(body, nodes);
  }

  /**
   * 获取物体的AABB
   * @param body 物体
   * @returns AABB
   */
  private getBodyAABB(body: CANNON.Body): CANNON.AABB {
    const aabb = new CANNON.AABB();
    // 计算物体的AABB
    if (body.shapes.length > 0) {
      // 使用 calculateWorldAABB 方法计算世界空间的AABB
      body.shapes[0].calculateWorldAABB(body.position, body.quaternion, aabb.lowerBound, aabb.upperBound);
    } else {
      // 如果没有形状，使用物体位置作为点AABB
      aabb.lowerBound.copy(body.position);
      aabb.upperBound.copy(body.position);
    }
    return aabb;
  }

  /**
   * 将物体插入节点
   * @param body 物体
   * @param aabb 物体AABB
   * @param node 节点
   * @param nodes 物体所在的节点集合
   */
  private insertBody(body: CANNON.Body, aabb: CANNON.AABB, node: OctreeNode, nodes: Set<OctreeNode>): void {
    // 检查物体是否与节点相交
    if (!this.aabbOverlap(aabb.lowerBound, aabb.upperBound, node.aabb.lowerBound, node.aabb.upperBound)) {
      return;
    }

    // 如果是叶子节点且物体数量小于最大物体数量，或者节点深度达到最大深度，或者节点大小小于最小节点大小，则将物体添加到节点
    if (node.isLeaf && (node.bodies.size < this.maxObjects || node.depth >= this.maxDepth || node.halfSize <= this.minNodeSize)) {
      node.bodies.add(body);
      nodes.add(node);
      return;
    }

    // 如果是叶子节点，则分割节点
    if (node.isLeaf) {
      this.splitNode(node);
    }

    // 将物体插入子节点
    for (const child of node.children!) {
      this.insertBody(body, aabb, child, nodes);
    }
  }

  /**
   * 移除物体
   * @param body 物体
   */
  public remove(body: CANNON.Body): void {
    // 如果物体不存在，则直接返回
    if (!this.bodies.has(body)) {
      return;
    }

    // 移除物体
    this.bodies.delete(body);

    // 获取物体所在的节点
    const nodes = this.bodyToNodes.get(body);
    if (nodes) {
      // 从节点中移除物体
      for (const node of nodes) {
        node.bodies.delete(body);
      }
    }

    // 移除物体到节点的映射
    this.bodyToNodes.delete(body);
  }

  /**
   * 更新物体
   * @param body 物体
   */
  public update(body: CANNON.Body): void {
    // 如果物体不存在，则直接返回
    if (!this.bodies.has(body)) {
      return;
    }

    // 移除物体并重新添加
    this.remove(body);
    this.add(body);
  }

  /**
   * 更新所有物体
   */
  public updateAll(): void {
    for (const body of this.bodies) {
      this.update(body);
    }
  }

  /**
   * 查询区域内的物体
   * @param min 最小坐标
   * @param max 最大坐标
   * @returns 区域内的物体
   */
  public queryRegion(min: CANNON.Vec3, max: CANNON.Vec3): CANNON.Body[] {
    // 创建查询AABB
    const queryAABB = new CANNON.AABB();
    queryAABB.lowerBound.copy(min);
    queryAABB.upperBound.copy(max);

    // 收集区域内的所有物体
    const bodies = new Set<CANNON.Body>();
    this.queryNodeRegion(this.root, queryAABB, bodies);

    return Array.from(bodies);
  }

  /**
   * 查询节点区域内的物体
   * @param node 节点
   * @param queryAABB 查询AABB
   * @param bodies 物体集合
   */
  private queryNodeRegion(node: OctreeNode, queryAABB: CANNON.AABB, bodies: Set<CANNON.Body>): void {
    // 检查节点是否与查询区域相交
    if (!this.aabbOverlap(node.aabb.lowerBound, node.aabb.upperBound, queryAABB.lowerBound, queryAABB.upperBound)) {
      return;
    }

    // 收集节点中的物体
    for (const body of node.bodies) {
      const bodyAABB = this.getBodyAABB(body);
      if (this.aabbOverlap(bodyAABB.lowerBound, bodyAABB.upperBound, queryAABB.lowerBound, queryAABB.upperBound)) {
        bodies.add(body);
      }
    }

    // 递归查询子节点
    if (node.children) {
      for (const child of node.children) {
        this.queryNodeRegion(child, queryAABB, bodies);
      }
    }
  }

  /**
   * 检查两个AABB是否重叠
   * @param minA AABB A的最小坐标
   * @param maxA AABB A的最大坐标
   * @param minB AABB B的最小坐标
   * @param maxB AABB B的最大坐标
   * @returns 是否重叠
   */
  private aabbOverlap(minA: CANNON.Vec3, maxA: CANNON.Vec3, minB: CANNON.Vec3, maxB: CANNON.Vec3): boolean {
    return (
      maxA.x >= minB.x && minA.x <= maxB.x &&
      maxA.y >= minB.y && minA.y <= maxB.y &&
      maxA.z >= minB.z && minA.z <= maxB.z
    );
  }

  /**
   * 查询射线碰撞的物体
   * @param from 射线起点
   * @param to 射线终点
   * @returns 射线碰撞的物体
   */
  public queryRay(from: CANNON.Vec3, to: CANNON.Vec3): CANNON.Body[] {
    // 创建射线
    const direction = new CANNON.Vec3();
    direction.copy(to);
    direction.vsub(from, direction);
    const length = direction.length();
    direction.normalize();

    // 收集射线碰撞的所有物体
    const bodies = new Set<CANNON.Body>();
    this.queryNodeRay(this.root, from, direction, length, bodies);

    return Array.from(bodies);
  }

  /**
   * 查询节点射线碰撞的物体
   * @param node 节点
   * @param from 射线起点
   * @param direction 射线方向
   * @param length 射线长度
   * @param bodies 物体集合
   */
  private queryNodeRay(node: OctreeNode, from: CANNON.Vec3, direction: CANNON.Vec3, length: number, bodies: Set<CANNON.Body>): void {
    // 检查射线是否与节点相交
    if (!this.rayAabbIntersect(from, direction, length, node.aabb.lowerBound, node.aabb.upperBound)) {
      return;
    }

    // 收集节点中的物体
    for (const body of node.bodies) {
      const bodyAABB = this.getBodyAABB(body);
      if (this.rayAabbIntersect(from, direction, length, bodyAABB.lowerBound, bodyAABB.upperBound)) {
        bodies.add(body);
      }
    }

    // 递归查询子节点
    if (node.children) {
      for (const child of node.children) {
        this.queryNodeRay(child, from, direction, length, bodies);
      }
    }
  }

  /**
   * 检查射线是否与AABB相交
   * @param from 射线起点
   * @param direction 射线方向
   * @param length 射线长度
   * @param min AABB的最小坐标
   * @param max AABB的最大坐标
   * @returns 是否相交
   */
  private rayAabbIntersect(from: CANNON.Vec3, direction: CANNON.Vec3, length: number, min: CANNON.Vec3, max: CANNON.Vec3): boolean {
    // 计算射线与AABB各面的交点
    const tmin = new CANNON.Vec3();
    const tmax = new CANNON.Vec3();

    tmin.x = (min.x - from.x) / direction.x;
    tmax.x = (max.x - from.x) / direction.x;
    if (tmin.x > tmax.x) {
      const temp = tmin.x;
      tmin.x = tmax.x;
      tmax.x = temp;
    }

    tmin.y = (min.y - from.y) / direction.y;
    tmax.y = (max.y - from.y) / direction.y;
    if (tmin.y > tmax.y) {
      const temp = tmin.y;
      tmin.y = tmax.y;
      tmax.y = temp;
    }

    tmin.z = (min.z - from.z) / direction.z;
    tmax.z = (max.z - from.z) / direction.z;
    if (tmin.z > tmax.z) {
      const temp = tmin.z;
      tmin.z = tmax.z;
      tmax.z = temp;
    }

    // 计算交点范围
    const tNear = Math.max(tmin.x, Math.max(tmin.y, tmin.z));
    const tFar = Math.min(tmax.x, Math.min(tmax.y, tmax.z));

    // 如果最近的交点比最远的交点还远，或者最远的交点为负，则没有相交
    if (tNear > tFar || tFar < 0) {
      return false;
    }

    // 如果最近的交点超出射线长度，则没有相交
    if (tNear > length) {
      return false;
    }

    return true;
  }

  /**
   * 查询球体碰撞的物体
   * @param center 球体中心
   * @param radius 球体半径
   * @returns 球体碰撞的物体
   */
  public querySphere(center: CANNON.Vec3, radius: number): CANNON.Body[] {
    // 创建包围球体的AABB
    const min = new CANNON.Vec3(center.x - radius, center.y - radius, center.z - radius);
    const max = new CANNON.Vec3(center.x + radius, center.y + radius, center.z + radius);

    // 创建查询AABB
    const queryAABB = new CANNON.AABB();
    queryAABB.lowerBound.copy(min);
    queryAABB.upperBound.copy(max);

    // 收集球体碰撞的所有物体
    const bodies = new Set<CANNON.Body>();
    this.queryNodeSphere(this.root, center, radius, queryAABB, bodies);

    return Array.from(bodies);
  }

  /**
   * 查询节点球体碰撞的物体
   * @param node 节点
   * @param center 球体中心
   * @param radius 球体半径
   * @param queryAABB 查询AABB
   * @param bodies 物体集合
   */
  private queryNodeSphere(node: OctreeNode, center: CANNON.Vec3, radius: number, queryAABB: CANNON.AABB, bodies: Set<CANNON.Body>): void {
    // 检查节点是否与查询区域相交
    if (!this.aabbOverlap(node.aabb.lowerBound, node.aabb.upperBound, queryAABB.lowerBound, queryAABB.upperBound)) {
      return;
    }

    // 收集节点中的物体
    for (const body of node.bodies) {
      const bodyAABB = this.getBodyAABB(body);
      if (this.aabbOverlap(bodyAABB.lowerBound, bodyAABB.upperBound, queryAABB.lowerBound, queryAABB.upperBound) &&
          this.sphereAabbIntersect(center, radius, bodyAABB.lowerBound, bodyAABB.upperBound)) {
        bodies.add(body);
      }
    }

    // 递归查询子节点
    if (node.children) {
      for (const child of node.children) {
        this.queryNodeSphere(child, center, radius, queryAABB, bodies);
      }
    }
  }

  /**
   * 检查球体是否与AABB相交
   * @param center 球体中心
   * @param radius 球体半径
   * @param min AABB的最小坐标
   * @param max AABB的最大坐标
   * @returns 是否相交
   */
  private sphereAabbIntersect(center: CANNON.Vec3, radius: number, min: CANNON.Vec3, max: CANNON.Vec3): boolean {
    // 计算球体中心到AABB的最近点
    const closestPoint = new CANNON.Vec3();
    closestPoint.x = Math.max(min.x, Math.min(center.x, max.x));
    closestPoint.y = Math.max(min.y, Math.min(center.y, max.y));
    closestPoint.z = Math.max(min.z, Math.min(center.z, max.z));

    // 计算球体中心到最近点的距离
    const distance = new CANNON.Vec3();
    distance.copy(closestPoint);
    distance.vsub(center, distance);
    const distanceSquared = distance.lengthSquared();

    // 如果距离小于等于半径，则相交
    return distanceSquared <= radius * radius;
  }

  /**
   * 查询与物体可能碰撞的物体
   * @param body 物体
   * @returns 可能碰撞的物体
   */
  public queryPotentialCollisions(body: CANNON.Body): CANNON.Body[] {
    // 如果物体不存在，则直接返回
    if (!this.bodies.has(body)) {
      return [];
    }

    // 获取物体的AABB
    const bodyAABB = this.getBodyAABB(body);

    // 收集可能碰撞的所有物体
    const bodies = new Set<CANNON.Body>();
    this.queryNodePotentialCollisions(this.root, body, bodyAABB, bodies);

    return Array.from(bodies);
  }

  /**
   * 查询节点与物体可能碰撞的物体
   * @param node 节点
   * @param body 物体
   * @param bodyAABB 物体AABB
   * @param bodies 物体集合
   */
  private queryNodePotentialCollisions(node: OctreeNode, body: CANNON.Body, bodyAABB: CANNON.AABB, bodies: Set<CANNON.Body>): void {
    // 检查节点是否与物体相交
    if (!this.aabbOverlap(node.aabb.lowerBound, node.aabb.upperBound, bodyAABB.lowerBound, bodyAABB.upperBound)) {
      return;
    }

    // 收集节点中的物体
    for (const otherBody of node.bodies) {
      // 排除自身
      if (otherBody !== body) {
        const otherBodyAABB = this.getBodyAABB(otherBody);
        if (this.aabbOverlap(bodyAABB.lowerBound, bodyAABB.upperBound, otherBodyAABB.lowerBound, otherBodyAABB.upperBound)) {
          bodies.add(otherBody);
        }
      }
    }

    // 递归查询子节点
    if (node.children) {
      for (const child of node.children) {
        this.queryNodePotentialCollisions(child, body, bodyAABB, bodies);
      }
    }
  }

  /**
   * 获取所有物体
   * @returns 所有物体
   */
  public getBodies(): CANNON.Body[] {
    return Array.from(this.bodies);
  }

  /**
   * 获取物体数量
   * @returns 物体数量
   */
  public getBodyCount(): number {
    return this.bodies.size;
  }

  /**
   * 清空
   */
  public clear(): void {
    // 清空物体
    this.bodies.clear();
    this.bodyToNodes.clear();

    // 重新创建根节点
    this.root = this.createNode(this.worldCenter, this.worldSize / 2, 0);

    // 更新调试网格
    if (this.useDebugVisualization && this.debugMesh) {
      this.updateDebugMesh();
    }
  }

  /**
   * 销毁
   */
  public dispose(): void {
    this.clear();

    // 销毁调试网格
    if (this.debugMesh) {
      while (this.debugMesh.children.length > 0) {
        const child = this.debugMesh.children[0];
        this.debugMesh.remove(child);
        if (child instanceof THREE.Mesh) {
          (child.geometry as any).dispose();
          (child.material as THREE.Material).dispose();
        }
      }
      this.debugMesh = null;
    }
  }

  /**
   * 获取调试信息
   * @returns 调试信息
   */
  public getDebugInfo(): any {
    // 收集节点信息
    const nodeInfo = this.getNodeInfo(this.root);

    return {
      maxDepth: this.maxDepth,
      maxObjects: this.maxObjects,
      minNodeSize: this.minNodeSize,
      worldSize: this.worldSize,
      worldCenter: this.worldCenter,
      useLooseOctree: this.useLooseOctree,
      looseFactor: this.looseFactor,
      bodyCount: this.bodies.size,
      nodeCount: nodeInfo.nodeCount,
      leafNodeCount: nodeInfo.leafNodeCount,
      maxNodeDepth: nodeInfo.maxDepth,
      averageBodiesPerLeafNode: nodeInfo.leafNodeCount > 0 ? nodeInfo.totalBodies / nodeInfo.leafNodeCount : 0
    };
  }

  /**
   * 获取节点信息
   * @param node 节点
   * @returns 节点信息
   */
  private getNodeInfo(node: OctreeNode): { nodeCount: number; leafNodeCount: number; maxDepth: number; totalBodies: number } {
    let nodeCount = 1;
    let leafNodeCount = node.isLeaf ? 1 : 0;
    let maxDepth = node.depth;
    let totalBodies = node.bodies.size;

    if (node.children) {
      for (const child of node.children) {
        const childInfo = this.getNodeInfo(child);
        nodeCount += childInfo.nodeCount;
        leafNodeCount += childInfo.leafNodeCount;
        maxDepth = Math.max(maxDepth, childInfo.maxDepth);
        totalBodies += childInfo.totalBodies;
      }
    }

    return { nodeCount, leafNodeCount, maxDepth, totalBodies };
  }

  /**
   * 获取调试网格
   * @returns 调试网格
   */
  public getDebugMesh(): THREE.Object3D {
    if (!this.debugMesh) {
      this.createDebugMesh();
    }
    return this.debugMesh!;
  }

  /**
   * 分割节点
   * @param node 节点
   */
  private splitNode(node: OctreeNode): void {
    // 如果节点深度达到最大深度，则不分割
    if (node.depth >= this.maxDepth) {
      return;
    }

    // 如果节点大小小于最小节点大小，则不分割
    if (node.halfSize <= this.minNodeSize) {
      return;
    }

    // 创建子节点
    const childHalfSize = node.halfSize / 2;
    const children: OctreeNode[] = [];

    // 创建8个子节点
    for (let i = 0; i < 8; i++) {
      const x = ((i & 1) === 0) ? -1 : 1;
      const y = ((i & 2) === 0) ? -1 : 1;
      const z = ((i & 4) === 0) ? -1 : 1;

      const childCenter = new CANNON.Vec3(
        node.center.x + x * childHalfSize,
        node.center.y + y * childHalfSize,
        node.center.z + z * childHalfSize
      );

      const child = this.createNode(childCenter, childHalfSize, node.depth + 1);
      children.push(child);
    }

    // 设置子节点
    node.children = children;
    node.isLeaf = false;

    // 将节点中的物体重新分配到子节点
    for (const body of node.bodies) {
      const bodyAABB = this.getBodyAABB(body);
      const bodyNodes = this.bodyToNodes.get(body);
      if (bodyNodes) {
        bodyNodes.delete(node);
        for (const child of children) {
          this.insertBody(body, bodyAABB, child, bodyNodes);
        }
      }
    }

    // 清空节点中的物体
    node.bodies.clear();
  }
}
