# DL引擎渲染服务故障排除指南

## 概述

本文档提供DL引擎渲染服务的常见问题诊断、故障排除方法和维护建议，帮助运维人员快速定位和解决问题。

## 1. 常见问题诊断

### 1.1 服务启动失败

#### 问题现象
- 容器启动后立即退出
- 健康检查失败
- 无法连接到服务端口

#### 诊断步骤
```bash
# 查看容器日志
docker logs render-service

# 查看容器状态
docker ps -a | grep render-service

# 检查端口占用
netstat -tlnp | grep :3004
netstat -tlnp | grep :4004

# 检查环境变量
docker exec render-service env | grep -E "(DATABASE|REDIS|JWT)"
```

#### 常见原因和解决方案

**数据库连接失败**
```bash
# 检查数据库连接
docker exec render-service nc -zv mysql 3306

# 验证数据库凭据
mysql -h localhost -u root -p -e "SHOW DATABASES;"

# 解决方案：更新环境变量
docker run -e DATABASE_HOST=correct-host \
           -e DATABASE_PASSWORD=correct-password \
           dl-engine/render-service:latest
```

**Redis连接失败**
```bash
# 检查Redis连接
docker exec render-service nc -zv redis 6379

# 测试Redis连接
redis-cli -h localhost ping

# 解决方案：确保Redis服务运行
docker start redis
```

**端口冲突**
```bash
# 查找占用端口的进程
lsof -i :3004
lsof -i :4004

# 解决方案：更改端口映射
docker run -p 3014:3004 -p 4014:4004 dl-engine/render-service:latest
```

### 1.2 渲染任务失败

#### 问题现象
- 任务状态一直为"处理中"
- 任务失败率高
- 渲染结果异常

#### 诊断步骤
```bash
# 查看渲染任务日志
docker exec render-service tail -f /app/logs/render.log

# 检查队列状态
docker exec redis redis-cli LLEN bull:render:waiting
docker exec redis redis-cli LLEN bull:render:active
docker exec redis redis-cli LLEN bull:render:failed

# 查看系统资源使用
docker stats render-service
```

#### 常见原因和解决方案

**内存不足**
```bash
# 检查内存使用
docker exec render-service free -h
docker exec render-service ps aux --sort=-%mem | head

# 解决方案：增加内存限制
docker run --memory=8g dl-engine/render-service:latest

# Kubernetes中增加资源限制
resources:
  limits:
    memory: "8Gi"
    cpu: "4000m"
```

**场景数据加载失败**
```bash
# 检查项目服务连接
docker exec render-service nc -zv project-service 3002

# 验证场景数据
curl -H "Authorization: Bearer $TOKEN" \
     http://project-service:4002/api/scenes/$SCENE_ID

# 解决方案：确保依赖服务正常
docker restart project-service
```

**渲染超时**
```bash
# 检查渲染超时设置
docker exec render-service env | grep MAX_RENDER_TIME

# 解决方案：调整超时时间
docker run -e MAX_RENDER_TIME=600000 dl-engine/render-service:latest
```

### 1.3 性能问题

#### 问题现象
- 渲染速度慢
- 队列积压严重
- CPU/内存使用率高

#### 诊断步骤
```bash
# 监控系统性能
top -p $(docker inspect --format '{{.State.Pid}}' render-service)
iostat -x 1
vmstat 1

# 分析渲染性能
docker exec render-service node --prof app.js
docker exec render-service node --prof-process isolate-*.log > profile.txt
```

#### 优化建议

**并发控制优化**
```typescript
// 调整并发任务数量
const queueOptions = {
  concurrency: Math.min(os.cpus().length, 5),
  settings: {
    stalledInterval: 30000,
    maxStalledCount: 1,
  },
};
```

**资源池管理**
```typescript
// 实现渲染器资源池
class RendererPool {
  private pool: THREE.WebGLRenderer[] = [];
  private maxSize = 5;
  
  public async getRenderer(): Promise<THREE.WebGLRenderer> {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createRenderer();
  }
  
  public releaseRenderer(renderer: THREE.WebGLRenderer): void {
    if (this.pool.length < this.maxSize) {
      this.pool.push(renderer);
    } else {
      renderer.dispose();
    }
  }
}
```

## 2. 监控和告警

### 2.1 关键指标监控

#### 服务健康指标
```typescript
// 健康检查端点
@Get('health')
async getHealth(): Promise<HealthStatus> {
  const checks = await Promise.allSettled([
    this.checkDatabase(),
    this.checkRedis(),
    this.checkDependentServices(),
    this.checkDiskSpace(),
  ]);
  
  return {
    status: checks.every(check => check.status === 'fulfilled') ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    checks: checks.map((check, index) => ({
      name: ['database', 'redis', 'services', 'disk'][index],
      status: check.status,
      message: check.status === 'rejected' ? check.reason : 'OK'
    }))
  };
}
```

#### 业务指标监控
```typescript
// Prometheus指标
import { Counter, Histogram, Gauge } from 'prom-client';

const renderJobsTotal = new Counter({
  name: 'render_jobs_total',
  help: '渲染任务总数',
  labelNames: ['type', 'status']
});

const renderDuration = new Histogram({
  name: 'render_duration_seconds',
  help: '渲染耗时',
  labelNames: ['type'],
  buckets: [1, 5, 10, 30, 60, 300, 600]
});

const queueLength = new Gauge({
  name: 'render_queue_length',
  help: '渲染队列长度'
});
```

### 2.2 告警规则配置

#### Prometheus告警规则
```yaml
# render-service-alerts.yml
groups:
- name: render-service
  rules:
  - alert: RenderServiceDown
    expr: up{job="render-service"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "渲染服务不可用"
      description: "渲染服务已停止响应超过1分钟"

  - alert: HighRenderFailureRate
    expr: rate(render_jobs_total{status="failed"}[5m]) / rate(render_jobs_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "渲染失败率过高"
      description: "渲染失败率超过10%，当前值：{{ $value }}"

  - alert: LongRenderQueue
    expr: render_queue_length > 100
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "渲染队列积压"
      description: "渲染队列长度超过100，当前值：{{ $value }}"

  - alert: HighMemoryUsage
    expr: container_memory_usage_bytes{container="render-service"} / container_spec_memory_limit_bytes > 0.9
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "内存使用率过高"
      description: "渲染服务内存使用率超过90%"
```

## 3. 维护操作

### 3.1 日常维护任务

#### 清理过期数据
```sql
-- 清理30天前的已完成任务
DELETE FROM render_jobs 
WHERE status = 'completed' 
  AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理孤立的渲染结果
DELETE FROM render_results 
WHERE job_id NOT IN (SELECT id FROM render_jobs);
```

#### 清理渲染文件
```bash
#!/bin/bash
# cleanup-render-files.sh

RENDER_DIR="/app/renders"
DAYS_TO_KEEP=7

# 清理7天前的渲染文件
find $RENDER_DIR -type f -mtime +$DAYS_TO_KEEP -delete

# 清理空目录
find $RENDER_DIR -type d -empty -delete

# 记录清理结果
echo "$(date): 清理完成，释放空间：$(du -sh $RENDER_DIR)"
```

#### 数据库维护
```sql
-- 优化表结构
OPTIMIZE TABLE render_jobs;
OPTIMIZE TABLE render_results;

-- 更新统计信息
ANALYZE TABLE render_jobs;
ANALYZE TABLE render_results;

-- 检查表完整性
CHECK TABLE render_jobs;
CHECK TABLE render_results;
```

### 3.2 备份和恢复

#### 数据备份
```bash
#!/bin/bash
# backup-render-data.sh

BACKUP_DIR="/backup/render-service"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -h mysql -u root -p$MYSQL_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  dl_engine > $BACKUP_DIR/render_db_$DATE.sql

# 备份渲染文件
tar -czf $BACKUP_DIR/render_files_$DATE.tar.gz /app/renders

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

#### 数据恢复
```bash
#!/bin/bash
# restore-render-data.sh

BACKUP_FILE=$1
RENDER_FILES_BACKUP=$2

if [ -z "$BACKUP_FILE" ]; then
  echo "用法: $0 <数据库备份文件> [渲染文件备份]"
  exit 1
fi

# 恢复数据库
mysql -h mysql -u root -p$MYSQL_PASSWORD dl_engine < $BACKUP_FILE

# 恢复渲染文件（可选）
if [ -n "$RENDER_FILES_BACKUP" ]; then
  tar -xzf $RENDER_FILES_BACKUP -C /
fi

echo "数据恢复完成"
```

### 3.3 版本升级

#### 滚动升级步骤
```bash
#!/bin/bash
# rolling-update.sh

NEW_VERSION=$1

if [ -z "$NEW_VERSION" ]; then
  echo "用法: $0 <新版本号>"
  exit 1
fi

# 1. 拉取新镜像
docker pull dl-engine/render-service:$NEW_VERSION

# 2. 逐个更新实例
for i in {1..3}; do
  echo "更新实例 $i..."
  
  # 停止旧容器
  docker stop render-service-$i
  
  # 启动新容器
  docker run -d \
    --name render-service-$i-new \
    --network dl-engine-network \
    -e DATABASE_HOST=mysql \
    -e REDIS_HOST=redis \
    dl-engine/render-service:$NEW_VERSION
  
  # 等待健康检查通过
  sleep 30
  
  # 验证新实例
  if curl -f http://localhost:4004/api/health; then
    echo "实例 $i 更新成功"
    docker rm render-service-$i
    docker rename render-service-$i-new render-service-$i
  else
    echo "实例 $i 更新失败，回滚"
    docker stop render-service-$i-new
    docker rm render-service-$i-new
    docker start render-service-$i
    exit 1
  fi
done

echo "滚动升级完成"
```

## 4. 应急响应

### 4.1 服务中断处理

#### 快速恢复步骤
1. **确认问题范围**
   ```bash
   # 检查服务状态
   kubectl get pods -n dl-engine | grep render-service
   docker ps | grep render-service
   ```

2. **临时修复**
   ```bash
   # 重启服务
   kubectl rollout restart deployment/render-service -n dl-engine
   docker restart render-service
   ```

3. **流量切换**
   ```bash
   # 切换到备用实例
   kubectl patch service render-service -p '{"spec":{"selector":{"app":"render-service-backup"}}}'
   ```

### 4.2 数据恢复

#### 紧急数据恢复
```bash
# 从最近备份恢复
LATEST_BACKUP=$(ls -t /backup/render-service/render_db_*.sql | head -1)
mysql -h mysql -u root -p$MYSQL_PASSWORD dl_engine < $LATEST_BACKUP

# 重建索引
mysql -h mysql -u root -p$MYSQL_PASSWORD -e "
  USE dl_engine;
  ALTER TABLE render_jobs ADD INDEX idx_user_status (user_id, status);
  ALTER TABLE render_jobs ADD INDEX idx_created_at (created_at);
"
```

---

*本文档提供了DL引擎渲染服务的全面故障排除和维护指南，确保服务的稳定运行和快速问题解决。*
