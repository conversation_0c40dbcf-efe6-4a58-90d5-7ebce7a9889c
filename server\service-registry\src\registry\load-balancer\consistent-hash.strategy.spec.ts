import { Test, TestingModule } from '@nestjs/testing';
import { ConsistentHashLoadBalancerStrategy } from './consistent-hash.strategy';
import { LoadBalancerContext, LoadBalancerAlgorithm } from './load-balancer.interface';
import { ServiceInstanceEntity } from '../entities/service-instance.entity';

describe('ConsistentHashLoadBalancerStrategy', () => {
  let strategy: ConsistentHashLoadBalancerStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ConsistentHashLoadBalancerStrategy],
    }).compile();

    strategy = module.get<ConsistentHashLoadBalancerStrategy>(ConsistentHashLoadBalancerStrategy);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  it('should return correct algorithm name', () => {
    expect(strategy.getName()).toBe(LoadBalancerAlgorithm.CONSISTENT_HASH);
  });

  describe('select', () => {
    it('should return null for empty instances', async () => {
      const context: LoadBalancerContext = {
        serviceName: 'test-service',
        clientIp: '127.0.0.1',
      };

      const result = await strategy.select([], context);
      expect(result).toBeNull();
    });

    it('should return the only instance if there is only one', async () => {
      const instances: ServiceInstanceEntity[] = [
        {
          id: 'instance-1',
          serviceId: 'service-1',
          instanceId: 'test-instance-1',
          host: 'localhost',
          port: 3000,
          status: 'UP',
        } as ServiceInstanceEntity,
      ];

      const context: LoadBalancerContext = {
        serviceName: 'test-service',
        clientIp: '127.0.0.1',
      };

      const result = await strategy.select(instances, context);
      expect(result).toBe(instances[0]);
    });

    it('should consistently select the same instance for the same key', async () => {
      const instances: ServiceInstanceEntity[] = [
        {
          id: 'instance-1',
          serviceId: 'service-1',
          instanceId: 'test-instance-1',
          host: 'localhost',
          port: 3000,
          status: 'UP',
        } as ServiceInstanceEntity,
        {
          id: 'instance-2',
          serviceId: 'service-1',
          instanceId: 'test-instance-2',
          host: 'localhost',
          port: 3001,
          status: 'UP',
        } as ServiceInstanceEntity,
        {
          id: 'instance-3',
          serviceId: 'service-1',
          instanceId: 'test-instance-3',
          host: 'localhost',
          port: 3002,
          status: 'UP',
        } as ServiceInstanceEntity,
      ];

      const context: LoadBalancerContext = {
        serviceName: 'test-service',
        clientIp: '127.0.0.1',
        sessionId: 'test-session-1',
      };

      // 第一次选择
      const result1 = await strategy.select(instances, context);
      expect(result1).toBeDefined();

      // 第二次选择（应该与第一次相同）
      const result2 = await strategy.select(instances, context);
      expect(result2).toBe(result1);

      // 使用不同的会话ID
      const context2: LoadBalancerContext = {
        serviceName: 'test-service',
        clientIp: '127.0.0.1',
        sessionId: 'test-session-2',
      };

      // 第三次选择（可能与前两次不同）
      const result3 = await strategy.select(instances, context2);
      // 不做具体实例的断言，因为哈希结果是不确定的
      expect(result3).toBeDefined();
    });
  });

  describe('updateConfig', () => {
    it('should update configuration', () => {
      const config = {
        algorithm: LoadBalancerAlgorithm.CONSISTENT_HASH,
        parameters: {
          virtualNodeCount: 200,
        },
      };

      strategy.updateConfig(config);
      
      // 通过select方法间接测试配置是否生效
      const instances: ServiceInstanceEntity[] = Array(5).fill(0).map((_, i) => ({
        id: `instance-${i}`,
        serviceId: 'service-1',
        instanceId: `test-instance-${i}`,
        host: 'localhost',
        port: 3000 + i,
        status: 'UP',
      } as ServiceInstanceEntity));

      const context: LoadBalancerContext = {
        serviceName: 'test-service',
        clientIp: '127.0.0.1',
        sessionId: 'test-session-1',
      };

      // 执行select，不测试具体结果，只确保不抛出异常
      expect(async () => {
        await strategy.select(instances, context);
      }).not.toThrow();
    });
  });
});
