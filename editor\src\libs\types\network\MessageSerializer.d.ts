/**
 * 消息序列化器
 * 负责序列化和反序列化网络消息
 */
import { NetworkMessage } from './NetworkMessage';
/**
 * 压缩级别
 */
export declare enum CompressionLevel {
    /** 不压缩 */
    NONE = 0,
    /** 快速压缩 */
    FAST = 1,
    /** 标准压缩 */
    STANDARD = 2,
    /** 最大压缩 */
    MAX = 3
}
/**
 * 消息序列化器
 * 负责序列化和反序列化网络消息
 */
export declare class MessageSerializer {
    /** 是否使用压缩 */
    private useCompression;
    /** 压缩级别 */
    private compressionLevel;
    /** 文本编码器 */
    private encoder;
    /** 文本解码器 */
    private decoder;
    /** 消息缓存 */
    private messageCache;
    /** 缓存大小限制 */
    private cacheSizeLimit;
    /** 是否启用缓存 */
    private enableCache;
    /**
     * 创建消息序列化器
     * @param useCompression 是否使用压缩
     * @param compressionLevel 压缩级别
     * @param enableCache 是否启用缓存
     */
    constructor(useCompression?: boolean, compressionLevel?: CompressionLevel, enableCache?: boolean);
    /**
     * 序列化消息
     * @param message 消息对象
     * @returns 序列化后的数据
     */
    serialize(message: NetworkMessage): Promise<ArrayBuffer | string>;
    /**
     * 反序列化消息
     * @param data 序列化后的数据
     * @returns 消息对象
     */
    deserialize(data: ArrayBuffer | string): Promise<NetworkMessage>;
    /**
     * 压缩数据
     * @param data 原始数据
     * @returns 压缩后的数据
     */
    private compress;
    /**
     * 使用RLE算法压缩数据
     * @param data 原始数据
     * @returns 压缩后的数据
     */
    private compressRLE;
    /**
     * 解压缩数据
     * @param data 压缩后的数据
     * @returns 原始数据
     */
    private decompress;
    /**
     * 使用RLE算法解压缩数据
     * @param data 压缩后的数据
     * @returns 原始数据
     */
    private decompressRLE;
    /**
     * 计算缓存键
     * @param message 消息对象
     * @returns 缓存键
     */
    private calculateCacheKey;
    /**
     * 添加到缓存
     * @param key 缓存键
     * @param value 缓存值
     */
    private addToCache;
    /**
     * 清除缓存
     */
    clearCache(): void;
    /**
     * 设置是否使用压缩
     * @param useCompression 是否使用压缩
     */
    setUseCompression(useCompression: boolean): void;
    /**
     * 是否使用压缩
     * @returns 是否使用压缩
     */
    isUsingCompression(): boolean;
    /**
     * 设置压缩级别
     * @param level 压缩级别
     */
    setCompressionLevel(level: CompressionLevel): void;
    /**
     * 获取压缩级别
     * @returns 压缩级别
     */
    getCompressionLevel(): CompressionLevel;
    /**
     * 设置是否启用缓存
     * @param enable 是否启用缓存
     */
    setEnableCache(enable: boolean): void;
    /**
     * 是否启用缓存
     * @returns 是否启用缓存
     */
    isEnableCache(): boolean;
    /**
     * 设置缓存大小限制
     * @param limit 缓存大小限制
     */
    setCacheSizeLimit(limit: number): void;
    /**
     * 获取缓存大小限制
     * @returns 缓存大小限制
     */
    getCacheSizeLimit(): number;
}
