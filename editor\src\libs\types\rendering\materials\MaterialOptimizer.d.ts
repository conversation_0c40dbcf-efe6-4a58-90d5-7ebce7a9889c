/**
 * 材质优化器
 * 用于优化材质性能
 */
import * as THREE from 'three';
import { DeviceCapabilities } from '../../utils/DeviceCapabilities';
/**
 * 材质优化器配置接口
 */
export interface MaterialOptimizerOptions {
    /** 设备能力检测 */
    deviceCapabilities?: DeviceCapabilities;
    /** 是否启用纹理压缩 */
    enableTextureCompression?: boolean;
    /** 是否启用纹理大小限制 */
    enableTextureSizeLimit?: boolean;
    /** 最大纹理大小 */
    maxTextureSize?: number;
    /** 是否启用各向异性过滤 */
    enableAnisotropy?: boolean;
    /** 最大各向异性级别 */
    maxAnisotropy?: number;
    /** 是否启用MIP映射 */
    enableMipmap?: boolean;
    /** 是否启用着色器优化 */
    enableShaderOptimization?: boolean;
}
/**
 * 材质优化器类
 */
export declare class MaterialOptimizer {
    /** 设备能力检测 */
    private deviceCapabilities;
    /** 是否启用纹理压缩 */
    private enableTextureCompression;
    /** 是否启用纹理大小限制 */
    private enableTextureSizeLimit;
    /** 最大纹理大小 */
    private maxTextureSize;
    /** 是否启用各向异性过滤 */
    private enableAnisotropy;
    /** 最大各向异性级别 */
    private maxAnisotropy;
    /** 是否启用MIP映射 */
    private enableMipmap;
    /** 是否启用着色器优化 */
    private enableShaderOptimization;
    /**
     * 创建材质优化器
     * @param options 材质优化器配置
     */
    constructor(options?: MaterialOptimizerOptions);
    /**
     * 优化材质
     * @param material 材质
     * @returns 优化后的材质
     */
    optimizeMaterial(material: THREE.Material): THREE.Material;
    /**
     * 为低性能设备优化材质
     * @param material 材质
     * @returns 优化后的材质
     */
    private optimizeForLowPerformance;
    /**
     * 为中等性能设备优化材质
     * @param material 材质
     * @returns 优化后的材质
     */
    private optimizeForMediumPerformance;
    /**
     * 为高性能设备优化材质
     * @param material 材质
     * @returns 优化后的材质
     */
    private optimizeForHighPerformance;
    /**
     * 优化纹理
     * @param material 材质
     * @param maxSize 最大纹理大小
     * @param anisotropyLevel 各向异性级别
     */
    private optimizeTextures;
    /**
     * 优化纹理
     * @param texture 纹理
     * @param maxSize 最大纹理大小
     * @param anisotropyLevel 各向异性级别
     */
    private optimizeTexture;
    /**
     * 优化着色器
     * @param material 材质
     * @param simplify 是否简化
     */
    private optimizeShader;
    /**
     * 从材质中获取所有纹理
     * @param material 材质
     * @returns 纹理数组
     */
    private getTexturesFromMaterial;
    /**
     * 设置是否启用纹理压缩
     * @param enabled 是否启用
     */
    setEnableTextureCompression(enabled: boolean): void;
    /**
     * 设置是否启用纹理大小限制
     * @param enabled 是否启用
     */
    setEnableTextureSizeLimit(enabled: boolean): void;
    /**
     * 设置最大纹理大小
     * @param size 最大纹理大小
     */
    setMaxTextureSize(size: number): void;
    /**
     * 设置是否启用各向异性过滤
     * @param enabled 是否启用
     */
    setEnableAnisotropy(enabled: boolean): void;
    /**
     * 设置最大各向异性级别
     * @param level 最大各向异性级别
     */
    setMaxAnisotropy(level: number): void;
    /**
     * 设置是否启用MIP映射
     * @param enabled 是否启用
     */
    setEnableMipmap(enabled: boolean): void;
    /**
     * 设置是否启用着色器优化
     * @param enabled 是否启用
     */
    setEnableShaderOptimization(enabled: boolean): void;
}
