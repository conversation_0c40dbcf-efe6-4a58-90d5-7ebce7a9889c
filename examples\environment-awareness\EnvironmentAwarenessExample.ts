/**
 * 环境感知和响应示例
 * 
 * 本示例展示了如何使用环境感知和响应系统，使角色能够感知周围的环境并做出相应的反应。
 */

import { World } from '../../engine/src/core/World';
import { Scene } from '../../engine/src/core/Scene';
import { Entity } from '../../engine/src/core/Entity';
import { Vector3 } from 'three';
import { 
  EnvironmentAwarenessComponent, 
  EnvironmentResponseComponent, 
  EnvironmentAwarenessSystem,
  EnvironmentType,
  WeatherType,
  TerrainType,
  ResponseType,
  ResponsePriority,
  createRainyWeatherResponse,
  createSnowyWeatherResponse,
  createHotWeatherResponse,
  createDarkEnvironmentResponse,
  createUnderwaterResponse
} from '../../engine/src/environment';

/**
 * 环境感知示例类
 */
export class EnvironmentAwarenessExample {
  private world: World;
  private scene: Scene;
  private character: Entity;
  private environmentSystem: EnvironmentAwarenessSystem;
  private awarenessComponent: EnvironmentAwarenessComponent;
  private responseComponent: EnvironmentResponseComponent;
  private weatherChangeInterval: number = 30000; // 30秒
  private weatherTimer: any;
  private currentWeatherIndex: number = 0;
  private weatherTypes: WeatherType[] = [
    WeatherType.CLEAR,
    WeatherType.CLOUDY,
    WeatherType.RAINY,
    WeatherType.STORMY,
    WeatherType.SNOWY,
    WeatherType.FOGGY
  ];

  /**
   * 构造函数
   * @param world 世界
   */
  constructor(world: World) {
    this.world = world;
    this.scene = world.getActiveScene();
    
    // 创建角色
    this.character = this.createCharacter();
    
    // 添加环境感知组件
    this.awarenessComponent = this.addEnvironmentAwarenessComponent();
    
    // 添加环境响应组件
    this.responseComponent = this.addEnvironmentResponseComponent();
    
    // 创建环境感知系统
    this.environmentSystem = this.createEnvironmentAwarenessSystem();
    
    // 添加系统到世界
    this.world.addSystem(this.environmentSystem);
    
    // 添加自定义响应规则
    this.addCustomResponseRules();
    
    // 添加预设响应规则
    this.addPresetResponseRules();
    
    // 创建环境区域
    this.createEnvironmentZones();
    
    // 开始天气变化循环
    this.startWeatherChangeLoop();
    
    console.log('环境感知示例已初始化');
  }

  /**
   * 创建角色
   * @returns 角色实体
   */
  private createCharacter(): Entity {
    // 创建角色实体
    const character = new Entity('Character');
    
    // 添加变换组件
    character.addTransform({
      position: new Vector3(0, 0, 0),
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(1, 1, 1)
    });
    
    // 添加模型组件
    character.addModel('character.glb');
    
    // 添加动画组件
    character.addAnimator();
    
    // 添加音频组件
    character.addAudioSource();
    
    // 添加到场景
    this.scene.addEntity(character);
    
    return character;
  }

  /**
   * 添加环境感知组件
   * @returns 环境感知组件
   */
  private addEnvironmentAwarenessComponent(): EnvironmentAwarenessComponent {
    // 创建环境感知组件
    const awarenessComponent = new EnvironmentAwarenessComponent(this.character, {
      awarenessRange: 50, // 感知范围（米）
      updateFrequency: 1000, // 更新频率（毫秒）
      debug: true, // 启用调试模式
      autoDetect: true // 自动检测环境
    });
    
    // 添加组件到角色
    this.character.addComponent(awarenessComponent);
    
    // 注册环境变化回调
    awarenessComponent.onEnvironmentChange((data) => {
      console.log('环境变化:', data);
    });
    
    return awarenessComponent;
  }

  /**
   * 添加环境响应组件
   * @returns 环境响应组件
   */
  private addEnvironmentResponseComponent(): EnvironmentResponseComponent {
    // 创建环境响应组件
    const responseComponent = new EnvironmentResponseComponent(this.character, {
      autoRespond: true, // 自动响应
      debug: true // 启用调试模式
    });
    
    // 添加组件到角色
    this.character.addComponent(responseComponent);
    
    // 注册响应变化回调
    responseComponent.onResponseChange((activeResponses) => {
      console.log('响应变化:', activeResponses);
    });
    
    return responseComponent;
  }

  /**
   * 创建环境感知系统
   * @returns 环境感知系统
   */
  private createEnvironmentAwarenessSystem(): EnvironmentAwarenessSystem {
    // 创建环境感知系统
    const environmentSystem = new EnvironmentAwarenessSystem(this.world, {
      debug: true, // 启用调试模式
      updateFrequency: 1000, // 更新频率（毫秒）
      autoDetect: true, // 自动检测环境
      enableResponse: true, // 启用环境响应
      enableVisualization: true // 启用环境可视化
    });
    
    return environmentSystem;
  }

  /**
   * 添加自定义响应规则
   */
  private addCustomResponseRules(): void {
    // 创建高温响应规则
    const highTemperatureRule = {
      id: 'high_temperature_response',
      name: '高温响应',
      description: '角色在高温环境下的响应行为',
      responseType: ResponseType.ANIMATION,
      priority: ResponsePriority.MEDIUM,
      conditions: [
        {
          type: 'temperature',
          params: { minTemperature: 30 },
          evaluate: (data) => data.temperature >= 30
        }
      ],
      actions: [
        {
          type: ResponseType.ANIMATION,
          params: { animationName: 'character_hot_idle', blendTime: 0.5 },
          execute: (entity) => {
            const animator = entity.getAnimator();
            if (animator) {
              animator.playAnimation('character_hot_idle', { blendTime: 0.5, loop: true });
            }
          },
          stop: (entity) => {
            const animator = entity.getAnimator();
            if (animator) {
              animator.stopAnimation('character_hot_idle', { blendTime: 0.5 });
            }
          }
        },
        {
          type: ResponseType.SOUND,
          params: { soundName: 'character_hot_reaction', volume: 0.8 },
          execute: (entity) => {
            const audioSource = entity.getAudioSource();
            if (audioSource) {
              audioSource.play('character_hot_reaction', { volume: 0.8, loop: false });
            }
          },
          stop: (entity) => {
            const audioSource = entity.getAudioSource();
            if (audioSource) {
              audioSource.stop('character_hot_reaction');
            }
          }
        }
      ],
      cooldown: 15000, // 15秒冷却时间
      enabled: true
    };
    
    // 创建低光照响应规则
    const lowLightRule = {
      id: 'low_light_response',
      name: '低光照响应',
      description: '角色在低光照环境下的响应行为',
      responseType: ResponseType.ANIMATION,
      priority: ResponsePriority.HIGH,
      conditions: [
        {
          type: 'light',
          params: { maxIntensity: 0.3 },
          evaluate: (data) => data.lightIntensity <= 0.3
        }
      ],
      actions: [
        {
          type: ResponseType.ANIMATION,
          params: { animationName: 'character_dark_idle', blendTime: 0.5 },
          execute: (entity) => {
            const animator = entity.getAnimator();
            if (animator) {
              animator.playAnimation('character_dark_idle', { blendTime: 0.5, loop: true });
            }
          },
          stop: (entity) => {
            const animator = entity.getAnimator();
            if (animator) {
              animator.stopAnimation('character_dark_idle', { blendTime: 0.5 });
            }
          }
        }
      ],
      cooldown: 5000, // 5秒冷却时间
      enabled: true
    };
    
    // 添加规则到响应组件
    this.responseComponent.addRule(highTemperatureRule);
    this.responseComponent.addRule(lowLightRule);
  }

  /**
   * 添加预设响应规则
   */
  private addPresetResponseRules(): void {
    // 应用雨天预设
    this.responseComponent.addRule(createRainyWeatherResponse());
    
    // 应用雪天预设
    this.responseComponent.addRule(createSnowyWeatherResponse());
    
    // 应用炎热天气预设
    this.responseComponent.addRule(createHotWeatherResponse());
    
    // 应用黑暗环境预设
    this.responseComponent.addRule(createDarkEnvironmentResponse());
    
    // 应用水下环境预设
    this.responseComponent.addRule(createUnderwaterResponse());
  }

  /**
   * 创建环境区域
   */
  private createEnvironmentZones(): void {
    // 创建沙漠区域
    this.environmentSystem.addEnvironmentZone('desert_zone', {
      id: 'desert_zone',
      name: '沙漠区域',
      environmentData: {
        environmentType: EnvironmentType.DESERT,
        temperature: 40,
        humidity: 0.1,
        windSpeed: 5
      },
      containsPoint: (point) => {
        // 检查点是否在沙漠区域内
        return point.x > 100 && point.x < 200 && point.z > 100 && point.z < 200;
      }
    });
    
    // 创建森林区域
    this.environmentSystem.addEnvironmentZone('forest_zone', {
      id: 'forest_zone',
      name: '森林区域',
      environmentData: {
        environmentType: EnvironmentType.FOREST,
        temperature: 22,
        humidity: 0.7,
        windSpeed: 2
      },
      containsPoint: (point) => {
        // 检查点是否在森林区域内
        return point.x > -200 && point.x < -100 && point.z > -200 && point.z < -100;
      }
    });
    
    // 创建水域区域
    this.environmentSystem.addEnvironmentZone('water_zone', {
      id: 'water_zone',
      name: '水域区域',
      environmentData: {
        environmentType: EnvironmentType.UNDERWATER,
        waterLevel: 5,
        visibility: 20,
        temperature: 15
      },
      containsPoint: (point) => {
        // 检查点是否在水域区域内
        return point.x > -50 && point.x < 50 && point.z > 200 && point.z < 300;
      }
    });
  }

  /**
   * 开始天气变化循环
   */
  private startWeatherChangeLoop(): void {
    // 清除现有定时器
    if (this.weatherTimer) {
      clearInterval(this.weatherTimer);
    }
    
    // 设置定时器，定期改变天气
    this.weatherTimer = setInterval(() => {
      this.changeWeather();
    }, this.weatherChangeInterval);
  }

  /**
   * 改变天气
   */
  private changeWeather(): void {
    // 更新天气索引
    this.currentWeatherIndex = (this.currentWeatherIndex + 1) % this.weatherTypes.length;
    
    // 获取新天气
    const newWeather = this.weatherTypes[this.currentWeatherIndex];
    
    // 更新全局环境数据
    this.environmentSystem.setGlobalEnvironmentData({
      weatherType: newWeather,
      lastEnvironmentChangeTime: Date.now()
    });
    
    console.log(`天气已变更为: ${newWeather}`);
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    // 清除天气定时器
    if (this.weatherTimer) {
      clearInterval(this.weatherTimer);
    }
    
    // 移除系统
    this.world.removeSystem(this.environmentSystem);
    
    console.log('环境感知示例已清理');
  }
}
