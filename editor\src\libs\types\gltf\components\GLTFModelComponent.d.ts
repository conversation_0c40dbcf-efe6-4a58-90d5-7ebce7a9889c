/**
 * GLTF模型组件
 * 用于存储GLTF模型数据
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
export interface GLTF {
    scene: THREE.Group;
    scenes: THREE.Group[];
    animations: THREE.AnimationClip[];
    cameras: THREE.Camera[];
    asset: any;
    parser: any;
    userData: any;
}
/**
 * GLTF模型组件
 */
export declare class GLTFModelComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** GLTF模型 */
    private gltf;
    /** 模型URL */
    private url;
    /** 是否已加载 */
    private loaded;
    /** 加载错误 */
    private error;
    /**
     * 创建GLTF模型组件
     * @param gltf GLTF模型
     */
    constructor(gltf?: GLTF);
    /**
     * 获取GLTF模型
     * @returns GLTF模型
     */
    getGLTF(): GLTF | null;
    /**
     * 设置GLTF模型
     * @param gltf GLTF模型
     */
    setGLTF(gltf: GLTF): void;
    /**
     * 获取模型URL
     * @returns 模型URL
     */
    getURL(): string;
    /**
     * 设置模型URL
     * @param url 模型URL
     */
    setURL(url: string): void;
    /**
     * 是否已加载
     * @returns 是否已加载
     */
    isLoaded(): boolean;
    /**
     * 设置加载状态
     * @param loaded 是否已加载
     */
    setLoaded(loaded: boolean): void;
    /**
     * 获取加载错误
     * @returns 加载错误
     */
    getError(): Error | null;
    /**
     * 设置加载错误
     * @param error 加载错误
     */
    setError(error: Error | null): void;
    /**
     * 获取场景
     * @returns 场景
     */
    getScene(): THREE.Group | null;
    /**
     * 获取动画
     * @returns 动画数组
     */
    getAnimations(): THREE.AnimationClip[];
    /**
     * 获取相机
     * @returns 相机数组
     */
    getCameras(): THREE.Camera[];
    /**
     * 获取场景
     * @returns 场景数组
     */
    getScenes(): THREE.Group[];
    /**
     * 获取资产
     * @returns 资产
     */
    getAsset(): any;
    /**
     * 克隆组件
     * @returns 克隆的组件
     */
    clone(): GLTFModelComponent;
    /**
     * 销毁组件
     */
    dispose(): void;
    /**
     * 销毁材质
     * @param material 材质
     */
    private disposeMaterial;
}
