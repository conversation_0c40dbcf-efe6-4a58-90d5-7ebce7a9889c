import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsUUID,
  IsArray,
  IsObject,
  IsBoolean,
  MaxLength,
  MinLength,
  IsNotEmpty,
  ValidateNested,
  IsUrl,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TemplateType, TemplateComplexity, TemplateLicense } from '../entities/scene-template.entity';
import { CreateTemplateParameterDto } from '../../parameters/dto/create-parameter.dto';

export class CreateTemplateDto {
  @ApiProperty({
    description: '模板名称',
    example: '现代办公室模板',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({
    description: '模板描述',
    example: '一个现代化的办公室场景模板，包含办公桌、椅子、电脑等元素',
  })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  description?: string;

  @ApiPropertyOptional({
    description: '使用说明',
    example: '1. 选择办公桌样式\n2. 调整光照参数\n3. 设置材质属性',
  })
  @IsOptional()
  @IsString()
  @MaxLength(5000)
  instructions?: string;

  @ApiProperty({
    description: '模板类型',
    enum: TemplateType,
    example: TemplateType.OFFICE,
  })
  @IsEnum(TemplateType)
  type: TemplateType;

  @ApiPropertyOptional({
    description: '复杂度',
    enum: TemplateComplexity,
    example: TemplateComplexity.MEDIUM,
  })
  @IsOptional()
  @IsEnum(TemplateComplexity)
  complexity?: TemplateComplexity;

  @ApiPropertyOptional({
    description: '许可证类型',
    enum: TemplateLicense,
    example: TemplateLicense.FREE,
  })
  @IsOptional()
  @IsEnum(TemplateLicense)
  license?: TemplateLicense;

  @ApiProperty({
    description: '场景数据',
    example: {
      objects: [
        { id: '1', type: 'desk', position: [0, 0, 0], rotation: [0, 0, 0] },
        { id: '2', type: 'chair', position: [0, 0, -1], rotation: [0, 0, 0] },
      ],
      lighting: { ambient: 0.3, directional: { intensity: 1.0, direction: [1, -1, 1] } },
      environment: { skybox: 'office_hdri', fog: { enabled: false } },
    },
  })
  @IsObject()
  sceneData: Record<string, any>;

  @ApiPropertyOptional({
    description: '默认参数值',
    example: {
      deskColor: '#8B4513',
      lightIntensity: 1.0,
      roomSize: [10, 3, 8],
    },
  })
  @IsOptional()
  @IsObject()
  defaultParameters?: Record<string, any>;

  @ApiPropertyOptional({
    description: '元数据',
    example: {
      author: 'DL Engine Team',
      version: '1.0.0',
      tags: ['office', 'modern', 'workspace'],
      requirements: { minMemory: '2GB', minGPU: 'GTX 1060' },
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty({
    description: '分类ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  categoryId: string;

  @ApiPropertyOptional({
    description: '缩略图URL',
    example: 'https://example.com/thumbnails/office-template.jpg',
  })
  @IsOptional()
  @IsUrl()
  thumbnailUrl?: string;

  @ApiPropertyOptional({
    description: '预览图片URL数组',
    type: [String],
    example: [
      'https://example.com/previews/office-1.jpg',
      'https://example.com/previews/office-2.jpg',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  previewImages?: string[];

  @ApiPropertyOptional({
    description: '演示视频URL',
    example: 'https://example.com/videos/office-demo.mp4',
  })
  @IsOptional()
  @IsUrl()
  demoVideoUrl?: string;

  @ApiPropertyOptional({
    description: '是否公开',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({
    description: '标签数组',
    type: [String],
    example: ['office', 'modern', 'workspace', 'business'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: '基础模板ID（如果基于其他模板创建）',
    example: '456e7890-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  baseTemplateId?: string;

  @ApiPropertyOptional({
    description: '模板参数配置',
    type: [CreateTemplateParameterDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTemplateParameterDto)
  parameters?: CreateTemplateParameterDto[];
}

export class UpdateTemplateDto {
  @ApiPropertyOptional({
    description: '模板名称',
    minLength: 1,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  name?: string;

  @ApiPropertyOptional({
    description: '模板描述',
  })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  description?: string;

  @ApiPropertyOptional({
    description: '使用说明',
  })
  @IsOptional()
  @IsString()
  @MaxLength(5000)
  instructions?: string;

  @ApiPropertyOptional({
    description: '模板类型',
    enum: TemplateType,
  })
  @IsOptional()
  @IsEnum(TemplateType)
  type?: TemplateType;

  @ApiPropertyOptional({
    description: '复杂度',
    enum: TemplateComplexity,
  })
  @IsOptional()
  @IsEnum(TemplateComplexity)
  complexity?: TemplateComplexity;

  @ApiPropertyOptional({
    description: '许可证类型',
    enum: TemplateLicense,
  })
  @IsOptional()
  @IsEnum(TemplateLicense)
  license?: TemplateLicense;

  @ApiPropertyOptional({
    description: '场景数据',
  })
  @IsOptional()
  @IsObject()
  sceneData?: Record<string, any>;

  @ApiPropertyOptional({
    description: '默认参数值',
  })
  @IsOptional()
  @IsObject()
  defaultParameters?: Record<string, any>;

  @ApiPropertyOptional({
    description: '元数据',
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: '分类ID',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({
    description: '缩略图URL',
  })
  @IsOptional()
  @IsUrl()
  thumbnailUrl?: string;

  @ApiPropertyOptional({
    description: '预览图片URL数组',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  previewImages?: string[];

  @ApiPropertyOptional({
    description: '演示视频URL',
  })
  @IsOptional()
  @IsUrl()
  demoVideoUrl?: string;

  @ApiPropertyOptional({
    description: '是否公开',
  })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({
    description: '标签数组',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export class TemplateQueryDto {
  @ApiPropertyOptional({
    description: '搜索关键词',
    example: '办公室',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: '模板类型过滤',
    enum: TemplateType,
  })
  @IsOptional()
  @IsEnum(TemplateType)
  type?: TemplateType;

  @ApiPropertyOptional({
    description: '分类ID过滤',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({
    description: '复杂度过滤',
    enum: TemplateComplexity,
  })
  @IsOptional()
  @IsEnum(TemplateComplexity)
  complexity?: TemplateComplexity;

  @ApiPropertyOptional({
    description: '许可证类型过滤',
    enum: TemplateLicense,
  })
  @IsOptional()
  @IsEnum(TemplateLicense)
  license?: TemplateLicense;

  @ApiPropertyOptional({
    description: '标签过滤',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: '是否只显示公开模板',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  publicOnly?: boolean;

  @ApiPropertyOptional({
    description: '是否只显示精选模板',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  featuredOnly?: boolean;

  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    description: '每页数量',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  limit?: number = 20;

  @ApiPropertyOptional({
    description: '排序字段',
    example: 'createdAt',
    enum: ['name', 'createdAt', 'updatedAt', 'downloadCount', 'rating', 'useCount'],
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    description: '排序方向',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
