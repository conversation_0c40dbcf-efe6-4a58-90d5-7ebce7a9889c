/**
 * 场景预加载器
 * 负责场景资源的预加载和管理
 */
import { AssetManager } from '../assets/AssetManager';
import { AssetType } from '../assets/ResourceManager';
import { ResourcePreloader, PreloadGroupInfo } from '../assets/ResourcePreloader';
import { Scene } from './Scene';
import { EventEmitter } from '../utils/EventEmitter';
import type { Entity } from '../core/Entity';
import * as THREE from 'three';

/**
 * 场景资源类型
 */
export enum SceneResourceType {
  /** 纹理 */
  TEXTURE = 'texture',
  /** 模型 */
  MODEL = 'model',
  /** 音频 */
  AUDIO = 'audio',
  /** 字体 */
  FONT = 'font',
  /** 着色器 */
  SHADER = 'shader',
  /** 材质 */
  MATERIAL = 'material',
  /** 其他 */
  OTHER = 'other'
}

/**
 * 场景资源信息
 */
export interface SceneResourceInfo {
  /** 资源ID */
  id: string;
  /** 资源类型 */
  type: SceneResourceType;
  /** 资源URL */
  url: string;
  /** 资源优先级 */
  priority?: number;
  /** 资源大小（字节） */
  size?: number;
  /** 所属实体ID */
  entityId?: string;
  /** 所属组件类型 */
  componentType?: string;
  /** 是否必需 */
  required?: boolean;
}

/**
 * 场景预加载选项
 */
export interface ScenePreloaderOptions {
  /** 资产管理器 */
  assetManager: AssetManager;
  /** 是否自动分析场景资源 */
  autoAnalyzeResources?: boolean;
  /** 是否自动注册资源 */
  autoRegisterResources?: boolean;
  /** 最大并发加载数 */
  maxConcurrentLoads?: number;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 场景预加载进度信息
 */
export interface ScenePreloadProgressInfo {
  /** 场景ID */
  sceneId: string;
  /** 已加载资源数 */
  loaded: number;
  /** 总资源数 */
  total: number;
  /** 加载进度（0-1） */
  progress: number;
  /** 已加载资源列表 */
  loadedResources: string[];
  /** 加载失败资源列表 */
  failedResources: string[];
  /** 当前加载资源 */
  currentResource?: string;
  /** 已加载字节数 */
  loadedBytes: number;
  /** 总字节数 */
  totalBytes: number;
}

/**
 * 场景预加载器
 */
export class ScenePreloader extends EventEmitter {
  /** 资产管理器 */
  private assetManager: AssetManager;

  /** 资源预加载器 */
  private resourcePreloader: ResourcePreloader;

  /** 场景资源映射（场景ID -> 资源信息数组） */
  private sceneResources: Map<string, SceneResourceInfo[]> = new Map();

  /** 是否自动分析场景资源 */
  private autoAnalyzeResources: boolean;

  /** 是否自动注册资源 */
  private autoRegisterResources: boolean;

  /** 是否启用调试模式 */
  private debug: boolean;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景预加载器实例
   * @param options 预加载器选项
   */
  constructor(options: ScenePreloaderOptions) {
    super();

    this.assetManager = options.assetManager;
    this.autoAnalyzeResources = options.autoAnalyzeResources !== undefined ? options.autoAnalyzeResources : true;
    this.autoRegisterResources = options.autoRegisterResources !== undefined ? options.autoRegisterResources : true;
    this.debug = options.debug !== undefined ? options.debug : false;

    // 创建资源预加载器
    this.resourcePreloader = new ResourcePreloader({
      assetManager: this.assetManager,
      autoRegisterAssets: false, // 我们将手动注册资源
      autoLoadDependencies: true,
      maxConcurrentLoads: options.maxConcurrentLoads || 4,
      retryCount: 2
    });
  }

  /**
   * 初始化预加载器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 监听资源预加载器事件
    this.resourcePreloader.on('loadStart', (data) => {
      this.emit('loadStart', data);
    });

    this.resourcePreloader.on('loadProgress', (data) => {
      this.emit('loadProgress', data);
    });

    this.resourcePreloader.on('loadComplete', (data) => {
      this.emit('loadComplete', data);
    });

    this.resourcePreloader.on('loadError', (data) => {
      this.emit('loadError', data);
    });

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 分析场景资源
   * @param scene 场景实例
   * @returns 场景资源信息数组
   */
  public analyzeSceneResources(scene: Scene): SceneResourceInfo[] {
    if (!scene.id) {
      throw new Error('场景没有ID');
    }

    if (this.debug) {
      console.log(`[ScenePreloader] 分析场景资源: ${scene.name} (${scene.id})`);
    }

    // 获取场景中的所有实体
    const entities = scene.getEntities();

    // 收集所有资源
    const resources: SceneResourceInfo[] = [];

    // 分析场景天空盒资源
    const skybox = scene.getSkybox();
    if (skybox) {
      const skyboxResources = this.analyzeSkyboxResources(skybox);
      resources.push(...skyboxResources);
    }

    // 分析实体资源
    for (const entity of entities) {
      const entityResources = this.analyzeEntityResources(entity);
      resources.push(...entityResources);
    }

    // 存储场景资源
    this.sceneResources.set(scene.id, resources);

    // 发出资源分析完成事件
    this.emit('resourcesAnalyzed', { sceneId: scene.id, resources });

    return resources;
  }

  /**
   * 分析天空盒资源
   * @param skybox 天空盒实例
   * @returns 资源信息数组
   */
  private analyzeSkyboxResources(skybox: any): SceneResourceInfo[] {
    const resources: SceneResourceInfo[] = [];

    // 获取天空盒类型
    const skyboxType = skybox.getType();

    if (skyboxType === 'cubemap') {
      // 获取立方体贴图
      const cubeTexture = skybox.getCubeTexture();

      if (cubeTexture && cubeTexture.images) {
        // 添加立方体贴图资源
        for (let i = 0; i < cubeTexture.images.length; i++) {
          const image = cubeTexture.images[i];

          if (image && image.src) {
            resources.push({
              id: `skybox_cubemap_${i}`,
              type: SceneResourceType.TEXTURE,
              url: image.src,
              priority: 90, // 天空盒优先级较高
              required: true
            });
          }
        }
      }
    } else if (skyboxType === 'equirectangular') {
      // 获取全景贴图
      const texture = skybox.getTexture();

      if (texture && texture.image && texture.image.src) {
        resources.push({
          id: 'skybox_equirectangular',
          type: SceneResourceType.TEXTURE,
          url: texture.image.src,
          priority: 90, // 天空盒优先级较高
          required: true
        });
      }
    }

    return resources;
  }

  /**
   * 分析实体资源
   * @param entity 实体实例
   * @returns 资源信息数组
   */
  private analyzeEntityResources(entity: Entity): SceneResourceInfo[] {
    const resources: SceneResourceInfo[] = [];

    // 获取实体的所有组件
    const components = entity.getAllComponents();

    // 分析每个组件的资源
    for (const component of components) {
      const componentType = component.getType();

      // 根据组件类型分析资源
      switch (componentType) {
        case 'MeshComponent':
          this.analyzeMeshComponentResources(entity, component, resources);
          break;

        case 'AudioComponent':
          this.analyzeAudioComponentResources(entity, component, resources);
          break;

        case 'LightComponent':
          this.analyzeLightComponentResources(entity, component, resources);
          break;

        // 可以添加更多组件类型的分析
      }
    }

    // 递归分析子实体
    const children = entity.getChildren();
    for (const child of children) {
      const childResources = this.analyzeEntityResources(child);
      resources.push(...childResources);
    }

    return resources;
  }

  /**
   * 分析网格组件资源
   * @param entity 实体实例
   * @param component 组件实例
   * @param resources 资源信息数组
   */
  private analyzeMeshComponentResources(entity: Entity, component: any, resources: SceneResourceInfo[]): void {
    // 获取网格
    const mesh = component.getMesh();

    if (!mesh) {
      return;
    }

    // 分析材质
    if (mesh.material) {
      this.analyzeMaterialResources(entity, mesh.material, resources);
    }

    // 如果是模型，可能需要分析几何体
    // 这里简化处理，实际应用中可能需要更复杂的分析
  }

  /**
   * 分析材质资源
   * @param entity 实体实例
   * @param material 材质实例
   * @param resources 资源信息数组
   */
  private analyzeMaterialResources(entity: Entity, material: THREE.Material | THREE.Material[], resources: SceneResourceInfo[]): void {
    // 处理材质数组
    if (Array.isArray(material)) {
      for (const mat of material) {
        this.analyzeMaterialResources(entity, mat, resources);
      }
      return;
    }

    // 处理标准材质
    if (material instanceof THREE.MeshStandardMaterial) {
      // 分析贴图
      this.analyzeTextureResource(entity, 'map', material.map, resources);
      this.analyzeTextureResource(entity, 'normalMap', material.normalMap, resources);
      this.analyzeTextureResource(entity, 'roughnessMap', material.roughnessMap, resources);
      this.analyzeTextureResource(entity, 'metalnessMap', material.metalnessMap, resources);
      this.analyzeTextureResource(entity, 'aoMap', material.aoMap, resources);
      this.analyzeTextureResource(entity, 'emissiveMap', material.emissiveMap, resources);
      this.analyzeTextureResource(entity, 'displacementMap', material.displacementMap, resources);
    }
    // 可以添加更多材质类型的分析
  }

  /**
   * 分析贴图资源
   * @param entity 实体实例
   * @param mapType 贴图类型
   * @param texture 贴图实例
   * @param resources 资源信息数组
   */
  private analyzeTextureResource(entity: Entity, mapType: string, texture: THREE.Texture | null, resources: SceneResourceInfo[]): void {
    if (!texture || !texture.image || !texture.image.src) {
      return;
    }

    // 创建资源ID
    const resourceId = `texture_${entity.id}_${mapType}_${texture.uuid}`;

    // 添加贴图资源
    resources.push({
      id: resourceId,
      type: SceneResourceType.TEXTURE,
      url: texture.image.src,
      priority: mapType === 'map' ? 80 : 70, // 漫反射贴图优先级高一些
      entityId: entity.id,
      componentType: 'MeshComponent',
      required: mapType === 'map' // 漫反射贴图是必需的
    });
  }

  /**
   * 分析音频组件资源
   * @param entity 实体实例
   * @param component 组件实例
   * @param resources 资源信息数组
   */
  private analyzeAudioComponentResources(entity: Entity, component: any, resources: SceneResourceInfo[]): void {
    // 获取音频URL
    const audioUrl = component.getAudioUrl();

    if (!audioUrl) {
      return;
    }

    // 创建资源ID
    const resourceId = `audio_${entity.id}`;

    // 添加音频资源
    resources.push({
      id: resourceId,
      type: SceneResourceType.AUDIO,
      url: audioUrl,
      priority: 60, // 音频优先级较低
      entityId: entity.id,
      componentType: 'AudioComponent',
      required: false // 音频通常不是必需的
    });
  }

  /**
   * 分析光照组件资源
   * @param entity 实体实例
   * @param component 组件实例
   * @param resources 资源信息数组
   */
  private analyzeLightComponentResources(entity: Entity, component: any, resources: SceneResourceInfo[]): void {
    // 获取光照类型
    const lightType = component.getLightType();

    // 目前只有IES光照需要加载外部资源
    if (lightType === 'IES') {
      // 获取IES文件URL
      const iesUrl = component.getIESUrl();

      if (!iesUrl) {
        return;
      }

      // 创建资源ID
      const resourceId = `ies_${entity.id}`;

      // 添加IES资源
      resources.push({
        id: resourceId,
        type: SceneResourceType.OTHER,
        url: iesUrl,
        priority: 50, // IES优先级较低
        entityId: entity.id,
        componentType: 'LightComponent',
        required: false // IES通常不是必需的
      });
    }
  }
  /**
   * 注册场景资源
   * @param sceneId 场景ID
   * @returns 是否成功注册
   */
  public registerSceneResources(sceneId: string): boolean {
    // 检查场景资源是否存在
    if (!this.sceneResources.has(sceneId)) {
      return false;
    }

    const resources = this.sceneResources.get(sceneId)!;

    if (this.debug) {
      console.log(`[ScenePreloader] 注册场景资源: ${sceneId}, 资源数: ${resources.length}`);
    }

    // 注册每个资源
    for (const resource of resources) {
      this.registerResource(resource);
    }

    // 创建预加载组
    this.createPreloadGroup(sceneId, resources);

    return true;
  }

  /**
   * 注册资源
   * @param resource 资源信息
   * @returns 是否成功注册
   */
  private registerResource(resource: SceneResourceInfo): boolean {
    // 将场景资源类型转换为资产类型
    const assetType = this.convertResourceTypeToAssetType(resource.type);

    // 注册资产
    try {
      this.assetManager.registerAsset(
        resource.id,
        resource.id,
        assetType,
        resource.url,
        {
          entityId: resource.entityId,
          componentType: resource.componentType,
          required: resource.required,
          priority: resource.priority
        }
      );

      return true;
    } catch (error) {
      console.error(`注册资源失败: ${resource.id}`, error);
      return false;
    }
  }

  /**
   * 将场景资源类型转换为资产类型
   * @param resourceType 场景资源类型
   * @returns 资产类型
   */
  private convertResourceTypeToAssetType(resourceType: SceneResourceType): AssetType {
    switch (resourceType) {
      case SceneResourceType.TEXTURE:
        return AssetType.TEXTURE;

      case SceneResourceType.MODEL:
        return AssetType.MODEL;

      case SceneResourceType.AUDIO:
        return AssetType.AUDIO;

      case SceneResourceType.FONT:
        return AssetType.FONT;

      case SceneResourceType.SHADER:
        return AssetType.TEXT;

      case SceneResourceType.MATERIAL:
        return AssetType.JSON;

      default:
        return AssetType.BINARY;
    }
  }

  /**
   * 创建预加载组
   * @param sceneId 场景ID
   * @param resources 资源信息数组
   */
  private createPreloadGroup(sceneId: string, resources: SceneResourceInfo[]): void {
    // 将资源分为必需和非必需两组
    const requiredResources = resources.filter(resource => resource.required);
    const optionalResources = resources.filter(resource => !resource.required);

    // 创建必需资源组
    const requiredGroupInfo: PreloadGroupInfo = {
      name: `${sceneId}_required`,
      priority: 100,
      resources: requiredResources.map(resource => ({
        id: resource.id,
        type: this.convertResourceTypeToAssetType(resource.type),
        url: resource.url,
        priority: resource.priority || 0
      }))
    };

    // 创建非必需资源组
    const optionalGroupInfo: PreloadGroupInfo = {
      name: `${sceneId}_optional`,
      priority: 50,
      dependencies: [`${sceneId}_required`],
      resources: optionalResources.map(resource => ({
        id: resource.id,
        type: this.convertResourceTypeToAssetType(resource.type),
        url: resource.url,
        priority: resource.priority || 0
      }))
    };

    // 添加预加载组
    if (requiredGroupInfo.resources.length > 0) {
      this.resourcePreloader.addGroup(requiredGroupInfo);
    }

    if (optionalGroupInfo.resources.length > 0) {
      this.resourcePreloader.addGroup(optionalGroupInfo);
    }
  }

  /**
   * 加载资源
   * @param resourceIds 资源ID数组
   * @param options 加载选项
   */
  public loadResources(
    resourceIds: string[],
    options: {
      onProgress?: (progress: any) => void;
      onComplete?: (progress: any) => void;
      onError?: (error: any) => void;
      priority?: number;
      maxConcurrentLoads?: number;
      memoryLimit?: number;
      useLOD?: boolean;
      lodLevel?: number;
      useTextureCompression?: boolean;
      useGeometrySimplification?: boolean;
      useInstancing?: boolean;
    } = {}
  ): void {
    if (this.debug) {
      console.log(`[ScenePreloader] 加载资源: ${resourceIds.length} 个资源`);
    }

    // 创建加载进度信息
    const progressInfo = {
      total: resourceIds.length,
      loaded: 0,
      failed: 0,
      progress: 0,
      resources: []
    };

    // 如果没有资源需要加载，直接返回
    if (resourceIds.length === 0) {
      if (options.onComplete) {
        options.onComplete(progressInfo);
      }
      return;
    }

    // 发出加载开始事件
    this.emit('loadStart', progressInfo);

    // 调用进度回调
    if (options.onProgress) {
      options.onProgress(progressInfo);
    }

    // 设置最大并发加载数
    const maxConcurrentLoads = options.maxConcurrentLoads || 5;

    // 创建加载队列
    const queue = [...resourceIds];

    // 当前正在加载的资源数
    let activeLoads = 0;

    // 处理下一个资源
    const processNext = () => {
      // 如果队列为空或已达到最大并发加载数，则返回
      if (queue.length === 0 || activeLoads >= maxConcurrentLoads) {
        return;
      }

      // 获取下一个资源ID
      const resourceId = queue.shift();

      // 增加活动加载数
      activeLoads++;

      // 获取资源信息
      const resource = this.assetManager.getAssetInfo(resourceId);

      if (!resource) {
        console.warn(`找不到资源: ${resourceId}`);

        // 更新进度信息
        progressInfo.failed++;
        progressInfo.progress = progressInfo.loaded / progressInfo.total;

        // 减少活动加载数
        activeLoads--;

        // 处理下一个资源
        processNext();

        return;
      }

      // 应用优化选项
      const loadOptions: any = {
        priority: options.priority || (resource.metadata?.priority) || 1
      };

      // 应用LOD
      if (options.useLOD && options.lodLevel !== undefined) {
        loadOptions.lodLevel = options.lodLevel;
      }

      // 应用纹理压缩
      if (options.useTextureCompression) {
        loadOptions.useTextureCompression = true;
      }

      // 应用几何体简化
      if (options.useGeometrySimplification) {
        loadOptions.useGeometrySimplification = true;
      }

      // 应用实例化
      if (options.useInstancing) {
        loadOptions.useInstancing = true;
      }

      // 加载资源
      this.assetManager.loadAsset(resourceId)
        .then(() => {
          // 更新进度信息
          progressInfo.loaded++;
          progressInfo.progress = progressInfo.loaded / progressInfo.total;
          progressInfo.resources.push({
            id: resourceId,
            url: resource.url,
            type: resource.type,
            status: 'loaded'
          });

          // 发出加载进度事件
          this.emit('loadProgress', progressInfo);

          // 调用进度回调
          if (options.onProgress) {
            options.onProgress(progressInfo);
          }

          // 减少活动加载数
          activeLoads--;

          // 处理下一个资源
          processNext();

          // 检查是否所有资源都已加载
          if (progressInfo.loaded + progressInfo.failed === progressInfo.total) {
            // 发出加载完成事件
            this.emit('loadComplete', progressInfo);

            // 调用完成回调
            if (options.onComplete) {
              options.onComplete(progressInfo);
            }
          }
        })
        .catch((error) => {
          console.error(`加载资源失败: ${resourceId}`, error);

          // 更新进度信息
          progressInfo.failed++;
          progressInfo.progress = progressInfo.loaded / progressInfo.total;
          progressInfo.resources.push({
            id: resourceId,
            url: resource.url,
            type: resource.type,
            status: 'failed',
            error: error
          });

          // 发出加载错误事件
          this.emit('loadError', {
            id: resourceId,
            url: resource.url,
            type: resource.type,
            error: error
          });

          // 调用错误回调
          if (options.onError) {
            options.onError(error);
          }

          // 减少活动加载数
          activeLoads--;

          // 处理下一个资源
          processNext();

          // 检查是否所有资源都已加载
          if (progressInfo.loaded + progressInfo.failed === progressInfo.total) {
            // 发出加载完成事件
            this.emit('loadComplete', progressInfo);

            // 调用完成回调
            if (options.onComplete) {
              options.onComplete(progressInfo);
            }
          }
        });

      // 处理下一个资源
      processNext();
    };

    // 开始处理队列
    for (let i = 0; i < Math.min(maxConcurrentLoads, resourceIds.length); i++) {
      processNext();
    }
  }

  /**
   * 预加载场景资源
   * @param sceneId 场景ID
   * @param onProgress 进度回调
   * @returns Promise，解析为加载结果
   */
  public async preloadSceneResources(
    sceneId: string,
    onProgress?: (progress: ScenePreloadProgressInfo) => void
  ): Promise<ScenePreloadProgressInfo> {
    // 检查场景资源是否存在
    if (!this.sceneResources.has(sceneId)) {
      throw new Error(`找不到场景资源: ${sceneId}`);
    }

    if (this.debug) {
      console.log(`[ScenePreloader] 预加载场景资源: ${sceneId}`);
    }

    // 创建进度信息
    const progressInfo: ScenePreloadProgressInfo = {
      sceneId,
      loaded: 0,
      total: 0,
      progress: 0,
      loadedResources: [],
      failedResources: [],
      loadedBytes: 0,
      totalBytes: 0
    };

    // 获取资源总数
    const resources = this.sceneResources.get(sceneId)!;
    progressInfo.total = resources.length;

    // 计算总字节数
    for (const resource of resources) {
      progressInfo.totalBytes += resource.size || 0;
    }

    // 加载必需资源组
    try {
      const requiredResult = await this.resourcePreloader.loadGroup(
        `${sceneId}_required`,
        (groupProgress) => {
          // 更新进度信息
          progressInfo.loaded = groupProgress.loaded;
          progressInfo.loadedResources = groupProgress.loadedResources;
          progressInfo.failedResources = groupProgress.failedResources;
          progressInfo.progress = groupProgress.progress;

          // 调用进度回调
          if (onProgress) {
            onProgress(progressInfo);
          }
        }
      );

      // 尝试加载非必需资源组
      try {
        await this.resourcePreloader.loadGroup(
          `${sceneId}_optional`,
          (groupProgress) => {
            // 更新进度信息
            progressInfo.loaded = requiredResult.loaded + groupProgress.loaded;
            progressInfo.loadedResources = [
              ...requiredResult.loadedResources,
              ...groupProgress.loadedResources
            ];
            progressInfo.failedResources = [
              ...requiredResult.failedResources,
              ...groupProgress.failedResources
            ];
            progressInfo.progress = (progressInfo.loaded / progressInfo.total) || 0;

            // 调用进度回调
            if (onProgress) {
              onProgress(progressInfo);
            }
          }
        );
      } catch (error) {
        console.warn(`加载非必需资源失败: ${sceneId}`, error);
      }

      return progressInfo;
    } catch (error) {
      throw new Error(`加载必需资源失败: ${sceneId}, ${error.message}`);
    }
  }

  /**
   * 预加载场景
   * @param scene 场景实例
   * @param onProgress 进度回调
   * @returns Promise，解析为加载结果
   */
  public async preloadScene(
    scene: Scene,
    onProgress?: (progress: ScenePreloadProgressInfo) => void
  ): Promise<ScenePreloadProgressInfo> {
    if (!scene.id) {
      throw new Error('场景没有ID');
    }

    // 如果启用自动分析资源，则分析场景资源
    if (this.autoAnalyzeResources) {
      this.analyzeSceneResources(scene);
    }

    // 如果启用自动注册资源，则注册场景资源
    if (this.autoRegisterResources) {
      this.registerSceneResources(scene.id);
    }

    // 预加载场景资源
    return this.preloadSceneResources(scene.id, onProgress);
  }

  /**
   * 卸载场景资源
   * @param sceneId 场景ID
   * @returns 是否成功卸载
   */
  public unloadSceneResources(sceneId: string): boolean {
    // 检查场景资源是否存在
    if (!this.sceneResources.has(sceneId)) {
      return false;
    }

    if (this.debug) {
      console.log(`[ScenePreloader] 卸载场景资源: ${sceneId}`);
    }

    // 获取场景资源
    const resources = this.sceneResources.get(sceneId)!;

    // 卸载每个资源
    for (const resource of resources) {
      this.assetManager.unloadAsset(resource.id);
    }

    return true;
  }

  /**
   * 获取场景资源
   * @param sceneId 场景ID
   * @returns 场景资源信息数组
   */
  public getSceneResources(sceneId: string): SceneResourceInfo[] {
    return this.sceneResources.get(sceneId) || [];
  }

  /**
   * 清除场景资源
   * @param sceneId 场景ID
   * @returns 是否成功清除
   */
  public clearSceneResources(sceneId: string): boolean {
    // 检查场景资源是否存在
    if (!this.sceneResources.has(sceneId)) {
      return false;
    }

    // 卸载场景资源
    this.unloadSceneResources(sceneId);

    // 移除场景资源
    this.sceneResources.delete(sceneId);

    return true;
  }

  /**
   * 销毁预加载器
   */
  public dispose(): void {
    // 清空所有场景资源
    const sceneIds = Array.from(this.sceneResources.keys());
    for (const sceneId of sceneIds) {
      this.clearSceneResources(sceneId);
    }

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}