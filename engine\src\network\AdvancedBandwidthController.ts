/**
 * 高级带宽控制器
 * 提供更精细的带宽控制和优化策略
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';
import { NetworkQualityData, NetworkQualityLevel } from './NetworkQualityMonitor';
import { DataPriority } from './types';

/**
 * 带宽控制策略
 */
export enum BandwidthControlStrategy {
  /** 固定带宽 */
  FIXED = 'fixed',
  /** 自适应带宽 */
  ADAPTIVE = 'adaptive',
  /** 质量优先 */
  QUALITY_FIRST = 'quality_first',
  /** 性能优先 */
  PERFORMANCE_FIRST = 'performance_first',
  /** 优先级动态分配 */
  DYNAMIC_PRIORITY = 'dynamic_priority',
  /** 预测性带宽分配 */
  PREDICTIVE = 'predictive',
}

/**
 * 带宽分配模式
 */
export enum BandwidthAllocationMode {
  /** 平均分配 */
  EQUAL = 'equal',
  /** 按优先级分配 */
  PRIORITY = 'priority',
  /** 按需分配 */
  ON_DEMAND = 'on_demand',
  /** 动态分配 */
  DYNAMIC = 'dynamic',
}

/**
 * 带宽使用数据
 */
export interface BandwidthUsageData {
  /** 上行带宽使用（字节/秒） */
  upload: number;
  /** 下行带宽使用（字节/秒） */
  download: number;
  /** 上行带宽限制（字节/秒） */
  uploadLimit: number;
  /** 下行带宽限制（字节/秒） */
  downloadLimit: number;
  /** 上行带宽使用率（0-1） */
  uploadUsageRatio: number;
  /** 下行带宽使用率（0-1） */
  downloadUsageRatio: number;
  /** 按优先级的带宽使用 */
  priorityUsage: Map<DataPriority, number>;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 带宽分配配置
 */
export interface BandwidthAllocationConfig {
  /** 分配模式 */
  mode: BandwidthAllocationMode;
  /** 优先级分配比例 */
  priorityAllocation?: {
    [key in DataPriority]?: number;
  };
  /** 最小保证带宽（字节/秒） */
  minimumGuaranteed?: {
    [key in DataPriority]?: number;
  };
  /** 是否允许借用未使用带宽 */
  allowBorrowing?: boolean;
  /** 是否启用动态调整 */
  enableDynamicAdjustment?: boolean;
}

/**
 * 高级带宽控制器配置
 */
export interface AdvancedBandwidthControllerConfig {
  /** 最大上行带宽（字节/秒） */
  maxUploadBandwidth?: number;
  /** 最大下行带宽（字节/秒） */
  maxDownloadBandwidth?: number;
  /** 带宽控制策略 */
  strategy?: BandwidthControlStrategy;
  /** 带宽分配配置 */
  allocation?: BandwidthAllocationConfig;
  /** 带宽使用目标（0-1，表示最大带宽的使用比例） */
  targetUsage?: number;
  /** 是否启用自动调整 */
  autoAdjust?: boolean;
  /** 调整间隔（毫秒） */
  adjustInterval?: number;
  /** 是否启用突发流量控制 */
  enableBurstControl?: boolean;
  /** 突发流量最大倍率 */
  burstMultiplier?: number;
  /** 突发流量最大持续时间（毫秒） */
  burstDuration?: number;
  /** 是否启用预测性带宽分配 */
  enablePredictiveAllocation?: boolean;
  /** 预测窗口大小（毫秒） */
  predictiveWindowSize?: number;
  /** 是否启用带宽平滑过渡 */
  enableSmoothTransition?: boolean;
  /** 平滑因子（0-1） */
  smoothFactor?: number;
  /** 是否启用带宽使用历史记录 */
  enableHistory?: boolean;
  /** 历史记录大小 */
  historySize?: number;
}

/**
 * 带宽请求
 */
export interface BandwidthRequest {
  /** 请求ID */
  id: string;
  /** 数据大小（字节） */
  size: number;
  /** 优先级 */
  priority: DataPriority;
  /** 最大延迟（毫秒） */
  maxDelay: number;
  /** 请求时间 */
  timestamp: number;
  /** 是否已分配 */
  allocated: boolean;
  /** 分配的带宽（字节/秒） */
  allocatedBandwidth: number;
}

/**
 * 高级带宽控制器
 */
export class AdvancedBandwidthController extends EventEmitter {
  /** 配置 */
  private config: Required<AdvancedBandwidthControllerConfig>;

  /** 当前上行带宽限制（字节/秒） */
  private currentUploadLimit: number;

  /** 当前下行带宽限制（字节/秒） */
  private currentDownloadLimit: number;

  /** 当前上行带宽使用（字节/秒） */
  private currentUploadUsage: number = 0;

  /** 当前下行带宽使用（字节/秒） */
  private currentDownloadUsage: number = 0;

  /** 上行数据计数（当前周期） */
  private uploadByteCount: number = 0;

  /** 下行数据计数（当前周期） */
  private downloadByteCount: number = 0;

  /** 上次计数重置时间 */
  private lastResetTime: number = Date.now();

  /** 调整定时器ID */
  private adjustTimerId: number | null = null;

  /** 优先级带宽分配 */
  private priorityBandwidthAllocation: Map<DataPriority, number> = new Map();

  /** 优先级带宽使用 */
  private priorityBandwidthUsage: Map<DataPriority, number> = new Map();

  /** 带宽请求队列 */
  private bandwidthRequests: BandwidthRequest[] = [];

  /** 最近的网络质量数据 */
  private latestNetworkQuality: NetworkQualityData | null = null;

  /** 带宽使用历史 */
  private usageHistory: BandwidthUsageData[] = [];

  /** 突发流量状态 */
  private burstState: {
    active: boolean;
    startTime: number;
    originalUploadLimit: number;
    originalDownloadLimit: number;
  } = {
    active: false,
    startTime: 0,
    originalUploadLimit: 0,
    originalDownloadLimit: 0,
  };

  /** 带宽预测数据 */
  private bandwidthPrediction: {
    predictedUpload: number;
    predictedDownload: number;
    confidence: number;
  } = {
    predictedUpload: 0,
    predictedDownload: 0,
    confidence: 0,
  };

  /**
   * 创建高级带宽控制器
   * @param config 配置
   */
  constructor(config: AdvancedBandwidthControllerConfig = {}) {
    super();

    // 默认配置
    this.config = {
      maxUploadBandwidth: 1024 * 1024, // 1MB/s
      maxDownloadBandwidth: 1024 * 1024, // 1MB/s
      strategy: BandwidthControlStrategy.ADAPTIVE,
      allocation: {
        mode: BandwidthAllocationMode.PRIORITY,
        priorityAllocation: {
          [DataPriority.HIGHEST]: 0.4, // 40%
          [DataPriority.HIGH]: 0.3, // 30%
          [DataPriority.MEDIUM]: 0.2, // 20%
          [DataPriority.LOW]: 0.07, // 7%
          [DataPriority.LOWEST]: 0.03, // 3%
        },
        minimumGuaranteed: {
          [DataPriority.HIGHEST]: 102400, // 100KB/s
          [DataPriority.HIGH]: 51200, // 50KB/s
          [DataPriority.MEDIUM]: 20480, // 20KB/s
          [DataPriority.LOW]: 10240, // 10KB/s
          [DataPriority.LOWEST]: 5120, // 5KB/s
        },
        allowBorrowing: true,
        enableDynamicAdjustment: true,
      },
      targetUsage: 0.8, // 80%
      autoAdjust: true,
      adjustInterval: 1000, // 1秒
      enableBurstControl: true,
      burstMultiplier: 1.5,
      burstDuration: 5000, // 5秒
      enablePredictiveAllocation: true,
      predictiveWindowSize: 5000, // 5秒
      enableSmoothTransition: true,
      smoothFactor: 0.3,
      enableHistory: true,
      historySize: 60, // 60秒历史
      ...config,
    };

    // 初始化带宽限制
    this.currentUploadLimit = this.config.maxUploadBandwidth;
    this.currentDownloadLimit = this.config.maxDownloadBandwidth;

    // 初始化优先级带宽分配
    this.updatePriorityBandwidthAllocation();

    // 如果启用自动调整，则启动调整定时器
    if (this.config.autoAdjust) {
      this.startAutoAdjust();
    }

    // 初始化优先级带宽使用
    for (const priority of Object.values(DataPriority)) {
      if (typeof priority === 'number') {
        this.priorityBandwidthUsage.set(priority, 0);
      }
    }
  }

  /**
   * 启动自动调整
   */
  public startAutoAdjust(): void {
    if (this.adjustTimerId !== null) {
      return;
    }

    this.adjustTimerId = window.setInterval(() => {
      this.adjustBandwidth();
      this.resetCounters();
      this.processBandwidthRequests();
    }, this.config.adjustInterval);
  }

  /**
   * 停止自动调整
   */
  public stopAutoAdjust(): void {
    if (this.adjustTimerId !== null) {
      clearInterval(this.adjustTimerId);
      this.adjustTimerId = null;
    }
  }

  /**
   * 记录上行数据
   * @param bytes 字节数
   * @param priority 数据优先级
   */
  public recordUpload(bytes: number, priority: DataPriority = DataPriority.MEDIUM): void {
    this.uploadByteCount += bytes;

    // 更新优先级带宽使用
    const currentUsage = this.priorityBandwidthUsage.get(priority) || 0;
    this.priorityBandwidthUsage.set(priority, currentUsage + bytes);

    // 检查是否需要启动突发流量控制
    if (this.config.enableBurstControl && !this.burstState.active) {
      const now = Date.now();
      const elapsed = (now - this.lastResetTime) / 1000; // 转换为秒

      if (elapsed > 0) {
        const currentRate = this.uploadByteCount / elapsed;

        // 如果当前速率超过限制的90%，启动突发流量控制
        if (currentRate > this.currentUploadLimit * 0.9) {
          this.startBurstMode();
        }
      }
    }
  }

  /**
   * 记录下行数据
   * @param bytes 字节数
   * @param priority 数据优先级
   */
  public recordDownload(bytes: number, priority: DataPriority = DataPriority.MEDIUM): void {
    this.downloadByteCount += bytes;

    // 更新优先级带宽使用
    const currentUsage = this.priorityBandwidthUsage.get(priority) || 0;
    this.priorityBandwidthUsage.set(priority, currentUsage + bytes);
  }

  /**
   * 重置计数器
   */
  private resetCounters(): void {
    const now = Date.now();
    const elapsed = (now - this.lastResetTime) / 1000; // 转换为秒

    if (elapsed > 0) {
      // 计算当前带宽使用
      this.currentUploadUsage = this.uploadByteCount / elapsed;
      this.currentDownloadUsage = this.downloadByteCount / elapsed;

      // 记录带宽使用历史
      if (this.config.enableHistory) {
        const usageData: BandwidthUsageData = {
          upload: this.currentUploadUsage,
          download: this.currentDownloadUsage,
          uploadLimit: this.currentUploadLimit,
          downloadLimit: this.currentDownloadLimit,
          uploadUsageRatio: this.currentUploadLimit > 0 ? this.currentUploadUsage / this.currentUploadLimit : 0,
          downloadUsageRatio: this.currentDownloadLimit > 0 ? this.currentDownloadUsage / this.currentDownloadLimit : 0,
          priorityUsage: new Map(this.priorityBandwidthUsage),
          timestamp: now,
        };

        this.usageHistory.unshift(usageData);

        // 限制历史记录大小
        if (this.usageHistory.length > this.config.historySize) {
          this.usageHistory.pop();
        }
      }

      // 重置计数器
      this.uploadByteCount = 0;
      this.downloadByteCount = 0;
      this.lastResetTime = now;

      // 重置优先级带宽使用
      for (const priority of Object.values(DataPriority)) {
        if (typeof priority === 'number') {
          this.priorityBandwidthUsage.set(priority, 0);
        }
      }

      // 触发带宽使用更新事件
      this.emit('bandwidthUsageUpdated', this.getBandwidthUsage());
    }
  }

  /**
   * 调整带宽
   */
  private adjustBandwidth(): void {
    // 检查突发流量控制状态
    if (this.burstState.active) {
      const now = Date.now();

      // 如果突发流量持续时间已过，恢复正常带宽
      if (now - this.burstState.startTime >= this.config.burstDuration) {
        this.stopBurstMode();
      }
    }

    // 根据策略调整带宽
    switch (this.config.strategy) {
      case BandwidthControlStrategy.FIXED:
        // 固定带宽，不进行调整
        break;

      case BandwidthControlStrategy.ADAPTIVE:
        this.adjustAdaptive();
        break;

      case BandwidthControlStrategy.QUALITY_FIRST:
        this.adjustQualityFirst();
        break;

      case BandwidthControlStrategy.PERFORMANCE_FIRST:
        this.adjustPerformanceFirst();
        break;

      case BandwidthControlStrategy.DYNAMIC_PRIORITY:
        this.adjustDynamicPriority();
        break;

      case BandwidthControlStrategy.PREDICTIVE:
        this.adjustPredictive();
        break;
    }

    // 更新优先级带宽分配
    this.updatePriorityBandwidthAllocation();

    // 触发带宽调整事件
    this.emit('bandwidthAdjusted', this.getBandwidthUsage());
  }

  /**
   * 自适应调整带宽
   */
  private adjustAdaptive(): void {
    const now = Date.now();
    const elapsed = (now - this.lastResetTime) / 1000; // 转换为秒

    if (elapsed > 0) {
      // 计算使用率
      const uploadUsageRatio = this.currentUploadLimit > 0 ? this.currentUploadUsage / this.currentUploadLimit : 0;
      const downloadUsageRatio = this.currentDownloadLimit > 0 ? this.currentDownloadUsage / this.currentDownloadLimit : 0;

      // 根据使用率调整带宽限制
      if (uploadUsageRatio > this.config.targetUsage * 1.1) {
        // 使用率过高，降低限制
        this.currentUploadLimit = this.smoothAdjust(
          this.currentUploadLimit,
          Math.max(this.currentUploadLimit * 0.9, this.currentUploadUsage)
        );
      } else if (uploadUsageRatio < this.config.targetUsage * 0.8) {
        // 使用率过低，提高限制
        this.currentUploadLimit = this.smoothAdjust(
          this.currentUploadLimit,
          Math.min(this.currentUploadLimit * 1.1, this.config.maxUploadBandwidth)
        );
      }

      // 同样调整下行带宽
      if (downloadUsageRatio > this.config.targetUsage * 1.1) {
        this.currentDownloadLimit = this.smoothAdjust(
          this.currentDownloadLimit,
          Math.max(this.currentDownloadLimit * 0.9, this.currentDownloadUsage)
        );
      } else if (downloadUsageRatio < this.config.targetUsage * 0.8) {
        this.currentDownloadLimit = this.smoothAdjust(
          this.currentDownloadLimit,
          Math.min(this.currentDownloadLimit * 1.1, this.config.maxDownloadBandwidth)
        );
      }
    }

    // 如果有网络质量数据，根据网络质量进一步调整
    if (this.latestNetworkQuality) {
      this.adjustBasedOnNetworkQuality();
    }
  }

  /**
   * 质量优先调整带宽
   */
  private adjustQualityFirst(): void {
    // 如果没有网络质量数据，使用自适应调整
    if (!this.latestNetworkQuality) {
      this.adjustAdaptive();
      return;
    }

    // 根据网络质量调整带宽，优先保证质量
    switch (this.latestNetworkQuality.level) {
      case NetworkQualityLevel.EXCELLENT:
        // 网络质量极好，使用最大带宽
        this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth);
        this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth);
        break;

      case NetworkQualityLevel.GOOD:
        // 网络质量良好，使用90%带宽
        this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.9);
        this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.9);
        break;

      case NetworkQualityLevel.MEDIUM:
        // 网络质量一般，使用70%带宽
        this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.7);
        this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.7);
        break;

      case NetworkQualityLevel.BAD:
        // 网络质量差，使用50%带宽
        this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.5);
        this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.5);
        break;

      case NetworkQualityLevel.VERY_BAD:
        // 网络质量极差，使用30%带宽
        this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.3);
        this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.3);
        break;
    }
  }

  /**
   * 性能优先调整带宽
   */
  private adjustPerformanceFirst(): void {
    // 如果没有网络质量数据，使用自适应调整
    if (!this.latestNetworkQuality) {
      this.adjustAdaptive();
      return;
    }

    // 根据网络质量调整带宽，但更加激进地使用带宽
    switch (this.latestNetworkQuality.level) {
      case NetworkQualityLevel.EXCELLENT:
      case NetworkQualityLevel.GOOD:
        // 网络质量良好，使用最大带宽
        this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth);
        this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth);
        break;

      case NetworkQualityLevel.MEDIUM:
        // 网络质量一般，使用80%带宽
        this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.8);
        this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.8);
        break;

      case NetworkQualityLevel.BAD:
        // 网络质量差，使用60%带宽
        this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.6);
        this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.6);
        break;

      case NetworkQualityLevel.VERY_BAD:
        // 网络质量极差，使用40%带宽
        this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.4);
        this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.4);
        break;
    }
  }

  /**
   * 动态优先级调整带宽
   */
  private adjustDynamicPriority(): void {
    // 先使用自适应调整总带宽
    this.adjustAdaptive();

    // 然后根据实际使用情况动态调整优先级分配
    if (this.config.allocation.enableDynamicAdjustment) {
      const totalUploadUsage = Array.from(this.priorityBandwidthUsage.values()).reduce((sum, usage) => sum + usage, 0);

      if (totalUploadUsage > 0) {
        // 计算每个优先级的实际使用比例
        const usageRatios = new Map<DataPriority, number>();

        for (const [priority, usage] of this.priorityBandwidthUsage.entries()) {
          usageRatios.set(priority, usage / totalUploadUsage);
        }

        // 调整分配比例，向实际使用比例靠拢
        const allocation = this.config.allocation.priorityAllocation || {};
        const newAllocation: {[key in DataPriority]?: number} = {};

        for (const priority of Object.values(DataPriority)) {
          if (typeof priority === 'number') {
            const currentAllocation = allocation[priority] || 0;
            const usageRatio = usageRatios.get(priority) || 0;

            // 平滑调整分配比例
            newAllocation[priority] = this.smoothAdjust(currentAllocation, usageRatio);
          }
        }

        // 归一化分配比例
        const totalAllocation = Object.values(newAllocation).reduce((sum, value) => sum + value, 0);

        if (totalAllocation > 0) {
          for (const priority of Object.values(DataPriority)) {
            if (typeof priority === 'number') {
              newAllocation[priority] = (newAllocation[priority] || 0) / totalAllocation;
            }
          }

          // 更新分配比例
          this.config.allocation.priorityAllocation = newAllocation;
        }
      }
    }
  }

  /**
   * 预测性调整带宽
   */
  private adjustPredictive(): void {
    if (!this.config.enablePredictiveAllocation || this.usageHistory.length < 5) {
      // 如果没有足够的历史数据，使用自适应调整
      this.adjustAdaptive();
      return;
    }

    // 预测未来带宽使用
    this.predictBandwidthUsage();

    // 根据预测结果调整带宽
    if (this.bandwidthPrediction.confidence > 0.7) {
      // 预测可信度高，根据预测结果调整
      const predictedUploadRatio = this.bandwidthPrediction.predictedUpload / this.currentUploadLimit;
      const predictedDownloadRatio = this.bandwidthPrediction.predictedDownload / this.currentDownloadLimit;

      // 提前调整带宽以适应预测的使用
      if (predictedUploadRatio > this.config.targetUsage * 1.1) {
        // 预测使用率过高，提前增加带宽
        this.currentUploadLimit = this.smoothAdjust(
          this.currentUploadLimit,
          Math.min(this.currentUploadLimit * 1.2, this.config.maxUploadBandwidth)
        );
      } else if (predictedUploadRatio < this.config.targetUsage * 0.5) {
        // 预测使用率过低，提前减少带宽
        this.currentUploadLimit = this.smoothAdjust(
          this.currentUploadLimit,
          Math.max(this.currentUploadLimit * 0.9, this.bandwidthPrediction.predictedUpload * 1.2)
        );
      }

      // 同样调整下行带宽
      if (predictedDownloadRatio > this.config.targetUsage * 1.1) {
        this.currentDownloadLimit = this.smoothAdjust(
          this.currentDownloadLimit,
          Math.min(this.currentDownloadLimit * 1.2, this.config.maxDownloadBandwidth)
        );
      } else if (predictedDownloadRatio < this.config.targetUsage * 0.5) {
        this.currentDownloadLimit = this.smoothAdjust(
          this.currentDownloadLimit,
          Math.max(this.currentDownloadLimit * 0.9, this.bandwidthPrediction.predictedDownload * 1.2)
        );
      }
    } else {
      // 预测可信度低，使用自适应调整
      this.adjustAdaptive();
    }
  }

  /**
   * 根据网络质量调整带宽
   */
  private adjustBasedOnNetworkQuality(): void {
    if (!this.latestNetworkQuality) {
      return;
    }

    // 根据网络质量调整带宽
    switch (this.latestNetworkQuality.level) {
      case NetworkQualityLevel.EXCELLENT:
        // 网络质量极好，可以使用更多带宽
        this.currentUploadLimit = this.smoothAdjust(
          this.currentUploadLimit,
          Math.min(this.currentUploadLimit * 1.1, this.config.maxUploadBandwidth)
        );
        this.currentDownloadLimit = this.smoothAdjust(
          this.currentDownloadLimit,
          Math.min(this.currentDownloadLimit * 1.1, this.config.maxDownloadBandwidth)
        );
        break;

      case NetworkQualityLevel.GOOD:
        // 网络质量良好，略微增加带宽
        this.currentUploadLimit = this.smoothAdjust(
          this.currentUploadLimit,
          Math.min(this.currentUploadLimit * 1.05, this.config.maxUploadBandwidth * 0.9)
        );
        this.currentDownloadLimit = this.smoothAdjust(
          this.currentDownloadLimit,
          Math.min(this.currentDownloadLimit * 1.05, this.config.maxDownloadBandwidth * 0.9)
        );
        break;

      case NetworkQualityLevel.MEDIUM:
        // 网络质量一般，保持当前带宽
        break;

      case NetworkQualityLevel.BAD:
        // 网络质量差，减少带宽
        this.currentUploadLimit = this.smoothAdjust(
          this.currentUploadLimit,
          Math.min(this.currentUploadLimit * 0.9, this.config.maxUploadBandwidth * 0.7)
        );
        this.currentDownloadLimit = this.smoothAdjust(
          this.currentDownloadLimit,
          Math.min(this.currentDownloadLimit * 0.9, this.config.maxDownloadBandwidth * 0.7)
        );
        break;

      case NetworkQualityLevel.VERY_BAD:
        // 网络质量极差，大幅减少带宽
        this.currentUploadLimit = this.smoothAdjust(
          this.currentUploadLimit,
          Math.min(this.currentUploadLimit * 0.8, this.config.maxUploadBandwidth * 0.5)
        );
        this.currentDownloadLimit = this.smoothAdjust(
          this.currentDownloadLimit,
          Math.min(this.currentDownloadLimit * 0.8, this.config.maxDownloadBandwidth * 0.5)
        );
        break;
    }
  }

  /**
   * 预测带宽使用
   */
  private predictBandwidthUsage(): void {
    if (this.usageHistory.length < 5) {
      // 历史数据不足，无法预测
      this.bandwidthPrediction = {
        predictedUpload: this.currentUploadUsage,
        predictedDownload: this.currentDownloadUsage,
        confidence: 0,
      };
      return;
    }

    // 使用线性回归预测未来带宽使用
    const uploadHistory = this.usageHistory.map(data => data.upload);
    const downloadHistory = this.usageHistory.map(data => data.download);

    // 简单线性回归
    const uploadTrend = this.calculateLinearTrend(uploadHistory);
    const downloadTrend = this.calculateLinearTrend(downloadHistory);

    // 预测未来带宽使用
    const predictedUpload = uploadHistory[0] + uploadTrend.slope;
    const predictedDownload = downloadHistory[0] + downloadTrend.slope;

    // 计算预测可信度
    const uploadConfidence = Math.min(1, Math.max(0, 1 - uploadTrend.error));
    const downloadConfidence = Math.min(1, Math.max(0, 1 - downloadTrend.error));
    const confidence = (uploadConfidence + downloadConfidence) / 2;

    // 更新预测结果
    this.bandwidthPrediction = {
      predictedUpload: Math.max(0, predictedUpload),
      predictedDownload: Math.max(0, predictedDownload),
      confidence,
    };
  }

  /**
   * 计算线性趋势
   * @param data 数据
   * @returns 趋势
   */
  private calculateLinearTrend(data: number[]): { slope: number; error: number } {
    if (data.length < 2) {
      return { slope: 0, error: 1 };
    }

    // 计算平均值
    const sum = data.reduce((acc, value) => acc + value, 0);
    const mean = sum / data.length;

    // 计算斜率
    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < data.length; i++) {
      const x = data.length - i - 1;
      const y = data[i];

      numerator += (x - (data.length - 1) / 2) * (y - mean);
      denominator += Math.pow(x - (data.length - 1) / 2, 2);
    }

    const slope = denominator !== 0 ? numerator / denominator : 0;

    // 计算误差
    let errorSum = 0;
    for (let i = 0; i < data.length; i++) {
      const x = data.length - i - 1;
      const y = data[i];
      const predicted = mean + slope * (x - (data.length - 1) / 2);

      errorSum += Math.pow(y - predicted, 2);
    }

    const error = Math.sqrt(errorSum / data.length) / (mean || 1);

    return { slope, error };
  }

  /**
   * 平滑调整数值
   * @param currentValue 当前值
   * @param targetValue 目标值
   * @returns 调整后的值
   */
  private smoothAdjust(currentValue: number, targetValue: number): number {
    if (!this.config.enableSmoothTransition) {
      return targetValue;
    }

    const factor = this.config.smoothFactor;
    return currentValue * (1 - factor) + targetValue * factor;
  }

  /**
   * 更新优先级带宽分配
   */
  private updatePriorityBandwidthAllocation(): void {
    const allocation = this.config.allocation;

    if (allocation.mode === BandwidthAllocationMode.PRIORITY) {
      // 按优先级分配带宽
      const priorityAllocation = allocation.priorityAllocation || {};

      for (const priority of Object.values(DataPriority)) {
        if (typeof priority === 'number') {
          const ratio = priorityAllocation[priority] || 0;
          const bandwidth = this.currentUploadLimit * ratio;

          this.priorityBandwidthAllocation.set(priority, bandwidth);
        }
      }
    } else if (allocation.mode === BandwidthAllocationMode.EQUAL) {
      // 平均分配带宽
      const priorityCount = Object.keys(DataPriority).filter(key => !isNaN(Number(key))).length;
      const bandwidthPerPriority = this.currentUploadLimit / priorityCount;

      for (const priority of Object.values(DataPriority)) {
        if (typeof priority === 'number') {
          this.priorityBandwidthAllocation.set(priority, bandwidthPerPriority);
        }
      }
    }

    // 确保最小保证带宽
    if (allocation.minimumGuaranteed) {
      for (const priority of Object.values(DataPriority)) {
        if (typeof priority === 'number') {
          const minBandwidth = allocation.minimumGuaranteed[priority] || 0;
          const currentBandwidth = this.priorityBandwidthAllocation.get(priority) || 0;

          if (currentBandwidth < minBandwidth) {
            this.priorityBandwidthAllocation.set(priority, minBandwidth);
          }
        }
      }
    }
  }

  /**
   * 启动突发流量模式
   */
  private startBurstMode(): void {
    if (this.burstState.active) {
      return;
    }

    // 记录当前带宽限制
    this.burstState.originalUploadLimit = this.currentUploadLimit;
    this.burstState.originalDownloadLimit = this.currentDownloadLimit;

    // 增加带宽限制
    this.currentUploadLimit *= this.config.burstMultiplier;
    this.currentDownloadLimit *= this.config.burstMultiplier;

    // 更新状态
    this.burstState.active = true;
    this.burstState.startTime = Date.now();

    // 更新优先级带宽分配
    this.updatePriorityBandwidthAllocation();

    // 触发突发流量开始事件
    this.emit('burstModeStarted', {
      originalUploadLimit: this.burstState.originalUploadLimit,
      originalDownloadLimit: this.burstState.originalDownloadLimit,
      currentUploadLimit: this.currentUploadLimit,
      currentDownloadLimit: this.currentDownloadLimit,
      duration: this.config.burstDuration,
    });
  }

  /**
   * 停止突发流量模式
   */
  private stopBurstMode(): void {
    if (!this.burstState.active) {
      return;
    }

    // 恢复原始带宽限制
    this.currentUploadLimit = this.burstState.originalUploadLimit;
    this.currentDownloadLimit = this.burstState.originalDownloadLimit;

    // 更新状态
    this.burstState.active = false;

    // 更新优先级带宽分配
    this.updatePriorityBandwidthAllocation();

    // 触发突发流量结束事件
    this.emit('burstModeEnded', {
      currentUploadLimit: this.currentUploadLimit,
      currentDownloadLimit: this.currentDownloadLimit,
    });
  }

  /**
   * 处理带宽请求
   */
  private processBandwidthRequests(): void {
    if (this.bandwidthRequests.length === 0) {
      return;
    }

    // 按优先级排序请求
    this.bandwidthRequests.sort((a, b) => a.priority - b.priority);

    // 计算可用带宽
    const availableBandwidth = this.currentUploadLimit - this.currentUploadUsage;

    if (availableBandwidth <= 0) {
      // 没有可用带宽，延迟处理
      return;
    }

    // 处理请求
    let remainingBandwidth = availableBandwidth;
    const now = Date.now();
    const allocatedRequests: BandwidthRequest[] = [];

    for (const request of this.bandwidthRequests) {
      // 检查请求是否已过期
      if (now - request.timestamp > request.maxDelay) {
        // 请求已过期，移除
        continue;
      }

      // 获取该优先级的分配带宽
      const priorityBandwidth = this.priorityBandwidthAllocation.get(request.priority) || 0;

      // 计算可分配的带宽
      let allocatableBandwidth = Math.min(remainingBandwidth, priorityBandwidth);

      // 如果允许借用未使用带宽，则可以使用更多带宽
      if (this.config.allocation.allowBorrowing) {
        allocatableBandwidth = remainingBandwidth;
      }

      // 如果可分配带宽足够，则分配
      if (allocatableBandwidth >= request.size) {
        request.allocated = true;
        request.allocatedBandwidth = allocatableBandwidth;

        remainingBandwidth -= allocatableBandwidth;
        allocatedRequests.push(request);
      }
    }

    // 移除已分配的请求
    this.bandwidthRequests = this.bandwidthRequests.filter(request => !allocatedRequests.includes(request));

    // 触发带宽分配事件
    if (allocatedRequests.length > 0) {
      this.emit('bandwidthAllocated', {
        requests: allocatedRequests,
        remainingBandwidth,
      });
    }
  }

  /**
   * 请求带宽
   * @param size 数据大小（字节）
   * @param priority 优先级
   * @param maxDelay 最大延迟（毫秒）
   * @returns 请求ID
   */
  public requestBandwidth(size: number, priority: DataPriority = DataPriority.MEDIUM, maxDelay: number = 5000): string {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 创建请求
    const request: BandwidthRequest = {
      id: requestId,
      size,
      priority,
      maxDelay,
      timestamp: Date.now(),
      allocated: false,
      allocatedBandwidth: 0,
    };

    // 添加到请求队列
    this.bandwidthRequests.push(request);

    // 立即处理请求
    this.processBandwidthRequests();

    return requestId;
  }

  /**
   * 取消带宽请求
   * @param requestId 请求ID
   * @returns 是否成功取消
   */
  public cancelBandwidthRequest(requestId: string): boolean {
    const index = this.bandwidthRequests.findIndex(request => request.id === requestId);

    if (index !== -1) {
      this.bandwidthRequests.splice(index, 1);
      return true;
    }

    return false;
  }

  /**
   * 设置网络质量数据
   * @param quality 网络质量数据
   */
  public setNetworkQuality(quality: NetworkQualityData): void {
    this.latestNetworkQuality = quality;
  }

  /**
   * 设置最大带宽
   * @param uploadBandwidth 上行带宽（字节/秒）
   * @param downloadBandwidth 下行带宽（字节/秒）
   */
  public setMaxBandwidth(uploadBandwidth: number, downloadBandwidth: number): void {
    this.config.maxUploadBandwidth = uploadBandwidth;
    this.config.maxDownloadBandwidth = downloadBandwidth;

    // 如果当前限制超过最大值，则调整
    if (this.currentUploadLimit > uploadBandwidth) {
      this.currentUploadLimit = uploadBandwidth;
    }

    if (this.currentDownloadLimit > downloadBandwidth) {
      this.currentDownloadLimit = downloadBandwidth;
    }

    // 更新优先级带宽分配
    this.updatePriorityBandwidthAllocation();
  }

  /**
   * 获取带宽使用数据
   * @returns 带宽使用数据
   */
  public getBandwidthUsage(): BandwidthUsageData {
    return {
      upload: this.currentUploadUsage,
      download: this.currentDownloadUsage,
      uploadLimit: this.currentUploadLimit,
      downloadLimit: this.currentDownloadLimit,
      uploadUsageRatio: this.currentUploadLimit > 0 ? this.currentUploadUsage / this.currentUploadLimit : 0,
      downloadUsageRatio: this.currentDownloadLimit > 0 ? this.currentDownloadUsage / this.currentDownloadLimit : 0,
      priorityUsage: new Map(this.priorityBandwidthUsage),
      timestamp: Date.now(),
    };
  }

  /**
   * 获取带宽使用历史
   * @returns 带宽使用历史
   */
  public getBandwidthUsageHistory(): BandwidthUsageData[] {
    return [...this.usageHistory];
  }

  /**
   * 获取带宽预测
   * @returns 带宽预测
   */
  public getBandwidthPrediction(): {
    predictedUpload: number;
    predictedDownload: number;
    confidence: number;
  } {
    return { ...this.bandwidthPrediction };
  }

  /**
   * 设置带宽控制策略
   * @param strategy 带宽控制策略
   */
  public setStrategy(strategy: BandwidthControlStrategy): void {
    this.config.strategy = strategy;
  }

  /**
   * 设置带宽分配配置
   * @param allocation 带宽分配配置
   */
  public setAllocationConfig(allocation: BandwidthAllocationConfig): void {
    this.config.allocation = allocation;

    // 更新优先级带宽分配
    this.updatePriorityBandwidthAllocation();
  }

  /**
   * 重置带宽控制器
   */
  public reset(): void {
    // 重置带宽限制
    this.currentUploadLimit = this.config.maxUploadBandwidth;
    this.currentDownloadLimit = this.config.maxDownloadBandwidth;

    // 重置计数器
    this.uploadByteCount = 0;
    this.downloadByteCount = 0;
    this.currentUploadUsage = 0;
    this.currentDownloadUsage = 0;
    this.lastResetTime = Date.now();

    // 重置优先级带宽使用
    for (const priority of Object.values(DataPriority)) {
      if (typeof priority === 'number') {
        this.priorityBandwidthUsage.set(priority, 0);
      }
    }

    // 更新优先级带宽分配
    this.updatePriorityBandwidthAllocation();

    // 清空请求队列
    this.bandwidthRequests = [];

    // 停止突发流量模式
    if (this.burstState.active) {
      this.stopBurstMode();
    }

    // 清空历史记录
    this.usageHistory = [];

    // 重置预测数据
    this.bandwidthPrediction = {
      predictedUpload: 0,
      predictedDownload: 0,
      confidence: 0,
    };
  }

  /**
   * 销毁带宽控制器
   */
  public dispose(): void {
    this.stopAutoAdjust();
    this.removeAllListeners();
    this.reset();
  }
}
