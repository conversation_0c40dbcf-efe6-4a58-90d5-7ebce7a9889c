/**
 * 增强压缩模块
 */
import { DynamicModule, Module, Provider } from '@nestjs/common';
import { EnhancedMessageCompressorService, EnhancedCompressionOptions } from './enhanced-message-compressor.service';

/**
 * 增强压缩模块配置
 */
export interface EnhancedCompressionModuleOptions {
  /**
   * 是否全局注册
   */
  isGlobal?: boolean;
  
  /**
   * 压缩配置
   */
  config?: EnhancedCompressionOptions;
}

@Module({})
export class EnhancedCompressionModule {
  /**
   * 注册增强压缩模块
   * @param options 模块配置
   */
  static register(options: EnhancedCompressionModuleOptions = {}): DynamicModule {
    const providers: Provider[] = [
      {
        provide: 'COMPRESSION_OPTIONS',
        useValue: options.config || {},
      },
      {
        provide: EnhancedMessageCompressorService,
        useFactory: (config: EnhancedCompressionOptions) => {
          return new EnhancedMessageCompressorService(config);
        },
        inject: ['COMPRESSION_OPTIONS'],
      },
    ];
    
    return {
      global: options.isGlobal,
      module: EnhancedCompressionModule,
      providers,
      exports: [EnhancedMessageCompressorService],
    };
  }
}
