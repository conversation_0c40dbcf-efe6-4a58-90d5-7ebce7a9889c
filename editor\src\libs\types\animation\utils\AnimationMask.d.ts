/**
 * 动画遮罩
 * 用于控制动画只影响特定的骨骼或属性
 */
import * as THREE from 'three';
export interface MaskRule {
    /** 目标路径（骨骼名称或属性路径） */
    path: string;
    /** 权重（0-1） */
    weight: number;
    /** 是否递归应用到子骨骼 */
    recursive?: boolean;
}
export declare class AnimationMask {
    /** 遮罩名称 */
    readonly name: string;
    /** 遮罩规则 */
    private rules;
    /** 默认权重 */
    private defaultWeight;
    constructor(name: string, defaultWeight?: number);
    /**
     * 添加遮罩规则
     */
    addRule(path: string, weight: number, recursive?: boolean): void;
    /**
     * 移除遮罩规则
     */
    removeRule(path: string): boolean;
    /**
     * 获取遮罩规则
     */
    getRule(path: string): MaskRule | null;
    /**
     * 获取所有规则
     */
    getRules(): MaskRule[];
    /**
     * 获取路径的权重
     */
    getWeight(path: string): number;
    /**
     * 设置默认权重
     */
    setDefaultWeight(weight: number): void;
    /**
     * 获取默认权重
     */
    getDefaultWeight(): number;
    /**
     * 应用遮罩到动画片段
     */
    applyToClip(clip: THREE.AnimationClip): THREE.AnimationClip;
    /**
     * 应用权重到轨道
     */
    private applyWeightToTrack;
    /**
     * 合并遮罩
     */
    merge(other: AnimationMask): AnimationMask;
    /**
     * 反转遮罩
     */
    invert(): AnimationMask;
    /**
     * 克隆遮罩
     */
    clone(): AnimationMask;
    /**
     * 序列化遮罩
     */
    serialize(): any;
    /**
     * 反序列化遮罩
     */
    static deserialize(data: any): AnimationMask;
    /**
     * 获取遮罩名称
     */
    getName(): string;
    /**
     * 创建上半身遮罩
     */
    static createUpperBodyMask(): AnimationMask;
    /**
     * 创建下半身遮罩
     */
    static createLowerBodyMask(): AnimationMask;
    /**
     * 创建左手遮罩
     */
    static createLeftHandMask(): AnimationMask;
    /**
     * 创建右手遮罩
     */
    static createRightHandMask(): AnimationMask;
    /**
     * 设置缓存启用状态
     */
    setCacheEnabled(enabled: boolean): void;
    /**
     * 设置对象池启用状态
     */
    setObjectPoolEnabled(enabled: boolean): void;
}
