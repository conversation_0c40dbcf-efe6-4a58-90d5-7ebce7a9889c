/**
 * 三维向量类
 * 表示三维空间中的点或向量
 */
export class Vector3 {
  /** X分量 */
  public x: number;
  
  /** Y分量 */
  public y: number;
  
  /** Z分量 */
  public z: number;
  
  /**
   * 创建三维向量
   * @param x X分量
   * @param y Y分量
   * @param z Z分量
   */
  constructor(x: number = 0, y: number = 0, z: number = 0) {
    this.x = x;
    this.y = y;
    this.z = z;
  }
  
  /**
   * 设置向量分量
   * @param x X分量
   * @param y Y分量
   * @param z Z分量
   * @returns 当前向量
   */
  public set(x: number, y: number, z: number): Vector3 {
    this.x = x;
    this.y = y;
    this.z = z;
    return this;
  }
  
  /**
   * 复制另一个向量的值
   * @param v 要复制的向量
   * @returns 当前向量
   */
  public copy(v: Vector3): Vector3 {
    this.x = v.x;
    this.y = v.y;
    this.z = v.z;
    return this;
  }
  
  /**
   * 克隆向量
   * @returns 新的向量实例
   */
  public clone(): Vector3 {
    return new Vector3(this.x, this.y, this.z);
  }
  
  /**
   * 向量加法
   * @param v 要加的向量
   * @returns 当前向量
   */
  public add(v: Vector3): Vector3 {
    this.x += v.x;
    this.y += v.y;
    this.z += v.z;
    return this;
  }
  
  /**
   * 向量减法
   * @param v 要减的向量
   * @returns 当前向量
   */
  public subtract(v: Vector3): Vector3 {
    this.x -= v.x;
    this.y -= v.y;
    this.z -= v.z;
    return this;
  }
  
  /**
   * 向量乘以标量
   * @param s 标量
   * @returns 当前向量
   */
  public multiplyScalar(s: number): Vector3 {
    this.x *= s;
    this.y *= s;
    this.z *= s;
    return this;
  }
  
  /**
   * 向量除以标量
   * @param s 标量
   * @returns 当前向量
   */
  public divideScalar(s: number): Vector3 {
    if (s !== 0) {
      const invScalar = 1 / s;
      this.x *= invScalar;
      this.y *= invScalar;
      this.z *= invScalar;
    } else {
      this.x = 0;
      this.y = 0;
      this.z = 0;
    }
    return this;
  }
  
  /**
   * 向量点积
   * @param v 另一个向量
   * @returns 点积结果
   */
  public dot(v: Vector3): number {
    return this.x * v.x + this.y * v.y + this.z * v.z;
  }
  
  /**
   * 向量叉积
   * @param v 另一个向量
   * @returns 当前向量
   */
  public cross(v: Vector3): Vector3 {
    const x = this.y * v.z - this.z * v.y;
    const y = this.z * v.x - this.x * v.z;
    const z = this.x * v.y - this.y * v.x;
    
    this.x = x;
    this.y = y;
    this.z = z;
    
    return this;
  }
  
  /**
   * 计算向量长度
   * @returns 向量长度
   */
  public length(): number {
    return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
  }
  
  /**
   * 计算向量长度的平方
   * @returns 向量长度的平方
   */
  public lengthSquared(): number {
    return this.x * this.x + this.y * this.y + this.z * this.z;
  }
  
  /**
   * 归一化向量
   * @returns 当前向量
   */
  public normalize(): Vector3 {
    return this.divideScalar(this.length() || 1);
  }
  
  /**
   * 计算与另一个向量的距离
   * @param v 另一个向量
   * @returns 距离
   */
  public distanceTo(v: Vector3): number {
    return Math.sqrt(this.distanceToSquared(v));
  }
  
  /**
   * 计算与另一个向量的距离的平方
   * @param v 另一个向量
   * @returns 距离的平方
   */
  public distanceToSquared(v: Vector3): number {
    const dx = this.x - v.x;
    const dy = this.y - v.y;
    const dz = this.z - v.z;
    
    return dx * dx + dy * dy + dz * dz;
  }
  
  /**
   * 设置向量为零向量
   * @returns 当前向量
   */
  public setZero(): Vector3 {
    this.x = 0;
    this.y = 0;
    this.z = 0;
    return this;
  }
  
  /**
   * 判断向量是否为零向量
   * @returns 是否为零向量
   */
  public isZero(): boolean {
    return this.x === 0 && this.y === 0 && this.z === 0;
  }
  
  /**
   * 判断向量是否约等于另一个向量
   * @param v 另一个向量
   * @param epsilon 误差范围
   * @returns 是否约等于
   */
  public equals(v: Vector3, epsilon: number = 0.0001): boolean {
    return (
      Math.abs(this.x - v.x) < epsilon &&
      Math.abs(this.y - v.y) < epsilon &&
      Math.abs(this.z - v.z) < epsilon
    );
  }
}
