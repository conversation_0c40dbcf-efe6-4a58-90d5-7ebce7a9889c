import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

/**
 * 钉钉通知服务
 * 负责向钉钉发送通知消息
 */
@Injectable()
export class DingTalkNotifierService {
  private readonly logger = new Logger(DingTalkNotifierService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 发送钉钉通知
   */
  async sendNotification(data: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success';
    webhookUrl: string;
    secret?: string;
    atMobiles?: string[];
    atUserIds?: string[];
    isAtAll?: boolean;
    metadata?: any;
  }): Promise<boolean> {
    try {
      const timestamp = Date.now();
      const sign = data.secret ? this.generateSign(timestamp, data.secret) : undefined;

      const payload = {
        msgtype: 'markdown',
        markdown: {
          title: data.title,
          text: this.formatMarkdownMessage(data),
        },
        at: {
          atMobiles: data.atMobiles || [],
          atUserIds: data.atUserIds || [],
          isAtAll: data.isAtAll || false,
        },
      };

      const url = sign 
        ? `${data.webhookUrl}&timestamp=${timestamp}&sign=${sign}`
        : data.webhookUrl;

      const response = await firstValueFrom(
        this.httpService.post(url, payload)
      );

      if (response.data.errcode === 0) {
        this.logger.log(`钉钉通知发送成功: ${data.title}`);
        return true;
      } else {
        this.logger.error(`钉钉通知发送失败: ${response.data.errmsg}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`发送钉钉通知时出错: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 测试钉钉连接
   */
  async testConnection(webhookUrl: string, secret?: string): Promise<boolean> {
    try {
      const timestamp = Date.now();
      const sign = secret ? this.generateSign(timestamp, secret) : undefined;

      const testPayload = {
        msgtype: 'text',
        text: {
          content: '这是一条测试消息，用于验证钉钉通知配置是否正确。',
        },
      };

      const url = sign 
        ? `${webhookUrl}&timestamp=${timestamp}&sign=${sign}`
        : webhookUrl;

      const response = await firstValueFrom(
        this.httpService.post(url, testPayload)
      );

      return response.data.errcode === 0;
    } catch (error) {
      this.logger.error(`测试钉钉连接失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 生成签名
   */
  private generateSign(timestamp: number, secret: string): string {
    const crypto = require('crypto');
    const stringToSign = `${timestamp}\n${secret}`;
    const sign = crypto
      .createHmac('sha256', secret)
      .update(stringToSign)
      .digest('base64');
    
    return encodeURIComponent(sign);
  }

  /**
   * 格式化Markdown消息
   */
  private formatMarkdownMessage(data: {
    title: string;
    message: string;
    type: string;
    metadata?: any;
  }): string {
    const emoji = this.getEmojiByType(data.type);
    let markdown = `## ${emoji} ${data.title}\n\n`;
    markdown += `${data.message}\n\n`;

    if (data.metadata) {
      markdown += '**详细信息:**\n\n';
      for (const [key, value] of Object.entries(data.metadata)) {
        markdown += `- **${key}:** ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
      }
    }

    markdown += `\n---\n*发送时间: ${new Date().toLocaleString('zh-CN')}*`;

    return markdown;
  }

  /**
   * 根据类型获取表情符号
   */
  private getEmojiByType(type: string): string {
    const emojis = {
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
      success: '✅',
    };

    return emojis[type] || emojis.info;
  }
}
