<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>性能优化示例 - DL（Digital Learning）引擎</title>
  <link rel="stylesheet" href="styles/main.css">
</head>
<body>
  <div id="app">
    <div id="canvas-container"></div>
    <div id="ui-container">
      <div class="panel control-panel">
        <h2>优化控制</h2>
        <div class="control-group">
          <h3>LOD系统</h3>
          <label>
            <input type="checkbox" id="enable-lod" checked>
            启用LOD
          </label>
          <div class="slider-container">
            <label for="lod-distance">LOD距离阈值</label>
            <input type="range" id="lod-distance" min="10" max="200" value="50">
            <span id="lod-distance-value">50</span>
          </div>
        </div>
        <div class="control-group">
          <h3>实例化渲染</h3>
          <label>
            <input type="checkbox" id="enable-instancing" checked>
            启用实例化
          </label>
          <div class="slider-container">
            <label for="instance-count">实例数量</label>
            <input type="range" id="instance-count" min="100" max="10000" value="1000">
            <span id="instance-count-value">1000</span>
          </div>
        </div>
        <div class="control-group">
          <h3>几何体合并</h3>
          <label>
            <input type="checkbox" id="enable-merging" checked>
            启用几何体合并
          </label>
        </div>
        <div class="control-group">
          <h3>纹理优化</h3>
          <label>
            <input type="checkbox" id="enable-texture-compression" checked>
            启用纹理压缩
          </label>
          <label>
            <input type="checkbox" id="enable-texture-atlas" checked>
            启用纹理图集
          </label>
        </div>
        <div class="control-group">
          <h3>着色器优化</h3>
          <label>
            <input type="checkbox" id="enable-shader-optimization" checked>
            启用着色器优化
          </label>
        </div>
        <div class="control-group">
          <h3>场景复杂度</h3>
          <div class="slider-container">
            <label for="scene-complexity">场景复杂度</label>
            <input type="range" id="scene-complexity" min="1" max="5" value="3">
            <span id="scene-complexity-value">中等</span>
          </div>
        </div>
        <button id="reset-button">重置所有设置</button>
      </div>
      <div class="panel performance-panel">
        <h2>性能监控</h2>
        <div class="performance-metrics">
          <div class="metric">
            <span class="metric-label">FPS:</span>
            <span id="fps-value" class="metric-value">60</span>
          </div>
          <div class="metric">
            <span class="metric-label">绘制调用:</span>
            <span id="draw-calls-value" class="metric-value">100</span>
          </div>
          <div class="metric">
            <span class="metric-label">三角形数:</span>
            <span id="triangles-value" class="metric-value">10000</span>
          </div>
          <div class="metric">
            <span class="metric-label">内存使用:</span>
            <span id="memory-value" class="metric-value">100MB</span>
          </div>
          <div class="metric">
            <span class="metric-label">纹理内存:</span>
            <span id="texture-memory-value" class="metric-value">50MB</span>
          </div>
          <div class="metric">
            <span class="metric-label">几何体内存:</span>
            <span id="geometry-memory-value" class="metric-value">30MB</span>
          </div>
        </div>
        <div class="performance-chart">
          <canvas id="fps-chart"></canvas>
        </div>
        <button id="analyze-button">分析性能</button>
      </div>
    </div>
    <div id="analysis-modal" class="modal">
      <div class="modal-content">
        <span class="close-button">&times;</span>
        <h2>性能分析结果</h2>
        <div id="analysis-results"></div>
      </div>
    </div>
  </div>

  <script type="module">
    import { Engine, World, Entity, Scene, Transform, Camera, Mesh, Material, Light, LODComponent, InstancedMeshComponent, GeometryUtils, PerformanceMonitor } from '/engine/dist/index.js';
    import { PerformanceAnalyzer } from './scripts/PerformanceAnalyzer.js';
    import { SceneBuilder } from './scripts/SceneBuilder.js';
    import { UIController } from './scripts/UIController.js';
    import { ChartRenderer } from './scripts/ChartRenderer.js';

    // 初始化引擎
    const engine = new Engine({
      container: document.getElementById('canvas-container'),
      antialias: true,
      shadows: true,
    });

    // 创建世界
    const world = new World(engine);

    // 创建性能监控器
    const performanceMonitor = new PerformanceMonitor(world, {
      sampleRate: 1000,
      historyLength: 60,
    });

    // 创建性能分析器
    const performanceAnalyzer = new PerformanceAnalyzer(world, performanceMonitor);

    // 创建场景构建器
    const sceneBuilder = new SceneBuilder(world);

    // 创建UI控制器
    const uiController = new UIController(sceneBuilder, performanceMonitor, performanceAnalyzer);

    // 创建图表渲染器
    const chartRenderer = new ChartRenderer('fps-chart', performanceMonitor);

    // 初始化场景
    sceneBuilder.buildScene(3); // 中等复杂度

    // 启动引擎
    engine.start();
  </script>
</body>
</html>
