/**
 * 器官软体组件 - 用于模拟心脏等器官的物理行为
 */
import * as THREE from 'three';
import { SoftBodyComponent, SoftBodyComponentOptions } from '../SoftBodyComponent';
/**
 * 器官类型枚举
 */
export declare enum OrganType {
    /** 心脏 */
    HEART = "heart",
    /** 肺部 */
    LUNG = "lung",
    /** 肝脏 */
    LIVER = "liver",
    /** 肾脏 */
    KIDNEY = "kidney",
    /** 胃部 */
    STOMACH = "stomach",
    /** 肠道 */
    INTESTINE = "intestine"
}
/**
 * 血管连接类型
 */
export declare enum VesselConnectionType {
    /** 动脉连接 */
    ARTERIAL = "arterial",
    /** 静脉连接 */
    VENOUS = "venous",
    /** 毛细血管连接 */
    CAPILLARY = "capillary"
}
/**
 * 血管连接接口
 */
export interface VesselConnection {
    /** 连接ID */
    id: string;
    /** 连接类型 */
    type: VesselConnectionType;
    /** 连接位置 */
    position: THREE.Vector3;
    /** 连接方向 */
    direction: THREE.Vector3;
    /** 血管直径 */
    diameter: number;
    /** 流量 */
    flowRate: number;
    /** 压力 */
    pressure: number;
}
/**
 * 器官软体组件选项
 */
export interface OrganSoftBodyOptions extends SoftBodyComponentOptions {
    /** 器官类型 */
    organType: OrganType;
    /** 组织弹性参数 */
    tissueElasticity?: number;
    /** 组织密度 */
    tissueDensity?: number;
    /** 血管连接点 */
    vesselConnections?: VesselConnection[];
}
export declare class OrganSoftBody extends SoftBodyComponent {
    /** 器官类型 */
    private organType;
    /** 血管连接点 */
    private vesselConnections;
    /** 组织弹性参数 */
    private tissueElasticity;
    /** 组织密度 */
    private tissueDensity;
    /**
     * 构造函数
     * @param options 器官软体组件选项
     */
    constructor(options: OrganSoftBodyOptions);
    /**
     * 获取器官类型
     * @returns 器官类型
     */
    getOrganType(): OrganType;
    /**
     * 获取血管连接点
     * @returns 血管连接点数组
     */
    getVesselConnections(): VesselConnection[];
    /**
     * 添加血管连接点
     * @param connection 血管连接
     */
    addVesselConnection(connection: VesselConnection): void;
    /**
     * 获取组织弹性参数
     * @returns 组织弹性参数
     */
    getTissueElasticity(): number;
    /**
     * 设置组织弹性参数
     * @param elasticity 弹性参数
     */
    setTissueElasticity(elasticity: number): void;
    /**
     * 获取组织密度
     * @returns 组织密度
     */
    getTissueDensity(): number;
    /**
     * 设置组织密度
     * @param density 组织密度
     */
    setTissueDensity(density: number): void;
    /**
     * 创建心脏模型
     * 使用四腔结构和主要血管连接点
     */
    createHeart(): void;
    /**
     * 创建心脏四腔结构
     * 创建左心房、右心房、左心室、右心室
     */
    private createHeartChambers;
    /**
     * 创建血管连接点
     * 创建主要的血管连接，如主动脉、肺动脉等
     */
    private createVesselConnections;
    /**
     * 设置心脏特定的物理参数
     */
    private setHeartPhysicsParameters;
    /**
     * 模拟心脏搏动
     * @param rate 心率(次/分钟)
     * @param strength 收缩强度 (0-1)
     */
    simulateHeartbeat(rate: number, strength: number): void;
    /**
     * 应用收缩效果
     * @param factor 收缩因子 (0-1)
     */
    private applyContraction;
}
