import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
/**
 * Avatar动画系统配置
 */
export interface AvatarAnimationSystemConfig {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 是否自动重定向动画 */
    autoRetarget?: boolean;
    /** 是否使用状态机 */
    useStateMachine?: boolean;
    /** 是否使用混合空间 */
    useBlendSpace?: boolean;
}
/**
 * Avatar动画系统
 */
export declare class AvatarAnimationSystem extends System {
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** Avatar实体列表 */
    private avatarEntities;
    /** 默认动画映射 */
    private defaultAnimations;
    /** 骨骼映射 */
    private boneMappings;
    /** 重定向缓存 */
    private retargetCache;
    /** 上次更新时间 */
    private lastSystemUpdateTime;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(config?: AvatarAnimationSystemConfig);
    /**
     * 注册Avatar实体
     * @param entity 实体
     */
    registerAvatar(entity: Entity): void;
    /**
     * 注销Avatar实体
     * @param entity 实体
     */
    unregisterAvatar(entity: Entity): void;
    /**
     * 设置默认状态机
     * @param stateMachine 状态机
     * @param animationComponent 动画组件
     */
    private setupDefaultStateMachine;
    /**
     * 重定向动画
     * @param entity 实体
     */
    private retargetAnimations;
    /**
     * 将THREE.AnimationClip转换为AnimationClip
     * @param threeClip THREE动画片段
     * @returns 动画片段
     */
    private convertFromThreeClip;
    /**
     * 获取骨骼映射
     * @param entity 实体
     * @returns 骨骼映射
     */
    private getBoneMapping;
    /**
     * 获取骨骼
     * @param entity 实体
     * @returns 骨骼数组
     */
    private getSkeleton;
    /**
     * 将AnimationClip转换为THREE.AnimationClip
     * @param clip 动画片段
     * @returns THREE动画片段
     */
    private convertToThreeClip;
    /**
     * 更新系统
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 设置实体更新频率
     * @param entity 实体
     * @param frequency 更新频率（秒）
     */
    setUpdateFrequency(entity: Entity, frequency: number): void;
    /**
     * 标记实体需要更新
     * @param entity 实体
     */
    markNeedsUpdate(entity: Entity): void;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    addEventListener(type: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    removeEventListener(type: string, callback: (data: any) => void): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
