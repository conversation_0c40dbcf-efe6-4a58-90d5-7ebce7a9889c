import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, Index } from 'typeorm';

@Entity('service_metrics')
export class ServiceMetricsEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  serviceId: string;

  @Column()
  @Index()
  serviceType: string;

  @Column()
  @Index()
  instanceId: string;

  @Column()
  @Index()
  hostname: string;

  @Column('int')
  requestCount: number;

  @Column('int')
  errorCount: number;

  @Column('float')
  errorRate: number;

  @Column('float')
  averageResponseTime: number;

  @Column('int')
  activeConnections: number;

  @Column('int')
  queueLength: number;

  @Column('json', { nullable: true })
  customMetrics: Record<string, any>;

  @Column('timestamp')
  @Index()
  timestamp: Date;

  @CreateDateColumn()
  createdAt: Date;
}
