/**
 * 物理驱动动画系统
 * 用于基于物理模拟驱动角色动画
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
/**
 * 物理驱动动画配置
 */
export interface PhysicsBasedAnimationConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 物理更新频率 */
    physicsUpdateRate?: number;
    /** 是否使用子步进 */
    useSubsteps?: boolean;
    /** 子步进数量 */
    substeps?: number;
    /** 是否使用连续碰撞检测 */
    useCCD?: boolean;
    /** 重力 */
    gravity?: THREE.Vector3;
    /** 阻尼 */
    damping?: number;
    /** 弹性 */
    restitution?: number;
    /** 摩擦力 */
    friction?: number;
}
/**
 * 物理骨骼配置
 */
export interface PhysicsBoneConfig {
    /** 骨骼名称 */
    name: string;
    /** 质量 */
    mass: number;
    /** 半径 */
    radius: number;
    /** 长度 */
    length: number;
    /** 阻尼 */
    damping?: number;
    /** 弹性 */
    restitution?: number;
    /** 摩擦力 */
    friction?: number;
    /** 是否固定 */
    isKinematic?: boolean;
    /** 碰撞组 */
    collisionGroup?: number;
    /** 碰撞掩码 */
    collisionMask?: number;
}
/**
 * 物理约束配置
 */
export interface PhysicsConstraintConfig {
    /** 约束名称 */
    name: string;
    /** 骨骼A */
    boneA: string;
    /** 骨骼B */
    boneB: string;
    /** 约束类型 */
    type: 'hinge' | 'point' | 'distance' | 'cone' | 'slider';
    /** 局部轴A */
    localAxisA?: THREE.Vector3;
    /** 局部轴B */
    localAxisB?: THREE.Vector3;
    /** 局部点A */
    localPointA?: THREE.Vector3;
    /** 局部点B */
    localPointB?: THREE.Vector3;
    /** 最小角度（弧度） */
    minAngle?: number;
    /** 最大角度（弧度） */
    maxAngle?: number;
    /** 最小距离 */
    minDistance?: number;
    /** 最大距离 */
    maxDistance?: number;
    /** 弹簧常数 */
    springConstant?: number;
    /** 阻尼系数 */
    dampingCoefficient?: number;
}
/**
 * 物理驱动动画组件
 */
export declare class PhysicsBasedAnimationComponent extends Component {
    /** 组件类型 */
    static readonly type = "PhysicsBasedAnimation";
    /** 物理骨骼 */
    private physicsBones;
    /** 物理约束 */
    private physicsConstraints;
    /** 是否启用 */
    protected enabled: boolean;
    /** 是否初始化 */
    private initialized;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     */
    constructor();
    /**
     * 添加物理骨骼
     * @param config 物理骨骼配置
     */
    addPhysicsBone(config: PhysicsBoneConfig): void;
    /**
     * 移除物理骨骼
     * @param name 骨骼名称
     * @returns 是否成功移除
     */
    removePhysicsBone(name: string): boolean;
    /**
     * 获取物理骨骼
     * @param name 骨骼名称
     * @returns 物理骨骼配置，如果不存在则返回null
     */
    getPhysicsBone(name: string): PhysicsBoneConfig | null;
    /**
     * 获取所有物理骨骼
     * @returns 物理骨骼配置数组
     */
    getPhysicsBones(): PhysicsBoneConfig[];
    /**
     * 添加物理约束
     * @param config 物理约束配置
     */
    addPhysicsConstraint(config: PhysicsConstraintConfig): void;
    /**
     * 移除物理约束
     * @param name 约束名称
     * @returns 是否成功移除
     */
    removePhysicsConstraint(name: string): boolean;
    /**
     * 获取物理约束
     * @param name 约束名称
     * @returns 物理约束配置，如果不存在则返回null
     */
    getPhysicsConstraint(name: string): PhysicsConstraintConfig | null;
    /**
     * 获取所有物理约束
     * @returns 物理约束配置数组
     */
    getPhysicsConstraints(): PhysicsConstraintConfig[];
    /**
     * 启用组件
     */
    enable(): void;
    /**
     * 禁用组件
     */
    disable(): void;
    /**
     * 是否已启用
     * @returns 是否已启用
     */
    isEnabled(): boolean;
    /**
     * 是否已初始化
     * @returns 是否已初始化
     */
    isInitialized(): boolean;
    /**
     * 设置初始化状态
     * @param initialized 初始化状态
     */
    setInitialized(initialized: boolean): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
/**
 * 物理驱动动画系统
 */
export declare class PhysicsBasedAnimationSystem extends System {
    /** 系统类型 */
    static readonly type = "PhysicsBasedAnimation";
    /** 物理驱动动画组件 */
    private components;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 物理世界 */
    private physicsWorld;
    /** 物理对象映射 */
    private physicsObjects;
    /** 物理约束映射 */
    private physicsConstraints;
    /** 累积时间 */
    private accumulator;
    /** 物理时间步长 */
    private timeStep;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<PhysicsBasedAnimationConfig>);
    /**
     * 初始化物理世界
     */
    private initPhysicsWorld;
    /**
     * 创建物理驱动动画组件
     * @param entity 实体
     * @returns 物理驱动动画组件
     */
    createPhysicsBasedAnimation(entity: Entity): PhysicsBasedAnimationComponent;
    /**
     * 移除物理驱动动画组件
     * @param entity 实体
     */
    removePhysicsBasedAnimation(entity: Entity): void;
    /**
     * 获取物理驱动动画组件
     * @param entity 实体
     * @returns 物理驱动动画组件，如果不存在则返回null
     */
    getPhysicsBasedAnimation(entity: Entity): PhysicsBasedAnimationComponent | null;
    /**
     * 初始化物理对象
     * @param entity 实体
     */
    initPhysicsObjects(entity: Entity): void;
    /**
     * 创建物理骨骼
     * @param entity 实体
     * @param config 物理骨骼配置
     */
    private createPhysicsBone;
    /**
     * 创建物理约束
     * @param entity 实体
     * @param config 物理约束配置
     */
    private createPhysicsConstraint;
    /**
     * 清理物理对象
     * @param entity 实体
     */
    private cleanupPhysicsObjects;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 物理模拟步进
     * @param timeStep 时间步长
     */
    private stepPhysics;
    /**
     * 执行物理模拟步进
     * @param timeStep 时间步长
     */
    private performPhysicsStep;
}
