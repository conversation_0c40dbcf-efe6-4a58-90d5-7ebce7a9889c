/**
 * 输入系统
 * 用于处理键盘、鼠标和触摸输入
 */
import { EventCallback } from '../utils/EventEmitter';
import { System } from '../core/System';
/**
 * 键盘按键状态
 */
export declare enum KeyState {
    /** 按下 */
    DOWN = "down",
    /** 按住 */
    PRESSED = "pressed",
    /** 释放 */
    UP = "up",
    /** 未按下 */
    NONE = "none"
}
/**
 * 鼠标按钮状态
 */
export declare enum MouseButtonState {
    /** 按下 */
    DOWN = "down",
    /** 按住 */
    PRESSED = "pressed",
    /** 释放 */
    UP = "up",
    /** 未按下 */
    NONE = "none"
}
/**
 * 鼠标按钮
 */
export declare enum MouseButton {
    /** 左键 */
    LEFT = 0,
    /** 中键 */
    MIDDLE = 1,
    /** 右键 */
    RIGHT = 2
}
/**
 * 输入事件类型
 */
export declare enum InputEventType {
    /** 键盘按键按下 */
    KEY_DOWN = "keyDown",
    /** 键盘按键释放 */
    KEY_UP = "keyUp",
    /** 鼠标按钮按下 */
    MOUSE_DOWN = "mouseDown",
    /** 鼠标按钮释放 */
    MOUSE_UP = "mouseUp",
    /** 鼠标移动 */
    MOUSE_MOVE = "mouseMove",
    /** 鼠标滚轮 */
    MOUSE_WHEEL = "mouseWheel",
    /** 触摸开始 */
    TOUCH_START = "touchStart",
    /** 触摸移动 */
    TOUCH_MOVE = "touchMove",
    /** 触摸结束 */
    TOUCH_END = "touchEnd",
    /** 触摸取消 */
    TOUCH_CANCEL = "touchCancel",
    /** 游戏手柄连接 */
    GAMEPAD_CONNECTED = "gamepadConnected",
    /** 游戏手柄断开 */
    GAMEPAD_DISCONNECTED = "gamepadDisconnected",
    /** 游戏手柄按钮按下 */
    GAMEPAD_BUTTON_DOWN = "gamepadButtonDown",
    /** 游戏手柄按钮释放 */
    GAMEPAD_BUTTON_UP = "gamepadButtonUp",
    /** 游戏手柄摇杆移动 */
    GAMEPAD_AXIS_MOVE = "gamepadAxisMove"
}
/**
 * 输入系统选项
 */
export interface InputSystemOptions {
    /** 目标元素 */
    element?: HTMLElement;
    /** 是否阻止默认行为 */
    preventDefault?: boolean;
    /** 是否阻止事件传播 */
    stopPropagation?: boolean;
    /** 是否启用键盘输入 */
    enableKeyboard?: boolean;
    /** 是否启用鼠标输入 */
    enableMouse?: boolean;
    /** 是否启用触摸输入 */
    enableTouch?: boolean;
    /** 是否启用游戏手柄输入 */
    enableGamepad?: boolean;
    /** 是否启用指针锁定 */
    enablePointerLock?: boolean;
}
/**
 * 输入系统
 */
export declare class InputSystem extends System {
    /** 系统名称 */
    static readonly NAME: string;
    /** 目标元素 */
    private element;
    /** 是否阻止默认行为 */
    private preventDefault;
    /** 是否阻止事件传播 */
    private stopPropagation;
    /** 是否启用键盘输入 */
    private enableKeyboard;
    /** 是否启用鼠标输入 */
    private enableMouse;
    /** 是否启用触摸输入 */
    private enableTouch;
    /** 是否启用游戏手柄输入 */
    private enableGamepad;
    /** 是否启用指针锁定 */
    private enablePointerLock;
    /** 事件发射器 */
    private eventEmitter;
    /** 按键状态映射 */
    private keyStates;
    /** 鼠标按钮状态映射 */
    private mouseButtonStates;
    /** 鼠标位置 */
    private mousePosition;
    /** 鼠标相对位置（相对于上一帧） */
    private mouseMovement;
    /** 鼠标滚轮增量 */
    private mouseWheelDelta;
    /** 触摸点映射 */
    private touchPoints;
    /** 游戏手柄映射 */
    private gamepads;
    /** 游戏手柄按钮状态映射 */
    private gamepadButtonStates;
    /** 游戏手柄摇杆值映射 */
    private gamepadAxisValues;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /** 是否支持当前环境 */
    private supported;
    /** 是否处于指针锁定状态 */
    private pointerLocked;
    /** 键盘事件处理器 */
    private keyboardEventHandlers;
    /** 鼠标事件处理器 */
    private mouseEventHandlers;
    /** 触摸事件处理器 */
    private touchEventHandlers;
    /** 游戏手柄事件处理器 */
    private gamepadEventHandlers;
    /** 指针锁定事件处理器 */
    private pointerLockEventHandlers;
    /**
     * 创建输入系统
     * @param options 输入系统选项
     */
    constructor(options?: InputSystemOptions);
    /**
     * 初始化事件处理器
     */
    private initEventHandlers;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加事件监听器
     */
    private addEventListeners;
    /**
     * 更新游戏手柄状态
     */
    private updateGamepads;
    /**
     * 处理键盘按键按下事件
     * @param event 键盘事件
     */
    private handleKeyDown;
    /**
     * 处理键盘按键释放事件
     * @param event 键盘事件
     */
    private handleKeyUp;
    /**
     * 处理鼠标按钮按下事件
     * @param event 鼠标事件
     */
    private handleMouseDown;
    /**
     * 处理鼠标按钮释放事件
     * @param event 鼠标事件
     */
    private handleMouseUp;
    /**
     * 处理鼠标移动事件
     * @param event 鼠标事件
     */
    private handleMouseMove;
    /**
     * 处理鼠标滚轮事件
     * @param event 滚轮事件
     */
    private handleMouseWheel;
    /**
     * 处理上下文菜单事件（右键菜单）
     * @param event 鼠标事件
     */
    private handleContextMenu;
    /**
     * 处理触摸开始事件
     * @param event 触摸事件
     */
    private handleTouchStart;
    /**
     * 处理触摸移动事件
     * @param event 触摸事件
     */
    private handleTouchMove;
    /**
     * 处理触摸结束事件
     * @param event 触摸事件
     */
    private handleTouchEnd;
    /**
     * 处理触摸取消事件
     * @param event 触摸事件
     */
    private handleTouchCancel;
    /**
     * 处理游戏手柄连接事件
     * @param event 游戏手柄事件
     */
    private handleGamepadConnected;
    /**
     * 处理游戏手柄断开事件
     * @param event 游戏手柄事件
     */
    private handleGamepadDisconnected;
    /**
     * 处理指针锁定变化事件
     */
    private handlePointerLockChange;
    /**
     * 处理指针锁定错误事件
     */
    private handlePointerLockError;
    /**
     * 更新鼠标位置
     * @param event 鼠标事件
     */
    private updateMousePosition;
    /**
     * 请求指针锁定
     */
    requestPointerLock(): void;
    /**
     * 退出指针锁定
     */
    exitPointerLock(): void;
    /**
     * 检查按键是否按下
     * @param key 按键
     * @returns 是否按下
     */
    isKeyDown(key: string): boolean;
    /**
     * 检查按键是否按住
     * @param key 按键
     * @returns 是否按住
     */
    isKeyPressed(key: string): boolean;
    /**
     * 检查按键是否释放
     * @param key 按键
     * @returns 是否释放
     */
    isKeyUp(key: string): boolean;
    /**
     * 检查鼠标按钮是否按下
     * @param button 鼠标按钮
     * @returns 是否按下
     */
    isMouseButtonDown(button: MouseButton): boolean;
    /**
     * 检查鼠标按钮是否按住
     * @param button 鼠标按钮
     * @returns 是否按住
     */
    isMouseButtonPressed(button: MouseButton): boolean;
    /**
     * 检查鼠标按钮是否释放
     * @param button 鼠标按钮
     * @returns 是否释放
     */
    isMouseButtonUp(button: MouseButton): boolean;
    /**
     * 获取鼠标位置
     * @returns 鼠标位置
     */
    getMousePosition(): {
        x: number;
        y: number;
    };
    /**
     * 获取鼠标移动增量
     * @returns 鼠标移动增量
     */
    getMouseMovement(): {
        x: number;
        y: number;
    };
    /**
     * 获取鼠标滚轮增量
     * @returns 鼠标滚轮增量
     */
    getMouseWheelDelta(): number;
    /**
     * 获取触摸点
     * @param id 触摸点ID
     * @returns 触摸点
     */
    getTouchPoint(id: number): Touch | undefined;
    /**
     * 获取所有触摸点
     * @returns 触摸点数组
     */
    getTouchPoints(): Touch[];
    /**
     * 获取游戏手柄
     * @param index 游戏手柄索引
     * @returns 游戏手柄
     */
    getGamepad(index: number): Gamepad | undefined;
    /**
     * 获取所有游戏手柄
     * @returns 游戏手柄数组
     */
    getGamepads(): Gamepad[];
    /**
     * 检查游戏手柄按钮是否按下
     * @param gamepadIndex 游戏手柄索引
     * @param buttonIndex 按钮索引
     * @returns 是否按下
     */
    isGamepadButtonPressed(gamepadIndex: number, buttonIndex: number): boolean;
    /**
     * 获取游戏手柄摇杆值
     * @param gamepadIndex 游戏手柄索引
     * @param axisIndex 摇杆索引
     * @returns 摇杆值（-1到1）
     */
    getGamepadAxisValue(gamepadIndex: number, axisIndex: number): number;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param callback 监听器函数
     * @returns 当前实例，用于链式调用
     */
    on(event: string, callback: EventCallback): this;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param callback 监听器函数（可选）
     * @returns 当前实例，用于链式调用
     */
    off(event: string, callback?: EventCallback): this;
    /**
     * 移除事件监听器
     */
    private removeEventListeners;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
