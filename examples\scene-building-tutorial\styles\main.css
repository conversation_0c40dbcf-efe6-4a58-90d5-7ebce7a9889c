/* 场景构建教程样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', sans-serif;
  background-color: #f0f2f5;
  color: #333;
}

#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

#canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

#tutorial-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  pointer-events: none;
}

.tutorial-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: calc(100% - 320px - 40px);
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  pointer-events: auto;
  z-index: 100;
}

.tutorial-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
}

.tutorial-header h1 {
  font-size: 20px;
  color: #1890ff;
  margin: 0;
}

.tutorial-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.tutorial-controls button {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.tutorial-controls button:hover {
  background-color: #40a9ff;
}

.tutorial-controls button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

#step-indicator {
  font-size: 14px;
  color: #666;
}

.tutorial-content {
  padding: 15px;
}

.step-title h2 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
}

.step-description {
  margin-bottom: 15px;
}

.step-description p {
  margin-bottom: 10px;
  line-height: 1.5;
}

.step-description ol, .step-description ul {
  margin-left: 20px;
  margin-bottom: 10px;
}

.step-description li {
  margin-bottom: 5px;
  line-height: 1.5;
}

.tip {
  background-color: #e6f7ff;
  border-left: 4px solid #1890ff;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 0 4px 4px 0;
}

.step-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.action-button {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.action-button:hover {
  background-color: #40a9ff;
}

.tools-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 300px;
  height: calc(100% - 40px);
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 15px;
  overflow-y: auto;
  pointer-events: auto;
  z-index: 100;
}

.tools-panel h2 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #1890ff;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.tool-group {
  margin-bottom: 20px;
}

.tool-group h3 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

.tool-button {
  background-color: #f0f0f0;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 6px 12px;
  margin-right: 5px;
  margin-bottom: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.tool-button:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.tool-button.active {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
}

.tool-settings {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.tool-settings label {
  display: block;
  margin-bottom: 8px;
}

.tool-settings input[type="range"] {
  width: 100%;
  margin-top: 5px;
}

.texture-selector {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-top: 10px;
}

.texture-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s;
}

.texture-item:hover {
  background-color: #e6f7ff;
}

.texture-item.active {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.texture-item img {
  width: 100%;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 5px;
}

.object-categories {
  display: flex;
  margin-bottom: 10px;
  overflow-x: auto;
}

.category-button {
  background-color: #f0f0f0;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 6px 12px;
  margin-right: 5px;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
  transition: all 0.3s;
}

.category-button:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.category-button.active {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
}

.object-list {
  height: 300px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tutorial-panel {
    width: 100%;
    left: 0;
    bottom: 0;
    border-radius: 0;
  }
  
  .tools-panel {
    display: none;
  }
}
