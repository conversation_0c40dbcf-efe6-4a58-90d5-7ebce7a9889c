/* 性能优化示例项目样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', sans-serif;
  background-color: #f0f2f5;
  color: #333;
}

#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

#canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

#ui-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px;
  pointer-events: none;
}

.panel {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 15px;
  pointer-events: auto;
}

.control-panel {
  flex: 1;
  overflow-y: auto;
}

.performance-panel {
  height: 300px;
}

h2 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #1890ff;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

h3 {
  font-size: 16px;
  margin: 10px 0 5px;
  color: #333;
}

.control-group {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #e8e8e8;
}

.control-group:last-child {
  border-bottom: none;
}

label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
}

input[type="checkbox"] {
  margin-right: 8px;
}

.slider-container {
  margin-top: 8px;
}

input[type="range"] {
  width: 100%;
  margin: 5px 0;
}

button {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #40a9ff;
}

#reset-button {
  width: 100%;
  margin-top: 10px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-weight: bold;
}

.metric-value {
  font-family: monospace;
}

.performance-chart {
  height: 120px;
  margin-bottom: 10px;
}

#analyze-button {
  width: 100%;
}

.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 600px;
  max-height: 80%;
  overflow-y: auto;
}

.close-button {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close-button:hover {
  color: black;
}

#analysis-results {
  margin-top: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #ui-container {
    width: 100%;
    height: auto;
    position: relative;
    padding: 5px;
  }
  
  .panel {
    margin-bottom: 5px;
  }
  
  .performance-panel {
    height: auto;
  }
}
