import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { LoggingService } from './logging.service';
import { LoggingController } from './logging.controller';
import { LogEntity } from './entities/log.entity';
import { LogQueryEntity } from './entities/log-query.entity';
import { LogStorageService } from './log-storage.service';
import { LogAnalysisService } from './log-analysis.service';
import { ElasticsearchLogService } from './elasticsearch-log.service';
import { LogAggregatorService } from './log-aggregator.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LogEntity,
      LogQueryEntity,
    ]),
    HttpModule,
  ],
  controllers: [LoggingController],
  providers: [
    LoggingService,
    LogStorageService,
    LogAnalysisService,
    ElasticsearchLogService,
    LogAggregatorService,
  ],
  exports: [
    LoggingService,
    LogStorageService,
    LogAnalysisService,
    ElasticsearchLogService,
  ],
})
export class LoggingModule {}
