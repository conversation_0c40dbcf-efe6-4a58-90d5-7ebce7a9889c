/**
 * 四叉树空间分区
 * 用于网络实体的高效空间查询和同步优化
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
/**
 * 四叉树节点
 */
export declare class QuadtreeNode {
    /** 边界 */
    bounds: {
        minX: number;
        minZ: number;
        maxX: number;
        maxZ: number;
    };
    /** 深度 */
    depth: number;
    /** 子节点 */
    children: QuadtreeNode[] | null;
    /** 实体列表 */
    entities: Map<string, Entity>;
    /** 父节点 */
    parent: QuadtreeNode | null;
    /** 是否已分割 */
    divided: boolean;
    /**
     * 创建四叉树节点
     * @param minX 最小X坐标
     * @param minZ 最小Z坐标
     * @param maxX 最大X坐标
     * @param maxZ 最大Z坐标
     * @param depth 深度
     * @param parent 父节点
     */
    constructor(minX: number, minZ: number, maxX: number, maxZ: number, depth?: number, parent?: QuadtreeNode | null);
    /**
     * 获取中心点
     * @returns 中心点
     */
    getCenter(): {
        x: number;
        z: number;
    };
    /**
     * 获取宽度
     * @returns 宽度
     */
    getWidth(): number;
    /**
     * 获取高度
     * @returns 高度
     */
    getHeight(): number;
    /**
     * 分割节点
     */
    subdivide(): void;
    /**
     * 重新分配实体
     */
    private redistributeEntities;
    /**
     * 检查点是否在边界内
     * @param x X坐标
     * @param z Z坐标
     * @param bounds 边界
     * @returns 是否在边界内
     */
    private isPointInBounds;
    /**
     * 获取实体位置
     * @param entity 实体
     * @returns 位置
     */
    private getEntityPosition;
}
/**
 * 四叉树配置
 */
export interface QuadtreeConfig {
    /** 最大深度 */
    maxDepth?: number;
    /** 节点最大实体数量 */
    maxEntities?: number;
    /** 最小节点大小 */
    minNodeSize?: number;
    /** 世界大小 */
    worldSize?: number;
    /** 世界中心 */
    worldCenter?: THREE.Vector3;
    /** 是否启用动态调整 */
    enableDynamicAdjustment?: boolean;
    /** 是否启用松散四叉树 */
    enableLooseQuadtree?: boolean;
    /** 松散因子 */
    looseFactor?: number;
}
/**
 * 四叉树空间分区
 */
export declare class QuadtreePartitioning {
    /** 配置 */
    private config;
    /** 根节点 */
    private root;
    /** 实体到节点的映射 */
    private entityToNode;
    /** 实体列表 */
    private entities;
    /** 调试网格 */
    private debugMesh;
    /**
     * 创建四叉树空间分区
     * @param config 配置
     */
    constructor(config?: QuadtreeConfig);
    /**
     * 添加实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    addEntity(entityId: string, entity: Entity): void;
    /**
     * 移除实体
     * @param entityId 实体ID
     */
    removeEntity(entityId: string): void;
    /**
     * 更新实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    updateEntity(entityId: string, entity: Entity): void;
    /**
     * 查询区域内的实体
     * @param minX 最小X坐标
     * @param minZ 最小Z坐标
     * @param maxX 最大X坐标
     * @param maxZ 最大Z坐标
     * @returns 实体列表
     */
    queryRegion(minX: number, minZ: number, maxX: number, maxZ: number): Map<string, Entity>;
    /**
     * 递归查询区域内的实体
     * @param node 节点
     * @param minX 最小X坐标
     * @param minZ 最小Z坐标
     * @param maxX 最大X坐标
     * @param maxZ 最大Z坐标
     * @param result 结果
     */
    private queryRegionRecursive;
    /**
     * 查询距离内的实体
     * @param x 中心X坐标
     * @param z 中心Z坐标
     * @param radius 半径
     * @returns 实体列表
     */
    queryRadius(x: number, z: number, radius: number): Map<string, Entity>;
    /**
     * 查找位置所在的节点
     * @param x X坐标
     * @param z Z坐标
     * @returns 节点
     */
    private findNodeForPosition;
    /**
     * 递归查找位置所在的节点
     * @param node 节点
     * @param x X坐标
     * @param z Z坐标
     * @returns 节点
     */
    private findNodeForPositionRecursive;
    /**
     * 检查并分割节点
     * @param node 节点
     */
    private checkAndSubdivide;
    /**
     * 检查并合并节点
     * @param node 节点
     */
    private checkAndMerge;
    /**
     * 检查点是否在边界内
     * @param x X坐标
     * @param z Z坐标
     * @param bounds 边界
     * @returns 是否在边界内
     */
    private isPointInBounds;
    /**
     * 检查区域是否与边界相交
     * @param minX 区域最小X
     * @param minZ 区域最小Z
     * @param maxX 区域最大X
     * @param maxZ 区域最大Z
     * @param bounds 边界
     * @returns 是否相交
     */
    private isRegionOverlap;
    /**
     * 获取实体位置
     * @param entity 实体
     * @returns 位置
     */
    private getEntityPosition;
    /**
     * 清空四叉树
     */
    clear(): void;
    /**
     * 获取实体数量
     * @returns 实体数量
     */
    getEntityCount(): number;
    /**
     * 获取所有实体
     * @returns 实体列表
     */
    getAllEntities(): Map<string, Entity>;
}
