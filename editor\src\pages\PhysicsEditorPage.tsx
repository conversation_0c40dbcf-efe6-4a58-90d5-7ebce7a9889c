/**
 * 物理编辑器页面
 * 用于展示和编辑物理系统和组件
 */
import React from 'react';
import { Layout, Tabs, Card, Row, Col, Button, Space, Divider, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import {
  PhysicsSystemEditor,
  PhysicsBodyEditor,
  PhysicsColliderEditor,
  PhysicsConstraintEditor,
  CharacterControllerEditor,
  PhysicsMaterialEditor,
  PhysicsPresetEditor,
  PhysicsDebuggerEditor,
  PhysicsInteractionEditor
} from '../components/physics';
import ScenePanel from '../components/ScenePanel';
import PropertiesPanel from '../components/PropertiesPanel';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

/**
 * 物理编辑器页面
 */
const PhysicsEditorPage: React.FC = () => {
  const { t } = useTranslation();

  // 获取选中的实体
  const selectedEntityId = useSelector((state: RootState) => state.scene.selectedEntityId);

  return (
    <Layout className="physics-editor-page">
      <Header className="page-header">
        <Title level={3}>{t('editor.physics.physicsEditor')}</Title>
      </Header>

      <Layout>
        <Sider width={300} className="editor-sider">
          <ScenePanel />
        </Sider>

        <Content className="editor-content">
          <Tabs
            defaultActiveKey="system"
            className="editor-tabs"
            items={[
              {
                key: 'system',
                label: t('editor.physics.system'),
                children: <PhysicsSystemEditor />
              },
              {
                key: 'components',
                label: t('editor.physics.components'),
                children: selectedEntityId ? (
                  <Row gutter={[16, 16]}>
                    <Col span={24}>
                      <PropertiesPanel />
                    </Col>

                    <Col span={12}>
                      <PhysicsBodyEditor entityId={selectedEntityId} />
                    </Col>

                    <Col span={12}>
                      <PhysicsColliderEditor entityId={selectedEntityId} />
                    </Col>

                    <Col span={12}>
                      <PhysicsConstraintEditor entityId={selectedEntityId} />
                    </Col>

                    <Col span={12}>
                      <CharacterControllerEditor entityId={selectedEntityId} />
                    </Col>

                    <Col span={12}>
                      <PhysicsInteractionEditor selectedEntityId={selectedEntityId} />
                    </Col>
                  </Row>
                ) : (
                  <div className="empty-state">
                    <Text>{t('editor.common.selectEntityPrompt')}</Text>
                  </div>
                )
              },
              {
                key: 'materials',
                label: t('editor.physics.materials'),
                children: <PhysicsMaterialEditor />
              },
              {
                key: 'presets',
                label: t('editor.physics.presets'),
                children: <PhysicsPresetEditor />
              },
              {
                key: 'debugger',
                label: t('editor.physics.debugger'),
                children: <PhysicsDebuggerEditor />
              },
              {
                key: 'examples',
                label: t('editor.physics.examples'),
                children: (
                  <Card title={t('editor.physics.physicsExamples')}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button block>{t('editor.physics.examples.basicPhysics')}</Button>
                      <Button block>{t('editor.physics.examples.constraints')}</Button>
                      <Button block>{t('editor.physics.examples.characterController')}</Button>
                      <Button block>{t('editor.physics.examples.continuousCollisionDetection')}</Button>
                      <Button block>{t('editor.physics.examples.collisionVisualization')}</Button>
                      <Button block>{t('editor.physics.examples.physicsPresets')}</Button>
                      <Button block>{t('editor.physics.examples.vehiclePhysics')}</Button>
                      <Button block>{t('editor.physics.examples.ragdoll')}</Button>
                      <Button block>{t('editor.physics.examples.softBody')}</Button>
                    </Space>
                  </Card>
                )
              },
              {
                key: 'import_export',
                label: t('editor.physics.import_export'),
                children: (
                  <Card title={t('editor.physics.sceneImportExport')}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button type="primary" block>{t('editor.physics.exportScene')}</Button>
                      <Button block>{t('editor.physics.importScene')}</Button>
                      <Divider />
                      <Button type="primary" block>{t('editor.physics.exportPresets')}</Button>
                      <Button block>{t('editor.physics.importPresets')}</Button>
                    </Space>
                  </Card>
                )
              }
            ]}
          />
        </Content>
      </Layout>
    </Layout>
  );
};

export default PhysicsEditorPage;
