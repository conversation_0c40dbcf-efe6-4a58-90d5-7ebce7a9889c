/**
 * 物理约束组件
 * 为实体提供物理约束
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';

/**
 * 约束类型
 */
export type ConstraintType = 'point' | 'distance' | 'hinge' | 'lock' | 'spring';

/**
 * 物理约束选项
 */
export interface PhysicsConstraintOptions {
  /** 约束类型 */
  type: ConstraintType;
  /** 约束的第一个实体 */
  entityA: Entity;
  /** 约束的第二个实体（可选） */
  entityB?: Entity;
  /** 第一个实体的枢轴点 */
  pivotA?: THREE.Vector3;
  /** 第二个实体的枢轴点 */
  pivotB?: THREE.Vector3;
  /** 第一个实体的轴向 */
  axisA?: THREE.Vector3;
  /** 第二个实体的轴向 */
  axisB?: THREE.Vector3;
  /** 距离约束的距离 */
  distance?: number;
  /** 弹簧约束的刚度 */
  stiffness?: number;
  /** 弹簧约束的阻尼 */
  damping?: number;
  /** 铰链约束的最大角度 */
  maxAngle?: number;
  /** 铰链约束的最小角度 */
  minAngle?: number;
  /** 铰链约束的马达启用 */
  enableMotor?: boolean;
  /** 铰链约束的马达速度 */
  motorSpeed?: number;
  /** 铰链约束的马达最大力 */
  motorMaxForce?: number;
  /** 碰撞启用 */
  collideConnected?: boolean;
}

/**
 * 物理约束组件
 */
export class PhysicsConstraintComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'PhysicsConstraintComponent';

  /** 约束类型 */
  public constraintType: ConstraintType;

  /** 约束的第一个实体 */
  public entityA: Entity;

  /** 约束的第二个实体 */
  public entityB: Entity | null;

  /** 第一个实体的枢轴点 */
  public pivotA: THREE.Vector3;

  /** 第二个实体的枢轴点 */
  public pivotB: THREE.Vector3;

  /** 第一个实体的轴向 */
  public axisA: THREE.Vector3;

  /** 第二个实体的轴向 */
  public axisB: THREE.Vector3;

  /** 距离约束的距离 */
  public distance: number;

  /** 弹簧约束的刚度 */
  public stiffness: number;

  /** 弹簧约束的阻尼 */
  public damping: number;

  /** 铰链约束的最大角度 */
  public maxAngle: number | null;

  /** 铰链约束的最小角度 */
  public minAngle: number | null;

  /** 铰链约束的马达启用 */
  public enableMotor: boolean;

  /** 铰链约束的马达速度 */
  public motorSpeed: number;

  /** 铰链约束的马达最大力 */
  public motorMaxForce: number;

  /** 碰撞启用 */
  public collideConnected: boolean;

  /** CANNON.js约束 */
  private constraint: CANNON.Constraint | null = null;

  /** 物理世界 */
  private world: CANNON.World | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /**
   * 创建物理约束组件
   * @param options 约束选项
   */
  constructor(options: PhysicsConstraintOptions) {
    super(PhysicsConstraintComponent.type);

    this.constraintType = options.type;
    this.entityA = options.entityA;
    this.entityB = options.entityB || null;
    this.pivotA = options.pivotA ? options.pivotA.clone() : new THREE.Vector3();
    this.pivotB = options.pivotB ? options.pivotB.clone() : new THREE.Vector3();
    this.axisA = options.axisA ? options.axisA.clone() : new THREE.Vector3(1, 0, 0);
    this.axisB = options.axisB ? options.axisB.clone() : new THREE.Vector3(1, 0, 0);
    this.distance = options.distance || 1;
    this.stiffness = options.stiffness || 100;
    this.damping = options.damping || 1;
    this.maxAngle = options.maxAngle !== undefined ? options.maxAngle : null;
    this.minAngle = options.minAngle !== undefined ? options.minAngle : null;
    this.enableMotor = options.enableMotor || false;
    this.motorSpeed = options.motorSpeed || 0;
    this.motorMaxForce = options.motorMaxForce || 1;
    this.collideConnected = options.collideConnected || false;
  }

  /**
   * 初始化约束
   * @param world 物理世界
   */
  public initialize(world: CANNON.World): void {
    if (this.initialized || !this.entity || this.destroyed) return;

    this.world = world;

    // 获取物理体
    const bodyA = this.getBody(this.entityA);
    const bodyB = this.entityB ? this.getBody(this.entityB) : null;

    if (!bodyA) {
      console.warn('约束的第一个实体没有物理体组件');
      return;
    }

    // 创建约束
    switch (this.constraintType) {
      case 'point':
        this.createPointConstraint(bodyA, bodyB);
        break;

      case 'distance':
        this.createDistanceConstraint(bodyA, bodyB);
        break;

      case 'hinge':
        this.createHingeConstraint(bodyA, bodyB);
        break;

      case 'lock':
        this.createLockConstraint(bodyA, bodyB);
        break;

      case 'spring':
        this.createSpringConstraint(bodyA, bodyB);
        break;

      default:
        console.warn(`不支持的约束类型: ${this.constraintType}`);
        return;
    }

    // 添加到物理世界
    if (this.constraint) {
      world.addConstraint(this.constraint);
    }

    this.initialized = true;
  }

  /**
   * 获取实体的物理体
   * @param entity 实体
   * @returns 物理体
   */
  private getBody(entity: Entity): CANNON.Body | null {
    const physicsBody = entity.getComponent('PhysicsBodyComponent') as any as any;
    if (!physicsBody) return null;

    // 使用类型断言访问 getCannonBody 方法
    return (physicsBody as any).getCannonBody();
  }

  /**
   * 创建点约束
   * @param bodyA 第一个物理体
   * @param bodyB 第二个物理体
   */
  private createPointConstraint(bodyA: CANNON.Body, bodyB: CANNON.Body | null): void {
    if (!bodyB) {
      console.warn('点约束需要两个物理体');
      return;
    }

    this.constraint = new CANNON.PointToPointConstraint(
      bodyA,
      new CANNON.Vec3(this.pivotA.x, this.pivotA.y, this.pivotA.z),
      bodyB,
      new CANNON.Vec3(this.pivotB.x, this.pivotB.y, this.pivotB.z),
      this.maxAngle ? this.maxAngle : undefined
    );
  }

  /**
   * 创建距离约束
   * @param bodyA 第一个物理体
   * @param bodyB 第二个物理体
   */
  private createDistanceConstraint(bodyA: CANNON.Body, bodyB: CANNON.Body | null): void {
    if (!bodyB) {
      console.warn('距离约束需要两个物理体');
      return;
    }

    this.constraint = new CANNON.DistanceConstraint(
      bodyA,
      bodyB,
      this.distance,
      this.maxAngle ? this.maxAngle : undefined
    );
  }

  /**
   * 创建铰链约束
   * @param bodyA 第一个物理体
   * @param bodyB 第二个物理体
   */
  private createHingeConstraint(bodyA: CANNON.Body, bodyB: CANNON.Body | null): void {
    if (!bodyB) {
      console.warn('铰链约束需要两个物理体');
      return;
    }

    this.constraint = new CANNON.HingeConstraint(bodyA, bodyB, {
      pivotA: new CANNON.Vec3(this.pivotA.x, this.pivotA.y, this.pivotA.z),
      pivotB: new CANNON.Vec3(this.pivotB.x, this.pivotB.y, this.pivotB.z),
      axisA: new CANNON.Vec3(this.axisA.x, this.axisA.y, this.axisA.z),
      axisB: new CANNON.Vec3(this.axisB.x, this.axisB.y, this.axisB.z),
      collideConnected: this.collideConnected
    });

    // 设置马达
    if (this.enableMotor) {
      (this.constraint as CANNON.HingeConstraint).enableMotor();
      (this.constraint as CANNON.HingeConstraint).setMotorSpeed(this.motorSpeed);
      (this.constraint as CANNON.HingeConstraint).setMotorMaxForce(this.motorMaxForce);
    }

    // 设置角度限制
    if (this.minAngle !== null && this.maxAngle !== null) {
      // CANNON.js 可能没有直接的 setLimits 方法，使用自定义属性设置
      const hingeConstraint = this.constraint as CANNON.HingeConstraint;
      // 使用类型断言设置限制
      (hingeConstraint as any).setLimits?.(this.minAngle, this.maxAngle);
    }
  }

  /**
   * 创建锁约束
   * @param bodyA 第一个物理体
   * @param bodyB 第二个物理体
   */
  private createLockConstraint(bodyA: CANNON.Body, bodyB: CANNON.Body | null): void {
    if (!bodyB) {
      console.warn('锁约束需要两个物理体');
      return;
    }

    this.constraint = new CANNON.LockConstraint(bodyA, bodyB, {
      maxForce: this.motorMaxForce
    });
  }

  /**
   * 创建弹簧约束
   * @param bodyA 第一个物理体
   * @param bodyB 第二个物理体
   */
  private createSpringConstraint(bodyA: CANNON.Body, bodyB: CANNON.Body | null): void {
    if (!bodyB) {
      console.warn('弹簧约束需要两个物理体');
      return;
    }

    // 创建一个Spring对象（不是Constraint）
    const spring = new CANNON.Spring(bodyA, bodyB, {
      localAnchorA: new CANNON.Vec3(this.pivotA.x, this.pivotA.y, this.pivotA.z),
      localAnchorB: new CANNON.Vec3(this.pivotB.x, this.pivotB.y, this.pivotB.z),
      restLength: this.distance,
      stiffness: this.stiffness,
      damping: this.damping
    });

    // 弹簧约束需要在每次更新时手动更新
    const world = this.world;
    if (world) {
      // 添加事件监听器来应用弹簧力
      world.addEventListener('postStep', () => {
        spring.applyForce();
      });

      // 存储Spring对象为自定义属性
      (this as any).spring = spring;

      // 创建一个空的约束对象以保持一致的接口
      this.constraint = new CANNON.PointToPointConstraint(
        bodyA,
        new CANNON.Vec3(this.pivotA.x, this.pivotA.y, this.pivotA.z),
        bodyB,
        new CANNON.Vec3(this.pivotB.x, this.pivotB.y, this.pivotB.z),
        0 // 使用最小力
      );

      // 禁用约束，只使用弹簧力
      // 使用类型断言访问 enabled 属性
      (this.constraint as any).enabled = false;
    }
  }

  /**
   * 获取CANNON.js约束
   * @returns CANNON.js约束
   */
  public getConstraint(): CANNON.Constraint | null {
    return this.constraint;
  }

  /**
   * 启用马达（仅适用于铰链约束）
   */
  public enableMotorFunc(): void {
    if (this.constraint && this.constraintType === 'hinge') {
      (this.constraint as CANNON.HingeConstraint).enableMotor();
      this.enableMotor = true;
    }
  }

  /**
   * 禁用马达（仅适用于铰链约束）
   */
  public disableMotor(): void {
    if (this.constraint && this.constraintType === 'hinge') {
      (this.constraint as CANNON.HingeConstraint).disableMotor();
      this.enableMotor = false;
    }
  }

  /**
   * 设置马达速度（仅适用于铰链约束）
   * @param speed 马达速度
   */
  public setMotorSpeed(speed: number): void {
    if (this.constraint && this.constraintType === 'hinge') {
      (this.constraint as CANNON.HingeConstraint).setMotorSpeed(speed);
      this.motorSpeed = speed;
    }
  }

  /**
   * 设置马达最大力（仅适用于铰链约束）
   * @param maxForce 马达最大力
   */
  public setMotorMaxForce(maxForce: number): void {
    if (this.constraint && this.constraintType === 'hinge') {
      (this.constraint as CANNON.HingeConstraint).setMotorMaxForce(maxForce);
      this.motorMaxForce = maxForce;
    }
  }

  /**
   * 设置角度限制（仅适用于铰链约束）
   * @param min 最小角度
   * @param max 最大角度
   */
  public setLimits(min: number, max: number): void {
    if (this.constraint && this.constraintType === 'hinge') {
      // 使用类型断言设置限制
      (this.constraint as any).setLimits?.(min, max);
      this.minAngle = min;
      this.maxAngle = max;
    }
  }

  /**
   * 销毁约束
   */
  public dispose(): void {
    if (this.destroyed) return;

    if (this.initialized && this.constraint && this.world) {
      this.world.removeConstraint(this.constraint);
      this.constraint = null;
      this.world = null;
      this.initialized = false;
    }

    // 清理Spring对象（如果存在）
    if ((this as any).spring) {
      (this as any).spring = null;
    }

    this.destroyed = true;

    // 调用基类的dispose方法
    super.dispose();
  }
}
