/**
 * 键盘输入设备
 */
import { BaseInputDevice } from '../InputDevice';
import { KeyState } from '../InputSystem';

/**
 * 键盘输入设备
 */
export class KeyboardDevice extends BaseInputDevice {
  /** 目标元素 */
  private element: HTMLElement;

  /** 是否阻止默认行为 */
  private preventDefault: boolean;

  /** 是否阻止事件传播 */
  private stopPropagation: boolean;

  /** 键盘事件处理器 */
  private keyboardEventHandlers: { [key: string]: (event: KeyboardEvent) => void } = {};

  /**
   * 创建键盘输入设备
   * @param element 目标元素
   * @param preventDefault 是否阻止默认行为
   * @param stopPropagation 是否阻止事件传播
   */
  constructor(element: HTMLElement = document.body, preventDefault: boolean = true, stopPropagation: boolean = false) {
    super('keyboard');
    this.element = element;
    this.preventDefault = preventDefault;
    this.stopPropagation = stopPropagation;

    // 初始化事件处理器
    this.initEventHandlers();
  }

  /**
   * 初始化事件处理器
   */
  private initEventHandlers(): void {
    // 键盘按下事件
    this.keyboardEventHandlers.keydown = this.handleKeyDown.bind(this);

    // 键盘释放事件
    this.keyboardEventHandlers.keyup = this.handleKeyUp.bind(this);
  }

  /**
   * 初始化设备
   */
  public initialize(): void {
    if (this.initialized) return;

    // 添加事件监听器
    this.addEventListeners();

    super.initialize();
  }

  /**
   * 销毁设备
   */
  public destroy(): void {
    if (this.destroyed) return;

    // 移除事件监听器
    this.removeEventListeners();

    super.destroy();
  }

  /**
   * 更新设备状态
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || this.destroyed) return;

    // 更新按键状态
    for (const [key, value] of this.values.entries()) {
      if (value === KeyState.DOWN) {
        // 将按下状态更新为按住状态
        this.setValue(key, KeyState.PRESSED);
      } else if (value === KeyState.UP) {
        // 将释放状态更新为未按下状态
        this.setValue(key, KeyState.NONE);
      }
    }

    super.update(deltaTime);
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 添加键盘事件监听器
    for (const [event, handler] of Object.entries(this.keyboardEventHandlers)) {
      document.addEventListener(event, handler as EventListener, { passive: !this.preventDefault });
    }
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 移除键盘事件监听器
    for (const [event, handler] of Object.entries(this.keyboardEventHandlers)) {
      document.removeEventListener(event, handler as EventListener);
    }
  }

  /**
   * 处理键盘按键按下事件
   * @param event 键盘事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    const key = event.code || event.key;

    // 如果按键未按下或已释放，则设置为按下状态
    if (this.getValue(key) !== KeyState.PRESSED) {
      this.setValue(key, KeyState.DOWN);

      // 触发按键按下事件
      this.eventEmitter.emit(`${key}:down`, {
        key,
        code: event.code,
        altKey: event.altKey,
        ctrlKey: event.ctrlKey,
        shiftKey: event.shiftKey,
        metaKey: event.metaKey,
        repeat: event.repeat
      });
    }
  }

  /**
   * 处理键盘按键释放事件
   * @param event 键盘事件
   */
  private handleKeyUp(event: KeyboardEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    const key = event.code || event.key;

    // 设置按键为释放状态
    this.setValue(key, KeyState.UP);

    // 触发按键释放事件
    this.eventEmitter.emit(`${key}:up`, {
      key,
      code: event.code,
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 检查按键是否按下
   * @param key 按键
   * @returns 是否按下
   */
  public isKeyDown(key: string): boolean {
    const state = this.getValue(key);
    return state === KeyState.DOWN || state === KeyState.PRESSED;
  }

  /**
   * 检查按键是否刚按下
   * @param key 按键
   * @returns 是否刚按下
   */
  public isKeyJustDown(key: string): boolean {
    return this.getValue(key) === KeyState.DOWN;
  }

  /**
   * 检查按键是否刚释放
   * @param key 按键
   * @returns 是否刚释放
   */
  public isKeyJustUp(key: string): boolean {
    return this.getValue(key) === KeyState.UP;
  }
}
