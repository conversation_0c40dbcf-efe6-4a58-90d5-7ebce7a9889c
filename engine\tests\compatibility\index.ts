/**
 * 兼容性测试入口文件
 */
import {
  runAllTests,
  runCoreTests,
  runPhysicsTests,
  runRenderingTests,
  runAnimationTests,
  runInputTests,
  runSceneTests,
  runNetworkTests,
  runVisualScriptTests,
  runInteractionTests,
  runAvatarTests,
  runMocapTests
} from './CompatibilityTestRunner';
import { Debug } from '../../src/utils/Debug';

// 获取命令行参数
const args = process.argv.slice(2);

/**
 * 主函数
 */
async function main(): Promise<void> {
  Debug.log('兼容性测试', '开始运行兼容性测试');
  
  // 根据命令行参数运行测试
  if (args.length === 0 || args[0] === 'all') {
    await runAllTests();
  } else if (args[0] === 'core') {
    await runCoreTests();
  } else if (args[0] === 'physics') {
    await runPhysicsTests();
  } else if (args[0] === 'rendering') {
    await runRenderingTests();
  } else if (args[0] === 'animation') {
    await runAnimationTests();
  } else if (args[0] === 'input') {
    await runInputTests();
  } else if (args[0] === 'scene') {
    await runSceneTests();
  } else if (args[0] === 'network') {
    await runNetworkTests();
  } else if (args[0] === 'visualscript') {
    await runVisualScriptTests();
  } else if (args[0] === 'interaction') {
    await runInteractionTests();
  } else if (args[0] === 'avatar') {
    await runAvatarTests();
  } else if (args[0] === 'mocap') {
    await runMocapTests();
  } else {
    Debug.error('兼容性测试', `未知的测试类型: ${args[0]}`);
    Debug.log('兼容性测试', '可用的测试类型: all, core, physics, rendering, animation, input, scene, network, visualscript, interaction, avatar, mocap');
  }
  
  Debug.log('兼容性测试', '兼容性测试完成');
}

// 运行主函数
main().catch(error => {
  Debug.error('兼容性测试', `运行兼容性测试失败: ${error}`);
  process.exit(1);
});
