/**
 * InteractableComponent类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { InteractableComponent } from '../../src/interaction/InteractableComponent';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import * as THREE from 'three';

describe('InteractableComponent', () => {
  let engine: Engine;
  let world: World;
  let entity: Entity;
  let interactableComponent: InteractableComponent;
  
  // 在每个测试前创建一个新的可交互组件实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建实体
    entity = new Entity(world);
    entity.name = '可交互物体';
    
    // 添加变换组件
    const transform = new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity.addComponent(transform);
    
    // 创建可交互组件
    interactableComponent = new InteractableComponent(entity, {
      highlightable: true,
      interactable: true,
      priority: 0
    });
    
    // 添加可交互组件到实体
    entity.addComponent(interactableComponent);
    
    // 添加实体到世界
    world.addEntity(entity);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试可交互组件初始化
  it('应该正确初始化可交互组件', () => {
    expect(interactableComponent).toBeDefined();
    expect(interactableComponent.getEntity()).toBe(entity);
    expect(interactableComponent.isHighlightable()).toBe(true);
    expect(interactableComponent.isInteractable()).toBe(true);
    expect(interactableComponent.getPriority()).toBe(0);
  });
  
  // 测试高亮功能
  it('应该能够高亮可交互组件', () => {
    // 创建模拟的Three.js对象
    const mockMesh = new THREE.Mesh(
      new THREE.BoxGeometry(1, 1, 1),
      new THREE.MeshStandardMaterial({ color: 0xffffff })
    );
    
    // 设置可交互组件的Three.js对象
    (interactableComponent as any).object3D = mockMesh;
    
    // 高亮可交互组件
    interactableComponent.highlight(0xff0000, 0.5);
    
    // 验证高亮效果
    expect(interactableComponent.isHighlighted()).toBe(true);
    
    // 取消高亮
    interactableComponent.unhighlight();
    
    // 验证高亮已取消
    expect(interactableComponent.isHighlighted()).toBe(false);
  });
  
  // 测试交互功能
  it('应该能够与可交互组件交互', () => {
    // 创建交互事件监听器
    const interactSpy = vi.fn();
    
    // 添加事件监听器
    interactableComponent.on('interact', interactSpy);
    
    // 触发交互
    interactableComponent.interact();
    
    // 验证交互事件被触发
    expect(interactSpy).toHaveBeenCalled();
  });
  
  // 测试设置高亮状态
  it('应该能够设置和获取高亮状态', () => {
    // 验证初始高亮状态
    expect(interactableComponent.isHighlightable()).toBe(true);
    
    // 设置高亮状态
    interactableComponent.setHighlightable(false);
    
    // 验证高亮状态已更改
    expect(interactableComponent.isHighlightable()).toBe(false);
    
    // 再次设置高亮状态
    interactableComponent.setHighlightable(true);
    
    // 验证高亮状态已更改
    expect(interactableComponent.isHighlightable()).toBe(true);
  });
  
  // 测试设置交互状态
  it('应该能够设置和获取交互状态', () => {
    // 验证初始交互状态
    expect(interactableComponent.isInteractable()).toBe(true);
    
    // 设置交互状态
    interactableComponent.setInteractable(false);
    
    // 验证交互状态已更改
    expect(interactableComponent.isInteractable()).toBe(false);
    
    // 再次设置交互状态
    interactableComponent.setInteractable(true);
    
    // 验证交互状态已更改
    expect(interactableComponent.isInteractable()).toBe(true);
  });
  
  // 测试设置优先级
  it('应该能够设置和获取优先级', () => {
    // 验证初始优先级
    expect(interactableComponent.getPriority()).toBe(0);
    
    // 设置优先级
    interactableComponent.setPriority(10);
    
    // 验证优先级已更改
    expect(interactableComponent.getPriority()).toBe(10);
  });
  
  // 测试交互回调
  it('应该能够设置和调用交互回调', () => {
    // 创建交互回调
    const interactCallback = vi.fn();
    
    // 设置交互回调
    interactableComponent.setInteractCallback(interactCallback);
    
    // 触发交互
    interactableComponent.interact();
    
    // 验证交互回调被调用
    expect(interactCallback).toHaveBeenCalled();
  });
  
  // 测试交互事件
  it('应该能够发射和监听交互事件', () => {
    // 创建交互开始事件监听器
    const interactStartSpy = vi.fn();
    
    // 创建交互结束事件监听器
    const interactEndSpy = vi.fn();
    
    // 添加事件监听器
    interactableComponent.on('interactStart', interactStartSpy);
    interactableComponent.on('interactEnd', interactEndSpy);
    
    // 触发交互开始事件
    interactableComponent.emit('interactStart', { entity });
    
    // 验证交互开始事件被触发
    expect(interactStartSpy).toHaveBeenCalledWith({ entity });
    
    // 触发交互结束事件
    interactableComponent.emit('interactEnd', { entity });
    
    // 验证交互结束事件被触发
    expect(interactEndSpy).toHaveBeenCalledWith({ entity });
  });
  
  // 测试交互数据
  it('应该能够设置和获取交互数据', () => {
    // 设置交互数据
    const interactionData = { type: 'button', action: 'open' };
    interactableComponent.setInteractionData(interactionData);
    
    // 验证交互数据
    expect(interactableComponent.getInteractionData()).toEqual(interactionData);
  });
  
  // 测试交互标签
  it('应该能够设置和获取交互标签', () => {
    // 设置交互标签
    interactableComponent.setLabel('按钮');
    
    // 验证交互标签
    expect(interactableComponent.getLabel()).toBe('按钮');
  });
  
  // 测试交互提示
  it('应该能够设置和获取交互提示', () => {
    // 设置交互提示
    interactableComponent.setPrompt('按E键交互');
    
    // 验证交互提示
    expect(interactableComponent.getPrompt()).toBe('按E键交互');
  });
});
