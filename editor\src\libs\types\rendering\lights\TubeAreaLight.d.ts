/**
 * 管状区域光
 * 物理精确的管状区域光
 */
import * as THREE from 'three';
import { AreaLight, AreaLightOptions, AreaLightType } from './AreaLight';
/**
 * 管状区域光选项接口
 */
export interface TubeAreaLightOptions extends AreaLightOptions {
    /** 光源类型 */
    type: AreaLightType.TUBE;
    /** 长度 */
    length?: number;
    /** 半径 */
    radius?: number;
    /** 是否使用物理单位 */
    usePhysicalUnits?: boolean;
    /** 功率（瓦特） */
    power?: number;
    /** 发光效率（流明/瓦特） */
    efficacy?: number;
    /** 辅助对象细分数 */
    helperSegments?: number;
}
/**
 * 管状区域光组件类
 */
export declare class TubeAreaLightComponent extends AreaLight {
    /** 长度 */
    private length;
    /** 半径 */
    private radius;
    /** 是否使用物理单位 */
    private usePhysicalUnits;
    /** 功率（瓦特） */
    private power;
    /** 发光效率（流明/瓦特） */
    private efficacy;
    /** 辅助对象细分数 */
    private helperSegments;
    /** 辅助对象材质 */
    private helperMaterial;
    /**
     * 创建管状区域光组件
     * @param options 管状区域光选项
     */
    constructor(options: TubeAreaLightOptions);
    /**
     * 创建光源
     * @param options 管状区域光选项
     * @returns Three.js线性光源（使用多个点光源模拟）
     */
    protected createLight(options: TubeAreaLightOptions): THREE.Group;
    /**
     * 创建辅助对象
     * @returns Three.js管状区域光辅助对象
     */
    protected createHelper(): THREE.Object3D;
    /**
     * 更新辅助对象颜色
     */
    protected updateHelperColor(): void;
    /**
     * 更新物理强度
     */
    private updatePhysicalIntensity;
    /**
     * 设置长度
     * @param length 长度
     */
    setLength(length: number): void;
    /**
     * 获取长度
     * @returns 长度
     */
    getLength(): number;
    /**
     * 设置半径
     * @param radius 半径
     */
    setRadius(radius: number): void;
    /**
     * 获取半径
     * @returns 半径
     */
    getRadius(): number;
    /**
     * 设置是否使用物理单位
     * @param use 是否使用
     */
    setUsePhysicalUnits(use: boolean): void;
    /**
     * 获取是否使用物理单位
     * @returns 是否使用
     */
    isUsePhysicalUnits(): boolean;
    /**
     * 设置功率
     * @param power 功率（瓦特）
     */
    setPower(power: number): void;
    /**
     * 获取功率
     * @returns 功率（瓦特）
     */
    getPower(): number;
    /**
     * 设置发光效率
     * @param efficacy 发光效率（流明/瓦特）
     */
    setEfficacy(efficacy: number): void;
    /**
     * 获取发光效率
     * @returns 发光效率（流明/瓦特）
     */
    getEfficacy(): number;
    /**
     * 设置辅助对象细分数
     * @param segments 细分数
     */
    setHelperSegments(segments: number): void;
    /**
     * 获取辅助对象细分数
     * @returns 细分数
     */
    getHelperSegments(): number;
    /**
     * 更新光源位置
     */
    private updateLightPositions;
    /**
     * 更新辅助对象
     */
    private updateHelper;
    /**
     * 更新组件
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
}
