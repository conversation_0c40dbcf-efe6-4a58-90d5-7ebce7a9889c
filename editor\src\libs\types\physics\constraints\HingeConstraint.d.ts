/**
 * 铰链约束
 * 将两个物体通过一个轴连接起来，允许它们绕该轴旋转
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { PhysicsConstraint } from './PhysicsConstraint';
/**
 * 铰链约束选项
 */
export interface HingeConstraintOptions {
    /** 源物体上的连接点（局部坐标） */
    pivotA?: THREE.Vector3;
    /** 目标物体上的连接点（局部坐标） */
    pivotB?: THREE.Vector3;
    /** 源物体上的轴（局部坐标） */
    axisA?: THREE.Vector3;
    /** 目标物体上的轴（局部坐标） */
    axisB?: THREE.Vector3;
    /** 最大力 */
    maxForce?: number;
    /** 是否允许连接的物体之间碰撞 */
    collideConnected?: boolean;
    /** 电机启用 */
    motorEnabled?: boolean;
    /** 电机速度 */
    motorSpeed?: number;
    /** 电机最大力 */
    motorMaxForce?: number;
    /** 角度限制启用 */
    angleEnabled?: boolean;
    /** 最小角度 */
    angleMin?: number;
    /** 最大角度 */
    angleMax?: number;
}
/**
 * 铰链约束
 */
export declare class HingeConstraint extends PhysicsConstraint {
    /** 组件类型 */
    static readonly type: string;
    /** 源物体上的连接点（局部坐标） */
    private pivotA;
    /** 目标物体上的连接点（局部坐标） */
    private pivotB;
    /** 源物体上的轴（局部坐标） */
    private axisA;
    /** 目标物体上的轴（局部坐标） */
    private axisB;
    /** 最大力 */
    private maxForce;
    /** 电机启用 */
    private motorEnabled;
    /** 电机速度 */
    private motorSpeed;
    /** 电机最大力 */
    private motorMaxForce;
    /** 角度限制启用 */
    private angleEnabled;
    /** 最小角度 */
    private angleMin;
    /** 最大角度 */
    private angleMax;
    /**
     * 创建铰链约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    constructor(targetEntity?: Entity | null, options?: HingeConstraintOptions);
    /**
     * 创建约束
     */
    protected createConstraint(): void;
    /**
     * 设置源物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    setPivotA(pivot: THREE.Vector3): void;
    /**
     * 获取源物体上的连接点
     * @returns 连接点（局部坐标）
     */
    getPivotA(): THREE.Vector3;
    /**
     * 设置目标物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    setPivotB(pivot: THREE.Vector3): void;
    /**
     * 获取目标物体上的连接点
     * @returns 连接点（局部坐标）
     */
    getPivotB(): THREE.Vector3;
    /**
     * 设置源物体上的轴
     * @param axis 轴（局部坐标）
     */
    setAxisA(axis: THREE.Vector3): void;
    /**
     * 获取源物体上的轴
     * @returns 轴（局部坐标）
     */
    getAxisA(): THREE.Vector3;
    /**
     * 设置目标物体上的轴
     * @param axis 轴（局部坐标）
     */
    setAxisB(axis: THREE.Vector3): void;
    /**
     * 获取目标物体上的轴
     * @returns 轴（局部坐标）
     */
    getAxisB(): THREE.Vector3;
    /**
     * 启用电机
     */
    enableMotor(): void;
    /**
     * 禁用电机
     */
    disableMotor(): void;
    /**
     * 设置电机速度
     * @param speed 速度（弧度/秒）
     */
    setMotorSpeed(speed: number): void;
    /**
     * 获取电机速度
     * @returns 速度（弧度/秒）
     */
    getMotorSpeed(): number;
    /**
     * 设置电机最大力
     * @param maxForce 最大力
     */
    setMotorMaxForce(maxForce: number): void;
    /**
     * 获取电机最大力
     * @returns 最大力
     */
    getMotorMaxForce(): number;
    /**
     * 启用角度限制
     * 注意：CANNON.js的HingeConstraint没有内置的角度限制功能
     * 这里只是设置内部状态，实际限制需要在物理更新中手动实现
     */
    enableAngleLimits(): void;
    /**
     * 禁用角度限制
     */
    disableAngleLimits(): void;
    /**
     * 设置角度限制
     * @param min 最小角度（弧度）
     * @param max 最大角度（弧度）
     * 注意：CANNON.js的HingeConstraint没有内置的角度限制功能
     */
    setAngleLimits(min: number, max: number): void;
    /**
     * 获取最小角度
     * @returns 最小角度（弧度）
     */
    getAngleMin(): number;
    /**
     * 获取最大角度
     * @returns 最大角度（弧度）
     */
    getAngleMax(): number;
    /**
     * 获取当前角度
     * @returns 当前角度（弧度）
     * 注意：CANNON.js的HingeConstraint没有内置的getAngle方法
     * 需要通过计算两个物体的相对旋转来获取角度
     */
    getAngle(): number;
    /**
     * 重新创建约束
     */
    private recreateConstraint;
}
