/**
 * 泛光效果
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 泛光效果选项
 */
export interface BloomEffectOptions extends PostProcessingEffectOptions {
    /** 强度 */
    strength?: number;
    /** 半径 */
    radius?: number;
    /** 阈值 */
    threshold?: number;
    /** 是否使用选择性泛光 */
    selectiveBloom?: boolean;
}
/**
 * 泛光效果
 */
export declare class BloomEffect extends PostProcessingEffect {
    /** 强度 */
    private strength;
    /** 半径 */
    private radius;
    /** 阈值 */
    private threshold;
    /** 是否使用选择性泛光 */
    private selectiveBloom;
    /** 泛光通道 */
    private bloomPass;
    /** 泛光层 */
    private BLOOM_LAYER;
    /** 原始场景 */
    private originalScene;
    /** 暗物体材质 */
    private darkMaterial;
    /** 材质缓存 */
    private materials;
    /**
     * 创建泛光效果
     * @param options 泛光效果选项
     */
    constructor(options?: BloomEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 更新效果
     * @param _deltaTime 帧间隔时间（秒）- 未使用，保留以符合接口
     */
    update(_deltaTime: number): void;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
    /**
     * 设置强度
     * @param strength 强度
     */
    setStrength(strength: number): void;
    /**
     * 获取强度
     * @returns 强度
     */
    getStrength(): number;
    /**
     * 设置半径
     * @param radius 半径
     */
    setRadius(radius: number): void;
    /**
     * 获取半径
     * @returns 半径
     */
    getRadius(): number;
    /**
     * 设置阈值
     * @param threshold 阈值
     */
    setThreshold(threshold: number): void;
    /**
     * 获取阈值
     * @returns 阈值
     */
    getThreshold(): number;
    /**
     * 设置是否使用选择性泛光
     * @param selective 是否使用选择性泛光
     */
    setSelectiveBloom(selective: boolean): void;
    /**
     * 获取是否使用选择性泛光
     * @returns 是否使用选择性泛光
     */
    isSelectiveBloom(): boolean;
    /**
     * 设置场景
     * @param scene 场景
     */
    setScene(scene: THREE.Scene): void;
    /**
     * 将对象添加到泛光层
     * @param object 对象
     */
    addToBloomLayer(object: THREE.Object3D): void;
    /**
     * 将对象从泛光层移除
     * @param object 对象
     */
    removeFromBloomLayer(object: THREE.Object3D): void;
    /**
     * 使非泛光对象变暗
     */
    private darkenNonBloomObjects;
    /**
     * 恢复原始材质
     */
    private restoreOriginalMaterials;
    /**
     * 销毁效果
     */
    dispose(): void;
}
