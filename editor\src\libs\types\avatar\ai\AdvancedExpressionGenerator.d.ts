/**
 * 高级表情生成器
 * 用于生成复杂的面部表情动画
 */
import { EmotionAnalysisResult, EmotionTimePoint } from './AdvancedEmotionAnalyzer';
/**
 * 表情生成请求
 */
export interface ExpressionGenerationRequest {
    /** 请求ID */
    id: string;
    /** 情感分析结果 */
    emotionResult: EmotionAnalysisResult;
    /** 持续时间（秒） */
    duration: number;
    /** 是否循环 */
    loop?: boolean;
    /** 风格 */
    style?: ExpressionStyle;
    /** 强度 */
    intensity?: number;
    /** 是否启用微表情 */
    enableMicroExpressions?: boolean;
    /** 微表情配置 */
    microExpressionConfig?: MicroExpressionConfig;
    /** 混合配置 */
    blendConfig?: ExpressionBlendConfig;
    /** 自定义参数 */
    customParams?: Record<string, any>;
}
/**
 * 表情生成结果
 */
export interface ExpressionGenerationResult {
    /** 请求ID */
    id: string;
    /** 是否成功 */
    success: boolean;
    /** 错误信息 */
    error?: string;
    /** 表情数据 */
    expressionData?: ExpressionData;
    /** 生成时间（毫秒） */
    generationTime?: number;
}
/**
 * 表情数据
 */
export interface ExpressionData {
    /** 表情关键帧 */
    keyframes: ExpressionKeyframe[];
    /** 持续时间（秒） */
    duration: number;
    /** 是否循环 */
    loop: boolean;
    /** 表情名称 */
    name: string;
    /** 表情类型 */
    type: string;
    /** 表情强度 */
    intensity: number;
    /** 微表情数据 */
    microExpressions?: MicroExpressionData[];
}
/**
 * 表情关键帧
 */
export interface ExpressionKeyframe {
    /** 时间（秒） */
    time: number;
    /** 表情混合形状 */
    blendShapes: Record<string, number>;
    /** 情感 */
    emotion?: string;
    /** 强度 */
    intensity?: number;
}
/**
 * 微表情数据
 */
export interface MicroExpressionData {
    /** 类型 */
    type: string;
    /** 开始时间（秒） */
    startTime: number;
    /** 持续时间（秒） */
    duration: number;
    /** 强度 */
    intensity: number;
    /** 表情混合形状 */
    blendShapes: Record<string, number>;
}
/**
 * 表情风格
 */
export type ExpressionStyle = 'natural' | 'cartoon' | 'exaggerated' | 'subtle' | 'dramatic';
/**
 * 微表情配置
 */
export interface MicroExpressionConfig {
    /** 是否启用 */
    enabled: boolean;
    /** 频率 */
    frequency: number;
    /** 强度 */
    intensity: number;
    /** 持续时间 */
    duration: number;
    /** 类型 */
    types: string[];
    /** 随机性 */
    randomness: number;
}
/**
 * 表情混合配置
 */
export interface ExpressionBlendConfig {
    /** 混合模式 */
    mode: 'add' | 'multiply' | 'override' | 'weighted';
    /** 混合权重 */
    weight?: number;
    /** 过渡时间 */
    transitionTime: number;
    /** 情感变化 */
    emotionChanges?: EmotionTimePoint[];
}
/**
 * 高级表情生成器配置
 */
export interface AdvancedExpressionGeneratorConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 默认风格 */
    defaultStyle?: ExpressionStyle;
    /** 默认强度 */
    defaultIntensity?: number;
    /** 是否启用微表情 */
    enableMicroExpressions?: boolean;
    /** 微表情配置 */
    microExpressionConfig?: Partial<MicroExpressionConfig>;
    /** 混合配置 */
    blendConfig?: Partial<ExpressionBlendConfig>;
    /** 是否使用物理模拟 */
    usePhysics?: boolean;
    /** 物理参数 */
    physicsParams?: Record<string, any>;
}
/**
 * 表情混合形状映射
 */
export interface BlendShapeMapping {
    /** 情感到混合形状的映射 */
    emotionToBlendShapes: Record<string, Record<string, number>>;
    /** 微表情到混合形状的映射 */
    microExpressionToBlendShapes: Record<string, Record<string, number>>;
}
/**
 * 高级表情生成器
 */
export declare class AdvancedExpressionGenerator {
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 混合形状映射 */
    private blendShapeMapping;
    /** 事件发射器 */
    private eventEmitter;
    /** 随机数生成器种子 */
    private randomSeed;
    /**
     * 构造函数
     * @param config 配置
     * @param blendShapeMapping 混合形状映射
     */
    constructor(config?: AdvancedExpressionGeneratorConfig, blendShapeMapping?: BlendShapeMapping);
    /**
     * 生成表情
     * @param request 请求
     * @returns 生成结果
     */
    generateExpression(request: ExpressionGenerationRequest): Promise<ExpressionGenerationResult>;
    /**
     * 创建表情数据
     * @param request 请求
     * @returns 表情数据
     */
    private createExpressionData;
    /**
     * 创建表情关键帧
     * @param request 请求
     * @returns 表情关键帧数组
     */
    private createExpressionKeyframes;
    /**
     * 创建默认情感变化
     * @param emotionResult 情感分析结果
     * @returns 情感时间点数组
     */
    private createDefaultEmotionChanges;
    /**
     * 创建微表情
     * @param request 请求
     * @returns 微表情数据数组
     */
    private createMicroExpressions;
    /**
     * 获取情感的混合形状
     * @param emotion 情感
     * @param intensity 强度
     * @param style 风格
     * @returns 混合形状
     */
    private getBlendShapesForEmotion;
    /**
     * 获取微表情的混合形状
     * @param type 类型
     * @param intensity 强度
     * @returns 混合形状
     */
    private getBlendShapesForMicroExpression;
    /**
     * 应用风格到混合形状
     * @param blendShapes 混合形状
     * @param style 风格
     */
    private applyStyleToBlendShapes;
    /**
     * 设置随机种子
     * @param seed 种子
     */
    private setRandomSeed;
    /**
     * 生成随机数
     * @returns 0-1之间的随机数
     */
    private random;
    /**
     * 设置混合形状映射
     * @param mapping 映射
     */
    setBlendShapeMapping(mapping: BlendShapeMapping): void;
    /**
     * 获取混合形状映射
     * @returns 映射
     */
    getBlendShapeMapping(): BlendShapeMapping;
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    on(event: string, listener: (...args: any[]) => void): void;
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    off(event: string, listener: (...args: any[]) => void): void;
    /**
     * 销毁
     */
    dispose(): void;
}
