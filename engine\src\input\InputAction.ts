/**
 * 输入动作
 * 表示一个可以被触发的输入动作
 */
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 输入动作类型
 */
export enum InputActionType {
  /** 按钮动作 */
  BUTTON = 'button',
  /** 值动作 */
  VALUE = 'value',
  /** 向量动作 */
  VECTOR = 'vector'
}

/**
 * 输入动作接口
 */
export interface InputAction {
  /**
   * 获取动作名称
   * @returns 动作名称
   */
  getName(): string;

  /**
   * 获取动作类型
   * @returns 动作类型
   */
  getType(): InputActionType;

  /**
   * 更新动作状态
   * @param value 动作值
   */
  update(value: any): void;

  /**
   * 检查动作状态是否发生变化
   * @returns 是否发生变化
   */
  hasChanged(): boolean;

  /**
   * 获取动作值
   * @returns 动作值
   */
  getValue(): any;

  /**
   * 重置动作状态
   */
  reset(): void;

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  addEventListener(event: string, callback: (data: any) => void): void;

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  removeEventListener(event: string, callback: (data: any) => void): void;
}

/**
 * 输入动作基类
 */
export abstract class BaseInputAction implements InputAction {
  /** 动作名称 */
  protected name: string;

  /** 动作类型 */
  protected type: InputActionType;

  /** 动作值 */
  protected value: any;

  /** 上一次动作值 */
  protected previousValue: any;

  /** 是否发生变化 */
  protected changed: boolean = false;

  /** 事件发射器 */
  protected eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 创建输入动作
   * @param name 动作名称
   * @param type 动作类型
   */
  constructor(name: string, type: InputActionType) {
    this.name = name;
    this.type = type;
  }

  /**
   * 获取动作名称
   * @returns 动作名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 获取动作类型
   * @returns 动作类型
   */
  public getType(): InputActionType {
    return this.type;
  }

  /**
   * 更新动作状态
   * @param value 动作值
   */
  public update(value: any): void {
    this.previousValue = this.value;
    this.value = value;
    this.changed = this.previousValue !== this.value;

    // 触发事件
    if (this.changed) {
      this.eventEmitter.emit('changed', { value: this.value, previousValue: this.previousValue });
    }
  }

  /**
   * 检查动作状态是否发生变化
   * @returns 是否发生变化
   */
  public hasChanged(): boolean {
    return this.changed;
  }

  /**
   * 获取动作值
   * @returns 动作值
   */
  public getValue(): any {
    return this.value;
  }

  /**
   * 重置动作状态
   */
  public reset(): void {
    this.previousValue = this.value;
    this.changed = false;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }
}

/**
 * 按钮动作
 */
export class ButtonInputAction extends BaseInputAction {
  /** 是否按下 */
  private pressed: boolean = false;

  /** 是否刚按下 */
  private justPressed: boolean = false;

  /** 是否刚释放 */
  private justReleased: boolean = false;

  /**
   * 创建按钮动作
   * @param name 动作名称
   */
  constructor(name: string) {
    super(name, InputActionType.BUTTON);
    this.value = false;
    this.previousValue = false;
  }

  /**
   * 更新动作状态
   * @param value 动作值
   */
  public update(value: boolean): void {
    this.previousValue = this.value;
    this.value = value;
    this.changed = this.previousValue !== this.value;

    this.pressed = this.value;
    this.justPressed = this.value && !this.previousValue;
    this.justReleased = !this.value && this.previousValue;

    // 触发事件
    if (this.changed) {
      this.eventEmitter.emit('changed', { value: this.value, previousValue: this.previousValue });
    }
    if (this.justPressed) {
      this.eventEmitter.emit('started', { value: this.value });
    }
    if (this.justReleased) {
      this.eventEmitter.emit('ended', { value: this.value });
    }
  }

  /**
   * 检查按钮是否按下
   * @returns 是否按下
   */
  public isPressed(): boolean {
    return this.pressed;
  }

  /**
   * 检查按钮是否刚按下
   * @returns 是否刚按下
   */
  public isJustPressed(): boolean {
    return this.justPressed;
  }

  /**
   * 检查按钮是否刚释放
   * @returns 是否刚释放
   */
  public isJustReleased(): boolean {
    return this.justReleased;
  }

  /**
   * 重置动作状态
   */
  public reset(): void {
    super.reset();
    this.justPressed = false;
    this.justReleased = false;
  }
}

/**
 * 值动作
 */
export class ValueInputAction extends BaseInputAction {
  /**
   * 创建值动作
   * @param name 动作名称
   */
  constructor(name: string) {
    super(name, InputActionType.VALUE);
    this.value = 0;
    this.previousValue = 0;
  }
}

/**
 * 向量动作
 */
export class VectorInputAction extends BaseInputAction {
  /**
   * 创建向量动作
   * @param name 动作名称
   */
  constructor(name: string) {
    super(name, InputActionType.VECTOR);
    this.value = [0, 0];
    this.previousValue = [0, 0];
  }

  /**
   * 更新动作状态
   * @param value 动作值
   */
  public update(value: [number, number]): void {
    this.previousValue = this.value;
    this.value = value;
    this.changed = this.previousValue[0] !== this.value[0] || this.previousValue[1] !== this.value[1];
  }

  /**
   * 获取X轴值
   * @returns X轴值
   */
  public getX(): number {
    return this.value[0];
  }

  /**
   * 获取Y轴值
   * @returns Y轴值
   */
  public getY(): number {
    return this.value[1];
  }

  /**
   * 获取向量长度
   * @returns 向量长度
   */
  public getLength(): number {
    return Math.sqrt(this.value[0] * this.value[0] + this.value[1] * this.value[1]);
  }

  /**
   * 获取向量方向
   * @returns 向量方向（弧度）
   */
  public getDirection(): number {
    return Math.atan2(this.value[1], this.value[0]);
  }
}
