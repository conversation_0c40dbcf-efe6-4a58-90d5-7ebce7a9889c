@echo off
echo ========================================
echo 启动数字人RAG交互系统微服务集群
echo ========================================

REM 设置环境变量
set COMPOSE_PROJECT_NAME=digital-human-rag
set DOCKER_BUILDKIT=1
set COMPOSE_DOCKER_CLI_BUILD=1

REM 检查Docker是否运行
echo 检查Docker服务状态...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker服务未运行，请先启动Docker Desktop
    pause
    exit /b 1
)

REM 创建网络（如果不存在）
echo 创建Docker网络...
docker network create digital-human-network 2>nul

REM 设置环境变量文件
if not exist .env (
    echo 创建环境变量文件...
    echo # 数字人RAG系统环境变量 > .env
    echo OPENAI_API_KEY=your-openai-api-key-here >> .env
    echo OPENAI_BASE_URL=https://api.openai.com/v1 >> .env
    echo NODE_ENV=production >> .env
    echo LOG_LEVEL=info >> .env
)

REM 清理旧的容器和镜像（可选）
echo 是否要清理旧的容器和镜像？(y/N)
set /p cleanup=
if /i "%cleanup%"=="y" (
    echo 清理旧容器...
    docker-compose -f docker-compose.windows.yml down --remove-orphans
    echo 清理未使用的镜像...
    docker image prune -f
)

REM 构建并启动服务
echo 构建并启动微服务...
docker-compose -f docker-compose.windows.yml up -d --build

REM 检查服务状态
echo 等待服务启动...
timeout /t 30 /nobreak >nul

echo ========================================
echo 检查服务状态
echo ========================================

REM 检查基础设施服务
echo 检查基础设施服务...
docker-compose -f docker-compose.windows.yml ps postgres redis minio elasticsearch chroma

REM 检查核心服务
echo 检查核心微服务...
docker-compose -f docker-compose.windows.yml ps service-registry api-gateway

REM 检查业务服务
echo 检查业务服务...
docker-compose -f docker-compose.windows.yml ps user-service project-service asset-service scene-generation-service

echo ========================================
echo 服务访问地址
echo ========================================
echo API网关:              http://localhost:8080
echo API文档:              http://localhost:8080/api/docs
echo 服务注册中心:          http://localhost:8761
echo Grafana监控:          http://localhost:3001 (admin/admin123)
echo Prometheus:           http://localhost:9090
echo Jaeger链路追踪:       http://localhost:16686
echo MinIO对象存储:        http://localhost:9001 (minioadmin/minioadmin123)
echo Elasticsearch:        http://localhost:9200
echo Chroma向量数据库:      http://localhost:8000
echo ========================================
echo 微服务端口列表:
echo 用户服务:              http://localhost:3001 (TCP) / http://localhost:4001 (HTTP)
echo 项目服务:              http://localhost:3002
echo 资产服务:              http://localhost:3003 (TCP) / http://localhost:4003 (HTTP)
echo 场景生成服务:          http://localhost:3004
echo 资产库服务:            http://localhost:3005
echo 渲染服务:              http://localhost:3006
echo 协作服务:              http://localhost:3007
echo 知识服务:              http://localhost:3008
echo RAG引擎:               http://localhost:3009
echo AI模型服务:            http://localhost:3010
echo 绑定服务:              http://localhost:3011
echo 游戏服务器:            http://localhost:3012
echo 监控服务:              http://localhost:3013
echo 场景模板服务:          http://localhost:8004
echo ========================================

REM 等待用户输入
echo 按任意键查看服务日志...
pause >nul

REM 显示服务日志
echo 显示API网关日志（按Ctrl+C退出）...
docker-compose -f docker-compose.windows.yml logs -f api-gateway
