/**
 * 关键点索引常量
 * 基于MediaPipe Pose模型的关键点索引
 */
export declare const LandmarkIndices: {
    /** 鼻子 */
    NOSE: number;
    /** 左眼内侧 */
    LEFT_EYE_INNER: number;
    /** 左眼 */
    LEFT_EYE: number;
    /** 左眼外侧 */
    LEFT_EYE_OUTER: number;
    /** 右眼内侧 */
    RIGHT_EYE_INNER: number;
    /** 右眼 */
    RIGHT_EYE: number;
    /** 右眼外侧 */
    RIGHT_EYE_OUTER: number;
    /** 左耳 */
    LEFT_EAR: number;
    /** 右耳 */
    RIGHT_EAR: number;
    /** 嘴左侧 */
    MOUTH_LEFT: number;
    /** 嘴右侧 */
    MOUTH_RIGHT: number;
    /** 左肩 */
    LEFT_SHOULDER: number;
    /** 右肩 */
    RIGHT_SHOULDER: number;
    /** 左肘 */
    LEFT_ELBOW: number;
    /** 右肘 */
    RIGHT_ELBOW: number;
    /** 左手腕 */
    LEFT_WRIST: number;
    /** 右手腕 */
    RIGHT_WRIST: number;
    /** 左小指 */
    LEFT_PINKY: number;
    /** 右小指 */
    RIGHT_PINKY: number;
    /** 左食指 */
    LEFT_INDEX: number;
    /** 右食指 */
    RIGHT_INDEX: number;
    /** 左拇指 */
    LEFT_THUMB: number;
    /** 右拇指 */
    RIGHT_THUMB: number;
    /** 左髋 */
    LEFT_HIP: number;
    /** 右髋 */
    RIGHT_HIP: number;
    /** 左膝 */
    LEFT_KNEE: number;
    /** 右膝 */
    RIGHT_KNEE: number;
    /** 左踝 */
    LEFT_ANKLE: number;
    /** 右踝 */
    RIGHT_ANKLE: number;
    /** 左脚跟 */
    LEFT_HEEL: number;
    /** 右脚跟 */
    RIGHT_HEEL: number;
    /** 左脚尖 */
    LEFT_FOOT_INDEX: number;
    /** 右脚尖 */
    RIGHT_FOOT_INDEX: number;
};
/**
 * 关键点连接
 * 定义了关键点之间的连接关系，用于可视化
 */
export declare const LandmarkConnections: {
    start: number;
    end: number;
}[];
/**
 * 关键点分组
 * 将关键点分为不同的组，用于处理不同部位
 */
export declare const LandmarkGroups: {
    /** 面部关键点 */
    FACE: number[];
    /** 上半身关键点 */
    UPPER_BODY: number[];
    /** 躯干关键点 */
    TORSO: number[];
    /** 下半身关键点 */
    LOWER_BODY: number[];
    /** 左手关键点 */
    LEFT_HAND: number[];
    /** 右手关键点 */
    RIGHT_HAND: number[];
};
