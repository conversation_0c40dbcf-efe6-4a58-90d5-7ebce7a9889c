/**
 * 资产模块
 */
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AssetsController } from './assets.controller';
import { AssetsService } from './assets.service';
import { Asset } from './entities/asset.entity';
import { AssetVersion } from './entities/asset-version.entity';
import { AssetTag } from './entities/asset-tag.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Asset, AssetVersion, AssetTag]),
    // 微服务客户端已在 AppModule 中全局注册，无需重复注册
  ],
  controllers: [AssetsController],
  providers: [AssetsService],
  exports: [AssetsService],
})
export class AssetsModule {}
