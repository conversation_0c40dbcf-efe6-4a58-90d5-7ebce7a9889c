/**
 * 分布式事务模块
 */
import { Module, DynamicModule, Provider, Global } from '@nestjs/common';
import { DiscoveryModule } from '@nestjs/core';
import { TransactionCoordinatorService } from './transaction-coordinator.service';
import { TransactionOptions } from './transaction.interface';
import { TRANSACTION_OPTIONS } from './transaction.constants';
import { TransactionExplorerService } from './transaction-explorer.service';
import { TransactionParticipantRegistryService } from './participant/transaction-participant-registry.service';
import { TransactionMonitorService } from './monitoring/transaction-monitor.service';
import { TransactionDashboardController } from './api/transaction-dashboard.controller';
import { TransactionApiService } from './api/transaction-api.service';
import { TransactionApiController } from './api/transaction-api.controller';
import { TransactionRecoveryService } from './recovery/transaction-recovery.service';
import { TransactionLogStorageService } from './storage/transaction-log-storage.service';
import { EventBusModule } from '../event-bus';

@Global()
@Module({
  imports: [DiscoveryModule, EventBusModule],
  providers: [TransactionExplorerService, TransactionParticipantRegistryService],
  exports: [TransactionCoordinatorService, TransactionParticipantRegistryService],
})
export class TransactionModule {
  /**
   * 注册事务模块
   * @param options 事务配置
   */
  static register(options: TransactionOptions): DynamicModule {
    const providers: Provider[] = [
      {
        provide: TRANSACTION_OPTIONS,
        useValue: options,
      },
      TransactionCoordinatorService,
      TransactionExplorerService,
      TransactionParticipantRegistryService,
      TransactionMonitorService,
      TransactionRecoveryService,
      {
        provide: TransactionLogStorageService,
        useFactory: (options: TransactionOptions) => {
          return new TransactionLogStorageService({
            storagePath: options.logStoragePath || './logs',
            filename: 'transaction.log',
            enableChecksum: true,
          });
        },
        inject: [TRANSACTION_OPTIONS],
      },
      TransactionApiService,
    ];

    return {
      module: TransactionModule,
      controllers: [TransactionApiController, TransactionDashboardController],
      providers,
      exports: [
        TransactionCoordinatorService,
        TransactionParticipantRegistryService,
        TransactionMonitorService,
        TransactionRecoveryService,
        TransactionLogStorageService,
        TransactionApiService,
      ],
    };
  }

  /**
   * 注册事务模块（不包含API控制器）
   * @param options 事务配置
   */
  static registerCore(options: TransactionOptions): DynamicModule {
    const providers: Provider[] = [
      {
        provide: TRANSACTION_OPTIONS,
        useValue: options,
      },
      TransactionCoordinatorService,
      TransactionExplorerService,
      TransactionParticipantRegistryService,
      TransactionMonitorService,
      TransactionRecoveryService,
      {
        provide: TransactionLogStorageService,
        useFactory: (options: TransactionOptions) => {
          return new TransactionLogStorageService({
            storagePath: options.logStoragePath || './logs',
            filename: 'transaction.log',
            enableChecksum: true,
          });
        },
        inject: [TRANSACTION_OPTIONS],
      },
    ];

    return {
      module: TransactionModule,
      providers,
      exports: [
        TransactionCoordinatorService,
        TransactionParticipantRegistryService,
        TransactionMonitorService,
        TransactionRecoveryService,
        TransactionLogStorageService,
      ],
    };
  }
}
