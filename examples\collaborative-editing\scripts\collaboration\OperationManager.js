/**
 * 操作管理器
 * 负责管理和应用编辑操作
 */
export class OperationManager {
  /**
   * 构造函数
   * @param {Scene} scene 场景对象
   * @param {UserManager} userManager 用户管理器
   */
  constructor(scene, userManager) {
    this.scene = scene;
    this.userManager = userManager;
    this.operations = []; // 操作历史
    this.pendingOperations = new Map(); // 用户ID -> 待处理操作数组
    this.eventListeners = new Map(); // 事件类型 -> 监听器数组
    
    // 操作类型
    this.OperationType = {
      CREATE: 'create',
      DELETE: 'delete',
      TRANSFORM: 'transform',
      PROPERTY: 'property'
    };
    
    // 变换类型
    this.TransformType = {
      POSITION: 'position',
      ROTATION: 'rotation',
      SCALE: 'scale'
    };
  }
  
  /**
   * 创建操作
   * @param {string} type 操作类型
   * @param {Object} data 操作数据
   * @returns {Object} 操作对象
   */
  createOperation(type, data) {
    const operation = {
      id: this.generateOperationId(),
      type: type,
      data: data,
      timestamp: Date.now(),
      userId: this.userManager.currentUser ? this.userManager.currentUser.id : null
    };
    
    return operation;
  }
  
  /**
   * 应用操作
   * @param {Object} operation 操作对象
   */
  applyOperation(operation) {
    switch (operation.type) {
      case this.OperationType.CREATE:
        this.applyCreateOperation(operation);
        break;
      case this.OperationType.DELETE:
        this.applyDeleteOperation(operation);
        break;
      case this.OperationType.TRANSFORM:
        this.applyTransformOperation(operation);
        break;
      case this.OperationType.PROPERTY:
        this.applyPropertyOperation(operation);
        break;
      default:
        console.warn('未知操作类型:', operation.type);
    }
    
    // 添加到操作历史
    this.operations.push(operation);
    
    // 触发操作应用事件
    this.emit('operationApplied', operation);
  }
  
  /**
   * 应用远程操作
   * @param {string} userId 用户ID
   * @param {Object} operation 操作对象
   */
  applyRemoteOperation(userId, operation) {
    // 检查是否有冲突
    const conflict = this.checkConflict(userId, operation);
    
    if (conflict) {
      // 处理冲突
      this.emit('conflict', {
        userId: userId,
        localOperation: conflict.localOperation,
        remoteOperation: operation
      });
      
      // 将操作添加到待处理队列
      if (!this.pendingOperations.has(userId)) {
        this.pendingOperations.set(userId, []);
      }
      
      this.pendingOperations.get(userId).push(operation);
    } else {
      // 应用操作
      this.applyOperation(operation);
    }
  }
  
  /**
   * 应用创建操作
   * @param {Object} operation 操作对象
   */
  applyCreateOperation(operation) {
    const { entityData } = operation.data;
    
    // 在实际应用中，这里应该根据实体数据创建实体
    console.log('应用创建操作:', entityData);
    
    // 模拟创建实体
    const entity = {
      id: entityData.id,
      name: entityData.name,
      type: entityData.type,
      transform: entityData.transform,
      components: entityData.components
    };
    
    // 触发实体创建事件
    this.emit('entityCreated', entity);
  }
  
  /**
   * 应用删除操作
   * @param {Object} operation 操作对象
   */
  applyDeleteOperation(operation) {
    const { entityId } = operation.data;
    
    // 在实际应用中，这里应该删除实体
    console.log('应用删除操作:', entityId);
    
    // 触发实体删除事件
    this.emit('entityDeleted', entityId);
  }
  
  /**
   * 应用变换操作
   * @param {Object} operation 操作对象
   */
  applyTransformOperation(operation) {
    const { entityId, transformType, value } = operation.data;
    
    // 在实际应用中，这里应该更新实体的变换
    console.log('应用变换操作:', entityId, transformType, value);
    
    // 触发实体变换事件
    this.emit('entityTransformed', {
      entityId,
      transformType,
      value
    });
  }
  
  /**
   * 应用属性操作
   * @param {Object} operation 操作对象
   */
  applyPropertyOperation(operation) {
    const { entityId, propertyPath, value } = operation.data;
    
    // 在实际应用中，这里应该更新实体的属性
    console.log('应用属性操作:', entityId, propertyPath, value);
    
    // 触发实体属性更新事件
    this.emit('entityPropertyUpdated', {
      entityId,
      propertyPath,
      value
    });
  }
  
  /**
   * 检查操作冲突
   * @param {string} userId 用户ID
   * @param {Object} operation 操作对象
   * @returns {Object|null} 冲突对象
   */
  checkConflict(userId, operation) {
    // 获取最近的本地操作
    const recentLocalOperations = this.getRecentLocalOperations();
    
    // 检查是否有冲突
    for (const localOperation of recentLocalOperations) {
      if (this.operationsConflict(localOperation, operation)) {
        return {
          localOperation,
          remoteOperation: operation
        };
      }
    }
    
    return null;
  }
  
  /**
   * 判断两个操作是否冲突
   * @param {Object} op1 操作1
   * @param {Object} op2 操作2
   * @returns {boolean} 是否冲突
   */
  operationsConflict(op1, op2) {
    // 如果操作类型不同，不冲突
    if (op1.type !== op2.type) {
      return false;
    }
    
    // 根据操作类型判断冲突
    switch (op1.type) {
      case this.OperationType.CREATE:
        // 创建操作不会冲突
        return false;
      
      case this.OperationType.DELETE:
        // 如果删除同一个实体，冲突
        return op1.data.entityId === op2.data.entityId;
      
      case this.OperationType.TRANSFORM:
        // 如果变换同一个实体的同一个属性，冲突
        return op1.data.entityId === op2.data.entityId && 
               op1.data.transformType === op2.data.transformType;
      
      case this.OperationType.PROPERTY:
        // 如果修改同一个实体的同一个属性，冲突
        return op1.data.entityId === op2.data.entityId && 
               op1.data.propertyPath === op2.data.propertyPath;
      
      default:
        return false;
    }
  }
  
  /**
   * 获取最近的本地操作
   * @param {number} count 操作数量
   * @returns {Array} 操作数组
   */
  getRecentLocalOperations(count = 10) {
    // 获取当前用户ID
    const currentUserId = this.userManager.currentUser ? this.userManager.currentUser.id : null;
    
    if (!currentUserId) {
      return [];
    }
    
    // 获取最近的本地操作
    return this.operations
      .filter(op => op.userId === currentUserId)
      .slice(-count);
  }
  
  /**
   * 生成操作ID
   * @returns {string} 操作ID
   */
  generateOperationId() {
    return 'op_' + Math.random().toString(36).substr(2, 9);
  }
  
  /**
   * 添加事件监听器
   * @param {string} event 事件类型
   * @param {Function} listener 监听器函数
   */
  on(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    
    this.eventListeners.get(event).push(listener);
  }
  
  /**
   * 移除事件监听器
   * @param {string} event 事件类型
   * @param {Function} listener 监听器函数
   */
  off(event, listener) {
    if (!this.eventListeners.has(event)) {
      return;
    }
    
    const listeners = this.eventListeners.get(event);
    const index = listeners.indexOf(listener);
    
    if (index !== -1) {
      listeners.splice(index, 1);
    }
  }
  
  /**
   * 触发事件
   * @param {string} event 事件类型
   * @param {*} data 事件数据
   */
  emit(event, data) {
    if (!this.eventListeners.has(event)) {
      return;
    }
    
    const listeners = this.eventListeners.get(event);
    
    for (const listener of listeners) {
      listener(data);
    }
  }
  
  /**
   * 创建实体操作
   * @param {Object} entityData 实体数据
   * @returns {Object} 操作对象
   */
  createEntityOperation(entityData) {
    return this.createOperation(this.OperationType.CREATE, {
      entityData
    });
  }
  
  /**
   * 删除实体操作
   * @param {string} entityId 实体ID
   * @returns {Object} 操作对象
   */
  deleteEntityOperation(entityId) {
    return this.createOperation(this.OperationType.DELETE, {
      entityId
    });
  }
  
  /**
   * 变换实体操作
   * @param {string} entityId 实体ID
   * @param {string} transformType 变换类型
   * @param {Object} value 变换值
   * @returns {Object} 操作对象
   */
  transformEntityOperation(entityId, transformType, value) {
    return this.createOperation(this.OperationType.TRANSFORM, {
      entityId,
      transformType,
      value
    });
  }
  
  /**
   * 更新实体属性操作
   * @param {string} entityId 实体ID
   * @param {string} propertyPath 属性路径
   * @param {*} value 属性值
   * @returns {Object} 操作对象
   */
  updateEntityPropertyOperation(entityId, propertyPath, value) {
    return this.createOperation(this.OperationType.PROPERTY, {
      entityId,
      propertyPath,
      value
    });
  }
}
