import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { WebRTCService } from './webrtc.service';
import { WebRTCController } from './webrtc.controller';
import { DataChannelService } from './data-channel.service';

@Module({
  imports: [
    ConfigModule,
  ],
  controllers: [WebRTCController],
  providers: [WebRTCService, DataChannelService],
  exports: [WebRTCService, DataChannelService],
})
export class WebRTCModule {}
