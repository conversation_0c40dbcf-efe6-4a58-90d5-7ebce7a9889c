/**
 * 水体预设测试类
 */
export declare class WaterPresetsTest {
    /**
     * 运行所有测试
     */
    static runAllTests(): void;
    /**
     * 测试获取所有预设类型
     */
    private static testGetAllPresetTypes;
    /**
     * 测试获取预设显示名称
     */
    private static testGetPresetDisplayName;
    /**
     * 测试获取预设配置
     */
    private static testGetPresetConfig;
    /**
     * 测试创建预设
     */
    private static testCreatePreset;
    /**
     * 测试预设配置的完整性
     */
    static testPresetCompleteness(): void;
}
