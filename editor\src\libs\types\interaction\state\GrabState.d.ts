import type { Entity } from '../../core/Entity';
import { Hand } from '../components/GrabbableComponent';
/**
 * 抓取事件类型
 */
export declare enum GrabEventType {
    /** 抓取开始 */
    GRAB_START = "grabStart",
    /** 抓取结束 */
    GRAB_END = "grabEnd",
    /** 抓取更新 */
    GRAB_UPDATE = "grabUpdate",
    /** 抓取状态变化 */
    STATE_CHANGE = "stateChange"
}
/**
 * 抓取事件数据
 */
export interface GrabEventData {
    /** 抓取者 */
    grabber: Entity;
    /** 被抓取实体 */
    grabbed: Entity;
    /** 抓取手 */
    hand: Hand;
    /** 时间戳 */
    timestamp: number;
    /** 其他数据 */
    [key: string]: any;
}
/**
 * 抓取状态
 */
export declare class GrabState {
    /** 单例实例 */
    private static instance;
    /** 事件发射器 */
    private eventEmitter;
    /** 当前被抓取的实体映射 - 实体ID到抓取数据 */
    private grabbedEntities;
    /** 当前抓取者映射 - 实体ID到被抓取实体 */
    private grabbers;
    /**
     * 私有构造函数
     */
    private constructor();
    /**
     * 获取单例实例
     */
    static getInstance(): GrabState;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器函数
     */
    addEventListener(event: GrabEventType, listener: (data: GrabEventData) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器函数
     */
    removeEventListener(event: GrabEventType, listener?: (data: GrabEventData) => void): void;
    /**
     * 触发事件
     * @param event 事件类型
     * @param data 事件数据
     */
    private dispatchEvent;
    /**
     * 注册抓取
     * @param grabbed 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    registerGrab(grabbed: Entity, grabber: Entity, hand: Hand): void;
    /**
     * 注销抓取
     * @param grabbed 被抓取实体
     */
    unregisterGrab(grabbed: Entity): void;
    /**
     * 更新抓取
     * @param grabbed 被抓取实体
     * @param data 更新数据
     */
    updateGrab(grabbed: Entity, data?: any): void;
    /**
     * 检查实体是否被抓取
     * @param entity 实体
     * @returns 是否被抓取
     */
    isGrabbed(entity: Entity): boolean;
    /**
     * 获取抓取者
     * @param grabbed 被抓取实体
     * @returns 抓取者
     */
    getGrabber(grabbed: Entity): Entity | undefined;
    /**
     * 获取抓取手
     * @param grabbed 被抓取实体
     * @returns 抓取手
     */
    getGrabHand(grabbed: Entity): Hand | undefined;
    /**
     * 获取被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     * @returns 被抓取实体
     */
    getGrabbedEntity(grabber: Entity, hand: Hand): Entity | undefined;
    /**
     * 获取所有被抓取的实体
     * @returns 被抓取的实体数组
     */
    getAllGrabbedEntities(): Entity[];
    /**
     * 获取所有抓取者
     * @returns 抓取者数组
     */
    getAllGrabbers(): Entity[];
    /**
     * 清除所有抓取状态
     */
    clear(): void;
}
