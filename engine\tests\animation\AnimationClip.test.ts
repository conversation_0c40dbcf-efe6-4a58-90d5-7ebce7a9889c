/**
 * AnimationClip单元测试
 */
import { describe, it, expect, beforeEach } from 'vitest';
import { AnimationClip, LoopMode } from '../../src/animation/AnimationClip';
import * as THREE from 'three';

describe('AnimationClip', () => {
  let clip: AnimationClip;

  beforeEach(() => {
    // 创建测试用的动画片段
    clip = new AnimationClip('test-clip', 2.0);
  });

  it('应该正确创建动画片段', () => {
    expect(clip).toBeDefined();
    expect(clip.name).toBe('test-clip');
    expect(clip.duration).toBe(2.0);
    expect(clip.loopMode).toBe(LoopMode.REPEAT); // 默认循环模式
  });

  it('应该能够添加和获取轨道', () => {
    // 添加位置轨道
    clip.addTrack('node1.position', 'vector3', [
      { time: 0, value: [0, 0, 0] },
      { time: 1, value: [1, 2, 3] },
      { time: 2, value: [2, 4, 6] }
    ]);

    // 添加旋转轨道
    clip.addTrack('node1.quaternion', 'quaternion', [
      { time: 0, value: [0, 0, 0, 1] },
      { time: 1, value: [0.1, 0.2, 0.3, 0.9] },
      { time: 2, value: [0.2, 0.4, 0.6, 0.7] }
    ]);

    // 获取轨道
    const tracks = clip.getTracks();
    expect(tracks.length).toBe(2);
    expect(tracks[0].targetPath).toBe('node1.position');
    expect(tracks[1].targetPath).toBe('node1.quaternion');
  });

  it('应该能够在指定时间点获取动画状态', () => {
    // 添加位置轨道
    clip.addTrack('node1.position', 'vector3', [
      { time: 0, value: [0, 0, 0] },
      { time: 1, value: [1, 2, 3] },
      { time: 2, value: [2, 4, 6] }
    ]);

    // 获取t=0.5时的状态
    const state = new Map<string, any>();
    clip.getStateAtTime(0.5, state);

    // 验证插值结果
    expect(state.has('node1.position')).toBe(true);
    const position = state.get('node1.position');
    expect(position).toBeInstanceOf(THREE.Vector3);
    expect(position.x).toBeCloseTo(0.5);
    expect(position.y).toBeCloseTo(1.0);
    expect(position.z).toBeCloseTo(1.5);
  });

  it('应该能够处理不同的循环模式', () => {
    // 添加位置轨道
    clip.addTrack('node1.position', 'vector3', [
      { time: 0, value: [0, 0, 0] },
      { time: 1, value: [1, 2, 3] },
      { time: 2, value: [2, 4, 6] }
    ]);

    // 测试REPEAT模式
    clip.loopMode = LoopMode.REPEAT;
    let state = new Map<string, any>();
    clip.getStateAtTime(2.5, state);
    let position = state.get('node1.position');
    expect(position.x).toBeCloseTo(0.5);
    expect(position.y).toBeCloseTo(1.0);
    expect(position.z).toBeCloseTo(1.5);

    // 测试PING_PONG模式
    clip.loopMode = LoopMode.PING_PONG;
    state = new Map<string, any>();
    clip.getStateAtTime(2.5, state);
    position = state.get('node1.position');
    expect(position.x).toBeCloseTo(1.5);
    expect(position.y).toBeCloseTo(3.0);
    expect(position.z).toBeCloseTo(4.5);

    // 测试NONE模式
    clip.loopMode = LoopMode.NONE;
    state = new Map<string, any>();
    clip.getStateAtTime(2.5, state);
    position = state.get('node1.position');
    expect(position.x).toBeCloseTo(2.0);
    expect(position.y).toBeCloseTo(4.0);
    expect(position.z).toBeCloseTo(6.0);
  });

  it('应该能够转换为THREE.AnimationClip', () => {
    // 添加位置轨道
    clip.addTrack('node1.position', 'vector3', [
      { time: 0, value: [0, 0, 0] },
      { time: 1, value: [1, 2, 3] },
      { time: 2, value: [2, 4, 6] }
    ]);

    // 添加旋转轨道
    clip.addTrack('node1.quaternion', 'quaternion', [
      { time: 0, value: [0, 0, 0, 1] },
      { time: 1, value: [0.1, 0.2, 0.3, 0.9] },
      { time: 2, value: [0.2, 0.4, 0.6, 0.7] }
    ]);

    // 转换为THREE.AnimationClip
    const threeClip = clip.toThreeAnimationClip();
    
    expect(threeClip).toBeInstanceOf(THREE.AnimationClip);
    expect(threeClip.name).toBe('test-clip');
    expect(threeClip.duration).toBe(2.0);
    expect(threeClip.tracks.length).toBe(2);
    expect(threeClip.tracks[0]).toBeInstanceOf(THREE.VectorKeyframeTrack);
    expect(threeClip.tracks[1]).toBeInstanceOf(THREE.QuaternionKeyframeTrack);
  });

  it('应该能够克隆动画片段', () => {
    // 添加位置轨道
    clip.addTrack('node1.position', 'vector3', [
      { time: 0, value: [0, 0, 0] },
      { time: 1, value: [1, 2, 3] },
      { time: 2, value: [2, 4, 6] }
    ]);

    // 克隆动画片段
    const clonedClip = clip.clone();
    
    expect(clonedClip).not.toBe(clip);
    expect(clonedClip.name).toBe(clip.name);
    expect(clonedClip.duration).toBe(clip.duration);
    expect(clonedClip.loopMode).toBe(clip.loopMode);
    
    const tracks = clonedClip.getTracks();
    expect(tracks.length).toBe(1);
    expect(tracks[0].targetPath).toBe('node1.position');
  });
});
