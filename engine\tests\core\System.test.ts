/**
 * System类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { System } from '../../src/core/System';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Component } from '../../src/core/Component';

// 创建一个测试组件类
class TestComponent extends Component {
  public value: number = 0;
  
  constructor(entity: Entity, value: number = 0) {
    super(entity);
    this.value = value;
  }
  
  public static readonly TYPE: string = 'TestComponent';
  
  public getType(): string {
    return TestComponent.TYPE;
  }
  
  public setValue(value: number): void {
    this.value = value;
  }
  
  public getValue(): number {
    return this.value;
  }
}

// 创建一个测试系统类
class TestSystem extends System {
  public initialized: boolean = false;
  public updated: boolean = false;
  public fixedUpdated: boolean = false;
  public lateUpdated: boolean = false;
  public disposed: boolean = false;
  public processedEntities: Entity[] = [];
  
  constructor(priority: number = 0) {
    super(priority);
  }
  
  public initialize(): void {
    this.initialized = true;
  }
  
  public update(deltaTime: number): void {
    this.updated = true;
    this.processEntities(deltaTime);
  }
  
  public fixedUpdate(fixedDeltaTime: number): void {
    this.fixedUpdated = true;
  }
  
  public lateUpdate(deltaTime: number): void {
    this.lateUpdated = true;
  }
  
  public dispose(): void {
    this.disposed = true;
  }
  
  private processEntities(deltaTime: number): void {
    if (!this.world) return;
    
    // 查找具有TestComponent的实体
    const entities = this.world.findEntitiesByComponent(TestComponent);
    
    // 处理实体
    for (const entity of entities) {
      if (entity.isActive()) {
        const component = entity.getComponent<TestComponent>(TestComponent.TYPE);
        if (component && component.isEnabled()) {
          // 增加组件值
          component.setValue(component.getValue() + 1);
          
          // 记录已处理的实体
          this.processedEntities.push(entity);
        }
      }
    }
  }
}

describe('System', () => {
  let engine: Engine;
  let world: World;
  let system: TestSystem;
  
  // 在每个测试前创建一个新的系统实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建系统
    system = new TestSystem(1);
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试系统初始化
  it('应该正确初始化系统', () => {
    expect(system).toBeDefined();
    expect(system.getPriority()).toBe(1);
    expect(system.isEnabled()).toBe(true);
    expect(system.initialized).toBe(false); // 尚未调用initialize方法
    
    // 设置系统的世界
    system.setWorld(world);
    
    // 验证世界已设置
    expect(system.getWorld()).toBe(world);
    
    // 初始化系统
    system.initialize();
    
    // 验证系统已初始化
    expect(system.initialized).toBe(true);
  });
  
  // 测试系统启用/禁用
  it('应该能够启用和禁用系统', () => {
    // 验证系统已启用
    expect(system.isEnabled()).toBe(true);
    
    // 禁用系统
    system.setEnabled(false);
    
    // 验证系统已禁用
    expect(system.isEnabled()).toBe(false);
    
    // 启用系统
    system.setEnabled(true);
    
    // 验证系统已启用
    expect(system.isEnabled()).toBe(true);
  });
  
  // 测试系统更新
  it('应该能够更新系统', () => {
    // 设置系统的世界
    system.setWorld(world);
    
    // 更新系统
    const deltaTime = 0.016;
    system.update(deltaTime);
    
    // 验证系统已更新
    expect(system.updated).toBe(true);
  });
  
  // 测试系统固定更新
  it('应该能够固定更新系统', () => {
    // 设置系统的世界
    system.setWorld(world);
    
    // 固定更新系统
    const fixedDeltaTime = 0.02;
    system.fixedUpdate(fixedDeltaTime);
    
    // 验证系统已固定更新
    expect(system.fixedUpdated).toBe(true);
  });
  
  // 测试系统后更新
  it('应该能够后更新系统', () => {
    // 设置系统的世界
    system.setWorld(world);
    
    // 后更新系统
    const deltaTime = 0.016;
    system.lateUpdate(deltaTime);
    
    // 验证系统已后更新
    expect(system.lateUpdated).toBe(true);
  });
  
  // 测试系统处理实体
  it('应该能够处理实体', () => {
    // 设置系统的世界
    system.setWorld(world);
    
    // 创建实体
    const entity1 = new Entity(world);
    const entity2 = new Entity(world);
    const entity3 = new Entity(world);
    
    // 添加组件到实体
    entity1.addComponent(new TestComponent(entity1, 10));
    entity2.addComponent(new TestComponent(entity2, 20));
    // entity3没有TestComponent
    
    // 禁用entity2
    entity2.setActive(false);
    
    // 添加实体到世界
    world.addEntity(entity1);
    world.addEntity(entity2);
    world.addEntity(entity3);
    
    // 更新系统
    system.update(0.016);
    
    // 验证系统已处理实体
    expect(system.processedEntities.length).toBe(1);
    expect(system.processedEntities[0]).toBe(entity1);
    
    // 验证组件值已更新
    const component1 = entity1.getComponent<TestComponent>(TestComponent.TYPE);
    expect(component1?.getValue()).toBe(11);
    
    // 验证禁用实体的组件值未更新
    const component2 = entity2.getComponent<TestComponent>(TestComponent.TYPE);
    expect(component2?.getValue()).toBe(20);
  });
  
  // 测试系统优先级
  it('应该能够设置和获取系统优先级', () => {
    // 验证初始优先级
    expect(system.getPriority()).toBe(1);
    
    // 设置新优先级
    system.setPriority(5);
    
    // 验证优先级已更改
    expect(system.getPriority()).toBe(5);
  });
  
  // 测试系统引擎
  it('应该能够设置和获取系统引擎', () => {
    // 设置系统的引擎
    system.setEngine(engine);
    
    // 验证引擎已设置
    expect(system.getEngine()).toBe(engine);
  });
  
  // 测试系统事件
  it('应该能够发射和监听系统事件', () => {
    // 创建事件监听器
    const listener = vi.fn();
    
    // 添加事件监听器
    system.on('test', listener);
    
    // 发射事件
    system.emit('test', { data: 'test' });
    
    // 验证事件监听器被调用
    expect(listener).toHaveBeenCalledWith({ data: 'test' });
    
    // 移除事件监听器
    system.off('test', listener);
    
    // 再次发射事件
    system.emit('test', { data: 'test2' });
    
    // 验证事件监听器未被再次调用
    expect(listener).toHaveBeenCalledTimes(1);
  });
  
  // 测试系统销毁
  it('应该能够正确销毁系统', () => {
    // 设置系统的世界
    system.setWorld(world);
    
    // 创建事件监听器
    const listener = vi.fn();
    
    // 添加事件监听器
    system.on('disposed', listener);
    
    // 销毁系统
    system.dispose();
    
    // 验证系统已销毁
    expect(system.disposed).toBe(true);
    
    // 验证事件监听器被调用
    expect(listener).toHaveBeenCalled();
    
    // 验证世界引用已清除
    expect(system.getWorld()).toBeNull();
  });
});
