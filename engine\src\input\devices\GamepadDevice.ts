/**
 * 游戏手柄输入设备
 */
import { BaseInputDevice } from '../InputDevice';

/**
 * 游戏手柄输入设备
 */
export class GamepadDevice extends BaseInputDevice {
  /** 游戏手柄映射 */
  private gamepads: Map<number, Gamepad> = new Map();

  /** 游戏手柄事件处理器 */
  private gamepadEventHandlers: { [key: string]: (event: GamepadEvent) => void } = {};

  /**
   * 创建游戏手柄输入设备
   */
  constructor() {
    super('gamepad');

    // 初始化事件处理器
    this.initEventHandlers();
  }

  /**
   * 初始化事件处理器
   */
  private initEventHandlers(): void {
    // 游戏手柄连接事件
    this.gamepadEventHandlers.gamepadconnected = this.handleGamepadConnected.bind(this);

    // 游戏手柄断开事件
    this.gamepadEventHandlers.gamepaddisconnected = this.handleGamepadDisconnected.bind(this);
  }

  /**
   * 初始化设备
   */
  public initialize(): void {
    if (this.initialized) return;

    // 添加事件监听器
    this.addEventListeners();

    // 初始化已连接的游戏手柄
    this.initGamepads();

    super.initialize();
  }

  /**
   * 销毁设备
   */
  public destroy(): void {
    if (this.destroyed) return;

    // 移除事件监听器
    this.removeEventListeners();

    // 清空游戏手柄映射
    this.gamepads.clear();

    super.destroy();
  }

  /**
   * 更新设备状态
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || this.destroyed) return;

    // 更新游戏手柄状态
    this.updateGamepads();

    super.update(deltaTime);
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 添加游戏手柄事件监听器
    for (const [event, handler] of Object.entries(this.gamepadEventHandlers)) {
      window.addEventListener(event, handler as EventListener);
    }
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 移除游戏手柄事件监听器
    for (const [event, handler] of Object.entries(this.gamepadEventHandlers)) {
      window.removeEventListener(event, handler as EventListener);
    }
  }

  /**
   * 初始化已连接的游戏手柄
   */
  private initGamepads(): void {
    // 获取所有游戏手柄
    const gamepads = navigator.getGamepads ? navigator.getGamepads() : [];

    // 初始化游戏手柄
    for (const gamepad of gamepads) {
      if (!gamepad) continue;
      this.addGamepad(gamepad);
    }
  }

  /**
   * 更新游戏手柄状态
   */
  private updateGamepads(): void {
    // 获取所有游戏手柄
    const gamepads = navigator.getGamepads ? navigator.getGamepads() : [];

    // 更新游戏手柄状态
    for (const gamepad of gamepads) {
      if (!gamepad) continue;

      // 更新游戏手柄映射
      this.gamepads.set(gamepad.index, gamepad);

      // 更新按钮状态
      for (let i = 0; i < gamepad.buttons.length; i++) {
        const button = gamepad.buttons[i];
        const buttonKey = `${gamepad.index}:button:${i}`;
        const pressed = button.pressed || button.value > 0.5;
        const wasPressed = this.getValue(buttonKey) || false;

        // 更新按钮状态
        this.setValue(buttonKey, pressed);
        this.setValue(`${gamepad.index}:button:${i}:value`, button.value);

        // 触发按钮事件
        if (pressed && !wasPressed) {
          this.eventEmitter.emit(`${buttonKey}:down`, {
            gamepad: gamepad.index,
            button: i,
            value: button.value
          });
        } else if (!pressed && wasPressed) {
          this.eventEmitter.emit(`${buttonKey}:up`, {
            gamepad: gamepad.index,
            button: i,
            value: button.value
          });
        }
      }

      // 更新轴状态
      for (let i = 0; i < gamepad.axes.length; i++) {
        const axisKey = `${gamepad.index}:axis:${i}`;
        const value = gamepad.axes[i];
        const oldValue = this.getValue(axisKey) || 0;

        // 更新轴状态
        this.setValue(axisKey, value);

        // 触发轴事件
        if (value !== oldValue) {
          this.eventEmitter.emit(`${axisKey}:change`, {
            gamepad: gamepad.index,
            axis: i,
            value: value
          });
        }
      }
    }
  }

  /**
   * 添加游戏手柄
   * @param gamepad 游戏手柄
   */
  private addGamepad(gamepad: Gamepad): void {
    // 添加游戏手柄到映射
    this.gamepads.set(gamepad.index, gamepad);

    // 初始化按钮状态
    for (let i = 0; i < gamepad.buttons.length; i++) {
      const button = gamepad.buttons[i];
      this.setValue(`${gamepad.index}:button:${i}`, button.pressed);
      this.setValue(`${gamepad.index}:button:${i}:value`, button.value);
    }

    // 初始化轴状态
    for (let i = 0; i < gamepad.axes.length; i++) {
      this.setValue(`${gamepad.index}:axis:${i}`, gamepad.axes[i]);
    }

    // 触发游戏手柄连接事件
    this.eventEmitter.emit('connected', {
      gamepad: gamepad.index,
      id: gamepad.id,
      mapping: gamepad.mapping
    });
  }

  /**
   * 移除游戏手柄
   * @param gamepad 游戏手柄
   */
  private removeGamepad(gamepad: Gamepad): void {
    // 从映射中移除游戏手柄
    this.gamepads.delete(gamepad.index);

    // 触发游戏手柄断开事件
    this.eventEmitter.emit('disconnected', {
      gamepad: gamepad.index,
      id: gamepad.id,
      mapping: gamepad.mapping
    });
  }

  /**
   * 处理游戏手柄连接事件
   * @param event 游戏手柄事件
   */
  private handleGamepadConnected(event: GamepadEvent): void {
    this.addGamepad(event.gamepad);
  }

  /**
   * 处理游戏手柄断开事件
   * @param event 游戏手柄事件
   */
  private handleGamepadDisconnected(event: GamepadEvent): void {
    this.removeGamepad(event.gamepad);
  }

  /**
   * 获取游戏手柄
   * @param index 游戏手柄索引
   * @returns 游戏手柄
   */
  public getGamepad(index: number): Gamepad | undefined {
    return this.gamepads.get(index);
  }

  /**
   * 获取所有游戏手柄
   * @returns 游戏手柄列表
   */
  public getGamepads(): Gamepad[] {
    return Array.from(this.gamepads.values());
  }

  /**
   * 检查游戏手柄按钮是否按下
   * @param gamepadIndex 游戏手柄索引
   * @param buttonIndex 按钮索引
   * @returns 是否按下
   */
  public isButtonPressed(gamepadIndex: number, buttonIndex: number): boolean {
    return !!this.getValue(`${gamepadIndex}:button:${buttonIndex}`);
  }

  /**
   * 获取游戏手柄按钮值
   * @param gamepadIndex 游戏手柄索引
   * @param buttonIndex 按钮索引
   * @returns 按钮值
   */
  public getButtonValue(gamepadIndex: number, buttonIndex: number): number {
    return this.getValue(`${gamepadIndex}:button:${buttonIndex}:value`) || 0;
  }

  /**
   * 获取游戏手柄轴值
   * @param gamepadIndex 游戏手柄索引
   * @param axisIndex 轴索引
   * @returns 轴值
   */
  public getAxisValue(gamepadIndex: number, axisIndex: number): number {
    return this.getValue(`${gamepadIndex}:axis:${axisIndex}`) || 0;
  }
}
