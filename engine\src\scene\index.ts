/**
 * 场景管理模块
 * 导出所有场景管理相关的类和接口
 */

// 导出场景类
export { Scene } from './Scene';

// 导出天空盒类
export { Skybox, SkyboxType } from './Skybox';

// 导出场景管理器
export { SceneManager } from './SceneManager';
export type {
  SceneTransitionType,
  SceneTransitionOptions,
  SceneLoadOptions,
  SceneManagerOptions
} from './SceneManager';

// 导出场景序列化器
export { SceneSerializer } from './SceneSerializer';
export type {
  SceneSerializeOptions,
  SceneDeserializeOptions,
  SceneSerializedData
} from './SceneSerializer';

// 导出场景图
export { SceneGraph } from './SceneGraph';
export type {
  SceneGraphNode,
  SceneGraphQueryOptions
} from './SceneGraph';

// 导出场景预加载器
export { ScenePreloader } from './ScenePreloader';
export type {
  SceneResourceInfo,
  SceneResourceType,
  ScenePreloaderOptions,
  ScenePreloadProgressInfo
} from './ScenePreloader';

// 导出场景图层
export { SceneLayer } from './SceneLayer';
export type {
  SceneLayerOptions
} from './SceneLayer';

// 导出场景图层管理器
export { SceneLayerManager } from './SceneLayerManager';
export type {
  SceneLayerManagerOptions,
  SceneLayerQueryOptions
} from './SceneLayerManager';
