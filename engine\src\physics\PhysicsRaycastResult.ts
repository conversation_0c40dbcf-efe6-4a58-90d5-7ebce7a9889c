/**
 * 物理射线检测结果
 * 用于存储射线检测的结果
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../core/Entity';

/**
 * 物理射线检测结果
 */
export class PhysicsRaycastResult {
  /** 是否命中 */
  private hit: boolean = false;

  /** 命中点（世界坐标） */
  private hitPoint: THREE.Vector3 = new THREE.Vector3();

  /** 命中法线（世界坐标） */
  private hitNormal: THREE.Vector3 = new THREE.Vector3();

  /** 命中距离 */
  private hitDistance: number = 0;

  /** 命中的物理体 */
  private hitBody: CANNON.Body | null = null;

  /** 命中的实体 */
  private hitEntity: Entity | null = null;

  /** 命中的形状 */
  private hitShape: CANNON.Shape | null = null;

  /** 原始CANNON射线检测结果 */
  private cannonResult: CANNON.RaycastResult | null = null;

  /**
   * 创建物理射线检测结果
   * @param cannonResult CANNON射线检测结果
   * @param entityMap 物理体到实体的映射
   */
  constructor(cannonResult?: CANNON.RaycastResult, entityMap?: Map<CANNON.Body, Entity>) {
    if (cannonResult) {
      this.updateFromCannonResult(cannonResult, entityMap);
    }
  }

  /**
   * 是否命中
   * @returns 是否命中
   */
  public hasHit(): boolean {
    return this.hit;
  }

  /**
   * 获取命中点（世界坐标）
   * @returns 命中点
   */
  public getHitPoint(): THREE.Vector3 {
    return this.hitPoint.clone();
  }

  /**
   * 获取命中法线（世界坐标）
   * @returns 命中法线
   */
  public getHitNormal(): THREE.Vector3 {
    return this.hitNormal.clone();
  }

  /**
   * 获取命中距离
   * @returns 命中距离
   */
  public getHitDistance(): number {
    return this.hitDistance;
  }

  /**
   * 获取命中的物理体
   * @returns 命中的物理体
   */
  public getHitBody(): CANNON.Body | null {
    return this.hitBody;
  }

  /**
   * 获取命中的实体
   * @returns 命中的实体
   */
  public getHitEntity(): Entity | null {
    return this.hitEntity;
  }

  /**
   * 获取命中的形状
   * @returns 命中的形状
   */
  public getHitShape(): CANNON.Shape | null {
    return this.hitShape;
  }

  /**
   * 获取原始CANNON射线检测结果
   * @returns 原始CANNON射线检测结果
   */
  public getCannonResult(): CANNON.RaycastResult | null {
    return this.cannonResult;
  }

  /**
   * 重置结果
   */
  public reset(): void {
    this.hit = false;
    this.hitPoint.set(0, 0, 0);
    this.hitNormal.set(0, 0, 0);
    this.hitDistance = 0;
    this.hitBody = null;
    this.hitEntity = null;
    this.hitShape = null;
    this.cannonResult = null;
  }

  /**
   * 从CANNON射线检测结果更新
   * @param cannonResult CANNON射线检测结果
   * @param entityMap 物理体到实体的映射
   */
  public updateFromCannonResult(cannonResult: CANNON.RaycastResult, entityMap?: Map<CANNON.Body, Entity>): void {
    this.cannonResult = cannonResult;
    this.hit = cannonResult.hasHit;

    if (this.hit) {
      this.hitPoint.set(
        cannonResult.hitPointWorld.x,
        cannonResult.hitPointWorld.y,
        cannonResult.hitPointWorld.z
      );

      this.hitNormal.set(
        cannonResult.hitNormalWorld.x,
        cannonResult.hitNormalWorld.y,
        cannonResult.hitNormalWorld.z
      );

      this.hitDistance = cannonResult.distance;
      this.hitBody = cannonResult.body;
      this.hitShape = cannonResult.shape;

      // 查找命中的实体
      if (entityMap && this.hitBody) {
        this.hitEntity = entityMap.get(this.hitBody) || null;
      } else {
        this.hitEntity = this.getEntityFromBody(this.hitBody);
      }
    } else {
      this.hitPoint.set(0, 0, 0);
      this.hitNormal.set(0, 0, 0);
      this.hitDistance = 0;
      this.hitBody = null;
      this.hitEntity = null;
      this.hitShape = null;
    }
  }

  /**
   * 从物理体获取实体
   * @param body 物理体
   * @returns 实体
   */
  private getEntityFromBody(body: CANNON.Body | null): Entity | null {
    if (!body) return null;

    // 使用类型断言访问自定义属性
    const userData = (body as any).userData;
    if (!userData) return null;

    return userData.entity || null;
  }
}
