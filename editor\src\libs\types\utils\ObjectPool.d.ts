/**
 * 对象池
 * 用于减少对象创建和销毁的开销，提高性能
 */
/**
 * 对象池接口
 * @template T 对象类型
 */
export interface IObjectPool<T> {
    /**
     * 获取对象
     * @returns 对象实例
     */
    get(): T;
    /**
     * 释放对象
     * @param obj 要释放的对象
     */
    release(obj: T): void;
    /**
     * 清空对象池
     */
    clear(): void;
    /**
     * 获取对象池大小
     * @returns 对象池大小
     */
    size(): number;
    /**
     * 获取活跃对象数量
     * @returns 活跃对象数量
     */
    activeCount(): number;
}
/**
 * 对象池选项
 * @template T 对象类型
 */
export interface ObjectPoolOptions<T> {
    /**
     * 对象创建函数
     */
    create: () => T;
    /**
     * 对象重置函数
     */
    reset?: (obj: T) => void;
    /**
     * 对象销毁函数
     */
    dispose?: (obj: T) => void;
    /**
     * 初始容量
     */
    initialCapacity?: number;
    /**
     * 最大容量
     */
    maxCapacity?: number;
    /**
     * 自动扩容增量
     */
    expandBy?: number;
}
/**
 * 对象池
 * 用于减少对象创建和销毁的开销，提高性能
 * @template T 对象类型
 */
export declare class ObjectPool<T> implements IObjectPool<T> {
    /** 对象创建函数 */
    private create;
    /** 对象重置函数 */
    private reset;
    /** 对象销毁函数 */
    private dispose;
    /** 最大容量 */
    private maxCapacity;
    /** 自动扩容增量 */
    private expandBy;
    /** 对象池 */
    private pool;
    /** 活跃对象集合 */
    private activeObjects;
    /**
     * 创建对象池
     * @param options 对象池选项
     */
    constructor(options: ObjectPoolOptions<T>);
    /**
     * 获取对象
     * @returns 对象实例
     */
    get(): T;
    /**
     * 释放对象
     * @param obj 要释放的对象
     */
    release(obj: T): void;
    /**
     * 清空对象池
     */
    clear(): void;
    /**
     * 获取对象池大小
     * @returns 对象池大小
     */
    size(): number;
    /**
     * 获取活跃对象数量
     * @returns 活跃对象数量
     */
    activeCount(): number;
    /**
     * 扩容对象池
     * @param count 扩容数量
     */
    private expand;
}
