import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Sse,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Response } from 'express';
import { Observable } from 'rxjs';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RAGService } from './rag.service';

@ApiTags('RAG查询引擎')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('rag')
export class RAGController {
  constructor(private readonly ragService: RAGService) {}

  @Post('query')
  @ApiOperation({ summary: '执行RAG查询' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        question: {
          type: 'string',
          description: '用户问题',
          example: '什么是人工智能？',
        },
        digitalHumanId: {
          type: 'string',
          description: '数字人ID',
        },
        sessionId: {
          type: 'string',
          description: '会话ID（可选）',
        },
        context: {
          type: 'string',
          description: '额外上下文（可选）',
        },
        maxResults: {
          type: 'number',
          description: '最大结果数量',
          default: 10,
        },
        temperature: {
          type: 'number',
          description: 'LLM温度参数',
          default: 0.7,
        },
        language: {
          type: 'string',
          description: '回答语言',
          default: 'zh-CN',
        },
      },
      required: ['question', 'digitalHumanId'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'RAG查询结果',
    schema: {
      type: 'object',
      properties: {
        answer: { type: 'string', description: '生成的回答' },
        sources: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              content: { type: 'string' },
              score: { type: 'number' },
              source: {
                type: 'object',
                properties: {
                  documentId: { type: 'string' },
                  filename: { type: 'string' },
                  knowledgeBaseId: { type: 'string' },
                  knowledgeBaseName: { type: 'string' },
                  chunkIndex: { type: 'number' },
                },
              },
            },
          },
        },
        confidence: { type: 'number', description: '置信度' },
        responseTime: { type: 'number', description: '响应时间（毫秒）' },
        sessionId: { type: 'string', description: '会话ID' },
        metadata: {
          type: 'object',
          properties: {
            knowledgeBasesUsed: {
              type: 'array',
              items: { type: 'string' },
            },
            totalChunks: { type: 'number' },
            llmModel: { type: 'string' },
            processingSteps: {
              type: 'array',
              items: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async query(@Body() body: any): Promise<any> {
    try {
      return await this.ragService.query(body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || 'RAG查询失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('query/stream')
  @ApiOperation({ summary: '流式RAG查询' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        question: { type: 'string' },
        digitalHumanId: { type: 'string' },
        sessionId: { type: 'string' },
        context: { type: 'string' },
        maxResults: { type: 'number' },
        temperature: { type: 'number' },
        language: { type: 'string' },
      },
      required: ['question', 'digitalHumanId'],
    },
  })
  async streamQuery(@Body() body: any, @Res() res: Response): Promise<void> {
    try {
      const stream = await this.ragService.queryStream(body);
      
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');

      stream.pipe(res);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '流式RAG查询失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('search')
  @ApiOperation({ summary: '向量搜索（不生成回答）' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        query: { type: 'string' },
        digitalHumanId: { type: 'string' },
        maxResults: { type: 'number' },
        threshold: { type: 'number' },
      },
      required: ['query', 'digitalHumanId'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '搜索结果',
    schema: {
      type: 'object',
      properties: {
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              content: { type: 'string' },
              score: { type: 'number' },
              source: {
                type: 'object',
                properties: {
                  documentId: { type: 'string' },
                  filename: { type: 'string' },
                  knowledgeBaseId: { type: 'string' },
                  knowledgeBaseName: { type: 'string' },
                  chunkIndex: { type: 'number' },
                },
              },
            },
          },
        },
        searchTime: { type: 'number' },
        totalResults: { type: 'number' },
      },
    },
  })
  async search(@Body() body: any): Promise<any> {
    try {
      return await this.ragService.search(body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '搜索失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('digital-humans/:id/knowledge-bases')
  @ApiOperation({ summary: '获取数字人绑定的知识库' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiResponse({
    status: 200,
    description: '知识库列表',
  })
  async getDigitalHumanKnowledgeBases(
    @Param('id') digitalHumanId: string,
  ): Promise<any> {
    try {
      return await this.ragService.getDigitalHumanKnowledgeBases(digitalHumanId);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取知识库列表失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('sessions/:sessionId')
  @ApiOperation({ summary: '获取会话历史' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({
    status: 200,
    description: '会话历史',
  })
  async getSessionHistory(@Param('sessionId') sessionId: string): Promise<any> {
    try {
      return await this.ragService.getSessionHistory(sessionId);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取会话历史失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('sessions/:sessionId/cache')
  @ApiOperation({ summary: '清除会话缓存' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({
    status: 200,
    description: '缓存清除成功',
  })
  async clearSessionCache(@Param('sessionId') sessionId: string): Promise<any> {
    try {
      return await this.ragService.clearSessionCache(sessionId);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '清除缓存失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('batch-query')
  @ApiOperation({ summary: '批量查询' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        queries: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              question: { type: 'string' },
              digitalHumanId: { type: 'string' },
              sessionId: { type: 'string' },
              maxResults: { type: 'number' },
            },
          },
        },
      },
      required: ['queries'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '批量查询结果',
  })
  async batchQuery(@Body() body: any): Promise<any> {
    try {
      return await this.ragService.batchQuery(body.queries);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '批量查询失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('digital-humans/:id/suggestions')
  @ApiOperation({ summary: '获取查询建议' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiQuery({ name: 'q', description: '部分查询内容' })
  @ApiResponse({
    status: 200,
    description: '查询建议列表',
  })
  async getQuerySuggestions(
    @Param('id') digitalHumanId: string,
    @Query('q') partialQuery: string,
  ): Promise<any> {
    try {
      return await this.ragService.getQuerySuggestions(digitalHumanId, partialQuery);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取建议失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('sessions/:sessionId/rating')
  @ApiOperation({ summary: '评价回答质量' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        rating: {
          type: 'number',
          minimum: 1,
          maximum: 5,
          description: '评分（1-5）',
        },
        feedback: {
          type: 'string',
          description: '反馈内容（可选）',
        },
      },
      required: ['rating'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '评价提交成功',
  })
  async rateAnswer(
    @Param('sessionId') sessionId: string,
    @Body() body: any,
  ): Promise<any> {
    try {
      return await this.ragService.rateAnswer(sessionId, body.rating, body.feedback);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '提交评价失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({
    status: 200,
    description: '服务状态',
  })
  async healthCheck(): Promise<any> {
    try {
      return await this.ragService.healthCheck();
    } catch (error) {
      throw new HttpException(
        '健康检查失败',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  @Get('stats')
  @ApiOperation({ summary: '获取RAG引擎统计信息' })
  @ApiResponse({
    status: 200,
    description: '统计信息',
  })
  async getStats(): Promise<any> {
    try {
      return await this.ragService.getStats();
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取统计信息失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
