/**
 * GPU性能分析器
 * 用于分析GPU性能指标和瓶颈
 */
import * as THREE from 'three';
/**
 * GPU性能分析器事件类型
 */
export declare enum GPUPerformanceAnalyzerEventType {
    /** 分析开始 */
    ANALYSIS_STARTED = "analysis_started",
    /** 分析完成 */
    ANALYSIS_COMPLETED = "analysis_completed",
    /** 分析错误 */
    ANALYSIS_ERROR = "analysis_error",
    /** 瓶颈检测 */
    BOTTLENECK_DETECTED = "bottleneck_detected",
    /** 优化建议 */
    OPTIMIZATION_SUGGESTION = "optimization_suggestion"
}
/**
 * GPU性能分析器配置
 */
export interface GPUPerformanceAnalyzerConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 采样间隔（毫秒） */
    sampleInterval?: number;
    /** 历史记录长度 */
    historyLength?: number;
    /** 是否自动采样 */
    autoSample?: boolean;
    /** 是否检测瓶颈 */
    detectBottlenecks?: boolean;
    /** 是否生成优化建议 */
    generateSuggestions?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * GPU性能指标
 */
export interface GPUPerformanceMetrics {
    /** 帧率 */
    fps: number;
    /** 绘制调用次数 */
    drawCalls: number;
    /** 三角形数量 */
    triangles: number;
    /** 点数量 */
    points: number;
    /** 线段数量 */
    lines: number;
    /** 几何体数量 */
    geometries: number;
    /** 纹理数量 */
    textures: number;
    /** 着色器程序数量 */
    programs: number;
    /** 渲染目标数量 */
    renderTargets: number;
    /** 渲染时间（毫秒） */
    renderTime: number;
    /** 后处理时间（毫秒） */
    postProcessingTime: number;
    /** 内存使用量（MB） */
    memoryUsage: number;
    /** 纹理内存（MB） */
    textureMemory: number;
    /** 几何体内存（MB） */
    geometryMemory: number;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 瓶颈类型
 */
export declare enum BottleneckType {
    /** CPU瓶颈 */
    CPU = "cpu",
    /** GPU瓶颈 */
    GPU = "gpu",
    /** 内存瓶颈 */
    MEMORY = "memory",
    /** 绘制调用瓶颈 */
    DRAW_CALLS = "draw_calls",
    /** 几何体瓶颈 */
    GEOMETRY = "geometry",
    /** 纹理瓶颈 */
    TEXTURE = "texture",
    /** 着色器瓶颈 */
    SHADER = "shader",
    /** 后处理瓶颈 */
    POST_PROCESSING = "post_processing"
}
/**
 * 瓶颈信息
 */
export interface BottleneckInfo {
    /** 瓶颈类型 */
    type: BottleneckType;
    /** 严重程度（0-1） */
    severity: number;
    /** 描述 */
    description: string;
    /** 相关指标 */
    metrics: Record<string, number>;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 优化建议
 */
export interface OptimizationSuggestion {
    /** 建议ID */
    id: string;
    /** 标题 */
    title: string;
    /** 描述 */
    description: string;
    /** 优先级（0-1） */
    priority: number;
    /** 难度（0-1） */
    difficulty: number;
    /** 预期改进（0-1） */
    expectedImprovement: number;
    /** 相关瓶颈类型 */
    bottleneckType: BottleneckType;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 分析报告
 */
export interface AnalysisReport {
    /** 当前指标 */
    currentMetrics: GPUPerformanceMetrics;
    /** 平均指标 */
    averageMetrics: GPUPerformanceMetrics;
    /** 最小指标 */
    minMetrics: GPUPerformanceMetrics;
    /** 最大指标 */
    maxMetrics: GPUPerformanceMetrics;
    /** 指标历史 */
    metricsHistory: GPUPerformanceMetrics[];
    /** 检测到的瓶颈 */
    detectedBottlenecks: BottleneckInfo[];
    /** 优化建议 */
    optimizationSuggestions: OptimizationSuggestion[];
    /** 性能评分（0-100） */
    performanceScore: number;
    /** 时间戳 */
    timestamp: number;
}
/**
 * GPU性能分析器
 */
export declare class GPUPerformanceAnalyzer {
    /** 单例实例 */
    private static instance;
    /** 配置 */
    private config;
    /** 是否运行中 */
    private running;
    /** 渲染器 */
    private renderer;
    /** 性能监控器 */
    private performanceMonitor;
    /** 事件发射器 */
    private eventEmitter;
    /** 指标历史 */
    private metricsHistory;
    /** 检测到的瓶颈 */
    private detectedBottlenecks;
    /** 优化建议 */
    private optimizationSuggestions;
    /** 采样定时器ID */
    private sampleTimerId;
    /** 上次采样时间 */
    private lastSampleTime;
    /** 性能评分 */
    private performanceScore;
    /**
     * 获取单例实例
     * @returns GPU性能分析器实例
     */
    static getInstance(): GPUPerformanceAnalyzer;
    /**
     * 私有构造函数
     */
    private constructor();
    /**
     * 配置分析器
     * @param config 配置
     */
    configure(config: GPUPerformanceAnalyzerConfig): void;
    /**
     * 设置渲染器
     * @param renderer 渲染器
     */
    setRenderer(renderer: THREE.WebGLRenderer): void;
    /**
     * 启动分析
     */
    start(): void;
    /**
     * 停止分析
     */
    stop(): void;
    /**
     * 启动自动采样
     */
    private startAutoSampling;
    /**
     * 停止自动采样
     */
    private stopAutoSampling;
    /**
     * 采样
     */
    sample(): void;
    /**
     * 估算纹理内存
     * @returns 纹理内存（MB）
     */
    private estimateTextureMemory;
    /**
     * 估算几何体内存
     * @returns 几何体内存（MB）
     */
    private estimateGeometryMemory;
    /**
     * 检测瓶颈
     * @param metrics 性能指标
     */
    private detectBottlenecks;
    /**
     * 添加瓶颈
     * @param type 瓶颈类型
     * @param severity 严重程度
     * @param description 描述
     * @param metrics 相关指标
     */
    private addBottleneck;
    /**
     * 生成优化建议
     */
    private generateOptimizationSuggestions;
    /**
     * 获取瓶颈的优化建议
     * @param bottleneck 瓶颈信息
     * @returns 优化建议数组
     */
    private getSuggestionsForBottleneck;
    /**
     * 计算性能评分
     */
    private calculatePerformanceScore;
    /**
     * 获取分析报告
     * @returns 分析报告
     */
    getAnalysisReport(): AnalysisReport;
    /**
     * 计算平均指标
     * @returns 平均指标
     */
    private calculateAverageMetrics;
    /**
     * 计算最小指标
     * @returns 最小指标
     */
    private calculateMinMetrics;
    /**
     * 计算最大指标
     * @returns 最大指标
     */
    private calculateMaxMetrics;
    /**
     * 创建空指标
     * @returns 空指标
     */
    private createEmptyMetrics;
    /**
     * 添加事件监听器
     * @param eventType 事件类型
     * @param listener 监听器
     */
    addEventListener(eventType: GPUPerformanceAnalyzerEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param eventType 事件类型
     * @param listener 监听器
     */
    removeEventListener(eventType: GPUPerformanceAnalyzerEventType, listener: (...args: any[]) => void): void;
    /**
     * 清除所有数据
     */
    clearData(): void;
}
