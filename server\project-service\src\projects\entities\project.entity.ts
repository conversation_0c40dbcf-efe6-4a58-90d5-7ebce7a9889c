/**
 * 项目实体
 */
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { ProjectMember } from './project-member.entity';
import { ProjectSetting } from './project-setting.entity';

export enum ProjectVisibility {
  PUBLIC = 'public',
  PRIVATE = 'private',
}

@Entity('projects')
export class Project {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  thumbnailUrl: string;

  @Column({
    type: 'enum',
    enum: ProjectVisibility,
    default: ProjectVisibility.PRIVATE,
  })
  visibility: ProjectVisibility;

  @Column()
  ownerId: string;

  @Column({ default: false })
  isTemplate: boolean;

  @Column({ default: false })
  isArchived: boolean;

  @OneToMany(() => ProjectMember, member => member.project, { cascade: true })
  members: ProjectMember[];

  @OneToMany(() => ProjectSetting, setting => setting.project, { cascade: true })
  settings: ProjectSetting[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
