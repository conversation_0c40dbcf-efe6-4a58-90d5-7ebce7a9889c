<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>情感响应系统示例</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
    }
    
    #loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      z-index: 1000;
    }
    
    #loading h1 {
      font-size: 24px;
      margin-bottom: 20px;
    }
    
    #loading .progress {
      width: 300px;
      height: 20px;
      background-color: #333;
      border-radius: 10px;
      overflow: hidden;
    }
    
    #loading .progress-bar {
      height: 100%;
      background-color: #4CAF50;
      width: 0%;
      transition: width 0.3s;
    }
    
    #loading .status {
      margin-top: 10px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div id="loading">
    <h1>情感响应系统示例</h1>
    <div class="progress">
      <div class="progress-bar" id="progress-bar"></div>
    </div>
    <div class="status" id="status">加载中...</div>
  </div>
  
  <script type="module">
    import { EmotionResponseExample } from '../../engine/examples/avatar/EmotionResponseExample.js';
    
    // 更新加载进度
    function updateProgress(percent, message) {
      const progressBar = document.getElementById('progress-bar');
      const status = document.getElementById('status');
      
      progressBar.style.width = `${percent}%`;
      status.textContent = message;
    }
    
    // 隐藏加载界面
    function hideLoading() {
      const loading = document.getElementById('loading');
      loading.style.opacity = '0';
      loading.style.transition = 'opacity 0.5s';
      
      setTimeout(() => {
        loading.style.display = 'none';
      }, 500);
    }
    
    // 初始化示例
    async function init() {
      try {
        updateProgress(20, '初始化引擎...');
        
        // 创建示例
        const example = new EmotionResponseExample();
        
        updateProgress(100, '加载完成');
        
        // 隐藏加载界面
        setTimeout(hideLoading, 500);
        
        // 添加卸载事件
        window.addEventListener('beforeunload', () => {
          example.dispose();
        });
      } catch (error) {
        console.error('初始化失败:', error);
        updateProgress(100, `初始化失败: ${error.message}`);
      }
    }
    
    // 启动
    window.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html>
