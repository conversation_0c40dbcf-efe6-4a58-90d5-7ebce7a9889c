/**
 * 色差效果
 * 模拟镜头色差
 */
import * as THREE from 'three';
// 使用类型断言导入 ShaderPass
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 色差效果选项
 */
export interface ChromaticAberrationEffectOptions extends PostProcessingEffectOptions {
  /** 偏移量 */
  offset?: THREE.Vector2;
  /** 径向模式 */
  radialMode?: boolean;
  /** 强度 */
  intensity?: number;
}

/**
 * 色差效果
 */
export class ChromaticAberrationEffect extends PostProcessingEffect {
  /** 偏移量 */
  private offset: THREE.Vector2;

  /** 径向模式 */
  private radialMode: boolean;

  /** 强度 */
  private intensity: number;

  /** 色差通道 */
  private chromaticAberrationPass: ShaderPass | null = null;

  /**
   * 创建色差效果
   * @param options 色差效果选项
   */
  constructor(options: ChromaticAberrationEffectOptions = { name: 'ChromaticAberration' }) {
    super(options);

    this.offset = options.offset || new THREE.Vector2(0.005, 0.005);
    this.radialMode = options.radialMode !== undefined ? options.radialMode : false;
    this.intensity = options.intensity !== undefined ? options.intensity : 1.0;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 创建色差着色器
    const chromaticAberrationShader = {
      uniforms: {
        'tDiffuse': { value: null },
        'offset': { value: this.offset },
        'radialMode': { value: this.radialMode ? 1 : 0 },
        'intensity': { value: this.intensity },
        'resolution': { value: new THREE.Vector2(this.width, this.height) }
      },
      vertexShader: /* glsl */`
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: /* glsl */`
        uniform sampler2D tDiffuse;
        uniform vec2 offset;
        uniform int radialMode;
        uniform float intensity;
        uniform vec2 resolution;
        
        varying vec2 vUv;
        
        void main() {
          vec2 uv = vUv;
          vec2 direction;
          
          if (radialMode == 1) {
            // 径向模式
            direction = normalize(uv - 0.5) * offset * intensity;
          } else {
            // 线性模式
            direction = offset * intensity;
          }
          
          vec2 r_uv = uv - direction;
          vec2 g_uv = uv;
          vec2 b_uv = uv + direction;
          
          // 确保UV坐标在有效范围内
          r_uv = clamp(r_uv, 0.0, 1.0);
          g_uv = clamp(g_uv, 0.0, 1.0);
          b_uv = clamp(b_uv, 0.0, 1.0);
          
          float r = texture2D(tDiffuse, r_uv).r;
          float g = texture2D(tDiffuse, g_uv).g;
          float b = texture2D(tDiffuse, b_uv).b;
          float a = texture2D(tDiffuse, vUv).a;
          
          gl_FragColor = vec4(r, g, b, a);
        }
      `
    };

    // 创建色差通道
    this.chromaticAberrationPass = new ShaderPass(chromaticAberrationShader);

    // 设置通道
    this.pass = this.chromaticAberrationPass;
  }

  /**
   * 设置偏移量
   * @param offset 偏移量
   */
  public setOffset(offset: THREE.Vector2): void {
    this.offset.copy(offset);

    if (this.chromaticAberrationPass) {
      const uniforms = this.chromaticAberrationPass.uniforms;
      if (uniforms.offset) {
        uniforms.offset.value = this.offset;
      }
    }
  }

  /**
   * 获取偏移量
   * @returns 偏移量
   */
  public getOffset(): THREE.Vector2 {
    return this.offset.clone();
  }

  /**
   * 设置径向模式
   * @param enabled 是否启用径向模式
   */
  public setRadialMode(enabled: boolean): void {
    this.radialMode = enabled;

    if (this.chromaticAberrationPass) {
      const uniforms = this.chromaticAberrationPass.uniforms;
      if (uniforms.radialMode) {
        uniforms.radialMode.value = enabled ? 1 : 0;
      }
    }
  }

  /**
   * 获取径向模式
   * @returns 是否启用径向模式
   */
  public isRadialMode(): boolean {
    return this.radialMode;
  }

  /**
   * 设置强度
   * @param intensity 强度
   */
  public setIntensity(intensity: number): void {
    this.intensity = intensity;

    if (this.chromaticAberrationPass) {
      const uniforms = this.chromaticAberrationPass.uniforms;
      if (uniforms.intensity) {
        uniforms.intensity.value = intensity;
      }
    }
  }

  /**
   * 获取强度
   * @returns 强度
   */
  public getIntensity(): number {
    return this.intensity;
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    super.resize(width, height);

    if (this.chromaticAberrationPass) {
      const uniforms = this.chromaticAberrationPass.uniforms;
      if (uniforms.resolution) {
        uniforms.resolution.value.set(width, height);
      }
    }
  }
}
