import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { Asset } from '../../assets/entities/asset.entity';

export enum UserRole {
  USER = 'user',
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  BANNED = 'banned',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  username: string;

  @Column({ length: 255, unique: true })
  email: string;

  @Column()
  @Exclude()
  password: string;

  @Column({ name: 'display_name', length: 100, nullable: true })
  displayName: string;

  @Column({ name: 'first_name', length: 50, nullable: true })
  firstName: string;

  @Column({ name: 'last_name', length: 50, nullable: true })
  lastName: string;

  @Column({ nullable: true })
  avatar: string;

  @Column({ type: 'text', nullable: true })
  bio: string;

  @Column({ nullable: true })
  website: string;

  @Column({ nullable: true })
  location: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  @Index()
  role: UserRole;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  @Index()
  status: UserStatus;

  @Column({ name: 'email_verified', default: false })
  emailVerified: boolean;

  @Column({ name: 'email_verification_token', nullable: true })
  @Exclude()
  emailVerificationToken: string;

  @Column({ name: 'password_reset_token', nullable: true })
  @Exclude()
  passwordResetToken: string;

  @Column({ name: 'password_reset_expires', nullable: true })
  @Exclude()
  passwordResetExpires: Date;

  // 统计信息
  @Column({ name: 'asset_count', default: 0 })
  assetCount: number;

  @Column({ name: 'download_count', default: 0 })
  downloadCount: number;

  @Column({ name: 'follower_count', default: 0 })
  followerCount: number;

  @Column({ name: 'following_count', default: 0 })
  followingCount: number;

  // 偏好设置
  @Column({ type: 'json', nullable: true })
  preferences: Record<string, any>;

  // 关联关系
  @OneToMany(() => Asset, asset => asset.creator)
  assets: Asset[];

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'last_login_at', nullable: true })
  lastLoginAt: Date;

  @Column({ name: 'last_active_at', nullable: true })
  lastActiveAt: Date;

  // 计算属性
  get fullName(): string {
    if (this.firstName && this.lastName) {
      return `${this.firstName} ${this.lastName}`;
    }
    return this.displayName || this.username;
  }

  get isActive(): boolean {
    return this.status === UserStatus.ACTIVE;
  }

  get isVerified(): boolean {
    return this.emailVerified;
  }

  get canUploadAssets(): boolean {
    return this.isActive && this.isVerified;
  }

  get isModerator(): boolean {
    return [UserRole.MODERATOR, UserRole.ADMIN, UserRole.SUPER_ADMIN].includes(this.role);
  }

  get isAdmin(): boolean {
    return [UserRole.ADMIN, UserRole.SUPER_ADMIN].includes(this.role);
  }

  get profileCompleteness(): number {
    let score = 0;
    const fields = [
      this.displayName,
      this.firstName,
      this.lastName,
      this.avatar,
      this.bio,
      this.website,
      this.location,
    ];
    
    fields.forEach(field => {
      if (field && field.trim()) score += 1;
    });
    
    return Math.round((score / fields.length) * 100);
  }
}
