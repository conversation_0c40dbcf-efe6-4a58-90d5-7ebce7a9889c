/**
 * 动画事件系统
 * 用于在动画播放过程中触发事件
 */
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import type { World } from '../core/World';
import { Animator } from './Animator';
/**
 * 动画事件类型
 */
export declare enum AnimationEventType {
    /** 开始 */
    START = "start",
    /** 结束 */
    COMPLETE = "complete",
    /** 循环 */
    LOOP = "loop",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 动画事件数据
 */
export interface AnimationEventData {
    /** 事件类型 */
    type: AnimationEventType;
    /** 事件名称 */
    name: string;
    /** 时间点（秒） */
    time: number;
    /** 动画片段名称 */
    clipName: string;
    /** 参数 */
    params?: any;
}
/**
 * 动画事件组件
 */
export declare class AnimationEventComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 事件列表 */
    private events;
    /** 事件发射器 */
    private eventEmitter;
    /** 动画控制器 */
    private animator;
    /** 上一帧时间 */
    private lastTime;
    /** 是否启用 */
    private eventEnabled;
    /**
     * 创建动画事件组件
     */
    constructor();
    /**
     * 设置动画控制器
     * @param animator 动画控制器
     */
    setAnimator(animator: Animator): void;
    /**
     * 获取动画控制器
     * @returns 动画控制器
     */
    getAnimator(): Animator | null;
    /**
     * 添加事件
     * @param clipName 动画片段名称
     * @param eventData 事件数据
     */
    addEvent(clipName: string, eventData: AnimationEventData): void;
    /**
     * 移除事件
     * @param clipName 动画片段名称
     * @param eventName 事件名称
     * @returns 是否成功移除
     */
    removeEvent(clipName: string, eventName: string): boolean;
    /**
     * 清除事件
     * @param clipName 动画片段名称，如果不指定则清除所有事件
     */
    clearEvents(clipName?: string): void;
    /**
     * 获取事件
     * @param clipName 动画片段名称
     * @returns 事件列表
     */
    getEvents(clipName: string): AnimationEventData[];
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: string, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: string, listener: (data: any) => void): void;
    /**
     * 是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 设置启用状态
     * @param enabled 启用状态
     */
    setEnabled(enabled: boolean): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
}
/**
 * 动画事件系统
 */
export declare class AnimationEventSystem extends System {
    /** 组件列表 */
    private components;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否启用调试 */
    private debug;
    /**
     * 创建动画事件系统
     * @param world 世界
     * @param debug 是否启用调试
     */
    constructor(_world: World, debug?: boolean);
    /**
     * 创建动画事件组件
     * @param entity 实体
     * @param animator 动画控制器
     * @returns 动画事件组件
     */
    createAnimationEvent(entity: Entity, animator: Animator): AnimationEventComponent;
    /**
     * 移除动画事件组件
     * @param entity 实体
     * @returns 是否成功移除
     */
    removeAnimationEvent(entity: Entity): boolean;
    /**
     * 添加事件
     * @param entity 实体
     * @param clipName 动画片段名称
     * @param eventData 事件数据
     * @returns 是否成功添加
     */
    addEvent(entity: Entity, clipName: string, eventData: AnimationEventData): boolean;
    /**
     * 移除事件
     * @param entity 实体
     * @param clipName 动画片段名称
     * @param eventName 事件名称
     * @returns 是否成功移除
     */
    removeEvent(entity: Entity, clipName: string, eventName: string): boolean;
    /**
     * 添加事件监听器
     * @param entity 实体
     * @param type 事件类型
     * @param listener 监听器
     * @returns 是否成功添加
     */
    addEventListener(entity: Entity, type: string, listener: (data: any) => void): boolean;
    /**
     * 移除事件监听器
     * @param entity 实体
     * @param type 事件类型
     * @param listener 监听器
     * @returns 是否成功移除
     */
    removeEventListener(entity: Entity, type: string, listener: (data: any) => void): boolean;
    /**
     * 添加全局事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addGlobalEventListener(type: string, listener: (data: any) => void): void;
    /**
     * 移除全局事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeGlobalEventListener(type: string, listener: (data: any) => void): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
