/**
 * PhysicsCollider类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { PhysicsCollider } from '../../src/physics/PhysicsCollider';
import { PhysicsSystem } from '../../src/physics/PhysicsSystem';
import { PhysicsBody } from '../../src/physics/PhysicsBody';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { ColliderType, BodyType } from '../../src/physics/types';

describe('PhysicsCollider', () => {
  let engine: Engine;
  let world: World;
  let physicsSystem: PhysicsSystem;
  let entity: Entity;
  let transform: Transform;
  let physicsBody: PhysicsBody;
  let physicsCollider: PhysicsCollider;
  
  // 在每个测试前创建一个新的碰撞体实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建物理系统
    physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: false
    });
    
    // 添加物理系统到引擎
    engine.addSystem(physicsSystem);
    
    // 初始化引擎
    engine.initialize();
    
    // 创建实体
    entity = new Entity(world);
    
    // 添加变换组件
    transform = new Transform({
      position: { x: 0, y: 5, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity.addComponent(transform);
    
    // 创建物理体
    physicsBody = new PhysicsBody(entity, {
      type: BodyType.DYNAMIC,
      mass: 1,
      position: { x: 0, y: 5, z: 0 }
    });
    entity.addComponent(physicsBody);
    
    // 创建碰撞体
    physicsCollider = new PhysicsCollider(entity, {
      type: ColliderType.BOX,
      size: { x: 1, y: 1, z: 1 },
      offset: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      isTrigger: false,
      material: {
        friction: 0.3,
        restitution: 0.3
      }
    });
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试碰撞体初始化
  it('应该正确初始化碰撞体', () => {
    expect(physicsCollider).toBeDefined();
    expect(physicsCollider.getEntity()).toBe(entity);
    expect(physicsCollider.getType()).toBe(ColliderType.BOX);
    expect(physicsCollider.getSize()).toEqual({ x: 1, y: 1, z: 1 });
    expect(physicsCollider.getOffset()).toEqual({ x: 0, y: 0, z: 0 });
    expect(physicsCollider.getRotation()).toEqual({ x: 0, y: 0, z: 0 });
    expect(physicsCollider.isTrigger()).toBe(false);
    expect(physicsCollider.getMaterial()).toEqual({
      friction: 0.3,
      restitution: 0.3
    });
  });
  
  // 测试碰撞体类型
  it('应该能够设置和获取碰撞体类型', () => {
    // 验证初始类型
    expect(physicsCollider.getType()).toBe(ColliderType.BOX);
    
    // 设置新类型
    physicsCollider.setType(ColliderType.SPHERE);
    
    // 验证类型已更改
    expect(physicsCollider.getType()).toBe(ColliderType.SPHERE);
    
    // 设置另一个类型
    physicsCollider.setType(ColliderType.CAPSULE);
    
    // 验证类型已更改
    expect(physicsCollider.getType()).toBe(ColliderType.CAPSULE);
  });
  
  // 测试碰撞体尺寸
  it('应该能够设置和获取碰撞体尺寸', () => {
    // 验证初始尺寸
    expect(physicsCollider.getSize()).toEqual({ x: 1, y: 1, z: 1 });
    
    // 设置新尺寸
    physicsCollider.setSize({ x: 2, y: 3, z: 4 });
    
    // 验证尺寸已更改
    expect(physicsCollider.getSize()).toEqual({ x: 2, y: 3, z: 4 });
  });
  
  // 测试碰撞体偏移
  it('应该能够设置和获取碰撞体偏移', () => {
    // 验证初始偏移
    expect(physicsCollider.getOffset()).toEqual({ x: 0, y: 0, z: 0 });
    
    // 设置新偏移
    physicsCollider.setOffset({ x: 1, y: 2, z: 3 });
    
    // 验证偏移已更改
    expect(physicsCollider.getOffset()).toEqual({ x: 1, y: 2, z: 3 });
  });
  
  // 测试碰撞体旋转
  it('应该能够设置和获取碰撞体旋转', () => {
    // 验证初始旋转
    expect(physicsCollider.getRotation()).toEqual({ x: 0, y: 0, z: 0 });
    
    // 设置新旋转
    physicsCollider.setRotation({ x: 0.1, y: 0.2, z: 0.3 });
    
    // 验证旋转已更改
    const rotation = physicsCollider.getRotation();
    expect(rotation.x).toBeCloseTo(0.1);
    expect(rotation.y).toBeCloseTo(0.2);
    expect(rotation.z).toBeCloseTo(0.3);
  });
  
  // 测试碰撞体触发器
  it('应该能够设置和获取碰撞体触发器状态', () => {
    // 验证初始触发器状态
    expect(physicsCollider.isTrigger()).toBe(false);
    
    // 设置触发器状态
    physicsCollider.setTrigger(true);
    
    // 验证触发器状态已更改
    expect(physicsCollider.isTrigger()).toBe(true);
  });
  
  // 测试碰撞体材质
  it('应该能够设置和获取碰撞体材质', () => {
    // 验证初始材质
    expect(physicsCollider.getMaterial()).toEqual({
      friction: 0.3,
      restitution: 0.3
    });
    
    // 设置新材质
    physicsCollider.setMaterial({
      friction: 0.5,
      restitution: 0.7
    });
    
    // 验证材质已更改
    expect(physicsCollider.getMaterial()).toEqual({
      friction: 0.5,
      restitution: 0.7
    });
  });
  
  // 测试创建盒体碰撞体
  it('应该能够创建盒体碰撞体', () => {
    // 创建Cannon.js物理体的模拟
    const cannonBody = new CANNON.Body({
      mass: 1,
      position: new CANNON.Vec3(0, 5, 0)
    });
    
    // 设置物理体的Cannon.js物理体
    (physicsBody as any).body = cannonBody;
    
    // 创建addShape方法的模拟
    const addShapeSpy = vi.spyOn(cannonBody, 'addShape');
    
    // 设置碰撞体类型为盒体
    physicsCollider.setType(ColliderType.BOX);
    
    // 创建碰撞体形状
    physicsCollider.createShape();
    
    // 验证addShape方法被调用
    expect(addShapeSpy).toHaveBeenCalled();
    
    // 验证创建的是CANNON.Box
    const shape = addShapeSpy.mock.calls[0][0];
    expect(shape).toBeInstanceOf(CANNON.Box);
  });
  
  // 测试创建球体碰撞体
  it('应该能够创建球体碰撞体', () => {
    // 创建Cannon.js物理体的模拟
    const cannonBody = new CANNON.Body({
      mass: 1,
      position: new CANNON.Vec3(0, 5, 0)
    });
    
    // 设置物理体的Cannon.js物理体
    (physicsBody as any).body = cannonBody;
    
    // 创建addShape方法的模拟
    const addShapeSpy = vi.spyOn(cannonBody, 'addShape');
    
    // 设置碰撞体类型为球体
    physicsCollider.setType(ColliderType.SPHERE);
    
    // 创建碰撞体形状
    physicsCollider.createShape();
    
    // 验证addShape方法被调用
    expect(addShapeSpy).toHaveBeenCalled();
    
    // 验证创建的是CANNON.Sphere
    const shape = addShapeSpy.mock.calls[0][0];
    expect(shape).toBeInstanceOf(CANNON.Sphere);
  });
  
  // 测试创建胶囊体碰撞体
  it('应该能够创建胶囊体碰撞体', () => {
    // 创建Cannon.js物理体的模拟
    const cannonBody = new CANNON.Body({
      mass: 1,
      position: new CANNON.Vec3(0, 5, 0)
    });
    
    // 设置物理体的Cannon.js物理体
    (physicsBody as any).body = cannonBody;
    
    // 创建addShape方法的模拟
    const addShapeSpy = vi.spyOn(cannonBody, 'addShape');
    
    // 设置碰撞体类型为胶囊体
    physicsCollider.setType(ColliderType.CAPSULE);
    
    // 创建碰撞体形状
    physicsCollider.createShape();
    
    // 验证addShape方法被调用
    expect(addShapeSpy).toHaveBeenCalled();
  });
  
  // 测试创建圆柱体碰撞体
  it('应该能够创建圆柱体碰撞体', () => {
    // 创建Cannon.js物理体的模拟
    const cannonBody = new CANNON.Body({
      mass: 1,
      position: new CANNON.Vec3(0, 5, 0)
    });
    
    // 设置物理体的Cannon.js物理体
    (physicsBody as any).body = cannonBody;
    
    // 创建addShape方法的模拟
    const addShapeSpy = vi.spyOn(cannonBody, 'addShape');
    
    // 设置碰撞体类型为圆柱体
    physicsCollider.setType(ColliderType.CYLINDER);
    
    // 创建碰撞体形状
    physicsCollider.createShape();
    
    // 验证addShape方法被调用
    expect(addShapeSpy).toHaveBeenCalled();
    
    // 验证创建的是CANNON.Cylinder
    const shape = addShapeSpy.mock.calls[0][0];
    expect(shape).toBeInstanceOf(CANNON.Cylinder);
  });
  
  // 测试碰撞体事件
  it('应该能够发射和监听碰撞体事件', () => {
    // 创建事件监听器
    const listener = vi.fn();
    
    // 添加事件监听器
    physicsCollider.on('collisionStart', listener);
    
    // 发射事件
    physicsCollider.emit('collisionStart', { entity: entity });
    
    // 验证事件监听器被调用
    expect(listener).toHaveBeenCalledWith({ entity: entity });
  });
});
