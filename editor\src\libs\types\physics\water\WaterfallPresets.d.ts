/**
 * 瀑布预设（简化版）
 * 提供各种类型的瀑布预设配置
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import { World } from '../../core/World';
/**
 * 瀑布预设类型
 */
export declare enum WaterfallPresetType {
    /** 标准瀑布 */
    STANDARD = "standard",
    /** 高瀑布 */
    HIGH = "high",
    /** 宽瀑布 */
    WIDE = "wide",
    /** 小瀑布 */
    SMALL = "small"
}
/**
 * 瀑布预设配置（简化版）
 */
export interface WaterfallPresetConfig {
    /** 预设类型 */
    type: WaterfallPresetType;
    /** 瀑布宽度 */
    width?: number;
    /** 瀑布高度 */
    height?: number;
    /** 瀑布深度 */
    depth?: number;
    /** 瀑布位置 */
    position?: THREE.Vector3;
    /** 瀑布旋转 */
    rotation?: THREE.Euler;
    /** 瀑布颜色 */
    color?: THREE.Color;
    /** 瀑布不透明度 */
    opacity?: number;
    /** 瀑布流速 */
    flowSpeed?: number;
    /** 瀑布流向 */
    flowDirection?: {
        x: number;
        y: number;
        z: number;
    };
    /** 瀑布湍流强度 */
    turbulenceStrength?: number;
    /** 瀑布湍流频率 */
    turbulenceFrequency?: number;
    /** 瀑布湍流速度 */
    turbulenceSpeed?: number;
}
/**
 * 瀑布预设（简化版）
 */
export declare class WaterfallPresets {
    /**
     * 将预设类型转换为瀑布类型
     * @param presetType 预设类型
     * @returns 瀑布类型
     */
    private static convertPresetTypeToWaterfallType;
    /**
     * 创建瀑布预设
     * @param world 世界
     * @param config 预设配置
     * @returns 瀑布实体
     */
    static createPreset(world: World, config: WaterfallPresetConfig): Entity;
    /**
     * 应用标准瀑布预设
     * @param config 瀑布配置
     */
    private static applyStandardPreset;
    /**
     * 应用高瀑布预设
     * @param config 瀑布配置
     */
    private static applyHighPreset;
    /**
     * 应用宽瀑布预设
     * @param config 瀑布配置
     */
    private static applyWidePreset;
    /**
     * 应用小瀑布预设
     * @param config 瀑布配置
     */
    private static applySmallPreset;
}
