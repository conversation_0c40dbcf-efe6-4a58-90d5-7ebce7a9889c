import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { type EventCallback } from '../../utils/EventEmitter';
import { AdvancedCharacterController } from './AdvancedCharacterController';
import { ActionRecorder, ActionRecording, ActionPlayback } from '../recording';
/**
 * 动作类型
 */
export declare enum ActionType {
    /** 基础动作 */
    BASIC = "basic",
    /** 组合动作 */
    COMBO = "combo",
    /** 环境交互动作 */
    ENVIRONMENT = "environment",
    /** 物品使用动作 */
    ITEM = "item",
    /** 战斗动作 */
    COMBAT = "combat",
    /** 社交动作 */
    SOCIAL = "social",
    /** 情感动作 */
    EMOTE = "emote",
    /** 自定义动作 */
    CUSTOM = "custom",
    /** 物理交互动作 */
    PHYSICS_INTERACTION = "physics_interaction",
    /** 环境响应动作 */
    ENVIRONMENT_RESPONSE = "environment_response",
    /** 姿势动作 */
    POSE = "pose",
    /** 手势动作 */
    GESTURE = "gesture",
    /** 面部表情动作 */
    FACIAL = "facial",
    /** 对话动作 */
    DIALOGUE = "dialogue",
    /** 移动动作 */
    LOCOMOTION = "locomotion"
}
/**
 * 动作优先级
 */
export declare enum ActionPriority {
    /** 低优先级 */
    LOW = 0,
    /** 中优先级 */
    MEDIUM = 1,
    /** 高优先级 */
    HIGH = 2,
    /** 最高优先级 */
    CRITICAL = 3
}
/**
 * 动作数据
 */
export interface ActionData {
    /** 动作ID */
    id: string;
    /** 动作名称 */
    name: string;
    /** 动作类型 */
    type: ActionType;
    /** 动作优先级 */
    priority: ActionPriority;
    /** 动画名称 */
    animationName: string;
    /** 是否可中断 */
    interruptible: boolean;
    /** 是否循环 */
    loop: boolean;
    /** 过渡时间 */
    transitionTime: number;
    /** 动作持续时间（秒，0表示无限） */
    duration: number;
    /** 动作参数 */
    params?: Record<string, any>;
    /** 动作事件 */
    events?: ActionEvent[];
}
/**
 * 动作事件
 */
export interface ActionEvent {
    /** 事件名称 */
    name: string;
    /** 事件时间（秒） */
    time: number;
    /** 事件参数 */
    params?: Record<string, any>;
}
/**
 * 动作实例
 */
export interface ActionInstance {
    /** 动作数据 */
    data: ActionData;
    /** 实体 */
    entity: Entity;
    /** 开始时间 */
    startTime: number;
    /** 结束时间 */
    endTime: number;
    /** 是否正在播放 */
    isPlaying: boolean;
    /** 是否已完成 */
    isCompleted: boolean;
    /** 已触发的事件 */
    triggeredEvents: Set<string>;
}
/**
 * 动作控制系统配置
 */
export interface ActionControlSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 最大同时动作数 */
    maxConcurrentActions?: number;
    /** 是否启用动作混合 */
    enableActionBlending?: boolean;
    /** 是否启用动作队列 */
    enableActionQueue?: boolean;
    /** 是否启用动作事件 */
    enableActionEvents?: boolean;
    /** 是否启用物理驱动动作 */
    enablePhysicsDrivenActions?: boolean;
    /** 是否启用动作录制 */
    enableActionRecording?: boolean;
    /** 是否启用动作回放 */
    enableActionPlayback?: boolean;
}
/**
 * 动作控制系统
 */
export declare class ActionControlSystem extends System {
    /** 系统类型 */
    static readonly type: string;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 动画系统 */
    private animationSystem;
    /** 物理系统 */
    private physicsSystem;
    /** 动作库 */
    private actionLibrary;
    /** 活动动作 */
    private activeActions;
    /** 动作队列 */
    private actionQueues;
    /** 角色控制器映射 */
    private characterControllers;
    /** 事件发射器 */
    private eventEmitter;
    /** 当前时间 */
    private currentTime;
    /** 动作录制器映射 */
    private actionRecorders;
    /** 动作回放器映射 */
    private actionPlaybacks;
    /** 录制的动作 */
    private recordings;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<ActionControlSystemConfig>);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 注册角色控制器
     * @param entity 实体
     * @param controller 控制器
     */
    registerController(entity: Entity, controller: AdvancedCharacterController): void;
    /**
     * 注册动作
     * @param action 动作数据
     */
    registerAction(action: ActionData): void;
    /**
     * 播放动作
     * @param entity 实体
     * @param actionId 动作ID
     * @param params 动作参数
     * @returns 是否成功播放
     */
    playAction(entity: Entity, actionId: string, params?: Record<string, any>): boolean;
    /**
     * 停止动作
     * @param entity 实体
     * @param actionId 动作ID
     * @returns 是否成功停止
     */
    stopAction(entity: Entity, actionId: string): boolean;
    /**
     * 更新系统
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新实体动作
     * @param entity 实体
     * @param actions 动作实例数组
     * @param deltaTime 时间增量（秒）
     */
    private updateEntityActions;
    /**
     * 处理动作队列
     */
    private processActionQueues;
    /**
     * 创建动作录制器
     * @param entity 实体
     * @param config 录制器配置
     * @returns 动作录制器
     */
    createActionRecorder(entity: Entity, config?: any): ActionRecorder;
    /**
     * 创建动作回放器
     * @param entity 实体
     * @param config 回放器配置
     * @returns 动作回放器
     */
    createActionPlayback(entity: Entity, config?: any): ActionPlayback;
    /**
     * 开始录制动作
     * @param entity 实体
     * @param name 录制名称
     * @returns 是否成功开始录制
     */
    startRecording(entity: Entity, name?: string): boolean;
    /**
     * 停止录制动作
     * @param entity 实体
     * @returns 录制数据
     */
    stopRecording(entity: Entity): ActionRecording | null;
    /**
     * 播放录制的动作
     * @param entity 实体
     * @param recordingId 录制ID
     * @param speed 播放速度
     * @returns 是否成功开始播放
     */
    playRecording(entity: Entity, recordingId: string, speed?: number): boolean;
    /**
     * 暂停播放
     * @param entity 实体
     * @returns 是否成功暂停
     */
    pausePlayback(entity: Entity): boolean;
    /**
     * 继续播放
     * @param entity 实体
     * @returns 是否成功继续
     */
    resumePlayback(entity: Entity): boolean;
    /**
     * 停止播放
     * @param entity 实体
     * @returns 是否成功停止
     */
    stopPlayback(entity: Entity): boolean;
    /**
     * 获取录制
     * @param recordingId 录制ID
     * @returns 录制数据
     */
    getRecording(recordingId: string): ActionRecording | undefined;
    /**
     * 获取所有录制
     * @returns 录制数据数组
     */
    getAllRecordings(): ActionRecording[];
    /**
     * 添加录制
     * @param recording 录制数据
     */
    addRecording(recording: ActionRecording): void;
    /**
     * 删除录制
     * @param recordingId 录制ID
     * @returns 是否成功删除
     */
    removeRecording(recordingId: string): boolean;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    on(event: string, callback: EventCallback): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    off(event: string, callback?: EventCallback): this;
}
