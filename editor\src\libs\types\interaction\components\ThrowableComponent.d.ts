/**
 * 可抛掷组件
 * 用于处理物体的抛掷行为
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector3 } from 'three';
import { EventCallback } from '../../utils/EventEmitter';
import { Hand } from './GrabbableComponent';
/**
 * 抛掷类型
 */
export declare enum ThrowType {
    /** 物理抛掷 - 使用物理引擎计算 */
    PHYSICS = "physics",
    /** 速度抛掷 - 直接设置速度 */
    VELOCITY = "velocity",
    /** 弹道抛掷 - 计算弹道轨迹 */
    BALLISTIC = "ballistic"
}
/**
 * 可抛掷组件配置
 */
export interface ThrowableComponentConfig {
    /** 是否可抛掷 */
    throwable?: boolean;
    /** 抛掷类型 */
    throwType?: ThrowType;
    /** 抛掷力量倍数 */
    throwForceMultiplier?: number;
    /** 抛掷角速度倍数 */
    throwAngularForceMultiplier?: number;
    /** 抛掷速度平滑因子 */
    throwVelocitySmoothingFactor?: number;
    /** 抛掷历史记录长度 */
    throwHistoryLength?: number;
    /** 抛掷时是否保持旋转 */
    preserveRotationOnThrow?: boolean;
    /** 抛掷声音 */
    throwSound?: string;
    /** 抛掷回调 */
    onThrow?: (entity: Entity, velocity: Vector3, angularVelocity: Vector3) => void;
}
/**
 * 可抛掷组件
 */
export declare class ThrowableComponent extends Component {
    /** 组件类型 */
    static readonly TYPE: string;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否可抛掷 */
    private _throwable;
    /** 抛掷类型 */
    private _throwType;
    /** 抛掷力量倍数 */
    private _throwForceMultiplier;
    /** 抛掷角速度倍数 */
    private _throwAngularForceMultiplier;
    /** 抛掷速度平滑因子 - 预留功能 */
    private _throwVelocitySmoothingFactor;
    /** 抛掷历史记录长度 */
    private _throwHistoryLength;
    /** 抛掷时是否保持旋转 - 预留功能 */
    private _preserveRotationOnThrow;
    /** 抛掷声音 */
    private _throwSound?;
    /** 抛掷回调 */
    private _onThrow?;
    /** 位置历史记录 */
    private _positionHistory;
    /** 旋转历史记录 */
    private _rotationHistory;
    /** 时间戳历史记录 */
    private _timeHistory;
    /** 是否正在被抓取 */
    private _isGrabbed;
    /** 抓取者 - 预留功能 */
    private _grabber?;
    /** 抓取手 - 预留功能 */
    private _hand?;
    /** 上次更新时间 - 预留功能 */
    private _lastUpdateTime;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: ThrowableComponentConfig);
    /**
     * 获取是否可抛掷
     */
    get throwable(): boolean;
    /**
     * 设置是否可抛掷
     */
    set throwable(value: boolean);
    /**
     * 获取抛掷类型
     */
    get throwType(): ThrowType;
    /**
     * 设置抛掷类型
     */
    set throwType(value: ThrowType);
    /**
     * 获取抛掷力量倍数
     */
    get throwForceMultiplier(): number;
    /**
     * 设置抛掷力量倍数
     */
    set throwForceMultiplier(value: number);
    /**
     * 获取抛掷角速度倍数
     */
    get throwAngularForceMultiplier(): number;
    /**
     * 设置抛掷角速度倍数
     */
    set throwAngularForceMultiplier(value: number);
    /**
     * 开始抓取
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    startGrab(grabber: Entity, hand: Hand): void;
    /**
     * 结束抓取
     */
    endGrab(): void;
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 清空历史记录
     */
    private clearHistory;
    /**
     * 计算抛掷速度和角速度
     * @returns 速度和角速度
     */
    private calculateThrowVelocity;
    /**
     * 应用抛掷
     * @param velocity 速度
     * @param angularVelocity 角速度
     */
    private applyThrow;
    /**
     * 应用物理抛掷
     * @param velocity 速度
     * @param angularVelocity 角速度
     */
    private applyPhysicsThrow;
    /**
     * 应用速度抛掷
     * @param velocity 速度
     * @param angularVelocity 角速度
     */
    private applyVelocityThrow;
    /**
     * 应用弹道抛掷
     * @param velocity 速度
     * @param angularVelocity 角速度
     */
    private applyBallisticThrow;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    on(event: string, listener: EventCallback): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    off(event: string, listener?: EventCallback): this;
    /**
     * 销毁组件
     */
    destroy(): void;
}
