import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 生成身体动画节点
 * 使用AI生成身体动画
 */
export declare class GenerateBodyAnimationNode extends AsyncNode {
    /** 请求ID */
    private requestId;
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
    /**
     * 等待动画生成结果
     * @param component AI动画合成组件
     * @param requestId 请求ID
     * @returns 生成结果
     */
    private waitForAnimationResult;
    /**
     * 取消执行
     */
    cancel(): void;
}
/**
 * 生成面部动画节点
 * 使用AI生成面部动画
 */
export declare class GenerateFacialAnimationNode extends AsyncNode {
    /** 请求ID */
    private requestId;
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
    /**
     * 等待动画生成结果
     * @param component AI动画合成组件
     * @param requestId 请求ID
     * @returns 生成结果
     */
    private waitForAnimationResult;
    /**
     * 取消执行
     */
    cancel(): void;
}
/**
 * 注册AI节点
 * @param registry 节点注册表
 */
export declare function registerAINodes(registry: NodeRegistry): void;
