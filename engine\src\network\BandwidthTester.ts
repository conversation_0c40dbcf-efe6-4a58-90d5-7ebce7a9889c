/**
 * 带宽测试器
 * 用于测试网络带宽和延迟
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';

/**
 * 带宽测试配置
 */
export interface BandwidthTestConfig {
  /** 测试服务器URL */
  serverUrl: string;
  /** 上传测试数据大小（字节） */
  uploadSize?: number;
  /** 下载测试数据大小（字节） */
  downloadSize?: number;
  /** 测试超时时间（毫秒） */
  timeout?: number;
  /** 测试重试次数 */
  retries?: number;
  /** 是否启用详细日志 */
  detailedLogging?: boolean;
  /** 测试并发连接数 */
  concurrentConnections?: number;
  /** 是否使用WebSocket进行测试 */
  useWebSocket?: boolean;
  /** 是否使用HTTP进行测试 */
  useHttp?: boolean;
  /** 是否测试上传带宽 */
  testUpload?: boolean;
  /** 是否测试下载带宽 */
  testDownload?: boolean;
  /** 是否测试延迟 */
  testLatency?: boolean;
  /** 延迟测试次数 */
  latencyTestCount?: number;
}

/**
 * 带宽测试结果
 */
export interface BandwidthTestResult {
  /** 上传带宽（字节/秒） */
  uploadBandwidth: number;
  /** 下载带宽（字节/秒） */
  downloadBandwidth: number;
  /** 延迟（毫秒） */
  latency: number;
  /** 抖动（毫秒） */
  jitter: number;
  /** 测试是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 测试开始时间 */
  startTime: number;
  /** 测试结束时间 */
  endTime: number;
  /** 测试持续时间（毫秒） */
  duration: number;
  /** 上传测试详情 */
  uploadDetails?: {
    /** 上传的字节数 */
    bytes: number;
    /** 上传时间（毫秒） */
    time: number;
    /** 上传速度（字节/秒） */
    speed: number;
  };
  /** 下载测试详情 */
  downloadDetails?: {
    /** 下载的字节数 */
    bytes: number;
    /** 下载时间（毫秒） */
    time: number;
    /** 下载速度（字节/秒） */
    speed: number;
  };
  /** 延迟测试详情 */
  latencyDetails?: {
    /** 最小延迟（毫秒） */
    min: number;
    /** 最大延迟（毫秒） */
    max: number;
    /** 平均延迟（毫秒） */
    avg: number;
    /** 延迟标准差（毫秒） */
    stdDev: number;
    /** 延迟测量值列表（毫秒） */
    values: number[];
  };
}

/**
 * 带宽测试器
 * 用于测试网络带宽和延迟
 */
export class BandwidthTester extends EventEmitter {
  /** 配置 */
  private config: Required<BandwidthTestConfig>;
  
  /** 当前测试结果 */
  private currentResult: Partial<BandwidthTestResult> = {};
  
  /** 测试是否正在进行 */
  private testing: boolean = false;
  
  /** WebSocket连接 */
  private ws: WebSocket | null = null;
  
  /** 测试超时定时器ID */
  private timeoutId: number | null = null;
  
  /** 延迟测试结果 */
  private latencyResults: number[] = [];
  
  /** 上传测试开始时间 */
  private uploadStartTime: number = 0;
  
  /** 下载测试开始时间 */
  private downloadStartTime: number = 0;
  
  /**
   * 创建带宽测试器
   * @param config 配置
   */
  constructor(config: BandwidthTestConfig) {
    super();
    
    // 默认配置
    this.config = {
      serverUrl: config.serverUrl,
      uploadSize: config.uploadSize || 1024 * 1024, // 1MB
      downloadSize: config.downloadSize || 10 * 1024 * 1024, // 10MB
      timeout: config.timeout || 30000, // 30秒
      retries: config.retries || 3,
      detailedLogging: config.detailedLogging || false,
      concurrentConnections: config.concurrentConnections || 3,
      useWebSocket: config.useWebSocket !== undefined ? config.useWebSocket : true,
      useHttp: config.useHttp !== undefined ? config.useHttp : false,
      testUpload: config.testUpload !== undefined ? config.testUpload : true,
      testDownload: config.testDownload !== undefined ? config.testDownload : true,
      testLatency: config.testLatency !== undefined ? config.testLatency : true,
      latencyTestCount: config.latencyTestCount || 10,
    };
    
    if (this.config.detailedLogging) {
      Debug.log('BandwidthTester', '带宽测试器已创建');
    }
  }
  
  /**
   * 开始测试
   * @returns 测试结果Promise
   */
  public async startTest(): Promise<BandwidthTestResult> {
    if (this.testing) {
      throw new Error('测试已在进行中');
    }
    
    this.testing = true;
    this.resetTestResult();
    
    // 记录测试开始时间
    this.currentResult.startTime = Date.now();
    
    try {
      // 设置超时
      this.setTestTimeout();
      
      // 发送测试开始事件
      this.emit('testStart', { time: this.currentResult.startTime });
      
      if (this.config.detailedLogging) {
        Debug.log('BandwidthTester', '开始带宽测试');
      }
      
      // 测试延迟
      if (this.config.testLatency) {
        await this.testLatency();
      }
      
      // 测试下载带宽
      if (this.config.testDownload) {
        await this.testDownloadBandwidth();
      }
      
      // 测试上传带宽
      if (this.config.testUpload) {
        await this.testUploadBandwidth();
      }
      
      // 清除超时
      this.clearTestTimeout();
      
      // 记录测试结束时间
      this.currentResult.endTime = Date.now();
      this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
      this.currentResult.success = true;
      
      // 发送测试完成事件
      this.emit('testComplete', this.getTestResult());
      
      if (this.config.detailedLogging) {
        Debug.log('BandwidthTester', `带宽测试完成: 上传=${this.currentResult.uploadBandwidth}B/s, 下载=${this.currentResult.downloadBandwidth}B/s, 延迟=${this.currentResult.latency}ms`);
      }
      
      this.testing = false;
      return this.getTestResult();
    } catch (error) {
      // 清除超时
      this.clearTestTimeout();
      
      // 记录测试结束时间
      this.currentResult.endTime = Date.now();
      this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
      this.currentResult.success = false;
      this.currentResult.error = error instanceof Error ? error.message : String(error);
      
      // 发送测试失败事件
      this.emit('testError', { error: this.currentResult.error, result: this.getTestResult() });
      
      if (this.config.detailedLogging) {
        Debug.log('BandwidthTester', `带宽测试失败: ${this.currentResult.error}`);
      }
      
      this.testing = false;
      return this.getTestResult();
    }
  }
  
  /**
   * 取消测试
   */
  public cancelTest(): void {
    if (!this.testing) {
      return;
    }
    
    // 清除超时
    this.clearTestTimeout();
    
    // 关闭WebSocket连接
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    // 记录测试结束时间
    this.currentResult.endTime = Date.now();
    this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
    this.currentResult.success = false;
    this.currentResult.error = '测试被取消';
    
    // 发送测试取消事件
    this.emit('testCancel', this.getTestResult());
    
    if (this.config.detailedLogging) {
      Debug.log('BandwidthTester', '带宽测试被取消');
    }
    
    this.testing = false;
  }
  
  /**
   * 设置测试超时
   */
  private setTestTimeout(): void {
    this.timeoutId = window.setTimeout(() => {
      if (this.testing) {
        this.cancelTest();
        this.currentResult.error = '测试超时';
        this.emit('testTimeout', this.getTestResult());
        
        if (this.config.detailedLogging) {
          Debug.log('BandwidthTester', '带宽测试超时');
        }
      }
    }, this.config.timeout);
  }
  
  /**
   * 清除测试超时
   */
  private clearTestTimeout(): void {
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }
  
  /**
   * 重置测试结果
   */
  private resetTestResult(): void {
    this.currentResult = {
      uploadBandwidth: 0,
      downloadBandwidth: 0,
      latency: 0,
      jitter: 0,
      success: false,
      startTime: 0,
      endTime: 0,
      duration: 0,
    };
    
    this.latencyResults = [];
    this.uploadStartTime = 0;
    this.downloadStartTime = 0;
  }
  
  /**
   * 获取测试结果
   * @returns 测试结果
   */
  public getTestResult(): BandwidthTestResult {
    return this.currentResult as BandwidthTestResult;
  }
  
  /**
   * 测试延迟
   */
  private async testLatency(): Promise<void> {
    if (this.config.detailedLogging) {
      Debug.log('BandwidthTester', '开始延迟测试');
    }
    
    // 发送延迟测试开始事件
    this.emit('latencyTestStart');
    
    this.latencyResults = [];
    
    // 进行多次延迟测试
    for (let i = 0; i < this.config.latencyTestCount; i++) {
      try {
        const latency = await this.measureLatency();
        this.latencyResults.push(latency);
        
        // 发送延迟测试进度事件
        this.emit('latencyTestProgress', {
          current: i + 1,
          total: this.config.latencyTestCount,
          latency,
        });
        
        if (this.config.detailedLogging) {
          Debug.log('BandwidthTester', `延迟测试 ${i + 1}/${this.config.latencyTestCount}: ${latency}ms`);
        }
      } catch (error) {
        if (this.config.detailedLogging) {
          Debug.log('BandwidthTester', `延迟测试 ${i + 1}/${this.config.latencyTestCount} 失败: ${error}`);
        }
      }
    }
    
    // 计算延迟统计数据
    if (this.latencyResults.length > 0) {
      // 计算平均延迟
      const sum = this.latencyResults.reduce((acc, val) => acc + val, 0);
      const avg = sum / this.latencyResults.length;
      
      // 计算最小和最大延迟
      const min = Math.min(...this.latencyResults);
      const max = Math.max(...this.latencyResults);
      
      // 计算标准差
      const squaredDiffs = this.latencyResults.map(val => Math.pow(val - avg, 2));
      const avgSquaredDiff = squaredDiffs.reduce((acc, val) => acc + val, 0) / squaredDiffs.length;
      const stdDev = Math.sqrt(avgSquaredDiff);
      
      // 更新测试结果
      this.currentResult.latency = avg;
      this.currentResult.jitter = stdDev;
      this.currentResult.latencyDetails = {
        min,
        max,
        avg,
        stdDev,
        values: [...this.latencyResults],
      };
    }
    
    // 发送延迟测试完成事件
    this.emit('latencyTestComplete', {
      latency: this.currentResult.latency,
      jitter: this.currentResult.jitter,
      details: this.currentResult.latencyDetails,
    });
    
    if (this.config.detailedLogging) {
      Debug.log('BandwidthTester', `延迟测试完成: 平均=${this.currentResult.latency}ms, 抖动=${this.currentResult.jitter}ms`);
    }
  }
  
  /**
   * 测试下载带宽
   */
  private async testDownloadBandwidth(): Promise<void> {
    // 实现下载带宽测试
    // 这里需要根据实际情况实现，可以使用HTTP请求或WebSocket
    // 为简化示例，这里使用模拟数据
    this.downloadStartTime = Date.now();
    
    // 发送下载测试开始事件
    this.emit('downloadTestStart');
    
    if (this.config.detailedLogging) {
      Debug.log('BandwidthTester', '开始下载带宽测试');
    }
    
    // 模拟下载测试
    await new Promise<void>(resolve => {
      setTimeout(() => {
        const downloadTime = Date.now() - this.downloadStartTime;
        const downloadSpeed = this.config.downloadSize / (downloadTime / 1000);
        
        this.currentResult.downloadBandwidth = downloadSpeed;
        this.currentResult.downloadDetails = {
          bytes: this.config.downloadSize,
          time: downloadTime,
          speed: downloadSpeed,
        };
        
        // 发送下载测试完成事件
        this.emit('downloadTestComplete', {
          bandwidth: downloadSpeed,
          details: this.currentResult.downloadDetails,
        });
        
        if (this.config.detailedLogging) {
          Debug.log('BandwidthTester', `下载带宽测试完成: ${downloadSpeed}B/s`);
        }
        
        resolve();
      }, 2000); // 模拟2秒的下载测试
    });
  }
  
  /**
   * 测试上传带宽
   */
  private async testUploadBandwidth(): Promise<void> {
    // 实现上传带宽测试
    // 这里需要根据实际情况实现，可以使用HTTP请求或WebSocket
    // 为简化示例，这里使用模拟数据
    this.uploadStartTime = Date.now();
    
    // 发送上传测试开始事件
    this.emit('uploadTestStart');
    
    if (this.config.detailedLogging) {
      Debug.log('BandwidthTester', '开始上传带宽测试');
    }
    
    // 模拟上传测试
    await new Promise<void>(resolve => {
      setTimeout(() => {
        const uploadTime = Date.now() - this.uploadStartTime;
        const uploadSpeed = this.config.uploadSize / (uploadTime / 1000);
        
        this.currentResult.uploadBandwidth = uploadSpeed;
        this.currentResult.uploadDetails = {
          bytes: this.config.uploadSize,
          time: uploadTime,
          speed: uploadSpeed,
        };
        
        // 发送上传测试完成事件
        this.emit('uploadTestComplete', {
          bandwidth: uploadSpeed,
          details: this.currentResult.uploadDetails,
        });
        
        if (this.config.detailedLogging) {
          Debug.log('BandwidthTester', `上传带宽测试完成: ${uploadSpeed}B/s`);
        }
        
        resolve();
      }, 1500); // 模拟1.5秒的上传测试
    });
  }
  
  /**
   * 测量延迟
   * @returns 延迟（毫秒）
   */
  private async measureLatency(): Promise<number> {
    // 实现延迟测量
    // 这里需要根据实际情况实现，可以使用HTTP请求或WebSocket
    // 为简化示例，这里使用模拟数据
    return new Promise<number>(resolve => {
      const startTime = Date.now();
      
      // 模拟网络请求
      setTimeout(() => {
        const latency = Date.now() - startTime;
        resolve(latency);
      }, 20 + Math.random() * 100); // 模拟20-120ms的延迟
    });
  }
  
  /**
   * 销毁测试器
   */
  public dispose(): void {
    if (this.testing) {
      this.cancelTest();
    }
    
    this.removeAllListeners();
    
    if (this.config.detailedLogging) {
      Debug.log('BandwidthTester', '带宽测试器已销毁');
    }
  }
}
