/**
 * 对象池
 * 用于减少对象创建和销毁的开销，提高性能
 */

/**
 * 对象池接口
 * @template T 对象类型
 */
export interface IObjectPool<T> {
  /**
   * 获取对象
   * @returns 对象实例
   */
  get(): T;

  /**
   * 释放对象
   * @param obj 要释放的对象
   */
  release(obj: T): void;

  /**
   * 清空对象池
   */
  clear(): void;

  /**
   * 获取对象池大小
   * @returns 对象池大小
   */
  size(): number;

  /**
   * 获取活跃对象数量
   * @returns 活跃对象数量
   */
  activeCount(): number;
}

/**
 * 对象池选项
 * @template T 对象类型
 */
export interface ObjectPoolOptions<T> {
  /**
   * 对象创建函数
   */
  create: () => T;

  /**
   * 对象重置函数
   */
  reset?: (obj: T) => void;

  /**
   * 对象销毁函数
   */
  dispose?: (obj: T) => void;

  /**
   * 初始容量
   */
  initialCapacity?: number;

  /**
   * 最大容量
   */
  maxCapacity?: number;

  /**
   * 自动扩容增量
   */
  expandBy?: number;
}

/**
 * 对象池
 * 用于减少对象创建和销毁的开销，提高性能
 * @template T 对象类型
 */
export class ObjectPool<T> implements IObjectPool<T> {
  /** 对象创建函数 */
  private create: () => T;

  /** 对象重置函数 */
  private reset: (obj: T) => void;

  /** 对象销毁函数 */
  private dispose: (obj: T) => void;

  /** 最大容量 */
  private maxCapacity: number;

  /** 自动扩容增量 */
  private expandBy: number;

  /** 对象池 */
  private pool: T[] = [];

  /** 活跃对象集合 */
  private activeObjects: Set<T> = new Set();

  /**
   * 创建对象池
   * @param options 对象池选项
   */
  constructor(options: ObjectPoolOptions<T>) {
    this.create = options.create;
    this.reset = options.reset || ((obj: T) => {});
    this.dispose = options.dispose || ((obj: T) => {});
    this.maxCapacity = options.maxCapacity || Number.MAX_SAFE_INTEGER;
    this.expandBy = options.expandBy || 10;

    // 初始化对象池
    const initialCapacity = options.initialCapacity || 0;
    this.expand(initialCapacity);
  }

  /**
   * 获取对象
   * @returns 对象实例
   */
  public get(): T {
    // 如果池中有可用对象，则返回
    if (this.pool.length > 0) {
      const obj = this.pool.pop()!;
      this.activeObjects.add(obj);
      return obj;
    }

    // 如果池中没有可用对象，且未达到最大容量，则扩容
    if (this.activeObjects.size < this.maxCapacity) {
      this.expand(Math.min(this.expandBy, this.maxCapacity - this.activeObjects.size));
      return this.get();
    }

    // 如果达到最大容量，则创建一个临时对象
    const obj = this.create();
    this.activeObjects.add(obj);
    return obj;
  }

  /**
   * 释放对象
   * @param obj 要释放的对象
   */
  public release(obj: T): void {
    // 如果对象不在活跃集合中，则忽略
    if (!this.activeObjects.has(obj)) {
      return;
    }

    // 从活跃集合中移除
    this.activeObjects.delete(obj);

    // 重置对象
    this.reset(obj);

    // 如果池未满，则加入池中
    if (this.pool.length < this.maxCapacity) {
      this.pool.push(obj);
    } else {
      // 如果池已满，则销毁对象
      this.dispose(obj);
    }
  }

  /**
   * 清空对象池
   */
  public clear(): void {
    // 销毁所有对象
    for (const obj of this.pool) {
      this.dispose(obj);
    }
    for (const obj of this.activeObjects) {
      this.dispose(obj);
    }

    // 清空池和活跃集合
    this.pool = [];
    this.activeObjects.clear();
  }

  /**
   * 获取对象池大小
   * @returns 对象池大小
   */
  public size(): number {
    return this.pool.length;
  }

  /**
   * 获取活跃对象数量
   * @returns 活跃对象数量
   */
  public activeCount(): number {
    return this.activeObjects.size;
  }

  /**
   * 扩容对象池
   * @param count 扩容数量
   */
  private expand(count: number): void {
    for (let i = 0; i < count; i++) {
      this.pool.push(this.create());
    }
  }
}
