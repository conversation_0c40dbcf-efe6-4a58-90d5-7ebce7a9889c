const { spawn } = require('child_process');
const path = require('path');

console.log('开始检查 TypeScript 编译错误...');

const tsc = spawn('npx', ['tsc', '--noEmit', '--listFiles'], {
  cwd: __dirname,
  stdio: ['inherit', 'pipe', 'pipe']
});

let stdout = '';
let stderr = '';

tsc.stdout.on('data', (data) => {
  stdout += data.toString();
});

tsc.stderr.on('data', (data) => {
  stderr += data.toString();
});

tsc.on('close', (code) => {
  console.log(`TypeScript 编译检查完成，退出代码: ${code}`);
  
  if (code !== 0) {
    console.log('=== 编译错误 ===');
    console.log(stderr);
  } else {
    console.log('编译检查通过！');
  }
  
  if (stdout) {
    console.log('=== 输出信息 ===');
    console.log(stdout);
  }
});

tsc.on('error', (error) => {
  console.error('启动 TypeScript 编译器失败:', error);
});
