/**
 * 事务装饰器
 */
import { SetMetadata } from '@nestjs/common';
import { TRANSACTION_PARTICIPANT_METADATA } from './transaction.constants';
import { TransactionParticipantOptions } from './transaction.interface';

/**
 * 事务参与者装饰器
 * 用于标记类为事务参与者
 * @param participantId 参与者ID
 * @param options 参与者选项
 */
export const TransactionParticipantDecorator = (
  participantId: string,
  options?: TransactionParticipantOptions,
) => SetMetadata(TRANSACTION_PARTICIPANT_METADATA, { participantId, options });
