/**
 * 网络安全系统
 * 提供数据加密/解密、用户认证、权限验证等功能
 */
import { System } from '../core/System';
/**
 * 加密算法类型
 */
export declare enum EncryptionAlgorithm {
    /** AES加密 */
    AES = "aes",
    /** RSA加密 */
    RSA = "rsa",
    /** ChaCha20加密 */
    CHACHA20 = "chacha20",
    /** 自定义加密 */
    CUSTOM = "custom"
}
/**
 * 哈希算法类型
 */
export declare enum HashAlgorithm {
    /** MD5哈希 */
    MD5 = "md5",
    /** SHA-1哈希 */
    SHA1 = "sha1",
    /** SHA-256哈希 */
    SHA256 = "sha256",
    /** SHA-512哈希 */
    SHA512 = "sha512",
    /** 自定义哈希 */
    CUSTOM = "custom"
}
/**
 * 网络安全系统配置
 */
export interface NetworkSecuritySystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 默认加密算法 */
    defaultEncryptionAlgorithm?: EncryptionAlgorithm;
    /** 默认哈希算法 */
    defaultHashAlgorithm?: HashAlgorithm;
    /** 是否启用端到端加密 */
    enableEndToEndEncryption?: boolean;
    /** 是否启用安全密钥交换 */
    enableSecureKeyExchange?: boolean;
    /** 是否启用消息签名 */
    enableMessageSigning?: boolean;
    /** 是否启用会话管理 */
    enableSessionManagement?: boolean;
    /** 会话超时时间（毫秒） */
    sessionTimeout?: number;
    /** 是否启用访问控制 */
    enableAccessControl?: boolean;
    /** 是否启用审计日志 */
    enableAuditLog?: boolean;
    /** 是否启用防重放攻击 */
    enableReplayProtection?: boolean;
    /** 是否启用安全令牌 */
    enableSecureTokens?: boolean;
    /** 令牌过期时间（毫秒） */
    tokenExpiration?: number;
    /** 是否启用证书验证 */
    enableCertificateValidation?: boolean;
    /** 证书路径 */
    certificatePath?: string;
}
/**
 * 网络安全系统
 */
export declare class NetworkSecuritySystem extends System {
    /** 系统优先级 */
    static readonly PRIORITY = 150;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 事件发射器 */
    private eventEmitter;
    /** 加密密钥映射 */
    private encryptionKeys;
    /** 会话映射 */
    private sessions;
    /** 令牌映射 */
    private tokens;
    /** 审计日志 */
    private auditLog;
    /** 消息计数器（防重放） */
    private messageCounters;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: NetworkSecuritySystemConfig);
    /**
     * 初始化
     */
    initialize(): void;
    /**
     * 加载证书
     * @param path 证书路径
     */
    private loadCertificate;
    /**
     * 加密数据
     * @param data 要加密的数据
     * @param algorithm 加密算法
     * @param key 加密密钥
     * @returns 加密后的数据
     */
    encryptData(data: any, algorithm?: string, key?: string): string;
    /**
     * 解密数据
     * @param encryptedData 加密的数据
     * @param algorithm 加密算法
     * @param key 加密密钥
     * @returns 解密后的数据
     */
    decryptData(encryptedData: string, algorithm?: string, key?: string): any;
    /**
     * 计算哈希
     * @param data 要哈希的数据
     * @param algorithm 哈希算法
     * @returns 哈希值
     */
    computeHash(data: any, algorithm?: string): string;
    /**
     * 生成签名
     * @param data 要签名的数据
     * @param privateKey 私钥
     * @returns 签名
     */
    generateSignature(data: any, privateKey: string): string;
    /**
     * 验证签名
     * @param data 签名的数据
     * @param signature 签名
     * @param publicKey 公钥
     * @returns 是否有效
     */
    verifySignature(data: any, signature: string, publicKey: string): boolean;
    /**
     * 创建会话
     * @param userId 用户ID
     * @param data 会话数据
     * @returns 会话ID
     */
    createSession(userId: string, data?: any): string;
    /**
     * 验证会话
     * @param sessionId 会话ID
     * @returns 是否有效
     */
    validateSession(sessionId: string): boolean;
    /**
     * 创建令牌
     * @param userId 用户ID
     * @param scope 令牌范围
     * @param expiresIn 过期时间（毫秒）
     * @returns 令牌
     */
    createToken(userId: string, scope?: string[], expiresIn?: number): string;
    /**
     * 验证令牌
     * @param tokenId 令牌ID
     * @param requiredScope 所需范围
     * @returns 是否有效
     */
    validateToken(tokenId: string, requiredScope?: string): boolean;
    /**
     * 获取默认密钥
     * @param algorithm 算法
     * @returns 密钥
     */
    private getDefaultKey;
    /**
     * 生成随机密钥
     * @param algorithm 算法
     * @returns 密钥
     */
    private generateRandomKey;
    /**
     * 生成随机字符串
     * @param length 长度
     * @returns 随机字符串
     */
    private generateRandomString;
    /**
     * 生成随机ID
     * @returns 随机ID
     */
    private generateRandomId;
    /**
     * 记录审计日志
     * @param action 操作
     * @param userId 用户ID
     * @param details 详情
     */
    private logAudit;
    /**
     * AES加密
     * @param data 数据
     * @param key 密钥
     * @returns 加密后的数据
     */
    private encryptAES;
    /**
     * AES解密
     * @param encryptedData 加密的数据
     * @param key 密钥
     * @returns 解密后的数据
     */
    private decryptAES;
    /**
     * RSA加密
     * @param data 数据
     * @param key 密钥
     * @returns 加密后的数据
     */
    private encryptRSA;
    /**
     * RSA解密
     * @param encryptedData 加密的数据
     * @param key 密钥
     * @returns 解密后的数据
     */
    private decryptRSA;
    /**
     * ChaCha20加密
     * @param data 数据
     * @param key 密钥
     * @returns 加密后的数据
     */
    private encryptChaCha20;
    /**
     * ChaCha20解密
     * @param encryptedData 加密的数据
     * @param key 密钥
     * @returns 解密后的数据
     */
    private decryptChaCha20;
    /**
     * MD5哈希
     * @param data 数据
     * @returns 哈希值
     */
    private hashMD5;
    /**
     * SHA-1哈希
     * @param data 数据
     * @returns 哈希值
     */
    private hashSHA1;
    /**
     * SHA-256哈希
     * @param data 数据
     * @returns 哈希值
     */
    private hashSHA256;
    /**
     * SHA-512哈希
     * @param data 数据
     * @returns 哈希值
     */
    private hashSHA512;
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    on(event: string, listener: (...args: any[]) => void): this;
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    off(event: string, listener: (...args: any[]) => void): this;
    /**
     * 销毁
     */
    dispose(): void;
}
