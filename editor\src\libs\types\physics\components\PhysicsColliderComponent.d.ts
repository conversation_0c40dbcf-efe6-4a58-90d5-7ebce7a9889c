/**
 * 物理碰撞器组件
 * 为实体提供碰撞形状
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { Component } from '../../core/Component';
/**
 * 碰撞器类型
 */
export declare enum ColliderType {
    /** 盒体 */
    BOX = "box",
    /** 球体 */
    SPHERE = "sphere",
    /** 圆柱体 */
    CYLINDER = "cylinder",
    /** 胶囊体 */
    CAPSULE = "capsule",
    /** 凸包 */
    CONVEX = "convex",
    /** 三角网格 */
    TRIMESH = "trimesh",
    /** 平面 */
    PLANE = "plane",
    /** 复合 */
    COMPOUND = "compound",
    /** 高度场 */
    HEIGHTFIELD = "heightfield"
}
/**
 * 碰撞形状
 */
export interface ColliderShape {
    /** 碰撞形状 */
    shape: CANNON.Shape;
    /** 位置偏移 */
    offset: CANNON.Vec3;
    /** 旋转偏移 */
    orientation: CANNON.Quaternion;
}
/**
 * 碰撞器选项
 */
export interface ColliderOptions {
    /** 碰撞器类型 */
    type: ColliderType;
    /** 碰撞器参数 */
    params?: any;
    /** 位置偏移 */
    offset?: THREE.Vector3;
    /** 旋转偏移 */
    orientation?: THREE.Quaternion;
    /** 是否触发器 */
    isTrigger?: boolean;
}
/**
 * 物理碰撞器组件
 */
export declare class PhysicsColliderComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 碰撞形状列表 */
    shapes: ColliderShape[];
    /** 碰撞器类型 */
    private colliderType;
    /** 碰撞器参数 */
    private params;
    /** 位置偏移 */
    private offset;
    /** 旋转偏移 */
    private orientation;
    /** 是否触发器 */
    private isTrigger;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /**
     * 创建物理碰撞器组件
     * @param options 碰撞器选项
     */
    constructor(options: ColliderOptions);
    /**
     * 初始化碰撞器
     * @param _world 物理世界（未使用）
     */
    initialize(_world: CANNON.World): void;
    /**
     * 创建碰撞形状
     * @returns 碰撞形状
     */
    private createShape;
    /**
     * 创建盒体形状
     * @param scale 缩放
     * @returns 盒体形状
     */
    private createBoxShape;
    /**
     * 创建球体形状
     * @param scale 缩放
     * @returns 球体形状
     */
    private createSphereShape;
    /**
     * 创建圆柱体形状
     * @param scale 缩放
     * @returns 圆柱体形状
     */
    private createCylinderShape;
    /**
     * 创建胶囊体形状
     * @param scale 缩放
     * @returns 胶囊体形状
     */
    private createCapsuleShape;
    /**
     * 创建凸包形状
     * @returns 凸包形状
     */
    private createConvexShape;
    /**
     * 创建三角网格形状
     * @returns 三角网格形状
     */
    private createTrimeshShape;
    /**
     * 从Three.js几何体创建三角网格形状
     * @param geometry Three.js几何体
     * @returns 三角网格形状
     */
    private createTrimeshFromGeometry;
    /**
     * 创建平面形状
     * @returns 平面形状
     */
    private createPlaneShape;
    /**
     * 创建高度场形状
     * @returns 高度场形状
     */
    private createHeightfieldShape;
    /**
     * 获取碰撞形状列表
     * @returns 碰撞形状列表
     */
    getShapes(): ColliderShape[];
    /**
     * 设置是否触发器
     * @param isTrigger 是否触发器
     */
    setTrigger(isTrigger: boolean): void;
    /**
     * 是否触发器
     * @returns 是否触发器
     */
    isTriggerCollider(): boolean;
    /**
     * 销毁碰撞器
     */
    dispose(): void;
}
