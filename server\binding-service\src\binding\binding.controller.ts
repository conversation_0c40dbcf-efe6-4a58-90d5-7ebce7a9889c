import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Param,
  Body,
  Query,
  HttpException,
  HttpStatus,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiResponse,
} from '@nestjs/swagger';
import {
  BindingService,
  BindingConfig,
  BindingResult,
  BatchBindingResult,
  KnowledgeBaseBinding,
} from './binding.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@ApiTags('数字人知识库绑定')
@Controller('api/digital-humans')
@UseGuards(JwtAuthGuard)
export class BindingController {
  constructor(private readonly bindingService: BindingService) {}

  @Post(':id/knowledge-bases')
  @ApiOperation({ summary: '绑定知识库到数字人' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        knowledgeBaseIds: {
          type: 'array',
          items: { type: 'string' },
          description: '知识库ID列表',
        },
        bindingConfig: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
              enum: ['primary', 'secondary'],
              description: '绑定类型',
            },
            priority: {
              type: 'number',
              description: '优先级',
            },
            searchWeight: {
              type: 'number',
              description: '搜索权重',
            },
            enabledFeatures: {
              type: 'array',
              items: { type: 'string' },
              description: '启用的功能',
            },
          },
        },
      },
      required: ['knowledgeBaseIds'],
    },
  })
  @ApiResponse({
    status: 201,
    description: '绑定成功',
    schema: {
      type: 'object',
      properties: {
        bindingIds: {
          type: 'array',
          items: { type: 'string' },
        },
        message: { type: 'string' },
      },
    },
  })
  async bindKnowledgeBases(
    @Param('id') digitalHumanId: string,
    @Body() body: any,
    @Request() req: any,
  ): Promise<any> {
    const { knowledgeBaseIds, bindingConfig } = body;

    if (!knowledgeBaseIds || knowledgeBaseIds.length === 0) {
      throw new HttpException('知识库ID列表不能为空', HttpStatus.BAD_REQUEST);
    }

    // 从认证用户中获取用户ID
    const userId = req.user?.id || 'system';

    try {
      if (knowledgeBaseIds.length === 1) {
        // 单个绑定
        const config: BindingConfig = {
          type: bindingConfig?.type || 'secondary',
          priority: bindingConfig?.priority || 1,
          searchWeight: bindingConfig?.searchWeight,
          enabledFeatures: bindingConfig?.enabledFeatures,
          customSettings: bindingConfig?.customSettings,
        };

        const result = await this.bindingService.bindKnowledgeBase(
          digitalHumanId,
          knowledgeBaseIds[0],
          config,
          userId,
        );

        return {
          bindingIds: [result.bindingId],
          message: result.message,
        };
      } else {
        // 批量绑定
        const result = await this.bindingService.batchBindKnowledgeBases(
          digitalHumanId,
          knowledgeBaseIds,
          userId,
        );

        return {
          bindingIds: result.successful.map(r => r.bindingId),
          message: `成功绑定 ${result.successCount} 个知识库`,
          details: result,
        };
      }
    } catch (error) {
      throw new HttpException(
        error.message || '绑定失败',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get(':id/knowledge-bases')
  @ApiOperation({ summary: '获取数字人的知识库绑定' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiResponse({
    status: 200,
    description: '知识库绑定列表',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          bindingId: { type: 'string' },
          bindingType: { type: 'string' },
          priority: { type: 'number' },
          config: { type: 'object' },
          knowledgeBase: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              description: { type: 'string' },
              category: { type: 'string' },
              language: { type: 'string' },
              documentCount: { type: 'number' },
              totalChunks: { type: 'number' },
            },
          },
        },
      },
    },
  })
  async getKnowledgeBaseBindings(
    @Param('id') digitalHumanId: string,
  ): Promise<KnowledgeBaseBinding[]> {
    try {
      return await this.bindingService.getDigitalHumanKnowledgeBases(digitalHumanId);
    } catch (error) {
      throw new HttpException(
        error.message || '获取绑定列表失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':dhId/knowledge-bases/:kbId')
  @ApiOperation({ summary: '解绑知识库' })
  @ApiParam({ name: 'dhId', description: '数字人ID' })
  @ApiParam({ name: 'kbId', description: '知识库ID' })
  @ApiResponse({
    status: 200,
    description: '解绑成功',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  async unbindKnowledgeBase(
    @Param('dhId') digitalHumanId: string,
    @Param('kbId') knowledgeBaseId: string,
    @Request() req: any,
  ): Promise<any> {
    const userId = req.user?.id || 'system';

    try {
      await this.bindingService.unbindKnowledgeBase(
        digitalHumanId,
        knowledgeBaseId,
        userId,
      );

      return {
        message: '知识库解绑成功',
      };
    } catch (error) {
      throw new HttpException(
        error.message || '解绑失败',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put(':dhId/knowledge-bases/:kbId')
  @ApiOperation({ summary: '更新绑定配置' })
  @ApiParam({ name: 'dhId', description: '数字人ID' })
  @ApiParam({ name: 'kbId', description: '知识库ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        priority: { type: 'number' },
        isActive: { type: 'boolean' },
        config: { type: 'object' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  async updateBindingConfig(
    @Param('dhId') digitalHumanId: string,
    @Param('kbId') knowledgeBaseId: string,
    @Body() body: any,
    @Request() req: any,
  ): Promise<any> {
    const userId = req.user?.id || 'system';

    try {
      await this.bindingService.updateBindingConfig(
        digitalHumanId,
        knowledgeBaseId,
        body,
        userId,
      );

      return {
        message: '绑定配置更新成功',
      };
    } catch (error) {
      throw new HttpException(
        error.message || '更新失败',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put(':id/knowledge-bases/reorder')
  @ApiOperation({ summary: '重新排序绑定优先级' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        bindingOrders: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              bindingId: { type: 'string' },
              priority: { type: 'number' },
            },
          },
        },
      },
      required: ['bindingOrders'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '重新排序成功',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  async reorderBindings(
    @Param('id') digitalHumanId: string,
    @Body() body: any,
    @Request() req: any,
  ): Promise<any> {
    const { bindingOrders } = body;
    const userId = req.user?.id || 'system';

    if (!bindingOrders || bindingOrders.length === 0) {
      throw new HttpException('绑定排序列表不能为空', HttpStatus.BAD_REQUEST);
    }

    try {
      await this.bindingService.reorderBindings(
        digitalHumanId,
        bindingOrders,
        userId,
      );

      return {
        message: '绑定优先级重新排序成功',
      };
    } catch (error) {
      throw new HttpException(
        error.message || '重新排序失败',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('knowledge-bases/:id/binding-stats')
  @ApiOperation({ summary: '获取知识库的绑定统计' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiResponse({
    status: 200,
    description: '绑定统计信息',
    schema: {
      type: 'object',
      properties: {
        totalBindings: { type: 'number' },
        primaryBindings: { type: 'number' },
        secondaryBindings: { type: 'number' },
      },
    },
  })
  async getKnowledgeBaseBindingStats(
    @Param('id') knowledgeBaseId: string,
  ): Promise<any> {
    try {
      return await this.bindingService.getKnowledgeBaseBindingStats(knowledgeBaseId);
    } catch (error) {
      throw new HttpException(
        error.message || '获取统计信息失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
