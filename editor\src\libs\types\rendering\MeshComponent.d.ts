/**
 * 网格组件
 * 用于渲染3D网格
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 材质类型枚举
 */
export declare enum MaterialType {
    BASIC = "basic",
    STANDARD = "standard",
    PHYSICAL = "physical",
    TOON = "toon",
    MATCAP = "matcap",
    NORMAL = "normal",
    DEPTH = "depth",
    DISTANCE = "distance",
    LAMBERT = "lambert",
    PHONG = "phong",
    SHADER = "shader",
    SPRITE = "sprite",
    POINTS = "points",
    LINE = "line",
    DASH = "dash"
}
/**
 * 材质选项接口
 */
export interface MaterialOptions {
    /** 材质名称 */
    name?: string;
    /** 材质颜色 */
    color?: THREE.Color | string | number;
    /** 材质贴图 */
    map?: THREE.Texture;
    /** 是否透明 */
    transparent?: boolean;
    /** 透明度 */
    opacity?: number;
    /** 是否双面渲染 */
    side?: THREE.Side;
    /** 混合模式 */
    blending?: THREE.Blending;
    /** 是否启用深度测试 */
    depthTest?: boolean;
    /** 是否写入深度缓冲 */
    depthWrite?: boolean;
    /** 是否启用Alpha测试 */
    alphaTest?: number;
    /** 是否启用雾效 */
    fog?: boolean;
    /** 是否启用线框模式 */
    wireframe?: boolean;
}
/**
 * 网格组件配置
 */
export interface MeshComponentOptions {
    /** 几何体 */
    geometry?: THREE.BufferGeometry;
    /** 材质 */
    material?: THREE.Material | THREE.Material[];
    /** 材质类型（如果没有提供材质） */
    materialType?: MaterialType;
    /** 材质选项（如果没有提供材质） */
    materialOptions?: MaterialOptions;
    /** 是否可见 */
    visible?: boolean;
    /** 是否接收阴影 */
    receiveShadow?: boolean;
    /** 是否投射阴影 */
    castShadow?: boolean;
    /** 是否启用视锥体剔除 */
    frustumCulled?: boolean;
    /** 渲染顺序 */
    renderOrder?: number;
    /** 是否启用实例化渲染 */
    instanced?: boolean;
    /** 实例数量 */
    instanceCount?: number;
    /** 是否启用LOD */
    enableLOD?: boolean;
    /** LOD级别 */
    lodLevels?: {
        distance: number;
        geometry: THREE.BufferGeometry;
    }[];
}
/**
 * 网格组件
 * 用于渲染3D网格
 */
export declare class MeshComponent extends Component {
    /** 组件类型 */
    static readonly type = "MeshComponent";
    /** 网格对象 */
    mesh: THREE.Mesh;
    /** 几何体 */
    geometry: THREE.BufferGeometry;
    /** 材质 */
    material: THREE.Material | THREE.Material[];
    /** 是否启用LOD */
    enableLOD: boolean;
    /** LOD对象 */
    lod: THREE.LOD | null;
    /** 事件发射器 */
    events: EventEmitter;
    /** 是否启用实例化渲染 */
    instanced: boolean;
    /** 实例数量 */
    instanceCount: number;
    /**
     * 构造函数
     * @param options 选项
     */
    constructor(options?: MeshComponentOptions);
    /**
     * 设置LOD
     * @param lodLevels LOD级别
     */
    private setupLOD;
    /**
     * 设置材质
     * @param material 材质
     */
    setMaterial(material: THREE.Material | THREE.Material[]): void;
    /**
     * 设置几何体
     * @param geometry 几何体
     */
    setGeometry(geometry: THREE.BufferGeometry): void;
    /**
     * 获取包围盒
     * @param target 目标包围盒
     * @returns 包围盒
     */
    getBoundingBox(target?: THREE.Box3): THREE.Box3;
    /**
     * 获取包围球
     * @param target 目标包围球
     * @returns 包围球
     */
    getBoundingSphere(target?: THREE.Sphere): THREE.Sphere;
}
