/**
 * 角色控制器预设管理器
 * 用于管理角色控制器的预设和模板
 */
import { type EventCallback } from '../../utils/EventEmitter';
import { AdvancedCharacterControllerConfig } from './AdvancedCharacterController';
import { ActionData } from './ActionControlSystem';
/**
 * 控制器预设类型
 */
export declare enum ControllerPresetType {
    /** 基础预设 */
    BASIC = "basic",
    /** 高级预设 */
    ADVANCED = "advanced",
    /** 特殊预设 */
    SPECIAL = "special",
    /** 自定义预设 */
    CUSTOM = "custom",
    /** 第一人称预设 */
    FIRST_PERSON = "first_person",
    /** 第三人称预设 */
    THIRD_PERSON = "third_person",
    /** 飞行预设 */
    FLYING = "flying",
    /** 游泳预设 */
    SWIMMING = "swimming",
    /** 驾驶预设 */
    DRIVING = "driving",
    /** 战斗预设 */
    COMBAT = "combat",
    /** 潜行预设 */
    STEALTH = "stealth",
    /** 攀爬预设 */
    CLIMBING = "climbing",
    /** 跑酷预设 */
    PARKOUR = "parkour",
    /** 舞蹈预设 */
    DANCING = "dancing",
    /** 物理交互预设 */
    PHYSICS_INTERACTION = "physics_interaction",
    /** 环境感知预设 */
    ENVIRONMENT_AWARE = "environment_aware"
}
/**
 * 控制器预设数据
 */
export interface ControllerPresetData {
    /** 预设ID */
    id: string;
    /** 预设名称 */
    name: string;
    /** 预设描述 */
    description: string;
    /** 预设类型 */
    type: ControllerPresetType;
    /** 预设标签 */
    tags: string[];
    /** 控制器配置 */
    config: Partial<AdvancedCharacterControllerConfig>;
    /** 默认动作 */
    defaultActions?: ActionData[];
    /** 预设缩略图 */
    thumbnail?: string;
    /** 作者 */
    author?: string;
    /** 创建时间 */
    createdAt: Date;
    /** 更新时间 */
    updatedAt: Date;
}
/**
 * 控制器模板参数
 */
export interface ControllerTemplateParameter {
    /** 参数ID */
    id: string;
    /** 参数名称 */
    name: string;
    /** 参数描述 */
    description: string;
    /** 参数类型 */
    type: 'number' | 'boolean' | 'string' | 'vector' | 'color' | 'enum';
    /** 默认值 */
    defaultValue: any;
    /** 最小值（数值类型） */
    min?: number;
    /** 最大值（数值类型） */
    max?: number;
    /** 步长（数值类型） */
    step?: number;
    /** 枚举选项（枚举类型） */
    options?: {
        value: any;
        label: string;
    }[];
    /** 是否必需 */
    required?: boolean;
    /** 分组 */
    group?: string;
}
/**
 * 控制器模板数据
 */
export interface ControllerTemplateData {
    /** 模板ID */
    id: string;
    /** 模板名称 */
    name: string;
    /** 模板描述 */
    description: string;
    /** 模板类型 */
    type: ControllerPresetType;
    /** 模板标签 */
    tags: string[];
    /** 基础控制器配置 */
    baseConfig: Partial<AdvancedCharacterControllerConfig>;
    /** 模板参数 */
    parameters: ControllerTemplateParameter[];
    /** 默认动作 */
    defaultActions?: ActionData[];
    /** 模板缩略图 */
    thumbnail?: string;
    /** 作者 */
    author?: string;
    /** 创建时间 */
    createdAt: Date;
    /** 更新时间 */
    updatedAt: Date;
}
/**
 * 角色控制器预设管理器配置
 */
export interface CharacterControllerPresetManagerConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 预设路径 */
    presetsPath?: string;
    /** 是否自动加载预设 */
    autoLoadPresets?: boolean;
    /** 是否使用本地存储 */
    useLocalStorage?: boolean;
}
/**
 * 角色控制器预设管理器
 */
export declare class CharacterControllerPresetManager {
    /** 单例实例 */
    private static instance;
    /** 预设映射 */
    private presets;
    /** 模板映射 */
    private templates;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否已初始化 */
    private initialized;
    /**
     * 构造函数
     * @param config 配置
     */
    private constructor();
    /**
     * 获取单例实例
     * @param config 配置
     * @returns 预设管理器实例
     */
    static getInstance(config?: Partial<CharacterControllerPresetManagerConfig>): CharacterControllerPresetManager;
    /**
     * 初始化
     */
    private initialize;
    /**
     * 加载预设
     */
    private loadPresets;
    /**
     * 加载内置预设
     */
    private loadBuiltInPresets;
    /**
     * 从本地存储加载
     */
    private loadFromLocalStorage;
    /**
     * 从服务器加载
     */
    private loadFromServer;
    /**
     * 添加预设
     * @param preset 预设数据
     */
    addPreset(preset: ControllerPresetData): void;
    /**
     * 添加模板
     * @param template 模板数据
     */
    addTemplate(template: ControllerTemplateData): void;
    /**
     * 获取预设
     * @param id 预设ID
     * @returns 预设数据
     */
    getPreset(id: string): ControllerPresetData | undefined;
    /**
     * 获取模板
     * @param id 模板ID
     * @returns 模板数据
     */
    getTemplate(id: string): ControllerTemplateData | undefined;
    /**
     * 获取所有预设
     * @returns 预设数据数组
     */
    getAllPresets(): ControllerPresetData[];
    /**
     * 获取所有模板
     * @returns 模板数据数组
     */
    getAllTemplates(): ControllerTemplateData[];
    /**
     * 应用模板
     * @param templateId 模板ID
     * @param parameters 参数值
     * @returns 控制器配置
     */
    applyTemplate(templateId: string, parameters?: Record<string, any>): Partial<AdvancedCharacterControllerConfig> | null;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: EventCallback): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: EventCallback): void;
}
