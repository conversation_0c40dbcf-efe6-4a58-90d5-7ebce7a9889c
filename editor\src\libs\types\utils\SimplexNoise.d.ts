/**
 * SimplexNoise 类
 * 实现 Simplex 噪声算法，用于生成自然的随机值
 */
export declare class SimplexNoise {
    private perm;
    private permMod12;
    private static grad3;
    private static F2;
    private static G2;
    private static F3;
    private static G3;
    /**
     * 构造函数
     * @param seed 随机种子
     */
    constructor(seed?: number);
    /**
     * 2D Simplex 噪声
     * @param xin X 坐标
     * @param yin Y 坐标
     * @returns 噪声值 (-1 到 1 范围)
     */
    noise2D(xin: number, yin: number): number;
    /**
     * 3D Simplex 噪声
     * @param xin X 坐标
     * @param yin Y 坐标
     * @param zin Z 坐标
     * @returns 噪声值 (-1 到 1 范围)
     */
    noise3D(xin: number, yin: number, zin: number): number;
    /**
     * 计算 2D 点积
     * @param g 梯度向量
     * @param x X 坐标
     * @param y Y 坐标
     * @returns 点积
     */
    private dot2D;
    /**
     * 计算 3D 点积
     * @param g 梯度向量
     * @param x X 坐标
     * @param y Y 坐标
     * @param z Z 坐标
     * @returns 点积
     */
    private dot3D;
}
