/**
 * InteractionSystem.test.ts
 * 
 * 交互系统单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { World, Entity, Transform } from '../../src/core';
import { 
  InteractionSystem, 
  InteractableComponent, 
  InteractionType,
  InteractionEventComponent,
  InteractionEventType
} from '../../src/interaction';
import { InputSystem } from '../../src/input/InputSystem';
import { Vector3 } from 'three';

describe('交互系统测试', () => {
  // 测试变量
  let world: World;
  let inputSystem: InputSystem;
  let interactionSystem: InteractionSystem;
  let entity: Entity;
  let interactable: InteractableComponent;
  let interactionEvent: InteractionEventComponent;

  // 每个测试前的准备工作
  beforeEach(() => {
    // 创建世界
    world = new World();

    // 创建输入系统
    inputSystem = new InputSystem({
      enableKeyboard: true,
      enableMouse: true
    });
    world.addSystem(inputSystem);

    // 创建交互系统
    interactionSystem = new InteractionSystem(world, {
      debug: false,
      maxInteractionDistance: 5
    });
    world.addSystem(interactionSystem);

    // 创建实体
    entity = new Entity(world);
    entity.addComponent(new Transform({
      position: new Vector3(0, 0, 0)
    }));

    // 创建可交互组件
    interactable = new InteractableComponent(entity, {
      interactionType: InteractionType.CLICK,
      label: '测试对象',
      prompt: '按E键交互',
      interactionDistance: 2
    });
    entity.addComponent(interactable);

    // 创建交互事件组件
    interactionEvent = new InteractionEventComponent(entity);
    entity.addComponent(interactionEvent);

    // 注册组件到交互系统
    interactionSystem.registerInteractableComponent(entity, interactable);
    interactionSystem.registerInteractionEventComponent(entity, interactionEvent);
  });

  // 每个测试后的清理工作
  afterEach(() => {
    // 注销组件
    interactionSystem.unregisterInteractableComponent(entity);
    interactionSystem.unregisterInteractionEventComponent(entity);

    // 销毁组件
    interactable.dispose();
    interactionEvent.dispose();

    // 销毁实体
    entity.dispose();

    // 销毁系统
    interactionSystem.dispose();
    inputSystem.dispose();

    // 销毁世界
    world.dispose();
  });

  // 测试交互系统初始化
  it('应该正确初始化交互系统', () => {
    expect(interactionSystem).toBeDefined();
    expect(world.getSystem(InteractionSystem.NAME)).toBe(interactionSystem);
  });

  // 测试注册和注销组件
  it('应该正确注册和注销组件', () => {
    // 创建新实体
    const newEntity = new Entity(world);
    
    // 创建新组件
    const newInteractable = new InteractableComponent(newEntity, {
      interactionType: InteractionType.CLICK
    });
    
    const newInteractionEvent = new InteractionEventComponent(newEntity);
    
    // 注册组件
    interactionSystem.registerInteractableComponent(newEntity, newInteractable);
    interactionSystem.registerInteractionEventComponent(newEntity, newInteractionEvent);
    
    // 注销组件
    interactionSystem.unregisterInteractableComponent(newEntity);
    interactionSystem.unregisterInteractionEventComponent(newEntity);
    
    // 清理
    newInteractable.dispose();
    newInteractionEvent.dispose();
    newEntity.dispose();
  });

  // 测试交互回调
  it('应该正确触发交互回调', () => {
    // 创建模拟回调
    const mockCallback = vi.fn();
    
    // 设置交互回调
    interactable.onInteract = mockCallback;
    
    // 触发交互
    interactable.interact();
    
    // 验证回调被调用
    expect(mockCallback).toHaveBeenCalledTimes(1);
    expect(mockCallback).toHaveBeenCalledWith(entity);
  });

  // 测试交互事件
  it('应该正确分发交互事件', () => {
    // 创建模拟事件监听器
    const mockListener = vi.fn();
    
    // 添加事件监听器
    interactionEvent.addEventListener(InteractionEventType.INTERACTION_START, mockListener);
    
    // 分发事件
    interactionEvent.handleInteractionStart(entity);
    
    // 验证监听器被调用
    expect(mockListener).toHaveBeenCalledTimes(1);
    expect(mockListener.mock.calls[0][0].type).toBe(InteractionEventType.INTERACTION_START);
    expect(mockListener.mock.calls[0][0].target).toBe(entity);
    
    // 移除事件监听器
    interactionEvent.removeEventListener(InteractionEventType.INTERACTION_START, mockListener);
    
    // 再次分发事件
    interactionEvent.handleInteractionStart(entity);
    
    // 验证监听器没有被再次调用
    expect(mockListener).toHaveBeenCalledTimes(1);
  });

  // 测试交互可见性
  it('应该正确控制交互可见性', () => {
    // 默认可见
    expect(interactable.visible).toBe(true);
    
    // 设置为不可见
    interactable.visible = false;
    expect(interactable.visible).toBe(false);
    
    // 设置为可见
    interactable.visible = true;
    expect(interactable.visible).toBe(true);
  });

  // 测试交互启用状态
  it('应该正确控制交互启用状态', () => {
    // 默认可交互
    expect(interactable.interactive).toBe(true);
    
    // 设置为不可交互
    interactable.interactive = false;
    expect(interactable.interactive).toBe(false);
    
    // 设置为可交互
    interactable.interactive = true;
    expect(interactable.interactive).toBe(true);
  });

  // 测试交互类型
  it('应该正确设置和获取交互类型', () => {
    // 默认类型
    expect(interactable.interactionType).toBe(InteractionType.CLICK);
    
    // 设置为接近交互
    interactable.interactionType = InteractionType.PROXIMITY;
    expect(interactable.interactionType).toBe(InteractionType.PROXIMITY);
    
    // 设置为悬停交互
    interactable.interactionType = InteractionType.HOVER;
    expect(interactable.interactionType).toBe(InteractionType.HOVER);
  });

  // 测试交互距离
  it('应该正确设置和获取交互距离', () => {
    // 默认距离
    expect(interactable.interactionDistance).toBe(2);
    
    // 设置新距离
    interactable.interactionDistance = 3.5;
    expect(interactable.interactionDistance).toBe(3.5);
  });

  // 测试交互标签和提示
  it('应该正确设置和获取交互标签和提示', () => {
    // 默认标签和提示
    expect(interactable.label).toBe('测试对象');
    expect(interactable.prompt).toBe('按E键交互');
    
    // 设置新标签和提示
    interactable.label = '新测试对象';
    interactable.prompt = '按空格键交互';
    expect(interactable.label).toBe('新测试对象');
    expect(interactable.prompt).toBe('按空格键交互');
  });

  // 测试事件历史记录
  it('应该正确记录事件历史', () => {
    // 分发多个事件
    interactionEvent.handleInteractionStart(entity);
    interactionEvent.handleInteractionEnd(entity);
    interactionEvent.handleEnterRange(entity);
    
    // 获取事件历史
    const history = interactionEvent.getEventHistory();
    
    // 验证历史记录
    expect(history.length).toBe(3);
    expect(history[0].type).toBe(InteractionEventType.ENTER_RANGE);
    expect(history[1].type).toBe(InteractionEventType.INTERACTION_END);
    expect(history[2].type).toBe(InteractionEventType.INTERACTION_START);
    
    // 清除历史记录
    interactionEvent.clearEventHistory();
    
    // 验证历史记录已清除
    expect(interactionEvent.getEventHistory().length).toBe(0);
  });

  // 测试系统更新
  it('应该正确更新交互系统', () => {
    // 模拟系统更新
    interactionSystem.update(0.016); // 约60fps
  });
});
