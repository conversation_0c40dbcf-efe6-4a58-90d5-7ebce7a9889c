/**
 * 手势抓取组件
 * 用于处理基于手势的抓取交互
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { GestureType, GestureEventData } from '../../input/devices/GestureDevice';
import { Hand } from './GrabbableComponent';
/**
 * 手势抓取配置
 */
export interface GestureGrabComponentConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 抓取手势类型 */
    grabGestureType?: GestureType;
    /** 释放手势类型 */
    releaseGestureType?: GestureType;
    /** 旋转手势类型 */
    rotateGestureType?: GestureType;
    /** 缩放手势类型 */
    scaleGestureType?: GestureType;
    /** 抓取距离 */
    grabDistance?: number;
    /** 是否使用射线检测 */
    useRaycasting?: boolean;
    /** 是否使用手部追踪 */
    useHandTracking?: boolean;
    /** 是否使用手势预测 */
    useGesturePrediction?: boolean;
    /** 手势灵敏度 */
    gestureSensitivity?: number;
    /** 抓取回调 */
    onGrab?: (entity: Entity, hand: Hand) => void;
    /** 释放回调 */
    onRelease?: (entity: Entity, hand: Hand) => void;
}
/**
 * 手势抓取组件
 */
export declare class GestureGrabComponent extends Component {
    /** 组件类型 */
    static readonly TYPE: string;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否启用 */
    private _enabled;
    /** 抓取手势类型 */
    private _grabGestureType;
    /** 释放手势类型 */
    private _releaseGestureType;
    /** 旋转手势类型 */
    private _rotateGestureType;
    /** 缩放手势类型 */
    private _scaleGestureType;
    /** 抓取距离 */
    private _grabDistance;
    /** 是否使用射线检测 */
    private _useRaycasting;
    /** 是否使用手部追踪 */
    private _useHandTracking;
    /** 是否使用手势预测 */
    private _useGesturePrediction;
    /** 手势灵敏度 */
    private _gestureSensitivity;
    /** 抓取回调 */
    private _onGrab?;
    /** 释放回调 */
    private _onRelease?;
    /** 当前抓取的实体 - 左手 */
    private _leftHandGrabbedEntity?;
    /** 当前抓取的实体 - 右手 */
    private _rightHandGrabbedEntity?;
    /** 是否正在旋转 */
    private _isRotating;
    /** 是否正在缩放 */
    private _isScaling;
    /** 旋转起始角度 */
    private _rotationStart;
    /** 缩放起始值 */
    private _scaleStart;
    /** 手势位置历史 */
    private _gesturePositionHistory;
    /** 手势时间历史 */
    private _gestureTimeHistory;
    /** 手势预测位置 */
    private _predictedPosition?;
    /** 关联的抓取者组件 */
    private _grabberComponent?;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: GestureGrabComponentConfig);
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 处理手势事件
     * @param gestureEvent 手势事件数据
     */
    handleGestureEvent(gestureEvent: GestureEventData): void;
    /**
     * 记录手势位置
     * @param gestureEvent 手势事件数据
     */
    private recordGesturePosition;
    /**
     * 预测下一个位置
     */
    private predictNextPosition;
    /**
     * 处理抓取手势
     * @param gestureEvent 手势事件数据
     */
    private handleGrabGesture;
    /**
     * 处理释放手势
     * @param gestureEvent 手势事件数据
     */
    private handleReleaseGesture;
    /**
     * 处理旋转手势
     * @param gestureEvent 手势事件数据
     */
    private handleRotateGesture;
    /**
     * 处理缩放手势
     * @param gestureEvent 手势事件数据
     */
    private handleScaleGesture;
    /**
     * 应用旋转
     * @param entity 实体
     * @param rotationDelta 旋转角度（弧度）
     */
    private applyRotation;
    /**
     * 应用缩放
     * @param entity 实体
     * @param scaleFactor 缩放因子
     */
    private applyScale;
    /**
     * 确定使用哪只手
     * @param gestureEvent 手势事件数据
     * @returns 手
     */
    private determineHand;
    /**
     * 查找可抓取的实体
     * @param gestureEvent 手势事件数据
     * @returns 可抓取的实体
     */
    private findGrabbableEntity;
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新手部追踪
     */
    private updateHandTracking;
    /**
     * 添加手势事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    addGestureListener(event: string, listener: (...args: any[]) => void): void;
    /**
     * 移除手势事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    removeGestureListener(event: string, listener: (...args: any[]) => void): void;
    /**
     * 销毁组件
     */
    destroy(): void;
}
