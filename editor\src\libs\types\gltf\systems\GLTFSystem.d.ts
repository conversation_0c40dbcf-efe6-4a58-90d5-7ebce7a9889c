/**
 * GLTF系统
 * 用于管理GLTF模型的加载、处理和动画
 */
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { GLTFLoader } from '../GLTFLoader';
import { GLTFExporter } from '../GLTFExporter';
/**
 * GLTF系统选项
 */
export interface GLTFSystemOptions {
    /** 是否自动播放动画 */
    autoPlayAnimations?: boolean;
    /** 是否使用Draco压缩 */
    useDraco?: boolean;
    /** Draco解码器路径 */
    dracoDecoderPath?: string;
    /** 是否使用KTX2纹理 */
    useKTX2?: boolean;
    /** KTX2解码器路径 */
    ktx2DecoderPath?: string;
    /** 是否优化几何体 */
    optimizeGeometry?: boolean;
}
/**
 * GLTF系统
 */
export declare class GLTFSystem extends System {
    /** 系统类型 */
    static readonly type: string;
    /** GLTF加载器 */
    private loader;
    /** GLTF导出器 */
    private exporter;
    /** 是否自动播放动画 */
    private autoPlayAnimations;
    /** 加载中的模型 */
    private loadingModels;
    /** 已加载的模型 */
    private loadedModels;
    /** 实体到URL的映射 */
    private entityToUrl;
    /**
     * 创建GLTF系统
     * @param options GLTF系统选项
     */
    constructor(options?: GLTFSystemOptions);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 处理实体创建事件
     * @param entity 创建的实体
     */
    private handleEntityCreated;
    /**
     * 处理实体移除事件
     * @param entity 移除的实体
     */
    private handleEntityRemoved;
    /**
     * 设置实体的GLTF
     * @param entity 实体
     */
    private setupEntityGLTF;
    /**
     * 加载模型
     * @param url 模型URL
     * @returns Promise，解析为加载的GLTF模型
     */
    loadModel(url: string): Promise<any>;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 导出场景
     * @param scene 场景
     * @param options 导出选项
     * @returns Promise，解析为导出的GLTF数据
     */
    exportScene(scene: any, options?: any): Promise<ArrayBuffer | object>;
    /**
     * 导出实体
     * @param entity 实体
     * @param options 导出选项
     * @returns Promise，解析为导出的GLTF数据
     */
    exportEntity(entity: Entity, options?: any): Promise<ArrayBuffer | object>;
    /**
     * 获取GLTF加载器
     * @returns GLTF加载器
     */
    getLoader(): GLTFLoader;
    /**
     * 获取GLTF导出器
     * @returns GLTF导出器
     */
    getExporter(): GLTFExporter;
    /**
     * 销毁系统
     */
    dispose(): void;
}
