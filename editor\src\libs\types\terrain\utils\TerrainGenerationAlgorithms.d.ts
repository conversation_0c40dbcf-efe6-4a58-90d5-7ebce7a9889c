/**
 * 地形生成算法
 * 包含各种地形生成算法的实现
 */
import { TerrainComponent } from '../components/TerrainComponent';
import { TerrainGenerationWorkerManagerConfig } from '../workers/TerrainGenerationWorkerManager';
import { UndergroundRiverParams } from './UndergroundRiverGenerator';
import { UndergroundLakeParams } from './UndergroundLakeGenerator';
/**
 * 侵蚀类型
 */
export declare enum ErosionType {
    /** 热侵蚀 */
    THERMAL = "thermal",
    /** 水侵蚀 */
    HYDRAULIC = "hydraulic"
}
/**
 * 地形特征类型
 */
export declare enum TerrainFeatureType {
    /** 山脉 */
    MOUNTAIN = "mountain",
    /** 峡谷 */
    CANYON = "canyon",
    /** 河流 */
    RIVER = "river",
    /** 洞穴 */
    CAVE = "cave",
    /** 悬崖 */
    CLIFF = "cliff",
    /** 平原 */
    PLAIN = "plain",
    /** 丘陵 */
    HILLS = "hills",
    /** 沙漠 */
    DESERT = "desert",
    /** 火山 */
    VOLCANO = "volcano",
    /** 岛屿 */
    ISLAND = "island",
    /** 地下河 */
    UNDERGROUND_RIVER = "underground_river",
    /** 地下湖泊 */
    UNDERGROUND_LAKE = "underground_lake"
}
/**
 * 热侵蚀参数
 */
export interface ThermalErosionParams {
    /** 侵蚀迭代次数 */
    iterations: number;
    /** 侵蚀强度 */
    strength: number;
    /** 坡度阈值 */
    slopeThreshold: number;
    /** 沉积系数 */
    depositionRate: number;
}
/**
 * 水侵蚀参数
 */
export interface HydraulicErosionParams {
    /** 侵蚀迭代次数 */
    iterations: number;
    /** 雨滴数量 */
    droplets: number;
    /** 雨滴容量 */
    capacity: number;
    /** 侵蚀强度 */
    erosionStrength: number;
    /** 沉积强度 */
    depositionStrength: number;
    /** 蒸发率 */
    evaporationRate: number;
    /** 惯性 */
    inertia: number;
    /** 最小坡度 */
    minSlope: number;
    /** 重力 */
    gravity: number;
}
/**
 * 河流生成参数
 */
export interface RiverGenerationParams {
    /** 河流数量 */
    count: number;
    /** 河流宽度 */
    width: number;
    /** 河流深度 */
    depth: number;
    /** 河流曲折度 */
    sinuosity: number;
    /** 河流分支概率 */
    branchProbability: number;
    /** 最小长度 */
    minLength: number;
    /** 最大长度 */
    maxLength: number;
    /** 种子 */
    seed: number;
}
/**
 * 山脉生成参数
 */
export interface MountainGenerationParams {
    /** 山脉数量 */
    count: number;
    /** 山脉高度 */
    height: number;
    /** 山脉宽度 */
    width: number;
    /** 山脉粗糙度 */
    roughness: number;
    /** 山脉锐度 */
    sharpness: number;
    /** 种子 */
    seed: number;
}
/**
 * 峡谷生成参数
 */
export interface CanyonGenerationParams {
    /** 峡谷数量 */
    count: number;
    /** 峡谷深度 */
    depth: number;
    /** 峡谷宽度 */
    width: number;
    /** 峡谷曲折度 */
    sinuosity: number;
    /** 峡谷粗糙度 */
    roughness: number;
    /** 种子 */
    seed: number;
}
/**
 * 洞穴生成参数
 */
export interface CaveGenerationParams {
    /** 洞穴数量 */
    count: number;
    /** 洞穴大小 */
    size: number;
    /** 洞穴深度 */
    depth: number;
    /** 洞穴复杂度 */
    complexity: number;
    /** 连接概率 */
    connectionProbability: number;
    /** 随机种子 */
    seed: number;
}
/**
 * 悬崖生成参数
 */
export interface CliffGenerationParams {
    /** 悬崖数量 */
    count: number;
    /** 悬崖高度 */
    height: number;
    /** 悬崖宽度 */
    width: number;
    /** 悬崖陡峭度 */
    steepness: number;
    /** 悬崖粗糙度 */
    roughness: number;
    /** 随机种子 */
    seed: number;
}
/**
 * 火山生成参数
 */
export interface VolcanoGenerationParams {
    /** 火山数量 */
    count: number;
    /** 火山高度 */
    height: number;
    /** 火山半径 */
    radius: number;
    /** 火山口大小 */
    craterSize: number;
    /** 火山口深度 */
    craterDepth: number;
    /** 随机种子 */
    seed: number;
}
/**
 * 地形特征组合生成参数
 */
export interface TerrainFeatureCombinationParams {
    /** 基础地形类型 */
    baseTerrainType: TerrainFeatureType;
    /** 基础地形参数 */
    baseTerrainParams: any;
    /** 特征列表 */
    features: {
        /** 特征类型 */
        type: TerrainFeatureType;
        /** 特征参数 */
        params: any;
        /** 权重 */
        weight: number;
    }[];
    /** 随机种子 */
    seed: number;
}
/**
 * 地形生成算法类
 */
export declare class TerrainGenerationAlgorithms {
    /** 工作线程管理器 */
    private static workerManager;
    /** 是否启用多线程 */
    private static enableMultithreading;
    /** 是否已初始化 */
    private static initialized;
    /**
     * 初始化
     * @param config 配置
     */
    static initialize(config?: TerrainGenerationWorkerManagerConfig): void;
    /**
     * 销毁
     */
    static dispose(): void;
    /**
     * 应用热侵蚀
     * @param terrain 地形组件
     * @param params 热侵蚀参数
     * @param useWorker 是否使用工作线程
     */
    static applyThermalErosion(terrain: TerrainComponent, params: ThermalErosionParams, useWorker?: boolean): Promise<void>;
    /**
     * 应用水侵蚀
     * @param terrain 地形组件
     * @param params 水侵蚀参数
     * @param useWorker 是否使用工作线程
     */
    static applyHydraulicErosion(terrain: TerrainComponent, params: HydraulicErosionParams, useWorker?: boolean): Promise<void>;
    /**
     * 计算梯度
     * @param heightData 高度数据
     * @param resolution 分辨率
     * @param x X坐标
     * @param z Z坐标
     * @param dx X方向
     * @param dz Z方向
     * @returns 梯度
     */
    private static calculateGradient;
    /**
     * 生成河流
     * @param terrain 地形组件
     * @param params 河流生成参数
     * @param useWorker 是否使用工作线程
     */
    static generateRivers(terrain: TerrainComponent, params: RiverGenerationParams, useWorker?: boolean): Promise<void>;
    /**
     * 应用河流到地形
     * @param terrain 地形组件
     * @param riverPaths 河流路径
     * @param width 河流宽度
     * @param depth 河流深度
     */
    private static applyRiversToTerrain;
    /**
     * 生成山脉
     * @param terrain 地形组件
     * @param params 山脉生成参数
     * @param useWorker 是否使用工作线程
     */
    static generateMountains(terrain: TerrainComponent, params: MountainGenerationParams, useWorker?: boolean): Promise<void>;
    /**
     * 应用山脉到地形
     * @param terrain 地形组件
     * @param mountainPath 山脉路径
     * @param height 山脉高度
     * @param width 山脉宽度
     * @param sharpness 山脉锐度
     */
    private static applyMountainToTerrain;
    /**
     * 生成峡谷
     * @param terrain 地形组件
     * @param params 峡谷生成参数
     * @param useWorker 是否使用工作线程
     */
    static generateCanyons(terrain: TerrainComponent, params: CanyonGenerationParams, useWorker?: boolean): Promise<void>;
    /**
     * 应用峡谷到地形
     * @param terrain 地形组件
     * @param canyonPath 峡谷路径
     * @param depth 峡谷深度
     * @param width 峡谷宽度
     * @param roughness 峡谷粗糙度
     * @param seed 种子
     */
    private static applyCanyonToTerrain;
    /**
     * 创建随机数生成器
     * @param seed 种子
     * @returns 随机数生成器函数
     */
    private static createRandomGenerator;
    /**
     * 生成洞穴
     * @param terrain 地形组件
     * @param params 洞穴生成参数
     * @param useWorker 是否使用工作线程
     */
    static generateCaves(terrain: TerrainComponent, params: CaveGenerationParams, useWorker?: boolean): Promise<void>;
    /**
     * 应用洞穴到地形
     * @param terrain 地形组件
     * @param centerX 中心X坐标
     * @param centerZ 中心Z坐标
     * @param radius 半径
     * @param depth 深度
     * @param complexity 复杂度
     * @param random 随机数生成器
     */
    private static applyCaveToTerrain;
    /**
     * 连接两个洞穴
     * @param terrain 地形组件
     * @param cave1 第一个洞穴
     * @param cave2 第二个洞穴
     * @param depth 深度
     * @param complexity 复杂度
     * @param random 随机数生成器
     */
    private static connectCaves;
    /**
     * 生成悬崖
     * @param terrain 地形组件
     * @param params 悬崖生成参数
     * @param useWorker 是否使用工作线程
     */
    static generateCliffs(terrain: TerrainComponent, params: CliffGenerationParams, useWorker?: boolean): Promise<void>;
    /**
     * 应用悬崖到地形
     * @param terrain 地形组件
     * @param cliffPath 悬崖路径
     * @param height 悬崖高度
     * @param width 悬崖宽度
     * @param steepness 悬崖陡峭度
     * @param roughness 悬崖粗糙度
     * @param seed 种子
     */
    private static applyCliffToTerrain;
    /**
     * 生成火山
     * @param terrain 地形组件
     * @param params 火山生成参数
     * @param useWorker 是否使用工作线程
     */
    static generateVolcanoes(terrain: TerrainComponent, params: VolcanoGenerationParams, useWorker?: boolean): Promise<void>;
    /**
     * 生成地形特征组合
     * @param terrain 地形组件
     * @param params 地形特征组合参数
     * @param useWorker 是否使用工作线程
     */
    static generateTerrainFeatureCombination(terrain: TerrainComponent, params: TerrainFeatureCombinationParams, useWorker?: boolean): Promise<void>;
    /**
     * 生成基础地形
     * @param terrain 地形组件
     * @param terrainType 地形类型
     * @param params 参数
     */
    private static generateBaseTerrain;
    /**
     * 应用地形特征
     * @param terrain 地形组件
     * @param featureType 特征类型
     * @param params 参数
     * @param useWorker 是否使用工作线程
     */
    private static applyTerrainFeature;
    /**
     * 生成平坦地形
     * @param terrain 地形组件
     * @param baseHeight 基础高度
     */
    private static generateFlatTerrain;
    /**
     * 生成丘陵地形
     * @param terrain 地形组件
     * @param params 参数
     */
    private static generateHillyTerrain;
    /**
     * 生成沙漠地形
     * @param terrain 地形组件
     * @param params 参数
     */
    private static generateDesertTerrain;
    /**
     * 生成地下河
     * @param terrain 地形组件
     * @param params 地下河生成参数
     */
    static generateUndergroundRivers(terrain: TerrainComponent, params: UndergroundRiverParams): void;
    /**
     * 生成地下湖泊
     * @param terrain 地形组件
     * @param params 地下湖泊生成参数
     */
    static generateUndergroundLakes(terrain: TerrainComponent, params: UndergroundLakeParams): void;
    /**
     * 生成岛屿地形
     * @param terrain 地形组件
     * @param params 参数
     */
    private static generateIslandTerrain;
    /**
     * 简单的Simplex噪声实现
     * @param x X坐标
     * @param y Y坐标
     * @param seed 种子
     * @returns 噪声值
     */
    private static simplexNoise;
}
