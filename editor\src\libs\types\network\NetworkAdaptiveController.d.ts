/**
 * 网络自适应控制器
 * 用于根据网络质量自动调整网络参数
 */
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkQualityData } from './NetworkQualityMonitor';
import { BandwidthController } from './BandwidthController';
import { EntitySyncManager } from './EntitySyncManager';
/**
 * 网络自适应策略
 */
export declare enum AdaptiveStrategy {
    /** 保守策略 - 优先保证稳定性 */
    CONSERVATIVE = "conservative",
    /** 平衡策略 - 平衡稳定性和性能 */
    BALANCED = "balanced",
    /** 激进策略 - 优先保证性能 */
    AGGRESSIVE = "aggressive",
    /** 自动策略 - 根据网络状况自动选择 */
    AUTO = "auto"
}
/**
 * 网络参数配置
 */
export interface NetworkParamsConfig {
    /** 同步间隔（毫秒） */
    syncInterval: number;
    /** 压缩级别（0-9） */
    compressionLevel: number;
    /** 是否使用增量同步 */
    useDeltaSync: boolean;
    /** 是否使用预测 */
    usePrediction: boolean;
    /** 预测时间（毫秒） */
    predictionTime: number;
    /** 是否使用插值 */
    useInterpolation: boolean;
    /** 插值因子（0-1） */
    interpolationFactor: number;
    /** 是否使用优先级同步 */
    usePrioritySync: boolean;
    /** 是否使用空间分区 */
    useSpatialPartitioning: boolean;
    /** 是否使用抖动缓冲 */
    useJitterBuffer: boolean;
    /** 抖动缓冲大小（毫秒） */
    jitterBufferSize: number;
    /** 最大上传带宽（字节/秒） */
    maxUploadBandwidth: number;
    /** 最大下载带宽（字节/秒） */
    maxDownloadBandwidth: number;
}
/**
 * 网络自适应控制器配置
 */
export interface NetworkAdaptiveControllerConfig {
    /** 自适应策略 */
    strategy?: AdaptiveStrategy;
    /** 调整间隔（毫秒） */
    adjustInterval?: number;
    /** 是否启用自动调整 */
    enableAutoAdjust?: boolean;
    /** 是否启用历史记录 */
    enableHistory?: boolean;
    /** 历史记录大小 */
    historySize?: number;
    /** 是否启用调试日志 */
    enableDebugLog?: boolean;
    /** 是否启用平滑过渡 */
    enableSmoothTransition?: boolean;
    /** 平滑因子（0-1） */
    smoothFactor?: number;
    /** 是否启用预测性调整 */
    enablePredictiveAdjustment?: boolean;
    /** 预测窗口大小（毫秒） */
    predictiveWindowSize?: number;
}
/**
 * 网络参数调整记录
 */
export interface NetworkParamsAdjustment {
    /** 时间戳 */
    timestamp: number;
    /** 网络质量 */
    networkQuality: NetworkQualityData;
    /** 调整前参数 */
    beforeParams: NetworkParamsConfig;
    /** 调整后参数 */
    afterParams: NetworkParamsConfig;
    /** 调整原因 */
    reason: string;
}
/**
 * 网络自适应控制器
 * 用于根据网络质量自动调整网络参数
 */
export declare class NetworkAdaptiveController extends EventEmitter {
    /** 配置 */
    private config;
    /** 当前网络参数 */
    private currentParams;
    /** 最近的网络质量数据 */
    private latestNetworkQuality;
    /** 带宽控制器 */
    private bandwidthController;
    /** 实体同步管理器 */
    private entitySyncManager;
    /** 调整定时器ID */
    private adjustTimerId;
    /** 历史记录 */
    private history;
    /** 网络质量历史 */
    private qualityHistory;
    /** 网络质量趋势 */
    private qualityTrend;
    /** 上次调整时间 */
    private lastAdjustTime;
    /** 调整计数器 */
    private adjustCounter;
    /**
     * 创建网络自适应控制器
     * @param initialParams 初始网络参数
     * @param config 配置
     */
    constructor(initialParams: NetworkParamsConfig, config?: NetworkAdaptiveControllerConfig);
    /**
     * 设置带宽控制器
     * @param controller 带宽控制器
     */
    setBandwidthController(controller: BandwidthController): void;
    /**
     * 设置实体同步管理器
     * @param manager 实体同步管理器
     */
    setEntitySyncManager(manager: EntitySyncManager): void;
    /**
     * 设置网络质量数据
     * @param quality 网络质量数据
     */
    setNetworkQuality(quality: NetworkQualityData): void;
    /**
     * 启动自动调整
     */
    startAutoAdjust(): void;
    /**
     * 停止自动调整
     */
    stopAutoAdjust(): void;
    /**
     * 调整网络参数
     */
    private adjustNetworkParams;
    /**
     * 应用网络参数
     */
    private applyNetworkParams;
    /**
     * 保守策略调整
     * @returns 调整原因
     */
    private adjustConservative;
    /**
     * 平衡策略调整
     * @returns 调整原因
     */
    private adjustBalanced;
    /**
     * 激进策略调整
     * @returns 调整原因
     */
    private adjustAggressive;
    /**
     * 自动策略调整
     * @returns 调整原因
     */
    private adjustAuto;
    /**
     * 平滑调整数值
     * @param currentValue 当前值
     * @param targetValue 目标值
     * @returns 调整后的值
     */
    private smoothAdjust;
    /**
     * 更新网络质量趋势
     */
    private updateQualityTrend;
    /**
     * 计算网络质量分数
     * @param quality 网络质量数据
     * @returns 质量分数（越高越好）
     */
    private calculateQualityScore;
    /**
     * 检查是否需要立即调整
     * @returns 是否需要立即调整
     */
    private shouldAdjustImmediately;
    /**
     * 获取当前网络参数
     * @returns 当前网络参数
     */
    getCurrentParams(): NetworkParamsConfig;
    /**
     * 获取调整历史记录
     * @returns 调整历史记录
     */
    getAdjustmentHistory(): NetworkParamsAdjustment[];
    /**
     * 获取网络质量趋势
     * @returns 网络质量趋势
     */
    getQualityTrend(): 'improving' | 'stable' | 'degrading';
    /**
     * 设置自适应策略
     * @param strategy 自适应策略
     */
    setStrategy(strategy: AdaptiveStrategy): void;
    /**
     * 手动设置网络参数
     * @param params 网络参数
     */
    setNetworkParams(params: Partial<NetworkParamsConfig>): void;
    /**
     * 重置为默认参数
     */
    resetToDefaults(): void;
    /**
     * 销毁控制器
     */
    dispose(): void;
}
