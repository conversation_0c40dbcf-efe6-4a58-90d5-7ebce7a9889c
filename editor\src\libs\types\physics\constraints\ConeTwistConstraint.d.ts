/**
 * 圆锥扭转约束
 * 限制两个物体之间的相对旋转，类似于万向节
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { PhysicsConstraint } from './PhysicsConstraint';
/**
 * 圆锥扭转约束选项
 */
export interface ConeTwistConstraintOptions {
    /** 源物体上的连接点（局部坐标） */
    pivotA?: THREE.Vector3;
    /** 目标物体上的连接点（局部坐标） */
    pivotB?: THREE.Vector3;
    /** 源物体上的轴（局部坐标） */
    axisA?: THREE.Vector3;
    /** 目标物体上的轴（局部坐标） */
    axisB?: THREE.Vector3;
    /** 最大力 */
    maxForce?: number;
    /** 是否允许连接的物体之间碰撞 */
    collideConnected?: boolean;
    /** 圆锥角度（弧度） */
    angle?: number;
    /** 扭转角度（弧度） */
    twistAngle?: number;
}
/**
 * 圆锥扭转约束
 */
export declare class ConeTwistConstraint extends PhysicsConstraint {
    /** 组件类型 */
    static readonly type: string;
    /** 源物体上的连接点（局部坐标） */
    private pivotA;
    /** 目标物体上的连接点（局部坐标） */
    private pivotB;
    /** 源物体上的轴（局部坐标） */
    private axisA;
    /** 目标物体上的轴（局部坐标） */
    private axisB;
    /** 最大力 */
    private maxForce;
    /** 圆锥角度（弧度） */
    private angle;
    /** 扭转角度（弧度） */
    private twistAngle;
    /**
     * 创建圆锥扭转约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    constructor(targetEntity?: Entity | null, options?: ConeTwistConstraintOptions);
    /**
     * 创建约束
     */
    protected createConstraint(): void;
    /**
     * 设置源物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    setPivotA(pivot: THREE.Vector3): void;
    /**
     * 获取源物体上的连接点
     * @returns 连接点（局部坐标）
     */
    getPivotA(): THREE.Vector3;
    /**
     * 设置目标物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    setPivotB(pivot: THREE.Vector3): void;
    /**
     * 获取目标物体上的连接点
     * @returns 连接点（局部坐标）
     */
    getPivotB(): THREE.Vector3;
    /**
     * 设置源物体上的轴
     * @param axis 轴（局部坐标）
     */
    setAxisA(axis: THREE.Vector3): void;
    /**
     * 获取源物体上的轴
     * @returns 轴（局部坐标）
     */
    getAxisA(): THREE.Vector3;
    /**
     * 设置目标物体上的轴
     * @param axis 轴（局部坐标）
     */
    setAxisB(axis: THREE.Vector3): void;
    /**
     * 获取目标物体上的轴
     * @returns 轴（局部坐标）
     */
    getAxisB(): THREE.Vector3;
    /**
     * 设置圆锥角度
     * @param angle 角度（弧度）
     */
    setAngle(angle: number): void;
    /**
     * 获取圆锥角度
     * @returns 角度（弧度）
     */
    getAngle(): number;
    /**
     * 设置扭转角度
     * @param angle 角度（弧度）
     */
    setTwistAngle(angle: number): void;
    /**
     * 获取扭转角度
     * @returns 角度（弧度）
     */
    getTwistAngle(): number;
    /**
     * 设置最大力
     * @param maxForce 最大力
     */
    setMaxForce(maxForce: number): void;
    /**
     * 获取最大力
     * @returns 最大力
     */
    getMaxForce(): number;
    /**
     * 重新创建约束
     */
    private recreateConstraint;
}
