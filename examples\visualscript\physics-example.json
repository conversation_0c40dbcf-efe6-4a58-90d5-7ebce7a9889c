{"id": "physics-example", "name": "物理节点示例", "description": "展示如何使用视觉脚本系统中的物理节点", "version": "1.0.0", "nodes": [{"id": "start-node", "type": "core/events/onStart", "position": {"x": 100, "y": 100}, "data": {"label": "开始"}, "flows": {"flow": {"nodeId": "create-floor", "socket": "flow"}}}, {"id": "create-floor", "type": "entity/create", "position": {"x": 300, "y": 100}, "data": {"label": "创建地面"}, "parameters": {"name": {"value": "地面"}}, "flows": {"flow": {"nodeId": "add-floor-mesh", "socket": "flow"}}}, {"id": "add-floor-mesh", "type": "entity/component/add", "position": {"x": 500, "y": 100}, "data": {"label": "添加地面网格"}, "parameters": {"componentType": {"value": "MeshComponent"}, "meshType": {"value": "plane"}, "width": {"value": 10}, "height": {"value": 10}}, "inputs": {"entity": {"nodeId": "create-floor", "socket": "entity"}}, "flows": {"flow": {"nodeId": "add-floor-physics", "socket": "flow"}}}, {"id": "add-floor-physics", "type": "entity/component/add", "position": {"x": 700, "y": 100}, "data": {"label": "添加地面物理组件"}, "parameters": {"componentType": {"value": "PhysicsBodyComponent"}, "bodyType": {"value": "static"}}, "inputs": {"entity": {"nodeId": "create-floor", "socket": "entity"}}, "flows": {"flow": {"nodeId": "add-floor-collider", "socket": "flow"}}}, {"id": "add-floor-collider", "type": "entity/component/add", "position": {"x": 900, "y": 100}, "data": {"label": "添加地面碰撞体"}, "parameters": {"componentType": {"value": "PhysicsColliderComponent"}, "colliderType": {"value": "box"}, "size": {"value": {"x": 10, "y": 0.1, "z": 10}}}, "inputs": {"entity": {"nodeId": "create-floor", "socket": "entity"}}, "flows": {"flow": {"nodeId": "create-box", "socket": "flow"}}}, {"id": "create-box", "type": "entity/create", "position": {"x": 300, "y": 300}, "data": {"label": "创建盒子"}, "parameters": {"name": {"value": "盒子"}}, "flows": {"flow": {"nodeId": "add-box-mesh", "socket": "flow"}}}, {"id": "add-box-mesh", "type": "entity/component/add", "position": {"x": 500, "y": 300}, "data": {"label": "添加盒子网格"}, "parameters": {"componentType": {"value": "MeshComponent"}, "meshType": {"value": "box"}, "size": {"value": 1}}, "inputs": {"entity": {"nodeId": "create-box", "socket": "entity"}}, "flows": {"flow": {"nodeId": "add-box-transform", "socket": "flow"}}}, {"id": "add-box-transform", "type": "entity/component/add", "position": {"x": 700, "y": 300}, "data": {"label": "添加盒子变换"}, "parameters": {"componentType": {"value": "TransformComponent"}, "position": {"value": {"x": 0, "y": 5, "z": 0}}}, "inputs": {"entity": {"nodeId": "create-box", "socket": "entity"}}, "flows": {"flow": {"nodeId": "add-box-physics", "socket": "flow"}}}, {"id": "add-box-physics", "type": "entity/component/add", "position": {"x": 900, "y": 300}, "data": {"label": "添加盒子物理组件"}, "parameters": {"componentType": {"value": "PhysicsBodyComponent"}, "bodyType": {"value": "dynamic"}, "mass": {"value": 1}}, "inputs": {"entity": {"nodeId": "create-box", "socket": "entity"}}, "flows": {"flow": {"nodeId": "add-box-collider", "socket": "flow"}}}, {"id": "add-box-collider", "type": "entity/component/add", "position": {"x": 1100, "y": 300}, "data": {"label": "添加盒子碰撞体"}, "parameters": {"componentType": {"value": "PhysicsColliderComponent"}, "colliderType": {"value": "box"}, "size": {"value": {"x": 1, "y": 1, "z": 1}}}, "inputs": {"entity": {"nodeId": "create-box", "socket": "entity"}}, "flows": {"flow": {"nodeId": "create-cloth", "socket": "flow"}}}, {"id": "create-cloth", "type": "physics/softbody/create<PERSON>loth", "position": {"x": 300, "y": 500}, "data": {"label": "创建布料"}, "parameters": {"width": {"value": 2}, "height": {"value": 2}, "segments": {"value": 10}, "position": {"value": {"x": 2, "y": 3, "z": 0}}, "fixedCorners": {"value": true}}, "flows": {"flow": {"nodeId": "create-rope", "socket": "flow"}}}, {"id": "create-rope", "type": "physics/softbody/createRope", "position": {"x": 500, "y": 500}, "data": {"label": "创建绳索"}, "parameters": {"start": {"value": {"x": -2, "y": 5, "z": 0}}, "end": {"value": {"x": -2, "y": 0, "z": 0}}, "segments": {"value": 10}, "fixedEnds": {"value": true}}, "flows": {"flow": {"nodeId": "setup-raycast", "socket": "flow"}}}, {"id": "setup-raycast", "type": "core/events/onUpdate", "position": {"x": 100, "y": 700}, "data": {"label": "设置射线检测"}, "flows": {"flow": {"nodeId": "perform-raycast", "socket": "flow"}}}, {"id": "perform-raycast", "type": "physics/raycast", "position": {"x": 300, "y": 700}, "data": {"label": "执行射线检测"}, "parameters": {"origin": {"value": {"x": 0, "y": 10, "z": 0}}, "direction": {"value": {"x": 0, "y": -1, "z": 0}}, "maxDistance": {"value": 20}}, "flows": {"flow": {"nodeId": "check-hit", "socket": "flow"}}}, {"id": "check-hit", "type": "core/flow/branch", "position": {"x": 500, "y": 700}, "data": {"label": "检查是否命中"}, "inputs": {"condition": {"nodeId": "perform-raycast", "socket": "hit"}}, "flows": {"true": {"nodeId": "debug-hit", "socket": "flow"}, "false": {"nodeId": null, "socket": null}}}, {"id": "debug-hit", "type": "core/debug/print", "position": {"x": 700, "y": 700}, "data": {"label": "输出命中信息"}, "parameters": {"message": {"value": "射线命中物体，距离: "}}, "inputs": {"value": {"nodeId": "perform-raycast", "socket": "hitDistance"}}}], "variables": [], "customEvents": []}