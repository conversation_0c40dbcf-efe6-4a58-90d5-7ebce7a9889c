/**
 * 相机系统兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';
import * as THREE from 'three';

/**
 * 相机系统兼容性测试
 */
export const cameraSystemTest: TestCase = {
  name: '相机系统兼容性测试',
  description: '测试相机系统功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目相机系统实例
      const originalCameraSystem = new original.CameraSystem();
      
      // 创建重构后项目相机系统实例
      const refactoredCameraSystem = new refactored.CameraSystem();
      
      // 检查相机系统实例是否创建成功
      if (!originalCameraSystem || !refactoredCameraSystem) {
        return {
          name: '相机系统兼容性测试',
          passed: false,
          errorMessage: '相机系统实例创建失败'
        };
      }
      
      // 初始化相机系统
      originalCameraSystem.initialize();
      refactoredCameraSystem.initialize();
      
      // 创建透视相机
      const originalPerspectiveCamera = originalCameraSystem.createPerspectiveCamera({
        fov: 75,
        aspect: 800 / 600,
        near: 0.1,
        far: 1000,
        position: new THREE.Vector3(0, 0, 5)
      });
      
      const refactoredPerspectiveCamera = refactoredCameraSystem.createPerspectiveCamera({
        fov: 75,
        aspect: 800 / 600,
        near: 0.1,
        far: 1000,
        position: new THREE.Vector3(0, 0, 5)
      });
      
      // 检查透视相机是否创建成功
      if (!originalPerspectiveCamera || !refactoredPerspectiveCamera) {
        return {
          name: '相机系统兼容性测试',
          passed: false,
          errorMessage: '透视相机创建失败',
          details: {
            originalPerspectiveCamera,
            refactoredPerspectiveCamera
          }
        };
      }
      
      // 检查透视相机属性
      if (
        originalPerspectiveCamera.fov !== refactoredPerspectiveCamera.fov ||
        originalPerspectiveCamera.aspect !== refactoredPerspectiveCamera.aspect ||
        originalPerspectiveCamera.near !== refactoredPerspectiveCamera.near ||
        originalPerspectiveCamera.far !== refactoredPerspectiveCamera.far
      ) {
        return {
          name: '相机系统兼容性测试',
          passed: false,
          errorMessage: '透视相机属性不一致',
          details: {
            originalFov: originalPerspectiveCamera.fov,
            refactoredFov: refactoredPerspectiveCamera.fov,
            originalAspect: originalPerspectiveCamera.aspect,
            refactoredAspect: refactoredPerspectiveCamera.aspect,
            originalNear: originalPerspectiveCamera.near,
            refactoredNear: refactoredPerspectiveCamera.near,
            originalFar: originalPerspectiveCamera.far,
            refactoredFar: refactoredPerspectiveCamera.far
          }
        };
      }
      
      // 检查透视相机位置
      if (
        originalPerspectiveCamera.position.x !== refactoredPerspectiveCamera.position.x ||
        originalPerspectiveCamera.position.y !== refactoredPerspectiveCamera.position.y ||
        originalPerspectiveCamera.position.z !== refactoredPerspectiveCamera.position.z
      ) {
        return {
          name: '相机系统兼容性测试',
          passed: false,
          errorMessage: '透视相机位置不一致',
          details: {
            originalPosition: originalPerspectiveCamera.position,
            refactoredPosition: refactoredPerspectiveCamera.position
          }
        };
      }
      
      // 创建正交相机
      const originalOrthographicCamera = originalCameraSystem.createOrthographicCamera({
        left: -5,
        right: 5,
        top: 5,
        bottom: -5,
        near: 0.1,
        far: 1000,
        position: new THREE.Vector3(0, 0, 5)
      });
      
      const refactoredOrthographicCamera = refactoredCameraSystem.createOrthographicCamera({
        left: -5,
        right: 5,
        top: 5,
        bottom: -5,
        near: 0.1,
        far: 1000,
        position: new THREE.Vector3(0, 0, 5)
      });
      
      // 检查正交相机是否创建成功
      if (!originalOrthographicCamera || !refactoredOrthographicCamera) {
        return {
          name: '相机系统兼容性测试',
          passed: false,
          errorMessage: '正交相机创建失败',
          details: {
            originalOrthographicCamera,
            refactoredOrthographicCamera
          }
        };
      }
      
      // 检查正交相机属性
      if (
        originalOrthographicCamera.left !== refactoredOrthographicCamera.left ||
        originalOrthographicCamera.right !== refactoredOrthographicCamera.right ||
        originalOrthographicCamera.top !== refactoredOrthographicCamera.top ||
        originalOrthographicCamera.bottom !== refactoredOrthographicCamera.bottom ||
        originalOrthographicCamera.near !== refactoredOrthographicCamera.near ||
        originalOrthographicCamera.far !== refactoredOrthographicCamera.far
      ) {
        return {
          name: '相机系统兼容性测试',
          passed: false,
          errorMessage: '正交相机属性不一致',
          details: {
            originalLeft: originalOrthographicCamera.left,
            refactoredLeft: refactoredOrthographicCamera.left,
            originalRight: originalOrthographicCamera.right,
            refactoredRight: refactoredOrthographicCamera.right,
            originalTop: originalOrthographicCamera.top,
            refactoredTop: refactoredOrthographicCamera.top,
            originalBottom: originalOrthographicCamera.bottom,
            refactoredBottom: refactoredOrthographicCamera.bottom,
            originalNear: originalOrthographicCamera.near,
            refactoredNear: refactoredOrthographicCamera.near,
            originalFar: originalOrthographicCamera.far,
            refactoredFar: refactoredOrthographicCamera.far
          }
        };
      }
      
      // 测试相机控制器
      const originalControls = originalCameraSystem.createOrbitControls(originalPerspectiveCamera, document.createElement('div'));
      const refactoredControls = refactoredCameraSystem.createOrbitControls(refactoredPerspectiveCamera, document.createElement('div'));
      
      // 检查相机控制器是否创建成功
      if (!originalControls || !refactoredControls) {
        return {
          name: '相机系统兼容性测试',
          passed: false,
          errorMessage: '相机控制器创建失败',
          details: {
            originalControls,
            refactoredControls
          }
        };
      }
      
      // 销毁相机系统
      originalCameraSystem.dispose();
      refactoredCameraSystem.dispose();
      
      return {
        name: '相机系统兼容性测试',
        passed: true,
        details: {
          originalPerspectiveCamera,
          refactoredPerspectiveCamera,
          originalOrthographicCamera,
          refactoredOrthographicCamera
        }
      };
    } catch (error) {
      return {
        name: '相机系统兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
