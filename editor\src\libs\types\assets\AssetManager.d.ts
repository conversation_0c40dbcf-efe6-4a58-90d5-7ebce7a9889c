/**
 * 资产管理器类
 * 负责加载和管理资产
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetLoader } from './AssetLoader';
import { ResourceManager, AssetType } from './ResourceManager';
import { ResourceDependencyManager, DependencyType } from './ResourceDependencyManager';
export interface AssetInfo {
    /** 资产ID */
    id: string;
    /** 资产名称 */
    name: string;
    /** 资产类型 */
    type: AssetType;
    /** 资产URL */
    url: string;
    /** 资产数据 */
    data?: any;
    /** 资产元数据 */
    metadata?: Record<string, any>;
    /** 是否已加载 */
    loaded: boolean;
    /** 加载错误 */
    error?: Error;
}
export interface AssetManagerOptions {
    /** 基础URL */
    baseUrl?: string;
    /** 资源管理器选项 */
    resourceOptions?: any;
    /** 是否启用依赖管理 */
    enableDependencyManagement?: boolean;
}
export declare class AssetManager extends EventEmitter {
    /** 资产加载器 */
    private loader;
    /** 资源管理器 */
    private resourceManager;
    /** 资源依赖管理器 */
    private dependencyManager;
    /** 资产映射 */
    private assets;
    /** 基础URL */
    private baseUrl;
    /** 是否启用依赖管理 */
    private enableDependencyManagement;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建资产管理器实例
     * @param options 资产管理器选项
     */
    constructor(options?: AssetManagerOptions);
    /**
     * 初始化资产管理器
     */
    initialize(): void;
    /**
     * 注册资产
     * @param id 资产ID
     * @param name 资产名称
     * @param type 资产类型
     * @param url 资产URL
     * @param metadata 资产元数据
     * @returns 资产信息
     */
    registerAsset(id: string, name: string, type: AssetType, url: string, metadata?: Record<string, any>): AssetInfo;
    /**
     * 加载资产
     * @param id 资产ID
     * @param loadDependencies 是否加载依赖
     * @returns Promise，解析为资产数据
     */
    loadAsset(id: string, loadDependencies?: boolean): Promise<any>;
    /**
     * 加载资产依赖
     * @param id 资产ID
     * @returns Promise
     */
    private loadDependencies;
    /**
     * 批量加载资产
     * @param ids 资产ID数组
     * @param onProgress 进度回调
     * @returns Promise，解析为资产数据映射
     */
    loadAssets(ids: string[], onProgress?: (loaded: number, total: number) => void): Promise<Map<string, any>>;
    /**
     * 卸载资产
     * @param id 资产ID
     * @param unloadDependents 是否卸载依赖于此资产的资产
     * @returns 是否成功卸载
     */
    unloadAsset(id: string, unloadDependents?: boolean): boolean;
    /**
     * 卸载依赖于指定资产的资产
     * @param id 资产ID
     */
    private unloadDependents;
    /**
     * 获取资产
     * @param id 资产ID
     * @returns 资产数据，如果未加载则返回null
     */
    getAsset(id: string): any;
    /**
     * 获取资产信息
     * @param id 资产ID
     * @returns 资产信息，如果不存在则返回null
     */
    getAssetInfo(id: string): AssetInfo | null;
    /**
     * 获取所有资产信息
     * @returns 资产信息数组
     */
    getAllAssetInfo(): AssetInfo[];
    /**
     * 根据类型获取资产信息
     * @param type 资产类型
     * @returns 资产信息数组
     */
    getAssetInfoByType(type: AssetType): AssetInfo[];
    /**
     * 根据名称查找资产信息
     * @param name 资产名称
     * @returns 资产信息数组
     */
    findAssetInfoByName(name: string): AssetInfo[];
    /**
     * 移除资产
     * @param id 资产ID
     * @returns 是否成功移除
     */
    removeAsset(id: string): boolean;
    /**
     * 设置基础URL
     * @param baseUrl 基础URL
     */
    setBaseUrl(baseUrl: string): void;
    /**
     * 获取基础URL
     * @returns 基础URL
     */
    getBaseUrl(): string;
    /**
     * 解析URL
     * @param url 相对URL
     * @returns 完整URL
     */
    private resolveUrl;
    /**
     * 获取资源管理器
     * @returns 资源管理器
     */
    getResourceManager(): ResourceManager;
    /**
     * 获取资源依赖管理器
     * @returns 资源依赖管理器
     */
    getDependencyManager(): ResourceDependencyManager;
    /**
     * 获取资产加载器
     * @returns 资产加载器
     */
    getLoader(): AssetLoader;
    /**
     * 添加资产依赖关系
     * @param resourceId 资产ID
     * @param dependencyId 依赖资产ID
     * @param type 依赖类型
     * @returns 是否成功添加
     */
    addDependency(resourceId: string, dependencyId: string, type?: DependencyType): boolean;
    /**
     * 移除资产依赖关系
     * @param resourceId 资产ID
     * @param dependencyId 依赖资产ID
     * @returns 是否成功移除
     */
    removeDependency(resourceId: string, dependencyId: string): boolean;
    /**
     * 获取资产的依赖
     * @param id 资产ID
     * @returns 依赖资产ID数组
     */
    getDependencies(id: string): string[];
    /**
     * 获取依赖于资产的资产
     * @param id 资产ID
     * @returns 依赖于此资产的资产ID数组
     */
    getDependents(id: string): string[];
    /**
     * 清空所有资产
     */
    clear(): void;
    /**
     * 销毁资产管理器
     */
    dispose(): void;
}
