/**
 * 输入动作单元测试
 */
import { ButtonInputAction, ValueInputAction, VectorInputAction, InputActionType } from '../../src/input/InputAction';

describe('InputAction', () => {
  describe('ButtonInputAction', () => {
    let action: ButtonInputAction;

    beforeEach(() => {
      action = new ButtonInputAction('test');
    });

    test('应该正确初始化按钮动作', () => {
      expect(action.getName()).toBe('test');
      expect(action.getType()).toBe(InputActionType.BUTTON);
      expect(action.getValue()).toBe(false);
      expect(action.isPressed()).toBe(false);
      expect(action.isJustPressed()).toBe(false);
      expect(action.isJustReleased()).toBe(false);
    });

    test('应该正确更新按钮状态', () => {
      // 按下按钮
      action.update(true);
      expect(action.getValue()).toBe(true);
      expect(action.isPressed()).toBe(true);
      expect(action.isJustPressed()).toBe(true);
      expect(action.isJustReleased()).toBe(false);
      expect(action.hasChanged()).toBe(true);

      // 重置状态
      action.reset();
      expect(action.hasChanged()).toBe(false);
      expect(action.isJustPressed()).toBe(false);
      expect(action.isJustReleased()).toBe(false);
      expect(action.isPressed()).toBe(true);

      // 释放按钮
      action.update(false);
      expect(action.getValue()).toBe(false);
      expect(action.isPressed()).toBe(false);
      expect(action.isJustPressed()).toBe(false);
      expect(action.isJustReleased()).toBe(true);
      expect(action.hasChanged()).toBe(true);

      // 重置状态
      action.reset();
      expect(action.hasChanged()).toBe(false);
      expect(action.isJustPressed()).toBe(false);
      expect(action.isJustReleased()).toBe(false);
      expect(action.isPressed()).toBe(false);
    });
  });

  describe('ValueInputAction', () => {
    let action: ValueInputAction;

    beforeEach(() => {
      action = new ValueInputAction('test');
    });

    test('应该正确初始化值动作', () => {
      expect(action.getName()).toBe('test');
      expect(action.getType()).toBe(InputActionType.VALUE);
      expect(action.getValue()).toBe(0);
    });

    test('应该正确更新值', () => {
      action.update(0.5);
      expect(action.getValue()).toBe(0.5);
      expect(action.hasChanged()).toBe(true);

      action.reset();
      expect(action.hasChanged()).toBe(false);
      expect(action.getValue()).toBe(0.5);

      action.update(0.5);
      expect(action.hasChanged()).toBe(false);

      action.update(0.8);
      expect(action.getValue()).toBe(0.8);
      expect(action.hasChanged()).toBe(true);
    });
  });

  describe('VectorInputAction', () => {
    let action: VectorInputAction;

    beforeEach(() => {
      action = new VectorInputAction('test');
    });

    test('应该正确初始化向量动作', () => {
      expect(action.getName()).toBe('test');
      expect(action.getType()).toBe(InputActionType.VECTOR);
      expect(action.getValue()).toEqual([0, 0]);
      expect(action.getX()).toBe(0);
      expect(action.getY()).toBe(0);
      expect(action.getLength()).toBe(0);
    });

    test('应该正确更新向量', () => {
      action.update([0.5, 0.5]);
      expect(action.getValue()).toEqual([0.5, 0.5]);
      expect(action.getX()).toBe(0.5);
      expect(action.getY()).toBe(0.5);
      expect(action.getLength()).toBeCloseTo(0.7071, 4);
      expect(action.getDirection()).toBeCloseTo(Math.PI / 4, 4);
      expect(action.hasChanged()).toBe(true);

      action.reset();
      expect(action.hasChanged()).toBe(false);
      expect(action.getValue()).toEqual([0.5, 0.5]);

      action.update([0.5, 0.5]);
      expect(action.hasChanged()).toBe(false);

      action.update([0.8, 0.2]);
      expect(action.getValue()).toEqual([0.8, 0.2]);
      expect(action.getX()).toBe(0.8);
      expect(action.getY()).toBe(0.2);
      expect(action.hasChanged()).toBe(true);
    });
  });
});
