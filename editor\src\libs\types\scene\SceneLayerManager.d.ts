/**
 * 场景图层管理器
 * 管理场景中的所有图层
 */
import { Scene } from './Scene';
import { SceneLayer, SceneLayerOptions } from './SceneLayer';
import { EventEmitter } from '../utils/EventEmitter';
import type { Entity } from '../core/Entity';
/**
 * 场景图层管理器选项
 */
export interface SceneLayerManagerOptions {
    /** 是否自动创建默认图层 */
    createDefaultLayers?: boolean;
    /** 默认图层数量 */
    defaultLayerCount?: number;
}
/**
 * 场景图层查询选项
 */
export interface SceneLayerQueryOptions {
    /** 是否包含不可见图层 */
    includeInvisible?: boolean;
    /** 是否包含锁定图层 */
    includeLocked?: boolean;
    /** 标签过滤器 */
    tagFilter?: string[];
    /** 名称过滤器（支持正则表达式） */
    nameFilter?: string | RegExp;
    /** 自定义过滤函数 */
    customFilter?: (layer: SceneLayer) => boolean;
}
/**
 * 场景图层管理器
 */
export declare class SceneLayerManager extends EventEmitter {
    /** 所属场景 */
    private scene;
    /** 图层映射 */
    private layers;
    /** 默认图层 */
    private defaultLayer;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建场景图层管理器
     * @param scene 所属场景
     * @param options 选项
     */
    constructor(scene: Scene, options?: SceneLayerManagerOptions);
    /**
     * 初始化图层管理器
     */
    initialize(): void;
    /**
     * 创建默认图层
     * @param count 默认图层数量
     */
    private createDefaultLayers;
    /**
     * 创建图层
     * @param options 图层选项
     * @returns 创建的图层
     */
    createLayer(options: SceneLayerOptions): SceneLayer;
    /**
     * 设置图层事件监听
     * @param layer 图层
     */
    private setupLayerEvents;
    /**
     * 获取图层
     * @param id 图层ID
     * @returns 图层实例
     */
    getLayer(id: string): SceneLayer | null;
    /**
     * 获取默认图层
     * @returns 默认图层
     */
    getDefaultLayer(): SceneLayer | null;
    /**
     * 获取所有图层
     * @returns 图层数组
     */
    getLayers(): SceneLayer[];
    /**
     * 获取根图层
     * @returns 根图层数组（没有父图层的图层）
     */
    getRootLayers(): SceneLayer[];
    /**
     * 获取图层的子图层
     * @param layerId 图层ID
     * @returns 子图层数组
     */
    getChildLayers(layerId: string): SceneLayer[];
    /**
     * 更新子图层的可见性
     * @param layerId 图层ID
     * @param visible 是否可见
     */
    private updateChildrenVisibility;
    /**
     * 更新子图层的锁定状态
     * @param layerId 图层ID
     * @param locked 是否锁定
     */
    private updateChildrenLock;
    /**
     * 创建图层组
     * @param name 组名称
     * @param parentId 父图层ID
     * @returns 创建的图层组
     */
    createLayerGroup(name: string, parentId?: string): SceneLayer;
    /**
     * 获取图层数量
     * @returns 图层数量
     */
    getLayerCount(): number;
    /**
     * 查询图层
     * @param options 查询选项
     * @returns 匹配的图层数组
     */
    queryLayers(options?: SceneLayerQueryOptions): SceneLayer[];
    /**
     * 添加实体到图层
     * @param entity 实体
     * @param layerId 图层ID
     * @returns 是否成功添加
     */
    addEntityToLayer(entity: Entity, layerId: string): boolean;
    /**
     * 从图层中移除实体
     * @param entity 实体
     * @param layerId 图层ID
     * @returns 是否成功移除
     */
    removeEntityFromLayer(entity: Entity, layerId: string): boolean;
    /**
     * 移除图层
     * @param id 图层ID
     * @param removeChildren 是否同时移除子图层
     * @returns 是否成功移除
     */
    removeLayer(id: string, removeChildren?: boolean): boolean;
    /**
     * 清空所有图层
     */
    clearLayers(): void;
    /**
     * 销毁图层管理器
     */
    dispose(): void;
}
