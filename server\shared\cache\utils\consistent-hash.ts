/**
 * 一致性哈希实现
 * 用于分布式缓存的分片
 */

import * as crypto from 'crypto';

/**
 * 一致性哈希类
 */
export class ConsistentHash {
  // 虚拟节点数量
  private readonly virtualNodeCount: number;
  
  // 哈希环，按哈希值排序
  private readonly ring: { hash: number; node: string }[] = [];
  
  // 节点列表
  private readonly nodes: string[] = [];

  /**
   * 创建一致性哈希实例
   * @param nodeCount 节点数量
   * @param virtualNodeCount 每个物理节点的虚拟节点数量
   */
  constructor(nodeCount: number = 16, virtualNodeCount: number = 100) {
    this.virtualNodeCount = virtualNodeCount;
    
    // 创建节点
    for (let i = 0; i < nodeCount; i++) {
      this.addNode(`node-${i}`);
    }
  }

  /**
   * 添加节点
   * @param node 节点名称
   */
  public addNode(node: string): void {
    if (this.nodes.includes(node)) {
      return;
    }
    
    this.nodes.push(node);
    
    // 为每个物理节点创建多个虚拟节点
    for (let i = 0; i < this.virtualNodeCount; i++) {
      const virtualNode = `${node}:${i}`;
      const hash = this.hash(virtualNode);
      
      this.ring.push({ hash, node });
    }
    
    // 对哈希环进行排序
    this.sortRing();
  }

  /**
   * 移除节点
   * @param node 节点名称
   */
  public removeNode(node: string): void {
    const index = this.nodes.indexOf(node);
    if (index === -1) {
      return;
    }
    
    this.nodes.splice(index, 1);
    
    // 移除节点的所有虚拟节点
    for (let i = this.ring.length - 1; i >= 0; i--) {
      const virtualNode = this.ring[i];
      if (virtualNode.node === node) {
        this.ring.splice(i, 1);
      }
    }
  }

  /**
   * 获取键对应的节点
   * @param key 键
   * @returns 节点名称
   */
  public getNode(key: string): string {
    if (this.ring.length === 0) {
      throw new Error('哈希环为空');
    }
    
    const hash = this.hash(key);
    
    // 在哈希环上查找第一个大于等于hash的节点
    for (const virtualNode of this.ring) {
      if (virtualNode.hash >= hash) {
        return virtualNode.node;
      }
    }
    
    // 如果没有找到，则返回第一个节点（环形结构）
    return this.ring[0].node;
  }

  /**
   * 获取所有节点
   * @returns 节点列表
   */
  public getNodes(): string[] {
    return [...this.nodes];
  }

  /**
   * 获取节点数量
   * @returns 节点数量
   */
  public getNodeCount(): number {
    return this.nodes.length;
  }

  /**
   * 计算哈希值
   * @param key 键
   * @returns 哈希值
   */
  private hash(key: string): number {
    const md5 = crypto.createHash('md5').update(key).digest('hex');
    return parseInt(md5.substring(0, 8), 16);
  }

  /**
   * 对哈希环进行排序
   */
  private sortRing(): void {
    this.ring.sort((a, b) => a.hash - b.hash);
  }
}
