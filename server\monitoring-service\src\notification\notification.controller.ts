import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { NotificationService } from './notification.service';

/**
 * 通知控制器
 * 提供通知管理的API接口
 */
@Controller('notifications')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  /**
   * 发送通知
   */
  @Post('send')
  async sendNotification(@Body() notificationData: {
    title: string;
    message: string;
    type: string;
    channels: string[];
    recipients?: string[];
    metadata?: any;
  }) {
    // 暂时返回成功响应，实际实现需要根据具体需求调整
    return { success: true, message: '通知发送请求已接收' };
  }

  /**
   * 获取通知历史
   */
  @Get('history')
  async getNotificationHistory(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('type') type?: string,
    @Query('status') status?: string,
  ) {
    return await this.notificationService.getNotificationHistory({
      page: page || 1,
      limit: limit || 20,
      type,
      status,
    });
  }

  /**
   * 获取通知渠道
   */
  @Get('channels')
  async getNotificationChannels() {
    return await this.notificationService.getNotificationChannels();
  }

  /**
   * 创建通知渠道
   */
  @Post('channels')
  async createNotificationChannel(@Body() channelData: {
    name: string;
    type: string;
    config: any;
    enabled: boolean;
  }) {
    return await this.notificationService.createNotificationChannel(channelData);
  }

  /**
   * 更新通知渠道
   */
  @Put('channels/:id')
  async updateNotificationChannel(
    @Param('id') id: string,
    @Body() channelData: any,
  ) {
    return await this.notificationService.updateNotificationChannel(id, channelData);
  }

  /**
   * 删除通知渠道
   */
  @Delete('channels/:id')
  async deleteNotificationChannel(@Param('id') id: string) {
    return await this.notificationService.deleteNotificationChannel(id);
  }

  /**
   * 测试通知渠道
   */
  @Post('channels/:id/test')
  async testNotificationChannel(@Param('id') id: string) {
    return await this.notificationService.testNotificationChannel(id);
  }

  /**
   * 启用/禁用通知渠道
   */
  @Put('channels/:id/toggle')
  async toggleNotificationChannel(
    @Param('id') id: string,
    @Body() body: { enabled: boolean },
  ) {
    return await this.notificationService.toggleNotificationChannel(id, body.enabled);
  }

  /**
   * 获取通知统计
   */
  @Get('stats')
  async getNotificationStats(
    @Query('from') from?: string,
    @Query('to') to?: string,
  ) {
    return await this.notificationService.getNotificationStats({
      from: from ? new Date(from) : undefined,
      to: to ? new Date(to) : undefined,
    });
  }
}
