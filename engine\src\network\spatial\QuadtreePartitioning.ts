/**
 * 四叉树空间分区
 * 用于网络实体的高效空间查询和同步优化
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';

/**
 * 四叉树节点
 */
export class QuadtreeNode {
  /** 边界 */
  public bounds: {
    minX: number;
    minZ: number;
    maxX: number;
    maxZ: number;
  };

  /** 深度 */
  public depth: number;

  /** 子节点 */
  public children: QuadtreeNode[] | null = null;

  /** 实体列表 */
  public entities: Map<string, Entity> = new Map();

  /** 父节点 */
  public parent: QuadtreeNode | null = null;

  /** 是否已分割 */
  public divided: boolean = false;

  /**
   * 创建四叉树节点
   * @param minX 最小X坐标
   * @param minZ 最小Z坐标
   * @param maxX 最大X坐标
   * @param maxZ 最大Z坐标
   * @param depth 深度
   * @param parent 父节点
   */
  constructor(
    minX: number,
    minZ: number,
    maxX: number,
    maxZ: number,
    depth: number = 0,
    parent: QuadtreeNode | null = null
  ) {
    this.bounds = { minX, minZ, maxX, maxZ };
    this.depth = depth;
    this.parent = parent;
  }

  /**
   * 获取中心点
   * @returns 中心点
   */
  public getCenter(): { x: number, z: number } {
    const { minX, minZ, maxX, maxZ } = this.bounds;
    return {
      x: (minX + maxX) / 2,
      z: (minZ + maxZ) / 2
    };
  }

  /**
   * 获取宽度
   * @returns 宽度
   */
  public getWidth(): number {
    return this.bounds.maxX - this.bounds.minX;
  }

  /**
   * 获取高度
   * @returns 高度
   */
  public getHeight(): number {
    return this.bounds.maxZ - this.bounds.minZ;
  }

  /**
   * 分割节点
   */
  public subdivide(): void {
    if (this.divided) return;

    const { minX, minZ, maxX, maxZ } = this.bounds;
    const centerX = (minX + maxX) / 2;
    const centerZ = (minZ + maxZ) / 2;
    const nextDepth = this.depth + 1;

    // 创建四个子节点
    this.children = [
      // 左上
      new QuadtreeNode(minX, minZ, centerX, centerZ, nextDepth, this),
      // 右上
      new QuadtreeNode(centerX, minZ, maxX, centerZ, nextDepth, this),
      // 左下
      new QuadtreeNode(minX, centerZ, centerX, maxZ, nextDepth, this),
      // 右下
      new QuadtreeNode(centerX, centerZ, maxX, maxZ, nextDepth, this)
    ];

    this.divided = true;

    // 重新分配实体
    this.redistributeEntities();
  }

  /**
   * 重新分配实体
   */
  private redistributeEntities(): void {
    if (!this.children) return;

    // 遍历所有实体
    for (const [entityId, entity] of this.entities.entries()) {
      // 获取实体位置
      const position = this.getEntityPosition(entity);
      if (!position) continue;

      // 查找适合的子节点
      for (const child of this.children) {
        if (this.isPointInBounds(position.x, position.z, child.bounds)) {
          // 添加到子节点
          child.entities.set(entityId, entity);
          break;
        }
      }
    }
  }

  /**
   * 检查点是否在边界内
   * @param x X坐标
   * @param z Z坐标
   * @param bounds 边界
   * @returns 是否在边界内
   */
  private isPointInBounds(
    x: number,
    z: number,
    bounds: { minX: number; minZ: number; maxX: number; maxZ: number }
  ): boolean {
    return (
      x >= bounds.minX &&
      x < bounds.maxX &&
      z >= bounds.minZ &&
      z < bounds.maxZ
    );
  }

  /**
   * 获取实体位置
   * @param entity 实体
   * @returns 位置
   */
  private getEntityPosition(entity: Entity): THREE.Vector3 | null {
    const transform = entity.getComponent('Transform') as any as any;
    if (!transform || !transform.position) {
      return null;
    }
    return transform.position;
  }
}

/**
 * 四叉树配置
 */
export interface QuadtreeConfig {
  /** 最大深度 */
  maxDepth?: number;
  /** 节点最大实体数量 */
  maxEntities?: number;
  /** 最小节点大小 */
  minNodeSize?: number;
  /** 世界大小 */
  worldSize?: number;
  /** 世界中心 */
  worldCenter?: THREE.Vector3;
  /** 是否启用动态调整 */
  enableDynamicAdjustment?: boolean;
  /** 是否启用松散四叉树 */
  enableLooseQuadtree?: boolean;
  /** 松散因子 */
  looseFactor?: number;
}

/**
 * 四叉树空间分区
 */
export class QuadtreePartitioning {
  /** 配置 */
  private config: Required<QuadtreeConfig>;

  /** 根节点 */
  private root: QuadtreeNode;

  /** 实体到节点的映射 */
  private entityToNode: Map<string, QuadtreeNode> = new Map();

  /** 实体列表 */
  private entities: Map<string, Entity> = new Map();

  /** 调试网格 */
  private debugMesh: THREE.Object3D | null = null;

  /**
   * 创建四叉树空间分区
   * @param config 配置
   */
  constructor(config: QuadtreeConfig = {}) {
    // 默认配置
    this.config = {
      maxDepth: 8,
      maxEntities: 16,
      minNodeSize: 5,
      worldSize: 1000,
      worldCenter: new THREE.Vector3(0, 0, 0),
      enableDynamicAdjustment: true,
      enableLooseQuadtree: true,
      looseFactor: 1.5,
      ...config
    };

    // 创建根节点
    const halfSize = this.config.worldSize / 2;
    const center = this.config.worldCenter;
    this.root = new QuadtreeNode(
      center.x - halfSize,
      center.z - halfSize,
      center.x + halfSize,
      center.z + halfSize
    );
  }

  /**
   * 添加实体
   * @param entityId 实体ID
   * @param entity 实体
   */
  public addEntity(entityId: string, entity: Entity): void {
    // 如果实体已存在，先移除
    if (this.entities.has(entityId)) {
      this.removeEntity(entityId);
    }

    // 添加到实体列表
    this.entities.set(entityId, entity);

    // 获取实体位置
    const position = this.getEntityPosition(entity);
    if (!position) {
      Debug.warn('QuadtreePartitioning', `实体 ${entityId} 没有位置信息`);
      return;
    }

    // 查找适合的节点
    const node = this.findNodeForPosition(position.x, position.z);
    if (!node) {
      Debug.warn('QuadtreePartitioning', `无法为实体 ${entityId} 找到合适的节点`);
      return;
    }

    // 添加到节点
    node.entities.set(entityId, entity);
    this.entityToNode.set(entityId, node);

    // 检查是否需要分割
    this.checkAndSubdivide(node);
  }

  /**
   * 移除实体
   * @param entityId 实体ID
   */
  public removeEntity(entityId: string): void {
    // 获取实体所在节点
    const node = this.entityToNode.get(entityId);
    if (!node) {
      return;
    }

    // 从节点中移除
    node.entities.delete(entityId);

    // 从映射中移除
    this.entityToNode.delete(entityId);

    // 从实体列表中移除
    this.entities.delete(entityId);

    // 检查是否需要合并
    this.checkAndMerge(node);
  }

  /**
   * 更新实体
   * @param entityId 实体ID
   * @param entity 实体
   */
  public updateEntity(entityId: string, entity: Entity): void {
    // 获取实体所在节点
    const node = this.entityToNode.get(entityId);
    if (!node) {
      // 如果实体不存在，则添加
      this.addEntity(entityId, entity);
      return;
    }

    // 获取实体位置
    const position = this.getEntityPosition(entity);
    if (!position) {
      return;
    }

    // 检查是否仍在同一节点
    if (this.isPointInBounds(position.x, position.z, node.bounds)) {
      // 更新实体
      this.entities.set(entityId, entity);
      node.entities.set(entityId, entity);
    } else {
      // 移除并重新添加
      this.removeEntity(entityId);
      this.addEntity(entityId, entity);
    }
  }

  /**
   * 查询区域内的实体
   * @param minX 最小X坐标
   * @param minZ 最小Z坐标
   * @param maxX 最大X坐标
   * @param maxZ 最大Z坐标
   * @returns 实体列表
   */
  public queryRegion(minX: number, minZ: number, maxX: number, maxZ: number): Map<string, Entity> {
    const result = new Map<string, Entity>();
    this.queryRegionRecursive(this.root, minX, minZ, maxX, maxZ, result);
    return result;
  }

  /**
   * 递归查询区域内的实体
   * @param node 节点
   * @param minX 最小X坐标
   * @param minZ 最小Z坐标
   * @param maxX 最大X坐标
   * @param maxZ 最大Z坐标
   * @param result 结果
   */
  private queryRegionRecursive(
    node: QuadtreeNode,
    minX: number,
    minZ: number,
    maxX: number,
    maxZ: number,
    result: Map<string, Entity>
  ): void {
    // 检查是否与节点相交
    if (!this.isRegionOverlap(minX, minZ, maxX, maxZ, node.bounds)) {
      return;
    }

    // 添加节点中的实体
    for (const [entityId, entity] of node.entities.entries()) {
      const position = this.getEntityPosition(entity);
      if (!position) continue;

      if (
        position.x >= minX &&
        position.x <= maxX &&
        position.z >= minZ &&
        position.z <= maxZ
      ) {
        result.set(entityId, entity);
      }
    }

    // 如果有子节点，递归查询
    if (node.children) {
      for (const child of node.children) {
        this.queryRegionRecursive(child, minX, minZ, maxX, maxZ, result);
      }
    }
  }

  /**
   * 查询距离内的实体
   * @param x 中心X坐标
   * @param z 中心Z坐标
   * @param radius 半径
   * @returns 实体列表
   */
  public queryRadius(x: number, z: number, radius: number): Map<string, Entity> {
    // 创建包围盒
    const minX = x - radius;
    const minZ = z - radius;
    const maxX = x + radius;
    const maxZ = z + radius;

    // 先查询区域
    const regionResult = this.queryRegion(minX, minZ, maxX, maxZ);
    const result = new Map<string, Entity>();

    // 过滤距离
    const radiusSq = radius * radius;
    for (const [entityId, entity] of regionResult.entries()) {
      const position = this.getEntityPosition(entity);
      if (!position) continue;

      const dx = position.x - x;
      const dz = position.z - z;
      const distSq = dx * dx + dz * dz;

      if (distSq <= radiusSq) {
        result.set(entityId, entity);
      }
    }

    return result;
  }

  /**
   * 查找位置所在的节点
   * @param x X坐标
   * @param z Z坐标
   * @returns 节点
   */
  private findNodeForPosition(x: number, z: number): QuadtreeNode | null {
    // 检查是否在根节点范围内
    if (!this.isPointInBounds(x, z, this.root.bounds)) {
      return null;
    }

    return this.findNodeForPositionRecursive(this.root, x, z);
  }

  /**
   * 递归查找位置所在的节点
   * @param node 节点
   * @param x X坐标
   * @param z Z坐标
   * @returns 节点
   */
  private findNodeForPositionRecursive(node: QuadtreeNode, x: number, z: number): QuadtreeNode {
    // 如果没有子节点或达到最大深度，返回当前节点
    if (!node.children || node.depth >= this.config.maxDepth) {
      return node;
    }

    // 查找包含位置的子节点
    for (const child of node.children) {
      if (this.isPointInBounds(x, z, child.bounds)) {
        return this.findNodeForPositionRecursive(child, x, z);
      }
    }

    // 如果没有找到合适的子节点，返回当前节点
    return node;
  }

  /**
   * 检查并分割节点
   * @param node 节点
   */
  private checkAndSubdivide(node: QuadtreeNode): void {
    // 如果已达到最大深度或节点大小小于最小节点大小，不分割
    if (
      node.depth >= this.config.maxDepth ||
      node.getWidth() <= this.config.minNodeSize ||
      node.getHeight() <= this.config.minNodeSize
    ) {
      return;
    }

    // 如果实体数量超过最大值，分割节点
    if (node.entities.size > this.config.maxEntities) {
      node.subdivide();
    }
  }

  /**
   * 检查并合并节点
   * @param node 节点
   */
  private checkAndMerge(node: QuadtreeNode): void {
    // 如果没有父节点，不合并
    if (!node.parent || !node.parent.children) {
      return;
    }

    // 计算所有子节点的实体总数
    let totalEntities = 0;
    for (const child of node.parent.children) {
      totalEntities += child.entities.size;
    }

    // 如果总实体数小于最大值的一半，合并节点
    if (totalEntities <= this.config.maxEntities / 2) {
      // 将所有子节点的实体移动到父节点
      for (const child of node.parent.children) {
        for (const [entityId, entity] of child.entities.entries()) {
          node.parent.entities.set(entityId, entity);
          this.entityToNode.set(entityId, node.parent);
        }
        child.entities.clear();
      }

      // 移除子节点
      node.parent.children = null;
      node.parent.divided = false;
    }
  }

  /**
   * 检查点是否在边界内
   * @param x X坐标
   * @param z Z坐标
   * @param bounds 边界
   * @returns 是否在边界内
   */
  private isPointInBounds(
    x: number,
    z: number,
    bounds: { minX: number; minZ: number; maxX: number; maxZ: number }
  ): boolean {
    return (
      x >= bounds.minX &&
      x < bounds.maxX &&
      z >= bounds.minZ &&
      z < bounds.maxZ
    );
  }

  /**
   * 检查区域是否与边界相交
   * @param minX 区域最小X
   * @param minZ 区域最小Z
   * @param maxX 区域最大X
   * @param maxZ 区域最大Z
   * @param bounds 边界
   * @returns 是否相交
   */
  private isRegionOverlap(
    minX: number,
    minZ: number,
    maxX: number,
    maxZ: number,
    bounds: { minX: number; minZ: number; maxX: number; maxZ: number }
  ): boolean {
    return (
      maxX >= bounds.minX &&
      minX <= bounds.maxX &&
      maxZ >= bounds.minZ &&
      minZ <= bounds.maxZ
    );
  }

  /**
   * 获取实体位置
   * @param entity 实体
   * @returns 位置
   */
  private getEntityPosition(entity: Entity): THREE.Vector3 | null {
    const transform = entity.getComponent('Transform') as any as any;
    if (!transform || !transform.position) {
      return null;
    }
    return transform.position;
  }

  /**
   * 清空四叉树
   */
  public clear(): void {
    this.entities.clear();
    this.entityToNode.clear();

    // 重新创建根节点
    const halfSize = this.config.worldSize / 2;
    const center = this.config.worldCenter;
    this.root = new QuadtreeNode(
      center.x - halfSize,
      center.z - halfSize,
      center.x + halfSize,
      center.z + halfSize
    );
  }

  /**
   * 获取实体数量
   * @returns 实体数量
   */
  public getEntityCount(): number {
    return this.entities.size;
  }

  /**
   * 获取所有实体
   * @returns 实体列表
   */
  public getAllEntities(): Map<string, Entity> {
    return new Map(this.entities);
  }
}
