import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  Tree,
  TreeChildren,
  TreeParent,
  Index,
} from 'typeorm';
import { Asset } from '../../assets/entities/asset.entity';

@Entity('categories')
@Tree('closure-table')
@Index(['name', 'parentId'])
export class Category {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  @Index()
  name: string;

  @Column({ length: 500, nullable: true })
  description: string;

  @Column({ length: 50, unique: true })
  slug: string;

  @Column({ nullable: true })
  icon: string;

  @Column({ name: 'sort_order', default: 0 })
  sortOrder: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'asset_count', default: 0 })
  assetCount: number;

  // 树形结构
  @TreeChildren()
  children: Category[];

  @TreeParent()
  parent: Category;

  @Column({ name: 'parent_id', nullable: true })
  parentId: string;

  @Column({ default: 0 })
  level: number;

  @Column({ nullable: true })
  path: string;

  // 关联关系
  @OneToMany(() => Asset, asset => asset.category)
  assets: Asset[];

  // 元数据
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get fullPath(): string {
    return this.path || this.name;
  }

  get hasChildren(): boolean {
    return this.children && this.children.length > 0;
  }

  get isRoot(): boolean {
    return !this.parentId;
  }
}
