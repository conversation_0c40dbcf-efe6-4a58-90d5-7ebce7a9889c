/**
 * 地形性能面板
 * 用于显示和控制地形性能优化
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Tabs, Button, Switch, Slider, Space, Divider, Typography, Row, Col, Statistic, Tag } from 'antd';
import {
  DashboardOutlined,
  SettingOutlined,
  LineChartOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  AreaChartOutlined,
  PauseOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
// import { useSelector } from 'react-redux';
// import { RootState } from '../../store';
import TerrainBenchmarkPanel from './TerrainBenchmarkPanel';
import './TerrainPerformancePanel.less';

const { Text } = Typography;

/**
 * 性能数据接口
 */
interface PerformanceData {
  fps: number;
  memoryUsage: number;
  terrainRenderTime: number;
  visibleTerrainChunks: number;
  terrainTriangles: number;
  terrainVertices: number;
  terrainTextureMemory: number;
  terrainGeometryMemory: number;
}

/**
 * 性能瓶颈类型
 */
enum PerformanceBottleneckType {
  GPU_MEMORY = 'gpu_memory',
  CPU_USAGE = 'cpu_usage',
  DRAW_CALLS = 'draw_calls',
  TEXTURE_MEMORY = 'texture_memory',
  GEOMETRY_COMPLEXITY = 'geometry_complexity'
}

/**
 * 性能监控事件类型
 */
enum PerformanceMonitorEventType {
  PERFORMANCE_DATA_UPDATED = 'performance_data_updated',
  PERFORMANCE_WARNING = 'performance_warning',
  BOTTLENECK_DETECTED = 'bottleneck_detected'
}

/**
 * 性能监控器类（模拟）
 */
class TerrainPerformanceMonitor {
  private static instance: TerrainPerformanceMonitor;
  private listeners: Map<string, Function[]> = new Map();
  private isRunning = false;

  static getInstance(): TerrainPerformanceMonitor {
    if (!TerrainPerformanceMonitor.instance) {
      TerrainPerformanceMonitor.instance = new TerrainPerformanceMonitor();
    }
    return TerrainPerformanceMonitor.instance;
  }

  configure(_config: any) {
    // 配置监控器
  }

  start() {
    this.isRunning = true;
  }

  stop() {
    this.isRunning = false;
  }

  isActive(): boolean {
    return this.isRunning;
  }

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function) {
    const listeners = this.listeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }
}

/**
 * 地形性能面板属性
 */
interface TerrainPerformancePanelProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
}

/**
 * 地形性能面板组件
 */
const TerrainPerformancePanel: React.FC<TerrainPerformancePanelProps> = ({
  entityId,
  editable = true
}) => {
  const { t } = useTranslation();

  // 从Redux获取地形数据（暂时保留以备将来使用）
  // const terrainData = useSelector((state: RootState) => {
  //   if (!entityId) return null;
  //   const entity = state.scene.entities.find(e => e.id === entityId);
  //   return entity?.components?.TerrainComponent || null;
  // });

  // 性能监控器
  const performanceMonitor = useRef<TerrainPerformanceMonitor | null>(null);

  // 状态
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [isMonitoring, setIsMonitoring] = useState<boolean>(false);
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [, setPerformanceHistory] = useState<PerformanceData[]>([]);
  const [bottlenecks, setBottlenecks] = useState<{type: PerformanceBottleneckType, message: string}[]>([]);
  const [, setWarnings] = useState<{type: string, message: string, value: number, threshold: number}[]>([]);

  // 配置
  const [updateFrequency, setUpdateFrequency] = useState<number>(1000);
  const [sampleSize, setSampleSize] = useState<number>(60);
  const [recordHistory, setRecordHistory] = useState<boolean>(true);
  const [historyLength, setHistoryLength] = useState<number>(60);
  const [enableWarnings, setEnableWarnings] = useState<boolean>(true);
  const [enableBottleneckDetection, setEnableBottleneckDetection] = useState<boolean>(true);
  const [showSettings, setShowSettings] = useState<boolean>(false);

  // 注册事件监听器
  const registerEventListeners = React.useCallback(() => {
    if (!performanceMonitor.current) return;

    // 性能数据更新事件
    performanceMonitor.current.on(PerformanceMonitorEventType.PERFORMANCE_DATA_UPDATED, (data: PerformanceData) => {
      setPerformanceData(data);

      // 更新历史数据
      if (recordHistory) {
        setPerformanceHistory(prev => {
          const newHistory = [...prev, data];
          if (newHistory.length > historyLength) {
            return newHistory.slice(-historyLength);
          }
          return newHistory;
        });
      }
    });

    // 性能警告事件
    performanceMonitor.current.on(PerformanceMonitorEventType.PERFORMANCE_WARNING, (data: any[]) => {
      setWarnings(data);
    });

    // 性能瓶颈检测事件
    performanceMonitor.current.on(PerformanceMonitorEventType.BOTTLENECK_DETECTED, (type: PerformanceBottleneckType, message: string) => {
      setBottlenecks(prev => {
        // 检查是否已存在相同类型的瓶颈
        const exists = prev.some(b => b.type === type);
        if (exists) {
          return prev;
        }
        return [...prev, { type, message }];
      });
    });
  }, [recordHistory, historyLength]);

  // 取消注册事件监听器
  const unregisterEventListeners = React.useCallback(() => {
    if (!performanceMonitor.current) return;

    performanceMonitor.current.off(PerformanceMonitorEventType.PERFORMANCE_DATA_UPDATED, () => {});
    performanceMonitor.current.off(PerformanceMonitorEventType.PERFORMANCE_WARNING, () => {});
    performanceMonitor.current.off(PerformanceMonitorEventType.BOTTLENECK_DETECTED, () => {});
  }, []);

  // 初始化性能监控器
  useEffect(() => {
    // 获取性能监控器实例
    performanceMonitor.current = TerrainPerformanceMonitor.getInstance();

    // 配置性能监控器
    performanceMonitor.current.configure({
      enabled: isMonitoring,
      updateFrequency,
      sampleSize,
      recordHistory,
      maxHistoryLength: historyLength,
      enableWarnings,
      enableBottleneckDetection
    });

    // 注册事件监听器
    registerEventListeners();

    // 清理函数
    return () => {
      unregisterEventListeners();
    };
  }, [registerEventListeners, unregisterEventListeners, isMonitoring, updateFrequency, sampleSize, recordHistory, historyLength, enableWarnings, enableBottleneckDetection]);

  // 启动监控
  const startMonitoring = () => {
    if (!performanceMonitor.current) return;

    performanceMonitor.current.start();
    setIsMonitoring(true);
  };

  // 停止监控
  const stopMonitoring = () => {
    if (!performanceMonitor.current) return;

    performanceMonitor.current.stop();
    setIsMonitoring(false);
  };

  // 切换监控
  const toggleMonitoring = () => {
    if (isMonitoring) {
      stopMonitoring();
    } else {
      startMonitoring();
    }
  };

  // 重置性能数据
  const resetPerformanceData = () => {
    setPerformanceData(null);
    setPerformanceHistory([]);
    setBottlenecks([]);
    setWarnings([]);
  };

  // 更新配置
  const updateConfig = React.useCallback(() => {
    if (!performanceMonitor.current) return;

    performanceMonitor.current.configure({
      enabled: isMonitoring,
      updateFrequency,
      sampleSize,
      recordHistory,
      maxHistoryLength: historyLength,
      enableWarnings,
      enableBottleneckDetection
    });
  }, [isMonitoring, updateFrequency, sampleSize, recordHistory, historyLength, enableWarnings, enableBottleneckDetection]);

  // 配置变更时更新
  useEffect(() => {
    updateConfig();
  }, [updateConfig]);

  // 获取指标状态颜色
  const getMetricStatusColor = (value: number, threshold: number, isHigherBetter: boolean = false): string => {
    if (isHigherBetter) {
      if (value >= threshold) return '#52c41a'; // 绿色
      if (value >= threshold * 0.7) return '#faad14'; // 黄色
      return '#f5222d'; // 红色
    } else {
      if (value <= threshold) return '#52c41a'; // 绿色
      if (value <= threshold * 1.3) return '#faad14'; // 黄色
      return '#f5222d'; // 红色
    }
  };

  // 渲染概览
  const renderOverview = () => {
    if (!performanceData) {
      return (
        <div className="no-data-message">
          <InfoCircleOutlined />
          <Text>{t('terrain.performance.noData')}</Text>
        </div>
      );
    }

    return (
      <div className="performance-overview">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card>
              <Statistic
                title={t('terrain.performance.fps')}
                value={performanceData.fps}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(performanceData.fps, 30, true) }}
                prefix={<DashboardOutlined />}
                suffix="FPS"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title={t('terrain.performance.memory')}
                value={performanceData.memoryUsage / (1024 * 1024)}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(performanceData.memoryUsage / (1024 * 1024), 500) }}
                prefix={<AreaChartOutlined />}
                suffix="MB"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title={t('terrain.performance.renderTime')}
                value={performanceData.terrainRenderTime}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(performanceData.terrainRenderTime, 16) }}
                prefix={<LineChartOutlined />}
                suffix="ms"
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card title={t('terrain.performance.terrainStats')}>
              <div className="stat-item">
                <Text>{t('terrain.performance.visibleChunks')}</Text>
                <Text>{performanceData.visibleTerrainChunks}</Text>
              </div>
              <div className="stat-item">
                <Text>{t('terrain.performance.triangles')}</Text>
                <Text>{performanceData.terrainTriangles.toLocaleString()}</Text>
              </div>
              <div className="stat-item">
                <Text>{t('terrain.performance.vertices')}</Text>
                <Text>{performanceData.terrainVertices.toLocaleString()}</Text>
              </div>
              <div className="stat-item">
                <Text>{t('terrain.performance.textureMemory')}</Text>
                <Text>{(performanceData.terrainTextureMemory / (1024 * 1024)).toFixed(1)} MB</Text>
              </div>
              <div className="stat-item">
                <Text>{t('terrain.performance.geometryMemory')}</Text>
                <Text>{(performanceData.terrainGeometryMemory / (1024 * 1024)).toFixed(1)} MB</Text>
              </div>
            </Card>
          </Col>

          <Col span={12}>
            <Card title={t('terrain.performance.bottlenecks')}>
              {bottlenecks.length > 0 ? (
                <div className="bottleneck-list">
                  {bottlenecks.map((bottleneck, index) => (
                    <Tag key={index} color="red" className="bottleneck-tag">
                      <WarningOutlined /> {bottleneck.message}
                    </Tag>
                  ))}
                </div>
              ) : (
                <div className="no-bottlenecks">
                  <CheckCircleOutlined />
                  <Text>{t('terrain.performance.noBottlenecks')}</Text>
                </div>
              )}
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染设置
  const renderSettings = () => {
    return (
      <div className="performance-settings">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.updateFrequency')}</Text>
              <Slider
                min={100}
                max={5000}
                step={100}
                value={updateFrequency}
                onChange={(value) => setUpdateFrequency(value)}
              />
              <Text type="secondary">{updateFrequency} ms</Text>
            </div>
          </Col>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.sampleSize')}</Text>
              <Slider
                min={10}
                max={120}
                step={10}
                value={sampleSize}
                onChange={(value) => setSampleSize(value)}
              />
              <Text type="secondary">{sampleSize}</Text>
            </div>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.recordHistory')}</Text>
              <Switch
                checked={recordHistory}
                onChange={(checked) => setRecordHistory(checked)}
              />
            </div>
          </Col>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.historyLength')}</Text>
              <Slider
                min={10}
                max={120}
                step={10}
                value={historyLength}
                onChange={(value) => setHistoryLength(value)}
                disabled={!recordHistory}
              />
              <Text type="secondary">{historyLength}</Text>
            </div>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.enableWarnings')}</Text>
              <Switch
                checked={enableWarnings}
                onChange={(checked) => setEnableWarnings(checked)}
              />
            </div>
          </Col>
          <Col span={12}>
            <div className="setting-item">
              <Text>{t('terrain.performance.enableBottleneckDetection')}</Text>
              <Switch
                checked={enableBottleneckDetection}
                onChange={(checked) => setEnableBottleneckDetection(checked)}
              />
            </div>
          </Col>
        </Row>
      </div>
    );
  };

  /**
   * 渲染CDLOD性能内容
   */
  const renderCDLODPerformance = () => (
    <div>
      <Row gutter={16}>
        <Col span={12}>
          <Statistic
            title="CDLOD层级数"
            value={performanceData?.visibleTerrainChunks || 0}
            suffix="层"
          />
        </Col>
        <Col span={12}>
          <Statistic
            title="活跃节点数"
            value={Math.floor((performanceData?.terrainTriangles || 0) / 1000)}
            suffix="K"
          />
        </Col>
      </Row>
      <Divider />
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text>LOD距离因子</Text>
          <Slider
            min={0.5}
            max={5.0}
            step={0.1}
            defaultValue={2.0}
            marks={{ 0.5: '0.5', 2.0: '2.0', 5.0: '5.0' }}
          />
        </div>
        <div>
          <Text>最大LOD级别</Text>
          <Slider
            min={4}
            max={12}
            step={1}
            defaultValue={8}
            marks={{ 4: '4', 8: '8', 12: '12' }}
          />
        </div>
      </Space>
    </div>
  );

  /**
   * 渲染纹理性能内容
   */
  const renderTexturePerformance = () => (
    <div>
      <Row gutter={16}>
        <Col span={8}>
          <Statistic
            title="纹理内存使用"
            value={performanceData?.terrainTextureMemory || 0}
            suffix="MB"
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="纹理绑定次数"
            value={Math.floor(Math.random() * 100) + 50}
            suffix="次/帧"
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="纹理缓存命中率"
            value={85 + Math.floor(Math.random() * 10)}
            suffix="%"
          />
        </Col>
      </Row>
      <Divider />
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text>纹理质量</Text>
          <Slider
            min={1}
            max={4}
            step={1}
            defaultValue={3}
            marks={{ 1: '低', 2: '中', 3: '高', 4: '超高' }}
          />
        </div>
        <div>
          <Text>各向异性过滤</Text>
          <Slider
            min={1}
            max={16}
            step={1}
            defaultValue={8}
            marks={{ 1: '1x', 4: '4x', 8: '8x', 16: '16x' }}
          />
        </div>
      </Space>
    </div>
  );

  /**
   * 渲染物理性能内容
   */
  const renderPhysicsPerformance = () => (
    <div>
      <Row gutter={16}>
        <Col span={8}>
          <Statistic
            title="物理更新时间"
            value={(Math.random() * 2 + 1).toFixed(2)}
            suffix="ms"
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="碰撞检测次数"
            value={Math.floor(Math.random() * 500) + 100}
            suffix="次/帧"
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="物理内存使用"
            value={((performanceData?.terrainGeometryMemory || 0) * 0.3).toFixed(1)}
            suffix="MB"
          />
        </Col>
      </Row>
      <Divider />
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text>启用物理碰撞</Text>
          <Switch defaultChecked />
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text>启用物理材质</Text>
          <Switch defaultChecked />
        </div>
        <div>
          <Text>碰撞精度</Text>
          <Slider
            min={1}
            max={5}
            step={1}
            defaultValue={3}
            marks={{ 1: '粗糙', 3: '中等', 5: '精细' }}
          />
        </div>
      </Space>
    </div>
  );

  /**
   * 渲染虚拟纹理性能内容
   */
  const renderVirtualTexturingPerformance = () => (
    <div>
      <Row gutter={16}>
        <Col span={8}>
          <Statistic
            title="虚拟纹理缓存"
            value={(Math.random() * 200 + 100).toFixed(0)}
            suffix="MB"
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="页面请求数"
            value={Math.floor(Math.random() * 50) + 10}
            suffix="个/帧"
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="流式加载速度"
            value={(Math.random() * 50 + 20).toFixed(1)}
            suffix="MB/s"
          />
        </Col>
      </Row>
      <Divider />
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text>启用虚拟纹理</Text>
          <Switch defaultChecked />
        </div>
        <div>
          <Text>缓存大小</Text>
          <Slider
            min={64}
            max={512}
            step={64}
            defaultValue={256}
            marks={{ 64: '64MB', 256: '256MB', 512: '512MB' }}
          />
        </div>
        <div>
          <Text>页面大小</Text>
          <Slider
            min={128}
            max={1024}
            step={128}
            defaultValue={512}
            marks={{ 128: '128', 512: '512', 1024: '1024' }}
          />
        </div>
      </Space>
    </div>
  );

  return (
    <div className="terrain-performance-panel">
      <Card
        title={
          <Space>
            <DashboardOutlined />
            <span>{t('terrain.performance.title')}</span>
          </Space>
        }
        extra={
          <Space>
            <Button
              type={isMonitoring ? 'primary' : 'default'}
              icon={isMonitoring ? <PauseOutlined /> : <PlayCircleOutlined />}
              onClick={toggleMonitoring}
            >
              {isMonitoring ? t('terrain.performance.stopMonitoring') : t('terrain.performance.startMonitoring')}
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetPerformanceData}
              disabled={!isMonitoring}
            >
              {t('terrain.performance.reset')}
            </Button>
            <Button
              icon={<SettingOutlined />}
              onClick={() => setShowSettings(!showSettings)}
            >
              {t('terrain.performance.settings')}
            </Button>
          </Space>
        }
      >
        {showSettings ? (
          renderSettings()
        ) : (
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: 'overview',
                label: t('terrain.performance.overview'),
                children: renderOverview()
              },
              {
                key: 'cdlod',
                label: t('terrain.performance.cdlod'),
                children: renderCDLODPerformance()
              },
              {
                key: 'textures',
                label: t('terrain.performance.textures'),
                children: renderTexturePerformance()
              },
              {
                key: 'physics',
                label: t('terrain.performance.physics'),
                children: renderPhysicsPerformance()
              },
              {
                key: 'virtualTexturing',
                label: t('terrain.performance.virtualTexturing'),
                children: renderVirtualTexturingPerformance()
              },
              {
                key: 'benchmark',
                label: t('terrain.performance.benchmark'),
                children: (
                  <TerrainBenchmarkPanel
                    entityId={entityId}
                    editable={editable}
                  />
                )
              }
            ]}
          />
        )}
      </Card>
    </div>
  );
};

export default TerrainPerformancePanel;
