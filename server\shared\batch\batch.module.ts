/**
 * 批处理模块
 * 提供批处理功能
 */
import { Module, DynamicModule, Global } from '@nestjs/common';
import { BatchConfig } from './interfaces/batch-config.interface';

@Global()
@Module({})
export class BatchModule {
  /**
   * 注册批处理模块
   * @param config 批处理配置
   * @returns 动态模块
   */
  static register(config?: Partial<BatchConfig>): DynamicModule {
    return {
      module: BatchModule,
      providers: [
        {
          provide: 'BATCH_CONFIG',
          useValue: config || {},
        },
      ],
      exports: ['BATCH_CONFIG'],
    };
  }
}
