/**
 * 事务日志存储服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { Transaction } from '../transaction.interface';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

/**
 * 事务日志条目
 */
export interface TransactionLogEntry {
  /** 日志类型 */
  type: 'transaction';
  /** 时间戳 */
  timestamp: string;
  /** 事务状态 */
  status: string;
  /** 事务对象 */
  transaction: Transaction;
  /** 校验和 */
  checksum?: string;
}

/**
 * 事务日志存储选项
 */
export interface TransactionLogStorageOptions {
  /** 日志存储路径 */
  storagePath: string;
  /** 日志文件名 */
  filename?: string;
  /** 是否启用压缩 */
  enableCompression?: boolean;
  /** 是否启用加密 */
  enableEncryption?: boolean;
  /** 加密密钥 */
  encryptionKey?: string;
  /** 是否启用校验和 */
  enableChecksum?: boolean;
  /** 日志轮转大小（字节） */
  rotationSize?: number;
  /** 日志保留数量 */
  retentionCount?: number;
}

/**
 * 事务日志存储服务
 */
@Injectable()
export class TransactionLogStorageService {
  private readonly logger = new Logger(TransactionLogStorageService.name);
  private readonly options: TransactionLogStorageOptions;
  private readonly logFilePath: string;
  private currentLogSize: number = 0;
  
  constructor(options: TransactionLogStorageOptions) {
    this.options = {
      storagePath: options.storagePath,
      filename: options.filename || 'transaction.log',
      enableCompression: options.enableCompression || false,
      enableEncryption: options.enableEncryption || false,
      encryptionKey: options.encryptionKey,
      enableChecksum: options.enableChecksum || true,
      rotationSize: options.rotationSize || 10 * 1024 * 1024, // 10MB
      retentionCount: options.retentionCount || 5,
    };
    
    this.logFilePath = path.join(this.options.storagePath, this.options.filename);
    
    // 确保日志目录存在
    if (!fs.existsSync(this.options.storagePath)) {
      fs.mkdirSync(this.options.storagePath, { recursive: true });
    }
    
    // 获取当前日志文件大小
    if (fs.existsSync(this.logFilePath)) {
      const stats = fs.statSync(this.logFilePath);
      this.currentLogSize = stats.size;
    }
  }
  
  /**
   * 记录事务日志
   * @param transaction 事务对象
   */
  async logTransaction(transaction: Transaction): Promise<void> {
    try {
      // 创建日志条目
      const logEntry: TransactionLogEntry = {
        type: 'transaction',
        timestamp: new Date().toISOString(),
        status: transaction.status,
        transaction,
      };
      
      // 添加校验和
      if (this.options.enableChecksum) {
        logEntry.checksum = this.calculateChecksum(JSON.stringify(logEntry));
      }
      
      // 序列化日志条目
      let logData = JSON.stringify(logEntry) + '\n';
      
      // 加密日志数据
      if (this.options.enableEncryption && this.options.encryptionKey) {
        logData = this.encrypt(logData, this.options.encryptionKey);
      }
      
      // 检查是否需要轮转日志
      if (this.currentLogSize + logData.length > this.options.rotationSize) {
        await this.rotateLog();
      }
      
      // 写入日志
      fs.appendFileSync(this.logFilePath, logData);
      
      // 更新当前日志大小
      this.currentLogSize += logData.length;
    } catch (error) {
      this.logger.error(`记录事务日志失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 读取事务日志
   * @param limit 限制数量
   * @param offset 偏移量
   */
  async readTransactionLog(
    limit?: number,
    offset?: number,
  ): Promise<TransactionLogEntry[]> {
    try {
      if (!fs.existsSync(this.logFilePath)) {
        return [];
      }
      
      // 读取日志文件
      const logContent = fs.readFileSync(this.logFilePath, 'utf-8');
      const logLines = logContent.split('\n').filter(line => line.trim());
      
      // 解析日志条目
      const logEntries: TransactionLogEntry[] = [];
      
      for (const line of logLines) {
        try {
          // 解密日志数据
          let logData = line;
          if (this.options.enableEncryption && this.options.encryptionKey) {
            logData = this.decrypt(logData, this.options.encryptionKey);
          }
          
          // 解析日志条目
          const logEntry = JSON.parse(logData) as TransactionLogEntry;
          
          // 验证校验和
          if (this.options.enableChecksum && logEntry.checksum) {
            const entryWithoutChecksum = { ...logEntry };
            delete entryWithoutChecksum.checksum;
            
            const calculatedChecksum = this.calculateChecksum(
              JSON.stringify(entryWithoutChecksum),
            );
            
            if (calculatedChecksum !== logEntry.checksum) {
              this.logger.warn(`事务日志校验和不匹配: ${logEntry.transaction.id}`);
              continue;
            }
          }
          
          logEntries.push(logEntry);
        } catch (error) {
          this.logger.error(`解析事务日志条目失败: ${error.message}`, error.stack);
        }
      }
      
      // 应用分页
      if (offset !== undefined && limit !== undefined) {
        return logEntries.slice(offset, offset + limit);
      } else if (offset !== undefined) {
        return logEntries.slice(offset);
      } else if (limit !== undefined) {
        return logEntries.slice(0, limit);
      }
      
      return logEntries;
    } catch (error) {
      this.logger.error(`读取事务日志失败: ${error.message}`, error.stack);
      return [];
    }
  }
  
  /**
   * 清理过期日志
   */
  async cleanupLogs(): Promise<void> {
    try {
      const logDir = this.options.storagePath;
      const logFiles = fs.readdirSync(logDir)
        .filter(file => file.startsWith(path.basename(this.options.filename, '.log')))
        .filter(file => file.endsWith('.log'))
        .sort((a, b) => {
          const aMatch = a.match(/\.(\d+)\.log$/);
          const bMatch = b.match(/\.(\d+)\.log$/);
          
          if (!aMatch && !bMatch) {
            return 0;
          } else if (!aMatch) {
            return -1;
          } else if (!bMatch) {
            return 1;
          }
          
          return parseInt(bMatch[1], 10) - parseInt(aMatch[1], 10);
        });
      
      // 保留最新的N个日志文件
      if (logFiles.length > this.options.retentionCount) {
        const filesToDelete = logFiles.slice(this.options.retentionCount);
        
        for (const file of filesToDelete) {
          fs.unlinkSync(path.join(logDir, file));
          this.logger.debug(`已删除过期日志文件: ${file}`);
        }
      }
    } catch (error) {
      this.logger.error(`清理过期日志失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 轮转日志
   */
  private async rotateLog(): Promise<void> {
    try {
      if (!fs.existsSync(this.logFilePath)) {
        return;
      }
      
      // 获取当前时间戳
      const timestamp = new Date().getTime();
      
      // 构建新的日志文件名
      const rotatedLogPath = `${this.logFilePath}.${timestamp}`;
      
      // 重命名当前日志文件
      fs.renameSync(this.logFilePath, rotatedLogPath);
      
      // 重置当前日志大小
      this.currentLogSize = 0;
      
      this.logger.debug(`已轮转日志文件: ${this.logFilePath} -> ${rotatedLogPath}`);
      
      // 清理过期日志
      await this.cleanupLogs();
    } catch (error) {
      this.logger.error(`轮转日志失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 计算校验和
   * @param data 数据
   */
  private calculateChecksum(data: string): string {
    return crypto.createHash('sha256').update(data).digest('hex');
  }
  
  /**
   * 加密数据
   * @param data 数据
   * @param key 密钥
   */
  private encrypt(data: string, key: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key.padEnd(32).slice(0, 32)), iv);
    
    let encrypted = cipher.update(data, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    
    return `${iv.toString('hex')}:${encrypted}`;
  }
  
  /**
   * 解密数据
   * @param data 数据
   * @param key 密钥
   */
  private decrypt(data: string, key: string): string {
    const [ivHex, encryptedData] = data.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key.padEnd(32).slice(0, 32)), iv);
    
    let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
