/**
 * 光源组件
 * 表示场景中的各种光源
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
import { ShadowSystem } from './shadows/ShadowSystem';

/**
 * 光源类型枚举
 */
export enum LightType {
  AMBIENT = 'ambient',
  DIRECTIONAL = 'directional',
  POINT = 'point',
  SPOT = 'spot',
  HEMISPHERE = 'hemisphere',
  RECT_AREA = 'rectArea'
}

/**
 * 光源基础选项接口
 */
export interface LightOptions {
  /** 光源类型 */
  type: string;
  /** 光源颜色 */
  color?: THREE.ColorRepresentation;
  /** 光源强度 */
  intensity?: number;
  /** 是否投射阴影 */
  castShadow?: boolean;
}

/**
 * 环境光选项接口
 */
export interface AmbientLightOptions extends LightOptions {
  type: 'ambient';
}

/**
 * 方向光选项接口
 */
export interface DirectionalLightOptions extends LightOptions {
  type: 'directional';
  /** 阴影贴图大小 */
  shadowMapSize?: number;
  /** 阴影相机近平面 */
  shadowCameraNear?: number;
  /** 阴影相机远平面 */
  shadowCameraFar?: number;
  /** 阴影相机左平面 */
  shadowCameraLeft?: number;
  /** 阴影相机右平面 */
  shadowCameraRight?: number;
  /** 阴影相机上平面 */
  shadowCameraTop?: number;
  /** 阴影相机下平面 */
  shadowCameraBottom?: number;
  /** 阴影偏移 */
  shadowBias?: number;
  /** 阴影半径 */
  shadowRadius?: number;
}

/**
 * 点光源选项接口
 */
export interface PointLightOptions extends LightOptions {
  type: 'point';
  /** 光源距离 */
  distance?: number;
  /** 光源衰减 */
  decay?: number;
  /** 阴影贴图大小 */
  shadowMapSize?: number;
  /** 阴影相机近平面 */
  shadowCameraNear?: number;
  /** 阴影相机远平面 */
  shadowCameraFar?: number;
  /** 阴影偏移 */
  shadowBias?: number;
  /** 阴影半径 */
  shadowRadius?: number;
}

/**
 * 聚光灯选项接口
 */
export interface SpotLightOptions extends LightOptions {
  type: 'spot';
  /** 光源距离 */
  distance?: number;
  /** 光源角度 */
  angle?: number;
  /** 光源半影 */
  penumbra?: number;
  /** 光源衰减 */
  decay?: number;
  /** 阴影贴图大小 */
  shadowMapSize?: number;
  /** 阴影相机近平面 */
  shadowCameraNear?: number;
  /** 阴影相机远平面 */
  shadowCameraFar?: number;
  /** 阴影偏移 */
  shadowBias?: number;
  /** 阴影半径 */
  shadowRadius?: number;
}

/**
 * 半球光选项接口
 */
export interface HemisphereLightOptions extends LightOptions {
  type: 'hemisphere';
  /** 地面颜色 */
  groundColor?: THREE.ColorRepresentation;
}

/**
 * 矩形区域光选项接口
 */
export interface RectAreaLightOptions extends LightOptions {
  type: 'rectArea';
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
}

/**
 * 光源组件类
 */
export class Light extends Component {
  /** 组件类型 */
  public static readonly type: string = 'Light';

  /** 光源类型 */
  private lightType: LightType;

  /** Three.js光源 */
  private light: THREE.Light;

  /**
   * 创建光源组件
   * @param options 光源选项
   */
  constructor(options: LightOptions) {
    super(Light.type);

    this.lightType = options.type as LightType;

    // 创建对应类型的光源
    switch (this.lightType) {
      case LightType.AMBIENT:
        this.light = this.createAmbientLight(options as AmbientLightOptions);
        break;
      case LightType.DIRECTIONAL:
        this.light = this.createDirectionalLight(options as DirectionalLightOptions);
        break;
      case LightType.POINT:
        this.light = this.createPointLight(options as PointLightOptions);
        break;
      case LightType.SPOT:
        this.light = this.createSpotLight(options as SpotLightOptions);
        break;
      case LightType.HEMISPHERE:
        this.light = this.createHemisphereLight(options as HemisphereLightOptions);
        break;
      case LightType.RECT_AREA:
        this.light = this.createRectAreaLight(options as RectAreaLightOptions);
        break;
      default:
        throw new Error(`不支持的光源类型: ${options.type}`);
    }
  }

  /**
   * 创建环境光
   * @param options 环境光选项
   * @returns Three.js环境光
   */
  private createAmbientLight(options: AmbientLightOptions): THREE.AmbientLight {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    return new THREE.AmbientLight(color, intensity);
  }

  /**
   * 创建方向光
   * @param options 方向光选项
   * @returns Three.js方向光
   */
  private createDirectionalLight(options: DirectionalLightOptions): THREE.DirectionalLight {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    const light = new THREE.DirectionalLight(color, intensity);

    // 设置阴影
    light.castShadow = options.castShadow !== undefined ? options.castShadow : false;
    
    if (light.castShadow) {
      // 设置阴影贴图大小
      if (options.shadowMapSize !== undefined) {
        light.shadow.mapSize.width = options.shadowMapSize;
        light.shadow.mapSize.height = options.shadowMapSize;
      }

      // 设置阴影相机参数
      if (options.shadowCameraNear !== undefined) light.shadow.camera.near = options.shadowCameraNear;
      if (options.shadowCameraFar !== undefined) light.shadow.camera.far = options.shadowCameraFar;
      if (options.shadowCameraLeft !== undefined) (light.shadow.camera as THREE.OrthographicCamera).left = options.shadowCameraLeft;
      if (options.shadowCameraRight !== undefined) (light.shadow.camera as THREE.OrthographicCamera).right = options.shadowCameraRight;
      if (options.shadowCameraTop !== undefined) (light.shadow.camera as THREE.OrthographicCamera).top = options.shadowCameraTop;
      if (options.shadowCameraBottom !== undefined) (light.shadow.camera as THREE.OrthographicCamera).bottom = options.shadowCameraBottom;

      // 设置阴影偏移和半径
      if (options.shadowBias !== undefined) light.shadow.bias = options.shadowBias;
      if (options.shadowRadius !== undefined) light.shadow.radius = options.shadowRadius;
    }

    return light;
  }

  /**
   * 创建点光源
   * @param options 点光源选项
   * @returns Three.js点光源
   */
  private createPointLight(options: PointLightOptions): THREE.PointLight {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    const distance = options.distance !== undefined ? options.distance : 0;
    const decay = options.decay !== undefined ? options.decay : 2;
    
    const light = new THREE.PointLight(color, intensity, distance, decay);

    // 设置阴影
    light.castShadow = options.castShadow !== undefined ? options.castShadow : false;
    
    if (light.castShadow) {
      // 设置阴影贴图大小
      if (options.shadowMapSize !== undefined) {
        light.shadow.mapSize.width = options.shadowMapSize;
        light.shadow.mapSize.height = options.shadowMapSize;
      }

      // 设置阴影相机参数
      if (options.shadowCameraNear !== undefined) light.shadow.camera.near = options.shadowCameraNear;
      if (options.shadowCameraFar !== undefined) light.shadow.camera.far = options.shadowCameraFar;

      // 设置阴影偏移和半径
      if (options.shadowBias !== undefined) light.shadow.bias = options.shadowBias;
      if (options.shadowRadius !== undefined) light.shadow.radius = options.shadowRadius;
    }

    return light;
  }

  /**
   * 创建聚光灯
   * @param options 聚光灯选项
   * @returns Three.js聚光灯
   */
  private createSpotLight(options: SpotLightOptions): THREE.SpotLight {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    const distance = options.distance !== undefined ? options.distance : 0;
    const angle = options.angle !== undefined ? options.angle : Math.PI / 3;
    const penumbra = options.penumbra !== undefined ? options.penumbra : 0;
    const decay = options.decay !== undefined ? options.decay : 2;
    
    const light = new THREE.SpotLight(color, intensity, distance, angle, penumbra, decay);

    // 设置阴影
    light.castShadow = options.castShadow !== undefined ? options.castShadow : false;
    
    if (light.castShadow) {
      // 设置阴影贴图大小
      if (options.shadowMapSize !== undefined) {
        light.shadow.mapSize.width = options.shadowMapSize;
        light.shadow.mapSize.height = options.shadowMapSize;
      }

      // 设置阴影相机参数
      if (options.shadowCameraNear !== undefined) light.shadow.camera.near = options.shadowCameraNear;
      if (options.shadowCameraFar !== undefined) light.shadow.camera.far = options.shadowCameraFar;

      // 设置阴影偏移和半径
      if (options.shadowBias !== undefined) light.shadow.bias = options.shadowBias;
      if (options.shadowRadius !== undefined) light.shadow.radius = options.shadowRadius;
    }

    return light;
  }

  /**
   * 创建半球光
   * @param options 半球光选项
   * @returns Three.js半球光
   */
  private createHemisphereLight(options: HemisphereLightOptions): THREE.HemisphereLight {
    const skyColor = options.color !== undefined ? options.color : 0xffffff;
    const groundColor = options.groundColor !== undefined ? options.groundColor : 0x444444;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    
    return new THREE.HemisphereLight(skyColor, groundColor, intensity);
  }

  /**
   * 创建矩形区域光
   * @param options 矩形区域光选项
   * @returns Three.js矩形区域光
   */
  private createRectAreaLight(options: RectAreaLightOptions): THREE.RectAreaLight {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    const width = options.width !== undefined ? options.width : 10;
    const height = options.height !== undefined ? options.height : 10;
    
    return new THREE.RectAreaLight(color, intensity, width, height);
  }

  /**
   * 当组件附加到实体时调用
   */
  protected onAttach(): void {
    if (!this.entity) return;

    // 获取实体的变换组件
    const transform = this.entity.getTransform();
    if (transform) {
      // 将光源添加到变换的Three.js对象
      transform.getObject3D().add(this.light);

      // 如果是方向光或聚光灯，还需要添加目标
      if (this.light instanceof THREE.DirectionalLight || this.light instanceof THREE.SpotLight) {
        transform.getObject3D().add(this.light.target);
      }
    }

    // 如果是方向光，通知阴影系统
    if (this.lightType === LightType.DIRECTIONAL) {
      const shadowSystem = this.entity.getWorld()?.getSystem(ShadowSystem);
      if (shadowSystem) {
        shadowSystem.addDirectionalLight(this.entity);
      }
    }
  }

  /**
   * 当组件从实体分离时调用
   */
  protected onDetach(): void {
    // 如果是方向光，通知阴影系统
    if (this.lightType === LightType.DIRECTIONAL) {
      const shadowSystem = this.entity.getWorld()?.getSystem(ShadowSystem);
      if (shadowSystem) {
        shadowSystem.removeDirectionalLight(this.entity);
      }
    }
  }

  /**
   * 获取光源类型
   * @returns 光源类型
   */
  public getType(): LightType {
    return this.lightType;
  }

  /**
   * 获取Three.js光源
   * @returns Three.js光源
   */
  public getThreeLight(): THREE.Light {
    return this.light;
  }

  /**
   * 设置光源颜色
   * @param color 颜色
   */
  public setColor(color: THREE.ColorRepresentation): void {
    if (this.light instanceof THREE.Light) {
      this.light.color.set(color);
    }
  }

  /**
   * 设置光源强度
   * @param intensity 强度
   */
  public setIntensity(intensity: number): void {
    if (this.light instanceof THREE.Light) {
      this.light.intensity = intensity;
    }
  }

  /**
   * 设置是否投射阴影
   * @param castShadow 是否投射阴影
   */
  public setCastShadow(castShadow: boolean): void {
    if (this.light instanceof THREE.DirectionalLight || 
        this.light instanceof THREE.PointLight || 
        this.light instanceof THREE.SpotLight) {
      this.light.castShadow = castShadow;
    }
  }
}
