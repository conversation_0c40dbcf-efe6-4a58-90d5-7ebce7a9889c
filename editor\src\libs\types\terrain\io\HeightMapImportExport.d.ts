import { TerrainComponent } from '../components/TerrainComponent';
import { HeightMapFormat } from './TerrainImportExportService';
/**
 * 高度图导出选项
 */
export interface HeightMapExportOptions {
    /** 格式 */
    format: HeightMapFormat;
    /** 宽度 */
    width?: number;
    /** 高度 */
    height?: number;
    /** 是否反转Y轴 */
    flipY?: boolean;
    /** 是否应用归一化 */
    normalize?: boolean;
    /** 位深度 (8, 16, 32) */
    bitDepth?: number;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 高度图导入选项
 */
export interface HeightMapImportOptions {
    /** 是否反转Y轴 */
    flipY?: boolean;
    /** 高度缩放 */
    heightScale?: number;
    /** 是否应用平滑 */
    applySmoothing?: boolean;
    /** 平滑强度 */
    smoothingStrength?: number;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 高度图导入导出
 */
export declare class HeightMapImportExport {
    /**
     * 从高度图导入地形
     * @param terrain 地形组件
     * @param heightMap 高度图（URL、File、Blob、ImageData、Canvas、Image）
     * @param options 导入选项
     * @returns Promise，解析为是否导入成功
     */
    importHeightMap(terrain: TerrainComponent, heightMap: string | File | Blob | ImageData | HTMLCanvasElement | HTMLImageElement, options?: HeightMapImportOptions): Promise<boolean>;
    /**
     * 导出地形为高度图
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns Promise，解析为Blob
     */
    exportHeightMap(terrain: TerrainComponent, options: HeightMapExportOptions): Promise<Blob>;
    /**
     * 加载图像数据
     * @param source 图像源
     * @returns Promise，解析为ImageData
     */
    private loadImageData;
    /**
     * 加载图像
     * @param url 图像URL
     * @returns Promise，解析为HTMLImageElement
     */
    private loadImage;
    /**
     * 提取高度数据
     * @param imageData 图像数据
     * @param options 导入选项
     * @returns 高度数据
     */
    private extractHeightData;
    /**
     * 平滑高度数据
     * @param heightData 高度数据
     * @param resolution 分辨率
     * @param strength 平滑强度
     */
    private smoothHeightData;
    /**
     * 导出原始高度图
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns Blob
     */
    private exportRawHeightMap;
    /**
     * 导出ASC高度图
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns Blob
     */
    private exportASCHeightMap;
    /**
     * 导出特殊格式高度图
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns Blob
     */
    private exportSpecialHeightMap;
}
