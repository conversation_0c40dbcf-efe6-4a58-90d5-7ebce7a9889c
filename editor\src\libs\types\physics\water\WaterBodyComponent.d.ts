/**
 * 水体组件
 * 用于表示水体及其物理属性
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
/**
 * 水体类型
 */
export declare enum WaterBodyType {
    /** 湖泊 */
    LAKE = "lake",
    /** 河流 */
    RIVER = "river",
    /** 海洋 */
    OCEAN = "ocean",
    /** 瀑布 */
    WATERFALL = "waterfall",
    /** 温泉 */
    HOT_SPRING = "hot_spring",
    /** 地下湖泊 */
    UNDERGROUND_LAKE = "underground_lake",
    /** 地下河流 */
    UNDERGROUND_RIVER = "underground_river",
    /** 喷泉 */
    FOUNTAIN = "fountain",
    /** 雨水 */
    RAIN_WATER = "rain_water"
}
/**
 * 水体形状
 */
export declare enum WaterBodyShape {
    /** 平面 */
    PLANE = "plane",
    /** 立方体 */
    BOX = "box",
    /** 球体 */
    SPHERE = "sphere",
    /** 圆柱体 */
    CYLINDER = "cylinder",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 水流方向
 */
export interface WaterFlowDirection {
    /** X方向流速 */
    x: number;
    /** Y方向流速 */
    y: number;
    /** Z方向流速 */
    z: number;
}
/**
 * 水体波动参数
 */
export interface WaterWaveParams {
    /** 波动幅度 */
    amplitude: number;
    /** 波动频率 */
    frequency: number;
    /** 波动速度 */
    speed: number;
    /** 波动方向 */
    direction: {
        x: number;
        z: number;
    };
}
/**
 * 水体组件配置
 */
export interface WaterBodyConfig {
    /** 水体类型 */
    type?: WaterBodyType;
    /** 水体形状 */
    shape?: WaterBodyShape;
    /** 水体尺寸 */
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    /** 水体密度 */
    density?: number;
    /** 水体粘度 */
    viscosity?: number;
    /** 水体温度 */
    temperature?: number;
    /** 水体流向 */
    flowDirection?: WaterFlowDirection;
    /** 水体流速 */
    flowSpeed?: number;
    /** 水体波动参数 */
    waveParams?: WaterWaveParams;
    /** 是否启用浮力 */
    enableBuoyancy?: boolean;
    /** 是否启用阻力 */
    enableDrag?: boolean;
    /** 是否启用波动 */
    enableWaves?: boolean;
    /** 是否启用流动 */
    enableFlow?: boolean;
    /** 是否启用反射 */
    enableReflection?: boolean;
    /** 是否启用折射 */
    enableRefraction?: boolean;
    /** 是否启用因果波纹 */
    enableCaustics?: boolean;
    /** 是否启用水下雾效 */
    enableUnderwaterFog?: boolean;
    /** 是否启用水下扭曲 */
    enableUnderwaterDistortion?: boolean;
    /** 是否启用水体粒子 */
    enableParticles?: boolean;
    /** 是否启用 */
    enabled?: boolean;
}
/**
 * 水体组件
 */
export declare class WaterBodyComponent extends Component {
    /** 水体类型 */
    private waterType;
    /** 水体形状 */
    private shape;
    /** 水体尺寸 */
    private size;
    /** 水体密度 */
    private density;
    /** 水体粘度 */
    private viscosity;
    /** 水体温度 */
    private temperature;
    /** 水体流向 */
    private flowDirection;
    /** 水体流速 */
    private flowSpeed;
    /** 水体波动参数 */
    private waveParams;
    /** 是否启用浮力 */
    private enableBuoyancy;
    /** 是否启用阻力 */
    private enableDrag;
    /** 是否启用波动 */
    private enableWaves;
    /** 是否启用流动 */
    private enableFlow;
    /** 是否启用反射 */
    private enableReflection;
    /** 是否启用折射 */
    private enableRefraction;
    /** 是否启用因果波纹 */
    private enableCaustics;
    /** 是否启用水下雾效 */
    private enableUnderwaterFog;
    /** 是否启用水下扭曲 */
    private enableUnderwaterDistortion;
    /** 是否启用水体粒子 */
    private enableParticles;
    /** 是否已初始化 */
    private initialized;
    /** 水体网格 */
    private waterMesh;
    /** 水体材质 */
    private waterMaterial;
    /** 水面高度图 */
    private heightMap;
    /** 水面法线图 */
    private normalMap;
    /** 水流速度图 */
    private velocityMap;
    /** 水体粒子系统 */
    private particleSystem;
    /** 波动时间 */
    private waveTime;
    /** 分裂效果列表 */
    private splittingEffects;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: WaterBodyConfig);
    /**
     * 初始化水体组件
     */
    initialize(): void;
    /**
     * 创建水体网格
     */
    private createWaterMesh;
    /**
     * 创建平面几何体
     * @returns 平面几何体
     */
    private createPlaneGeometry;
    /**
     * 创建盒体几何体
     * @returns 盒体几何体
     */
    private createBoxGeometry;
    /**
     * 创建球体几何体
     * @returns 球体几何体
     */
    private createSphereGeometry;
    /**
     * 创建圆柱体几何体
     * @returns 圆柱体几何体
     */
    private createCylinderGeometry;
    /**
     * 创建自定义几何体
     * @returns 自定义几何体
     */
    private createCustomGeometry;
    /**
     * 创建水体材质
     */
    private createWaterMaterial;
    /**
     * 初始化水面高度图
     */
    private initializeHeightMap;
    /**
     * 初始化水面法线图
     */
    private initializeNormalMap;
    /**
     * 初始化水流速度图
     */
    private initializeVelocityMap;
    /**
     * 初始化粒子系统
     */
    private initializeParticleSystem;
    /**
     * 是否已初始化
     * @returns 是否已初始化
     */
    isInitialized(): boolean;
    /**
     * 获取水体类型
     * @returns 水体类型
     */
    getWaterType(): WaterBodyType;
    /**
     * 获取水体形状
     * @returns 水体形状
     */
    getShape(): WaterBodyShape;
    /**
     * 获取水体尺寸
     * @returns 水体尺寸
     */
    getSize(): {
        width: number;
        height: number;
        depth: number;
    };
    /**
     * 获取水体密度
     * @returns 水体密度
     */
    getDensity(): number;
    /**
     * 获取水体粘度
     * @returns 水体粘度
     */
    getViscosity(): number;
    /**
     * 获取水体温度
     * @returns 水体温度
     */
    getTemperature(): number;
    /**
     * 获取水体流向
     * @returns 水体流向
     */
    getFlowDirection(): WaterFlowDirection;
    /**
     * 获取水体流速
     * @returns 水体流速
     */
    getFlowSpeed(): number;
    /**
     * 获取水体波动参数
     * @returns 水体波动参数
     */
    getWaveParams(): WaterWaveParams;
    /**
     * 是否启用浮力
     * @returns 是否启用浮力
     */
    isBuoyancyEnabled(): boolean;
    /**
     * 是否启用阻力
     * @returns 是否启用阻力
     */
    isDragEnabled(): boolean;
    /**
     * 获取水体网格
     * @returns 水体网格
     */
    getWaterMesh(): THREE.Mesh | null;
    /**
     * 获取水体材质
     * @returns 水体材质
     */
    getWaterMaterial(): THREE.Material | null;
    /**
     * 获取水面高度图
     * @returns 水面高度图
     */
    getHeightMap(): Float32Array | null;
    /**
     * 设置水面高度图
     * @param heightMap 水面高度图
     */
    setHeightMap(heightMap: Float32Array): void;
    /**
     * 获取水面法线图
     * @returns 水面法线图
     */
    getNormalMap(): Float32Array | null;
    /**
     * 设置水面法线图
     * @param normalMap 水面法线图
     */
    setNormalMap(normalMap: Float32Array): void;
    /**
     * 获取水流速度图
     * @returns 水流速度图
     */
    getVelocityMap(): Float32Array | null;
    /**
     * 设置水流速度图
     * @param velocityMap 水流速度图
     */
    setVelocityMap(velocityMap: Float32Array): void;
    /**
     * 获取粒子系统
     * @returns 粒子系统
     */
    getParticleSystem(): any;
    /**
     * 设置水体尺寸
     * @param size 水体尺寸
     */
    setSize(size: {
        width: number;
        height: number;
        depth: number;
    }): void;
    /**
     * 设置水体位置
     * @param position 水体位置
     */
    setPosition(position: THREE.Vector3): void;
    /**
     * 设置水体旋转
     * @param rotation 水体旋转
     */
    setRotation(rotation: THREE.Euler): void;
    /**
     * 设置水体颜色
     * @param color 水体颜色
     */
    setColor(color: THREE.Color): void;
    /**
     * 设置水体透明度
     * @param opacity 水体透明度
     */
    setOpacity(opacity: number): void;
    /**
     * 设置水体流速
     * @param speed 流速
     */
    setFlowSpeed(speed: number): void;
    /**
     * 设置水体流向
     * @param direction 流向（可以是 THREE.Vector3 或 WaterFlowDirection）
     */
    setFlowDirection(direction: THREE.Vector3 | WaterFlowDirection): void;
    /**
     * 设置水体密度
     * @param density 密度
     */
    setDensity(density: number): void;
    /**
     * 设置水体粘度
     * @param viscosity 粘度
     */
    setViscosity(viscosity: number): void;
    /**
     * 设置水体波动参数
     * @param waveParams 波动参数
     */
    setWaveParams(waveParams: WaterWaveParams): void;
    /**
     * 设置水体分辨率
     * @param resolution 分辨率
     */
    setResolution(resolution: number): void;
    /**
     * 设置水体波动质量
     * @param quality 波动质量 ('low' | 'medium' | 'high')
     */
    setWaveQuality(quality: string): void;
    /**
     * 设置是否启用反射
     * @param enabled 是否启用
     */
    setEnableReflection(enabled: boolean): void;
    /**
     * 设置是否启用折射
     * @param enabled 是否启用
     */
    setEnableRefraction(enabled: boolean): void;
    /**
     * 设置是否启用因果波纹
     * @param enabled 是否启用
     */
    setEnableCaustics(enabled: boolean): void;
    /**
     * 设置是否启用泡沫
     * @param enabled 是否启用
     */
    setEnableFoam(enabled: boolean): void;
    /**
     * 设置是否启用水下雾效
     * @param enabled 是否启用
     */
    setEnableUnderwaterFog(enabled: boolean): void;
    /**
     * 设置是否启用水下扭曲
     * @param enabled 是否启用
     */
    setEnableUnderwaterDistortion(enabled: boolean): void;
    /**
     * 获取水体位置
     * @returns 水体位置
     */
    getPosition(): THREE.Vector3;
    /**
     * 获取水体颜色
     * @returns 水体颜色
     */
    getColor(): THREE.Color;
    /**
     * 获取水体透明度
     * @returns 水体透明度
     */
    getOpacity(): number;
    /**
     * 获取水体旋转
     * @returns 水体旋转
     */
    getRotation(): THREE.Quaternion;
    /**
     * 获取水体分辨率
     * @returns 水体分辨率
     */
    getResolution(): number;
    /**
     * 获取波动时间
     * @returns 波动时间
     */
    getWaveTime(): number;
    /**
     * 更新波动时间
     * @param deltaTime 时间增量
     */
    updateWaveTime(deltaTime: number): void;
    /**
     * 更新水体网格
     */
    updateWaterMesh(): void;
    /**
     * 添加波纹
     * @param position 位置
     * @param radius 半径
     * @param strength 强度
     */
    addRipple(position: THREE.Vector3, radius: number, strength: number): void;
    /**
     * 添加分裂效果
     * @param effect 分裂效果
     */
    addSplittingEffect(effect: any): void;
    /**
     * 获取分裂效果列表
     * @returns 分裂效果列表
     */
    getSplittingEffects(): any[];
    /**
     * 更新分裂效果
     * @param deltaTime 时间增量
     */
    updateSplittingEffects(deltaTime: number): void;
}
