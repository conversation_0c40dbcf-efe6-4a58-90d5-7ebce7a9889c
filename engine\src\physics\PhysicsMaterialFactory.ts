/**
 * 物理材质工厂
 * 用于创建和管理物理材质
 */
import * as CANNON from 'cannon-es';

/**
 * 物理材质工厂
 */
export class PhysicsMaterialFactory {
  /** 材质映射 */
  private static materials: Map<string, CANNON.Material> = new Map();
  
  /** 接触材质映射 */
  private static contactMaterials: Map<string, CANNON.ContactMaterial> = new Map();
  
  /** 默认材质 */
  private static defaultMaterial: CANNON.Material;
  
  /**
   * 初始化物理材质工厂
   */
  public static initialize(): void {
    // 创建默认材质
    this.defaultMaterial = new CANNON.Material('default');
    this.materials.set('default', this.defaultMaterial);
    
    // 创建常用材质
    this.createMaterial('metal', 0.3, 0.3);
    this.createMaterial('wood', 0.4, 0.2);
    this.createMaterial('plastic', 0.5, 0.4);
    this.createMaterial('rubber', 0.7, 0.1);
    this.createMaterial('ice', 0.05, 0.9);
    this.createMaterial('glass', 0.2, 0.8);
    this.createMaterial('concrete', 0.8, 0.2);
    this.createMaterial('bouncy', 0.5, 1.5);
  }
  
  /**
   * 创建材质
   * @param name 材质名称
   * @param friction 摩擦力
   * @param restitution 恢复系数
   * @returns 材质
   */
  public static createMaterial(
    name: string,
    friction: number = 0.3,
    restitution: number = 0.3
  ): CANNON.Material {
    // 如果已存在，则返回现有材质
    if (this.materials.has(name)) {
      return this.materials.get(name)!;
    }
    
    // 创建新材质
    const material = new CANNON.Material(name);
    this.materials.set(name, material);
    
    // 创建与默认材质的接触材质
    this.createContactMaterial(
      material,
      this.defaultMaterial,
      friction,
      restitution
    );
    
    return material;
  }
  
  /**
   * 获取材质
   * @param name 材质名称
   * @returns 材质
   */
  public static getMaterial(name: string): CANNON.Material {
    // 如果不存在，则返回默认材质
    if (!this.materials.has(name)) {
      console.warn(`材质 "${name}" 不存在，使用默认材质`);
      return this.defaultMaterial;
    }
    
    return this.materials.get(name)!;
  }
  
  /**
   * 创建接触材质
   * @param materialA 材质A
   * @param materialB 材质B
   * @param friction 摩擦力
   * @param restitution 恢复系数
   * @param options 其他选项
   * @returns 接触材质
   */
  public static createContactMaterial(
    materialA: CANNON.Material,
    materialB: CANNON.Material,
    friction: number = 0.3,
    restitution: number = 0.3,
    options: {
      contactEquationStiffness?: number;
      contactEquationRelaxation?: number;
      frictionEquationStiffness?: number;
      frictionEquationRelaxation?: number;
    } = {}
  ): CANNON.ContactMaterial {
    // 创建唯一键
    const key = `${materialA.name}_${materialB.name}`;
    
    // 如果已存在，则返回现有接触材质
    if (this.contactMaterials.has(key)) {
      return this.contactMaterials.get(key)!;
    }
    
    // 创建新接触材质
    const contactMaterial = new CANNON.ContactMaterial(materialA, materialB, {
      friction,
      restitution,
      ...options
    });
    
    this.contactMaterials.set(key, contactMaterial);
    
    return contactMaterial;
  }
  
  /**
   * 获取接触材质
   * @param materialA 材质A
   * @param materialB 材质B
   * @returns 接触材质
   */
  public static getContactMaterial(
    materialA: CANNON.Material,
    materialB: CANNON.Material
  ): CANNON.ContactMaterial | null {
    // 创建唯一键
    const key = `${materialA.name}_${materialB.name}`;
    
    // 如果不存在，则尝试反向键
    if (!this.contactMaterials.has(key)) {
      const reverseKey = `${materialB.name}_${materialA.name}`;
      
      if (this.contactMaterials.has(reverseKey)) {
        return this.contactMaterials.get(reverseKey)!;
      }
      
      return null;
    }
    
    return this.contactMaterials.get(key)!;
  }
  
  /**
   * 添加接触材质到物理世界
   * @param world 物理世界
   * @param contactMaterial 接触材质
   */
  public static addContactMaterialToWorld(
    world: CANNON.World,
    contactMaterial: CANNON.ContactMaterial
  ): void {
    world.addContactMaterial(contactMaterial);
  }
  
  /**
   * 添加所有接触材质到物理世界
   * @param world 物理世界
   */
  public static addAllContactMaterialsToWorld(world: CANNON.World): void {
    for (const contactMaterial of this.contactMaterials.values()) {
      world.addContactMaterial(contactMaterial);
    }
  }
  
  /**
   * 获取默认材质
   * @returns 默认材质
   */
  public static getDefaultMaterial(): CANNON.Material {
    return this.defaultMaterial;
  }
  
  /**
   * 清除所有材质
   */
  public static clear(): void {
    this.materials.clear();
    this.contactMaterials.clear();
    
    // 重新创建默认材质
    this.defaultMaterial = new CANNON.Material('default');
    this.materials.set('default', this.defaultMaterial);
  }
}
