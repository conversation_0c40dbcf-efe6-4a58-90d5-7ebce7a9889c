/**
 * 输入性能工具
 * 提供事件节流、防抖等性能优化功能
 */
/**
 * 节流选项
 */
export interface ThrottleOptions {
    /** 节流时间间隔（毫秒） */
    interval: number;
    /** 是否在开始时执行 */
    leading?: boolean;
    /** 是否在结束时执行 */
    trailing?: boolean;
}
/**
 * 防抖选项
 */
export interface DebounceOptions {
    /** 防抖等待时间（毫秒） */
    wait: number;
    /** 是否在开始时执行 */
    leading?: boolean;
    /** 是否在结束时执行 */
    trailing?: boolean;
    /** 最大等待时间（毫秒） */
    maxWait?: number;
}
/**
 * 节流函数
 * 限制函数在一定时间内只能执行一次
 * @param func 要节流的函数
 * @param options 节流选项
 * @returns 节流后的函数
 */
export declare function throttle<T extends (...args: any[]) => any>(func: T, options: ThrottleOptions): (...args: Parameters<T>) => ReturnType<T> | undefined;
/**
 * 防抖函数
 * 将多次连续调用合并为一次
 * @param func 要防抖的函数
 * @param options 防抖选项
 * @returns 防抖后的函数
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, options: DebounceOptions): (...args: Parameters<T>) => ReturnType<T> | undefined;
