/**
 * 优化的抓取系统
 * 用于处理大量对象同时被抓取的情况
 */
import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { GrabbableComponent, GrabType } from '../components/GrabbableComponent';
import { GrabberComponent } from '../components/GrabberComponent';
import { GrabbedComponent } from '../components/GrabbedComponent';
import { ThrowableComponent } from '../components/ThrowableComponent';
import { GestureGrabComponent } from '../components/GestureGrabComponent';
import { XRGrabComponent } from '../components/XRGrabComponent';
import { GrabNetworkComponent } from '../components/GrabNetworkComponent';
/**
 * 优化的抓取系统配置
 */
export interface OptimizedGrabSystemConfig {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 是否启用物理抓取 */
    enablePhysicsGrab?: boolean;
    /** 是否启用网络同步 */
    enableNetworkSync?: boolean;
    /** 是否启用手势抓取 */
    enableGestureGrab?: boolean;
    /** 是否启用XR抓取 */
    enableXRGrab?: boolean;
    /** 默认抓取类型 */
    defaultGrabType?: GrabType;
    /** 是否启用空间分区 */
    enableSpatialPartitioning?: boolean;
    /** 空间分区网格大小 */
    spatialGridSize?: number;
    /** 是否启用对象池 */
    enableObjectPool?: boolean;
    /** 是否启用多线程 */
    enableMultithreading?: boolean;
    /** 是否启用LOD */
    enableLOD?: boolean;
    /** 更新频率（帧数） */
    updateFrequency?: number;
}
/**
 * 优化的抓取系统
 */
export declare class OptimizedGrabSystem extends System {
    /** 系统名称 */
    static readonly NAME: string;
    /** 系统配置 */
    private config;
    /** 输入系统引用 */
    private inputSystem?;
    /** 物理系统引用 */
    private physicsSystem?;
    /** 可抓取组件映射 */
    private grabbableComponents;
    /** 抓取者组件映射 */
    private grabberComponents;
    /** 被抓取组件映射 */
    private grabbedComponents;
    /** 可抛掷组件映射 */
    private throwableComponents;
    /** 手势抓取组件映射 */
    private gestureGrabComponents;
    /** XR抓取组件映射 */
    private xrGrabComponents;
    /** 网络抓取组件映射 */
    private grabNetworkComponents;
    /** 空间分区网格 */
    private spatialGrid;
    /** 临时向量 - 用于计算 - 预留功能 */
    private tempVector;
    /** 临时四元数 - 用于计算 - 预留功能 */
    private tempQuaternion;
    /** 临时矩阵 - 用于计算 - 预留功能 */
    private tempMatrix;
    /** 原始物理体类型映射 - 用于恢复物理体类型 - 预留功能 */
    private originalBodyTypes;
    /** 事件发射器 */
    private eventEmitter;
    /** 帧计数器 */
    private frameCount;
    /** 工作线程 */
    private worker?;
    /** 对象池 */
    private vectorPool?;
    private quaternionPool?;
    private matrixPool?;
    /** LOD级别映射 */
    private lodLevels;
    /**
     * 构造函数
     * @param world 世界引用
     * @param config 系统配置
     */
    constructor(world: World, config?: OptimizedGrabSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化对象池
     */
    private initObjectPools;
    /**
     * 初始化工作线程
     */
    private initWorker;
    /**
     * 注册可抓取组件
     * @param entity 实体
     * @param component 可抓取组件
     */
    registerGrabbableComponent(entity: Entity, component: GrabbableComponent): void;
    /**
     * 注册抓取者组件
     * @param entity 实体
     * @param component 抓取者组件
     */
    registerGrabberComponent(entity: Entity, component: GrabberComponent): void;
    /**
     * 注册被抓取组件
     * @param entity 实体
     * @param component 被抓取组件
     */
    registerGrabbedComponent(entity: Entity, component: GrabbedComponent): void;
    /**
     * 注册可抛掷组件
     * @param entity 实体
     * @param component 可抛掷组件
     */
    registerThrowableComponent(entity: Entity, component: ThrowableComponent): void;
    /**
     * 注册手势抓取组件
     * @param entity 实体
     * @param component 手势抓取组件
     */
    registerGestureGrabComponent(entity: Entity, component: GestureGrabComponent): void;
    /**
     * 注册XR抓取组件
     * @param entity 实体
     * @param component XR抓取组件
     */
    registerXRGrabComponent(entity: Entity, component: XRGrabComponent): void;
    /**
     * 注册网络抓取组件
     * @param entity 实体
     * @param component 网络抓取组件
     */
    registerGrabNetworkComponent(entity: Entity, component: GrabNetworkComponent): void;
    /**
     * 取消注册可抓取组件
     * @param entity 实体
     */
    unregisterGrabbableComponent(entity: Entity): void;
    /**
     * 取消注册抓取者组件
     * @param entity 实体
     */
    unregisterGrabberComponent(entity: Entity): void;
    /**
     * 取消注册被抓取组件
     * @param entity 实体
     */
    unregisterGrabbedComponent(entity: Entity): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 处理输入
     */
    private handleInput;
    /**
     * 更新被抓取实体
     */
    private updateGrabbedEntities;
    /**
     * 更新直接抓取
     * @param entity 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    private updateDirectGrab;
    /**
     * 更新距离抓取
     * @param entity 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    private updateDistanceGrab;
    /**
     * 更新弹簧抓取
     * @param entity 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    private updateSpringGrab;
    /**
     * 获取手的位置
     * @param grabber 抓取者
     * @param hand 抓取手
     * @returns 手的位置
     */
    private getHandPosition;
    /**
     * 获取手的旋转
     * @param grabber 抓取者
     * @param hand 抓取手
     * @returns 手的旋转
     */
    private getHandRotation;
    /**
     * 获取抓取方向
     * @param grabber 抓取者
     * @param hand 抓取手
     * @returns 抓取方向
     */
    private getGrabDirection;
    /**
     * 添加实体到空间网格
     * @param entity 实体
     */
    private addEntityToSpatialGrid;
    /**
     * 从空间网格中移除实体
     * @param entity 实体
     */
    private removeEntityFromSpatialGrid;
    /**
     * 更新空间网格
     */
    private updateSpatialGrid;
    /**
     * 获取网格坐标
     * @param position 位置
     * @returns 网格坐标
     */
    private getGridCoordinates;
    /**
     * 获取网格键
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     * @returns 网格键
     */
    private getGridKey;
    /**
     * 更新LOD级别
     */
    private updateLODLevels;
    /**
     * 获取相机位置
     * @returns 相机位置
     */
    private getCameraPosition;
    /**
     * 更新手势抓取组件
     */
    private updateGestureGrabComponents;
    /**
     * 更新XR抓取组件
     */
    private updateXRGrabComponents;
    /**
     * 更新网络同步
     */
    private updateNetworkSync;
    /**
     * 销毁系统
     */
    destroy(): void;
}
