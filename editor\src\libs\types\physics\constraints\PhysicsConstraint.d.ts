/**
 * 物理约束基类
 * 用于创建物理约束
 */
import * as CANNON from 'cannon-es';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
/**
 * 物理约束类型
 */
export declare enum ConstraintType {
    /** 点对点约束 */
    POINT_TO_POINT = "pointToPoint",
    /** 铰链约束 */
    HINGE = "hinge",
    /** 距离约束 */
    DISTANCE = "distance",
    /** 锁定约束 */
    LOCK = "lock",
    /** 弹簧约束 */
    SPRING = "spring",
    /** 圆锥扭转约束 */
    CONE_TWIST = "coneTwist",
    /** 滑动约束 */
    SLIDER = "slider",
    /** 固定约束 */
    FIXED = "fixed",
    /** 车轮约束 */
    WHEEL = "wheel"
}
/**
 * 物理约束基类
 */
export declare abstract class PhysicsConstraint extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 约束类型 */
    protected constraintType: ConstraintType;
    /** CANNON.js约束 */
    protected constraint: CANNON.Constraint | null;
    /** 物理世界 */
    protected world: CANNON.World | null;
    /** 是否已初始化 */
    protected initialized: boolean;
    /** 目标实体 */
    protected targetEntity: Entity | null;
    /** 碰撞启用 */
    protected collideConnected: boolean;
    /** 是否启用 */
    protected enabled: boolean;
    /**
     * 创建物理约束
     * @param type 约束类型
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    constructor(type: ConstraintType, targetEntity?: Entity | null, options?: any);
    /**
     * 初始化约束
     * @param world 物理世界
     */
    initialize(world: CANNON.World): void;
    /**
     * 创建约束
     * 子类必须实现此方法
     */
    protected abstract createConstraint(): void;
    /**
     * 获取源实体的物理体
     * @returns 源实体的物理体
     */
    protected getSourceBody(): CANNON.Body | null;
    /**
     * 获取目标实体的物理体
     * @returns 目标实体的物理体
     */
    protected getTargetBody(): CANNON.Body | null;
    /**
     * 设置目标实体
     * @param entity 目标实体
     */
    setTargetEntity(entity: Entity | null): void;
    /**
     * 获取目标实体
     * @returns 目标实体
     */
    getTargetEntity(): Entity | null;
    /**
     * 获取约束类型
     * @returns 约束类型
     */
    getConstraintType(): ConstraintType;
    /**
     * 获取CANNON.js约束
     * @returns CANNON.js约束
     */
    getCannonConstraint(): CANNON.Constraint | null;
    /**
     * 启用约束
     */
    enable(): void;
    /**
     * 禁用约束
     */
    disable(): void;
    /**
     * 是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 设置是否允许连接的物体之间碰撞
     * @param collide 是否允许碰撞
     */
    setCollideConnected(collide: boolean): void;
    /**
     * 是否允许连接的物体之间碰撞
     * @returns 是否允许碰撞
     */
    isCollideConnected(): boolean;
    /**
     * 销毁约束
     */
    dispose(): void;
}
