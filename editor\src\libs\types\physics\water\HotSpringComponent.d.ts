/**
 * 温泉组件
 * 用于表示温泉及其特殊物理属性和效果
 */
import * as THREE from 'three';
import { WaterBodyComponent } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
/**
 * 温泉配置
 */
export interface HotSpringConfig {
    /** 温泉类型 */
    hotSpringType?: HotSpringType;
    /** 温泉宽度 */
    width?: number;
    /** 温泉高度 */
    height?: number;
    /** 温泉深度 */
    depth?: number;
    /** 温泉位置 */
    position?: THREE.Vector3;
    /** 温泉旋转 */
    rotation?: THREE.Euler;
    /** 温泉颜色 */
    color?: THREE.Color;
    /** 温泉不透明度 */
    opacity?: number;
    /** 温泉温度 */
    temperature?: number;
    /** 温泉波动强度 */
    waveAmplitude?: number;
    /** 温泉波动频率 */
    waveFrequency?: number;
    /** 温泉波动速度 */
    waveSpeed?: number;
    /** 是否启用气泡效果 */
    enableBubbleEffect?: boolean;
    /** 气泡效果强度 */
    bubbleEffectStrength?: number;
    /** 气泡大小范围 */
    bubbleSizeRange?: [number, number];
    /** 气泡速度范围 */
    bubbleSpeedRange?: [number, number];
    /** 气泡密度 */
    bubbleDensity?: number;
    /** 气泡分布范围 */
    bubbleDistributionRadius?: number;
    /** 是否启用气泡爆裂效果 */
    enableBubbleBurstEffect?: boolean;
    /** 气泡爆裂效果强度 */
    bubbleBurstEffectStrength?: number;
    /** 是否启用水蒸气效果 */
    enableSteamEffect?: boolean;
    /** 水蒸气效果强度 */
    steamEffectStrength?: number;
    /** 水蒸气颜色 */
    steamColor?: THREE.Color;
    /** 水蒸气密度 */
    steamDensity?: number;
    /** 水蒸气大小范围 */
    steamSizeRange?: [number, number];
    /** 水蒸气速度范围 */
    steamSpeedRange?: [number, number];
    /** 水蒸气上升高度 */
    steamRiseHeight?: number;
    /** 是否启用声音效果 */
    enableSoundEffect?: boolean;
    /** 声音效果音量 */
    soundEffectVolume?: number;
    /** 是否启用热扩散效果 */
    enableHeatDiffusion?: boolean;
    /** 热扩散范围 */
    heatDiffusionRange?: number;
    /** 是否启用矿物质效果 */
    enableMineralEffect?: boolean;
    /** 矿物质颜色 */
    mineralColor?: THREE.Color;
}
/**
 * 温泉类型
 */
export declare enum HotSpringType {
    /** 标准温泉 */
    STANDARD = "standard",
    /** 高温温泉 */
    HIGH_TEMPERATURE = "high_temperature",
    /** 低温温泉 */
    LOW_TEMPERATURE = "low_temperature",
    /** 大型温泉 */
    LARGE = "large",
    /** 小型温泉 */
    SMALL = "small",
    /** 硫磺温泉 */
    SULFUR = "sulfur",
    /** 矿物质温泉 */
    MINERAL = "mineral",
    /** 地下温泉 */
    UNDERGROUND = "underground"
}
/**
 * 温泉组件
 */
export declare class HotSpringComponent extends WaterBodyComponent {
    /** 温泉类型 */
    private hotSpringType;
    /** 温泉温度 */
    private hotSpringTemperature;
    /** 温泉波动强度 */
    private waveAmplitude;
    /** 温泉波动频率 */
    private waveFrequency;
    /** 温泉波动速度 */
    private waveSpeed;
    /** 是否启用气泡效果 */
    private enableBubbleEffect;
    /** 气泡效果强度 */
    private bubbleEffectStrength;
    /** 气泡大小范围 */
    private bubbleSizeRange;
    /** 气泡速度范围 */
    private bubbleSpeedRange;
    /** 气泡密度 */
    private bubbleDensity;
    /** 气泡分布范围 */
    private bubbleDistributionRadius;
    /** 是否启用气泡爆裂效果 */
    private enableBubbleBurstEffect;
    /** 气泡爆裂效果强度 */
    private bubbleBurstEffectStrength;
    /** 是否启用水蒸气效果 */
    private enableSteamEffect;
    /** 水蒸气效果强度 */
    private steamEffectStrength;
    /** 水蒸气颜色 */
    private steamColor;
    /** 水蒸气密度 */
    private steamDensity;
    /** 水蒸气大小范围 */
    private steamSizeRange;
    /** 水蒸气速度范围 */
    private steamSpeedRange;
    /** 水蒸气上升高度 */
    private steamRiseHeight;
    /** 是否启用声音效果 */
    private enableSoundEffect;
    /** 声音效果音量 */
    private soundEffectVolume;
    /** 是否启用热扩散效果 */
    private enableHeatDiffusion;
    /** 热扩散范围 */
    private heatDiffusionRange;
    /** 是否启用矿物质效果 */
    private enableMineralEffect;
    /** 矿物质颜色 */
    private mineralColor;
    /** 音频源 */
    private audioSource;
    /** 矿物质边缘 */
    private mineralEdge;
    /** 热扩散区域 */
    private heatDiffusionArea;
    /** 气泡粒子组 */
    private bubbleParticles;
    /** 水蒸气粒子组 */
    private steamParticles;
    /** 粒子更新时间 */
    private particleUpdateTime;
    /** 温泉波动时间 */
    private hotSpringWaveTime;
    /** 热扩散影响的实体列表 */
    private affectedEntities;
    /**
     * 创建温泉组件
     * @param entity 实体
     * @param config 温泉配置
     */
    constructor(entity: Entity, config?: HotSpringConfig);
    /**
     * 应用配置
     * @param config 温泉配置
     */
    private applyConfig;
    /**
     * 初始化温泉组件
     */
    initialize(): void;
    /**
     * 初始化音频
     */
    private initializeAudio;
    /**
     * 初始化粒子系统
     */
    private initializeParticleSystems;
    /**
     * 创建气泡粒子
     */
    private createBubbleParticles;
    /**
     * 创建水蒸气粒子
     */
    private createSteamParticles;
    /**
     * 创建矿物质边缘
     */
    private createMineralEdge;
    /**
     * 创建热扩散区域
     */
    private createHeatDiffusionArea;
    /**
     * 更新温泉组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新粒子效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateParticleEffects;
    /**
     * 更新气泡粒子
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateBubbleParticles;
    /**
     * 重置气泡粒子
     */
    private resetBubbleParticle;
    /**
     * 更新水蒸气粒子
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateSteamParticles;
    /**
     * 重置水蒸气粒子
     */
    private resetSteamParticle;
    /**
     * 更新热扩散效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateHeatDiffusion;
    /**
     * 应用热效果
     * @param entity 实体
     * @param strength 强度
     * @param deltaTime 帧间隔时间（秒）
     */
    private applyHeatEffect;
    /**
     * 更新热扩散区域视觉效果
     * @param _deltaTime 帧间隔时间（秒）
     */
    private updateHeatDiffusionVisuals;
    /**
     * 更新音频
     * @param _deltaTime 帧间隔时间（秒）
     */
    private updateAudio;
    /**
     * 更新波动效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaveEffects;
    /**
     * 更新水面波动
     * @param _deltaTime 帧间隔时间（秒）
     */
    private updateWaterSurfaceWaves;
    /**
     * 更新矿物质边缘波动
     * @param _deltaTime 帧间隔时间（秒）
     */
    private updateMineralEdgeWaves;
    /**
     * 销毁温泉组件
     */
    destroy(): void;
    /**
     * 销毁粒子系统
     */
    private destroyParticleSystems;
    /**
     * 获取温泉温度
     * @returns 温泉温度（摄氏度）
     */
    getTemperature(): number;
    /**
     * 设置温泉温度
     * @param temperature 温泉温度（摄氏度）
     */
    setTemperature(temperature: number): void;
    /**
     * 获取波动强度
     * @returns 波动强度
     */
    getWaveAmplitude(): number;
    /**
     * 设置波动强度
     * @param amplitude 波动强度
     */
    setWaveAmplitude(amplitude: number): void;
    /**
     * 获取波动频率
     * @returns 波动频率
     */
    getWaveFrequency(): number;
    /**
     * 设置波动频率
     * @param frequency 波动频率
     */
    setWaveFrequency(frequency: number): void;
    /**
     * 获取波动速度
     * @returns 波动速度
     */
    getWaveSpeed(): number;
    /**
     * 设置波动速度
     * @param speed 波动速度
     */
    setWaveSpeed(speed: number): void;
    /**
     * 获取温泉类型
     * @returns 温泉类型
     */
    getHotSpringType(): HotSpringType;
    /**
     * 设置温泉类型
     * @param type 温泉类型
     */
    setHotSpringType(type: HotSpringType): void;
}
