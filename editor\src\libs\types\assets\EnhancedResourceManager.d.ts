import { EventEmitter } from '../utils/EventEmitter';
import { AssetType, ResourceState } from './ResourceManager';
import { LoaderOptions } from './EnhancedAssetLoader';
import { EnhancedResourceVersionManager } from './EnhancedResourceVersionManager';
/**
 * 资源信息
 */
export interface ResourceInfo {
    /** 资源ID */
    id: string;
    /** 资源类型 */
    type: AssetType;
    /** 资源URL */
    url: string;
    /** 资源数据 */
    data: any;
    /** 资源状态 */
    state: ResourceState;
    /** 引用计数 */
    refCount: number;
    /** 上次访问时间 */
    lastAccessTime: number;
    /** 资源大小（字节） */
    size: number;
    /** 加载错误 */
    error?: Error;
    /** 资源元数据 */
    metadata?: Record<string, any>;
    /** 资源优先级 */
    priority?: number;
    /** 资源组 */
    group?: string;
    /** 资源标签 */
    tags?: string[];
}
/**
 * 增强资源管理器选项
 */
export interface EnhancedResourceManagerOptions {
    /** 最大缓存大小（字节） */
    maxCacheSize?: number;
    /** 缓存清理阈值（0-1） */
    cleanupThreshold?: number;
    /** 缓存清理间隔（毫秒） */
    cleanupInterval?: number;
    /** 是否启用自动清理 */
    autoCleanup?: boolean;
    /** 是否启用预加载 */
    enablePreload?: boolean;
    /** 最大并发加载数 */
    maxConcurrentLoads?: number;
    /** 加载重试次数 */
    retryCount?: number;
    /** 重试延迟（毫秒） */
    retryDelay?: number;
    /** 加载超时（毫秒） */
    loadTimeout?: number;
    /** 是否启用资源压缩 */
    enableCompression?: boolean;
    /** 是否启用资源版本控制 */
    enableVersioning?: boolean;
    /** 资源版本查询参数名 */
    versionQueryParam?: string;
    /** 资源版本 */
    resourceVersion?: string;
    /** 加载器选项 */
    loaderOptions?: LoaderOptions;
    /** 是否启用调试模式 */
    debug?: boolean;
}
/**
 * 增强资源管理器
 */
export declare class EnhancedResourceManager extends EventEmitter {
    /** 资源映射 */
    private resources;
    /** 加载中的资源 */
    private loadingResources;
    /** 最大缓存大小（字节） */
    private maxCacheSize;
    /** 当前缓存大小（字节） */
    private currentCacheSize;
    /** 缓存清理阈值（0-1） */
    private cleanupThreshold;
    /** 缓存清理间隔（毫秒） */
    private cleanupInterval;
    /** 是否启用自动清理 */
    private autoCleanup;
    /** 是否启用预加载 */
    private enablePreload;
    /** 最大并发加载数 */
    private maxConcurrentLoads;
    /** 当前并发加载数 */
    private currentConcurrentLoads;
    /** 加载队列 */
    private loadQueue;
    /** 加载重试次数 */
    private retryCount;
    /** 重试延迟（毫秒） */
    private retryDelay;
    /** 加载超时（毫秒） */
    private loadTimeout;
    /** 是否启用资源压缩 */
    private enableCompression;
    /** 是否启用资源版本控制 */
    private enableVersioning;
    /** 资源版本查询参数名 */
    private versionQueryParam;
    /** 资源版本 */
    private resourceVersion;
    /** 资产加载器 */
    private loader;
    /** 清理定时器ID */
    private cleanupTimerId;
    /** 是否已初始化 */
    private initialized;
    /** 是否启用调试模式 */
    private debug;
    /** 资源版本管理器 */
    private versionManager;
    /**
     * 创建增强资源管理器实例
     * @param options 资源管理器选项
     */
    constructor(options?: EnhancedResourceManagerOptions);
    /**
     * 初始化资源管理器
     */
    initialize(): void;
    /**
     * 加载资源
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @param priority 资源优先级（0-100，数值越大优先级越高）
     * @returns Promise，解析为资源数据
     */
    load(id: string, type: AssetType, url: string, priority?: number): Promise<any>;
    /**
     * 实际加载资源
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @param retryCount 当前重试次数
     * @returns Promise，解析为资源数据
     */
    private loadResource;
    /**
     * 处理URL（添加版本号等）
     * @param url 原始URL
     * @returns 处理后的URL
     */
    private processUrl;
    /**
     * 处理队列中的下一个加载请求
     */
    private processNextQueuedLoad;
    /**
     * 更新资源状态
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @param state 资源状态
     * @param data 资源数据
     * @param error 加载错误
     */
    private updateResourceState;
    /**
     * 计算资源大小（字节）
     * @param data 资源数据
     * @returns 资源大小
     */
    private calculateResourceSize;
    /**
     * 释放资源
     * @param id 资源ID
     * @returns 是否成功释放
     */
    release(id: string): boolean;
    /**
     * 释放资源数据内存
     * @param data 资源数据
     */
    private disposeResourceData;
    /**
     * 获取资源
     * @param id 资源ID
     * @returns 资源信息，如果不存在则返回null
     */
    getResource(id: string): ResourceInfo | null;
    /**
     * 获取资源数据
     * @param id 资源ID
     * @returns 资源数据，如果不存在或未加载则返回null
     */
    getResourceData(id: string): any;
    /**
     * 预加载资源
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @param priority 资源优先级
     * @returns Promise，解析为资源数据
     */
    preload(id: string, type: AssetType, url: string, priority?: number): Promise<any>;
    /**
     * 批量预加载资源
     * @param resources 资源数组
     * @param onProgress 进度回调
     * @returns Promise，解析为资源数据映射
     */
    preloadBatch(resources: Array<{
        id: string;
        type: AssetType;
        url: string;
        priority?: number;
    }>, onProgress?: (loaded: number, total: number) => void): Promise<Map<string, any>>;
    /**
     * 清理缓存
     */
    cleanup(): void;
    /**
     * 格式化大小
     * @param bytes 字节数
     * @returns 格式化后的大小字符串
     */
    private formatSize;
    /**
     * 启动清理定时器
     */
    private startCleanupTimer;
    /**
     * 停止清理定时器
     */
    private stopCleanupTimer;
    /**
     * 清空缓存
     */
    clearCache(): void;
    /**
     * 获取缓存统计信息
     * @returns 缓存统计信息
     */
    getCacheStats(): {
        totalResources: number;
        loadedResources: number;
        errorResources: number;
        totalSize: number;
        maxSize: number;
        usagePercentage: number;
    };
    /**
     * 获取资源版本管理器
     * @returns 资源版本管理器
     */
    getVersionManager(): EnhancedResourceVersionManager;
    /**
     * 创建资源版本
     * @param resourceId 资源ID
     * @param description 版本描述
     * @param tags 版本标签
     * @returns 创建的版本ID
     */
    createResourceVersion(resourceId: string, description?: string, tags?: string[]): string | null;
    /**
     * 回滚到指定版本
     * @param resourceId 资源ID
     * @param versionId 版本ID
     * @returns 是否成功回滚
     */
    rollbackToVersion(resourceId: string, versionId: string): boolean;
    /**
     * 计算资源哈希
     * @param data 资源数据
     * @returns 资源哈希
     */
    private calculateResourceHash;
    /**
     * 销毁资源管理器
     */
    dispose(): void;
}
