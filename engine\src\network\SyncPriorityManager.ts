/**
 * 同步优先级管理器
 * 负责管理网络实体的同步优先级
 */
import { Debug } from '../utils/Debug';
import type { Entity } from '../core/Entity';
import { Vector3 } from '../math/Vector3';
import { NetworkEntityComponent  } from './components/NetworkEntityComponent';

/**
 * 同步优先级类型
 */
export enum SyncPriorityType {
  /** 距离优先级 */
  DISTANCE = 'distance',
  /** 视野优先级 */
  VISIBILITY = 'visibility',
  /** 重要性优先级 */
  IMPORTANCE = 'importance',
  /** 活动优先级 */
  ACTIVITY = 'activity',
  /** 自定义优先级 */
  CUSTOM = 'custom',
}

/**
 * 同步优先级配置
 */
export interface SyncPriorityConfig {
  /** 是否启用距离优先级 */
  useDistancePriority?: boolean;
  /** 距离优先级权重 */
  distancePriorityWeight?: number;
  /** 最大距离（超过此距离的实体优先级为0） */
  maxDistance?: number;

  /** 是否启用视野优先级 */
  useVisibilityPriority?: boolean;
  /** 视野优先级权重 */
  visibilityPriorityWeight?: number;
  /** 视野角度（度） */
  visibilityAngle?: number;

  /** 是否启用重要性优先级 */
  useImportancePriority?: boolean;
  /** 重要性优先级权重 */
  importancePriorityWeight?: number;

  /** 是否启用活动优先级 */
  useActivityPriority?: boolean;
  /** 活动优先级权重 */
  activityPriorityWeight?: number;
  /** 活动检测阈值 */
  activityThreshold?: number;
  /** 活动衰减时间（毫秒） */
  activityDecayTime?: number;

  /** 是否启用自定义优先级 */
  useCustomPriority?: boolean;
  /** 自定义优先级权重 */
  customPriorityWeight?: number;
  /** 自定义优先级计算函数 */
  customPriorityFunction?: (entity: Entity, observerPosition: Vector3) => number;

  /** 优先级更新间隔（毫秒） */
  priorityUpdateInterval?: number;
  /** 优先级范围（0-1） */
  priorityRange?: [number, number];

  /** 是否启用自适应同步频率 */
  useAdaptiveSync?: boolean;
  /** 最小同步间隔（毫秒） */
  minSyncInterval?: number;
  /** 最大同步间隔（毫秒） */
  maxSyncInterval?: number;
  /** 基准同步间隔（毫秒） */
  baseSyncInterval?: number;
  /** 优先级对同步间隔的影响系数（0-1） */
  priorityToIntervalFactor?: number;
}

/**
 * 实体优先级状态
 */
interface EntityPriorityState {
  /** 实体ID */
  entityId: string;
  /** 总优先级（0-1） */
  priority: number;
  /** 距离优先级（0-1） */
  distancePriority: number;
  /** 视野优先级（0-1） */
  visibilityPriority: number;
  /** 重要性优先级（0-1） */
  importancePriority: number;
  /** 活动优先级（0-1） */
  activityPriority: number;
  /** 自定义优先级（0-1） */
  customPriority: number;
  /** 上次活动时间 */
  lastActivityTime: number;
  /** 上次位置 */
  lastPosition: Vector3;
  /** 上次更新时间 */
  lastUpdateTime: number;
  /** 推荐同步间隔（毫秒） */
  recommendedSyncInterval: number;
  /** 上次同步间隔更新时间 */
  lastSyncIntervalUpdateTime: number;
}

/**
 * 同步优先级管理器
 */
export class SyncPriorityManager {
  /** 配置 */
  private config: Required<SyncPriorityConfig>;

  /** 本地用户ID */
  private localUserId: string = '';

  /** 观察者位置 */
  private observerPosition: Vector3 = new Vector3();

  /** 观察者方向 */
  private observerDirection: Vector3 = new Vector3(0, 0, 1);

  /** 实体优先级状态映射表 */
  private entityPriorityStates: Map<string, EntityPriorityState> = new Map();

  /** 优先级更新计时器 */
  private priorityUpdateTimer: number = 0;

  /** 是否正在更新优先级 */
  private isUpdatingPriority: boolean = false;

  /**
   * 创建同步优先级管理器
   * @param config 配置
   */
  constructor(config: SyncPriorityConfig = {}) {
    // 默认配置
    this.config = {
      useDistancePriority: true,
      distancePriorityWeight: 0.5,
      maxDistance: 100,

      useVisibilityPriority: true,
      visibilityPriorityWeight: 0.3,
      visibilityAngle: 120,

      useImportancePriority: true,
      importancePriorityWeight: 0.2,

      useActivityPriority: true,
      activityPriorityWeight: 0.4,
      activityThreshold: 0.1,
      activityDecayTime: 5000,

      useCustomPriority: false,
      customPriorityWeight: 0.1,
      customPriorityFunction: null,

      priorityUpdateInterval: 1000,
      priorityRange: [0, 1],

      useAdaptiveSync: true,
      minSyncInterval: 50,
      maxSyncInterval: 1000,
      baseSyncInterval: 100,
      priorityToIntervalFactor: 0.8,
      ...config,
    };
  }

  /**
   * 初始化管理器
   * @param localUserId 本地用户ID
   */
  public initialize(localUserId: string): void {
    this.localUserId = localUserId;
  }

  /**
   * 设置观察者位置和方向
   * @param position 位置
   * @param direction 方向
   */
  public setObserverTransform(position: Vector3, direction: Vector3): void {
    this.observerPosition.copy(position);
    this.observerDirection.copy(direction).normalize();
  }

  /**
   * 注册实体
   * @param entityId 实体ID
   * @param entity 实体
   */
  public registerEntity(entityId: string, entity: Entity): void {
    // 获取网络组件
    const networkEntity = entity.getComponent(NetworkEntityComponent.type) as NetworkEntityComponent;
    if (!networkEntity) {
      Debug.warn('SyncPriorityManager', `Entity ${entityId} does not have NetworkEntityComponent`);
      return;
    }

    // 获取变换组件
    const transform = entity.getComponent('Transform') as any as any;
    const position = transform ? new Vector3(transform.position.x, transform.position.y, transform.position.z) : new Vector3();

    // 创建优先级状态
    const priorityState: EntityPriorityState = {
      entityId,
      priority: 0,
      distancePriority: 0,
      visibilityPriority: 0,
      importancePriority: networkEntity.syncPriority,
      activityPriority: 0,
      customPriority: 0,
      lastActivityTime: Date.now(),
      lastPosition: position.clone(),
      lastUpdateTime: Date.now(),
      recommendedSyncInterval: this.config.baseSyncInterval,
      lastSyncIntervalUpdateTime: Date.now()
    };

    // 添加到映射表
    this.entityPriorityStates.set(entityId, priorityState);

    // 立即计算初始优先级
    this.updateEntityPriority(entityId, entity);
  }

  /**
   * 注销实体
   * @param entityId 实体ID
   */
  public unregisterEntity(entityId: string): void {
    this.entityPriorityStates.delete(entityId);
  }

  /**
   * 更新
   * @param deltaTime 时间增量（毫秒）
   * @param entities 实体映射表
   */
  public update(deltaTime: number, entities: Map<string, Entity>): void {
    // 更新优先级更新计时器
    this.priorityUpdateTimer += deltaTime;

    // 如果达到更新间隔，则更新所有实体的优先级
    if (this.priorityUpdateTimer >= this.config.priorityUpdateInterval) {
      this.updateAllPriorities(entities);
      this.priorityUpdateTimer = 0;
    }
  }

  /**
   * 更新所有实体的优先级
   * @param entities 实体映射表
   */
  private updateAllPriorities(entities: Map<string, Entity>): void {
    if (this.isUpdatingPriority) {
      return;
    }

    this.isUpdatingPriority = true;

    try {
      // 更新每个实体的优先级
      for (const [entityId, entity] of entities.entries()) {
        // 跳过本地用户实体
        if (entityId === this.localUserId) {
          continue;
        }

        // 更新实体优先级
        this.updateEntityPriority(entityId, entity);
      }
    } finally {
      this.isUpdatingPriority = false;
    }
  }

  /**
   * 更新实体优先级
   * @param entityId 实体ID
   * @param entity 实体
   */
  private updateEntityPriority(entityId: string, entity: Entity): void {
    const priorityState = this.entityPriorityStates.get(entityId);
    if (!priorityState) {
      return;
    }

    // 获取网络组件
    const networkEntity = entity.getComponent(NetworkEntityComponent.type) as NetworkEntityComponent;
    if (!networkEntity) {
      return;
    }

    // 获取变换组件
    const transform = entity.getComponent('Transform') as any as any;
    const position = transform ? new Vector3(transform.position.x, transform.position.y, transform.position.z) : new Vector3();

    // 计算各种优先级
    if (this.config.useDistancePriority) {
      priorityState.distancePriority = this.calculateDistancePriority(position);
    }

    if (this.config.useVisibilityPriority) {
      priorityState.visibilityPriority = this.calculateVisibilityPriority(position);
    }

    if (this.config.useImportancePriority) {
      priorityState.importancePriority = networkEntity.syncPriority;
    }

    if (this.config.useActivityPriority) {
      priorityState.activityPriority = this.calculateActivityPriority(position, priorityState);
    }

    if (this.config.useCustomPriority && this.config.customPriorityFunction) {
      priorityState.customPriority = this.config.customPriorityFunction(entity, this.observerPosition);
    }

    // 计算总优先级
    priorityState.priority = this.calculateTotalPriority(priorityState);

    // 如果启用自适应同步，则计算推荐同步间隔
    if (this.config.useAdaptiveSync) {
      this.calculateRecommendedSyncInterval(priorityState);
    }

    // 更新状态
    priorityState.lastPosition.copy(position);
    priorityState.lastUpdateTime = Date.now();
  }

  /**
   * 计算推荐同步间隔
   * @param priorityState 优先级状态
   */
  private calculateRecommendedSyncInterval(priorityState: EntityPriorityState): void {
    // 检查是否需要更新同步间隔
    const now = Date.now();
    const timeSinceLastUpdate = now - priorityState.lastSyncIntervalUpdateTime;

    // 每秒最多更新一次同步间隔
    if (timeSinceLastUpdate < 1000) {
      return;
    }

    // 根据优先级计算同步间隔
    // 优先级越高，同步间隔越短
    const priority = priorityState.priority;
    const factor = this.config.priorityToIntervalFactor;
    const minInterval = this.config.minSyncInterval;
    const maxInterval = this.config.maxSyncInterval;

    // 计算同步间隔
    // 当优先级为0时，使用最大间隔
    // 当优先级为1时，使用最小间隔
    // 中间值使用线性插值
    const intervalRange = maxInterval - minInterval;
    const priorityEffect = priority * factor;
    const interval = Math.round(maxInterval - (intervalRange * priorityEffect));

    // 限制在配置范围内
    const finalInterval = Math.max(minInterval, Math.min(interval, maxInterval));

    // 平滑过渡：与上次推荐间隔进行加权平均
    const smoothFactor = 0.3; // 平滑因子
    const smoothedInterval = Math.round(
      (finalInterval * smoothFactor) + (priorityState.recommendedSyncInterval * (1 - smoothFactor))
    );

    // 更新推荐同步间隔
    priorityState.recommendedSyncInterval = smoothedInterval;
    priorityState.lastSyncIntervalUpdateTime = now;
  }

  /**
   * 计算距离优先级
   * @param position 实体位置
   * @returns 距离优先级（0-1）
   */
  private calculateDistancePriority(position: Vector3): number {
    // 计算与观察者的距离
    const distance = position.distanceTo(this.observerPosition);

    // 距离越近，优先级越高
    const priority = 1 - Math.min(1, distance / this.config.maxDistance);

    return priority;
  }

  /**
   * 计算视野优先级
   * @param position 实体位置
   * @returns 视野优先级（0-1）
   */
  private calculateVisibilityPriority(position: Vector3): number {
    // 计算方向向量
    const directionToEntity = position.clone().subtract(this.observerPosition).normalize();

    // 计算与观察者方向的夹角（弧度）
    const angle = Math.acos(Math.max(-1, Math.min(1, this.observerDirection.dot(directionToEntity))));

    // 转换为角度
    const angleDegrees = angle * (180 / Math.PI);

    // 如果在视野角度内，则优先级为1，否则为0
    const halfAngle = this.config.visibilityAngle / 2;
    const priority = angleDegrees <= halfAngle ? 1 : Math.max(0, 1 - (angleDegrees - halfAngle) / (180 - halfAngle));

    return priority;
  }

  /**
   * 计算活动优先级
   * @param position 实体位置
   * @param priorityState 优先级状态
   * @returns 活动优先级（0-1）
   */
  private calculateActivityPriority(position: Vector3, priorityState: EntityPriorityState): number {
    // 计算位置变化
    const positionChange = position.distanceTo(priorityState.lastPosition);

    // 如果位置变化超过阈值，则更新活动时间
    if (positionChange > this.config.activityThreshold) {
      priorityState.lastActivityTime = Date.now();
    }

    // 计算活动衰减
    const timeSinceLastActivity = Date.now() - priorityState.lastActivityTime;
    const activityDecay = Math.max(0, 1 - timeSinceLastActivity / this.config.activityDecayTime);

    // 活动优先级 = 位置变化 + 活动衰减
    const priority = Math.min(1, positionChange / this.config.activityThreshold + activityDecay);

    return priority;
  }

  /**
   * 计算总优先级
   * @param priorityState 优先级状态
   * @returns 总优先级（0-1）
   */
  private calculateTotalPriority(priorityState: EntityPriorityState): number {
    // 加权平均
    let totalWeight = 0;
    let weightedSum = 0;

    if (this.config.useDistancePriority) {
      weightedSum += priorityState.distancePriority * this.config.distancePriorityWeight;
      totalWeight += this.config.distancePriorityWeight;
    }

    if (this.config.useVisibilityPriority) {
      weightedSum += priorityState.visibilityPriority * this.config.visibilityPriorityWeight;
      totalWeight += this.config.visibilityPriorityWeight;
    }

    if (this.config.useImportancePriority) {
      weightedSum += priorityState.importancePriority * this.config.importancePriorityWeight;
      totalWeight += this.config.importancePriorityWeight;
    }

    if (this.config.useActivityPriority) {
      weightedSum += priorityState.activityPriority * this.config.activityPriorityWeight;
      totalWeight += this.config.activityPriorityWeight;
    }

    if (this.config.useCustomPriority) {
      weightedSum += priorityState.customPriority * this.config.customPriorityWeight;
      totalWeight += this.config.customPriorityWeight;
    }

    // 如果没有权重，则返回默认优先级
    if (totalWeight === 0) {
      return 0.5;
    }

    // 计算加权平均
    const priority = weightedSum / totalWeight;

    // 限制在配置的范围内
    return Math.max(this.config.priorityRange[0], Math.min(priority, this.config.priorityRange[1]));
  }

  /**
   * 获取实体优先级
   * @param entityId 实体ID
   * @returns 优先级（0-1）
   */
  public getEntityPriority(entityId: string): number {
    const priorityState = this.entityPriorityStates.get(entityId);
    return priorityState ? priorityState.priority : 0;
  }

  /**
   * 获取实体详细优先级状态
   * @param entityId 实体ID
   * @returns 优先级状态
   */
  public getEntityPriorityState(entityId: string): any {
    const priorityState = this.entityPriorityStates.get(entityId);
    if (!priorityState) {
      return null;
    }

    return {
      entityId: priorityState.entityId,
      priority: priorityState.priority,
      distancePriority: priorityState.distancePriority,
      visibilityPriority: priorityState.visibilityPriority,
      importancePriority: priorityState.importancePriority,
      activityPriority: priorityState.activityPriority,
      customPriority: priorityState.customPriority,
      lastActivityTime: priorityState.lastActivityTime,
      lastUpdateTime: priorityState.lastUpdateTime,
      recommendedSyncInterval: priorityState.recommendedSyncInterval,
      lastSyncIntervalUpdateTime: priorityState.lastSyncIntervalUpdateTime
    };
  }

  /**
   * 获取实体推荐同步间隔
   * @param entityId 实体ID
   * @returns 推荐同步间隔（毫秒）
   */
  public getEntityRecommendedSyncInterval(entityId: string): number {
    const priorityState = this.entityPriorityStates.get(entityId);
    if (!priorityState) {
      return this.config.baseSyncInterval;
    }

    return priorityState.recommendedSyncInterval;
  }

  /**
   * 获取所有实体的优先级
   * @returns 实体优先级映射表
   */
  public getAllEntityPriorities(): Map<string, number> {
    const priorities = new Map<string, number>();

    for (const [entityId, priorityState] of this.entityPriorityStates.entries()) {
      priorities.set(entityId, priorityState.priority);
    }

    return priorities;
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    this.entityPriorityStates.clear();
    this.priorityUpdateTimer = 0;
    this.isUpdatingPriority = false;
  }
}
