/**
 * 动画控制器
 * 用于控制和混合多个动画片段的播放
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
import type { Entity } from '../core/Entity';
import { AnimationClip } from './AnimationClip';
/**
 * 动画状态
 */
export declare enum AnimationState {
    /** 停止 */
    STOPPED = "stopped",
    /** 播放中 */
    PLAYING = "playing",
    /** 暂停 */
    PAUSED = "paused",
    /** 混合中 */
    BLENDING = "blending"
}
/**
 * 动画事件类型
 */
export declare enum AnimationEventType {
    /** 开始 */
    START = "start",
    /** 停止 */
    STOP = "stop",
    /** 暂停 */
    PAUSE = "pause",
    /** 恢复 */
    RESUME = "resume",
    /** 循环 */
    LOOP = "loop",
    /** 完成 */
    COMPLETE = "complete",
    /** 混合开始 */
    BLEND_START = "blendStart",
    /** 混合完成 */
    BLEND_COMPLETE = "blendComplete"
}
/**
 * 动画控制器选项
 */
export interface AnimatorOptions {
    /** 目标实体 */
    entity?: Entity;
    /** 动画片段 */
    clips?: AnimationClip[];
    /** 是否自动播放 */
    autoPlay?: boolean;
    /** 默认混合时间（秒） */
    defaultBlendTime?: number;
    /** 时间缩放 */
    timeScale?: number;
}
/**
 * 动画控制器组件
 */
export declare class Animator extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 目标实体引用 */
    protected _targetEntity: Entity | null;
    /** 动画片段映射 */
    private clips;
    /** 当前播放的动画片段 */
    private currentClip;
    /** 下一个要播放的动画片段（用于混合） */
    private nextClip;
    /** 当前动画状态 */
    private state;
    /** 当前播放时间（秒） */
    private time;
    /** 混合开始时间（秒） */
    private blendStartTime;
    /** 混合持续时间（秒） */
    private blendTime;
    /** 混合因子（0-1） */
    private blendFactor;
    /** 默认混合时间（秒） */
    private defaultBlendTime;
    /** 时间缩放 */
    private timeScale;
    /** 是否循环 */
    private loop;
    /** 事件发射器 */
    private eventEmitter;
    /** Three.js动画混合器（用于兼容） */
    private mixer;
    /** Three.js动画动作映射 */
    private actions;
    /** 当前动画状态 */
    private animationState;
    /** 缓存的动画状态 */
    private _cachedAnimationState;
    /** 缓存的时间 */
    private _cachedTime;
    /** 缓存的混合因子 */
    private _cachedBlendFactor;
    /** 是否需要更新 */
    private _needsUpdate;
    /** 是否启用缓存 */
    private _cacheEnabled;
    /** 动画参数映射 */
    private parameters;
    /**
     * 创建动画控制器
     * @param options 动画控制器选项
     */
    constructor(options?: AnimatorOptions);
    /**
     * 设置目标实体
     * @param entity 实体
     */
    setEntity(entity: Entity): void;
    /**
     * 获取目标实体
     * @returns 实体
     */
    getTargetEntity(): Entity | null;
    /**
     * 添加动画片段
     * @param clip 动画片段
     */
    addClip(clip: AnimationClip): void;
    /**
     * 移除动画片段
     * @param name 动画片段名称
     * @returns 是否成功移除
     */
    removeClip(name: string): boolean;
    /**
     * 获取动画片段
     * @param name 动画片段名称
     * @returns 动画片段，如果不存在则返回null
     */
    getClip(name: string): AnimationClip | null;
    /**
     * 获取所有动画片段
     * @returns 动画片段数组
     */
    getClips(): AnimationClip[];
    /**
     * 播放动画
     * @param name 动画片段名称
     * @param blendTime 混合时间（秒），如果为0则立即切换
     * @returns 是否成功开始播放
     */
    play(name: string, blendTime?: number): boolean;
    /**
     * 停止播放
     */
    stop(): void;
    /**
     * 暂停播放
     */
    pause(): void;
    /**
     * 恢复播放
     */
    resume(): void;
    /**
     * 设置播放时间
     * @param time 时间（秒）
     */
    setTime(time: number): void;
    /**
     * 获取播放时间
     * @returns 时间（秒）
     */
    getTime(): number;
    /**
     * 设置时间缩放
     * @param timeScale 时间缩放
     */
    setTimeScale(timeScale: number): void;
    /**
     * 获取时间缩放
     * @returns 时间缩放
     */
    getTimeScale(): number;
    /**
     * 设置循环模式
     * @param loop 是否循环
     */
    setLoop(loop: boolean): void;
    /**
     * 获取循环模式
     * @returns 是否循环
     */
    getLoop(): boolean;
    /**
     * 获取当前动画状态
     * @returns 动画状态
     */
    getState(): AnimationState;
    /**
     * 获取当前播放的动画片段
     * @returns 动画片段，如果没有则返回null
     */
    getCurrentClip(): AnimationClip | null;
    /**
     * 获取下一个要播放的动画片段
     * @returns 动画片段，如果没有则返回null
     */
    getNextClip(): AnimationClip | null;
    /**
     * 获取混合因子
     * @returns 混合因子（0-1）
     */
    getBlendFactor(): number;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    addListener(type: AnimationEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    removeListener(type: AnimationEventType, listener: (...args: any[]) => void): void;
    /**
     * 更新动画
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 设置是否启用缓存
     * @param enabled 是否启用
     */
    setCacheEnabled(enabled: boolean): void;
    /**
     * 清除缓存
     */
    clearCache(): void;
    /**
     * 获取Three.js动画混合器
     * @returns 动画混合器，如果不存在则返回null
     */
    getMixer(): THREE.AnimationMixer | null;
    /**
     * 获取指定名称的动画动作
     * @param name 动画片段名称
     * @returns 动画动作，如果不存在则返回null
     */
    getAction(name: string): THREE.AnimationAction | null;
    /**
     * 设置动画参数
     * @param name 参数名称
     * @param value 参数值
     */
    setParameter(name: string, value: any): void;
    /**
     * 获取动画参数
     * @param name 参数名称
     * @returns 参数值
     */
    getParameter(name: string): any;
    /**
     * 获取所有动画参数
     * @returns 参数映射
     */
    getParameters(): Map<string, any>;
    /**
     * 获取骨骼
     * @returns 骨骼，如果不存在则返回null
     */
    getSkeleton(): THREE.Skeleton | null;
    /**
     * 更新动画片段
     * @param name 动画片段名称
     * @param clip 新的动画片段
     * @returns 是否成功更新
     */
    updateClip(name: string, clip: AnimationClip): boolean;
    /**
     * 更新动作映射
     * @param clipName 动画片段名称
     * @param action 新的动作
     */
    updateAction(clipName: string, action: THREE.AnimationAction): void;
    /**
     * 更新动画状态
     */
    private updateAnimationState;
    /**
     * 克隆值
     * @param value 要克隆的值
     * @returns 克隆的值
     */
    private _cloneValue;
    /**
     * 混合两个值
     * @param a 值A
     * @param b 值B
     * @param t 混合因子（0-1）
     * @returns 混合结果
     */
    private blendValues;
    /**
     * 应用动画状态到实体
     */
    private applyAnimationState;
    /**
     * 销毁组件
     */
    dispose(): void;
}
