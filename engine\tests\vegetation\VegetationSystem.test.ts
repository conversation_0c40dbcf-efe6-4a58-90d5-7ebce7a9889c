/**
 * 植被系统测试
 */
import * as THREE from 'three';
import { Entity } from '../../src/core/Entity';
import { EntityManager } from '../../src/core/EntityManager';
import { World } from '../../src/core/World';
import { Camera } from '../../src/rendering/Camera';
import { Scene } from '../../src/scene/Scene';
import { TerrainComponent } from '../../src/terrain/components/TerrainComponent';
import { VegetationComponent, VegetationItemConfig } from '../../src/vegetation/components/VegetationComponent';
import { VegetationSystem } from '../../src/vegetation/VegetationSystem';

describe('VegetationSystem', () => {
  let world: World;
  let entityManager: EntityManager;
  let system: VegetationSystem;
  let terrainEntity: Entity;
  let vegetationEntity: Entity;
  let camera: Entity;
  let scene: Entity;

  beforeEach(() => {
    // 创建世界
    world = new World();

    // 创建实体管理器
    entityManager = new EntityManager();

    // 创建相机实体
    camera = new Entity();
    const cameraComponent = new Camera();
    camera.addComponent(cameraComponent);
    entityManager.addEntity(camera);

    // 创建场景实体
    scene = new Entity();
    const sceneComponent = new Scene();
    scene.addComponent(sceneComponent);
    entityManager.addEntity(scene);

    // 创建地形实体
    terrainEntity = new Entity('terrain');
    const terrainComponent = new TerrainComponent({
      width: 100,
      height: 100,
      resolution: 32,
      maxHeight: 10
    });
    terrainEntity.addComponent(terrainComponent);
    entityManager.addEntity(terrainEntity);

    // 创建植被系统
    system = new VegetationSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      useInstancing: true,
      useLOD: true,
      useFrustumCulling: true,
      useOctree: true,
      useGPUInstancing: false,
      useShadow: true,
      useWind: true,
      useDebugVisualization: false
    });
    system.setEntityManager(entityManager);
    system.initialize();
  });

  afterEach(() => {
    system.destroy();
  });

  test('应该正确初始化植被系统', () => {
    expect(system).toBeDefined();
    expect(system['enabled']).toBe(true);
    expect(system['autoUpdate']).toBe(true);
    expect(system['updateFrequency']).toBe(1);
    expect(system['useInstancing']).toBe(true);
    expect(system['useLOD']).toBe(true);
    expect(system['useFrustumCulling']).toBe(true);
    expect(system['useOctree']).toBe(true);
    expect(system['useGPUInstancing']).toBe(false);
    expect(system['useShadow']).toBe(true);
    expect(system['useWind']).toBe(true);
    expect(system['useDebugVisualization']).toBe(false);
  });

  test('应该正确添加植被实体', () => {
    // 创建植被实体
    vegetationEntity = new Entity('vegetation');
    const vegetationComponent = new VegetationComponent({
      terrainEntity: terrainEntity.id,
      autoGenerate: false,
      items: []
    });
    vegetationEntity.addComponent(vegetationComponent);
    entityManager.addEntity(vegetationEntity);

    // 验证植被实体已添加
    expect(system['vegetationEntities'].size).toBe(1);
    expect(system['vegetationEntities'].get(vegetationEntity)).toBe(vegetationComponent);
  });

  test('应该正确移除植被实体', () => {
    // 创建植被实体
    vegetationEntity = new Entity('vegetation');
    const vegetationComponent = new VegetationComponent({
      terrainEntity: terrainEntity.id,
      autoGenerate: false,
      items: []
    });
    vegetationEntity.addComponent(vegetationComponent);
    entityManager.addEntity(vegetationEntity);

    // 验证植被实体已添加
    expect(system['vegetationEntities'].size).toBe(1);

    // 移除植被实体
    entityManager.removeEntity(vegetationEntity);

    // 验证植被实体已移除
    expect(system['vegetationEntities'].size).toBe(0);
  });

  test('应该正确生成植被', async () => {
    // 创建植被实体
    vegetationEntity = new Entity('vegetation');
    const vegetationComponent = new VegetationComponent({
      terrainEntity: terrainEntity.id,
      autoGenerate: false,
      items: [
        {
          model: 'assets/models/test_tree.glb',
          density: 0.01,
          minScale: 0.8,
          maxScale: 1.2,
          minHeight: 0,
          maxHeight: 10,
          slopeMin: 0,
          slopeMax: 90
        }
      ]
    });
    vegetationEntity.addComponent(vegetationComponent);
    entityManager.addEntity(vegetationEntity);

    // 模拟加载模型
    jest.spyOn(system['modelLoader'], 'load').mockImplementation(async (url: string) => {
      const mesh = new THREE.Mesh(
        new THREE.BoxGeometry(1, 1, 1),
        new THREE.MeshBasicMaterial({ color: 0xff0000 })
      );
      return mesh;
    });

    // 生成植被
    await system.generateVegetation(vegetationEntity, vegetationComponent);

    // 验证植被已生成
    expect(vegetationComponent.initialized).toBe(true);
    expect(vegetationComponent.needsUpdate).toBe(false);
  });

  test('应该正确清除植被', async () => {
    // 创建植被实体
    vegetationEntity = new Entity('vegetation');
    const vegetationComponent = new VegetationComponent({
      terrainEntity: terrainEntity.id,
      autoGenerate: false,
      items: [
        {
          model: 'assets/models/test_tree.glb',
          density: 0.01,
          minScale: 0.8,
          maxScale: 1.2,
          minHeight: 0,
          maxHeight: 10,
          slopeMin: 0,
          slopeMax: 90
        }
      ]
    });
    vegetationEntity.addComponent(vegetationComponent);
    entityManager.addEntity(vegetationEntity);

    // 模拟加载模型
    jest.spyOn(system['modelLoader'], 'load').mockImplementation(async (url: string) => {
      const mesh = new THREE.Mesh(
        new THREE.BoxGeometry(1, 1, 1),
        new THREE.MeshBasicMaterial({ color: 0xff0000 })
      );
      return mesh;
    });

    // 生成植被
    await system.generateVegetation(vegetationEntity, vegetationComponent);

    // 验证植被已生成
    expect(vegetationComponent.instances.size).toBeGreaterThan(0);

    // 清除植被
    system.clearVegetation(vegetationEntity, vegetationComponent);

    // 验证植被已清除
    expect(vegetationComponent.instances.size).toBe(0);
  });

  test('应该正确更新植被', async () => {
    // 创建植被实体
    vegetationEntity = new Entity('vegetation');
    const vegetationComponent = new VegetationComponent({
      terrainEntity: terrainEntity.id,
      autoGenerate: false,
      useWind: true,
      windParams: {
        strength: 0.1,
        direction: new THREE.Vector2(1, 0),
        frequency: 0.2,
        turbulence: 0.1
      },
      items: [
        {
          model: 'assets/models/test_tree.glb',
          density: 0.01,
          minScale: 0.8,
          maxScale: 1.2,
          minHeight: 0,
          maxHeight: 10,
          slopeMin: 0,
          slopeMax: 90,
          windEffect: true
        }
      ]
    });
    vegetationEntity.addComponent(vegetationComponent);
    entityManager.addEntity(vegetationEntity);

    // 模拟加载模型
    jest.spyOn(system['modelLoader'], 'load').mockImplementation(async (url: string) => {
      const mesh = new THREE.Mesh(
        new THREE.BoxGeometry(1, 1, 1),
        new THREE.MeshBasicMaterial({ color: 0xff0000 })
      );
      return mesh;
    });

    // 生成植被
    await system.generateVegetation(vegetationEntity, vegetationComponent);

    // 标记需要更新
    vegetationComponent.needsUpdate = true;

    // 更新系统
    system.update(0.016);

    // 验证植被已更新
    expect(vegetationComponent.needsUpdate).toBe(false);
  });

  test('应该正确处理风效果', async () => {
    // 创建植被实体
    vegetationEntity = new Entity('vegetation');
    const vegetationComponent = new VegetationComponent({
      terrainEntity: terrainEntity.id,
      autoGenerate: false,
      useWind: true,
      windParams: {
        strength: 0.1,
        direction: new THREE.Vector2(1, 0),
        frequency: 0.2,
        turbulence: 0.1
      },
      items: [
        {
          model: 'assets/models/test_tree.glb',
          density: 0.01,
          minScale: 0.8,
          maxScale: 1.2,
          minHeight: 0,
          maxHeight: 10,
          slopeMin: 0,
          slopeMax: 90,
          windEffect: true
        }
      ]
    });
    vegetationEntity.addComponent(vegetationComponent);
    entityManager.addEntity(vegetationEntity);

    // 模拟加载模型
    jest.spyOn(system['modelLoader'], 'load').mockImplementation(async (url: string) => {
      const mesh = new THREE.Mesh(
        new THREE.BoxGeometry(1, 1, 1),
        new THREE.MeshBasicMaterial({ color: 0xff0000 })
      );
      return mesh;
    });

    // 生成植被
    await system.generateVegetation(vegetationEntity, vegetationComponent);

    // 模拟更新风效果
    const updateWindEffectSpy = jest.spyOn(system as any, 'updateWindEffect');

    // 更新系统
    system.update(0.016);

    // 验证风效果已更新
    expect(updateWindEffectSpy).toHaveBeenCalled();
  });

  test('应该正确处理季节效果', async () => {
    // 创建植被实体
    vegetationEntity = new Entity('vegetation');
    const vegetationComponent = new VegetationComponent({
      terrainEntity: terrainEntity.id,
      autoGenerate: false,
      useSeasonal: true,
      seasonalParams: {
        season: 'autumn',
        intensity: 0.8
      },
      items: [
        {
          model: 'assets/models/test_tree.glb',
          density: 0.01,
          minScale: 0.8,
          maxScale: 1.2,
          minHeight: 0,
          maxHeight: 10,
          slopeMin: 0,
          slopeMax: 90,
          seasonalEffect: true
        }
      ]
    });
    vegetationEntity.addComponent(vegetationComponent);
    entityManager.addEntity(vegetationEntity);

    // 模拟加载模型
    jest.spyOn(system['modelLoader'], 'load').mockImplementation(async (url: string) => {
      const mesh = new THREE.Mesh(
        new THREE.BoxGeometry(1, 1, 1),
        new THREE.MeshBasicMaterial({ color: 0xff0000 })
      );
      return mesh;
    });

    // 生成植被
    await system.generateVegetation(vegetationEntity, vegetationComponent);

    // 模拟更新季节效果
    const updateSeasonalEffectSpy = jest.spyOn(system as any, 'updateSeasonalEffect');

    // 更新系统
    system.update(0.016);

    // 验证季节效果已更新
    expect(updateSeasonalEffectSpy).toHaveBeenCalled();
  });
});
