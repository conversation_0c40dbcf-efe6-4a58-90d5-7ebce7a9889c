# 多阶段构建
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制资产库服务代码
COPY asset-library-service/package*.json ./asset-library-service/
WORKDIR /app/asset-library-service

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制资产库服务源代码
COPY asset-library-service/ ./

# 构建应用
RUN npm run build

# 生产阶段
FROM node:22-alpine AS production

# 安装dumb-init用于信号处理
RUN apk add --no-cache dumb-init

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY --from=builder /app/asset-library-service/package*.json ./

# 只安装生产依赖
RUN npm ci --only=production && npm cache clean --force

# 从构建阶段复制构建产物
COPY --from=builder --chown=nestjs:nodejs /app/asset-library-service/dist ./dist

# 创建日志目录
RUN mkdir -p logs && chown nestjs:nodejs logs

# 切换到非root用户
USER nestjs

# 暴露端口
EXPOSE 8003

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node dist/health-check.js

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/main.js"]
