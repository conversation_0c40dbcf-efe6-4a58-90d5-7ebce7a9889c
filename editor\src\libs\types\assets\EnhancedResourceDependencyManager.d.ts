/**
 * 增强资源依赖管理器
 * 提供更高效的资源依赖管理功能
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 依赖类型
 */
export declare enum DependencyType {
    /** 强依赖（必须加载） */
    STRONG = "strong",
    /** 弱依赖（可选加载） */
    WEAK = "weak",
    /** 延迟依赖（按需加载） */
    LAZY = "lazy",
    /** 预加载依赖（提前加载） */
    PRELOAD = "preload"
}
/**
 * 依赖信息
 */
export interface DependencyInfo {
    /** 依赖资源ID */
    id: string;
    /** 依赖类型 */
    type: DependencyType;
    /** 依赖优先级 */
    priority?: number;
    /** 依赖元数据 */
    metadata?: Record<string, any>;
}
/**
 * 增强资源依赖管理器选项
 */
export interface EnhancedResourceDependencyManagerOptions {
    /** 是否启用循环依赖检测 */
    enableCycleDetection?: boolean;
    /** 是否启用依赖优化 */
    enableOptimization?: boolean;
    /** 是否启用自动优化 */
    enableAutoOptimization?: boolean;
    /** 是否启用深度分析 */
    enableDeepAnalysis?: boolean;
    /** 是否启用依赖缓存 */
    enableDependencyCache?: boolean;
    /** 是否启用调试模式 */
    debug?: boolean;
}
/**
 * 增强资源依赖管理器
 */
export declare class EnhancedResourceDependencyManager extends EventEmitter {
    /** 依赖图 */
    private dependencyGraph;
    /** 是否启用循环依赖检测 */
    private enableCycleDetection;
    /** 是否启用依赖优化 */
    private enableOptimization;
    /** 是否启用自动优化 */
    private enableAutoOptimization;
    /** 是否启用深度分析 */
    private enableDeepAnalysis;
    /** 是否启用依赖缓存 */
    private enableDependencyCache;
    /** 是否启用调试模式 */
    private debug;
    /** 是否已初始化 */
    private initialized;
    /** 依赖分析缓存 */
    private analysisCache;
    /** 循环依赖缓存 */
    private cyclicDependenciesCache;
    /** 上次优化时间 */
    private lastOptimizationTime;
    /** 优化间隔（毫秒） */
    private optimizationInterval;
    /**
     * 创建增强资源依赖管理器实例
     * @param options 依赖管理器选项
     */
    constructor(options?: EnhancedResourceDependencyManagerOptions);
    /**
     * 初始化依赖管理器
     */
    initialize(): void;
    /**
     * 设置自动优化
     */
    private setupAutoOptimization;
    /**
     * 设置优化间隔
     * @param interval 优化间隔（毫秒）
     */
    setOptimizationInterval(interval: number): void;
    /**
     * 添加依赖关系
     * @param resourceId 资源ID
     * @param dependencyId 依赖资源ID
     * @param type 依赖类型
     * @param priority 依赖优先级
     * @param metadata 依赖元数据
     * @returns 是否成功添加
     */
    addDependency(resourceId: string, dependencyId: string, type?: DependencyType, priority?: number, metadata?: Record<string, any>): boolean;
    /**
     * 判断是否应该更新依赖类型
     * @param existingType 现有依赖类型
     * @param newType 新依赖类型
     * @returns 是否应该更新
     */
    private shouldUpdateDependencyType;
    /**
     * 获取或创建依赖图节点
     * @param id 资源ID
     * @returns 依赖图节点
     */
    private getOrCreateNode;
    /**
     * 检查添加依赖是否会形成循环
     * @param resourceId 资源ID
     * @param dependencyId 依赖资源ID
     * @returns 是否会形成循环
     */
    private wouldFormCycle;
    /**
     * 移除依赖关系
     * @param resourceId 资源ID
     * @param dependencyId 依赖资源ID
     * @returns 是否成功移除
     */
    removeDependency(resourceId: string, dependencyId: string): boolean;
    /**
     * 获取资源的直接依赖
     * @param resourceId 资源ID
     * @returns 依赖信息数组
     */
    getDependencies(resourceId: string): DependencyInfo[];
    /**
     * 获取资源的直接被依赖
     * @param resourceId 资源ID
     * @returns 资源ID数组
     */
    getDependents(resourceId: string): string[];
    /**
     * 获取特定类型的依赖
     * @param resourceId 资源ID
     * @param type 依赖类型
     * @returns 依赖信息数组
     */
    getDependenciesByType(resourceId: string, type: DependencyType): DependencyInfo[];
    /**
     * 获取强依赖
     * @param resourceId 资源ID
     * @returns 依赖信息数组
     */
    getStrongDependencies(resourceId: string): DependencyInfo[];
    /**
     * 获取弱依赖
     * @param resourceId 资源ID
     * @returns 依赖信息数组
     */
    getWeakDependencies(resourceId: string): DependencyInfo[];
    /**
     * 获取延迟依赖
     * @param resourceId 资源ID
     * @returns 依赖信息数组
     */
    getLazyDependencies(resourceId: string): DependencyInfo[];
    /**
     * 获取预加载依赖
     * @param resourceId 资源ID
     * @returns 依赖信息数组
     */
    getPreloadDependencies(resourceId: string): DependencyInfo[];
    /**
     * 获取资源的所有依赖（包括间接依赖）
     * @param resourceId 资源ID
     * @param types 包含的依赖类型数组
     * @returns 依赖信息数组
     */
    getAllDependencies(resourceId: string, types?: DependencyType[]): DependencyInfo[];
    /**
     * 获取资源的所有被依赖（包括间接被依赖）
     * @param resourceId 资源ID
     * @returns 资源ID数组
     */
    getAllDependents(resourceId: string): string[];
    /**
     * 获取加载顺序
     * @param resourceIds 资源ID数组
     * @param includeTypes 包含的依赖类型数组
     * @returns 按加载顺序排序的资源ID数组
     */
    getLoadingOrder(resourceIds: string[], includeTypes?: DependencyType[]): string[];
    /**
     * 获取加载顺序（备用方法）
     * @param resourceIds 资源ID数组
     * @param includeTypes 包含的依赖类型数组
     * @returns 按加载顺序排序的资源ID数组
     */
    private getLoadingOrderFallback;
    /**
     * 优化依赖关系
     * @param strategies 优化策略数组，为空则使用所有策略
     * @returns 是否成功优化
     */
    optimizeDependencies(strategies?: string[]): boolean;
    /**
     * 移除冗余依赖
     */
    private removeRedundantDependencies;
    /**
     * 合并相似依赖
     */
    private mergeSimilarDependencies;
    /**
     * 转换为弱依赖
     * 将可能的强依赖转换为弱依赖，以减少加载时间
     */
    private convertToWeakDependencies;
    /**
     * 优化延迟依赖
     * 将不常用的依赖转换为延迟依赖
     */
    private optimizeLazyDependencies;
    /**
     * 优化预加载依赖
     * 将频繁使用的依赖转换为预加载依赖
     */
    private optimizePreloadDependencies;
    /**
     * 更新依赖类型
     * @param resourceId 资源ID
     * @param dependencyId 依赖资源ID
     * @param newType 新依赖类型
     * @returns 是否成功更新
     */
    private updateDependencyType;
    /**
     * 检查资源是否关键
     * @param resourceId 资源ID
     * @returns 是否关键
     */
    private isResourceCritical;
    /**
     * 检查资源是否不常用
     * @param resourceId 资源ID
     * @returns 是否不常用
     */
    private isResourceRarelyUsed;
    /**
     * 检查资源是否频繁使用
     * @param resourceId 资源ID
     * @returns 是否频繁使用
     */
    private isResourceFrequentlyUsed;
    /**
     * 分析依赖关系
     * @param resourceId 资源ID，如果为空则分析所有资源
     * @returns 分析结果
     */
    analyzeDependencies(resourceId?: string): any;
    /**
     * 检测循环依赖
     * @param resourceIds 资源ID数组
     * @returns 循环依赖数组
     */
    private detectCircularDependencies;
    /**
     * 检测未使用资源
     * @returns 未使用资源ID数组
     */
    private detectUnusedResources;
    /**
     * 计算依赖链
     * @param resourceId 资源ID
     * @returns 依赖链数组
     */
    private calculateDependencyChains;
    /**
     * 计算依赖统计信息
     * @param resourceIds 资源ID数组
     * @returns 统计信息
     */
    private calculateDependencyStatistics;
    /**
     * 检测冗余依赖
     * @param resourceIds 资源ID数组
     * @returns 冗余依赖数组
     */
    private detectRedundantDependencies;
    /**
     * 检测可转换为弱依赖的候选项
     * @param resourceIds 资源ID数组
     * @returns 弱依赖候选项数组
     */
    private detectWeakDependencyCandidates;
    /**
     * 检测可转换为延迟依赖的候选项
     * @param resourceIds 资源ID数组
     * @returns 延迟依赖候选项数组
     */
    private detectLazyDependencyCandidates;
    /**
     * 检测可转换为预加载依赖的候选项
     * @param resourceIds 资源ID数组
     * @returns 预加载依赖候选项数组
     */
    private detectPreloadDependencyCandidates;
    /**
     * 清除分析缓存
     */
    clearAnalysisCache(): void;
    /**
     * 清除所有依赖关系
     */
    clearDependencies(): void;
    /**
     * 销毁依赖管理器
     */
    dispose(): void;
}
