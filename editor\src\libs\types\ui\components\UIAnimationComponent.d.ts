/**
 * UIAnimationComponent.ts
 *
 * UI动画组件，用于为UI元素添加动画效果
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { IUIAnimation, UIAnimationType, UIEasingFunction } from '../interfaces/IUIElement';
import { UIComponent } from './UIComponent';
/**
 * 动画目标属性类型
 */
export type AnimationTargetProperty = 'position' | 'size' | 'opacity' | 'rotation' | 'scale' | 'color' | 'backgroundColor' | 'borderColor' | 'fontColor';
/**
 * 动画缓动函数
 */
export declare class UIEasing {
    static linear: UIEasingFunction;
    static quadIn: UIEasingFunction;
    static quadOut: UIEasingFunction;
    static quadInOut: UIEasingFunction;
    static cubicIn: UIEasingFunction;
    static cubicOut: UIEasingFunction;
    static cubicInOut: UIEasingFunction;
    static quartIn: UIEasingFunction;
    static quartOut: UIEasingFunction;
    static quartInOut: UIEasingFunction;
    static expoIn: UIEasingFunction;
    static expoOut: UIEasingFunction;
    static expoInOut: UIEasingFunction;
    static sineIn: UIEasingFunction;
    static sineOut: UIEasingFunction;
    static sineInOut: UIEasingFunction;
    static elasticIn: UIEasingFunction;
    static elasticOut: UIEasingFunction;
    static elasticInOut: UIEasingFunction;
    static bounceIn: UIEasingFunction;
    static bounceOut: UIEasingFunction;
    static bounceInOut: UIEasingFunction;
}
/**
 * UI动画类
 * 实现IUIAnimation接口
 */
export declare class UIAnimation implements IUIAnimation {
    type: UIAnimationType;
    target: UIComponent;
    duration: number;
    delay: number;
    easing: UIEasingFunction;
    loop: boolean;
    from: any;
    to: any;
    progress: number;
    property: AnimationTargetProperty;
    startTime: number;
    isPlaying: boolean;
    isPaused: boolean;
    isCompleted: boolean;
    onComplete?: () => void;
    onUpdate?: (value: any) => void;
    /**
     * 构造函数
     * @param type 动画类型
     * @param target 目标UI元素
     * @param property 目标属性
     * @param from 起始值
     * @param to 结束值
     * @param duration 持续时间（毫秒）
     * @param options 其他选项
     */
    constructor(type: UIAnimationType, target: UIComponent, property: AnimationTargetProperty, from: any, to: any, duration?: number, options?: {
        delay?: number;
        easing?: UIEasingFunction;
        loop?: boolean;
        onComplete?: () => void;
        onUpdate?: (value: any) => void;
    });
    /**
     * 开始动画
     */
    start(): void;
    /**
     * 暂停动画
     */
    pause(): void;
    /**
     * 恢复动画
     */
    resume(): void;
    /**
     * 停止动画
     */
    stop(): void;
    /**
     * 更新动画
     * @param _deltaTime 时间增量 - 未使用，使用 Date.now() 代替
     */
    update(_deltaTime: number): void;
    /**
     * 插值计算
     * @param from 起始值
     * @param to 结束值
     * @param progress 进度（0-1）
     * @returns 插值结果
     */
    private interpolate;
    /**
     * 应用值到目标
     * @param value 要应用的值
     */
    private applyValue;
}
/**
 * UI动画组件
 * 用于管理实体的UI动画
 */
export declare class UIAnimationComponent extends Component {
    animations: UIAnimation[];
    /**
     * 构造函数
     * @param entity 关联的实体
     */
    constructor(entity: Entity);
    /**
     * 添加动画
     * @param animation 要添加的动画
     * @returns 添加的动画
     */
    addAnimation(animation: UIAnimation): UIAnimation;
    /**
     * 移除动画
     * @param animation 要移除的动画
     */
    removeAnimation(animation: UIAnimation): void;
    /**
     * 清除所有动画
     */
    clearAnimations(): void;
    /**
     * 更新所有动画
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 创建并添加动画
     * @param type 动画类型
     * @param target 目标UI元素
     * @param property 目标属性
     * @param from 起始值
     * @param to 结束值
     * @param duration 持续时间（毫秒）
     * @param options 其他选项
     * @returns 创建的动画
     */
    createAnimation(type: UIAnimationType, target: UIComponent, property: AnimationTargetProperty, from: any, to: any, duration?: number, options?: {
        delay?: number;
        easing?: UIEasingFunction;
        loop?: boolean;
        onComplete?: () => void;
        onUpdate?: (value: any) => void;
    }): UIAnimation;
}
