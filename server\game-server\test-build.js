// 简单的构建测试脚本
console.log('开始测试 game-server 构建...');

try {
  // 尝试导入主模块
  const { NestFactory } = require('@nestjs/core');
  console.log('✓ NestJS 导入成功');
  
  // 尝试导入应用模块
  const { AppModule } = require('./dist/app.module');
  console.log('✓ AppModule 导入成功');
  
  console.log('✓ 所有模块导入成功，构建测试通过！');
  
} catch (error) {
  console.error('✗ 构建测试失败:', error.message);
  console.error('错误详情:', error.stack);
  process.exit(1);
}
