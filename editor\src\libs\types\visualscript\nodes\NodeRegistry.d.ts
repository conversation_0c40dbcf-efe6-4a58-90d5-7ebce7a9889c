/**
 * 视觉脚本节点注册表
 * 用于注册和管理节点类型
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Node, NodeCategory } from './Node';
/**
 * 节点构造函数类型
 */
export type NodeConstructor = new (...args: any[]) => Node;
/**
 * 节点类型信息
 */
export interface NodeTypeInfo {
    /** 节点类型名称 */
    type: string;
    /** 节点类别 */
    category: NodeCategory;
    /** 节点构造函数 */
    constructor: NodeConstructor;
    /** 节点描述 */
    description?: string;
    /** 节点标签 */
    label?: string;
    /** 节点图标 */
    icon?: string;
    /** 节点颜色 */
    color?: string;
    /** 是否已弃用 */
    deprecated?: boolean;
    /** 弃用原因 */
    deprecatedReason?: string;
    /** 是否实验性 */
    experimental?: boolean;
    /** 标签列表 */
    tags?: string[];
    /** 示例代码 */
    examples?: string[];
    /** 文档链接 */
    documentationUrl?: string;
    /** 版本 */
    version?: string;
    /** 作者 */
    author?: string;
    /** 许可证 */
    license?: string;
    /** 依赖项 */
    dependencies?: string[];
    /** 自定义元数据 */
    [key: string]: any;
}
/**
 * 节点注册表
 * 用于注册和管理节点类型
 */
export declare class NodeRegistry extends EventEmitter {
    /** 节点类型映射 */
    private nodeTypes;
    /** 节点类别映射 */
    private categories;
    /** 节点标签映射 */
    private tags;
    /**
     * 创建节点注册表
     */
    constructor();
    /**
     * 注册节点类型
     * @param info 节点类型信息
     * @returns 是否注册成功
     */
    registerNodeType(info: NodeTypeInfo): boolean;
    /**
     * 注销节点类型
     * @param type 节点类型名称
     * @returns 是否注销成功
     */
    unregisterNodeType(type: string): boolean;
    /**
     * 获取节点类型
     * @param type 节点类型名称
     * @returns 节点构造函数
     */
    getNodeType(type: string): NodeConstructor | undefined;
    /**
     * 获取节点类型信息
     * @param type 节点类型名称
     * @returns 节点类型信息
     */
    getNodeTypeInfo(type: string): NodeTypeInfo | undefined;
    /**
     * 获取所有节点类型
     * @returns 节点类型信息列表
     */
    getAllNodeTypes(): NodeTypeInfo[];
    /**
     * 获取指定类别的节点类型
     * @param category 节点类别
     * @returns 节点类型信息列表
     */
    getNodeTypesByCategory(category: NodeCategory): NodeTypeInfo[];
    /**
     * 获取指定标签的节点类型
     * @param tag 标签
     * @returns 节点类型信息列表
     */
    getNodeTypesByTag(tag: string): NodeTypeInfo[];
    /**
     * 获取所有类别
     * @returns 类别列表
     */
    getAllCategories(): NodeCategory[];
    /**
     * 获取所有标签
     * @returns 标签列表
     */
    getAllTags(): string[];
    /**
     * 搜索节点类型
     * @param query 搜索查询
     * @returns 节点类型信息列表
     */
    searchNodeTypes(query: string): NodeTypeInfo[];
    /**
     * 清空注册表
     */
    clear(): void;
}
