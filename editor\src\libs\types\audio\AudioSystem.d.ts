import { EventCallback } from '../utils/EventEmitter';
import { System } from '../core/System';
import { AudioListener } from './AudioListener';
import { AudioSource } from './AudioSource';
/**
 * 音频类型
 */
export declare enum AudioType {
    /** 音效 */
    SOUND = "sound",
    /** 音乐 */
    MUSIC = "music",
    /** 语音 */
    VOICE = "voice",
    /** 环境音 */
    AMBIENT = "ambient",
    /** 界面音效 */
    UI = "ui"
}
/**
 * 音频事件类型
 */
export declare enum AudioEventType {
    /** 加载完成 */
    LOAD = "load",
    /** 加载错误 */
    ERROR = "error",
    /** 播放 */
    PLAY = "play",
    /** 暂停 */
    PAUSE = "pause",
    /** 停止 */
    STOP = "stop",
    /** 结束 */
    END = "end",
    /** 循环 */
    LOOP = "loop",
    /** 音量变化 */
    VOLUME_CHANGE = "volumeChange",
    /** 静音变化 */
    MUTE_CHANGE = "muteChange",
    /** 音频添加 */
    AUDIO_ADDED = "audioAdded",
    /** 音频移除 */
    AUDIO_REMOVED = "audioRemoved"
}
/**
 * 音频系统选项
 */
export interface AudioSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 全局音量 */
    volume?: number;
    /** 是否静音 */
    muted?: boolean;
    /** 音频类型音量 */
    typeVolumes?: {
        [key in AudioType]?: number;
    };
    /** 音频类型静音 */
    typeMuted?: {
        [key in AudioType]?: boolean;
    };
    /** 是否启用空间音频 */
    spatialAudio?: boolean;
    /** 是否自动加载 */
    autoLoad?: boolean;
    /** 最大同时播放数量 */
    maxSimultaneous?: number;
    /** 音频上下文选项 */
    contextOptions?: AudioContextOptions;
}
/**
 * 音频系统
 */
export declare class AudioSystem extends System {
    /** 系统名称 */
    static readonly NAME: string;
    /** 是否启用音频系统 */
    private audioEnabled;
    /** 全局音量 */
    private volume;
    /** 是否静音 */
    private muted;
    /** 音频类型音量 */
    private typeVolumes;
    /** 音频类型静音 */
    private typeMuted;
    /** 是否启用空间音频 */
    private spatialAudio;
    /** 是否自动加载 */
    private autoLoad;
    /** 最大同时播放数量 */
    private maxSimultaneous;
    /** 音频上下文 */
    private context;
    /** 主音量节点 */
    private masterGain;
    /** 类型音量节点 */
    private typeGains;
    /** 音频监听器 */
    private listener;
    /** 音频源映射 */
    private sources;
    /** 当前播放的音频源 */
    private playingSources;
    /** 音频缓存 */
    private bufferCache;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /** 音频加载器 */
    private audioLoader;
    /** 是否支持Web Audio API */
    private supported;
    /**
     * 创建音频系统
     * @param options 音频系统选项
     */
    constructor(options?: AudioSystemOptions);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 恢复音频上下文
     */
    private resumeContext;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 创建音频源
     * @param id 音频源ID
     * @param type 音频类型
     * @returns 音频源
     */
    createSource(id: string, type?: AudioType): AudioSource | null;
    /**
     * 移除音频源
     * @param id 音频源ID
     * @returns 是否成功移除
     */
    removeSource(id: string): boolean;
    /**
     * 获取音频源
     * @param id 音频源ID
     * @returns 音频源
     */
    getSource(id: string): AudioSource | null;
    /**
     * 获取所有音频源
     * @returns 音频源数组
     */
    getSources(): AudioSource[];
    /**
     * 获取指定类型的音频源
     * @param type 音频类型
     * @returns 音频源数组
     */
    getSourcesByType(type: AudioType): AudioSource[];
    /**
     * 获取当前播放的音频源
     * @returns 音频源数组
     */
    getPlayingSources(): AudioSource[];
    /**
     * 加载音频
     * @param url 音频URL
     * @param onLoad 加载完成回调
     * @param onError 加载错误回调
     */
    loadAudio(url: string, onLoad?: (buffer: AudioBuffer) => void, onError?: (error: Error) => void): void;
    /**
     * 播放音频
     * @param id 音频源ID
     * @param url 音频URL
     * @param options 播放选项
     * @returns 是否成功开始播放
     */
    play(id: string, url: string, options?: any): boolean;
    /**
     * 停止音频
     * @param id 音频源ID
     * @returns 是否成功停止
     */
    stop(id: string): boolean;
    /**
     * 暂停音频
     * @param id 音频源ID
     * @returns 是否成功暂停
     */
    pause(id: string): boolean;
    /**
     * 恢复音频
     * @param id 音频源ID
     * @returns 是否成功恢复
     */
    resume(id: string): boolean;
    /**
     * 停止所有音频
     */
    stopAll(): void;
    /**
     * 暂停所有音频
     */
    pauseAll(): void;
    /**
     * 恢复所有音频
     */
    resumeAll(): void;
    /**
     * 停止指定类型的所有音频
     * @param type 音频类型
     */
    stopAllByType(type: AudioType): void;
    /**
     * 暂停指定类型的所有音频
     * @param type 音频类型
     */
    pauseAllByType(type: AudioType): void;
    /**
     * 恢复指定类型的所有音频
     * @param type 音频类型
     */
    resumeAllByType(type: AudioType): void;
    /**
     * 设置全局音量
     * @param volume 音量（0-1）
     */
    setVolume(volume: number): void;
    /**
     * 获取全局音量
     * @returns 音量（0-1）
     */
    getVolume(): number;
    /**
     * 设置全局静音
     * @param muted 是否静音
     */
    setMuted(muted: boolean): void;
    /**
     * 获取全局静音
     * @returns 是否静音
     */
    getMuted(): boolean;
    /**
     * 设置音频类型音量
     * @param type 音频类型
     * @param volume 音量（0-1）
     */
    setTypeVolume(type: AudioType, volume: number): void;
    /**
     * 获取音频类型音量
     * @param type 音频类型
     * @returns 音量（0-1）
     */
    getTypeVolume(type: AudioType): number;
    /**
     * 设置音频类型静音
     * @param type 音频类型
     * @param muted 是否静音
     */
    setTypeMuted(type: AudioType, muted: boolean): void;
    /**
     * 获取音频类型静音
     * @param type 音频类型
     * @returns 是否静音
     */
    getTypeMuted(type: AudioType): boolean;
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    setAudioEnabled(enabled: boolean): void;
    /**
     * 获取是否启用
     * @returns 是否启用
     */
    getAudioEnabled(): boolean;
    /**
     * 设置是否启用空间音频
     * @param enabled 是否启用
     */
    setSpatialAudio(enabled: boolean): void;
    /**
     * 获取是否启用空间音频
     * @returns 是否启用
     */
    getSpatialAudio(): boolean;
    /**
     * 获取音频监听器
     * @returns 音频监听器
     */
    getListener(): AudioListener | null;
    /**
     * 获取音频上下文
     * @returns 音频上下文
     */
    getContext(): AudioContext | null;
    /**
     * 获取是否支持Web Audio API
     * @returns 是否支持
     */
    isSupported(): boolean;
    /**
     * 清除音频缓存
     */
    clearCache(): void;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     * @returns 当前实例，用于链式调用
     */
    on(event: string, callback: EventCallback): this;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     * @returns 当前实例，用于链式调用
     */
    off(event: string, callback?: EventCallback): this;
    /**
     * 销毁系统
     */
    dispose(): void;
}
