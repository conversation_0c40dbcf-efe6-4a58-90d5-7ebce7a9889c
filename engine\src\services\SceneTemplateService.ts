/**
 * 场景模板服务
 * 管理预定义场景模板的创建、存储、检索和应用
 */
import { EventEmitter } from '../utils/EventEmitter';
import * as THREE from 'three';
// import { DatabaseManager } from '../database/DatabaseManager';

// 临时类型定义，实际应该从相应模块导入
interface DatabaseManager {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  query(sql: string, params?: any[]): Promise<any>;
  insert(table: string, data: any): Promise<any>;
  update(table: string, data: any, where: any): Promise<any>;
  delete(table: string, where: any): Promise<any>;
  saveTemplate(template: any): Promise<void>;
  searchTemplates(criteria: any): Promise<any>;
  getTemplate(id: string): Promise<any>;
  updateTemplate(id: string, data: any): Promise<void>;
  deleteTemplate(id: string): Promise<void>;
  getTemplateCategories(): Promise<any[]>;
  getPopularTemplates(limit: number): Promise<any[]>;
  getUserTemplates(userId: string): Promise<any[]>;
  favoriteTemplate(templateId: string, userId: string): Promise<void>;
  unfavoriteTemplate(templateId: string, userId: string): Promise<void>;
  rateTemplate(templateId: string, rating: number, review: string, userId: string): Promise<void>;
  updateTemplateStats(templateId: string, action: string): Promise<void>;
  getAllTemplates(): Promise<any[]>;
}

// 简单的实现类
class MockDatabaseManager implements DatabaseManager {
  async connect(): Promise<void> { }
  async disconnect(): Promise<void> { }
  async query(sql: string, params?: any[]): Promise<any> { return []; }
  async insert(table: string, data: any): Promise<any> { return { id: 'mock-id' }; }
  async update(table: string, data: any, where: any): Promise<any> { return {}; }
  async delete(table: string, where: any): Promise<any> { return {}; }
  async saveTemplate(template: any): Promise<void> { }
  async searchTemplates(criteria: any): Promise<any> { return { templates: [], total: 0 }; }
  async getTemplate(id: string): Promise<any> { return null; }
  async updateTemplate(id: string, data: any): Promise<void> { }
  async deleteTemplate(id: string): Promise<void> { }
  async getTemplateCategories(): Promise<any[]> { return []; }
  async getPopularTemplates(limit: number): Promise<any[]> { return []; }
  async getUserTemplates(userId: string): Promise<any[]> { return []; }
  async favoriteTemplate(templateId: string, userId: string): Promise<void> { }
  async unfavoriteTemplate(templateId: string, userId: string): Promise<void> { }
  async rateTemplate(templateId: string, rating: number, review: string, userId: string): Promise<void> { }
  async updateTemplateStats(templateId: string, action: string): Promise<void> { }
  async getAllTemplates(): Promise<any[]> { return []; }
}
import { SceneLayout, SceneUnderstanding } from '../ai/scene/SceneGenerationTypes';

/**
 * 场景模板
 */
export interface SceneTemplate {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description: string;
  /** 模板类型 */
  type: 'office' | 'living_room' | 'bedroom' | 'kitchen' | 'classroom' | 'meeting_room' | 'custom';
  /** 风格 */
  style: string;
  /** 标签 */
  tags: string[];
  /** 预览图 */
  thumbnail?: string;
  /** 场景布局 */
  layout: SceneLayout;
  /** 场景理解 */
  understanding: SceneUnderstanding;
  /** 模板参数 */
  parameters: TemplateParameter[];
  /** 创建者 */
  creator: string;
  /** 是否公开 */
  isPublic: boolean;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 使用统计 */
  usageStats: {
    usageCount: number;
    rating: number;
    reviewCount: number;
    favoriteCount: number;
  };
  /** 版本信息 */
  version: string;
  /** 兼容性 */
  compatibility: {
    minEngineVersion: string;
    maxEngineVersion?: string;
    requiredFeatures: string[];
  };
}

/**
 * 模板参数
 */
export interface TemplateParameter {
  /** 参数名 */
  name: string;
  /** 参数类型 */
  type: 'string' | 'number' | 'boolean' | 'color' | 'select' | 'range';
  /** 参数标签 */
  label: string;
  /** 参数描述 */
  description: string;
  /** 默认值 */
  defaultValue: any;
  /** 可选值（用于select类型） */
  options?: { label: string; value: any }[];
  /** 范围（用于range类型） */
  range?: { min: number; max: number; step: number };
  /** 是否必需 */
  required: boolean;
}

/**
 * 模板搜索条件
 */
export interface TemplateSearchCriteria {
  /** 关键词 */
  keywords?: string;
  /** 类型过滤 */
  type?: string;
  /** 风格过滤 */
  style?: string;
  /** 标签过滤 */
  tags?: string[];
  /** 创建者过滤 */
  creator?: string;
  /** 是否只显示公开模板 */
  publicOnly?: boolean;
  /** 排序方式 */
  sortBy?: 'name' | 'createdAt' | 'rating' | 'usageCount';
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 分页 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
}

/**
 * 模板应用选项
 */
export interface TemplateApplyOptions {
  /** 参数值 */
  parameters?: Record<string, any>;
  /** 是否保留现有元素 */
  preserveExisting?: boolean;
  /** 是否自动调整尺寸 */
  autoScale?: boolean;
  /** 目标场景边界 */
  targetBounds?: any;
}

/**
 * 场景模板服务
 */
export class SceneTemplateService extends EventEmitter {
  private dbManager: DatabaseManager;
  private templateCache: Map<string, SceneTemplate> = new Map();
  private builtinTemplates: SceneTemplate[] = [];

  constructor() {
    super();
    this.dbManager = new MockDatabaseManager();
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    await this.dbManager.connect();
    await this.loadBuiltinTemplates();
    await this.loadTemplateCache();
    
    console.log('场景模板服务初始化完成');
  }

  /**
   * 创建模板
   */
  async createTemplate(
    templateData: Omit<SceneTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usageStats'>,
    userId: string
  ): Promise<SceneTemplate> {
    try {
      const template: SceneTemplate = {
        ...templateData,
        id: this.generateTemplateId(),
        creator: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        usageStats: {
          usageCount: 0,
          rating: 0,
          reviewCount: 0,
          favoriteCount: 0
        }
      };

      // 验证模板
      this.validateTemplate(template);

      // 保存到数据库
      await this.dbManager.saveTemplate(template);

      // 更新缓存
      this.templateCache.set(template.id, template);

      // 发出事件
      this.emit('templateCreated', template);

      return template;
    } catch (error) {
      console.error('创建模板失败:', error);
      throw error;
    }
  }

  /**
   * 搜索模板
   */
  async searchTemplates(criteria: TemplateSearchCriteria): Promise<{
    templates: SceneTemplate[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const result = await this.dbManager.searchTemplates(criteria);
      
      // 更新使用统计
      result.templates.forEach(template => {
        this.updateUsageStats(template.id, 'search');
      });

      return result;
    } catch (error) {
      console.error('模板搜索失败:', error);
      throw error;
    }
  }

  /**
   * 获取模板详情
   */
  async getTemplate(templateId: string): Promise<SceneTemplate | null> {
    try {
      // 先从缓存获取
      if (this.templateCache.has(templateId)) {
        return this.templateCache.get(templateId)!;
      }

      // 从数据库获取
      const template = await this.dbManager.getTemplate(templateId);
      if (template) {
        this.templateCache.set(templateId, template);
      }

      return template;
    } catch (error) {
      console.error('获取模板失败:', error);
      return null;
    }
  }

  /**
   * 应用模板
   */
  async applyTemplate(
    templateId: string,
    options: TemplateApplyOptions = {},
    userId: string
  ): Promise<{ layout: SceneLayout; understanding: SceneUnderstanding }> {
    try {
      const template = await this.getTemplate(templateId);
      if (!template) {
        throw new Error('模板不存在');
      }

      // 应用参数
      const appliedLayout = this.applyParameters(template.layout, options.parameters || {});
      const appliedUnderstanding = this.applyParameters(template.understanding, options.parameters || {});

      // 自动调整尺寸
      if (options.autoScale && options.targetBounds) {
        this.scaleLayout(appliedLayout, options.targetBounds);
      }

      // 更新使用统计
      await this.updateUsageStats(templateId, 'apply');

      // 发出事件
      this.emit('templateApplied', { templateId, userId });

      return {
        layout: appliedLayout,
        understanding: appliedUnderstanding
      };
    } catch (error) {
      console.error('应用模板失败:', error);
      throw error;
    }
  }

  /**
   * 更新模板
   */
  async updateTemplate(
    templateId: string,
    updates: Partial<SceneTemplate>,
    userId: string
  ): Promise<SceneTemplate> {
    try {
      const template = await this.getTemplate(templateId);
      if (!template) {
        throw new Error('模板不存在');
      }

      // 检查权限
      await this.checkUpdatePermission(templateId, userId);

      // 更新模板
      const updatedTemplate: SceneTemplate = {
        ...template,
        ...updates,
        updatedAt: new Date()
      };

      // 验证模板
      this.validateTemplate(updatedTemplate);

      // 保存到数据库
      await this.dbManager.updateTemplate(templateId, updatedTemplate);

      // 更新缓存
      this.templateCache.set(templateId, updatedTemplate);

      // 发出事件
      this.emit('templateUpdated', updatedTemplate);

      return updatedTemplate;
    } catch (error) {
      console.error('更新模板失败:', error);
      throw error;
    }
  }

  /**
   * 删除模板
   */
  async deleteTemplate(templateId: string, userId: string): Promise<void> {
    try {
      const template = await this.getTemplate(templateId);
      if (!template) {
        throw new Error('模板不存在');
      }

      // 检查权限
      await this.checkDeletePermission(templateId, userId);

      // 从数据库删除
      await this.dbManager.deleteTemplate(templateId);

      // 从缓存删除
      this.templateCache.delete(templateId);

      // 发出事件
      this.emit('templateDeleted', { templateId, userId });
    } catch (error) {
      console.error('删除模板失败:', error);
      throw error;
    }
  }

  /**
   * 获取模板分类
   */
  async getTemplateCategories(): Promise<{ type: string; count: number }[]> {
    try {
      return await this.dbManager.getTemplateCategories();
    } catch (error) {
      console.error('获取模板分类失败:', error);
      return [];
    }
  }

  /**
   * 获取热门模板
   */
  async getPopularTemplates(limit: number = 10): Promise<SceneTemplate[]> {
    try {
      return await this.dbManager.getPopularTemplates(limit);
    } catch (error) {
      console.error('获取热门模板失败:', error);
      return [];
    }
  }

  /**
   * 获取用户模板
   */
  async getUserTemplates(userId: string): Promise<SceneTemplate[]> {
    try {
      return await this.dbManager.getUserTemplates(userId);
    } catch (error) {
      console.error('获取用户模板失败:', error);
      return [];
    }
  }

  /**
   * 收藏模板
   */
  async favoriteTemplate(templateId: string, userId: string): Promise<void> {
    try {
      await this.dbManager.favoriteTemplate(templateId, userId);
      await this.updateUsageStats(templateId, 'favorite');
      
      this.emit('templateFavorited', { templateId, userId });
    } catch (error) {
      console.error('收藏模板失败:', error);
      throw error;
    }
  }

  /**
   * 取消收藏模板
   */
  async unfavoriteTemplate(templateId: string, userId: string): Promise<void> {
    try {
      await this.dbManager.unfavoriteTemplate(templateId, userId);
      await this.updateUsageStats(templateId, 'unfavorite');
      
      this.emit('templateUnfavorited', { templateId, userId });
    } catch (error) {
      console.error('取消收藏失败:', error);
      throw error;
    }
  }

  /**
   * 评价模板
   */
  async rateTemplate(
    templateId: string,
    rating: number,
    userId: string,
    review?: string
  ): Promise<void> {
    try {
      if (rating < 1 || rating > 5) {
        throw new Error('评分必须在1-5之间');
      }

      await this.dbManager.rateTemplate(templateId, rating, review, userId);
      await this.updateUsageStats(templateId, 'rate');
      
      this.emit('templateRated', { templateId, rating, review, userId });
    } catch (error) {
      console.error('评价模板失败:', error);
      throw error;
    }
  }

  /**
   * 生成模板ID
   */
  private generateTemplateId(): string {
    return `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 验证模板
   */
  private validateTemplate(template: SceneTemplate): void {
    if (!template.name || template.name.trim().length === 0) {
      throw new Error('模板名称不能为空');
    }

    if (!template.layout || !template.understanding) {
      throw new Error('模板必须包含布局和理解信息');
    }

    // 验证参数
    template.parameters.forEach(param => {
      if (!param.name || !param.type) {
        throw new Error('模板参数必须包含名称和类型');
      }
    });
  }

  /**
   * 应用参数
   */
  private applyParameters(data: any, parameters: Record<string, any>): any {
    // 深拷贝数据
    const result = JSON.parse(JSON.stringify(data));
    
    // 应用参数替换
    Object.entries(parameters).forEach(([key, value]) => {
      this.replaceParameterInObject(result, `{{${key}}}`, value);
    });

    return result;
  }

  /**
   * 在对象中替换参数
   */
  private replaceParameterInObject(obj: any, placeholder: string, value: any): any {
    if (typeof obj === 'string') {
      return obj.replace(placeholder, value);
    }

    if (Array.isArray(obj)) {
      obj.forEach(item => this.replaceParameterInObject(item, placeholder, value));
    } else if (typeof obj === 'object' && obj !== null) {
      Object.keys(obj).forEach(key => {
        obj[key] = this.replaceParameterInObject(obj[key], placeholder, value);
      });
    }
  }

  /**
   * 缩放布局
   */
  private scaleLayout(layout: SceneLayout, targetBounds: any): void {
    // 实现布局缩放逻辑
    const currentBounds = layout.bounds;
    const size = new THREE.Vector3();
    currentBounds.getSize(size);
    const scaleX = targetBounds.width / size.x;
    const scaleY = targetBounds.height / size.y;
    const scaleZ = targetBounds.depth / size.z;
    
    const scale = Math.min(scaleX, scaleY, scaleZ);
    
    layout.elements.forEach(element => {
      element.position.multiplyScalar(scale);
      element.scale.multiplyScalar(scale);
    });
  }

  /**
   * 更新使用统计
   */
  private async updateUsageStats(
    templateId: string,
    action: 'search' | 'apply' | 'favorite' | 'unfavorite' | 'rate'
  ): Promise<void> {
    try {
      await this.dbManager.updateTemplateStats(templateId, action);
    } catch (error) {
      console.error('更新统计失败:', error);
    }
  }

  /**
   * 检查更新权限
   */
  private async checkUpdatePermission(templateId: string, userId: string): Promise<void> {
    const template = await this.getTemplate(templateId);
    if (template && template.creator !== userId) {
      throw new Error('没有权限更新此模板');
    }
  }

  /**
   * 检查删除权限
   */
  private async checkDeletePermission(templateId: string, userId: string): Promise<void> {
    const template = await this.getTemplate(templateId);
    if (template && template.creator !== userId) {
      throw new Error('没有权限删除此模板');
    }
  }

  /**
   * 加载内置模板
   */
  private async loadBuiltinTemplates(): Promise<void> {
    this.builtinTemplates = [
      {
        id: 'builtin_office_modern',
        name: '现代办公室',
        description: '简洁现代的办公空间，包含会议桌和办公椅',
        type: 'office',
        style: 'modern',
        tags: ['办公', '现代', '简约'],
        layout: this.createDefaultOfficeLayout(),
        understanding: this.createDefaultOfficeUnderstanding(),
        parameters: [
          {
            name: 'tableSize',
            type: 'select',
            label: '会议桌尺寸',
            description: '选择会议桌的大小',
            defaultValue: 'medium',
            options: [
              { label: '小型(4人)', value: 'small' },
              { label: '中型(8人)', value: 'medium' },
              { label: '大型(12人)', value: 'large' }
            ],
            required: true
          },
          {
            name: 'chairCount',
            type: 'range',
            label: '椅子数量',
            description: '设置椅子的数量',
            defaultValue: 8,
            range: { min: 4, max: 16, step: 2 },
            required: true
          }
        ],
        creator: 'system',
        isPublic: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        usageStats: { usageCount: 0, rating: 0, reviewCount: 0, favoriteCount: 0 },
        version: '1.0.0',
        compatibility: {
          minEngineVersion: '1.0.0',
          requiredFeatures: ['basic_rendering']
        }
      }
      // 可以添加更多内置模板
    ];

    // 将内置模板添加到缓存
    this.builtinTemplates.forEach(template => {
      this.templateCache.set(template.id, template);
    });
  }

  /**
   * 创建默认办公室布局
   */
  private createDefaultOfficeLayout(): SceneLayout {
    // 返回简化的默认布局
    return {
      id: 'default_office_layout',
      bounds: new (require('three')).Box3(),
      elements: [],
      environment: {
        ambientColor: new (require('three')).Color(0x404040),
        ambientIntensity: 0.4
      }
    };
  }

  /**
   * 创建默认办公室理解
   */
  private createDefaultOfficeUnderstanding(): SceneUnderstanding {
    return {
      elements: [],
      spatialRelations: [],
      intent: {
        sceneType: 'office',
        style: 'modern',
        functionality: ['work', 'meeting']
      },
      constraints: [],
      confidence: 1.0
    };
  }

  /**
   * 加载模板缓存
   */
  private async loadTemplateCache(): Promise<void> {
    try {
      const templates = await this.dbManager.getAllTemplates();
      templates.forEach(template => {
        this.templateCache.set(template.id, template);
      });
      console.log(`加载了 ${templates.length} 个模板到缓存`);
    } catch (error) {
      console.error('加载模板缓存失败:', error);
    }
  }

  /**
   * 销毁服务
   */
  async destroy(): Promise<void> {
    await this.dbManager.disconnect();
    this.templateCache.clear();
    this.removeAllListeners();
  }
}
