/**
 * 服务注册控制器
 */
import { Controller, Get, Post, Body, Param, Delete, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MessagePattern } from '@nestjs/microservices';
import { RegistryService } from './registry.service';
import { RegisterServiceInstanceDto } from './dto/register-service.dto';
import { HeartbeatDto } from './dto/heartbeat.dto';
import { DiscoverServiceDto } from './dto/discover-service.dto';
import { ServiceEntity } from './entities/service.entity';
import { ServiceInstanceEntity } from './entities/service-instance.entity';

@ApiTags('服务注册')
@Controller('registry')
export class RegistryController {
  constructor(private readonly registryService: RegistryService) {}

  @Post('register')
  @ApiOperation({ summary: '注册服务实例' })
  @ApiResponse({ status: 201, description: '服务实例注册成功' })
  async registerService(@Body() dto: RegisterServiceInstanceDto): Promise<ServiceInstanceEntity> {
    return this.registryService.registerService(dto);
  }

  @Post('heartbeat')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '服务心跳' })
  async heartbeat(@Body() dto: HeartbeatDto): Promise<void> {
    await this.registryService.heartbeat(dto);
  }

  @Post('discover')
  @ApiOperation({ summary: '发现服务实例' })
  @ApiResponse({ status: 200, description: '返回服务实例列表' })
  async discoverService(@Body() dto: DiscoverServiceDto): Promise<ServiceInstanceEntity[]> {
    return this.registryService.discoverService(dto);
  }

  @Get('services')
  @ApiOperation({ summary: '获取所有服务' })
  @ApiResponse({ status: 200, description: '返回所有服务列表' })
  async getAllServices(): Promise<ServiceEntity[]> {
    return this.registryService.getAllServices();
  }

  @Get('services/:name')
  @ApiOperation({ summary: '获取服务详情' })
  @ApiResponse({ status: 200, description: '返回服务详情' })
  async getServiceDetails(@Param('name') name: string): Promise<ServiceEntity> {
    return this.registryService.getServiceDetails(name);
  }

  @Delete('services/:name/instances/:instanceId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '注销服务实例' })
  async deregisterInstance(
    @Param('name') name: string,
    @Param('instanceId') instanceId: string,
  ): Promise<void> {
    await this.registryService.deregisterInstance(name, instanceId);
  }

  // 微服务消息处理
  @MessagePattern({ cmd: 'register' })
  async handleRegister(data: RegisterServiceInstanceDto): Promise<ServiceInstanceEntity> {
    return this.registryService.registerService(data);
  }

  @MessagePattern({ cmd: 'heartbeat' })
  async handleHeartbeat(data: HeartbeatDto): Promise<void> {
    await this.registryService.heartbeat(data);
  }

  @MessagePattern({ cmd: 'discover' })
  async handleDiscover(data: DiscoverServiceDto): Promise<ServiceInstanceEntity[]> {
    return this.registryService.discoverService(data);
  }
}
