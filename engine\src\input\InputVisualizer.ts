/**
 * 输入可视化工具
 * 用于可视化输入状态和事件
 */
import { InputManager } from './InputManager';
import { InputDevice } from './InputDevice';
import { InputAction } from './InputAction';
import { InputComponent } from './components/InputComponent';

/**
 * 输入可视化器选项
 */
export interface InputVisualizerOptions {
  /** 容器元素 */
  container?: HTMLElement;
  /** 是否显示设备状态 */
  showDevices?: boolean;
  /** 是否显示动作状态 */
  showActions?: boolean;
  /** 是否显示事件日志 */
  showEventLog?: boolean;
  /** 最大事件日志条数 */
  maxEventLogEntries?: number;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新间隔（毫秒） */
  updateInterval?: number;
}

/**
 * 输入事件日志条目
 */
interface InputEventLogEntry {
  /** 时间戳 */
  timestamp: number;
  /** 设备名称 */
  deviceName: string;
  /** 事件类型 */
  eventType: string;
  /** 事件数据 */
  eventData: any;
}

/**
 * 输入可视化器
 */
export class InputVisualizer {
  /** 输入管理器 */
  private inputManager: InputManager;

  /** 容器元素 */
  private container: HTMLElement;

  /** 设备状态元素 */
  private devicesElement: HTMLElement;

  /** 动作状态元素 */
  private actionsElement: HTMLElement;

  /** 事件日志元素 */
  private eventLogElement: HTMLElement;

  /** 是否显示设备状态 */
  private showDevices: boolean;

  /** 是否显示动作状态 */
  private showActions: boolean;

  /** 是否显示事件日志 */
  private showEventLog: boolean;

  /** 最大事件日志条数 */
  private maxEventLogEntries: number;

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新间隔（毫秒） */
  private updateInterval: number;

  /** 更新定时器 */
  private updateTimer: number | null = null;

  /** 事件日志 */
  private eventLog: InputEventLogEntry[] = [];

  /** 设备事件处理器 */
  private deviceEventHandlers: Map<string, (event: any) => void> = new Map();

  /** 输入组件列表 */
  private inputComponents: InputComponent[] = [];

  /**
   * 创建输入可视化器
   * @param options 选项
   */
  constructor(options: InputVisualizerOptions = {}) {
    this.inputManager = InputManager.getInstance();
    this.container = options.container || document.body;
    this.showDevices = options.showDevices !== undefined ? options.showDevices : true;
    this.showActions = options.showActions !== undefined ? options.showActions : true;
    this.showEventLog = options.showEventLog !== undefined ? options.showEventLog : true;
    this.maxEventLogEntries = options.maxEventLogEntries || 100;
    this.autoUpdate = options.autoUpdate !== undefined ? options.autoUpdate : true;
    this.updateInterval = options.updateInterval || 100;

    // 创建UI元素
    this.createUI();

    // 添加设备事件监听器
    this.addDeviceEventListeners();

    // 开始自动更新
    if (this.autoUpdate) {
      this.startAutoUpdate();
    }
  }

  /**
   * 创建UI元素
   */
  private createUI(): void {
    // 创建主容器
    const visualizerContainer = document.createElement('div');
    visualizerContainer.className = 'input-visualizer';
    visualizerContainer.style.position = 'absolute';
    visualizerContainer.style.top = '10px';
    visualizerContainer.style.right = '10px';
    visualizerContainer.style.width = '300px';
    visualizerContainer.style.maxHeight = '80vh';
    visualizerContainer.style.overflowY = 'auto';
    visualizerContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    visualizerContainer.style.color = 'white';
    visualizerContainer.style.fontFamily = 'monospace';
    visualizerContainer.style.fontSize = '12px';
    visualizerContainer.style.padding = '10px';
    visualizerContainer.style.borderRadius = '5px';
    visualizerContainer.style.zIndex = '1000';

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '输入可视化器';
    title.style.margin = '0 0 10px 0';
    title.style.fontSize = '16px';
    title.style.fontWeight = 'bold';
    visualizerContainer.appendChild(title);

    // 创建设备状态容器
    if (this.showDevices) {
      const devicesTitle = document.createElement('h3');
      devicesTitle.textContent = '设备状态';
      devicesTitle.style.margin = '10px 0 5px 0';
      devicesTitle.style.fontSize = '14px';
      devicesTitle.style.fontWeight = 'bold';
      visualizerContainer.appendChild(devicesTitle);

      this.devicesElement = document.createElement('div');
      this.devicesElement.className = 'input-visualizer-devices';
      visualizerContainer.appendChild(this.devicesElement);
    }

    // 创建动作状态容器
    if (this.showActions) {
      const actionsTitle = document.createElement('h3');
      actionsTitle.textContent = '动作状态';
      actionsTitle.style.margin = '10px 0 5px 0';
      actionsTitle.style.fontSize = '14px';
      actionsTitle.style.fontWeight = 'bold';
      visualizerContainer.appendChild(actionsTitle);

      this.actionsElement = document.createElement('div');
      this.actionsElement.className = 'input-visualizer-actions';
      visualizerContainer.appendChild(this.actionsElement);
    }

    // 创建事件日志容器
    if (this.showEventLog) {
      const eventLogTitle = document.createElement('h3');
      eventLogTitle.textContent = '事件日志';
      eventLogTitle.style.margin = '10px 0 5px 0';
      eventLogTitle.style.fontSize = '14px';
      eventLogTitle.style.fontWeight = 'bold';
      visualizerContainer.appendChild(eventLogTitle);

      const eventLogControls = document.createElement('div');
      eventLogControls.style.marginBottom = '5px';

      const clearButton = document.createElement('button');
      clearButton.textContent = '清除';
      clearButton.onclick = () => this.clearEventLog();
      eventLogControls.appendChild(clearButton);

      visualizerContainer.appendChild(eventLogControls);

      this.eventLogElement = document.createElement('div');
      this.eventLogElement.className = 'input-visualizer-event-log';
      this.eventLogElement.style.maxHeight = '200px';
      this.eventLogElement.style.overflowY = 'auto';
      this.eventLogElement.style.border = '1px solid #444';
      this.eventLogElement.style.padding = '5px';
      this.eventLogElement.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
      visualizerContainer.appendChild(this.eventLogElement);
    }

    // 添加到容器
    this.container.appendChild(visualizerContainer);
  }

  /**
   * 添加设备事件监听器
   */
  private addDeviceEventListeners(): void {
    // 获取所有设备
    const devices = this.getAllDevices();

    // 添加事件监听器
    for (const device of devices) {
      const deviceName = device.getName();
      const handler = (event: any) => {
        // 添加事件日志
        this.addEventLogEntry(deviceName, event.type, event);
      };

      // 添加事件监听器
      device.on('*', handler);

      // 保存处理器
      this.deviceEventHandlers.set(deviceName, handler);
    }
  }

  /**
   * 移除设备事件监听器
   */
  private removeDeviceEventListeners(): void {
    // 获取所有设备
    const devices = this.getAllDevices();

    // 移除事件监听器
    for (const device of devices) {
      const deviceName = device.getName();
      const handler = this.deviceEventHandlers.get(deviceName);
      if (handler) {
        device.off('*', handler);
        this.deviceEventHandlers.delete(deviceName);
      }
    }
  }

  /**
   * 获取所有设备
   * @returns 设备列表
   */
  private getAllDevices(): InputDevice[] {
    const devices: InputDevice[] = [];
    
    // 获取键盘设备
    const keyboard = this.inputManager.getDevice('keyboard');
    if (keyboard) devices.push(keyboard);
    
    // 获取鼠标设备
    const mouse = this.inputManager.getDevice('mouse');
    if (mouse) devices.push(mouse);
    
    // 获取触摸设备
    const touch = this.inputManager.getDevice('touch');
    if (touch) devices.push(touch);
    
    // 获取游戏手柄设备
    const gamepad = this.inputManager.getDevice('gamepad');
    if (gamepad) devices.push(gamepad);
    
    // 获取XR设备
    const xr = this.inputManager.getDevice('xr');
    if (xr) devices.push(xr);
    
    return devices;
  }

  /**
   * 添加输入组件
   * @param component 输入组件
   */
  public addInputComponent(component: InputComponent): void {
    if (!this.inputComponents.includes(component)) {
      this.inputComponents.push(component);
    }
  }

  /**
   * 移除输入组件
   * @param component 输入组件
   */
  public removeInputComponent(component: InputComponent): void {
    const index = this.inputComponents.indexOf(component);
    if (index !== -1) {
      this.inputComponents.splice(index, 1);
    }
  }

  /**
   * 添加事件日志条目
   * @param deviceName 设备名称
   * @param eventType 事件类型
   * @param eventData 事件数据
   */
  private addEventLogEntry(deviceName: string, eventType: string, eventData: any): void {
    // 添加事件日志条目
    this.eventLog.push({
      timestamp: Date.now(),
      deviceName,
      eventType,
      eventData
    });

    // 限制事件日志条数
    if (this.eventLog.length > this.maxEventLogEntries) {
      this.eventLog.shift();
    }

    // 更新事件日志UI
    if (this.showEventLog) {
      this.updateEventLogUI();
    }
  }

  /**
   * 清除事件日志
   */
  public clearEventLog(): void {
    this.eventLog = [];
    
    // 更新事件日志UI
    if (this.showEventLog) {
      this.updateEventLogUI();
    }
  }

  /**
   * 开始自动更新
   */
  public startAutoUpdate(): void {
    if (this.updateTimer !== null) return;

    this.updateTimer = window.setInterval(() => {
      this.update();
    }, this.updateInterval);
  }

  /**
   * 停止自动更新
   */
  public stopAutoUpdate(): void {
    if (this.updateTimer === null) return;

    window.clearInterval(this.updateTimer);
    this.updateTimer = null;
  }

  /**
   * 更新
   */
  public update(): void {
    // 更新设备状态UI
    if (this.showDevices) {
      this.updateDevicesUI();
    }

    // 更新动作状态UI
    if (this.showActions) {
      this.updateActionsUI();
    }
  }

  /**
   * 更新设备状态UI
   */
  private updateDevicesUI(): void {
    // 获取所有设备
    const devices = this.getAllDevices();

    // 更新设备状态UI
    let html = '';
    for (const device of devices) {
      const deviceName = device.getName();
      const keys = device.getKeys();

      html += `<div style="margin-bottom: 5px;"><strong>${deviceName}</strong></div>`;
      html += '<div style="margin-left: 10px;">';

      for (const key of keys) {
        const value = device.getValue(key);
        html += `<div>${key}: ${this.formatValue(value)}</div>`;
      }

      html += '</div>';
    }

    this.devicesElement.innerHTML = html;
  }

  /**
   * 更新动作状态UI
   */
  private updateActionsUI(): void {
    // 获取所有动作
    const actions: InputAction[] = [];
    for (const component of this.inputComponents) {
      actions.push(...component.getActions());
    }

    // 更新动作状态UI
    let html = '';
    for (const action of actions) {
      const actionName = action.getName();
      const actionType = action.getType();
      const actionValue = action.getValue();

      html += `<div style="margin-bottom: 5px;"><strong>${actionName}</strong> (${actionType})</div>`;
      html += '<div style="margin-left: 10px;">';
      html += `<div>值: ${this.formatValue(actionValue)}</div>`;
      html += '</div>';
    }

    this.actionsElement.innerHTML = html;
  }

  /**
   * 更新事件日志UI
   */
  private updateEventLogUI(): void {
    // 更新事件日志UI
    let html = '';
    for (let i = this.eventLog.length - 1; i >= 0; i--) {
      const entry = this.eventLog[i];
      const time = new Date(entry.timestamp).toISOString().substr(11, 12);
      
      html += `<div style="margin-bottom: 2px; border-bottom: 1px solid #333; padding-bottom: 2px;">`;
      html += `<span style="color: #999;">[${time}]</span> `;
      html += `<span style="color: #6cf;">${entry.deviceName}</span> `;
      html += `<span style="color: #fc6;">${entry.eventType}</span>`;
      
      if (entry.eventData) {
        const data = JSON.stringify(entry.eventData).substr(0, 100);
        html += ` <span style="color: #ccc;">${data}</span>`;
      }
      
      html += `</div>`;
    }

    this.eventLogElement.innerHTML = html;
  }

  /**
   * 格式化值
   * @param value 值
   * @returns 格式化后的值
   */
  private formatValue(value: any): string {
    if (value === undefined) return 'undefined';
    if (value === null) return 'null';
    
    if (typeof value === 'boolean') {
      return value ? '<span style="color: #6f6;">true</span>' : '<span style="color: #f66;">false</span>';
    }
    
    if (typeof value === 'number') {
      return `<span style="color: #6cf;">${value.toFixed(4)}</span>`;
    }
    
    if (typeof value === 'string') {
      return `<span style="color: #fc6;">"${value}"</span>`;
    }
    
    if (Array.isArray(value)) {
      return `[${value.map(v => this.formatValue(v)).join(', ')}]`;
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  }

  /**
   * 销毁
   */
  public destroy(): void {
    // 停止自动更新
    this.stopAutoUpdate();

    // 移除设备事件监听器
    this.removeDeviceEventListeners();

    // 移除UI元素
    const visualizerElement = this.container.querySelector('.input-visualizer');
    if (visualizerElement) {
      this.container.removeChild(visualizerElement);
    }
  }
}
