/**
 * 微服务客户端共享模块
 */
import { Module, Global } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Global() // 使模块全局可用
@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'SERVICE_REGISTRY',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('SERVICE_REGISTRY_HOST', 'localhost'),
            port: configService.get<number>('SERVICE_REGISTRY_PORT', 3010),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'USER_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('USER_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('USER_SERVICE_PORT', 3001),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  exports: [ClientsModule], // 导出微服务客户端
})
export class MicroservicesModule {}
