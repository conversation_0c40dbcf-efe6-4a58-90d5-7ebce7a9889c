/**
 * 面部动画编辑器组件
 * 用于编辑和管理面部动画
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { FacialExpressionType, VisemeType } from './FacialAnimationComponent';
import { FacialAnimationClip } from '../animation/FacialAnimationClip';
/**
 * 编辑器状态
 */
export declare enum EditorState {
    /** 停止 */
    STOPPED = "stopped",
    /** 播放 */
    PLAYING = "playing",
    /** 暂停 */
    PAUSED = "paused",
    /** 录制 */
    RECORDING = "recording"
}
/**
 * 面部动画编辑器组件类型
 */
export declare const FacialAnimationEditorComponentType = "FacialAnimationEditorComponent";
/**
 * 面部动画编辑器组件
 */
export declare class FacialAnimationEditorComponent extends Component {
    /** 组件类型 */
    static readonly TYPE = "FacialAnimationEditorComponent";
    /** 事件发射器 */
    private eventEmitter;
    /** 动画片段映射 */
    private clips;
    /** 当前片段名称 */
    private currentClipName;
    /** 当前时间（秒） */
    private currentTime;
    /** 播放速度 */
    private playbackSpeed;
    /** 编辑器状态 */
    private state;
    /** 历史记录 */
    private history;
    /** 历史记录索引 */
    private historyIndex;
    /** 最大历史记录数 */
    private maxHistoryItems;
    /** 是否启用撤销/重做 */
    private undoRedoEnabled;
    /** 是否循环播放 */
    private loop;
    /** 是否启用自动保存 */
    private autoSave;
    /** 自动保存间隔（毫秒） */
    private autoSaveInterval;
    /** 自动保存计时器 */
    private autoSaveTimer;
    /** 是否启用调试 */
    private debug;
    /**
     * 构造函数
     * @param entity 实体
     */
    constructor(entity: Entity);
    /**
     * 获取组件类型
     */
    getType(): string;
    /**
     * 添加动画片段
     * @param clip 动画片段
     * @returns 是否成功添加
     */
    addClip(clip: FacialAnimationClip): boolean;
    /**
     * 移除动画片段
     * @param name 片段名称
     * @returns 是否成功移除
     */
    removeClip(name: string): boolean;
    /**
     * 获取动画片段
     * @param name 片段名称
     * @returns 动画片段
     */
    getClip(name: string): FacialAnimationClip | null;
    /**
     * 获取当前动画片段
     * @returns 当前动画片段
     */
    getCurrentClip(): FacialAnimationClip | null;
    /**
     * 获取所有动画片段
     * @returns 动画片段映射
     */
    getAllClips(): Map<string, FacialAnimationClip>;
    /**
     * 设置当前动画片段
     * @param name 片段名称
     * @returns 是否成功设置
     */
    setCurrentClip(name: string): boolean;
    /**
     * 播放
     * @returns 是否成功播放
     */
    play(): boolean;
    /**
     * 停止
     * @returns 是否成功停止
     */
    stop(): boolean;
    /**
     * 暂停
     * @returns 是否成功暂停
     */
    pause(): boolean;
    /**
     * 设置播放速度
     * @param speed 速度
     * @returns 是否成功设置
     */
    setPlaybackSpeed(speed: number): boolean;
    /**
     * 设置当前时间
     * @param time 时间（秒）
     * @returns 是否成功设置
     */
    setCurrentTime(time: number): boolean;
    /**
     * 添加表情关键帧
     * @param time 时间（秒）
     * @param expression 表情类型
     * @param weight 权重
     * @returns 是否成功添加
     */
    addExpressionKeyframe(time: number, expression: FacialExpressionType, weight?: number): boolean;
    /**
     * 添加口型关键帧
     * @param time 时间（秒）
     * @param viseme 口型类型
     * @param weight 权重
     * @returns 是否成功添加
     */
    addVisemeKeyframe(time: number, viseme: VisemeType, weight?: number): boolean;
    /**
     * 移除表情关键帧
     * @param time 时间（秒）
     * @returns 是否成功移除
     */
    removeExpressionKeyframe(time: number): boolean;
    /**
     * 移除口型关键帧
     * @param time 时间（秒）
     * @returns 是否成功移除
     */
    removeVisemeKeyframe(time: number): boolean;
    /**
     * 保存历史记录
     */
    private saveHistory;
    /**
     * 撤销
     * @returns 是否成功撤销
     */
    undo(): boolean;
    /**
     * 重做
     * @returns 是否成功重做
     */
    redo(): boolean;
    /**
     * 保存状态
     */
    saveState(): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
}
