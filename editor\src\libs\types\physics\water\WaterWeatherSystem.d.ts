import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { WaterBodyComponent } from './WaterBodyComponent';
/**
 * 天气类型
 */
export declare enum WeatherType {
    /** 晴天 */
    SUNNY = "sunny",
    /** 多云 */
    CLOUDY = "cloudy",
    /** 雨天 */
    RAINY = "rainy",
    /** 暴雨 */
    HEAVY_RAIN = "heavyRain",
    /** 雷雨 */
    THUNDERSTORM = "thunderstorm",
    /** 大风 */
    WINDY = "windy",
    /** 暴风 */
    STORM = "storm",
    /** 雪天 */
    SNOWY = "snowy",
    /** 大雪 */
    HEAVY_SNOW = "heavySnow",
    /** 雾天 */
    FOGGY = "foggy"
}
/**
 * 水体天气影响配置
 */
export interface WaterWeatherSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 天气类型 */
    weatherType?: WeatherType;
    /** 风速 */
    windSpeed?: number;
    /** 风向 */
    windDirection?: number;
    /** 雨强度 */
    rainIntensity?: number;
    /** 雪强度 */
    snowIntensity?: number;
    /** 雾强度 */
    fogIntensity?: number;
    /** 雷电强度 */
    thunderIntensity?: number;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 水体天气影响系统事件类型
 */
export declare enum WaterWeatherSystemEventType {
    /** 天气更新 */
    WEATHER_UPDATED = "weatherUpdated",
    /** 天气参数变化 */
    WEATHER_PARAMS_CHANGED = "weatherParamsChanged",
    /** 雨滴生成 */
    RAINDROP_GENERATED = "raindropGenerated",
    /** 雪花生成 */
    SNOWFLAKE_GENERATED = "snowflakeGenerated",
    /** 雷电生成 */
    THUNDER_GENERATED = "thunderGenerated"
}
/**
 * 水体天气影响系统
 */
export declare class WaterWeatherSystem extends System {
    /** 系统类型 */
    static readonly TYPE = "WaterWeatherSystem";
    /** 配置 */
    private config;
    /** 水体实体映射 */
    private waterEntities;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监视器 */
    private performanceMonitor;
    /** 调试对象 */
    private debugObjects;
    /** 水体实例化渲染器 */
    private waterInstancedRenderer;
    /** 雨滴生成计时器 */
    private raindropTimer;
    /** 雪花生成计时器 */
    private snowflakeTimer;
    /** 雷电生成计时器 */
    private thunderTimer;
    /** 风向向量 */
    private windVector;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(config?: WaterWeatherSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新天气效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWeatherEffects;
    /**
     * 更新雨效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateRainEffects;
    /**
     * 获取雨滴生成间隔
     * @returns 雨滴生成间隔（秒）
     */
    private getRaindropInterval;
    /**
     * 生成雨滴
     */
    private generateRaindrops;
    /**
     * 计算雨滴数量
     * @returns 雨滴数量
     */
    private calculateRaindropCount;
    /**
     * 创建雨滴
     * @param position 位置
     */
    private createRaindrop;
    /**
     * 更新雪效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateSnowEffects;
    /**
     * 获取雪花生成间隔
     * @returns 雪花生成间隔（秒）
     */
    private getSnowflakeInterval;
    /**
     * 生成雪花
     */
    private generateSnowflakes;
    /**
     * 计算雪花数量
     * @returns 雪花数量
     */
    private calculateSnowflakeCount;
    /**
     * 创建雪花
     * @param position 位置
     */
    private createSnowflake;
    /**
     * 更新风效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWindEffects;
    /**
     * 生成水雾
     * @param deltaTime 帧间隔时间（秒）
     */
    private generateWaterMist;
    /**
     * 创建水雾
     * @param position 位置
     */
    private createWaterMist;
    /**
     * 更新雾效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateFogEffects;
    /**
     * 更新雷电效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateThunderEffects;
    /**
     * 生成雷电
     */
    private generateThunder;
    /**
     * 更新水体材质
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterMaterials;
    /**
     * 更新风向向量
     */
    private updateWindVector;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 添加水体实体
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterEntity(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体实体
     * @param entity 实体
     */
    removeWaterEntity(entity: Entity): void;
    /**
     * 设置天气类型
     * @param weatherType 天气类型
     */
    setWeatherType(weatherType: WeatherType): void;
    /**
     * 设置天气参数
     * @param params 参数
     */
    setWeatherParams(params: Partial<WaterWeatherSystemConfig>): void;
}
