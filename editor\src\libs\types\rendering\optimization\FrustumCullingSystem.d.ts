import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
import { CullableComponent } from './CullableComponent';
/**
 * 视锥体剔除系统配置接口
 */
export interface FrustumCullingSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率（帧） */
    updateFrequency?: number;
    /** 是否使用八叉树 */
    useOctree?: boolean;
    /** 是否使用遮挡剔除 */
    useOcclusionCulling?: boolean;
    /** 是否使用包围盒剔除 */
    useBoundingBoxCulling?: boolean;
    /** 是否使用包围球剔除 */
    useBoundingSphereCulling?: boolean;
    /** 是否使用距离剔除 */
    useDistanceCulling?: boolean;
    /** 最大剔除距离 */
    maxCullingDistance?: number;
}
/**
 * 视锥体剔除系统事件类型
 */
export declare enum FrustumCullingSystemEventType {
    /** 实体被剔除 */
    ENTITY_CULLED = "entity_culled",
    /** 实体被恢复 */
    ENTITY_RESTORED = "entity_restored",
    /** 组件添加 */
    COMPONENT_ADDED = "component_added",
    /** 组件移除 */
    COMPONENT_REMOVED = "component_removed"
}
/**
 * 视锥体剔除系统类
 */
export declare class FrustumCullingSystem extends System {
    /** 系统类型 */
    private static readonly TYPE;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率（帧） */
    private updateFrequency;
    /** 当前帧计数 */
    private frameCount;
    /** 是否使用八叉树 */
    private useOctree;
    /** 是否使用遮挡剔除 */
    private useOcclusionCulling;
    /** 是否使用包围盒剔除 */
    private useBoundingBoxCulling;
    /** 是否使用包围球剔除 */
    private useBoundingSphereCulling;
    /** 是否使用距离剔除 */
    private useDistanceCulling;
    /** 最大剔除距离 */
    private maxCullingDistance;
    /** 活跃相机 */
    private activeCamera;
    /** 活跃场景 */
    private activeScene;
    /** 可剔除组件列表 */
    private cullableComponents;
    /** 已剔除实体列表 */
    private culledEntities;
    /** 视锥体 */
    private frustum;
    /** 八叉树 */
    private octree;
    /** 事件发射器 */
    private eventEmitter;
    /** 临时向量 */
    private tempVector;
    /** 临时矩阵 */
    private tempMatrix;
    /** 临时包围球 */
    private tempSphere;
    /** 临时包围盒 */
    private tempBox;
    /**
     * 创建视锥体剔除系统
     * @param options 视锥体剔除系统配置
     */
    constructor(options?: FrustumCullingSystemOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 设置活跃相机
     * @param camera 相机
     */
    setActiveCamera(camera: Camera): void;
    /**
     * 获取活跃相机
     * @returns 活跃相机
     */
    getActiveCamera(): Camera | null;
    /**
     * 设置活跃场景
     * @param scene 场景
     */
    setActiveScene(scene: Scene): void;
    /**
     * 获取活跃场景
     * @returns 活跃场景
     */
    getActiveScene(): Scene | null;
    /**
     * 注册可剔除组件
     * @param entity 实体
     * @param component 可剔除组件
     */
    registerCullableComponent(entity: Entity, component: CullableComponent): void;
    /**
     * 注销可剔除组件
     * @param entity 实体
     */
    unregisterCullableComponent(entity: Entity): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新视锥体
     */
    private updateFrustum;
    /**
     * 更新剔除
     */
    private updateCulling;
    /**
     * 使用八叉树进行剔除
     * @param cameraPosition 相机位置
     */
    private updateOctreeCulling;
    /**
     * 使用暴力方法进行剔除
     * @param cameraPosition 相机位置
     */
    private updateBruteForceCulling;
    /**
     * 剔除实体
     * @param entity 实体
     */
    private cullEntity;
    /**
     * 恢复实体
     * @param entity 实体
     */
    private restoreEntity;
    /**
     * 重建八叉树
     */
    private rebuildOctree;
    /**
     * 手动更新剔除
     */
    manualUpdate(): void;
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 设置是否自动更新
     * @param autoUpdate 是否自动更新
     */
    setAutoUpdate(autoUpdate: boolean): void;
    /**
     * 获取是否自动更新
     * @returns 是否自动更新
     */
    isAutoUpdate(): boolean;
    /**
     * 设置更新频率
     * @param frequency 更新频率
     */
    setUpdateFrequency(frequency: number): void;
    /**
     * 获取更新频率
     * @returns 更新频率
     */
    getUpdateFrequency(): number;
    /**
     * 设置是否使用八叉树
     * @param useOctree 是否使用八叉树
     */
    setUseOctree(useOctree: boolean): void;
    /**
     * 获取是否使用八叉树
     * @returns 是否使用八叉树
     */
    isUseOctree(): boolean;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: FrustumCullingSystemEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: FrustumCullingSystemEventType, listener: (...args: any[]) => void): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
