/**
 * UIEventComponent.ts
 *
 * UI事件组件，用于处理UI元素的事件
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector2, Vector3 } from 'three';
import type { Camera } from 'three';
import { IUIEvent, UIEventType } from '../interfaces/IUIElement';
import { UIComponent } from './UIComponent';
/**
 * UI事件数据
 */
export interface UIEventData {
    type: UIEventType;
    target: UIComponent;
    originalEvent?: any;
    position?: Vector2 | Vector3;
    delta?: Vector2;
    button?: number;
    key?: string;
    keyCode?: number;
    altKey?: boolean;
    ctrlKey?: boolean;
    shiftKey?: boolean;
    metaKey?: boolean;
    value?: any;
    stopPropagation: boolean;
    preventDefault: boolean;
    timestamp: number;
}
/**
 * UI事件
 * 实现IUIEvent接口
 */
export declare class UIEvent implements IUIEvent {
    type: UIEventType;
    target: UIComponent;
    data: UIEventData;
    /**
     * 构造函数
     * @param type 事件类型
     * @param target 目标UI元素
     * @param data 事件数据
     */
    constructor(type: UIEventType, target: UIComponent, data?: Partial<UIEventData>);
    /**
     * 阻止事件冒泡
     */
    stopPropagation(): void;
    /**
     * 阻止事件默认行为
     */
    preventDefault(): void;
}
/**
 * 事件监听器类型
 */
export type EventListener = (event: UIEvent) => void;
/**
 * UI事件组件
 * 用于处理实体的UI事件
 */
export declare class UIEventComponent extends Component {
    private eventListeners;
    private hoveredElement?;
    private focusedElement?;
    private activeElement?;
    private isDragging;
    private dragStartPosition?;
    private dragCurrentPosition?;
    private raycaster;
    /**
     * 构造函数
     * @param entity 关联的实体
     */
    constructor(entity: Entity);
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    addEventListener(type: UIEventType, listener: EventListener): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    removeEventListener(type: UIEventType, listener: EventListener): void;
    /**
     * 分发事件
     * @param event UI事件
     */
    dispatchEvent(event: UIEvent): void;
    /**
     * 创建并分发事件
     * @param type 事件类型
     * @param target 目标UI元素
     * @param data 事件数据
     */
    createAndDispatchEvent(type: UIEventType, target: UIComponent, data?: Partial<UIEventData>): void;
    /**
     * 处理鼠标移动
     * @param x 鼠标X坐标
     * @param y 鼠标Y坐标
     * @param uiElements UI元素列表
     * @param camera 相机（用于3D UI元素）
     */
    handleMouseMove(x: number, y: number, uiElements: UIComponent[], camera?: Camera): void;
    /**
     * 处理鼠标按下
     * @param x 鼠标X坐标
     * @param y 鼠标Y坐标
     * @param button 按下的按钮
     * @param uiElements UI元素列表
     * @param camera 相机（用于3D UI元素）
     */
    handleMouseDown(x: number, y: number, button: number, uiElements: UIComponent[], camera?: Camera): void;
    /**
     * 处理鼠标释放
     * @param x 鼠标X坐标
     * @param y 鼠标Y坐标
     * @param button 释放的按钮
     * @param _uiElements UI元素列表 - 未使用，保留以保持API一致性
     * @param _camera 相机（用于3D UI元素） - 未使用，保留以保持API一致性
     */
    handleMouseUp(x: number, y: number, button: number, _uiElements: UIComponent[], _camera?: Camera): void;
    /**
     * 处理键盘按下
     * @param key 按下的键
     * @param keyCode 键码
     * @param modifiers 修饰键状态
     */
    handleKeyDown(key: string, keyCode: number, modifiers: {
        altKey: boolean;
        ctrlKey: boolean;
        shiftKey: boolean;
        metaKey: boolean;
    }): void;
    /**
     * 处理键盘释放
     * @param key 释放的键
     * @param keyCode 键码
     * @param modifiers 修饰键状态
     */
    handleKeyUp(key: string, keyCode: number, modifiers: {
        altKey: boolean;
        ctrlKey: boolean;
        shiftKey: boolean;
        metaKey: boolean;
    }): void;
    /**
     * 在指定位置查找UI元素
     * @param x X坐标
     * @param y Y坐标
     * @param uiElements UI元素列表
     * @param camera 相机（用于3D UI元素）
     * @returns 找到的UI元素，如果没有找到则返回undefined
     */
    private findElementAtPosition;
}
