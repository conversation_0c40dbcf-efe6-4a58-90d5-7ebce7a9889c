/**
 * 物理驱动的角色交互系统
 * 管理角色与物理对象之间的交互，包括物理约束和反馈
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { PhysicsSystem } from '../PhysicsSystem';
import { PhysicsInteractionComponent } from './PhysicsInteractionComponent';
import { PhysicsInteractionConstraint } from './PhysicsInteractionConstraint';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 物理角色交互系统配置
 */
export interface PhysicsCharacterInteractionSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 最大交互距离 */
  maxInteractionDistance?: number;
  /** 最大交互力 */
  maxInteractionForce?: number;
  /** 交互力衰减系数 */
  interactionForceDamping?: number;
  /** 是否启用力反馈 */
  enableForceFeedback?: boolean;
  /** 是否启用交互动画 */
  enableInteractionAnimation?: boolean;
}

/**
 * 物理角色交互系统
 * 管理角色与物理对象之间的交互，包括物理约束和反馈
 */
export class PhysicsCharacterInteractionSystem extends System {
  /** 系统类型 */
  public static readonly type: string = 'PhysicsCharacterInteractionSystem';

  /** 物理系统 */
  private physicsSystem: PhysicsSystem | null = null;

  /** 交互组件映射 */
  private interactionComponents: Map<string, PhysicsInteractionComponent> = new Map();

  /** 交互约束映射 */
  private interactionConstraints: Map<string, PhysicsInteractionConstraint> = new Map();

  /** 当前活动交互 */
  private activeInteractions: Map<string, Entity> = new Map();

  /** 系统配置 */
  private config: PhysicsCharacterInteractionSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 调试渲染器 */
  private debugRenderer: THREE.Group | null = null;

  /**
   * 创建物理角色交互系统
   * @param config 系统配置
   */
  constructor(config: PhysicsCharacterInteractionSystemConfig = {}) {
    super(2); // 优先级2，在物理系统之后更新

    // 设置默认配置
    this.config = {
      debug: config.debug !== undefined ? config.debug : false,
      maxInteractionDistance: config.maxInteractionDistance || 2.0,
      maxInteractionForce: config.maxInteractionForce || 1000.0,
      interactionForceDamping: config.interactionForceDamping || 0.5,
      enableForceFeedback: config.enableForceFeedback !== undefined ? config.enableForceFeedback : true,
      enableInteractionAnimation: config.enableInteractionAnimation !== undefined ? config.enableInteractionAnimation : true
    };

    // 创建调试渲染器
    if (this.config.debug) {
      this.debugRenderer = new THREE.Group();
      this.debugRenderer.name = 'PhysicsCharacterInteractionDebug';
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.engine) {
      // 获取物理系统
      this.physicsSystem = this.engine.getSystem<PhysicsSystem>(PhysicsSystem.type);
      if (!this.physicsSystem) {
        Debug.warn('PhysicsCharacterInteractionSystem', '物理系统未找到，物理角色交互系统将不会正常工作');
        return;
      }

      // 获取世界中的所有实体
      const world = this.engine.getWorld();
      if (world) {
        // 查找具有交互组件的实体
        const entities = world.getAllEntities();
        for (const entity of entities) {
          this.setupEntityInteraction(entity);
        }

        // 监听实体创建事件
        world.on('entityCreated', this.handleEntityCreated.bind(this));

        // 监听实体移除事件
        world.on('entityRemoved', this.handleEntityRemoved.bind(this));
      }

      // 添加调试渲染器到场景
      if (this.config.debug && this.debugRenderer) {
        const world = this.engine.getWorld();
        const activeScene = world?.getActiveScene();
        if (activeScene) {
          const threeScene = activeScene.getThreeScene();
          threeScene.add(this.debugRenderer);
        }
      }
    }
  }

  /**
   * 处理实体创建事件
   * @param entity 创建的实体
   */
  private handleEntityCreated(entity: Entity): void {
    this.setupEntityInteraction(entity);
  }

  /**
   * 处理实体移除事件
   * @param entity 移除的实体
   */
  private handleEntityRemoved(entity: Entity): void {
    this.removeEntityInteraction(entity);
  }

  /**
   * 设置实体交互
   * @param entity 实体
   */
  private setupEntityInteraction(entity: Entity): void {
    // 检查实体是否有交互组件
    const interactionComponent = entity.getComponent(PhysicsInteractionComponent.type) as unknown as PhysicsInteractionComponent | null;
    if (interactionComponent) {
      // 初始化交互组件
      interactionComponent.initialize();

      // 添加到映射
      this.interactionComponents.set(entity.id, interactionComponent);
    }
  }

  /**
   * 移除实体交互
   * @param entity 实体
   */
  private removeEntityInteraction(entity: Entity): void {
    // 移除交互组件
    const interactionComponent = this.interactionComponents.get(entity.id);
    if (interactionComponent) {
      // 终止所有活动交互
      this.terminateAllInteractions(entity);

      // 销毁交互组件
      interactionComponent.destroy();

      // 从映射中移除
      this.interactionComponents.delete(entity.id);
    }

    // 移除作为交互目标的实体
    for (const [characterId, targetEntity] of this.activeInteractions.entries()) {
      if (targetEntity.id === entity.id) {
        // 终止交互
        this.terminateInteraction(characterId, entity.id);
      }
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有交互组件
    for (const component of this.interactionComponents.values()) {
      component.update(deltaTime);
    }

    // 更新所有交互约束
    for (const constraint of this.interactionConstraints.values()) {
      constraint.update(deltaTime);
    }

    // 更新调试渲染器
    if (this.config.debug && this.debugRenderer) {
      this.updateDebugRenderer();
    }
  }

  /**
   * 更新调试渲染器
   */
  private updateDebugRenderer(): void {
    if (!this.debugRenderer) return;

    // 清除旧的调试对象
    while (this.debugRenderer.children.length > 0) {
      this.debugRenderer.remove(this.debugRenderer.children[0]);
    }

    // 为每个活动交互创建调试可视化
    for (const [characterId, targetEntity] of this.activeInteractions.entries()) {
      const characterEntity = this.engine?.getWorld()?.getEntity(characterId);
      if (!characterEntity) continue;

      const characterTransform = characterEntity.getTransform();
      const targetTransform = targetEntity.getTransform();
      if (!characterTransform || !targetTransform) continue;

      // 创建线条表示交互
      const characterPosition = characterTransform.getWorldPosition();
      const targetPosition = targetTransform.getWorldPosition();

      const geometry = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(characterPosition.x, characterPosition.y, characterPosition.z),
        new THREE.Vector3(targetPosition.x, targetPosition.y, targetPosition.z)
      ]);
      const material = new THREE.LineBasicMaterial({ color: 0x00ff00 });
      const line = new THREE.Line(geometry, material);
      this.debugRenderer.add(line);

      // 创建球体表示交互点
      const sphereGeometry = new THREE.SphereGeometry(0.1, 8, 8);
      const sphereMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
      sphere.position.copy(targetPosition);
      this.debugRenderer.add(sphere);
    }
  }

  /**
   * 开始交互
   * @param characterEntity 角色实体
   * @param targetEntity 目标实体
   * @param interactionType 交互类型
   * @returns 是否成功开始交互
   */
  public startInteraction(characterEntity: Entity, targetEntity: Entity, interactionType: string): boolean {
    // 检查角色是否有交互组件
    const interactionComponent = this.interactionComponents.get(characterEntity.id);
    if (!interactionComponent) {
      Debug.warn('PhysicsCharacterInteractionSystem', `角色实体 ${characterEntity.id} 没有交互组件`);
      return false;
    }

    // 检查角色是否已经在与目标交互
    if (this.activeInteractions.has(characterEntity.id)) {
      const currentTarget = this.activeInteractions.get(characterEntity.id);
      if (currentTarget && currentTarget.id === targetEntity.id) {
        // 已经在交互中
        return true;
      }

      // 终止当前交互
      this.terminateInteraction(characterEntity.id, currentTarget!.id);
    }

    // 创建交互约束
    const constraintKey = `${characterEntity.id}_${targetEntity.id}`;
    const constraint = new PhysicsInteractionConstraint(
      characterEntity,
      targetEntity,
      interactionType,
      this.physicsSystem!
    );

    // 初始化约束
    const success = constraint.initialize();
    if (!success) {
      Debug.warn('PhysicsCharacterInteractionSystem', `无法创建交互约束: ${constraintKey}`);
      return false;
    }

    // 添加约束到映射
    this.interactionConstraints.set(constraintKey, constraint);

    // 更新活动交互
    this.activeInteractions.set(characterEntity.id, targetEntity);

    // 触发交互开始事件
    this.eventEmitter.emit('interactionStart', {
      character: characterEntity,
      target: targetEntity,
      type: interactionType
    });

    return true;
  }

  /**
   * 终止交互
   * @param characterId 角色实体ID
   * @param targetId 目标实体ID
   * @returns 是否成功终止交互
   */
  public terminateInteraction(characterId: string, targetId: string): boolean {
    // 检查是否存在交互
    const constraintKey = `${characterId}_${targetId}`;
    const constraint = this.interactionConstraints.get(constraintKey);
    if (!constraint) {
      return false;
    }

    // 销毁约束
    constraint.destroy();

    // 从映射中移除
    this.interactionConstraints.delete(constraintKey);

    // 更新活动交互
    this.activeInteractions.delete(characterId);

    // 获取实体
    const characterEntity = this.engine?.getWorld()?.getEntity(characterId);
    const targetEntity = this.engine?.getWorld()?.getEntity(targetId);

    // 触发交互结束事件
    if (characterEntity && targetEntity) {
      this.eventEmitter.emit('interactionEnd', {
        character: characterEntity,
        target: targetEntity
      });
    }

    return true;
  }

  /**
   * 终止所有交互
   * @param characterEntity 角色实体
   */
  public terminateAllInteractions(characterEntity: Entity): void {
    // 检查角色是否有活动交互
    if (this.activeInteractions.has(characterEntity.id)) {
      const targetEntity = this.activeInteractions.get(characterEntity.id)!;
      this.terminateInteraction(characterEntity.id, targetEntity.id);
    }
  }

  /**
   * 获取角色的当前交互目标
   * @param characterEntity 角色实体
   * @returns 交互目标实体，如果没有则返回null
   */
  public getInteractionTarget(characterEntity: Entity): Entity | null {
    const targetEntity = this.activeInteractions.get(characterEntity.id);
    return targetEntity || null;
  }

  /**
   * 检查角色是否正在交互
   * @param characterEntity 角色实体
   * @returns 是否正在交互
   */
  public isInteracting(characterEntity: Entity): boolean {
    return this.activeInteractions.has(characterEntity.id);
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 终止所有交互
    for (const [characterId, targetEntity] of this.activeInteractions.entries()) {
      this.terminateInteraction(characterId, targetEntity.id);
    }

    // 清除映射
    this.interactionComponents.clear();
    this.interactionConstraints.clear();
    this.activeInteractions.clear();

    // 移除调试渲染器
    if (this.debugRenderer) {
      const world = this.engine?.getWorld();
      const activeScene = world?.getActiveScene();
      if (activeScene) {
        const threeScene = activeScene.getThreeScene();
        threeScene.remove(this.debugRenderer);
      }
      this.debugRenderer = null;
    }

    // 调用父类销毁方法
    super.dispose();
  }
}
