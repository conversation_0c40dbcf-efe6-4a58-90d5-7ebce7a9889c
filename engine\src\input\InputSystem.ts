/**
 * 输入系统
 * 用于处理键盘、鼠标和触摸输入
 */
import { EventEmitter, EventCallback } from '../utils/EventEmitter';
import { System } from '../core/System';

/**
 * 键盘按键状态
 */
export enum KeyState {
  /** 按下 */
  DOWN = 'down',
  /** 按住 */
  PRESSED = 'pressed',
  /** 释放 */
  UP = 'up',
  /** 未按下 */
  NONE = 'none'
}

/**
 * 鼠标按钮状态
 */
export enum MouseButtonState {
  /** 按下 */
  DOWN = 'down',
  /** 按住 */
  PRESSED = 'pressed',
  /** 释放 */
  UP = 'up',
  /** 未按下 */
  NONE = 'none'
}

/**
 * 鼠标按钮
 */
export enum MouseButton {
  /** 左键 */
  LEFT = 0,
  /** 中键 */
  MIDDLE = 1,
  /** 右键 */
  RIGHT = 2
}

/**
 * 输入事件类型
 */
export enum InputEventType {
  /** 键盘按键按下 */
  KEY_DOWN = 'keyDown',
  /** 键盘按键释放 */
  KEY_UP = 'keyUp',
  /** 鼠标按钮按下 */
  MOUSE_DOWN = 'mouseDown',
  /** 鼠标按钮释放 */
  MOUSE_UP = 'mouseUp',
  /** 鼠标移动 */
  MOUSE_MOVE = 'mouseMove',
  /** 鼠标滚轮 */
  MOUSE_WHEEL = 'mouseWheel',
  /** 触摸开始 */
  TOUCH_START = 'touchStart',
  /** 触摸移动 */
  TOUCH_MOVE = 'touchMove',
  /** 触摸结束 */
  TOUCH_END = 'touchEnd',
  /** 触摸取消 */
  TOUCH_CANCEL = 'touchCancel',
  /** 游戏手柄连接 */
  GAMEPAD_CONNECTED = 'gamepadConnected',
  /** 游戏手柄断开 */
  GAMEPAD_DISCONNECTED = 'gamepadDisconnected',
  /** 游戏手柄按钮按下 */
  GAMEPAD_BUTTON_DOWN = 'gamepadButtonDown',
  /** 游戏手柄按钮释放 */
  GAMEPAD_BUTTON_UP = 'gamepadButtonUp',
  /** 游戏手柄摇杆移动 */
  GAMEPAD_AXIS_MOVE = 'gamepadAxisMove'
}

/**
 * 输入系统选项
 */
export interface InputSystemOptions {
  /** 目标元素 */
  element?: HTMLElement;
  /** 是否阻止默认行为 */
  preventDefault?: boolean;
  /** 是否阻止事件传播 */
  stopPropagation?: boolean;
  /** 是否启用键盘输入 */
  enableKeyboard?: boolean;
  /** 是否启用鼠标输入 */
  enableMouse?: boolean;
  /** 是否启用触摸输入 */
  enableTouch?: boolean;
  /** 是否启用游戏手柄输入 */
  enableGamepad?: boolean;
  /** 是否启用指针锁定 */
  enablePointerLock?: boolean;
}

/**
 * 输入系统
 */
export class InputSystem extends System {
  /** 系统名称 */
  public static readonly NAME: string = 'InputSystem';

  /** 目标元素 */
  private element: HTMLElement;

  /** 是否阻止默认行为 */
  private preventDefault: boolean;

  /** 是否阻止事件传播 */
  private stopPropagation: boolean;

  /** 是否启用键盘输入 */
  private enableKeyboard: boolean;

  /** 是否启用鼠标输入 */
  private enableMouse: boolean;

  /** 是否启用触摸输入 */
  private enableTouch: boolean;

  /** 是否启用游戏手柄输入 */
  private enableGamepad: boolean;

  /** 是否启用指针锁定 */
  private enablePointerLock: boolean;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 按键状态映射 */
  private keyStates: Map<string, KeyState> = new Map();

  /** 鼠标按钮状态映射 */
  private mouseButtonStates: Map<number, MouseButtonState> = new Map();

  /** 鼠标位置 */
  private mousePosition: { x: number; y: number } = { x: 0, y: 0 };

  /** 鼠标相对位置（相对于上一帧） */
  private mouseMovement: { x: number; y: number } = { x: 0, y: 0 };

  /** 鼠标滚轮增量 */
  private mouseWheelDelta: number = 0;

  /** 触摸点映射 */
  private touchPoints: Map<number, Touch> = new Map();

  /** 游戏手柄映射 */
  private gamepads: Map<number, Gamepad> = new Map();

  /** 游戏手柄按钮状态映射 */
  private gamepadButtonStates: Map<string, boolean> = new Map();

  /** 游戏手柄摇杆值映射 */
  private gamepadAxisValues: Map<string, number> = new Map();

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /** 是否支持当前环境 */
  private supported: boolean = true;

  /** 是否处于指针锁定状态 */
  private pointerLocked: boolean = false;

  /** 键盘事件处理器 */
  private keyboardEventHandlers: { [key: string]: (event: KeyboardEvent) => void } = {};

  /** 鼠标事件处理器 */
  private mouseEventHandlers: { [key: string]: (event: MouseEvent) => void } = {};

  /** 触摸事件处理器 */
  private touchEventHandlers: { [key: string]: (event: TouchEvent) => void } = {};

  /** 游戏手柄事件处理器 */
  private gamepadEventHandlers: { [key: string]: (event: GamepadEvent) => void } = {};

  /** 指针锁定事件处理器 */
  private pointerLockEventHandlers: { [key: string]: (event: Event) => void } = {};

  /**
   * 创建输入系统
   * @param options 输入系统选项
   */
  constructor(options: InputSystemOptions = {}) {
    // 将系统名称转换为数字优先级，这里使用0作为默认优先级
    super(0);

    this.element = options.element || document.body;
    this.preventDefault = options.preventDefault !== undefined ? options.preventDefault : true;
    this.stopPropagation = options.stopPropagation !== undefined ? options.stopPropagation : false;
    this.enableKeyboard = options.enableKeyboard !== undefined ? options.enableKeyboard : true;
    this.enableMouse = options.enableMouse !== undefined ? options.enableMouse : true;
    this.enableTouch = options.enableTouch !== undefined ? options.enableTouch : true;
    this.enableGamepad = options.enableGamepad !== undefined ? options.enableGamepad : true;
    this.enablePointerLock = options.enablePointerLock !== undefined ? options.enablePointerLock : false;

    // 初始化事件处理器
    this.initEventHandlers();
  }

  /**
   * 初始化事件处理器
   */
  private initEventHandlers(): void {
    // 键盘事件处理器
    this.keyboardEventHandlers = {
      keydown: this.handleKeyDown.bind(this),
      keyup: this.handleKeyUp.bind(this)
    };

    // 鼠标事件处理器
    this.mouseEventHandlers = {
      mousedown: this.handleMouseDown.bind(this),
      mouseup: this.handleMouseUp.bind(this),
      mousemove: this.handleMouseMove.bind(this),
      contextmenu: this.handleContextMenu.bind(this)
    };

    // 滚轮事件处理器（单独处理，因为类型不同）
    this.mouseEventHandlers['wheel'] = this.handleMouseWheel.bind(this);

    // 触摸事件处理器
    this.touchEventHandlers = {
      touchstart: this.handleTouchStart.bind(this),
      touchmove: this.handleTouchMove.bind(this),
      touchend: this.handleTouchEnd.bind(this),
      touchcancel: this.handleTouchCancel.bind(this)
    };

    // 游戏手柄事件处理器
    this.gamepadEventHandlers = {
      gamepadconnected: this.handleGamepadConnected.bind(this),
      gamepaddisconnected: this.handleGamepadDisconnected.bind(this)
    };

    // 指针锁定事件处理器
    this.pointerLockEventHandlers = {
      pointerlockchange: this.handlePointerLockChange.bind(this),
      pointerlockerror: this.handlePointerLockError.bind(this)
    };
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.initialized) return;

    // 添加事件监听器
    this.addEventListeners();

    this.initialized = true;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || !this.isEnabled() || !this.supported) return;

    // deltaTime 参数在此方法中未使用，但保留以符合 System 基类的接口

    // 更新游戏手柄状态
    if (this.enableGamepad) {
      this.updateGamepads();
    }

    // 处理按键状态更新
    for (const [key, state] of this.keyStates.entries()) {
      if (state === KeyState.DOWN) {
        // 将按下状态更新为按住状态
        this.keyStates.set(key, KeyState.PRESSED);
      } else if (state === KeyState.UP) {
        // 将释放状态更新为未按下状态
        this.keyStates.set(key, KeyState.NONE);
      }
    }

    // 处理鼠标按钮状态更新
    for (const [button, state] of this.mouseButtonStates.entries()) {
      if (state === MouseButtonState.DOWN) {
        // 将按下状态更新为按住状态
        this.mouseButtonStates.set(button, MouseButtonState.PRESSED);
      } else if (state === MouseButtonState.UP) {
        // 将释放状态更新为未按下状态
        this.mouseButtonStates.set(button, MouseButtonState.NONE);
      }
    }

    // 重置鼠标移动增量和滚轮增量
    this.mouseMovement.x = 0;
    this.mouseMovement.y = 0;
    this.mouseWheelDelta = 0;
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 添加键盘事件监听器
    if (this.enableKeyboard) {
      for (const [event, handler] of Object.entries(this.keyboardEventHandlers)) {
        document.addEventListener(event, handler as EventListener, { passive: !this.preventDefault });
      }
    }

    // 添加鼠标事件监听器
    if (this.enableMouse) {
      for (const [event, handler] of Object.entries(this.mouseEventHandlers)) {
        this.element.addEventListener(event, handler as EventListener, { passive: !this.preventDefault });
      }
    }

    // 添加触摸事件监听器
    if (this.enableTouch) {
      for (const [event, handler] of Object.entries(this.touchEventHandlers)) {
        this.element.addEventListener(event, handler as EventListener, { passive: !this.preventDefault });
      }
    }

    // 添加游戏手柄事件监听器
    if (this.enableGamepad) {
      for (const [event, handler] of Object.entries(this.gamepadEventHandlers)) {
        window.addEventListener(event, handler as EventListener);
      }
    }

    // 添加指针锁定事件监听器
    if (this.enablePointerLock) {
      for (const [event, handler] of Object.entries(this.pointerLockEventHandlers)) {
        document.addEventListener(event, handler as EventListener);
      }
    }
  }

  /**
   * 更新游戏手柄状态
   */
  private updateGamepads(): void {
    // 获取所有游戏手柄
    const gamepads = navigator.getGamepads ? navigator.getGamepads() : [];

    // 更新游戏手柄状态
    for (const gamepad of gamepads) {
      if (!gamepad) continue;

      // 更新游戏手柄映射
      this.gamepads.set(gamepad.index, gamepad);

      // 更新按钮状态
      for (let i = 0; i < gamepad.buttons.length; i++) {
        const button = gamepad.buttons[i];
        const buttonKey = `${gamepad.index}:${i}`;
        const pressed = button.pressed || button.value > 0.5;
        const wasPressed = this.gamepadButtonStates.get(buttonKey) || false;

        // 更新按钮状态
        this.gamepadButtonStates.set(buttonKey, pressed);

        // 触发按钮事件
        if (pressed && !wasPressed) {
          this.eventEmitter.emit(InputEventType.GAMEPAD_BUTTON_DOWN, {
            gamepad: gamepad.index,
            button: i,
            value: button.value
          });
        } else if (!pressed && wasPressed) {
          this.eventEmitter.emit(InputEventType.GAMEPAD_BUTTON_UP, {
            gamepad: gamepad.index,
            button: i,
            value: button.value
          });
        }
      }

      // 更新摇杆状态
      for (let i = 0; i < gamepad.axes.length; i++) {
        const axis = gamepad.axes[i];
        const axisKey = `${gamepad.index}:${i}`;
        const value = Math.abs(axis) < 0.1 ? 0 : axis; // 添加死区
        const oldValue = this.gamepadAxisValues.get(axisKey) || 0;

        // 如果值发生变化，则触发事件
        if (value !== oldValue) {
          this.gamepadAxisValues.set(axisKey, value);

          this.eventEmitter.emit(InputEventType.GAMEPAD_AXIS_MOVE, {
            gamepad: gamepad.index,
            axis: i,
            value: value
          });
        }
      }
    }
  }

  /**
   * 处理键盘按键按下事件
   * @param event 键盘事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    const key = event.code || event.key;

    // 如果按键未按下或已释放，则设置为按下状态
    if (this.keyStates.get(key) !== KeyState.PRESSED) {
      this.keyStates.set(key, KeyState.DOWN);

      // 触发按键按下事件
      this.eventEmitter.emit(InputEventType.KEY_DOWN, {
        key,
        code: event.code,
        altKey: event.altKey,
        ctrlKey: event.ctrlKey,
        shiftKey: event.shiftKey,
        metaKey: event.metaKey,
        repeat: event.repeat
      });
    }
  }

  /**
   * 处理键盘按键释放事件
   * @param event 键盘事件
   */
  private handleKeyUp(event: KeyboardEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    const key = event.code || event.key;

    // 设置按键为释放状态
    this.keyStates.set(key, KeyState.UP);

    // 触发按键释放事件
    this.eventEmitter.emit(InputEventType.KEY_UP, {
      key,
      code: event.code,
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理鼠标按钮按下事件
   * @param event 鼠标事件
   */
  private handleMouseDown(event: MouseEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新鼠标位置
    this.updateMousePosition(event);

    // 设置鼠标按钮为按下状态
    this.mouseButtonStates.set(event.button, MouseButtonState.DOWN);

    // 触发鼠标按钮按下事件
    this.eventEmitter.emit(InputEventType.MOUSE_DOWN, {
      button: event.button,
      x: this.mousePosition.x,
      y: this.mousePosition.y,
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });

    // 如果启用了指针锁定，则请求锁定
    if (this.enablePointerLock && !this.pointerLocked) {
      this.requestPointerLock();
    }
  }

  /**
   * 处理鼠标按钮释放事件
   * @param event 鼠标事件
   */
  private handleMouseUp(event: MouseEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新鼠标位置
    this.updateMousePosition(event);

    // 设置鼠标按钮为释放状态
    this.mouseButtonStates.set(event.button, MouseButtonState.UP);

    // 触发鼠标按钮释放事件
    this.eventEmitter.emit(InputEventType.MOUSE_UP, {
      button: event.button,
      x: this.mousePosition.x,
      y: this.mousePosition.y,
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理鼠标移动事件
   * @param event 鼠标事件
   */
  private handleMouseMove(event: MouseEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 计算鼠标移动增量
    const oldX = this.mousePosition.x;
    const oldY = this.mousePosition.y;

    // 更新鼠标位置
    this.updateMousePosition(event);

    // 计算移动增量
    if (this.pointerLocked) {
      // 如果处于指针锁定状态，则使用movementX和movementY
      this.mouseMovement.x += event.movementX || 0;
      this.mouseMovement.y += event.movementY || 0;
    } else {
      // 否则，计算位置差异
      this.mouseMovement.x += this.mousePosition.x - oldX;
      this.mouseMovement.y += this.mousePosition.y - oldY;
    }

    // 触发鼠标移动事件
    this.eventEmitter.emit(InputEventType.MOUSE_MOVE, {
      x: this.mousePosition.x,
      y: this.mousePosition.y,
      movementX: event.movementX || this.mousePosition.x - oldX,
      movementY: event.movementY || this.mousePosition.y - oldY,
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理鼠标滚轮事件
   * @param event 滚轮事件
   */
  private handleMouseWheel(event: WheelEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新鼠标位置
    this.updateMousePosition(event);

    // 计算滚轮增量
    const delta = event.deltaY || event.detail || 0;
    this.mouseWheelDelta += delta;

    // 触发鼠标滚轮事件
    this.eventEmitter.emit(InputEventType.MOUSE_WHEEL, {
      x: this.mousePosition.x,
      y: this.mousePosition.y,
      deltaX: event.deltaX || 0,
      deltaY: delta,
      deltaZ: event.deltaZ || 0,
      deltaMode: event.deltaMode,
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理上下文菜单事件（右键菜单）
   * @param event 鼠标事件
   */
  private handleContextMenu(event: MouseEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();
  }

  /**
   * 处理触摸开始事件
   * @param event 触摸事件
   */
  private handleTouchStart(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      this.touchPoints.set(touch.identifier, touch);
    }

    // 触发触摸开始事件
    this.eventEmitter.emit(InputEventType.TOUCH_START, {
      touches: Array.from(event.touches),
      changedTouches: Array.from(event.changedTouches),
      targetTouches: Array.from(event.targetTouches),
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理触摸移动事件
   * @param event 触摸事件
   */
  private handleTouchMove(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      this.touchPoints.set(touch.identifier, touch);
    }

    // 触发触摸移动事件
    this.eventEmitter.emit(InputEventType.TOUCH_MOVE, {
      touches: Array.from(event.touches),
      changedTouches: Array.from(event.changedTouches),
      targetTouches: Array.from(event.targetTouches),
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理触摸结束事件
   * @param event 触摸事件
   */
  private handleTouchEnd(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 移除触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      this.touchPoints.delete(touch.identifier);
    }

    // 触发触摸结束事件
    this.eventEmitter.emit(InputEventType.TOUCH_END, {
      touches: Array.from(event.touches),
      changedTouches: Array.from(event.changedTouches),
      targetTouches: Array.from(event.targetTouches),
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理触摸取消事件
   * @param event 触摸事件
   */
  private handleTouchCancel(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 移除触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      this.touchPoints.delete(touch.identifier);
    }

    // 触发触摸取消事件
    this.eventEmitter.emit(InputEventType.TOUCH_CANCEL, {
      touches: Array.from(event.touches),
      changedTouches: Array.from(event.changedTouches),
      targetTouches: Array.from(event.targetTouches),
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理游戏手柄连接事件
   * @param event 游戏手柄事件
   */
  private handleGamepadConnected(event: GamepadEvent): void {
    const gamepad = event.gamepad;

    // 添加游戏手柄
    this.gamepads.set(gamepad.index, gamepad);

    // 触发游戏手柄连接事件
    this.eventEmitter.emit(InputEventType.GAMEPAD_CONNECTED, {
      gamepad: gamepad.index,
      id: gamepad.id,
      buttons: gamepad.buttons.length,
      axes: gamepad.axes.length
    });
  }

  /**
   * 处理游戏手柄断开事件
   * @param event 游戏手柄事件
   */
  private handleGamepadDisconnected(event: GamepadEvent): void {
    const gamepad = event.gamepad;

    // 移除游戏手柄
    this.gamepads.delete(gamepad.index);

    // 清除该游戏手柄的按钮和摇杆状态
    for (const key of this.gamepadButtonStates.keys()) {
      if (key.startsWith(`${gamepad.index}:`)) {
        this.gamepadButtonStates.delete(key);
      }
    }

    for (const key of this.gamepadAxisValues.keys()) {
      if (key.startsWith(`${gamepad.index}:`)) {
        this.gamepadAxisValues.delete(key);
      }
    }

    // 触发游戏手柄断开事件
    this.eventEmitter.emit(InputEventType.GAMEPAD_DISCONNECTED, {
      gamepad: gamepad.index,
      id: gamepad.id
    });
  }

  /**
   * 处理指针锁定变化事件
   */
  private handlePointerLockChange(): void {
    this.pointerLocked = document.pointerLockElement === this.element;
  }

  /**
   * 处理指针锁定错误事件
   */
  private handlePointerLockError(): void {
    this.pointerLocked = false;
    console.error('指针锁定失败');
  }

  /**
   * 更新鼠标位置
   * @param event 鼠标事件
   */
  private updateMousePosition(event: MouseEvent): void {
    // 获取元素的边界矩形
    const rect = this.element.getBoundingClientRect();

    // 计算相对于元素的坐标
    this.mousePosition.x = event.clientX - rect.left;
    this.mousePosition.y = event.clientY - rect.top;
  }

  /**
   * 请求指针锁定
   */
  public requestPointerLock(): void {
    if (this.element.requestPointerLock) {
      this.element.requestPointerLock();
    }
  }

  /**
   * 退出指针锁定
   */
  public exitPointerLock(): void {
    if (document.exitPointerLock) {
      document.exitPointerLock();
    }
  }



  /**
   * 检查按键是否按下
   * @param key 按键
   * @returns 是否按下
   */
  public isKeyDown(key: string): boolean {
    return this.keyStates.get(key) === KeyState.DOWN;
  }

  /**
   * 检查按键是否按住
   * @param key 按键
   * @returns 是否按住
   */
  public isKeyPressed(key: string): boolean {
    const state = this.keyStates.get(key);
    return state === KeyState.DOWN || state === KeyState.PRESSED;
  }

  /**
   * 检查按键是否释放
   * @param key 按键
   * @returns 是否释放
   */
  public isKeyUp(key: string): boolean {
    return this.keyStates.get(key) === KeyState.UP;
  }

  /**
   * 检查鼠标按钮是否按下
   * @param button 鼠标按钮
   * @returns 是否按下
   */
  public isMouseButtonDown(button: MouseButton): boolean {
    return this.mouseButtonStates.get(button) === MouseButtonState.DOWN;
  }

  /**
   * 检查鼠标按钮是否按住
   * @param button 鼠标按钮
   * @returns 是否按住
   */
  public isMouseButtonPressed(button: MouseButton): boolean {
    const state = this.mouseButtonStates.get(button);
    return state === MouseButtonState.DOWN || state === MouseButtonState.PRESSED;
  }

  /**
   * 检查鼠标按钮是否释放
   * @param button 鼠标按钮
   * @returns 是否释放
   */
  public isMouseButtonUp(button: MouseButton): boolean {
    return this.mouseButtonStates.get(button) === MouseButtonState.UP;
  }

  /**
   * 获取鼠标位置
   * @returns 鼠标位置
   */
  public getMousePosition(): { x: number; y: number } {
    return { ...this.mousePosition };
  }

  /**
   * 获取鼠标移动增量
   * @returns 鼠标移动增量
   */
  public getMouseMovement(): { x: number; y: number } {
    return { ...this.mouseMovement };
  }

  /**
   * 获取鼠标滚轮增量
   * @returns 鼠标滚轮增量
   */
  public getMouseWheelDelta(): number {
    return this.mouseWheelDelta;
  }

  /**
   * 获取触摸点
   * @param id 触摸点ID
   * @returns 触摸点
   */
  public getTouchPoint(id: number): Touch | undefined {
    return this.touchPoints.get(id);
  }

  /**
   * 获取所有触摸点
   * @returns 触摸点数组
   */
  public getTouchPoints(): Touch[] {
    return Array.from(this.touchPoints.values());
  }

  /**
   * 获取游戏手柄
   * @param index 游戏手柄索引
   * @returns 游戏手柄
   */
  public getGamepad(index: number): Gamepad | undefined {
    return this.gamepads.get(index);
  }

  /**
   * 获取所有游戏手柄
   * @returns 游戏手柄数组
   */
  public getGamepads(): Gamepad[] {
    return Array.from(this.gamepads.values());
  }

  /**
   * 检查游戏手柄按钮是否按下
   * @param gamepadIndex 游戏手柄索引
   * @param buttonIndex 按钮索引
   * @returns 是否按下
   */
  public isGamepadButtonPressed(gamepadIndex: number, buttonIndex: number): boolean {
    return this.gamepadButtonStates.get(`${gamepadIndex}:${buttonIndex}`) || false;
  }

  /**
   * 获取游戏手柄摇杆值
   * @param gamepadIndex 游戏手柄索引
   * @param axisIndex 摇杆索引
   * @returns 摇杆值（-1到1）
   */
  public getGamepadAxisValue(gamepadIndex: number, axisIndex: number): number {
    return this.gamepadAxisValues.get(`${gamepadIndex}:${axisIndex}`) || 0;
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param callback 监听器函数
   * @returns 当前实例，用于链式调用
   */
  public on(event: string, callback: EventCallback): this {
    // 使用私有的事件发射器处理事件
    this.eventEmitter.on(event, callback);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param callback 监听器函数（可选）
   * @returns 当前实例，用于链式调用
   */
  public off(event: string, callback?: EventCallback): this {
    // 使用私有的事件发射器处理事件
    this.eventEmitter.off(event, callback);
    return this;
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 移除键盘事件监听器
    if (this.enableKeyboard) {
      for (const [event, handler] of Object.entries(this.keyboardEventHandlers)) {
        document.removeEventListener(event, handler as EventListener);
      }
    }

    // 移除鼠标事件监听器
    if (this.enableMouse) {
      for (const [event, handler] of Object.entries(this.mouseEventHandlers)) {
        this.element.removeEventListener(event, handler as EventListener);
      }

      // 单独移除滚轮事件监听器
      this.element.removeEventListener('wheel', this.handleMouseWheel.bind(this));
    }

    // 移除触摸事件监听器
    if (this.enableTouch) {
      for (const [event, handler] of Object.entries(this.touchEventHandlers)) {
        this.element.removeEventListener(event, handler as EventListener);
      }
    }

    // 移除游戏手柄事件监听器
    if (this.enableGamepad) {
      for (const [event, handler] of Object.entries(this.gamepadEventHandlers)) {
        window.removeEventListener(event, handler as EventListener);
      }
    }

    // 移除指针锁定事件监听器
    if (this.enablePointerLock) {
      for (const [event, handler] of Object.entries(this.pointerLockEventHandlers)) {
        document.removeEventListener(event, handler as EventListener);
      }
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    if (this.destroyed) return;

    // 移除事件监听器
    this.removeEventListeners();

    // 退出指针锁定
    if (this.pointerLocked) {
      this.exitPointerLock();
    }

    // 清除状态
    this.keyStates.clear();
    this.mouseButtonStates.clear();
    this.touchPoints.clear();
    this.gamepads.clear();
    this.gamepadButtonStates.clear();
    this.gamepadAxisValues.clear();

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();

    this.initialized = false;
    this.destroyed = true;

    super.dispose();
  }
}
