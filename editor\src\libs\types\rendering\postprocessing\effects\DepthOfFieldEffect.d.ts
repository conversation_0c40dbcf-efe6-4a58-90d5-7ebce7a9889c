/**
 * 景深效果
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 景深效果选项
 */
export interface DepthOfFieldEffectOptions extends PostProcessingEffectOptions {
    /** 焦距 */
    focus?: number;
    /** 光圈 */
    aperture?: number;
    /** 最大模糊 */
    maxBlur?: number;
    /** 是否使用景深贴图 */
    useDepthMap?: boolean;
}
/**
 * 景深效果
 */
export declare class DepthOfFieldEffect extends PostProcessingEffect {
    /** 焦距 */
    private focus;
    /** 光圈 */
    private aperture;
    /** 最大模糊 */
    private maxBlur;
    /** 是否使用景深贴图 */
    private useDepthMap;
    /** 景深通道 */
    private bokehPass;
    /** 场景 */
    private scene;
    /** 相机 */
    private camera;
    /**
     * 创建景深效果
     * @param options 景深效果选项
     */
    constructor(options?: DepthOfFieldEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 设置场景和相机
     * @param scene 场景
     * @param camera 相机
     */
    setSceneAndCamera(scene: THREE.Scene, camera: THREE.Camera): void;
    /**
     * 设置焦距
     * @param focus 焦距
     */
    setFocus(focus: number): void;
    /**
     * 获取焦距
     * @returns 焦距
     */
    getFocus(): number;
    /**
     * 设置光圈
     * @param aperture 光圈
     */
    setAperture(aperture: number): void;
    /**
     * 获取光圈
     * @returns 光圈
     */
    getAperture(): number;
    /**
     * 设置最大模糊
     * @param maxBlur 最大模糊
     */
    setMaxBlur(maxBlur: number): void;
    /**
     * 获取最大模糊
     * @returns 最大模糊
     */
    getMaxBlur(): number;
    /**
     * 设置是否使用景深贴图
     * @param useDepthMap 是否使用景深贴图
     */
    setUseDepthMap(useDepthMap: boolean): void;
    /**
     * 获取是否使用景深贴图
     * @returns 是否使用景深贴图
     */
    isUseDepthMap(): boolean;
    /**
     * 设置景深贴图
     * @param depthMap 景深贴图
     */
    setDepthMap(depthMap: THREE.Texture): void;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
}
