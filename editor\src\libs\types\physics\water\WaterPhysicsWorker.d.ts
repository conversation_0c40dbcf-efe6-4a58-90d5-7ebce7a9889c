/**
 * 水体物理计算工作线程
 * 用于在独立线程中进行水体物理计算
 */
/**
 * 消息类型
 */
export declare enum WaterPhysicsWorkerMessageType {
    /** 初始化 */
    INIT = "init",
    /** 更新水波 */
    UPDATE_WAVES = "update_waves",
    /** 更新水流 */
    UPDATE_FLOW = "update_flow",
    /** 更新浮力和阻力 */
    UPDATE_BUOYANCY_AND_DRAG = "update_buoyancy_and_drag",
    /** 更新法线图 */
    UPDATE_NORMAL_MAP = "update_normal_map",
    /** 完成 */
    COMPLETE = "complete",
    /** 错误 */
    ERROR = "error"
}
/**
 * 水波更新参数
 */
export interface WaterWavesUpdateParams {
    /** 高度图 */
    heightMap: Float32Array;
    /** 分辨率 */
    resolution: number;
    /** 波动参数 */
    waveParams: {
        /** 振幅 */
        amplitude: number;
        /** 频率 */
        frequency: number;
        /** 速度 */
        speed: number;
        /** 方向 */
        direction: {
            x: number;
            z: number;
        };
    };
    /** 时间 */
    time: number;
    /** 时间增量 */
    deltaTime: number;
}
/**
 * 水流更新参数
 */
export interface WaterFlowUpdateParams {
    /** 高度图 */
    heightMap: Float32Array;
    /** 速度图 */
    velocityMap: Float32Array;
    /** 分辨率 */
    resolution: number;
    /** 流向 */
    flowDirection: {
        x: number;
        y: number;
        z: number;
    };
    /** 流速 */
    flowSpeed: number;
    /** 时间增量 */
    deltaTime: number;
}
/**
 * 法线图更新参数
 */
export interface NormalMapUpdateParams {
    /** 高度图 */
    heightMap: Float32Array;
    /** 法线图 */
    normalMap: Float32Array;
    /** 分辨率 */
    resolution: number;
}
/**
 * 创建水体物理工作线程
 * @returns 工作线程
 */
export declare function createWaterPhysicsWorker(): Worker;
