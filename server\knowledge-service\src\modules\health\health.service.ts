import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { KnowledgeBase } from '../../entities/knowledge-base.entity';

@Injectable()
export class HealthService {
  constructor(
    @InjectRepository(KnowledgeBase)
    private readonly knowledgeBaseRepository: Repository<KnowledgeBase>,
  ) {}

  async checkHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'knowledge-service',
    };
  }

  async checkDetailedHealth() {
    const checks = [];

    // 检查数据库连接
    try {
      await this.knowledgeBaseRepository.count();
      checks.push({
        name: 'database',
        status: 'ok',
        message: '数据库连接正常',
      });
    } catch (error) {
      checks.push({
        name: 'database',
        status: 'error',
        message: '数据库连接失败',
        error: error.message,
      });
    }

    const overallStatus = checks.every(check => check.status === 'ok') ? 'ok' : 'error';

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      service: 'knowledge-service',
      checks,
    };
  }
}
