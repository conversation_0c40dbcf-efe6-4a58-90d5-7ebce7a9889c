/**
 * 本地认证策略
 */
import { Strategy } from 'passport-local';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthService } from '../auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly authService: AuthService) {
    super({
      usernameField: 'usernameOrEmail',
      passwordField: 'password',
    });
  }

  async validate(usernameOrEmail: string, password: string) {
    try {
      const user = await this.authService.validateUser(usernameOrEmail, password);
      return user;
    } catch (error) {
      throw new UnauthorizedException('用户名/邮箱或密码错误');
    }
  }
}
