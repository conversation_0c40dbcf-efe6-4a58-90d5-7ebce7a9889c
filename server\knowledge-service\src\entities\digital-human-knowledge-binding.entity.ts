import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
} from 'typeorm';
import { DigitalHuman } from './digital-human.entity';
import { KnowledgeBase } from './knowledge-base.entity';

@Entity('digital_human_knowledge_bindings')
@Unique(['digitalHumanId', 'knowledgeBaseId'])
export class DigitalHumanKnowledgeBinding {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  digitalHumanId: string;

  @Column({ type: 'uuid' })
  knowledgeBaseId: string;

  @Column({ type: 'varchar', length: 50, default: 'primary' })
  bindingType: string;

  @Column({ type: 'integer', default: 1 })
  priority: number;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'json', nullable: true })
  bindingConfig: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @ManyToOne(() => DigitalHuman, (digitalHuman) => digitalHuman.knowledgeBindings)
  @JoinColumn({ name: 'digitalHumanId' })
  digitalHuman: DigitalHuman;

  @ManyToOne(() => KnowledgeBase, (knowledgeBase) => knowledgeBase.digitalHumanBindings)
  @JoinColumn({ name: 'knowledgeBaseId' })
  knowledgeBase: KnowledgeBase;
}
