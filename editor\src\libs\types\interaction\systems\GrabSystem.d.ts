/**
 * 抓取系统
 * 用于处理实体之间的抓取交互
 */
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { Vector3 } from 'three';
import { GrabbableComponent } from '../components/GrabbableComponent';
import { GrabberComponent } from '../components/GrabberComponent';
import { Hand } from '../components/PhysicsGrabComponent';
export declare enum GrabType {
    DIRECT = "direct",
    DISTANCE = "distance",
    SPRING = "spring"
}
interface GrabbedComponent {
    grabber: Entity;
    hand: Hand;
    offset: Vector3;
}
/**
 * 抓取系统配置
 */
export interface GrabSystemConfig {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 是否启用物理抓取 */
    enablePhysicsGrab?: boolean;
    /** 是否启用网络同步 */
    enableNetworkSync?: boolean;
    /** 是否启用手势抓取 */
    enableGestureGrab?: boolean;
    /** 默认抓取类型 */
    defaultGrabType?: GrabType;
}
/**
 * 抓取系统
 */
export declare class GrabSystem extends System {
    /** 系统名称 */
    static readonly NAME: string;
    /** 输入系统引用 */
    private inputSystem?;
    /** 物理系统引用 */
    private physicsSystem?;
    /** 可抓取组件列表 */
    private grabbableComponents;
    /** 抓取者组件列表 */
    private grabberComponents;
    /** 被抓取组件列表 */
    private grabbedComponents;
    /** 配置 */
    private config;
    /** 临时向量 - 用于计算 */
    private tempVector;
    /** 临时四元数 - 用于计算 - 预留功能 */
    private tempQuaternion;
    /** 临时矩阵 - 用于计算 - 预留功能 */
    private tempMatrix;
    /**
     * 构造函数
     * @param world 世界实例
     * @param config 系统配置
     */
    constructor(world: World, config?: GrabSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 处理实体添加事件
     * @param entity 添加的实体
     */
    private onEntityAdded;
    /**
     * 处理实体移除事件
     * @param entity 移除的实体
     */
    private onEntityRemoved;
    /**
     * 设置实体组件
     * @param entity 实体
     */
    private setupEntityComponents;
    /**
     * 注册可抓取组件
     * @param entity 实体
     * @param component 可抓取组件
     */
    registerGrabbableComponent(entity: Entity, component: GrabbableComponent): void;
    /**
     * 注册抓取者组件
     * @param entity 实体
     * @param component 抓取者组件
     */
    registerGrabberComponent(entity: Entity, component: GrabberComponent): void;
    /**
     * 注册被抓取组件
     * @param entity 实体
     * @param component 被抓取组件
     */
    registerGrabbedComponent(entity: Entity, component: GrabbedComponent): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 处理输入
     */
    private handleInput;
    /**
     * 更新被抓取实体
     */
    private updateGrabbedEntities;
    /**
     * 更新直接抓取
     * @param entity 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    private updateDirectGrab;
    /**
     * 更新距离抓取
     * @param entity 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    private updateDistanceGrab;
    /**
     * 更新弹簧抓取
     * @param entity 被抓取实体
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    private updateSpringGrab;
    /**
     * 获取手的位置
     * @param grabber 抓取者
     * @param hand 手
     * @returns 手的位置
     */
    private getHandPosition;
    /**
     * 获取手的旋转
     * @param grabber 抓取者
     * @param hand 手
     * @returns 手的旋转
     */
    private getHandRotation;
}
export {};
