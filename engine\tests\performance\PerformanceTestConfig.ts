/**
 * 性能测试配置
 * 定义要运行的性能测试
 */
import { TestSuiteConfig } from './PerformanceTestRunner';

/**
 * 渲染性能测试套件
 */
export const renderingTestSuite: TestSuiteConfig = {
  name: '渲染性能测试套件',
  description: '测试渲染系统在不同场景复杂度和配置下的性能',
  renderingTests: [
    // 基础渲染测试 - 低复杂度
    {
      name: '基础渲染 - 低复杂度',
      description: '测试基础渲染性能 - 低复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'low',
      enablePostProcessing: false,
      enableOptimizations: false,
      enableShadows: false,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 16,
      },
    },
    
    // 基础渲染测试 - 中等复杂度
    {
      name: '基础渲染 - 中等复杂度',
      description: '测试基础渲染性能 - 中等复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'medium',
      enablePostProcessing: false,
      enableOptimizations: false,
      enableShadows: false,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 16,
      },
    },
    
    // 阴影渲染测试 - 中等复杂度
    {
      name: '阴影渲染 - 中等复杂度',
      description: '测试带阴影的渲染性能 - 中等复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'medium',
      enablePostProcessing: false,
      enableOptimizations: false,
      enableShadows: true,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 20,
      },
    },
    
    // 后处理渲染测试 - 中等复杂度
    {
      name: '后处理渲染 - 中等复杂度',
      description: '测试带后处理的渲染性能 - 中等复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'medium',
      enablePostProcessing: true,
      enableOptimizations: false,
      enableShadows: false,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 20,
      },
    },
    
    // 优化渲染测试 - 高复杂度
    {
      name: '优化渲染 - 高复杂度',
      description: '测试带优化的渲染性能 - 高复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'high',
      enablePostProcessing: false,
      enableOptimizations: true,
      enableShadows: false,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 20,
      },
    },
    
    // 完整渲染测试 - 高复杂度
    {
      name: '完整渲染 - 高复杂度',
      description: '测试完整渲染性能 - 高复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'high',
      enablePostProcessing: true,
      enableOptimizations: true,
      enableShadows: true,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 25,
      },
    },
    
    // 极限渲染测试 - 极高复杂度
    {
      name: '极限渲染 - 极高复杂度',
      description: '测试极限渲染性能 - 极高复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'extreme',
      enablePostProcessing: true,
      enableOptimizations: true,
      enableShadows: true,
      thresholds: {
        minFPS: 20,
        maxRenderTime: 50,
      },
    },
  ],
};

/**
 * 物理性能测试套件
 */
export const physicsTestSuite: TestSuiteConfig = {
  name: '物理性能测试套件',
  description: '测试物理系统在不同场景复杂度和配置下的性能',
  physicsTests: [
    // 基础物理测试 - 低复杂度
    {
      name: '基础物理 - 低复杂度',
      description: '测试基础物理性能 - 低复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'low',
      enableCCD: false,
      allowSleep: true,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 16,
      },
    },
    
    // 基础物理测试 - 中等复杂度
    {
      name: '基础物理 - 中等复杂度',
      description: '测试基础物理性能 - 中等复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'medium',
      enableCCD: false,
      allowSleep: true,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 16,
      },
    },
    
    // 连续碰撞检测测试 - 中等复杂度
    {
      name: '连续碰撞检测 - 中等复杂度',
      description: '测试带连续碰撞检测的物理性能 - 中等复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'medium',
      enableCCD: true,
      allowSleep: true,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 20,
      },
    },
    
    // 无休眠物理测试 - 中等复杂度
    {
      name: '无休眠物理 - 中等复杂度',
      description: '测试禁用休眠的物理性能 - 中等复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'medium',
      enableCCD: false,
      allowSleep: false,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 20,
      },
    },
    
    // 高迭代次数物理测试 - 中等复杂度
    {
      name: '高迭代次数物理 - 中等复杂度',
      description: '测试高迭代次数的物理性能 - 中等复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'medium',
      enableCCD: false,
      allowSleep: true,
      iterations: 20,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 20,
      },
    },
    
    // 完整物理测试 - 高复杂度
    {
      name: '完整物理 - 高复杂度',
      description: '测试完整物理性能 - 高复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'high',
      enableCCD: true,
      allowSleep: true,
      iterations: 10,
      thresholds: {
        minFPS: 30,
        maxRenderTime: 25,
      },
    },
    
    // 极限物理测试 - 极高复杂度
    {
      name: '极限物理 - 极高复杂度',
      description: '测试极限物理性能 - 极高复杂度场景',
      duration: 5000,
      warmupDuration: 1000,
      complexity: 'extreme',
      enableCCD: true,
      allowSleep: true,
      iterations: 10,
      thresholds: {
        minFPS: 20,
        maxRenderTime: 50,
      },
    },
  ],
};

/**
 * 完整性能测试套件
 */
export const fullTestSuite: TestSuiteConfig = {
  name: 'DL（Digital Learning）引擎完整性能测试套件',
  description: '测试DL（Digital Learning）引擎各个系统的性能',
  renderingTests: renderingTestSuite.renderingTests,
  physicsTests: physicsTestSuite.physicsTests,
};
