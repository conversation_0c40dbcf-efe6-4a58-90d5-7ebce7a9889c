/**
 * 节点性能数据
 */
export interface NodePerformanceData {
    /** 节点ID */
    nodeId: string;
    /** 节点类型 */
    nodeType?: string;
    /** 节点标签 */
    nodeLabel?: string;
    /** 执行次数 */
    executionCount: number;
    /** 总执行时间（毫秒） */
    totalExecutionTime: number;
    /** 最小执行时间（毫秒） */
    minExecutionTime: number;
    /** 最大执行时间（毫秒） */
    maxExecutionTime: number;
    /** 平均执行时间（毫秒） */
    averageExecutionTime: number;
    /** 最后执行时间（毫秒） */
    lastExecutionTime: number;
    /** 最后执行时间戳 */
    lastExecutionTimestamp: number;
    /** 执行时间历史 */
    executionTimeHistory: number[];
    /** 是否正在执行 */
    isExecuting: boolean;
    /** 执行开始时间戳 */
    executionStartTimestamp?: number;
}
/**
 * 图性能数据
 */
export interface GraphPerformanceData {
    /** 图ID */
    graphId: string;
    /** 图名称 */
    graphName?: string;
    /** 执行次数 */
    executionCount: number;
    /** 总执行时间（毫秒） */
    totalExecutionTime: number;
    /** 最小执行时间（毫秒） */
    minExecutionTime: number;
    /** 最大执行时间（毫秒） */
    maxExecutionTime: number;
    /** 平均执行时间（毫秒） */
    averageExecutionTime: number;
    /** 最后执行时间（毫秒） */
    lastExecutionTime: number;
    /** 最后执行时间戳 */
    lastExecutionTimestamp: number;
    /** 执行时间历史 */
    executionTimeHistory: number[];
    /** 是否正在执行 */
    isExecuting: boolean;
    /** 执行开始时间戳 */
    executionStartTimestamp?: number;
    /** 节点性能数据映射 */
    nodePerformance: Record<string, NodePerformanceData>;
}
/**
 * 性能分析器配置
 */
export interface PerformanceAnalyzerConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 最大历史记录长度 */
    maxHistoryLength?: number;
    /** 是否记录节点执行时间 */
    recordNodeExecutionTime?: boolean;
    /** 是否记录图执行时间 */
    recordGraphExecutionTime?: boolean;
    /** 是否自动清除旧数据 */
    autoClearOldData?: boolean;
    /** 自动清除数据的时间阈值（毫秒） */
    autoClearThreshold?: number;
}
/**
 * 性能分析器
 */
export declare class PerformanceAnalyzer {
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 事件发射器 */
    private eventEmitter;
    /** 节点性能数据映射 */
    private nodePerformance;
    /** 图性能数据映射 */
    private graphPerformance;
    /** 最后清除时间 */
    private lastClearTime;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: PerformanceAnalyzerConfig);
    /**
     * 启用性能分析
     */
    enable(): void;
    /**
     * 禁用性能分析
     */
    disable(): void;
    /**
     * 是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 开始节点执行
     * @param nodeId 节点ID
     * @param nodeType 节点类型
     * @param nodeLabel 节点标签
     */
    startNodeExecution(nodeId: string, nodeType?: string, nodeLabel?: string): void;
    /**
     * 结束节点执行
     * @param nodeId 节点ID
     */
    endNodeExecution(nodeId: string): void;
    /**
     * 开始图执行
     * @param graphId 图ID
     * @param graphName 图名称
     */
    startGraphExecution(graphId: string, graphName?: string): void;
    /**
     * 结束图执行
     * @param graphId 图ID
     */
    endGraphExecution(graphId: string): void;
    /**
     * 获取节点执行时间统计
     * @returns 节点执行时间统计
     */
    getNodeExecutionTimeStatistics(): Record<string, NodePerformanceData>;
    /**
     * 获取图执行时间统计
     * @returns 图执行时间统计
     */
    getGraphExecutionTimeStatistics(): Record<string, GraphPerformanceData>;
    /**
     * 获取节点性能数据
     * @param nodeId 节点ID
     * @returns 节点性能数据
     */
    getNodePerformanceData(nodeId: string): NodePerformanceData | null;
    /**
     * 获取图性能数据
     * @param graphId 图ID
     * @returns 图性能数据
     */
    getGraphPerformanceData(graphId: string): GraphPerformanceData | null;
    /**
     * 清除节点性能数据
     * @param nodeId 节点ID
     * @returns 是否成功
     */
    clearNodePerformanceData(nodeId: string): boolean;
    /**
     * 清除图性能数据
     * @param graphId 图ID
     * @returns 是否成功
     */
    clearGraphPerformanceData(graphId: string): boolean;
    /**
     * 清除所有性能数据
     */
    clearAllPerformanceData(): void;
    /**
     * 检查是否需要自动清除旧数据
     */
    private checkAutoClear;
    /**
     * 清除旧数据
     */
    private clearOldData;
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    on(event: string, listener: (...args: any[]) => void): void;
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    off(event: string, listener: (...args: any[]) => void): void;
    /**
     * 销毁
     */
    dispose(): void;
}
