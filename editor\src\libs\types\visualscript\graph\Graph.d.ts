/**
 * 视觉脚本图形
 * 表示一个完整的视觉脚本图形，包含节点、变量和自定义事件
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { CustomEvent } from '../events/CustomEvent';
import { Node } from '../nodes/Node';
import { Variable } from '../values/Variable';
import { GraphJSON } from './GraphJSON';
/**
 * 图形选项
 */
export interface GraphOptions {
    /** 图形ID */
    id: string;
    /** 图形名称 */
    name?: string;
    /** 图形描述 */
    description?: string;
}
/**
 * 图形类
 */
export declare class Graph extends EventEmitter {
    /** 图形ID */
    readonly id: string;
    /** 图形名称 */
    private name;
    /** 图形描述 */
    private description;
    /** 节点映射 */
    private nodes;
    /** 变量映射 */
    private variables;
    /** 自定义事件映射 */
    private customEvents;
    /** 创建时间 */
    private createdAt;
    /** 最后修改时间 */
    private updatedAt;
    /**
     * 创建图形
     * @param options 图形选项
     */
    constructor(options: GraphOptions);
    /**
     * 获取图形名称
     * @returns 图形名称
     */
    getName(): string;
    /**
     * 设置图形名称
     * @param name 图形名称
     */
    setName(name: string): void;
    /**
     * 获取图形描述
     * @returns 图形描述
     */
    getDescription(): string;
    /**
     * 设置图形描述
     * @param description 图形描述
     */
    setDescription(description: string): void;
    /**
     * 添加节点
     * @param node 节点
     * @returns 是否添加成功
     */
    addNode(node: Node): boolean;
    /**
     * 移除节点
     * @param id 节点ID
     * @returns 是否移除成功
     */
    removeNode(id: string): boolean;
    /**
     * 获取节点
     * @param id 节点ID
     * @returns 节点
     */
    getNode(id: string): Node | undefined;
    /**
     * 获取所有节点
     * @returns 节点列表
     */
    getNodes(): Node[];
    /**
     * 添加变量
     * @param variable 变量
     * @returns 是否添加成功
     */
    addVariable(variable: Variable): boolean;
    /**
     * 移除变量
     * @param id 变量ID
     * @returns 是否移除成功
     */
    removeVariable(id: string): boolean;
    /**
     * 获取变量
     * @param id 变量ID
     * @returns 变量
     */
    getVariable(id: string): Variable | undefined;
    /**
     * 获取所有变量
     * @returns 变量列表
     */
    getVariables(): Variable[];
    /**
     * 添加自定义事件
     * @param event 自定义事件
     * @returns 是否添加成功
     */
    addCustomEvent(event: CustomEvent): boolean;
    /**
     * 移除自定义事件
     * @param id 自定义事件ID
     * @returns 是否移除成功
     */
    removeCustomEvent(id: string): boolean;
    /**
     * 获取自定义事件
     * @param id 自定义事件ID
     * @returns 自定义事件
     */
    getCustomEvent(id: string): CustomEvent | undefined;
    /**
     * 获取所有自定义事件
     * @returns 自定义事件列表
     */
    getCustomEvents(): CustomEvent[];
    /**
     * 获取到输入的连接
     * @param nodeId 节点ID
     * @param inputName 输入插槽名称
     * @returns 连接信息
     */
    getConnectionToInput(nodeId: string, inputName: string): {
        outputNodeId: string;
        outputSocketName: string;
    } | null;
    /**
     * 清空图形
     */
    clear(): void;
    /**
     * 序列化为JSON
     * @returns 图形JSON数据
     */
    toJSON(): GraphJSON;
    /**
     * 从JSON创建图形
     * @param json 图形JSON数据
     * @returns 图形实例
     */
    static fromJSON(json: GraphJSON): Graph;
}
