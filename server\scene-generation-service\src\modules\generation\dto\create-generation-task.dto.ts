import { IsString, IsOptional, IsEnum, IsObject, IsUrl } from 'class-validator';
import { TaskType } from '../../../modules/tasks/entities/generation-task.entity';

export class CreateGenerationTaskDto {
  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsEnum(TaskType)
  type: TaskType;

  @IsString()
  @IsOptional()
  textInput?: string;

  @IsUrl()
  @IsOptional()
  voiceFileUrl?: string;

  @IsUrl()
  @IsOptional()
  imageFileUrl?: string;

  @IsString()
  @IsOptional()
  templateId?: string;

  @IsObject()
  @IsOptional()
  inputData?: any;

  @IsObject()
  @IsOptional()
  config?: any;
}
