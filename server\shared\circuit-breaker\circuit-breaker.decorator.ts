/**
 * 熔断器装饰器
 */
import { SetMetadata, applyDecorators } from '@nestjs/common';
import { CircuitBreakerOptions } from './circuit-breaker.interface';

/**
 * 熔断器元数据键
 */
export const CIRCUIT_BREAKER_METADATA = 'circuit_breaker_metadata';

/**
 * 熔断器装饰器元数据
 */
export interface CircuitBreakerMetadata {
  /**
   * 熔断器名称
   */
  name: string;
  
  /**
   * 熔断器配置
   */
  options?: Partial<CircuitBreakerOptions>;
}

/**
 * 熔断器装饰器
 * 用于在控制器或方法级别应用熔断器
 * @param name 熔断器名称
 * @param options 熔断器配置
 */
export function UseCircuitBreaker(
  name: string,
  options?: Partial<CircuitBreakerOptions>,
): MethodDecorator & ClassDecorator {
  return applyDecorators(
    SetMetadata(CIRCUIT_BREAKER_METADATA, { name, options }),
  );
}
