FROM node:22-alpine AS builder

WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制用户服务代码
COPY user-service/package*.json ./user-service/
WORKDIR /app/user-service
RUN npm install

# 复制用户服务源代码
COPY user-service/ ./

RUN npm run build

FROM node:22-alpine

WORKDIR /app

COPY --from=builder /app/user-service/package*.json ./
COPY --from=builder /app/user-service/dist ./dist

RUN npm install --only=production

EXPOSE 3001 4001

CMD ["node", "dist/main.js"]
