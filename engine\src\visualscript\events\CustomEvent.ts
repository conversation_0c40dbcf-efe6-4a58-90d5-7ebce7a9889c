/**
 * 视觉脚本自定义事件
 * 用于在视觉脚本中定义和触发自定义事件
 */
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 自定义事件选项
 */
export interface CustomEventOptions {
  /** 事件ID */
  id: string;
  /** 事件名称 */
  name: string;
  /** 事件参数类型 */
  parameterTypes?: string[];
  /** 事件描述 */
  description?: string;
}

/**
 * 视觉脚本自定义事件
 * 用于在视觉脚本中定义和触发自定义事件
 */
export class CustomEvent extends EventEmitter {
  /** 事件ID */
  public readonly id: string;
  
  /** 事件名称 */
  private _name: string;
  
  /** 事件参数类型 */
  private _parameterTypes: string[];
  
  /** 事件描述 */
  private _description: string;
  
  /**
   * 创建自定义事件
   * @param options 事件选项
   */
  constructor(options: CustomEventOptions) {
    super();
    
    this.id = options.id;
    this._name = options.name;
    this._parameterTypes = options.parameterTypes || [];
    this._description = options.description || '';
  }
  
  /**
   * 获取事件名称
   * @returns 事件名称
   */
  public get name(): string {
    return this._name;
  }
  
  /**
   * 设置事件名称
   * @param value 事件名称
   */
  public set name(value: string) {
    if (this._name !== value) {
      const oldValue = this._name;
      this._name = value;
      this.emit('nameChanged', value, oldValue);
    }
  }
  
  /**
   * 获取事件参数类型
   * @returns 事件参数类型
   */
  public get parameterTypes(): string[] {
    return [...this._parameterTypes];
  }
  
  /**
   * 设置事件参数类型
   * @param value 事件参数类型
   */
  public set parameterTypes(value: string[]) {
    const oldValue = [...this._parameterTypes];
    this._parameterTypes = [...value];
    this.emit('parameterTypesChanged', this._parameterTypes, oldValue);
  }
  
  /**
   * 获取事件描述
   * @returns 事件描述
   */
  public get description(): string {
    return this._description;
  }
  
  /**
   * 设置事件描述
   * @param value 事件描述
   */
  public set description(value: string) {
    if (this._description !== value) {
      const oldValue = this._description;
      this._description = value;
      this.emit('descriptionChanged', value, oldValue);
    }
  }
  
  /**
   * 添加参数类型
   * @param type 参数类型
   * @returns 是否添加成功
   */
  public addParameterType(type: string): boolean {
    if (this._parameterTypes.includes(type)) {
      return false;
    }
    
    const oldValue = [...this._parameterTypes];
    this._parameterTypes.push(type);
    this.emit('parameterTypesChanged', this._parameterTypes, oldValue);
    
    return true;
  }
  
  /**
   * 移除参数类型
   * @param type 参数类型
   * @returns 是否移除成功
   */
  public removeParameterType(type: string): boolean {
    const index = this._parameterTypes.indexOf(type);
    
    if (index === -1) {
      return false;
    }
    
    const oldValue = [...this._parameterTypes];
    this._parameterTypes.splice(index, 1);
    this.emit('parameterTypesChanged', this._parameterTypes, oldValue);
    
    return true;
  }
  
  /**
   * 触发事件
   * @param args 事件参数
   */
  public trigger(...args: any[]): void {
    // 检查参数类型
    if (args.length !== this._parameterTypes.length) {
      console.warn(`参数数量不匹配: 期望 ${this._parameterTypes.length} 个参数，实际 ${args.length} 个参数`);
    }
    
    // 触发事件
    this.emit('triggered', ...args);
  }
  
  /**
   * 克隆事件
   * @returns 克隆的事件
   */
  public clone(): CustomEvent {
    return new CustomEvent({
      id: this.id,
      name: this._name,
      parameterTypes: [...this._parameterTypes],
      description: this._description
    });
  }
  
  /**
   * 序列化事件
   * @returns 序列化数据
   */
  public serialize(): any {
    return {
      id: this.id,
      name: this._name,
      parameterTypes: [...this._parameterTypes],
      description: this._description
    };
  }
  
  /**
   * 反序列化事件
   * @param data 序列化数据
   */
  public deserialize(data: any): void {
    if (data.name !== undefined) {
      this._name = data.name;
    }
    
    if (data.parameterTypes !== undefined) {
      this._parameterTypes = [...data.parameterTypes];
    }
    
    if (data.description !== undefined) {
      this._description = data.description;
    }
  }
}
