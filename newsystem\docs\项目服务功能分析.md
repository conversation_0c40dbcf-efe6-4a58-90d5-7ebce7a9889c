# 项目服务功能详细分析

## 概述

项目服务（Project Service）是本系统的核心业务微服务，负责管理多媒体/游戏引擎项目的完整生命周期。该服务基于NestJS框架构建，采用微服务架构模式，提供项目创建、场景管理、成员协作、权限控制等核心功能。

## 服务架构

### 技术栈
- **框架**: NestJS 10.x
- **数据库**: MySQL 8.x
- **ORM**: TypeORM 0.3.x
- **认证**: JWT + Passport
- **API文档**: Swagger/OpenAPI
- **容器化**: Docker
- **微服务通信**: TCP传输

### 服务端口配置
- **微服务端口**: 3002 (TCP通信)
- **HTTP API端口**: 4002 (RESTful API)

## 核心功能模块

### 1. 项目管理模块 (ProjectsModule)

#### 1.1 功能概述
负责项目的创建、查询、更新、删除等基础CRUD操作，以及项目成员管理和权限控制。

#### 1.2 数据模型

**Project实体 (项目主表)**
```typescript
- id: string (UUID主键)
- name: string (项目名称)
- description: string (项目描述)
- thumbnailUrl: string (缩略图URL)
- visibility: ProjectVisibility (可见性: PUBLIC/PRIVATE)
- ownerId: string (项目所有者ID)
- isTemplate: boolean (是否为模板)
- isArchived: boolean (是否已归档)
- createdAt: Date (创建时间)
- updatedAt: Date (更新时间)
```

**ProjectMember实体 (项目成员表)**
```typescript
- id: string (UUID主键)
- userId: string (用户ID)
- role: ProjectMemberRole (角色: OWNER/ADMIN/EDITOR/VIEWER)
- projectId: string (关联项目ID)
- createdAt: Date (加入时间)
- updatedAt: Date (更新时间)
```

**ProjectSetting实体 (项目设置表)**
```typescript
- id: string (UUID主键)
- key: string (设置键)
- value: string (设置值)
- projectId: string (关联项目ID)
- createdAt: Date (创建时间)
- updatedAt: Date (更新时间)
```

#### 1.3 权限角色系统

**角色定义:**
- **OWNER**: 项目所有者，拥有所有权限
- **ADMIN**: 管理员，可管理项目和成员
- **EDITOR**: 编辑者，可编辑项目内容
- **VIEWER**: 查看者，只能查看项目

#### 1.4 主要功能

**ProjectsService (项目服务)**
- `create()` - 创建项目
- `findAll()` - 查询用户可访问的项目
- `findOne()` - 根据ID查询项目详情
- `update()` - 更新项目信息
- `remove()` - 删除项目
- `addMember()` - 添加项目成员
- `removeMember()` - 移除项目成员
- `updateMemberRole()` - 更新成员角色
- `checkPermission()` - 检查用户权限

**ProjectsController (项目控制器)**
- `POST /api/projects` - 创建项目
- `GET /api/projects` - 获取项目列表
- `GET /api/projects/:id` - 获取项目详情
- `PATCH /api/projects/:id` - 更新项目
- `DELETE /api/projects/:id` - 删除项目
- `POST /api/projects/:id/members` - 添加成员
- `DELETE /api/projects/:id/members/:userId` - 移除成员
- `PATCH /api/projects/:id/members/:userId` - 更新成员角色

### 2. 场景管理模块 (ScenesModule)

#### 2.1 功能概述
负责项目内场景的管理，包括场景创建、场景实体管理、场景层级结构等功能。

#### 2.2 数据模型

**Scene实体 (场景表)**
```typescript
- id: string (UUID主键)
- name: string (场景名称)
- description: string (场景描述)
- thumbnailUrl: string (缩略图URL)
- projectId: string (关联项目ID)
- isDefault: boolean (是否为默认场景)
- metadata: Record<string, any> (元数据)
- createdAt: Date (创建时间)
- updatedAt: Date (更新时间)
```

**SceneEntity实体 (场景实体表)**
```typescript
- id: string (UUID主键)
- name: string (实体名称)
- type: string (实体类型)
- transform: Transform (变换信息: 位置/旋转/缩放)
- components: Record<string, any> (组件数据)
- parentId: string (父实体ID)
- metadata: Record<string, any> (元数据)
- sceneId: string (关联场景ID)
- createdAt: Date (创建时间)
- updatedAt: Date (更新时间)
```

#### 2.3 主要功能

**ScenesService (场景服务)**
- `create()` - 创建场景
- `findAll()` - 查询项目的所有场景
- `findOne()` - 根据ID查询场景详情
- `update()` - 更新场景信息
- `remove()` - 删除场景
- `createEntity()` - 创建场景实体
- `updateEntity()` - 更新场景实体
- `removeEntity()` - 删除场景实体
- `getEntities()` - 获取场景实体列表

**ScenesController (场景控制器)**
- `POST /api/projects/:projectId/scenes` - 创建场景
- `GET /api/projects/:projectId/scenes` - 获取场景列表
- `GET /api/scenes/:id` - 获取场景详情
- `PATCH /api/scenes/:id` - 更新场景
- `DELETE /api/scenes/:id` - 删除场景
- `POST /api/scenes/:id/entities` - 创建场景实体
- `GET /api/scenes/:id/entities` - 获取场景实体
- `PATCH /api/entities/:id` - 更新场景实体
- `DELETE /api/entities/:id` - 删除场景实体

### 3. 权限控制系统

#### 3.1 权限检查机制
项目服务实现了细粒度的权限控制系统：

**权限检查流程:**
1. 验证用户身份（JWT认证）
2. 检查用户是否为项目成员
3. 验证用户角色是否满足操作要求
4. 执行相应的业务操作

**权限矩阵:**
| 操作 | OWNER | ADMIN | EDITOR | VIEWER |
|------|-------|-------|--------|--------|
| 查看项目 | ✓ | ✓ | ✓ | ✓ |
| 编辑项目 | ✓ | ✓ | ✗ | ✗ |
| 删除项目 | ✓ | ✗ | ✗ | ✗ |
| 管理成员 | ✓ | ✓ | ✗ | ✗ |
| 创建场景 | ✓ | ✓ | ✓ | ✗ |
| 编辑场景 | ✓ | ✓ | ✓ | ✗ |
| 删除场景 | ✓ | ✓ | ✗ | ✗ |

#### 3.2 权限守卫
- `JwtAuthGuard` - JWT令牌验证
- 自定义权限检查逻辑集成在服务层

### 4. 微服务集成

#### 4.1 服务间通信
项目服务与其他微服务的集成：

**与用户服务集成:**
- 验证用户存在性
- 获取用户信息

**与协作服务集成:**
- 提供项目权限检查接口
- 支持实时协作功能

**与资产服务集成:**
- 项目资产权限验证
- 资产归属项目管理

#### 4.2 微服务接口
提供以下微服务命令：
- `createProject` - 创建项目
- `findAllProjects` - 查询项目列表
- `getProject` - 获取项目信息
- `getScene` - 获取场景信息
- `checkProjectPermission` - 检查项目权限
- `getProjectMembers` - 获取项目成员

### 5. 数据库设计

#### 5.1 数据库配置
- **类型**: MySQL
- **数据库名**: ir_engine_projects
- **连接池**: 自动管理
- **同步模式**: 开发环境自动同步

#### 5.2 表关系设计
```
Project (1) -> ProjectMember (N) - 一对多关系
Project (1) -> ProjectSetting (N) - 一对多关系
Project (1) -> Scene (N) - 一对多关系
Scene (1) -> SceneEntity (N) - 一对多关系
SceneEntity (1) -> SceneEntity (N) - 自关联（父子关系）
```

#### 5.3 索引优化
- projectId外键索引
- userId外键索引
- 复合索引(projectId, userId)用于成员查询
- parentId索引用于层级查询

## 业务流程

### 1. 项目创建流程
```
用户发起创建请求 → 验证用户身份 → 创建项目记录 → 
添加创建者为OWNER → 创建默认场景 → 返回项目信息
```

### 2. 成员协作流程
```
项目所有者/管理员邀请成员 → 验证被邀请用户存在 → 
检查用户是否已是成员 → 创建成员记录 → 分配角色权限
```

### 3. 场景编辑流程
```
用户请求编辑场景 → 验证项目权限 → 检查编辑权限 → 
更新场景数据 → 触发协作同步 → 返回更新结果
```

## 安全机制

### 1. 认证与授权
- JWT令牌验证
- 基于角色的访问控制(RBAC)
- 细粒度权限检查

### 2. 数据安全
- 输入验证和清理
- SQL注入防护
- XSS攻击防护

### 3. 业务安全
- 项目可见性控制
- 成员权限隔离
- 操作审计日志

## 性能优化

### 1. 数据库优化
- 合理的索引设计
- 查询优化
- 分页查询支持

### 2. 缓存策略
- 项目信息缓存
- 权限检查结果缓存
- 场景数据缓存

### 3. 查询优化
- 关联查询优化
- 批量操作支持
- 懒加载策略

## 扩展性设计

### 1. 模块化架构
- 清晰的模块边界
- 可插拔的功能组件
- 标准化的接口设计

### 2. 数据扩展
- 灵活的元数据系统
- 可扩展的设置机制
- 版本化的数据结构

### 3. 功能扩展
- 插件化的组件系统
- 可配置的工作流
- 自定义字段支持

## 监控与日志

### 1. 健康检查
- `/health` 端点监控
- 数据库连接状态
- 微服务通信状态

### 2. 业务监控
- 项目创建统计
- 用户活跃度监控
- 协作操作频率

### 3. 日志记录
- 操作审计日志
- 错误日志记录
- 性能指标收集

## 前端集成

### 1. 编辑器集成
项目服务与前端编辑器的深度集成：

**AssetService集成:**
- 项目资产管理
- 文件夹结构管理
- 资产上传和组织

**权限服务集成:**
- 实时权限检查
- 角色权限管理
- 操作权限验证

### 2. 实时协作
- WebSocket连接管理
- 实时数据同步
- 冲突解决机制

### 3. 状态管理
- Redux状态同步
- 本地缓存管理
- 离线数据处理

## 部署配置

### 1. 环境变量
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=ir_engine_projects

# JWT配置
JWT_SECRET=your-secret-key

# 服务配置
PROJECT_SERVICE_HOST=localhost
PROJECT_SERVICE_PORT=3002
PROJECT_SERVICE_HTTP_PORT=4002

# 微服务依赖
USER_SERVICE_HOST=localhost
USER_SERVICE_PORT=3001

# 运行环境
NODE_ENV=development
```

### 2. Docker配置
- 基于Node.js 18 Alpine镜像
- 多阶段构建优化
- 暴露3002和4002端口
- 健康检查配置

### 3. 数据库迁移
- TypeORM自动同步
- 数据库版本管理
- 生产环境迁移脚本

## 测试策略

### 1. 单元测试
- Service层业务逻辑测试
- Controller层接口测试
- 权限检查逻辑测试

### 2. 集成测试
- 数据库集成测试
- 微服务通信测试
- API端到端测试

### 3. 性能测试
- 并发访问测试
- 大数据量测试
- 响应时间测试

## 错误处理

### 1. 常见错误类型
- `BadRequestException` - 请求参数错误
- `NotFoundException` - 项目/场景不存在
- `ForbiddenException` - 权限不足
- `ConflictException` - 数据冲突

### 2. 错误响应格式
```json
{
  "statusCode": 403,
  "message": "您没有权限访问此项目",
  "error": "Forbidden"
}
```

### 3. 错误恢复机制
- 自动重试机制
- 降级处理策略
- 数据一致性保证

## 最佳实践

### 1. 代码规范
- TypeScript严格模式
- ESLint代码检查
- Prettier代码格式化

### 2. 数据库设计
- 规范化设计原则
- 合理的外键约束
- 软删除策略

### 3. API设计
- RESTful设计原则
- 统一的响应格式
- 完整的API文档

## 未来发展

### 1. 功能增强
- 项目模板系统
- 版本控制集成
- 自动化部署

### 2. 性能优化
- 分布式缓存
- 读写分离
- 数据分片

### 3. 生态扩展
- 第三方插件支持
- 开放API平台
- 社区模板库

## 总结

项目服务作为多媒体/游戏引擎系统的核心业务服务，提供了完整的项目生命周期管理功能。其设计特点包括：

1. **完整的项目管理**: 从创建到归档的全生命周期支持
2. **灵活的权限系统**: 基于角色的细粒度权限控制
3. **强大的场景管理**: 支持复杂的3D场景结构和实体管理
4. **高效的协作机制**: 多用户实时协作和冲突解决
5. **可扩展的架构**: 模块化设计支持功能扩展
6. **完善的集成能力**: 与其他微服务的无缝集成

该服务为整个系统提供了稳定可靠的项目管理基础，支持复杂的多媒体内容创作和团队协作需求，是系统架构中的关键组成部分。通过微服务架构和标准化接口，能够很好地支持系统的横向扩展和功能演进。
