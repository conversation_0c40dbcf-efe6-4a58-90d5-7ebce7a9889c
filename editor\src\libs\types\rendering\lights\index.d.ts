/**
 * 光照系统模块
 * 导出所有光源相关的类和接口
 */
export { AreaLight } from './AreaLight';
export type { AreaLightType, AreaLightOptions } from './AreaLight';
export { RectAreaLightComponent } from './RectAreaLight';
export type { RectAreaLightOptions } from './RectAreaLight';
export { SphereAreaLightComponent } from './SphereAreaLight';
export type { SphereAreaLightOptions } from './SphereAreaLight';
export { DiskAreaLightComponent } from './DiskAreaLight';
export type { DiskAreaLightOptions } from './DiskAreaLight';
export { TubeAreaLightComponent } from './TubeAreaLight';
export type { TubeAreaLightOptions } from './TubeAreaLight';
export { IESLight } from './IESLight';
export type { IESLightType, IESLightOptions } from './IESLight';
export { IESLoader } from './IESLoader';
