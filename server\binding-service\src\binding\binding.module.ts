import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { DigitalHuman, KnowledgeBase, DigitalHumanKnowledgeBinding } from '../entities';
import { CacheModule } from '../cache/cache.module';
import { BindingService } from './binding.service';
import { BindingController } from './binding.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DigitalHuman,
      KnowledgeBase,
      DigitalHumanKnowledgeBinding,
    ]),
    ConfigModule,
    CacheModule,
  ],
  controllers: [BindingController],
  providers: [BindingService],
  exports: [BindingService],
})
export class BindingModule {}
