/**
 * 网络协议抽象类
 * 定义网络协议的通用接口
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 网络协议类型
 */
export enum NetworkProtocolType {
  /** WebSocket协议 */
  WEBSOCKET = 'websocket',
  /** WebRTC数据通道 */
  WEBRTC = 'webrtc',
  /** UDP协议 */
  UDP = 'udp',
  /** TCP协议 */
  TCP = 'tcp',
  /** HTTP长轮询 */
  HTTP_LONG_POLLING = 'http_long_polling',
  /** Server-Sent Events */
  SSE = 'sse',
  /** 自定义协议 */
  CUSTOM = 'custom',
}

/**
 * 网络协议状态
 */
export enum NetworkProtocolState {
  /** 已断开连接 */
  DISCONNECTED = 'disconnected',
  /** 正在连接 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 正在断开连接 */
  DISCONNECTING = 'disconnecting',
  /** 连接错误 */
  ERROR = 'error',
}

/**
 * 网络协议配置选项
 */
export interface NetworkProtocolOptions {
  /** 自动重连 */
  autoReconnect?: boolean;
  /** 最大重连次数 */
  maxReconnectAttempts?: number;
  /** 重连间隔（毫秒） */
  reconnectInterval?: number;
  /** 心跳间隔（毫秒） */
  heartbeatInterval?: number;
  /** 心跳超时（毫秒） */
  heartbeatTimeout?: number;
  /** 连接超时（毫秒） */
  connectionTimeout?: number;
  /** 是否启用压缩 */
  enableCompression?: boolean;
  /** 是否启用加密 */
  enableEncryption?: boolean;
  /** 加密密钥 */
  encryptionKey?: string;
  /** 是否启用调试日志 */
  enableDebugLog?: boolean;
  /** 自定义协议选项 */
  [key: string]: any;
}

/**
 * 网络协议统计信息
 */
export interface NetworkProtocolStats {
  /** 发送的字节数 */
  bytesSent: number;
  /** 接收的字节数 */
  bytesReceived: number;
  /** 发送的消息数 */
  messagesSent: number;
  /** 接收的消息数 */
  messagesReceived: number;
  /** 丢失的消息数 */
  messagesLost: number;
  /** 平均往返时间（毫秒） */
  averageRtt: number;
  /** 最小往返时间（毫秒） */
  minRtt: number;
  /** 最大往返时间（毫秒） */
  maxRtt: number;
  /** 连接时间（毫秒） */
  connectionTime: number;
  /** 连接状态 */
  state: NetworkProtocolState;
  /** 重连次数 */
  reconnectCount: number;
  /** 上次活动时间 */
  lastActivityTime: number;
}

/**
 * 网络协议抽象类
 * 定义网络协议的通用接口
 */
export abstract class NetworkProtocol extends EventEmitter {
  /** 协议类型 */
  protected type: NetworkProtocolType;
  
  /** 协议状态 */
  protected state: NetworkProtocolState = NetworkProtocolState.DISCONNECTED;
  
  /** 配置选项 */
  protected options: NetworkProtocolOptions;
  
  /** 统计信息 */
  protected stats: NetworkProtocolStats;
  
  /** 重连尝试次数 */
  protected reconnectAttempts: number = 0;
  
  /** 重连定时器ID */
  protected reconnectTimerId: number | null = null;
  
  /** 心跳定时器ID */
  protected heartbeatTimerId: number | null = null;
  
  /** 连接超时定时器ID */
  protected connectionTimeoutTimerId: number | null = null;
  
  /**
   * 创建网络协议
   * @param type 协议类型
   * @param options 配置选项
   */
  constructor(type: NetworkProtocolType, options: NetworkProtocolOptions = {}) {
    super();
    
    this.type = type;
    
    // 默认配置
    this.options = {
      autoReconnect: true,
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      heartbeatInterval: 30000,
      heartbeatTimeout: 5000,
      connectionTimeout: 10000,
      enableCompression: false,
      enableEncryption: false,
      enableDebugLog: false,
      ...options,
    };
    
    // 初始化统计信息
    this.stats = {
      bytesSent: 0,
      bytesReceived: 0,
      messagesSent: 0,
      messagesReceived: 0,
      messagesLost: 0,
      averageRtt: 0,
      minRtt: Infinity,
      maxRtt: 0,
      connectionTime: 0,
      state: NetworkProtocolState.DISCONNECTED,
      reconnectCount: 0,
      lastActivityTime: Date.now(),
    };
  }
  
  /**
   * 连接到服务器
   * @param url 服务器URL
   * @returns 连接Promise
   */
  public abstract connect(url: string): Promise<void>;
  
  /**
   * 断开连接
   * @returns 断开连接Promise
   */
  public abstract disconnect(): Promise<void>;
  
  /**
   * 发送数据
   * @param data 要发送的数据
   * @returns 是否成功发送
   */
  public abstract send(data: any): boolean;
  
  /**
   * 获取协议类型
   * @returns 协议类型
   */
  public getType(): NetworkProtocolType {
    return this.type;
  }
  
  /**
   * 获取协议状态
   * @returns 协议状态
   */
  public getState(): NetworkProtocolState {
    return this.state;
  }
  
  /**
   * 获取统计信息
   * @returns 统计信息
   */
  public getStats(): NetworkProtocolStats {
    return { ...this.stats };
  }
  
  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      bytesSent: 0,
      bytesReceived: 0,
      messagesSent: 0,
      messagesReceived: 0,
      messagesLost: 0,
      averageRtt: 0,
      minRtt: Infinity,
      maxRtt: 0,
      connectionTime: 0,
      state: this.state,
      reconnectCount: 0,
      lastActivityTime: Date.now(),
    };
  }
  
  /**
   * 是否已连接
   * @returns 是否已连接
   */
  public isConnected(): boolean {
    return this.state === NetworkProtocolState.CONNECTED;
  }
  
  /**
   * 是否正在连接
   * @returns 是否正在连接
   */
  public isConnecting(): boolean {
    return this.state === NetworkProtocolState.CONNECTING;
  }
  
  /**
   * 是否已断开连接
   * @returns 是否已断开连接
   */
  public isDisconnected(): boolean {
    return this.state === NetworkProtocolState.DISCONNECTED || this.state === NetworkProtocolState.ERROR;
  }
  
  /**
   * 设置状态
   * @param state 协议状态
   */
  protected setState(state: NetworkProtocolState): void {
    const oldState = this.state;
    this.state = state;
    this.stats.state = state;
    
    // 触发状态变更事件
    this.emit('stateChange', state, oldState);
    
    // 触发特定状态事件
    switch (state) {
      case NetworkProtocolState.CONNECTED:
        this.emit('connected');
        break;
        
      case NetworkProtocolState.DISCONNECTED:
        this.emit('disconnected');
        break;
        
      case NetworkProtocolState.ERROR:
        this.emit('error', new Error('Connection error'));
        break;
    }
  }
  
  /**
   * 更新统计信息
   * @param stats 部分统计信息
   */
  protected updateStats(stats: Partial<NetworkProtocolStats>): void {
    Object.assign(this.stats, stats);
    this.stats.lastActivityTime = Date.now();
    
    // 触发统计信息更新事件
    this.emit('statsUpdate', this.getStats());
  }
  
  /**
   * 记录发送的数据
   * @param data 发送的数据
   */
  protected recordSentData(data: any): void {
    const size = this.getDataSize(data);
    
    this.stats.bytesSent += size;
    this.stats.messagesSent++;
    this.stats.lastActivityTime = Date.now();
  }
  
  /**
   * 记录接收的数据
   * @param data 接收的数据
   */
  protected recordReceivedData(data: any): void {
    const size = this.getDataSize(data);
    
    this.stats.bytesReceived += size;
    this.stats.messagesReceived++;
    this.stats.lastActivityTime = Date.now();
  }
  
  /**
   * 获取数据大小
   * @param data 数据
   * @returns 数据大小（字节）
   */
  protected getDataSize(data: any): number {
    if (typeof data === 'string') {
      return new TextEncoder().encode(data).length;
    } else if (data instanceof ArrayBuffer || data instanceof Uint8Array) {
      return data.byteLength;
    } else {
      try {
        return new TextEncoder().encode(JSON.stringify(data)).length;
      } catch (error) {
        return 0;
      }
    }
  }
  
  /**
   * 启动心跳
   */
  protected startHeartbeat(): void {
    if (this.heartbeatTimerId !== null || !this.options.heartbeatInterval) {
      return;
    }
    
    this.heartbeatTimerId = window.setInterval(() => {
      this.sendHeartbeat();
    }, this.options.heartbeatInterval);
  }
  
  /**
   * 停止心跳
   */
  protected stopHeartbeat(): void {
    if (this.heartbeatTimerId !== null) {
      clearInterval(this.heartbeatTimerId);
      this.heartbeatTimerId = null;
    }
  }
  
  /**
   * 发送心跳
   */
  protected sendHeartbeat(): void {
    if (!this.isConnected()) {
      return;
    }
    
    // 发送心跳消息
    this.send({ type: 'heartbeat', timestamp: Date.now() });
  }
  
  /**
   * 尝试重连
   */
  protected attemptReconnect(): void {
    if (!this.options.autoReconnect || this.reconnectAttempts >= this.options.maxReconnectAttempts!) {
      this.setState(NetworkProtocolState.ERROR);
      return;
    }
    
    this.reconnectAttempts++;
    this.stats.reconnectCount++;
    
    // 触发重连尝试事件
    this.emit('reconnectAttempt', this.reconnectAttempts);
    
    // 设置重连定时器
    this.reconnectTimerId = window.setTimeout(() => {
      this.reconnectTimerId = null;
      
      // 尝试重新连接
      this.connect('').catch(() => {
        // 如果连接失败，则继续尝试重连
        this.attemptReconnect();
      });
    }, this.options.reconnectInterval);
  }
  
  /**
   * 取消重连
   */
  protected cancelReconnect(): void {
    if (this.reconnectTimerId !== null) {
      clearTimeout(this.reconnectTimerId);
      this.reconnectTimerId = null;
    }
  }
  
  /**
   * 设置连接超时
   */
  protected setConnectionTimeout(): void {
    if (this.connectionTimeoutTimerId !== null || !this.options.connectionTimeout) {
      return;
    }
    
    this.connectionTimeoutTimerId = window.setTimeout(() => {
      this.connectionTimeoutTimerId = null;
      
      if (this.isConnecting()) {
        // 连接超时
        this.setState(NetworkProtocolState.ERROR);
        this.emit('connectionTimeout');
        
        // 尝试重连
        if (this.options.autoReconnect) {
          this.attemptReconnect();
        }
      }
    }, this.options.connectionTimeout);
  }
  
  /**
   * 取消连接超时
   */
  protected cancelConnectionTimeout(): void {
    if (this.connectionTimeoutTimerId !== null) {
      clearTimeout(this.connectionTimeoutTimerId);
      this.connectionTimeoutTimerId = null;
    }
  }
  
  /**
   * 销毁协议
   */
  public dispose(): void {
    // 断开连接
    this.disconnect().catch(() => {});
    
    // 停止心跳
    this.stopHeartbeat();
    
    // 取消重连
    this.cancelReconnect();
    
    // 取消连接超时
    this.cancelConnectionTimeout();
    
    // 移除所有事件监听器
    this.removeAllListeners();
  }
}
