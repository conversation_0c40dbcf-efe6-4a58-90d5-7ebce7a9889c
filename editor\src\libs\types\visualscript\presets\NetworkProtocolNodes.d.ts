/**
 * 视觉脚本网络协议节点
 * 提供不同网络协议的支持，如UDP、TCP、HTTP等
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * UDP发送节点
 * 使用UDP协议发送数据
 */
export declare class UDPSendNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * HTTP请求节点
 * 发送HTTP请求
 */
export declare class HTTPRequestNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 注册网络协议节点
 * @param registry 节点注册表
 */
export declare function registerNetworkProtocolNodes(registry: NodeRegistry): void;
