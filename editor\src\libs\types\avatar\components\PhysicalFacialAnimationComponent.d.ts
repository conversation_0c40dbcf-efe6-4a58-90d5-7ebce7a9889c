/**
 * 物理驱动的面部动画组件
 * 使用物理模拟来驱动面部动画，实现更自然的面部表情
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { FacialExpressionType } from './FacialAnimationComponent';
import { MuscleData } from '../systems/PhysicalFacialAnimationSystem';
/**
 * 物理肌肉
 */
export interface PhysicalMuscle extends MuscleData {
    /** 物理体 */
    bodies?: any[];
    /** 约束 */
    constraints?: any[];
    /** 当前力 */
    currentForce?: THREE.Vector3;
    /** 目标位置 */
    targetPosition?: THREE.Vector3;
    /** 初始位置 */
    initialPosition?: THREE.Vector3;
    /** 是否活动 */
    active?: boolean;
}
/**
 * 表情肌肉配置
 */
export interface ExpressionMuscleConfig {
    /** 肌肉名称 */
    muscleName: string;
    /** 力方向 */
    forceDirection: THREE.Vector3;
    /** 力大小 */
    forceMagnitude: number;
    /** 目标位置 */
    targetPosition?: THREE.Vector3;
    /** 刚度 */
    stiffness?: number;
    /** 阻尼 */
    damping?: number;
}
/**
 * 表情配置
 */
export interface ExpressionConfig {
    /** 表情类型 */
    expression: FacialExpressionType;
    /** 肌肉配置 */
    muscles: ExpressionMuscleConfig[];
}
/**
 * 物理驱动的面部动画组件
 */
export declare class PhysicalFacialAnimationComponent extends Component {
    /** 组件类型 */
    static readonly type = "PhysicalFacialAnimation";
    /** 肌肉映射 */
    private muscles;
    /** 表情配置 */
    private expressionConfigs;
    /** 当前表情 */
    private currentExpression;
    /** 表情权重 */
    private expressionWeight;
    /** 混合形状权重 */
    private blendShapeWeights;
    /** 物理体 */
    private bodies;
    /** 约束 */
    private constraints;
    /** 是否已初始化 */
    private initialized;
    /** 调试模式 */
    private debug;
    /**
     * 构造函数
     * @param entity 实体
     */
    constructor(entity: Entity);
    /**
     * 初始化默认表情配置
     */
    private initDefaultExpressionConfigs;
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 初始化肌肉
     * @param muscle 肌肉
     */
    private initializeMuscle;
    /**
     * 添加肌肉
     * @param muscleData 肌肉数据
     * @returns 是否成功添加
     */
    addMuscle(muscleData: MuscleData): boolean;
    /**
     * 应用表情
     * @param expression 表情类型
     * @param weight 权重
     * @returns 是否成功应用
     */
    applyExpression(expression: FacialExpressionType, weight?: number): boolean;
    /**
     * 应用肌肉力
     * @param muscleName 肌肉名称
     * @param force 力
     * @returns 是否成功应用
     */
    applyMuscleForce(muscleName: string, force: THREE.Vector3): boolean;
    /**
     * 设置肌肉刚度
     * @param muscleName 肌肉名称
     * @param stiffness 刚度
     * @returns 是否成功设置
     */
    setMuscleStiffness(muscleName: string, stiffness: number): boolean;
    /**
     * 设置肌肉阻尼
     * @param muscleName 肌肉名称
     * @param damping 阻尼
     * @returns 是否成功设置
     */
    setMuscleDamping(muscleName: string, damping: number): boolean;
    /**
     * 重置所有肌肉
     * @returns 是否成功重置
     */
    resetAllMuscles(): boolean;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新混合形状权重
     */
    private updateBlendShapeWeights;
    /**
     * 获取混合形状权重
     * @returns 混合形状权重映射
     */
    getBlendShapeWeights(): Map<string, number>;
    /**
     * 获取肌肉
     * @returns 肌肉数组
     */
    getMuscles(): PhysicalMuscle[];
    /**
     * 获取物理体
     * @returns 物理体数组
     */
    getBodies(): any[];
    /**
     * 获取约束
     * @returns 约束数组
     */
    getConstraints(): any[];
    /**
     * 设置调试模式
     * @param debug 是否启用调试
     */
    setDebug(debug: boolean): void;
    /**
     * 获取调试模式
     * @returns 是否启用调试
     */
    isDebug(): boolean;
}
