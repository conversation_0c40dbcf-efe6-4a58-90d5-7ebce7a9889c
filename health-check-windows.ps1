#!/usr/bin/env pwsh
# DL Engine Windows 健康检查脚本
# 使用方法: .\health-check-windows.ps1 [选项]

param(
    [switch]$Detailed,        # 详细检查
    [switch]$Json,            # JSON格式输出
    [switch]$Continuous,      # 持续监控
    [int]$Interval = 30,      # 检查间隔（秒）
    [switch]$Help             # 显示帮助
)

# 设置错误处理
$ErrorActionPreference = "SilentlyContinue"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    if (-not $Json) { Write-ColorOutput Cyan "ℹ️  $message" }
}

function Write-Success($message) {
    if (-not $Json) { Write-ColorOutput Green "✅ $message" }
}

function Write-Warning($message) {
    if (-not $Json) { Write-ColorOutput Yellow "⚠️  $message" }
}

function Write-Error($message) {
    if (-not $Json) { Write-ColorOutput Red "❌ $message" }
}

function Write-Header($message) {
    if (-not $Json) {
        Write-Host ""
        Write-ColorOutput Magenta "🔍 $message"
        Write-Host "=" * 60
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
DL Engine Windows 健康检查脚本

用法: .\health-check-windows.ps1 [选项]

选项:
  -Detailed       执行详细的健康检查
  -Json           以JSON格式输出结果
  -Continuous     持续监控模式
  -Interval <秒>  持续监控的检查间隔（默认30秒）
  -Help           显示此帮助信息

示例:
  .\health-check-windows.ps1                    # 基本健康检查
  .\health-check-windows.ps1 -Detailed          # 详细检查
  .\health-check-windows.ps1 -Json              # JSON格式输出
  .\health-check-windows.ps1 -Continuous        # 持续监控
  .\health-check-windows.ps1 -Continuous -Interval 60  # 每60秒检查一次
"@
}

# 检查Docker状态
function Test-DockerStatus {
    $result = @{
        Name = "Docker Desktop"
        Status = "Unknown"
        Details = @{}
    }
    
    try {
        $dockerInfo = docker info --format "{{json .}}" | ConvertFrom-Json
        $result.Status = "Healthy"
        $result.Details = @{
            Version = $dockerInfo.ServerVersion
            Containers = $dockerInfo.Containers
            Images = $dockerInfo.Images
            MemoryLimit = $dockerInfo.MemTotal
        }
    } catch {
        $result.Status = "Unhealthy"
        $result.Details.Error = $_.Exception.Message
    }
    
    return $result
}

# 检查容器状态
function Test-ContainerStatus {
    $result = @{
        Name = "Containers"
        Status = "Unknown"
        Details = @{}
    }
    
    try {
        $containers = docker-compose -f docker-compose.windows.yml ps --format json | ConvertFrom-Json
        $runningCount = 0
        $totalCount = 0
        $containerDetails = @{}
        
        foreach ($container in $containers) {
            $totalCount++
            $containerDetails[$container.Service] = @{
                State = $container.State
                Status = $container.Status
                Ports = $container.Publishers
            }
            
            if ($container.State -eq "running") {
                $runningCount++
            }
        }
        
        $result.Details = @{
            Total = $totalCount
            Running = $runningCount
            Containers = $containerDetails
        }
        
        if ($runningCount -eq $totalCount -and $totalCount -gt 0) {
            $result.Status = "Healthy"
        } elseif ($runningCount -gt 0) {
            $result.Status = "Degraded"
        } else {
            $result.Status = "Unhealthy"
        }
    } catch {
        $result.Status = "Unhealthy"
        $result.Details.Error = $_.Exception.Message
    }
    
    return $result
}

# 检查服务端点
function Test-ServiceEndpoints {
    $endpoints = @(
        @{Name="API Gateway"; URL="http://localhost:3000/api/health"; Timeout=10},
        @{Name="MinIO API"; URL="http://localhost:9000/minio/health/live"; Timeout=5},
        @{Name="MinIO Console"; URL="http://localhost:9001"; Timeout=5},
        @{Name="Chroma API"; URL="http://localhost:8000/api/v1/heartbeat"; Timeout=5},
        @{Name="Frontend"; URL="http://localhost"; Timeout=10}
    )
    
    $result = @{
        Name = "Service Endpoints"
        Status = "Unknown"
        Details = @{}
    }
    
    $healthyCount = 0
    $totalCount = $endpoints.Count
    
    foreach ($endpoint in $endpoints) {
        try {
            $response = Invoke-RestMethod -Uri $endpoint.URL -TimeoutSec $endpoint.Timeout -Method Get
            $result.Details[$endpoint.Name] = @{
                Status = "Healthy"
                URL = $endpoint.URL
                ResponseTime = (Measure-Command { Invoke-RestMethod -Uri $endpoint.URL -TimeoutSec $endpoint.Timeout }).TotalMilliseconds
            }
            $healthyCount++
        } catch {
            $result.Details[$endpoint.Name] = @{
                Status = "Unhealthy"
                URL = $endpoint.URL
                Error = $_.Exception.Message
            }
        }
    }
    
    if ($healthyCount -eq $totalCount) {
        $result.Status = "Healthy"
    } elseif ($healthyCount -gt 0) {
        $result.Status = "Degraded"
    } else {
        $result.Status = "Unhealthy"
    }
    
    return $result
}

# 检查系统资源
function Test-SystemResources {
    $result = @{
        Name = "System Resources"
        Status = "Unknown"
        Details = @{}
    }
    
    try {
        # 内存检查
        $memory = Get-CimInstance -ClassName Win32_ComputerSystem
        $memoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
        
        # 磁盘检查
        $disk = Get-CimInstance -ClassName Win32_LogicalDisk -Filter "DeviceID='C:'"
        $freeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($disk.Size / 1GB, 2)
        $usedPercentage = [math]::Round((($disk.Size - $disk.FreeSpace) / $disk.Size) * 100, 2)
        
        # CPU检查
        $cpu = Get-CimInstance -ClassName Win32_Processor
        $cpuUsage = (Get-Counter "\Processor(_Total)\% Processor Time").CounterSamples.CookedValue
        
        $result.Details = @{
            Memory = @{
                TotalGB = $memoryGB
                Status = if ($memoryGB -ge 8) { "Adequate" } else { "Insufficient" }
            }
            Disk = @{
                TotalGB = $totalSpaceGB
                FreeGB = $freeSpaceGB
                UsedPercentage = $usedPercentage
                Status = if ($freeSpaceGB -ge 20) { "Adequate" } else { "Low" }
            }
            CPU = @{
                Cores = $cpu.NumberOfCores
                Usage = [math]::Round($cpuUsage, 2)
                Status = if ($cpuUsage -lt 80) { "Normal" } else { "High" }
            }
        }
        
        # 综合状态评估
        $issues = @()
        if ($memoryGB -lt 8) { $issues += "Low Memory" }
        if ($freeSpaceGB -lt 20) { $issues += "Low Disk Space" }
        if ($cpuUsage -gt 90) { $issues += "High CPU Usage" }
        
        if ($issues.Count -eq 0) {
            $result.Status = "Healthy"
        } elseif ($issues.Count -le 1) {
            $result.Status = "Warning"
        } else {
            $result.Status = "Critical"
        }
        
        $result.Details.Issues = $issues
        
    } catch {
        $result.Status = "Error"
        $result.Details.Error = $_.Exception.Message
    }
    
    return $result
}

# 检查网络连接
function Test-NetworkConnectivity {
    $result = @{
        Name = "Network Connectivity"
        Status = "Unknown"
        Details = @{}
    }
    
    try {
        # 检查Docker网络
        $networks = docker network ls --filter "name=dl-engine" --format "{{json .}}" | ConvertFrom-Json
        
        $result.Details.DockerNetworks = @{}
        foreach ($network in $networks) {
            $networkInfo = docker network inspect $network.Name | ConvertFrom-Json
            $result.Details.DockerNetworks[$network.Name] = @{
                Driver = $network.Driver
                Scope = $network.Scope
                Containers = $networkInfo.Containers.Count
            }
        }
        
        # 检查端口监听
        $ports = @(80, 3000, 3306, 6379, 9000, 9001, 8000)
        $result.Details.PortStatus = @{}
        
        foreach ($port in $ports) {
            $connection = Test-NetConnection -ComputerName localhost -Port $port -InformationLevel Quiet
            $result.Details.PortStatus[$port] = if ($connection) { "Open" } else { "Closed" }
        }
        
        $result.Status = "Healthy"
        
    } catch {
        $result.Status = "Error"
        $result.Details.Error = $_.Exception.Message
    }
    
    return $result
}

# 执行健康检查
function Invoke-HealthCheck {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    $healthCheck = @{
        Timestamp = $timestamp
        OverallStatus = "Unknown"
        Checks = @()
    }
    
    # 基本检查
    $healthCheck.Checks += Test-DockerStatus
    $healthCheck.Checks += Test-ContainerStatus
    $healthCheck.Checks += Test-ServiceEndpoints
    
    # 详细检查
    if ($Detailed) {
        $healthCheck.Checks += Test-SystemResources
        $healthCheck.Checks += Test-NetworkConnectivity
    }
    
    # 计算总体状态
    $healthyCount = ($healthCheck.Checks | Where-Object { $_.Status -eq "Healthy" }).Count
    $totalCount = $healthCheck.Checks.Count
    $unhealthyCount = ($healthCheck.Checks | Where-Object { $_.Status -eq "Unhealthy" }).Count
    
    if ($unhealthyCount -eq 0) {
        $healthCheck.OverallStatus = "Healthy"
    } elseif ($healthyCount -gt $unhealthyCount) {
        $healthCheck.OverallStatus = "Degraded"
    } else {
        $healthCheck.OverallStatus = "Unhealthy"
    }
    
    return $healthCheck
}

# 输出结果
function Write-HealthCheckResult($healthCheck) {
    if ($Json) {
        $healthCheck | ConvertTo-Json -Depth 10
    } else {
        Write-Header "DL Engine 健康检查报告"
        Write-Host "检查时间: $($healthCheck.Timestamp)" -ForegroundColor Gray
        Write-Host "总体状态: " -NoNewline
        
        switch ($healthCheck.OverallStatus) {
            "Healthy" { Write-Success $healthCheck.OverallStatus }
            "Degraded" { Write-Warning $healthCheck.OverallStatus }
            "Unhealthy" { Write-Error $healthCheck.OverallStatus }
            default { Write-Info $healthCheck.OverallStatus }
        }
        
        Write-Host ""
        
        foreach ($check in $healthCheck.Checks) {
            Write-Host "$($check.Name): " -NoNewline
            switch ($check.Status) {
                "Healthy" { Write-Success $check.Status }
                "Degraded" { Write-Warning $check.Status }
                "Unhealthy" { Write-Error $check.Status }
                default { Write-Info $check.Status }
            }
            
            if ($Detailed -and $check.Details) {
                foreach ($key in $check.Details.Keys) {
                    if ($check.Details[$key] -is [hashtable]) {
                        Write-Host "  $key:" -ForegroundColor Gray
                        foreach ($subKey in $check.Details[$key].Keys) {
                            Write-Host "    $subKey`: $($check.Details[$key][$subKey])" -ForegroundColor DarkGray
                        }
                    } else {
                        Write-Host "  $key`: $($check.Details[$key])" -ForegroundColor Gray
                    }
                }
            }
        }
    }
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    if ($Continuous) {
        Write-Info "启动持续监控模式，检查间隔: $Interval 秒"
        Write-Info "按 Ctrl+C 停止监控"
        
        while ($true) {
            Clear-Host
            $healthCheck = Invoke-HealthCheck
            Write-HealthCheckResult $healthCheck
            
            Start-Sleep -Seconds $Interval
        }
    } else {
        $healthCheck = Invoke-HealthCheck
        Write-HealthCheckResult $healthCheck
        
        # 设置退出代码
        switch ($healthCheck.OverallStatus) {
            "Healthy" { exit 0 }
            "Degraded" { exit 1 }
            "Unhealthy" { exit 2 }
            default { exit 3 }
        }
    }
}

# 执行主函数
try {
    Main
} catch {
    if ($Json) {
        @{
            Error = $_.Exception.Message
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        } | ConvertTo-Json
    } else {
        Write-Error "健康检查失败: $($_.Exception.Message)"
    }
    exit 4
}
