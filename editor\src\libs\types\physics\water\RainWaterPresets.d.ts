/**
 * 雨水预设
 * 提供各种类型的雨水预设配置
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
/**
 * 雨水预设类型
 */
export declare enum RainWaterPresetType {
    /** 轻雨 */
    LIGHT = "light",
    /** 中雨 */
    MEDIUM = "medium",
    /** 暴雨 */
    HEAVY = "heavy",
    /** 雷雨 */
    THUNDERSTORM = "thunderstorm",
    /** 季风雨 */
    MONSOON = "monsoon",
    /** 春雨 */
    SPRING_RAIN = "spring_rain",
    /** 夏雨 */
    SUMMER_RAIN = "summer_rain",
    /** 秋雨 */
    AUTUMN_RAIN = "autumn_rain",
    /** 冬雨 */
    WINTER_RAIN = "winter_rain"
}
/**
 * 雨水预设配置
 */
export interface RainWaterPresetConfig {
    /** 预设类型 */
    type: RainWaterPresetType;
    /** 位置 */
    position?: THREE.Vector3;
    /** 旋转 */
    rotation?: THREE.Euler;
    /** 尺寸 */
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    /** 是否启用 */
    enabled?: boolean;
}
/**
 * 雨水预设
 */
export declare class RainWaterPresets {
    /**
     * 创建雨水预设
     * @param world 世界
     * @param config 预设配置
     * @returns 雨水实体
     */
    static createPreset(world: World, config: RainWaterPresetConfig): Entity;
    /**
     * 应用轻雨预设
     * @param config 雨水配置
     */
    private static applyLightPreset;
    /**
     * 应用中雨预设
     * @param config 雨水配置
     */
    private static applyMediumPreset;
    /**
     * 应用暴雨预设
     * @param config 雨水配置
     */
    private static applyHeavyPreset;
    /**
     * 应用雷雨预设
     * @param config 雨水配置
     */
    private static applyThunderstormPreset;
    /**
     * 应用季风雨预设
     * @param config 雨水配置
     */
    private static applyMonsoonPreset;
    /**
     * 应用春雨预设
     * @param config 雨水配置
     */
    private static applySpringRainPreset;
    /**
     * 应用夏雨预设
     * @param config 雨水配置
     */
    private static applySummerRainPreset;
    /**
     * 应用秋雨预设
     * @param config 雨水配置
     */
    private static applyAutumnRainPreset;
    /**
     * 应用冬雨预设
     * @param config 雨水配置
     */
    private static applyWinterRainPreset;
}
