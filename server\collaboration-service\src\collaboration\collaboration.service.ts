/**
 * 协作服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { WebSocket } from 'ws';
import { v4 as uuidv4 } from 'uuid';
import { CollaborationRoom } from './entities/collaboration-room.entity';
import {
  Message,
  MessageType,
  CollaborationUser,
  CollaborationRole,
  Operation,
  JoinMessageData,
  UserStatusData,
} from './dto/message.dto';
import { MessageCompressor, CompressionAlgorithm } from '../utils/message-compressor';

@Injectable()
export class CollaborationService {
  private readonly logger = new Logger(CollaborationService.name);

  // 房间映射表，键为 "projectId:sceneId"
  private rooms: Map<string, CollaborationRoom> = new Map();

  // 消息压缩器
  private readonly messageCompressor: MessageCompressor;

  // 用户颜色列表
  private readonly userColors: string[] = [
    '#1890ff', // 蓝色
    '#52c41a', // 绿色
    '#fa8c16', // 橙色
    '#722ed1', // 紫色
    '#eb2f96', // 粉色
    '#faad14', // 黄色
    '#13c2c2', // 青色
    '#f5222d', // 红色
  ];

  constructor() {
    // 初始化消息压缩器
    this.messageCompressor = new MessageCompressor({
      algorithm: CompressionAlgorithm.DEFLATE,
      level: 6,
      minSize: 100,
    });
  }

  /**
   * 获取房间ID
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @returns 房间ID
   */
  private getRoomId(projectId: string, sceneId: string): string {
    return `${projectId}:${sceneId}`;
  }

  /**
   * 获取房间
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @returns 房间
   */
  getRoom(projectId: string, sceneId: string): CollaborationRoom | undefined {
    const roomId = this.getRoomId(projectId, sceneId);
    return this.rooms.get(roomId);
  }

  /**
   * 创建房间
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @returns 房间
   */
  createRoom(projectId: string, sceneId: string): CollaborationRoom {
    const roomId = this.getRoomId(projectId, sceneId);
    const room = new CollaborationRoom(projectId, sceneId);
    this.rooms.set(roomId, room);
    this.logger.log(`创建房间: ${roomId}`);
    return room;
  }

  /**
   * 获取或创建房间
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @returns 房间
   */
  getOrCreateRoom(projectId: string, sceneId: string): CollaborationRoom {
    const room = this.getRoom(projectId, sceneId);
    if (room) {
      return room;
    }
    return this.createRoom(projectId, sceneId);
  }

  /**
   * 删除房间
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  deleteRoom(projectId: string, sceneId: string): void {
    const roomId = this.getRoomId(projectId, sceneId);
    this.rooms.delete(roomId);
    this.logger.log(`删除房间: ${roomId}`);
  }

  /**
   * 处理用户加入
   * @param data 加入数据
   * @param client WebSocket客户端
   * @param userRole 用户角色
   * @returns 是否成功
   */
  handleUserJoin(data: JoinMessageData, client: WebSocket, userRole: CollaborationRole): boolean {
    try {
      const { userId, userName, projectId, sceneId } = data;

      // 获取或创建房间
      const room = this.getOrCreateRoom(projectId, sceneId);

      // 检查用户是否已在房间中
      if (room.getUser(userId)) {
        // 更新客户端连接
        room.clients.set(userId, client);
        this.logger.log(`用户重新连接: ${userName} (${userId}) 到房间 ${projectId}:${sceneId}`);
      } else {
        // 创建用户对象
        const user: CollaborationUser = {
          id: userId,
          name: userName,
          role: userRole,
          color: this.getRandomColor(),
          isActive: true,
          lastActivity: Date.now(),
        };

        // 添加用户到房间
        room.addUser(user, client);
        this.logger.log(`用户加入: ${userName} (${userId}) 到房间 ${projectId}:${sceneId}`);

        // 广播用户加入消息
        this.broadcastToRoom(room, {
          type: MessageType.USER_JOINED,
          data: user,
        }, userId);
      }

      // 发送用户列表
      this.sendToUser(userId, {
        type: MessageType.USER_LIST,
        data: {
          users: room.getAllUsers(),
        },
      }, client);

      // 发送操作历史
      this.sendToUser(userId, {
        type: MessageType.OPERATION_HISTORY,
        data: {
          operations: room.getOperationHistory(),
        },
      }, client);

      return true;
    } catch (error) {
      this.logger.error('处理用户加入时出错:', error);
      return false;
    }
  }

  /**
   * 处理用户离开
   * @param userId 用户ID
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  handleUserLeave(userId: string, projectId: string, sceneId: string): void {
    try {
      const room = this.getRoom(projectId, sceneId);

      if (!room) {
        return;
      }

      const user = room.getUser(userId);

      if (!user) {
        return;
      }

      // 移除用户
      room.removeUser(userId);
      this.logger.log(`用户离开: ${user.name} (${userId}) 从房间 ${projectId}:${sceneId}`);

      // 广播用户离开消息
      this.broadcastToRoom(room, {
        type: MessageType.USER_LEFT,
        data: {
          userId,
          userName: user.name,
        },
      });

      // 如果房间为空，删除房间
      if (room.isEmpty()) {
        this.deleteRoom(projectId, sceneId);
      }
    } catch (error) {
      this.logger.error('处理用户离开时出错:', error);
    }
  }

  /**
   * 处理用户状态更新
   * @param data 状态数据
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  handleUserStatus(data: UserStatusData, projectId: string, sceneId: string): void {
    try {
      const room = this.getRoom(projectId, sceneId);

      if (!room) {
        return;
      }

      const { userId, ...updates } = data;

      // 更新用户状态
      room.updateUserStatus(userId, {
        ...updates,
        lastActivity: Date.now(),
      });

      // 广播用户状态更新
      this.broadcastToRoom(room, {
        type: MessageType.USER_STATUS,
        data,
      }, userId);
    } catch (error) {
      this.logger.error('处理用户状态更新时出错:', error);
    }
  }

  /**
   * 处理操作
   * @param operation 操作
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  handleOperation(operation: Operation, projectId: string, sceneId: string): void {
    try {
      const room = this.getRoom(projectId, sceneId);

      if (!room) {
        return;
      }

      // 添加操作到历史
      room.addOperation(operation);

      // 广播操作
      this.broadcastToRoom(room, {
        type: MessageType.OPERATION,
        data: operation,
      }, operation.userId);
    } catch (error) {
      this.logger.error('处理操作时出错:', error);
    }
  }

  /**
   * 批量处理操作
   * @param operations 操作数组
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  async handleBatchOperations(operations: Operation[], projectId: string, sceneId: string): Promise<void> {
    try {
      if (operations.length === 0) {
        return;
      }

      const room = this.getRoom(projectId, sceneId);

      if (!room) {
        return;
      }

      // 按用户ID分组
      const operationsByUser = new Map<string, Operation[]>();

      for (const operation of operations) {
        if (!operationsByUser.has(operation.userId)) {
          operationsByUser.set(operation.userId, []);
        }

        operationsByUser.get(operation.userId)!.push(operation);

        // 添加操作到历史
        room.addOperation(operation);
      }

      // 为每个用户批量发送操作
      for (const [userId, userOperations] of operationsByUser.entries()) {
        // 如果只有一个操作，使用单个操作消息
        if (userOperations.length === 1) {
          this.broadcastToRoom(room, {
            type: MessageType.OPERATION,
            data: userOperations[0],
          }, userId);
        } else {
          // 否则使用批量操作消息
          this.broadcastToRoom(room, {
            type: MessageType.BATCH_OPERATION,
            data: {
              operations: userOperations,
            },
          }, userId);
        }
      }
    } catch (error) {
      this.logger.error('批量处理操作时出错:', error);
    }
  }

  /**
   * 广播消息到房间
   * @param room 房间
   * @param message 消息
   * @param excludeUserId 排除的用户ID
   */
  async broadcastToRoom(room: CollaborationRoom, message: Message, excludeUserId?: string): Promise<void> {
    try {
      // 普通JSON字符串（用于不支持压缩的客户端）
      const messageStr = JSON.stringify(message);

      // 压缩后的消息缓存
      let compressedMessage: { data: Buffer; algorithm: CompressionAlgorithm } | null = null;

      for (const [userId, client] of room.clients.entries()) {
        if (excludeUserId && userId === excludeUserId) {
          continue;
        }

        if (client.readyState === WebSocket.OPEN) {
          // 检查客户端是否支持压缩
          if ((client as any).supportsCompression && (client as any).compressionAlgorithm) {
            // 延迟压缩，直到确实需要
            if (!compressedMessage) {
              compressedMessage = await this.messageCompressor.compress(message);
            }

            // 添加压缩头信息
            const compressionHeader = Buffer.from([
              0x01, // 压缩标记
              compressedMessage.algorithm === CompressionAlgorithm.DEFLATE ? 0x01 :
              compressedMessage.algorithm === CompressionAlgorithm.GZIP ? 0x02 :
              compressedMessage.algorithm === CompressionAlgorithm.BROTLI ? 0x03 : 0x00,
            ]);

            // 发送压缩消息
            const compressedData = Buffer.concat([compressionHeader, compressedMessage.data]);
            client.send(compressedData);
          } else {
            // 发送未压缩消息
            client.send(messageStr);
          }
        }
      }
    } catch (error) {
      this.logger.error('广播消息时出错:', error);
    }
  }

  /**
   * 发送消息到用户
   * @param userId 用户ID
   * @param message 消息
   * @param client WebSocket客户端
   */
  async sendToUser(userId: string, message: Message, client?: WebSocket): Promise<void> {
    try {
      // 普通JSON字符串（用于不支持压缩的客户端）
      const messageStr = JSON.stringify(message);

      // 确定要使用的客户端
      let targetClient = client;

      if (!targetClient || targetClient.readyState !== WebSocket.OPEN) {
        // 查找所有房间中的用户
        for (const room of this.rooms.values()) {
          const userClient = room.getClient(userId);

          if (userClient && userClient.readyState === WebSocket.OPEN) {
            targetClient = userClient;
            break;
          }
        }
      }

      // 如果找到了有效的客户端
      if (targetClient && targetClient.readyState === WebSocket.OPEN) {
        // 检查客户端是否支持压缩
        if ((targetClient as any).supportsCompression && (targetClient as any).compressionAlgorithm) {
          // 压缩消息
          const { data, algorithm } = await this.messageCompressor.compress(message);

          // 添加压缩头信息
          const compressionHeader = Buffer.from([
            0x01, // 压缩标记
            algorithm === CompressionAlgorithm.DEFLATE ? 0x01 :
            algorithm === CompressionAlgorithm.GZIP ? 0x02 :
            algorithm === CompressionAlgorithm.BROTLI ? 0x03 : 0x00,
          ]);

          // 发送压缩消息
          const compressedData = Buffer.concat([compressionHeader, data]);
          targetClient.send(compressedData);
        } else {
          // 发送未压缩消息
          targetClient.send(messageStr);
        }
      }
    } catch (error) {
      this.logger.error('发送消息时出错:', error);
    }
  }

  /**
   * 获取随机颜色
   * @returns 颜色代码
   */
  private getRandomColor(): string {
    const index = Math.floor(Math.random() * this.userColors.length);
    return this.userColors[index];
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  generateId(): string {
    return uuidv4();
  }
}
