/**
 * 增强版喷泉预设
 * 提供各种类型的增强喷泉预设配置
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { EnhancedFountainType, FountainJetShape } from './EnhancedFountainComponent';
/**
 * 增强版喷泉预设配置
 */
export interface EnhancedFountainPresetConfig {
    /** 预设类型 */
    type: EnhancedFountainType;
    /** 位置 */
    position?: THREE.Vector3;
    /** 尺寸 */
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    /** 颜色 */
    color?: THREE.Color;
    /** 不透明度 */
    opacity?: number;
    /** 喷泉喷射形状 */
    jetShape?: FountainJetShape;
    /** 是否启用彩色照明 */
    enableColoredLighting?: boolean;
    /** 彩色照明颜色 */
    coloredLightingColors?: THREE.Color[];
    /** 是否启用音乐同步 */
    enableMusicSync?: boolean;
    /** 是否启用交互式控制 */
    enableInteractiveControl?: boolean;
    /** 是否启用GPU加速 */
    enableGPUAcceleration?: boolean;
}
/**
 * 增强版喷泉预设
 */
export declare class EnhancedFountainPresets {
    /**
     * 创建喷泉预设
     * @param world 世界
     * @param config 配置
     * @returns 喷泉实体
     */
    static createPreset(world: World, config: EnhancedFountainPresetConfig): Entity;
    /**
     * 应用标准喷泉预设
     * @param config 配置
     */
    private static applyStandardPreset;
    /**
     * 应用高喷泉预设
     * @param config 配置
     */
    private static applyHighPreset;
    /**
     * 应用宽喷泉预设
     * @param config 配置
     */
    private static applyWidePreset;
    /**
     * 应用多喷头喷泉预设
     * @param config 配置
     */
    private static applyMultiJetPreset;
    /**
     * 应用舞蹈喷泉预设
     * @param config 配置
     */
    private static applyDancingPreset;
    /**
     * 应用音乐喷泉预设
     * @param config 配置
     */
    private static applyMusicalPreset;
    /**
     * 应用脉冲喷泉预设
     * @param config 配置
     */
    private static applyPulsePreset;
    /**
     * 应用交替喷泉预设
     * @param config 配置
     */
    private static applyAlternatingPreset;
    /**
     * 应用序列喷泉预设
     * @param config 配置
     */
    private static applySequencePreset;
    /**
     * 应用随机喷泉预设
     * @param config 配置
     */
    private static applyRandomPreset;
    /**
     * 应用螺旋喷泉预设
     * @param config 配置
     */
    private static applySpiralPreset;
    /**
     * 应用圆锥喷泉预设
     * @param config 配置
     */
    private static applyConePreset;
    /**
     * 应用花朵喷泉预设
     * @param config 配置
     */
    private static applyFlowerPreset;
    /**
     * 应用彩虹喷泉预设
     * @param config 配置
     */
    private static applyRainbowPreset;
    /**
     * 应用交互式喷泉预设
     * @param config 配置
     */
    private static applyInteractivePreset;
}
