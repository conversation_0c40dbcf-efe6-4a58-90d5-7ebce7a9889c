import { System } from '../core/System';
import { ParticleEmitter } from './ParticleEmitter';
import { Particle } from './Particle';
export interface ParticleSystemOptions {
    /** 最大粒子数量 */
    maxParticles?: number;
    /** 是否使用GPU加速 */
    useGPU?: boolean;
    /** 是否启用碰撞 */
    enableCollision?: boolean;
    /** 是否启用物理模拟 */
    enablePhysics?: boolean;
    /** 是否启用粒子排序 */
    enableSorting?: boolean;
}
export declare class ParticleSystem extends System {
    /** 系统类型 */
    static readonly type: string;
    /** 粒子发射器列表 */
    private emitters;
    /** 最大粒子数量 */
    private maxParticles;
    /** 是否使用GPU加速 */
    private useGPU;
    /** 是否启用碰撞 */
    private enableCollision;
    /** 是否启用物理模拟 */
    private enablePhysics;
    /** 是否启用粒子排序 */
    private enableSorting;
    /** 物理系统引用 */
    private physicsSystem;
    /** 粒子池 */
    private particlePool;
    /** 活跃粒子数量 */
    private activeParticleCount;
    /**
     * 创建粒子系统
     * @param options 粒子系统选项
     */
    constructor(options?: ParticleSystemOptions);
    /**
     * 初始化粒子池
     */
    private initializeParticlePool;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 处理实体创建事件
     * @param entity 创建的实体
     */
    private handleEntityCreated;
    /**
     * 处理实体移除事件
     * @param entity 移除的实体
     */
    private handleEntityRemoved;
    /**
     * 设置实体的粒子发射器
     * @param entity 实体
     */
    private setupEntityParticles;
    /**
     * 移除实体的粒子发射器
     * @param entity 实体
     */
    private removeEntityParticles;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新活跃粒子计数
     */
    private updateActiveParticleCount;
    /**
     * 对粒子进行排序
     */
    private sortParticles;
    /**
     * 创建粒子
     * @returns 粒子实例，如果粒子池已满则返回null
     */
    createParticle(): Particle | null;
    /**
     * 释放粒子
     * @param particle 要释放的粒子
     */
    releaseParticle(particle: Particle): void;
    /**
     * 添加粒子发射器
     * @param emitter 粒子发射器
     */
    addEmitter(emitter: ParticleEmitter): void;
    /**
     * 移除粒子发射器
     * @param emitter 粒子发射器
     */
    removeEmitter(emitter: ParticleEmitter): void;
    /**
     * 获取所有粒子发射器
     * @returns 粒子发射器数组
     */
    getEmitters(): ParticleEmitter[];
    /**
     * 获取活跃粒子数量
     * @returns 活跃粒子数量
     */
    getActiveParticleCount(): number;
    /**
     * 获取最大粒子数量
     * @returns 最大粒子数量
     */
    getMaxParticles(): number;
    /**
     * 是否使用GPU加速
     * @returns 是否使用GPU加速
     */
    isUsingGPU(): boolean;
    /**
     * 是否启用碰撞
     * @returns 是否启用碰撞
     */
    isCollisionEnabled(): boolean;
    /**
     * 是否启用物理模拟
     * @returns 是否启用物理模拟
     */
    isPhysicsEnabled(): boolean;
    /**
     * 获取物理系统
     * @returns 物理系统
     */
    getPhysicsSystem(): any;
    /**
     * 清除所有粒子
     */
    clearAllParticles(): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
