/**
 * 监控模块
 */
import { Module } from '@nestjs/common';
import { MonitoringService } from './monitoring.service';
import { MonitoringController } from './monitoring.controller';
import { CollaborationModule } from '../collaboration/collaboration.module';

/**
 * 监控模块
 */
@Module({
  imports: [CollaborationModule],
  controllers: [MonitoringController],
  providers: [MonitoringService],
  exports: [MonitoringService],
})
export class MonitoringModule {}
