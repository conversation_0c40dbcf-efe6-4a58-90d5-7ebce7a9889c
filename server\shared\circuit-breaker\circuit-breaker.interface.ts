/**
 * 熔断器接口定义
 */

/**
 * 熔断器状态
 */
export enum CircuitBreakerState {
  /** 关闭状态（正常工作） */
  CLOSED = 'closed',
  /** 开启状态（快速失败） */
  OPEN = 'open',
  /** 半开状态（尝试恢复） */
  HALF_OPEN = 'half-open',
}

/**
 * 熔断器配置
 */
export interface CircuitBreakerOptions {
  /**
   * 熔断器名称
   */
  name: string;

  /**
   * 失败阈值（触发熔断的失败次数）
   */
  failureThreshold?: number;

  /**
   * 成功阈值（恢复服务的成功次数）
   */
  successThreshold?: number;

  /**
   * 超时时间（毫秒）
   */
  timeout?: number;

  /**
   * 重置时间（毫秒）
   * 熔断器从开启状态转为半开状态的时间
   */
  resetTimeout?: number;

  /**
   * 是否启用回退
   */
  enableFallback?: boolean;

  /**
   * 回退函数
   */
  fallback?: (...args: any[]) => any;

  /**
   * 是否启用监控
   */
  enableMonitoring?: boolean;

  /**
   * 监控回调函数
   */
  onStateChange?: (from: CircuitBreakerState, to: CircuitBreakerState) => void;
}

/**
 * 熔断器统计信息
 */
export interface CircuitBreakerStats {
  /**
   * 熔断器名称
   */
  name: string;

  /**
   * 熔断器状态
   */
  state: CircuitBreakerState;

  /**
   * 失败次数
   */
  failures: number;

  /**
   * 成功次数
   */
  successes: number;

  /**
   * 拒绝次数
   */
  rejects: number;

  /**
   * 超时次数
   */
  timeouts: number;

  /**
   * 回退次数
   */
  fallbacks: number;

  /**
   * 上次状态变更时间
   */
  lastStateChangeTime: Date;

  /**
   * 上次失败时间
   */
  lastFailureTime?: Date;

  /**
   * 上次成功时间
   */
  lastSuccessTime?: Date;
}

/**
 * 熔断器接口
 */
export interface ICircuitBreaker {
  /**
   * 执行受熔断器保护的函数
   * @param fn 要执行的函数
   * @param args 函数参数
   */
  execute<T>(fn: (...args: any[]) => Promise<T>, ...args: any[]): Promise<T>;

  /**
   * 获取熔断器状态
   */
  getState(): CircuitBreakerState;

  /**
   * 获取熔断器统计信息
   */
  getStats(): CircuitBreakerStats;

  /**
   * 重置熔断器
   */
  reset(): void;

  /**
   * 强制开启熔断器
   */
  forceOpen(): void;

  /**
   * 强制关闭熔断器
   */
  forceClose(): void;
}
