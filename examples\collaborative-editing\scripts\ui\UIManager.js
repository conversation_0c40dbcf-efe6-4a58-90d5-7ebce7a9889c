/**
 * UI管理器
 * 负责管理用户界面和交互
 */
export class UIManager {
  /**
   * 构造函数
   */
  constructor() {
    this.activeTab = 'properties'; // 当前激活的标签页
    this.isDarkMode = false; // 是否为暗色模式
    this.isFullscreen = false; // 是否为全屏模式
    this.isSidebarCollapsed = false; // 是否折叠侧边栏
    
    // 绑定方法
    this.initialize = this.initialize.bind(this);
    this.setActiveTab = this.setActiveTab.bind(this);
    this.toggleDarkMode = this.toggleDarkMode.bind(this);
    this.toggleFullscreen = this.toggleFullscreen.bind(this);
    this.toggleSidebar = this.toggleSidebar.bind(this);
  }
  
  /**
   * 初始化UI
   */
  initialize() {
    // 设置标签页切换事件
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tab = button.getAttribute('data-tab');
        this.setActiveTab(tab);
      });
    });
    
    // 设置工具按钮事件
    const toolButtons = document.querySelectorAll('.tool-button');
    toolButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tool = button.getAttribute('data-tool');
        this.setActiveTool(tool);
      });
    });
    
    // 设置默认激活的工具
    this.setActiveTool('select');
    
    // 更新UI状态
    this.updateUIState();
    
    console.log('UI初始化完成');
  }
  
  /**
   * 设置激活的标签页
   * @param {string} tab 标签页名称
   */
  setActiveTab(tab) {
    this.activeTab = tab;
    
    // 更新标签按钮状态
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      const buttonTab = button.getAttribute('data-tab');
      if (buttonTab === tab) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
    
    // 更新标签内容显示
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
      const contentId = content.getAttribute('id');
      if (contentId === `${tab}-tab`) {
        content.classList.add('active');
      } else {
        content.classList.remove('active');
      }
    });
    
    console.log('设置激活标签页:', tab);
  }
  
  /**
   * 设置激活的工具
   * @param {string} tool 工具名称
   */
  setActiveTool(tool) {
    // 更新工具按钮状态
    const toolButtons = document.querySelectorAll('.tool-button');
    toolButtons.forEach(button => {
      const buttonTool = button.getAttribute('data-tool');
      if (buttonTool === tool) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
    
    console.log('设置激活工具:', tool);
  }
  
  /**
   * 切换暗色模式
   */
  toggleDarkMode() {
    this.isDarkMode = !this.isDarkMode;
    
    // 更新文档类名
    if (this.isDarkMode) {
      document.documentElement.classList.add('dark-mode');
    } else {
      document.documentElement.classList.remove('dark-mode');
    }
    
    console.log('切换暗色模式:', this.isDarkMode);
  }
  
  /**
   * 切换全屏模式
   */
  toggleFullscreen() {
    if (!this.isFullscreen) {
      // 进入全屏模式
      const element = document.documentElement;
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    } else {
      // 退出全屏模式
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
    
    this.isFullscreen = !this.isFullscreen;
    console.log('切换全屏模式:', this.isFullscreen);
  }
  
  /**
   * 切换侧边栏
   */
  toggleSidebar() {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
    
    // 更新侧边栏状态
    const leftPanel = document.querySelector('.left-panel');
    if (leftPanel) {
      if (this.isSidebarCollapsed) {
        leftPanel.classList.add('collapsed');
      } else {
        leftPanel.classList.remove('collapsed');
      }
    }
    
    console.log('切换侧边栏:', this.isSidebarCollapsed);
  }
  
  /**
   * 显示对话框
   * @param {string} dialogId 对话框ID
   */
  showDialog(dialogId) {
    const dialog = document.getElementById(dialogId);
    if (dialog) {
      dialog.classList.add('active');
    }
  }
  
  /**
   * 隐藏对话框
   * @param {string} dialogId 对话框ID
   */
  hideDialog(dialogId) {
    const dialog = document.getElementById(dialogId);
    if (dialog) {
      dialog.classList.remove('active');
    }
  }
  
  /**
   * 显示通知
   * @param {string} message 通知消息
   * @param {string} type 通知类型 ('info', 'success', 'warning', 'error')
   * @param {number} duration 显示时长（毫秒）
   */
  showNotification(message, type = 'info', duration = 3000) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = message;
    
    // 添加到文档
    const notificationsContainer = document.querySelector('.notifications-container');
    if (!notificationsContainer) {
      // 创建通知容器
      const container = document.createElement('div');
      container.className = 'notifications-container';
      document.body.appendChild(container);
      container.appendChild(notification);
    } else {
      notificationsContainer.appendChild(notification);
    }
    
    // 设置自动关闭
    setTimeout(() => {
      notification.classList.add('closing');
      setTimeout(() => {
        notification.parentNode.removeChild(notification);
      }, 300);
    }, duration);
  }
  
  /**
   * 更新UI状态
   */
  updateUIState() {
    // 更新标签页状态
    this.setActiveTab(this.activeTab);
    
    // 更新暗色模式状态
    if (this.isDarkMode) {
      document.documentElement.classList.add('dark-mode');
    } else {
      document.documentElement.classList.remove('dark-mode');
    }
    
    // 更新侧边栏状态
    const leftPanel = document.querySelector('.left-panel');
    if (leftPanel) {
      if (this.isSidebarCollapsed) {
        leftPanel.classList.add('collapsed');
      } else {
        leftPanel.classList.remove('collapsed');
      }
    }
  }
  
  /**
   * 更新FPS计数器
   * @param {number} fps 帧率
   */
  updateFPSCounter(fps) {
    const fpsCounter = document.getElementById('fps-counter');
    if (fpsCounter) {
      fpsCounter.textContent = `FPS: ${Math.round(fps)}`;
    }
  }
  
  /**
   * 更新保存状态
   * @param {string} status 状态文本
   */
  updateSaveStatus(status) {
    const saveStatus = document.getElementById('save-status');
    if (saveStatus) {
      saveStatus.textContent = status;
    }
  }
  
  /**
   * 更新项目名称
   * @param {string} name 项目名称
   */
  updateProjectName(name) {
    const projectName = document.getElementById('project-name');
    if (projectName) {
      projectName.textContent = name;
    }
  }
  
  /**
   * 更新协作状态
   * @param {string} status 状态文本
   */
  updateCollaborationStatus(status) {
    const collaborationStatus = document.getElementById('collaboration-status');
    if (collaborationStatus) {
      collaborationStatus.textContent = `协作模式: ${status}`;
    }
  }
  
  /**
   * 更新在线用户数量
   * @param {number} count 用户数量
   */
  updateOnlineUsersCount(count) {
    const onlineUsersCount = document.getElementById('online-users-count');
    if (onlineUsersCount) {
      onlineUsersCount.textContent = `在线用户: ${count}`;
    }
  }
}
