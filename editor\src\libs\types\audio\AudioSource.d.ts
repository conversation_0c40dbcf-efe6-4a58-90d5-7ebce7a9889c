/**
 * 音频源
 * 用于播放音频和控制3D音频效果
 */
import * as THREE from 'three';
import { EventCallback } from '../utils/EventEmitter';
import { AudioListener } from './AudioListener';
import { AudioType } from './AudioSystem';
/**
 * 音频源状态
 */
export declare enum AudioSourceState {
    /** 初始化 */
    INITIAL = "initial",
    /** 播放中 */
    PLAYING = "playing",
    /** 暂停 */
    PAUSED = "paused",
    /** 停止 */
    STOPPED = "stopped",
    /** 结束 */
    ENDED = "ended"
}
/**
 * 音频源事件类型
 */
export declare enum AudioSourceEventType {
    /** 播放 */
    PLAY = "play",
    /** 暂停 */
    PAUSE = "pause",
    /** 停止 */
    STOP = "stop",
    /** 结束 */
    END = "end",
    /** 循环 */
    LOOP = "loop",
    /** 音量变化 */
    VOLUME_CHANGE = "volumeChange",
    /** 静音变化 */
    MUTE_CHANGE = "muteChange",
    /** 位置变化 */
    POSITION_CHANGE = "positionChange",
    /** 速度变化 */
    VELOCITY_CHANGE = "velocityChange",
    /** 方向变化 */
    ORIENTATION_CHANGE = "orientationChange",
    /** 缓冲区设置 */
    BUFFER_SET = "bufferSet"
}
/**
 * 音频源选项
 */
export interface AudioSourceOptions {
    /** 音频源ID */
    id: string;
    /** 音频类型 */
    type?: AudioType;
    /** 音频上下文 */
    context: AudioContext;
    /** 音频监听器 */
    listener: AudioListener;
    /** 目标节点 */
    destination?: AudioNode;
    /** 音频缓冲区 */
    buffer?: AudioBuffer;
    /** 是否循环 */
    loop?: boolean;
    /** 音量 */
    volume?: number;
    /** 是否静音 */
    muted?: boolean;
    /** 播放速率 */
    playbackRate?: number;
    /** 是否空间音频 */
    spatial?: boolean;
    /** 位置 */
    position?: THREE.Vector3;
    /** 速度 */
    velocity?: THREE.Vector3;
    /** 方向 */
    orientation?: THREE.Vector3;
    /** 参考距离 */
    refDistance?: number;
    /** 最大距离 */
    maxDistance?: number;
    /** 衰减因子 */
    rolloffFactor?: number;
    /** 内锥角 */
    coneInnerAngle?: number;
    /** 外锥角 */
    coneOuterAngle?: number;
    /** 外锥增益 */
    coneOuterGain?: number;
    /** 失谐 */
    detune?: number;
}
/**
 * 音频源
 */
export declare class AudioSource {
    /** 音频源ID */
    private id;
    /** 音频类型 */
    private type;
    /** 音频上下文 */
    private context;
    /** 音频监听器 - 用于3D音频定位 */
    private readonly listener;
    /** 目标节点 */
    private destination;
    /** 音频缓冲区 */
    private buffer;
    /** 音频缓冲源节点 */
    private source;
    /** 增益节点 */
    private gainNode;
    /** 立体声声相节点 */
    private pannerNode;
    /** 是否循环 */
    private loop;
    /** 音量 */
    private volume;
    /** 是否静音 */
    private muted;
    /** 播放速率 */
    private playbackRate;
    /** 失谐 */
    private detune;
    /** 是否空间音频 */
    private spatial;
    /** 位置 */
    private position;
    /** 速度 */
    private velocity;
    /** 方向 */
    private orientation;
    /** 参考距离 */
    private refDistance;
    /** 最大距离 */
    private maxDistance;
    /** 衰减因子 */
    private rolloffFactor;
    /** 内锥角 */
    private coneInnerAngle;
    /** 外锥角 */
    private coneOuterAngle;
    /** 外锥增益 */
    private coneOuterGain;
    /** 当前状态 */
    private state;
    /** 开始时间 */
    private startTime;
    /** 暂停时间 - 记录暂停发生的时间点 */
    private pauseTime;
    /** 偏移时间 */
    private offset;
    /** 持续时间 */
    private duration;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否已销毁 */
    private destroyed;
    /** 循环计数 */
    private loopCount;
    /** 上次更新时间 - 用于计算时间差 */
    private lastUpdateTime;
    /**
     * 创建音频源
     * @param options 音频源选项
     */
    constructor(options: AudioSourceOptions);
    /**
     * 设置节点连接
     */
    private setupNodes;
    /**
     * 创建声相节点
     */
    private createPannerNode;
    /**
     * 移除声相节点
     */
    private removePannerNode;
    /**
     * 创建音频缓冲源节点
     */
    private createSourceNode;
    /**
     * 处理音频结束事件
     */
    private handleEnded;
    /**
     * 设置音频缓冲区
     * @param buffer 音频缓冲区
     */
    setBuffer(buffer: AudioBuffer): void;
    /**
     * 播放音频
     * @param offset 偏移时间（秒）
     * @param duration 持续时间（秒）
     */
    play(offset?: number, duration?: number): void;
    /**
     * 停止播放
     */
    stop(): void;
    /**
     * 暂停播放
     */
    pause(): void;
    /**
     * 恢复播放
     */
    resume(): void;
    /**
     * 更新音频源
     * @param deltaTime 帧间隔时间（秒）- 保留参数以保持API一致性
     */
    update(deltaTime: number): void;
    /**
     * 获取当前播放时间
     * @returns 当前播放时间（秒）
     */
    getCurrentTime(): number;
    /**
     * 设置循环
     * @param loop 是否循环
     */
    setLoop(loop: boolean): void;
    /**
     * 获取循环
     * @returns 是否循环
     */
    getLoop(): boolean;
    /**
     * 设置音量
     * @param volume 音量（0-1）
     */
    setVolume(volume: number): void;
    /**
     * 获取音量
     * @returns 音量（0-1）
     */
    getVolume(): number;
    /**
     * 设置静音
     * @param muted 是否静音
     */
    setMuted(muted: boolean): void;
    /**
     * 获取静音
     * @returns 是否静音
     */
    getMuted(): boolean;
    /**
     * 设置播放速率
     * @param rate 播放速率
     */
    setPlaybackRate(rate: number): void;
    /**
     * 获取播放速率
     * @returns 播放速率
     */
    getPlaybackRate(): number;
    /**
     * 设置失谐
     * @param detune 失谐（音分）
     */
    setDetune(detune: number): void;
    /**
     * 获取失谐
     * @returns 失谐（音分）
     */
    getDetune(): number;
    /**
     * 设置是否空间音频
     * @param spatial 是否空间音频
     */
    setSpatial(spatial: boolean): void;
    /**
     * 获取是否空间音频
     * @returns 是否空间音频
     */
    getSpatial(): boolean;
    /**
     * 设置位置
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     */
    setPosition(x: number, y: number, z: number): void;
    /**
     * 获取位置
     * @returns 位置
     */
    getPosition(): THREE.Vector3;
    /**
     * 设置速度
     * @param x X速度
     * @param y Y速度
     * @param z Z速度
     */
    setVelocity(x: number, y: number, z: number): void;
    /**
     * 获取速度
     * @returns 速度
     */
    getVelocity(): THREE.Vector3;
    /**
     * 设置方向
     * @param x X方向
     * @param y Y方向
     * @param z Z方向
     */
    setOrientation(x: number, y: number, z: number): void;
    /**
     * 获取方向
     * @returns 方向
     */
    getOrientation(): THREE.Vector3;
    /**
     * 设置参考距离
     * @param distance 参考距离
     */
    setRefDistance(distance: number): void;
    /**
     * 获取参考距离
     * @returns 参考距离
     */
    getRefDistance(): number;
    /**
     * 设置最大距离
     * @param distance 最大距离
     */
    setMaxDistance(distance: number): void;
    /**
     * 获取最大距离
     * @returns 最大距离
     */
    getMaxDistance(): number;
    /**
     * 设置衰减因子
     * @param factor 衰减因子
     */
    setRolloffFactor(factor: number): void;
    /**
     * 获取衰减因子
     * @returns 衰减因子
     */
    getRolloffFactor(): number;
    /**
     * 设置内锥角
     * @param angle 内锥角（度）
     */
    setConeInnerAngle(angle: number): void;
    /**
     * 获取内锥角
     * @returns 内锥角（度）
     */
    getConeInnerAngle(): number;
    /**
     * 设置外锥角
     * @param angle 外锥角（度）
     */
    setConeOuterAngle(angle: number): void;
    /**
     * 获取外锥角
     * @returns 外锥角（度）
     */
    getConeOuterAngle(): number;
    /**
     * 设置外锥增益
     * @param gain 外锥增益（0-1）
     */
    setConeOuterGain(gain: number): void;
    /**
     * 获取外锥增益
     * @returns 外锥增益（0-1）
     */
    getConeOuterGain(): number;
    /**
     * 获取音频缓冲区
     * @returns 音频缓冲区
     */
    getBuffer(): AudioBuffer | null;
    /**
     * 获取音频持续时间
     * @returns 音频持续时间（秒）
     */
    getDuration(): number;
    /**
     * 获取当前状态
     * @returns 当前状态
     */
    getState(): AudioSourceState;
    /**
     * 获取音频源ID
     * @returns 音频源ID
     */
    getId(): string;
    /**
     * 获取音频类型
     * @returns 音频类型
     */
    getType(): AudioType;
    /**
     * 设置音频类型
     * @param type 音频类型
     */
    setType(type: AudioType): void;
    /**
     * 是否正在播放
     * @returns 是否正在播放
     */
    isPlaying(): boolean;
    /**
     * 是否已暂停
     * @returns 是否已暂停
     */
    isPaused(): boolean;
    /**
     * 是否已停止
     * @returns 是否已停止
     */
    isStopped(): boolean;
    /**
     * 是否已结束
     * @returns 是否已结束
     */
    isEnded(): boolean;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    on(type: AudioSourceEventType, listener: EventCallback): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    off(type: AudioSourceEventType, listener: EventCallback): void;
    /**
     * 销毁音频源
     */
    dispose(): void;
    /**
     * 更新声相位置
     */
    private updatePannerPosition;
    /**
     * 更新声相速度
     */
    private updatePannerVelocity;
    /**
     * 更新声相方向
     */
    private updatePannerOrientation;
}
