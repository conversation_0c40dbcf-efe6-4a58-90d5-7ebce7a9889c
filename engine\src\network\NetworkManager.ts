/**
 * 网络管理器
 * 负责管理网络连接、消息处理和数据同步
 */
import { EventEmitter } from '../utils/EventEmitter';

import { NetworkMessage } from './NetworkMessage';
import type { NetworkSystemOptions } from './NetworkSystem';
import { WebRTCConnection } from './WebRTCConnection';
import { WebSocketConnection } from './WebSocketConnection';
import { Debug } from '../utils/Debug';
import { NetworkUser } from './NetworkUser';
import type { NetworkEntity } from './NetworkEntity';
import { MessageType } from './MessageType';


/**
 * 网络管理器
 * 负责管理网络连接、消息处理和数据同步
 */
export class NetworkManager extends EventEmitter {
  /** 配置选项 */
  private options: NetworkSystemOptions;
  
  /** WebSocket连接 */
  private wsConnection: WebSocketConnection | null = null;
  
  /** WebRTC连接映射表 */
  private rtcConnections: Map<string, WebRTCConnection> = new Map();
  
  /** 本地用户ID */
  private localUserId: string | null = null;
  
  /** 本地用户名 */
  private localUsername: string | null = null;
  
  /** 远程用户映射表 */
  private remoteUsers: Map<string, NetworkUser> = new Map();
  
  /** 网络实体映射表 */
  private networkEntities: Map<string, NetworkEntity> = new Map();
  
  /** 是否已连接 */
  private connected: boolean = false;
  
  /** 房间ID */
  private roomId: string | null = null;

  /**
   * 创建网络管理器
   * @param options 配置选项
   */
  constructor(options: NetworkSystemOptions) {
    super();
    
    this.options = options;

    // 设置本地用户信息
    if (options.userId) {
      this.localUserId = options.userId;
    }
    
    if (options.username) {
      this.localUsername = options.username;
    }
  }

  /**
   * 连接到服务器
   * @param serverUrl 服务器URL
   * @param roomId 房间ID
   * @returns Promise
   */
  public async connect(serverUrl: string, roomId?: string): Promise<void> {
    if (this.connected) {
      Debug.warn('NetworkManager', 'Already connected to server');
      return;
    }
    
    this.roomId = roomId || null;
    
    // 创建WebSocket连接
    this.wsConnection = new WebSocketConnection(serverUrl);
    
    // 设置WebSocket事件监听器
    this.setupWebSocketListeners();
    
    // 连接到WebSocket服务器
    await this.wsConnection.connect();
    
    // 发送加入房间消息
    if (this.roomId) {
      this.sendJoinRoom(this.roomId);
    }
    
    this.connected = true;
  }

  /**
   * 断开连接
   * @returns Promise
   */
  public async disconnect(): Promise<void> {
    if (!this.connected) {
      return;
    }
    
    // 发送离开消息
    this.sendLeaveRoom();
    
    // 断开所有WebRTC连接
    for (const connection of this.rtcConnections.values()) {
      await connection.disconnect();
    }
    this.rtcConnections.clear();
    
    // 断开WebSocket连接
    if (this.wsConnection) {
      await this.wsConnection.disconnect();
      this.wsConnection = null;
    }
    
    // 清空用户和实体
    this.remoteUsers.clear();
    this.networkEntities.clear();
    
    this.connected = false;
  }

  /**
   * 更新网络管理器
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新WebRTC连接
    for (const connection of this.rtcConnections.values()) {
      connection.update(deltaTime);
    }
  }

  /**
   * 发送消息到所有用户
   * @param type 消息类型
   * @param data 消息数据
   */
  public sendToAll(type: string, data: any): void {
    const message: NetworkMessage = {
      type,
      data,
      senderId: this.localUserId,
      timestamp: Date.now(),
    };
    
    // 通过WebSocket发送到服务器
    if (this.wsConnection) {
      this.wsConnection.send(MessageType.BROADCAST, message);
    }
    
    // 通过WebRTC发送到所有对等连接
    for (const connection of this.rtcConnections.values()) {
      connection.send(message);
    }
  }

  /**
   * 发送消息到特定用户
   * @param userId 用户ID
   * @param type 消息类型
   * @param data 消息数据
   */
  public sendToUser(userId: string, type: string, data: any): void {
    const message: NetworkMessage = {
      type,
      data,
      senderId: this.localUserId,
      receiverId: userId,
      timestamp: Date.now(),
    };
    
    // 通过WebSocket发送到服务器
    if (this.wsConnection) {
      this.wsConnection.send(MessageType.DIRECT, message);
    }
    
    // 如果存在WebRTC连接，则直接发送
    const connection = this.rtcConnections.get(userId);
    if (connection) {
      connection.send(message);
    }
  }

  /**
   * 发送实体更新
   * @param entityId 实体ID
   * @param data 实体数据
   */
  public sendEntityUpdate(entityId: string, data: any): void {
    const message: NetworkMessage = {
      type: MessageType.ENTITY_UPDATE,
      data: {
        entityId,
        ...data,
      },
      senderId: this.localUserId,
      timestamp: Date.now(),
    };
    
    // 通过WebSocket发送到服务器
    if (this.wsConnection) {
      this.wsConnection.send(MessageType.BROADCAST, message);
    }
    
    // 通过WebRTC发送到所有对等连接
    for (const connection of this.rtcConnections.values()) {
      connection.send(message);
    }
  }

  /**
   * 发送加入房间消息
   * @param roomId 房间ID
   */
  private sendJoinRoom(roomId: string): void {
    if (!this.wsConnection) {
      return;
    }
    
    const message: NetworkMessage = {
      type: MessageType.JOIN_ROOM,
      data: {
        roomId,
        userId: this.localUserId,
        username: this.localUsername,
      },
      senderId: this.localUserId,
      timestamp: Date.now(),
    };
    
    this.wsConnection.send(MessageType.SYSTEM, message);
  }

  /**
   * 发送离开房间消息
   */
  private sendLeaveRoom(): void {
    if (!this.wsConnection || !this.roomId) {
      return;
    }
    
    const message: NetworkMessage = {
      type: MessageType.LEAVE_ROOM,
      data: {
        roomId: this.roomId,
        userId: this.localUserId,
      },
      senderId: this.localUserId,
      timestamp: Date.now(),
    };
    
    this.wsConnection.send(MessageType.SYSTEM, message);
  }

  /**
   * 设置WebSocket事件监听器
   */
  private setupWebSocketListeners(): void {
    if (!this.wsConnection) {
      return;
    }
    
    // 监听连接事件
    this.wsConnection.on('connected', () => {
      Debug.log('NetworkManager', 'WebSocket connected');
      this.emit('connected');
    });
    
    // 监听断开连接事件
    this.wsConnection.on('disconnected', () => {
      Debug.log('NetworkManager', 'WebSocket disconnected');
      this.emit('disconnected');
    });
    
    // 监听错误事件
    this.wsConnection.on('error', (error) => {
      Debug.error('NetworkManager', 'WebSocket error:', error);
      this.emit('error', error);
    });
    
    // 监听消息事件
    this.wsConnection.on('message', (message) => {
      this.handleMessage(message);
    });
  }

  /**
   * 处理接收到的消息
   * @param message 网络消息
   */
  private handleMessage(message: NetworkMessage): void {
    switch (message.type) {
      case MessageType.JOIN_ROOM_SUCCESS:
        this.handleJoinRoomSuccess(message);
        break;
        
      case MessageType.USER_JOINED:
        this.handleUserJoined(message);
        break;
        
      case MessageType.USER_LEFT:
        this.handleUserLeft(message);
        break;
        
      case MessageType.ENTITY_CREATE:
        this.handleEntityCreate(message);
        break;
        
      case MessageType.ENTITY_UPDATE:
        this.handleEntityUpdate(message);
        break;
        
      case MessageType.ENTITY_DELETE:
        this.handleEntityDelete(message);
        break;
        
      case MessageType.WEBRTC_OFFER:
        this.handleWebRTCOffer(message);
        break;
        
      case MessageType.WEBRTC_ANSWER:
        this.handleWebRTCAnswer(message);
        break;
        
      case MessageType.WEBRTC_ICE_CANDIDATE:
        this.handleWebRTCIceCandidate(message);
        break;
        
      default:
        // 触发自定义消息事件
        this.emit('message', message);
        break;
    }
  }

  /**
   * 处理加入房间成功消息
   * @param message 网络消息
   */
  private handleJoinRoomSuccess(message: NetworkMessage): void {
    const { userId, users, entities } = message.data;
    
    // 设置本地用户ID
    this.localUserId = userId;
    
    Debug.log('NetworkManager', `Joined room as user ${userId}`);
    
    // 处理房间中的用户
    if (users && Array.isArray(users)) {
      for (const user of users) {
        if (user.userId !== this.localUserId) {
          this.addRemoteUser(user.userId, user.username);
          
          // 如果启用了WebRTC，则创建对等连接
          if (this.options.enableWebRTC) {
            this.createWebRTCConnection(user.userId);
          }
        }
      }
    }
    
    // 处理房间中的实体
    if (entities && Array.isArray(entities)) {
      for (const entity of entities) {
        this.addNetworkEntity(entity.entityId, entity);
      }
    }
    
    // 触发加入房间成功事件
    this.emit('joinedRoom', this.roomId, userId);
  }

  /**
   * 处理用户加入消息
   * @param message 网络消息
   */
  private handleUserJoined(message: NetworkMessage): void {
    const { userId, username } = message.data;
    
    // 添加远程用户
    this.addRemoteUser(userId, username);
    
    // 如果启用了WebRTC，则创建对等连接
    if (this.options.enableWebRTC) {
      this.createWebRTCConnection(userId);
    }
    
    // 触发用户加入事件
    this.emit('userJoined', userId, username);
  }

  /**
   * 处理用户离开消息
   * @param message 网络消息
   */
  private handleUserLeft(message: NetworkMessage): void {
    const { userId } = message.data;
    
    // 移除WebRTC连接
    this.removeWebRTCConnection(userId);
    
    // 移除远程用户
    this.removeRemoteUser(userId);
    
    // 触发用户离开事件
    this.emit('userLeft', userId);
  }

  /**
   * 处理实体创建消息
   * @param message 网络消息
   */
  private handleEntityCreate(message: NetworkMessage): void {
    const { entityId, ...data } = message.data;
    
    // 添加网络实体
    this.addNetworkEntity(entityId, data);
    
    // 触发实体创建事件
    this.emit('entityCreated', entityId, data);
  }

  /**
   * 处理实体更新消息
   * @param message 网络消息
   */
  private handleEntityUpdate(message: NetworkMessage): void {
    const { entityId, ...data } = message.data;
    
    // 更新网络实体
    this.updateNetworkEntity(entityId, data);
    
    // 触发实体更新事件
    this.emit('entityUpdated', entityId, data);
  }

  /**
   * 处理实体删除消息
   * @param message 网络消息
   */
  private handleEntityDelete(message: NetworkMessage): void {
    const { entityId } = message.data;
    
    // 移除网络实体
    this.removeNetworkEntity(entityId);
    
    // 触发实体删除事件
    this.emit('entityDeleted', entityId);
  }

  /**
   * 处理WebRTC提议消息
   * @param message 网络消息
   */
  private handleWebRTCOffer(message: NetworkMessage): void {
    const { userId, offer } = message.data;
    
    // 获取WebRTC连接
    const connection = this.rtcConnections.get(userId);
    
    if (connection) {
      // 处理提议
      connection.handleOffer(offer);
    }
  }

  /**
   * 处理WebRTC应答消息
   * @param message 网络消息
   */
  private handleWebRTCAnswer(message: NetworkMessage): void {
    const { userId, answer } = message.data;
    
    // 获取WebRTC连接
    const connection = this.rtcConnections.get(userId);
    
    if (connection) {
      // 处理应答
      connection.handleAnswer(answer);
    }
  }

  /**
   * 处理WebRTC ICE候选消息
   * @param message 网络消息
   */
  private handleWebRTCIceCandidate(message: NetworkMessage): void {
    const { userId, candidate } = message.data;
    
    // 获取WebRTC连接
    const connection = this.rtcConnections.get(userId);
    
    if (connection) {
      // 处理ICE候选
      connection.handleIceCandidate(candidate);
    }
  }

  /**
   * 添加远程用户
   * @param userId 用户ID
   * @param username 用户名
   */
  private addRemoteUser(userId: string, username: string): void {
    if (this.remoteUsers.has(userId)) {
      return;
    }
    
    const user: NetworkUser = {
      userId,
      username,
      joinTime: Date.now(),
    };
    
    this.remoteUsers.set(userId, user);
    
    Debug.log('NetworkManager', `Added remote user: ${username} (${userId})`);
  }

  /**
   * 移除远程用户
   * @param userId 用户ID
   */
  private removeRemoteUser(userId: string): void {
    if (!this.remoteUsers.has(userId)) {
      return;
    }
    
    const user = this.remoteUsers.get(userId)!;
    
    this.remoteUsers.delete(userId);
    
    Debug.log('NetworkManager', `Removed remote user: ${user.username} (${userId})`);
  }

  /**
   * 添加网络实体
   * @param entityId 实体ID
   * @param data 实体数据
   */
  private addNetworkEntity(entityId: string, data: any): void {
    if (this.networkEntities.has(entityId)) {
      return;
    }
    
    const entity: NetworkEntity = {
      entityId,
      ownerId: data.ownerId,
      data,
      createTime: Date.now(),
      updateTime: Date.now(),
    };
    
    this.networkEntities.set(entityId, entity);
    
    Debug.log('NetworkManager', `Added network entity: ${entityId}`);
  }

  /**
   * 更新网络实体
   * @param entityId 实体ID
   * @param data 实体数据
   */
  private updateNetworkEntity(entityId: string, data: any): void {
    const entity = this.networkEntities.get(entityId);
    
    if (!entity) {
      return;
    }
    
    // 更新实体数据
    entity.data = { ...entity.data, ...data };
    entity.updateTime = Date.now();
  }

  /**
   * 移除网络实体
   * @param entityId 实体ID
   */
  private removeNetworkEntity(entityId: string): void {
    if (!this.networkEntities.has(entityId)) {
      return;
    }
    
    this.networkEntities.delete(entityId);
    
    Debug.log('NetworkManager', `Removed network entity: ${entityId}`);
  }

  /**
   * 创建WebRTC连接
   * @param userId 用户ID
   */
  private createWebRTCConnection(userId: string): void {
    if (this.rtcConnections.has(userId)) {
      return;
    }
    
    const connection = new WebRTCConnection(userId, this.options.iceServers || []);
    
    // 设置WebRTC事件监听器
    this.setupWebRTCListeners(connection);
    
    // 添加到连接映射表
    this.rtcConnections.set(userId, connection);
    
    // 创建对等连接
    connection.createConnection();
    
    Debug.log('NetworkManager', `Created WebRTC connection to user: ${userId}`);
  }

  /**
   * 移除WebRTC连接
   * @param userId 用户ID
   */
  private removeWebRTCConnection(userId: string): void {
    const connection = this.rtcConnections.get(userId);
    
    if (!connection) {
      return;
    }
    
    // 断开连接
    connection.disconnect();
    
    // 从连接映射表中移除
    this.rtcConnections.delete(userId);
    
    Debug.log('NetworkManager', `Removed WebRTC connection to user: ${userId}`);
  }

  /**
   * 设置WebRTC事件监听器
   * @param connection WebRTC连接
   */
  private setupWebRTCListeners(connection: WebRTCConnection): void {
    // 监听连接事件
    connection.on('connected', () => {
      Debug.log('NetworkManager', `WebRTC connected to user: ${connection.getUserId()}`);
      this.emit('webrtcConnected', connection.getUserId());
    });
    
    // 监听断开连接事件
    connection.on('disconnected', () => {
      Debug.log('NetworkManager', `WebRTC disconnected from user: ${connection.getUserId()}`);
      this.emit('webrtcDisconnected', connection.getUserId());
    });
    
    // 监听错误事件
    connection.on('error', (error) => {
      Debug.error('NetworkManager', `WebRTC error with user ${connection.getUserId()}:`, error);
      this.emit('webrtcError', connection.getUserId(), error);
    });
    
    // 监听消息事件
    connection.on('message', (message) => {
      this.handleMessage(message);
    });
    
    // 监听ICE候选事件
    connection.on('iceCandidate', (candidate) => {
      this.sendWebRTCIceCandidate(connection.getUserId(), candidate);
    });
    
    // 监听提议事件
    connection.on('offer', (offer) => {
      this.sendWebRTCOffer(connection.getUserId(), offer);
    });
    
    // 监听应答事件
    connection.on('answer', (answer) => {
      this.sendWebRTCAnswer(connection.getUserId(), answer);
    });
  }

  /**
   * 发送WebRTC提议
   * @param userId 用户ID
   * @param offer 提议
   */
  private sendWebRTCOffer(userId: string, offer: RTCSessionDescriptionInit): void {
    if (!this.wsConnection) {
      return;
    }
    
    const message: NetworkMessage = {
      type: MessageType.WEBRTC_OFFER,
      data: {
        userId: this.localUserId,
        targetUserId: userId,
        offer,
      },
      senderId: this.localUserId,
      receiverId: userId,
      timestamp: Date.now(),
    };
    
    this.wsConnection.send(MessageType.SYSTEM, message);
  }

  /**
   * 发送WebRTC应答
   * @param userId 用户ID
   * @param answer 应答
   */
  private sendWebRTCAnswer(userId: string, answer: RTCSessionDescriptionInit): void {
    if (!this.wsConnection) {
      return;
    }
    
    const message: NetworkMessage = {
      type: MessageType.WEBRTC_ANSWER,
      data: {
        userId: this.localUserId,
        targetUserId: userId,
        answer,
      },
      senderId: this.localUserId,
      receiverId: userId,
      timestamp: Date.now(),
    };
    
    this.wsConnection.send(MessageType.SYSTEM, message);
  }

  /**
   * 发送WebRTC ICE候选
   * @param userId 用户ID
   * @param candidate ICE候选
   */
  private sendWebRTCIceCandidate(userId: string, candidate: RTCIceCandidateInit): void {
    if (!this.wsConnection) {
      return;
    }
    
    const message: NetworkMessage = {
      type: MessageType.WEBRTC_ICE_CANDIDATE,
      data: {
        userId: this.localUserId,
        targetUserId: userId,
        candidate,
      },
      senderId: this.localUserId,
      receiverId: userId,
      timestamp: Date.now(),
    };
    
    this.wsConnection.send(MessageType.SYSTEM, message);
  }

  /**
   * 销毁网络管理器
   */
  public dispose(): void {
    // 断开连接
    this.disconnect();
    
    // 移除所有事件监听器
    this.removeAllListeners();
  }
}
