/**
 * 动画混合性能监控工具
 * 用于收集和分析动画混合系统的性能数据
 */
export interface BlendPerformanceData {
    /** 操作名称 */
    operation: string;
    /** 开始时间 */
    startTime: number;
    /** 结束时间 */
    endTime: number;
    /** 持续时间（毫秒） */
    duration: number;
    /** 混合层数量 */
    layerCount?: number;
    /** 遮罩数量 */
    maskCount?: number;
    /** 混合模式 */
    blendMode?: string;
    /** 混合因子 */
    blendFactor?: number;
    /** 是否完成 */
    isComplete?: boolean;
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 是否使用对象池 */
    useObjectPool?: boolean;
    /** 是否使用批处理 */
    useBatchProcessing?: boolean;
    /** 其他元数据 */
    metadata?: Record<string, any>;
}
/**
 * 性能统计信息
 */
export interface PerformanceStats {
    /** 总操作次数 */
    totalOperations: number;
    /** 总持续时间 */
    totalDuration: number;
    /** 平均持续时间 */
    averageDuration: number;
    /** 最小持续时间 */
    minDuration: number;
    /** 最大持续时间 */
    maxDuration: number;
    /** 按操作类型分组的统计信息 */
    byOperation: Record<string, {
        count: number;
        totalDuration: number;
        averageDuration: number;
        minDuration: number;
        maxDuration: number;
    }>;
    /** 按混合层数量分组的统计信息 */
    byLayerCount?: Record<number, {
        count: number;
        totalDuration: number;
        averageDuration: number;
    }>;
    /** 按混合模式分组的统计信息 */
    byBlendMode?: Record<string, {
        count: number;
        totalDuration: number;
        averageDuration: number;
    }>;
    /** 缓存性能比较 */
    cacheComparison?: {
        withCache: {
            count: number;
            totalDuration: number;
            averageDuration: number;
        };
        withoutCache: {
            count: number;
            totalDuration: number;
            averageDuration: number;
        };
    };
    /** 对象池性能比较 */
    objectPoolComparison?: {
        withObjectPool: {
            count: number;
            totalDuration: number;
            averageDuration: number;
        };
        withoutObjectPool: {
            count: number;
            totalDuration: number;
            averageDuration: number;
        };
    };
    /** 批处理性能比较 */
    batchProcessingComparison?: {
        withBatchProcessing: {
            count: number;
            totalDuration: number;
            averageDuration: number;
        };
        withoutBatchProcessing: {
            count: number;
            totalDuration: number;
            averageDuration: number;
        };
    };
}
/**
 * 动画混合性能监控器
 */
export declare class BlendPerformanceMonitor {
    /** 性能数据记录 */
    private records;
    /** 是否启用 */
    private enabled;
    /** 最大记录数量 */
    private maxRecords;
    /** 单例实例 */
    private static instance;
    /**
     * 获取单例实例
     */
    static getInstance(): BlendPerformanceMonitor;
    /**
     * 启用性能监控
     * @param maxRecords 最大记录数量
     */
    enable(maxRecords?: number): void;
    /**
     * 禁用性能监控
     */
    disable(): void;
    /**
     * 清除所有记录
     */
    clear(): void;
    /**
     * 开始记录操作
     * @param operation 操作名称
     * @param metadata 元数据
     * @returns 操作ID
     */
    startOperation(operation: string, metadata?: Record<string, any>): number;
    /**
     * 结束记录操作
     * @param id 操作ID
     * @param additionalData 附加数据
     */
    endOperation(id: number, additionalData?: Partial<BlendPerformanceData>): void;
    /**
     * 获取所有记录
     */
    getRecords(): BlendPerformanceData[];
    /**
     * 获取性能统计信息
     */
    getStats(): PerformanceStats;
    /**
     * 导出性能数据为JSON
     */
    exportToJSON(): string;
}
