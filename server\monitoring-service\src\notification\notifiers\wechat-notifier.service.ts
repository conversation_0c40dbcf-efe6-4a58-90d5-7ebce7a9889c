import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

/**
 * 企业微信通知服务
 * 负责向企业微信发送通知消息
 */
@Injectable()
export class WeChatNotifierService {
  private readonly logger = new Logger(WeChatNotifierService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 发送企业微信通知
   */
  async sendNotification(data: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success';
    webhookUrl: string;
    mentionedList?: string[];
    mentionedMobileList?: string[];
    metadata?: any;
  }): Promise<boolean> {
    try {
      const payload = {
        msgtype: 'markdown',
        markdown: {
          content: this.formatMarkdownMessage(data),
          mentioned_list: data.mentionedList || [],
          mentioned_mobile_list: data.mentionedMobileList || [],
        },
      };

      const response = await firstValueFrom(
        this.httpService.post(data.webhookUrl, payload)
      );

      if (response.data.errcode === 0) {
        this.logger.log(`企业微信通知发送成功: ${data.title}`);
        return true;
      } else {
        this.logger.error(`企业微信通知发送失败: ${response.data.errmsg}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`发送企业微信通知时出错: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 发送文本通知
   */
  async sendTextNotification(data: {
    content: string;
    webhookUrl: string;
    mentionedList?: string[];
    mentionedMobileList?: string[];
  }): Promise<boolean> {
    try {
      const payload = {
        msgtype: 'text',
        text: {
          content: data.content,
          mentioned_list: data.mentionedList || [],
          mentioned_mobile_list: data.mentionedMobileList || [],
        },
      };

      const response = await firstValueFrom(
        this.httpService.post(data.webhookUrl, payload)
      );

      if (response.data.errcode === 0) {
        this.logger.log('企业微信文本通知发送成功');
        return true;
      } else {
        this.logger.error(`企业微信文本通知发送失败: ${response.data.errmsg}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`发送企业微信文本通知时出错: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 测试企业微信连接
   */
  async testConnection(webhookUrl: string): Promise<boolean> {
    try {
      const testPayload = {
        msgtype: 'text',
        text: {
          content: '这是一条测试消息，用于验证企业微信通知配置是否正确。',
        },
      };

      const response = await firstValueFrom(
        this.httpService.post(webhookUrl, testPayload)
      );

      return response.data.errcode === 0;
    } catch (error) {
      this.logger.error(`测试企业微信连接失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 格式化Markdown消息
   */
  private formatMarkdownMessage(data: {
    title: string;
    message: string;
    type: string;
    metadata?: any;
  }): string {
    const emoji = this.getEmojiByType(data.type);
    const color = this.getColorByType(data.type);
    
    let markdown = `## <font color="${color}">${emoji} ${data.title}</font>\n\n`;
    markdown += `${data.message}\n\n`;

    if (data.metadata) {
      markdown += '**详细信息:**\n\n';
      for (const [key, value] of Object.entries(data.metadata)) {
        markdown += `> **${key}:** ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
      }
    }

    markdown += `\n---\n*发送时间: ${new Date().toLocaleString('zh-CN')}*`;

    return markdown;
  }

  /**
   * 根据类型获取表情符号
   */
  private getEmojiByType(type: string): string {
    const emojis = {
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
      success: '✅',
    };

    return emojis[type] || emojis.info;
  }

  /**
   * 根据类型获取颜色
   */
  private getColorByType(type: string): string {
    const colors = {
      info: 'info',
      warning: 'warning',
      error: 'comment',
      success: 'info',
    };

    return colors[type] || colors.info;
  }
}
