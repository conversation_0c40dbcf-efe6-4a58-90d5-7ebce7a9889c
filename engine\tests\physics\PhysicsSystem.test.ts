/**
 * PhysicsSystem类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { PhysicsSystem } from '../../src/physics/PhysicsSystem';
import { PhysicsBody } from '../../src/physics/PhysicsBody';
import { PhysicsCollider } from '../../src/physics/PhysicsCollider';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { PhysicsBodyComponent } from '../../src/physics/components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from '../../src/physics/components/PhysicsColliderComponent';
import { BodyType } from '../../src/physics/types';
import { ColliderType } from '../../src/physics/types';
import { PhysicsRaycastResult } from '../../src/physics/PhysicsRaycastResult';

describe('PhysicsSystem', () => {
  let engine: Engine;
  let world: World;
  let physicsSystem: PhysicsSystem;
  
  // 在每个测试前创建一个新的物理系统
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({ autoStart: false });
    world = engine.getWorld();
    
    // 创建物理系统
    physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: false
    });
    
    // 添加物理系统到引擎
    engine.addSystem(physicsSystem);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后清理
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试物理系统初始化
  it('应该正确初始化物理系统', () => {
    expect(physicsSystem).toBeDefined();
    expect(physicsSystem['physicsWorld']).toBeDefined();
    expect(physicsSystem['physicsWorld'].gravity.y).toBeCloseTo(-9.82);
  });
  
  // 测试添加刚体
  it('应该能够添加刚体', () => {
    // 创建实体
    const entity = new Entity();
    entity.addComponent(new Transform());
    
    // 创建物理体组件
    const bodyComponent = new PhysicsBodyComponent(entity, {
      type: BodyType.DYNAMIC,
      mass: 1,
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      linearVelocity: { x: 0, y: 0, z: 0 },
      angularVelocity: { x: 0, y: 0, z: 0 },
      linearDamping: 0.01,
      angularDamping: 0.01,
      fixedRotation: false
    });
    
    // 创建碰撞体组件
    const colliderComponent = new PhysicsColliderComponent(entity, {
      type: ColliderType.BOX,
      size: { x: 1, y: 1, z: 1 },
      offset: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      isTrigger: false,
      material: {
        friction: 0.3,
        restitution: 0.3
      }
    });
    
    // 添加组件到实体
    entity.addComponent(bodyComponent);
    entity.addComponent(colliderComponent);
    
    // 添加实体到世界
    world.addEntity(entity);
    
    // 注册组件到物理系统
    physicsSystem.registerPhysicsBodyComponent(entity, bodyComponent);
    physicsSystem.registerPhysicsColliderComponent(entity, colliderComponent);
    
    // 验证组件已注册
    expect(physicsSystem['bodies'].has(entity.id)).toBe(true);
    expect(physicsSystem['colliders'].has(entity.id)).toBe(true);
    
    // 验证物理世界中的刚体数量
    expect(physicsSystem['physicsWorld'].bodies.length).toBe(1);
  });
  
  // 测试射线检测
  it('应该能够执行射线检测', () => {
    // 创建实体
    const entity = new Entity();
    entity.addComponent(new Transform());
    
    // 创建物理体组件
    const bodyComponent = new PhysicsBodyComponent(entity, {
      type: BodyType.DYNAMIC,
      mass: 1,
      position: { x: 0, y: 0, z: 0 }
    });
    
    // 创建碰撞体组件
    const colliderComponent = new PhysicsColliderComponent(entity, {
      type: ColliderType.BOX,
      size: { x: 1, y: 1, z: 1 }
    });
    
    // 添加组件到实体
    entity.addComponent(bodyComponent);
    entity.addComponent(colliderComponent);
    
    // 添加实体到世界
    world.addEntity(entity);
    
    // 注册组件到物理系统
    physicsSystem.registerPhysicsBodyComponent(entity, bodyComponent);
    physicsSystem.registerPhysicsColliderComponent(entity, colliderComponent);
    
    // 创建射线
    const rayOrigin = new THREE.Vector3(0, 5, 0);
    const rayDirection = new THREE.Vector3(0, -1, 0);
    
    // 执行射线检测
    const results = physicsSystem.raycast(rayOrigin, rayDirection, 10);
    
    // 验证射线检测结果
    expect(results.length).toBe(1);
    expect(results[0].entity).toBe(entity);
    expect(results[0].distance).toBeGreaterThan(0);
    expect(results[0].point.y).toBeCloseTo(0.5); // 碰撞点应该在盒子表面
  });
  
  // 测试物理更新
  it('应该更新物理世界', () => {
    // 创建实体
    const entity = new Entity();
    entity.addComponent(new Transform());
    
    // 创建物理体组件
    const bodyComponent = new PhysicsBodyComponent(entity, {
      type: BodyType.DYNAMIC,
      mass: 1,
      position: { x: 0, y: 10, z: 0 } // 从高处开始
    });
    
    // 创建碰撞体组件
    const colliderComponent = new PhysicsColliderComponent(entity, {
      type: ColliderType.SPHERE,
      radius: 0.5
    });
    
    // 添加组件到实体
    entity.addComponent(bodyComponent);
    entity.addComponent(colliderComponent);
    
    // 添加实体到世界
    world.addEntity(entity);
    
    // 注册组件到物理系统
    physicsSystem.registerPhysicsBodyComponent(entity, bodyComponent);
    physicsSystem.registerPhysicsColliderComponent(entity, colliderComponent);
    
    // 记录初始位置
    const initialY = bodyComponent.getPosition().y;
    
    // 执行物理更新
    physicsSystem.fixedUpdate(1/60);
    
    // 验证物体受到重力影响
    const newY = bodyComponent.getPosition().y;
    expect(newY).toBeLessThan(initialY);
  });
  
  // 测试碰撞检测
  it('应该检测碰撞', () => {
    // 创建地面实体
    const groundEntity = new Entity();
    groundEntity.addComponent(new Transform({
      position: { x: 0, y: 0, z: 0 }
    }));
    
    // 创建地面物理体组件
    const groundBodyComponent = new PhysicsBodyComponent(groundEntity, {
      type: BodyType.STATIC,
      mass: 0,
      position: { x: 0, y: 0, z: 0 }
    });
    
    // 创建地面碰撞体组件
    const groundColliderComponent = new PhysicsColliderComponent(groundEntity, {
      type: ColliderType.BOX,
      size: { x: 10, y: 0.1, z: 10 }
    });
    
    // 添加组件到地面实体
    groundEntity.addComponent(groundBodyComponent);
    groundEntity.addComponent(groundColliderComponent);
    
    // 添加地面实体到世界
    world.addEntity(groundEntity);
    
    // 注册地面组件到物理系统
    physicsSystem.registerPhysicsBodyComponent(groundEntity, groundBodyComponent);
    physicsSystem.registerPhysicsColliderComponent(groundEntity, groundColliderComponent);
    
    // 创建球体实体
    const sphereEntity = new Entity();
    sphereEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 0 }
    }));
    
    // 创建球体物理体组件
    const sphereBodyComponent = new PhysicsBodyComponent(sphereEntity, {
      type: BodyType.DYNAMIC,
      mass: 1,
      position: { x: 0, y: 5, z: 0 }
    });
    
    // 创建球体碰撞体组件
    const sphereColliderComponent = new PhysicsColliderComponent(sphereEntity, {
      type: ColliderType.SPHERE,
      radius: 0.5
    });
    
    // 添加组件到球体实体
    sphereEntity.addComponent(sphereBodyComponent);
    sphereEntity.addComponent(sphereColliderComponent);
    
    // 添加球体实体到世界
    world.addEntity(sphereEntity);
    
    // 注册球体组件到物理系统
    physicsSystem.registerPhysicsBodyComponent(sphereEntity, sphereBodyComponent);
    physicsSystem.registerPhysicsColliderComponent(sphereEntity, sphereColliderComponent);
    
    // 创建碰撞监听器
    const collisionStartSpy = vi.fn();
    const collisionEndSpy = vi.fn();
    
    // 添加碰撞监听器
    physicsSystem.on('collisionStart', collisionStartSpy);
    physicsSystem.on('collisionEnd', collisionEndSpy);
    
    // 执行多次物理更新，让球体落到地面
    for (let i = 0; i < 60; i++) {
      physicsSystem.fixedUpdate(1/60);
    }
    
    // 验证碰撞开始事件被触发
    expect(collisionStartSpy).toHaveBeenCalled();
  });
});
