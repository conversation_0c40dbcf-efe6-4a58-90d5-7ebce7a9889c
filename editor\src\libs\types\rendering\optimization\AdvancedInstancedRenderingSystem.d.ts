/**
 * 高级实例化渲染系统
 * 支持更多类型的实例化对象和高级渲染功能
 */
import * as THREE from 'three';
import { EnhancedInstancedRenderingSystem, EnhancedInstancedRenderingSystemOptions } from './EnhancedInstancedRenderingSystem';
/**
 * 高级实例数据接口
 */
export interface AdvancedInstanceData {
    /** 位置 */
    position: THREE.Vector3;
    /** 旋转（四元数） */
    quaternion: THREE.Quaternion;
    /** 缩放 */
    scale: THREE.Vector3;
    /** 颜色 */
    color?: THREE.Color;
    /** 自定义属性 */
    customAttributes?: Record<string, any>;
    /** 材质覆盖 */
    materialOverrides?: Record<string, any>;
    /** LOD配置 */
    lodConfig?: {
        /** 距离系数 */
        distanceFactor?: number;
        /** 是否启用 */
        enabled?: boolean;
    };
    /** 是否可见 */
    visible?: boolean;
    /** 是否投射阴影 */
    castShadow?: boolean;
    /** 是否接收阴影 */
    receiveShadow?: boolean;
    /** 是否使用物理 */
    usePhysics?: boolean;
    /** 物理属性 */
    physicsProperties?: {
        /** 质量 */
        mass?: number;
        /** 是否是静态的 */
        isStatic?: boolean;
        /** 碰撞形状 */
        collisionShape?: 'box' | 'sphere' | 'capsule' | 'cylinder' | 'convexHull' | 'mesh';
        /** 摩擦系数 */
        friction?: number;
        /** 恢复系数 */
        restitution?: number;
    };
    /** 用户数据 */
    userData?: any;
}
/**
 * 高级实例化渲染系统配置接口
 */
export interface AdvancedInstancedRenderingSystemOptions extends EnhancedInstancedRenderingSystemOptions {
    /** 是否支持自定义着色器 */
    supportCustomShaders?: boolean;
    /** 是否支持材质变体 */
    supportMaterialVariants?: boolean;
    /** 是否支持实例LOD */
    supportInstanceLOD?: boolean;
    /** 是否支持实例动画 */
    supportInstanceAnimation?: boolean;
    /** 是否支持实例物理 */
    supportInstancePhysics?: boolean;
    /** 是否使用高级剔除 */
    useAdvancedCulling?: boolean;
    /** 是否使用实例缓存 */
    useInstanceCache?: boolean;
    /** 是否使用实例合并 */
    useInstanceMerging?: boolean;
    /** 是否使用自动实例化 */
    useAutoInstancing?: boolean;
}
/**
 * 材质变体接口
 */
export interface MaterialVariant {
    /** 变体ID */
    id: string;
    /** 原始材质 */
    originalMaterial: THREE.Material;
    /** 变体材质 */
    variantMaterial: THREE.Material;
    /** 属性映射 */
    propertyMap: Record<string, string>;
}
/**
 * 高级实例化渲染系统
 * 支持更多类型的实例化对象和高级渲染功能
 */
export declare class AdvancedInstancedRenderingSystem extends EnhancedInstancedRenderingSystem {
    /** 系统类型 */
    protected static readonly TYPE: string;
    /** 是否支持自定义着色器 */
    protected supportCustomShaders: boolean;
    /** 是否支持材质变体 */
    protected supportMaterialVariants: boolean;
    /** 是否支持实例LOD */
    protected supportInstanceLOD: boolean;
    /** 材质变体映射 */
    protected materialVariants: Map<string, MaterialVariant>;
    /** 自定义着色器映射 */
    protected customShaders: Map<string, THREE.ShaderMaterial>;
    /** 实例LOD映射 */
    protected instanceLODs: Map<string, THREE.InstancedMesh[]>;
    /**
     * 创建高级实例化渲染系统
     * @param options 系统选项
     */
    constructor(options?: AdvancedInstancedRenderingSystemOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 检查功能支持
     */
    private checkFeatureSupport;
    /**
     * 创建材质变体
     * @param originalMaterial 原始材质
     * @param variantProperties 变体属性
     * @returns 变体ID
     */
    createMaterialVariant(originalMaterial: THREE.Material, variantProperties: Record<string, any>): string;
    /**
     * 创建自定义着色器
     * @param name 着色器名称
     * @param vertexShader 顶点着色器
     * @param fragmentShader 片段着色器
     * @param uniforms 统一变量
     * @returns 着色器ID
     */
    createCustomShader(name: string, vertexShader: string, fragmentShader: string, uniforms: Record<string, THREE.IUniform>): string;
    /**
     * 创建高级实例
     * @param geometry 几何体
     * @param material 材质
     * @param instanceData 实例数据
     * @returns 实例ID
     */
    createAdvancedInstance(geometry: THREE.BufferGeometry, material: THREE.Material, instanceData: AdvancedInstanceData): string;
    /**
     * 创建实例LOD
     * @param instanceId 实例ID
     * @param geometry 几何体
     * @param material 材质
     * @param instanceData 实例数据
     */
    private createInstanceLOD;
    /**
     * 简化几何体
     * @param geometry 几何体
     * @param ratio 简化比例
     * @returns 简化后的几何体
     */
    private simplifyGeometry;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
