<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>面部动画预设示例</title>
  <link rel="stylesheet" href="./style.css">
</head>
<body>
  <div class="app-container">
    <header>
      <h1>面部动画预设示例</h1>
      <div class="header-controls">
        <button id="loadModelBtn">加载模型</button>
        <select id="modelSelect">
          <option value="default">默认角色</option>
          <option value="female">女性角色</option>
          <option value="male">男性角色</option>
          <option value="cartoon">卡通角色</option>
        </select>
      </div>
    </header>
    
    <main>
      <div class="preview-container">
        <div id="preview"></div>
        <div class="preview-controls">
          <button id="playBtn" class="control-btn">播放</button>
          <button id="pauseBtn" class="control-btn">暂停</button>
          <button id="resetBtn" class="control-btn">重置</button>
        </div>
      </div>
      
      <div class="presets-container">
        <div class="tabs">
          <button class="tab-btn active" data-tab="presets">预设</button>
          <button class="tab-btn" data-tab="templates">模板</button>
        </div>
        
        <div class="tab-content active" id="presets-tab">
          <div class="filters">
            <div class="filter-group">
              <label>类型:</label>
              <select id="presetTypeSelect">
                <option value="standard">标准表情</option>
                <option value="cultural">文化特定表情</option>
                <option value="emotion_combo">情感组合</option>
                <option value="animation_sequence">动画序列</option>
                <option value="custom">自定义</option>
              </select>
            </div>
            
            <div class="filter-group" id="cultureFilterGroup">
              <label>文化:</label>
              <select id="cultureSelect">
                <option value="global">全球</option>
                <option value="chinese">中国</option>
                <option value="japanese">日本</option>
                <option value="american">美国</option>
              </select>
            </div>
            
            <div class="filter-group">
              <input type="text" id="searchPresets" placeholder="搜索预设...">
            </div>
          </div>
          
          <div class="presets-grid" id="presetsGrid"></div>
        </div>
        
        <div class="tab-content" id="templates-tab">
          <div class="filters">
            <div class="filter-group">
              <label>类别:</label>
              <select id="categorySelect">
                <option value="">全部</option>
                <option value="面部">面部</option>
                <option value="口型">口型</option>
                <option value="组合">组合</option>
              </select>
            </div>
            
            <div class="filter-group">
              <input type="text" id="searchTemplates" placeholder="搜索模板...">
            </div>
          </div>
          
          <div class="templates-grid" id="templatesGrid"></div>
        </div>
      </div>
    </main>
    
    <div class="modal" id="previewModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 id="modalTitle">预览</h2>
          <button class="close-btn">&times;</button>
        </div>
        <div class="modal-body">
          <div class="modal-preview" id="modalPreview"></div>
          <div class="modal-info">
            <h3 id="modalItemName"></h3>
            <p id="modalItemDescription"></p>
            <div id="modalItemTags" class="tags-container"></div>
            <div id="modalParameters" class="parameters-container"></div>
          </div>
        </div>
        <div class="modal-footer">
          <button id="applyBtn" class="primary-btn">应用</button>
          <button class="cancel-btn">取消</button>
        </div>
      </div>
    </div>
  </div>
  
  <script src="../../engine/dist/engine.js"></script>
  <script src="./main.js"></script>
</body>
</html>
