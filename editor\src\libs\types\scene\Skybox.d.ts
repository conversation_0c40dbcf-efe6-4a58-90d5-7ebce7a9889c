/**
 * 天空盒类
 * 创建和管理场景的天空盒
 */
import * as THREE from 'three';
export declare enum SkyboxType {
    CUBEMAP = "cubemap",
    EQUIRECTANGULAR = "equirectangular",
    PROCEDURAL = "procedural"
}
export interface SkyboxOptions {
    /** 天空盒类型 */
    type: SkyboxType;
    /** 立方体贴图路径（6个面的贴图路径） */
    cubemapPaths?: string[];
    /** 等距矩形贴图路径 */
    equirectangularPath?: string;
    /** 程序化天空盒参数 */
    proceduralParams?: {
        /** 顶部颜色 */
        topColor?: THREE.Color | string | number;
        /** 底部颜色 */
        bottomColor?: THREE.Color | string | number;
        /** 指数 */
        exponent?: number;
    };
    /** 是否旋转 */
    rotate?: boolean;
    /** 旋转速度 */
    rotationSpeed?: number;
}
export declare class Skybox {
    /** 天空盒类型 */
    private type;
    /** 天空盒网格 */
    private mesh;
    /** 天空盒材质 */
    private material;
    /** 天空盒几何体 */
    private geometry;
    /** 是否旋转 */
    private rotate;
    /** 旋转速度 */
    private rotationSpeed;
    /**
     * 创建天空盒实例
     * @param options 天空盒选项
     */
    constructor(options: SkyboxOptions);
    /**
     * 创建立方体贴图天空盒
     * @param paths 6个面的贴图路径（顺序：右、左、上、下、前、后）
     */
    private createCubemapSkybox;
    /**
     * 创建等距矩形贴图天空盒
     * @param path 等距矩形贴图路径
     */
    private createEquirectangularSkybox;
    /**
     * 创建程序化天空盒
     * @param params 程序化天空盒参数
     */
    private createProceduralSkybox;
    /**
     * 更新天空盒
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 设置旋转
     * @param rotate 是否旋转
     */
    setRotate(rotate: boolean): void;
    /**
     * 设置旋转速度
     * @param speed 旋转速度
     */
    setRotationSpeed(speed: number): void;
    /**
     * 获取天空盒网格
     * @returns 天空盒网格
     */
    getMesh(): THREE.Mesh | null;
    /**
     * 获取天空盒类型
     * @returns 天空盒类型
     */
    getType(): SkyboxType;
    /**
     * 销毁天空盒
     */
    dispose(): void;
}
