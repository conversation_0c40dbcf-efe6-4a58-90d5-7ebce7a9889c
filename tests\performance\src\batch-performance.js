/**
 * 批处理性能测试
 * 测试批处理服务的性能
 */
import { check } from 'k6';
import http from 'k6/http';
import { sleep } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';
import { CONFIG, randomSleep } from './config.js';

// 性能指标
const batchSize = new Trend('batch_size');
const batchTime = new Trend('batch_time');
const requestLatency = new Trend('request_latency');
const requestsPerSecond = new Rate('requests_per_second');
const errorRate = new Rate('error_rate');

// 测试数据
const testData = {
  items: [],
};

// 初始化函数 - 在测试开始前运行一次
export function setup() {
  // 生成测试项目
  for (let i = 0; i < 1000; i++) {
    testData.items.push({
      id: `item-${i}`,
      value: `value-${i}`,
    });
  }

  return testData;
}

// 默认函数 - 在测试期间为每个虚拟用户运行
export default function(data) {
  // 随机选择一个项目
  const itemIndex = Math.floor(Math.random() * data.items.length);
  const item = data.items[itemIndex];
  
  // 发送请求，测试批处理性能
  const startTime = Date.now();
  
  const res = http.post(`${CONFIG.apiGateway}/api/batch-test`, JSON.stringify(item), {
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  const duration = Date.now() - startTime;
  
  // 记录请求延迟
  requestLatency.add(duration);
  
  // 记录请求成功/失败
  errorRate.add(res.status !== 200);
  
  // 记录每秒请求数
  requestsPerSecond.add(1);
  
  // 检查响应
  check(res, {
    '请求成功': (r) => r.status === 200,
  });
  
  // 记录批处理大小
  if (res.headers['X-Batch-Size']) {
    batchSize.add(parseInt(res.headers['X-Batch-Size']));
  }
  
  // 记录批处理时间
  if (res.headers['X-Batch-Time']) {
    batchTime.add(parseFloat(res.headers['X-Batch-Time']));
  }
  
  // 短暂休息
  randomSleep(0.01, 0.1);
}

// 测试配置
export const options = {
  scenarios: {
    // 低并发测试
    low_concurrency: {
      executor: 'constant-vus',
      vus: 10,
      duration: '30s',
    },
    // 中等并发测试
    medium_concurrency: {
      executor: 'constant-vus',
      vus: 50,
      duration: '30s',
      startTime: '30s',
    },
    // 高并发测试
    high_concurrency: {
      executor: 'constant-vus',
      vus: 100,
      duration: '30s',
      startTime: '1m',
    },
    // 突发测试
    burst: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '10s', target: 200 },
        { duration: '20s', target: 200 },
        { duration: '10s', target: 0 },
      ],
      startTime: '1m30s',
    },
  },
  thresholds: {
    'request_latency': ['p(95)<200'], // 95%的请求延迟小于200ms
    'requests_per_second': ['avg>200'], // 平均每秒请求数大于200
    'error_rate': ['rate<0.01'], // 错误率小于1%
    'batch_size': ['avg>10'], // 平均批处理大小大于10
  },
};
