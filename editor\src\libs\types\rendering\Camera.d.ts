/**
 * 相机类
 * 表示场景中的相机
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
export declare enum CameraType {
    PERSPECTIVE = "perspective",
    ORTHOGRAPHIC = "orthographic"
}
export interface CameraOptions {
    /** 相机类型 */
    type?: CameraType;
    /** 视野角度（透视相机） */
    fov?: number;
    /** 近裁剪面 */
    near?: number;
    /** 远裁剪面 */
    far?: number;
    /** 宽高比 */
    aspect?: number;
    /** 左平面（正交相机） */
    left?: number;
    /** 右平面（正交相机） */
    right?: number;
    /** 上平面（正交相机） */
    top?: number;
    /** 下平面（正交相机） */
    bottom?: number;
    /** 是否自动更新宽高比 */
    autoAspect?: boolean;
}
export declare class Camera extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 相机类型 */
    private cameraType;
    /** Three.js相机 */
    private camera;
    /** 是否自动更新宽高比 */
    private autoAspect;
    /** 调整大小监听器 */
    private resizeListener;
    /**
     * 创建相机组件
     * @param options 相机选项
     */
    constructor(options?: CameraOptions);
    /**
     * 当组件附加到实体时调用
     */
    protected onAttach(): void;
    /**
     * 处理窗口调整大小事件
     */
    private handleResize;
    /**
     * 更新宽高比
     */
    private updateAspect;
    /**
     * 设置视野角度（透视相机）
     * @param fov 视野角度
     */
    setFov(fov: number): void;
    /**
     * 获取视野角度（透视相机）
     * @returns 视野角度
     */
    getFov(): number;
    /**
     * 设置宽高比（透视相机）
     * @param aspect 宽高比
     */
    setAspect(aspect: number): void;
    /**
     * 获取宽高比（透视相机）
     * @returns 宽高比
     */
    getAspect(): number;
    /**
     * 设置近裁剪面
     * @param near 近裁剪面
     */
    setNear(near: number): void;
    /**
     * 获取近裁剪面
     * @returns 近裁剪面
     */
    getNear(): number;
    /**
     * 设置远裁剪面
     * @param far 远裁剪面
     */
    setFar(far: number): void;
    /**
     * 获取远裁剪面
     * @returns 远裁剪面
     */
    getFar(): number;
    /**
     * 设置正交相机参数
     * @param left 左平面
     * @param right 右平面
     * @param top 上平面
     * @param bottom 下平面
     */
    setOrthographicSize(left: number, right: number, top: number, bottom: number): void;
    /**
     * 设置正交相机缩放
     * @param zoom 缩放
     */
    setZoom(zoom: number): void;
    /**
     * 获取正交相机缩放
     * @returns 缩放
     */
    getZoom(): number;
    /**
     * 获取相机类型
     * @returns 相机类型
     */
    getType(): CameraType;
    /**
     * 获取Three.js相机
     * @returns Three.js相机
     */
    getThreeCamera(): THREE.Camera;
    /**
     * 获取视图矩阵
     * @returns 视图矩阵
     */
    getViewMatrix(): THREE.Matrix4;
    /**
     * 获取投影矩阵
     * @returns 投影矩阵
     */
    getProjectionMatrix(): THREE.Matrix4;
    /**
     * 获取视图投影矩阵
     * @returns 视图投影矩阵
     */
    getViewProjectionMatrix(): THREE.Matrix4;
    /**
     * 获取世界位置
     * @returns 世界位置
     */
    getPosition(): THREE.Vector3;
    /**
     * 获取前方向
     * @returns 前方向
     */
    getForward(): THREE.Vector3;
    /**
     * 获取右方向
     * @returns 右方向
     */
    getRight(): THREE.Vector3;
    /**
     * 获取上方向
     * @returns 上方向
     */
    getUp(): THREE.Vector3;
    /**
     * 设置自动更新宽高比
     * @param autoAspect 是否自动更新宽高比
     */
    setAutoAspect(autoAspect: boolean): void;
    /**
     * 是否自动更新宽高比
     * @returns 是否自动更新宽高比
     */
    isAutoAspect(): boolean;
    /**
     * 销毁组件
     */
    dispose(): void;
}
