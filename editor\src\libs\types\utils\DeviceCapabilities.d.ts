/**
 * 设备能力检测
 * 用于检测设备的性能和功能支持情况
 */
import * as THREE from 'three';
/**
 * 设备性能级别枚举
 */
export declare enum DevicePerformanceLevel {
    /** 低性能 */
    LOW = 0,
    /** 中等性能 */
    MEDIUM = 1,
    /** 高性能 */
    HIGH = 2
}
/**
 * 设备能力检测配置接口
 */
export interface DeviceCapabilitiesOptions {
    /** 是否强制使用低性能模式 */
    forceLowPerformance?: boolean;
    /** 是否强制使用高性能模式 */
    forceHighPerformance?: boolean;
    /** 是否启用电池监控 */
    enableBatteryMonitoring?: boolean;
    /** 是否启用温度监控 */
    enableTemperatureMonitoring?: boolean;
    /** 是否启用网络监控 */
    enableNetworkMonitoring?: boolean;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
    /** 是否启用自动性能调整 */
    enableAutoPerformanceAdjustment?: boolean;
    /** 低电量阈值（百分比） */
    lowBatteryThreshold?: number;
    /** 高温阈值（摄氏度） */
    highTemperatureThreshold?: number;
    /** 目标帧率 */
    targetFPS?: number;
    /** 最小可接受帧率 */
    minAcceptableFPS?: number;
}
/**
 * 设备能力检测类
 * 单例模式
 */
export declare class DeviceCapabilities {
    /** 单例实例 */
    private static instance;
    /** 设备性能级别 */
    private performanceLevel;
    /** 是否支持WebGL2 */
    private supportsWebGL2;
    /** 是否支持浮点纹理 */
    private supportsFloatTextures;
    /** 是否支持各向异性过滤 */
    private supportsAnisotropy;
    /** 是否支持实例化渲染 */
    private supportsInstancing;
    /** 是否支持压缩纹理 */
    private supportsCompressedTextures;
    /** 是否支持HDR */
    private supportsHDR;
    /** 是否支持阴影 */
    private supportsShadows;
    /** 最大纹理大小 */
    private maxTextureSize;
    /** 最大各向异性级别 */
    private maxAnisotropy;
    /** 是否是移动设备 */
    private isMobile;
    /** 是否是VR/AR设备 */
    private isXRDevice;
    /** 是否强制使用低性能模式 */
    private forceLowPerformance;
    /** 是否强制使用高性能模式 */
    private forceHighPerformance;
    /** 是否启用电池监控 */
    private enableBatteryMonitoring;
    /** 是否启用温度监控 */
    private enableTemperatureMonitoring;
    /** 是否启用网络监控 */
    private enableNetworkMonitoring;
    /** 是否启用性能监控 */
    private enablePerformanceMonitoring;
    /** 是否启用自动性能调整 */
    private enableAutoPerformanceAdjustment;
    /** 低电量阈值（百分比） */
    private lowBatteryThreshold;
    /** 高温阈值（摄氏度） */
    private highTemperatureThreshold;
    /** 目标帧率 */
    private targetFPS;
    /** 最小可接受帧率 */
    private minAcceptableFPS;
    /** 电池电量 */
    private batteryLevel;
    /** 电池是否正在充电 */
    private batteryCharging;
    /** 设备温度 */
    private temperature;
    /** 当前帧率 */
    private currentFPS;
    /** CPU使用率 */
    private cpuUsage;
    /** GPU使用率 */
    private gpuUsage;
    /** 内存使用率 */
    private memoryUsage;
    /** 渲染器 */
    private renderer;
    /**
     * 获取单例实例
     * @returns DeviceCapabilities实例
     */
    static getInstance(options?: DeviceCapabilitiesOptions): DeviceCapabilities;
    /**
     * 创建设备能力检测
     * @param options 设备能力检测配置
     */
    private constructor();
    /**
     * 初始化设备能力检测
     * @param renderer Three.js渲染器
     */
    initialize(renderer: THREE.WebGLRenderer): void;
    /**
     * 获取设备性能级别
     * @returns 设备性能级别
     */
    getPerformanceLevel(): DevicePerformanceLevel;
    /**
     * 是否是低性能设备
     * @returns 是否是低性能设备
     */
    isLowPerformanceDevice(): boolean;
    /**
     * 是否是高性能设备
     * @returns 是否是高性能设备
     */
    isHighPerformanceDevice(): boolean;
    /**
     * 是否支持WebGL2
     * @returns 是否支持WebGL2
     */
    isWebGL2Supported(): boolean;
    /**
     * 是否支持浮点纹理
     * @returns 是否支持浮点纹理
     */
    isFloatTexturesSupported(): boolean;
    /**
     * 是否支持各向异性过滤
     * @returns 是否支持各向异性过滤
     */
    isAnisotropySupported(): boolean;
    /**
     * 获取最大各向异性级别
     * @returns 最大各向异性级别
     */
    getMaxAnisotropy(): number;
    /**
     * 是否支持实例化渲染
     * @returns 是否支持实例化渲染
     */
    isInstancingSupported(): boolean;
    /**
     * 是否支持压缩纹理
     * @returns 是否支持压缩纹理
     */
    isCompressedTexturesSupported(): boolean;
    /**
     * 是否支持HDR
     * @returns 是否支持HDR
     */
    isHDRSupported(): boolean;
    /**
     * 是否支持阴影
     * @returns 是否支持阴影
     */
    isShadowsSupported(): boolean;
    /**
     * 获取最大纹理大小
     * @returns 最大纹理大小
     */
    getMaxTextureSize(): number;
    /**
     * 是否是移动设备
     * @returns 是否是移动设备
     */
    isMobileDevice(): boolean;
    /**
     * 是否是VR/AR设备
     * @returns 是否是VR/AR设备
     */
    isXRDeviceEnabled(): boolean;
    /**
     * 设置是否强制使用低性能模式
     * @param force 是否强制
     */
    setForceLowPerformance(force: boolean): void;
    /**
     * 设置是否强制使用高性能模式
     * @param force 是否强制
     */
    setForceHighPerformance(force: boolean): void;
    /**
     * 检测是否是移动设备
     * @returns 是否是移动设备
     */
    private detectMobileDevice;
    /**
     * 检测是否是VR/AR设备
     * @returns 是否是VR/AR设备
     */
    private detectXRDevice;
    /**
     * 确定设备性能级别
     * @returns 设备性能级别
     */
    private determinePerformanceLevel;
    /**
     * 初始化电池监控
     */
    private initializeBatteryMonitoring;
    /**
     * 更新电池状态
     * @param battery 电池对象
     */
    private updateBatteryStatus;
    /**
     * 初始化温度监控
     */
    private initializeTemperatureMonitoring;
    /**
     * 初始化网络监控
     */
    private initializeNetworkMonitoring;
    /**
     * 初始化性能监控
     */
    private initializePerformanceMonitoring;
    /**
     * 调整性能级别
     */
    private adjustPerformanceLevel;
    /**
     * 获取电池电量
     * @returns 电池电量（百分比）
     */
    getBatteryLevel(): number;
    /**
     * 获取电池是否正在充电
     * @returns 电池是否正在充电
     */
    isBatteryCharging(): boolean;
    /**
     * 获取设备温度
     * @returns 设备温度（摄氏度）
     */
    getTemperature(): number;
    /**
     * 获取当前帧率
     * @returns 当前帧率
     */
    getCurrentFPS(): number;
    /**
     * 获取CPU使用率
     * @returns CPU使用率（0-1）
     */
    getCPUUsage(): number;
    /**
     * 获取GPU使用率
     * @returns GPU使用率（0-1）
     */
    getGPUUsage(): number;
    /**
     * 获取内存使用率
     * @returns 内存使用率（0-1）
     */
    getMemoryUsage(): number;
    /**
     * 设置性能级别
     * @param level 性能级别
     */
    setPerformanceLevel(level: DevicePerformanceLevel): void;
    /**
     * 设置自动性能调整
     * @param enable 是否启用
     */
    setAutoPerformanceAdjustment(enable: boolean): void;
    /**
     * 设置低电量阈值
     * @param threshold 阈值（百分比）
     */
    setLowBatteryThreshold(threshold: number): void;
    /**
     * 设置高温阈值
     * @param threshold 阈值（摄氏度）
     */
    setHighTemperatureThreshold(threshold: number): void;
    /**
     * 设置目标帧率
     * @param fps 帧率
     */
    setTargetFPS(fps: number): void;
    /**
     * 设置最小可接受帧率
     * @param fps 帧率
     */
    setMinAcceptableFPS(fps: number): void;
}
