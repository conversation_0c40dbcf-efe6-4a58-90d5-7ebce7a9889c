/**
 * 数据可视化示例 - 主脚本
 */

import { Engine, World, Entity, Scene, Renderer, Camera, Transform, Mesh, Material, Light, PhysicsSystem, UISystem } from '/engine/dist/index.js';
import { createBarChart, createScatterPlot, createHeatMap, createGeoMap } from './visualizations.js';

// 全局变量
let engine, world, scene, camera;
let currentVisualization = 'barChart';
let currentDataset = 'population';
let currentYear = '2020';
let dataEntities = new Map();
let isPlaying = false;
let playInterval = null;

// 模拟数据
const DATA = {
  population: {
    1990: generateRandomData(30, 100000, 1500000, '人口'),
    2000: generateRandomData(30, 200000, 2000000, '人口'),
    2010: generateRandomData(30, 300000, 2500000, '人口'),
    2020: generateRandomData(30, 400000, 3000000, '人口'),
  },
  economy: {
    1990: generateRandomData(30, 1000, 10000, '经济'),
    2000: generateRandomData(30, 5000, 20000, '经济'),
    2010: generateRandomData(30, 10000, 50000, '经济'),
    2020: generateRandomData(30, 20000, 80000, '经济'),
  },
  climate: {
    1990: generateRandomData(30, 10, 30, '气温'),
    2000: generateRandomData(30, 12, 32, '气温'),
    2010: generateRandomData(30, 14, 34, '气温'),
    2020: generateRandomData(30, 16, 36, '气温'),
  }
};

// 配置选项
const visualizationOptions = {
  barChart: {
    width: 20,
    depth: 20,
    heightScale: 1,
    spacing: 0.2,
    colorScheme: 'default',
    showLabels: true,
  },
  scatterPlot: {
    width: 20,
    height: 10,
    depth: 20,
    pointSize: 0.5,
    colorScheme: 'rainbow',
    showLabels: true,
  },
  heatMap: {
    width: 20,
    depth: 20,
    heightScale: 0.2,
    resolution: 20,
    colorScheme: 'heatmap',
    showLabels: false,
  },
  geoMap: {
    width: 20,
    depth: 20,
    heightScale: 1,
    pointSize: 0.3,
    colorScheme: 'blueRed',
    showLabels: true,
  }
};

/**
 * 生成随机数据
 * @param {number} count - 数据点数量
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @param {string} type - 数据类型
 * @returns {Array} 随机数据数组
 */
function generateRandomData(count, min, max, type) {
  const data = [];
  for (let i = 0; i < count; i++) {
    data.push({
      id: `region-${i}`,
      name: `区域 ${i + 1}`,
      value: Math.floor(Math.random() * (max - min) + min),
      color: `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`,
      position: {
        x: (Math.random() - 0.5) * 20,
        z: (Math.random() - 0.5) * 20,
      },
      type: type,
    });
  }
  return data;
}

/**
 * 初始化引擎和场景
 */
function initEngine() {
  // 创建引擎
  engine = new Engine({
    container: document.getElementById('scene-container'),
    antialias: true,
    shadows: true,
  });

  // 创建世界
  world = new World(engine);

  // 创建场景
  scene = new Scene(world, {
    name: '数据可视化示例',
    background: { type: 'color', value: '#1a1a2e' },
  });

  // 创建相机
  camera = new Entity(world)
    .addComponent(new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000,
    }))
    .addComponent(new Transform({
      position: { x: 0, y: 20, z: 30 },
      rotation: { x: -0.5, y: 0, z: 0 },
    }));

  // 创建地面
  const ground = new Entity(world)
    .addComponent(new Transform({
      position: { x: 0, y: 0, z: 0 },
      scale: { x: 30, y: 0.1, z: 30 },
    }))
    .addComponent(new Mesh({
      geometry: { type: 'plane' },
    }))
    .addComponent(new Material({
      type: 'standard',
      color: '#16213e',
      roughness: 0.8,
      metalness: 0.2,
    }));

  // 创建网格
  const grid = new Entity(world)
    .addComponent(new Transform({
      position: { x: 0, y: 0.1, z: 0 },
      rotation: { x: Math.PI / 2, y: 0, z: 0 },
      scale: { x: 30, y: 30, z: 1 },
    }))
    .addComponent(new Mesh({
      geometry: { type: 'grid', size: 30, divisions: 30 },
    }))
    .addComponent(new Material({
      type: 'basic',
      color: '#4a5568',
      wireframe: true,
    }));

  // 创建方向光
  const directionalLight = new Entity(world)
    .addComponent(new Transform({
      position: { x: 10, y: 20, z: 10 },
      rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
    }))
    .addComponent(new Light({
      type: 'directional',
      color: '#ffffff',
      intensity: 1,
      castShadow: true,
      shadowMapSize: 2048,
    }));

  // 创建环境光
  const ambientLight = new Entity(world)
    .addComponent(new Light({
      type: 'ambient',
      color: '#ffffff',
      intensity: 0.3,
    }));

  // 添加实体到场景
  scene.addEntity(camera);
  scene.addEntity(ground);
  scene.addEntity(grid);
  scene.addEntity(directionalLight);
  scene.addEntity(ambientLight);

  // 设置当前场景和相机
  world.setActiveScene(scene);
  world.setActiveCamera(camera);

  // 启动引擎
  engine.start();
}

/**
 * 更新可视化
 */
function updateVisualization() {
  // 清除现有数据实体
  dataEntities.forEach(entity => {
    world.removeEntity(entity);
  });
  dataEntities.clear();

  // 获取选定的数据集
  const dataset = DATA[currentDataset][currentYear];

  // 获取可视化选项
  const options = {
    ...visualizationOptions[currentVisualization],
    heightScale: parseFloat(document.getElementById('heightSlider').value),
    showLabels: document.getElementById('show-labels').checked,
    colorScheme: document.getElementById('colorScheme').value,
  };

  // 根据可视化类型创建实体
  switch (currentVisualization) {
    case 'barChart':
      dataEntities = createBarChart(world, dataset, options);
      break;
    case 'scatterPlot':
      dataEntities = createScatterPlot(world, dataset, options);
      break;
    case 'heatMap':
      dataEntities = createHeatMap(world, dataset, options);
      break;
    case 'geoMap':
      dataEntities = createGeoMap(world, dataset, options);
      break;
  }

  // 将实体添加到场景
  dataEntities.forEach(entity => {
    scene.addEntity(entity);
  });

  // 更新统计信息
  updateStatistics(dataset);
}

/**
 * 更新统计信息
 * @param {Array} dataset - 数据集
 */
function updateStatistics(dataset) {
  // 计算统计信息
  const values = dataset.map(item => item.value);
  const count = values.length;
  const sum = values.reduce((a, b) => a + b, 0);
  const avg = sum / count;
  const min = Math.min(...values);
  const max = Math.max(...values);

  // 更新UI
  document.getElementById('dataPointCount').textContent = count;
  document.getElementById('averageValue').textContent = Math.round(avg).toLocaleString();
  document.getElementById('maxDataValue').textContent = max.toLocaleString();
  document.getElementById('minDataValue').textContent = min.toLocaleString();

  // 更新数据统计面板
  const statsHtml = `
    <p><strong>数据类型:</strong> ${dataset[0].type}</p>
    <p><strong>数据点数:</strong> ${count}</p>
    <p><strong>平均值:</strong> ${Math.round(avg).toLocaleString()}</p>
    <p><strong>最大值:</strong> ${max.toLocaleString()}</p>
    <p><strong>最小值:</strong> ${min.toLocaleString()}</p>
    <p><strong>总和:</strong> ${sum.toLocaleString()}</p>
    <p><strong>年份:</strong> ${currentYear}</p>
  `;
  document.getElementById('dataStats').innerHTML = statsHtml;
}

/**
 * 初始化UI和事件处理
 */
function initUI() {
  // 可视化类型选择
  document.querySelectorAll('input[name="visualType"]').forEach(radio => {
    radio.addEventListener('change', (e) => {
      currentVisualization = e.target.value;
      updateVisualization();
    });
  });

  // 数据集选择
  document.getElementById('datasetSelect').addEventListener('change', (e) => {
    currentDataset = e.target.value;
    updateVisualization();
  });

  // 时间滑块
  document.getElementById('timeSlider').addEventListener('input', (e) => {
    const yearIndex = parseInt(e.target.value);
    const years = ['1990', '2000', '2010', '2020'];
    currentYear = years[yearIndex];
    updateVisualization();
  });

  // 比例滑块
  document.getElementById('scaleSlider').addEventListener('input', (e) => {
    document.getElementById('scaleValue').textContent = e.target.value;
    // 更新场景中所有数据实体的缩放
    dataEntities.forEach(entity => {
      const transform = entity.getComponent(Transform);
      if (transform) {
        const scale = transform.scale;
        transform.scale = {
          x: scale.x * parseFloat(e.target.value) / parseFloat(document.getElementById('scaleValue').textContent),
          y: scale.y,
          z: scale.z * parseFloat(e.target.value) / parseFloat(document.getElementById('scaleValue').textContent)
        };
      }
    });
  });

  // 高度滑块
  document.getElementById('heightSlider').addEventListener('input', (e) => {
    document.getElementById('heightValue').textContent = e.target.value;
    updateVisualization();
  });

  // 颜色方案选择
  document.getElementById('colorScheme').addEventListener('change', () => {
    updateVisualization();
  });

  // 显示标签选择
  document.getElementById('show-labels').addEventListener('change', () => {
    updateVisualization();
  });

  // 播放按钮
  document.getElementById('playBtn').addEventListener('click', () => {
    if (!isPlaying) {
      isPlaying = true;
      playInterval = setInterval(() => {
        const timeSlider = document.getElementById('timeSlider');
        let value = parseInt(timeSlider.value);
        value = (value + 1) % 4;
        timeSlider.value = value;
        
        const years = ['1990', '2000', '2010', '2020'];
        currentYear = years[value];
        updateVisualization();
      }, 2000);
    }
  });

  // 暂停按钮
  document.getElementById('pauseBtn').addEventListener('click', () => {
    if (isPlaying) {
      isPlaying = false;
      clearInterval(playInterval);
    }
  });

  // 重置按钮
  document.getElementById('resetBtn').addEventListener('click', () => {
    document.getElementById('timeSlider').value = 0;
    currentYear = '1990';
    updateVisualization();
  });

  // 导入按钮
  document.getElementById('importBtn').addEventListener('click', () => {
    document.getElementById('importDialog').style.display = 'flex';
  });

  // 导出按钮
  document.getElementById('exportBtn').addEventListener('click', () => {
    document.getElementById('exportDialog').style.display = 'flex';
  });

  // 帮助按钮
  document.getElementById('helpBtn').addEventListener('click', () => {
    document.getElementById('helpDialog').style.display = 'flex';
  });

  // 对话框关闭按钮
  document.querySelectorAll('.dialog .btn').forEach(btn => {
    if (btn.id.includes('Cancel') || btn.id.includes('Close')) {
      btn.addEventListener('click', () => {
        btn.closest('.dialog').style.display = 'none';
      });
    }
  });

  // 隐藏加载覆盖层
  setTimeout(() => {
    document.getElementById('loading-overlay').style.display = 'none';
  }, 1000);
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  initEngine();
  initUI();
  updateVisualization();
});

// 导出全局函数
window.updateVisualization = updateVisualization;
