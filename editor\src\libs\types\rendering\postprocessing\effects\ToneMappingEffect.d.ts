import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 色调映射类型
 */
export declare enum ToneMappingType {
    /** 线性 */
    Linear = "Linear",
    /** Reinhard */
    Reinhard = "Reinhard",
    /** Cineon */
    Cineon = "Cineon",
    /** ACESFilmic */
    ACESFilmic = "ACESFilmic",
    /** 自定义 */
    Custom = "Custom"
}
/**
 * 色调映射效果选项
 */
export interface ToneMappingEffectOptions extends PostProcessingEffectOptions {
    /** 色调映射类型 */
    type?: ToneMappingType;
    /** 曝光 */
    exposure?: number;
    /** 伽马 */
    gamma?: number;
    /** 对比度 */
    contrast?: number;
    /** 饱和度 */
    saturation?: number;
    /** 亮度 */
    brightness?: number;
}
/**
 * 色调映射效果
 */
export declare class ToneMappingEffect extends PostProcessingEffect {
    /** 色调映射类型 */
    private type;
    /** 曝光 */
    private exposure;
    /** 伽马 */
    private gamma;
    /** 对比度 */
    private contrast;
    /** 饱和度 */
    private saturation;
    /** 亮度 */
    private brightness;
    /** 色调映射通道 */
    private toneMappingPass;
    /**
     * 创建色调映射效果
     * @param options 色调映射效果选项
     */
    constructor(options?: ToneMappingEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 获取色调映射类型值
     * @returns 色调映射类型值
     */
    private getToneMappingTypeValue;
    /**
     * 设置色调映射类型
     * @param type 色调映射类型
     */
    setType(type: ToneMappingType): void;
    /**
     * 获取色调映射类型
     * @returns 色调映射类型
     */
    getType(): ToneMappingType;
    /**
     * 设置曝光
     * @param exposure 曝光
     */
    setExposure(exposure: number): void;
    /**
     * 获取曝光
     * @returns 曝光
     */
    getExposure(): number;
    /**
     * 设置伽马
     * @param gamma 伽马
     */
    setGamma(gamma: number): void;
    /**
     * 获取伽马
     * @returns 伽马
     */
    getGamma(): number;
    /**
     * 设置对比度
     * @param contrast 对比度
     */
    setContrast(contrast: number): void;
    /**
     * 获取对比度
     * @returns 对比度
     */
    getContrast(): number;
    /**
     * 设置饱和度
     * @param saturation 饱和度
     */
    setSaturation(saturation: number): void;
    /**
     * 获取饱和度
     * @returns 饱和度
     */
    getSaturation(): number;
    /**
     * 设置亮度
     * @param brightness 亮度
     */
    setBrightness(brightness: number): void;
    /**
     * 获取亮度
     * @returns 亮度
     */
    getBrightness(): number;
    /**
     * 更新效果
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
}
