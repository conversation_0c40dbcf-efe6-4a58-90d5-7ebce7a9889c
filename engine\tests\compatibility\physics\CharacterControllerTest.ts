/**
 * 角色控制器兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 角色控制器兼容性测试
 */
export const characterControllerTest: TestCase = {
  name: '角色控制器兼容性测试',
  description: '测试角色控制器功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目物理系统实例
      const originalPhysicsSystem = new original.PhysicsSystem();
      originalPhysicsSystem.initialize();
      
      // 创建重构后项目物理系统实例
      const refactoredPhysicsSystem = new refactored.PhysicsSystem();
      refactoredPhysicsSystem.initialize();
      
      // 创建原有项目实体
      const originalEntity = new original.Entity('Character');
      
      // 创建重构后项目实体
      const refactoredEntity = new refactored.Entity('Character');
      
      // 创建原有项目角色控制器组件
      const originalCharacterController = new original.CharacterController(originalEntity, {
        height: 1.8,
        radius: 0.3,
        stepHeight: 0.3,
        slopeLimit: 45
      });
      
      // 创建重构后项目角色控制器组件
      const refactoredCharacterController = new refactored.CharacterController(refactoredEntity, {
        height: 1.8,
        radius: 0.3,
        stepHeight: 0.3,
        slopeLimit: 45
      });
      
      // 添加组件到实体
      originalEntity.addComponent(originalCharacterController);
      refactoredEntity.addComponent(refactoredCharacterController);
      
      // 注册组件到物理系统
      originalPhysicsSystem.registerCharacterController(originalEntity, originalCharacterController);
      refactoredPhysicsSystem.registerCharacterController(refactoredEntity, refactoredCharacterController);
      
      // 设置实体位置
      originalCharacterController.setPosition({ x: 0, y: 5, z: 0 });
      refactoredCharacterController.setPosition({ x: 0, y: 5, z: 0 });
      
      // 更新物理系统多次，让角色下落
      for (let i = 0; i < 100; i++) {
        originalPhysicsSystem.update(0.016);
        refactoredPhysicsSystem.update(0.016);
      }
      
      // 获取角色位置
      const originalPosition = originalCharacterController.getPosition();
      const refactoredPosition = refactoredCharacterController.getPosition();
      
      // 注意：由于物理引擎的实现差异，位置可能不完全相同，所以我们允许一定的误差
      if (
        Math.abs(originalPosition.x - refactoredPosition.x) > 0.5 ||
        Math.abs(originalPosition.z - refactoredPosition.z) > 0.5
      ) {
        return {
          name: '角色控制器兼容性测试',
          passed: false,
          errorMessage: `角色位置的x/z坐标相差过大: 原有项目=${JSON.stringify(originalPosition)}, 重构后项目=${JSON.stringify(refactoredPosition)}`,
          details: {
            originalPosition,
            refactoredPosition
          }
        };
      }
      
      // 测试角色移动
      const moveDirection = { x: 1, y: 0, z: 0 };
      originalCharacterController.move(moveDirection, 0.016);
      refactoredCharacterController.move(moveDirection, 0.016);
      
      // 更新物理系统
      originalPhysicsSystem.update(0.016);
      refactoredPhysicsSystem.update(0.016);
      
      // 获取角色移动后的位置
      const originalPositionAfterMove = originalCharacterController.getPosition();
      const refactoredPositionAfterMove = refactoredCharacterController.getPosition();
      
      // 检查角色是否移动
      const originalMoved = originalPositionAfterMove.x > originalPosition.x;
      const refactoredMoved = refactoredPositionAfterMove.x > refactoredPosition.x;
      
      if (originalMoved !== refactoredMoved) {
        return {
          name: '角色控制器兼容性测试',
          passed: false,
          errorMessage: `角色移动结果不一致: 原有项目=${originalMoved}, 重构后项目=${refactoredMoved}`,
          details: {
            originalPosition,
            originalPositionAfterMove,
            refactoredPosition,
            refactoredPositionAfterMove
          }
        };
      }
      
      // 测试角色跳跃
      originalCharacterController.jump();
      refactoredCharacterController.jump();
      
      // 更新物理系统
      originalPhysicsSystem.update(0.016);
      refactoredPhysicsSystem.update(0.016);
      
      // 获取角色跳跃后的位置
      const originalPositionAfterJump = originalCharacterController.getPosition();
      const refactoredPositionAfterJump = refactoredCharacterController.getPosition();
      
      // 检查角色是否跳跃
      const originalJumped = originalPositionAfterJump.y > originalPositionAfterMove.y;
      const refactoredJumped = refactoredPositionAfterJump.y > refactoredPositionAfterMove.y;
      
      if (originalJumped !== refactoredJumped) {
        return {
          name: '角色控制器兼容性测试',
          passed: false,
          errorMessage: `角色跳跃结果不一致: 原有项目=${originalJumped}, 重构后项目=${refactoredJumped}`,
          details: {
            originalPositionAfterMove,
            originalPositionAfterJump,
            refactoredPositionAfterMove,
            refactoredPositionAfterJump
          }
        };
      }
      
      // 销毁物理系统
      originalPhysicsSystem.destroy();
      refactoredPhysicsSystem.destroy();
      
      return {
        name: '角色控制器兼容性测试',
        passed: true,
        details: {
          originalMoved,
          refactoredMoved,
          originalJumped,
          refactoredJumped
        }
      };
    } catch (error) {
      return {
        name: '角色控制器兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
