import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import * as path from 'path';

@Injectable()
export class StorageService {
  private readonly uploadPath: string;

  constructor(private configService: ConfigService) {
    this.uploadPath = this.configService.get('UPLOAD_PATH', './uploads');
  }

  async saveFile(filename: string, buffer: Buffer): Promise<string> {
    const filePath = path.join(this.uploadPath, filename);
    
    // 确保目录存在
    await fs.mkdir(path.dirname(filePath), { recursive: true });
    
    // 保存文件
    await fs.writeFile(filePath, buffer);
    
    return filePath;
  }

  async getFile(filename: string): Promise<Buffer> {
    const filePath = path.join(this.uploadPath, filename);
    return await fs.readFile(filePath);
  }

  async deleteFile(filename: string): Promise<void> {
    const filePath = path.join(this.uploadPath, filename);
    await fs.unlink(filePath);
  }

  async fileExists(filename: string): Promise<boolean> {
    const filePath = path.join(this.uploadPath, filename);
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  getFileUrl(filename: string): string {
    const baseUrl = this.configService.get('BASE_URL', 'http://localhost:3004');
    return `${baseUrl}/uploads/${filename}`;
  }

  async uploadBuffer(buffer: Buffer, filename: string, mimeType: string): Promise<string> {
    const filePath = await this.saveFile(filename, buffer);
    return this.getFileUrl(filename);
  }
}
