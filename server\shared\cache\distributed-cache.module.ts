/**
 * 分布式缓存模块
 */
import { DynamicModule, Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { DistributedCacheService } from './distributed-cache.service';
import { DistributedCacheInterceptor } from './interceptors/distributed-cache.interceptor';
import { APP_INTERCEPTOR } from '@nestjs/core';

/**
 * 分布式缓存模块配置
 */
export interface DistributedCacheModuleOptions {
  /**
   * 是否全局注册缓存拦截器
   */
  isGlobal?: boolean;
  
  /**
   * 缓存配置
   */
  config?: any;
}

@Module({})
export class DistributedCacheModule {
  /**
   * 注册分布式缓存模块
   * @param options 模块配置
   */
  static register(options: DistributedCacheModuleOptions = {}): DynamicModule {
    const providers: Provider[] = [
      {
        provide: 'CACHE_OPTIONS',
        useValue: options.config || {},
      },
      DistributedCacheService,
    ];
    
    // 如果需要全局注册缓存拦截器
    if (options.isGlobal) {
      providers.push({
        provide: APP_INTERCEPTOR,
        useClass: DistributedCacheInterceptor,
      });
    }
    
    return {
      global: options.isGlobal,
      imports: [EventEmitterModule.forRoot()],
      module: DistributedCacheModule,
      providers,
      exports: [DistributedCacheService],
    };
  }
}
