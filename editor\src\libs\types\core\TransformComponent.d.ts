/**
 * 变换组件
 * 管理实体的位置、旋转和缩放
 */
import * as THREE from 'three';
import { Component } from './Component';
export declare class TransformComponent extends Component {
    static readonly type: string;
    /** Three.js 对象 */
    object3D: THREE.Object3D;
    /** 位置 */
    position: THREE.Vector3;
    /** 旋转 */
    rotation: THREE.Euler;
    /** 四元数旋转 */
    quaternion: THREE.Quaternion;
    /** 缩放 */
    scale: THREE.Vector3;
    /** 变换矩阵 */
    matrix: THREE.Matrix4;
    /** 世界变换矩阵 */
    matrixWorld: THREE.Matrix4;
    constructor();
    /**
     * 设置位置
     */
    setPosition(x: number, y: number, z: number): void;
    /**
     * 设置旋转（欧拉角）
     */
    setRotation(x: number, y: number, z: number): void;
    /**
     * 设置旋转（四元数）
     */
    setQuaternion(x: number, y: number, z: number, w: number): void;
    /**
     * 设置缩放
     */
    setScale(x: number, y: number, z: number): void;
    /**
     * 获取位置
     */
    getPosition(): THREE.Vector3;
    /**
     * 获取旋转
     */
    getRotation(): THREE.Euler;
    /**
     * 获取四元数
     */
    getQuaternion(): THREE.Quaternion;
    /**
     * 获取缩放
     */
    getScale(): THREE.Vector3;
    /**
     * 获取世界位置
     */
    getWorldPosition(): THREE.Vector3;
    /**
     * 获取世界旋转
     */
    getWorldQuaternion(): THREE.Quaternion;
    /**
     * 获取世界缩放
     */
    getWorldScale(): THREE.Vector3;
    /**
     * 向前移动
     */
    translateZ(distance: number): void;
    /**
     * 向右移动
     */
    translateX(distance: number): void;
    /**
     * 向上移动
     */
    translateY(distance: number): void;
    /**
     * 绕X轴旋转
     */
    rotateX(angle: number): void;
    /**
     * 绕Y轴旋转
     */
    rotateY(angle: number): void;
    /**
     * 绕Z轴旋转
     */
    rotateZ(angle: number): void;
    /**
     * 看向目标
     */
    lookAt(target: THREE.Vector3): void;
    /**
     * 添加子对象
     */
    add(object: THREE.Object3D): void;
    /**
     * 移除子对象
     */
    remove(object: THREE.Object3D): void;
    /**
     * 更新变换矩阵
     */
    updateMatrix(): void;
    /**
     * 更新世界变换矩阵
     */
    updateMatrixWorld(force?: boolean): void;
    /**
     * 更新组件
     */
    protected onUpdate(deltaTime: number): void;
    /**
     * 销毁组件
     */
    protected onDispose(): void;
}
