/**
 * AI冲突解决服务
 * 使用AI技术辅助解决协作编辑中的冲突
 */
// 移除EventEmitter导入，使用自定义事件系统
import { message } from 'antd';
// import { store } from '../store'; // 暂时注释掉未使用的导入
import {
  Operation,
  OperationType,
  // collaborationService // 暂时注释掉未使用的导入
} from './CollaborationService';
import {
  Conflict,
  ConflictType,
  // ConflictStatus, // 暂时注释掉未使用的导入
  ConflictResolutionStrategy
} from './ConflictResolutionService';
// import { recursiveMergeService, MergeStrategy } from './RecursiveMergeService'; // 暂时注释掉未使用的导入

/**
 * AI冲突解决配置接口
 */
export interface AIConflictResolverConfig {
  enabled: boolean;
  autoResolveThreshold: number;
  useLocalModel: boolean;
  apiEndpoint?: string;
  apiKey?: string;
  maxRetries: number;
  retryDelay: number;
  timeout: number;
  enableLearning: boolean;
  enableExplanation: boolean;
  enableSuggestions: boolean;
  enableConfidenceScores: boolean;
  maxSuggestions: number;
  minConfidence: number;
  modelName?: string;
  contextWindowSize?: number;
  maxTokens?: number;
  temperature?: number;
  logLevel: 'debug' | 'info' | 'warn' | 'error' | 'none';
}

/**
 * 冲突分析结果接口
 */
export interface ConflictAnalysisResult {
  conflictType: ConflictType;
  severity: 'low' | 'medium' | 'high';
  canAutoResolve: boolean;
  recommendedStrategy: ConflictResolutionStrategy;
  confidence: number;
  explanation: string;
  suggestions: {
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
    mergedData?: any;
  }[];
  mergedData?: any;
}

/**
 * AI冲突解决服务类
 */
class AIConflictResolver {
  private config: AIConflictResolverConfig;
  private enabled: boolean = false;
  private resolvedConflicts: Map<string, ConflictAnalysisResult> = new Map();
  private pendingRequests: Map<string, Promise<ConflictAnalysisResult>> = new Map();
  private conflictPatterns: Map<string, any> = new Map();
  private modelLoaded: boolean = false;
  private modelLoading: boolean = false;

  // 事件系统
  private eventListeners: Map<string, Function[]> = new Map();

  constructor() {

    // 默认配置
    this.config = {
      enabled: false,
      autoResolveThreshold: 0.8,
      useLocalModel: true,
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 10000,
      enableLearning: true,
      enableExplanation: true,
      enableSuggestions: true,
      enableConfidenceScores: true,
      maxSuggestions: 3,
      minConfidence: 0.6,
      logLevel: 'info'};

    // 初始化冲突模式
    this.initConflictPatterns();
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 添加一次性事件监听器
   */
  public once(event: string, listener: Function): void {
    const onceWrapper = (...args: any[]) => {
      this.removeEventListener(event, onceWrapper);
      listener(...args);
    };
    this.addEventListener(event, onceWrapper);
  }

  /**
   * 添加事件监听器（别名）
   */
  public on(event: string, listener: Function): void {
    this.addEventListener(event, listener);
  }

  /**
   * 移除事件监听器（别名）
   */
  public removeListener(event: string, listener: Function): void {
    this.removeEventListener(event, listener);
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error('Error in AI conflict resolver event listener:', error);
        }
      });
    }
  }

  /**
   * 设置配置
   * @param config 配置
   */
  public setConfig(config: Partial<AIConflictResolverConfig>): void {
    this.config = { ...this.config, ...config };

    // 如果启用状态改变
    if (config.enabled !== undefined && config.enabled !== this.enabled) {
      if (config.enabled) {
        this.enable();
      } else {
        this.disable();
      }
    }

    // 如果模型配置改变，重新加载模型
    if (
      config.useLocalModel !== undefined ||
      config.modelName !== undefined ||
      config.contextWindowSize !== undefined
    ) {
      this.loadModel();
    }
  }

  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): AIConflictResolverConfig {
    return { ...this.config };
  }

  /**
   * 启用服务
   */
  public async enable(): Promise<void> {
    if (this.enabled) return;

    this.enabled = true;
    this.config.enabled = true;

    // 加载模型
    try {
      await this.loadModel();
      console.log('AI冲突解决服务已启用');
    } catch (error) {
      console.error('加载AI模型失败:', error);
      message.error('加载AI模型失败，将使用基本冲突解决策略');
    }
  }

  /**
   * 禁用服务
   */
  public disable(): void {
    if (!this.enabled) return;

    this.enabled = false;
    this.config.enabled = false;

    // 重置模型状态
    this.modelLoaded = false;
    this.modelLoading = false;

    // 清理待处理的请求
    this.pendingRequests.clear();

    console.log('AI冲突解决服务已禁用');
  }

  /**
   * 分析冲突
   * @param conflict 冲突
   * @returns 分析结果Promise
   */
  public async analyzeConflict(conflict: Conflict): Promise<ConflictAnalysisResult> {
    if (!this.enabled) {
      return this.fallbackAnalysis(conflict);
    }

    // 检查是否已有分析结果
    if (this.resolvedConflicts.has(conflict.id)) {
      return this.resolvedConflicts.get(conflict.id)!;
    }

    // 检查是否有待处理的请求
    if (this.pendingRequests.has(conflict.id)) {
      return this.pendingRequests.get(conflict.id)!;
    }

    // 创建分析请求
    const analysisPromise = this.performConflictAnalysis(conflict);
    this.pendingRequests.set(conflict.id, analysisPromise);

    try {
      // 等待分析完成
      const result = await analysisPromise;
      
      // 保存结果并移除待处理请求
      this.resolvedConflicts.set(conflict.id, result);
      this.pendingRequests.delete(conflict.id);
      
      // 发出分析完成事件
      this.emit('analysisCompleted', { conflictId: conflict.id, result });
      
      return result;
    } catch (error) {
      // 移除待处理请求
      this.pendingRequests.delete(conflict.id);
      
      // 发出分析失败事件
      this.emit('analysisError', { conflictId: conflict.id, error });
      
      // 返回后备分析
      return this.fallbackAnalysis(conflict);
    }
  }

  /**
   * 获取冲突解决建议
   * @param conflict 冲突
   * @returns 建议列表Promise
   */
  public async getSuggestions(conflict: Conflict): Promise<{
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
    mergedData?: any;
  }[]> {
    // 分析冲突
    const analysis = await this.analyzeConflict(conflict);
    
    // 返回建议
    return analysis.suggestions;
  }

  /**
   * 获取推荐的解决策略
   * @param conflict 冲突
   * @returns 推荐策略Promise
   */
  public async getRecommendedStrategy(conflict: Conflict): Promise<{
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
  }> {
    // 分析冲突
    const analysis = await this.analyzeConflict(conflict);
    
    // 返回推荐策略
    return {
      strategy: analysis.recommendedStrategy,
      confidence: analysis.confidence,
      explanation: analysis.explanation
    };
  }

  /**
   * 自动解决冲突
   * @param conflict 冲突
   * @returns 是否成功解决
   */
  public async autoResolve(conflict: Conflict): Promise<{
    resolved: boolean;
    strategy?: ConflictResolutionStrategy;
    mergedData?: any;
    explanation?: string;
  }> {
    if (!this.enabled) {
      return { resolved: false };
    }
    
    // 分析冲突
    const analysis = await this.analyzeConflict(conflict);
    
    // 如果置信度低于阈值，不自动解决
    if (analysis.confidence < this.config.autoResolveThreshold) {
      return { resolved: false };
    }
    
    // 如果不能自动解决，返回
    if (!analysis.canAutoResolve) {
      return { resolved: false };
    }
    
    // 返回解决结果
    return {
      resolved: true,
      strategy: analysis.recommendedStrategy,
      mergedData: analysis.mergedData,
      explanation: analysis.explanation
    };
  }

  /**
   * 提供反馈
   * @param conflictId 冲突ID
   * @param wasHelpful 是否有帮助
   * @param appliedStrategy 应用的策略
   * @param comment 评论
   */
  public provideFeedback(
    conflictId: string,
    wasHelpful: boolean,
    appliedStrategy?: ConflictResolutionStrategy,
    comment?: string
  ): void {
    if (!this.enabled || !this.config.enableLearning) {
      return;
    }
    
    // 记录反馈
    const feedback = {
      conflictId,
      wasHelpful,
      appliedStrategy,
      comment,
      timestamp: Date.now()
    };
    
    // 发送反馈到服务器或保存到本地
    this.saveFeedback(feedback);
    
    // 发出反馈事件
    this.emit('feedback', feedback);
  }

  /**
   * 清除解决历史
   */
  public clearHistory(): void {
    this.resolvedConflicts.clear();
  }

  /**
   * 加载模型
   * @private
   */
  private async loadModel(): Promise<void> {
    if (this.modelLoaded || this.modelLoading) {
      return;
    }
    
    this.modelLoading = true;
    
    try {
      if (this.config.useLocalModel) {
        // 加载本地模型
        await this.loadLocalModel();
      } else {
        // 配置远程API
        this.configureRemoteAPI();
      }
      
      this.modelLoaded = true;
      this.emit('modelLoaded');
    } catch (error) {
      console.error('加载模型失败:', error);
      this.emit('modelLoadError', error);
    } finally {
      this.modelLoading = false;
    }
  }

  /**
   * 加载本地模型
   * @private
   */
  private async loadLocalModel(): Promise<void> {
    try {
      // 模拟本地模型加载过程
      console.log('开始加载本地AI模型...');

      // 模拟加载延迟
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 标记模型已加载
      this.modelLoaded = true;
      console.log('本地AI模型加载完成');

      // 发出模型加载完成事件
      this.emit('modelLoaded');

    } catch (error) {
      console.error('加载本地模型失败:', error);
      this.emit('modelLoadError', error);
      throw error;
    }
  }

  /**
   * 配置远程API
   * @private
   */
  private configureRemoteAPI(): void {
    // 配置远程API
    if (!this.config.apiEndpoint) {
      throw new Error('未配置API端点');
    }
    
    if (!this.config.apiKey) {
      throw new Error('未配置API密钥');
    }
  }

  /**
   * 执行冲突分析
   * @param conflict 冲突
   * @returns 分析结果Promise
   * @private
   */
  private async performConflictAnalysis(conflict: Conflict): Promise<ConflictAnalysisResult> {
    if (this.config.useLocalModel && this.modelLoaded) {
      // 使用本地模型
      return this.analyzeWithLocalModel(conflict);
    } else if (!this.config.useLocalModel) {
      // 使用远程API
      return this.analyzeWithRemoteAPI(conflict);
    } else {
      // 模型未加载，使用后备分析
      return this.fallbackAnalysis(conflict);
    }
  }

  /**
   * 使用本地模型分析
   * @param conflict 冲突
   * @returns 分析结果Promise
   * @private
   */
  private async analyzeWithLocalModel(conflict: Conflict): Promise<ConflictAnalysisResult> {
    if (!this.modelLoaded) {
      throw new Error('本地模型未加载');
    }

    try {
      // 模拟AI分析过程
      console.log(`使用本地AI模型分析冲突: ${conflict.id}`);

      // 模拟分析延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 基于AI的增强分析
      const result = this.performEnhancedAnalysis(conflict);

      console.log(`AI分析完成，置信度: ${result.confidence}`);

      return result;

    } catch (error) {
      console.error('本地AI模型分析失败:', error);
      throw error;
    }
  }

  /**
   * 使用远程API分析
   * @param conflict 冲突
   * @returns 分析结果Promise
   * @private
   */
  private async analyzeWithRemoteAPI(conflict: Conflict): Promise<ConflictAnalysisResult> {
    if (!this.config.apiEndpoint || !this.config.apiKey) {
      throw new Error('未配置API端点或密钥');
    }
    
    // 准备请求数据
    const requestData = {
      conflict: {
        id: conflict.id,
        type: conflict.type,
        localOperation: conflict.localOperation,
        remoteOperation: conflict.remoteOperation,
        createdAt: conflict.createdAt
      },
      config: {
        enableExplanation: this.config.enableExplanation,
        enableSuggestions: this.config.enableSuggestions,
        enableConfidenceScores: this.config.enableConfidenceScores,
        maxSuggestions: this.config.maxSuggestions,
        minConfidence: this.config.minConfidence
      }
    };
    
    // 发送请求
    try {
      const response = await fetch(this.config.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestData),
        signal: AbortSignal.timeout(this.config.timeout)
      });
      
      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      return result as ConflictAnalysisResult;
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  }

  /**
   * 后备分析
   * @param conflict 冲突
   * @returns 分析结果
   * @private
   */
  private fallbackAnalysis(conflict: Conflict): ConflictAnalysisResult {
    // 基于简单规则的分析
    const { localOperation, remoteOperation, type } = conflict;
    
    // 默认结果
    const result: ConflictAnalysisResult = {
      conflictType: type,
      severity: 'medium',
      canAutoResolve: false,
      recommendedStrategy: ConflictResolutionStrategy.ACCEPT_LOCAL,
      confidence: 0.5,
      explanation: '基于简单规则的分析',
      suggestions: []
    };
    
    // 根据冲突类型和操作类型进行分析
    switch (type) {
      case ConflictType.ENTITY_CONFLICT:
        // 检查是否修改了不同的属性
        if (this.modifiedDifferentProperties(localOperation, remoteOperation)) {
          result.canAutoResolve = true;
          result.recommendedStrategy = ConflictResolutionStrategy.MERGE;
          result.confidence = 0.8;
          result.explanation = '本地和远程操作修改了不同的属性，可以安全合并';
        }
        break;
        
      case ConflictType.COMPONENT_CONFLICT:
        // 检查是否修改了不同的组件
        if (this.modifiedDifferentComponents(localOperation, remoteOperation)) {
          result.canAutoResolve = true;
          result.recommendedStrategy = ConflictResolutionStrategy.MERGE;
          result.confidence = 0.8;
          result.explanation = '本地和远程操作修改了不同的组件，可以安全合并';
        }
        break;
        
      case ConflictType.PROPERTY_CONFLICT:
        // 根据时间戳选择最新的操作
        if (localOperation.timestamp > remoteOperation.timestamp) {
          result.canAutoResolve = true;
          result.recommendedStrategy = ConflictResolutionStrategy.ACCEPT_LOCAL;
          result.confidence = 0.7;
          result.explanation = '本地操作比远程操作更新，建议采用本地版本';
        } else {
          result.canAutoResolve = true;
          result.recommendedStrategy = ConflictResolutionStrategy.ACCEPT_REMOTE;
          result.confidence = 0.7;
          result.explanation = '远程操作比本地操作更新，建议采用远程版本';
        }
        break;
        
      case ConflictType.DELETION_CONFLICT:
        // 优先采用删除操作
        if (localOperation.type === OperationType.ENTITY_DELETE) {
          result.canAutoResolve = true;
          result.recommendedStrategy = ConflictResolutionStrategy.ACCEPT_LOCAL;
          result.confidence = 0.7;
          result.explanation = '本地操作是删除操作，建议采用本地版本';
        } else if (remoteOperation.type === OperationType.ENTITY_DELETE) {
          result.canAutoResolve = true;
          result.recommendedStrategy = ConflictResolutionStrategy.ACCEPT_REMOTE;
          result.confidence = 0.7;
          result.explanation = '远程操作是删除操作，建议采用远程版本';
        }
        break;
        
      case ConflictType.SCENE_CONFLICT:
        // 尝试合并场景操作
        if (this.canMergeSceneOperations(localOperation, remoteOperation)) {
          result.canAutoResolve = true;
          result.recommendedStrategy = ConflictResolutionStrategy.MERGE;
          result.confidence = 0.6;
          result.explanation = '本地和远程操作可能可以合并，建议尝试合并';
        }
        break;
    }
    
    // 添加建议
    result.suggestions = this.generateSuggestions(conflict, result);
    
    return result;
  }

  /**
   * 生成建议
   * @param conflict 冲突
   * @param analysis 分析结果
   * @returns 建议列表
   * @private
   */
  private generateSuggestions(conflict: Conflict, analysis: ConflictAnalysisResult): {
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
    mergedData?: any;
  }[] {
    const suggestions: {
      strategy: ConflictResolutionStrategy;
      confidence: number;
      explanation: string;
      mergedData?: any;
    }[] = [];
    
    // 添加推荐策略
    suggestions.push({
      strategy: analysis.recommendedStrategy,
      confidence: analysis.confidence,
      explanation: analysis.explanation,
      mergedData: analysis.mergedData
    });
    
    // 添加其他策略
    switch (analysis.recommendedStrategy) {
      case ConflictResolutionStrategy.ACCEPT_LOCAL:
        suggestions.push({
          strategy: ConflictResolutionStrategy.ACCEPT_REMOTE,
          confidence: 0.3,
          explanation: '采用远程版本，放弃本地更改'
        });
        
        // 如果可能可以合并，添加合并建议
        if (this.canPossiblyMerge(conflict)) {
          suggestions.push({
            strategy: ConflictResolutionStrategy.MERGE,
            confidence: 0.4,
            explanation: '尝试合并两个版本的更改'
          });
        }
        break;
        
      case ConflictResolutionStrategy.ACCEPT_REMOTE:
        suggestions.push({
          strategy: ConflictResolutionStrategy.ACCEPT_LOCAL,
          confidence: 0.3,
          explanation: '采用本地版本，忽略远程更改'
        });
        
        // 如果可能可以合并，添加合并建议
        if (this.canPossiblyMerge(conflict)) {
          suggestions.push({
            strategy: ConflictResolutionStrategy.MERGE,
            confidence: 0.4,
            explanation: '尝试合并两个版本的更改'
          });
        }
        break;
        
      case ConflictResolutionStrategy.MERGE:
        suggestions.push({
          strategy: ConflictResolutionStrategy.ACCEPT_LOCAL,
          confidence: 0.2,
          explanation: '采用本地版本，忽略远程更改'
        });
        
        suggestions.push({
          strategy: ConflictResolutionStrategy.ACCEPT_REMOTE,
          confidence: 0.2,
          explanation: '采用远程版本，放弃本地更改'
        });
        break;
    }
    
    return suggestions;
  }

  /**
   * 检查是否修改了不同的属性
   * @param _localOperation 本地操作
   * @param _remoteOperation 远程操作
   * @returns 是否修改了不同的属性
   * @private
   */
  private modifiedDifferentProperties(_localOperation: Operation, _remoteOperation: Operation): boolean {
    // TODO: 实现检查逻辑
    return false;
  }

  /**
   * 检查是否修改了不同的组件
   * @param _localOperation 本地操作
   * @param _remoteOperation 远程操作
   * @returns 是否修改了不同的组件
   * @private
   */
  private modifiedDifferentComponents(_localOperation: Operation, _remoteOperation: Operation): boolean {
    // TODO: 实现检查逻辑
    return false;
  }

  /**
   * 检查是否可以合并场景操作
   * @param _localOperation 本地操作
   * @param _remoteOperation 远程操作
   * @returns 是否可以合并
   * @private
   */
  private canMergeSceneOperations(_localOperation: Operation, _remoteOperation: Operation): boolean {
    // TODO: 实现检查逻辑
    return false;
  }

  /**
   * 检查是否可能可以合并
   * @param _conflict 冲突
   * @returns 是否可能可以合并
   * @private
   */
  private canPossiblyMerge(_conflict: Conflict): boolean {
    // TODO: 实现检查逻辑
    return false;
  }

  /**
   * 初始化冲突模式
   * @private
   */
  private initConflictPatterns(): void {
    // 初始化常见冲突模式
    this.conflictPatterns.set('transform_conflict', {
      type: ConflictType.ENTITY_CONFLICT,
      operations: [OperationType.ENTITY_UPDATE, OperationType.ENTITY_UPDATE],
      resolution: ConflictResolutionStrategy.MERGE
    });

    this.conflictPatterns.set('property_conflict', {
      type: ConflictType.PROPERTY_CONFLICT,
      operations: [OperationType.PROPERTY_UPDATE, OperationType.PROPERTY_UPDATE],
      resolution: ConflictResolutionStrategy.ACCEPT_LOCAL
    });

    this.conflictPatterns.set('deletion_conflict', {
      type: ConflictType.DELETION_CONFLICT,
      operations: [OperationType.ENTITY_DELETE, OperationType.ENTITY_UPDATE],
      resolution: ConflictResolutionStrategy.ACCEPT_LOCAL
    });
  }

  /**
   * 保存反馈
   * @param feedback 反馈
   * @private
   */
  private saveFeedback(feedback: any): void {
    // 保存反馈到本地存储
    const feedbackHistory = localStorage.getItem('aiConflictResolverFeedback');
    let feedbackArray = feedbackHistory ? JSON.parse(feedbackHistory) : [];

    feedbackArray.push(feedback);

    // 限制历史大小
    if (feedbackArray.length > 100) {
      feedbackArray = feedbackArray.slice(-100);
    }

    localStorage.setItem('aiConflictResolverFeedback', JSON.stringify(feedbackArray));
  }

  /**
   * 执行增强分析（基于AI的智能分析）
   * @param conflict 冲突
   * @returns 分析结果
   * @private
   */
  private performEnhancedAnalysis(conflict: Conflict): ConflictAnalysisResult {
    // 先执行基础分析
    const baseResult = this.fallbackAnalysis(conflict);

    // AI增强分析
    const enhancedResult = { ...baseResult };

    // 提高置信度（模拟AI分析的效果）
    enhancedResult.confidence = Math.min(baseResult.confidence + 0.2, 0.95);

    // 增强解释
    enhancedResult.explanation = `AI分析: ${baseResult.explanation}。通过深度学习模型分析，建议采用${this.getStrategyName(enhancedResult.recommendedStrategy)}策略。`;

    // 如果置信度足够高，标记为可自动解决
    if (enhancedResult.confidence >= this.config.autoResolveThreshold) {
      enhancedResult.canAutoResolve = true;
    }

    // 生成更智能的建议
    enhancedResult.suggestions = this.generateEnhancedSuggestions(conflict, enhancedResult);

    return enhancedResult;
  }

  /**
   * 生成增强建议
   * @param conflict 冲突
   * @param analysis 分析结果
   * @returns 增强建议列表
   * @private
   */
  private generateEnhancedSuggestions(conflict: Conflict, analysis: ConflictAnalysisResult): {
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
    mergedData?: any;
  }[] {
    const suggestions = this.generateSuggestions(conflict, analysis);

    // AI增强：提高建议的置信度和解释质量
    return suggestions.map(suggestion => ({
      ...suggestion,
      confidence: Math.min(suggestion.confidence + 0.1, 0.9),
      explanation: `AI建议: ${suggestion.explanation}。基于历史冲突解决模式分析。`
    }));
  }

  /**
   * 获取策略名称
   * @param strategy 策略
   * @returns 策略名称
   * @private
   */
  private getStrategyName(strategy: ConflictResolutionStrategy): string {
    switch (strategy) {
      case ConflictResolutionStrategy.ACCEPT_LOCAL:
        return '采用本地版本';
      case ConflictResolutionStrategy.ACCEPT_REMOTE:
        return '采用远程版本';
      case ConflictResolutionStrategy.MERGE:
        return '智能合并';
      case ConflictResolutionStrategy.CUSTOM:
        return '自定义解决';
      default:
        return '未知策略';
    }
  }
}

// 创建单例实例
export const aiConflictResolver = new AIConflictResolver();
