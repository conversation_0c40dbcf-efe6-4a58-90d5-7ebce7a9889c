/**
 * 交互管理器
 * 负责处理角色与环境和其他对象的交互
 */
export class InteractionManager {
  /**
   * 构造函数
   */
  constructor() {
    this.character = null;
    this.characterController = null;
    this.interactableObjects = [];
    this.currentInteractable = null;
    this.interactionDistance = 2.0;
    this.isInteracting = false;
    
    // 交互类型
    this.interactionTypes = {
      PICKUP: 'pickup',
      USE: 'use',
      TALK: 'talk',
      EXAMINE: 'examine',
      CLIMB: 'climb',
      PUSH: 'push',
      PULL: 'pull'
    };
    
    // 交互状态
    this.interactionState = {
      type: null,
      target: null,
      progress: 0,
      duration: 0,
      callback: null
    };
    
    // 物品栏
    this.inventory = [];
    this.maxInventorySize = 10;
    
    // 对话系统
    this.dialogSystem = {
      isActive: false,
      currentDialog: null,
      currentSpeaker: null,
      currentOptions: [],
      selectedOption: -1
    };
  }
  
  /**
   * 初始化
   * @param {Object} character 角色对象
   * @param {CharacterController} characterController 角色控制器
   */
  initialize(character, characterController) {
    this.character = character;
    this.characterController = characterController;
  }
  
  /**
   * 注册可交互对象
   * @param {Object} object 可交互对象
   */
  registerInteractable(object) {
    // 确保对象有必要的属性
    if (!object.id || !object.position || !object.interactionType) {
      console.warn('无效的可交互对象:', object);
      return;
    }
    
    // 添加到可交互对象列表
    this.interactableObjects.push(object);
    console.log('注册可交互对象:', object);
  }
  
  /**
   * 注销可交互对象
   * @param {string} objectId 对象ID
   */
  unregisterInteractable(objectId) {
    const index = this.interactableObjects.findIndex(obj => obj.id === objectId);
    
    if (index !== -1) {
      this.interactableObjects.splice(index, 1);
      console.log('注销可交互对象:', objectId);
      
      // 如果当前交互对象被注销，清除当前交互
      if (this.currentInteractable && this.currentInteractable.id === objectId) {
        this.clearCurrentInteractable();
      }
    }
  }
  
  /**
   * 更新
   * @param {number} deltaTime 时间增量（秒）
   */
  update(deltaTime) {
    // 查找最近的可交互对象
    this.findNearestInteractable();
    
    // 更新交互状态
    this.updateInteractionState(deltaTime);
    
    // 更新对话系统
    this.updateDialogSystem(deltaTime);
    
    // 更新UI
    this.updateInteractionUI();
  }
  
  /**
   * 查找最近的可交互对象
   */
  findNearestInteractable() {
    if (!this.character || !this.character.position) {
      return;
    }
    
    let nearestObject = null;
    let nearestDistance = this.interactionDistance;
    
    for (const object of this.interactableObjects) {
      // 计算距离
      const distance = this.calculateDistance(
        this.character.position,
        object.position
      );
      
      // 如果在交互范围内且比当前最近的更近
      if (distance <= nearestDistance) {
        nearestObject = object;
        nearestDistance = distance;
      }
    }
    
    // 更新当前可交互对象
    if (nearestObject !== this.currentInteractable) {
      this.setCurrentInteractable(nearestObject);
    }
  }
  
  /**
   * 计算距离
   * @param {Object} position1 位置1
   * @param {Object} position2 位置2
   * @returns {number} 距离
   */
  calculateDistance(position1, position2) {
    const dx = position1.x - position2.x;
    const dy = position1.y - position2.y;
    const dz = position1.z - position2.z;
    
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }
  
  /**
   * 设置当前可交互对象
   * @param {Object} object 可交互对象
   */
  setCurrentInteractable(object) {
    // 如果之前有可交互对象，触发离开事件
    if (this.currentInteractable) {
      this.onInteractableExit(this.currentInteractable);
    }
    
    // 设置新的可交互对象
    this.currentInteractable = object;
    
    // 如果有新的可交互对象，触发进入事件
    if (this.currentInteractable) {
      this.onInteractableEnter(this.currentInteractable);
    }
  }
  
  /**
   * 清除当前可交互对象
   */
  clearCurrentInteractable() {
    if (this.currentInteractable) {
      this.onInteractableExit(this.currentInteractable);
      this.currentInteractable = null;
    }
  }
  
  /**
   * 可交互对象进入事件
   * @param {Object} object 可交互对象
   */
  onInteractableEnter(object) {
    console.log('可交互对象进入:', object);
    
    // 在实际应用中，这里可以显示交互提示
    this.showInteractionPrompt(object);
  }
  
  /**
   * 可交互对象离开事件
   * @param {Object} object 可交互对象
   */
  onInteractableExit(object) {
    console.log('可交互对象离开:', object);
    
    // 在实际应用中，这里可以隐藏交互提示
    this.hideInteractionPrompt();
  }
  
  /**
   * 显示交互提示
   * @param {Object} object 可交互对象
   */
  showInteractionPrompt(object) {
    // 在实际应用中，这里应该显示交互提示UI
    console.log('显示交互提示:', object.interactionType);
  }
  
  /**
   * 隐藏交互提示
   */
  hideInteractionPrompt() {
    // 在实际应用中，这里应该隐藏交互提示UI
    console.log('隐藏交互提示');
  }
  
  /**
   * 开始交互
   */
  startInteraction() {
    if (!this.currentInteractable || this.isInteracting) {
      return;
    }
    
    // 设置交互状态
    this.isInteracting = true;
    
    // 根据交互类型执行不同的交互
    switch (this.currentInteractable.interactionType) {
      case this.interactionTypes.PICKUP:
        this.pickupObject(this.currentInteractable);
        break;
      case this.interactionTypes.USE:
        this.useObject(this.currentInteractable);
        break;
      case this.interactionTypes.TALK:
        this.startDialog(this.currentInteractable);
        break;
      case this.interactionTypes.EXAMINE:
        this.examineObject(this.currentInteractable);
        break;
      case this.interactionTypes.CLIMB:
        this.climbObject(this.currentInteractable);
        break;
      case this.interactionTypes.PUSH:
        this.pushObject(this.currentInteractable);
        break;
      case this.interactionTypes.PULL:
        this.pullObject(this.currentInteractable);
        break;
      default:
        console.warn('未知的交互类型:', this.currentInteractable.interactionType);
        this.isInteracting = false;
        break;
    }
  }
  
  /**
   * 结束交互
   */
  endInteraction() {
    if (!this.isInteracting) {
      return;
    }
    
    // 重置交互状态
    this.isInteracting = false;
    this.interactionState.type = null;
    this.interactionState.target = null;
    this.interactionState.progress = 0;
    this.interactionState.duration = 0;
    this.interactionState.callback = null;
    
    // 如果对话系统活跃，结束对话
    if (this.dialogSystem.isActive) {
      this.endDialog();
    }
    
    console.log('结束交互');
  }
  
  /**
   * 更新交互状态
   * @param {number} deltaTime 时间增量（秒）
   */
  updateInteractionState(deltaTime) {
    if (!this.isInteracting || !this.interactionState.type) {
      return;
    }
    
    // 更新进度
    if (this.interactionState.duration > 0) {
      this.interactionState.progress += deltaTime;
      
      // 如果完成，执行回调
      if (this.interactionState.progress >= this.interactionState.duration) {
        if (this.interactionState.callback) {
          this.interactionState.callback();
        }
        
        // 重置交互状态
        this.interactionState.type = null;
        this.interactionState.target = null;
        this.interactionState.progress = 0;
        this.interactionState.duration = 0;
        this.interactionState.callback = null;
      }
    }
  }
  
  /**
   * 拾取对象
   * @param {Object} object 可交互对象
   */
  pickupObject(object) {
    console.log('拾取对象:', object);
    
    // 检查物品栏是否已满
    if (this.inventory.length >= this.maxInventorySize) {
      console.warn('物品栏已满');
      this.isInteracting = false;
      return;
    }
    
    // 设置交互状态
    this.interactionState.type = this.interactionTypes.PICKUP;
    this.interactionState.target = object;
    this.interactionState.progress = 0;
    this.interactionState.duration = 0.5; // 拾取动画持续时间（秒）
    this.interactionState.callback = () => {
      // 添加到物品栏
      this.inventory.push(object);
      
      // 从场景中移除
      this.unregisterInteractable(object.id);
      
      // 结束交互
      this.isInteracting = false;
      
      console.log('拾取完成:', object);
    };
  }
  
  /**
   * 使用对象
   * @param {Object} object 可交互对象
   */
  useObject(object) {
    console.log('使用对象:', object);
    
    // 设置交互状态
    this.interactionState.type = this.interactionTypes.USE;
    this.interactionState.target = object;
    this.interactionState.progress = 0;
    this.interactionState.duration = 1.0; // 使用动画持续时间（秒）
    this.interactionState.callback = () => {
      // 执行对象的使用效果
      if (object.onUse) {
        object.onUse(this.character);
      }
      
      // 结束交互
      this.isInteracting = false;
      
      console.log('使用完成:', object);
    };
  }
  
  /**
   * 开始对话
   * @param {Object} object 可交互对象
   */
  startDialog(object) {
    console.log('开始对话:', object);
    
    // 检查对象是否有对话数据
    if (!object.dialogData) {
      console.warn('对象没有对话数据');
      this.isInteracting = false;
      return;
    }
    
    // 设置对话系统状态
    this.dialogSystem.isActive = true;
    this.dialogSystem.currentDialog = object.dialogData;
    this.dialogSystem.currentSpeaker = object;
    this.dialogSystem.currentOptions = object.dialogData.options || [];
    this.dialogSystem.selectedOption = -1;
    
    // 显示对话UI
    this.showDialogUI();
  }
  
  /**
   * 结束对话
   */
  endDialog() {
    console.log('结束对话');
    
    // 重置对话系统状态
    this.dialogSystem.isActive = false;
    this.dialogSystem.currentDialog = null;
    this.dialogSystem.currentSpeaker = null;
    this.dialogSystem.currentOptions = [];
    this.dialogSystem.selectedOption = -1;
    
    // 隐藏对话UI
    this.hideDialogUI();
    
    // 结束交互
    this.isInteracting = false;
  }
  
  /**
   * 选择对话选项
   * @param {number} optionIndex 选项索引
   */
  selectDialogOption(optionIndex) {
    if (!this.dialogSystem.isActive || optionIndex < 0 || optionIndex >= this.dialogSystem.currentOptions.length) {
      return;
    }
    
    // 设置选中的选项
    this.dialogSystem.selectedOption = optionIndex;
    
    // 获取选项
    const option = this.dialogSystem.currentOptions[optionIndex];
    
    // 执行选项效果
    if (option.onSelect) {
      option.onSelect(this.character, this.dialogSystem.currentSpeaker);
    }
    
    // 如果有下一个对话，切换到下一个对话
    if (option.nextDialog) {
      this.dialogSystem.currentDialog = option.nextDialog;
      this.dialogSystem.currentOptions = option.nextDialog.options || [];
      this.dialogSystem.selectedOption = -1;
      
      // 更新对话UI
      this.updateDialogUI();
    } else {
      // 否则结束对话
      this.endDialog();
    }
  }
  
  /**
   * 显示对话UI
   */
  showDialogUI() {
    // 在实际应用中，这里应该显示对话UI
    console.log('显示对话UI:', this.dialogSystem.currentDialog);
  }
  
  /**
   * 更新对话UI
   */
  updateDialogUI() {
    // 在实际应用中，这里应该更新对话UI
    console.log('更新对话UI:', this.dialogSystem.currentDialog);
  }
  
  /**
   * 隐藏对话UI
   */
  hideDialogUI() {
    // 在实际应用中，这里应该隐藏对话UI
    console.log('隐藏对话UI');
  }
  
  /**
   * 更新对话系统
   * @param {number} deltaTime 时间增量（秒）
   */
  updateDialogSystem(deltaTime) {
    if (!this.dialogSystem.isActive) {
      return;
    }
    
    // 在实际应用中，这里可以更新对话动画、字幕等
  }
  
  /**
   * 检查对象
   * @param {Object} object 可交互对象
   */
  examineObject(object) {
    console.log('检查对象:', object);
    
    // 设置交互状态
    this.interactionState.type = this.interactionTypes.EXAMINE;
    this.interactionState.target = object;
    this.interactionState.progress = 0;
    this.interactionState.duration = 2.0; // 检查动画持续时间（秒）
    this.interactionState.callback = () => {
      // 显示对象信息
      if (object.description) {
        this.showObjectDescription(object);
      }
      
      // 结束交互
      this.isInteracting = false;
      
      console.log('检查完成:', object);
    };
  }
  
  /**
   * 显示对象描述
   * @param {Object} object 可交互对象
   */
  showObjectDescription(object) {
    // 在实际应用中，这里应该显示对象描述UI
    console.log('显示对象描述:', object.description);
  }
  
  /**
   * 攀爬对象
   * @param {Object} object 可交互对象
   */
  climbObject(object) {
    console.log('攀爬对象:', object);
    
    // 设置交互状态
    this.interactionState.type = this.interactionTypes.CLIMB;
    this.interactionState.target = object;
    this.interactionState.progress = 0;
    this.interactionState.duration = 3.0; // 攀爬动画持续时间（秒）
    this.interactionState.callback = () => {
      // 移动角色到攀爬目标位置
      if (object.climbTargetPosition && this.character && this.character.position) {
        this.character.position = { ...object.climbTargetPosition };
      }
      
      // 结束交互
      this.isInteracting = false;
      
      console.log('攀爬完成:', object);
    };
  }
  
  /**
   * 推动对象
   * @param {Object} object 可交互对象
   */
  pushObject(object) {
    console.log('推动对象:', object);
    
    // 设置交互状态
    this.interactionState.type = this.interactionTypes.PUSH;
    this.interactionState.target = object;
    this.interactionState.progress = 0;
    this.interactionState.duration = 1.5; // 推动动画持续时间（秒）
    this.interactionState.callback = () => {
      // 移动对象
      if (object.position && object.pushDirection) {
        object.position.x += object.pushDirection.x;
        object.position.y += object.pushDirection.y;
        object.position.z += object.pushDirection.z;
      }
      
      // 结束交互
      this.isInteracting = false;
      
      console.log('推动完成:', object);
    };
  }
  
  /**
   * 拉动对象
   * @param {Object} object 可交互对象
   */
  pullObject(object) {
    console.log('拉动对象:', object);
    
    // 设置交互状态
    this.interactionState.type = this.interactionTypes.PULL;
    this.interactionState.target = object;
    this.interactionState.progress = 0;
    this.interactionState.duration = 1.5; // 拉动动画持续时间（秒）
    this.interactionState.callback = () => {
      // 移动对象
      if (object.position && object.pullDirection) {
        object.position.x += object.pullDirection.x;
        object.position.y += object.pullDirection.y;
        object.position.z += object.pullDirection.z;
      }
      
      // 结束交互
      this.isInteracting = false;
      
      console.log('拉动完成:', object);
    };
  }
  
  /**
   * 更新交互UI
   */
  updateInteractionUI() {
    // 在实际应用中，这里应该更新交互UI
    // 例如显示交互提示、进度条等
    
    if (this.isInteracting && this.interactionState.type && this.interactionState.duration > 0) {
      // 计算进度百分比
      const progress = this.interactionState.progress / this.interactionState.duration * 100;
      
      // 更新进度条
      console.log('交互进度:', progress.toFixed(0) + '%');
    }
  }
}
