import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { KnowledgeBase } from '../../entities/knowledge-base.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([KnowledgeBase]),
  ],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService],
})
export class HealthModule {}
