FROM node:22-alpine AS builder

WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制API网关代码
COPY api-gateway/package*.json ./api-gateway/
WORKDIR /app/api-gateway
RUN npm install

# 复制API网关源代码
COPY api-gateway/ ./

RUN npm run build

FROM node:22-alpine

WORKDIR /app

COPY --from=builder /app/api-gateway/package*.json ./
COPY --from=builder /app/api-gateway/dist ./dist

RUN npm install --only=production

# 创建上传目录
RUN mkdir -p /app/uploads/images /app/uploads/models /app/uploads/audio /app/uploads/other

EXPOSE 8080

CMD ["node", "dist/main.js"]
