/**
 * 事务监控面板控制器
 */
import { Controller, Get, Param, Query, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { TransactionMonitorService, TransactionStats } from '../monitoring/transaction-monitor.service';
import { Transaction, TransactionStatus } from '../transaction.interface';

@ApiTags('事务监控')
@Controller('api/transactions')
export class TransactionDashboardController {
  constructor(
    private readonly transactionMonitorService: TransactionMonitorService,
  ) {}
  
  /**
   * 获取事务统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取事务统计信息' })
  @ApiResponse({ status: HttpStatus.OK, description: '事务统计信息' })
  getTransactionStats(): TransactionStats {
    return this.transactionMonitorService.getTransactionStats();
  }
  
  /**
   * 获取活跃事务列表
   */
  @Get('active')
  @ApiOperation({ summary: '获取活跃事务列表' })
  @ApiResponse({ status: HttpStatus.OK, description: '活跃事务列表' })
  getActiveTransactions(): Transaction[] {
    return this.transactionMonitorService.getActiveTransactions();
  }
  
  /**
   * 获取事务历史
   * @param limit 限制数量
   * @param offset 偏移量
   * @param status 事务状态
   * @param initiator 发起者
   * @param participant 参与者
   * @param startTimeFrom 开始时间（从）
   * @param startTimeTo 开始时间（到）
   * @param minDuration 最小耗时
   * @param maxDuration 最大耗时
   * @param errorType 错误类型
   */
  @Get('history')
  @ApiOperation({ summary: '获取事务历史' })
  @ApiResponse({ status: HttpStatus.OK, description: '事务历史' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, enum: TransactionStatus })
  @ApiQuery({ name: 'initiator', required: false, type: String })
  @ApiQuery({ name: 'participant', required: false, type: String })
  @ApiQuery({ name: 'startTimeFrom', required: false, type: Date })
  @ApiQuery({ name: 'startTimeTo', required: false, type: Date })
  @ApiQuery({ name: 'minDuration', required: false, type: Number })
  @ApiQuery({ name: 'maxDuration', required: false, type: Number })
  @ApiQuery({ name: 'errorType', required: false, type: String })
  getTransactionHistory(
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
    @Query('status') status?: TransactionStatus,
    @Query('initiator') initiator?: string,
    @Query('participant') participant?: string,
    @Query('startTimeFrom') startTimeFrom?: Date,
    @Query('startTimeTo') startTimeTo?: Date,
    @Query('minDuration') minDuration?: number,
    @Query('maxDuration') maxDuration?: number,
    @Query('errorType') errorType?: string,
  ) {
    return this.transactionMonitorService.getTransactionHistory(
      limit,
      offset,
      {
        status,
        initiator,
        participant,
        startTimeFrom,
        startTimeTo,
        minDuration,
        maxDuration,
        errorType,
      },
    );
  }
  
  /**
   * 获取事务详情
   * @param id 事务ID
   */
  @Get(':id')
  @ApiOperation({ summary: '获取事务详情' })
  @ApiResponse({ status: HttpStatus.OK, description: '事务详情' })
  getTransactionDetails(@Param('id') id: string) {
    return this.transactionMonitorService.getTransactionDetails(id);
  }
  
  /**
   * 获取事务性能指标
   * @param id 事务ID
   */
  @Get(':id/metrics')
  @ApiOperation({ summary: '获取事务性能指标' })
  @ApiResponse({ status: HttpStatus.OK, description: '事务性能指标' })
  getTransactionMetrics(@Param('id') id: string) {
    return this.transactionMonitorService.getPerformanceMetrics(id);
  }
  
  /**
   * 获取所有事务性能指标
   */
  @Get('metrics/all')
  @ApiOperation({ summary: '获取所有事务性能指标' })
  @ApiResponse({ status: HttpStatus.OK, description: '所有事务性能指标' })
  getAllTransactionMetrics() {
    return this.transactionMonitorService.getAllPerformanceMetrics();
  }
}
