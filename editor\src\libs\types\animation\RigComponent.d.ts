/**
 * 骨骼绑定组件
 * 管理实体与骨骼的映射关系
 */
import { Component } from '../core/Component';
export interface BoneMapping {
    /** 实体ID */
    entityId: string;
    /** 骨骼名称 */
    boneName: string;
    /** 权重 */
    weight?: number;
}
export declare class RigComponent extends Component {
    static readonly type: string;
    /** 实体到骨骼的映射 */
    entitiesToBones: Record<string, string>;
    /** 骨骼到实体的映射 */
    bonesToEntities: Record<string, string>;
    /** 骨骼映射列表 */
    private boneMappings;
    /** 根骨骼名称 */
    rootBone: string;
    constructor();
    /**
     * 添加骨骼映射
     */
    addBoneMapping(entityId: string, boneName: string, weight?: number): void;
    /**
     * 移除骨骼映射
     */
    removeBoneMapping(entityId: string): void;
    /**
     * 根据实体ID获取骨骼名称
     */
    getBoneNameByEntity(entityId: string): string | null;
    /**
     * 根据骨骼名称获取实体ID
     */
    getEntityByBoneName(boneName: string): string | null;
    /**
     * 获取所有骨骼映射
     */
    getBoneMappings(): BoneMapping[];
    /**
     * 设置根骨骼
     */
    setRootBone(boneName: string): void;
    /**
     * 获取根骨骼
     */
    getRootBone(): string;
    /**
     * 清除所有映射
     */
    clearMappings(): void;
    /**
     * 检查是否有指定的骨骼映射
     */
    hasBoneMapping(entityId: string): boolean;
    /**
     * 检查是否有指定的实体映射
     */
    hasEntityMapping(boneName: string): boolean;
    /**
     * 获取映射数量
     */
    getMappingCount(): number;
    /**
     * 从配置对象创建映射
     */
    fromConfig(config: {
        entitiesToBones?: Record<string, string>;
        rootBone?: string;
    }): void;
    /**
     * 导出为配置对象
     */
    toConfig(): {
        entitiesToBones: Record<string, string>;
        rootBone: string;
    };
    /**
     * 克隆组件
     */
    clone(): RigComponent;
}
