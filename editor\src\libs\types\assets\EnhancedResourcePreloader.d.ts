/**
 * 增强资源预加载器
 * 提供更高效的资源预加载功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetType } from './ResourceManager';
import { EnhancedResourceManager } from './EnhancedResourceManager';
import { EnhancedResourceDependencyManager } from './EnhancedResourceDependencyManager';
/**
 * 预加载资源信息
 */
export interface PreloadResourceInfo {
    /** 资源ID */
    id: string;
    /** 资源类型 */
    type: AssetType;
    /** 资源URL */
    url: string;
    /** 资源优先级（0-100，数值越大优先级越高） */
    priority?: number;
    /** 资源组 */
    group?: string;
    /** 资源元数据 */
    metadata?: Record<string, any>;
    /** 资源标签 */
    tags?: string[];
}
/**
 * 预加载组信息
 */
export interface PreloadGroupInfo {
    /** 组名 */
    name: string;
    /** 组优先级（0-100，数值越大优先级越高） */
    priority: number;
    /** 组依赖 */
    dependencies?: string[];
    /** 组资源 */
    resources: PreloadResourceInfo[];
    /** 组元数据 */
    metadata?: Record<string, any>;
    /** 组标签 */
    tags?: string[];
}
/**
 * 预加载进度信息
 */
export interface PreloadProgressInfo {
    /** 组名 */
    group: string;
    /** 已加载资源数 */
    loaded: number;
    /** 总资源数 */
    total: number;
    /** 加载进度（0-1） */
    progress: number;
    /** 已加载资源 */
    loadedResources: string[];
    /** 加载失败资源 */
    failedResources: string[];
    /** 当前加载资源 */
    currentResource?: string;
    /** 已用时间（毫秒） */
    elapsedTime?: number;
    /** 估计剩余时间（毫秒） */
    estimatedTimeRemaining?: number;
    /** 加载速度（资源/秒） */
    loadingSpeed?: number;
}
/**
 * 增强资源预加载器选项
 */
export interface EnhancedResourcePreloaderOptions {
    /** 资源管理器 */
    resourceManager: EnhancedResourceManager;
    /** 依赖管理器 */
    dependencyManager?: EnhancedResourceDependencyManager;
    /** 是否自动注册资源 */
    autoRegisterAssets?: boolean;
    /** 是否自动加载依赖组 */
    autoLoadDependencies?: boolean;
    /** 最大并发加载数 */
    maxConcurrentLoads?: number;
    /** 重试次数 */
    retryCount?: number;
    /** 重试延迟（毫秒） */
    retryDelay?: number;
    /** 是否启用进度估计 */
    enableProgressEstimation?: boolean;
    /** 是否启用调试模式 */
    debug?: boolean;
}
/**
 * 增强资源预加载器
 */
export declare class EnhancedResourcePreloader extends EventEmitter {
    /** 资源管理器 */
    private resourceManager;
    /** 依赖管理器 */
    private dependencyManager?;
    /** 是否自动注册资源 */
    private autoRegisterAssets;
    /** 是否自动加载依赖组 */
    private autoLoadDependencies;
    /** 最大并发加载数 */
    private maxConcurrentLoads;
    /** 当前并发加载数 */
    private currentConcurrentLoads;
    /** 重试次数 */
    private retryCount;
    /** 重试延迟（毫秒） */
    private retryDelay;
    /** 是否启用进度估计 */
    private enableProgressEstimation;
    /** 是否启用调试模式 */
    private debug;
    /** 预加载组映射 */
    private groups;
    /** 预加载进度映射 */
    private progress;
    /** 当前加载组 */
    private currentGroup;
    /** 加载队列 */
    private loadQueue;
    /** 是否正在加载 */
    private loading;
    /** 是否暂停 */
    private paused;
    /** 加载开始时间 */
    private loadStartTime;
    /** 上次进度更新时间 */
    private lastProgressUpdateTime;
    /** 上次加载资源数 */
    private lastLoadedCount;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建增强资源预加载器实例
     * @param options 预加载器选项
     */
    constructor(options: EnhancedResourcePreloaderOptions);
    /**
     * 初始化预加载器
     */
    initialize(): void;
    /**
     * 添加预加载组
     * @param group 预加载组信息
     * @returns 是否成功添加
     */
    addGroup(group: PreloadGroupInfo): boolean;
    /**
     * 移除预加载组
     * @param name 组名
     * @returns 是否成功移除
     */
    removeGroup(name: string): boolean;
    /**
     * 注册组中的资源
     * @param name 组名
     * @returns 是否成功注册
     */
    private registerGroupAssets;
    /**
     * 重置组进度信息
     * @param name 组名
     */
    private resetProgress;
    /**
     * 更新进度信息
     * @param name 组名
     * @param resourceId 资源ID
     * @param success 是否成功加载
     */
    private updateProgress;
    /**
     * 加载预加载组
     * @param name 组名
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    loadGroup(name: string, onProgress?: (progress: PreloadProgressInfo) => void): Promise<PreloadProgressInfo>;
    /**
     * 暂停加载
     * @returns 是否成功暂停
     */
    pause(): boolean;
    /**
     * 恢复加载
     * @returns 是否成功恢复
     */
    resume(): boolean;
    /**
     * 取消加载
     * @returns 是否成功取消
     */
    cancel(): boolean;
    /**
     * 准备加载队列
     * @param name 组名
     */
    private prepareLoadQueue;
    /**
     * 开始加载
     * @param onProgress 进度回调
     * @returns Promise
     */
    private startLoading;
    /**
     * 加载资源
     * @param resource 资源信息
     * @param retryCount 当前重试次数
     * @returns Promise
     */
    private loadResource;
    /**
     * 获取组信息
     * @param name 组名
     * @returns 组信息
     */
    getGroup(name: string): PreloadGroupInfo | null;
    /**
     * 获取所有组
     * @returns 组信息数组
     */
    getAllGroups(): PreloadGroupInfo[];
    /**
     * 获取进度信息
     * @param name 组名
     * @returns 进度信息
     */
    getProgress(name: string): PreloadProgressInfo | null;
    /**
     * 清除所有组
     */
    clearGroups(): void;
    /**
     * 销毁预加载器
     */
    dispose(): void;
}
