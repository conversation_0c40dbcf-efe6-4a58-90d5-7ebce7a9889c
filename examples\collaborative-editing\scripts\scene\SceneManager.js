/**
 * 场景管理器
 * 负责管理场景、实体和交互
 */
export class SceneManager {
  /**
   * 构造函数
   * @param {World} world 世界对象
   * @param {Scene} scene 场景对象
   * @param {Entity} camera 相机实体
   */
  constructor(world, scene, camera) {
    this.world = world;
    this.scene = scene;
    this.camera = camera;
    this.entities = new Map(); // 实体ID -> 实体对象
    this.selectedEntity = null; // 当前选中的实体
    this.activeTool = 'select'; // 当前激活的工具
    
    // 绑定方法
    this.addObject = this.addObject.bind(this);
    this.deleteSelectedObject = this.deleteSelectedObject.bind(this);
    this.selectEntity = this.selectEntity.bind(this);
    this.setActiveTool = this.setActiveTool.bind(this);
    
    // 设置事件监听器
    this.setupEventListeners();
  }
  
  /**
   * 加载默认场景
   * @returns {Promise<void>}
   */
  async loadDefaultScene() {
    try {
      // 创建地面
      const ground = this.createGround();
      this.addEntityToScene(ground);
      
      // 创建立方体
      const cube = this.createCube();
      this.addEntityToScene(cube);
      
      // 创建球体
      const sphere = this.createSphere();
      this.addEntityToScene(sphere);
      
      // 创建光源
      const light = this.createLight();
      this.addEntityToScene(light);
      
      // 更新场景层级UI
      this.updateSceneHierarchyUI();
      
      console.log('默认场景加载完成');
      
      return true;
    } catch (error) {
      console.error('加载默认场景失败:', error);
      return false;
    }
  }
  
  /**
   * 创建地面
   * @returns {Entity} 地面实体
   */
  createGround() {
    // 在实际应用中，这里应该创建一个真实的地面实体
    // 这里使用模拟数据
    return {
      id: 'ground',
      name: '地面',
      type: 'ground',
      transform: {
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 10, y: 0.1, z: 10 }
      },
      components: [
        {
          type: 'mesh',
          geometry: {
            type: 'box',
            width: 1,
            height: 1,
            depth: 1
          },
          material: {
            type: 'standard',
            color: '#CCCCCC'
          }
        }
      ]
    };
  }
  
  /**
   * 创建立方体
   * @returns {Entity} 立方体实体
   */
  createCube() {
    // 在实际应用中，这里应该创建一个真实的立方体实体
    // 这里使用模拟数据
    return {
      id: 'cube',
      name: '立方体',
      type: 'cube',
      transform: {
        position: { x: -1.5, y: 0.5, z: 0 },
        rotation: { x: 0, y: 0.3, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      components: [
        {
          type: 'mesh',
          geometry: {
            type: 'box',
            width: 1,
            height: 1,
            depth: 1
          },
          material: {
            type: 'standard',
            color: '#FF0000'
          }
        }
      ]
    };
  }
  
  /**
   * 创建球体
   * @returns {Entity} 球体实体
   */
  createSphere() {
    // 在实际应用中，这里应该创建一个真实的球体实体
    // 这里使用模拟数据
    return {
      id: 'sphere',
      name: '球体',
      type: 'sphere',
      transform: {
        position: { x: 1.5, y: 0.5, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      components: [
        {
          type: 'mesh',
          geometry: {
            type: 'sphere',
            radius: 0.5,
            segments: 32
          },
          material: {
            type: 'standard',
            color: '#0000FF'
          }
        }
      ]
    };
  }
  
  /**
   * 创建光源
   * @returns {Entity} 光源实体
   */
  createLight() {
    // 在实际应用中，这里应该创建一个真实的光源实体
    // 这里使用模拟数据
    return {
      id: 'light',
      name: '方向光',
      type: 'light',
      transform: {
        position: { x: 5, y: 10, z: 5 },
        rotation: { x: -0.5, y: 0.5, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      components: [
        {
          type: 'light',
          lightType: 'directional',
          color: '#FFFFFF',
          intensity: 1,
          castShadow: true
        }
      ]
    };
  }
  
  /**
   * 添加实体到场景
   * @param {Entity} entity 实体对象
   */
  addEntityToScene(entity) {
    // 在实际应用中，这里应该将实体添加到场景中
    // 这里只是将实体添加到实体列表
    this.entities.set(entity.id, entity);
  }
  
  /**
   * 从场景中移除实体
   * @param {string} entityId 实体ID
   */
  removeEntityFromScene(entityId) {
    // 在实际应用中，这里应该从场景中移除实体
    // 这里只是从实体列表中移除实体
    this.entities.delete(entityId);
    
    // 如果移除的是当前选中的实体，清除选中状态
    if (this.selectedEntity && this.selectedEntity.id === entityId) {
      this.selectEntity(null);
    }
  }
  
  /**
   * 添加对象
   */
  addObject() {
    // 创建一个新的立方体
    const id = 'cube_' + Math.random().toString(36).substr(2, 9);
    const cube = {
      id: id,
      name: '新建立方体',
      type: 'cube',
      transform: {
        position: { x: 0, y: 0.5, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      components: [
        {
          type: 'mesh',
          geometry: {
            type: 'box',
            width: 1,
            height: 1,
            depth: 1
          },
          material: {
            type: 'standard',
            color: '#00FF00'
          }
        }
      ]
    };
    
    // 添加到场景
    this.addEntityToScene(cube);
    
    // 选中新创建的对象
    this.selectEntity(cube);
    
    // 更新场景层级UI
    this.updateSceneHierarchyUI();
    
    console.log('添加对象:', cube);
  }
  
  /**
   * 删除选中的对象
   */
  deleteSelectedObject() {
    if (!this.selectedEntity) {
      console.warn('没有选中的对象');
      return;
    }
    
    // 从场景中移除实体
    this.removeEntityFromScene(this.selectedEntity.id);
    
    // 更新场景层级UI
    this.updateSceneHierarchyUI();
    
    console.log('删除对象:', this.selectedEntity);
  }
  
  /**
   * 选择实体
   * @param {Entity|null} entity 实体对象
   */
  selectEntity(entity) {
    this.selectedEntity = entity;
    
    // 更新场景层级UI
    this.updateSceneHierarchyUI();
    
    // 更新属性面板UI
    this.updatePropertiesPanelUI();
    
    console.log('选择实体:', entity);
  }
  
  /**
   * 设置激活工具
   * @param {string} tool 工具名称
   */
  setActiveTool(tool) {
    this.activeTool = tool;
    console.log('设置激活工具:', tool);
  }
  
  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 在实际应用中，这里应该设置场景交互的事件监听器
    // 例如鼠标点击、拖动等
  }
  
  /**
   * 更新场景层级UI
   */
  updateSceneHierarchyUI() {
    const hierarchyElement = document.getElementById('scene-hierarchy');
    if (!hierarchyElement) {
      return;
    }
    
    // 清空层级列表
    hierarchyElement.innerHTML = '';
    
    // 添加实体项
    this.entities.forEach(entity => {
      const entityElement = document.createElement('div');
      entityElement.className = `hierarchy-item ${this.selectedEntity && this.selectedEntity.id === entity.id ? 'selected' : ''}`;
      entityElement.setAttribute('data-id', entity.id);
      
      // 获取实体图标
      const iconName = this.getEntityIconName(entity.type);
      
      entityElement.innerHTML = `
        <img src="assets/images/icons/${iconName}.svg" alt="${entity.type}" class="icon">
        <span>${entity.name}</span>
      `;
      
      // 添加点击事件
      entityElement.addEventListener('click', () => {
        this.selectEntity(entity);
      });
      
      hierarchyElement.appendChild(entityElement);
    });
    
    // 更新对象计数器
    const objectCounter = document.getElementById('object-counter');
    if (objectCounter) {
      objectCounter.textContent = `对象: ${this.entities.size}`;
    }
  }
  
  /**
   * 更新属性面板UI
   */
  updatePropertiesPanelUI() {
    const propertiesPanel = document.getElementById('properties-panel');
    if (!propertiesPanel) {
      return;
    }
    
    if (!this.selectedEntity) {
      propertiesPanel.innerHTML = '<div class="no-selection-message">未选择对象</div>';
      return;
    }
    
    // 创建属性面板内容
    let html = '';
    
    // 基本信息
    html += `
      <div class="property-group">
        <div class="property-group-title">基本信息</div>
        <div class="property-row">
          <div class="property-label">名称</div>
          <div class="property-value">
            <input type="text" class="property-input" value="${this.selectedEntity.name}" data-property="name">
          </div>
        </div>
        <div class="property-row">
          <div class="property-label">类型</div>
          <div class="property-value">${this.getEntityTypeName(this.selectedEntity.type)}</div>
        </div>
      </div>
    `;
    
    // 变换信息
    const transform = this.selectedEntity.transform;
    html += `
      <div class="property-group">
        <div class="property-group-title">变换</div>
        <div class="property-row">
          <div class="property-label">位置</div>
          <div class="property-value">
            <div class="property-vector">
              <input type="number" class="property-input" value="${transform.position.x}" data-property="position.x" step="0.1">
              <input type="number" class="property-input" value="${transform.position.y}" data-property="position.y" step="0.1">
              <input type="number" class="property-input" value="${transform.position.z}" data-property="position.z" step="0.1">
            </div>
          </div>
        </div>
        <div class="property-row">
          <div class="property-label">旋转</div>
          <div class="property-value">
            <div class="property-vector">
              <input type="number" class="property-input" value="${transform.rotation.x}" data-property="rotation.x" step="0.1">
              <input type="number" class="property-input" value="${transform.rotation.y}" data-property="rotation.y" step="0.1">
              <input type="number" class="property-input" value="${transform.rotation.z}" data-property="rotation.z" step="0.1">
            </div>
          </div>
        </div>
        <div class="property-row">
          <div class="property-label">缩放</div>
          <div class="property-value">
            <div class="property-vector">
              <input type="number" class="property-input" value="${transform.scale.x}" data-property="scale.x" step="0.1">
              <input type="number" class="property-input" value="${transform.scale.y}" data-property="scale.y" step="0.1">
              <input type="number" class="property-input" value="${transform.scale.z}" data-property="scale.z" step="0.1">
            </div>
          </div>
        </div>
      </div>
    `;
    
    // 组件信息
    if (this.selectedEntity.components && this.selectedEntity.components.length > 0) {
      html += `
        <div class="property-group">
          <div class="property-group-title">组件</div>
      `;
      
      for (const component of this.selectedEntity.components) {
        html += `
          <div class="component-item">
            <div class="component-header">${this.getComponentTypeName(component.type)}</div>
        `;
        
        // 根据组件类型显示不同的属性
        switch (component.type) {
          case 'mesh':
            html += `
              <div class="property-row">
                <div class="property-label">几何体</div>
                <div class="property-value">${this.getGeometryTypeName(component.geometry.type)}</div>
              </div>
              <div class="property-row">
                <div class="property-label">材质</div>
                <div class="property-value">${this.getMaterialTypeName(component.material.type)}</div>
              </div>
              <div class="property-row">
                <div class="property-label">颜色</div>
                <div class="property-value">
                  <input type="color" class="property-input" value="${component.material.color}" data-property="components[0].material.color">
                </div>
              </div>
            `;
            break;
          
          case 'light':
            html += `
              <div class="property-row">
                <div class="property-label">光源类型</div>
                <div class="property-value">${this.getLightTypeName(component.lightType)}</div>
              </div>
              <div class="property-row">
                <div class="property-label">颜色</div>
                <div class="property-value">
                  <input type="color" class="property-input" value="${component.color}" data-property="components[0].color">
                </div>
              </div>
              <div class="property-row">
                <div class="property-label">强度</div>
                <div class="property-value">
                  <input type="number" class="property-input" value="${component.intensity}" data-property="components[0].intensity" step="0.1" min="0" max="10">
                </div>
              </div>
              <div class="property-row">
                <div class="property-label">投射阴影</div>
                <div class="property-value">
                  <input type="checkbox" class="property-input" ${component.castShadow ? 'checked' : ''} data-property="components[0].castShadow">
                </div>
              </div>
            `;
            break;
        }
        
        html += `
          </div>
        `;
      }
      
      html += `
        </div>
      `;
    }
    
    propertiesPanel.innerHTML = html;
    
    // 添加属性输入事件监听器
    const inputs = propertiesPanel.querySelectorAll('.property-input');
    inputs.forEach(input => {
      input.addEventListener('change', (e) => {
        this.updateEntityProperty(e.target);
      });
    });
  }
  
  /**
   * 更新实体属性
   * @param {HTMLElement} input 输入元素
   */
  updateEntityProperty(input) {
    if (!this.selectedEntity) {
      return;
    }
    
    const property = input.getAttribute('data-property');
    let value;
    
    // 根据输入类型获取值
    if (input.type === 'checkbox') {
      value = input.checked;
    } else if (input.type === 'number') {
      value = parseFloat(input.value);
    } else {
      value = input.value;
    }
    
    // 更新实体属性
    this.updateEntityPropertyByPath(this.selectedEntity, property, value);
    
    console.log('更新属性:', property, value);
  }
  
  /**
   * 根据路径更新实体属性
   * @param {Object} entity 实体对象
   * @param {string} path 属性路径
   * @param {*} value 属性值
   */
  updateEntityPropertyByPath(entity, path, value) {
    const parts = path.split('.');
    let current = entity;
    
    // 遍历路径
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      
      // 处理数组索引
      if (part.includes('[') && part.includes(']')) {
        const name = part.substring(0, part.indexOf('['));
        const index = parseInt(part.substring(part.indexOf('[') + 1, part.indexOf(']')));
        
        if (!current[name]) {
          current[name] = [];
        }
        
        if (!current[name][index]) {
          current[name][index] = {};
        }
        
        current = current[name][index];
      } else {
        if (!current[part]) {
          current[part] = {};
        }
        
        current = current[part];
      }
    }
    
    // 设置属性值
    current[parts[parts.length - 1]] = value;
  }
  
  /**
   * 获取实体图标名称
   * @param {string} type 实体类型
   * @returns {string} 图标名称
   */
  getEntityIconName(type) {
    switch (type) {
      case 'ground':
        return 'ground';
      case 'cube':
        return 'cube';
      case 'sphere':
        return 'sphere';
      case 'light':
        return 'light';
      default:
        return 'entity';
    }
  }
  
  /**
   * 获取实体类型名称
   * @param {string} type 实体类型
   * @returns {string} 类型名称
   */
  getEntityTypeName(type) {
    switch (type) {
      case 'ground':
        return '地面';
      case 'cube':
        return '立方体';
      case 'sphere':
        return '球体';
      case 'light':
        return '光源';
      default:
        return type;
    }
  }
  
  /**
   * 获取组件类型名称
   * @param {string} type 组件类型
   * @returns {string} 类型名称
   */
  getComponentTypeName(type) {
    switch (type) {
      case 'mesh':
        return '网格';
      case 'light':
        return '光源';
      default:
        return type;
    }
  }
  
  /**
   * 获取几何体类型名称
   * @param {string} type 几何体类型
   * @returns {string} 类型名称
   */
  getGeometryTypeName(type) {
    switch (type) {
      case 'box':
        return '立方体';
      case 'sphere':
        return '球体';
      default:
        return type;
    }
  }
  
  /**
   * 获取材质类型名称
   * @param {string} type 材质类型
   * @returns {string} 类型名称
   */
  getMaterialTypeName(type) {
    switch (type) {
      case 'standard':
        return '标准材质';
      default:
        return type;
    }
  }
  
  /**
   * 获取光源类型名称
   * @param {string} type 光源类型
   * @returns {string} 类型名称
   */
  getLightTypeName(type) {
    switch (type) {
      case 'directional':
        return '方向光';
      case 'point':
        return '点光源';
      case 'spot':
        return '聚光灯';
      default:
        return type;
    }
  }
}
