/**
 * 视锥体类
 * 用于级联阴影映射的视锥体分割
 */
import * as THREE from 'three';
/**
 * 视锥体顶点接口
 */
export interface FrustumVertices {
    near: THREE.Vector3[];
    far: THREE.Vector3[];
}
/**
 * 视锥体参数接口
 */
export interface FrustumParams {
    projectionMatrix?: THREE.Matrix4;
    maxFar?: number;
}
/**
 * 视锥体类
 */
export declare class Frustum {
    /**
     * 视锥体顶点
     */
    vertices: FrustumVertices;
    /**
     * 创建视锥体
     * @param params 视锥体参数
     */
    constructor(params?: FrustumParams);
    /**
     * 从投影矩阵设置视锥体
     * @param projectionMatrix 投影矩阵
     * @param maxFar 最大远平面距离
     */
    setFromProjectionMatrix(projectionMatrix: THREE.Matrix4, maxFar: number): void;
    /**
     * 从透视投影矩阵设置视锥体
     * @param projectionMatrix 投影矩阵
     * @param maxFar 最大远平面距离
     */
    private setFromPerspectiveProjectionMatrix;
    /**
     * 从正交投影矩阵设置视锥体
     * @param projectionMatrix 投影矩阵
     * @param maxFar 最大远平面距离
     */
    private setFromOrthographicProjectionMatrix;
    /**
     * 获取包围盒
     * @param box 包围盒
     * @returns 包围盒
     */
    getBoundingBox(box?: THREE.Box3): THREE.Box3;
    /**
     * 应用矩阵变换
     * @param matrix 变换矩阵
     */
    applyMatrix4(matrix: THREE.Matrix4): void;
    /**
     * 复制视锥体
     * @param frustum 源视锥体
     * @returns 当前视锥体
     */
    copy(frustum: Frustum): Frustum;
    /**
     * 分割视锥体
     * @param breaks 分割点
     * @param result 结果数组
     * @returns 分割后的视锥体数组
     */
    split(breaks: number[], result?: Frustum[]): Frustum[];
}
