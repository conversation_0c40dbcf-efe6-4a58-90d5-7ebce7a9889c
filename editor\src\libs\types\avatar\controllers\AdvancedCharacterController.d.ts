/**
 * 高级角色控制器
 * 提供更丰富的角色控制功能，包括高级动作控制、环境感知和响应
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { type EventCallback } from '../../utils/EventEmitter';
import { CharacterControllerOptions } from '../../physics/character/CharacterController';
/**
 * 角色移动模式
 */
export declare enum CharacterMovementMode {
    /** 行走模式 */
    WALKING = "walking",
    /** 跑步模式 */
    RUNNING = "running",
    /** 潜行模式 */
    CROUCHING = "crouching",
    /** 爬行模式 */
    CRAWLING = "crawling",
    /** 游泳模式 */
    SWIMMING = "swimming",
    /** 攀爬模式 */
    CLIMBING = "climbing",
    /** 飞行模式 */
    FLYING = "flying"
}
/**
 * 角色状态
 */
export declare enum CharacterState {
    /** 空闲 */
    IDLE = "idle",
    /** 行走 */
    WALK = "walk",
    /** 跑步 */
    RUN = "run",
    /** 跳跃 */
    JUMP = "jump",
    /** 下落 */
    FALL = "fall",
    /** 着陆 */
    LAND = "land",
    /** 潜行 */
    CROUCH = "crouch",
    /** 爬行 */
    CRAWL = "crawl",
    /** 游泳 */
    SWIM = "swim",
    /** 攀爬 */
    CLIMB = "climb",
    /** 飞行 */
    FLY = "fly",
    /** 受伤 */
    HURT = "hurt",
    /** 死亡 */
    DEATH = "death",
    /** 交互 */
    INTERACT = "interact",
    /** 攻击 */
    ATTACK = "attack",
    /** 防御 */
    DEFEND = "defend",
    /** 使用物品 */
    USE_ITEM = "use_item"
}
/**
 * 高级角色控制器配置
 */
export interface AdvancedCharacterControllerConfig {
    /** 行走速度 */
    walkSpeed?: number;
    /** 跑步速度 */
    runSpeed?: number;
    /** 潜行速度 */
    crouchSpeed?: number;
    /** 爬行速度 */
    crawlSpeed?: number;
    /** 游泳速度 */
    swimSpeed?: number;
    /** 攀爬速度 */
    climbSpeed?: number;
    /** 飞行速度 */
    flySpeed?: number;
    /** 跳跃力量 */
    jumpForce?: number;
    /** 重力 */
    gravity?: number;
    /** 转向速度 */
    turnSpeed?: number;
    /** 空中控制系数 */
    airControl?: number;
    /** 是否使用物理系统 */
    usePhysics?: boolean;
    /** 是否使用动画状态机 */
    useStateMachine?: boolean;
    /** 是否使用混合空间 */
    useBlendSpace?: boolean;
    /** 是否使用IK */
    useIK?: boolean;
    /** 是否使用环境感知 */
    useEnvironmentAwareness?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
    /** 物理控制器选项 */
    physicsControllerOptions?: CharacterControllerOptions;
}
/**
 * 高级角色控制器
 */
export declare class AdvancedCharacterController {
    /** 关联的实体 */
    private entity;
    /** 世界 */
    private world;
    /** 物理系统 */
    private physicsSystem;
    /** 动画系统 */
    private animationSystem;
    /** 输入系统 */
    private inputSystem;
    /** 物理控制器 */
    private physicsController;
    /** 动画状态机 */
    private stateMachine;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 当前移动模式 */
    private movementMode;
    /** 当前状态 */
    private currentState;
    /** 移动方向 */
    private moveDirection;
    /** 速度 */
    private velocity;
    /** 是否在地面上 */
    private isGrounded;
    /** 是否正在跳跃 */
    private isJumping;
    /** 是否正在跑步 */
    private isRunning;
    /** 是否正在潜行 */
    private isCrouching;
    /** 是否正在爬行 */
    private isCrawling;
    /** 是否正在游泳 */
    private isSwimming;
    /** 是否正在攀爬 */
    private isClimbing;
    /** 是否正在飞行 */
    private isFlying;
    /** 是否正在交互 */
    private isInteracting;
    /** 是否正在攻击 */
    private isAttacking;
    /** 是否正在防御 */
    private isDefending;
    /** 是否正在使用物品 */
    private isUsingItem;
    /** 是否已初始化 */
    private initialized;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<AdvancedCharacterControllerConfig>);
    /**
     * 初始化控制器
     * @param entity 实体
     * @param world 世界
     */
    initialize(entity: Entity, world: World): void;
    /**
     * 更新控制器
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 初始化物理控制器
     */
    private initPhysicsController;
    /**
     * 初始化动画状态机
     */
    private initAnimationStateMachine;
    /**
     * 初始化输入处理
     */
    private initInputHandling;
    /**
     * 更新物理
     * @param deltaTime 时间增量（秒）
     */
    private updatePhysics;
    /**
     * 更新动画
     * @param deltaTime 时间增量（秒）
     */
    private updateAnimation;
    /**
     * 更新环境感知
     * @param deltaTime 时间增量（秒）
     */
    private updateEnvironmentAwareness;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: EventCallback): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: EventCallback): void;
    /**
     * 获取当前移动模式
     */
    getMovementMode(): CharacterMovementMode;
    /**
     * 设置移动模式
     * @param mode 移动模式
     */
    setMovementMode(mode: CharacterMovementMode): void;
    /**
     * 获取当前状态
     */
    getCurrentState(): CharacterState;
    /**
     * 设置当前状态
     * @param state 状态
     */
    setCurrentState(state: CharacterState): void;
    /**
     * 获取移动方向
     */
    getMoveDirection(): THREE.Vector3;
    /**
     * 设置移动方向
     * @param direction 移动方向
     */
    setMoveDirection(direction: THREE.Vector3): void;
    /**
     * 获取速度
     */
    getVelocity(): THREE.Vector3;
    /**
     * 设置速度
     * @param velocity 速度
     */
    setVelocity(velocity: THREE.Vector3): void;
    /**
     * 是否在地面上
     */
    getIsGrounded(): boolean;
    /**
     * 设置是否在地面上
     * @param grounded 是否在地面上
     */
    setIsGrounded(grounded: boolean): void;
}
