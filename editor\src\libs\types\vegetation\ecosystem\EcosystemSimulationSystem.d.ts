/**
 * 生态系统模拟系统
 * 用于模拟生态系统中的植被分布规则
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { VegetationComponent } from '../components/VegetationComponent';
import { TerrainComponent } from '../../terrain/components/TerrainComponent';
/**
 * 生态系统模拟系统配置
 */
export interface EcosystemSimulationSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 是否使用生态系统网格 */
    useEcosystemGrid?: boolean;
    /** 生态系统网格分辨率 */
    ecosystemGridResolution?: number;
    /** 是否使用季节变化 */
    useSeasonalChanges?: boolean;
    /** 是否使用植被竞争 */
    useVegetationCompetition?: boolean;
    /** 是否使用植被生长 */
    useVegetationGrowth?: boolean;
}
/**
 * 生态系统模拟系统事件类型
 */
export declare enum EcosystemSimulationSystemEventType {
    /** 生态系统更新 */
    ECOSYSTEM_UPDATED = "ecosystem_updated",
    /** 植被分布更新 */
    VEGETATION_DISTRIBUTION_UPDATED = "vegetation_distribution_updated",
    /** 季节变化 */
    SEASON_CHANGED = "season_changed"
}
/**
 * 植被类型
 */
export declare enum VegetationType {
    /** 树木 */
    TREE = "tree",
    /** 灌木 */
    SHRUB = "shrub",
    /** 草地 */
    GRASS = "grass",
    /** 花卉 */
    FLOWER = "flower",
    /** 水生植物 */
    AQUATIC = "aquatic",
    /** 季节性植物 */
    SEASONAL = "seasonal",
    /** 自定义植物 */
    CUSTOM = "custom"
}
/**
 * 植被生长阶段
 */
export declare enum VegetationGrowthStage {
    /** 种子 */
    SEED = "seed",
    /** 幼苗 */
    SEEDLING = "seedling",
    /** 幼年 */
    JUVENILE = "juvenile",
    /** 成年 */
    ADULT = "adult",
    /** 老年 */
    SENIOR = "senior",
    /** 死亡 */
    DEAD = "dead"
}
/**
 * 季节类型
 */
export declare enum SeasonType {
    /** 春季 */
    SPRING = "spring",
    /** 夏季 */
    SUMMER = "summer",
    /** 秋季 */
    AUTUMN = "autumn",
    /** 冬季 */
    WINTER = "winter"
}
/**
 * 植被数据
 */
export interface VegetationData {
    /** 植被类型 */
    type: VegetationType;
    /** 生长阶段 */
    growthStage: VegetationGrowthStage;
    /** 年龄 */
    age: number;
    /** 健康度 */
    health: number;
    /** 竞争力 */
    competitiveness: number;
    /** 耐旱性 */
    droughtTolerance: number;
    /** 耐寒性 */
    coldTolerance: number;
    /** 耐热性 */
    heatTolerance: number;
    /** 耐湿性 */
    wetTolerance: number;
    /** 光照需求 */
    lightRequirement: number;
    /** 营养需求 */
    nutrientRequirement: number;
    /** 生长速度 */
    growthRate: number;
    /** 最大寿命 */
    maxAge: number;
    /** 最大高度 */
    maxHeight: number;
    /** 最大宽度 */
    maxWidth: number;
    /** 当前高度 */
    currentHeight: number;
    /** 当前宽度 */
    currentWidth: number;
    /** 是否常绿 */
    evergreen: boolean;
    /** 是否开花 */
    flowering: boolean;
    /** 开花季节 */
    floweringSeason: SeasonType[];
    /** 是否结果 */
    fruiting: boolean;
    /** 结果季节 */
    fruitingSeason: SeasonType[];
    /** 是否落叶 */
    deciduous: boolean;
    /** 落叶季节 */
    leafFallSeason: SeasonType[];
    /** 自定义数据 */
    userData?: any;
}
/**
 * 生态系统网格单元
 */
export interface EcosystemGridCell {
    /** 位置 */
    position: THREE.Vector3;
    /** 大小 */
    size: THREE.Vector3;
    /** 植被数据列表 */
    vegetationData: VegetationData[];
    /** 土壤湿度 */
    soilMoisture: number;
    /** 土壤肥力 */
    soilFertility: number;
    /** 光照强度 */
    lightIntensity: number;
    /** 温度 */
    temperature: number;
    /** 高度 */
    elevation: number;
    /** 坡度 */
    slope: number;
    /** 坡向 */
    aspect: number;
    /** 是否水域 */
    isWater: boolean;
    /** 自定义数据 */
    userData?: any;
}
/**
 * 生态系统模拟系统
 */
export declare class EcosystemSimulationSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 配置 */
    private config;
    /** 是否系统启用 */
    private systemEnabled;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 帧计数器 */
    private frameCount;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 是否使用生态系统网格 */
    private useEcosystemGrid;
    /** 生态系统网格分辨率 */
    private ecosystemGridResolution;
    /** 是否使用季节变化 */
    private useSeasonalChanges;
    /** 是否使用植被竞争 */
    private useVegetationCompetition;
    /** 是否使用植被生长 */
    private useVegetationGrowth;
    /** 生态系统网格 */
    private ecosystemGrid;
    /** 植被组件列表 */
    private vegetationComponents;
    /** 地形组件列表 */
    private terrainComponents;
    /** 当前季节 */
    private currentSeason;
    /** 季节变化计时器 */
    private seasonChangeTimer;
    /** 季节持续时间（秒） */
    private seasonDuration;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监控器 */
    private performanceMonitor;
    /** 调试网格列表 */
    private debugMeshes;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: EcosystemSimulationSystemConfig);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 销毁系统
     */
    destroy(): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 实体添加事件处理
     * @param entity 实体
     */
    private onEntityAdded;
    /**
     * 实体移除事件处理
     * @param entity 实体
     */
    private onEntityRemoved;
    /**
     * 添加植被组件
     * @param entity 实体
     * @param component 植被组件
     */
    addVegetationComponent(entity: Entity, component: VegetationComponent): void;
    /**
     * 移除植被组件
     * @param entity 实体
     */
    removeVegetationComponent(entity: Entity): void;
    /**
     * 添加地形组件
     * @param entity 实体
     * @param component 地形组件
     */
    addTerrainComponent(entity: Entity, component: TerrainComponent): void;
    /**
     * 移除地形组件
     * @param entity 实体
     */
    removeTerrainComponent(entity: Entity): void;
    /**
     * 初始化生态系统网格
     */
    private initEcosystemGrid;
    /**
     * 更新生态系统网格
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateEcosystemGrid;
    /**
     * 更新季节
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateSeason;
    /**
     * 更新植被生长
     * @param cell 生态系统网格单元
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateVegetationGrowth;
    /**
     * 更新植被竞争
     * @param cell 生态系统网格单元
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateVegetationCompetition;
    /**
     * 更新植被分布
     */
    private updateVegetationDistribution;
    /**
     * 更新植被组件分布
     * @param entity 实体
     * @param component 植被组件
     */
    private updateVegetationComponentDistribution;
    /**
     * 更新生长阶段
     * @param vegetation 植被数据
     */
    private updateGrowthStage;
    /**
     * 更新植被大小
     * @param vegetation 植被数据
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateVegetationSize;
    /**
     * 更新季节性特征
     * @param vegetation 植被数据
     * @param season 季节
     */
    private updateSeasonalFeatures;
    /**
     * 计算植被健康度
     * @param vegetation 植被数据
     * @param cell 生态系统网格单元
     * @returns 健康度
     */
    private calculateVegetationHealth;
    /**
     * 计算水分因子
     * @param vegetation 植被数据
     * @param moisture 土壤湿度
     * @returns 水分因子
     */
    private calculateMoistureFactor;
    /**
     * 计算温度因子
     * @param vegetation 植被数据
     * @param temperature 温度
     * @returns 温度因子
     */
    private calculateTemperatureFactor;
    /**
     * 计算光照因子
     * @param vegetation 植被数据
     * @param lightIntensity 光照强度
     * @returns 光照因子
     */
    private calculateLightFactor;
    /**
     * 计算营养因子
     * @param vegetation 植被数据
     * @param fertility 土壤肥力
     * @returns 营养因子
     */
    private calculateNutrientFactor;
    /**
     * 计算竞争因子
     * @param vegetation 植被数据
     * @param otherVegetation 其他植被数据
     * @returns 竞争因子
     */
    private calculateCompetitionFactor;
    /**
     * 计算土壤湿度
     * @param x X坐标
     * @param z Z坐标
     * @param y 高度
     * @param terrainComponent 地形组件
     * @returns 土壤湿度
     */
    private calculateSoilMoisture;
    /**
     * 计算水域接近度
     * @param x X坐标
     * @param z Z坐标
     * @param terrainComponent 地形组件
     * @returns 水域接近度
     */
    private calculateWaterProximity;
    /**
     * 计算土壤肥力
     * @param x X坐标
     * @param z Z坐标
     * @param y 高度
     * @param terrainComponent 地形组件
     * @returns 土壤肥力
     */
    private calculateSoilFertility;
    /**
     * 计算光照强度
     * @param x X坐标
     * @param z Z坐标
     * @param y 高度
     * @param normal 法线
     * @returns 光照强度
     */
    private calculateLightIntensity;
    /**
     * 计算温度
     * @param x X坐标
     * @param z Z坐标
     * @param y 高度
     * @param season 季节
     * @returns 温度
     */
    private calculateTemperature;
    /**
     * 获取地形法线
     * @param terrainComponent 地形组件
     * @param x X坐标
     * @param z Z坐标
     * @returns 法线
     */
    private getTerrainNormal;
    /**
     * 计算坡度
     * @param normal 法线
     * @returns 坡度（度）
     */
    private calculateSlope;
    /**
     * 计算坡向
     * @param normal 法线
     * @returns 坡向（度）
     */
    private calculateAspect;
    /**
     * 柏林噪声
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     * @returns 噪声值
     */
    private perlinNoise;
    /**
     * 柏林噪声辅助函数：fade
     * @param t 参数
     * @returns 结果
     */
    private fade;
    /**
     * 柏林噪声辅助函数：lerp
     * @param t 参数
     * @param a 参数
     * @param b 参数
     * @returns 结果
     */
    private lerp;
    /**
     * 柏林噪声辅助函数：grad
     * @param hash 参数
     * @param x 参数
     * @param y 参数
     * @param z 参数
     * @returns 结果
     */
    private grad;
    /**
     * 柏林噪声辅助数组
     */
    private p;
    /**
     * 初始化柏林噪声
     */
    private initPerlinNoise;
    /**
     * 初始化调试可视化
     */
    private initDebugVisualization;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 获取网格单元颜色
     * @param cell 生态系统网格单元
     * @returns 颜色
     */
    private getCellColor;
    /**
     * 清除调试网格
     */
    private clearDebugMeshes;
}
