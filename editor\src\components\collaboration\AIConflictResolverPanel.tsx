/**
 * AI冲突解析器面板组件
 * 用于显示AI生成的冲突解决方案
 */
import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  Card,
  List,
  Button,
  Space,
  Tooltip,
  Divider,
  Typography,
  Tag,
  Alert,
  Switch,
  Slider,
  Empty,
  Badge,
  Progress,
  Tabs,
  Select,
  Spin,
  Modal
} from 'antd';
import {
  RobotOutlined,
  CheckOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  BulbOutlined,
  LoadingOutlined,
  QuestionCircleOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { RootState, store } from '../../store';
import {
  AIResolution,
  AIModelType,
  AIResolverStatus,
  aiConflictResolverService
} from '../../services/AIConflictResolverService';
import {
  setAIResolverEnabled,
  setDefaultModel,
  setAutoApply,
  setAutoApplyThreshold,
  setConfidenceThreshold,
  setSelectedAIResolutionId,
  clearAppliedAIResolutions
} from '../../store/collaboration/aiResolverSlice';
import { formatDateTime, formatRelativeTime } from '../../utils/formatters';
import JsonView from '../common/JsonView';

const { Title, Text } = Typography;
const { Option } = Select;
const { confirm } = Modal;

/**
 * AI冲突解析器面板组件
 */
const AIConflictResolverPanel: React.FC<{ conflictId?: string }> = ({ conflictId }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取状态
  const enabled = useSelector((state: RootState) => state.aiResolver.enabled);
  const status = useSelector((state: RootState) => state.aiResolver.status);
  const resolutions = useSelector((state: RootState) => state.aiResolver.resolutions);
  const selectedResolutionId = useSelector((state: RootState) => state.aiResolver.selectedResolutionId);
  const defaultModel = useSelector((state: RootState) => state.aiResolver.defaultModel);
  const autoApply = useSelector((state: RootState) => state.aiResolver.autoApply);
  const autoApplyThreshold = useSelector((state: RootState) => state.aiResolver.autoApplyThreshold);
  const confidenceThreshold = useSelector((state: RootState) => state.aiResolver.confidenceThreshold);

  // 本地状态
  const [activeTab, setActiveTab] = useState<string>('resolutions');
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [processing, setProcessing] = useState<boolean>(false);
  const [selectedConflictId, setSelectedConflictId] = useState<string | undefined>(conflictId);

  // 过滤解决方案
  const filteredResolutions = selectedConflictId
    ? resolutions.filter(r => r.conflictId === selectedConflictId)
    : resolutions;

  const pendingResolutions = filteredResolutions.filter(r => !r.applied);
  const appliedResolutions = filteredResolutions.filter(r => r.applied);

  // 选中的解决方案
  const selectedResolution = resolutions.find(r => r.id === selectedResolutionId);

  // 处理启用/禁用AI解析器
  const handleToggleEnabled = (checked: boolean) => {
    dispatch(setAIResolverEnabled(checked));
    aiConflictResolverService.setEnabled(checked);
  };

  // 处理默认模型变更
  const handleModelChange = (value: AIModelType) => {
    dispatch(setDefaultModel(value));
    aiConflictResolverService.updateConfig({ defaultModel: value });
  };

  // 处理自动应用变更
  const handleAutoApplyChange = (checked: boolean) => {
    dispatch(setAutoApply(checked));
    aiConflictResolverService.updateConfig({ autoApply: checked });
  };

  // 处理自动应用阈值变更
  const handleAutoApplyThresholdChange = (value: number) => {
    dispatch(setAutoApplyThreshold(value));
    aiConflictResolverService.updateConfig({ autoApplyThreshold: value });
  };

  // 处理置信度阈值变更
  const handleConfidenceThresholdChange = (value: number) => {
    dispatch(setConfidenceThreshold(value));
    aiConflictResolverService.updateConfig({ confidenceThreshold: value });
  };

  // 处理选择解决方案
  const handleSelectResolution = (id: string) => {
    dispatch(setSelectedAIResolutionId(id));
  };

  // 处理应用解决方案
  const handleApplyResolution = (id: string) => {
    confirm({
      title: t('collaboration.aiResolver.confirmApply') || '确认应用',
      icon: <QuestionCircleOutlined />,
      content: t('collaboration.aiResolver.confirmApplyDescription') || '确定要应用这个AI解决方案吗？',
      onOk() {
        aiConflictResolverService.applyResolution(id);
      }
    });
  };

  // 处理解析冲突
  const handleResolveConflict = async () => {
    if (!selectedConflictId) {
      return;
    }

    // 获取冲突
    const conflict = store.getState().conflict.conflicts.find(c => c.id === selectedConflictId);

    if (!conflict) {
      return;
    }

    setProcessing(true);

    try {
      await aiConflictResolverService.resolveConflict(conflict);
    } catch (error) {
      console.error('解析冲突时出错:', error);
    } finally {
      setProcessing(false);
    }
  };

  // 处理清除已应用的解决方案
  const handleClearApplied = () => {
    dispatch(clearAppliedAIResolutions());
  };

  // 渲染置信度
  const renderConfidence = (confidence: number) => {
    let color = 'green';

    if (confidence < 0.6) {
      color = 'red';
    } else if (confidence < 0.8) {
      color = 'orange';
    }

    return (
      <Tooltip title={`置信度: ${Math.round(confidence * 100)}%`}>
        <Progress
          percent={Math.round(confidence * 100)}
          size="small"
          strokeColor={color}
          showInfo={false}
        />
      </Tooltip>
    );
  };

  // 渲染模型类型标签
  const renderModelTypeTag = (type: AIModelType) => {
    let color = 'blue';
    let text = '基础模型';

    switch (type) {
      case AIModelType.ADVANCED:
        color = 'green';
        text = '高级模型';
        break;

      case AIModelType.EXPERT:
        color = 'purple';
        text = '专家模型';
        break;

      case AIModelType.CUSTOM:
        color = 'orange';
        text = '自定义模型';
        break;
    }

    return <Tag color={color}>{text}</Tag>;
  };

  // 渲染解决方案项
  const renderResolutionItem = (resolution: AIResolution) => {
    const { id, modelType, confidence, explanation, createdAt, applied, appliedAt } = resolution;
    const isSelected = id === selectedResolutionId;

    return (
      <List.Item
        key={id}
        className={`resolution-item ${isSelected ? 'selected' : ''} ${applied ? 'applied' : ''}`}
        actions={[
          <Button
            key="view"
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleSelectResolution(id)}
          />,
          !applied && (
            <Button
              key="apply"
              type="text"
              icon={<CheckOutlined />}
              onClick={() => handleApplyResolution(id)}
            />
          )
        ]}
      >
        <List.Item.Meta
          avatar={
            <Badge status={applied ? 'success' : 'processing'} />
          }
          title={
            <Space>
              {renderModelTypeTag(modelType)}
              <Text strong>
                {explanation.length > 30 ? explanation.substring(0, 30) + '...' : explanation}
              </Text>
            </Space>
          }
          description={
            <Space direction="vertical" style={{ width: '100%' }}>
              {renderConfidence(confidence)}
              <Text type="secondary">
                {formatRelativeTime(createdAt)}
                {applied && appliedAt && (
                  <span> · 已应用于 {formatRelativeTime(appliedAt)}</span>
                )}
              </Text>
            </Space>
          }
        />
      </List.Item>
    );
  };

  // 渲染设置面板
  const renderSettingsPanel = () => {
    return (
      <div className="settings-panel">
        <Title level={5}>{t('collaboration.aiResolver.settings') || '设置'}</Title>

        <div className="setting-item">
          <Text>{t('collaboration.aiResolver.enableAI') || '启用AI'}</Text>
          <Switch
            checked={enabled}
            onChange={handleToggleEnabled}
          />
        </div>

        <div className="setting-item">
          <Text>{t('collaboration.aiResolver.defaultModel') || '默认模型'}</Text>
          <Select
            value={defaultModel}
            onChange={handleModelChange}
            disabled={!enabled}
            style={{ width: 150 }}
          >
            <Option value={AIModelType.BASIC}>{t('collaboration.aiResolver.basicModel') || '基础模型'}</Option>
            <Option value={AIModelType.ADVANCED}>{t('collaboration.aiResolver.advancedModel') || '高级模型'}</Option>
            <Option value={AIModelType.EXPERT}>{t('collaboration.aiResolver.expertModel') || '专家模型'}</Option>
            <Option value={AIModelType.CUSTOM}>{t('collaboration.aiResolver.customModel') || '自定义模型'}</Option>
          </Select>
        </div>

        <div className="setting-item">
          <Text>{t('collaboration.aiResolver.autoApply') || '自动应用'}</Text>
          <Switch
            checked={autoApply}
            onChange={handleAutoApplyChange}
            disabled={!enabled}
          />
        </div>

        <div className="setting-item">
          <Text>{t('collaboration.aiResolver.autoApplyThreshold') || '自动应用阈值'}</Text>
          <Slider
            min={0.5}
            max={1}
            step={0.05}
            value={autoApplyThreshold}
            onChange={handleAutoApplyThresholdChange}
            disabled={!enabled || !autoApply}
            tooltip={{ formatter: (value) => `${Math.round((value || 0) * 100)}%` }}
            style={{ width: 150 }}
          />
        </div>

        <div className="setting-item">
          <Text>{t('collaboration.aiResolver.confidenceThreshold') || '置信度阈值'}</Text>
          <Slider
            min={0.1}
            max={1}
            step={0.05}
            value={confidenceThreshold}
            onChange={handleConfidenceThresholdChange}
            disabled={!enabled}
            tooltip={{ formatter: (value) => `${Math.round((value || 0) * 100)}%` }}
            style={{ width: 150 }}
          />
        </div>
      </div>
    );
  };

  // 渲染选中的解决方案详情
  const renderSelectedResolutionDetails = () => {
    if (!selectedResolution) {
      return (
        <Empty description={t('collaboration.aiResolver.noSelectedResolution') || '未选择解决方案'} />
      );
    }

    const { modelType, confidence, explanation, createdAt, applied, appliedAt, mergeStrategy } = selectedResolution;

    return (
      <div className="selected-resolution-details">
        <Alert
          type={applied ? 'success' : 'info'}
          message={
            <Space>
              {renderModelTypeTag(modelType)}
              <Text strong>
                {t('collaboration.aiResolver.aiSolution') || 'AI解决方案'}
              </Text>
              {applied && (
                <Tag color="green">{t('collaboration.aiResolver.applied') || '已应用'}</Tag>
              )}
            </Space>
          }
          description={
            <Space direction="vertical" style={{ width: '100%' }}>
              <div className="resolution-confidence">
                <Text>{t('collaboration.aiResolver.confidence') || '置信度'}: {Math.round(confidence * 100)}%</Text>
                {renderConfidence(confidence)}
              </div>

              <div className="resolution-explanation">
                <Text>{explanation}</Text>
              </div>

              <div className="resolution-strategy">
                <Text type="secondary">{t('collaboration.aiResolver.mergeStrategy') || '合并策略'}: {mergeStrategy}</Text>
              </div>

              <div className="resolution-time">
                <Text type="secondary">
                  {t('collaboration.aiResolver.createdAt') || '创建时间'}: {formatDateTime(createdAt)}
                </Text>
                {applied && appliedAt && (
                  <div>
                    <Text type="secondary">
                      {t('collaboration.aiResolver.appliedAt') || '应用时间'}: {formatDateTime(appliedAt)}
                    </Text>
                  </div>
                )}
              </div>
            </Space>
          }
          showIcon
        />

        <Divider>{t('collaboration.aiResolver.resolvedData') || '解决后的数据'}</Divider>

        <div className="resolution-data">
          <JsonView data={selectedResolution.resolvedData} />
        </div>

        {!applied && (
          <div className="resolution-actions">
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={() => handleApplyResolution(selectedResolution.id)}
            >
              {t('collaboration.aiResolver.applyResolution') || '应用解决方案'}
            </Button>
          </div>
        )}
      </div>
    );
  };

  // 标签页配置
  const tabItems = [
    {
      key: 'resolutions',
      label: (
        <span>
          <BulbOutlined />
          {t('collaboration.aiResolver.resolutions') || '解决方案'}
          {pendingResolutions.length > 0 && (
            <Badge count={pendingResolutions.length} style={{ marginLeft: 8 }} />
          )}
        </span>
      ),
      children: pendingResolutions.length > 0 ? (
        <List
          dataSource={pendingResolutions}
          renderItem={renderResolutionItem}
          className="resolution-list"
        />
      ) : (
        <Empty description={t('collaboration.aiResolver.noResolutions') || '暂无解决方案'} />
      )
    },
    {
      key: 'applied',
      label: (
        <span>
          <CheckOutlined />
          {t('collaboration.aiResolver.applied') || '已应用'}
          {appliedResolutions.length > 0 && (
            <Badge count={appliedResolutions.length} style={{ marginLeft: 8 }} />
          )}
        </span>
      ),
      children: appliedResolutions.length > 0 ? (
        <List
          dataSource={appliedResolutions}
          renderItem={renderResolutionItem}
          className="resolution-list"
        />
      ) : (
        <Empty description={t('collaboration.aiResolver.noAppliedResolutions') || '暂无已应用的解决方案'} />
      )
    },
    {
      key: 'details',
      label: (
        <span>
          <InfoCircleOutlined />
          {t('collaboration.aiResolver.details') || '详情'}
        </span>
      ),
      children: renderSelectedResolutionDetails()
    }
  ];

  return (
    <Card
      title={
        <Space>
          <RobotOutlined />
          <span>{t('collaboration.aiResolver.title') || 'AI冲突解析器'}</span>
          {status === AIResolverStatus.PROCESSING && (
            <Spin indicator={<LoadingOutlined style={{ fontSize: 16 }} spin />} />
          )}
        </Space>
      }
      extra={
        <Space>
          <Tooltip title={t('collaboration.aiResolver.settings') || '设置'}>
            <Button
              type={showSettings ? 'primary' : 'default'}
              shape="circle"
              icon={<SettingOutlined />}
              onClick={() => setShowSettings(!showSettings)}
            />
          </Tooltip>
          {appliedResolutions.length > 0 && (
            <Button
              size="small"
              onClick={handleClearApplied}
            >
              {t('collaboration.aiResolver.clearApplied') || '清除已应用'}
            </Button>
          )}
        </Space>
      }
      className="ai-resolver-panel"
    >
      {showSettings && (
        <>
          {renderSettingsPanel()}
          <Divider />
        </>
      )}

      <div className="conflict-selector">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>{t('collaboration.aiResolver.selectConflict') || '选择冲突'}</Text>
          <Space>
            <Select
              value={selectedConflictId}
              onChange={setSelectedConflictId}
              placeholder={t('collaboration.aiResolver.selectConflictPlaceholder') || '选择冲突'}
              style={{ width: 300 }}
              allowClear
            >
              {store.getState().conflict.conflicts
                .filter(c => c.status !== 'resolved')
                .map(conflict => (
                  <Option key={conflict.id} value={conflict.id}>
                    {conflict.type} - {conflict.localOperation?.userId || '未知用户'} ({formatRelativeTime(conflict.createdAt)})
                  </Option>
                ))}
            </Select>
            <Button
              type="primary"
              icon={<BulbOutlined />}
              onClick={handleResolveConflict}
              disabled={!selectedConflictId || !enabled || processing}
              loading={processing}
            >
              {t('collaboration.aiResolver.resolveConflict') || '解析冲突'}
            </Button>
          </Space>
        </Space>
      </div>

      <Divider />

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
      />

      <style>{`
        .ai-resolver-panel {
          margin-bottom: 16px;
        }

        .settings-panel {
          padding: 8px;
          background-color: #f7f7f7;
          border-radius: 4px;
        }

        .setting-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
        }

        .conflict-selector {
          margin-top: 16px;
        }

        .resolution-list {
          max-height: 400px;
          overflow-y: auto;
        }

        .resolution-item {
          border-left: 3px solid transparent;
          transition: all 0.3s;
        }

        .resolution-item:hover {
          background-color: #f5f5f5;
        }

        .resolution-item.selected {
          border-left-color: #1890ff;
          background-color: #e6f7ff;
        }

        .resolution-item.applied {
          opacity: 0.7;
        }

        .selected-resolution-details {
          padding: 8px;
        }

        .resolution-data {
          margin-top: 16px;
          max-height: 400px;
          overflow-y: auto;
        }

        .resolution-actions {
          margin-top: 16px;
          display: flex;
          justify-content: flex-end;
        }

        .resolution-confidence, .resolution-explanation, .resolution-strategy, .resolution-time {
          margin-bottom: 8px;
        }
      `}</style>
    </Card>
  );
}

export default AIConflictResolverPanel;