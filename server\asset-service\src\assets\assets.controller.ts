/**
 * 资产控制器
 */
import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards, 
  Request, 
  Query, 
  UseInterceptors, 
  UploadedFile,
  Res,
  StreamableFile
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { MessagePattern } from '@nestjs/microservices';
import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';
import { AssetsService } from './assets.service';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';
import { Asset } from './entities/asset.entity';
import { AssetVersion } from './entities/asset-version.entity';
import { AssetType } from './enums/asset.enums';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('资产')
@Controller('assets')
export class AssetsController {
  constructor(private readonly assetsService: AssetsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建资产' })
  @ApiResponse({ status: 201, description: '资产创建成功', type: Asset })
  async create(@Request() req, @Body() createAssetDto: CreateAssetDto): Promise<Asset> {
    return this.assetsService.create(req.user.id, createAssetDto);
  }

  @Post('upload')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '上传资产文件' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        name: {
          type: 'string',
        },
        description: {
          type: 'string',
        },
        type: {
          type: 'string',
          enum: Object.values(AssetType),
        },
        projectId: {
          type: 'string',
        },
        tags: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({ status: 201, description: '资产上传成功', type: Asset })
  async upload(
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
    @Body() createAssetDto: CreateAssetDto,
  ): Promise<Asset> {
    // 解析标签
    if (createAssetDto.tags && typeof createAssetDto.tags === 'string') {
      createAssetDto.tags = (createAssetDto.tags as unknown as string).split(',');
    }
    
    return this.assetsService.create(req.user.id, createAssetDto, file);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有资产' })
  @ApiResponse({ status: 200, description: '返回所有资产', type: [Asset] })
  async findAll(
    @Request() req,
    @Query('projectId') projectId?: string,
    @Query('type') type?: AssetType,
    @Query('tags') tags?: string,
  ): Promise<Asset[]> {
    const tagArray = tags ? tags.split(',') : undefined;
    return this.assetsService.findAll(req.user.id, projectId, type, tagArray);
  }

  @Get('search')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索资产' })
  @ApiResponse({ status: 200, description: '返回搜索结果', type: [Asset] })
  async search(
    @Request() req,
    @Query('query') query: string,
    @Query('type') type?: AssetType,
  ): Promise<Asset[]> {
    return this.assetsService.search(query, req.user.id, type);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取资产' })
  @ApiResponse({ status: 200, description: '返回资产信息', type: Asset })
  async findOne(@Param('id') id: string, @Request() req): Promise<Asset> {
    return this.assetsService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新资产' })
  @ApiResponse({ status: 200, description: '资产更新成功', type: Asset })
  async update(
    @Param('id') id: string,
    @Request() req,
    @Body() updateAssetDto: UpdateAssetDto,
  ): Promise<Asset> {
    return this.assetsService.update(id, req.user.id, updateAssetDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除资产' })
  @ApiResponse({ status: 204, description: '资产删除成功' })
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    return this.assetsService.remove(id, req.user.id);
  }

  @Post(':id/versions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '上传新版本' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({ status: 201, description: '新版本上传成功', type: AssetVersion })
  async uploadVersion(
    @Param('id') id: string,
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
  ): Promise<AssetVersion> {
    return this.assetsService.uploadVersion(id, req.user.id, file);
  }

  @Get(':id/versions/:versionId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取资产版本' })
  @ApiResponse({ status: 200, description: '返回资产版本信息', type: AssetVersion })
  async getVersion(
    @Param('id') id: string,
    @Param('versionId') versionId: string,
    @Request() req,
  ): Promise<AssetVersion> {
    return this.assetsService.getVersion(id, versionId, req.user.id);
  }

  @Get(':id/versions/:versionId/download')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '下载资产文件' })
  @ApiResponse({ status: 200, description: '返回资产文件' })
  async downloadFile(
    @Param('id') id: string,
    @Param('versionId') versionId: string,
    @Request() req,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    const filePath = await this.assetsService.getAssetFilePath(id, versionId, req.user.id);
    
    const file = fs.createReadStream(filePath);
    const fileName = path.basename(filePath);
    
    res.set({
      'Content-Disposition': `attachment; filename="${fileName}"`,
    });
    
    return new StreamableFile(file);
  }

  // 微服务消息处理
  @MessagePattern({ cmd: 'findAssetById' })
  async handleFindAssetById(data: { id: string; userId: string }): Promise<Asset> {
    return this.assetsService.findOne(data.id, data.userId);
  }
}
