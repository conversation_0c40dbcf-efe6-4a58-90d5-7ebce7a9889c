/**
 * 场景图类
 * 管理场景中实体的层级结构
 */
import type { Entity } from '../core/Entity';
import { Scene } from './Scene';
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 场景图节点接口
 */
export interface SceneGraphNode {
    /** 节点ID */
    id: string;
    /** 节点名称 */
    name: string;
    /** 节点类型 */
    type: string;
    /** 子节点列表 */
    children: SceneGraphNode[];
    /** 组件列表 */
    components: string[];
    /** 是否可见 */
    visible: boolean;
    /** 是否锁定 */
    locked: boolean;
    /** 是否展开 */
    expanded?: boolean;
    /** 自定义数据 */
    userData?: Record<string, any>;
}
/**
 * 场景图查询选项
 */
export interface SceneGraphQueryOptions {
    /** 是否包含不可见节点 */
    includeInvisible?: boolean;
    /** 是否包含锁定节点 */
    includeLocked?: boolean;
    /** 是否递归查询子节点 */
    recursive?: boolean;
    /** 最大递归深度 */
    maxDepth?: number;
    /** 组件过滤器 */
    componentFilter?: string[];
    /** 名称过滤器（支持正则表达式） */
    nameFilter?: string | RegExp;
    /** 标签过滤器 */
    tagFilter?: string[];
    /** 自定义过滤函数 */
    customFilter?: (node: SceneGraphNode) => boolean;
}
/**
 * 场景图类
 */
export declare class SceneGraph extends EventEmitter {
    /** 场景实例 */
    private scene;
    /** 根节点 */
    private rootNode;
    /** 节点映射 */
    private nodeMap;
    /** 实体映射 */
    private entityMap;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建场景图实例
     * @param scene 场景实例
     */
    constructor(scene: Scene);
    /**
     * 初始化场景图
     */
    initialize(): void;
    /**
     * 构建场景图
     */
    private buildSceneGraph;
    /**
     * 构建场景图节点
     * @param entity 实体实例
     * @returns 场景图节点
     */
    private buildSceneGraphNode;
    /**
     * 实体添加事件处理
     * @param entity 添加的实体
     */
    private onEntityAdded;
    /**
     * 实体移除事件处理
     * @param entity 移除的实体
     */
    private onEntityRemoved;
    /**
     * 获取根节点
     * @returns 根节点
     */
    getRootNode(): SceneGraphNode | null;
    /**
     * 获取节点
     * @param id 节点ID
     * @returns 节点实例
     */
    getNode(id: string): SceneGraphNode | null;
    /**
     * 获取实体
     * @param id 节点ID
     * @returns 实体实例
     */
    getEntity(id: string): Entity | null;
    /**
     * 查询节点
     * @param options 查询选项
     * @returns 匹配的节点数组
     */
    queryNodes(options?: SceneGraphQueryOptions): SceneGraphNode[];
    /**
     * 递归查询节点
     * @param node 当前节点
     * @param options 查询选项
     * @param depth 当前深度
     * @returns 匹配的节点数组
     */
    private queryNodeRecursive;
    /**
     * 更新场景图
     */
    update(): void;
    /**
     * 销毁场景图
     */
    dispose(): void;
}
