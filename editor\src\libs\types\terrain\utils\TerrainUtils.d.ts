import { TerrainComponent } from '../components/TerrainComponent';
/**
 * 笔刷类型
 */
export declare enum BrushType {
    /** 提升 */
    RAISE = "raise",
    /** 降低 */
    LOWER = "lower",
    /** 平滑 */
    SMOOTH = "smooth",
    /** 平坦化 */
    FLATTEN = "flatten",
    /** 噪声 */
    NOISE = "noise",
    /** 绘制 */
    PAINT = "paint"
}
/**
 * 笔刷形状
 */
export declare enum BrushShape {
    /** 圆形 */
    CIRCLE = "circle",
    /** 方形 */
    SQUARE = "square",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 笔刷参数
 */
export interface BrushParams {
    /** 笔刷类型 */
    type: BrushType;
    /** 笔刷形状 */
    shape: BrushShape;
    /** 笔刷大小 */
    size: number;
    /** 笔刷强度 */
    strength: number;
    /** 笔刷衰减 */
    falloff: number;
    /** 目标高度（用于平坦化） */
    targetHeight?: number;
    /** 噪声种子（用于噪声） */
    noiseSeed?: number;
    /** 噪声比例（用于噪声） */
    noiseScale?: number;
    /** 纹理索引（用于绘制） */
    textureIndex?: number;
}
/**
 * 地形生成参数
 */
export interface TerrainGenerationParams {
    /** 种子 */
    seed: number;
    /** 比例 */
    scale: number;
    /** 持久度 */
    persistence: number;
    /** 八度 */
    octaves: number;
    /** 频率 */
    frequency: number;
    /** 幅度 */
    amplitude: number;
    /** 侵蚀迭代次数 */
    erosionIterations: number;
    /** 侵蚀强度 */
    erosionStrength: number;
}
/**
 * 地形工具类
 */
export declare class TerrainUtils {
    /**
     * 创建地形工具类
     */
    constructor();
    /**
     * 应用笔刷
     * @param terrain 地形组件
     * @param worldX 世界X坐标
     * @param worldZ 世界Z坐标
     * @param params 笔刷参数
     */
    applyBrush(terrain: TerrainComponent, worldX: number, worldZ: number, params: BrushParams): void;
    /**
     * 应用提升笔刷
     */
    private applyRaiseBrush;
    /**
     * 应用降低笔刷
     */
    private applyLowerBrush;
    /**
     * 应用平滑笔刷
     */
    private applySmoothBrush;
    /**
     * 应用平坦化笔刷
     */
    private applyFlattenBrush;
    /**
     * 应用噪声笔刷
     */
    private applyNoiseBrush;
    /**
     * 应用绘制笔刷
     */
    private applyPaintBrush;
    /**
     * 获取笔刷距离
     */
    private getBrushDistance;
    /**
     * 计算衰减
     */
    private calculateFalloff;
    /**
     * Perlin噪声
     */
    private perlinNoise;
    /**
     * 随机梯度
     */
    private randomGradient;
    /**
     * 缓动函数
     */
    private fade;
    /**
     * 线性插值
     */
    private lerp;
}
