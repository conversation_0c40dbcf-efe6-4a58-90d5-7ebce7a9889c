/**
 * 事务参与者注册服务
 */
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DiscoveryService, MetadataScanner, Reflector } from '@nestjs/core';
import { InstanceWrapper } from '@nestjs/core/injector/instance-wrapper';
import { TRANSACTION_PARTICIPANT_METADATA } from '../transaction.constants';
import { TransactionCoordinatorService } from '../transaction-coordinator.service';
import { TransactionParticipantHandler, TransactionParticipantOptions } from '../transaction.interface';

/**
 * 事务参与者注册服务
 */
@Injectable()
export class TransactionParticipantRegistryService implements OnModuleInit {
  private readonly logger = new Logger(TransactionParticipantRegistryService.name);
  private readonly participantHandlers = new Map<string, TransactionParticipantHandler>();
  private readonly participantOptions = new Map<string, TransactionParticipantOptions>();
  
  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
    private readonly reflector: Reflector,
    private readonly transactionCoordinatorService: TransactionCoordinatorService,
  ) {}
  
  /**
   * 模块初始化时探索和注册所有事务参与者
   */
  onModuleInit() {
    this.exploreAndRegisterParticipants();
  }
  
  /**
   * 探索并注册所有事务参与者
   */
  exploreAndRegisterParticipants() {
    const providers = this.discoveryService.getProviders();
    
    // 探索提供者中的事务参与者
    providers.forEach((wrapper) => {
      this.exploreTransactionParticipantsInProvider(wrapper);
    });
    
    this.logger.log(`已注册 ${this.participantHandlers.size} 个事务参与者`);
  }
  
  /**
   * 在提供者中探索事务参与者
   * @param wrapper 提供者包装器
   */
  private exploreTransactionParticipantsInProvider(wrapper: InstanceWrapper) {
    // 跳过非单例提供者
    if (!wrapper.isDependencyTreeStatic()) {
      return;
    }
    
    const { instance } = wrapper;
    
    // 跳过没有实例的提供者
    if (!instance) {
      return;
    }
    
    // 获取事务参与者元数据
    const participantMetadata = this.reflector.get(
      TRANSACTION_PARTICIPANT_METADATA,
      instance.constructor,
    );
    
    if (!participantMetadata) {
      return;
    }
    
    const { participantId, options } = participantMetadata;
    
    // 检查实例是否实现了事务参与者接口
    if (
      typeof instance.prepare === 'function' &&
      typeof instance.commit === 'function' &&
      typeof instance.rollback === 'function'
    ) {
      // 注册事务参与者
      this.registerParticipant(participantId, instance, options);
    } else {
      this.logger.warn(
        `类 ${instance.constructor.name} 被标记为事务参与者，但未实现所需的方法`,
      );
    }
  }
  
  /**
   * 注册事务参与者
   * @param participantId 参与者ID
   * @param handler 参与者处理器
   * @param options 参与者选项
   */
  registerParticipant(
    participantId: string,
    handler: TransactionParticipantHandler,
    options?: TransactionParticipantOptions,
  ): void {
    // 存储参与者处理器
    this.participantHandlers.set(participantId, handler);
    
    // 存储参与者选项
    if (options) {
      this.participantOptions.set(participantId, options);
    }
    
    // 向事务协调器注册参与者
    this.transactionCoordinatorService.registerParticipantHandler(participantId, handler);
    
    this.logger.debug(`已注册事务参与者: ${participantId}`);
  }
  
  /**
   * 注销事务参与者
   * @param participantId 参与者ID
   */
  unregisterParticipant(participantId: string): void {
    // 从事务协调器注销参与者
    this.transactionCoordinatorService.unregisterParticipantHandler(participantId);
    
    // 移除参与者处理器
    this.participantHandlers.delete(participantId);
    
    // 移除参与者选项
    this.participantOptions.delete(participantId);
    
    this.logger.debug(`已注销事务参与者: ${participantId}`);
  }
  
  /**
   * 获取事务参与者处理器
   * @param participantId 参与者ID
   */
  getParticipantHandler(participantId: string): TransactionParticipantHandler | null {
    return this.participantHandlers.get(participantId) || null;
  }
  
  /**
   * 获取事务参与者选项
   * @param participantId 参与者ID
   */
  getParticipantOptions(participantId: string): TransactionParticipantOptions | null {
    return this.participantOptions.get(participantId) || null;
  }
  
  /**
   * 获取所有事务参与者ID
   */
  getAllParticipantIds(): string[] {
    return Array.from(this.participantHandlers.keys());
  }
  
  /**
   * 获取所有事务参与者
   */
  getAllParticipants(): { id: string; handler: TransactionParticipantHandler; options?: TransactionParticipantOptions }[] {
    return Array.from(this.participantHandlers.entries()).map(([id, handler]) => ({
      id,
      handler,
      options: this.participantOptions.get(id),
    }));
  }
}
