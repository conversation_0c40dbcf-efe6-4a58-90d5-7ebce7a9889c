/**
 * 熔断器实现
 */
import { Injectable, Logger } from '@nestjs/common';
import {
  CircuitBreakerOptions,
  CircuitBreakerState,
  CircuitBreakerStats,
  ICircuitBreaker,
} from './circuit-breaker.interface';

/**
 * 熔断器类
 * 实现了断路器模式，用于防止系统级联故障
 */
@Injectable()
export class CircuitBreaker implements ICircuitBreaker {
  private readonly logger = new Logger(CircuitBreaker.name);
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failures = 0;
  private successes = 0;
  private rejects = 0;
  private timeouts = 0;
  private fallbacks = 0;
  private lastStateChangeTime: Date = new Date();
  private lastFailureTime?: Date;
  private lastSuccessTime?: Date;
  private resetTimer?: NodeJS.Timeout;

  /**
   * 创建熔断器
   * @param options 熔断器配置
   */
  constructor(private readonly options: CircuitBreakerOptions) {
    this.logger.log(`熔断器 ${options.name} 已创建`);
  }

  /**
   * 执行受熔断器保护的函数
   * @param fn 要执行的函数
   * @param args 函数参数
   */
  async execute<T>(fn: (...args: any[]) => Promise<T>, ...args: any[]): Promise<T> {
    // 如果熔断器处于开启状态，直接拒绝请求
    if (this.state === CircuitBreakerState.OPEN) {
      this.rejects++;
      this.logger.debug(`熔断器 ${this.options.name} 处于开启状态，拒绝请求`);
      
      // 如果启用了回退，则执行回退函数
      if (this.options.enableFallback && this.options.fallback) {
        this.fallbacks++;
        return this.options.fallback(...args);
      }
      
      throw new Error(`Circuit breaker ${this.options.name} is open`);
    }

    try {
      // 创建超时Promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Circuit breaker ${this.options.name} timeout`));
        }, this.options.timeout);
      });

      // 执行函数，并设置超时
      const result = await Promise.race([fn(...args), timeoutPromise]);

      // 处理成功情况
      this.handleSuccess();
      return result;
    } catch (error) {
      // 处理失败情况
      this.handleFailure(error);
      
      // 如果启用了回退，则执行回退函数
      if (this.options.enableFallback && this.options.fallback) {
        this.fallbacks++;
        return this.options.fallback(...args);
      }
      
      throw error;
    }
  }

  /**
   * 处理成功情况
   */
  private handleSuccess(): void {
    this.lastSuccessTime = new Date();
    
    // 如果熔断器处于半开状态，增加成功计数
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.successes++;
      
      // 如果成功次数达到阈值，关闭熔断器
      if (this.successes >= this.options.successThreshold) {
        this.transitionToClosed();
      }
    }
    
    // 如果熔断器处于关闭状态，重置失败计数
    if (this.state === CircuitBreakerState.CLOSED) {
      this.failures = 0;
    }
  }

  /**
   * 处理失败情况
   * @param error 错误对象
   */
  private handleFailure(error: any): void {
    this.lastFailureTime = new Date();
    
    // 检查是否是超时错误
    if (error.message && error.message.includes('timeout')) {
      this.timeouts++;
    }
    
    // 如果熔断器处于半开状态，立即开启熔断器
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.transitionToOpen();
      return;
    }
    
    // 如果熔断器处于关闭状态，增加失败计数
    if (this.state === CircuitBreakerState.CLOSED) {
      this.failures++;
      
      // 如果失败次数达到阈值，开启熔断器
      if (this.failures >= this.options.failureThreshold) {
        this.transitionToOpen();
      }
    }
  }

  /**
   * 转换到开启状态
   */
  private transitionToOpen(): void {
    if (this.state === CircuitBreakerState.OPEN) {
      return;
    }
    
    const previousState = this.state;
    this.state = CircuitBreakerState.OPEN;
    this.lastStateChangeTime = new Date();
    this.successes = 0;
    
    this.logger.log(`熔断器 ${this.options.name} 状态从 ${previousState} 变为 ${this.state}`);
    
    // 通知状态变更
    if (this.options.onStateChange) {
      this.options.onStateChange(previousState, this.state);
    }
    
    // 设置重置定时器
    this.resetTimer = setTimeout(() => {
      this.transitionToHalfOpen();
    }, this.options.resetTimeout);
  }

  /**
   * 转换到半开状态
   */
  private transitionToHalfOpen(): void {
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      return;
    }
    
    const previousState = this.state;
    this.state = CircuitBreakerState.HALF_OPEN;
    this.lastStateChangeTime = new Date();
    this.successes = 0;
    
    this.logger.log(`熔断器 ${this.options.name} 状态从 ${previousState} 变为 ${this.state}`);
    
    // 通知状态变更
    if (this.options.onStateChange) {
      this.options.onStateChange(previousState, this.state);
    }
  }

  /**
   * 转换到关闭状态
   */
  private transitionToClosed(): void {
    if (this.state === CircuitBreakerState.CLOSED) {
      return;
    }
    
    const previousState = this.state;
    this.state = CircuitBreakerState.CLOSED;
    this.lastStateChangeTime = new Date();
    this.failures = 0;
    this.successes = 0;
    
    this.logger.log(`熔断器 ${this.options.name} 状态从 ${previousState} 变为 ${this.state}`);
    
    // 通知状态变更
    if (this.options.onStateChange) {
      this.options.onStateChange(previousState, this.state);
    }
  }

  /**
   * 获取熔断器状态
   */
  getState(): CircuitBreakerState {
    return this.state;
  }

  /**
   * 获取熔断器统计信息
   */
  getStats(): CircuitBreakerStats {
    return {
      name: this.options.name,
      state: this.state,
      failures: this.failures,
      successes: this.successes,
      rejects: this.rejects,
      timeouts: this.timeouts,
      fallbacks: this.fallbacks,
      lastStateChangeTime: this.lastStateChangeTime,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
    };
  }

  /**
   * 重置熔断器
   */
  reset(): void {
    this.transitionToClosed();
  }

  /**
   * 强制开启熔断器
   */
  forceOpen(): void {
    this.transitionToOpen();
  }

  /**
   * 强制关闭熔断器
   */
  forceClose(): void {
    this.transitionToClosed();
  }
}
