version: '3.8'

services:
  # 监控服务
  monitoring-service:
    build:
      context: ./server/monitoring-service
      dockerfile: Dockerfile
    container_name: monitoring-service
    restart: unless-stopped
    ports:
      - "3100:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - DB_DATABASE=monitoring
      - ELASTICSEARCH_ENABLED=true
      - ELASTICSEARCH_NODE=http://elasticsearch:9200
      - ELASTICSEARCH_INDEX=logs
      - SERVICE_REGISTRY_URL=http://service-registry:3000
      - MAIL_HOST=smtp.example.com
      - MAIL_PORT=587
      - MAIL_USER=<EMAIL>
      - MAIL_PASS=password
      - MAIL_FROM=<EMAIL>
    volumes:
      - ./logs:/app/logs
    depends_on:
      - mysql
      - elasticsearch
      - service-registry
    networks:
      - monitoring-network
      - app-network

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    container_name: elasticsearch
    environment:
      - node.name=elasticsearch
      - cluster.name=dl-engine-es
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
      - "9300:9300"
    networks:
      - monitoring-network

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:7.14.0
    container_name: kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - monitoring-network

  # Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:7.14.0
    container_name: logstash
    environment:
      - xpack.monitoring.enabled=false
    volumes:
      - ./config/logstash/pipeline:/usr/share/logstash/pipeline
      - ./config/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml
    ports:
      - "5044:5044"
      - "5000:5000/tcp"
      - "5000:5000/udp"
      - "9600:9600"
    depends_on:
      - elasticsearch
    networks:
      - monitoring-network

  # Filebeat
  filebeat:
    image: docker.elastic.co/beats/filebeat:7.14.0
    container_name: filebeat
    user: root
    volumes:
      - ./config/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/var/log/dl-engine:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - elasticsearch
      - logstash
    networks:
      - monitoring-network

  # Prometheus
  prometheus:
    image: prom/prometheus:v2.30.0
    container_name: prometheus
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - monitoring-network

  # Grafana
  grafana:
    image: grafana/grafana:8.2.0
    container_name: grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - monitoring-network

  # Node Exporter
  node-exporter:
    image: prom/node-exporter:v1.2.2
    container_name: node-exporter
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - monitoring-network

  # cAdvisor
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.39.3
    container_name: cadvisor
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "8080:8080"
    networks:
      - monitoring-network

  # MySQL
  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=monitoring
    volumes:
      - mysql-data:/var/lib/mysql
      - ./config/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - monitoring-network
      - app-network

networks:
  monitoring-network:
    driver: bridge
  app-network:
    external: true

volumes:
  elasticsearch-data:
  prometheus-data:
  grafana-data:
  mysql-data:
