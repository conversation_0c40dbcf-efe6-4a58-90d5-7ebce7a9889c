/**
 * 动画重定向系统
 * 用于将一个骨骼结构的动画应用到另一个骨骼结构上
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
/**
 * 骨骼映射接口
 */
export interface BoneMapping {
    /** 源骨骼名称 */
    source: string;
    /** 目标骨骼名称 */
    target: string;
}
/**
 * 骨骼重定向配置
 */
export interface RetargetingConfig {
    /** 骨骼映射 */
    boneMapping: BoneMapping[];
    /** 是否保留位置轨道 */
    preservePositionTracks?: boolean;
    /** 是否保留缩放轨道 */
    preserveScaleTracks?: boolean;
    /** 是否规范化旋转 */
    normalizeRotations?: boolean;
    /** 是否调整根骨骼高度 */
    adjustRootHeight?: boolean;
    /** 是否调整骨骼长度 */
    adjustBoneLength?: boolean;
}
/**
 * 动画重定向系统
 */
export declare class AnimationRetargeting {
    /**
     * 重定向动画片段
     * @param sourceClip 源动画片段
     * @param sourceSkeleton 源骨骼
     * @param targetSkeleton 目标骨骼
     * @param config 重定向配置
     * @returns 重定向后的动画片段
     */
    static retargetClip(sourceClip: THREE.AnimationClip, sourceSkeleton: THREE.SkinnedMesh | THREE.Bone[], targetSkeleton: THREE.SkinnedMesh | THREE.Bone[], config: RetargetingConfig): THREE.AnimationClip;
    /**
     * 重定向旋转轨道
     * @param track 源轨道
     * @param sourceBoneName 源骨骼名称
     * @param targetBoneName 目标骨骼名称
     * @param sourceBones 源骨骼数组
     * @param targetBones 目标骨骼数组
     * @param config 重定向配置
     * @returns 重定向后的轨道
     */
    private static retargetRotationTrack;
    /**
     * 重定向位置轨道
     * @param track 源轨道
     * @param sourceBoneName 源骨骼名称
     * @param targetBoneName 目标骨骼名称
     * @param sourceBones 源骨骼数组
     * @param targetBones 目标骨骼数组
     * @param config 重定向配置
     * @returns 重定向后的轨道
     */
    private static retargetPositionTrack;
    /**
     * 重定向缩放轨道
     * @param track 源轨道
     * @param sourceBoneName 源骨骼名称
     * @param targetBoneName 目标骨骼名称
     * @returns 重定向后的轨道
     */
    private static retargetScaleTrack;
    /**
     * 获取骨骼信息
     * @param bone 骨骼
     * @returns 骨骼信息
     */
    private static getBoneInfo;
    /**
     * 重定向旋转
     * @param rotation 源旋转
     * @param sourceBoneInfo 源骨骼信息
     * @param targetBoneInfo 目标骨骼信息
     * @param normalize 是否规范化
     * @returns 重定向后的旋转
     */
    private static retargetRotation;
    /**
     * 获取骨骼长度
     * @param bone 骨骼
     * @returns 骨骼长度
     */
    private static getBoneLength;
    /**
     * 从实体中获取骨骼映射
     * @param entity 实体
     * @returns 骨骼映射
     */
    static getBoneMappingFromEntity(entity: Entity): Map<string, string>;
}
