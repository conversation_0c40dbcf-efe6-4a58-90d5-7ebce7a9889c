/**
 * 批处理请求
 */
export interface BatchRequest<T, R> {
    /** 请求ID */
    id: string;
    /** 输入数据 */
    input: T;
    /** 选项 */
    options?: any;
    /** 解析回调 */
    resolve: (result: R) => void;
    /** 拒绝回调 */
    reject: (error: Error) => void;
    /** 提交时间 */
    submitTime: number;
}
/**
 * 批处理配置
 */
export interface BatchProcessorConfig {
    /** 最大批处理大小 */
    maxBatchSize?: number;
    /** 最大等待时间（毫秒） */
    maxWaitTime?: number;
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否启用动态批处理大小 */
    dynamicBatchSize?: boolean;
    /** 最小批处理大小 */
    minBatchSize?: number;
    /** 性能监控窗口大小 */
    performanceWindowSize?: number;
    /** 批处理超时时间（毫秒） */
    batchTimeout?: number;
    /** 最大队列大小 */
    maxQueueSize?: number;
}
/**
 * 批处理性能统计
 */
export interface BatchPerformanceStats {
    /** 批处理总数 */
    batchCount: number;
    /** 请求总数 */
    requestCount: number;
    /** 平均批处理大小 */
    averageBatchSize: number;
    /** 平均处理时间（毫秒） */
    averageProcessTime: number;
    /** 平均等待时间（毫秒） */
    averageWaitTime: number;
    /** 当前队列大小 */
    queueSize: number;
    /** 当前批处理大小 */
    currentBatchSize: number;
    /** 最大批处理大小 */
    maxBatchSize: number;
    /** 最大等待时间（毫秒） */
    maxWaitTime: number;
}
/**
 * 批处理器
 */
export declare class BatchProcessor<T, R> {
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 请求队列 */
    private queue;
    /** 处理函数 */
    private processFn;
    /** 是否正在处理 */
    private isProcessing;
    /** 定时器 */
    private timer;
    /** 事件发射器 */
    private eventEmitter;
    /** 批处理计数 */
    private batchCount;
    /** 请求计数 */
    private requestCount;
    /** 处理时间历史 */
    private processTimeHistory;
    /** 等待时间历史 */
    private waitTimeHistory;
    /** 批处理大小历史 */
    private batchSizeHistory;
    /** 当前批处理大小 */
    private currentBatchSize;
    /**
     * 构造函数
     * @param processFn 处理函数
     * @param config 配置
     */
    constructor(processFn: (inputs: T[], options?: any[]) => Promise<R[]>, config?: BatchProcessorConfig);
    /**
     * 提交请求
     * @param input 输入数据
     * @param options 选项
     * @returns 处理结果
     */
    process(input: T, options?: any): Promise<R>;
    /**
     * 处理批次
     */
    private processBatch;
    /**
     * 调整批处理大小
     * @param processTime 处理时间
     * @param batchSize 批处理大小
     */
    private adjustBatchSize;
    /**
     * 获取性能统计
     * @returns 性能统计
     */
    getPerformanceStats(): BatchPerformanceStats;
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    on(event: string, listener: (...args: any[]) => void): void;
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    off(event: string, listener: (...args: any[]) => void): void;
    /**
     * 清空队列
     */
    clearQueue(): void;
    /**
     * 销毁
     */
    dispose(): void;
}
