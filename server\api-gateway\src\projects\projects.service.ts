/**
 * 项目服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom, timeout, catchError, throwError } from 'rxjs';

@Injectable()
export class ProjectsService {
  private readonly logger = new Logger(ProjectsService.name);

  constructor(@Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy) {}

  /**
   * 创建项目
   */
  async create(userId: string, createProjectDto: any) {
    try {
      return await firstValueFrom(
        this.projectService.send({ cmd: 'createProject' }, { userId, ...createProjectDto }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error('创建项目失败', error);
      throw error;
    }
  }

  /**
   * 获取所有项目
   */
  async findAll(userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send({ cmd: 'findAllProjects' }, { userId }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error('获取所有项目失败', error);
      throw error;
    }
  }

  /**
   * 获取用户拥有的项目
   */
  async findUserProjects(userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send({ cmd: 'findUserProjects' }, { userId }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error('获取用户项目失败', error);
      throw error;
    }
  }

  /**
   * 根据ID获取项目
   */
  async findOne(id: string, userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send({ cmd: 'findProjectById' }, { id, userId }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`获取项目ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 更新项目
   */
  async update(id: string, userId: string, updateProjectDto: any) {
    try {
      return await firstValueFrom(
        this.projectService.send({ cmd: 'updateProject' }, { id, userId, ...updateProjectDto }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`更新项目ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 删除项目
   */
  async remove(id: string, userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send({ cmd: 'removeProject' }, { id, userId }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`删除项目ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 添加项目成员
   */
  async addMember(projectId: string, userId: string, addMemberDto: any) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'addProjectMember' },
          { projectId, userId, ...addMemberDto },
        ).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`添加项目ID ${projectId} 成员失败`, error);
      throw error;
    }
  }

  /**
   * 更新项目成员
   */
  async updateMember(projectId: string, memberId: string, userId: string, updateMemberDto: any) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'updateProjectMember' },
          { projectId, memberId, userId, ...updateMemberDto },
        ).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`更新项目ID ${projectId} 成员ID ${memberId} 失败`, error);
      throw error;
    }
  }

  /**
   * 删除项目成员
   */
  async removeMember(projectId: string, memberId: string, userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send({ cmd: 'removeProjectMember' }, { projectId, memberId, userId }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`删除项目ID ${projectId} 成员ID ${memberId} 失败`, error);
      throw error;
    }
  }

  /**
   * 创建项目设置
   */
  async createSetting(projectId: string, createSettingDto: any) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'createProjectSetting' },
          { projectId, ...createSettingDto },
        ).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`创建项目ID ${projectId} 设置失败`, error);
      throw error;
    }
  }

  /**
   * 获取项目设置
   */
  async getSetting(projectId: string, key: string) {
    try {
      return await firstValueFrom(
        this.projectService.send({ cmd: 'getProjectSetting' }, { projectId, key }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`获取项目ID ${projectId} 设置 ${key} 失败`, error);
      throw error;
    }
  }

  /**
   * 删除项目设置
   */
  async removeSetting(projectId: string, key: string) {
    try {
      return await firstValueFrom(
        this.projectService.send({ cmd: 'removeProjectSetting' }, { projectId, key }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('项目服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`删除项目ID ${projectId} 设置 ${key} 失败`, error);
      throw error;
    }
  }
}
