import { <PERSON>, Post, Body, Get, Param } from '@nestjs/common';
import { GenerationService } from './generation.service';
import { CreateGenerationTaskDto } from './dto/create-generation-task.dto';

@Controller('generation')
export class GenerationController {
  constructor(private readonly generationService: GenerationService) {}

  @Post('tasks')
  async createTask(@Body() createTaskDto: CreateGenerationTaskDto) {
    return await this.generationService.createGenerationTask(createTaskDto, 'user-id');
  }

  @Get('tasks/:id')
  async getTask(@Param('id') id: string) {
    return await this.generationService.getTaskStatus(id);
  }
}
