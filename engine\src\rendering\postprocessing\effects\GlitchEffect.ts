/**
 * 故障效果
 * 模拟数字信号故障
 */
import * as THREE from 'three';
// 使用类型断言导入 GlitchPass
// @ts-ignore
import { GlitchPass } from 'three/examples/jsm/postprocessing/GlitchPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 故障效果选项
 */
export interface GlitchEffectOptions extends PostProcessingEffectOptions {
  /** 是否使用随机故障 */
  goWild?: boolean;
  /** 故障强度 */
  amount?: number;
  /** 故障间隔（秒） */
  interval?: number;
  /** 故障持续时间（秒） */
  duration?: number;
}

/**
 * 故障效果
 */
export class GlitchEffect extends PostProcessingEffect {
  /** 是否使用随机故障 */
  private goWild: boolean;

  /** 故障强度 */
  private amount: number;

  /** 故障间隔（秒） */
  private interval: number;

  /** 故障持续时间（秒） */
  private duration: number;

  /** 故障通道 */
  private glitchPass: GlitchPass | null = null;

  /** 计时器 */
  private timer: number = 0;

  /** 是否正在故障 */
  private isGlitching: boolean = false;

  /** 故障计时器 */
  private glitchTimer: number = 0;

  /**
   * 创建故障效果
   * @param options 故障效果选项
   */
  constructor(options: GlitchEffectOptions = { name: 'Glitch' }) {
    super(options);

    this.goWild = options.goWild !== undefined ? options.goWild : false;
    this.amount = options.amount !== undefined ? options.amount : 1.0;
    this.interval = options.interval !== undefined ? options.interval : 3.0;
    this.duration = options.duration !== undefined ? options.duration : 0.5;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 创建故障通道
    this.glitchPass = new GlitchPass();

    // 设置参数
    this.glitchPass.goWild = this.goWild;

    // 如果不是随机故障模式，则禁用故障
    if (!this.goWild) {
      // 使用类型断言访问 uniforms
      const uniforms = (this.glitchPass as any).uniforms;
      if (uniforms && uniforms.byp) {
        uniforms.byp.value = 1;
      }
    }

    // 设置通道
    this.pass = this.glitchPass;
  }

  /**
   * 更新效果
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 如果是随机故障模式，则不需要手动控制
    if (this.goWild || !this.glitchPass) return;

    // 更新计时器
    this.timer += deltaTime;

    // 如果正在故障中
    if (this.isGlitching) {
      this.glitchTimer += deltaTime;

      // 如果故障时间结束，则停止故障
      if (this.glitchTimer >= this.duration) {
        this.stopGlitch();
        this.glitchTimer = 0;
        this.isGlitching = false;
      }
    } else {
      // 如果达到故障间隔，则开始故障
      if (this.timer >= this.interval) {
        this.startGlitch();
        this.timer = 0;
        this.isGlitching = true;
      }
    }
  }

  /**
   * 开始故障
   */
  public startGlitch(): void {
    if (!this.glitchPass) return;

    // 使用类型断言访问 uniforms
    const uniforms = (this.glitchPass as any).uniforms;
    if (uniforms && uniforms.byp) {
      uniforms.byp.value = 0;
    }

    // 设置故障强度
    if (uniforms && uniforms.amount) {
      uniforms.amount.value = this.amount;
    }
  }

  /**
   * 停止故障
   */
  public stopGlitch(): void {
    if (!this.glitchPass) return;

    // 使用类型断言访问 uniforms
    const uniforms = (this.glitchPass as any).uniforms;
    if (uniforms && uniforms.byp) {
      uniforms.byp.value = 1;
    }
  }

  /**
   * 设置是否使用随机故障
   * @param goWild 是否使用随机故障
   */
  public setGoWild(goWild: boolean): void {
    this.goWild = goWild;

    if (this.glitchPass) {
      this.glitchPass.goWild = goWild;

      // 如果不是随机故障模式，则禁用故障
      if (!goWild) {
        this.stopGlitch();
      }
    }
  }

  /**
   * 获取是否使用随机故障
   * @returns 是否使用随机故障
   */
  public isGoWild(): boolean {
    return this.goWild;
  }

  /**
   * 设置故障强度
   * @param amount 故障强度
   */
  public setAmount(amount: number): void {
    this.amount = amount;

    if (this.glitchPass && this.isGlitching) {
      // 使用类型断言访问 uniforms
      const uniforms = (this.glitchPass as any).uniforms;
      if (uniforms && uniforms.amount) {
        uniforms.amount.value = amount;
      }
    }
  }

  /**
   * 获取故障强度
   * @returns 故障强度
   */
  public getAmount(): number {
    return this.amount;
  }

  /**
   * 设置故障间隔
   * @param interval 故障间隔（秒）
   */
  public setInterval(interval: number): void {
    this.interval = interval;
  }

  /**
   * 获取故障间隔
   * @returns 故障间隔（秒）
   */
  public getInterval(): number {
    return this.interval;
  }

  /**
   * 设置故障持续时间
   * @param duration 故障持续时间（秒）
   */
  public setDuration(duration: number): void {
    this.duration = duration;
  }

  /**
   * 获取故障持续时间
   * @returns 故障持续时间（秒）
   */
  public getDuration(): number {
    return this.duration;
  }
}
