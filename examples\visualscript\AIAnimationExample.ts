/**
 * 视觉脚本AI动画示例
 * 演示如何使用视觉脚本系统的AI节点生成动画
 */
import { Engine, World, Entity, Scene, Transform, Camera, VisualScriptComponent, VisualScriptSystem } from '../../engine/src';
import { AIAnimationSynthesisSystem } from '../../engine/src/animation/AIAnimationSynthesisSystem';
import { AnimatorComponent } from '../../engine/src/animation/AnimatorComponent';
import { ModelComponent } from '../../engine/src/rendering/ModelComponent';
import { GraphJSON } from '../../engine/src/visualscript/graph/GraphJSON';
import { NodeRegistry } from '../../engine/src/visualscript/nodes/NodeRegistry';
import { ValueTypeRegistry } from '../../engine/src/visualscript/values/ValueTypeRegistry';
import { registerCoreNodes } from '../../engine/src/visualscript/presets/CoreNodes';
import { registerAINodes } from '../../engine/src/visualscript/presets/AINodes';
import { registerLogicNodes } from '../../engine/src/visualscript/presets/LogicNodes';
import { registerEntityNodes } from '../../engine/src/visualscript/presets/EntityNodes';

/**
 * 视觉脚本AI动画示例
 */
export class AIAnimationExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 世界实例 */
  private world: World;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 视觉脚本系统 */
  private visualScriptSystem: VisualScriptSystem;
  
  /** AI动画合成系统 */
  private aiAnimationSystem: AIAnimationSynthesisSystem;
  
  /** 角色实体 */
  private characterEntity: Entity;
  
  /** UI元素 */
  private ui: HTMLElement;
  
  /**
   * 创建示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine({
      debug: true
    });
    
    // 创建世界
    this.world = new World(this.engine);
    
    // 创建节点注册表
    const nodeRegistry = new NodeRegistry();
    
    // 注册核心节点
    registerCoreNodes(nodeRegistry);
    
    // 注册逻辑节点
    registerLogicNodes(nodeRegistry);
    
    // 注册实体节点
    registerEntityNodes(nodeRegistry);
    
    // 注册AI节点
    registerAINodes(nodeRegistry);
    
    // 创建值类型注册表
    const valueTypeRegistry = new ValueTypeRegistry();
    
    // 创建视觉脚本系统
    this.visualScriptSystem = new VisualScriptSystem(this.world, {
      defaultDomain: 'default'
    });
    
    // 注册脚本域
    this.visualScriptSystem.registerDomain('default', nodeRegistry, valueTypeRegistry);
    
    // 添加视觉脚本系统到世界
    this.world.addSystem(this.visualScriptSystem);
    
    // 创建AI动画合成系统
    this.aiAnimationSystem = new AIAnimationSynthesisSystem(this.world, {
      debug: true,
      useLocalModel: true
    });
    
    // 添加AI动画合成系统到世界
    this.world.addSystem(this.aiAnimationSystem);
    
    // 创建场景
    this.scene = new Scene(this.world, {
      name: '视觉脚本AI动画示例场景'
    });
    
    // 创建相机
    const camera = new Entity('相机');
    camera.addComponent(new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000
    }));
    camera.addComponent(new Transform({
      position: { x: 0, y: 1.6, z: 3 },
      rotation: { x: 0, y: 0, z: 0 }
    }));
    
    // 添加相机到场景
    this.scene.addEntity(camera);
    
    // 创建角色
    this.createCharacter();
    
    // 创建控制器
    this.createController();
    
    // 创建UI
    this.createUI();
    
    // 启动引擎
    this.engine.start();
  }
  
  /**
   * 创建角色
   */
  private createCharacter(): void {
    // 创建角色实体
    this.characterEntity = new Entity('角色');
    
    // 添加变换组件
    this.characterEntity.addComponent(new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    
    // 添加模型组件
    this.characterEntity.addComponent(new ModelComponent({
      url: 'models/character.glb'
    }));
    
    // 添加动画器组件
    this.characterEntity.addComponent(new AnimatorComponent());
    
    // 添加到场景
    this.scene.addEntity(this.characterEntity);
    
    // 创建AI动画合成组件
    this.aiAnimationSystem.createAIAnimationSynthesis(this.characterEntity);
  }
  
  /**
   * 创建控制器
   */
  private createController(): void {
    // 创建控制器实体
    const controllerEntity = new Entity('控制器');
    
    // 添加视觉脚本组件
    const controllerScript = this.createControllerScript();
    controllerEntity.addComponent(new VisualScriptComponent({
      script: controllerScript,
      running: true,
      domain: 'default'
    }));
    
    // 添加到场景
    this.scene.addEntity(controllerEntity);
  }
  
  /**
   * 创建控制器脚本
   * @returns 控制器脚本
   */
  private createControllerScript(): GraphJSON {
    // 创建控制器脚本
    const script: GraphJSON = {
      nodes: [
        {
          id: 'start',
          type: 'core/events/onStart',
          metadata: {
            positionX: 100,
            positionY: 100
          },
          flows: {
            flow: {
              nodeId: 'getCharacter',
              socket: 'flow'
            }
          }
        },
        {
          id: 'getCharacter',
          type: 'entity/get',
          metadata: {
            positionX: 300,
            positionY: 100
          },
          parameters: {
            id: {
              value: '角色'
            }
          },
          flows: {
            flow: {
              nodeId: 'generateWalkAnimation',
              socket: 'flow'
            }
          }
        },
        {
          id: 'generateWalkAnimation',
          type: 'ai/animation/generateBodyAnimation',
          metadata: {
            positionX: 500,
            positionY: 100
          },
          parameters: {
            entity: {
              reference: {
                nodeId: 'getCharacter',
                socket: 'entity'
              }
            },
            prompt: {
              value: '走路'
            },
            duration: {
              value: 5.0
            },
            loop: {
              value: true
            },
            style: {
              value: 'natural'
            },
            intensity: {
              value: 1.0
            }
          },
          flows: {
            success: {
              nodeId: 'logSuccess',
              socket: 'flow'
            },
            fail: {
              nodeId: 'logFail',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logSuccess',
          type: 'core/debug/log',
          metadata: {
            positionX: 700,
            positionY: 50
          },
          parameters: {
            message: {
              value: '生成走路动画成功'
            }
          },
          flows: {
            flow: {
              nodeId: 'wait',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logFail',
          type: 'core/debug/log',
          metadata: {
            positionX: 700,
            positionY: 150
          },
          parameters: {
            message: {
              value: '生成走路动画失败'
            }
          }
        },
        {
          id: 'wait',
          type: 'core/time/delay',
          metadata: {
            positionX: 900,
            positionY: 100
          },
          parameters: {
            duration: {
              value: 10
            }
          },
          flows: {
            flow: {
              nodeId: 'generateTalkAnimation',
              socket: 'flow'
            }
          }
        },
        {
          id: 'generateTalkAnimation',
          type: 'ai/animation/generateFacialAnimation',
          metadata: {
            positionX: 1100,
            positionY: 100
          },
          parameters: {
            entity: {
              reference: {
                nodeId: 'getCharacter',
                socket: 'entity'
              }
            },
            prompt: {
              value: '你好，我是一个AI生成的角色。'
            },
            duration: {
              value: 5.0
            },
            loop: {
              value: false
            }
          },
          flows: {
            success: {
              nodeId: 'logTalkSuccess',
              socket: 'flow'
            },
            fail: {
              nodeId: 'logTalkFail',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logTalkSuccess',
          type: 'core/debug/log',
          metadata: {
            positionX: 1300,
            positionY: 50
          },
          parameters: {
            message: {
              value: '生成说话动画成功'
            }
          },
          flows: {
            flow: {
              nodeId: 'wait2',
              socket: 'flow'
            }
          }
        },
        {
          id: 'logTalkFail',
          type: 'core/debug/log',
          metadata: {
            positionX: 1300,
            positionY: 150
          },
          parameters: {
            message: {
              value: '生成说话动画失败'
            }
          }
        },
        {
          id: 'wait2',
          type: 'core/time/delay',
          metadata: {
            positionX: 1500,
            positionY: 100
          },
          parameters: {
            duration: {
              value: 10
            }
          },
          flows: {
            flow: {
              nodeId: 'generateWalkAnimation',
              socket: 'flow'
            }
          }
        }
      ],
      variables: [],
      customEvents: []
    };
    
    return script;
  }
  
  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    this.ui = document.createElement('div');
    this.ui.style.position = 'absolute';
    this.ui.style.top = '10px';
    this.ui.style.left = '10px';
    this.ui.style.padding = '10px';
    this.ui.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    this.ui.style.color = 'white';
    this.ui.style.fontFamily = 'Arial, sans-serif';
    this.ui.style.borderRadius = '5px';
    
    // 添加标题
    const title = document.createElement('h2');
    title.textContent = '视觉脚本AI动画示例';
    this.ui.appendChild(title);
    
    // 添加说明
    const description = document.createElement('p');
    description.textContent = '这个示例演示了如何使用视觉脚本系统的AI节点生成角色动画。';
    this.ui.appendChild(description);
    
    // 添加状态显示
    const status = document.createElement('div');
    status.id = 'animation-status';
    status.textContent = '动画状态: 初始化中';
    this.ui.appendChild(status);
    
    // 添加动画控制按钮
    const buttonContainer = document.createElement('div');
    buttonContainer.style.marginTop = '10px';
    
    const walkButton = document.createElement('button');
    walkButton.textContent = '生成走路动画';
    walkButton.style.marginRight = '10px';
    walkButton.onclick = () => this.generateAnimation('走路', 5.0, true);
    buttonContainer.appendChild(walkButton);
    
    const runButton = document.createElement('button');
    runButton.textContent = '生成跑步动画';
    runButton.style.marginRight = '10px';
    runButton.onclick = () => this.generateAnimation('跑步', 3.0, true);
    buttonContainer.appendChild(runButton);
    
    const jumpButton = document.createElement('button');
    jumpButton.textContent = '生成跳跃动画';
    jumpButton.style.marginRight = '10px';
    jumpButton.onclick = () => this.generateAnimation('跳跃', 2.0, false);
    buttonContainer.appendChild(jumpButton);
    
    const danceButton = document.createElement('button');
    danceButton.textContent = '生成舞蹈动画';
    danceButton.onclick = () => this.generateAnimation('跳舞', 10.0, true);
    buttonContainer.appendChild(danceButton);
    
    this.ui.appendChild(buttonContainer);
    
    // 添加到文档
    document.body.appendChild(this.ui);
  }
  
  /**
   * 生成动画
   * @param prompt 提示文本
   * @param duration 持续时间
   * @param loop 是否循环
   */
  private generateAnimation(prompt: string, duration: number, loop: boolean): void {
    // 更新状态
    const statusElement = document.getElementById('animation-status');
    if (statusElement) {
      statusElement.textContent = `动画状态: 正在生成 "${prompt}" 动画...`;
      statusElement.style.color = 'yellow';
    }
    
    // 生成动画
    const requestId = this.aiAnimationSystem.generateBodyAnimation(
      this.characterEntity,
      prompt,
      duration,
      { loop }
    );
    
    if (requestId) {
      // 获取AI动画合成组件
      const component = this.aiAnimationSystem.getAIAnimationSynthesis(this.characterEntity);
      
      if (component) {
        // 等待动画生成完成
        component.waitForResult(requestId).then(result => {
          if (result && result.success) {
            if (statusElement) {
              statusElement.textContent = `动画状态: "${prompt}" 动画生成成功`;
              statusElement.style.color = 'lightgreen';
            }
          } else {
            if (statusElement) {
              statusElement.textContent = `动画状态: "${prompt}" 动画生成失败`;
              statusElement.style.color = 'red';
            }
          }
        });
      }
    } else {
      if (statusElement) {
        statusElement.textContent = `动画状态: "${prompt}" 动画生成请求失败`;
        statusElement.style.color = 'red';
      }
    }
  }
  
  /**
   * 销毁示例
   */
  public dispose(): void {
    // 停止引擎
    this.engine.stop();
    
    // 移除UI
    if (this.ui && this.ui.parentNode) {
      this.ui.parentNode.removeChild(this.ui);
    }
    
    // 销毁引擎
    this.engine.dispose();
  }
}
