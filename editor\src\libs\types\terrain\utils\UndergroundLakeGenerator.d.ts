import { TerrainComponent } from '../components/TerrainComponent';
/**
 * 湖泊形状类型
 */
export declare enum LakeShapeType {
    /** 圆形 */
    CIRCULAR = "circular",
    /** 不规则 */
    IRREGULAR = "irregular",
    /** 分叉 */
    BRANCHING = "branching",
    /** 连接 */
    CONNECTED = "connected",
    /** 洞穴型 */
    CAVERN = "cavern"
}
/**
 * 地下瀑布数据
 */
export interface UndergroundWaterfall {
    /** 起点X坐标 */
    startX: number;
    /** 起点Z坐标 */
    startZ: number;
    /** 终点X坐标 */
    endX: number;
    /** 终点Z坐标 */
    endZ: number;
    /** 高度 */
    height: number;
    /** 宽度 */
    width: number;
    /** 流量 */
    flowRate: number;
}
/**
 * 地下温泉数据
 */
export interface HotSpring {
    /** X坐标 */
    x: number;
    /** Z坐标 */
    z: number;
    /** 半径 */
    radius: number;
    /** 温度 */
    temperature: number;
    /** 气泡率 */
    bubbleRate: number;
}
/**
 * 钟乳石数据
 */
export interface Stalactite {
    /** X坐标 */
    x: number;
    /** Z坐标 */
    z: number;
    /** 高度 */
    height: number;
    /** 半径 */
    radius: number;
    /** 类型（钟乳石/石笋） */
    type: 'stalactite' | 'stalagmite';
}
/**
 * 地下湖泊生成参数
 */
export interface UndergroundLakeParams {
    /** 湖泊数量 */
    count: number;
    /** 湖泊最小半径 */
    minRadius: number;
    /** 湖泊最大半径 */
    maxRadius: number;
    /** 湖泊深度 */
    depth: number;
    /** 湖泊复杂度（形状不规则程度） */
    complexity: number;
    /** 湖泊深度变化（湖底不平整程度） */
    depthVariation: number;
    /** 连接到洞穴的概率 */
    caveProbability: number;
    /** 连接到地下河的概率 */
    riverProbability: number;
    /** 最小深度（相对于地形高度的比例） */
    minDepthRatio: number;
    /** 最大深度（相对于地形高度的比例） */
    maxDepthRatio: number;
    /** 湖泊形状类型 */
    shapeType?: LakeShapeType;
    /** 是否生成钟乳石 */
    generateStalactites?: boolean;
    /** 钟乳石密度 */
    stalactiteDensity?: number;
    /** 是否生成地下瀑布 */
    generateWaterfall?: boolean;
    /** 瀑布高度 */
    waterfallHeight?: number;
    /** 是否生成温泉 */
    generateHotSpring?: boolean;
    /** 温泉温度 */
    hotSpringTemperature?: number;
    /** 种子 */
    seed: number;
}
/**
 * 地下湖泊节点
 */
export interface UndergroundLakeNode {
    /** X坐标 */
    x: number;
    /** Z坐标 */
    z: number;
    /** 半径 */
    radius: number;
    /** 深度比例 */
    depthRatio: number;
    /** 形状控制点（用于不规则形状） */
    controlPoints?: {
        x: number;
        z: number;
        influence: number;
    }[];
    /** 节点类型 */
    type?: 'main' | 'branch' | 'connection';
}
/**
 * 地下湖泊
 */
export interface UndergroundLake {
    /** 湖泊节点 */
    nodes: UndergroundLakeNode[];
    /** 连接的洞穴 */
    connectedCaves: any[];
    /** 连接的河流 */
    connectedRivers: any[];
    /** 湖泊形状类型 */
    shapeType?: LakeShapeType;
    /** 瀑布列表 */
    waterfalls?: UndergroundWaterfall[];
    /** 温泉列表 */
    hotSprings?: HotSpring[];
    /** 钟乳石列表 */
    stalactites?: Stalactite[];
}
/**
 * 地下湖泊生成器
 */
export declare class UndergroundLakeGenerator {
    /**
     * 生成地下湖泊系统
     * @param terrain 地形组件
     * @param params 地下湖泊生成参数
     */
    static generateUndergroundLakes(terrain: TerrainComponent, params: UndergroundLakeParams): void;
    /**
     * 生成单个地下湖泊
     * @param terrain 地形组件
     * @param centerX 中心X坐标
     * @param centerZ 中心Z坐标
     * @param radius 半径
     * @param depth 深度
     * @param complexity 复杂度
     * @param depthVariation 深度变化
     * @param depthRatio 深度比例
     * @param shapeType 湖泊形状类型
     * @param generateStalactites 是否生成钟乳石
     * @param stalactiteDensity 钟乳石密度
     * @param generateWaterfall 是否生成瀑布
     * @param waterfallHeight 瀑布高度
     * @param generateHotSpring 是否生成温泉
     * @param hotSpringTemperature 温泉温度
     * @param random 随机数生成器
     * @returns 地下湖泊
     */
    private static generateLake;
    /**
     * 应用湖泊到地形
     * @param terrain 地形组件
     * @param lake 湖泊
     * @param depth 深度
     * @param depthVariation 深度变化
     * @param random 随机数生成器
     */
    private static applyLakeToTerrain;
    /**
     * 应用湖泊到地形
     * @param terrain 地形组件
     * @param lakes 湖泊列表
     * @param depth 深度
     */
    private static applyLakesToTerrain;
    /**
     * 连接湖泊到洞穴
     * @param terrain 地形组件
     * @param lakes 湖泊列表
     * @param caveProbability 洞穴概率
     * @param random 随机数生成器
     */
    private static connectLakesToCaves;
    /**
     * 连接湖泊到洞穴
     * @param terrain 地形组件
     * @param lake 湖泊
     * @param caves 洞穴数据
     * @param random 随机数生成器
     */
    private static connectLakeToCave;
    /**
     * 连接湖泊到地下河
     * @param terrain 地形组件
     * @param lakes 湖泊列表
     * @param riverProbability 河流概率
     * @param random 随机数生成器
     */
    private static connectLakesToRivers;
    /**
     * 连接湖泊到地下河
     * @param terrain 地形组件
     * @param lake 湖泊
     * @param rivers 地下河数据
     * @param random 随机数生成器
     */
    private static connectLakeToRiver;
    /**
     * 创建连接通道
     * @param terrain 地形组件
     * @param lakeNode 湖泊节点
     * @param cave 洞穴
     * @param random 随机数生成器
     */
    private static createConnectionTunnel;
    /**
     * 创建河流连接
     * @param terrain 地形组件
     * @param lakeNode 湖泊节点
     * @param riverNode 河流节点
     * @param random 随机数生成器
     */
    private static createRiverConnection;
    /**
     * 创建随机数生成器
     * @param seed 种子
     * @returns 随机数生成器函数
     */
    private static createRandomGenerator;
    /**
     * 简化版Simplex噪声函数
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     * @returns 噪声值
     */
    private static simplexNoise;
    /**
     * 生成不规则湖泊
     * @param lake 湖泊
     * @param centerX 中心X坐标
     * @param centerZ 中心Z坐标
     * @param radius 半径
     * @param depthRatio 深度比例
     * @param complexity 复杂度
     * @param random 随机数生成器
     */
    private static generateIrregularLake;
    /**
     * 生成分叉湖泊
     * @param lake 湖泊
     * @param centerX 中心X坐标
     * @param centerZ 中心Z坐标
     * @param radius 半径
     * @param depthRatio 深度比例
     * @param complexity 复杂度
     * @param random 随机数生成器
     */
    private static generateBranchingLake;
    /**
     * 生成连接湖泊
     * @param lake 湖泊
     * @param centerX 中心X坐标
     * @param centerZ 中心Z坐标
     * @param radius 半径
     * @param depthRatio 深度比例
     * @param complexity 复杂度
     * @param random 随机数生成器
     */
    private static generateConnectedLake;
    /**
     * 生成洞穴型湖泊
     * @param lake 湖泊
     * @param centerX 中心X坐标
     * @param centerZ 中心Z坐标
     * @param radius 半径
     * @param depthRatio 深度比例
     * @param complexity 复杂度
     * @param random 随机数生成器
     */
    private static generateCavernLake;
    /**
     * 在两个节点之间创建连接
     * @param lake 湖泊
     * @param start 起点
     * @param end 终点
     * @param width 宽度
     * @param depthRatio 深度比例
     * @param random 随机数生成器
     */
    private static createConnectionBetweenNodes;
    /**
     * 生成钟乳石
     * @param lake 湖泊
     * @param density 密度
     * @param random 随机数生成器
     */
    private static generateStalactites;
    /**
     * 生成瀑布
     * @param lake 湖泊
     * @param height 高度
     * @param random 随机数生成器
     */
    private static generateWaterfall;
    /**
     * 生成温泉
     * @param lake 湖泊
     * @param temperature 温度
     * @param random 随机数生成器
     */
    private static generateHotSpring;
}
