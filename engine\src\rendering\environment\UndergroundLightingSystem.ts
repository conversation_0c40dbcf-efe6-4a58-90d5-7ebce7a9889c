/**
 * 地下环境光照系统
 * 用于模拟地下环境的光照效果，包括洞穴光照、钟乳石反射光、水面反射光等
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { World } from '../../core/World';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { LightProbeSystem } from './LightProbeSystem';

/**
 * 地下环境光照系统配置
 */
export interface UndergroundLightingSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用洞穴光照 */
  enableCaveLighting?: boolean;
  /** 是否启用钟乳石反射光 */
  enableStalactiteReflection?: boolean;
  /** 是否启用水面反射光 */
  enableWaterReflection?: boolean;
  /** 是否启用体积光 */
  enableVolumetricLight?: boolean;
  /** 是否启用体积雾 */
  enableVolumetricFog?: boolean;
  /** 是否启用调试可视化 */
  enableDebugVisualization?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
}

/**
 * 洞穴光照配置
 */
export interface CaveLightingConfig {
  /** 位置 */
  position: THREE.Vector3;
  /** 大小 */
  size: THREE.Vector3;
  /** 强度 */
  intensity: number;
  /** 颜色 */
  color: THREE.Color;
  /** 衰减 */
  decay: number;
  /** 是否产生阴影 */
  castShadow: boolean;
}

/**
 * 钟乳石反射光配置
 */
export interface StalactiteReflectionConfig {
  /** 位置 */
  position: THREE.Vector3;
  /** 大小 */
  size: number;
  /** 强度 */
  intensity: number;
  /** 颜色 */
  color: THREE.Color;
  /** 闪烁频率 */
  flickerFrequency: number;
  /** 闪烁强度 */
  flickerIntensity: number;
}

/**
 * 水面反射光配置
 */
export interface WaterReflectionConfig {
  /** 水面位置 */
  position: THREE.Vector3;
  /** 水面大小 */
  size: { width: number, depth: number };
  /** 反射强度 */
  intensity: number;
  /** 反射颜色 */
  color: THREE.Color;
  /** 波动频率 */
  waveFrequency: number;
  /** 波动强度 */
  waveIntensity: number;
}

/**
 * 地下环境光照系统
 */
export class UndergroundLightingSystem extends System {
  /** 配置 */
  private config: Required<UndergroundLightingSystemConfig>;
  /** 光照探针系统 */
  private lightProbeSystem: LightProbeSystem | null = null;
  /** 洞穴光照列表 */
  private caveLights: Map<string, { config: CaveLightingConfig, light: THREE.Light }> = new Map();
  /** 钟乳石反射光列表 */
  private stalactiteReflections: Map<string, { config: StalactiteReflectionConfig, light: THREE.Light }> = new Map();
  /** 水面反射光列表 */
  private waterReflections: Map<string, { config: WaterReflectionConfig, light: THREE.Light }> = new Map();
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 调试渲染器 */
  private debugRenderer: THREE.Object3D | null = null;
  /** 时间 */
  private time: number = 0;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: UndergroundLightingSystemConfig = {}) {
    super();

    // 设置配置
    this.config = {
      enabled: config.enabled ?? true,
      autoUpdate: config.autoUpdate ?? true,
      updateFrequency: config.updateFrequency ?? 1,
      enableCaveLighting: config.enableCaveLighting ?? true,
      enableStalactiteReflection: config.enableStalactiteReflection ?? true,
      enableWaterReflection: config.enableWaterReflection ?? true,
      enableVolumetricLight: config.enableVolumetricLight ?? false,
      enableVolumetricFog: config.enableVolumetricFog ?? false,
      enableDebugVisualization: config.enableDebugVisualization ?? false,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring ?? false
    };

    // 设置世界
    this.setWorld(world);

    // 获取光照探针系统
    if (world) {
      this.lightProbeSystem = world.getSystem(LightProbeSystem);
    }

    // 创建调试渲染器
    if (this.config.enableDebugVisualization) {
      this.createDebugRenderer();
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 如果没有光照探针系统，尝试获取
    if (!this.lightProbeSystem && this.world) {
      this.lightProbeSystem = this.world.getSystem(LightProbeSystem);
    }

    // 如果启用了性能监控，启动性能监视器
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.start();
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }

    // 更新时间
    this.time += deltaTime;

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.beginMeasure('undergroundLightingUpdate');
    }

    // 更新洞穴光照
    if (this.config.enableCaveLighting) {
      this.updateCaveLights(deltaTime);
    }

    // 更新钟乳石反射光
    if (this.config.enableStalactiteReflection) {
      this.updateStalactiteReflections(deltaTime);
    }

    // 更新水面反射光
    if (this.config.enableWaterReflection) {
      this.updateWaterReflections(deltaTime);
    }

    // 如果启用了调试可视化，更新调试渲染器
    if (this.config.enableDebugVisualization && this.debugRenderer) {
      this.updateDebugRenderer();
    }

    // 如果启用了性能监控，结束计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.endMeasure('undergroundLightingUpdate');
    }
  }

  /**
   * 创建调试渲染器
   */
  private createDebugRenderer(): void {
    this.debugRenderer = new THREE.Group();
    this.debugRenderer.name = 'UndergroundLightingDebugRenderer';

    // 添加到场景
    const scene = this.world?.getActiveScene();
    if (scene) {
      scene.getThreeScene().add(this.debugRenderer);
    }
  }

  /**
   * 更新调试渲染器
   */
  private updateDebugRenderer(): void {
    if (!this.debugRenderer) return;

    // 清空调试渲染器
    while (this.debugRenderer.children.length > 0) {
      this.debugRenderer.remove(this.debugRenderer.children[0]);
    }

    // 添加洞穴光照调试可视化
    this.caveLights.forEach(({ light }, id) => {
      // 创建光源辅助对象
      const helper = new THREE.PointLightHelper(light as THREE.PointLight, 0.5);
      helper.name = `CaveLight_${id}_Helper`;
      this.debugRenderer!.add(helper);
    });

    // 添加钟乳石反射光调试可视化
    this.stalactiteReflections.forEach(({ light }, id) => {
      // 创建光源辅助对象
      const helper = new THREE.PointLightHelper(light as THREE.PointLight, 0.2);
      helper.name = `StalactiteReflection_${id}_Helper`;
      this.debugRenderer!.add(helper);
    });

    // 添加水面反射光调试可视化
    this.waterReflections.forEach(({ light }, id) => {
      // 创建光源辅助对象
      const helper = new THREE.SpotLightHelper(light as THREE.SpotLight);
      helper.name = `WaterReflection_${id}_Helper`;
      this.debugRenderer!.add(helper);
    });
  }

  /**
   * 添加洞穴光照
   * @param id 光照ID
   * @param config 洞穴光照配置
   * @returns 光照ID
   */
  public addCaveLight(id: string, config: CaveLightingConfig): string {
    // 创建光源
    const light = new THREE.PointLight(
      config.color,
      config.intensity,
      config.size.x * 2,
      config.decay
    );
    light.position.copy(config.position);
    light.castShadow = config.castShadow;

    // 设置阴影参数
    if (config.castShadow && light.shadow) {
      light.shadow.mapSize.width = 1024;
      light.shadow.mapSize.height = 1024;
      light.shadow.camera.near = 0.5;
      light.shadow.camera.far = config.size.x * 2;
      light.shadow.bias = -0.001;
    }

    // 添加到场景
    const scene = this.world?.getActiveScene();
    if (scene) {
      scene.getThreeScene().add(light);
    }

    // 添加到洞穴光照列表
    this.caveLights.set(id, { config, light });

    // 如果启用了光照探针，创建洞穴光照探针
    if (this.lightProbeSystem && this.config.enableCaveLighting) {
      this.lightProbeSystem.createCaveLightProbe(
        `CaveProbe_${id}`,
        config.position,
        config.size,
        config.intensity
      );
    }

    return id;
  }

  /**
   * 移除洞穴光照
   * @param id 光照ID
   */
  public removeCaveLight(id: string): void {
    // 获取洞穴光照
    const caveLight = this.caveLights.get(id);
    if (!caveLight) return;

    // 从场景中移除
    const scene = this.world?.getActiveScene();
    if (scene) {
      scene.getThreeScene().remove(caveLight.light);
    }

    // 从洞穴光照列表中移除
    this.caveLights.delete(id);
  }

  /**
   * 更新洞穴光照
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateCaveLights(_deltaTime: number): void {
    // 遍历所有洞穴光照
    this.caveLights.forEach(({ config, light }) => {
      // 更新光照强度（可以添加一些随机变化，模拟火把等光源的闪烁）
      const flickerAmount = Math.sin(this.time * 2) * 0.1 + 0.9;
      light.intensity = config.intensity * flickerAmount;
    });
  }

  /**
   * 添加钟乳石反射光
   * @param id 光照ID
   * @param config 钟乳石反射光配置
   * @returns 光照ID
   */
  public addStalactiteReflection(id: string, config: StalactiteReflectionConfig): string {
    // 创建光源
    const light = new THREE.PointLight(
      config.color,
      config.intensity,
      config.size * 2,
      2.0
    );
    light.position.copy(config.position);
    light.castShadow = false;

    // 添加到场景
    const scene = this.world?.getActiveScene();
    if (scene) {
      scene.getThreeScene().add(light);
    }

    // 添加到钟乳石反射光列表
    this.stalactiteReflections.set(id, { config, light });

    return id;
  }

  /**
   * 移除钟乳石反射光
   * @param id 光照ID
   */
  public removeStalactiteReflection(id: string): void {
    // 获取钟乳石反射光
    const stalactiteReflection = this.stalactiteReflections.get(id);
    if (!stalactiteReflection) return;

    // 从场景中移除
    const scene = this.world?.getActiveScene();
    if (scene) {
      scene.getThreeScene().remove(stalactiteReflection.light);
    }

    // 从钟乳石反射光列表中移除
    this.stalactiteReflections.delete(id);
  }

  /**
   * 更新钟乳石反射光
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateStalactiteReflections(_deltaTime: number): void {
    // 遍历所有钟乳石反射光
    this.stalactiteReflections.forEach(({ config, light }) => {
      // 更新光照强度（基于闪烁频率和强度）
      const flickerAmount = Math.sin(this.time * config.flickerFrequency) * config.flickerIntensity + (1 - config.flickerIntensity);
      light.intensity = config.intensity * flickerAmount;

      // 更新光照颜色（可以添加一些随机变化，模拟钟乳石反射光的变化）
      const hue = (Math.sin(this.time * 0.5) * 0.05 + 0.95);

      const color = new THREE.Color(config.color);
      color.offsetHSL(0, 0, (hue - 1) * 0.1);
      light.color = color;
    });
  }

  /**
   * 添加水面反射光
   * @param id 光照ID
   * @param config 水面反射光配置
   * @returns 光照ID
   */
  public addWaterReflection(id: string, config: WaterReflectionConfig): string {
    // 创建光源
    const light = new THREE.SpotLight(
      config.color,
      config.intensity,
      config.size.width * 2,
      Math.PI / 4,
      0.5,
      2.0
    );
    light.position.copy(config.position);
    light.position.y += 0.1; // 稍微抬高，避免与水面重合
    light.target.position.set(
      config.position.x,
      config.position.y - 1,
      config.position.z
    );
    light.castShadow = false;

    // 添加到场景
    const scene = this.world?.getActiveScene();
    if (scene) {
      scene.getThreeScene().add(light);
      scene.getThreeScene().add(light.target);
    }

    // 添加到水面反射光列表
    this.waterReflections.set(id, { config, light });

    return id;
  }

  /**
   * 移除水面反射光
   * @param id 光照ID
   */
  public removeWaterReflection(id: string): void {
    // 获取水面反射光
    const waterReflection = this.waterReflections.get(id);
    if (!waterReflection) return;

    // 从场景中移除
    const scene = this.world?.getActiveScene();
    if (scene) {
      scene.getThreeScene().remove(waterReflection.light);
      scene.getThreeScene().remove((waterReflection.light as THREE.SpotLight).target);
    }

    // 从水面反射光列表中移除
    this.waterReflections.delete(id);
  }

  /**
   * 更新水面反射光
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterReflections(_deltaTime: number): void {
    // 遍历所有水面反射光
    this.waterReflections.forEach(({ config, light }) => {
      // 获取水面波动
      const waveX = Math.sin(this.time * config.waveFrequency) * config.waveIntensity;
      const waveZ = Math.cos(this.time * config.waveFrequency * 1.3) * config.waveIntensity;

      // 更新光照位置
      const spotLight = light as THREE.SpotLight;
      spotLight.position.x = config.position.x + waveX;
      spotLight.position.z = config.position.z + waveZ;

      // 更新光照目标位置
      spotLight.target.position.x = config.position.x + waveX;
      spotLight.target.position.z = config.position.z + waveZ;
      spotLight.target.position.y = config.position.y - 1;

      // 更新光照强度
      const intensityVariation = Math.sin(this.time * config.waveFrequency * 2) * 0.2 + 0.8;
      spotLight.intensity = config.intensity * intensityVariation;

      // 更新光照颜色
      const lightness = (Math.sin(this.time * 0.5) * 0.1 + 0.9);

      const color = new THREE.Color(config.color);
      color.offsetHSL(0, 0, (lightness - 1) * 0.2);
      spotLight.color = color;
    });
  }
}
