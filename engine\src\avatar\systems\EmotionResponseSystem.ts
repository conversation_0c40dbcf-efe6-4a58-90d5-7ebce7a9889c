/**
 * 情感响应系统
 * 用于处理角色对环境和事件的情感反应
 */
import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationSystem } from './FacialAnimationSystem';
import { AIAnimationSynthesisSystem } from './AIAnimationSynthesisSystem';
import { FacialExpressionType } from '../components/FacialAnimationComponent';
import { EmotionBlendController } from '../controllers/EmotionBlendController';
import { Vector3 } from 'three';

/**
 * 情感响应系统配置
 */
export interface EmotionResponseSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否自动响应事件 */
  autoRespondToEvents?: boolean;
  /** 响应范围 */
  responseRange?: number;
  /** 最小响应强度 */
  minResponseIntensity?: number;
  /** 最大响应强度 */
  maxResponseIntensity?: number;
  /** 情感衰减速率 */
  emotionDecayRate?: number;
  /** 是否启用情感混合 */
  enableEmotionBlending?: boolean;
  /** 是否启用情感记忆 */
  enableEmotionMemory?: boolean;
  /** 情感记忆持续时间（秒） */
  emotionMemoryDuration?: number;
  /** 是否启用情感传染 */
  enableEmotionContagion?: boolean;
  /** 情感传染范围 */
  emotionContagionRange?: number;
  /** 情感传染强度 */
  emotionContagionStrength?: number;
}

/**
 * 情感事件类型
 */
export enum EmotionEventType {
  /** 惊吓 */
  STARTLE = 'startle',
  /** 惊喜 */
  SURPRISE = 'surprise',
  /** 威胁 */
  THREAT = 'threat',
  /** 喜悦 */
  JOY = 'joy',
  /** 悲伤 */
  SADNESS = 'sadness',
  /** 愤怒 */
  ANGER = 'anger',
  /** 恐惧 */
  FEAR = 'fear',
  /** 厌恶 */
  DISGUST = 'disgust',
  /** 中性 */
  NEUTRAL = 'neutral',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 情感事件数据
 */
export interface EmotionEventData {
  /** 事件类型 */
  type: EmotionEventType;
  /** 事件源 */
  source?: Entity;
  /** 事件位置 */
  position?: Vector3;
  /** 事件强度 */
  intensity?: number;
  /** 事件范围 */
  range?: number;
  /** 事件持续时间 */
  duration?: number;
  /** 事件描述 */
  description?: string;
  /** 自定义数据 */
  customData?: any;
}

/**
 * 情感响应数据
 */
export interface EmotionResponseData {
  /** 响应实体 */
  entity: Entity;
  /** 事件类型 */
  eventType: EmotionEventType;
  /** 响应强度 */
  intensity: number;
  /** 响应持续时间 */
  duration: number;
  /** 响应表情 */
  expression: FacialExpressionType;
  /** 响应描述 */
  description: string;
  /** 响应时间戳 */
  timestamp: number;
}

/**
 * 情感记忆
 */
export interface EmotionMemory {
  /** 事件类型 */
  eventType: EmotionEventType;
  /** 事件强度 */
  intensity: number;
  /** 事件时间戳 */
  timestamp: number;
  /** 事件源 */
  source?: Entity;
  /** 事件描述 */
  description?: string;
}

/**
 * 情感响应系统
 */
export class EmotionResponseSystem extends System {
  /** 系统名称 */
  public static readonly systemName: string = 'EmotionResponseSystem';

  /** 配置 */
  private config: Required<EmotionResponseSystemConfig>;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem | null = null;

  /** AI动画合成系统 */
  private aiAnimationSystem: AIAnimationSynthesisSystem | null = null;

  /** 情感混合控制器映射 */
  private emotionBlendControllers: Map<Entity, EmotionBlendController> = new Map();

  /** 情感记忆映射 */
  private emotionMemories: Map<Entity, EmotionMemory[]> = new Map();

  /** 活跃的情感响应 */
  private activeResponses: Map<Entity, EmotionResponseData> = new Map();

  /** 情感事件队列 */
  private emotionEventQueue: EmotionEventData[] = [];

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: EmotionResponseSystemConfig = {}) {
    super(350); // 设置优先级

    // 设置默认配置
    this.config = {
      debug: config.debug ?? false,
      autoRespondToEvents: config.autoRespondToEvents ?? true,
      responseRange: config.responseRange ?? 10.0,
      minResponseIntensity: config.minResponseIntensity ?? 0.1,
      maxResponseIntensity: config.maxResponseIntensity ?? 1.0,
      emotionDecayRate: config.emotionDecayRate ?? 0.1,
      enableEmotionBlending: config.enableEmotionBlending ?? true,
      enableEmotionMemory: config.enableEmotionMemory ?? true,
      emotionMemoryDuration: config.emotionMemoryDuration ?? 60.0,
      enableEmotionContagion: config.enableEmotionContagion ?? false,
      emotionContagionRange: config.emotionContagionRange ?? 5.0,
      emotionContagionStrength: config.emotionContagionStrength ?? 0.3
    };

    // 创建事件发射器
    this.eventEmitter = new EventEmitter();
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 查找面部动画系统
    if (this.world) {
      this.facialAnimationSystem = this.world.getSystem(FacialAnimationSystem);
      this.aiAnimationSystem = this.world.getSystem(AIAnimationSynthesisSystem);
    }

    if (this.config.debug) {
      console.log('情感响应系统初始化');
      console.log('面部动画系统:', this.facialAnimationSystem ? '已找到' : '未找到');
      console.log('AI动画合成系统:', this.aiAnimationSystem ? '已找到' : '未找到');
    }
  }



  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 处理情感事件队列
    this.processEmotionEventQueue();

    // 更新活跃的情感响应
    this.updateActiveResponses(deltaTime);

    // 更新情感混合控制器
    this.updateEmotionBlendControllers(deltaTime);

    // 更新情感记忆
    if (this.config.enableEmotionMemory) {
      this.updateEmotionMemories(deltaTime);
    }

    // 处理情感传染
    if (this.config.enableEmotionContagion) {
      this.processEmotionContagion(deltaTime);
    }
  }

  /**
   * 触发情感事件
   * @param eventData 事件数据
   */
  public triggerEmotionEvent(eventData: EmotionEventData): void {
    // 添加到事件队列
    this.emotionEventQueue.push(eventData);

    // 触发事件
    this.eventEmitter.emit('emotionEvent', eventData);

    if (this.config.debug) {
      console.log('触发情感事件:', eventData);
    }
  }

  /**
   * 处理情感事件队列
   */
  private processEmotionEventQueue(): void {
    // 处理队列中的所有事件
    while (this.emotionEventQueue.length > 0) {
      const eventData = this.emotionEventQueue.shift();
      if (eventData) {
        this.processEmotionEvent(eventData);
      }
    }
  }

  /**
   * 处理情感事件
   * @param eventData 事件数据
   */
  private processEmotionEvent(eventData: EmotionEventData): void {
    // 如果不自动响应事件，则跳过
    if (!this.config.autoRespondToEvents) return;

    // 获取所有可能受影响的实体
    const affectedEntities = this.getAffectedEntities(eventData);

    // 为每个实体生成情感响应
    for (const entity of affectedEntities) {
      // 计算响应强度
      const intensity = this.calculateResponseIntensity(entity, eventData);

      // 如果强度低于最小阈值，则跳过
      if (intensity < this.config.minResponseIntensity) continue;

      // 生成情感响应
      this.generateEmotionResponse(entity, eventData, intensity);
    }
  }

  /**
   * 获取受影响的实体
   * @param eventData 事件数据
   * @returns 受影响的实体数组
   */
  private getAffectedEntities(eventData: EmotionEventData): Entity[] {
    const affectedEntities: Entity[] = [];

    // 获取事件范围
    const range = eventData.range || this.config.responseRange;

    // 如果没有位置信息，则返回所有具有面部动画组件的实体
    if (!eventData.position) {
      if (this.facialAnimationSystem) {
        // 获取所有具有面部动画组件的实体
        return Array.from((this.facialAnimationSystem as any).components.keys());
      }
      return affectedEntities;
    }

    // 获取所有具有面部动画组件的实体
    if (this.facialAnimationSystem) {
      const entities = Array.from((this.facialAnimationSystem as any).components.keys()) as Entity[];

      // 遍历所有实体
      for (const entity of entities) {
        // 获取实体位置
        const position = this.getEntityPosition(entity);

        // 如果没有位置信息，则跳过
        if (!position) continue;

        // 计算距离
        const distance = position.distanceTo(eventData.position);

        // 如果在范围内，则添加到受影响实体列表
        if (distance <= range) {
          affectedEntities.push(entity);
        }
      }
    }

    return affectedEntities;
  }

  /**
   * 获取实体位置
   * @param entity 实体
   * @returns 实体位置
   */
  private getEntityPosition(entity: Entity): Vector3 | null {
    // 获取实体的变换组件
    const transform = entity.getTransform();

    // 如果有变换组件，则返回位置
    if (transform) {
      const position = transform.getPosition();
      return new Vector3(position.x, position.y, position.z);
    }

    return null;
  }

  /**
   * 计算响应强度
   * @param entity 实体
   * @param eventData 事件数据
   * @returns 响应强度
   */
  private calculateResponseIntensity(entity: Entity, eventData: EmotionEventData): number {
    // 基础强度
    let intensity = eventData.intensity || 1.0;

    // 如果有位置信息，则根据距离衰减
    if (eventData.position) {
      const entityPosition = this.getEntityPosition(entity);

      if (entityPosition) {
        const distance = entityPosition.distanceTo(eventData.position);
        const range = eventData.range || this.config.responseRange;

        // 线性衰减
        intensity *= Math.max(0, 1.0 - (distance / range));
      }
    }

    // 考虑情感记忆
    if (this.config.enableEmotionMemory) {
      intensity = this.applyEmotionMemoryEffect(entity, eventData, intensity);
    }

    // 限制在有效范围内
    return Math.max(this.config.minResponseIntensity, Math.min(this.config.maxResponseIntensity, intensity));
  }

  /**
   * 应用情感记忆效果
   * @param entity 实体
   * @param eventData 事件数据
   * @param baseIntensity 基础强度
   * @returns 调整后的强度
   */
  private applyEmotionMemoryEffect(entity: Entity, eventData: EmotionEventData, baseIntensity: number): number {
    // 获取实体的情感记忆
    const memories = this.emotionMemories.get(entity);

    // 如果没有记忆，则返回基础强度
    if (!memories || memories.length === 0) return baseIntensity;

    // 查找相同类型的记忆
    const sameTypeMemories = memories.filter(memory => memory.eventType === eventData.type);

    // 如果没有相同类型的记忆，则返回基础强度
    if (sameTypeMemories.length === 0) return baseIntensity;

    // 计算记忆影响
    let memoryEffect = 0;
    const currentTime = Date.now();

    for (const memory of sameTypeMemories) {
      // 计算时间衰减因子
      const timeDiff = (currentTime - memory.timestamp) / 1000; // 转换为秒
      const timeFactor = Math.max(0, 1.0 - (timeDiff / this.config.emotionMemoryDuration));

      // 累加记忆效果
      memoryEffect += memory.intensity * timeFactor * 0.2; // 记忆影响因子
    }

    // 应用记忆效果（增强或减弱）
    return baseIntensity * (1.0 + memoryEffect);
  }

  /**
   * 生成情感响应
   * @param entity 实体
   * @param eventData 事件数据
   * @param intensity 响应强度
   */
  private generateEmotionResponse(entity: Entity, eventData: EmotionEventData, intensity: number): void {
    // 映射事件类型到表情类型
    const expression = this.mapEventTypeToExpression(eventData.type);

    // 计算响应持续时间
    const duration = eventData.duration || this.calculateResponseDuration(eventData.type, intensity);

    // 创建响应数据
    const responseData: EmotionResponseData = {
      entity,
      eventType: eventData.type,
      intensity,
      duration,
      expression,
      description: eventData.description || `对${eventData.type}的情感响应`,
      timestamp: Date.now()
    };

    // 添加到活跃响应
    this.activeResponses.set(entity, responseData);

    // 触发响应事件
    this.eventEmitter.emit('emotionResponse', responseData);

    // 添加到情感记忆
    if (this.config.enableEmotionMemory) {
      this.addEmotionMemory(entity, {
        eventType: eventData.type,
        intensity,
        timestamp: Date.now(),
        source: eventData.source,
        description: eventData.description
      });
    }

    // 应用表情
    this.applyEmotionExpression(entity, responseData);

    if (this.config.debug) {
      console.log('生成情感响应:', responseData);
    }
  }

  /**
   * 映射事件类型到表情类型
   * @param eventType 事件类型
   * @returns 表情类型
   */
  private mapEventTypeToExpression(eventType: EmotionEventType): FacialExpressionType {
    switch (eventType) {
      case EmotionEventType.STARTLE:
      case EmotionEventType.SURPRISE:
        return FacialExpressionType.SURPRISED;
      case EmotionEventType.THREAT:
      case EmotionEventType.ANGER:
        return FacialExpressionType.ANGRY;
      case EmotionEventType.JOY:
        return FacialExpressionType.HAPPY;
      case EmotionEventType.SADNESS:
        return FacialExpressionType.SAD;
      case EmotionEventType.FEAR:
        return FacialExpressionType.FEARFUL;
      case EmotionEventType.DISGUST:
        return FacialExpressionType.DISGUSTED;
      case EmotionEventType.NEUTRAL:
        return FacialExpressionType.NEUTRAL;
      case EmotionEventType.CUSTOM:
      default:
        return FacialExpressionType.NEUTRAL;
    }
  }

  /**
   * 计算响应持续时间
   * @param eventType 事件类型
   * @param intensity 响应强度
   * @returns 响应持续时间（秒）
   */
  private calculateResponseDuration(eventType: EmotionEventType, intensity: number): number {
    // 基础持续时间
    let baseDuration = 3.0;

    // 根据事件类型调整
    switch (eventType) {
      case EmotionEventType.STARTLE:
        baseDuration = 1.5;
        break;
      case EmotionEventType.SURPRISE:
        baseDuration = 2.0;
        break;
      case EmotionEventType.THREAT:
      case EmotionEventType.FEAR:
        baseDuration = 4.0;
        break;
      case EmotionEventType.JOY:
        baseDuration = 5.0;
        break;
      case EmotionEventType.SADNESS:
        baseDuration = 6.0;
        break;
      case EmotionEventType.ANGER:
        baseDuration = 4.5;
        break;
      case EmotionEventType.DISGUST:
        baseDuration = 3.5;
        break;
      case EmotionEventType.NEUTRAL:
        baseDuration = 2.0;
        break;
    }

    // 根据强度调整
    return baseDuration * (0.5 + intensity * 0.5);
  }

  /**
   * 应用情感表情
   * @param entity 实体
   * @param responseData 响应数据
   */
  private applyEmotionExpression(entity: Entity, responseData: EmotionResponseData): void {
    // 如果启用情感混合，则使用混合控制器
    if (this.config.enableEmotionBlending) {
      // 获取或创建混合控制器
      let blendController = this.emotionBlendControllers.get(entity);

      if (!blendController) {
        blendController = new EmotionBlendController(entity, this.world);
        this.emotionBlendControllers.set(entity, blendController);
      }

      // 添加表情
      blendController.addExpression(responseData.expression, responseData.intensity, responseData.duration);
    }
    // 否则直接应用表情
    else if (this.facialAnimationSystem) {
      const facialAnimation = this.facialAnimationSystem.getFacialAnimation(entity);

      if (facialAnimation) {
        // 设置表情
        facialAnimation.setExpression(responseData.expression, responseData.intensity);
      }
    }

    // 如果有AI动画合成系统，则生成面部动画
    if (this.aiAnimationSystem) {
      // 生成描述
      const prompt = this.generateEmotionPrompt(responseData);

      // 生成面部动画
      this.aiAnimationSystem.generateFacialAnimation(
        entity,
        prompt,
        responseData.duration,
        {
          loop: false,
          style: 'natural',
          intensity: responseData.intensity
        }
      );
    }
  }

  /**
   * 生成情感提示
   * @param responseData 响应数据
   * @returns 情感提示
   */
  private generateEmotionPrompt(responseData: EmotionResponseData): string {
    // 如果有描述，则使用描述
    if (responseData.description) {
      return responseData.description;
    }

    // 否则根据事件类型和强度生成提示
    let prompt = '';

    switch (responseData.eventType) {
      case EmotionEventType.STARTLE:
        prompt = '角色被突然的声音吓了一跳';
        break;
      case EmotionEventType.SURPRISE:
        prompt = '角色对意外的事情感到惊讶';
        break;
      case EmotionEventType.THREAT:
        prompt = '角色感到受到威胁';
        break;
      case EmotionEventType.JOY:
        prompt = '角色感到非常开心';
        break;
      case EmotionEventType.SADNESS:
        prompt = '角色感到悲伤';
        break;
      case EmotionEventType.ANGER:
        prompt = '角色感到愤怒';
        break;
      case EmotionEventType.FEAR:
        prompt = '角色感到恐惧';
        break;
      case EmotionEventType.DISGUST:
        prompt = '角色感到厌恶';
        break;
      case EmotionEventType.NEUTRAL:
        prompt = '角色表情平静';
        break;
      default:
        prompt = '角色表情变化';
        break;
    }

    // 根据强度调整
    if (responseData.intensity > 0.8) {
      prompt = prompt.replace('感到', '感到非常');
    } else if (responseData.intensity < 0.3) {
      prompt = prompt.replace('感到', '感到轻微');
    }

    return prompt;
  }

  /**
   * 更新活跃的情感响应
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateActiveResponses(deltaTime: number): void {
    const currentTime = Date.now();

    // 遍历所有活跃响应
    for (const [entity, response] of this.activeResponses.entries()) {
      // 计算已经过去的时间
      const elapsedTime = (currentTime - response.timestamp) / 1000; // 转换为秒

      // 如果超过持续时间，则移除
      if (elapsedTime >= response.duration) {
        this.activeResponses.delete(entity);

        // 触发响应结束事件
        this.eventEmitter.emit('emotionResponseEnd', response);

        // 重置表情
        if (this.facialAnimationSystem && !this.config.enableEmotionBlending) {
          const facialAnimation = this.facialAnimationSystem.getFacialAnimation(entity);

          if (facialAnimation) {
            // 重置为中性表情
            facialAnimation.setExpression(FacialExpressionType.NEUTRAL, 1.0);
          }
        }
      }
    }
  }

  /**
   * 更新情感混合控制器
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateEmotionBlendControllers(deltaTime: number): void {
    // 遍历所有混合控制器
    for (const [entity, controller] of this.emotionBlendControllers.entries()) {
      // 更新控制器
      controller.update(deltaTime);

      // 如果控制器没有活跃表情，则移除
      if (!controller.hasActiveExpressions()) {
        this.emotionBlendControllers.delete(entity);
      }
    }
  }

  /**
   * 更新情感记忆
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateEmotionMemories(deltaTime: number): void {
    const currentTime = Date.now();

    // 遍历所有实体的情感记忆
    for (const [entity, memories] of this.emotionMemories.entries()) {
      // 过滤掉过期的记忆
      const validMemories = memories.filter(memory => {
        const elapsedTime = (currentTime - memory.timestamp) / 1000; // 转换为秒
        return elapsedTime < this.config.emotionMemoryDuration;
      });

      // 更新记忆
      if (validMemories.length !== memories.length) {
        this.emotionMemories.set(entity, validMemories);
      }
    }
  }

  /**
   * 添加情感记忆
   * @param entity 实体
   * @param memory 情感记忆
   */
  private addEmotionMemory(entity: Entity, memory: EmotionMemory): void {
    // 获取实体的情感记忆
    let memories = this.emotionMemories.get(entity);

    // 如果没有记忆，则创建
    if (!memories) {
      memories = [];
      this.emotionMemories.set(entity, memories);
    }

    // 添加记忆
    memories.push(memory);

    // 限制记忆数量
    const maxMemories = 10;
    if (memories.length > maxMemories) {
      // 移除最旧的记忆
      memories.sort((a, b) => b.timestamp - a.timestamp);
      memories.splice(maxMemories);
    }
  }

  /**
   * 处理情感传染
   * @param deltaTime 帧间隔时间（秒）
   */
  private processEmotionContagion(deltaTime: number): void {
    // 如果没有活跃响应，则跳过
    if (this.activeResponses.size === 0) return;

    // 获取所有具有面部动画组件的实体
    if (!this.facialAnimationSystem) return;

    const entities = Array.from((this.facialAnimationSystem as any).components.keys()) as Entity[];

    // 遍历所有活跃响应
    for (const [sourceEntity, response] of this.activeResponses.entries()) {
      // 获取源实体位置
      const sourcePosition = this.getEntityPosition(sourceEntity);

      // 如果没有位置信息，则跳过
      if (!sourcePosition) continue;

      // 遍历所有实体
      for (const targetEntity of entities) {
        // 跳过源实体
        if (targetEntity === sourceEntity) continue;

        // 获取目标实体位置
        const targetPosition = this.getEntityPosition(targetEntity);

        // 如果没有位置信息，则跳过
        if (!targetPosition) continue;

        // 计算距离
        const distance = targetPosition.distanceTo(sourcePosition);

        // 如果在传染范围内，则传染情感
        if (distance <= this.config.emotionContagionRange) {
          // 计算传染强度
          const contagionIntensity = response.intensity * this.config.emotionContagionStrength *
                                    (1.0 - distance / this.config.emotionContagionRange);

          // 如果强度低于最小阈值，则跳过
          if (contagionIntensity < this.config.minResponseIntensity) continue;

          // 创建情感事件
          const contagionEvent: EmotionEventData = {
            type: response.eventType,
            source: sourceEntity,
            intensity: contagionIntensity,
            duration: response.duration * 0.7, // 传染的情感持续时间较短
            description: `通过情感传染对${response.eventType}的响应`
          };

          // 触发情感事件
          this.triggerEmotionEvent(contagionEvent);
        }
      }
    }
  }

  /**
   * 获取情感混合控制器
   * @param entity 实体
   * @returns 情感混合控制器
   */
  public getEmotionBlendController(entity: Entity): EmotionBlendController | null {
    return this.emotionBlendControllers.get(entity) || null;
  }

  /**
   * 获取活跃的情感响应
   * @param entity 实体
   * @returns 情感响应数据
   */
  public getActiveResponse(entity: Entity): EmotionResponseData | null {
    return this.activeResponses.get(entity) || null;
  }

  /**
   * 获取情感记忆
   * @param entity 实体
   * @returns 情感记忆数组
   */
  public getEmotionMemories(entity: Entity): EmotionMemory[] {
    return this.emotionMemories.get(entity) || [];
  }

  /**
   * 清除情感记忆
   * @param entity 实体
   */
  public clearEmotionMemories(entity: Entity): void {
    this.emotionMemories.delete(entity);
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
