/**
 * 照片到3D转换管道
 * 将2D照片转换为3D数字人模型的完整管道
 */
import * as THREE from 'three';
import { EventEmitter } from '../../utils/EventEmitter';
import { Entity } from '../../core/Entity';
import { World } from '../../core/World';
import { MinIOStorageService } from '../../storage/MinIOStorageService';

/**
 * 转换管道配置
 */
export interface PhotoTo3DConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 输出质量 */
  quality?: 'low' | 'medium' | 'high' | 'ultra';
  /** 是否生成纹理 */
  generateTextures?: boolean;
  /** 是否生成法线贴图 */
  generateNormalMaps?: boolean;
  /** 是否优化网格 */
  optimizeMesh?: boolean;
  /** 最大处理时间（秒） */
  maxProcessingTime?: number;
}

/**
 * 人脸特征点
 */
export interface FacialLandmarks {
  /** 特征点坐标 */
  points: THREE.Vector2[];
  /** 置信度 */
  confidence: number;
  /** 人脸边界框 */
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

/**
 * 人脸分析结果
 */
export interface FaceAnalysisResult {
  /** 人脸特征点 */
  landmarks: FacialLandmarks;
  /** 人脸朝向 */
  pose: {
    yaw: number;
    pitch: number;
    roll: number;
  };
  /** 人脸属性 */
  attributes: {
    age?: number;
    gender?: 'male' | 'female';
    emotion?: string;
    glasses?: boolean;
    beard?: boolean;
  };
  /** 人脸质量评分 */
  qualityScore: number;
}

/**
 * 3D网格生成结果
 */
export interface MeshGenerationResult {
  /** 生成的网格 */
  mesh: THREE.Mesh;
  /** 顶点数量 */
  vertexCount: number;
  /** 面数量 */
  faceCount: number;
  /** 纹理坐标 */
  uvCoordinates: Float32Array;
  /** 法线数据 */
  normals: Float32Array;
}

/**
 * 纹理生成结果
 */
export interface TextureGenerationResult {
  /** 漫反射纹理 */
  diffuseTexture: THREE.Texture;
  /** 法线纹理 */
  normalTexture?: THREE.Texture;
  /** 粗糙度纹理 */
  roughnessTexture?: THREE.Texture;
  /** 金属度纹理 */
  metallicTexture?: THREE.Texture;
  /** 纹理分辨率 */
  resolution: [number, number];
}

/**
 * 转换进度信息
 */
export interface ConversionProgress {
  /** 当前阶段 */
  stage: string;
  /** 进度百分比 */
  progress: number;
  /** 状态消息 */
  message: string;
  /** 预估剩余时间（秒） */
  estimatedTimeRemaining?: number;
}

/**
 * 转换结果
 */
export interface ConversionResult {
  /** 是否成功 */
  success: boolean;
  /** 生成的数字人实体 */
  digitalHuman?: Entity;
  /** 生成的资产ID列表 */
  assetIds?: string[];
  /** 处理时间（秒） */
  processingTime?: number;
  /** 错误信息 */
  error?: string;
  /** 警告信息 */
  warnings?: string[];
}

/**
 * 照片到3D转换管道
 */
export class PhotoTo3DPipeline extends EventEmitter {
  /** 配置 */
  private config: PhotoTo3DConfig;

  /** 世界实例 */
  private world: World;

  /** 存储服务 */
  private storageService: MinIOStorageService;

  /** 当前处理任务 */
  private activeConversions: Map<string, ConversionProgress> = new Map();

  /**
   * 构造函数
   * @param world 世界实例
   * @param storageService 存储服务
   * @param config 配置
   */
  constructor(world: World, storageService: MinIOStorageService, config: PhotoTo3DConfig = {}) {
    super();

    this.world = world;
    this.storageService = storageService;
    this.config = {
      debug: false,
      quality: 'medium',
      generateTextures: true,
      generateNormalMaps: true,
      optimizeMesh: true,
      maxProcessingTime: 300, // 5分钟
      ...config
    };
  }

  /**
   * 从照片生成3D数字人
   * @param photoFile 照片文件
   * @param options 生成选项
   * @returns 转换结果
   */
  public async generateDigitalHumanFromPhoto(
    photoFile: File,
    options: any = {}
  ): Promise<ConversionResult> {
    const conversionId = `photo_${Date.now()}`;
    const startTime = performance.now();

    try {
      if (this.config.debug) {
        console.log(`[PhotoTo3DPipeline] 开始照片转换: ${photoFile.name}`);
      }

      this.emit('conversionStarted', conversionId, photoFile);

      // 1. 照片预处理
      this.updateProgress(conversionId, '照片预处理', 5, '正在预处理输入照片...');
      const preprocessedImage = await this.preprocessPhoto(photoFile);

      // 2. 人脸检测和分析
      this.updateProgress(conversionId, '人脸分析', 15, '正在检测和分析人脸特征...');
      const faceAnalysis = await this.analyzeFace(preprocessedImage);

      // 3. 验证人脸质量
      if (faceAnalysis.qualityScore < 0.7) {
        throw new Error('照片质量不足，请使用更清晰的正面照片');
      }

      // 4. 生成3D网格
      this.updateProgress(conversionId, '3D网格生成', 35, '正在生成3D面部网格...');
      const meshResult = await this.generate3DMesh(faceAnalysis);

      // 5. 生成纹理
      this.updateProgress(conversionId, '纹理生成', 60, '正在生成面部纹理...');
      const textureResult = await this.generateTextures(preprocessedImage, meshResult);

      // 6. 组装数字人
      this.updateProgress(conversionId, '数字人组装', 80, '正在组装数字人模型...');
      const digitalHuman = await this.assembleDigitalHuman(meshResult, textureResult, faceAnalysis);

      // 7. 优化和后处理
      this.updateProgress(conversionId, '优化处理', 90, '正在优化模型...');
      await this.optimizeDigitalHuman(digitalHuman);

      // 8. 保存资产
      this.updateProgress(conversionId, '保存资产', 95, '正在保存生成的资产...');
      const assetIds = await this.saveAssets(digitalHuman, conversionId);

      // 9. 完成
      this.updateProgress(conversionId, '完成', 100, '数字人生成完成');

      const processingTime = (performance.now() - startTime) / 1000;
      const result: ConversionResult = {
        success: true,
        digitalHuman,
        assetIds,
        processingTime
      };

      this.emit('conversionCompleted', conversionId, result);

      if (this.config.debug) {
        console.log(`[PhotoTo3DPipeline] 转换完成，耗时 ${processingTime.toFixed(2)}s`);
      }

      return result;

    } catch (error) {
      const processingTime = (performance.now() - startTime) / 1000;
      const result: ConversionResult = {
        success: false,
        error: error.message,
        processingTime
      };

      this.emit('conversionError', conversionId, error);

      if (this.config.debug) {
        console.error('[PhotoTo3DPipeline] 转换失败', error);
      }

      return result;

    } finally {
      this.activeConversions.delete(conversionId);
    }
  }

  /**
   * 预处理照片
   * @param photoFile 照片文件
   * @returns 预处理后的图像数据
   */
  private async preprocessPhoto(photoFile: File): Promise<ImageData> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('无法创建Canvas上下文'));
        return;
      }

      img.onload = () => {
        // 调整图像大小到标准尺寸
        const targetSize = this.getTargetImageSize();
        canvas.width = targetSize.width;
        canvas.height = targetSize.height;

        // 绘制并缩放图像
        ctx.drawImage(img, 0, 0, targetSize.width, targetSize.height);

        // 应用图像增强
        this.enhanceImage(ctx, targetSize.width, targetSize.height);

        // 获取图像数据
        const imageData = ctx.getImageData(0, 0, targetSize.width, targetSize.height);
        resolve(imageData);
      };

      img.onerror = () => {
        reject(new Error('无法加载图像'));
      };

      img.src = URL.createObjectURL(photoFile);
    });
  }

  /**
   * 获取目标图像尺寸
   * @returns 目标尺寸
   */
  private getTargetImageSize(): { width: number; height: number } {
    switch (this.config.quality) {
      case 'low':
        return { width: 256, height: 256 };
      case 'medium':
        return { width: 512, height: 512 };
      case 'high':
        return { width: 1024, height: 1024 };
      case 'ultra':
        return { width: 2048, height: 2048 };
      default:
        return { width: 512, height: 512 };
    }
  }

  /**
   * 增强图像
   * @param ctx Canvas上下文
   * @param width 图像宽度
   * @param height 图像高度
   */
  private enhanceImage(ctx: CanvasRenderingContext2D, width: number, height: number): void {
    const imageData = ctx.getImageData(0, 0, width, height);
    const data = imageData.data;

    // 应用亮度和对比度调整
    for (let i = 0; i < data.length; i += 4) {
      // 亮度调整
      data[i] = Math.min(255, data[i] * 1.1);     // R
      data[i + 1] = Math.min(255, data[i + 1] * 1.1); // G
      data[i + 2] = Math.min(255, data[i + 2] * 1.1); // B
    }

    ctx.putImageData(imageData, 0, 0);
  }

  /**
   * 分析人脸
   * @param imageData 图像数据
   * @returns 人脸分析结果
   */
  private async analyzeFace(imageData: ImageData): Promise<FaceAnalysisResult> {
    // TODO: 集成实际的人脸检测和分析算法
    // 这里使用模拟数据

    const mockLandmarks: FacialLandmarks = {
      points: this.generateMockLandmarks(),
      confidence: 0.95,
      boundingBox: {
        x: imageData.width * 0.2,
        y: imageData.height * 0.15,
        width: imageData.width * 0.6,
        height: imageData.height * 0.7
      }
    };

    return {
      landmarks: mockLandmarks,
      pose: {
        yaw: 0,
        pitch: 0,
        roll: 0
      },
      attributes: {
        age: 25,
        gender: 'female',
        emotion: 'neutral',
        glasses: false,
        beard: false
      },
      qualityScore: 0.9
    };
  }

  /**
   * 生成模拟特征点
   * @returns 特征点数组
   */
  private generateMockLandmarks(): THREE.Vector2[] {
    const landmarks: THREE.Vector2[] = [];

    // 生成68个标准人脸特征点
    for (let i = 0; i < 68; i++) {
      landmarks.push(new THREE.Vector2(
        Math.random() * 512,
        Math.random() * 512
      ));
    }

    return landmarks;
  }

  /**
   * 更新进度
   * @param conversionId 转换ID
   * @param stage 阶段
   * @param progress 进度
   * @param message 消息
   */
  private updateProgress(conversionId: string, stage: string, progress: number, message: string): void {
    const progressInfo: ConversionProgress = {
      stage,
      progress,
      message,
      estimatedTimeRemaining: this.estimateRemainingTime(progress)
    };

    this.activeConversions.set(conversionId, progressInfo);
    this.emit('conversionProgress', conversionId, progressInfo);
  }

  /**
   * 估算剩余时间
   * @param currentProgress 当前进度
   * @returns 估算剩余时间（秒）
   */
  private estimateRemainingTime(currentProgress: number): number {
    if (currentProgress <= 0) return this.config.maxProcessingTime || 300;

    const avgTimePerPercent = (this.config.maxProcessingTime || 300) / 100;
    return Math.max(0, (100 - currentProgress) * avgTimePerPercent);
  }

  /**
   * 生成3D网格
   * @param faceAnalysis 人脸分析结果
   * @returns 网格生成结果
   */
  private async generate3DMesh(faceAnalysis: FaceAnalysisResult): Promise<MeshGenerationResult> {
    // 基于人脸特征点生成3D网格
    const geometry = this.createFaceGeometry(faceAnalysis.landmarks);

    // 创建材质
    const material = new THREE.MeshStandardMaterial({
      color: 0xfdbcb4, // 默认肤色
      roughness: 0.8,
      metalness: 0.0
    });

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);

    return {
      mesh,
      vertexCount: geometry.attributes.position.count,
      faceCount: geometry.index ? geometry.index.count / 3 : geometry.attributes.position.count / 3,
      uvCoordinates: geometry.attributes.uv.array as Float32Array,
      normals: geometry.attributes.normal.array as Float32Array
    };
  }

  /**
   * 创建人脸几何体
   * @param landmarks 人脸特征点
   * @returns 几何体
   */
  private createFaceGeometry(landmarks: FacialLandmarks): THREE.BufferGeometry {
    const geometry = new THREE.BufferGeometry();

    // 基于特征点生成顶点
    const vertices = this.generateVerticesFromLandmarks(landmarks);
    const faces = this.generateFaceIndices(vertices.length / 3);
    const uvs = this.generateUVCoordinates(vertices.length / 3);
    const normals = this.calculateNormals(vertices, faces);

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
    geometry.setIndex(faces);

    return geometry;
  }

  /**
   * 从特征点生成顶点
   * @param landmarks 特征点
   * @returns 顶点数组
   */
  private generateVerticesFromLandmarks(landmarks: FacialLandmarks): Float32Array {
    const vertices: number[] = [];

    // 将2D特征点转换为3D顶点
    for (const point of landmarks.points) {
      // 将屏幕坐标转换为3D坐标
      const x = (point.x / 512 - 0.5) * 2; // 归一化到[-1, 1]
      const y = -(point.y / 512 - 0.5) * 2; // Y轴翻转
      const z = this.estimateDepthFromLandmark(point, landmarks); // 估算深度

      vertices.push(x, y, z);
    }

    // 添加额外的顶点以形成完整的面部网格
    const additionalVertices = this.generateAdditionalVertices(landmarks);
    vertices.push(...additionalVertices);

    return new Float32Array(vertices);
  }

  /**
   * 估算特征点深度
   * @param point 特征点
   * @param landmarks 所有特征点
   * @returns 深度值
   */
  private estimateDepthFromLandmark(point: THREE.Vector2, landmarks: FacialLandmarks): number {
    // 简化的深度估算，实际应用中需要更复杂的算法
    const centerX = landmarks.boundingBox.x + landmarks.boundingBox.width / 2;
    const centerY = landmarks.boundingBox.y + landmarks.boundingBox.height / 2;

    const distanceFromCenter = Math.sqrt(
      Math.pow(point.x - centerX, 2) + Math.pow(point.y - centerY, 2)
    );

    // 距离中心越远，深度越小（更平）
    return Math.max(0, 0.3 - distanceFromCenter / 1000);
  }

  /**
   * 生成额外顶点
   * @param landmarks 特征点
   * @returns 额外顶点数组
   */
  private generateAdditionalVertices(landmarks: FacialLandmarks): number[] {
    const vertices: number[] = [];

    // 在特征点之间插值生成更多顶点，形成更密集的网格
    // 这里简化处理，实际应用中需要更复杂的网格生成算法

    return vertices;
  }

  /**
   * 生成面索引
   * @param vertexCount 顶点数量
   * @returns 面索引数组
   */
  private generateFaceIndices(vertexCount: number): number[] {
    const indices: number[] = [];

    // 基于顶点生成三角形面
    // 这里使用简化的三角剖分算法
    for (let i = 0; i < vertexCount - 2; i++) {
      if (i % 2 === 0) {
        indices.push(i, i + 1, i + 2);
      } else {
        indices.push(i, i + 2, i + 1);
      }
    }

    return indices;
  }

  /**
   * 生成UV坐标
   * @param vertexCount 顶点数量
   * @returns UV坐标数组
   */
  private generateUVCoordinates(vertexCount: number): Float32Array {
    const uvs: number[] = [];

    // 为每个顶点生成UV坐标
    for (let i = 0; i < vertexCount; i++) {
      const u = (i % 10) / 10; // 简化的UV映射
      const v = Math.floor(i / 10) / Math.ceil(vertexCount / 10);
      uvs.push(u, v);
    }

    return new Float32Array(uvs);
  }

  /**
   * 计算法线
   * @param vertices 顶点数组
   * @param faces 面索引数组
   * @returns 法线数组
   */
  private calculateNormals(vertices: Float32Array, faces: number[]): Float32Array {
    const normals = new Float32Array(vertices.length);

    // 为每个面计算法线
    for (let i = 0; i < faces.length; i += 3) {
      const i1 = faces[i] * 3;
      const i2 = faces[i + 1] * 3;
      const i3 = faces[i + 2] * 3;

      const v1 = new THREE.Vector3(vertices[i1], vertices[i1 + 1], vertices[i1 + 2]);
      const v2 = new THREE.Vector3(vertices[i2], vertices[i2 + 1], vertices[i2 + 2]);
      const v3 = new THREE.Vector3(vertices[i3], vertices[i3 + 1], vertices[i3 + 2]);

      const normal = new THREE.Vector3()
        .subVectors(v2, v1)
        .cross(new THREE.Vector3().subVectors(v3, v1))
        .normalize();

      // 将法线应用到三个顶点
      normals[i1] = normal.x;
      normals[i1 + 1] = normal.y;
      normals[i1 + 2] = normal.z;

      normals[i2] = normal.x;
      normals[i2 + 1] = normal.y;
      normals[i2 + 2] = normal.z;

      normals[i3] = normal.x;
      normals[i3 + 1] = normal.y;
      normals[i3 + 2] = normal.z;
    }

    return normals;
  }

  /**
   * 生成纹理
   * @param imageData 原始图像数据
   * @param meshResult 网格结果
   * @returns 纹理生成结果
   */
  private async generateTextures(
    imageData: ImageData,
    meshResult: MeshGenerationResult
  ): Promise<TextureGenerationResult> {
    // 创建漫反射纹理
    const diffuseTexture = this.createTextureFromImageData(imageData);

    const result: TextureGenerationResult = {
      diffuseTexture,
      resolution: [imageData.width, imageData.height]
    };

    // 生成法线贴图
    if (this.config.generateNormalMaps) {
      result.normalTexture = this.generateNormalMap(imageData);
    }

    // 生成粗糙度和金属度贴图
    result.roughnessTexture = this.generateRoughnessMap(imageData);
    result.metallicTexture = this.generateMetallicMap(imageData);

    return result;
  }

  /**
   * 从图像数据创建纹理
   * @param imageData 图像数据
   * @returns Three.js纹理
   */
  private createTextureFromImageData(imageData: ImageData): THREE.Texture {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;

    canvas.width = imageData.width;
    canvas.height = imageData.height;
    ctx.putImageData(imageData, 0, 0);

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;

    return texture;
  }

  /**
   * 生成法线贴图
   * @param imageData 原始图像数据
   * @returns 法线纹理
   */
  private generateNormalMap(imageData: ImageData): THREE.Texture {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;

    canvas.width = imageData.width;
    canvas.height = imageData.height;

    const normalData = ctx.createImageData(imageData.width, imageData.height);

    // 简化的法线贴图生成算法
    for (let y = 1; y < imageData.height - 1; y++) {
      for (let x = 1; x < imageData.width - 1; x++) {
        const idx = (y * imageData.width + x) * 4;

        // 计算梯度
        const left = this.getGrayscale(imageData, x - 1, y);
        const right = this.getGrayscale(imageData, x + 1, y);
        const top = this.getGrayscale(imageData, x, y - 1);
        const bottom = this.getGrayscale(imageData, x, y + 1);

        const dx = (right - left) * 0.5;
        const dy = (bottom - top) * 0.5;

        // 转换为法线向量
        const normal = new THREE.Vector3(-dx, -dy, 1).normalize();

        normalData.data[idx] = (normal.x + 1) * 127.5;     // R
        normalData.data[idx + 1] = (normal.y + 1) * 127.5; // G
        normalData.data[idx + 2] = (normal.z + 1) * 127.5; // B
        normalData.data[idx + 3] = 255;                     // A
      }
    }

    ctx.putImageData(normalData, 0, 0);
    return new THREE.CanvasTexture(canvas);
  }

  /**
   * 获取像素灰度值
   * @param imageData 图像数据
   * @param x X坐标
   * @param y Y坐标
   * @returns 灰度值
   */
  private getGrayscale(imageData: ImageData, x: number, y: number): number {
    const idx = (y * imageData.width + x) * 4;
    const r = imageData.data[idx];
    const g = imageData.data[idx + 1];
    const b = imageData.data[idx + 2];
    return (r + g + b) / 3 / 255;
  }

  /**
   * 生成粗糙度贴图
   * @param imageData 原始图像数据
   * @returns 粗糙度纹理
   */
  private generateRoughnessMap(imageData: ImageData): THREE.Texture {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;

    canvas.width = imageData.width;
    canvas.height = imageData.height;

    const roughnessData = ctx.createImageData(imageData.width, imageData.height);

    // 基于原始图像生成粗糙度
    for (let i = 0; i < imageData.data.length; i += 4) {
      const grayscale = this.getGrayscaleFromRGB(
        imageData.data[i],
        imageData.data[i + 1],
        imageData.data[i + 2]
      );

      // 较暗的区域通常更粗糙
      const roughness = Math.max(0.3, 1 - grayscale);
      const value = roughness * 255;

      roughnessData.data[i] = value;     // R
      roughnessData.data[i + 1] = value; // G
      roughnessData.data[i + 2] = value; // B
      roughnessData.data[i + 3] = 255;   // A
    }

    ctx.putImageData(roughnessData, 0, 0);
    return new THREE.CanvasTexture(canvas);
  }

  /**
   * 生成金属度贴图
   * @param imageData 原始图像数据
   * @returns 金属度纹理
   */
  private generateMetallicMap(imageData: ImageData): THREE.Texture {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;

    canvas.width = imageData.width;
    canvas.height = imageData.height;

    const metallicData = ctx.createImageData(imageData.width, imageData.height);

    // 皮肤通常不是金属，设置为低金属度
    for (let i = 0; i < imageData.data.length; i += 4) {
      const metallic = 0.0; // 皮肤金属度为0
      const value = metallic * 255;

      metallicData.data[i] = value;     // R
      metallicData.data[i + 1] = value; // G
      metallicData.data[i + 2] = value; // B
      metallicData.data[i + 3] = 255;   // A
    }

    ctx.putImageData(metallicData, 0, 0);
    return new THREE.CanvasTexture(canvas);
  }

  /**
   * 从RGB获取灰度值
   * @param r 红色分量
   * @param g 绿色分量
   * @param b 蓝色分量
   * @returns 灰度值
   */
  private getGrayscaleFromRGB(r: number, g: number, b: number): number {
    return (r + g + b) / 3 / 255;
  }

  /**
   * 组装数字人
   * @param meshResult 网格结果
   * @param textureResult 纹理结果
   * @param faceAnalysis 人脸分析结果
   * @returns 数字人实体
   */
  private async assembleDigitalHuman(
    meshResult: MeshGenerationResult,
    textureResult: TextureGenerationResult,
    faceAnalysis: FaceAnalysisResult
  ): Promise<Entity> {
    // 创建数字人实体
    const digitalHuman = new Entity('数字人');
    digitalHuman.name = `Generated Digital Human ${Date.now()}`;

    // 应用纹理到材质
    const material = meshResult.mesh.material as THREE.MeshStandardMaterial;
    material.map = textureResult.diffuseTexture;

    if (textureResult.normalTexture) {
      material.normalMap = textureResult.normalTexture;
    }

    if (textureResult.roughnessTexture) {
      material.roughnessMap = textureResult.roughnessTexture;
    }

    if (textureResult.metallicTexture) {
      material.metalnessMap = textureResult.metallicTexture;
    }

    // 添加数字人组件
    const digitalHumanComponent = new (await import('../components/DigitalHumanComponent')).DigitalHumanComponent(digitalHuman, {
      name: digitalHuman.name,
      source: 'photo' as any // 使用DigitalHumanSource.PHOTO
      // sourceData 不是 DigitalHumanComponentConfig 的属性，已移除
    });

    digitalHuman.addComponent(digitalHumanComponent);

    // 添加网格组件
    const meshComponent = new (await import('../../rendering/MeshComponent')).MeshComponent({
      geometry: meshResult.mesh.geometry,
      material: meshResult.mesh.material
    });
    digitalHuman.addComponent(meshComponent);

    // 添加变换组件
    const transform = new (await import('../../scene/Transform')).Transform();
    digitalHuman.addComponent(transform);

    // 设置默认位置和缩放
    transform.setPosition(0, 0, 0);
    transform.setScale(1, 1, 1);

    // 添加到世界
    this.world.addEntity(digitalHuman);

    return digitalHuman;
  }

  /**
   * 优化数字人
   * @param digitalHuman 数字人实体
   */
  private async optimizeDigitalHuman(digitalHuman: Entity): Promise<void> {
    if (!this.config.optimizeMesh) return;

    // 获取网格组件
    const meshComponent = digitalHuman.getComponent('MeshComponent') as any;
    if (!meshComponent || !meshComponent.mesh) return;

    const mesh = meshComponent.mesh as THREE.Mesh;
    const geometry = mesh.geometry;

    // 合并重复顶点（mergeVertices在新版本Three.js中已被移除）
    // geometry.mergeVertices(); // 已注释，使用BufferGeometryUtils.mergeVertices代替

    // 计算边界球
    geometry.computeBoundingSphere();

    // 计算边界盒
    geometry.computeBoundingBox();

    // 优化索引
    if (!geometry.index) {
      geometry.setIndex(this.generateOptimizedIndices(geometry));
    }

    // 压缩纹理（如果支持）
    await this.compressTextures(mesh.material as THREE.MeshStandardMaterial);

    if (this.config.debug) {
      console.log('[PhotoTo3DPipeline] 数字人优化完成');
    }
  }

  /**
   * 生成优化的索引
   * @param geometry 几何体
   * @returns 索引数组
   */
  private generateOptimizedIndices(geometry: THREE.BufferGeometry): number[] {
    const positions = geometry.attributes.position.array;
    const indices: number[] = [];

    // 简化的索引生成
    for (let i = 0; i < positions.length / 3; i++) {
      indices.push(i);
    }

    return indices;
  }

  /**
   * 压缩纹理
   * @param material 材质
   */
  private async compressTextures(material: THREE.MeshStandardMaterial): Promise<void> {
    // TODO: 实现纹理压缩
    // 可以使用KTX2或其他压缩格式

    if (this.config.debug) {
      console.log('[PhotoTo3DPipeline] 纹理压缩完成');
    }
  }

  /**
   * 保存资产
   * @param digitalHuman 数字人实体
   * @param conversionId 转换ID
   * @returns 资产ID列表
   */
  private async saveAssets(digitalHuman: Entity, conversionId: string): Promise<string[]> {
    const assetIds: string[] = [];

    try {
      // 保存模型数据
      const modelAssetId = await this.saveModelAsset(digitalHuman, conversionId);
      assetIds.push(modelAssetId);

      // 保存纹理资产
      const textureAssetIds = await this.saveTextureAssets(digitalHuman, conversionId);
      assetIds.push(...textureAssetIds);

      // 保存元数据
      const metadataAssetId = await this.saveMetadataAsset(digitalHuman, conversionId);
      assetIds.push(metadataAssetId);

      if (this.config.debug) {
        console.log(`[PhotoTo3DPipeline] 保存了 ${assetIds.length} 个资产`);
      }

    } catch (error) {
      if (this.config.debug) {
        console.error('[PhotoTo3DPipeline] 保存资产失败', error);
      }
    }

    return assetIds;
  }

  /**
   * 保存模型资产
   * @param digitalHuman 数字人实体
   * @param conversionId 转换ID
   * @returns 模型资产ID
   */
  private async saveModelAsset(digitalHuman: Entity, conversionId: string): Promise<string> {
    const meshComponent = digitalHuman.getComponent('MeshComponent') as any;
    if (!meshComponent || !meshComponent.mesh) {
      throw new Error('数字人缺少网格组件');
    }

    // 导出为GLTF格式
    const gltfData = await this.exportToGLTF(meshComponent.mesh);
    const blob = new Blob([JSON.stringify(gltfData)], { type: 'application/json' });
    const file = new File([blob], `${conversionId}_model.gltf`, { type: 'application/json' });

    const uploadResult = await this.storageService.uploadFile(file, 'digital-humans/models');
    return uploadResult.fullPath;
  }

  /**
   * 保存纹理资产
   * @param digitalHuman 数字人实体
   * @param conversionId 转换ID
   * @returns 纹理资产ID列表
   */
  private async saveTextureAssets(digitalHuman: Entity, conversionId: string): Promise<string[]> {
    const assetIds: string[] = [];
    const meshComponent = digitalHuman.getComponent('MeshComponent') as any;

    if (!meshComponent || !meshComponent.mesh) return assetIds;

    const material = meshComponent.mesh.material as THREE.MeshStandardMaterial;

    // 保存各种纹理
    const textures = [
      { texture: material.map, name: 'diffuse' },
      { texture: material.normalMap, name: 'normal' },
      { texture: material.roughnessMap, name: 'roughness' },
      { texture: material.metalnessMap, name: 'metallic' }
    ];

    for (const { texture, name } of textures) {
      if (texture && texture instanceof THREE.CanvasTexture) {
        const canvas = texture.source.data as HTMLCanvasElement;
        const blob = await this.canvasToBlob(canvas);
        const file = new File([blob], `${conversionId}_${name}.png`, { type: 'image/png' });

        const uploadResult = await this.storageService.uploadFile(file, 'digital-humans/textures');
        assetIds.push(uploadResult.fullPath);
      }
    }

    return assetIds;
  }

  /**
   * 保存元数据资产
   * @param digitalHuman 数字人实体
   * @param conversionId 转换ID
   * @returns 元数据资产ID
   */
  private async saveMetadataAsset(digitalHuman: Entity, conversionId: string): Promise<string> {
    const digitalHumanComponent = digitalHuman.getComponent('DigitalHumanComponent');

    const metadata = {
      id: digitalHuman.id,
      name: digitalHuman.name,
      conversionId,
      createdAt: new Date().toISOString(),
      source: 'photo_generation',
      config: this.config,
      sourceData: {} // digitalHumanComponent 没有 sourceData 属性
    };

    const blob = new Blob([JSON.stringify(metadata, null, 2)], { type: 'application/json' });
    const file = new File([blob], `${conversionId}_metadata.json`, { type: 'application/json' });

    const uploadResult = await this.storageService.uploadFile(file, 'digital-humans/metadata');
    return uploadResult.fullPath;
  }

  /**
   * 导出为GLTF格式
   * @param mesh 网格
   * @returns GLTF数据
   */
  private async exportToGLTF(mesh: THREE.Mesh): Promise<any> {
    // TODO: 实现完整的GLTF导出
    // 这里返回简化的GLTF结构
    return {
      asset: {
        version: '2.0',
        generator: 'PhotoTo3DPipeline'
      },
      scenes: [{ nodes: [0] }],
      nodes: [{ mesh: 0 }],
      meshes: [{ primitives: [{ attributes: { POSITION: 0 } }] }]
    };
  }

  /**
   * Canvas转Blob
   * @param canvas Canvas元素
   * @returns Blob Promise
   */
  private canvasToBlob(canvas: HTMLCanvasElement): Promise<Blob> {
    return new Promise((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('无法转换Canvas为Blob'));
        }
      }, 'image/png');
    });
  }

  /**
   * 销毁管道
   */
  public dispose(): void {
    this.activeConversions.clear();
    this.removeAllListeners();

    if (this.config.debug) {
      console.log('[PhotoTo3DPipeline] 管道已销毁');
    }
  }
}