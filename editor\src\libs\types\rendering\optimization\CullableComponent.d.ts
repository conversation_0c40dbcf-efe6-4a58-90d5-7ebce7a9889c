/**
 * 可剔除组件
 * 用于标记可以被视锥体剔除的实体
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
/**
 * 可剔除组件选项接口
 */
export interface CullableComponentOptions {
    /** 包围半径 */
    boundingRadius?: number;
    /** 是否自动计算包围半径 */
    autoComputeBoundingRadius?: boolean;
    /** 是否使用包围盒 */
    useBoundingBox?: boolean;
    /** 是否自动计算包围盒 */
    autoComputeBoundingBox?: boolean;
    /** 是否可见 */
    visible?: boolean;
    /** 是否可剔除 */
    cullable?: boolean;
}
/**
 * 可剔除组件类
 */
export declare class CullableComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 包围半径 */
    private boundingRadius;
    /** 是否自动计算包围半径 */
    private autoComputeBoundingRadius;
    /** 是否使用包围盒 */
    private useBoundingBox;
    /** 是否自动计算包围盒 */
    private autoComputeBoundingBox;
    /** 包围盒 */
    private boundingBox;
    /** 是否可见 */
    private visible;
    /** 是否可剔除 */
    private cullable;
    /** 原始可见性 */
    private originalVisibility;
    /**
     * 创建可剔除组件
     * @param options 可剔除组件选项
     */
    constructor(options?: CullableComponentOptions);
    /**
     * 当组件附加到实体时调用
     */
    protected onAttach(): void;
    /**
     * 当组件从实体分离时调用
     */
    protected onDetach(): void;
    /**
     * 保存原始可见性
     * @param object Three.js对象
     */
    private saveOriginalVisibility;
    /**
     * 恢复原始可见性
     * @param object Three.js对象
     */
    private restoreOriginalVisibility;
    /**
     * 计算包围半径
     */
    computeBoundingRadius(): void;
    /**
     * 计算包围盒
     */
    computeBoundingBox(): void;
    /**
     * 更新可见性
     */
    private updateVisibility;
    /**
     * 设置对象的可见性
     * @param object Three.js对象
     * @param visible 是否可见
     */
    private setObjectVisibility;
    /**
     * 设置是否可见
     * @param visible 是否可见
     */
    setVisible(visible: boolean): void;
    /**
     * 获取是否可见
     * @returns 是否可见
     */
    isVisible(): boolean;
    /**
     * 设置是否可剔除
     * @param cullable 是否可剔除
     */
    setCullable(cullable: boolean): void;
    /**
     * 获取是否可剔除
     * @returns 是否可剔除
     */
    isCullable(): boolean;
    /**
     * 设置包围半径
     * @param radius 半径
     */
    setBoundingRadius(radius: number): void;
    /**
     * 获取包围半径
     * @returns 包围半径
     */
    getBoundingRadius(): number;
    /**
     * 设置是否自动计算包围半径
     * @param auto 是否自动计算
     */
    setAutoComputeBoundingRadius(auto: boolean): void;
    /**
     * 获取是否自动计算包围半径
     * @returns 是否自动计算
     */
    isAutoComputeBoundingRadius(): boolean;
    /**
     * 设置是否使用包围盒
     * @param use 是否使用
     */
    setUseBoundingBox(use: boolean): void;
    /**
     * 获取是否使用包围盒
     * @returns 是否使用
     */
    isUseBoundingBox(): boolean;
    /**
     * 设置是否自动计算包围盒
     * @param auto 是否自动计算
     */
    setAutoComputeBoundingBox(auto: boolean): void;
    /**
     * 获取是否自动计算包围盒
     * @returns 是否自动计算
     */
    isAutoComputeBoundingBox(): boolean;
    /**
     * 设置包围盒
     * @param box 包围盒
     */
    setBoundingBox(box: THREE.Box3): void;
    /**
     * 获取包围盒
     * @returns 包围盒
     */
    getBoundingBox(): THREE.Box3 | null;
}
