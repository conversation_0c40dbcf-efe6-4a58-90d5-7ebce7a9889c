/**
 * 触摸输入设备
 */
import { BaseInputDevice } from '../InputDevice';

/**
 * 触摸输入设备
 */
export class TouchDevice extends BaseInputDevice {
  /** 目标元素 */
  private element: HTMLElement;

  /** 是否阻止默认行为 */
  private preventDefault: boolean;

  /** 是否阻止事件传播 */
  private stopPropagation: boolean;

  /** 触摸点映射 */
  private touchPoints: Map<number, Touch> = new Map();

  /** 触摸事件处理器 */
  private touchEventHandlers: { [key: string]: (event: TouchEvent) => void } = {};

  /**
   * 创建触摸输入设备
   * @param element 目标元素
   * @param preventDefault 是否阻止默认行为
   * @param stopPropagation 是否阻止事件传播
   */
  constructor(element: HTMLElement = document.body, preventDefault: boolean = true, stopPropagation: boolean = false) {
    super('touch');
    this.element = element;
    this.preventDefault = preventDefault;
    this.stopPropagation = stopPropagation;

    // 初始化事件处理器
    this.initEventHandlers();
  }

  /**
   * 初始化事件处理器
   */
  private initEventHandlers(): void {
    // 触摸开始事件
    this.touchEventHandlers.touchstart = this.handleTouchStart.bind(this);

    // 触摸移动事件
    this.touchEventHandlers.touchmove = this.handleTouchMove.bind(this);

    // 触摸结束事件
    this.touchEventHandlers.touchend = this.handleTouchEnd.bind(this);

    // 触摸取消事件
    this.touchEventHandlers.touchcancel = this.handleTouchCancel.bind(this);
  }

  /**
   * 初始化设备
   */
  public initialize(): void {
    if (this.initialized) return;

    // 添加事件监听器
    this.addEventListeners();

    super.initialize();
  }

  /**
   * 销毁设备
   */
  public destroy(): void {
    if (this.destroyed) return;

    // 移除事件监听器
    this.removeEventListeners();

    // 清空触摸点映射
    this.touchPoints.clear();

    super.destroy();
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 添加触摸事件监听器
    for (const [event, handler] of Object.entries(this.touchEventHandlers)) {
      this.element.addEventListener(event, handler as EventListener, { passive: !this.preventDefault });
    }
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 移除触摸事件监听器
    for (const [event, handler] of Object.entries(this.touchEventHandlers)) {
      this.element.removeEventListener(event, handler as EventListener);
    }
  }

  /**
   * 处理触摸开始事件
   * @param event 触摸事件
   */
  private handleTouchStart(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      this.touchPoints.set(touch.identifier, touch);
      this.setValue(`touch:${touch.identifier}:active`, true);
      this.setValue(`touch:${touch.identifier}:x`, touch.clientX);
      this.setValue(`touch:${touch.identifier}:y`, touch.clientY);
    }

    // 触发触摸开始事件
    this.eventEmitter.emit('touchstart', {
      touches: Array.from(event.touches),
      changedTouches: Array.from(event.changedTouches),
      targetTouches: Array.from(event.targetTouches),
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理触摸移动事件
   * @param event 触摸事件
   */
  private handleTouchMove(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      const oldTouch = this.touchPoints.get(touch.identifier);
      this.touchPoints.set(touch.identifier, touch);
      this.setValue(`touch:${touch.identifier}:x`, touch.clientX);
      this.setValue(`touch:${touch.identifier}:y`, touch.clientY);
      
      if (oldTouch) {
        this.setValue(`touch:${touch.identifier}:deltaX`, touch.clientX - oldTouch.clientX);
        this.setValue(`touch:${touch.identifier}:deltaY`, touch.clientY - oldTouch.clientY);
      }
    }

    // 触发触摸移动事件
    this.eventEmitter.emit('touchmove', {
      touches: Array.from(event.touches),
      changedTouches: Array.from(event.changedTouches),
      targetTouches: Array.from(event.targetTouches),
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理触摸结束事件
   * @param event 触摸事件
   */
  private handleTouchEnd(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      this.touchPoints.delete(touch.identifier);
      this.setValue(`touch:${touch.identifier}:active`, false);
    }

    // 触发触摸结束事件
    this.eventEmitter.emit('touchend', {
      touches: Array.from(event.touches),
      changedTouches: Array.from(event.changedTouches),
      targetTouches: Array.from(event.targetTouches),
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理触摸取消事件
   * @param event 触摸事件
   */
  private handleTouchCancel(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      this.touchPoints.delete(touch.identifier);
      this.setValue(`touch:${touch.identifier}:active`, false);
    }

    // 触发触摸取消事件
    this.eventEmitter.emit('touchcancel', {
      touches: Array.from(event.touches),
      changedTouches: Array.from(event.changedTouches),
      targetTouches: Array.from(event.targetTouches),
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 获取触摸点
   * @param identifier 触摸点标识符
   * @returns 触摸点
   */
  public getTouchPoint(identifier: number): Touch | undefined {
    return this.touchPoints.get(identifier);
  }

  /**
   * 获取所有触摸点
   * @returns 触摸点列表
   */
  public getTouchPoints(): Touch[] {
    return Array.from(this.touchPoints.values());
  }

  /**
   * 获取触摸点数量
   * @returns 触摸点数量
   */
  public getTouchCount(): number {
    return this.touchPoints.size;
  }

  /**
   * 检查触摸点是否活跃
   * @param identifier 触摸点标识符
   * @returns 是否活跃
   */
  public isTouchActive(identifier: number): boolean {
    return !!this.getValue(`touch:${identifier}:active`);
  }

  /**
   * 获取触摸点位置
   * @param identifier 触摸点标识符
   * @returns 触摸点位置
   */
  public getTouchPosition(identifier: number): { x: number; y: number } | undefined {
    const x = this.getValue(`touch:${identifier}:x`);
    const y = this.getValue(`touch:${identifier}:y`);
    if (x !== undefined && y !== undefined) {
      return { x, y };
    }
    return undefined;
  }

  /**
   * 获取触摸点移动
   * @param identifier 触摸点标识符
   * @returns 触摸点移动
   */
  public getTouchDelta(identifier: number): { x: number; y: number } | undefined {
    const deltaX = this.getValue(`touch:${identifier}:deltaX`);
    const deltaY = this.getValue(`touch:${identifier}:deltaY`);
    if (deltaX !== undefined && deltaY !== undefined) {
      return { x: deltaX, y: deltaY };
    }
    return undefined;
  }
}
