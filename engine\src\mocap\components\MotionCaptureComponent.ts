/**
 * 动作捕捉组件
 * 用于存储动作捕捉数据
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { LandmarkData, WorldLandmarkData   } from '../types/LandmarkData';
import { VRMHumanBoneName } from '../../avatar/types/VRMHumanBoneName';
import { Quaternion, Vector3 } from 'three';

/**
 * 动作捕捉组件配置
 */
export interface MotionCaptureComponentConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 平滑系数 */
  smoothingFactor?: number;
  /** 可见度阈值 */
  visibilityThreshold?: number;
}

/**
 * 动作捕捉组件
 * 存储动作捕捉数据和骨骼映射
 */
export class MotionCaptureComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'MotionCaptureComponent';

  /** 平滑系数 */
  public smoothingFactor: number;
  
  /** 可见度阈值 */
  public visibilityThreshold: number;
  
  /** 世界坐标系中的关键点数据 */
  public worldLandmarks: WorldLandmarkData[] = [];
  
  /** 屏幕坐标系中的关键点数据 */
  public landmarks: LandmarkData[] = [];
  
  /** 上一帧的世界坐标系关键点数据 */
  public prevWorldLandmarks: WorldLandmarkData[] = [];
  
  /** 上一帧的屏幕坐标系关键点数据 */
  public prevLandmarks: LandmarkData[] = [];
  
  /** 骨骼旋转数据 */
  public boneRotations: Map<VRMHumanBoneName, Quaternion> = new Map();
  
  /** 平滑后的骨骼旋转数据 */
  public smoothedBoneRotations: Map<VRMHumanBoneName, Quaternion> = new Map();
  
  /** 髋关节位置 */
  public hipPosition: Vector3 = new Vector3();
  
  /** 是否正在解算下半身 */
  public solvingLowerBody: boolean = false;
  
  /** 脚部偏移 */
  public footOffset: number = 0;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 组件配置
   */
  constructor(entity: Entity, config: MotionCaptureComponentConfig = {}) {
    super(MotionCaptureComponent.TYPE);

    // 设置实体引用
    this.setEntity(entity);

    // 设置启用状态
    this.setEnabled(config.enabled !== undefined ? config.enabled : true);
    this.smoothingFactor = config.smoothingFactor || 0.5;
    this.visibilityThreshold = config.visibilityThreshold || 0.1;

    // 初始化骨骼旋转数据
    this.initBoneRotations();
  }

  /**
   * 初始化骨骼旋转数据
   */
  private initBoneRotations(): void {
    // 初始化所有VRM人形骨骼的旋转数据
    for (const boneName of Object.values(VRMHumanBoneName)) {
      this.boneRotations.set(boneName, new Quaternion());
      this.smoothedBoneRotations.set(boneName, new Quaternion());
    }
  }

  /**
   * 设置世界坐标系关键点数据
   * @param landmarks 关键点数据
   */
  public setWorldLandmarks(landmarks: WorldLandmarkData[]): void {
    // 保存上一帧数据
    this.prevWorldLandmarks = [...this.worldLandmarks];
    
    // 更新当前帧数据
    this.worldLandmarks = landmarks;
  }

  /**
   * 设置屏幕坐标系关键点数据
   * @param landmarks 关键点数据
   */
  public setLandmarks(landmarks: LandmarkData[]): void {
    // 保存上一帧数据
    this.prevLandmarks = [...this.landmarks];
    
    // 更新当前帧数据
    this.landmarks = landmarks;
  }

  /**
   * 设置骨骼旋转
   * @param boneName 骨骼名称
   * @param rotation 旋转四元数
   */
  public setBoneRotation(boneName: VRMHumanBoneName, rotation: Quaternion): void {
    // 获取当前骨骼旋转
    const currentRotation = this.boneRotations.get(boneName);
    if (!currentRotation) return;
    
    // 更新骨骼旋转
    currentRotation.copy(rotation);
    
    // 平滑处理
    this.smoothBoneRotation(boneName);
  }

  /**
   * 平滑骨骼旋转
   * @param boneName 骨骼名称
   */
  private smoothBoneRotation(boneName: VRMHumanBoneName): void {
    // 获取当前骨骼旋转
    const currentRotation = this.boneRotations.get(boneName);
    if (!currentRotation) return;
    
    // 获取平滑后的骨骼旋转
    const smoothedRotation = this.smoothedBoneRotations.get(boneName);
    if (!smoothedRotation) return;
    
    // 球面线性插值
    smoothedRotation.slerp(currentRotation, this.smoothingFactor);
  }

  /**
   * 获取平滑后的骨骼旋转
   * @param boneName 骨骼名称
   * @returns 平滑后的骨骼旋转
   */
  public getSmoothedBoneRotation(boneName: VRMHumanBoneName): Quaternion | undefined {
    return this.smoothedBoneRotations.get(boneName);
  }

  /**
   * 设置髋关节位置
   * @param position 位置向量
   */
  public setHipPosition(position: Vector3): void {
    this.hipPosition.copy(position);
  }

  /**
   * 设置是否正在解算下半身
   * @param solving 是否正在解算
   */
  public setSolvingLowerBody(solving: boolean): void {
    this.solvingLowerBody = solving;
  }

  /**
   * 设置脚部偏移
   * @param offset 偏移值
   */
  public setFootOffset(offset: number): void {
    this.footOffset = offset;
  }

  /**
   * 克隆组件
   * @param entity 目标实体
   * @returns 克隆的组件
   */
  public clone(entity: Entity): MotionCaptureComponent {
    const component = new MotionCaptureComponent(entity, {
      enabled: this.isEnabled(),
      smoothingFactor: this.smoothingFactor,
      visibilityThreshold: this.visibilityThreshold
    });
    
    // 复制数据
    component.worldLandmarks = [...this.worldLandmarks];
    component.landmarks = [...this.landmarks];
    component.prevWorldLandmarks = [...this.prevWorldLandmarks];
    component.prevLandmarks = [...this.prevLandmarks];
    component.hipPosition.copy(this.hipPosition);
    component.solvingLowerBody = this.solvingLowerBody;
    component.footOffset = this.footOffset;
    
    // 复制骨骼旋转
    for (const [boneName, rotation] of this.boneRotations.entries()) {
      component.boneRotations.set(boneName, rotation.clone());
    }
    
    // 复制平滑后的骨骼旋转
    for (const [boneName, rotation] of this.smoothedBoneRotations.entries()) {
      component.smoothedBoneRotations.set(boneName, rotation.clone());
    }
    
    return component;
  }
}
