/**
 * 网络变换组件
 * 用于同步实体的变换（位置、旋转、缩放）
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { Debug } from '../../utils/Debug';

/**
 * 网络变换组件属性
 */
export interface NetworkTransformComponentProps {
  /** 同步间隔（毫秒） */
  syncInterval?: number;

  /** 是否自动同步 */
  autoSync?: boolean;

  /** 是否同步位置 */
  syncPosition?: boolean;

  /** 是否同步旋转 */
  syncRotation?: boolean;

  /** 是否同步缩放 */
  syncScale?: boolean;

  /** 位置插值速度 */
  positionLerpSpeed?: number;

  /** 旋转插值速度 */
  rotationLerpSpeed?: number;

  /** 缩放插值速度 */
  scaleLerpSpeed?: number;

  /** 位置同步阈值 */
  positionThreshold?: number;

  /** 旋转同步阈值（弧度） */
  rotationThreshold?: number;

  /** 缩放同步阈值 */
  scaleThreshold?: number;

  /** 是否使用压缩 */
  useCompression?: boolean;

  /** 是否使用预测 */
  usePrediction?: boolean;

  /** 是否使用平滑 */
  useSmoothing?: boolean;

  /** 是否使用外推 */
  useExtrapolation?: boolean;

  /** 外推时间（秒） */
  extrapolationTime?: number;

  /** 最大外推距离 */
  maxExtrapolationDistance?: number;

  /** 同步优先级 */
  syncPriority?: number;
}

/**
 * 网络变换组件
 * 用于同步实体的变换（位置、旋转、缩放）
 */
export class NetworkTransformComponent extends Component {
  /** 组件类型 */
  public static readonly type = 'NetworkTransform';

  /** 同步间隔（毫秒） */
  public syncInterval: number;

  /** 是否自动同步 */
  public autoSync: boolean;

  /** 是否同步位置 */
  public syncPosition: boolean;

  /** 是否同步旋转 */
  public syncRotation: boolean;

  /** 是否同步缩放 */
  public syncScale: boolean;

  /** 位置插值速度 */
  public positionLerpSpeed: number;

  /** 旋转插值速度 */
  public rotationLerpSpeed: number;

  /** 缩放插值速度 */
  public scaleLerpSpeed: number;

  /** 位置同步阈值 */
  public positionThreshold: number;

  /** 旋转同步阈值（弧度） */
  public rotationThreshold: number;

  /** 缩放同步阈值 */
  public scaleThreshold: number;

  /** 是否使用压缩 */
  public useCompression: boolean;

  /** 是否使用预测 */
  public usePrediction: boolean;

  /** 是否使用平滑 */
  public useSmoothing: boolean;

  /** 是否使用外推 */
  public useExtrapolation: boolean;

  /** 外推时间（秒） */
  public extrapolationTime: number;

  /** 最大外推距离 */
  public maxExtrapolationDistance: number;

  /** 同步优先级 */
  public syncPriority: number;

  /** 上次同步时间 */
  private lastSyncTime: number = 0;

  /** 上次同步位置 */
  private lastSyncedPosition: THREE.Vector3 = new THREE.Vector3();

  /** 上次同步旋转 */
  private lastSyncedRotation: THREE.Quaternion = new THREE.Quaternion();

  /** 上次同步缩放 */
  private lastSyncedScale: THREE.Vector3 = new THREE.Vector3(1, 1, 1);

  /** 目标位置 */
  private targetPosition: THREE.Vector3 = new THREE.Vector3();

  /** 目标旋转 */
  private targetRotation: THREE.Quaternion = new THREE.Quaternion();

  /** 目标缩放 */
  private targetScale: THREE.Vector3 = new THREE.Vector3(1, 1, 1);

  /** 是否正在插值 */
  private isLerping: boolean = false;

  /** 插值开始时间 */
  private lerpStartTime: number = 0;

  /** 插值持续时间 */
  private lerpDuration: number = 0;

  /** 是否有待同步的更改 */
  private hasPendingChanges: boolean = false;

  /** 网络实体组件 */
  private networkEntity: any;

  /** 变换组件 */
  private transform: any;

  /**
   * 创建网络变换组件
   * @param props 组件属性
   */
  constructor(props: NetworkTransformComponentProps = {}) {
    super(NetworkTransformComponent.type);

    this.syncInterval = props.syncInterval || 100;
    this.autoSync = props.autoSync !== undefined ? props.autoSync : true;
    this.syncPosition = props.syncPosition !== undefined ? props.syncPosition : true;
    this.syncRotation = props.syncRotation !== undefined ? props.syncRotation : true;
    this.syncScale = props.syncScale !== undefined ? props.syncScale : true;
    this.positionLerpSpeed = props.positionLerpSpeed || 10;
    this.rotationLerpSpeed = props.rotationLerpSpeed || 10;
    this.scaleLerpSpeed = props.scaleLerpSpeed || 10;
    this.positionThreshold = props.positionThreshold || 0.001;
    this.rotationThreshold = props.rotationThreshold || 0.001;
    this.scaleThreshold = props.scaleThreshold || 0.001;
    this.useCompression = props.useCompression !== undefined ? props.useCompression : true;
    this.usePrediction = props.usePrediction !== undefined ? props.usePrediction : true;
    this.useSmoothing = props.useSmoothing !== undefined ? props.useSmoothing : true;
    this.useExtrapolation = props.useExtrapolation !== undefined ? props.useExtrapolation : false;
    this.extrapolationTime = props.extrapolationTime || 0.1;
    this.maxExtrapolationDistance = props.maxExtrapolationDistance || 5;
    this.syncPriority = props.syncPriority || 0;
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    // 获取网络实体组件
    this.networkEntity = this.entity?.getComponent('NetworkEntity') as any as any;

    if (!this.networkEntity) {
      Debug.warn('NetworkTransformComponent', 'Entity does not have a NetworkEntity component');
      return;
    }

    // 获取变换组件
    this.transform = this.entity?.getComponent('Transform') as any as any;

    if (!this.transform) {
      Debug.warn('NetworkTransformComponent', 'Entity does not have a Transform component');
      return;
    }

    // 保存初始变换
    this.lastSyncedPosition.copy(this.transform.getPosition());
    this.lastSyncedRotation.copy(this.transform.getRotationQuaternion());
    this.lastSyncedScale.copy(this.transform.getScale());

    // 设置目标变换
    this.targetPosition.copy(this.transform.getPosition());
    this.targetRotation.copy(this.transform.getRotationQuaternion());
    this.targetScale.copy(this.transform.getScale());

    // 监听变换变化事件
    this.entity?.on('transformChanged', this.onTransformChanged.bind(this));

    // 监听网络同步事件
    this.entity?.on('networkSync', this.onNetworkSync.bind(this));
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 如果没有网络实体组件或变换组件，则不更新
    if (!this.networkEntity || !this.transform) {
      return;
    }

    // 如果是本地拥有的实体，则检查是否需要同步
    if (this.networkEntity.isLocallyOwned) {
      // 如果启用了自动同步，则检查是否需要同步
      if (this.autoSync && this.hasPendingChanges) {
        const now = Date.now();

        // 检查是否达到同步间隔
        if (now - this.lastSyncTime >= this.syncInterval) {
          this.sync();
        }
      }
    }
    // 如果不是本地拥有的实体，则进行插值
    else if (this.isLerping) {
      this.interpolate(deltaTime);
    }
  }

  /**
   * 同步变换
   */
  public sync(): void {
    if (!this.networkEntity || !this.transform || !this.hasPendingChanges) {
      return;
    }

    // 获取当前变换
    const position = this.transform.getPosition();
    const rotation = this.transform.getRotationQuaternion();
    const scale = this.transform.getScale();

    // 检查是否有足够的变化需要同步
    let needSync = false;

    if (this.syncPosition && position.distanceToSquared(this.lastSyncedPosition) > this.positionThreshold * this.positionThreshold) {
      needSync = true;
    }

    if (this.syncRotation && 1 - rotation.dot(this.lastSyncedRotation) > this.rotationThreshold) {
      needSync = true;
    }

    if (this.syncScale && scale.distanceToSquared(this.lastSyncedScale) > this.scaleThreshold * this.scaleThreshold) {
      needSync = true;
    }

    if (!needSync) {
      return;
    }

    // 构建同步数据
    const syncData: any = {};

    if (this.syncPosition) {
      syncData.position = {
        x: position.x,
        y: position.y,
        z: position.z,
      };

      // 更新上次同步位置
      this.lastSyncedPosition.copy(position);
    }

    if (this.syncRotation) {
      syncData.rotation = {
        x: rotation.x,
        y: rotation.y,
        z: rotation.z,
        w: rotation.w,
      };

      // 更新上次同步旋转
      this.lastSyncedRotation.copy(rotation);
    }

    if (this.syncScale) {
      syncData.scale = {
        x: scale.x,
        y: scale.y,
        z: scale.z,
      };

      // 更新上次同步缩放
      this.lastSyncedScale.copy(scale);
    }

    // 触发网络同步事件
    this.entity?.emit('networkSync', {
      entityId: this.networkEntity.entityId,
      ownerId: this.networkEntity.ownerId,
      transform: syncData,
    });

    // 更新同步时间
    this.lastSyncTime = Date.now();

    // 清除待同步标记
    this.hasPendingChanges = false;
  }

  /**
   * 应用同步数据
   * @param data 同步数据
   */
  public applySyncData(data: any): void {
    if (!this.transform || this.networkEntity.isLocallyOwned) {
      return;
    }

    // 如果没有变换数据，则不应用
    if (!data.transform) {
      return;
    }

    const transform = data.transform;

    // 设置目标变换
    if (transform.position && this.syncPosition) {
      this.targetPosition.set(
        transform.position.x,
        transform.position.y,
        transform.position.z
      );
    }

    if (transform.rotation && this.syncRotation) {
      this.targetRotation.set(
        transform.rotation.x,
        transform.rotation.y,
        transform.rotation.z,
        transform.rotation.w
      );
    }

    if (transform.scale && this.syncScale) {
      this.targetScale.set(
        transform.scale.x,
        transform.scale.y,
        transform.scale.z
      );
    }

    // 如果使用平滑，则开始插值
    if (this.useSmoothing) {
      this.startLerping();
    }
    // 否则直接设置变换
    else {
      if (transform.position && this.syncPosition) {
        this.transform.setPosition(this.targetPosition);
      }

      if (transform.rotation && this.syncRotation) {
        this.transform.setRotationQuaternion(this.targetRotation);
      }

      if (transform.scale && this.syncScale) {
        this.transform.setScale(this.targetScale);
      }
    }
  }

  /**
   * 开始插值
   */
  private startLerping(): void {
    this.isLerping = true;
    this.lerpStartTime = Date.now();
    this.lerpDuration = this.syncInterval;
  }

  /**
   * 插值更新
   * @param deltaTime 帧间隔时间（秒）
   */
  private interpolate(deltaTime: number): void {
    if (!this.isLerping || !this.transform) {
      return;
    }

    // 计算插值因子
    const elapsed = (Date.now() - this.lerpStartTime) / this.lerpDuration;
    const t = Math.min(1, elapsed);

    // 插值位置
    if (this.syncPosition) {
      const currentPosition = this.transform.getPosition();
      const lerpedPosition = currentPosition.lerp(this.targetPosition, t * this.positionLerpSpeed * deltaTime);
      this.transform.setPosition(lerpedPosition);
    }

    // 插值旋转
    if (this.syncRotation) {
      const currentRotation = this.transform.getRotationQuaternion();
      const lerpedRotation = currentRotation.slerp(this.targetRotation, t * this.rotationLerpSpeed * deltaTime);
      this.transform.setRotationQuaternion(lerpedRotation);
    }

    // 插值缩放
    if (this.syncScale) {
      const currentScale = this.transform.getScale();
      const lerpedScale = currentScale.lerp(this.targetScale, t * this.scaleLerpSpeed * deltaTime);
      this.transform.setScale(lerpedScale);
    }

    // 如果插值完成，则停止插值
    if (t >= 1) {
      this.isLerping = false;
    }
  }

  /**
   * 变换变化事件处理
   */
  private onTransformChanged(): void {
    if (!this.networkEntity || !this.networkEntity.isLocallyOwned) {
      return;
    }

    this.hasPendingChanges = true;
  }

  /**
   * 网络同步事件处理
   * @param data 同步数据
   */
  private onNetworkSync(data: any): void {
    this.applySyncData(data);
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 移除事件监听器
    this.entity?.off('transformChanged', this.onTransformChanged);
    this.entity?.off('networkSync', this.onNetworkSync);
  }
}
