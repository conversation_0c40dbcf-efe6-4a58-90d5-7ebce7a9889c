/**
 * 地形纹理压缩
 * 提供地形纹理压缩功能，支持多种压缩格式
 */
import * as THREE from 'three';
/**
 * 纹理压缩格式
 */
export declare enum TextureCompressionFormat {
    /** 不压缩 */
    NONE = "none",
    /** DXT1 (BC1) - RGB压缩，无alpha */
    DXT1 = "dxt1",
    /** DXT5 (BC3) - RGBA压缩 */
    DXT5 = "dxt5",
    /** ETC1 - RGB压缩，无alpha，适用于Android */
    ETC1 = "etc1",
    /** ETC2 - RGBA压缩，适用于Android */
    ETC2 = "etc2",
    /** ASTC - 高级压缩，支持多种块大小 */
    ASTC = "astc",
    /** PVRTC - iOS设备压缩格式 */
    PVRTC = "pvrtc",
    /** BASIS - 通用超级压缩格式 */
    BASIS = "basis",
    /** KTX2 - 基于Khronos的容器格式 */
    KTX2 = "ktx2"
}
/**
 * 纹理压缩选项
 */
export interface TextureCompressionOptions {
    /** 压缩格式 */
    format?: TextureCompressionFormat;
    /** 压缩质量 (0-1) */
    quality?: number;
    /** 是否生成mipmap */
    generateMipmaps?: boolean;
    /** 是否使用GPU加速 */
    useGPUCompression?: boolean;
    /** ASTC块大小 (仅ASTC格式) */
    astcBlockSize?: '4x4' | '5x5' | '6x6' | '8x8' | '10x10' | '12x12';
    /** 是否使用sRGB颜色空间 */
    sRGB?: boolean;
    /** 最大纹理尺寸 */
    maxTextureSize?: number;
    /** 是否保留原始纹理 */
    keepOriginal?: boolean;
}
/**
 * 地形纹理压缩类
 */
export declare class TerrainTextureCompression {
    /** 默认压缩选项 */
    private static readonly DEFAULT_OPTIONS;
    /** 压缩选项 */
    private options;
    /** KTX2编码器 */
    private ktx2Encoder;
    /** 是否支持压缩纹理 */
    private supportsCompressedTextures;
    /** 支持的压缩格式 */
    private supportedFormats;
    /**
     * 创建地形纹理压缩
     * @param options 压缩选项
     */
    constructor(options?: TextureCompressionOptions);
    /**
     * 检查是否支持压缩纹理
     * @returns 是否支持
     */
    private checkCompressedTextureSupport;
    /**
     * 获取支持的压缩格式（私有方法）
     * @returns 支持的格式列表
     */
    private getSupportedFormatsInternal;
    /**
     * 初始化KTX2编码器
     */
    private initializeKTX2Encoder;
    /**
     * 压缩纹理
     * @param texture 原始纹理
     * @param options 压缩选项
     * @returns 压缩后的纹理
     */
    compressTexture(texture: THREE.Texture, options?: TextureCompressionOptions): Promise<THREE.CompressedTexture | THREE.Texture>;
    /**
     * 从纹理获取图像数据
     * @param texture 纹理
     * @returns 图像数据
     */
    private getImageDataFromTexture;
    /**
     * 如果需要，调整图像大小
     * @param imageData 图像数据
     * @param maxSize 最大尺寸
     * @returns 调整后的图像数据
     */
    private resizeImageIfNeeded;
    /**
     * 压缩为KTX2格式
     * @param imageData 图像数据
     * @param options 压缩选项
     * @returns 压缩后的纹理
     */
    private compressToKTX2;
    /**
     * 压缩为Basis格式
     * @param imageData 图像数据
     * @param options 压缩选项
     * @returns 压缩后的纹理
     */
    private compressToBasis;
    /**
     * 压缩为S3TC格式(DXT1/DXT5)
     * @param imageData 图像数据
     * @param _options 压缩选项（暂未使用）
     * @returns 压缩后的纹理
     */
    private compressToS3TC;
    /**
     * 压缩为ETC格式(ETC1/ETC2)
     * @param imageData 图像数据
     * @param _options 压缩选项（暂未使用）
     * @returns 压缩后的纹理
     */
    private compressToETC;
    /**
     * 压缩为ASTC格式
     * @param imageData 图像数据
     * @param _options 压缩选项（暂未使用）
     * @returns 压缩后的纹理
     */
    private compressToASTC;
    /**
     * 压缩为PVRTC格式
     * @param imageData 图像数据
     * @param _options 压缩选项（暂未使用）
     * @returns 压缩后的纹理
     */
    private compressToPVRTC;
    /**
     * 创建未压缩纹理
     * @param imageData 图像数据
     * @returns 未压缩纹理
     */
    private createUncompressedTexture;
    /**
     * 获取支持的压缩格式
     * @returns 支持的格式列表
     */
    getSupportedFormats(): TextureCompressionFormat[];
    /**
     * 是否支持压缩纹理
     * @returns 是否支持
     */
    isCompressionSupported(): boolean;
    /**
     * 设置压缩选项
     * @param options 压缩选项
     */
    setOptions(options: TextureCompressionOptions): void;
}
