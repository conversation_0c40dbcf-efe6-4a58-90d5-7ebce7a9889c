/**
 * 鼠标输入设备
 */
import { BaseInputDevice } from '../InputDevice';
import { MouseButton } from '../InputSystem';
/**
 * 鼠标输入设备
 */
export declare class MouseDevice extends BaseInputDevice {
    /** 目标元素 */
    private element;
    /** 是否阻止默认行为 */
    private preventDefault;
    /** 是否阻止事件传播 */
    private stopPropagation;
    /** 鼠标位置 */
    private position;
    /** 鼠标相对位置（相对于上一帧） */
    private movement;
    /** 鼠标滚轮增量 */
    private wheelDelta;
    /** 鼠标事件处理器 */
    private mouseEventHandlers;
    /**
     * 创建鼠标输入设备
     * @param element 目标元素
     * @param preventDefault 是否阻止默认行为
     * @param stopPropagation 是否阻止事件传播
     */
    constructor(element?: HTMLElement, preventDefault?: boolean, stopPropagation?: boolean);
    /**
     * 初始化事件处理器
     */
    private initEventHandlers;
    /**
     * 初始化设备
     */
    initialize(): void;
    /**
     * 销毁设备
     */
    destroy(): void;
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加事件监听器
     */
    private addEventListeners;
    /**
     * 移除事件监听器
     */
    private removeEventListeners;
    /**
     * 更新鼠标位置
     * @param event 鼠标事件
     */
    private updateMousePosition;
    /**
     * 处理鼠标按钮按下事件
     * @param event 鼠标事件
     */
    private handleMouseDown;
    /**
     * 处理鼠标按钮释放事件
     * @param event 鼠标事件
     */
    private handleMouseUp;
    /**
     * 处理鼠标移动事件
     * @param event 鼠标事件
     */
    private handleMouseMove;
    /**
     * 处理鼠标滚轮事件
     * @param event 滚轮事件
     */
    private handleMouseWheel;
    /**
     * 获取鼠标位置
     * @returns 鼠标位置
     */
    getPosition(): {
        x: number;
        y: number;
    };
    /**
     * 获取鼠标移动
     * @returns 鼠标移动
     */
    getMovement(): {
        x: number;
        y: number;
    };
    /**
     * 获取滚轮增量
     * @returns 滚轮增量
     */
    getWheelDelta(): number;
    /**
     * 检查鼠标按钮是否按下
     * @param button 鼠标按钮
     * @returns 是否按下
     */
    isButtonDown(button: MouseButton): boolean;
    /**
     * 检查鼠标按钮是否刚按下
     * @param button 鼠标按钮
     * @returns 是否刚按下
     */
    isButtonJustDown(button: MouseButton): boolean;
    /**
     * 检查鼠标按钮是否刚释放
     * @param button 鼠标按钮
     * @returns 是否刚释放
     */
    isButtonJustUp(button: MouseButton): boolean;
}
