/**
 * 负载均衡接口和类型定义
 */
import { ServiceInstanceEntity } from '../entities/service-instance.entity';

/**
 * 负载均衡算法类型
 */
export enum LoadBalancerAlgorithm {
  /** 随机 */
  RANDOM = 'random',
  /** 轮询 */
  ROUND_ROBIN = 'round-robin',
  /** 加权轮询 */
  WEIGHTED_ROUND_ROBIN = 'weighted-round-robin',
  /** 最少连接 */
  LEAST_CONNECTIONS = 'least-connections',
  /** 最少响应时间 */
  LEAST_RESPONSE_TIME = 'least-response-time',
  /** 一致性哈希 */
  CONSISTENT_HASH = 'consistent-hash',
  /** 区域感知 */
  ZONE_AWARE = 'zone-aware',
}

/**
 * 负载均衡配置
 */
export interface LoadBalancerConfig {
  /** 负载均衡算法 */
  algorithm: LoadBalancerAlgorithm;
  /** 是否启用粘性会话 */
  enableStickySession?: boolean;
  /** 粘性会话超时（毫秒） */
  stickySessionTimeout?: number;
  /** 是否启用故障转移 */
  enableFailover?: boolean;
  /** 故障转移重试次数 */
  failoverRetries?: number;
  /** 是否启用区域亲和性 */
  enableZoneAffinity?: boolean;
  /** 区域亲和性权重 */
  zoneAffinityWeight?: number;
  /** 额外配置 */
  options?: Record<string, any>;
}

/**
 * 负载均衡上下文
 */
export interface LoadBalancerContext {
  /** 服务名称 */
  serviceName: string;
  /** 客户端IP */
  clientIp?: string;
  /** 客户端区域 */
  clientZone?: string;
  /** 请求路径 */
  path?: string;
  /** 请求方法 */
  method?: string;
  /** 请求头 */
  headers?: Record<string, string>;
  /** 会话ID */
  sessionId?: string;
  /** 额外上下文 */
  extra?: Record<string, any>;
}

/**
 * 负载均衡策略接口
 */
export interface LoadBalancerStrategy {
  /**
   * 选择服务实例
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  select(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): Promise<ServiceInstanceEntity | null>;
  
  /**
   * 初始化策略
   * @param config 负载均衡配置
   */
  init(config: LoadBalancerConfig): void;
  
  /**
   * 更新策略配置
   * @param config 负载均衡配置
   */
  updateConfig(config: LoadBalancerConfig): void;
  
  /**
   * 获取策略名称
   */
  getName(): string;
  
  /**
   * 获取策略配置
   */
  getConfig(): LoadBalancerConfig;
  
  /**
   * 重置策略状态
   */
  reset(): void;
}
