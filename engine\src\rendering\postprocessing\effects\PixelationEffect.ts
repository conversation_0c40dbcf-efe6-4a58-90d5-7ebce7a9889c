/**
 * 像素化效果
 * 模拟低分辨率像素风格
 */
import * as THREE from 'three';
// 使用类型断言导入 ShaderPass
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 像素化效果选项
 */
export interface PixelationEffectOptions extends PostProcessingEffectOptions {
  /** 像素大小 */
  pixelSize?: number;
  /** 是否保持纵横比 */
  preserveAspect?: boolean;
  /** 是否使用像素边缘 */
  pixelBorder?: boolean;
  /** 像素边缘颜色 */
  borderColor?: THREE.Color;
  /** 像素边缘宽度 */
  borderWidth?: number;
}

/**
 * 像素化效果
 */
export class PixelationEffect extends PostProcessingEffect {
  /** 像素大小 */
  private pixelSize: number;

  /** 是否保持纵横比 */
  private preserveAspect: boolean;

  /** 是否使用像素边缘 */
  private pixelBorder: boolean;

  /** 像素边缘颜色 */
  private borderColor: THREE.Color;

  /** 像素边缘宽度 */
  private borderWidth: number;

  /** 像素化通道 */
  private pixelationPass: ShaderPass | null = null;

  /**
   * 创建像素化效果
   * @param options 像素化效果选项
   */
  constructor(options: PixelationEffectOptions = { name: 'Pixelation' }) {
    super(options);

    this.pixelSize = options.pixelSize || 8;
    this.preserveAspect = options.preserveAspect !== undefined ? options.preserveAspect : true;
    this.pixelBorder = options.pixelBorder !== undefined ? options.pixelBorder : false;
    this.borderColor = options.borderColor || new THREE.Color(0x000000);
    this.borderWidth = options.borderWidth !== undefined ? options.borderWidth : 0.2;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 创建像素化着色器
    const pixelationShader = {
      uniforms: {
        'tDiffuse': { value: null },
        'pixelSize': { value: this.pixelSize },
        'resolution': { value: new THREE.Vector2(this.width, this.height) },
        'preserveAspect': { value: this.preserveAspect ? 1 : 0 },
        'pixelBorder': { value: this.pixelBorder ? 1 : 0 },
        'borderColor': { value: this.borderColor },
        'borderWidth': { value: this.borderWidth }
      },
      vertexShader: /* glsl */`
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: /* glsl */`
        uniform sampler2D tDiffuse;
        uniform float pixelSize;
        uniform vec2 resolution;
        uniform int preserveAspect;
        uniform int pixelBorder;
        uniform vec3 borderColor;
        uniform float borderWidth;
        
        varying vec2 vUv;
        
        void main() {
          vec2 pixelatedUV;
          
          if (preserveAspect == 1) {
            // 保持纵横比
            float aspectRatio = resolution.x / resolution.y;
            vec2 dxy = pixelSize / resolution;
            vec2 coord = vUv / dxy;
            vec2 pixelCoord = floor(coord);
            vec2 fraction = fract(coord);
            pixelatedUV = pixelCoord * dxy;
          } else {
            // 不保持纵横比
            vec2 dxy = vec2(pixelSize) / resolution;
            vec2 coord = vUv / dxy;
            vec2 pixelCoord = floor(coord);
            vec2 fraction = fract(coord);
            pixelatedUV = pixelCoord * dxy;
          }
          
          vec4 texel = texture2D(tDiffuse, pixelatedUV);
          
          // 添加像素边缘
          if (pixelBorder == 1) {
            vec2 fraction = fract(vUv * resolution / pixelSize);
            float borderMask = 0.0;
            
            // 计算边缘遮罩
            if (fraction.x < borderWidth || fraction.x > (1.0 - borderWidth) || 
                fraction.y < borderWidth || fraction.y > (1.0 - borderWidth)) {
              borderMask = 1.0;
            }
            
            // 混合边缘颜色
            texel.rgb = mix(texel.rgb, borderColor, borderMask);
          }
          
          gl_FragColor = texel;
        }
      `
    };

    // 创建像素化通道
    this.pixelationPass = new ShaderPass(pixelationShader);

    // 设置通道
    this.pass = this.pixelationPass;
  }

  /**
   * 设置像素大小
   * @param size 像素大小
   */
  public setPixelSize(size: number): void {
    this.pixelSize = size;

    if (this.pixelationPass) {
      const uniforms = this.pixelationPass.uniforms;
      if (uniforms.pixelSize) {
        uniforms.pixelSize.value = size;
      }
    }
  }

  /**
   * 获取像素大小
   * @returns 像素大小
   */
  public getPixelSize(): number {
    return this.pixelSize;
  }

  /**
   * 设置是否保持纵横比
   * @param preserve 是否保持纵横比
   */
  public setPreserveAspect(preserve: boolean): void {
    this.preserveAspect = preserve;

    if (this.pixelationPass) {
      const uniforms = this.pixelationPass.uniforms;
      if (uniforms.preserveAspect) {
        uniforms.preserveAspect.value = preserve ? 1 : 0;
      }
    }
  }

  /**
   * 获取是否保持纵横比
   * @returns 是否保持纵横比
   */
  public isPreserveAspect(): boolean {
    return this.preserveAspect;
  }

  /**
   * 设置是否使用像素边缘
   * @param border 是否使用像素边缘
   */
  public setPixelBorder(border: boolean): void {
    this.pixelBorder = border;

    if (this.pixelationPass) {
      const uniforms = this.pixelationPass.uniforms;
      if (uniforms.pixelBorder) {
        uniforms.pixelBorder.value = border ? 1 : 0;
      }
    }
  }

  /**
   * 获取是否使用像素边缘
   * @returns 是否使用像素边缘
   */
  public isPixelBorder(): boolean {
    return this.pixelBorder;
  }

  /**
   * 设置像素边缘颜色
   * @param color 像素边缘颜色
   */
  public setBorderColor(color: THREE.Color): void {
    this.borderColor.copy(color);

    if (this.pixelationPass) {
      const uniforms = this.pixelationPass.uniforms;
      if (uniforms.borderColor) {
        uniforms.borderColor.value = this.borderColor;
      }
    }
  }

  /**
   * 获取像素边缘颜色
   * @returns 像素边缘颜色
   */
  public getBorderColor(): THREE.Color {
    return this.borderColor.clone();
  }

  /**
   * 设置像素边缘宽度
   * @param width 像素边缘宽度
   */
  public setBorderWidth(width: number): void {
    this.borderWidth = width;

    if (this.pixelationPass) {
      const uniforms = this.pixelationPass.uniforms;
      if (uniforms.borderWidth) {
        uniforms.borderWidth.value = width;
      }
    }
  }

  /**
   * 获取像素边缘宽度
   * @returns 像素边缘宽度
   */
  public getBorderWidth(): number {
    return this.borderWidth;
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    super.resize(width, height);

    if (this.pixelationPass) {
      const uniforms = this.pixelationPass.uniforms;
      if (uniforms.resolution) {
        uniforms.resolution.value.set(width, height);
      }
    }
  }
}
