/**
 * 动画状态机
 * 管理数字人的动画状态转换和播放
 */
import * as THREE from 'three';
import { EventEmitter } from '../../utils/EventEmitter';
import { Entity } from '../../core/Entity';
import {
  AnimationState,
  StateTransition,
  TransitionCondition,
  ComparisonType,
  BlendMode,
  AnimationClip
} from './MultiActionFusionTypes';

/**
 * 状态机配置
 */
export interface StateMachineConfig {
  /** 默认状态 */
  defaultState?: string;
  /** 是否启用调试 */
  debug?: boolean;
  /** 最大转换时间 */
  maxTransitionTime?: number;
}

/**
 * 转换选项
 */
export interface TransitionOptions {
  /** 淡入时间 */
  fadeTime?: number;
  /** 是否循环 */
  loop?: boolean;
  /** 播放速度 */
  speed?: number;
}

/**
 * 状态机参数
 */
export interface StateMachineParameter {
  /** 参数名称 */
  name: string;
  /** 参数值 */
  value: number;
  /** 参数类型 */
  type: 'float' | 'int' | 'bool' | 'trigger';
}

/**
 * 动画状态机
 */
export class AnimationStateMachine extends EventEmitter {
  /** 关联的实体 */
  private entity: Entity;

  /** 配置 */
  private config: StateMachineConfig;

  /** 状态映射 */
  private states: Map<string, AnimationState> = new Map();

  /** 转换映射 */
  private transitions: Map<string, StateTransition[]> = new Map();

  /** 当前状态 */
  private currentState: string | null = null;

  /** 目标状态 */
  private targetState: string | null = null;

  /** 转换进度 */
  private transitionProgress: number = 0;

  /** 转换时间 */
  private transitionDuration: number = 0;

  /** 是否正在转换 */
  private isTransitioning: boolean = false;

  /** 状态机参数 */
  private parameters: Map<string, StateMachineParameter> = new Map();

  /** 动画混合器 */
  private mixer: THREE.AnimationMixer;

  /** 动画动作映射 */
  private actions: Map<string, THREE.AnimationAction> = new Map();

  /**
   * 构造函数
   * @param entity 关联实体
   * @param config 配置
   */
  constructor(entity: Entity, config: StateMachineConfig = {}) {
    super();

    this.entity = entity;
    this.config = {
      debug: false,
      maxTransitionTime: 2.0,
      ...config
    };

    // 创建动画混合器
    this.mixer = new THREE.AnimationMixer(new THREE.Object3D());
  }

  /**
   * 设置状态列表
   * @param states 状态列表
   */
  public setStates(states: AnimationState[]): void {
    this.states.clear();
    this.actions.clear();

    for (const state of states) {
      this.states.set(state.name, state);

      // 创建Three.js动画动作
      const threeClip = this.convertToThreeClip(state.animation);
      const action = this.mixer.clipAction(threeClip);
      
      // 设置动作属性
      action.loop = state.isLooping ? THREE.LoopRepeat : THREE.LoopOnce;
      action.weight = state.weight;
      action.timeScale = 1.0;

      this.actions.set(state.name, action);
    }

    // 设置默认状态
    if (this.config.defaultState && this.states.has(this.config.defaultState)) {
      this.currentState = this.config.defaultState;
    } else if (states.length > 0) {
      this.currentState = states[0].name;
    }

    if (this.config.debug) {
      console.log(`[AnimationStateMachine] 设置 ${states.length} 个状态`);
    }
  }

  /**
   * 设置转换列表
   * @param transitions 转换列表
   */
  public setTransitions(transitions: StateTransition[]): void {
    this.transitions.clear();

    for (const transition of transitions) {
      if (!this.transitions.has(transition.fromState)) {
        this.transitions.set(transition.fromState, []);
      }
      this.transitions.get(transition.fromState)!.push(transition);
    }

    if (this.config.debug) {
      console.log(`[AnimationStateMachine] 设置 ${transitions.length} 个转换`);
    }
  }

  /**
   * 转换到指定状态
   * @param stateName 状态名称
   * @param options 转换选项
   */
  public async transitionTo(stateName: string, options: TransitionOptions = {}): Promise<void> {
    if (!this.states.has(stateName)) {
      throw new Error(`状态不存在: ${stateName}`);
    }

    if (this.currentState === stateName && !this.isTransitioning) {
      return; // 已经在目标状态
    }

    const fadeTime = options.fadeTime || 0.3;
    
    this.targetState = stateName;
    this.transitionDuration = fadeTime;
    this.transitionProgress = 0;
    this.isTransitioning = true;

    this.emit('transitionStarted', this.currentState, stateName);

    if (this.config.debug) {
      console.log(`[AnimationStateMachine] 开始转换: ${this.currentState} -> ${stateName}`);
    }

    return new Promise((resolve) => {
      const checkTransition = () => {
        if (!this.isTransitioning) {
          resolve();
        } else {
          requestAnimationFrame(checkTransition);
        }
      };
      checkTransition();
    });
  }

  /**
   * 更新状态机
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    // 更新动画混合器
    this.mixer.update(deltaTime);

    // 处理状态转换
    if (this.isTransitioning && this.targetState) {
      this.updateTransition(deltaTime);
    }

    // 检查自动转换
    this.checkAutoTransitions();
  }

  /**
   * 更新转换
   * @param deltaTime 时间增量
   */
  private updateTransition(deltaTime: number): void {
    if (!this.targetState) return;

    this.transitionProgress += deltaTime / this.transitionDuration;

    if (this.transitionProgress >= 1.0) {
      // 转换完成
      this.completeTransition();
    } else {
      // 更新混合权重
      this.updateBlendWeights();
    }
  }

  /**
   * 完成转换
   */
  private completeTransition(): void {
    if (!this.targetState) return;

    // 停止当前动作
    if (this.currentState) {
      const currentAction = this.actions.get(this.currentState);
      if (currentAction) {
        currentAction.fadeOut(0.1);
      }
    }

    // 启动目标动作
    const targetAction = this.actions.get(this.targetState);
    if (targetAction) {
      targetAction.reset();
      targetAction.fadeIn(0.1);
      targetAction.play();
    }

    const previousState = this.currentState;
    this.currentState = this.targetState;
    this.targetState = null;
    this.isTransitioning = false;
    this.transitionProgress = 0;

    this.emit('transitionCompleted', previousState, this.currentState);

    if (this.config.debug) {
      console.log(`[AnimationStateMachine] 转换完成: ${previousState} -> ${this.currentState}`);
    }
  }

  /**
   * 更新混合权重
   */
  private updateBlendWeights(): void {
    if (!this.currentState || !this.targetState) return;

    const currentAction = this.actions.get(this.currentState);
    const targetAction = this.actions.get(this.targetState);

    if (currentAction && targetAction) {
      const currentWeight = 1.0 - this.transitionProgress;
      const targetWeight = this.transitionProgress;

      currentAction.weight = currentWeight;
      targetAction.weight = targetWeight;

      // 确保目标动作正在播放
      if (!targetAction.isRunning()) {
        targetAction.play();
      }
    }
  }

  /**
   * 检查自动转换
   */
  private checkAutoTransitions(): void {
    if (!this.currentState || this.isTransitioning) return;

    const availableTransitions = this.transitions.get(this.currentState);
    if (!availableTransitions) return;

    for (const transition of availableTransitions) {
      if (this.evaluateTransitionConditions(transition.conditions)) {
        this.transitionTo(transition.toState, { fadeTime: transition.duration });
        break;
      }
    }
  }

  /**
   * 评估转换条件
   * @param conditions 条件列表
   * @returns 是否满足条件
   */
  private evaluateTransitionConditions(conditions: TransitionCondition[]): boolean {
    for (const condition of conditions) {
      const parameter = this.parameters.get(condition.parameter);
      if (!parameter) continue;

      const satisfied = this.evaluateCondition(parameter.value, condition.comparison, condition.threshold);
      if (!satisfied) {
        return false;
      }
    }

    return true;
  }

  /**
   * 评估单个条件
   * @param value 参数值
   * @param comparison 比较类型
   * @param threshold 阈值
   * @returns 是否满足条件
   */
  private evaluateCondition(value: number, comparison: ComparisonType, threshold: number): boolean {
    switch (comparison) {
      case ComparisonType.GREATER:
        return value > threshold;
      case ComparisonType.LESS:
        return value < threshold;
      case ComparisonType.EQUAL:
        return Math.abs(value - threshold) < 0.001;
      case ComparisonType.NOT_EQUAL:
        return Math.abs(value - threshold) >= 0.001;
      default:
        return false;
    }
  }

  /**
   * 设置参数值
   * @param name 参数名称
   * @param value 参数值
   */
  public setParameter(name: string, value: number): void {
    const parameter = this.parameters.get(name);
    if (parameter) {
      parameter.value = value;
    } else {
      this.parameters.set(name, {
        name,
        value,
        type: 'float'
      });
    }
  }

  /**
   * 获取参数值
   * @param name 参数名称
   * @returns 参数值
   */
  public getParameter(name: string): number | undefined {
    const parameter = this.parameters.get(name);
    return parameter?.value;
  }

  /**
   * 获取当前状态
   * @returns 当前状态名称
   */
  public getCurrentState(): string | null {
    return this.currentState;
  }

  /**
   * 是否正在转换
   * @returns 转换状态
   */
  public getIsTransitioning(): boolean {
    return this.isTransitioning;
  }

  /**
   * 转换动画片段为Three.js格式
   * @param clip 动画片段
   * @returns Three.js动画片段
   */
  private convertToThreeClip(clip: AnimationClip): THREE.AnimationClip {
    const tracks: THREE.KeyframeTrack[] = [];

    for (const track of clip.tracks) {
      // TODO: 转换轨道数据
      // 这里需要根据实际的轨道数据格式进行转换
    }

    return new THREE.AnimationClip(clip.name, clip.duration, tracks);
  }

  /**
   * 销毁状态机
   */
  public dispose(): void {
    // 停止所有动作
    for (const action of Array.from(this.actions.values())) {
      action.stop();
    }

    this.actions.clear();
    this.states.clear();
    this.transitions.clear();
    this.parameters.clear();

    this.mixer.stopAllAction();
    this.removeAllListeners();

    if (this.config.debug) {
      console.log('[AnimationStateMachine] 状态机已销毁');
    }
  }
}
