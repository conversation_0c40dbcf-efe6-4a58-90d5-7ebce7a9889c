/**
 * Avatar动画系统
 * 用于管理角色动画，包括动画混合、状态机和重定向
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { AnimationStateMachine } from '../../animation/AnimationStateMachine';
import { AnimationRetargeting } from '../../animation/AnimationRetargeting';
import { AnimationClip } from '../../animation/AnimationClip';
import { AvatarRigComponent } from '../components/AvatarRigComponent';
import { AvatarAnimationComponent } from '../components/AvatarAnimationComponent';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * Avatar动画系统配置
 */
export interface AvatarAnimationSystemConfig {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 是否自动重定向动画 */
  autoRetarget?: boolean;
  /** 是否使用状态机 */
  useStateMachine?: boolean;
  /** 是否使用混合空间 */
  useBlendSpace?: boolean;
}

/**
 * Avatar动画系统
 */
export class AvatarAnimationSystem extends System {
  /** 配置 */
  private config: AvatarAnimationSystemConfig;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** Avatar实体列表 */
  private avatarEntities: Map<Entity, {
    animationComponent: AvatarAnimationComponent,
    rigComponent: AvatarRigComponent,
    stateMachine?: AnimationStateMachine,
    lastUpdateTime: number,
    updateFrequency: number,
    needsUpdate: boolean
  }> = new Map();
  /** 默认动画映射 */
  private defaultAnimations: Map<string, THREE.AnimationClip> = new Map();
  /** 骨骼映射 */
  private boneMappings: Map<string, Map<string, string>> = new Map();
  /** 重定向缓存 */
  private retargetCache: Map<string, THREE.AnimationClip> = new Map();
  /** 上次更新时间 */
  private lastSystemUpdateTime: number = 0;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(config: AvatarAnimationSystemConfig = {}) {
    super(300); // 设置优先级

    this.config = {
      debug: config.debug || false,
      autoRetarget: config.autoRetarget !== undefined ? config.autoRetarget : true,
      useStateMachine: config.useStateMachine !== undefined ? config.useStateMachine : true,
      useBlendSpace: config.useBlendSpace !== undefined ? config.useBlendSpace : true
    };
  }

  /**
   * 注册Avatar实体
   * @param entity 实体
   */
  public registerAvatar(entity: Entity): void {
    // 获取组件
    const animationComponent = entity.getComponent<AvatarAnimationComponent>(AvatarAnimationComponent.type);
    const rigComponent = entity.getComponent<AvatarRigComponent>(AvatarRigComponent.type);

    if (!animationComponent || !rigComponent) {
      if (this.config.debug) {
        console.warn('无法注册Avatar实体，缺少必要组件', entity);
      }
      return;
    }

    // 如果已经注册，则先注销
    if (this.avatarEntities.has(entity)) {
      this.unregisterAvatar(entity);
    }

    // 创建状态机
    let stateMachine: AnimationStateMachine | undefined = undefined;
    if (this.config.useStateMachine && animationComponent.animator) {
      stateMachine = new AnimationStateMachine(animationComponent.animator);
      this.setupDefaultStateMachine(stateMachine, animationComponent);
    }

    // 注册实体
    this.avatarEntities.set(entity, {
      animationComponent,
      rigComponent,
      stateMachine,
      lastUpdateTime: 0,
      updateFrequency: 1/60, // 默认每帧更新
      needsUpdate: true
    });

    // 如果需要自动重定向动画
    if (this.config.autoRetarget) {
      this.retargetAnimations(entity);
    }

    if (this.config.debug) {
      console.log('注册Avatar实体', entity);
    }
  }

  /**
   * 注销Avatar实体
   * @param entity 实体
   */
  public unregisterAvatar(entity: Entity): void {
    if (!this.avatarEntities.has(entity)) return;

    this.avatarEntities.delete(entity);

    if (this.config.debug) {
      console.log('注销Avatar实体', entity);
    }
  }

  /**
   * 设置默认状态机
   * @param stateMachine 状态机
   * @param animationComponent 动画组件
   */
  private setupDefaultStateMachine(stateMachine: AnimationStateMachine, animationComponent: AvatarAnimationComponent): void {
    // 获取所有动画片段
    const clips = animationComponent.animator.getClips();
    if (!clips.length) return;

    // 创建默认状态
    let idleClip = clips.find(clip => clip.name.toLowerCase().includes('idle'));
    if (!idleClip) idleClip = clips[0];

    // 添加空闲状态
    stateMachine.addState({
      name: 'Idle',
      type: 'SingleAnimationState',
      clipName: idleClip.name,
      loop: true,
      clamp: false
    });

    // 查找行走和跑步动画
    const walkClip = clips.find(clip => clip.name.toLowerCase().includes('walk'));
    const runClip = clips.find(clip => clip.name.toLowerCase().includes('run'));

    // 如果有行走和跑步动画，创建运动状态
    if (walkClip && runClip && this.config.useBlendSpace) {
      // 创建1D混合空间
      const locomotionState: any = {
        name: 'Locomotion',
        type: 'BlendAnimationState',
        parameterName: 'moveSpeed',
        blendSpaceType: '1D',
        blendSpaceConfig: {
          minValue: 0,
          maxValue: 1
        }
      };

      // 添加运动状态
      stateMachine.addState(locomotionState);

      // 添加从空闲到运动的转换
      stateMachine.addTransition({
        from: 'Idle',
        to: 'Locomotion',
        condition: () => {
          const moveSpeed = stateMachine.getParameter('moveSpeed') || 0;
          return moveSpeed > 0.1;
        },
        duration: 0.25,
        canInterrupt: true
      });

      // 添加从运动到空闲的转换
      stateMachine.addTransition({
        from: 'Locomotion',
        to: 'Idle',
        condition: () => {
          const moveSpeed = stateMachine.getParameter('moveSpeed') || 0;
          return moveSpeed <= 0.1;
        },
        duration: 0.25,
        canInterrupt: true
      });
    }

    // 设置初始状态
    stateMachine.setCurrentState('Idle');
  }

  /**
   * 重定向动画
   * @param entity 实体
   */
  private retargetAnimations(entity: Entity): void {
    const avatarData = this.avatarEntities.get(entity);
    if (!avatarData) return;

    const { animationComponent, rigComponent } = avatarData;
    if (!animationComponent || !rigComponent) return;

    // 获取骨骼映射
    const boneMapping = this.getBoneMapping(entity);
    if (!boneMapping || boneMapping.size === 0) {
      if (this.config.debug) {
        console.warn('无法获取骨骼映射', entity);
      }
      return;
    }

    // 获取动画片段
    const clips = animationComponent.animator.getClips();
    if (!clips.length) return;

    // 获取骨骼
    const skeleton = this.getSkeleton(entity);
    if (!skeleton) {
      if (this.config.debug) {
        console.warn('无法获取骨骼', entity);
      }
      return;
    }

    // 创建重定向配置
    const config = {
      boneMapping: Array.from(boneMapping.entries()).map(([source, target]) => ({
        source,
        target
      })),
      preservePositionTracks: true,
      preserveScaleTracks: false,
      normalizeRotations: true,
      adjustRootHeight: true,
      adjustBoneLength: true
    };

    // 创建缓存键
    const createCacheKey = (clipName: string, entityId: string) => {
      return `${clipName}_${entityId}`;
    };

    // 重定向每个动画片段
    for (const clip of clips) {
      // 检查缓存
      const cacheKey = createCacheKey(clip.name, entity.id);
      let retargetedClip = this.retargetCache.get(cacheKey);

      if (!retargetedClip) {
        // 创建THREE.AnimationClip
        const threeClip = this.convertToThreeClip(clip);
        if (!threeClip) continue;

        // 重定向动画
        retargetedClip = AnimationRetargeting.retargetClip(
          threeClip,
          skeleton,
          skeleton,
          config
        );

        // 缓存结果
        if (retargetedClip) {
          this.retargetCache.set(cacheKey, retargetedClip);
        }
      }

      // 替换原始动画
      if (retargetedClip) {
        // 这里需要根据实际情况替换动画
        // 例如，可以将重定向后的动画添加到动画组件中
        const animator = animationComponent.animator;
        if (animator) {
          // 移除原始动画
          animator.removeClip(clip.name);

          // 添加重定向后的动画
          const newClip = this.convertFromThreeClip(retargetedClip);
          if (newClip) {
            animator.addClip(newClip);
          }
        }
      }
    }

    // 标记实体需要更新
    this.markNeedsUpdate(entity);
  }

  /**
   * 将THREE.AnimationClip转换为AnimationClip
   * @param threeClip THREE动画片段
   * @returns 动画片段
   */
  private convertFromThreeClip(threeClip: THREE.AnimationClip): any {
    // 这里需要根据实际情况转换
    // 创建新的AnimationClip
    const clip = new AnimationClip({
      name: threeClip.name,
      duration: threeClip.duration
    });

    // 转换轨道
    for (const track of threeClip.tracks) {
      const targetPath = track.name;
      let type: 'vector3' | 'quaternion' | 'color' | 'number' | 'boolean' | '' = '';
      let values = [];

      if (track instanceof THREE.VectorKeyframeTrack) {
        type = 'vector3';
        for (let i = 0; i < track.times.length; i++) {
          const time = track.times[i];
          const value = [
            track.values[i * 3],
            track.values[i * 3 + 1],
            track.values[i * 3 + 2]
          ];
          values.push({ time, value });
        }
      } else if (track instanceof THREE.QuaternionKeyframeTrack) {
        type = 'quaternion';
        for (let i = 0; i < track.times.length; i++) {
          const time = track.times[i];
          const value = [
            track.values[i * 4],
            track.values[i * 4 + 1],
            track.values[i * 4 + 2],
            track.values[i * 4 + 3]
          ];
          values.push({ time, value });
        }
      } else if (track instanceof THREE.ColorKeyframeTrack) {
        type = 'color';
        for (let i = 0; i < track.times.length; i++) {
          const time = track.times[i];
          const value = [
            track.values[i * 3],
            track.values[i * 3 + 1],
            track.values[i * 3 + 2]
          ];
          values.push({ time, value });
        }
      } else if (track instanceof THREE.NumberKeyframeTrack) {
        type = 'number';
        for (let i = 0; i < track.times.length; i++) {
          const time = track.times[i];
          const value = track.values[i];
          values.push({ time, value });
        }
      } else if (track instanceof THREE.BooleanKeyframeTrack) {
        type = 'boolean';
        for (let i = 0; i < track.times.length; i++) {
          const time = track.times[i];
          const value = track.values[i];
          values.push({ time, value });
        }
      }

      if (type && values.length > 0) {
        clip.addTrack({
          targetPath,
          type: type as any, // 临时类型转换，需要根据实际TrackType定义调整
          keyframes: values,
          defaultInterpolation: 'linear' as any // 添加必需的属性，临时类型转换
        });
      }
    }

    return clip;
  }

  /**
   * 获取骨骼映射
   * @param entity 实体
   * @returns 骨骼映射
   */
  private getBoneMapping(entity: Entity): Map<string, string> {
    // 检查缓存
    if (this.boneMappings.has(entity.id)) {
      return this.boneMappings.get(entity.id);
    }

    // 从实体获取骨骼映射
    const boneMapping = AnimationRetargeting.getBoneMappingFromEntity(entity);

    // 缓存映射
    this.boneMappings.set(entity.id, boneMapping);

    return boneMapping;
  }

  /**
   * 获取骨骼
   * @param entity 实体
   * @returns 骨骼数组
   */
  private getSkeleton(entity: Entity): THREE.Bone[] {
    // 这里需要根据实际情况获取骨骼
    // 例如，可以从实体的组件中获取骨骼信息
    const avatarData = this.avatarEntities.get(entity);
    if (!avatarData) return null;

    const { rigComponent } = avatarData;
    if (!rigComponent) return null;

    // 假设rigComponent有一个getBones方法
    return rigComponent.getBones();
  }

  /**
   * 将AnimationClip转换为THREE.AnimationClip
   * @param clip 动画片段
   * @returns THREE动画片段
   */
  private convertToThreeClip(clip: any): THREE.AnimationClip {
    // 这里需要根据实际情况转换
    // 如果clip已经是THREE.AnimationClip，则直接返回
    if (clip instanceof THREE.AnimationClip) {
      return clip;
    }

    // 否则，创建新的THREE.AnimationClip
    // 这里假设clip有toThreeAnimationClip方法
    if (typeof clip.toThreeAnimationClip === 'function') {
      return clip.toThreeAnimationClip();
    }

    return null;
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    const currentTime = performance.now() / 1000;
    const systemDeltaTime = currentTime - this.lastSystemUpdateTime;
    this.lastSystemUpdateTime = currentTime;

    // 如果deltaTime为0，则使用系统时间差
    if (deltaTime <= 0) {
      deltaTime = systemDeltaTime;
    }

    // 创建需要更新的实体列表
    const entitiesToUpdate: Array<[Entity, {
      animationComponent: AvatarAnimationComponent,
      rigComponent: AvatarRigComponent,
      stateMachine?: AnimationStateMachine,
      lastUpdateTime: number,
      updateFrequency: number,
      needsUpdate: boolean
    }]> = [];

    // 检查哪些实体需要更新
    for (const [entity, data] of this.avatarEntities) {
      // 计算距离上次更新的时间
      const timeSinceLastUpdate = currentTime - data.lastUpdateTime;

      // 如果时间超过更新频率或者标记为需要更新，则添加到更新列表
      if (timeSinceLastUpdate >= data.updateFrequency || data.needsUpdate) {
        entitiesToUpdate.push([entity, data]);
      }
    }

    // 如果没有需要更新的实体，则直接返回
    if (entitiesToUpdate.length === 0) {
      return;
    }

    // 按照优先级排序（距离上次更新时间越长，优先级越高）
    entitiesToUpdate.sort((a, b) => {
      const timeA = currentTime - a[1].lastUpdateTime;
      const timeB = currentTime - b[1].lastUpdateTime;
      return timeB - timeA;
    });

    // 限制每帧更新的实体数量
    const maxUpdatesPerFrame = 10; // 可以根据性能需求调整
    const updateCount = Math.min(entitiesToUpdate.length, maxUpdatesPerFrame);

    // 更新实体
    for (let i = 0; i < updateCount; i++) {
      const [_, data] = entitiesToUpdate[i];

      // 更新状态机
      if (data.stateMachine) {
        data.stateMachine.update(deltaTime);
      }

      // 更新动画组件
      if (data.animationComponent && data.animationComponent.isEnabled()) {
        data.animationComponent.update(deltaTime);
      }

      // 更新最后更新时间和状态
      data.lastUpdateTime = currentTime;
      data.needsUpdate = false;
    }
  }

  /**
   * 设置实体更新频率
   * @param entity 实体
   * @param frequency 更新频率（秒）
   */
  public setUpdateFrequency(entity: Entity, frequency: number): void {
    const data = this.avatarEntities.get(entity);
    if (data) {
      data.updateFrequency = Math.max(0, frequency);
    }
  }

  /**
   * 标记实体需要更新
   * @param entity 实体
   */
  public markNeedsUpdate(entity: Entity): void {
    const data = this.avatarEntities.get(entity);
    if (data) {
      data.needsUpdate = true;
    }
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public addEventListener(type: string, callback: (data: any) => void): void {
    this.eventEmitter.on(type, callback);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public removeEventListener(type: string, callback: (data: any) => void): void {
    this.eventEmitter.off(type, callback);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.avatarEntities.clear();
    this.defaultAnimations.clear();
    this.boneMappings.clear();
  }
}
