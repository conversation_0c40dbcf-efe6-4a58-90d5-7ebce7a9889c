/**
 * 输入事件
 */
export interface InputEvent {
    /** 时间戳 */
    timestamp: number;
    /** 设备名称 */
    deviceName: string;
    /** 键名 */
    key: string;
    /** 值 */
    value: any;
}
/**
 * 输入录制
 */
export interface InputRecording {
    /** 开始时间戳 */
    startTimestamp: number;
    /** 结束时间戳 */
    endTimestamp: number;
    /** 输入事件列表 */
    events: InputEvent[];
}
/**
 * 输入录制器选项
 */
export interface InputRecorderOptions {
    /** 是否自动开始录制 */
    autoStart?: boolean;
    /** 是否记录所有设备 */
    recordAllDevices?: boolean;
    /** 要记录的设备名称列表 */
    deviceNames?: string[];
}
/**
 * 输入录制器
 */
export declare class InputRecorder {
    /** 输入管理器 */
    private inputManager;
    /** 是否正在录制 */
    private recording;
    /** 是否正在回放 */
    private playing;
    /** 当前录制 */
    private currentRecording;
    /** 回放开始时间戳 */
    private playbackStartTimestamp;
    /** 回放事件索引 */
    private playbackEventIndex;
    /** 回放回调 */
    private playbackCallback;
    /** 是否记录所有设备 */
    private recordAllDevices;
    /** 要记录的设备名称列表 */
    private deviceNames;
    /** 设备值变化处理器 */
    private deviceValueChangeHandlers;
    /**
     * 创建输入录制器
     * @param options 选项
     */
    constructor(options?: InputRecorderOptions);
    /**
     * 开始录制
     */
    startRecording(): void;
    /**
     * 停止录制
     * @returns 录制结果
     */
    stopRecording(): InputRecording | null;
    /**
     * 开始回放
     * @param recording 录制
     * @param callback 回放完成回调
     */
    startPlayback(recording: InputRecording, callback?: () => void): void;
    /**
     * 停止回放
     */
    stopPlayback(): void;
    /**
     * 回放下一个事件
     * @param recording 录制
     */
    private playbackNextEvent;
    /**
     * 添加设备值变化处理器
     * @param device 设备
     */
    private addDeviceValueChangeHandler;
    /**
     * 移除设备值变化处理器
     * @param device 设备
     */
    private removeDeviceValueChangeHandler;
    /**
     * 移除所有设备值变化处理器
     */
    private removeAllDeviceValueChangeHandlers;
    /**
     * 获取所有设备
     * @returns 设备列表
     */
    private getAllDevices;
    /**
     * 保存录制到文件
     * @param recording 录制
     * @param filename 文件名
     */
    static saveToFile(recording: InputRecording, filename?: string): void;
    /**
     * 从文件加载录制
     * @param file 文件
     * @returns Promise<InputRecording>
     */
    static loadFromFile(file: File): Promise<InputRecording>;
}
