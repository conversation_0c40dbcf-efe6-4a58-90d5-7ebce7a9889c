/**
 * 水体示例入口文件
 */
import { WaterfallExample } from './WaterfallExample';
import { HotSpringExample } from './HotSpringExample';
import { EnhancedWaterSystemExample } from './EnhancedWaterSystemExample';

// 创建示例
let currentExample: WaterfallExample | HotSpringExample | EnhancedWaterSystemExample | null = null;

// 创建UI
createUI();

/**
 * 创建UI
 */
function createUI(): void {
  // 创建UI容器
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.top = '10px';
  container.style.right = '10px';
  container.style.padding = '10px';
  container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
  container.style.color = 'white';
  container.style.fontFamily = 'Arial, sans-serif';
  container.style.borderRadius = '5px';
  document.body.appendChild(container);

  // 创建标题
  const title = document.createElement('h2');
  title.textContent = '水体示例';
  title.style.margin = '0 0 10px 0';
  container.appendChild(title);

  // 创建瀑布示例按钮
  const waterfallButton = document.createElement('button');
  waterfallButton.textContent = '瀑布示例';
  waterfallButton.style.marginRight = '10px';
  waterfallButton.style.padding = '5px 10px';
  waterfallButton.style.backgroundColor = '#4CAF50';
  waterfallButton.style.border = 'none';
  waterfallButton.style.color = 'white';
  waterfallButton.style.cursor = 'pointer';
  waterfallButton.style.borderRadius = '3px';
  container.appendChild(waterfallButton);

  // 创建温泉示例按钮
  const hotSpringButton = document.createElement('button');
  hotSpringButton.textContent = '温泉示例';
  hotSpringButton.style.padding = '5px 10px';
  hotSpringButton.style.backgroundColor = '#2196F3';
  hotSpringButton.style.border = 'none';
  hotSpringButton.style.color = 'white';
  hotSpringButton.style.cursor = 'pointer';
  hotSpringButton.style.borderRadius = '3px';
  hotSpringButton.style.marginRight = '10px';
  container.appendChild(hotSpringButton);

  // 创建增强水系统示例按钮
  const enhancedWaterButton = document.createElement('button');
  enhancedWaterButton.textContent = '增强水系统示例';
  enhancedWaterButton.style.padding = '5px 10px';
  enhancedWaterButton.style.backgroundColor = '#FF9800';
  enhancedWaterButton.style.border = 'none';
  enhancedWaterButton.style.color = 'white';
  enhancedWaterButton.style.cursor = 'pointer';
  enhancedWaterButton.style.borderRadius = '3px';
  container.appendChild(enhancedWaterButton);

  // 添加瀑布示例按钮点击事件
  waterfallButton.addEventListener('click', () => {
    // 如果当前示例不是瀑布示例，则创建瀑布示例
    if (!(currentExample instanceof WaterfallExample)) {
      // 销毁当前示例
      if (currentExample) {
        // 如果是增强水系统示例，调用其stop方法
        if (currentExample instanceof EnhancedWaterSystemExample) {
          currentExample.stop();
        }
        // 其他示例没有实现销毁逻辑
      }

      // 创建瀑布示例
      currentExample = new WaterfallExample();
    }
  });

  // 添加温泉示例按钮点击事件
  hotSpringButton.addEventListener('click', () => {
    // 如果当前示例不是温泉示例，则创建温泉示例
    if (!(currentExample instanceof HotSpringExample)) {
      // 销毁当前示例
      if (currentExample) {
        // 如果是增强水系统示例，调用其stop方法
        if (currentExample instanceof EnhancedWaterSystemExample) {
          currentExample.stop();
        }
        // 其他示例没有实现销毁逻辑
      }

      // 创建温泉示例
      currentExample = new HotSpringExample();
    }
  });

  // 添加增强水系统示例按钮点击事件
  enhancedWaterButton.addEventListener('click', () => {
    // 如果当前示例不是增强水系统示例，则创建增强水系统示例
    if (!(currentExample instanceof EnhancedWaterSystemExample)) {
      // 销毁当前示例
      if (currentExample) {
        // 如果是增强水系统示例，调用其stop方法
        if (currentExample instanceof EnhancedWaterSystemExample) {
          currentExample.stop();
        }
        // 其他示例没有实现销毁逻辑
      }

      // 创建增强水系统示例
      currentExample = new EnhancedWaterSystemExample();
      // 启动示例
      currentExample.start();
    }
  });

  // 默认创建瀑布示例
  waterfallButton.click();
}

// 导出示例
export { currentExample };
