import { SubClip } from './SubClip';
import { AnimationSubClip } from './AnimationSubClip';
/**
 * 子片段事件类型
 */
export declare enum SubClipEventType {
    /** 事件触发 */
    EVENT_TRIGGERED = "eventTriggered",
    /** 事件添加 */
    EVENT_ADDED = "eventAdded",
    /** 事件移除 */
    EVENT_REMOVED = "eventRemoved",
    /** 事件更新 */
    EVENT_UPDATED = "eventUpdated"
}
/**
 * 事件触发类型
 */
export declare enum EventTriggerType {
    /** 时间点 */
    TIME = "time",
    /** 百分比 */
    PERCENT = "percent",
    /** 帧 */
    FRAME = "frame",
    /** 开始 */
    START = "start",
    /** 结束 */
    END = "end",
    /** 循环 */
    LOOP = "loop",
    /** 条件 */
    CONDITION = "condition"
}
/**
 * 子片段事件配置
 */
export interface SubClipEventConfig {
    /** 事件名称 */
    name?: string;
    /** 触发类型 */
    triggerType?: EventTriggerType;
    /** 触发值 */
    triggerValue?: number;
    /** 触发条件 */
    triggerCondition?: (time: number, progress: number, clip: SubClip | AnimationSubClip) => boolean;
    /** 回调函数 */
    callback?: (event: any) => void;
    /** 自定义数据 */
    userData?: any;
    /** 是否只触发一次 */
    once?: boolean;
    /** 是否启用 */
    enabled?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 子片段事件
 */
export declare class SubClipEvent {
    /** 事件名称 */
    private name;
    /** 触发类型 */
    private triggerType;
    /** 触发值 */
    private triggerValue;
    /** 触发条件 */
    private triggerCondition?;
    /** 回调函数 */
    private callback?;
    /** 自定义数据 */
    private userData;
    /** 是否只触发一次 */
    private once;
    /** 是否启用 */
    private enabled;
    /** 是否启用调试 */
    private debug;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否已触发 */
    private triggered;
    /**
     * 创建子片段事件
     * @param config 配置
     */
    constructor(config?: SubClipEventConfig);
    /**
     * 获取事件名称
     * @returns 事件名称
     */
    getName(): string;
    /**
     * 设置事件名称
     * @param name 事件名称
     */
    setName(name: string): void;
    /**
     * 获取触发类型
     * @returns 触发类型
     */
    getTriggerType(): EventTriggerType;
    /**
     * 设置触发类型
     * @param type 触发类型
     */
    setTriggerType(type: EventTriggerType): void;
    /**
     * 获取触发值
     * @returns 触发值
     */
    getTriggerValue(): number;
    /**
     * 设置触发值
     * @param value 触发值
     */
    setTriggerValue(value: number): void;
    /**
     * 设置触发条件
     * @param condition 触发条件
     */
    setTriggerCondition(condition: (time: number, progress: number, clip: SubClip | AnimationSubClip) => boolean): void;
    /**
     * 获取回调函数
     * @returns 回调函数
     */
    getCallback(): ((event: any) => void) | undefined;
    /**
     * 设置回调函数
     * @param callback 回调函数
     */
    setCallback(callback: (event: any) => void): void;
    /**
     * 获取自定义数据
     * @returns 自定义数据
     */
    getUserData(): any;
    /**
     * 设置自定义数据
     * @param data 自定义数据
     */
    setUserData(data: any): void;
    /**
     * 是否只触发一次
     * @returns 是否只触发一次
     */
    isOnce(): boolean;
    /**
     * 设置是否只触发一次
     * @param once 是否只触发一次
     */
    setOnce(once: boolean): void;
    /**
     * 是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 是否已触发
     * @returns 是否已触发
     */
    isTriggered(): boolean;
    /**
     * 重置触发状态
     */
    reset(): void;
    /**
     * 检查是否应该触发事件
     * @param time 当前时间
     * @param progress 当前进度（0-1）
     * @param clip 子片段
     * @returns 是否应该触发
     */
    shouldTrigger(time: number, progress: number, clip: SubClip | AnimationSubClip): boolean;
    /**
     * 触发事件
     * @param time 当前时间
     * @param progress 当前进度
     * @param clip 子片段
     */
    trigger(time: number, progress: number, clip: SubClip | AnimationSubClip): void;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: SubClipEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: SubClipEventType, listener: (data: any) => void): void;
}
