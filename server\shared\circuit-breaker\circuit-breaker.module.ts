/**
 * 熔断器模块
 */
import { Module, DynamicModule, Provider, Global } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { CircuitBreakerService } from './circuit-breaker.service';
import { CircuitBreakerOptions } from './circuit-breaker.interface';

/**
 * 熔断器模块配置
 */
export interface CircuitBreakerModuleOptions {
  /**
   * 是否全局注册
   */
  isGlobal?: boolean;
  
  /**
   * 默认熔断器配置
   */
  defaultOptions?: Partial<CircuitBreakerOptions>;
}

/**
 * 熔断器模块
 */
@Global()
@Module({
  imports: [EventEmitterModule.forRoot()],
  providers: [CircuitBreakerService],
  exports: [CircuitBreakerService],
})
export class CircuitBreakerModule {
  /**
   * 注册熔断器模块
   * @param options 模块配置
   */
  static register(options: CircuitBreakerModuleOptions = {}): DynamicModule {
    return {
      module: CircuitBreakerModule,
      global: options.isGlobal,
      providers: [
        {
          provide: CircuitBreakerService,
          useFactory: (eventEmitter) => {
            const service = new CircuitBreakerService(eventEmitter);
            if (options.defaultOptions) {
              Object.assign(service['defaultOptions'], options.defaultOptions);
            }
            return service;
          },
          inject: ['EventEmitter2'],
        },
      ],
      exports: [CircuitBreakerService],
    };
  }
}

/**
 * 导出所有熔断器相关类和接口
 */
export * from './circuit-breaker';
export * from './circuit-breaker.service';
export * from './circuit-breaker.interface';
