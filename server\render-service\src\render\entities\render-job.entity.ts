/**
 * 渲染任务实体
 */
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { RenderResult } from './render-result.entity';

export enum RenderJobStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELED = 'canceled',
}

export enum RenderJobType {
  IMAGE = 'image',
  VIDEO = 'video',
  ANIMATION = 'animation',
}

@Entity('render_jobs')
export class RenderJob {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: RenderJobType,
    default: RenderJobType.IMAGE,
  })
  type: RenderJobType;

  @Column({
    type: 'enum',
    enum: RenderJobStatus,
    default: RenderJobStatus.PENDING,
  })
  status: RenderJobStatus;

  @Column()
  userId: string;

  @Column()
  projectId: string;

  @Column()
  sceneId: string;

  @Column({ type: 'json' })
  settings: {
    width: number;
    height: number;
    quality: number;
    format: string;
    frames?: number;
    fps?: number;
    camera?: string;
    lighting?: string;
    postProcessing?: boolean;
    [key: string]: any;
  };

  @Column({ nullable: true })
  errorMessage: string;

  @Column({ nullable: true })
  progress: number;

  @Column({ nullable: true })
  estimatedTimeRemaining: number;

  @OneToMany(() => RenderResult, result => result.job, { cascade: true })
  results: RenderResult[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
