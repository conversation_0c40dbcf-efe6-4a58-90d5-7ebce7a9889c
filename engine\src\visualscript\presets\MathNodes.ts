/**
 * 视觉脚本数学节点
 * 提供数学运算相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 三角函数节点类型
 */
enum TrigonometricType {
  SIN = 'sin',
  COS = 'cos',
  TAN = 'tan',
  ASIN = 'asin',
  ACOS = 'acos',
  ATAN = 'atan'
}

/**
 * 加法节点
 * 计算两个数的和
 */
export class AddNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算结果
    const result = a + b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 减法节点
 * 计算两个数的差
 */
export class SubtractNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算结果
    const result = a - b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 乘法节点
 * 计算两个数的积
 */
export class MultiplyNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算结果
    const result = a * b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 除法节点
 * 计算两个数的商
 */
export class DivideNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '被除数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '除数',
      defaultValue: 1
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 检查除数是否为0
    if (b === 0) {
      console.error('除数不能为0');
      this.triggerFlow('flow');
      return NaN;
    }

    // 计算结果
    const result = a / b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 取模节点
 * 计算两个数的模
 */
export class ModuloNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '被除数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '除数',
      defaultValue: 1
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 检查除数是否为0
    if (b === 0) {
      console.error('除数不能为0');
      this.triggerFlow('flow');
      return NaN;
    }

    // 计算结果
    const result = a % b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 幂运算节点
 * 计算一个数的幂
 */
export class PowerNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加底数输入
    this.addInput({
      name: 'base',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '底数',
      defaultValue: 0
    });

    // 添加指数输入
    this.addInput({
      name: 'exponent',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '指数',
      defaultValue: 1
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const base = this.getInputValue('base') as number;
    const exponent = this.getInputValue('exponent') as number;

    // 计算结果
    const result = Math.pow(base, exponent);

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 平方根节点
 * 计算一个数的平方根
 */
export class SquareRootNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加数值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '数值',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const value = this.getInputValue('value') as number;

    // 检查输入值是否为负数
    if (value < 0) {
      console.error('不能计算负数的平方根');
      this.triggerFlow('flow');
      return NaN;
    }

    // 计算结果
    const result = Math.sqrt(value);

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 三角函数节点
 * 计算三角函数值
 */
export class TrigonometricNode extends FunctionNode {
  /** 三角函数类型 */
  private trigType: TrigonometricType;

  /**
   * 创建三角函数节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置三角函数类型
    this.trigType = options.metadata?.trigType || TrigonometricType.SIN;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加角度输入
    this.addInput({
      name: 'angle',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '角度（弧度）',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const angle = this.getInputValue('angle') as number;

    // 根据三角函数类型计算结果
    let result: number;

    switch (this.trigType) {
      case TrigonometricType.SIN:
        result = Math.sin(angle);
        break;
      case TrigonometricType.COS:
        result = Math.cos(angle);
        break;
      case TrigonometricType.TAN:
        result = Math.tan(angle);
        break;
      case TrigonometricType.ASIN:
        result = Math.asin(angle);
        break;
      case TrigonometricType.ACOS:
        result = Math.acos(angle);
        break;
      case TrigonometricType.ATAN:
        result = Math.atan(angle);
        break;
      default:
        result = 0;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 注册数学节点
 * @param registry 节点注册表
 */
export function registerMathNodes(registry: NodeRegistry): void {
  // 注册加法节点
  registry.registerNodeType({
    type: 'math/basic/add',
    category: NodeCategory.MATH,
    constructor: AddNode,
    label: '加法',
    description: '计算两个数的和',
    icon: 'plus',
    color: '#2196F3',
    tags: ['math', 'basic', 'add']
  });

  // 注册减法节点
  registry.registerNodeType({
    type: 'math/basic/subtract',
    category: NodeCategory.MATH,
    constructor: SubtractNode,
    label: '减法',
    description: '计算两个数的差',
    icon: 'minus',
    color: '#2196F3',
    tags: ['math', 'basic', 'subtract']
  });

  // 注册乘法节点
  registry.registerNodeType({
    type: 'math/basic/multiply',
    category: NodeCategory.MATH,
    constructor: MultiplyNode,
    label: '乘法',
    description: '计算两个数的积',
    icon: 'multiply',
    color: '#2196F3',
    tags: ['math', 'basic', 'multiply']
  });

  // 注册除法节点
  registry.registerNodeType({
    type: 'math/basic/divide',
    category: NodeCategory.MATH,
    constructor: DivideNode,
    label: '除法',
    description: '计算两个数的商',
    icon: 'divide',
    color: '#2196F3',
    tags: ['math', 'basic', 'divide']
  });

  // 注册取模节点
  registry.registerNodeType({
    type: 'math/basic/modulo',
    category: NodeCategory.MATH,
    constructor: ModuloNode,
    label: '取模',
    description: '计算两个数的模',
    icon: 'modulo',
    color: '#2196F3',
    tags: ['math', 'basic', 'modulo']
  });

  // 注册幂运算节点
  registry.registerNodeType({
    type: 'math/advanced/power',
    category: NodeCategory.MATH,
    constructor: PowerNode,
    label: '幂运算',
    description: '计算一个数的幂',
    icon: 'power',
    color: '#2196F3',
    tags: ['math', 'advanced', 'power']
  });

  // 注册平方根节点
  registry.registerNodeType({
    type: 'math/advanced/sqrt',
    category: NodeCategory.MATH,
    constructor: SquareRootNode,
    label: '平方根',
    description: '计算一个数的平方根',
    icon: 'sqrt',
    color: '#2196F3',
    tags: ['math', 'advanced', 'sqrt']
  });

  // 注册正弦函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/sin',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '正弦',
    description: '计算正弦值',
    icon: 'sin',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'sin'],
    metadata: {
      trigType: TrigonometricType.SIN
    }
  });

  // 注册余弦函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/cos',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '余弦',
    description: '计算余弦值',
    icon: 'cos',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'cos'],
    metadata: {
      trigType: TrigonometricType.COS
    }
  });

  // 注册正切函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/tan',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '正切',
    description: '计算正切值',
    icon: 'tan',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'tan'],
    metadata: {
      trigType: TrigonometricType.TAN
    }
  });
}
