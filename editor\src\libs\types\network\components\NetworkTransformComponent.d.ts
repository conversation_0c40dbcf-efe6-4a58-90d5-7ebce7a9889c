import { Component } from '../../core/Component';
/**
 * 网络变换组件属性
 */
export interface NetworkTransformComponentProps {
    /** 同步间隔（毫秒） */
    syncInterval?: number;
    /** 是否自动同步 */
    autoSync?: boolean;
    /** 是否同步位置 */
    syncPosition?: boolean;
    /** 是否同步旋转 */
    syncRotation?: boolean;
    /** 是否同步缩放 */
    syncScale?: boolean;
    /** 位置插值速度 */
    positionLerpSpeed?: number;
    /** 旋转插值速度 */
    rotationLerpSpeed?: number;
    /** 缩放插值速度 */
    scaleLerpSpeed?: number;
    /** 位置同步阈值 */
    positionThreshold?: number;
    /** 旋转同步阈值（弧度） */
    rotationThreshold?: number;
    /** 缩放同步阈值 */
    scaleThreshold?: number;
    /** 是否使用压缩 */
    useCompression?: boolean;
    /** 是否使用预测 */
    usePrediction?: boolean;
    /** 是否使用平滑 */
    useSmoothing?: boolean;
    /** 是否使用外推 */
    useExtrapolation?: boolean;
    /** 外推时间（秒） */
    extrapolationTime?: number;
    /** 最大外推距离 */
    maxExtrapolationDistance?: number;
    /** 同步优先级 */
    syncPriority?: number;
}
/**
 * 网络变换组件
 * 用于同步实体的变换（位置、旋转、缩放）
 */
export declare class NetworkTransformComponent extends Component {
    /** 组件类型 */
    static readonly type = "NetworkTransform";
    /** 同步间隔（毫秒） */
    syncInterval: number;
    /** 是否自动同步 */
    autoSync: boolean;
    /** 是否同步位置 */
    syncPosition: boolean;
    /** 是否同步旋转 */
    syncRotation: boolean;
    /** 是否同步缩放 */
    syncScale: boolean;
    /** 位置插值速度 */
    positionLerpSpeed: number;
    /** 旋转插值速度 */
    rotationLerpSpeed: number;
    /** 缩放插值速度 */
    scaleLerpSpeed: number;
    /** 位置同步阈值 */
    positionThreshold: number;
    /** 旋转同步阈值（弧度） */
    rotationThreshold: number;
    /** 缩放同步阈值 */
    scaleThreshold: number;
    /** 是否使用压缩 */
    useCompression: boolean;
    /** 是否使用预测 */
    usePrediction: boolean;
    /** 是否使用平滑 */
    useSmoothing: boolean;
    /** 是否使用外推 */
    useExtrapolation: boolean;
    /** 外推时间（秒） */
    extrapolationTime: number;
    /** 最大外推距离 */
    maxExtrapolationDistance: number;
    /** 同步优先级 */
    syncPriority: number;
    /** 上次同步时间 */
    private lastSyncTime;
    /** 上次同步位置 */
    private lastSyncedPosition;
    /** 上次同步旋转 */
    private lastSyncedRotation;
    /** 上次同步缩放 */
    private lastSyncedScale;
    /** 目标位置 */
    private targetPosition;
    /** 目标旋转 */
    private targetRotation;
    /** 目标缩放 */
    private targetScale;
    /** 是否正在插值 */
    private isLerping;
    /** 插值开始时间 */
    private lerpStartTime;
    /** 插值持续时间 */
    private lerpDuration;
    /** 是否有待同步的更改 */
    private hasPendingChanges;
    /** 网络实体组件 */
    private networkEntity;
    /** 变换组件 */
    private transform;
    /**
     * 创建网络变换组件
     * @param props 组件属性
     */
    constructor(props?: NetworkTransformComponentProps);
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 同步变换
     */
    sync(): void;
    /**
     * 应用同步数据
     * @param data 同步数据
     */
    applySyncData(data: any): void;
    /**
     * 开始插值
     */
    private startLerping;
    /**
     * 插值更新
     * @param deltaTime 帧间隔时间（秒）
     */
    private interpolate;
    /**
     * 变换变化事件处理
     */
    private onTransformChanged;
    /**
     * 网络同步事件处理
     * @param data 同步数据
     */
    private onNetworkSync;
    /**
     * 销毁组件
     */
    dispose(): void;
}
