/**
 * 协作编辑示例项目主脚本
 */
import { Engine, World, Entity, Scene, Transform, Camera, Mesh, Material, Light } from '../../../engine/dist/index.js';
import { CollaborationManager } from './collaboration/CollaborationManager.js';
import { UserManager } from './collaboration/UserManager.js';
import { OperationManager } from './collaboration/OperationManager.js';
import { ConflictResolver } from './collaboration/ConflictResolver.js';
import { PermissionManager } from './collaboration/PermissionManager.js';
import { HistoryManager } from './collaboration/HistoryManager.js';
import { ChatManager } from './collaboration/ChatManager.js';
import { UIManager } from './ui/UIManager.js';
import { SceneManager } from './scene/SceneManager.js';

// 全局变量
let engine;
let world;
let scene;
let camera;
let collaborationManager;
let userManager;
let operationManager;
let conflictResolver;
let permissionManager;
let historyManager;
let chatManager;
let uiManager;
let sceneManager;
let currentUser = null;

// 初始化函数
async function initialize() {
  // 初始化引擎
  engine = new Engine({
    container: document.getElementById('viewport-canvas'),
    antialias: true,
    shadows: true,
  });

  // 创建世界
  world = new World(engine);

  // 创建场景
  scene = new Scene(world, {
    name: '协作编辑示例场景',
    background: { type: 'color', value: '#87CEEB' },
  });

  // 创建相机
  camera = new Entity(world)
    .addComponent(new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000,
    }))
    .addComponent(new Transform({
      position: { x: 0, y: 2, z: 5 },
      rotation: { x: -0.2, y: 0, z: 0 },
    }));

  // 添加相机到场景
  scene.addEntity(camera);

  // 初始化场景管理器
  sceneManager = new SceneManager(world, scene, camera);
  await sceneManager.loadDefaultScene();

  // 初始化UI管理器
  uiManager = new UIManager();
  uiManager.initialize();

  // 初始化用户管理器
  userManager = new UserManager();
  
  // 初始化权限管理器
  permissionManager = new PermissionManager();
  
  // 初始化操作管理器
  operationManager = new OperationManager(scene, userManager);
  
  // 初始化冲突解决器
  conflictResolver = new ConflictResolver(operationManager);
  
  // 初始化历史管理器
  historyManager = new HistoryManager(operationManager);
  
  // 初始化聊天管理器
  chatManager = new ChatManager(userManager);
  
  // 初始化协作管理器
  collaborationManager = new CollaborationManager({
    userManager,
    operationManager,
    conflictResolver,
    permissionManager,
    historyManager,
    chatManager
  });

  // 设置事件监听器
  setupEventListeners();
  
  // 显示登录对话框
  showLoginDialog();
  
  // 开始渲染循环
  engine.start();
}

// 设置事件监听器
function setupEventListeners() {
  // 登录按钮点击事件
  document.getElementById('login-button').addEventListener('click', () => {
    showLoginDialog();
  });
  
  // 退出按钮点击事件
  document.getElementById('logout-button').addEventListener('click', () => {
    logout();
  });
  
  // 登录表单提交事件
  document.getElementById('login-form').addEventListener('submit', (e) => {
    e.preventDefault();
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    login(username, password);
  });
  
  // 访客登录按钮点击事件
  document.getElementById('login-as-guest').addEventListener('click', () => {
    loginAsGuest();
  });
  
  // 登录对话框关闭按钮点击事件
  document.getElementById('login-dialog-close').addEventListener('click', () => {
    hideLoginDialog();
  });
  
  // 冲突对话框关闭按钮点击事件
  document.getElementById('conflict-dialog-close').addEventListener('click', () => {
    hideConflictDialog();
  });
  
  // 保留本地更改按钮点击事件
  document.getElementById('keep-local').addEventListener('click', () => {
    conflictResolver.resolveConflict('local');
    hideConflictDialog();
  });
  
  // 采用远程更改按钮点击事件
  document.getElementById('keep-remote').addEventListener('click', () => {
    conflictResolver.resolveConflict('remote');
    hideConflictDialog();
  });
  
  // 手动合并更改按钮点击事件
  document.getElementById('merge-changes').addEventListener('click', () => {
    conflictResolver.resolveConflict('merge');
    hideConflictDialog();
  });
  
  // 聊天发送按钮点击事件
  document.getElementById('chat-send-button').addEventListener('click', () => {
    sendChatMessage();
  });
  
  // 聊天输入框回车事件
  document.getElementById('chat-input-field').addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      sendChatMessage();
    }
  });
  
  // 撤销按钮点击事件
  document.getElementById('undo-button').addEventListener('click', () => {
    historyManager.undo();
  });
  
  // 重做按钮点击事件
  document.getElementById('redo-button').addEventListener('click', () => {
    historyManager.redo();
  });
  
  // 添加对象按钮点击事件
  document.getElementById('add-object-button').addEventListener('click', () => {
    sceneManager.addObject();
  });
  
  // 删除对象按钮点击事件
  document.getElementById('delete-object-button').addEventListener('click', () => {
    sceneManager.deleteSelectedObject();
  });
  
  // 工具按钮点击事件
  document.querySelectorAll('.tool-button').forEach(button => {
    button.addEventListener('click', () => {
      const tool = button.getAttribute('data-tool');
      sceneManager.setActiveTool(tool);
      
      // 更新UI
      document.querySelectorAll('.tool-button').forEach(btn => {
        btn.classList.remove('active');
      });
      button.classList.add('active');
    });
  });
  
  // 标签页切换事件
  document.querySelectorAll('.tab-button').forEach(button => {
    button.addEventListener('click', () => {
      const tab = button.getAttribute('data-tab');
      
      // 更新标签按钮状态
      document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
      });
      button.classList.add('active');
      
      // 更新标签内容显示
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });
      document.getElementById(`${tab}-tab`).classList.add('active');
    });
  });
  
  // 窗口大小变化事件
  window.addEventListener('resize', () => {
    engine.resize();
  });
}

// 显示登录对话框
function showLoginDialog() {
  const dialog = document.getElementById('login-dialog');
  dialog.classList.add('active');
}

// 隐藏登录对话框
function hideLoginDialog() {
  const dialog = document.getElementById('login-dialog');
  dialog.classList.remove('active');
}

// 显示冲突对话框
function showConflictDialog(conflict) {
  const dialog = document.getElementById('conflict-dialog');
  
  // 设置冲突预览
  document.getElementById('local-change-preview').innerHTML = `
    <pre>${JSON.stringify(conflict.localOperation, null, 2)}</pre>
  `;
  
  document.getElementById('remote-change-preview').innerHTML = `
    <pre>${JSON.stringify(conflict.remoteOperation, null, 2)}</pre>
  `;
  
  dialog.classList.add('active');
}

// 隐藏冲突对话框
function hideConflictDialog() {
  const dialog = document.getElementById('conflict-dialog');
  dialog.classList.remove('active');
}

// 登录
async function login(username, password) {
  try {
    // 在实际应用中，这里应该调用API进行身份验证
    // 这里使用模拟数据
    currentUser = {
      id: 'user1',
      username: username,
      role: 'editor',
      avatar: 'assets/images/avatar1.png'
    };
    
    // 更新UI
    updateUserUI();
    
    // 隐藏登录对话框
    hideLoginDialog();
    
    // 连接到协作会话
    await collaborationManager.connect(currentUser);
    
    console.log('登录成功:', currentUser);
  } catch (error) {
    console.error('登录失败:', error);
    alert('登录失败: ' + error.message);
  }
}

// 以访客身份登录
async function loginAsGuest() {
  try {
    // 生成随机访客ID
    const guestId = 'guest_' + Math.random().toString(36).substr(2, 9);
    
    currentUser = {
      id: guestId,
      username: '访客_' + guestId.substr(6),
      role: 'viewer',
      avatar: 'assets/images/avatar-guest.png'
    };
    
    // 更新UI
    updateUserUI();
    
    // 隐藏登录对话框
    hideLoginDialog();
    
    // 连接到协作会话
    await collaborationManager.connect(currentUser);
    
    console.log('访客登录成功:', currentUser);
  } catch (error) {
    console.error('访客登录失败:', error);
    alert('访客登录失败: ' + error.message);
  }
}

// 退出
async function logout() {
  try {
    // 断开协作会话
    await collaborationManager.disconnect();
    
    // 清除当前用户
    currentUser = null;
    
    // 更新UI
    updateUserUI();
    
    console.log('退出成功');
  } catch (error) {
    console.error('退出失败:', error);
    alert('退出失败: ' + error.message);
  }
}

// 更新用户UI
function updateUserUI() {
  if (currentUser) {
    document.getElementById('current-user-avatar').src = currentUser.avatar;
    document.getElementById('current-user-name').textContent = currentUser.username;
    document.getElementById('login-button').style.display = 'none';
    document.getElementById('logout-button').style.display = 'inline-flex';
  } else {
    document.getElementById('current-user-avatar').src = 'assets/images/avatar-default.png';
    document.getElementById('current-user-name').textContent = '未登录';
    document.getElementById('login-button').style.display = 'inline-flex';
    document.getElementById('logout-button').style.display = 'none';
  }
}

// 发送聊天消息
function sendChatMessage() {
  const inputField = document.getElementById('chat-input-field');
  const message = inputField.value.trim();
  
  if (message && currentUser) {
    chatManager.sendMessage(currentUser, message);
    inputField.value = '';
  }
}

// 当文档加载完成时初始化
document.addEventListener('DOMContentLoaded', initialize);
