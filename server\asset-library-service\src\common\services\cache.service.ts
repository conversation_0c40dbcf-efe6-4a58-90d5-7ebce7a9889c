import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, RedisClientType } from 'redis';
import { LoggerService } from './logger.service';

@Injectable()
export class CacheService implements OnModuleInit, OnModuleDestroy {
  private client: RedisClientType;
  private isConnected = false;

  constructor(
    private configService: ConfigService,
    private logger: LoggerService,
  ) {}

  async onModuleInit(): Promise<void> {
    await this.connect();
  }

  async onModuleDestroy(): Promise<void> {
    await this.disconnect();
  }

  private async connect(): Promise<void> {
    try {
      this.client = createClient({
        url: this.configService.get('REDIS_URL', 'redis://localhost:6379'),
        password: this.configService.get('REDIS_PASSWORD'),
        database: this.configService.get('REDIS_DB', 0),
        socket: {
          connectTimeout: 5000, // 5秒连接超时
        },
        commandsQueueMaxLength: 1000, // 命令队列最大长度
      });

      this.client.on('error', (error) => {
        this.logger.error('Redis连接错误', error.message, 'CacheService');
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        this.logger.log('Redis连接成功', 'CacheService');
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        this.logger.warn('Redis连接断开', 'CacheService');
        this.isConnected = false;
      });

      this.client.on('reconnecting', () => {
        this.logger.log('Redis重新连接中...', 'CacheService');
      });

      // 设置连接超时
      const connectPromise = this.client.connect();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Redis连接超时')), 10000);
      });

      await Promise.race([connectPromise, timeoutPromise]);
    } catch (error) {
      this.logger.error('Redis初始化失败', error.message, 'CacheService');
      this.isConnected = false;
      // 不抛出错误，允许服务在没有Redis的情况下继续运行
    }
  }

  private async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      await this.client.disconnect();
      this.isConnected = false;
    }
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    if (!this.isConnected) {
      this.logger.warn('Redis未连接，跳过缓存设置', 'CacheService');
      return;
    }

    try {
      const serializedValue = JSON.stringify(value);
      if (ttl) {
        await this.client.setEx(key, ttl, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }
    } catch (error) {
      this.logger.error('缓存设置失败', error.message, 'CacheService');
    }
  }

  /**
   * 获取缓存
   */
  async get<T = any>(key: string): Promise<T | null> {
    if (!this.isConnected) {
      return null;
    }

    try {
      const value = await this.client.get(key);
      if (value) {
        return JSON.parse(value);
      }
      return null;
    } catch (error) {
      this.logger.error('缓存获取失败', error.message, 'CacheService');
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await this.client.del(key);
    } catch (error) {
      this.logger.error('缓存删除失败', error.message, 'CacheService');
    }
  }

  /**
   * 批量删除缓存（支持通配符）
   */
  async delPattern(pattern: string): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        await this.client.del(keys);
      }
    } catch (error) {
      this.logger.error('批量缓存删除失败', error.message, 'CacheService');
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    if (!this.isConnected) {
      return false;
    }

    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      this.logger.error('缓存存在性检查失败', error.message, 'CacheService');
      return false;
    }
  }

  /**
   * 设置过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await this.client.expire(key, ttl);
    } catch (error) {
      this.logger.error('设置缓存过期时间失败', error.message, 'CacheService');
    }
  }

  /**
   * 获取剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    if (!this.isConnected) {
      return -1;
    }

    try {
      return await this.client.ttl(key);
    } catch (error) {
      this.logger.error('获取缓存过期时间失败', error.message, 'CacheService');
      return -1;
    }
  }

  /**
   * 原子递增
   */
  async incr(key: string): Promise<number> {
    if (!this.isConnected) {
      return 0;
    }

    try {
      return await this.client.incr(key);
    } catch (error) {
      this.logger.error('缓存递增失败', error.message, 'CacheService');
      return 0;
    }
  }

  /**
   * 原子递减
   */
  async decr(key: string): Promise<number> {
    if (!this.isConnected) {
      return 0;
    }

    try {
      return await this.client.decr(key);
    } catch (error) {
      this.logger.error('缓存递减失败', error.message, 'CacheService');
      return 0;
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * 清空所有缓存
   */
  async flushAll(): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await this.client.flushAll();
      this.logger.log('所有缓存已清空', 'CacheService');
    } catch (error) {
      this.logger.error('清空缓存失败', error.message, 'CacheService');
    }
  }
}
