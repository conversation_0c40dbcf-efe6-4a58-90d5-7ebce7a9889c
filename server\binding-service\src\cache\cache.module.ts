import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { createClient } from 'redis';
import { CacheService } from './cache.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'REDIS_CLIENT',
      useFactory: async (configService: ConfigService) => {
        const client = createClient({
          socket: {
            host: configService.get('binding.cache.host', 'localhost'),
            port: configService.get('binding.cache.port', 6379),
          },
          password: configService.get('binding.cache.password'),
        });

        client.on('error', (err) => {
          console.error('Redis Client Error:', err);
        });

        client.on('connect', () => {
          console.log('Redis Client Connected');
        });

        try {
          await client.connect();
          return client;
        } catch (error) {
          console.warn('Redis connection failed, using mock client:', error.message);
          // 返回一个模拟的 Redis 客户端用于开发环境
          return {
            setEx: async () => {},
            get: async () => null,
            del: async () => {},
            keys: async () => [],
            exists: async () => 0,
            expire: async () => {},
            ttl: async () => -1,
          };
        }
      },
      inject: [ConfigService],
    },
    CacheService,
  ],
  exports: ['REDIS_CLIENT', CacheService],
})
export class CacheModule {}
