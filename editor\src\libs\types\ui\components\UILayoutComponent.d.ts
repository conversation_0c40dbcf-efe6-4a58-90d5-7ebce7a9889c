/**
 * UILayoutComponent.ts
 *
 * UI布局组件，用于管理UI元素的布局
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { IUILayout, UILayoutType } from '../interfaces/IUIElement';
import { UIComponent } from './UIComponent';
/**
 * 网格布局参数
 */
export interface GridLayoutParams {
    columns: number;
    rows?: number;
    cellWidth: number;
    cellHeight: number;
    columnGap?: number;
    rowGap?: number;
    justifyItems?: 'start' | 'center' | 'end' | 'stretch';
    alignItems?: 'start' | 'center' | 'end' | 'stretch';
    autoFlow?: 'row' | 'column';
}
/**
 * 弹性布局参数
 */
export interface FlexLayoutParams {
    direction: 'row' | 'column' | 'row-reverse' | 'column-reverse';
    wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
    justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
    alignItems?: 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch';
    alignContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'stretch';
    gap?: number;
}
/**
 * 绝对布局参数
 */
export interface AbsoluteLayoutParams {
}
/**
 * 相对布局参数
 */
export interface RelativeLayoutParams {
    spacing?: number;
    padding?: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
}
/**
 * 布局项参数
 */
export interface LayoutItemParams {
    gridColumn?: string;
    gridRow?: string;
    gridArea?: string;
    flexGrow?: number;
    flexShrink?: number;
    flexBasis?: number | string;
    alignSelf?: 'auto' | 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch';
    order?: number;
    left?: number;
    top?: number;
    right?: number;
    bottom?: number;
    zIndex?: number;
    margin?: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
}
/**
 * 网格布局
 */
export declare class GridLayout implements IUILayout {
    type: UILayoutType;
    params: GridLayoutParams;
    /**
     * 构造函数
     * @param params 网格布局参数
     */
    constructor(params: GridLayoutParams);
    /**
     * 应用布局
     * @param container 容器UI元素
     */
    apply(container: UIComponent): void;
}
/**
 * 弹性布局
 */
export declare class FlexLayout implements IUILayout {
    type: UILayoutType;
    params: FlexLayoutParams;
    /**
     * 构造函数
     * @param params 弹性布局参数
     */
    constructor(params: FlexLayoutParams);
    /**
     * 应用布局
     * @param container 容器UI元素
     */
    apply(container: UIComponent): void;
}
/**
 * 绝对布局
 */
export declare class AbsoluteLayout implements IUILayout {
    type: UILayoutType;
    params: AbsoluteLayoutParams;
    /**
     * 构造函数
     * @param params 绝对布局参数
     */
    constructor(params?: AbsoluteLayoutParams);
    /**
     * 应用布局
     * @param container 容器UI元素
     */
    apply(container: UIComponent): void;
}
/**
 * 相对布局
 */
export declare class RelativeLayout implements IUILayout {
    type: UILayoutType;
    params: RelativeLayoutParams;
    /**
     * 构造函数
     * @param params 相对布局参数
     */
    constructor(params?: RelativeLayoutParams);
    /**
     * 应用布局
     * @param container 容器UI元素
     */
    apply(container: UIComponent): void;
}
/**
 * UI布局组件
 * 用于管理实体的UI布局
 */
export declare class UILayoutComponent extends Component {
    layout: IUILayout;
    layoutItemParams?: LayoutItemParams;
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param layout 布局
     * @param layoutItemParams 布局项参数
     */
    constructor(entity: Entity, layout: IUILayout, layoutItemParams?: LayoutItemParams);
    /**
     * 应用布局
     * @param container 容器UI元素
     */
    applyLayout(container: UIComponent): void;
    /**
     * 设置布局
     * @param layout 新布局
     */
    setLayout(layout: IUILayout): void;
    /**
     * 设置布局项参数
     * @param params 布局项参数
     */
    setLayoutItemParams(params: LayoutItemParams): void;
}
