import { check } from 'k6';
import http from 'k6/http';
import { sleep } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';

// 性能指标
const cacheHits = new Counter('cache_hits');
const cacheMisses = new Counter('cache_misses');
const requestLatency = new Trend('request_latency');
const requestsPerSecond = new Rate('requests_per_second');
const errorRate = new Rate('error_rate');

// 配置
const CONFIG = {
  serviceRegistry: __ENV.SERVICE_REGISTRY_URL || 'http://localhost:4010',
  testService: __ENV.TEST_SERVICE || 'test-service',
  requestCount: parseInt(__ENV.REQUEST_COUNT || '1000'),
  uniqueKeysCount: parseInt(__ENV.UNIQUE_KEYS_COUNT || '100'),
};

// 生成测试键
function generateTestKeys(count) {
  const keys = [];
  for (let i = 0; i < count; i++) {
    keys.push(`test-key-${i}`);
  }
  return keys;
}

// 初始化函数 - 在测试开始前运行一次
export function setup() {
  // 注册测试服务实例
  for (let i = 0; i < 5; i++) {
    const res = http.post(`${CONFIG.serviceRegistry}/registry/services`, JSON.stringify({
      name: CONFIG.testService,
      instanceId: `test-instance-${i}`,
      host: `localhost`,
      port: 3000 + i,
      metadata: {
        version: '1.0.0',
      },
    }), {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    check(res, {
      '服务实例注册成功': (r) => r.status === 201 || r.status === 200,
    });
  }
  
  // 生成测试键
  return {
    keys: generateTestKeys(CONFIG.uniqueKeysCount),
  };
}

// 默认函数 - 在测试期间为每个虚拟用户运行
export default function(data) {
  // 随机选择一个键
  const key = data.keys[Math.floor(Math.random() * data.keys.length)];
  
  // 发送请求，测试缓存性能
  const startTime = new Date();
  
  const res = http.get(`${CONFIG.serviceRegistry}/registry/services/${CONFIG.testService}/instances`, {
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'max-age=60',
    },
    params: {
      key: key, // 用于缓存键的生成
    },
  });
  
  const duration = new Date() - startTime;
  
  // 记录请求延迟
  requestLatency.add(duration);
  
  // 记录请求成功/失败
  errorRate.add(res.status !== 200);
  
  // 记录每秒请求数
  requestsPerSecond.add(1);
  
  // 检查响应
  check(res, {
    '请求成功': (r) => r.status === 200,
    '返回实例信息': (r) => r.json().length > 0,
  });
  
  // 检查缓存命中情况
  if (res.headers['X-Cache'] === 'HIT') {
    cacheHits.add(1);
  } else {
    cacheMisses.add(1);
  }
  
  // 短暂休息
  sleep(0.05);
}

// 清理函数 - 在测试结束后运行一次
export function teardown(data) {
  // 清理测试服务实例
  for (let i = 0; i < 5; i++) {
    http.del(`${CONFIG.serviceRegistry}/registry/services/${CONFIG.testService}/instances/test-instance-${i}`, null, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
  
  // 清理缓存
  http.post(`${CONFIG.serviceRegistry}/registry/cache/clear`, null, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 测试配置
export const options = {
  scenarios: {
    // 缓存预热测试
    cache_warmup: {
      executor: 'constant-vus',
      vus: 10,
      duration: '30s',
    },
    // 高并发测试
    high_concurrency: {
      executor: 'constant-vus',
      vus: 50,
      duration: '1m',
      startTime: '30s',
    },
    // 缓存过期测试
    cache_expiry: {
      executor: 'constant-vus',
      vus: 10,
      duration: '2m',
      startTime: '1m30s',
    },
  },
  thresholds: {
    'request_latency': ['p(95)<100'], // 95%的请求延迟小于100ms
    'requests_per_second': ['avg>200'], // 平均每秒请求数大于200
    'error_rate': ['rate<0.01'], // 错误率小于1%
    'cache_hits': ['count>1000'], // 缓存命中次数大于1000
  },
};
