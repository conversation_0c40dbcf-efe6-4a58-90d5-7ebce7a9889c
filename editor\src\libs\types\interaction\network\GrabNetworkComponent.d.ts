/**
 * 抓取网络组件
 * 用于处理抓取系统的网络同步
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Hand } from '../components/GrabbableComponent';
/**
 * 抓取网络事件类型
 */
export declare enum GrabNetworkEventType {
    /** 抓取请求 */
    GRAB_REQUEST = "grabRequest",
    /** 抓取确认 */
    GRAB_CONFIRM = "grabConfirm",
    /** 抓取拒绝 */
    GRAB_REJECT = "grabReject",
    /** 释放请求 */
    RELEASE_REQUEST = "releaseRequest",
    /** 释放确认 */
    RELEASE_CONFIRM = "releaseConfirm",
    /** 状态同步 */
    STATE_SYNC = "stateSync"
}
/**
 * 抓取网络事件数据
 */
export interface GrabNetworkEventData {
    /** 事件类型 */
    type: GrabNetworkEventType;
    /** 抓取者ID */
    grabberEntityId: string;
    /** 被抓取实体ID */
    grabbedEntityId: string;
    /** 抓取手 */
    hand: Hand;
    /** 时间戳 */
    timestamp: number;
    /** 用户ID */
    userId: string;
    /** 会话ID */
    sessionId: string;
    /** 其他数据 */
    [key: string]: any;
}
/**
 * 抓取网络组件配置
 */
export interface GrabNetworkComponentConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 同步间隔（毫秒） */
    syncInterval?: number;
    /** 是否是权威节点 */
    isAuthority?: boolean;
    /** 用户ID */
    userId?: string;
    /** 会话ID */
    sessionId?: string;
}
/**
 * 抓取网络组件
 */
export declare class GrabNetworkComponent extends Component {
    /** 组件类型 */
    static readonly TYPE: string;
    /** 是否启用 */
    private _enabled;
    /** 同步间隔（毫秒） */
    private _syncInterval;
    /** 是否是权威节点 */
    private _isAuthority;
    /** 用户ID */
    private _userId;
    /** 会话ID */
    private _sessionId;
    /** 上次同步时间 */
    private _lastSyncTime;
    /** 待处理的事件队列 */
    private _pendingEvents;
    /** 事件发射器 */
    private eventEmitter;
    /** 网络连接状态 */
    private _isConnected;
    /** 网络延迟（毫秒） */
    private _networkLatency;
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param config 组件配置
     */
    constructor(entity: Entity, config?: GrabNetworkComponentConfig);
    /**
     * 获取是否启用
     */
    get componentEnabled(): boolean;
    /**
     * 设置是否启用
     */
    set componentEnabled(value: boolean);
    /**
     * 获取同步间隔
     */
    get syncInterval(): number;
    /**
     * 设置同步间隔
     */
    set syncInterval(value: number);
    /**
     * 获取是否是权威节点
     */
    get isAuthority(): boolean;
    /**
     * 设置是否是权威节点
     */
    set isAuthority(value: boolean);
    /**
     * 获取用户ID
     */
    get userId(): string;
    /**
     * 设置用户ID
     */
    set userId(value: string);
    /**
     * 获取会话ID
     */
    get sessionId(): string;
    /**
     * 设置会话ID
     */
    set sessionId(value: string);
    /**
     * 获取网络连接状态
     */
    get isConnected(): boolean;
    /**
     * 设置网络连接状态
     */
    set isConnected(value: boolean);
    /**
     * 获取网络延迟
     */
    get networkLatency(): number;
    /**
     * 设置网络延迟
     */
    set networkLatency(value: number);
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器函数
     */
    addEventListener(event: GrabNetworkEventType, listener: (data: GrabNetworkEventData) => void): this;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器函数
     */
    removeEventListener(event: GrabNetworkEventType, listener?: (data: GrabNetworkEventData) => void): this;
    /**
     * 发送抓取请求
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @returns 是否发送成功
     */
    sendGrabRequest(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean;
    /**
     * 发送抓取确认
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @returns 是否发送成功
     */
    sendGrabConfirm(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean;
    /**
     * 发送抓取拒绝
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @param reason 拒绝原因
     * @returns 是否发送成功
     */
    sendGrabReject(grabberEntityId: string, grabbedEntityId: string, hand: Hand, reason: string): boolean;
    /**
     * 发送释放请求
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @returns 是否发送成功
     */
    sendReleaseRequest(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean;
    /**
     * 发送释放确认
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @returns 是否发送成功
     */
    sendReleaseConfirm(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean;
    /**
     * 发送状态同步
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @param state 状态数据
     * @returns 是否发送成功
     */
    sendStateSync(grabberEntityId: string, grabbedEntityId: string, hand: Hand, state: any): boolean;
    /**
     * 接收网络事件
     * @param eventData 事件数据
     */
    receiveNetworkEvent(eventData: GrabNetworkEventData): void;
    /**
     * 处理待处理事件
     */
    processPendingEvents(): void;
    /**
     * 发送网络事件
     * @param eventData 事件数据
     * @private
     */
    private sendNetworkEvent;
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    update(_deltaTime: number): void;
}
