/**
 * 屏幕空间全局光照效果
 * 提升场景的整体光照质量
 */
import * as THREE from 'three';
// 使用类型断言导入 ShaderPass
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * SSGI效果选项
 */
export interface SSGIEffectOptions extends PostProcessingEffectOptions {
  /** 强度 */
  intensity?: number;
  /** 半径 */
  radius?: number;
  /** 偏移 */
  bias?: number;
  /** 采样数 */
  samples?: number;
  /** 衰减 */
  falloff?: number;
  /** 是否使用法线贴图 */
  useNormalMap?: boolean;
  /** 是否使用深度贴图 */
  useDepthMap?: boolean;
  /** 去噪迭代次数 */
  denoiseIterations?: number;
  /** 去噪核大小 */
  denoiseKernel?: number;
  /** 去噪漫反射强度 */
  denoiseDiffuse?: number;
  /** 去噪高光强度 */
  denoiseSpecular?: number;
  /** Phi值 */
  phi?: number;
  /** 亮度Phi值 */
  lumaPhi?: number;
  /** 深度Phi值 */
  depthPhi?: number;
  /** 法线Phi值 */
  normalPhi?: number;
  /** 粗糙度Phi值 */
  roughnessPhi?: number;
  /** 高光Phi值 */
  specularPhi?: number;
  /** 环境模糊 */
  envBlur?: number;
  /** 是否使用重要性采样 */
  importanceSampling?: boolean;
  /** 精细步数 */
  refineSteps?: number;
  /** 分辨率缩放 */
  resolutionScale?: number;
  /** 是否显示丢失的光线 */
  missedRays?: boolean;
}

/**
 * 屏幕空间全局光照效果
 */
export class SSGIEffect extends PostProcessingEffect {
  /** 强度 */
  private intensity: number;

  /** 半径 */
  private radius: number;

  /** 偏移 */
  private bias: number;

  /** 采样数 */
  private samples: number;

  /** 衰减 */
  private falloff: number;

  /** 是否使用法线贴图 */
  private useNormalMap: boolean;

  /** 是否使用深度贴图 */
  private useDepthMap: boolean;

  /** 去噪迭代次数 */
  private denoiseIterations: number;

  /** 去噪核大小 */
  private denoiseKernel: number;

  /** 去噪漫反射强度 */
  private denoiseDiffuse: number;

  /** 去噪高光强度 */
  private denoiseSpecular: number;

  /** Phi值 */
  private phi: number;

  /** 亮度Phi值 */
  private lumaPhi: number;

  /** 深度Phi值 */
  private depthPhi: number;

  /** 法线Phi值 */
  private normalPhi: number;

  /** 粗糙度Phi值 */
  private roughnessPhi: number;

  /** 高光Phi值 */
  private specularPhi: number;

  /** 环境模糊 */
  private envBlur: number;

  /** 是否使用重要性采样 */
  private importanceSampling: boolean;

  /** 精细步数 */
  private refineSteps: number;

  /** 分辨率缩放 */
  private resolutionScale: number;

  /** 是否显示丢失的光线 */
  private missedRays: boolean;

  /** SSGI通道 */
  private ssgiPass: ShaderPass | null = null;

  /** 场景 */
  private scene: THREE.Scene | null = null;

  /** 相机 */
  private camera: THREE.PerspectiveCamera | THREE.OrthographicCamera | null = null;

  /** 深度纹理 */
  private depthTexture: THREE.DepthTexture | null = null;

  /** 法线纹理 */
  private normalTexture: THREE.Texture | null = null;

  /** 渲染目标 */
  private renderTarget: THREE.WebGLRenderTarget | null = null;

  /**
   * 创建SSGI效果
   * @param options SSGI效果选项
   */
  constructor(options: SSGIEffectOptions = { name: 'SSGI' }) {
    super(options);

    this.intensity = options.intensity !== undefined ? options.intensity : 1.0;
    this.radius = options.radius || 5.0;
    this.bias = options.bias || 0.05;
    this.samples = options.samples || 16;
    this.falloff = options.falloff || 1.0;
    this.useNormalMap = options.useNormalMap !== undefined ? options.useNormalMap : true;
    this.useDepthMap = options.useDepthMap !== undefined ? options.useDepthMap : true;

    // 高级选项
    this.denoiseIterations = options.denoiseIterations || 1;
    this.denoiseKernel = options.denoiseKernel || 2;
    this.denoiseDiffuse = options.denoiseDiffuse || 10;
    this.denoiseSpecular = options.denoiseSpecular || 10;
    this.phi = options.phi || 0.5;
    this.lumaPhi = options.lumaPhi || 5;
    this.depthPhi = options.depthPhi || 2;
    this.normalPhi = options.normalPhi || 50;
    this.roughnessPhi = options.roughnessPhi || 50;
    this.specularPhi = options.specularPhi || 50;
    this.envBlur = options.envBlur || 0.5;
    this.importanceSampling = options.importanceSampling !== undefined ? options.importanceSampling : true;
    this.refineSteps = options.refineSteps || 5;
    this.resolutionScale = options.resolutionScale || 1;
    this.missedRays = options.missedRays !== undefined ? options.missedRays : false;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 如果没有场景或相机，则不创建通道
    if (!this.scene || !this.camera || !this.renderer) return;

    // 创建深度纹理
    this.depthTexture = new THREE.DepthTexture(this.width, this.height);
    this.depthTexture.type = THREE.UnsignedShortType;

    // 创建渲染目标
    this.renderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {
      minFilter: THREE.LinearFilter,
      magFilter: THREE.LinearFilter,
      format: THREE.RGBAFormat,
      depthTexture: this.depthTexture,
      depthBuffer: true
    });

    // 创建SSGI着色器
    const ssgiShader = {
      uniforms: {
        'tDiffuse': { value: null },
        'tDepth': { value: this.depthTexture },
        'tNormal': { value: this.normalTexture },
        'cameraNear': { value: this.camera.near },
        'cameraFar': { value: this.camera.far },
        'resolution': { value: new THREE.Vector2(this.width, this.height) },
        'cameraProjectionMatrix': { value: this.camera.projectionMatrix },
        'cameraInverseProjectionMatrix': { value: new THREE.Matrix4().copy(this.camera.projectionMatrix).invert() },
        'cameraWorldMatrix': { value: this.camera.matrixWorld },
        'intensity': { value: this.intensity },
        'radius': { value: this.radius },
        'bias': { value: this.bias },
        'samples': { value: this.samples },
        'falloff': { value: this.falloff },
        'useNormalMap': { value: this.useNormalMap ? 1 : 0 },
        'useDepthMap': { value: this.useDepthMap ? 1 : 0 },
        'denoiseIterations': { value: this.denoiseIterations },
        'denoiseKernel': { value: this.denoiseKernel },
        'denoiseDiffuse': { value: this.denoiseDiffuse },
        'denoiseSpecular': { value: this.denoiseSpecular },
        'phi': { value: this.phi },
        'lumaPhi': { value: this.lumaPhi },
        'depthPhi': { value: this.depthPhi },
        'normalPhi': { value: this.normalPhi },
        'roughnessPhi': { value: this.roughnessPhi },
        'specularPhi': { value: this.specularPhi },
        'envBlur': { value: this.envBlur },
        'importanceSampling': { value: this.importanceSampling ? 1 : 0 },
        'refineSteps': { value: this.refineSteps },
        'resolutionScale': { value: this.resolutionScale },
        'missedRays': { value: this.missedRays ? 1 : 0 },
        'time': { value: 0 }
      },
      vertexShader: /* glsl */`
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: /* glsl */`
        uniform sampler2D tDiffuse;
        uniform sampler2D tDepth;
        uniform sampler2D tNormal;
        uniform float cameraNear;
        uniform float cameraFar;
        uniform vec2 resolution;
        uniform mat4 cameraProjectionMatrix;
        uniform mat4 cameraInverseProjectionMatrix;
        uniform mat4 cameraWorldMatrix;
        uniform float intensity;
        uniform float radius;
        uniform float bias;
        uniform int samples;
        uniform float falloff;
        uniform int useNormalMap;
        uniform int useDepthMap;
        uniform int denoiseIterations;
        uniform int denoiseKernel;
        uniform float denoiseDiffuse;
        uniform float denoiseSpecular;
        uniform float phi;
        uniform float lumaPhi;
        uniform float depthPhi;
        uniform float normalPhi;
        uniform float roughnessPhi;
        uniform float specularPhi;
        uniform float envBlur;
        uniform int importanceSampling;
        uniform int refineSteps;
        uniform float resolutionScale;
        uniform int missedRays;
        uniform float time;

        varying vec2 vUv;

        const float PI = 3.14159265359;

        // 从深度纹理中获取深度值
        float getDepth(vec2 uv) {
          return texture2D(tDepth, uv).r;
        }

        // 从法线纹理中获取法线
        vec3 getNormal(vec2 uv) {
          if (useNormalMap == 1) {
            return normalize(texture2D(tNormal, uv).rgb * 2.0 - 1.0);
          } else {
            // 使用深度纹理计算法线
            float depth = getDepth(uv);
            vec2 size = vec2(1.0 / resolution.x, 1.0 / resolution.y);

            float dx = getDepth(uv + vec2(size.x, 0.0)) - getDepth(uv - vec2(size.x, 0.0));
            float dy = getDepth(uv + vec2(0.0, size.y)) - getDepth(uv - vec2(0.0, size.y));

            vec3 normal = normalize(vec3(dx, dy, 0.1));
            return normal;
          }
        }

        // 将屏幕空间坐标转换为世界空间坐标
        vec3 screenToWorld(vec2 uv, float depth) {
          vec4 clipPos = vec4(uv * 2.0 - 1.0, depth * 2.0 - 1.0, 1.0);
          vec4 viewPos = cameraInverseProjectionMatrix * clipPos;
          viewPos /= viewPos.w;
          vec4 worldPos = cameraWorldMatrix * viewPos;
          return worldPos.xyz;
        }

        // 将世界空间坐标转换为屏幕空间坐标
        vec2 worldToScreen(vec3 worldPos) {
          vec4 clipPos = cameraProjectionMatrix * cameraWorldMatrix * vec4(worldPos, 1.0);
          clipPos.xy /= clipPos.w;
          return clipPos.xy * 0.5 + 0.5;
        }

        // 随机函数
        float random(vec2 co) {
          return fract(sin(dot(co, vec2(12.9898, 78.233))) * 43758.5453);
        }

        // 生成半球采样向量
        vec3 hemisphereSample(float i, float n, vec3 normal) {
          // 使用黄金螺旋序列生成均匀分布的采样点
          float phi = PI * (3.0 - sqrt(5.0)); // 黄金角
          float y = 1.0 - (i / (n - 1.0)) * 2.0; // y从1到-1
          float radius = sqrt(1.0 - y * y); // 半球半径
          float theta = phi * i; // 角度

          vec3 sample = vec3(cos(theta) * radius, y, sin(theta) * radius);

          // 确保采样向量在法线的半球内
          float dotNS = dot(normal, sample);
          if (dotNS < 0.0) {
            sample = -sample;
          }

          return sample;
        }

        // 重要性采样
        vec3 importanceSampleGGX(vec2 Xi, float roughnessFactor, vec3 normal) {
          float a = roughnessFactor * roughnessFactor;

          float phi = 2.0 * PI * Xi.x;
          float cosTheta = sqrt((1.0 - Xi.y) / (1.0 + (a*a - 1.0) * Xi.y));
          float sinTheta = sqrt(1.0 - cosTheta * cosTheta);

          // 从球面坐标转换为笛卡尔坐标
          vec3 H = vec3(
            sinTheta * cos(phi),
            sinTheta * sin(phi),
            cosTheta
          );

          // 从切线空间转换到世界空间
          vec3 up = abs(normal.z) < 0.999 ? vec3(0.0, 0.0, 1.0) : vec3(1.0, 0.0, 0.0);
          vec3 tangent = normalize(cross(up, normal));
          vec3 bitangent = cross(normal, tangent);

          return tangent * H.x + bitangent * H.y + normal * H.z;
        }

        // 计算环境光遮蔽
        float computeAO(vec3 position, vec3 normal, float radius, float bias) {
          float occlusion = 0.0;

          // 生成随机旋转
          float randomAngle = random(vUv + vec2(time, 0.0)) * PI * 2.0;
          vec3 randomVec = vec3(cos(randomAngle), 0.0, sin(randomAngle));

          // 创建TBN矩阵
          vec3 tangent = normalize(randomVec - normal * dot(randomVec, normal));
          vec3 bitangent = cross(normal, tangent);
          mat3 TBN = mat3(tangent, bitangent, normal);

          // 使用重要性采样生成多个方向
          int sampleCount = importanceSampling == 1 ? min(samples, 32) : samples;

          // 采样周围点
          for (int i = 0; i < 64; i++) {
            if (i >= sampleCount) break;

            vec3 sampleVec;

            // 如果使用重要性采样，则生成更好的采样方向
            if (importanceSampling == 1 && i > 0) {
              vec2 Xi = vec2(random(vUv + vec2(float(i), time)), random(vUv + vec2(time, float(i))));
              sampleVec = importanceSampleGGX(Xi, 0.5, normal);
            } else {
              // 生成半球采样向量
              sampleVec = TBN * hemisphereSample(float(i), float(sampleCount), normal);
            }

            // 计算采样点
            vec3 samplePos = position + sampleVec * radius;

            // 将采样点转换为屏幕空间
            vec2 sampleUV = worldToScreen(samplePos);

            // 检查是否超出屏幕
            if (sampleUV.x < 0.0 || sampleUV.x > 1.0 || sampleUV.y < 0.0 || sampleUV.y > 1.0) {
              continue;
            }

            // 获取采样点的深度
            float sampleDepth = getDepth(sampleUV);
            vec3 sampleWorldPos = screenToWorld(sampleUV, sampleDepth);

            // 计算遮蔽因子
            float rangeCheck = smoothstep(0.0, 1.0, radius / length(position - sampleWorldPos));
            float difference = sampleWorldPos.z - (position.z + bias);

            // 如果采样点比当前点更靠近相机，则产生遮蔽
            if (difference > 0.0 && difference < radius) {
              // 精细步骤，提高精度
              if (refineSteps > 0) {
                vec3 refinedPos = samplePos;
                float refinedStepSize = radius / float(refineSteps);

                for (int j = 0; j < 8; j++) {
                  if (j >= refineSteps) break;

                  refinedPos -= sampleVec * refinedStepSize;
                  vec2 refinedUV = worldToScreen(refinedPos);

                  if (refinedUV.x < 0.0 || refinedUV.x > 1.0 || refinedUV.y < 0.0 || refinedUV.y > 1.0) {
                    continue;
                  }

                  float refinedDepth = getDepth(refinedUV);
                  vec3 refinedSamplePos = screenToWorld(refinedUV, refinedDepth);
                  float refinedDifference = refinedSamplePos.z - (position.z + bias);

                  if (refinedDifference < 0.0) {
                    break;
                  }

                  if (refinedDifference < radius) {
                    samplePos = refinedPos;
                    sampleUV = refinedUV;
                    difference = refinedDifference;
                    break;
                  }
                }
              }

              occlusion += (1.0 - smoothstep(bias, radius, difference)) * rangeCheck;
            }
          }

          // 归一化遮蔽值
          occlusion = 1.0 - (occlusion / float(sampleCount));

          // 应用衰减
          occlusion = pow(occlusion, falloff);

          return occlusion;
        }

        // 去噪函数
        vec4 denoise(vec2 uv, vec4 color) {
          if (denoiseIterations <= 0) return color;

          vec4 result = color;
          float totalWeight = 1.0;

          // 获取中心像素的属性
          float centerDepth = getDepth(uv);
          vec3 centerNormal = getNormal(uv);
          float centerLuma = dot(color.rgb, vec3(0.299, 0.587, 0.114));

          // 多次迭代去噪
          for (int iter = 0; iter < 3; iter++) {
            if (iter >= denoiseIterations) break;

            // 在每次迭代中使用不同的核大小
            float kernelSize = float(denoiseKernel) * (1.0 - float(iter) / float(denoiseIterations));

            // 采样周围像素
            for (int y = -3; y <= 3; y++) {
              if (y < -denoiseKernel || y > denoiseKernel) continue;

              for (int x = -3; x <= 3; x++) {
                if (x < -denoiseKernel || x > denoiseKernel) continue;
                if (x == 0 && y == 0) continue;

                vec2 offset = vec2(float(x), float(y)) / resolution * kernelSize;
                vec2 sampleUV = uv + offset;

                // 检查采样点是否在屏幕内
                if (sampleUV.x < 0.0 || sampleUV.x > 1.0 || sampleUV.y < 0.0 || sampleUV.y > 1.0) {
                  continue;
                }

                // 获取采样点的属性
                vec4 sampleColor = texture2D(tDiffuse, sampleUV);
                float sampleDepth = getDepth(sampleUV);
                vec3 sampleNormal = getNormal(sampleUV);
                float sampleLuma = dot(sampleColor.rgb, vec3(0.299, 0.587, 0.114));

                // 计算各种差异
                float depthDiff = abs(centerDepth - sampleDepth);
                float normalDiff = 1.0 - dot(centerNormal, sampleNormal);
                float lumaDiff = abs(centerLuma - sampleLuma);

                // 计算权重
                float w_depth = exp(-depthDiff * depthPhi);
                float w_normal = exp(-normalDiff * normalPhi);
                float w_luma = exp(-lumaDiff * lumaPhi);

                // 组合权重
                float weight = w_depth * w_normal * w_luma;

                // 累加结果
                result += sampleColor * weight;
                totalWeight += weight;
              }
            }
          }

          // 归一化结果
          if (totalWeight > 0.0) {
            result /= totalWeight;
          }

          return result;
        }

        void main() {
          // 获取原始颜色
          vec4 diffuse = texture2D(tDiffuse, vUv);

          // 获取深度
          float depth = getDepth(vUv);

          // 如果是背景，则不计算SSGI
          if (depth >= 1.0) {
            gl_FragColor = diffuse;
            return;
          }

          // 获取世界空间位置
          vec3 worldPos = screenToWorld(vUv, depth);

          // 获取法线
          vec3 normal = getNormal(vUv);

          // 计算环境光遮蔽
          float ao = computeAO(worldPos, normal, radius, bias);

          // 应用环境光遮蔽
          vec4 aoColor = vec4(diffuse.rgb * mix(1.0, ao, intensity), diffuse.a);

          // 应用去噪
          vec4 finalColor = denoise(vUv, aoColor);

          gl_FragColor = finalColor;
        }
      `
    };

    // 创建SSGI通道
    this.ssgiPass = new ShaderPass(ssgiShader);

    // 设置通道
    this.pass = this.ssgiPass;
  }

  /**
   * 设置场景和相机
   * @param scene 场景
   * @param camera 相机
   */
  public setSceneAndCamera(scene: THREE.Scene, camera: THREE.PerspectiveCamera | THREE.OrthographicCamera): void {
    this.scene = scene;
    this.camera = camera;

    // 如果已初始化，则重新创建通道
    if (this.initialized) {
      this.createPass();
    }
  }

  /**
   * 设置法线纹理
   * @param texture 法线纹理
   */
  public setNormalTexture(texture: THREE.Texture): void {
    this.normalTexture = texture;

    // 更新通道的法线纹理
    if (this.ssgiPass && this.ssgiPass.uniforms.tNormal) {
      this.ssgiPass.uniforms.tNormal.value = texture;
    }
  }

  /**
   * 设置强度
   * @param intensity 强度
   */
  public setIntensity(intensity: number): void {
    this.intensity = intensity;

    // 更新通道的强度
    if (this.ssgiPass && this.ssgiPass.uniforms.intensity) {
      this.ssgiPass.uniforms.intensity.value = intensity;
    }
  }

  /**
   * 获取强度
   * @returns 强度
   */
  public getIntensity(): number {
    return this.intensity;
  }

  /**
   * 设置半径
   * @param radius 半径
   */
  public setRadius(radius: number): void {
    this.radius = radius;

    // 更新通道的半径
    if (this.ssgiPass && this.ssgiPass.uniforms.radius) {
      this.ssgiPass.uniforms.radius.value = radius;
    }
  }

  /**
   * 获取半径
   * @returns 半径
   */
  public getRadius(): number {
    return this.radius;
  }

  /**
   * 设置偏移
   * @param bias 偏移
   */
  public setBias(bias: number): void {
    this.bias = bias;

    // 更新通道的偏移
    if (this.ssgiPass && this.ssgiPass.uniforms.bias) {
      this.ssgiPass.uniforms.bias.value = bias;
    }
  }

  /**
   * 获取偏移
   * @returns 偏移
   */
  public getBias(): number {
    return this.bias;
  }

  /**
   * 设置采样数
   * @param samples 采样数
   */
  public setSamples(samples: number): void {
    this.samples = samples;

    // 更新通道的采样数
    if (this.ssgiPass && this.ssgiPass.uniforms.samples) {
      this.ssgiPass.uniforms.samples.value = samples;
    }
  }

  /**
   * 获取采样数
   * @returns 采样数
   */
  public getSamples(): number {
    return this.samples;
  }

  /**
   * 设置衰减
   * @param falloff 衰减
   */
  public setFalloff(falloff: number): void {
    this.falloff = falloff;

    // 更新通道的衰减
    if (this.ssgiPass && this.ssgiPass.uniforms.falloff) {
      this.ssgiPass.uniforms.falloff.value = falloff;
    }
  }

  /**
   * 获取衰减
   * @returns 衰减
   */
  public getFalloff(): number {
    return this.falloff;
  }

  /**
   * 设置是否使用法线贴图
   * @param use 是否使用
   */
  public setUseNormalMap(use: boolean): void {
    this.useNormalMap = use;

    // 更新通道的法线贴图使用标志
    if (this.ssgiPass && this.ssgiPass.uniforms.useNormalMap) {
      this.ssgiPass.uniforms.useNormalMap.value = use ? 1 : 0;
    }
  }

  /**
   * 获取是否使用法线贴图
   * @returns 是否使用
   */
  public isUseNormalMap(): boolean {
    return this.useNormalMap;
  }

  /**
   * 设置是否使用深度贴图
   * @param use 是否使用
   */
  public setUseDepthMap(use: boolean): void {
    this.useDepthMap = use;

    // 更新通道的深度贴图使用标志
    if (this.ssgiPass && this.ssgiPass.uniforms.useDepthMap) {
      this.ssgiPass.uniforms.useDepthMap.value = use ? 1 : 0;
    }
  }

  /**
   * 获取是否使用深度贴图
   * @returns 是否使用
   */
  public isUseDepthMap(): boolean {
    return this.useDepthMap;
  }

  /**
   * 更新效果
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.camera || !this.ssgiPass) return;

    // 更新相机相关的Uniforms
    const uniforms = this.ssgiPass.uniforms;
    if (uniforms.cameraNear) uniforms.cameraNear.value = this.camera.near;
    if (uniforms.cameraFar) uniforms.cameraFar.value = this.camera.far;
    if (uniforms.cameraProjectionMatrix) uniforms.cameraProjectionMatrix.value = this.camera.projectionMatrix;
    if (uniforms.cameraInverseProjectionMatrix) {
      uniforms.cameraInverseProjectionMatrix.value.copy(this.camera.projectionMatrix).invert();
    }
    if (uniforms.cameraWorldMatrix) uniforms.cameraWorldMatrix.value = this.camera.matrixWorld;

    // 更新时间，用于随机化采样
    if (uniforms.time) {
      uniforms.time.value = (uniforms.time.value || 0) + deltaTime;
    }
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    super.resize(width, height);

    // 更新渲染目标大小
    if (this.renderTarget) {
      this.renderTarget.setSize(width, height);
    }

    // 更新分辨率Uniform
    if (this.ssgiPass && this.ssgiPass.uniforms.resolution) {
      this.ssgiPass.uniforms.resolution.value.set(width, height);
    }
  }

  /**
   * 销毁效果
   */
  public dispose(): void {
    // 销毁渲染目标
    if (this.renderTarget) {
      (this.renderTarget as any).dispose();
      this.renderTarget = null;
    }

    // 销毁深度纹理
    if (this.depthTexture) {
      (this.depthTexture as any).dispose();
      this.depthTexture = null;
    }

    super.dispose();
  }
}
