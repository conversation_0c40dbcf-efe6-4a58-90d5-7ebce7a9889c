/**
 * 地下环境光照系统
 * 用于增强地下环境的光照效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
/**
 * 光束类型
 */
export declare enum LightShaftType {
    /** 体积光 */
    VOLUMETRIC = "volumetric",
    /** 光线 */
    RAY = "ray",
    /** 聚光 */
    SPOT = "spot",
    /** 环境光 */
    AMBIENT = "ambient"
}
/**
 * 光束配置
 */
export interface LightShaftConfig {
    /** 光束类型 */
    type: LightShaftType;
    /** 位置 */
    position: THREE.Vector3;
    /** 方向 */
    direction?: THREE.Vector3;
    /** 颜色 */
    color?: THREE.Color | number | string;
    /** 强度 */
    intensity?: number;
    /** 衰减 */
    decay?: number;
    /** 距离 */
    distance?: number;
    /** 角度 */
    angle?: number;
    /** 半影 */
    penumbra?: number;
    /** 体积密度 */
    density?: number;
    /** 散射系数 */
    scattering?: number;
    /** 采样数 */
    samples?: number;
    /** 是否投射阴影 */
    castShadow?: boolean;
    /** 阴影贴图大小 */
    shadowMapSize?: number;
    /** 是否启用动态效果 */
    enableDynamicEffect?: boolean;
    /** 动态效果参数 */
    dynamicEffect?: {
        /** 强度变化 */
        intensityVariation?: number;
        /** 颜色变化 */
        colorVariation?: number;
        /** 方向变化 */
        directionVariation?: number;
        /** 变化速度 */
        speed?: number;
    };
}
/**
 * 地下环境光照系统配置
 */
export interface UndergroundLightingSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否启用后处理 */
    enablePostProcessing?: boolean;
    /** 是否启用阴影 */
    enableShadows?: boolean;
    /** 是否启用体积光 */
    enableVolumetricLighting?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 地下环境光照系统
 */
export declare class UndergroundLightingSystem extends System {
    /** 配置 */
    private config;
    /** 光束映射 */
    private lightShafts;
    /** 场景 */
    private scene;
    /** 相机 */
    private camera;
    /** 后处理渲染器 */
    private composer;
    /** 体积光着色器 */
    private volumetricLightShader;
    /** 是否初始化 */
    private initialized;
    /** 时间 */
    private time;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: UndergroundLightingSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 设置渲染环境
     * @param scene 场景
     * @param camera 相机
     * @param renderer 渲染器
     */
    setRenderEnvironment(scene: THREE.Scene, camera: THREE.Camera, renderer: THREE.WebGLRenderer): void;
    /**
     * 初始化体积光着色器
     */
    private initializeVolumetricLightShader;
    /**
     * 初始化后处理
     * @param _renderer 渲染器
     */
    private initializePostProcessing;
    /**
     * 添加光束
     * @param id 光束ID
     * @param config 光束配置
     */
    addLightShaft(id: string, config: LightShaftConfig): void;
    /**
     * 移除光束
     * @param id 光束ID
     */
    removeLightShaft(id: string): void;
    /**
     * 更新光束
     * @param id 光束ID
     * @param config 光束配置
     */
    updateLightShaft(id: string, config: Partial<LightShaftConfig>): void;
    /**
     * 更新
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 更新光束动态效果
     * @param _id 光束ID
     * @param lightShaft 光束对象
     * @param _deltaTime 时间增量
     */
    private updateLightShaftDynamicEffects;
    /**
     * 渲染
     * @param _renderer 渲染器
     */
    render(_renderer: THREE.WebGLRenderer): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
}
