/**
 * 子片段
 * 用于从动画片段中提取子片段
 */
import * as THREE from 'three';
/**
 * 子片段事件类型
 */
export declare enum SubClipEventType {
    /** 子片段创建 */
    CREATED = "created",
    /** 子片段更新 */
    UPDATED = "updated"
}
/**
 * 子片段配置
 */
export interface SubClipConfig {
    /** 子片段名称 */
    name?: string;
    /** 源片段名称 */
    sourceName?: string;
    /** 开始时间（秒） */
    startTime?: number;
    /** 结束时间（秒） */
    endTime?: number;
    /** 是否循环 */
    loop?: boolean;
    /** 是否反向播放 */
    reverse?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 子片段
 * 用于从动画片段中提取子片段
 */
export declare class SubClip {
    /** 子片段名称 */
    private name;
    /** 源片段名称 */
    private sourceName;
    /** 开始时间（秒） */
    private startTime;
    /** 结束时间（秒） */
    private endTime;
    /** 是否循环 */
    private loop;
    /** 是否反向播放 */
    private reverse;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否启用调试 */
    private debug;
    /**
     * 创建子片段
     * @param config 配置
     */
    constructor(config?: SubClipConfig);
    /**
     * 获取子片段名称
     * @returns 子片段名称
     */
    getName(): string;
    /**
     * 设置子片段名称
     * @param name 子片段名称
     */
    setName(name: string): void;
    /**
     * 获取源片段名称
     * @returns 源片段名称
     */
    getSourceName(): string;
    /**
     * 设置源片段名称
     * @param sourceName 源片段名称
     */
    setSourceName(sourceName: string): void;
    /**
     * 获取开始时间
     * @returns 开始时间（秒）
     */
    getStartTime(): number;
    /**
     * 设置开始时间
     * @param startTime 开始时间（秒）
     */
    setStartTime(startTime: number): void;
    /**
     * 获取结束时间
     * @returns 结束时间（秒）
     */
    getEndTime(): number;
    /**
     * 设置结束时间
     * @param endTime 结束时间（秒）
     */
    setEndTime(endTime: number): void;
    /**
     * 获取持续时间
     * @returns 持续时间（秒）
     */
    getDuration(): number;
    /**
     * 是否循环
     * @returns 是否循环
     */
    isLoop(): boolean;
    /**
     * 设置是否循环
     * @param loop 是否循环
     */
    setLoop(loop: boolean): void;
    /**
     * 是否反向播放
     * @returns 是否反向播放
     */
    isReverse(): boolean;
    /**
     * 设置是否反向播放
     * @param reverse 是否反向播放
     */
    setReverse(reverse: boolean): void;
    /**
     * 从源片段创建子片段
     * @param sourceClip 源片段
     * @returns 子片段
     */
    createFromClip(sourceClip: THREE.AnimationClip): THREE.AnimationClip;
    /**
     * 反转片段
     * @param clip 动画片段
     */
    private reverseClip;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: SubClipEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: SubClipEventType, listener: (data: any) => void): void;
    /**
     * 创建动作子片段
     * @param sourceClip 源片段
     * @param actionName 动作名称
     * @param startTime 开始时间（秒）
     * @param endTime 结束时间（秒）
     * @param loop 是否循环
     * @param debug 是否启用调试
     * @returns 子片段
     */
    static createActionSubClip(sourceClip: THREE.AnimationClip, actionName: string, startTime: number, endTime: number, loop?: boolean, debug?: boolean): THREE.AnimationClip;
    /**
     * 创建循环子片段
     * @param sourceClip 源片段
     * @param loopStartTime 循环开始时间（秒）
     * @param loopEndTime 循环结束时间（秒）
     * @param debug 是否启用调试
     * @returns 子片段
     */
    static createLoopSubClip(sourceClip: THREE.AnimationClip, loopStartTime: number, loopEndTime: number, debug?: boolean): THREE.AnimationClip;
    /**
     * 创建入场子片段
     * @param sourceClip 源片段
     * @param entryEndTime 入场结束时间（秒）
     * @param debug 是否启用调试
     * @returns 子片段
     */
    static createEntrySubClip(sourceClip: THREE.AnimationClip, entryEndTime: number, debug?: boolean): THREE.AnimationClip;
    /**
     * 创建退场子片段
     * @param sourceClip 源片段
     * @param exitStartTime 退场开始时间（秒）
     * @param debug 是否启用调试
     * @returns 子片段
     */
    static createExitSubClip(sourceClip: THREE.AnimationClip, exitStartTime: number, debug?: boolean): THREE.AnimationClip;
}
