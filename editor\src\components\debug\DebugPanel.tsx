/**
 * 调试面板组件
 * 集成各种调试工具
 */
import React, { useState } from 'react';
import { Tabs } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  DashboardOutlined,
  AreaChartOutlined,
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  <PERSON>boltOutlined,
  BulbOutlined,
  RocketOutlined,
  DesktopOutlined,
  AppstoreOutlined} from '@ant-design/icons';
import PerformancePanel from './PerformancePanel';
import PerformanceComparisonPanel from './PerformanceComparisonPanel';
import PerformanceTestAutomationPanel from './PerformanceTestAutomationPanel';
import MemoryAnalysisPanel from './MemoryAnalysisPanel';
import RenderingAnalysisPanel from './RenderingAnalysisPanel';
import SceneOptimizationPanel from './SceneOptimizationPanel';
import PerformanceOptimizationPanel from './PerformanceOptimizationPanel';
import ResourceOptimizationPanel from './ResourceOptimizationPanel';
import UIOptimizationPanel from './UIOptimizationPanel';
import './DebugPanel.less';



interface DebugPanelProps {
  className?: string;
}

/**
 * 调试面板组件
 */
const DebugPanel: React.FC<DebugPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('performance');

  const tabItems = [
    {
      key: 'performance',
      label: (
        <span>
          <DashboardOutlined />
          {t('debug.tabs.performance')}
        </span>
      ),
      children: <PerformancePanel />
    },
    {
      key: 'performanceComparison',
      label: (
        <span>
          <LineChartOutlined />
          {t('debug.tabs.performanceComparison')}
        </span>
      ),
      children: <PerformanceComparisonPanel />
    },
    {
      key: 'performanceTest',
      label: (
        <span>
          <ThunderboltOutlined />
          {t('debug.tabs.performanceTest')}
        </span>
      ),
      children: <PerformanceTestAutomationPanel />
    },
    {
      key: 'memory',
      label: (
        <span>
          <AreaChartOutlined />
          {t('debug.tabs.memory')}
        </span>
      ),
      children: <MemoryAnalysisPanel />
    },
    {
      key: 'rendering',
      label: (
        <span>
          <BarChartOutlined />
          {t('debug.tabs.rendering')}
        </span>
      ),
      children: <RenderingAnalysisPanel />
    },
    {
      key: 'optimization',
      label: (
        <span>
          <BulbOutlined />
          {t('debug.tabs.optimization')}
        </span>
      ),
      children: <SceneOptimizationPanel />
    },
    {
      key: 'performanceOptimization',
      label: (
        <span>
          <RocketOutlined />
          {t('debug.tabs.performanceOptimization')}
        </span>
      ),
      children: <PerformanceOptimizationPanel />
    },
    {
      key: 'resourceOptimization',
      label: (
        <span>
          <AppstoreOutlined />
          {t('debug.tabs.resourceOptimization')}
        </span>
      ),
      children: <ResourceOptimizationPanel />
    },
    {
      key: 'uiOptimization',
      label: (
        <span>
          <DesktopOutlined />
          {t('debug.tabs.uiOptimization')}
        </span>
      ),
      children: <UIOptimizationPanel />
    }
  ];

  return (
    <div className={`debug-panel ${className || ''}`}>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
        items={tabItems}
      />
    </div>
  );
};

export default DebugPanel;
