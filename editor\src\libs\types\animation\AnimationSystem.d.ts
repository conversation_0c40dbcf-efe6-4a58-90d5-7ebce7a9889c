/**
 * 动画系统
 * 管理和更新场景中的所有动画
 */
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { AnimationClip } from './AnimationClip';
import { Animator } from './Animator';
/**
 * 动画系统配置
 */
export interface AnimationSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
    /** 更新频率（毫秒） */
    updateFrequency?: number;
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存大小 */
    cacheSize?: number;
    /** 是否使用对象池 */
    useObjectPool?: boolean;
    /** 对象池大小 */
    objectPoolSize?: number;
    /** 是否使用批处理 */
    useBatchProcessing?: boolean;
    /** 批处理大小 */
    batchSize?: number;
    /** 是否使用GPU加速 */
    useGPUAcceleration?: boolean;
    /** 是否使用工作线程 */
    useWorker?: boolean;
}
/**
 * 动画系统
 * 管理和更新场景中的所有动画
 */
export declare class AnimationSystem extends System {
    /** 系统名称 */
    static readonly systemName = "AnimationSystem";
    /** 配置 */
    private config;
    /** 动画控制器映射 */
    private animators;
    /** 动画混合器映射 */
    private mixers;
    /** 事件发射器 */
    private eventEmitter;
    /** 上次更新时间 */
    private lastUpdateTime;
    /** 动画缓存 */
    private clipCache;
    /** 对象池 */
    private objectPool;
    /** 性能统计 */
    private stats;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: AnimationSystemConfig);
    /**
     * 初始化对象池
     */
    private initObjectPool;
    /**
     * 创建动画控制器
     * @param entity 实体
     * @param clips 动画片段
     * @returns 动画控制器
     */
    createAnimator(entity: Entity, clips?: AnimationClip[]): Animator;
    /**
     * 获取动画控制器
     * @param entity 实体
     * @returns 动画控制器
     */
    getAnimator(entity: Entity): Animator | null;
    /**
     * 移除动画控制器
     * @param entity 实体
     */
    removeAnimator(entity: Entity): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 获取性能统计
     * @returns 性能统计
     */
    getStats(): any;
    /**
     * 清除缓存
     */
    clearCache(): void;
    /**
     * 重置对象池
     */
    resetObjectPool(): void;
}
