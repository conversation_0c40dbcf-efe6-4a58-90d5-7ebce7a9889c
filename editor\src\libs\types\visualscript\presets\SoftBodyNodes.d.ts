import { FlowNode } from '../nodes/FlowNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 创建布料节点
 * 创建布料软体
 */
export declare class CreateClothNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 创建绳索节点
 * 创建绳索软体
 */
export declare class CreateRopeNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 创建气球节点
 * 创建气球软体
 */
export declare class CreateBalloonNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 创建果冻节点
 * 创建果冻软体
 */
export declare class CreateJellyNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 软体切割节点
 * 切割软体
 */
export declare class CutSoftBodyNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 注册软体物理节点
 * @param registry 节点注册表
 */
export declare function registerSoftBodyNodes(registry: NodeRegistry): void;
