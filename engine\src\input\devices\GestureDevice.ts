/**
 * 手势识别设备
 * 用于识别用户的手势输入
 */
import { BaseInputDevice } from '../InputDevice';

/**
 * 手势类型
 */
export enum GestureType {
  /** 点击 */
  TAP = 'tap',
  /** 双击 */
  DOUBLE_TAP = 'doubleTap',
  /** 长按 */
  LONG_PRESS = 'longPress',
  /** 滑动 */
  SWIPE = 'swipe',
  /** 捏合 */
  PINCH = 'pinch',
  /** 旋转 */
  ROTATE = 'rotate',
  /** 平移 */
  PAN = 'pan'
}

/**
 * 手势方向
 */
export enum GestureDirection {
  /** 无方向 */
  NONE = 'none',
  /** 上 */
  UP = 'up',
  /** 下 */
  DOWN = 'down',
  /** 左 */
  LEFT = 'left',
  /** 右 */
  RIGHT = 'right'
}

/**
 * 手势状态
 */
export enum GestureState {
  /** 开始 */
  BEGIN = 'begin',
  /** 更新 */
  UPDATE = 'update',
  /** 结束 */
  END = 'end'
}

/**
 * 手势事件数据
 */
export interface GestureEventData {
  /** 手势类型 */
  type: GestureType;
  /** 手势状态 */
  state: GestureState;
  /** 手势方向 */
  direction: GestureDirection;
  /** 手势位置X */
  x: number;
  /** 手势位置Y */
  y: number;
  /** 手势缩放比例（捏合手势） */
  scale?: number;
  /** 手势旋转角度（旋转手势） */
  rotation?: number;
  /** 手势速度 */
  velocity?: number;
  /** 手势持续时间（毫秒） */
  duration?: number;
  /** 原始事件 */
  originalEvent?: Event;
}

/**
 * 手势识别器选项
 */
export interface GestureRecognizerOptions {
  /** 目标元素 */
  element?: HTMLElement;
  /** 是否阻止默认行为 */
  preventDefault?: boolean;
  /** 是否阻止事件传播 */
  stopPropagation?: boolean;
  /** 长按阈值（毫秒） */
  longPressThreshold?: number;
  /** 双击阈值（毫秒） */
  doubleTapThreshold?: number;
  /** 滑动阈值（像素） */
  swipeThreshold?: number;
  /** 滑动速度阈值（像素/毫秒） */
  swipeVelocityThreshold?: number;
}

/**
 * 手势识别设备
 */
export class GestureDevice extends BaseInputDevice {
  /** 目标元素 */
  private element: HTMLElement;

  /** 是否阻止默认行为 */
  private preventDefault: boolean;

  /** 是否阻止事件传播 */
  private stopPropagation: boolean;

  /** 长按阈值（毫秒） */
  private longPressThreshold: number;

  /** 双击阈值（毫秒） */
  private doubleTapThreshold: number;

  /** 滑动阈值（像素） */
  private swipeThreshold: number;

  /** 滑动速度阈值（像素/毫秒） */
  private swipeVelocityThreshold: number;

  /** 触摸事件处理器 */
  private touchEventHandlers: { [key: string]: (event: TouchEvent) => void } = {};

  /** 鼠标事件处理器 */
  private mouseEventHandlers: { [key: string]: (event: MouseEvent) => void } = {};

  /** 指针事件处理器 */
  private pointerEventHandlers: { [key: string]: (event: PointerEvent) => void } = {};

  /** 当前触摸点 */
  private touches: Touch[] = [];

  /** 上一次触摸点 */
  private previousTouches: Touch[] = [];

  /** 触摸开始时间 */
  private touchStartTime: number = 0;

  /** 上一次点击时间 */
  private lastTapTime: number = 0;

  /** 长按定时器 */
  private longPressTimer: number | null = null;

  /** 是否正在进行手势 */
  private isGesturing: boolean = false;

  /** 当前手势类型 */
  private currentGestureType: GestureType | null = null;

  /** 手势开始位置 */
  private gestureStartPosition: { x: number; y: number } = { x: 0, y: 0 };

  /** 手势当前位置 */
  private gestureCurrentPosition: { x: number; y: number } = { x: 0, y: 0 };

  /** 手势开始时间 */
  private gestureStartTime: number = 0;

  /** 手势缩放初始值 */
  private initialScale: number = 1;

  /** 手势旋转初始值 */
  private initialRotation: number = 0;

  /**
   * 创建手势识别设备
   * @param options 选项
   */
  constructor(options: GestureRecognizerOptions = {}) {
    super('gesture');

    this.element = options.element || document.body;
    this.preventDefault = options.preventDefault !== undefined ? options.preventDefault : true;
    this.stopPropagation = options.stopPropagation !== undefined ? options.stopPropagation : false;
    this.longPressThreshold = options.longPressThreshold || 500;
    this.doubleTapThreshold = options.doubleTapThreshold || 300;
    this.swipeThreshold = options.swipeThreshold || 50;
    this.swipeVelocityThreshold = options.swipeVelocityThreshold || 0.3;

    // 初始化事件处理器
    this.initEventHandlers();
  }

  /**
   * 初始化事件处理器
   */
  private initEventHandlers(): void {
    // 触摸事件处理器
    this.touchEventHandlers = {
      touchstart: this.handleTouchStart.bind(this),
      touchmove: this.handleTouchMove.bind(this),
      touchend: this.handleTouchEnd.bind(this),
      touchcancel: this.handleTouchCancel.bind(this)
    };

    // 鼠标事件处理器
    this.mouseEventHandlers = {
      mousedown: this.handleMouseDown.bind(this),
      mousemove: this.handleMouseMove.bind(this),
      mouseup: this.handleMouseUp.bind(this)
    };

    // 指针事件处理器
    this.pointerEventHandlers = {
      pointerdown: this.handlePointerDown.bind(this),
      pointermove: this.handlePointerMove.bind(this),
      pointerup: this.handlePointerUp.bind(this),
      pointercancel: this.handlePointerCancel.bind(this)
    };
  }

  /**
   * 初始化设备
   */
  public initialize(): void {
    if (this.initialized) return;

    // 添加事件监听器
    this.addEventListeners();

    super.initialize();
  }

  /**
   * 销毁设备
   */
  public destroy(): void {
    if (this.destroyed) return;

    // 移除事件监听器
    this.removeEventListeners();

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 移除触摸事件监听器
    for (const [event, handler] of Object.entries(this.touchEventHandlers)) {
      this.element.removeEventListener(event, handler as EventListener);
    }

    // 移除鼠标事件监听器
    for (const [event, handler] of Object.entries(this.mouseEventHandlers)) {
      this.element.removeEventListener(event, handler as EventListener);
    }

    // 移除指针事件监听器
    for (const [event, handler] of Object.entries(this.pointerEventHandlers)) {
      this.element.removeEventListener(event, handler as EventListener);
    }
  }

  /**
   * 处理触摸开始事件
   * @param event 触摸事件
   */
  private handleTouchStart(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 保存触摸点
    this.touches = Array.from(event.touches);
    this.previousTouches = [...this.touches];

    // 记录触摸开始时间
    this.touchStartTime = Date.now();

    // 记录手势开始位置和时间
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.gestureStartPosition = { x: touch.clientX, y: touch.clientY };
      this.gestureCurrentPosition = { x: touch.clientX, y: touch.clientY };
      this.gestureStartTime = Date.now();

      // 启动长按定时器
      this.startLongPressTimer(touch);
    } else if (event.touches.length === 2) {
      // 双指手势
      this.handleMultiTouchStart(event);
    }

    // 设置手势状态
    this.isGesturing = true;
  }

  /**
   * 处理触摸移动事件
   * @param event 触摸事件
   */
  private handleTouchMove(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 保存触摸点
    this.previousTouches = [...this.touches];
    this.touches = Array.from(event.touches);

    // 更新手势当前位置
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.gestureCurrentPosition = { x: touch.clientX, y: touch.clientY };

      // 检测滑动手势
      this.detectSwipeGesture();
    } else if (event.touches.length === 2) {
      // 处理双指手势
      this.handleMultiTouchMove(event);
    }

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  /**
   * 处理触摸结束事件
   * @param event 触摸事件
   */
  private handleTouchEnd(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 检测点击和双击手势
    if (this.previousTouches.length === 1 && event.touches.length === 0) {
      const touch = this.previousTouches[0];
      const touchEndTime = Date.now();
      const touchDuration = touchEndTime - this.touchStartTime;

      // 检测是否为点击手势
      if (touchDuration < this.longPressThreshold) {
        const dx = this.gestureCurrentPosition.x - this.gestureStartPosition.x;
        const dy = this.gestureCurrentPosition.y - this.gestureStartPosition.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 如果移动距离小于阈值，则认为是点击
        if (distance < this.swipeThreshold) {
          // 检测是否为双击
          const timeSinceLastTap = touchEndTime - this.lastTapTime;
          if (timeSinceLastTap < this.doubleTapThreshold) {
            // 双击手势
            this.triggerGestureEvent(GestureType.DOUBLE_TAP, GestureState.BEGIN, GestureDirection.NONE, touch.clientX, touch.clientY);
            this.triggerGestureEvent(GestureType.DOUBLE_TAP, GestureState.END, GestureDirection.NONE, touch.clientX, touch.clientY);
            this.lastTapTime = 0; // 重置双击时间
          } else {
            // 单击手势
            this.triggerGestureEvent(GestureType.TAP, GestureState.BEGIN, GestureDirection.NONE, touch.clientX, touch.clientY);
            this.triggerGestureEvent(GestureType.TAP, GestureState.END, GestureDirection.NONE, touch.clientX, touch.clientY);
            this.lastTapTime = touchEndTime;
          }
        }
      }
    } else if (this.previousTouches.length === 2 && (event.touches.length === 1 || event.touches.length === 0)) {
      // 结束双指手势
      this.handleMultiTouchEnd(event);
    }

    // 结束当前手势
    if (this.currentGestureType && this.isGesturing) {
      this.triggerGestureEvent(
        this.currentGestureType,
        GestureState.END,
        this.getSwipeDirection(),
        this.gestureCurrentPosition.x,
        this.gestureCurrentPosition.y
      );
    }

    // 重置手势状态
    this.isGesturing = false;
    this.currentGestureType = null;
    this.touches = Array.from(event.touches);
    this.previousTouches = [...this.touches];
  }

  /**
   * 处理触摸取消事件
   * @param event 触摸事件
   */
  private handleTouchCancel(event: TouchEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 结束当前手势
    if (this.currentGestureType && this.isGesturing) {
      this.triggerGestureEvent(
        this.currentGestureType,
        GestureState.END,
        this.getSwipeDirection(),
        this.gestureCurrentPosition.x,
        this.gestureCurrentPosition.y
      );
    }

    // 重置手势状态
    this.isGesturing = false;
    this.currentGestureType = null;
    this.touches = [];
    this.previousTouches = [];
  }

  /**
   * 处理鼠标按下事件
   * @param event 鼠标事件
   */
  private handleMouseDown(event: MouseEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 记录手势开始位置和时间
    this.gestureStartPosition = { x: event.clientX, y: event.clientY };
    this.gestureCurrentPosition = { x: event.clientX, y: event.clientY };
    this.gestureStartTime = Date.now();

    // 启动长按定时器
    this.startLongPressTimer({ clientX: event.clientX, clientY: event.clientY } as Touch);

    // 设置手势状态
    this.isGesturing = true;
  }

  /**
   * 处理鼠标移动事件
   * @param event 鼠标事件
   */
  private handleMouseMove(event: MouseEvent): void {
    // 如果没有按下鼠标，则忽略
    if (!this.isGesturing) return;

    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新手势当前位置
    this.gestureCurrentPosition = { x: event.clientX, y: event.clientY };

    // 检测滑动手势
    this.detectSwipeGesture();

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  /**
   * 处理鼠标释放事件
   * @param event 鼠标事件
   */
  private handleMouseUp(event: MouseEvent): void {
    // 如果没有按下鼠标，则忽略
    if (!this.isGesturing) return;

    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 检测点击和双击手势
    const mouseUpTime = Date.now();
    const mouseDuration = mouseUpTime - this.gestureStartTime;

    // 检测是否为点击手势
    if (mouseDuration < this.longPressThreshold) {
      const dx = this.gestureCurrentPosition.x - this.gestureStartPosition.x;
      const dy = this.gestureCurrentPosition.y - this.gestureStartPosition.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // 如果移动距离小于阈值，则认为是点击
      if (distance < this.swipeThreshold) {
        // 检测是否为双击
        const timeSinceLastTap = mouseUpTime - this.lastTapTime;
        if (timeSinceLastTap < this.doubleTapThreshold) {
          // 双击手势
          this.triggerGestureEvent(GestureType.DOUBLE_TAP, GestureState.BEGIN, GestureDirection.NONE, event.clientX, event.clientY);
          this.triggerGestureEvent(GestureType.DOUBLE_TAP, GestureState.END, GestureDirection.NONE, event.clientX, event.clientY);
          this.lastTapTime = 0; // 重置双击时间
        } else {
          // 单击手势
          this.triggerGestureEvent(GestureType.TAP, GestureState.BEGIN, GestureDirection.NONE, event.clientX, event.clientY);
          this.triggerGestureEvent(GestureType.TAP, GestureState.END, GestureDirection.NONE, event.clientX, event.clientY);
          this.lastTapTime = mouseUpTime;
        }
      }
    }

    // 结束当前手势
    if (this.currentGestureType) {
      this.triggerGestureEvent(
        this.currentGestureType,
        GestureState.END,
        this.getSwipeDirection(),
        event.clientX,
        event.clientY
      );
    }

    // 重置手势状态
    this.isGesturing = false;
    this.currentGestureType = null;
  }

  /**
   * 处理指针按下事件
   * @param event 指针事件
   */
  private handlePointerDown(event: PointerEvent): void {
    // 模拟鼠标事件处理
    this.handleMouseDown(event);
  }

  /**
   * 处理指针移动事件
   * @param event 指针事件
   */
  private handlePointerMove(event: PointerEvent): void {
    // 模拟鼠标事件处理
    this.handleMouseMove(event);
  }

  /**
   * 处理指针释放事件
   * @param event 指针事件
   */
  private handlePointerUp(event: PointerEvent): void {
    // 模拟鼠标事件处理
    this.handleMouseUp(event);
  }

  /**
   * 处理指针取消事件
   * @param event 指针事件
   */
  private handlePointerCancel(event: PointerEvent): void {
    // 如果没有按下指针，则忽略
    if (!this.isGesturing) return;

    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 结束当前手势
    if (this.currentGestureType) {
      this.triggerGestureEvent(
        this.currentGestureType,
        GestureState.END,
        this.getSwipeDirection(),
        event.clientX,
        event.clientY
      );
    }

    // 重置手势状态
    this.isGesturing = false;
    this.currentGestureType = null;
  }

  /**
   * 启动长按定时器
   * @param touch 触摸点
   */
  private startLongPressTimer(touch: Touch): void {
    // 清除现有定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
    }

    // 创建新定时器
    this.longPressTimer = window.setTimeout(() => {
      // 触发长按手势
      this.triggerGestureEvent(GestureType.LONG_PRESS, GestureState.BEGIN, GestureDirection.NONE, touch.clientX, touch.clientY);
      this.currentGestureType = GestureType.LONG_PRESS;
      this.longPressTimer = null;
    }, this.longPressThreshold);
  }

  /**
   * 检测滑动手势
   */
  private detectSwipeGesture(): void {
    const dx = this.gestureCurrentPosition.x - this.gestureStartPosition.x;
    const dy = this.gestureCurrentPosition.y - this.gestureStartPosition.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    const duration = Date.now() - this.gestureStartTime;
    const velocity = distance / duration;

    // 如果移动距离大于阈值，则认为是滑动
    if (distance > this.swipeThreshold) {
      // 如果当前没有手势或手势不是滑动，则开始滑动手势
      if (this.currentGestureType !== GestureType.SWIPE) {
        this.currentGestureType = GestureType.SWIPE;
        this.triggerGestureEvent(
          GestureType.SWIPE,
          GestureState.BEGIN,
          this.getSwipeDirection(),
          this.gestureCurrentPosition.x,
          this.gestureCurrentPosition.y,
          undefined,
          undefined,
          velocity
        );
      } else {
        // 更新滑动手势
        this.triggerGestureEvent(
          GestureType.SWIPE,
          GestureState.UPDATE,
          this.getSwipeDirection(),
          this.gestureCurrentPosition.x,
          this.gestureCurrentPosition.y,
          undefined,
          undefined,
          velocity
        );
      }
    }
  }

  /**
   * 获取滑动方向
   * @returns 滑动方向
   */
  private getSwipeDirection(): GestureDirection {
    const dx = this.gestureCurrentPosition.x - this.gestureStartPosition.x;
    const dy = this.gestureCurrentPosition.y - this.gestureStartPosition.y;
    const absDx = Math.abs(dx);
    const absDy = Math.abs(dy);

    // 如果水平移动大于垂直移动
    if (absDx > absDy) {
      return dx > 0 ? GestureDirection.RIGHT : GestureDirection.LEFT;
    } else if (absDy > absDx) {
      return dy > 0 ? GestureDirection.DOWN : GestureDirection.UP;
    }

    return GestureDirection.NONE;
  }

  /**
   * 处理多点触摸开始
   * @param event 触摸事件
   */
  private handleMultiTouchStart(event: TouchEvent): void {
    if (event.touches.length === 2) {
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];

      // 计算初始距离和角度
      const dx = touch2.clientX - touch1.clientX;
      const dy = touch2.clientY - touch1.clientY;
      this.initialScale = Math.sqrt(dx * dx + dy * dy);
      this.initialRotation = Math.atan2(dy, dx);

      // 设置手势中心点
      this.gestureStartPosition = {
        x: (touch1.clientX + touch2.clientX) / 2,
        y: (touch1.clientY + touch2.clientY) / 2
      };
      this.gestureCurrentPosition = { ...this.gestureStartPosition };
    }
  }

  /**
   * 处理多点触摸移动
   * @param event 触摸事件
   */
  private handleMultiTouchMove(event: TouchEvent): void {
    if (event.touches.length === 2) {
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];

      // 计算当前距离和角度
      const dx = touch2.clientX - touch1.clientX;
      const dy = touch2.clientY - touch1.clientY;
      const currentDistance = Math.sqrt(dx * dx + dy * dy);
      const currentRotation = Math.atan2(dy, dx);

      // 计算缩放比例和旋转角度
      const scale = currentDistance / this.initialScale;
      const rotation = currentRotation - this.initialRotation;

      // 更新手势中心点
      this.gestureCurrentPosition = {
        x: (touch1.clientX + touch2.clientX) / 2,
        y: (touch1.clientY + touch2.clientY) / 2
      };

      // 检测捏合手势
      if (Math.abs(scale - 1) > 0.1) {
        if (this.currentGestureType !== GestureType.PINCH) {
          this.currentGestureType = GestureType.PINCH;
          this.triggerGestureEvent(
            GestureType.PINCH,
            GestureState.BEGIN,
            GestureDirection.NONE,
            this.gestureCurrentPosition.x,
            this.gestureCurrentPosition.y,
            scale
          );
        } else {
          this.triggerGestureEvent(
            GestureType.PINCH,
            GestureState.UPDATE,
            GestureDirection.NONE,
            this.gestureCurrentPosition.x,
            this.gestureCurrentPosition.y,
            scale
          );
        }
      }

      // 检测旋转手势
      if (Math.abs(rotation) > 0.1) {
        if (this.currentGestureType !== GestureType.ROTATE) {
          this.currentGestureType = GestureType.ROTATE;
          this.triggerGestureEvent(
            GestureType.ROTATE,
            GestureState.BEGIN,
            GestureDirection.NONE,
            this.gestureCurrentPosition.x,
            this.gestureCurrentPosition.y,
            undefined,
            rotation
          );
        } else {
          this.triggerGestureEvent(
            GestureType.ROTATE,
            GestureState.UPDATE,
            GestureDirection.NONE,
            this.gestureCurrentPosition.x,
            this.gestureCurrentPosition.y,
            undefined,
            rotation
          );
        }
      }

      // 检测平移手势
      const panDx = this.gestureCurrentPosition.x - this.gestureStartPosition.x;
      const panDy = this.gestureCurrentPosition.y - this.gestureStartPosition.y;
      const panDistance = Math.sqrt(panDx * panDx + panDy * panDy);

      if (panDistance > this.swipeThreshold) {
        if (this.currentGestureType !== GestureType.PAN) {
          this.currentGestureType = GestureType.PAN;
          this.triggerGestureEvent(
            GestureType.PAN,
            GestureState.BEGIN,
            this.getSwipeDirection(),
            this.gestureCurrentPosition.x,
            this.gestureCurrentPosition.y
          );
        } else {
          this.triggerGestureEvent(
            GestureType.PAN,
            GestureState.UPDATE,
            this.getSwipeDirection(),
            this.gestureCurrentPosition.x,
            this.gestureCurrentPosition.y
          );
        }
      }
    }
  }

  /**
   * 处理多点触摸结束
   * @param event 触摸事件
   */
  private handleMultiTouchEnd(event: TouchEvent): void {
    // 结束当前手势
    if (this.currentGestureType) {
      this.triggerGestureEvent(
        this.currentGestureType,
        GestureState.END,
        GestureDirection.NONE,
        this.gestureCurrentPosition.x,
        this.gestureCurrentPosition.y
      );
    }
  }

  /**
   * 触发手势事件
   * @param type 手势类型
   * @param state 手势状态
   * @param direction 手势方向
   * @param x X坐标
   * @param y Y坐标
   * @param scale 缩放比例
   * @param rotation 旋转角度
   * @param velocity 速度
   * @param originalEvent 原始事件
   */
  private triggerGestureEvent(
    type: GestureType,
    state: GestureState,
    direction: GestureDirection,
    x: number,
    y: number,
    scale?: number,
    rotation?: number,
    velocity?: number,
    originalEvent?: Event
  ): void {
    // 创建事件数据
    const eventData: GestureEventData = {
      type,
      state,
      direction,
      x,
      y,
      scale,
      rotation,
      velocity,
      duration: Date.now() - this.gestureStartTime,
      originalEvent
    };

    // 更新设备值
    this.setValue('type', type);
    this.setValue('state', state);
    this.setValue('direction', direction);
    this.setValue('position', { x, y });
    if (scale !== undefined) this.setValue('scale', scale);
    if (rotation !== undefined) this.setValue('rotation', rotation);
    if (velocity !== undefined) this.setValue('velocity', velocity);

    // 触发事件
    this.eventEmitter.emit(`${type}:${state}`, eventData);
    this.eventEmitter.emit('gesture', eventData);
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 检测设备支持的事件类型
    if ('ontouchstart' in window) {
      // 触摸设备
      for (const [event, handler] of Object.entries(this.touchEventHandlers)) {
        this.element.addEventListener(event, handler as EventListener, { passive: !this.preventDefault });
      }
    } else if ('onpointerdown' in window) {
      // 支持指针事件的设备
      for (const [event, handler] of Object.entries(this.pointerEventHandlers)) {
        this.element.addEventListener(event, handler as EventListener, { passive: !this.preventDefault });
      }
    } else {
      // 鼠标设备
      for (const [event, handler] of Object.entries(this.mouseEventHandlers)) {
        this.element.addEventListener(event, handler as EventListener, { passive: !this.preventDefault });
      }
    }
  }
}
