/**
 * 限流器守卫
 */
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { RateLimiterService } from './rate-limiter.service';
import { RATE_LIMITER_METADATA, RateLimiterMetadata } from './rate-limiter.decorator';

/**
 * 限流器守卫
 * 用于在请求处理前进行限流检查
 */
@Injectable()
export class RateLimiterGuard implements CanActivate {
  private readonly logger = new Logger(RateLimiterGuard.name);

  /**
   * 创建限流器守卫
   * @param reflector 反射器
   * @param rateLimiterService 限流器服务
   */
  constructor(
    private readonly reflector: Reflector,
    private readonly rateLimiterService: RateLimiterService,
  ) {}

  /**
   * 检查请求是否可以通过
   * @param context 执行上下文
   */
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // 获取限流器元数据
    const metadata = this.getRateLimiterMetadata(context);
    if (!metadata) {
      return true;
    }

    const { name, options, keyGenerator } = metadata;
    const request = context.switchToHttp().getRequest();
    
    // 生成限流键
    const key = keyGenerator ? keyGenerator(request) : this.generateKey(request);
    
    // 获取或创建限流器
    const rateLimiter = this.rateLimiterService.getOrCreate(name, options);
    
    // 检查是否可以通过
    return this.checkRateLimit(rateLimiter, key);
  }

  /**
   * 检查是否可以通过限流
   * @param rateLimiter 限流器
   * @param key 键
   */
  private async checkRateLimit(rateLimiter: any, key: string): Promise<boolean> {
    try {
      const allowed = await rateLimiter.consume(key);
      
      if (!allowed) {
        throw new HttpException('Too Many Requests', HttpStatus.TOO_MANY_REQUESTS);
      }
      
      return true;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`限流检查失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 获取限流器元数据
   * @param context 执行上下文
   */
  private getRateLimiterMetadata(context: ExecutionContext): RateLimiterMetadata | undefined {
    // 首先检查方法级别的元数据
    const methodMetadata = this.reflector.get<RateLimiterMetadata>(
      RATE_LIMITER_METADATA,
      context.getHandler(),
    );

    if (methodMetadata) {
      return methodMetadata;
    }

    // 然后检查控制器级别的元数据
    return this.reflector.get<RateLimiterMetadata>(
      RATE_LIMITER_METADATA,
      context.getClass(),
    );
  }

  /**
   * 生成限流键
   * @param request 请求对象
   */
  private generateKey(request: any): string {
    // 默认使用IP地址作为键
    const ip = request.ip || request.connection?.remoteAddress || 'unknown';
    
    // 可以根据需要添加更多信息，如路径、方法等
    const path = request.path || request.url || '';
    const method = request.method || '';
    
    return `${ip}:${method}:${path}`;
  }
}
