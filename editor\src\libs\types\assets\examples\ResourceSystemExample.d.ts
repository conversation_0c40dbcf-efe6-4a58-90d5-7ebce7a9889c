/**
 * 资源管理系统示例
 */
export declare class ResourceSystemExample {
    /** 资源管理系统 */
    private resourceSystem;
    /** Three.js场景 */
    private scene;
    /** Three.js相机 */
    private camera;
    /** Three.js渲染器 */
    private renderer;
    /** 加载状态元素 */
    private loadingElement;
    /** 进度元素 */
    private progressElement;
    /** 状态元素 */
    private statusElement;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建资源管理系统示例实例
     */
    constructor();
    /**
     * 创建UI元素
     */
    private createUI;
    /**
     * 初始化
     */
    initialize(): void;
    /**
     * 设置事件监听器
     */
    private setupEventListeners;
    /**
     * 更新加载状态
     * @param text 状态文本
     */
    private updateLoading;
    /**
     * 更新进度
     * @param percent 进度百分比
     */
    private updateProgress;
    /**
     * 更新状态
     * @param text 状态文本
     */
    private updateStatus;
    /**
     * 窗口大小变化处理
     */
    private onWindowResize;
    /**
     * 加载基础资源
     */
    private loadBasicResources;
    /**
     * 使用基础资源
     */
    private useBasicResources;
    /**
     * 加载模型资源
     */
    private loadModelResources;
    /**
     * 使用模型资源
     */
    private useModelResources;
    /**
     * 清理缓存
     */
    private cleanupCache;
    /**
     * 显示统计信息
     */
    private showStats;
    /**
     * 格式化大小
     * @param bytes 字节数
     * @returns 格式化后的大小字符串
     */
    private formatSize;
    /**
     * 动画循环
     */
    private animate;
    /**
     * 销毁
     */
    dispose(): void;
}
