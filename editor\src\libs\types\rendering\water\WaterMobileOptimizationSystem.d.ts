/**
 * 水体移动设备优化系统
 * 用于优化水体系统在移动设备上的性能
 */
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
import { DevicePerformanceLevel } from '../../utils/DeviceCapabilities';
/**
 * 水体移动设备优化系统配置
 */
export interface WaterMobileOptimizationSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用调试可视化 */
    enableDebugVisualization?: boolean;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
    /** 是否启用自动性能优化 */
    enableAutoOptimization?: boolean;
    /** 是否启用电池优化 */
    enableBatteryOptimization?: boolean;
    /** 是否启用温度优化 */
    enableTemperatureOptimization?: boolean;
    /** 目标帧率 */
    targetFPS?: number;
    /** 最小可接受帧率 */
    minAcceptableFPS?: number;
    /** 默认性能级别 */
    defaultPerformanceLevel?: DevicePerformanceLevel;
    /** 低电量阈值（百分比） */
    lowBatteryThreshold?: number;
    /** 高温阈值（摄氏度） */
    highTemperatureThreshold?: number;
}
/**
 * 水体移动设备优化系统
 */
export declare class WaterMobileOptimizationSystem extends System {
    /** 配置 */
    private config;
    /** 水体组件映射 */
    private waterBodies;
    /** 水体物理系统 */
    private waterPhysicsSystem;
    /** 水体渲染器 */
    private waterRenderer;
    /** 水体分块系统 */
    private waterChunkSystem;
    /** 水体LOD系统 */
    private waterLODSystem;
    /** 水体表面渲染器 */
    private waterSurfaceRenderer;
    /** 水体实例化渲染器 */
    private waterInstancedRenderer;
    /** 水体光照系统 */
    private waterLightingSystem;
    /** 性能监控器 */
    private performanceMonitor;
    /** 设备能力 */
    private deviceCapabilities;
    /** 当前性能级别 */
    private currentPerformanceLevel;
    /** 帧计数器 */
    private frameCount;
    /** 监控数据 */
    private monitorData;
    /** 性能配置 */
    private performanceConfigs;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: WaterMobileOptimizationSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 查找水体相关系统
     */
    private findWaterSystems;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 更新监控数据
     */
    private updateMonitorData;
    /**
     * 根据性能调整性能级别
     */
    private adjustPerformance;
    /**
     * 根据电池电量调整性能级别
     */
    private adjustPerformanceByBattery;
    /**
     * 根据设备温度调整性能级别
     */
    private adjustPerformanceByTemperature;
    /**
     * 提高性能级别
     */
    private increasePerformanceLevel;
    /**
     * 降低性能级别
     */
    private decreasePerformanceLevel;
    /**
     * 设置性能级别
     * @param level 性能级别
     */
    setPerformanceLevel(level: DevicePerformanceLevel): void;
    /**
     * 应用性能配置
     * @param level 性能级别
     */
    private applyPerformanceConfig;
    /**
     * 更新水体组件以适应移动设备
     * @param waterBody 水体组件
     * @param level 性能级别
     */
    private updateWaterBodyForMobile;
    /**
     * 添加水体组件
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterBody(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体组件
     * @param entity 实体
     */
    removeWaterBody(entity: Entity): void;
    /**
     * 获取当前性能级别
     * @returns 当前性能级别
     */
    getCurrentPerformanceLevel(): DevicePerformanceLevel;
    /**
     * 获取监控数据
     * @returns 监控数据
     */
    getMonitorData(): any;
    /**
     * 获取性能配置
     * @param level 性能级别
     * @returns 性能配置
     */
    getPerformanceConfig(level: DevicePerformanceLevel): any;
    /**
     * 设置性能配置
     * @param level 性能级别
     * @param config 性能配置
     */
    setPerformanceConfig(level: DevicePerformanceLevel, config: any): void;
}
