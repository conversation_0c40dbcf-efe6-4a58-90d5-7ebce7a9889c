/**
 * 被抓取组件
 * 用于标记当前被抓取的对象
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Hand } from './GrabbableComponent';

/**
 * 被抓取组件配置
 */
export interface GrabbedComponentConfig {
  /** 抓取者 */
  grabber: Entity;
  /** 抓取手 */
  hand: Hand;
  /** 抓取偏移 */
  offset?: { x: number; y: number; z: number };
}

/**
 * 被抓取组件
 * 当一个实体被抓取时，会添加此组件
 */
export class GrabbedComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'GrabbedComponent';

  /** 抓取者 */
  private _grabber: Entity;

  /** 抓取手 */
  private _hand: Hand;

  /** 抓取偏移 */
  private _offset: { x: number; y: number; z: number };

  /** 抓取时间戳 */
  private _grabTime: number;

  /**
   * 构造函数
   * @param entity 关联的实体
   * @param config 组件配置
   */
  constructor(entity: Entity, config: GrabbedComponentConfig) {
    // 调用基类构造函数，传入组件类型名称
    super(GrabbedComponent.TYPE);

    // 设置实体引用
    this.setEntity(entity);

    // 初始化属性
    this._grabber = config.grabber;
    this._hand = config.hand;
    this._offset = config.offset || { x: 0, y: 0, z: 0 };
    this._grabTime = Date.now();
  }

  /**
   * 获取抓取者
   */
  get grabber(): Entity {
    return this._grabber;
  }

  /**
   * 获取抓取手
   */
  get hand(): Hand {
    return this._hand;
  }

  /**
   * 获取抓取偏移
   */
  get offset(): { x: number; y: number; z: number } {
    return this._offset;
  }

  /**
   * 设置抓取偏移
   */
  set offset(value: { x: number; y: number; z: number }) {
    this._offset = value;
  }

  /**
   * 获取抓取时间戳
   */
  get grabTime(): number {
    return this._grabTime;
  }

  /**
   * 获取抓取持续时间（毫秒）
   */
  getDuration(): number {
    return Date.now() - this._grabTime;
  }
}
