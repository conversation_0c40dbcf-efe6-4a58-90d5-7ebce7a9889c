/**
 * 熔断器拦截器
 */
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { CircuitBreakerService } from './circuit-breaker.service';
import { CIRCUIT_BREAKER_METADATA, CircuitBreakerMetadata } from './circuit-breaker.decorator';

/**
 * 熔断器拦截器
 * 拦截请求并应用熔断器
 */
@Injectable()
export class CircuitBreakerInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CircuitBreakerInterceptor.name);

  /**
   * 创建熔断器拦截器
   * @param reflector 反射器
   * @param circuitBreakerService 熔断器服务
   */
  constructor(
    private readonly reflector: Reflector,
    private readonly circuitBreakerService: CircuitBreakerService,
  ) {}

  /**
   * 拦截请求
   * @param context 执行上下文
   * @param next 下一个处理器
   */
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // 获取熔断器元数据
    const metadata = this.getCircuitBreakerMetadata(context);
    if (!metadata) {
      return next.handle();
    }

    const { name, options } = metadata;
    const circuitBreaker = this.circuitBreakerService.getOrCreate(name, options);

    // 检查熔断器状态
    if (circuitBreaker.getState() === 'open') {
      this.logger.warn(`熔断器 ${name} 处于开启状态，拒绝请求`);
      return throwError(() => new Error(`Circuit breaker ${name} is open`));
    }

    // 执行请求
    return next.handle().pipe(
      tap(() => {
        // 请求成功，记录成功
        circuitBreaker['handleSuccess']();
      }),
      catchError(error => {
        // 请求失败，记录失败
        circuitBreaker['handleFailure'](error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * 获取熔断器元数据
   * @param context 执行上下文
   */
  private getCircuitBreakerMetadata(context: ExecutionContext): CircuitBreakerMetadata | undefined {
    // 首先检查方法级别的元数据
    const methodMetadata = this.reflector.get<CircuitBreakerMetadata>(
      CIRCUIT_BREAKER_METADATA,
      context.getHandler(),
    );

    if (methodMetadata) {
      return methodMetadata;
    }

    // 然后检查控制器级别的元数据
    return this.reflector.get<CircuitBreakerMetadata>(
      CIRCUIT_BREAKER_METADATA,
      context.getClass(),
    );
  }
}
