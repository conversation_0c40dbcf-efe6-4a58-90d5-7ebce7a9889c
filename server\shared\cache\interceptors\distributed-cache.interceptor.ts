/**
 * 分布式缓存拦截器
 * 用于自动缓存控制器方法的结果
 */
import { Injectable, NestInterceptor, ExecutionContext, CallHandler, Inject } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, of } from 'rxjs';
import { Request } from 'express';
import { DistributedCacheService } from '../distributed-cache.service';

/**
 * 缓存TTL元数据键
 */
export const CACHE_TTL_METADATA = 'cache:ttl';

/**
 * 缓存键生成器元数据键
 */
export const CACHE_KEY_GENERATOR_METADATA = 'cache:key-generator';

/**
 * 缓存键生成器函数类型
 */
export type CacheKeyGenerator = (context: ExecutionContext) => string;

/**
 * 分布式缓存拦截器
 */
@Injectable()
export class DistributedCacheInterceptor implements NestInterceptor {
  /**
   * 构造函数
   * @param cacheService 分布式缓存服务
   * @param reflector 反射器
   */
  constructor(
    private readonly cacheService: DistributedCacheService,
    private readonly reflector: Reflector,
  ) {}

  /**
   * 拦截请求
   * @param context 执行上下文
   * @param next 下一个处理器
   */
  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    // 获取请求对象
    const request = context.switchToHttp().getRequest<Request>();

    // 如果是非GET请求，不进行缓存
    if (request.method !== 'GET') {
      return next.handle();
    }

    // 获取缓存键
    const cacheKey = this.getCacheKey(context);

    // 获取缓存TTL
    const ttl = this.reflector.get(CACHE_TTL_METADATA, context.getHandler());

    // 尝试从缓存获取数据
    try {
      return of(await this.cacheService.get(cacheKey, () => {
        return next.handle().toPromise();
      }, ttl));
    } catch (error) {
      // 如果获取缓存失败，直接执行处理器
      return next.handle();
    }
  }

  /**
   * 获取缓存键
   * @param context 执行上下文
   */
  private getCacheKey(context: ExecutionContext): string {
    // 获取自定义缓存键生成器
    const keyGenerator = this.reflector.get<CacheKeyGenerator>(
      CACHE_KEY_GENERATOR_METADATA,
      context.getHandler(),
    );

    if (keyGenerator) {
      return keyGenerator(context);
    }

    // 默认缓存键生成逻辑
    const request = context.switchToHttp().getRequest<Request>();
    const controller = context.getClass().name;
    const handler = context.getHandler().name;
    const url = request.originalUrl || request.url;
    const queryString = JSON.stringify(request.query);

    return `${controller}:${handler}:${url}:${queryString}`;
  }
}
