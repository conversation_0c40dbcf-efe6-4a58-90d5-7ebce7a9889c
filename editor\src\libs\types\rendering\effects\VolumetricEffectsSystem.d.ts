/**
 * 体积效果系统
 * 用于实现体积光和体积雾等效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { World } from '../../core/World';
/**
 * 体积效果系统配置
 */
export interface VolumetricEffectsSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用体积光 */
    enableVolumetricLight?: boolean;
    /** 是否启用体积雾 */
    enableVolumetricFog?: boolean;
    /** 是否启用调试可视化 */
    enableDebugVisualization?: boolean;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
}
/**
 * 体积光配置
 */
export interface VolumetricLightConfig {
    /** 位置 */
    position: THREE.Vector3;
    /** 方向 */
    direction: THREE.Vector3;
    /** 颜色 */
    color: THREE.Color;
    /** 强度 */
    intensity: number;
    /** 衰减 */
    decay: number;
    /** 长度 */
    length: number;
    /** 角度 */
    angle: number;
    /** 密度 */
    density: number;
}
/**
 * 体积雾配置
 */
export interface VolumetricFogConfig {
    /** 位置 */
    position: THREE.Vector3;
    /** 大小 */
    size: THREE.Vector3;
    /** 颜色 */
    color: THREE.Color;
    /** 密度 */
    density: number;
    /** 是否启用噪声 */
    enableNoise?: boolean;
    /** 噪声尺度 */
    noiseScale?: number;
    /** 噪声速度 */
    noiseSpeed?: number;
}
/**
 * 体积效果系统
 */
export declare class VolumetricEffectsSystem extends System {
    /** 配置 */
    private config;
    /** 体积光列表 */
    private volumetricLights;
    /** 体积雾列表 */
    private volumetricFogs;
    /** 帧计数器 */
    private frameCount;
    /** 性能监视器 */
    private performanceMonitor;
    /** 调试渲染器 */
    private debugRenderer;
    /** 时间 */
    private time;
    /** 体积光材质 */
    private volumetricLightMaterial;
    /** 体积雾材质 */
    private volumetricFogMaterial;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: VolumetricEffectsSystemConfig);
    /**
     * 创建材质
     */
    private createMaterials;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 创建调试渲染器
     */
    private createDebugRenderer;
    /**
     * 更新调试渲染器
     */
    private updateDebugRenderer;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加体积光
     * @param id 体积光ID
     * @param config 体积光配置
     * @returns 体积光ID
     */
    addVolumetricLight(id: string, config: VolumetricLightConfig): string;
    /**
     * 移除体积光
     * @param id 体积光ID
     */
    removeVolumetricLight(id: string): void;
    /**
     * 更新体积光
     * @param _deltaTime 帧间隔时间（秒）
     */
    private updateVolumetricLights;
    /**
     * 添加体积雾
     * @param id 体积雾ID
     * @param config 体积雾配置
     * @returns 体积雾ID
     */
    addVolumetricFog(id: string, config: VolumetricFogConfig): string;
    /**
     * 移除体积雾
     * @param id 体积雾ID
     */
    removeVolumetricFog(id: string): void;
    /**
     * 更新体积雾
     * @param _deltaTime 帧间隔时间（秒）
     */
    private updateVolumetricFogs;
    /**
     * 销毁系统
     */
    dispose(): void;
}
