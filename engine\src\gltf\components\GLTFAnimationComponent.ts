/**
 * GLTF动画组件
 * 用于管理GLTF模型的动画
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 动画状态
 */
export enum AnimationState {
  STOPPED = 'stopped',
  PLAYING = 'playing',
  PAUSED = 'paused',
  FINISHED = 'finished'
}

/**
 * 动画循环模式
 */
export enum AnimationLoopMode {
  ONCE = THREE.LoopOnce,
  REPEAT = THREE.LoopRepeat,
  PINGPONG = THREE.LoopPingPong
}

/**
 * 动画混合模式
 */
export enum AnimationBlendMode {
  NORMAL = 'normal',
  ADDITIVE = 'additive'
}

/**
 * 动画事件类型
 */
export enum AnimationEventType {
  START = 'start',
  STOP = 'stop',
  PAUSE = 'pause',
  RESUME = 'resume',
  LOOP = 'loop',
  FINISHED = 'finished',
  TIME_UPDATE = 'timeUpdate'
}

/**
 * GLTF动画组件
 */
export class GLTFAnimationComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'GLTFAnimationComponent';

  /** 动画剪辑 */
  private clips: THREE.AnimationClip[];

  /** 动画混合器 */
  private mixer: THREE.AnimationMixer | null = null;

  /** 活跃动作 */
  private actions: Map<string, THREE.AnimationAction> = new Map();

  /** 当前状态 */
  private state: AnimationState = AnimationState.STOPPED;

  /** 事件发射器 */
  private emitter: EventEmitter = new EventEmitter();

  /** 时间比例 */
  private timeScale: number = 1.0;



  /** 默认循环模式 */
  private defaultLoopMode: AnimationLoopMode = AnimationLoopMode.REPEAT;

  /** 默认混合模式 */
  private defaultBlendMode: AnimationBlendMode = AnimationBlendMode.NORMAL;

  /** 默认交叉淡入时间 */
  private defaultCrossFadeTime: number = 0.3;

  /**
   * 创建GLTF动画组件
   * @param clips 动画剪辑
   */
  constructor(clips: THREE.AnimationClip[]) {
    super(GLTFAnimationComponent.type);

    this.clips = clips;
  }

  /**
   * 初始化动画混合器
   * @param root 根对象
   */
  public initMixer(root: THREE.Object3D): void {
    this.mixer = new THREE.AnimationMixer(root);

    // 创建所有动作
    for (const clip of this.clips) {
      const action = this.mixer.clipAction(clip);
      this.actions.set(clip.name, action);
    }

    // 监听动画完成事件
    this.mixer.addEventListener('finished', (event) => {
      this.state = AnimationState.FINISHED;
      this.emitter.emit(AnimationEventType.FINISHED, event);
    });
  }

  /**
   * 更新动画
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled() || !this.mixer || this.state !== AnimationState.PLAYING) {
      return;
    }

    // 更新混合器
    this.mixer.update(deltaTime * this.timeScale);

    // 发出时间更新事件
    this.emitter.emit(AnimationEventType.TIME_UPDATE, this.mixer.time);
  }

  /**
   * 播放动画
   * @param name 动画名称
   * @param options 播放选项
   * @returns 动画动作
   */
  public play(name: string, options: {
    loopMode?: AnimationLoopMode;
    blendMode?: AnimationBlendMode;
    crossFadeTime?: number;
    timeScale?: number;
    clampWhenFinished?: boolean;
  } = {}): THREE.AnimationAction | null {
    if (!this.mixer) {
      console.warn('动画混合器未初始化');
      return null;
    }

    // 获取动作
    const action = this.actions.get(name);
    if (!action) {
      console.warn(`找不到名为 ${name} 的动画`);
      return null;
    }

    // 设置选项
    const loopMode = options.loopMode !== undefined ? options.loopMode : this.defaultLoopMode;
    const blendMode = options.blendMode !== undefined ? options.blendMode : this.defaultBlendMode;
    const crossFadeTime = options.crossFadeTime !== undefined ? options.crossFadeTime : this.defaultCrossFadeTime;
    const timeScale = options.timeScale !== undefined ? options.timeScale : this.timeScale;
    const clampWhenFinished = options.clampWhenFinished !== undefined ? options.clampWhenFinished : false;

    // 设置动作属性
    action.setLoop(loopMode as any, Infinity);
    action.clampWhenFinished = clampWhenFinished;
    action.timeScale = timeScale;

    // 设置混合模式
    if (blendMode === AnimationBlendMode.ADDITIVE) {
      action.setEffectiveWeight(1.0);
      action.setEffectiveTimeScale(1.0);
      // 注意：AnimationAction没有blendMode属性，这里移除错误的设置
    }

    // 如果有其他动作正在播放，则交叉淡入
    for (const [otherName, otherAction] of this.actions) {
      if (otherName !== name && otherAction.enabled && otherAction.weight > 0) {
        action.crossFadeFrom(otherAction, crossFadeTime, true);
        break;
      }
    }

    // 播放动作
    action.play();

    // 更新状态
    this.state = AnimationState.PLAYING;

    // 发出事件
    this.emitter.emit(AnimationEventType.START, { name, action });

    return action;
  }

  /**
   * 停止动画
   * @param name 动画名称，如果未提供则停止所有动画
   */
  public stop(name?: string): void {
    if (!this.mixer) {
      return;
    }

    if (name) {
      // 停止指定动画
      const action = this.actions.get(name);
      if (action) {
        action.stop();
        this.emitter.emit(AnimationEventType.STOP, { name, action });
      }
    } else {
      // 停止所有动画
      for (const [name, action] of this.actions) {
        action.stop();
        this.emitter.emit(AnimationEventType.STOP, { name, action });
      }
    }

    this.state = AnimationState.STOPPED;
  }

  /**
   * 暂停动画
   * @param name 动画名称，如果未提供则暂停所有动画
   */
  public pause(name?: string): void {
    if (!this.mixer) {
      return;
    }

    if (name) {
      // 暂停指定动画
      const action = this.actions.get(name);
      if (action) {
        action.paused = true;
        this.emitter.emit(AnimationEventType.PAUSE, { name, action });
      }
    } else {
      // 暂停所有动画
      for (const [name, action] of this.actions) {
        action.paused = true;
        this.emitter.emit(AnimationEventType.PAUSE, { name, action });
      }
    }

    this.state = AnimationState.PAUSED;
  }

  /**
   * 恢复动画
   * @param name 动画名称，如果未提供则恢复所有动画
   */
  public resume(name?: string): void {
    if (!this.mixer || this.state !== AnimationState.PAUSED) {
      return;
    }

    if (name) {
      // 恢复指定动画
      const action = this.actions.get(name);
      if (action) {
        action.paused = false;
        this.emitter.emit(AnimationEventType.RESUME, { name, action });
      }
    } else {
      // 恢复所有动画
      for (const [name, action] of this.actions) {
        action.paused = false;
        this.emitter.emit(AnimationEventType.RESUME, { name, action });
      }
    }

    this.state = AnimationState.PLAYING;
  }

  /**
   * 获取动画剪辑
   * @returns 动画剪辑数组
   */
  public getClips(): THREE.AnimationClip[] {
    return [...this.clips];
  }

  /**
   * 获取动画混合器
   * @returns 动画混合器
   */
  public getMixer(): THREE.AnimationMixer | null {
    return this.mixer;
  }

  /**
   * 获取动作
   * @param name 动画名称
   * @returns 动画动作
   */
  public getAction(name: string): THREE.AnimationAction | undefined {
    return this.actions.get(name);
  }

  /**
   * 获取所有动作
   * @returns 动作映射
   */
  public getActions(): Map<string, THREE.AnimationAction> {
    return new Map(this.actions);
  }

  /**
   * 获取当前状态
   * @returns 动画状态
   */
  public getState(): AnimationState {
    return this.state;
  }

  /**
   * 设置时间比例
   * @param scale 时间比例
   */
  public setTimeScale(scale: number): void {
    this.timeScale = scale;

    // 更新所有动作的时间比例
    for (const action of this.actions.values()) {
      action.timeScale = scale;
    }
  }

  /**
   * 获取时间比例
   * @returns 时间比例
   */
  public getTimeScale(): number {
    return this.timeScale;
  }



  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: AnimationEventType, listener: (...args: any[]) => void): void {
    this.emitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: AnimationEventType, listener: (...args: any[]) => void): void {
    this.emitter.off(type, listener);
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 停止所有动画
    this.stop();

    // 清除所有事件监听器
    this.emitter.removeAllListeners();

    // 清除动作
    this.actions.clear();

    // 销毁混合器
    if (this.mixer) {
      this.mixer.stopAllAction();
      // 清理混合器缓存
      const root = this.mixer.getRoot();
      if (root) {
        this.mixer.uncacheRoot(root);
      }
    }

    // 调用基类的dispose方法
    super.dispose();
  }
}
