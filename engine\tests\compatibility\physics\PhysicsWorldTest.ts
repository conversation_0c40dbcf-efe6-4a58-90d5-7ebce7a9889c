/**
 * 物理世界兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 物理世界兼容性测试
 */
export const physicsWorldTest: TestCase = {
  name: '物理世界兼容性测试',
  description: '测试物理世界功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目物理系统实例
      const originalPhysicsSystem = new original.PhysicsSystem();
      
      // 创建重构后项目物理系统实例
      const refactoredPhysicsSystem = new refactored.PhysicsSystem();
      
      // 检查物理系统实例是否创建成功
      if (!originalPhysicsSystem || !refactoredPhysicsSystem) {
        return {
          name: '物理世界兼容性测试',
          passed: false,
          errorMessage: '物理系统实例创建失败'
        };
      }
      
      // 初始化物理系统
      originalPhysicsSystem.initialize();
      refactoredPhysicsSystem.initialize();
      
      // 检查物理系统是否初始化成功
      if (!originalPhysicsSystem.isInitialized() || !refactoredPhysicsSystem.isInitialized()) {
        return {
          name: '物理世界兼容性测试',
          passed: false,
          errorMessage: '物理系统初始化失败',
          details: {
            originalInitialized: originalPhysicsSystem.isInitialized(),
            refactoredInitialized: refactoredPhysicsSystem.isInitialized()
          }
        };
      }
      
      // 设置重力
      const gravity = { x: 0, y: -9.8, z: 0 };
      originalPhysicsSystem.setGravity(gravity);
      refactoredPhysicsSystem.setGravity(gravity);
      
      // 检查重力是否设置成功
      const originalGravity = originalPhysicsSystem.getGravity();
      const refactoredGravity = refactoredPhysicsSystem.getGravity();
      
      if (
        Math.abs(originalGravity.x - refactoredGravity.x) > 0.001 ||
        Math.abs(originalGravity.y - refactoredGravity.y) > 0.001 ||
        Math.abs(originalGravity.z - refactoredGravity.z) > 0.001
      ) {
        return {
          name: '物理世界兼容性测试',
          passed: false,
          errorMessage: `重力设置不一致: 原有项目=${JSON.stringify(originalGravity)}, 重构后项目=${JSON.stringify(refactoredGravity)}`,
          details: {
            originalGravity,
            refactoredGravity
          }
        };
      }
      
      // 更新物理系统
      originalPhysicsSystem.update(0.016);
      refactoredPhysicsSystem.update(0.016);
      
      // 检查物理系统的方法
      const originalMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(originalPhysicsSystem));
      const refactoredMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(refactoredPhysicsSystem));
      
      // 检查重构后的物理系统是否包含原有物理系统的所有方法
      const missingMethods = originalMethods.filter(method => {
        // 忽略构造函数和私有方法
        if (method === 'constructor' || method.startsWith('_')) {
          return false;
        }
        
        return !refactoredMethods.includes(method);
      });
      
      if (missingMethods.length > 0) {
        return {
          name: '物理世界兼容性测试',
          passed: false,
          errorMessage: `重构后的物理系统缺少以下方法: ${missingMethods.join(', ')}`,
          details: {
            originalMethods,
            refactoredMethods,
            missingMethods
          }
        };
      }
      
      // 销毁物理系统
      originalPhysicsSystem.destroy();
      refactoredPhysicsSystem.destroy();
      
      return {
        name: '物理世界兼容性测试',
        passed: true,
        details: {
          originalMethods,
          refactoredMethods
        }
      };
    } catch (error) {
      return {
        name: '物理世界兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
