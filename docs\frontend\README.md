# 前端文档

本目录包含了项目前端相关的所有技术文档，主要涵盖可视化编辑器的功能分析、架构设计和使用指南。

## 📋 文档目录

### 核心文档
- [可视化编辑器功能总结](visual-editor-summary.md) - 编辑器功能快速概览
- [可视化编辑器功能详细分析](visual-editor-analysis.md) - 编辑器架构和功能详解

### 相关文档
- [前端架构设计](../developer/architecture/README.md) - 前端架构详细说明
- [组件开发指南](../developer/api/components.md) - 组件开发文档
- [用户指南](../user-guide/README.md) - 用户使用指南

## 🎯 可视化编辑器概览

### 核心特性
- **🎨 可视化编辑**: 所见即所得的3D场景编辑
- **🧩 模块化架构**: 可扩展的面板和组件系统
- **🚀 实时协作**: 多人同时编辑支持
- **📱 跨平台**: 桌面端和移动端适配

### 技术栈
- **前端框架**: React 18 + TypeScript
- **状态管理**: Redux Toolkit
- **UI组件**: Ant Design + 自定义组件
- **3D引擎**: 自研dl-engine引擎
- **国际化**: react-i18next

### 主要功能
- **场景编辑**: 实体管理、层级结构、组件系统
- **资产管理**: 多格式支持、拖拽导入、实时预览
- **可视化脚本**: 节点编辑、代码编辑、实时执行
- **属性编辑**: 实时同步、批量编辑、撤销重做

## 🏗️ 架构概览

### 分层架构
```
┌─────────────────────────────────────────┐
│              用户界面层                  │
│  工具栏 │ 菜单栏 │ 状态栏 │ 面板系统    │
├─────────────────────────────────────────┤
│              状态管理层                  │
│  Redux Store │ 中间件 │ 事件系统       │
├─────────────────────────────────────────┤
│               服务层                     │
│  引擎服务 │ 场景服务 │ 资产服务        │
├─────────────────────────────────────────┤
│             引擎集成层                   │
│            DL-Engine                    │
└─────────────────────────────────────────┘
```

### 核心模块
- **components/**: UI组件库
  - **panels/**: 面板组件
  - **toolbar/**: 工具栏组件
  - **viewport/**: 视口组件
  - **layout/**: 布局组件
- **store/**: Redux状态管理
  - **editor/**: 编辑器状态
  - **scene/**: 场景状态
  - **ui/**: UI状态
  - **collaboration/**: 协作状态
- **services/**: 服务层
  - **EngineService**: 引擎服务
  - **SceneService**: 场景服务
  - **AssetService**: 资产服务
- **libs/**: 引擎库
  - **dl-engine/**: 底层引擎

## 🔧 开发指南

### 环境要求
- **Node.js**: >= 16.0.0
- **浏览器**: 支持WebGL 2.0的现代浏览器
- **内存**: 建议8GB以上
- **显卡**: 支持硬件加速的显卡

### 快速开始
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 运行测试
npm run test
```

### 开发工具
- **TypeScript**: 类型安全的JavaScript
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Jest**: 单元测试框架
- **Storybook**: 组件开发和文档

## 📱 移动端支持

### 响应式设计
- **设备检测**: 自动检测设备类型
- **布局调整**: 根据屏幕尺寸调整布局
- **触控优化**: 针对触控设备的交互优化
- **手势支持**: 支持多点触控手势

### 移动端特性
- **简化界面**: 适合小屏幕的简化界面
- **快速访问**: 常用功能的快速访问
- **离线编辑**: 支持离线模式编辑
- **云端同步**: 与云端的数据同步

## 🌐 国际化

### 支持语言
- **中文**: 简体中文 (zh-CN)
- **英文**: 英文 (en-US)

### 使用方式
```typescript
// 在组件中使用翻译
const { t } = useTranslation();
return <Button>{t('editor.save')}</Button>;

// 切换语言
import { changeLanguage } from '../i18n';
changeLanguage('en-US');
```

## 🎨 主题系统

### 内置主题
- **亮色主题**: 适合白天使用
- **暗色主题**: 适合夜间使用

### 主题切换
```typescript
// 切换主题
import { useDispatch } from 'react-redux';
import { setTheme } from '../store/ui/uiSlice';

const dispatch = useDispatch();
dispatch(setTheme('dark'));
```

## 🔌 扩展系统

### 面板扩展
```typescript
// 注册自定义面板
import { PanelRegistry } from '../components/panels/PanelRegistry';

PanelRegistry.register({
  id: 'custom-panel',
  type: PanelType.CUSTOM,
  title: '自定义面板',
  icon: <CustomIcon />,
  component: CustomPanel
});
```

### 组件扩展
```typescript
// 注册组件编辑器
import { ComponentEditorRegistry } from '../services/ComponentEditorRegistry';

ComponentEditorRegistry.register(
  ComponentType.CUSTOM,
  new CustomComponentEditor()
);
```

### 工具扩展
```typescript
// 注册自定义工具
import { ToolRegistry } from '../services/ToolRegistry';

ToolRegistry.register(new CustomTool({
  id: 'custom-tool',
  name: '自定义工具',
  icon: <ToolIcon />
}));
```

## 🚀 性能优化

### 渲染优化
- **视锥剔除**: 只渲染可见对象
- **LOD系统**: 距离相关的细节层次
- **批量渲染**: 减少绘制调用次数
- **实例化渲染**: 相同对象的实例化渲染

### 内存管理
- **资源池**: 复用常用资源对象
- **懒加载**: 按需加载资源
- **垃圾回收**: 及时释放不用的资源
- **缓存策略**: 智能的资源缓存

### 交互优化
- **防抖处理**: 避免频繁的状态更新
- **虚拟滚动**: 大量数据的高效显示
- **异步加载**: 非阻塞的资源加载
- **预加载**: 预测性的资源预加载

## 🔍 调试工具

### 开发者工具
- **控制台**: 集成的开发者控制台
- **性能监控**: FPS和内存监控
- **场景检查器**: 场景结构分析
- **资源分析器**: 资源使用分析

### 调试面板
- **性能面板**: 实时性能监控
- **网络面板**: 网络请求监控
- **错误面板**: 错误日志显示
- **状态面板**: Redux状态查看

## 📊 使用效果

### 用户体验
- ✅ **直观易用**: 符合用户习惯的界面设计
- ✅ **响应迅速**: 流畅的交互响应
- ✅ **功能完整**: 专业级的编辑功能
- ✅ **学习成本低**: 渐进式的功能学习

### 开发效率
- ✅ **快速原型**: 快速创建3D内容原型
- ✅ **模板系统**: 丰富的预设模板
- ✅ **批量操作**: 高效的批量编辑
- ✅ **实时预览**: 即时查看编辑效果

### 协作效果
- ✅ **实时协作**: 多人同时编辑
- ✅ **版本控制**: 完整的版本管理
- ✅ **冲突解决**: 智能冲突处理
- ✅ **权限管理**: 灵活的权限控制

## 🔮 未来发展

### 短期目标
- **性能优化**: 进一步提升渲染性能
- **功能完善**: 补充更多专业功能
- **用户体验**: 优化用户交互体验
- **稳定性**: 提高系统稳定性

### 长期规划
- **AI集成**: 集成更多AI辅助功能
- **云端编辑**: 支持云端编辑和渲染
- **WebXR**: 支持VR/AR编辑模式
- **实时光追**: 集成实时光线追踪

## 📚 学习资源

### 官方文档
- [React官方文档](https://react.dev/)
- [TypeScript官方文档](https://www.typescriptlang.org/)
- [Redux Toolkit文档](https://redux-toolkit.js.org/)
- [Ant Design文档](https://ant.design/)

### 社区资源
- [React社区](https://reactjs.org/community/support.html)
- [TypeScript社区](https://www.typescriptlang.org/community/)
- [WebGL学习资源](https://webglfundamentals.org/)

## 🤝 贡献指南

### 开发流程
1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 代码审查和合并

### 代码规范
- 遵循TypeScript最佳实践
- 使用ESLint和Prettier
- 编写单元测试
- 添加适当的注释

### 提交规范
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建工具或辅助工具的变动
```

---

**注意**: 本文档会随着项目发展持续更新，请定期查看最新版本。
