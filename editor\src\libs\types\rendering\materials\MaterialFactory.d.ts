/**
 * 材质工厂
 * 用于创建各种类型的材质
 */
import * as THREE from 'three';
/**
 * 材质类型枚举
 */
export declare enum MaterialType {
    /** 基础材质 */
    BASIC = "basic",
    /** Lambert材质 */
    LAMBERT = "lambert",
    /** Phong材质 */
    PHONG = "phong",
    /** 标准材质 */
    STANDARD = "standard",
    /** 物理材质 */
    PHYSICAL = "physical",
    /** 卡通材质 */
    TOON = "toon",
    /** 深度材质 */
    DEPTH = "depth",
    /** 法线材质 */
    NORMAL = "normal",
    /** 线条材质 */
    LINE_BASIC = "lineBasic",
    /** 虚线材质 */
    LINE_DASHED = "lineDashed",
    /** 点材质 */
    POINTS = "points",
    /** 精灵材质 */
    SPRITE = "sprite",
    /** 着色器材质 */
    SHADER = "shader",
    /** 原始着色器材质 */
    RAW_SHADER = "rawShader",
    /** 阴影材质 */
    SHADOW = "shadow",
    /** Matcap材质 */
    MATCAP = "matcap"
}
/**
 * 材质工厂类
 */
export declare class MaterialFactory {
    /** 材质创建函数映射 */
    private materialCreators;
    /**
     * 创建材质工厂
     */
    constructor();
    /**
     * 注册默认材质
     */
    private registerDefaultMaterials;
    /**
     * 注册材质
     * @param type 材质类型
     * @param creator 创建函数
     */
    registerMaterial(type: string, creator: (params: any) => THREE.Material): void;
    /**
     * 创建材质
     * @param type 材质类型
     * @param params 材质参数
     * @returns 材质
     */
    createMaterial(type: string, params?: any): THREE.Material;
    /**
     * 创建基础材质
     * @param params 材质参数
     * @returns 基础材质
     */
    createBasicMaterial(params?: THREE.MeshBasicMaterialParameters): THREE.MeshBasicMaterial;
    /**
     * 创建Lambert材质
     * @param params 材质参数
     * @returns Lambert材质
     */
    createLambertMaterial(params?: THREE.MeshLambertMaterialParameters): THREE.MeshLambertMaterial;
    /**
     * 创建Phong材质
     * @param params 材质参数
     * @returns Phong材质
     */
    createPhongMaterial(params?: THREE.MeshPhongMaterialParameters): THREE.MeshPhongMaterial;
    /**
     * 创建标准材质
     * @param params 材质参数
     * @returns 标准材质
     */
    createStandardMaterial(params?: THREE.MeshStandardMaterialParameters): THREE.MeshStandardMaterial;
    /**
     * 创建物理材质
     * @param params 材质参数
     * @returns 物理材质
     */
    createPhysicalMaterial(params?: THREE.MeshPhysicalMaterialParameters): THREE.MeshPhysicalMaterial;
    /**
     * 创建卡通材质
     * @param params 材质参数
     * @returns 卡通材质
     */
    createToonMaterial(params?: THREE.MeshToonMaterialParameters): THREE.MeshToonMaterial;
    /**
     * 创建深度材质
     * @param params 材质参数
     * @returns 深度材质
     */
    createDepthMaterial(params?: THREE.MeshDepthMaterialParameters): THREE.MeshDepthMaterial;
    /**
     * 创建法线材质
     * @param params 材质参数
     * @returns 法线材质
     */
    createNormalMaterial(params?: THREE.MeshNormalMaterialParameters): THREE.MeshNormalMaterial;
    /**
     * 创建线条材质
     * @param params 材质参数
     * @returns 线条材质
     */
    createLineBasicMaterial(params?: THREE.LineBasicMaterialParameters): THREE.LineBasicMaterial;
    /**
     * 创建虚线材质
     * @param params 材质参数
     * @returns 虚线材质
     */
    createLineDashedMaterial(params?: THREE.LineDashedMaterialParameters): THREE.LineDashedMaterial;
    /**
     * 创建点材质
     * @param params 材质参数
     * @returns 点材质
     */
    createPointsMaterial(params?: THREE.PointsMaterialParameters): THREE.PointsMaterial;
    /**
     * 创建精灵材质
     * @param params 材质参数
     * @returns 精灵材质
     */
    createSpriteMaterial(params?: THREE.SpriteMaterialParameters): THREE.SpriteMaterial;
    /**
     * 创建着色器材质
     * @param params 材质参数
     * @returns 着色器材质
     */
    createShaderMaterial(params?: THREE.ShaderMaterialParameters): THREE.ShaderMaterial;
    /**
     * 创建原始着色器材质
     * @param params 材质参数
     * @returns 原始着色器材质
     */
    createRawShaderMaterial(params?: THREE.ShaderMaterialParameters): THREE.RawShaderMaterial;
    /**
     * 创建阴影材质
     * @param params 材质参数
     * @returns 阴影材质
     */
    createShadowMaterial(params?: THREE.ShadowMaterialParameters): THREE.ShadowMaterial;
    /**
     * 创建Matcap材质
     * @param params 材质参数
     * @returns Matcap材质
     */
    createMatcapMaterial(params?: THREE.MeshMatcapMaterialParameters): THREE.MeshMatcapMaterial;
}
