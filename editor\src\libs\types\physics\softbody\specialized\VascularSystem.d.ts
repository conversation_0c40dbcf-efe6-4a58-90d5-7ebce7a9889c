/**
 * 血管系统 - 模拟血管网络和连接
 */
import * as THREE from 'three';
import { Component } from '../../../core/Component';
import { VesselConnectionType } from './OrganSoftBody';
/**
 * 血管类型枚举
 */
export declare enum VesselType {
    /** 动脉 */
    ARTERY = "artery",
    /** 静脉 */
    VEIN = "vein",
    /** 毛细血管 */
    CAPILLARY = "capillary",
    /** 主动脉 */
    AORTA = "aorta",
    /** 肺动脉 */
    PULMONARY_ARTERY = "pulmonary_artery",
    /** 冠状动脉 */
    CORONARY_ARTERY = "coronary_artery"
}
/**
 * 血管组件
 */
export declare class VesselComponent extends Component {
    /** 血管类型 */
    vesselType: VesselType;
    /** 血管直径 (mm) */
    diameter: number;
    /** 血管长度 (mm) */
    length: number;
    /** 血管壁厚度 (mm) */
    wallThickness: number;
    /** 血管弹性 */
    elasticity: number;
    /** 血管阻力 */
    resistance: number;
    /** 当前流量 (ml/min) */
    flowRate: number;
    /** 当前压力 (mmHg) */
    pressure: number;
    /** 血管路径点 */
    pathPoints: THREE.Vector3[];
    /** 连接的血管 */
    connectedVessels: string[];
    constructor(vesselType: VesselType, diameter: number, length: number);
}
/**
 * 流体系统接口
 */
export interface FluidSystem {
    /** 计算流体流动 */
    calculateFlow(vessel: VesselComponent, deltaTime: number): number;
    /** 更新压力 */
    updatePressure(vessel: VesselComponent, pressure: number): void;
    /** 获取粘度 */
    getViscosity(): number;
    /** 获取密度 */
    getDensity(): number;
}
/**
 * 简单流体系统实现
 */
export declare class SimpleFluidSystem implements FluidSystem {
    /** 血液粘度 (Pa·s) */
    private viscosity;
    /** 血液密度 (kg/m³) */
    private density;
    calculateFlow(vessel: VesselComponent, deltaTime: number): number;
    updatePressure(vessel: VesselComponent, pressure: number): void;
    getViscosity(): number;
    getDensity(): number;
}
/**
 * 血管连接类
 */
export declare class VesselConnection {
    /** 连接ID */
    id: string;
    /** 源血管 */
    sourceVessel: VesselComponent;
    /** 目标血管 */
    targetVessel: VesselComponent;
    /** 连接类型 */
    type: VesselConnectionType;
    /** 连接位置 */
    position: THREE.Vector3;
    /** 连接方向 */
    direction: THREE.Vector3;
    /** 血管直径 */
    diameter: number;
    /** 流量 */
    flowRate: number;
    /** 压力 */
    pressure: number;
    /** 阻力 */
    resistance: number;
    /** 最大流量 */
    maxFlowRate: number;
    constructor(sourceVessel: VesselComponent, targetVessel: VesselComponent, type: VesselConnectionType);
    /**
     * 设置流体流动参数
     * @param params 流动参数
     */
    setFlowParameters(params: {
        resistance?: number;
        flowRate?: number;
        maxFlowRate?: number;
    }): void;
    /**
     * 计算连接阻力
     * @returns 连接阻力
     */
    calculateResistance(): number;
}
/**
 * 血管系统配置
 */
export interface VascularSystemConfig {
    /** 是否启用血液流动模拟 */
    enableFlowSimulation?: boolean;
    /** 是否启用压力计算 */
    enablePressureCalculation?: boolean;
    /** 是否启用血管弹性 */
    enableVesselElasticity?: boolean;
    /** 流体系统 */
    fluidSystem?: FluidSystem;
    /** 心率 (次/分钟) */
    heartRate?: number;
    /** 收缩压 (mmHg) */
    systolicPressure?: number;
    /** 舒张压 (mmHg) */
    diastolicPressure?: number;
}
export declare class VascularSystem {
    /** 血管网络 */
    private vessels;
    /** 血管连接 */
    private connections;
    /** 流体系统引用 */
    private fluidSystem;
    /** 系统配置 */
    private config;
    /** 当前心率 */
    private heartRate;
    /** 收缩压 */
    private systolicPressure;
    /** 舒张压 */
    private diastolicPressure;
    /**
     * 构造函数
     * @param config 血管系统配置
     */
    constructor(config?: VascularSystemConfig);
    /**
     * 添加血管
     * @param id 血管ID
     * @param vessel 血管组件
     */
    addVessel(id: string, vessel: VesselComponent): void;
    /**
     * 获取血管
     * @param id 血管ID
     * @returns 血管组件
     */
    getVessel(id: string): VesselComponent | undefined;
    /**
     * 获取所有血管
     * @returns 血管映射
     */
    getAllVessels(): Map<string, VesselComponent>;
    /**
     * 创建血管连接
     * @param sourceVessel 源血管
     * @param targetVessel 目标血管
     * @param type 连接类型 (吻合/缝合)
     */
    createVesselConnection(sourceVessel: VesselComponent, targetVessel: VesselComponent, type: VesselConnectionType): VesselConnection;
    /**
     * 获取血管连接
     * @param id 连接ID
     * @returns 血管连接
     */
    getConnection(id: string): VesselConnection | undefined;
    /**
     * 获取所有连接
     * @returns 连接映射
     */
    getAllConnections(): Map<string, VesselConnection>;
    /**
     * 设置心率
     * @param rate 心率 (次/分钟)
     */
    setHeartRate(rate: number): void;
    /**
     * 获取心率
     * @returns 心率
     */
    getHeartRate(): number;
    /**
     * 设置血压
     * @param systolic 收缩压 (mmHg)
     * @param diastolic 舒张压 (mmHg)
     */
    setBloodPressure(systolic: number, diastolic: number): void;
    /**
     * 获取血压
     * @returns 血压 {收缩压, 舒张压}
     */
    getBloodPressure(): {
        systolic: number;
        diastolic: number;
    };
    /**
     * 模拟血液流动
     * @param deltaTime 时间步长
     */
    simulateBloodFlow(deltaTime: number): void;
    /**
     * 获取系统统计信息
     * @returns 统计信息
     */
    getSystemStats(): {
        vesselCount: number;
        connectionCount: number;
        totalFlow: number;
        averagePressure: number;
    };
}
