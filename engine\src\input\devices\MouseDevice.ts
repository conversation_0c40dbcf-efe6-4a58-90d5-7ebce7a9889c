/**
 * 鼠标输入设备
 */
import { BaseInputDevice } from '../InputDevice';
import { MouseButton, MouseButtonState } from '../InputSystem';

/**
 * 鼠标输入设备
 */
export class MouseDevice extends BaseInputDevice {
  /** 目标元素 */
  private element: HTMLElement;

  /** 是否阻止默认行为 */
  private preventDefault: boolean;

  /** 是否阻止事件传播 */
  private stopPropagation: boolean;

  /** 鼠标位置 */
  private position: { x: number; y: number } = { x: 0, y: 0 };

  /** 鼠标相对位置（相对于上一帧） */
  private movement: { x: number; y: number } = { x: 0, y: 0 };

  /** 鼠标滚轮增量 */
  private wheelDelta: number = 0;

  /** 鼠标事件处理器 */
  private mouseEventHandlers: { [key: string]: (event: MouseEvent | WheelEvent) => void } = {};

  /**
   * 创建鼠标输入设备
   * @param element 目标元素
   * @param preventDefault 是否阻止默认行为
   * @param stopPropagation 是否阻止事件传播
   */
  constructor(element: HTMLElement = document.body, preventDefault: boolean = true, stopPropagation: boolean = false) {
    super('mouse');
    this.element = element;
    this.preventDefault = preventDefault;
    this.stopPropagation = stopPropagation;

    // 初始化事件处理器
    this.initEventHandlers();

    // 初始化按钮状态
    this.setValue(`button:${MouseButton.LEFT}`, MouseButtonState.NONE);
    this.setValue(`button:${MouseButton.MIDDLE}`, MouseButtonState.NONE);
    this.setValue(`button:${MouseButton.RIGHT}`, MouseButtonState.NONE);

    // 初始化位置和移动
    this.setValue('position:x', 0);
    this.setValue('position:y', 0);
    this.setValue('movement:x', 0);
    this.setValue('movement:y', 0);
    this.setValue('wheel:delta', 0);
  }

  /**
   * 初始化事件处理器
   */
  private initEventHandlers(): void {
    // 鼠标按下事件
    this.mouseEventHandlers.mousedown = this.handleMouseDown.bind(this);

    // 鼠标释放事件
    this.mouseEventHandlers.mouseup = this.handleMouseUp.bind(this);

    // 鼠标移动事件
    this.mouseEventHandlers.mousemove = this.handleMouseMove.bind(this);

    // 鼠标滚轮事件
    this.mouseEventHandlers.wheel = this.handleMouseWheel.bind(this);
  }

  /**
   * 初始化设备
   */
  public initialize(): void {
    if (this.initialized) return;

    // 添加事件监听器
    this.addEventListeners();

    super.initialize();
  }

  /**
   * 销毁设备
   */
  public destroy(): void {
    if (this.destroyed) return;

    // 移除事件监听器
    this.removeEventListeners();

    super.destroy();
  }

  /**
   * 更新设备状态
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || this.destroyed) return;

    // 更新按钮状态
    for (let i = 0; i < 3; i++) {
      const key = `button:${i}`;
      const state = this.getValue(key);
      if (state === MouseButtonState.DOWN) {
        // 将按下状态更新为按住状态
        this.setValue(key, MouseButtonState.PRESSED);
      } else if (state === MouseButtonState.UP) {
        // 将释放状态更新为未按下状态
        this.setValue(key, MouseButtonState.NONE);
      }
    }

    // 重置鼠标移动和滚轮增量
    this.movement.x = 0;
    this.movement.y = 0;
    this.wheelDelta = 0;
    this.setValue('movement:x', 0);
    this.setValue('movement:y', 0);
    this.setValue('wheel:delta', 0);

    super.update(deltaTime);
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 添加鼠标事件监听器
    for (const [event, handler] of Object.entries(this.mouseEventHandlers)) {
      this.element.addEventListener(event, handler as EventListener, { passive: !this.preventDefault });
    }
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 移除鼠标事件监听器
    for (const [event, handler] of Object.entries(this.mouseEventHandlers)) {
      this.element.removeEventListener(event, handler as EventListener);
    }
  }

  /**
   * 更新鼠标位置
   * @param event 鼠标事件
   */
  private updateMousePosition(event: MouseEvent): void {
    // 获取元素位置和大小
    const rect = this.element.getBoundingClientRect();

    // 计算鼠标在元素内的位置
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // 计算鼠标移动
    this.movement.x = x - this.position.x;
    this.movement.y = y - this.position.y;

    // 更新鼠标位置
    this.position.x = x;
    this.position.y = y;

    // 更新设备值
    this.setValue('position:x', this.position.x);
    this.setValue('position:y', this.position.y);
    this.setValue('movement:x', this.movement.x);
    this.setValue('movement:y', this.movement.y);
  }

  /**
   * 处理鼠标按钮按下事件
   * @param event 鼠标事件
   */
  private handleMouseDown(event: MouseEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新鼠标位置
    this.updateMousePosition(event);

    // 设置鼠标按钮为按下状态
    const key = `button:${event.button}`;
    this.setValue(key, MouseButtonState.DOWN);

    // 触发鼠标按钮按下事件
    this.eventEmitter.emit(`${key}:down`, {
      button: event.button,
      x: this.position.x,
      y: this.position.y,
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理鼠标按钮释放事件
   * @param event 鼠标事件
   */
  private handleMouseUp(event: MouseEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新鼠标位置
    this.updateMousePosition(event);

    // 设置鼠标按钮为释放状态
    const key = `button:${event.button}`;
    this.setValue(key, MouseButtonState.UP);

    // 触发鼠标按钮释放事件
    this.eventEmitter.emit(`${key}:up`, {
      button: event.button,
      x: this.position.x,
      y: this.position.y,
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理鼠标移动事件
   * @param event 鼠标事件
   */
  private handleMouseMove(event: MouseEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新鼠标位置
    this.updateMousePosition(event);

    // 触发鼠标移动事件
    this.eventEmitter.emit('move', {
      x: this.position.x,
      y: this.position.y,
      movementX: this.movement.x,
      movementY: this.movement.y,
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 处理鼠标滚轮事件
   * @param event 滚轮事件
   */
  private handleMouseWheel(event: WheelEvent): void {
    // 阻止默认行为和事件传播
    if (this.preventDefault) event.preventDefault();
    if (this.stopPropagation) event.stopPropagation();

    // 更新滚轮增量
    this.wheelDelta = event.deltaY;
    this.setValue('wheel:delta', this.wheelDelta);

    // 触发鼠标滚轮事件
    this.eventEmitter.emit('wheel', {
      delta: this.wheelDelta,
      altKey: event.altKey,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      metaKey: event.metaKey
    });
  }

  /**
   * 获取鼠标位置
   * @returns 鼠标位置
   */
  public getPosition(): { x: number; y: number } {
    return { x: this.position.x, y: this.position.y };
  }

  /**
   * 获取鼠标移动
   * @returns 鼠标移动
   */
  public getMovement(): { x: number; y: number } {
    return { x: this.movement.x, y: this.movement.y };
  }

  /**
   * 获取滚轮增量
   * @returns 滚轮增量
   */
  public getWheelDelta(): number {
    return this.wheelDelta;
  }

  /**
   * 检查鼠标按钮是否按下
   * @param button 鼠标按钮
   * @returns 是否按下
   */
  public isButtonDown(button: MouseButton): boolean {
    const state = this.getValue(`button:${button}`);
    return state === MouseButtonState.DOWN || state === MouseButtonState.PRESSED;
  }

  /**
   * 检查鼠标按钮是否刚按下
   * @param button 鼠标按钮
   * @returns 是否刚按下
   */
  public isButtonJustDown(button: MouseButton): boolean {
    return this.getValue(`button:${button}`) === MouseButtonState.DOWN;
  }

  /**
   * 检查鼠标按钮是否刚释放
   * @param button 鼠标按钮
   * @returns 是否刚释放
   */
  public isButtonJustUp(button: MouseButton): boolean {
    return this.getValue(`button:${button}`) === MouseButtonState.UP;
  }
}
