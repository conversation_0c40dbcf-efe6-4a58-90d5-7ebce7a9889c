import { Animator } from '../Animator';
import type { Entity } from '../../core/Entity';
/**
 * 动画实例管理器
 */
export declare class AnimationInstanceManager {
    /** 实例组 */
    private instanceGroups;
    /** 实体到组的映射 */
    private entityToGroup;
    /** 是否启用GPU蒙皮 */
    private enableGPUSkinning;
    /** 是否启用实例化 */
    private enableInstancing;
    /** 最大实例数量 */
    private maxInstancesPerGroup;
    /** 上次更新时间 */
    private lastUpdateTime;
    /**
     * 构造函数
     * @param options 选项
     */
    constructor(options?: {
        enableGPUSkinning?: boolean;
        enableInstancing?: boolean;
        maxInstancesPerGroup?: number;
    });
    /**
     * 注册动画实例
     * @param groupId 组ID
     * @param entity 实体
     * @param animator 动画控制器
     * @returns 是否成功注册
     */
    registerInstance(groupId: string, entity: Entity, animator: Animator): boolean;
    /**
     * 注销动画实例
     * @param entity 实体
     * @returns 是否成功注销
     */
    unregisterInstance(entity: Entity): boolean;
    /**
     * 播放动画
     * @param entity 实体
     * @param clipName 动画片段名称
     * @param blendTime 混合时间
     * @returns 是否成功播放
     */
    play(entity: Entity, clipName: string, blendTime?: number): boolean;
    /**
     * 设置更新频率
     * @param entity 实体
     * @param frequency 更新频率（秒）
     */
    setUpdateFrequency(entity: Entity, frequency: number): void;
    /**
     * 标记需要更新
     * @param entity 实体
     */
    markNeedsUpdate(entity: Entity): void;
    /**
     * 更新动画实例
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 应用共享动画状态到实体
     * @param group 实例组
     * @param entity 实体
     * @param instanceData 实例数据
     */
    private applySharedAnimationState;
    /**
     * 混合两个值
     * @param a 值A
     * @param b 值B
     * @param t 混合因子（0-1）
     * @returns 混合结果
     */
    private blendValues;
    /**
     * 应用动画状态到实体
     * @param entity 实体
     * @param animationState 动画状态
     */
    private applyAnimationState;
}
