# 使用官方Node.js 22镜像作为基础镜像
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制场景生成服务代码
COPY scene-generation-service/package*.json ./scene-generation-service/
WORKDIR /app/scene-generation-service

# 安装依赖
RUN npm ci --only=production

# 复制场景生成服务源代码
COPY scene-generation-service/ ./

# 构建应用
RUN npm run build

FROM node:22-alpine

WORKDIR /app

COPY --from=builder /app/scene-generation-service/package*.json ./
COPY --from=builder /app/scene-generation-service/dist ./dist

RUN npm install --only=production

# 创建uploads目录
RUN mkdir -p uploads

# 创建logs目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 3004

# 设置环境变量
ENV NODE_ENV=production

# 启动应用
CMD ["node", "dist/main.js"]
