import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { LipSyncComponent } from '../components/LipSyncComponent';
/**
 * 增强型口型同步系统配置
 */
export interface EnhancedLipSyncSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** FFT大小 */
    fftSize?: number;
    /** 音量阈值 */
    volumeThreshold?: number;
    /** 分析间隔（毫秒） */
    analysisInterval?: number;
    /** 是否使用Web Worker */
    useWorker?: boolean;
    /** 频率带数量 */
    numFrequencyBands?: number;
    /** 是否使用高级分析器 */
    useAdvancedAnalyzer?: boolean;
    /** 是否使用AI预测 */
    useAIPrediction?: boolean;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 是否使用语音识别 */
    useSpeechRecognition?: boolean;
    /** 语音识别语言 */
    speechRecognitionLang?: string;
    /** 是否使用音素分析 */
    usePhonemeAnalysis?: boolean;
    /** 是否使用上下文预测 */
    useContextPrediction?: boolean;
    /** 上下文窗口大小 */
    contextWindowSize?: number;
    /** 是否使用平滑过渡 */
    useSmoothTransition?: boolean;
    /** 过渡时间（秒） */
    transitionTime?: number;
    /** 是否使用中文音素映射 */
    useChinesePhonemeMapping?: boolean;
}
/**
 * 音素类型
 */
export declare enum PhonemeType {
    A = "a",
    E = "e",
    I = "i",
    O = "o",
    U = "u",
    B = "b",
    P = "p",
    M = "m",
    F = "f",
    V = "v",
    TH = "th",
    DH = "dh",
    T = "t",
    D = "d",
    N = "n",
    L = "l",
    R = "r",
    S = "s",
    Z = "z",
    SH = "sh",
    ZH = "zh",
    CH = "ch",
    J = "j",
    K = "k",
    G = "g",
    NG = "ng",
    H = "h",
    W = "w",
    Y = "y",
    SILENCE = "silence"
}
/**
 * 中文音素类型
 */
export declare enum ChinesePhonemeType {
    A = "a",
    O = "o",
    E = "e",
    I = "i",
    U = "u",
    V = "v",
    AI = "ai",
    EI = "ei",
    AO = "ao",
    OU = "ou",
    AN = "an",
    EN = "en",
    ANG = "ang",
    ENG = "eng",
    ER = "er",
    B = "b",
    P = "p",
    M = "m",
    F = "f",
    D = "d",
    T = "t",
    N = "n",
    L = "l",
    G = "g",
    K = "k",
    H = "h",
    J = "j",
    Q = "q",
    X = "x",
    ZH = "zh",
    CH = "ch",
    SH = "sh",
    R = "r",
    Z = "z",
    C = "c",
    S = "s",
    SILENCE = "silence"
}
/**
 * 增强型口型同步系统
 */
export declare class EnhancedLipSyncSystem extends System {
    /** 系统名称 */
    static readonly NAME = "EnhancedLipSyncSystem";
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 口型同步组件映射 */
    private components;
    /** 音频上下文 */
    private audioContext;
    /** 音频分析器 */
    private audioAnalyser;
    /** 音频源 */
    private audioSource;
    /** 频谱数据 */
    private spectrum;
    /** 是否正在跟踪 */
    private isTracking;
    /** 语音识别器 */
    private speechRecognition;
    /** 当前识别的文本 */
    private recognizedText;
    /** 当前音素序列 */
    private currentPhonemes;
    /** 当前口型 */
    private currentViseme;
    /** 上一个口型 */
    private previousViseme;
    /** 口型历史 */
    private visemeHistory;
    /** 过渡计时器 */
    private transitionTimer;
    /** 过渡开始时间 */
    private transitionStartTime;
    /** Web Worker */
    private worker;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: EnhancedLipSyncSystemConfig);
    /**
     * 初始化音频上下文
     */
    private initAudioContext;
    /**
     * 初始化语音识别
     */
    private initSpeechRecognition;
    /**
     * 初始化Web Worker
     */
    private initWorker;
    /**
     * 处理语音识别结果
     * @param event 语音识别事件
     */
    private handleSpeechRecognitionResult;
    /**
     * 处理Worker消息
     * @param event 消息事件
     */
    private handleWorkerMessage;
    /**
     * 分析音素
     * @param text 文本
     */
    private analyzePhonemes;
    /**
     * 分析中文音素
     * @param text 中文文本
     */
    private analyzeChinesePhonemes;
    /**
     * 分析英文音素
     * @param text 英文文本
     */
    private analyzeEnglishPhonemes;
    /**
     * 根据音素序列设置口型
     */
    private setVisemeFromPhonemes;
    /**
     * 为所有实体设置口型
     * @param viseme 口型
     * @param weight 权重
     */
    private setVisemeForAllEntities;
    /**
     * 更新口型历史
     * @param viseme 口型
     */
    private updateVisemeHistory;
    /**
     * 创建口型同步组件
     * @param entity 实体
     * @returns 口型同步组件
     */
    createLipSync(entity: Entity): LipSyncComponent;
    /**
     * 移除口型同步组件
     * @param entity 实体
     */
    removeLipSync(entity: Entity): void;
    /**
     * 获取口型同步组件
     * @param entity 实体
     * @returns 口型同步组件
     */
    getLipSync(entity: Entity): LipSyncComponent | undefined;
    /**
     * 启动口型同步跟踪
     * @param audioElement 音频元素
     */
    startTracking(audioElement: HTMLAudioElement | HTMLVideoElement): void;
    /**
     * 启动麦克风跟踪
     */
    startMicrophoneTracking(): Promise<boolean>;
    /**
     * 停止口型同步跟踪
     */
    stopTracking(): void;
    /**
     * 启动分析定时器
     */
    private startAnalysisTimer;
    /**
     * 分析音频
     */
    private analyzeAudio;
    /**
     * 计算RMS
     * @param spectrum 频谱数据
     * @returns RMS值
     */
    private calculateRMS;
    /**
     * 分析频谱
     * @param spectrum 频谱数据
     * @returns 口型
     */
    private analyzeSpectrum;
    /**
     * 计算频率带能量
     * @param spectrum 频谱数据
     * @returns 频率带能量
     */
    private calculateFrequencyBands;
    /**
     * 根据上下文预测口型
     * @param currentViseme 当前口型
     * @returns 预测的口型
     */
    private predictVisemeFromContext;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
