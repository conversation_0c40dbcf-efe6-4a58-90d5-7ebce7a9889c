/**
 * 点对点约束
 * 将两个物体通过一个点连接起来
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import { PhysicsConstraint, ConstraintType } from './PhysicsConstraint';

/**
 * 点对点约束选项
 */
export interface PointToPointConstraintOptions {
  /** 源物体上的连接点（局部坐标） */
  pivotA?: THREE.Vector3;
  /** 目标物体上的连接点（局部坐标） */
  pivotB?: THREE.Vector3;
  /** 最大力 */
  maxForce?: number;
  /** 是否允许连接的物体之间碰撞 */
  collideConnected?: boolean;
}

/**
 * 点对点约束
 */
export class PointToPointConstraint extends PhysicsConstraint {
  /** 组件类型 */
  public static readonly type: string = 'PointToPointConstraint';

  /** 源物体上的连接点（局部坐标） */
  private pivotA: CANNON.Vec3;

  /** 目标物体上的连接点（局部坐标） */
  private pivotB: CANNON.Vec3;

  /** 最大力 */
  private maxForce: number;

  /**
   * 创建点对点约束
   * @param targetEntity 目标实体
   * @param options 约束选项
   */
  constructor(targetEntity: Entity | null = null, options: PointToPointConstraintOptions = {}) {
    super(ConstraintType.POINT_TO_POINT, targetEntity, options);

    // 设置源物体上的连接点
    const pivotA = options.pivotA || new THREE.Vector3(0, 0, 0);
    this.pivotA = new CANNON.Vec3(pivotA.x, pivotA.y, pivotA.z);

    // 设置目标物体上的连接点
    const pivotB = options.pivotB || new THREE.Vector3(0, 0, 0);
    this.pivotB = new CANNON.Vec3(pivotB.x, pivotB.y, pivotB.z);

    // 设置最大力
    this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
  }

  /**
   * 创建约束
   */
  protected createConstraint(): void {
    // 获取源物体和目标物体
    const bodyA = this.getSourceBody();
    const bodyB = this.getTargetBody();

    // 如果没有源物体或目标物体，则无法创建约束
    if (!bodyA || !bodyB) {
      console.warn('无法创建点对点约束：缺少源物体或目标物体');
      return;
    }

    // 创建点对点约束
    this.constraint = new CANNON.PointToPointConstraint(
      bodyA,
      this.pivotA,
      bodyB,
      this.pivotB,
      this.maxForce
    );

    // 设置是否允许连接的物体之间碰撞
    this.constraint.collideConnected = this.collideConnected;
  }

  /**
   * 设置源物体上的连接点
   * @param pivot 连接点（局部坐标）
   */
  public setPivotA(pivot: THREE.Vector3): void {
    this.pivotA.set(pivot.x, pivot.y, pivot.z);

    // 如果约束已创建，更新约束
    if (this.constraint instanceof CANNON.PointToPointConstraint) {
      this.constraint.pivotA.copy(this.pivotA);
    }
  }

  /**
   * 获取源物体上的连接点
   * @returns 连接点（局部坐标）
   */
  public getPivotA(): THREE.Vector3 {
    return new THREE.Vector3(this.pivotA.x, this.pivotA.y, this.pivotA.z);
  }

  /**
   * 设置目标物体上的连接点
   * @param pivot 连接点（局部坐标）
   */
  public setPivotB(pivot: THREE.Vector3): void {
    this.pivotB.set(pivot.x, pivot.y, pivot.z);

    // 如果约束已创建，更新约束
    if (this.constraint instanceof CANNON.PointToPointConstraint) {
      this.constraint.pivotB.copy(this.pivotB);
    }
  }

  /**
   * 获取目标物体上的连接点
   * @returns 连接点（局部坐标）
   */
  public getPivotB(): THREE.Vector3 {
    return new THREE.Vector3(this.pivotB.x, this.pivotB.y, this.pivotB.z);
  }

  /**
   * 设置最大力
   * @param maxForce 最大力
   */
  public setMaxForce(maxForce: number): void {
    this.maxForce = maxForce;

    // 如果约束已创建，更新约束
    // 注意：CANNON.js 的 PointToPointConstraint 不直接支持 maxForce 属性
    // 需要通过约束的方程来设置最大力
    if (this.constraint instanceof CANNON.PointToPointConstraint) {
      // 设置每个方程的最大力
      this.constraint.equations.forEach(equation => {
        if ('maxForce' in equation) {
          (equation as any).maxForce = maxForce;
        }
      });
    }
  }

  /**
   * 获取最大力
   * @returns 最大力
   */
  public getMaxForce(): number {
    return this.maxForce;
  }
}
