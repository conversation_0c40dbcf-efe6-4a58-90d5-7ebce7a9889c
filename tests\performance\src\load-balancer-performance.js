import { check } from 'k6';
import http from 'k6/http';
import { sleep, randomSeed } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';

// 性能指标
const hashDistribution = new Counter('hash_distribution');
const requestLatency = new Trend('request_latency');
const requestsPerSecond = new Rate('requests_per_second');
const errorRate = new Rate('error_rate');

// 配置
const CONFIG = {
  serviceRegistry: __ENV.SERVICE_REGISTRY_URL || 'http://localhost:4010',
  testService: __ENV.TEST_SERVICE || 'test-service',
  sessionCount: parseInt(__ENV.SESSION_COUNT || '1000'),
  requestsPerSession: parseInt(__ENV.REQUESTS_PER_SESSION || '10'),
};

// 初始化随机数生成器
randomSeed(42);

// 生成测试会话ID
function generateSessionIds(count) {
  const sessions = [];
  for (let i = 0; i < count; i++) {
    sessions.push(`session-${i}-${Date.now()}`);
  }
  return sessions;
}

// 初始化函数 - 在测试开始前运行一次
export function setup() {
  // 注册测试服务实例
  for (let i = 0; i < 10; i++) {
    const res = http.post(`${CONFIG.serviceRegistry}/registry/services`, JSON.stringify({
      name: CONFIG.testService,
      instanceId: `test-instance-${i}`,
      host: `localhost`,
      port: 3000 + i,
      metadata: {
        version: '1.0.0',
        weight: 1,
      },
    }), {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    check(res, {
      '服务实例注册成功': (r) => r.status === 201 || r.status === 200,
    });
  }
  
  // 生成会话ID
  return {
    sessions: generateSessionIds(CONFIG.sessionCount),
  };
}

// 默认函数 - 在测试期间为每个虚拟用户运行
export default function(data) {
  // 随机选择一个会话ID
  const sessionId = data.sessions[Math.floor(Math.random() * data.sessions.length)];
  
  // 发送多个请求，使用一致性哈希负载均衡
  for (let i = 0; i < CONFIG.requestsPerSession; i++) {
    const startTime = new Date();
    
    const res = http.get(`${CONFIG.serviceRegistry}/registry/services/${CONFIG.testService}/instances`, {
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': sessionId,
      },
      params: {
        algorithm: 'consistent-hash',
      },
    });
    
    const duration = new Date() - startTime;
    
    // 记录请求延迟
    requestLatency.add(duration);
    
    // 记录请求成功/失败
    errorRate.add(res.status !== 200);
    
    // 记录每秒请求数
    requestsPerSecond.add(1);
    
    // 检查响应
    const success = check(res, {
      '请求成功': (r) => r.status === 200,
      '返回实例信息': (r) => r.json().length > 0,
    });
    
    if (success && res.json().length > 0) {
      // 记录实例分布
      const instance = res.json()[0];
      hashDistribution.add(1, { instanceId: instance.instanceId });
    }
    
    // 短暂休息
    sleep(0.1);
  }
}

// 清理函数 - 在测试结束后运行一次
export function teardown(data) {
  // 清理测试服务实例
  for (let i = 0; i < 10; i++) {
    http.del(`${CONFIG.serviceRegistry}/registry/services/${CONFIG.testService}/instances/test-instance-${i}`, null, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}

// 测试配置
export const options = {
  scenarios: {
    // 稳定负载测试
    steady_load: {
      executor: 'constant-vus',
      vus: 50,
      duration: '1m',
    },
    // 阶梯负载测试
    ramp_up: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '30s', target: 50 },
        { duration: '1m', target: 50 },
        { duration: '30s', target: 0 },
      ],
    },
    // 峰值负载测试
    spike: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '10s', target: 0 },
        { duration: '10s', target: 100 },
        { duration: '30s', target: 100 },
        { duration: '10s', target: 0 },
      ],
    },
  },
  thresholds: {
    'request_latency': ['p(95)<500'], // 95%的请求延迟小于500ms
    'requests_per_second': ['avg>100'], // 平均每秒请求数大于100
    'error_rate': ['rate<0.01'], // 错误率小于1%
  },
};
