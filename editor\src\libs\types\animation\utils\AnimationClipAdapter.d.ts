/**
 * 动画片段适配器
 * 用于在自定义 AnimationClip 和 Three.js AnimationClip 之间进行转换
 */
import * as THREE from 'three';
import { AnimationClip } from '../AnimationClip';
export declare class AnimationClipAdapter {
    /**
     * 将自定义 AnimationClip 转换为 Three.js AnimationClip
     */
    static toThreeClip(customClip: AnimationClip): THREE.AnimationClip;
    /**
     * 将 Three.js AnimationClip 转换为自定义 AnimationClip
     */
    static fromThreeClip(threeClip: THREE.AnimationClip): AnimationClip;
    /**
     * 安全地将任意类型转换为 Three.js AnimationClip
     */
    static ensureThreeClip(clip: any): THREE.AnimationClip;
    /**
     * 安全地将任意类型转换为自定义 AnimationClip
     */
    static ensureCustomClip(clip: any): AnimationClip;
    /**
     * 批量转换为 Three.js AnimationClip
     */
    static toThreeClips(clips: any[]): THREE.AnimationClip[];
    /**
     * 批量转换为自定义 AnimationClip
     */
    static toCustomClips(clips: any[]): AnimationClip[];
    /**
     * 检查是否为有效的动画片段
     */
    static isValidClip(clip: any): boolean;
    /**
     * 复制动画片段
     */
    static cloneClip(clip: any): any;
}
