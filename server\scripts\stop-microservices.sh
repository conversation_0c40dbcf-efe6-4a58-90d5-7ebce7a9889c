#!/bin/bash

# 微服务架构停止脚本
# 用于停止完整的文本语音场景生成系统微服务架构

set -e

echo "🛑 停止文本语音场景生成系统微服务架构..."

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装"
    exit 1
fi

# 停止所有服务
echo "🔄 停止所有微服务..."
docker-compose -f docker-compose.microservices.yml down

# 可选：移除所有容器、网络和卷
read -p "是否要删除所有数据卷？这将删除所有数据库数据 (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  删除所有数据卷..."
    docker-compose -f docker-compose.microservices.yml down -v
    
    # 删除未使用的卷
    docker volume prune -f
    
    echo "✅ 所有数据卷已删除"
else
    echo "📦 保留数据卷"
fi

# 可选：删除镜像
read -p "是否要删除构建的镜像？ (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  删除构建的镜像..."
    
    # 获取项目名称（默认为目录名）
    PROJECT_NAME=$(basename "$(pwd)")
    
    # 删除项目相关的镜像
    docker images --format "table {{.Repository}}:{{.Tag}}" | grep "^${PROJECT_NAME}" | xargs -r docker rmi
    
    # 删除未使用的镜像
    docker image prune -f
    
    echo "✅ 镜像已删除"
else
    echo "🖼️  保留镜像"
fi

# 清理未使用的网络
echo "🧹 清理未使用的网络..."
docker network prune -f

# 显示剩余的容器和卷
echo ""
echo "📊 剩余资源："
echo "容器："
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "数据卷："
docker volume ls --format "table {{.Name}}\t{{.Driver}}"

echo ""
echo "网络："
docker network ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}"

echo ""
echo "✅ 微服务架构已停止"
echo ""
echo "🔄 重新启动："
echo "  ./scripts/start-microservices.sh"
echo ""
echo "📝 查看停止的容器："
echo "  docker ps -a"
echo ""
echo "🗑️  完全清理（删除所有容器、镜像、卷）："
echo "  docker system prune -a --volumes"
