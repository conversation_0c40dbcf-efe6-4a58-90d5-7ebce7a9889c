#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 快速测试所有项目的 TypeScript 编译...\n');

const projects = [
  'editor',
  'engine', 
  'server/game-server',
  'server/shared',
  'server/api-gateway',
  'server/user-service',
  'server/project-service',
  'server/asset-service',
  'server/collaboration-service',
  'server/render-service',
  'server/service-registry',
];

let passed = 0;
let failed = 0;

projects.forEach(project => {
  try {
    console.log(`📦 测试 ${project}...`);
    execSync('npx tsc --noEmit', { 
      cwd: path.resolve(project), 
      stdio: 'pipe',
      timeout: 60000 
    });
    console.log(`   ✅ ${project} 通过`);
    passed++;
  } catch (error) {
    console.log(`   ❌ ${project} 失败`);
    failed++;
  }
});

console.log(`\n📊 结果: ${passed} 通过, ${failed} 失败`);
if (failed === 0) {
  console.log('🎉 所有项目 TypeScript 编译通过！');
} else {
  console.log('⚠️  有项目编译失败');
}
