/**
 * 性能指标类型
 */
export declare enum PerformanceMetricType {
    /** 帧率 */
    FPS = "fps",
    /** 渲染时间 */
    RENDER_TIME = "renderTime",
    /** 物理更新时间 */
    PHYSICS_TIME = "physicsTime",
    /** 动画更新时间 */
    ANIMATION_TIME = "animationTime",
    /** 输入处理时间 */
    INPUT_TIME = "inputTime",
    /** 网络更新时间 */
    NETWORK_TIME = "networkTime",
    /** 脚本执行时间 */
    SCRIPT_TIME = "scriptTime",
    /** 总更新时间 */
    TOTAL_UPDATE_TIME = "totalUpdateTime",
    /** 内存使用 */
    MEMORY_USAGE = "memoryUsage",
    /** 纹理内存 */
    TEXTURE_MEMORY = "textureMemory",
    /** 几何体内存 */
    GEOMETRY_MEMORY = "geometryMemory",
    /** 绘制调用次数 */
    DRAW_CALLS = "drawCalls",
    /** 三角形数量 */
    TRIANGLES = "triangles",
    /** 顶点数量 */
    VERTICES = "vertices",
    /** 碰撞对数量 */
    COLLISION_PAIRS = "collisionPairs",
    /** 接触点数量 */
    CONTACT_POINTS = "contactPoints",
    /** 实体数量 */
    ENTITY_COUNT = "entityCount",
    /** 组件数量 */
    COMPONENT_COUNT = "componentCount",
    /** 系统数量 */
    SYSTEM_COUNT = "systemCount",
    /** GPU使用率 */
    GPU_USAGE = "gpuUsage",
    /** GPU内存使用 */
    GPU_MEMORY = "gpuMemory",
    /** CPU使用率 */
    CPU_USAGE = "cpuUsage",
    /** 资源加载时间 */
    RESOURCE_LOAD_TIME = "resourceLoadTime",
    /** 资源数量 */
    RESOURCE_COUNT = "resourceCount",
    /** 资源内存使用 */
    RESOURCE_MEMORY = "resourceMemory",
    /** 着色器编译时间 */
    SHADER_COMPILE_TIME = "shaderCompileTime",
    /** 着色器数量 */
    SHADER_COUNT = "shaderCount",
    /** 材质数量 */
    MATERIAL_COUNT = "materialCount",
    /** 纹理数量 */
    TEXTURE_COUNT = "textureCount",
    /** 几何体数量 */
    GEOMETRY_COUNT = "geometryCount",
    /** 光源数量 */
    LIGHT_COUNT = "lightCount",
    /** 阴影贴图数量 */
    SHADOW_MAP_COUNT = "shadowMapCount",
    /** 后处理通道数量 */
    POST_PROCESS_PASS_COUNT = "postProcessPassCount",
    /** 后处理时间 */
    POST_PROCESS_TIME = "postProcessTime",
    /** 可见对象数量 */
    VISIBLE_OBJECT_COUNT = "visibleObjectCount",
    /** 剔除对象数量 */
    CULLED_OBJECT_COUNT = "culledObjectCount",
    /** 网络消息数量 */
    NETWORK_MESSAGE_COUNT = "networkMessageCount",
    /** 网络数据大小 */
    NETWORK_DATA_SIZE = "networkDataSize",
    /** 网络延迟 */
    NETWORK_LATENCY = "networkLatency",
    /** 事件数量 */
    EVENT_COUNT = "eventCount",
    /** 事件处理时间 */
    EVENT_PROCESSING_TIME = "eventProcessingTime",
    /** 垃圾回收时间 */
    GC_TIME = "gcTime",
    /** 垃圾回收次数 */
    GC_COUNT = "gcCount",
    /** 自定义指标 */
    CUSTOM = "custom"
}
/**
 * 性能指标
 */
export interface PerformanceMetric {
    /** 指标类型 */
    type: PerformanceMetricType;
    /** 指标名称 */
    name: string;
    /** 当前值 */
    value: number;
    /** 最小值 */
    min: number;
    /** 最大值 */
    max: number;
    /** 平均值 */
    average: number;
    /** 历史值 */
    history: number[];
    /** 历史长度限制 */
    historyLimit: number;
    /** 单位 */
    unit?: string;
    /** 阈值 */
    threshold?: number;
    /** 是否超过阈值 */
    exceedsThreshold?: boolean;
}
/**
 * 性能瓶颈类型
 */
export declare enum PerformanceBottleneckType {
    /** 无瓶颈 */
    NONE = "none",
    /** CPU瓶颈 */
    CPU = "cpu",
    /** GPU瓶颈 */
    GPU = "gpu",
    /** 内存瓶颈 */
    MEMORY = "memory",
    /** 网络瓶颈 */
    NETWORK = "network",
    /** 渲染瓶颈 */
    RENDERING = "rendering",
    /** 物理瓶颈 */
    PHYSICS = "physics",
    /** 脚本瓶颈 */
    SCRIPT = "script",
    /** 资源瓶颈 */
    RESOURCES = "resources",
    /** 未知瓶颈 */
    UNKNOWN = "unknown"
}
/**
 * 性能瓶颈
 */
export interface PerformanceBottleneck {
    /** 瓶颈类型 */
    type: PerformanceBottleneckType;
    /** 瓶颈严重程度 (0-1) */
    severity: number;
    /** 瓶颈描述 */
    description: string;
    /** 相关指标 */
    relatedMetrics: string[];
    /** 建议优化措施 */
    optimizationSuggestions: string[];
}
/**
 * 性能趋势类型
 */
export declare enum PerformanceTrendType {
    /** 稳定 */
    STABLE = "stable",
    /** 改善 */
    IMPROVING = "improving",
    /** 恶化 */
    DEGRADING = "degrading",
    /** 波动 */
    FLUCTUATING = "fluctuating",
    /** 未知 */
    UNKNOWN = "unknown"
}
/**
 * 性能趋势
 */
export interface PerformanceTrend {
    /** 趋势类型 */
    type: PerformanceTrendType;
    /** 指标类型 */
    metricType: PerformanceMetricType;
    /** 变化率 */
    changeRate: number;
    /** 趋势开始时间 */
    startTime: number;
    /** 趋势持续时间 */
    duration: number;
}
/**
 * 性能报告
 */
export interface PerformanceReport {
    /** 报告时间 */
    timestamp: number;
    /** 性能指标 */
    metrics: {
        [key: string]: PerformanceMetric;
    };
    /** 性能瓶颈 */
    bottlenecks: PerformanceBottleneck[];
    /** 性能趋势 */
    trends: PerformanceTrend[];
    /** 总体性能评分 (0-100) */
    overallScore: number;
    /** 性能状态 */
    status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
    /** 自定义数据 */
    customData?: {
        [key: string]: any;
    };
}
/**
 * 性能监控配置
 */
export interface PerformanceMonitorConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 采样间隔（毫秒） */
    sampleInterval?: number;
    /** 历史长度限制 */
    historyLimit?: number;
    /** 是否启用自动采样 */
    autoSample?: boolean;
    /** 是否启用调试输出 */
    debug?: boolean;
    /** 是否启用性能警告 */
    enableWarnings?: boolean;
    /** 是否收集渲染指标 */
    collectRenderMetrics?: boolean;
    /** 是否收集物理指标 */
    collectPhysicsMetrics?: boolean;
    /** 是否收集内存指标 */
    collectMemoryMetrics?: boolean;
    /** 是否收集系统指标 */
    collectSystemMetrics?: boolean;
    /** 是否收集GPU指标 */
    collectGPUMetrics?: boolean;
    /** 是否收集CPU指标 */
    collectCPUMetrics?: boolean;
    /** 是否收集网络指标 */
    collectNetworkMetrics?: boolean;
    /** 是否收集资源指标 */
    collectResourceMetrics?: boolean;
    /** 是否收集事件指标 */
    collectEventMetrics?: boolean;
    /** 是否收集垃圾回收指标 */
    collectGCMetrics?: boolean;
    /** 是否启用瓶颈检测 */
    enableBottleneckDetection?: boolean;
    /** 是否启用趋势分析 */
    enableTrendAnalysis?: boolean;
    /** 是否启用性能评分 */
    enablePerformanceScoring?: boolean;
    /** 是否启用优化建议 */
    enableOptimizationSuggestions?: boolean;
    /** 是否启用自动优化 */
    enableAutoOptimization?: boolean;
    /** 是否启用性能报告导出 */
    enableReportExport?: boolean;
    /** 性能报告导出间隔（毫秒） */
    reportExportInterval?: number;
    /** 性能报告导出格式 */
    reportExportFormat?: 'json' | 'csv' | 'html';
    /** 性能报告导出路径 */
    reportExportPath?: string;
    /** 是否启用远程监控 */
    enableRemoteMonitoring?: boolean;
    /** 远程监控服务器URL */
    remoteMonitoringURL?: string;
    /** 远程监控认证令牌 */
    remoteMonitoringToken?: string;
    /** 远程监控数据发送间隔（毫秒） */
    remoteMonitoringSendInterval?: number;
}
/**
 * 性能监控系统
 */
export declare class PerformanceMonitor {
    /** 单例实例 */
    private static instance;
    /** 配置 */
    private config;
    /** 性能指标 */
    private metrics;
    /** 是否正在运行 */
    private running;
    /** 采样定时器ID */
    private sampleTimerId;
    /** 帧开始时间 */
    private frameStartTime;
    /** 上一帧时间 */
    private lastFrameTime;
    /** 帧计数 */
    private frameCount;
    /** 测量开始时间映射 */
    private measureStartTimes;
    /** 性能瓶颈 */
    private bottlenecks;
    /** 性能趋势 */
    private trends;
    /** 上次分析时间 */
    private lastAnalysisTime;
    /** 总体性能评分 */
    private overallScore;
    /** 性能状态 */
    private performanceStatus;
    /** 报告导出定时器ID */
    private reportExportTimerId;
    /** 远程监控定时器ID */
    private remoteMonitoringTimerId;
    /** 瓶颈检测定时器ID */
    private bottleneckDetectionTimerId;
    /** 趋势分析定时器ID */
    private trendAnalysisTimerId;
    /** 性能历史数据 */
    private performanceHistory;
    /** 优化建议缓存 */
    private optimizationSuggestions;
    /**
     * 获取单例实例
     */
    static getInstance(): PerformanceMonitor;
    /**
     * 私有构造函数
     */
    private constructor();
    /**
     * 初始化分析功能
     */
    private initializeAnalytics;
    /**
     * 初始化指标
     */
    private initializeMetrics;
    /**
     * 配置性能监控系统
     * @param config 配置
     */
    configure(config: PerformanceMonitorConfig): void;
    /**
     * 启动监控
     */
    start(): void;
    /**
     * 停止监控
     */
    stop(): void;
    /**
     * 停止所有定时器
     * @private
     */
    private stopAllTimers;
    /**
     * 启动自动采样
     */
    private startAutoSampling;
    /**
     * 启动瓶颈检测
     */
    private startBottleneckDetection;
    /**
     * 启动趋势分析
     */
    private startTrendAnalysis;
    /**
     * 启动报告导出
     */
    private startReportExport;
    /**
     * 启动远程监控
     */
    private startRemoteMonitoring;
    /**
     * 记录帧开始
     */
    beginFrame(): void;
    /**
     * 记录帧结束
     */
    endFrame(): void;
    /**
     * 开始测量
     * @param name 测量名称
     */
    beginMeasure(name: string): void;
    /**
     * 结束测量
     * @param name 测量名称
     * @param metricType 指标类型
     */
    endMeasure(name: string, metricType?: PerformanceMetricType): void;
    /**
     * 采样
     */
    sample(): void;
    /**
     * 保存性能报告到历史记录
     * @private
     */
    private savePerformanceHistory;
    /**
     * 收集内存指标
     */
    private collectMemoryMetrics;
    /**
     * 收集渲染指标
     */
    private collectRenderMetrics;
    /**
     * 收集系统指标
     */
    private collectSystemMetrics;
    /**
     * 收集GPU指标
     */
    private collectGPUMetrics;
    /**
     * 收集CPU指标
     */
    private collectCPUMetrics;
    /**
     * 收集网络指标
     */
    private collectNetworkMetrics;
    /**
     * 收集资源指标
     */
    private collectResourceMetrics;
    /**
     * 收集事件指标
     */
    private collectEventMetrics;
    /**
     * 收集垃圾回收指标
     */
    private collectGCMetrics;
    /**
     * 检查性能警告
     */
    private checkPerformanceWarnings;
    /**
     * 注册指标
     * @param type 指标类型
     * @param name 指标名称
     * @param initialValue 初始值
     * @param unit 单位
     * @param threshold 阈值
     */
    registerMetric(type: PerformanceMetricType, name: string, initialValue?: number, unit?: string, threshold?: number): void;
    /**
     * 注册自定义指标
     * @param name 指标名称
     * @param initialValue 初始值
     * @param unit 单位
     * @param threshold 阈值
     */
    registerCustomMetric(name: string, initialValue?: number, unit?: string, threshold?: number): void;
    /**
     * 更新指标
     * @param type 指标类型
     * @param value 值
     */
    updateMetric(type: PerformanceMetricType, value: number): void;
    /**
     * 更新自定义指标
     * @param name 指标名称
     * @param value 值
     * @param customData 自定义数据
     */
    updateCustomMetric(name: string, value: number, customData?: any): void;
    /**
     * 检测性能瓶颈
     * @private
     */
    private detectBottlenecks;
    /**
     * 检测FPS瓶颈
     * @private
     */
    private detectFPSBottleneck;
    /**
     * 检测CPU瓶颈
     * @private
     */
    private detectCPUBottleneck;
    /**
     * 检测GPU瓶颈
     * @private
     */
    private detectGPUBottleneck;
    /**
     * 检测内存瓶颈
     * @private
     */
    private detectMemoryBottleneck;
    /**
     * 检测渲染瓶颈
     * @private
     */
    private detectRenderingBottleneck;
    /**
     * 检测物理瓶颈
     * @private
     */
    private detectPhysicsBottleneck;
    /**
     * 检测网络瓶颈
     * @private
     */
    private detectNetworkBottleneck;
    /**
     * 检测资源瓶颈
     * @private
     */
    private detectResourceBottleneck;
    /**
     * 更新性能评分
     * @private
     */
    private updatePerformanceScore;
    /**
     * 分析性能趋势
     * @private
     */
    private analyzeTrends;
    /**
     * 分析指标趋势
     * @param metricType 指标类型
     * @private
     */
    private analyzeMetricTrend;
    /**
     * 计算标准差
     * @param values 数值数组
     * @private
     */
    private calculateStandardDeviation;
    /**
     * 导出性能报告
     * @private
     */
    private exportReport;
    /**
     * 发送远程监控数据
     * @private
     */
    private sendRemoteMonitoringData;
    /**
     * 获取性能报告
     * @returns 性能报告
     */
    getReport(): PerformanceReport;
    /**
     * 获取指标
     * @param type 指标类型
     * @returns 指标
     */
    getMetric(type: PerformanceMetricType): PerformanceMetric | undefined;
    /**
     * 获取自定义指标
     * @param name 指标名称
     * @returns 指标
     */
    getCustomMetric(name: string): PerformanceMetric | undefined;
    /**
     * 重置指标
     * @param type 指标类型
     */
    resetMetric(type: PerformanceMetricType): void;
    /**
     * 重置所有指标
     */
    resetAllMetrics(): void;
    /**
     * 清除历史数据
     * @param type 指标类型
     */
    clearHistory(type: PerformanceMetricType): void;
    /**
     * 清除所有历史数据
     */
    clearAllHistory(): void;
    /**
     * 启动监控
     */
    static start(): void;
    /**
     * 停止监控
     */
    static stop(): void;
    /**
     * 记录帧开始
     */
    static beginFrame(): void;
    /**
     * 记录帧结束
     */
    static endFrame(): void;
    /**
     * 开始测量
     * @param name 测量名称
     */
    static beginMeasure(name: string): void;
    /**
     * 结束测量
     * @param name 测量名称
     * @param metricType 指标类型
     */
    static endMeasure(name: string, metricType?: PerformanceMetricType): void;
    /**
     * 采样
     */
    static sample(): void;
    /**
     * 获取性能报告
     * @returns 性能报告
     */
    static getReport(): PerformanceReport;
}
