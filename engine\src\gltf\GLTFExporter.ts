/**
 * GLTF导出器
 * 用于导出场景或对象为GLTF格式
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { Scene } from '../scene/Scene';
import { EventEmitter } from '../utils/EventEmitter';

// 简化的GLTFExporter类型定义
declare class ThreeGLTFExporter {
  parse(
    input: THREE.Object3D | THREE.Object3D[] | THREE.Scene,
    onCompleted: (gltf: any) => void,
    onError: (error: any) => void,
    options?: any
  ): void;
}

/**
 * GLTF导出选项
 */
export interface GLTFExportOptions {
  /** 是否导出动画 */
  animations?: THREE.AnimationClip[];
  /** 是否二进制格式 */
  binary?: boolean;
  /** 是否包含自定义属性 */
  includeCustomExtensions?: boolean;
  /** 是否仅导出可见对象 */
  onlyVisible?: boolean;
  /** 是否压缩几何体 */
  truncateDrawRange?: boolean;
  /** 是否强制索引 */
  forceIndices?: boolean;
  /** 最大UV数量 */
  maxTextureSize?: number;
  /** 是否导出变换 */
  exportTransforms?: boolean;
}

/**
 * GLTF导出器
 */
export class GLTFExporter extends EventEmitter {
  /** Three.js GLTF导出器 */
  private exporter: ThreeGLTFExporter;

  /**
   * 创建GLTF导出器
   */
  constructor() {
    super();

    // 动态导入GLTFExporter
    this.exporter = new (THREE as any).GLTFExporter();
  }

  /**
   * 导出场景
   * @param scene 场景
   * @param options 导出选项
   * @returns Promise，解析为导出的GLTF数据
   */
  public async exportScene(scene: Scene, options: GLTFExportOptions = {}): Promise<ArrayBuffer | object> {
    // 获取Three.js场景
    const threeScene = scene.getThreeScene();

    // 导出场景
    return this.export(threeScene, options);
  }

  /**
   * 导出实体
   * @param entity 实体
   * @param options 导出选项
   * @returns Promise，解析为导出的GLTF数据
   */
  public async exportEntity(entity: Entity, options: GLTFExportOptions = {}): Promise<ArrayBuffer | object> {
    // 获取实体的变换组件
    const transform = entity.getTransform();
    if (!transform) {
      throw new Error('实体没有变换组件');
    }

    // 获取Three.js对象
    const object = transform.getObject3D();

    // 导出对象
    return this.export(object, options);
  }

  /**
   * 导出对象
   * @param input 输入对象（Three.js对象、场景或数组）
   * @param options 导出选项
   * @returns Promise，解析为导出的GLTF数据
   */
  public async export(
    input: THREE.Object3D | THREE.Object3D[] | THREE.Scene,
    options: GLTFExportOptions = {}
  ): Promise<ArrayBuffer | object> {
    return new Promise((resolve, reject) => {
      try {
        this.exporter.parse(
          input,
          (gltf) => {
            resolve(gltf);
            this.emit('exported', { input, gltf });
          },
          (error) => {
            reject(new Error(`导出GLTF失败: ${error.message}`));
            this.emit('error', error);
          },
          {
            animations: options.animations || [],
            binary: options.binary !== undefined ? options.binary : false,
            includeCustomExtensions: options.includeCustomExtensions !== undefined ? options.includeCustomExtensions : false,
            onlyVisible: options.onlyVisible !== undefined ? options.onlyVisible : true,
            truncateDrawRange: options.truncateDrawRange !== undefined ? options.truncateDrawRange : true,
            forceIndices: options.forceIndices !== undefined ? options.forceIndices : false,
            maxTextureSize: options.maxTextureSize || Infinity
          }
        );
      } catch (error) {
        reject(error);
        this.emit('error', error);
      }
    });
  }

  /**
   * 将GLTF数据保存为文件
   * @param gltf GLTF数据
   * @param filename 文件名
   */
  public saveAs(gltf: ArrayBuffer | object, filename: string): void {
    let blob: Blob;

    if (gltf instanceof ArrayBuffer) {
      // 二进制GLTF
      blob = new Blob([gltf], { type: 'application/octet-stream' });

      // 如果没有扩展名，添加.glb扩展名
      if (!filename.toLowerCase().endsWith('.glb')) {
        filename += '.glb';
      }
    } else {
      // JSON GLTF
      const json = JSON.stringify(gltf, null, 2);
      blob = new Blob([json], { type: 'application/json' });

      // 如果没有扩展名，添加.gltf扩展名
      if (!filename.toLowerCase().endsWith('.gltf')) {
        filename += '.gltf';
      }
    }

    // 创建下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();

    // 清理
    URL.revokeObjectURL(link.href);
  }
}
