<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>物理系统演示</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      font-family: 'Microsoft YaHei', sans-serif;
    }
    #canvas-container {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    #ui-container {
      position: absolute;
      top: 10px;
      left: 10px;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 10px;
      border-radius: 5px;
      width: 300px;
      z-index: 10;
    }
    h2 {
      margin-top: 0;
      color: #333;
    }
    .demo-group {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 5px;
    }
    .demo-title {
      font-weight: bold;
      color: #4caf50;
      margin-bottom: 5px;
    }
    .demo-description {
      font-size: 14px;
      color: #555;
      margin-bottom: 10px;
    }
    .control-group {
      margin-bottom: 10px;
    }
    label {
      display: block;
      margin-bottom: 3px;
      font-size: 14px;
    }
    input[type="range"] {
      width: 100%;
    }
    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 10px;
    }
    button {
      background-color: #4caf50;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 3px;
      cursor: pointer;
      flex-grow: 1;
      min-width: 80px;
    }
    button:hover {
      background-color: #45a049;
    }
    .info-box {
      background-color: #e8f5e9;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      font-size: 14px;
      color: #2e7d32;
      border-left: 4px solid #4caf50;
    }
    .physics-controls {
      margin-top: 15px;
    }
    .tab-buttons {
      display: flex;
      margin-bottom: 10px;
    }
    .tab-button {
      flex-grow: 1;
      padding: 8px;
      background-color: #e0e0e0;
      border: none;
      cursor: pointer;
      text-align: center;
    }
    .tab-button.active {
      background-color: #4caf50;
      color: white;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
  </style>
</head>
<body>
  <div id="canvas-container"></div>
  <div id="ui-container">
    <h2>物理系统演示</h2>
    
    <div class="info-box">
      本示例展示了DL（Digital Learning）引擎的物理系统功能，包括刚体物理、碰撞检测、物理约束等。
      使用下方的控制面板来尝试不同的物理效果。
    </div>
    
    <div class="tab-buttons">
      <button class="tab-button active" data-tab="basic">基础物理</button>
      <button class="tab-button" data-tab="constraints">物理约束</button>
      <button class="tab-button" data-tab="materials">物理材质</button>
    </div>
    
    <div id="basic-tab" class="tab-content active">
      <div class="demo-group">
        <div class="demo-title">刚体物理</div>
        <div class="demo-description">添加不同形状的刚体，观察它们的物理行为。</div>
        
        <div class="button-group">
          <button id="add-cube">添加立方体</button>
          <button id="add-sphere">添加球体</button>
          <button id="add-cylinder">添加圆柱体</button>
        </div>
        
        <div class="control-group">
          <label for="gravity-strength">重力强度: <span id="gravity-strength-value">9.8</span></label>
          <input type="range" id="gravity-strength" min="0" max="20" step="0.1" value="9.8">
        </div>
        
        <div class="control-group">
          <label for="restitution">弹性系数: <span id="restitution-value">0.5</span></label>
          <input type="range" id="restitution" min="0" max="1" step="0.01" value="0.5">
        </div>
      </div>
      
      <div class="demo-group">
        <div class="demo-title">射线检测</div>
        <div class="demo-description">使用射线检测与场景中的物体交互。</div>
        
        <div class="button-group">
          <button id="toggle-ray">显示/隐藏射线</button>
          <button id="apply-force">应用力</button>
        </div>
      </div>
    </div>
    
    <div id="constraints-tab" class="tab-content">
      <div class="demo-group">
        <div class="demo-title">物理约束</div>
        <div class="demo-description">创建不同类型的物理约束，控制物体之间的关系。</div>
        
        <div class="button-group">
          <button id="create-hinge">铰链约束</button>
          <button id="create-distance">距离约束</button>
          <button id="create-point">点对点约束</button>
        </div>
        
        <div class="control-group">
          <label for="constraint-strength">约束强度: <span id="constraint-strength-value">1.0</span></label>
          <input type="range" id="constraint-strength" min="0" max="2" step="0.01" value="1.0">
        </div>
      </div>
      
      <div class="demo-group">
        <div class="demo-title">复合约束</div>
        <div class="demo-description">创建由多个约束组成的复杂物理系统。</div>
        
        <div class="button-group">
          <button id="create-pendulum">创建摆锤</button>
          <button id="create-chain">创建链条</button>
          <button id="create-ragdoll">创建布娃娃</button>
        </div>
      </div>
    </div>
    
    <div id="materials-tab" class="tab-content">
      <div class="demo-group">
        <div class="demo-title">物理材质</div>
        <div class="demo-description">尝试不同的物理材质，观察它们的物理特性。</div>
        
        <div class="button-group">
          <button id="material-wood">木材</button>
          <button id="material-metal">金属</button>
          <button id="material-rubber">橡胶</button>
          <button id="material-ice">冰</button>
        </div>
        
        <div class="control-group">
          <label for="friction">摩擦系数: <span id="friction-value">0.5</span></label>
          <input type="range" id="friction" min="0" max="1" step="0.01" value="0.5">
        </div>
      </div>
      
      <div class="demo-group">
        <div class="demo-title">物理预设</div>
        <div class="demo-description">加载预设的物理场景。</div>
        
        <div class="button-group">
          <button id="preset-bowling">保龄球</button>
          <button id="preset-domino">多米诺骨牌</button>
          <button id="preset-jenga">叠叠乐</button>
        </div>
      </div>
    </div>
    
    <div class="button-group">
      <button id="reset-scene">重置场景</button>
      <button id="toggle-debug">显示/隐藏调试</button>
    </div>
  </div>

  <script type="module">
    import { Engine, World, Entity, Scene, Transform, Camera, Mesh, Material, Light, PhysicsSystem, PhysicsBody, PhysicsCollider, PhysicsConstraint, PhysicsMaterial, Debug } from '/engine/dist/index.js';

    // 初始化引擎
    const engine = new Engine({
      container: document.getElementById('canvas-container'),
      antialias: true,
      shadows: true,
    });

    // 创建世界
    const world = new World(engine);

    // 创建物理系统
    const physicsSystem = new PhysicsSystem(world, {
      gravity: { x: 0, y: -9.8, z: 0 },
      debug: false
    });
    world.addSystem(physicsSystem);

    // 创建场景
    const scene = new Scene(world, {
      name: '物理系统演示场景',
      background: { type: 'color', value: '#87CEEB' },
    });

    // 创建相机
    const camera = new Entity(world)
      .addComponent(new Camera({
        type: 'perspective',
        fov: 60,
        near: 0.1,
        far: 1000,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 5, z: 10 },
        rotation: { x: -0.3, y: 0, z: 0 },
      }));

    // 添加相机到场景
    scene.addEntity(camera);

    // 创建环境光
    const ambientLight = new Entity(world)
      .addComponent(new Light({
        type: 'ambient',
        color: '#ffffff',
        intensity: 0.3,
      }));

    // 添加环境光到场景
    scene.addEntity(ambientLight);

    // 创建平行光
    const directionalLight = new Entity(world)
      .addComponent(new Light({
        type: 'directional',
        color: '#ffffff',
        intensity: 0.8,
        castShadow: true,
      }))
      .addComponent(new Transform({
        position: { x: 5, y: 10, z: 5 },
        rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
      }));

    // 添加平行光到场景
    scene.addEntity(directionalLight);

    // 创建地面
    const ground = new Entity(world)
      .addComponent(new Mesh({
        geometry: { type: 'plane', width: 20, height: 20 },
        receiveShadow: true,
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#cccccc',
        roughness: 0.9,
        metalness: 0.1,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: -Math.PI / 2, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      }))
      .addComponent(new PhysicsCollider({
        type: 'plane',
        restitution: 0.5,
        friction: 0.5,
      }))
      .addComponent(new PhysicsBody({
        type: 'static',
      }));

    // 添加地面到场景
    scene.addEntity(ground);

    // 创建墙壁
    const createWall = (x, y, z, width, height, depth, rotation = { x: 0, y: 0, z: 0 }) => {
      const wall = new Entity(world)
        .addComponent(new Mesh({
          geometry: { type: 'box', width, height, depth },
          receiveShadow: true,
          castShadow: true,
        }))
        .addComponent(new Material({
          type: 'standard',
          color: '#aaaaaa',
          roughness: 0.7,
          metalness: 0.2,
        }))
        .addComponent(new Transform({
          position: { x, y, z },
          rotation,
          scale: { x: 1, y: 1, z: 1 },
        }))
        .addComponent(new PhysicsCollider({
          type: 'box',
          width, height, depth,
          restitution: 0.3,
          friction: 0.5,
        }))
        .addComponent(new PhysicsBody({
          type: 'static',
        }));

      scene.addEntity(wall);
      return wall;
    };

    // 创建四面墙
    createWall(0, 2, -10, 20, 4, 0.5);
    createWall(0, 2, 10, 20, 4, 0.5);
    createWall(-10, 2, 0, 0.5, 4, 20);
    createWall(10, 2, 0, 0.5, 4, 20);

    // 创建物理对象函数
    const createPhysicsObject = (type, position, size, material) => {
      let geometry, collider;
      
      switch (type) {
        case 'cube':
          geometry = { type: 'box', width: size, height: size, depth: size };
          collider = { type: 'box', width: size, height: size, depth: size };
          break;
        case 'sphere':
          geometry = { type: 'sphere', radius: size / 2, widthSegments: 32, heightSegments: 32 };
          collider = { type: 'sphere', radius: size / 2 };
          break;
        case 'cylinder':
          geometry = { type: 'cylinder', radiusTop: size / 2, radiusBottom: size / 2, height: size, radialSegments: 32 };
          collider = { type: 'cylinder', radius: size / 2, height: size };
          break;
      }
      
      const restitution = parseFloat(document.getElementById('restitution').value);
      const friction = parseFloat(document.getElementById('friction').value);
      
      const entity = new Entity(world)
        .addComponent(new Mesh({
          geometry,
          castShadow: true,
          receiveShadow: true,
        }))
        .addComponent(new Material(material))
        .addComponent(new Transform({
          position,
          rotation: { 
            x: Math.random() * Math.PI, 
            y: Math.random() * Math.PI, 
            z: Math.random() * Math.PI 
          },
          scale: { x: 1, y: 1, z: 1 },
        }))
        .addComponent(new PhysicsCollider({
          ...collider,
          restitution,
          friction,
        }))
        .addComponent(new PhysicsBody({
          type: 'dynamic',
          mass: 1,
        }));

      scene.addEntity(entity);
      return entity;
    };

    // 启动引擎
    engine.start();

    // UI控制
    document.getElementById('add-cube').addEventListener('click', () => {
      createPhysicsObject('cube', { 
        x: (Math.random() - 0.5) * 8, 
        y: 5 + Math.random() * 5, 
        z: (Math.random() - 0.5) * 8 
      }, 1, {
        type: 'standard',
        color: '#ff0000',
        roughness: 0.7,
        metalness: 0.3,
      });
    });

    document.getElementById('add-sphere').addEventListener('click', () => {
      createPhysicsObject('sphere', { 
        x: (Math.random() - 0.5) * 8, 
        y: 5 + Math.random() * 5, 
        z: (Math.random() - 0.5) * 8 
      }, 1, {
        type: 'standard',
        color: '#00ff00',
        roughness: 0.2,
        metalness: 0.8,
      });
    });

    document.getElementById('add-cylinder').addEventListener('click', () => {
      createPhysicsObject('cylinder', { 
        x: (Math.random() - 0.5) * 8, 
        y: 5 + Math.random() * 5, 
        z: (Math.random() - 0.5) * 8 
      }, 1, {
        type: 'standard',
        color: '#0000ff',
        roughness: 0.5,
        metalness: 0.5,
      });
    });

    document.getElementById('gravity-strength').addEventListener('input', (e) => {
      const value = parseFloat(e.target.value);
      document.getElementById('gravity-strength-value').textContent = value;
      physicsSystem.setGravity({ x: 0, y: -value, z: 0 });
    });

    document.getElementById('restitution').addEventListener('input', (e) => {
      document.getElementById('restitution-value').textContent = e.target.value;
    });

    document.getElementById('friction').addEventListener('input', (e) => {
      document.getElementById('friction-value').textContent = e.target.value;
    });

    document.getElementById('reset-scene').addEventListener('click', () => {
      location.reload();
    });

    document.getElementById('toggle-debug').addEventListener('click', () => {
      physicsSystem.toggleDebug();
    });

    // 标签切换
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.getAttribute('data-tab');
        
        // 更新按钮状态
        tabButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        // 更新内容状态
        tabContents.forEach(content => content.classList.remove('active'));
        document.getElementById(`${tabId}-tab`).classList.add('active');
      });
    });

    // 物理约束相关功能
    document.getElementById('create-hinge').addEventListener('click', () => {
      // 创建铰链约束示例
      const base = createPhysicsObject('cube', { x: 0, y: 3, z: 0 }, 1, {
        type: 'standard',
        color: '#ff5722',
        roughness: 0.7,
        metalness: 0.3,
      });
      
      const pendulum = createPhysicsObject('sphere', { x: 0, y: 1, z: 0 }, 0.8, {
        type: 'standard',
        color: '#2196f3',
        roughness: 0.2,
        metalness: 0.8,
      });
      
      // 添加铰链约束
      const constraint = new PhysicsConstraint({
        type: 'hinge',
        bodyA: base.getComponent(PhysicsBody),
        bodyB: pendulum.getComponent(PhysicsBody),
        pivotA: { x: 0, y: -0.5, z: 0 },
        pivotB: { x: 0, y: 0.4, z: 0 },
        axisA: { x: 0, y: 0, z: 1 },
        axisB: { x: 0, y: 0, z: 1 },
      });
      
      world.addComponent(constraint, base);
    });

    // 物理材质相关功能
    document.getElementById('material-wood').addEventListener('click', () => {
      createPhysicsObject('cube', { 
        x: (Math.random() - 0.5) * 8, 
        y: 5 + Math.random() * 5, 
        z: (Math.random() - 0.5) * 8 
      }, 1, {
        type: 'standard',
        color: '#8d6e63',
        roughness: 0.9,
        metalness: 0.1,
      });
    });

    document.getElementById('material-metal').addEventListener('click', () => {
      createPhysicsObject('sphere', { 
        x: (Math.random() - 0.5) * 8, 
        y: 5 + Math.random() * 5, 
        z: (Math.random() - 0.5) * 8 
      }, 1, {
        type: 'standard',
        color: '#b0bec5',
        roughness: 0.1,
        metalness: 0.9,
      });
    });

    document.getElementById('material-rubber').addEventListener('click', () => {
      createPhysicsObject('sphere', { 
        x: (Math.random() - 0.5) * 8, 
        y: 5 + Math.random() * 5, 
        z: (Math.random() - 0.5) * 8 
      }, 1, {
        type: 'standard',
        color: '#212121',
        roughness: 0.9,
        metalness: 0.0,
      });
    });
  </script>
</body>
</html>
