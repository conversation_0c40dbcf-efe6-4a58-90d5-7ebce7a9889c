/**
 * 缓存装饰器
 */
import { SetMetadata, UseInterceptors, applyDecorators, ExecutionContext } from '@nestjs/common';
import { CACHE_KEY_METADATA, CACHE_TTL_METADATA } from './cache.constants';
import { CacheInterceptor } from '../interceptors/cache.interceptor';
import { DistributedCacheInterceptor } from '../interceptors/distributed-cache.interceptor';

/**
 * 缓存键生成器函数类型
 */
export type CacheKeyGenerator = (context: ExecutionContext) => string;

/**
 * 缓存装饰器选项
 */
export interface CacheOptions {
  /**
   * 缓存键
   */
  key?: string;

  /**
   * 缓存TTL（毫秒）
   */
  ttl?: number;

  /**
   * 缓存键生成器
   */
  keyGenerator?: CacheKeyGenerator;

  /**
   * 是否使用分布式缓存
   */
  distributed?: boolean;
}

/**
 * 缓存装饰器
 * @param options 缓存选项
 * @returns 装饰器
 */
export function UseCache(options: CacheOptions = {}) {
  const decorators = [
    UseInterceptors(options.distributed ? DistributedCacheInterceptor : CacheInterceptor)
  ];

  if (options.key) {
    decorators.push(SetMetadata(CACHE_KEY_METADATA, options.key));
  }

  if (options.ttl) {
    decorators.push(SetMetadata(CACHE_TTL_METADATA, options.ttl));
  }

  if (options.keyGenerator) {
    decorators.push(SetMetadata('cache:key-generator', options.keyGenerator));
  }

  return applyDecorators(...decorators);
}

/**
 * 缓存TTL装饰器
 * @param ttl 缓存TTL（毫秒）
 */
export const CacheTTL = (ttl: number): MethodDecorator => SetMetadata(CACHE_TTL_METADATA, ttl);

/**
 * 缓存键生成器装饰器
 * @param keyGenerator 缓存键生成器
 */
export const CacheKeyGenerator = (keyGenerator: CacheKeyGenerator): MethodDecorator =>
  SetMetadata('cache:key-generator', keyGenerator);

/**
 * 缓存键前缀装饰器
 * @param prefix 缓存键前缀
 */
export function CacheKeyPrefix(prefix: string): MethodDecorator {
  return CacheKeyGenerator((context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest();
    const url = request.originalUrl || request.url;
    return `${prefix}:${url}`;
  });
}

/**
 * 用户缓存键装饰器
 * 根据用户ID生成缓存键
 */
export function UserCache(): MethodDecorator {
  return CacheKeyGenerator((context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest();
    const userId = request.user?.id || 'anonymous';
    const controller = context.getClass().name;
    const handler = context.getHandler().name;
    const url = request.originalUrl || request.url;

    return `user:${userId}:${controller}:${handler}:${url}`;
  });
}
