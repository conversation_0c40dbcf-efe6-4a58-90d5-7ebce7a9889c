/**
 * 带宽控制器
 * 负责管理网络带宽使用
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';
import { NetworkQualityData, NetworkQualityLevel } from './NetworkQualityMonitor';

/**
 * 带宽控制策略
 */
export enum BandwidthControlStrategy {
  /** 固定带宽 */
  FIXED = 'fixed',
  /** 自适应带宽 */
  ADAPTIVE = 'adaptive',
  /** 质量优先 */
  QUALITY_FIRST = 'quality_first',
  /** 性能优先 */
  PERFORMANCE_FIRST = 'performance_first',
}

/**
 * 数据优先级
 */
export enum DataPriority {
  /** 最高优先级 */
  HIGHEST = 0,
  /** 高优先级 */
  HIGH = 1,
  /** 中等优先级 */
  MEDIUM = 2,
  /** 低优先级 */
  LOW = 3,
  /** 最低优先级 */
  LOWEST = 4,
}

/**
 * 带宽控制器配置
 */
export interface BandwidthControllerConfig {
  /** 最大上行带宽（字节/秒） */
  maxUploadBandwidth?: number;
  /** 最大下行带宽（字节/秒） */
  maxDownloadBandwidth?: number;
  /** 带宽控制策略 */
  strategy?: BandwidthControlStrategy;
  /** 带宽使用目标（0-1，表示最大带宽的使用比例） */
  targetUsage?: number;
  /** 带宽分配比例（按优先级） */
  priorityAllocation?: Record<DataPriority, number>;
  /** 是否启用自动调整 */
  autoAdjust?: boolean;
  /** 调整间隔（毫秒） */
  adjustInterval?: number;
}

/**
 * 带宽使用数据
 */
export interface BandwidthUsageData {
  /** 上行带宽使用（字节/秒） */
  upload: number;
  /** 下行带宽使用（字节/秒） */
  download: number;
  /** 上行带宽限制（字节/秒） */
  uploadLimit: number;
  /** 下行带宽限制（字节/秒） */
  downloadLimit: number;
  /** 上行带宽使用率（0-1） */
  uploadUsageRatio: number;
  /** 下行带宽使用率（0-1） */
  downloadUsageRatio: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 带宽控制器
 * 负责管理网络带宽使用
 */
export class BandwidthController extends EventEmitter {
  /** 配置 */
  private config: Required<BandwidthControllerConfig>;
  
  /** 当前上行带宽限制（字节/秒） */
  private currentUploadLimit: number;
  
  /** 当前下行带宽限制（字节/秒） */
  private currentDownloadLimit: number;
  
  /** 当前上行带宽使用（字节/秒） */
  private currentUploadUsage: number = 0;
  
  /** 当前下行带宽使用（字节/秒） */
  private currentDownloadUsage: number = 0;
  
  /** 上行数据计数（当前周期） */
  private uploadByteCount: number = 0;
  
  /** 下行数据计数（当前周期） */
  private downloadByteCount: number = 0;
  
  /** 上次计数重置时间 */
  private lastResetTime: number = Date.now();
  
  /** 调整定时器ID */
  private adjustTimerId: number | null = null;
  
  /** 优先级队列（按优先级存储待发送数据） */
  private priorityQueues: Map<DataPriority, any[]> = new Map();
  
  /** 优先级带宽分配 */
  private priorityBandwidthAllocation: Map<DataPriority, number> = new Map();
  
  /** 最近的网络质量数据 */
  private latestNetworkQuality: NetworkQualityData | null = null;
  
  /**
   * 创建带宽控制器
   * @param config 配置
   */
  constructor(config: BandwidthControllerConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      maxUploadBandwidth: 1024 * 1024, // 1MB/s
      maxDownloadBandwidth: 1024 * 1024, // 1MB/s
      strategy: BandwidthControlStrategy.ADAPTIVE,
      targetUsage: 0.8, // 80%
      priorityAllocation: {
        [DataPriority.HIGHEST]: 0.4, // 40%
        [DataPriority.HIGH]: 0.3, // 30%
        [DataPriority.MEDIUM]: 0.2, // 20%
        [DataPriority.LOW]: 0.07, // 7%
        [DataPriority.LOWEST]: 0.03, // 3%
      },
      autoAdjust: true,
      adjustInterval: 1000, // 1秒
      ...config,
    };
    
    // 初始化带宽限制
    this.currentUploadLimit = this.config.maxUploadBandwidth;
    this.currentDownloadLimit = this.config.maxDownloadBandwidth;
    
    // 初始化优先级队列
    this.initPriorityQueues();
    
    // 初始化优先级带宽分配
    this.updatePriorityBandwidthAllocation();
    
    // 如果启用自动调整，则启动调整定时器
    if (this.config.autoAdjust) {
      this.startAutoAdjust();
    }
  }
  
  /**
   * 初始化优先级队列
   */
  private initPriorityQueues(): void {
    this.priorityQueues.set(DataPriority.HIGHEST, []);
    this.priorityQueues.set(DataPriority.HIGH, []);
    this.priorityQueues.set(DataPriority.MEDIUM, []);
    this.priorityQueues.set(DataPriority.LOW, []);
    this.priorityQueues.set(DataPriority.LOWEST, []);
  }
  
  /**
   * 更新优先级带宽分配
   */
  private updatePriorityBandwidthAllocation(): void {
    const { priorityAllocation } = this.config;
    
    // 计算总分配比例
    const totalAllocation = Object.values(priorityAllocation).reduce((sum, value) => sum + value, 0);
    
    // 如果总分配比例不为1，则进行归一化
    const normalizationFactor = totalAllocation !== 0 ? 1 / totalAllocation : 1;
    
    // 更新每个优先级的带宽分配
    for (const priority of Object.values(DataPriority)) {
      if (typeof priority === 'number') {
        const allocation = priorityAllocation[priority] * normalizationFactor;
        this.priorityBandwidthAllocation.set(priority, allocation);
      }
    }
  }
  
  /**
   * 启动自动调整
   */
  public startAutoAdjust(): void {
    if (this.adjustTimerId !== null) {
      return;
    }
    
    this.adjustTimerId = window.setInterval(() => {
      this.adjustBandwidth();
      this.resetCounters();
    }, this.config.adjustInterval);
  }
  
  /**
   * 停止自动调整
   */
  public stopAutoAdjust(): void {
    if (this.adjustTimerId !== null) {
      clearInterval(this.adjustTimerId);
      this.adjustTimerId = null;
    }
  }
  
  /**
   * 调整带宽
   */
  private adjustBandwidth(): void {
    // 根据策略调整带宽
    switch (this.config.strategy) {
      case BandwidthControlStrategy.FIXED:
        // 固定带宽，不进行调整
        break;
        
      case BandwidthControlStrategy.ADAPTIVE:
        this.adjustAdaptive();
        break;
        
      case BandwidthControlStrategy.QUALITY_FIRST:
        this.adjustQualityFirst();
        break;
        
      case BandwidthControlStrategy.PERFORMANCE_FIRST:
        this.adjustPerformanceFirst();
        break;
    }
    
    // 更新优先级带宽分配
    this.updatePriorityBandwidthAllocation();
    
    // 触发带宽调整事件
    this.emit('bandwidthAdjusted', this.getBandwidthUsage());
  }
  
  /**
   * 自适应调整带宽
   */
  private adjustAdaptive(): void {
    const now = Date.now();
    const elapsed = (now - this.lastResetTime) / 1000; // 转换为秒
    
    if (elapsed > 0) {
      // 计算当前带宽使用
      this.currentUploadUsage = this.uploadByteCount / elapsed;
      this.currentDownloadUsage = this.downloadByteCount / elapsed;
      
      // 计算使用率
      const uploadUsageRatio = this.currentUploadLimit > 0 ? this.currentUploadUsage / this.currentUploadLimit : 0;
      const downloadUsageRatio = this.currentDownloadLimit > 0 ? this.currentDownloadUsage / this.currentDownloadLimit : 0;
      
      // 根据使用率调整带宽限制
      if (uploadUsageRatio > this.config.targetUsage * 1.1) {
        // 使用率过高，降低限制
        this.currentUploadLimit = Math.max(this.currentUploadLimit * 0.9, this.currentUploadUsage);
      } else if (uploadUsageRatio < this.config.targetUsage * 0.8) {
        // 使用率过低，提高限制
        this.currentUploadLimit = Math.min(this.currentUploadLimit * 1.1, this.config.maxUploadBandwidth);
      }
      
      if (downloadUsageRatio > this.config.targetUsage * 1.1) {
        // 使用率过高，降低限制
        this.currentDownloadLimit = Math.max(this.currentDownloadLimit * 0.9, this.currentDownloadUsage);
      } else if (downloadUsageRatio < this.config.targetUsage * 0.8) {
        // 使用率过低，提高限制
        this.currentDownloadLimit = Math.min(this.currentDownloadLimit * 1.1, this.config.maxDownloadBandwidth);
      }
    }
  }
  
  /**
   * 质量优先调整带宽
   */
  private adjustQualityFirst(): void {
    // 如果没有网络质量数据，则使用自适应调整
    if (!this.latestNetworkQuality) {
      this.adjustAdaptive();
      return;
    }
    
    // 根据网络质量调整带宽
    switch (this.latestNetworkQuality.level) {
      case NetworkQualityLevel.EXCELLENT:
        // 网络质量极好，使用最大带宽
        this.currentUploadLimit = this.config.maxUploadBandwidth;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth;
        break;
        
      case NetworkQualityLevel.GOOD:
        // 网络质量良好，使用90%带宽
        this.currentUploadLimit = this.config.maxUploadBandwidth * 0.9;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.9;
        break;
        
      case NetworkQualityLevel.MEDIUM:
        // 网络质量一般，使用70%带宽
        this.currentUploadLimit = this.config.maxUploadBandwidth * 0.7;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.7;
        break;
        
      case NetworkQualityLevel.BAD:
        // 网络质量差，使用50%带宽
        this.currentUploadLimit = this.config.maxUploadBandwidth * 0.5;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.5;
        break;
        
      case NetworkQualityLevel.VERY_BAD:
        // 网络质量极差，使用30%带宽
        this.currentUploadLimit = this.config.maxUploadBandwidth * 0.3;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.3;
        break;
        
      default:
        // 未知质量，使用自适应调整
        this.adjustAdaptive();
        break;
    }
  }
  
  /**
   * 性能优先调整带宽
   */
  private adjustPerformanceFirst(): void {
    // 如果没有网络质量数据，则使用自适应调整
    if (!this.latestNetworkQuality) {
      this.adjustAdaptive();
      return;
    }
    
    // 根据网络质量调整带宽，但更加激进地减少带宽使用
    switch (this.latestNetworkQuality.level) {
      case NetworkQualityLevel.EXCELLENT:
        // 网络质量极好，使用80%带宽
        this.currentUploadLimit = this.config.maxUploadBandwidth * 0.8;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.8;
        break;
        
      case NetworkQualityLevel.GOOD:
        // 网络质量良好，使用60%带宽
        this.currentUploadLimit = this.config.maxUploadBandwidth * 0.6;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.6;
        break;
        
      case NetworkQualityLevel.MEDIUM:
        // 网络质量一般，使用40%带宽
        this.currentUploadLimit = this.config.maxUploadBandwidth * 0.4;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.4;
        break;
        
      case NetworkQualityLevel.BAD:
        // 网络质量差，使用20%带宽
        this.currentUploadLimit = this.config.maxUploadBandwidth * 0.2;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.2;
        break;
        
      case NetworkQualityLevel.VERY_BAD:
        // 网络质量极差，使用10%带宽
        this.currentUploadLimit = this.config.maxUploadBandwidth * 0.1;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth * 0.1;
        break;
        
      default:
        // 未知质量，使用自适应调整
        this.adjustAdaptive();
        break;
    }
  }
  
  /**
   * 重置计数器
   */
  private resetCounters(): void {
    this.uploadByteCount = 0;
    this.downloadByteCount = 0;
    this.lastResetTime = Date.now();
  }
  
  /**
   * 记录上行数据
   * @param bytes 字节数
   */
  public recordUpload(bytes: number): void {
    this.uploadByteCount += bytes;
  }
  
  /**
   * 记录下行数据
   * @param bytes 字节数
   */
  public recordDownload(bytes: number): void {
    this.downloadByteCount += bytes;
  }
  
  /**
   * 获取带宽使用数据
   * @returns 带宽使用数据
   */
  public getBandwidthUsage(): BandwidthUsageData {
    return {
      upload: this.currentUploadUsage,
      download: this.currentDownloadUsage,
      uploadLimit: this.currentUploadLimit,
      downloadLimit: this.currentDownloadLimit,
      uploadUsageRatio: this.currentUploadLimit > 0 ? this.currentUploadUsage / this.currentUploadLimit : 0,
      downloadUsageRatio: this.currentDownloadLimit > 0 ? this.currentDownloadUsage / this.currentDownloadLimit : 0,
      timestamp: Date.now(),
    };
  }
  
  /**
   * 设置网络质量数据
   * @param quality 网络质量数据
   */
  public setNetworkQuality(quality: NetworkQualityData): void {
    this.latestNetworkQuality = quality;
  }
  
  /**
   * 设置带宽控制策略
   * @param strategy 带宽控制策略
   */
  public setStrategy(strategy: BandwidthControlStrategy): void {
    this.config.strategy = strategy;
  }
  
  /**
   * 获取带宽控制策略
   * @returns 带宽控制策略
   */
  public getStrategy(): BandwidthControlStrategy {
    return this.config.strategy;
  }
  
  /**
   * 设置最大上行带宽
   * @param bandwidth 带宽（字节/秒）
   */
  public setMaxUploadBandwidth(bandwidth: number): void {
    this.config.maxUploadBandwidth = bandwidth;
    
    // 确保当前限制不超过最大值
    if (this.currentUploadLimit > bandwidth) {
      this.currentUploadLimit = bandwidth;
    }
  }
  
  /**
   * 设置最大下行带宽
   * @param bandwidth 带宽（字节/秒）
   */
  public setMaxDownloadBandwidth(bandwidth: number): void {
    this.config.maxDownloadBandwidth = bandwidth;
    
    // 确保当前限制不超过最大值
    if (this.currentDownloadLimit > bandwidth) {
      this.currentDownloadLimit = bandwidth;
    }
  }
  
  /**
   * 销毁控制器
   */
  public dispose(): void {
    this.stopAutoAdjust();
    this.removeAllListeners();
  }
}
