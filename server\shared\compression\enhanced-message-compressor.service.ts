/**
 * 增强消息压缩服务
 * 提供多种压缩算法和自适应选择功能
 */
import { Injectable, Logger } from '@nestjs/common';
import * as zlib from 'zlib';
import { promisify } from 'util';

/**
 * 压缩算法枚举
 */
export enum CompressionAlgorithm {
  /** 不压缩 */
  NONE = 'none',
  /** DEFLATE算法 */
  DEFLATE = 'deflate',
  /** GZIP算法 */
  GZIP = 'gzip',
  /** Brotli算法 */
  BROTLI = 'brotli',
  /** DEFLATE Raw算法 */
  DEFLATE_RAW = 'deflate-raw',
}

/**
 * 压缩级别枚举
 */
export enum CompressionLevel {
  /** 无压缩 */
  NONE = 0,
  /** 最快压缩 */
  FASTEST = 1,
  /** 快速压缩 */
  FAST = 3,
  /** 默认压缩 */
  DEFAULT = 6,
  /** 高压缩 */
  HIGH = 7,
  /** 最高压缩 */
  MAXIMUM = 9,
}

/**
 * 压缩结果接口
 */
export interface CompressionResult {
  /** 压缩后的数据 */
  data: Buffer;
  /** 使用的压缩算法 */
  algorithm: CompressionAlgorithm;
  /** 原始大小 */
  originalSize?: number;
  /** 压缩后大小 */
  compressedSize?: number;
  /** 压缩率 */
  compressionRatio?: number;
  /** 压缩时间（毫秒） */
  compressionTime?: number;
}

/**
 * 压缩统计信息接口
 */
export interface CompressionStats {
  /** 总压缩次数 */
  totalCompressions: number;
  /** 总解压次数 */
  totalDecompressions: number;
  /** 未压缩次数 */
  uncompressedCount: number;
  /** 各算法使用次数 */
  algorithmUsage: Record<CompressionAlgorithm, number>;
  /** 各算法平均压缩率 */
  averageCompressionRatio: Record<CompressionAlgorithm, number>;
  /** 各算法平均压缩时间 */
  averageCompressionTime: Record<CompressionAlgorithm, number>;
  /** 各算法平均解压时间 */
  averageDecompressionTime: Record<CompressionAlgorithm, number>;
  /** 总节省空间（字节） */
  totalSpaceSaved: number;
  /** 缓存命中次数 */
  cacheHits: number;
  /** 缓存命中率 */
  cacheHitRate: number;
}

/**
 * 消息类型压缩配置接口
 */
export interface MessageTypeCompressionConfig {
  /** 消息类型 */
  type: string;
  /** 推荐的压缩算法 */
  algorithm: CompressionAlgorithm;
  /** 压缩级别 */
  level?: CompressionLevel;
  /** 最小压缩大小 */
  minSize?: number;
}

/**
 * 压缩选项接口
 */
export interface EnhancedCompressionOptions {
  /** 默认压缩算法 */
  defaultAlgorithm?: CompressionAlgorithm;
  /** 压缩级别 */
  level?: CompressionLevel;
  /** 最小压缩大小（字节） */
  minSize?: number;
  /** 是否启用自适应压缩 */
  enableAdaptive?: boolean;
  /** 是否启用压缩缓存 */
  enableCache?: boolean;
  /** 压缩缓存大小 */
  cacheSize?: number;
  /** 是否收集统计信息 */
  collectStats?: boolean;
  /** 消息类型字段名 */
  messageTypeField?: string;
  /** 消息类型压缩配置 */
  messageTypeConfigs?: MessageTypeCompressionConfig[];
  /** 是否启用自动学习 */
  enableAutoLearning?: boolean;
  /** 学习样本大小 */
  learningSampleSize?: number;
  /** 学习间隔（毫秒） */
  learningInterval?: number;
  /** 是否启用调试日志 */
  debug?: boolean;
}

/**
 * 缓存条目接口
 */
interface CacheEntry {
  /** 压缩后的数据 */
  data: Buffer;
  /** 使用的压缩算法 */
  algorithm: CompressionAlgorithm;
  /** 最后访问时间 */
  lastAccessed: number;
  /** 访问次数 */
  accessCount: number;
}

/**
 * 算法性能数据接口
 */
interface AlgorithmPerformanceData {
  /** 压缩次数 */
  compressionCount: number;
  /** 总压缩率 */
  totalCompressionRatio: number;
  /** 总压缩时间 */
  totalCompressionTime: number;
  /** 总解压时间 */
  totalDecompressionTime: number;
  /** 解压次数 */
  decompressionCount: number;
}

@Injectable()
export class EnhancedMessageCompressorService {
  private readonly logger = new Logger(EnhancedMessageCompressorService.name);

  // 压缩函数
  private readonly deflateAsync = promisify(zlib.deflate);
  private readonly gzipAsync = promisify(zlib.gzip);
  private readonly brotliCompressAsync = promisify(zlib.brotliCompress);
  private readonly deflateRawAsync = promisify(zlib.deflateRaw);

  // 解压函数
  private readonly inflateAsync = promisify(zlib.inflate);
  private readonly gunzipAsync = promisify(zlib.gunzip);
  private readonly brotliDecompressAsync = promisify(zlib.brotliDecompress);
  private readonly inflateRawAsync = promisify(zlib.inflateRaw);

  // 压缩缓存
  private readonly compressionCache = new Map<string, CacheEntry>();

  // 算法性能数据
  private readonly algorithmPerformance: Record<CompressionAlgorithm, AlgorithmPerformanceData> = {
    [CompressionAlgorithm.NONE]: { compressionCount: 0, totalCompressionRatio: 0, totalCompressionTime: 0, totalDecompressionTime: 0, decompressionCount: 0 },
    [CompressionAlgorithm.DEFLATE]: { compressionCount: 0, totalCompressionRatio: 0, totalCompressionTime: 0, totalDecompressionTime: 0, decompressionCount: 0 },
    [CompressionAlgorithm.GZIP]: { compressionCount: 0, totalCompressionRatio: 0, totalCompressionTime: 0, totalDecompressionTime: 0, decompressionCount: 0 },
    [CompressionAlgorithm.BROTLI]: { compressionCount: 0, totalCompressionRatio: 0, totalCompressionTime: 0, totalDecompressionTime: 0, decompressionCount: 0 },
    [CompressionAlgorithm.DEFLATE_RAW]: { compressionCount: 0, totalCompressionRatio: 0, totalCompressionTime: 0, totalDecompressionTime: 0, decompressionCount: 0 },
  };

  // 消息类型性能数据
  private readonly messageTypePerformance = new Map<string, Record<CompressionAlgorithm, AlgorithmPerformanceData>>();

  // 学习样本
  private readonly learningSamples: Array<{ messageType: string; size: number; algorithm: CompressionAlgorithm; compressionRatio: number; compressionTime: number }> = [];

  // 学习定时器
  private learningTimer: NodeJS.Timeout | null = null;

  // 统计信息
  private stats: CompressionStats = {
    totalCompressions: 0,
    totalDecompressions: 0,
    uncompressedCount: 0,
    algorithmUsage: {
      [CompressionAlgorithm.NONE]: 0,
      [CompressionAlgorithm.DEFLATE]: 0,
      [CompressionAlgorithm.GZIP]: 0,
      [CompressionAlgorithm.BROTLI]: 0,
      [CompressionAlgorithm.DEFLATE_RAW]: 0,
    },
    averageCompressionRatio: {
      [CompressionAlgorithm.NONE]: 1,
      [CompressionAlgorithm.DEFLATE]: 0,
      [CompressionAlgorithm.GZIP]: 0,
      [CompressionAlgorithm.BROTLI]: 0,
      [CompressionAlgorithm.DEFLATE_RAW]: 0,
    },
    averageCompressionTime: {
      [CompressionAlgorithm.NONE]: 0,
      [CompressionAlgorithm.DEFLATE]: 0,
      [CompressionAlgorithm.GZIP]: 0,
      [CompressionAlgorithm.BROTLI]: 0,
      [CompressionAlgorithm.DEFLATE_RAW]: 0,
    },
    averageDecompressionTime: {
      [CompressionAlgorithm.NONE]: 0,
      [CompressionAlgorithm.DEFLATE]: 0,
      [CompressionAlgorithm.GZIP]: 0,
      [CompressionAlgorithm.BROTLI]: 0,
      [CompressionAlgorithm.DEFLATE_RAW]: 0,
    },
    totalSpaceSaved: 0,
    cacheHits: 0,
    cacheHitRate: 0,
  };

  // 默认配置
  private readonly defaultOptions: Required<EnhancedCompressionOptions> = {
    defaultAlgorithm: CompressionAlgorithm.DEFLATE,
    level: CompressionLevel.DEFAULT,
    minSize: 100,
    enableAdaptive: true,
    enableCache: true,
    cacheSize: 1000,
    collectStats: true,
    messageTypeField: 'type',
    messageTypeConfigs: [],
    enableAutoLearning: true,
    learningSampleSize: 1000,
    learningInterval: 3600000, // 1小时
    debug: false,
  };

  // 合并后的配置
  private readonly options: Required<EnhancedCompressionOptions>;

  constructor(options: EnhancedCompressionOptions = {}) {
    // 合并配置
    this.options = {
      ...this.defaultOptions,
      ...options,
      messageTypeConfigs: [
        ...this.defaultOptions.messageTypeConfigs,
        ...(options.messageTypeConfigs || []),
      ],
    };

    // 如果启用自动学习，启动学习定时器
    if (this.options.enableAutoLearning) {
      this.learningTimer = setInterval(() => {
        this.learnOptimalAlgorithms();
      }, this.options.learningInterval);
    }

    this.logger.log(`增强消息压缩服务已初始化，默认算法: ${this.options.defaultAlgorithm}`);
  }

  /**
   * 压缩消息
   * @param message 消息对象
   * @returns 压缩结果
   */
  async compress(message: any): Promise<CompressionResult> {
    const startTime = Date.now();

    // 将消息转换为JSON字符串
    const jsonString = typeof message === 'string' ? message : JSON.stringify(message);
    const messageBuffer = Buffer.from(jsonString, 'utf-8');
    const originalSize = messageBuffer.length;

    // 如果消息小于最小压缩大小，则不压缩
    if (originalSize < this.options.minSize) {
      if (this.options.collectStats) {
        this.updateCompressionStats(
          CompressionAlgorithm.NONE,
          originalSize,
          originalSize,
          Date.now() - startTime
        );
      }

      return {
        data: messageBuffer,
        algorithm: CompressionAlgorithm.NONE,
        originalSize,
        compressedSize: originalSize,
        compressionRatio: 1,
        compressionTime: Date.now() - startTime,
      };
    }

    // 检查缓存
    if (this.options.enableCache) {
      const cacheKey = this.getCacheKey(jsonString);
      const cachedResult = this.compressionCache.get(cacheKey);

      if (cachedResult) {
        // 更新缓存访问统计
        cachedResult.lastAccessed = Date.now();
        cachedResult.accessCount++;

        // 更新统计信息
        if (this.options.collectStats) {
          this.stats.cacheHits++;
          this.stats.cacheHitRate = this.stats.cacheHits / this.stats.totalCompressions;
        }

        return {
          data: cachedResult.data,
          algorithm: cachedResult.algorithm,
          originalSize,
          compressedSize: cachedResult.data.length,
          compressionRatio: originalSize / cachedResult.data.length,
          compressionTime: Date.now() - startTime,
        };
      }
    }

    // 选择压缩算法
    let algorithm = this.options.defaultAlgorithm;

    // 如果启用自适应压缩，根据消息类型和大小选择最佳算法
    if (this.options.enableAdaptive && typeof message === 'object' && message !== null) {
      algorithm = this.selectOptimalAlgorithm(message, originalSize);
    }

    // 压缩数据
    const compressionStartTime = Date.now();
    let compressedData: Buffer;

    try {
      compressedData = await this.compressWithAlgorithm(messageBuffer, algorithm);
    } catch (error) {
      this.logger.warn(`使用算法 ${algorithm} 压缩失败: ${error.message}，回退到默认算法`);

      // 如果压缩失败，回退到默认算法
      algorithm = CompressionAlgorithm.DEFLATE;
      try {
        compressedData = await this.compressWithAlgorithm(messageBuffer, algorithm);
      } catch (fallbackError) {
        this.logger.error(`使用默认算法压缩失败: ${fallbackError.message}，返回未压缩数据`);

        // 如果默认算法也失败，返回未压缩数据
        algorithm = CompressionAlgorithm.NONE;
        compressedData = messageBuffer;
      }
    }

    const compressionTime = Date.now() - compressionStartTime;
    const compressedSize = compressedData.length;

    // 如果压缩后的大小大于原始大小，使用未压缩的数据
    if (compressedSize >= originalSize && algorithm !== CompressionAlgorithm.NONE) {
      if (this.options.collectStats) {
        this.updateCompressionStats(
          CompressionAlgorithm.NONE,
          originalSize,
          originalSize,
          Date.now() - startTime
        );
      }

      return {
        data: messageBuffer,
        algorithm: CompressionAlgorithm.NONE,
        originalSize,
        compressedSize: originalSize,
        compressionRatio: 1,
        compressionTime: Date.now() - startTime,
      };
    }

    // 计算压缩率
    const compressionRatio = originalSize / compressedSize;

    // 更新统计信息
    if (this.options.collectStats) {
      this.updateCompressionStats(
        algorithm,
        originalSize,
        compressedSize,
        compressionTime
      );

      // 如果启用自动学习，添加样本
      if (this.options.enableAutoLearning && typeof message === 'object' && message !== null) {
        const messageType = message[this.options.messageTypeField] || 'unknown';
        this.addLearningSample(messageType, originalSize, algorithm, compressionRatio, compressionTime);
      }
    }

    // 缓存压缩结果
    if (this.options.enableCache) {
      this.cacheCompressionResult(jsonString, compressedData, algorithm);
    }

    return {
      data: compressedData,
      algorithm,
      originalSize,
      compressedSize,
      compressionRatio,
      compressionTime,
    };
  }

  /**
   * 解压消息
   * @param data 压缩数据
   * @param algorithm 压缩算法
   * @returns 解压后的消息
   */
  async decompress(data: Buffer, algorithm: CompressionAlgorithm): Promise<any> {
    // 如果没有压缩，直接返回
    if (algorithm === CompressionAlgorithm.NONE) {
      return JSON.parse(data.toString('utf-8'));
    }

    // 解压数据
    const startTime = Date.now();
    let decompressedData: Buffer;

    try {
      decompressedData = await this.decompressWithAlgorithm(data, algorithm);
    } catch (error) {
      this.logger.error(`使用算法 ${algorithm} 解压失败: ${error.message}`);
      throw error;
    }

    const decompressionTime = Date.now() - startTime;

    // 更新统计信息
    if (this.options.collectStats) {
      this.updateDecompressionStats(algorithm, decompressionTime);
    }

    // 解析JSON
    return JSON.parse(decompressedData.toString('utf-8'));
  }

  /**
   * 获取压缩统计信息
   * @returns 压缩统计信息
   */
  getStats(): CompressionStats {
    return { ...this.stats };
  }

  /**
   * 清除统计信息
   */
  clearStats(): void {
    this.stats = {
      totalCompressions: 0,
      totalDecompressions: 0,
      uncompressedCount: 0,
      algorithmUsage: {
        [CompressionAlgorithm.NONE]: 0,
        [CompressionAlgorithm.DEFLATE]: 0,
        [CompressionAlgorithm.GZIP]: 0,
        [CompressionAlgorithm.BROTLI]: 0,
        [CompressionAlgorithm.DEFLATE_RAW]: 0,
      },
      averageCompressionRatio: {
        [CompressionAlgorithm.NONE]: 1,
        [CompressionAlgorithm.DEFLATE]: 0,
        [CompressionAlgorithm.GZIP]: 0,
        [CompressionAlgorithm.BROTLI]: 0,
        [CompressionAlgorithm.DEFLATE_RAW]: 0,
      },
      averageCompressionTime: {
        [CompressionAlgorithm.NONE]: 0,
        [CompressionAlgorithm.DEFLATE]: 0,
        [CompressionAlgorithm.GZIP]: 0,
        [CompressionAlgorithm.BROTLI]: 0,
        [CompressionAlgorithm.DEFLATE_RAW]: 0,
      },
      averageDecompressionTime: {
        [CompressionAlgorithm.NONE]: 0,
        [CompressionAlgorithm.DEFLATE]: 0,
        [CompressionAlgorithm.GZIP]: 0,
        [CompressionAlgorithm.BROTLI]: 0,
        [CompressionAlgorithm.DEFLATE_RAW]: 0,
      },
      totalSpaceSaved: 0,
      cacheHits: 0,
      cacheHitRate: 0,
    };
  }

  /**
   * 使用指定算法压缩数据
   * @param data 原始数据
   * @param algorithm 压缩算法
   * @returns 压缩后的数据
   */
  private async compressWithAlgorithm(data: Buffer, algorithm: CompressionAlgorithm): Promise<Buffer> {
    switch (algorithm) {
      case CompressionAlgorithm.DEFLATE:
        return this.deflateAsync(data, { level: this.options.level });

      case CompressionAlgorithm.GZIP:
        return this.gzipAsync(data, { level: this.options.level });

      case CompressionAlgorithm.BROTLI:
        return this.brotliCompressAsync(data, {
          params: {
            [zlib.constants.BROTLI_PARAM_QUALITY]: this.options.level,
          },
        });

      case CompressionAlgorithm.DEFLATE_RAW:
        return this.deflateRawAsync(data, { level: this.options.level });

      case CompressionAlgorithm.NONE:
      default:
        return data;
    }
  }

  /**
   * 使用指定算法解压数据
   * @param data 压缩数据
   * @param algorithm 压缩算法
   * @returns 解压后的数据
   */
  private async decompressWithAlgorithm(data: Buffer, algorithm: CompressionAlgorithm): Promise<Buffer> {
    switch (algorithm) {
      case CompressionAlgorithm.DEFLATE:
        return this.inflateAsync(data);

      case CompressionAlgorithm.GZIP:
        return this.gunzipAsync(data);

      case CompressionAlgorithm.BROTLI:
        return this.brotliDecompressAsync(data);

      case CompressionAlgorithm.DEFLATE_RAW:
        return this.inflateRawAsync(data);

      case CompressionAlgorithm.NONE:
      default:
        return data;
    }
  }

  /**
   * 选择最佳压缩算法
   * @param message 消息对象
   * @param size 消息大小
   * @returns 最佳压缩算法
   */
  private selectOptimalAlgorithm(message: any, size: number): CompressionAlgorithm {
    // 获取消息类型
    const messageType = message[this.options.messageTypeField] || 'unknown';

    // 检查是否有针对该消息类型的配置
    const typeConfig = this.options.messageTypeConfigs.find(config => config.type === messageType);
    if (typeConfig) {
      // 如果消息大小小于配置的最小大小，不压缩
      if (typeConfig.minSize !== undefined && size < typeConfig.minSize) {
        return CompressionAlgorithm.NONE;
      }

      return typeConfig.algorithm;
    }

    // 检查是否有针对该消息类型的性能数据
    const typePerformance = this.messageTypePerformance.get(messageType);
    if (typePerformance) {
      // 找出压缩率最高的算法
      let bestAlgorithm = this.options.defaultAlgorithm;
      let bestRatio = 0;

      for (const [algorithm, data] of Object.entries(typePerformance)) {
        if (data.compressionCount > 0) {
          const avgRatio = data.totalCompressionRatio / data.compressionCount;
          const avgTime = data.totalCompressionTime / data.compressionCount;

          // 计算性能得分（压缩率 / 时间）
          const score = avgRatio / (avgTime + 1); // 加1避免除以0

          if (score > bestRatio) {
            bestRatio = score;
            bestAlgorithm = algorithm as CompressionAlgorithm;
          }
        }
      }

      return bestAlgorithm;
    }

    // 根据消息大小选择算法
    if (size < 1024) {
      // 小消息使用快速算法
      return CompressionAlgorithm.DEFLATE_RAW;
    } else if (size < 10240) {
      // 中等消息使用平衡算法
      return CompressionAlgorithm.DEFLATE;
    } else {
      // 大消息使用高压缩算法
      return CompressionAlgorithm.BROTLI;
    }
  }

  /**
   * 更新压缩统计信息
   * @param algorithm 压缩算法
   * @param originalSize 原始大小
   * @param compressedSize 压缩后大小
   * @param compressionTime 压缩时间
   */
  private updateCompressionStats(
    algorithm: CompressionAlgorithm,
    originalSize: number,
    compressedSize: number,
    compressionTime: number
  ): void {
    // 更新总统计
    this.stats.totalCompressions++;
    this.stats.algorithmUsage[algorithm]++;

    if (algorithm === CompressionAlgorithm.NONE) {
      this.stats.uncompressedCount++;
    } else {
      // 计算压缩率
      const compressionRatio = originalSize / compressedSize;

      // 更新算法性能数据
      const perfData = this.algorithmPerformance[algorithm];
      perfData.compressionCount++;
      perfData.totalCompressionRatio += compressionRatio;
      perfData.totalCompressionTime += compressionTime;

      // 更新平均压缩率
      this.stats.averageCompressionRatio[algorithm] = perfData.totalCompressionRatio / perfData.compressionCount;

      // 更新平均压缩时间
      this.stats.averageCompressionTime[algorithm] = perfData.totalCompressionTime / perfData.compressionCount;

      // 更新总节省空间
      this.stats.totalSpaceSaved += originalSize - compressedSize;
    }
  }

  /**
   * 更新解压统计信息
   * @param algorithm 压缩算法
   * @param decompressionTime 解压时间
   */
  private updateDecompressionStats(algorithm: CompressionAlgorithm, decompressionTime: number): void {
    // 更新总统计
    this.stats.totalDecompressions++;

    // 更新算法性能数据
    const perfData = this.algorithmPerformance[algorithm];
    perfData.decompressionCount++;
    perfData.totalDecompressionTime += decompressionTime;

    // 更新平均解压时间
    this.stats.averageDecompressionTime[algorithm] = perfData.totalDecompressionTime / perfData.decompressionCount;
  }

  /**
   * 获取缓存键
   * @param jsonString JSON字符串
   * @returns 缓存键
   */
  private getCacheKey(jsonString: string): string {
    // 简单哈希函数
    let hash = 0;
    for (let i = 0; i < jsonString.length; i++) {
      const char = jsonString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(16);
  }

  /**
   * 缓存压缩结果
   * @param jsonString JSON字符串
   * @param compressedData 压缩后的数据
   * @param algorithm 压缩算法
   */
  private cacheCompressionResult(jsonString: string, compressedData: Buffer, algorithm: CompressionAlgorithm): void {
    // 检查缓存大小
    if (this.compressionCache.size >= this.options.cacheSize) {
      this.evictLeastRecentlyUsed();
    }

    // 添加到缓存
    const cacheKey = this.getCacheKey(jsonString);
    this.compressionCache.set(cacheKey, {
      data: compressedData,
      algorithm,
      lastAccessed: Date.now(),
      accessCount: 0,
    });
  }

  /**
   * 淘汰最近最少使用的缓存条目
   */
  private evictLeastRecentlyUsed(): void {
    let lruKey: string | null = null;
    let lruTime = Infinity;

    // 查找最近最少使用的条目
    for (const [key, entry] of this.compressionCache.entries()) {
      if (entry.lastAccessed < lruTime) {
        lruKey = key;
        lruTime = entry.lastAccessed;
      }
    }

    // 如果找到LRU条目，删除它
    if (lruKey) {
      this.compressionCache.delete(lruKey);
    }
  }

  /**
   * 添加学习样本
   * @param messageType 消息类型
   * @param size 消息大小
   * @param algorithm 压缩算法
   * @param compressionRatio 压缩率
   * @param compressionTime 压缩时间
   */
  private addLearningSample(
    messageType: string,
    size: number,
    algorithm: CompressionAlgorithm,
    compressionRatio: number,
    compressionTime: number
  ): void {
    // 添加样本
    this.learningSamples.push({
      messageType,
      size,
      algorithm,
      compressionRatio,
      compressionTime,
    });

    // 如果样本数量超过限制，移除最旧的样本
    if (this.learningSamples.length > this.options.learningSampleSize) {
      this.learningSamples.shift();
    }

    // 更新消息类型性能数据
    let typePerformance = this.messageTypePerformance.get(messageType);
    if (!typePerformance) {
      typePerformance = {
        [CompressionAlgorithm.NONE]: { compressionCount: 0, totalCompressionRatio: 0, totalCompressionTime: 0, totalDecompressionTime: 0, decompressionCount: 0 },
        [CompressionAlgorithm.DEFLATE]: { compressionCount: 0, totalCompressionRatio: 0, totalCompressionTime: 0, totalDecompressionTime: 0, decompressionCount: 0 },
        [CompressionAlgorithm.GZIP]: { compressionCount: 0, totalCompressionRatio: 0, totalCompressionTime: 0, totalDecompressionTime: 0, decompressionCount: 0 },
        [CompressionAlgorithm.BROTLI]: { compressionCount: 0, totalCompressionRatio: 0, totalCompressionTime: 0, totalDecompressionTime: 0, decompressionCount: 0 },
        [CompressionAlgorithm.DEFLATE_RAW]: { compressionCount: 0, totalCompressionRatio: 0, totalCompressionTime: 0, totalDecompressionTime: 0, decompressionCount: 0 },
      };
      this.messageTypePerformance.set(messageType, typePerformance);
    }

    // 更新算法性能数据
    const perfData = typePerformance[algorithm];
    perfData.compressionCount++;
    perfData.totalCompressionRatio += compressionRatio;
    perfData.totalCompressionTime += compressionTime;
  }

  /**
   * 学习最佳压缩算法
   */
  private learnOptimalAlgorithms(): void {
    if (this.learningSamples.length === 0) {
      return;
    }

    this.logger.log(`开始学习最佳压缩算法，样本数量: ${this.learningSamples.length}`);

    // 按消息类型分组
    const samplesByType = new Map<string, typeof this.learningSamples>();

    for (const sample of this.learningSamples) {
      const samples = samplesByType.get(sample.messageType) || [];
      samples.push(sample);
      samplesByType.set(sample.messageType, samples);
    }

    // 为每种消息类型找出最佳算法
    const newConfigs: MessageTypeCompressionConfig[] = [];

    for (const [messageType, samples] of samplesByType.entries()) {
      // 按算法分组
      const samplesByAlgorithm = new Map<CompressionAlgorithm, typeof this.learningSamples>();

      for (const sample of samples) {
        const algSamples = samplesByAlgorithm.get(sample.algorithm) || [];
        algSamples.push(sample);
        samplesByAlgorithm.set(sample.algorithm, algSamples);
      }

      // 计算每种算法的平均性能
      const algorithmPerformance = new Map<CompressionAlgorithm, { avgRatio: number; avgTime: number; score: number }>();

      for (const [algorithm, algSamples] of samplesByAlgorithm.entries()) {
        if (algorithm === CompressionAlgorithm.NONE) {
          continue;
        }

        const totalRatio = algSamples.reduce((sum, sample) => sum + sample.compressionRatio, 0);
        const totalTime = algSamples.reduce((sum, sample) => sum + sample.compressionTime, 0);

        const avgRatio = totalRatio / algSamples.length;
        const avgTime = totalTime / algSamples.length;

        // 计算性能得分（压缩率 / 时间）
        const score = avgRatio / (avgTime + 1); // 加1避免除以0

        algorithmPerformance.set(algorithm, { avgRatio, avgTime, score });
      }

      // 找出得分最高的算法
      let bestAlgorithm = this.options.defaultAlgorithm;
      let bestScore = 0;

      for (const [algorithm, perf] of algorithmPerformance.entries()) {
        if (perf.score > bestScore) {
          bestScore = perf.score;
          bestAlgorithm = algorithm;
        }
      }

      // 计算最小压缩大小
      const minSize = Math.max(
        this.options.minSize,
        Math.floor(
          samples.reduce((sum, sample) => sum + sample.size, 0) / samples.length * 0.1
        )
      );

      // 添加新配置
      newConfigs.push({
        type: messageType,
        algorithm: bestAlgorithm,
        level: this.options.level,
        minSize,
      });

      if (this.options.debug) {
        const perf = algorithmPerformance.get(bestAlgorithm);
        this.logger.debug(
          `学习结果 - 消息类型: ${messageType}, 最佳算法: ${bestAlgorithm}, ` +
          `平均压缩率: ${perf?.avgRatio.toFixed(2)}, 平均时间: ${perf?.avgTime.toFixed(2)}ms, ` +
          `最小压缩大小: ${minSize}字节`
        );
      }
    }

    // 更新配置
    this.options.messageTypeConfigs = newConfigs;

    this.logger.log(`学习完成，更新了 ${newConfigs.length} 个消息类型的压缩配置`);

    // 清空学习样本
    this.learningSamples.length = 0;
  }
}
