/**
 * 负载均衡服务
 */
import { Injectable, Logger } from '@nestjs/common';
import {
  LoadBalancerStrategy,
  LoadBalancerConfig,
  LoadBalancerContext,
  LoadBalancerAlgorithm
} from './load-balancer.interface';
import { ServiceInstanceEntity } from '../entities/service-instance.entity';
import { RandomLoadBalancerStrategy } from './random.strategy';
import { RoundRobinLoadBalancerStrategy } from './round-robin.strategy';
import { WeightedRoundRobinLoadBalancerStrategy } from './weighted-round-robin.strategy';
import { LeastResponseTimeLoadBalancerStrategy } from './least-response-time.strategy';
import { ConsistentHashLoadBalancerStrategy } from './consistent-hash.strategy';
import { ZoneAwareLoadBalancerStrategy } from './zone-aware.strategy';

/**
 * 负载均衡统计信息
 */
export interface LoadBalancerStats {
  /** 请求总数 */
  requestCount: number;
  /** 错误总数 */
  errorCount: number;
  /** 平均响应时间（毫秒） */
  avgResponseTime: number;
  /** 按策略统计的请求数 */
  requestsByStrategy: Record<string, number>;
  /** 按服务统计的请求数 */
  requestsByService: Record<string, number>;
}

@Injectable()
export class LoadBalancerService {
  private readonly logger = new Logger(LoadBalancerService.name);
  private readonly strategies = new Map<string, LoadBalancerStrategy>();
  private readonly serviceConfigs = new Map<string, LoadBalancerConfig>();

  // 统计信息
  private stats: LoadBalancerStats = {
    requestCount: 0,
    errorCount: 0,
    avgResponseTime: 0,
    requestsByStrategy: {},
    requestsByService: {},
  };

  // 总响应时间，用于计算平均值
  private totalResponseTime = 0;

  constructor(
    private readonly randomStrategy: RandomLoadBalancerStrategy,
    private readonly roundRobinStrategy: RoundRobinLoadBalancerStrategy,
    private readonly weightedRoundRobinStrategy: WeightedRoundRobinLoadBalancerStrategy,
    private readonly leastResponseTimeStrategy: LeastResponseTimeLoadBalancerStrategy,
    private readonly consistentHashStrategy: ConsistentHashLoadBalancerStrategy,
    private readonly zoneAwareStrategy: ZoneAwareLoadBalancerStrategy,
  ) {
    // 注册内置策略
    this.registerStrategy(this.randomStrategy);
    this.registerStrategy(this.roundRobinStrategy);
    this.registerStrategy(this.weightedRoundRobinStrategy);
    this.registerStrategy(this.leastResponseTimeStrategy);
    this.registerStrategy(this.consistentHashStrategy);
    this.registerStrategy(this.zoneAwareStrategy);
  }

  /**
   * 选择服务实例
   * @param serviceName 服务名称
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  async selectInstance(
    serviceName: string,
    instances: ServiceInstanceEntity[],
    context?: Partial<LoadBalancerContext>,
  ): Promise<ServiceInstanceEntity | null> {
    const startTime = Date.now();
    let selectedInstance: ServiceInstanceEntity | null = null;
    let usedStrategy: string = LoadBalancerAlgorithm.RANDOM;
    let error = false;

    try {
      if (!instances || instances.length === 0) {
        error = true;
        return null;
      }

      // 如果只有一个实例，直接返回
      if (instances.length === 1) {
        selectedInstance = instances[0];
        return selectedInstance;
      }

      // 获取服务的负载均衡配置
      const config = this.serviceConfigs.get(serviceName) || {
        algorithm: LoadBalancerAlgorithm.RANDOM,
      };

      // 获取对应的负载均衡策略
      const strategy = this.strategies.get(config.algorithm);
      usedStrategy = config.algorithm;

      if (!strategy) {
        this.logger.warn(`未找到负载均衡策略: ${config.algorithm}，使用随机策略`);
        usedStrategy = LoadBalancerAlgorithm.RANDOM;
        selectedInstance = await this.randomStrategy.select(instances, {
          serviceName,
          ...context,
        });
      } else {
        // 执行负载均衡策略
        selectedInstance = await strategy.select(instances, {
          serviceName,
          ...context,
        });
      }

      return selectedInstance;
    } catch (e) {
      this.logger.error(`负载均衡选择实例失败: ${e.message}`, e.stack);
      error = true;
      return null;
    } finally {
      // 更新统计信息
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      this.stats.requestCount++;
      if (error) {
        this.stats.errorCount++;
      }

      this.totalResponseTime += responseTime;
      this.stats.avgResponseTime = this.totalResponseTime / this.stats.requestCount;

      // 按策略统计
      this.stats.requestsByStrategy[usedStrategy] = (this.stats.requestsByStrategy[usedStrategy] || 0) + 1;

      // 按服务统计
      this.stats.requestsByService[serviceName] = (this.stats.requestsByService[serviceName] || 0) + 1;

      // 如果选择了实例，更新响应时间
      if (selectedInstance && !error) {
        this.updateResponseTime(serviceName, selectedInstance.instanceId, responseTime);
      }
    }
  }

  /**
   * 注册负载均衡策略
   * @param strategy 负载均衡策略
   */
  registerStrategy(strategy: LoadBalancerStrategy): void {
    this.strategies.set(strategy.getName(), strategy);
    this.logger.log(`已注册负载均衡策略: ${strategy.getName()}`);
  }

  /**
   * 设置服务的负载均衡配置
   * @param serviceName 服务名称
   * @param config 负载均衡配置
   */
  setServiceConfig(serviceName: string, config: LoadBalancerConfig): void {
    this.serviceConfigs.set(serviceName, config);

    // 更新策略配置
    const strategy = this.strategies.get(config.algorithm);
    if (strategy) {
      strategy.updateConfig(config);
    }

    this.logger.log(`已设置服务 ${serviceName} 的负载均衡配置: ${config.algorithm}`);
  }

  /**
   * 获取服务的负载均衡配置
   * @param serviceName 服务名称
   */
  getServiceConfig(serviceName: string): LoadBalancerConfig | null {
    return this.serviceConfigs.get(serviceName) || null;
  }

  /**
   * 获取所有负载均衡策略
   */
  getAllStrategies(): LoadBalancerStrategy[] {
    return Array.from(this.strategies.values());
  }

  /**
   * 获取负载均衡策略
   * @param name 策略名称
   */
  getStrategy(name: string): LoadBalancerStrategy | null {
    return this.strategies.get(name) || null;
  }

  /**
   * 更新实例响应时间
   * @param serviceName 服务名称
   * @param instanceId 实例ID
   * @param responseTime 响应时间（毫秒）
   */
  updateResponseTime(serviceName: string, instanceId: string, responseTime: number): void {
    // 更新最少响应时间策略的响应时间记录
    this.leastResponseTimeStrategy.updateResponseTime(serviceName, instanceId, responseTime);
  }

  /**
   * 获取负载均衡统计信息
   */
  getStats(): LoadBalancerStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      requestCount: 0,
      errorCount: 0,
      avgResponseTime: 0,
      requestsByStrategy: {},
      requestsByService: {},
    };
    this.totalResponseTime = 0;
  }

  /**
   * 重置所有策略状态
   */
  resetAllStrategies(): void {
    for (const strategy of this.strategies.values()) {
      strategy.reset();
    }

    this.logger.log('已重置所有负载均衡策略状态');
  }
}
