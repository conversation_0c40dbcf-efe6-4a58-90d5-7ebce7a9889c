/**
 * 资产服务应用模块
 */
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { MulterModule } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AssetsModule } from './assets/assets.module';
import { ModelsModule } from './models/models.module';
import { TexturesModule } from './textures/textures.module';
import { AudioModule } from './audio/audio.module';
import { HealthModule } from './health/health.module';
import { MicroservicesModule } from './shared/microservices.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 3306),
        username: configService.get<string>('DB_USERNAME', 'root'),
        password: configService.get<string>('DB_PASSWORD', 'password'),
        database: configService.get<string>('DB_DATABASE_ASSETS', 'ir_engine_assets'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get<string>('NODE_ENV', 'development') !== 'production',
        logging: configService.get<string>('NODE_ENV', 'development') !== 'production',
        charset: 'utf8mb4',
        timezone: '+08:00',
        extra: {
          ssl: false,
        },
        pool: {
          max: 10,
          min: 0,
          acquireTimeoutMillis: 60000,
          idleTimeoutMillis: 30000,
        },
      }),
    }),

    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'your-secret-key'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1d'),
        },
      }),
    }),

    // 文件上传模块
    MulterModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        storage: diskStorage({
          destination: (req, file, cb) => {
            let uploadPath = './uploads';

            // 根据文件类型确定上传路径
            if (file.mimetype.startsWith('image/')) {
              uploadPath += '/textures';
            } else if (file.mimetype.startsWith('audio/')) {
              uploadPath += '/audio';
            } else if (file.mimetype.startsWith('model/') ||
                      file.originalname.endsWith('.gltf') ||
                      file.originalname.endsWith('.glb') ||
                      file.originalname.endsWith('.obj') ||
                      file.originalname.endsWith('.fbx')) {
              uploadPath += '/models';
            } else {
              uploadPath += '/other';
            }

            cb(null, uploadPath);
          },
          filename: (req, file, cb) => {
            const uniqueSuffix = uuidv4();
            const ext = extname(file.originalname);
            cb(null, `${uniqueSuffix}${ext}`);
          },
        }),
        limits: {
          fileSize: configService.get<number>('MAX_FILE_SIZE', 100 * 1024 * 1024), // 默认100MB
        },
      }),
    }),

    // 微服务客户端模块
    MicroservicesModule,

    // 功能模块
    AssetsModule,
    ModelsModule,
    TexturesModule,
    AudioModule,
    HealthModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
