import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as net from 'net';
import { HealthCheckEntity, HealthCheckStatus } from './entities/health-check.entity';

const execAsync = promisify(exec);

@Injectable()
export class ServiceHealthCheckService {
  private readonly logger = new Logger(ServiceHealthCheckService.name);

  constructor(
    private readonly httpService: HttpService,
  ) {}

  /**
   * 执行HTTP健康检查
   */
  async checkHttp(healthCheck: HealthCheckEntity): Promise<{
    status: HealthCheckStatus;
    details: string;
    metadata?: Record<string, any>;
  }> {
    try {
      const options = healthCheck.options || {};
      const method = options.method || 'GET';
      const headers = options.headers || {};
      const timeout = healthCheck.timeout * 1000;
      const expectedStatus = options.expectedStatus || [200];
      const expectedContent = options.expectedContent;
      
      // 发送请求
      const response = await firstValueFrom(
        this.httpService.request({
          method,
          url: healthCheck.target,
          headers,
          timeout,
          validateStatus: () => true, // 不抛出HTTP错误
        })
      );
      
      // 检查状态码
      const isStatusValid = expectedStatus.includes(response.status);
      
      // 检查响应内容
      let isContentValid = true;
      
      if (expectedContent && response.data) {
        const responseText = typeof response.data === 'string'
          ? response.data
          : JSON.stringify(response.data);
        
        isContentValid = responseText.includes(expectedContent);
      }
      
      // 判断健康状态
      const isHealthy = isStatusValid && isContentValid;
      
      return {
        status: isHealthy ? HealthCheckStatus.HEALTHY : HealthCheckStatus.UNHEALTHY,
        details: isHealthy
          ? `HTTP检查成功: ${response.status}`
          : `HTTP检查失败: ${response.status}, 期望: ${expectedStatus.join(', ')}${!isContentValid ? ', 内容不匹配' : ''}`,
        metadata: {
          statusCode: response.status,
          statusText: response.statusText,
          headers: response.headers,
          content: typeof response.data === 'string'
            ? (response.data.length > 1000 ? response.data.substring(0, 1000) + '...' : response.data)
            : undefined,
        },
      };
    } catch (error) {
      this.logger.error(`HTTP健康检查失败: ${error.message}`, error.stack);
      
      return {
        status: HealthCheckStatus.UNHEALTHY,
        details: `HTTP检查异常: ${error.message}`,
        metadata: {
          error: error.message,
        },
      };
    }
  }

  /**
   * 执行TCP健康检查
   */
  async checkTcp(healthCheck: HealthCheckEntity): Promise<{
    status: HealthCheckStatus;
    details: string;
    metadata?: Record<string, any>;
  }> {
    return new Promise((resolve) => {
      try {
        const options = healthCheck.options || {};
        const timeout = healthCheck.timeout * 1000;
        const send = options.send;
        const expect = options.expect;
        
        // 解析目标地址
        const [host, portStr] = healthCheck.target.split(':');
        const port = parseInt(portStr, 10);
        
        if (!host || isNaN(port)) {
          throw new Error(`无效的TCP目标: ${healthCheck.target}`);
        }
        
        // 创建TCP连接
        const socket = new net.Socket();
        let responseData = '';
        
        // 设置超时
        socket.setTimeout(timeout);
        
        // 连接成功
        socket.on('connect', () => {
          // 如果需要发送数据
          if (send) {
            socket.write(send);
          } else {
            // 如果不需要检查响应，则直接认为健康
            if (!expect) {
              socket.destroy();
              
              resolve({
                status: HealthCheckStatus.HEALTHY,
                details: `TCP连接成功: ${host}:${port}`,
              });
            }
          }
        });
        
        // 接收数据
        socket.on('data', (data) => {
          responseData += data.toString();
          
          // 如果期望的响应已收到，则认为健康
          if (expect && responseData.includes(expect)) {
            socket.destroy();
            
            resolve({
              status: HealthCheckStatus.HEALTHY,
              details: `TCP检查成功: ${host}:${port}`,
              metadata: {
                response: responseData,
              },
            });
          }
        });
        
        // 连接结束
        socket.on('end', () => {
          // 如果期望的响应未收到，则认为不健康
          if (expect && !responseData.includes(expect)) {
            resolve({
              status: HealthCheckStatus.UNHEALTHY,
              details: `TCP响应不匹配: ${host}:${port}`,
              metadata: {
                response: responseData,
                expected: expect,
              },
            });
          }
        });
        
        // 连接错误
        socket.on('error', (error) => {
          socket.destroy();
          
          this.logger.error(`TCP连接错误: ${error.message}`, error.stack);
          
          resolve({
            status: HealthCheckStatus.UNHEALTHY,
            details: `TCP连接错误: ${error.message}`,
            metadata: {
              error: error.message,
            },
          });
        });
        
        // 连接超时
        socket.on('timeout', () => {
          socket.destroy();
          
          this.logger.warn(`TCP连接超时: ${host}:${port}`);
          
          resolve({
            status: HealthCheckStatus.UNHEALTHY,
            details: `TCP连接超时: ${host}:${port}`,
          });
        });
        
        // 连接关闭
        socket.on('close', (hadError) => {
          if (!hadError && !expect) {
            resolve({
              status: HealthCheckStatus.HEALTHY,
              details: `TCP连接成功并关闭: ${host}:${port}`,
            });
          }
        });
        
        // 建立连接
        socket.connect(port, host);
      } catch (error) {
        this.logger.error(`TCP健康检查失败: ${error.message}`, error.stack);
        
        resolve({
          status: HealthCheckStatus.UNHEALTHY,
          details: `TCP检查异常: ${error.message}`,
          metadata: {
            error: error.message,
          },
        });
      }
    });
  }

  /**
   * 执行脚本健康检查
   */
  async checkScript(healthCheck: HealthCheckEntity): Promise<{
    status: HealthCheckStatus;
    details: string;
    metadata?: Record<string, any>;
  }> {
    try {
      const options = healthCheck.options || {};
      const timeout = healthCheck.timeout * 1000;
      const expectedExitCode = options.expectedExitCode || [0];
      
      // 执行脚本
      const { stdout, stderr } = await execAsync(healthCheck.target, {
        timeout,
      });
      
      // 检查退出码（成功执行的退出码为0）
      const exitCode = 0;
      
      // 判断健康状态
      const isHealthy = expectedExitCode.includes(exitCode);
      
      return {
        status: isHealthy ? HealthCheckStatus.HEALTHY : HealthCheckStatus.UNHEALTHY,
        details: isHealthy
          ? `脚本检查成功: 退出码 ${exitCode}`
          : `脚本检查失败: 退出码 ${exitCode}, 期望: ${expectedExitCode.join(', ')}`,
        metadata: {
          exitCode,
          stdout: stdout.trim(),
          stderr: stderr.trim(),
        },
      };
    } catch (error) {
      this.logger.error(`脚本健康检查失败: ${error.message}`, error.stack);
      
      // 如果是超时错误
      if (error.signal === 'SIGTERM') {
        return {
          status: HealthCheckStatus.UNHEALTHY,
          details: `脚本执行超时: ${healthCheck.timeout}秒`,
          metadata: {
            error: '执行超时',
          },
        };
      }
      
      return {
        status: HealthCheckStatus.UNHEALTHY,
        details: `脚本检查异常: ${error.message}`,
        metadata: {
          error: error.message,
          stderr: error.stderr,
        },
      };
    }
  }

  /**
   * 执行自定义健康检查
   */
  async checkCustom(healthCheck: HealthCheckEntity): Promise<{
    status: HealthCheckStatus;
    details: string;
    metadata?: Record<string, any>;
  }> {
    // 自定义健康检查的实现
    this.logger.debug(`自定义健康检查 ${healthCheck.id} 的实现尚未完成`);

    return {
      status: HealthCheckStatus.UNKNOWN,
      details: '自定义健康检查尚未实现',
    };
  }

  /**
   * 检查所有服务
   */
  async checkAllServices(): Promise<any> {
    // 简化实现，返回模拟数据
    this.logger.debug('检查所有服务健康状态');
    return {
      status: 'healthy',
      services: [],
      timestamp: new Date(),
    };
  }

  /**
   * 检查特定服务
   */
  async checkService(serviceName: string): Promise<any> {
    // 简化实现，返回模拟数据
    this.logger.debug(`检查服务健康状态: ${serviceName}`);
    return {
      status: 'healthy',
      service: serviceName,
      timestamp: new Date(),
      details: '服务运行正常',
    };
  }
}
