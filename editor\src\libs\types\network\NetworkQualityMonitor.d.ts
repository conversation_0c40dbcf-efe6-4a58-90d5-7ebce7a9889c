/**
 * 网络质量监控器
 * 负责监控网络连接质量并提供相关数据
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 网络质量等级
 */
export declare enum NetworkQualityLevel {
    /** 未知 */
    UNKNOWN = "unknown",
    /** 极差 */
    VERY_BAD = "very_bad",
    /** 差 */
    BAD = "bad",
    /** 一般 */
    MEDIUM = "medium",
    /** 良好 */
    GOOD = "good",
    /** 极好 */
    EXCELLENT = "excellent"
}
/**
 * 网络问题类型
 */
export declare enum NetworkIssueType {
    /** 无问题 */
    NONE = "none",
    /** 高延迟 */
    HIGH_LATENCY = "high_latency",
    /** 丢包 */
    PACKET_LOSS = "packet_loss",
    /** 高抖动 */
    HIGH_JITTER = "high_jitter",
    /** 低带宽 */
    LOW_BANDWIDTH = "low_bandwidth",
    /** 连接不稳定 */
    UNSTABLE_CONNECTION = "unstable_connection",
    /** 连接中断 */
    CONNECTION_INTERRUPTED = "connection_interrupted",
    /** 网络拥塞 */
    NETWORK_CONGESTION = "network_congestion",
    /** 带宽波动 */
    BANDWIDTH_FLUCTUATION = "bandwidth_fluctuation",
    /** 服务器响应慢 */
    SLOW_SERVER_RESPONSE = "slow_server_response",
    /** DNS解析问题 */
    DNS_RESOLUTION_ISSUE = "dns_resolution_issue"
}
/**
 * 网络问题详情
 */
export interface NetworkIssue {
    /** 问题类型 */
    type: NetworkIssueType;
    /** 问题严重程度（0-1） */
    severity: number;
    /** 问题描述 */
    description: string;
    /** 建议解决方案 */
    solution: string;
    /** 问题开始时间 */
    startTime: number;
    /** 问题持续时间（毫秒） */
    duration: number;
    /** 是否已解决 */
    resolved: boolean;
}
/**
 * 网络质量数据
 */
export interface NetworkQualityData {
    /** 往返时间（毫秒） */
    rtt: number;
    /** 丢包率（0-1） */
    packetLoss: number;
    /** 抖动（毫秒） */
    jitter: number;
    /** 带宽（字节/秒） */
    bandwidth: number;
    /** 上行带宽（字节/秒） */
    uploadBandwidth?: number;
    /** 下行带宽（字节/秒） */
    downloadBandwidth?: number;
    /** 连接稳定性（0-1，1表示最稳定） */
    stability?: number;
    /** 网络拥塞程度（0-1，1表示最拥塞） */
    congestion?: number;
    /** 质量等级 */
    level: NetworkQualityLevel;
    /** 时间戳 */
    timestamp: number;
    /** 检测到的网络问题 */
    issues?: NetworkIssue[];
    /** 带宽利用率（0-1） */
    bandwidthUtilization?: number;
    /** 网络质量分数（0-100，越高越好） */
    qualityScore?: number;
    /** 连接可靠性（0-1，1表示最可靠） */
    reliability?: number;
    /** 网络延迟趋势（-1: 恶化, 0: 稳定, 1: 改善） */
    latencyTrend?: number;
    /** 网络类型 (例如: wifi, cellular, ethernet) */
    networkType?: string;
    /** 连接类型 (例如: 4g, 5g, wifi) */
    connectionType?: string;
    /** 信号强度（仅适用于无线连接，0-1） */
    signalStrength?: number;
    /** 路由跳数 */
    hopCount?: number;
    /** 服务器响应时间（毫秒） */
    serverResponseTime?: number;
    /** 连接建立时间（毫秒） */
    connectionEstablishTime?: number;
    /** 数据传输速率（字节/秒） */
    dataTransferRate?: number;
    /** 网络接口状态 */
    interfaceStatus?: string;
    /** DNS解析时间（毫秒） */
    dnsResolutionTime?: number;
    /** 网络错误计数 */
    errorCount?: number;
    /** 网络警告计数 */
    warningCount?: number;
    /** 网络预测数据 */
    prediction?: {
        /** 预测的未来RTT */
        rtt?: number;
        /** 预测的未来丢包率 */
        packetLoss?: number;
        /** 预测的未来带宽 */
        bandwidth?: number;
        /** 预测的未来稳定性 */
        stability?: number;
        /** 预测的置信度（0-1） */
        confidence?: number;
    };
}
/**
 * 网络质量监控器配置
 */
export interface NetworkQualityMonitorConfig {
    /** 采样间隔（毫秒） */
    sampleInterval?: number;
    /** 历史数据保留时间（毫秒） */
    historyDuration?: number;
    /** 是否启用自动采样 */
    autoSample?: boolean;
    /** RTT阈值（毫秒） */
    rttThresholds?: {
        excellent: number;
        good: number;
        medium: number;
        bad: number;
    };
    /** 丢包率阈值（0-1） */
    packetLossThresholds?: {
        excellent: number;
        good: number;
        medium: number;
        bad: number;
    };
    /** 抖动阈值（毫秒） */
    jitterThresholds?: {
        excellent: number;
        good: number;
        medium: number;
        bad: number;
    };
    /** 带宽阈值（字节/秒） */
    bandwidthThresholds?: {
        excellent: number;
        good: number;
        medium: number;
        bad: number;
    };
    /** 是否启用网络问题诊断 */
    enableDiagnostics?: boolean;
    /** 问题检测间隔（毫秒） */
    diagnosticsInterval?: number;
    /** 是否记录详细日志 */
    detailedLogging?: boolean;
    /** 是否启用连续测量模式（更准确但消耗更多资源） */
    continuousMeasurement?: boolean;
    /** 是否自动尝试解决检测到的问题 */
    autoTroubleshoot?: boolean;
    /** 历史记录大小 */
    historySize?: number;
    /** Ping间隔（毫秒） */
    pingInterval?: number;
    /** 是否启用高级诊断 */
    enableAdvancedDiagnostics?: boolean;
    /** 是否启用网络路径分析 */
    enablePathAnalysis?: boolean;
    /** 是否启用网络类型检测 */
    enableNetworkTypeDetection?: boolean;
    /** 是否启用连接类型检测 */
    enableConnectionTypeDetection?: boolean;
    /** 是否启用信号强度检测 */
    enableSignalStrengthDetection?: boolean;
    /** 是否启用DNS监控 */
    enableDnsMonitoring?: boolean;
    /** 是否启用服务器响应时间监控 */
    enableServerResponseTimeMonitoring?: boolean;
    /** 是否启用网络接口监控 */
    enableInterfaceMonitoring?: boolean;
    /** 是否启用网络地址监控 */
    enableNetworkAddressMonitoring?: boolean;
    /** 是否启用错误计数 */
    enableErrorCounting?: boolean;
    /** 是否启用警告计数 */
    enableWarningCounting?: boolean;
    /** 是否启用网络预测 */
    enableNetworkPrediction?: boolean;
    /** 预测窗口大小 */
    predictionWindowSize?: number;
    /** 是否启用网络事件记录 */
    enableNetworkEventLogging?: boolean;
    /** 是否启用网络报告生成 */
    enableReportGeneration?: boolean;
    /** 报告生成间隔（毫秒） */
    reportGenerationInterval?: number;
}
/**
 * 网络质量监控器
 * 负责监控网络连接质量并提供相关数据
 */
export declare class NetworkQualityMonitor extends EventEmitter {
    /** 配置 */
    private config;
    /** 当前质量数据 */
    private currentQuality;
    /** 历史质量数据 */
    private qualityHistory;
    /** 采样定时器ID */
    private sampleTimerId;
    /** 诊断定时器ID */
    private diagnosticsTimerId;
    /** 上次发送的探测包时间戳 */
    private lastProbeSentTime;
    /** 探测包序列号 */
    private probeSequence;
    /** 待确认的探测包 */
    private pendingProbes;
    /** 接收到的探测包响应时间 */
    private probeResponses;
    /** 丢失的探测包数量 */
    private lostProbes;
    /** 总发送的探测包数量 */
    private totalProbes;
    /** 带宽测量开始时间 */
    private bandwidthMeasureStartTime;
    /** 带宽测量接收字节数 */
    private bandwidthMeasureBytes;
    /** 上行带宽测量字节数 */
    private uploadBandwidthBytes;
    /** 下行带宽测量字节数 */
    private downloadBandwidthBytes;
    /** 当前检测到的网络问题 */
    private activeIssues;
    /** 历史网络问题 */
    private issueHistory;
    /** 连接稳定性历史数据 */
    private stabilityHistory;
    /** 网络拥塞历史数据 */
    private congestionHistory;
    /** RTT历史数据 */
    private rttHistory;
    /** 带宽利用率历史数据 */
    private bandwidthUtilizationHistory;
    /** 可靠性历史数据 */
    private reliabilityHistory;
    /** 带宽测量窗口大小（毫秒） */
    private bandwidthMeasureWindowSize;
    /** 上次带宽测量时间 */
    private lastBandwidthMeasureTime;
    /** 带宽测量样本数 */
    private bandwidthMeasureSamples;
    /** 最大测量带宽 */
    private maxMeasuredBandwidth;
    /** 网络预测数据 */
    private predictionData;
    /** 网络事件日志 */
    private networkEventLog;
    /** 报告生成定时器ID */
    private reportTimerId;
    /** 网络路径分析数据 */
    private pathAnalysisData;
    /** 网络类型 */
    private networkType;
    /** 连接类型 */
    private connectionType;
    /** 信号强度 */
    private signalStrength;
    /** DNS解析时间 */
    private dnsResolutionTime;
    /** 服务器响应时间 */
    private serverResponseTime;
    /** 网络接口状态 */
    private interfaceStatus;
    /** 网络地址 */
    private networkAddress;
    /** 错误计数 */
    private errorCount;
    /** 警告计数 */
    private warningCount;
    /**
     * 创建网络质量监控器
     * @param config 配置
     */
    constructor(config?: NetworkQualityMonitorConfig);
    /**
     * 启动采样
     */
    startSampling(): void;
    /**
     * 停止采样
     */
    stopSampling(): void;
    /**
     * 进行一次采样
     */
    sample(): void;
    /**
     * 发送探测包
     */
    private sendProbe;
    /**
     * 接收探测包响应
     * @param sequence 探测包序列号
     * @param size 响应大小（字节）
     */
    receiveProbeResponse(sequence: number, size?: number): void;
    /**
     * 检查超时的探测包
     */
    private checkTimeoutProbes;
    /**
     * 更新带宽测量
     * @param bytes 接收到的字节数
     */
    private updateBandwidthMeasurement;
    /**
     * 记录上行数据
     * @param bytes 字节数
     */
    recordUpload(bytes: number): void;
    /**
     * 记录下行数据
     * @param bytes 字节数
     */
    recordDownload(bytes: number): void;
    /**
     * 计算网络质量
     */
    private calculateNetworkQuality;
    /**
     * 计算数据传输速率
     * @returns 数据传输速率（字节/秒）
     */
    private calculateDataTransferRate;
    /**
     * 更新错误计数
     */
    private updateErrorCount;
    /**
     * 更新警告计数
     */
    private updateWarningCount;
    /**
     * 确定质量等级
     * @returns 质量等级
     */
    private determineQualityLevel;
    /**
     * 清理过期的历史数据
     */
    private cleanupHistory;
    /**
     * 获取当前网络质量
     * @returns 当前网络质量数据
     */
    getCurrentQuality(): NetworkQualityData;
    /**
     * 获取历史网络质量数据
     * @param duration 时间范围（毫秒），如果为0则返回所有历史数据
     * @returns 历史网络质量数据
     */
    getQualityHistory(duration?: number): NetworkQualityData[];
    /**
     * 导出网络质量数据
     * @param format 导出格式，支持'json'和'csv'
     * @param duration 时间范围（毫秒），如果为0则导出所有历史数据
     * @returns 导出的数据字符串
     */
    exportQualityData(format?: 'json' | 'csv', duration?: number): string;
    /**
     * 重置监控器
     */
    reset(): void;
    /**
     * 启动诊断
     */
    startDiagnostics(): void;
    /**
     * 停止诊断
     */
    stopDiagnostics(): void;
    /**
     * 诊断网络问题
     * 检测各种网络问题并更新问题列表
     */
    private diagnoseNetworkIssues;
    /**
     * 获取或创建网络问题
     * @param type 问题类型
     * @param severity 严重程度
     * @param now 当前时间
     * @param description 问题描述（可选）
     * @param solution 解决方案（可选）
     * @returns 网络问题
     */
    private getOrCreateIssue;
    /**
     * 解决网络问题
     * @param type 问题类型
     * @param now 当前时间
     */
    private resolveIssue;
    /**
     * 获取问题描述
     * @param type 问题类型
     * @returns 问题描述
     */
    private getIssueDescription;
    /**
     * 获取问题解决方案
     * @param type 问题类型
     * @returns 问题解决方案
     */
    private getIssueSolution;
    /**
     * 尝试解决网络问题
     * @param issues 网络问题列表
     */
    private troubleshootIssues;
    /**
     * 解决高延迟问题
     * @param issue 网络问题
     */
    private troubleshootHighLatency;
    /**
     * 解决丢包问题
     * @param issue 网络问题
     */
    private troubleshootPacketLoss;
    /**
     * 解决网络拥塞问题
     * @param issue 网络问题
     */
    private troubleshootNetworkCongestion;
    /**
     * 解决带宽波动问题
     * @param issue 网络问题
     */
    private troubleshootBandwidthFluctuation;
    /**
     * 解决连接不稳定问题
     * @param issue 网络问题
     */
    private troubleshootUnstableConnection;
    /**
     * 计算连接稳定性
     * 基于最近的网络质量数据计算稳定性指标（0-1）
     */
    private calculateStability;
    /**
     * 计算数据变化率
     * @param values 数值数组
     * @returns 变化率（0-1）
     */
    private calculateVariation;
    /**
     * 计算网络拥塞程度
     * 基于RTT、丢包率和带宽计算拥塞指标（0-1）
     */
    private calculateCongestion;
    /**
     * 计算连接可靠性
     * 基于丢包率、连接稳定性和网络拥塞程度计算可靠性指标（0-1）
     */
    private calculateReliability;
    /**
     * 计算网络质量分数
     * 综合各项指标计算网络质量分数（0-100）
     */
    private calculateQualityScore;
    /**
     * 计算延迟趋势
     * 分析最近的RTT数据，确定延迟趋势（-1: 恶化, 0: 稳定, 1: 改善）
     */
    private calculateLatencyTrend;
    /**
     * 获取网络问题历史
     * @param count 获取的问题数量，默认为所有
     * @returns 网络问题历史
     */
    getIssueHistory(count?: number): NetworkIssue[];
    /**
     * 获取当前活动的网络问题
     * @returns 当前活动的网络问题
     */
    getActiveIssues(): NetworkIssue[];
    /**
     * 获取网络稳定性历史
     * @returns 网络稳定性历史
     */
    getStabilityHistory(): number[];
    /**
     * 获取网络拥塞历史
     * @returns 网络拥塞历史
     */
    getCongestionHistory(): number[];
    /**
     * 销毁监控器
     */
    dispose(): void;
    /**
     * 启动网络报告生成
     */
    startReportGeneration(): void;
    /**
     * 停止网络报告生成
     */
    stopReportGeneration(): void;
    /**
     * 生成网络报告
     * @returns 网络报告
     */
    private generateNetworkReport;
    /**
     * 计算平均网络质量
     * @returns 平均网络质量数据
     */
    private calculateAverageQuality;
    /**
     * 预测网络质量
     * @returns 预测的网络质量数据
     */
    private predictNetworkQuality;
    /**
     * 计算数据趋势
     * @param values 数值数组
     * @returns 趋势值
     */
    private calculateTrend;
    /**
     * 生成网络优化建议
     * @returns 建议列表
     */
    private generateRecommendations;
    /**
     * 记录网络事件
     * @param type 事件类型
     * @param data 事件数据
     */
    logNetworkEvent(type: string, data: any): void;
    /**
     * 获取网络事件日志
     * @param count 获取的事件数量，默认为所有
     * @returns 网络事件日志
     */
    getNetworkEventLog(count?: number): Array<{
        timestamp: number;
        type: string;
        data: any;
    }>;
    /**
     * 检测网络类型
     * 注意：这是一个模拟实现，实际应用中需要使用浏览器API或其他方法获取真实网络类型
     */
    private detectNetworkType;
    /**
     * 测量DNS解析时间
     * 注意：这是一个模拟实现，实际应用中需要使用真实的DNS解析测量
     */
    private measureDnsResolutionTime;
    /**
     * 测量服务器响应时间
     * 注意：这是一个模拟实现，实际应用中需要使用真实的服务器响应时间测量
     */
    private measureServerResponseTime;
    /**
     * 分析网络路径
     * 注意：这是一个模拟实现，实际应用中需要使用真实的网络路径分析
     */
    private analyzeNetworkPath;
    /**
     * 获取网络路径分析数据
     * @returns 网络路径分析数据
     */
    getNetworkPathData(): Array<{
        hop: number;
        address: string;
        rtt: number;
    }>;
}
