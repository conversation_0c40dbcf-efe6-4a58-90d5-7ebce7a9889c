/**
 * 增强版雨水预设
 * 提供各种类型的增强雨水预设配置
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { EnhancedRainWaterType } from './EnhancedRainWaterComponent';
/**
 * 增强版雨水预设配置
 */
export interface EnhancedRainWaterPresetConfig {
    /** 预设类型 */
    type: EnhancedRainWaterType;
    /** 位置 */
    position?: THREE.Vector3;
    /** 尺寸 */
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    /** 颜色 */
    color?: THREE.Color;
    /** 不透明度 */
    opacity?: number;
    /** 是否启用风力影响 */
    enableWindEffect?: boolean;
    /** 风力强度 */
    windStrength?: number;
    /** 风力方向 */
    windDirection?: THREE.Vector2;
    /** 是否启用雨滴轨迹 */
    enableRaindropTrails?: boolean;
    /** 是否启用雨滴涟漪 */
    enableRaindropRipples?: boolean;
    /** 是否启用雨滴积水 */
    enableRaindropPuddles?: boolean;
    /** 是否启用雨滴声音 */
    enableRaindropSounds?: boolean;
    /** 是否启用GPU加速 */
    enableGPUAcceleration?: boolean;
}
/**
 * 增强版雨水预设
 */
export declare class EnhancedRainWaterPresets {
    /**
     * 创建雨水预设
     * @param world 世界
     * @param config 配置
     * @returns 雨水实体
     */
    static createPreset(world: World, config: EnhancedRainWaterPresetConfig): Entity;
    /**
     * 应用轻雨预设
     * @param config 配置
     */
    private static applyLightPreset;
    /**
     * 应用中雨预设
     * @param config 配置
     */
    private static applyMediumPreset;
    /**
     * 应用暴雨预设
     * @param config 配置
     */
    private static applyHeavyPreset;
    /**
     * 应用雷雨预设
     * @param config 配置
     */
    private static applyThunderstormPreset;
    /**
     * 应用季风雨预设
     * @param config 配置
     */
    private static applyMonsoonPreset;
    /**
     * 应用春雨预设
     * @param config 配置
     */
    private static applySpringRainPreset;
    /**
     * 应用夏雨预设
     * @param config 配置
     */
    private static applySummerRainPreset;
    /**
     * 应用秋雨预设
     * @param config 配置
     */
    private static applyAutumnRainPreset;
    /**
     * 应用冬雨预设
     * @param config 配置
     */
    private static applyWinterRainPreset;
    /**
     * 应用雪雨混合预设
     * @param config 配置
     */
    private static applySleetPreset;
    /**
     * 应用酸雨预设
     * @param config 配置
     */
    private static applyAcidRainPreset;
    /**
     * 应用热带雨预设
     * @param config 配置
     */
    private static applyTropicalRainPreset;
}
