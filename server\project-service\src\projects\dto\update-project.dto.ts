/**
 * 更新项目DTO
 */
import { IsString, IsOptional, IsEnum, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ProjectVisibility } from '../entities/project.entity';

export class UpdateProjectDto {
  @ApiProperty({ description: '项目名称', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '项目描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '缩略图URL', required: false })
  @IsString()
  @IsOptional()
  thumbnailUrl?: string;

  @ApiProperty({ description: '项目可见性', required: false, enum: ProjectVisibility })
  @IsEnum(ProjectVisibility)
  @IsOptional()
  visibility?: ProjectVisibility;

  @ApiProperty({ description: '是否为模板', required: false })
  @IsBoolean()
  @IsOptional()
  isTemplate?: boolean;

  @ApiProperty({ description: '是否已归档', required: false })
  @IsBoolean()
  @IsOptional()
  isArchived?: boolean;
}
