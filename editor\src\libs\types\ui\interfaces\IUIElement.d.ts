/**
 * IUIElement.ts
 *
 * 定义UI元素的基本接口
 * 所有UI元素都应该实现这个接口
 */
import type { Entity } from '../../core/Entity';
import { Vector2, Vector3 } from 'three';
/**
 * UI元素的基本接口
 */
export interface IUIElement {
    /**
     * UI元素的唯一标识符
     */
    id: string;
    /**
     * UI元素关联的实体
     */
    entity: Entity;
    /**
     * UI元素的父元素
     */
    parent?: IUIElement;
    /**
     * UI元素的子元素
     */
    children: IUIElement[];
    /**
     * UI元素是否可见
     */
    visible: boolean;
    /**
     * UI元素是否可交互
     */
    interactive: boolean;
    /**
     * UI元素的位置
     */
    position: Vector3 | Vector2;
    /**
     * UI元素的尺寸
     */
    size: Vector2;
    /**
     * UI元素的透明度
     */
    opacity: number;
    /**
     * 添加子元素
     * @param child 要添加的子元素
     */
    addChild(child: IUIElement): void;
    /**
     * 移除子元素
     * @param child 要移除的子元素
     */
    removeChild(child: IUIElement): void;
    /**
     * 更新UI元素
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 渲染UI元素
     */
    render(): void;
    /**
     * 销毁UI元素
     */
    dispose(): void;
}
/**
 * UI元素的布局接口
 */
export interface IUILayout {
    /**
     * 布局类型
     */
    type: UILayoutType;
    /**
     * 布局参数
     */
    params: any;
    /**
     * 应用布局
     * @param element 要应用布局的UI元素
     */
    apply(element: IUIElement): void;
}
/**
 * UI布局类型枚举
 */
export declare enum UILayoutType {
    NONE = "none",
    GRID = "grid",
    FLEX = "flex",
    ABSOLUTE = "absolute",
    RELATIVE = "relative"
}
/**
 * UI元素的事件接口
 */
export interface IUIEvent {
    /**
     * 事件类型
     */
    type: UIEventType;
    /**
     * 事件目标
     */
    target: IUIElement;
    /**
     * 事件数据
     */
    data: any;
    /**
     * 阻止事件冒泡
     */
    stopPropagation(): void;
    /**
     * 阻止事件默认行为
     */
    preventDefault(): void;
}
/**
 * UI事件类型枚举
 */
export declare enum UIEventType {
    CLICK = "click",
    HOVER = "hover",
    DRAG_START = "dragstart",
    DRAG = "drag",
    DRAG_END = "dragend",
    FOCUS = "focus",
    BLUR = "blur",
    KEY_DOWN = "keydown",
    KEY_UP = "keyup"
}
/**
 * UI元素的动画接口
 */
export interface IUIAnimation {
    /**
     * 动画类型
     */
    type: UIAnimationType;
    /**
     * 动画目标
     */
    target: IUIElement;
    /**
     * 动画持续时间（毫秒）
     */
    duration: number;
    /**
     * 动画延迟（毫秒）
     */
    delay: number;
    /**
     * 动画缓动函数
     */
    easing: UIEasingFunction;
    /**
     * 动画是否循环
     */
    loop: boolean;
    /**
     * 动画开始值
     */
    from: any;
    /**
     * 动画结束值
     */
    to: any;
    /**
     * 动画进度（0-1）
     */
    progress: number;
    /**
     * 开始动画
     */
    start(): void;
    /**
     * 暂停动画
     */
    pause(): void;
    /**
     * 恢复动画
     */
    resume(): void;
    /**
     * 停止动画
     */
    stop(): void;
    /**
     * 更新动画
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 动画完成回调
     */
    onComplete?: () => void;
}
/**
 * UI动画类型枚举
 */
export declare enum UIAnimationType {
    FADE = "fade",
    SCALE = "scale",
    MOVE = "move",
    ROTATE = "rotate",
    COLOR = "color"
}
/**
 * UI缓动函数类型
 */
export type UIEasingFunction = (t: number) => number;
