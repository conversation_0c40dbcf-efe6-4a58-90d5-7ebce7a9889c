# DL（Digital Learning）引擎底层功能分析

## 概述

DL（Digital Learning）引擎是一个强大的3D渲染引擎和编辑器平台，基于TypeScript和Three.js构建，采用现代化的ECS（Entity-Component-System）架构设计。该引擎专为数字化学习和交互式3D应用开发而设计，提供了完整的3D场景管理、渲染、物理模拟、动画、音频、输入处理等功能模块。

## 核心架构设计

### 1. ECS架构模式

DL引擎采用Entity-Component-System（实体-组件-系统）架构模式，这是现代游戏引擎的标准架构：

#### Entity（实体）
- **定义**：场景中的基本对象，如角色、道具、光源等
- **特点**：实体本身只是一个容器，不包含具体功能
- **实现**：每个实体拥有唯一ID、名称、标签和组件集合
- **层级关系**：支持父子关系，形成场景树结构

#### Component（组件）
- **定义**：实体的功能模块，如变换、渲染、物理等
- **特点**：纯数据结构，不包含逻辑处理
- **类型**：Transform（变换）、MeshRenderer（网格渲染）、PhysicsBody（物理体）等
- **生命周期**：支持启用/禁用状态管理

```typescript
abstract class Component extends EventEmitter {
  private type: string;
  protected entity: Entity | null = null;
  protected enabled: boolean = true;

  constructor(type: string) {
    super();
    this.type = type;
  }

  // 生命周期方法
  protected onAttach(): void { /* 附加到实体时调用 */ }
  protected onEnable(): void { /* 启用时调用 */ }
  protected onDisable(): void { /* 禁用时调用 */ }
  public update(deltaTime: number): void { /* 每帧更新 */ }
  public fixedUpdate(fixedDeltaTime: number): void { /* 固定时间步长更新 */ }
}
```

#### System（系统）
- **定义**：处理特定类型组件的逻辑系统
- **特点**：包含所有业务逻辑，对组件进行批量处理
- **更新机制**：支持普通更新、固定时间步长更新和后更新
- **优先级**：系统按优先级排序执行

```typescript
abstract class System extends EventEmitter {
  protected engine: Engine | null = null;
  protected enabled: boolean = true;
  protected priority: number = 0;

  // 系统生命周期
  public initialize(): void { /* 初始化系统 */ }
  public update(deltaTime: number): void { /* 每帧更新 */ }
  public fixedUpdate(fixedDeltaTime: number): void { /* 固定时间步长更新 */ }
  public lateUpdate(deltaTime: number): void { /* 后更新 */ }
  public dispose(): void { /* 销毁系统 */ }

  // 优先级管理
  public getPriority(): number { return this.priority; }
  public setPriority(priority: number): void { this.priority = priority; }
}
```

### 2. 引擎核心类

#### Engine（引擎主类）
```typescript
class Engine extends EventEmitter {
  private world: World;              // 世界实例
  private systems: System[];         // 系统列表
  private renderer: Renderer;        // 渲染器
  private assetManager: AssetManager; // 资产管理器
  private i18n: I18n;               // 国际化
  private running: boolean = false;  // 运行状态
  private animationFrameId: number;  // 动画帧ID
  private fixedUpdateTimeStep: number = 1/60; // 固定时间步长
}
```

**主要功能**：
- 引擎生命周期管理（初始化、启动、停止、销毁）
- 渲染循环控制（60FPS固定时间步长）
- 系统管理（添加、移除、排序）
- 事件分发机制

**核心更新循环**：
```typescript
private update(timestamp: number): void {
  const deltaTime = (timestamp - this.lastFrameTime) / 1000;

  // 更新时间系统
  Time.update(deltaTime);

  // 更新世界和所有系统
  this.world.update(deltaTime);

  // 固定时间步长更新（物理计算）
  this.fixedUpdateAccumulator += deltaTime;
  while (this.fixedUpdateAccumulator >= this.fixedUpdateTimeStep) {
    this.world.fixedUpdate(this.fixedUpdateTimeStep);
    this.fixedUpdateAccumulator -= this.fixedUpdateTimeStep;
  }

  // 继续渲染循环
  this.animationFrameId = requestAnimationFrame(this.update.bind(this));
}
```

#### World（世界）
```typescript
class World extends EventEmitter {
  private entities: Map<string, Entity>; // 实体映射
  private scenes: Map<string, Scene>;    // 场景映射
  private systems: System[];             // 系统列表
  private activeScene: Scene | null;     // 当前活跃场景
}
```

**主要功能**：
- 实体生命周期管理
- 场景管理和切换
- 系统协调和更新
- 事件传播

**实体管理示例**：
```typescript
// 创建实体
public createEntity(name: string = '实体'): Entity {
  const entity = new Entity(name);
  entity.id = generateUUID();
  entity.setWorld(this);

  this.entities.set(entity.id, entity);

  // 添加到当前场景
  if (this.activeScene) {
    this.activeScene.addEntity(entity);
  }

  this.emit('entityCreated', entity);
  return entity;
}
```

## 渲染系统

### 1. 渲染器（Renderer）

基于Three.js WebGL渲染器，提供高性能3D渲染能力：

**核心特性**：
- WebGL硬件加速渲染
- 多种阴影类型支持（PCF、PCFSoft、VSM）
- 色调映射和颜色空间管理
- 抗锯齿和像素比适配
- 渲染目标和多通道渲染

**渲染管线**：
1. 场景遍历和视锥体剔除
2. 深度排序和批处理
3. 光照计算和阴影映射
4. 材质渲染和纹理采样
5. 后处理效果应用

### 2. 渲染系统（RenderSystem）

**功能模块**：
- 相机管理（透视/正交投影）
- 光照系统（方向光、点光源、聚光灯、环境光）
- 阴影系统（实时阴影映射）
- 后处理系统（FXAA、SSAO、Bloom等）
- LOD系统（细节层次优化）

### 3. 材质系统

**材质类型**：
- Basic Material（基础材质）
- Standard Material（标准PBR材质）
- Physical Material（物理材质）
- Phong Material（Phong光照模型）
- Lambert Material（Lambert光照模型）

**PBR渲染**：
- 基于物理的渲染管线
- 金属度/粗糙度工作流
- IBL环境光照
- 实时全局光照

## 物理系统

### 1. 物理引擎集成

基于Cannon.js物理引擎，提供完整的物理模拟：

**刚体物理**：
- 碰撞检测（AABB、OBB、球体、凸包）
- 物理约束（铰链、滑块、弹簧、固定）
- 力和冲量应用
- 摩擦力和弹性系数

```typescript
class PhysicsSystem extends System {
  private physicsWorld: CANNON.World;
  private bodies: Map<string, PhysicsBody> = new Map();
  private constraints: Map<string, Constraint> = new Map();

  public initialize(): void {
    // 创建物理世界
    this.physicsWorld = new CANNON.World();
    this.physicsWorld.gravity.set(0, -9.82, 0);
    this.physicsWorld.broadphase = new CANNON.NaiveBroadphase();

    // 设置碰撞检测
    this.physicsWorld.defaultContactMaterial.friction = 0.4;
    this.physicsWorld.defaultContactMaterial.restitution = 0.3;
  }

  public fixedUpdate(fixedDeltaTime: number): void {
    // 更新物理世界
    this.physicsWorld.step(fixedDeltaTime);

    // 同步物理体位置到渲染对象
    for (const [entityId, physicsBody] of this.bodies.entries()) {
      physicsBody.updateTransform();
    }
  }
}
```

**软体物理**：
- 布料模拟（粒子-弹簧系统）
- 绳索和链条
- 体积软体（气球、果冻）
- 软体与刚体交互

```typescript
class SoftBodySystem extends System {
  private softBodies: Map<string, SoftBodyComponent> = new Map();
  private spatialPartitioning: SpatialPartitioning;

  public update(deltaTime: number): void {
    // 更新所有软体
    for (const [id, softBody] of this.softBodies.entries()) {
      this.updateSoftBody(softBody, deltaTime);
    }

    // 处理软体碰撞
    this.handleSoftBodyCollisions();
  }

  private updateSoftBody(softBody: SoftBodyComponent, deltaTime: number): void {
    // 更新粒子位置
    for (const particle of softBody.particles) {
      particle.integrate(deltaTime);
    }

    // 更新约束
    for (const constraint of softBody.constraints) {
      constraint.solve();
    }
  }
}
```

### 2. 高级物理功能

**连续碰撞检测（CCD）**：
- 防止高速物体穿透
- 时间步长细分
- 轨迹预测算法

**角色控制器**：
- 胶囊体碰撞器
- 斜坡行走和台阶攀爬
- 跳跃和重力处理
- 移动平台支持

## 动画系统

### 1. 骨骼动画

**核心功能**：
- 骨骼层次结构管理
- 关键帧插值（线性、贝塞尔、样条）
- 动画混合和过渡
- 动画状态机

```typescript
class AnimationSystem extends System {
  private animators: Map<Entity, Animator> = new Map();
  private mixers: Map<Entity, THREE.AnimationMixer> = new Map();

  public createAnimator(entity: Entity, clips: AnimationClip[] = []): Animator {
    const animator = new Animator();

    // 添加动画片段
    for (const clip of clips) {
      animator.addClip(clip);
    }

    // 创建Three.js动画混合器
    const transformComponent = entity.getComponent('Transform') as Transform;
    const object3D = transformComponent?.getObject3D() || new THREE.Object3D();
    const mixer = new THREE.AnimationMixer(object3D);

    this.animators.set(entity, animator);
    this.mixers.set(entity, mixer);

    return animator;
  }

  public update(deltaTime: number): void {
    // 更新所有动画混合器
    this.mixers.forEach((mixer, entity) => {
      mixer.update(deltaTime);
    });

    // 更新所有动画控制器
    this.animators.forEach((animator, entity) => {
      animator.update(deltaTime);
    });
  }
}
```

**高级特性**：
- IK（反向运动学）求解
- 动画重定向（不同骨骼结构间的动画迁移）
- 动画压缩和优化
- 表情动画和变形目标

```typescript
class IKSolver {
  public solveTwoBoneIK(
    startBone: Bone,
    middleBone: Bone,
    endBone: Bone,
    target: Vector3,
    poleVector?: Vector3
  ): void {
    // 两骨骼IK求解算法
    const startPos = startBone.getWorldPosition();
    const middlePos = middleBone.getWorldPosition();
    const endPos = endBone.getWorldPosition();

    const upperLength = startPos.distanceTo(middlePos);
    const lowerLength = middlePos.distanceTo(endPos);
    const targetDistance = startPos.distanceTo(target);

    // 计算关节角度
    const cosAngle = (upperLength * upperLength + lowerLength * lowerLength - targetDistance * targetDistance)
                   / (2 * upperLength * lowerLength);
    const angle = Math.acos(Math.max(-1, Math.min(1, cosAngle)));

    // 应用旋转
    middleBone.rotation.setFromAxisAngle(new Vector3(0, 0, 1), Math.PI - angle);
  }
}
```

### 2. 动画混合

**混合类型**：
- 线性混合（权重插值）
- 加法混合（动画叠加）
- 遮罩混合（部分骨骼混合）
- 层级混合（多层动画组合）

## 音频系统

### 1. 3D音频

基于Web Audio API，提供沉浸式音频体验：

**空间音频**：
- 3D位置音频（距离衰减、方向性）
- 多普勒效应模拟
- 环境混响和回声
- HRTF（头部相关传输函数）

```typescript
class AudioSystem extends System {
  private context: AudioContext;
  private listener: AudioListener;
  private sources: Map<string, AudioSource> = new Map();
  private bufferCache: Map<string, AudioBuffer> = new Map();

  public initialize(): void {
    // 创建音频上下文
    this.context = new (window.AudioContext || window.webkitAudioContext)();
    this.listener = this.context.listener;

    // 设置3D音频参数
    if (this.listener.positionX) {
      this.listener.positionX.value = 0;
      this.listener.positionY.value = 0;
      this.listener.positionZ.value = 0;
    }
  }

  public createSource(id: string, type: AudioType = AudioType.SOUND): AudioSource {
    const source = new AudioSource({
      id,
      type,
      context: this.context,
      listener: this.listener
    });

    this.sources.set(id, source);
    return source;
  }

  public play(id: string, url: string, options: any = {}): boolean {
    let source = this.sources.get(id);
    if (!source) {
      source = this.createSource(id, options.type);
    }

    // 设置3D位置
    if (options.position) {
      source.setPosition(options.position.x, options.position.y, options.position.z);
    }

    // 加载并播放音频
    if (this.bufferCache.has(url)) {
      const buffer = this.bufferCache.get(url);
      source.setBuffer(buffer);
      source.play(options.offset, options.duration);
      return true;
    }

    return false;
  }
}
```

### 2. 音频管理

**功能特性**：
- 音频资源加载和缓存
- 音频分组和混音
- 动态音量控制
- 音频压缩和流式播放

## 输入系统

### 1. 多设备支持

**输入设备**：
- 键盘（按键状态管理）
- 鼠标（位置、按键、滚轮）
- 游戏手柄（Xbox、PlayStation控制器）
- 触摸屏（多点触控、手势识别）

```typescript
class InputSystem extends System {
  private keyStates: Map<string, KeyState> = new Map();
  private mouseState: MouseState = new MouseState();
  private gamepads: Map<number, GamepadState> = new Map();
  private touchStates: Map<number, TouchState> = new Map();

  public initialize(): void {
    // 注册键盘事件
    document.addEventListener('keydown', this.onKeyDown.bind(this));
    document.addEventListener('keyup', this.onKeyUp.bind(this));

    // 注册鼠标事件
    document.addEventListener('mousedown', this.onMouseDown.bind(this));
    document.addEventListener('mouseup', this.onMouseUp.bind(this));
    document.addEventListener('mousemove', this.onMouseMove.bind(this));

    // 注册触摸事件
    document.addEventListener('touchstart', this.onTouchStart.bind(this));
    document.addEventListener('touchend', this.onTouchEnd.bind(this));
    document.addEventListener('touchmove', this.onTouchMove.bind(this));
  }

  public update(deltaTime: number): void {
    // 更新游戏手柄状态
    this.updateGamepads();

    // 处理按键状态更新
    for (const [key, state] of this.keyStates.entries()) {
      if (state === KeyState.DOWN) {
        this.keyStates.set(key, KeyState.PRESSED);
      } else if (state === KeyState.UP) {
        this.keyStates.set(key, KeyState.NONE);
      }
    }
  }

  private updateGamepads(): void {
    const gamepads = navigator.getGamepads();
    for (let i = 0; i < gamepads.length; i++) {
      const gamepad = gamepads[i];
      if (gamepad) {
        this.updateGamepadState(i, gamepad);
      }
    }
  }
}
```

### 2. 输入映射

**映射系统**：
- 动作映射（将输入映射到游戏动作）
- 轴映射（模拟输入处理）
- 组合键支持
- 自定义输入配置

```typescript
class InputManager {
  private actionMappings: Map<string, InputAction> = new Map();
  private axisMappings: Map<string, AxisMapping> = new Map();

  public mapAction(actionName: string, keys: string[], callback: () => void): void {
    const action = new InputAction(actionName, keys, callback);
    this.actionMappings.set(actionName, action);
  }

  public mapAxis(axisName: string, positiveKey: string, negativeKey: string): void {
    const axis = new AxisMapping(axisName, positiveKey, negativeKey);
    this.axisMappings.set(axisName, axis);
  }

  public getAxisValue(axisName: string): number {
    const axis = this.axisMappings.get(axisName);
    if (!axis) return 0;

    let value = 0;
    if (InputSystem.isKeyPressed(axis.positiveKey)) value += 1;
    if (InputSystem.isKeyPressed(axis.negativeKey)) value -= 1;

    return value;
  }
}
```

## 场景管理

### 1. 场景系统

**场景结构**：
- 层次化场景图
- 空间分区（八叉树、BSP树）
- 视锥体剔除优化
- 场景序列化和反序列化

### 2. 资产管理

**资产类型**：
- 3D模型（glTF、FBX、OBJ）
- 纹理（PNG、JPG、HDR、EXR）
- 音频（MP3、WAV、OGG）
- 动画数据
- 着色器程序

**加载策略**：
- 异步加载和进度跟踪
- 资源缓存和内存管理
- 热重载支持
- 依赖关系管理

## 网络系统

### 1. 多人协作

**网络架构**：
- WebSocket实时通信
- WebRTC点对点连接
- 状态同步和预测
- 网络延迟补偿

### 2. 数据同步

**同步机制**：
- 实体状态同步
- 动画同步
- 物理状态同步
- 用户输入同步

## 调试和优化

### 1. 性能监控

**监控指标**：
- FPS和帧时间
- 内存使用情况
- 渲染统计（绘制调用、三角形数量）
- 物理计算性能

### 2. 调试工具

**可视化调试**：
- 物理碰撞器显示
- 骨骼和关节可视化
- 光照和阴影调试
- 性能分析器

## 扩展性设计

### 1. 插件系统

**扩展机制**：
- 自定义组件开发
- 系统扩展接口
- 事件驱动架构
- 模块化设计

### 2. 平台适配

**跨平台支持**：
- Web浏览器（WebGL）
- 移动设备（WebGL ES）
- VR/AR设备（WebXR）
- 桌面应用（Electron）

## 高级功能模块

### 1. XR/VR支持

**虚拟现实功能**：
- WebXR API集成
- VR设备检测和初始化
- 立体渲染（左右眼分离渲染）
- 6DOF头部追踪
- 手柄输入处理
- 房间尺度追踪

**增强现实功能**：
- AR会话管理
- 平面检测和锚点
- 光照估计
- 遮挡处理
- 手势识别

### 2. 地形系统

**地形生成**：
- 高度图地形生成
- 程序化地形生成
- 多层纹理混合
- 细节纹理和法线贴图
- LOD地形优化

**植被系统**：
- 实例化草地渲染
- 树木和植物放置
- 风力动画效果
- 季节变化系统

### 3. 天气和环境系统

**天气模拟**：
- 动态天空盒
- 云层渲染
- 降雨和降雪效果
- 雾效和大气散射
- 昼夜循环

**水体系统**：
- 实时水面渲染
- 波浪模拟
- 反射和折射
- 水下效果
- 浮力物理

### 4. 粒子系统

**粒子效果**：
- GPU粒子系统
- 发射器配置
- 粒子生命周期管理
- 碰撞和物理交互
- 纹理动画和序列帧

**特效系统**：
- 爆炸和火焰效果
- 烟雾和蒸汽
- 魔法和能量效果
- 环境粒子（灰尘、花粉）

### 5. AI系统

**人工智能功能**：
- 导航网格（NavMesh）
- 路径查找算法（A*、Dijkstra）
- 行为树系统
- 状态机AI
- 群体行为模拟

**NPC系统**：
- 角色AI控制
- 对话系统
- 任务系统
- 情感状态模拟

## 性能优化技术

### 1. 渲染优化

**几何优化**：
- 网格简化和LOD
- 实例化渲染
- 批处理优化
- 视锥体剔除
- 遮挡剔除

**纹理优化**：
- 纹理压缩（DXT、ETC、ASTC）
- Mipmap生成
- 纹理流式加载
- 纹理图集合并

### 2. 内存管理

**内存优化**：
- 对象池技术
- 垃圾回收优化
- 内存泄漏检测
- 资源引用计数
- 智能缓存策略

**加载优化**：
- 异步资源加载
- 预加载策略
- 渐进式加载
- 压缩和解压缩
- CDN分发优化

### 3. 多线程处理

**Web Worker支持**：
- 物理计算线程分离
- 资源加载后台处理
- AI计算并行化
- 音频处理线程

## 开发工具集成

### 1. 可视化编辑器

**场景编辑器**：
- 3D场景预览
- 实体层次结构编辑
- 组件属性面板
- 实时预览更新
- 撤销/重做系统

**材质编辑器**：
- 节点式材质编辑
- 实时材质预览
- 着色器代码生成
- 材质库管理

### 2. 脚本系统

**可视化脚本**：
- 节点式逻辑编辑
- 事件驱动编程
- 变量和函数管理
- 调试和断点支持

**TypeScript集成**：
- 类型安全的脚本开发
- 智能代码提示
- 热重载支持
- 错误检测和调试

### 3. 资产管道

**资产处理**：
- 模型导入和优化
- 纹理压缩和转换
- 动画数据处理
- 音频格式转换
- 自动化构建流程

## 国际化支持

### 1. 多语言系统

**文本国际化**：
- 多语言文本管理
- 动态语言切换
- 文本格式化
- 复数形式处理
- 从右到左文本支持

**资源国际化**：
- 多语言音频资源
- 地区化纹理和模型
- 文化适配内容

### 2. 本地化工具

**翻译工作流**：
- 翻译文件导出/导入
- 翻译进度跟踪
- 术语一致性检查
- 自动化翻译集成

## 安全和隐私

### 1. 数据安全

**安全措施**：
- 资源加密和解密
- 网络通信加密
- 用户数据保护
- 防作弊机制

### 2. 隐私保护

**隐私功能**：
- 用户同意管理
- 数据最小化原则
- 匿名化处理
- GDPR合规支持

## 部署和分发

### 1. 构建系统

**构建优化**：
- 代码分割和懒加载
- 资源压缩和优化
- 缓存策略配置
- 版本管理

### 2. 云服务集成

**云平台支持**：
- CDN内容分发
- 云存储集成
- 实时数据库
- 用户认证服务
- 分析和监控

## 社区和生态

### 1. 开发者社区

**社区支持**：
- 开发文档和教程
- 示例项目和模板
- 开发者论坛
- 技术支持服务

### 2. 第三方集成

**插件生态**：
- 第三方插件市场
- API和SDK提供
- 合作伙伴集成
- 开源贡献支持

## 未来发展方向

### 1. 技术演进

**新技术集成**：
- WebGPU支持
- 机器学习集成
- 区块链技术
- 云渲染服务

### 2. 功能扩展

**功能规划**：
- 更强大的AI系统
- 增强的VR/AR功能
- 实时光线追踪
- 程序化内容生成

## 总结

DL引擎通过现代化的ECS架构、完整的功能模块和高性能的渲染管线，为数字化学习应用提供了强大的技术基础。其模块化设计和扩展性架构使得开发者能够快速构建复杂的3D交互应用，同时保持良好的性能和可维护性。

引擎的核心优势包括：

1. **架构优势**：ECS架构提供了良好的可扩展性和维护性
2. **功能完整**：涵盖了3D应用开发的所有核心功能模块
3. **性能优化**：多层次的性能优化确保流畅的用户体验
4. **开发效率**：丰富的工具链和可视化编辑器提升开发效率
5. **跨平台支持**：基于Web技术，天然支持多平台部署
6. **社区生态**：开放的架构支持第三方扩展和社区贡献

DL引擎为数字化学习领域提供了一个强大、灵活、易用的3D开发平台，能够满足从简单交互到复杂虚拟环境的各种应用需求。

## 使用示例

### 基础场景创建

```typescript
// 创建引擎实例
const engine = new Engine({
  canvas: document.getElementById('canvas') as HTMLCanvasElement,
  autoStart: true,
  debug: true
});

// 获取世界实例
const world = engine.getWorld();

// 创建场景
const scene = new Scene('主场景');
world.addScene(scene);
world.setActiveScene(scene);

// 创建相机
const cameraEntity = world.createEntity('主相机');
const camera = new Camera({
  type: CameraType.PERSPECTIVE,
  fov: 75,
  aspect: window.innerWidth / window.innerHeight,
  near: 0.1,
  far: 1000
});
cameraEntity.addComponent(camera);

// 设置相机位置
const cameraTransform = cameraEntity.getComponent('Transform') as Transform;
cameraTransform.setPosition(0, 5, 10);
cameraTransform.lookAt(0, 0, 0);

// 创建光源
const lightEntity = world.createEntity('方向光');
const light = new Light({
  type: LightType.DIRECTIONAL,
  intensity: 1.0,
  color: 0xffffff
});
lightEntity.addComponent(light);

// 创建立方体
const cubeEntity = world.createEntity('立方体');
const meshRenderer = new MeshRenderer({
  geometry: new THREE.BoxGeometry(1, 1, 1),
  material: new THREE.MeshStandardMaterial({ color: 0x00ff00 })
});
cubeEntity.addComponent(meshRenderer);

// 添加物理组件
const physicsBody = new PhysicsBodyComponent({
  type: PhysicsBodyType.DYNAMIC,
  shape: PhysicsShapeType.BOX,
  mass: 1.0
});
cubeEntity.addComponent(physicsBody);

// 添加系统
const renderSystem = new RenderSystem(engine.getRenderer());
const physicsSystem = new PhysicsSystem();
const animationSystem = new AnimationSystem();
const audioSystem = new AudioSystem();
const inputSystem = new InputSystem();

world.addSystem(renderSystem);
world.addSystem(physicsSystem);
world.addSystem(animationSystem);
world.addSystem(audioSystem);
world.addSystem(inputSystem);

// 启动引擎
engine.start();
```

### 角色控制示例

```typescript
// 创建角色实体
const playerEntity = world.createEntity('玩家');

// 添加角色控制器
const characterController = new CharacterController({
  height: 1.8,
  radius: 0.3,
  stepHeight: 0.3,
  slopeLimit: 45
});
playerEntity.addComponent(characterController);

// 添加输入处理组件
const inputComponent = new InputComponent();
playerEntity.addComponent(inputComponent);

// 设置输入映射
const inputManager = InputManager.getInstance();
inputManager.mapAxis('MoveForward', 'KeyW', 'KeyS');
inputManager.mapAxis('MoveRight', 'KeyD', 'KeyA');
inputManager.mapAction('Jump', ['Space'], () => {
  characterController.jump();
});

// 创建角色控制系统
class PlayerControlSystem extends System {
  public update(deltaTime: number): void {
    const moveForward = inputManager.getAxisValue('MoveForward');
    const moveRight = inputManager.getAxisValue('MoveRight');

    const movement = new Vector3(moveRight, 0, -moveForward);
    movement.normalize().multiplyScalar(5.0 * deltaTime);

    characterController.move(movement);
  }
}

world.addSystem(new PlayerControlSystem());
```

### 动画播放示例

```typescript
// 加载角色模型和动画
const assetManager = engine.getAssetManager();
const characterModel = await assetManager.loadModel('character.glb');
const walkAnimation = await assetManager.loadAnimation('walk.glb');
const runAnimation = await assetManager.loadAnimation('run.glb');

// 创建角色实体
const characterEntity = world.createEntity('角色');
characterEntity.addComponent(new MeshRenderer({
  geometry: characterModel.geometry,
  material: characterModel.material
}));

// 创建动画控制器
const animationSystem = world.getSystem('AnimationSystem') as AnimationSystem;
const animator = animationSystem.createAnimator(characterEntity, [
  walkAnimation,
  runAnimation
]);

// 播放动画
animator.play('walk', {
  loop: true,
  fadeInTime: 0.2
});

// 动画状态切换
if (inputManager.getAxisValue('MoveForward') > 0.5) {
  animator.crossFade('run', 0.3);
} else {
  animator.crossFade('walk', 0.3);
}
```
