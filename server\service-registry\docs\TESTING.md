# 服务注册中心测试指南

本文档提供了服务注册中心的测试指南，包括单元测试、性能测试和集成测试。

## 单元测试

### 概述

单元测试用于测试服务注册中心的各个组件和功能，确保它们按预期工作。我们使用Jest作为测试框架，结合NestJS的测试工具进行测试。

### 测试覆盖范围

单元测试覆盖以下主要功能：

1. **服务注册与发现**：测试服务注册、心跳更新、服务发现等功能
2. **负载均衡策略**：测试各种负载均衡策略的正确性和一致性
3. **服务缓存机制**：测试缓存的命中、失效和更新机制
4. **监控和告警**：测试监控指标收集和告警触发机制

### 运行单元测试

```bash
# 运行所有单元测试
npm run test

# 运行特定测试文件
npm run test -- registry.service.spec.ts

# 运行特定测试套件
npm run test -- --testPathPattern=load-balancer

# 生成测试覆盖率报告
npm run test:cov
```

### 编写单元测试

单元测试文件应放在与被测试文件相同的目录下，并以`.spec.ts`结尾。例如，对于`registry.service.ts`，其测试文件应为`registry.service.spec.ts`。

以下是一个单元测试示例：

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { RegistryService } from './registry.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ServiceEntity } from './entities/service.entity';
import { ServiceInstanceEntity } from './entities/service-instance.entity';
import { EventBusService } from '../../../shared/event-bus';

describe('RegistryService', () => {
  let service: RegistryService;
  let serviceRepository;
  let instanceRepository;
  let eventBusService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RegistryService,
        {
          provide: getRepositoryToken(ServiceEntity),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ServiceInstanceEntity),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: EventBusService,
          useValue: {
            publish: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<RegistryService>(RegistryService);
    serviceRepository = module.get(getRepositoryToken(ServiceEntity));
    instanceRepository = module.get(getRepositoryToken(ServiceInstanceEntity));
    eventBusService = module.get<EventBusService>(EventBusService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // 更多测试用例...
});
```

### 测试最佳实践

1. **测试隔离**：每个测试应该是独立的，不依赖于其他测试的状态
2. **模拟外部依赖**：使用Jest的模拟功能模拟外部依赖，如数据库、Redis等
3. **测试边缘情况**：不仅测试正常情况，还要测试边缘情况和错误处理
4. **保持测试简单**：每个测试应该只测试一个功能点
5. **使用描述性的测试名称**：测试名称应该清晰描述测试的内容

## 性能测试

### 概述

性能测试用于评估服务注册中心在高负载下的性能和稳定性。我们使用k6作为性能测试工具，它可以模拟大量并发请求并收集性能指标。

### 测试场景

性能测试覆盖以下主要场景：

1. **服务注册性能**：测试服务注册的吞吐量和响应时间
2. **服务发现性能**：测试服务发现的吞吐量和响应时间，特别是在启用缓存的情况下
3. **负载均衡性能**：测试各种负载均衡策略在高并发下的性能
4. **缓存性能**：测试缓存机制在高并发下的命中率和响应时间

### 运行性能测试

```bash
# 安装k6
npm install -g k6

# 运行负载均衡性能测试
k6 run tests/performance/src/load-balancer-performance.js

# 运行服务缓存性能测试
k6 run tests/performance/src/service-cache-performance.js

# 自定义测试参数
k6 run --env SERVICE_REGISTRY_URL=http://localhost:4010 --env SESSION_COUNT=2000 tests/performance/src/load-balancer-performance.js
```

### 性能测试脚本示例

以下是一个性能测试脚本示例：

```javascript
import { check } from 'k6';
import http from 'k6/http';
import { sleep } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';

// 性能指标
const requestLatency = new Trend('request_latency');
const requestsPerSecond = new Rate('requests_per_second');
const errorRate = new Rate('error_rate');

// 配置
const CONFIG = {
  serviceRegistry: __ENV.SERVICE_REGISTRY_URL || 'http://localhost:4010',
  testService: __ENV.TEST_SERVICE || 'test-service',
};

// 测试配置
export const options = {
  scenarios: {
    constant_load: {
      executor: 'constant-vus',
      vus: 50,
      duration: '1m',
    },
  },
  thresholds: {
    'request_latency': ['p(95)<500'], // 95%的请求延迟小于500ms
    'requests_per_second': ['avg>100'], // 平均每秒请求数大于100
    'error_rate': ['rate<0.01'], // 错误率小于1%
  },
};

// 默认函数 - 在测试期间为每个虚拟用户运行
export default function() {
  const startTime = new Date();
  
  const res = http.get(`${CONFIG.serviceRegistry}/registry/services/${CONFIG.testService}/instances`);
  
  const duration = new Date() - startTime;
  
  // 记录请求延迟
  requestLatency.add(duration);
  
  // 记录请求成功/失败
  errorRate.add(res.status !== 200);
  
  // 记录每秒请求数
  requestsPerSecond.add(1);
  
  // 检查响应
  check(res, {
    '请求成功': (r) => r.status === 200,
  });
  
  // 短暂休息
  sleep(1);
}
```

### 性能测试最佳实践

1. **逐步增加负载**：从低负载开始，逐步增加到目标负载
2. **监控资源使用**：在测试期间监控CPU、内存、网络等资源使用情况
3. **设置性能基准**：为关键指标设置基准值，如响应时间、吞吐量等
4. **测试不同配置**：测试不同的配置参数，找到最佳配置
5. **定期进行性能测试**：定期进行性能测试，及时发现性能退化

## 集成测试

### 概述

集成测试用于测试服务注册中心与其他服务的集成，确保它们能够正常协同工作。我们使用Jest和Axios进行集成测试。

### 测试场景

集成测试覆盖以下主要场景：

1. **服务注册与发现流程**：测试服务注册、发现的端到端流程
2. **负载均衡策略**：测试负载均衡策略在实际环境中的效果
3. **服务健康检查**：测试健康检查机制的正确性
4. **服务缓存**：测试缓存机制在实际环境中的效果

### 运行集成测试

```bash
# 运行所有集成测试
npm run test:integration

# 运行特定集成测试
npm run test:integration -- service-registry.integration.test.ts
```

### 集成测试示例

以下是一个集成测试示例：

```typescript
import axios from 'axios';
import waitForExpect from 'wait-for-expect';
import { TEST_CONFIG } from './setup';

describe('服务注册与发现集成测试', () => {
  const testServiceName = `test-service-${Date.now()}`;
  const testInstances = [
    {
      instanceId: `test-instance-1-${Date.now()}`,
      host: 'localhost',
      port: 3001,
    },
    {
      instanceId: `test-instance-2-${Date.now()}`,
      host: 'localhost',
      port: 3002,
    },
  ];

  // 注册的实例ID列表
  const registeredInstanceIds = [];

  // 在所有测试开始前注册测试服务实例
  beforeAll(async () => {
    for (const instance of testInstances) {
      try {
        const response = await axios.post(
          `${TEST_CONFIG.serviceRegistry.url}/registry/services`,
          {
            name: testServiceName,
            instanceId: instance.instanceId,
            host: instance.host,
            port: instance.port,
          }
        );

        if (response.status === 201 || response.status === 200) {
          registeredInstanceIds.push(instance.instanceId);
        }
      } catch (error) {
        console.error(`注册服务实例失败: ${error.message}`);
      }
    }
  });

  // 在所有测试结束后注销测试服务实例
  afterAll(async () => {
    for (const instanceId of registeredInstanceIds) {
      try {
        await axios.delete(
          `${TEST_CONFIG.serviceRegistry.url}/registry/services/${testServiceName}/instances/${instanceId}`
        );
      } catch (error) {
        console.error(`注销服务实例失败: ${error.message}`);
      }
    }
  });

  test('应该能够发现所有注册的服务实例', async () => {
    const response = await axios.get(
      `${TEST_CONFIG.serviceRegistry.url}/registry/services/${testServiceName}/instances`
    );

    expect(response.status).toBe(200);
    expect(response.data).toBeInstanceOf(Array);
    expect(response.data.length).toBe(testInstances.length);
  });

  // 更多测试用例...
});
```

### 集成测试最佳实践

1. **使用独立的测试环境**：使用独立的测试环境，避免影响生产环境
2. **清理测试数据**：在测试结束后清理测试数据，避免影响其他测试
3. **模拟外部服务**：如果需要，使用模拟服务替代外部服务
4. **测试超时处理**：设置合理的超时时间，避免测试卡住
5. **记录详细日志**：记录详细的测试日志，方便排查问题

## 测试覆盖率目标

为确保代码质量，我们设定了以下测试覆盖率目标：

- **单元测试**：代码覆盖率 > 80%
- **集成测试**：关键流程覆盖率 > 90%
- **性能测试**：所有关键API都有性能测试

## 持续集成

我们使用GitHub Actions进行持续集成，每次提交代码都会自动运行单元测试和集成测试。性能测试则在每周定期运行。

### CI配置示例

```yaml
name: Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: ir_service_registry_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:6.0
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '14'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run unit tests
      run: npm run test:cov
      
    - name: Run integration tests
      run: npm run test:integration
      
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

## 故障排除

### 常见问题

1. **测试超时**：检查测试环境是否正常，增加超时时间
2. **数据库连接失败**：检查数据库配置和连接
3. **Redis连接失败**：检查Redis配置和连接
4. **测试数据冲突**：确保每个测试使用唯一的测试数据
5. **性能测试结果不稳定**：增加测试样本数量，排除外部因素影响
