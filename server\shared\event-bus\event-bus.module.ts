/**
 * 事件总线模块
 */
import { Module, DynamicModule, Provider, Global } from '@nestjs/common';
import { DiscoveryModule } from '@nestjs/core';
import { EventBusService } from './event-bus.service';
import { EnhancedEventBusService } from './enhanced-event-bus.service';
import { EventBusOptions } from './event-bus.interface';
import { EVENT_BUS_OPTIONS } from './event-bus.constants';
import { EventExplorerService } from './event-explorer.service';
import { RedisModule } from '@liaoliaots/nestjs-redis';

@Global()
@Module({
  imports: [DiscoveryModule],
  providers: [EventExplorerService],
  exports: [],
})
export class EventBusModule {
  /**
   * 注册事件总线模块
   * @param options 事件总线配置
   */
  static register(options: EventBusOptions): DynamicModule {
    const redisImports = options.redis
      ? [
          RedisModule.forRoot({
            config: {
              host: options.redis.host,
              port: options.redis.port,
              password: options.redis.password,
              db: options.redis.db || 0,
            },
          }),
        ]
      : [];

    const providers: Provider[] = [
      {
        provide: EVENT_BUS_OPTIONS,
        useValue: options,
      },
      EventBusService,
      EnhancedEventBusService,
    ];

    return {
      module: EventBusModule,
      imports: [...redisImports],
      providers,
      exports: [EventBusService, EnhancedEventBusService],
    };
  }
}
