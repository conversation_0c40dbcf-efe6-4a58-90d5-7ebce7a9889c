/**
 * 材质系统
 * 负责管理和优化材质
 */
import * as THREE from 'three';
import { System } from '../../core/System';
/**
 * 材质系统配置接口
 */
export interface MaterialSystemOptions {
    /** 是否启用自动优化 */
    autoOptimize?: boolean;
    /** 是否启用自动降级 */
    autoDowngrade?: boolean;
    /** 最大材质数量 */
    maxMaterials?: number;
    /** 是否启用材质缓存 */
    enableCache?: boolean;
}
/**
 * 材质系统类
 */
export declare class MaterialSystem extends System {
    /** 系统类型 */
    static readonly type: string;
    /** 材质工厂 */
    private factory;
    /** 材质优化器 */
    private optimizer;
    /** 材质转换器 */
    private converter;
    /** 设备能力检测 */
    private deviceCapabilities;
    /** 是否启用自动优化 */
    private autoOptimize;
    /** 是否启用自动降级 */
    private autoDowngrade;
    /** 最大材质数量 */
    private maxMaterials;
    /** 是否启用材质缓存 */
    private enableCache;
    /** 材质映射表 */
    private materials;
    /** 材质使用计数 */
    private materialUsageCount;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 创建材质系统
     * @param options 材质系统配置
     */
    constructor(options?: MaterialSystemOptions);
    /**
     * 创建材质
     * @param type 材质类型
     * @param params 材质参数
     * @returns 材质
     */
    createMaterial(type: string, params?: any): THREE.Material;
    /**
     * 获取材质
     * @param id 材质ID
     * @returns 材质
     */
    getMaterial(id: string): THREE.Material | null;
    /**
     * 释放材质
     * @param material 材质
     */
    releaseMaterial(material: THREE.Material): void;
    /**
     * 优化材质
     * @param material 材质
     * @returns 优化后的材质
     */
    optimizeMaterial(material: THREE.Material): THREE.Material;
    /**
     * 转换材质类型
     * @param material 材质
     * @param targetType 目标类型
     * @returns 转换后的材质
     */
    convertMaterial(material: THREE.Material, targetType: string): THREE.Material;
    /**
     * 降级材质
     * @param material 材质
     * @returns 降级后的材质
     */
    downgradeMaterial(material: THREE.Material): THREE.Material;
    /**
     * 升级材质
     * @param material 材质
     * @returns 升级后的材质
     */
    upgradeMaterial(material: THREE.Material): THREE.Material;
    /**
     * 设置是否启用自动优化
     * @param enabled 是否启用
     */
    setAutoOptimize(enabled: boolean): void;
    /**
     * 设置是否启用自动降级
     * @param enabled 是否启用
     */
    setAutoDowngrade(enabled: boolean): void;
    /**
     * 设置最大材质数量
     * @param maxMaterials 最大材质数量
     */
    setMaxMaterials(maxMaterials: number): void;
    /**
     * 设置是否启用材质缓存
     * @param enabled 是否启用
     */
    setEnableCache(enabled: boolean): void;
    /**
     * 清理未使用的材质
     */
    cleanupUnusedMaterials(): void;
    /**
     * 清理所有材质
     */
    clearAllMaterials(): void;
    /**
     * 获取材质数量
     * @returns 材质数量
     */
    getMaterialCount(): number;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @returns 当前实例，用于链式调用
     */
    on(event: string, callback: (...args: any[]) => void): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @returns 当前实例，用于链式调用
     */
    off(event: string, callback?: (...args: any[]) => void): this;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 生成材质ID
     * @param type 材质类型
     * @param params 材质参数
     * @returns 材质ID
     */
    private generateMaterialId;
    /**
     * 获取材质ID
     * @param material 材质
     * @returns 材质ID
     */
    private getMaterialId;
    /**
     * 增加材质使用计数
     * @param id 材质ID
     */
    private incrementUsageCount;
    /**
     * 减少材质使用计数
     * @param id 材质ID
     */
    private decrementUsageCount;
    /**
     * 检查材质数量是否超过限制
     */
    private checkMaterialLimit;
}
