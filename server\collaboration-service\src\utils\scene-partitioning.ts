/**
 * 场景分区工具
 * 用于大型场景的协作编辑优化，只同步用户关注区域的变更
 */
import { Logger } from '@nestjs/common';

/**
 * 三维向量接口
 */
export interface Vector3 {
  x: number;
  y: number;
  z: number;
}

/**
 * 场景区域接口
 */
export interface SceneRegion {
  /**
   * 区域ID
   */
  id: string;

  /**
   * 区域名称
   */
  name: string;

  /**
   * 区域中心点
   */
  center: Vector3;

  /**
   * 区域半径
   */
  radius: number;

  /**
   * 区域边界盒最小点
   */
  min?: Vector3;

  /**
   * 区域边界盒最大点
   */
  max?: Vector3;

  /**
   * 区域内的实体ID列表
   */
  entityIds: string[];

  /**
   * 区域优先级
   */
  priority: number;

  /**
   * 区域是否活跃
   */
  active: boolean;

  /**
   * 区域最后更新时间
   */
  lastUpdated: number;

  /**
   * 区域元数据
   */
  metadata?: Record<string, any>;
}

/**
 * 用户兴趣区域接口
 */
export interface UserInterestArea {
  /**
   * 用户ID
   */
  userId: string;

  /**
   * 兴趣区域中心点
   */
  center: Vector3;

  /**
   * 兴趣区域半径
   */
  radius: number;

  /**
   * 兴趣区域最后更新时间
   */
  lastUpdated: number;

  /**
   * 用户订阅的区域ID列表
   */
  subscribedRegions: string[];
}

/**
 * 场景分区配置接口
 */
export interface ScenePartitioningConfig {
  /**
   * 是否启用场景分区
   */
  enabled?: boolean;

  /**
   * 默认区域大小
   */
  defaultRegionSize?: number;

  /**
   * 默认用户兴趣区域半径
   */
  defaultUserInterestRadius?: number;

  /**
   * 区域重叠比例
   */
  regionOverlapRatio?: number;

  /**
   * 是否自动调整区域大小
   */
  autoAdjustRegionSize?: boolean;

  /**
   * 最大区域数量
   */
  maxRegionCount?: number;

  /**
   * 每个区域的最大实体数量
   */
  maxEntitiesPerRegion?: number;

  /**
   * 是否启用动态区域
   */
  enableDynamicRegions?: boolean;

  /**
   * 是否启用区域优先级
   */
  enableRegionPriority?: boolean;

  /**
   * 是否启用区域缓存
   */
  enableRegionCache?: boolean;

  /**
   * 区域缓存过期时间（毫秒）
   */
  regionCacheExpiration?: number;
}

/**
 * 场景分区管理器
 * 用于管理场景分区和用户兴趣区域
 */
export class ScenePartitioning {
  private logger = new Logger(ScenePartitioning.name);
  private config: Required<ScenePartitioningConfig>;
  private regions: Map<string, SceneRegion> = new Map();
  private userInterestAreas: Map<string, UserInterestArea> = new Map();
  private entityRegionMap: Map<string, string[]> = new Map();
  private regionCache: Map<string, { data: any; expires: number }> = new Map();

  /**
   * 创建场景分区管理器
   * @param config 配置
   */
  constructor(config?: ScenePartitioningConfig) {
    this.config = {
      enabled: true,
      defaultRegionSize: 100,
      defaultUserInterestRadius: 50,
      regionOverlapRatio: 0.2,
      autoAdjustRegionSize: true,
      maxRegionCount: 100,
      maxEntitiesPerRegion: 1000,
      enableDynamicRegions: true,
      enableRegionPriority: true,
      enableRegionCache: true,
      regionCacheExpiration: 5000, // 5秒
      ...config,
    };
  }

  /**
   * 创建场景区域
   * @param id 区域ID
   * @param name 区域名称
   * @param center 区域中心点
   * @param radius 区域半径
   * @param entityIds 区域内的实体ID列表
   * @param priority 区域优先级
   * @param metadata 区域元数据
   * @returns 创建的区域
   */
  public createRegion(
    id: string,
    name: string,
    center: Vector3,
    radius: number = this.config.defaultRegionSize,
    entityIds: string[] = [],
    priority: number = 0,
    metadata?: Record<string, any>,
  ): SceneRegion {
    // 创建区域
    const region: SceneRegion = {
      id,
      name,
      center,
      radius,
      entityIds,
      priority,
      active: true,
      lastUpdated: Date.now(),
      metadata,
    };

    // 计算边界盒
    this.calculateRegionBounds(region);

    // 添加到区域映射
    this.regions.set(id, region);

    // 更新实体-区域映射
    for (const entityId of entityIds) {
      this.addEntityToRegion(entityId, id);
    }

    this.logger.debug(`创建区域: ${id}, 实体数量: ${entityIds.length}`);

    return region;
  }

  /**
   * 更新场景区域
   * @param id 区域ID
   * @param updates 更新内容
   * @returns 更新后的区域
   */
  public updateRegion(
    id: string,
    updates: Partial<SceneRegion>,
  ): SceneRegion | null {
    const region = this.regions.get(id);
    if (!region) {
      this.logger.warn(`更新区域失败: 找不到区域 ${id}`);
      return null;
    }

    // 应用更新
    Object.assign(region, updates, { lastUpdated: Date.now() });

    // 如果中心点或半径更新了，重新计算边界盒
    if (updates.center || updates.radius) {
      this.calculateRegionBounds(region);
    }

    // 如果实体列表更新了，更新实体-区域映射
    if (updates.entityIds) {
      // 移除旧映射
      for (const entityId of region.entityIds) {
        this.removeEntityFromRegion(entityId, id);
      }

      // 添加新映射
      for (const entityId of updates.entityIds) {
        this.addEntityToRegion(entityId, id);
      }
    }

    this.logger.debug(`更新区域: ${id}`);

    return region;
  }

  /**
   * 删除场景区域
   * @param id 区域ID
   * @returns 是否成功删除
   */
  public removeRegion(id: string): boolean {
    const region = this.regions.get(id);
    if (!region) {
      this.logger.warn(`删除区域失败: 找不到区域 ${id}`);
      return false;
    }

    // 移除实体-区域映射
    for (const entityId of region.entityIds) {
      this.removeEntityFromRegion(entityId, id);
    }

    // 移除区域
    this.regions.delete(id);

    // 更新用户订阅
    for (const [userId, interestArea] of this.userInterestAreas.entries()) {
      // 忽略未使用的变量
      void userId;
      const index = interestArea.subscribedRegions.indexOf(id);
      if (index !== -1) {
        interestArea.subscribedRegions.splice(index, 1);
      }
    }

    this.logger.debug(`删除区域: ${id}`);

    return true;
  }

  /**
   * 获取场景区域
   * @param id 区域ID
   * @returns 区域
   */
  public getRegion(id: string): SceneRegion | null {
    return this.regions.get(id) || null;
  }

  /**
   * 获取所有场景区域
   * @returns 所有区域
   */
  public getAllRegions(): SceneRegion[] {
    return Array.from(this.regions.values());
  }

  /**
   * 创建用户兴趣区域
   * @param userId 用户ID
   * @param center 兴趣区域中心点
   * @param radius 兴趣区域半径
   * @returns 创建的兴趣区域
   */
  public createUserInterestArea(
    userId: string,
    center: Vector3,
    radius: number = this.config.defaultUserInterestRadius,
  ): UserInterestArea {
    // 创建兴趣区域
    const interestArea: UserInterestArea = {
      userId,
      center,
      radius,
      lastUpdated: Date.now(),
      subscribedRegions: [],
    };

    // 添加到用户兴趣区域映射
    this.userInterestAreas.set(userId, interestArea);

    // 更新订阅区域
    this.updateUserSubscribedRegions(userId);

    this.logger.debug(`创建用户兴趣区域: ${userId}`);

    return interestArea;
  }

  /**
   * 更新用户兴趣区域
   * @param userId 用户ID
   * @param updates 更新内容
   * @returns 更新后的兴趣区域
   */
  public updateUserInterestArea(
    userId: string,
    updates: Partial<UserInterestArea>,
  ): UserInterestArea | null {
    const interestArea = this.userInterestAreas.get(userId);
    if (!interestArea) {
      this.logger.warn(`更新用户兴趣区域失败: 找不到用户 ${userId}`);
      return null;
    }

    // 应用更新
    Object.assign(interestArea, updates, { lastUpdated: Date.now() });

    // 如果中心点或半径更新了，更新订阅区域
    if (updates.center || updates.radius) {
      this.updateUserSubscribedRegions(userId);
    }

    this.logger.debug(`更新用户兴趣区域: ${userId}`);

    return interestArea;
  }

  /**
   * 删除用户兴趣区域
   * @param userId 用户ID
   * @returns 是否成功删除
   */
  public removeUserInterestArea(userId: string): boolean {
    if (!this.userInterestAreas.has(userId)) {
      this.logger.warn(`删除用户兴趣区域失败: 找不到用户 ${userId}`);
      return false;
    }

    // 移除用户兴趣区域
    this.userInterestAreas.delete(userId);

    this.logger.debug(`删除用户兴趣区域: ${userId}`);

    return true;
  }

  /**
   * 获取用户兴趣区域
   * @param userId 用户ID
   * @returns 用户兴趣区域
   */
  public getUserInterestArea(userId: string): UserInterestArea | null {
    return this.userInterestAreas.get(userId) || null;
  }

  /**
   * 获取用户订阅的区域
   * @param userId 用户ID
   * @returns 用户订阅的区域
   */
  public getUserSubscribedRegions(userId: string): SceneRegion[] {
    const interestArea = this.userInterestAreas.get(userId);
    if (!interestArea) {
      return [];
    }

    return interestArea.subscribedRegions
      .map(regionId => this.regions.get(regionId))
      .filter(region => region !== undefined) as SceneRegion[];
  }

  /**
   * 获取实体所在的区域
   * @param entityId 实体ID
   * @returns 实体所在的区域
   */
  public getEntityRegions(entityId: string): SceneRegion[] {
    const regionIds = this.entityRegionMap.get(entityId) || [];
    return regionIds
      .map(regionId => this.regions.get(regionId))
      .filter(region => region !== undefined) as SceneRegion[];
  }

  /**
   * 检查用户是否应该接收实体更新
   * @param userId 用户ID
   * @param entityId 实体ID
   * @returns 是否应该接收更新
   */
  public shouldUserReceiveEntityUpdate(userId: string, entityId: string): boolean {
    // 如果未启用场景分区，所有用户都接收所有更新
    if (!this.config.enabled) {
      return true;
    }

    const interestArea = this.userInterestAreas.get(userId);
    if (!interestArea) {
      // 如果用户没有兴趣区域，默认接收所有更新
      return true;
    }

    // 获取实体所在的区域
    const entityRegions = this.getEntityRegions(entityId);
    if (entityRegions.length === 0) {
      // 如果实体不在任何区域中，默认接收更新
      return true;
    }

    // 检查用户是否订阅了实体所在的任何区域
    for (const region of entityRegions) {
      if (interestArea.subscribedRegions.includes(region.id)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 获取应该接收实体更新的用户
   * @param entityId 实体ID
   * @returns 应该接收更新的用户ID列表
   */
  public getUsersForEntityUpdate(entityId: string): string[] {
    // 如果未启用场景分区，所有用户都接收所有更新
    if (!this.config.enabled) {
      return Array.from(this.userInterestAreas.keys());
    }

    // 获取实体所在的区域
    const entityRegions = this.getEntityRegions(entityId);
    if (entityRegions.length === 0) {
      // 如果实体不在任何区域中，所有用户都接收更新
      return Array.from(this.userInterestAreas.keys());
    }

    // 找出订阅了实体所在区域的用户
    const userIds: string[] = [];
    for (const [userId, interestArea] of this.userInterestAreas.entries()) {
      for (const region of entityRegions) {
        if (interestArea.subscribedRegions.includes(region.id)) {
          userIds.push(userId);
          break;
        }
      }
    }

    return userIds;
  }

  /**
   * 计算区域边界盒
   * @param region 区域
   */
  private calculateRegionBounds(region: SceneRegion): void {
    region.min = {
      x: region.center.x - region.radius,
      y: region.center.y - region.radius,
      z: region.center.z - region.radius,
    };

    region.max = {
      x: region.center.x + region.radius,
      y: region.center.y + region.radius,
      z: region.center.z + region.radius,
    };
  }

  /**
   * 添加实体到区域
   * @param entityId 实体ID
   * @param regionId 区域ID
   */
  private addEntityToRegion(entityId: string, regionId: string): void {
    let regionIds = this.entityRegionMap.get(entityId);
    if (!regionIds) {
      regionIds = [];
      this.entityRegionMap.set(entityId, regionIds);
    }

    if (!regionIds.includes(regionId)) {
      regionIds.push(regionId);
    }
  }

  /**
   * 从区域中移除实体
   * @param entityId 实体ID
   * @param regionId 区域ID
   */
  private removeEntityFromRegion(entityId: string, regionId: string): void {
    const regionIds = this.entityRegionMap.get(entityId);
    if (!regionIds) {
      return;
    }

    const index = regionIds.indexOf(regionId);
    if (index !== -1) {
      regionIds.splice(index, 1);
    }

    if (regionIds.length === 0) {
      this.entityRegionMap.delete(entityId);
    }
  }

  /**
   * 更新用户订阅的区域
   * @param userId 用户ID
   */
  private updateUserSubscribedRegions(userId: string): void {
    const interestArea = this.userInterestAreas.get(userId);
    if (!interestArea) {
      return;
    }

    // 清空当前订阅
    interestArea.subscribedRegions = [];

    // 找出与用户兴趣区域相交的区域
    for (const [regionId, region] of this.regions.entries()) {
      if (this.isInterestAreaOverlappingRegion(interestArea, region)) {
        interestArea.subscribedRegions.push(regionId);
      }
    }

    this.logger.debug(`更新用户订阅区域: ${userId}, 订阅数量: ${interestArea.subscribedRegions.length}`);
  }

  /**
   * 检查兴趣区域是否与区域相交
   * @param interestArea 兴趣区域
   * @param region 区域
   * @returns 是否相交
   */
  private isInterestAreaOverlappingRegion(interestArea: UserInterestArea, region: SceneRegion): boolean {
    // 计算两个中心点之间的距离
    const distance = this.calculateDistance(interestArea.center, region.center);

    // 如果距离小于两个半径之和，则相交
    return distance < (interestArea.radius + region.radius);
  }

  /**
   * 计算两点之间的距离
   * @param a 点A
   * @param b 点B
   * @returns 距离
   */
  private calculateDistance(a: Vector3, b: Vector3): number {
    const dx = a.x - b.x;
    const dy = a.y - b.y;
    const dz = a.z - b.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }
}
