/**
 * 组件管理兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 组件管理兼容性测试
 */
export const componentManagementTest: TestCase = {
  name: '组件管理兼容性测试',
  description: '测试组件管理功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目实体实例
      const originalEntity = new original.Entity('TestEntity');
      
      // 创建重构后项目实体实例
      const refactoredEntity = new refactored.Entity('TestEntity');
      
      // 检查实体实例是否创建成功
      if (!originalEntity || !refactoredEntity) {
        return {
          name: '组件管理兼容性测试',
          passed: false,
          errorMessage: '实体实例创建失败'
        };
      }
      
      // 创建组件
      class OriginalTestComponent extends original.Component {
        constructor(entity: any) {
          super(entity);
          this.value = 42;
        }
      }
      
      class RefactoredTestComponent extends refactored.Component {
        constructor(entity: any) {
          super(entity);
          this.value = 42;
        }
      }
      
      // 添加组件到实体
      const originalComponent = new OriginalTestComponent(originalEntity);
      const refactoredComponent = new RefactoredTestComponent(refactoredEntity);
      
      originalEntity.addComponent(originalComponent);
      refactoredEntity.addComponent(refactoredComponent);
      
      // 检查组件是否添加成功
      const originalComponentCount = originalEntity.getComponentCount();
      const refactoredComponentCount = refactoredEntity.getComponentCount();
      
      if (originalComponentCount !== refactoredComponentCount) {
        return {
          name: '组件管理兼容性测试',
          passed: false,
          errorMessage: `组件数量不一致: 原有项目=${originalComponentCount}, 重构后项目=${refactoredComponentCount}`,
          details: {
            originalComponentCount,
            refactoredComponentCount
          }
        };
      }
      
      // 获取组件
      const originalComponentById = originalEntity.getComponentById(originalComponent.id);
      const refactoredComponentById = refactoredEntity.getComponentById(refactoredComponent.id);
      
      if (!originalComponentById || !refactoredComponentById) {
        return {
          name: '组件管理兼容性测试',
          passed: false,
          errorMessage: '通过ID获取组件失败',
          details: {
            originalComponentById,
            refactoredComponentById
          }
        };
      }
      
      // 检查组件值
      if (originalComponentById.value !== refactoredComponentById.value) {
        return {
          name: '组件管理兼容性测试',
          passed: false,
          errorMessage: `组件值不一致: 原有项目=${originalComponentById.value}, 重构后项目=${refactoredComponentById.value}`,
          details: {
            originalComponentValue: originalComponentById.value,
            refactoredComponentValue: refactoredComponentById.value
          }
        };
      }
      
      // 移除组件
      originalEntity.removeComponent(originalComponent);
      refactoredEntity.removeComponent(refactoredComponent);
      
      // 检查组件是否移除成功
      const originalComponentCountAfterRemove = originalEntity.getComponentCount();
      const refactoredComponentCountAfterRemove = refactoredEntity.getComponentCount();
      
      if (originalComponentCountAfterRemove !== refactoredComponentCountAfterRemove) {
        return {
          name: '组件管理兼容性测试',
          passed: false,
          errorMessage: `移除组件后数量不一致: 原有项目=${originalComponentCountAfterRemove}, 重构后项目=${refactoredComponentCountAfterRemove}`,
          details: {
            originalComponentCountAfterRemove,
            refactoredComponentCountAfterRemove
          }
        };
      }
      
      return {
        name: '组件管理兼容性测试',
        passed: true,
        details: {
          originalComponentCount,
          refactoredComponentCount,
          originalComponentCountAfterRemove,
          refactoredComponentCountAfterRemove
        }
      };
    } catch (error) {
      return {
        name: '组件管理兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
