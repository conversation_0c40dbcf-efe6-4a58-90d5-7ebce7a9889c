/**
 * 地形生成工作线程管理器
 * 负责创建和管理地形生成工作线程
 */

import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { 
  TerrainWorkerMessageType, 
  TerrainWorkerMessageData, 
  TerrainGenerationWorkerParams,
  ThermalErosionWorkerParams,
  HydraulicErosionWorkerParams,
  TerrainFeatureCombinationWorkerParams,
  createTerrainGenerationWorker
} from './TerrainGenerationWorker';

/**
 * 地形生成工作线程管理器配置
 */
export interface TerrainGenerationWorkerManagerConfig {
  /** 最大工作线程数量 */
  maxWorkers?: number;
  /** 是否启用多线程 */
  enableMultithreading?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 地形生成工作线程管理器
 */
export class TerrainGenerationWorkerManager {
  /** 工作线程池 */
  private workers: Worker[] = [];
  /** 工作线程状态 */
  private workerStatus: boolean[] = [];
  /** 工作线程任务队列 */
  private taskQueue: {
    type: TerrainWorkerMessageType;
    data: any;
    resolve: (data: any) => void;
    reject: (error: Error) => void;
  }[] = [];
  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 配置 */
  private config: TerrainGenerationWorkerManagerConfig;
  /** 是否支持Web Worker */
  private supportsWorkers: boolean;

  /**
   * 创建地形生成工作线程管理器
   * @param config 配置
   */
  constructor(config: TerrainGenerationWorkerManagerConfig = {}) {
    this.config = {
      maxWorkers: config.maxWorkers || navigator.hardwareConcurrency || 4,
      enableMultithreading: config.enableMultithreading !== undefined ? config.enableMultithreading : true,
      debug: config.debug || false
    };

    this.eventEmitter = new EventEmitter();
    this.supportsWorkers = this.checkWorkerSupport();

    if (this.supportsWorkers && this.config.enableMultithreading) {
      this.initWorkers();
    } else {
      Debug.warn('TerrainGenerationWorkerManager', '多线程不可用，将使用主线程生成地形');
    }
  }

  /**
   * 检查Web Worker支持
   * @returns 是否支持Web Worker
   */
  private checkWorkerSupport(): boolean {
    return typeof Worker !== 'undefined';
  }

  /**
   * 初始化工作线程
   */
  private initWorkers(): void {
    try {
      // 创建工作线程
      for (let i = 0; i < this.config.maxWorkers!; i++) {
        const worker = createTerrainGenerationWorker();
        
        // 设置消息处理器
        worker.onmessage = (e: MessageEvent<TerrainWorkerMessageData>) => {
          this.handleWorkerMessage(i, e.data);
        };
        
        worker.onerror = (e: ErrorEvent) => {
          Debug.error('TerrainGenerationWorkerManager', `工作线程 ${i} 错误:`, e.message);
          this.workerStatus[i] = false;
          this.processNextTask();
        };
        
        this.workers.push(worker);
        this.workerStatus.push(false);
        
        // 初始化工作线程
        worker.postMessage({ 
          type: TerrainWorkerMessageType.INIT, 
          data: {} 
        });
      }
      
      if (this.config.debug) {
        Debug.log('TerrainGenerationWorkerManager', `已初始化 ${this.workers.length} 个工作线程`);
      }
    } catch (error) {
      Debug.error('TerrainGenerationWorkerManager', '初始化工作线程失败:', error);
      this.supportsWorkers = false;
    }
  }

  /**
   * 处理工作线程消息
   * @param workerId 工作线程ID
   * @param message 消息
   */
  private handleWorkerMessage(workerId: number, message: TerrainWorkerMessageData): void {
    const { type, data } = message;
    
    switch (type) {
      case TerrainWorkerMessageType.INIT:
        // 工作线程初始化完成
        this.workerStatus[workerId] = false;
        this.processNextTask();
        break;
        
      case TerrainWorkerMessageType.PROGRESS:
        // 进度更新
        this.eventEmitter.emit('progress', { workerId, progress: data.progress });
        break;
        
      case TerrainWorkerMessageType.COMPLETE:
        // 任务完成
        const task = this.taskQueue.shift();
        if (task) {
          task.resolve(data);
        }
        
        this.workerStatus[workerId] = false;
        this.processNextTask();
        break;
        
      case TerrainWorkerMessageType.ERROR:
        // 错误
        Debug.error('TerrainGenerationWorkerManager', `工作线程 ${workerId} 错误:`, data.message);
        
        const errorTask = this.taskQueue.shift();
        if (errorTask) {
          errorTask.reject(new Error(data.message));
        }
        
        this.workerStatus[workerId] = false;
        this.processNextTask();
        break;
    }
  }

  /**
   * 处理下一个任务
   */
  private processNextTask(): void {
    if (this.taskQueue.length === 0) {
      return;
    }
    
    // 查找空闲工作线程
    const workerId = this.findIdleWorker();
    if (workerId === -1) {
      return;
    }
    
    // 获取任务
    const task = this.taskQueue[0];
    
    // 标记工作线程为忙碌
    this.workerStatus[workerId] = true;
    
    // 发送任务到工作线程
    this.workers[workerId].postMessage({ 
      type: task.type, 
      data: task.data 
    }, this.getTransferables(task.data));
  }

  /**
   * 查找空闲工作线程
   * @returns 空闲工作线程ID，如果没有则返回-1
   */
  private findIdleWorker(): number {
    for (let i = 0; i < this.workerStatus.length; i++) {
      if (!this.workerStatus[i]) {
        return i;
      }
    }
    return -1;
  }

  /**
   * 获取可转移对象
   * @param data 数据
   * @returns 可转移对象数组
   */
  private getTransferables(data: any): Transferable[] {
    const transferables: Transferable[] = [];
    
    // 如果数据包含高度数据，添加到可转移对象
    if (data.heightData && data.heightData.buffer) {
      transferables.push(data.heightData.buffer);
    }
    
    return transferables;
  }

  /**
   * 添加任务到队列
   * @param type 任务类型
   * @param data 任务数据
   * @returns Promise
   */
  private addTask<T>(type: TerrainWorkerMessageType, data: any): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      // 添加任务到队列
      this.taskQueue.push({ type, data, resolve, reject });
      
      // 如果支持多线程，处理下一个任务
      if (this.supportsWorkers && this.config.enableMultithreading) {
        this.processNextTask();
      } else {
        // 否则，在主线程中执行任务
        this.executeTaskInMainThread(type, data, resolve, reject);
      }
    });
  }

  /**
   * 在主线程中执行任务
   * @param type 任务类型
   * @param data 任务数据
   * @param resolve 解析函数
   * @param reject 拒绝函数
   */
  private executeTaskInMainThread(
    type: TerrainWorkerMessageType,
    data: any,
    resolve: (data: any) => void,
    reject: (error: Error) => void
  ): void {
    // 这里应该实现在主线程中执行任务的逻辑
    // 实际实现时，应该导入相应的算法并在主线程中执行
    // 为简化示例，这里只是模拟任务执行
    setTimeout(() => {
      try {
        // 根据任务类型执行不同的操作
        let result: any;

        switch (type) {
          case TerrainWorkerMessageType.GENERATE:
            // 模拟地形生成
            result = { heightData: new Float32Array(data.resolution * data.resolution) };
            break;

          case TerrainWorkerMessageType.APPLY_EROSION:
            // 模拟侵蚀应用
            result = { heightData: new Float32Array(data.heightData.length) };
            break;

          case TerrainWorkerMessageType.GENERATE_FEATURE:
            // 模拟特征生成
            result = { heightData: new Float32Array(data.resolution * data.resolution) };
            break;

          case TerrainWorkerMessageType.GENERATE_FEATURE_COMBINATION:
            // 模拟特征组合生成
            result = { heightData: new Float32Array(data.resolution * data.resolution) };
            break;

          default:
            throw new Error(`不支持的任务类型: ${type}`);
        }

        resolve(result);
      } catch (error) {
        reject(error as Error);
      }
    }, 100);
  }

  /**
   * 生成地形
   * @param params 参数
   * @returns Promise
   */
  public generateTerrain(params: TerrainGenerationWorkerParams): Promise<{ heightData: Float32Array }> {
    return this.addTask(TerrainWorkerMessageType.GENERATE, params);
  }

  /**
   * 应用侵蚀
   * @param params 参数
   * @returns Promise
   */
  public applyErosion(params: ThermalErosionWorkerParams | HydraulicErosionWorkerParams): Promise<{ heightData: Float32Array }> {
    return this.addTask(TerrainWorkerMessageType.APPLY_EROSION, params);
  }

  /**
   * 生成特征
   * @param params 参数
   * @returns Promise
   */
  public generateFeature(params: any): Promise<{ heightData: Float32Array }> {
    return this.addTask(TerrainWorkerMessageType.GENERATE_FEATURE, params);
  }

  /**
   * 生成特征组合
   * @param params 参数
   * @returns Promise
   */
  public generateFeatureCombination(params: TerrainFeatureCombinationWorkerParams): Promise<{ heightData: Float32Array }> {
    return this.addTask(TerrainWorkerMessageType.GENERATE_FEATURE_COMBINATION, params);
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    // 终止所有工作线程
    for (const worker of this.workers) {
      worker.terminate();
    }
    
    // 清空数组
    this.workers = [];
    this.workerStatus = [];
    this.taskQueue = [];
    
    if (this.config.debug) {
      Debug.log('TerrainGenerationWorkerManager', '已销毁工作线程管理器');
    }
  }
}
