/**
 * 性能优化面板组件
 * 用于显示和控制性能优化功能
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Tabs, Button, Switch, Slider, Space, Typography, Progress, Alert, Collapse, Statistic, Tooltip, message, Empty } from 'antd';
import { useTranslation } from 'react-i18next';
// 移除 @ant-design/charts 导入，使用简单的图表替代
// import { Line } from '@ant-design/charts';
import {
  PauseOutlined,
  PlayCircleOutlined,
  WarningOutlined,
  DashboardOutlined,
  AreaChartOutlined,
  LineChartOutlined,
  SettingOutlined,
  RocketOutlined,
  ClockCircleOutlined} from '@ant-design/icons';
import { PerformanceOptimizationService, PerformanceOptimizationEventType } from '../../services/PerformanceOptimizationService';
import './PerformanceOptimizationPanel.less';

// 本地性能指标类型定义
enum PerformanceMetricType {
  FPS = 'fps',
  MEMORY_USAGE = 'memoryUsage',
  RENDER_TIME = 'renderTime',
  CPU_USAGE = 'cpuUsage',
  GPU_USAGE = 'gpuUsage'
}

const { Title, Text } = Typography;
const { Panel } = Collapse;

/**
 * 性能优化面板组件
 */
const PerformanceOptimizationPanel: React.FC = () => {
  const { t } = useTranslation();

  // 状态
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);
  const [performanceReport, setPerformanceReport] = useState<any>(null);
  const [warnings, setWarnings] = useState<any[]>([]);
  const [optimizationResults, setOptimizationResults] = useState<any>(null);

  // 配置
  const [config, setConfig] = useState<any>({
    enabled: true,
    enableAutoOptimize: false,
    autoOptimizeInterval: 60000,
    monitorSampleInterval: 1000,
    monitorHistoryLength: 60,
    enableWarnings: true,
    fpsWarningThreshold: 30,
    memoryWarningThreshold: 500,
    renderTimeWarningThreshold: 16,
    enableRenderingOptimization: true,
    enableMemoryOptimization: true,
    enableUIOptimization: true,
    enableResourceOptimization: true});

  // 引用
  const optimizationService = useRef<PerformanceOptimizationService>(PerformanceOptimizationService.getInstance());

  // 初始化
  useEffect(() => {
    // 初始化性能优化服务
    optimizationService.current.initialize(config);

    // 获取当前配置
    setConfig(optimizationService.current.getConfig());

    // 添加事件监听器
    optimizationService.current.on(PerformanceOptimizationEventType.PERFORMANCE_WARNING, handleWarning);
    optimizationService.current.on(PerformanceOptimizationEventType.OPTIMIZATION_START, handleOptimizationStart);
    optimizationService.current.on(PerformanceOptimizationEventType.OPTIMIZATION_COMPLETE, handleOptimizationComplete);
    optimizationService.current.on(PerformanceOptimizationEventType.OPTIMIZATION_FAILED, handleOptimizationFailed);
    optimizationService.current.on(PerformanceOptimizationEventType.PERFORMANCE_REPORT_UPDATED, handlePerformanceReportUpdated);

    // 清理函数
    return () => {
      // 移除事件监听器
      optimizationService.current.off(PerformanceOptimizationEventType.PERFORMANCE_WARNING, handleWarning);
      optimizationService.current.off(PerformanceOptimizationEventType.OPTIMIZATION_START, handleOptimizationStart);
      optimizationService.current.off(PerformanceOptimizationEventType.OPTIMIZATION_COMPLETE, handleOptimizationComplete);
      optimizationService.current.off(PerformanceOptimizationEventType.OPTIMIZATION_FAILED, handleOptimizationFailed);
      optimizationService.current.off(PerformanceOptimizationEventType.PERFORMANCE_REPORT_UPDATED, handlePerformanceReportUpdated);

      // 停止监控
      optimizationService.current.stopMonitoring();

      // 停止自动优化
      optimizationService.current.stopAutoOptimize();
    };
  }, []);

  // 处理性能警告
  const handleWarning = (warnings: any[]) => {
    setWarnings(warnings);
  };

  // 处理优化开始
  const handleOptimizationStart = () => {
    setIsOptimizing(true);
    setOptimizationProgress(0);
    message.info(t('debug.performance.optimizationStarted'));
  };

  // 处理优化完成
  const handleOptimizationComplete = (results: any) => {
    setIsOptimizing(false);
    setOptimizationProgress(100);
    setOptimizationResults(results);
    message.success(t('debug.performance.optimizationCompleted'));
  };

  // 处理优化失败
  const handleOptimizationFailed = (error: any) => {
    setIsOptimizing(false);
    setOptimizationProgress(0);
    message.error(`${t('debug.performance.optimizationFailed')}: ${error.message}`);
  };

  // 处理性能报告更新
  const handlePerformanceReportUpdated = (report: any) => {
    setPerformanceReport(report);
  };

  // 启动监控
  const startMonitoring = () => {
    optimizationService.current.startMonitoring();
    setIsMonitoring(true);
  };

  // 停止监控
  const stopMonitoring = () => {
    optimizationService.current.stopMonitoring();
    setIsMonitoring(false);
  };

  // 切换监控
  const toggleMonitoring = () => {
    if (isMonitoring) {
      stopMonitoring();
    } else {
      startMonitoring();
    }
  };

  // 切换自动优化
  const toggleAutoOptimize = (checked: boolean) => {
    const newConfig = { ...config, enableAutoOptimize: checked };
    setConfig(newConfig);
    optimizationService.current.updateConfig(newConfig);
  };

  // 执行优化
  const runOptimization = async () => {
    try {
      await optimizationService.current.optimizeScene();
    } catch (error) {
      console.error('优化失败:', error);
    }
  };

  // 更新配置
  const updateConfig = (key: string, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    optimizationService.current.updateConfig(newConfig);
  };

  // 渲染性能概览
  const renderPerformanceOverview = () => {
    const metrics = performanceReport?.metrics || {};

    // 获取指标状态颜色
    const getMetricStatusColor = (value: number, threshold: number, isHigherBetter: boolean = false) => {
      if (value === undefined) return '';

      if (isHigherBetter) {
        return value >= threshold ? '#52c41a' : '#f5222d';
      } else {
        return value <= threshold ? '#52c41a' : '#f5222d';
      }
    };

    return (
      <div className="performance-overview">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card>
              <Statistic
                title={t('debug.performance.fps')}
                value={metrics[PerformanceMetricType.FPS]?.value || 0}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(metrics[PerformanceMetricType.FPS]?.value || 0, config.fpsWarningThreshold, true) }}
                prefix={<DashboardOutlined />}
                suffix="FPS"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title={t('debug.performance.memory')}
                value={metrics[PerformanceMetricType.MEMORY_USAGE]?.value || 0}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(metrics[PerformanceMetricType.MEMORY_USAGE]?.value || 0, config.memoryWarningThreshold) }}
                prefix={<AreaChartOutlined />}
                suffix="MB"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title={t('debug.performance.renderTime')}
                value={metrics[PerformanceMetricType.RENDER_TIME]?.value || 0}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(metrics[PerformanceMetricType.RENDER_TIME]?.value || 0, config.renderTimeWarningThreshold) }}
                prefix={<ClockCircleOutlined />}
                suffix="ms"
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染性能图表
  const renderPerformanceCharts = () => {
    return (
      <div className="performance-charts">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card title={t('debug.performance.fpsChart')}>
              <Empty
                description={t('debug.performance.chartNotAvailable')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card title={t('debug.performance.memoryChart')}>
              <Empty
                description={t('debug.performance.chartNotAvailable')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card title={t('debug.performance.renderTimeChart')}>
              <Empty
                description={t('debug.performance.chartNotAvailable')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染优化结果
  const renderOptimizationResults = () => {
    if (!optimizationResults) {
      return (
        <Empty description={t('debug.performance.noOptimizationResults')} />
      );
    }

    const { rendering, memory, ui, resource } = optimizationResults;

    return (
      <div className="optimization-results">
        <Collapse defaultActiveKey={['rendering', 'memory', 'ui', 'resource']}>
          <Panel header={t('debug.performance.renderingOptimization')} key="rendering">
            {rendering && (
              <ul>
                <li>{t('debug.performance.lodGenerated')}: {rendering.lodGenerated}</li>
                <li>{t('debug.performance.materialOptimized')}: {rendering.materialOptimized}</li>
                <li>{t('debug.performance.batchesCreated')}: {rendering.batchesCreated}</li>
                <li>{t('debug.performance.instancesCreated')}: {rendering.instancesCreated}</li>
                <li>{t('debug.performance.cullingEnabled')}: {rendering.cullingEnabled ? t('common.yes') : t('common.no')}</li>
              </ul>
            )}
          </Panel>
          <Panel header={t('debug.performance.memoryOptimization')} key="memory">
            {memory && (
              <ul>
                <li>{t('debug.performance.texturesOptimized')}: {memory.texturesOptimized}</li>
                <li>{t('debug.performance.geometriesOptimized')}: {memory.geometriesOptimized}</li>
                <li>{t('debug.performance.resourcesReleased')}: {memory.resourcesReleased}</li>
              </ul>
            )}
          </Panel>
          <Panel header={t('debug.performance.uiOptimization')} key="ui">
            {ui && (
              <ul>
                <li>{t('debug.performance.componentsOptimized')}: {ui.componentsOptimized}</li>
                <li>{t('debug.performance.eventListenersOptimized')}: {ui.eventListenersOptimized}</li>
              </ul>
            )}
          </Panel>
          <Panel header={t('debug.performance.resourceOptimization')} key="resource">
            {resource && (
              <ul>
                <li>{t('debug.performance.resourcesOptimized')}: {resource.resourcesOptimized}</li>
                <li>{t('debug.performance.loadingStrategyUpdated')}: {resource.loadingStrategyUpdated ? t('common.yes') : t('common.no')}</li>
              </ul>
            )}
          </Panel>
        </Collapse>
      </div>
    );
  };

  // 渲染警告
  const renderWarnings = () => {
    if (warnings.length === 0) {
      return (
        <Alert message={t('debug.performance.noWarnings')} type="success" showIcon />
      );
    }

    return (
      <div className="performance-warnings">
        {warnings.map((warning, index) => (
          <Alert
            key={index}
            message={warning.message}
            type="warning"
            showIcon
            icon={<WarningOutlined />}
            description={`${t('debug.performance.current')}: ${warning.value.toFixed(1)}, ${t('debug.performance.threshold')}: ${warning.threshold}`}
          />
        ))}
      </div>
    );
  };

  // 渲染设置
  const renderSettings = () => {
    return (
      <div className="performance-settings">
        <Collapse defaultActiveKey={['general', 'thresholds', 'optimization']}>
          <Panel header={t('debug.performance.generalSettings')} key="general">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div className="setting-item">
                  <Text>{t('debug.performance.monitoringInterval')}</Text>
                  <Slider
                    min={100}
                    max={5000}
                    step={100}
                    value={config.monitorSampleInterval}
                    onChange={(value) => updateConfig('monitorSampleInterval', value)}
                  />
                  <Text type="secondary">{config.monitorSampleInterval} ms</Text>
                </div>
              </Col>
              <Col span={12}>
                <div className="setting-item">
                  <Text>{t('debug.performance.historyLength')}</Text>
                  <Slider
                    min={10}
                    max={120}
                    step={10}
                    value={config.monitorHistoryLength}
                    onChange={(value) => updateConfig('monitorHistoryLength', value)}
                  />
                  <Text type="secondary">{config.monitorHistoryLength} {t('debug.performance.samples')}</Text>
                </div>
              </Col>
            </Row>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div className="setting-item">
                  <Text>{t('debug.performance.enableWarnings')}</Text>
                  <Switch
                    checked={config.enableWarnings}
                    onChange={(checked) => updateConfig('enableWarnings', checked)}
                  />
                </div>
              </Col>
              <Col span={12}>
                <div className="setting-item">
                  <Text>{t('debug.performance.autoOptimizeInterval')}</Text>
                  <Slider
                    min={10000}
                    max={300000}
                    step={10000}
                    value={config.autoOptimizeInterval}
                    onChange={(value) => updateConfig('autoOptimizeInterval', value)}
                    disabled={!config.enableAutoOptimize}
                  />
                  <Text type="secondary">{config.autoOptimizeInterval / 1000} {t('debug.performance.seconds')}</Text>
                </div>
              </Col>
            </Row>
          </Panel>
          <Panel header={t('debug.performance.warningThresholds')} key="thresholds">
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <div className="setting-item">
                  <Text>{t('debug.performance.fpsWarningThreshold')}</Text>
                  <Slider
                    min={10}
                    max={60}
                    step={1}
                    value={config.fpsWarningThreshold}
                    onChange={(value) => updateConfig('fpsWarningThreshold', value)}
                  />
                  <Text type="secondary">{config.fpsWarningThreshold} FPS</Text>
                </div>
              </Col>
              <Col span={8}>
                <div className="setting-item">
                  <Text>{t('debug.performance.memoryWarningThreshold')}</Text>
                  <Slider
                    min={100}
                    max={2000}
                    step={100}
                    value={config.memoryWarningThreshold}
                    onChange={(value) => updateConfig('memoryWarningThreshold', value)}
                  />
                  <Text type="secondary">{config.memoryWarningThreshold} MB</Text>
                </div>
              </Col>
              <Col span={8}>
                <div className="setting-item">
                  <Text>{t('debug.performance.renderTimeWarningThreshold')}</Text>
                  <Slider
                    min={5}
                    max={50}
                    step={1}
                    value={config.renderTimeWarningThreshold}
                    onChange={(value) => updateConfig('renderTimeWarningThreshold', value)}
                  />
                  <Text type="secondary">{config.renderTimeWarningThreshold} ms</Text>
                </div>
              </Col>
            </Row>
          </Panel>
          <Panel header={t('debug.performance.optimizationSettings')} key="optimization">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div className="setting-item">
                  <Text>{t('debug.performance.enableRenderingOptimization')}</Text>
                  <Switch
                    checked={config.enableRenderingOptimization}
                    onChange={(checked) => updateConfig('enableRenderingOptimization', checked)}
                  />
                </div>
              </Col>
              <Col span={12}>
                <div className="setting-item">
                  <Text>{t('debug.performance.enableMemoryOptimization')}</Text>
                  <Switch
                    checked={config.enableMemoryOptimization}
                    onChange={(checked) => updateConfig('enableMemoryOptimization', checked)}
                  />
                </div>
              </Col>
            </Row>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div className="setting-item">
                  <Text>{t('debug.performance.enableUIOptimization')}</Text>
                  <Switch
                    checked={config.enableUIOptimization}
                    onChange={(checked) => updateConfig('enableUIOptimization', checked)}
                  />
                </div>
              </Col>
              <Col span={12}>
                <div className="setting-item">
                  <Text>{t('debug.performance.enableResourceOptimization')}</Text>
                  <Switch
                    checked={config.enableResourceOptimization}
                    onChange={(checked) => updateConfig('enableResourceOptimization', checked)}
                  />
                </div>
              </Col>
            </Row>
          </Panel>
        </Collapse>
      </div>
    );
  };

  return (
    <div className="performance-optimization-panel">
      <div className="panel-header">
        <Space>
          <Title level={4}>{t('debug.performance.title')}</Title>
          <Tooltip title={isMonitoring ? t('debug.performance.stopMonitoring') : t('debug.performance.startMonitoring')}>
            <Button
              type="primary"
              icon={isMonitoring ? <PauseOutlined /> : <PlayCircleOutlined />}
              onClick={toggleMonitoring}
            />
          </Tooltip>
          <Tooltip title={t('debug.performance.optimize')}>
            <Button
              type="primary"
              icon={<RocketOutlined />}
              onClick={runOptimization}
              loading={isOptimizing}
              disabled={isOptimizing}
            />
          </Tooltip>
          <div className="auto-optimize-switch">
            <Text>{t('debug.performance.autoOptimize')}</Text>
            <Switch
              checked={config.enableAutoOptimize}
              onChange={toggleAutoOptimize}
            />
          </div>
        </Space>
      </div>

      {isOptimizing && (
        <div className="optimization-progress">
          <Progress percent={optimizationProgress} status="active" />
        </div>
      )}

      {warnings.length > 0 && (
        <div className="warnings-container">
          {renderWarnings()}
        </div>
      )}

{(() => {
        const tabItems = [
          {
            key: 'overview',
            label: (
              <span>
                <DashboardOutlined />
                {t('debug.performance.overview')}
              </span>
            ),
            children: renderPerformanceOverview()
          },
          {
            key: 'charts',
            label: (
              <span>
                <LineChartOutlined />
                {t('debug.performance.charts')}
              </span>
            ),
            children: renderPerformanceCharts()
          },
          {
            key: 'results',
            label: (
              <span>
                <RocketOutlined />
                {t('debug.performance.optimizationResults')}
              </span>
            ),
            children: renderOptimizationResults()
          },
          {
            key: 'settings',
            label: (
              <span>
                <SettingOutlined />
                {t('debug.performance.settings')}
              </span>
            ),
            children: renderSettings()
          }
        ];

        return (
          <Tabs
            defaultActiveKey="overview"
            items={tabItems}
          />
        );
      })()}
    </div>
  );
};

export default PerformanceOptimizationPanel;
