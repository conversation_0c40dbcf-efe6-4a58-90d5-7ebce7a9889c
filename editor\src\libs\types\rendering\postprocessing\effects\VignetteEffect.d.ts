/**
 * 暗角效果
 * 模拟镜头边缘暗角
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 暗角效果选项
 */
export interface VignetteEffectOptions extends PostProcessingEffectOptions {
    /** 偏移 */
    offset?: number;
    /** 暗化程度 */
    darkness?: number;
    /** 是否使用椭圆形 */
    eskil?: boolean;
    /** 暗角颜色 */
    color?: THREE.Color;
}
/**
 * 暗角效果
 */
export declare class VignetteEffect extends PostProcessingEffect {
    /** 偏移 */
    private offset;
    /** 暗化程度 */
    private darkness;
    /** 是否使用椭圆形 */
    private eskil;
    /** 暗角颜色 */
    private color;
    /** 暗角通道 */
    private vignettePass;
    /**
     * 创建暗角效果
     * @param options 暗角效果选项
     */
    constructor(options?: VignetteEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 设置偏移
     * @param offset 偏移
     */
    setOffset(offset: number): void;
    /**
     * 获取偏移
     * @returns 偏移
     */
    getOffset(): number;
    /**
     * 设置暗化程度
     * @param darkness 暗化程度
     */
    setDarkness(darkness: number): void;
    /**
     * 获取暗化程度
     * @returns 暗化程度
     */
    getDarkness(): number;
    /**
     * 设置是否使用椭圆形
     * @param eskil 是否使用椭圆形
     */
    setEskil(eskil: boolean): void;
    /**
     * 获取是否使用椭圆形
     * @returns 是否使用椭圆形
     */
    isEskil(): boolean;
    /**
     * 设置暗角颜色
     * @param color 暗角颜色
     */
    setColor(color: THREE.Color): void;
    /**
     * 获取暗角颜色
     * @returns 暗角颜色
     */
    getColor(): THREE.Color;
}
