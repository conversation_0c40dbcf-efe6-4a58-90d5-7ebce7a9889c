/**
 * 用户服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom, timeout, catchError, throwError } from 'rxjs';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(@Inject('USER_SERVICE') private readonly userService: ClientProxy) {}

  /**
   * 获取所有用户
   */
  async findAll() {
    try {
      return await firstValueFrom(
        this.userService.send({ cmd: 'findAllUsers' }, {}).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error('获取所有用户失败', error);
      throw error;
    }
  }

  /**
   * 根据ID获取用户
   */
  async findOne(id: string) {
    try {
      return await firstValueFrom(
        this.userService.send({ cmd: 'findUserById' }, id).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`获取用户ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 更新用户
   */
  async update(id: string, updateUserDto: any) {
    try {
      return await firstValueFrom(
        this.userService.send({ cmd: 'updateUser' }, { id, ...updateUserDto }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`更新用户ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 删除用户
   */
  async remove(id: string) {
    try {
      return await firstValueFrom(
        this.userService.send({ cmd: 'removeUser' }, id).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`删除用户ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 创建用户头像
   */
  async createAvatar(userId: string, createUserAvatarDto: any) {
    try {
      return await firstValueFrom(
        this.userService.send({ cmd: 'createUserAvatar' }, { userId, ...createUserAvatarDto }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`创建用户ID ${userId} 头像失败`, error);
      throw error;
    }
  }

  /**
   * 创建用户设置
   */
  async createSetting(userId: string, createUserSettingDto: any) {
    try {
      return await firstValueFrom(
        this.userService.send({ cmd: 'createUserSetting' }, { userId, ...createUserSettingDto }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`创建用户ID ${userId} 设置失败`, error);
      throw error;
    }
  }

  /**
   * 获取用户设置
   */
  async getSetting(userId: string, key: string) {
    try {
      return await firstValueFrom(
        this.userService.send({ cmd: 'getUserSetting' }, { userId, key }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`获取用户ID ${userId} 设置 ${key} 失败`, error);
      throw error;
    }
  }

  /**
   * 删除用户设置
   */
  async removeSetting(userId: string, key: string) {
    try {
      return await firstValueFrom(
        this.userService.send({ cmd: 'removeUserSetting' }, { userId, key }).pipe(
          timeout(10000),
          catchError(error => {
            this.logger.error('用户服务调用失败', error);
            return throwError(() => error);
          })
        )
      );
    } catch (error) {
      this.logger.error(`删除用户ID ${userId} 设置 ${key} 失败`, error);
      throw error;
    }
  }
}
