/**
 * AI动画合成系统
 * 用于基于文本描述生成面部动画
 */
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationComponent } from '../components/FacialAnimationComponent';
import { AIAnimationSynthesisComponent } from '../components/AIAnimationSynthesisComponent';
import { EmotionBasedAnimationGenerator } from '../ai/EmotionBasedAnimationGenerator';
import { AdvancedEmotionBasedAnimationGenerator } from '../ai/AdvancedEmotionBasedAnimationGenerator';
import { AnimationGenerationRequest, AnimationGenerationResult } from '../ai/AnimationGenerationTypes';
import { EmotionModelFactory, EmotionModelType, EmotionModelVariant } from '../ai/EmotionModelFactory';

/**
 * AI动画合成系统配置
 */
export interface AIAnimationSynthesisSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用本地模型 */
  useLocalModel?: boolean;
  /** 模型路径 */
  modelPath?: string;
  /** 是否使用Web Worker */
  useWorker?: boolean;
  /** 是否使用GPU */
  useGPU?: boolean;
  /** 模型类型 */
  modelType?: EmotionModelType;
  /** 模型变体 */
  modelVariant?: EmotionModelVariant;
  /** 是否使用高级生成器 */
  useAdvancedGenerator?: boolean;
  /** 是否使用中文模型 */
  useChineseModel?: boolean;
  /** 是否使用多语言模型 */
  useMultilingualModel?: boolean;
}

/**
 * AI动画合成系统
 */
export class AIAnimationSynthesisSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'AIAnimationSynthesisSystem';

  /** 配置 */
  private config: AIAnimationSynthesisSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** AI动画合成组件映射 */
  private components: Map<Entity, AIAnimationSynthesisComponent> = new Map();

  /** 情感动画生成器 */
  private emotionGenerator: EmotionBasedAnimationGenerator | AdvancedEmotionBasedAnimationGenerator | null = null;

  /** 情感模型工厂 */
  private emotionModelFactory: EmotionModelFactory | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否正在初始化 */
  private initializing: boolean = false;

  /** Web Worker */
  private worker: Worker | null = null;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AIAnimationSynthesisSystemConfig = {}) {
    super(400); // 设置优先级

    this.config = {
      debug: false,
      useLocalModel: false,
      modelPath: '',
      useWorker: false,
      useGPU: false,
      modelType: EmotionModelType.BERT,
      modelVariant: EmotionModelVariant.BASE,
      useAdvancedGenerator: false,
      useChineseModel: false,
      useMultilingualModel: false,
      ...config
    };

    if (this.config.debug) {
      console.log('AI动画合成系统初始化');
    }

    // 创建情感模型工厂
    this.emotionModelFactory = new EmotionModelFactory({
      debug: this.config.debug,
      useGPU: this.config.useGPU,
      useRemoteAPI: !this.config.useLocalModel,
      modelPathPrefix: this.config.modelPath || 'models/'
    });

    // 初始化情感动画生成器
    this.initEmotionGenerator();

    // 初始化Web Worker
    if (this.config.useWorker) {
      this.initWorker();
    }
  }

  /**
   * 初始化情感动画生成器
   */
  private async initEmotionGenerator(): Promise<void> {
    if (this.initializing) return;
    this.initializing = true;

    try {
      // 根据配置选择合适的模型和生成器
      // 选择模型（暂时注释掉，因为生成器配置中不需要aiModel参数）
      if (this.config.useChineseModel) {
        if (this.config.debug) {
          console.log('使用中文BERT情感模型');
        }
      } else if (this.config.useMultilingualModel) {
        if (this.config.debug) {
          console.log('使用多语言BERT情感模型');
        }
      } else {
        if (this.config.debug) {
          console.log(`使用${this.config.modelType}情感模型`);
        }
      }

      // 选择生成器
      if (this.config.useAdvancedGenerator) {
        // 使用高级生成器
        this.emotionGenerator = new AdvancedEmotionBasedAnimationGenerator({
          debug: this.config.debug,
          useLocalModel: this.config.useLocalModel,
          modelPath: this.config.modelPath,
          useGPU: this.config.useGPU
        });

        if (this.config.debug) {
          console.log('使用高级情感动画生成器');
        }
      } else {
        // 使用标准生成器
        this.emotionGenerator = new EmotionBasedAnimationGenerator({
          debug: this.config.debug,
          useLocalModel: this.config.useLocalModel,
          modelPath: this.config.modelPath,
          useGPU: this.config.useGPU
        });

        if (this.config.debug) {
          console.log('使用标准情感动画生成器');
        }
      }

      // 初始化生成器
      await this.emotionGenerator.initialize();

      this.initialized = true;

      if (this.config.debug) {
        console.log('情感动画生成器初始化成功');
      }
    } catch (error) {
      console.error('初始化情感动画生成器失败:', error);
    } finally {
      this.initializing = false;
    }
  }

  /**
   * 初始化Web Worker
   */
  private initWorker(): void {
    try {
      // 创建Web Worker
      // 这里是Web Worker的占位代码，实际实现需要根据具体需求
      // TODO: 实现Web Worker

      if (this.config.debug) {
        console.log('Web Worker初始化成功');
      }
    } catch (error) {
      console.error('初始化Web Worker失败:', error);
    }
  }

  /**
   * 创建AI动画合成组件
   * @param entity 实体
   * @returns AI动画合成组件
   */
  public createAIAnimationSynthesis(entity: Entity): AIAnimationSynthesisComponent {
    // 检查实体是否已有AI动画合成组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建AI动画合成组件
    const component = new AIAnimationSynthesisComponent();

    // 添加组件到实体
    entity.addComponent(component);

    // 添加组件到映射
    this.components.set(entity, component);

    // 设置回调
    component.setGenerationCallback(this.handleGenerationRequest.bind(this));

    if (this.config.debug) {
      console.log('创建AI动画合成组件', entity);
    }

    return component;
  }

  /**
   * 获取AI动画合成组件
   * @param entity 实体
   * @returns AI动画合成组件
   */
  public getAIAnimationSynthesis(entity: Entity): AIAnimationSynthesisComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 移除AI动画合成组件
   * @param entity 实体
   */
  public removeAIAnimationSynthesis(entity: Entity): void {
    const component = this.components.get(entity);
    if (component) {
      entity.removeComponent(component);
      this.components.delete(entity);

      if (this.config.debug) {
        console.log('移除AI动画合成组件', entity);
      }
    }
  }

  /**
   * 生成面部动画
   * @param entity 实体
   * @param prompt 提示文本
   * @param duration 持续时间（秒）
   * @param options 其他选项
   * @returns 请求ID
   */
  public generateFacialAnimation(
    entity: Entity,
    prompt: string,
    duration: number = 5.0,
    options: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>> = {}
  ): string | null {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return null;

    const request: Omit<AnimationGenerationRequest, 'id'> = {
      prompt,
      duration,
      type: 'facial',
      loop: options.loop ?? false,
      referenceClip: options.referenceClip,
      style: options.style,
      intensity: options.intensity,
      seed: options.seed,
      userData: options.userData
    };

    return component.requestAnimation(request);
  }

  /**
   * 处理生成请求
   * @param entity 实体
   * @param request 请求
   */
  private async handleGenerationRequest(entity: Entity, request: AnimationGenerationRequest): Promise<void> {
    if (!this.initialized) {
      await this.initEmotionGenerator();
    }

    if (!this.emotionGenerator) {
      this.handleGenerationError(entity, request, '情感动画生成器未初始化');
      return;
    }

    try {
      // 生成动画
      const result = await this.emotionGenerator.generateFacialAnimation(request);

      // 处理结果
      this.handleGenerationResult(entity, result);
    } catch (error) {
      this.handleGenerationError(entity, request, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 处理生成结果
   * @param entity 实体
   * @param result 结果
   */
  private handleGenerationResult(entity: Entity, result: AnimationGenerationResult): void {
    // 获取组件
    const component = this.components.get(entity);
    if (!component) return;

    // 更新组件
    component.setGenerationResult(result);

    // 如果生成成功，应用动画
    if (result.success && result.clip) {
      // 获取面部动画组件
      const facialComponent = entity.getComponent(FacialAnimationComponent.TYPE) as FacialAnimationComponent;
      if (facialComponent) {
        // 应用动画
        // TODO: 实现动画应用
      }
    }

    // 发出事件
    this.eventEmitter.emit('generationComplete', { entity, result });
  }

  /**
   * 处理生成错误
   * @param entity 实体
   * @param request 请求
   * @param error 错误信息
   */
  private handleGenerationError(entity: Entity, request: AnimationGenerationRequest, error: string): void {
    // 获取组件
    const component = this.components.get(entity);
    if (!component) return;

    // 创建错误结果
    const result: AnimationGenerationResult = {
      id: request.id,
      success: false,
      error,
      userData: request.userData
    };

    // 更新组件
    component.setGenerationResult(result);

    // 发出事件
    this.eventEmitter.emit('generationError', { entity, request, error });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有AI动画合成组件
    for (const [_, component] of this.components.entries()) {
      component.update(deltaTime);
    }
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 关闭Web Worker
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    // 清除组件映射
    this.components.clear();

    if (this.config.debug) {
      console.log('AI动画合成系统销毁');
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }
}
