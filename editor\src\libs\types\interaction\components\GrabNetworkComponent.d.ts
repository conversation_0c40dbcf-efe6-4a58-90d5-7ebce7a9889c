/**
 * 抓取网络组件
 * 用于处理抓取系统的网络同步
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventCallback } from '../../utils/EventEmitter';
import { Hand } from './GrabbableComponent';
import { NetworkManager } from '../../network/NetworkManager';
import { Vector3, Quaternion } from 'three';
/**
 * 抓取网络事件类型
 */
export declare enum GrabNetworkEventType {
    /** 抓取请求 */
    GRAB_REQUEST = "grabRequest",
    /** 抓取确认 */
    GRAB_CONFIRM = "grabConfirm",
    /** 抓取拒绝 */
    GRAB_REJECT = "grabReject",
    /** 释放请求 */
    RELEASE_REQUEST = "releaseRequest",
    /** 释放确认 */
    RELEASE_CONFIRM = "releaseConfirm",
    /** 状态同步 */
    STATE_SYNC = "stateSync",
    /** 抛掷事件 */
    THROW_EVENT = "throwEvent",
    /** 旋转事件 */
    ROTATE_EVENT = "rotateEvent",
    /** 权限请求 */
    AUTHORITY_REQUEST = "authorityRequest",
    /** 权限确认 */
    AUTHORITY_CONFIRM = "authorityConfirm",
    /** 权限拒绝 */
    AUTHORITY_REJECT = "authorityReject"
}
/**
 * 抓取网络事件数据
 */
export interface GrabNetworkEventData {
    /** 事件类型 */
    type: GrabNetworkEventType;
    /** 抓取者实体ID */
    grabberEntityId: string;
    /** 被抓取实体ID */
    grabbedEntityId: string;
    /** 抓取手 */
    hand: Hand;
    /** 时间戳 */
    timestamp: number;
    /** 用户ID */
    userId: string;
    /** 会话ID */
    sessionId: string;
    /** 状态数据 */
    state?: any;
    /** 速度向量（用于抛掷） */
    velocity?: {
        x: number;
        y: number;
        z: number;
    };
    /** 角速度向量（用于抛掷） */
    angularVelocity?: {
        x: number;
        y: number;
        z: number;
    };
    /** 旋转数据 */
    rotation?: {
        x: number;
        y: number;
        z: number;
        w: number;
    };
}
/**
 * 抓取网络组件配置
 */
export interface GrabNetworkComponentConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 同步间隔（毫秒） */
    syncInterval?: number;
    /** 网络延迟（毫秒） - 仅用于模拟 */
    networkLatency?: number;
    /** 是否使用压缩 */
    useCompression?: boolean;
    /** 是否使用预测 */
    usePrediction?: boolean;
    /** 是否使用权限控制 */
    useAuthorityControl?: boolean;
    /** 是否使用自适应同步 */
    useAdaptiveSync?: boolean;
}
/**
 * 抓取网络组件
 */
export declare class GrabNetworkComponent extends Component {
    /** 组件类型 */
    static readonly TYPE: string;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否启用 */
    private _enabled;
    /** 同步间隔（毫秒） */
    private _syncInterval;
    /** 网络延迟（毫秒） - 仅用于模拟 */
    private _networkLatency;
    /** 是否使用压缩 */
    private _useCompression;
    /** 是否使用预测 - 预留功能 */
    private _usePrediction;
    /** 是否使用权限控制 */
    private _useAuthorityControl;
    /** 是否使用自适应同步 */
    private _useAdaptiveSync;
    /** 是否已连接 */
    private _isConnected;
    /** 用户ID */
    private _userId;
    /** 会话ID */
    private _sessionId;
    /** 最后同步时间 */
    private _lastSyncTime;
    /** 当前网络质量 */
    private _networkQuality;
    /** 待处理事件队列 */
    private _pendingEvents;
    /** 网络管理器引用 */
    private _networkManager?;
    /** 事件对象池 */
    private _eventPool;
    /** 当前拥有权限的实体映射 */
    private _entityAuthorities;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: GrabNetworkComponentConfig);
    /**
     * 设置网络管理器
     * @param networkManager 网络管理器
     */
    setNetworkManager(networkManager: NetworkManager): void;
    /**
     * 更新同步间隔
     * 根据网络质量调整同步频率
     */
    private updateSyncInterval;
    /**
     * 设置用户信息
     * @param userId 用户ID
     * @param sessionId 会话ID
     */
    setUserInfo(userId: string, sessionId: string): void;
    /**
     * 发送抓取请求
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @returns 是否发送成功
     */
    sendGrabRequest(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean;
    /**
     * 发送抓取确认
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @returns 是否发送成功
     */
    sendGrabConfirm(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean;
    /**
     * 发送抓取拒绝
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @returns 是否发送成功
     */
    sendGrabReject(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean;
    /**
     * 发送释放请求
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @returns 是否发送成功
     */
    sendReleaseRequest(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean;
    /**
     * 发送释放确认
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @returns 是否发送成功
     */
    sendReleaseConfirm(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean;
    /**
     * 发送状态同步
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @param state 状态数据
     * @returns 是否发送成功
     */
    sendStateSync(grabberEntityId: string, grabbedEntityId: string, hand: Hand, state: any): boolean;
    /**
     * 发送抛掷事件
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @param velocity 速度向量
     * @param angularVelocity 角速度向量
     * @returns 是否发送成功
     */
    sendThrowEvent(grabberEntityId: string, grabbedEntityId: string, hand: Hand, velocity: Vector3, angularVelocity: Vector3): boolean;
    /**
     * 发送旋转事件
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @param hand 抓取手
     * @param rotation 旋转四元数
     * @returns 是否发送成功
     */
    sendRotateEvent(grabberEntityId: string, grabbedEntityId: string, hand: Hand, rotation: Quaternion): boolean;
    /**
     * 发送权限请求
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @returns 是否发送成功
     */
    sendAuthorityRequest(grabberEntityId: string, grabbedEntityId: string): boolean;
    /**
     * 发送权限确认
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @returns 是否发送成功
     */
    sendAuthorityConfirm(grabberEntityId: string, grabbedEntityId: string): boolean;
    /**
     * 发送权限拒绝
     * @param grabberEntityId 抓取者实体ID
     * @param grabbedEntityId 被抓取实体ID
     * @returns 是否发送成功
     */
    sendAuthorityReject(grabberEntityId: string, grabbedEntityId: string): boolean;
    /**
     * 发送网络事件
     * @param eventData 事件数据
     * @private
     */
    private sendNetworkEvent;
    /**
     * 压缩事件数据
     * @param eventData 事件数据
     * @returns 压缩后的数据
     * @private
     */
    private compressEventData;
    /**
     * 接收网络事件
     * @param eventData 事件数据
     */
    receiveNetworkEvent(eventData: GrabNetworkEventData): void;
    /**
     * 处理待处理事件
     * @private
     */
    private processPendingEvents;
    /**
     * 处理事件
     * @param eventData 事件数据
     * @private
     */
    private processEvent;
    /**
     * 处理抓取请求
     * @param eventData 事件数据
     * @private
     */
    private handleGrabRequest;
    /**
     * 处理抓取确认
     * @param eventData 事件数据
     * @private
     */
    private handleGrabConfirm;
    /**
     * 处理抓取拒绝
     * @param eventData 事件数据
     * @private
     */
    private handleGrabReject;
    /**
     * 处理释放请求
     * @param eventData 事件数据
     * @private
     */
    private handleReleaseRequest;
    /**
     * 处理释放确认
     * @param eventData 事件数据
     * @private
     */
    private handleReleaseConfirm;
    /**
     * 处理状态同步
     * @param eventData 事件数据
     * @private
     */
    private handleStateSync;
    /**
     * 处理抛掷事件
     * @param eventData 事件数据
     * @private
     */
    private handleThrowEvent;
    /**
     * 处理旋转事件
     * @param eventData 事件数据
     * @private
     */
    private handleRotateEvent;
    /**
     * 处理权限请求
     * @param eventData 事件数据
     * @private
     */
    private handleAuthorityRequest;
    /**
     * 处理权限确认
     * @param eventData 事件数据
     * @private
     */
    private handleAuthorityConfirm;
    /**
     * 处理权限拒绝
     * @param eventData 事件数据
     * @private
     */
    private handleAuthorityReject;
    /**
     * 检查实体权限
     * @param entityId 实体ID
     * @param userId 用户ID
     * @returns 是否有权限
     */
    hasAuthority(entityId: string, userId: string): boolean;
    /**
     * 设置实体权限
     * @param entityId 实体ID
     * @param userId 用户ID
     */
    setAuthority(entityId: string, userId: string): void;
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    on(event: string, listener: EventCallback): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    off(event: string, listener?: EventCallback): this;
    /**
     * 销毁组件
     */
    destroy(): void;
}
