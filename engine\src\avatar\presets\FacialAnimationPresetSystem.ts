/**
 * 面部动画预设系统
 * 管理面部动画预设和模板
 */
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationComponent } from '../components/FacialAnimationComponent';
import { FacialExpressionType } from '../components/FacialAnimationComponent';

/**
 * 面部动画预设类型
 */
export enum FacialAnimationPresetType {
  /** 标准表情 */
  STANDARD = 'standard',
  /** 文化特定表情 */
  CULTURAL = 'cultural',
  /** 情感组合 */
  EMOTION_COMBO = 'emotion_combo',
  /** 动画序列 */
  ANIMATION_SEQUENCE = 'animation_sequence',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 面部动画预设配置
 */
export interface FacialAnimationPresetConfig {
  /** 预设名称 */
  name: string;
  /** 预设类型 */
  type: FacialAnimationPresetType;
  /** 表情类型 */
  expression?: FacialExpressionType;
  /** 表情权重 */
  weight?: number;
  /** 表情组合 */
  expressionCombos?: { expression: FacialExpressionType, weight: number }[];
  /** 动画序列 */
  animationSequence?: { expression: FacialExpressionType, weight: number, duration: number }[];
  /** 文化标识 */
  culture?: string;
  /** 标签 */
  tags?: string[];
  /** 描述 */
  description?: string;
  /** 作者 */
  author?: string;
  /** 创建日期 */
  createdAt?: Date;
  /** 更新日期 */
  updatedAt?: Date;
  /** 自定义数据 */
  customData?: any;
}

/**
 * 面部动画预设系统配置
 */
export interface FacialAnimationPresetSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否自动加载预设 */
  autoLoadPresets?: boolean;
  /** 预设路径 */
  presetsPath?: string;
  /** 默认文化 */
  defaultCulture?: string;
}

/**
 * 面部动画预设系统
 */
export class FacialAnimationPresetSystem extends System {
  /** 系统类型 */
  static readonly type = 'FacialAnimationPreset';

  /** 预设 */
  private presets: Map<string, FacialAnimationPresetConfig> = new Map();

  /** 配置 */
  private config: FacialAnimationPresetSystemConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: FacialAnimationPresetSystemConfig = {
    debug: false,
    autoLoadPresets: true,
    presetsPath: 'presets/facial',
    defaultCulture: 'global'
  };

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: Partial<FacialAnimationPresetSystemConfig>) {
    super(200); // 设置优先级
    this.config = { ...FacialAnimationPresetSystem.DEFAULT_CONFIG, ...config };
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 自动加载预设
    if (this.config.autoLoadPresets) {
      this.loadDefaultPresets();
    }
  }

  /**
   * 加载默认预设
   */
  private loadDefaultPresets(): void {
    // 标准表情预设
    this.registerStandardPresets();

    // 文化特定表情预设
    this.registerCulturalPresets();

    // 情感组合预设
    this.registerEmotionComboPresets();

    // 动画序列预设
    this.registerAnimationSequencePresets();

    if (this.config.debug) {
      console.log(`已加载 ${this.presets.size} 个预设`);
    }
  }

  /**
   * 注册标准表情预设
   */
  private registerStandardPresets(): void {
    // 注册基本表情
    this.registerPreset({
      name: 'neutral',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.NEUTRAL,
      weight: 1.0,
      description: '中性表情',
      tags: ['basic', 'neutral']
    });

    this.registerPreset({
      name: 'happy',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.HAPPY,
      weight: 1.0,
      description: '开心表情',
      tags: ['basic', 'positive']
    });

    this.registerPreset({
      name: 'sad',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.SAD,
      weight: 1.0,
      description: '悲伤表情',
      tags: ['basic', 'negative']
    });

    this.registerPreset({
      name: 'angry',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.ANGRY,
      weight: 1.0,
      description: '愤怒表情',
      tags: ['basic', 'negative']
    });

    this.registerPreset({
      name: 'surprised',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.SURPRISED,
      weight: 1.0,
      description: '惊讶表情',
      tags: ['basic', 'neutral']
    });

    this.registerPreset({
      name: 'fearful',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.FEARFUL,
      weight: 1.0,
      description: '恐惧表情',
      tags: ['basic', 'negative']
    });

    this.registerPreset({
      name: 'disgusted',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.DISGUSTED,
      weight: 1.0,
      description: '厌恶表情',
      tags: ['basic', 'negative']
    });

    this.registerPreset({
      name: 'contempt',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.CONTEMPT,
      weight: 1.0,
      description: '蔑视表情',
      tags: ['basic', 'negative']
    });

    // 注册强度变化的表情
    this.registerPreset({
      name: 'slight_smile',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.HAPPY,
      weight: 0.3,
      description: '微笑',
      tags: ['subtle', 'positive']
    });

    this.registerPreset({
      name: 'big_smile',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.HAPPY,
      weight: 1.0,
      description: '大笑',
      tags: ['intense', 'positive']
    });

    this.registerPreset({
      name: 'slight_sadness',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.SAD,
      weight: 0.3,
      description: '轻微悲伤',
      tags: ['subtle', 'negative']
    });

    this.registerPreset({
      name: 'deep_sadness',
      type: FacialAnimationPresetType.STANDARD,
      expression: FacialExpressionType.SAD,
      weight: 1.0,
      description: '深度悲伤',
      tags: ['intense', 'negative']
    });
  }

  /**
   * 注册文化特定表情预设
   */
  private registerCulturalPresets(): void {
    // 中国文化特定表情
    this.registerPreset({
      name: 'chinese_smile',
      type: FacialAnimationPresetType.CULTURAL,
      expression: FacialExpressionType.HAPPY,
      weight: 0.4,
      culture: 'chinese',
      description: '中国式微笑（含蓄）',
      tags: ['cultural', 'chinese', 'positive']
    });

    this.registerPreset({
      name: 'chinese_respect',
      type: FacialAnimationPresetType.CULTURAL,
      expressionCombos: [
        { expression: FacialExpressionType.NEUTRAL, weight: 0.7 },
        { expression: FacialExpressionType.HAPPY, weight: 0.3 }
      ],
      culture: 'chinese',
      description: '中国式尊敬表情',
      tags: ['cultural', 'chinese', 'respect']
    });

    // 日本文化特定表情
    this.registerPreset({
      name: 'japanese_bow',
      type: FacialAnimationPresetType.CULTURAL,
      expressionCombos: [
        { expression: FacialExpressionType.NEUTRAL, weight: 0.8 },
        { expression: FacialExpressionType.HAPPY, weight: 0.2 }
      ],
      culture: 'japanese',
      description: '日本式鞠躬表情',
      tags: ['cultural', 'japanese', 'respect']
    });

    // 美国文化特定表情
    this.registerPreset({
      name: 'american_smile',
      type: FacialAnimationPresetType.CULTURAL,
      expression: FacialExpressionType.HAPPY,
      weight: 0.8,
      culture: 'american',
      description: '美国式微笑（外向）',
      tags: ['cultural', 'american', 'positive']
    });
  }

  /**
   * 注册情感组合预设
   */
  private registerEmotionComboPresets(): void {
    // 复杂情感组合
    this.registerPreset({
      name: 'bittersweet',
      type: FacialAnimationPresetType.EMOTION_COMBO,
      expressionCombos: [
        { expression: FacialExpressionType.HAPPY, weight: 0.5 },
        { expression: FacialExpressionType.SAD, weight: 0.5 }
      ],
      description: '苦乐参半',
      tags: ['complex', 'mixed']
    });

    this.registerPreset({
      name: 'nervous_smile',
      type: FacialAnimationPresetType.EMOTION_COMBO,
      expressionCombos: [
        { expression: FacialExpressionType.HAPPY, weight: 0.6 },
        { expression: FacialExpressionType.FEARFUL, weight: 0.4 }
      ],
      description: '紧张的微笑',
      tags: ['complex', 'mixed']
    });

    this.registerPreset({
      name: 'angry_surprise',
      type: FacialAnimationPresetType.EMOTION_COMBO,
      expressionCombos: [
        { expression: FacialExpressionType.ANGRY, weight: 0.6 },
        { expression: FacialExpressionType.SURPRISED, weight: 0.4 }
      ],
      description: '愤怒的惊讶',
      tags: ['complex', 'negative']
    });

    this.registerPreset({
      name: 'happy_surprise',
      type: FacialAnimationPresetType.EMOTION_COMBO,
      expressionCombos: [
        { expression: FacialExpressionType.HAPPY, weight: 0.6 },
        { expression: FacialExpressionType.SURPRISED, weight: 0.4 }
      ],
      description: '惊喜',
      tags: ['complex', 'positive']
    });
  }

  /**
   * 注册动画序列预设
   */
  private registerAnimationSequencePresets(): void {
    // 动画序列
    this.registerPreset({
      name: 'laugh_sequence',
      type: FacialAnimationPresetType.ANIMATION_SEQUENCE,
      animationSequence: [
        { expression: FacialExpressionType.HAPPY, weight: 0.5, duration: 0.3 },
        { expression: FacialExpressionType.HAPPY, weight: 0.8, duration: 0.2 },
        { expression: FacialExpressionType.HAPPY, weight: 1.0, duration: 0.5 },
        { expression: FacialExpressionType.HAPPY, weight: 0.8, duration: 0.3 },
        { expression: FacialExpressionType.HAPPY, weight: 0.5, duration: 0.2 },
        { expression: FacialExpressionType.NEUTRAL, weight: 0.3, duration: 0.2 }
      ],
      description: '笑声序列',
      tags: ['sequence', 'positive']
    });

    this.registerPreset({
      name: 'surprise_to_happy',
      type: FacialAnimationPresetType.ANIMATION_SEQUENCE,
      animationSequence: [
        { expression: FacialExpressionType.SURPRISED, weight: 1.0, duration: 0.5 },
        { expression: FacialExpressionType.SURPRISED, weight: 0.7, duration: 0.3 },
        { expression: FacialExpressionType.HAPPY, weight: 0.3, duration: 0.3 },
        { expression: FacialExpressionType.HAPPY, weight: 0.7, duration: 0.4 },
        { expression: FacialExpressionType.HAPPY, weight: 1.0, duration: 0.5 }
      ],
      description: '从惊讶到开心',
      tags: ['sequence', 'transition', 'positive']
    });

    this.registerPreset({
      name: 'sad_to_neutral',
      type: FacialAnimationPresetType.ANIMATION_SEQUENCE,
      animationSequence: [
        { expression: FacialExpressionType.SAD, weight: 1.0, duration: 0.5 },
        { expression: FacialExpressionType.SAD, weight: 0.7, duration: 0.4 },
        { expression: FacialExpressionType.SAD, weight: 0.4, duration: 0.3 },
        { expression: FacialExpressionType.NEUTRAL, weight: 0.3, duration: 0.3 },
        { expression: FacialExpressionType.NEUTRAL, weight: 0.7, duration: 0.4 },
        { expression: FacialExpressionType.NEUTRAL, weight: 1.0, duration: 0.5 }
      ],
      description: '从悲伤到中性',
      tags: ['sequence', 'transition', 'recovery']
    });

    this.registerPreset({
      name: 'angry_outburst',
      type: FacialAnimationPresetType.ANIMATION_SEQUENCE,
      animationSequence: [
        { expression: FacialExpressionType.NEUTRAL, weight: 0.5, duration: 0.2 },
        { expression: FacialExpressionType.ANGRY, weight: 0.3, duration: 0.1 },
        { expression: FacialExpressionType.ANGRY, weight: 0.7, duration: 0.2 },
        { expression: FacialExpressionType.ANGRY, weight: 1.0, duration: 0.5 },
        { expression: FacialExpressionType.ANGRY, weight: 0.8, duration: 0.3 },
        { expression: FacialExpressionType.ANGRY, weight: 0.5, duration: 0.4 },
        { expression: FacialExpressionType.NEUTRAL, weight: 0.3, duration: 0.3 }
      ],
      description: '愤怒爆发',
      tags: ['sequence', 'negative', 'intense']
    });
  }

  /**
   * 注册预设
   * @param preset 预设配置
   * @returns 是否成功注册
   */
  public registerPreset(preset: FacialAnimationPresetConfig): boolean {
    // 检查预设名称是否已存在
    if (this.presets.has(preset.name)) {
      if (this.config.debug) {
        console.warn(`预设名称已存在: ${preset.name}`);
      }
      return false;
    }

    // 添加时间戳
    if (!preset.createdAt) {
      preset.createdAt = new Date();
    }
    preset.updatedAt = new Date();

    // 注册预设
    this.presets.set(preset.name, preset);

    // 发送事件
    this.eventEmitter.emit('preset_registered', { preset });

    if (this.config.debug) {
      console.log(`注册预设: ${preset.name}`);
    }

    return true;
  }

  /**
   * 更新预设
   * @param name 预设名称
   * @param preset 预设配置
   * @returns 是否成功更新
   */
  public updatePreset(name: string, preset: Partial<FacialAnimationPresetConfig>): boolean {
    // 检查预设是否存在
    if (!this.presets.has(name)) {
      if (this.config.debug) {
        console.warn(`预设不存在: ${name}`);
      }
      return false;
    }

    // 获取原始预设
    const originalPreset = this.presets.get(name)!;

    // 更新预设
    const updatedPreset = {
      ...originalPreset,
      ...preset,
      updatedAt: new Date()
    };

    // 保存预设
    this.presets.set(name, updatedPreset);

    // 发送事件
    this.eventEmitter.emit('preset_updated', { name, preset: updatedPreset });

    if (this.config.debug) {
      console.log(`更新预设: ${name}`);
    }

    return true;
  }

  /**
   * 删除预设
   * @param name 预设名称
   * @returns 是否成功删除
   */
  public deletePreset(name: string): boolean {
    // 检查预设是否存在
    if (!this.presets.has(name)) {
      if (this.config.debug) {
        console.warn(`预设不存在: ${name}`);
      }
      return false;
    }

    // 获取预设
    const preset = this.presets.get(name)!;

    // 删除预设
    this.presets.delete(name);

    // 发送事件
    this.eventEmitter.emit('preset_deleted', { name, preset });

    if (this.config.debug) {
      console.log(`删除预设: ${name}`);
    }

    return true;
  }

  /**
   * 获取预设
   * @param name 预设名称
   * @returns 预设配置
   */
  public getPreset(name: string): FacialAnimationPresetConfig | null {
    return this.presets.get(name) || null;
  }

  /**
   * 获取所有预设
   * @returns 预设配置数组
   */
  public getAllPresets(): FacialAnimationPresetConfig[] {
    return Array.from(this.presets.values());
  }

  /**
   * 获取指定类型的预设
   * @param type 预设类型
   * @returns 预设配置数组
   */
  public getPresetsByType(type: FacialAnimationPresetType): FacialAnimationPresetConfig[] {
    return Array.from(this.presets.values()).filter(preset => preset.type === type);
  }

  /**
   * 获取指定文化的预设
   * @param culture 文化标识
   * @returns 预设配置数组
   */
  public getPresetsByCulture(culture: string): FacialAnimationPresetConfig[] {
    return Array.from(this.presets.values()).filter(preset => preset.culture === culture);
  }

  /**
   * 获取指定标签的预设
   * @param tag 标签
   * @returns 预设配置数组
   */
  public getPresetsByTag(tag: string): FacialAnimationPresetConfig[] {
    return Array.from(this.presets.values()).filter(preset => preset.tags?.includes(tag));
  }

  /**
   * 应用预设到实体
   * @param entity 实体
   * @param presetName 预设名称
   * @returns 是否成功应用
   */
  public applyPreset(entity: Entity, presetName: string): boolean {
    // 获取预设
    const preset = this.getPreset(presetName);
    if (!preset) {
      if (this.config.debug) {
        console.warn(`预设不存在: ${presetName}`);
      }
      return false;
    }

    // 获取面部动画组件
    const facialAnimation = entity.getComponent<FacialAnimationComponent>(FacialAnimationComponent.TYPE);
    if (!facialAnimation) {
      if (this.config.debug) {
        console.warn(`实体没有面部动画组件: ${entity.name}`);
      }
      return false;
    }

    // 根据预设类型应用不同的动画
    switch (preset.type) {
      case FacialAnimationPresetType.STANDARD:
        // 应用标准表情
        if (preset.expression) {
          facialAnimation.setExpression(preset.expression, preset.weight || 1.0);
        }
        break;

      case FacialAnimationPresetType.CULTURAL:
      case FacialAnimationPresetType.EMOTION_COMBO:
        // 应用情感组合
        if (preset.expressionCombos) {
          // 重置到中性表情
          facialAnimation.resetExpression();
          // 应用第一个表情组合（简化处理）
          if (preset.expressionCombos.length > 0) {
            const combo = preset.expressionCombos[0];
            facialAnimation.setExpression(combo.expression, combo.weight);
          }
        } else if (preset.expression) {
          facialAnimation.setExpression(preset.expression, preset.weight || 1.0);
        }
        break;

      case FacialAnimationPresetType.ANIMATION_SEQUENCE:
        // 应用动画序列
        if (preset.animationSequence) {
          this.playAnimationSequence(entity, preset.animationSequence);
        }
        break;

      case FacialAnimationPresetType.CUSTOM:
        // 应用自定义预设
        if (preset.customData) {
          // 处理自定义数据
          // TODO: 实现自定义预设处理
        }
        break;

      default:
        if (this.config.debug) {
          console.warn(`不支持的预设类型: ${preset.type}`);
        }
        return false;
    }

    // 发送事件
    this.eventEmitter.emit('preset_applied', { entity, presetName, preset });

    if (this.config.debug) {
      console.log(`应用预设: ${presetName} 到实体: ${entity.name}`);
    }

    return true;
  }

  /**
   * 播放动画序列
   * @param entity 实体
   * @param sequence 动画序列
   */
  private playAnimationSequence(entity: Entity, sequence: { expression: FacialExpressionType, weight: number, duration: number }[]): void {
    // 获取面部动画组件
    const facialAnimation = entity.getComponent<FacialAnimationComponent>(FacialAnimationComponent.TYPE);
    if (!facialAnimation) return;

    // 重置到中性表情
    facialAnimation.resetExpression();

    // 播放序列
    let totalTime = 0;

    for (let i = 0; i < sequence.length; i++) {
      const frame = sequence[i];

      // 设置延迟执行
      setTimeout(() => {
        facialAnimation.setExpression(frame.expression, frame.weight);
      }, totalTime * 1000);

      totalTime += frame.duration;
    }

    // 发送事件
    this.eventEmitter.emit('animation_sequence_started', { entity, sequence, totalTime });
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 发送更新事件
    this.eventEmitter.emit('update', { deltaTime });
  }
}
