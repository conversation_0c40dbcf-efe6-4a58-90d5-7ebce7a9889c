/**
 * 压缩算法
 */
export enum CompressionAlgorithm {
  /** 不压缩 */
  NONE = 'none',
  /** DEFLATE算法 */
  DEFLATE = 'deflate',
  /** GZIP算法 */
  GZIP = 'gzip',
  /** Brotli算法 */
  BROTLI = 'brotli',
}

/**
 * 压缩选项
 */
export interface CompressionOptions {
  /**
   * 压缩算法
   * @default CompressionAlgorithm.DEFLATE
   */
  algorithm?: CompressionAlgorithm;

  /**
   * 压缩级别
   * @default 6
   */
  level?: number;

  /**
   * 最小压缩大小（字节）
   * @default 100
   */
  minSize?: number;

  /**
   * 是否启用自适应压缩
   * @default true
   */
  enableAdaptive?: boolean;

  /**
   * 是否启用缓存
   * @default true
   */
  enableCache?: boolean;

  /**
   * 缓存大小
   * @default 1000
   */
  cacheSize?: number;

  /**
   * 是否收集统计信息
   * @default true
   */
  collectStats?: boolean;

  /**
   * 消息类型字段
   * @default 'type'
   */
  messageTypeField?: string;

  /**
   * 消息类型算法映射
   * 根据消息类型选择不同的压缩算法
   * @default {}
   */
  messageTypeAlgorithmMap?: Record<string, CompressionAlgorithm>;
}

/**
 * 压缩统计信息
 */
export interface CompressionStats {
  /**
   * 压缩消息数量
   */
  compressedCount: number;

  /**
   * 未压缩消息数量
   */
  uncompressedCount: number;

  /**
   * 总压缩率
   */
  compressionRatio: number;

  /**
   * 总节省字节数
   */
  bytesSaved: number;

  /**
   * 平均压缩时间（毫秒）
   */
  avgCompressionTime: number;

  /**
   * 平均解压时间（毫秒）
   */
  avgDecompressionTime: number;

  /**
   * 缓存命中次数
   */
  cacheHits: number;

  /**
   * 缓存未命中次数
   */
  cacheMisses: number;

  /**
   * 缓存命中率
   */
  cacheHitRate: number;

  /**
   * 算法统计
   */
  algorithmStats: Record<CompressionAlgorithm, {
    /**
     * 使用次数
     */
    count: number;

    /**
     * 平均压缩率
     */
    avgRatio: number;

    /**
     * 平均压缩时间（毫秒）
     */
    avgTime: number;
  }>;
}
