/**
 * 数字人导航组件
 * 实现数字人沿路径移动、停留点处理、状态管理
 */

import * as THREE from 'three';
import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';
import { Transform } from '../../scene/Transform';
import { DigitalHumanPathEditor, StopPoint, PathPoint } from './DigitalHumanPathEditor';

/**
 * 导航状态枚举
 */
export enum NavigationState {
  IDLE = 'idle',
  MOVING = 'moving',
  STOPPED = 'stopped',
  PAUSED = 'paused',
  WAITING = 'waiting',
  INTERACTING = 'interacting'
}

/**
 * 导航事件类型
 */
export enum NavigationEventType {
  STATE_CHANGED = 'stateChanged',
  REACHED_STOP_POINT = 'reachedStopPoint',
  PATH_COMPLETED = 'pathCompleted',
  MOVEMENT_STARTED = 'movementStarted',
  MOVEMENT_PAUSED = 'movementPaused',
  MOVEMENT_RESUMED = 'movementResumed'
}

/**
 * 导航配置接口
 */
export interface NavigationConfig {
  moveSpeed: number;
  rotationSpeed: number;
  stopThreshold: number;
  lookAhead: number;
  smoothRotation: boolean;
  autoStart: boolean;
  debug: boolean;
}

/**
 * 导航事件数据
 */
export interface NavigationEventData {
  state: NavigationState;
  progress: number;
  currentStopPoint?: StopPoint;
  position: THREE.Vector3;
  direction: THREE.Vector3;
}

/**
 * 数字人导航组件
 */
export class DigitalHumanNavigationComponent extends Component {
  public static readonly TYPE = 'DigitalHumanNavigation';
  
  private currentPath: DigitalHumanPathEditor | null = null;
  private currentProgress: number = 0;
  private state: NavigationState = NavigationState.IDLE;
  private currentStopPoint: StopPoint | null = null;
  private waitTimer: number = 0;
  private lastPosition: THREE.Vector3 = new THREE.Vector3();
  private targetRotation: THREE.Quaternion = new THREE.Quaternion();
  
  // 配置
  private config: NavigationConfig = {
    moveSpeed: 2.0,
    rotationSpeed: 5.0,
    stopThreshold: 0.5,
    lookAhead: 0.1,
    smoothRotation: true,
    autoStart: false,
    debug: false
  };
  
  // 事件回调
  public onStateChanged?: (data: NavigationEventData) => void;
  public onReachedStopPoint?: (stopPoint: StopPoint) => void;
  public onPathCompleted?: () => void;

  constructor(entity: Entity, config?: Partial<NavigationConfig>) {
    super(DigitalHumanNavigationComponent.TYPE);
    
    if (config) {
      Object.assign(this.config, config);
    }
  }

  /**
   * 设置路径
   */
  public setPath(path: DigitalHumanPathEditor): void {
    this.currentPath = path;
    this.currentProgress = 0;
    this.currentStopPoint = null;
    this.waitTimer = 0;
    
    if (this.config.autoStart) {
      this.startMoving();
    } else {
      this.setState(NavigationState.IDLE);
    }
  }

  /**
   * 开始沿路径移动
   */
  public startMoving(): void {
    if (!this.currentPath || this.currentPath.getPathPoints().length < 2) {
      if (this.config.debug) {
        console.warn('DigitalHumanNavigation: 无有效路径，无法开始移动');
      }
      return;
    }
    
    this.setState(NavigationState.MOVING);
    this.currentProgress = 0;
    this.currentStopPoint = null;
    this.waitTimer = 0;
    
    if (this.config.debug) {
      console.log('DigitalHumanNavigation: 开始移动');
    }
  }

  /**
   * 停止移动
   */
  public stopMoving(): void {
    this.setState(NavigationState.STOPPED);
    
    if (this.config.debug) {
      console.log('DigitalHumanNavigation: 停止移动');
    }
  }

  /**
   * 暂停移动
   */
  public pauseMoving(): void {
    if (this.state === NavigationState.MOVING) {
      this.setState(NavigationState.PAUSED);
      
      if (this.config.debug) {
        console.log('DigitalHumanNavigation: 暂停移动');
      }
    }
  }

  /**
   * 恢复移动
   */
  public resumeMoving(): void {
    if (this.state === NavigationState.PAUSED) {
      this.setState(NavigationState.MOVING);
      
      if (this.config.debug) {
        console.log('DigitalHumanNavigation: 恢复移动');
      }
    }
  }

  /**
   * 设置移动速度
   */
  public setMoveSpeed(speed: number): void {
    this.config.moveSpeed = Math.max(0, speed);
  }

  /**
   * 获取移动速度
   */
  public getMoveSpeed(): number {
    return this.config.moveSpeed;
  }

  /**
   * 获取当前状态
   */
  public getState(): NavigationState {
    return this.state;
  }

  /**
   * 获取当前进度 (0-1)
   */
  public getProgress(): number {
    return this.currentProgress;
  }

  /**
   * 设置进度
   */
  public setProgress(progress: number): void {
    this.currentProgress = Math.max(0, Math.min(1, progress));
    
    if (this.currentPath) {
      const position = this.currentPath.getPositionAt(this.currentProgress);
      const direction = this.currentPath.getDirectionAt(this.currentProgress);
      
      this.updateEntityTransform(position, direction);
    }
  }

  /**
   * 跳转到指定停留点
   */
  public jumpToStopPoint(stopPointId: string): boolean {
    if (!this.currentPath) return false;
    
    const stopPoint = this.currentPath.getStopPoints().find(sp => sp.id === stopPointId);
    if (!stopPoint) return false;
    
    // 计算停留点在路径上的位置
    const pathPoints = this.currentPath.getPathPoints();
    const stopIndex = pathPoints.findIndex(p => p.id === stopPointId);
    
    if (stopIndex !== -1) {
      this.currentProgress = stopIndex / (pathPoints.length - 1);
      this.updateEntityTransform(stopPoint.position, new THREE.Vector3(0, 0, 1));
      
      if (this.config.debug) {
        console.log(`DigitalHumanNavigation: 跳转到停留点 ${stopPointId}`);
      }
      
      return true;
    }
    
    return false;
  }

  /**
   * 更新组件
   */
  public update(deltaTime: number): void {
    if (!this.currentPath || this.state === NavigationState.IDLE || this.state === NavigationState.STOPPED) {
      return;
    }
    
    // 处理等待状态
    if (this.state === NavigationState.WAITING) {
      this.waitTimer -= deltaTime * 1000; // 转换为毫秒
      
      if (this.waitTimer <= 0) {
        this.currentStopPoint = null;
        this.setState(NavigationState.MOVING);
      }
      
      return;
    }
    
    // 处理移动状态
    if (this.state === NavigationState.MOVING) {
      this.updateMovement(deltaTime);
    }
  }

  /**
   * 更新移动
   */
  private updateMovement(deltaTime: number): void {
    if (!this.currentPath) return;
    
    const pathLength = this.currentPath.getLength();
    if (pathLength === 0) return;
    
    // 更新进度
    const progressDelta = (this.config.moveSpeed * deltaTime) / pathLength;
    this.currentProgress += progressDelta;
    
    // 检查是否到达路径终点
    if (this.currentProgress >= 1.0) {
      this.currentProgress = 1.0;
      this.setState(NavigationState.IDLE);
      
      if (this.onPathCompleted) {
        this.onPathCompleted();
      }
      
      if (this.config.debug) {
        console.log('DigitalHumanNavigation: 路径完成');
      }
      
      return;
    }
    
    // 获取当前位置和方向
    const position = this.currentPath.getPositionAt(this.currentProgress);
    const direction = this.currentPath.getDirectionAt(this.currentProgress);
    
    // 更新实体变换
    this.updateEntityTransform(position, direction);
    
    // 检查是否到达停留点
    this.checkStopPoints(position);
    
    // 存储当前位置
    this.lastPosition.copy(position);
  }

  /**
   * 更新实体变换
   */
  private updateEntityTransform(position: THREE.Vector3, direction: THREE.Vector3): void {
    const transform = this.entity.getComponent<Transform>(Transform.type);
    if (!transform) return;

    // 更新位置
    transform.setPosition(position);

    // 更新旋转
    if (this.config.smoothRotation) {
      // 计算目标旋转
      const lookAt = position.clone().add(direction);
      this.targetRotation.setFromRotationMatrix(
        new THREE.Matrix4().lookAt(position, lookAt, new THREE.Vector3(0, 1, 0))
      );

      // 平滑插值到目标旋转
      const currentQuaternion = new THREE.Quaternion();
      currentQuaternion.setFromEuler(transform.getRotation());
      currentQuaternion.slerp(this.targetRotation, this.config.rotationSpeed * 0.016); // 假设60fps
      const newEuler = new THREE.Euler().setFromQuaternion(currentQuaternion);
      transform.setRotation(newEuler);
    } else {
      // 直接设置朝向
      const lookAt = position.clone().add(direction);
      transform.lookAt(lookAt);
    }
  }

  /**
   * 检查停留点
   */
  private checkStopPoints(currentPosition: THREE.Vector3): void {
    if (!this.currentPath || this.currentStopPoint) return;

    const stopPoints = this.currentPath.getStopPoints();

    for (const stopPoint of stopPoints) {
      const distance = currentPosition.distanceTo(stopPoint.position);

      if (distance <= this.config.stopThreshold) {
        this.currentStopPoint = stopPoint;
        this.handleStopPoint(stopPoint);
        break;
      }
    }
  }

  /**
   * 处理停留点
   */
  private handleStopPoint(stopPoint: StopPoint): void {
    this.setState(NavigationState.WAITING);
    this.waitTimer = stopPoint.waitTime;

    if (this.config.debug) {
      console.log(`DigitalHumanNavigation: 到达停留点 ${stopPoint.id}, 等待 ${stopPoint.waitTime}ms`);
    }

    // 触发停留点动作
    this.triggerStopPointActions(stopPoint);

    // 触发事件
    if (this.onReachedStopPoint) {
      this.onReachedStopPoint(stopPoint);
    }
  }

  /**
   * 触发停留点动作
   */
  private triggerStopPointActions(stopPoint: StopPoint): void {
    for (const action of stopPoint.triggerActions) {
      this.triggerAction(action);
    }
  }

  /**
   * 触发动作
   */
  private triggerAction(actionName: string): void {
    // 这里可以集成动画系统或其他行为系统
    if (this.config.debug) {
      console.log(`DigitalHumanNavigation: 触发动作 ${actionName}`);
    }

    // 发送动作事件到实体的其他组件
    this.entity.emit('action', { actionName, source: 'navigation' });
  }

  /**
   * 设置状态
   */
  private setState(newState: NavigationState): void {
    if (this.state === newState) return;

    const oldState = this.state;
    this.state = newState;

    if (this.config.debug) {
      console.log(`DigitalHumanNavigation: 状态变化 ${oldState} -> ${newState}`);
    }

    // 触发状态变化事件
    if (this.onStateChanged) {
      const eventData: NavigationEventData = {
        state: newState,
        progress: this.currentProgress,
        currentStopPoint: this.currentStopPoint || undefined,
        position: this.lastPosition.clone(),
        direction: this.currentPath ? this.currentPath.getDirectionAt(this.currentProgress) : new THREE.Vector3(0, 0, 1)
      };

      this.onStateChanged(eventData);
    }
  }

  /**
   * 获取当前路径
   */
  public getCurrentPath(): DigitalHumanPathEditor | null {
    return this.currentPath;
  }

  /**
   * 获取当前停留点
   */
  public getCurrentStopPoint(): StopPoint | null {
    return this.currentStopPoint;
  }

  /**
   * 获取剩余等待时间
   */
  public getRemainingWaitTime(): number {
    return this.waitTimer;
  }

  /**
   * 跳过当前等待
   */
  public skipWait(): void {
    if (this.state === NavigationState.WAITING) {
      this.waitTimer = 0;
      this.currentStopPoint = null;
      this.setState(NavigationState.MOVING);

      if (this.config.debug) {
        console.log('DigitalHumanNavigation: 跳过等待');
      }
    }
  }

  /**
   * 获取到下一个停留点的距离
   */
  public getDistanceToNextStopPoint(): number {
    if (!this.currentPath) return -1;

    const stopPoints = this.currentPath.getStopPoints();
    const currentPosition = this.currentPath.getPositionAt(this.currentProgress);

    let minDistance = Infinity;

    for (const stopPoint of stopPoints) {
      // 只考虑前方的停留点
      const pathPoints = this.currentPath.getPathPoints();
      const stopIndex = pathPoints.findIndex(p => p.id === stopPoint.id);
      const currentIndex = Math.floor(this.currentProgress * (pathPoints.length - 1));

      if (stopIndex > currentIndex) {
        const distance = currentPosition.distanceTo(stopPoint.position);
        minDistance = Math.min(minDistance, distance);
      }
    }

    return minDistance === Infinity ? -1 : minDistance;
  }

  /**
   * 获取配置
   */
  public getConfig(): NavigationConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<NavigationConfig>): void {
    Object.assign(this.config, config);
  }

  /**
   * 重置导航
   */
  public reset(): void {
    this.currentProgress = 0;
    this.currentStopPoint = null;
    this.waitTimer = 0;
    this.setState(NavigationState.IDLE);

    if (this.config.debug) {
      console.log('DigitalHumanNavigation: 重置导航');
    }
  }

  /**
   * 获取导航信息
   */
  public getNavigationInfo(): any {
    return {
      state: this.state,
      progress: this.currentProgress,
      hasPath: !!this.currentPath,
      pathLength: this.currentPath ? this.currentPath.getLength() : 0,
      pathPointCount: this.currentPath ? this.currentPath.getPathPoints().length : 0,
      stopPointCount: this.currentPath ? this.currentPath.getStopPoints().length : 0,
      currentStopPoint: this.currentStopPoint ? {
        id: this.currentStopPoint.id,
        waitTime: this.currentStopPoint.waitTime,
        remainingWaitTime: this.waitTimer
      } : null,
      distanceToNextStop: this.getDistanceToNextStopPoint(),
      config: this.getConfig()
    };
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    this.currentPath = null;
    this.currentStopPoint = null;
    this.setState(NavigationState.IDLE);

    // 清理事件回调
    this.onStateChanged = undefined;
    this.onReachedStopPoint = undefined;
    this.onPathCompleted = undefined;
  }
}
