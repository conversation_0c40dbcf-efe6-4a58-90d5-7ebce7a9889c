# Service Registry 健康检查 URL 修复报告

## 问题描述

在 service-registry 服务运行时出现了 `TypeError: Only absolute URLs are supported` 错误，错误发生在健康检查过程中使用 node-fetch 库时。

### 错误信息
```
TypeError: Only absolute URLs are supported
    at getNodeRequestOptions (/app/node_modules/node-fetch/lib/index.js:1447:9)
    at /app/node_modules/node-fetch/lib/index.js:1547:19
    at new Promise (<anonymous>)
    at fetch (/app/node_modules/node-fetch/lib/index.js:1547:9)
    at HttpHealthCheckStrategy.check (/app/dist/service-registry/src/registry/health-check/http-health-check.strategy.js:36:36)
```

## 根本原因

在 `enhanced-service-discovery.service.ts` 中，健康检查 URL 的配置存在问题：

1. **默认配置问题**: `healthCheckUrl` 的默认值设置为 `/health`（相对路径）
2. **URL 构建逻辑缺陷**: 在构建健康检查配置时，没有正确处理相对路径的情况
3. **数据存储问题**: 相对路径被直接存储到数据库中，导致后续使用时出错

### 问题代码位置

**文件**: `server/service-registry/src/registry/enhanced-service-discovery.service.ts`

**第 171 行**: 
```typescript
healthCheckUrl: this.configService.get<string>('HEALTH_CHECK_URL', '/health'),
```

**第 254 行**: 
```typescript
target: this.serviceOptions.healthCheckUrl || `http://${ipAddress}:${this.serviceOptions.port}/health`,
```

**第 292 行和第 311 行**: 
```typescript
healthCheckUrl: this.serviceOptions.healthCheckUrl,
```

## 修复方案

### 1. 修复健康检查目标 URL 构建逻辑

在构建健康检查配置时，添加了 URL 类型检查和转换逻辑：

```typescript
// 创建健康检查配置
let healthCheckTarget: string;
if (this.serviceOptions.healthCheckUrl) {
  // 如果配置了健康检查URL，检查是否为绝对URL
  if (this.serviceOptions.healthCheckUrl.startsWith('http://') || this.serviceOptions.healthCheckUrl.startsWith('https://')) {
    healthCheckTarget = this.serviceOptions.healthCheckUrl;
  } else {
    // 如果是相对路径，构建完整的URL
    healthCheckTarget = `http://${ipAddress}:${this.serviceOptions.port}${this.serviceOptions.healthCheckUrl}`;
  }
} else {
  // 默认健康检查路径
  healthCheckTarget = `http://${ipAddress}:${this.serviceOptions.port}/health`;
}
```

### 2. 修复数据库存储

确保存储到数据库的 `healthCheckUrl` 是完整的 URL：

```typescript
// 创建服务实例时
healthCheckUrl: healthCheckTarget,

// 更新服务实例时
serviceInstance.healthCheckUrl = healthCheckTarget;
```

## 修复效果

1. **支持相对路径**: 可以在配置中使用相对路径（如 `/health`），系统会自动转换为完整 URL
2. **支持绝对路径**: 可以在配置中使用完整 URL（如 `http://example.com/health`），系统会直接使用
3. **避免 node-fetch 错误**: 确保传递给 node-fetch 的始终是绝对 URL
4. **数据一致性**: 数据库中存储的是完整的健康检查 URL

## 测试验证

修复后，service-registry 服务应该能够：

1. 正常启动并注册自身服务
2. 执行健康检查而不出现 URL 错误
3. 正确处理其他服务的注册和健康检查

## 相关文件

- `server/service-registry/src/registry/enhanced-service-discovery.service.ts` - 主要修复文件
- `server/service-registry/src/registry/health-check/http-health-check.strategy.ts` - HTTP 健康检查策略
- `server/service-registry/src/registry/health-check/health-check.interface.ts` - 健康检查接口定义

## 注意事项

1. 此修复保持了向后兼容性，既支持相对路径也支持绝对路径
2. 修复不会影响现有的健康检查逻辑和功能
3. 建议在生产环境中使用完整的健康检查 URL 以避免潜在的配置问题
