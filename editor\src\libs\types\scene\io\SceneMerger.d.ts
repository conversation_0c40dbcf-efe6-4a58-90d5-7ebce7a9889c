import { Scene } from '../Scene';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
/**
 * 场景合并选项
 */
export interface SceneMergeOptions {
    /** 是否保留原始场景 */
    preserveOriginalScenes?: boolean;
    /** 是否合并材质 */
    mergeMaterials?: boolean;
    /** 是否合并几何体 */
    mergeGeometries?: boolean;
    /** 是否合并纹理 */
    mergeTextures?: boolean;
    /** 是否优化合并后的场景 */
    optimizeMergedScene?: boolean;
    /** 是否应用变换 */
    applyTransforms?: boolean;
    /** 是否重新生成UUID */
    regenerateUUIDs?: boolean;
    /** 是否保留场景层次结构 */
    preserveHierarchy?: boolean;
    /** 是否使用实例化渲染 */
    useInstancing?: boolean;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 场景拆分选项
 */
export interface SceneSplitOptions {
    /** 拆分方式 */
    splitMethod?: 'hierarchy' | 'spatial' | 'material' | 'custom';
    /** 是否保留原始场景 */
    preserveOriginalScene?: boolean;
    /** 是否优化拆分后的场景 */
    optimizeSplitScenes?: boolean;
    /** 是否重新生成UUID */
    regenerateUUIDs?: boolean;
    /** 自定义拆分函数 */
    customSplitFunction?: (entity: Entity) => number;
    /** 空间拆分的网格大小 */
    spatialGridSize?: number;
    /** 最大场景数量 */
    maxSceneCount?: number;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 场景合并结果
 */
export interface SceneMergeResult {
    /** 合并后的场景 */
    mergedScene: Scene;
    /** 实体映射（原始实体ID -> 新实体ID） */
    entityMap: Map<string, string>;
    /** 组件映射（原始组件ID -> 新组件ID） */
    componentMap: Map<string, string>;
    /** 材质映射（原始材质ID -> 新材质ID） */
    materialMap: Map<string, string>;
    /** 几何体映射（原始几何体ID -> 新几何体ID） */
    geometryMap: Map<string, string>;
    /** 纹理映射（原始纹理ID -> 新纹理ID） */
    textureMap: Map<string, string>;
}
/**
 * 场景拆分结果
 */
export interface SceneSplitResult {
    /** 拆分后的场景列表 */
    splitScenes: Scene[];
    /** 实体映射（原始实体ID -> 新实体ID） */
    entityMap: Map<string, string[]>;
    /** 组件映射（原始组件ID -> 新组件ID） */
    componentMap: Map<string, string[]>;
    /** 材质映射（原始材质ID -> 新材质ID） */
    materialMap: Map<string, string[]>;
    /** 几何体映射（原始几何体ID -> 新几何体ID） */
    geometryMap: Map<string, string[]>;
    /** 纹理映射（原始纹理ID -> 新纹理ID） */
    textureMap: Map<string, string[]>;
}
/**
 * 场景合并器
 */
export declare class SceneMerger extends EventEmitter {
    /** 世界实例 */
    private world;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建场景合并器
     * @param world 世界实例
     */
    constructor(world: World);
    /**
     * 初始化合并器
     */
    initialize(): void;
    /**
     * 合并场景
     * @param scenes 要合并的场景列表
     * @param options 合并选项
     * @returns 合并结果
     */
    mergeScenes(scenes: Scene[], options?: SceneMergeOptions): SceneMergeResult;
    /**
     * 拆分场景
     * @param scene 要拆分的场景
     * @param options 拆分选项
     * @returns 拆分结果
     */
    splitScene(scene: Scene, options?: SceneSplitOptions): SceneSplitResult;
    /**
     * 将场景合并到目标场景中
     * @param sourceScene 源场景
     * @param targetScene 目标场景
     * @param options 合并选项
     * @param entityMap 实体映射
     * @param componentMap 组件映射
     * @param materialMap 材质映射
     * @param geometryMap 几何体映射
     * @param textureMap 纹理映射
     */
    private mergeSceneInto;
    /**
     * 合并实体
     * @param sourceEntity 源实体
     * @param targetScene 目标场景
     * @param options 合并选项
     * @param entityMap 实体映射
     * @param componentMap 组件映射
     */
    private mergeEntity;
    /**
     * 合并场景设置
     * @param sourceScene 源场景
     * @param targetScene 目标场景
     */
    private mergeSceneSettings;
    /**
     * 优化场景
     * @param scene 要优化的场景
     */
    private optimizeScene;
    /**
     * 优化材质
     * @param scene 场景
     */
    private optimizeMaterials;
    /**
     * 优化几何体
     * @param scene 场景
     */
    private optimizeGeometries;
    /**
     * 优化纹理
     * @param scene 场景
     */
    private optimizeTextures;
    /**
     * 优化场景图
     * @param scene 场景
     */
    private optimizeSceneGraph;
    /**
     * 按层次结构拆分场景
     * @param scene 要拆分的场景
     * @param options 拆分选项
     * @returns 拆分结果
     */
    private splitSceneByHierarchy;
    /**
     * 按空间拆分场景
     * @param scene 要拆分的场景
     * @param options 拆分选项
     * @returns 拆分结果
     */
    private splitSceneBySpatial;
    /**
     * 按材质拆分场景
     * @param scene 要拆分的场景
     * @param options 拆分选项
     * @returns 拆分结果
     */
    private splitSceneByMaterial;
    /**
     * 按自定义方式拆分场景
     * @param scene 要拆分的场景
     * @param options 拆分选项
     * @returns 拆分结果
     */
    private splitSceneByCustom;
    /**
     * 复制实体层次结构到场景
     * @param entity 要复制的实体
     * @param targetScene 目标场景
     * @param entityMap 实体映射
     * @param componentMap 组件映射
     */
    private copyEntityHierarchy;
    /**
     * 复制实体到场景
     * @param entity 要复制的实体
     * @param targetScene 目标场景
     * @param entityMap 实体映射
     * @param _componentMap 组件映射（暂未使用）
     */
    private copyEntityToScene;
}
