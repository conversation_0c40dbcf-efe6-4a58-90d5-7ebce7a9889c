/**
 * 音频控制器
 */
import { Controller, Get, UseGuards, Request, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AudioService } from './audio.service';
import { Asset } from '../assets/entities/asset.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('音频')
@Controller('audio')
export class AudioController {
  constructor(private readonly audioService: AudioService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有音频' })
  @ApiResponse({ status: 200, description: '返回所有音频', type: [Asset] })
  async findAll(
    @Request() req,
    @Query('projectId') projectId?: string,
    @Query('tags') tags?: string,
  ) {
    const tagArray = tags ? tags.split(',') : undefined;
    return this.audioService.findAll(req.user.id, projectId, tagArray);
  }

  @Get('search')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索音频' })
  @ApiResponse({ status: 200, description: '返回搜索结果', type: [Asset] })
  async search(@Request() req, @Query('query') query: string) {
    return this.audioService.search(query, req.user.id);
  }
}
