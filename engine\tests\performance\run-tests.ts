/**
 * 运行性能测试并收集数据
 */
import { PerformanceTestRunner } from './PerformanceTestRunner';
import { renderingTestSuite, physicsTestSuite, fullTestSuite } from './PerformanceTestConfig';
import { Debug } from '../../src/utils/Debug';
import * as fs from 'fs';
import * as path from 'path';

// 报告输出目录
const REPORTS_DIR = path.join(__dirname, 'reports');

// 确保目录存在
if (!fs.existsSync(REPORTS_DIR)) {
  fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

/**
 * 运行渲染性能测试
 */
async function runRenderingTests(): Promise<void> {
  Debug.log('性能测试', '开始运行渲染性能测试');
  
  return new Promise((resolve) => {
    const runner = new PerformanceTestRunner(renderingTestSuite);
    runner.run((report) => {
      Debug.log('性能测试', '渲染性能测试完成');
      
      // 保存HTML报告
      const htmlReport = PerformanceTestRunner.generateHTMLReport(report);
      fs.writeFileSync(path.join(REPORTS_DIR, 'rendering-performance-report.html'), htmlReport);
      
      // 保存JSON报告
      fs.writeFileSync(
        path.join(REPORTS_DIR, 'rendering-performance-report.json'), 
        JSON.stringify(report, null, 2)
      );
      
      Debug.log('性能测试', `渲染性能测试报告已保存到 ${REPORTS_DIR}`);
      resolve();
    });
  });
}

/**
 * 运行物理性能测试
 */
async function runPhysicsTests(): Promise<void> {
  Debug.log('性能测试', '开始运行物理性能测试');
  
  return new Promise((resolve) => {
    const runner = new PerformanceTestRunner(physicsTestSuite);
    runner.run((report) => {
      Debug.log('性能测试', '物理性能测试完成');
      
      // 保存HTML报告
      const htmlReport = PerformanceTestRunner.generateHTMLReport(report);
      fs.writeFileSync(path.join(REPORTS_DIR, 'physics-performance-report.html'), htmlReport);
      
      // 保存JSON报告
      fs.writeFileSync(
        path.join(REPORTS_DIR, 'physics-performance-report.json'), 
        JSON.stringify(report, null, 2)
      );
      
      Debug.log('性能测试', `物理性能测试报告已保存到 ${REPORTS_DIR}`);
      resolve();
    });
  });
}

/**
 * 运行完整性能测试
 */
async function runFullTests(): Promise<void> {
  Debug.log('性能测试', '开始运行完整性能测试');
  
  return new Promise((resolve) => {
    const runner = new PerformanceTestRunner(fullTestSuite);
    runner.run((report) => {
      Debug.log('性能测试', '完整性能测试完成');
      
      // 保存HTML报告
      const htmlReport = PerformanceTestRunner.generateHTMLReport(report);
      fs.writeFileSync(path.join(REPORTS_DIR, 'full-performance-report.html'), htmlReport);
      
      // 保存JSON报告
      fs.writeFileSync(
        path.join(REPORTS_DIR, 'full-performance-report.json'), 
        JSON.stringify(report, null, 2)
      );
      
      Debug.log('性能测试', `完整性能测试报告已保存到 ${REPORTS_DIR}`);
      resolve();
    });
  });
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const startTime = Date.now();
  Debug.log('性能测试', '开始运行性能测试');
  
  try {
    // 运行渲染测试
    await runRenderingTests();
    
    // 运行物理测试
    await runPhysicsTests();
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    Debug.log('性能测试', `所有测试完成，总耗时: ${duration.toFixed(2)}秒`);
    Debug.log('性能测试', `测试报告已保存到 ${REPORTS_DIR}`);
  } catch (error) {
    Debug.error('性能测试', `测试运行失败: ${error}`);
  }
}

// 运行主函数
main().catch((error) => {
  Debug.error('性能测试', `未捕获的错误: ${error}`);
  process.exit(1);
});
