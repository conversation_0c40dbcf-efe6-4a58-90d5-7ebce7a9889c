/**
 * 输入性能监控
 * 用于监控和自动调整输入系统的性能
 */
import { DeviceCapabilities, DevicePerformanceLevel } from '../../utils/DeviceCapabilities';
/**
 * 性能数据
 */
export interface PerformanceData {
    /** 时间戳 */
    timestamp: number;
    /** 帧率 */
    fps: number;
    /** 输入延迟（毫秒） */
    inputLatency: number;
    /** 处理时间（毫秒） */
    processingTime: number;
}
/**
 * 性能监控选项
 */
export interface PerformanceMonitorOptions {
    /** 采样间隔（毫秒） */
    sampleInterval?: number;
    /** 历史数据长度 */
    historyLength?: number;
    /** 是否自动调整 */
    autoAdjust?: boolean;
    /** 目标帧率 */
    targetFPS?: number;
    /** 最大输入延迟（毫秒） */
    maxInputLatency?: number;
    /** 设备能力检测 */
    deviceCapabilities?: DeviceCapabilities;
}
/**
 * 输入性能监控
 */
export declare class InputPerformanceMonitor {
    /** 采样间隔（毫秒） */
    private sampleInterval;
    /** 历史数据长度 */
    private historyLength;
    /** 是否自动调整 */
    private autoAdjust;
    /** 目标帧率 */
    private targetFPS;
    /** 最大输入延迟（毫秒） */
    private maxInputLatency;
    /** 设备能力检测 */
    private deviceCapabilities;
    /** 性能历史数据 */
    private history;
    /** 上一帧时间 */
    private lastFrameTime;
    /** 当前帧率 */
    private currentFPS;
    /** 当前输入延迟 */
    private currentInputLatency;
    /** 当前处理时间 */
    private currentProcessingTime;
    /** 采样定时器 */
    private sampleTimer;
    /** 性能级别 */
    private performanceLevel;
    /** 事件处理器 */
    private eventHandlers;
    /**
     * 创建输入性能监控
     * @param options 性能监控选项
     */
    constructor(options?: PerformanceMonitorOptions);
    /**
     * 开始监控
     */
    start(): void;
    /**
     * 停止监控
     */
    stop(): void;
    /**
     * 记录输入事件
     * @param eventType 事件类型
     * @param processingTime 处理时间（毫秒）
     */
    recordInputEvent(eventType: string, processingTime: number): void;
    /**
     * 获取当前帧率
     * @returns 当前帧率
     */
    getFPS(): number;
    /**
     * 获取当前输入延迟
     * @returns 当前输入延迟（毫秒）
     */
    getInputLatency(): number;
    /**
     * 获取当前处理时间
     * @returns 当前处理时间（毫秒）
     */
    getProcessingTime(): number;
    /**
     * 获取性能历史数据
     * @returns 性能历史数据
     */
    getHistory(): PerformanceData[];
    /**
     * 获取性能级别
     * @returns 性能级别
     */
    getPerformanceLevel(): DevicePerformanceLevel;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param handler 事件处理函数
     */
    on(event: string, handler: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param handler 事件处理函数
     */
    off(event: string, handler: (data: any) => void): void;
    /**
     * 触发事件
     * @param event 事件名称
     * @param data 事件数据
     */
    private emit;
    /**
     * 采样性能数据
     */
    private sample;
    /**
     * 根据性能数据调整性能
     */
    private adjustPerformance;
}
