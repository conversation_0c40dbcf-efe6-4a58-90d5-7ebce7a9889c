/**
 * 地形性能基准测试
 * 用于测试和比较地形渲染性能
 */
import * as THREE from 'three';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { TerrainComponent } from '../components/TerrainComponent';
import type { Entity } from '../../core/Entity';
import type { Camera   } from '../../rendering/Camera';
import { Scene } from '../../scene/Scene';
import { TerrainPerformanceMonitor, PerformanceData } from './TerrainPerformanceMonitor';

/**
 * 基准测试事件类型
 */
export enum BenchmarkEventType {
  /** 基准测试开始 */
  BENCHMARK_STARTED = 'benchmark_started',
  /** 基准测试完成 */
  BENCHMARK_COMPLETED = 'benchmark_completed',
  /** 基准测试进度 */
  BENCHMARK_PROGRESS = 'benchmark_progress',
  /** 基准测试错误 */
  BENCHMARK_ERROR = 'benchmark_error'
}

/**
 * 基准测试配置
 */
export interface BenchmarkConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 测试持续时间（毫秒） */
  testDuration?: number;
  /** 预热时间（毫秒） */
  warmupDuration?: number;
  /** 采样间隔（毫秒） */
  sampleInterval?: number;
  /** 相机路径类型 */
  cameraPathType?: CameraPathType;
  /** 是否测试LOD */
  testLOD?: boolean;
  /** 是否测试视锥体剔除 */
  testFrustumCulling?: boolean;
  /** 是否测试纹理流式加载 */
  testTextureStreaming?: boolean;
  /** 是否测试虚拟纹理 */
  testVirtualTexturing?: boolean;
  /** 是否测试几何体压缩 */
  testGeometryCompression?: boolean;
  /** 是否测试物理LOD */
  testPhysicsLOD?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 相机路径类型
 */
export enum CameraPathType {
  /** 静态 */
  STATIC = 'static',
  /** 圆形 */
  CIRCULAR = 'circular',
  /** 飞越 */
  FLYOVER = 'flyover',
  /** 随机 */
  RANDOM = 'random',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 基准测试结果
 */
export interface BenchmarkResult {
  /** 测试名称 */
  name: string;
  /** 测试持续时间（毫秒） */
  duration: number;
  /** 平均帧率 */
  averageFPS: number;
  /** 最小帧率 */
  minFPS: number;
  /** 最大帧率 */
  maxFPS: number;
  /** 帧率标准差 */
  fpsStdDev: number;
  /** 平均帧时间（毫秒） */
  averageFrameTime: number;
  /** 平均CPU使用率 */
  averageCPUUsage: number;
  /** 平均GPU使用率 */
  averageGPUUsage: number;
  /** 平均内存使用量（字节） */
  averageMemoryUsage: number;
  /** 平均渲染时间（毫秒） */
  averageRenderTime: number;
  /** 平均物理时间（毫秒） */
  averagePhysicsTime: number;
  /** 平均可见地形块数 */
  averageVisibleTerrainChunks: number;
  /** 平均地形三角形数 */
  averageTerrainTriangles: number;
  /** 平均地形顶点数 */
  averageTerrainVertices: number;
  /** 平均地形纹理内存（字节） */
  averageTerrainTextureMemory: number;
  /** 平均地形几何体内存（字节） */
  averageTerrainGeometryMemory: number;
  /** 平均地形物理内存（字节） */
  averageTerrainPhysicsMemory: number;
  /** 性能数据样本 */
  samples: PerformanceData[];
  /** 测试配置 */
  config: BenchmarkConfig;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 地形性能基准测试类
 */
export class TerrainPerformanceBenchmark {
  /** 是否启用 */
  private enabled: boolean;
  /** 测试持续时间（毫秒） */
  private testDuration: number;
  /** 预热时间（毫秒） */
  private warmupDuration: number;
  /** 采样间隔（毫秒） */
  private sampleInterval: number;
  /** 相机路径类型 */
  private cameraPathType: CameraPathType;
  /** 是否测试LOD */
  private testLOD: boolean;
  /** 是否测试视锥体剔除 */
  private testFrustumCulling: boolean;
  /** 是否测试纹理流式加载 */
  private testTextureStreaming: boolean;
  /** 是否测试虚拟纹理 */
  private testVirtualTexturing: boolean;
  /** 是否测试几何体压缩 */
  private testGeometryCompression: boolean;
  /** 是否测试物理LOD */
  private testPhysicsLOD: boolean;
  /** 是否启用调试 */
  private debug: boolean;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 性能监控器 */
  private performanceMonitor: TerrainPerformanceMonitor;
  /** 是否正在测试 */
  private isTesting: boolean;
  /** 当前测试 */
  private currentTest: string | null;
  /** 测试结果 */
  private testResults: Map<string, BenchmarkResult>;
  /** 测试定时器ID */
  private testTimerId: number | null;
  /** 采样定时器ID */
  private sampleTimerId: number | null;
  /** 相机原始位置 */
  private originalCameraPosition: THREE.Vector3 | null;
  /** 相机原始旋转 */
  private originalCameraRotation: THREE.Euler | null;
  /** 相机路径点 */
  private cameraPathPoints: THREE.Vector3[];
  /** 相机路径时间 */
  private cameraPathTime: number;
  /** 测试开始时间 */
  private testStartTime: number;
  /** 性能数据样本 */
  private performanceSamples: PerformanceData[];
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null;
  /** 相机 */
  private camera: Camera | null;
  /** 场景 */
  private scene: Scene | null;

  /** 性能基准测试实例 */
  private static instance: TerrainPerformanceBenchmark | null = null;

  /**
   * 获取性能基准测试实例
   * @returns 性能基准测试实例
   */
  public static getInstance(): TerrainPerformanceBenchmark {
    if (!TerrainPerformanceBenchmark.instance) {
      TerrainPerformanceBenchmark.instance = new TerrainPerformanceBenchmark();
    }
    return TerrainPerformanceBenchmark.instance;
  }

  /**
   * 创建地形性能基准测试
   * @param config 配置
   */
  constructor(config: BenchmarkConfig = {}) {
    // 初始化配置
    this.enabled = config.enabled !== undefined ? config.enabled : true;
    this.testDuration = config.testDuration || 10000; // 10秒
    this.warmupDuration = config.warmupDuration || 2000; // 2秒
    this.sampleInterval = config.sampleInterval || 100; // 100毫秒
    this.cameraPathType = config.cameraPathType || CameraPathType.CIRCULAR;
    this.testLOD = config.testLOD !== undefined ? config.testLOD : true;
    this.testFrustumCulling = config.testFrustumCulling !== undefined ? config.testFrustumCulling : true;
    this.testTextureStreaming = config.testTextureStreaming !== undefined ? config.testTextureStreaming : true;
    this.testVirtualTexturing = config.testVirtualTexturing !== undefined ? config.testVirtualTexturing : true;
    this.testGeometryCompression = config.testGeometryCompression !== undefined ? config.testGeometryCompression : true;
    this.testPhysicsLOD = config.testPhysicsLOD !== undefined ? config.testPhysicsLOD : true;
    this.debug = config.debug !== undefined ? config.debug : false;

    // 初始化属性
    this.eventEmitter = new EventEmitter();
    this.performanceMonitor = TerrainPerformanceMonitor.getInstance();
    this.isTesting = false;
    this.currentTest = null;
    this.testResults = new Map();
    this.testTimerId = null;
    this.sampleTimerId = null;
    this.originalCameraPosition = null;
    this.originalCameraRotation = null;
    this.cameraPathPoints = [];
    this.cameraPathTime = 0;
    this.testStartTime = 0;
    this.performanceSamples = [];
    this.renderer = null;
    this.camera = null;
    this.scene = null;
  }

  /**
   * 设置渲染器
   * @param renderer 渲染器
   */
  public setRenderer(renderer: THREE.WebGLRenderer): void {
    this.renderer = renderer;
  }

  /**
   * 设置相机
   * @param camera 相机
   */
  public setCamera(camera: Camera): void {
    this.camera = camera;
  }

  /**
   * 设置场景
   * @param scene 场景
   */
  public setScene(scene: Scene): void {
    this.scene = scene;
  }

  /**
   * 运行基准测试
   * @param testName 测试名称
   * @param config 测试配置
   * @returns 测试结果
   */
  public async runBenchmark(testName: string, config?: BenchmarkConfig): Promise<BenchmarkResult> {
    if (!this.enabled) {
      throw new Error('基准测试未启用');
    }

    if (this.isTesting) {
      throw new Error('已有基准测试正在运行');
    }

    // 合并配置
    const mergedConfig: BenchmarkConfig = { ...this, ...config };

    try {
      // 设置测试状态
      this.isTesting = true;
      this.currentTest = testName;
      this.performanceSamples = [];

      // 发出基准测试开始事件
      this.eventEmitter.emit(BenchmarkEventType.BENCHMARK_STARTED, testName, mergedConfig);

      // 保存相机原始状态
      this.saveOriginalCameraState();

      // 生成相机路径
      this.generateCameraPath(mergedConfig.cameraPathType!);

      // 配置性能监控器
      this.performanceMonitor.setConfig({
        enabled: true,
        updateFrequency: mergedConfig.sampleInterval,
        sampleSize: Math.ceil(mergedConfig.testDuration! / mergedConfig.sampleInterval!),
        recordHistory: true,
        maxHistoryLength: Math.ceil(mergedConfig.testDuration! / mergedConfig.sampleInterval!),
        enableWarnings: false,
        enableBottleneckDetection: false
      });

      // 开始性能监控
      this.performanceMonitor.start();

      // 预热
      await this.warmup(mergedConfig.warmupDuration!);

      // 开始测试
      this.testStartTime = performance.now();

      // 设置采样定时器
      this.sampleTimerId = window.setInterval(() => {
        this.collectSample();
      }, mergedConfig.sampleInterval);

      // 设置测试定时器
      this.testTimerId = window.setTimeout(() => {
        this.finishTest();
      }, mergedConfig.testDuration);

      // 等待测试完成
      return new Promise<BenchmarkResult>((resolve) => {
        this.eventEmitter.once(BenchmarkEventType.BENCHMARK_COMPLETED, (result: BenchmarkResult) => {
          resolve(result);
        });
      });
    } catch (error) {
      // 发出基准测试错误事件
      this.eventEmitter.emit(BenchmarkEventType.BENCHMARK_ERROR, error);
      Debug.error('TerrainPerformanceBenchmark', '运行基准测试失败:', error);
      this.cleanup();
      throw error;
    }
  }

  /**
   * 保存相机原始状态
   */
  private saveOriginalCameraState(): void {
    if (!this.camera) {
      return;
    }

    const threeCamera = this.camera.getThreeCamera();
    this.originalCameraPosition = threeCamera.position.clone();
    this.originalCameraRotation = threeCamera.rotation.clone();
  }

  /**
   * 恢复相机原始状态
   */
  private restoreOriginalCameraState(): void {
    if (!this.camera || !this.originalCameraPosition || !this.originalCameraRotation) {
      return;
    }

    const threeCamera = this.camera.getThreeCamera();
    threeCamera.position.copy(this.originalCameraPosition);
    threeCamera.rotation.copy(this.originalCameraRotation);
  }

  /**
   * 生成相机路径
   * @param pathType 路径类型
   */
  private generateCameraPath(pathType: CameraPathType): void {
    // TODO: 实现相机路径生成逻辑
    // 这里应该根据路径类型生成相机路径点
  }

  /**
   * 预热
   * @param duration 预热时间（毫秒）
   */
  private async warmup(duration: number): Promise<void> {
    // TODO: 实现预热逻辑
    // 这里应该进行预热，让系统稳定下来
    return new Promise<void>((resolve) => {
      setTimeout(resolve, duration);
    });
  }

  /**
   * 收集样本
   */
  private collectSample(): void {
    if (!this.performanceMonitor) {
      return;
    }

    // 获取性能数据
    const data = this.performanceMonitor.getPerformanceData();
    this.performanceSamples.push(data);

    // 更新相机位置
    this.updateCameraPosition();

    // 计算进度
    const elapsed = performance.now() - this.testStartTime;
    const progress = Math.min(elapsed / this.testDuration, 1);

    // 发出基准测试进度事件
    this.eventEmitter.emit(BenchmarkEventType.BENCHMARK_PROGRESS, progress, data);
  }

  /**
   * 更新相机位置
   */
  private updateCameraPosition(): void {
    // TODO: 实现相机位置更新逻辑
    // 这里应该根据相机路径更新相机位置
  }

  /**
   * 完成测试
   */
  private finishTest(): void {
    // 清除定时器
    if (this.sampleTimerId !== null) {
      window.clearInterval(this.sampleTimerId);
      this.sampleTimerId = null;
    }

    if (this.testTimerId !== null) {
      window.clearTimeout(this.testTimerId);
      this.testTimerId = null;
    }

    // 停止性能监控
    this.performanceMonitor.stop();

    // 恢复相机状态
    this.restoreOriginalCameraState();

    // 计算结果
    const result = this.calculateResult();

    // 存储结果
    this.testResults.set(this.currentTest!, result);

    // 清理状态
    this.cleanup();

    // 发出基准测试完成事件
    this.eventEmitter.emit(BenchmarkEventType.BENCHMARK_COMPLETED, result);
  }

  /**
   * 计算结果
   * @returns 基准测试结果
   */
  private calculateResult(): BenchmarkResult {
    // 计算统计数据
    const fpsList = this.performanceSamples.map(s => s.fps);
    const averageFPS = this.calculateAverage(fpsList);
    const minFPS = Math.min(...fpsList);
    const maxFPS = Math.max(...fpsList);
    const fpsStdDev = this.calculateStandardDeviation(fpsList);

    // 创建结果
    const result: BenchmarkResult = {
      name: this.currentTest!,
      duration: this.testDuration,
      averageFPS,
      minFPS,
      maxFPS,
      fpsStdDev,
      averageFrameTime: this.calculateAverage(this.performanceSamples.map(s => s.frameTime)),
      averageCPUUsage: this.calculateAverage(this.performanceSamples.map(s => s.cpuUsage)),
      averageGPUUsage: this.calculateAverage(this.performanceSamples.map(s => s.gpuUsage)),
      averageMemoryUsage: this.calculateAverage(this.performanceSamples.map(s => s.memoryUsage)),
      averageRenderTime: this.calculateAverage(this.performanceSamples.map(s => s.renderTime)),
      averagePhysicsTime: this.calculateAverage(this.performanceSamples.map(s => s.physicsTime)),
      averageVisibleTerrainChunks: this.calculateAverage(this.performanceSamples.map(s => s.visibleTerrainChunks)),
      averageTerrainTriangles: this.calculateAverage(this.performanceSamples.map(s => s.terrainTriangles)),
      averageTerrainVertices: this.calculateAverage(this.performanceSamples.map(s => s.terrainVertices)),
      averageTerrainTextureMemory: this.calculateAverage(this.performanceSamples.map(s => s.terrainTextureMemory)),
      averageTerrainGeometryMemory: this.calculateAverage(this.performanceSamples.map(s => s.terrainGeometryMemory)),
      averageTerrainPhysicsMemory: this.calculateAverage(this.performanceSamples.map(s => s.terrainPhysicsMemory)),
      samples: this.performanceSamples,
      config: {
        testDuration: this.testDuration,
        warmupDuration: this.warmupDuration,
        sampleInterval: this.sampleInterval,
        cameraPathType: this.cameraPathType,
        testLOD: this.testLOD,
        testFrustumCulling: this.testFrustumCulling,
        testTextureStreaming: this.testTextureStreaming,
        testVirtualTexturing: this.testVirtualTexturing,
        testGeometryCompression: this.testGeometryCompression,
        testPhysicsLOD: this.testPhysicsLOD
      },
      timestamp: Date.now()
    };

    return result;
  }

  /**
   * 计算平均值
   * @param values 值数组
   * @returns 平均值
   */
  private calculateAverage(values: number[]): number {
    if (values.length === 0) {
      return 0;
    }
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  }

  /**
   * 计算标准差
   * @param values 值数组
   * @returns 标准差
   */
  private calculateStandardDeviation(values: number[]): number {
    if (values.length === 0) {
      return 0;
    }
    const avg = this.calculateAverage(values);
    const squareDiffs = values.map(value => {
      const diff = value - avg;
      return diff * diff;
    });
    const avgSquareDiff = this.calculateAverage(squareDiffs);
    return Math.sqrt(avgSquareDiff);
  }

  /**
   * 清理
   */
  private cleanup(): void {
    this.isTesting = false;
    this.currentTest = null;
    this.originalCameraPosition = null;
    this.originalCameraRotation = null;
    this.cameraPathPoints = [];
    this.cameraPathTime = 0;
    this.testStartTime = 0;
  }

  /**
   * 获取测试结果
   * @param testName 测试名称
   * @returns 测试结果
   */
  public getTestResult(testName: string): BenchmarkResult | null {
    return this.testResults.get(testName) || null;
  }

  /**
   * 获取所有测试结果
   * @returns 测试结果映射
   */
  public getAllTestResults(): Map<string, BenchmarkResult> {
    return new Map(this.testResults);
  }

  /**
   * 清除测试结果
   * @param testName 测试名称
   */
  public clearTestResult(testName: string): void {
    this.testResults.delete(testName);
  }

  /**
   * 清除所有测试结果
   */
  public clearAllTestResults(): void {
    this.testResults.clear();
  }

  /**
   * 注册事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: BenchmarkEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: BenchmarkEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
