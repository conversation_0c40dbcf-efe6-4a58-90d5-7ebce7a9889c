/**
 * 日志拦截器
 */
import { Injectable, NestInterceptor, ExecutionContext, CallHandler, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest();
    const { method, url, body, ip, headers } = req;
    const userAgent = headers['user-agent'] || '';
    const userId = req.user?.id || 'anonymous';

    const now = Date.now();
    this.logger.log(
      `[${userId}] ${method} ${url} ${ip} ${userAgent} - Request Body: ${JSON.stringify(body)}`,
    );

    return next.handle().pipe(
      tap({
        next: (data) => {
          const responseTime = Date.now() - now;
          this.logger.log(
            `[${userId}] ${method} ${url} ${ip} ${responseTime}ms - Response: ${
              data ? JSON.stringify(data).substring(0, 100) + '...' : 'no data'
            }`,
          );
        },
        error: (error) => {
          const responseTime = Date.now() - now;
          this.logger.error(
            `[${userId}] ${method} ${url} ${ip} ${responseTime}ms - Error: ${error.message}`,
          );
        },
      }),
    );
  }
}
