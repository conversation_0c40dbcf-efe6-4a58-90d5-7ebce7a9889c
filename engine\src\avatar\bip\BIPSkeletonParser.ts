/**
 * BIP骨骼解析器
 * 解析BIP格式的骨骼文件
 */
import * as THREE from 'three';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * BIP骨骼节点
 */
export interface BIPBone {
  /** 骨骼名称 */
  name: string;
  /** 骨骼ID */
  id: number;
  /** 父骨骼ID */
  parentId: number;
  /** 本地位置 */
  position: THREE.Vector3;
  /** 本地旋转 */
  rotation: THREE.Quaternion;
  /** 本地缩放 */
  scale: THREE.Vector3;
  /** 世界变换矩阵 */
  worldMatrix: THREE.Matrix4;
  /** 绑定姿势矩阵 */
  bindMatrix: THREE.Matrix4;
  /** 子骨骼ID列表 */
  children: number[];
  /** 骨骼长度 */
  length: number;
  /** 骨骼类型 */
  type: BIPBoneType;
  /** 自由度限制 */
  dofLimits?: DOFLimits;
}

/**
 * BIP骨骼类型
 */
export enum BIPBoneType {
  ROOT = 'root',
  SPINE = 'spine',
  HEAD = 'head',
  ARM = 'arm',
  LEG = 'leg',
  HAND = 'hand',
  FOOT = 'foot',
  FINGER = 'finger',
  TOE = 'toe',
  AUXILIARY = 'auxiliary'
}

/**
 * 自由度限制
 */
export interface DOFLimits {
  /** X轴旋转限制 */
  rotationX: { min: number; max: number };
  /** Y轴旋转限制 */
  rotationY: { min: number; max: number };
  /** Z轴旋转限制 */
  rotationZ: { min: number; max: number };
  /** 位置限制 */
  position?: {
    x: { min: number; max: number };
    y: { min: number; max: number };
    z: { min: number; max: number };
  };
}

/**
 * BIP骨骼数据
 */
export interface BIPSkeletonData {
  /** 版本信息 */
  version: string;
  /** 骨骼列表 */
  bones: BIPBone[];
  /** 根骨骼ID */
  rootBoneId: number;
  /** 骨骼映射表 */
  boneMap: Map<string, number>;
  /** 层级结构 */
  hierarchy: BIPBoneHierarchy;
  /** 元数据 */
  metadata: {
    creator?: string;
    created?: Date;
    modified?: Date;
    description?: string;
    units?: string;
    frameRate?: number;
  };
}

/**
 * BIP骨骼层级结构
 */
export interface BIPBoneHierarchy {
  /** 根节点 */
  root: BIPBoneNode;
  /** 深度映射 */
  depthMap: Map<number, number>;
  /** 最大深度 */
  maxDepth: number;
}

/**
 * BIP骨骼节点
 */
export interface BIPBoneNode {
  /** 骨骼ID */
  boneId: number;
  /** 子节点 */
  children: BIPBoneNode[];
  /** 深度 */
  depth: number;
}

/**
 * BIP解析配置
 */
export interface BIPParserConfig {
  /** 是否验证骨骼结构 */
  validateStructure?: boolean;
  /** 是否计算世界变换 */
  calculateWorldTransforms?: boolean;
  /** 是否生成层级结构 */
  generateHierarchy?: boolean;
  /** 单位转换比例 */
  unitScale?: number;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * BIP骨骼解析器
 */
export class BIPSkeletonParser extends EventEmitter {
  /** 配置 */
  private config: BIPParserConfig;

  /** 标准BIP骨骼名称映射 */
  private static readonly STANDARD_BONE_NAMES = {
    // 躯干
    'Bip01': BIPBoneType.ROOT,
    'Bip01 Pelvis': BIPBoneType.ROOT,
    'Bip01 Spine': BIPBoneType.SPINE,
    'Bip01 Spine1': BIPBoneType.SPINE,
    'Bip01 Spine2': BIPBoneType.SPINE,
    'Bip01 Spine3': BIPBoneType.SPINE,
    'Bip01 Neck': BIPBoneType.SPINE,
    'Bip01 Head': BIPBoneType.HEAD,

    // 左臂
    'Bip01 L Clavicle': BIPBoneType.ARM,
    'Bip01 L UpperArm': BIPBoneType.ARM,
    'Bip01 L Forearm': BIPBoneType.ARM,
    'Bip01 L Hand': BIPBoneType.HAND,

    // 右臂
    'Bip01 R Clavicle': BIPBoneType.ARM,
    'Bip01 R UpperArm': BIPBoneType.ARM,
    'Bip01 R Forearm': BIPBoneType.ARM,
    'Bip01 R Hand': BIPBoneType.HAND,

    // 左腿
    'Bip01 L Thigh': BIPBoneType.LEG,
    'Bip01 L Calf': BIPBoneType.LEG,
    'Bip01 L Foot': BIPBoneType.FOOT,
    'Bip01 L Toe0': BIPBoneType.TOE,

    // 右腿
    'Bip01 R Thigh': BIPBoneType.LEG,
    'Bip01 R Calf': BIPBoneType.LEG,
    'Bip01 R Foot': BIPBoneType.FOOT,
    'Bip01 R Toe0': BIPBoneType.TOE,

    // 左手指
    'Bip01 L Finger0': BIPBoneType.FINGER,
    'Bip01 L Finger01': BIPBoneType.FINGER,
    'Bip01 L Finger02': BIPBoneType.FINGER,
    'Bip01 L Finger1': BIPBoneType.FINGER,
    'Bip01 L Finger11': BIPBoneType.FINGER,
    'Bip01 L Finger12': BIPBoneType.FINGER,
    'Bip01 L Finger2': BIPBoneType.FINGER,
    'Bip01 L Finger21': BIPBoneType.FINGER,
    'Bip01 L Finger22': BIPBoneType.FINGER,
    'Bip01 L Finger3': BIPBoneType.FINGER,
    'Bip01 L Finger31': BIPBoneType.FINGER,
    'Bip01 L Finger32': BIPBoneType.FINGER,
    'Bip01 L Finger4': BIPBoneType.FINGER,
    'Bip01 L Finger41': BIPBoneType.FINGER,
    'Bip01 L Finger42': BIPBoneType.FINGER,

    // 右手指
    'Bip01 R Finger0': BIPBoneType.FINGER,
    'Bip01 R Finger01': BIPBoneType.FINGER,
    'Bip01 R Finger02': BIPBoneType.FINGER,
    'Bip01 R Finger1': BIPBoneType.FINGER,
    'Bip01 R Finger11': BIPBoneType.FINGER,
    'Bip01 R Finger12': BIPBoneType.FINGER,
    'Bip01 R Finger2': BIPBoneType.FINGER,
    'Bip01 R Finger21': BIPBoneType.FINGER,
    'Bip01 R Finger22': BIPBoneType.FINGER,
    'Bip01 R Finger3': BIPBoneType.FINGER,
    'Bip01 R Finger31': BIPBoneType.FINGER,
    'Bip01 R Finger32': BIPBoneType.FINGER,
    'Bip01 R Finger4': BIPBoneType.FINGER,
    'Bip01 R Finger41': BIPBoneType.FINGER,
    'Bip01 R Finger42': BIPBoneType.FINGER
  };

  /**
   * 构造函数
   * @param config 解析配置
   */
  constructor(config: BIPParserConfig = {}) {
    super();

    this.config = {
      validateStructure: true,
      calculateWorldTransforms: true,
      generateHierarchy: true,
      unitScale: 1.0,
      debug: false,
      ...config
    };
  }

  /**
   * 解析BIP文件
   * @param fileData 文件数据
   * @returns Promise<BIPSkeletonData>
   */
  public async parseBIPFile(fileData: ArrayBuffer | string): Promise<BIPSkeletonData> {
    try {
      if (this.config.debug) {
        console.log('[BIPSkeletonParser] 开始解析BIP文件');
      }

      this.emit('parseStarted');

      // 解析文件内容
      const rawData = this.parseFileContent(fileData);

      // 提取骨骼数据
      const bones = this.extractBones(rawData);

      // 构建骨骼映射
      const boneMap = this.buildBoneMap(bones);

      // 查找根骨骼
      const rootBoneId = this.findRootBone(bones);

      // 计算世界变换
      if (this.config.calculateWorldTransforms) {
        this.calculateWorldTransforms(bones, rootBoneId);
      }

      // 生成层级结构
      let hierarchy: BIPBoneHierarchy | undefined;
      if (this.config.generateHierarchy) {
        hierarchy = this.generateHierarchy(bones, rootBoneId);
      }

      // 验证骨骼结构
      if (this.config.validateStructure) {
        this.validateSkeletonStructure(bones, rootBoneId);
      }

      const skeletonData: BIPSkeletonData = {
        version: rawData.version || '1.0',
        bones,
        rootBoneId,
        boneMap,
        hierarchy: hierarchy!,
        metadata: rawData.metadata || {}
      };

      this.emit('parseCompleted', skeletonData);

      if (this.config.debug) {
        console.log(`[BIPSkeletonParser] BIP文件解析完成，共 ${bones.length} 个骨骼`);
      }

      return skeletonData;
    } catch (error) {
      this.emit('parseError', error);
      throw error;
    }
  }

  /**
   * 解析文件内容
   * @param fileData 文件数据
   * @returns 原始数据
   */
  private parseFileContent(fileData: ArrayBuffer | string): any {
    // TODO: 实现实际的BIP文件格式解析
    // BIP文件可能是二进制格式或文本格式
    // 这里提供一个简化的实现

    if (typeof fileData === 'string') {
      // 文本格式BIP文件
      return this.parseTextBIP(fileData);
    } else {
      // 二进制格式BIP文件
      return this.parseBinaryBIP(fileData);
    }
  }

  /**
   * 解析文本格式BIP
   * @param textData 文本数据
   * @returns 解析结果
   */
  private parseTextBIP(textData: string): any {
    // 简化的文本BIP解析
    const lines = textData.split('\n');
    const bones: any[] = [];
    let currentBone: any = null;

    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed.startsWith('BONE')) {
        if (currentBone) {
          bones.push(currentBone);
        }
        currentBone = {
          name: trimmed.split(' ')[1],
          id: bones.length,
          parentId: -1,
          position: new THREE.Vector3(),
          rotation: new THREE.Quaternion(),
          scale: new THREE.Vector3(1, 1, 1)
        };
      } else if (trimmed.startsWith('PARENT') && currentBone) {
        const parentName = trimmed.split(' ')[1];
        // 查找父骨骼ID
        const parentBone = bones.find(b => b.name === parentName);
        if (parentBone) {
          currentBone.parentId = parentBone.id;
        }
      } else if (trimmed.startsWith('POSITION') && currentBone) {
        const coords = trimmed.split(' ').slice(1).map(Number);
        currentBone.position.set(coords[0], coords[1], coords[2]);
      } else if (trimmed.startsWith('ROTATION') && currentBone) {
        const coords = trimmed.split(' ').slice(1).map(Number);
        currentBone.rotation.set(coords[0], coords[1], coords[2], coords[3]);
      }
    }

    if (currentBone) {
      bones.push(currentBone);
    }

    return {
      version: '1.0',
      bones,
      metadata: {}
    };
  }

  /**
   * 解析二进制格式BIP
   * @param binaryData 二进制数据
   * @returns 解析结果
   */
  private parseBinaryBIP(binaryData: ArrayBuffer): any {
    // TODO: 实现二进制BIP文件解析
    // 这需要根据具体的BIP二进制格式规范来实现
    
    // 简化实现：创建默认骨骼结构
    return this.createDefaultBIPStructure();
  }

  /**
   * 创建默认BIP结构
   * @returns 默认BIP数据
   */
  private createDefaultBIPStructure(): any {
    const bones = [
      { name: 'Bip01', id: 0, parentId: -1 },
      { name: 'Bip01 Pelvis', id: 1, parentId: 0 },
      { name: 'Bip01 Spine', id: 2, parentId: 1 },
      { name: 'Bip01 Spine1', id: 3, parentId: 2 },
      { name: 'Bip01 Neck', id: 4, parentId: 3 },
      { name: 'Bip01 Head', id: 5, parentId: 4 },
      { name: 'Bip01 L Clavicle', id: 6, parentId: 3 },
      { name: 'Bip01 L UpperArm', id: 7, parentId: 6 },
      { name: 'Bip01 L Forearm', id: 8, parentId: 7 },
      { name: 'Bip01 L Hand', id: 9, parentId: 8 },
      { name: 'Bip01 R Clavicle', id: 10, parentId: 3 },
      { name: 'Bip01 R UpperArm', id: 11, parentId: 10 },
      { name: 'Bip01 R Forearm', id: 12, parentId: 11 },
      { name: 'Bip01 R Hand', id: 13, parentId: 12 },
      { name: 'Bip01 L Thigh', id: 14, parentId: 1 },
      { name: 'Bip01 L Calf', id: 15, parentId: 14 },
      { name: 'Bip01 L Foot', id: 16, parentId: 15 },
      { name: 'Bip01 R Thigh', id: 17, parentId: 1 },
      { name: 'Bip01 R Calf', id: 18, parentId: 17 },
      { name: 'Bip01 R Foot', id: 19, parentId: 18 }
    ];

    // 为每个骨骼添加默认变换
    bones.forEach((bone: any) => {
      bone.position = new THREE.Vector3();
      bone.rotation = new THREE.Quaternion();
      bone.scale = new THREE.Vector3(1, 1, 1);
    });

    return {
      version: '1.0',
      bones,
      metadata: {
        creator: 'BIPSkeletonParser',
        created: new Date(),
        description: 'Default BIP skeleton structure'
      }
    };
  }

  /**
   * 提取骨骼数据
   * @param rawData 原始数据
   * @returns 骨骼数组
   */
  private extractBones(rawData: any): BIPBone[] {
    const bones: BIPBone[] = [];

    for (const boneData of rawData.bones) {
      const bone: BIPBone = {
        name: boneData.name,
        id: boneData.id,
        parentId: boneData.parentId,
        position: boneData.position || new THREE.Vector3(),
        rotation: boneData.rotation || new THREE.Quaternion(),
        scale: boneData.scale || new THREE.Vector3(1, 1, 1),
        worldMatrix: new THREE.Matrix4(),
        bindMatrix: new THREE.Matrix4(),
        children: [],
        length: this.calculateBoneLength(boneData),
        type: this.determineBoneType(boneData.name),
        dofLimits: this.getDefaultDOFLimits(boneData.name)
      };

      bones.push(bone);
    }

    // 构建子骨骼关系
    for (const bone of bones) {
      if (bone.parentId >= 0) {
        const parent = bones.find(b => b.id === bone.parentId);
        if (parent) {
          parent.children.push(bone.id);
        }
      }
    }

    return bones;
  }

  /**
   * 计算骨骼长度
   * @param boneData 骨骼数据
   * @returns 骨骼长度
   */
  private calculateBoneLength(boneData: any): number {
    // TODO: 根据子骨骼位置计算实际长度
    // 这里返回默认长度
    return 1.0;
  }

  /**
   * 确定骨骼类型
   * @param boneName 骨骼名称
   * @returns 骨骼类型
   */
  private determineBoneType(boneName: string): BIPBoneType {
    return BIPSkeletonParser.STANDARD_BONE_NAMES[boneName] || BIPBoneType.AUXILIARY;
  }

  /**
   * 获取默认自由度限制
   * @param boneName 骨骼名称
   * @returns 自由度限制
   */
  private getDefaultDOFLimits(boneName: string): DOFLimits {
    // 根据骨骼类型返回合理的自由度限制
    const boneType = this.determineBoneType(boneName);

    switch (boneType) {
      case BIPBoneType.SPINE:
        return {
          rotationX: { min: -30, max: 30 },
          rotationY: { min: -45, max: 45 },
          rotationZ: { min: -30, max: 30 }
        };
      case BIPBoneType.HEAD:
        return {
          rotationX: { min: -60, max: 60 },
          rotationY: { min: -90, max: 90 },
          rotationZ: { min: -45, max: 45 }
        };
      case BIPBoneType.ARM:
        return {
          rotationX: { min: -180, max: 180 },
          rotationY: { min: -90, max: 90 },
          rotationZ: { min: -180, max: 180 }
        };
      case BIPBoneType.LEG:
        return {
          rotationX: { min: -120, max: 120 },
          rotationY: { min: -45, max: 45 },
          rotationZ: { min: -30, max: 30 }
        };
      default:
        return {
          rotationX: { min: -180, max: 180 },
          rotationY: { min: -180, max: 180 },
          rotationZ: { min: -180, max: 180 }
        };
    }
  }

  /**
   * 构建骨骼映射
   * @param bones 骨骼数组
   * @returns 骨骼映射
   */
  private buildBoneMap(bones: BIPBone[]): Map<string, number> {
    const boneMap = new Map<string, number>();

    for (const bone of bones) {
      boneMap.set(bone.name, bone.id);
    }

    return boneMap;
  }

  /**
   * 查找根骨骼
   * @param bones 骨骼数组
   * @returns 根骨骼ID
   */
  private findRootBone(bones: BIPBone[]): number {
    const rootBone = bones.find(bone => bone.parentId === -1);
    if (!rootBone) {
      throw new Error('未找到根骨骼');
    }
    return rootBone.id;
  }

  /**
   * 计算世界变换
   * @param bones 骨骼数组
   * @param rootBoneId 根骨骼ID
   */
  private calculateWorldTransforms(bones: BIPBone[], rootBoneId: number): void {
    const boneById = new Map<number, BIPBone>();
    for (const bone of bones) {
      boneById.set(bone.id, bone);
    }

    // 递归计算世界变换
    const calculateTransform = (boneId: number, parentWorldMatrix?: THREE.Matrix4) => {
      const bone = boneById.get(boneId);
      if (!bone) return;

      // 构建本地变换矩阵
      const localMatrix = new THREE.Matrix4();
      localMatrix.compose(bone.position, bone.rotation, bone.scale);

      // 计算世界变换矩阵
      if (parentWorldMatrix) {
        bone.worldMatrix.multiplyMatrices(parentWorldMatrix, localMatrix);
      } else {
        bone.worldMatrix.copy(localMatrix);
      }

      // 计算绑定姿势矩阵（世界变换的逆矩阵）
      bone.bindMatrix.copy(bone.worldMatrix).invert();

      // 递归处理子骨骼
      for (const childId of bone.children) {
        calculateTransform(childId, bone.worldMatrix);
      }
    };

    calculateTransform(rootBoneId);
  }

  /**
   * 生成层级结构
   * @param bones 骨骼数组
   * @param rootBoneId 根骨骼ID
   * @returns 层级结构
   */
  private generateHierarchy(bones: BIPBone[], rootBoneId: number): BIPBoneHierarchy {
    const depthMap = new Map<number, number>();
    let maxDepth = 0;

    // 递归构建层级节点
    const buildNode = (boneId: number, depth: number): BIPBoneNode => {
      const bone = bones.find(b => b.id === boneId);
      if (!bone) {
        throw new Error(`骨骼不存在: ${boneId}`);
      }

      depthMap.set(boneId, depth);
      maxDepth = Math.max(maxDepth, depth);

      const node: BIPBoneNode = {
        boneId,
        children: [],
        depth
      };

      // 递归构建子节点
      for (const childId of bone.children) {
        node.children.push(buildNode(childId, depth + 1));
      }

      return node;
    };

    const root = buildNode(rootBoneId, 0);

    return {
      root,
      depthMap,
      maxDepth
    };
  }

  /**
   * 验证骨骼结构
   * @param bones 骨骼数组
   * @param rootBoneId 根骨骼ID
   */
  private validateSkeletonStructure(bones: BIPBone[], rootBoneId: number): void {
    // 检查根骨骼
    const rootBone = bones.find(b => b.id === rootBoneId);
    if (!rootBone) {
      throw new Error('根骨骼不存在');
    }

    // 检查骨骼连接性
    for (const bone of bones) {
      if (bone.id !== rootBoneId && bone.parentId >= 0) {
        const parent = bones.find(b => b.id === bone.parentId);
        if (!parent) {
          throw new Error(`骨骼 ${bone.name} 的父骨骼不存在`);
        }
      }
    }

    // 检查循环引用
    this.checkCircularReferences(bones, rootBoneId);

    if (this.config.debug) {
      console.log('[BIPSkeletonParser] 骨骼结构验证通过');
    }
  }

  /**
   * 检查循环引用
   * @param bones 骨骼数组
   * @param rootBoneId 根骨骼ID
   */
  private checkCircularReferences(bones: BIPBone[], rootBoneId: number): void {
    const visited = new Set<number>();
    const recursionStack = new Set<number>();

    const dfs = (boneId: number): boolean => {
      if (recursionStack.has(boneId)) {
        return true; // 发现循环
      }

      if (visited.has(boneId)) {
        return false;
      }

      visited.add(boneId);
      recursionStack.add(boneId);

      const bone = bones.find(b => b.id === boneId);
      if (bone) {
        for (const childId of bone.children) {
          if (dfs(childId)) {
            return true;
          }
        }
      }

      recursionStack.delete(boneId);
      return false;
    };

    if (dfs(rootBoneId)) {
      throw new Error('检测到骨骼层级中的循环引用');
    }
  }

  /**
   * 销毁解析器
   */
  public dispose(): void {
    this.removeAllListeners();

    if (this.config.debug) {
      console.log('[BIPSkeletonParser] 解析器已销毁');
    }
  }
}
