/**
 * 输入性能工具
 * 提供事件节流、防抖等性能优化功能
 */

/**
 * 节流选项
 */
export interface ThrottleOptions {
  /** 节流时间间隔（毫秒） */
  interval: number;
  /** 是否在开始时执行 */
  leading?: boolean;
  /** 是否在结束时执行 */
  trailing?: boolean;
}

/**
 * 防抖选项
 */
export interface DebounceOptions {
  /** 防抖等待时间（毫秒） */
  wait: number;
  /** 是否在开始时执行 */
  leading?: boolean;
  /** 是否在结束时执行 */
  trailing?: boolean;
  /** 最大等待时间（毫秒） */
  maxWait?: number;
}

/**
 * 节流函数
 * 限制函数在一定时间内只能执行一次
 * @param func 要节流的函数
 * @param options 节流选项
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  options: ThrottleOptions
): (...args: Parameters<T>) => ReturnType<T> | undefined {
  const { interval, leading = true, trailing = true } = options;
  let lastTime = 0;
  let timer: number | null = null;
  let lastArgs: Parameters<T> | null = null;
  let lastResult: ReturnType<T>;

  // 清除定时器
  const clearTimer = () => {
    if (timer !== null) {
      clearTimeout(timer);
      timer = null;
    }
  };

  // 执行函数
  const invoke = (time: number, args: Parameters<T>): ReturnType<T> => {
    lastTime = time;
    lastResult = func(...args);
    return lastResult;
  };

  // 返回节流后的函数
  return function(this: any, ...args: Parameters<T>): ReturnType<T> | undefined {
    const now = Date.now();
    lastArgs = args;

    // 如果是第一次调用，且允许在开始时执行
    if (lastTime === 0 && !leading) {
      lastTime = now;
    }

    // 计算剩余时间
    const remaining = interval - (now - lastTime);

    // 如果已经过了间隔时间，或者是第一次调用且允许在开始时执行
    if (remaining <= 0 || remaining > interval) {
      clearTimer();
      return invoke(now, args);
    }

    // 如果允许在结束时执行，且没有设置定时器
    if (trailing && timer === null) {
      timer = window.setTimeout(() => {
        timer = null;
        if (lastArgs) {
          invoke(leading ? Date.now() : 0, lastArgs);
          lastArgs = null;
        }
      }, remaining);
    }

    return lastResult;
  };
}

/**
 * 防抖函数
 * 将多次连续调用合并为一次
 * @param func 要防抖的函数
 * @param options 防抖选项
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  options: DebounceOptions
): (...args: Parameters<T>) => ReturnType<T> | undefined {
  const { wait, leading = false, trailing = true, maxWait } = options;
  let timer: number | null = null;
  let lastCallTime = 0;
  let lastInvokeTime = 0;
  let lastArgs: Parameters<T> | null = null;
  let lastThis: any = null;
  let lastResult: ReturnType<T>;

  // 计算是否应该调用函数
  const shouldInvoke = (time: number): boolean => {
    const timeSinceLastCall = time - lastCallTime;
    const timeSinceLastInvoke = time - lastInvokeTime;

    // 如果是第一次调用，或者已经过了等待时间，或者已经超过最大等待时间
    return (
      lastCallTime === 0 ||
      timeSinceLastCall >= wait ||
      (maxWait !== undefined && timeSinceLastInvoke >= maxWait)
    );
  };

  // 执行函数
  const invokeFunc = (time: number): ReturnType<T> => {
    const args = lastArgs!;
    const thisArg = lastThis;

    lastArgs = null;
    lastThis = null;
    lastInvokeTime = time;

    lastResult = func.apply(thisArg, args);
    return lastResult;
  };

  // 启动定时器
  const startTimer = (pendingFunc: () => void, delay: number): number => {
    return window.setTimeout(pendingFunc, delay);
  };

  // 取消定时器
  const cancelTimer = (id: number | null): void => {
    if (id !== null) {
      clearTimeout(id);
    }
  };

  // 执行延迟函数
  const timerExpired = (): void => {
    const time = Date.now();

    // 如果应该调用函数
    if (shouldInvoke(time)) {
      return trailingEdge(time);
    }

    // 否则重新启动定时器
    timer = startTimer(timerExpired, remainingWait(time));
  };

  // 计算剩余等待时间
  const remainingWait = (time: number): number => {
    const timeSinceLastCall = time - lastCallTime;
    const timeSinceLastInvoke = time - lastInvokeTime;
    const timeWaiting = wait - timeSinceLastCall;

    // 如果设置了最大等待时间，则取最小值
    return maxWait !== undefined
      ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting;
  };

  // 前沿执行
  const leadingEdge = (time: number): ReturnType<T> | undefined => {
    lastInvokeTime = time;

    // 如果允许在开始时执行
    if (leading) {
      return invokeFunc(time);
    }

    // 否则启动定时器
    timer = startTimer(timerExpired, wait);
    return undefined;
  };

  // 后沿执行
  const trailingEdge = (time: number): ReturnType<T> | undefined => {
    timer = null;

    // 如果有参数且允许在结束时执行
    if (trailing && lastArgs) {
      return invokeFunc(time);
    }

    lastArgs = null;
    lastThis = null;
    return undefined;
  };

  // 返回防抖后的函数
  return function(this: any, ...args: Parameters<T>): ReturnType<T> | undefined {
    const time = Date.now();
    const isInvoking = shouldInvoke(time);

    lastArgs = args;
    lastThis = this;
    lastCallTime = time;

    // 如果是第一次调用或者应该调用函数
    if (isInvoking) {
      if (timer === null) {
        return leadingEdge(time);
      }

      // 如果设置了最大等待时间
      if (maxWait !== undefined) {
        cancelTimer(timer);
        timer = startTimer(timerExpired, wait);
        return invokeFunc(time);
      }
    }

    // 如果没有定时器，则启动定时器
    if (timer === null) {
      timer = startTimer(timerExpired, wait);
    }

    return lastResult;
  };
}
