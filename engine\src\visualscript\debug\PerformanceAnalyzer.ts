/**
 * 性能分析器
 * 分析视觉脚本的性能
 */
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 节点性能数据
 */
export interface NodePerformanceData {
  /** 节点ID */
  nodeId: string;
  
  /** 节点类型 */
  nodeType?: string;
  
  /** 节点标签 */
  nodeLabel?: string;
  
  /** 执行次数 */
  executionCount: number;
  
  /** 总执行时间（毫秒） */
  totalExecutionTime: number;
  
  /** 最小执行时间（毫秒） */
  minExecutionTime: number;
  
  /** 最大执行时间（毫秒） */
  maxExecutionTime: number;
  
  /** 平均执行时间（毫秒） */
  averageExecutionTime: number;
  
  /** 最后执行时间（毫秒） */
  lastExecutionTime: number;
  
  /** 最后执行时间戳 */
  lastExecutionTimestamp: number;
  
  /** 执行时间历史 */
  executionTimeHistory: number[];
  
  /** 是否正在执行 */
  isExecuting: boolean;
  
  /** 执行开始时间戳 */
  executionStartTimestamp?: number;
}

/**
 * 图性能数据
 */
export interface GraphPerformanceData {
  /** 图ID */
  graphId: string;
  
  /** 图名称 */
  graphName?: string;
  
  /** 执行次数 */
  executionCount: number;
  
  /** 总执行时间（毫秒） */
  totalExecutionTime: number;
  
  /** 最小执行时间（毫秒） */
  minExecutionTime: number;
  
  /** 最大执行时间（毫秒） */
  maxExecutionTime: number;
  
  /** 平均执行时间（毫秒） */
  averageExecutionTime: number;
  
  /** 最后执行时间（毫秒） */
  lastExecutionTime: number;
  
  /** 最后执行时间戳 */
  lastExecutionTimestamp: number;
  
  /** 执行时间历史 */
  executionTimeHistory: number[];
  
  /** 是否正在执行 */
  isExecuting: boolean;
  
  /** 执行开始时间戳 */
  executionStartTimestamp?: number;
  
  /** 节点性能数据映射 */
  nodePerformance: Record<string, NodePerformanceData>;
}

/**
 * 性能分析器配置
 */
export interface PerformanceAnalyzerConfig {
  /** 是否启用 */
  enabled?: boolean;
  
  /** 最大历史记录长度 */
  maxHistoryLength?: number;
  
  /** 是否记录节点执行时间 */
  recordNodeExecutionTime?: boolean;
  
  /** 是否记录图执行时间 */
  recordGraphExecutionTime?: boolean;
  
  /** 是否自动清除旧数据 */
  autoClearOldData?: boolean;
  
  /** 自动清除数据的时间阈值（毫秒） */
  autoClearThreshold?: number;
}

/**
 * 性能分析器
 */
export class PerformanceAnalyzer {
  /** 配置 */
  private config: PerformanceAnalyzerConfig;
  
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: PerformanceAnalyzerConfig = {
    enabled: true,
    maxHistoryLength: 100,
    recordNodeExecutionTime: true,
    recordGraphExecutionTime: true,
    autoClearOldData: true,
    autoClearThreshold: 3600000 // 1小时
  };
  
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  
  /** 节点性能数据映射 */
  private nodePerformance: Map<string, NodePerformanceData> = new Map();
  
  /** 图性能数据映射 */
  private graphPerformance: Map<string, GraphPerformanceData> = new Map();
  
  /** 最后清除时间 */
  private lastClearTime: number = Date.now();
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: PerformanceAnalyzerConfig = {}) {
    // 合并配置
    this.config = {
      ...PerformanceAnalyzer.DEFAULT_CONFIG,
      ...config
    };
  }
  
  /**
   * 启用性能分析
   */
  public enable(): void {
    this.config.enabled = true;
    this.eventEmitter.emit('enabled');
  }
  
  /**
   * 禁用性能分析
   */
  public disable(): void {
    this.config.enabled = false;
    this.eventEmitter.emit('disabled');
  }
  
  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.config.enabled === true;
  }
  
  /**
   * 开始节点执行
   * @param nodeId 节点ID
   * @param nodeType 节点类型
   * @param nodeLabel 节点标签
   */
  public startNodeExecution(nodeId: string, nodeType?: string, nodeLabel?: string): void {
    // 如果未启用，直接返回
    if (!this.isEnabled() || !this.config.recordNodeExecutionTime) {
      return;
    }
    
    // 获取当前时间戳
    const now = Date.now();
    
    // 获取节点性能数据
    let nodeData = this.nodePerformance.get(nodeId);
    
    // 如果节点性能数据不存在，创建新的
    if (!nodeData) {
      nodeData = {
        nodeId,
        nodeType,
        nodeLabel,
        executionCount: 0,
        totalExecutionTime: 0,
        minExecutionTime: Number.MAX_VALUE,
        maxExecutionTime: 0,
        averageExecutionTime: 0,
        lastExecutionTime: 0,
        lastExecutionTimestamp: now,
        executionTimeHistory: [],
        isExecuting: false
      };
      
      this.nodePerformance.set(nodeId, nodeData);
    }
    
    // 更新节点类型和标签
    if (nodeType) {
      nodeData.nodeType = nodeType;
    }
    
    if (nodeLabel) {
      nodeData.nodeLabel = nodeLabel;
    }
    
    // 设置执行状态
    nodeData.isExecuting = true;
    nodeData.executionStartTimestamp = now;
    
    // 触发事件
    this.eventEmitter.emit('nodeExecutionStarted', {
      nodeId,
      nodeType,
      nodeLabel,
      timestamp: now
    });
    
    // 检查是否需要清除旧数据
    this.checkAutoClear();
  }
  
  /**
   * 结束节点执行
   * @param nodeId 节点ID
   */
  public endNodeExecution(nodeId: string): void {
    // 如果未启用，直接返回
    if (!this.isEnabled() || !this.config.recordNodeExecutionTime) {
      return;
    }
    
    // 获取当前时间戳
    const now = Date.now();
    
    // 获取节点性能数据
    const nodeData = this.nodePerformance.get(nodeId);
    
    // 如果节点性能数据不存在或未开始执行，直接返回
    if (!nodeData || !nodeData.isExecuting || !nodeData.executionStartTimestamp) {
      return;
    }
    
    // 计算执行时间
    const executionTime = now - nodeData.executionStartTimestamp;
    
    // 更新节点性能数据
    nodeData.executionCount++;
    nodeData.totalExecutionTime += executionTime;
    nodeData.minExecutionTime = Math.min(nodeData.minExecutionTime, executionTime);
    nodeData.maxExecutionTime = Math.max(nodeData.maxExecutionTime, executionTime);
    nodeData.averageExecutionTime = nodeData.totalExecutionTime / nodeData.executionCount;
    nodeData.lastExecutionTime = executionTime;
    nodeData.lastExecutionTimestamp = now;
    nodeData.isExecuting = false;
    nodeData.executionStartTimestamp = undefined;
    
    // 添加到历史记录
    nodeData.executionTimeHistory.push(executionTime);
    
    // 限制历史记录长度
    if (nodeData.executionTimeHistory.length > (this.config.maxHistoryLength || 100)) {
      nodeData.executionTimeHistory.shift();
    }
    
    // 触发事件
    this.eventEmitter.emit('nodeExecutionEnded', {
      nodeId,
      nodeType: nodeData.nodeType,
      nodeLabel: nodeData.nodeLabel,
      executionTime,
      timestamp: now
    });
  }
  
  /**
   * 开始图执行
   * @param graphId 图ID
   * @param graphName 图名称
   */
  public startGraphExecution(graphId: string, graphName?: string): void {
    // 如果未启用，直接返回
    if (!this.isEnabled() || !this.config.recordGraphExecutionTime) {
      return;
    }
    
    // 获取当前时间戳
    const now = Date.now();
    
    // 获取图性能数据
    let graphData = this.graphPerformance.get(graphId);
    
    // 如果图性能数据不存在，创建新的
    if (!graphData) {
      graphData = {
        graphId,
        graphName,
        executionCount: 0,
        totalExecutionTime: 0,
        minExecutionTime: Number.MAX_VALUE,
        maxExecutionTime: 0,
        averageExecutionTime: 0,
        lastExecutionTime: 0,
        lastExecutionTimestamp: now,
        executionTimeHistory: [],
        isExecuting: false,
        nodePerformance: {}
      };
      
      this.graphPerformance.set(graphId, graphData);
    }
    
    // 更新图名称
    if (graphName) {
      graphData.graphName = graphName;
    }
    
    // 设置执行状态
    graphData.isExecuting = true;
    graphData.executionStartTimestamp = now;
    
    // 触发事件
    this.eventEmitter.emit('graphExecutionStarted', {
      graphId,
      graphName,
      timestamp: now
    });
  }
  
  /**
   * 结束图执行
   * @param graphId 图ID
   */
  public endGraphExecution(graphId: string): void {
    // 如果未启用，直接返回
    if (!this.isEnabled() || !this.config.recordGraphExecutionTime) {
      return;
    }
    
    // 获取当前时间戳
    const now = Date.now();
    
    // 获取图性能数据
    const graphData = this.graphPerformance.get(graphId);
    
    // 如果图性能数据不存在或未开始执行，直接返回
    if (!graphData || !graphData.isExecuting || !graphData.executionStartTimestamp) {
      return;
    }
    
    // 计算执行时间
    const executionTime = now - graphData.executionStartTimestamp;
    
    // 更新图性能数据
    graphData.executionCount++;
    graphData.totalExecutionTime += executionTime;
    graphData.minExecutionTime = Math.min(graphData.minExecutionTime, executionTime);
    graphData.maxExecutionTime = Math.max(graphData.maxExecutionTime, executionTime);
    graphData.averageExecutionTime = graphData.totalExecutionTime / graphData.executionCount;
    graphData.lastExecutionTime = executionTime;
    graphData.lastExecutionTimestamp = now;
    graphData.isExecuting = false;
    graphData.executionStartTimestamp = undefined;
    
    // 添加到历史记录
    graphData.executionTimeHistory.push(executionTime);
    
    // 限制历史记录长度
    if (graphData.executionTimeHistory.length > (this.config.maxHistoryLength || 100)) {
      graphData.executionTimeHistory.shift();
    }
    
    // 触发事件
    this.eventEmitter.emit('graphExecutionEnded', {
      graphId,
      graphName: graphData.graphName,
      executionTime,
      timestamp: now
    });
  }
  
  /**
   * 获取节点执行时间统计
   * @returns 节点执行时间统计
   */
  public getNodeExecutionTimeStatistics(): Record<string, NodePerformanceData> {
    const result: Record<string, NodePerformanceData> = {};
    
    for (const [nodeId, nodeData] of this.nodePerformance.entries()) {
      result[nodeId] = { ...nodeData };
    }
    
    return result;
  }
  
  /**
   * 获取图执行时间统计
   * @returns 图执行时间统计
   */
  public getGraphExecutionTimeStatistics(): Record<string, GraphPerformanceData> {
    const result: Record<string, GraphPerformanceData> = {};
    
    for (const [graphId, graphData] of this.graphPerformance.entries()) {
      result[graphId] = { ...graphData };
    }
    
    return result;
  }
  
  /**
   * 获取节点性能数据
   * @param nodeId 节点ID
   * @returns 节点性能数据
   */
  public getNodePerformanceData(nodeId: string): NodePerformanceData | null {
    return this.nodePerformance.get(nodeId) || null;
  }
  
  /**
   * 获取图性能数据
   * @param graphId 图ID
   * @returns 图性能数据
   */
  public getGraphPerformanceData(graphId: string): GraphPerformanceData | null {
    return this.graphPerformance.get(graphId) || null;
  }
  
  /**
   * 清除节点性能数据
   * @param nodeId 节点ID
   * @returns 是否成功
   */
  public clearNodePerformanceData(nodeId: string): boolean {
    return this.nodePerformance.delete(nodeId);
  }
  
  /**
   * 清除图性能数据
   * @param graphId 图ID
   * @returns 是否成功
   */
  public clearGraphPerformanceData(graphId: string): boolean {
    return this.graphPerformance.delete(graphId);
  }
  
  /**
   * 清除所有性能数据
   */
  public clearAllPerformanceData(): void {
    this.nodePerformance.clear();
    this.graphPerformance.clear();
    this.lastClearTime = Date.now();
    
    this.eventEmitter.emit('allDataCleared');
  }
  
  /**
   * 检查是否需要自动清除旧数据
   */
  private checkAutoClear(): void {
    // 如果未启用自动清除，直接返回
    if (!this.config.autoClearOldData) {
      return;
    }
    
    // 获取当前时间戳
    const now = Date.now();
    
    // 如果距离上次清除时间不足阈值，直接返回
    if (now - this.lastClearTime < (this.config.autoClearThreshold || 3600000)) {
      return;
    }
    
    // 清除旧数据
    this.clearOldData();
    
    // 更新最后清除时间
    this.lastClearTime = now;
  }
  
  /**
   * 清除旧数据
   */
  private clearOldData(): void {
    // 获取当前时间戳
    const now = Date.now();
    
    // 获取阈值
    const threshold = this.config.autoClearThreshold || 3600000;
    
    // 清除旧的节点性能数据
    for (const [nodeId, nodeData] of this.nodePerformance.entries()) {
      if (now - nodeData.lastExecutionTimestamp > threshold) {
        this.nodePerformance.delete(nodeId);
      }
    }
    
    // 清除旧的图性能数据
    for (const [graphId, graphData] of this.graphPerformance.entries()) {
      if (now - graphData.lastExecutionTimestamp > threshold) {
        this.graphPerformance.delete(graphId);
      }
    }
    
    this.eventEmitter.emit('oldDataCleared');
  }
  
  /**
   * 监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }
  
  /**
   * 取消监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
  
  /**
   * 销毁
   */
  public dispose(): void {
    // 清空性能数据
    this.nodePerformance.clear();
    this.graphPerformance.clear();
    
    // 清空事件监听器
    this.eventEmitter.removeAllListeners();
  }
}
