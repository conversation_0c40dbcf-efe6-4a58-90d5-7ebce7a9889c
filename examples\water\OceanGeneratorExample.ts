/**
 * 海洋生成器示例
 * 展示如何使用海洋生成器创建海洋
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { Camera } from '../../engine/src/rendering/Camera';
import { Transform } from '../../engine/src/core/Transform';
import { Light, LightType } from '../../engine/src/rendering/Light';
import { RenderSystem } from '../../engine/src/rendering/RenderSystem';
import { CameraSystem } from '../../engine/src/rendering/CameraSystem';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { PhysicsSystem } from '../../engine/src/physics/PhysicsSystem';
import { TerrainComponent } from '../../engine/src/terrain/components/TerrainComponent';
import { TerrainSystem } from '../../engine/src/terrain/TerrainSystem';
import { TerrainGenerationAlgorithms } from '../../engine/src/terrain/generation/TerrainGenerationAlgorithms';
import { OceanGenerator } from '../../engine/src/physics/water/OceanGenerator';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterSurfaceRenderer } from '../../engine/src/rendering/water/WaterSurfaceRenderer';
import { Debug } from '../../engine/src/utils/Debug';

/**
 * 海洋生成器示例
 */
export class OceanGeneratorExample {
  /** 世界 */
  private world: World;
  /** 渲染系统 */
  private renderingSystem: RenderSystem;
  /** 相机系统 */
  private cameraSystem: CameraSystem;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 地形系统 */
  private terrainSystem: TerrainSystem;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水面渲染器 */
  private waterSurfaceRenderer: WaterSurfaceRenderer;
  /** 地形实体 */
  private terrainEntity: Entity;
  /** 地形组件 */
  private terrainComponent: TerrainComponent;
  /** 海洋实体 */
  private oceanEntity: Entity;

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 创建系统
    this.createSystems();

    // 创建场景
    this.createScene();

    // 创建地形
    this.createTerrain();

    // 创建海洋
    this.createOcean();

    // 启动渲染循环
    this.startRenderLoop();
  }

  /**
   * 创建系统
   */
  private createSystems(): void {
    // 创建渲染系统
    this.renderingSystem = new RenderSystem(this.world);
    this.world.addSystem(this.renderingSystem);

    // 创建相机系统
    this.cameraSystem = new CameraSystem(this.world);
    this.world.addSystem(this.cameraSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world);
    this.world.addSystem(this.inputSystem);

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem(this.world);
    this.world.addSystem(this.physicsSystem);

    // 创建地形系统
    this.terrainSystem = new TerrainSystem(this.world);
    this.world.addSystem(this.terrainSystem);

    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水面渲染器
    this.waterSurfaceRenderer = new WaterSurfaceRenderer(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true,
      enableDepthTest: true,
      reflectionMapResolution: 512,
      refractionMapResolution: 512,
      useHighQualityWaves: true
    });
    this.world.addSystem(this.waterSurfaceRenderer);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建相机实体
    const cameraEntity = new Entity();
    cameraEntity.addComponent(new Transform());
    cameraEntity.addComponent(new Camera());
    cameraEntity.getComponent<Transform>('Transform').setPosition(new THREE.Vector3(0, 100, 200));
    cameraEntity.getComponent<Transform>('Transform').lookAt(new THREE.Vector3(0, 0, 0));
    this.world.addEntity(cameraEntity);

    // 创建光源实体
    const lightEntity = new Entity();
    lightEntity.addComponent(new Transform());
    lightEntity.addComponent(new Light(LightType.DIRECTIONAL));
    lightEntity.getComponent<Transform>('Transform').setPosition(new THREE.Vector3(50, 100, 50));
    lightEntity.getComponent<Transform>('Transform').lookAt(new THREE.Vector3(0, 0, 0));
    lightEntity.getComponent<Light>('Light').setIntensity(1.0);
    lightEntity.getComponent<Light>('Light').setCastShadow(true);
    this.world.addEntity(lightEntity);

    // 创建环境光实体
    const ambientLightEntity = new Entity();
    ambientLightEntity.addComponent(new Transform());
    ambientLightEntity.addComponent(new Light(LightType.AMBIENT));
    ambientLightEntity.getComponent<Light>('Light').setIntensity(0.3);
    this.world.addEntity(ambientLightEntity);
  }

  /**
   * 创建地形
   */
  private createTerrain(): void {
    // 创建地形实体
    this.terrainEntity = new Entity();
    this.terrainEntity.addComponent(new Transform());

    // 创建地形组件
    this.terrainComponent = new TerrainComponent({
      width: 2000,
      height: 200,
      depth: 2000,
      widthSegments: 200,
      heightSegments: 1,
      depthSegments: 200,
      usePhysics: true
    });

    // 生成地形
    TerrainGenerationAlgorithms.generatePerlinNoiseTerrain(this.terrainComponent, {
      scale: 0.005,
      octaves: 8,
      persistence: 0.5,
      lacunarity: 2.0,
      seed: 12345
    });

    // 添加地形组件
    this.terrainEntity.addComponent(this.terrainComponent);

    // 添加到世界
    this.world.addEntity(this.terrainEntity);
  }

  /**
   * 创建海洋
   */
  private createOcean(): void {
    // 创建海洋生成器
    const oceanGenerator = new OceanGenerator(this.world, {
      position: new THREE.Vector3(0, 0, 0),
      size: {
        width: 2000,
        depth: 2000
      },
      depth: 100,
      resolution: 64,
      followTerrain: true,
      terrainOffset: 0.5,
      generateSeabed: true,
      generateUnderwaterVegetation: true,
      underwaterVegetationDensity: 0.0001,
      generateUnderwaterParticles: true,
      underwaterParticleCount: 2000,
      generateWaves: true,
      waveHeight: 1.0,
      waveFrequency: 0.1,
      waveSpeed: 0.5,
      waveDirection: new THREE.Vector2(1, 1),
      generateCoastline: true,
      coastlineWidth: 20.0,
      coastlineHeight: 2.0,
      generateFoam: true,
      foamDensity: 0.5,
      generateReflection: true,
      reflectionStrength: 0.5,
      generateRefraction: true,
      refractionStrength: 0.5,
      generateCaustics: true,
      causticsStrength: 0.5
    });

    // 设置地形组件
    oceanGenerator.setTerrainComponent(this.terrainComponent);

    // 生成海洋
    this.oceanEntity = oceanGenerator.generate();

    Debug.log('OceanGeneratorExample', '海洋生成完成');
  }

  /**
   * 启动渲染循环
   */
  private startRenderLoop(): void {
    // 更新世界
    this.world.update();

    // 请求下一帧
    requestAnimationFrame(() => this.startRenderLoop());
  }
}
