/**
 * 输入性能监控
 * 用于监控和自动调整输入系统的性能
 */
import { DeviceCapabilities, DevicePerformanceLevel } from '../../utils/DeviceCapabilities';

/**
 * 性能数据
 */
export interface PerformanceData {
  /** 时间戳 */
  timestamp: number;
  /** 帧率 */
  fps: number;
  /** 输入延迟（毫秒） */
  inputLatency: number;
  /** 处理时间（毫秒） */
  processingTime: number;
}

/**
 * 性能监控选项
 */
export interface PerformanceMonitorOptions {
  /** 采样间隔（毫秒） */
  sampleInterval?: number;
  /** 历史数据长度 */
  historyLength?: number;
  /** 是否自动调整 */
  autoAdjust?: boolean;
  /** 目标帧率 */
  targetFPS?: number;
  /** 最大输入延迟（毫秒） */
  maxInputLatency?: number;
  /** 设备能力检测 */
  deviceCapabilities?: DeviceCapabilities;
}

/**
 * 输入性能监控
 */
export class InputPerformanceMonitor {
  /** 采样间隔（毫秒） */
  private sampleInterval: number;

  /** 历史数据长度 */
  private historyLength: number;

  /** 是否自动调整 */
  private autoAdjust: boolean;

  /** 目标帧率 */
  private targetFPS: number;

  /** 最大输入延迟（毫秒） */
  private maxInputLatency: number;

  /** 设备能力检测 */
  private deviceCapabilities: DeviceCapabilities;

  /** 性能历史数据 */
  private history: PerformanceData[] = [];

  /** 上一帧时间 */
  private lastFrameTime: number = 0;

  /** 当前帧率 */
  private currentFPS: number = 60;

  /** 当前输入延迟 */
  private currentInputLatency: number = 0;

  /** 当前处理时间 */
  private currentProcessingTime: number = 0;

  /** 采样定时器 */
  private sampleTimer: number | null = null;

  /** 性能级别 */
  private performanceLevel: DevicePerformanceLevel;

  /** 事件处理器 */
  private eventHandlers: Map<string, Set<(data: any) => void>> = new Map();

  /**
   * 创建输入性能监控
   * @param options 性能监控选项
   */
  constructor(options: PerformanceMonitorOptions = {}) {
    this.sampleInterval = options.sampleInterval || 1000;
    this.historyLength = options.historyLength || 60;
    this.autoAdjust = options.autoAdjust !== undefined ? options.autoAdjust : true;
    this.targetFPS = options.targetFPS || 60;
    this.maxInputLatency = options.maxInputLatency || 50;
    this.deviceCapabilities = options.deviceCapabilities || DeviceCapabilities.getInstance();
    this.performanceLevel = this.deviceCapabilities.getPerformanceLevel();
  }

  /**
   * 开始监控
   */
  public start(): void {
    if (this.sampleTimer !== null) {
      return;
    }

    this.lastFrameTime = performance.now();
    this.sampleTimer = window.setInterval(() => this.sample(), this.sampleInterval);
  }

  /**
   * 停止监控
   */
  public stop(): void {
    if (this.sampleTimer === null) {
      return;
    }

    clearInterval(this.sampleTimer);
    this.sampleTimer = null;
  }

  /**
   * 记录输入事件
   * @param eventType 事件类型
   * @param processingTime 处理时间（毫秒）
   */
  public recordInputEvent(eventType: string, processingTime: number): void {
    const now = performance.now();
    this.currentInputLatency = now - this.lastFrameTime;
    this.currentProcessingTime = processingTime;
    this.lastFrameTime = now;

    // 如果启用自动调整，则根据性能数据调整
    if (this.autoAdjust) {
      this.adjustPerformance();
    }
  }

  /**
   * 获取当前帧率
   * @returns 当前帧率
   */
  public getFPS(): number {
    return this.currentFPS;
  }

  /**
   * 获取当前输入延迟
   * @returns 当前输入延迟（毫秒）
   */
  public getInputLatency(): number {
    return this.currentInputLatency;
  }

  /**
   * 获取当前处理时间
   * @returns 当前处理时间（毫秒）
   */
  public getProcessingTime(): number {
    return this.currentProcessingTime;
  }

  /**
   * 获取性能历史数据
   * @returns 性能历史数据
   */
  public getHistory(): PerformanceData[] {
    return [...this.history];
  }

  /**
   * 获取性能级别
   * @returns 性能级别
   */
  public getPerformanceLevel(): DevicePerformanceLevel {
    return this.performanceLevel;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param handler 事件处理函数
   */
  public on(event: string, handler: (data: any) => void): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set());
    }
    this.eventHandlers.get(event)!.add(handler);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param handler 事件处理函数
   */
  public off(event: string, handler: (data: any) => void): void {
    if (!this.eventHandlers.has(event)) {
      return;
    }
    this.eventHandlers.get(event)!.delete(handler);
  }

  /**
   * 触发事件
   * @param event 事件名称
   * @param data 事件数据
   */
  private emit(event: string, data: any): void {
    if (!this.eventHandlers.has(event)) {
      return;
    }
    for (const handler of this.eventHandlers.get(event)!) {
      handler(data);
    }
  }

  /**
   * 采样性能数据
   */
  private sample(): void {
    const now = performance.now();
    const deltaTime = now - this.lastFrameTime;
    this.currentFPS = 1000 / deltaTime;

    // 添加性能数据到历史记录
    this.history.push({
      timestamp: now,
      fps: this.currentFPS,
      inputLatency: this.currentInputLatency,
      processingTime: this.currentProcessingTime
    });

    // 限制历史记录长度
    if (this.history.length > this.historyLength) {
      this.history.shift();
    }

    // 触发性能数据更新事件
    this.emit('update', {
      fps: this.currentFPS,
      inputLatency: this.currentInputLatency,
      processingTime: this.currentProcessingTime
    });
  }

  /**
   * 根据性能数据调整性能
   */
  private adjustPerformance(): void {
    // 如果输入延迟超过最大值，或者帧率低于目标值的80%
    if (this.currentInputLatency > this.maxInputLatency || this.currentFPS < this.targetFPS * 0.8) {
      // 降低性能级别
      if (this.performanceLevel > DevicePerformanceLevel.LOW) {
        this.performanceLevel = this.performanceLevel - 1;
        this.emit('performanceChange', { level: this.performanceLevel, reason: 'degraded' });
      }
    }
    // 如果输入延迟小于最大值的50%，且帧率高于目标值的120%
    else if (this.currentInputLatency < this.maxInputLatency * 0.5 && this.currentFPS > this.targetFPS * 1.2) {
      // 提高性能级别
      if (this.performanceLevel < DevicePerformanceLevel.HIGH) {
        this.performanceLevel = this.performanceLevel + 1;
        this.emit('performanceChange', { level: this.performanceLevel, reason: 'improved' });
      }
    }
  }
}
