/**
 * 视觉脚本值类型注册表
 * 用于注册和管理值类型
 */
import { EventEmitter } from '../../utils/EventEmitter';
/**
 * 值类型创建函数
 */
export type ValueTypeCreator = () => any;
/**
 * 值类型验证函数
 */
export type ValueTypeValidator = (value: any) => boolean;
/**
 * 值类型转换函数
 */
export type ValueTypeConverter = (value: any) => any;
/**
 * 值类型信息
 */
export interface ValueTypeInfo {
    /** 值类型名称 */
    type: string;
    /** 值类型标签 */
    label?: string;
    /** 值类型描述 */
    description?: string;
    /** 值类型图标 */
    icon?: string;
    /** 值类型颜色 */
    color?: string;
    /** 默认值创建函数 */
    creator: ValueTypeCreator;
    /** 值类型验证函数 */
    validator: ValueTypeValidator;
    /** 值类型转换函数 */
    converter?: ValueTypeConverter;
    /** 是否为基本类型 */
    primitive?: boolean;
    /** 是否为复合类型 */
    composite?: boolean;
    /** 是否为自定义类型 */
    custom?: boolean;
    /** 是否已弃用 */
    deprecated?: boolean;
    /** 弃用原因 */
    deprecatedReason?: string;
    /** 是否实验性 */
    experimental?: boolean;
    /** 标签列表 */
    tags?: string[];
    /** 示例值 */
    examples?: any[];
    /** 文档链接 */
    documentationUrl?: string;
    /** 版本 */
    version?: string;
    /** 作者 */
    author?: string;
    /** 许可证 */
    license?: string;
    /** 依赖项 */
    dependencies?: string[];
    /** 自定义元数据 */
    [key: string]: any;
}
/**
 * 值类型注册表
 * 用于注册和管理值类型
 */
export declare class ValueTypeRegistry extends EventEmitter {
    /** 值类型映射 */
    private valueTypes;
    /** 值类型标签映射 */
    private tags;
    /**
     * 创建值类型注册表
     */
    constructor();
    /**
     * 注册内置值类型
     */
    private registerBuiltinValueTypes;
    /**
     * 注册值类型
     * @param info 值类型信息
     * @returns 是否注册成功
     */
    registerValueType(info: ValueTypeInfo): boolean;
    /**
     * 注销值类型
     * @param type 值类型名称
     * @returns 是否注销成功
     */
    unregisterValueType(type: string): boolean;
    /**
     * 获取值类型信息
     * @param type 值类型名称
     * @returns 值类型信息
     */
    getValueTypeInfo(type: string): ValueTypeInfo | undefined;
    /**
     * 获取所有值类型
     * @returns 值类型信息列表
     */
    getAllValueTypes(): ValueTypeInfo[];
    /**
     * 获取指定标签的值类型
     * @param tag 标签
     * @returns 值类型信息列表
     */
    getValueTypesByTag(tag: string): ValueTypeInfo[];
    /**
     * 获取所有标签
     * @returns 标签列表
     */
    getAllTags(): string[];
    /**
     * 创建值
     * @param type 值类型名称
     * @returns 创建的值
     */
    createValue(type: string): any;
    /**
     * 验证值
     * @param type 值类型名称
     * @param value 要验证的值
     * @returns 是否有效
     */
    validateValue(type: string, value: any): boolean;
    /**
     * 转换值
     * @param type 值类型名称
     * @param value 要转换的值
     * @returns 转换后的值
     */
    convertValue(type: string, value: any): any;
    /**
     * 清空注册表
     */
    clear(): void;
}
