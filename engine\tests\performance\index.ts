/**
 * DL（Digital Learning）引擎性能测试入口
 */
import { PerformanceTestRunner, TestReport } from './PerformanceTestRunner';
import { renderingTestSuite, physicsTestSuite, fullTestSuite } from './PerformanceTestConfig';
import { Debug } from '../../src/utils/Debug';

/**
 * 运行渲染性能测试
 */
export function runRenderingTests(): void {
  Debug.log('性能测试', '开始运行渲染性能测试');
  
  const runner = new PerformanceTestRunner(renderingTestSuite);
  runner.run((report) => {
    Debug.log('性能测试', '渲染性能测试完成');
    saveTestReport(report, 'rendering-performance-report.html');
  });
}

/**
 * 运行物理性能测试
 */
export function runPhysicsTests(): void {
  Debug.log('性能测试', '开始运行物理性能测试');
  
  const runner = new PerformanceTestRunner(physicsTestSuite);
  runner.run((report) => {
    Debug.log('性能测试', '物理性能测试完成');
    saveTestReport(report, 'physics-performance-report.html');
  });
}

/**
 * 运行完整性能测试
 */
export function runFullTests(): void {
  Debug.log('性能测试', '开始运行完整性能测试');
  
  const runner = new PerformanceTestRunner(fullTestSuite);
  runner.run((report) => {
    Debug.log('性能测试', '完整性能测试完成');
    saveTestReport(report, 'full-performance-report.html');
  });
}

/**
 * 保存测试报告
 * @param report 测试报告
 * @param filename 文件名
 */
function saveTestReport(report: TestReport, filename: string): void {
  try {
    PerformanceTestRunner.saveHTMLReport(report, filename);
    Debug.log('性能测试', `测试报告已保存到 ${filename}`);
  } catch (error) {
    Debug.error('性能测试', `保存测试报告失败: ${error}`);
  }
}

/**
 * 主函数
 */
function main(): void {
  // 获取命令行参数
  const args = process.argv.slice(2);
  
  // 如果没有参数，运行完整测试
  if (args.length === 0) {
    runFullTests();
    return;
  }
  
  // 根据参数运行不同的测试
  for (const arg of args) {
    switch (arg.toLowerCase()) {
      case 'rendering':
        runRenderingTests();
        break;
      case 'physics':
        runPhysicsTests();
        break;
      case 'full':
        runFullTests();
        break;
      default:
        Debug.error('性能测试', `未知的测试类型: ${arg}`);
        break;
    }
  }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  main();
}

// 导出测试函数
export default {
  runRenderingTests,
  runPhysicsTests,
  runFullTests,
};
