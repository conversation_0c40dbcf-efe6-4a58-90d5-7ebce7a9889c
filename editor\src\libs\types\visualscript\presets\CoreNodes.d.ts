/**
 * 视觉脚本核心节点
 * 提供基本的流程控制和调试节点
 */
import { EventNode } from '../nodes/EventNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 开始事件节点
 * 当视觉脚本开始执行时触发
 */
export declare class OnStartNode extends EventNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 当视觉脚本开始执行时调用
     */
    onStart(): void;
}
/**
 * 更新事件节点
 * 每帧更新时触发
 */
export declare class OnUpdateNode extends EventNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 当视觉脚本更新时调用
     * @param deltaTime 帧间隔时间（秒）
     */
    onUpdate(deltaTime: number): void;
}
/**
 * 分支节点
 * 根据条件选择执行路径
 */
export declare class BranchNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 处理输入并确定输出流程
     * @param inputs 输入值
     * @returns 输出流程名称
     */
    protected process(inputs: Record<string, any>): string | null;
}
/**
 * 序列节点
 * 按顺序执行多个流程
 */
export declare class SequenceNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 打印日志节点
 * 在控制台打印日志
 */
export declare class PrintLogNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 处理输入并确定输出流程
     * @param inputs 输入值
     * @returns 输出流程名称
     */
    protected process(inputs: Record<string, any>): string | null;
}
/**
 * 延时节点
 * 延时执行流程
 */
export declare class DelayNode extends FlowNode {
    /** 定时器ID */
    private timerId;
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 处理输入并确定输出流程
     * @param inputs 输入值
     * @returns 输出流程名称
     */
    protected process(inputs: Record<string, any>): string | null;
    /**
     * 销毁节点
     */
    dispose(): void;
}
/**
 * 注册核心节点
 * @param registry 节点注册表
 */
export declare function registerCoreNodes(registry: NodeRegistry): void;
