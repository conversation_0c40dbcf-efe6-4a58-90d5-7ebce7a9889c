/**
 * 缓存配置接口
 */
export interface CacheConfig {
  /**
   * 是否启用缓存
   * @default true
   */
  enabled?: boolean;

  /**
   * 缓存级别
   * @default ['memory']
   */
  levels?: CacheLevel[];

  /**
   * 内存缓存TTL（毫秒）
   * @default 60000 (1分钟)
   */
  memoryTtl?: number;

  /**
   * Redis缓存TTL（毫秒）
   * @default 300000 (5分钟)
   */
  redisTtl?: number;

  /**
   * 最大缓存条目数
   * @default 10000
   */
  maxEntries?: number;

  /**
   * 是否启用自适应TTL
   * @default true
   */
  enableAdaptiveTtl?: boolean;

  /**
   * 是否启用统计
   * @default true
   */
  enableStats?: boolean;

  /**
   * 是否启用调试
   * @default false
   */
  debug?: boolean;

  /**
   * Redis连接配置
   */
  redis?: {
    /**
     * 主机
     * @default 'localhost'
     */
    host?: string;

    /**
     * 端口
     * @default 6379
     */
    port?: number;

    /**
     * 密码
     */
    password?: string;

    /**
     * 数据库索引
     * @default 0
     */
    db?: number;

    /**
     * 键前缀
     * @default 'cache:'
     */
    keyPrefix?: string;
  };
}

/**
 * 缓存级别
 */
export enum CacheLevel {
  /** 内存缓存 */
  MEMORY = 'memory',
  /** Redis缓存 */
  REDIS = 'redis',
}
