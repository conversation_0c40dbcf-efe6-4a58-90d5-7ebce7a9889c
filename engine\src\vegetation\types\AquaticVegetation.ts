/**
 * 水生植物类
 * 用于实现水生植物类型
 */
import * as THREE from 'three';
import { VegetationType, VegetationGrowthStage, SeasonType } from '../ecosystem/EcosystemSimulationSystem';
import { VegetationInstanceData } from '../growth/VegetationGrowthSystem';

/**
 * 水生植物类型
 */
export enum AquaticVegetationType {
  /** 睡莲 */
  WATER_LILY = 'water_lily',
  /** 芦苇 */
  REED = 'reed',
  /** 荷花 */
  LOTUS = 'lotus',
  /** 水草 */
  SEAWEED = 'seaweed',
  /** 浮萍 */
  DUCKWEED = 'duckweed',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 水生植物配置
 */
export interface AquaticVegetationConfig {
  /** 类型 */
  type: AquaticVegetationType;
  /** 最大高度 */
  maxHeight?: number;
  /** 最大宽度 */
  maxWidth?: number;
  /** 最大寿命（天） */
  maxAge?: number;
  /** 生长速度 */
  growthRate?: number;
  /** 水深范围 */
  waterDepthRange?: {
    min: number;
    max: number;
  };
  /** 水流速度范围 */
  waterFlowSpeedRange?: {
    min: number;
    max: number;
  };
  /** 水质要求 */
  waterQualityRequirement?: number;
  /** 光照要求 */
  lightRequirement?: number;
  /** 温度范围 */
  temperatureRange?: {
    min: number;
    max: number;
  };
  /** 是否浮水 */
  isFloating?: boolean;
  /** 是否水下 */
  isSubmerged?: boolean;
  /** 是否挺水 */
  isEmergent?: boolean;
  /** 是否开花 */
  canFlower?: boolean;
  /** 开花季节 */
  floweringSeason?: SeasonType[];
  /** 是否结果 */
  canFruit?: boolean;
  /** 结果季节 */
  fruitingSeason?: SeasonType[];
  /** 是否常绿 */
  isEvergreen?: boolean;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 水生植物类
 */
export class AquaticVegetation {
  /** 配置 */
  private config: AquaticVegetationConfig;

  /** 默认配置映射 */
  private static readonly DEFAULT_CONFIGS: { [key in AquaticVegetationType]: AquaticVegetationConfig } = {
    [AquaticVegetationType.WATER_LILY]: {
      type: AquaticVegetationType.WATER_LILY,
      maxHeight: 0.2,
      maxWidth: 1.0,
      maxAge: 365 * 2, // 2年
      growthRate: 1.0,
      waterDepthRange: {
        min: 0.1,
        max: 0.5
      },
      waterFlowSpeedRange: {
        min: 0,
        max: 0.2
      },
      waterQualityRequirement: 0.5,
      lightRequirement: 0.8,
      temperatureRange: {
        min: 15,
        max: 30
      },
      isFloating: true,
      isSubmerged: false,
      isEmergent: false,
      canFlower: true,
      floweringSeason: [SeasonType.SPRING, SeasonType.SUMMER],
      canFruit: false,
      fruitingSeason: [],
      isEvergreen: true
    },
    [AquaticVegetationType.REED]: {
      type: AquaticVegetationType.REED,
      maxHeight: 2.0,
      maxWidth: 0.5,
      maxAge: 365 * 3, // 3年
      growthRate: 1.2,
      waterDepthRange: {
        min: 0.1,
        max: 1.0
      },
      waterFlowSpeedRange: {
        min: 0,
        max: 0.5
      },
      waterQualityRequirement: 0.3,
      lightRequirement: 0.7,
      temperatureRange: {
        min: 10,
        max: 35
      },
      isFloating: false,
      isSubmerged: false,
      isEmergent: true,
      canFlower: true,
      floweringSeason: [SeasonType.SUMMER],
      canFruit: true,
      fruitingSeason: [SeasonType.AUTUMN],
      isEvergreen: false
    },
    [AquaticVegetationType.LOTUS]: {
      type: AquaticVegetationType.LOTUS,
      maxHeight: 1.5,
      maxWidth: 1.2,
      maxAge: 365 * 5, // 5年
      growthRate: 0.8,
      waterDepthRange: {
        min: 0.3,
        max: 1.5
      },
      waterFlowSpeedRange: {
        min: 0,
        max: 0.3
      },
      waterQualityRequirement: 0.7,
      lightRequirement: 0.9,
      temperatureRange: {
        min: 18,
        max: 35
      },
      isFloating: true,
      isSubmerged: false,
      isEmergent: true,
      canFlower: true,
      floweringSeason: [SeasonType.SUMMER],
      canFruit: true,
      fruitingSeason: [SeasonType.AUTUMN],
      isEvergreen: false
    },
    [AquaticVegetationType.SEAWEED]: {
      type: AquaticVegetationType.SEAWEED,
      maxHeight: 1.0,
      maxWidth: 0.3,
      maxAge: 365 * 1, // 1年
      growthRate: 1.5,
      waterDepthRange: {
        min: 0.5,
        max: 10.0
      },
      waterFlowSpeedRange: {
        min: 0,
        max: 1.0
      },
      waterQualityRequirement: 0.4,
      lightRequirement: 0.5,
      temperatureRange: {
        min: 5,
        max: 30
      },
      isFloating: false,
      isSubmerged: true,
      isEmergent: false,
      canFlower: false,
      floweringSeason: [],
      canFruit: false,
      fruitingSeason: [],
      isEvergreen: true
    },
    [AquaticVegetationType.DUCKWEED]: {
      type: AquaticVegetationType.DUCKWEED,
      maxHeight: 0.05,
      maxWidth: 0.1,
      maxAge: 365 * 0.5, // 半年
      growthRate: 2.0,
      waterDepthRange: {
        min: 0,
        max: 0.3
      },
      waterFlowSpeedRange: {
        min: 0,
        max: 0.2
      },
      waterQualityRequirement: 0.2,
      lightRequirement: 0.6,
      temperatureRange: {
        min: 10,
        max: 35
      },
      isFloating: true,
      isSubmerged: false,
      isEmergent: false,
      canFlower: false,
      floweringSeason: [],
      canFruit: false,
      fruitingSeason: [],
      isEvergreen: true
    },
    [AquaticVegetationType.CUSTOM]: {
      type: AquaticVegetationType.CUSTOM,
      maxHeight: 1.0,
      maxWidth: 1.0,
      maxAge: 365 * 1, // 1年
      growthRate: 1.0,
      waterDepthRange: {
        min: 0,
        max: 1.0
      },
      waterFlowSpeedRange: {
        min: 0,
        max: 0.5
      },
      waterQualityRequirement: 0.5,
      lightRequirement: 0.5,
      temperatureRange: {
        min: 10,
        max: 30
      },
      isFloating: false,
      isSubmerged: false,
      isEmergent: false,
      canFlower: false,
      floweringSeason: [],
      canFruit: false,
      fruitingSeason: [],
      isEvergreen: true
    }
  };

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AquaticVegetationConfig) {
    // 合并配置
    this.config = { ...AquaticVegetation.DEFAULT_CONFIGS[config.type], ...config };
  }

  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): AquaticVegetationConfig {
    return this.config;
  }

  /**
   * 创建实例数据
   * @param id 实例ID
   * @param position 位置
   * @param rotation 旋转
   * @param scale 缩放
   * @returns 实例数据
   */
  public createInstanceData(
    id: string,
    position: THREE.Vector3,
    rotation: THREE.Euler,
    scale: THREE.Vector3
  ): VegetationInstanceData {
    return {
      id,
      type: VegetationType.AQUATIC,
      growthStage: VegetationGrowthStage.SEED,
      age: 0,
      health: 1.0,
      maxAge: this.config.maxAge || 365,
      growthRate: this.config.growthRate || 1.0,
      currentHeight: 0,
      maxHeight: this.config.maxHeight || 1.0,
      currentWidth: 0,
      maxWidth: this.config.maxWidth || 1.0,
      currentScale: new THREE.Vector3(0.05, 0.05, 0.05),
      maxScale: scale.clone(),
      position: position.clone(),
      rotation: rotation.clone(),
      evergreen: this.config.isEvergreen || true,
      flowering: false,
      floweringSeason: this.config.floweringSeason || [],
      fruiting: false,
      fruitingSeason: this.config.fruitingSeason || [],
      deciduous: !this.config.isEvergreen,
      leafFallSeason: [SeasonType.AUTUMN],
      userData: {
        aquaticType: this.config.type,
        isFloating: this.config.isFloating,
        isSubmerged: this.config.isSubmerged,
        isEmergent: this.config.isEmergent,
        waterDepthRange: this.config.waterDepthRange,
        waterFlowSpeedRange: this.config.waterFlowSpeedRange,
        waterQualityRequirement: this.config.waterQualityRequirement,
        lightRequirement: this.config.lightRequirement,
        temperatureRange: this.config.temperatureRange
      }
    };
  }

  /**
   * 更新实例数据
   * @param instanceData 实例数据
   * @param waterDepth 水深
   * @param waterFlowSpeed 水流速度
   * @param waterQuality 水质
   * @param lightIntensity 光照强度
   * @param temperature 温度
   * @param deltaTime 帧间隔时间（秒）
   */
  public updateInstanceData(
    instanceData: VegetationInstanceData,
    waterDepth: number,
    waterFlowSpeed: number,
    waterQuality: number,
    lightIntensity: number,
    temperature: number,
    deltaTime: number
  ): void {
    // 检查水深是否在范围内
    const waterDepthRange = this.config.waterDepthRange || { min: 0, max: 1.0 };
    const isWaterDepthSuitable = waterDepth >= waterDepthRange.min && waterDepth <= waterDepthRange.max;

    // 检查水流速度是否在范围内
    const waterFlowSpeedRange = this.config.waterFlowSpeedRange || { min: 0, max: 0.5 };
    const isWaterFlowSpeedSuitable = waterFlowSpeed >= waterFlowSpeedRange.min && waterFlowSpeed <= waterFlowSpeedRange.max;

    // 检查水质是否满足要求
    const waterQualityRequirement = this.config.waterQualityRequirement || 0.5;
    const isWaterQualitySuitable = waterQuality >= waterQualityRequirement;

    // 检查光照强度是否满足要求
    const lightRequirement = this.config.lightRequirement || 0.5;
    const isLightIntensitySuitable = lightIntensity >= lightRequirement;

    // 检查温度是否在范围内
    const temperatureRange = this.config.temperatureRange || { min: 10, max: 30 };
    const isTemperatureSuitable = temperature >= temperatureRange.min && temperature <= temperatureRange.max;

    // 计算环境适应度
    const environmentSuitability = (
      (isWaterDepthSuitable ? 1 : 0) +
      (isWaterFlowSpeedSuitable ? 1 : 0) +
      (isWaterQualitySuitable ? 1 : 0) +
      (isLightIntensitySuitable ? 1 : 0) +
      (isTemperatureSuitable ? 1 : 0)
    ) / 5;

    // 更新健康度
    instanceData.health = Math.max(0, Math.min(1, instanceData.health + (environmentSuitability - 0.5) * 0.01 * deltaTime));

    // 更新生长速度
    instanceData.growthRate = this.config.growthRate || 1.0;
    instanceData.growthRate *= environmentSuitability;
  }

  /**
   * 创建水生植物模型
   * @param type 水生植物类型
   * @returns 模型
   */
  public static createModel(type: AquaticVegetationType): THREE.Object3D {
    let model: THREE.Object3D;

    switch (type) {
      case AquaticVegetationType.WATER_LILY:
        model = AquaticVegetation.createWaterLilyModel();
        break;
      case AquaticVegetationType.REED:
        model = AquaticVegetation.createReedModel();
        break;
      case AquaticVegetationType.LOTUS:
        model = AquaticVegetation.createLotusModel();
        break;
      case AquaticVegetationType.SEAWEED:
        model = AquaticVegetation.createSeaweedModel();
        break;
      case AquaticVegetationType.DUCKWEED:
        model = AquaticVegetation.createDuckweedModel();
        break;
      case AquaticVegetationType.CUSTOM:
      default:
        model = AquaticVegetation.createDefaultModel();
        break;
    }

    return model;
  }

  /**
   * 创建睡莲模型
   * @returns 睡莲模型
   */
  private static createWaterLilyModel(): THREE.Object3D {
    const group = new THREE.Group();

    // 创建叶子
    const leafGeometry = new THREE.CircleGeometry(0.5, 16);
    const leafMaterial = new THREE.MeshBasicMaterial({
      color: 0x00aa00,
      side: THREE.DoubleSide
    });
    const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
    leaf.rotation.x = -Math.PI / 2;
    leaf.position.y = 0.01;
    group.add(leaf);

    // 创建花朵
    const flowerGeometry = new THREE.SphereGeometry(0.1, 8, 8);
    const flowerMaterial = new THREE.MeshBasicMaterial({
      color: 0xffffff
    });
    const flower = new THREE.Mesh(flowerGeometry, flowerMaterial);
    flower.position.y = 0.1;
    group.add(flower);

    return group;
  }

  /**
   * 创建芦苇模型
   * @returns 芦苇模型
   */
  private static createReedModel(): THREE.Object3D {
    const group = new THREE.Group();

    // 创建茎
    const stemGeometry = new THREE.CylinderGeometry(0.02, 0.02, 1.0, 8);
    const stemMaterial = new THREE.MeshBasicMaterial({
      color: 0x00aa00
    });
    const stem = new THREE.Mesh(stemGeometry, stemMaterial);
    stem.position.y = 0.5;
    group.add(stem);

    // 创建叶子
    const leafGeometry = new THREE.PlaneGeometry(0.1, 0.5);
    const leafMaterial = new THREE.MeshBasicMaterial({
      color: 0x00aa00,
      side: THREE.DoubleSide
    });

    // 添加多个叶子
    for (let i = 0; i < 3; i++) {
      const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
      leaf.position.y = 0.3 + i * 0.2;
      leaf.rotation.x = Math.PI / 2;
      leaf.rotation.z = Math.random() * Math.PI * 2;
      group.add(leaf);
    }

    return group;
  }

  /**
   * 创建荷花模型
   * @returns 荷花模型
   */
  private static createLotusModel(): THREE.Object3D {
    const group = new THREE.Group();

    // 创建叶子
    const leafGeometry = new THREE.CircleGeometry(0.6, 16);
    const leafMaterial = new THREE.MeshBasicMaterial({
      color: 0x00aa00,
      side: THREE.DoubleSide
    });
    const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
    leaf.rotation.x = -Math.PI / 2;
    leaf.position.y = 0.01;
    group.add(leaf);

    // 创建茎
    const stemGeometry = new THREE.CylinderGeometry(0.02, 0.02, 1.0, 8);
    const stemMaterial = new THREE.MeshBasicMaterial({
      color: 0x00aa00
    });
    const stem = new THREE.Mesh(stemGeometry, stemMaterial);
    stem.position.y = 0.5;
    group.add(stem);

    // 创建花朵
    const flowerGeometry = new THREE.SphereGeometry(0.2, 16, 16);
    const flowerMaterial = new THREE.MeshBasicMaterial({
      color: 0xff88ff
    });
    const flower = new THREE.Mesh(flowerGeometry, flowerMaterial);
    flower.position.y = 1.0;
    group.add(flower);

    return group;
  }

  /**
   * 创建水草模型
   * @returns 水草模型
   */
  private static createSeaweedModel(): THREE.Object3D {
    const group = new THREE.Group();

    // 创建多个茎
    for (let i = 0; i < 5; i++) {
      const height = 0.5 + Math.random() * 0.5;
      const stemGeometry = new THREE.CylinderGeometry(0.01, 0.01, height, 8);
      const stemMaterial = new THREE.MeshBasicMaterial({
        color: 0x00aa00
      });
      const stem = new THREE.Mesh(stemGeometry, stemMaterial);
      stem.position.x = (Math.random() - 0.5) * 0.2;
      stem.position.z = (Math.random() - 0.5) * 0.2;
      stem.position.y = height / 2;
      stem.rotation.x = (Math.random() - 0.5) * 0.2;
      stem.rotation.z = (Math.random() - 0.5) * 0.2;
      group.add(stem);
    }

    return group;
  }

  /**
   * 创建浮萍模型
   * @returns 浮萍模型
   */
  private static createDuckweedModel(): THREE.Object3D {
    const group = new THREE.Group();

    // 创建多个小叶子
    for (let i = 0; i < 10; i++) {
      const leafGeometry = new THREE.CircleGeometry(0.05, 8);
      const leafMaterial = new THREE.MeshBasicMaterial({
        color: 0x00aa00,
        side: THREE.DoubleSide
      });
      const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
      leaf.rotation.x = -Math.PI / 2;
      leaf.position.x = (Math.random() - 0.5) * 0.2;
      leaf.position.z = (Math.random() - 0.5) * 0.2;
      leaf.position.y = 0.01;
      group.add(leaf);
    }

    return group;
  }

  /**
   * 创建默认模型
   * @returns 默认模型
   */
  private static createDefaultModel(): THREE.Object3D {
    const group = new THREE.Group();

    // 创建简单的几何体
    const geometry = new THREE.SphereGeometry(0.2, 8, 8);
    const material = new THREE.MeshBasicMaterial({
      color: 0x00aa00
    });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.y = 0.2;
    group.add(mesh);

    return group;
  }
}