import type { Entity } from '../core/Entity';
import { System } from '../core/System';
import { FacialAnimationEditorComponent, FacialAnimationEditorConfig, FacialAnimationClip } from './FacialAnimationEditor';
/**
 * 面部动画编辑器系统
 */
export declare class FacialAnimationEditorSystem extends System {
    /** 系统类型 */
    static readonly type = "FacialAnimationEditor";
    /** 面部动画编辑器组件 */
    private components;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(config?: Partial<FacialAnimationEditorConfig>);
    /**
     * 创建面部动画编辑器组件
     * @param entity 实体
     * @returns 面部动画编辑器组件
     */
    createEditor(entity: Entity): FacialAnimationEditorComponent;
    /**
     * 移除面部动画编辑器组件
     * @param entity 实体
     */
    removeEditor(entity: Entity): void;
    /**
     * 获取面部动画编辑器组件
     * @param entity 实体
     * @returns 面部动画编辑器组件，如果不存在则返回null
     */
    getEditor(entity: Entity): FacialAnimationEditorComponent | null;
    /**
     * 创建默认表情动画
     * @param entity 实体
     * @param name 动画名称
     * @returns 动画片段
     */
    createDefaultExpressionAnimation(entity: Entity, name: string): FacialAnimationClip | null;
    /**
     * 创建默认口型动画
     * @param entity 实体
     * @param name 动画名称
     * @returns 动画片段
     */
    createDefaultVisemeAnimation(entity: Entity, name: string): FacialAnimationClip | null;
    /**
     * 创建组合动画
     * @param entity 实体
     * @param name 动画名称
     * @returns 动画片段
     */
    createCombinedAnimation(entity: Entity, name: string): FacialAnimationClip | null;
    /**
     * 导出动画片段为JSON
     * @param entity 实体
     * @param clipName 动画片段名称
     * @returns JSON字符串
     */
    exportClipToJSON(entity: Entity, clipName: string): string | null;
    /**
     * 从JSON导入动画片段
     * @param entity 实体
     * @param json JSON字符串
     * @returns 导入的动画片段
     */
    importClipFromJSON(entity: Entity, json: string): FacialAnimationClip | null;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
