/**
 * VisualScriptEngine类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { VisualScriptEngine } from '../../src/visualscript/VisualScriptEngine';
import { Node } from '../../src/visualscript/Node';
import { NodeRegistry } from '../../src/visualscript/NodeRegistry';
import { Graph } from '../../src/visualscript/Graph';
import { Connection } from '../../src/visualscript/Connection';

// 创建一个测试节点类
class TestNode extends Node {
  public executed: boolean = false;
  public inputValue: any = null;
  
  constructor() {
    super('TestNode');
    
    // 添加输入端口
    this.addInputPort('input', 'any');
    
    // 添加输出端口
    this.addOutputPort('output', 'any');
  }
  
  public execute(inputs: Map<string, any>): Map<string, any> {
    this.executed = true;
    this.inputValue = inputs.get('input');
    
    const outputs = new Map<string, any>();
    outputs.set('output', this.inputValue);
    
    return outputs;
  }
}

// 创建一个数学节点类
class MathNode extends Node {
  public operation: string;
  
  constructor(operation: string = 'add') {
    super('MathNode');
    this.operation = operation;
    
    // 添加输入端口
    this.addInputPort('a', 'number');
    this.addInputPort('b', 'number');
    
    // 添加输出端口
    this.addOutputPort('result', 'number');
  }
  
  public execute(inputs: Map<string, any>): Map<string, any> {
    const a = inputs.get('a') || 0;
    const b = inputs.get('b') || 0;
    let result = 0;
    
    switch (this.operation) {
      case 'add':
        result = a + b;
        break;
      case 'subtract':
        result = a - b;
        break;
      case 'multiply':
        result = a * b;
        break;
      case 'divide':
        result = b !== 0 ? a / b : 0;
        break;
      default:
        result = a + b;
    }
    
    const outputs = new Map<string, any>();
    outputs.set('result', result);
    
    return outputs;
  }
}

// 创建一个条件节点类
class ConditionNode extends Node {
  constructor() {
    super('ConditionNode');
    
    // 添加输入端口
    this.addInputPort('condition', 'boolean');
    this.addInputPort('trueValue', 'any');
    this.addInputPort('falseValue', 'any');
    
    // 添加输出端口
    this.addOutputPort('result', 'any');
  }
  
  public execute(inputs: Map<string, any>): Map<string, any> {
    const condition = inputs.get('condition') || false;
    const trueValue = inputs.get('trueValue');
    const falseValue = inputs.get('falseValue');
    
    const outputs = new Map<string, any>();
    outputs.set('result', condition ? trueValue : falseValue);
    
    return outputs;
  }
}

describe('VisualScriptEngine', () => {
  let visualScriptEngine: VisualScriptEngine;
  let graph: Graph;
  
  // 在每个测试前创建一个新的视觉脚本引擎实例
  beforeEach(() => {
    // 注册测试节点
    NodeRegistry.registerNodeType('TestNode', TestNode);
    NodeRegistry.registerNodeType('MathNode', MathNode);
    NodeRegistry.registerNodeType('ConditionNode', ConditionNode);
    
    // 创建视觉脚本引擎
    visualScriptEngine = new VisualScriptEngine();
    
    // 创建图
    graph = new Graph();
  });
  
  // 在每个测试后清理
  afterEach(() => {
    // 清理节点注册表
    NodeRegistry.clear();
  });
  
  // 测试视觉脚本引擎初始化
  it('应该正确初始化视觉脚本引擎', () => {
    expect(visualScriptEngine).toBeDefined();
  });
  
  // 测试执行简单图
  it('应该能够执行简单图', () => {
    // 创建测试节点
    const testNode = new TestNode();
    testNode.id = 'node1';
    
    // 添加节点到图
    graph.addNode(testNode);
    
    // 设置节点输入
    graph.setNodeInput(testNode.id, 'input', 'Hello, World!');
    
    // 执行图
    visualScriptEngine.executeGraph(graph);
    
    // 验证节点已执行
    expect(testNode.executed).toBe(true);
    expect(testNode.inputValue).toBe('Hello, World!');
    
    // 获取节点输出
    const output = graph.getNodeOutput(testNode.id, 'output');
    
    // 验证输出
    expect(output).toBe('Hello, World!');
  });
  
  // 测试执行带连接的图
  it('应该能够执行带连接的图', () => {
    // 创建数学节点
    const mathNode1 = new MathNode('add');
    mathNode1.id = 'math1';
    
    const mathNode2 = new MathNode('multiply');
    mathNode2.id = 'math2';
    
    // 添加节点到图
    graph.addNode(mathNode1);
    graph.addNode(mathNode2);
    
    // 设置节点输入
    graph.setNodeInput(mathNode1.id, 'a', 5);
    graph.setNodeInput(mathNode1.id, 'b', 3);
    
    // 创建连接
    const connection = new Connection(
      mathNode1.id, 'result',
      mathNode2.id, 'a'
    );
    
    // 添加连接到图
    graph.addConnection(connection);
    
    // 设置第二个节点的输入
    graph.setNodeInput(mathNode2.id, 'b', 2);
    
    // 执行图
    visualScriptEngine.executeGraph(graph);
    
    // 获取最终输出
    const output = graph.getNodeOutput(mathNode2.id, 'result');
    
    // 验证输出 (5 + 3) * 2 = 16
    expect(output).toBe(16);
  });
  
  // 测试执行条件图
  it('应该能够执行条件图', () => {
    // 创建条件节点
    const conditionNode = new ConditionNode();
    conditionNode.id = 'condition1';
    
    // 添加节点到图
    graph.addNode(conditionNode);
    
    // 设置节点输入 - 条件为真
    graph.setNodeInput(conditionNode.id, 'condition', true);
    graph.setNodeInput(conditionNode.id, 'trueValue', 'True Path');
    graph.setNodeInput(conditionNode.id, 'falseValue', 'False Path');
    
    // 执行图
    visualScriptEngine.executeGraph(graph);
    
    // 获取输出
    let output = graph.getNodeOutput(conditionNode.id, 'result');
    
    // 验证输出
    expect(output).toBe('True Path');
    
    // 设置节点输入 - 条件为假
    graph.setNodeInput(conditionNode.id, 'condition', false);
    
    // 执行图
    visualScriptEngine.executeGraph(graph);
    
    // 获取输出
    output = graph.getNodeOutput(conditionNode.id, 'result');
    
    // 验证输出
    expect(output).toBe('False Path');
  });
  
  // 测试执行复杂图
  it('应该能够执行复杂图', () => {
    // 创建数学节点
    const addNode = new MathNode('add');
    addNode.id = 'add';
    
    const multiplyNode = new MathNode('multiply');
    multiplyNode.id = 'multiply';
    
    const subtractNode = new MathNode('subtract');
    subtractNode.id = 'subtract';
    
    // 创建条件节点
    const conditionNode = new ConditionNode();
    conditionNode.id = 'condition';
    
    // 添加节点到图
    graph.addNode(addNode);
    graph.addNode(multiplyNode);
    graph.addNode(subtractNode);
    graph.addNode(conditionNode);
    
    // 设置节点输入
    graph.setNodeInput(addNode.id, 'a', 5);
    graph.setNodeInput(addNode.id, 'b', 3);
    
    graph.setNodeInput(multiplyNode.id, 'b', 2);
    
    graph.setNodeInput(subtractNode.id, 'b', 5);
    
    // 创建连接
    const connection1 = new Connection(
      addNode.id, 'result',
      multiplyNode.id, 'a'
    );
    
    const connection2 = new Connection(
      multiplyNode.id, 'result',
      subtractNode.id, 'a'
    );
    
    const connection3 = new Connection(
      addNode.id, 'result',
      conditionNode.id, 'condition'
    );
    
    const connection4 = new Connection(
      multiplyNode.id, 'result',
      conditionNode.id, 'trueValue'
    );
    
    const connection5 = new Connection(
      subtractNode.id, 'result',
      conditionNode.id, 'falseValue'
    );
    
    // 添加连接到图
    graph.addConnection(connection1);
    graph.addConnection(connection2);
    graph.addConnection(connection3);
    graph.addConnection(connection4);
    graph.addConnection(connection5);
    
    // 执行图
    visualScriptEngine.executeGraph(graph);
    
    // 获取各节点输出
    const addOutput = graph.getNodeOutput(addNode.id, 'result');
    const multiplyOutput = graph.getNodeOutput(multiplyNode.id, 'result');
    const subtractOutput = graph.getNodeOutput(subtractNode.id, 'result');
    const conditionOutput = graph.getNodeOutput(conditionNode.id, 'result');
    
    // 验证输出
    // add: 5 + 3 = 8
    // multiply: 8 * 2 = 16
    // subtract: 16 - 5 = 11
    // condition: (8 > 0) ? 16 : 11 = 16
    expect(addOutput).toBe(8);
    expect(multiplyOutput).toBe(16);
    expect(subtractOutput).toBe(11);
    expect(conditionOutput).toBe(16);
  });
  
  // 测试执行事件
  it('应该能够触发执行事件', () => {
    // 创建事件监听器
    const startSpy = vi.fn();
    const endSpy = vi.fn();
    const nodeSpy = vi.fn();
    
    // 添加事件监听器
    visualScriptEngine.on('executionStart', startSpy);
    visualScriptEngine.on('executionEnd', endSpy);
    visualScriptEngine.on('nodeExecuted', nodeSpy);
    
    // 创建测试节点
    const testNode = new TestNode();
    testNode.id = 'node1';
    
    // 添加节点到图
    graph.addNode(testNode);
    
    // 执行图
    visualScriptEngine.executeGraph(graph);
    
    // 验证事件被触发
    expect(startSpy).toHaveBeenCalled();
    expect(endSpy).toHaveBeenCalled();
    expect(nodeSpy).toHaveBeenCalledWith({ nodeId: 'node1', node: testNode });
  });
});
