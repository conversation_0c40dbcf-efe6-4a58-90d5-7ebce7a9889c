/**
 * 注册服务DTO
 */
import { IsString, IsNotEmpty, IsNumber, IsOptional, IsObject, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class RegisterServiceInstanceDto {
  @ApiProperty({ description: '服务名称' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '服务描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '实例ID' })
  @IsString()
  @IsNotEmpty()
  instanceId: string;

  @ApiProperty({ description: '主机地址' })
  @IsString()
  @IsNotEmpty()
  host: string;

  @ApiProperty({ description: '微服务端口' })
  @IsNumber()
  @IsNotEmpty()
  port: number;

  @ApiProperty({ description: 'HTTP端口', required: false })
  @IsNumber()
  @IsOptional()
  httpPort?: number;

  @ApiProperty({ description: '元数据', required: false })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
