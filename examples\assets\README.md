# DL（Digital Learning）引擎示例项目资源

本目录包含DL（Digital Learning）引擎示例项目的共享资源，包括图片、模型、纹理和脚本等。这些资源可以被多个示例项目共享使用，以减少重复和提高一致性。

## 目录结构

```
/assets/
  ├── images/               # 图片资源
  │   ├── previews/         # 示例项目预览图
  │   ├── textures/         # 纹理图片
  │   ├── icons/            # 图标
  │   └── ui/               # UI图片
  ├── models/               # 3D模型
  │   ├── characters/       # 角色模型
  │   ├── environments/     # 环境模型
  │   ├── props/            # 道具模型
  │   └── primitives/       # 基本几何体模型
  ├── textures/             # 纹理资源
  │   ├── pbr/              # PBR材质纹理
  │   ├── environments/     # 环境纹理
  │   ├── patterns/         # 图案纹理
  │   └── surfaces/         # 表面纹理
  └── scripts/              # 共享脚本
      ├── utils/            # 工具函数
      ├── components/       # 共享组件
      ├── shaders/          # 着色器
      └── templates/        # 模板脚本
```

## 资源使用指南

### 图片资源

图片资源位于 `/assets/images/` 目录下，包括：

- **previews/**: 示例项目的预览图，用于在示例项目浏览器中显示
- **textures/**: 可用作纹理的图片
- **icons/**: 界面图标
- **ui/**: 用户界面元素

使用示例：

```javascript
// 加载预览图
const previewImage = new Image();
previewImage.src = '/examples/assets/images/previews/editor-basics.jpg';

// 加载纹理
const texture = new Texture({
  source: '/examples/assets/images/textures/brick_diffuse.jpg'
});
```

### 3D模型

3D模型位于 `/assets/models/` 目录下，包括：

- **characters/**: 角色模型
- **environments/**: 环境模型
- **props/**: 道具模型
- **primitives/**: 基本几何体模型

使用示例：

```javascript
// 加载角色模型
const modelLoader = new ModelLoader();
modelLoader.load('/examples/assets/models/characters/humanoid.glb', (model) => {
  scene.add(model);
});
```

### 纹理资源

纹理资源位于 `/assets/textures/` 目录下，包括：

- **pbr/**: PBR材质纹理（漫反射、法线、金属度、粗糙度等）
- **environments/**: 环境纹理（环境贴图、HDR等）
- **patterns/**: 图案纹理
- **surfaces/**: 表面纹理

使用示例：

```javascript
// 创建PBR材质
const material = new Material({
  type: 'standard',
  maps: {
    diffuse: '/examples/assets/textures/pbr/wood/diffuse.jpg',
    normal: '/examples/assets/textures/pbr/wood/normal.jpg',
    roughness: '/examples/assets/textures/pbr/wood/roughness.jpg',
    metalness: '/examples/assets/textures/pbr/wood/metalness.jpg'
  }
});
```

### 共享脚本

共享脚本位于 `/assets/scripts/` 目录下，包括：

- **utils/**: 工具函数
- **components/**: 共享组件
- **shaders/**: 着色器
- **templates/**: 模板脚本

使用示例：

```javascript
// 导入工具函数
import { randomColor } from '/examples/assets/scripts/utils/colors.js';

// 使用工具函数
const color = randomColor();
```

## 资源命名规范

为了保持一致性和可读性，请遵循以下命名规范：

### 文件命名

- 使用小写字母和连字符（例如：`brick-wall.jpg`）
- 避免使用空格和特殊字符
- 使用有意义的名称，反映内容或功能
- 对于多文件资源，使用统一的前缀和后缀（例如：`wood_diffuse.jpg`, `wood_normal.jpg`）

### 目录命名

- 使用小写字母和连字符（例如：`character-animations`）
- 避免使用空格和特殊字符
- 使用复数形式表示集合（例如：`models`, `textures`）

## 资源优化指南

为了确保示例项目的性能和加载速度，请遵循以下优化指南：

### 图片优化

- 使用适当的图片格式（JPG用于照片，PNG用于透明图片，WebP用于现代浏览器）
- 压缩图片以减小文件大小
- 提供不同分辨率的图片以适应不同设备
- 对于纹理，使用2的幂次方尺寸（例如：256x256, 512x512, 1024x1024）

### 模型优化

- 减少多边形数量
- 优化UV映射
- 合并材质
- 移除不必要的细节
- 使用LOD（细节层次）技术

### 纹理优化

- 使用纹理图集（Texture Atlas）合并多个小纹理
- 对于PBR材质，考虑使用压缩格式
- 使用适当的纹理分辨率
- 考虑使用程序化纹理生成

## 贡献资源

如果您想贡献新的资源，请遵循以下步骤：

1. 确保资源符合上述命名规范和优化指南
2. 确保资源具有适当的许可证（优先使用CC0、CC-BY或类似的开放许可证）
3. 将资源放置在适当的目录中
4. 更新相关文档
5. 提交资源到DL（Digital Learning）引擎示例项目仓库
6. 等待审核和合并

## 许可证信息

本目录中的资源使用多种许可证，详细信息请参阅各资源目录中的LICENSE文件。

大多数资源使用以下许可证之一：

- [CC0](https://creativecommons.org/publicdomain/zero/1.0/): 公共领域
- [CC-BY](https://creativecommons.org/licenses/by/4.0/): 署名许可证
- [CC-BY-SA](https://creativecommons.org/licenses/by-sa/4.0/): 署名-相同方式共享许可证

如果您使用这些资源，请遵守相应的许可证要求。
