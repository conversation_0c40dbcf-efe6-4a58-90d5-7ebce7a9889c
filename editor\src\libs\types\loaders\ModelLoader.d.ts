/**
 * 模型加载器
 * 用于加载3D模型
 */
import * as THREE from 'three';
/**
 * 模型加载器事件类型
 */
export declare enum ModelLoaderEventType {
    LOAD_START = "load_start",
    LOAD_PROGRESS = "load_progress",
    LOAD_COMPLETE = "load_complete",
    LOAD_ERROR = "load_error"
}
/**
 * 模型加载器选项接口
 */
export interface ModelLoaderOptions {
    /** 是否启用缓存 */
    enableCache?: boolean;
    /** 是否启用纹理压缩 */
    enableTextureCompression?: boolean;
    /** 是否启用几何体优化 */
    enableGeometryOptimization?: boolean;
    /** 是否启用材质优化 */
    enableMaterialOptimization?: boolean;
    /** 是否启用LOD生成 */
    enableLODGeneration?: boolean;
    /** 是否启用调试 */
    enableDebug?: boolean;
}
/**
 * 模型加载器类
 */
export declare class ModelLoader {
    /** 是否启用缓存 */
    private enableCache;
    /** 是否启用纹理压缩 */
    private enableTextureCompression;
    /** 是否启用几何体优化 */
    private enableGeometryOptimization;
    /** 是否启用材质优化 */
    private enableMaterialOptimization;
    /** 是否启用LOD生成 */
    private enableLODGeneration;
    /** 是否启用调试 */
    private enableDebug;
    /** GLTF加载器 */
    private gltfLoader;
    /** OBJ加载器 */
    private objLoader;
    /** FBX加载器 */
    private fbxLoader;
    /** 模型缓存 */
    private modelCache;
    /** 事件发射器 */
    private eventEmitter;
    /** 加载管理器 */
    private loadingManager;
    /**
     * 创建模型加载器
     * @param options 选项
     */
    constructor(options?: ModelLoaderOptions);
    /**
     * 加载模型
     * @param url 模型URL
     * @returns 模型
     */
    load(url: string): Promise<THREE.Object3D | null>;
    /**
     * 加载GLTF模型
     * @param url 模型URL
     * @returns 模型
     */
    private loadGLTF;
    /**
     * 加载OBJ模型
     * @param url 模型URL
     * @returns 模型
     */
    private loadOBJ;
    /**
     * 加载FBX模型
     * @param url 模型URL
     * @returns 模型
     */
    private loadFBX;
    /**
     * 优化模型
     * @param model 模型
     */
    private optimizeModel;
    /**
     * 优化几何体
     * @param geometry 几何体
     */
    private optimizeGeometry;
    /**
     * 优化材质
     * @param material 材质
     */
    private optimizeMaterial;
    /**
     * 优化单个材质
     * @param material 材质
     */
    private optimizeSingleMaterial;
    /**
     * 优化纹理
     * @param texture 纹理
     */
    private optimizeTexture;
    /**
     * 生成LOD
     * @param _model 模型
     */
    private generateLOD;
    /**
     * 清除缓存
     */
    clearCache(): void;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    on(type: ModelLoaderEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    off(type: ModelLoaderEventType, listener: (...args: any[]) => void): void;
}
