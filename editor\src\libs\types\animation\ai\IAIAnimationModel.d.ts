/**
 * AI动画模型接口
 * 定义AI动画生成模型的通用接口
 */
import { FacialAnimationClip } from '../FacialAnimationEditor';
import type { AnimationClip } from '../AnimationClip';
/**
 * 动画生成请求
 */
export interface AnimationGenerationRequest {
    /** 请求ID */
    id: string;
    /** 提示文本 */
    prompt: string;
    /** 动画类型 */
    type: 'body' | 'facial' | 'combined';
    /** 持续时间（秒） */
    duration: number;
    /** 是否循环 */
    loop: boolean;
    /** 参考动画 */
    referenceClip?: AnimationClip | FacialAnimationClip;
    /** 风格 */
    style?: string;
    /** 强度 */
    intensity?: number;
    /** 随机种子 */
    seed?: number;
    /** 用户数据 */
    userData?: any;
}
/**
 * 动画生成结果
 */
export interface AnimationGenerationResult {
    /** 请求ID */
    id: string;
    /** 是否成功 */
    success: boolean;
    /** 错误信息 */
    error?: string;
    /** 生成的动画片段 */
    clip?: AnimationClip | FacialAnimationClip;
    /** 生成时间（毫秒） */
    generationTime?: number;
    /** 用户数据 */
    userData?: any;
}
/**
 * 情感分析结果
 */
export interface EmotionAnalysisResult {
    /** 主要情感 */
    primaryEmotion: string;
    /** 情感强度 */
    intensity: number;
    /** 情感分数映射 */
    scores: {
        [key: string]: number;
    };
}
/**
 * AI动画模型接口
 */
export interface IAIAnimationModel {
    /**
     * 初始化模型
     * @returns 是否成功初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 生成身体动画
     * @param request 生成请求
     * @returns 生成结果
     */
    generateBodyAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    /**
     * 生成面部动画
     * @param request 生成请求
     * @returns 生成结果
     */
    generateFacialAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    /**
     * 生成组合动画
     * @param request 生成请求
     * @returns 生成结果
     */
    generateCombinedAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    /**
     * 分析文本情感
     * @param text 文本
     * @returns 情感分析结果
     */
    analyzeEmotion(text: string): Promise<EmotionAnalysisResult>;
    /**
     * 取消请求
     * @param id 请求ID
     * @returns 是否成功取消
     */
    cancelRequest(id: string): boolean;
    /**
     * 释放资源
     */
    dispose(): void;
}
