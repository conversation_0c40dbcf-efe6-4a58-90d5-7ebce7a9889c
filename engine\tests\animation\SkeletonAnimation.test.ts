/**
 * SkeletonAnimation单元测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SkeletonAnimation } from '../../src/animation/SkeletonAnimation';
import { AnimationClip, LoopMode } from '../../src/animation/AnimationClip';
import { Entity } from '../../src/core/Entity';
import * as THREE from 'three';

// 模拟Animator类
vi.mock('../../src/animation/Animator', () => {
  return {
    Animator: vi.fn().mockImplementation(() => {
      return {
        play: vi.fn(),
        stop: vi.fn(),
        pause: vi.fn(),
        resume: vi.fn(),
        update: vi.fn(),
        addClip: vi.fn(),
        removeClip: vi.fn(),
        getClip: vi.fn(),
        getClips: vi.fn().mockReturnValue([]),
        setTimeScale: vi.fn(),
        setLoop: vi.fn()
      };
    })
  };
});

describe('SkeletonAnimation', () => {
  let skeletonAnimation: SkeletonAnimation;
  let entity: Entity;
  let skinnedMesh: THREE.SkinnedMesh;
  let skeleton: THREE.Skeleton;
  let clip1: AnimationClip;
  let clip2: AnimationClip;

  beforeEach(() => {
    // 创建测试用的实体
    entity = new Entity();
    
    // 创建测试用的骨骼网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial();
    
    // 创建骨骼
    const bones: THREE.Bone[] = [];
    
    const bone1 = new THREE.Bone();
    bone1.name = 'root';
    bone1.position.set(0, 0, 0);
    bones.push(bone1);
    
    const bone2 = new THREE.Bone();
    bone2.name = 'bone2';
    bone2.position.set(0, 1, 0);
    bone1.add(bone2);
    bones.push(bone2);
    
    const bone3 = new THREE.Bone();
    bone3.name = 'bone3';
    bone3.position.set(0, 1, 0);
    bone2.add(bone3);
    bones.push(bone3);
    
    // 创建骨架
    skeleton = new THREE.Skeleton(bones);
    
    // 创建蒙皮网格
    skinnedMesh = new THREE.SkinnedMesh(geometry, material);
    skinnedMesh.bind(skeleton);
    
    // 创建测试用的动画片段
    clip1 = new AnimationClip('clip1', 1.0);
    clip2 = new AnimationClip('clip2', 1.0);
    
    // 创建测试用的骨骼动画组件
    skeletonAnimation = new SkeletonAnimation({
      entity: entity,
      skinnedMesh: skinnedMesh,
      clips: [clip1, clip2]
    });
  });

  it('应该正确创建骨骼动画组件', () => {
    expect(skeletonAnimation).toBeDefined();
    expect(skeletonAnimation.getSkinnedMesh()).toBe(skinnedMesh);
    expect(skeletonAnimation.getSkeleton()).toBe(skeleton);
  });

  it('应该能够设置和获取骨骼网格', () => {
    // 创建新的骨骼网格
    const newGeometry = new THREE.BoxGeometry(2, 2, 2);
    const newMaterial = new THREE.MeshBasicMaterial();
    const newSkinnedMesh = new THREE.SkinnedMesh(newGeometry, newMaterial);
    const newSkeleton = new THREE.Skeleton([new THREE.Bone()]);
    newSkinnedMesh.bind(newSkeleton);
    
    // 设置新的骨骼网格
    skeletonAnimation.setSkinnedMesh(newSkinnedMesh);
    
    // 验证骨骼网格已更新
    expect(skeletonAnimation.getSkinnedMesh()).toBe(newSkinnedMesh);
    expect(skeletonAnimation.getSkeleton()).toBe(newSkeleton);
  });

  it('应该能够播放、停止、暂停和恢复动画', () => {
    // 播放动画
    skeletonAnimation.play('clip1');
    expect(skeletonAnimation.getAnimator().play).toHaveBeenCalledWith('clip1', undefined);
    
    // 停止动画
    skeletonAnimation.stop();
    expect(skeletonAnimation.getAnimator().stop).toHaveBeenCalled();
    
    // 暂停动画
    skeletonAnimation.pause();
    expect(skeletonAnimation.getAnimator().pause).toHaveBeenCalled();
    
    // 恢复动画
    skeletonAnimation.resume();
    expect(skeletonAnimation.getAnimator().resume).toHaveBeenCalled();
  });

  it('应该能够添加和移除动画片段', () => {
    // 创建新的动画片段
    const newClip = new AnimationClip('newClip', 1.0);
    
    // 添加动画片段
    skeletonAnimation.addClip(newClip);
    expect(skeletonAnimation.getAnimator().addClip).toHaveBeenCalledWith(newClip);
    
    // 移除动画片段
    skeletonAnimation.removeClip('newClip');
    expect(skeletonAnimation.getAnimator().removeClip).toHaveBeenCalledWith('newClip');
  });

  it('应该能够获取动画片段', () => {
    // 模拟getClip方法返回clip1
    skeletonAnimation.getAnimator().getClip = vi.fn().mockReturnValue(clip1);
    
    // 获取动画片段
    const clip = skeletonAnimation.getClip('clip1');
    expect(skeletonAnimation.getAnimator().getClip).toHaveBeenCalledWith('clip1');
    expect(clip).toBe(clip1);
  });

  it('应该能够获取所有动画片段', () => {
    // 模拟getClips方法返回[clip1, clip2]
    skeletonAnimation.getAnimator().getClips = vi.fn().mockReturnValue([clip1, clip2]);
    
    // 获取所有动画片段
    const clips = skeletonAnimation.getClips();
    expect(skeletonAnimation.getAnimator().getClips).toHaveBeenCalled();
    expect(clips).toEqual([clip1, clip2]);
  });

  it('应该能够设置和获取骨骼变换', () => {
    // 获取骨骼
    const bone = skeletonAnimation.getBone('bone2');
    expect(bone).toBeDefined();
    expect(bone?.name).toBe('bone2');
    
    // 设置骨骼位置
    const position = new THREE.Vector3(1, 2, 3);
    const result = skeletonAnimation.setBonePosition('bone2', position);
    expect(result).toBe(true);
    expect(bone?.position.equals(position)).toBe(true);
    
    // 设置骨骼旋转
    const rotation = new THREE.Quaternion(0.1, 0.2, 0.3, 0.9);
    const result2 = skeletonAnimation.setBoneRotation('bone2', rotation);
    expect(result2).toBe(true);
    expect(bone?.quaternion.equals(rotation)).toBe(true);
    
    // 设置骨骼缩放
    const scale = new THREE.Vector3(2, 2, 2);
    const result3 = skeletonAnimation.setBoneScale('bone2', scale);
    expect(result3).toBe(true);
    expect(bone?.scale.equals(scale)).toBe(true);
  });

  it('应该能够更新动画', () => {
    // 更新动画
    skeletonAnimation.update(0.1);
    expect(skeletonAnimation.getAnimator().update).toHaveBeenCalledWith(0.1);
  });

  it('应该能够重置骨骼', () => {
    // 修改骨骼位置
    const bone = skeletonAnimation.getBone('bone2');
    if (bone) {
      bone.position.set(1, 2, 3);
      bone.quaternion.set(0.1, 0.2, 0.3, 0.9);
      bone.scale.set(2, 2, 2);
    }
    
    // 重置骨骼
    skeletonAnimation.resetBones();
    
    // 验证骨骼已重置
    // 注意：由于我们没有保存原始变换，这里只是验证方法被调用
    // 实际实现中应该验证骨骼是否恢复到原始状态
  });
});
