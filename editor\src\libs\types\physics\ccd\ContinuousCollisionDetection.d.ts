import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
/**
 * 连续碰撞检测选项
 */
export interface CCDOptions {
    /** 最大子步数 */
    maxSubSteps?: number;
    /** 最小子步时间 */
    minSubStepTime?: number;
    /** 速度阈值 - 超过此值启用CCD */
    velocityThreshold?: number;
    /** 是否对所有物体启用CCD */
    enableForAll?: boolean;
}
/**
 * 连续碰撞检测
 */
export declare class ContinuousCollisionDetection {
    /** 物理世界 */
    private world;
    /** 最大子步数 */
    private maxSubSteps;
    /** 最小子步时间 */
    private minSubStepTime;
    /** 速度阈值 - 超过此值启用CCD */
    private velocityThreshold;
    /** 是否对所有物体启用CCD */
    private enableForAll;
    /** 启用CCD的物体 */
    private enabledBodies;
    /** 物体的上一个位置 */
    private previousPositions;
    /** 物体的上一个旋转 */
    private previousQuaternions;
    /**
     * 创建连续碰撞检测
     * @param world 物理世界
     * @param options 选项
     */
    constructor(world: CANNON.World, options?: CCDOptions);
    /**
     * 更新所有物体的上一个状态
     */
    private updatePreviousStates;
    /**
     * 启用物体的CCD
     * @param body 物理体
     */
    enableBodyCCD(body: CANNON.Body): void;
    /**
     * 禁用物体的CCD
     * @param body 物理体
     */
    disableBodyCCD(body: CANNON.Body): void;
    /**
     * 检查物体是否启用CCD
     * @param body 物理体
     * @returns 是否启用CCD
     */
    isBodyCCDEnabled(body: CANNON.Body): boolean;
    /**
     * 启用实体的CCD
     * @param entity 实体
     */
    enableEntityCCD(entity: Entity): void;
    /**
     * 禁用实体的CCD
     * @param entity 实体
     */
    disableEntityCCD(entity: Entity): void;
    /**
     * 检查实体是否启用CCD
     * @param entity 实体
     * @returns 是否启用CCD
     */
    isEntityCCDEnabled(entity: Entity): boolean;
    /**
     * 更新连续碰撞检测
     * @param deltaTime 时间步长
     */
    update(deltaTime: number): void;
    /**
     * 检查是否需要进行子步进
     * @returns 是否需要子步进
     */
    private checkNeedsSubStepping;
    /**
     * 更新物体位置
     * @param subStepTime 子步时间
     * @param currentStep 当前子步
     * @param totalSteps 总子步数
     * @param originalPositions 原始位置
     * @param originalQuaternions 原始旋转
     */
    private updateBodyPositions;
}
