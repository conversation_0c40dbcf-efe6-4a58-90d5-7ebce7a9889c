/**
 * 注册DTO
 */
import { <PERSON><PERSON><PERSON>, IsEmail, IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RegisterDto {
  @ApiProperty({ description: '用户名', example: 'johndoe' })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({ description: '电子邮箱', example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({ description: '显示名称', required: false, example: '<PERSON> Doe' })
  @IsString()
  @IsOptional()
  displayName?: string;
}
