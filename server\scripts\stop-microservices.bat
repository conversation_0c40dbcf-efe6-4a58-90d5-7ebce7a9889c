@echo off
setlocal enabledelayedexpansion

echo 🛑 停止文本语音场景生成系统微服务架构...

REM 检查Docker Compose是否安装
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose未安装
    pause
    exit /b 1
)

REM 停止所有服务
echo 🔄 停止所有微服务...
docker-compose -f docker-compose.microservices.yml down

REM 询问是否删除数据卷
set /p "delete_volumes=是否要删除所有数据卷？这将删除所有数据库数据 (y/N): "
if /i "!delete_volumes!"=="y" (
    echo 🗑️  删除所有数据卷...
    docker-compose -f docker-compose.microservices.yml down -v
    docker volume prune -f
    echo ✅ 所有数据卷已删除
) else (
    echo 📦 保留数据卷
)

REM 询问是否删除镜像
set /p "delete_images=是否要删除构建的镜像？ (y/N): "
if /i "!delete_images!"=="y" (
    echo 🗑️  删除构建的镜像...
    
    REM 删除未使用的镜像
    docker image prune -f
    
    echo ✅ 镜像已删除
) else (
    echo 🖼️  保留镜像
)

REM 清理未使用的网络
echo 🧹 清理未使用的网络...
docker network prune -f

REM 显示剩余的容器和卷
echo.
echo 📊 剩余资源：
echo 容器：
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo 数据卷：
docker volume ls --format "table {{.Name}}\t{{.Driver}}"

echo.
echo 网络：
docker network ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}"

echo.
echo ✅ 微服务架构已停止
echo.
echo 🔄 重新启动：
echo   scripts\start-microservices.bat
echo.
echo 📝 查看停止的容器：
echo   docker ps -a
echo.
echo 🗑️  完全清理（删除所有容器、镜像、卷）：
echo   docker system prune -a --volumes

pause
