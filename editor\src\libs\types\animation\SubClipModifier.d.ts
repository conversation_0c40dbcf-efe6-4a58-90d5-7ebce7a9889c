/**
 * 子片段变形器
 * 用于修改子片段的播放方式
 */
import * as THREE from 'three';
/**
 * 子片段变形事件类型
 */
export declare enum SubClipModifierEventType {
    /** 变形应用 */
    MODIFIER_APPLIED = "modifierApplied",
    /** 变形更新 */
    MODIFIER_UPDATED = "modifierUpdated",
    /** 变形重置 */
    MODIFIER_RESET = "modifierReset"
}
/**
 * 变形类型
 */
export declare enum ModifierType {
    /** 时间缩放 */
    TIME_SCALE = "timeScale",
    /** 反向播放 */
    REVERSE = "reverse",
    /** 循环 */
    LOOP = "loop",
    /** 镜像 */
    MIRROR = "mirror",
    /** 抖动 */
    JITTER = "jitter",
    /** 延迟 */
    DELAY = "delay",
    /** 平滑 */
    SMOOTH = "smooth",
    /** 噪声 */
    NOISE = "noise",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 子片段变形配置
 */
export interface SubClipModifierConfig {
    /** 变形名称 */
    name?: string;
    /** 变形类型 */
    type?: ModifierType;
    /** 参数 */
    params?: any;
    /** 是否启用 */
    enabled?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 子片段变形器
 */
export declare class SubClipModifier {
    /** 变形名称 */
    private name;
    /** 变形类型 */
    private type;
    /** 参数 */
    private params;
    /** 是否启用 */
    private enabled;
    /** 是否启用调试 */
    private debug;
    /** 事件发射器 */
    private eventEmitter;
    /** 自定义变形函数 */
    private customModifier?;
    /**
     * 创建子片段变形器
     * @param config 配置
     */
    constructor(config?: SubClipModifierConfig);
    /**
     * 获取变形名称
     * @returns 变形名称
     */
    getName(): string;
    /**
     * 设置变形名称
     * @param name 变形名称
     */
    setName(name: string): void;
    /**
     * 获取变形类型
     * @returns 变形类型
     */
    getType(): ModifierType;
    /**
     * 设置变形类型
     * @param type 变形类型
     */
    setType(type: ModifierType): void;
    /**
     * 获取参数
     * @returns 参数
     */
    getParams(): any;
    /**
     * 设置参数
     * @param params 参数
     */
    setParams(params: any): void;
    /**
     * 是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 设置自定义变形函数
     * @param func 自定义变形函数
     */
    setCustomModifier(func: (clip: THREE.AnimationClip, params: any) => THREE.AnimationClip): void;
    /**
     * 应用变形
     * @param clip 动画片段
     * @returns 变形后的动画片段
     */
    apply(clip: THREE.AnimationClip): THREE.AnimationClip;
    /**
     * 应用时间缩放变形
     * @param clip 动画片段
     */
    private applyTimeScale;
    /**
     * 应用反向播放变形
     * @param clip 动画片段
     */
    private applyReverse;
    /**
     * 应用循环变形
     * @param clip 动画片段
     */
    private applyLoop;
    /**
     * 应用镜像变形
     * @param clip 动画片段
     */
    private applyMirror;
    /**
     * 应用抖动变形
     * @param clip 动画片段
     */
    private applyJitter;
    /**
     * 应用延迟变形
     * @param clip 动画片段
     */
    private applyDelay;
    /**
     * 应用平滑变形
     * @param clip 动画片段
     */
    private applySmooth;
    /**
     * 应用噪声变形
     * @param clip 动画片段
     */
    private applyNoise;
    /**
     * 淡入淡出函数
     * @param t 参数
     * @returns 结果
     */
    private fade;
    /**
     * 线性插值函数
     * @param t 参数
     * @param a 值1
     * @param b 值2
     * @returns 结果
     */
    private lerp;
    /**
     * 梯度函数
     * @param hash 哈希值
     * @param x x坐标
     * @param y y坐标
     * @param z z坐标
     * @returns 结果
     */
    private grad;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: SubClipModifierEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: SubClipModifierEventType, listener: (data: any) => void): void;
}
