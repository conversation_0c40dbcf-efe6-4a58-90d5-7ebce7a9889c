/**
 * 温泉组件
 * 用于表示温泉及其特殊物理属性和效果
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';
import { AudioSource } from '../../audio/AudioSource';

/**
 * 温泉配置
 */
export interface HotSpringConfig {
  /** 温泉类型 */
  hotSpringType?: HotSpringType;
  /** 温泉宽度 */
  width?: number;
  /** 温泉高度 */
  height?: number;
  /** 温泉深度 */
  depth?: number;
  /** 温泉位置 */
  position?: THREE.Vector3;
  /** 温泉旋转 */
  rotation?: THREE.Euler;
  /** 温泉颜色 */
  color?: THREE.Color;
  /** 温泉不透明度 */
  opacity?: number;
  /** 温泉温度 */
  temperature?: number;
  /** 温泉波动强度 */
  waveAmplitude?: number;
  /** 温泉波动频率 */
  waveFrequency?: number;
  /** 温泉波动速度 */
  waveSpeed?: number;
  /** 是否启用气泡效果 */
  enableBubbleEffect?: boolean;
  /** 气泡效果强度 */
  bubbleEffectStrength?: number;
  /** 气泡大小范围 */
  bubbleSizeRange?: [number, number];
  /** 气泡速度范围 */
  bubbleSpeedRange?: [number, number];
  /** 气泡密度 */
  bubbleDensity?: number;
  /** 气泡分布范围 */
  bubbleDistributionRadius?: number;
  /** 是否启用气泡爆裂效果 */
  enableBubbleBurstEffect?: boolean;
  /** 气泡爆裂效果强度 */
  bubbleBurstEffectStrength?: number;
  /** 是否启用水蒸气效果 */
  enableSteamEffect?: boolean;
  /** 水蒸气效果强度 */
  steamEffectStrength?: number;
  /** 水蒸气颜色 */
  steamColor?: THREE.Color;
  /** 水蒸气密度 */
  steamDensity?: number;
  /** 水蒸气大小范围 */
  steamSizeRange?: [number, number];
  /** 水蒸气速度范围 */
  steamSpeedRange?: [number, number];
  /** 水蒸气上升高度 */
  steamRiseHeight?: number;
  /** 是否启用声音效果 */
  enableSoundEffect?: boolean;
  /** 声音效果音量 */
  soundEffectVolume?: number;
  /** 是否启用热扩散效果 */
  enableHeatDiffusion?: boolean;
  /** 热扩散范围 */
  heatDiffusionRange?: number;
  /** 是否启用矿物质效果 */
  enableMineralEffect?: boolean;
  /** 矿物质颜色 */
  mineralColor?: THREE.Color;
}

/**
 * 温泉类型
 */
export enum HotSpringType {
  /** 标准温泉 */
  STANDARD = 'standard',
  /** 高温温泉 */
  HIGH_TEMPERATURE = 'high_temperature',
  /** 低温温泉 */
  LOW_TEMPERATURE = 'low_temperature',
  /** 大型温泉 */
  LARGE = 'large',
  /** 小型温泉 */
  SMALL = 'small',
  /** 硫磺温泉 */
  SULFUR = 'sulfur',
  /** 矿物质温泉 */
  MINERAL = 'mineral',
  /** 地下温泉 */
  UNDERGROUND = 'underground'
}

/**
 * 温泉组件
 */
export class HotSpringComponent extends WaterBodyComponent {
  /** 温泉类型 */
  private hotSpringType: HotSpringType = HotSpringType.STANDARD;
  /** 温泉温度 */
  private hotSpringTemperature: number = 60.0; // 摄氏度
  /** 温泉波动强度 */
  private waveAmplitude: number = 0.1;
  /** 温泉波动频率 */
  private waveFrequency: number = 2.0;
  /** 温泉波动速度 */
  private waveSpeed: number = 0.5;
  /** 是否启用气泡效果 */
  private enableBubbleEffect: boolean = true;
  /** 气泡效果强度 */
  private bubbleEffectStrength: number = 1.0;
  /** 气泡大小范围 */
  private bubbleSizeRange: [number, number] = [0.05, 0.2];
  /** 气泡速度范围 */
  private bubbleSpeedRange: [number, number] = [0.1, 0.3];
  /** 气泡密度 */
  private bubbleDensity: number = 1.0;
  /** 气泡分布范围 */
  private bubbleDistributionRadius: number = 0.8;
  /** 是否启用气泡爆裂效果 */
  private enableBubbleBurstEffect: boolean = true;
  /** 气泡爆裂效果强度 */
  private bubbleBurstEffectStrength: number = 1.0;
  /** 是否启用水蒸气效果 */
  private enableSteamEffect: boolean = true;
  /** 水蒸气效果强度 */
  private steamEffectStrength: number = 1.0;
  /** 水蒸气颜色 */
  private steamColor: THREE.Color = new THREE.Color(0xffffff);
  /** 水蒸气密度 */
  private steamDensity: number = 1.0;
  /** 水蒸气大小范围 */
  private steamSizeRange: [number, number] = [0.5, 1.5];
  /** 水蒸气速度范围 */
  private steamSpeedRange: [number, number] = [0.05, 0.1];
  /** 水蒸气上升高度 */
  private steamRiseHeight: number = 2.0;
  /** 是否启用声音效果 */
  private enableSoundEffect: boolean = true;
  /** 声音效果音量 */
  private soundEffectVolume: number = 1.0;
  /** 是否启用热扩散效果 */
  private enableHeatDiffusion: boolean = true;
  /** 热扩散范围 */
  private heatDiffusionRange: number = 5.0;
  /** 是否启用矿物质效果 */
  private enableMineralEffect: boolean = true;
  /** 矿物质颜色 */
  private mineralColor: THREE.Color = new THREE.Color(0xc0a080);
  /** 音频源 */
  private audioSource: AudioSource | null = null;
  /** 矿物质边缘 */
  private mineralEdge: THREE.Mesh | null = null;
  /** 热扩散区域 */
  private heatDiffusionArea: THREE.Mesh | null = null;
  /** 气泡粒子组 */
  private bubbleParticles: THREE.Points[] = [];
  /** 水蒸气粒子组 */
  private steamParticles: THREE.Points[] = [];
  /** 粒子更新时间 */
  private particleUpdateTime: number = 0;
  /** 温泉波动时间 */
  private hotSpringWaveTime: number = 0;
  /** 热扩散影响的实体列表 */
  private affectedEntities: Set<Entity> = new Set();

  /**
   * 创建温泉组件
   * @param entity 实体
   * @param config 温泉配置
   */
  constructor(entity: Entity, config: HotSpringConfig = {}) {
    // 确保水体类型设置为温泉
    const hotSpringConfig = {
      ...config,
      type: WaterBodyType.HOT_SPRING
    };

    super(entity, hotSpringConfig);

    // 应用配置
    this.applyConfig(config);

    // 初始化温泉特有属性
    this.initialize();
  }

  /**
   * 应用配置
   * @param config 温泉配置
   */
  private applyConfig(config: HotSpringConfig): void {
    // 基本配置通过父类构造函数已经处理，这里只处理温泉特有配置

    // 应用温泉特有配置
    if (config.hotSpringType !== undefined) this.hotSpringType = config.hotSpringType;
    if (config.temperature !== undefined) this.hotSpringTemperature = config.temperature;
    if (config.waveAmplitude !== undefined) this.waveAmplitude = config.waveAmplitude;
    if (config.waveFrequency !== undefined) this.waveFrequency = config.waveFrequency;
    if (config.waveSpeed !== undefined) this.waveSpeed = config.waveSpeed;

    // 应用气泡效果配置
    if (config.enableBubbleEffect !== undefined) this.enableBubbleEffect = config.enableBubbleEffect;
    if (config.bubbleEffectStrength !== undefined) this.bubbleEffectStrength = config.bubbleEffectStrength;
    if (config.bubbleSizeRange !== undefined) this.bubbleSizeRange = config.bubbleSizeRange;
    if (config.bubbleSpeedRange !== undefined) this.bubbleSpeedRange = config.bubbleSpeedRange;
    if (config.bubbleDensity !== undefined) this.bubbleDensity = config.bubbleDensity;
    if (config.bubbleDistributionRadius !== undefined) this.bubbleDistributionRadius = config.bubbleDistributionRadius;

    // 应用气泡爆裂效果配置
    if (config.enableBubbleBurstEffect !== undefined) this.enableBubbleBurstEffect = config.enableBubbleBurstEffect;
    if (config.bubbleBurstEffectStrength !== undefined) this.bubbleBurstEffectStrength = config.bubbleBurstEffectStrength;

    // 应用水蒸气效果配置
    if (config.enableSteamEffect !== undefined) this.enableSteamEffect = config.enableSteamEffect;
    if (config.steamEffectStrength !== undefined) this.steamEffectStrength = config.steamEffectStrength;
    if (config.steamColor) this.steamColor = config.steamColor;
    if (config.steamDensity !== undefined) this.steamDensity = config.steamDensity;
    if (config.steamSizeRange !== undefined) this.steamSizeRange = config.steamSizeRange;
    if (config.steamSpeedRange !== undefined) this.steamSpeedRange = config.steamSpeedRange;
    if (config.steamRiseHeight !== undefined) this.steamRiseHeight = config.steamRiseHeight;

    // 应用其他效果配置
    if (config.enableSoundEffect !== undefined) this.enableSoundEffect = config.enableSoundEffect;
    if (config.soundEffectVolume !== undefined) this.soundEffectVolume = config.soundEffectVolume;
    if (config.enableHeatDiffusion !== undefined) this.enableHeatDiffusion = config.enableHeatDiffusion;
    if (config.heatDiffusionRange !== undefined) this.heatDiffusionRange = config.heatDiffusionRange;
    if (config.enableMineralEffect !== undefined) this.enableMineralEffect = config.enableMineralEffect;
    if (config.mineralColor) this.mineralColor = config.mineralColor;
  }

  /**
   * 初始化温泉组件
   */
  public initialize(): void {
    // 调用父类初始化
    super.initialize();

    // 设置波动参数（如果父类有此方法）
    if (typeof (this as any).setWaveParams === 'function') {
      (this as any).setWaveParams({
        amplitude: this.waveAmplitude,
        frequency: this.waveFrequency,
        speed: this.waveSpeed,
        direction: new THREE.Vector2(1, 1)
      });
    }

    // 初始化音频
    this.initializeAudio();

    // 初始化粒子系统
    this.initializeParticleSystems();

    // 创建矿物质边缘
    this.createMineralEdge();

    // 创建热扩散区域
    this.createHeatDiffusionArea();

    Debug.log('HotSpringComponent', '温泉组件初始化完成');
  }

  /**
   * 初始化音频
   */
  private initializeAudio(): void {
    if (!this.enableSoundEffect) {
      return;
    }

    try {
      // 获取音频系统（如果存在）
      const audioSystem = this.entity.getWorld().getSystems().find(s => s.constructor.name === 'AudioSystem') as any;
      if (!audioSystem) {
        Debug.warn('HotSpringComponent', '音频系统不可用');
        return;
      }

      // 根据温泉类型设置不同的音频文件
      let audioFile = 'sounds/hotspring/standard.mp3';
      switch (this.hotSpringType) {
        case HotSpringType.HIGH_TEMPERATURE:
          audioFile = 'sounds/hotspring/high_temp.mp3';
          break;
        case HotSpringType.LOW_TEMPERATURE:
          audioFile = 'sounds/hotspring/low_temp.mp3';
          break;
        case HotSpringType.LARGE:
          audioFile = 'sounds/hotspring/large.mp3';
          break;
        case HotSpringType.SMALL:
          audioFile = 'sounds/hotspring/small.mp3';
          break;
        case HotSpringType.SULFUR:
          audioFile = 'sounds/hotspring/sulfur.mp3';
          break;
        case HotSpringType.MINERAL:
          audioFile = 'sounds/hotspring/mineral.mp3';
          break;
        case HotSpringType.UNDERGROUND:
          audioFile = 'sounds/hotspring/underground.mp3';
          break;
        default:
          audioFile = 'sounds/hotspring/standard.mp3';
          break;
      }

      // 创建音频源选项
      const position = this.getPosition();
      const audioOptions = {
        id: `hotspring_${this.entity.id}`,
        context: audioSystem.getContext(),
        listener: audioSystem.getListener(),
        loop: true,
        volume: this.soundEffectVolume,
        spatial: true,
        position: position,
        refDistance: 2.0,
        maxDistance: 20.0,
        rolloffFactor: 1.0
      };

      // 创建音频源
      this.audioSource = new AudioSource(audioOptions);

      // 设置位置
      this.audioSource.setPosition(position.x, position.y, position.z);

      // 加载并播放音频
      audioSystem.loadAudio(audioFile).then((buffer: AudioBuffer) => {
        if (this.audioSource) {
          this.audioSource.setBuffer(buffer);
          this.audioSource.play();
        }
      }).catch((error: any) => {
        Debug.warn('HotSpringComponent', '音频加载失败:', error);
      });

      Debug.log('HotSpringComponent', `音频系统初始化完成: ${audioFile}`);
    } catch (error) {
      Debug.warn('HotSpringComponent', '音频初始化失败:', error);
      this.audioSource = null;
    }
  }

  /**
   * 初始化粒子系统
   */
  private initializeParticleSystems(): void {
    try {
      // 初始化气泡粒子系统
      if (this.enableBubbleEffect) {
        this.createBubbleParticles();
      }

      // 初始化水蒸气粒子系统
      if (this.enableSteamEffect) {
        this.createSteamParticles();
      }

      Debug.log('HotSpringComponent', '粒子系统初始化完成');
    } catch (error) {
      Debug.warn('HotSpringComponent', '粒子系统初始化失败:', error);
    }
  }

  /**
   * 创建气泡粒子
   */
  private createBubbleParticles(): void {
    const position = this.getPosition();
    const particleCount = Math.floor(this.bubbleDensity * 100);

    // 创建气泡几何体
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const velocities = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);
    const lifetimes = new Float32Array(particleCount);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;

      // 随机分布在温泉区域内
      const angle = Math.random() * Math.PI * 2;
      const radius = Math.random() * this.bubbleDistributionRadius;

      positions[i3] = position.x + Math.cos(angle) * radius;
      positions[i3 + 1] = position.y - this.getSize().height / 2 + Math.random() * 0.5;
      positions[i3 + 2] = position.z + Math.sin(angle) * radius;

      // 设置上升速度
      velocities[i3] = (Math.random() - 0.5) * 0.1;
      velocities[i3 + 1] = this.bubbleSpeedRange[0] + Math.random() * (this.bubbleSpeedRange[1] - this.bubbleSpeedRange[0]);
      velocities[i3 + 2] = (Math.random() - 0.5) * 0.1;

      // 设置大小
      sizes[i] = this.bubbleSizeRange[0] + Math.random() * (this.bubbleSizeRange[1] - this.bubbleSizeRange[0]);

      // 设置生命周期
      lifetimes[i] = Math.random() * 5.0 + 2.0;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    geometry.setAttribute('lifetime', new THREE.BufferAttribute(lifetimes, 1));

    // 创建气泡材质
    const material = new THREE.PointsMaterial({
      color: 0xffffff,
      size: 0.1,
      transparent: true,
      opacity: 0.6,
      alphaTest: 0.1,
      sizeAttenuation: true
    });

    // 创建气泡粒子系统
    const bubbleParticles = new THREE.Points(geometry, material);
    this.bubbleParticles.push(bubbleParticles);

    // 添加到场景（通过渲染组件）
    const renderComponent = this.entity.getComponent('RenderComponent') as any;
    if (renderComponent) {
      renderComponent.addObject(bubbleParticles);
    }
  }

  /**
   * 创建水蒸气粒子
   */
  private createSteamParticles(): void {
    const position = this.getPosition();
    const particleCount = Math.floor(this.steamDensity * 50);

    // 创建水蒸气几何体
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const velocities = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);
    const lifetimes = new Float32Array(particleCount);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;

      // 随机分布在温泉表面
      const angle = Math.random() * Math.PI * 2;
      const radius = Math.random() * (this.getSize().width / 2);

      positions[i3] = position.x + Math.cos(angle) * radius;
      positions[i3 + 1] = position.y + 0.1;
      positions[i3 + 2] = position.z + Math.sin(angle) * radius;

      // 设置上升速度
      velocities[i3] = (Math.random() - 0.5) * 0.2;
      velocities[i3 + 1] = this.steamSpeedRange[0] + Math.random() * (this.steamSpeedRange[1] - this.steamSpeedRange[0]);
      velocities[i3 + 2] = (Math.random() - 0.5) * 0.2;

      // 设置大小
      sizes[i] = this.steamSizeRange[0] + Math.random() * (this.steamSizeRange[1] - this.steamSizeRange[0]);

      // 设置生命周期
      lifetimes[i] = Math.random() * 8.0 + 3.0;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    geometry.setAttribute('lifetime', new THREE.BufferAttribute(lifetimes, 1));

    // 创建水蒸气材质
    const material = new THREE.PointsMaterial({
      color: this.steamColor,
      size: 0.2,
      transparent: true,
      opacity: 0.3,
      alphaTest: 0.1,
      sizeAttenuation: true
    });

    // 创建水蒸气粒子系统
    const steamParticles = new THREE.Points(geometry, material);
    this.steamParticles.push(steamParticles);

    // 添加到场景（通过渲染组件）
    const renderComponent = this.entity.getComponent('RenderComponent') as any;
    if (renderComponent) {
      renderComponent.addObject(steamParticles);
    }
  }

  /**
   * 创建矿物质边缘
   */
  private createMineralEdge(): void {
    if (!this.enableMineralEffect) {
      return;
    }

    try {
      const position = this.getPosition();
      const size = this.getSize();

      // 创建矿物质边缘几何体
      const geometry = new THREE.RingGeometry(
        size.width / 2,
        size.width / 2 + 1.5,
        32,
        1
      );

      // 根据温泉类型设置矿物质颜色
      let mineralColor = this.mineralColor;
      switch (this.hotSpringType) {
        case HotSpringType.SULFUR:
          mineralColor = new THREE.Color(0xffff88); // 硫磺黄色
          break;
        case HotSpringType.HIGH_TEMPERATURE:
          mineralColor = new THREE.Color(0xff8888); // 高温红色
          break;
        case HotSpringType.LOW_TEMPERATURE:
          mineralColor = new THREE.Color(0x88ffff); // 低温蓝色
          break;
        case HotSpringType.LARGE:
          mineralColor = new THREE.Color(0xaaaaaa); // 大型灰色
          break;
        case HotSpringType.SMALL:
          mineralColor = new THREE.Color(0xcccccc); // 小型浅灰色
          break;
        case HotSpringType.MINERAL:
          mineralColor = new THREE.Color(0x88ff88); // 矿物质绿色
          break;
        case HotSpringType.UNDERGROUND:
          mineralColor = new THREE.Color(0x888888); // 地下灰色
          break;
        default:
          mineralColor = this.mineralColor;
          break;
      }

      // 创建矿物质边缘材质
      const material = new THREE.MeshStandardMaterial({
        color: mineralColor,
        transparent: true,
        opacity: 0.8,
        side: THREE.DoubleSide,
        roughness: 0.8,
        metalness: 0.2
      });

      // 创建矿物质边缘网格
      this.mineralEdge = new THREE.Mesh(geometry, material);
      this.mineralEdge.position.copy(position);
      this.mineralEdge.position.y = position.y - size.height / 2 + 0.05;
      this.mineralEdge.rotation.x = -Math.PI / 2;

      // 添加到场景
      const renderComponent = this.entity.getComponent('RenderComponent') as any;
      if (renderComponent) {
        renderComponent.addObject(this.mineralEdge);
      }

      Debug.log('HotSpringComponent', '矿物质边缘创建完成');
    } catch (error) {
      Debug.warn('HotSpringComponent', '矿物质边缘创建失败:', error);
    }
  }

  /**
   * 创建热扩散区域
   */
  private createHeatDiffusionArea(): void {
    if (!this.enableHeatDiffusion) {
      return;
    }

    try {
      const position = this.getPosition();

      // 创建热扩散区域几何体
      const geometry = new THREE.CircleGeometry(this.heatDiffusionRange, 32);

      // 创建热扩散区域材质
      const material = new THREE.MeshBasicMaterial({
        color: 0xff6666,
        transparent: true,
        opacity: 0.1,
        side: THREE.DoubleSide,
        depthWrite: false
      });

      // 创建热扩散区域网格
      this.heatDiffusionArea = new THREE.Mesh(geometry, material);
      this.heatDiffusionArea.position.copy(position);
      this.heatDiffusionArea.position.y = position.y - this.getSize().height / 2 + 0.1;
      this.heatDiffusionArea.rotation.x = -Math.PI / 2;

      // 添加到场景
      const renderComponent = this.entity.getComponent('RenderComponent') as any;
      if (renderComponent) {
        renderComponent.addObject(this.heatDiffusionArea);
      }

      Debug.log('HotSpringComponent', '热扩散区域创建完成');
    } catch (error) {
      Debug.warn('HotSpringComponent', '热扩散区域创建失败:', error);
    }
  }

  /**
   * 更新温泉组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 调用父类更新
    super.update(deltaTime);

    // 更新粒子效果
    this.updateParticleEffects(deltaTime);

    // 更新热扩散效果
    this.updateHeatDiffusion(deltaTime);

    // 更新音频
    this.updateAudio(deltaTime);

    // 更新波动效果
    this.updateWaveEffects(deltaTime);
  }

  /**
   * 更新粒子效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateParticleEffects(deltaTime: number): void {
    this.particleUpdateTime += deltaTime;

    // 更新气泡粒子
    this.updateBubbleParticles(deltaTime);

    // 更新水蒸气粒子
    this.updateSteamParticles(deltaTime);
  }

  /**
   * 更新气泡粒子
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateBubbleParticles(deltaTime: number): void {
    if (!this.enableBubbleEffect || this.bubbleParticles.length === 0) {
      return;
    }

    for (const bubbleParticle of this.bubbleParticles) {
      const positions = bubbleParticle.geometry.attributes.position as THREE.BufferAttribute;
      const velocities = bubbleParticle.geometry.attributes.velocity as THREE.BufferAttribute;
      const lifetimes = bubbleParticle.geometry.attributes.lifetime as THREE.BufferAttribute;

      const positionArray = positions.array as Float32Array;
      const velocityArray = velocities.array as Float32Array;
      const lifetimeArray = lifetimes.array as Float32Array;

      const particleCount = positionArray.length / 3;

      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // 更新位置
        positionArray[i3] += velocityArray[i3] * deltaTime;
        positionArray[i3 + 1] += velocityArray[i3 + 1] * deltaTime;
        positionArray[i3 + 2] += velocityArray[i3 + 2] * deltaTime;

        // 减少生命周期
        lifetimeArray[i] -= deltaTime;

        // 如果粒子生命周期结束，重新初始化
        if (lifetimeArray[i] <= 0) {
          this.resetBubbleParticle(i, positionArray, velocityArray, lifetimeArray);
        }
      }

      // 标记需要更新
      positions.needsUpdate = true;
      velocities.needsUpdate = true;
      lifetimes.needsUpdate = true;
    }
  }

  /**
   * 重置气泡粒子
   */
  private resetBubbleParticle(index: number, positions: Float32Array, velocities: Float32Array, lifetimes: Float32Array): void {
    const i3 = index * 3;
    const position = this.getPosition();

    // 重新随机分布在温泉区域内
    const angle = Math.random() * Math.PI * 2;
    const radius = Math.random() * this.bubbleDistributionRadius;

    positions[i3] = position.x + Math.cos(angle) * radius;
    positions[i3 + 1] = position.y - this.getSize().height / 2 + Math.random() * 0.5;
    positions[i3 + 2] = position.z + Math.sin(angle) * radius;

    // 重新设置速度
    velocities[i3] = (Math.random() - 0.5) * 0.1;
    velocities[i3 + 1] = this.bubbleSpeedRange[0] + Math.random() * (this.bubbleSpeedRange[1] - this.bubbleSpeedRange[0]);
    velocities[i3 + 2] = (Math.random() - 0.5) * 0.1;

    // 重新设置生命周期
    lifetimes[index] = Math.random() * 5.0 + 2.0;
  }

  /**
   * 更新水蒸气粒子
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateSteamParticles(deltaTime: number): void {
    if (!this.enableSteamEffect || this.steamParticles.length === 0) {
      return;
    }

    for (const steamParticle of this.steamParticles) {
      const positions = steamParticle.geometry.attributes.position as THREE.BufferAttribute;
      const velocities = steamParticle.geometry.attributes.velocity as THREE.BufferAttribute;
      const lifetimes = steamParticle.geometry.attributes.lifetime as THREE.BufferAttribute;

      const positionArray = positions.array as Float32Array;
      const velocityArray = velocities.array as Float32Array;
      const lifetimeArray = lifetimes.array as Float32Array;

      const particleCount = positionArray.length / 3;

      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // 更新位置
        positionArray[i3] += velocityArray[i3] * deltaTime;
        positionArray[i3 + 1] += velocityArray[i3 + 1] * deltaTime;
        positionArray[i3 + 2] += velocityArray[i3 + 2] * deltaTime;

        // 减少生命周期
        lifetimeArray[i] -= deltaTime;

        // 如果粒子生命周期结束，重新初始化
        if (lifetimeArray[i] <= 0) {
          this.resetSteamParticle(i, positionArray, velocityArray, lifetimeArray);
        }
      }

      // 标记需要更新
      positions.needsUpdate = true;
      velocities.needsUpdate = true;
      lifetimes.needsUpdate = true;
    }
  }

  /**
   * 重置水蒸气粒子
   */
  private resetSteamParticle(index: number, positions: Float32Array, velocities: Float32Array, lifetimes: Float32Array): void {
    const i3 = index * 3;
    const position = this.getPosition();

    // 重新随机分布在温泉表面
    const angle = Math.random() * Math.PI * 2;
    const radius = Math.random() * (this.getSize().width / 2);

    positions[i3] = position.x + Math.cos(angle) * radius;
    positions[i3 + 1] = position.y + 0.1;
    positions[i3 + 2] = position.z + Math.sin(angle) * radius;

    // 重新设置速度
    velocities[i3] = (Math.random() - 0.5) * 0.2;
    velocities[i3 + 1] = this.steamSpeedRange[0] + Math.random() * (this.steamSpeedRange[1] - this.steamSpeedRange[0]);
    velocities[i3 + 2] = (Math.random() - 0.5) * 0.2;

    // 重新设置生命周期
    lifetimes[index] = Math.random() * 8.0 + 3.0;
  }

  /**
   * 更新热扩散效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateHeatDiffusion(deltaTime: number): void {
    if (!this.enableHeatDiffusion || !this.heatDiffusionArea) {
      return;
    }

    try {
      const position = this.getPosition();

      // 获取热扩散区域内的实体
      const entities = this.entity.getWorld().getEntities();
      this.affectedEntities.clear();

      entities.forEach((entity) => {
        if (entity === this.entity) return;

        const transform = entity.getTransform();
        if (!transform) return;

        const entityPosition = transform.getWorldPosition();
        const distance = position.distanceTo(entityPosition);

        if (distance <= this.heatDiffusionRange) {
          this.affectedEntities.add(entity);

          // 计算热效果强度
          const strength = 1 - (distance / this.heatDiffusionRange);
          this.applyHeatEffect(entity, strength, deltaTime);
        }
      });

      // 更新热扩散区域的视觉效果
      this.updateHeatDiffusionVisuals(deltaTime);
    } catch (error) {
      Debug.warn('HotSpringComponent', '热扩散更新失败:', error);
    }
  }

  /**
   * 应用热效果
   * @param entity 实体
   * @param strength 强度
   * @param deltaTime 帧间隔时间（秒）
   */
  private applyHeatEffect(entity: Entity, strength: number, deltaTime: number): void {
    // 获取实体的健康组件（如果有）
    const healthComponent = entity.getComponent('HealthComponent') as any;
    if (healthComponent && healthComponent.heal) {
      const recoveryAmount = strength * (this.hotSpringTemperature / 100) * deltaTime * 5;
      healthComponent.heal(recoveryAmount);
    }

    // 获取实体的状态组件（如果有）
    const statusComponent = entity.getComponent('StatusComponent') as any;
    if (statusComponent && statusComponent.addStatus) {
      statusComponent.addStatus('warm', strength * 10, this.hotSpringTemperature / 10);
    }
  }

  /**
   * 更新热扩散区域视觉效果
   * @param _deltaTime 帧间隔时间（秒）
   */
  private updateHeatDiffusionVisuals(_deltaTime: number): void {
    if (!this.heatDiffusionArea) return;

    const material = this.heatDiffusionArea.material as THREE.MeshBasicMaterial;
    if (material) {
      // 根据温度调整热扩散区域的颜色
      const hue = 0.05; // 红色偏黄
      const saturation = 0.6;
      const lightness = 0.5 + 0.2 * Math.sin(Date.now() * 0.001);
      material.color.setHSL(hue, saturation, lightness);
    }
  }

  /**
   * 更新音频
   * @param _deltaTime 帧间隔时间（秒）
   */
  private updateAudio(_deltaTime: number): void {
    if (!this.enableSoundEffect || !this.audioSource) {
      return;
    }

    try {
      // 更新音频源位置
      const position = this.getPosition();
      this.audioSource.setPosition(position.x, position.y, position.z);

      // 根据温泉温度调整音量
      const volumeFactor = Math.min(1.0, this.hotSpringTemperature / 80);
      this.audioSource.setVolume(this.soundEffectVolume * volumeFactor);
    } catch (error) {
      Debug.warn('HotSpringComponent', '音频更新失败:', error);
    }
  }

  /**
   * 更新波动效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaveEffects(deltaTime: number): void {
    this.hotSpringWaveTime += deltaTime;

    // 更新水面波动
    this.updateWaterSurfaceWaves(deltaTime);

    // 更新矿物质边缘波动
    this.updateMineralEdgeWaves(deltaTime);
  }

  /**
   * 更新水面波动
   * @param _deltaTime 帧间隔时间（秒）
   */
  private updateWaterSurfaceWaves(_deltaTime: number): void {
    // 应用波动参数到水体
    const waveParams = {
      amplitude: this.waveAmplitude * (this.hotSpringTemperature / 100),
      frequency: this.waveFrequency,
      speed: this.waveSpeed,
      direction: new THREE.Vector2(1, 1),
      time: this.hotSpringWaveTime
    };

    // 如果父类有setWaveParams方法，则调用
    if (typeof (this as any).setWaveParams === 'function') {
      (this as any).setWaveParams(waveParams);
    }
  }

  /**
   * 更新矿物质边缘波动
   * @param _deltaTime 帧间隔时间（秒）
   */
  private updateMineralEdgeWaves(_deltaTime: number): void {
    if (!this.mineralEdge) return;

    // 添加微妙的上下波动
    const waveOffset = Math.sin(this.hotSpringWaveTime * 2) * 0.02;
    this.mineralEdge.position.y = this.getPosition().y - this.getSize().height / 2 + 0.05 + waveOffset;
  }

  /**
   * 销毁温泉组件
   */
  public destroy(): void {
    try {
      // 停止并清理音频
      if (this.audioSource) {
        this.audioSource.stop();
        this.audioSource = null;
      }

      // 清理粒子系统
      this.destroyParticleSystems();

      // 清理矿物质边缘
      if (this.mineralEdge) {
        const renderComponent = this.entity.getComponent('RenderComponent') as any;
        if (renderComponent) {
          renderComponent.removeObject(this.mineralEdge);
        }
        (this.mineralEdge.geometry as any).dispose();
        (this.mineralEdge.material as THREE.Material).dispose();
        this.mineralEdge = null;
      }

      // 清理热扩散区域
      if (this.heatDiffusionArea) {
        const renderComponent = this.entity.getComponent('RenderComponent') as any;
        if (renderComponent) {
          renderComponent.removeObject(this.heatDiffusionArea);
        }
        (this.heatDiffusionArea.geometry as any).dispose();
        (this.heatDiffusionArea.material as THREE.Material).dispose();
        this.heatDiffusionArea = null;
      }

      // 清理影响的实体列表
      this.affectedEntities.clear();

      Debug.log('HotSpringComponent', '温泉组件已销毁');
    } catch (error) {
      Debug.warn('HotSpringComponent', '温泉组件销毁失败:', error);
    }
  }

  /**
   * 销毁粒子系统
   */
  private destroyParticleSystems(): void {
    try {
      // 清理气泡粒子
      for (const bubbleParticle of this.bubbleParticles) {
        const renderComponent = this.entity.getComponent('RenderComponent') as any;
        if (renderComponent) {
          renderComponent.removeObject(bubbleParticle);
        }
        bubbleParticle.geometry.dispose();
        (bubbleParticle.material as THREE.Material).dispose();
      }
      this.bubbleParticles = [];

      // 清理水蒸气粒子
      for (const steamParticle of this.steamParticles) {
        const renderComponent = this.entity.getComponent('RenderComponent') as any;
        if (renderComponent) {
          renderComponent.removeObject(steamParticle);
        }
        steamParticle.geometry.dispose();
        (steamParticle.material as THREE.Material).dispose();
      }
      this.steamParticles = [];

      Debug.log('HotSpringComponent', '粒子系统已清理');
    } catch (error) {
      Debug.warn('HotSpringComponent', '粒子系统清理失败:', error);
    }
  }

  /**
   * 获取温泉温度
   * @returns 温泉温度（摄氏度）
   */
  public getTemperature(): number {
    return this.hotSpringTemperature;
  }

  /**
   * 设置温泉温度
   * @param temperature 温泉温度（摄氏度）
   */
  public setTemperature(temperature: number): void {
    this.hotSpringTemperature = temperature;
  }

  /**
   * 获取波动强度
   * @returns 波动强度
   */
  public getWaveAmplitude(): number {
    return this.waveAmplitude;
  }

  /**
   * 设置波动强度
   * @param amplitude 波动强度
   */
  public setWaveAmplitude(amplitude: number): void {
    this.waveAmplitude = amplitude;
  }

  /**
   * 获取波动频率
   * @returns 波动频率
   */
  public getWaveFrequency(): number {
    return this.waveFrequency;
  }

  /**
   * 设置波动频率
   * @param frequency 波动频率
   */
  public setWaveFrequency(frequency: number): void {
    this.waveFrequency = frequency;
  }

  /**
   * 获取波动速度
   * @returns 波动速度
   */
  public getWaveSpeed(): number {
    return this.waveSpeed;
  }

  /**
   * 设置波动速度
   * @param speed 波动速度
   */
  public setWaveSpeed(speed: number): void {
    this.waveSpeed = speed;
  }

  /**
   * 获取温泉类型
   * @returns 温泉类型
   */
  public getHotSpringType(): HotSpringType {
    return this.hotSpringType;
  }

  /**
   * 设置温泉类型
   * @param type 温泉类型
   */
  public setHotSpringType(type: HotSpringType): void {
    this.hotSpringType = type;
  }
}
