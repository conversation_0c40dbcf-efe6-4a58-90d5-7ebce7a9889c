/**
 * 性能测试服务
 * 提供全面的性能测试功能，包括负载测试、压力测试、并发测试等
 */
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * 性能测试类型枚举
 */
export enum PerformanceTestType {
  /** 负载测试 */
  LOAD = 'load',
  /** 压力测试 */
  STRESS = 'stress',
  /** 并发测试 */
  CONCURRENCY = 'concurrency',
  /** 持久性测试 */
  ENDURANCE = 'endurance',
  /** 峰值测试 */
  SPIKE = 'spike',
  /** 容量测试 */
  CAPACITY = 'capacity',
  /** 可扩展性测试 */
  SCALABILITY = 'scalability',
  /** 网络测试 */
  NETWORK = 'network',
  /** 数据库测试 */
  DATABASE = 'database',
  /** 缓存测试 */
  CACHE = 'cache',
}

/**
 * 性能测试结果接口
 */
export interface PerformanceTestResult {
  /** 测试ID */
  id: string;
  /** 测试名称 */
  name: string;
  /** 测试类型 */
  type: PerformanceTestType;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
  /** 总耗时（毫秒） */
  duration: number;
  /** 总请求数 */
  totalRequests: number;
  /** 成功请求数 */
  successfulRequests: number;
  /** 失败请求数 */
  failedRequests: number;
  /** 每秒请求数 */
  requestsPerSecond: number;
  /** 最小响应时间（毫秒） */
  minResponseTime: number;
  /** 最大响应时间（毫秒） */
  maxResponseTime: number;
  /** 平均响应时间（毫秒） */
  avgResponseTime: number;
  /** 中位数响应时间（毫秒） */
  medianResponseTime: number;
  /** 90%响应时间（毫秒） */
  p90ResponseTime: number;
  /** 95%响应时间（毫秒） */
  p95ResponseTime: number;
  /** 99%响应时间（毫秒） */
  p99ResponseTime: number;
  /** 错误率 */
  errorRate: number;
  /** 并发用户数 */
  concurrentUsers: number;
  /** 内存使用（MB） */
  memoryUsage: number;
  /** CPU使用率（%） */
  cpuUsage: number;
  /** 网络吞吐量（KB/s） */
  networkThroughput: number;
  /** 测试参数 */
  parameters: Record<string, any>;
  /** 错误详情 */
  errors: Array<{
    /** 错误消息 */
    message: string;
    /** 错误代码 */
    code: string;
    /** 错误次数 */
    count: number;
  }>;
  /** 响应时间分布 */
  responseTimeDistribution: Array<{
    /** 范围开始（毫秒） */
    from: number;
    /** 范围结束（毫秒） */
    to: number;
    /** 请求数 */
    count: number;
  }>;
  /** 系统资源使用情况 */
  systemResources: {
    /** CPU使用率历史（%） */
    cpuHistory: number[];
    /** 内存使用历史（MB） */
    memoryHistory: number[];
    /** 网络吞吐量历史（KB/s） */
    networkHistory: number[];
  };
  /** 测试环境 */
  environment: {
    /** 操作系统 */
    os: string;
    /** CPU核心数 */
    cpuCores: number;
    /** 总内存（MB） */
    totalMemory: number;
    /** Node.js版本 */
    nodeVersion: string;
  };
}

/**
 * 性能测试配置接口
 */
export interface PerformanceTestConfig {
  /** 测试ID */
  id?: string;
  /** 测试名称 */
  name: string;
  /** 测试类型 */
  type: PerformanceTestType;
  /** 测试函数 */
  testFunction: (parameters: any) => Promise<any>;
  /** 测试参数 */
  parameters?: Record<string, any>;
  /** 并发用户数 */
  concurrentUsers?: number;
  /** 每秒请求数 */
  requestsPerSecond?: number;
  /** 测试持续时间（秒） */
  duration?: number;
  /** 预热时间（秒） */
  warmupDuration?: number;
  /** 最大请求数 */
  maxRequests?: number;
  /** 是否收集系统资源使用情况 */
  collectSystemResources?: boolean;
  /** 系统资源采样间隔（毫秒） */
  resourceSamplingInterval?: number;
  /** 是否生成报告 */
  generateReport?: boolean;
  /** 报告格式 */
  reportFormat?: 'json' | 'html' | 'csv';
  /** 报告输出目录 */
  reportOutputDir?: string;
  /** 是否启用调试日志 */
  debug?: boolean;
}

/**
 * 性能测试服务
 */
@Injectable()
export class PerformanceTestService {
  private readonly logger = new Logger(PerformanceTestService.name);

  // 当前运行的测试
  private runningTests = new Map<string, {
    config: PerformanceTestConfig;
    startTime: number;
    endTime?: number;
    responseTimes: number[];
    successCount: number;
    failureCount: number;
    errors: Map<string, { message: string; code: string; count: number }>;
    resourceSamplingInterval?: NodeJS.Timeout;
    cpuHistory: number[];
    memoryHistory: number[];
    networkHistory: number[];
    lastNetworkStats?: { rx: number; tx: number; timestamp: number };
  }>();

  constructor(private readonly eventEmitter: EventEmitter2) {}

  /**
   * 运行性能测试
   * @param config 测试配置
   * @returns 测试ID
   */
  async runTest(config: PerformanceTestConfig): Promise<string> {
    // 生成测试ID
    const testId = config.id || `test-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // 默认配置
    const defaultConfig: Partial<PerformanceTestConfig> = {
      concurrentUsers: 10,
      requestsPerSecond: 50,
      duration: 60,
      warmupDuration: 5,
      maxRequests: 10000,
      collectSystemResources: true,
      resourceSamplingInterval: 1000,
      generateReport: true,
      reportFormat: 'json',
      reportOutputDir: './reports',
      debug: false,
    };

    // 合并配置
    const mergedConfig: PerformanceTestConfig = {
      ...defaultConfig,
      ...config,
      id: testId,
    };

    // 检查测试ID是否已存在
    if (this.runningTests.has(testId)) {
      throw new Error(`测试ID ${testId} 已存在`);
    }

    // 初始化测试状态
    const testState = {
      config: mergedConfig,
      startTime: Date.now(),
      responseTimes: [],
      successCount: 0,
      failureCount: 0,
      errors: new Map<string, { message: string; code: string; count: number }>(),
      resourceSamplingInterval: undefined as NodeJS.Timeout | undefined,
      cpuHistory: [],
      memoryHistory: [],
      networkHistory: [],
      lastNetworkStats: undefined as { rx: number; tx: number; timestamp: number } | undefined,
    };

    this.runningTests.set(testId, testState);

    this.logger.log(`开始性能测试: ${mergedConfig.name} (ID: ${testId}), 类型: ${mergedConfig.type}`);

    // 发出测试开始事件
    this.eventEmitter.emit('performance.test.start', {
      id: testId,
      name: mergedConfig.name,
      type: mergedConfig.type,
      startTime: testState.startTime,
      parameters: mergedConfig.parameters,
    });

    // 如果启用系统资源监控，开始采样
    if (mergedConfig.collectSystemResources) {
      this.startResourceSampling(testId, mergedConfig.resourceSamplingInterval!);
    }

    try {
      // 根据测试类型执行不同的测试
      switch (mergedConfig.type) {
        case PerformanceTestType.LOAD:
          await this.runLoadTest(testId, mergedConfig);
          break;

        case PerformanceTestType.STRESS:
          await this.runStressTest(testId, mergedConfig);
          break;

        case PerformanceTestType.CONCURRENCY:
          await this.runConcurrencyTest(testId, mergedConfig);
          break;

        case PerformanceTestType.ENDURANCE:
          await this.runEnduranceTest(testId, mergedConfig);
          break;

        case PerformanceTestType.SPIKE:
          await this.runSpikeTest(testId, mergedConfig);
          break;

        default:
          await this.runGenericTest(testId, mergedConfig);
          break;
      }

      // 完成测试
      await this.finishTest(testId);

      return testId;
    } catch (error) {
      // 测试失败
      this.logger.error(`测试失败: ${error.message}`, error.stack);

      // 停止资源采样
      if (testState.resourceSamplingInterval) {
        clearInterval(testState.resourceSamplingInterval);
      }

      // 从运行测试中移除
      this.runningTests.delete(testId);

      // 发出测试失败事件
      this.eventEmitter.emit('performance.test.error', {
        id: testId,
        name: mergedConfig.name,
        type: mergedConfig.type,
        error: error.message,
        stack: error.stack,
      });

      throw error;
    }
  }

  /**
   * 获取测试结果
   * @param testId 测试ID
   * @returns 测试结果
   */
  getTestResult(testId: string): PerformanceTestResult | null {
    const testState = this.runningTests.get(testId);

    if (!testState || !testState.endTime) {
      return null;
    }

    return this.calculateTestResult(testId);
  }

  /**
   * 获取所有测试结果
   * @returns 测试结果数组
   */
  getAllTestResults(): PerformanceTestResult[] {
    const results: PerformanceTestResult[] = [];

    for (const [testId, testState] of this.runningTests.entries()) {
      if (testState.endTime) {
        const result = this.calculateTestResult(testId);
        if (result) {
          results.push(result);
        }
      }
    }

    return results;
  }

  /**
   * 开始资源采样
   * @param testId 测试ID
   * @param interval 采样间隔（毫秒）
   */
  private startResourceSampling(testId: string, interval: number): void {
    const testState = this.runningTests.get(testId)!;

    // 初始化网络统计
    testState.lastNetworkStats = this.getNetworkStats();

    // 设置采样定时器
    testState.resourceSamplingInterval = setInterval(() => {
      // 采样CPU使用率
      const cpuUsage = this.getCpuUsage();
      testState.cpuHistory.push(cpuUsage);

      // 采样内存使用
      const memoryUsage = this.getMemoryUsage();
      testState.memoryHistory.push(memoryUsage);

      // 采样网络吞吐量
      const networkThroughput = this.getNetworkThroughput(testState.lastNetworkStats!);
      testState.networkHistory.push(networkThroughput);

      // 更新上次网络统计
      testState.lastNetworkStats = this.getNetworkStats();
    }, interval);
  }

  /**
   * 获取CPU使用率
   * @returns CPU使用率（%）
   */
  private getCpuUsage(): number {
    // 获取CPU使用率
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type as keyof typeof cpu.times];
      }
      totalIdle += cpu.times.idle;
    }

    // 计算CPU使用率
    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const usage = 100 - (idle / total) * 100;

    return Math.round(usage * 100) / 100;
  }

  /**
   * 获取内存使用
   * @returns 内存使用（MB）
   */
  private getMemoryUsage(): number {
    const memoryUsage = process.memoryUsage();
    return Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100;
  }

  /**
   * 获取网络统计
   * @returns 网络统计
   */
  private getNetworkStats(): { rx: number; tx: number; timestamp: number } {
    // 获取网络接口统计
    const interfaces = os.networkInterfaces();
    let rx = 0;
    let tx = 0;

    // 累加所有接口的接收和发送字节数
    for (const iface of Object.values(interfaces)) {
      if (iface) {
        for (const info of iface) {
          if (info.internal) {
            continue;
          }

          // 这里只是一个示例，实际上需要使用系统特定的方法获取网络统计
          // 在这里我们使用随机值模拟
          rx += Math.random() * 1000;
          tx += Math.random() * 1000;
        }
      }
    }

    return {
      rx,
      tx,
      timestamp: Date.now(),
    };
  }

  /**
   * 获取网络吞吐量
   * @param lastStats 上次网络统计
   * @returns 网络吞吐量（KB/s）
   */
  private getNetworkThroughput(lastStats: { rx: number; tx: number; timestamp: number }): number {
    const currentStats = this.getNetworkStats();
    const elapsedTime = (currentStats.timestamp - lastStats.timestamp) / 1000; // 秒

    // 计算吞吐量（KB/s）
    const rxThroughput = (currentStats.rx - lastStats.rx) / 1024 / elapsedTime;
    const txThroughput = (currentStats.tx - lastStats.tx) / 1024 / elapsedTime;

    // 总吞吐量
    const throughput = rxThroughput + txThroughput;

    return Math.round(throughput * 100) / 100;
  }

  /**
   * 完成测试
   * @param testId 测试ID
   */
  private async finishTest(testId: string): Promise<void> {
    const testState = this.runningTests.get(testId)!;

    // 设置结束时间
    testState.endTime = Date.now();

    // 停止资源采样
    if (testState.resourceSamplingInterval) {
      clearInterval(testState.resourceSamplingInterval);
    }

    // 计算测试结果
    const result = this.calculateTestResult(testId);

    // 发出测试完成事件
    this.eventEmitter.emit('performance.test.complete', result);

    // 生成报告
    if (testState.config.generateReport) {
      await this.generateReport(testId, result!);
    }

    this.logger.log(`测试完成: ${testState.config.name} (ID: ${testId}), 总请求: ${result!.totalRequests}, 成功率: ${(result!.successfulRequests / result!.totalRequests * 100).toFixed(2)}%, 平均响应时间: ${result!.avgResponseTime.toFixed(2)}ms`);
  }

  /**
   * 计算测试结果
   * @param testId 测试ID
   * @returns 测试结果
   */
  private calculateTestResult(testId: string): PerformanceTestResult | null {
    const testState = this.runningTests.get(testId);

    if (!testState || !testState.endTime) {
      return null;
    }

    const { config, startTime, endTime, responseTimes, successCount, failureCount, errors, cpuHistory, memoryHistory, networkHistory } = testState;

    // 计算响应时间统计
    const sortedResponseTimes = [...responseTimes].sort((a, b) => a - b);
    const totalRequests = successCount + failureCount;

    // 计算百分位数
    const p90Index = Math.floor(sortedResponseTimes.length * 0.9);
    const p95Index = Math.floor(sortedResponseTimes.length * 0.95);
    const p99Index = Math.floor(sortedResponseTimes.length * 0.99);

    // 计算响应时间分布
    const responseTimeDistribution: Array<{ from: number; to: number; count: number }> = [];
    const distributionRanges = [0, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000];

    for (let i = 0; i < distributionRanges.length - 1; i++) {
      const from = distributionRanges[i];
      const to = distributionRanges[i + 1];
      const count = sortedResponseTimes.filter(time => time >= from && time < to).length;

      responseTimeDistribution.push({ from, to, count });
    }

    // 添加最后一个范围
    const lastFrom = distributionRanges[distributionRanges.length - 1];
    const lastCount = sortedResponseTimes.filter(time => time >= lastFrom).length;
    responseTimeDistribution.push({ from: lastFrom, to: Infinity, count: lastCount });

    // 计算系统资源平均值
    const avgCpuUsage = cpuHistory.length > 0
      ? cpuHistory.reduce((sum, value) => sum + value, 0) / cpuHistory.length
      : 0;

    const avgMemoryUsage = memoryHistory.length > 0
      ? memoryHistory.reduce((sum, value) => sum + value, 0) / memoryHistory.length
      : 0;

    const avgNetworkThroughput = networkHistory.length > 0
      ? networkHistory.reduce((sum, value) => sum + value, 0) / networkHistory.length
      : 0;

    // 转换错误Map为数组
    const errorArray = Array.from(errors.values());

    // 创建测试结果
    const result: PerformanceTestResult = {
      id: testId,
      name: config.name,
      type: config.type,
      startTime,
      endTime,
      duration: endTime - startTime,
      totalRequests,
      successfulRequests: successCount,
      failedRequests: failureCount,
      requestsPerSecond: totalRequests / ((endTime - startTime) / 1000),
      minResponseTime: sortedResponseTimes[0] || 0,
      maxResponseTime: sortedResponseTimes[sortedResponseTimes.length - 1] || 0,
      avgResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
      medianResponseTime: sortedResponseTimes[Math.floor(sortedResponseTimes.length / 2)] || 0,
      p90ResponseTime: sortedResponseTimes[p90Index] || 0,
      p95ResponseTime: sortedResponseTimes[p95Index] || 0,
      p99ResponseTime: sortedResponseTimes[p99Index] || 0,
      errorRate: totalRequests > 0 ? failureCount / totalRequests : 0,
      concurrentUsers: config.concurrentUsers!,
      memoryUsage: avgMemoryUsage,
      cpuUsage: avgCpuUsage,
      networkThroughput: avgNetworkThroughput,
      parameters: config.parameters || {},
      errors: errorArray,
      responseTimeDistribution,
      systemResources: {
        cpuHistory,
        memoryHistory,
        networkHistory,
      },
      environment: {
        os: `${os.type()} ${os.release()}`,
        cpuCores: os.cpus().length,
        totalMemory: Math.round(os.totalmem() / 1024 / 1024),
        nodeVersion: process.version,
      },
    };

    return result;
  }

  /**
   * 生成报告
   * @param testId 测试ID
   * @param result 测试结果
   */
  private async generateReport(testId: string, result: PerformanceTestResult): Promise<void> {
    const testState = this.runningTests.get(testId)!;
    const { reportFormat, reportOutputDir } = testState.config;

    // 创建输出目录
    if (!fs.existsSync(reportOutputDir!)) {
      fs.mkdirSync(reportOutputDir!, { recursive: true });
    }

    // 生成报告文件名
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
    const reportFileName = `${result.name.replace(/\s+/g, '-')}-${testId}-${timestamp}`;

    // 根据格式生成报告
    switch (reportFormat) {
      case 'json':
        await this.generateJsonReport(reportOutputDir!, reportFileName, result);
        break;

      case 'html':
        await this.generateHtmlReport(reportOutputDir!, reportFileName, result);
        break;

      case 'csv':
        await this.generateCsvReport(reportOutputDir!, reportFileName, result);
        break;
    }

    this.logger.log(`已生成测试报告: ${path.join(reportOutputDir!, reportFileName)}.${reportFormat}`);
  }

  /**
   * 生成JSON报告
   * @param outputDir 输出目录
   * @param fileName 文件名
   * @param result 测试结果
   */
  private async generateJsonReport(outputDir: string, fileName: string, result: PerformanceTestResult): Promise<void> {
    const reportPath = path.join(outputDir, `${fileName}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(result, null, 2));
  }

  /**
   * 生成HTML报告
   * @param outputDir 输出目录
   * @param fileName 文件名
   * @param result 测试结果
   */
  private async generateHtmlReport(outputDir: string, fileName: string, result: PerformanceTestResult): Promise<void> {
    const reportPath = path.join(outputDir, `${fileName}.html`);

    // 简单的HTML报告模板
    const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>性能测试报告 - ${result.name}</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1, h2 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .chart { width: 100%; height: 300px; margin-bottom: 20px; }
    .error { color: #d9534f; }
    .success { color: #5cb85c; }
  </style>
</head>
<body>
  <h1>性能测试报告</h1>

  <h2>测试信息</h2>
  <table>
    <tr><th>测试名称</th><td>${result.name}</td></tr>
    <tr><th>测试ID</th><td>${result.id}</td></tr>
    <tr><th>测试类型</th><td>${result.type}</td></tr>
    <tr><th>开始时间</th><td>${new Date(result.startTime).toLocaleString()}</td></tr>
    <tr><th>结束时间</th><td>${new Date(result.endTime).toLocaleString()}</td></tr>
    <tr><th>持续时间</th><td>${(result.duration / 1000).toFixed(2)} 秒</td></tr>
    <tr><th>并发用户数</th><td>${result.concurrentUsers}</td></tr>
  </table>

  <h2>测试结果摘要</h2>
  <table>
    <tr><th>总请求数</th><td>${result.totalRequests}</td></tr>
    <tr><th>成功请求数</th><td>${result.successfulRequests}</td></tr>
    <tr><th>失败请求数</th><td>${result.failedRequests}</td></tr>
    <tr><th>每秒请求数</th><td>${result.requestsPerSecond.toFixed(2)}</td></tr>
    <tr><th>错误率</th><td>${(result.errorRate * 100).toFixed(2)}%</td></tr>
  </table>

  <h2>响应时间</h2>
  <table>
    <tr><th>最小响应时间</th><td>${result.minResponseTime.toFixed(2)} ms</td></tr>
    <tr><th>最大响应时间</th><td>${result.maxResponseTime.toFixed(2)} ms</td></tr>
    <tr><th>平均响应时间</th><td>${result.avgResponseTime.toFixed(2)} ms</td></tr>
    <tr><th>中位数响应时间</th><td>${result.medianResponseTime.toFixed(2)} ms</td></tr>
    <tr><th>90%响应时间</th><td>${result.p90ResponseTime.toFixed(2)} ms</td></tr>
    <tr><th>95%响应时间</th><td>${result.p95ResponseTime.toFixed(2)} ms</td></tr>
    <tr><th>99%响应时间</th><td>${result.p99ResponseTime.toFixed(2)} ms</td></tr>
  </table>

  <h2>系统资源</h2>
  <table>
    <tr><th>CPU使用率</th><td>${result.cpuUsage.toFixed(2)}%</td></tr>
    <tr><th>内存使用</th><td>${result.memoryUsage.toFixed(2)} MB</td></tr>
    <tr><th>网络吞吐量</th><td>${result.networkThroughput.toFixed(2)} KB/s</td></tr>
  </table>

  <h2>错误详情</h2>
  <table>
    <tr><th>错误代码</th><th>错误消息</th><th>次数</th></tr>
    ${result.errors.map(error => `
      <tr>
        <td>${error.code}</td>
        <td>${error.message}</td>
        <td>${error.count}</td>
      </tr>
    `).join('')}
  </table>

  <h2>响应时间分布</h2>
  <table>
    <tr><th>响应时间范围 (ms)</th><th>请求数</th><th>百分比</th></tr>
    ${result.responseTimeDistribution.map(dist => `
      <tr>
        <td>${dist.from} - ${dist.to === Infinity ? '∞' : dist.to}</td>
        <td>${dist.count}</td>
        <td>${(dist.count / result.totalRequests * 100).toFixed(2)}%</td>
      </tr>
    `).join('')}
  </table>

  <h2>测试环境</h2>
  <table>
    <tr><th>操作系统</th><td>${result.environment.os}</td></tr>
    <tr><th>CPU核心数</th><td>${result.environment.cpuCores}</td></tr>
    <tr><th>总内存</th><td>${result.environment.totalMemory} MB</td></tr>
    <tr><th>Node.js版本</th><td>${result.environment.nodeVersion}</td></tr>
  </table>
</body>
</html>
    `;

    fs.writeFileSync(reportPath, html);
  }

  /**
   * 生成CSV报告
   * @param outputDir 输出目录
   * @param fileName 文件名
   * @param result 测试结果
   */
  private async generateCsvReport(outputDir: string, fileName: string, result: PerformanceTestResult): Promise<void> {
    const reportPath = path.join(outputDir, `${fileName}.csv`);

    // 创建CSV内容
    const csvRows = [
      ['测试名称', result.name],
      ['测试ID', result.id],
      ['测试类型', result.type],
      ['开始时间', new Date(result.startTime).toLocaleString()],
      ['结束时间', new Date(result.endTime).toLocaleString()],
      ['持续时间 (秒)', (result.duration / 1000).toFixed(2)],
      ['并发用户数', result.concurrentUsers.toString()],
      ['总请求数', result.totalRequests.toString()],
      ['成功请求数', result.successfulRequests.toString()],
      ['失败请求数', result.failedRequests.toString()],
      ['每秒请求数', result.requestsPerSecond.toFixed(2)],
      ['错误率', (result.errorRate * 100).toFixed(2) + '%'],
      ['最小响应时间 (ms)', result.minResponseTime.toFixed(2)],
      ['最大响应时间 (ms)', result.maxResponseTime.toFixed(2)],
      ['平均响应时间 (ms)', result.avgResponseTime.toFixed(2)],
      ['中位数响应时间 (ms)', result.medianResponseTime.toFixed(2)],
      ['90%响应时间 (ms)', result.p90ResponseTime.toFixed(2)],
      ['95%响应时间 (ms)', result.p95ResponseTime.toFixed(2)],
      ['99%响应时间 (ms)', result.p99ResponseTime.toFixed(2)],
      ['CPU使用率 (%)', result.cpuUsage.toFixed(2)],
      ['内存使用 (MB)', result.memoryUsage.toFixed(2)],
      ['网络吞吐量 (KB/s)', result.networkThroughput.toFixed(2)],
    ];

    // 转换为CSV字符串
    const csvContent = csvRows.map(row => row.join(',')).join('\n');

    fs.writeFileSync(reportPath, csvContent);
  }

  /**
   * 运行负载测试
   * @param testId 测试ID
   * @param config 测试配置
   */
  private async runLoadTest(testId: string, config: PerformanceTestConfig): Promise<void> {
    const testState = this.runningTests.get(testId)!;
    const { testFunction, parameters, concurrentUsers, duration, warmupDuration } = config;

    this.logger.log(`开始负载测试: 并发用户数=${concurrentUsers}, 持续时间=${duration}秒`);

    // 预热阶段
    if (warmupDuration && warmupDuration > 0) {
      this.logger.log(`开始预热阶段: ${warmupDuration}秒`);
      await this.runTestPhase(testId, testFunction, parameters, Math.min(concurrentUsers! / 2, 5), warmupDuration * 1000, false);
      this.logger.log('预热阶段完成');
    }

    // 主测试阶段
    await this.runTestPhase(testId, testFunction, parameters, concurrentUsers!, duration! * 1000, true);
  }

  /**
   * 运行压力测试
   * @param testId 测试ID
   * @param config 测试配置
   */
  private async runStressTest(testId: string, config: PerformanceTestConfig): Promise<void> {
    const testState = this.runningTests.get(testId)!;
    const { testFunction, parameters, duration } = config;

    this.logger.log(`开始压力测试: 逐步增加负载`);

    // 压力测试：逐步增加并发用户数
    const maxUsers = config.concurrentUsers || 100;
    const stepDuration = (duration! * 1000) / 5; // 分5个阶段
    const userStep = Math.ceil(maxUsers / 5);

    for (let step = 1; step <= 5; step++) {
      const currentUsers = Math.min(userStep * step, maxUsers);
      this.logger.log(`压力测试阶段 ${step}/5: 并发用户数=${currentUsers}`);
      await this.runTestPhase(testId, testFunction, parameters, currentUsers, stepDuration, true);
    }
  }

  /**
   * 运行并发测试
   * @param testId 测试ID
   * @param config 测试配置
   */
  private async runConcurrencyTest(testId: string, config: PerformanceTestConfig): Promise<void> {
    const testState = this.runningTests.get(testId)!;
    const { testFunction, parameters, concurrentUsers, duration } = config;

    this.logger.log(`开始并发测试: 并发用户数=${concurrentUsers}`);

    // 并发测试：固定并发用户数
    await this.runTestPhase(testId, testFunction, parameters, concurrentUsers!, duration! * 1000, true);
  }

  /**
   * 运行持久性测试
   * @param testId 测试ID
   * @param config 测试配置
   */
  private async runEnduranceTest(testId: string, config: PerformanceTestConfig): Promise<void> {
    const testState = this.runningTests.get(testId)!;
    const { testFunction, parameters, concurrentUsers, duration } = config;

    this.logger.log(`开始持久性测试: 并发用户数=${concurrentUsers}, 持续时间=${duration}秒`);

    // 持久性测试：长时间运行
    await this.runTestPhase(testId, testFunction, parameters, concurrentUsers!, duration! * 1000, true);
  }

  /**
   * 运行峰值测试
   * @param testId 测试ID
   * @param config 测试配置
   */
  private async runSpikeTest(testId: string, config: PerformanceTestConfig): Promise<void> {
    const testState = this.runningTests.get(testId)!;
    const { testFunction, parameters, concurrentUsers, duration } = config;

    this.logger.log(`开始峰值测试: 突发负载`);

    const normalUsers = Math.ceil(concurrentUsers! / 4);
    const spikeUsers = concurrentUsers!;
    const phaseDuration = (duration! * 1000) / 3;

    // 阶段1：正常负载
    this.logger.log(`峰值测试阶段 1/3: 正常负载 (${normalUsers} 用户)`);
    await this.runTestPhase(testId, testFunction, parameters, normalUsers, phaseDuration, true);

    // 阶段2：峰值负载
    this.logger.log(`峰值测试阶段 2/3: 峰值负载 (${spikeUsers} 用户)`);
    await this.runTestPhase(testId, testFunction, parameters, spikeUsers, phaseDuration, true);

    // 阶段3：恢复到正常负载
    this.logger.log(`峰值测试阶段 3/3: 恢复负载 (${normalUsers} 用户)`);
    await this.runTestPhase(testId, testFunction, parameters, normalUsers, phaseDuration, true);
  }

  /**
   * 运行通用测试
   * @param testId 测试ID
   * @param config 测试配置
   */
  private async runGenericTest(testId: string, config: PerformanceTestConfig): Promise<void> {
    const testState = this.runningTests.get(testId)!;
    const { testFunction, parameters, concurrentUsers, duration } = config;

    this.logger.log(`开始通用测试: 并发用户数=${concurrentUsers}, 持续时间=${duration}秒`);

    // 通用测试：基本的负载测试
    await this.runTestPhase(testId, testFunction, parameters, concurrentUsers!, duration! * 1000, true);
  }

  /**
   * 运行测试阶段
   * @param testId 测试ID
   * @param testFunction 测试函数
   * @param parameters 测试参数
   * @param concurrentUsers 并发用户数
   * @param duration 持续时间（毫秒）
   * @param recordResults 是否记录结果
   */
  private async runTestPhase(
    testId: string,
    testFunction: (parameters: any) => Promise<any>,
    parameters: any,
    concurrentUsers: number,
    duration: number,
    recordResults: boolean
  ): Promise<void> {
    const testState = this.runningTests.get(testId)!;
    const startTime = Date.now();
    const endTime = startTime + duration;

    // 创建并发任务
    const tasks: Promise<void>[] = [];

    for (let i = 0; i < concurrentUsers; i++) {
      const task = this.runUserSession(testId, testFunction, parameters, endTime, recordResults);
      tasks.push(task);
    }

    // 等待所有任务完成或超时
    await Promise.allSettled(tasks);
  }

  /**
   * 运行用户会话
   * @param testId 测试ID
   * @param testFunction 测试函数
   * @param parameters 测试参数
   * @param endTime 结束时间
   * @param recordResults 是否记录结果
   */
  private async runUserSession(
    testId: string,
    testFunction: (parameters: any) => Promise<any>,
    parameters: any,
    endTime: number,
    recordResults: boolean
  ): Promise<void> {
    const testState = this.runningTests.get(testId)!;

    while (Date.now() < endTime) {
      const requestStartTime = Date.now();

      try {
        // 执行测试函数
        await testFunction(parameters);

        // 记录成功
        if (recordResults) {
          const responseTime = Date.now() - requestStartTime;
          testState.responseTimes.push(responseTime);
          testState.successCount++;
        }
      } catch (error) {
        // 记录失败
        if (recordResults) {
          const responseTime = Date.now() - requestStartTime;
          testState.responseTimes.push(responseTime);
          testState.failureCount++;

          // 记录错误
          const errorKey = `${error.name || 'Error'}: ${error.message}`;
          const existingError = testState.errors.get(errorKey);
          if (existingError) {
            existingError.count++;
          } else {
            testState.errors.set(errorKey, {
              message: error.message,
              code: error.name || 'Error',
              count: 1,
            });
          }
        }
      }

      // 短暂延迟以避免过度占用CPU
      await new Promise(resolve => setTimeout(resolve, 1));
    }
  }
}
