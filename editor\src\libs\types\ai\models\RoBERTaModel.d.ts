/**
 * RoBERTa模型
 * 用于情感分析、文本分类等任务
 */
import { AIModelType } from '../AIModelType';
import { AIModelConfig } from '../AIModelConfig';
import { IAIModel, TextGenerationOptions, TextClassificationResult, EmotionAnalysisResult } from './IAIModel';
/**
 * RoBERTa模型配置
 */
export interface RoBERTaModelConfig extends AIModelConfig {
    /** 模型变体 */
    variant?: 'base' | 'large' | 'distilled';
    /** 是否使用多标签分类 */
    useMultiLabel?: boolean;
    /** 情感类别 */
    emotionCategories?: string[];
    /** 置信度阈值 */
    confidenceThreshold?: number;
}
/**
 * RoBERTa模型
 */
export declare class RoBERTaModel implements IAIModel {
    /** 模型类型 */
    private readonly modelType;
    /** 模型配置 */
    private config;
    /** 全局配置 */
    private globalConfig;
    /** 是否已初始化 */
    private initialized;
    /** 是否正在初始化 */
    private initializing;
    /** 模型（仅用于类型安全） */
    private model;
    /** 分词器（仅用于类型安全） */
    private tokenizer;
    /** 事件发射器 */
    private eventEmitter;
    /** 获取模型实例（仅用于内部使用） */
    private getModelInstance;
    /** 获取分词器实例（仅用于内部使用） */
    private getTokenizerInstance;
    /** 默认情感类别 */
    private static readonly DEFAULT_EMOTION_CATEGORIES;
    /**
     * 构造函数
     * @param config 模型配置
     * @param globalConfig 全局配置
     */
    constructor(config?: RoBERTaModelConfig, globalConfig?: any);
    /**
     * 获取模型类型
     * @returns 模型类型
     */
    getType(): AIModelType;
    /**
     * 获取模型配置
     * @returns 模型配置
     */
    getConfig(): AIModelConfig;
    /**
     * 初始化模型
     * @returns 是否成功初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 生成文本
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 生成的文本
     */
    generateText(_prompt: string, _options?: TextGenerationOptions): Promise<string>;
    /**
     * 分类文本
     * @param text 要分类的文本
     * @param categories 分类类别
     * @returns 分类结果
     */
    classifyText(text: string, _categories?: string[]): Promise<TextClassificationResult>;
    /**
     * 分析情感
     * @param text 要分析的文本
     * @returns 情感分析结果
     */
    analyzeEmotion(text: string): Promise<EmotionAnalysisResult>;
    /**
     * 模拟情感分析
     * @param text 文本
     * @returns 情感分析结果
     */
    private mockAnalyzeEmotion;
    /**
     * 模拟预测
     * @param input 输入
     * @returns 预测结果
     */
    private mockPredict;
    /**
     * 销毁模型
     */
    dispose(): void;
}
