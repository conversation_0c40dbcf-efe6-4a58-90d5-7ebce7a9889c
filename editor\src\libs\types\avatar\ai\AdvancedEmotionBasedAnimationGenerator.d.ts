import { AnimationGenerationRequest, AnimationGenerationResult } from './AnimationGenerationTypes';
/**
 * 情感变化点
 */
export interface EmotionChangePoint {
    /** 时间点（0-1范围内的相对时间） */
    time: number;
    /** 情感类型 */
    emotion: string;
    /** 情感强度 */
    intensity: number;
}
/**
 * 情感分析结果
 */
export interface EmotionAnalysisResult {
    /** 主要情感 */
    primaryEmotion: string;
    /** 主要情感强度 */
    primaryIntensity: number;
    /** 次要情感 */
    secondaryEmotion?: string;
    /** 次要情感强度 */
    secondaryIntensity?: number;
    /** 情感变化点 */
    emotionChanges?: EmotionChangePoint[];
    /** 情感分数 */
    scores?: Record<string, number>;
    /** 置信度 */
    confidence?: number;
    /** 详细情感数据 */
    detailedEmotions?: Record<string, any>;
}
/**
 * 高级情感动画生成器配置
 */
export interface AdvancedEmotionBasedAnimationGeneratorConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用本地模型 */
    useLocalModel?: boolean;
    /** 模型路径 */
    modelPath?: string;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 关键帧密度（每秒关键帧数） */
    keyframeDensity?: number;
    /** 是否启用表情混合 */
    enableExpressionBlending?: boolean;
    /** 是否启用微表情 */
    enableMicroExpressions?: boolean;
    /** 是否启用情感过渡 */
    enableEmotionTransitions?: boolean;
    /** 是否启用自然变化 */
    enableNaturalVariation?: boolean;
    /** 模型类型 */
    modelType?: 'bert' | 'roberta' | 'distilbert' | 'albert' | 'xlnet' | 'custom';
    /** 模型变体 */
    modelVariant?: string;
    /** 是否使用量化模型 */
    useQuantized?: boolean;
    /** 量化位数 */
    quantizationBits?: 8 | 16 | 32;
    /** 批处理大小 */
    batchSize?: number;
    /** 情感类别 */
    emotionCategories?: string[];
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存大小 */
    cacheSize?: number;
    /** 是否使用上下文 */
    useContext?: boolean;
    /** 上下文窗口大小 */
    contextWindowSize?: number;
    /** 微表情配置 */
    microExpressionConfig?: any;
    /** 混合配置 */
    blendConfig?: any;
    /** 是否使用物理模拟 */
    usePhysics?: boolean;
    /** 物理参数 */
    physicsParams?: Record<string, any>;
}
/**
 * 高级情感动画生成器
 */
export declare class AdvancedEmotionBasedAnimationGenerator {
    /** 配置 */
    private config;
    /** AI模型 */
    private aiModel;
    /** 是否已初始化 */
    private initialized;
    /** 是否启用调试 */
    private debug;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: AdvancedEmotionBasedAnimationGeneratorConfig);
    /**
     * 初始化
     * @returns 是否成功初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 生成面部动画
     * @param request 生成请求
     * @returns 生成结果
     */
    generateFacialAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    /**
     * 创建高级情感面部动画片段
     * @param request 请求
     * @param emotionResult 情感分析结果
     * @returns 面部动画片段
     */
    private createAdvancedEmotionBasedFacialClip;
    /**
     * 添加表情关键帧
     * @param clip 动画片段
     * @param request 请求
     * @param emotionResult 情感分析结果
     */
    private addExpressionKeyframes;
    /**
     * 添加口型关键帧
     * @param clip 动画片段
     * @param request 请求
     */
    private addVisemeKeyframes;
    /**
     * 为单词生成口型序列
     * @param clip 动画片段
     * @param word 单词
     * @param startTime 开始时间
     * @param duration 持续时间
     */
    private generateVisemeSequenceForWord;
    /**
     * 添加自然变化
     * @param clip 动画片段
     * @param request 请求
     */
    private addNaturalVariation;
    /**
     * 添加眨眼
     * @param clip 动画片段
     * @param duration 持续时间
     */
    private addBlinking;
    /**
     * 添加头部运动
     * @param clip 动画片段
     * @param duration 持续时间
     */
    private addHeadMovement;
    /**
     * 添加微表情
     * @param clip 动画片段
     * @param request 请求
     * @param emotionResult 情感分析结果
     */
    private addMicroExpressions;
    /**
     * 获取情感对应的微表情
     * @param emotion 情感
     * @returns 微表情混合形状名称
     */
    private getMicroExpressionForEmotion;
    /**
     * 获取情感对应的表情
     * @param emotion 情感
     * @returns 表情
     */
    private getExpressionForEmotion;
}
