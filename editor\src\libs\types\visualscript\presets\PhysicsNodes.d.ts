import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 射线检测节点
 * 执行物理射线检测
 */
export declare class RaycastNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 应用力节点
 * 向物理体应用力
 */
export declare class ApplyForceNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 碰撞检测节点
 * 检测两个实体之间的碰撞
 */
export declare class CollisionDetectionNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 物理约束节点
 * 创建物理约束
 */
export declare class CreateConstraintNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 物理材质节点
 * 创建物理材质
 */
export declare class CreatePhysicsMaterialNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 注册物理节点
 * @param registry 节点注册表
 */
export declare function registerPhysicsNodes(registry: NodeRegistry): void;
