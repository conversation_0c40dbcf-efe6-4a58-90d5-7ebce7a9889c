/**
 * 用户管理器
 * 负责管理用户信息、状态和光标位置
 */
export class UserManager {
  /**
   * 构造函数
   */
  constructor() {
    this.users = new Map(); // 用户ID -> 用户对象
    this.currentUser = null; // 当前用户
    this.cursors = new Map(); // 用户ID -> 光标元素
    this.eventListeners = new Map(); // 事件类型 -> 监听器数组
    
    // 初始化用户列表UI
    this.initializeUserListUI();
    
    // 设置鼠标移动监听器
    this.setupMouseMoveListener();
  }
  
  /**
   * 设置当前用户
   * @param {Object} user 用户对象
   */
  setCurrentUser(user) {
    this.currentUser = user;
    this.addUser(user);
    this.emit('currentUserChanged', user);
  }
  
  /**
   * 清除当前用户
   */
  clearCurrentUser() {
    this.currentUser = null;
    this.emit('currentUserChanged', null);
  }
  
  /**
   * 添加用户
   * @param {Object} user 用户对象
   */
  addUser(user) {
    // 添加用户到用户列表
    this.users.set(user.id, {
      ...user,
      online: true,
      lastActivity: Date.now()
    });
    
    // 创建用户光标
    this.createUserCursor(user);
    
    // 更新用户列表UI
    this.updateUserListUI();
    
    // 触发用户添加事件
    this.emit('userAdded', user);
  }
  
  /**
   * 移除用户
   * @param {string} userId 用户ID
   */
  removeUser(userId) {
    // 从用户列表中移除用户
    const user = this.users.get(userId);
    if (user) {
      this.users.delete(userId);
      
      // 移除用户光标
      this.removeUserCursor(userId);
      
      // 更新用户列表UI
      this.updateUserListUI();
      
      // 触发用户移除事件
      this.emit('userRemoved', user);
    }
  }
  
  /**
   * 更新用户状态
   * @param {string} userId 用户ID
   * @param {boolean} online 是否在线
   */
  updateUserStatus(userId, online) {
    const user = this.users.get(userId);
    if (user) {
      user.online = online;
      user.lastActivity = Date.now();
      
      // 更新用户列表UI
      this.updateUserListUI();
      
      // 触发用户状态更新事件
      this.emit('userStatusUpdated', { userId, online });
    }
  }
  
  /**
   * 更新用户光标位置
   * @param {string} userId 用户ID
   * @param {Object} position 位置对象 { x, y }
   */
  updateUserCursor(userId, position) {
    const user = this.users.get(userId);
    if (user) {
      // 更新用户最后活动时间
      user.lastActivity = Date.now();
      
      // 更新光标位置
      const cursor = this.cursors.get(userId);
      if (cursor) {
        cursor.style.left = `${position.x}px`;
        cursor.style.top = `${position.y}px`;
        cursor.style.display = 'block';
        
        // 设置光标隐藏定时器
        if (cursor.hideTimeout) {
          clearTimeout(cursor.hideTimeout);
        }
        
        cursor.hideTimeout = setTimeout(() => {
          cursor.style.display = 'none';
        }, 5000);
      }
    }
  }
  
  /**
   * 获取用户
   * @param {string} userId 用户ID
   * @returns {Object|null} 用户对象
   */
  getUserById(userId) {
    return this.users.get(userId) || null;
  }
  
  /**
   * 获取所有用户
   * @returns {Array} 用户数组
   */
  getAllUsers() {
    return Array.from(this.users.values());
  }
  
  /**
   * 获取在线用户
   * @returns {Array} 在线用户数组
   */
  getOnlineUsers() {
    return this.getAllUsers().filter(user => user.online);
  }
  
  /**
   * 获取在线用户数量
   * @returns {number} 在线用户数量
   */
  getOnlineUsersCount() {
    return this.getOnlineUsers().length;
  }
  
  /**
   * 创建用户光标
   * @param {Object} user 用户对象
   */
  createUserCursor(user) {
    // 如果是当前用户，不创建光标
    if (this.currentUser && user.id === this.currentUser.id) {
      return;
    }
    
    // 创建光标元素
    const cursor = document.createElement('div');
    cursor.className = 'user-cursor';
    cursor.innerHTML = `
      <svg class="cursor-pointer" width="20" height="20" viewBox="0 0 20 20">
        <path d="M5,2 L18,15 L12,15 L8,19 L8,15 L5,15 Z" fill="${this.getUserColor(user.id)}" stroke="white" stroke-width="1" />
      </svg>
      <span class="cursor-label" style="background-color: ${this.getUserColor(user.id)}">${user.username}</span>
    `;
    cursor.style.display = 'none';
    
    // 添加到视口
    const viewport = document.getElementById('viewport');
    viewport.appendChild(cursor);
    
    // 保存光标元素
    this.cursors.set(user.id, cursor);
  }
  
  /**
   * 移除用户光标
   * @param {string} userId 用户ID
   */
  removeUserCursor(userId) {
    const cursor = this.cursors.get(userId);
    if (cursor) {
      // 移除光标元素
      cursor.parentNode.removeChild(cursor);
      
      // 清除定时器
      if (cursor.hideTimeout) {
        clearTimeout(cursor.hideTimeout);
      }
      
      // 从光标列表中移除
      this.cursors.delete(userId);
    }
  }
  
  /**
   * 获取用户颜色
   * @param {string} userId 用户ID
   * @returns {string} 颜色代码
   */
  getUserColor(userId) {
    // 根据用户ID生成一个固定的颜色
    const colors = [
      '#1890ff', // 蓝色
      '#52c41a', // 绿色
      '#fa8c16', // 橙色
      '#f5222d', // 红色
      '#722ed1', // 紫色
      '#13c2c2', // 青色
      '#eb2f96', // 粉色
      '#faad14', // 黄色
    ];
    
    // 使用用户ID的哈希值来选择颜色
    const hash = userId.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);
    
    return colors[Math.abs(hash) % colors.length];
  }
  
  /**
   * 初始化用户列表UI
   */
  initializeUserListUI() {
    const usersList = document.getElementById('users-list');
    if (usersList) {
      usersList.innerHTML = '<div class="no-users-message">没有在线用户</div>';
    }
  }
  
  /**
   * 更新用户列表UI
   */
  updateUserListUI() {
    const usersList = document.getElementById('users-list');
    if (usersList) {
      // 获取所有用户
      const users = this.getAllUsers();
      
      if (users.length === 0) {
        usersList.innerHTML = '<div class="no-users-message">没有在线用户</div>';
        return;
      }
      
      // 清空用户列表
      usersList.innerHTML = '';
      
      // 添加用户项
      users.forEach(user => {
        const userItem = document.createElement('div');
        userItem.className = 'user-item';
        userItem.innerHTML = `
          <img src="${user.avatar}" alt="${user.username}" class="user-avatar">
          <div class="user-info">
            <div class="user-name">${user.username}</div>
            <div class="user-role">${this.getRoleName(user.role)}</div>
          </div>
          <div class="user-status ${user.online ? 'online' : 'offline'}"></div>
        `;
        
        // 如果是当前用户，添加标记
        if (this.currentUser && user.id === this.currentUser.id) {
          userItem.classList.add('current-user');
        }
        
        usersList.appendChild(userItem);
      });
    }
  }
  
  /**
   * 获取角色名称
   * @param {string} role 角色代码
   * @returns {string} 角色名称
   */
  getRoleName(role) {
    switch (role) {
      case 'owner':
        return '所有者';
      case 'admin':
        return '管理员';
      case 'editor':
        return '编辑者';
      case 'viewer':
        return '查看者';
      default:
        return role;
    }
  }
  
  /**
   * 设置鼠标移动监听器
   */
  setupMouseMoveListener() {
    const viewport = document.getElementById('viewport');
    if (viewport) {
      viewport.addEventListener('mousemove', (event) => {
        if (this.currentUser) {
          // 计算相对于视口的位置
          const rect = viewport.getBoundingClientRect();
          const x = event.clientX - rect.left;
          const y = event.clientY - rect.top;
          
          // 触发鼠标移动事件
          this.emit('mouseMoved', { x, y });
        }
      });
    }
  }
  
  /**
   * 添加事件监听器
   * @param {string} event 事件类型
   * @param {Function} listener 监听器函数
   */
  on(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    
    this.eventListeners.get(event).push(listener);
  }
  
  /**
   * 移除事件监听器
   * @param {string} event 事件类型
   * @param {Function} listener 监听器函数
   */
  off(event, listener) {
    if (!this.eventListeners.has(event)) {
      return;
    }
    
    const listeners = this.eventListeners.get(event);
    const index = listeners.indexOf(listener);
    
    if (index !== -1) {
      listeners.splice(index, 1);
    }
  }
  
  /**
   * 触发事件
   * @param {string} event 事件类型
   * @param {*} data 事件数据
   */
  emit(event, data) {
    if (!this.eventListeners.has(event)) {
      return;
    }
    
    const listeners = this.eventListeners.get(event);
    
    for (const listener of listeners) {
      listener(data);
    }
  }
}
