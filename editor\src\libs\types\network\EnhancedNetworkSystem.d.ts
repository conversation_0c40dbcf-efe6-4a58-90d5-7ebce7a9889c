/**
 * 增强型网络系统
 * 集成了预测和插值、空间分区、网络质量监控和带宽控制等高级功能
 */
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { UserRole } from './UserSessionManager';
import { NetworkQualityData } from './NetworkQualityMonitor';
import { CompressionAlgorithm, CompressionLevel } from './DataCompressor';
import { ServiceDiscoveryClient } from './ServiceDiscoveryClient';
import { PredictionAlgorithm } from './NetworkPredictor';
import { AdaptiveStrategy } from './NetworkAdaptiveController';
import { BandwidthControlStrategy } from './AdvancedBandwidthController';
import { NetworkState } from './types';
export declare enum NetworkEntityType {
    STATIC = "static",
    DYNAMIC = "dynamic",
    PLAYER = "player",
    NPC = "npc",
    OBJECT = "object"
}
export declare enum NetworkEntitySyncMode {
    FULL = "full",
    DELTA = "delta",
    PRIORITY = "priority",
    SPATIAL = "spatial"
}
/**
 * 增强型网络系统配置
 */
export interface EnhancedNetworkSystemConfig {
    /** 是否启用网络系统 */
    enabled?: boolean;
    /** 本地用户ID */
    localUserId?: string;
    /** 同步间隔（毫秒） */
    syncInterval?: number;
    /** 最大重连尝试次数 */
    maxReconnectAttempts?: number;
    /** 重连间隔（毫秒） */
    reconnectInterval?: number;
    /** 是否启用压缩 */
    enableCompression?: boolean;
    /** 压缩算法 */
    compressionAlgorithm?: CompressionAlgorithm;
    /** 压缩级别 */
    compressionLevel?: CompressionLevel;
    /** 是否启用媒体流 */
    enableMediaStream?: boolean;
    /** 是否启用音频 */
    enableAudio?: boolean;
    /** 是否启用视频 */
    enableVideo?: boolean;
    /** 是否启用屏幕共享 */
    enableScreenShare?: boolean;
    /** 是否启用网络质量监控 */
    enableNetworkQualityMonitor?: boolean;
    /** 是否启用带宽控制 */
    enableBandwidthControl?: boolean;
    /** 带宽控制策略 */
    bandwidthControlStrategy?: BandwidthControlStrategy;
    /** 最大上传带宽（字节/秒） */
    maxUploadBandwidth?: number;
    /** 最大下载带宽（字节/秒） */
    maxDownloadBandwidth?: number;
    /** 是否启用实体同步 */
    enableEntitySync?: boolean;
    /** 是否启用用户会话管理 */
    enableUserSessionManagement?: boolean;
    /** 默认用户角色 */
    defaultUserRole?: UserRole;
    /** 是否启用权限检查 */
    enablePermissionCheck?: boolean;
    /** 是否启用事件缓冲 */
    enableEventBuffer?: boolean;
    /** 是否启用事件日志 */
    enableEventLogging?: boolean;
    /** 是否启用服务发现 */
    enableServiceDiscovery?: boolean;
    /** 服务注册URL */
    serviceRegistryUrl?: string;
    /** 是否启用微服务客户端 */
    enableMicroserviceClient?: boolean;
    /** API网关URL */
    apiGatewayUrl?: string;
    /** 是否使用API网关 */
    useApiGateway?: boolean;
    /** 是否启用预测 */
    enablePrediction?: boolean;
    /** 预测算法 */
    predictionAlgorithm?: PredictionAlgorithm;
    /** 最大预测时间（毫秒） */
    maxPredictionTime?: number;
    /** 是否启用插值 */
    enableInterpolation?: boolean;
    /** 插值因子（0-1） */
    interpolationFactor?: number;
    /** 是否启用空间分区 */
    enableSpatialPartitioning?: boolean;
    /** 空间分区最大深度 */
    spatialPartitioningMaxDepth?: number;
    /** 空间分区最大实体数量 */
    spatialPartitioningMaxEntities?: number;
    /** 是否启用自适应网络控制 */
    enableAdaptiveControl?: boolean;
    /** 自适应策略 */
    adaptiveStrategy?: AdaptiveStrategy;
    /** 是否启用抖动缓冲 */
    enableJitterBuffer?: boolean;
    /** 抖动缓冲大小（毫秒） */
    jitterBufferSize?: number;
    /** 是否启用优先级同步 */
    enablePrioritySync?: boolean;
    /** 是否启用增量同步 */
    enableDeltaSync?: boolean;
}
/**
 * 增强型网络系统
 */
export declare class EnhancedNetworkSystem extends System {
    /** 配置 */
    private options;
    /** 事件发射器 */
    private eventEmitter;
    /** 网络状态 */
    private state;
    /** 本地用户ID */
    private localUserId;
    /** 重连尝试次数 */
    private reconnectAttempts;
    /** 重连定时器ID */
    private reconnectTimerId;
    /** 同步定时器ID */
    private syncTimerId;
    /** 网络管理器 */
    private networkManager;
    /** 媒体流管理器 */
    private mediaStreamManager;
    /** 实体同步管理器 */
    private entitySyncManager;
    /** 用户会话管理器 */
    private userSessionManager;
    /** 网络事件调度器 */
    private eventDispatcher;
    /** 网络事件缓冲 */
    private eventBuffer;
    /** 网络质量监控器 */
    private networkQualityMonitor;
    /** 数据压缩器 */
    private dataCompressor;
    /** 服务发现客户端 */
    private serviceDiscoveryClient;
    /** 网络预测器 */
    private networkPredictor;
    /** 网络自适应控制器 */
    private networkAdaptiveController;
    /** 高级带宽控制器 */
    private bandwidthController;
    /** 空间分区系统 */
    private spatialPartitioning;
    /** 网络实体映射表 */
    private networkEntities;
    /**
     * 创建增强型网络系统
     * @param options 配置
     */
    constructor(options?: EnhancedNetworkSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 启动同步定时器
     */
    private startSyncTimer;
    /**
     * 停止同步定时器
     */
    private stopSyncTimer;
    /**
     * 同步实体
     */
    private syncEntities;
    /**
     * 处理网络质量变化
     * @param quality 网络质量数据
     */
    private handleNetworkQualityChange;
    /**
     * 处理网络参数调整
     * @param params 网络参数
     */
    private handleNetworkParamsAdjusted;
    /**
     * 处理对等连接
     * @param peerId 对等ID
     */
    private handlePeerConnected;
    /**
     * 处理对等断开连接
     * @param peerId 对等ID
     */
    private handlePeerDisconnected;
    /**
     * 处理接收到的数据
     * @param peerId 对等ID
     * @param data 数据
     */
    private handleDataReceived;
    /**
     * 尝试重连
     */
    private tryReconnect;
    /**
     * 系统更新
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 连接到服务器
     * @param serverUrl 服务器URL
     */
    connect(serverUrl: string): void;
    /**
     * 断开连接
     */
    disconnect(): void;
    /**
     * 发送数据到所有对等点
     * @param data 数据
     * @param compress 是否压缩
     */
    sendToAll(data: any, compress?: boolean): void;
    /**
     * 发送数据到特定对等点
     * @param peerId 对等ID
     * @param data 数据
     * @param compress 是否压缩
     */
    sendToPeer(peerId: string, data: any, compress?: boolean): Promise<void>;
    /**
     * 注册网络实体
     * @param entity 实体
     * @param entityType 实体类型
     * @param syncMode 同步模式
     */
    registerNetworkEntity(entity: Entity, entityType?: NetworkEntityType, syncMode?: NetworkEntitySyncMode): void;
    /**
     * 注销网络实体
     * @param entityId 实体ID
     */
    unregisterNetworkEntity(entityId: string): void;
    /**
     * 获取网络实体
     * @param entityId 实体ID
     * @returns 实体
     */
    getNetworkEntity(entityId: string): Entity | undefined;
    /**
     * 获取所有网络实体
     * @returns 实体映射表
     */
    getAllNetworkEntities(): Map<string, Entity>;
    /**
     * 查询区域内的网络实体
     * @param minX 最小X坐标
     * @param minZ 最小Z坐标
     * @param maxX 最大X坐标
     * @param maxZ 最大Z坐标
     * @returns 实体映射表
     */
    queryNetworkEntitiesInRegion(minX: number, minZ: number, maxX: number, maxZ: number): Map<string, Entity>;
    /**
     * 查询半径内的网络实体
     * @param x 中心X坐标
     * @param z 中心Z坐标
     * @param radius 半径
     * @returns 实体映射表
     */
    queryNetworkEntitiesInRadius(x: number, z: number, radius: number): Map<string, Entity>;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    addEventListener(event: string, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    removeEventListener(event: string, listener: (...args: any[]) => void): void;
    /**
     * 获取网络状态
     * @returns 网络状态
     */
    getState(): NetworkState;
    /**
     * 获取本地用户ID
     * @returns 本地用户ID
     */
    getLocalUserId(): string | null;
    /**
     * 获取网络质量
     * @returns 网络质量数据
     */
    getNetworkQuality(): NetworkQualityData | null;
    /**
     * 获取服务发现客户端
     * @returns 服务发现客户端
     */
    getServiceDiscoveryClient(): ServiceDiscoveryClient | null;
    /**
     * 获取带宽使用
     * @returns 带宽使用数据
     */
    getBandwidthUsage(): any;
    /**
     * 销毁系统
     */
    dispose(): void;
}
