/**
 * AI动画合成组件
 * 用于管理AI生成的动画
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter, type EventCallback } from '../../utils/EventEmitter';
import { AnimationGenerationRequest, AnimationGenerationResult } from '../ai/AnimationGenerationTypes';
import { v4 as uuidv4 } from 'uuid';

/**
 * AI动画合成组件类型
 */
export const AIAnimationSynthesisComponentType = 'AIAnimationSynthesisComponent';

/**
 * AI动画合成组件
 */
export class AIAnimationSynthesisComponent extends Component {
  /** 组件类型 */
  static readonly TYPE = AIAnimationSynthesisComponentType;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 生成回调 */
  private generationCallback: ((entity: Entity, request: AnimationGenerationRequest) => void) | null = null;

  /** 当前请求 */
  private currentRequest: AnimationGenerationRequest | null = null;

  /** 当前结果 */
  private currentResult: AnimationGenerationResult | null = null;

  /** 是否正在生成 */
  private generating: boolean = false;

  /** 请求队列 */
  private requestQueue: AnimationGenerationRequest[] = [];

  /** 结果历史 */
  private resultHistory: AnimationGenerationResult[] = [];

  /** 历史长度 */
  private historyLength: number = 10;

  /**
   * 构造函数
   */
  constructor() {
    super(AIAnimationSynthesisComponent.TYPE);
  }

  /**
   * 获取组件类型
   */
  public getType(): string {
    return AIAnimationSynthesisComponent.TYPE;
  }

  /**
   * 设置生成回调
   * @param callback 回调函数
   */
  public setGenerationCallback(callback: (entity: Entity, request: AnimationGenerationRequest) => void): void {
    this.generationCallback = callback;
  }

  /**
   * 请求动画生成
   * @param request 请求
   * @returns 请求ID
   */
  public requestAnimation(request: Omit<AnimationGenerationRequest, 'id'>): string {
    if (!this.isEnabled()) return '';

    // 生成请求ID
    const id = uuidv4();

    // 创建完整请求
    const fullRequest: AnimationGenerationRequest = {
      id,
      ...request
    };

    // 添加到队列
    this.requestQueue.push(fullRequest);

    // 如果没有正在生成的请求，开始生成
    if (!this.generating) {
      this.processNextRequest();
    }

    return id;
  }

  /**
   * 处理下一个请求
   */
  private processNextRequest(): void {
    if (!this.isEnabled() || this.generating || this.requestQueue.length === 0) return;

    // 设置正在生成
    this.generating = true;

    // 获取下一个请求
    this.currentRequest = this.requestQueue.shift()!;

    // 发出事件
    this.eventEmitter.emit('generationStart', { request: this.currentRequest });

    // 调用回调
    if (this.generationCallback) {
      const entity = this.getEntity();
      if (entity) {
        this.generationCallback(entity, this.currentRequest);
      }
    }
  }

  /**
   * 设置生成结果
   * @param result 结果
   */
  public setGenerationResult(result: AnimationGenerationResult): void {
    // 设置当前结果
    this.currentResult = result;

    // 添加到历史
    this.resultHistory.push(result);
    if (this.resultHistory.length > this.historyLength) {
      this.resultHistory.shift();
    }

    // 设置不再生成
    this.generating = false;

    // 发出事件
    this.eventEmitter.emit('generationComplete', { result });

    // 处理下一个请求
    this.processNextRequest();
  }

  /**
   * 取消请求
   * @param id 请求ID
   * @returns 是否成功取消
   */
  public cancelRequest(id: string): boolean {
    // 检查当前请求
    if (this.currentRequest && this.currentRequest.id === id) {
      // 无法取消正在生成的请求
      return false;
    }

    // 检查队列
    const index = this.requestQueue.findIndex(request => request.id === id);
    if (index >= 0) {
      // 从队列中移除
      this.requestQueue.splice(index, 1);
      return true;
    }

    return false;
  }

  /**
   * 清空队列
   */
  public clearQueue(): void {
    this.requestQueue = [];
  }

  /**
   * 获取当前请求
   */
  public getCurrentRequest(): AnimationGenerationRequest | null {
    return this.currentRequest;
  }

  /**
   * 获取当前结果
   */
  public getCurrentResult(): AnimationGenerationResult | null {
    return this.currentResult;
  }

  /**
   * 是否正在生成
   */
  public isGenerating(): boolean {
    return this.generating;
  }

  /**
   * 获取队列长度
   */
  public getQueueLength(): number {
    return this.requestQueue.length;
  }

  /**
   * 获取结果历史
   */
  public getResultHistory(): AnimationGenerationResult[] {
    return this.resultHistory;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled()) return;

    // 如果没有正在生成的请求，但队列中有请求，开始生成
    if (!this.generating && this.requestQueue.length > 0) {
      this.processNextRequest();
    }

    // deltaTime 可用于未来的时间相关逻辑
    // 例如：超时检查、进度更新等
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: EventCallback): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: EventCallback): void {
    this.eventEmitter.off(event, callback);
  }
}
