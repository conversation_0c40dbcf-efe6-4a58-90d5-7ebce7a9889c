/**
 * 健康检查控制器
 */
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { HealthCheck, HealthCheckService, TypeOrmHealthIndicator } from '@nestjs/terminus';

@ApiTags('健康检查')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  @ApiOperation({ summary: '健康检查' })
  async check() {
    try {
      const result = await this.health.check([
        () => this.db.pingCheck('database', { timeout: 5000 }),
      ]);

      console.log('✅ 健康检查通过:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('❌ 健康检查失败:', error.message);

      // 如果数据库检查失败，返回降级状态而不是完全失败
      const errorResponse = {
        status: 'error',
        info: {
          service: {
            status: 'up',
            message: '服务注册中心运行正常',
          },
        },
        error: {
          database: {
            status: 'down',
            message: error.message || '数据库连接失败',
            timestamp: new Date().toISOString(),
          },
        },
        details: {
          service: {
            status: 'up',
            message: '服务注册中心运行正常',
          },
          database: {
            status: 'down',
            message: error.message || '数据库连接失败',
            timestamp: new Date().toISOString(),
          },
        },
      };

      console.log('⚠️ 返回降级健康状态:', JSON.stringify(errorResponse, null, 2));
      return errorResponse;
    }
  }
}
