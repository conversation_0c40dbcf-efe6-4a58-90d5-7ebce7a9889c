/**
 * WebRTC连接管理器
 * 负责管理WebRTC连接的创建、维护和销毁
 */
import { EventEmitter } from '../utils/EventEmitter';
import { WebRTCConnection, WebRTCConnectionConfig } from './WebRTCConnection';
/**
 * WebRTC连接管理器配置
 */
export interface WebRTCConnectionManagerConfig {
    /** ICE服务器配置 */
    iceServers?: RTCIceServer[];
    /** 是否启用数据通道 */
    enableDataChannel?: boolean;
    /** 是否启用音频 */
    enableAudio?: boolean;
    /** 是否启用视频 */
    enableVideo?: boolean;
    /** 是否启用屏幕共享 */
    enableScreenShare?: boolean;
    /** 数据通道配置 */
    dataChannelConfig?: RTCDataChannelInit;
    /** 音频约束 */
    audioConstraints?: MediaTrackConstraints;
    /** 视频约束 */
    videoConstraints?: MediaTrackConstraints;
    /** 是否使用压缩 */
    useCompression?: boolean;
    /** 最大重连次数 */
    maxReconnectAttempts?: number;
    /** 重连间隔（毫秒） */
    reconnectInterval?: number;
    /** 心跳间隔（毫秒） */
    heartbeatInterval?: number;
    /** 连接超时（毫秒） */
    connectionTimeout?: number;
    /** 是否自动重连 */
    autoReconnect?: boolean;
    /** 是否使用网络质量监控 */
    useNetworkQualityMonitor?: boolean;
    /** 是否使用带宽控制 */
    useBandwidthController?: boolean;
}
/**
 * WebRTC连接管理器
 * 负责管理WebRTC连接的创建、维护和销毁
 */
export declare class WebRTCConnectionManager extends EventEmitter {
    /** 配置 */
    private config;
    /** WebRTC连接映射表 */
    private connections;
    /** 重连尝试次数映射表 */
    private reconnectAttempts;
    /** 重连定时器ID映射表 */
    private reconnectTimers;
    /** 连接超时定时器ID映射表 */
    private connectionTimeoutTimers;
    /** 网络质量监控器 */
    private networkQualityMonitor;
    /** 带宽控制器 */
    private bandwidthController;
    /** 本地用户ID */
    private localUserId;
    /**
     * 创建WebRTC连接管理器
     * @param config 配置
     */
    constructor(config?: WebRTCConnectionManagerConfig);
    /**
     * 初始化管理器
     * @param localUserId 本地用户ID
     */
    initialize(localUserId: string): void;
    /**
     * 创建WebRTC连接
     * @param userId 远程用户ID
     * @param config 连接配置
     * @returns WebRTC连接
     */
    createConnection(userId: string, config?: WebRTCConnectionConfig): WebRTCConnection;
    /**
     * 获取WebRTC连接
     * @param userId 远程用户ID
     * @returns WebRTC连接
     */
    getConnection(userId: string): WebRTCConnection | undefined;
    /**
     * 关闭WebRTC连接
     * @param userId 远程用户ID
     * @returns 是否成功关闭
     */
    closeConnection(userId: string): boolean;
    /**
     * 关闭所有WebRTC连接
     */
    closeAllConnections(): void;
    /**
     * 设置连接事件监听器
     * @param connection WebRTC连接
     */
    private setupConnectionListeners;
    /**
     * 尝试重连
     * @param userId 远程用户ID
     */
    private attemptReconnect;
    /**
     * 清除重连定时器
     * @param userId 远程用户ID
     */
    private clearReconnectTimer;
    /**
     * 设置连接超时定时器
     * @param userId 远程用户ID
     */
    private setConnectionTimeout;
    /**
     * 清除连接超时定时器
     * @param userId 远程用户ID
     */
    private clearConnectionTimeout;
    /**
     * 处理WebRTC提议
     * @param userId 远程用户ID
     * @param offer 提议
     */
    handleOffer(userId: string, offer: RTCSessionDescriptionInit): void;
    /**
     * 处理WebRTC应答
     * @param userId 远程用户ID
     * @param answer 应答
     */
    handleAnswer(userId: string, answer: RTCSessionDescriptionInit): void;
    /**
     * 处理WebRTC ICE候选
     * @param userId 远程用户ID
     * @param candidate ICE候选
     */
    handleIceCandidate(userId: string, candidate: RTCIceCandidateInit): void;
    /**
     * 发送消息到用户
     * @param userId 远程用户ID
     * @param message 消息
     * @returns 是否成功发送
     */
    sendMessage(userId: string, message: any): boolean;
    /**
     * 广播消息到所有用户
     * @param message 消息
     * @param excludeUserIds 排除的用户ID列表
     */
    broadcastMessage(message: any, excludeUserIds?: string[]): void;
    /**
     * 获取所有连接的用户ID
     * @returns 用户ID列表
     */
    getConnectedUserIds(): string[];
    /**
     * 获取连接数量
     * @returns 连接数量
     */
    getConnectionCount(): number;
    /**
     * 获取已连接的连接数量
     * @returns 已连接的连接数量
     */
    getConnectedCount(): number;
    /**
     * 销毁管理器
     */
    dispose(): void;
}
