# 权限管理指南

本教程将指导您如何在DL（Digital Learning）引擎的协作编辑中管理用户权限，包括角色设置、权限分配和权限继承等内容。

## 目录

- [权限系统概述](#权限系统概述)
- [用户角色](#用户角色)
- [权限类型](#权限类型)
- [管理用户权限](#管理用户权限)
- [实体级权限](#实体级权限)
- [权限继承](#权限继承)
- [权限审计](#权限审计)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 权限系统概述

DL（Digital Learning）引擎的权限系统采用基于角色的访问控制（RBAC）模型，通过为用户分配角色来控制其访问权限。系统支持全局权限和实体级权限两个层次，并提供灵活的权限继承机制。

### 权限系统特点

- **基于角色**：通过角色分配权限，简化管理
- **细粒度控制**：支持全局和实体级权限
- **灵活继承**：支持权限继承和覆盖
- **实时生效**：权限变更实时生效
- **审计功能**：记录权限变更历史

## 用户角色

系统预定义了四种基本角色：

### 所有者（Owner）

- 项目或会话的创建者
- 拥有所有权限
- 可以管理其他用户的权限
- 不可被降级或移除（除非转让所有权）

### 管理员（Admin）

- 由所有者指定
- 可以编辑场景和管理用户
- 可以分配编辑者和查看者角色
- 不能管理其他管理员的权限

### 编辑者（Editor）

- 可以编辑场景内容
- 可以添加、修改和删除对象
- 不能管理用户和权限

### 查看者（Viewer）

- 只能查看场景
- 不能进行任何编辑操作
- 可以使用聊天功能

## 权限类型

系统支持以下基本权限类型：

### 读取权限（Read）

- 查看场景和对象
- 查看属性和组件
- 查看历史记录

### 写入权限（Write）

- 添加和修改对象
- 修改属性和组件
- 保存场景

### 删除权限（Delete）

- 删除对象
- 删除组件
- 删除场景

### 用户管理权限（Manage Users）

- 添加和移除用户
- 分配编辑者和查看者角色
- 管理用户状态

### 权限管理权限（Manage Permissions）

- 分配和修改权限
- 创建自定义角色
- 设置实体级权限

## 管理用户权限

### 查看用户列表

1. 打开协作面板
2. 切换到"用户"标签页
3. 查看当前在线用户列表

### 分配角色

1. 在用户列表中找到目标用户
2. 点击用户旁边的设置图标
3. 选择"更改角色"
4. 从下拉菜单中选择新角色
5. 点击"确认"按钮

### 移除用户

1. 在用户列表中找到目标用户
2. 点击用户旁边的设置图标
3. 选择"移除用户"
4. 确认移除操作

### 转让所有权

1. 在用户列表中找到目标用户
2. 点击用户旁边的设置图标
3. 选择"转让所有权"
4. 确认转让操作
5. 输入您的密码进行验证

## 实体级权限

除了全局角色权限外，系统还支持对特定实体设置权限，实现更细粒度的控制。

### 设置实体权限

1. 选择目标实体
2. 右键点击并选择"权限"
3. 在权限对话框中，选择用户或角色
4. 设置允许或拒绝的权限
5. 点击"保存"按钮

### 权限选项

对于每个实体，可以设置以下权限选项：

- **允许**：明确授予权限
- **拒绝**：明确拒绝权限
- **继承**：从父级继承权限

### 实体权限优先级

权限检查遵循以下优先级规则：

1. 实体级明确拒绝
2. 实体级明确允许
3. 父级实体权限
4. 全局角色权限

## 权限继承

DL（Digital Learning）引擎的权限系统支持层次化继承，使权限管理更加灵活和高效。

### 继承规则

1. 子实体默认继承父实体的权限
2. 实体级权限覆盖继承的权限
3. 明确的拒绝优先于继承的允许
4. 明确的允许优先于继承的拒绝

### 继承示例

假设有以下层次结构：

```
场景
├── 组1
│   ├── 对象A
│   └── 对象B
└── 组2
    ├── 对象C
    └── 对象D
```

如果在"组1"上设置用户X有编辑权限，则用户X自动获得对"对象A"和"对象B"的编辑权限。如果明确拒绝用户X对"对象A"的编辑权限，则该拒绝将覆盖从"组1"继承的权限。

## 权限审计

系统会记录所有权限变更操作，以便审计和追踪。

### 查看权限历史

1. 打开协作面板
2. 切换到"权限"标签页
3. 点击"历史"按钮
4. 查看权限变更历史记录

### 审计信息

每条审计记录包含以下信息：

- 操作时间
- 操作用户
- 目标用户或角色
- 变更前权限
- 变更后权限
- 影响的实体（如适用）

## 最佳实践

### 权限设计原则

1. **最小权限原则**：只授予用户完成任务所需的最小权限
2. **职责分离**：将关键任务分配给不同角色的用户
3. **分组管理**：通过实体分组简化权限管理
4. **明确拒绝**：对敏感操作使用明确拒绝而非不允许
5. **定期审查**：定期检查和更新权限设置

### 权限管理策略

1. **基于团队结构**：根据团队结构和工作流程设计权限
2. **基于区域**：为不同场景区域分配不同的编辑权限
3. **基于时间**：在不同项目阶段调整权限设置
4. **基于功能**：根据功能模块分配专项权限

## 常见问题

### 为什么用户无法编辑某个对象？

可能的原因：
- 用户角色没有编辑权限
- 对象有明确的拒绝权限
- 对象被其他用户锁定
- 父级对象有权限限制

解决方法：
- 检查用户角色和权限
- 检查对象的实体级权限
- 检查对象是否被锁定
- 检查父级对象的权限设置

### 如何快速为多个对象设置权限？

1. 选择多个对象（按住Shift或Ctrl键选择）
2. 右键点击并选择"权限"
3. 设置批量权限
4. 点击"应用到所有选中对象"

### 如何处理权限冲突？

当出现权限冲突时（例如，用户从不同路径继承了冲突的权限），系统会按照以下规则解决：

1. 明确的拒绝优先于任何允许
2. 明确的允许优先于继承的拒绝
3. 较低级别的设置优先于较高级别

如果仍有冲突，可以在实体上设置明确的权限来解决。

### 如何恢复默认权限？

1. 选择目标实体
2. 右键点击并选择"权限"
3. 点击"重置为默认"按钮
4. 确认重置操作
