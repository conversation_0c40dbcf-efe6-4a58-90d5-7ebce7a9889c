/**
 * Avatar动画组件
 * 用于管理角色动画
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Animator } from '../../animation/Animator';
import type { AnimationClip } from '../../animation/AnimationClip';
import { EventEmitter, type EventCallback } from '../../utils/EventEmitter';
import { Vector3 } from 'three';

/**
 * 动画图节点类型
 */
export enum AnimationGraphNodeType {
  /** 单一动画 */
  SINGLE = 'single',
  /** 混合空间 */
  BLEND_SPACE = 'blendSpace',
  /** 状态机 */
  STATE_MACHINE = 'stateMachine'
}

/**
 * 动画图节点
 */
export interface AnimationGraphNode {
  /** 节点类型 */
  type: AnimationGraphNodeType;
  /** 节点名称 */
  name: string;
  /** 节点数据 */
  data: any;
}

/**
 * 动画图
 */
export interface AnimationGraph {
  /** 节点映射 */
  nodes: Map<string, AnimationGraphNode>;
  /** 当前节点 */
  currentNode: AnimationGraphNode | null;
  /** 是否正在混合 */
  blending: boolean;
  /** 混合强度 */
  blendStrength: number;
  /** 混合目标节点 */
  blendTargetNode: AnimationGraphNode | null;
}

/**
 * Avatar动画组件
 */
export class AvatarAnimationComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'AvatarAnimation';

  /** 动画控制器 */
  public animator: Animator;

  /** 动画图 */
  private animationGraph: AnimationGraph;

  /** 运动向量 */
  private locomotion: THREE.Vector3 = new THREE.Vector3();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param animator 动画控制器
   */
  constructor(animator?: Animator) {
    super(AvatarAnimationComponent.type);

    this.animator = animator || new Animator();

    // 初始化动画图
    this.animationGraph = {
      nodes: new Map(),
      currentNode: null,
      blending: false,
      blendStrength: 0,
      blendTargetNode: null
    };
  }

  /**
   * 设置动画控制器
   * @param animator 动画控制器
   */
  public setAnimator(animator: Animator): void {
    this.animator = animator;
  }

  /**
   * 获取动画控制器
   * @returns 动画控制器
   */
  public getAnimator(): Animator {
    return this.animator;
  }

  /**
   * 添加动画图节点
   * @param node 动画图节点
   */
  public addGraphNode(node: AnimationGraphNode): void {
    this.animationGraph.nodes.set(node.name, node);
  }

  /**
   * 移除动画图节点
   * @param name 节点名称
   */
  public removeGraphNode(name: string): void {
    // 如果是当前节点，则清除
    if (this.animationGraph.currentNode && this.animationGraph.currentNode.name === name) {
      this.animationGraph.currentNode = null;
    }

    // 如果是混合目标节点，则清除
    if (this.animationGraph.blendTargetNode && this.animationGraph.blendTargetNode.name === name) {
      this.animationGraph.blendTargetNode = null;
      this.animationGraph.blending = false;
    }

    // 移除节点
    this.animationGraph.nodes.delete(name);
  }

  /**
   * 设置当前节点
   * @param name 节点名称
   * @param blendTime 混合时间（秒）
   */
  public setCurrentNode(name: string, blendTime: number = 0.3): void {
    // 获取节点
    const node = this.animationGraph.nodes.get(name);
    if (!node) {
      console.warn(`动画图节点 "${name}" 不存在`);
      return;
    }

    // 如果已经是当前节点，则不做任何操作
    if (this.animationGraph.currentNode && this.animationGraph.currentNode.name === name) {
      return;
    }

    // 如果没有当前节点，或者混合时间为0，则直接设置
    if (!this.animationGraph.currentNode || blendTime <= 0) {
      this.animationGraph.currentNode = node;
      this.animationGraph.blending = false;
      this.animationGraph.blendTargetNode = null;

      // 播放节点动画
      this.playNodeAnimation(node);

      return;
    }

    // 否则，设置为混合状态
    this.animationGraph.blending = true;
    this.animationGraph.blendStrength = 0;
    this.animationGraph.blendTargetNode = node;

    // 播放混合动画
    this.playBlendAnimation(this.animationGraph.currentNode, node, blendTime);
  }

  /**
   * 播放节点动画
   * @param node 动画图节点
   */
  private playNodeAnimation(node: AnimationGraphNode): void {
    if (!this.animator) return;

    // 根据节点类型播放动画
    switch (node.type) {
      case AnimationGraphNodeType.SINGLE:
        // 播放单一动画
        this.animator.play(node.data.clipName, 0);
        break;

      case AnimationGraphNodeType.BLEND_SPACE:
        // 播放混合空间动画
        this.playBlendSpaceAnimation(node);
        break;

      case AnimationGraphNodeType.STATE_MACHINE:
        // 播放状态机动画
        this.playStateMachineAnimation(node);
        break;
    }
  }

  /**
   * 播放混合动画
   * @param fromNode 源节点
   * @param toNode 目标节点
   * @param blendTime 混合时间（秒）
   */
  private playBlendAnimation(fromNode: AnimationGraphNode, toNode: AnimationGraphNode, blendTime: number): void {
    if (!this.animator) return;

    // 根据目标节点类型播放混合动画
    switch (toNode.type) {
      case AnimationGraphNodeType.SINGLE:
        // 混合到单一动画
        this.animator.play(toNode.data.clipName, blendTime);
        break;

      case AnimationGraphNodeType.BLEND_SPACE:
        // 混合到混合空间动画
        // 这里需要根据实际情况实现
        break;

      case AnimationGraphNodeType.STATE_MACHINE:
        // 混合到状态机动画
        // 这里需要根据实际情况实现
        break;
    }
  }

  /**
   * 播放混合空间动画
   * @param node 动画图节点
   */
  private playBlendSpaceAnimation(node: AnimationGraphNode): void {
    // 这里需要根据实际情况实现
    // 例如，可以根据locomotion向量更新混合空间
  }

  /**
   * 播放状态机动画
   * @param node 动画图节点
   */
  private playStateMachineAnimation(node: AnimationGraphNode): void {
    // 这里需要根据实际情况实现
    // 例如，可以设置状态机的当前状态
  }

  /**
   * 设置运动向量
   * @param x X分量
   * @param y Y分量
   * @param z Z分量
   */
  public setLocomotion(x: number, y: number, z: number): void {
    this.locomotion.set(x, y, z);
  }

  /**
   * 获取运动向量
   * @returns 运动向量
   */
  public getLocomotion(): THREE.Vector3 {
    return this.locomotion.clone();
  }



  /**
   * 更新组件
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled() || !this.animator) return;

    // 更新动画控制器
    this.animator.update(deltaTime);

    // 如果正在混合
    if (this.animationGraph.blending && this.animationGraph.blendTargetNode) {
      // 更新混合强度
      this.animationGraph.blendStrength += deltaTime / 0.3; // 假设混合时间为0.3秒

      // 如果混合完成
      if (this.animationGraph.blendStrength >= 1.0) {
        this.animationGraph.currentNode = this.animationGraph.blendTargetNode;
        this.animationGraph.blendTargetNode = null;
        this.animationGraph.blending = false;
      }
    }

    // 更新当前节点
    if (this.animationGraph.currentNode) {
      this.updateNode(this.animationGraph.currentNode, deltaTime);
    }
  }

  /**
   * 更新节点
   * @param node 动画图节点
   * @param deltaTime 时间增量（秒）
   */
  private updateNode(node: AnimationGraphNode, deltaTime: number): void {
    // 根据节点类型更新
    switch (node.type) {
      case AnimationGraphNodeType.BLEND_SPACE:
        // 更新混合空间
        this.updateBlendSpace(node, deltaTime);
        break;

      case AnimationGraphNodeType.STATE_MACHINE:
        // 更新状态机
        this.updateStateMachine(node, deltaTime);
        break;
    }
  }

  /**
   * 更新混合空间
   * @param node 动画图节点
   * @param deltaTime 时间增量（秒）
   */
  private updateBlendSpace(node: AnimationGraphNode, deltaTime: number): void {
    // 这里需要根据实际情况实现
    // 例如，可以根据locomotion向量更新混合空间
  }

  /**
   * 更新状态机
   * @param node 动画图节点
   * @param deltaTime 时间增量（秒）
   */
  private updateStateMachine(node: AnimationGraphNode, deltaTime: number): void {
    // 这里需要根据实际情况实现
    // 例如，可以更新状态机的状态
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public addEventListener(type: string, callback: EventCallback): void {
    this.eventEmitter.on(type, callback);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public removeEventListener(type: string, callback: EventCallback): void {
    this.eventEmitter.off(type, callback);
  }

  /**
   * 克隆组件
   * @returns 克隆的组件
   */
  public clone(): AvatarAnimationComponent {
    const clone = new AvatarAnimationComponent(this.animator);

    // 复制运动向量
    clone.locomotion.copy(this.locomotion);

    // 复制启用状态
    clone.setEnabled(this.isEnabled());

    return clone;
  }
}
