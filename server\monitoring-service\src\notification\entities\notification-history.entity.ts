import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { NotificationChannelEntity } from './notification-channel.entity';

export enum NotificationStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
  PENDING = 'pending',
  RATE_LIMITED = 'rate_limited',
}

@Entity('notification_history')
export class NotificationHistoryEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  channelId: string;

  @ManyToOne(() => NotificationChannelEntity)
  @JoinColumn({ name: 'channelId' })
  channel: NotificationChannelEntity;

  @Column({ nullable: true })
  @Index()
  alertId: string;

  @Column('text')
  subject: string;

  @Column('text')
  content: string;

  @Column('json', { nullable: true })
  data: Record<string, any>;

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING,
  })
  @Index()
  status: NotificationStatus;

  @Column('text', { nullable: true })
  errorMessage: string;

  @Column('int', { default: 0 })
  retryCount: number;

  @Column('timestamp', { nullable: true })
  nextRetryTime: Date;

  @CreateDateColumn()
  @Index()
  createdAt: Date;
}
