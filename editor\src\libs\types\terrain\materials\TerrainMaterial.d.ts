/**
 * 地形材质
 * 用于渲染地形
 */
import * as THREE from 'three';
import { TerrainTextureLayer } from '../components/TerrainComponent';
/**
 * 地形材质选项
 */
export interface TerrainMaterialOptions {
    /** 纹理层 */
    layers: TerrainTextureLayer[];
    /** 分辨率 */
    resolution: number;
    /** 宽度 */
    width: number;
    /** 高度 */
    height: number;
    /** 最大高度 */
    maxHeight: number;
}
/**
 * 地形材质
 */
export declare class TerrainMaterial extends THREE.ShaderMaterial {
    /** 纹理层 */
    private layers;
    /** 分辨率 */
    private resolution;
    /** 宽度 */
    private width;
    /** 高度 */
    private height;
    /** 最大高度 */
    private maxHeight;
    /** 混合贴图 */
    private blendMaps;
    /** 纹理加载器 */
    private textureLoader;
    /**
     * 创建地形材质
     * @param options 选项
     */
    constructor(options: TerrainMaterialOptions);
    /**
     * 初始化材质
     */
    private initializeMaterial;
    /**
     * 加载纹理
     */
    private loadTextures;
    /**
     * 创建混合贴图
     */
    private createBlendMaps;
    /**
     * 更新混合贴图
     * @param layerIndex 层索引
     * @param x X坐标
     * @param y Y坐标
     * @param radius 半径
     * @param strength 强度
     * @param falloff 衰减
     */
    updateBlendMap(layerIndex: number, x: number, y: number, radius: number, strength: number, falloff: number): void;
    /**
     * 释放资源
     */
    dispose(): void;
}
