/**
 * 材质转换器
 * 用于在不同类型的材质之间进行转换
 */
import * as THREE from 'three';
import { DeviceCapabilities } from '../../utils/DeviceCapabilities';
import { MaterialType } from './MaterialFactory';

/**
 * 材质转换器配置接口
 */
export interface MaterialConverterOptions {
  /** 设备能力检测 */
  deviceCapabilities?: DeviceCapabilities;
  /** 是否保留原始材质 */
  preserveOriginal?: boolean;
}

/**
 * 材质转换器类
 */
export class MaterialConverter {
  /** 设备能力检测 */
  private deviceCapabilities: DeviceCapabilities;

  /** 是否保留原始材质 */
  private preserveOriginal: boolean;

  /** 材质转换映射 */
  private conversionMap: Map<string, Map<string, (material: THREE.Material) => THREE.Material>> = new Map();

  /** 材质降级映射 */
  private downgradeMap: Map<string, (material: THREE.Material) => THREE.Material> = new Map();

  /** 材质升级映射 */
  private upgradeMap: Map<string, (material: THREE.Material) => THREE.Material> = new Map();

  /**
   * 创建材质转换器
   * @param options 材质转换器配置
   */
  constructor(options: MaterialConverterOptions = {}) {
    this.deviceCapabilities = options.deviceCapabilities || DeviceCapabilities.getInstance();
    this.preserveOriginal = options.preserveOriginal !== undefined ? options.preserveOriginal : false;

    this.registerDefaultConversions();
    this.registerDefaultDowngrades();
    this.registerDefaultUpgrades();
  }

  /**
   * 注册默认转换
   */
  private registerDefaultConversions(): void {
    // 注册标准材质到基础材质的转换
    this.registerConversion(MaterialType.STANDARD, MaterialType.BASIC, (material) => {
      const standardMaterial = material as THREE.MeshStandardMaterial;
      const basicMaterial = new THREE.MeshBasicMaterial();

      // 复制基本属性
      basicMaterial.color.copy(standardMaterial.color);
      basicMaterial.opacity = standardMaterial.opacity;
      basicMaterial.transparent = standardMaterial.transparent;
      basicMaterial.side = standardMaterial.side;
      basicMaterial.alphaTest = standardMaterial.alphaTest;
      basicMaterial.blending = standardMaterial.blending;
      basicMaterial.visible = standardMaterial.visible;

      // 复制纹理
      basicMaterial.map = standardMaterial.map;
      basicMaterial.aoMap = standardMaterial.aoMap;
      basicMaterial.aoMapIntensity = standardMaterial.aoMapIntensity;
      basicMaterial.alphaMap = standardMaterial.alphaMap;
      basicMaterial.envMap = standardMaterial.envMap;

      // 复制自发光（基础材质不支持自发光，跳过）
      // MeshBasicMaterial 不支持 emissive 属性

      return basicMaterial;
    });

    // 注册物理材质到标准材质的转换
    this.registerConversion(MaterialType.PHYSICAL, MaterialType.STANDARD, (material) => {
      const physicalMaterial = material as THREE.MeshPhysicalMaterial;
      const standardMaterial = new THREE.MeshStandardMaterial();

      // 复制基本属性
      standardMaterial.color.copy(physicalMaterial.color);
      standardMaterial.opacity = physicalMaterial.opacity;
      standardMaterial.transparent = physicalMaterial.transparent;
      standardMaterial.side = physicalMaterial.side;
      standardMaterial.alphaTest = physicalMaterial.alphaTest;
      standardMaterial.blending = physicalMaterial.blending;
      standardMaterial.visible = physicalMaterial.visible;

      // 复制标准材质特有属性
      standardMaterial.roughness = physicalMaterial.roughness;
      standardMaterial.metalness = physicalMaterial.metalness;
      standardMaterial.emissive.copy(physicalMaterial.emissive);
      standardMaterial.emissiveIntensity = physicalMaterial.emissiveIntensity;
      standardMaterial.wireframe = physicalMaterial.wireframe;

      // 复制纹理
      standardMaterial.map = physicalMaterial.map;
      standardMaterial.aoMap = physicalMaterial.aoMap;
      standardMaterial.aoMapIntensity = physicalMaterial.aoMapIntensity;
      standardMaterial.normalMap = physicalMaterial.normalMap;
      standardMaterial.normalMapType = physicalMaterial.normalMapType;
      standardMaterial.normalScale.copy(physicalMaterial.normalScale);
      standardMaterial.displacementMap = physicalMaterial.displacementMap;
      standardMaterial.displacementScale = physicalMaterial.displacementScale;
      standardMaterial.displacementBias = physicalMaterial.displacementBias;
      standardMaterial.roughnessMap = physicalMaterial.roughnessMap;
      standardMaterial.metalnessMap = physicalMaterial.metalnessMap;
      standardMaterial.alphaMap = physicalMaterial.alphaMap;
      standardMaterial.envMap = physicalMaterial.envMap;
      standardMaterial.envMapIntensity = physicalMaterial.envMapIntensity;
      standardMaterial.emissiveMap = physicalMaterial.emissiveMap;

      return standardMaterial;
    });

    // 注册标准材质到Lambert材质的转换
    this.registerConversion(MaterialType.STANDARD, MaterialType.LAMBERT, (material) => {
      const standardMaterial = material as THREE.MeshStandardMaterial;
      const lambertMaterial = new THREE.MeshLambertMaterial();

      // 复制基本属性
      lambertMaterial.color.copy(standardMaterial.color);
      lambertMaterial.opacity = standardMaterial.opacity;
      lambertMaterial.transparent = standardMaterial.transparent;
      lambertMaterial.side = standardMaterial.side;
      lambertMaterial.alphaTest = standardMaterial.alphaTest;
      lambertMaterial.blending = standardMaterial.blending;
      lambertMaterial.visible = standardMaterial.visible;

      // 复制纹理
      lambertMaterial.map = standardMaterial.map;
      lambertMaterial.aoMap = standardMaterial.aoMap;
      lambertMaterial.aoMapIntensity = standardMaterial.aoMapIntensity;
      lambertMaterial.alphaMap = standardMaterial.alphaMap;
      lambertMaterial.envMap = standardMaterial.envMap;

      // 复制自发光
      lambertMaterial.emissive.copy(standardMaterial.emissive);
      lambertMaterial.emissiveMap = standardMaterial.emissiveMap;
      lambertMaterial.emissiveIntensity = standardMaterial.emissiveIntensity;

      // 设置反射率（基于金属度）
      lambertMaterial.reflectivity = standardMaterial.metalness;

      return lambertMaterial;
    });

    // 注册物理材质到Lambert材质的转换
    this.registerConversion(MaterialType.PHYSICAL, MaterialType.LAMBERT, (material) => {
      // 先转换为标准材质，再转换为Lambert材质
      const standardMaterial = this.convert(material, MaterialType.STANDARD);
      return this.convert(standardMaterial, MaterialType.LAMBERT);
    });

    // 注册Phong材质到Lambert材质的转换
    this.registerConversion(MaterialType.PHONG, MaterialType.LAMBERT, (material) => {
      const phongMaterial = material as THREE.MeshPhongMaterial;
      const lambertMaterial = new THREE.MeshLambertMaterial();

      // 复制基本属性
      lambertMaterial.color.copy(phongMaterial.color);
      lambertMaterial.opacity = phongMaterial.opacity;
      lambertMaterial.transparent = phongMaterial.transparent;
      lambertMaterial.side = phongMaterial.side;
      lambertMaterial.alphaTest = phongMaterial.alphaTest;
      lambertMaterial.blending = phongMaterial.blending;
      lambertMaterial.visible = phongMaterial.visible;

      // 复制纹理
      lambertMaterial.map = phongMaterial.map;
      lambertMaterial.aoMap = phongMaterial.aoMap;
      lambertMaterial.aoMapIntensity = phongMaterial.aoMapIntensity;
      lambertMaterial.alphaMap = phongMaterial.alphaMap;
      lambertMaterial.envMap = phongMaterial.envMap;

      // 复制自发光
      lambertMaterial.emissive.copy(phongMaterial.emissive);
      lambertMaterial.emissiveMap = phongMaterial.emissiveMap;
      lambertMaterial.emissiveIntensity = phongMaterial.emissiveIntensity;

      // 设置反射率
      lambertMaterial.reflectivity = phongMaterial.shininess / 100;

      return lambertMaterial;
    });
  }

  /**
   * 注册默认降级
   */
  private registerDefaultDowngrades(): void {
    // 注册物理材质的降级
    this.registerDowngrade(MaterialType.PHYSICAL, (material) => {
      return this.convert(material, MaterialType.STANDARD);
    });

    // 注册标准材质的降级
    this.registerDowngrade(MaterialType.STANDARD, (material) => {
      return this.convert(material, MaterialType.LAMBERT);
    });

    // 注册Phong材质的降级
    this.registerDowngrade(MaterialType.PHONG, (material) => {
      return this.convert(material, MaterialType.LAMBERT);
    });

    // 注册Lambert材质的降级
    this.registerDowngrade(MaterialType.LAMBERT, (material) => {
      return this.convert(material, MaterialType.BASIC);
    });

    // 注册卡通材质的降级
    this.registerDowngrade(MaterialType.TOON, (material) => {
      return this.convert(material, MaterialType.LAMBERT);
    });
  }

  /**
   * 注册默认升级
   */
  private registerDefaultUpgrades(): void {
    // 注册基础材质的升级
    this.registerUpgrade(MaterialType.BASIC, (material) => {
      const basicMaterial = material as THREE.MeshBasicMaterial;
      const lambertMaterial = new THREE.MeshLambertMaterial();

      // 复制基本属性
      lambertMaterial.color.copy(basicMaterial.color);
      lambertMaterial.opacity = basicMaterial.opacity;
      lambertMaterial.transparent = basicMaterial.transparent;
      lambertMaterial.side = basicMaterial.side;
      lambertMaterial.alphaTest = basicMaterial.alphaTest;
      lambertMaterial.blending = basicMaterial.blending;
      lambertMaterial.visible = basicMaterial.visible;

      // 复制纹理
      lambertMaterial.map = basicMaterial.map;
      lambertMaterial.aoMap = basicMaterial.aoMap;
      lambertMaterial.aoMapIntensity = basicMaterial.aoMapIntensity;
      lambertMaterial.alphaMap = basicMaterial.alphaMap;
      lambertMaterial.envMap = basicMaterial.envMap;

      // 复制自发光（基础材质不支持自发光，设置默认值）
      // MeshBasicMaterial 不支持 emissive 属性，设置默认自发光
      lambertMaterial.emissive.setHex(0x000000);
      lambertMaterial.emissiveIntensity = 1.0;

      return lambertMaterial;
    });

    // 注册Lambert材质的升级
    this.registerUpgrade(MaterialType.LAMBERT, (material) => {
      const lambertMaterial = material as THREE.MeshLambertMaterial;
      const standardMaterial = new THREE.MeshStandardMaterial();

      // 复制基本属性
      standardMaterial.color.copy(lambertMaterial.color);
      standardMaterial.opacity = lambertMaterial.opacity;
      standardMaterial.transparent = lambertMaterial.transparent;
      standardMaterial.side = lambertMaterial.side;
      standardMaterial.alphaTest = lambertMaterial.alphaTest;
      standardMaterial.blending = lambertMaterial.blending;
      standardMaterial.visible = lambertMaterial.visible;

      // 复制纹理
      standardMaterial.map = lambertMaterial.map;
      standardMaterial.aoMap = lambertMaterial.aoMap;
      standardMaterial.aoMapIntensity = lambertMaterial.aoMapIntensity;
      standardMaterial.alphaMap = lambertMaterial.alphaMap;
      standardMaterial.envMap = lambertMaterial.envMap;

      // 复制自发光
      standardMaterial.emissive.copy(lambertMaterial.emissive);
      standardMaterial.emissiveMap = lambertMaterial.emissiveMap;
      standardMaterial.emissiveIntensity = lambertMaterial.emissiveIntensity;

      // 设置粗糙度和金属度
      standardMaterial.roughness = 0.7;
      standardMaterial.metalness = lambertMaterial.reflectivity || 0.0;

      return standardMaterial;
    });

    // 注册标准材质的升级
    this.registerUpgrade(MaterialType.STANDARD, (material) => {
      const standardMaterial = material as THREE.MeshStandardMaterial;
      const physicalMaterial = new THREE.MeshPhysicalMaterial();

      // 复制基本属性
      physicalMaterial.color.copy(standardMaterial.color);
      physicalMaterial.opacity = standardMaterial.opacity;
      physicalMaterial.transparent = standardMaterial.transparent;
      physicalMaterial.side = standardMaterial.side;
      physicalMaterial.alphaTest = standardMaterial.alphaTest;
      physicalMaterial.blending = standardMaterial.blending;
      physicalMaterial.visible = standardMaterial.visible;

      // 复制标准材质特有属性
      physicalMaterial.roughness = standardMaterial.roughness;
      physicalMaterial.metalness = standardMaterial.metalness;
      physicalMaterial.emissive.copy(standardMaterial.emissive);
      physicalMaterial.emissiveIntensity = standardMaterial.emissiveIntensity;
      physicalMaterial.wireframe = standardMaterial.wireframe;

      // 复制纹理
      physicalMaterial.map = standardMaterial.map;
      physicalMaterial.aoMap = standardMaterial.aoMap;
      physicalMaterial.aoMapIntensity = standardMaterial.aoMapIntensity;
      physicalMaterial.normalMap = standardMaterial.normalMap;
      physicalMaterial.normalMapType = standardMaterial.normalMapType;
      physicalMaterial.normalScale.copy(standardMaterial.normalScale);
      physicalMaterial.displacementMap = standardMaterial.displacementMap;
      physicalMaterial.displacementScale = standardMaterial.displacementScale;
      physicalMaterial.displacementBias = standardMaterial.displacementBias;
      physicalMaterial.roughnessMap = standardMaterial.roughnessMap;
      physicalMaterial.metalnessMap = standardMaterial.metalnessMap;
      physicalMaterial.alphaMap = standardMaterial.alphaMap;
      physicalMaterial.envMap = standardMaterial.envMap;
      physicalMaterial.envMapIntensity = standardMaterial.envMapIntensity;
      physicalMaterial.emissiveMap = standardMaterial.emissiveMap;

      // 设置物理材质特有属性
      physicalMaterial.clearcoat = 0.0;
      physicalMaterial.clearcoatRoughness = 0.0;
      physicalMaterial.ior = 1.5;
      physicalMaterial.reflectivity = 0.5;
      physicalMaterial.transmission = 0.0;
      physicalMaterial.thickness = 0.0;

      return physicalMaterial;
    });
  }

  /**
   * 注册材质转换
   * @param fromType 源材质类型
   * @param toType 目标材质类型
   * @param converter 转换函数
   */
  public registerConversion(
    fromType: string,
    toType: string,
    converter: (material: THREE.Material) => THREE.Material
  ): void {
    if (!this.conversionMap.has(fromType)) {
      this.conversionMap.set(fromType, new Map());
    }
    this.conversionMap.get(fromType)!.set(toType, converter);
  }

  /**
   * 注册材质降级
   * @param type 材质类型
   * @param downgrader 降级函数
   */
  public registerDowngrade(
    type: string,
    downgrader: (material: THREE.Material) => THREE.Material
  ): void {
    this.downgradeMap.set(type, downgrader);
  }

  /**
   * 注册材质升级
   * @param type 材质类型
   * @param upgrader 升级函数
   */
  public registerUpgrade(
    type: string,
    upgrader: (material: THREE.Material) => THREE.Material
  ): void {
    this.upgradeMap.set(type, upgrader);
  }

  /**
   * 转换材质
   * @param material 材质
   * @param targetType 目标类型
   * @returns 转换后的材质
   */
  public convert(material: THREE.Material, targetType: string): THREE.Material {
    // 如果材质类型已经是目标类型，则直接返回
    if (this.getMaterialType(material) === targetType) {
      return material;
    }

    // 获取材质类型
    const materialType = this.getMaterialType(material);

    // 查找转换函数
    const converters = this.conversionMap.get(materialType);
    if (!converters) {
      console.warn(`没有找到从 ${materialType} 类型的转换函数`);
      return material;
    }

    const converter = converters.get(targetType);
    if (!converter) {
      console.warn(`没有找到从 ${materialType} 到 ${targetType} 的转换函数`);
      return material;
    }

    // 转换材质
    const convertedMaterial = converter(material);

    // 如果不保留原始材质，则释放原始材质
    if (!this.preserveOriginal) {
      (material as any).dispose();
    }

    return convertedMaterial;
  }

  /**
   * 降级材质
   * @param material 材质
   * @returns 降级后的材质
   */
  public downgrade(material: THREE.Material): THREE.Material {
    // 获取材质类型
    const materialType = this.getMaterialType(material);

    // 查找降级函数
    const downgrader = this.downgradeMap.get(materialType);
    if (!downgrader) {
      // 如果没有找到降级函数，则尝试转换为基础材质
      if (materialType !== MaterialType.BASIC) {
        return this.convert(material, MaterialType.BASIC);
      }
      return material;
    }

    // 降级材质
    const downgradedMaterial = downgrader(material);

    // 如果不保留原始材质，则释放原始材质
    if (!this.preserveOriginal) {
      (material as any).dispose();
    }

    return downgradedMaterial;
  }

  /**
   * 升级材质
   * @param material 材质
   * @returns 升级后的材质
   */
  public upgrade(material: THREE.Material): THREE.Material {
    // 获取材质类型
    const materialType = this.getMaterialType(material);

    // 查找升级函数
    const upgrader = this.upgradeMap.get(materialType);
    if (!upgrader) {
      // 如果没有找到升级函数，则尝试转换为物理材质
      if (materialType !== MaterialType.PHYSICAL) {
        return this.convert(material, MaterialType.PHYSICAL);
      }
      return material;
    }

    // 升级材质
    const upgradedMaterial = upgrader(material);

    // 如果不保留原始材质，则释放原始材质
    if (!this.preserveOriginal) {
      (material as any).dispose();
    }

    return upgradedMaterial;
  }

  /**
   * 获取材质类型
   * @param material 材质
   * @returns 材质类型
   */
  private getMaterialType(material: THREE.Material): string {
    if (material instanceof THREE.MeshBasicMaterial) {
      return MaterialType.BASIC;
    } else if (material instanceof THREE.MeshLambertMaterial) {
      return MaterialType.LAMBERT;
    } else if (material instanceof THREE.MeshPhongMaterial) {
      return MaterialType.PHONG;
    } else if (material instanceof THREE.MeshStandardMaterial) {
      if (material instanceof THREE.MeshPhysicalMaterial) {
        return MaterialType.PHYSICAL;
      }
      return MaterialType.STANDARD;
    } else if (material instanceof THREE.MeshToonMaterial) {
      return MaterialType.TOON;
    } else if (material instanceof THREE.MeshDepthMaterial) {
      return MaterialType.DEPTH;
    } else if (material instanceof THREE.MeshNormalMaterial) {
      return MaterialType.NORMAL;
    } else if (material instanceof THREE.LineBasicMaterial) {
      return MaterialType.LINE_BASIC;
    } else if (material instanceof THREE.LineDashedMaterial) {
      return MaterialType.LINE_DASHED;
    } else if (material instanceof THREE.PointsMaterial) {
      return MaterialType.POINTS;
    } else if (material instanceof THREE.SpriteMaterial) {
      return MaterialType.SPRITE;
    } else if (material instanceof THREE.ShaderMaterial) {
      if (material instanceof THREE.RawShaderMaterial) {
        return MaterialType.RAW_SHADER;
      }
      return MaterialType.SHADER;
    } else if (material instanceof THREE.ShadowMaterial) {
      return MaterialType.SHADOW;
    } else if (material instanceof THREE.MeshMatcapMaterial) {
      return MaterialType.MATCAP;
    }

    return 'unknown';
  }

  /**
   * 设置是否保留原始材质
   * @param preserve 是否保留
   */
  public setPreserveOriginal(preserve: boolean): void {
    this.preserveOriginal = preserve;
  }
}
