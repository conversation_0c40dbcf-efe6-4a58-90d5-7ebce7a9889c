/**
 * 文本场景生成面板
 * 提供文本输入的场景生成功能
 */
import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import {
  Card,
  Button,
  Space,
  Input,
  Select,
  Form,
  Row,
  Col,
  Switch,
  Progress,
  Typography,
  Alert,
  Collapse,
  Tag,
  Tooltip,
  List,
  Slider
} from 'antd';
import {
  ThunderboltOutlined,
  ClearOutlined,
  FileTextOutlined,
  BulbOutlined,
  SaveOutlined,
  LoadingOutlined
} from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;
const { Panel } = Collapse;

/**
 * 生成选项
 */
interface GenerationOptions {
  /** 场景风格 */
  style: string;
  /** 复杂程度 */
  complexity: 'simple' | 'medium' | 'complex';
  /** 实时预览 */
  realTimePreview: boolean;
  /** 质量级别 */
  qualityLevel: 'fast' | 'balanced' | 'high';
  /** 场景规模 */
  sceneScale: 'small' | 'medium' | 'large';
}

/**
 * 智能建议组件
 */
const SmartSuggestionComponent: React.FC<{
  currentDescription: string;
  onSuggestionSelect: (suggestion: string) => void;
}> = ({ currentDescription, onSuggestionSelect }) => {
  const [suggestions, setSuggestions] = useState<string[]>([]);

  useEffect(() => {
    // 根据当前描述生成建议
    const generateSuggestions = () => {
      const baseSuggestions = [
        '添加更多家具细节',
        '指定颜色和材质',
        '描述光照条件',
        '添加装饰元素',
        '指定场景风格'
      ];

      if (currentDescription.includes('办公')) {
        setSuggestions([
          '添加办公桌和椅子',
          '设置现代风格',
          '添加电脑和文件柜',
          '设置明亮的照明'
        ]);
      } else if (currentDescription.includes('客厅')) {
        setSuggestions([
          '添加沙发和茶几',
          '设置温馨的灯光',
          '添加电视和装饰品',
          '选择舒适的色调'
        ]);
      } else {
        setSuggestions(baseSuggestions);
      }
    };

    generateSuggestions();
  }, [currentDescription]);

  return (
    <div>
      <Title level={5}>智能建议</Title>
      <List
        size="small"
        dataSource={suggestions}
        renderItem={(suggestion) => (
          <List.Item
            actions={[
              <Button 
                type="link" 
                size="small" 
                onClick={() => onSuggestionSelect(suggestion)}
              >
                应用
              </Button>
            ]}
          >
            <BulbOutlined style={{ marginRight: '8px', color: '#faad14' }} />
            {suggestion}
          </List.Item>
        )}
      />
    </div>
  );
};

/**
 * 文本场景生成面板Ref接口
 */
export interface TextSceneGenerationPanelRef {
  /** 使用指定描述重新生成 */
  regenerateWithDescription: (description: string) => void;
  /** 获取当前描述 */
  getCurrentDescription: () => string;
  /** 设置描述 */
  setDescription: (description: string) => void;
}

/**
 * 文本场景生成面板属性
 */
interface TextSceneGenerationPanelProps {
  /** 是否禁用 */
  disabled?: boolean;
  /** 生成完成回调 */
  onSceneGenerated?: (scene: any) => void;
  /** 错误回调 */
  onError?: (error: string) => void;
}

/**
 * 文本场景生成面板
 */
const TextSceneGenerationPanel = forwardRef<TextSceneGenerationPanelRef, TextSceneGenerationPanelProps>(({
  disabled = false,
  onSceneGenerated,
  onError
}, ref) => {
  const [form] = Form.useForm();

  // 状态管理
  const [description, setDescription] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [previewEnabled, setPreviewEnabled] = useState(false);
  const [validationResult, setValidationResult] = useState<any>(null);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(false);
  const [generationOptions, setGenerationOptions] = useState<GenerationOptions>({
    style: 'modern',
    complexity: 'medium',
    realTimePreview: true,
    qualityLevel: 'balanced',
    sceneScale: 'medium'
  });

  // 组件初始化时加载保存的配置
  useEffect(() => {
    handleLoadConfiguration();
  }, []);

  // 自动保存功能
  useEffect(() => {
    if (autoSaveEnabled && (description || generationOptions)) {
      const timeoutId = setTimeout(() => {
        handleSaveConfiguration();
      }, 2000); // 2秒后自动保存

      return () => clearTimeout(timeoutId);
    }
  }, [description, generationOptions, autoSaveEnabled]);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    regenerateWithDescription: (desc: string) => {
      setDescription(desc);
      form.setFieldsValue({ description: desc });
      // 自动触发生成
      setTimeout(() => {
        handleGenerateScene();
      }, 100);
    },
    getCurrentDescription: () => description,
    setDescription: (desc: string) => {
      setDescription(desc);
      form.setFieldsValue({ description: desc });
    }
  }), [description, form]);

  /**
   * 处理描述变化
   */
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setDescription(value);

    // 实时验证
    if (value.length > 10) {
      validateDescription(value);
    }

    // 实时预览
    if (generationOptions.realTimePreview && value.length > 20) {
      debouncePreview(value);
    }
  };

  /**
   * 验证描述
   */
  const validateDescription = async (desc: string) => {
    try {
      // 模拟验证逻辑
      const validation = {
        isValid: desc.length >= 10,
        issues: desc.length < 10 ? ['描述过于简短'] : [],
        suggestions: desc.length < 20 ? ['请提供更详细的描述'] : []
      };

      setValidationResult(validation);
    } catch (error) {
      console.error('验证失败:', error);
    }
  };

  /**
   * 防抖预览
   */
  const debouncePreview = (() => {
    let timeoutId: NodeJS.Timeout;
    return (desc: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        generatePreview(desc);
      }, 1000);
    };
  })();

  /**
   * 生成预览
   */
  const generatePreview = async (desc: string) => {
    if (!previewEnabled) return;

    try {
      // 简化的预览生成逻辑
      const previewElements = extractElementsFromDescription(desc);
      const previewConfidence = calculateConfidence(desc);

      // 更新验证结果以显示预览信息
      setValidationResult({
        isValid: true,
        confidence: previewConfidence,
        suggestions: [],
        elements: previewElements,
        isPreview: true
      });

      console.log('预览生成完成:', {
        elements: previewElements,
        confidence: previewConfidence
      });
    } catch (error) {
      console.error('预览生成失败:', error);
    }
  };

  /**
   * 生成场景
   */
  const handleGenerateScene = async () => {
    if (!description.trim()) {
      setError('请输入场景描述');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);
    setError(null);

    try {
      // 模拟场景生成过程
      const steps = [
        { progress: 15, message: '验证输入描述...' },
        { progress: 30, message: '理解场景需求...' },
        { progress: 50, message: '生成场景布局...' },
        { progress: 70, message: '匹配场景资产...' },
        { progress: 85, message: '构建3D场景...' },
        { progress: 100, message: '场景生成完成！' }
      ];

      for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 800));
        setGenerationProgress(step.progress);
      }

      // 模拟生成结果
      const mockScene = {
        id: 'text_generated_scene_' + Date.now(),
        name: '文本生成的场景',
        description: description,
        options: generationOptions,
        elements: extractElementsFromDescription(description),
        confidence: calculateConfidence(description)
      };

      // 回调
      onSceneGenerated?.(mockScene);

      // 重置状态
      setIsGenerating(false);
      setGenerationProgress(0);

    } catch (error) {
      console.error('场景生成失败:', error);
      setError(error instanceof Error ? error.message : '场景生成失败');
      setIsGenerating(false);
      setGenerationProgress(0);
      onError?.(error instanceof Error ? error.message : '场景生成失败');
    }
  };

  /**
   * 从描述中提取元素
   */
  const extractElementsFromDescription = (desc: string): string[] => {
    const elements: string[] = [];
    const keywords = ['桌子', '椅子', '沙发', '床', '柜子', '灯', '电视', '电脑'];
    
    keywords.forEach(keyword => {
      if (desc.includes(keyword)) {
        elements.push(keyword);
      }
    });

    return elements;
  };

  /**
   * 计算置信度
   */
  const calculateConfidence = (desc: string): number => {
    let confidence = 0.5;
    
    if (desc.length > 50) confidence += 0.2;
    if (desc.includes('风格')) confidence += 0.1;
    if (desc.includes('颜色')) confidence += 0.1;
    if (desc.includes('材质')) confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  };

  /**
   * 清空描述
   */
  const handleClearDescription = () => {
    setDescription('');
    setValidationResult(null);
    form.resetFields(['description']);
  };

  /**
   * 加载模板
   */
  const handleLoadTemplate = () => {
    const templates = [
      '创建一个现代办公室，包含一张大会议桌，周围放置8把椅子，墙上挂着一块白板，角落里有一盆绿植，整体采用简约现代风格，光线明亮。',
      '设计一个温馨的客厅，有一套灰色布艺沙发，前面放着一张木质茶几，电视墙采用深色木纹，旁边有一个书架，整体色调温暖舒适。',
      '构建一个简约的卧室，有一张双人床，床头两侧各有一个床头柜和台灯，对面是一个大衣柜，窗边有一把休闲椅，整体风格简洁明快。'
    ];

    const randomTemplate = templates[Math.floor(Math.random() * templates.length)];
    setDescription(randomTemplate);
    form.setFieldsValue({ description: randomTemplate });
  };

  /**
   * 处理建议选择
   */
  const handleSuggestionSelect = (suggestion: string) => {
    const newDescription = description + (description ? '，' : '') + suggestion;
    setDescription(newDescription);
    form.setFieldsValue({ description: newDescription });
  };

  /**
   * 保存当前配置
   */
  const handleSaveConfiguration = () => {
    try {
      const config = {
        generationOptions,
        description,
        timestamp: Date.now()
      };
      localStorage.setItem('textSceneGenerationConfig', JSON.stringify(config));
      // 这里可以添加成功提示
      console.log('配置已保存');
    } catch (error) {
      console.error('保存配置失败:', error);
    }
  };

  /**
   * 加载保存的配置
   */
  const handleLoadConfiguration = () => {
    try {
      const savedConfig = localStorage.getItem('textSceneGenerationConfig');
      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        setGenerationOptions(config.generationOptions || generationOptions);
        if (config.description) {
          setDescription(config.description);
          form.setFieldsValue({ description: config.description });
        }
        console.log('配置已加载');
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  };

  /**
   * 渲染描述输入区域
   */
  const renderDescriptionInput = () => (
    <div style={{ marginBottom: '20px' }}>
      <Form form={form} layout="vertical">
        <Form.Item
          label="场景描述"
          name="description"
          rules={[
            { required: true, message: '请输入场景描述' },
            { min: 10, message: '描述至少需要10个字符' }
          ]}
        >
          <TextArea
            rows={6}
            placeholder="请详细描述您想要创建的场景，例如：&#10;创建一个现代办公室，包含一张大会议桌，周围放置8把椅子，墙上挂着一块白板，角落里有一盆绿植，整体采用简约现代风格..."
            value={description}
            onChange={handleDescriptionChange}
            showCount
            maxLength={1000}
            disabled={disabled || isGenerating}
          />
        </Form.Item>
      </Form>

      {validationResult && (
        <div style={{ marginTop: '10px' }}>
          {validationResult.issues.length > 0 && (
            <Alert
              message="输入建议"
              description={validationResult.issues.join('，')}
              type="warning"
              showIcon
              style={{ marginBottom: '10px' }}
            />
          )}
          {validationResult.suggestions.length > 0 && (
            <div>
              <Text type="secondary">建议：</Text>
              {validationResult.suggestions.map((suggestion: string, index: number) => (
                <Tag key={index} color="blue" style={{ margin: '2px' }}>
                  {suggestion}
                </Tag>
              ))}
            </div>
          )}
          {validationResult.isPreview && validationResult.elements && (
            <div style={{ marginTop: '10px' }}>
              <Alert
                message="预览信息"
                description={
                  <div>
                    <Text>识别到的场景元素：</Text>
                    <div style={{ marginTop: '5px' }}>
                      {validationResult.elements.map((element: string, index: number) => (
                        <Tag key={index} color="green" style={{ margin: '2px' }}>
                          {element}
                        </Tag>
                      ))}
                    </div>
                    <div style={{ marginTop: '5px' }}>
                      <Text type="secondary">
                        预览置信度: {Math.round(validationResult.confidence * 100)}%
                      </Text>
                    </div>
                  </div>
                }
                type="info"
                showIcon
              />
            </div>
          )}
        </div>
      )}
    </div>
  );

  /**
   * 渲染生成选项
   */
  const renderGenerationOptions = () => (
    <div style={{ marginBottom: '20px' }}>
      <Row gutter={16}>
        <Col span={8}>
          <Form.Item label="场景风格">
            <Select
              value={generationOptions.style}
              onChange={(value) => setGenerationOptions(prev => ({...prev, style: value}))}
              disabled={disabled || isGenerating}
            >
              <Option value="modern">现代风格</Option>
              <Option value="classical">古典风格</Option>
              <Option value="minimalist">极简风格</Option>
              <Option value="industrial">工业风格</Option>
              <Option value="scandinavian">北欧风格</Option>
              <Option value="traditional">传统风格</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="复杂程度">
            <Select
              value={generationOptions.complexity}
              onChange={(value) => setGenerationOptions(prev => ({...prev, complexity: value}))}
              disabled={disabled || isGenerating}
            >
              <Option value="simple">简单</Option>
              <Option value="medium">中等</Option>
              <Option value="complex">复杂</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="质量级别">
            <Select
              value={generationOptions.qualityLevel}
              onChange={(value) => setGenerationOptions(prev => ({...prev, qualityLevel: value}))}
              disabled={disabled || isGenerating}
            >
              <Option value="fast">快速</Option>
              <Option value="balanced">平衡</Option>
              <Option value="high">高质量</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="场景规模">
            <Slider
              value={generationOptions.sceneScale === 'small' ? 1 : generationOptions.sceneScale === 'medium' ? 2 : 3}
              min={1}
              max={3}
              marks={{
                1: '小',
                2: '中',
                3: '大'
              }}
              onChange={(value) => {
                const scale = value === 1 ? 'small' : value === 2 ? 'medium' : 'large';
                setGenerationOptions(prev => ({...prev, sceneScale: scale}));
              }}
              disabled={disabled || isGenerating}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="实时预览">
            <Switch
              checked={generationOptions.realTimePreview}
              onChange={(checked) => {
                setGenerationOptions(prev => ({...prev, realTimePreview: checked}));
                setPreviewEnabled(checked);
              }}
              disabled={disabled || isGenerating}
            />
            <Text type="secondary" style={{ marginLeft: '8px' }}>
              输入时实时生成预览
            </Text>
          </Form.Item>
        </Col>
      </Row>
    </div>
  );

  /**
   * 渲染控制按钮
   */
  const renderControls = () => (
    <div style={{ marginBottom: '20px' }}>
      <Space>
        <Button
          type="primary"
          size="large"
          icon={<ThunderboltOutlined />}
          onClick={handleGenerateScene}
          disabled={disabled || !description.trim() || isGenerating}
          loading={isGenerating}
        >
          {isGenerating ? '生成中...' : '生成场景'}
        </Button>
        <Button 
          icon={<ClearOutlined />}
          onClick={handleClearDescription}
          disabled={disabled || isGenerating}
        >
          清空
        </Button>
        <Button 
          icon={<FileTextOutlined />}
          onClick={handleLoadTemplate}
          disabled={disabled || isGenerating}
        >
          加载模板
        </Button>
        <Tooltip title="保存当前配置">
          <Button
            icon={<SaveOutlined />}
            onClick={handleSaveConfiguration}
            disabled={disabled || isGenerating}
          >
            保存配置
          </Button>
        </Tooltip>
      </Space>
    </div>
  );

  /**
   * 渲染生成进度
   */
  const renderGenerationProgress = () => {
    if (!isGenerating && generationProgress === 0) return null;

    return (
      <div style={{ margin: '20px 0' }}>
        <Progress
          percent={generationProgress}
          status={isGenerating ? 'active' : 'success'}
          format={(percent) => `生成进度 ${percent}%`}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
        />
        {isGenerating && (
          <div style={{ textAlign: 'center', marginTop: '10px' }}>
            <LoadingOutlined style={{ marginRight: '8px' }} />
            <Text>正在生成场景，请稍候...</Text>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card title="文本场景生成" className="text-scene-panel">
      {error && (
        <Alert
          message="生成错误"
          description={error}
          type="error"
          closable
          onClose={() => setError(null)}
          style={{ marginBottom: '16px' }}
        />
      )}

      {renderDescriptionInput()}

      {renderGenerationOptions()}

      {renderControls()}

      {renderGenerationProgress()}

      <Collapse>
        <Panel header="智能建议" key="suggestions">
          <SmartSuggestionComponent
            currentDescription={description}
            onSuggestionSelect={handleSuggestionSelect}
          />
        </Panel>
        <Panel header="高级设置" key="advanced">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="启用实时预览">
                <Switch
                  checked={previewEnabled}
                  onChange={setPreviewEnabled}
                  disabled={disabled || isGenerating}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="自动保存">
                <Switch
                  checked={autoSaveEnabled}
                  onChange={setAutoSaveEnabled}
                  disabled={disabled || isGenerating}
                />
              </Form.Item>
            </Col>
          </Row>
        </Panel>
      </Collapse>
    </Card>
  );
});

// 设置组件显示名称
TextSceneGenerationPanel.displayName = 'TextSceneGenerationPanel';

export default TextSceneGenerationPanel;
