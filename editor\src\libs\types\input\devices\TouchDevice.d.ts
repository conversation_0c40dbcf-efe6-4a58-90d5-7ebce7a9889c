/**
 * 触摸输入设备
 */
import { BaseInputDevice } from '../InputDevice';
/**
 * 触摸输入设备
 */
export declare class TouchDevice extends BaseInputDevice {
    /** 目标元素 */
    private element;
    /** 是否阻止默认行为 */
    private preventDefault;
    /** 是否阻止事件传播 */
    private stopPropagation;
    /** 触摸点映射 */
    private touchPoints;
    /** 触摸事件处理器 */
    private touchEventHandlers;
    /**
     * 创建触摸输入设备
     * @param element 目标元素
     * @param preventDefault 是否阻止默认行为
     * @param stopPropagation 是否阻止事件传播
     */
    constructor(element?: HTMLElement, preventDefault?: boolean, stopPropagation?: boolean);
    /**
     * 初始化事件处理器
     */
    private initEventHandlers;
    /**
     * 初始化设备
     */
    initialize(): void;
    /**
     * 销毁设备
     */
    destroy(): void;
    /**
     * 添加事件监听器
     */
    private addEventListeners;
    /**
     * 移除事件监听器
     */
    private removeEventListeners;
    /**
     * 处理触摸开始事件
     * @param event 触摸事件
     */
    private handleTouchStart;
    /**
     * 处理触摸移动事件
     * @param event 触摸事件
     */
    private handleTouchMove;
    /**
     * 处理触摸结束事件
     * @param event 触摸事件
     */
    private handleTouchEnd;
    /**
     * 处理触摸取消事件
     * @param event 触摸事件
     */
    private handleTouchCancel;
    /**
     * 获取触摸点
     * @param identifier 触摸点标识符
     * @returns 触摸点
     */
    getTouchPoint(identifier: number): Touch | undefined;
    /**
     * 获取所有触摸点
     * @returns 触摸点列表
     */
    getTouchPoints(): Touch[];
    /**
     * 获取触摸点数量
     * @returns 触摸点数量
     */
    getTouchCount(): number;
    /**
     * 检查触摸点是否活跃
     * @param identifier 触摸点标识符
     * @returns 是否活跃
     */
    isTouchActive(identifier: number): boolean;
    /**
     * 获取触摸点位置
     * @param identifier 触摸点标识符
     * @returns 触摸点位置
     */
    getTouchPosition(identifier: number): {
        x: number;
        y: number;
    } | undefined;
    /**
     * 获取触摸点移动
     * @param identifier 触摸点标识符
     * @returns 触摸点移动
     */
    getTouchDelta(identifier: number): {
        x: number;
        y: number;
    } | undefined;
}
