/**
 * 优化水体渲染示例
 * 展示如何使用优化的水体渲染系统
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { WaterBodyComponent, WaterBodyType } from '../../engine/src/physics/water/WaterBodyComponent';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../engine/src/physics/water/WaterInteractionSystem';
import { WaterRenderer } from '../../engine/src/rendering/water/WaterRenderer';
import { WaterChunkSystem } from '../../engine/src/rendering/water/WaterChunkSystem';
import { WaterLODSystem } from '../../engine/src/rendering/water/WaterLODSystem';
import { WaterSurfaceRenderer } from '../../engine/src/rendering/water/WaterSurfaceRenderer';
import { WaterInstancedRenderer } from '../../engine/src/rendering/water/WaterInstancedRenderer';
import { WaterRenderOptimizationSystem, DevicePerformanceLevel } from '../../engine/src/rendering/water/WaterRenderOptimizationSystem';
import { OceanFFTWaveSimulation } from '../../engine/src/physics/water/OceanFFTWaveSimulation';
import { Debug } from '../../engine/src/utils/Debug';
import { PerformanceMonitor } from '../../engine/src/utils/PerformanceMonitor';

/**
 * 优化水体渲染示例
 */
export class OptimizedWaterRenderingExample {
  /** 世界 */
  private world: World;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水体交互系统 */
  private waterInteractionSystem: WaterInteractionSystem;
  /** 水体渲染器 */
  private waterRenderer: WaterRenderer;
  /** 水体分块系统 */
  private waterChunkSystem: WaterChunkSystem;
  /** 水体LOD系统 */
  private waterLODSystem: WaterLODSystem;
  /** 水体表面渲染器 */
  private waterSurfaceRenderer: WaterSurfaceRenderer;
  /** 水体实例化渲染器 */
  private waterInstancedRenderer: WaterInstancedRenderer;
  /** 水体渲染优化系统 */
  private waterRenderOptimizationSystem: WaterRenderOptimizationSystem;
  /** 海洋FFT波浪模拟 */
  private oceanFFTWaveSimulation: OceanFFTWaveSimulation;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor;
  /** 水体实体 */
  private waterEntities: Entity[] = [];
  /** 性能UI */
  private performanceUI: any;

  /**
   * 构造函数
   * @param world 世界
   */
  constructor(world: World) {
    this.world = world;
    this.performanceMonitor = PerformanceMonitor.getInstance();

    // 初始化系统
    this.initializeSystems();

    // 创建水体
    this.createWaterBodies();

    // 创建性能UI
    this.createPerformanceUI();

    Debug.log('OptimizedWaterRenderingExample', '优化水体渲染示例已初始化');
  }

  /**
   * 初始化系统
   */
  private initializeSystems(): void {
    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水体交互系统
    this.waterInteractionSystem = new WaterInteractionSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true
    });
    this.world.addSystem(this.waterInteractionSystem);

    // 创建水体渲染器
    this.waterRenderer = new WaterRenderer(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1
    });
    this.world.addSystem(this.waterRenderer);

    // 创建水体分块系统
    this.waterChunkSystem = new WaterChunkSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      chunkSize: 16,
      useLOD: true,
      useFrustumCulling: true,
      useOctree: true
    });
    this.world.addSystem(this.waterChunkSystem);

    // 创建水体LOD系统
    this.waterLODSystem = new WaterLODSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      lodDistances: [100, 300, 600, 1000],
      useSmoothTransition: true
    });
    this.world.addSystem(this.waterLODSystem);

    // 创建水体表面渲染器
    this.waterSurfaceRenderer = new WaterSurfaceRenderer(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true
    });
    this.world.addSystem(this.waterSurfaceRenderer);

    // 创建水体实例化渲染器
    this.waterInstancedRenderer = new WaterInstancedRenderer(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      maxInstances: 1000
    });
    this.world.addSystem(this.waterInstancedRenderer);

    // 创建海洋FFT波浪模拟
    this.oceanFFTWaveSimulation = new OceanFFTWaveSimulation(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      resolution: 256,
      size: 1000,
      windSpeed: 10,
      windDirection: new THREE.Vector2(1, 1),
      waveScale: 1.0,
      waveHeight: 1.0,
      waveSpeed: 1.0,
      useGPUCompute: true
    });
    this.world.addSystem(this.oceanFFTWaveSimulation);

    // 创建水体渲染优化系统
    this.waterRenderOptimizationSystem = new WaterRenderOptimizationSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 60,
      enablePerformanceMonitoring: true,
      enableAutoOptimization: true,
      enableBatteryOptimization: true,
      enableTemperatureOptimization: true,
      enableMemoryOptimization: true,
      enableGPUOptimization: true,
      enableCPUOptimization: true,
      enableMobileOptimization: true,
      enableDesktopOptimization: true,
      enableDebugVisualization: true,
      defaultPerformanceLevel: DevicePerformanceLevel.HIGH
    });
    this.world.addSystem(this.waterRenderOptimizationSystem);
  }

  /**
   * 创建水体
   */
  private createWaterBodies(): void {
    // 创建海洋
    this.createOcean();

    // 创建湖泊
    this.createLake();

    // 创建河流
    this.createRiver();

    // 创建瀑布
    this.createWaterfall();

    // 创建温泉
    this.createHotSpring();

    // 创建地下湖泊
    this.createUndergroundLake();

    // 创建地下河流
    this.createUndergroundRiver();
  }

  /**
   * 创建海洋
   */
  private createOcean(): void {
    // 创建海洋实体
    const oceanEntity = new Entity('ocean');
    this.world.addEntity(oceanEntity);

    // 创建海洋水体组件
    const oceanWaterBody = new WaterBodyComponent({
      type: WaterBodyType.OCEAN,
      size: { width: 1000, height: 100, depth: 1000 },
      waveStrength: 0.5,
      waveSpeed: 1.0,
      enableWaves: true,
      enableFlow: true,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableFoam: true,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true
    });
    oceanEntity.addComponent(oceanWaterBody);

    // 添加到水体物理系统
    this.waterPhysicsSystem.addWaterBody(oceanEntity, oceanWaterBody);

    // 添加到水体渲染优化系统
    this.waterRenderOptimizationSystem.addWaterBody(oceanEntity, oceanWaterBody);

    // 添加到水体实体列表
    this.waterEntities.push(oceanEntity);
  }

  /**
   * 创建湖泊
   */
  private createLake(): void {
    // 创建湖泊实体
    const lakeEntity = new Entity('lake');
    this.world.addEntity(lakeEntity);

    // 创建湖泊水体组件
    const lakeWaterBody = new WaterBodyComponent({
      type: WaterBodyType.LAKE,
      size: { width: 200, height: 20, depth: 200 },
      waveStrength: 0.2,
      waveSpeed: 0.5,
      enableWaves: true,
      enableFlow: false,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableFoam: true,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true
    });
    lakeEntity.addComponent(lakeWaterBody);

    // 添加到水体物理系统
    this.waterPhysicsSystem.addWaterBody(lakeEntity, lakeWaterBody);

    // 添加到水体渲染优化系统
    this.waterRenderOptimizationSystem.addWaterBody(lakeEntity, lakeWaterBody);

    // 添加到水体实体列表
    this.waterEntities.push(lakeEntity);
  }

  /**
   * 创建河流
   */
  private createRiver(): void {
    // 创建河流实体
    const riverEntity = new Entity('river');
    this.world.addEntity(riverEntity);

    // 创建河流水体组件
    const riverWaterBody = new WaterBodyComponent({
      type: WaterBodyType.RIVER,
      size: { width: 20, height: 5, depth: 500 },
      waveStrength: 0.1,
      waveSpeed: 0.8,
      enableWaves: true,
      enableFlow: true,
      flowDirection: { x: 0, y: 0, z: 1 },
      flowSpeed: 2.0,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableFoam: true,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true
    });
    riverEntity.addComponent(riverWaterBody);

    // 添加到水体物理系统
    this.waterPhysicsSystem.addWaterBody(riverEntity, riverWaterBody);

    // 添加到水体渲染优化系统
    this.waterRenderOptimizationSystem.addWaterBody(riverEntity, riverWaterBody);

    // 添加到水体实体列表
    this.waterEntities.push(riverEntity);
  }

  /**
   * 创建瀑布
   */
  private createWaterfall(): void {
    // 创建瀑布实体
    const waterfallEntity = new Entity('waterfall');
    this.world.addEntity(waterfallEntity);

    // 创建瀑布水体组件
    const waterfallWaterBody = new WaterBodyComponent({
      type: WaterBodyType.WATERFALL,
      size: { width: 10, height: 50, depth: 5 },
      waveStrength: 0.3,
      waveSpeed: 2.0,
      enableWaves: true,
      enableFlow: true,
      flowDirection: { x: 0, y: -1, z: 0 },
      flowSpeed: 5.0,
      enableReflection: false,
      enableRefraction: true,
      enableCaustics: false,
      enableFoam: true,
      enableUnderwaterFog: false,
      enableUnderwaterDistortion: true
    });
    waterfallEntity.addComponent(waterfallWaterBody);

    // 添加到水体物理系统
    this.waterPhysicsSystem.addWaterBody(waterfallEntity, waterfallWaterBody);

    // 添加到水体渲染优化系统
    this.waterRenderOptimizationSystem.addWaterBody(waterfallEntity, waterfallWaterBody);

    // 添加到水体实体列表
    this.waterEntities.push(waterfallEntity);
  }

  /**
   * 创建温泉
   */
  private createHotSpring(): void {
    // 创建温泉实体
    const hotSpringEntity = new Entity('hotSpring');
    this.world.addEntity(hotSpringEntity);

    // 创建温泉水体组件
    const hotSpringWaterBody = new WaterBodyComponent({
      type: WaterBodyType.HOT_SPRING,
      size: { width: 10, height: 2, depth: 10 },
      waveStrength: 0.1,
      waveSpeed: 0.3,
      enableWaves: true,
      enableFlow: false,
      temperature: 60, // 摄氏度
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: false,
      enableFoam: true,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true
    });
    hotSpringEntity.addComponent(hotSpringWaterBody);

    // 添加到水体物理系统
    this.waterPhysicsSystem.addWaterBody(hotSpringEntity, hotSpringWaterBody);

    // 添加到水体渲染优化系统
    this.waterRenderOptimizationSystem.addWaterBody(hotSpringEntity, hotSpringWaterBody);

    // 添加到水体实体列表
    this.waterEntities.push(hotSpringEntity);
  }

  /**
   * 创建地下湖泊
   */
  private createUndergroundLake(): void {
    // 创建地下湖泊实体
    const undergroundLakeEntity = new Entity('undergroundLake');
    this.world.addEntity(undergroundLakeEntity);

    // 创建地下湖泊水体组件
    const undergroundLakeWaterBody = new WaterBodyComponent({
      type: WaterBodyType.UNDERGROUND_LAKE,
      size: { width: 100, height: 10, depth: 100 },
      waveStrength: 0.05,
      waveSpeed: 0.2,
      enableWaves: true,
      enableFlow: false,
      enableReflection: false,
      enableRefraction: true,
      enableCaustics: true,
      enableFoam: false,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true
    });
    undergroundLakeEntity.addComponent(undergroundLakeWaterBody);

    // 添加到水体物理系统
    this.waterPhysicsSystem.addWaterBody(undergroundLakeEntity, undergroundLakeWaterBody);

    // 添加到水体渲染优化系统
    this.waterRenderOptimizationSystem.addWaterBody(undergroundLakeEntity, undergroundLakeWaterBody);

    // 添加到水体实体列表
    this.waterEntities.push(undergroundLakeEntity);
  }

  /**
   * 创建地下河流
   */
  private createUndergroundRiver(): void {
    // 创建地下河流实体
    const undergroundRiverEntity = new Entity('undergroundRiver');
    this.world.addEntity(undergroundRiverEntity);

    // 创建地下河流水体组件
    const undergroundRiverWaterBody = new WaterBodyComponent({
      type: WaterBodyType.UNDERGROUND_RIVER,
      size: { width: 10, height: 3, depth: 300 },
      waveStrength: 0.05,
      waveSpeed: 0.5,
      enableWaves: true,
      enableFlow: true,
      flowDirection: { x: 0, y: 0, z: 1 },
      flowSpeed: 1.5,
      enableReflection: false,
      enableRefraction: true,
      enableCaustics: true,
      enableFoam: false,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true
    });
    undergroundRiverEntity.addComponent(undergroundRiverWaterBody);

    // 添加到水体物理系统
    this.waterPhysicsSystem.addWaterBody(undergroundRiverEntity, undergroundRiverWaterBody);

    // 添加到水体渲染优化系统
    this.waterRenderOptimizationSystem.addWaterBody(undergroundRiverEntity, undergroundRiverWaterBody);

    // 添加到水体实体列表
    this.waterEntities.push(undergroundRiverEntity);
  }

  /**
   * 创建性能UI
   */
  private createPerformanceUI(): void {
    // 这里应该创建一个性能UI，显示性能数据和优化设置
    // 由于这是一个复杂的功能，这里只是一个占位符
    Debug.log('OptimizedWaterRenderingExample', '创建性能UI');
  }

  /**
   * 更新
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新性能UI
    this.updatePerformanceUI();
  }

  /**
   * 更新性能UI
   */
  private updatePerformanceUI(): void {
    // 获取性能数据
    const monitorData = this.waterRenderOptimizationSystem.getMonitorData();
    const currentPerformanceLevel = this.waterRenderOptimizationSystem.getCurrentPerformanceLevel();

    // 更新性能UI
    // 这里应该更新性能UI，显示性能数据和优化设置
    // 由于这是一个复杂的功能，这里只是一个占位符
  }

  /**
   * 设置性能级别
   * @param level 性能级别
   */
  public setPerformanceLevel(level: DevicePerformanceLevel): void {
    this.waterRenderOptimizationSystem.setPerformanceLevel(level);
  }

  /**
   * 销毁
   */
  public destroy(): void {
    // 移除所有水体实体
    for (const entity of this.waterEntities) {
      this.world.removeEntity(entity);
    }

    // 移除所有系统
    this.world.removeSystem(this.waterPhysicsSystem);
    this.world.removeSystem(this.waterInteractionSystem);
    this.world.removeSystem(this.waterRenderer);
    this.world.removeSystem(this.waterChunkSystem);
    this.world.removeSystem(this.waterLODSystem);
    this.world.removeSystem(this.waterSurfaceRenderer);
    this.world.removeSystem(this.waterInstancedRenderer);
    this.world.removeSystem(this.waterRenderOptimizationSystem);
    this.world.removeSystem(this.oceanFFTWaveSimulation);

    Debug.log('OptimizedWaterRenderingExample', '优化水体渲染示例已销毁');
  }
}
