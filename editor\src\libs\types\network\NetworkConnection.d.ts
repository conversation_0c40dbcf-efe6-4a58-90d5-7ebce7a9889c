/**
 * 网络连接
 * 定义网络连接的抽象接口
 */
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkMessage } from './NetworkMessage';
/**
 * 网络连接状态
 */
export declare enum NetworkConnectionState {
    /** 已断开连接 */
    DISCONNECTED = "disconnected",
    /** 正在连接 */
    CONNECTING = "connecting",
    /** 已连接 */
    CONNECTED = "connected",
    /** 正在断开连接 */
    DISCONNECTING = "disconnecting",
    /** 连接错误 */
    ERROR = "error"
}
/**
 * 网络连接
 * 定义网络连接的抽象接口
 */
export declare abstract class NetworkConnection extends EventEmitter {
    /** 连接状态 */
    protected state: NetworkConnectionState;
    /** 连接ID */
    protected id: string;
    /** 远程地址 */
    protected remoteAddress: string;
    /** 连接时间 */
    protected connectTime: number;
    /** 最后活动时间 */
    protected lastActivityTime: number;
    /** 发送的消息数量 */
    protected sentMessages: number;
    /** 接收的消息数量 */
    protected receivedMessages: number;
    /** 发送的字节数 */
    protected sentBytes: number;
    /** 接收的字节数 */
    protected receivedBytes: number;
    /**
     * 创建网络连接
     * @param id 连接ID
     * @param remoteAddress 远程地址
     */
    constructor(id: string, remoteAddress: string);
    /**
     * 连接到远程地址
     * @returns Promise
     */
    abstract connect(): Promise<void>;
    /**
     * 断开连接
     * @returns Promise
     */
    abstract disconnect(): Promise<void>;
    /**
     * 发送消息
     * @param type 消息类型
     * @param message 消息对象
     */
    abstract send(type: string, message: NetworkMessage): void;
    /**
     * 获取连接状态
     * @returns 连接状态
     */
    getState(): NetworkConnectionState;
    /**
     * 是否已连接
     * @returns 是否已连接
     */
    isConnected(): boolean;
    /**
     * 获取连接ID
     * @returns 连接ID
     */
    getId(): string;
    /**
     * 获取远程地址
     * @returns 远程地址
     */
    getRemoteAddress(): string;
    /**
     * 获取连接时间
     * @returns 连接时间
     */
    getConnectTime(): number;
    /**
     * 获取最后活动时间
     * @returns 最后活动时间
     */
    getLastActivityTime(): number;
    /**
     * 获取发送的消息数量
     * @returns 发送的消息数量
     */
    getSentMessages(): number;
    /**
     * 获取接收的消息数量
     * @returns 接收的消息数量
     */
    getReceivedMessages(): number;
    /**
     * 获取发送的字节数
     * @returns 发送的字节数
     */
    getSentBytes(): number;
    /**
     * 获取接收的字节数
     * @returns 接收的字节数
     */
    getReceivedBytes(): number;
    /**
     * 获取连接统计信息
     * @returns 连接统计信息
     */
    getStats(): {
        id: string;
        remoteAddress: string;
        state: NetworkConnectionState;
        connectTime: number;
        lastActivityTime: number;
        sentMessages: number;
        receivedMessages: number;
        sentBytes: number;
        receivedBytes: number;
        uptime: number;
    };
    /**
     * 重置统计信息
     */
    resetStats(): void;
    /**
     * 更新最后活动时间
     */
    protected updateLastActivityTime(): void;
    /**
     * 更新发送统计信息
     * @param messageSize 消息大小（字节）
     */
    protected updateSentStats(messageSize: number): void;
    /**
     * 更新接收统计信息
     * @param messageSize 消息大小（字节）
     */
    protected updateReceivedStats(messageSize: number): void;
}
