/**
 * 物理系统空间分区测试
 */
import * as CANN<PERSON> from 'cannon-es';
import * as THREE from 'three';
import { 
  SpatialPartitioningFactory, 
  SpatialPartitioningStrategy, 
  UniformGrid, 
  ISpatialPartitioning 
} from '../../src/physics/optimization/SpatialPartitioning';
import { Octree } from '../../src/physics/optimization/Octree';
import { SpatialHash } from '../../src/physics/optimization/SpatialHash';

describe('SpatialPartitioning', () => {
  describe('SpatialPartitioningFactory', () => {
    test('应该创建均匀网格', () => {
      const spatialPartitioning = SpatialPartitioningFactory.create(SpatialPartitioningStrategy.UNIFORM_GRID);
      expect(spatialPartitioning).toBeInstanceOf(UniformGrid);
    });

    test('应该创建八叉树', () => {
      const spatialPartitioning = SpatialPartitioningFactory.create(SpatialPartitioningStrategy.OCTREE);
      expect(spatialPartitioning).toBeInstanceOf(Octree);
    });

    test('应该创建空间哈希', () => {
      const spatialPartitioning = SpatialPartitioningFactory.create(SpatialPartitioningStrategy.SPATIAL_HASH);
      expect(spatialPartitioning).toBeInstanceOf(SpatialHash);
    });

    test('应该使用默认策略', () => {
      const spatialPartitioning = SpatialPartitioningFactory.create('invalid_strategy' as any);
      expect(spatialPartitioning).toBeInstanceOf(UniformGrid);
    });
  });

  describe('UniformGrid', () => {
    let uniformGrid: ISpatialPartitioning;

    beforeEach(() => {
      uniformGrid = new UniformGrid({
        cellSize: 5,
        worldSize: 100,
        worldCenter: new CANNON.Vec3(0, 0, 0),
        autoResize: true,
        useDynamicGrid: true,
        useDebugVisualization: false,
      });
    });

    test('应该正确添加物体', () => {
      // 创建物体
      const body = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(0, 0, 0),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });

      // 添加物体
      uniformGrid.add(body);

      // 验证物体数量
      expect(uniformGrid.getBodyCount()).toBe(1);
      expect(uniformGrid.getBodies()).toContain(body);
    });

    test('应该正确移除物体', () => {
      // 创建物体
      const body = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(0, 0, 0),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });

      // 添加物体
      uniformGrid.add(body);
      expect(uniformGrid.getBodyCount()).toBe(1);

      // 移除物体
      uniformGrid.remove(body);
      expect(uniformGrid.getBodyCount()).toBe(0);
      expect(uniformGrid.getBodies()).not.toContain(body);
    });

    test('应该正确更新物体', () => {
      // 创建物体
      const body = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(0, 0, 0),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });

      // 添加物体
      uniformGrid.add(body);

      // 移动物体
      body.position.set(10, 10, 10);
      body.computeAABB();

      // 更新物体
      uniformGrid.update(body);

      // 验证物体已更新
      const potentialCollisions = uniformGrid.queryPotentialCollisions(body);
      expect(potentialCollisions).toHaveLength(0);
    });

    test('应该正确查询区域', () => {
      // 创建物体
      const body1 = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(0, 0, 0),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });
      const body2 = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(10, 10, 10),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });

      // 添加物体
      uniformGrid.add(body1);
      uniformGrid.add(body2);

      // 查询区域
      const min = new CANNON.Vec3(-5, -5, -5);
      const max = new CANNON.Vec3(5, 5, 5);
      const bodies = uniformGrid.queryRegion(min, max);

      // 验证查询结果
      expect(bodies).toHaveLength(1);
      expect(bodies).toContain(body1);
      expect(bodies).not.toContain(body2);
    });

    test('应该正确查询射线', () => {
      // 创建物体
      const body1 = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(0, 0, 0),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });
      const body2 = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(10, 10, 10),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });

      // 添加物体
      uniformGrid.add(body1);
      uniformGrid.add(body2);

      // 查询射线
      const from = new CANNON.Vec3(-10, 0, 0);
      const to = new CANNON.Vec3(10, 0, 0);
      const bodies = uniformGrid.queryRay(from, to);

      // 验证查询结果
      expect(bodies).toHaveLength(1);
      expect(bodies).toContain(body1);
      expect(bodies).not.toContain(body2);
    });

    test('应该正确查询球体', () => {
      // 创建物体
      const body1 = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(0, 0, 0),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });
      const body2 = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(10, 10, 10),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });

      // 添加物体
      uniformGrid.add(body1);
      uniformGrid.add(body2);

      // 查询球体
      const center = new CANNON.Vec3(0, 0, 0);
      const radius = 5;
      const bodies = uniformGrid.querySphere(center, radius);

      // 验证查询结果
      expect(bodies).toHaveLength(1);
      expect(bodies).toContain(body1);
      expect(bodies).not.toContain(body2);
    });

    test('应该正确查询潜在碰撞', () => {
      // 创建物体
      const body1 = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(0, 0, 0),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });
      const body2 = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(2, 0, 0),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });
      const body3 = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(10, 10, 10),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });

      // 添加物体
      uniformGrid.add(body1);
      uniformGrid.add(body2);
      uniformGrid.add(body3);

      // 查询潜在碰撞
      const potentialCollisions = uniformGrid.queryPotentialCollisions(body1);

      // 验证查询结果
      expect(potentialCollisions).toHaveLength(1);
      expect(potentialCollisions).toContain(body2);
      expect(potentialCollisions).not.toContain(body3);
    });

    test('应该正确清空', () => {
      // 创建物体
      const body = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(0, 0, 0),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });

      // 添加物体
      uniformGrid.add(body);
      expect(uniformGrid.getBodyCount()).toBe(1);

      // 清空
      uniformGrid.clear();
      expect(uniformGrid.getBodyCount()).toBe(0);
      expect(uniformGrid.getBodies()).toHaveLength(0);
    });

    test('应该正确销毁', () => {
      // 创建物体
      const body = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(0, 0, 0),
        shape: new CANNON.Box(new CANNON.Vec3(1, 1, 1)),
      });

      // 添加物体
      uniformGrid.add(body);

      // 销毁
      uniformGrid.dispose();
      expect(uniformGrid.getBodyCount()).toBe(0);
      expect(uniformGrid.getBodies()).toHaveLength(0);
    });

    test('应该获取调试信息', () => {
      // 获取调试信息
      const debugInfo = uniformGrid.getDebugInfo();

      // 验证调试信息
      expect(debugInfo).toBeDefined();
      expect(debugInfo.cellSize).toBe(5);
      expect(debugInfo.worldSize).toBe(100);
      expect(debugInfo.bodyCount).toBe(0);
    });

    test('应该获取调试网格', () => {
      // 获取调试网格
      const debugMesh = uniformGrid.getDebugMesh();

      // 验证调试网格
      expect(debugMesh).toBeDefined();
      expect(debugMesh).toBeInstanceOf(THREE.Object3D);
    });
  });
});
