/**
 * 级联阴影映射(CSM)实现
 * 基于Three.js实现的级联阴影映射系统
 */
import * as THREE from 'three';
import { Frustum } from './Frustum';
import { EventEmitter } from '../../utils/EventEmitter';
/**
 * CSM模式枚举
 */
export declare const CSMModes: {
    UNIFORM: string;
    LOGARITHMIC: string;
    PRACTICAL: string;
    CUSTOM: string;
};
/**
 * CSM参数接口
 */
export interface CSMParams {
    light?: THREE.DirectionalLight;
    cascades?: number;
    maxFar?: number;
    mode?: string;
    shadowMapSize?: number;
    shadowBias?: number;
    lightDirection?: THREE.Vector3;
    lightDirectionUp?: THREE.Vector3;
    lightIntensity?: number;
    lightColor?: THREE.ColorRepresentation;
    lightNear?: number;
    lightFar?: number;
    lightMargin?: number;
    customSplitsCallback?: (amount: number, near: number, far: number, target: number[]) => void;
    fade?: boolean;
}
/**
 * 级联阴影映射类
 */
export declare class CSM extends EventEmitter {
    cascades: number;
    maxFar: number;
    mode: string;
    shadowBias: number;
    shadowNormalBias: number;
    shadowMapSize: number;
    lightDirection: THREE.Vector3;
    lightDirectionUp: THREE.Vector3;
    lightColor: THREE.ColorRepresentation;
    lightIntensity: number;
    lightMargin: number;
    customSplitsCallback?: (amount: number, near: number, far: number, target: number[]) => void;
    fade: boolean;
    mainFrustum: Frustum;
    frustums: Frustum[];
    breaks: number[];
    sourceLight?: THREE.DirectionalLight;
    lights: THREE.DirectionalLight[];
    lightEntities: any[];
    shaders: Map<THREE.Material, THREE.Shader>;
    materials: Set<THREE.Material>;
    needsUpdate: boolean;
    /**
     * 创建CSM实例
     * @param params CSM参数
     */
    constructor(params?: CSMParams);
    /**
     * 创建光源
     * @param sourceLight 源光源
     */
    createLights(sourceLight?: THREE.DirectionalLight): void;
    /**
     * 创建单个光源
     * @param light 光源
     * @param index 索引
     */
    createLight(light: THREE.DirectionalLight, index: number): void;
    /**
     * 更新
     */
    update(camera: THREE.Camera): void;
    /**
     * 更新光源位置和方向
     */
    updateLights(camera: THREE.Camera): void;
    /**
     * 注入着色器代码
     */
    injectInclude(): void;
    /**
     * 移除着色器代码
     */
    removeInclude(): void;
    /**
     * 设置材质
     * @param mesh 网格
     */
    setupMaterial(mesh: THREE.Mesh): void;
    /**
     * 清理材质
     * @param material 材质
     */
    teardownMaterial(material: THREE.Material): void;
    /**
     * 计算分割点
     */
    getBreaks(): void;
    /**
     * 获取均匀分割点
     */
    private getUniformBreaks;
    /**
     * 获取对数分割点
     */
    private getLogarithmicBreaks;
    /**
     * 获取实用分割点
     */
    private getPracticalBreaks;
    /**
     * 初始化级联
     */
    initCascades(): void;
    /**
     * 更新阴影边界
     */
    updateShadowBounds(): void;
    /**
     * 更改光源
     */
    changeLights(light: THREE.DirectionalLight): void;
    /**
     * 更新视锥体
     */
    updateFrustums(): void;
    /**
     * 销毁
     */
    dispose(): void;
}
