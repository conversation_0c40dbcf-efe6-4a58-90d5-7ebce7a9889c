/**
 * 角色组件
 * 用于表示一个角色实体
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
/**
 * 角色类型
 */
export declare enum AvatarType {
    /** 本地玩家 */
    LOCAL_PLAYER = "local_player",
    /** 远程玩家 */
    REMOTE_PLAYER = "remote_player",
    /** NPC */
    NPC = "npc",
    /** 其他 */
    OTHER = "other"
}
/**
 * 角色组件配置
 */
export interface AvatarComponentConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 角色类型 */
    type?: AvatarType;
    /** 用户ID */
    userId?: string;
    /** 角色名称 */
    name?: string;
    /** 角色模型URL */
    modelUrl?: string;
}
/**
 * 角色组件
 * 表示一个角色实体
 */
export declare class AvatarComponent extends Component {
    /** 组件类型 */
    static readonly TYPE = "AvatarComponent";
    /** 角色类型 */
    avatarType: AvatarType;
    /** 用户ID */
    userId: string;
    /** 角色名称 */
    name: string;
    /** 角色模型URL */
    modelUrl: string;
    /** 是否已加载 */
    isLoaded: boolean;
    /** 是否正在加载 */
    isLoading: boolean;
    /** 用户ID到角色实体的映射 */
    private static userAvatarEntities;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 组件配置
     */
    constructor(entity: Entity, config?: AvatarComponentConfig);
    /**
     * 获取用户的角色实体
     * @param userId 用户ID
     * @returns 角色实体
     */
    static getUserAvatarEntity(userId: string): Entity | undefined;
    /**
     * 获取本地玩家的角色实体
     * @returns 本地玩家的角色实体
     */
    static getSelfAvatarEntity(): Entity | undefined;
    /**
     * 设置用户ID
     * @param userId 用户ID
     */
    setUserId(userId: string): void;
    /**
     * 设置角色类型
     * @param type 角色类型
     */
    setType(type: AvatarType): void;
    /**
     * 设置角色名称
     * @param name 角色名称
     */
    setName(name: string): void;
    /**
     * 设置角色模型URL
     * @param modelUrl 角色模型URL
     */
    setModelUrl(modelUrl: string): void;
    /**
     * 设置加载状态
     * @param isLoaded 是否已加载
     */
    setLoaded(isLoaded: boolean): void;
    /**
     * 设置正在加载状态
     * @param isLoading 是否正在加载
     */
    setLoading(isLoading: boolean): void;
    /**
     * 组件销毁时调用
     */
    onDestroy(): void;
    /**
     * 克隆组件
     * @param entity 目标实体
     * @returns 克隆的组件
     */
    clone(entity: Entity): AvatarComponent;
}
