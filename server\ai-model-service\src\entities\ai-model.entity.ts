/**
 * AI模型实体
 */
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
  BeforeInsert,
  BeforeUpdate
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsNumber, IsBoolean, IsOptional, IsObject, Min, Max } from 'class-validator';
import { ModelVersion } from './model-version.entity';
import { InferenceLog } from './inference-log.entity';
import { ModelMetrics } from './model-metrics.entity';

// 模型类型枚举
export enum ModelType {
  BERT = 'bert',
  GPT = 'gpt',
  T5 = 't5',
  ROBERTA = 'roberta',
  DISTILBERT = 'distilbert',
  ALBERT = 'albert',
  XLNET = 'xlnet',
  BART = 'bart',
  STABLE_DIFFUSION = 'stable_diffusion',
  WHISPER = 'whisper',
  CLIP = 'clip',
  CUSTOM = 'custom'
}

// 模型状态枚举
export enum ModelStatus {
  LOADING = 'loading',
  READY = 'ready',
  ERROR = 'error',
  UPDATING = 'updating',
  DISABLED = 'disabled'
}

// 模型用途枚举
export enum ModelPurpose {
  TEXT_ANALYSIS = 'text_analysis',
  TEXT_GENERATION = 'text_generation',
  IMAGE_GENERATION = 'image_generation',
  IMAGE_ANALYSIS = 'image_analysis',
  SPEECH_RECOGNITION = 'speech_recognition',
  SPEECH_SYNTHESIS = 'speech_synthesis',
  TRANSLATION = 'translation',
  SUMMARIZATION = 'summarization',
  CLASSIFICATION = 'classification',
  RECOMMENDATION = 'recommendation',
  EMBEDDING = 'embedding'
}

// 硬件要求接口
export interface HardwareRequirements {
  minCPU: string;
  minMemory: number; // MB
  minGPU?: string;
  minStorage: number; // MB
  recommendedCPU?: string;
  recommendedMemory?: number; // MB
  recommendedGPU?: string;
}

// 模型配置接口
export interface ModelConfig {
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  topK?: number;
  repetitionPenalty?: number;
  stopSequences?: string[];
  batchSize?: number;
  timeout?: number;
  customParams?: Record<string, any>;
}

// 性能指标接口
export interface PerformanceMetrics {
  averageLatency: number; // ms
  throughput: number; // requests/sec
  accuracy?: number; // 0-1
  f1Score?: number; // 0-1
  memoryUsage: number; // MB
  cpuUsage: number; // %
  gpuUsage?: number; // %
  errorRate: number; // %
}

@Entity('ai_models')
@Index(['type', 'status'])
@Index(['purpose', 'status'])
@Index(['isActive', 'status'])
export class AIModel {
  @ApiProperty({ description: '模型ID' })
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ApiProperty({ description: '模型名称' })
  @Column({ length: 100 })
  @IsString()
  name!: string;

  @ApiProperty({ description: '模型显示名称' })
  @Column({ length: 200 })
  @IsString()
  displayName!: string;

  @ApiProperty({ description: '模型描述' })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '模型类型', enum: ModelType })
  @Column({ type: 'enum', enum: ModelType })
  @IsEnum(ModelType)
  type!: ModelType;

  @ApiProperty({ description: '模型用途', enum: ModelPurpose })
  @Column({ type: 'enum', enum: ModelPurpose })
  @IsEnum(ModelPurpose)
  purpose!: ModelPurpose;

  @ApiProperty({ description: '模型状态', enum: ModelStatus })
  @Column({ type: 'enum', enum: ModelStatus, default: ModelStatus.LOADING })
  @IsEnum(ModelStatus)
  status!: ModelStatus;

  @ApiProperty({ description: '当前版本' })
  @Column({ length: 50 })
  @IsString()
  currentVersion!: string;

  @ApiProperty({ description: '模型文件路径' })
  @Column({ length: 500 })
  @IsString()
  filePath!: string;

  @ApiProperty({ description: '模型文件大小(MB)' })
  @Column({ type: 'bigint' })
  @IsNumber()
  @Min(0)
  fileSize!: number;

  @ApiProperty({ description: '模型哈希值' })
  @Column({ length: 64, nullable: true })
  @IsOptional()
  @IsString()
  fileHash?: string;

  @ApiProperty({ description: '硬件要求' })
  @Column({ type: 'json' })
  @IsObject()
  hardwareRequirements!: HardwareRequirements;

  @ApiProperty({ description: '模型配置' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsObject()
  config?: ModelConfig;

  @ApiProperty({ description: '性能指标' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsObject()
  performanceMetrics?: PerformanceMetrics;

  @ApiProperty({ description: '是否激活' })
  @Column({ default: true })
  @IsBoolean()
  isActive!: boolean;

  @ApiProperty({ description: '是否为默认模型' })
  @Column({ default: false })
  @IsBoolean()
  isDefault!: boolean;

  @ApiProperty({ description: '优先级(1-10)' })
  @Column({ type: 'tinyint', default: 5 })
  @IsNumber()
  @Min(1)
  @Max(10)
  priority!: number;

  @ApiProperty({ description: '标签' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  tags?: string[];

  @ApiProperty({ description: '模型提供者' })
  @Column({ length: 100, nullable: true })
  @IsOptional()
  @IsString()
  provider?: string;

  @ApiProperty({ description: '许可证' })
  @Column({ length: 100, nullable: true })
  @IsOptional()
  @IsString()
  license?: string;

  @ApiProperty({ description: '模型URL' })
  @Column({ length: 500, nullable: true })
  @IsOptional()
  @IsString()
  modelUrl?: string;

  @ApiProperty({ description: '文档URL' })
  @Column({ length: 500, nullable: true })
  @IsOptional()
  @IsString()
  documentationUrl?: string;

  @ApiProperty({ description: '最后使用时间' })
  @Column({ type: 'timestamp', nullable: true })
  lastUsedAt?: Date;

  @ApiProperty({ description: '使用次数' })
  @Column({ type: 'bigint', default: 0 })
  @IsNumber()
  @Min(0)
  usageCount!: number;

  @ApiProperty({ description: '错误次数' })
  @Column({ type: 'int', default: 0 })
  @IsNumber()
  @Min(0)
  errorCount!: number;

  @ApiProperty({ description: '平均响应时间(ms)' })
  @Column({ type: 'float', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  averageResponseTime?: number;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn()
  createdAt!: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn()
  updatedAt!: Date;

  // 关联关系
  @OneToMany(() => ModelVersion, version => version.model, { cascade: true })
  versions!: ModelVersion[];

  @OneToMany(() => InferenceLog, log => log.model)
  inferenceLogs!: InferenceLog[];

  @OneToMany(() => ModelMetrics, metrics => metrics.model, { cascade: true })
  metrics!: ModelMetrics[];

  // 生命周期钩子
  @BeforeInsert()
  @BeforeUpdate()
  validateModel() {
    // 验证硬件要求
    if (!this.hardwareRequirements.minCPU || !this.hardwareRequirements.minMemory) {
      throw new Error('硬件要求必须包含最小CPU和内存配置');
    }

    // 验证文件路径
    if (!this.filePath || this.filePath.trim() === '') {
      throw new Error('模型文件路径不能为空');
    }

    // 验证文件大小
    if (this.fileSize <= 0) {
      throw new Error('模型文件大小必须大于0');
    }

    // 验证优先级
    if (this.priority < 1 || this.priority > 10) {
      throw new Error('优先级必须在1-10之间');
    }
  }

  // 实例方法
  /**
   * 更新使用统计
   */
  updateUsageStats(responseTime: number, isError: boolean = false) {
    this.usageCount++;
    this.lastUsedAt = new Date();

    if (isError) {
      this.errorCount++;
    } else {
      // 更新平均响应时间
      if (this.averageResponseTime) {
        this.averageResponseTime = (this.averageResponseTime * (this.usageCount - 1) + responseTime) / this.usageCount;
      } else {
        this.averageResponseTime = responseTime;
      }
    }
  }

  /**
   * 检查模型是否可用
   */
  isAvailable(): boolean {
    return this.isActive && this.status === ModelStatus.READY;
  }

  /**
   * 获取错误率
   */
  getErrorRate(): number {
    if (this.usageCount === 0) return 0;
    return (this.errorCount / this.usageCount) * 100;
  }

  /**
   * 检查硬件兼容性
   */
  checkHardwareCompatibility(availableResources: {
    cpu: string;
    memory: number;
    gpu?: string;
    storage: number;
  }): boolean {
    const req = this.hardwareRequirements;
    
    // 检查内存
    if (availableResources.memory < req.minMemory) {
      return false;
    }

    // 检查存储
    if (availableResources.storage < req.minStorage) {
      return false;
    }

    // 检查GPU（如果需要）
    if (req.minGPU && !availableResources.gpu) {
      return false;
    }

    return true;
  }

  /**
   * 获取模型摘要信息
   */
  getSummary() {
    return {
      id: this.id,
      name: this.name,
      displayName: this.displayName,
      type: this.type,
      purpose: this.purpose,
      status: this.status,
      version: this.currentVersion,
      isActive: this.isActive,
      isDefault: this.isDefault,
      priority: this.priority,
      usageCount: this.usageCount,
      errorRate: this.getErrorRate(),
      averageResponseTime: this.averageResponseTime,
      lastUsedAt: this.lastUsedAt
    };
  }
}
