/**
 * 手势抓取组件
 * 用于处理基于手势的抓取交互
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { Vector3, Quaternion } from 'three';
import { GestureType, GestureState, GestureDirection, GestureEventData } from '../../input/devices/GestureDevice';
import { Hand } from './GrabbableComponent';
import { GrabberComponent } from './GrabberComponent';
import type { Transform } from '../../scene/Transform';

/**
 * 手势抓取配置
 */
export interface GestureGrabComponentConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 抓取手势类型 */
  grabGestureType?: GestureType;
  /** 释放手势类型 */
  releaseGestureType?: GestureType;
  /** 旋转手势类型 */
  rotateGestureType?: GestureType;
  /** 缩放手势类型 */
  scaleGestureType?: GestureType;
  /** 抓取距离 */
  grabDistance?: number;
  /** 是否使用射线检测 */
  useRaycasting?: boolean;
  /** 是否使用手部追踪 */
  useHandTracking?: boolean;
  /** 是否使用手势预测 */
  useGesturePrediction?: boolean;
  /** 手势灵敏度 */
  gestureSensitivity?: number;
  /** 抓取回调 */
  onGrab?: (entity: Entity, hand: Hand) => void;
  /** 释放回调 */
  onRelease?: (entity: Entity, hand: Hand) => void;
}

/**
 * 手势抓取组件
 */
export class GestureGrabComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'GestureGrabComponent';

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否启用 */
  private _enabled: boolean;

  /** 抓取手势类型 */
  private _grabGestureType: GestureType;

  /** 释放手势类型 */
  private _releaseGestureType: GestureType;

  /** 旋转手势类型 */
  private _rotateGestureType: GestureType;

  /** 缩放手势类型 */
  private _scaleGestureType: GestureType;

  /** 抓取距离 */
  private _grabDistance: number;

  /** 是否使用射线检测 */
  private _useRaycasting: boolean;

  /** 是否使用手部追踪 */
  private _useHandTracking: boolean;

  /** 是否使用手势预测 */
  private _useGesturePrediction: boolean;

  /** 手势灵敏度 */
  private _gestureSensitivity: number;

  /** 抓取回调 */
  private _onGrab?: (entity: Entity, hand: Hand) => void;

  /** 释放回调 */
  private _onRelease?: (entity: Entity, hand: Hand) => void;

  /** 当前抓取的实体 - 左手 */
  private _leftHandGrabbedEntity?: Entity;

  /** 当前抓取的实体 - 右手 */
  private _rightHandGrabbedEntity?: Entity;

  /** 是否正在旋转 */
  private _isRotating: boolean = false;

  /** 是否正在缩放 */
  private _isScaling: boolean = false;

  /** 旋转起始角度 */
  private _rotationStart: number = 0;

  /** 缩放起始值 */
  private _scaleStart: number = 1;

  /** 手势位置历史 */
  private _gesturePositionHistory: Vector3[] = [];

  /** 手势时间历史 */
  private _gestureTimeHistory: number[] = [];

  /** 手势预测位置 */
  private _predictedPosition?: Vector3;

  /** 关联的抓取者组件 */
  private _grabberComponent?: GrabberComponent;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config: GestureGrabComponentConfig = {}) {
    super(GestureGrabComponent.TYPE);
    this.entity = entity;

    // 设置配置
    this._enabled = config.enabled !== undefined ? config.enabled : true;
    this._grabGestureType = config.grabGestureType || GestureType.PINCH;
    this._releaseGestureType = config.releaseGestureType || GestureType.TAP;
    this._rotateGestureType = config.rotateGestureType || GestureType.ROTATE;
    this._scaleGestureType = config.scaleGestureType || GestureType.PINCH;
    this._grabDistance = config.grabDistance || 1.0;
    this._useRaycasting = config.useRaycasting !== undefined ? config.useRaycasting : true;
    this._useHandTracking = config.useHandTracking !== undefined ? config.useHandTracking : false;
    this._useGesturePrediction = config.useGesturePrediction !== undefined ? config.useGesturePrediction : true;
    this._gestureSensitivity = config.gestureSensitivity || 1.0;
    this._onGrab = config.onGrab;
    this._onRelease = config.onRelease;

    // 查找抓取者组件
    this._grabberComponent = entity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    if (!this._grabberComponent && this._enabled) {
      Debug.warn('GestureGrabComponent', `实体 ${entity.id} 没有 GrabberComponent，手势抓取功能可能无法正常工作`);
    }
  }

  /**
   * 初始化组件
   */
  initialize(): void {
    if (!this._enabled) return;

    // 如果没有抓取者组件，尝试添加一个
    if (!this._grabberComponent) {
      this._grabberComponent = new GrabberComponent(this.entity);
      this.entity.addComponent(this._grabberComponent);
    }
  }

  /**
   * 处理手势事件
   * @param gestureEvent 手势事件数据
   */
  handleGestureEvent(gestureEvent: GestureEventData): void {
    if (!this._enabled) return;

    // 记录手势位置和时间（用于预测）
    if (this._useGesturePrediction) {
      this.recordGesturePosition(gestureEvent);
    }

    // 根据手势类型处理
    switch (gestureEvent.type) {
      case this._grabGestureType:
        this.handleGrabGesture(gestureEvent);
        break;
      case this._releaseGestureType:
        this.handleReleaseGesture(gestureEvent);
        break;
      case this._rotateGestureType:
        this.handleRotateGesture(gestureEvent);
        break;
      case this._scaleGestureType:
        this.handleScaleGesture(gestureEvent);
        break;
    }
  }

  /**
   * 记录手势位置
   * @param gestureEvent 手势事件数据
   */
  private recordGesturePosition(gestureEvent: GestureEventData): void {
    // 创建3D位置（z坐标暂时设为0）
    const position = new Vector3(gestureEvent.x, gestureEvent.y, 0);

    // 添加到历史记录
    this._gesturePositionHistory.push(position);
    this._gestureTimeHistory.push(Date.now());

    // 限制历史记录长度
    if (this._gesturePositionHistory.length > 10) {
      this._gesturePositionHistory.shift();
      this._gestureTimeHistory.shift();
    }

    // 预测下一个位置
    if (this._gesturePositionHistory.length >= 3) {
      this.predictNextPosition();
    }
  }

  /**
   * 预测下一个位置
   */
  private predictNextPosition(): void {
    // 简单线性预测
    const positions = this._gesturePositionHistory;
    const times = this._gestureTimeHistory;

    const lastPos = positions[positions.length - 1];
    const prevPos = positions[positions.length - 2];

    const lastTime = times[times.length - 1];
    const prevTime = times[times.length - 2];

    const dt = lastTime - prevTime;
    if (dt <= 0) return;

    const velocity = new Vector3().subVectors(lastPos, prevPos).divideScalar(dt / 1000);
    const predictTime = 100; // 预测100毫秒后的位置

    this._predictedPosition = new Vector3().copy(lastPos).add(
      velocity.multiplyScalar(predictTime / 1000)
    );
  }

  /**
   * 处理抓取手势
   * @param gestureEvent 手势事件数据
   */
  private handleGrabGesture(gestureEvent: GestureEventData): void {
    // 只处理开始状态
    if (gestureEvent.state !== GestureState.BEGIN) return;

    // 确定使用哪只手
    const hand = this.determineHand(gestureEvent);

    // 查找可抓取的实体
    const grabbableEntity = this.findGrabbableEntity(gestureEvent);
    if (!grabbableEntity) return;

    // 尝试抓取
    if (this._grabberComponent) {
      const success = this._grabberComponent.grab(grabbableEntity, hand);

      if (success) {
        // 更新状态
        if (hand === Hand.LEFT) {
          this._leftHandGrabbedEntity = grabbableEntity;
        } else {
          this._rightHandGrabbedEntity = grabbableEntity;
        }

        // 触发事件
        this.eventEmitter.emit('grab', this.entity, grabbableEntity, hand);

        // 调用回调
        if (this._onGrab) {
          this._onGrab(grabbableEntity, hand);
        }

        Debug.log('GestureGrabComponent', `通过手势抓取实体 ${grabbableEntity.id}，使用${hand === Hand.LEFT ? '左手' : '右手'}`);
      }
    }
  }

  /**
   * 处理释放手势
   * @param gestureEvent 手势事件数据
   */
  private handleReleaseGesture(gestureEvent: GestureEventData): void {
    // 只处理开始状态
    if (gestureEvent.state !== GestureState.BEGIN) return;

    // 确定使用哪只手
    const hand = this.determineHand(gestureEvent);

    // 获取当前抓取的实体
    const grabbedEntity = hand === Hand.LEFT ? this._leftHandGrabbedEntity : this._rightHandGrabbedEntity;
    if (!grabbedEntity) return;

    // 尝试释放
    if (this._grabberComponent) {
      const success = this._grabberComponent.release(hand);

      if (success) {
        // 更新状态
        if (hand === Hand.LEFT) {
          this._leftHandGrabbedEntity = undefined;
        } else {
          this._rightHandGrabbedEntity = undefined;
        }

        // 触发事件
        this.eventEmitter.emit('release', this.entity, grabbedEntity, hand);

        // 调用回调
        if (this._onRelease) {
          this._onRelease(grabbedEntity, hand);
        }

        Debug.log('GestureGrabComponent', `通过手势释放实体 ${grabbedEntity.id}，使用${hand === Hand.LEFT ? '左手' : '右手'}`);
      }
    }
  }

  /**
   * 处理旋转手势
   * @param gestureEvent 手势事件数据
   */
  private handleRotateGesture(gestureEvent: GestureEventData): void {
    // 获取当前抓取的实体（优先使用右手抓取的实体）
    const grabbedEntity = this._rightHandGrabbedEntity || this._leftHandGrabbedEntity;
    if (!grabbedEntity) return;

    // 处理不同的手势状态
    switch (gestureEvent.state) {
      case GestureState.BEGIN:
        // 开始旋转
        this._isRotating = true;
        this._rotationStart = gestureEvent.rotation || 0;
        break;

      case GestureState.UPDATE:
        // 继续旋转
        if (!this._isRotating) return;

        // 计算旋转角度差
        const currentRotation = gestureEvent.rotation || 0;
        const rotationDelta = currentRotation - this._rotationStart;

        // 应用旋转
        this.applyRotation(grabbedEntity, rotationDelta * this._gestureSensitivity);

        // 更新起始角度
        this._rotationStart = currentRotation;
        break;

      case GestureState.END:
        // 结束旋转
        this._isRotating = false;
        break;
    }
  }

  /**
   * 处理缩放手势
   * @param gestureEvent 手势事件数据
   */
  private handleScaleGesture(gestureEvent: GestureEventData): void {
    // 获取当前抓取的实体（优先使用右手抓取的实体）
    const grabbedEntity = this._rightHandGrabbedEntity || this._leftHandGrabbedEntity;
    if (!grabbedEntity) return;

    // 处理不同的手势状态
    switch (gestureEvent.state) {
      case GestureState.BEGIN:
        // 开始缩放
        this._isScaling = true;
        this._scaleStart = gestureEvent.scale || 1.0;
        break;

      case GestureState.UPDATE:
        // 继续缩放
        if (!this._isScaling) return;

        // 计算缩放比例
        const currentScale = gestureEvent.scale || 1.0;
        const scaleFactor = currentScale / this._scaleStart;

        // 应用缩放
        this.applyScale(grabbedEntity, scaleFactor);

        // 更新起始缩放
        this._scaleStart = currentScale;
        break;

      case GestureState.END:
        // 结束缩放
        this._isScaling = false;
        break;
    }
  }

  /**
   * 应用旋转
   * @param entity 实体
   * @param rotationDelta 旋转角度（弧度）
   */
  private applyRotation(entity: Entity, rotationDelta: number): void {
    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) return;

    // 创建旋转四元数（绕Y轴旋转）
    const rotation = new Quaternion().setFromAxisAngle(
      new Vector3(0, 1, 0),
      rotationDelta
    );

    // 应用旋转
    (transform as any).rotation.multiply(rotation);

    // 触发事件
    this.eventEmitter.emit('rotate', entity, rotationDelta);
  }

  /**
   * 应用缩放
   * @param entity 实体
   * @param scaleFactor 缩放因子
   */
  private applyScale(entity: Entity, scaleFactor: number): void {
    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) return;

    // 应用缩放
    (transform as any).scale.multiplyScalar(scaleFactor);

    // 触发事件
    this.eventEmitter.emit('scale', entity, scaleFactor);
  }

  /**
   * 确定使用哪只手
   * @param gestureEvent 手势事件数据
   * @returns 手
   */
  private determineHand(gestureEvent: GestureEventData): Hand {
    // 根据手势位置确定使用哪只手
    // 如果在屏幕左侧，则使用左手；否则使用右手
    const screenWidth = window.innerWidth;
    return gestureEvent.x < screenWidth / 2 ? Hand.LEFT : Hand.RIGHT;
  }

  /**
   * 查找可抓取的实体
   * @param gestureEvent 手势事件数据
   * @returns 可抓取的实体
   */
  private findGrabbableEntity(gestureEvent: GestureEventData): Entity | undefined {
    // 这里应该实现射线检测或其他方法来查找可抓取的实体
    // 简单起见，这里返回null，实际项目中应该实现具体的查找逻辑

    // TODO: 实现射线检测查找可抓取实体

    return undefined;
  }

  /**
   * 更新组件
   * @param deltaTime 时间增量（秒）
   */
  update(deltaTime: number): void {
    if (!this._enabled) return;

    // 如果使用手部追踪，更新手部位置
    if (this._useHandTracking) {
      this.updateHandTracking();
    }
  }

  /**
   * 更新手部追踪
   */
  private updateHandTracking(): void {
    // TODO: 实现手部追踪逻辑
  }

  /**
   * 添加手势事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  addGestureListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除手势事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  removeGestureListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 清空事件监听器
    this.eventEmitter.removeAllListeners();

    // 重置状态
    this._leftHandGrabbedEntity = undefined;
    this._rightHandGrabbedEntity = undefined;
    this._isRotating = false;
    this._isScaling = false;
    this._gesturePositionHistory = [];
    this._gestureTimeHistory = [];
    this._predictedPosition = undefined;
  }
}
