/**
 * 资源瓶颈检测面板组件
 * 用于检测和显示资源使用瓶颈
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Progress, Statistic, Typography, Space, Button, Tabs, Switch, Select, Tooltip, Empty, Tag, List } from 'antd';
import { useTranslation } from 'react-i18next';
import { Pie } from '@ant-design/charts';
import {
  WarningOutlined,
  SearchOutlined,
  FileImageOutlined,
  AppstoreOutlined,
  CodeOutlined,
  BgColorsOutlined,
  AreaChartOutlined} from '@ant-design/icons';
// 移除引擎直接导入，使用模拟数据
import './ResourceBottleneckPanel.less';

// 定义本地类型以替代引擎类型
enum PerformanceMetricType {
  TEXTURE_COUNT = 'textureCount',
  TEXTURE_MEMORY = 'textureMemory',
  GEOMETRY_COUNT = 'geometryCount',
  GEOMETRY_MEMORY = 'geometryMemory',
  MATERIAL_COUNT = 'materialCount',
  SHADER_COUNT = 'shaderCount',
  RESOURCE_COUNT = 'resourceCount',
  MEMORY_USAGE = 'memoryUsage',
  RESOURCE_LOAD_TIME = 'resourceLoadTime'
}

enum PerformanceBottleneckType {
  RESOURCES = 'resources',
  MEMORY = 'memory'
}

// 模拟性能监控器
class PerformanceMonitorConfigConfig {
  private static instance: PerformanceMonitorConfigConfig;

  static getInstance() {
    if (!this.instance) {
      this.instance = new PerformanceMonitorConfigConfig();
    }
    return this.instance;
  }

  getReport() {
    return {
      metrics: {
        [PerformanceMetricType.TEXTURE_COUNT]: { value: 50 },
        [PerformanceMetricType.TEXTURE_MEMORY]: { value: 256 },
        [PerformanceMetricType.GEOMETRY_COUNT]: { value: 100 },
        [PerformanceMetricType.GEOMETRY_MEMORY]: { value: 128 },
        [PerformanceMetricType.MATERIAL_COUNT]: { value: 25 },
        [PerformanceMetricType.SHADER_COUNT]: { value: 10 },
        [PerformanceMetricType.RESOURCE_COUNT]: { value: 185 },
        [PerformanceMetricType.MEMORY_USAGE]: { value: 512 },
        [PerformanceMetricType.RESOURCE_LOAD_TIME]: { value: 2500 }
      },
      bottlenecks: [] as any[]
    };
  }
}

// 模拟内存分析器
class MemoryAnalyzer {
  private static instance: MemoryAnalyzer;

  static getInstance() {
    if (!this.instance) {
      this.instance = new MemoryAnalyzer();
    }
    return this.instance;
  }

  detectLeaks() {
    return {
      potentialLeaks: [],
      totalLeakedMemory: 0
    };
  }
}

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 资源瓶颈信息接口
 */
interface ResourceBottleneck {
  id: string;
  type: string;
  name: string;
  severity: number;
  impact: string;
  description: string;
  suggestions: string[];
  timestamp: number;
}

/**
 * 资源瓶颈检测面板属性接口
 */
interface ResourceBottleneckPanelProps {
  className?: string;
}

/**
 * 资源瓶颈检测面板组件
 */
const ResourceBottleneckPanel: React.FC<ResourceBottleneckPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  
  // 引用
  const performanceMonitor = useRef(PerformanceMonitorConfigConfig.getInstance());
  const memoryAnalyzer = useRef(MemoryAnalyzer.getInstance());
  const autoRefreshTimerRef = useRef<number | null>(null);

  // 状态
  const [bottlenecks, setBottlenecks] = useState<ResourceBottleneck[]>([]);
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(5000); // 5秒
  const [selectedBottleneck, setSelectedBottleneck] = useState<string | null>(null);
  const [resourceMetrics, setResourceMetrics] = useState<any>({
    textureCount: 0,
    textureMemory: 0,
    geometryCount: 0,
    geometryMemory: 0,
    materialCount: 0,
    shaderCount: 0,
    totalResources: 0,
    totalMemory: 0,
    loadTime: 0
  });
  
  // 初始化
  useEffect(() => {
    // 初始化检测
    detectBottlenecks();
    
    // 设置自动刷新
    if (autoRefresh) {
      startAutoRefresh();
    }
    
    return () => {
      stopAutoRefresh();
    };
  }, []);
  
  // 自动刷新
  useEffect(() => {
    if (autoRefresh) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
    
    return () => {
      stopAutoRefresh();
    };
  }, [autoRefresh, refreshInterval]);
  
  // 启动自动刷新
  const startAutoRefresh = () => {
    stopAutoRefresh();
    
    autoRefreshTimerRef.current = window.setInterval(() => {
      detectBottlenecks();
    }, refreshInterval);
  };
  
  // 停止自动刷新
  const stopAutoRefresh = () => {
    if (autoRefreshTimerRef.current !== null) {
      clearInterval(autoRefreshTimerRef.current);
      autoRefreshTimerRef.current = null;
    }
  };
  
  // 检测资源瓶颈
  const detectBottlenecks = async () => {
    setLoading(true);
    
    try {
      // 获取性能报告
      const report = performanceMonitor.current.getReport();
      
      // 获取资源指标
      const metrics = {
        textureCount: report.metrics[PerformanceMetricType.TEXTURE_COUNT]?.value || 0,
        textureMemory: report.metrics[PerformanceMetricType.TEXTURE_MEMORY]?.value || 0,
        geometryCount: report.metrics[PerformanceMetricType.GEOMETRY_COUNT]?.value || 0,
        geometryMemory: report.metrics[PerformanceMetricType.GEOMETRY_MEMORY]?.value || 0,
        materialCount: report.metrics[PerformanceMetricType.MATERIAL_COUNT]?.value || 0,
        shaderCount: report.metrics[PerformanceMetricType.SHADER_COUNT]?.value || 0,
        totalResources: report.metrics[PerformanceMetricType.RESOURCE_COUNT]?.value || 0,
        totalMemory: report.metrics[PerformanceMetricType.MEMORY_USAGE]?.value || 0,
        loadTime: report.metrics[PerformanceMetricType.RESOURCE_LOAD_TIME]?.value || 0
      };
      
      setResourceMetrics(metrics);
      
      // 过滤资源相关的瓶颈
      const resourceBottlenecks = report.bottlenecks
        .filter(bottleneck => 
          bottleneck.type === PerformanceBottleneckType.RESOURCES || 
          bottleneck.type === PerformanceBottleneckType.MEMORY)
        .map((bottleneck, index) => ({
          id: `bottleneck-${index}-${Date.now()}`,
          type: bottleneck.type,
          name: getBottleneckName(bottleneck.type),
          severity: bottleneck.severity,
          impact: getBottleneckImpact(bottleneck.severity),
          description: bottleneck.description,
          suggestions: bottleneck.optimizationSuggestions,
          timestamp: Date.now()
        }));
      
      // 获取内存分析器的泄漏检测结果
      const { potentialLeaks, totalLeakedMemory } = memoryAnalyzer.current.detectLeaks();
      
      // 如果有内存泄漏，添加到瓶颈列表
      if (potentialLeaks.length > 0) {
        resourceBottlenecks.push({
          id: `leak-${Date.now()}`,
          type: 'memory-leak',
          name: t('debug.bottleneck.memoryLeak'),
          severity: Math.min(1, totalLeakedMemory / (1024 * 1024 * 100)), // 根据泄漏内存大小计算严重程度
          impact: getBottleneckImpact(Math.min(1, totalLeakedMemory / (1024 * 1024 * 100))),
          description: `检测到 ${potentialLeaks.length} 个潜在内存泄漏，总计 ${formatBytes(totalLeakedMemory)}`,
          suggestions: [
            '检查长时间未释放的资源',
            '确保不再使用的资源被正确释放',
            '实现资源自动清理机制',
            '使用弱引用管理临时资源',
            '定期执行垃圾回收'
          ],
          timestamp: Date.now()
        });
      }
      
      // 模拟检查大型纹理（由于EngineService结构问题，使用模拟数据）
      try {
        // 模拟大型纹理检测
        const mockLargeTextures = [
          { name: 'environment_map.jpg', size: 8 * 1024 * 1024 },
          { name: 'character_diffuse.png', size: 6 * 1024 * 1024 }
        ];

        if (mockLargeTextures.length > 0) {
          resourceBottlenecks.push({
            id: `large-textures-${Date.now()}`,
            type: 'large-textures',
            name: t('debug.bottleneck.largeTextures'),
            severity: Math.min(1, mockLargeTextures.length / 10), // 根据大型纹理数量计算严重程度
            impact: getBottleneckImpact(Math.min(1, mockLargeTextures.length / 10)),
            description: `检测到 ${mockLargeTextures.length} 个大型纹理，可能导致内存压力`,
            suggestions: [
              '压缩纹理或降低分辨率',
              '使用纹理图集合并小纹理',
              '对远处物体使用低分辨率纹理',
              '实现纹理流式加载',
              '使用纹理缓存管理策略'
            ],
            timestamp: Date.now()
          });
        }
      } catch (error) {
        console.warn('无法检测大型纹理:', error);
      }
      
      // 更新瓶颈列表
      setBottlenecks(resourceBottlenecks);
    } catch (error) {
      console.error('检测资源瓶颈失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // 获取瓶颈名称
  const getBottleneckName = (type: string): string => {
    switch (type) {
      case PerformanceBottleneckType.RESOURCES:
        return t('debug.bottleneck.resources');
      case PerformanceBottleneckType.MEMORY:
        return t('debug.bottleneck.memory');
      default:
        return t('debug.bottleneck.unknown');
    }
  };
  
  // 获取瓶颈影响
  const getBottleneckImpact = (severity: number): string => {
    if (severity >= 0.8) {
      return t('debug.bottleneck.critical');
    } else if (severity >= 0.5) {
      return t('debug.bottleneck.high');
    } else if (severity >= 0.3) {
      return t('debug.bottleneck.medium');
    } else {
      return t('debug.bottleneck.low');
    }
  };
  
  // 格式化字节大小
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  // 获取严重程度颜色
  const getSeverityColor = (severity: number): string => {
    if (severity >= 0.8) {
      return '#f5222d'; // 红色
    } else if (severity >= 0.5) {
      return '#fa8c16'; // 橙色
    } else if (severity >= 0.3) {
      return '#faad14'; // 黄色
    } else {
      return '#52c41a'; // 绿色
    }
  };
  
  // 渲染瓶颈列表
  const renderBottleneckList = () => {
    if (bottlenecks.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('debug.bottleneck.noBottlenecks')}
        />
      );
    }
    
    return (
      <List
        className="bottleneck-list"
        itemLayout="horizontal"
        dataSource={bottlenecks}
        renderItem={item => (
          <List.Item
            key={item.id}
            className={selectedBottleneck === item.id ? 'selected-bottleneck' : ''}
            onClick={() => setSelectedBottleneck(item.id)}
          >
            <Card 
              className="bottleneck-card"
              title={
                <Space>
                  <WarningOutlined style={{ color: getSeverityColor(item.severity) }} />
                  <span>{item.name}</span>
                  <Tag color={getSeverityColor(item.severity)}>{item.impact}</Tag>
                </Space>
              }
              style={{ width: '100%' }}
            >
              <div className="bottleneck-content">
                <div className="bottleneck-severity">
                  <Tooltip title={`${(item.severity * 100).toFixed(0)}%`}>
                    <Progress
                      type="circle"
                      percent={item.severity * 100}
                      size={80}
                      strokeColor={getSeverityColor(item.severity)}
                      format={percent => `${percent?.toFixed(0)}%`}
                    />
                  </Tooltip>
                </div>
                <div className="bottleneck-details">
                  <Paragraph>{item.description}</Paragraph>
                  <div className="bottleneck-suggestions">
                    <Text strong>{t('debug.bottleneck.suggestions')}:</Text>
                    <ul>
                      {item.suggestions.slice(0, 3).map((suggestion, index) => (
                        <li key={index}><Text>{suggestion}</Text></li>
                      ))}
                      {item.suggestions.length > 3 && (
                        <li><Text type="secondary">...</Text></li>
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            </Card>
          </List.Item>
        )}
      />
    );
  };
  
  // 渲染资源指标
  const renderResourceMetrics = () => {
    const data = [
      { type: t('debug.resource.texture'), value: resourceMetrics.textureMemory },
      { type: t('debug.resource.geometry'), value: resourceMetrics.geometryMemory },
      { type: t('debug.resource.other'), value: resourceMetrics.totalMemory - resourceMetrics.textureMemory - resourceMetrics.geometryMemory },
    ].filter(item => item.value > 0);
    
    const config = {
      appendPadding: 10,
      data,
      angleField: 'value',
      colorField: 'type',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name}: {percentage}'
      },
      interactions: [{ type: 'element-active' }]
    };
    
    return (
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title={t('debug.resource.memoryUsage')}>
            <Statistic
              title={t('debug.resource.totalMemory')}
              value={resourceMetrics.totalMemory}
              precision={2}
              suffix="MB"
            />
            <Pie {...config} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title={t('debug.resource.resourceCounts')}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title={t('debug.resource.texture')}
                  value={resourceMetrics.textureCount}
                  prefix={<FileImageOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={t('debug.resource.geometry')}
                  value={resourceMetrics.geometryCount}
                  prefix={<AppstoreOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={t('debug.resource.material')}
                  value={resourceMetrics.materialCount}
                  prefix={<BgColorsOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={t('debug.resource.shader')}
                  value={resourceMetrics.shaderCount}
                  prefix={<CodeOutlined />}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    );
  };
  
  return (
    <div className={`resource-bottleneck-panel ${className || ''}`}>
      <div className="panel-header">
        <Space>
          <Title level={4}>{t('debug.bottleneck.title')}</Title>
          <Tooltip title={t('debug.bottleneck.detect')}>
            <Button
              icon={<SearchOutlined />}
              onClick={detectBottlenecks}
              loading={loading}
            />
          </Tooltip>
          <Switch
            checkedChildren={t('debug.bottleneck.autoRefreshOn')}
            unCheckedChildren={t('debug.bottleneck.autoRefreshOff')}
            checked={autoRefresh}
            onChange={setAutoRefresh}
          />
          <Select
            value={refreshInterval}
            onChange={value => setRefreshInterval(value)}
            disabled={!autoRefresh}
            style={{ width: 120 }}
          >
            <Option value={2000}>2 {t('debug.bottleneck.seconds')}</Option>
            <Option value={5000}>5 {t('debug.bottleneck.seconds')}</Option>
            <Option value={10000}>10 {t('debug.bottleneck.seconds')}</Option>
            <Option value={30000}>30 {t('debug.bottleneck.seconds')}</Option>
          </Select>
        </Space>
      </div>
      
      <Tabs defaultActiveKey="bottlenecks">
        <TabPane tab={<span><WarningOutlined />{t('debug.bottleneck.bottlenecks')}</span>} key="bottlenecks">
          {renderBottleneckList()}
        </TabPane>
        <TabPane tab={<span><AreaChartOutlined />{t('debug.bottleneck.metrics')}</span>} key="metrics">
          {renderResourceMetrics()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ResourceBottleneckPanel;
