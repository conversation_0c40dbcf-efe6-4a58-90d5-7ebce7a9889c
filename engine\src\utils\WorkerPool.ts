/**
 * 工作线程池
 * 用于管理和分配工作线程
 */
export class WorkerPool {
  /** 最大工作线程数量 */
  private limit: number;
  /** 任务队列 */
  private queue: Array<{ resolve: any; msg: any; transfer: Transferable[] }> = [];
  /** 工作线程 */
  private workers: Worker[] = [];
  /** 工作线程解析函数 */
  private workersResolve: Array<any> = [];
  /** 工作线程状态 */
  private workerStatus = 0;
  /** 工作线程创建器 */
  private workerCreator?: () => Worker;

  /**
   * 构造函数
   * @param pool 工作线程数量
   */
  constructor(pool = 1) {
    this.limit = pool;
  }

  /**
   * 初始化工作线程
   * @param workerId 工作线程ID
   */
  private _initWorker(workerId: number) {
    if (!this.workers[workerId]) {
      const worker = this.workerCreator!();
      worker.addEventListener('message', this._onMessage.bind(this, workerId));
      this.workers[workerId] = worker;
    }
  }

  /**
   * 获取空闲工作线程
   * @returns 工作线程ID
   */
  private _getIdleWorker() {
    for (let i = 0; i < this.limit; i++) if (!(this.workerStatus & (1 << i))) return i;
    return -1;
  }

  /**
   * 消息处理函数
   * @param workerId 工作线程ID
   * @param msg 消息
   */
  private _onMessage(workerId: number, msg: MessageEvent) {
    const resolve = this.workersResolve[workerId];
    resolve && resolve(msg);
    this.workersResolve[workerId] = null;

    if (this.queue.length) {
      const { resolve, msg, transfer } = this.queue.shift() as any;
      this.workersResolve[workerId] = resolve;
      this.workers[workerId].postMessage(msg, transfer);
    } else {
      this.workerStatus ^= 1 << workerId;
    }
  }

  /**
   * 设置工作线程创建器
   * @param workerCreator 工作线程创建器
   */
  public setWorkerCreator(workerCreator: () => Worker) {
    this.workerCreator = workerCreator;
  }

  /**
   * 设置工作线程数量限制
   * @param pool 工作线程数量
   */
  public setWorkerLimit(pool: number) {
    this.limit = pool;
  }

  /**
   * 发送消息
   * @param msg 消息
   * @param transfer 可转移对象
   * @returns Promise
   */
  public postMessage<T = any>(msg: any, transfer: Transferable[] = []): Promise<MessageEvent<T>> {
    return new Promise((resolve) => {
      const workerId = this._getIdleWorker();

      if (workerId !== -1) {
        this._initWorker(workerId);
        this.workerStatus |= 1 << workerId;
        this.workersResolve[workerId] = resolve;
        this.workers[workerId].postMessage(msg, transfer);
      } else {
        this.queue.push({ resolve, msg, transfer });
      }
    });
  }

  /**
   * 销毁工作线程池
   */
  public dispose() {
    this.workers.forEach((worker) => worker.terminate());
    this.workersResolve.length = 0;
    this.workers.length = 0;
    this.queue.length = 0;
    this.workerStatus = 0;
  }
}
