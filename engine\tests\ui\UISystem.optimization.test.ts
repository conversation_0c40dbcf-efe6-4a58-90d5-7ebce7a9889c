import { UISystem } from '../../src/ui/systems/UISystem';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';

describe('UISystem 优化功能测试', () => {
  let uiSystem: UISystem;
  let engine: Engine;
  let world: World;

  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine();
    world = new World();
    
    // 创建UI系统
    uiSystem = new UISystem();
    uiSystem.setEngine(engine);
    uiSystem.setWorld(world);
    
    // 初始化系统
    uiSystem.initialize();
  });

  afterEach(() => {
    // 清理资源
    uiSystem.dispose();
    world.dispose();
    engine.dispose();
  });

  describe('getComponents', () => {
    it('应该返回UI组件列表', () => {
      const components = uiSystem.getComponents();
      expect(Array.isArray(components)).toBe(true);
    });

    it('返回的组件应该包含必要的属性', () => {
      const components = uiSystem.getComponents();
      
      if (components.length > 0) {
        const component = components[0];
        expect(component).toHaveProperty('id');
        expect(component).toHaveProperty('name');
        expect(component).toHaveProperty('type');
        expect(component).toHaveProperty('renderTime');
        expect(component).toHaveProperty('visible');
        expect(component).toHaveProperty('memoryUsage');
      }
    });
  });

  describe('getOptimizationSettings', () => {
    it('应该返回优化设置对象', () => {
      const settings = uiSystem.getOptimizationSettings();
      expect(typeof settings).toBe('object');
      expect(settings).toHaveProperty('enableBatchRendering');
      expect(settings).toHaveProperty('enableEventDelegation');
      expect(settings).toHaveProperty('enableLazyLoading');
      expect(settings).toHaveProperty('enableVirtualization');
      expect(settings).toHaveProperty('enableMemoization');
      expect(settings).toHaveProperty('enableCulling');
    });
  });

  describe('updateOptimizationSettings', () => {
    it('应该能够更新优化设置', () => {
      const newSettings = {
        enableBatchRendering: false,
        enableEventDelegation: false,
        enableLazyLoading: true,
        enableVirtualization: true,
        enableMemoization: false,
        enableCulling: true
      };

      uiSystem.updateOptimizationSettings(newSettings);
      const updatedSettings = uiSystem.getOptimizationSettings();

      expect(updatedSettings.enableBatchRendering).toBe(false);
      expect(updatedSettings.enableEventDelegation).toBe(false);
      expect(updatedSettings.enableLazyLoading).toBe(true);
      expect(updatedSettings.enableVirtualization).toBe(true);
      expect(updatedSettings.enableMemoization).toBe(false);
      expect(updatedSettings.enableCulling).toBe(true);
    });
  });

  describe('optimizeComponents', () => {
    it('应该能够优化指定组件', async () => {
      const componentIds = ['test-component-1', 'test-component-2'];
      const options = {
        enableBatchRendering: true,
        enableEventDelegation: true,
        enableLazyLoading: false,
        enableVirtualization: false,
        enableMemoization: true,
        enableCulling: false
      };

      const results = await uiSystem.optimizeComponents(componentIds, options);

      expect(results).toHaveProperty('optimizedComponents');
      expect(results).toHaveProperty('batchedComponents');
      expect(results).toHaveProperty('memoizedComponents');
      expect(results).toHaveProperty('renderTimeReduction');
      expect(results).toHaveProperty('memoryReduction');
      
      expect(typeof results.optimizedComponents).toBe('number');
      expect(typeof results.renderTimeReduction).toBe('number');
      expect(typeof results.memoryReduction).toBe('number');
    });
  });

  describe('optimizeAllComponents', () => {
    it('应该能够优化所有组件', async () => {
      const options = {
        enableBatchRendering: true,
        enableEventDelegation: true,
        enableLazyLoading: true,
        enableVirtualization: false,
        enableMemoization: true,
        enableCulling: true
      };

      const results = await uiSystem.optimizeAllComponents(options);

      expect(results).toHaveProperty('optimizedComponents');
      expect(results).toHaveProperty('batchedComponents');
      expect(results).toHaveProperty('memoizedComponents');
      expect(results).toHaveProperty('virtualizedComponents');
      expect(results).toHaveProperty('lazyLoadedComponents');
      expect(results).toHaveProperty('culledComponents');
      expect(results).toHaveProperty('delegatedEvents');
      expect(results).toHaveProperty('removedEventListeners');
      expect(results).toHaveProperty('renderTimeReduction');
      expect(results).toHaveProperty('memoryReduction');
    });
  });
});
