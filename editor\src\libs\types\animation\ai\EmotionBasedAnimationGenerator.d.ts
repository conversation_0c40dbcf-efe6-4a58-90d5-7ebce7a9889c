import { IAIAnimationModel, AnimationGenerationRequest, AnimationGenerationResult } from './IAIAnimationModel';
/**
 * 基于情感的动画生成器
 */
export declare class EmotionBasedAnimationGenerator {
    /** AI模型 */
    private aiModel;
    /** 是否启用调试 */
    private debug;
    /**
     * 构造函数
     * @param aiModel AI模型
     * @param debug 是否启用调试
     */
    constructor(aiModel: IAIAnimationModel, debug?: boolean);
    /**
     * 生成面部动画
     * @param request 生成请求
     * @returns 生成结果
     */
    generateFacialAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    /**
     * 创建基于情感的面部动画片段
     * @param request 生成请求
     * @param emotionResult 情感分析结果
     * @returns 面部动画片段
     */
    private createEmotionBasedFacialClip;
    /**
     * 添加口型关键帧
     * @param clip 动画片段
     * @param request 生成请求
     */
    private addVisemeKeyframes;
    /**
     * 将情感映射到表情
     * @param emotion 情感
     * @returns 表情类型
     */
    private mapEmotionToExpression;
}
