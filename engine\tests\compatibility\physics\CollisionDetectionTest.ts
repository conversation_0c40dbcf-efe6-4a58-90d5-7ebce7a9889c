/**
 * 碰撞检测兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 碰撞检测兼容性测试
 */
export const collisionDetectionTest: TestCase = {
  name: '碰撞检测兼容性测试',
  description: '测试碰撞检测功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目物理系统实例
      const originalPhysicsSystem = new original.PhysicsSystem();
      originalPhysicsSystem.initialize();
      
      // 创建重构后项目物理系统实例
      const refactoredPhysicsSystem = new refactored.PhysicsSystem();
      refactoredPhysicsSystem.initialize();
      
      // 创建原有项目实体
      const originalEntity1 = new original.Entity('Entity1');
      const originalEntity2 = new original.Entity('Entity2');
      
      // 创建重构后项目实体
      const refactoredEntity1 = new refactored.Entity('Entity1');
      const refactoredEntity2 = new refactored.Entity('Entity2');
      
      // 创建原有项目刚体组件
      const originalBodyComponent1 = new original.PhysicsBodyComponent(originalEntity1, {
        mass: 1,
        type: 'dynamic'
      });
      
      const originalBodyComponent2 = new original.PhysicsBodyComponent(originalEntity2, {
        mass: 0,
        type: 'static'
      });
      
      // 创建重构后项目刚体组件
      const refactoredBodyComponent1 = new refactored.PhysicsBodyComponent(refactoredEntity1, {
        mass: 1,
        type: 'dynamic'
      });
      
      const refactoredBodyComponent2 = new refactored.PhysicsBodyComponent(refactoredEntity2, {
        mass: 0,
        type: 'static'
      });
      
      // 创建原有项目碰撞体组件
      const originalColliderComponent1 = new original.PhysicsColliderComponent(originalEntity1, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      });
      
      const originalColliderComponent2 = new original.PhysicsColliderComponent(originalEntity2, {
        shape: 'box',
        size: { x: 10, y: 0.1, z: 10 }
      });
      
      // 创建重构后项目碰撞体组件
      const refactoredColliderComponent1 = new refactored.PhysicsColliderComponent(refactoredEntity1, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      });
      
      const refactoredColliderComponent2 = new refactored.PhysicsColliderComponent(refactoredEntity2, {
        shape: 'box',
        size: { x: 10, y: 0.1, z: 10 }
      });
      
      // 添加组件到实体
      originalEntity1.addComponent(originalBodyComponent1);
      originalEntity1.addComponent(originalColliderComponent1);
      originalEntity2.addComponent(originalBodyComponent2);
      originalEntity2.addComponent(originalColliderComponent2);
      
      refactoredEntity1.addComponent(refactoredBodyComponent1);
      refactoredEntity1.addComponent(refactoredColliderComponent1);
      refactoredEntity2.addComponent(refactoredBodyComponent2);
      refactoredEntity2.addComponent(refactoredColliderComponent2);
      
      // 注册组件到物理系统
      originalPhysicsSystem.registerPhysicsBodyComponent(originalEntity1, originalBodyComponent1);
      originalPhysicsSystem.registerPhysicsColliderComponent(originalEntity1, originalColliderComponent1);
      originalPhysicsSystem.registerPhysicsBodyComponent(originalEntity2, originalBodyComponent2);
      originalPhysicsSystem.registerPhysicsColliderComponent(originalEntity2, originalColliderComponent2);
      
      refactoredPhysicsSystem.registerPhysicsBodyComponent(refactoredEntity1, refactoredBodyComponent1);
      refactoredPhysicsSystem.registerPhysicsColliderComponent(refactoredEntity1, refactoredColliderComponent1);
      refactoredPhysicsSystem.registerPhysicsBodyComponent(refactoredEntity2, refactoredBodyComponent2);
      refactoredPhysicsSystem.registerPhysicsColliderComponent(refactoredEntity2, refactoredColliderComponent2);
      
      // 设置实体位置
      originalBodyComponent1.setPosition({ x: 0, y: 5, z: 0 });
      originalBodyComponent2.setPosition({ x: 0, y: 0, z: 0 });
      
      refactoredBodyComponent1.setPosition({ x: 0, y: 5, z: 0 });
      refactoredBodyComponent2.setPosition({ x: 0, y: 0, z: 0 });
      
      // 设置碰撞回调
      let originalCollisionCount = 0;
      let refactoredCollisionCount = 0;
      
      originalPhysicsSystem.onCollisionEnter((entityA, entityB) => {
        if (
          (entityA === originalEntity1 && entityB === originalEntity2) ||
          (entityA === originalEntity2 && entityB === originalEntity1)
        ) {
          originalCollisionCount++;
        }
      });
      
      refactoredPhysicsSystem.onCollisionEnter((entityA, entityB) => {
        if (
          (entityA === refactoredEntity1 && entityB === refactoredEntity2) ||
          (entityA === refactoredEntity2 && entityB === refactoredEntity1)
        ) {
          refactoredCollisionCount++;
        }
      });
      
      // 更新物理系统多次，让物体下落并碰撞
      for (let i = 0; i < 100; i++) {
        originalPhysicsSystem.update(0.016);
        refactoredPhysicsSystem.update(0.016);
      }
      
      // 检查碰撞是否发生
      if (originalCollisionCount === 0 && refactoredCollisionCount === 0) {
        return {
          name: '碰撞检测兼容性测试',
          passed: false,
          errorMessage: '两个系统都没有检测到碰撞',
          details: {
            originalCollisionCount,
            refactoredCollisionCount
          }
        };
      }
      
      // 注意：由于物理引擎的实现差异，碰撞次数可能不完全相同，所以我们只检查是否都检测到了碰撞
      const originalCollisionDetected = originalCollisionCount > 0;
      const refactoredCollisionDetected = refactoredCollisionCount > 0;
      
      if (originalCollisionDetected !== refactoredCollisionDetected) {
        return {
          name: '碰撞检测兼容性测试',
          passed: false,
          errorMessage: `碰撞检测结果不一致: 原有项目=${originalCollisionDetected}, 重构后项目=${refactoredCollisionDetected}`,
          details: {
            originalCollisionCount,
            refactoredCollisionCount
          }
        };
      }
      
      // 销毁物理系统
      originalPhysicsSystem.destroy();
      refactoredPhysicsSystem.destroy();
      
      return {
        name: '碰撞检测兼容性测试',
        passed: true,
        details: {
          originalCollisionCount,
          refactoredCollisionCount
        }
      };
    } catch (error) {
      return {
        name: '碰撞检测兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
