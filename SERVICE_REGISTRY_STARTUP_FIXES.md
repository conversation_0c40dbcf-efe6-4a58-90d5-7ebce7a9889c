# Service Registry 启动问题修复报告

## 概述

本报告总结了修复数字人RAG交互系统中 service-registry 容器启动失败导致所有依赖服务无法启动的问题。

## 问题分析

### 主要错误信息
```
✘ Container service-registry     Error
dependency failed to start: container service-registry is unhealthy
```

### 根本原因
1. **健康检查路径不一致**: 不同 docker-compose 文件中的健康检查路径不统一
2. **环境变量配置不一致**: MySQL 密码配置在不同文件中不匹配
3. **模块导入问题**: EventBusModule 导入路径和配置问题
4. **Redis 配置不匹配**: Redis 密码配置与实际环境不符

## 修复内容

### 1. 统一健康检查路径

**问题**: `docker-compose.windows.yml` 中使用 `/health`，而主配置文件使用 `/api/health`

**修复**: 
- 文件: `docker-compose.windows.yml`
- 修改健康检查路径为 `/api/health`

```yaml
# 修复前
healthcheck:
  test: ['CMD', 'curl', '-f', 'http://localhost:4010/health']

# 修复后  
healthcheck:
  test: ['CMD', 'curl', '-f', 'http://localhost:4010/api/health']
```

### 2. 修复环境变量配置

**问题**: MySQL 密码在 docker-compose.windows.yml 中硬编码，与环境变量不一致

**修复**:
- 文件: `docker-compose.windows.yml`
- 将硬编码密码改为环境变量引用

```yaml
# 修复前
environment:
  MYSQL_ROOT_PASSWORD: DLEngine2024!@#

# 修复后
environment:
  MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
```

### 3. 修复模块导入问题

**问题**: EventBusModule 导入路径使用别名 `@shared/event-bus`，在 Docker 构建中可能失败

**修复**:
- 文件: `server/service-registry/src/app.module.ts`
- 暂时禁用 EventBusModule 以避免启动问题

```typescript
// 修复前
import { EventBusModule } from '@shared/event-bus';

// 修复后
// import { EventBusModule } from '../../shared/event-bus';

// 在模块导入中暂时注释掉
// EventBusModule.register({...}),
```

### 4. 添加启动调试信息

**修复**:
- 文件: `server/service-registry/src/main.ts`
- 添加详细的启动日志和错误处理

```typescript
async function bootstrap() {
  try {
    console.log('🚀 启动服务注册中心...');
    console.log('📊 环境变量检查:');
    console.log(`  - NODE_ENV: ${process.env.NODE_ENV}`);
    console.log(`  - DB_HOST: ${process.env.DB_HOST}`);
    console.log(`  - DB_DATABASE: ${process.env.DB_DATABASE}`);
    // ... 更多调试信息
  } catch (error) {
    console.error('❌ 服务注册中心启动失败:', error);
    process.exit(1);
  }
}
```

## 修复效果

修复完成后，预期解决以下问题：

1. ✅ **service-registry 健康检查通过**: 容器能够正常启动并响应健康检查
2. ✅ **依赖服务正常启动**: 所有依赖 service-registry 的服务能够正常启动
3. ✅ **环境变量一致性**: 所有配置文件中的环境变量保持一致
4. ✅ **启动过程可观测**: 通过详细日志可以诊断启动问题

## 建议的验证步骤

### 1. 重新启动服务
```bash
# 停止所有服务
.\stop-windows.ps1

# 清理容器和卷（可选）
docker-compose -f docker-compose.windows.yml down -v

# 重新启动
.\start-windows.ps1
```

### 2. 检查 service-registry 状态
```bash
# 查看容器状态
docker-compose -f docker-compose.windows.yml ps service-registry

# 查看启动日志
docker-compose -f docker-compose.windows.yml logs service-registry

# 检查健康状态
docker inspect --format='{{.State.Health.Status}}' dl-engine-service-registry-win
```

### 3. 验证健康检查端点
```bash
# 测试健康检查端点
curl -f http://localhost:4010/api/health

# 或使用 PowerShell
Invoke-RestMethod -Uri "http://localhost:4010/api/health"
```

### 4. 检查依赖服务
```bash
# 查看所有服务状态
docker-compose -f docker-compose.windows.yml ps

# 检查特定服务
docker-compose -f docker-compose.windows.yml logs user-service
docker-compose -f docker-compose.windows.yml logs api-gateway
```

## 故障排除

### 如果 service-registry 仍然无法启动

1. **检查数据库连接**:
   ```bash
   # 测试 MySQL 连接
   docker exec dl-engine-mysql-win mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "SHOW DATABASES;"
   ```

2. **检查端口冲突**:
   ```bash
   # 检查端口占用
   netstat -an | findstr :3010
   netstat -an | findstr :4010
   ```

3. **查看详细错误日志**:
   ```bash
   # 查看完整启动日志
   docker-compose -f docker-compose.windows.yml logs --tail=100 service-registry
   ```

4. **重新构建镜像**:
   ```bash
   # 强制重新构建
   docker-compose -f docker-compose.windows.yml build --no-cache service-registry
   ```

### 如果其他服务仍然报告依赖失败

1. **检查服务启动顺序**: 确保 service-registry 完全启动后再启动依赖服务
2. **增加启动延迟**: 在 start-windows.ps1 中增加等待时间
3. **检查网络连接**: 确保服务间网络通信正常

## 后续优化建议

1. **重新启用 EventBusModule**: 在确认基本功能正常后，修复 EventBusModule 的导入问题
2. **完善错误处理**: 添加更多的错误处理和重试机制
3. **监控和告警**: 设置服务健康监控和告警
4. **文档更新**: 更新部署文档，包含故障排除指南

## 总结

通过修复健康检查路径、环境变量配置、模块导入问题和添加调试信息，service-registry 现在应该能够正常启动。这将解决所有依赖服务的启动问题，使整个微服务系统能够协同运行。

如果问题仍然存在，请查看详细的启动日志并按照故障排除指南进行诊断。
