# 场景构建教程

## 简介

本教程将指导您从零开始构建一个完整的3D场景，包括地形创建、建筑物放置、植被系统、环境光照设置和后期处理效果。通过本教程，您将学习DL（Digital Learning）引擎的场景构建工作流程，掌握创建高质量3D场景的技巧和最佳实践。

## 功能特性

- **地形系统**：创建和编辑地形，设置地形纹理和细节
- **建筑系统**：放置和编辑建筑物，设置材质和细节
- **植被系统**：添加树木、草和其他植被，设置分布和密度
- **水系统**：添加河流、湖泊和海洋，设置水面效果
- **天空系统**：设置天空盒、云层和大气效果
- **光照系统**：设置环境光、方向光、点光源和区域光源
- **后期处理**：添加景深、环境光遮蔽、屏幕空间反射等效果
- **场景优化**：LOD、实例化渲染、遮挡剔除等优化技术

## 教程步骤

### 第1步：创建新场景

1. 打开DL（Digital Learning）引擎编辑器
2. 选择"文件 > 新建 > 场景"
3. 设置场景名称和保存位置
4. 点击"创建"按钮

### 第2步：创建地形

1. 选择"场景 > 添加 > 地形"
2. 设置地形大小和分辨率
3. 使用地形工具编辑地形高度
4. 添加地形纹理和细节

```javascript
// 创建地形
const terrain = new Entity('terrain');
terrain.addComponent(new TerrainComponent({
  width: 1000,
  height: 1000,
  resolution: 256,
  heightMap: 'assets/textures/heightmap.png',
  layers: [
    {
      texture: 'assets/textures/grass.jpg',
      tiling: 20,
      normalMap: 'assets/textures/grass_normal.jpg'
    },
    {
      texture: 'assets/textures/rock.jpg',
      tiling: 15,
      normalMap: 'assets/textures/rock_normal.jpg'
    },
    {
      texture: 'assets/textures/sand.jpg',
      tiling: 10,
      normalMap: 'assets/textures/sand_normal.jpg'
    }
  ]
}));
world.addEntity(terrain);
```

### 第3步：添加水面

1. 选择"场景 > 添加 > 水面"
2. 设置水面位置和大小
3. 调整水面材质和效果

```javascript
// 创建水面
const water = new Entity('water');
water.addComponent(new Transform({
  position: { x: 0, y: 5, z: 0 },
  scale: { x: 500, y: 1, z: 500 }
}));
water.addComponent(new WaterComponent({
  color: { r: 0.1, g: 0.3, b: 0.5 },
  transparency: 0.8,
  reflectivity: 0.6,
  waveHeight: 0.2,
  waveSpeed: 0.5
}));
world.addEntity(water);
```

### 第4步：设置天空

1. 选择"场景 > 添加 > 天空"
2. 选择天空盒纹理或程序化天空
3. 调整天空参数和效果

```javascript
// 设置天空
world.setSky(new Sky({
  type: 'cubemap',
  cubemap: 'assets/textures/sky/cubemap',
  exposure: 1.0,
  rotation: 0.5
}));
```

### 第5步：添加建筑物

1. 导入建筑物模型
2. 放置建筑物到场景中
3. 调整建筑物位置、旋转和缩放
4. 设置建筑物材质和细节

### 第6步：添加植被

1. 选择"场景 > 添加 > 植被系统"
2. 导入植被模型（树木、草等）
3. 设置植被分布区域和密度
4. 调整植被参数和效果

```javascript
// 创建植被系统
const vegetation = new Entity('vegetation');
vegetation.addComponent(new VegetationComponent({
  terrainEntity: 'terrain',
  items: [
    {
      model: 'assets/models/tree1.glb',
      density: 0.001,
      minScale: 0.8,
      maxScale: 1.2,
      minHeight: 10,
      maxHeight: 50,
      slopeMin: 0,
      slopeMax: 30
    },
    {
      model: 'assets/models/grass1.glb',
      density: 0.05,
      minScale: 0.5,
      maxScale: 1.5,
      minHeight: 5,
      maxHeight: 40,
      slopeMin: 0,
      slopeMax: 45
    }
  ]
}));
world.addEntity(vegetation);
```

### 第7步：设置光照

1. 添加方向光（太阳光）
2. 添加环境光
3. 添加点光源和区域光源
4. 调整光照参数和阴影设置

### 第8步：添加后期处理效果

1. 选择"场景 > 添加 > 后期处理"
2. 添加景深效果
3. 添加环境光遮蔽效果
4. 添加屏幕空间反射效果
5. 调整后期处理参数和效果

### 第9步：场景优化

1. 设置LOD（细节层次）
2. 启用实例化渲染
3. 设置遮挡剔除
4. 优化资源和性能

## 学习要点

- 了解场景构建的基本工作流程
- 掌握地形、建筑、植被等系统的使用方法
- 学习光照和后期处理效果的设置技巧
- 理解场景优化的原理和方法

## 扩展建议

- 添加昼夜循环系统
- 实现季节变化效果
- 添加天气系统（雨、雪、雾等）
- 实现场景交互功能
- 添加角色和动画
