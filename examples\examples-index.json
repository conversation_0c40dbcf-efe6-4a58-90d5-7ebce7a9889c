{"version": "1.0.0", "categories": [{"id": "basic", "name": "基础功能", "description": "展示编辑器的基本功能和用法"}, {"id": "material", "name": "材质系统", "description": "展示材质系统的功能和用法"}, {"id": "animation", "name": "动画系统", "description": "展示动画系统的功能和用法"}, {"id": "physics", "name": "物理系统", "description": "展示物理系统的功能和用法"}, {"id": "visualscript", "name": "视觉脚本", "description": "展示视觉脚本系统的功能和用法"}, {"id": "performance", "name": "性能优化", "description": "展示性能优化技术和方法"}, {"id": "collaboration", "name": "协作编辑", "description": "展示协作编辑功能和用法"}, {"id": "tutorial", "name": "教程项目", "description": "提供学习DL（Digital Learning）引擎的教程项目"}], "tags": ["基础功能", "编辑器", "入门", "材质", "PBR", "纹理", "动画", "骨骼", "混合", "物理", "碰撞", "约束", "视觉脚本", "节点", "性能", "优化", "协作", "多人", "教程", "学习"], "examples": [{"id": "editor-basics", "title": "基础编辑器功能演示", "description": "展示编辑器的基本功能，包括场景创建、对象操作和属性编辑。", "category": "basic", "tags": ["基础功能", "编辑器", "入门"], "previewImage": "/examples/assets/images/previews/editor-basics.jpg", "path": "/examples/editor-basics", "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-01-15", "updatedAt": "2023-03-20", "popularity": 128, "difficulty": "beginner"}, {"id": "material-editor", "title": "材质编辑器演示", "description": "展示如何创建和编辑各种材质类型，包括PBR材质、基础材质和特殊效果材质。", "category": "material", "tags": ["材质", "PBR", "纹理"], "previewImage": "/examples/assets/images/previews/material-editor.jpg", "path": "/examples/material-editor", "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-01-20", "updatedAt": "2023-03-25", "popularity": 96, "difficulty": "intermediate"}, {"id": "animation-demo", "title": "动画系统演示", "description": "展示关键帧动画、骨骼动画和动画混合功能，以及动画状态机的使用。", "category": "animation", "tags": ["动画", "骨骼", "混合"], "previewImage": "/examples/assets/images/previews/animation-demo.jpg", "path": "/examples/animation-demo", "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-01-25", "updatedAt": "2023-03-30", "popularity": 84, "difficulty": "intermediate"}, {"id": "physics-demo", "title": "物理系统演示", "description": "展示刚体物理、碰撞检测、物理约束和物理材质等功能。", "category": "physics", "tags": ["物理", "碰撞", "约束"], "previewImage": "/examples/assets/images/previews/physics-demo.jpg", "path": "/examples/physics-demo", "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-02-01", "updatedAt": "2023-04-05", "popularity": 72, "difficulty": "intermediate"}, {"id": "visualscript-demo", "title": "视觉脚本演示", "description": "展示视觉脚本系统的节点创建、连接、调试和执行功能。", "category": "visualscript", "tags": ["视觉脚本", "节点", "逻辑"], "previewImage": "/examples/assets/images/previews/visualscript-demo.jpg", "path": "/examples/visualscript-demo", "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-02-05", "updatedAt": "2023-04-10", "popularity": 68, "difficulty": "advanced"}, {"id": "performance-optimization", "title": "性能优化最佳实践", "description": "展示场景优化技术，如LOD、实例化、合并等，以及性能分析和瓶颈检测。", "category": "performance", "tags": ["性能", "优化", "LOD"], "previewImage": "/examples/assets/images/previews/performance-optimization.jpg", "path": "/examples/performance-optimization", "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-02-10", "updatedAt": "2023-04-15", "popularity": 56, "difficulty": "advanced"}, {"id": "collaborative-editing", "title": "协作编辑最佳实践", "description": "展示多人协作编辑功能和工作流程，包括权限管理、冲突解决等。", "category": "collaboration", "tags": ["协作", "多人", "权限"], "previewImage": "/examples/assets/images/previews/collaborative-editing.jpg", "path": "/examples/collaborative-editing", "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-02-15", "updatedAt": "2023-04-20", "popularity": 48, "difficulty": "advanced"}, {"id": "beginner-tutorial", "title": "新手入门教程", "description": "为初学者提供的入门教程，包括基本概念、界面操作和简单项目创建。", "category": "tutorial", "tags": ["教程", "入门", "学习"], "previewImage": "/examples/assets/images/previews/beginner-tutorial.jpg", "path": "/examples/beginner-tutorial", "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-02-20", "updatedAt": "2023-04-25", "popularity": 156, "difficulty": "beginner"}, {"id": "scene-building-tutorial", "title": "场景构建教程", "description": "详细介绍如何构建复杂场景，包括地形、植被、建筑和环境效果。", "category": "tutorial", "tags": ["教程", "场景", "环境"], "previewImage": "/examples/assets/images/previews/scene-building-tutorial.jpg", "path": "/examples/scene-building-tutorial", "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-02-25", "updatedAt": "2023-04-30", "popularity": 124, "difficulty": "intermediate"}, {"id": "character-creation-tutorial", "title": "角色创建教程", "description": "详细介绍如何创建和设置角色，包括模型导入、骨骼设置、动画绑定和控制脚本。", "category": "tutorial", "tags": ["教程", "角色", "动画"], "previewImage": "/examples/assets/images/previews/character-creation-tutorial.jpg", "path": "/examples/character-creation-tutorial", "author": "DL（Digital Learning）引擎团队", "createdAt": "2023-03-01", "updatedAt": "2023-05-05", "popularity": 108, "difficulty": "intermediate"}]}