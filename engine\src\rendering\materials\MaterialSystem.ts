/**
 * 材质系统
 * 负责管理和优化材质
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { EventEmitter } from '../../utils/EventEmitter';
import { MaterialFactory } from './MaterialFactory';
import { MaterialOptimizer } from './MaterialOptimizer';
import { MaterialConverter } from './MaterialConverter';
import { DeviceCapabilities } from '../../utils/DeviceCapabilities';

/**
 * 材质系统配置接口
 */
export interface MaterialSystemOptions {
  /** 是否启用自动优化 */
  autoOptimize?: boolean;
  /** 是否启用自动降级 */
  autoDowngrade?: boolean;
  /** 最大材质数量 */
  maxMaterials?: number;
  /** 是否启用材质缓存 */
  enableCache?: boolean;
}

/**
 * 材质系统类
 */
export class MaterialSystem extends System {
  /** 系统类型 */
  public static readonly type: string = 'MaterialSystem';

  /** 材质工厂 */
  private factory: MaterialFactory;

  /** 材质优化器 */
  private optimizer: MaterialOptimizer;

  /** 材质转换器 */
  private converter: MaterialConverter;

  /** 设备能力检测 */
  private deviceCapabilities: DeviceCapabilities;

  /** 是否启用自动优化 */
  private autoOptimize: boolean;

  /** 是否启用自动降级 */
  private autoDowngrade: boolean;

  /** 最大材质数量 */
  private maxMaterials: number;

  /** 是否启用材质缓存 */
  private enableCache: boolean;

  /** 材质映射表 */
  private materials: Map<string, THREE.Material> = new Map();

  /** 材质使用计数 */
  private materialUsageCount: Map<string, number> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 创建材质系统
   * @param options 材质系统配置
   */
  constructor(options: MaterialSystemOptions = {}) {
    super(100); // 设置优先级

    this.autoOptimize = options.autoOptimize !== undefined ? options.autoOptimize : true;
    this.autoDowngrade = options.autoDowngrade !== undefined ? options.autoDowngrade : true;
    this.maxMaterials = options.maxMaterials || 1000;
    this.enableCache = options.enableCache !== undefined ? options.enableCache : true;

    // 创建设备能力检测
    this.deviceCapabilities = DeviceCapabilities.getInstance();

    // 创建材质工厂
    this.factory = new MaterialFactory();

    // 创建材质优化器
    this.optimizer = new MaterialOptimizer({
      deviceCapabilities: this.deviceCapabilities
    });

    // 创建材质转换器
    this.converter = new MaterialConverter({
      deviceCapabilities: this.deviceCapabilities
    });
  }

  /**
   * 创建材质
   * @param type 材质类型
   * @param params 材质参数
   * @returns 材质
   */
  public createMaterial(type: string, params: any = {}): THREE.Material {
    // 生成材质ID
    const materialId = this.generateMaterialId(type, params);

    // 如果启用缓存且材质已存在，则返回现有材质
    if (this.enableCache && this.materials.has(materialId)) {
      const material = this.materials.get(materialId)!;
      this.incrementUsageCount(materialId);
      return material;
    }

    // 创建材质
    let material = this.factory.createMaterial(type, params);

    // 如果启用自动优化，则优化材质
    if (this.autoOptimize) {
      material = this.optimizer.optimizeMaterial(material);
    }

    // 如果启用自动降级且设备性能较低，则降级材质
    if (this.autoDowngrade && this.deviceCapabilities.isLowPerformanceDevice()) {
      material = this.converter.downgrade(material);
    }

    // 存储材质
    if (this.enableCache) {
      this.materials.set(materialId, material);
      this.materialUsageCount.set(materialId, 1);
    }

    // 检查材质数量是否超过限制
    this.checkMaterialLimit();

    // 触发材质创建事件
    this.eventEmitter.emit('materialCreated', material);

    return material;
  }

  /**
   * 获取材质
   * @param id 材质ID
   * @returns 材质
   */
  public getMaterial(id: string): THREE.Material | null {
    if (!this.materials.has(id)) {
      return null;
    }

    const material = this.materials.get(id)!;
    this.incrementUsageCount(id);
    return material;
  }

  /**
   * 释放材质
   * @param material 材质
   */
  public releaseMaterial(material: THREE.Material): void {
    const id = this.getMaterialId(material);
    if (!id) return;

    this.decrementUsageCount(id);
  }

  /**
   * 优化材质
   * @param material 材质
   * @returns 优化后的材质
   */
  public optimizeMaterial(material: THREE.Material): THREE.Material {
    return this.optimizer.optimizeMaterial(material);
  }

  /**
   * 转换材质类型
   * @param material 材质
   * @param targetType 目标类型
   * @returns 转换后的材质
   */
  public convertMaterial(material: THREE.Material, targetType: string): THREE.Material {
    return this.converter.convert(material, targetType);
  }

  /**
   * 降级材质
   * @param material 材质
   * @returns 降级后的材质
   */
  public downgradeMaterial(material: THREE.Material): THREE.Material {
    return this.converter.downgrade(material);
  }

  /**
   * 升级材质
   * @param material 材质
   * @returns 升级后的材质
   */
  public upgradeMaterial(material: THREE.Material): THREE.Material {
    return this.converter.upgrade(material);
  }

  /**
   * 设置是否启用自动优化
   * @param enabled 是否启用
   */
  public setAutoOptimize(enabled: boolean): void {
    this.autoOptimize = enabled;
  }

  /**
   * 设置是否启用自动降级
   * @param enabled 是否启用
   */
  public setAutoDowngrade(enabled: boolean): void {
    this.autoDowngrade = enabled;
  }

  /**
   * 设置最大材质数量
   * @param maxMaterials 最大材质数量
   */
  public setMaxMaterials(maxMaterials: number): void {
    this.maxMaterials = maxMaterials;
  }

  /**
   * 设置是否启用材质缓存
   * @param enabled 是否启用
   */
  public setEnableCache(enabled: boolean): void {
    this.enableCache = enabled;
  }

  /**
   * 清理未使用的材质
   */
  public cleanupUnusedMaterials(): void {
    const entriesToDelete: string[] = [];
    this.materialUsageCount.forEach((count, id) => {
      if (count <= 0) {
        entriesToDelete.push(id);
      }
    });

    for (const id of entriesToDelete) {
      const material = this.materials.get(id);
      if (material) {
        (material as any).dispose();
        this.materials.delete(id);
        this.materialUsageCount.delete(id);
        this.eventEmitter.emit('materialDisposed', id);
      }
    }
  }

  /**
   * 清理所有材质
   */
  public clearAllMaterials(): void {
    this.materials.forEach((material) => {
      (material as any).dispose();
    });
    this.materials.clear();
    this.materialUsageCount.clear();
    this.eventEmitter.emit('allMaterialsCleared');
  }

  /**
   * 获取材质数量
   * @returns 材质数量
   */
  public getMaterialCount(): number {
    return this.materials.size;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   * @returns 当前实例，用于链式调用
   */
  public on(event: string, callback: (...args: any[]) => void): this {
    this.eventEmitter.on(event, callback);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   * @returns 当前实例，用于链式调用
   */
  public off(event: string, callback?: (...args: any[]) => void): this {
    this.eventEmitter.off(event, callback);
    return this;
  }

  /**
   * 更新系统
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 定期清理未使用的材质
    if (this.enableCache && Math.random() < 0.01) { // 约每100帧清理一次
      this.cleanupUnusedMaterials();
    }
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.clearAllMaterials();
    this.eventEmitter.removeAllListeners();
    super.dispose();
  }

  /**
   * 生成材质ID
   * @param type 材质类型
   * @param params 材质参数
   * @returns 材质ID
   */
  private generateMaterialId(type: string, params: any): string {
    // 简单的材质ID生成方法，可以根据需要改进
    return `${type}_${JSON.stringify(params)}`;
  }

  /**
   * 获取材质ID
   * @param material 材质
   * @returns 材质ID
   */
  private getMaterialId(material: THREE.Material): string | null {
    let foundId: string | null = null;
    this.materials.forEach((mat, id) => {
      if (mat === material) {
        foundId = id;
      }
    });
    return foundId;
  }

  /**
   * 增加材质使用计数
   * @param id 材质ID
   */
  private incrementUsageCount(id: string): void {
    const count = this.materialUsageCount.get(id) || 0;
    this.materialUsageCount.set(id, count + 1);
  }

  /**
   * 减少材质使用计数
   * @param id 材质ID
   */
  private decrementUsageCount(id: string): void {
    const count = this.materialUsageCount.get(id) || 0;
    this.materialUsageCount.set(id, Math.max(0, count - 1));
  }

  /**
   * 检查材质数量是否超过限制
   */
  private checkMaterialLimit(): void {
    if (this.materials.size <= this.maxMaterials) {
      return;
    }

    // 找出使用次数最少的材质
    let leastUsedId: string | null = null;
    let leastUsedCount = Infinity;

    this.materialUsageCount.forEach((count, id) => {
      if (count < leastUsedCount) {
        leastUsedId = id;
        leastUsedCount = count;
      }
    });

    // 移除使用次数最少的材质
    if (leastUsedId) {
      const material = this.materials.get(leastUsedId);
      if (material) {
        (material as any).dispose();
        this.materials.delete(leastUsedId);
        this.materialUsageCount.delete(leastUsedId);
        this.eventEmitter.emit('materialDisposed', leastUsedId);
      }
    }
  }
}
