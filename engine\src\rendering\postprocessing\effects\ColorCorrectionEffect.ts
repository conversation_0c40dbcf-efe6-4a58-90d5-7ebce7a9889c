/**
 * 颜色校正效果
 * 提供更精确的颜色控制
 */
import * as THREE from 'three';
// 使用类型断言导入 ShaderPass
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 颜色校正效果选项
 */
export interface ColorCorrectionEffectOptions extends PostProcessingEffectOptions {
  /** 亮度 */
  brightness?: number;
  /** 对比度 */
  contrast?: number;
  /** 饱和度 */
  saturation?: number;
  /** 色相偏移 */
  hue?: number;
  /** 伽马 */
  gamma?: number;
  /** 色温 */
  temperature?: number;
  /** 色调 */
  tint?: number;
  /** 阴影色调 */
  shadowTint?: THREE.Color;
  /** 高光色调 */
  highlightTint?: THREE.Color;
  /** 是否使用LUT */
  useLUT?: boolean;
  /** LUT纹理 */
  lutTexture?: THREE.Texture;
  /** LUT强度 */
  lutIntensity?: number;
}

/**
 * 颜色校正效果
 */
export class ColorCorrectionEffect extends PostProcessingEffect {
  /** 亮度 */
  private brightness: number;

  /** 对比度 */
  private contrast: number;

  /** 饱和度 */
  private saturation: number;

  /** 色相偏移 */
  private hue: number;

  /** 伽马 */
  private gamma: number;

  /** 色温 */
  private temperature: number;

  /** 色调 */
  private tint: number;

  /** 阴影色调 */
  private shadowTint: THREE.Color;

  /** 高光色调 */
  private highlightTint: THREE.Color;

  /** 是否使用LUT */
  private useLUT: boolean;

  /** LUT纹理 */
  private lutTexture: THREE.Texture | null;

  /** LUT强度 */
  private lutIntensity: number;

  /** 颜色校正通道 */
  private colorCorrectionPass: ShaderPass | null = null;

  /**
   * 创建颜色校正效果
   * @param options 颜色校正效果选项
   */
  constructor(options: ColorCorrectionEffectOptions = { name: 'ColorCorrection' }) {
    super(options);

    this.brightness = options.brightness !== undefined ? options.brightness : 1.0;
    this.contrast = options.contrast !== undefined ? options.contrast : 1.0;
    this.saturation = options.saturation !== undefined ? options.saturation : 1.0;
    this.hue = options.hue !== undefined ? options.hue : 0.0;
    this.gamma = options.gamma !== undefined ? options.gamma : 1.0;
    this.temperature = options.temperature !== undefined ? options.temperature : 6500.0;
    this.tint = options.tint !== undefined ? options.tint : 0.0;
    this.shadowTint = options.shadowTint || new THREE.Color(0.0, 0.0, 0.2);
    this.highlightTint = options.highlightTint || new THREE.Color(1.0, 0.9, 0.8);
    this.useLUT = options.useLUT !== undefined ? options.useLUT : false;
    this.lutTexture = options.lutTexture || null;
    this.lutIntensity = options.lutIntensity !== undefined ? options.lutIntensity : 1.0;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 创建颜色校正着色器
    const colorCorrectionShader = {
      uniforms: {
        'tDiffuse': { value: null },
        'brightness': { value: this.brightness },
        'contrast': { value: this.contrast },
        'saturation': { value: this.saturation },
        'hue': { value: this.hue },
        'gamma': { value: this.gamma },
        'temperature': { value: this.temperature },
        'tint': { value: this.tint },
        'shadowTint': { value: this.shadowTint },
        'highlightTint': { value: this.highlightTint },
        'useLUT': { value: this.useLUT ? 1 : 0 },
        'lutTexture': { value: this.lutTexture },
        'lutIntensity': { value: this.lutIntensity }
      },
      vertexShader: /* glsl */`
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: /* glsl */`
        uniform sampler2D tDiffuse;
        uniform float brightness;
        uniform float contrast;
        uniform float saturation;
        uniform float hue;
        uniform float gamma;
        uniform float temperature;
        uniform float tint;
        uniform vec3 shadowTint;
        uniform vec3 highlightTint;
        uniform int useLUT;
        uniform sampler2D lutTexture;
        uniform float lutIntensity;
        
        varying vec2 vUv;
        
        const float PI = 3.14159265359;
        
        // RGB转HSL
        vec3 rgb2hsl(vec3 color) {
          float maxColor = max(max(color.r, color.g), color.b);
          float minColor = min(min(color.r, color.g), color.b);
          float delta = maxColor - minColor;
          
          float h = 0.0;
          float s = 0.0;
          float l = (maxColor + minColor) / 2.0;
          
          if (delta > 0.0) {
            s = l < 0.5 ? delta / (maxColor + minColor) : delta / (2.0 - maxColor - minColor);
            
            if (maxColor == color.r) {
              h = (color.g - color.b) / delta + (color.g < color.b ? 6.0 : 0.0);
            } else if (maxColor == color.g) {
              h = (color.b - color.r) / delta + 2.0;
            } else {
              h = (color.r - color.g) / delta + 4.0;
            }
            
            h /= 6.0;
          }
          
          return vec3(h, s, l);
        }
        
        // HSL转RGB
        float hue2rgb(float p, float q, float t) {
          if (t < 0.0) t += 1.0;
          if (t > 1.0) t -= 1.0;
          if (t < 1.0/6.0) return p + (q - p) * 6.0 * t;
          if (t < 1.0/2.0) return q;
          if (t < 2.0/3.0) return p + (q - p) * (2.0/3.0 - t) * 6.0;
          return p;
        }
        
        vec3 hsl2rgb(vec3 hsl) {
          float h = hsl.x;
          float s = hsl.y;
          float l = hsl.z;
          
          float q = l < 0.5 ? l * (1.0 + s) : l + s - l * s;
          float p = 2.0 * l - q;
          
          float r = hue2rgb(p, q, h + 1.0/3.0);
          float g = hue2rgb(p, q, h);
          float b = hue2rgb(p, q, h - 1.0/3.0);
          
          return vec3(r, g, b);
        }
        
        // 应用亮度
        vec3 applyBrightness(vec3 color, float brightness) {
          return color * brightness;
        }
        
        // 应用对比度
        vec3 applyContrast(vec3 color, float contrast) {
          return (color - 0.5) * contrast + 0.5;
        }
        
        // 应用饱和度
        vec3 applySaturation(vec3 color, float saturation) {
          float luminance = dot(color, vec3(0.2126, 0.7152, 0.0722));
          return mix(vec3(luminance), color, saturation);
        }
        
        // 应用色相偏移
        vec3 applyHue(vec3 color, float hue) {
          vec3 hsl = rgb2hsl(color);
          hsl.x = fract(hsl.x + hue);
          return hsl2rgb(hsl);
        }
        
        // 应用伽马校正
        vec3 applyGamma(vec3 color, float gamma) {
          return pow(color, vec3(1.0 / gamma));
        }
        
        // 应用色温
        vec3 applyTemperature(vec3 color, float temperature) {
          // 将色温转换为RGB偏移
          // 简化版本，实际上色温转换更复杂
          float t = temperature / 6500.0; // 归一化到6500K
          
          if (t < 1.0) {
            // 冷色调（蓝色）
            color.r *= 0.8 + 0.2 * t;
            color.b *= 1.0 / (0.8 + 0.2 * t);
          } else {
            // 暖色调（黄色）
            color.r *= 1.0 + 0.1 * (t - 1.0);
            color.b *= 1.0 - 0.1 * (t - 1.0);
          }
          
          return color;
        }
        
        // 应用色调
        vec3 applyTint(vec3 color, float tint) {
          // 正值增加绿色，负值增加洋红色
          if (tint > 0.0) {
            color.g *= 1.0 + 0.1 * tint;
            color.r *= 1.0 - 0.05 * tint;
            color.b *= 1.0 - 0.05 * tint;
          } else {
            color.g *= 1.0 + 0.1 * tint;
            color.r *= 1.0 - 0.05 * tint;
            color.b *= 1.0 - 0.05 * tint;
          }
          
          return color;
        }
        
        // 应用阴影和高光色调
        vec3 applyShadowHighlightTint(vec3 color, vec3 shadowTint, vec3 highlightTint) {
          float luminance = dot(color, vec3(0.2126, 0.7152, 0.0722));
          
          // 阴影区域
          if (luminance < 0.5) {
            float factor = 1.0 - luminance * 2.0;
            color = mix(color, color * shadowTint, factor * 0.5);
          }
          // 高光区域
          else {
            float factor = (luminance - 0.5) * 2.0;
            color = mix(color, color * highlightTint, factor * 0.5);
          }
          
          return color;
        }
        
        // 应用LUT
        vec3 applyLUT(vec3 color, sampler2D lut, float intensity) {
          // 假设LUT是一个2D纹理，大小为512x512，包含64x64个颜色
          const float lutSize = 64.0;
          
          // 将颜色值缩放到LUT空间
          color = clamp(color, 0.0, 1.0);
          float scale = (lutSize - 1.0) / lutSize;
          float offset = 1.0 / (2.0 * lutSize);
          
          // 计算LUT坐标
          float b = color.b * scale + offset;
          float bY = floor(b * lutSize) / lutSize;
          float bX = fract(b * lutSize);
          
          // 计算LUT纹理坐标
          vec2 redGreen = color.rg * scale + offset;
          vec2 texPos1 = vec2(redGreen.x + bX / lutSize, redGreen.y + bY);
          
          // 采样LUT
          vec3 lutColor = texture2D(lut, texPos1).rgb;
          
          // 混合原始颜色和LUT颜色
          return mix(color, lutColor, intensity);
        }
        
        void main() {
          vec4 texel = texture2D(tDiffuse, vUv);
          vec3 color = texel.rgb;
          
          // 应用基本颜色校正
          color = applyBrightness(color, brightness);
          color = applyContrast(color, contrast);
          color = applySaturation(color, saturation);
          color = applyHue(color, hue);
          
          // 应用色温和色调
          color = applyTemperature(color, temperature);
          color = applyTint(color, tint);
          
          // 应用阴影和高光色调
          color = applyShadowHighlightTint(color, shadowTint, highlightTint);
          
          // 应用LUT
          if (useLUT == 1 && lutTexture != null) {
            color = applyLUT(color, lutTexture, lutIntensity);
          }
          
          // 应用伽马校正
          color = applyGamma(color, gamma);
          
          gl_FragColor = vec4(color, texel.a);
        }
      `
    };

    // 创建颜色校正通道
    this.colorCorrectionPass = new ShaderPass(colorCorrectionShader);

    // 设置通道
    this.pass = this.colorCorrectionPass;
  }

  /**
   * 设置亮度
   * @param brightness 亮度
   */
  public setBrightness(brightness: number): void {
    this.brightness = brightness;

    // 更新通道的亮度
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.brightness) {
      this.colorCorrectionPass.uniforms.brightness.value = brightness;
    }
  }

  /**
   * 获取亮度
   * @returns 亮度
   */
  public getBrightness(): number {
    return this.brightness;
  }

  /**
   * 设置对比度
   * @param contrast 对比度
   */
  public setContrast(contrast: number): void {
    this.contrast = contrast;

    // 更新通道的对比度
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.contrast) {
      this.colorCorrectionPass.uniforms.contrast.value = contrast;
    }
  }

  /**
   * 获取对比度
   * @returns 对比度
   */
  public getContrast(): number {
    return this.contrast;
  }

  /**
   * 设置饱和度
   * @param saturation 饱和度
   */
  public setSaturation(saturation: number): void {
    this.saturation = saturation;

    // 更新通道的饱和度
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.saturation) {
      this.colorCorrectionPass.uniforms.saturation.value = saturation;
    }
  }

  /**
   * 获取饱和度
   * @returns 饱和度
   */
  public getSaturation(): number {
    return this.saturation;
  }

  /**
   * 设置色相偏移
   * @param hue 色相偏移
   */
  public setHue(hue: number): void {
    this.hue = hue;

    // 更新通道的色相偏移
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.hue) {
      this.colorCorrectionPass.uniforms.hue.value = hue;
    }
  }

  /**
   * 获取色相偏移
   * @returns 色相偏移
   */
  public getHue(): number {
    return this.hue;
  }

  /**
   * 设置伽马
   * @param gamma 伽马
   */
  public setGamma(gamma: number): void {
    this.gamma = gamma;

    // 更新通道的伽马
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.gamma) {
      this.colorCorrectionPass.uniforms.gamma.value = gamma;
    }
  }

  /**
   * 获取伽马
   * @returns 伽马
   */
  public getGamma(): number {
    return this.gamma;
  }

  /**
   * 设置色温
   * @param temperature 色温
   */
  public setTemperature(temperature: number): void {
    this.temperature = temperature;

    // 更新通道的色温
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.temperature) {
      this.colorCorrectionPass.uniforms.temperature.value = temperature;
    }
  }

  /**
   * 获取色温
   * @returns 色温
   */
  public getTemperature(): number {
    return this.temperature;
  }

  /**
   * 设置色调
   * @param tint 色调
   */
  public setTint(tint: number): void {
    this.tint = tint;

    // 更新通道的色调
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.tint) {
      this.colorCorrectionPass.uniforms.tint.value = tint;
    }
  }

  /**
   * 获取色调
   * @returns 色调
   */
  public getTint(): number {
    return this.tint;
  }

  /**
   * 设置阴影色调
   * @param color 阴影色调
   */
  public setShadowTint(color: THREE.Color): void {
    this.shadowTint.copy(color);

    // 更新通道的阴影色调
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.shadowTint) {
      this.colorCorrectionPass.uniforms.shadowTint.value = this.shadowTint;
    }
  }

  /**
   * 获取阴影色调
   * @returns 阴影色调
   */
  public getShadowTint(): THREE.Color {
    return this.shadowTint;
  }

  /**
   * 设置高光色调
   * @param color 高光色调
   */
  public setHighlightTint(color: THREE.Color): void {
    this.highlightTint.copy(color);

    // 更新通道的高光色调
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.highlightTint) {
      this.colorCorrectionPass.uniforms.highlightTint.value = this.highlightTint;
    }
  }

  /**
   * 获取高光色调
   * @returns 高光色调
   */
  public getHighlightTint(): THREE.Color {
    return this.highlightTint;
  }

  /**
   * 设置是否使用LUT
   * @param use 是否使用
   */
  public setUseLUT(use: boolean): void {
    this.useLUT = use;

    // 更新通道的LUT使用标志
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.useLUT) {
      this.colorCorrectionPass.uniforms.useLUT.value = use ? 1 : 0;
    }
  }

  /**
   * 获取是否使用LUT
   * @returns 是否使用
   */
  public isUseLUT(): boolean {
    return this.useLUT;
  }

  /**
   * 设置LUT纹理
   * @param texture LUT纹理
   */
  public setLUTTexture(texture: THREE.Texture): void {
    this.lutTexture = texture;

    // 更新通道的LUT纹理
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.lutTexture) {
      this.colorCorrectionPass.uniforms.lutTexture.value = texture;
    }
  }

  /**
   * 获取LUT纹理
   * @returns LUT纹理
   */
  public getLUTTexture(): THREE.Texture | null {
    return this.lutTexture;
  }

  /**
   * 设置LUT强度
   * @param intensity LUT强度
   */
  public setLUTIntensity(intensity: number): void {
    this.lutIntensity = intensity;

    // 更新通道的LUT强度
    if (this.colorCorrectionPass && this.colorCorrectionPass.uniforms.lutIntensity) {
      this.colorCorrectionPass.uniforms.lutIntensity.value = intensity;
    }
  }

  /**
   * 获取LUT强度
   * @returns LUT强度
   */
  public getLUTIntensity(): number {
    return this.lutIntensity;
  }

  /**
   * 更新效果
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 颜色校正效果不需要每帧更新
  }
}
