/**
 * 矩形区域光
 * 物理精确的矩形区域光
 */
import * as THREE from 'three';
import { AreaLight, AreaLightOptions, AreaLightType } from './AreaLight';
/**
 * 矩形区域光选项接口
 */
export interface RectAreaLightOptions extends AreaLightOptions {
    /** 光源类型 */
    type: AreaLightType.RECT;
    /** 宽度 */
    width?: number;
    /** 高度 */
    height?: number;
    /** 是否使用物理单位 */
    usePhysicalUnits?: boolean;
    /** 功率（瓦特） */
    power?: number;
    /** 发光效率（流明/瓦特） */
    efficacy?: number;
}
/**
 * 矩形区域光组件类
 */
export declare class RectAreaLightComponent extends AreaLight {
    /** 宽度 */
    private width;
    /** 高度 */
    private height;
    /** 是否使用物理单位 */
    private usePhysicalUnits;
    /** 功率（瓦特） */
    private power;
    /** 发光效率（流明/瓦特） */
    private efficacy;
    /**
     * 创建矩形区域光组件
     * @param options 矩形区域光选项
     */
    constructor(options: RectAreaLightOptions);
    /**
     * 创建光源
     * @param options 矩形区域光选项
     * @returns Three.js矩形区域光
     */
    protected createLight(options: RectAreaLightOptions): THREE.RectAreaLight;
    /**
     * 创建辅助对象
     * @returns Three.js矩形区域光辅助对象
     */
    protected createHelper(): THREE.Object3D;
    /**
     * 更新辅助对象颜色
     */
    protected updateHelperColor(): void;
    /**
     * 更新物理强度
     */
    private updatePhysicalIntensity;
    /**
     * 设置宽度
     * @param width 宽度
     */
    setWidth(width: number): void;
    /**
     * 获取宽度
     * @returns 宽度
     */
    getWidth(): number;
    /**
     * 设置高度
     * @param height 高度
     */
    setHeight(height: number): void;
    /**
     * 获取高度
     * @returns 高度
     */
    getHeight(): number;
    /**
     * 设置是否使用物理单位
     * @param use 是否使用
     */
    setUsePhysicalUnits(use: boolean): void;
    /**
     * 获取是否使用物理单位
     * @returns 是否使用
     */
    isUsePhysicalUnits(): boolean;
    /**
     * 设置功率
     * @param power 功率（瓦特）
     */
    setPower(power: number): void;
    /**
     * 获取功率
     * @returns 功率（瓦特）
     */
    getPower(): number;
    /**
     * 设置发光效率
     * @param efficacy 发光效率（流明/瓦特）
     */
    setEfficacy(efficacy: number): void;
    /**
     * 获取发光效率
     * @returns 发光效率（流明/瓦特）
     */
    getEfficacy(): number;
    /**
     * 更新辅助对象
     */
    private updateHelper;
    /**
     * 更新组件
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
}
