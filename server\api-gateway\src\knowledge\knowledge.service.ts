import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';

@Injectable()
export class KnowledgeService {
  private readonly knowledgeServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.knowledgeServiceUrl = this.configService.get(
      'KNOWLEDGE_SERVICE_URL',
      'http://localhost:3000',
    );
  }

  /**
   * 创建知识库
   */
  async createKnowledgeBase(data: any): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.post(`${this.knowledgeServiceUrl}/api/knowledge-bases`, data),
    );
    return response.data;
  }

  /**
   * 获取知识库列表
   */
  async getKnowledgeBases(query: any): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.get(`${this.knowledgeServiceUrl}/api/knowledge-bases`, {
        params: query,
      }),
    );
    return response.data;
  }

  /**
   * 获取知识库详情
   */
  async getKnowledgeBase(id: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.get(`${this.knowledgeServiceUrl}/api/knowledge-bases/${id}`),
    );
    return response.data;
  }

  /**
   * 更新知识库
   */
  async updateKnowledgeBase(id: string, data: any): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.put(`${this.knowledgeServiceUrl}/api/knowledge-bases/${id}`, data),
    );
    return response.data;
  }

  /**
   * 删除知识库
   */
  async deleteKnowledgeBase(id: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.delete(`${this.knowledgeServiceUrl}/api/knowledge-bases/${id}`),
    );
    return response.data;
  }

  /**
   * 上传文档到知识库
   */
  async uploadDocument(knowledgeBaseId: string, formData: FormData): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.post(
        `${this.knowledgeServiceUrl}/api/knowledge-bases/${knowledgeBaseId}/documents`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      ),
    );
    return response.data;
  }

  /**
   * 批量上传文档
   */
  async batchUploadDocuments(knowledgeBaseId: string, formData: FormData): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.post(
        `${this.knowledgeServiceUrl}/api/knowledge-bases/${knowledgeBaseId}/documents/batch`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      ),
    );
    return response.data;
  }

  /**
   * 获取知识库文档列表
   */
  async getDocuments(knowledgeBaseId: string, query: any): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.get(
        `${this.knowledgeServiceUrl}/api/knowledge-bases/${knowledgeBaseId}/documents`,
        { params: query },
      ),
    );
    return response.data;
  }

  /**
   * 删除文档
   */
  async deleteDocument(knowledgeBaseId: string, documentId: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.delete(
        `${this.knowledgeServiceUrl}/api/knowledge-bases/${knowledgeBaseId}/documents/${documentId}`,
      ),
    );
    return response.data;
  }

  /**
   * 获取上传进度
   */
  async getUploadProgress(uploadId: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.get(`${this.knowledgeServiceUrl}/api/uploads/${uploadId}/progress`),
    );
    return response.data;
  }

  /**
   * 重新处理文档
   */
  async reprocessDocument(knowledgeBaseId: string, documentId: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.post(
        `${this.knowledgeServiceUrl}/api/knowledge-bases/${knowledgeBaseId}/documents/${documentId}/reprocess`,
      ),
    );
    return response.data;
  }

  /**
   * 获取知识库统计信息
   */
  async getKnowledgeBaseStats(id: string): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.get(`${this.knowledgeServiceUrl}/api/knowledge-bases/${id}/stats`),
    );
    return response.data;
  }

  /**
   * 搜索知识库内容
   */
  async searchKnowledgeBase(id: string, query: any): Promise<any> {
    const response: AxiosResponse = await firstValueFrom(
      this.httpService.post(
        `${this.knowledgeServiceUrl}/api/knowledge-bases/${id}/search`,
        query,
      ),
    );
    return response.data;
  }
}
