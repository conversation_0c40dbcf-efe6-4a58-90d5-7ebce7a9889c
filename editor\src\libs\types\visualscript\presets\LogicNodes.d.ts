/**
 * 视觉脚本逻辑节点
 * 提供逻辑运算相关的节点
 */
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 分支节点
 * 根据条件选择执行路径
 */
export declare class BranchNode extends FlowNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 比较节点
 * 比较两个值
 */
export declare class ComparisonNode extends FunctionNode {
    /** 比较运算符 */
    private operator;
    /**
     * 创建比较节点
     * @param options 节点选项
     */
    constructor(options: any);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 逻辑运算节点
 * 执行逻辑运算
 */
export declare class LogicalOperationNode extends FunctionNode {
    /** 逻辑运算符 */
    private operator;
    /**
     * 创建逻辑运算节点
     * @param options 节点选项
     */
    constructor(options: any);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 开关节点
 * 在两个状态之间切换
 */
export declare class ToggleNode extends FunctionNode {
    /** 当前状态 */
    private state;
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 注册逻辑节点
 * @param registry 节点注册表
 */
export declare function registerLogicNodes(registry: NodeRegistry): void;
