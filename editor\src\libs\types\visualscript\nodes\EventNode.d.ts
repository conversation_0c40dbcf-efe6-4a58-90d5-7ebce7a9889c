/**
 * 视觉脚本事件节点
 * 事件节点是视觉脚本的入口点，用于响应各种事件
 */
import { Node, NodeCategory, NodeOptions, NodeType } from './Node';
/**
 * 事件节点选项
 */
export interface EventNodeOptions extends NodeOptions {
    /** 事件名称 */
    eventName?: string;
}
/**
 * 事件节点基类
 */
export declare class EventNode extends Node {
    /** 节点类型 */
    readonly nodeType: NodeType;
    /** 节点类别 */
    readonly category: NodeCategory;
    /** 事件名称 */
    protected eventName: string;
    /**
     * 创建事件节点
     * @param options 节点选项
     */
    constructor(options: EventNodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 初始化节点
     * 在视觉脚本引擎启动时调用
     */
    initialize(): void;
    /**
     * 当视觉脚本开始执行时调用
     */
    onStart(): void;
    /**
     * 当视觉脚本停止执行时调用
     */
    onStop(): void;
    /**
     * 当视觉脚本更新时调用
     * @param _deltaTime 帧间隔时间（秒）
     */
    onUpdate(_deltaTime: number): void;
    /**
     * 获取事件名称
     * @returns 事件名称
     */
    getEventName(): string;
    /**
     * 设置事件名称
     * @param eventName 事件名称
     */
    setEventName(eventName: string): void;
    /**
     * 触发事件
     * @param args 事件参数
     */
    protected trigger(...args: any[]): void;
}
