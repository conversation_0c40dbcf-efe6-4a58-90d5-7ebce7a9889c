/**
 * 视觉脚本逻辑节点
 * 提供逻辑运算相关的节点
 */
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 比较运算符类型
 */
enum ComparisonOperator {
  EQUAL = 'equal',
  NOT_EQUAL = 'notEqual',
  GREATER = 'greater',
  GREATER_EQUAL = 'greaterEqual',
  LESS = 'less',
  LESS_EQUAL = 'lessEqual'
}

/**
 * 逻辑运算符类型
 */
enum LogicalOperator {
  AND = 'and',
  OR = 'or',
  NOT = 'not'
}

/**
 * 分支节点
 * 根据条件选择执行路径
 */
export class BranchNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加条件输入
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '条件',
      defaultValue: false
    });

    // 添加真值输出流程插槽
    this.addOutput({
      name: 'true',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '条件为真时执行'
    });

    // 添加假值输出流程插槽
    this.addOutput({
      name: 'false',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '条件为假时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取条件值
    const condition = this.getInputValue('condition') as boolean;

    // 根据条件选择执行路径
    if (condition) {
      this.triggerFlow('true');
      return true;
    } else {
      this.triggerFlow('false');
      return false;
    }
  }
}

/**
 * 比较节点
 * 比较两个值
 */
export class ComparisonNode extends FunctionNode {
  /** 比较运算符 */
  private operator: ComparisonOperator;

  /**
   * 创建比较节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置比较运算符
    this.operator = options.metadata?.operator || ComparisonOperator.EQUAL;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个值输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '第一个值',
      defaultValue: 0
    });

    // 添加第二个值输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '第二个值',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '比较结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a');
    const b = this.getInputValue('b');

    // 根据运算符比较值
    let result: boolean;

    switch (this.operator) {
      case ComparisonOperator.EQUAL:
        result = a === b;
        break;
      case ComparisonOperator.NOT_EQUAL:
        result = a !== b;
        break;
      case ComparisonOperator.GREATER:
        result = a > b;
        break;
      case ComparisonOperator.GREATER_EQUAL:
        result = a >= b;
        break;
      case ComparisonOperator.LESS:
        result = a < b;
        break;
      case ComparisonOperator.LESS_EQUAL:
        result = a <= b;
        break;
      default:
        result = false;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 逻辑运算节点
 * 执行逻辑运算
 */
export class LogicalOperationNode extends FunctionNode {
  /** 逻辑运算符 */
  private operator: LogicalOperator;

  /**
   * 创建逻辑运算节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置逻辑运算符
    this.operator = options.metadata?.operator || LogicalOperator.AND;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 根据运算符添加输入
    if (this.operator !== LogicalOperator.NOT) {
      // 添加第一个值输入
      this.addInput({
        name: 'a',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'boolean',
        description: '第一个值',
        defaultValue: false
      });

      // 添加第二个值输入
      this.addInput({
        name: 'b',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'boolean',
        description: '第二个值',
        defaultValue: false
      });
    } else {
      // 添加值输入
      this.addInput({
        name: 'value',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'boolean',
        description: '值',
        defaultValue: false
      });
    }

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '运算结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    let result: boolean;

    // 根据运算符执行逻辑运算
    switch (this.operator) {
      case LogicalOperator.AND:
        const a = this.getInputValue('a') as boolean;
        const b = this.getInputValue('b') as boolean;
        result = a && b;
        break;
      case LogicalOperator.OR:
        const c = this.getInputValue('a') as boolean;
        const d = this.getInputValue('b') as boolean;
        result = c || d;
        break;
      case LogicalOperator.NOT:
        const value = this.getInputValue('value') as boolean;
        result = !value;
        break;
      default:
        result = false;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 开关节点
 * 在两个状态之间切换
 */
export class ToggleNode extends FunctionNode {
  /** 当前状态 */
  private state: boolean = false;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加重置输入
    this.addInput({
      name: 'reset',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '重置状态',
      defaultValue: false
    });

    // 添加初始状态输入
    this.addInput({
      name: 'initialState',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '初始状态',
      defaultValue: false
    });

    // 添加当前状态输出
    this.addOutput({
      name: 'state',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '当前状态'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const reset = this.getInputValue('reset') as boolean;
    const initialState = this.getInputValue('initialState') as boolean;

    // 如果需要重置，则设置为初始状态
    if (reset) {
      this.state = initialState;
    } else {
      // 否则切换状态
      this.state = !this.state;
    }

    // 设置输出值
    this.setOutputValue('state', this.state);

    // 触发输出流程
    this.triggerFlow('flow');

    return this.state;
  }
}

/**
 * 注册逻辑节点
 * @param registry 节点注册表
 */
export function registerLogicNodes(registry: NodeRegistry): void {
  // 注册分支节点
  registry.registerNodeType({
    type: 'logic/flow/branch',
    category: NodeCategory.LOGIC,
    constructor: BranchNode,
    label: '分支',
    description: '根据条件选择执行路径',
    icon: 'branch',
    color: '#FF9800',
    tags: ['logic', 'flow', 'branch']
  });

  // 注册相等比较节点
  registry.registerNodeType({
    type: 'logic/comparison/equal',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '相等',
    description: '比较两个值是否相等',
    icon: 'equal',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'equal'],
    metadata: {
      operator: ComparisonOperator.EQUAL
    }
  });

  // 注册不相等比较节点
  registry.registerNodeType({
    type: 'logic/comparison/notEqual',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '不相等',
    description: '比较两个值是否不相等',
    icon: 'notEqual',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'notEqual'],
    metadata: {
      operator: ComparisonOperator.NOT_EQUAL
    }
  });

  // 注册大于比较节点
  registry.registerNodeType({
    type: 'logic/comparison/greater',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '大于',
    description: '比较第一个值是否大于第二个值',
    icon: 'greater',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'greater'],
    metadata: {
      operator: ComparisonOperator.GREATER
    }
  });

  // 注册大于等于比较节点
  registry.registerNodeType({
    type: 'logic/comparison/greaterEqual',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '大于等于',
    description: '比较第一个值是否大于等于第二个值',
    icon: 'greaterEqual',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'greaterEqual'],
    metadata: {
      operator: ComparisonOperator.GREATER_EQUAL
    }
  });

  // 注册小于比较节点
  registry.registerNodeType({
    type: 'logic/comparison/less',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '小于',
    description: '比较第一个值是否小于第二个值',
    icon: 'less',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'less'],
    metadata: {
      operator: ComparisonOperator.LESS
    }
  });

  // 注册小于等于比较节点
  registry.registerNodeType({
    type: 'logic/comparison/lessEqual',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '小于等于',
    description: '比较第一个值是否小于等于第二个值',
    icon: 'lessEqual',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'lessEqual'],
    metadata: {
      operator: ComparisonOperator.LESS_EQUAL
    }
  });

  // 注册与运算节点
  registry.registerNodeType({
    type: 'logic/operation/and',
    category: NodeCategory.LOGIC,
    constructor: LogicalOperationNode,
    label: '与',
    description: '执行逻辑与运算',
    icon: 'and',
    color: '#FF9800',
    tags: ['logic', 'operation', 'and'],
    metadata: {
      operator: LogicalOperator.AND
    }
  });

  // 注册或运算节点
  registry.registerNodeType({
    type: 'logic/operation/or',
    category: NodeCategory.LOGIC,
    constructor: LogicalOperationNode,
    label: '或',
    description: '执行逻辑或运算',
    icon: 'or',
    color: '#FF9800',
    tags: ['logic', 'operation', 'or'],
    metadata: {
      operator: LogicalOperator.OR
    }
  });

  // 注册非运算节点
  registry.registerNodeType({
    type: 'logic/operation/not',
    category: NodeCategory.LOGIC,
    constructor: LogicalOperationNode,
    label: '非',
    description: '执行逻辑非运算',
    icon: 'not',
    color: '#FF9800',
    tags: ['logic', 'operation', 'not'],
    metadata: {
      operator: LogicalOperator.NOT
    }
  });

  // 注册开关节点
  registry.registerNodeType({
    type: 'logic/flow/toggle',
    category: NodeCategory.LOGIC,
    constructor: ToggleNode,
    label: '开关',
    description: '在两个状态之间切换',
    icon: 'toggle',
    color: '#FF9800',
    tags: ['logic', 'flow', 'toggle']
  });
}
