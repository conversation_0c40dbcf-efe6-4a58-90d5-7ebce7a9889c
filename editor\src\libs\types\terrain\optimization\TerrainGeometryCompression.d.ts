/**
 * 地形几何体压缩
 * 提供地形几何体压缩功能，减少内存使用和提高渲染性能
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';
import type { Entity } from '../../core/Entity';
/**
 * 几何体压缩事件类型
 */
export declare enum GeometryCompressionEventType {
    /** 压缩开始 */
    COMPRESSION_STARTED = "compression_started",
    /** 压缩完成 */
    COMPRESSION_COMPLETED = "compression_completed",
    /** 压缩进度 */
    COMPRESSION_PROGRESS = "compression_progress",
    /** 压缩错误 */
    COMPRESSION_ERROR = "compression_error"
}
/**
 * 几何体压缩配置
 */
export interface GeometryCompressionConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否使用量化 */
    useQuantization?: boolean;
    /** 位置量化精度 */
    positionQuantizationBits?: number;
    /** 法线量化精度 */
    normalQuantizationBits?: number;
    /** UV量化精度 */
    uvQuantizationBits?: number;
    /** 是否使用索引优化 */
    useIndexOptimization?: boolean;
    /** 是否使用顶点缓存优化 */
    useVertexCacheOptimization?: boolean;
    /** 是否使用顶点预取优化 */
    useVertexFetchOptimization?: boolean;
    /** 是否使用网格简化 */
    useMeshSimplification?: boolean;
    /** 简化质量 (0-1) */
    simplificationQuality?: number;
    /** 是否使用GPU加速 */
    useGPUAcceleration?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 压缩结果
 */
export interface CompressionResult {
    /** 原始几何体 */
    originalGeometry: THREE.BufferGeometry;
    /** 压缩后的几何体 */
    compressedGeometry: THREE.BufferGeometry;
    /** 原始内存使用量（字节） */
    originalSize: number;
    /** 压缩后内存使用量（字节） */
    compressedSize: number;
    /** 压缩比率 */
    compressionRatio: number;
    /** 应用的优化 */
    appliedOptimizations: string[];
    /** 处理时间（毫秒） */
    processingTime: number;
}
/**
 * 地形几何体压缩类
 */
export declare class TerrainGeometryCompression {
    /** 是否启用 */
    private enabled;
    /** 是否使用量化 */
    private useQuantization;
    /** 位置量化精度 */
    private positionQuantizationBits;
    /** 法线量化精度 */
    private normalQuantizationBits;
    /** UV量化精度 */
    private uvQuantizationBits;
    /** 是否使用索引优化 */
    private useIndexOptimization;
    /** 是否使用顶点缓存优化 */
    private useVertexCacheOptimization;
    /** 是否使用顶点预取优化 */
    private useVertexFetchOptimization;
    /** 是否使用网格简化 */
    private useMeshSimplification;
    /** 简化质量 */
    private simplificationQuality;
    /** 是否使用GPU加速 */
    private useGPUAcceleration;
    /** 是否启用调试 */
    private debug;
    /** 事件发射器 */
    private eventEmitter;
    /** 工作线程 */
    private worker;
    /** 是否支持工作线程 */
    private supportsWorkers;
    /** 是否支持GPU加速 */
    private supportsGPUAcceleration;
    /**
     * 创建地形几何体压缩
     * @param config 配置
     */
    constructor(config?: GeometryCompressionConfig);
    /**
     * 检查GPU加速支持
     * @returns 是否支持GPU加速
     */
    private checkGPUAccelerationSupport;
    /**
     * 初始化工作线程
     */
    private initWorker;
    /**
     * 处理工作线程消息
     * @param event 消息事件
     */
    private handleWorkerMessage;
    /**
     * 压缩几何体
     * @param geometry 几何体
     * @returns 压缩结果
     */
    compressGeometry(geometry: THREE.BufferGeometry): Promise<CompressionResult>;
    /**
     * 创建虚拟结果
     * @param geometry 几何体
     * @returns 压缩结果
     */
    private createDummyResult;
    /**
     * 计算几何体大小
     * @param geometry 几何体
     * @returns 大小（字节）
     */
    private calculateGeometrySize;
    /**
     * 应用量化
     * @param result 压缩结果
     */
    private applyQuantization;
    /**
     * 量化位置
     * @param geometry 几何体
     * @param bits 量化位数
     */
    private quantizePositions;
    /**
     * 量化法线
     * @param geometry 几何体
     * @param bits 量化位数
     */
    private quantizeNormals;
    /**
     * 量化UV
     * @param geometry 几何体
     * @param bits 量化位数
     */
    private quantizeUVs;
    /**
     * 应用索引优化
     * @param result 压缩结果
     */
    private applyIndexOptimization;
    /**
     * 应用顶点缓存优化
     * @param result 压缩结果
     */
    private applyVertexCacheOptimization;
    /**
     * Tipsify算法实现（顶点缓存优化）
     * @param indices 索引数组
     * @param vertexCount 顶点数量
     * @param cacheSize 缓存大小
     * @returns 优化后的索引数组
     */
    private tipsify;
    /**
     * 应用顶点预取优化
     * @param result 压缩结果
     */
    private applyVertexFetchOptimization;
    /**
     * 应用网格简化
     * @param result 压缩结果
     */
    private applyMeshSimplification;
    /**
     * 添加边
     * @param v1 顶点1
     * @param v2 顶点2
     * @param vertices 顶点数组
     * @param edges 边数组
     * @param edgeMap 边映射
     */
    private addEdge;
    /**
     * 计算三角形的二次误差矩阵
     * @param p1 顶点1
     * @param p2 顶点2
     * @param p3 顶点3
     * @param normal 法线
     * @returns 二次误差矩阵
     */
    private computeTriangleQuadric;
    /**
     * 计算边折叠代价
     * @param v1 顶点1
     * @param v2 顶点2
     * @param vertices 顶点数组
     * @returns 代价和目标位置
     */
    private computeEdgeCollapseCost;
    /**
     * 应用到地形组件
     * @param entity 实体
     * @param component 地形组件
     * @returns 压缩结果
     */
    applyToTerrainComponent(entity: Entity, component: TerrainComponent): Promise<CompressionResult | null>;
    /**
     * 注册事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    on(event: GeometryCompressionEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    off(event: GeometryCompressionEventType, listener: (...args: any[]) => void): void;
}
