import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, Like } from 'typeorm';
import { LogEntity, LogLevel } from './entities/log.entity';

/**
 * 日志存储服务
 * 负责日志的存储和基本查询操作
 */
@Injectable()
export class LogStorageService {
  private readonly logger = new Logger(LogStorageService.name);

  constructor(
    @InjectRepository(LogEntity)
    private readonly logRepository: Repository<LogEntity>,
  ) {}

  /**
   * 存储日志
   */
  async storeLog(logData: {
    level: LogLevel;
    message: string;
    serviceId: string;
    serviceType?: string;
    instanceId?: string;
    hostname?: string;
    context?: string;
    stack?: string;
    timestamp?: Date;
    metadata?: any;
  }): Promise<LogEntity> {
    try {
      const log = this.logRepository.create({
        level: logData.level,
        message: logData.message,
        serviceId: logData.serviceId,
        serviceType: logData.serviceType,
        instanceId: logData.instanceId,
        hostname: logData.hostname,
        context: logData.context,
        stack: logData.stack,
        metadata: logData.metadata,
        timestamp: logData.timestamp || new Date(),
      });

      return await this.logRepository.save(log);
    } catch (error) {
      this.logger.error(`存储日志失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量存储日志
   */
  async storeLogs(logsData: any[]): Promise<LogEntity[]> {
    try {
      const logs = logsData.map(logData =>
        this.logRepository.create({
          level: logData.level || LogLevel.INFO,
          message: logData.message,
          serviceId: logData.serviceId,
          serviceType: logData.serviceType,
          instanceId: logData.instanceId,
          hostname: logData.hostname,
          context: logData.context,
          stack: logData.stack,
          metadata: logData.metadata,
          timestamp: logData.timestamp || new Date(),
        })
      );

      return await this.logRepository.save(logs);
    } catch (error) {
      this.logger.error(`批量存储日志失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 查询日志
   */
  async queryLogs(options: {
    level?: LogLevel;
    serviceId?: string;
    from?: Date;
    to?: Date;
    keyword?: string;
    page?: number;
    limit?: number;
  }): Promise<{ logs: LogEntity[]; total: number }> {
    try {
      const queryBuilder = this.logRepository.createQueryBuilder('log');

      // 添加过滤条件
      if (options.level) {
        queryBuilder.andWhere('log.level = :level', { level: options.level });
      }

      if (options.serviceId) {
        queryBuilder.andWhere('log.serviceId = :serviceId', { serviceId: options.serviceId });
      }

      if (options.from && options.to) {
        queryBuilder.andWhere('log.timestamp BETWEEN :from AND :to', {
          from: options.from,
          to: options.to,
        });
      }

      if (options.keyword) {
        queryBuilder.andWhere('log.message LIKE :keyword', {
          keyword: `%${options.keyword}%`,
        });
      }

      // 分页
      const page = options.page || 1;
      const limit = options.limit || 100;
      const offset = (page - 1) * limit;

      queryBuilder
        .orderBy('log.timestamp', 'DESC')
        .skip(offset)
        .take(limit);

      const [logs, total] = await queryBuilder.getManyAndCount();

      return { logs, total };
    } catch (error) {
      this.logger.error(`查询日志失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取日志统计
   */
  async getLogStats(options: {
    from?: Date;
    to?: Date;
    serviceId?: string;
  }): Promise<any> {
    try {
      const queryBuilder = this.logRepository.createQueryBuilder('log');

      if (options.serviceId) {
        queryBuilder.andWhere('log.serviceId = :serviceId', { serviceId: options.serviceId });
      }

      if (options.from && options.to) {
        queryBuilder.andWhere('log.timestamp BETWEEN :from AND :to', {
          from: options.from,
          to: options.to,
        });
      }

      const stats = await queryBuilder
        .select('log.level', 'level')
        .addSelect('COUNT(*)', 'count')
        .groupBy('log.level')
        .getRawMany();

      return stats;
    } catch (error) {
      this.logger.error(`获取日志统计失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除过期日志
   */
  async deleteExpiredLogs(beforeDate: Date): Promise<number> {
    try {
      const result = await this.logRepository.delete({
        timestamp: Between(new Date(0), beforeDate),
      });

      this.logger.log(`删除了 ${result.affected} 条过期日志`);
      return result.affected || 0;
    } catch (error) {
      this.logger.error(`删除过期日志失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
