{"name": "knowledge-service", "version": "1.0.0", "description": "RAG数字人交互系统 - 知识库管理服务", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "start:dev": "ts-node src/main.ts", "start:debug": "ts-node --inspect src/main.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/elasticsearch": "^10.0.2", "@nestjs/jwt": "^10.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/microservices": "^10.4.20", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/typeorm": "^10.0.0", "@types/passport-jwt": "^4.0.1", "bull": "^4.11.0", "cheerio": "^1.0.0-rc.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.8.1", "crypto": "^1.0.1", "helmet": "^8.1.0", "mammoth": "^1.6.0", "marked": "^5.1.0", "minio": "^7.1.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "passport-jwt": "^4.0.1", "pdf-parse": "^1.1.1", "pg": "^8.11.0", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "typeorm": "^0.3.17", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.0", "@types/multer": "^1.4.7", "@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "eslint": "^8.42.0", "jest": "^29.5.0", "prettier": "^2.8.8", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/*.test.ts", "**/*.spec.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/main.ts"]}}