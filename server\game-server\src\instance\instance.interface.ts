export interface Instance {
  id: string;
  sceneId?: string;
  locationId?: string;
  channelId?: string;
  ipAddress: string;
  port: number;
  podName?: string;
  status: 'creating' | 'ready' | 'allocated' | 'error' | 'closed' | 'closing';
  currentUsers: number;
  maxUsers: number;
  isMediaInstance: boolean;
  createdAt: Date;
  updatedAt: Date;
  assignedAt?: Date;
  ended: boolean;
}

export interface InstanceConfig {
  maxPlayers: number;
  gameMode?: string;
  mapName?: string;
  region?: string;
  version?: string;
  metadata?: Record<string, any>;
}

export interface InstanceStats {
  cpuUsage: number;
  memoryUsage: number;
  networkIn: number;
  networkOut: number;
  playerCount: number;
  uptime: number;
}

export interface LoadBalancingStrategy {
  name: string;
  selectInstance(instances: Instance[], criteria?: any): Instance | null;
}

export interface MigrationRequest {
  sourceInstanceId: string;
  targetInstanceId: string;
  playerIds?: string[];
  preserveState?: boolean;
  timeout?: number;
}
