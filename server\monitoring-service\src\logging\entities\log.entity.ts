import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, Index } from 'typeorm';

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

@Entity('logs')
export class LogEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: LogLevel,
    default: LogLevel.INFO,
  })
  @Index()
  level: LogLevel;

  @Column('text')
  message: string;

  @Column({ nullable: true })
  @Index()
  context: string;

  @Column({ nullable: true })
  @Index()
  serviceId: string;

  @Column({ nullable: true })
  @Index()
  serviceType: string;

  @Column({ nullable: true })
  @Index()
  instanceId: string;

  @Column({ nullable: true })
  @Index()
  hostname: string;

  @Column('text', { nullable: true })
  stack: string;

  @Column('json', { nullable: true })
  metadata: Record<string, any>;

  @Column('timestamp')
  @Index()
  timestamp: Date;

  @CreateDateColumn()
  createdAt: Date;
}
