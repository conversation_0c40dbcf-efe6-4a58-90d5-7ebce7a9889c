/**
 * PerformanceOptimizationExample.ts
 * 
 * 交互系统性能优化示例，展示如何优化大量交互对象的性能
 */

import { World, Entity, Scene, Transform, Camera } from '../../engine/src/core';
import { 
  InteractionSystem, 
  InteractableComponent, 
  InteractionType,
  InteractionEventComponent,
  InteractionEventType,
  InteractionPromptComponent,
  PromptPositionType,
  InteractionHighlightComponent,
  HighlightType
} from '../../engine/src/interaction';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { Debug } from '../../engine/src/utils/Debug';
import { Vector3, BoxGeometry, MeshBasicMaterial, Mesh, Color } from 'three';

/**
 * 性能优化示例类
 */
export class PerformanceOptimizationExample {
  /** 世界 */
  private world: World;

  /** 场景 */
  private scene: Scene;

  /** 输入系统 */
  private inputSystem: InputSystem;

  /** 交互系统 */
  private interactionSystem: InteractionSystem;

  /** 可交互对象列表 */
  private interactableObjects: Entity[] = [];

  /** 性能监控数据 */
  private performanceData = {
    frameTime: 0,
    interactionCheckTime: 0,
    frameCount: 0,
    lastReportTime: 0
  };

  /** 空间分区网格大小 */
  private readonly gridSize = 5;

  /** 空间分区网格 */
  private spatialGrid: Map<string, Entity[]> = new Map();

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 创建输入系统
    this.inputSystem = new InputSystem({
      enableKeyboard: true,
      enableMouse: true,
      enableTouch: true
    });

    // 添加输入系统到世界
    this.world.addSystem(this.inputSystem);

    // 创建交互系统
    this.interactionSystem = new InteractionSystem(this.world, {
      debug: true,
      maxInteractionDistance: 10,
      enableFrustumCheck: true,
      enableHighlight: true,
      enablePrompt: true
    });

    // 添加交互系统到世界
    this.world.addSystem(this.interactionSystem);

    // 创建场景
    this.scene = new Scene(this.world, {
      name: '性能优化示例场景'
    });

    // 初始化场景
    this.initializeScene();

    // 创建大量可交互对象
    this.createManyInteractableObjects(200);

    // 启动游戏循环
    this.startGameLoop();
  }

  /**
   * 初始化场景
   */
  private initializeScene(): void {
    // 创建相机
    const camera = new Camera(this.world, {
      fov: 60,
      aspect: window.innerWidth / window.innerHeight,
      near: 0.1,
      far: 1000,
      position: new Vector3(0, 10, 30),
      lookAt: new Vector3(0, 0, 0)
    });

    // 设置为主相机
    this.world.setMainCamera(camera);

    // 添加相机到场景
    this.scene.addEntity(camera.entity);

    // 创建地面
    const ground = new Entity(this.world);
    ground.name = '地面';
    ground.addComponent(new Transform({
      position: new Vector3(0, -1, 0),
      scale: new Vector3(100, 0.1, 100)
    }));

    // 添加地面到场景
    this.scene.addEntity(ground);
  }

  /**
   * 创建大量可交互对象
   * @param count 对象数量
   */
  private createManyInteractableObjects(count: number): void {
    const size = Math.ceil(Math.sqrt(count));
    const spacing = 5;
    const startX = -(size * spacing) / 2;
    const startZ = -(size * spacing) / 2;

    for (let i = 0; i < count; i++) {
      const x = startX + (i % size) * spacing;
      const z = startZ + Math.floor(i / size) * spacing;
      const y = 0.5;

      this.createInteractableObject(
        new Vector3(x, y, z),
        new Color(Math.random(), Math.random(), Math.random())
      );
    }

    Debug.log('性能优化示例', `创建了 ${count} 个可交互对象`);
  }

  /**
   * 创建单个可交互对象
   * @param position 位置
   * @param color 颜色
   */
  private createInteractableObject(position: Vector3, color: Color): void {
    // 创建实体
    const entity = new Entity(this.world);
    entity.name = `交互对象_${this.interactableObjects.length}`;

    // 添加变换组件
    entity.addComponent(new Transform({
      position,
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(1, 1, 1)
    }));

    // 创建几何体和材质
    const geometry = new BoxGeometry(1, 1, 1);
    const material = new MeshBasicMaterial({ color });
    const mesh = new Mesh(geometry, material);

    // 添加网格到实体（这里简化处理，实际应该使用渲染组件）
    (entity as any).mesh = mesh;

    // 添加可交互组件
    const interactable = new InteractableComponent(entity, {
      interactionType: InteractionType.CLICK,
      label: entity.name,
      prompt: `按E键与${entity.name}交互`,
      interactionDistance: 5,
      onInteract: (e) => {
        Debug.log('性能优化示例', `与${entity.name}交互`);
        // 在这里可以添加交互逻辑
      }
    });
    entity.addComponent(interactable);

    // 添加交互事件组件
    const interactionEvent = new InteractionEventComponent(entity);
    entity.addComponent(interactionEvent);

    // 添加交互提示组件
    const interactionPrompt = new InteractionPromptComponent(entity, {
      text: `按E键与${entity.name}交互`,
      positionType: PromptPositionType.FOLLOW,
      offset: new Vector3(0, 1.5, 0),
      visible: false
    });
    entity.addComponent(interactionPrompt);

    // 添加交互高亮组件
    const interactionHighlight = new InteractionHighlightComponent(entity, {
      highlightType: HighlightType.COLOR,
      highlightColor: color.clone().multiplyScalar(1.5),
      pulse: true,
      highlighted: false
    });
    entity.addComponent(interactionHighlight);

    // 将实体添加到场景
    this.scene.addEntity(entity);

    // 将实体添加到可交互对象列表
    this.interactableObjects.push(entity);

    // 注册组件到交互系统
    this.interactionSystem.registerInteractableComponent(entity, interactable);
    this.interactionSystem.registerInteractionEventComponent(entity, interactionEvent);

    // 添加到空间分区网格
    this.addToSpatialGrid(entity, position);
  }

  /**
   * 添加实体到空间分区网格
   * @param entity 实体
   * @param position 位置
   */
  private addToSpatialGrid(entity: Entity, position: Vector3): void {
    const gridX = Math.floor(position.x / this.gridSize);
    const gridZ = Math.floor(position.z / this.gridSize);
    const gridKey = `${gridX},${gridZ}`;

    if (!this.spatialGrid.has(gridKey)) {
      this.spatialGrid.set(gridKey, []);
    }

    this.spatialGrid.get(gridKey)!.push(entity);
  }

  /**
   * 获取指定位置附近的实体
   * @param position 位置
   * @param radius 半径
   * @returns 附近的实体数组
   */
  private getNearbyEntities(position: Vector3, radius: number): Entity[] {
    const result: Entity[] = [];
    const gridRadius = Math.ceil(radius / this.gridSize);
    const centerGridX = Math.floor(position.x / this.gridSize);
    const centerGridZ = Math.floor(position.z / this.gridSize);

    // 遍历附近的网格
    for (let dx = -gridRadius; dx <= gridRadius; dx++) {
      for (let dz = -gridRadius; dz <= gridRadius; dz++) {
        const gridX = centerGridX + dx;
        const gridZ = centerGridZ + dz;
        const gridKey = `${gridX},${gridZ}`;

        // 如果网格存在，添加其中的实体
        if (this.spatialGrid.has(gridKey)) {
          const entities = this.spatialGrid.get(gridKey)!;
          for (const entity of entities) {
            // 检查实际距离
            const transform = entity.getComponent('Transform');
            if (transform) {
              const entityPosition = transform.getPosition();
              const distance = entityPosition.distanceTo(position);
              if (distance <= radius) {
                result.push(entity);
              }
            }
          }
        }
      }
    }

    return result;
  }

  /**
   * 优化的交互检测
   * @param cameraPosition 相机位置
   * @param maxDistance 最大交互距离
   * @returns 可交互的实体数组
   */
  private optimizedInteractionCheck(cameraPosition: Vector3, maxDistance: number): Entity[] {
    // 使用空间分区获取附近的实体
    const startTime = performance.now();
    const nearbyEntities = this.getNearbyEntities(cameraPosition, maxDistance);
    
    // 过滤出可交互的实体
    const interactableEntities = nearbyEntities.filter(entity => {
      const component = this.interactionSystem.getInteractableComponent(entity);
      return component && component.visible && component.interactive;
    });
    
    // 按距离排序
    interactableEntities.sort((a, b) => {
      const componentA = this.interactionSystem.getInteractableComponent(a);
      const componentB = this.interactionSystem.getInteractableComponent(b);
      
      if (!componentA || !componentB) return 0;
      
      const positionA = componentA.getWorldPosition();
      const positionB = componentB.getWorldPosition();
      
      const distanceA = positionA.distanceTo(cameraPosition);
      const distanceB = positionB.distanceTo(cameraPosition);
      
      return distanceA - distanceB;
    });
    
    const endTime = performance.now();
    this.performanceData.interactionCheckTime = endTime - startTime;
    
    return interactableEntities;
  }

  /**
   * 启动游戏循环
   */
  private startGameLoop(): void {
    // 上一帧时间
    let lastTime = 0;

    // 游戏循环函数
    const gameLoop = (time: number) => {
      const startFrame = performance.now();
      
      // 计算时间增量（秒）
      const deltaTime = (time - lastTime) / 1000;
      lastTime = time;

      // 获取相机位置
      const camera = this.world.getMainCamera();
      if (camera) {
        const cameraPosition = camera.position.clone();
        
        // 使用优化的交互检测
        const interactableEntities = this.optimizedInteractionCheck(cameraPosition, 10);
        
        // 更新最近的交互对象
        if (interactableEntities.length > 0) {
          const closestEntity = interactableEntities[0];
          this.interactionSystem.setClosestInteractable(closestEntity);
        } else {
          this.interactionSystem.setClosestInteractable(undefined);
        }
      }

      // 更新世界
      this.world.update(deltaTime);
      
      // 计算帧时间
      const endFrame = performance.now();
      this.performanceData.frameTime = endFrame - startFrame;
      this.performanceData.frameCount++;
      
      // 每秒报告性能
      if (time - this.performanceData.lastReportTime > 1000) {
        this.reportPerformance();
        this.performanceData.lastReportTime = time;
        this.performanceData.frameCount = 0;
      }

      // 请求下一帧
      requestAnimationFrame(gameLoop);
    };

    // 开始游戏循环
    requestAnimationFrame(gameLoop);
  }
  
  /**
   * 报告性能数据
   */
  private reportPerformance(): void {
    Debug.log('性能优化示例', `帧时间: ${this.performanceData.frameTime.toFixed(2)}ms, 交互检测时间: ${this.performanceData.interactionCheckTime.toFixed(2)}ms, FPS: ${this.performanceData.frameCount}`);
  }
}

// 为InteractionSystem添加获取组件和设置最近交互对象的方法
// 注意：这是为了示例而添加的扩展方法，实际项目中应该直接修改InteractionSystem类
declare module '../../engine/src/interaction/InteractionSystem' {
  interface InteractionSystem {
    getInteractableComponent(entity: Entity): InteractableComponent | undefined;
    setClosestInteractable(entity: Entity | undefined): void;
  }
}

// 添加扩展方法
InteractionSystem.prototype.getInteractableComponent = function(entity: Entity): InteractableComponent | undefined {
  return this['interactableComponents'].get(entity);
};

InteractionSystem.prototype.setClosestInteractable = function(entity: Entity | undefined): void {
  this['closestInteractable'] = entity;
};

// 创建并运行示例
new PerformanceOptimizationExample();
