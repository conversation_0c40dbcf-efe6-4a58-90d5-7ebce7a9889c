/**
 * 键盘输入设备
 */
import { BaseInputDevice } from '../InputDevice';
/**
 * 键盘输入设备
 */
export declare class KeyboardDevice extends BaseInputDevice {
    /** 目标元素 */
    private element;
    /** 是否阻止默认行为 */
    private preventDefault;
    /** 是否阻止事件传播 */
    private stopPropagation;
    /** 键盘事件处理器 */
    private keyboardEventHandlers;
    /**
     * 创建键盘输入设备
     * @param element 目标元素
     * @param preventDefault 是否阻止默认行为
     * @param stopPropagation 是否阻止事件传播
     */
    constructor(element?: HTMLElement, preventDefault?: boolean, stopPropagation?: boolean);
    /**
     * 初始化事件处理器
     */
    private initEventHandlers;
    /**
     * 初始化设备
     */
    initialize(): void;
    /**
     * 销毁设备
     */
    destroy(): void;
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加事件监听器
     */
    private addEventListeners;
    /**
     * 移除事件监听器
     */
    private removeEventListeners;
    /**
     * 处理键盘按键按下事件
     * @param event 键盘事件
     */
    private handleKeyDown;
    /**
     * 处理键盘按键释放事件
     * @param event 键盘事件
     */
    private handleKeyUp;
    /**
     * 检查按键是否按下
     * @param key 按键
     * @returns 是否按下
     */
    isKeyDown(key: string): boolean;
    /**
     * 检查按键是否刚按下
     * @param key 按键
     * @returns 是否刚按下
     */
    isKeyJustDown(key: string): boolean;
    /**
     * 检查按键是否刚释放
     * @param key 按键
     * @returns 是否刚释放
     */
    isKeyJustUp(key: string): boolean;
}
