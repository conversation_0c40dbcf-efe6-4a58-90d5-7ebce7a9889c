/**
 * 水体系统测试类
 */
export declare class WaterSystemTest {
    private static manager;
    private static scene;
    /**
     * 运行所有测试
     */
    static runAllTests(): Promise<void>;
    /**
     * 设置测试环境
     */
    private static setupTestEnvironment;
    /**
     * 测试水体预设
     */
    private static testWaterPresets;
    /**
     * 测试水体生成器
     */
    private static testWaterGenerator;
    /**
     * 测试水体管理器
     */
    private static testWaterManager;
    /**
     * 测试水体场景
     */
    private static testWaterScenes;
    /**
     * 测试配置导入导出
     */
    private static testConfigImportExport;
    /**
     * 性能测试
     */
    static performanceTest(): Promise<void>;
}
