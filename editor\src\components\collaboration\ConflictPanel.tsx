/**
 * 冲突面板组件
 */
import React from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Divider,
  Typography,
  Badge,
  Tag,
  Empty,
  Tabs
} from 'antd';
import {
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  MergeCellsOutlined,
  ClockCircleOutlined,
  RobotOutlined
} from '@ant-design/icons';

import { useDispatch, useSelector } from 'react-redux';
import {
  selectPendingConflicts,
  selectResolvedConflicts,
  selectSelectedConflictId,
  selectSelectedConflict,
  setShowConflictPanel,
  selectConflict as selectConflictAction,
  clearResolvedConflicts
} from '../../store/collaboration/conflictSlice';
import {
  Conflict,
  ConflictStatus,
  ConflictType,
  ConflictResolutionStrategy,
  conflictResolutionService
} from '../../services/ConflictResolutionService';
import ConflictResolutionDialog from './ConflictResolutionDialog';
import AIConflictResolverPanel from './AIConflictResolverPanel';


const { Title, Text } = Typography;

// 格式化时间
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString();
};

// 获取冲突类型名称
const getConflictTypeName = (type: ConflictType): string => {
  switch (type) {
    case ConflictType.ENTITY_CONFLICT:
      return '实体冲突';
    case ConflictType.COMPONENT_CONFLICT:
      return '组件冲突';
    case ConflictType.PROPERTY_CONFLICT:
      return '属性冲突';
    case ConflictType.DELETION_CONFLICT:
      return '删除冲突';
    case ConflictType.SCENE_CONFLICT:
      return '场景冲突';
    default:
      return '未知冲突';
  }
};

// 获取冲突状态标签
const getConflictStatusTag = (status: ConflictStatus): JSX.Element => {
  switch (status) {
    case ConflictStatus.PENDING:
      return <Tag color="warning" icon={<WarningOutlined />}>待解决</Tag>;
    case ConflictStatus.RESOLVED:
      return <Tag color="success" icon={<CheckCircleOutlined />}>已解决</Tag>;
    case ConflictStatus.IGNORED:
      return <Tag color="default" icon={<CloseCircleOutlined />}>已忽略</Tag>;
    default:
      return <Tag>未知状态</Tag>;
  }
};

// 获取冲突解决策略名称
const getResolutionStrategyName = (strategy: ConflictResolutionStrategy): string => {
  switch (strategy) {
    case ConflictResolutionStrategy.ACCEPT_LOCAL:
      return '采用本地版本';
    case ConflictResolutionStrategy.ACCEPT_REMOTE:
      return '采用远程版本';
    case ConflictResolutionStrategy.MERGE:
      return '合并版本';
    case ConflictResolutionStrategy.CUSTOM:
      return '自定义解决';
    default:
      return '未知策略';
  }
};

/**
 * 冲突面板组件
 */
const ConflictPanel: React.FC = () => {
  const dispatch = useDispatch();

  // 从Redux获取状态
  const pendingConflicts = useSelector(selectPendingConflicts);
  const resolvedConflicts = useSelector(selectResolvedConflicts);
  const selectedConflictId = useSelector(selectSelectedConflictId);
  const selectedConflict = useSelector(selectSelectedConflict);

  // 状态
  const [resolutionDialogVisible, setResolutionDialogVisible] = React.useState(false);
  const [showAIResolver, setShowAIResolver] = React.useState(false);

  // 处理关闭面板
  const handleClose = () => {
    dispatch(setShowConflictPanel(false));
  };

  // 处理清除已解决的冲突
  const handleClearResolved = () => {
    dispatch(clearResolvedConflicts());
    conflictResolutionService.clearResolvedConflicts();
  };

  // 处理选择冲突
  const handleSelectConflict = (conflictId: string) => {
    dispatch(selectConflictAction(conflictId));
  };

  // 处理解决冲突
  const handleResolveConflict = () => {
    if (selectedConflict) {
      setResolutionDialogVisible(true);
    }
  };

  // 处理AI辅助解决
  const handleAIResolve = () => {
    setShowAIResolver(!showAIResolver);
  };

  // 渲染冲突列表项
  const renderConflictItem = (conflict: Conflict) => {
    const isSelected = selectedConflictId === conflict.id;

    return (
      <List.Item
        key={conflict.id}
        className={`conflict-item ${isSelected ? 'selected' : ''}`}
        onClick={() => handleSelectConflict(conflict.id)}
      >
        <List.Item.Meta
          avatar={
            <div className="conflict-icon">
              {conflict.status === ConflictStatus.PENDING ? (
                <WarningOutlined style={{ color: '#faad14', fontSize: 20 }} />
              ) : (
                <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 20 }} />
              )}
            </div>
          }
          title={
            <Space>
              <Text strong>{getConflictTypeName(conflict.type)}</Text>
              {getConflictStatusTag(conflict.status)}
            </Space>
          }
          description={
            <div>
              <div>
                <ClockCircleOutlined /> {formatTime(conflict.createdAt)}
              </div>
              {conflict.entityId && (
                <div>
                  <Text type="secondary">实体: {conflict.entityId}</Text>
                </div>
              )}
              {conflict.status === ConflictStatus.RESOLVED && conflict.resolution && (
                <div>
                  <Text type="secondary">解决方式: {getResolutionStrategyName(conflict.resolution)}</Text>
                </div>
              )}
            </div>
          }
        />
      </List.Item>
    );
  };

  return (
    <div className="conflict-panel">
      <Card
        title={
          <Space>
            <WarningOutlined style={{ color: '#faad14' }} />
            <span>冲突解决</span>
            <Badge count={pendingConflicts.length} style={{ backgroundColor: '#faad14' }} />
          </Space>
        }
        extra={
          <Button type="text" icon={<CloseCircleOutlined />} onClick={handleClose} />
        }
        className="conflict-card"
      >
        {(() => {
          const tabItems = [
            {
              key: 'pending',
              label: (
                <span>
                  <WarningOutlined />
                  待解决
                  <Badge count={pendingConflicts.length} style={{ marginLeft: 5 }} />
                </span>
              ),
              children: pendingConflicts.length > 0 ? (
                <List
                  className="conflict-list"
                  dataSource={pendingConflicts}
                  renderItem={renderConflictItem}
                />
              ) : (
                <Empty description="没有待解决的冲突" />
              )
            },
            {
              key: 'resolved',
              label: (
                <span>
                  <CheckCircleOutlined />
                  已解决
                  <Badge count={resolvedConflicts.length} style={{ marginLeft: 5 }} />
                </span>
              ),
              children: resolvedConflicts.length > 0 ? (
                <>
                  <List
                    className="conflict-list"
                    dataSource={resolvedConflicts}
                    renderItem={renderConflictItem}
                  />
                  <div style={{ textAlign: 'center', marginTop: 16 }}>
                    <Button
                      icon={<DeleteOutlined />}
                      onClick={handleClearResolved}
                    >
                      清除已解决的冲突
                    </Button>
                  </div>
                </>
              ) : (
                <Empty description="没有已解决的冲突" />
              )
            }
          ];

          return (
            <Tabs
              defaultActiveKey="pending"
              items={tabItems}
            />
          );
        })()}


        <Divider />

        {selectedConflict && (
          <div className="conflict-details">
            <Title level={5}>冲突详情</Title>

            <div className="conflict-info">
              <div>
                <Text strong>类型:</Text> {getConflictTypeName(selectedConflict.type)}
              </div>
              <div>
                <Text strong>状态:</Text> {getConflictStatusTag(selectedConflict.status)}
              </div>
              <div>
                <Text strong>创建时间:</Text> {formatTime(selectedConflict.createdAt)}
              </div>
              {selectedConflict.resolvedAt && (
                <div>
                  <Text strong>解决时间:</Text> {formatTime(selectedConflict.resolvedAt)}
                </div>
              )}
              {selectedConflict.entityId && (
                <div>
                  <Text strong>实体ID:</Text> {selectedConflict.entityId}
                </div>
              )}
              {selectedConflict.componentId && (
                <div>
                  <Text strong>组件ID:</Text> {selectedConflict.componentId}
                </div>
              )}
              {selectedConflict.propertyPath && (
                <div>
                  <Text strong>属性路径:</Text> {selectedConflict.propertyPath.join('.')}
                </div>
              )}
              {selectedConflict.resolution && (
                <div>
                  <Text strong>解决策略:</Text> {getResolutionStrategyName(selectedConflict.resolution)}
                </div>
              )}
            </div>

            {selectedConflict.status === ConflictStatus.PENDING && (
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Space>
                  <Button
                    type="primary"
                    icon={<MergeCellsOutlined />}
                    onClick={handleResolveConflict}
                  >
                    手动解决冲突
                  </Button>
                  <Button
                    type={showAIResolver ? 'primary' : 'default'}
                    icon={<RobotOutlined />}
                    onClick={handleAIResolve}
                  >
                    AI辅助解决
                  </Button>
                </Space>
              </div>
            )}
          </div>
        )}
      </Card>

      {selectedConflict && (
        <ConflictResolutionDialog
          visible={resolutionDialogVisible}
          conflict={selectedConflict}
          onClose={() => setResolutionDialogVisible(false)}
        />
      )}

      {showAIResolver && selectedConflict && (
        <div style={{ marginTop: 16 }}>
          <AIConflictResolverPanel conflictId={selectedConflict.id} />
        </div>
      )}
    </div>
  );
};

export default ConflictPanel;
