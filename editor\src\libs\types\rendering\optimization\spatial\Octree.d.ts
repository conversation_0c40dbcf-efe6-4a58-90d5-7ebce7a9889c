/**
 * 八叉树
 * 用于高效组织场景中的物体
 */
import * as THREE from 'three';
import type { Entity } from '../../../core/Entity';
/**
 * 八叉树配置接口
 */
export interface OctreeOptions {
    /** 最大深度 */
    maxDepth?: number;
    /** 每个节点的最大对象数 */
    maxObjectsPerNode?: number;
    /** 最小节点大小 */
    minNodeSize?: number;
    /** 中心点 */
    center?: THREE.Vector3;
    /** 大小 */
    size?: number;
}
/**
 * 八叉树类
 */
export declare class Octree {
    /** 根节点 */
    private root;
    /** 最大深度 */
    private maxDepth;
    /** 每个节点的最大对象数 */
    private maxObjectsPerNode;
    /** 最小节点大小 */
    private minNodeSize;
    /** 实体到节点的映射 */
    private entityToNode;
    /** 临时向量 */
    private tempVector;
    /**
     * 创建八叉树
     * @param options 八叉树配置
     */
    constructor(options?: OctreeOptions);
    /**
     * 插入实体
     * @param entity 实体
     * @param position 位置
     * @param radius 半径
     */
    insert(entity: Entity, position: THREE.Vector3, radius?: number): void;
    /**
     * 将实体插入节点
     * @param node 节点
     * @param entity 实体
     * @param position 位置
     * @param radius 半径
     * @returns 是否插入成功
     */
    private insertIntoNode;
    /**
     * 移除实体
     * @param entity 实体
     */
    remove(entity: Entity): void;
    /**
     * 更新实体
     * @param entity 实体
     * @param position 位置
     * @param radius 半径
     */
    update(entity: Entity, position: THREE.Vector3, radius?: number): void;
    /**
     * 查询包含点的实体
     * @param point 点
     * @returns 实体列表
     */
    queryPoint(point: THREE.Vector3): Entity[];
    /**
     * 在节点中查询包含点的实体
     * @param node 节点
     * @param point 点
     * @param result 结果列表
     */
    private queryPointInNode;
    /**
     * 查询与包围球相交的实体
     * @param center 包围球中心
     * @param radius 包围球半径
     * @returns 实体列表
     */
    querySphere(center: THREE.Vector3, radius: number): Entity[];
    /**
     * 在节点中查询与包围球相交的实体
     * @param node 节点
     * @param center 包围球中心
     * @param radius 包围球半径
     * @param result 结果列表
     */
    private querySphereInNode;
    /**
     * 查询与视锥体相交的实体
     * @param frustum 视锥体
     * @returns 实体列表
     */
    queryFrustum(frustum: THREE.Frustum): Entity[];
    /**
     * 在节点中查询与视锥体相交的实体
     * @param node 节点
     * @param frustum 视锥体
     * @param result 结果列表
     */
    private queryFrustumInNode;
    /**
     * 清空八叉树
     */
    clear(): void;
    /**
     * 获取实体数量
     * @returns 实体数量
     */
    getEntityCount(): number;
    /**
     * 获取节点数量
     * @returns 节点数量
     */
    getNodeCount(): number;
    /**
     * 计算节点数量
     * @param node 节点
     * @param count 计数
     */
    private countNodes;
}
