/**
 * 情感模型工厂
 * 用于创建和管理不同类型的情感分析模型
 */
import { BERTEmotionModel } from './BERTEmotionModel';
import { ChineseBERTEmotionModel } from './ChineseBERTEmotionModel';
import { MultilingualEmotionModel } from './MultilingualEmotionModel';
import { EventEmitter } from '../../utils/EventEmitter';
/**
 * 情感模型类型
 */
export declare enum EmotionModelType {
    BERT = "bert",
    ROBERTA = "roberta",
    DISTILBERT = "distilbert",
    CHINESE_BERT = "chinese-bert",
    MULTILINGUAL = "multilingual"
}
/**
 * 情感模型变体
 */
export declare enum EmotionModelVariant {
    BASE = "base",
    LARGE = "large",
    DISTILLED = "distilled",
    MULTILINGUAL = "multilingual"
}
/**
 * 情感模型工厂配置
 */
export interface EmotionModelFactoryConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存大小 */
    cacheSize?: number;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 是否使用远程API */
    useRemoteAPI?: boolean;
    /** 远程API URL */
    remoteAPIUrl?: string;
    /** API密钥 */
    apiKey?: string;
    /** 模型路径前缀 */
    modelPathPrefix?: string;
}
/**
 * 情感模型工厂
 */
export declare class EmotionModelFactory extends EventEmitter {
    /** 配置 */
    private config;
    /** 模型缓存 */
    private modelCache;
    /** 是否启用调试 */
    private debug;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: EmotionModelFactoryConfig);
    /**
     * 创建情感模型
     * @param type 模型类型
     * @param variant 模型变体
     * @param config 模型配置
     * @returns 情感模型
     */
    createModel(type: EmotionModelType, variant?: EmotionModelVariant, config?: any): any;
    /**
     * 创建中文BERT情感模型
     * @param variant 模型变体
     * @param config 模型配置
     * @returns 中文BERT情感模型
     */
    createChineseBERTModel(variant?: EmotionModelVariant, config?: any): ChineseBERTEmotionModel;
    /**
     * 创建多语言情感模型
     * @param config 模型配置
     * @returns 多语言情感模型
     */
    createMultilingualModel(config?: any): MultilingualEmotionModel;
    /**
     * 创建多语言BERT情感模型（旧版本，保留兼容性）
     * @param config 模型配置
     * @returns 多语言BERT情感模型
     * @deprecated 使用 createMultilingualModel 代替
     */
    createMultilingualBERTModel(config?: any): BERTEmotionModel;
    /**
     * 清除缓存
     */
    clearCache(): void;
    /**
     * 获取缓存大小
     * @returns 缓存大小
     */
    getCacheSize(): number;
}
