/**
 * 媒体流类型定义
 */
export declare enum MediaStreamType {
    /** 音频流 */
    AUDIO = "audio",
    /** 视频流 */
    VIDEO = "video",
    /** 音视频流 */
    AUDIO_VIDEO = "audioVideo",
    /** 屏幕共享 */
    SCREEN_SHARE = "screenShare",
    /** 摄像头 */
    CAMERA = "camera",
    /** 麦克风 */
    MICROPHONE = "microphone"
}
export interface MediaStreamConfig {
    /** 流类型 */
    type: MediaStreamType;
    /** 是否启用音频 */
    audio?: boolean;
    /** 是否启用视频 */
    video?: boolean;
    /** 音频约束 */
    audioConstraints?: MediaTrackConstraints;
    /** 视频约束 */
    videoConstraints?: MediaTrackConstraints;
}
export declare class MediaStreamManager {
    private streams;
    createStream(config: MediaStreamConfig): Promise<MediaStream | null>;
    addStream(id: string, stream: MediaStream): void;
    getStream(id: string): MediaStream | undefined;
    removeStream(id: string): void;
    stopAllStreams(): void;
}
