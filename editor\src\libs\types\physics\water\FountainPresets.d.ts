/**
 * 喷泉预设
 * 提供各种类型的喷泉预设配置
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
/**
 * 喷泉预设类型
 */
export declare enum FountainPresetType {
    /** 标准喷泉 */
    STANDARD = "standard",
    /** 高喷泉 */
    HIGH = "high",
    /** 宽喷泉 */
    WIDE = "wide",
    /** 多喷头喷泉 */
    MULTI_JET = "multi_jet",
    /** 舞蹈喷泉 */
    DANCING = "dancing",
    /** 音乐喷泉 */
    MUSICAL = "musical",
    /** 脉冲喷泉 */
    PULSE = "pulse",
    /** 交替喷泉 */
    ALTERNATING = "alternating",
    /** 序列喷泉 */
    SEQUENCE = "sequence",
    /** 随机喷泉 */
    RANDOM = "random"
}
/**
 * 喷泉预设配置
 */
export interface FountainPresetConfig {
    /** 预设类型 */
    type: FountainPresetType;
    /** 喷泉宽度 */
    width?: number;
    /** 喷泉高度 */
    height?: number;
    /** 喷泉深度 */
    depth?: number;
    /** 喷泉位置 */
    position?: THREE.Vector3;
    /** 喷泉旋转 */
    rotation?: THREE.Euler;
    /** 喷泉颜色 */
    color?: THREE.Color;
    /** 喷泉不透明度 */
    opacity?: number;
    /** 喷泉流速 */
    flowSpeed?: number;
    /** 喷泉湍流强度 */
    turbulenceStrength?: number;
    /** 喷泉湍流频率 */
    turbulenceFrequency?: number;
    /** 喷泉湍流速度 */
    turbulenceSpeed?: number;
    /** 是否启用水雾效果 */
    enableMistEffect?: boolean;
    /** 水雾效果强度 */
    mistEffectStrength?: number;
    /** 是否启用水花效果 */
    enableSplashEffect?: boolean;
    /** 水花效果强度 */
    splashEffectStrength?: number;
    /** 是否启用水滴效果 */
    enableDropletEffect?: boolean;
    /** 水滴效果强度 */
    dropletEffectStrength?: number;
    /** 是否启用声音效果 */
    enableSoundEffect?: boolean;
    /** 声音效果音量 */
    soundEffectVolume?: number;
    /** 是否启用水流动力学 */
    enableFluidDynamics?: boolean;
    /** 喷泉喷射高度 */
    jetHeight?: number;
    /** 喷泉喷射角度 */
    jetAngle?: number;
    /** 喷泉喷射数量 */
    jetCount?: number;
    /** 喷泉喷射间隔 */
    jetInterval?: number;
    /** 喷泉喷射持续时间 */
    jetDuration?: number;
    /** 喷泉喷射延迟 */
    jetDelay?: number;
    /** 喷泉喷射随机性 */
    jetRandomness?: number;
}
/**
 * 喷泉预设
 */
export declare class FountainPresets {
    /**
     * 创建喷泉预设
     * @param world 世界
     * @param config 预设配置
     * @returns 喷泉实体
     */
    static createPreset(world: World, config: FountainPresetConfig): Entity;
    /**
     * 应用标准喷泉预设
     * @param config 喷泉配置
     */
    private static applyStandardPreset;
    /**
     * 应用高喷泉预设
     * @param config 喷泉配置
     */
    private static applyHighPreset;
    /**
     * 应用宽喷泉预设
     * @param config 喷泉配置
     */
    private static applyWidePreset;
    /**
     * 应用多喷头喷泉预设
     * @param config 喷泉配置
     */
    private static applyMultiJetPreset;
    /**
     * 应用舞蹈喷泉预设
     * @param config 喷泉配置
     */
    private static applyDancingPreset;
    /**
     * 应用音乐喷泉预设
     * @param config 喷泉配置
     */
    private static applyMusicalPreset;
    /**
     * 应用脉冲喷泉预设
     * @param config 喷泉配置
     */
    private static applyPulsePreset;
    /**
     * 应用交替喷泉预设
     * @param config 喷泉配置
     */
    private static applyAlternatingPreset;
    /**
     * 应用序列喷泉预设
     * @param config 喷泉配置
     */
    private static applySequencePreset;
    /**
     * 应用随机喷泉预设
     * @param config 喷泉配置
     */
    private static applyRandomPreset;
}
