/**
 * 事件系统兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 事件系统兼容性测试
 */
export const eventSystemTest: TestCase = {
  name: '事件系统兼容性测试',
  description: '测试事件系统功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目事件系统实例
      const originalEventSystem = new original.EventEmitter();
      
      // 创建重构后项目事件系统实例
      const refactoredEventSystem = new refactored.EventEmitter();
      
      // 检查事件系统实例是否创建成功
      if (!originalEventSystem || !refactoredEventSystem) {
        return {
          name: '事件系统兼容性测试',
          passed: false,
          errorMessage: '事件系统实例创建失败'
        };
      }
      
      // 测试事件监听和触发
      let originalEventTriggered = false;
      let refactoredEventTriggered = false;
      
      // 添加事件监听器
      originalEventSystem.on('testEvent', (data: any) => {
        originalEventTriggered = true;
        if (data.value !== 42) {
          throw new Error(`原有项目事件数据不正确: ${data.value}`);
        }
      });
      
      refactoredEventSystem.on('testEvent', (data: any) => {
        refactoredEventTriggered = true;
        if (data.value !== 42) {
          throw new Error(`重构后项目事件数据不正确: ${data.value}`);
        }
      });
      
      // 触发事件
      originalEventSystem.emit('testEvent', { value: 42 });
      refactoredEventSystem.emit('testEvent', { value: 42 });
      
      // 检查事件是否触发
      if (originalEventTriggered !== refactoredEventTriggered) {
        return {
          name: '事件系统兼容性测试',
          passed: false,
          errorMessage: `事件触发状态不一致: 原有项目=${originalEventTriggered}, 重构后项目=${refactoredEventTriggered}`,
          details: {
            originalEventTriggered,
            refactoredEventTriggered
          }
        };
      }
      
      // 测试事件移除
      originalEventSystem.off('testEvent');
      refactoredEventSystem.off('testEvent');
      
      // 重置事件触发状态
      originalEventTriggered = false;
      refactoredEventTriggered = false;
      
      // 再次触发事件
      originalEventSystem.emit('testEvent', { value: 42 });
      refactoredEventSystem.emit('testEvent', { value: 42 });
      
      // 检查事件是否触发
      if (originalEventTriggered !== refactoredEventTriggered) {
        return {
          name: '事件系统兼容性测试',
          passed: false,
          errorMessage: `移除事件后触发状态不一致: 原有项目=${originalEventTriggered}, 重构后项目=${refactoredEventTriggered}`,
          details: {
            originalEventTriggered,
            refactoredEventTriggered
          }
        };
      }
      
      // 测试一次性事件
      originalEventTriggered = false;
      refactoredEventTriggered = false;
      
      // 添加一次性事件监听器
      originalEventSystem.once('testOnceEvent', () => {
        originalEventTriggered = true;
      });
      
      refactoredEventSystem.once('testOnceEvent', () => {
        refactoredEventTriggered = true;
      });
      
      // 触发事件
      originalEventSystem.emit('testOnceEvent');
      refactoredEventSystem.emit('testOnceEvent');
      
      // 检查事件是否触发
      if (originalEventTriggered !== refactoredEventTriggered) {
        return {
          name: '事件系统兼容性测试',
          passed: false,
          errorMessage: `一次性事件触发状态不一致: 原有项目=${originalEventTriggered}, 重构后项目=${refactoredEventTriggered}`,
          details: {
            originalEventTriggered,
            refactoredEventTriggered
          }
        };
      }
      
      // 重置事件触发状态
      originalEventTriggered = false;
      refactoredEventTriggered = false;
      
      // 再次触发事件
      originalEventSystem.emit('testOnceEvent');
      refactoredEventSystem.emit('testOnceEvent');
      
      // 检查事件是否触发
      if (originalEventTriggered !== refactoredEventTriggered) {
        return {
          name: '事件系统兼容性测试',
          passed: false,
          errorMessage: `一次性事件再次触发状态不一致: 原有项目=${originalEventTriggered}, 重构后项目=${refactoredEventTriggered}`,
          details: {
            originalEventTriggered,
            refactoredEventTriggered
          }
        };
      }
      
      return {
        name: '事件系统兼容性测试',
        passed: true,
        details: {
          originalEventTriggered,
          refactoredEventTriggered
        }
      };
    } catch (error) {
      return {
        name: '事件系统兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
