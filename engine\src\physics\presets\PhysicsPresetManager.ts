/**
 * 物理预设管理器
 * 用于管理物理预设，包括加载、保存、应用预设等功能
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { BodyType  } from '../PhysicsBody';
import { ColliderType } from '../PhysicsCollider';
import { PhysicsBodyComponent   } from '../components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from '../components/PhysicsColliderComponent';
import { PhysicsWorldComponent   } from '../components/PhysicsWorldComponent';
import { PhysicsMaterialFactory } from '../PhysicsMaterialFactory';
import { PhysicsPreset,
  PhysicsBodyPreset,
  PhysicsColliderPreset,
  PhysicsWorldPreset
  } from './PhysicsPreset';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 物理预设管理器
 */
export class PhysicsPresetManager extends EventEmitter {
  /** 预设映射 */
  private presets: Map<string, PhysicsPreset> = new Map();

  /** 预设类别 */
  private categories: Set<string> = new Set();

  /** 单例实例 */
  private static instance: PhysicsPresetManager;

  /**
   * 获取单例实例
   * @returns 物理预设管理器实例
   */
  public static getInstance(): PhysicsPresetManager {
    if (!PhysicsPresetManager.instance) {
      PhysicsPresetManager.instance = new PhysicsPresetManager();
    }
    return PhysicsPresetManager.instance;
  }

  /**
   * 创建物理预设管理器
   */
  private constructor() {
    super();
    this.initializeDefaultPresets();
  }

  /**
   * 初始化默认预设
   */
  private initializeDefaultPresets(): void {
    // 添加默认物理世界预设
    this.addPreset({
      name: 'default_world',
      description: '默认物理世界预设',
      category: '世界',
      worldPreset: {
        gravity: new THREE.Vector3(0, -9.82, 0),
        allowSleep: true,
        iterations: 10,
        broadphase: 'naive',
        defaultFriction: 0.3,
        defaultRestitution: 0.3
      }
    });

    // 添加常见物理体预设
    this.addPreset({
      name: 'dynamic_box',
      description: '动态盒子预设',
      category: '物理体',
      bodyPreset: {
        type: BodyType.DYNAMIC,
        mass: 1,
        linearDamping: 0.1,
        angularDamping: 0.1,
        allowSleep: true,
        fixedRotation: false,
        materialName: 'default',
        enableCCD: false,
        collisionFilterGroup: 1,
        collisionFilterMask: 1
      },
      colliderPreset: {
        type: ColliderType.BOX,
        isTrigger: false,
        params: {
          halfExtents: { x: 0.5, y: 0.5, z: 0.5 }
        },
        materialName: 'default'
      }
    });

    this.addPreset({
      name: 'static_ground',
      description: '静态地面预设',
      category: '物理体',
      bodyPreset: {
        type: BodyType.STATIC,
        mass: 0,
        linearDamping: 0.1,
        angularDamping: 0.1,
        allowSleep: true,
        fixedRotation: true,
        materialName: 'default',
        enableCCD: false,
        collisionFilterGroup: 1,
        collisionFilterMask: 1
      },
      colliderPreset: {
        type: ColliderType.BOX,
        isTrigger: false,
        params: {
          halfExtents: { x: 5, y: 0.5, z: 5 }
        },
        materialName: 'default'
      }
    });

    // 添加常见材质预设
    this.addPreset({
      name: 'material_presets',
      description: '常见材质预设',
      category: '材质',
      materialPresets: [
        {
          name: 'metal',
          friction: 0.3,
          restitution: 0.3,
          contactEquationStiffness: 1e7,
          contactEquationRelaxation: 3
        },
        {
          name: 'wood',
          friction: 0.4,
          restitution: 0.2,
          contactEquationStiffness: 1e6,
          contactEquationRelaxation: 6
        },
        {
          name: 'rubber',
          friction: 0.7,
          restitution: 0.1,
          contactEquationStiffness: 1e5,
          contactEquationRelaxation: 8
        },
        {
          name: 'ice',
          friction: 0.05,
          restitution: 0.9,
          contactEquationStiffness: 1e7,
          contactEquationRelaxation: 3
        }
      ]
    });

    // 添加车辆预设
    this.addPreset({
      name: 'vehicle',
      description: '简单车辆预设',
      category: '车辆',
      bodyPreset: {
        type: BodyType.DYNAMIC,
        mass: 800,
        linearDamping: 0.1,
        angularDamping: 0.1,
        allowSleep: false,
        fixedRotation: false,
        materialName: 'metal',
        enableCCD: false,
        collisionFilterGroup: 1,
        collisionFilterMask: 1
      },
      customData: {
        wheelRadius: 0.4,
        wheelMass: 30,
        suspensionStiffness: 30,
        suspensionRestLength: 0.3,
        suspensionDamping: 4.4,
        suspensionCompression: 2.3,
        rollInfluence: 0.01,
        maxSuspensionTravel: 0.3,
        maxSuspensionForce: 100000,
        frictionSlip: 30
      }
    });
  }

  /**
   * 添加预设
   * @param preset 物理预设
   */
  public addPreset(preset: PhysicsPreset): void {
    if (this.presets.has(preset.name)) {
      Debug.warn(`预设 "${preset.name}" 已存在，将被覆盖`);
    }

    this.presets.set(preset.name, preset);
    this.categories.add(preset.category);

    // 发出预设添加事件
    this.emit('presetAdded', preset);
  }

  /**
   * 获取预设
   * @param name 预设名称
   * @returns 物理预设
   */
  public getPreset(name: string): PhysicsPreset | undefined {
    return this.presets.get(name);
  }

  /**
   * 获取所有预设
   * @returns 所有物理预设
   */
  public getAllPresets(): PhysicsPreset[] {
    return Array.from(this.presets.values());
  }

  /**
   * 获取指定类别的预设
   * @param category 预设类别
   * @returns 指定类别的物理预设
   */
  public getPresetsByCategory(category: string): PhysicsPreset[] {
    return this.getAllPresets().filter(preset => preset.category === category);
  }

  /**
   * 获取所有预设类别
   * @returns 所有预设类别
   */
  public getAllCategories(): string[] {
    return Array.from(this.categories);
  }

  /**
   * 删除预设
   * @param name 预设名称
   * @returns 是否删除成功
   */
  public removePreset(name: string): boolean {
    const preset = this.presets.get(name);
    if (!preset) {
      return false;
    }

    const result = this.presets.delete(name);

    // 更新类别
    this.updateCategories();

    // 发出预设删除事件
    if (result) {
      this.emit('presetRemoved', preset);
    }

    return result;
  }

  /**
   * 更新类别
   */
  private updateCategories(): void {
    this.categories.clear();
    for (const preset of this.presets.values()) {
      this.categories.add(preset.category);
    }
  }

  /**
   * 应用物理体预设到实体
   * @param entity 实体
   * @param presetName 预设名称
   * @returns 是否应用成功
   */
  public applyBodyPreset(entity: Entity, presetName: string): boolean {
    const preset = this.presets.get(presetName);
    if (!preset || !preset.bodyPreset) {
      Debug.warn(`无法应用预设 "${presetName}": 预设不存在或不包含物理体预设`);
      return false;
    }

    const bodyPreset = preset.bodyPreset;

    // 获取或创建物理体组件
    let bodyComponent = entity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.type);
    if (!bodyComponent) {
      bodyComponent = new PhysicsBodyComponent({
        type: bodyPreset.type,
        mass: bodyPreset.mass,
        linearDamping: bodyPreset.linearDamping,
        angularDamping: bodyPreset.angularDamping,
        allowSleep: bodyPreset.allowSleep,
        fixedRotation: bodyPreset.fixedRotation,
        collisionFilterGroup: bodyPreset.collisionFilterGroup,
        collisionFilterMask: bodyPreset.collisionFilterMask
      });
      entity.addComponent(bodyComponent);
    } else {
      // 更新现有组件的属性
      bodyComponent.bodyType = bodyPreset.type;
      bodyComponent.mass = bodyPreset.mass;
      bodyComponent.linearDamping = bodyPreset.linearDamping;
      bodyComponent.angularDamping = bodyPreset.angularDamping;
      bodyComponent.allowSleep = bodyPreset.allowSleep;
      bodyComponent.fixedRotation = bodyPreset.fixedRotation;
      bodyComponent.collisionFilterGroup = bodyPreset.collisionFilterGroup;
      bodyComponent.collisionFilterMask = bodyPreset.collisionFilterMask;
    }

    // 设置材质
    if (bodyPreset.materialName) {
      const material = PhysicsMaterialFactory.getMaterial(bodyPreset.materialName);
      bodyComponent.material = material;
    }

    return true;
  }

  /**
   * 应用碰撞器预设到实体
   * @param entity 实体
   * @param presetName 预设名称
   * @returns 是否应用成功
   */
  public applyColliderPreset(entity: Entity, presetName: string): boolean {
    const preset = this.presets.get(presetName);
    if (!preset || !preset.colliderPreset) {
      Debug.warn(`无法应用预设 "${presetName}": 预设不存在或不包含碰撞器预设`);
      return false;
    }

    const colliderPreset = preset.colliderPreset;

    // 获取或创建碰撞器组件
    let colliderComponent = entity.getComponent<PhysicsColliderComponent>(PhysicsColliderComponent.type);
    if (!colliderComponent) {
      colliderComponent = new PhysicsColliderComponent({
        type: colliderPreset.type,
        params: colliderPreset.params,
        offset: colliderPreset.position,
        orientation: colliderPreset.quaternion,
        isTrigger: colliderPreset.isTrigger
      });
      entity.addComponent(colliderComponent);
    } else {
      // 对于现有组件，我们需要重新创建，因为组件不支持动态更新类型和参数
      entity.removeComponent(colliderComponent);
      colliderComponent = new PhysicsColliderComponent({
        type: colliderPreset.type,
        params: colliderPreset.params,
        offset: colliderPreset.position,
        orientation: colliderPreset.quaternion,
        isTrigger: colliderPreset.isTrigger
      });
      entity.addComponent(colliderComponent);
    }

    return true;
  }

  /**
   * 应用物理世界预设到实体
   * @param entity 实体
   * @param presetName 预设名称
   * @returns 是否应用成功
   */
  public applyWorldPreset(entity: Entity, presetName: string): boolean {
    const preset = this.presets.get(presetName);
    if (!preset || !preset.worldPreset) {
      Debug.warn(`无法应用预设 "${presetName}": 预设不存在或不包含物理世界预设`);
      return false;
    }

    const worldPreset = preset.worldPreset;

    // 获取或创建物理世界组件
    let worldComponent = entity.getComponent<PhysicsWorldComponent>(PhysicsWorldComponent.type);
    if (!worldComponent) {
      worldComponent = new PhysicsWorldComponent({
        gravity: worldPreset.gravity,
        allowSleep: worldPreset.allowSleep,
        iterations: worldPreset.iterations,
        broadphase: worldPreset.broadphase,
        gridBroadphaseSize: worldPreset.gridBroadphaseSize,
        defaultFriction: worldPreset.defaultFriction,
        defaultRestitution: worldPreset.defaultRestitution
      });
      entity.addComponent(worldComponent);
    } else {
      // 更新现有组件
      worldComponent.setGravity(worldPreset.gravity);
      worldComponent.setAllowSleep(worldPreset.allowSleep);
      worldComponent.setIterations(worldPreset.iterations);
      worldComponent.setBroadphaseAlgorithm(worldPreset.broadphase, worldPreset.gridBroadphaseSize);
    }

    return true;
  }

  /**
   * 从实体创建物理体预设
   * @param entity 实体
   * @param name 预设名称
   * @param description 预设描述
   * @param category 预设类别
   * @returns 创建的预设
   */
  public createBodyPresetFromEntity(
    entity: Entity,
    name: string,
    description: string = '',
    category: string = '自定义'
  ): PhysicsPreset | null {
    const bodyComponent = entity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.type);
    if (!bodyComponent) {
      Debug.warn(`实体没有物理体组件，无法创建预设`);
      return null;
    }

    const colliderComponent = entity.getComponent<PhysicsColliderComponent>(PhysicsColliderComponent.type);

    // 创建物理体预设
    const bodyPreset: PhysicsBodyPreset = {
      type: bodyComponent.bodyType,
      mass: bodyComponent.mass,
      linearDamping: bodyComponent.linearDamping,
      angularDamping: bodyComponent.angularDamping,
      allowSleep: bodyComponent.allowSleep,
      fixedRotation: bodyComponent.fixedRotation,
      materialName: bodyComponent.material?.name || 'default',
      enableCCD: false, // 这个属性在当前组件中不存在，使用默认值
      collisionFilterGroup: bodyComponent.collisionFilterGroup,
      collisionFilterMask: bodyComponent.collisionFilterMask
    };

    // 创建碰撞器预设
    let colliderPreset: PhysicsColliderPreset | undefined;
    if (colliderComponent) {
      // 由于组件的私有属性，我们只能获取有限的信息
      colliderPreset = {
        type: colliderComponent.getType() as ColliderType,
        isTrigger: colliderComponent.isTriggerCollider(),
        params: {}, // 参数是私有的，无法获取
        materialName: 'default' // 材质信息无法获取，使用默认值
      };
    }

    // 创建预设
    const preset: PhysicsPreset = {
      name,
      description,
      category,
      bodyPreset,
      colliderPreset
    };

    // 添加预设
    this.addPreset(preset);

    return preset;
  }

  /**
   * 从实体创建物理世界预设
   * @param entity 实体
   * @param name 预设名称
   * @param description 预设描述
   * @param category 预设类别
   * @returns 创建的预设
   */
  public createWorldPresetFromEntity(
    entity: Entity,
    name: string,
    description: string = '',
    category: string = '世界'
  ): PhysicsPreset | null {
    const worldComponent = entity.getComponent<PhysicsWorldComponent>(PhysicsWorldComponent.type);
    if (!worldComponent) {
      Debug.warn(`实体没有物理世界组件，无法创建预设`);
      return null;
    }

    // 创建物理世界预设
    const worldPreset: PhysicsWorldPreset = {
      gravity: worldComponent.getGravity().clone(),
      allowSleep: worldComponent.getAllowSleep(),
      iterations: worldComponent.getIterations(),
      broadphase: worldComponent.getBroadphaseAlgorithm() as 'naive' | 'sap' | 'grid',
      gridBroadphaseSize: 5, // 使用默认值，因为没有getter方法
      defaultFriction: 0.3, // 使用默认值，因为没有getter方法
      defaultRestitution: 0.3 // 使用默认值，因为没有getter方法
    };

    // 创建预设
    const preset: PhysicsPreset = {
      name,
      description,
      category,
      worldPreset
    };

    // 添加预设
    this.addPreset(preset);

    return preset;
  }

  /**
   * 序列化预设为JSON
   * @param name 预设名称
   * @returns JSON字符串
   */
  public serializePreset(name: string): string {
    const preset = this.presets.get(name);
    if (!preset) {
      Debug.warn(`预设 "${name}" 不存在，无法序列化`);
      return '';
    }

    return JSON.stringify(preset, (_key, value) => {
      // 处理THREE.Vector3和THREE.Quaternion
      if (value instanceof THREE.Vector3) {
        return { x: value.x, y: value.y, z: value.z, __type: 'Vector3' };
      } else if (value instanceof THREE.Quaternion) {
        return { x: value.x, y: value.y, z: value.z, w: value.w, __type: 'Quaternion' };
      }
      return value;
    }, 2);
  }

  /**
   * 从JSON反序列化预设
   * @param json JSON字符串
   * @returns 反序列化的预设
   */
  public deserializePreset(json: string): PhysicsPreset | null {
    try {
      const preset = JSON.parse(json, (_key, value) => {
        // 处理THREE.Vector3和THREE.Quaternion
        if (value && value.__type === 'Vector3') {
          return new THREE.Vector3(value.x, value.y, value.z);
        } else if (value && value.__type === 'Quaternion') {
          return new THREE.Quaternion(value.x, value.y, value.z, value.w);
        }
        return value;
      });

      // 验证预设
      if (!preset.name || !preset.category) {
        Debug.warn('无效的预设：缺少必要字段');
        return null;
      }

      // 添加预设
      this.addPreset(preset);

      return preset;
    } catch (error) {
      Debug.error('反序列化预设失败:', error);
      return null;
    }
  }

  /**
   * 导出所有预设为JSON
   * @returns JSON字符串
   */
  public exportAllPresets(): string {
    const presets = this.getAllPresets();
    return JSON.stringify(presets, (_key, value) => {
      // 处理THREE.Vector3和THREE.Quaternion
      if (value instanceof THREE.Vector3) {
        return { x: value.x, y: value.y, z: value.z, __type: 'Vector3' };
      } else if (value instanceof THREE.Quaternion) {
        return { x: value.x, y: value.y, z: value.z, w: value.w, __type: 'Quaternion' };
      }
      return value;
    }, 2);
  }

  /**
   * 导入预设
   * @param json JSON字符串
   * @returns 导入的预设数量
   */
  public importPresets(json: string): number {
    try {
      const presets = JSON.parse(json, (_key, value) => {
        // 处理THREE.Vector3和THREE.Quaternion
        if (value && value.__type === 'Vector3') {
          return new THREE.Vector3(value.x, value.y, value.z);
        } else if (value && value.__type === 'Quaternion') {
          return new THREE.Quaternion(value.x, value.y, value.z, value.w);
        }
        return value;
      });

      if (!Array.isArray(presets)) {
        Debug.warn('无效的预设数据：不是数组');
        return 0;
      }

      let importCount = 0;
      for (const preset of presets) {
        if (preset.name && preset.category) {
          this.addPreset(preset);
          importCount++;
        }
      }

      return importCount;
    } catch (error) {
      Debug.error('导入预设失败:', error);
      return 0;
    }
  }
}
