/**
 * 微服务客户端
 * 负责与后端微服务进行通信
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';
import { ServiceDiscoveryClient, ServiceInstance } from './ServiceDiscoveryClient';

/**
 * 微服务客户端配置
 */
export interface MicroserviceClientConfig {
  /** 服务发现客户端 */
  serviceDiscoveryClient?: ServiceDiscoveryClient;
  /** API网关URL */
  apiGatewayUrl?: string;
  /** 是否使用API网关 */
  useApiGateway?: boolean;
  /** 是否使用服务发现 */
  useServiceDiscovery?: boolean;
  /** 请求超时（毫秒） */
  requestTimeout?: number;
  /** 重试次数 */
  retryCount?: number;
  /** 重试间隔（毫秒） */
  retryInterval?: number;
  /** 是否启用请求缓存 */
  enableRequestCache?: boolean;
  /** 请求缓存时间（毫秒） */
  requestCacheTime?: number;
  /** 认证令牌 */
  authToken?: string;
}

/**
 * 请求选项
 */
export interface RequestOptions {
  /** 请求方法 */
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  /** 请求头 */
  headers?: Record<string, string>;
  /** 请求体 */
  body?: any;
  /** 是否使用缓存 */
  useCache?: boolean;
  /** 缓存时间（毫秒） */
  cacheTime?: number;
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retryCount?: number;
  /** 重试间隔（毫秒） */
  retryInterval?: number;
}

/**
 * 缓存项
 */
interface CacheItem {
  /** 数据 */
  data: any;
  /** 过期时间 */
  expireTime: number;
}

/**
 * 微服务客户端
 * 负责与后端微服务进行通信
 */
export class MicroserviceClient extends EventEmitter {
  /** 配置 */
  private config: Required<MicroserviceClientConfig>;
  
  /** 服务发现客户端 */
  private serviceDiscoveryClient: ServiceDiscoveryClient | null = null;
  
  /** 请求缓存 */
  private requestCache: Map<string, CacheItem> = new Map();
  
  /** 服务实例缓存 */
  private serviceInstanceCache: Map<string, ServiceInstance[]> = new Map();
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /**
   * 创建微服务客户端
   * @param config 配置
   */
  constructor(config: MicroserviceClientConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      serviceDiscoveryClient: undefined,
      apiGatewayUrl: 'http://localhost:3000/api',
      useApiGateway: true,
      useServiceDiscovery: true,
      requestTimeout: 30000, // 30秒
      retryCount: 3,
      retryInterval: 1000,
      enableRequestCache: true,
      requestCacheTime: 60000, // 1分钟
      authToken: '',
      ...config,
    };
    
    // 设置服务发现客户端
    if (this.config.serviceDiscoveryClient) {
      this.serviceDiscoveryClient = this.config.serviceDiscoveryClient;
    } else if (this.config.useServiceDiscovery) {
      this.serviceDiscoveryClient = new ServiceDiscoveryClient();
    }
  }
  
  /**
   * 初始化客户端
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }
    
    // 初始化服务发现客户端
    if (this.serviceDiscoveryClient) {
      this.serviceDiscoveryClient.initialize();
    }
    
    this.initialized = true;
    
    Debug.log('MicroserviceClient', 'Microservice client initialized');
  }
  
  /**
   * 设置认证令牌
   * @param token 认证令牌
   */
  public setAuthToken(token: string): void {
    this.config.authToken = token;
  }
  
  /**
   * 发送请求到服务
   * @param serviceName 服务名称
   * @param endpoint 端点
   * @param options 请求选项
   * @returns 响应数据
   */
  public async request<T = any>(serviceName: string, endpoint: string, options: RequestOptions = {}): Promise<T> {
    // 检查是否已初始化
    if (!this.initialized) {
      this.initialize();
    }
    
    // 合并选项
    const mergedOptions: Required<RequestOptions> = {
      method: 'GET',
      headers: {},
      body: undefined,
      useCache: this.config.enableRequestCache,
      cacheTime: this.config.requestCacheTime,
      timeout: this.config.requestTimeout,
      retryCount: this.config.retryCount,
      retryInterval: this.config.retryInterval,
      ...options,
    };
    
    // 添加认证头
    if (this.config.authToken) {
      mergedOptions.headers['Authorization'] = `Bearer ${this.config.authToken}`;
    }
    
    // 添加内容类型头
    if (mergedOptions.body && !mergedOptions.headers['Content-Type']) {
      mergedOptions.headers['Content-Type'] = 'application/json';
    }
    
    // 生成缓存键
    const cacheKey = this.generateCacheKey(serviceName, endpoint, mergedOptions);
    
    // 检查缓存
    if (mergedOptions.useCache) {
      const cached = this.requestCache.get(cacheKey);
      
      if (cached && Date.now() < cached.expireTime) {
        return cached.data;
      }
    }
    
    // 确定请求URL
    let url: string;
    
    if (this.config.useApiGateway) {
      // 通过API网关请求
      url = `${this.config.apiGatewayUrl}/${serviceName}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    } else if (this.config.useServiceDiscovery && this.serviceDiscoveryClient) {
      // 通过服务发现获取服务实例
      const instances = await this.discoverServiceInstances(serviceName);
      
      if (instances.length === 0) {
        throw new Error(`No instances found for service ${serviceName}`);
      }
      
      // 选择一个实例（简单的负载均衡）
      const instance = this.selectServiceInstance(instances);
      
      // 构建URL
      const protocol = instance.secure ? 'https' : 'http';
      url = `${protocol}://${instance.host}:${instance.port}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    } else {
      throw new Error('Either API gateway or service discovery must be enabled');
    }
    
    // 发送请求
    try {
      const response = await this.sendRequest(url, mergedOptions);
      
      // 缓存响应
      if (mergedOptions.useCache) {
        this.requestCache.set(cacheKey, {
          data: response,
          expireTime: Date.now() + mergedOptions.cacheTime,
        });
      }
      
      return response;
    } catch (error) {
      Debug.error('MicroserviceClient', `Request to ${serviceName}/${endpoint} failed:`, error);
      throw error;
    }
  }
  
  /**
   * 发送请求
   * @param url URL
   * @param options 请求选项
   * @returns 响应数据
   */
  private async sendRequest<T = any>(url: string, options: Required<RequestOptions>): Promise<T> {
    let retries = 0;
    let lastError: Error | null = null;
    
    while (retries <= options.retryCount) {
      try {
        // 创建请求控制器
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), options.timeout);
        
        // 准备请求选项
        const fetchOptions: RequestInit = {
          method: options.method,
          headers: options.headers,
          signal: controller.signal,
        };
        
        // 添加请求体
        if (options.body !== undefined) {
          fetchOptions.body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
        }
        
        // 发送请求
        const response = await fetch(url, fetchOptions);
        
        // 清除超时
        clearTimeout(timeoutId);
        
        // 检查响应状态
        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
        }
        
        // 解析响应
        const data = await response.json();
        
        return data;
      } catch (error) {
        lastError = error as Error;
        
        // 如果是中止错误，则不重试
        if (error instanceof DOMException && error.name === 'AbortError') {
          throw new Error(`Request timeout after ${options.timeout}ms`);
        }
        
        // 增加重试次数
        retries++;
        
        // 如果还有重试次数，则等待后重试
        if (retries <= options.retryCount) {
          await new Promise(resolve => setTimeout(resolve, options.retryInterval));
        }
      }
    }
    
    // 所有重试都失败
    throw lastError || new Error('Request failed');
  }
  
  /**
   * 发现服务实例
   * @param serviceName 服务名称
   * @returns 服务实例列表
   */
  private async discoverServiceInstances(serviceName: string): Promise<ServiceInstance[]> {
    if (!this.serviceDiscoveryClient) {
      throw new Error('Service discovery client is not available');
    }
    
    try {
      // 获取服务实例
      const instances = await this.serviceDiscoveryClient.discoverService(serviceName);
      
      // 更新缓存
      this.serviceInstanceCache.set(serviceName, instances);
      
      return instances;
    } catch (error) {
      // 如果发现失败，则使用缓存
      const cachedInstances = this.serviceInstanceCache.get(serviceName);
      
      if (cachedInstances && cachedInstances.length > 0) {
        Debug.warn('MicroserviceClient', `Using cached instances for service ${serviceName}`);
        return cachedInstances;
      }
      
      throw error;
    }
  }
  
  /**
   * 选择服务实例
   * @param instances 服务实例列表
   * @returns 选择的服务实例
   */
  private selectServiceInstance(instances: ServiceInstance[]): ServiceInstance {
    // 过滤出健康的实例
    const healthyInstances = instances.filter(instance => instance.status === 'UP');
    
    if (healthyInstances.length === 0) {
      // 如果没有健康的实例，则使用所有实例
      Debug.warn('MicroserviceClient', 'No healthy instances found, using all instances');
      return instances[Math.floor(Math.random() * instances.length)];
    }
    
    // 随机选择一个健康的实例（简单的负载均衡）
    return healthyInstances[Math.floor(Math.random() * healthyInstances.length)];
  }
  
  /**
   * 生成缓存键
   * @param serviceName 服务名称
   * @param endpoint 端点
   * @param options 请求选项
   * @returns 缓存键
   */
  private generateCacheKey(serviceName: string, endpoint: string, options: Required<RequestOptions>): string {
    const { method, body } = options;
    const bodyString = body ? JSON.stringify(body) : '';
    
    return `${method}:${serviceName}:${endpoint}:${bodyString}`;
  }
  
  /**
   * 清除请求缓存
   * @param serviceName 服务名称（可选，如果提供则只清除该服务的缓存）
   * @param endpoint 端点（可选，如果提供则只清除该端点的缓存）
   */
  public clearRequestCache(serviceName?: string, endpoint?: string): void {
    if (!serviceName) {
      // 清除所有缓存
      this.requestCache.clear();
      return;
    }
    
    const prefix = endpoint
      ? `${serviceName}:${endpoint}`
      : `${serviceName}:`;
    
    // 清除匹配的缓存
    for (const key of this.requestCache.keys()) {
      if (key.includes(prefix)) {
        this.requestCache.delete(key);
      }
    }
  }
  
  /**
   * 销毁客户端
   */
  public destroy(): void {
    // 清除缓存
    this.requestCache.clear();
    this.serviceInstanceCache.clear();
    
    // 移除所有事件监听器
    this.removeAllListeners();
    
    this.initialized = false;
    
    Debug.log('MicroserviceClient', 'Microservice client destroyed');
  }
}
