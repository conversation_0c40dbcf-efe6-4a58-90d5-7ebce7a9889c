import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServiceInstance } from './service-discovery.service';

export enum LoadBalancingStrategy {
  ROUND_ROBIN = 'round_robin',
  WEIGHTED_ROUND_ROBIN = 'weighted_round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  RANDOM = 'random',
  CONSISTENT_HASH = 'consistent_hash',
  HEALTH_BASED = 'health_based',
}

interface ServiceStats {
  requestCount: number;
  activeConnections: number;
  averageResponseTime: number;
  errorRate: number;
  lastRequestTime: Date;
}

@Injectable()
export class LoadBalancerService {
  private readonly logger = new Logger(LoadBalancerService.name);
  private readonly strategy: LoadBalancingStrategy;
  private readonly roundRobinCounters = new Map<string, number>();
  private readonly serviceStats = new Map<string, ServiceStats>();
  private readonly connectionCounts = new Map<string, number>();

  constructor(private readonly configService: ConfigService) {
    this.strategy = this.configService.get(
      'LOAD_BALANCING_STRATEGY',
      LoadBalancingStrategy.ROUND_ROBIN
    ) as LoadBalancingStrategy;
  }

  /**
   * 选择服务实例
   */
  selectInstance(serviceName: string, instances: ServiceInstance[]): string | null {
    if (instances.length === 0) {
      return null;
    }

    if (instances.length === 1) {
      return this.buildInstanceUrl(instances[0]);
    }

    // 过滤健康的实例
    const healthyInstances = instances.filter(instance => instance.health);
    if (healthyInstances.length === 0) {
      this.logger.warn(`没有健康的服务实例: ${serviceName}`);
      return null;
    }

    let selectedInstance: ServiceInstance;

    switch (this.strategy) {
      case LoadBalancingStrategy.ROUND_ROBIN:
        selectedInstance = this.roundRobinSelect(serviceName, healthyInstances);
        break;
      
      case LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
        selectedInstance = this.weightedRoundRobinSelect(serviceName, healthyInstances);
        break;
      
      case LoadBalancingStrategy.LEAST_CONNECTIONS:
        selectedInstance = this.leastConnectionsSelect(healthyInstances);
        break;
      
      case LoadBalancingStrategy.RANDOM:
        selectedInstance = this.randomSelect(healthyInstances);
        break;
      
      case LoadBalancingStrategy.CONSISTENT_HASH:
        selectedInstance = this.consistentHashSelect(serviceName, healthyInstances);
        break;
      
      case LoadBalancingStrategy.HEALTH_BASED:
        selectedInstance = this.healthBasedSelect(healthyInstances);
        break;
      
      default:
        selectedInstance = this.roundRobinSelect(serviceName, healthyInstances);
    }

    const instanceUrl = this.buildInstanceUrl(selectedInstance);
    this.updateStats(selectedInstance.id);
    
    return instanceUrl;
  }

  /**
   * 轮询选择
   */
  private roundRobinSelect(serviceName: string, instances: ServiceInstance[]): ServiceInstance {
    const counter = this.roundRobinCounters.get(serviceName) || 0;
    const index = counter % instances.length;
    
    this.roundRobinCounters.set(serviceName, counter + 1);
    
    return instances[index];
  }

  /**
   * 加权轮询选择
   */
  private weightedRoundRobinSelect(serviceName: string, instances: ServiceInstance[]): ServiceInstance {
    // 根据实例的权重进行选择，权重可以从metadata中获取
    const weightedInstances: Array<{ instance: ServiceInstance; weight: number }> = instances.map(instance => ({
      instance,
      weight: instance.metadata.weight || 1,
    }));

    const totalWeight = weightedInstances.reduce((sum, item) => sum + item.weight, 0);
    const counter = this.roundRobinCounters.get(serviceName) || 0;
    const weightedIndex = counter % totalWeight;

    let currentWeight = 0;
    for (const item of weightedInstances) {
      currentWeight += item.weight;
      if (weightedIndex < currentWeight) {
        this.roundRobinCounters.set(serviceName, counter + 1);
        return item.instance;
      }
    }

    // 回退到第一个实例
    this.roundRobinCounters.set(serviceName, counter + 1);
    return instances[0];
  }

  /**
   * 最少连接选择
   */
  private leastConnectionsSelect(instances: ServiceInstance[]): ServiceInstance {
    let selectedInstance = instances[0];
    let minConnections = this.connectionCounts.get(selectedInstance.id) || 0;

    for (const instance of instances) {
      const connections = this.connectionCounts.get(instance.id) || 0;
      if (connections < minConnections) {
        minConnections = connections;
        selectedInstance = instance;
      }
    }

    return selectedInstance;
  }

  /**
   * 随机选择
   */
  private randomSelect(instances: ServiceInstance[]): ServiceInstance {
    const index = Math.floor(Math.random() * instances.length);
    return instances[index];
  }

  /**
   * 一致性哈希选择
   */
  private consistentHashSelect(serviceName: string, instances: ServiceInstance[]): ServiceInstance {
    // 简化的一致性哈希实现
    const hash = this.simpleHash(serviceName);
    const index = hash % instances.length;
    return instances[index];
  }

  /**
   * 基于健康状态选择
   */
  private healthBasedSelect(instances: ServiceInstance[]): ServiceInstance {
    // 根据响应时间和错误率选择最佳实例
    let bestInstance = instances[0];
    let bestScore = this.calculateHealthScore(bestInstance);

    for (const instance of instances) {
      const score = this.calculateHealthScore(instance);
      if (score > bestScore) {
        bestScore = score;
        bestInstance = instance;
      }
    }

    return bestInstance;
  }

  /**
   * 计算健康分数
   */
  private calculateHealthScore(instance: ServiceInstance): number {
    const stats = this.serviceStats.get(instance.id);
    if (!stats) {
      return 1.0; // 新实例给予最高分数
    }

    // 基于响应时间和错误率计算分数
    const responseTimeScore = Math.max(0, 1 - (stats.averageResponseTime / 5000)); // 5秒为基准
    const errorRateScore = Math.max(0, 1 - stats.errorRate);
    const connectionScore = Math.max(0, 1 - (stats.activeConnections / 100)); // 100个连接为基准

    return (responseTimeScore + errorRateScore + connectionScore) / 3;
  }

  /**
   * 增加连接计数
   */
  incrementConnections(instanceId: string): void {
    const current = this.connectionCounts.get(instanceId) || 0;
    this.connectionCounts.set(instanceId, current + 1);
  }

  /**
   * 减少连接计数
   */
  decrementConnections(instanceId: string): void {
    const current = this.connectionCounts.get(instanceId) || 0;
    this.connectionCounts.set(instanceId, Math.max(0, current - 1));
  }

  /**
   * 更新统计信息
   */
  updateStats(instanceId: string, responseTime?: number, isError?: boolean): void {
    const stats = this.serviceStats.get(instanceId) || {
      requestCount: 0,
      activeConnections: 0,
      averageResponseTime: 0,
      errorRate: 0,
      lastRequestTime: new Date(),
    };

    stats.requestCount++;
    stats.lastRequestTime = new Date();

    if (responseTime !== undefined) {
      // 计算移动平均响应时间
      stats.averageResponseTime = (stats.averageResponseTime * 0.9) + (responseTime * 0.1);
    }

    if (isError !== undefined) {
      // 计算移动平均错误率
      const errorValue = isError ? 1 : 0;
      stats.errorRate = (stats.errorRate * 0.9) + (errorValue * 0.1);
    }

    stats.activeConnections = this.connectionCounts.get(instanceId) || 0;
    this.serviceStats.set(instanceId, stats);
  }

  /**
   * 获取负载均衡统计信息
   */
  getLoadBalancerStats(): {
    strategy: LoadBalancingStrategy;
    totalRequests: number;
    instanceStats: Array<{
      instanceId: string;
      requestCount: number;
      activeConnections: number;
      averageResponseTime: number;
      errorRate: number;
      healthScore: number;
    }>;
  } {
    const instanceStats = Array.from(this.serviceStats.entries()).map(([instanceId, stats]) => ({
      instanceId,
      requestCount: stats.requestCount,
      activeConnections: stats.activeConnections,
      averageResponseTime: stats.averageResponseTime,
      errorRate: stats.errorRate,
      healthScore: this.calculateHealthScore({ id: instanceId } as ServiceInstance),
    }));

    const totalRequests = instanceStats.reduce((sum, stat) => sum + stat.requestCount, 0);

    return {
      strategy: this.strategy,
      totalRequests,
      instanceStats,
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.serviceStats.clear();
    this.connectionCounts.clear();
    this.roundRobinCounters.clear();
    this.logger.log('负载均衡统计信息已重置');
  }

  /**
   * 构建实例URL
   */
  private buildInstanceUrl(instance: ServiceInstance): string {
    const protocol = instance.metadata.protocol || 'http';
    return `${protocol}://${instance.address}:${instance.port}`;
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }
}
