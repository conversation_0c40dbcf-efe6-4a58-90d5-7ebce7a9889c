/**
 * 动画遮罩
 * 用于控制动画对骨骼的影响
 */
import * as THREE from 'three';
/**
 * 遮罩类型
 */
export declare enum MaskType {
    /** 包含 - 只影响列表中的骨骼 */
    INCLUDE = "include",
    /** 排除 - 影响除列表外的所有骨骼 */
    EXCLUDE = "exclude",
    /** 层级 - 影响列表中的骨骼及其子骨骼 */
    HIERARCHY = "hierarchy",
    /** 反层级 - 影响除列表中的骨骼及其子骨骼外的所有骨骼 */
    INVERSE_HIERARCHY = "inverseHierarchy",
    /** 组 - 影响预定义骨骼组中的骨骼 */
    GROUP = "group",
    /** 混合 - 混合多个遮罩 */
    BLEND = "blend",
    /** 过渡 - 在两个遮罩之间过渡 */
    TRANSITION = "transition"
}
/**
 * 遮罩权重类型
 */
export declare enum MaskWeightType {
    /** 二进制 - 骨骼要么完全受影响，要么完全不受影响 */
    BINARY = "binary",
    /** 平滑 - 骨骼可以部分受影响 */
    SMOOTH = "smooth",
    /** 距离 - 根据骨骼与根骨骼的距离计算权重 */
    DISTANCE = "distance",
    /** 渐变 - 根据骨骼在层级中的位置计算权重 */
    GRADIENT = "gradient",
    /** 动态 - 根据动画状态动态计算权重 */
    DYNAMIC = "dynamic",
    /** 曲线 - 使用曲线函数计算权重 */
    CURVE = "curve",
    /** 混合 - 混合多个权重类型 */
    BLEND = "blend"
}
/**
 * 骨骼组类型
 */
export declare enum BoneGroupType {
    /** 头部 */
    HEAD = "head",
    /** 躯干 */
    TORSO = "torso",
    /** 左手臂 */
    LEFT_ARM = "leftArm",
    /** 右手臂 */
    RIGHT_ARM = "rightArm",
    /** 左腿 */
    LEFT_LEG = "leftLeg",
    /** 右腿 */
    RIGHT_LEG = "rightLeg",
    /** 左手 */
    LEFT_HAND = "leftHand",
    /** 右手 */
    RIGHT_HAND = "rightHand",
    /** 左脚 */
    LEFT_FOOT = "leftFoot",
    /** 右脚 */
    RIGHT_FOOT = "rightFoot",
    /** 上半身 */
    UPPER_BODY = "upperBody",
    /** 下半身 */
    LOWER_BODY = "lowerBody",
    /** 左侧 */
    LEFT_SIDE = "leftSide",
    /** 右侧 */
    RIGHT_SIDE = "rightSide",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 动画遮罩事件类型
 */
export declare enum AnimationMaskEventType {
    /** 遮罩改变 */
    MASK_CHANGED = "maskChanged",
    /** 权重改变 */
    WEIGHT_CHANGED = "weightChanged",
    /** 遮罩类型改变 */
    TYPE_CHANGED = "typeChanged",
    /** 权重类型改变 */
    WEIGHT_TYPE_CHANGED = "weightTypeChanged",
    /** 遮罩应用 */
    MASK_APPLIED = "maskApplied",
    /** 遮罩合并 */
    MASK_MERGED = "maskMerged",
    /** 遮罩反转 */
    MASK_INVERTED = "maskInverted",
    /** 遮罩层级更新 */
    HIERARCHY_UPDATED = "hierarchyUpdated"
}
/**
 * 动画遮罩配置
 */
export interface AnimationMaskConfig {
    /** 遮罩名称 */
    name?: string;
    /** 遮罩类型 */
    type?: MaskType;
    /** 权重类型 */
    weightType?: MaskWeightType;
    /** 骨骼列表 */
    bones?: string[];
    /** 骨骼权重映射 */
    boneWeights?: Map<string, number>;
    /** 是否启用调试 */
    debug?: boolean;
    /** 骨骼层级映射 */
    boneHierarchy?: Map<string, string[]>;
    /** 根骨骼 */
    rootBone?: string;
    /** 距离权重配置 */
    distanceWeightConfig?: {
        /** 最大距离 */
        maxDistance: number;
        /** 最小权重 */
        minWeight: number;
        /** 衰减函数 */
        falloffFunction?: 'linear' | 'quadratic' | 'exponential' | 'custom';
        /** 自定义衰减函数 */
        customFalloff?: (distance: number, maxDistance: number) => number;
    };
    /** 渐变权重配置 */
    gradientWeightConfig?: {
        /** 开始权重 */
        startWeight: number;
        /** 结束权重 */
        endWeight: number;
        /** 渐变函数 */
        gradientFunction?: 'linear' | 'smooth' | 'custom';
        /** 自定义渐变函数 */
        customGradient?: (level: number, maxLevel: number) => number;
    };
    /** 动态权重配置 */
    dynamicWeightConfig?: {
        /** 权重更新函数 */
        updateFunction: (bone: string, time: number, clip: THREE.AnimationClip) => number;
        /** 更新频率（秒） */
        updateFrequency?: number;
    };
}
/**
 * 动画遮罩
 * 用于控制动画对骨骼的影响
 */
export declare class AnimationMask {
    /** 遮罩名称 */
    private name;
    /** 遮罩类型 */
    private type;
    /** 权重类型 */
    private weightType;
    /** 骨骼列表 */
    private bones;
    /** 骨骼权重映射 */
    private boneWeights;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否启用调试 */
    private debug;
    /** 骨骼层级映射 */
    private boneHierarchy;
    /** 根骨骼 */
    private rootBone;
    /** 距离权重配置 */
    private distanceWeightConfig?;
    /** 渐变权重配置 */
    private gradientWeightConfig?;
    /** 动态权重配置 */
    private dynamicWeightConfig?;
    /** 混合参数 */
    private blendParams?;
    /** 过渡参数 */
    private transitionParams?;
    /**
     * 创建动画遮罩
     * @param config 配置
     */
    constructor(config?: AnimationMaskConfig);
    /**
     * 获取遮罩名称
     * @returns 遮罩名称
     */
    getName(): string;
    /**
     * 设置遮罩名称
     * @param name 遮罩名称
     */
    setName(name: string): void;
    /**
     * 获取遮罩类型
     * @returns 遮罩类型
     */
    getType(): MaskType;
    /**
     * 设置遮罩类型
     * @param type 遮罩类型
     */
    setType(type: MaskType): void;
    /**
     * 获取权重类型
     * @returns 权重类型
     */
    getWeightType(): MaskWeightType;
    /**
     * 设置权重类型
     * @param weightType 权重类型
     */
    setWeightType(weightType: MaskWeightType): void;
    /**
     * 设置混合参数
     * @param masks 遮罩列表
     * @param weights 权重列表
     * @param normalize 是否归一化
     */
    setBlendParams(masks: AnimationMask[], weights: number[], normalize?: boolean): void;
    /**
     * 获取混合参数
     */
    getBlendParams(): {
        masks: AnimationMask[];
        weights: number[];
        normalize: boolean;
    } | undefined;
    /**
     * 设置过渡参数
     * @param fromMask 源遮罩
     * @param toMask 目标遮罩
     * @param progress 过渡进度
     */
    setTransitionParams(fromMask: AnimationMask, toMask: AnimationMask, progress: number): void;
    /**
     * 获取过渡参数
     */
    getTransitionParams(): {
        fromMask: AnimationMask;
        toMask: AnimationMask;
        progress: number;
    } | undefined;
    /**
     * 添加骨骼
     * @param boneName 骨骼名称
     * @param weight 权重（仅在SMOOTH模式下有效）
     */
    addBone(boneName: string, weight?: number): void;
    /**
     * 移除骨骼
     * @param boneName 骨骼名称
     */
    removeBone(boneName: string): void;
    /**
     * 设置骨骼权重
     * @param boneName 骨骼名称
     * @param weight 权重
     */
    setBoneWeight(boneName: string, weight: number): void;
    /**
     * 获取骨骼权重
     * @param boneName 骨骼名称
     * @param skeleton 骨骼对象（用于层级和距离计算）
     * @param time 当前时间（用于动态权重计算）
     * @param clip 动画剪辑（用于动态权重计算）
     * @returns 权重
     */
    getBoneWeight(boneName: string, skeleton?: THREE.Skeleton, time?: number, clip?: THREE.AnimationClip): number;
    /**
     * 获取二进制权重
     * @param boneName 骨骼名称
     * @returns 权重
     */
    private getBinaryWeight;
    /**
     * 检查骨骼是否在骨骼组中
     * @param boneName 骨骼名称
     * @returns 是否在骨骼组中
     */
    private isInBoneGroup;
    /** 骨骼组映射 */
    private static boneGroups;
    /**
     * 初始化骨骼组
     */
    private static initBoneGroups;
    /**
     * 获取骨骼组
     * @param groupType 骨骼组类型
     * @returns 骨骼组
     */
    static getBoneGroup(groupType: BoneGroupType): string[];
    /**
     * 获取混合权重
     * @param boneName 骨骼名称
     * @returns 混合权重
     */
    private getBlendedWeight;
    /**
     * 获取过渡权重
     * @param boneName 骨骼名称
     * @returns 过渡权重
     */
    private getTransitionWeight;
    /**
     * 获取平滑权重
     * @param boneName 骨骼名称
     * @returns 权重
     */
    private getSmoothWeight;
    /**
     * 获取距离权重
     * @param boneName 骨骼名称
     * @param skeleton 骨骼对象
     * @returns 权重
     */
    private getDistanceWeight;
    /**
     * 计算衰减
     * @param t 归一化距离（0-1）
     * @returns 权重
     */
    private calculateFalloff;
    /**
     * 获取渐变权重
     * @param boneName 骨骼名称
     * @param skeleton 骨骼对象
     * @returns 权重
     */
    private getGradientWeight;
    /**
     * 计算渐变
     * @param t 归一化层级（0-1）
     * @returns 权重
     */
    private calculateGradient;
    /**
     * 获取动态权重
     * @param boneName 骨骼名称
     * @param time 当前时间
     * @param clip 动画剪辑
     * @returns 权重
     */
    private getDynamicWeight;
    /**
     * 清空骨骼列表
     */
    clearBones(): void;
    /**
     * 获取骨骼列表
     * @returns 骨骼列表
     */
    getBones(): string[];
    /**
     * 获取骨骼权重映射
     * @returns 骨骼权重映射
     */
    getBoneWeights(): Map<string, number>;
    /**
     * 应用遮罩到动画剪辑
     * @param clip 动画剪辑
     * @param skeleton 骨骼对象（用于层级和距离计算）
     * @param time 当前时间（用于动态权重计算）
     * @returns 遮罩后的动画剪辑
     */
    applyToClip(clip: THREE.AnimationClip, skeleton?: THREE.Skeleton, time?: number): THREE.AnimationClip;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: AnimationMaskEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: AnimationMaskEventType, listener: (data: any) => void): void;
    /**
     * 创建上半身遮罩
     * @param config 配置
     * @returns 上半身遮罩
     */
    static createUpperBodyMask(config?: Partial<AnimationMaskConfig>): AnimationMask;
    /**
     * 创建下半身遮罩
     * @param config 配置
     * @returns 下半身遮罩
     */
    static createLowerBodyMask(config?: Partial<AnimationMaskConfig>): AnimationMask;
    /**
     * 创建左手遮罩
     * @param config 配置
     * @returns 左手遮罩
     */
    static createLeftHandMask(config?: Partial<AnimationMaskConfig>): AnimationMask;
    /**
     * 创建右手遮罩
     * @param config 配置
     * @returns 右手遮罩
     */
    static createRightHandMask(config?: Partial<AnimationMaskConfig>): AnimationMask;
    /**
     * 创建骨骼组遮罩
     * @param groupType 骨骼组类型
     * @param config 配置
     * @returns 骨骼组遮罩
     */
    static createBoneGroupMask(groupType: BoneGroupType, config?: Partial<AnimationMaskConfig>): AnimationMask;
    /**
     * 创建混合遮罩
     * @param masks 遮罩列表
     * @param weights 权重列表
     * @param normalize 是否归一化权重
     * @param config 配置
     * @returns 混合遮罩
     */
    static createBlendMask(masks: AnimationMask[], weights: number[], normalize?: boolean, config?: Partial<AnimationMaskConfig>): AnimationMask;
    /**
     * 创建过渡遮罩
     * @param fromMask 源遮罩
     * @param toMask 目标遮罩
     * @param progress 过渡进度（0-1）
     * @param config 配置
     * @returns 过渡遮罩
     */
    static createTransitionMask(fromMask: AnimationMask, toMask: AnimationMask, progress?: number, config?: Partial<AnimationMaskConfig>): AnimationMask;
    /**
     * 更新过渡进度
     * @param progress 过渡进度（0-1）
     */
    updateTransitionProgress(progress: number): void;
    /**
     * 检查骨骼是否在层级中
     * @param boneName 骨骼名称
     * @returns 是否在层级中
     */
    private isInHierarchy;
    /**
     * 查找骨骼
     * @param skeleton 骨骼对象
     * @param boneName 骨骼名称
     * @returns 骨骼对象
     */
    private findBone;
    /**
     * 获取骨骼层级
     * @param boneName 骨骼名称
     * @param skeleton 骨骼对象
     * @returns 骨骼层级（-1表示找不到）
     */
    private getBoneLevel;
    /**
     * 获取最大骨骼层级
     * @param skeleton 骨骼对象
     * @returns 最大骨骼层级
     */
    private getMaxBoneLevel;
    /**
     * 更新骨骼层级
     * @param skeleton 骨骼对象
     */
    updateBoneHierarchy(skeleton: THREE.Skeleton): void;
}
