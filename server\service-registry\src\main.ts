/**
 * 服务注册中心入口文件
 */
import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as compression from 'compression';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';

async function bootstrap() {
  let retryCount = 0;
  const maxRetries = 10;

  while (retryCount < maxRetries) {
    try {
      console.log('🚀 启动服务注册中心...');
      console.log('📊 环境变量检查:');
      console.log(`  - NODE_ENV: ${process.env.NODE_ENV}`);
      console.log(`  - DB_HOST: ${process.env.DB_HOST}`);
      console.log(`  - DB_DATABASE: ${process.env.DB_DATABASE}`);
      console.log(`  - SERVICE_REGISTRY_PORT: ${process.env.SERVICE_REGISTRY_PORT}`);
      console.log(`  - SERVICE_REGISTRY_HTTP_PORT: ${process.env.SERVICE_REGISTRY_HTTP_PORT}`);

      // 创建Nest应用实例
      const app = await NestFactory.create(AppModule, {
        logger: ['error', 'warn', 'log'],
      });
      const configService = app.get(ConfigService);
      console.log('✅ Nest应用实例创建成功');
  
  // 配置微服务
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('SERVICE_REGISTRY_HOST', 'localhost'),
      port: configService.get<number>('SERVICE_REGISTRY_PORT', 3010),
    },
  });
  
  // 配置HTTP服务
  // 全局前缀
  app.setGlobalPrefix('api');
  
  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );
  
  // 启用CORS
  app.enableCors();
  
  // 启用压缩
  app.use(compression());
  
  // 启用安全头
  app.use(helmet());
  
  // Swagger文档
  const config = new DocumentBuilder()
    .setTitle('服务注册中心API')
    .setDescription('DL（Digital Learning）引擎服务注册中心API文档')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  
  // 启动微服务
  await app.startAllMicroservices();
  
      // 启动HTTP服务
      const httpPort = configService.get<number>('SERVICE_REGISTRY_HTTP_PORT', 4010);
      await app.listen(httpPort);
      console.log(`✅ 服务注册中心已启动，微服务端口: ${configService.get<number>('SERVICE_REGISTRY_PORT', 3010)}, HTTP端口: ${httpPort}`);
      console.log(`🌐 健康检查地址: http://localhost:${httpPort}/api/health`);

      // 启动成功，退出重试循环
      break;

    } catch (error) {
      retryCount++;
      console.error(`❌ 服务注册中心启动失败 (尝试 ${retryCount}/${maxRetries}):`, error.message);

      if (retryCount >= maxRetries) {
        console.error('❌ 达到最大重试次数，服务启动失败');
        process.exit(1);
      }

      // 等待一段时间后重试
      const delay = Math.min(1000 * Math.pow(2, retryCount), 30000); // 指数退避，最大30秒
      console.log(`⏳ ${delay/1000}秒后重试...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

bootstrap();
