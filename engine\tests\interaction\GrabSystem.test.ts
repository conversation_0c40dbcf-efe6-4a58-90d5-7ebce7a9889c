/**
 * 抓取系统测试
 */
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Transform } from '../../src/scene/Transform';
import { Vector3 } from 'three';
import { 
  GrabSystem, 
  GrabbableComponent, 
  GrabberComponent,
  GrabbedComponent,
  GrabType,
  Hand,
  PhysicsGrabComponent
} from '../../src/interaction';
import { PhysicsSystem, PhysicsBodyComponent, BodyType } from '../../src/physics';

describe('GrabSystem', () => {
  let world: World;
  let grabSystem: GrabSystem;
  let physicsSystem: PhysicsSystem;
  let grabberEntity: Entity;
  let grabbableEntity: Entity;

  // 每个测试前的准备工作
  beforeEach(() => {
    // 创建世界
    world = new World();

    // 创建物理系统
    physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.8, z: 0 },
      debug: false
    });
    world.addSystem(physicsSystem);

    // 创建抓取系统
    grabSystem = new GrabSystem(world, {
      debug: false,
      enablePhysicsGrab: true
    });
    world.addSystem(grabSystem);

    // 创建抓取者实体
    grabberEntity = new Entity(world);
    grabberEntity.addComponent(new Transform({
      position: new Vector3(0, 1, 0)
    }));
    grabberEntity.addComponent(new GrabberComponent(grabberEntity, {
      maxGrabDistance: 2.0
    }));
    world.addEntity(grabberEntity);

    // 创建可抓取实体
    grabbableEntity = new Entity(world);
    grabbableEntity.addComponent(new Transform({
      position: new Vector3(0, 0, 1)
    }));
    grabbableEntity.addComponent(new GrabbableComponent(grabbableEntity, {
      grabType: GrabType.DIRECT
    }));
    world.addEntity(grabbableEntity);

    // 初始化系统
    grabSystem.initialize();
  });

  // 每个测试后的清理工作
  afterEach(() => {
    // 清理实体和系统
    world.removeEntity(grabberEntity);
    world.removeEntity(grabbableEntity);
    world.removeSystem(grabSystem);
    world.removeSystem(physicsSystem);
  });

  // 测试抓取系统初始化
  test('应该正确初始化抓取系统', () => {
    expect(grabSystem).toBeDefined();
    expect(world.getSystem(GrabSystem.NAME)).toBe(grabSystem);
  });

  // 测试组件注册
  test('应该正确注册组件', () => {
    // 检查是否注册了可抓取组件
    expect(grabbableEntity.getComponent(GrabbableComponent.TYPE)).toBeDefined();
    
    // 检查是否注册了抓取者组件
    expect(grabberEntity.getComponent(GrabberComponent.TYPE)).toBeDefined();
  });

  // 测试抓取功能
  test('应该能够抓取对象', () => {
    // 获取抓取者组件
    const grabberComponent = grabberEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    expect(grabberComponent).toBeDefined();
    
    // 尝试抓取
    const success = grabberComponent!.grab(grabbableEntity, Hand.RIGHT);
    expect(success).toBe(true);
    
    // 检查抓取状态
    const grabbableComponent = grabbableEntity.getComponent<GrabbableComponent>(GrabbableComponent.TYPE);
    expect(grabbableComponent).toBeDefined();
    expect(grabbableComponent!.isGrabbed).toBe(true);
    expect(grabbableComponent!.grabber).toBe(grabberEntity);
    expect(grabbableComponent!.grabbedHand).toBe(Hand.RIGHT);
    
    // 检查抓取者状态
    expect(grabberComponent!.rightHandGrabbed).toBe(grabbableEntity);
    expect(grabberComponent!.leftHandGrabbed).toBeUndefined();
  });

  // 测试释放功能
  test('应该能够释放对象', () => {
    // 获取抓取者组件
    const grabberComponent = grabberEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    expect(grabberComponent).toBeDefined();
    
    // 先抓取
    const grabSuccess = grabberComponent!.grab(grabbableEntity, Hand.RIGHT);
    expect(grabSuccess).toBe(true);
    
    // 然后释放
    const releaseSuccess = grabberComponent!.release(Hand.RIGHT);
    expect(releaseSuccess).toBe(true);
    
    // 检查抓取状态
    const grabbableComponent = grabbableEntity.getComponent<GrabbableComponent>(GrabbableComponent.TYPE);
    expect(grabbableComponent).toBeDefined();
    expect(grabbableComponent!.isGrabbed).toBe(false);
    expect(grabbableComponent!.grabber).toBeUndefined();
    expect(grabbableComponent!.grabbedHand).toBeUndefined();
    
    // 检查抓取者状态
    expect(grabberComponent!.rightHandGrabbed).toBeUndefined();
    expect(grabberComponent!.leftHandGrabbed).toBeUndefined();
  });

  // 测试双手抓取
  test('应该能够用双手抓取不同对象', () => {
    // 创建第二个可抓取实体
    const grabbableEntity2 = new Entity(world);
    grabbableEntity2.addComponent(new Transform({
      position: new Vector3(1, 0, 1)
    }));
    grabbableEntity2.addComponent(new GrabbableComponent(grabbableEntity2, {
      grabType: GrabType.DIRECT
    }));
    world.addEntity(grabbableEntity2);
    
    // 获取抓取者组件
    const grabberComponent = grabberEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    expect(grabberComponent).toBeDefined();
    
    // 用右手抓取第一个对象
    const grabSuccess1 = grabberComponent!.grab(grabbableEntity, Hand.RIGHT);
    expect(grabSuccess1).toBe(true);
    
    // 用左手抓取第二个对象
    const grabSuccess2 = grabberComponent!.grab(grabbableEntity2, Hand.LEFT);
    expect(grabSuccess2).toBe(true);
    
    // 检查抓取者状态
    expect(grabberComponent!.rightHandGrabbed).toBe(grabbableEntity);
    expect(grabberComponent!.leftHandGrabbed).toBe(grabbableEntity2);
    
    // 清理
    world.removeEntity(grabbableEntity2);
  });

  // 测试物理抓取
  test('应该能够抓取物理对象', () => {
    // 添加物理组件
    grabbableEntity.addComponent(new PhysicsBodyComponent(grabbableEntity, {
      type: BodyType.DYNAMIC,
      mass: 1.0
    }));
    
    // 添加物理抓取组件
    grabbableEntity.addComponent(new PhysicsGrabComponent(grabbableEntity, {
      grabForce: 10.0,
      grabDamping: 0.5,
      grabBodyType: BodyType.KINEMATIC
    }));
    
    // 获取抓取者组件
    const grabberComponent = grabberEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    expect(grabberComponent).toBeDefined();
    
    // 尝试抓取
    const success = grabberComponent!.grab(grabbableEntity, Hand.RIGHT);
    expect(success).toBe(true);
    
    // 检查物理抓取组件状态
    const physicsGrabComponent = grabbableEntity.getComponent<PhysicsGrabComponent>(PhysicsGrabComponent.TYPE);
    expect(physicsGrabComponent).toBeDefined();
    expect(physicsGrabComponent!.isGrabbed).toBe(true);
    expect(physicsGrabComponent!.grabber).toBe(grabberEntity);
    expect(physicsGrabComponent!.hand).toBe(Hand.RIGHT);
    
    // 检查物理体类型
    const physicsBodyComponent = grabbableEntity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.TYPE);
    expect(physicsBodyComponent).toBeDefined();
    expect(physicsBodyComponent!.bodyType).toBe(BodyType.KINEMATIC);
  });

  // 测试抓取事件
  test('应该触发抓取和释放事件', () => {
    // 创建事件监听器
    const onGrabMock = jest.fn();
    const onReleaseMock = jest.fn();
    
    // 获取可抓取组件
    const grabbableComponent = grabbableEntity.getComponent<GrabbableComponent>(GrabbableComponent.TYPE);
    expect(grabbableComponent).toBeDefined();
    
    // 添加事件监听器
    grabbableComponent!.on('grab', onGrabMock);
    grabbableComponent!.on('release', onReleaseMock);
    
    // 获取抓取者组件
    const grabberComponent = grabberEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    expect(grabberComponent).toBeDefined();
    
    // 抓取
    grabberComponent!.grab(grabbableEntity, Hand.RIGHT);
    
    // 检查抓取事件是否被触发
    expect(onGrabMock).toHaveBeenCalledTimes(1);
    expect(onGrabMock).toHaveBeenCalledWith(grabbableEntity, grabberEntity);
    
    // 释放
    grabberComponent!.release(Hand.RIGHT);
    
    // 检查释放事件是否被触发
    expect(onReleaseMock).toHaveBeenCalledTimes(1);
    expect(onReleaseMock).toHaveBeenCalledWith(grabbableEntity, grabberEntity);
  });

  // 测试系统更新
  test('应该在更新时更新被抓取对象的位置', () => {
    // 获取抓取者组件
    const grabberComponent = grabberEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    expect(grabberComponent).toBeDefined();
    
    // 抓取
    grabberComponent!.grab(grabbableEntity, Hand.RIGHT);
    
    // 移动抓取者
    const grabberTransform = grabberEntity.getComponent<Transform>('Transform');
    expect(grabberTransform).toBeDefined();
    grabberTransform!.setPosition(1, 2, 3);
    
    // 更新系统
    grabSystem.update(0.016);
    
    // 检查被抓取对象的位置是否更新
    const grabbableTransform = grabbableEntity.getComponent<Transform>('Transform');
    expect(grabbableTransform).toBeDefined();
    
    // 由于抓取系统的实现细节，这里只能做一个近似的检查
    // 实际位置会根据手的偏移计算
    expect(grabbableTransform!.position.x).toBeGreaterThan(0);
    expect(grabbableTransform!.position.y).toBeGreaterThan(1);
    expect(grabbableTransform!.position.z).toBeGreaterThan(2);
  });
});
