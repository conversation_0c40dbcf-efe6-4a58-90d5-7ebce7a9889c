/**
 * 光照探针系统
 * 用于创建和管理光照探针，提供基于图像的光照
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { World } from '../../core/World';
/**
 * 光照探针系统配置
 */
export interface LightProbeSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用调试可视化 */
    enableDebugVisualization?: boolean;
}
/**
 * 光照探针组配置
 */
export interface LightProbeGroupConfig {
    /** 探针间距 */
    spacing?: number;
    /** 探针分辨率 */
    resolution?: {
        x: number;
        y: number;
        z: number;
    };
    /** 探针强度 */
    intensity?: number;
    /** 是否自动更新 */
    autoUpdate?: boolean;
}
/**
 * 光照探针系统
 */
export declare class LightProbeSystem extends System {
    /** 配置 */
    private config;
    /** 光照探针映射 */
    private probes;
    /** 光照探针组映射 */
    private probeGroups;
    /** 帧计数器 */
    private frameCount;
    /** 调试可视化对象 */
    private debugVisualization;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: LightProbeSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 创建光照探针
     * @param id 探针ID
     * @returns 光照探针
     */
    createLightProbe(id?: string): THREE.LightProbe;
    /**
     * 从环境贴图生成光照探针
     * @param probe 光照探针
     * @param envMap 环境贴图
     */
    generateFromEnvironment(probe: THREE.LightProbe, envMap: THREE.Texture): void;
    /**
     * 创建光照探针组
     * @param id 探针组ID
     * @param position 位置
     * @param size 大小
     * @param config 配置
     * @returns 光照探针组
     */
    createLightProbeGroup(id: string, position: THREE.Vector3, size: THREE.Vector3, config?: LightProbeGroupConfig): THREE.Group;
    /**
     * 创建洞穴光照探针
     * @param id 探针ID
     * @param position 位置
     * @param size 大小
     * @param intensity 强度
     * @returns 光照探针
     */
    createCaveLightProbe(id: string, position: THREE.Vector3, size: THREE.Vector3, intensity?: number): THREE.LightProbe;
    /**
     * 获取光照探针
     * @param id 探针ID
     * @returns 光照探针
     */
    getLightProbe(id: string): THREE.LightProbe | undefined;
    /**
     * 获取光照探针组
     * @param id 探针组ID
     * @returns 光照探针组
     */
    getLightProbeGroup(id: string): THREE.Group | undefined;
    /**
     * 移除光照探针
     * @param id 探针ID
     */
    removeLightProbe(id: string): void;
    /**
     * 移除光照探针组
     * @param id 探针组ID
     */
    removeLightProbeGroup(id: string): void;
    /**
     * 清除所有光照探针
     */
    clearLightProbes(): void;
    /**
     * 创建调试可视化
     */
    private createDebugVisualization;
    /**
     * 添加探针到调试可视化
     * @param id 探针ID
     * @param _probe 光照探针
     */
    private addProbeToDebugVisualization;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 销毁系统
     */
    dispose(): void;
}
