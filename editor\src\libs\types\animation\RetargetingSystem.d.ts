/**
 * 动画重定向系统
 * 用于管理和执行动画重定向操作
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { BoneMapping, RetargetingConfig } from './AnimationRetargeting';
import { AnimationRetargeter } from './AnimationRetargeter';
/**
 * 重定向系统配置
 */
export interface RetargetingSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否自动创建骨骼映射 */
    autoCreateMapping?: boolean;
    /** 是否缓存重定向结果 */
    cacheResults?: boolean;
    /** 最大缓存数量 */
    maxCacheSize?: number;
}
/**
 * 重定向结果
 */
export interface RetargetingResult {
    /** 源动画片段名称 */
    sourceClipName: string;
    /** 目标动画片段名称 */
    targetClipName: string;
    /** 源实体ID */
    sourceEntityId: string;
    /** 目标实体ID */
    targetEntityId: string;
    /** 重定向后的动画片段 */
    clip: THREE.AnimationClip;
    /** 重定向配置 */
    config: RetargetingConfig;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 重定向事件类型
 */
export declare enum RetargetingEventType {
    /** 重定向开始 */
    RETARGET_START = "retargetStart",
    /** 重定向完成 */
    RETARGET_COMPLETE = "retargetComplete",
    /** 重定向错误 */
    RETARGET_ERROR = "retargetError",
    /** 骨骼映射更新 */
    MAPPING_UPDATED = "mappingUpdated",
    /** 配置更新 */
    CONFIG_UPDATED = "configUpdated"
}
/**
 * 动画重定向系统
 * 用于管理和执行动画重定向操作
 */
export declare class RetargetingSystem extends System {
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 重定向器映射 */
    private retargeters;
    /** 结果缓存 */
    private resultCache;
    /** 骨骼映射预设 */
    private mappingPresets;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(config?: RetargetingSystemConfig);
    /**
     * 注册默认骨骼映射预设
     */
    private registerDefaultMappingPresets;
    /**
     * 注册骨骼映射预设
     * @param name 预设名称
     * @param mapping 骨骼映射
     */
    registerMappingPreset(name: string, mapping: BoneMapping[]): void;
    /**
     * 获取骨骼映射预设
     * @param name 预设名称
     * @returns 骨骼映射
     */
    getMappingPreset(name: string): BoneMapping[] | null;
    /**
     * 获取所有骨骼映射预设名称
     * @returns 预设名称数组
     */
    getMappingPresetNames(): string[];
    /**
     * 创建重定向器
     * @param sourceEntity 源实体
     * @param targetEntity 目标实体
     * @param config 重定向配置
     * @returns 重定向器
     */
    createRetargeter(sourceEntity: Entity, targetEntity: Entity, config?: Partial<RetargetingConfig>): AnimationRetargeter | null;
    /**
     * 从实体获取骨骼
     * @param entity 实体
     * @returns 骨骼
     */
    private getSkeletonFromEntity;
    /**
     * 重定向动画片段
     * @param sourceClip 源动画片段
     * @param sourceEntity 源实体
     * @param targetEntity 目标实体
     * @param config 重定向配置
     * @returns 重定向后的动画片段
     */
    retargetClip(sourceClip: THREE.AnimationClip, sourceEntity: Entity, targetEntity: Entity, config?: Partial<RetargetingConfig>): THREE.AnimationClip | null;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: RetargetingEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: RetargetingEventType, listener: (data: any) => void): void;
    /**
     * 清除缓存
     */
    clearCache(): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
