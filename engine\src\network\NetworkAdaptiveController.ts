/**
 * 网络自适应控制器
 * 用于根据网络质量自动调整网络参数
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';
import { NetworkQualityData, NetworkQualityLevel } from './NetworkQualityMonitor';
import { BandwidthController } from './BandwidthController';
import { EntitySyncManager } from './EntitySyncManager';

/**
 * 网络自适应策略
 */
export enum AdaptiveStrategy {
  /** 保守策略 - 优先保证稳定性 */
  CONSERVATIVE = 'conservative',
  /** 平衡策略 - 平衡稳定性和性能 */
  BALANCED = 'balanced',
  /** 激进策略 - 优先保证性能 */
  AGGRESSIVE = 'aggressive',
  /** 自动策略 - 根据网络状况自动选择 */
  AUTO = 'auto',
}

/**
 * 网络参数配置
 */
export interface NetworkParamsConfig {
  /** 同步间隔（毫秒） */
  syncInterval: number;
  /** 压缩级别（0-9） */
  compressionLevel: number;
  /** 是否使用增量同步 */
  useDeltaSync: boolean;
  /** 是否使用预测 */
  usePrediction: boolean;
  /** 预测时间（毫秒） */
  predictionTime: number;
  /** 是否使用插值 */
  useInterpolation: boolean;
  /** 插值因子（0-1） */
  interpolationFactor: number;
  /** 是否使用优先级同步 */
  usePrioritySync: boolean;
  /** 是否使用空间分区 */
  useSpatialPartitioning: boolean;
  /** 是否使用抖动缓冲 */
  useJitterBuffer: boolean;
  /** 抖动缓冲大小（毫秒） */
  jitterBufferSize: number;
  /** 最大上传带宽（字节/秒） */
  maxUploadBandwidth: number;
  /** 最大下载带宽（字节/秒） */
  maxDownloadBandwidth: number;
}

/**
 * 网络自适应控制器配置
 */
export interface NetworkAdaptiveControllerConfig {
  /** 自适应策略 */
  strategy?: AdaptiveStrategy;
  /** 调整间隔（毫秒） */
  adjustInterval?: number;
  /** 是否启用自动调整 */
  enableAutoAdjust?: boolean;
  /** 是否启用历史记录 */
  enableHistory?: boolean;
  /** 历史记录大小 */
  historySize?: number;
  /** 是否启用调试日志 */
  enableDebugLog?: boolean;
  /** 是否启用平滑过渡 */
  enableSmoothTransition?: boolean;
  /** 平滑因子（0-1） */
  smoothFactor?: number;
  /** 是否启用预测性调整 */
  enablePredictiveAdjustment?: boolean;
  /** 预测窗口大小（毫秒） */
  predictiveWindowSize?: number;
}

/**
 * 网络参数调整记录
 */
export interface NetworkParamsAdjustment {
  /** 时间戳 */
  timestamp: number;
  /** 网络质量 */
  networkQuality: NetworkQualityData;
  /** 调整前参数 */
  beforeParams: NetworkParamsConfig;
  /** 调整后参数 */
  afterParams: NetworkParamsConfig;
  /** 调整原因 */
  reason: string;
}

/**
 * 网络自适应控制器
 * 用于根据网络质量自动调整网络参数
 */
export class NetworkAdaptiveController extends EventEmitter {
  /** 配置 */
  private config: Required<NetworkAdaptiveControllerConfig>;

  /** 当前网络参数 */
  private currentParams: NetworkParamsConfig;

  /** 最近的网络质量数据 */
  private latestNetworkQuality: NetworkQualityData | null = null;

  /** 带宽控制器 */
  private bandwidthController: BandwidthController | null = null;

  /** 实体同步管理器 */
  private entitySyncManager: EntitySyncManager | null = null;

  /** 调整定时器ID */
  private adjustTimerId: number | null = null;

  /** 历史记录 */
  private history: NetworkParamsAdjustment[] = [];

  /** 网络质量历史 */
  private qualityHistory: NetworkQualityData[] = [];

  /** 网络质量趋势 */
  private qualityTrend: 'improving' | 'stable' | 'degrading' = 'stable';

  /** 上次调整时间 */
  private lastAdjustTime: number = 0;

  /** 调整计数器 */
  private adjustCounter: number = 0;

  /**
   * 创建网络自适应控制器
   * @param initialParams 初始网络参数
   * @param config 配置
   */
  constructor(initialParams: NetworkParamsConfig, config: NetworkAdaptiveControllerConfig = {}) {
    super();

    // 默认配置
    this.config = {
      strategy: AdaptiveStrategy.BALANCED,
      adjustInterval: 5000, // 5秒
      enableAutoAdjust: true,
      enableHistory: true,
      historySize: 20,
      enableDebugLog: false,
      enableSmoothTransition: true,
      smoothFactor: 0.3,
      enablePredictiveAdjustment: true,
      predictiveWindowSize: 10000, // 10秒
      ...config,
    };

    // 初始化当前参数
    this.currentParams = { ...initialParams };

    // 如果启用自动调整，则启动调整定时器
    if (this.config.enableAutoAdjust) {
      this.startAutoAdjust();
    }
  }

  /**
   * 设置带宽控制器
   * @param controller 带宽控制器
   */
  public setBandwidthController(controller: BandwidthController): void {
    this.bandwidthController = controller;
  }

  /**
   * 设置实体同步管理器
   * @param manager 实体同步管理器
   */
  public setEntitySyncManager(manager: EntitySyncManager): void {
    this.entitySyncManager = manager;
  }

  /**
   * 设置网络质量数据
   * @param quality 网络质量数据
   */
  public setNetworkQuality(quality: NetworkQualityData): void {
    this.latestNetworkQuality = quality;

    // 添加到历史记录
    if (this.config.enableHistory) {
      this.qualityHistory.unshift(quality);

      // 限制历史记录大小
      if (this.qualityHistory.length > this.config.historySize) {
        this.qualityHistory.pop();
      }

      // 更新网络质量趋势
      this.updateQualityTrend();
    }

    // 如果启用预测性调整，则检查是否需要立即调整
    if (this.config.enablePredictiveAdjustment && this.shouldAdjustImmediately()) {
      this.adjustNetworkParams();
    }
  }

  /**
   * 启动自动调整
   */
  public startAutoAdjust(): void {
    if (this.adjustTimerId !== null) {
      return;
    }

    this.adjustTimerId = window.setInterval(() => {
      this.adjustNetworkParams();
    }, this.config.adjustInterval);
  }

  /**
   * 停止自动调整
   */
  public stopAutoAdjust(): void {
    if (this.adjustTimerId !== null) {
      clearInterval(this.adjustTimerId);
      this.adjustTimerId = null;
    }
  }

  /**
   * 调整网络参数
   */
  private adjustNetworkParams(): void {
    // 如果没有网络质量数据，则不调整
    if (!this.latestNetworkQuality) {
      return;
    }

    // 记录调整前的参数
    const beforeParams = { ...this.currentParams };

    // 根据策略调整参数
    let reason = '';
    switch (this.config.strategy) {
      case AdaptiveStrategy.CONSERVATIVE:
        reason = this.adjustConservative();
        break;
      case AdaptiveStrategy.BALANCED:
        reason = this.adjustBalanced();
        break;
      case AdaptiveStrategy.AGGRESSIVE:
        reason = this.adjustAggressive();
        break;
      case AdaptiveStrategy.AUTO:
        reason = this.adjustAuto();
        break;
    }

    // 应用参数
    this.applyNetworkParams();

    // 记录调整后的参数
    const afterParams = { ...this.currentParams };

    // 添加到历史记录
    if (this.config.enableHistory) {
      const adjustment: NetworkParamsAdjustment = {
        timestamp: Date.now(),
        networkQuality: { ...this.latestNetworkQuality },
        beforeParams,
        afterParams,
        reason,
      };

      this.history.unshift(adjustment);

      // 限制历史记录大小
      if (this.history.length > this.config.historySize) {
        this.history.pop();
      }
    }

    // 更新调整计数器和时间
    this.adjustCounter++;
    this.lastAdjustTime = Date.now();

    // 触发调整事件
    this.emit('paramsAdjusted', {
      params: afterParams,
      quality: this.latestNetworkQuality,
      reason,
    });

    // 输出调试日志
    if (this.config.enableDebugLog) {
      Debug.log('NetworkAdaptiveController', `调整网络参数: ${reason}`);
      Debug.log('NetworkAdaptiveController', `网络质量: ${this.latestNetworkQuality.level}`);
      Debug.log('NetworkAdaptiveController', `同步间隔: ${beforeParams.syncInterval} -> ${afterParams.syncInterval}`);
    }
  }

  /**
   * 应用网络参数
   */
  private applyNetworkParams(): void {
    // 应用带宽控制
    if (this.bandwidthController) {
      this.bandwidthController.setMaxUploadBandwidth(this.currentParams.maxUploadBandwidth);
      this.bandwidthController.setMaxDownloadBandwidth(this.currentParams.maxDownloadBandwidth);
    }

    // 应用实体同步参数
    if (this.entitySyncManager) {
      // 由于 EntitySyncManager 没有 setConfig 方法，我们需要通过其他方式应用配置
      // 这里我们可以触发一个事件来通知配置变更
      this.emit('networkParamsChanged', {
        syncInterval: this.currentParams.syncInterval,
        useCompression: this.currentParams.compressionLevel > 0,
        compressionLevel: this.currentParams.compressionLevel,
        useDeltaSync: this.currentParams.useDeltaSync,
        usePrioritySync: this.currentParams.usePrioritySync,
        useSpatialPartitioning: this.currentParams.useSpatialPartitioning,
        useInterpolation: this.currentParams.useInterpolation,
        interpolationFactor: this.currentParams.interpolationFactor,
        useExtrapolation: this.currentParams.usePrediction,
        extrapolationTime: this.currentParams.predictionTime,
      });
    }
  }

  /**
   * 保守策略调整
   * @returns 调整原因
   */
  private adjustConservative(): string {
    const quality = this.latestNetworkQuality!;

    // 根据网络质量调整参数
    switch (quality.level) {
      case NetworkQualityLevel.EXCELLENT:
        // 网络质量极好，适度提高性能
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 150);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 5);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 100);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.5);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = false;
        return '网络质量极好，适度提高性能';

      case NetworkQualityLevel.GOOD:
        // 网络质量良好，保持平衡
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 200);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 6);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 150);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.6);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = false;
        return '网络质量良好，保持平衡';

      case NetworkQualityLevel.MEDIUM:
        // 网络质量一般，增加稳定性
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 250);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 7);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 200);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.7);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = true;
        this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 100);
        return '网络质量一般，增加稳定性';

      case NetworkQualityLevel.BAD:
        // 网络质量差，大幅增加稳定性
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 350);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 8);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 300);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.8);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = true;
        this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 150);
        return '网络质量差，大幅增加稳定性';

      case NetworkQualityLevel.VERY_BAD:
        // 网络质量极差，最大化稳定性
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 500);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 9);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 400);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.9);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = true;
        this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 200);
        return '网络质量极差，最大化稳定性';

      default:
        return '未知网络质量，保持当前设置';
    }
  }

  /**
   * 平衡策略调整
   * @returns 调整原因
   */
  private adjustBalanced(): string {
    const quality = this.latestNetworkQuality!;

    // 根据网络质量调整参数
    switch (quality.level) {
      case NetworkQualityLevel.EXCELLENT:
        // 网络质量极好，提高性能
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 100);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 4);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 80);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.4);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = false;
        return '网络质量极好，提高性能';

      case NetworkQualityLevel.GOOD:
        // 网络质量良好，适度提高性能
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 150);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 5);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 120);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.5);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = false;
        return '网络质量良好，适度提高性能';

      case NetworkQualityLevel.MEDIUM:
        // 网络质量一般，保持平衡
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 200);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 6);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 150);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.6);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = true;
        this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 80);
        return '网络质量一般，保持平衡';

      case NetworkQualityLevel.BAD:
        // 网络质量差，增加稳定性
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 300);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 7);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 250);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.7);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = true;
        this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 120);
        return '网络质量差，增加稳定性';

      case NetworkQualityLevel.VERY_BAD:
        // 网络质量极差，大幅增加稳定性
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 400);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 8);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 350);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.8);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = true;
        this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 150);
        return '网络质量极差，大幅增加稳定性';

      default:
        return '未知网络质量，保持当前设置';
    }
  }

  /**
   * 激进策略调整
   * @returns 调整原因
   */
  private adjustAggressive(): string {
    const quality = this.latestNetworkQuality!;

    // 根据网络质量调整参数
    switch (quality.level) {
      case NetworkQualityLevel.EXCELLENT:
        // 网络质量极好，最大化性能
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 50);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 3);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 50);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.3);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = false;
        return '网络质量极好，最大化性能';

      case NetworkQualityLevel.GOOD:
        // 网络质量良好，大幅提高性能
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 100);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 4);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 80);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.4);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = false;
        return '网络质量良好，大幅提高性能';

      case NetworkQualityLevel.MEDIUM:
        // 网络质量一般，适度提高性能
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 150);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 5);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 120);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.5);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = false;
        return '网络质量一般，适度提高性能';

      case NetworkQualityLevel.BAD:
        // 网络质量差，保持平衡
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 250);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 6);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 200);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.6);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = true;
        this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 100);
        return '网络质量差，保持平衡';

      case NetworkQualityLevel.VERY_BAD:
        // 网络质量极差，增加稳定性
        this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 350);
        this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 7);
        this.currentParams.useDeltaSync = true;
        this.currentParams.usePrediction = true;
        this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 300);
        this.currentParams.useInterpolation = true;
        this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.7);
        this.currentParams.usePrioritySync = true;
        this.currentParams.useSpatialPartitioning = true;
        this.currentParams.useJitterBuffer = true;
        this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 120);
        return '网络质量极差，增加稳定性';

      default:
        return '未知网络质量，保持当前设置';
    }
  }

  /**
   * 自动策略调整
   * @returns 调整原因
   */
  private adjustAuto(): string {
    // 根据网络质量趋势选择策略
    if (this.qualityTrend === 'improving') {
      // 网络质量正在改善，使用更激进的策略
      return this.adjustAggressive();
    } else if (this.qualityTrend === 'degrading') {
      // 网络质量正在恶化，使用更保守的策略
      return this.adjustConservative();
    } else {
      // 网络质量稳定，使用平衡策略
      return this.adjustBalanced();
    }
  }

  /**
   * 平滑调整数值
   * @param currentValue 当前值
   * @param targetValue 目标值
   * @returns 调整后的值
   */
  private smoothAdjust(currentValue: number, targetValue: number): number {
    if (!this.config.enableSmoothTransition) {
      return targetValue;
    }

    const factor = this.config.smoothFactor;
    return currentValue * (1 - factor) + targetValue * factor;
  }

  /**
   * 更新网络质量趋势
   */
  private updateQualityTrend(): void {
    if (this.qualityHistory.length < 3) {
      this.qualityTrend = 'stable';
      return;
    }

    // 计算最近几次质量变化
    const latest = this.qualityHistory[0];
    const previous = this.qualityHistory[1];
    const older = this.qualityHistory[2];

    // 根据RTT和丢包率判断趋势
    const latestScore = this.calculateQualityScore(latest);
    const previousScore = this.calculateQualityScore(previous);
    const olderScore = this.calculateQualityScore(older);

    if (latestScore > previousScore && previousScore > olderScore) {
      this.qualityTrend = 'improving';
    } else if (latestScore < previousScore && previousScore < olderScore) {
      this.qualityTrend = 'degrading';
    } else {
      this.qualityTrend = 'stable';
    }
  }

  /**
   * 计算网络质量分数
   * @param quality 网络质量数据
   * @returns 质量分数（越高越好）
   */
  private calculateQualityScore(quality: NetworkQualityData): number {
    // RTT越低越好，丢包率越低越好
    const rttScore = 1000 / (quality.rtt + 10); // 防止除以0
    const packetLossScore = 1 - quality.packetLoss;

    // 综合分数，权重可以调整
    return rttScore * 0.6 + packetLossScore * 0.4;
  }

  /**
   * 检查是否需要立即调整
   * @returns 是否需要立即调整
   */
  private shouldAdjustImmediately(): boolean {
    // 如果刚刚调整过，则不立即调整
    if (Date.now() - this.lastAdjustTime < 1000) {
      return false;
    }

    // 如果网络质量突然变差，立即调整
    if (this.qualityHistory.length >= 2) {
      const latest = this.qualityHistory[0];
      const previous = this.qualityHistory[1];

      // 如果RTT突然增加50%或丢包率突然增加10%，立即调整
      if (latest.rtt > previous.rtt * 1.5 || latest.packetLoss > previous.packetLoss + 0.1) {
        return true;
      }
    }

    return false;
  }

  /**
   * 获取当前网络参数
   * @returns 当前网络参数
   */
  public getCurrentParams(): NetworkParamsConfig {
    return { ...this.currentParams };
  }

  /**
   * 获取调整历史记录
   * @returns 调整历史记录
   */
  public getAdjustmentHistory(): NetworkParamsAdjustment[] {
    return [...this.history];
  }

  /**
   * 获取网络质量趋势
   * @returns 网络质量趋势
   */
  public getQualityTrend(): 'improving' | 'stable' | 'degrading' {
    return this.qualityTrend;
  }

  /**
   * 设置自适应策略
   * @param strategy 自适应策略
   */
  public setStrategy(strategy: AdaptiveStrategy): void {
    this.config.strategy = strategy;

    // 立即调整参数
    this.adjustNetworkParams();
  }

  /**
   * 手动设置网络参数
   * @param params 网络参数
   */
  public setNetworkParams(params: Partial<NetworkParamsConfig>): void {
    // 更新参数
    this.currentParams = {
      ...this.currentParams,
      ...params,
    };

    // 应用参数
    this.applyNetworkParams();

    // 触发事件
    this.emit('paramsManuallySet', {
      params: this.currentParams,
    });
  }

  /**
   * 重置为默认参数
   */
  public resetToDefaults(): void {
    // 根据当前网络质量调整为默认参数
    this.adjustNetworkParams();
  }

  /**
   * 销毁控制器
   */
  public dispose(): void {
    this.stopAutoAdjust();
    this.removeAllListeners();
    this.history = [];
    this.qualityHistory = [];
  }
}
