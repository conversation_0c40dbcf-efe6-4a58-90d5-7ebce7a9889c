import { registerAs } from '@nestjs/config';

export interface RAGConfig {
  // 缓存配置
  cache: {
    host: string;
    port: number;
    password?: string;
    ttl: number;
  };

  // 向量数据库配置
  vectorDatabase: {
    type: 'pinecone' | 'weaviate' | 'milvus' | 'chroma';
    endpoint: string;
    apiKey: string;
    indexName: string;
    dimension: number;
    metric: 'cosine' | 'euclidean' | 'dotproduct';
  };

  // LLM配置
  llm: {
    provider: 'openai' | 'azure' | 'anthropic' | 'local';
    apiKey: string;
    endpoint?: string;
    model: string;
    maxTokens: number;
    temperature: number;
  };

  // 嵌入模型配置
  embedding: {
    provider: 'openai' | 'azure' | 'local';
    apiKey: string;
    endpoint?: string;
    model: string;
    dimension: number;
  };

  // RAG配置
  rag: {
    maxDocuments: number;
    similarityThreshold: number;
    contextWindow: number;
    chunkOverlap: number;
  };
}

export default registerAs('rag', (): RAGConfig => ({
  cache: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    ttl: parseInt(process.env.CACHE_TTL || '3600'),
  },

  vectorDatabase: {
    type: (process.env.VECTOR_DB_TYPE as any) || 'chroma',
    endpoint: process.env.VECTOR_DB_ENDPOINT || 'http://localhost:8000',
    apiKey: process.env.VECTOR_DB_API_KEY || '',
    indexName: process.env.VECTOR_DB_INDEX || 'rag-index',
    dimension: parseInt(process.env.VECTOR_DB_DIMENSION || '1536'),
    metric: (process.env.VECTOR_DB_METRIC as any) || 'cosine',
  },

  llm: {
    provider: (process.env.LLM_PROVIDER as any) || 'openai',
    apiKey: process.env.LLM_API_KEY || '',
    endpoint: process.env.LLM_ENDPOINT,
    model: process.env.LLM_MODEL || 'gpt-3.5-turbo',
    maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '2048'),
    temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
  },

  embedding: {
    provider: (process.env.EMBEDDING_PROVIDER as any) || 'openai',
    apiKey: process.env.EMBEDDING_API_KEY || process.env.LLM_API_KEY || '',
    endpoint: process.env.EMBEDDING_ENDPOINT,
    model: process.env.EMBEDDING_MODEL || 'text-embedding-ada-002',
    dimension: parseInt(process.env.EMBEDDING_DIMENSION || '1536'),
  },

  rag: {
    maxDocuments: parseInt(process.env.RAG_MAX_DOCUMENTS || '10'),
    similarityThreshold: parseFloat(process.env.RAG_SIMILARITY_THRESHOLD || '0.7'),
    contextWindow: parseInt(process.env.RAG_CONTEXT_WINDOW || '4000'),
    chunkOverlap: parseInt(process.env.RAG_CHUNK_OVERLAP || '200'),
  },
}));
