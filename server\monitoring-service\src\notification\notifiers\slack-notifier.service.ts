import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

/**
 * Slack通知服务
 * 负责向Slack发送通知消息
 */
@Injectable()
export class SlackNotifierService {
  private readonly logger = new Logger(SlackNotifierService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 发送Slack通知
   */
  async sendNotification(data: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success';
    webhookUrl: string;
    channel?: string;
    username?: string;
    iconEmoji?: string;
    metadata?: any;
  }): Promise<boolean> {
    try {
      const color = this.getColorByType(data.type);
      const emoji = this.getEmojiByType(data.type);

      const payload = {
        channel: data.channel,
        username: data.username || '监控系统',
        icon_emoji: data.iconEmoji || emoji,
        attachments: [
          {
            color,
            title: data.title,
            text: data.message,
            timestamp: Math.floor(Date.now() / 1000),
            fields: data.metadata ? this.formatMetadataFields(data.metadata) : [],
          },
        ],
      };

      const response = await firstValueFrom(
        this.httpService.post(data.webhookUrl, payload)
      );

      if (response.status === 200) {
        this.logger.log(`Slack通知发送成功: ${data.title}`);
        return true;
      } else {
        this.logger.error(`Slack通知发送失败: ${response.status}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`发送Slack通知时出错: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 测试Slack连接
   */
  async testConnection(webhookUrl: string): Promise<boolean> {
    try {
      const testPayload = {
        text: '这是一条测试消息，用于验证Slack通知配置是否正确。',
        username: '监控系统测试',
        icon_emoji: ':robot_face:',
      };

      const response = await firstValueFrom(
        this.httpService.post(webhookUrl, testPayload)
      );

      return response.status === 200;
    } catch (error) {
      this.logger.error(`测试Slack连接失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 根据类型获取颜色
   */
  private getColorByType(type: string): string {
    const colors = {
      info: '#36a64f',      // 绿色
      warning: '#ff9500',   // 橙色
      error: '#ff0000',     // 红色
      success: '#36a64f',   // 绿色
    };

    return colors[type] || colors.info;
  }

  /**
   * 根据类型获取表情符号
   */
  private getEmojiByType(type: string): string {
    const emojis = {
      info: ':information_source:',
      warning: ':warning:',
      error: ':x:',
      success: ':white_check_mark:',
    };

    return emojis[type] || emojis.info;
  }

  /**
   * 格式化元数据字段
   */
  private formatMetadataFields(metadata: any): any[] {
    const fields = [];

    for (const [key, value] of Object.entries(metadata)) {
      fields.push({
        title: key,
        value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        short: true,
      });
    }

    return fields;
  }
}
