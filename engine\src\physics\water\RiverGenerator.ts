/**
 * 河流生成器
 * 用于生成河流水体
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent, WaterBodyType } from './WaterBodyComponent';
import { TerrainComponent } from '../../terrain/components/TerrainComponent';
import { Debug } from '../../utils/Debug';

/**
 * 河流路径点
 */
export interface RiverPathPoint {
  /** 位置 */
  position: THREE.Vector3;
  /** 宽度 */
  width: number;
  /** 深度 */
  depth: number;
  /** 流速 */
  flowSpeed?: number;
}

/**
 * 河流生成配置
 */
export interface RiverGeneratorConfig {
  /** 河流路径点 */
  pathPoints: RiverPathPoint[];
  /** 河流分段数 */
  segments?: number;
  /** 是否跟随地形 */
  followTerrain?: boolean;
  /** 地形偏移 */
  terrainOffset?: number;
  /** 是否使用样条曲线 */
  useSpline?: boolean;
  /** 是否生成河岸 */
  generateRiverBanks?: boolean;
  /** 河岸高度 */
  riverBankHeight?: number;
  /** 河岸宽度 */
  riverBankWidth?: number;
  /** 是否生成河床 */
  generateRiverBed?: boolean;
  /** 河床材质 */
  riverBedMaterial?: any;
  /** 是否生成水流粒子 */
  generateFlowParticles?: boolean;
  /** 水流粒子数量 */
  flowParticleCount?: number;
}

/**
 * 河流生成器
 */
export class RiverGenerator {
  /** 世界 */
  private world: World;
  /** 配置 */
  private config: RiverGeneratorConfig;
  /** 地形组件 */
  private terrainComponent: TerrainComponent | null = null;
  /** 河流实体 */
  private riverEntity: Entity | null = null;
  /** 河流水体组件 */
  private riverWaterBody: WaterBodyComponent | null = null;
  /** 河流路径点 */
  private pathPoints: RiverPathPoint[] = [];
  /** 河流路径 */
  private path: THREE.CurvePath<THREE.Vector3> | null = null;
  /** 河流几何体 */
  private riverGeometry: THREE.BufferGeometry | null = null;
  /** 河岸几何体 */
  private riverBankGeometry: THREE.BufferGeometry | null = null;
  /** 河床几何体 */
  private riverBedGeometry: THREE.BufferGeometry | null = null;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: RiverGeneratorConfig) {
    this.world = world;
    this.config = {
      segments: config.segments || 100,
      followTerrain: config.followTerrain !== undefined ? config.followTerrain : true,
      terrainOffset: config.terrainOffset || 0.5,
      useSpline: config.useSpline !== undefined ? config.useSpline : true,
      generateRiverBanks: config.generateRiverBanks !== undefined ? config.generateRiverBanks : true,
      riverBankHeight: config.riverBankHeight || 1.0,
      riverBankWidth: config.riverBankWidth || 2.0,
      generateRiverBed: config.generateRiverBed !== undefined ? config.generateRiverBed : true,
      generateFlowParticles: config.generateFlowParticles !== undefined ? config.generateFlowParticles : true,
      flowParticleCount: config.flowParticleCount || 1000,
      pathPoints: config.pathPoints || []
    };

    // 初始化路径点
    this.pathPoints = this.config.pathPoints;
  }

  /**
   * 设置地形组件
   * @param terrainComponent 地形组件
   */
  public setTerrainComponent(terrainComponent: TerrainComponent): void {
    this.terrainComponent = terrainComponent;
  }

  /**
   * 生成河流
   * @returns 河流实体
   */
  public generate(): Entity {
    // 创建河流实体
    this.riverEntity = new Entity('河流');

    // 创建河流路径
    this.createRiverPath();

    // 创建河流几何体
    this.createRiverGeometry();

    // 创建河流水体
    this.createRiverWaterBody();

    // 如果需要生成河岸，创建河岸
    if (this.config.generateRiverBanks) {
      this.createRiverBanks();
    }

    // 如果需要生成河床，创建河床
    if (this.config.generateRiverBed) {
      this.createRiverBed();
    }

    // 如果需要生成水流粒子，创建水流粒子
    if (this.config.generateFlowParticles) {
      this.createFlowParticles();
    }

    // 添加到世界
    this.world.addEntity(this.riverEntity!);

    return this.riverEntity!;
  }

  /**
   * 创建河流路径
   */
  private createRiverPath(): void {
    // 如果路径点少于2个，无法创建路径
    if (this.pathPoints.length < 2) {
      Debug.error('RiverGenerator', '路径点数量不足，无法创建河流路径');
      return;
    }

    // 创建路径
    this.path = new THREE.CurvePath<THREE.Vector3>();

    // 如果使用样条曲线，创建样条曲线
    if (this.config.useSpline) {
      // 提取位置点
      const points = this.pathPoints.map(point => point.position);

      // 创建样条曲线
      const spline = new THREE.CatmullRomCurve3(points);

      // 添加到路径
      this.path.add(spline);
    } else {
      // 否则，创建线段
      for (let i = 0; i < this.pathPoints.length - 1; i++) {
        const start = this.pathPoints[i].position;
        const end = this.pathPoints[i + 1].position;

        // 创建线段
        const line = new THREE.LineCurve3(start, end);

        // 添加到路径
        this.path.add(line);
      }
    }
  }

  /**
   * 创建河流几何体
   */
  private createRiverGeometry(): void {
    if (!this.path) {
      Debug.error('RiverGenerator', '河流路径未创建，无法创建河流几何体');
      return;
    }

    // 创建几何体
    const geometry = new THREE.BufferGeometry();

    // 获取路径点
    const pathPoints = this.path.getPoints(this.config.segments!);

    // 创建顶点和索引
    const vertices: number[] = [];
    const indices: number[] = [];
    const uvs: number[] = [];

    // 计算路径长度（暂时未使用）
    // const pathLength = this.path.getLength();

    // 遍历路径点
    for (let i = 0; i < pathPoints.length; i++) {
      const t = i / (pathPoints.length - 1);
      const point = pathPoints[i];

      // 获取路径点对应的宽度和深度
      const width = this.getWidthAtT(t);
      // const depth = this.getDepthAtT(t); // 暂时未使用

      // 计算路径切线
      const tangent = this.path.getTangent(t);

      // 计算路径法线（假设路径在xz平面上）
      const normal = new THREE.Vector3(-tangent.z, 0, tangent.x).normalize();

      // 计算左右两侧点
      const left = point.clone().add(normal.clone().multiplyScalar(width / 2));
      const right = point.clone().add(normal.clone().multiplyScalar(-width / 2));

      // 如果需要跟随地形，调整高度
      if (this.config.followTerrain && this.terrainComponent) {
        const leftHeight = this.getTerrainHeight(left.x, left.z);
        const rightHeight = this.getTerrainHeight(right.x, right.z);

        if (leftHeight !== null) {
          left.y = leftHeight + this.config.terrainOffset!;
        }

        if (rightHeight !== null) {
          right.y = rightHeight + this.config.terrainOffset!;
        }
      }

      // 添加顶点
      vertices.push(left.x, left.y, left.z);
      vertices.push(right.x, right.y, right.z);

      // 添加UV
      const u = t;
      uvs.push(0, u);
      uvs.push(1, u);

      // 添加索引（创建三角形）
      if (i < pathPoints.length - 1) {
        const base = i * 2;
        indices.push(base, base + 1, base + 2);
        indices.push(base + 1, base + 3, base + 2);
      }
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    // 保存几何体
    this.riverGeometry = geometry;
  }

  /**
   * 创建河流水体
   */
  private createRiverWaterBody(): void {
    if (!this.riverGeometry) {
      Debug.error('RiverGenerator', '河流几何体未创建，无法创建河流水体');
      return;
    }

    // 创建河流水体组件
    this.riverWaterBody = new WaterBodyComponent(this.riverEntity!, {
      type: WaterBodyType.RIVER,
      enableFlow: true,
      flowDirection: this.getAverageFlowDirection(),
      flowSpeed: this.getAverageFlowSpeed()
    });

    // 注意：WaterBodyComponent会自动处理几何体，这里不需要手动设置

    // 添加到实体
    this.riverEntity!.addComponent(this.riverWaterBody);
  }

  /**
   * 创建河岸
   */
  private createRiverBanks(): void {
    if (!this.path || !this.riverGeometry) {
      Debug.error('RiverGenerator', '河流路径或几何体未创建，无法创建河岸');
      return;
    }

    // 创建几何体
    const geometry = new THREE.BufferGeometry();

    // 获取路径点
    const pathPoints = this.path.getPoints(this.config.segments!);

    // 创建顶点和索引
    const vertices: number[] = [];
    const indices: number[] = [];
    const uvs: number[] = [];

    // 遍历路径点
    for (let i = 0; i < pathPoints.length; i++) {
      const t = i / (pathPoints.length - 1);
      const point = pathPoints[i];

      // 获取路径点对应的宽度
      const width = this.getWidthAtT(t);
      const bankWidth = this.config.riverBankWidth!;

      // 计算路径切线
      const tangent = this.path.getTangent(t);

      // 计算路径法线（假设路径在xz平面上）
      const normal = new THREE.Vector3(-tangent.z, 0, tangent.x).normalize();

      // 计算河岸内侧点和外侧点
      const leftInner = point.clone().add(normal.clone().multiplyScalar(width / 2));
      const leftOuter = point.clone().add(normal.clone().multiplyScalar(width / 2 + bankWidth));
      const rightInner = point.clone().add(normal.clone().multiplyScalar(-width / 2));
      const rightOuter = point.clone().add(normal.clone().multiplyScalar(-width / 2 - bankWidth));

      // 如果需要跟随地形，调整高度
      if (this.config.followTerrain && this.terrainComponent) {
        const leftInnerHeight = this.getTerrainHeight(leftInner.x, leftInner.z);
        const leftOuterHeight = this.getTerrainHeight(leftOuter.x, leftOuter.z);
        const rightInnerHeight = this.getTerrainHeight(rightInner.x, rightInner.z);
        const rightOuterHeight = this.getTerrainHeight(rightOuter.x, rightOuter.z);

        if (leftInnerHeight !== null) {
          leftInner.y = leftInnerHeight + this.config.terrainOffset!;
        }

        if (leftOuterHeight !== null) {
          leftOuter.y = leftOuterHeight;
        }

        if (rightInnerHeight !== null) {
          rightInner.y = rightInnerHeight + this.config.terrainOffset!;
        }

        if (rightOuterHeight !== null) {
          rightOuter.y = rightOuterHeight;
        }
      }

      // 添加河岸高度
      leftInner.y += this.config.riverBankHeight!;
      leftOuter.y += this.config.riverBankHeight!;
      rightInner.y += this.config.riverBankHeight!;
      rightOuter.y += this.config.riverBankHeight!;

      // 添加顶点（左侧河岸）
      vertices.push(leftInner.x, leftInner.y, leftInner.z);
      vertices.push(leftOuter.x, leftOuter.y, leftOuter.z);

      // 添加顶点（右侧河岸）
      vertices.push(rightInner.x, rightInner.y, rightInner.z);
      vertices.push(rightOuter.x, rightOuter.y, rightOuter.z);

      // 添加UV
      const u = t;
      uvs.push(0, u);
      uvs.push(1, u);
      uvs.push(0, u);
      uvs.push(1, u);

      // 添加索引（创建三角形）
      if (i < pathPoints.length - 1) {
        // 左侧河岸
        const leftBase = i * 4;
        indices.push(leftBase, leftBase + 1, leftBase + 4);
        indices.push(leftBase + 1, leftBase + 5, leftBase + 4);

        // 右侧河岸
        const rightBase = i * 4 + 2;
        indices.push(rightBase, rightBase + 1, rightBase + 4);
        indices.push(rightBase + 1, rightBase + 5, rightBase + 4);
      }
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    // 保存几何体
    this.riverBankGeometry = geometry;

    // 创建河岸材质
    const material = new THREE.MeshStandardMaterial({
      color: 0x8B4513,
      roughness: 0.8,
      metalness: 0.1
    });

    // 创建河岸网格
    const mesh = new THREE.Mesh(this.riverBankGeometry, material);

    // 添加到实体的Transform组件
    this.riverEntity!.getTransform().getObject3D().add(mesh);
  }

  /**
   * 创建河床
   */
  private createRiverBed(): void {
    if (!this.path || !this.riverGeometry) {
      Debug.error('RiverGenerator', '河流路径或几何体未创建，无法创建河床');
      return;
    }

    // 创建几何体
    const geometry = new THREE.BufferGeometry();

    // 获取路径点
    const pathPoints = this.path.getPoints(this.config.segments!);

    // 创建顶点和索引
    const vertices: number[] = [];
    const indices: number[] = [];
    const uvs: number[] = [];

    // 遍历路径点
    for (let i = 0; i < pathPoints.length; i++) {
      const t = i / (pathPoints.length - 1);
      const point = pathPoints[i];

      // 获取路径点对应的宽度和深度
      const width = this.getWidthAtT(t);
      const depth = this.getDepthAtT(t);

      // 计算路径切线
      const tangent = this.path.getTangent(t);

      // 计算路径法线（假设路径在xz平面上）
      const normal = new THREE.Vector3(-tangent.z, 0, tangent.x).normalize();

      // 计算左右两侧点
      const left = point.clone().add(normal.clone().multiplyScalar(width / 2));
      const right = point.clone().add(normal.clone().multiplyScalar(-width / 2));

      // 如果需要跟随地形，调整高度
      if (this.config.followTerrain && this.terrainComponent) {
        const leftHeight = this.getTerrainHeight(left.x, left.z);
        const rightHeight = this.getTerrainHeight(right.x, right.z);

        if (leftHeight !== null) {
          left.y = leftHeight + this.config.terrainOffset!;
        }

        if (rightHeight !== null) {
          right.y = rightHeight + this.config.terrainOffset!;
        }
      }

      // 添加深度
      left.y -= depth;
      right.y -= depth;

      // 添加顶点
      vertices.push(left.x, left.y, left.z);
      vertices.push(right.x, right.y, right.z);

      // 添加UV
      const u = t;
      uvs.push(0, u);
      uvs.push(1, u);

      // 添加索引（创建三角形）
      if (i < pathPoints.length - 1) {
        const base = i * 2;
        indices.push(base, base + 1, base + 2);
        indices.push(base + 1, base + 3, base + 2);
      }
    }

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    // 保存几何体
    this.riverBedGeometry = geometry;

    // 创建河床材质
    const material = this.config.riverBedMaterial || new THREE.MeshStandardMaterial({
      color: 0x7B5E3D,
      roughness: 0.9,
      metalness: 0.1
    });

    // 创建河床网格
    const mesh = new THREE.Mesh(this.riverBedGeometry, material);

    // 添加到实体的Transform组件
    this.riverEntity!.getTransform().getObject3D().add(mesh);
  }

  /**
   * 创建水流粒子
   */
  private createFlowParticles(): void {
    if (!this.path || !this.riverWaterBody) {
      Debug.error('RiverGenerator', '河流路径或水体未创建，无法创建水流粒子');
      return;
    }

    // 创建粒子系统
    const particleSystem = new THREE.Points(
      new THREE.BufferGeometry(),
      new THREE.PointsMaterial({
        color: 0xFFFFFF,
        size: 0.1,
        transparent: true,
        opacity: 0.6,
        map: new THREE.TextureLoader().load('/assets/textures/particle.png'),
        blending: THREE.AdditiveBlending,
        depthWrite: false
      })
    );

    // 创建粒子位置
    const positions: number[] = [];
    const velocities: number[] = [];
    const lifetimes: number[] = [];

    // 创建粒子
    for (let i = 0; i < this.config.flowParticleCount!; i++) {
      // 随机选择路径上的点
      const t = Math.random();
      const point = this.path.getPoint(t);

      // 获取路径点对应的宽度
      const width = this.getWidthAtT(t);

      // 计算路径切线
      const tangent = this.path.getTangent(t);

      // 计算路径法线（假设路径在xz平面上）
      const normal = new THREE.Vector3(-tangent.z, 0, tangent.x).normalize();

      // 随机偏移
      const offset = (Math.random() - 0.5) * width * 0.8;
      const offsetPoint = point.clone().add(normal.clone().multiplyScalar(offset));

      // 如果需要跟随地形，调整高度
      if (this.config.followTerrain && this.terrainComponent) {
        const height = this.getTerrainHeight(offsetPoint.x, offsetPoint.z);

        if (height !== null) {
          offsetPoint.y = height + this.config.terrainOffset! + Math.random() * 0.1;
        }
      }

      // 添加位置
      positions.push(offsetPoint.x, offsetPoint.y, offsetPoint.z);

      // 添加速度（沿着路径切线方向）
      const speed = this.getFlowSpeedAtT(t);
      velocities.push(tangent.x * speed, tangent.y * speed, tangent.z * speed);

      // 添加生命周期
      lifetimes.push(Math.random() * 5 + 5);
    }

    // 设置几何体属性
    particleSystem.geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
    particleSystem.geometry.setAttribute('velocity', new THREE.Float32BufferAttribute(velocities, 3));
    particleSystem.geometry.setAttribute('lifetime', new THREE.Float32BufferAttribute(lifetimes, 1));

    // 添加到实体的Transform组件
    this.riverEntity!.getTransform().getObject3D().add(particleSystem);

    // 添加更新函数（暂时注释，因为没有回调机制）
    /*
    const updateParticles = (deltaTime: number) => {
      const positions = particleSystem.geometry.getAttribute('position');
      const velocities = particleSystem.geometry.getAttribute('velocity');
      const lifetimes = particleSystem.geometry.getAttribute('lifetime');

      for (let i = 0; i < positions.count; i++) {
        // 更新位置
        positions.setXYZ(
          i,
          positions.getX(i) + velocities.getX(i) * deltaTime,
          positions.getY(i) + velocities.getY(i) * deltaTime,
          positions.getZ(i) + velocities.getZ(i) * deltaTime
        );

        // 更新生命周期
        lifetimes.setX(i, lifetimes.getX(i) - deltaTime);

        // 如果生命周期结束，重置粒子
        if (lifetimes.getX(i) <= 0) {
          // 随机选择路径上的点
          const t = Math.random();
          const point = this.path!.getPoint(t);

          // 获取路径点对应的宽度
          const width = this.getWidthAtT(t);

          // 计算路径切线
          const tangent = this.path!.getTangent(t);

          // 计算路径法线（假设路径在xz平面上）
          const normal = new THREE.Vector3(-tangent.z, 0, tangent.x).normalize();

          // 随机偏移
          const offset = (Math.random() - 0.5) * width * 0.8;
          const offsetPoint = point.clone().add(normal.clone().multiplyScalar(offset));

          // 如果需要跟随地形，调整高度
          if (this.config.followTerrain && this.terrainComponent) {
            const height = this.getTerrainHeight(offsetPoint.x, offsetPoint.z);

            if (height !== null) {
              offsetPoint.y = height + this.config.terrainOffset! + Math.random() * 0.1;
            }
          }

          // 重置位置
          positions.setXYZ(i, offsetPoint.x, offsetPoint.y, offsetPoint.z);

          // 重置速度
          const speed = this.getFlowSpeedAtT(t);
          velocities.setXYZ(i, tangent.x * speed, tangent.y * speed, tangent.z * speed);

          // 重置生命周期
          lifetimes.setX(i, Math.random() * 5 + 5);
        }
      }

      // 更新几何体
      positions.needsUpdate = true;
      lifetimes.needsUpdate = true;
    };
    */

    // TODO: 添加更新函数到水体组件（需要实现更新回调机制）
    // this.riverWaterBody.addUpdateCallback(updateParticles);
  }

  /**
   * 获取指定位置的地形高度
   * @param x X坐标
   * @param z Z坐标
   * @returns 地形高度，如果无法获取则返回null
   */
  private getTerrainHeight(x: number, z: number): number | null {
    if (!this.terrainComponent) {
      return null;
    }

    // 调用地形组件的获取高度方法
    return this.terrainComponent.getHeight(x, z);
  }

  /**
   * 获取指定t值处的宽度
   * @param t 路径参数（0-1）
   * @returns 宽度
   */
  private getWidthAtT(t: number): number {
    // 如果路径点少于2个，返回默认宽度
    if (this.pathPoints.length < 2) {
      return 5.0;
    }

    // 计算索引
    const index = Math.floor(t * (this.pathPoints.length - 1));
    const nextIndex = Math.min(index + 1, this.pathPoints.length - 1);

    // 计算插值因子
    const factor = t * (this.pathPoints.length - 1) - index;

    // 获取宽度
    const width1 = this.pathPoints[index].width;
    const width2 = this.pathPoints[nextIndex].width;

    // 线性插值
    return width1 * (1 - factor) + width2 * factor;
  }

  /**
   * 获取指定t值处的深度
   * @param t 路径参数（0-1）
   * @returns 深度
   */
  private getDepthAtT(t: number): number {
    // 如果路径点少于2个，返回默认深度
    if (this.pathPoints.length < 2) {
      return 1.0;
    }

    // 计算索引
    const index = Math.floor(t * (this.pathPoints.length - 1));
    const nextIndex = Math.min(index + 1, this.pathPoints.length - 1);

    // 计算插值因子
    const factor = t * (this.pathPoints.length - 1) - index;

    // 获取深度
    const depth1 = this.pathPoints[index].depth;
    const depth2 = this.pathPoints[nextIndex].depth;

    // 线性插值
    return depth1 * (1 - factor) + depth2 * factor;
  }

  /**
   * 获取指定t值处的流速
   * @param t 路径参数（0-1）
   * @returns 流速
   */
  private getFlowSpeedAtT(t: number): number {
    // 如果路径点少于2个，返回默认流速
    if (this.pathPoints.length < 2) {
      return 1.0;
    }

    // 计算索引
    const index = Math.floor(t * (this.pathPoints.length - 1));
    const nextIndex = Math.min(index + 1, this.pathPoints.length - 1);

    // 计算插值因子
    const factor = t * (this.pathPoints.length - 1) - index;

    // 获取流速
    const speed1 = this.pathPoints[index].flowSpeed || 1.0;
    const speed2 = this.pathPoints[nextIndex].flowSpeed || 1.0;

    // 线性插值
    return speed1 * (1 - factor) + speed2 * factor;
  }

  /**
   * 获取平均流向
   * @returns 平均流向
   */
  private getAverageFlowDirection(): THREE.Vector3 {
    if (!this.path) {
      return new THREE.Vector3(0, 0, 1);
    }

    // 获取路径中点的切线
    const tangent = this.path.getTangent(0.5);

    return tangent;
  }

  /**
   * 获取平均流速
   * @returns 平均流速
   */
  private getAverageFlowSpeed(): number {
    // 如果路径点少于2个，返回默认流速
    if (this.pathPoints.length < 2) {
      return 1.0;
    }

    // 计算平均流速
    let totalSpeed = 0;
    let count = 0;

    for (const point of this.pathPoints) {
      if (point.flowSpeed !== undefined) {
        totalSpeed += point.flowSpeed;
        count++;
      }
    }

    return count > 0 ? totalSpeed / count : 1.0;
  }
}
