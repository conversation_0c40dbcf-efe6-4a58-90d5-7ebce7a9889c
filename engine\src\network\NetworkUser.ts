/**
 * 网络用户
 * 定义网络用户的结构
 */

/**
 * 网络用户状态
 */
export enum NetworkUserState {
  /** 离线 */
  OFFLINE = 'offline',
  /** 在线 */
  ONLINE = 'online',
  /** 忙碌 */
  BUSY = 'busy',
  /** 离开 */
  AWAY = 'away',
  /** 隐身 */
  INVISIBLE = 'invisible',
}

/**
 * 网络用户角色
 */
export enum NetworkUserRole {
  /** 访客 */
  GUEST = 'guest',
  /** 用户 */
  USER = 'user',
  /** 管理员 */
  ADMIN = 'admin',
  /** 超级管理员 */
  SUPER_ADMIN = 'super_admin',
}

/**
 * 网络用户
 */
export interface NetworkUser {
  /** 用户ID */
  userId: string;
  
  /** 用户名 */
  username: string;
  
  /** 显示名称 */
  displayName?: string;
  
  /** 头像URL */
  avatarUrl?: string;
  
  /** 用户状态 */
  state?: NetworkUserState;
  
  /** 用户角色 */
  role?: NetworkUserRole;
  
  /** 用户加入时间 */
  joinTime: number;
  
  /** 用户最后活动时间 */
  lastActiveTime?: number;
  
  /** 用户IP地址 */
  ipAddress?: string;
  
  /** 用户设备信息 */
  deviceInfo?: string;
  
  /** 用户位置信息 */
  location?: {
    x: number;
    y: number;
    z: number;
  };
  
  /** 用户旋转信息 */
  rotation?: {
    x: number;
    y: number;
    z: number;
    w: number;
  };
  
  /** 用户缩放信息 */
  scale?: {
    x: number;
    y: number;
    z: number;
  };
  
  /** 用户速度信息 */
  velocity?: {
    x: number;
    y: number;
    z: number;
  };
  
  /** 用户加速度信息 */
  acceleration?: {
    x: number;
    y: number;
    z: number;
  };
  
  /** 用户输入状态 */
  inputState?: {
    [key: string]: any;
  };
  
  /** 用户自定义数据 */
  customData?: {
    [key: string]: any;
  };
  
  /** 用户元数据 */
  metadata?: {
    [key: string]: any;
  };
}
