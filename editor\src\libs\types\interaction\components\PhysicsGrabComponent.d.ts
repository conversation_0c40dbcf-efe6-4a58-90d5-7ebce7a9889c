/**
 * 物理抓取组件
 * 用于处理物理对象的抓取
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector3, Quaternion } from 'three';
import { BodyType } from '../../physics/types/BodyType';
export declare enum Hand {
    LEFT = "left",
    RIGHT = "right"
}
/**
 * 物理抓取组件配置
 */
export interface PhysicsGrabComponentConfig {
    /** 抓取力 */
    grabForce?: number;
    /** 抓取阻尼 */
    grabDamping?: number;
    /** 是否保持原始物理类型 */
    keepOriginalBodyType?: boolean;
    /** 抓取时的物理类型 */
    grabBodyType?: BodyType;
}
/**
 * 物理抓取组件
 */
export declare class PhysicsGrabComponent extends Component {
    /** 组件类型 */
    static readonly TYPE: string;
    /** 抓取力 */
    private _grabForce;
    /** 抓取阻尼 */
    private _grabDamping;
    /** 是否保持原始物理类型 */
    private _keepOriginalBodyType;
    /** 抓取时的物理类型 */
    private _grabBodyType;
    /** 原始物理类型 */
    private _originalBodyType?;
    /** 抓取者 */
    private _grabber?;
    /** 抓取手 */
    private _hand?;
    /** 抓取偏移 */
    private _grabOffset;
    /** 抓取旋转偏移 */
    private _grabRotationOffset;
    /** 是否被抓取 */
    private _isGrabbed;
    /**
     * 构造函数
     * @param config 组件配置
     */
    constructor(config?: PhysicsGrabComponentConfig);
    /**
     * 获取抓取力
     */
    get grabForce(): number;
    /**
     * 设置抓取力
     */
    set grabForce(value: number);
    /**
     * 获取抓取阻尼
     */
    get grabDamping(): number;
    /**
     * 设置抓取阻尼
     */
    set grabDamping(value: number);
    /**
     * 获取是否保持原始物理类型
     */
    get keepOriginalBodyType(): boolean;
    /**
     * 设置是否保持原始物理类型
     */
    set keepOriginalBodyType(value: boolean);
    /**
     * 获取抓取时的物理类型
     */
    get grabBodyType(): BodyType;
    /**
     * 设置抓取时的物理类型
     */
    set grabBodyType(value: BodyType);
    /**
     * 获取是否被抓取
     */
    get isGrabbed(): boolean;
    /**
     * 获取抓取者
     */
    get grabber(): Entity | undefined;
    /**
     * 获取抓取手
     */
    get hand(): Hand | undefined;
    /**
     * 获取抓取偏移
     */
    get grabOffset(): Vector3;
    /**
     * 获取抓取旋转偏移
     */
    get grabRotationOffset(): Quaternion;
    /**
     * 开始抓取
     * @param grabber 抓取者
     * @param hand 抓取手
     * @returns 是否成功
     */
    startGrab(grabber: Entity, hand: Hand): boolean;
    /**
     * 结束抓取
     * @returns 是否成功
     */
    endGrab(): boolean;
    /**
     * 计算抓取偏移
     * @param grabber 抓取者
     * @param hand 抓取手
     */
    private calculateGrabOffset;
    /**
     * 更新抓取
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
}
