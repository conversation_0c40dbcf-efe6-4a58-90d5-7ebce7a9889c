/**
 * 项目控制器
 */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ProjectsService } from './projects.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('项目')
@Controller('projects')
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建项目' })
  @ApiResponse({ status: 201, description: '项目创建成功' })
  async create(@Request() req, @Body() createProjectDto: any) {
    return this.projectsService.create(req.user.id, createProjectDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有项目' })
  @ApiResponse({ status: 200, description: '返回所有项目' })
  async findAll(@Request() req) {
    return this.projectsService.findAll(req.user.id);
  }

  @Get('my')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取我的项目' })
  @ApiResponse({ status: 200, description: '返回用户拥有的项目' })
  async findMyProjects(@Request() req) {
    return this.projectsService.findUserProjects(req.user.id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取项目' })
  @ApiResponse({ status: 200, description: '返回项目信息' })
  async findOne(@Param('id') id: string, @Request() req) {
    return this.projectsService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新项目' })
  @ApiResponse({ status: 200, description: '项目更新成功' })
  async update(@Param('id') id: string, @Request() req, @Body() updateProjectDto: any) {
    return this.projectsService.update(id, req.user.id, updateProjectDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除项目' })
  @ApiResponse({ status: 204, description: '项目删除成功' })
  async remove(@Param('id') id: string, @Request() req) {
    return this.projectsService.remove(id, req.user.id);
  }

  @Post(':id/members')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '添加项目成员' })
  @ApiResponse({ status: 201, description: '项目成员添加成功' })
  async addMember(@Param('id') id: string, @Request() req, @Body() addMemberDto: any) {
    return this.projectsService.addMember(id, req.user.id, addMemberDto);
  }

  @Patch(':id/members/:memberId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新项目成员' })
  @ApiResponse({ status: 200, description: '项目成员更新成功' })
  async updateMember(
    @Param('id') id: string,
    @Param('memberId') memberId: string,
    @Request() req,
    @Body() updateMemberDto: any,
  ) {
    return this.projectsService.updateMember(id, memberId, req.user.id, updateMemberDto);
  }

  @Delete(':id/members/:memberId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除项目成员' })
  @ApiResponse({ status: 204, description: '项目成员删除成功' })
  async removeMember(@Param('id') id: string, @Param('memberId') memberId: string, @Request() req) {
    return this.projectsService.removeMember(id, memberId, req.user.id);
  }

  @Post(':id/settings')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建项目设置' })
  @ApiResponse({ status: 201, description: '项目设置创建成功' })
  async createSetting(@Param('id') id: string, @Body() createSettingDto: any) {
    return this.projectsService.createSetting(id, createSettingDto);
  }

  @Get(':id/settings/:key')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取项目设置' })
  @ApiResponse({ status: 200, description: '返回项目设置' })
  async getSetting(@Param('id') id: string, @Param('key') key: string) {
    return this.projectsService.getSetting(id, key);
  }

  @Delete(':id/settings/:key')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除项目设置' })
  @ApiResponse({ status: 204, description: '项目设置删除成功' })
  async removeSetting(@Param('id') id: string, @Param('key') key: string) {
    return this.projectsService.removeSetting(id, key);
  }
}
