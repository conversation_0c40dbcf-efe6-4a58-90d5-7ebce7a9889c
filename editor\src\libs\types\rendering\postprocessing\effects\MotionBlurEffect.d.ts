/**
 * 运动模糊效果
 * 增加动态场景的真实感
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 运动模糊效果选项
 */
export interface MotionBlurEffectOptions extends PostProcessingEffectOptions {
    /** 强度 */
    intensity?: number;
    /** 采样数 */
    samples?: number;
    /** 是否使用相机运动 */
    useCameraMotion?: boolean;
    /** 是否使用对象运动 */
    useObjectMotion?: boolean;
}
/**
 * 运动模糊效果
 */
export declare class MotionBlurEffect extends PostProcessingEffect {
    /** 强度 */
    private intensity;
    /** 采样数 */
    private samples;
    /** 是否使用相机运动 */
    private useCameraMotion;
    /** 是否使用对象运动 */
    private useObjectMotion;
    /** 运动模糊通道 */
    private motionBlurPass;
    /** 场景 */
    private scene;
    /** 相机 */
    private camera;
    /** 上一帧相机位置 */
    private previousCameraPosition;
    /** 上一帧相机四元数 */
    private previousCameraQuaternion;
    /** 上一帧相机投影矩阵 */
    private previousCameraProjectionMatrix;
    /** 上一帧相机视图矩阵 */
    private previousCameraViewMatrix;
    /** 速度缓冲区 */
    private velocityRenderTarget;
    /** 速度材质 */
    private velocityMaterial;
    /** 对象上一帧矩阵映射 */
    private previousMatrixMap;
    /**
     * 创建运动模糊效果
     * @param options 运动模糊效果选项
     */
    constructor(options?: MotionBlurEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 设置场景和相机
     * @param scene 场景
     * @param camera 相机
     */
    setSceneAndCamera(scene: THREE.Scene, camera: THREE.Camera): void;
    /**
     * 设置强度
     * @param intensity 强度
     */
    setIntensity(intensity: number): void;
    /**
     * 获取强度
     * @returns 强度
     */
    getIntensity(): number;
    /**
     * 设置采样数
     * @param samples 采样数
     */
    setSamples(samples: number): void;
    /**
     * 获取采样数
     * @returns 采样数
     */
    getSamples(): number;
    /**
     * 设置是否使用相机运动
     * @param use 是否使用
     */
    setUseCameraMotion(use: boolean): void;
    /**
     * 获取是否使用相机运动
     * @returns 是否使用
     */
    isUseCameraMotion(): boolean;
    /**
     * 设置是否使用对象运动
     * @param use 是否使用
     */
    setUseObjectMotion(use: boolean): void;
    /**
     * 获取是否使用对象运动
     * @returns 是否使用
     */
    isUseObjectMotion(): boolean;
    /**
     * 更新速度缓冲区
     */
    private updateVelocityBuffer;
    /**
     * 更新效果
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
    /**
     * 销毁效果
     */
    dispose(): void;
}
