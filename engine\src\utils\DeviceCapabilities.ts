/**
 * 设备能力检测
 * 用于检测设备的性能和功能支持情况
 */
import * as THREE from 'three';

/**
 * 设备性能级别枚举
 */
export enum DevicePerformanceLevel {
  /** 低性能 */
  LOW = 0,
  /** 中等性能 */
  MEDIUM = 1,
  /** 高性能 */
  HIGH = 2
}

/**
 * 设备能力检测配置接口
 */
export interface DeviceCapabilitiesOptions {
  /** 是否强制使用低性能模式 */
  forceLowPerformance?: boolean;
  /** 是否强制使用高性能模式 */
  forceHighPerformance?: boolean;
  /** 是否启用电池监控 */
  enableBatteryMonitoring?: boolean;
  /** 是否启用温度监控 */
  enableTemperatureMonitoring?: boolean;
  /** 是否启用网络监控 */
  enableNetworkMonitoring?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 是否启用自动性能调整 */
  enableAutoPerformanceAdjustment?: boolean;
  /** 低电量阈值（百分比） */
  lowBatteryThreshold?: number;
  /** 高温阈值（摄氏度） */
  highTemperatureThreshold?: number;
  /** 目标帧率 */
  targetFPS?: number;
  /** 最小可接受帧率 */
  minAcceptableFPS?: number;
}

/**
 * 设备能力检测类
 * 单例模式
 */
export class DeviceCapabilities {
  /** 单例实例 */
  private static instance: DeviceCapabilities;

  /** 设备性能级别 */
  private performanceLevel: DevicePerformanceLevel;

  /** 是否支持WebGL2 */
  private supportsWebGL2: boolean;

  /** 是否支持浮点纹理 */
  private supportsFloatTextures: boolean;

  /** 是否支持各向异性过滤 */
  private supportsAnisotropy: boolean;

  /** 是否支持实例化渲染 */
  private supportsInstancing: boolean;

  /** 是否支持压缩纹理 */
  private supportsCompressedTextures: boolean;

  /** 是否支持HDR */
  private supportsHDR: boolean;

  /** 是否支持阴影 */
  private supportsShadows: boolean;

  /** 最大纹理大小 */
  private maxTextureSize: number;

  /** 最大各向异性级别 */
  private maxAnisotropy: number;

  /** 是否是移动设备 */
  private isMobile: boolean;

  /** 是否是VR/AR设备 */
  private isXRDevice: boolean;

  /** 是否强制使用低性能模式 */
  private forceLowPerformance: boolean;

  /** 是否强制使用高性能模式 */
  private forceHighPerformance: boolean;

  /** 是否启用电池监控 */
  private enableBatteryMonitoring: boolean;

  /** 是否启用温度监控 */
  private enableTemperatureMonitoring: boolean;

  /** 是否启用网络监控 */
  private enableNetworkMonitoring: boolean;

  /** 是否启用性能监控 */
  private enablePerformanceMonitoring: boolean;

  /** 是否启用自动性能调整 */
  private enableAutoPerformanceAdjustment: boolean;

  /** 低电量阈值（百分比） */
  private lowBatteryThreshold: number;

  /** 高温阈值（摄氏度） */
  private highTemperatureThreshold: number;

  /** 目标帧率 */
  private targetFPS: number;

  /** 最小可接受帧率 */
  private minAcceptableFPS: number;

  /** 电池电量 */
  private batteryLevel: number = 100;

  /** 电池是否正在充电 */
  private batteryCharging: boolean = false;

  /** 设备温度 */
  private temperature: number = 25;

  /** 当前帧率 */
  private currentFPS: number = 60;

  /** CPU使用率 */
  private cpuUsage: number = 0;

  /** GPU使用率 */
  private gpuUsage: number = 0;

  /** 内存使用率 */
  private memoryUsage: number = 0;

  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null = null;

  /**
   * 获取单例实例
   * @returns DeviceCapabilities实例
   */
  public static getInstance(options: DeviceCapabilitiesOptions = {}): DeviceCapabilities {
    if (!DeviceCapabilities.instance) {
      DeviceCapabilities.instance = new DeviceCapabilities(options);
    }
    return DeviceCapabilities.instance;
  }

  /**
   * 创建设备能力检测
   * @param options 设备能力检测配置
   */
  private constructor(options: DeviceCapabilitiesOptions = {}) {
    this.forceLowPerformance = options.forceLowPerformance || false;
    this.forceHighPerformance = options.forceHighPerformance || false;
    this.enableBatteryMonitoring = options.enableBatteryMonitoring !== undefined ? options.enableBatteryMonitoring : true;
    this.enableTemperatureMonitoring = options.enableTemperatureMonitoring !== undefined ? options.enableTemperatureMonitoring : true;
    this.enableNetworkMonitoring = options.enableNetworkMonitoring !== undefined ? options.enableNetworkMonitoring : true;
    this.enablePerformanceMonitoring = options.enablePerformanceMonitoring !== undefined ? options.enablePerformanceMonitoring : true;
    this.enableAutoPerformanceAdjustment = options.enableAutoPerformanceAdjustment !== undefined ? options.enableAutoPerformanceAdjustment : true;
    this.lowBatteryThreshold = options.lowBatteryThreshold || 20;
    this.highTemperatureThreshold = options.highTemperatureThreshold || 40;
    this.targetFPS = options.targetFPS || 60;
    this.minAcceptableFPS = options.minAcceptableFPS || 30;

    // 检测是否是移动设备
    this.isMobile = this.detectMobileDevice();

    // 检测是否是VR/AR设备
    this.isXRDevice = this.detectXRDevice();

    // 初始化默认值
    this.supportsWebGL2 = false;
    this.supportsFloatTextures = false;
    this.supportsAnisotropy = false;
    this.supportsInstancing = false;
    this.supportsCompressedTextures = false;
    this.supportsHDR = false;
    this.supportsShadows = false;
    this.maxTextureSize = 0;
    this.maxAnisotropy = 0;

    // 设置初始性能级别
    this.performanceLevel = this.determinePerformanceLevel();

    // 初始化电池监控
    if (this.enableBatteryMonitoring) {
      this.initializeBatteryMonitoring();
    }

    // 初始化温度监控
    if (this.enableTemperatureMonitoring) {
      this.initializeTemperatureMonitoring();
    }

    // 初始化网络监控
    if (this.enableNetworkMonitoring) {
      this.initializeNetworkMonitoring();
    }

    // 初始化性能监控
    if (this.enablePerformanceMonitoring) {
      this.initializePerformanceMonitoring();
    }
  }

  /**
   * 初始化设备能力检测
   * @param renderer Three.js渲染器
   */
  public initialize(renderer: THREE.WebGLRenderer): void {
    this.renderer = renderer;

    // 获取WebGL上下文
    const gl = renderer.getContext();

    // 检测WebGL2支持
    this.supportsWebGL2 = typeof WebGL2RenderingContext !== 'undefined' && gl instanceof WebGL2RenderingContext;

    // 获取渲染器能力
    const capabilities = renderer.capabilities;

    // 检测浮点纹理支持
    this.supportsFloatTextures = capabilities.isWebGL2 && capabilities.floatFragmentTextures;

    // 检测各向异性过滤支持
    this.supportsAnisotropy = capabilities.getMaxAnisotropy() > 0;
    this.maxAnisotropy = capabilities.getMaxAnisotropy();

    // 检测实例化渲染支持
    this.supportsInstancing = capabilities.isWebGL2;

    // 检测压缩纹理支持（简化检测，假设支持）
    this.supportsCompressedTextures = capabilities.isWebGL2;

    // 检测HDR支持
    this.supportsHDR = capabilities.isWebGL2 && capabilities.floatFragmentTextures;

    // 检测阴影支持
    this.supportsShadows = true; // 假设所有WebGL设备都支持基本阴影

    // 获取最大纹理大小
    this.maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);

    // 重新确定性能级别
    this.performanceLevel = this.determinePerformanceLevel();
  }

  /**
   * 获取设备性能级别
   * @returns 设备性能级别
   */
  public getPerformanceLevel(): DevicePerformanceLevel {
    return this.performanceLevel;
  }

  /**
   * 是否是低性能设备
   * @returns 是否是低性能设备
   */
  public isLowPerformanceDevice(): boolean {
    return this.performanceLevel === DevicePerformanceLevel.LOW || this.forceLowPerformance;
  }

  /**
   * 是否是高性能设备
   * @returns 是否是高性能设备
   */
  public isHighPerformanceDevice(): boolean {
    return this.performanceLevel === DevicePerformanceLevel.HIGH || this.forceHighPerformance;
  }

  /**
   * 是否支持WebGL2
   * @returns 是否支持WebGL2
   */
  public isWebGL2Supported(): boolean {
    return this.supportsWebGL2;
  }

  /**
   * 是否支持浮点纹理
   * @returns 是否支持浮点纹理
   */
  public isFloatTexturesSupported(): boolean {
    return this.supportsFloatTextures;
  }

  /**
   * 是否支持各向异性过滤
   * @returns 是否支持各向异性过滤
   */
  public isAnisotropySupported(): boolean {
    return this.supportsAnisotropy;
  }

  /**
   * 获取最大各向异性级别
   * @returns 最大各向异性级别
   */
  public getMaxAnisotropy(): number {
    return this.maxAnisotropy;
  }

  /**
   * 是否支持实例化渲染
   * @returns 是否支持实例化渲染
   */
  public isInstancingSupported(): boolean {
    return this.supportsInstancing;
  }

  /**
   * 是否支持压缩纹理
   * @returns 是否支持压缩纹理
   */
  public isCompressedTexturesSupported(): boolean {
    return this.supportsCompressedTextures;
  }

  /**
   * 是否支持HDR
   * @returns 是否支持HDR
   */
  public isHDRSupported(): boolean {
    return this.supportsHDR;
  }

  /**
   * 是否支持阴影
   * @returns 是否支持阴影
   */
  public isShadowsSupported(): boolean {
    return this.supportsShadows;
  }

  /**
   * 获取最大纹理大小
   * @returns 最大纹理大小
   */
  public getMaxTextureSize(): number {
    return this.maxTextureSize;
  }

  /**
   * 是否是移动设备
   * @returns 是否是移动设备
   */
  public isMobileDevice(): boolean {
    return this.isMobile;
  }

  /**
   * 是否是VR/AR设备
   * @returns 是否是VR/AR设备
   */
  public isXRDeviceEnabled(): boolean {
    return this.isXRDevice;
  }

  /**
   * 设置是否强制使用低性能模式
   * @param force 是否强制
   */
  public setForceLowPerformance(force: boolean): void {
    this.forceLowPerformance = force;
  }

  /**
   * 设置是否强制使用高性能模式
   * @param force 是否强制
   */
  public setForceHighPerformance(force: boolean): void {
    this.forceHighPerformance = force;
  }

  /**
   * 检测是否是移动设备
   * @returns 是否是移动设备
   */
  private detectMobileDevice(): boolean {
    if (typeof navigator === 'undefined') return false;

    const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;

    // 检测常见移动设备标识
    const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;

    return mobileRegex.test(userAgent);
  }

  /**
   * 检测是否是VR/AR设备
   * @returns 是否是VR/AR设备
   */
  private detectXRDevice(): boolean {
    if (typeof navigator === 'undefined') return false;

    // 检测WebXR支持
    return 'xr' in navigator;
  }

  /**
   * 确定设备性能级别
   * @returns 设备性能级别
   */
  private determinePerformanceLevel(): DevicePerformanceLevel {
    // 如果强制使用低性能模式
    if (this.forceLowPerformance) {
      return DevicePerformanceLevel.LOW;
    }

    // 如果强制使用高性能模式
    if (this.forceHighPerformance) {
      return DevicePerformanceLevel.HIGH;
    }

    // 如果是VR/AR设备，通常需要高性能
    if (this.isXRDevice) {
      return DevicePerformanceLevel.HIGH;
    }

    // 如果是移动设备，通常是中等或低性能
    if (this.isMobile) {
      // 如果不支持WebGL2，则为低性能
      if (!this.supportsWebGL2) {
        return DevicePerformanceLevel.LOW;
      }

      // 如果支持WebGL2但不支持浮点纹理，则为中等性能
      if (!this.supportsFloatTextures) {
        return DevicePerformanceLevel.MEDIUM;
      }
    }

    // 如果支持WebGL2和浮点纹理，则为高性能
    if (this.supportsWebGL2 && this.supportsFloatTextures) {
      return DevicePerformanceLevel.HIGH;
    }

    // 如果支持WebGL2但不支持浮点纹理，则为中等性能
    if (this.supportsWebGL2) {
      return DevicePerformanceLevel.MEDIUM;
    }

    // 默认为低性能
    return DevicePerformanceLevel.LOW;
  }

  /**
   * 初始化电池监控
   */
  private initializeBatteryMonitoring(): void {
    // 检查是否支持电池API
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        // 更新电池状态
        this.updateBatteryStatus(battery);

        // 监听电池状态变化
        battery.addEventListener('levelchange', () => {
          this.updateBatteryStatus(battery);
        });

        battery.addEventListener('chargingchange', () => {
          this.updateBatteryStatus(battery);
        });
      });
    }
  }

  /**
   * 更新电池状态
   * @param battery 电池对象
   */
  private updateBatteryStatus(battery: any): void {
    this.batteryLevel = battery.level * 100;
    this.batteryCharging = battery.charging;
  }

  /**
   * 初始化温度监控
   */
  private initializeTemperatureMonitoring(): void {
    // 目前浏览器没有直接获取设备温度的API
    // 这里使用一个模拟的温度值，实际应用中可能需要通过其他方式获取
    this.temperature = 25 + Math.random() * 10;
  }

  /**
   * 初始化网络监控
   */
  private initializeNetworkMonitoring(): void {
    // 检查是否支持网络信息API
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;

      // 监听网络状态变化
      connection.addEventListener('change', () => {
        // 更新网络状态
      });
    }
  }

  /**
   * 初始化性能监控
   */
  private initializePerformanceMonitoring(): void {
    // 初始化FPS计数器
    let lastTime = performance.now();
    let frames = 0;

    const updateFPS = () => {
      const now = performance.now();
      frames++;

      if (now >= lastTime + 1000) {
        this.currentFPS = frames * 1000 / (now - lastTime);
        frames = 0;
        lastTime = now;

        // 模拟CPU和GPU使用率
        this.cpuUsage = Math.min(0.1 + Math.random() * 0.3 + (60 / this.currentFPS) * 0.1, 1);
        this.gpuUsage = Math.min(0.1 + Math.random() * 0.3 + (60 / this.currentFPS) * 0.1, 1);
        this.memoryUsage = Math.min(0.2 + Math.random() * 0.2, 1);

        // 如果启用了自动性能调整，根据性能数据调整性能级别
        if (this.enableAutoPerformanceAdjustment) {
          this.adjustPerformanceLevel();
        }
      }

      requestAnimationFrame(updateFPS);
    };

    updateFPS();
  }

  /**
   * 调整性能级别
   */
  private adjustPerformanceLevel(): void {
    // 如果强制使用低性能或高性能模式，则不自动调整
    if (this.forceLowPerformance || this.forceHighPerformance) {
      return;
    }

    // 如果帧率低于最小可接受帧率，降低性能级别
    if (this.currentFPS < this.minAcceptableFPS) {
      if (this.performanceLevel > DevicePerformanceLevel.LOW) {
        this.performanceLevel--;
      }
    }
    // 如果帧率高于目标帧率的1.2倍，提高性能级别
    else if (this.currentFPS > this.targetFPS * 1.2) {
      if (this.performanceLevel < DevicePerformanceLevel.HIGH) {
        this.performanceLevel++;
      }
    }

    // 如果电池电量低于阈值，降低性能级别
    if (this.enableBatteryMonitoring && this.batteryLevel < this.lowBatteryThreshold && !this.batteryCharging) {
      if (this.performanceLevel > DevicePerformanceLevel.LOW) {
        this.performanceLevel = DevicePerformanceLevel.LOW;
      }
    }

    // 如果设备温度高于阈值，降低性能级别
    if (this.enableTemperatureMonitoring && this.temperature > this.highTemperatureThreshold) {
      if (this.performanceLevel > DevicePerformanceLevel.LOW) {
        this.performanceLevel = DevicePerformanceLevel.LOW;
      }
    }
  }

  /**
   * 获取电池电量
   * @returns 电池电量（百分比）
   */
  public getBatteryLevel(): number {
    return this.batteryLevel;
  }

  /**
   * 获取电池是否正在充电
   * @returns 电池是否正在充电
   */
  public isBatteryCharging(): boolean {
    return this.batteryCharging;
  }

  /**
   * 获取设备温度
   * @returns 设备温度（摄氏度）
   */
  public getTemperature(): number {
    return this.temperature;
  }

  /**
   * 获取当前帧率
   * @returns 当前帧率
   */
  public getCurrentFPS(): number {
    return this.currentFPS;
  }

  /**
   * 获取CPU使用率
   * @returns CPU使用率（0-1）
   */
  public getCPUUsage(): number {
    return this.cpuUsage;
  }

  /**
   * 获取GPU使用率
   * @returns GPU使用率（0-1）
   */
  public getGPUUsage(): number {
    return this.gpuUsage;
  }

  /**
   * 获取内存使用率
   * @returns 内存使用率（0-1）
   */
  public getMemoryUsage(): number {
    return this.memoryUsage;
  }

  /**
   * 设置性能级别
   * @param level 性能级别
   */
  public setPerformanceLevel(level: DevicePerformanceLevel): void {
    this.performanceLevel = level;
  }

  /**
   * 设置自动性能调整
   * @param enable 是否启用
   */
  public setAutoPerformanceAdjustment(enable: boolean): void {
    this.enableAutoPerformanceAdjustment = enable;
  }

  /**
   * 设置低电量阈值
   * @param threshold 阈值（百分比）
   */
  public setLowBatteryThreshold(threshold: number): void {
    this.lowBatteryThreshold = threshold;
  }

  /**
   * 设置高温阈值
   * @param threshold 阈值（摄氏度）
   */
  public setHighTemperatureThreshold(threshold: number): void {
    this.highTemperatureThreshold = threshold;
  }

  /**
   * 设置目标帧率
   * @param fps 帧率
   */
  public setTargetFPS(fps: number): void {
    this.targetFPS = fps;
  }

  /**
   * 设置最小可接受帧率
   * @param fps 帧率
   */
  public setMinAcceptableFPS(fps: number): void {
    this.minAcceptableFPS = fps;
  }
}
