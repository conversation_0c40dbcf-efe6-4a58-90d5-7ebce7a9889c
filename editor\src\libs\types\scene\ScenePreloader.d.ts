/**
 * 场景预加载器
 * 负责场景资源的预加载和管理
 */
import { AssetManager } from '../assets/AssetManager';
import { Scene } from './Scene';
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 场景资源类型
 */
export declare enum SceneResourceType {
    /** 纹理 */
    TEXTURE = "texture",
    /** 模型 */
    MODEL = "model",
    /** 音频 */
    AUDIO = "audio",
    /** 字体 */
    FONT = "font",
    /** 着色器 */
    SHADER = "shader",
    /** 材质 */
    MATERIAL = "material",
    /** 其他 */
    OTHER = "other"
}
/**
 * 场景资源信息
 */
export interface SceneResourceInfo {
    /** 资源ID */
    id: string;
    /** 资源类型 */
    type: SceneResourceType;
    /** 资源URL */
    url: string;
    /** 资源优先级 */
    priority?: number;
    /** 资源大小（字节） */
    size?: number;
    /** 所属实体ID */
    entityId?: string;
    /** 所属组件类型 */
    componentType?: string;
    /** 是否必需 */
    required?: boolean;
}
/**
 * 场景预加载选项
 */
export interface ScenePreloaderOptions {
    /** 资产管理器 */
    assetManager: AssetManager;
    /** 是否自动分析场景资源 */
    autoAnalyzeResources?: boolean;
    /** 是否自动注册资源 */
    autoRegisterResources?: boolean;
    /** 最大并发加载数 */
    maxConcurrentLoads?: number;
    /** 是否启用调试模式 */
    debug?: boolean;
}
/**
 * 场景预加载进度信息
 */
export interface ScenePreloadProgressInfo {
    /** 场景ID */
    sceneId: string;
    /** 已加载资源数 */
    loaded: number;
    /** 总资源数 */
    total: number;
    /** 加载进度（0-1） */
    progress: number;
    /** 已加载资源列表 */
    loadedResources: string[];
    /** 加载失败资源列表 */
    failedResources: string[];
    /** 当前加载资源 */
    currentResource?: string;
    /** 已加载字节数 */
    loadedBytes: number;
    /** 总字节数 */
    totalBytes: number;
}
/**
 * 场景预加载器
 */
export declare class ScenePreloader extends EventEmitter {
    /** 资产管理器 */
    private assetManager;
    /** 资源预加载器 */
    private resourcePreloader;
    /** 场景资源映射（场景ID -> 资源信息数组） */
    private sceneResources;
    /** 是否自动分析场景资源 */
    private autoAnalyzeResources;
    /** 是否自动注册资源 */
    private autoRegisterResources;
    /** 是否启用调试模式 */
    private debug;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建场景预加载器实例
     * @param options 预加载器选项
     */
    constructor(options: ScenePreloaderOptions);
    /**
     * 初始化预加载器
     */
    initialize(): void;
    /**
     * 分析场景资源
     * @param scene 场景实例
     * @returns 场景资源信息数组
     */
    analyzeSceneResources(scene: Scene): SceneResourceInfo[];
    /**
     * 分析天空盒资源
     * @param skybox 天空盒实例
     * @returns 资源信息数组
     */
    private analyzeSkyboxResources;
    /**
     * 分析实体资源
     * @param entity 实体实例
     * @returns 资源信息数组
     */
    private analyzeEntityResources;
    /**
     * 分析网格组件资源
     * @param entity 实体实例
     * @param component 组件实例
     * @param resources 资源信息数组
     */
    private analyzeMeshComponentResources;
    /**
     * 分析材质资源
     * @param entity 实体实例
     * @param material 材质实例
     * @param resources 资源信息数组
     */
    private analyzeMaterialResources;
    /**
     * 分析贴图资源
     * @param entity 实体实例
     * @param mapType 贴图类型
     * @param texture 贴图实例
     * @param resources 资源信息数组
     */
    private analyzeTextureResource;
    /**
     * 分析音频组件资源
     * @param entity 实体实例
     * @param component 组件实例
     * @param resources 资源信息数组
     */
    private analyzeAudioComponentResources;
    /**
     * 分析光照组件资源
     * @param entity 实体实例
     * @param component 组件实例
     * @param resources 资源信息数组
     */
    private analyzeLightComponentResources;
    /**
     * 注册场景资源
     * @param sceneId 场景ID
     * @returns 是否成功注册
     */
    registerSceneResources(sceneId: string): boolean;
    /**
     * 注册资源
     * @param resource 资源信息
     * @returns 是否成功注册
     */
    private registerResource;
    /**
     * 将场景资源类型转换为资产类型
     * @param resourceType 场景资源类型
     * @returns 资产类型
     */
    private convertResourceTypeToAssetType;
    /**
     * 创建预加载组
     * @param sceneId 场景ID
     * @param resources 资源信息数组
     */
    private createPreloadGroup;
    /**
     * 加载资源
     * @param resourceIds 资源ID数组
     * @param options 加载选项
     */
    loadResources(resourceIds: string[], options?: {
        onProgress?: (progress: any) => void;
        onComplete?: (progress: any) => void;
        onError?: (error: any) => void;
        priority?: number;
        maxConcurrentLoads?: number;
        memoryLimit?: number;
        useLOD?: boolean;
        lodLevel?: number;
        useTextureCompression?: boolean;
        useGeometrySimplification?: boolean;
        useInstancing?: boolean;
    }): void;
    /**
     * 预加载场景资源
     * @param sceneId 场景ID
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    preloadSceneResources(sceneId: string, onProgress?: (progress: ScenePreloadProgressInfo) => void): Promise<ScenePreloadProgressInfo>;
    /**
     * 预加载场景
     * @param scene 场景实例
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    preloadScene(scene: Scene, onProgress?: (progress: ScenePreloadProgressInfo) => void): Promise<ScenePreloadProgressInfo>;
    /**
     * 卸载场景资源
     * @param sceneId 场景ID
     * @returns 是否成功卸载
     */
    unloadSceneResources(sceneId: string): boolean;
    /**
     * 获取场景资源
     * @param sceneId 场景ID
     * @returns 场景资源信息数组
     */
    getSceneResources(sceneId: string): SceneResourceInfo[];
    /**
     * 清除场景资源
     * @param sceneId 场景ID
     * @returns 是否成功清除
     */
    clearSceneResources(sceneId: string): boolean;
    /**
     * 销毁预加载器
     */
    dispose(): void;
}
