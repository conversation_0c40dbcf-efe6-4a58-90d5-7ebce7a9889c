# 场景生成服务 (Scene Generation Service)

基于NestJS的智能场景生成微服务，支持文本、语音、图像等多种输入方式生成3D场景。

## 功能特性

- 🎯 **多模态输入**: 支持文本、语音、图像输入
- 🤖 **AI驱动**: 集成多种AI模型进行智能分析和生成
- 🏗️ **模块化架构**: 基于NestJS的微服务架构
- 🔄 **实时通信**: WebSocket支持实时进度推送
- 📊 **任务管理**: 完整的任务生命周期管理
- 🎨 **模板系统**: 预定义场景模板快速生成
- 📁 **资源管理**: 统一的3D资源管理系统

## 技术栈

- **框架**: NestJS + TypeScript
- **数据库**: PostgreSQL + TypeORM
- **缓存**: Redis
- **文档**: Swagger/OpenAPI
- **容器化**: Docker + Docker Compose

## 快速开始

### 使用Docker（推荐）

1. **启动服务**
   ```bash
   # Windows
   scripts\start.bat
   
   # 或者手动启动
   docker-compose up -d
   ```

2. **访问服务**
   - 服务地址: http://localhost:3004
   - API文档: http://localhost:3004/api/docs
   - 健康检查: http://localhost:3004/health

3. **停止服务**
   ```bash
   # Windows
   scripts\stop.bat
   
   # 或者手动停止
   docker-compose down
   ```

### 本地开发

1. **安装依赖**
   ```bash
   npm install
   ```

2. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件配置数据库和Redis连接
   ```

3. **启动数据库和Redis**
   ```bash
   docker-compose up postgres redis -d
   ```

4. **运行服务**
   ```bash
   # 开发模式
   npm run start:dev
   
   # 生产模式
   npm run build
   npm run start:prod
   ```

## API接口

### 场景生成

- `POST /generation/tasks` - 创建生成任务
- `GET /generation/tasks/:id` - 获取任务状态
- `GET /generation/tasks` - 获取任务列表

### 模板管理

- `GET /templates` - 获取模板列表
- `GET /templates/:id` - 获取模板详情

### 资源管理

- `GET /assets` - 获取资源列表
- `POST /assets/upload` - 上传资源文件

### 健康检查

- `GET /health` - 服务健康状态

## 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| PORT | 服务端口 | 3004 |
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 5432 |
| REDIS_HOST | Redis主机 | localhost |
| REDIS_PORT | Redis端口 | 6379 |

## 部署

### Docker部署

1. **构建镜像**
   ```bash
   docker build -t scene-generation-service .
   ```

2. **运行容器**
   ```bash
   docker-compose -f docker-compose.system.yml up -d
   ```

### 与其他服务集成

该服务设计为微服务架构的一部分，可以与其他服务协同工作：

- **RAG服务**: 提供知识检索能力
- **数字人服务**: 提供虚拟角色交互
- **前端应用**: 提供用户界面

## 开发指南

### 项目结构

```
src/
├── common/           # 公共模块
│   ├── services/     # 公共服务
│   └── controllers/  # 公共控制器
├── modules/          # 业务模块
│   ├── auth/         # 用户认证
│   ├── generation/   # 场景生成
│   ├── templates/    # 模板管理
│   ├── assets/       # 资源管理
│   └── websocket/    # WebSocket通信
├── app.module.ts     # 应用模块
└── main.ts          # 应用入口
```

### 添加新功能

1. 在 `src/modules/` 下创建新模块
2. 实现服务、控制器和实体
3. 在 `app.module.ts` 中注册模块
4. 添加相应的测试

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库是否启动
   - 验证连接配置

2. **Redis连接失败**
   - 检查Redis是否启动
   - 验证连接配置

3. **端口冲突**
   - 修改 `.env` 中的端口配置
   - 检查其他服务是否占用端口

### 日志查看

```bash
# 查看服务日志
docker-compose logs scene-generation-service

# 实时查看日志
docker-compose logs -f scene-generation-service
```

## 许可证

MIT License
