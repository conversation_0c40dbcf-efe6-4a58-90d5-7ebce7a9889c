/**
 * 滑动约束
 * 限制两个物体沿着一个轴线滑动
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { PhysicsConstraint } from './PhysicsConstraint';
/**
 * 滑动约束选项
 */
export interface SliderConstraintOptions {
    /** 轴向A */
    axisA?: THREE.Vector3;
    /** 轴向B */
    axisB?: THREE.Vector3;
    /** 最小位移 */
    lowerLimit?: number;
    /** 最大位移 */
    upperLimit?: number;
    /** 是否允许连接的物体之间碰撞 */
    collideConnected?: boolean;
    /** 最大力 */
    maxForce?: number;
}
/**
 * 滑动约束
 */
export declare class SliderConstraint extends PhysicsConstraint {
    /** 组件类型 */
    static readonly type: string;
    /** 轴向A */
    private axisA;
    /** 轴向B */
    private axisB;
    /** 最小位移 */
    private lowerLimit;
    /** 最大位移 */
    private upperLimit;
    /** 最大力 */
    private maxForce;
    /**
     * 创建滑动约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    constructor(targetEntity?: Entity | null, options?: SliderConstraintOptions);
    /**
     * 创建约束
     */
    protected createConstraint(): void;
    /**
     * 设置轴向A
     * @param axis 轴向
     */
    setAxisA(axis: THREE.Vector3): void;
    /**
     * 获取轴向A
     * @returns 轴向
     */
    getAxisA(): THREE.Vector3;
    /**
     * 设置轴向B
     * @param axis 轴向
     */
    setAxisB(axis: THREE.Vector3): void;
    /**
     * 获取轴向B
     * @returns 轴向
     */
    getAxisB(): THREE.Vector3;
    /**
     * 设置最小位移
     * @param limit 最小位移
     */
    setLowerLimit(limit: number): void;
    /**
     * 获取最小位移
     * @returns 最小位移
     */
    getLowerLimit(): number;
    /**
     * 设置最大位移
     * @param limit 最大位移
     */
    setUpperLimit(limit: number): void;
    /**
     * 获取最大位移
     * @returns 最大位移
     */
    getUpperLimit(): number;
    /**
     * 设置最大力
     * @param maxForce 最大力
     */
    setMaxForce(maxForce: number): void;
    /**
     * 获取最大力
     * @returns 最大力
     */
    getMaxForce(): number;
    /**
     * 重新创建约束
     */
    private recreateConstraint;
    /**
     * 销毁约束
     */
    dispose(): void;
}
