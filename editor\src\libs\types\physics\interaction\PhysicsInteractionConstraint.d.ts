import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import { PhysicsSystem } from '../PhysicsSystem';
/**
 * 物理交互约束配置
 */
export interface PhysicsInteractionConstraintConfig {
    /** 约束类型 */
    constraintType?: string;
    /** 约束参数 */
    constraintParams?: any;
    /** 是否使用弹簧约束 */
    useSpring?: boolean;
    /** 弹簧刚度 */
    springStiffness?: number;
    /** 弹簧阻尼 */
    springDamping?: number;
    /** 最大约束力 */
    maxForce?: number;
    /** 碰撞组 */
    collisionGroup?: number;
    /** 碰撞掩码 */
    collisionMask?: number;
}
/**
 * 物理交互约束
 * 用于实现角色与物理对象之间的约束关系
 */
export declare class PhysicsInteractionConstraint {
    /** 角色实体 */
    private characterEntity;
    /** 目标实体 */
    private targetEntity;
    /** 交互类型 */
    private interactionType;
    /** 物理系统 */
    private physicsSystem;
    /** 约束对象 */
    private constraint;
    /** 弹簧对象（如果使用弹簧） */
    private spring;
    /** 约束参数 */
    private constraintParams;
    /** 是否使用弹簧约束 */
    private useSpring;
    /** 弹簧刚度 */
    private springStiffness;
    /** 弹簧阻尼 */
    private springDamping;
    /** 最大约束力 */
    private maxForce;
    /** 碰撞组 */
    private collisionGroup;
    /** 碰撞掩码 */
    private collisionMask;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /**
     * 创建物理交互约束
     * @param characterEntity 角色实体
     * @param targetEntity 目标实体
     * @param interactionType 交互类型
     * @param physicsSystem 物理系统
     * @param config 约束配置
     */
    constructor(characterEntity: Entity, targetEntity: Entity, interactionType: string, physicsSystem: PhysicsSystem, config?: PhysicsInteractionConstraintConfig);
    /**
     * 初始化约束
     * @returns 是否成功初始化
     */
    initialize(): boolean;
    /**
     * 创建拉动约束
     * @param characterBody 角色物理体
     * @param targetBody 目标物理体
     * @returns 是否成功创建
     */
    private createPullConstraint;
    /**
     * 创建举起约束
     * @param characterBody 角色物理体
     * @param targetBody 目标物理体
     * @returns 是否成功创建
     */
    private createLiftConstraint;
    /**
     * 创建攀爬约束
     * @param characterBody 角色物理体
     * @param targetBody 目标物理体
     * @returns 是否成功创建
     */
    private createClimbConstraint;
    /**
     * 创建悬挂约束
     * @param characterBody 角色物理体
     * @param targetBody 目标物理体
     * @returns 是否成功创建
     */
    private createHangConstraint;
    /**
     * 更新约束
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新拉动约束
     * @param deltaTime 帧间隔时间（秒）
     */
    private updatePullConstraint;
    /**
     * 更新举起约束
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateLiftConstraint;
    /**
     * 更新攀爬约束
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateClimbConstraint;
    /**
     * 更新悬挂约束
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateHangConstraint;
    /**
     * 销毁约束
     */
    destroy(): void;
    /**
     * 获取角色实体
     */
    getCharacterEntity(): Entity;
    /**
     * 获取目标实体
     */
    getTargetEntity(): Entity;
    /**
     * 获取交互类型
     */
    getInteractionType(): string;
    /**
     * 获取约束对象
     */
    getConstraint(): CANNON.Constraint | null;
    /**
     * 是否已初始化
     */
    isInitialized(): boolean;
    /**
     * 是否已销毁
     */
    isDestroyed(): boolean;
}
