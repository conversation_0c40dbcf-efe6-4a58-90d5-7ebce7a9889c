/**
 * 地下环境光照系统
 * 用于增强地下环境的光照效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 光束类型
 */
export enum LightShaftType {
  /** 体积光 */
  VOLUMETRIC = 'volumetric',
  /** 光线 */
  RAY = 'ray',
  /** 聚光 */
  SPOT = 'spot',
  /** 环境光 */
  AMBIENT = 'ambient'
}

/**
 * 光束配置
 */
export interface LightShaftConfig {
  /** 光束类型 */
  type: LightShaftType;
  /** 位置 */
  position: THREE.Vector3;
  /** 方向 */
  direction?: THREE.Vector3;
  /** 颜色 */
  color?: THREE.Color | number | string;
  /** 强度 */
  intensity?: number;
  /** 衰减 */
  decay?: number;
  /** 距离 */
  distance?: number;
  /** 角度 */
  angle?: number;
  /** 半影 */
  penumbra?: number;
  /** 体积密度 */
  density?: number;
  /** 散射系数 */
  scattering?: number;
  /** 采样数 */
  samples?: number;
  /** 是否投射阴影 */
  castShadow?: boolean;
  /** 阴影贴图大小 */
  shadowMapSize?: number;
  /** 是否启用动态效果 */
  enableDynamicEffect?: boolean;
  /** 动态效果参数 */
  dynamicEffect?: {
    /** 强度变化 */
    intensityVariation?: number;
    /** 颜色变化 */
    colorVariation?: number;
    /** 方向变化 */
    directionVariation?: number;
    /** 变化速度 */
    speed?: number;
  };
}

/**
 * 地下环境光照系统配置
 */
export interface UndergroundLightingSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用后处理 */
  enablePostProcessing?: boolean;
  /** 是否启用阴影 */
  enableShadows?: boolean;
  /** 是否启用体积光 */
  enableVolumetricLighting?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 地下环境光照系统
 */
export class UndergroundLightingSystem extends System {
  /** 配置 */
  private config: UndergroundLightingSystemConfig;
  /** 光束映射 */
  private lightShafts: Map<string, {
    /** 配置 */
    config: LightShaftConfig;
    /** 光源 */
    light: THREE.Light;
    /** 辅助对象 */
    helper?: THREE.Object3D;
    /** 体积光网格 */
    volumetricMesh?: THREE.Mesh;
  }> = new Map();
  /** 场景 */
  private scene: THREE.Scene | null = null;
  /** 相机 */
  private camera: THREE.Camera | null = null;
  /** 后处理渲染器 */
  private composer: any = null;
  /** 体积光着色器 */
  private volumetricLightShader: {
    /** 顶点着色器 */
    vertexShader: string;
    /** 片段着色器 */
    fragmentShader: string;
    /** 统一变量 */
    uniforms: { [key: string]: THREE.IUniform };
  } | null = null;
  /** 是否初始化 */
  private initialized: boolean = false;
  /** 时间 */
  private time: number = 0;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: UndergroundLightingSystemConfig = {}) {
    super();
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      enablePostProcessing: config.enablePostProcessing !== undefined ? config.enablePostProcessing : true,
      enableShadows: config.enableShadows !== undefined ? config.enableShadows : true,
      enableVolumetricLighting: config.enableVolumetricLighting !== undefined ? config.enableVolumetricLighting : true,
      debug: config.debug !== undefined ? config.debug : false
    };
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 基类初始化
    super.initialize();
    this.initialized = true;

    if (this.config.debug) {
      Debug.log('UndergroundLightingSystem', '初始化地下环境光照系统');
    }
  }

  /**
   * 设置渲染环境
   * @param scene 场景
   * @param camera 相机
   * @param renderer 渲染器
   */
  public setRenderEnvironment(scene: THREE.Scene, camera: THREE.Camera, renderer: THREE.WebGLRenderer): void {
    this.scene = scene;
    this.camera = camera;

    // 初始化体积光着色器
    this.initializeVolumetricLightShader();

    // 初始化后处理
    if (this.config.enablePostProcessing) {
      this.initializePostProcessing(renderer);
    }

    if (this.config.debug) {
      Debug.log('UndergroundLightingSystem', '设置渲染环境');
    }
  }

  /**
   * 初始化体积光着色器
   */
  private initializeVolumetricLightShader(): void {
    this.volumetricLightShader = {
      uniforms: {
        tDiffuse: { value: null },
        lightPosition: { value: new THREE.Vector2(0.5, 0.5) },
        exposure: { value: 0.18 },
        decay: { value: 0.95 },
        density: { value: 0.8 },
        weight: { value: 0.4 },
        samples: { value: 50 }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        varying vec2 vUv;
        uniform sampler2D tDiffuse;
        uniform vec2 lightPosition;
        uniform float exposure;
        uniform float decay;
        uniform float density;
        uniform float weight;
        uniform int samples;

        void main() {
          vec2 texCoord = vUv;
          vec2 deltaTextCoord = texCoord - lightPosition;
          deltaTextCoord *= 1.0 / float(samples) * density;
          vec4 color = texture2D(tDiffuse, texCoord);
          float illuminationDecay = 1.0;

          for(int i=0; i < 100; i++) {
            if(i >= samples) break;
            texCoord -= deltaTextCoord;
            vec4 sample = texture2D(tDiffuse, texCoord);
            sample *= illuminationDecay * weight;
            color += sample;
            illuminationDecay *= decay;
          }

          gl_FragColor = color * exposure;
        }
      `
    };
  }

  /**
   * 初始化后处理
   * @param _renderer 渲染器
   */
  private initializePostProcessing(_renderer: THREE.WebGLRenderer): void {
    // 这里应该初始化后处理效果
    // 实际实现需要引入后处理相关库，如EffectComposer
    // 由于代码简化，这里只是一个占位
    if (this.config.debug) {
      Debug.log('UndergroundLightingSystem', '初始化后处理');
    }
  }

  /**
   * 添加光束
   * @param id 光束ID
   * @param config 光束配置
   */
  public addLightShaft(id: string, config: LightShaftConfig): void {
    if (!this.initialized || !this.scene) {
      Debug.warn('UndergroundLightingSystem', '光照系统未初始化');
      return;
    }

    // 检查是否已存在
    if (this.lightShafts.has(id)) {
      this.removeLightShaft(id);
    }

    // 创建光源
    let light: THREE.Light;
    let helper: THREE.Object3D | undefined;
    let volumetricMesh: THREE.Mesh | undefined;

    switch (config.type) {
      case LightShaftType.VOLUMETRIC:
        // 创建聚光灯作为体积光的基础
        const spotLight = new THREE.SpotLight(
          config.color !== undefined ? new THREE.Color(config.color) : 0xffffff,
          config.intensity !== undefined ? config.intensity : 1.0,
          config.distance !== undefined ? config.distance : 100,
          config.angle !== undefined ? config.angle : Math.PI / 6,
          config.penumbra !== undefined ? config.penumbra : 0.1,
          config.decay !== undefined ? config.decay : 2
        );
        spotLight.position.copy(config.position);
        if (config.direction) {
          spotLight.target.position.copy(config.position).add(config.direction);
          this.scene.add(spotLight.target);
        }
        light = spotLight;
        
        // 设置阴影
        if (this.config.enableShadows && config.castShadow) {
          light.castShadow = true;
          (light as THREE.SpotLight).shadow.mapSize.width = config.shadowMapSize || 1024;
          (light as THREE.SpotLight).shadow.mapSize.height = config.shadowMapSize || 1024;
        }
        
        // 创建体积光网格
        if (this.config.enableVolumetricLighting && this.volumetricLightShader) {
          // 实际实现应该创建体积光网格
          // 由于代码简化，这里只是一个占位
        }
        
        // 创建辅助对象
        if (this.config.debug) {
          helper = new THREE.SpotLightHelper(light as THREE.SpotLight);
          this.scene.add(helper);
        }
        break;

      case LightShaftType.RAY:
        // 创建平行光作为光线
        const directionalLight = new THREE.DirectionalLight(
          config.color !== undefined ? new THREE.Color(config.color) : 0xffffff,
          config.intensity !== undefined ? config.intensity : 1.0
        );
        directionalLight.position.copy(config.position);
        if (config.direction) {
          directionalLight.target.position.copy(config.position).add(config.direction);
          this.scene.add(directionalLight.target);
        }
        light = directionalLight;
        
        // 设置阴影
        if (this.config.enableShadows && config.castShadow) {
          light.castShadow = true;
          (light as THREE.DirectionalLight).shadow.mapSize.width = config.shadowMapSize || 1024;
          (light as THREE.DirectionalLight).shadow.mapSize.height = config.shadowMapSize || 1024;
        }
        
        // 创建辅助对象
        if (this.config.debug) {
          helper = new THREE.DirectionalLightHelper(light as THREE.DirectionalLight);
          this.scene.add(helper);
        }
        break;

      case LightShaftType.SPOT:
        // 创建聚光灯
        const spotLight2 = new THREE.SpotLight(
          config.color !== undefined ? new THREE.Color(config.color) : 0xffffff,
          config.intensity !== undefined ? config.intensity : 1.0,
          config.distance !== undefined ? config.distance : 100,
          config.angle !== undefined ? config.angle : Math.PI / 6,
          config.penumbra !== undefined ? config.penumbra : 0.1,
          config.decay !== undefined ? config.decay : 2
        );
        spotLight2.position.copy(config.position);
        if (config.direction) {
          spotLight2.target.position.copy(config.position).add(config.direction);
          this.scene.add(spotLight2.target);
        }
        light = spotLight2;
        
        // 设置阴影
        if (this.config.enableShadows && config.castShadow) {
          light.castShadow = true;
          (light as THREE.SpotLight).shadow.mapSize.width = config.shadowMapSize || 1024;
          (light as THREE.SpotLight).shadow.mapSize.height = config.shadowMapSize || 1024;
        }
        
        // 创建辅助对象
        if (this.config.debug) {
          helper = new THREE.SpotLightHelper(light as THREE.SpotLight);
          this.scene.add(helper);
        }
        break;

      case LightShaftType.AMBIENT:
      default:
        // 创建环境光
        light = new THREE.AmbientLight(
          config.color !== undefined ? new THREE.Color(config.color) : 0xffffff,
          config.intensity !== undefined ? config.intensity : 0.5
        );
        break;
    }

    // 添加到场景
    this.scene.add(light);

    // 保存光束
    this.lightShafts.set(id, {
      config,
      light,
      helper,
      volumetricMesh
    });

    // 发出事件
    this.eventEmitter.emit('lightShaftAdded', { id, config });

    if (this.config.debug) {
      Debug.log('UndergroundLightingSystem', `添加光束: ${id}, 类型: ${config.type}`);
    }
  }

  /**
   * 移除光束
   * @param id 光束ID
   */
  public removeLightShaft(id: string): void {
    const lightShaft = this.lightShafts.get(id);
    if (!lightShaft || !this.scene) return;

    // 从场景中移除
    this.scene.remove(lightShaft.light);
    if ((lightShaft.light as any).target) {
      this.scene.remove((lightShaft.light as any).target);
    }
    if (lightShaft.helper) {
      this.scene.remove(lightShaft.helper);
    }
    if (lightShaft.volumetricMesh) {
      this.scene.remove(lightShaft.volumetricMesh);
    }

    // 移除引用
    this.lightShafts.delete(id);

    // 发出事件
    this.eventEmitter.emit('lightShaftRemoved', { id });

    if (this.config.debug) {
      Debug.log('UndergroundLightingSystem', `移除光束: ${id}`);
    }
  }

  /**
   * 更新光束
   * @param id 光束ID
   * @param config 光束配置
   */
  public updateLightShaft(id: string, config: Partial<LightShaftConfig>): void {
    const lightShaft = this.lightShafts.get(id);
    if (!lightShaft) return;

    // 更新配置
    const newConfig = { ...lightShaft.config, ...config };
    lightShaft.config = newConfig as LightShaftConfig;

    // 更新光源属性
    // 实际实现应该根据光源类型更新不同的属性
    // 由于代码简化，这里只是一个占位

    // 发出事件
    this.eventEmitter.emit('lightShaftUpdated', { id, config: newConfig });

    if (this.config.debug) {
      Debug.log('UndergroundLightingSystem', `更新光束: ${id}`);
    }
  }

  /**
   * 更新
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    if (!this.initialized || !this.config.enabled) {
      return;
    }

    // 更新时间
    this.time += deltaTime;

    // 更新所有光束
    this.lightShafts.forEach((lightShaft, id) => {
      this.updateLightShaftDynamicEffects(id, lightShaft, deltaTime);
    });

    // 更新辅助对象
    this.lightShafts.forEach((lightShaft) => {
      if (lightShaft.helper) {
        if (lightShaft.helper instanceof THREE.SpotLightHelper) {
          lightShaft.helper.update();
        } else if (lightShaft.helper instanceof THREE.DirectionalLightHelper) {
          lightShaft.helper.update();
        }
      }
    });
  }

  /**
   * 更新光束动态效果
   * @param _id 光束ID
   * @param lightShaft 光束对象
   * @param _deltaTime 时间增量
   */
  private updateLightShaftDynamicEffects(_id: string, lightShaft: any, _deltaTime: number): void {
    const config = lightShaft.config;
    if (!config.enableDynamicEffect || !config.dynamicEffect) return;

    const light = lightShaft.light;
    const dynamicEffect = config.dynamicEffect;

    // 更新强度
    if (dynamicEffect.intensityVariation && light.intensity !== undefined) {
      const baseIntensity = config.intensity || 1.0;
      const variation = dynamicEffect.intensityVariation;
      const speed = dynamicEffect.speed || 1.0;
      light.intensity = baseIntensity + Math.sin(this.time * speed) * variation;
    }

    // 更新颜色
    if (dynamicEffect.colorVariation && light.color) {
      const baseColor = new THREE.Color(config.color || 0xffffff);
      const variation = dynamicEffect.colorVariation;
      const speed = dynamicEffect.speed || 1.0;
      const hsl = { h: 0, s: 0, l: 0 };
      baseColor.getHSL(hsl);
      hsl.h += Math.sin(this.time * speed) * variation;
      light.color.setHSL(hsl.h, hsl.s, hsl.l);
    }

    // 更新方向
    if (dynamicEffect.directionVariation && config.direction && (light as any).target) {
      const baseDirection = new THREE.Vector3().copy(config.direction);
      const variation = dynamicEffect.directionVariation;
      const speed = dynamicEffect.speed || 1.0;
      const angle = this.time * speed;
      const newDirection = new THREE.Vector3().copy(baseDirection);
      newDirection.applyAxisAngle(new THREE.Vector3(0, 1, 0), Math.sin(angle) * variation);
      (light as any).target.position.copy(config.position).add(newDirection);
    }
  }

  /**
   * 渲染
   * @param _renderer 渲染器
   */
  public render(_renderer: THREE.WebGLRenderer): void {
    if (!this.initialized || !this.config.enabled || !this.config.enablePostProcessing || !this.composer) {
      return;
    }

    // 使用后处理渲染
    // 实际实现应该调用composer.render()
    // 由于代码简化，这里只是一个占位
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }
}
