/**
 * 高级批处理服务
 * 提供基于优先级的批处理队列、动态批处理大小调整和错误处理机制
 */
import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * 批处理项接口
 */
export interface BatchItem<T> {
  /** 数据 */
  data: T;
  /** 优先级 */
  priority: number;
  /** 添加时间 */
  timestamp: number;
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 标签 */
  tags?: string[];
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 批处理结果接口
 */
export interface BatchResult<T, R> {
  /** 批处理项 */
  item: BatchItem<T>;
  /** 结果 */
  result?: R;
  /** 错误 */
  error?: Error;
  /** 是否成功 */
  success: boolean;
  /** 处理时间（毫秒） */
  processingTime: number;
  /** 重试次数 */
  retryCount: number;
}

/**
 * 批处理统计信息接口
 */
export interface BatchProcessorStats {
  /** 总处理项数 */
  totalProcessed: number;
  /** 成功处理项数 */
  successCount: number;
  /** 失败处理项数 */
  failureCount: number;
  /** 重试次数 */
  retryCount: number;
  /** 丢弃项数 */
  discardedCount: number;
  /** 超时项数 */
  timeoutCount: number;
  /** 平均处理时间（毫秒） */
  averageProcessingTime: number;
  /** 平均批处理大小 */
  averageBatchSize: number;
  /** 最大批处理大小 */
  maxBatchSize: number;
  /** 最小批处理大小 */
  minBatchSize: number;
  /** 当前队列大小 */
  queueSize: number;
  /** 按优先级的队列大小 */
  queueSizeByPriority: Record<number, number>;
  /** 按标签的处理项数 */
  processedByTag: Record<string, number>;
}

/**
 * 批处理选项接口
 */
export interface AdvancedBatchProcessorOptions<T, R> {
  /** 批处理函数 */
  processBatch: (items: BatchItem<T>[]) => Promise<(R | Error)[]>;
  /** 最大批处理大小 */
  maxBatchSize?: number;
  /** 最小批处理大小 */
  minBatchSize?: number;
  /** 初始批处理大小 */
  initialBatchSize?: number;
  /** 批处理间隔（毫秒） */
  batchInterval?: number;
  /** 是否启用自适应批处理大小 */
  enableAdaptiveBatchSize?: boolean;
  /** 自适应批处理大小调整因子 */
  adaptiveFactor?: number;
  /** 是否启用优先级队列 */
  enablePriorityQueue?: boolean;
  /** 默认优先级 */
  defaultPriority?: number;
  /** 优先级级别 */
  priorityLevels?: number[];
  /** 是否启用超时处理 */
  enableTimeout?: boolean;
  /** 默认超时时间（毫秒） */
  defaultTimeout?: number;
  /** 是否启用重试 */
  enableRetry?: boolean;
  /** 默认最大重试次数 */
  defaultMaxRetries?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 是否启用指数退避重试 */
  enableExponentialBackoff?: boolean;
  /** 指数退避因子 */
  backoffFactor?: number;
  /** 是否启用批处理结果事件 */
  enableResultEvents?: boolean;
  /** 是否启用统计信息 */
  enableStats?: boolean;
  /** 统计信息采样间隔（毫秒） */
  statsInterval?: number;
  /** 是否启用调试日志 */
  debug?: boolean;
}

@Injectable()
export class AdvancedBatchProcessorService<T, R> implements OnModuleDestroy {
  private readonly logger = new Logger(AdvancedBatchProcessorService.name);

  // 优先级队列
  private readonly queues: Map<number, BatchItem<T>[]> = new Map();

  // 处理定时器
  private processingTimer: NodeJS.Timeout | null = null;

  // 统计定时器
  private statsTimer: NodeJS.Timeout | null = null;

  // 当前批处理大小
  private currentBatchSize: number;

  // 统计信息
  private stats: BatchProcessorStats = {
    totalProcessed: 0,
    successCount: 0,
    failureCount: 0,
    retryCount: 0,
    discardedCount: 0,
    timeoutCount: 0,
    averageProcessingTime: 0,
    averageBatchSize: 0,
    maxBatchSize: 0,
    minBatchSize: Infinity,
    queueSize: 0,
    queueSizeByPriority: {},
    processedByTag: {},
  };

  // 总处理时间
  private totalProcessingTime = 0;

  // 总批处理大小
  private totalBatchSize = 0;

  // 批处理次数
  private batchCount = 0;

  // 默认选项
  private readonly defaultOptions: Required<AdvancedBatchProcessorOptions<T, R>> = {
    processBatch: async () => [],
    maxBatchSize: 100,
    minBatchSize: 1,
    initialBatchSize: 10,
    batchInterval: 1000,
    enableAdaptiveBatchSize: true,
    adaptiveFactor: 0.1,
    enablePriorityQueue: true,
    defaultPriority: 0,
    priorityLevels: [0, 1, 2, 3, 4, 5],
    enableTimeout: true,
    defaultTimeout: 30000,
    enableRetry: true,
    defaultMaxRetries: 3,
    retryDelay: 1000,
    enableExponentialBackoff: true,
    backoffFactor: 2,
    enableResultEvents: true,
    enableStats: true,
    statsInterval: 60000,
    debug: false,
  };

  // 合并后的选项
  private readonly options: Required<AdvancedBatchProcessorOptions<T, R>>;

  constructor(
    options: AdvancedBatchProcessorOptions<T, R>,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // 合并选项
    this.options = {
      ...this.defaultOptions,
      ...options,
      priorityLevels: [
        ...(options.priorityLevels || this.defaultOptions.priorityLevels),
      ],
    };

    // 初始化批处理大小
    this.currentBatchSize = this.options.initialBatchSize;

    // 初始化优先级队列
    if (this.options.enablePriorityQueue) {
      for (const level of this.options.priorityLevels) {
        this.queues.set(level, []);
        this.stats.queueSizeByPriority[level] = 0;
      }
    } else {
      this.queues.set(this.options.defaultPriority, []);
      this.stats.queueSizeByPriority[this.options.defaultPriority] = 0;
    }

    // 启动处理定时器
    this.startProcessing();

    // 启动统计定时器
    if (this.options.enableStats) {
      this.statsTimer = setInterval(() => {
        this.emitStats();
      }, this.options.statsInterval);
    }

    this.logger.log(`高级批处理服务已初始化，批处理大小: ${this.currentBatchSize}, 间隔: ${this.options.batchInterval}ms`);
  }

  /**
   * 模块销毁
   */
  onModuleDestroy() {
    // 停止处理定时器
    if (this.processingTimer) {
      clearInterval(this.processingTimer);
      this.processingTimer = null;
    }

    // 停止统计定时器
    if (this.statsTimer) {
      clearInterval(this.statsTimer);
      this.statsTimer = null;
    }

    this.logger.log('高级批处理服务已销毁');
  }

  /**
   * 添加批处理项
   * @param data 数据
   * @param options 选项
   * @returns 队列大小
   */
  add(data: T, options: {
    priority?: number;
    timeout?: number;
    maxRetries?: number;
    tags?: string[];
    metadata?: Record<string, any>;
  } = {}): number {
    const priority = options.priority !== undefined
      ? options.priority
      : this.options.defaultPriority;

    // 如果启用优先级队列但优先级不在配置的级别中，使用默认优先级
    const effectivePriority = this.options.enablePriorityQueue && !this.options.priorityLevels.includes(priority)
      ? this.options.defaultPriority
      : priority;

    // 创建批处理项
    const item: BatchItem<T> = {
      data,
      priority: effectivePriority,
      timestamp: Date.now(),
      timeout: options.timeout !== undefined ? options.timeout : this.options.defaultTimeout,
      retries: 0,
      maxRetries: options.maxRetries !== undefined ? options.maxRetries : this.options.defaultMaxRetries,
      tags: options.tags || [],
      metadata: options.metadata || {},
    };

    // 获取对应优先级的队列
    let queue = this.queues.get(effectivePriority);

    // 如果队列不存在，创建新队列
    if (!queue) {
      queue = [];
      this.queues.set(effectivePriority, queue);
      this.stats.queueSizeByPriority[effectivePriority] = 0;
    }

    // 添加到队列
    queue.push(item);

    // 更新统计信息
    this.stats.queueSize++;
    this.stats.queueSizeByPriority[effectivePriority]++;

    if (this.options.debug) {
      this.logger.debug(`添加批处理项，优先级: ${effectivePriority}, 队列大小: ${this.stats.queueSize}`);
    }

    return this.stats.queueSize;
  }

  /**
   * 批量添加处理项
   * @param items 批处理项数组
   * @returns 队列大小
   */
  addBatch(items: Array<{
    data: T;
    priority?: number;
    timeout?: number;
    maxRetries?: number;
    tags?: string[];
    metadata?: Record<string, any>;
  }>): number {
    for (const item of items) {
      this.add(
        item.data,
        {
          priority: item.priority,
          timeout: item.timeout,
          maxRetries: item.maxRetries,
          tags: item.tags,
          metadata: item.metadata,
        }
      );
    }

    return this.stats.queueSize;
  }

  /**
   * 获取统计信息
   * @returns 统计信息
   */
  getStats(): BatchProcessorStats {
    return { ...this.stats };
  }

  /**
   * 清空队列
   */
  clear(): void {
    for (const queue of this.queues.values()) {
      queue.length = 0;
    }

    // 更新统计信息
    this.stats.queueSize = 0;
    for (const priority in this.stats.queueSizeByPriority) {
      this.stats.queueSizeByPriority[priority] = 0;
    }

    this.logger.log('已清空批处理队列');
  }

  /**
   * 启动处理
   */
  private startProcessing(): void {
    if (this.processingTimer) {
      clearInterval(this.processingTimer);
    }

    this.processingTimer = setInterval(() => {
      this.processBatch();
    }, this.options.batchInterval);
  }

  /**
   * 处理一批数据
   */
  private async processBatch(): Promise<void> {
    // 如果队列为空，不处理
    if (this.stats.queueSize === 0) {
      return;
    }

    // 获取要处理的项
    const itemsToProcess = this.getNextBatch();

    // 如果没有要处理的项，不处理
    if (itemsToProcess.length === 0) {
      return;
    }

    if (this.options.debug) {
      this.logger.debug(`处理批次，大小: ${itemsToProcess.length}`);
    }

    const startTime = Date.now();

    try {
      // 处理批次
      const results = await this.options.processBatch(itemsToProcess);

      // 处理结果
      this.handleResults(itemsToProcess, results, startTime);
    } catch (error) {
      // 处理整批错误
      this.handleBatchError(itemsToProcess, error, startTime);
    }
  }

  /**
   * 获取下一批要处理的项
   * @returns 批处理项数组
   */
  private getNextBatch(): BatchItem<T>[] {
    const now = Date.now();
    const batch: BatchItem<T>[] = [];

    // 如果启用优先级队列，按优先级从高到低获取项
    if (this.options.enablePriorityQueue) {
      // 按优先级从高到低排序
      const priorities = [...this.queues.keys()].sort((a, b) => b - a);

      // 从每个优先级队列中获取项，直到达到批处理大小
      for (const priority of priorities) {
        const queue = this.queues.get(priority)!;

        // 检查队列中的项
        for (let i = 0; i < queue.length && batch.length < this.currentBatchSize; i++) {
          const item = queue[i];

          // 检查是否超时
          if (this.options.enableTimeout && item.timeout && now - item.timestamp > item.timeout) {
            // 移除超时项
            queue.splice(i, 1);
            i--;

            // 更新统计信息
            this.stats.queueSize--;
            this.stats.queueSizeByPriority[priority]--;
            this.stats.timeoutCount++;

            // 发出超时事件
            if (this.options.enableResultEvents) {
              this.eventEmitter.emit('batch.timeout', {
                item,
                timestamp: now,
              });
            }

            continue;
          }

          // 添加到批次
          batch.push(item);

          // 从队列中移除
          queue.splice(i, 1);
          i--;

          // 更新统计信息
          this.stats.queueSize--;
          this.stats.queueSizeByPriority[priority]--;
        }

        // 如果已经达到批处理大小，停止获取
        if (batch.length >= this.currentBatchSize) {
          break;
        }
      }
    } else {
      // 不使用优先级队列，直接从默认队列获取
      const queue = this.queues.get(this.options.defaultPriority)!;

      // 获取最多currentBatchSize个项
      while (batch.length < this.currentBatchSize && queue.length > 0) {
        const item = queue[0];

        // 检查是否超时
        if (this.options.enableTimeout && item.timeout && now - item.timestamp > item.timeout) {
          // 移除超时项
          queue.shift();

          // 更新统计信息
          this.stats.queueSize--;
          this.stats.queueSizeByPriority[this.options.defaultPriority]--;
          this.stats.timeoutCount++;

          // 发出超时事件
          if (this.options.enableResultEvents) {
            this.eventEmitter.emit('batch.timeout', {
              item,
              timestamp: now,
            });
          }

          continue;
        }

        // 添加到批次
        batch.push(item);

        // 从队列中移除
        queue.shift();

        // 更新统计信息
        this.stats.queueSize--;
        this.stats.queueSizeByPriority[this.options.defaultPriority]--;
      }
    }

    return batch;
  }

  /**
   * 处理批处理结果
   * @param items 批处理项
   * @param results 结果
   * @param startTime 开始时间
   */
  private handleResults(items: BatchItem<T>[], results: (R | Error)[], startTime: number): void {
    const processingTime = Date.now() - startTime;

    // 更新批处理统计信息
    this.updateBatchStats(items.length, processingTime);

    // 处理每个结果
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const result = results[i];

      // 检查结果是否为错误
      if (result instanceof Error) {
        this.handleItemError(item, result, processingTime);
      } else {
        this.handleItemSuccess(item, result, processingTime);
      }
    }

    // 如果启用自适应批处理大小，调整批处理大小
    if (this.options.enableAdaptiveBatchSize) {
      this.adjustBatchSize(processingTime);
    }
  }

  /**
   * 处理批处理错误
   * @param items 批处理项
   * @param error 错误
   * @param startTime 开始时间
   */
  private handleBatchError(items: BatchItem<T>[], error: any, startTime: number): void {
    const processingTime = Date.now() - startTime;

    this.logger.error(`批处理错误: ${error.message}`, error.stack);

    // 更新批处理统计信息
    this.updateBatchStats(items.length, processingTime);

    // 处理每个项的错误
    for (const item of items) {
      this.handleItemError(item, error, processingTime);
    }

    // 如果启用自适应批处理大小，减小批处理大小
    if (this.options.enableAdaptiveBatchSize) {
      // 批处理错误，减小批处理大小
      this.currentBatchSize = Math.max(
        this.options.minBatchSize,
        Math.floor(this.currentBatchSize * (1 - this.options.adaptiveFactor))
      );

      if (this.options.debug) {
        this.logger.debug(`批处理错误，减小批处理大小至: ${this.currentBatchSize}`);
      }
    }
  }

  /**
   * 处理项成功
   * @param item 批处理项
   * @param result 结果
   * @param processingTime 处理时间
   */
  private handleItemSuccess(item: BatchItem<T>, result: R, processingTime: number): void {
    // 更新统计信息
    this.stats.totalProcessed++;
    this.stats.successCount++;

    // 更新标签统计
    if (item.tags) {
      for (const tag of item.tags) {
        this.stats.processedByTag[tag] = (this.stats.processedByTag[tag] || 0) + 1;
      }
    }

    // 发出成功事件
    if (this.options.enableResultEvents) {
      const batchResult: BatchResult<T, R> = {
        item,
        result,
        success: true,
        processingTime,
        retryCount: item.retries || 0,
      };

      this.eventEmitter.emit('batch.success', batchResult);
    }
  }

  /**
   * 处理项错误
   * @param item 批处理项
   * @param error 错误
   * @param processingTime 处理时间
   */
  private handleItemError(item: BatchItem<T>, error: Error, processingTime: number): void {
    // 检查是否需要重试
    if (this.options.enableRetry && item.retries !== undefined && item.maxRetries !== undefined && item.retries < item.maxRetries) {
      // 增加重试次数
      item.retries++;

      // 更新统计信息
      this.stats.retryCount++;

      // 计算重试延迟
      let retryDelay = this.options.retryDelay;

      // 如果启用指数退避，计算退避延迟
      if (this.options.enableExponentialBackoff) {
        retryDelay = this.options.retryDelay * Math.pow(this.options.backoffFactor, item.retries - 1);
      }

      if (this.options.debug) {
        this.logger.debug(`重试批处理项，重试次数: ${item.retries}/${item.maxRetries}, 延迟: ${retryDelay}ms`);
      }

      // 延迟后重新添加到队列
      setTimeout(() => {
        // 获取对应优先级的队列
        let queue = this.queues.get(item.priority);

        // 如果队列不存在，创建新队列
        if (!queue) {
          queue = [];
          this.queues.set(item.priority, queue);
          this.stats.queueSizeByPriority[item.priority] = 0;
        }

        // 添加到队列
        queue.push(item);

        // 更新统计信息
        this.stats.queueSize++;
        this.stats.queueSizeByPriority[item.priority]++;

        // 发出重试事件
        if (this.options.enableResultEvents) {
          this.eventEmitter.emit('batch.retry', {
            item,
            error,
            retryCount: item.retries,
            timestamp: Date.now(),
          });
        }
      }, retryDelay);
    } else {
      // 不重试，处理为失败
      this.stats.totalProcessed++;
      this.stats.failureCount++;
      this.stats.discardedCount++;

      // 更新标签统计
      if (item.tags) {
        for (const tag of item.tags) {
          this.stats.processedByTag[tag] = (this.stats.processedByTag[tag] || 0) + 1;
        }
      }

      // 发出失败事件
      if (this.options.enableResultEvents) {
        const batchResult: BatchResult<T, R> = {
          item,
          error,
          success: false,
          processingTime,
          retryCount: item.retries || 0,
        };

        this.eventEmitter.emit('batch.failure', batchResult);
      }
    }
  }

  /**
   * 更新批处理统计信息
   * @param batchSize 批处理大小
   * @param processingTime 处理时间
   */
  private updateBatchStats(batchSize: number, processingTime: number): void {
    // 更新总处理时间
    this.totalProcessingTime += processingTime;

    // 更新总批处理大小
    this.totalBatchSize += batchSize;

    // 更新批处理次数
    this.batchCount++;

    // 更新平均处理时间
    this.stats.averageProcessingTime = this.totalProcessingTime / this.batchCount;

    // 更新平均批处理大小
    this.stats.averageBatchSize = this.totalBatchSize / this.batchCount;

    // 更新最大批处理大小
    this.stats.maxBatchSize = Math.max(this.stats.maxBatchSize, batchSize);

    // 更新最小批处理大小
    this.stats.minBatchSize = Math.min(this.stats.minBatchSize, batchSize);
  }

  /**
   * 调整批处理大小
   * @param processingTime 处理时间
   */
  private adjustBatchSize(processingTime: number): void {
    // 如果处理时间小于批处理间隔的一半，增加批处理大小
    if (processingTime < this.options.batchInterval / 2) {
      this.currentBatchSize = Math.min(
        this.options.maxBatchSize,
        Math.floor(this.currentBatchSize * (1 + this.options.adaptiveFactor))
      );

      if (this.options.debug) {
        this.logger.debug(`处理时间较短，增加批处理大小至: ${this.currentBatchSize}`);
      }
    }
    // 如果处理时间接近批处理间隔，减小批处理大小
    else if (processingTime > this.options.batchInterval * 0.8) {
      this.currentBatchSize = Math.max(
        this.options.minBatchSize,
        Math.floor(this.currentBatchSize * (1 - this.options.adaptiveFactor))
      );

      if (this.options.debug) {
        this.logger.debug(`处理时间较长，减小批处理大小至: ${this.currentBatchSize}`);
      }
    }
  }

  /**
   * 发送统计信息
   */
  private emitStats(): void {
    if (this.options.enableStats) {
      this.eventEmitter.emit('batch.stats', { ...this.stats });

      if (this.options.debug) {
        this.logger.debug(
          `批处理统计: 总处理=${this.stats.totalProcessed}, 成功=${this.stats.successCount}, ` +
          `失败=${this.stats.failureCount}, 重试=${this.stats.retryCount}, 丢弃=${this.stats.discardedCount}, ` +
          `队列大小=${this.stats.queueSize}, 平均处理时间=${this.stats.averageProcessingTime.toFixed(2)}ms, ` +
          `平均批处理大小=${this.stats.averageBatchSize.toFixed(2)}, 当前批处理大小=${this.currentBatchSize}`
        );
      }
    }
  }
}
