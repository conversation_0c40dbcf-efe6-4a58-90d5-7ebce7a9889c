FROM node:22-alpine AS builder

WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制资产服务代码
COPY asset-service/package*.json ./asset-service/
WORKDIR /app/asset-service
RUN npm install

# 复制资产服务源代码
COPY asset-service/ ./

RUN npm run build

FROM node:22-alpine

WORKDIR /app

COPY --from=builder /app/asset-service/package*.json ./
COPY --from=builder /app/asset-service/dist ./dist

RUN npm install --only=production

# 创建上传目录
RUN mkdir -p /app/uploads/models /app/uploads/textures /app/uploads/audio /app/uploads/other

EXPOSE 3003 4003

CMD ["node", "dist/main.js"]
