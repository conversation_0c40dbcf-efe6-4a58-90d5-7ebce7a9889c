/**
 * 网络用户组件
 * 用于标识和管理网络用户
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { NetworkUserRole, NetworkUserState } from '../NetworkUser';
/**
 * 网络用户组件属性
 */
export interface NetworkUserComponentProps {
    /** 用户ID */
    userId: string;
    /** 用户名 */
    username: string;
    /** 显示名称 */
    displayName?: string;
    /** 头像URL */
    avatarUrl?: string;
    /** 用户状态 */
    state?: NetworkUserState;
    /** 用户角色 */
    role?: NetworkUserRole;
    /** 是否是本地用户 */
    isLocal?: boolean;
    /** 自定义数据 */
    customData?: Record<string, any>;
}
/**
 * 网络用户组件
 * 用于标识和管理网络用户
 */
export declare class NetworkUserComponent extends Component {
    /** 组件类型 */
    static readonly type = "NetworkUser";
    /** 用户ID */
    userId: string;
    /** 用户名 */
    username: string;
    /** 显示名称 */
    displayName: string;
    /** 头像URL */
    avatarUrl: string;
    /** 用户状态 */
    state: NetworkUserState;
    /** 用户角色 */
    role: NetworkUserRole;
    /** 是否是本地用户 */
    isLocal: boolean;
    /** 自定义数据 */
    customData: Record<string, any>;
    /** 加入时间 */
    joinTime: number;
    /** 最后活动时间 */
    lastActiveTime: number;
    /** 是否有待同步的更改 */
    private hasPendingChanges;
    /** 待同步的属性 */
    private pendingProperties;
    /**
     * 创建网络用户组件
     * @param entity 实体
     * @param props 组件属性
     */
    constructor(entity: Entity, props: NetworkUserComponentProps);
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 设置用户状态
     * @param state 用户状态
     */
    setState(state: NetworkUserState): void;
    /**
     * 设置用户角色
     * @param role 用户角色
     */
    setRole(role: NetworkUserRole): void;
    /**
     * 设置自定义数据
     * @param key 键
     * @param value 值
     */
    setCustomData(key: string, value: any): void;
    /**
     * 获取自定义数据
     * @param key 键
     * @returns 值
     */
    getCustomData(key: string): any;
    /**
     * 更新活动时间
     */
    updateActivityTime(): void;
    /**
     * 获取用户数据
     * @returns 用户数据
     */
    getUserData(): any;
    /**
     * 应用用户数据
     * @param data 用户数据
     */
    applyUserData(data: any): void;
    /**
     * 标记属性为待同步
     * @param property 属性名
     */
    markPropertyDirty(property: string): void;
    /**
     * 标记所有属性为待同步
     */
    markAllPropertiesDirty(): void;
    /**
     * 同步用户数据
     */
    sync(): void;
    /**
     * 销毁组件
     */
    dispose(): void;
}
