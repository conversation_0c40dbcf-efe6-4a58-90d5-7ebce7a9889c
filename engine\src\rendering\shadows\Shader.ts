/**
 * 级联阴影映射着色器
 * 用于实现级联阴影映射的着色器代码
 */

/**
 * 着色器类
 */
export class Shader {
  /**
   * 生成lights_pars_begin着色器代码
   * @returns 着色器代码
   */
  public static lights_pars_begin(): string {
    return /* glsl */`
uniform sampler2D directionalShadowMap[ NUM_DIR_LIGHT_SHADOWS ];
varying vec4 vDirectionalShadowCoord[ NUM_DIR_LIGHT_SHADOWS ];
struct DirectionalLightShadow {
  float shadowBias;
  float shadowNormalBias;
  float shadowRadius;
  vec2 shadowMapSize;
};
uniform DirectionalLightShadow directionalLightShadows[ NUM_DIR_LIGHT_SHADOWS ];

// CSM specific variables and constants
#ifdef USE_CSM
  uniform vec2 CSM_cascades[CSM_CASCADES];
  uniform float cameraNear;
  uniform float shadowFar;
#endif

// helper functions from three.js
#if NUM_DIR_LIGHT_SHADOWS > 0
  float getShadow(sampler2D shadowMap, vec2 shadowMapSize, float shadowBias, float shadowRadius, vec4 shadowCoord) {
    float shadow = 1.0;
    shadowCoord.xyz /= shadowCoord.w;
    shadowCoord.z += shadowBias;
    bool inFrustum = shadowCoord.x >= 0.0 && shadowCoord.x <= 1.0 && shadowCoord.y >= 0.0 && shadowCoord.y <= 1.0;
    bool frustumTest = inFrustum && shadowCoord.z <= 1.0;
    if (frustumTest) {
      #ifdef USE_PCSS
        shadow = PCSS(shadowMap, shadowCoord.xy, shadowCoord.z, shadowMapSize, shadowRadius);
      #else
        #if defined(SHADOWMAP_TYPE_PCF)
          vec2 texelSize = vec2(1.0) / shadowMapSize;
          float dx0 = -texelSize.x * shadowRadius;
          float dy0 = -texelSize.y * shadowRadius;
          float dx1 = +texelSize.x * shadowRadius;
          float dy1 = +texelSize.y * shadowRadius;
          float dx2 = dx0 / 2.0;
          float dy2 = dy0 / 2.0;
          float dx3 = dx1 / 2.0;
          float dy3 = dy1 / 2.0;
          shadow = (
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx0, dy0), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(0.0, dy0), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx1, dy0), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx2, dy2), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(0.0, dy2), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx3, dy2), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx0, 0.0), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx2, 0.0), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy, shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx3, 0.0), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx1, 0.0), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx2, dy3), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(0.0, dy3), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx3, dy3), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx0, dy1), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(0.0, dy1), shadowCoord.z) +
            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx1, dy1), shadowCoord.z)
          ) * (1.0 / 17.0);
        #elif defined(SHADOWMAP_TYPE_PCF_SOFT)
          vec2 texelSize = vec2(1.0) / shadowMapSize;
          float dx = texelSize.x;
          float dy = texelSize.y;
          vec2 uv = shadowCoord.xy;
          vec2 f = fract(uv * shadowMapSize + 0.5);
          uv -= f * texelSize;
          shadow = (
            texture2DCompare(shadowMap, uv, shadowCoord.z) +
            texture2DCompare(shadowMap, uv + vec2(dx, 0.0), shadowCoord.z) +
            texture2DCompare(shadowMap, uv + vec2(0.0, dy), shadowCoord.z) +
            texture2DCompare(shadowMap, uv + texelSize, shadowCoord.z) +
            mix(
              mix(texture2DCompare(shadowMap, uv, shadowCoord.z),
                texture2DCompare(shadowMap, uv + vec2(dx, 0.0), shadowCoord.z), f.x),
              mix(texture2DCompare(shadowMap, uv + vec2(0.0, dy), shadowCoord.z),
                texture2DCompare(shadowMap, uv + texelSize, shadowCoord.z), f.x), f.y)
          ) * (1.0 / 5.0);
        #else // no PCF
          shadow = texture2DCompare(shadowMap, shadowCoord.xy, shadowCoord.z);
        #endif
      #endif
    }
    return shadow;
  }
#endif
`;
  }

  /**
   * 生成lights_fragment_begin着色器代码
   * @param cascades 级联数量
   * @returns 着色器代码
   */
  public static lights_fragment_begin(cascades: number): string {
    return /* glsl */`
/**
 * This is a template that can be used to light a material, it uses pluggable
 * RenderEquations (RE)for specific lighting scenarios.
 *
 * Instructions for use:
 * - Ensure that both RE_Direct, RE_IndirectDiffuse and RE_IndirectSpecular are defined
 * - Create a material parameter that is to be passed as the third parameter to your lighting functions.
 *
 * TODO:
 * - Add area light support.
 * - Add sphere light support.
 * - Add diffuse light probe (irradiance cubemap) support.
 */

GeometricContext geometry;

geometry.position = - vViewPosition;
geometry.normal = normal;
geometry.viewDir = ( isOrthographic ) ? vec3( 0, 0, 1 ) : normalize( vViewPosition );

#ifdef USE_CLEARCOAT

  geometry.clearcoatNormal = clearcoatNormal;

#endif

#ifdef USE_IRIDESCENCE

  float dotNVi = saturate( dot( normal, geometry.viewDir ) );

  if ( material.iridescenceThickness == 0.0 ) {

    material.iridescence = 0.0;

  } else {

    material.iridescence = saturate( material.iridescence );

  }

  if ( material.iridescence > 0.0 ) {

    material.iridescenceFresnel = evalIridescence( 1.0, material.iridescenceIOR, dotNVi, material.iridescenceThickness, material.specularColor );

    // Iridescence F0 approximation
    material.iridescenceF0 = Schlick_to_F0( material.iridescenceFresnel, 1.0, dotNVi );

  }

#endif

IncidentLight directLight;

#if ( NUM_POINT_LIGHTS > 0 ) && defined( RE_Direct )

  PointLight pointLight;
  #if defined( USE_SHADOWMAP ) && NUM_POINT_LIGHT_SHADOWS > 0
  PointLightShadow pointLightShadow;
  #endif

  #pragma unroll_loop_start
  for ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {

    pointLight = pointLights[ i ];

    getPointLightInfo( pointLight, geometry, directLight );

    #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_POINT_LIGHT_SHADOWS )
    pointLightShadow = pointLightShadows[ i ];
    directLight.color *= ( directLight.visible && receiveShadow ) ? getPointShadow( pointShadowMap[ i ], pointLightShadow.shadowMapSize, pointLightShadow.shadowBias, pointLightShadow.shadowRadius, vPointShadowCoord[ i ], pointLightShadow.shadowCameraNear, pointLightShadow.shadowCameraFar ) : 1.0;
    #endif

    RE_Direct( directLight, geometry, material, reflectedLight );

  }
  #pragma unroll_loop_end

#endif

#if ( NUM_SPOT_LIGHTS > 0 ) && defined( RE_Direct )

  SpotLight spotLight;
  vec4 spotColor;
  vec3 spotLightCoord;
  bool inSpotLightMap;

  #if defined( USE_SHADOWMAP ) && NUM_SPOT_LIGHT_SHADOWS > 0
  SpotLightShadow spotLightShadow;
  #endif

  #pragma unroll_loop_start
  for ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {

    spotLight = spotLights[ i ];

    getSpotLightInfo( spotLight, geometry, directLight );

    // spot lights are ordered [shadows with maps, shadows without maps, maps without shadows, none]
    #if ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS )
    #define SPOT_LIGHT_MAP_INDEX UNROLLED_LOOP_INDEX
    #elif ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )
    #define SPOT_LIGHT_MAP_INDEX NUM_SPOT_LIGHT_MAPS
    #else
    #define SPOT_LIGHT_MAP_INDEX ( UNROLLED_LOOP_INDEX - NUM_SPOT_LIGHT_SHADOWS + NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS )
    #endif

    #if ( SPOT_LIGHT_MAP_INDEX < NUM_SPOT_LIGHT_MAPS )
      spotLightCoord = vSpotLightCoord[ i ].xyz / vSpotLightCoord[ i ].w;
      inSpotLightMap = all( lessThan( abs( spotLightCoord * 2. - 1. ), vec3( 1.0 ) ) );
      spotColor = texture2D( spotLightMap[ SPOT_LIGHT_MAP_INDEX ], spotLightCoord.xy );
      directLight.color = inSpotLightMap ? directLight.color * spotColor.rgb : directLight.color;
    #endif

    #undef SPOT_LIGHT_MAP_INDEX

    #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )
    spotLightShadow = spotLightShadows[ i ];
    directLight.color *= ( directLight.visible && receiveShadow ) ? getShadow( spotShadowMap[ i ], spotLightShadow.shadowMapSize, spotLightShadow.shadowBias, spotLightShadow.shadowRadius, vSpotLightCoord[ i ] ) : 1.0;
    #endif

    RE_Direct( directLight, geometry, material, reflectedLight );

  }
  #pragma unroll_loop_end

#endif

#if ( NUM_DIR_LIGHTS > 0 ) && defined( RE_Direct )

  DirectionalLight directionalLight;
  #if defined( USE_SHADOWMAP ) && NUM_DIR_LIGHT_SHADOWS > 0
  DirectionalLightShadow directionalLightShadow;
  #endif

  #ifdef USE_CSM
  float linearDepth = (vViewPosition.z + cameraNear) / (shadowFar - cameraNear);
  float cascadeCenter;
  float cascadeSize;
  int cascadeIndex;
  #ifdef CSM_FADE
  float cascadeRatio;
  float ratio;
  #endif
  #endif

  #pragma unroll_loop_start
  for ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {

    directionalLight = directionalLights[ i ];
    getDirectionalLightInfo( directionalLight, geometry, directLight );

    #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_DIR_LIGHT_SHADOWS )
    
    #ifdef USE_CSM
    // 计算级联索引
    cascadeIndex = -1;
    for (int j = 0; j < ${cascades}; j++) {
      if (linearDepth >= CSM_cascades[j].x && linearDepth < CSM_cascades[j].y) {
        cascadeIndex = j;
        cascadeCenter = (CSM_cascades[j].x + CSM_cascades[j].y) * 0.5;
        cascadeSize = CSM_cascades[j].y - CSM_cascades[j].x;
        break;
      }
    }

    if (cascadeIndex >= 0) {
      vec3 prevColor = directLight.color;
      directionalLightShadow = directionalLightShadows[ i ];
      directLight.color *= (directLight.visible && receiveShadow) ? getShadow(
        directionalShadowMap[ i ],
        directionalLightShadow.shadowMapSize,
        directionalLightShadow.shadowBias,
        directionalLightShadow.shadowRadius,
        vDirectionalShadowCoord[ i ]
      ) : 1.0;

      #ifdef CSM_FADE
      // 计算级联边缘的淡入淡出
      bool shouldFadeLastCascade = cascadeIndex == ${cascades} - 1 && linearDepth > cascadeCenter;
      if (shouldFadeLastCascade) {
        ratio = (linearDepth - cascadeCenter) / (cascadeSize * 0.5);
        ratio = smoothstep(0.0, 1.0, ratio);
        directLight.color = mix(directLight.color, prevColor, ratio);
      }
      #endif
    }
    #else
    directionalLightShadow = directionalLightShadows[ i ];
    directLight.color *= ( directLight.visible && receiveShadow ) ? getShadow( directionalShadowMap[ i ], directionalLightShadow.shadowMapSize, directionalLightShadow.shadowBias, directionalLightShadow.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;
    #endif

    #endif

    RE_Direct( directLight, geometry, material, reflectedLight );

  }
  #pragma unroll_loop_end

#endif

#if ( NUM_RECT_AREA_LIGHTS > 0 ) && defined( RE_Direct_RectArea )

  RectAreaLight rectAreaLight;

  #pragma unroll_loop_start
  for ( int i = 0; i < NUM_RECT_AREA_LIGHTS; i ++ ) {

    rectAreaLight = rectAreaLights[ i ];
    RE_Direct_RectArea( rectAreaLight, geometry, material, reflectedLight );

  }
  #pragma unroll_loop_end

#endif

#if defined( RE_IndirectDiffuse )

  vec3 iblIrradiance = vec3( 0.0 );

  vec3 irradiance = getAmbientLightIrradiance( ambientLightColor );

  irradiance += getLightProbeIrradiance( lightProbe, geometry.normal );

  #if ( NUM_HEMI_LIGHTS > 0 )

    #pragma unroll_loop_start
    for ( int i = 0; i < NUM_HEMI_LIGHTS; i ++ ) {

      irradiance += getHemisphereLightIrradiance( hemisphereLights[ i ], geometry.normal );

    }
    #pragma unroll_loop_end

  #endif

#endif

#if defined( RE_IndirectSpecular )

  vec3 radiance = vec3( 0.0 );
  vec3 clearcoatRadiance = vec3( 0.0 );

#endif
`;
  }
}
