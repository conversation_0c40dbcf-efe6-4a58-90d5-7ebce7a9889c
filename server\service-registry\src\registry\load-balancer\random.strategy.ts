/**
 * 随机负载均衡策略
 */
import { Injectable } from '@nestjs/common';
import { BaseLoadBalancerStrategy } from './base-load-balancer.strategy';
import { LoadBalancerContext, LoadBalancerAlgorithm } from './load-balancer.interface';
import { ServiceInstanceEntity } from '../entities/service-instance.entity';

@Injectable()
export class RandomLoadBalancerStrategy extends BaseLoadBalancerStrategy {
  constructor() {
    super(LoadBalancerAlgorithm.RANDOM);
    this.config.algorithm = LoadBalancerAlgorithm.RANDOM;
  }
  
  /**
   * 随机选择服务实例
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  protected async doSelect(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): Promise<ServiceInstanceEntity | null> {
    if (!instances || instances.length === 0) {
      return null;
    }
    
    // 随机选择一个实例
    const index = Math.floor(Math.random() * instances.length);
    return instances[index];
  }
}
