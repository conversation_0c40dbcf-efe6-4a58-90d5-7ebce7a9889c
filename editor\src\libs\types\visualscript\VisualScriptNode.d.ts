/**
 * 可视化脚本节点基类
 */
export interface NodeInput {
    name: string;
    type: string;
    label: string;
    defaultValue?: any;
}
export interface NodeOutput {
    name: string;
    type: string;
    label: string;
}
export interface NodeConnection {
    fromNode: string;
    fromOutput: string;
    toNode: string;
    toInput: string;
}
export interface VisualScriptContext {
    deltaTime?: number;
    time?: number;
    [key: string]: any;
}
export declare abstract class VisualScriptNode {
    /** 节点ID */
    readonly id: string;
    /** 节点类型 */
    readonly nodeType: string;
    /** 节点名称 */
    name: string;
    /** 输入端口 */
    protected inputs: Map<string, NodeInput>;
    /** 输出端口 */
    protected outputs: Map<string, NodeOutput>;
    /** 执行上下文 */
    private context;
    constructor(nodeType: string, name: string, id?: string);
    /**
     * 生成唯一ID
     */
    private generateId;
    /**
     * 添加输入端口
     */
    protected addInput(name: string, type: string, label: string, defaultValue?: any): void;
    /**
     * 添加输出端口
     */
    protected addOutput(name: string, type: string, label: string): void;
    /**
     * 获取输入端口
     */
    getInputs(): NodeInput[];
    /**
     * 获取输出端口
     */
    getOutputs(): NodeOutput[];
    /**
     * 获取输入端口
     */
    getInput(name: string): NodeInput | null;
    /**
     * 获取输出端口
     */
    getOutput(name: string): NodeOutput | null;
    /**
     * 设置执行上下文
     */
    setContext(context: VisualScriptContext): void;
    /**
     * 获取执行上下文
     */
    getContext(): VisualScriptContext | null;
    /**
     * 执行节点
     * @param inputs 输入值
     * @returns 输出值
     */
    abstract execute(inputs?: any): any;
    /**
     * 验证输入
     */
    validateInputs(inputs: any): boolean;
    /**
     * 获取输入值（包含默认值）
     */
    protected getInputValue(inputs: any, name: string): any;
    /**
     * 克隆节点
     */
    clone(): VisualScriptNode;
    /**
     * 序列化节点
     */
    serialize(): any;
    /**
     * 反序列化节点
     */
    static deserialize(data: any): VisualScriptNode;
}
