/**
 * 帮助页面
 * 用于显示完整的帮助内容
 */
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Layout, Menu, Input, Button, Card, List, Tag, Typography, Breadcrumb, Tabs, Empty, Spin } from 'antd';
import {
  SearchOutlined,
  BookOutlined,
  VideoCameraOutlined,
  QuestionCircleOutlined,
  HomeOutlined,
  LeftOutlined,
  RightOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import MarkdownViewer from '../components/common/MarkdownViewer';
import helpSystem, { HelpContent } from '../components/help/HelpSystem';
import VideoPlayer from '../components/help/VideoPlayer';
import './HelpPage.less';

const { Header, Sider, Content } = Layout;
const { Title, Paragraph, Text, Link } = Typography;

/**
 * 帮助页面组件
 */
const HelpPage: React.FC = () => {
  const { t } = useTranslation();
  const { helpId } = useParams<{ helpId?: string }>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<HelpContent[]>([]);
  const [currentHelp, setCurrentHelp] = useState<HelpContent | undefined>(undefined);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);
  const [categoryContents, setCategoryContents] = useState<HelpContent[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [showVideo, setShowVideo] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('content');

  // 初始化
  useEffect(() => {
    // 获取分类
    setCategories(helpSystem.getHelpCategories());

    // 如果有指定的帮助ID，显示对应内容
    if (helpId) {
      const content = helpSystem.getHelpContent(helpId);
      if (content) {
        setCurrentHelp(content);
        setSelectedCategory(content.category);
      } else {
        // 如果找不到指定的帮助内容，显示默认内容
        const defaultContent = helpSystem.getAllHelpContents()[0];
        if (defaultContent) {
          setCurrentHelp(defaultContent);
          setSelectedCategory(defaultContent.category);
          navigate(`/help/${defaultContent.id}`);
        }
      }
    } else {
      // 如果没有指定帮助ID，显示默认内容
      const defaultContent = helpSystem.getAllHelpContents()[0];
      if (defaultContent) {
        setCurrentHelp(defaultContent);
        setSelectedCategory(defaultContent.category);
        navigate(`/help/${defaultContent.id}`);
      }
    }

    setLoading(false);
  }, [helpId, navigate]);

  // 当选择的分类变化时，更新分类内容
  useEffect(() => {
    if (selectedCategory) {
      const contents = helpSystem.getAllHelpContents()
        .filter(content => content.category === selectedCategory);
      setCategoryContents(contents);
    }
  }, [selectedCategory]);

  // 处理搜索
  const handleSearch = () => {
    if (!searchQuery.trim()) return;
    
    setLoading(true);
    const results = helpSystem.searchHelp(searchQuery);
    setSearchResults(results);
    setLoading(false);
    setActiveTab('search');
  };

  // 处理帮助项点击
  const handleHelpItemClick = (helpId: string) => {
    const content = helpSystem.getHelpContent(helpId);
    if (content) {
      setCurrentHelp(content);
      setSelectedCategory(content.category);
      navigate(`/help/${helpId}`);
    }
  };

  // 处理分类点击
  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
    const contents = helpSystem.getAllHelpContents()
      .filter(content => content.category === category);
    if (contents.length > 0) {
      setCurrentHelp(contents[0]);
      navigate(`/help/${contents[0].id}`);
    }
  };

  // 处理上一篇/下一篇
  const handleNavigation = (direction: 'prev' | 'next') => {
    if (!currentHelp || !selectedCategory) return;
    
    const currentIndex = categoryContents.findIndex(content => content.id === currentHelp.id);
    if (currentIndex === -1) return;
    
    let newIndex;
    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : categoryContents.length - 1;
    } else {
      newIndex = currentIndex < categoryContents.length - 1 ? currentIndex + 1 : 0;
    }
    
    const newContent = categoryContents[newIndex];
    setCurrentHelp(newContent);
    navigate(`/help/${newContent.id}`);
  };

  // 渲染帮助内容
  const renderHelpContent = () => {
    if (loading) {
      return <Spin size="large" />;
    }

    if (!currentHelp) {
      return (
        <Empty
          description={t('help.noContentSelected')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <div className="help-content">
        <Breadcrumb className="help-breadcrumb">
          <Breadcrumb.Item>
            <Link href="/help"><HomeOutlined /> {t('help.home')}</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link onClick={() => handleCategoryClick(currentHelp.category)}>
              {currentHelp.category}
            </Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{currentHelp.title}</Breadcrumb.Item>
        </Breadcrumb>

        <Title level={2}>{currentHelp.title}</Title>
        
        <div className="help-tags">
          <Tag color="blue">{currentHelp.category}</Tag>
          {currentHelp.tags.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </div>
        
        <div className="markdown-content">
          <MarkdownViewer content={currentHelp.content} />
        </div>
        
        {currentHelp.videoUrl && (
          <div className="video-section">
            <Title level={3}>{t('help.relatedVideo')}</Title>
            {showVideo ? (
              <VideoPlayer url={currentHelp.videoUrl} />
            ) : (
              <Button 
                type="primary" 
                icon={<VideoCameraOutlined />}
                onClick={() => setShowVideo(true)}
              >
                {t('help.watchVideo')}
              </Button>
            )}
          </div>
        )}
        
        {currentHelp.relatedTopics && currentHelp.relatedTopics.length > 0 && (
          <div className="related-topics">
            <Title level={3}>{t('help.relatedTopics')}</Title>
            <List
              itemLayout="horizontal"
              dataSource={currentHelp.relatedTopics.map(id => helpSystem.getHelpContent(id)).filter(Boolean) as HelpContent[]}
              renderItem={item => (
                <List.Item
                  key={item.id}
                  onClick={() => handleHelpItemClick(item.id)}
                  className="help-list-item"
                >
                  <List.Item.Meta
                    title={item.title}
                    description={
                      <div>
                        <Tag color="blue">{item.category}</Tag>
                        {item.tags.slice(0, 2).map(tag => (
                          <Tag key={tag}>{tag}</Tag>
                        ))}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </div>
        )}
        
        <div className="help-navigation">
          <Button
            type="default"
            icon={<LeftOutlined />}
            onClick={() => handleNavigation('prev')}
          >
            {t('help.prevTopic')}
          </Button>
          <Button
            type="default"
            icon={<RightOutlined />}
            onClick={() => handleNavigation('next')}
          >
            {t('help.nextTopic')}
          </Button>
        </div>
      </div>
    );
  };

  // 渲染搜索结果
  const renderSearchResults = () => {
    if (loading) {
      return <Spin size="large" />;
    }

    if (searchResults.length === 0) {
      return (
        <Empty
          description={t('help.noSearchResults')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <List
        itemLayout="vertical"
        dataSource={searchResults}
        renderItem={item => (
          <List.Item
            key={item.id}
            onClick={() => handleHelpItemClick(item.id)}
            className="help-list-item"
          >
            <List.Item.Meta
              title={item.title}
              description={
                <div>
                  <Tag color="blue">{item.category}</Tag>
                  {item.tags.slice(0, 3).map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </div>
              }
            />
            <Paragraph ellipsis={{ rows: 2 }}>
              {item.content.substring(0, 200)}...
            </Paragraph>
          </List.Item>
        )}
      />
    );
  };

  return (
    <Layout className="help-page">
      <Header className="help-header">
        <div className="logo">DL（Digital Learning）引擎帮助中心</div>
        <div className="search-container">
          <Input
            placeholder={t('help.searchPlaceholder') || '搜索帮助内容'}
            prefix={<SearchOutlined />}
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            onPressEnter={handleSearch}
            suffix={
              <Button
                type="primary"
                size="small"
                onClick={handleSearch}
                disabled={!searchQuery.trim()}
              >
                {t('help.search')}
              </Button>
            }
          />
        </div>
      </Header>
      <Layout>
        <Sider width={250} className="help-sider">
          <Menu
            mode="inline"
            selectedKeys={[selectedCategory || '']}
            style={{ height: '100%', borderRight: 0 }}
            onClick={({ key }) => handleCategoryClick(key)}
          >
            {categories.map(category => (
              <Menu.Item key={category} icon={<BookOutlined />}>
                {category}
              </Menu.Item>
            ))}
          </Menu>
        </Sider>
        <Layout className="help-content-layout">
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            className="help-tabs"
            items={[
              {
                key: 'content',
                label: (
                  <span>
                    <BookOutlined />
                    {t('help.content') || '内容'}
                  </span>
                ),
                children: (
                  <Content className="help-main-content">
                    {renderHelpContent()}
                  </Content>
                )
              },
              {
                key: 'search',
                label: (
                  <span>
                    <SearchOutlined />
                    {t('help.search') || '搜索'}
                    {searchResults.length > 0 && <Tag>{searchResults.length}</Tag>}
                  </span>
                ),
                children: (
                  <Content className="help-main-content">
                    {renderSearchResults()}
                  </Content>
                )
              },
              {
                key: 'videos',
                label: (
                  <span>
                    <VideoCameraOutlined />
                    {t('help.videos') || '视频'}
                  </span>
                ),
                children: (
                  <Content className="help-main-content">
                    <Card title={t('help.videoTutorials') || '视频教程'}>
                      <Text>{t('help.comingSoon') || '即将推出'}</Text>
                    </Card>
                  </Content>
                )
              },
              {
                key: 'faq',
                label: (
                  <span>
                    <QuestionCircleOutlined />
                    {t('help.faq') || '常见问题'}
                  </span>
                ),
                children: (
                  <Content className="help-main-content">
                    <Card title={t('help.frequentlyAskedQuestions') || '常见问题'}>
                      <Text>{t('help.comingSoon') || '即将推出'}</Text>
                    </Card>
                  </Content>
                )
              }
            ]}
          />
        </Layout>
      </Layout>
    </Layout>
  );
};

export default HelpPage;
