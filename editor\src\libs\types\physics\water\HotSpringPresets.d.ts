/**
 * 温泉预设
 * 提供各种类型的温泉预设配置
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { HotSpringType } from './HotSpringComponent';
/**
 * 温泉预设配置
 */
export interface HotSpringPresetConfig {
    /** 预设类型 */
    type: HotSpringType;
    /** 温泉宽度 */
    width?: number;
    /** 温泉高度 */
    height?: number;
    /** 温泉深度 */
    depth?: number;
    /** 温泉位置 */
    position?: THREE.Vector3;
    /** 温泉旋转 */
    rotation?: THREE.Euler;
    /** 温泉颜色 */
    color?: THREE.Color;
    /** 温泉不透明度 */
    opacity?: number;
    /** 温泉温度 */
    temperature?: number;
    /** 温泉波动强度 */
    waveAmplitude?: number;
    /** 温泉波动频率 */
    waveFrequency?: number;
    /** 温泉波动速度 */
    waveSpeed?: number;
    /** 是否启用气泡效果 */
    enableBubbleEffect?: boolean;
    /** 气泡效果强度 */
    bubbleEffectStrength?: number;
    /** 气泡大小范围 */
    bubbleSizeRange?: [number, number];
    /** 气泡速度范围 */
    bubbleSpeedRange?: [number, number];
    /** 气泡密度 */
    bubbleDensity?: number;
    /** 气泡分布范围 */
    bubbleDistributionRadius?: number;
    /** 是否启用气泡爆裂效果 */
    enableBubbleBurstEffect?: boolean;
    /** 气泡爆裂效果强度 */
    bubbleBurstEffectStrength?: number;
    /** 是否启用水蒸气效果 */
    enableSteamEffect?: boolean;
    /** 水蒸气效果强度 */
    steamEffectStrength?: number;
    /** 水蒸气颜色 */
    steamColor?: THREE.Color;
    /** 水蒸气密度 */
    steamDensity?: number;
    /** 水蒸气大小范围 */
    steamSizeRange?: [number, number];
    /** 水蒸气速度范围 */
    steamSpeedRange?: [number, number];
    /** 水蒸气上升高度 */
    steamRiseHeight?: number;
    /** 是否启用声音效果 */
    enableSoundEffect?: boolean;
    /** 声音效果音量 */
    soundEffectVolume?: number;
    /** 是否启用热扩散效果 */
    enableHeatDiffusion?: boolean;
    /** 热扩散范围 */
    heatDiffusionRange?: number;
    /** 是否启用矿物质效果 */
    enableMineralEffect?: boolean;
    /** 矿物质颜色 */
    mineralColor?: THREE.Color;
}
/**
 * 温泉预设
 */
export declare class HotSpringPresets {
    /**
     * 创建温泉预设
     * @param world 世界
     * @param config 预设配置
     * @returns 温泉实体
     */
    static createPreset(world: World, config: HotSpringPresetConfig): Entity;
    /**
     * 应用标准温泉预设
     * @param config 温泉配置
     */
    private static applyStandardPreset;
    /**
     * 应用高温温泉预设
     * @param config 温泉配置
     */
    private static applyHighTemperaturePreset;
    /**
     * 应用低温温泉预设
     * @param config 温泉配置
     */
    private static applyLowTemperaturePreset;
    /**
     * 应用大型温泉预设
     * @param config 温泉配置
     */
    private static applyLargePreset;
    /**
     * 应用小型温泉预设
     * @param config 温泉配置
     */
    private static applySmallPreset;
    /**
     * 应用硫磺温泉预设
     * @param config 温泉配置
     */
    private static applySulfurPreset;
    /**
     * 应用矿物质温泉预设
     * @param config 温泉配置
     */
    private static applyMineralPreset;
    /**
     * 应用地下温泉预设
     * @param config 温泉配置
     */
    private static applyUndergroundPreset;
}
