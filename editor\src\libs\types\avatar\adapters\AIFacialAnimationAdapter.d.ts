/**
 * AI面部动画适配器
 * 用于连接编辑器和引擎的AI面部动画生成功能
 */
import type { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { System } from '../../core/System';
import { FacialAnimationClip } from '../animation/FacialAnimationClip';
/**
 * AI面部动画适配器配置
 */
export interface AIFacialAnimationAdapterConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用本地模型 */
    useLocalModel?: boolean;
    /** 模型路径 */
    modelPath?: string;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 是否启用高级特性 */
    enableAdvancedFeatures?: boolean;
}
/**
 * AI面部动画生成请求
 */
export interface AIFacialAnimationRequest {
    /** 请求ID */
    id: string;
    /** 提示文本 */
    prompt: string;
    /** 持续时间（秒） */
    duration: number;
    /** 是否循环 */
    loop: boolean;
    /** 风格 */
    style?: string;
    /** 强度 */
    intensity?: number;
    /** 模型类型 */
    modelType: string;
    /** 是否使用高级特性 */
    useAdvancedFeatures: boolean;
}
/**
 * AI面部动画生成结果
 */
export interface AIFacialAnimationResult {
    /** 请求ID */
    id: string;
    /** 是否成功 */
    success: boolean;
    /** 错误信息 */
    error?: string;
    /** 动画片段ID */
    clipId?: string;
}
/**
 * AI面部动画适配器组件
 */
export declare class AIFacialAnimationAdapterComponent extends Component {
    /** 生成的动画片段 */
    generatedClips: Map<string, FacialAnimationClip>;
    /** 当前请求ID */
    currentRequestId: string | null;
    /** 是否正在生成 */
    isGenerating: boolean;
    /**
     * 构造函数
     */
    constructor();
}
/**
 * AI面部动画适配器系统
 */
export declare class AIFacialAnimationAdapterSystem extends System {
    /** 配置 */
    private config;
    /** 组件映射 */
    private components;
    /** 高级情感动画生成器 */
    private advancedGenerator;
    /** BERT情感模型 */
    private bertModel;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: AIFacialAnimationAdapterConfig);
    /**
     * 初始化
     */
    initialize(): Promise<void>;
    /**
     * 创建适配器组件
     * @param entity 实体
     * @returns 适配器组件
     */
    createAdapter(entity: Entity): AIFacialAnimationAdapterComponent;
    /**
     * 移除适配器组件
     * @param entity 实体
     */
    removeAdapter(entity: Entity): void;
    /**
     * 获取适配器组件
     * @param entity 实体
     * @returns 适配器组件
     */
    getAdapter(entity: Entity): AIFacialAnimationAdapterComponent | null;
    /**
     * 生成AI面部动画
     * @param entity 实体
     * @param request 请求
     * @returns 是否成功提交请求
     */
    generateAIFacialAnimation(entity: Entity, request: AIFacialAnimationRequest): Promise<boolean>;
    /**
     * 使用基本生成器生成动画
     * @param request 请求
     * @returns 动画片段
     */
    private generateWithBasic;
    /**
     * 使用高级生成器生成动画
     * @param request 请求
     * @returns 动画片段
     */
    private generateWithAdvanced;
    /**
     * 使用BERT模型生成动画
     * @param request 请求
     * @returns 动画片段
     */
    private generateWithBERT;
    /**
     * 应用生成的动画
     * @param entity 实体
     * @param clipId 动画片段ID
     * @returns 是否成功应用
     */
    applyGeneratedAnimation(entity: Entity, clipId: string): boolean;
    /**
     * 预览生成的动画
     * @param entity 实体
     * @param clipId 动画片段ID
     * @returns 是否成功预览
     */
    previewGeneratedAnimation(entity: Entity, clipId: string): boolean;
    /**
     * 发送生成完成事件
     * @param result 生成结果
     */
    private emitGenerationComplete;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    addEventListener(event: string, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    removeEventListener(event: string, listener: (...args: any[]) => void): void;
    /**
     * 更新
     * @param _deltaTime 时间增量
     */
    update(_deltaTime: number): void;
    /**
     * 销毁
     */
    dispose(): void;
}
