import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as os from 'os';
import * as process from 'process';
import { ServiceCacheService } from '../registry/cache/service-cache.service';
import { LoadBalancerService } from '../registry/load-balancer/load-balancer.service';
import { RegistryService } from '../registry/registry.service';

/**
 * 监控指标类型
 */
export interface MonitoringMetrics {
  timestamp: number;
  cpuUsage: number;
  memoryUsage: number;
  totalMemory: number;
  freeMemory: number;
  uptime: number;
  loadAverage: number[];
  serviceCount: number;
  instanceCount: number;
  healthyInstanceCount: number;
  unhealthyInstanceCount: number;
  cacheStats: {
    hits: number;
    misses: number;
    hitRate: number;
    size: number;
    memoryUsage: number;
  };
  loadBalancerStats: {
    requestCount: number;
    errorCount: number;
    avgResponseTime: number;
  };
}

/**
 * 告警类型
 */
export interface Alert {
  id: string;
  type: 'cpu' | 'memory' | 'service' | 'instance' | 'cache' | 'load-balancer' | 'system';
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  value: number;
  threshold: number;
  resolved: boolean;
  resolvedAt?: number;
}

/**
 * 监控服务
 */
@Injectable()
export class MonitoringService {
  private readonly logger = new Logger(MonitoringService.name);
  
  // 监控指标历史
  private metrics: MonitoringMetrics[] = [];
  
  // 告警历史
  private alerts: Alert[] = [];
  
  // 最大历史记录数
  private readonly maxMetricsHistory: number;
  private readonly maxAlertsHistory: number;
  
  // 告警阈值
  private readonly cpuThreshold: number;
  private readonly memoryThreshold: number;
  private readonly unhealthyInstanceThreshold: number;
  private readonly cacheHitRateThreshold: number;
  private readonly loadBalancerErrorRateThreshold: number;
  
  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly serviceCacheService: ServiceCacheService,
    private readonly loadBalancerService: LoadBalancerService,
    private readonly registryService: RegistryService,
  ) {
    this.maxMetricsHistory = this.configService.get<number>('MONITORING_MAX_METRICS_HISTORY', 1440); // 默认保存24小时的数据（每分钟一次）
    this.maxAlertsHistory = this.configService.get<number>('MONITORING_MAX_ALERTS_HISTORY', 1000);
    this.cpuThreshold = this.configService.get<number>('MONITORING_CPU_THRESHOLD', 0.8); // 80%
    this.memoryThreshold = this.configService.get<number>('MONITORING_MEMORY_THRESHOLD', 0.8); // 80%
    this.unhealthyInstanceThreshold = this.configService.get<number>('MONITORING_UNHEALTHY_INSTANCE_THRESHOLD', 0.2); // 20%
    this.cacheHitRateThreshold = this.configService.get<number>('MONITORING_CACHE_HIT_RATE_THRESHOLD', 0.5); // 50%
    this.loadBalancerErrorRateThreshold = this.configService.get<number>('MONITORING_LOAD_BALANCER_ERROR_RATE_THRESHOLD', 0.05); // 5%
  }
  
  /**
   * 定时收集指标
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async collectMetrics() {
    try {
      // 收集系统指标
      const cpuUsage = await this.getCpuUsage();
      const { totalMemory, freeMemory, memoryUsage } = this.getMemoryUsage();
      const uptime = process.uptime();
      const loadAverage = os.loadavg();
      
      // 收集服务注册指标
      const { serviceCount, instanceCount, healthyInstanceCount, unhealthyInstanceCount } = await this.getRegistryStats();
      
      // 收集缓存指标
      const cacheStats = this.serviceCacheService.getStats();
      
      // 收集负载均衡指标
      const loadBalancerStats = this.loadBalancerService.getStats();
      
      // 创建指标对象
      const metrics: MonitoringMetrics = {
        timestamp: Date.now(),
        cpuUsage,
        memoryUsage,
        totalMemory,
        freeMemory,
        uptime,
        loadAverage,
        serviceCount,
        instanceCount,
        healthyInstanceCount,
        unhealthyInstanceCount,
        cacheStats,
        loadBalancerStats,
      };
      
      // 添加到历史数据
      this.metrics.push(metrics);
      
      // 如果历史数据超过最大值，删除最旧的数据
      if (this.metrics.length > this.maxMetricsHistory) {
        this.metrics.splice(0, this.metrics.length - this.maxMetricsHistory);
      }
      
      // 检查告警
      this.checkAlerts(metrics);
      
      // 触发指标更新事件
      this.eventEmitter.emit('monitoring.metrics', metrics);
      
      this.logger.debug('已收集监控指标');
    } catch (error) {
      this.logger.error(`收集指标失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 获取CPU使用率
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      
      // 等待100ms
      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const userCpuUsage = endUsage.user / 1000; // 微秒转毫秒
        const systemCpuUsage = endUsage.system / 1000; // 微秒转毫秒
        const totalCpuUsage = (userCpuUsage + systemCpuUsage) / 100 / os.cpus().length;
        
        resolve(Math.min(totalCpuUsage, 1));
      }, 100);
    });
  }
  
  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): { totalMemory: number, freeMemory: number, memoryUsage: number } {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsage = usedMemory / totalMemory;
    
    return {
      totalMemory,
      freeMemory,
      memoryUsage,
    };
  }
  
  /**
   * 获取服务注册统计信息
   */
  private async getRegistryStats(): Promise<{
    serviceCount: number;
    instanceCount: number;
    healthyInstanceCount: number;
    unhealthyInstanceCount: number;
  }> {
    try {
      const services = await this.registryService.getAllServices();
      
      let instanceCount = 0;
      let healthyInstanceCount = 0;
      
      for (const service of services) {
        if (service.instances) {
          instanceCount += service.instances.length;
          healthyInstanceCount += service.instances.filter(instance => instance.isHealthy).length;
        }
      }
      
      return {
        serviceCount: services.length,
        instanceCount,
        healthyInstanceCount,
        unhealthyInstanceCount: instanceCount - healthyInstanceCount,
      };
    } catch (error) {
      this.logger.error(`获取服务注册统计信息失败: ${error.message}`, error.stack);
      return {
        serviceCount: 0,
        instanceCount: 0,
        healthyInstanceCount: 0,
        unhealthyInstanceCount: 0,
      };
    }
  }
  
  /**
   * 检查告警
   */
  private checkAlerts(metrics: MonitoringMetrics): void {
    // 检查CPU使用率
    if (metrics.cpuUsage > this.cpuThreshold) {
      this.createAlert('cpu', 'warning', `CPU使用率过高: ${(metrics.cpuUsage * 100).toFixed(2)}%`, metrics.cpuUsage, this.cpuThreshold);
    }
    
    // 检查内存使用率
    if (metrics.memoryUsage > this.memoryThreshold) {
      this.createAlert('memory', 'warning', `内存使用率过高: ${(metrics.memoryUsage * 100).toFixed(2)}%`, metrics.memoryUsage, this.memoryThreshold);
    }
    
    // 检查不健康实例比例
    if (metrics.instanceCount > 0) {
      const unhealthyRatio = metrics.unhealthyInstanceCount / metrics.instanceCount;
      if (unhealthyRatio > this.unhealthyInstanceThreshold) {
        this.createAlert('instance', 'warning', `不健康实例比例过高: ${(unhealthyRatio * 100).toFixed(2)}%`, unhealthyRatio, this.unhealthyInstanceThreshold);
      }
    }
    
    // 检查缓存命中率
    if (metrics.cacheStats.hits + metrics.cacheStats.misses > 0) {
      if (metrics.cacheStats.hitRate < this.cacheHitRateThreshold) {
        this.createAlert('cache', 'info', `缓存命中率过低: ${(metrics.cacheStats.hitRate * 100).toFixed(2)}%`, metrics.cacheStats.hitRate, this.cacheHitRateThreshold);
      }
    }
    
    // 检查负载均衡错误率
    if (metrics.loadBalancerStats.requestCount > 0) {
      const errorRate = metrics.loadBalancerStats.errorCount / metrics.loadBalancerStats.requestCount;
      if (errorRate > this.loadBalancerErrorRateThreshold) {
        this.createAlert('load-balancer', 'error', `负载均衡错误率过高: ${(errorRate * 100).toFixed(2)}%`, errorRate, this.loadBalancerErrorRateThreshold);
      }
    }
    
    // 解决已解决的告警
    this.resolveAlerts(metrics);
  }
  
  /**
   * 创建告警
   */
  private createAlert(
    type: Alert['type'],
    severity: Alert['severity'],
    message: string,
    value: number,
    threshold: number,
  ): void {
    // 检查是否已存在相同类型的未解决告警
    const existingAlert = this.alerts.find(alert => alert.type === type && !alert.resolved);
    
    if (existingAlert) {
      // 更新现有告警
      existingAlert.message = message;
      existingAlert.value = value;
      existingAlert.timestamp = Date.now();
      
      // 如果严重程度提高，更新严重程度
      if (this.getSeverityLevel(severity) > this.getSeverityLevel(existingAlert.severity)) {
        existingAlert.severity = severity;
      }
    } else {
      // 创建新告警
      const alert: Alert = {
        id: `${type}-${Date.now()}`,
        type,
        severity,
        message,
        timestamp: Date.now(),
        value,
        threshold,
        resolved: false,
      };
      
      this.alerts.push(alert);
      
      // 如果告警数量超过最大值，删除最旧的已解决告警
      if (this.alerts.length > this.maxAlertsHistory) {
        const resolvedAlerts = this.alerts.filter(a => a.resolved);
        if (resolvedAlerts.length > 0) {
          // 按解决时间排序，删除最早解决的告警
          resolvedAlerts.sort((a, b) => (a.resolvedAt || 0) - (b.resolvedAt || 0));
          const alertToRemove = resolvedAlerts[0];
          const index = this.alerts.indexOf(alertToRemove);
          if (index !== -1) {
            this.alerts.splice(index, 1);
          }
        } else {
          // 如果没有已解决的告警，删除最旧的未解决告警
          this.alerts.shift();
        }
      }
      
      // 触发告警事件
      this.eventEmitter.emit('monitoring.alert', alert);
      
      this.logger.warn(`创建告警: ${message}`);
    }
  }
  
  /**
   * 解决告警
   */
  private resolveAlerts(metrics: MonitoringMetrics): void {
    // 获取未解决的告警
    const unresolvedAlerts = this.alerts.filter(alert => !alert.resolved);
    
    for (const alert of unresolvedAlerts) {
      let resolved = false;
      
      switch (alert.type) {
        case 'cpu':
          resolved = metrics.cpuUsage < this.cpuThreshold * 0.9; // 低于阈值的90%
          break;
        case 'memory':
          resolved = metrics.memoryUsage < this.memoryThreshold * 0.9; // 低于阈值的90%
          break;
        case 'instance':
          if (metrics.instanceCount > 0) {
            const unhealthyRatio = metrics.unhealthyInstanceCount / metrics.instanceCount;
            resolved = unhealthyRatio < this.unhealthyInstanceThreshold * 0.9; // 低于阈值的90%
          }
          break;
        case 'cache':
          if (metrics.cacheStats.hits + metrics.cacheStats.misses > 0) {
            resolved = metrics.cacheStats.hitRate > this.cacheHitRateThreshold * 1.1; // 高于阈值的110%
          }
          break;
        case 'load-balancer':
          if (metrics.loadBalancerStats.requestCount > 0) {
            const errorRate = metrics.loadBalancerStats.errorCount / metrics.loadBalancerStats.requestCount;
            resolved = errorRate < this.loadBalancerErrorRateThreshold * 0.9; // 低于阈值的90%
          }
          break;
      }
      
      if (resolved) {
        alert.resolved = true;
        alert.resolvedAt = Date.now();
        
        // 触发告警解决事件
        this.eventEmitter.emit('monitoring.alert.resolved', alert);
        
        this.logger.log(`解决告警: ${alert.message}`);
      }
    }
  }
  
  /**
   * 获取严重程度级别
   */
  private getSeverityLevel(severity: Alert['severity']): number {
    switch (severity) {
      case 'info':
        return 1;
      case 'warning':
        return 2;
      case 'error':
        return 3;
      case 'critical':
        return 4;
      default:
        return 0;
    }
  }
  
  /**
   * 获取监控指标
   */
  getMetrics(limit?: number): MonitoringMetrics[] {
    if (limit) {
      return this.metrics.slice(-limit);
    }
    return this.metrics;
  }
  
  /**
   * 获取最新监控指标
   */
  getLatestMetrics(): MonitoringMetrics | null {
    if (this.metrics.length === 0) {
      return null;
    }
    return this.metrics[this.metrics.length - 1];
  }
  
  /**
   * 获取告警
   */
  getAlerts(onlyUnresolved: boolean = false): Alert[] {
    if (onlyUnresolved) {
      return this.alerts.filter(alert => !alert.resolved);
    }
    return this.alerts;
  }
}
