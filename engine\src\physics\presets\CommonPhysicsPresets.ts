/**
 * 常用物理预设
 * 提供一系列常用的物理预设
 */
import * as THREE from 'three';
import { PhysicsPreset } from './PhysicsPreset';
import { BodyType } from '../PhysicsBody';
import { ColliderType } from '../PhysicsCollider';

/**
 * 常用物理预设
 */
export class CommonPhysicsPresets {
  /**
   * 获取所有常用预设
   * @returns 常用预设数组
   */
  public static getAllPresets(): PhysicsPreset[] {
    return [
      ...this.getWorldPresets(),
      ...this.getBodyPresets(),
      ...this.getVehiclePresets(),
      ...this.getCharacterPresets(),
      ...this.getRagdollPresets(),
      ...this.getSoftBodyPresets()
    ];
  }
  
  /**
   * 获取世界预设
   * @returns 世界预设数组
   */
  public static getWorldPresets(): PhysicsPreset[] {
    return [
      {
        name: 'earth_gravity',
        description: '地球重力预设',
        category: '世界',
        worldPreset: {
          gravity: new THREE.Vector3(0, -9.82, 0),
          allowSleep: true,
          iterations: 10,
          broadphase: 'sap',
          defaultFriction: 0.3,
          defaultRestitution: 0.3
        }
      },
      {
        name: 'moon_gravity',
        description: '月球重力预设',
        category: '世界',
        worldPreset: {
          gravity: new THREE.Vector3(0, -1.62, 0),
          allowSleep: true,
          iterations: 10,
          broadphase: 'sap',
          defaultFriction: 0.3,
          defaultRestitution: 0.3
        }
      },
      {
        name: 'zero_gravity',
        description: '零重力预设',
        category: '世界',
        worldPreset: {
          gravity: new THREE.Vector3(0, 0, 0),
          allowSleep: false,
          iterations: 10,
          broadphase: 'sap',
          defaultFriction: 0.3,
          defaultRestitution: 0.3
        }
      },
      {
        name: 'high_precision',
        description: '高精度物理预设',
        category: '世界',
        worldPreset: {
          gravity: new THREE.Vector3(0, -9.82, 0),
          allowSleep: true,
          iterations: 20,
          broadphase: 'sap',
          defaultFriction: 0.3,
          defaultRestitution: 0.3
        }
      }
    ];
  }
  
  /**
   * 获取物理体预设
   * @returns 物理体预设数组
   */
  public static getBodyPresets(): PhysicsPreset[] {
    return [
      {
        name: 'dynamic_box',
        description: '动态盒子预设',
        category: '物理体',
        bodyPreset: {
          type: BodyType.DYNAMIC,
          mass: 1,
          linearDamping: 0.1,
          angularDamping: 0.1,
          allowSleep: true,
          fixedRotation: false,
          materialName: 'default',
          enableCCD: false,
          collisionFilterGroup: 1,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.BOX,
          isTrigger: false,
          params: {
            halfExtents: { x: 0.5, y: 0.5, z: 0.5 }
          },
          materialName: 'default'
        }
      },
      {
        name: 'dynamic_sphere',
        description: '动态球体预设',
        category: '物理体',
        bodyPreset: {
          type: BodyType.DYNAMIC,
          mass: 1,
          linearDamping: 0.1,
          angularDamping: 0.1,
          allowSleep: true,
          fixedRotation: false,
          materialName: 'default',
          enableCCD: false,
          collisionFilterGroup: 1,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.SPHERE,
          isTrigger: false,
          params: {
            radius: 0.5
          },
          materialName: 'default'
        }
      },
      {
        name: 'static_ground',
        description: '静态地面预设',
        category: '物理体',
        bodyPreset: {
          type: BodyType.STATIC,
          mass: 0,
          linearDamping: 0.1,
          angularDamping: 0.1,
          allowSleep: true,
          fixedRotation: true,
          materialName: 'default',
          enableCCD: false,
          collisionFilterGroup: 1,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.BOX,
          isTrigger: false,
          params: {
            halfExtents: { x: 5, y: 0.5, z: 5 }
          },
          materialName: 'default'
        }
      },
      {
        name: 'kinematic_platform',
        description: '运动学平台预设',
        category: '物理体',
        bodyPreset: {
          type: BodyType.KINEMATIC,
          mass: 0,
          linearDamping: 0.1,
          angularDamping: 0.1,
          allowSleep: false,
          fixedRotation: false,
          materialName: 'default',
          enableCCD: false,
          collisionFilterGroup: 1,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.BOX,
          isTrigger: false,
          params: {
            halfExtents: { x: 2, y: 0.2, z: 2 }
          },
          materialName: 'default'
        }
      },
      {
        name: 'trigger_zone',
        description: '触发区域预设',
        category: '物理体',
        bodyPreset: {
          type: BodyType.STATIC,
          mass: 0,
          linearDamping: 0.1,
          angularDamping: 0.1,
          allowSleep: true,
          fixedRotation: true,
          materialName: 'default',
          enableCCD: false,
          collisionFilterGroup: 2,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.BOX,
          isTrigger: true,
          params: {
            halfExtents: { x: 1, y: 1, z: 1 }
          },
          materialName: 'default'
        }
      },
      {
        name: 'bouncy_ball',
        description: '弹力球预设',
        category: '物理体',
        bodyPreset: {
          type: BodyType.DYNAMIC,
          mass: 1,
          linearDamping: 0.1,
          angularDamping: 0.1,
          allowSleep: true,
          fixedRotation: false,
          materialName: 'bouncy',
          enableCCD: true,
          collisionFilterGroup: 1,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.SPHERE,
          isTrigger: false,
          params: {
            radius: 0.5
          },
          materialName: 'bouncy'
        }
      }
    ];
  }
  
  /**
   * 获取车辆预设
   * @returns 车辆预设数组
   */
  public static getVehiclePresets(): PhysicsPreset[] {
    return [
      {
        name: 'car_chassis',
        description: '汽车底盘预设',
        category: '车辆',
        bodyPreset: {
          type: BodyType.DYNAMIC,
          mass: 800,
          linearDamping: 0.1,
          angularDamping: 0.1,
          allowSleep: false,
          fixedRotation: false,
          materialName: 'metal',
          enableCCD: false,
          collisionFilterGroup: 1,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.BOX,
          isTrigger: false,
          params: {
            halfExtents: { x: 1.2, y: 0.5, z: 2.5 }
          },
          materialName: 'metal'
        },
        customData: {
          wheelRadius: 0.4,
          wheelMass: 30,
          suspensionStiffness: 30,
          suspensionRestLength: 0.3,
          suspensionDamping: 4.4,
          suspensionCompression: 2.3,
          rollInfluence: 0.01,
          maxSuspensionTravel: 0.3,
          maxSuspensionForce: 100000,
          frictionSlip: 30
        }
      },
      {
        name: 'car_wheel',
        description: '汽车车轮预设',
        category: '车辆',
        bodyPreset: {
          type: BodyType.DYNAMIC,
          mass: 30,
          linearDamping: 0.1,
          angularDamping: 0.1,
          allowSleep: false,
          fixedRotation: false,
          materialName: 'rubber',
          enableCCD: false,
          collisionFilterGroup: 1,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.CYLINDER,
          isTrigger: false,
          params: {
            radiusTop: 0.4,
            radiusBottom: 0.4,
            height: 0.2,
            numSegments: 16
          },
          materialName: 'rubber'
        }
      }
    ];
  }
  
  /**
   * 获取角色预设
   * @returns 角色预设数组
   */
  public static getCharacterPresets(): PhysicsPreset[] {
    return [
      {
        name: 'character_capsule',
        description: '角色胶囊体预设',
        category: '角色',
        bodyPreset: {
          type: BodyType.DYNAMIC,
          mass: 70,
          linearDamping: 0.1,
          angularDamping: 0.9,
          allowSleep: false,
          fixedRotation: true,
          materialName: 'default',
          enableCCD: false,
          collisionFilterGroup: 1,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.CAPSULE,
          isTrigger: false,
          params: {
            radius: 0.4,
            height: 1.2
          },
          materialName: 'default'
        },
        customData: {
          jumpForce: 5,
          moveSpeed: 5,
          turnSpeed: 2,
          maxSlopeAngle: 45
        }
      }
    ];
  }
  
  /**
   * 获取布娃娃预设
   * @returns 布娃娃预设数组
   */
  public static getRagdollPresets(): PhysicsPreset[] {
    return [
      {
        name: 'ragdoll_torso',
        description: '布娃娃躯干预设',
        category: '布娃娃',
        bodyPreset: {
          type: BodyType.DYNAMIC,
          mass: 10,
          linearDamping: 0.1,
          angularDamping: 0.1,
          allowSleep: true,
          fixedRotation: false,
          materialName: 'default',
          enableCCD: false,
          collisionFilterGroup: 1,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.BOX,
          isTrigger: false,
          params: {
            halfExtents: { x: 0.2, y: 0.3, z: 0.1 }
          },
          materialName: 'default'
        }
      },
      {
        name: 'ragdoll_limb',
        description: '布娃娃肢体预设',
        category: '布娃娃',
        bodyPreset: {
          type: BodyType.DYNAMIC,
          mass: 2,
          linearDamping: 0.1,
          angularDamping: 0.1,
          allowSleep: true,
          fixedRotation: false,
          materialName: 'default',
          enableCCD: false,
          collisionFilterGroup: 1,
          collisionFilterMask: 1
        },
        colliderPreset: {
          type: ColliderType.CAPSULE,
          isTrigger: false,
          params: {
            radius: 0.1,
            height: 0.4
          },
          materialName: 'default'
        }
      }
    ];
  }
  
  /**
   * 获取软体预设
   * @returns 软体预设数组
   */
  public static getSoftBodyPresets(): PhysicsPreset[] {
    return [
      {
        name: 'soft_cloth',
        description: '软布料预设',
        category: '软体',
        customData: {
          gridSize: { x: 10, y: 10 },
          particleMass: 0.1,
          stiffness: 100,
          damping: 5,
          fixedCorners: true
        }
      },
      {
        name: 'soft_rope',
        description: '软绳索预设',
        category: '软体',
        customData: {
          segments: 10,
          particleMass: 0.1,
          stiffness: 100,
          damping: 5,
          fixedEnds: true
        }
      }
    ];
  }
}
