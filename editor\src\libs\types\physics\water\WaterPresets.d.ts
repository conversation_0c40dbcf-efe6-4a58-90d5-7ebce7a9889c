/**
 * 水体预设
 * 提供各种类型的水体预设配置
 */
import * as THREE from 'three';
import { WaterBodyComponent } from './WaterBodyComponent';
/**
 * 水体预设类型
 */
export declare enum WaterPresetType {
    /** 湖泊 */
    LAKE = "lake",
    /** 河流 */
    RIVER = "river",
    /** 海洋 */
    OCEAN = "ocean",
    /** 游泳池 */
    POOL = "pool",
    /** 温泉 */
    HOT_SPRING = "hot_spring",
    /** 地下湖泊 */
    UNDERGROUND_LAKE = "underground_lake",
    /** 地下河流 */
    UNDERGROUND_RIVER = "underground_river",
    /** 瀑布 */
    WATERFALL = "waterfall",
    /** 浅滩 */
    SHALLOW = "shallow",
    /** 沼泽 */
    SWAMP = "swamp",
    /** 冰湖 */
    ICE_LAKE = "ice_lake",
    /** 熔岩 */
    LAVA = "lava"
}
/**
 * 水体预设配置
 */
export interface WaterPresetConfig {
    /** 水体类型 */
    type: WaterPresetType;
    /** 水体尺寸 */
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    /** 水体位置 */
    position?: THREE.Vector3;
    /** 水体旋转 */
    rotation?: THREE.Euler;
    /** 水体颜色 */
    color?: THREE.Color;
    /** 水体透明度 */
    opacity?: number;
    /** 水体反射率 */
    reflectivity?: number;
    /** 水体折射率 */
    refractivity?: number;
    /** 水体波动参数 */
    waveParams?: {
        amplitude: number;
        frequency: number;
        speed: number;
        direction: THREE.Vector2;
    };
    /** 水体流动参数 */
    flowParams?: {
        direction: THREE.Vector3;
        speed: number;
    };
    /** 水体物理参数 */
    physicsParams?: {
        density: number;
        viscosity: number;
        surfaceTension?: number;
    };
}
/**
 * 水体预设类
 */
export declare class WaterPresets {
    /**
     * 获取预设配置
     * @param type 预设类型
     * @returns 预设配置
     */
    static getPresetConfig(type: WaterPresetType): WaterPresetConfig;
    /**
     * 创建水体预设
     * @param entity 实体
     * @param config 预设配置
     * @returns 水体组件
     */
    static createPreset(entity: any, config: WaterPresetConfig): WaterBodyComponent;
    /**
     * 转换预设类型到水体类型
     * @param presetType 预设类型
     * @returns 水体类型
     */
    private static convertToWaterBodyType;
    /**
     * 获取所有预设类型
     * @returns 预设类型数组
     */
    static getAllPresetTypes(): WaterPresetType[];
    /**
     * 获取预设显示名称
     * @param type 预设类型
     * @returns 显示名称
     */
    static getPresetDisplayName(type: WaterPresetType): string;
}
