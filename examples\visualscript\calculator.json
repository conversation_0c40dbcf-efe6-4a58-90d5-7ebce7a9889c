{"id": "calculator-example", "name": "简单计算器示例", "description": "一个简单的计算器示例，展示了如何使用数学节点进行基本运算。", "version": "1.0.0", "nodes": [{"id": "start-node", "type": "core/event/onStart", "position": {"x": 100, "y": 100}, "data": {"label": "开始"}}, {"id": "number-a", "type": "core/variable/get", "position": {"x": 300, "y": 50}, "data": {"label": "获取数字A", "variableName": "numberA"}}, {"id": "number-b", "type": "core/variable/get", "position": {"x": 300, "y": 150}, "data": {"label": "获取数字B", "variableName": "numberB"}}, {"id": "add-node", "type": "math/basic/add", "position": {"x": 500, "y": 50}, "data": {"label": "加法"}}, {"id": "subtract-node", "type": "math/basic/subtract", "position": {"x": 500, "y": 150}, "data": {"label": "减法"}}, {"id": "multiply-node", "type": "math/basic/multiply", "position": {"x": 500, "y": 250}, "data": {"label": "乘法"}}, {"id": "divide-node", "type": "math/basic/divide", "position": {"x": 500, "y": 350}, "data": {"label": "除法"}}, {"id": "result-add", "type": "core/variable/set", "position": {"x": 700, "y": 50}, "data": {"label": "设置加法结果", "variableName": "resultAdd"}}, {"id": "result-subtract", "type": "core/variable/set", "position": {"x": 700, "y": 150}, "data": {"label": "设置减法结果", "variableName": "resultSubtract"}}, {"id": "result-multiply", "type": "core/variable/set", "position": {"x": 700, "y": 250}, "data": {"label": "设置乘法结果", "variableName": "resultMultiply"}}, {"id": "result-divide", "type": "core/variable/set", "position": {"x": 700, "y": 350}, "data": {"label": "设置除法结果", "variableName": "resultDivide"}}, {"id": "display-results", "type": "core/debug/log", "position": {"x": 900, "y": 200}, "data": {"label": "显示结果"}}], "edges": [{"id": "edge-start-add", "source": "start-node", "target": "add-node", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-add-subtract", "source": "add-node", "target": "subtract-node", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-subtract-multiply", "source": "subtract-node", "target": "multiply-node", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-multiply-divide", "source": "multiply-node", "target": "divide-node", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-divide-display", "source": "divide-node", "target": "display-results", "sourceHandle": "flow", "targetHandle": "flow", "type": "flow"}, {"id": "edge-number-a-add", "source": "number-a", "target": "add-node", "sourceHandle": "value", "targetHandle": "a", "type": "data"}, {"id": "edge-number-b-add", "source": "number-b", "target": "add-node", "sourceHandle": "value", "targetHandle": "b", "type": "data"}, {"id": "edge-number-a-subtract", "source": "number-a", "target": "subtract-node", "sourceHandle": "value", "targetHandle": "a", "type": "data"}, {"id": "edge-number-b-subtract", "source": "number-b", "target": "subtract-node", "sourceHandle": "value", "targetHandle": "b", "type": "data"}, {"id": "edge-number-a-multiply", "source": "number-a", "target": "multiply-node", "sourceHandle": "value", "targetHandle": "a", "type": "data"}, {"id": "edge-number-b-multiply", "source": "number-b", "target": "multiply-node", "sourceHandle": "value", "targetHandle": "b", "type": "data"}, {"id": "edge-number-a-divide", "source": "number-a", "target": "divide-node", "sourceHandle": "value", "targetHandle": "a", "type": "data"}, {"id": "edge-number-b-divide", "source": "number-b", "target": "divide-node", "sourceHandle": "value", "targetHandle": "b", "type": "data"}, {"id": "edge-add-result", "source": "add-node", "target": "result-add", "sourceHandle": "result", "targetHandle": "value", "type": "data"}, {"id": "edge-subtract-result", "source": "subtract-node", "target": "result-subtract", "sourceHandle": "result", "targetHandle": "value", "type": "data"}, {"id": "edge-multiply-result", "source": "multiply-node", "target": "result-multiply", "sourceHandle": "result", "targetHandle": "value", "type": "data"}, {"id": "edge-divide-result", "source": "divide-node", "target": "result-divide", "sourceHandle": "result", "targetHandle": "value", "type": "data"}], "variables": [{"id": "var-number-a", "name": "numberA", "valueTypeName": "number", "initialValue": 10}, {"id": "var-number-b", "name": "numberB", "valueTypeName": "number", "initialValue": 5}, {"id": "var-result-add", "name": "resultAdd", "valueTypeName": "number", "initialValue": 0}, {"id": "var-result-subtract", "name": "resultSubtract", "valueTypeName": "number", "initialValue": 0}, {"id": "var-result-multiply", "name": "resultMultiply", "valueTypeName": "number", "initialValue": 0}, {"id": "var-result-divide", "name": "resultDivide", "valueTypeName": "number", "initialValue": 0}]}