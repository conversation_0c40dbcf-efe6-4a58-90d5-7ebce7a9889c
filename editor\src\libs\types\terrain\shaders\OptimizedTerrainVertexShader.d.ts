/**
 * 优化的地形顶点着色器
 * 用于提高地形渲染性能
 */
export declare const optimizedTerrainVertexShader = "\n// \u5C5E\u6027\nattribute vec3 position;\nattribute vec3 normal;\nattribute vec2 uv;\nattribute vec4 tangent;\n\n// \u53D8\u6362\u77E9\u9635\nuniform mat4 modelMatrix;\nuniform mat4 viewMatrix;\nuniform mat4 projectionMatrix;\nuniform mat4 modelViewMatrix;\nuniform mat3 normalMatrix;\n\n// \u76F8\u673A\u4F4D\u7F6E\nuniform vec3 cameraPosition;\n\n// \u5730\u5F62\u53C2\u6570\nuniform float uMaxHeight;\nuniform bool uUseLOD;\nuniform float uLODDistance;\nuniform float uLODFactor;\nuniform bool uUseFog;\nuniform float uFogNear;\nuniform float uFogFar;\nuniform bool uUseClipPlane;\nuniform vec4 uClipPlane;\n\n// \u8F93\u51FA\u53D8\u91CF\nvarying vec2 vUv;\nvarying vec3 vNormal;\nvarying vec4 vTangent;\nvarying vec3 vWorldPosition;\nvarying vec3 vPosition;\nvarying float vHeight;\nvarying float vSlope;\nvarying float vFogFactor;\nvarying float vLODBlendFactor;\n\n// \u8BA1\u7B97\u659C\u5EA6\nfloat calculateSlope(vec3 normal) {\n  return acos(normal.y) / 3.14159265359 * 2.0;\n}\n\n// \u8BA1\u7B97LOD\u6DF7\u5408\u56E0\u5B50\nfloat calculateLODBlendFactor(float distance, float lodDistance, float lodFactor) {\n  return clamp((distance - lodDistance) * lodFactor, 0.0, 1.0);\n}\n\nvoid main() {\n  // \u8BA1\u7B97UV\u5750\u6807\n  vUv = uv;\n  \n  // \u8BA1\u7B97\u6CD5\u7EBF\u548C\u5207\u7EBF\n  vNormal = normalize(normalMatrix * normal);\n  vTangent = vec4(normalize(normalMatrix * tangent.xyz), tangent.w);\n  \n  // \u8BA1\u7B97\u9876\u70B9\u4F4D\u7F6E\n  vec4 worldPosition = modelMatrix * vec4(position, 1.0);\n  vWorldPosition = worldPosition.xyz;\n  vPosition = position;\n  \n  // \u8BA1\u7B97\u9AD8\u5EA6\u548C\u659C\u5EA6\n  vHeight = position.y / uMaxHeight;\n  vSlope = calculateSlope(vNormal);\n  \n  // \u8BA1\u7B97\u5230\u76F8\u673A\u7684\u8DDD\u79BB\n  float cameraDistance = length(worldPosition.xyz - cameraPosition);\n  \n  // \u8BA1\u7B97LOD\u6DF7\u5408\u56E0\u5B50\n  if (uUseLOD) {\n    vLODBlendFactor = calculateLODBlendFactor(cameraDistance, uLODDistance, uLODFactor);\n  } else {\n    vLODBlendFactor = 0.0;\n  }\n  \n  // \u8BA1\u7B97\u96FE\u56E0\u5B50\n  if (uUseFog) {\n    vFogFactor = smoothstep(uFogNear, uFogFar, cameraDistance);\n  } else {\n    vFogFactor = 0.0;\n  }\n  \n  // \u8F93\u51FA\u88C1\u526A\u7A7A\u95F4\u5750\u6807\n  gl_Position = projectionMatrix * viewMatrix * worldPosition;\n  \n  // \u5E94\u7528\u88C1\u526A\u5E73\u9762\n  if (uUseClipPlane) {\n    gl_ClipDistance[0] = dot(worldPosition, uClipPlane);\n  }\n}\n";
