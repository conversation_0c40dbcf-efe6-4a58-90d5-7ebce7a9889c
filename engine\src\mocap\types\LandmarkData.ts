/**
 * 关键点数据类型定义
 */

/**
 * 关键点数据
 * 表示屏幕坐标系中的关键点
 */
export interface LandmarkData {
  /** X坐标（0-1范围） */
  x: number;
  
  /** Y坐标（0-1范围） */
  y: number;
  
  /** Z坐标（相对深度） */
  z: number;
  
  /** 可见度（0-1范围，0表示不可见，1表示完全可见） */
  visibility?: number;
}

/**
 * 世界坐标系关键点数据
 * 表示世界坐标系中的关键点
 */
export interface WorldLandmarkData {
  /** X坐标（米） */
  x: number;
  
  /** Y坐标（米） */
  y: number;
  
  /** Z坐标（米） */
  z: number;
  
  /** 可见度（0-1范围，0表示不可见，1表示完全可见） */
  visibility?: number;
}

/**
 * 关键点连接
 * 表示两个关键点之间的连接
 */
export interface LandmarkConnection {
  /** 起始关键点索引 */
  start: number;
  
  /** 结束关键点索引 */
  end: number;
}

/**
 * 关键点连接集合
 * 表示一组关键点连接
 */
export interface LandmarkConnections {
  /** 连接数组 */
  connections: LandmarkConnection[];
}

/**
 * 创建空的关键点数据
 * @returns 空的关键点数据
 */
export function createEmptyLandmark(): LandmarkData {
  return {
    x: 0,
    y: 0,
    z: 0,
    visibility: 0
  };
}

/**
 * 创建空的世界坐标系关键点数据
 * @returns 空的世界坐标系关键点数据
 */
export function createEmptyWorldLandmark(): WorldLandmarkData {
  return {
    x: 0,
    y: 0,
    z: 0,
    visibility: 0
  };
}

/**
 * 克隆关键点数据
 * @param landmark 关键点数据
 * @returns 克隆的关键点数据
 */
export function cloneLandmark(landmark: LandmarkData): LandmarkData {
  return {
    x: landmark.x,
    y: landmark.y,
    z: landmark.z,
    visibility: landmark.visibility
  };
}

/**
 * 克隆世界坐标系关键点数据
 * @param landmark 世界坐标系关键点数据
 * @returns 克隆的世界坐标系关键点数据
 */
export function cloneWorldLandmark(landmark: WorldLandmarkData): WorldLandmarkData {
  return {
    x: landmark.x,
    y: landmark.y,
    z: landmark.z,
    visibility: landmark.visibility
  };
}

/**
 * 插值关键点数据
 * @param a 起始关键点
 * @param b 结束关键点
 * @param t 插值因子（0-1）
 * @returns 插值后的关键点
 */
export function lerpLandmark(a: LandmarkData, b: LandmarkData, t: number): LandmarkData {
  return {
    x: a.x + (b.x - a.x) * t,
    y: a.y + (b.y - a.y) * t,
    z: a.z + (b.z - a.z) * t,
    visibility: a.visibility !== undefined && b.visibility !== undefined
      ? a.visibility + (b.visibility - a.visibility) * t
      : undefined
  };
}

/**
 * 插值世界坐标系关键点数据
 * @param a 起始关键点
 * @param b 结束关键点
 * @param t 插值因子（0-1）
 * @returns 插值后的关键点
 */
export function lerpWorldLandmark(a: WorldLandmarkData, b: WorldLandmarkData, t: number): WorldLandmarkData {
  return {
    x: a.x + (b.x - a.x) * t,
    y: a.y + (b.y - a.y) * t,
    z: a.z + (b.z - a.z) * t,
    visibility: a.visibility !== undefined && b.visibility !== undefined
      ? a.visibility + (b.visibility - a.visibility) * t
      : undefined
  };
}

/**
 * 计算两个关键点之间的距离
 * @param a 关键点A
 * @param b 关键点B
 * @returns 距离
 */
export function landmarkDistance(a: LandmarkData | WorldLandmarkData, b: LandmarkData | WorldLandmarkData): number {
  const dx = b.x - a.x;
  const dy = b.y - a.y;
  const dz = b.z - a.z;
  return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * 计算关键点的可见性权重
 * @param landmark 关键点
 * @param threshold 阈值
 * @returns 可见性权重（0-1）
 */
export function landmarkVisibilityWeight(landmark: LandmarkData | WorldLandmarkData, threshold: number = 0.1): number {
  if (landmark.visibility === undefined) return 1;
  return landmark.visibility < threshold ? 0 : landmark.visibility;
}

/**
 * 过滤关键点数据
 * @param landmarks 关键点数据数组
 * @param threshold 可见度阈值
 * @returns 过滤后的关键点数据数组
 */
export function filterLandmarksByVisibility<T extends LandmarkData | WorldLandmarkData>(
  landmarks: T[],
  threshold: number = 0.1
): T[] {
  return landmarks.map(landmark => {
    if (!landmark.visibility || landmark.visibility < threshold) {
      // 如果可见度低于阈值，返回不可见的关键点
      return {
        ...landmark,
        visibility: 0
      } as T;
    }
    return landmark;
  });
}

/**
 * 平滑关键点数据
 * @param current 当前关键点数据
 * @param previous 上一帧关键点数据
 * @param smoothingFactor 平滑系数（0-1）
 * @returns 平滑后的关键点数据
 */
export function smoothLandmarks<T extends LandmarkData | WorldLandmarkData>(
  current: T[],
  previous: T[],
  smoothingFactor: number = 0.5
): T[] {
  if (!previous.length) return current;
  
  return current.map((landmark, index) => {
    if (index >= previous.length) return landmark;
    
    const prev = previous[index];
    
    // 如果当前关键点不可见，使用上一帧的数据
    if (landmark.visibility !== undefined && landmark.visibility < 0.1) {
      return prev;
    }
    
    // 线性插值
    return {
      x: prev.x + (landmark.x - prev.x) * smoothingFactor,
      y: prev.y + (landmark.y - prev.y) * smoothingFactor,
      z: prev.z + (landmark.z - prev.z) * smoothingFactor,
      visibility: landmark.visibility
    } as T;
  });
}
