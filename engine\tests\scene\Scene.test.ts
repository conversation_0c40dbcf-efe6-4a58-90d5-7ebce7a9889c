/**
 * Scene类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Scene } from '../../src/scene/Scene';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { Component } from '../../src/core/Component';

// 创建一个测试组件类
class TestComponent extends Component {
  public value: number = 0;
  
  constructor(entity: Entity, value: number = 0) {
    super(entity);
    this.value = value;
  }
  
  public static readonly TYPE: string = 'TestComponent';
  
  public getType(): string {
    return TestComponent.TYPE;
  }
  
  public setValue(value: number): void {
    this.value = value;
  }
  
  public getValue(): number {
    return this.value;
  }
}

describe('Scene', () => {
  let engine: Engine;
  let world: World;
  let scene: Scene;
  
  // 在每个测试前创建一个新的场景实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建场景
    scene = new Scene('测试场景');
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试场景初始化
  it('应该正确初始化场景', () => {
    expect(scene).toBeDefined();
    expect(scene.getName()).toBe('测试场景');
    expect(scene.getId()).toBeDefined();
    expect(scene.getAllEntities()).toEqual([]);
  });
  
  // 测试场景名称
  it('应该能够设置和获取场景名称', () => {
    // 验证初始名称
    expect(scene.getName()).toBe('测试场景');
    
    // 设置新名称
    scene.setName('新场景');
    
    // 验证名称已更改
    expect(scene.getName()).toBe('新场景');
  });
  
  // 测试添加实体
  it('应该能够添加实体', () => {
    // 创建实体
    const entity = new Entity(world);
    entity.name = '测试实体';
    
    // 添加实体到场景
    scene.addEntity(entity);
    
    // 验证实体已添加
    expect(scene.getAllEntities()).toContain(entity);
    expect(scene.getEntity(entity.getId())).toBe(entity);
  });
  
  // 测试移除实体
  it('应该能够移除实体', () => {
    // 创建实体
    const entity = new Entity(world);
    entity.name = '测试实体';
    
    // 添加实体到场景
    scene.addEntity(entity);
    
    // 验证实体已添加
    expect(scene.getAllEntities()).toContain(entity);
    
    // 移除实体
    scene.removeEntity(entity);
    
    // 验证实体已移除
    expect(scene.getAllEntities()).not.toContain(entity);
    expect(scene.getEntity(entity.getId())).toBeUndefined();
  });
  
  // 测试查找实体
  it('应该能够查找实体', () => {
    // 创建实体
    const entity1 = new Entity(world);
    entity1.name = '实体1';
    entity1.addComponent(new TestComponent(entity1, 10));
    
    const entity2 = new Entity(world);
    entity2.name = '实体2';
    
    const entity3 = new Entity(world);
    entity3.name = '实体3';
    entity3.addComponent(new TestComponent(entity3, 20));
    
    // 添加实体到场景
    scene.addEntity(entity1);
    scene.addEntity(entity2);
    scene.addEntity(entity3);
    
    // 通过名称查找实体
    const entitiesByName = scene.findEntitiesByName('实体1');
    expect(entitiesByName.length).toBe(1);
    expect(entitiesByName[0]).toBe(entity1);
    
    // 通过组件类型查找实体
    const entitiesByComponent = scene.findEntitiesByComponent(TestComponent.TYPE);
    expect(entitiesByComponent.length).toBe(2);
    expect(entitiesByComponent).toContain(entity1);
    expect(entitiesByComponent).toContain(entity3);
    expect(entitiesByComponent).not.toContain(entity2);
  });
  
  // 测试场景层级结构
  it('应该能够管理场景层级结构', () => {
    // 创建父实体
    const parent = new Entity(world);
    parent.name = '父实体';
    
    // 创建子实体
    const child1 = new Entity(world);
    child1.name = '子实体1';
    
    const child2 = new Entity(world);
    child2.name = '子实体2';
    
    // 设置父子关系
    child1.setParent(parent);
    child2.setParent(parent);
    
    // 添加父实体到场景
    scene.addEntity(parent);
    
    // 验证父子关系
    expect(parent.getChildren()).toContain(child1);
    expect(parent.getChildren()).toContain(child2);
    expect(child1.getParent()).toBe(parent);
    expect(child2.getParent()).toBe(parent);
    
    // 验证场景包含所有实体
    expect(scene.getAllEntities()).toContain(parent);
    expect(scene.getAllEntities()).toContain(child1);
    expect(scene.getAllEntities()).toContain(child2);
  });
  
  // 测试场景序列化
  it('应该能够序列化场景', () => {
    // 创建实体
    const entity = new Entity(world);
    entity.name = '测试实体';
    entity.addComponent(new Transform({
      position: { x: 1, y: 2, z: 3 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    entity.addComponent(new TestComponent(entity, 42));
    
    // 添加实体到场景
    scene.addEntity(entity);
    
    // 序列化场景
    const data = scene.serialize();
    
    // 验证序列化数据
    expect(data).toBeDefined();
    expect(data.id).toBe(scene.getId());
    expect(data.name).toBe(scene.getName());
    expect(data.entities).toBeDefined();
    expect(data.entities.length).toBe(1);
    
    // 验证实体序列化数据
    const entityData = data.entities[0];
    expect(entityData.id).toBe(entity.getId());
    expect(entityData.name).toBe(entity.getName());
    expect(entityData.components).toBeDefined();
    expect(entityData.components.length).toBe(2);
    
    // 验证组件序列化数据
    const transformData = entityData.components.find(c => c.type === 'Transform');
    expect(transformData).toBeDefined();
    expect(transformData.position).toEqual({ x: 1, y: 2, z: 3 });
    
    const testComponentData = entityData.components.find(c => c.type === TestComponent.TYPE);
    expect(testComponentData).toBeDefined();
    expect(testComponentData.value).toBe(42);
  });
  
  // 测试场景反序列化
  it('应该能够反序列化场景', () => {
    // 创建序列化数据
    const data = {
      id: 'test-scene',
      name: '测试场景',
      entities: [
        {
          id: 'entity1',
          name: '实体1',
          components: [
            {
              type: 'Transform',
              position: { x: 1, y: 2, z: 3 },
              rotation: { x: 0, y: 0, z: 0 },
              scale: { x: 1, y: 1, z: 1 }
            },
            {
              type: TestComponent.TYPE,
              value: 42
            }
          ]
        }
      ]
    };
    
    // 反序列化场景
    scene.deserialize(data, world);
    
    // 验证场景属性
    expect(scene.getId()).toBe('test-scene');
    expect(scene.getName()).toBe('测试场景');
    
    // 验证实体
    const entities = scene.getAllEntities();
    expect(entities.length).toBe(1);
    
    const entity = entities[0];
    expect(entity.getId()).toBe('entity1');
    expect(entity.getName()).toBe('实体1');
    
    // 验证组件
    const transform = entity.getComponent<Transform>('Transform');
    expect(transform).toBeDefined();
    expect(transform!.position).toEqual({ x: 1, y: 2, z: 3 });
    
    const testComponent = entity.getComponent<TestComponent>(TestComponent.TYPE);
    expect(testComponent).toBeDefined();
    expect(testComponent!.getValue()).toBe(42);
  });
  
  // 测试场景克隆
  it('应该能够克隆场景', () => {
    // 创建实体
    const entity = new Entity(world);
    entity.name = '测试实体';
    entity.addComponent(new Transform({
      position: { x: 1, y: 2, z: 3 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    entity.addComponent(new TestComponent(entity, 42));
    
    // 添加实体到场景
    scene.addEntity(entity);
    
    // 克隆场景
    const clonedScene = scene.clone();
    
    // 验证克隆的场景属性
    expect(clonedScene.getName()).toBe(scene.getName());
    expect(clonedScene.getId()).not.toBe(scene.getId());
    
    // 验证克隆的实体
    const clonedEntities = clonedScene.getAllEntities();
    expect(clonedEntities.length).toBe(1);
    
    const clonedEntity = clonedEntities[0];
    expect(clonedEntity.getName()).toBe(entity.getName());
    expect(clonedEntity.getId()).not.toBe(entity.getId());
    
    // 验证克隆的组件
    const clonedTransform = clonedEntity.getComponent<Transform>('Transform');
    expect(clonedTransform).toBeDefined();
    expect(clonedTransform!.position).toEqual({ x: 1, y: 2, z: 3 });
    
    const clonedTestComponent = clonedEntity.getComponent<TestComponent>(TestComponent.TYPE);
    expect(clonedTestComponent).toBeDefined();
    expect(clonedTestComponent!.getValue()).toBe(42);
  });
});
