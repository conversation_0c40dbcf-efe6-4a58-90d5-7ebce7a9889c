/**
 * 输入组件
 * 用于处理实体的输入
 */
import { Component } from '../../core/Component';
import { InputAction } from '../InputAction';
import { InputBinding } from '../InputBinding';
import { InputMapping } from '../InputMapping';
/**
 * 输入组件选项
 */
export interface InputComponentOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 输入动作列表 */
    actions?: InputAction[];
    /** 输入绑定列表 */
    bindings?: InputBinding[];
    /** 输入映射列表 */
    mappings?: InputMapping[];
}
/**
 * 输入组件
 */
export declare class InputComponent extends Component {
    /** 组件类型 */
    static readonly TYPE: string;
    /** 是否启用 */
    protected enabled: boolean;
    /** 输入动作映射 */
    private actions;
    /** 输入绑定映射 */
    private bindings;
    /** 输入映射映射 */
    private mappings;
    /** 输入管理器 */
    private inputManager;
    /**
     * 创建输入组件
     * @param options 选项
     */
    constructor(options?: InputComponentOptions);
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 处理输入动作
     */
    private processActions;
    /**
     * 添加输入动作
     * @param action 输入动作
     */
    addAction(action: InputAction): void;
    /**
     * 获取输入动作
     * @param name 动作名称
     * @returns 输入动作
     */
    getAction<T extends InputAction>(name: string): T | undefined;
    /**
     * 添加输入绑定
     * @param binding 输入绑定
     */
    addBinding(binding: InputBinding): void;
    /**
     * 获取输入绑定
     * @param name 绑定名称
     * @returns 输入绑定
     */
    getBinding(name: string): InputBinding | undefined;
    /**
     * 添加输入映射
     * @param mapping 输入映射
     */
    addMapping(mapping: InputMapping): void;
    /**
     * 获取输入映射
     * @param name 映射名称
     * @returns 输入映射
     */
    getMapping(name: string): InputMapping | undefined;
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 检查是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 获取所有输入动作
     * @returns 输入动作列表
     */
    getActions(): InputAction[];
    /**
     * 获取所有输入绑定
     * @returns 输入绑定列表
     */
    getBindings(): InputBinding[];
    /**
     * 获取所有输入映射
     * @returns 输入映射列表
     */
    getMappings(): InputMapping[];
    /**
     * 清除所有输入动作
     */
    clearActions(): void;
    /**
     * 清除所有输入绑定
     */
    clearBindings(): void;
    /**
     * 清除所有输入映射
     */
    clearMappings(): void;
    /**
     * 清除所有输入
     */
    clearAll(): void;
}
