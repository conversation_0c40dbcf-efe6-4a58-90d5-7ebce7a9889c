/**
 * 屏幕空间环境光遮蔽效果
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * SSAO输出模式
 */
export declare enum SSAOOutputMode {
    Default = 0,
    SSAO = 1,
    Blur = 2,
    Beauty = 3,
    Depth = 4,
    Normal = 5
}
/**
 * SSAO效果选项
 */
export interface SSAOEffectOptions extends PostProcessingEffectOptions {
    /** 输出模式 */
    output?: SSAOOutputMode;
    /** 内核半径 */
    kernelRadius?: number;
    /** 最小距离 */
    minDistance?: number;
    /** 最大距离 */
    maxDistance?: number;
    /** 强度 */
    aoClamp?: number;
    /** 亮度 */
    lumInfluence?: number;
}
/**
 * 屏幕空间环境光遮蔽效果
 */
export declare class SSAOEffect extends PostProcessingEffect {
    /** 输出模式 */
    private output;
    /** 内核半径 */
    private kernelRadius;
    /** 最小距离 */
    private minDistance;
    /** 最大距离 */
    private maxDistance;
    /** 强度 */
    private aoClamp;
    /** 亮度 */
    private lumInfluence;
    /** SSAO通道 */
    private ssaoPass;
    /** 场景 */
    private scene;
    /** 相机 */
    private camera;
    /**
     * 创建SSAO效果
     * @param options SSAO效果选项
     */
    constructor(options?: SSAOEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 设置场景和相机
     * @param scene 场景
     * @param camera 相机
     */
    setSceneAndCamera(scene: THREE.Scene, camera: THREE.Camera): void;
    /**
     * 设置输出模式
     * @param output 输出模式
     */
    setOutput(output: SSAOOutputMode): void;
    /**
     * 获取输出模式
     * @returns 输出模式
     */
    getOutput(): SSAOOutputMode;
    /**
     * 设置内核半径
     * @param radius 内核半径
     */
    setKernelRadius(radius: number): void;
    /**
     * 获取内核半径
     * @returns 内核半径
     */
    getKernelRadius(): number;
    /**
     * 设置最小距离
     * @param distance 最小距离
     */
    setMinDistance(distance: number): void;
    /**
     * 获取最小距离
     * @returns 最小距离
     */
    getMinDistance(): number;
    /**
     * 设置最大距离
     * @param distance 最大距离
     */
    setMaxDistance(distance: number): void;
    /**
     * 获取最大距离
     * @returns 最大距离
     */
    getMaxDistance(): number;
    /**
     * 设置强度
     * @param clamp 强度
     */
    setAOClamp(clamp: number): void;
    /**
     * 获取强度
     * @returns 强度
     */
    getAOClamp(): number;
    /**
     * 设置亮度
     * @param influence 亮度
     */
    setLumInfluence(influence: number): void;
    /**
     * 获取亮度
     * @returns 亮度
     */
    getLumInfluence(): number;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
    /**
     * 更新效果
     * @param _deltaTime 帧间隔时间（秒）- 未使用，保留以符合接口
     */
    update(_deltaTime: number): void;
}
