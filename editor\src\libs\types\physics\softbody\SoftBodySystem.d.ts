/**
 * 软体物理系统
 * 基于粒子和约束实现软体物理模拟
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { PhysicsSystem } from '../PhysicsSystem';
import { PhysicsBodyComponent } from '../components/PhysicsBodyComponent';
import { SoftBodyComponent } from './SoftBodyComponent';
/**
 * 软体物理系统选项
 */
export interface SoftBodySystemOptions {
    /** 是否显示调试信息 */
    debug?: boolean;
    /** 迭代次数 */
    iterations?: number;
    /** 物理系统引用 */
    physicsSystem?: PhysicsSystem;
    /** 是否启用空间分区 */
    useSpatialPartitioning?: boolean;
    /** 是否启用LOD系统 */
    useLOD?: boolean;
    /** 是否启用软体与刚体交互 */
    useSoftRigidInteraction?: boolean;
    /** 是否启用软体切割 */
    useSoftBodyCutter?: boolean;
}
/**
 * 软体物理系统
 * 管理所有软体物理组件
 */
export declare class SoftBodySystem extends System {
    /** 物理系统引用 */
    private physicsSystem;
    /** 物理世界引用 */
    private physicsWorld;
    /** 软体组件映射 */
    private softBodies;
    /** 是否显示调试信息 */
    private debug;
    /** 迭代次数 */
    private iterations;
    /** 调试渲染器 */
    private debugRenderer;
    /** 空间分区系统 */
    private spatialPartitioning;
    /** LOD系统 */
    private lodSystem;
    /** 软体与刚体交互系统 */
    private softRigidInteraction;
    /** 是否启用空间分区 */
    private useSpatialPartitioning;
    /** 是否启用LOD系统 */
    private useLOD;
    /** 是否启用软体与刚体交互 */
    private useSoftRigidInteraction;
    /** 软体切割系统 */
    private softBodyCutter;
    /** 是否启用软体切割 */
    private useSoftBodyCutter;
    /**
     * 创建软体物理系统
     * @param options 软体物理系统选项
     */
    constructor(options?: SoftBodySystemOptions);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 添加软体组件
     * @param softBody 软体组件
     */
    addSoftBody(softBody: SoftBodyComponent): void;
    /**
     * 移除软体组件
     * @param entityId 实体ID
     */
    removeSoftBody(entityId: string): void;
    /**
     * 获取软体组件
     * @param entityId 实体ID
     * @returns 软体组件
     */
    getSoftBody(entityId: string): SoftBodyComponent | undefined;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新调试渲染器
     */
    private updateDebugRenderer;
    /**
     * 销毁系统
     */
    destroy(): void;
    /**
     * 添加刚体到交互系统
     * @param rigidBody 刚体组件
     */
    addRigidBody(rigidBody: PhysicsBodyComponent): void;
    /**
     * 移除刚体从交互系统
     * @param rigidBody 刚体组件
     */
    removeRigidBody(rigidBody: PhysicsBodyComponent): void;
    /**
     * 设置相机实体（用于LOD系统）
     * @param cameraEntity 相机实体
     */
    setCameraEntity(cameraEntity: Entity): void;
    /**
     * 使用平面切割软体
     * @param entityId 实体ID
     * @param plane 切割平面
     * @returns 是否成功切割
     */
    cutSoftBodyWithPlane(entityId: string, plane: {
        normal: THREE.Vector3;
        point: THREE.Vector3;
    }): boolean;
    /**
     * 使用射线切割软体
     * @param entityId 实体ID
     * @param ray 切割射线
     * @returns 是否成功切割
     */
    cutSoftBodyWithRay(entityId: string, ray: {
        origin: THREE.Vector3;
        direction: THREE.Vector3;
        length: number;
    }): boolean;
    /**
     * 设置撕裂阈值
     * @param threshold 撕裂阈值
     */
    setTearingThreshold(threshold: number): void;
    /**
     * 启用/禁用撕裂
     * @param enabled 是否启用
     */
    setTearingEnabled(enabled: boolean): void;
}
