/**
 * 水体管理器
 * 统一管理场景中的所有水体
 */
import * as THREE from 'three';
import { type Entity } from '../../core/Entity';
import { WaterBodyComponent } from './WaterBodyComponent';
import { WaterGenerationConfig } from './WaterGenerator';
import { WaterPresetType } from './WaterPresets';
/**
 * 水体信息
 */
export interface WaterInfo {
    id: string;
    name: string;
    type: WaterPresetType;
    entity: Entity;
    waterBody: WaterBodyComponent;
    createdAt: Date;
    lastUpdated: Date;
}
/**
 * 水体统计信息
 */
export interface WaterStats {
    totalWaterBodies: number;
    typeDistribution: Record<WaterPresetType, number>;
    totalVolume: number;
    averageDensity: number;
    activeWaterBodies: number;
}
/**
 * 水体管理器类
 */
export declare class WaterManager {
    private static instance;
    private waterBodies;
    private physicsSystem;
    private scene;
    private updateCallbacks;
    /**
     * 获取单例实例
     */
    static getInstance(): WaterManager;
    /**
     * 私有构造函数
     */
    private constructor();
    /**
     * 初始化管理器
     * @param scene Three.js 场景
     */
    initialize(scene: THREE.Scene): void;
    /**
     * 添加水体
     * @param config 生成配置
     * @returns 水体信息
     */
    addWater(config: WaterGenerationConfig): WaterInfo;
    /**
     * 移除水体
     * @param id 水体ID
     * @returns 是否成功移除
     */
    removeWater(id: string): boolean;
    /**
     * 获取水体信息
     * @param id 水体ID
     * @returns 水体信息
     */
    getWater(id: string): WaterInfo | undefined;
    /**
     * 获取所有水体
     * @returns 所有水体信息
     */
    getAllWaters(): WaterInfo[];
    /**
     * 根据类型获取水体
     * @param type 水体类型
     * @returns 指定类型的水体
     */
    getWatersByType(type: WaterPresetType): WaterInfo[];
    /**
     * 更新水体
     * @param id 水体ID
     * @param updates 更新内容
     * @returns 是否成功更新
     */
    updateWater(id: string, updates: Partial<WaterGenerationConfig>): boolean;
    /**
     * 清空所有水体
     */
    clearAllWaters(): void;
    /**
     * 获取水体统计信息
     * @returns 统计信息
     */
    getStats(): WaterStats;
    /**
     * 更新所有水体
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 添加更新回调
     * @param callback 回调函数
     */
    addUpdateCallback(callback: (waterInfo: WaterInfo) => void): void;
    /**
     * 移除更新回调
     * @param callback 回调函数
     */
    removeUpdateCallback(callback: (waterInfo: WaterInfo) => void): void;
    /**
     * 导出水体配置
     * @returns 配置数据
     */
    exportConfig(): any;
    /**
     * 导入水体配置
     * @param config 配置数据
     */
    importConfig(config: any): void;
}
