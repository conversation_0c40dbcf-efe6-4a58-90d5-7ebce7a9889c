/**
 * 地形法线贴图优化器
 * 提供地形法线贴图优化功能，减少内存使用和提高渲染性能
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';
import type { Entity } from '../../core/Entity';
/**
 * 法线贴图优化事件类型
 */
export declare enum NormalMapOptimizationEventType {
    /** 优化开始 */
    OPTIMIZATION_STARTED = "optimization_started",
    /** 优化完成 */
    OPTIMIZATION_COMPLETED = "optimization_completed",
    /** 优化进度 */
    OPTIMIZATION_PROGRESS = "optimization_progress",
    /** 优化错误 */
    OPTIMIZATION_ERROR = "optimization_error"
}
/**
 * 法线贴图优化配置
 */
export interface NormalMapOptimizationConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否使用压缩 */
    useCompression?: boolean;
    /** 压缩质量 (0-1) */
    compressionQuality?: number;
    /** 是否使用法线贴图合并 */
    useNormalMapMerging?: boolean;
    /** 是否使用法线贴图缩放 */
    useNormalMapScaling?: boolean;
    /** 最大纹理大小 */
    maxTextureSize?: number;
    /** 是否使用法线贴图生成 */
    useNormalMapGeneration?: boolean;
    /** 法线贴图生成强度 */
    normalMapGenerationStrength?: number;
    /** 是否使用GPU加速 */
    useGPUAcceleration?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 优化结果
 */
export interface OptimizationResult {
    /** 原始纹理 */
    originalTexture: THREE.Texture;
    /** 优化后的纹理 */
    optimizedTexture: THREE.Texture;
    /** 原始内存使用量（字节） */
    originalSize: number;
    /** 优化后内存使用量（字节） */
    optimizedSize: number;
    /** 优化比率 */
    optimizationRatio: number;
    /** 应用的优化 */
    appliedOptimizations: string[];
    /** 处理时间（毫秒） */
    processingTime: number;
}
/**
 * 地形法线贴图优化器类
 */
export declare class TerrainNormalMapOptimizer {
    /** 是否启用 */
    private enabled;
    /** 是否使用压缩 */
    private useCompression;
    /** 压缩质量 */
    private compressionQuality;
    /** 是否使用法线贴图合并 */
    private useNormalMapMerging;
    /** 是否使用法线贴图缩放 */
    private useNormalMapScaling;
    /** 最大纹理大小 */
    private maxTextureSize;
    /** 是否使用法线贴图生成 */
    private useNormalMapGeneration;
    /** 法线贴图生成强度 */
    private normalMapGenerationStrength;
    /** 是否使用GPU加速 */
    private useGPUAcceleration;
    /** 是否启用调试 */
    private debug;
    /** 缩放因子 */
    private scaleFactor;
    /** 压缩格式 */
    private compressionFormat;
    /** 事件发射器 */
    private eventEmitter;
    /** 工作线程 */
    private _worker;
    /** 是否支持工作线程 */
    private supportsWorkers;
    /** 是否支持GPU加速 */
    private _supportsGPUAcceleration;
    /** 渲染器 */
    private _renderer;
    /**
     * 创建地形法线贴图优化器
     * @param config 配置
     */
    constructor(config?: NormalMapOptimizationConfig);
    /**
     * 检查GPU加速支持
     * @returns 是否支持GPU加速
     */
    private checkGPUAccelerationSupport;
    /**
     * 初始化工作线程
     */
    private initWorker;
    /**
     * 处理工作线程消息
     * @param event 消息事件
     */
    private _handleWorkerMessage;
    /**
     * 设置渲染器
     * @param renderer 渲染器
     */
    setRenderer(renderer: THREE.WebGLRenderer): void;
    /**
     * 优化法线贴图
     * @param texture 纹理
     * @returns 优化结果
     */
    optimizeNormalMap(texture: THREE.Texture): Promise<OptimizationResult>;
    /**
     * 创建虚拟结果
     * @param texture 纹理
     * @returns 优化结果
     */
    private createDummyResult;
    /**
     * 计算纹理大小
     * @param texture 纹理
     * @returns 大小（字节）
     */
    private calculateTextureSize;
    /**
     * 应用法线贴图缩放
     * @param result 优化结果
     */
    private applyNormalMapScaling;
    /**
     * 应用压缩
     * @param result 优化结果
     */
    private applyCompression;
    /**
     * 简单的纹理压缩
     * @param texture 纹理
     * @returns 压缩后的纹理
     */
    private simpleTextureCompression;
    /**
     * 从高度图生成法线贴图
     * @param heightMap 高度图
     * @param strength 强度
     * @returns 法线贴图
     */
    generateNormalMapFromHeightMap(heightMap: THREE.Texture, strength?: number): Promise<THREE.Texture>;
    /**
     * 获取高度值
     * @param pixels 像素数据
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @returns 高度值
     */
    private getHeightValue;
    /**
     * 应用到地形组件
     * @param entity 实体
     * @param component 地形组件
     * @returns 优化结果数组
     */
    applyToTerrainComponent(entity: Entity, component: TerrainComponent): Promise<OptimizationResult[]>;
    /**
     * 注册事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    on(event: NormalMapOptimizationEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    off(event: NormalMapOptimizationEventType, listener: (...args: any[]) => void): void;
}
