/**
 * 高级带宽控制器
 * 提供更精细的带宽控制和优化策略
 */
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkQualityData } from './NetworkQualityMonitor';
import { DataPriority } from './types';
/**
 * 带宽控制策略
 */
export declare enum BandwidthControlStrategy {
    /** 固定带宽 */
    FIXED = "fixed",
    /** 自适应带宽 */
    ADAPTIVE = "adaptive",
    /** 质量优先 */
    QUALITY_FIRST = "quality_first",
    /** 性能优先 */
    PERFORMANCE_FIRST = "performance_first",
    /** 优先级动态分配 */
    DYNAMIC_PRIORITY = "dynamic_priority",
    /** 预测性带宽分配 */
    PREDICTIVE = "predictive"
}
/**
 * 带宽分配模式
 */
export declare enum BandwidthAllocationMode {
    /** 平均分配 */
    EQUAL = "equal",
    /** 按优先级分配 */
    PRIORITY = "priority",
    /** 按需分配 */
    ON_DEMAND = "on_demand",
    /** 动态分配 */
    DYNAMIC = "dynamic"
}
/**
 * 带宽使用数据
 */
export interface BandwidthUsageData {
    /** 上行带宽使用（字节/秒） */
    upload: number;
    /** 下行带宽使用（字节/秒） */
    download: number;
    /** 上行带宽限制（字节/秒） */
    uploadLimit: number;
    /** 下行带宽限制（字节/秒） */
    downloadLimit: number;
    /** 上行带宽使用率（0-1） */
    uploadUsageRatio: number;
    /** 下行带宽使用率（0-1） */
    downloadUsageRatio: number;
    /** 按优先级的带宽使用 */
    priorityUsage: Map<DataPriority, number>;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 带宽分配配置
 */
export interface BandwidthAllocationConfig {
    /** 分配模式 */
    mode: BandwidthAllocationMode;
    /** 优先级分配比例 */
    priorityAllocation?: {
        [key in DataPriority]?: number;
    };
    /** 最小保证带宽（字节/秒） */
    minimumGuaranteed?: {
        [key in DataPriority]?: number;
    };
    /** 是否允许借用未使用带宽 */
    allowBorrowing?: boolean;
    /** 是否启用动态调整 */
    enableDynamicAdjustment?: boolean;
}
/**
 * 高级带宽控制器配置
 */
export interface AdvancedBandwidthControllerConfig {
    /** 最大上行带宽（字节/秒） */
    maxUploadBandwidth?: number;
    /** 最大下行带宽（字节/秒） */
    maxDownloadBandwidth?: number;
    /** 带宽控制策略 */
    strategy?: BandwidthControlStrategy;
    /** 带宽分配配置 */
    allocation?: BandwidthAllocationConfig;
    /** 带宽使用目标（0-1，表示最大带宽的使用比例） */
    targetUsage?: number;
    /** 是否启用自动调整 */
    autoAdjust?: boolean;
    /** 调整间隔（毫秒） */
    adjustInterval?: number;
    /** 是否启用突发流量控制 */
    enableBurstControl?: boolean;
    /** 突发流量最大倍率 */
    burstMultiplier?: number;
    /** 突发流量最大持续时间（毫秒） */
    burstDuration?: number;
    /** 是否启用预测性带宽分配 */
    enablePredictiveAllocation?: boolean;
    /** 预测窗口大小（毫秒） */
    predictiveWindowSize?: number;
    /** 是否启用带宽平滑过渡 */
    enableSmoothTransition?: boolean;
    /** 平滑因子（0-1） */
    smoothFactor?: number;
    /** 是否启用带宽使用历史记录 */
    enableHistory?: boolean;
    /** 历史记录大小 */
    historySize?: number;
}
/**
 * 带宽请求
 */
export interface BandwidthRequest {
    /** 请求ID */
    id: string;
    /** 数据大小（字节） */
    size: number;
    /** 优先级 */
    priority: DataPriority;
    /** 最大延迟（毫秒） */
    maxDelay: number;
    /** 请求时间 */
    timestamp: number;
    /** 是否已分配 */
    allocated: boolean;
    /** 分配的带宽（字节/秒） */
    allocatedBandwidth: number;
}
/**
 * 高级带宽控制器
 */
export declare class AdvancedBandwidthController extends EventEmitter {
    /** 配置 */
    private config;
    /** 当前上行带宽限制（字节/秒） */
    private currentUploadLimit;
    /** 当前下行带宽限制（字节/秒） */
    private currentDownloadLimit;
    /** 当前上行带宽使用（字节/秒） */
    private currentUploadUsage;
    /** 当前下行带宽使用（字节/秒） */
    private currentDownloadUsage;
    /** 上行数据计数（当前周期） */
    private uploadByteCount;
    /** 下行数据计数（当前周期） */
    private downloadByteCount;
    /** 上次计数重置时间 */
    private lastResetTime;
    /** 调整定时器ID */
    private adjustTimerId;
    /** 优先级带宽分配 */
    private priorityBandwidthAllocation;
    /** 优先级带宽使用 */
    private priorityBandwidthUsage;
    /** 带宽请求队列 */
    private bandwidthRequests;
    /** 最近的网络质量数据 */
    private latestNetworkQuality;
    /** 带宽使用历史 */
    private usageHistory;
    /** 突发流量状态 */
    private burstState;
    /** 带宽预测数据 */
    private bandwidthPrediction;
    /**
     * 创建高级带宽控制器
     * @param config 配置
     */
    constructor(config?: AdvancedBandwidthControllerConfig);
    /**
     * 启动自动调整
     */
    startAutoAdjust(): void;
    /**
     * 停止自动调整
     */
    stopAutoAdjust(): void;
    /**
     * 记录上行数据
     * @param bytes 字节数
     * @param priority 数据优先级
     */
    recordUpload(bytes: number, priority?: DataPriority): void;
    /**
     * 记录下行数据
     * @param bytes 字节数
     * @param priority 数据优先级
     */
    recordDownload(bytes: number, priority?: DataPriority): void;
    /**
     * 重置计数器
     */
    private resetCounters;
    /**
     * 调整带宽
     */
    private adjustBandwidth;
    /**
     * 自适应调整带宽
     */
    private adjustAdaptive;
    /**
     * 质量优先调整带宽
     */
    private adjustQualityFirst;
    /**
     * 性能优先调整带宽
     */
    private adjustPerformanceFirst;
    /**
     * 动态优先级调整带宽
     */
    private adjustDynamicPriority;
    /**
     * 预测性调整带宽
     */
    private adjustPredictive;
    /**
     * 根据网络质量调整带宽
     */
    private adjustBasedOnNetworkQuality;
    /**
     * 预测带宽使用
     */
    private predictBandwidthUsage;
    /**
     * 计算线性趋势
     * @param data 数据
     * @returns 趋势
     */
    private calculateLinearTrend;
    /**
     * 平滑调整数值
     * @param currentValue 当前值
     * @param targetValue 目标值
     * @returns 调整后的值
     */
    private smoothAdjust;
    /**
     * 更新优先级带宽分配
     */
    private updatePriorityBandwidthAllocation;
    /**
     * 启动突发流量模式
     */
    private startBurstMode;
    /**
     * 停止突发流量模式
     */
    private stopBurstMode;
    /**
     * 处理带宽请求
     */
    private processBandwidthRequests;
    /**
     * 请求带宽
     * @param size 数据大小（字节）
     * @param priority 优先级
     * @param maxDelay 最大延迟（毫秒）
     * @returns 请求ID
     */
    requestBandwidth(size: number, priority?: DataPriority, maxDelay?: number): string;
    /**
     * 取消带宽请求
     * @param requestId 请求ID
     * @returns 是否成功取消
     */
    cancelBandwidthRequest(requestId: string): boolean;
    /**
     * 设置网络质量数据
     * @param quality 网络质量数据
     */
    setNetworkQuality(quality: NetworkQualityData): void;
    /**
     * 设置最大带宽
     * @param uploadBandwidth 上行带宽（字节/秒）
     * @param downloadBandwidth 下行带宽（字节/秒）
     */
    setMaxBandwidth(uploadBandwidth: number, downloadBandwidth: number): void;
    /**
     * 获取带宽使用数据
     * @returns 带宽使用数据
     */
    getBandwidthUsage(): BandwidthUsageData;
    /**
     * 获取带宽使用历史
     * @returns 带宽使用历史
     */
    getBandwidthUsageHistory(): BandwidthUsageData[];
    /**
     * 获取带宽预测
     * @returns 带宽预测
     */
    getBandwidthPrediction(): {
        predictedUpload: number;
        predictedDownload: number;
        confidence: number;
    };
    /**
     * 设置带宽控制策略
     * @param strategy 带宽控制策略
     */
    setStrategy(strategy: BandwidthControlStrategy): void;
    /**
     * 设置带宽分配配置
     * @param allocation 带宽分配配置
     */
    setAllocationConfig(allocation: BandwidthAllocationConfig): void;
    /**
     * 重置带宽控制器
     */
    reset(): void;
    /**
     * 销毁带宽控制器
     */
    dispose(): void;
}
