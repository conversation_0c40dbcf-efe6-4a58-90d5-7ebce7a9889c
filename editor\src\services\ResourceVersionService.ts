/**
 * 资源版本服务
 * 负责管理资源版本控制功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
import { store } from '../store';
import {
  addResourceVersion,
  setResourceVersions,
  setCurrentResourceVersion,
  deleteResourceVersion,
  setShowResourceVersionPanel,
  setIsRollingBack,
  setComparisonResult,
  type ResourceVersion,
  type VersionComparisonResult
} from '../store/resources/resourceVersionSlice';

// 模拟版本管理器
class EnhancedResourceVersionManager {
  private static instance: EnhancedResourceVersionManager;
  private versions: Map<string, ResourceVersion[]> = new Map();

  constructor(_config: any) {
    // 配置暂时不使用，但保留接口兼容性
  }

  static getInstance(config: any): EnhancedResourceVersionManager {
    if (!this.instance) {
      this.instance = new EnhancedResourceVersionManager(config);
    }
    return this.instance;
  }

  on(_event: string, _callback: (data: any) => void): void {
    // 模拟事件监听
  }

  getVersions(resourceId: string): ResourceVersion[] {
    return this.versions.get(resourceId) || [];
  }

  getLatestVersion(resourceId: string): ResourceVersion | null {
    const versions = this.getVersions(resourceId);
    return versions.length > 0 ? versions[versions.length - 1] : null;
  }

  getVersion(resourceId: string, versionId: string): ResourceVersion | null {
    const versions = this.getVersions(resourceId);
    return versions.find(v => v.id === versionId) || null;
  }

  createVersion(
    resourceId: string,
    _url: string,
    _type: string,
    hash: string,
    size: number,
    metadata: Record<string, any>,
    description: string,
    userId: string,
    userName: string,
    tags: string[]
  ): ResourceVersion {
    const version: ResourceVersion = {
      id: `version_${Date.now()}`,
      resourceId,
      version: `v${Date.now()}`,
      timestamp: Date.now(),
      author: userId,
      userName,
      description,
      tags,
      size,
      checksum: hash,
      metadata
    };

    const versions = this.getVersions(resourceId);
    versions.push(version);
    this.versions.set(resourceId, versions);

    return version;
  }

  deleteVersion(resourceId: string, versionId: string): boolean {
    const versions = this.getVersions(resourceId);
    const index = versions.findIndex(v => v.id === versionId);
    if (index >= 0) {
      versions.splice(index, 1);
      this.versions.set(resourceId, versions);
      return true;
    }
    return false;
  }

  compareVersions(resourceId: string, versionId1: string, versionId2: string): VersionComparisonResult | null {
    const version1 = this.getVersion(resourceId, versionId1);
    const version2 = this.getVersion(resourceId, versionId2);

    if (!version1 || !version2) {
      return null;
    }

    return {
      version1,
      version2,
      hasDifferences: false,
      differenceType: 'none',
      comparisonTimestamp: Date.now(),
      differences: [],
      similarity: 0.8
    };
  }

  addVersionTag(resourceId: string, versionId: string, tag: string): boolean {
    const version = this.getVersion(resourceId, versionId);
    if (version && version.tags && !version.tags.includes(tag)) {
      version.tags.push(tag);
      return true;
    } else if (version && !version.tags) {
      version.tags = [tag];
      return true;
    }
    return false;
  }

  removeVersionTag(resourceId: string, versionId: string, tag: string): boolean {
    const version = this.getVersion(resourceId, versionId);
    if (version && version.tags) {
      const index = version.tags.indexOf(tag);
      if (index >= 0) {
        version.tags.splice(index, 1);
        return true;
      }
    }
    return false;
  }

  updateVersionDescription(resourceId: string, versionId: string, description: string): boolean {
    const version = this.getVersion(resourceId, versionId);
    if (version) {
      version.description = description;
      return true;
    }
    return false;
  }
}

/**
 * 资源版本服务事件类型
 */
export enum ResourceVersionServiceEventType {
  VERSION_CREATED = 'versionCreated',
  VERSION_DELETED = 'versionDeleted',
  VERSION_ROLLBACK = 'versionRollback',
  VERSION_COMPARED = 'versionCompared',
  VERSION_TAGGED = 'versionTagged',
  VERSION_DESCRIPTION_UPDATED = 'versionDescriptionUpdated'
}

/**
 * 资源版本服务
 */
export class ResourceVersionService extends EventEmitter {
  /** 版本管理器 */
  private versionManager: EnhancedResourceVersionManager;
  
  /** 当前资源ID */
  private currentResourceId: string | null = null;
  
  /** 当前版本ID */
  private currentVersionId: string | null = null;
  
  /** 最大版本数量 */
  private maxVersions: number = 10;
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /** 单例实例 */
  private static instance: ResourceVersionService;
  
  /**
   * 获取单例实例
   * @returns 单例实例
   */
  public static getInstance(): ResourceVersionService {
    if (!ResourceVersionService.instance) {
      ResourceVersionService.instance = new ResourceVersionService();
    }
    return ResourceVersionService.instance;
  }
  
  /**
   * 创建资源版本服务实例
   */
  private constructor() {
    super();
    
    // 创建版本管理器
    this.versionManager = EnhancedResourceVersionManager.getInstance({
      debug: true,
      maxVersionsPerResource: this.maxVersions
    });
    
    // 转发版本管理器事件
    this.versionManager.on('versionCreated', (version: ResourceVersion) => {
      this.emit(ResourceVersionServiceEventType.VERSION_CREATED, version);
    });

    this.versionManager.on('versionDeleted', (version: ResourceVersion) => {
      this.emit(ResourceVersionServiceEventType.VERSION_DELETED, version);
    });

    this.versionManager.on('versionRollback', (version: ResourceVersion) => {
      this.emit(ResourceVersionServiceEventType.VERSION_ROLLBACK, version);
    });

    this.versionManager.on('versionCompared', (result: VersionComparisonResult) => {
      this.emit(ResourceVersionServiceEventType.VERSION_COMPARED, result);
    });

    this.versionManager.on('versionTagged', (data: any) => {
      this.emit(ResourceVersionServiceEventType.VERSION_TAGGED, data);
    });
  }
  
  /**
   * 初始化服务
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }
    
    this.initialized = true;
    console.log('[ResourceVersionService] 初始化完成');
  }
  
  /**
   * 设置当前资源
   * @param resourceId 资源ID
   */
  public setCurrentResource(resourceId: string): void {
    this.currentResourceId = resourceId;
    
    // 获取资源的版本列表
    const versions = this.versionManager.getVersions(resourceId);
    
    // 更新Redux状态
    store.dispatch(setResourceVersions(versions));
    
    // 如果有版本，设置最新版本为当前版本
    if (versions.length > 0) {
      const latestVersion = this.versionManager.getLatestVersion(resourceId);
      if (latestVersion) {
        this.currentVersionId = latestVersion.id;
        store.dispatch(setCurrentResourceVersion(latestVersion.id));
      }
    } else {
      this.currentVersionId = null;
      store.dispatch(setCurrentResourceVersion(null));
    }
  }
  
  /**
   * 创建资源版本
   * @param resourceId 资源ID
   * @param url 资源URL
   * @param type 资源类型
   * @param hash 资源哈希
   * @param size 资源大小
   * @param metadata 资源元数据
   * @param description 版本描述
   * @param tags 标签
   * @returns 创建的版本
   */
  public createVersion(
    resourceId: string,
    url: string,
    type: string,
    hash: string,
    size: number,
    metadata: Record<string, any> = {},
    description: string = '创建新版本',
    tags: string[] = []
  ): ResourceVersion {
    // 获取当前用户信息
    const userId = this.getUserId();
    const userName = this.getUserName();

    // 创建版本
    const version = this.versionManager.createVersion(
      resourceId,
      url,
      type,
      hash,
      size,
      metadata,
      description,
      userId,
      userName,
      tags
    );
    
    // 更新Redux状态
    store.dispatch(addResourceVersion(version));
    store.dispatch(setCurrentResourceVersion(version.id));
    
    // 更新当前版本ID
    this.currentVersionId = version.id;
    
    // 通知用户
    message.success(`已创建版本: ${description}`);
    
    return version;
  }
  
  /**
   * 删除资源版本
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @returns 是否成功删除
   */
  public deleteVersion(resourceId: string, versionId: string): boolean {
    // 删除版本
    const success = this.versionManager.deleteVersion(resourceId, versionId);
    
    if (success) {
      // 更新Redux状态
      store.dispatch(deleteResourceVersion(versionId));
      
      // 如果删除的是当前版本，设置最新版本为当前版本
      if (this.currentVersionId === versionId) {
        const latestVersion = this.versionManager.getLatestVersion(resourceId);
        if (latestVersion) {
          this.currentVersionId = latestVersion.id;
          store.dispatch(setCurrentResourceVersion(latestVersion.id));
        } else {
          this.currentVersionId = null;
          store.dispatch(setCurrentResourceVersion(null));
        }
      }
      
      // 通知用户
      message.success('已删除版本');
    } else {
      message.error('删除版本失败');
    }
    
    return success;
  }
  
  /**
   * 回滚到指定版本
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @returns 是否成功回滚
   */
  public rollbackToVersion(resourceId: string, versionId: string): boolean {
    // 获取版本
    const version = this.versionManager.getVersion(resourceId, versionId);
    
    if (!version) {
      console.error(`找不到版本: ${versionId}`);
      message.error('找不到要回滚的版本');
      return false;
    }
    
    try {
      // 设置回滚状态
      store.dispatch(setIsRollingBack(true));
      
      // TODO: 实现实际的资源回滚逻辑
      // 这里需要根据具体的资源管理系统实现回滚逻辑
      // 例如，加载旧版本的资源文件，替换当前资源等
      
      // 设置为当前版本
      this.currentVersionId = version.id;
      
      // 更新Redux状态
      store.dispatch(setCurrentResourceVersion(version.id));
      
      // 发出版本回滚事件
      this.emit(ResourceVersionServiceEventType.VERSION_ROLLBACK, version);
      
      // 通知用户
      message.success(`已回滚到版本: ${version.description}`);
      
      return true;
    } catch (error) {
      console.error('回滚版本时出错:', error);
      message.error('回滚版本失败');
      return false;
    } finally {
      // 清除回滚状态
      store.dispatch(setIsRollingBack(false));
    }
  }
  
  /**
   * 比较两个版本
   * @param resourceId 资源ID
   * @param versionId1 版本1 ID
   * @param versionId2 版本2 ID
   * @returns 比较结果
   */
  public compareVersions(resourceId: string, versionId1: string, versionId2: string): VersionComparisonResult | null {
    // 比较版本
    const result = this.versionManager.compareVersions(resourceId, versionId1, versionId2);
    
    if (result) {
      // 更新Redux状态
      store.dispatch(setComparisonResult(result));
    } else {
      message.error('比较版本失败');
    }
    
    return result;
  }
  
  /**
   * 添加版本标签
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @param tag 标签
   * @returns 是否成功添加
   */
  public addVersionTag(resourceId: string, versionId: string, tag: string): boolean {
    // 添加标签
    const success = this.versionManager.addVersionTag(resourceId, versionId, tag);
    
    if (success) {
      // 更新Redux状态
      const versions = this.versionManager.getVersions(resourceId);
      store.dispatch(setResourceVersions(versions));
      
      // 通知用户
      message.success(`已添加标签: ${tag}`);
    } else {
      message.error('添加标签失败');
    }
    
    return success;
  }
  
  /**
   * 移除版本标签
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @param tag 标签
   * @returns 是否成功移除
   */
  public removeVersionTag(resourceId: string, versionId: string, tag: string): boolean {
    // 移除标签
    const success = this.versionManager.removeVersionTag(resourceId, versionId, tag);
    
    if (success) {
      // 更新Redux状态
      const versions = this.versionManager.getVersions(resourceId);
      store.dispatch(setResourceVersions(versions));
      
      // 通知用户
      message.success(`已移除标签: ${tag}`);
    } else {
      message.error('移除标签失败');
    }
    
    return success;
  }
  
  /**
   * 更新版本描述
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @param description 新描述
   * @returns 是否成功更新
   */
  public updateVersionDescription(resourceId: string, versionId: string, description: string): boolean {
    // 更新描述
    const success = this.versionManager.updateVersionDescription(resourceId, versionId, description);
    
    if (success) {
      // 更新Redux状态
      const versions = this.versionManager.getVersions(resourceId);
      store.dispatch(setResourceVersions(versions));
      
      // 发出描述更新事件
      this.emit(ResourceVersionServiceEventType.VERSION_DESCRIPTION_UPDATED, {
        resourceId,
        versionId,
        description
      });
      
      // 通知用户
      message.success('已更新版本描述');
    } else {
      message.error('更新版本描述失败');
    }
    
    return success;
  }
  
  /**
   * 显示版本历史面板
   * @param show 是否显示
   */
  public showVersionPanel(show: boolean): void {
    store.dispatch(setShowResourceVersionPanel(show));
  }

  /**
   * 获取当前资源ID
   * @returns 当前资源ID
   */
  public getCurrentResourceId(): string | null {
    return this.currentResourceId;
  }

  /**
   * 获取当前版本ID
   * @returns 当前版本ID
   */
  public getCurrentVersionId(): string | null {
    return this.currentVersionId;
  }
  
  /**
   * 获取用户ID
   * @returns 用户ID
   */
  private getUserId(): string {
    // TODO: 从认证服务或Redux状态获取当前用户ID
    return 'user-123';
  }
  
  /**
   * 获取用户名
   * @returns 用户名
   */
  private getUserName(): string {
    // TODO: 从认证服务或Redux状态获取当前用户名
    return '当前用户';
  }
}

// 导出单例实例
export const resourceVersionService = ResourceVersionService.getInstance();
