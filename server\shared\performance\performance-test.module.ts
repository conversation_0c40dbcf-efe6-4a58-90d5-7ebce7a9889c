/**
 * 性能测试模块
 */
import { DynamicModule, Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { PerformanceTestService } from './performance-test.service';

/**
 * 性能测试模块配置
 */
export interface PerformanceTestModuleOptions {
  /**
   * 是否全局注册
   */
  isGlobal?: boolean;
}

@Module({})
export class PerformanceTestModule {
  /**
   * 注册性能测试模块
   * @param options 模块配置
   */
  static register(options: PerformanceTestModuleOptions = {}): DynamicModule {
    return {
      global: options.isGlobal,
      imports: [EventEmitterModule.forRoot()],
      module: PerformanceTestModule,
      providers: [PerformanceTestService],
      exports: [PerformanceTestService],
    };
  }
}
