/**
 * 时间管理兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 时间管理兼容性测试
 */
export const timeManagementTest: TestCase = {
  name: '时间管理兼容性测试',
  description: '测试时间管理功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目时间管理实例
      const originalTime = new original.Time();
      
      // 创建重构后项目时间管理实例
      const refactoredTime = new refactored.Time();
      
      // 检查时间管理实例是否创建成功
      if (!originalTime || !refactoredTime) {
        return {
          name: '时间管理兼容性测试',
          passed: false,
          errorMessage: '时间管理实例创建失败'
        };
      }
      
      // 检查时间管理实例的属性和方法
      const originalMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(originalTime));
      const refactoredMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(refactoredTime));
      
      // 检查重构后的时间管理是否包含原有时间管理的所有方法
      const missingMethods = originalMethods.filter(method => {
        // 忽略构造函数和私有方法
        if (method === 'constructor' || method.startsWith('_')) {
          return false;
        }
        
        return !refactoredMethods.includes(method);
      });
      
      if (missingMethods.length > 0) {
        return {
          name: '时间管理兼容性测试',
          passed: false,
          errorMessage: `重构后的时间管理缺少以下方法: ${missingMethods.join(', ')}`,
          details: {
            originalMethods,
            refactoredMethods,
            missingMethods
          }
        };
      }
      
      // 测试时间更新
      originalTime.update(0.016);
      refactoredTime.update(0.016);
      
      // 检查时间更新后的状态
      const originalDeltaTime = originalTime.getDeltaTime();
      const refactoredDeltaTime = refactoredTime.getDeltaTime();
      
      if (Math.abs(originalDeltaTime - refactoredDeltaTime) > 0.001) {
        return {
          name: '时间管理兼容性测试',
          passed: false,
          errorMessage: `时间更新后的deltaTime不一致: 原有项目=${originalDeltaTime}, 重构后项目=${refactoredDeltaTime}`,
          details: {
            originalDeltaTime,
            refactoredDeltaTime
          }
        };
      }
      
      // 测试时间缩放
      originalTime.setTimeScale(0.5);
      refactoredTime.setTimeScale(0.5);
      
      // 更新时间
      originalTime.update(0.016);
      refactoredTime.update(0.016);
      
      // 检查时间缩放后的状态
      const originalScaledDeltaTime = originalTime.getDeltaTime();
      const refactoredScaledDeltaTime = refactoredTime.getDeltaTime();
      
      if (Math.abs(originalScaledDeltaTime - refactoredScaledDeltaTime) > 0.001) {
        return {
          name: '时间管理兼容性测试',
          passed: false,
          errorMessage: `时间缩放后的deltaTime不一致: 原有项目=${originalScaledDeltaTime}, 重构后项目=${refactoredScaledDeltaTime}`,
          details: {
            originalScaledDeltaTime,
            refactoredScaledDeltaTime
          }
        };
      }
      
      // 测试时间暂停
      originalTime.setPaused(true);
      refactoredTime.setPaused(true);
      
      // 更新时间
      originalTime.update(0.016);
      refactoredTime.update(0.016);
      
      // 检查时间暂停后的状态
      const originalPausedDeltaTime = originalTime.getDeltaTime();
      const refactoredPausedDeltaTime = refactoredTime.getDeltaTime();
      
      if (Math.abs(originalPausedDeltaTime - refactoredPausedDeltaTime) > 0.001) {
        return {
          name: '时间管理兼容性测试',
          passed: false,
          errorMessage: `时间暂停后的deltaTime不一致: 原有项目=${originalPausedDeltaTime}, 重构后项目=${refactoredPausedDeltaTime}`,
          details: {
            originalPausedDeltaTime,
            refactoredPausedDeltaTime
          }
        };
      }
      
      return {
        name: '时间管理兼容性测试',
        passed: true,
        details: {
          originalMethods,
          refactoredMethods,
          originalDeltaTime,
          refactoredDeltaTime,
          originalScaledDeltaTime,
          refactoredScaledDeltaTime,
          originalPausedDeltaTime,
          refactoredPausedDeltaTime
        }
      };
    } catch (error) {
      return {
        name: '时间管理兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
