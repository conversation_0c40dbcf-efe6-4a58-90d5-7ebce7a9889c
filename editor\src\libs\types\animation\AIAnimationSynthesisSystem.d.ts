/**
 * AI动画合成系统
 * 用于管理和更新AI动画合成组件
 */
import type { Entity } from '../core/Entity';
import { System } from '../core/System';
import type { World } from '../core/World';
import { AIAnimationSynthesisComponent, AIAnimationSynthesisConfig, AnimationGenerationRequest, AnimationGenerationResult } from './AIAnimationSynthesis';
/**
 * AI动画合成系统
 */
export declare class AIAnimationSynthesisSystem extends System {
    /** 系统类型 */
    static readonly type = "AIAnimationSynthesis";
    /** AI动画合成组件 */
    private components;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 事件发射器 */
    private eventEmitter;
    /** AI模型 */
    private aiModel;
    /** 模型是否已加载 */
    private modelLoaded;
    /** 模型加载进度 */
    private modelLoadProgress;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(_world: World, config?: Partial<AIAnimationSynthesisConfig>);
    /**
     * 初始化AI模型
     */
    private initModel;
    /**
     * 创建AI动画合成组件
     * @param entity 实体
     * @returns AI动画合成组件
     */
    createAIAnimationSynthesis(entity: Entity): AIAnimationSynthesisComponent;
    /**
     * 移除AI动画合成组件
     * @param entity 实体
     */
    removeAIAnimationSynthesis(entity: Entity): void;
    /**
     * 获取AI动画合成组件
     * @param entity 实体
     * @returns AI动画合成组件，如果不存在则返回null
     */
    getAIAnimationSynthesis(entity: Entity): AIAnimationSynthesisComponent | null;
    /**
     * 生成身体动画
     * @param entity 实体
     * @param prompt 提示文本
     * @param duration 持续时间（秒）
     * @param options 其他选项
     * @returns 请求ID
     */
    generateBodyAnimation(entity: Entity, prompt: string, duration?: number, options?: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>>): string | null;
    /**
     * 生成面部动画
     * @param entity 实体
     * @param prompt 提示文本
     * @param duration 持续时间（秒）
     * @param options 其他选项
     * @returns 请求ID
     */
    generateFacialAnimation(entity: Entity, prompt: string, duration?: number, options?: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>>): string | null;
    /**
     * 生成组合动画
     * @param entity 实体
     * @param prompt 提示文本
     * @param duration 持续时间（秒）
     * @param options 其他选项
     * @returns 请求ID
     */
    generateCombinedAnimation(entity: Entity, prompt: string, duration?: number, options?: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>>): string | null;
    /**
     * 取消生成请求
     * @param entity 实体
     * @param requestId 请求ID
     * @returns 是否成功取消
     */
    cancelRequest(entity: Entity, requestId: string): boolean;
    /**
     * 获取生成结果
     * @param entity 实体
     * @param requestId 请求ID
     * @returns 生成结果，如果不存在则返回null
     */
    getResult(entity: Entity, requestId: string): AnimationGenerationResult | null;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
