@echo off
echo ========================================
echo 停止数字人RAG交互系统微服务集群
echo ========================================

REM 设置环境变量
set COMPOSE_PROJECT_NAME=digital-human-rag

REM 检查Docker是否运行
echo 检查Docker服务状态...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker服务未运行
    pause
    exit /b 1
)

REM 显示当前运行的服务
echo 当前运行的服务:
docker-compose -f docker-compose.windows.yml ps

echo.
echo 是否要停止所有服务？(Y/n)
set /p confirm=
if /i "%confirm%"=="n" (
    echo 操作已取消
    pause
    exit /b 0
)

REM 停止服务
echo 正在停止微服务...
docker-compose -f docker-compose.windows.yml down

echo.
echo 是否要删除数据卷？(y/N)
echo 警告: 这将删除所有数据库数据、文件存储等
set /p cleanup=
if /i "%cleanup%"=="y" (
    echo 删除数据卷...
    docker-compose -f docker-compose.windows.yml down -v
    echo 清理未使用的卷...
    docker volume prune -f
)

echo.
echo 是否要清理未使用的镜像？(y/N)
set /p cleanup_images=
if /i "%cleanup_images%"=="y" (
    echo 清理未使用的镜像...
    docker image prune -f
    echo 清理构建缓存...
    docker builder prune -f
)

echo ========================================
echo 微服务集群已停止
echo ========================================

pause
