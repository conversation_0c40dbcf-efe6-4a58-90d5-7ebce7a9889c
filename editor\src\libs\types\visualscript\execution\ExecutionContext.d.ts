/**
 * 视觉脚本执行上下文
 * 提供视觉脚本执行时的上下文环境
 */
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { VisualScriptEngine } from '../VisualScriptEngine';
/**
 * 执行上下文选项
 */
export interface ExecutionContextOptions {
    /** 视觉脚本引擎 */
    engine: VisualScriptEngine;
    /** 所属实体 */
    entity: Entity;
    /** 所属世界 */
    world: World;
}
/**
 * 视觉脚本执行上下文
 * 提供视觉脚本执行时的上下文环境
 */
export declare class ExecutionContext {
    /** 视觉脚本引擎 */
    readonly engine: VisualScriptEngine;
    /** 所属实体 */
    readonly entity: Entity;
    /** 所属世界 */
    readonly world: World;
    /** 上下文变量 */
    private variables;
    /** 上下文函数 */
    private functions;
    /**
     * 创建执行上下文
     * @param options 上下文选项
     */
    constructor(options: ExecutionContextOptions);
    /**
     * 初始化上下文
     */
    private initialize;
    /**
     * 注册内置函数
     */
    private registerBuiltinFunctions;
    /**
     * 设置变量
     * @param name 变量名称
     * @param value 变量值
     */
    setVariable(name: string, value: any): void;
    /**
     * 获取变量
     * @param name 变量名称
     * @returns 变量值
     */
    getVariable(name: string): any;
    /**
     * 删除变量
     * @param name 变量名称
     * @returns 是否删除成功
     */
    deleteVariable(name: string): boolean;
    /**
     * 注册函数
     * @param name 函数名称
     * @param func 函数实现
     */
    registerFunction(name: string, func: Function): void;
    /**
     * 获取函数
     * @param name 函数名称
     * @returns 函数实现
     */
    getFunction(name: string): Function | undefined;
    /**
     * 调用函数
     * @param name 函数名称
     * @param args 函数参数
     * @returns 函数返回值
     */
    callFunction(name: string, ...args: any[]): any;
    /**
     * 清空上下文
     */
    clear(): void;
}
