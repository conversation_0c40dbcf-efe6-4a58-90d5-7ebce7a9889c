/**
 * 增强水系统示例
 * 展示如何使用增强水体系统创建不同类型的水体
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { RenderingSystem } from '../../engine/src/rendering/RenderingSystem';
import { CameraSystem } from '../../engine/src/core/CameraSystem';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { PhysicsSystem } from '../../engine/src/physics/PhysicsSystem';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../engine/src/physics/water/WaterInteractionSystem';
import { EnhancedWaterSurfaceRenderer } from '../../engine/src/rendering/water/EnhancedWaterSurfaceRenderer';
import { WaterBodyComponent, WaterBodyType } from '../../engine/src/physics/water/WaterBodyComponent';
import { RiverWaterComponent } from '../../engine/src/physics/water/RiverWaterComponent';
import { LakeWaterComponent, LakeShapeType } from '../../engine/src/physics/water/LakeWaterComponent';
import { OceanWaterComponent, OceanWaveType } from '../../engine/src/physics/water/OceanWaterComponent';
import { Camera } from '../../engine/src/core/Camera';
import { OrbitControls } from '../../engine/src/controls/OrbitControls';
import { TerrainComponent } from '../../engine/src/terrain/TerrainComponent';
import { TerrainSystem } from '../../engine/src/terrain/TerrainSystem';
import { Debug } from '../../engine/src/utils/Debug';

/**
 * 增强水系统示例
 */
export class EnhancedWaterSystemExample {
  /** 世界 */
  private world: World;
  /** 渲染系统 */
  private renderingSystem: RenderingSystem;
  /** 相机系统 */
  private cameraSystem: CameraSystem;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水体交互系统 */
  private waterInteractionSystem: WaterInteractionSystem;
  /** 增强水面渲染器 */
  private enhancedWaterSurfaceRenderer: EnhancedWaterSurfaceRenderer;
  /** 地形系统 */
  private terrainSystem: TerrainSystem;
  /** 相机实体 */
  private cameraEntity: Entity;
  /** 相机控制器 */
  private cameraControls: OrbitControls;
  /** 地形实体 */
  private terrainEntity: Entity;
  /** 河流实体 */
  private riverEntity: Entity;
  /** 湖泊实体 */
  private lakeEntity: Entity;
  /** 海洋实体 */
  private oceanEntity: Entity;
  /** 当前示例索引 */
  private currentExampleIndex: number = 0;
  /** 示例名称 */
  private exampleNames: string[] = [
    '河流',
    '湖泊',
    '海洋',
    '组合场景'
  ];
  /** 渲染循环ID */
  private animationFrameId: number = 0;

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 创建系统
    this.createSystems();

    // 创建场景
    this.createScene();

    // 创建UI
    this.createUI();

    // 注册事件
    this.registerEvents();

    // 启动渲染循环
    this.startRenderLoop();

    Debug.log('EnhancedWaterSystemExample', '增强水系统示例初始化完成');
  }

  /**
   * 创建系统
   */
  private createSystems(): void {
    // 创建渲染系统
    this.renderingSystem = new RenderingSystem(this.world);
    this.world.addSystem(this.renderingSystem);

    // 创建相机系统
    this.cameraSystem = new CameraSystem(this.world);
    this.world.addSystem(this.cameraSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world);
    this.world.addSystem(this.inputSystem);

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem(this.world);
    this.world.addSystem(this.physicsSystem);

    // 创建地形系统
    this.terrainSystem = new TerrainSystem(this.world);
    this.world.addSystem(this.terrainSystem);

    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水体交互系统
    this.waterInteractionSystem = new WaterInteractionSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true
    });
    this.world.addSystem(this.waterInteractionSystem);

    // 创建增强水面渲染器
    this.enhancedWaterSurfaceRenderer = new EnhancedWaterSurfaceRenderer(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      reflectionMapResolution: 512,
      refractionMapResolution: 512,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true,
      enablePerformanceMonitoring: true
    });
    this.world.addSystem(this.enhancedWaterSurfaceRenderer);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建场景
    const scene = new THREE.Scene();
    this.world.setScene(scene);

    // 创建环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
    scene.add(ambientLight);

    // 创建平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(100, 100, 50);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 500;
    directionalLight.shadow.camera.left = -100;
    directionalLight.shadow.camera.right = 100;
    directionalLight.shadow.camera.top = 100;
    directionalLight.shadow.camera.bottom = -100;
    scene.add(directionalLight);

    // 创建相机
    this.cameraEntity = new Entity();
    const camera = new Camera();
    camera.setPerspective(60, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.setPosition(new THREE.Vector3(0, 50, 100));
    camera.lookAt(new THREE.Vector3(0, 0, 0));
    this.cameraEntity.addComponent(camera);
    this.world.addEntity(this.cameraEntity);

    // 创建相机控制器
    this.cameraControls = new OrbitControls(camera.getThreeCamera(), this.renderingSystem.getRenderer().domElement);
    this.cameraControls.enableDamping = true;
    this.cameraControls.dampingFactor = 0.05;

    // 创建地形
    this.createTerrain();

    // 创建示例
    this.createExample(this.currentExampleIndex);
  }

  /**
   * 创建地形
   */
  private createTerrain(): void {
    // 创建地形实体
    this.terrainEntity = new Entity();
    this.terrainEntity.name = 'Terrain';

    // 创建地形组件
    const terrainComponent = new TerrainComponent();
    terrainComponent.setSize(500, 500);
    terrainComponent.setResolution(128, 128);
    terrainComponent.setHeightRange(-20, 20);
    terrainComponent.generateFlatTerrain();
    this.terrainEntity.addComponent(terrainComponent);

    // 添加到世界
    this.world.addEntity(this.terrainEntity);
  }

  /**
   * 创建示例
   * @param index 示例索引
   */
  private createExample(index: number): void {
    // 清除现有水体
    this.clearWaterEntities();

    // 创建示例
    switch (index) {
      case 0:
        this.createRiverExample();
        break;
      case 1:
        this.createLakeExample();
        break;
      case 2:
        this.createOceanExample();
        break;
      case 3:
        this.createCombinedExample();
        break;
      default:
        this.createRiverExample();
        break;
    }
  }

  /**
   * 创建河流示例
   */
  private createRiverExample(): void {
    // 创建河流实体
    this.riverEntity = new Entity();
    this.riverEntity.name = 'River';

    // 创建河流路径点
    const pathPoints = [
      new THREE.Vector3(-100, 0, -50),
      new THREE.Vector3(-50, 0, -20),
      new THREE.Vector3(0, 0, 0),
      new THREE.Vector3(50, 0, 20),
      new THREE.Vector3(100, 0, 50)
    ];

    // 创建河流组件
    const riverComponent = new RiverWaterComponent(this.riverEntity, {
      pathPoints: pathPoints,
      width: 15,
      depth: 5,
      flowSpeed: 1.0,
      curvature: 0.5,
      bedHeightVariation: 1.0,
      bankHeight: 2.0,
      generateBanks: true
    });
    this.riverEntity.addComponent(riverComponent);

    // 添加到世界
    this.world.addEntity(this.riverEntity);

    // 添加到增强水面渲染器
    this.enhancedWaterSurfaceRenderer.addWaterBody(this.riverEntity, riverComponent);
  }

  /**
   * 创建湖泊示例
   */
  private createLakeExample(): void {
    // 创建湖泊实体
    this.lakeEntity = new Entity();
    this.lakeEntity.name = 'Lake';

    // 创建湖泊组件
    const lakeComponent = new LakeWaterComponent(this.lakeEntity, {
      shapeType: LakeShapeType.IRREGULAR,
      width: 80,
      length: 60,
      depth: 15,
      resolution: 32,
      bedHeightVariation: 5.0,
      generateShore: true,
      shoreHeight: 2.0,
      shoreWidth: 8.0
    });
    this.lakeEntity.addComponent(lakeComponent);

    // 添加到世界
    this.world.addEntity(this.lakeEntity);

    // 添加到增强水面渲染器
    this.enhancedWaterSurfaceRenderer.addWaterBody(this.lakeEntity, lakeComponent);
  }

  /**
   * 创建海洋示例
   */
  private createOceanExample(): void {
    // 创建海洋实体
    this.oceanEntity = new Entity();
    this.oceanEntity.name = 'Ocean';

    // 创建海洋组件
    const oceanComponent = new OceanWaterComponent(this.oceanEntity, {
      size: { width: 500, depth: 500 },
      depth: 100,
      resolution: { width: 128, depth: 128 },
      waveType: OceanWaveType.MODERATE,
      waveHeight: 1.5,
      waveFrequency: 1.0,
      waveDirection: { x: 0.7, z: 0.3 },
      useFFTWaves: true,
      enableTides: true,
      tidePeriod: 600, // 10分钟
      tideHeight: 2.0,
      enableSeabed: true,
      seabedHeightVariation: 20.0
    });
    this.oceanEntity.addComponent(oceanComponent);

    // 添加到世界
    this.world.addEntity(this.oceanEntity);

    // 添加到增强水面渲染器
    this.enhancedWaterSurfaceRenderer.addWaterBody(this.oceanEntity, oceanComponent);
  }

  /**
   * 创建组合场景示例
   */
  private createCombinedExample(): void {
    // 创建河流
    this.riverEntity = new Entity();
    this.riverEntity.name = 'River';

    // 创建河流路径点
    const riverPathPoints = [
      new THREE.Vector3(-100, 0, -50),
      new THREE.Vector3(-50, 0, -20),
      new THREE.Vector3(0, 0, 0),
      new THREE.Vector3(50, 0, 20),
      new THREE.Vector3(100, 0, 50)
    ];

    // 创建河流组件
    const riverComponent = new RiverWaterComponent(this.riverEntity, {
      pathPoints: riverPathPoints,
      width: 10,
      depth: 3,
      flowSpeed: 1.0,
      curvature: 0.5,
      bedHeightVariation: 0.5,
      bankHeight: 1.5,
      generateBanks: true
    });
    this.riverEntity.addComponent(riverComponent);

    // 添加到世界
    this.world.addEntity(this.riverEntity);

    // 添加到增强水面渲染器
    this.enhancedWaterSurfaceRenderer.addWaterBody(this.riverEntity, riverComponent);

    // 创建湖泊
    this.lakeEntity = new Entity();
    this.lakeEntity.name = 'Lake';
    this.lakeEntity.setPosition(new THREE.Vector3(100, 0, 50));

    // 创建湖泊组件
    const lakeComponent = new LakeWaterComponent(this.lakeEntity, {
      shapeType: LakeShapeType.CIRCLE,
      width: 50,
      depth: 10,
      resolution: 32,
      bedHeightVariation: 3.0,
      generateShore: true,
      shoreHeight: 1.5,
      shoreWidth: 5.0
    });
    this.lakeEntity.addComponent(lakeComponent);

    // 添加到世界
    this.world.addEntity(this.lakeEntity);

    // 添加到增强水面渲染器
    this.enhancedWaterSurfaceRenderer.addWaterBody(this.lakeEntity, lakeComponent);

    // 创建海洋
    this.oceanEntity = new Entity();
    this.oceanEntity.name = 'Ocean';
    this.oceanEntity.setPosition(new THREE.Vector3(0, -5, -200));

    // 创建海洋组件
    const oceanComponent = new OceanWaterComponent(this.oceanEntity, {
      size: { width: 400, depth: 400 },
      depth: 80,
      resolution: { width: 64, depth: 64 },
      waveType: OceanWaveType.MODERATE,
      waveHeight: 1.0,
      waveFrequency: 0.8,
      waveDirection: { x: 0.7, z: 0.3 },
      useFFTWaves: true,
      enableTides: true,
      tidePeriod: 600, // 10分钟
      tideHeight: 1.5,
      enableSeabed: true,
      seabedHeightVariation: 15.0
    });
    this.oceanEntity.addComponent(oceanComponent);

    // 添加到世界
    this.world.addEntity(this.oceanEntity);

    // 添加到增强水面渲染器
    this.enhancedWaterSurfaceRenderer.addWaterBody(this.oceanEntity, oceanComponent);
  }

  /**
   * 清除水体实体
   */
  private clearWaterEntities(): void {
    // 清除河流实体
    if (this.riverEntity) {
      this.world.removeEntity(this.riverEntity.id);
      this.riverEntity = null;
    }

    // 清除湖泊实体
    if (this.lakeEntity) {
      this.world.removeEntity(this.lakeEntity.id);
      this.lakeEntity = null;
    }

    // 清除海洋实体
    if (this.oceanEntity) {
      this.world.removeEntity(this.oceanEntity.id);
      this.oceanEntity = null;
    }
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    uiContainer.style.color = 'white';
    uiContainer.style.fontFamily = 'Arial, sans-serif';
    uiContainer.style.borderRadius = '5px';
    document.body.appendChild(uiContainer);

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '增强水系统示例';
    title.style.margin = '0 0 10px 0';
    uiContainer.appendChild(title);

    // 创建当前示例显示
    const exampleDisplay = document.createElement('div');
    exampleDisplay.id = 'example-display';
    exampleDisplay.textContent = `当前示例：${this.exampleNames[this.currentExampleIndex]}`;
    exampleDisplay.style.marginBottom = '10px';
    uiContainer.appendChild(exampleDisplay);

    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.gap = '10px';
    uiContainer.appendChild(buttonContainer);

    // 创建上一个示例按钮
    const prevButton = document.createElement('button');
    prevButton.textContent = '上一个';
    prevButton.style.padding = '5px 10px';
    prevButton.style.cursor = 'pointer';
    prevButton.addEventListener('click', () => this.showPreviousExample());
    buttonContainer.appendChild(prevButton);

    // 创建下一个示例按钮
    const nextButton = document.createElement('button');
    nextButton.textContent = '下一个';
    nextButton.style.padding = '5px 10px';
    nextButton.style.cursor = 'pointer';
    nextButton.addEventListener('click', () => this.showNextExample());
    buttonContainer.appendChild(nextButton);

    // 创建水面效果设置容器
    const settingsContainer = document.createElement('div');
    settingsContainer.style.marginTop = '20px';
    uiContainer.appendChild(settingsContainer);

    // 创建水面效果设置标题
    const settingsTitle = document.createElement('h3');
    settingsTitle.textContent = '水面效果设置';
    settingsTitle.style.margin = '0 0 10px 0';
    settingsContainer.appendChild(settingsTitle);

    // 创建反射开关
    this.createToggle(settingsContainer, '启用反射', this.enhancedWaterSurfaceRenderer.config.enableReflection, (checked) => {
      this.enhancedWaterSurfaceRenderer.setConfig({ enableReflection: checked });
    });

    // 创建折射开关
    this.createToggle(settingsContainer, '启用折射', this.enhancedWaterSurfaceRenderer.config.enableRefraction, (checked) => {
      this.enhancedWaterSurfaceRenderer.setConfig({ enableRefraction: checked });
    });

    // 创建焦散开关
    this.createToggle(settingsContainer, '启用焦散', this.enhancedWaterSurfaceRenderer.config.enableCaustics, (checked) => {
      this.enhancedWaterSurfaceRenderer.setConfig({ enableCaustics: checked });
    });

    // 创建水下雾效开关
    this.createToggle(settingsContainer, '启用水下雾效', this.enhancedWaterSurfaceRenderer.config.enableUnderwaterFog, (checked) => {
      this.enhancedWaterSurfaceRenderer.setConfig({ enableUnderwaterFog: checked });
    });

    // 创建水下扭曲开关
    this.createToggle(settingsContainer, '启用水下扭曲', this.enhancedWaterSurfaceRenderer.config.enableUnderwaterDistortion, (checked) => {
      this.enhancedWaterSurfaceRenderer.setConfig({ enableUnderwaterDistortion: checked });
    });

    // 创建低质量模式开关
    this.createToggle(settingsContainer, '低质量模式', this.enhancedWaterSurfaceRenderer.config.useLowQualityMode, (checked) => {
      this.enhancedWaterSurfaceRenderer.setConfig({ useLowQualityMode: checked });
    });
  }

  /**
   * 创建开关
   * @param container 容器
   * @param label 标签
   * @param initialValue 初始值
   * @param onChange 变化回调
   */
  private createToggle(container: HTMLElement, label: string, initialValue: boolean, onChange: (checked: boolean) => void): void {
    const toggleContainer = document.createElement('div');
    toggleContainer.style.display = 'flex';
    toggleContainer.style.alignItems = 'center';
    toggleContainer.style.marginBottom = '5px';
    container.appendChild(toggleContainer);

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.checked = initialValue;
    checkbox.style.marginRight = '5px';
    checkbox.addEventListener('change', () => onChange(checkbox.checked));
    toggleContainer.appendChild(checkbox);

    const labelElement = document.createElement('label');
    labelElement.textContent = label;
    toggleContainer.appendChild(labelElement);
  }

  /**
   * 注册事件
   */
  private registerEvents(): void {
    // 注册窗口大小变化事件
    window.addEventListener('resize', () => this.onResize());

    // 注册键盘事件
    window.addEventListener('keydown', (event) => this.onKeyDown(event));
  }

  /**
   * 窗口大小变化事件处理
   */
  private onResize(): void {
    // 获取相机
    const camera = this.cameraEntity.getComponent<Camera>('Camera');
    if (camera) {
      // 更新相机宽高比
      camera.setAspect(window.innerWidth / window.innerHeight);
    }

    // 更新渲染器大小
    this.renderingSystem.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 键盘事件处理
   * @param event 键盘事件
   */
  private onKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'ArrowLeft':
        this.showPreviousExample();
        break;
      case 'ArrowRight':
        this.showNextExample();
        break;
    }
  }

  /**
   * 显示上一个示例
   */
  private showPreviousExample(): void {
    this.currentExampleIndex = (this.currentExampleIndex - 1 + this.exampleNames.length) % this.exampleNames.length;
    this.createExample(this.currentExampleIndex);
    this.updateExampleDisplay();
  }

  /**
   * 显示下一个示例
   */
  private showNextExample(): void {
    this.currentExampleIndex = (this.currentExampleIndex + 1) % this.exampleNames.length;
    this.createExample(this.currentExampleIndex);
    this.updateExampleDisplay();
  }

  /**
   * 更新示例显示
   */
  private updateExampleDisplay(): void {
    const exampleDisplay = document.getElementById('example-display');
    if (exampleDisplay) {
      exampleDisplay.textContent = `当前示例：${this.exampleNames[this.currentExampleIndex]}`;
    }
  }

  /**
   * 启动渲染循环
   */
  private startRenderLoop(): void {
    // 定义渲染函数
    const render = () => {
      // 更新相机控制器
      if (this.cameraControls) {
        this.cameraControls.update();
      }

      // 更新世界
      this.world.update();

      // 请求下一帧
      this.animationFrameId = requestAnimationFrame(render);
    };

    // 启动渲染循环
    this.animationFrameId = requestAnimationFrame(render);
  }

  /**
   * 停止渲染循环
   */
  private stopRenderLoop(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = 0;
    }
  }

  /**
   * 启动示例
   */
  public start(): void {
    // 启动世界
    this.world.start();

    // 启动渲染循环
    this.startRenderLoop();
  }

  /**
   * 停止示例
   */
  public stop(): void {
    // 停止渲染循环
    this.stopRenderLoop();

    // 停止世界
    this.world.stop();

    // 清除水体实体
    this.clearWaterEntities();

    // 移除事件监听
    window.removeEventListener('resize', () => this.onResize());
    window.removeEventListener('keydown', (event) => this.onKeyDown(event));
  }
}