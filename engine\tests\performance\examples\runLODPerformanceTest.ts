/**
 * 运行LOD性能测试
 */
import { LODPerformanceTest } from './LODPerformanceTest';
import * as fs from 'fs';
import * as path from 'path';
import { Debug } from '../../../src/utils/Debug';

// 报告输出目录
const REPORTS_DIR = path.join(__dirname, '../reports');

// 确保目录存在
if (!fs.existsSync(REPORTS_DIR)) {
  fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

/**
 * 保存测试报告
 * @param report 测试报告
 * @param filename 文件名
 */
function saveTestReport(report: any, filename: string): void {
  try {
    // 保存JSON报告
    const jsonPath = path.join(REPORTS_DIR, filename);
    fs.writeFileSync(jsonPath, JSON.stringify(report, null, 2));
    Debug.log('LOD性能测试', `测试报告已保存到 ${jsonPath}`);
  } catch (error) {
    Debug.error('LOD性能测试', `保存测试报告失败: ${error}`);
  }
}

/**
 * 生成HTML报告
 * @param reports 测试报告列表
 * @param filename 文件名
 */
function generateHTMLReport(reports: any[], filename: string): void {
  try {
    // 生成HTML报告
    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>LOD性能测试报告</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
          h1, h2, h3 { color: #0066cc; }
          .summary { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .test-result { margin-bottom: 15px; padding: 15px; border-radius: 5px; background-color: #f9f9f9; border: 1px solid #ddd; }
          table { border-collapse: collapse; width: 100%; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          .chart { height: 300px; margin-top: 20px; }
        </style>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
      </head>
      <body>
        <h1>LOD性能测试报告</h1>
        <div class="summary">
          <h2>测试摘要</h2>
          <p>测试时间: ${new Date().toLocaleString()}</p>
          <p>测试数量: ${reports.length}</p>
        </div>
        
        <h2>测试结果</h2>
    `;
    
    // 添加每个测试结果
    for (let i = 0; i < reports.length; i++) {
      const report = reports[i];
      
      html += `
        <div class="test-result">
          <h3>${report.config.name}</h3>
          <p>${report.config.description}</p>
          
          <table>
            <tr>
              <th>配置</th>
              <th>值</th>
            </tr>
            <tr>
              <td>对象数量</td>
              <td>${report.config.objectCount}</td>
            </tr>
            <tr>
              <td>启用LOD</td>
              <td>${report.config.enableLOD ? '是' : '否'}</td>
            </tr>
            <tr>
              <td>使用八叉树</td>
              <td>${report.config.useOctree ? '是' : '否'}</td>
            </tr>
            <tr>
              <td>使用自动LOD生成</td>
              <td>${report.config.useAutoLODGeneration ? '是' : '否'}</td>
            </tr>
            <tr>
              <td>测试持续时间</td>
              <td>${report.config.duration / 1000}秒</td>
            </tr>
            <tr>
              <td>相机移动速度</td>
              <td>${report.config.cameraSpeed}</td>
            </tr>
            <tr>
              <td>相机移动路径</td>
              <td>${report.config.cameraPath}</td>
            </tr>
          </table>
          
          <h4>性能指标</h4>
          <table>
            <tr>
              <th>指标</th>
              <th>值</th>
            </tr>
            <tr>
              <td>平均FPS</td>
              <td>${report.averageFPS.toFixed(2)}</td>
            </tr>
            <tr>
              <td>最低FPS</td>
              <td>${report.minFPS.toFixed(2)}</td>
            </tr>
            <tr>
              <td>最高FPS</td>
              <td>${report.maxFPS.toFixed(2)}</td>
            </tr>
            <tr>
              <td>平均渲染时间</td>
              <td>${report.averageRenderTime.toFixed(2)}ms</td>
            </tr>
            <tr>
              <td>平均CPU使用率</td>
              <td>${report.averageCPUUsage.toFixed(2)}%</td>
            </tr>
            <tr>
              <td>平均内存使用</td>
              <td>${report.averageMemoryUsage.toFixed(2)}MB</td>
            </tr>
            <tr>
              <td>可见对象数量</td>
              <td>${report.visibleObjectCount}</td>
            </tr>
            <tr>
              <td>剔除对象数量</td>
              <td>${report.culledObjectCount}</td>
            </tr>
          </table>
          
          <h4>LOD级别统计</h4>
          <table>
            <tr>
              <th>级别</th>
              <th>数量</th>
              <th>百分比</th>
            </tr>
            <tr>
              <td>高细节</td>
              <td>${report.lodLevelStats.high}</td>
              <td>${(report.lodLevelStats.high / report.visibleObjectCount * 100).toFixed(2)}%</td>
            </tr>
            <tr>
              <td>中细节</td>
              <td>${report.lodLevelStats.medium}</td>
              <td>${(report.lodLevelStats.medium / report.visibleObjectCount * 100).toFixed(2)}%</td>
            </tr>
            <tr>
              <td>低细节</td>
              <td>${report.lodLevelStats.low}</td>
              <td>${(report.lodLevelStats.low / report.visibleObjectCount * 100).toFixed(2)}%</td>
            </tr>
            <tr>
              <td>极低细节</td>
              <td>${report.lodLevelStats.very_low}</td>
              <td>${(report.lodLevelStats.very_low / report.visibleObjectCount * 100).toFixed(2)}%</td>
            </tr>
          </table>
          
          <div class="chart">
            <canvas id="chart-fps-${i}"></canvas>
          </div>
          
          <script>
            new Chart(document.getElementById('chart-fps-${i}'), {
              type: 'bar',
              data: {
                labels: ['平均FPS', '最低FPS', '最高FPS'],
                datasets: [{
                  label: 'FPS',
                  data: [${report.averageFPS}, ${report.minFPS}, ${report.maxFPS}],
                  backgroundColor: [
                    'rgba(75, 192, 192, 0.5)',
                    'rgba(255, 99, 132, 0.5)',
                    'rgba(54, 162, 235, 0.5)'
                  ],
                  borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)'
                  ],
                  borderWidth: 1
                }]
              },
              options: {
                scales: {
                  y: {
                    beginAtZero: true
                  }
                }
              }
            });
          </script>
          
          <div class="chart">
            <canvas id="chart-lod-${i}"></canvas>
          </div>
          
          <script>
            new Chart(document.getElementById('chart-lod-${i}'), {
              type: 'pie',
              data: {
                labels: ['高细节', '中细节', '低细节', '极低细节'],
                datasets: [{
                  label: 'LOD级别统计',
                  data: [
                    ${report.lodLevelStats.high},
                    ${report.lodLevelStats.medium},
                    ${report.lodLevelStats.low},
                    ${report.lodLevelStats.very_low}
                  ],
                  backgroundColor: [
                    'rgba(255, 99, 132, 0.5)',
                    'rgba(54, 162, 235, 0.5)',
                    'rgba(255, 206, 86, 0.5)',
                    'rgba(75, 192, 192, 0.5)'
                  ],
                  borderColor: [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)'
                  ],
                  borderWidth: 1
                }]
              }
            });
          </script>
        </div>
      `;
    }
    
    // 添加比较图表
    if (reports.length > 1) {
      html += `
        <h2>测试比较</h2>
        
        <div class="chart">
          <canvas id="chart-comparison-fps"></canvas>
        </div>
        
        <script>
          new Chart(document.getElementById('chart-comparison-fps'), {
            type: 'bar',
            data: {
              labels: [${reports.map(r => `'${r.config.name}'`).join(', ')}],
              datasets: [{
                label: '平均FPS',
                data: [${reports.map(r => r.averageFPS).join(', ')}],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
              }]
            },
            options: {
              scales: {
                y: {
                  beginAtZero: true
                }
              }
            }
          });
        </script>
        
        <div class="chart">
          <canvas id="chart-comparison-render-time"></canvas>
        </div>
        
        <script>
          new Chart(document.getElementById('chart-comparison-render-time'), {
            type: 'bar',
            data: {
              labels: [${reports.map(r => `'${r.config.name}'`).join(', ')}],
              datasets: [{
                label: '平均渲染时间 (ms)',
                data: [${reports.map(r => r.averageRenderTime).join(', ')}],
                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
              }]
            },
            options: {
              scales: {
                y: {
                  beginAtZero: true
                }
              }
            }
          });
        </script>
      `;
    }
    
    html += `
      </body>
      </html>
    `;
    
    // 保存HTML报告
    const htmlPath = path.join(REPORTS_DIR, filename);
    fs.writeFileSync(htmlPath, html);
    Debug.log('LOD性能测试', `HTML报告已保存到 ${htmlPath}`);
  } catch (error) {
    Debug.error('LOD性能测试', `生成HTML报告失败: ${error}`);
  }
}

/**
 * 运行测试
 */
async function runTests(): Promise<void> {
  const reports: any[] = [];
  
  // 测试1：不启用LOD
  Debug.log('LOD性能测试', '开始测试1：不启用LOD');
  const test1 = new LODPerformanceTest({
    name: '测试1：不启用LOD',
    description: '测试在不启用LOD的情况下的性能',
    objectCount: 1000,
    enableLOD: false,
    useOctree: false,
    useAutoLODGeneration: false,
    duration: 10000,
    cameraSpeed: 0.5,
    cameraPath: 'circle'
  });
  
  await new Promise<void>((resolve) => {
    test1.run((result) => {
      reports.push(result);
      saveTestReport(result, 'lod-test-1.json');
      resolve();
    });
  });
  
  // 测试2：启用LOD，不使用八叉树
  Debug.log('LOD性能测试', '开始测试2：启用LOD，不使用八叉树');
  const test2 = new LODPerformanceTest({
    name: '测试2：启用LOD，不使用八叉树',
    description: '测试在启用LOD但不使用八叉树的情况下的性能',
    objectCount: 1000,
    enableLOD: true,
    useOctree: false,
    useAutoLODGeneration: false,
    duration: 10000,
    cameraSpeed: 0.5,
    cameraPath: 'circle'
  });
  
  await new Promise<void>((resolve) => {
    test2.run((result) => {
      reports.push(result);
      saveTestReport(result, 'lod-test-2.json');
      resolve();
    });
  });
  
  // 测试3：启用LOD，使用八叉树
  Debug.log('LOD性能测试', '开始测试3：启用LOD，使用八叉树');
  const test3 = new LODPerformanceTest({
    name: '测试3：启用LOD，使用八叉树',
    description: '测试在启用LOD并使用八叉树的情况下的性能',
    objectCount: 1000,
    enableLOD: true,
    useOctree: true,
    useAutoLODGeneration: false,
    duration: 10000,
    cameraSpeed: 0.5,
    cameraPath: 'circle'
  });
  
  await new Promise<void>((resolve) => {
    test3.run((result) => {
      reports.push(result);
      saveTestReport(result, 'lod-test-3.json');
      resolve();
    });
  });
  
  // 测试4：启用LOD，使用八叉树，使用自动LOD生成
  Debug.log('LOD性能测试', '开始测试4：启用LOD，使用八叉树，使用自动LOD生成');
  const test4 = new LODPerformanceTest({
    name: '测试4：启用LOD，使用八叉树，使用自动LOD生成',
    description: '测试在启用LOD、使用八叉树并使用自动LOD生成的情况下的性能',
    objectCount: 1000,
    enableLOD: true,
    useOctree: true,
    useAutoLODGeneration: true,
    duration: 10000,
    cameraSpeed: 0.5,
    cameraPath: 'circle'
  });
  
  await new Promise<void>((resolve) => {
    test4.run((result) => {
      reports.push(result);
      saveTestReport(result, 'lod-test-4.json');
      resolve();
    });
  });
  
  // 生成HTML报告
  generateHTMLReport(reports, 'lod-performance-report.html');
  
  Debug.log('LOD性能测试', '所有测试完成');
}

// 运行测试
runTests().catch((error) => {
  Debug.error('LOD性能测试', `运行测试失败: ${error}`);
});
