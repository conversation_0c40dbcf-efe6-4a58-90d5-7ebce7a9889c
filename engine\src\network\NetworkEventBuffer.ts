/**
 * 网络事件缓冲器
 * 负责管理网络事件的缓冲和优先级处理
 */
import { NetworkEvent, NetworkEventType } from './NetworkEvent';
import { Debug } from '../utils/Debug';

/**
 * 事件优先级
 */
export enum EventPriority {
  /** 最高优先级 */
  HIGHEST = 0,
  /** 高优先级 */
  HIGH = 1,
  /** 中等优先级 */
  MEDIUM = 2,
  /** 低优先级 */
  LOW = 3,
  /** 最低优先级 */
  LOWEST = 4,
}

/**
 * 网络事件缓冲器配置
 */
export interface NetworkEventBufferConfig {
  /** 最大缓冲事件数量 */
  maxBufferSize?: number;
  /** 事件处理间隔（毫秒） */
  processInterval?: number;
  /** 是否自动处理事件 */
  autoProcess?: boolean;
  /** 每次处理的最大事件数量 */
  maxEventsPerProcess?: number;
  /** 事件类型优先级映射 */
  eventTypePriorities?: Record<string, EventPriority>;
}

/**
 * 网络事件缓冲器
 * 负责管理网络事件的缓冲和优先级处理
 */
export class NetworkEventBuffer {
  /** 配置 */
  private config: Required<NetworkEventBufferConfig>;
  
  /** 事件缓冲区 */
  private eventBuffer: NetworkEvent[] = [];
  
  /** 处理定时器ID */
  private processTimerId: number | null = null;
  
  /** 事件处理回调 */
  private eventHandler: ((event: NetworkEvent) => void) | null = null;
  
  /** 是否正在处理事件 */
  private isProcessing: boolean = false;
  
  /** 事件类型优先级映射 */
  private eventTypePriorities: Map<string, EventPriority> = new Map();
  
  /**
   * 创建网络事件缓冲器
   * @param config 配置
   */
  constructor(config: NetworkEventBufferConfig = {}) {
    // 默认配置
    this.config = {
      maxBufferSize: 1000,
      processInterval: 16, // 约60fps
      autoProcess: true,
      maxEventsPerProcess: 10,
      eventTypePriorities: {},
      ...config,
    };
    
    // 初始化事件类型优先级映射
    this.initEventTypePriorities();
    
    // 如果启用自动处理，则启动处理定时器
    if (this.config.autoProcess) {
      this.startProcessing();
    }
  }
  
  /**
   * 初始化事件类型优先级映射
   */
  private initEventTypePriorities(): void {
    // 默认优先级
    const defaultPriorities: Record<string, EventPriority> = {
      // 系统事件（最高优先级）
      [NetworkEventType.SYSTEM_ERROR]: EventPriority.HIGHEST,
      [NetworkEventType.SYSTEM_WARNING]: EventPriority.HIGHEST,
      [NetworkEventType.SYSTEM_INFO]: EventPriority.HIGH,
      
      // 连接事件（高优先级）
      [NetworkEventType.CONNECTED]: EventPriority.HIGHEST,
      [NetworkEventType.CONNECTION_FAILED]: EventPriority.HIGHEST,
      [NetworkEventType.DISCONNECTED]: EventPriority.HIGHEST,
      [NetworkEventType.RECONNECTED]: EventPriority.HIGHEST,
      [NetworkEventType.RECONNECTION_FAILED]: EventPriority.HIGHEST,
      [NetworkEventType.CONNECTION_ERROR]: EventPriority.HIGHEST,
      
      // 房间事件（高优先级）
      [NetworkEventType.ROOM_JOINED]: EventPriority.HIGH,
      [NetworkEventType.ROOM_LEFT]: EventPriority.HIGH,
      [NetworkEventType.ROOM_CREATED]: EventPriority.HIGH,
      [NetworkEventType.ROOM_CLOSED]: EventPriority.HIGH,
      [NetworkEventType.ROOM_UPDATED]: EventPriority.MEDIUM,
      
      // 用户事件（中等优先级）
      [NetworkEventType.USER_JOINED]: EventPriority.HIGH,
      [NetworkEventType.USER_LEFT]: EventPriority.HIGH,
      [NetworkEventType.USER_UPDATED]: EventPriority.MEDIUM,
      
      // 实体事件（中等优先级）
      [NetworkEventType.ENTITY_CREATED]: EventPriority.MEDIUM,
      [NetworkEventType.ENTITY_UPDATED]: EventPriority.MEDIUM,
      [NetworkEventType.ENTITY_DELETED]: EventPriority.MEDIUM,
      [NetworkEventType.ENTITY_OWNERSHIP_CHANGED]: EventPriority.MEDIUM,
      
      // 消息事件（低优先级）
      [NetworkEventType.MESSAGE_RECEIVED]: EventPriority.LOW,
      [NetworkEventType.MESSAGE_SENT]: EventPriority.LOW,
      [NetworkEventType.MESSAGE_ACKNOWLEDGED]: EventPriority.LOW,
      [NetworkEventType.MESSAGE_ERROR]: EventPriority.MEDIUM,
      
      // WebRTC事件（中等优先级）
      [NetworkEventType.WEBRTC_CONNECTED]: EventPriority.HIGH,
      [NetworkEventType.WEBRTC_CONNECTION_FAILED]: EventPriority.HIGH,
      [NetworkEventType.WEBRTC_DISCONNECTED]: EventPriority.HIGH,
      [NetworkEventType.WEBRTC_ERROR]: EventPriority.HIGH,
      [NetworkEventType.WEBRTC_STREAM_ADDED]: EventPriority.MEDIUM,
      [NetworkEventType.WEBRTC_STREAM_REMOVED]: EventPriority.MEDIUM,
      
      // 自定义事件（最低优先级）
      [NetworkEventType.CUSTOM]: EventPriority.LOWEST,
    };
    
    // 合并默认优先级和用户配置的优先级
    const mergedPriorities = { ...defaultPriorities, ...this.config.eventTypePriorities };
    
    // 填充优先级映射
    for (const [type, priority] of Object.entries(mergedPriorities)) {
      this.eventTypePriorities.set(type, priority);
    }
  }
  
  /**
   * 启动事件处理
   */
  public startProcessing(): void {
    if (this.processTimerId !== null) {
      return;
    }
    
    this.processTimerId = window.setInterval(() => {
      this.processEvents();
    }, this.config.processInterval);
  }
  
  /**
   * 停止事件处理
   */
  public stopProcessing(): void {
    if (this.processTimerId !== null) {
      clearInterval(this.processTimerId);
      this.processTimerId = null;
    }
  }
  
  /**
   * 设置事件处理回调
   * @param handler 事件处理回调
   */
  public setEventHandler(handler: (event: NetworkEvent) => void): void {
    this.eventHandler = handler;
  }
  
  /**
   * 添加事件到缓冲区
   * @param event 网络事件
   * @returns 是否添加成功
   */
  public addEvent(event: NetworkEvent): boolean {
    // 检查缓冲区是否已满
    if (this.eventBuffer.length >= this.config.maxBufferSize) {
      Debug.warn('NetworkEventBuffer', 'Event buffer is full, dropping event:', event);
      return false;
    }
    
    // 设置事件优先级（如果未设置）
    if (event.priority === undefined) {
      event.priority = this.getEventPriority(event.type);
    }
    
    // 添加到缓冲区
    this.eventBuffer.push(event);
    
    // 按优先级排序
    this.sortEventBuffer();
    
    return true;
  }
  
  /**
   * 处理事件
   */
  public processEvents(): void {
    // 如果正在处理事件或者没有事件处理回调，则跳过
    if (this.isProcessing || !this.eventHandler) {
      return;
    }
    
    this.isProcessing = true;
    
    try {
      // 处理指定数量的事件
      const count = Math.min(this.config.maxEventsPerProcess, this.eventBuffer.length);
      
      for (let i = 0; i < count; i++) {
        const event = this.eventBuffer.shift();
        
        if (event) {
          try {
            // 调用事件处理回调
            this.eventHandler(event);
            
            // 标记事件为已处理
            event.handled = true;
          } catch (error) {
            Debug.error('NetworkEventBuffer', 'Error processing event:', error);
            
            // 标记事件为未处理
            event.handled = false;
            
            // 如果是高优先级事件，则重新添加到缓冲区
            if (event.priority !== undefined && event.priority <= EventPriority.HIGH) {
              this.eventBuffer.unshift(event);
            }
          }
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }
  
  /**
   * 获取事件优先级
   * @param eventType 事件类型
   * @returns 事件优先级
   */
  private getEventPriority(eventType: string): EventPriority {
    return this.eventTypePriorities.get(eventType) || EventPriority.MEDIUM;
  }
  
  /**
   * 按优先级排序事件缓冲区
   */
  private sortEventBuffer(): void {
    this.eventBuffer.sort((a, b) => {
      // 首先按优先级排序（数字越小优先级越高）
      const priorityA = a.priority !== undefined ? a.priority : this.getEventPriority(a.type);
      const priorityB = b.priority !== undefined ? b.priority : this.getEventPriority(b.type);
      
      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }
      
      // 其次按时间戳排序（较早的事件先处理）
      return a.timestamp - b.timestamp;
    });
  }
  
  /**
   * 清空事件缓冲区
   */
  public clearBuffer(): void {
    this.eventBuffer = [];
  }
  
  /**
   * 获取缓冲区中的事件数量
   * @returns 事件数量
   */
  public getBufferSize(): number {
    return this.eventBuffer.length;
  }
  
  /**
   * 设置事件类型的优先级
   * @param eventType 事件类型
   * @param priority 优先级
   */
  public setEventTypePriority(eventType: string, priority: EventPriority): void {
    this.eventTypePriorities.set(eventType, priority);
    
    // 重新排序缓冲区
    this.sortEventBuffer();
  }
  
  /**
   * 获取事件类型的优先级
   * @param eventType 事件类型
   * @returns 优先级
   */
  public getEventTypePriority(eventType: string): EventPriority {
    return this.getEventPriority(eventType);
  }
  
  /**
   * 销毁缓冲器
   */
  public dispose(): void {
    this.stopProcessing();
    this.clearBuffer();
    this.eventHandler = null;
  }
}
