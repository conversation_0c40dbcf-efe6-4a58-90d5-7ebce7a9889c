/**
 * WebSocket连接
 * 负责管理与服务器的WebSocket连接
 */
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkMessage } from './NetworkMessage';
import { Debug } from '../utils/Debug';
import { MessageSerializer } from './MessageSerializer';

/**
 * WebSocket连接状态
 */
export enum WebSocketConnectionState {
  /** 已断开连接 */
  DISCONNECTED = 'disconnected',
  /** 正在连接 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 正在断开连接 */
  DISCONNECTING = 'disconnecting',
  /** 连接错误 */
  ERROR = 'error',
}

/**
 * WebSocket连接
 * 负责管理与服务器的WebSocket连接
 */
export class WebSocketConnection extends EventEmitter {
  /** WebSocket实例 */
  private socket: WebSocket | null = null;
  
  /** 服务器URL */
  private url: string;
  
  /** 连接状态 */
  private state: WebSocketConnectionState = WebSocketConnectionState.DISCONNECTED;
  
  /** 消息序列化器 */
  private messageSerializer: MessageSerializer;
  
  /** 重连尝试次数 */
  private reconnectAttempts: number = 0;
  
  /** 最大重连尝试次数 */
  private maxReconnectAttempts: number = 5;
  
  /** 重连间隔（毫秒） */
  private reconnectInterval: number = 3000;
  
  /** 重连定时器ID */
  private reconnectTimerId: number | null = null;
  
  /** 心跳间隔（毫秒） */
  private heartbeatInterval: number = 30000;
  
  /** 心跳定时器ID */
  private heartbeatTimerId: number | null = null;
  
  /** 最后一次接收消息的时间戳 */
  private lastReceivedTime: number = 0;
  
  /** 消息队列 */
  private messageQueue: NetworkMessage[] = [];
  
  /** 是否启用消息队列 */
  private useMessageQueue: boolean = true;
  
  /** 是否正在处理消息队列 */
  private processingQueue: boolean = false;

  /**
   * 创建WebSocket连接
   * @param url 服务器URL
   * @param useCompression 是否使用压缩
   */
  constructor(url: string, useCompression: boolean = true) {
    super();
    
    this.url = url;
    this.messageSerializer = new MessageSerializer(useCompression);
  }

  /**
   * 连接到服务器
   * @returns Promise
   */
  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.state === WebSocketConnectionState.CONNECTED) {
        resolve();
        return;
      }
      
      if (this.state === WebSocketConnectionState.CONNECTING) {
        reject(new Error('Already connecting to server'));
        return;
      }
      
      this.state = WebSocketConnectionState.CONNECTING;
      
      try {
        this.socket = new WebSocket(this.url);
        
        // 设置二进制类型为ArrayBuffer
        this.socket.binaryType = 'arraybuffer';
        
        // 设置事件监听器
        this.socket.onopen = () => {
          this.state = WebSocketConnectionState.CONNECTED;
          this.reconnectAttempts = 0;
          this.lastReceivedTime = Date.now();
          
          // 启动心跳
          this.startHeartbeat();
          
          // 处理消息队列
          this.processMessageQueue();
          
          this.emit('connected');
          resolve();
        };
        
        this.socket.onclose = (event) => {
          const wasConnected = this.state === WebSocketConnectionState.CONNECTED;
          const wasConnecting = this.state === WebSocketConnectionState.CONNECTING;

          this.state = WebSocketConnectionState.DISCONNECTED;

          // 停止心跳
          this.stopHeartbeat();

          if (wasConnected) {
            this.emit('disconnected', event.code, event.reason);

            // 尝试重连
            this.attemptReconnect();
          } else if (wasConnecting) {
            reject(new Error(`Failed to connect to server: ${event.code} ${event.reason}`));

            // 尝试重连
            this.attemptReconnect();
          }
        };
        
        this.socket.onerror = (event) => {
          const wasConnecting = this.state === WebSocketConnectionState.CONNECTING;

          this.state = WebSocketConnectionState.ERROR;

          const error = new Error('WebSocket error');
          this.emit('error', error);

          if (wasConnecting) {
            reject(error);
          }
        };
        
        this.socket.onmessage = async (event) => {
          this.lastReceivedTime = Date.now();

          try {
            // 反序列化消息
            const message = await this.messageSerializer.deserialize(event.data);

            // 如果是心跳消息，则不触发消息事件
            if (message.type === 'heartbeat') {
              return;
            }

            this.emit('message', message);
          } catch (error) {
            Debug.error('WebSocketConnection', 'Failed to deserialize message:', error);
            this.emit('error', error);
          }
        };
      } catch (error) {
        this.state = WebSocketConnectionState.ERROR;
        this.emit('error', error);
        reject(error);
      }
    });
  }

  /**
   * 断开连接
   * @returns Promise
   */
  public disconnect(): Promise<void> {
    return new Promise((resolve) => {
      if (this.state === WebSocketConnectionState.DISCONNECTED) {
        resolve();
        return;
      }
      
      this.state = WebSocketConnectionState.DISCONNECTING;
      
      // 停止心跳
      this.stopHeartbeat();
      
      // 停止重连
      this.stopReconnect();
      
      if (this.socket) {
        // 设置关闭事件监听器
        const onClose = () => {
          this.socket!.removeEventListener('close', onClose);
          this.state = WebSocketConnectionState.DISCONNECTED;
          this.emit('disconnected', 1000, 'Normal closure');
          resolve();
        };
        
        this.socket.addEventListener('close', onClose);
        
        // 关闭WebSocket连接
        this.socket.close(1000, 'Normal closure');
      } else {
        this.state = WebSocketConnectionState.DISCONNECTED;
        resolve();
      }
    });
  }

  /**
   * 发送消息
   * @param type 消息类型
   * @param message 消息对象
   */
  public async send(type: string, message: NetworkMessage): Promise<void> {
    if (this.state !== WebSocketConnectionState.CONNECTED) {
      if (this.useMessageQueue) {
        // 添加到消息队列
        this.messageQueue.push(message);
      }
      return;
    }

    try {
      // 序列化消息
      const data = await this.messageSerializer.serialize(message);

      // 发送消息
      if (typeof data === 'string') {
        this.socket!.send(data);
      } else {
        this.socket!.send(data);
      }
    } catch (error) {
      Debug.error('WebSocketConnection', 'Failed to send message:', error);
      this.emit('error', error);

      if (this.useMessageQueue) {
        // 添加到消息队列
        this.messageQueue.push(message);
      }
    }
  }

  /**
   * 获取连接状态
   * @returns 连接状态
   */
  public getState(): WebSocketConnectionState {
    return this.state;
  }

  /**
   * 是否已连接
   * @returns 是否已连接
   */
  public isConnected(): boolean {
    return this.state === WebSocketConnectionState.CONNECTED;
  }

  /**
   * 设置重连参数
   * @param maxAttempts 最大重连尝试次数
   * @param interval 重连间隔（毫秒）
   */
  public setReconnectParams(maxAttempts: number, interval: number): void {
    this.maxReconnectAttempts = maxAttempts;
    this.reconnectInterval = interval;
  }

  /**
   * 设置心跳间隔
   * @param interval 心跳间隔（毫秒）
   */
  public setHeartbeatInterval(interval: number): void {
    this.heartbeatInterval = interval;
    
    // 如果已连接，则重新启动心跳
    if (this.state === WebSocketConnectionState.CONNECTED) {
      this.stopHeartbeat();
      this.startHeartbeat();
    }
  }

  /**
   * 设置是否使用消息队列
   * @param useQueue 是否使用消息队列
   */
  public setUseMessageQueue(useQueue: boolean): void {
    this.useMessageQueue = useQueue;
  }

  /**
   * 清空消息队列
   */
  public clearMessageQueue(): void {
    this.messageQueue = [];
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      Debug.error('WebSocketConnection', 'Max reconnect attempts reached');
      return;
    }
    
    this.reconnectAttempts++;
    
    Debug.log('WebSocketConnection', `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    // 设置重连定时器
    this.reconnectTimerId = window.setTimeout(() => {
      this.connect().catch((error) => {
        Debug.error('WebSocketConnection', 'Reconnect failed:', error);
      });
    }, this.reconnectInterval);
  }

  /**
   * 停止重连
   */
  private stopReconnect(): void {
    if (this.reconnectTimerId !== null) {
      clearTimeout(this.reconnectTimerId);
      this.reconnectTimerId = null;
    }
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (this.heartbeatTimerId !== null) {
      return;
    }
    
    this.heartbeatTimerId = window.setInterval(() => {
      this.sendHeartbeat();
    }, this.heartbeatInterval);
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimerId !== null) {
      clearInterval(this.heartbeatTimerId);
      this.heartbeatTimerId = null;
    }
  }

  /**
   * 发送心跳消息
   */
  private async sendHeartbeat(): Promise<void> {
    if (this.state !== WebSocketConnectionState.CONNECTED) {
      return;
    }

    const message: NetworkMessage = {
      type: 'heartbeat',
      data: {
        timestamp: Date.now(),
      },
      timestamp: Date.now(),
    };

    try {
      // 序列化消息
      const data = await this.messageSerializer.serialize(message);

      // 发送消息
      if (typeof data === 'string') {
        this.socket!.send(data);
      } else {
        this.socket!.send(data);
      }
    } catch (error) {
      Debug.error('WebSocketConnection', 'Failed to send heartbeat:', error);
    }
  }

  /**
   * 处理消息队列
   */
  private async processMessageQueue(): Promise<void> {
    if (!this.useMessageQueue || this.processingQueue || this.messageQueue.length === 0) {
      return;
    }

    this.processingQueue = true;

    // 复制消息队列
    const queue = [...this.messageQueue];
    this.messageQueue = [];

    // 发送队列中的消息
    for (const message of queue) {
      await this.send(message.type, message);
    }

    this.processingQueue = false;
  }
}
