/**
 * 地形分块系统
 * 负责地形的分块管理和渲染优化
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { TerrainComponent } from './components/TerrainComponent';
/**
 * 地形块接口
 */
export interface TerrainChunk {
    /** 块ID */
    id: string;
    /** 块索引X */
    indexX: number;
    /** 块索引Z */
    indexZ: number;
    /** 块大小 */
    size: number;
    /** 块分辨率 */
    resolution: number;
    /** 块中心位置 */
    center: THREE.Vector3;
    /** 块包围盒 */
    boundingBox: THREE.Box3;
    /** 块包围球 */
    boundingSphere: THREE.Sphere;
    /** 块LOD级别 */
    lodLevel: number;
    /** 块网格 */
    mesh: THREE.Mesh | null;
    /** 块几何体 */
    geometry: THREE.BufferGeometry | null;
    /** 块材质 */
    material: THREE.Material | null;
    /** 块高度数据 */
    heightData: Float32Array;
    /** 块法线数据 */
    normalData: Float32Array;
    /** 是否可见 */
    visible: boolean;
    /** 是否已加载 */
    loaded: boolean;
    /** 是否需要更新 */
    needsUpdate: boolean;
}
/**
 * 地形分块系统事件类型
 */
export declare enum TerrainChunkSystemEventType {
    /** 块创建 */
    CHUNK_CREATED = "chunk_created",
    /** 块销毁 */
    CHUNK_DESTROYED = "chunk_destroyed",
    /** 块可见性变更 */
    CHUNK_VISIBILITY_CHANGED = "chunk_visibility_changed",
    /** 块LOD级别变更 */
    CHUNK_LOD_CHANGED = "chunk_lod_changed",
    /** 块更新 */
    CHUNK_UPDATED = "chunk_updated"
}
/**
 * 地形分块系统配置接口
 */
export interface TerrainChunkSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 块大小 */
    chunkSize?: number;
    /** 块分辨率 */
    chunkResolution?: number;
    /** 是否使用八叉树 */
    useOctree?: boolean;
    /** 是否使用视锥体剔除 */
    useFrustumCulling?: boolean;
    /** 是否使用LOD */
    useLOD?: boolean;
    /** LOD距离 */
    lodDistances?: number[];
    /** 是否使用GPU实例化 */
    useGPUInstancing?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 地形分块系统类
 */
export declare class TerrainChunkSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 块大小 */
    private chunkSize;
    /** 块分辨率 */
    private chunkResolution;
    /** 是否使用八叉树 */
    private useOctree;
    /** 是否使用视锥体剔除 */
    private useFrustumCulling;
    /** 是否使用LOD */
    private useLOD;
    /** LOD距离 */
    private lodDistances;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 地形实体映射 */
    private terrainEntities;
    /** 地形块映射 */
    private terrainChunks;
    /** 实体到块映射 */
    private entityToChunks;
    /** 八叉树 */
    private octree;
    /** 视锥体 */
    private frustum;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 调试网格 */
    private debugMeshes;
    /** 块计数器 */
    private chunkCounter;
    /**
     * 创建地形分块系统
     * @param options 配置选项
     */
    constructor(options?: TerrainChunkSystemOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化八叉树
     */
    private initializeOctree;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 添加地形实体
     * @param entity 实体
     * @param component 地形组件
     */
    addTerrainEntity(entity: Entity, component: TerrainComponent): void;
    /**
     * 移除地形实体
     * @param entity 实体
     */
    removeTerrainEntity(entity: Entity): void;
    /**
     * 为地形创建块
     * @param entity 实体
     * @param component 地形组件
     */
    private createChunksForTerrain;
    /**
     * 创建地形块
     * @param entity 实体
     * @param component 地形组件
     * @param indexX X索引
     * @param indexZ Z索引
     * @returns 块ID
     */
    private createTerrainChunk;
    /**
     * 从地形组件中提取高度数据
     * @param component 地形组件
     * @param indexX X索引
     * @param indexZ Z索引
     * @param heightData 高度数据
     */
    private extractHeightData;
    /**
     * 初始化块
     * @param _entity 实体（未使用）
     * @param component 地形组件
     * @param chunk 地形块
     */
    private initializeChunk;
    /**
     * 创建块几何体
     * @param chunk 地形块
     * @param maxHeight 最大高度
     * @returns 几何体
     */
    private createChunkGeometry;
    /**
     * 创建块材质
     * @param component 地形组件
     * @param _chunk 地形块（未使用）
     * @returns 材质
     */
    private createChunkMaterial;
    /**
     * 销毁块
     * @param chunkId 块ID
     */
    private destroyChunk;
    /**
     * 更新块
     * @param chunk 地形块
     */
    updateChunk(chunk: TerrainChunk): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 更新视锥体
     * @param camera 相机
     */
    private updateFrustum;
    /**
     * 更新所有块
     * @param camera 相机
     */
    private updateChunks;
    /**
     * 使用八叉树更新块
     * @param camera 相机
     */
    private updateChunksWithOctree;
    /**
     * 使用暴力方法更新块
     * @param camera 相机
     */
    private updateChunksWithBruteForce;
    /**
     * 设置块可见性
     * @param chunk 地形块
     * @param visible 是否可见
     */
    private setChunkVisible;
    /**
     * 更新块的LOD级别
     * @param chunk 地形块
     * @param distance 距离
     */
    private updateChunkLOD;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 获取场景
     * @returns 场景
     */
    private getScene;
}
