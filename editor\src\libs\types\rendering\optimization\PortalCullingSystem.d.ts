/**
 * 门户剔除系统
 * 用于室内场景的高效剔除，通过门户（如门、窗口等）连接不同的房间
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
/**
 * 门户剔除系统配置接口
 */
export interface PortalCullingSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 是否使用保守剔除 */
    useConservativeCulling?: boolean;
    /** 是否使用时间一致性 */
    useTemporalCoherence?: boolean;
    /** 是否使用门户合并 */
    usePortalMerging?: boolean;
    /** 是否使用门户缓存 */
    usePortalCache?: boolean;
    /** 门户缓存更新间隔（帧） */
    portalCacheUpdateInterval?: number;
    /** 是否使用抗锯齿 */
    useAntiAliasing?: boolean;
}
/**
 * 门户接口
 */
export interface Portal {
    /** 门户ID */
    id: string;
    /** 门户几何体 */
    geometry: THREE.BufferGeometry;
    /** 门户位置 */
    position: THREE.Vector3;
    /** 门户旋转 */
    rotation: THREE.Euler;
    /** 门户缩放 */
    scale: THREE.Vector3;
    /** 门户矩阵 */
    matrix: THREE.Matrix4;
    /** 门户法线 */
    normal: THREE.Vector3;
    /** 门户中心 */
    center: THREE.Vector3;
    /** 门户半宽 */
    halfWidth: number;
    /** 门户半高 */
    halfHeight: number;
    /** 门户连接的房间A */
    roomA: Room;
    /** 门户连接的房间B */
    roomB: Room;
    /** 门户是否可见 */
    visible: boolean;
    /** 门户是否双向 */
    bidirectional: boolean;
    /** 门户是否开启 */
    open: boolean;
    /** 门户开启程度（0-1） */
    openness: number;
    /** 门户视锥体 */
    frustum?: THREE.Frustum;
    /** 门户平面 */
    plane?: THREE.Plane;
}
/**
 * 房间接口
 */
export interface Room {
    /** 房间ID */
    id: string;
    /** 房间几何体 */
    geometry: THREE.BufferGeometry;
    /** 房间位置 */
    position: THREE.Vector3;
    /** 房间旋转 */
    rotation: THREE.Euler;
    /** 房间缩放 */
    scale: THREE.Vector3;
    /** 房间矩阵 */
    matrix: THREE.Matrix4;
    /** 房间包围盒 */
    boundingBox: THREE.Box3;
    /** 房间包围球 */
    boundingSphere: THREE.Sphere;
    /** 房间门户列表 */
    portals: Portal[];
    /** 房间实体列表 */
    entities: Entity[];
    /** 房间是否可见 */
    visible: boolean;
    /** 房间是否已访问 */
    visited: boolean;
    /** 房间深度（从相机所在房间开始） */
    depth: number;
}
/**
 * 门户剔除结果接口
 */
export interface PortalCullingResult {
    /** 可见房间数量 */
    visibleRoomCount: number;
    /** 不可见房间数量 */
    invisibleRoomCount: number;
    /** 可见门户数量 */
    visiblePortalCount: number;
    /** 不可见门户数量 */
    invisiblePortalCount: number;
    /** 可见实体数量 */
    visibleEntityCount: number;
    /** 不可见实体数量 */
    invisibleEntityCount: number;
    /** 总房间数量 */
    totalRoomCount: number;
    /** 总门户数量 */
    totalPortalCount: number;
    /** 总实体数量 */
    totalEntityCount: number;
    /** 房间剔除率 */
    roomCullingRate: number;
    /** 门户剔除率 */
    portalCullingRate: number;
    /** 实体剔除率 */
    entityCullingRate: number;
    /** 剔除时间（毫秒） */
    cullingTime: number;
}
/**
 * 门户剔除系统类
 */
export declare class PortalCullingSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 是否使用保守剔除 */
    protected useConservativeCulling: boolean;
    /** 是否使用时间一致性 */
    private useTemporalCoherence;
    /** 是否使用门户合并 */
    protected usePortalMerging: boolean;
    /** 是否使用门户缓存 */
    private usePortalCache;
    /** 门户缓存更新间隔（帧） */
    private portalCacheUpdateInterval;
    /** 是否使用抗锯齿 */
    protected useAntiAliasing: boolean;
    /** 当前帧计数 */
    private frameCount;
    /** 房间列表 */
    private rooms;
    /** 门户列表 */
    private portals;
    /** 实体到房间的映射 */
    private entityToRoom;
    /** 相机所在房间 */
    private cameraRoom;
    /** 上一帧相机所在房间 */
    private previousCameraRoom;
    /** 可见房间列表 */
    private visibleRooms;
    /** 可见门户列表 */
    private visiblePortals;
    /** 门户视锥体缓存 */
    private portalFrustumCache;
    /** 调试可视化材质 */
    private debugMaterial;
    /** 调试可视化网格 */
    private debugMeshes;
    /** 剔除时间 */
    private cullingTime;
    /**
     * 创建门户剔除系统
     * @param options 门户剔除系统配置
     */
    constructor(options?: PortalCullingSystemOptions);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 获取场景
     * @returns 场景
     */
    private getScene;
    /**
     * 更新相机所在房间
     * @param camera 相机
     */
    private updateCameraRoom;
    /**
     * 检查点是否在房间内
     * @param point 点
     * @param room 房间
     * @returns 是否在房间内
     */
    private isPointInRoom;
    /**
     * 使用门户进行剔除
     * @param camera 相机
     * @param _scene 场景
     */
    private cullWithPortals;
    /**
     * 递归遍历门户
     * @param camera 相机
     * @param parentFrustum 父视锥体
     * @param currentRoom 当前房间
     * @param depth 深度
     */
    private traversePortals;
    /**
     * 检查门户是否在视锥体内
     * @param portal 门户
     * @param frustum 视锥体
     * @returns 是否在视锥体内
     */
    private isPortalInFrustum;
    /**
     * 计算门户视锥体
     * @param _camera 相机
     * @param portal 门户
     * @returns 门户视锥体
     */
    private computePortalFrustum;
    /**
     * 更新实体可见性
     */
    private updateEntityVisibility;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 添加房间
     * @param room 房间
     */
    addRoom(room: Room): void;
    /**
     * 移除房间
     * @param roomId 房间ID
     */
    removeRoom(roomId: string): void;
    /**
     * 添加门户
     * @param portal 门户
     */
    addPortal(portal: Portal): void;
    /**
     * 移除门户
     * @param portalId 门户ID
     */
    removePortal(portalId: string): void;
    /**
     * 添加实体到房间
     * @param entity 实体
     * @param roomId 房间ID
     */
    addEntityToRoom(entity: Entity, roomId: string): void;
    /**
     * 从房间中移除实体
     * @param entity 实体
     */
    removeEntityFromRoom(entity: Entity): void;
    /**
     * 设置门户开启状态
     * @param portalId 门户ID
     * @param open 是否开启
     * @param openness 开启程度（0-1）
     */
    setPortalOpen(portalId: string, open: boolean, openness?: number): void;
    /**
     * 获取门户剔除结果
     * @returns 门户剔除结果
     */
    getPortalCullingResult(): PortalCullingResult;
    /**
     * 清除所有房间和门户
     */
    clear(): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
