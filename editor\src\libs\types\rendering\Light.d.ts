/**
 * 光源组件
 * 表示场景中的各种光源
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
/**
 * 光源类型枚举
 */
export declare enum LightType {
    AMBIENT = "ambient",
    DIRECTIONAL = "directional",
    POINT = "point",
    SPOT = "spot",
    HEMISPHERE = "hemisphere",
    RECT_AREA = "rectArea"
}
/**
 * 光源基础选项接口
 */
export interface LightOptions {
    /** 光源类型 */
    type: string;
    /** 光源颜色 */
    color?: THREE.ColorRepresentation;
    /** 光源强度 */
    intensity?: number;
    /** 是否投射阴影 */
    castShadow?: boolean;
}
/**
 * 环境光选项接口
 */
export interface AmbientLightOptions extends LightOptions {
    type: 'ambient';
}
/**
 * 方向光选项接口
 */
export interface DirectionalLightOptions extends LightOptions {
    type: 'directional';
    /** 阴影贴图大小 */
    shadowMapSize?: number;
    /** 阴影相机近平面 */
    shadowCameraNear?: number;
    /** 阴影相机远平面 */
    shadowCameraFar?: number;
    /** 阴影相机左平面 */
    shadowCameraLeft?: number;
    /** 阴影相机右平面 */
    shadowCameraRight?: number;
    /** 阴影相机上平面 */
    shadowCameraTop?: number;
    /** 阴影相机下平面 */
    shadowCameraBottom?: number;
    /** 阴影偏移 */
    shadowBias?: number;
    /** 阴影半径 */
    shadowRadius?: number;
}
/**
 * 点光源选项接口
 */
export interface PointLightOptions extends LightOptions {
    type: 'point';
    /** 光源距离 */
    distance?: number;
    /** 光源衰减 */
    decay?: number;
    /** 阴影贴图大小 */
    shadowMapSize?: number;
    /** 阴影相机近平面 */
    shadowCameraNear?: number;
    /** 阴影相机远平面 */
    shadowCameraFar?: number;
    /** 阴影偏移 */
    shadowBias?: number;
    /** 阴影半径 */
    shadowRadius?: number;
}
/**
 * 聚光灯选项接口
 */
export interface SpotLightOptions extends LightOptions {
    type: 'spot';
    /** 光源距离 */
    distance?: number;
    /** 光源角度 */
    angle?: number;
    /** 光源半影 */
    penumbra?: number;
    /** 光源衰减 */
    decay?: number;
    /** 阴影贴图大小 */
    shadowMapSize?: number;
    /** 阴影相机近平面 */
    shadowCameraNear?: number;
    /** 阴影相机远平面 */
    shadowCameraFar?: number;
    /** 阴影偏移 */
    shadowBias?: number;
    /** 阴影半径 */
    shadowRadius?: number;
}
/**
 * 半球光选项接口
 */
export interface HemisphereLightOptions extends LightOptions {
    type: 'hemisphere';
    /** 地面颜色 */
    groundColor?: THREE.ColorRepresentation;
}
/**
 * 矩形区域光选项接口
 */
export interface RectAreaLightOptions extends LightOptions {
    type: 'rectArea';
    /** 宽度 */
    width?: number;
    /** 高度 */
    height?: number;
}
/**
 * 光源组件类
 */
export declare class Light extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 光源类型 */
    private lightType;
    /** Three.js光源 */
    private light;
    /**
     * 创建光源组件
     * @param options 光源选项
     */
    constructor(options: LightOptions);
    /**
     * 创建环境光
     * @param options 环境光选项
     * @returns Three.js环境光
     */
    private createAmbientLight;
    /**
     * 创建方向光
     * @param options 方向光选项
     * @returns Three.js方向光
     */
    private createDirectionalLight;
    /**
     * 创建点光源
     * @param options 点光源选项
     * @returns Three.js点光源
     */
    private createPointLight;
    /**
     * 创建聚光灯
     * @param options 聚光灯选项
     * @returns Three.js聚光灯
     */
    private createSpotLight;
    /**
     * 创建半球光
     * @param options 半球光选项
     * @returns Three.js半球光
     */
    private createHemisphereLight;
    /**
     * 创建矩形区域光
     * @param options 矩形区域光选项
     * @returns Three.js矩形区域光
     */
    private createRectAreaLight;
    /**
     * 当组件附加到实体时调用
     */
    protected onAttach(): void;
    /**
     * 当组件从实体分离时调用
     */
    protected onDetach(): void;
    /**
     * 获取光源类型
     * @returns 光源类型
     */
    getType(): LightType;
    /**
     * 获取Three.js光源
     * @returns Three.js光源
     */
    getThreeLight(): THREE.Light;
    /**
     * 设置光源颜色
     * @param color 颜色
     */
    setColor(color: THREE.ColorRepresentation): void;
    /**
     * 设置光源强度
     * @param intensity 强度
     */
    setIntensity(intensity: number): void;
    /**
     * 设置是否投射阴影
     * @param castShadow 是否投射阴影
     */
    setCastShadow(castShadow: boolean): void;
}
