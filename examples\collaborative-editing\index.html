<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>协作编辑最佳实践 - DL（Digital Learning）引擎示例项目</title>
  <link rel="stylesheet" href="styles/main.css">
  <script type="module" src="scripts/main.js" defer></script>
</head>
<body>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="logo">
        <img src="../assets/images/logo.png" alt="DL（Digital Learning）引擎">
        <h1>协作编辑最佳实践</h1>
      </div>
      <div class="user-info">
        <div class="current-user">
          <img src="assets/images/avatar-default.png" alt="用户头像" id="current-user-avatar">
          <span id="current-user-name">未登录</span>
        </div>
        <button id="login-button" class="button">登录</button>
        <button id="logout-button" class="button" style="display: none;">退出</button>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="app-main">
      <!-- 左侧面板 -->
      <aside class="left-panel">
        <div class="panel-header">
          <h2>场景层级</h2>
        </div>
        <div class="panel-content">
          <div class="scene-hierarchy" id="scene-hierarchy">
            <!-- 场景层级结构将通过JavaScript动态生成 -->
          </div>
        </div>
      </aside>

      <!-- 中央视口 -->
      <section class="viewport-container">
        <div class="viewport-header">
          <div class="viewport-tools">
            <button class="tool-button" data-tool="select" title="选择工具">
              <img src="assets/images/icons/select.svg" alt="选择">
            </button>
            <button class="tool-button" data-tool="move" title="移动工具">
              <img src="assets/images/icons/move.svg" alt="移动">
            </button>
            <button class="tool-button" data-tool="rotate" title="旋转工具">
              <img src="assets/images/icons/rotate.svg" alt="旋转">
            </button>
            <button class="tool-button" data-tool="scale" title="缩放工具">
              <img src="assets/images/icons/scale.svg" alt="缩放">
            </button>
          </div>
          <div class="viewport-actions">
            <button class="action-button" id="add-object-button" title="添加对象">
              <img src="assets/images/icons/add.svg" alt="添加">
            </button>
            <button class="action-button" id="delete-object-button" title="删除对象">
              <img src="assets/images/icons/delete.svg" alt="删除">
            </button>
          </div>
        </div>
        <div class="viewport" id="viewport">
          <!-- 3D视口将通过JavaScript渲染 -->
          <canvas id="viewport-canvas"></canvas>
          <!-- 用户光标指示器将动态添加 -->
        </div>
        <div class="viewport-footer">
          <div class="viewport-stats">
            <span id="fps-counter">FPS: 60</span>
            <span id="object-counter">对象: 0</span>
          </div>
          <div class="viewport-status">
            <span id="connection-status">未连接</span>
          </div>
        </div>
      </section>

      <!-- 右侧面板 -->
      <aside class="right-panel">
        <div class="panel-tabs">
          <button class="tab-button active" data-tab="properties">属性</button>
          <button class="tab-button" data-tab="users">用户</button>
          <button class="tab-button" data-tab="chat">聊天</button>
          <button class="tab-button" data-tab="history">历史</button>
        </div>
        
        <!-- 属性面板 -->
        <div class="tab-content active" id="properties-tab">
          <div class="panel-header">
            <h2>属性</h2>
          </div>
          <div class="panel-content">
            <div class="properties-panel" id="properties-panel">
              <!-- 属性编辑器将通过JavaScript动态生成 -->
              <div class="no-selection-message">未选择对象</div>
            </div>
          </div>
        </div>
        
        <!-- 用户面板 -->
        <div class="tab-content" id="users-tab">
          <div class="panel-header">
            <h2>在线用户</h2>
          </div>
          <div class="panel-content">
            <div class="users-list" id="users-list">
              <!-- 用户列表将通过JavaScript动态生成 -->
            </div>
            <div class="permissions-panel" id="permissions-panel">
              <h3>权限管理</h3>
              <div class="permissions-content">
                <!-- 权限管理界面将通过JavaScript动态生成 -->
              </div>
            </div>
          </div>
        </div>
        
        <!-- 聊天面板 -->
        <div class="tab-content" id="chat-tab">
          <div class="panel-header">
            <h2>聊天</h2>
          </div>
          <div class="panel-content">
            <div class="chat-messages" id="chat-messages">
              <!-- 聊天消息将通过JavaScript动态生成 -->
            </div>
            <div class="chat-input">
              <input type="text" id="chat-input-field" placeholder="输入消息...">
              <button id="chat-send-button">发送</button>
            </div>
          </div>
        </div>
        
        <!-- 历史面板 -->
        <div class="tab-content" id="history-tab">
          <div class="panel-header">
            <h2>编辑历史</h2>
          </div>
          <div class="panel-content">
            <div class="history-list" id="history-list">
              <!-- 历史记录将通过JavaScript动态生成 -->
            </div>
            <div class="history-actions">
              <button id="undo-button" class="button">撤销</button>
              <button id="redo-button" class="button">重做</button>
            </div>
          </div>
        </div>
      </aside>
    </main>

    <!-- 底部状态栏 -->
    <footer class="app-footer">
      <div class="status-info">
        <span id="project-name">示例项目</span>
        <span id="save-status">已保存</span>
      </div>
      <div class="collaboration-info">
        <span id="collaboration-status">协作模式: 已启用</span>
        <span id="online-users-count">在线用户: 1</span>
      </div>
    </footer>
  </div>

  <!-- 对话框 -->
  <div class="dialog" id="login-dialog">
    <div class="dialog-content">
      <div class="dialog-header">
        <h2>登录</h2>
        <button class="close-button" id="login-dialog-close">&times;</button>
      </div>
      <div class="dialog-body">
        <form id="login-form">
          <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" id="username" name="username" required>
          </div>
          <div class="form-group">
            <label for="password">密码</label>
            <input type="password" id="password" name="password" required>
          </div>
          <div class="form-actions">
            <button type="submit" class="button primary">登录</button>
            <button type="button" class="button" id="login-as-guest">以访客身份登录</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div class="dialog" id="conflict-dialog">
    <div class="dialog-content">
      <div class="dialog-header">
        <h2>检测到编辑冲突</h2>
        <button class="close-button" id="conflict-dialog-close">&times;</button>
      </div>
      <div class="dialog-body">
        <p>检测到与其他用户的编辑冲突。请选择如何解决：</p>
        <div class="conflict-options">
          <div class="conflict-option">
            <h3>保留我的更改</h3>
            <div class="conflict-preview" id="local-change-preview">
              <!-- 本地更改预览 -->
            </div>
            <button class="button" id="keep-local">保留我的更改</button>
          </div>
          <div class="conflict-option">
            <h3>采用他人的更改</h3>
            <div class="conflict-preview" id="remote-change-preview">
              <!-- 远程更改预览 -->
            </div>
            <button class="button" id="keep-remote">采用他人的更改</button>
          </div>
        </div>
        <button class="button primary" id="merge-changes">手动合并更改</button>
      </div>
    </div>
  </div>

  <!-- 加载脚本 -->
  <script src="../engine/dist/index.js"></script>
</body>
</html>
