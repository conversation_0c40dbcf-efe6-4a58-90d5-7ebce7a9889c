import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

/**
 * 应用主控制器
 * 提供基本的健康检查和服务信息接口
 */
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  /**
   * 获取服务基本信息
   */
  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  /**
   * 健康检查接口
   */
  @Get('health')
  getHealth(): object {
    return this.appService.getHealth();
  }

  /**
   * 获取服务版本信息
   */
  @Get('version')
  getVersion(): object {
    return this.appService.getVersion();
  }
}
