/**
 * 音频相关的可视化脚本节点
 */
import { VisualScriptNode } from '../VisualScriptNode';
/**
 * 播放音频节点
 */
export declare class PlayAudioNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 停止音频节点
 */
export declare class StopAudioNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 设置音量节点
 */
export declare class SetVolumeNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 音频分析节点
 */
export declare class AudioAnalyzerNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 3D音频节点
 */
export declare class Audio3DNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 注册音频节点
 */
export declare function registerAudioNodes(): void;
