/**
 * 视觉脚本流程节点
 * 流程节点用于控制执行流程
 */
import { Node, NodeCategory, NodeOptions, NodeType } from './Node';
/**
 * 流程节点选项
 */
export interface FlowNodeOptions extends NodeOptions {
    /** 输入流程插槽名称 */
    inputFlowName?: string;
    /** 输出流程插槽名称列表 */
    outputFlowNames?: string[];
}
/**
 * 流程节点基类
 */
export declare class FlowNode extends Node {
    /** 节点类型 */
    readonly nodeType: NodeType;
    /** 节点类别 */
    readonly category: NodeCategory;
    /** 输入流程插槽名称 */
    protected inputFlowName: string;
    /** 输出流程插槽名称列表 */
    protected outputFlowNames: string[];
    /**
     * 创建流程节点
     * @param options 节点选项
     */
    constructor(options: FlowNodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
    /**
     * 处理输入并确定输出流程
     * @param inputs 输入值
     * @returns 输出流程名称
     */
    protected process(inputs: Record<string, any>): string | null;
}
