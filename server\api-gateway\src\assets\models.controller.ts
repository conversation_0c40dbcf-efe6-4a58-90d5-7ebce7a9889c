/**
 * 模型控制器
 */
import { Controller, Get, UseGuards, Request, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AssetsService } from './assets.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('模型')
@Controller('models')
export class ModelsController {
  constructor(private readonly assetsService: AssetsService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有模型' })
  @ApiResponse({ status: 200, description: '返回所有模型' })
  async findAll(
    @Request() req,
    @Query('projectId') projectId?: string,
    @Query('tags') tags?: string,
  ) {
    const tagArray = tags ? tags.split(',') : undefined;
    return this.assetsService.findAllModels(req.user.id, projectId, tagArray);
  }

  @Get('search')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索模型' })
  @ApiResponse({ status: 200, description: '返回搜索结果' })
  async search(@Request() req, @Query('query') query: string) {
    return this.assetsService.searchModels(req.user.id, query);
  }
}
