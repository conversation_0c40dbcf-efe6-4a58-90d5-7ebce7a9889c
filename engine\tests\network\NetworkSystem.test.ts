/**
 * NetworkSystem类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NetworkSystem } from '../../src/network/NetworkSystem';
import { NetworkManager } from '../../src/network/NetworkManager';
import { EntitySyncManager } from '../../src/network/EntitySyncManager';
import { NetworkEventDispatcher } from '../../src/network/NetworkEventDispatcher';
import { NetworkEntityComponent } from '../../src/network/components/NetworkEntityComponent';
import { Entity } from '../../src/core/Entity';
import { Transform } from '../../src/scene/Transform';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { WebSocketConnection } from '../../src/network/WebSocketConnection';
import { WebRTCConnection } from '../../src/network/WebRTCConnection';
import { NetworkState } from '../../src/network/types';

// 模拟WebSocket
class MockWebSocket {
  public onopen: Function | null = null;
  public onclose: Function | null = null;
  public onmessage: Function | null = null;
  public onerror: Function | null = null;
  public readyState: number = 0;
  
  constructor(public url: string) {
    setTimeout(() => {
      this.readyState = 1;
      if (this.onopen) this.onopen({ target: this });
    }, 10);
  }
  
  public send(data: string): void {
    // 模拟发送数据
  }
  
  public close(): void {
    this.readyState = 3;
    if (this.onclose) this.onclose({ target: this });
  }
}

// 模拟RTCPeerConnection
class MockRTCPeerConnection {
  public onicecandidate: Function | null = null;
  public oniceconnectionstatechange: Function | null = null;
  public ondatachannel: Function | null = null;
  public iceConnectionState: string = 'new';
  
  constructor() {
    setTimeout(() => {
      this.iceConnectionState = 'connected';
      if (this.oniceconnectionstatechange) this.oniceconnectionstatechange();
    }, 10);
  }
  
  public createDataChannel(label: string): MockRTCDataChannel {
    return new MockRTCDataChannel(label);
  }
  
  public createOffer(): Promise<any> {
    return Promise.resolve({ type: 'offer', sdp: 'mock-sdp' });
  }
  
  public setLocalDescription(): Promise<void> {
    return Promise.resolve();
  }
  
  public setRemoteDescription(): Promise<void> {
    return Promise.resolve();
  }
  
  public addIceCandidate(): Promise<void> {
    return Promise.resolve();
  }
}

// 模拟RTCDataChannel
class MockRTCDataChannel {
  public onopen: Function | null = null;
  public onclose: Function | null = null;
  public onmessage: Function | null = null;
  public readyState: string = 'connecting';
  
  constructor(public label: string) {
    setTimeout(() => {
      this.readyState = 'open';
      if (this.onopen) this.onopen({ target: this });
    }, 10);
  }
  
  public send(data: string): void {
    // 模拟发送数据
  }
  
  public close(): void {
    this.readyState = 'closed';
    if (this.onclose) this.onclose({ target: this });
  }
}

// 设置全局模拟
global.WebSocket = MockWebSocket as any;
global.RTCPeerConnection = MockRTCPeerConnection as any;
global.RTCSessionDescription = Object as any;
global.RTCIceCandidate = Object as any;

describe('NetworkSystem', () => {
  let engine: Engine;
  let world: World;
  let networkSystem: NetworkSystem;
  
  // 在每个测试前创建一个新的网络系统
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({ autoStart: false });
    world = engine.getWorld();
    
    // 创建网络系统
    networkSystem = new NetworkSystem({
      autoConnect: false,
      serverUrl: 'ws://localhost:8080',
      enableWebRTC: true,
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' }
      ]
    });
    
    // 添加网络系统到引擎
    engine.addSystem(networkSystem);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后清理
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试网络系统初始化
  it('应该正确初始化网络系统', () => {
    expect(networkSystem).toBeDefined();
    expect(networkSystem['networkManager']).toBeDefined();
    expect(networkSystem['entitySyncManager']).toBeDefined();
    expect(networkSystem['eventDispatcher']).toBeDefined();
    expect(networkSystem['state']).toBe(NetworkState.DISCONNECTED);
  });
  
  // 测试连接到服务器
  it('应该能够连接到服务器', async () => {
    // 创建连接监听器
    const connectingSpy = vi.fn();
    const connectedSpy = vi.fn();
    
    // 添加事件监听器
    networkSystem.on('connecting', connectingSpy);
    networkSystem.on('connected', connectedSpy);
    
    // 连接到服务器
    networkSystem.connect('ws://localhost:8080');
    
    // 验证状态已更改为连接中
    expect(networkSystem['state']).toBe(NetworkState.CONNECTING);
    
    // 验证连接中事件被触发
    expect(connectingSpy).toHaveBeenCalled();
    
    // 等待连接完成
    await new Promise(resolve => setTimeout(resolve, 20));
    
    // 验证状态已更改为已连接
    expect(networkSystem['state']).toBe(NetworkState.CONNECTED);
    
    // 验证已连接事件被触发
    expect(connectedSpy).toHaveBeenCalled();
  });
  
  // 测试断开连接
  it('应该能够断开连接', async () => {
    // 连接到服务器
    networkSystem.connect('ws://localhost:8080');
    
    // 等待连接完成
    await new Promise(resolve => setTimeout(resolve, 20));
    
    // 创建断开连接监听器
    const disconnectedSpy = vi.fn();
    
    // 添加事件监听器
    networkSystem.on('disconnected', disconnectedSpy);
    
    // 断开连接
    networkSystem.disconnect();
    
    // 验证状态已更改为已断开连接
    expect(networkSystem['state']).toBe(NetworkState.DISCONNECTED);
    
    // 验证已断开连接事件被触发
    expect(disconnectedSpy).toHaveBeenCalled();
  });
  
  // 测试发送消息
  it('应该能够发送消息', async () => {
    // 模拟NetworkManager.sendMessage方法
    const sendMessageSpy = vi.spyOn(networkSystem['networkManager'], 'sendMessage').mockImplementation(() => {});
    
    // 连接到服务器
    networkSystem.connect('ws://localhost:8080');
    
    // 等待连接完成
    await new Promise(resolve => setTimeout(resolve, 20));
    
    // 发送消息
    const message = { type: 'test', data: 'hello' };
    networkSystem.sendMessage(message);
    
    // 验证NetworkManager.sendMessage被调用
    expect(sendMessageSpy).toHaveBeenCalledWith(message);
    
    // 恢复模拟
    sendMessageSpy.mockRestore();
  });
  
  // 测试实体同步
  it('应该能够同步实体', async () => {
    // 模拟EntitySyncManager.registerEntity方法
    const registerEntitySpy = vi.spyOn(networkSystem['entitySyncManager'], 'registerEntity').mockImplementation(() => {});
    
    // 连接到服务器
    networkSystem.connect('ws://localhost:8080');
    
    // 等待连接完成
    await new Promise(resolve => setTimeout(resolve, 20));
    
    // 创建实体
    const entity = new Entity();
    entity.addComponent(new Transform());
    
    // 创建网络实体组件
    const networkComponent = new NetworkEntityComponent(entity, {
      ownerId: 'user1',
      networkId: 'entity1',
      syncPosition: true,
      syncRotation: true,
      syncScale: false,
      syncInterval: 0.1
    });
    
    // 添加组件到实体
    entity.addComponent(networkComponent);
    
    // 添加实体到世界
    world.addEntity(entity);
    
    // 注册网络实体组件
    networkSystem.registerNetworkEntityComponent(entity, networkComponent);
    
    // 验证EntitySyncManager.registerEntity被调用
    expect(registerEntitySpy).toHaveBeenCalledWith(entity, networkComponent);
    
    // 恢复模拟
    registerEntitySpy.mockRestore();
  });
  
  // 测试WebRTC连接
  it('应该能够建立WebRTC连接', async () => {
    // 模拟NetworkManager.createWebRTCConnection方法
    const createWebRTCConnectionSpy = vi.spyOn(networkSystem['networkManager'], 'createWebRTCConnection').mockImplementation(() => {
      return Promise.resolve(new WebRTCConnection('peer1', new MockRTCPeerConnection() as any));
    });
    
    // 连接到服务器
    networkSystem.connect('ws://localhost:8080');
    
    // 等待连接完成
    await new Promise(resolve => setTimeout(resolve, 20));
    
    // 创建WebRTC连接
    const connection = await networkSystem.createWebRTCConnection('peer1');
    
    // 验证NetworkManager.createWebRTCConnection被调用
    expect(createWebRTCConnectionSpy).toHaveBeenCalledWith('peer1');
    
    // 验证连接已创建
    expect(connection).toBeDefined();
    
    // 恢复模拟
    createWebRTCConnectionSpy.mockRestore();
  });
});
