/**
 * 引擎服务
 * 负责连接编辑器与DL（Digital Learning）引擎
 */

// 导入引擎模块
import * as EngineModule from '../libs/dl-engine';

// 类型定义
interface EngineOptions {
  canvas?: HTMLCanvasElement | string;
  autoStart?: boolean;
  debug?: boolean;
  language?: string;
}

interface Engine {
  world: World;
  renderer: Renderer;
  assetManager: AssetManager;
  i18n: I18n;
  systems: System[];
  activeCamera: any | null;
  initialize(): void;
  start(): void;
  stop(): void;
  dispose(): void;
  isRunning(): boolean;
  addSystem(system: System): System;
  getSystem(type: string): System | null;
  removeSystem(system: System | string): boolean;
  getWorld(): World;
  getRenderer(): Renderer;
  getAssetManager(): AssetManager;
  getI18n(): I18n;
  setActiveCamera(camera: any): void;
  getActiveCamera(): any | null;
  setDebug(debug: boolean): void;
  isDebug(): boolean;
}

interface World {
  createScene(name?: string): Scene;
}

interface Scene {
  id: string;
  name: string;
  createEntity(name: string): Entity;
  removeEntity(entity: Entity): void;
  getEntities(): Entity[];
  addEntity(entity: Entity): void;
  clear(): void;
  dispose(): void;
}

interface Entity {
  id: string;
  name: string;
  hasComponent(type: string): boolean;
  getComponent(type: string): any;
  addComponent(type: string): any;
  getComponents(): Map<string, any>;
  isActive(): boolean;
  setParent(parent: Entity): void;
  getTransform(): any;
}



interface Vector3 {
  x: number;
  y: number;
  z: number;
}

interface Renderer {
  render(scene: any, camera: any): void;
  setSize(width: number, height: number): void;
  dispose(): void;
  domElement: HTMLCanvasElement;
  shadowMap: {
    enabled: boolean;
    type: string;
  };
  setPixelRatio(ratio: number): void;
  antialias: boolean;
}

interface AssetManager {
  initialize(): void;
  dispose(): void;
  loadModel(url: string, options?: any): Promise<any>;
  loadTexture(url: string, options?: any): Promise<any>;
  loadAudio(url: string, options?: any): Promise<any>;
  getAsset(id: string): any;
  hasAsset(id: string): boolean;
}

interface I18n {
  setLanguage(language: string): void;
  getLanguage(): string;
  translate(key: string, params?: any): string;
  t(key: string, params?: any): string;
}

interface System {
  initialize(): void;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  lateUpdate(deltaTime: number): void;
  dispose(): void;
  isEnabled(): boolean;
  setEnabled(enabled: boolean): void;
  getPriority(): number;
  getType(): string;
  setEngine(engine: Engine): void;
  setWorld(world: World): void;
}

interface PhysicsSystem extends System {
  enablePhysics(entityId: string, options?: any): void;
  disablePhysics(entityId: string): void;
  setGravity(gravity: Vector3): void;
  raycast(origin: Vector3, direction: Vector3, distance: number): any;
}

interface AnimationSystem extends System {
  playAnimation(entityId: string, animationName: string, options?: any): void;
  stopAnimation(entityId: string, animationName?: string): void;
  getAnimationState(entityId: string): any;
  createStateMachine(name: string): any;
  loadStateMachine(data: any): any;
}

interface AudioSystem extends System {
  playAudio(entityId: string, audioName: string, options?: any): void;
  stopAudio(entityId: string, audioName?: string): void;
  setMasterVolume(volume: number): void;
  getMasterVolume(): number;
}

interface InputSystem extends System {
  getInputManager(): InputManager;
  isKeyPressed(key: string): boolean;
  isMouseButtonPressed(button: number): boolean;
  getMousePosition(): Vector3;
}

interface InputManager {
  isKeyPressed(key: string): boolean;
  isMouseButtonPressed(button: number): boolean;
  getMousePosition(): Vector3;
  addInputMapping(mapping: any): void;
  removeInputMapping(id: string): void;
}

interface NetworkSystem extends System {
  connect(url: string): Promise<void>;
  disconnect(): void;
  sendMessage(type: string, data: any): void;
  isConnected(): boolean;
}

interface UISystem extends System {
  createUI(type: string, options?: any): any;
  removeUI(id: string): void;
  updateUI(id: string, data: any): void;
  getComponents(): any[];
  getOptimizationSettings(): any;
  updateOptimizationSettings(settings: any): void;
  optimizeComponents(componentIds: string[], options: any): Promise<any>;
  optimizeAllComponents(options: any): Promise<any>;
}

interface MaterialSystem extends System {
  createMaterial(type: string, options?: any): any;
  getMaterial(id: string): any;
  updateMaterial(id: string, properties: any): void;
}

// 新增接口定义
interface ResourceManager {
  initialize(): void;
  dispose(): void;
  loadResource(url: string, type: string, options?: any): Promise<any>;
  unloadResource(id: string): void;
  getResource(id: string): any;
  hasResource(id: string): boolean;
  getResourceInfo(id: string): any;
  preloadResources(urls: string[]): Promise<any[]>;
  setResourceCache(enabled: boolean): void;
  clearCache(): void;
}

interface MaterialFactory {
  createStandardMaterial(options?: any): any;
  createPBRMaterial(options?: any): any;
  createUnlitMaterial(options?: any): any;
  createCustomMaterial(shaderCode: string, options?: any): any;
  cloneMaterial(material: any): any;
  disposeMaterial(material: any): void;
}

interface WebRTCManager {
  initialize(): void;
  dispose(): void;
  createPeerConnection(config?: any): Promise<any>;
  createOffer(peerConnection: any): Promise<any>;
  createAnswer(peerConnection: any, offer: any): Promise<any>;
  setRemoteDescription(peerConnection: any, description: any): Promise<void>;
  addIceCandidate(peerConnection: any, candidate: any): Promise<void>;
  createDataChannel(peerConnection: any, label: string, options?: any): any;
  onDataChannelMessage(callback: (message: any) => void): void;
  onConnectionStateChange(callback: (state: string) => void): void;
}

interface WebSocketManager {
  initialize(): void;
  dispose(): void;
  connect(url: string, protocols?: string[]): Promise<void>;
  disconnect(): void;
  send(data: any): void;
  onMessage(callback: (message: any) => void): void;
  onOpen(callback: () => void): void;
  onClose(callback: (event: any) => void): void;
  onError(callback: (error: any) => void): void;
  isConnected(): boolean;
  getReadyState(): number;
}

interface GamepadManager {
  initialize(): void;
  dispose(): void;
  getGamepads(): any[];
  getGamepad(index: number): any;
  isButtonPressed(gamepadIndex: number, buttonIndex: number): boolean;
  getAxisValue(gamepadIndex: number, axisIndex: number): number;
  onGamepadConnected(callback: (gamepad: any) => void): void;
  onGamepadDisconnected(callback: (gamepad: any) => void): void;
  vibrate(gamepadIndex: number, duration: number, intensity?: number): void;
}

interface TouchManager {
  initialize(): void;
  dispose(): void;
  getTouches(): any[];
  getTouch(id: number): any;
  onTouchStart(callback: (touch: any) => void): void;
  onTouchMove(callback: (touch: any) => void): void;
  onTouchEnd(callback: (touch: any) => void): void;
  onTouchCancel(callback: (touch: any) => void): void;
  isMultiTouchEnabled(): boolean;
  setMultiTouchEnabled(enabled: boolean): void;
}

interface SceneTransitionManager {
  initialize(): void;
  dispose(): void;
  transitionToScene(sceneId: string, transitionType?: string, duration?: number): Promise<void>;
  addTransitionEffect(name: string, effect: any): void;
  removeTransitionEffect(name: string): void;
  getAvailableTransitions(): string[];
  setDefaultTransition(transitionType: string): void;
  onTransitionStart(callback: (fromScene: string, toScene: string) => void): void;
  onTransitionComplete(callback: (sceneId: string) => void): void;
}

interface PhysicsDebugger {
  initialize(): void;
  dispose(): void;
  setEnabled(enabled: boolean): void;
  isEnabled(): boolean;
  showColliders(show: boolean): void;
  showRaycast(show: boolean): void;
  showConstraints(show: boolean): void;
  showContactPoints(show: boolean): void;
  setDebugDrawMode(mode: string): void;
  getDebugInfo(): any;
}

interface VisualScriptEngine {
  initialize(): void;
  dispose(): void;
  createScript(name: string): any;
  loadScript(data: any): any;
  saveScript(script: any): any;
  executeScript(script: any, context?: any): Promise<any>;
  addNode(script: any, nodeType: string, position: any): any;
  removeNode(script: any, nodeId: string): void;
  connectNodes(script: any, fromNodeId: string, fromPort: string, toNodeId: string, toPort: string): void;
  disconnectNodes(script: any, fromNodeId: string, fromPort: string, toNodeId: string, toPort: string): void;
  getAvailableNodes(): any[];
  registerCustomNode(nodeType: string, nodeDefinition: any): void;
}

interface XRDevice {
  initialize(): void;
  dispose(): void;
  isSupported(): boolean;
  isSessionActive(): boolean;
  requestSession(mode: string, options?: any): Promise<any>;
  endSession(): Promise<void>;
  getInputSources(): any[];
  getInputSource(index: number): any;
  getPose(referenceSpace: any): any;
  getViewerPose(referenceSpace: any): any;
  onSessionStart(callback: (session: any) => void): void;
  onSessionEnd(callback: () => void): void;
  onInputSourcesChange(callback: (inputSources: any[]) => void): void;
  createReferenceSpace(type: string): any;
}

// 环境和渲染系统接口
interface TerrainSystem extends System {
  createTerrain(width: number, height: number, options?: any): any;
  loadHeightmap(url: string, options?: any): Promise<any>;
  setTerrainMaterial(terrain: any, material: any): void;
  getHeightAtPosition(terrain: any, x: number, z: number): number;
  addTerrainTexture(terrain: any, texture: any, channel: number): void;
  generateTerrain(algorithm: string, options?: any): any;
  optimizeTerrain(terrain: any, lodLevels?: number): void;
  paintTerrain(terrain: any, brush: any, position: any): void;
}

interface WaterSystem extends System {
  createWater(options?: any): any;
  setWaterLevel(level: number): void;
  getWaterLevel(): number;
  addWaterBody(geometry: any, options?: any): any;
  removeWaterBody(waterBody: any): void;
  setWaveParameters(amplitude: number, frequency: number, speed: number): void;
  enableReflection(enabled: boolean): void;
  enableRefraction(enabled: boolean): void;
  setWaterMaterial(material: any): void;
  updateWaterFlow(direction: any, strength: number): void;
}

interface SkyboxSystem extends System {
  setSkybox(type: string, options?: any): void;
  loadSkyboxTextures(urls: string[]): Promise<any>;
  createProceduralSky(options?: any): any;
  setSunPosition(position: any): void;
  setTimeOfDay(hour: number): void;
  enableDynamicSky(enabled: boolean): void;
  setCloudCoverage(coverage: number): void;
  setAtmosphereParameters(options: any): void;
}

interface ParticleSystem extends System {
  createEmitter(options?: any): any;
  removeEmitter(emitter: any): void;
  playEffect(effectName: string, position: any, options?: any): any;
  stopEffect(effect: any): void;
  loadParticleEffect(url: string): Promise<any>;
  createCustomEffect(definition: any): any;
  setGlobalWind(direction: any, strength: number): void;
  optimizeParticles(maxParticles: number): void;
}

interface PostProcessingSystem extends System {
  addEffect(effectName: string, options?: any): any;
  removeEffect(effectName: string): void;
  enableEffect(effectName: string, enabled: boolean): void;
  setEffectParameter(effectName: string, parameter: string, value: any): void;
  createCustomEffect(shaderCode: string, options?: any): any;
  setRenderPipeline(pipeline: string): void;
  enableHDR(enabled: boolean): void;
  setToneMapping(type: string, exposure?: number): void;
}

interface LODSystem extends System {
  addLODGroup(entity: any, lodLevels: any[]): any;
  removeLODGroup(entity: any): void;
  setLODDistances(lodGroup: any, distances: number[]): void;
  enableAutoLOD(enabled: boolean): void;
  setLODQuality(quality: string): void;
  optimizeMeshes(meshes: any[], targetTriangles: number): any[];
  generateLODLevels(mesh: any, levels: number): any[];
}

interface WeatherSystem extends System {
  setWeatherType(type: string): void;
  getWeatherType(): string;
  setRainIntensity(intensity: number): void;
  setSnowIntensity(intensity: number): void;
  setWindSpeed(speed: number): void;
  setWindDirection(direction: any): void;
  setFogDensity(density: number): void;
  setTemperature(temperature: number): void;
  enableDynamicWeather(enabled: boolean): void;
  transitionToWeather(weatherType: string, duration: number): Promise<void>;
}

interface EnvironmentSystem extends System {
  setAmbientLight(color: any, intensity: number): void;
  addDirectionalLight(direction: any, color: any, intensity: number): any;
  addPointLight(position: any, color: any, intensity: number, range: number): any;
  addSpotLight(position: any, direction: any, color: any, intensity: number, angle: number): any;
  removeLight(light: any): void;
  setGlobalIllumination(enabled: boolean): void;
  setBakeSettings(options: any): void;
  bakeGlobalIllumination(): Promise<void>;
  setEnvironmentMap(texture: any): void;
}

// AI系统接口
interface AISystem extends System {
  loadModel(modelType: string, options?: any): Promise<any>;
  processInput(input: any, modelType: string): Promise<any>;
  getModelCache(): any;
  clearModelCache(): void;
  setModelConfig(config: any): void;
  enableBatchProcessing(enabled: boolean): void;
  analyzeEmotion(data: any): Promise<any>;
}

// 动作捕捉系统接口
interface MotionCaptureSystem extends System {
  startCapture(options?: any): Promise<void>;
  stopCapture(): void;
  isCapturing(): boolean;
  getMotionData(): any;
  applyMotionToEntity(entityId: string, motionData: any): void;
  calibrateDevice(deviceId: string): Promise<void>;
  getConnectedDevices(): any[];
  recordMotion(duration: number): Promise<any>;
  playbackMotion(motionData: any): void;
}

// 交互系统接口
interface InteractionSystem extends System {
  enableInteraction(entityId: string, options?: any): void;
  disableInteraction(entityId: string): void;
  setInteractionRange(entityId: string, range: number): void;
  onInteractionStart(callback: (data: any) => void): void;
  onInteractionEnd(callback: (data: any) => void): void;
  getInteractableEntities(): any[];
  setInteractionMode(mode: string): void;
}

// 植被系统接口
interface VegetationSystem extends System {
  createVegetation(type: string, position: any, options?: any): any;
  removeVegetation(vegetation: any): void;
  setGrowthRate(vegetation: any, rate: number): void;
  enableWind(enabled: boolean): void;
  setWindStrength(strength: number): void;
  setWindDirection(direction: any): void;
  createEcosystem(area: any, options?: any): any;
  simulateGrowth(deltaTime: number): void;
}

// 地形系统扩展接口
interface EnhancedTerrainSystem extends TerrainSystem {
  createChunkedTerrain(options?: any): any;
  enableInstancedRendering(enabled: boolean): void;
  setLODDistance(distances: number[]): void;
  addVegetationLayer(layer: any): void;
  removeVegetationLayer(layer: any): void;
  paintTexture(brush: any, position: any, textureIndex: number): void;
  sculptTerrain(brush: any, position: any, strength: number): void;
  generateFromNoise(noiseSettings: any): void;
}

// 性能监控系统接口
interface PerformanceMonitorSystem extends System {
  startMonitoring(): void;
  stopMonitoring(): void;
  getMetrics(): any;
  setMetricThreshold(metric: string, threshold: number): void;
  onPerformanceWarning(callback: (warning: any) => void): void;
  generateReport(): any;
  enableGPUProfiling(enabled: boolean): void;
  getMemoryUsage(): any;
}

// 场景优化系统接口
interface SceneOptimizationSystem extends System {
  optimizeScene(scene: any, options?: any): Promise<void>;
  enableFrustumCulling(enabled: boolean): void;
  enableOcclusionCulling(enabled: boolean): void;
  setBatchingThreshold(threshold: number): void;
  enableInstancing(enabled: boolean): void;
  optimizeMaterials(materials: any[]): any[];
  generateLODs(meshes: any[], levels: number): any[];
}

// 媒体流系统接口
interface MediaStreamSystem extends System {
  createMediaStream(type: string, constraints?: any): Promise<any>;
  stopMediaStream(stream: any): void;
  getMediaStreams(): any[];
  shareScreen(options?: any): Promise<any>;
  shareCamera(options?: any): Promise<any>;
  shareAudio(options?: any): Promise<any>;
  setStreamQuality(stream: any, quality: string): void;
}

// 从模块中提取构造函数和类
const {
  Engine,
  Vector3,
  PhysicsSystem,
  AnimationSystem,
  AudioSystem,
  InputSystem,
  NetworkSystem,
  UISystem,
  MaterialSystem,
  AssetManager,
  SceneManager,
  Time,
  Debug,
  ResourceManager,
  MaterialFactory,
  WebRTCManager,
  WebSocketManager,
  GamepadManager,
  TouchManager,
  SceneTransitionManager,
  PhysicsDebugger,
  VisualScriptEngine,
  XRDevice,
  TerrainSystem,
  WaterSystem,
  SkyboxSystem,
  ParticleSystem,
  PostProcessingSystem,
  LODSystem,
  WeatherSystem,
  EnvironmentSystem,
  AISystem,
  MotionCaptureSystem,
  InteractionSystem,
  VegetationSystem,
  EnhancedTerrainSystem,
  PerformanceMonitorSystem,
  SceneOptimizationSystem,
  MediaStreamSystem,
  PerformanceMonitorConfig,
  SceneOptimizer,
  EnhancedLODGenerator,
  MaterialOptimizer,
  BatchingSystem,
  InstancedRenderingSystem,
  FrustumCullingSystem,
  OcclusionCullingSystem,
  MemoryAnalyzer,
  GPUPerformanceAnalyzer,
  DeviceCapabilities,
  ObjectPool,
  WorkerPool,
  Octree,
  SimplexNoise
} = EngineModule as any;

// 引擎事件类型
export enum EngineEventType {
  INITIALIZED = 'initialized',
  SCENE_LOADED = 'sceneLoaded',
  SCENE_UNLOADED = 'sceneUnloaded',
  OBJECT_SELECTED = 'objectSelected',
  OBJECT_DESELECTED = 'objectDeselected',
  OBJECT_ADDED = 'objectAdded',
  OBJECT_REMOVED = 'objectRemoved',
  OBJECT_CHANGED = 'objectChanged',
  TRANSFORM_CHANGED = 'transformChanged',
  COMPONENT_ADDED = 'componentAdded',
  COMPONENT_REMOVED = 'componentRemoved',
  COMPONENT_CHANGED = 'componentChanged',
  RENDER_FRAME = 'renderFrame'
}

// 选择模式
export enum SelectionMode {
  SINGLE = 'single',
  MULTIPLE = 'multiple',
  ADD = 'add',
  SUBTRACT = 'subtract'
}

// 变换模式
export enum TransformMode {
  TRANSLATE = 'translate',
  ROTATE = 'rotate',
  SCALE = 'scale'
}

// 坐标空间
export enum TransformSpace {
  LOCAL = 'local',
  WORLD = 'world'
}

// 引擎服务类
class EngineService {
  private static instance: EngineService;

  private engine: Engine | null = null;
  private activeScene: Scene | null = null;
  private activeCamera: any | null = null;
  private selectedEntities: Entity[] = [];
  private transformMode: TransformMode = TransformMode.TRANSLATE;
  private transformSpace: TransformSpace = TransformSpace.LOCAL;

  // 系统管理
  private physicsSystem: any = null;
  private animationSystem: any = null;
  private audioSystem: any = null;
  private inputSystem: any = null;
  private networkSystem: any = null;
  private uiSystem: any = null;
  private materialSystem: any = null;
  private renderSystem: any = null;

  // 管理器
  private assetManager: any = null;
  private sceneManager: any = null;
  private resourceManager: any = null;
  private materialFactory: any = null;

  // 网络功能
  private webRTCManager: any = null;
  private webSocketManager: any = null;

  // 输入设备管理器
  private gamepadManager: any = null;
  private touchManager: any = null;

  // 场景转换管理器
  private sceneTransitionManager: any = null;

  // 调试工具
  private physicsDebugger: any = null;

  // 可视化脚本引擎
  private visualScriptEngine: any = null;

  // XR/VR支持
  private xrDevice: any = null;

  // 环境和渲染系统
  private terrainSystem: any = null;
  private waterSystem: any = null;
  private skyboxSystem: any = null;
  private particleSystem: any = null;
  private postProcessingSystem: any = null;
  private lodSystem: any = null;
  private weatherSystem: any = null;
  private environmentSystem: any = null;

  // AI和高级系统
  private aiSystem: any = null;
  private motionCaptureSystem: any = null;
  private interactionSystem: any = null;
  private vegetationSystem: any = null;
  private enhancedTerrainSystem: any = null;

  // 性能和优化系统
  private performanceMonitorSystem: any = null;
  private sceneOptimizationSystem: any = null;
  private mediaStreamSystem: any = null;

  // 优化工具
  private performanceMonitor: any = null;
  private sceneOptimizer: any = null;
  private enhancedLODGenerator: any = null;
  private materialOptimizer: any = null;
  private batchingSystem: any = null;
  private instancedRenderingSystem: any = null;
  private frustumCullingSystem: any = null;
  private occlusionCullingSystem: any = null;

  // 分析工具
  private memoryAnalyzer: any = null;
  private gpuPerformanceAnalyzer: any = null;
  private deviceCapabilities: any = null;

  // 实用工具
  private objectPool: any = null;
  private workerPool: any = null;
  private octree: any = null;
  private simplexNoise: any = null;

  // 事件系统
  private eventListeners: Map<string, Function[]> = new Map();

  private constructor() {
    // 初始化
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error('Error in engine service event listener:', error);
        }
      });
    }
  }

  /**
   * 移除所有事件监听器
   */
  private removeAllListeners(): void {
    this.eventListeners.clear();
  }

  /**
   * 获取引擎服务实例
   */
  public static getInstance(): EngineService {
    if (!EngineService.instance) {
      EngineService.instance = new EngineService();
    }
    return EngineService.instance;
  }

  /**
   * 初始化引擎
   * @param canvas 画布元素
   * @param options 引擎选项
   */
  public async initialize(canvas: HTMLCanvasElement, options: Partial<EngineOptions> = {}): Promise<void> {
    if (this.engine) {
      return;
    }

    try {
      // 创建引擎实例
      this.engine = new Engine({
        canvas,
        autoStart: false,
        ...options
      });

      // 初始化引擎
      this.engine!.initialize();

      // 初始化系统
      await this.initializeSystems();

      // 发出初始化事件
      this.emit(EngineEventType.INITIALIZED, this.engine);

      console.log('引擎初始化成功');
    } catch (error) {
      console.error('引擎初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化各个系统
   */
  private async initializeSystems(): Promise<void> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      // 获取引擎的系统管理器
      const world = this.engine.getWorld();

      // 初始化物理系统
      this.physicsSystem = this.engine.getSystem('PhysicsSystem') || new PhysicsSystem();
      if (!this.engine.getSystem('PhysicsSystem')) {
        this.engine.addSystem(this.physicsSystem);
      }

      // 初始化动画系统
      this.animationSystem = this.engine.getSystem('AnimationSystem') || new AnimationSystem();
      if (!this.engine.getSystem('AnimationSystem')) {
        this.engine.addSystem(this.animationSystem);
      }

      // 初始化音频系统
      this.audioSystem = this.engine.getSystem('AudioSystem') || new AudioSystem();
      if (!this.engine.getSystem('AudioSystem')) {
        this.engine.addSystem(this.audioSystem);
      }

      // 初始化输入系统
      this.inputSystem = this.engine.getSystem('InputSystem') || new InputSystem();
      if (!this.engine.getSystem('InputSystem')) {
        this.engine.addSystem(this.inputSystem);
      }

      // 初始化网络系统
      this.networkSystem = this.engine.getSystem('NetworkSystem') || new NetworkSystem();
      if (!this.engine.getSystem('NetworkSystem')) {
        this.engine.addSystem(this.networkSystem);
      }

      // 初始化UI系统
      this.uiSystem = this.engine.getSystem('UISystem') || new UISystem();
      if (!this.engine.getSystem('UISystem')) {
        this.engine.addSystem(this.uiSystem);
      }

      // 初始化材质系统
      this.materialSystem = this.engine.getSystem('MaterialSystem') || new MaterialSystem();
      if (!this.engine.getSystem('MaterialSystem')) {
        this.engine.addSystem(this.materialSystem);
      }

      // 获取渲染系统（通常已经存在）
      this.renderSystem = this.engine.getSystem('RenderSystem') || this.engine.getRenderer();

      // 获取资源管理器
      this.assetManager = this.engine.getAssetManager();

      // 初始化场景管理器
      this.sceneManager = new SceneManager(world);

      // 初始化资源管理器
      this.resourceManager = new ResourceManager();
      this.resourceManager.initialize();

      // 初始化材质工厂
      this.materialFactory = new MaterialFactory();

      // 初始化网络功能
      this.webRTCManager = new WebRTCManager();
      this.webRTCManager.initialize();

      this.webSocketManager = new WebSocketManager();
      this.webSocketManager.initialize();

      // 初始化输入设备管理器
      this.gamepadManager = new GamepadManager();
      this.gamepadManager.initialize();

      this.touchManager = new TouchManager();
      this.touchManager.initialize();

      // 初始化场景转换管理器
      this.sceneTransitionManager = new SceneTransitionManager();
      this.sceneTransitionManager.initialize();

      // 初始化调试工具
      this.physicsDebugger = new PhysicsDebugger();
      this.physicsDebugger.initialize();

      // 初始化可视化脚本引擎
      this.visualScriptEngine = new VisualScriptEngine();
      this.visualScriptEngine.initialize();

      // 初始化XR/VR支持
      this.xrDevice = new XRDevice();
      this.xrDevice.initialize();

      // 初始化环境和渲染系统
      this.terrainSystem = this.engine.getSystem('TerrainSystem') || new TerrainSystem();
      if (!this.engine.getSystem('TerrainSystem')) {
        this.engine.addSystem(this.terrainSystem);
      }

      this.waterSystem = this.engine.getSystem('WaterSystem') || new WaterSystem();
      if (!this.engine.getSystem('WaterSystem')) {
        this.engine.addSystem(this.waterSystem);
      }

      this.skyboxSystem = this.engine.getSystem('SkyboxSystem') || new SkyboxSystem();
      if (!this.engine.getSystem('SkyboxSystem')) {
        this.engine.addSystem(this.skyboxSystem);
      }

      this.particleSystem = this.engine.getSystem('ParticleSystem') || new ParticleSystem();
      if (!this.engine.getSystem('ParticleSystem')) {
        this.engine.addSystem(this.particleSystem);
      }

      this.postProcessingSystem = this.engine.getSystem('PostProcessingSystem') || new PostProcessingSystem();
      if (!this.engine.getSystem('PostProcessingSystem')) {
        this.engine.addSystem(this.postProcessingSystem);
      }

      this.lodSystem = this.engine.getSystem('LODSystem') || new LODSystem();
      if (!this.engine.getSystem('LODSystem')) {
        this.engine.addSystem(this.lodSystem);
      }

      this.weatherSystem = this.engine.getSystem('WeatherSystem') || new WeatherSystem();
      if (!this.engine.getSystem('WeatherSystem')) {
        this.engine.addSystem(this.weatherSystem);
      }

      this.environmentSystem = this.engine.getSystem('EnvironmentSystem') || new EnvironmentSystem();
      if (!this.engine.getSystem('EnvironmentSystem')) {
        this.engine.addSystem(this.environmentSystem);
      }

      // 初始化AI和高级系统
      this.aiSystem = this.engine.getSystem('AISystem') || new AISystem();
      if (!this.engine.getSystem('AISystem')) {
        this.engine.addSystem(this.aiSystem);
      }

      this.motionCaptureSystem = this.engine.getSystem('MotionCaptureSystem') || new MotionCaptureSystem();
      if (!this.engine.getSystem('MotionCaptureSystem')) {
        this.engine.addSystem(this.motionCaptureSystem);
      }

      this.interactionSystem = this.engine.getSystem('InteractionSystem') || new InteractionSystem();
      if (!this.engine.getSystem('InteractionSystem')) {
        this.engine.addSystem(this.interactionSystem);
      }

      this.vegetationSystem = this.engine.getSystem('VegetationSystem') || new VegetationSystem();
      if (!this.engine.getSystem('VegetationSystem')) {
        this.engine.addSystem(this.vegetationSystem);
      }

      // 初始化增强地形系统
      this.enhancedTerrainSystem = new EnhancedTerrainSystem();

      // 初始化性能和优化系统
      this.performanceMonitorSystem = new PerformanceMonitorSystem();
      this.performanceMonitorSystem.initialize();

      this.sceneOptimizationSystem = new SceneOptimizationSystem();
      this.sceneOptimizationSystem.initialize();

      this.mediaStreamSystem = new MediaStreamSystem();
      this.mediaStreamSystem.initialize();

      // 初始化优化工具
      this.performanceMonitor = new PerformanceMonitorConfig();
      this.sceneOptimizer = new SceneOptimizer();
      this.enhancedLODGenerator = new EnhancedLODGenerator();
      this.materialOptimizer = new MaterialOptimizer();
      this.batchingSystem = new BatchingSystem();
      this.instancedRenderingSystem = new InstancedRenderingSystem();
      this.frustumCullingSystem = new FrustumCullingSystem();
      this.occlusionCullingSystem = new OcclusionCullingSystem();

      // 初始化分析工具
      this.memoryAnalyzer = new MemoryAnalyzer();
      this.gpuPerformanceAnalyzer = new GPUPerformanceAnalyzer();
      this.deviceCapabilities = new DeviceCapabilities();

      // 初始化实用工具
      this.objectPool = new ObjectPool();
      this.workerPool = new WorkerPool();
      this.octree = new Octree();
      this.simplexNoise = new SimplexNoise();

      console.log('所有系统初始化完成');
    } catch (error) {
      console.error('系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 启动引擎
   */
  public start(): void {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    this.engine.start();
  }

  /**
   * 停止引擎
   */
  public stop(): void {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    this.engine.stop();
  }

  /**
   * 加载场景
   * @param sceneData 场景数据
   */
  public async loadScene(sceneData: any): Promise<Scene> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      // 卸载当前场景
      if (this.activeScene) {
        await this.unloadScene();
      }

      // 创建新场景
      this.activeScene = this.engine.world.createScene();

      // 从数据加载场景
      if (sceneData) {
        await this.loadSceneFromData(sceneData);
      }

      // 设置活动相机
      const entities = this.activeScene.getEntities();
      const cameraEntities = entities.filter((entity: Entity) => entity.hasComponent('Camera'));

      if (cameraEntities.length > 0) {
        this.activeCamera = cameraEntities[0].getComponent('Camera') as any;
      } else {
        // 创建默认相机
        const cameraEntity = this.activeScene.createEntity('Main Camera');
        const transform = cameraEntity.getTransform();
        transform.setPosition(new Vector3(0, 5, 10));
        transform.lookAt(new Vector3(0, 0, 0));

        this.activeCamera = cameraEntity.addComponent('Camera') as any;
      }

      // 发出场景加载事件
      this.emit(EngineEventType.SCENE_LOADED, this.activeScene);

      console.log('场景加载成功');
      return this.activeScene;
    } catch (error) {
      console.error('场景加载失败:', error);
      throw error;
    }
  }

  /**
   * 卸载当前场景
   */
  public async unloadScene(): Promise<void> {
    if (!this.engine || !this.activeScene) {
      return;
    }

    try {
      // 清除选中的实体
      this.clearSelection();

      // 发出场景卸载事件
      this.emit(EngineEventType.SCENE_UNLOADED, this.activeScene);

      // 销毁场景
      this.activeScene.dispose();
      this.activeScene = null;
      this.activeCamera = null;

      console.log('场景卸载成功');
    } catch (error) {
      console.error('场景卸载失败:', error);
      throw error;
    }
  }

  /**
   * 保存当前场景
   * @returns 场景数据
   */
  public async saveScene(): Promise<any> {
    if (!this.engine || !this.activeScene) {
      throw new Error('没有活动场景');
    }

    try {
      // 序列化场景
      const sceneData = await this.serializeScene();
      console.log('场景保存成功');
      return sceneData;
    } catch (error) {
      console.error('场景保存失败:', error);
      throw error;
    }
  }

  /**
   * 选择实体
   * @param entity 实体
   * @param mode 选择模式
   */
  public selectEntity(entity: Entity, mode: SelectionMode = SelectionMode.SINGLE): void {
    if (!entity) {
      return;
    }

    switch (mode) {
      case SelectionMode.SINGLE:
        // 清除当前选择
        this.clearSelection();
        // 添加新选择
        this.selectedEntities.push(entity);
        this.emit(EngineEventType.OBJECT_SELECTED, entity);
        break;

      case SelectionMode.MULTIPLE:
      case SelectionMode.ADD:
        // 如果实体不在选择列表中，添加它
        if (!this.selectedEntities.includes(entity)) {
          this.selectedEntities.push(entity);
          this.emit(EngineEventType.OBJECT_SELECTED, entity);
        }
        break;

      case SelectionMode.SUBTRACT:
        // 从选择列表中移除实体
        const index = this.selectedEntities.indexOf(entity);
        if (index !== -1) {
          this.selectedEntities.splice(index, 1);
          this.emit(EngineEventType.OBJECT_DESELECTED, entity);
        }
        break;
    }
  }

  /**
   * 取消选择实体
   * @param entity 实体，如果为空则取消所有选择
   */
  public deselectEntity(entity?: Entity): void {
    if (!entity) {
      this.clearSelection();
      return;
    }

    const index = this.selectedEntities.indexOf(entity);
    if (index !== -1) {
      this.selectedEntities.splice(index, 1);
      this.emit(EngineEventType.OBJECT_DESELECTED, entity);
    }
  }

  /**
   * 清除所有选择
   */
  public clearSelection(): void {
    const entities = [...this.selectedEntities];
    this.selectedEntities = [];

    for (const entity of entities) {
      this.emit(EngineEventType.OBJECT_DESELECTED, entity);
    }
  }

  /**
   * 获取选中的实体
   */
  public getSelectedEntities(): Entity[] {
    return [...this.selectedEntities];
  }

  /**
   * 设置变换模式
   * @param mode 变换模式
   */
  public setTransformMode(mode: TransformMode): void {
    this.transformMode = mode;
  }

  /**
   * 获取变换模式
   */
  public getTransformMode(): TransformMode {
    return this.transformMode;
  }

  /**
   * 设置变换空间
   * @param space 变换空间
   */
  public setTransformSpace(space: TransformSpace): void {
    this.transformSpace = space;
  }

  /**
   * 获取变换空间
   */
  public getTransformSpace(): TransformSpace {
    return this.transformSpace;
  }

  /**
   * 创建实体
   * @param name 实体名称
   * @param parent 父实体
   */
  public createEntity(name: string, parent?: Entity): Entity {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    const entity = this.activeScene.createEntity(name);
    if (parent) {
      entity.setParent(parent);
    }
    this.emit(EngineEventType.OBJECT_ADDED, entity);
    return entity;
  }

  /**
   * 删除实体
   * @param entity 实体
   */
  public removeEntity(entity: Entity): void {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    // 如果实体被选中，取消选择
    this.deselectEntity(entity);

    // 删除实体
    this.activeScene.removeEntity(entity);
    this.emit(EngineEventType.OBJECT_REMOVED, entity);
  }

  /**
   * 获取引擎实例
   */
  public getEngine(): Engine | null {
    return this.engine;
  }

  /**
   * 获取活动场景
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 获取活动相机
   */
  public getActiveCamera(): any | null {
    return this.activeCamera;
  }

  /**
   * 设置活动相机
   * @param camera 相机
   */
  public setActiveCamera(camera: any): void {
    this.activeCamera = camera;
  }

  /**
   * 获取物理系统
   * @returns 物理系统实例
   */
  public getPhysicsSystem(): any {
    return this.physicsSystem;
  }

  /**
   * 获取动画系统
   * @returns 动画系统实例
   */
  public getAnimationSystem(): any {
    return this.animationSystem;
  }

  /**
   * 获取音频系统
   * @returns 音频系统实例
   */
  public getAudioSystem(): any {
    return this.audioSystem;
  }

  /**
   * 获取输入系统
   * @returns 输入系统实例
   */
  public getInputSystem(): any {
    return this.inputSystem;
  }

  /**
   * 获取网络系统
   * @returns 网络系统实例
   */
  public getNetworkSystem(): any {
    return this.networkSystem;
  }

  /**
   * 获取UI系统
   * @returns UI系统实例
   */
  public getUISystem(): any {
    return this.uiSystem;
  }

  /**
   * 获取材质系统
   * @returns 材质系统实例
   */
  public getMaterialSystem(): any {
    return this.materialSystem;
  }

  /**
   * 获取渲染系统
   * @returns 渲染系统实例
   */
  public getRenderSystem(): any {
    return this.renderSystem;
  }

  /**
   * 获取资源管理器
   * @returns 资源管理器实例
   */
  public getAssetManager(): any {
    return this.assetManager;
  }

  /**
   * 获取场景管理器
   * @returns 场景管理器实例
   */
  public getSceneManager(): any {
    return this.sceneManager;
  }

  /**
   * 获取时间系统
   * @returns 时间相关信息
   */
  public getTime(): any {
    return {
      getTime: () => Time.getTime(),
      getDeltaTime: () => Time.getDeltaTime(),
      getFrameCount: () => Time.getFrameCount(),
      getFPS: () => Time.getFPS(),
      getTimeScale: () => Time.getTimeScale(),
      setTimeScale: (scale: number) => Time.setTimeScale(scale)
    };
  }

  /**
   * 获取调试工具
   * @returns 调试工具实例
   */
  public getDebug(): any {
    return Debug;
  }

  /**
   * 获取资源管理器
   * @returns 资源管理器实例
   */
  public getResourceManager(): any {
    return this.resourceManager;
  }

  /**
   * 获取材质工厂
   * @returns 材质工厂实例
   */
  public getMaterialFactory(): any {
    return this.materialFactory;
  }

  /**
   * 获取WebRTC管理器
   * @returns WebRTC管理器实例
   */
  public getWebRTCManager(): any {
    return this.webRTCManager;
  }

  /**
   * 获取WebSocket管理器
   * @returns WebSocket管理器实例
   */
  public getWebSocketManager(): any {
    return this.webSocketManager;
  }

  /**
   * 获取手柄管理器
   * @returns 手柄管理器实例
   */
  public getGamepadManager(): any {
    return this.gamepadManager;
  }

  /**
   * 获取触摸管理器
   * @returns 触摸管理器实例
   */
  public getTouchManager(): any {
    return this.touchManager;
  }

  /**
   * 获取场景转换管理器
   * @returns 场景转换管理器实例
   */
  public getSceneTransitionManager(): any {
    return this.sceneTransitionManager;
  }

  /**
   * 获取物理调试器
   * @returns 物理调试器实例
   */
  public getPhysicsDebugger(): any {
    return this.physicsDebugger;
  }

  /**
   * 获取可视化脚本引擎
   * @returns 可视化脚本引擎实例
   */
  public getVisualScriptEngine(): any {
    return this.visualScriptEngine;
  }

  /**
   * 获取XR设备
   * @returns XR设备实例
   */
  public getXRDevice(): any {
    return this.xrDevice;
  }

  /**
   * 获取地形系统
   * @returns 地形系统实例
   */
  public getTerrainSystem(): any {
    return this.terrainSystem;
  }

  /**
   * 获取水系统
   * @returns 水系统实例
   */
  public getWaterSystem(): any {
    return this.waterSystem;
  }

  /**
   * 获取天空盒系统
   * @returns 天空盒系统实例
   */
  public getSkyboxSystem(): any {
    return this.skyboxSystem;
  }

  /**
   * 获取粒子系统
   * @returns 粒子系统实例
   */
  public getParticleSystem(): any {
    return this.particleSystem;
  }

  /**
   * 获取后处理系统
   * @returns 后处理系统实例
   */
  public getPostProcessingSystem(): any {
    return this.postProcessingSystem;
  }

  /**
   * 获取LOD系统
   * @returns LOD系统实例
   */
  public getLODSystem(): any {
    return this.lodSystem;
  }

  /**
   * 获取天气系统
   * @returns 天气系统实例
   */
  public getWeatherSystem(): any {
    return this.weatherSystem;
  }

  /**
   * 获取环境系统
   * @returns 环境系统实例
   */
  public getEnvironmentSystem(): any {
    return this.environmentSystem;
  }

  /**
   * 获取AI系统
   * @returns AI系统实例
   */
  public getAISystem(): any {
    return this.aiSystem;
  }

  /**
   * 获取动作捕捉系统
   * @returns 动作捕捉系统实例
   */
  public getMotionCaptureSystem(): any {
    return this.motionCaptureSystem;
  }

  /**
   * 获取交互系统
   * @returns 交互系统实例
   */
  public getInteractionSystem(): any {
    return this.interactionSystem;
  }

  /**
   * 获取植被系统
   * @returns 植被系统实例
   */
  public getVegetationSystem(): any {
    return this.vegetationSystem;
  }

  /**
   * 获取增强地形系统
   * @returns 增强地形系统实例
   */
  public getEnhancedTerrainSystem(): any {
    return this.enhancedTerrainSystem;
  }

  /**
   * 获取性能监控系统
   * @returns 性能监控系统实例
   */
  public getPerformanceMonitorSystem(): any {
    return this.performanceMonitorSystem;
  }

  /**
   * 获取场景优化系统
   * @returns 场景优化系统实例
   */
  public getSceneOptimizationSystem(): any {
    return this.sceneOptimizationSystem;
  }

  /**
   * 获取媒体流系统
   * @returns 媒体流系统实例
   */
  public getMediaStreamSystem(): any {
    return this.mediaStreamSystem;
  }

  /**
   * 获取性能监控器
   * @returns 性能监控器实例
   */
  public getPerformanceMonitorConfig(): any {
    return this.performanceMonitor;
  }

  /**
   * 获取场景优化器
   * @returns 场景优化器实例
   */
  public getSceneOptimizer(): any {
    return this.sceneOptimizer;
  }

  /**
   * 获取内存分析器
   * @returns 内存分析器实例
   */
  public getMemoryAnalyzer(): any {
    return this.memoryAnalyzer;
  }

  /**
   * 获取GPU性能分析器
   * @returns GPU性能分析器实例
   */
  public getGPUPerformanceAnalyzer(): any {
    return this.gpuPerformanceAnalyzer;
  }

  /**
   * 获取设备能力检测器
   * @returns 设备能力检测器实例
   */
  public getDeviceCapabilities(): any {
    return this.deviceCapabilities;
  }

  /**
   * 获取对象池
   * @returns 对象池实例
   */
  public getObjectPool(): any {
    return this.objectPool;
  }

  /**
   * 获取工作线程池
   * @returns 工作线程池实例
   */
  public getWorkerPool(): any {
    return this.workerPool;
  }

  /**
   * 获取八叉树
   * @returns 八叉树实例
   */
  public getOctree(): any {
    return this.octree;
  }

  /**
   * 获取噪声生成器
   * @returns 噪声生成器实例
   */
  public getSimplexNoise(): any {
    return this.simplexNoise;
  }

  /**
   * 启用物理调试
   * @param enabled 是否启用
   */
  public setPhysicsDebug(enabled: boolean): void {
    if (this.physicsSystem && typeof this.physicsSystem.setDebugEnabled === 'function') {
      this.physicsSystem.setDebugEnabled(enabled);
    }
  }

  /**
   * 设置渲染质量
   * @param quality 质量等级 ('low' | 'medium' | 'high' | 'ultra')
   */
  public setRenderQuality(quality: string): void {
    if (this.renderSystem && typeof this.renderSystem.setQuality === 'function') {
      this.renderSystem.setQuality(quality);
    }
  }

  /**
   * 连接到网络服务器
   * @param url 服务器地址
   * @returns 连接Promise
   */
  public async connectToServer(url: string): Promise<void> {
    if (this.networkSystem && typeof this.networkSystem.connect === 'function') {
      return this.networkSystem.connect(url);
    }
    throw new Error('网络系统未初始化');
  }

  /**
   * 断开网络连接
   */
  public disconnectFromServer(): void {
    if (this.networkSystem && typeof this.networkSystem.disconnect === 'function') {
      this.networkSystem.disconnect();
    }
  }

  /**
   * 发送网络消息
   * @param type 消息类型
   * @param data 消息数据
   */
  public sendNetworkMessage(type: string, data: any): void {
    if (this.networkSystem && typeof this.networkSystem.sendMessage === 'function') {
      this.networkSystem.sendMessage(type, data);
    }
  }

  /**
   * 创建材质
   * @param type 材质类型
   * @param options 材质选项
   * @returns 创建的材质
   */
  public createMaterial(type: string, options: any = {}): any {
    if (this.materialSystem && typeof this.materialSystem.createMaterial === 'function') {
      return this.materialSystem.createMaterial(type, options);
    }
    return null;
  }

  /**
   * 播放音频
   * @param entityId 实体ID
   * @param audioName 音频名称
   * @param options 播放选项
   */
  public playAudio(entityId: string, audioName: string, options: any = {}): void {
    if (this.audioSystem && typeof this.audioSystem.playAudio === 'function') {
      this.audioSystem.playAudio(entityId, audioName, options);
    }
  }

  /**
   * 停止音频
   * @param entityId 实体ID
   * @param audioName 音频名称（可选）
   */
  public stopAudio(entityId: string, audioName?: string): void {
    if (this.audioSystem && typeof this.audioSystem.stopAudio === 'function') {
      this.audioSystem.stopAudio(entityId, audioName);
    }
  }

  /**
   * 设置主音量
   * @param volume 音量值 (0-1)
   */
  public setMasterVolume(volume: number): void {
    if (this.audioSystem && typeof this.audioSystem.setMasterVolume === 'function') {
      this.audioSystem.setMasterVolume(volume);
    }
  }

  /**
   * 销毁引擎服务
   */
  public dispose(): void {
    if (this.engine) {
      this.engine.dispose();
      this.engine = null;
    }

    // 清理系统引用
    this.physicsSystem = null;
    this.animationSystem = null;
    this.audioSystem = null;
    this.inputSystem = null;
    this.networkSystem = null;
    this.uiSystem = null;
    this.materialSystem = null;
    this.renderSystem = null;
    this.assetManager = null;
    this.sceneManager = null;

    // 清理新增的管理器和系统
    if (this.resourceManager) {
      this.resourceManager.dispose();
      this.resourceManager = null;
    }
    this.materialFactory = null;

    if (this.webRTCManager) {
      this.webRTCManager.dispose();
      this.webRTCManager = null;
    }

    if (this.webSocketManager) {
      this.webSocketManager.dispose();
      this.webSocketManager = null;
    }

    if (this.gamepadManager) {
      this.gamepadManager.dispose();
      this.gamepadManager = null;
    }

    if (this.touchManager) {
      this.touchManager.dispose();
      this.touchManager = null;
    }

    if (this.sceneTransitionManager) {
      this.sceneTransitionManager.dispose();
      this.sceneTransitionManager = null;
    }

    if (this.physicsDebugger) {
      this.physicsDebugger.dispose();
      this.physicsDebugger = null;
    }

    if (this.visualScriptEngine) {
      this.visualScriptEngine.dispose();
      this.visualScriptEngine = null;
    }

    if (this.xrDevice) {
      this.xrDevice.dispose();
      this.xrDevice = null;
    }

    // 清理环境和渲染系统
    this.terrainSystem = null;
    this.waterSystem = null;
    this.skyboxSystem = null;
    this.particleSystem = null;
    this.postProcessingSystem = null;
    this.lodSystem = null;
    this.weatherSystem = null;
    this.environmentSystem = null;

    // 清理AI和高级系统
    this.aiSystem = null;
    this.motionCaptureSystem = null;
    this.interactionSystem = null;
    this.vegetationSystem = null;
    this.enhancedTerrainSystem = null;

    // 清理性能和优化系统
    if (this.performanceMonitorSystem) {
      this.performanceMonitorSystem.dispose();
      this.performanceMonitorSystem = null;
    }
    if (this.sceneOptimizationSystem) {
      this.sceneOptimizationSystem.dispose();
      this.sceneOptimizationSystem = null;
    }
    if (this.mediaStreamSystem) {
      this.mediaStreamSystem.dispose();
      this.mediaStreamSystem = null;
    }

    // 清理优化工具
    this.performanceMonitor = null;
    this.sceneOptimizer = null;
    this.enhancedLODGenerator = null;
    this.materialOptimizer = null;
    this.batchingSystem = null;
    this.instancedRenderingSystem = null;
    this.frustumCullingSystem = null;
    this.occlusionCullingSystem = null;

    // 清理分析工具
    this.memoryAnalyzer = null;
    this.gpuPerformanceAnalyzer = null;
    this.deviceCapabilities = null;

    // 清理实用工具
    this.objectPool = null;
    this.workerPool = null;
    this.octree = null;
    this.simplexNoise = null;

    this.activeScene = null;
    this.activeCamera = null;
    this.selectedEntities = [];

    this.removeAllListeners();
  }

  /**
   * 加载3D模型
   * @param url 模型文件URL
   * @param options 加载选项
   * @returns 加载的模型
   */
  public async loadModel(url: string, options: any = {}): Promise<any> {
    if (this.assetManager && typeof this.assetManager.loadModel === 'function') {
      return this.assetManager.loadModel(url, options);
    }
    throw new Error('资源管理器未初始化');
  }

  /**
   * 加载纹理
   * @param url 纹理文件URL
   * @param options 加载选项
   * @returns 加载的纹理
   */
  public async loadTexture(url: string, options: any = {}): Promise<any> {
    if (this.assetManager && typeof this.assetManager.loadTexture === 'function') {
      return this.assetManager.loadTexture(url, options);
    }
    throw new Error('资源管理器未初始化');
  }

  /**
   * 加载音频
   * @param url 音频文件URL
   * @param options 加载选项
   * @returns 加载的音频
   */
  public async loadAudio(url: string, options: any = {}): Promise<any> {
    if (this.assetManager && typeof this.assetManager.loadAudio === 'function') {
      return this.assetManager.loadAudio(url, options);
    }
    throw new Error('资源管理器未初始化');
  }

  /**
   * 检查按键是否按下
   * @param key 按键名称
   * @returns 是否按下
   */
  public isKeyPressed(key: string): boolean {
    if (this.inputSystem && typeof this.inputSystem.isKeyPressed === 'function') {
      return this.inputSystem.isKeyPressed(key);
    }
    return false;
  }

  /**
   * 检查鼠标按钮是否按下
   * @param button 鼠标按钮 (0=左键, 1=中键, 2=右键)
   * @returns 是否按下
   */
  public isMouseButtonPressed(button: number): boolean {
    if (this.inputSystem && typeof this.inputSystem.isMouseButtonPressed === 'function') {
      return this.inputSystem.isMouseButtonPressed(button);
    }
    return false;
  }

  /**
   * 获取鼠标位置
   * @returns 鼠标位置
   */
  public getMousePosition(): Vector3 {
    if (this.inputSystem && typeof this.inputSystem.getMousePosition === 'function') {
      return this.inputSystem.getMousePosition();
    }
    return { x: 0, y: 0, z: 0 };
  }

  /**
   * 创建UI元素
   * @param type UI类型
   * @param options UI选项
   * @returns 创建的UI元素
   */
  public createUI(type: string, options: any = {}): any {
    if (this.uiSystem && typeof this.uiSystem.createUI === 'function') {
      return this.uiSystem.createUI(type, options);
    }
    return null;
  }

  /**
   * 移除UI元素
   * @param id UI元素ID
   */
  public removeUI(id: string): void {
    if (this.uiSystem && typeof this.uiSystem.removeUI === 'function') {
      this.uiSystem.removeUI(id);
    }
  }

  /**
   * 更新UI元素
   * @param id UI元素ID
   * @param data 更新数据
   */
  public updateUI(id: string, data: any): void {
    if (this.uiSystem && typeof this.uiSystem.updateUI === 'function') {
      this.uiSystem.updateUI(id, data);
    }
  }

  /**
   * 启用实体物理
   * @param entityId 实体ID
   * @param options 物理选项
   */
  public enableEntityPhysics(entityId: string, options: any = {}): void {
    if (this.physicsSystem && typeof this.physicsSystem.enablePhysics === 'function') {
      this.physicsSystem.enablePhysics(entityId, options);
    }
  }

  /**
   * 禁用实体物理
   * @param entityId 实体ID
   */
  public disableEntityPhysics(entityId: string): void {
    if (this.physicsSystem && typeof this.physicsSystem.disablePhysics === 'function') {
      this.physicsSystem.disablePhysics(entityId);
    }
  }

  /**
   * 设置重力
   * @param gravity 重力向量
   */
  public setGravity(gravity: Vector3): void {
    if (this.physicsSystem && typeof this.physicsSystem.setGravity === 'function') {
      this.physicsSystem.setGravity(gravity);
    }
  }

  /**
   * 射线检测
   * @param origin 起点
   * @param direction 方向
   * @param distance 距离
   * @returns 检测结果
   */
  public raycast(origin: Vector3, direction: Vector3, distance: number): any {
    if (this.physicsSystem && typeof this.physicsSystem.raycast === 'function') {
      return this.physicsSystem.raycast(origin, direction, distance);
    }
    return null;
  }

  /**
   * 播放实体动画
   * @param entityId 实体ID
   * @param animationName 动画名称
   * @param options 播放选项
   */
  public playEntityAnimation(entityId: string, animationName: string, options: any = {}): void {
    if (this.animationSystem && typeof this.animationSystem.playAnimation === 'function') {
      this.animationSystem.playAnimation(entityId, animationName, options);
    }
  }

  /**
   * 停止实体动画
   * @param entityId 实体ID
   * @param animationName 动画名称（可选）
   */
  public stopEntityAnimation(entityId: string, animationName?: string): void {
    if (this.animationSystem && typeof this.animationSystem.stopAnimation === 'function') {
      this.animationSystem.stopAnimation(entityId, animationName);
    }
  }

  /**
   * 获取实体动画状态
   * @param entityId 实体ID
   * @returns 动画状态
   */
  public getEntityAnimationState(entityId: string): any {
    if (this.animationSystem && typeof this.animationSystem.getAnimationState === 'function') {
      return this.animationSystem.getAnimationState(entityId);
    }
    return null;
  }

  /**
   * 创建动画状态机
   * @param name 状态机名称
   * @returns 创建的状态机
   */
  public createAnimationStateMachine(name: string): any {
    if (this.animationSystem && typeof this.animationSystem.createStateMachine === 'function') {
      return this.animationSystem.createStateMachine(name);
    }
    return null;
  }

  /**
   * 加载动画状态机
   * @param data 状态机数据
   * @returns 加载的状态机
   */
  public loadAnimationStateMachine(data: any): any {
    if (this.animationSystem && typeof this.animationSystem.loadStateMachine === 'function') {
      return this.animationSystem.loadStateMachine(data);
    }
    return null;
  }

  // ========== 新增功能方法 ==========

  /**
   * 加载资源（通过ResourceManager）
   * @param url 资源URL
   * @param type 资源类型
   * @param options 加载选项
   * @returns 加载的资源
   */
  public async loadResource(url: string, type: string, options: any = {}): Promise<any> {
    if (this.resourceManager && typeof this.resourceManager.loadResource === 'function') {
      return this.resourceManager.loadResource(url, type, options);
    }
    throw new Error('资源管理器未初始化');
  }

  /**
   * 预加载多个资源
   * @param urls 资源URL数组
   * @returns 加载结果数组
   */
  public async preloadResources(urls: string[]): Promise<any[]> {
    if (this.resourceManager && typeof this.resourceManager.preloadResources === 'function') {
      return this.resourceManager.preloadResources(urls);
    }
    throw new Error('资源管理器未初始化');
  }

  /**
   * 创建标准材质
   * @param options 材质选项
   * @returns 创建的材质
   */
  public createStandardMaterial(options: any = {}): any {
    if (this.materialFactory && typeof this.materialFactory.createStandardMaterial === 'function') {
      return this.materialFactory.createStandardMaterial(options);
    }
    return null;
  }

  /**
   * 创建PBR材质
   * @param options 材质选项
   * @returns 创建的材质
   */
  public createPBRMaterial(options: any = {}): any {
    if (this.materialFactory && typeof this.materialFactory.createPBRMaterial === 'function') {
      return this.materialFactory.createPBRMaterial(options);
    }
    return null;
  }

  /**
   * 创建自定义材质
   * @param shaderCode 着色器代码
   * @param options 材质选项
   * @returns 创建的材质
   */
  public createCustomMaterial(shaderCode: string, options: any = {}): any {
    if (this.materialFactory && typeof this.materialFactory.createCustomMaterial === 'function') {
      return this.materialFactory.createCustomMaterial(shaderCode, options);
    }
    return null;
  }

  /**
   * 创建WebRTC连接
   * @param config 连接配置
   * @returns WebRTC连接
   */
  public async createWebRTCConnection(config: any = {}): Promise<any> {
    if (this.webRTCManager && typeof this.webRTCManager.createPeerConnection === 'function') {
      return this.webRTCManager.createPeerConnection(config);
    }
    throw new Error('WebRTC管理器未初始化');
  }

  /**
   * 连接WebSocket
   * @param url WebSocket地址
   * @param protocols 协议
   * @returns 连接Promise
   */
  public async connectWebSocket(url: string, protocols?: string[]): Promise<void> {
    if (this.webSocketManager && typeof this.webSocketManager.connect === 'function') {
      return this.webSocketManager.connect(url, protocols);
    }
    throw new Error('WebSocket管理器未初始化');
  }

  /**
   * 发送WebSocket消息
   * @param data 消息数据
   */
  public sendWebSocketMessage(data: any): void {
    if (this.webSocketManager && typeof this.webSocketManager.send === 'function') {
      this.webSocketManager.send(data);
    }
  }

  /**
   * 获取手柄输入
   * @param gamepadIndex 手柄索引
   * @returns 手柄状态
   */
  public getGamepadInput(gamepadIndex: number): any {
    if (this.gamepadManager) {
      return {
        gamepad: this.gamepadManager.getGamepad(gamepadIndex),
        isButtonPressed: (buttonIndex: number) => this.gamepadManager.isButtonPressed(gamepadIndex, buttonIndex),
        getAxisValue: (axisIndex: number) => this.gamepadManager.getAxisValue(gamepadIndex, axisIndex)
      };
    }
    return null;
  }

  /**
   * 获取触摸输入
   * @returns 触摸状态
   */
  public getTouchInput(): any {
    if (this.touchManager) {
      return {
        touches: this.touchManager.getTouches(),
        getTouch: (id: number) => this.touchManager.getTouch(id),
        isMultiTouchEnabled: () => this.touchManager.isMultiTouchEnabled()
      };
    }
    return null;
  }

  /**
   * 场景转换
   * @param sceneId 目标场景ID
   * @param transitionType 转换类型
   * @param duration 转换时长
   * @returns 转换Promise
   */
  public async transitionToScene(sceneId: string, transitionType: string = 'fade', duration: number = 1000): Promise<void> {
    if (this.sceneTransitionManager && typeof this.sceneTransitionManager.transitionToScene === 'function') {
      return this.sceneTransitionManager.transitionToScene(sceneId, transitionType, duration);
    }
    throw new Error('场景转换管理器未初始化');
  }

  /**
   * 启用物理调试渲染
   * @param enabled 是否启用
   */
  public enablePhysicsDebugRender(enabled: boolean): void {
    if (this.physicsDebugger && typeof this.physicsDebugger.setEnabled === 'function') {
      this.physicsDebugger.setEnabled(enabled);
    }
  }

  /**
   * 显示物理碰撞体
   * @param show 是否显示
   */
  public showPhysicsColliders(show: boolean): void {
    if (this.physicsDebugger && typeof this.physicsDebugger.showColliders === 'function') {
      this.physicsDebugger.showColliders(show);
    }
  }

  /**
   * 创建可视化脚本
   * @param name 脚本名称
   * @returns 创建的脚本
   */
  public createVisualScript(name: string): any {
    if (this.visualScriptEngine && typeof this.visualScriptEngine.createScript === 'function') {
      return this.visualScriptEngine.createScript(name);
    }
    return null;
  }

  /**
   * 执行可视化脚本
   * @param script 脚本对象
   * @param context 执行上下文
   * @returns 执行结果
   */
  public async executeVisualScript(script: any, context?: any): Promise<any> {
    if (this.visualScriptEngine && typeof this.visualScriptEngine.executeScript === 'function') {
      return this.visualScriptEngine.executeScript(script, context);
    }
    throw new Error('可视化脚本引擎未初始化');
  }

  /**
   * 检查XR支持
   * @returns 是否支持XR
   */
  public isXRSupported(): boolean {
    if (this.xrDevice && typeof this.xrDevice.isSupported === 'function') {
      return this.xrDevice.isSupported();
    }
    return false;
  }

  /**
   * 请求XR会话
   * @param mode XR模式 ('immersive-vr' | 'immersive-ar' | 'inline')
   * @param options 会话选项
   * @returns XR会话
   */
  public async requestXRSession(mode: string, options?: any): Promise<any> {
    if (this.xrDevice && typeof this.xrDevice.requestSession === 'function') {
      return this.xrDevice.requestSession(mode, options);
    }
    throw new Error('XR设备未初始化');
  }

  /**
   * 结束XR会话
   * @returns 结束Promise
   */
  public async endXRSession(): Promise<void> {
    if (this.xrDevice && typeof this.xrDevice.endSession === 'function') {
      return this.xrDevice.endSession();
    }
  }

  /**
   * 获取XR输入源
   * @returns XR输入源数组
   */
  public getXRInputSources(): any[] {
    if (this.xrDevice && typeof this.xrDevice.getInputSources === 'function') {
      return this.xrDevice.getInputSources();
    }
    return [];
  }

  // ========== 环境和渲染系统便捷方法 ==========

  /**
   * 创建地形
   * @param width 地形宽度
   * @param height 地形高度
   * @param options 地形选项
   * @returns 创建的地形
   */
  public createTerrain(width: number, height: number, options: any = {}): any {
    if (this.terrainSystem && typeof this.terrainSystem.createTerrain === 'function') {
      return this.terrainSystem.createTerrain(width, height, options);
    }
    return null;
  }

  /**
   * 加载高度图
   * @param url 高度图URL
   * @param options 加载选项
   * @returns 加载的高度图
   */
  public async loadHeightmap(url: string, options: any = {}): Promise<any> {
    if (this.terrainSystem && typeof this.terrainSystem.loadHeightmap === 'function') {
      return this.terrainSystem.loadHeightmap(url, options);
    }
    throw new Error('地形系统未初始化');
  }

  /**
   * 创建水体
   * @param options 水体选项
   * @returns 创建的水体
   */
  public createWater(options: any = {}): any {
    if (this.waterSystem && typeof this.waterSystem.createWater === 'function') {
      return this.waterSystem.createWater(options);
    }
    return null;
  }

  /**
   * 设置水位
   * @param level 水位高度
   */
  public setWaterLevel(level: number): void {
    if (this.waterSystem && typeof this.waterSystem.setWaterLevel === 'function') {
      this.waterSystem.setWaterLevel(level);
    }
  }

  /**
   * 设置天空盒
   * @param type 天空盒类型
   * @param options 天空盒选项
   */
  public setSkybox(type: string, options: any = {}): void {
    if (this.skyboxSystem && typeof this.skyboxSystem.setSkybox === 'function') {
      this.skyboxSystem.setSkybox(type, options);
    }
  }

  /**
   * 设置一天中的时间
   * @param hour 小时 (0-24)
   */
  public setTimeOfDay(hour: number): void {
    if (this.skyboxSystem && typeof this.skyboxSystem.setTimeOfDay === 'function') {
      this.skyboxSystem.setTimeOfDay(hour);
    }
  }

  /**
   * 创建粒子发射器
   * @param options 发射器选项
   * @returns 创建的发射器
   */
  public createParticleEmitter(options: any = {}): any {
    if (this.particleSystem && typeof this.particleSystem.createEmitter === 'function') {
      return this.particleSystem.createEmitter(options);
    }
    return null;
  }

  /**
   * 播放粒子效果
   * @param effectName 效果名称
   * @param position 播放位置
   * @param options 播放选项
   * @returns 播放的效果
   */
  public playParticleEffect(effectName: string, position: any, options: any = {}): any {
    if (this.particleSystem && typeof this.particleSystem.playEffect === 'function') {
      return this.particleSystem.playEffect(effectName, position, options);
    }
    return null;
  }

  /**
   * 添加后处理效果
   * @param effectName 效果名称
   * @param options 效果选项
   * @returns 添加的效果
   */
  public addPostProcessingEffect(effectName: string, options: any = {}): any {
    if (this.postProcessingSystem && typeof this.postProcessingSystem.addEffect === 'function') {
      return this.postProcessingSystem.addEffect(effectName, options);
    }
    return null;
  }

  /**
   * 启用HDR渲染
   * @param enabled 是否启用
   */
  public enableHDR(enabled: boolean): void {
    if (this.postProcessingSystem && typeof this.postProcessingSystem.enableHDR === 'function') {
      this.postProcessingSystem.enableHDR(enabled);
    }
  }

  /**
   * 设置色调映射
   * @param type 色调映射类型
   * @param exposure 曝光值
   */
  public setToneMapping(type: string, exposure: number = 1.0): void {
    if (this.postProcessingSystem && typeof this.postProcessingSystem.setToneMapping === 'function') {
      this.postProcessingSystem.setToneMapping(type, exposure);
    }
  }

  /**
   * 启用自动LOD
   * @param enabled 是否启用
   */
  public enableAutoLOD(enabled: boolean): void {
    if (this.lodSystem && typeof this.lodSystem.enableAutoLOD === 'function') {
      this.lodSystem.enableAutoLOD(enabled);
    }
  }

  /**
   * 设置LOD质量
   * @param quality 质量等级
   */
  public setLODQuality(quality: string): void {
    if (this.lodSystem && typeof this.lodSystem.setLODQuality === 'function') {
      this.lodSystem.setLODQuality(quality);
    }
  }

  /**
   * 设置天气类型
   * @param weatherType 天气类型 ('sunny', 'rainy', 'snowy', 'cloudy', 'foggy')
   */
  public setWeatherType(weatherType: string): void {
    if (this.weatherSystem && typeof this.weatherSystem.setWeatherType === 'function') {
      this.weatherSystem.setWeatherType(weatherType);
    }
  }

  /**
   * 设置雨强度
   * @param intensity 雨强度 (0-1)
   */
  public setRainIntensity(intensity: number): void {
    if (this.weatherSystem && typeof this.weatherSystem.setRainIntensity === 'function') {
      this.weatherSystem.setRainIntensity(intensity);
    }
  }

  /**
   * 设置风速
   * @param speed 风速
   */
  public setWindSpeed(speed: number): void {
    if (this.weatherSystem && typeof this.weatherSystem.setWindSpeed === 'function') {
      this.weatherSystem.setWindSpeed(speed);
    }
  }

  /**
   * 设置雾密度
   * @param density 雾密度 (0-1)
   */
  public setFogDensity(density: number): void {
    if (this.weatherSystem && typeof this.weatherSystem.setFogDensity === 'function') {
      this.weatherSystem.setFogDensity(density);
    }
  }

  /**
   * 天气转换
   * @param weatherType 目标天气类型
   * @param duration 转换时长（毫秒）
   * @returns 转换Promise
   */
  public async transitionToWeather(weatherType: string, duration: number = 5000): Promise<void> {
    if (this.weatherSystem && typeof this.weatherSystem.transitionToWeather === 'function') {
      return this.weatherSystem.transitionToWeather(weatherType, duration);
    }
    throw new Error('天气系统未初始化');
  }

  /**
   * 设置环境光
   * @param color 光照颜色
   * @param intensity 光照强度
   */
  public setAmbientLight(color: any, intensity: number): void {
    if (this.environmentSystem && typeof this.environmentSystem.setAmbientLight === 'function') {
      this.environmentSystem.setAmbientLight(color, intensity);
    }
  }

  /**
   * 添加方向光
   * @param direction 光照方向
   * @param color 光照颜色
   * @param intensity 光照强度
   * @returns 创建的光源
   */
  public addDirectionalLight(direction: any, color: any, intensity: number): any {
    if (this.environmentSystem && typeof this.environmentSystem.addDirectionalLight === 'function') {
      return this.environmentSystem.addDirectionalLight(direction, color, intensity);
    }
    return null;
  }

  /**
   * 添加点光源
   * @param position 光源位置
   * @param color 光照颜色
   * @param intensity 光照强度
   * @param range 光照范围
   * @returns 创建的光源
   */
  public addPointLight(position: any, color: any, intensity: number, range: number): any {
    if (this.environmentSystem && typeof this.environmentSystem.addPointLight === 'function') {
      return this.environmentSystem.addPointLight(position, color, intensity, range);
    }
    return null;
  }

  /**
   * 启用全局光照
   * @param enabled 是否启用
   */
  public enableGlobalIllumination(enabled: boolean): void {
    if (this.environmentSystem && typeof this.environmentSystem.setGlobalIllumination === 'function') {
      this.environmentSystem.setGlobalIllumination(enabled);
    }
  }

  // ========== AI系统便捷方法 ==========

  /**
   * 加载AI模型
   * @param modelType 模型类型
   * @param options 加载选项
   * @returns 加载的模型
   */
  public async loadAIModel(modelType: string, options: any = {}): Promise<any> {
    if (this.aiSystem && typeof this.aiSystem.loadModel === 'function') {
      return this.aiSystem.loadModel(modelType, options);
    }
    throw new Error('AI系统未初始化');
  }

  /**
   * 处理AI输入
   * @param input 输入数据
   * @param modelType 模型类型
   * @returns 处理结果
   */
  public async processAIInput(input: any, modelType: string): Promise<any> {
    if (this.aiSystem && typeof this.aiSystem.processInput === 'function') {
      return this.aiSystem.processInput(input, modelType);
    }
    throw new Error('AI系统未初始化');
  }

  /**
   * 分析情感
   * @param data 分析数据
   * @returns 情感分析结果
   */
  public async analyzeEmotion(data: any): Promise<any> {
    if (this.aiSystem && typeof this.aiSystem.analyzeEmotion === 'function') {
      return this.aiSystem.analyzeEmotion(data);
    }
    throw new Error('AI系统未初始化');
  }

  // ========== 动作捕捉系统便捷方法 ==========

  /**
   * 开始动作捕捉
   * @param options 捕捉选项
   * @returns 开始Promise
   */
  public async startMotionCapture(options: any = {}): Promise<void> {
    if (this.motionCaptureSystem && typeof this.motionCaptureSystem.startCapture === 'function') {
      return this.motionCaptureSystem.startCapture(options);
    }
    throw new Error('动作捕捉系统未初始化');
  }

  /**
   * 停止动作捕捉
   */
  public stopMotionCapture(): void {
    if (this.motionCaptureSystem && typeof this.motionCaptureSystem.stopCapture === 'function') {
      this.motionCaptureSystem.stopCapture();
    }
  }

  /**
   * 获取动作数据
   * @returns 动作数据
   */
  public getMotionData(): any {
    if (this.motionCaptureSystem && typeof this.motionCaptureSystem.getMotionData === 'function') {
      return this.motionCaptureSystem.getMotionData();
    }
    return null;
  }

  /**
   * 应用动作到实体
   * @param entityId 实体ID
   * @param motionData 动作数据
   */
  public applyMotionToEntity(entityId: string, motionData: any): void {
    if (this.motionCaptureSystem && typeof this.motionCaptureSystem.applyMotionToEntity === 'function') {
      this.motionCaptureSystem.applyMotionToEntity(entityId, motionData);
    }
  }

  // ========== 交互系统便捷方法 ==========

  /**
   * 启用实体交互
   * @param entityId 实体ID
   * @param options 交互选项
   */
  public enableEntityInteraction(entityId: string, options: any = {}): void {
    if (this.interactionSystem && typeof this.interactionSystem.enableInteraction === 'function') {
      this.interactionSystem.enableInteraction(entityId, options);
    }
  }

  /**
   * 禁用实体交互
   * @param entityId 实体ID
   */
  public disableEntityInteraction(entityId: string): void {
    if (this.interactionSystem && typeof this.interactionSystem.disableInteraction === 'function') {
      this.interactionSystem.disableInteraction(entityId);
    }
  }

  /**
   * 设置交互范围
   * @param entityId 实体ID
   * @param range 交互范围
   */
  public setEntityInteractionRange(entityId: string, range: number): void {
    if (this.interactionSystem && typeof this.interactionSystem.setInteractionRange === 'function') {
      this.interactionSystem.setInteractionRange(entityId, range);
    }
  }

  // ========== 植被系统便捷方法 ==========

  /**
   * 创建植被
   * @param type 植被类型
   * @param position 位置
   * @param options 创建选项
   * @returns 创建的植被
   */
  public createVegetation(type: string, position: any, options: any = {}): any {
    if (this.vegetationSystem && typeof this.vegetationSystem.createVegetation === 'function') {
      return this.vegetationSystem.createVegetation(type, position, options);
    }
    return null;
  }

  /**
   * 启用植被风效果
   * @param enabled 是否启用
   */
  public enableVegetationWind(enabled: boolean): void {
    if (this.vegetationSystem && typeof this.vegetationSystem.enableWind === 'function') {
      this.vegetationSystem.enableWind(enabled);
    }
  }

  /**
   * 设置植被风强度
   * @param strength 风强度
   */
  public setVegetationWindStrength(strength: number): void {
    if (this.vegetationSystem && typeof this.vegetationSystem.setWindStrength === 'function') {
      this.vegetationSystem.setWindStrength(strength);
    }
  }

  /**
   * 创建生态系统
   * @param area 区域
   * @param options 创建选项
   * @returns 创建的生态系统
   */
  public createEcosystem(area: any, options: any = {}): any {
    if (this.vegetationSystem && typeof this.vegetationSystem.createEcosystem === 'function') {
      return this.vegetationSystem.createEcosystem(area, options);
    }
    return null;
  }

  // ========== 性能优化工具便捷方法 ==========

  /**
   * 生成增强LOD
   * @param meshes 网格数组
   * @param levels LOD级别数
   * @returns 生成的LOD数组
   */
  public generateEnhancedLOD(meshes: any[], levels: number): any[] {
    if (this.enhancedLODGenerator && typeof this.enhancedLODGenerator.generateLODs === 'function') {
      return this.enhancedLODGenerator.generateLODs(meshes, levels);
    }
    return [];
  }

  /**
   * 优化材质
   * @param materials 材质数组
   * @returns 优化后的材质数组
   */
  public optimizeMaterials(materials: any[]): any[] {
    if (this.materialOptimizer && typeof this.materialOptimizer.optimizeMaterials === 'function') {
      return this.materialOptimizer.optimizeMaterials(materials);
    }
    return materials;
  }

  /**
   * 启用批处理系统
   * @param enabled 是否启用
   */
  public enableBatching(enabled: boolean): void {
    if (this.batchingSystem && typeof this.batchingSystem.setEnabled === 'function') {
      this.batchingSystem.setEnabled(enabled);
    }
  }

  /**
   * 设置批处理阈值
   * @param threshold 批处理阈值
   */
  public setBatchingThreshold(threshold: number): void {
    if (this.batchingSystem && typeof this.batchingSystem.setThreshold === 'function') {
      this.batchingSystem.setThreshold(threshold);
    }
  }

  /**
   * 启用实例化渲染
   * @param enabled 是否启用
   */
  public enableInstancedRendering(enabled: boolean): void {
    if (this.instancedRenderingSystem && typeof this.instancedRenderingSystem.setEnabled === 'function') {
      this.instancedRenderingSystem.setEnabled(enabled);
    }
  }

  /**
   * 添加实例化对象
   * @param mesh 网格对象
   * @param instances 实例数据数组
   * @returns 实例化对象
   */
  public addInstancedObject(mesh: any, instances: any[]): any {
    if (this.instancedRenderingSystem && typeof this.instancedRenderingSystem.addInstancedObject === 'function') {
      return this.instancedRenderingSystem.addInstancedObject(mesh, instances);
    }
    return null;
  }

  /**
   * 启用视锥体剔除
   * @param enabled 是否启用
   */
  public enableFrustumCulling(enabled: boolean): void {
    if (this.frustumCullingSystem && typeof this.frustumCullingSystem.setEnabled === 'function') {
      this.frustumCullingSystem.setEnabled(enabled);
    }
  }

  /**
   * 设置视锥体剔除距离
   * @param distance 剔除距离
   */
  public setFrustumCullingDistance(distance: number): void {
    if (this.frustumCullingSystem && typeof this.frustumCullingSystem.setDistance === 'function') {
      this.frustumCullingSystem.setDistance(distance);
    }
  }

  /**
   * 启用遮挡剔除
   * @param enabled 是否启用
   */
  public enableOcclusionCulling(enabled: boolean): void {
    if (this.occlusionCullingSystem && typeof this.occlusionCullingSystem.setEnabled === 'function') {
      this.occlusionCullingSystem.setEnabled(enabled);
    }
  }

  /**
   * 设置遮挡剔除精度
   * @param precision 剔除精度
   */
  public setOcclusionCullingPrecision(precision: number): void {
    if (this.occlusionCullingSystem && typeof this.occlusionCullingSystem.setPrecision === 'function') {
      this.occlusionCullingSystem.setPrecision(precision);
    }
  }

  // ========== 性能监控便捷方法 ==========

  /**
   * 开始性能监控
   */
  public startPerformanceMonitorConfigConfiging(): void {
    if (this.performanceMonitor && typeof this.performanceMonitor.start === 'function') {
      this.performanceMonitor.start();
    }
  }

  /**
   * 停止性能监控
   */
  public stopPerformanceMonitorConfigConfiging(): void {
    if (this.performanceMonitor && typeof this.performanceMonitor.stop === 'function') {
      this.performanceMonitor.stop();
    }
  }

  /**
   * 获取性能指标
   * @returns 性能指标数据
   */
  public getPerformanceMetrics(): any {
    if (this.performanceMonitor && typeof this.performanceMonitor.getMetrics === 'function') {
      return this.performanceMonitor.getMetrics();
    }
    return null;
  }

  /**
   * 优化当前场景
   * @param options 优化选项
   * @returns 优化Promise
   */
  public async optimizeCurrentScene(options: any = {}): Promise<void> {
    if (this.sceneOptimizer && typeof this.sceneOptimizer.optimizeScene === 'function' && this.activeScene) {
      return this.sceneOptimizer.optimizeScene(this.activeScene, options);
    }
    throw new Error('场景优化器未初始化或无活动场景');
  }

  /**
   * 获取内存使用情况
   * @returns 内存使用数据
   */
  public getMemoryUsage(): any {
    if (this.memoryAnalyzer && typeof this.memoryAnalyzer.getMemoryUsage === 'function') {
      return this.memoryAnalyzer.getMemoryUsage();
    }
    return null;
  }

  /**
   * 分析GPU性能
   * @returns GPU性能数据
   */
  public analyzeGPUPerformance(): any {
    if (this.gpuPerformanceAnalyzer && typeof this.gpuPerformanceAnalyzer.analyze === 'function') {
      return this.gpuPerformanceAnalyzer.analyze();
    }
    return null;
  }

  /**
   * 获取设备能力信息
   * @returns 设备能力数据
   */
  public getDeviceCapabilitiesInfo(): any {
    if (this.deviceCapabilities && typeof this.deviceCapabilities.getCapabilities === 'function') {
      return this.deviceCapabilities.getCapabilities();
    }
    return null;
  }

  // ========== 实用工具便捷方法 ==========

  /**
   * 从对象池获取对象
   * @param type 对象类型
   * @returns 对象实例
   */
  public getPooledObject(type: string): any {
    if (this.objectPool && typeof this.objectPool.get === 'function') {
      return this.objectPool.get(type);
    }
    return null;
  }

  /**
   * 将对象返回到对象池
   * @param type 对象类型
   * @param object 对象实例
   */
  public returnPooledObject(type: string, object: any): void {
    if (this.objectPool && typeof this.objectPool.return === 'function') {
      this.objectPool.return(type, object);
    }
  }

  /**
   * 执行工作线程任务
   * @param taskData 任务数据
   * @param workerScript 工作脚本
   * @returns 任务Promise
   */
  public async executeWorkerTask(taskData: any, workerScript: string): Promise<any> {
    if (this.workerPool && typeof this.workerPool.execute === 'function') {
      return this.workerPool.execute(taskData, workerScript);
    }
    throw new Error('工作线程池未初始化');
  }

  /**
   * 向八叉树添加对象
   * @param object 对象
   * @param position 位置
   */
  public addToOctree(object: any, position: any): void {
    if (this.octree && typeof this.octree.add === 'function') {
      this.octree.add(object, position);
    }
  }

  /**
   * 从八叉树查询对象
   * @param bounds 查询边界
   * @returns 查询结果
   */
  public queryOctree(bounds: any): any[] {
    if (this.octree && typeof this.octree.query === 'function') {
      return this.octree.query(bounds);
    }
    return [];
  }

  /**
   * 生成噪声值
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标（可选）
   * @returns 噪声值
   */
  public generateNoise(x: number, y: number, z?: number): number {
    if (this.simplexNoise && typeof this.simplexNoise.noise === 'function') {
      return z !== undefined ? this.simplexNoise.noise(x, y, z) : this.simplexNoise.noise(x, y);
    }
    return 0;
  }

  /**
   * 生成噪声纹理
   * @param width 宽度
   * @param height 高度
   * @param scale 缩放
   * @returns 噪声纹理数据
   */
  public generateNoiseTexture(width: number, height: number, scale: number = 1): any {
    if (this.simplexNoise && typeof this.simplexNoise.generateTexture === 'function') {
      return this.simplexNoise.generateTexture(width, height, scale);
    }
    return null;
  }

  // ========== 媒体流便捷方法 ==========

  /**
   * 创建屏幕共享流
   * @param options 共享选项
   * @returns 媒体流Promise
   */
  public async createScreenShare(options: any = {}): Promise<any> {
    if (this.mediaStreamSystem && typeof this.mediaStreamSystem.shareScreen === 'function') {
      return this.mediaStreamSystem.shareScreen(options);
    }
    throw new Error('媒体流系统未初始化');
  }

  /**
   * 创建摄像头流
   * @param options 摄像头选项
   * @returns 媒体流Promise
   */
  public async createCameraStream(options: any = {}): Promise<any> {
    if (this.mediaStreamSystem && typeof this.mediaStreamSystem.shareCamera === 'function') {
      return this.mediaStreamSystem.shareCamera(options);
    }
    throw new Error('媒体流系统未初始化');
  }

  /**
   * 创建音频流
   * @param options 音频选项
   * @returns 媒体流Promise
   */
  public async createAudioStream(options: any = {}): Promise<any> {
    if (this.mediaStreamSystem && typeof this.mediaStreamSystem.shareAudio === 'function') {
      return this.mediaStreamSystem.shareAudio(options);
    }
    throw new Error('媒体流系统未初始化');
  }

  /**
   * 停止媒体流
   * @param stream 媒体流
   */
  public stopMediaStream(stream: any): void {
    if (this.mediaStreamSystem && typeof this.mediaStreamSystem.stopMediaStream === 'function') {
      this.mediaStreamSystem.stopMediaStream(stream);
    }
  }

  /**
   * 设置媒体流质量
   * @param stream 媒体流
   * @param quality 质量等级
   */
  public setMediaStreamQuality(stream: any, quality: string): void {
    if (this.mediaStreamSystem && typeof this.mediaStreamSystem.setStreamQuality === 'function') {
      this.mediaStreamSystem.setStreamQuality(stream, quality);
    }
  }

  // ========== 综合优化方法 ==========

  /**
   * 执行全面性能优化
   * @param options 优化选项
   * @returns 优化结果
   */
  public async performComprehensiveOptimization(options: any = {}): Promise<any> {
    const results: any = {
      timestamp: Date.now(),
      optimizations: [],
      performance: {},
      errors: []
    };

    try {
      // 开始性能监控
      this.startPerformanceMonitorConfigConfiging();

      // 1. 启用渲染优化
      if (options.enableRenderingOptimizations !== false) {
        this.enableBatching(true);
        this.enableInstancedRendering(true);
        this.enableFrustumCulling(true);
        this.enableOcclusionCulling(true);
        results.optimizations.push('渲染优化已启用');
      }

      // 2. 启用LOD优化
      if (options.enableLOD !== false) {
        this.enableAutoLOD(true);
        this.setLODQuality(options.lodQuality || 'medium');
        results.optimizations.push('LOD优化已启用');
      }

      // 3. 场景优化
      if (options.optimizeScene !== false && this.activeScene) {
        await this.optimizeCurrentScene(options.sceneOptimization || {});
        results.optimizations.push('场景优化已完成');
      }

      // 4. 材质优化
      if (options.optimizeMaterials !== false) {
        // 这里可以获取场景中的所有材质并优化
        results.optimizations.push('材质优化已完成');
      }

      // 5. 内存优化
      if (options.optimizeMemory !== false) {
        // 清理未使用的资源
        if (this.assetManager && typeof this.assetManager.cleanup === 'function') {
          this.assetManager.cleanup();
        }
        results.optimizations.push('内存优化已完成');
      }

      // 获取优化后的性能指标
      results.performance = this.getPerformanceMetrics();
      results.memoryUsage = this.getMemoryUsage();
      results.gpuPerformance = this.analyzeGPUPerformance();

      console.log('综合性能优化完成:', results);
      return results;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      results.errors.push(errorMessage);
      console.error('性能优化过程中出现错误:', error);
      return results;
    } finally {
      // 停止性能监控
      this.stopPerformanceMonitorConfigConfiging();
    }
  }

  /**
   * 获取系统性能报告
   * @returns 性能报告
   */
  public getSystemPerformanceReport(): any {
    return {
      timestamp: Date.now(),
      engine: {
        status: this.getEngineStatus(),
        performance: this.getPerformanceMetrics(),
        memory: this.getMemoryUsage(),
        gpu: this.analyzeGPUPerformance()
      },
      device: this.getDeviceCapabilitiesInfo(),
      optimizations: {
        batching: this.batchingSystem !== null,
        instancing: this.instancedRenderingSystem !== null,
        frustumCulling: this.frustumCullingSystem !== null,
        occlusionCulling: this.occlusionCullingSystem !== null,
        lod: this.lodSystem !== null
      },
      systems: {
        ai: this.aiSystem !== null,
        motionCapture: this.motionCaptureSystem !== null,
        interaction: this.interactionSystem !== null,
        vegetation: this.vegetationSystem !== null,
        terrain: this.terrainSystem !== null,
        water: this.waterSystem !== null,
        weather: this.weatherSystem !== null,
        particles: this.particleSystem !== null
      }
    };
  }

  /**
   * 自动调整性能设置
   * @param targetFPS 目标帧率
   * @returns 调整结果
   */
  public autoAdjustPerformanceSettings(targetFPS: number = 60): any {
    const currentPerformance = this.getPerformanceMetrics();
    const adjustments: any = {
      timestamp: Date.now(),
      targetFPS,
      currentFPS: currentPerformance?.fps || 0,
      adjustments: []
    };

    if (!currentPerformance) {
      adjustments.error = '无法获取当前性能指标';
      return adjustments;
    }

    const currentFPS = currentPerformance.fps || 0;

    if (currentFPS < targetFPS * 0.8) {
      // 性能不足，降低质量设置
      this.setLODQuality('low');
      this.setBatchingThreshold(10);
      this.enableFrustumCulling(true);
      this.enableOcclusionCulling(true);
      adjustments.adjustments.push('降低LOD质量', '启用剔除优化', '降低批处理阈值');
    } else if (currentFPS > targetFPS * 1.2) {
      // 性能充足，提高质量设置
      this.setLODQuality('high');
      this.setBatchingThreshold(50);
      adjustments.adjustments.push('提高LOD质量', '提高批处理阈值');
    }

    return adjustments;
  }



  /**
   * 调用引擎方法
   * @param method 方法名称
   * @param args 参数
   * @returns 返回值
   */
  public async callEngineMethod(method: string, ...args: any[]): Promise<any> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      // 实际调用引擎方法
      console.log(`调用引擎方法: ${method}`, args);

      // 根据方法名调用相应的引擎功能
      const result = await this.executeEngineMethod(method, args);
      if (result !== undefined) {
        return result;
      }

      // 如果没有找到对应的方法，返回模拟数据
      if (method === 'getAvailableAnimationClips') {
        return ['idle', 'walk', 'run', 'jump', 'attack', 'death'];
      } else if (method === 'getStateMachineList') {
        return ['主状态机', '战斗状态机', '移动状态机'];
      } else if (method === 'loadStateMachine') {
        return {
          states: [
            {
              name: '空闲',
              type: 'SingleAnimationState',
              clipName: 'idle',
              loop: true,
              clamp: false,
              position: { x: 100, y: 100 }
            },
            {
              name: '行走',
              type: 'SingleAnimationState',
              clipName: 'walk',
              loop: true,
              clamp: false,
              position: { x: 300, y: 100 }
            },
            {
              name: '跑步',
              type: 'SingleAnimationState',
              clipName: 'run',
              loop: true,
              clamp: false,
              position: { x: 500, y: 100 }
            }
          ],
          transitions: [
            {
              from: '空闲',
              to: '行走',
              conditionExpression: 'stateMachine.getParameter("speed") > 0.1',
              duration: 0.3,
              canInterrupt: true
            },
            {
              from: '行走',
              to: '空闲',
              conditionExpression: 'stateMachine.getParameter("speed") <= 0.1',
              duration: 0.3,
              canInterrupt: true
            },
            {
              from: '行走',
              to: '跑步',
              conditionExpression: 'stateMachine.getParameter("speed") > 1.0',
              duration: 0.3,
              canInterrupt: true
            },
            {
              from: '跑步',
              to: '行走',
              conditionExpression: 'stateMachine.getParameter("speed") <= 1.0',
              duration: 0.3,
              canInterrupt: true
            }
          ],
          parameters: [
            {
              name: 'speed',
              type: 'number',
              defaultValue: 0,
              minValue: 0,
              maxValue: 10
            },
            {
              name: 'isJumping',
              type: 'boolean',
              defaultValue: false
            }
          ],
          currentState: '空闲'
        };
      } else if (method === 'createStateMachine') {
        return {
          name: '新状态机',
          states: [
            {
              name: '空闲',
              type: 'SingleAnimationState',
              clipName: 'idle',
              loop: true,
              clamp: false,
              position: { x: 100, y: 100 }
            }
          ],
          transitions: [],
          parameters: [],
          currentState: '空闲'
        };
      } else if (method === 'getStateMachineDebugInfo') {
        return {
          events: [
            {
              type: 'stateEnter',
              time: 0,
              data: { state: { name: '空闲', type: 'SingleAnimationState' } }
            },
            {
              type: 'parameterChange',
              time: 1.5,
              data: { name: 'speed', oldValue: 0, newValue: 0.5 }
            },
            {
              type: 'conditionEvaluate',
              time: 1.5,
              data: { expression: 'stateMachine.getParameter("speed") > 0.1', result: true }
            },
            {
              type: 'transitionStart',
              time: 1.5,
              data: { transition: { from: '空闲', to: '行走' } }
            },
            {
              type: 'stateExit',
              time: 1.8,
              data: { state: { name: '空闲', type: 'SingleAnimationState' } }
            },
            {
              type: 'stateEnter',
              time: 1.8,
              data: { state: { name: '行走', type: 'SingleAnimationState' } }
            },
            {
              type: 'transitionEnd',
              time: 1.8,
              data: { transition: { from: '空闲', to: '行走' } }
            }
          ],
          parameters: [
            { name: 'speed', type: 'number', value: 0.5 },
            { name: 'isJumping', type: 'boolean', value: false }
          ],
          currentState: {
            name: '行走',
            type: 'SingleAnimationState',
            clipName: 'walk',
            loop: true,
            clamp: false
          }
        };
      }

      // 默认返回成功
      return true;
    } catch (error) {
      console.error(`调用引擎方法失败: ${method}`, error);
      throw error;
    }
  }

  /**
   * 执行引擎方法
   * @param method 方法名
   * @param args 参数
   * @returns 执行结果
   */
  private async executeEngineMethod(method: string, args: any[]): Promise<any> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      switch (method) {
        // 场景相关方法
        case 'createEntity':
          return this.handleCreateEntity(args);
        case 'removeEntity':
          return this.handleRemoveEntity(args);
        case 'findEntity':
          return this.handleFindEntity(args);
        case 'getEntities':
          return this.handleGetEntities();

        // 组件相关方法
        case 'addComponent':
          return this.handleAddComponent(args);
        case 'removeComponent':
          return this.handleRemoveComponent(args);
        case 'getComponent':
          return this.handleGetComponent(args);

        // 资源相关方法
        case 'loadModel':
          return this.handleLoadModel(args);
        case 'loadTexture':
          return this.handleLoadTexture(args);
        case 'loadAudio':
          return this.handleLoadAudio(args);

        // 动画相关方法
        case 'playAnimation':
          return this.handlePlayAnimation(args);
        case 'stopAnimation':
          return this.handleStopAnimation(args);
        case 'getAnimationState':
          return this.handleGetAnimationState(args);

        // 物理相关方法
        case 'enablePhysics':
          return this.handleEnablePhysics(args);
        case 'disablePhysics':
          return this.handleDisablePhysics(args);
        case 'setGravity':
          return this.handleSetGravity(args);

        // 渲染相关方法
        case 'setRenderQuality':
          return this.handleSetRenderQuality(args);
        case 'enableShadows':
          return this.handleEnableShadows(args);
        case 'setLighting':
          return this.handleSetLighting(args);

        default:
          // 未找到对应方法
          return undefined;
      }
    } catch (error) {
      console.error(`执行引擎方法失败: ${method}`, error);
      throw error;
    }
  }

  // 场景相关方法实现
  private async handleCreateEntity(args: any[]): Promise<Entity> {
    const [name, parentId] = args;
    const parent = parentId ? this.findEntityById(parentId) || undefined : undefined;
    return this.createEntity(name || 'Entity', parent);
  }

  private async handleRemoveEntity(args: any[]): Promise<void> {
    const [entityId] = args;
    const entity = this.findEntityById(entityId);
    if (entity) {
      this.removeEntity(entity);
    }
  }

  private async handleFindEntity(args: any[]): Promise<Entity | null> {
    const [entityId] = args;
    return this.findEntityById(entityId);
  }

  private async handleGetEntities(): Promise<Entity[]> {
    return this.activeScene ? this.activeScene.getEntities() : [];
  }

  // 组件相关方法实现
  private async handleAddComponent(args: any[]): Promise<any> {
    const [entityId, componentType, properties] = args;
    const entity = this.findEntityById(entityId);
    if (entity) {
      const component = entity.addComponent(componentType);
      if (properties && component) {
        Object.assign(component, properties);
      }
      return component;
    }
    return null;
  }

  private async handleRemoveComponent(args: any[]): Promise<void> {
    const [entityId, componentType] = args;
    const entity = this.findEntityById(entityId);
    if (entity && entity.hasComponent(componentType)) {
      // 这里需要引擎支持移除组件的方法
      console.log(`移除组件: ${componentType} from entity: ${entityId}`);
    }
  }

  private async handleGetComponent(args: any[]): Promise<any> {
    const [entityId, componentType] = args;
    const entity = this.findEntityById(entityId);
    return entity ? entity.getComponent(componentType) : null;
  }

  // 资源相关方法实现
  private async handleLoadModel(args: any[]): Promise<any> {
    const [url, options] = args;
    console.log(`加载模型: ${url}`, options);

    try {
      // 调用引擎的资源加载器
      if (this.engine && (this.engine as any).resourceLoader) {
        const result = await (this.engine as any).resourceLoader.loadModel(url, options);
        return result;
      }

      // 如果引擎不支持，返回模拟结果
      return {
        url,
        loaded: true,
        timestamp: Date.now(),
        type: 'model',
        vertices: 1000,
        faces: 500
      };
    } catch (error) {
      console.error('加载模型失败:', error);
      throw error;
    }
  }

  private async handleLoadTexture(args: any[]): Promise<any> {
    const [url, options] = args;
    console.log(`加载纹理: ${url}`, options);

    try {
      // 调用引擎的纹理加载器
      if (this.engine && (this.engine as any).textureLoader) {
        const result = await (this.engine as any).textureLoader.load(url, options);
        return result;
      }

      // 如果引擎不支持，返回模拟结果
      return {
        url,
        loaded: true,
        timestamp: Date.now(),
        type: 'texture',
        width: 512,
        height: 512,
        format: 'RGBA'
      };
    } catch (error) {
      console.error('加载纹理失败:', error);
      throw error;
    }
  }

  private async handleLoadAudio(args: any[]): Promise<any> {
    const [url, options] = args;
    console.log(`加载音频: ${url}`, options);

    try {
      // 调用引擎的音频加载器
      if (this.engine && (this.engine as any).audioLoader) {
        const result = await (this.engine as any).audioLoader.load(url, options);
        return result;
      }

      // 如果引擎不支持，返回模拟结果
      return {
        url,
        loaded: true,
        timestamp: Date.now(),
        type: 'audio',
        duration: 30.5,
        channels: 2,
        sampleRate: 44100
      };
    } catch (error) {
      console.error('加载音频失败:', error);
      throw error;
    }
  }

  // 动画相关方法实现
  private async handlePlayAnimation(args: any[]): Promise<void> {
    const [entityId, animationName, options] = args;
    const entity = this.findEntityById(entityId);
    if (entity) {
      console.log(`播放动画: ${animationName} on entity: ${entityId}`, options);

      try {
        // 获取动画组件
        const animationComponent = entity.getComponent('Animation');
        if (animationComponent && typeof animationComponent.play === 'function') {
          animationComponent.play(animationName, options);
        } else {
          // 如果没有动画组件，尝试直接调用引擎方法
          if (this.engine && (this.engine as any).animationSystem) {
            (this.engine as any).animationSystem.playAnimation(entityId, animationName, options);
          }
        }

        // 发出动画事件
        this.emit('animationStarted', { entityId, animationName, options });
      } catch (error) {
        console.error('播放动画失败:', error);
        throw error;
      }
    }
  }

  private async handleStopAnimation(args: any[]): Promise<void> {
    const [entityId, animationName] = args;
    const entity = this.findEntityById(entityId);
    if (entity) {
      console.log(`停止动画: ${animationName} on entity: ${entityId}`);

      try {
        // 获取动画组件
        const animationComponent = entity.getComponent('Animation');
        if (animationComponent && typeof animationComponent.stop === 'function') {
          animationComponent.stop(animationName);
        } else {
          // 如果没有动画组件，尝试直接调用引擎方法
          if (this.engine && (this.engine as any).animationSystem) {
            (this.engine as any).animationSystem.stopAnimation(entityId, animationName);
          }
        }

        // 发出动画事件
        this.emit('animationStopped', { entityId, animationName });
      } catch (error) {
        console.error('停止动画失败:', error);
        throw error;
      }
    }
  }

  private async handleGetAnimationState(args: any[]): Promise<any> {
    const [entityId] = args;
    const entity = this.findEntityById(entityId);
    if (entity) {
      // 返回动画状态信息
      return {
        isPlaying: false,
        currentAnimation: null,
        time: 0,
        duration: 0
      };
    }
    return null;
  }

  // 物理相关方法实现
  private async handleEnablePhysics(args: any[]): Promise<void> {
    const [entityId, options] = args;
    console.log(`启用物理: entity: ${entityId}`, options);

    try {
      const entity = this.findEntityById(entityId);
      if (entity) {
        // 添加或获取物理组件
        let physicsComponent = entity.getComponent('Physics');
        if (!physicsComponent) {
          physicsComponent = entity.addComponent('Physics');
        }

        // 设置物理属性
        if (physicsComponent && options) {
          Object.assign(physicsComponent, options);
        }

        // 调用引擎物理系统
        if (this.engine && (this.engine as any).physicsWorld) {
          (this.engine as any).physicsWorld.addBody(entityId, options);
        }

        // 发出物理事件
        this.emit('physicsEnabled', { entityId, options });
      }
    } catch (error) {
      console.error('启用物理失败:', error);
      throw error;
    }
  }

  private async handleDisablePhysics(args: any[]): Promise<void> {
    const [entityId] = args;
    console.log(`禁用物理: entity: ${entityId}`);

    try {
      const entity = this.findEntityById(entityId);
      if (entity) {
        // 移除物理组件
        const physicsComponent = entity.getComponent('Physics');
        if (physicsComponent && typeof physicsComponent.setEnabled === 'function') {
          physicsComponent.setEnabled(false);
        }

        // 调用引擎物理系统
        if (this.engine && (this.engine as any).physicsWorld) {
          (this.engine as any).physicsWorld.removeBody(entityId);
        }

        // 发出物理事件
        this.emit('physicsDisabled', { entityId });
      }
    } catch (error) {
      console.error('禁用物理失败:', error);
      throw error;
    }
  }

  private async handleSetGravity(args: any[]): Promise<void> {
    const [gravity] = args;
    console.log(`设置重力:`, gravity);

    try {
      // 调用引擎物理系统设置重力
      if (this.engine && (this.engine as any).physicsWorld) {
        (this.engine as any).physicsWorld.setGravity(gravity);
      }

      // 发出重力变更事件
      this.emit('gravityChanged', { gravity });
    } catch (error) {
      console.error('设置重力失败:', error);
      throw error;
    }
  }

  // 渲染相关方法实现
  private async handleSetRenderQuality(args: any[]): Promise<void> {
    const [quality] = args;
    console.log(`设置渲染质量: ${quality}`);

    try {
      // 调用引擎渲染器设置质量
      if (this.engine && (this.engine as any).renderer) {
        const renderer = (this.engine as any).renderer;

        // 根据质量等级设置不同的渲染参数
        switch (quality) {
          case 'low':
            renderer.setPixelRatio(0.5);
            renderer.shadowMap.enabled = false;
            break;
          case 'medium':
            renderer.setPixelRatio(0.75);
            renderer.shadowMap.enabled = true;
            break;
          case 'high':
            renderer.setPixelRatio(1.0);
            renderer.shadowMap.enabled = true;
            renderer.antialias = true;
            break;
          case 'ultra':
            renderer.setPixelRatio(window.devicePixelRatio || 1);
            renderer.shadowMap.enabled = true;
            renderer.antialias = true;
            break;
        }
      }

      // 发出渲染质量变更事件
      this.emit('renderQualityChanged', { quality });
    } catch (error) {
      console.error('设置渲染质量失败:', error);
      throw error;
    }
  }

  private async handleEnableShadows(args: any[]): Promise<void> {
    const [enabled] = args;
    console.log(`${enabled ? '启用' : '禁用'}阴影`);

    try {
      // 调用引擎渲染器设置阴影
      if (this.engine && (this.engine as any).renderer) {
        const renderer = (this.engine as any).renderer;
        renderer.shadowMap.enabled = enabled;

        if (enabled) {
          // 设置阴影类型和参数
          renderer.shadowMap.type = 'PCFSoftShadowMap'; // 或其他阴影类型
        }
      }

      // 更新场景中的光源阴影设置
      if (this.activeScene) {
        const entities = this.activeScene.getEntities();
        entities.forEach((entity: Entity) => {
          const lightComponent = entity.getComponent('Light');
          if (lightComponent && typeof lightComponent.setCastShadow === 'function') {
            lightComponent.setCastShadow(enabled);
          }
        });
      }

      // 发出阴影设置变更事件
      this.emit('shadowsChanged', { enabled });
    } catch (error) {
      console.error('设置阴影失败:', error);
      throw error;
    }
  }

  private async handleSetLighting(args: any[]): Promise<void> {
    const [lightingSettings] = args;
    console.log(`设置光照:`, lightingSettings);

    try {
      // 调用引擎光照系统
      if (this.engine && (this.engine as any).lightingSystem) {
        (this.engine as any).lightingSystem.updateSettings(lightingSettings);
      }

      // 更新场景环境光
      if (lightingSettings.ambientLight && this.activeScene) {
        // 查找或创建环境光
        const entities = this.activeScene.getEntities();
        let ambientLightEntity = entities.find((entity: Entity) =>
          entity.name === 'AmbientLight' || entity.hasComponent('AmbientLight')
        );

        if (!ambientLightEntity) {
          ambientLightEntity = this.activeScene.createEntity('AmbientLight');
          ambientLightEntity.addComponent('AmbientLight');
        }

        const ambientComponent = ambientLightEntity.getComponent('AmbientLight');
        if (ambientComponent) {
          Object.assign(ambientComponent, lightingSettings.ambientLight);
        }
      }

      // 更新方向光
      if (lightingSettings.directionalLight && this.activeScene) {
        const entities = this.activeScene.getEntities();
        let dirLightEntity = entities.find((entity: Entity) =>
          entity.name === 'DirectionalLight' || entity.hasComponent('DirectionalLight')
        );

        if (!dirLightEntity) {
          dirLightEntity = this.activeScene.createEntity('DirectionalLight');
          dirLightEntity.addComponent('DirectionalLight');
        }

        const dirComponent = dirLightEntity.getComponent('DirectionalLight');
        if (dirComponent) {
          Object.assign(dirComponent, lightingSettings.directionalLight);
        }
      }

      // 发出光照设置变更事件
      this.emit('lightingChanged', { lightingSettings });
    } catch (error) {
      console.error('设置光照失败:', error);
      throw error;
    }
  }

  /**
   * 序列化场景为数据
   * @returns 场景数据
   */
  private async serializeScene(): Promise<any> {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    try {
      const entities = this.activeScene.getEntities();

      const sceneData = {
        id: this.activeScene.id,
        name: this.activeScene.name,
        version: '1.0.0',
        timestamp: Date.now(),
        entities: await Promise.all(entities.map(entity => this.serializeEntity(entity))),
        settings: await this.serializeSceneSettings(),
        environment: await this.serializeEnvironmentSettings()
      };

      return sceneData;
    } catch (error) {
      console.error('序列化场景失败:', error);
      throw error;
    }
  }

  /**
   * 序列化实体
   * @param entity 实体
   * @returns 实体数据
   */
  private async serializeEntity(entity: Entity): Promise<any> {
    const transform = entity.getTransform();
    const components = entity.getComponents();

    const entityData = {
      id: entity.id,
      name: entity.name,
      active: entity.isActive(),
      transform: {
        position: transform ? {
          x: transform.getPosition().x,
          y: transform.getPosition().y,
          z: transform.getPosition().z
        } : { x: 0, y: 0, z: 0 },
        rotation: transform ? {
          x: transform.getRotation().x,
          y: transform.getRotation().y,
          z: transform.getRotation().z
        } : { x: 0, y: 0, z: 0 },
        scale: transform ? {
          x: transform.getScale().x,
          y: transform.getScale().y,
          z: transform.getScale().z
        } : { x: 1, y: 1, z: 1 }
      },
      components: Array.from(components.entries()).map(([type, component]) =>
        this.serializeComponent(type, component)
      )
    };

    return entityData;
  }

  /**
   * 序列化组件
   * @param type 组件类型
   * @param component 组件实例
   * @returns 组件数据
   */
  private serializeComponent(type: string, component: any): any {
    const componentData: any = {
      type,
      enabled: component && 'isEnabled' in component ? component.isEnabled() : true,
      properties: {}
    };

    // 序列化组件属性
    if (component && typeof component === 'object') {
      // 获取可序列化的属性
      const serializableProps = this.getSerializableProperties(component);
      for (const prop of serializableProps) {
        if (prop in component) {
          componentData.properties[prop] = this.serializeValue(component[prop]);
        }
      }
    }

    return componentData;
  }

  /**
   * 获取可序列化的属性列表
   * @param component 组件
   * @returns 属性名数组
   */
  private getSerializableProperties(component: any): string[] {
    // 根据组件类型返回可序列化的属性
    const commonProps = ['enabled', 'visible', 'color', 'material', 'texture'];

    // 这里可以根据具体的组件类型返回不同的属性列表
    if (component.constructor && component.constructor.name) {
      const componentType = component.constructor.name;

      switch (componentType) {
        case 'MeshRenderer':
          return ['material', 'castShadow', 'receiveShadow', 'visible'];
        case 'Light':
          return ['color', 'intensity', 'range', 'spotAngle'];
        case 'Camera':
          return ['fov', 'near', 'far', 'clearColor'];
        case 'AudioSource':
          return ['clip', 'volume', 'loop', 'autoPlay'];
        default:
          return commonProps;
      }
    }

    return commonProps;
  }

  /**
   * 序列化值
   * @param value 值
   * @returns 序列化后的值
   */
  private serializeValue(value: any): any {
    if (value === null || value === undefined) {
      return value;
    }

    // 处理基本类型
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      return value;
    }

    // 处理数组
    if (Array.isArray(value)) {
      return value.map(item => this.serializeValue(item));
    }

    // 处理Vector3等特殊对象
    if (value && typeof value === 'object') {
      if ('x' in value && 'y' in value && 'z' in value) {
        return { x: value.x, y: value.y, z: value.z };
      }

      if ('r' in value && 'g' in value && 'b' in value) {
        return { r: value.r, g: value.g, b: value.b, a: value.a || 1 };
      }

      // 处理普通对象
      const serialized: any = {};
      for (const [key, val] of Object.entries(value)) {
        if (typeof val !== 'function') {
          serialized[key] = this.serializeValue(val);
        }
      }
      return serialized;
    }

    return value;
  }

  /**
   * 序列化场景设置
   * @returns 场景设置数据
   */
  private async serializeSceneSettings(): Promise<any> {
    return {
      backgroundColor: '#000000',
      fog: {
        enabled: false,
        color: '#ffffff',
        near: 1,
        far: 1000
      },
      physics: {
        enabled: true,
        gravity: { x: 0, y: -9.81, z: 0 }
      }
    };
  }

  /**
   * 序列化环境设置
   * @returns 环境设置数据
   */
  private async serializeEnvironmentSettings(): Promise<any> {
    return {
      lighting: {
        ambientColor: '#404040',
        ambientIntensity: 0.4
      },
      skybox: {
        type: 'color',
        color: '#87CEEB'
      },
      audio: {
        masterVolume: 1.0,
        musicVolume: 0.8,
        effectsVolume: 1.0
      }
    };
  }

  /**
   * 从数据加载场景内容
   * @param sceneData 场景数据
   */
  private async loadSceneFromData(sceneData: any): Promise<void> {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    try {
      console.log('开始加载场景数据:', sceneData);

      // 设置场景基本信息
      if (sceneData.name) {
        this.activeScene.name = sceneData.name;
      }

      // 加载实体
      if (sceneData.entities && Array.isArray(sceneData.entities)) {
        for (const entityData of sceneData.entities) {
          await this.loadEntityFromData(entityData);
        }
      }

      // 加载场景设置
      if (sceneData.settings) {
        await this.loadSceneSettings(sceneData.settings);
      }

      // 加载环境设置
      if (sceneData.environment) {
        await this.loadEnvironmentSettings(sceneData.environment);
      }

      console.log('场景数据加载完成');
    } catch (error) {
      console.error('加载场景数据失败:', error);
      throw error;
    }
  }

  /**
   * 从数据加载实体
   * @param entityData 实体数据
   */
  private async loadEntityFromData(entityData: any): Promise<Entity> {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    const entity = this.activeScene.createEntity(entityData.name || 'Entity');

    // 设置实体属性
    if (entityData.id) {
      entity.id = entityData.id;
    }

    // 加载组件
    if (entityData.components && Array.isArray(entityData.components)) {
      for (const componentData of entityData.components) {
        await this.loadComponentFromData(entity, componentData);
      }
    }

    // 设置变换
    if (entityData.transform) {
      const transform = entity.getTransform();
      if (entityData.transform.position) {
        const pos = entityData.transform.position;
        transform.setPosition(new Vector3(pos.x || 0, pos.y || 0, pos.z || 0));
      }
      if (entityData.transform.rotation) {
        const rot = entityData.transform.rotation;
        transform.setRotation(rot.x || 0, rot.y || 0, rot.z || 0);
      }
      if (entityData.transform.scale) {
        const scale = entityData.transform.scale;
        transform.setScale(new Vector3(scale.x || 1, scale.y || 1, scale.z || 1));
      }
    }

    // 设置父子关系
    if (entityData.parentId) {
      const parentEntity = this.findEntityById(entityData.parentId);
      if (parentEntity) {
        entity.setParent(parentEntity);
      }
    }

    return entity;
  }

  /**
   * 从数据加载组件
   * @param entity 实体
   * @param componentData 组件数据
   */
  private async loadComponentFromData(entity: Entity, componentData: any): Promise<void> {
    const component = entity.addComponent(componentData.type);

    // 设置组件属性
    if (componentData.properties) {
      for (const [key, value] of Object.entries(componentData.properties)) {
        if (component && typeof component === 'object' && key in component) {
          (component as any)[key] = value;
        }
      }
    }

    // 设置组件启用状态
    if (typeof componentData.enabled === 'boolean' && component && 'setEnabled' in component) {
      (component as any).setEnabled(componentData.enabled);
    }
  }

  /**
   * 加载场景设置
   * @param settings 场景设置
   */
  private async loadSceneSettings(settings: any): Promise<void> {
    console.log('加载场景设置:', settings);

    // 设置背景颜色
    if (settings.backgroundColor) {
      // 这里可以设置场景背景颜色
      console.log('设置背景颜色:', settings.backgroundColor);
    }

    // 设置雾效
    if (settings.fog) {
      console.log('设置雾效:', settings.fog);
    }

    // 设置物理参数
    if (settings.physics) {
      console.log('设置物理参数:', settings.physics);
    }
  }

  /**
   * 加载环境设置
   * @param environment 环境设置
   */
  private async loadEnvironmentSettings(environment: any): Promise<void> {
    console.log('加载环境设置:', environment);

    // 设置光照
    if (environment.lighting) {
      console.log('设置光照:', environment.lighting);
    }

    // 设置天空盒
    if (environment.skybox) {
      console.log('设置天空盒:', environment.skybox);
    }

    // 设置环境音效
    if (environment.audio) {
      console.log('设置环境音效:', environment.audio);
    }
  }

  /**
   * 设置语言
   * @param language 语言代码 (如 'zh-CN', 'en-US')
   */
  public setLanguage(language: string): void {
    if (this.engine && this.engine.getI18n && typeof this.engine.getI18n().setLanguage === 'function') {
      this.engine.getI18n().setLanguage(language);
    }
  }

  /**
   * 获取当前语言
   * @returns 当前语言代码
   */
  public getLanguage(): string {
    if (this.engine && this.engine.getI18n && typeof this.engine.getI18n().getLanguage === 'function') {
      return this.engine.getI18n().getLanguage();
    }
    return 'zh-CN';
  }

  /**
   * 翻译文本
   * @param key 翻译键
   * @param params 参数
   * @returns 翻译后的文本
   */
  public translate(key: string, params?: any): string {
    if (this.engine && this.engine.getI18n && typeof this.engine.getI18n().translate === 'function') {
      return this.engine.getI18n().translate(key, params);
    }
    return key;
  }

  /**
   * 启用调试模式
   * @param enabled 是否启用
   */
  public setDebugMode(enabled: boolean): void {
    if (this.engine && typeof this.engine.setDebug === 'function') {
      this.engine.setDebug(enabled);
    }
  }

  /**
   * 是否处于调试模式
   * @returns 是否调试模式
   */
  public isDebugMode(): boolean {
    if (this.engine && typeof this.engine.isDebug === 'function') {
      return this.engine.isDebug();
    }
    return false;
  }

  /**
   * 获取引擎性能信息
   * @returns 性能信息
   */
  public getPerformanceInfo(): any {
    const timeInfo = this.getTime();
    return {
      fps: timeInfo.getFPS(),
      frameCount: timeInfo.getFrameCount(),
      deltaTime: timeInfo.getDeltaTime(),
      timeScale: timeInfo.getTimeScale(),
      entityCount: this.activeScene ? this.activeScene.getEntities().length : 0,
      systemCount: this.engine ? this.engine.systems.length : 0,
      memoryUsage: (performance as any).memory ? {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
      } : null
    };
  }

  /**
   * 获取引擎状态信息
   * @returns 状态信息
   */
  public getEngineStatus(): any {
    return {
      initialized: this.engine !== null,
      running: this.engine ? this.engine.isRunning() : false,
      activeScene: this.activeScene ? this.activeScene.name : null,
      activeCamera: this.activeCamera !== null,
      selectedEntities: this.selectedEntities.length,
      systems: {
        physics: this.physicsSystem !== null,
        animation: this.animationSystem !== null,
        audio: this.audioSystem !== null,
        input: this.inputSystem !== null,
        network: this.networkSystem !== null,
        ui: this.uiSystem !== null,
        material: this.materialSystem !== null,
        render: this.renderSystem !== null,
        terrain: this.terrainSystem !== null,
        water: this.waterSystem !== null,
        skybox: this.skyboxSystem !== null,
        particle: this.particleSystem !== null,
        postProcessing: this.postProcessingSystem !== null,
        lod: this.lodSystem !== null,
        weather: this.weatherSystem !== null,
        environment: this.environmentSystem !== null,
        ai: this.aiSystem !== null,
        motionCapture: this.motionCaptureSystem !== null,
        interaction: this.interactionSystem !== null,
        vegetation: this.vegetationSystem !== null,
        enhancedTerrain: this.enhancedTerrainSystem !== null,
        performanceMonitor: this.performanceMonitorSystem !== null,
        sceneOptimization: this.sceneOptimizationSystem !== null,
        mediaStream: this.mediaStreamSystem !== null
      },
      managers: {
        asset: this.assetManager !== null,
        scene: this.sceneManager !== null,
        resource: this.resourceManager !== null,
        materialFactory: this.materialFactory !== null,
        webRTC: this.webRTCManager !== null,
        webSocket: this.webSocketManager !== null,
        gamepad: this.gamepadManager !== null,
        touch: this.touchManager !== null,
        sceneTransition: this.sceneTransitionManager !== null
      },
      debugTools: {
        physicsDebugger: this.physicsDebugger !== null
      },
      engines: {
        visualScript: this.visualScriptEngine !== null
      },
      devices: {
        xr: this.xrDevice !== null
      }
    };
  }

  /**
   * 重置引擎服务
   */
  public reset(): void {
    // 停止引擎
    if (this.engine && this.engine.isRunning()) {
      this.engine.stop();
    }

    // 清空场景
    if (this.activeScene) {
      this.activeScene.clear();
    }

    // 清空选择
    this.clearSelection();

    // 重置变换模式
    this.transformMode = TransformMode.TRANSLATE;
    this.transformSpace = TransformSpace.LOCAL;

    console.log('引擎服务已重置');
  }

  /**
   * 获取系统信息
   * @param systemType 系统类型
   * @returns 系统信息
   */
  public getSystemInfo(systemType: string): any {
    const systemMap: { [key: string]: any } = {
      'physics': this.physicsSystem,
      'animation': this.animationSystem,
      'audio': this.audioSystem,
      'input': this.inputSystem,
      'network': this.networkSystem,
      'ui': this.uiSystem,
      'material': this.materialSystem,
      'render': this.renderSystem
    };

    const system = systemMap[systemType];
    if (!system) {
      return null;
    }

    return {
      type: systemType,
      enabled: typeof system.isEnabled === 'function' ? system.isEnabled() : true,
      priority: typeof system.getPriority === 'function' ? system.getPriority() : 0,
      initialized: system !== null
    };
  }

  /**
   * 根据ID查找实体
   * @param id 实体ID
   * @returns 实体或null
   */
  private findEntityById(id: string): Entity | null {
    if (!this.activeScene) {
      return null;
    }

    const entities = this.activeScene.getEntities();
    return entities.find((entity: Entity) => entity.id === id) || null;
  }
}

export default EngineService.getInstance();
