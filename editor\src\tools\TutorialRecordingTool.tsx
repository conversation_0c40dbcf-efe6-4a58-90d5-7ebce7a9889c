/**
 * 视频教程录制辅助工具
 * 帮助录制者按照脚本和规范进行录制
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Button,
  Tabs,
  Typography,
  Space,
  Divider,
  List,
  Tag,
  Select,
  Slider,
  Switch,
  Modal,
  Progress,
  message,
  Input
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepForwardOutlined,
  StepBackwardOutlined,
  AudioOutlined,
  FileTextOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './TutorialRecordingTool.less';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

// 脚本章节接口
interface ScriptChapter {
  id: string;
  title: string;
  duration: number; // 预计时长（秒）
  content: string; // 旁白文本
  sceneDescription: string; // 画面描述
  keyPoints: string[]; // 关键点
}

// 录制状态
enum RecordingState {
  Idle = 'idle',
  Preparing = 'preparing',
  Recording = 'recording',
  Paused = 'paused',
  Completed = 'completed'
}

// 音频级别
interface AudioLevel {
  timestamp: number;
  level: number; // 0-100
}

// 录制会话
interface RecordingSession {
  id: string;
  tutorialId: string;
  chapterId: string;
  startTime: number;
  endTime: number | null;
  duration: number;
  audioLevels: AudioLevel[];
  notes: string;
}

// 工具属性
interface TutorialRecordingToolProps {
  tutorialId: string;
  tutorialTitle: string;
  onClose?: () => void;
}

/**
 * 视频教程录制辅助工具组件
 */
const TutorialRecordingTool: React.FC<TutorialRecordingToolProps> = ({ 
  tutorialId, 
  tutorialTitle, 
  onClose 
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('script');
  const [recordingState, setRecordingState] = useState<RecordingState>(RecordingState.Idle);
  const [currentChapterIndex, setCurrentChapterIndex] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [audioLevel, setAudioLevel] = useState(0);
  const [recordingSession, setRecordingSession] = useState<RecordingSession | null>(null);
  const [notes, setNotes] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [countdownValue, setCountdownValue] = useState(3);
  const [isCountingDown, setIsCountingDown] = useState(false);
  const [countdown, setCountdown] = useState(3);
  const [showAudioWarning, setShowAudioWarning] = useState(false);
  const [audioLevels, setAudioLevels] = useState<AudioLevel[]>([]);
  
  // 模拟脚本数据
  const [script] = useState<ScriptChapter[]>([
    {
      id: 'introduction',
      title: '介绍',
      duration: 60,
      content: '欢迎来到DL（Digital Learning）引擎编辑器视频教程。在本教程中，我们将学习如何使用编辑器的基本功能。无论您是初学者还是有经验的开发者，本教程都将帮助您快速上手DL（Digital Learning）引擎编辑器。',
      sceneDescription: 'DL（Digital Learning）引擎编辑器启动画面，然后过渡到编辑器主界面。',
      keyPoints: ['视频目标：快速入门DL（Digital Learning）引擎编辑器', '编辑器用途：创建3D应用', '前提条件：安装最新版本']
    },
    {
      id: 'interface-overview',
      title: '界面概览',
      duration: 120,
      content: '让我们先来了解一下DL（Digital Learning）引擎编辑器的界面布局。编辑器界面分为几个主要区域，每个区域都有特定的功能。顶部是菜单栏和工具栏。中央区域是场景视图，这是您创建和编辑3D场景的主要工作区。左侧是层次面板，显示场景中所有对象的层次结构。下方是资产面板，用于管理项目中的所有资源。右侧是属性面板，显示当前选中对象的属性。底部是状态栏，显示当前编辑器状态和有用信息。',
      sceneDescription: '展示编辑器主界面，依次高亮各个主要区域。',
      keyPoints: ['菜单栏和工具栏：顶部，包含所有功能', '场景视图：中央，主要工作区', '层次面板：左侧，管理对象层次结构', '资产面板：下方，管理项目资源', '属性面板：右侧，编辑对象属性', '状态栏：底部，显示状态信息']
    },
    {
      id: 'basic-navigation',
      title: '基本导航',
      duration: 120,
      content: '现在，让我们学习如何在场景中导航。熟练的导航操作是高效使用编辑器的基础。要旋转视图，按住鼠标右键并拖动。要平移视图，按住鼠标中键或按住Alt键加鼠标左键，然后拖动。要缩放视图，滚动鼠标滚轮向上或向下。如果您想快速聚焦到某个对象，只需在场景或层次面板中选择该对象，然后按F键。编辑器还提供了多种预设视图。您可以使用数字键快速切换：1键为前视图，3键为右视图，7键为顶视图，5键可以在透视视图和正交视图之间切换。',
      sceneDescription: '演示在场景视图中的各种导航操作。',
      keyPoints: ['旋转视图：鼠标右键拖动', '平移视图：鼠标中键或Alt+鼠标左键拖动', '缩放视图：鼠标滚轮', '聚焦对象：选中对象后按F键', '预设视图：数字键1、3、7、5']
    }
  ]);
  
  // 定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);
  const audioAnalyserRef = useRef<AnalyserNode | null>(null);
  const audioStreamRef = useRef<MediaStream | null>(null);
  
  // 当前章节
  const currentChapter = script[currentChapterIndex];
  
  // 模拟音频分析
  useEffect(() => {
    if (recordingState === RecordingState.Recording) {
      // 模拟音频级别变化
      const audioTimer = setInterval(() => {
        // 生成0-100之间的随机值，模拟音频级别
        const newLevel = Math.floor(Math.random() * 100);
        setAudioLevel(newLevel);
        
        // 记录音频级别
        const newAudioLevel: AudioLevel = {
          timestamp: Date.now(),
          level: newLevel
        };
        setAudioLevels(prev => [...prev, newAudioLevel]);
        
        // 检查音频级别是否过低或过高
        if (newLevel < 10 || newLevel > 90) {
          setShowAudioWarning(true);
        } else {
          setShowAudioWarning(false);
        }
      }, 500);
      
      return () => clearInterval(audioTimer);
    } else {
      setAudioLevel(0);
      setShowAudioWarning(false);
    }
  }, [recordingState]);
  
  // 计时器
  useEffect(() => {
    if (recordingState === RecordingState.Recording) {
      timerRef.current = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [recordingState]);
  
  // 倒计时
  useEffect(() => {
    if (isCountingDown && countdown > 0) {
      countdownTimerRef.current = setTimeout(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    } else if (isCountingDown && countdown === 0) {
      setIsCountingDown(false);
      startRecording();
    }
    
    return () => {
      if (countdownTimerRef.current) {
        clearTimeout(countdownTimerRef.current);
        countdownTimerRef.current = null;
      }
    };
  }, [isCountingDown, countdown]);
  
  // 准备录制
  const prepareRecording = () => {
    setRecordingState(RecordingState.Preparing);
    setIsCountingDown(true);
    setCountdown(countdownValue);
    
    // 请求音频权限
    navigator.mediaDevices.getUserMedia({ audio: true })
      .then(stream => {
        audioStreamRef.current = stream;
        
        // 创建音频分析器
        const audioContext = new AudioContext();
        const analyser = audioContext.createAnalyser();
        const microphone = audioContext.createMediaStreamSource(stream);
        microphone.connect(analyser);
        analyser.fftSize = 256;
        audioAnalyserRef.current = analyser;
        
        message.success(t('tutorialRecording.audioPermissionGranted'));
      })
      .catch(error => {
        console.error('Error accessing microphone:', error);
        message.error(t('tutorialRecording.audioPermissionDenied'));
        setIsCountingDown(false);
        setRecordingState(RecordingState.Idle);
      });
  };
  
  // 开始录制
  const startRecording = () => {
    setRecordingState(RecordingState.Recording);
    setElapsedTime(0);
    setAudioLevels([]);
    
    // 创建新的录制会话
    const newSession: RecordingSession = {
      id: `session_${Date.now()}`,
      tutorialId,
      chapterId: currentChapter.id,
      startTime: Date.now(),
      endTime: null,
      duration: 0,
      audioLevels: [],
      notes: ''
    };
    setRecordingSession(newSession);
    
    message.success(t('tutorialRecording.recordingStarted'));
  };
  
  // 暂停录制
  const pauseRecording = () => {
    setRecordingState(RecordingState.Paused);
    message.info(t('tutorialRecording.recordingPaused'));
  };
  
  // 继续录制
  const resumeRecording = () => {
    setRecordingState(RecordingState.Recording);
    message.info(t('tutorialRecording.recordingResumed'));
  };
  
  // 停止录制
  const stopRecording = () => {
    if (recordingSession) {
      // 更新录制会话
      const updatedSession: RecordingSession = {
        ...recordingSession,
        endTime: Date.now(),
        duration: elapsedTime,
        audioLevels,
        notes
      };
      setRecordingSession(updatedSession);
      
      // 保存录制会话（这里只是模拟）
      console.log('Recording session saved:', updatedSession);
      message.success(t('tutorialRecording.recordingSaved'));
    }
    
    // 停止音频流
    if (audioStreamRef.current) {
      audioStreamRef.current.getTracks().forEach(track => track.stop());
      audioStreamRef.current = null;
    }
    
    setRecordingState(RecordingState.Completed);
  };
  
  // 重置录制
  const resetRecording = () => {
    setRecordingState(RecordingState.Idle);
    setElapsedTime(0);
    setAudioLevels([]);
    setNotes('');
    setRecordingSession(null);
    
    // 停止音频流
    if (audioStreamRef.current) {
      audioStreamRef.current.getTracks().forEach(track => track.stop());
      audioStreamRef.current = null;
    }
    
    message.info(t('tutorialRecording.recordingReset'));
  };
  
  // 下一章节
  const nextChapter = () => {
    if (currentChapterIndex < script.length - 1) {
      setCurrentChapterIndex(currentChapterIndex + 1);
      resetRecording();
    }
  };
  
  // 上一章节
  const prevChapter = () => {
    if (currentChapterIndex > 0) {
      setCurrentChapterIndex(currentChapterIndex - 1);
      resetRecording();
    }
  };
  
  // 格式化时间
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };
  
  // 渲染录制控制按钮
  const renderRecordingControls = () => {
    switch (recordingState) {
      case RecordingState.Idle:
        return (
          <Button 
            type="primary" 
            icon={<PlayCircleOutlined />} 
            onClick={prepareRecording}
          >
            {t('tutorialRecording.startRecording')}
          </Button>
        );
      case RecordingState.Preparing:
        return (
          <div className="countdown-container">
            <div className="countdown">{countdown}</div>
            <Button 
              danger 
              onClick={() => {
                setIsCountingDown(false);
                setRecordingState(RecordingState.Idle);
              }}
            >
              {t('tutorialRecording.cancel')}
            </Button>
          </div>
        );
      case RecordingState.Recording:
        return (
          <Space>
            <Button 
              icon={<PauseCircleOutlined />} 
              onClick={pauseRecording}
            >
              {t('tutorialRecording.pause')}
            </Button>
            <Button 
              type="primary" 
              danger 
              onClick={stopRecording}
            >
              {t('tutorialRecording.stop')}
            </Button>
          </Space>
        );
      case RecordingState.Paused:
        return (
          <Space>
            <Button 
              type="primary" 
              icon={<PlayCircleOutlined />} 
              onClick={resumeRecording}
            >
              {t('tutorialRecording.resume')}
            </Button>
            <Button 
              danger 
              onClick={stopRecording}
            >
              {t('tutorialRecording.stop')}
            </Button>
          </Space>
        );
      case RecordingState.Completed:
        return (
          <Space>
            <Button 
              type="primary" 
              icon={<PlayCircleOutlined />} 
              onClick={prepareRecording}
            >
              {t('tutorialRecording.recordAgain')}
            </Button>
            <Button 
              onClick={resetRecording}
            >
              {t('tutorialRecording.reset')}
            </Button>
          </Space>
        );
      default:
        return null;
    }
  };
  
  return (
    <div className="tutorial-recording-tool">
      <Card
        title={
          <Space>
            <AudioOutlined />
            {t('tutorialRecording.title')}
          </Space>
        }
        extra={
          <Space>
            <Button 
              icon={<SettingOutlined />} 
              onClick={() => setShowSettings(true)}
            >
              {t('tutorialRecording.settings')}
            </Button>
            {onClose && (
              <Button onClick={onClose}>
                {t('common.close')}
              </Button>
            )}
          </Space>
        }
      >
        <div className="tutorial-info">
          <Title level={4}>{tutorialTitle}</Title>
          <div className="chapter-navigation">
            <Button 
              icon={<StepBackwardOutlined />} 
              onClick={prevChapter}
              disabled={currentChapterIndex === 0 || recordingState === RecordingState.Recording}
            >
              {t('tutorialRecording.prevChapter')}
            </Button>
            <Text strong>
              {t('tutorialRecording.chapter')} {currentChapterIndex + 1}/{script.length}: {currentChapter.title}
            </Text>
            <Button 
              icon={<StepForwardOutlined />} 
              onClick={nextChapter}
              disabled={currentChapterIndex === script.length - 1 || recordingState === RecordingState.Recording}
            >
              {t('tutorialRecording.nextChapter')}
            </Button>
          </div>
        </div>
        
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <FileTextOutlined />
                {t('tutorialRecording.script')}
              </span>
            } 
            key="script"
          >
            <div className="script-content">
              <div className="script-section">
                <Title level={5}>{t('tutorialRecording.sceneDescription')}</Title>
                <Paragraph>{currentChapter.sceneDescription}</Paragraph>
              </div>
              
              <div className="script-section">
                <Title level={5}>{t('tutorialRecording.narration')}</Title>
                <Paragraph>{currentChapter.content}</Paragraph>
              </div>
              
              <div className="script-section">
                <Title level={5}>{t('tutorialRecording.keyPoints')}</Title>
                <List
                  dataSource={currentChapter.keyPoints}
                  renderItem={item => (
                    <List.Item>
                      <Text>• {item}</Text>
                    </List.Item>
                  )}
                />
              </div>
            </div>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <AudioOutlined />
                {t('tutorialRecording.recording')}
              </span>
            } 
            key="recording"
          >
            <div className="recording-controls">
              <div className="recording-status">
                <div className="status-indicator">
                  {recordingState === RecordingState.Recording && (
                    <Tag color="red" icon={<AudioOutlined />}>
                      {t('tutorialRecording.recording')}
                    </Tag>
                  )}
                  {recordingState === RecordingState.Paused && (
                    <Tag color="orange" icon={<PauseCircleOutlined />}>
                      {t('tutorialRecording.paused')}
                    </Tag>
                  )}
                  {recordingState === RecordingState.Completed && (
                    <Tag color="green" icon={<CheckCircleOutlined />}>
                      {t('tutorialRecording.completed')}
                    </Tag>
                  )}
                </div>
                
                <div className="time-display">
                  <ClockCircleOutlined /> {formatTime(elapsedTime)} / {formatTime(currentChapter.duration)}
                </div>
              </div>
              
              <div className="audio-meter">
                <Progress 
                  percent={audioLevel} 
                  status={
                    audioLevel < 10 ? "exception" : 
                    audioLevel > 90 ? "exception" : "active"
                  }
                  showInfo={false}
                />
                <div className="audio-level-text">
                  <Text>{audioLevel}%</Text>
                  {showAudioWarning && (
                    <Text type="danger">
                      <WarningOutlined /> {
                        audioLevel < 10 ? 
                        t('tutorialRecording.audioTooLow') : 
                        t('tutorialRecording.audioTooHigh')
                      }
                    </Text>
                  )}
                </div>
              </div>
              
              <div className="recording-buttons">
                {renderRecordingControls()}
              </div>
              
              <Divider />
              
              <div className="recording-notes">
                <Title level={5}>{t('tutorialRecording.notes')}</Title>
                <TextArea
                  rows={4}
                  value={notes}
                  onChange={e => setNotes(e.target.value)}
                  placeholder={t('tutorialRecording.notesPlaceholder') || '添加录制备注...'}
                  disabled={recordingState === RecordingState.Recording}
                />
              </div>
            </div>
          </TabPane>
        </Tabs>
      </Card>
      
      <Modal
        title={t('tutorialRecording.settings')}
        open={showSettings}
        onCancel={() => setShowSettings(false)}
        onOk={() => setShowSettings(false)}
      >
        <div className="settings-content">
          <div className="setting-item">
            <Text strong>{t('tutorialRecording.countdownDuration')}</Text>
            <Slider
              min={1}
              max={10}
              value={countdownValue}
              onChange={value => setCountdownValue(value)}
              marks={{
                1: '1s',
                3: '3s',
                5: '5s',
                10: '10s'
              }}
            />
          </div>
          
          <div className="setting-item">
            <Text strong>{t('tutorialRecording.audioMonitoring')}</Text>
            <Switch defaultChecked />
          </div>
          
          <div className="setting-item">
            <Text strong>{t('tutorialRecording.audioDevice')}</Text>
            <Select style={{ width: '100%' }} defaultValue="default">
              <Option value="default">{t('tutorialRecording.defaultDevice')}</Option>
              <Option value="device1">Microphone (USB Audio Device)</Option>
              <Option value="device2">Headset Microphone</Option>
            </Select>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default TutorialRecordingTool;
