/**
 * 季节性植物类
 * 用于实现季节性植物类型
 */
import * as THREE from 'three';
import { SeasonType } from '../ecosystem/EcosystemSimulationSystem';
import { VegetationInstanceData } from '../growth/VegetationGrowthSystem';
/**
 * 季节性植物类型
 */
export declare enum SeasonalVegetationType {
    /** 春季花卉 */
    SPRING_FLOWER = "spring_flower",
    /** 夏季花卉 */
    SUMMER_FLOWER = "summer_flower",
    /** 秋季花卉 */
    AUTUMN_FLOWER = "autumn_flower",
    /** 冬季花卉 */
    WINTER_FLOWER = "winter_flower",
    /** 落叶乔木 */
    DECIDUOUS_TREE = "deciduous_tree",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 季节性植物配置
 */
export interface SeasonalVegetationConfig {
    /** 类型 */
    type: SeasonalVegetationType;
    /** 最大高度 */
    maxHeight?: number;
    /** 最大宽度 */
    maxWidth?: number;
    /** 最大寿命（天） */
    maxAge?: number;
    /** 生长速度 */
    growthRate?: number;
    /** 活跃季节 */
    activeSeasons?: SeasonType[];
    /** 休眠季节 */
    dormantSeasons?: SeasonType[];
    /** 开花季节 */
    floweringSeason?: SeasonType[];
    /** 结果季节 */
    fruitingSeason?: SeasonType[];
    /** 落叶季节 */
    leafFallSeason?: SeasonType[];
    /** 是否常绿 */
    isEvergreen?: boolean;
    /** 温度范围 */
    temperatureRange?: {
        min: number;
        max: number;
    };
    /** 光照要求 */
    lightRequirement?: number;
    /** 水分要求 */
    moistureRequirement?: number;
    /** 营养要求 */
    nutrientRequirement?: number;
    /** 自定义数据 */
    userData?: any;
}
/**
 * 季节性植物类
 */
export declare class SeasonalVegetation {
    /** 配置 */
    private config;
    /** 默认配置映射 */
    private static readonly DEFAULT_CONFIGS;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config: SeasonalVegetationConfig);
    /**
     * 获取配置
     * @returns 配置
     */
    getConfig(): SeasonalVegetationConfig;
    /**
     * 创建实例数据
     * @param id 实例ID
     * @param position 位置
     * @param rotation 旋转
     * @param scale 缩放
     * @returns 实例数据
     */
    createInstanceData(id: string, position: THREE.Vector3, rotation: THREE.Euler, scale: THREE.Vector3): VegetationInstanceData;
    /**
     * 更新实例数据
     * @param instanceData 实例数据
     * @param season 当前季节
     * @param temperature 温度
     * @param lightIntensity 光照强度
     * @param moisture 水分
     * @param nutrient 营养
     * @param deltaTime 帧间隔时间（秒）
     */
    updateInstanceData(instanceData: VegetationInstanceData, season: SeasonType, temperature: number, lightIntensity: number, moisture: number, nutrient: number, deltaTime: number): void;
    /**
     * 更新季节性特征
     * @param instanceData 实例数据
     * @param season 当前季节
     */
    private updateSeasonalFeatures;
    /**
     * 创建季节性植物模型
     * @param type 季节性植物类型
     * @param season 当前季节
     * @returns 模型
     */
    static createModel(type: SeasonalVegetationType, season: SeasonType): THREE.Object3D;
    /**
     * 创建春季花卉模型
     * @param season 当前季节
     * @returns 春季花卉模型
     */
    private static createSpringFlowerModel;
    /**
     * 创建夏季花卉模型
     * @param season 当前季节
     * @returns 夏季花卉模型
     */
    private static createSummerFlowerModel;
    /**
     * 创建秋季花卉模型
     * @param season 当前季节
     * @returns 秋季花卉模型
     */
    private static createAutumnFlowerModel;
    /**
     * 创建冬季花卉模型
     * @param season 当前季节
     * @returns 冬季花卉模型
     */
    private static createWinterFlowerModel;
    /**
     * 创建落叶乔木模型
     * @param season 当前季节
     * @returns 落叶乔木模型
     */
    private static createDeciduousTreeModel;
    /**
     * 创建默认模型
     * @param season 当前季节
     * @returns 默认模型
     */
    private static createDefaultModel;
}
