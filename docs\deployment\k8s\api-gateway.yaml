# API网关部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: dl-engine
  labels:
    app: api-gateway
    component: gateway
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        component: gateway
        version: v1
    spec:
      containers:
      - name: api-gateway
        image: your-registry/dl-engine-api-gateway:latest
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: PORT
          value: "3000"
        - name: CORS_ORIGIN
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: CORS_ORIGIN
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: JWT_EXPIRES_IN
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: JWT_EXPIRES_IN
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        - name: USER_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: USER_SERVICE_HOST
        - name: USER_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: USER_SERVICE_PORT
        - name: PROJECT_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: PROJECT_SERVICE_HOST
        - name: PROJECT_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: PROJECT_SERVICE_PORT
        - name: ASSET_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: ASSET_SERVICE_HOST
        - name: ASSET_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: ASSET_SERVICE_PORT
        - name: RENDER_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: RENDER_SERVICE_HOST
        - name: RENDER_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: RENDER_SERVICE_PORT
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - api-gateway
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: dl-engine
  labels:
    app: api-gateway
    component: gateway
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: api-gateway
---
# API网关的HPA配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: dl-engine
  labels:
    app: api-gateway
    component: gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-gateway-pdb
  namespace: dl-engine
  labels:
    app: api-gateway
    component: gateway
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: api-gateway
---
# ServiceMonitor - Prometheus监控配置
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: api-gateway-monitor
  namespace: dl-engine
  labels:
    app: api-gateway
    component: gateway
spec:
  selector:
    matchLabels:
      app: api-gateway
  endpoints:
  - port: http
    path: /api/metrics
    interval: 30s
    scrapeTimeout: 10s
---
# 网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-gateway-netpol
  namespace: dl-engine
  labels:
    app: api-gateway
    component: gateway
spec:
  podSelector:
    matchLabels:
      app: api-gateway
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from: []  # 允许所有入站流量（通过Ingress控制）
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: dl-engine
    ports:
    - protocol: TCP
      port: 3001  # User Service
    - protocol: TCP
      port: 3002  # Project Service
    - protocol: TCP
      port: 3003  # Asset Service
    - protocol: TCP
      port: 3004  # Render Service
    - protocol: TCP
      port: 3010  # Service Registry
  - to: []  # 允许DNS查询
    ports:
    - protocol: UDP
      port: 53
