/**
 * 混合模式测试
 */
import * as THREE from 'three';
import { AnimationBlender, BlendMode, BlendLayer } from '../../src/animation/AnimationBlender';
import { Animator } from '../../src/animation/Animator';

describe('混合模式测试', () => {
  let scene: THREE.Scene;
  let camera: THREE.PerspectiveCamera;
  let renderer: THREE.WebGLRenderer;
  let clock: THREE.Clock;
  let mixer: THREE.AnimationMixer;
  let animator: Animator;
  let blender: AnimationBlender;
  let model: THREE.Object3D;
  let animations: THREE.AnimationClip[];

  // 在每个测试前设置场景
  beforeEach(() => {
    // 创建场景
    scene = new THREE.Scene();
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    renderer = new THREE.WebGLRenderer();
    clock = new THREE.Clock();

    // 创建模型和动画
    model = new THREE.Group();
    scene.add(model);

    // 创建骨骼
    const skeleton = createTestSkeleton();
    model.add(skeleton.bones[0]); // 添加根骨骼到模型

    // 创建动画
    animations = createTestAnimations(skeleton);

    // 创建混合器
    mixer = new THREE.AnimationMixer(model);

    // 创建动画控制器
    animator = new Animator(mixer, animations);

    // 创建混合器
    blender = new AnimationBlender(animator);
  });

  // 在每个测试后清理场景
  afterEach(() => {
    scene.clear();
    renderer.dispose();
  });

  // 创建测试骨骼
  function createTestSkeleton(): THREE.SkeletonHelper {
    const bones: THREE.Bone[] = [];

    // 创建根骨骼
    const rootBone = new THREE.Bone();
    rootBone.name = 'root';
    bones.push(rootBone);

    // 创建脊柱骨骼
    const spineBone = new THREE.Bone();
    spineBone.name = 'spine';
    spineBone.position.y = 1;
    rootBone.add(spineBone);
    bones.push(spineBone);

    // 创建头部骨骼
    const headBone = new THREE.Bone();
    headBone.name = 'head';
    headBone.position.y = 0.5;
    spineBone.add(headBone);
    bones.push(headBone);

    // 创建左臂骨骼
    const leftArmBone = new THREE.Bone();
    leftArmBone.name = 'leftArm';
    leftArmBone.position.x = 0.5;
    spineBone.add(leftArmBone);
    bones.push(leftArmBone);

    // 创建右臂骨骼
    const rightArmBone = new THREE.Bone();
    rightArmBone.name = 'rightArm';
    rightArmBone.position.x = -0.5;
    spineBone.add(rightArmBone);
    bones.push(rightArmBone);

    // 创建左腿骨骼
    const leftLegBone = new THREE.Bone();
    leftLegBone.name = 'leftLeg';
    leftLegBone.position.x = 0.2;
    leftLegBone.position.y = -1;
    rootBone.add(leftLegBone);
    bones.push(leftLegBone);

    // 创建右腿骨骼
    const rightLegBone = new THREE.Bone();
    rightLegBone.name = 'rightLeg';
    rightLegBone.position.x = -0.2;
    rightLegBone.position.y = -1;
    rootBone.add(rightLegBone);
    bones.push(rightLegBone);

    // 创建骨骼辅助对象
    return new THREE.SkeletonHelper(rootBone);
  }

  // 创建测试动画
  function createTestAnimations(skeleton: THREE.SkeletonHelper): THREE.AnimationClip[] {
    const animations: THREE.AnimationClip[] = [];

    // 创建行走动画
    const walkClip = new THREE.AnimationClip('walk', 1, [
      // 左腿动画
      new THREE.QuaternionKeyframeTrack(
        'leftLeg.quaternion',
        [0, 0.5, 1],
        [
          0, 0, 0, 1, // 初始姿势
          0.2, 0, 0, 0.98, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      ),
      // 右腿动画
      new THREE.QuaternionKeyframeTrack(
        'rightLeg.quaternion',
        [0, 0.5, 1],
        [
          0, 0, 0, 1, // 初始姿势
          -0.2, 0, 0, 0.98, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(walkClip);

    // 创建跑步动画
    const runClip = new THREE.AnimationClip('run', 0.6, [
      // 左腿动画
      new THREE.QuaternionKeyframeTrack(
        'leftLeg.quaternion',
        [0, 0.3, 0.6],
        [
          0, 0, 0, 1, // 初始姿势
          0.4, 0, 0, 0.92, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      ),
      // 右腿动画
      new THREE.QuaternionKeyframeTrack(
        'rightLeg.quaternion',
        [0, 0.3, 0.6],
        [
          0, 0, 0, 1, // 初始姿势
          -0.4, 0, 0, 0.92, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(runClip);

    // 创建挥手动画
    const waveClip = new THREE.AnimationClip('wave', 1.5, [
      // 右臂动画
      new THREE.QuaternionKeyframeTrack(
        'rightArm.quaternion',
        [0, 0.5, 1, 1.5],
        [
          0, 0, 0, 1, // 初始姿势
          0, 0, 0.4, 0.92, // 抬起手臂
          0, 0, -0.4, 0.92, // 挥动
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(waveClip);

    // 创建跳跃动画
    const jumpClip = new THREE.AnimationClip('jump', 1, [
      // 根骨骼动画
      new THREE.VectorKeyframeTrack(
        'root.position',
        [0, 0.5, 1],
        [
          0, 0, 0, // 初始位置
          0, 0.5, 0, // 跳起
          0, 0, 0 // 回到初始位置
        ]
      ),
      // 左腿动画
      new THREE.QuaternionKeyframeTrack(
        'leftLeg.quaternion',
        [0, 0.25, 0.75, 1],
        [
          0, 0, 0, 1, // 初始姿势
          0.3, 0, 0, 0.95, // 弯曲
          0.3, 0, 0, 0.95, // 保持弯曲
          0, 0, 0, 1 // 回到初始姿势
        ]
      ),
      // 右腿动画
      new THREE.QuaternionKeyframeTrack(
        'rightLeg.quaternion',
        [0, 0.25, 0.75, 1],
        [
          0, 0, 0, 1, // 初始姿势
          0.3, 0, 0, 0.95, // 弯曲
          0.3, 0, 0, 0.95, // 保持弯曲
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
    animations.push(jumpClip);

    return animations;
  }

  // 测试覆盖混合模式
  test('覆盖混合模式', () => {
    // 创建混合层
    const layer: BlendLayer = {
      clipName: 'walk',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE
    };

    // 添加混合层
    blender.addLayer(layer);

    // 更新混合器
    blender.update(0.1);

    // 获取动作
    const action = animator.getAction('walk');

    // 验证动作存在
    expect(action).toBeDefined();
    
    // 验证动作权重
    expect(action?.weight).toBe(1.0);
    
    // 验证动作混合模式
    expect(action?.blendMode).toBe(THREE.NormalBlending);
  });

  // 测试叠加混合模式
  test('叠加混合模式', () => {
    // 创建混合层
    const layer1: BlendLayer = {
      clipName: 'walk',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE
    };

    const layer2: BlendLayer = {
      clipName: 'wave',
      weight: 0.8,
      timeScale: 1.0,
      blendMode: BlendMode.ADDITIVE
    };

    // 添加混合层
    blender.addLayer(layer1);
    blender.addLayer(layer2);

    // 更新混合器
    blender.update(0.1);

    // 获取动作
    const walkAction = animator.getAction('walk');
    const waveAction = animator.getAction('wave');

    // 验证动作存在
    expect(walkAction).toBeDefined();
    expect(waveAction).toBeDefined();
    
    // 验证动作权重
    expect(walkAction?.weight).toBe(1.0);
    expect(waveAction?.weight).toBe(0.8);
    
    // 验证动作混合模式
    expect(walkAction?.blendMode).toBe(THREE.NormalBlending);
    expect(waveAction?.blendMode).toBe(THREE.AdditiveBlending);
  });

  // 测试交叉淡入淡出混合模式
  test('交叉淡入淡出混合模式', () => {
    // 创建混合层
    const layer1: BlendLayer = {
      clipName: 'walk',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE
    };

    const layer2: BlendLayer = {
      clipName: 'run',
      weight: 0.5,
      timeScale: 1.0,
      blendMode: BlendMode.CROSS_FADE,
      crossFadeTime: 0.5
    };

    // 添加混合层
    blender.addLayer(layer1);
    blender.addLayer(layer2);

    // 更新混合器
    blender.update(0.1);

    // 获取动作
    const walkAction = animator.getAction('walk');
    const runAction = animator.getAction('run');

    // 验证动作存在
    expect(walkAction).toBeDefined();
    expect(runAction).toBeDefined();
    
    // 验证动作权重
    expect(walkAction?.weight).toBe(1.0);
    expect(runAction?.weight).toBe(0.5);
    
    // 验证动作混合模式
    expect(walkAction?.blendMode).toBe(THREE.NormalBlending);
    expect(runAction?.blendMode).toBe(THREE.NormalBlending);
  });
});
