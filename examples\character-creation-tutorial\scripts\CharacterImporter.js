/**
 * 角色导入器
 * 负责处理角色模型的导入和初始设置
 */
export class CharacterImporter {
  /**
   * 构造函数
   */
  constructor() {
    this.engine = null;
    this.world = null;
    this.scene = null;
    this.character = null;
    this.modelPath = null;
    this.importOptions = {
      scale: 1.0,
      normalizeScale: true,
      centerModel: true,
      importMaterials: true,
      importAnimations: true
    };
    
    // 示例模型列表
    this.sampleModels = [
      {
        name: '男性角色',
        path: '../assets/models/characters/male_character.fbx',
        thumbnail: '../assets/images/thumbnails/male_character.jpg',
        description: '标准男性角色模型，包含基本骨骼结构和材质'
      },
      {
        name: '女性角色',
        path: '../assets/models/characters/female_character.fbx',
        thumbnail: '../assets/images/thumbnails/female_character.jpg',
        description: '标准女性角色模型，包含基本骨骼结构和材质'
      },
      {
        name: '机器人角色',
        path: '../assets/models/characters/robot_character.fbx',
        thumbnail: '../assets/images/thumbnails/robot_character.jpg',
        description: '机器人角色模型，包含特殊关节结构和金属材质'
      }
    ];
  }
  
  /**
   * 初始化
   * @param {Engine} engine 引擎实例
   * @param {World} world 世界实例
   * @param {Scene} scene 场景实例
   */
  initialize(engine, world, scene) {
    this.engine = engine;
    this.world = world;
    this.scene = scene;
  }
  
  /**
   * 使用示例模型
   */
  useSampleModel() {
    // 显示示例模型选择对话框
    this.showSampleModelDialog();
  }
  
  /**
   * 显示示例模型选择对话框
   */
  showSampleModelDialog() {
    // 创建对话框
    const dialog = document.createElement('div');
    dialog.className = 'sample-model-dialog';
    dialog.innerHTML = `
      <div class="dialog-header">
        <h2>选择示例模型</h2>
        <button class="close-button">&times;</button>
      </div>
      <div class="dialog-content">
        <div class="model-grid">
          ${this.sampleModels.map((model, index) => `
            <div class="model-card" data-index="${index}">
              <div class="model-thumbnail" style="background-image: url('${model.thumbnail}')"></div>
              <div class="model-info">
                <h3>${model.name}</h3>
                <p>${model.description}</p>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
      <div class="dialog-footer">
        <button class="cancel-button">取消</button>
        <button class="select-button">选择</button>
      </div>
    `;
    
    // 添加到文档
    document.body.appendChild(dialog);
    
    // 设置事件
    const closeButton = dialog.querySelector('.close-button');
    const cancelButton = dialog.querySelector('.cancel-button');
    const selectButton = dialog.querySelector('.select-button');
    const modelCards = dialog.querySelectorAll('.model-card');
    
    let selectedIndex = -1;
    
    // 关闭对话框
    const closeDialog = () => {
      document.body.removeChild(dialog);
    };
    
    // 选择模型
    const selectModel = (index) => {
      selectedIndex = index;
      modelCards.forEach((card, i) => {
        if (i === index) {
          card.classList.add('selected');
        } else {
          card.classList.remove('selected');
        }
      });
    };
    
    // 确认选择
    const confirmSelection = () => {
      if (selectedIndex >= 0) {
        this.modelPath = this.sampleModels[selectedIndex].path;
        closeDialog();
        this.loadModel();
      }
    };
    
    // 绑定事件
    closeButton.addEventListener('click', closeDialog);
    cancelButton.addEventListener('click', closeDialog);
    selectButton.addEventListener('click', confirmSelection);
    
    modelCards.forEach((card, index) => {
      card.addEventListener('click', () => selectModel(index));
    });
  }
  
  /**
   * 打开模型选择器
   */
  openModelSelector() {
    // 创建文件输入元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.fbx,.gltf,.glb,.obj';
    fileInput.style.display = 'none';
    
    // 添加到文档
    document.body.appendChild(fileInput);
    
    // 设置事件
    fileInput.addEventListener('change', (event) => {
      if (event.target.files.length > 0) {
        const file = event.target.files[0];
        this.modelPath = URL.createObjectURL(file);
        this.loadModel();
      }
      
      // 移除元素
      document.body.removeChild(fileInput);
    });
    
    // 触发点击
    fileInput.click();
  }
  
  /**
   * 导入模型
   */
  importModel() {
    if (this.modelPath) {
      this.loadModel();
    } else {
      this.openModelSelector();
    }
  }
  
  /**
   * 加载模型
   */
  loadModel() {
    console.log('加载模型:', this.modelPath);
    
    // 在实际应用中，这里应该使用引擎的模型加载功能
    // 这里使用模拟代码
    
    // 创建加载提示
    this.showLoadingIndicator();
    
    // 模拟加载延迟
    setTimeout(() => {
      // 创建角色实体
      this.character = {
        id: 'character1',
        name: '角色',
        model: this.modelPath,
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      };
      
      // 隐藏加载提示
      this.hideLoadingIndicator();
      
      // 显示成功消息
      this.showSuccessMessage('角色模型导入成功！');
      
      // 触发模型加载完成事件
      this.onModelLoaded();
    }, 2000);
  }
  
  /**
   * 显示加载提示
   */
  showLoadingIndicator() {
    const loadingIndicator = document.createElement('div');
    loadingIndicator.id = 'loading-indicator';
    loadingIndicator.innerHTML = `
      <div class="spinner"></div>
      <div class="loading-text">正在导入模型...</div>
    `;
    
    document.body.appendChild(loadingIndicator);
  }
  
  /**
   * 隐藏加载提示
   */
  hideLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
      document.body.removeChild(loadingIndicator);
    }
  }
  
  /**
   * 显示成功消息
   * @param {string} message 消息内容
   */
  showSuccessMessage(message) {
    const messageElement = document.createElement('div');
    messageElement.className = 'success-message';
    messageElement.textContent = message;
    
    document.body.appendChild(messageElement);
    
    // 自动隐藏
    setTimeout(() => {
      document.body.removeChild(messageElement);
    }, 3000);
  }
  
  /**
   * 模型加载完成事件
   */
  onModelLoaded() {
    // 这里可以触发事件或回调
    console.log('模型加载完成');
    
    // 在实际应用中，这里应该通知其他组件模型已加载完成
  }
}
