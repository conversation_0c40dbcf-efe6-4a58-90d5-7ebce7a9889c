/**
 * BART模型
 * 用于序列到序列任务的双向和自回归变换器
 */
import { AIModelType } from '../AIModelType';
import { AIModelConfig } from '../AIModelConfig';
import { IAIModel, TextGenerationOptions, TextSummaryResult } from './IAIModel';
/**
 * BART模型配置
 */
export interface BARTModelConfig extends AIModelConfig {
    /** 模型变体 */
    variant?: 'base' | 'large' | 'cnn';
    /** 最小生成长度 */
    minLength?: number;
    /** 最大生成长度 */
    maxLength?: number;
    /** 是否使用束搜索 */
    useBeamSearch?: boolean;
    /** 束大小 */
    beamSize?: number;
    /** 是否使用长度惩罚 */
    useLengthPenalty?: boolean;
    /** 长度惩罚系数 */
    lengthPenalty?: number;
}
/**
 * BART模型
 */
export declare class BARTModel implements IAIModel {
    /** 模型类型 */
    private readonly modelType;
    /** 模型配置 */
    private config;
    /** 全局配置 */
    private globalConfig;
    /** 是否已初始化 */
    private initialized;
    /** 是否正在初始化 */
    private initializing;
    /** 模型（仅用于类型安全） */
    private model;
    /** 分词器（仅用于类型安全） */
    private tokenizer;
    /** 事件发射器 */
    private eventEmitter;
    /** 获取模型实例（仅用于内部使用） */
    private getModelInstance;
    /** 获取分词器实例（仅用于内部使用） */
    private getTokenizerInstance;
    /**
     * 构造函数
     * @param config 模型配置
     * @param globalConfig 全局配置
     */
    constructor(config?: BARTModelConfig, globalConfig?: any);
    /**
     * 获取模型类型
     * @returns 模型类型
     */
    getType(): AIModelType;
    /**
     * 获取模型配置
     * @returns 模型配置
     */
    getConfig(): AIModelConfig;
    /**
     * 初始化模型
     * @returns 是否成功初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 生成文本
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 生成的文本
     */
    generateText(prompt: string, _options?: TextGenerationOptions): Promise<string>;
    /**
     * 生成文本摘要
     * @param text 要摘要的文本
     * @param maxLength 最大摘要长度
     * @returns 摘要结果
     */
    summarizeText(text: string, maxLength?: number): Promise<TextSummaryResult>;
    /**
     * 销毁模型
     */
    dispose(): void;
    /**
     * 模拟生成
     * @param input 输入
     * @returns 生成结果
     */
    private mockGenerate;
    /**
     * 模拟摘要
     * @param input 输入
     * @returns 摘要结果
     */
    private mockSummarize;
}
