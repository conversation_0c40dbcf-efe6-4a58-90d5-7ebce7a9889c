/**
 * 粒子系统编辑器组件
 */
import React, { useState, useEffect, useRef } from 'react';
import { Tabs, Form, Input, InputNumber, Select, Button, Upload, Switch, Space } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  SaveOutlined,
  UploadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// Engine导入已移除，使用EngineService代替
import './ParticleEditor.less';

const { TabPane } = Tabs;
const { Option } = Select;

// 粒子形状选项
const particleShapeOptions = [
  { value: 'circle', label: '圆形' },
  { value: 'square', label: '方形' },
  { value: 'triangle', label: '三角形' },
  { value: 'custom', label: '自定义' },
];

// 粒子混合模式选项
const blendModeOptions = [
  { value: 'normal', label: '正常' },
  { value: 'add', label: '相加' },
  { value: 'multiply', label: '相乘' },
  { value: 'screen', label: '滤色' },
];

// 发射器类型选项
const emitterTypeOptions = [
  { value: 'point', label: '点' },
  { value: 'circle', label: '圆形' },
  { value: 'box', label: '矩形' },
  { value: 'sphere', label: '球体' },
];

interface ParticleEditorProps {
  particleSystemId?: string;
  onSave?: (particleSystem: any) => void;
  onCancel?: () => void;
}

const ParticleEditor: React.FC<ParticleEditorProps> = ({ particleSystemId, onSave, onCancel }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<any>(null);
  const particleSystemRef = useRef<any>(null);

  // 粒子系统状态
  const [isPlaying, setIsPlaying] = useState<boolean>(true);
  const [presetName, setPresetName] = useState<string>('');
  const [, setCustomTexture] = useState<string>(''); // 用于纹理上传功能
  
  // 初始化引擎和预览
  useEffect(() => {
    if (!previewCanvasRef.current) return;
    
    // 创建引擎实例 - 使用模拟引擎
    engineRef.current = {
      canvas: previewCanvasRef.current,
      autoStart: false,
      start: () => {},
      stop: () => {},
      dispose: () => {}
    } as any;
    
    // 设置预览场景
    setupPreviewScene();
    
    // 如果有粒子系统ID，加载粒子系统
    if (particleSystemId) {
      loadParticleSystem(particleSystemId);
    } else {
      // 否则创建默认粒子系统
      createDefaultParticleSystem();
    }
    
    // 清理函数
    return () => {
      if (engineRef.current) {
        engineRef.current.dispose();
      }
    };
  }, [particleSystemId]);
  
  // 设置预览场景
  const setupPreviewScene = () => {
    if (!engineRef.current) return;

    // 初始化引擎
    engineRef.current.initialize();

    // 启动引擎
    engineRef.current.start();
  };
  
  // 创建默认粒子系统
  const createDefaultParticleSystem = () => {
    if (!engineRef.current) return;
    
    // 创建默认粒子系统
    const defaultSettings = {
      name: '新粒子系统',
      maxParticles: 1000,
      emissionRate: 100,
      lifetime: { min: 1, max: 2 },
      startSize: { min: 0.1, max: 0.3 },
      endSize: { min: 0, max: 0.1 },
      startColor: '#ffffff',
      endColor: '#ffffff00',
      startSpeed: { min: 1, max: 2 },
      gravity: [0, -0.5, 0],
      emitterType: 'point',
      emitterSize: [0, 0, 0],
      emitterRadius: 0,
      shape: 'circle',
      blendMode: 'add',
      texture: '',
      loop: true,
      prewarm: false,
      simulationSpace: 'local',
      renderMode: '3d'
    };
    
    // 设置表单值
    form.setFieldsValue(defaultSettings);
    
    // 创建粒子系统
    createParticleSystem(defaultSettings);
  };
  
  // 加载粒子系统
  const loadParticleSystem = (id: string) => {
    // 这里应该从API或Redux加载粒子系统数据
    // 由于我们没有实际的API，这里只是一个示例
    const dummyParticleSystem = {
      id,
      name: '示例粒子系统',
      maxParticles: 1000,
      emissionRate: 100,
      lifetime: { min: 1, max: 2 },
      startSize: { min: 0.1, max: 0.3 },
      endSize: { min: 0, max: 0.1 },
      startColor: '#ffffff',
      endColor: '#ffffff00',
      startSpeed: { min: 1, max: 2 },
      gravity: [0, -0.5, 0],
      emitterType: 'point',
      emitterSize: [0, 0, 0],
      emitterRadius: 0,
      shape: 'circle',
      blendMode: 'add',
      texture: '',
      loop: true,
      prewarm: false,
      simulationSpace: 'local',
      renderMode: '3d'
    };
    
    // 设置表单值
    form.setFieldsValue(dummyParticleSystem);
    
    // 创建粒子系统
    createParticleSystem(dummyParticleSystem);
  };
  
  // 创建粒子系统
  const createParticleSystem = (settings: any) => {
    if (!engineRef.current) return;

    // 这里应该使用引擎的粒子系统API
    // 由于当前引擎API还在开发中，这里先用模拟实现
    console.log('创建粒子系统:', settings);

    // 模拟粒子系统对象
    particleSystemRef.current = {
      settings,
      isPlaying: true,
      pause: () => { console.log('暂停粒子系统'); },
      play: () => { console.log('播放粒子系统'); },
      reset: () => { console.log('重置粒子系统'); },
      setMaxParticles: (max: number) => { console.log('设置最大粒子数:', max); },
      setEmissionRate: (rate: number) => { console.log('设置发射率:', rate); },
      setLifetime: (lifetime: number[]) => { console.log('设置生命周期:', lifetime); },
      setStartSize: (size: number[]) => { console.log('设置起始大小:', size); },
      setEndSize: (size: number[]) => { console.log('设置结束大小:', size); },
      setStartColor: (color: string) => { console.log('设置起始颜色:', color); },
      setEndColor: (color: string) => { console.log('设置结束颜色:', color); },
      setStartSpeed: (speed: number[]) => { console.log('设置起始速度:', speed); },
      setGravity: (gravity: number[]) => { console.log('设置重力:', gravity); },
      setEmitterType: (type: string) => { console.log('设置发射器类型:', type); },
      setEmitterSize: (size: number[]) => { console.log('设置发射器大小:', size); },
      setEmitterRadius: (radius: number) => { console.log('设置发射器半径:', radius); },
      setShape: (shape: string) => { console.log('设置形状:', shape); },
      setBlendMode: (mode: string) => { console.log('设置混合模式:', mode); },
      setTexture: (texture: string) => { console.log('设置纹理:', texture); },
      setLoop: (loop: boolean) => { console.log('设置循环:', loop); },
      setPrewarm: (prewarm: boolean) => { console.log('设置预热:', prewarm); },
      setSimulationSpace: (space: string) => { console.log('设置模拟空间:', space); },
      setRenderMode: (mode: string) => { console.log('设置渲染模式:', mode); }
    };

    // 如果不是播放状态，暂停粒子系统
    if (!isPlaying) {
      particleSystemRef.current.pause();
    }
  };
  
  // 更新粒子系统
  const updateParticleSystem = (changedValues: any, allValues: any) => {
    if (!particleSystemRef.current) return;
    
    // 更新粒子系统属性
    Object.keys(changedValues).forEach(key => {
      switch (key) {
        case 'maxParticles':
          particleSystemRef.current.setMaxParticles(allValues.maxParticles);
          break;
        case 'emissionRate':
          particleSystemRef.current.setEmissionRate(allValues.emissionRate);
          break;
        case 'lifetime':
          particleSystemRef.current.setLifetime([allValues.lifetime.min, allValues.lifetime.max]);
          break;
        case 'startSize':
          particleSystemRef.current.setStartSize([allValues.startSize.min, allValues.startSize.max]);
          break;
        case 'endSize':
          particleSystemRef.current.setEndSize([allValues.endSize.min, allValues.endSize.max]);
          break;
        case 'startColor':
          particleSystemRef.current.setStartColor(allValues.startColor);
          break;
        case 'endColor':
          particleSystemRef.current.setEndColor(allValues.endColor);
          break;
        case 'startSpeed':
          particleSystemRef.current.setStartSpeed([allValues.startSpeed.min, allValues.startSpeed.max]);
          break;
        case 'gravity':
          particleSystemRef.current.setGravity(allValues.gravity);
          break;
        case 'emitterType':
          particleSystemRef.current.setEmitterType(allValues.emitterType);
          break;
        case 'emitterSize':
          particleSystemRef.current.setEmitterSize(allValues.emitterSize);
          break;
        case 'emitterRadius':
          particleSystemRef.current.setEmitterRadius(allValues.emitterRadius);
          break;
        case 'shape':
          particleSystemRef.current.setShape(allValues.shape);
          break;
        case 'blendMode':
          particleSystemRef.current.setBlendMode(allValues.blendMode);
          break;
        case 'texture':
          particleSystemRef.current.setTexture(allValues.texture);
          break;
        case 'loop':
          particleSystemRef.current.setLoop(allValues.loop);
          break;
        case 'prewarm':
          particleSystemRef.current.setPrewarm(allValues.prewarm);
          break;
        case 'simulationSpace':
          particleSystemRef.current.setSimulationSpace(allValues.simulationSpace);
          break;
        case 'renderMode':
          particleSystemRef.current.setRenderMode(allValues.renderMode);
          break;
      }
    });
  };
  
  // 处理表单值变化
  const handleValuesChange = (changedValues: any, allValues: any) => {
    updateParticleSystem(changedValues, allValues);
  };
  
  // 处理播放/暂停
  const handlePlayPause = () => {
    if (!particleSystemRef.current) return;
    
    if (isPlaying) {
      particleSystemRef.current.pause();
    } else {
      particleSystemRef.current.play();
    }
    
    setIsPlaying(!isPlaying);
  };
  
  // 处理重置
  const handleReset = () => {
    if (!particleSystemRef.current) return;
    
    particleSystemRef.current.reset();
  };
  
  // 处理保存
  const handleSave = () => {
    form.validateFields().then(values => {
      if (onSave) {
        onSave({
          id: particleSystemId,
          ...values
        });
      }
    });
  };
  
  // 处理预设选择
  const handlePresetSelect = (preset: string) => {
    // 这里应该加载预设数据
    // 由于我们没有实际的预设数据，这里只是一个示例
    let presetData;
    
    switch (preset) {
      case 'fire':
        presetData = {
          name: '火焰',
          maxParticles: 1000,
          emissionRate: 100,
          lifetime: { min: 1, max: 2 },
          startSize: { min: 0.5, max: 1 },
          endSize: { min: 0.1, max: 0.3 },
          startColor: '#ff7700',
          endColor: '#ff000000',
          startSpeed: { min: 1, max: 2 },
          gravity: [0, 1, 0],
          emitterType: 'point',
          emitterSize: [0, 0, 0],
          emitterRadius: 0,
          shape: 'circle',
          blendMode: 'add',
          texture: '',
          loop: true,
          prewarm: false,
          simulationSpace: 'local',
          renderMode: '3d'
        };
        break;
      case 'smoke':
        presetData = {
          name: '烟雾',
          maxParticles: 500,
          emissionRate: 50,
          lifetime: { min: 2, max: 4 },
          startSize: { min: 0.3, max: 0.5 },
          endSize: { min: 1, max: 2 },
          startColor: '#555555',
          endColor: '#55555500',
          startSpeed: { min: 0.5, max: 1 },
          gravity: [0, 0.2, 0],
          emitterType: 'circle',
          emitterSize: [0, 0, 0],
          emitterRadius: 0.5,
          shape: 'circle',
          blendMode: 'normal',
          texture: '',
          loop: true,
          prewarm: false,
          simulationSpace: 'local',
          renderMode: '3d'
        };
        break;
      case 'snow':
        presetData = {
          name: '雪花',
          maxParticles: 2000,
          emissionRate: 200,
          lifetime: { min: 5, max: 10 },
          startSize: { min: 0.05, max: 0.1 },
          endSize: { min: 0.05, max: 0.1 },
          startColor: '#ffffff',
          endColor: '#ffffff',
          startSpeed: { min: 0.5, max: 1 },
          gravity: [0, -0.1, 0],
          emitterType: 'box',
          emitterSize: [10, 0, 10],
          emitterRadius: 0,
          shape: 'circle',
          blendMode: 'normal',
          texture: '',
          loop: true,
          prewarm: false,
          simulationSpace: 'world',
          renderMode: '3d'
        };
        break;
      default:
        return;
    }
    
    // 设置表单值
    form.setFieldsValue(presetData);
    
    // 更新粒子系统
    createParticleSystem(presetData);
    
    // 设置预设名称
    setPresetName(preset);
  };
  
  return (
    <div className="particle-editor">
      <div className="particle-editor-header">
        <h2>{particleSystemId ? t('editor.particle.edit') : t('editor.particle.create')}</h2>
        <div className="particle-editor-actions">
          <Button onClick={onCancel}>{t('editor.cancel')}</Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
            {t('editor.save')}
          </Button>
        </div>
      </div>
      
      <div className="particle-editor-content">
        <div className="particle-editor-preview">
          <canvas ref={previewCanvasRef} className="preview-canvas" />
          
          <div className="preview-controls">
            <Button
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={handlePlayPause}
            >
              {isPlaying ? t('editor.pause') : t('editor.play')}
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>
              {t('editor.reset')}
            </Button>
          </div>
          
          <div className="preset-controls">
            <span>{t('editor.particle.presets')}:</span>
            <Button
              type={presetName === 'fire' ? 'primary' : 'default'}
              onClick={() => handlePresetSelect('fire')}
            >
              {t('editor.particle.presets.fire')}
            </Button>
            <Button
              type={presetName === 'smoke' ? 'primary' : 'default'}
              onClick={() => handlePresetSelect('smoke')}
            >
              {t('editor.particle.presets.smoke')}
            </Button>
            <Button
              type={presetName === 'snow' ? 'primary' : 'default'}
              onClick={() => handlePresetSelect('snow')}
            >
              {t('editor.particle.presets.snow')}
            </Button>
          </div>
        </div>
        
        <div className="particle-editor-form">
          <Form
            form={form}
            layout="vertical"
            onValuesChange={handleValuesChange}
          >
            <Form.Item name="name" label={t('editor.particle.name')} rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            
            <Tabs defaultActiveKey="emission">
              <TabPane tab={t('editor.particle.emission')} key="emission">
                <Form.Item name="maxParticles" label={t('editor.particle.maxParticles')} rules={[{ required: true }]}>
                  <InputNumber min={1} max={10000} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item name="emissionRate" label={t('editor.particle.emissionRate')} rules={[{ required: true }]}>
                  <InputNumber min={0} max={1000} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item label={t('editor.particle.lifetime')} required>
                  <Space.Compact>
                    <Form.Item name={['lifetime', 'min']} noStyle>
                      <InputNumber
                        min={0}
                        step={0.1}
                        style={{ width: '50%' }}
                        placeholder={t('editor.min') || '最小值'}
                      />
                    </Form.Item>
                    <Form.Item name={['lifetime', 'max']} noStyle>
                      <InputNumber
                        min={0}
                        step={0.1}
                        style={{ width: '50%' }}
                        placeholder={t('editor.max') || '最大值'}
                      />
                    </Form.Item>
                  </Space.Compact>
                </Form.Item>
                
                <Form.Item name="loop" label={t('editor.particle.loop')} valuePropName="checked">
                  <Switch />
                </Form.Item>
                
                <Form.Item name="prewarm" label={t('editor.particle.prewarm')} valuePropName="checked">
                  <Switch />
                </Form.Item>
                
                <Form.Item name="simulationSpace" label={t('editor.particle.simulationSpace')}>
                  <Select>
                    <Option value="local">{t('editor.particle.simulationSpace.local')}</Option>
                    <Option value="world">{t('editor.particle.simulationSpace.world')}</Option>
                  </Select>
                </Form.Item>
              </TabPane>
              
              <TabPane tab={t('editor.particle.shape')} key="shape">
                <Form.Item name="shape" label={t('editor.particle.particleShape')}>
                  <Select>
                    {particleShapeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                
                {form.getFieldValue('shape') === 'custom' && (
                  <Form.Item name="texture" label={t('editor.particle.texture')}>
                    <Input
                      addonAfter={
                        <Upload
                          showUploadList={false}
                          beforeUpload={(file) => {
                            // 这里应该处理纹理上传
                            // 由于我们没有实际的上传功能，这里只是一个示例
                            const reader = new FileReader();
                            reader.onload = (e) => {
                              if (e.target?.result) {
                                setCustomTexture(e.target.result as string);
                                form.setFieldsValue({ texture: file.name });
                                
                                // 更新粒子系统纹理
                                if (particleSystemRef.current) {
                                  particleSystemRef.current.setTexture(e.target.result as string);
                                }
                              }
                            };
                            reader.readAsDataURL(file);
                            return false;
                          }}
                        >
                          <Button icon={<UploadOutlined />} />
                        </Upload>
                      }
                    />
                  </Form.Item>
                )}
                
                <Form.Item name="emitterType" label={t('editor.particle.emitterType')}>
                  <Select>
                    {emitterTypeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                
                {(form.getFieldValue('emitterType') === 'circle' || form.getFieldValue('emitterType') === 'sphere') && (
                  <Form.Item name="emitterRadius" label={t('editor.particle.emitterRadius')}>
                    <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                )}
                
                {form.getFieldValue('emitterType') === 'box' && (
                  <Form.Item label={t('editor.particle.emitterSize')}>
                    <Space.Compact>
                      <Form.Item name={['emitterSize', 0]} noStyle>
                        <InputNumber
                          min={0}
                          step={0.1}
                          style={{ width: '33%' }}
                          placeholder="X"
                        />
                      </Form.Item>
                      <Form.Item name={['emitterSize', 1]} noStyle>
                        <InputNumber
                          min={0}
                          step={0.1}
                          style={{ width: '33%' }}
                          placeholder="Y"
                        />
                      </Form.Item>
                      <Form.Item name={['emitterSize', 2]} noStyle>
                        <InputNumber
                          min={0}
                          step={0.1}
                          style={{ width: '34%' }}
                          placeholder="Z"
                        />
                      </Form.Item>
                    </Space.Compact>
                  </Form.Item>
                )}
              </TabPane>
              
              <TabPane tab={t('editor.particle.appearance')} key="appearance">
                <Form.Item label={t('editor.particle.startSize')}>
                  <Space.Compact>
                    <Form.Item name={['startSize', 'min']} noStyle>
                      <InputNumber
                        min={0}
                        step={0.01}
                        style={{ width: '50%' }}
                        placeholder={t('editor.min') || '最小值'}
                      />
                    </Form.Item>
                    <Form.Item name={['startSize', 'max']} noStyle>
                      <InputNumber
                        min={0}
                        step={0.01}
                        style={{ width: '50%' }}
                        placeholder={t('editor.max') || '最大值'}
                      />
                    </Form.Item>
                  </Space.Compact>
                </Form.Item>
                
                <Form.Item label={t('editor.particle.endSize')}>
                  <Space.Compact>
                    <Form.Item name={['endSize', 'min']} noStyle>
                      <InputNumber
                        min={0}
                        step={0.01}
                        style={{ width: '50%' }}
                        placeholder={t('editor.min') || '最小值'}
                      />
                    </Form.Item>
                    <Form.Item name={['endSize', 'max']} noStyle>
                      <InputNumber
                        min={0}
                        step={0.01}
                        style={{ width: '50%' }}
                        placeholder={t('editor.max') || '最大值'}
                      />
                    </Form.Item>
                  </Space.Compact>
                </Form.Item>
                
                <Form.Item name="startColor" label={t('editor.particle.startColor')}>
                  <Input type="color" />
                </Form.Item>
                
                <Form.Item name="endColor" label={t('editor.particle.endColor')}>
                  <Input type="color" />
                </Form.Item>
                
                <Form.Item name="blendMode" label={t('editor.particle.blendMode')}>
                  <Select>
                    {blendModeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                
                <Form.Item name="renderMode" label={t('editor.particle.renderMode')}>
                  <Select>
                    <Option value="3d">{t('editor.particle.renderMode.3d')}</Option>
                    <Option value="billboard">{t('editor.particle.renderMode.billboard')}</Option>
                    <Option value="stretched">{t('editor.particle.renderMode.stretched')}</Option>
                  </Select>
                </Form.Item>
              </TabPane>
              
              <TabPane tab={t('editor.particle.motion')} key="motion">
                <Form.Item label={t('editor.particle.startSpeed')}>
                  <Space.Compact>
                    <Form.Item name={['startSpeed', 'min']} noStyle>
                      <InputNumber
                        step={0.1}
                        style={{ width: '50%' }}
                        placeholder={t('editor.min') || '最小值'}
                      />
                    </Form.Item>
                    <Form.Item name={['startSpeed', 'max']} noStyle>
                      <InputNumber
                        step={0.1}
                        style={{ width: '50%' }}
                        placeholder={t('editor.max') || '最大值'}
                      />
                    </Form.Item>
                  </Space.Compact>
                </Form.Item>
                
                <Form.Item label={t('editor.particle.gravity')}>
                  <Space.Compact>
                    <Form.Item name={['gravity', 0]} noStyle>
                      <InputNumber
                        step={0.1}
                        style={{ width: '33%' }}
                        placeholder="X"
                      />
                    </Form.Item>
                    <Form.Item name={['gravity', 1]} noStyle>
                      <InputNumber
                        step={0.1}
                        style={{ width: '33%' }}
                        placeholder="Y"
                      />
                    </Form.Item>
                    <Form.Item name={['gravity', 2]} noStyle>
                      <InputNumber
                        step={0.1}
                        style={{ width: '34%' }}
                        placeholder="Z"
                      />
                    </Form.Item>
                  </Space.Compact>
                </Form.Item>
              </TabPane>
            </Tabs>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default ParticleEditor;
