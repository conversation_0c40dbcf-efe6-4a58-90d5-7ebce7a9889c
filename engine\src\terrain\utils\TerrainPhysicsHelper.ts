/**
 * 地形物理助手
 * 用于处理地形物理相关功能
 */
import * as THREE from 'three';
// import * as RAPIER from '@dimforge/rapier3d-compat'; // 暂时注释掉，避免导入错误
import type { Entity } from '../../core/Entity';
import { TerrainComponent } from '../components/TerrainComponent';
import { PhysicsSystem } from '../../physics/PhysicsSystem';
import { Debug } from '../../utils/Debug';

// 临时类型定义，直到 RAPIER 正确导入
interface MockRapierCollider {
  handle: number;
}

interface MockRapierWorld {
  createCollider(desc: any): MockRapierCollider;
  removeCollider(collider: MockRapierCollider, wakeUp?: boolean): void;
}

interface MockRapierVector3 {
  x: number;
  y: number;
  z: number;
}

interface MockRapierColliderDesc {
  setFriction(friction: number): void;
  setRestitution(restitution: number): void;
  setDensity(density: number): void;
}

// 模拟 RAPIER 对象
const RAPIER = {
  ColliderDesc: {
    heightfield: (
      nrows: number,
      ncols: number,
      heights: Float32Array,
      scale: MockRapierVector3
    ): MockRapierColliderDesc => ({
      setFriction: () => {},
      setRestitution: () => {},
      setDensity: () => {}
    })
  },
  Vector3: class {
    constructor(public x: number, public y: number, public z: number) {}
  }
};

/**
 * 地形物理材质属性
 */
export interface TerrainPhysicsMaterialProps {
  /** 摩擦力 */
  friction: number;
  /** 弹性 */
  restitution: number;
  /** 密度 */
  density: number;
}

/**
 * 地形物理助手
 */
export class TerrainPhysicsHelper {
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 碰撞器映射 */
  private colliderMap: Map<string, MockRapierCollider>;
  /** 调试可视化 */
  private debugVisuals: Map<string, THREE.Object3D>;
  
  /**
   * 创建地形物理助手
   * @param physicsSystem 物理系统
   */
  constructor(physicsSystem: PhysicsSystem) {
    this.physicsSystem = physicsSystem;
    this.colliderMap = new Map();
    this.debugVisuals = new Map();
  }
  
  /**
   * 创建地形物理
   * @param entity 实体
   * @param terrain 地形组件
   * @param materialProps 物理材质属性
   * @returns 是否创建成功
   */
  public createTerrainPhysics(
    entity: Entity, 
    terrain: TerrainComponent, 
    materialProps: TerrainPhysicsMaterialProps
  ): boolean {
    try {
      // 移除现有的物理
      this.removeTerrainPhysics(entity);
      
      // 获取物理世界
      const world = this.physicsSystem.getWorld();
      if (!world) {
        Debug.warn('无法创建地形物理：物理世界不存在');
        return false;
      }
      
      // 创建高度场碰撞器
      const collider = this.createHeightfieldCollider(entity, terrain, materialProps);
      if (!collider) {
        Debug.warn('无法创建地形物理：创建碰撞器失败');
        return false;
      }
      
      // 存储碰撞器
      this.colliderMap.set(entity.id, collider);
      
      // 创建调试可视化
      if (terrain.showPhysicsDebug) {
        this.createDebugVisuals(entity, terrain);
      }
      
      return true;
    } catch (error) {
      Debug.error('创建地形物理失败:', error);
      return false;
    }
  }
  
  /**
   * 更新地形物理
   * @param entity 实体
   * @param terrain 地形组件
   * @param materialProps 物理材质属性
   * @returns 是否更新成功
   */
  public updateTerrainPhysics(
    entity: Entity, 
    terrain: TerrainComponent, 
    materialProps: TerrainPhysicsMaterialProps
  ): boolean {
    try {
      // 移除现有的物理
      this.removeTerrainPhysics(entity);
      
      // 重新创建物理
      return this.createTerrainPhysics(entity, terrain, materialProps);
    } catch (error) {
      Debug.error('更新地形物理失败:', error);
      return false;
    }
  }
  
  /**
   * 移除地形物理
   * @param entity 实体
   * @returns 是否移除成功
   */
  public removeTerrainPhysics(entity: Entity): boolean {
    try {
      // 移除碰撞器
      const collider = this.colliderMap.get(entity.id);
      if (collider) {
        // 获取物理世界
        const world = this.physicsSystem.getWorld() as unknown as MockRapierWorld;
        if (world && world.removeCollider) {
          world.removeCollider(collider, true);
        }
        
        this.colliderMap.delete(entity.id);
      }
      
      // 移除调试可视化
      const debugVisual = this.debugVisuals.get(entity.id);
      if (debugVisual) {
        // 通过 Transform 组件移除对象
        const transform = entity.getComponent('Transform');
        if (transform) {
          (transform as any).getObject3D().remove(debugVisual);
        }
        this.debugVisuals.delete(entity.id);
      }
      
      return true;
    } catch (error) {
      Debug.error('移除地形物理失败:', error);
      return false;
    }
  }
  
  /**
   * 创建高度场碰撞器
   * @param entity 实体
   * @param terrain 地形组件
   * @param materialProps 物理材质属性
   * @returns 碰撞器
   */
  private createHeightfieldCollider(
    entity: Entity,
    terrain: TerrainComponent,
    materialProps: TerrainPhysicsMaterialProps
  ): MockRapierCollider | null {
    try {
      // 获取物理世界
      const world = this.physicsSystem.getWorld() as unknown as MockRapierWorld;
      if (!world) {
        return null;
      }

      // 计算物理分辨率
      const physicsResolution = terrain.physicsResolution || 64;

      // 创建高度场数据
      const heights = this.createHeightfieldData(terrain, physicsResolution);

      // 计算缩放
      const scaleX = terrain.width / (physicsResolution - 1);
      const scaleZ = terrain.height / (physicsResolution - 1);

      // 创建高度场碰撞器描述
      const colliderDesc = RAPIER.ColliderDesc.heightfield(
        physicsResolution - 1,
        physicsResolution - 1,
        heights,
        new RAPIER.Vector3(scaleX, terrain.maxHeight, scaleZ)
      );

      // 设置物理材质属性
      colliderDesc.setFriction(materialProps.friction);
      colliderDesc.setRestitution(materialProps.restitution);
      colliderDesc.setDensity(materialProps.density);

      // 创建碰撞器
      const collider = world.createCollider ? world.createCollider(colliderDesc) : { handle: 0 };
      
      return collider;
    } catch (error) {
      Debug.error('创建高度场碰撞器失败:', error);
      return null;
    }
  }
  
  /**
   * 创建高度场数据
   * @param terrain 地形组件
   * @param resolution 分辨率
   * @returns 高度场数据
   */
  private createHeightfieldData(terrain: TerrainComponent, resolution: number): Float32Array {
    // 创建高度场数据
    const heights = new Float32Array(resolution * resolution);
    
    // 计算采样步长
    const stepX = (terrain.resolution - 1) / (resolution - 1);
    const stepZ = (terrain.resolution - 1) / (resolution - 1);
    
    // 采样高度数据
    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        // 计算采样坐标
        const sampleX = Math.min(Math.floor(x * stepX), terrain.resolution - 1);
        const sampleZ = Math.min(Math.floor(z * stepZ), terrain.resolution - 1);
        
        // 获取高度
        const height = terrain.heightData[sampleZ * terrain.resolution + sampleX];
        
        // 存储高度
        heights[z * resolution + x] = height;
      }
    }
    
    return heights;
  }
  
  /**
   * 创建调试可视化
   * @param entity 实体
   * @param terrain 地形组件
   */
  private createDebugVisuals(entity: Entity, terrain: TerrainComponent): void {
    try {
      // 移除现有的调试可视化
      const existingDebugVisual = this.debugVisuals.get(entity.id);
      if (existingDebugVisual) {
        const transform = entity.getComponent('Transform');
        if (transform) {
          (transform as any).getObject3D().remove(existingDebugVisual);
        }
      }
      
      // 计算物理分辨率
      const physicsResolution = terrain.physicsResolution || 64;
      
      // 创建线框几何体
      const geometry = new THREE.PlaneGeometry(
        terrain.width,
        terrain.height,
        physicsResolution - 1,
        physicsResolution - 1
      );
      geometry.rotateX(-Math.PI / 2);
      
      // 更新顶点高度
      const heights = this.createHeightfieldData(terrain, physicsResolution);
      const positions = geometry.attributes.position.array as Float32Array;
      for (let i = 0, j = 0; i < positions.length; i += 3, j++) {
        positions[i + 1] = heights[j] * terrain.maxHeight;
      }
      
      // 创建线框材质
      const material = new THREE.MeshBasicMaterial({
        color: 0x00ff00,
        wireframe: true,
        transparent: true,
        opacity: 0.5
      });
      
      // 创建线框网格
      const wireframe = new THREE.Mesh(geometry, material);
      wireframe.name = `TerrainPhysicsDebug_${entity.id}`;
      
      // 添加到实体的Transform组件
      const transform = entity.getComponent('Transform');
      if (transform) {
        (transform as any).getObject3D().add(wireframe);
      }
      
      // 存储调试可视化
      this.debugVisuals.set(entity.id, wireframe);
    } catch (error) {
      Debug.error('创建地形物理调试可视化失败:', error);
    }
  }
  
  /**
   * 更新调试可视化
   * @param entity 实体
   * @param terrain 地形组件
   * @param show 是否显示
   */
  public updateDebugVisuals(entity: Entity, terrain: TerrainComponent, show: boolean): void {
    try {
      if (show) {
        // 创建或更新调试可视化
        this.createDebugVisuals(entity, terrain);
      } else {
        // 移除调试可视化
        const debugVisual = this.debugVisuals.get(entity.id);
        if (debugVisual) {
          const transform = entity.getComponent('Transform');
          if (transform) {
            (transform as any).getObject3D().remove(debugVisual);
          }
          this.debugVisuals.delete(entity.id);
        }
      }
    } catch (error) {
      Debug.error('更新地形物理调试可视化失败:', error);
    }
  }
}
