/**
 * 物理驱动的面部动画系统
 * 使用物理模拟来驱动面部动画，实现更自然的面部表情
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { FacialExpressionType } from '../components/FacialAnimationComponent';
import { PhysicalFacialAnimationComponent } from '../components/PhysicalFacialAnimationComponent';
/**
 * 物理驱动的面部动画系统配置
 */
export interface PhysicalFacialAnimationSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 物理更新频率 */
    physicsUpdateRate?: number;
    /** 重力 */
    gravity?: THREE.Vector3;
    /** 阻尼 */
    damping?: number;
    /** 是否使用GPU加速 */
    useGPU?: boolean;
    /** 是否显示调试信息 */
    showDebugInfo?: boolean;
    /** 是否使用高级物理 */
    useAdvancedPhysics?: boolean;
    /** 是否使用软体物理 */
    useSoftBodyPhysics?: boolean;
    /** 是否使用碰撞检测 */
    useCollisionDetection?: boolean;
}
/**
 * 肌肉类型
 */
export declare enum MuscleType {
    JAW = "jaw",
    CHEEK = "cheek",
    EYEBROW = "eyebrow",
    LIP = "lip",
    EYELID = "eyelid",
    NOSE = "nose",
    FOREHEAD = "forehead",
    TONGUE = "tongue"
}
/**
 * 肌肉数据
 */
export interface MuscleData {
    /** 肌肉类型 */
    type: MuscleType;
    /** 肌肉名称 */
    name: string;
    /** 起始点 */
    start: THREE.Vector3;
    /** 结束点 */
    end: THREE.Vector3;
    /** 质量 */
    mass: number;
    /** 半径 */
    radius: number;
    /** 刚度 */
    stiffness: number;
    /** 阻尼 */
    damping: number;
    /** 起始点是否固定 */
    fixedStart: boolean;
    /** 结束点是否固定 */
    fixedEnd: boolean;
    /** 最大拉伸 */
    maxStretch?: number;
    /** 最大压缩 */
    maxCompress?: number;
    /** 休息长度 */
    restLength?: number;
    /** 控制点 */
    controlPoints?: THREE.Vector3[];
}
/**
 * 物理驱动的面部动画系统
 */
export declare class PhysicalFacialAnimationSystem extends System {
    /** 系统名称 */
    static readonly NAME = "PhysicalFacialAnimationSystem";
    /** 配置 */
    private config;
    /** 事件发射器 */
    private _eventEmitter;
    /** 物理面部动画组件映射 */
    private components;
    /** 物理世界 */
    private physicsWorld;
    /** 上次物理更新时间 */
    private _lastPhysicsUpdateTime;
    /** 物理更新间隔 */
    private physicsUpdateInterval;
    /** 累积时间 */
    private accumulatedTime;
    /** 调试渲染器 */
    private debugRenderer;
    /** 调试场景 */
    private debugScene;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: PhysicalFacialAnimationSystemConfig);
    /**
     * 初始化物理世界
     */
    private initPhysicsWorld;
    /**
     * 初始化调试渲染器
     */
    private initDebugRenderer;
    /**
     * 创建物理面部动画组件
     * @param entity 实体
     * @returns 物理面部动画组件
     */
    createPhysicalFacialAnimation(entity: Entity): PhysicalFacialAnimationComponent;
    /**
     * 移除物理面部动画组件
     * @param entity 实体
     */
    removePhysicalFacialAnimation(entity: Entity): void;
    /**
     * 清理物理资源
     * @param component 物理面部动画组件
     */
    private cleanupPhysicsResources;
    /**
     * 获取物理面部动画组件
     * @param entity 实体
     * @returns 物理面部动画组件
     */
    getPhysicalFacialAnimation(entity: Entity): PhysicalFacialAnimationComponent | undefined;
    /**
     * 添加肌肉
     * @param entity 实体
     * @param muscleData 肌肉数据
     * @returns 是否成功添加
     */
    addMuscle(entity: Entity, muscleData: MuscleData): boolean;
    /**
     * 应用表情
     * @param entity 实体
     * @param expression 表情类型
     * @param weight 权重
     * @returns 是否成功应用
     */
    applyExpression(entity: Entity, expression: FacialExpressionType, weight?: number): boolean;
    /**
     * 应用肌肉力
     * @param entity 实体
     * @param muscleName 肌肉名称
     * @param force 力
     * @returns 是否成功应用
     */
    applyMuscleForce(entity: Entity, muscleName: string, force: THREE.Vector3): boolean;
    /**
     * 设置肌肉刚度
     * @param entity 实体
     * @param muscleName 肌肉名称
     * @param stiffness 刚度
     * @returns 是否成功设置
     */
    setMuscleStiffness(entity: Entity, muscleName: string, stiffness: number): boolean;
    /**
     * 设置肌肉阻尼
     * @param entity 实体
     * @param muscleName 肌肉名称
     * @param damping 阻尼
     * @returns 是否成功设置
     */
    setMuscleDamping(entity: Entity, muscleName: string, damping: number): boolean;
    /**
     * 重置所有肌肉
     * @param entity 实体
     * @returns 是否成功重置
     */
    resetAllMuscles(entity: Entity): boolean;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新物理
     * @param deltaTime 帧间隔时间（秒）
     */
    private updatePhysics;
    /**
     * 同步到面部动画组件
     * @param entity 实体
     * @param physicalComponent 物理面部动画组件
     */
    private syncToFacialAnimation;
    /**
     * 更新调试渲染器
     */
    private updateDebugRenderer;
    /**
     * 创建默认肌肉配置
     * @param entity 实体
     * @returns 是否成功创建
     */
    createDefaultMuscleConfiguration(entity: Entity): boolean;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 发射事件
     * @param event 事件名称
     * @param data 事件数据
     */
    emitEvent(event: string, data?: any): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
