/**
 * 头像系统
 * 导出所有头像相关的组件和系统
 */

// 组件
export { FacialAnimationComponent } from './components/FacialAnimationComponent';
export type { FacialExpressionType, VisemeType } from './components/FacialAnimationComponent';
export { LipSyncComponent } from './components/LipSyncComponent';
export { AIAnimationSynthesisComponent } from './components/AIAnimationSynthesisComponent';
export { FacialAnimationEditorComponent } from './components/FacialAnimationEditorComponent';
export type { EditorState } from './components/FacialAnimationEditorComponent';
export { DigitalHumanComponent } from './components/DigitalHumanComponent';
export type {
  DigitalHumanComponentConfig,
  DigitalHumanSource,
  FaceGeometry,
  BodyMorphTargets,
  ClothingSlot,
  ClothingSlots,
  ClothingSlotType,
  PersonalityTraits
} from './components/DigitalHumanComponent';
export { AvatarComponent } from './components/AvatarComponent';
export type { AvatarComponentConfig, AvatarType } from './components/AvatarComponent';
export { AvatarRigComponent } from './components/AvatarRigComponent';
export type { HumanBoneName } from './components/AvatarRigComponent';

// 系统
export { FacialAnimationSystem } from './systems/FacialAnimationSystem';
export { FacialAnimationModelAdapterSystem } from './systems/FacialAnimationModelAdapterSystem';
export { LipSyncSystem } from './systems/LipSyncSystem';
export { AIAnimationSynthesisSystem } from './systems/AIAnimationSynthesisSystem';
export { FacialAnimationEditorSystem } from './systems/FacialAnimationEditorSystem';
export { DigitalHumanSystem } from './systems/DigitalHumanSystem';
export type { DigitalHumanSystemConfig, DigitalHumanGenerationRequest } from './systems/DigitalHumanSystem';

// 适配器
export { FacialAnimationModelAdapterComponent } from './adapters/FacialAnimationModelAdapter';
export type { FacialAnimationModelType } from './adapters/FacialAnimationModelAdapter';

// 动画
export { FacialAnimationClip } from './animation/FacialAnimationClip';
export type { ExpressionKeyframe, VisemeKeyframe } from './animation/FacialAnimationClip';

// 多动作融合系统
export * from './animation/MultiActionFusionTypes';
export { MultiActionFusionManager } from './animation/MultiActionFusionManager';
export { ActionConflictResolver } from './animation/ActionConflictResolver';
export { AnimationStateMachine } from './animation/AnimationStateMachine';
export { AnimationRetargeter } from './animation/AnimationRetargeter';

// BIP集成系统
export { BIPIntegrationSystem } from './systems/BIPIntegrationSystem';

// 数字人导入系统
export { DigitalHumanImportSystem, SupportedFileFormat } from './systems/DigitalHumanImportSystem';

// 数字人文件格式（排除重复的BoneHierarchy导出）
export { DIGITAL_HUMAN_FORMAT_VERSION, DigitalHumanFileValidator } from './formats/DigitalHumanFormat';
export type {
  DigitalHumanMetadata,
  ModelData,
  MaterialData,
  TextureData,
  MeshData,
  SkinningData,
  SkeletonData,
  BoneData,
  // BoneHierarchy, // 注释掉避免重复导出
  AnimationData,
  AnimationClipData,
  AnimationTrackData,
  KeyframeData,
  FacialData,
  BlendShapeData,
  FacialPresetData,
  ClothingData,
  ClothingItemData,
  ClothingPhysicsData,
  PhysicsConstraintData,
  DigitalHumanFile
} from './formats/DigitalHumanFormat';

// 数字人转换器
export { DigitalHumanConverter } from './converters/DigitalHumanConverter';

// 照片到3D转换管道
export { PhotoTo3DPipeline } from './generation/PhotoTo3DPipeline';

// AI
export { EmotionBasedAnimationGenerator } from './ai/EmotionBasedAnimationGenerator';
export type { EmotionAnalysisResult } from './ai/EmotionBasedAnimationGenerator';
export { AIModel } from './ai/AIModel';
export type { AnimationGenerationRequest, AnimationGenerationResult, EmotionType } from './ai/AnimationGenerationTypes';

// AI处理服务
export {
  FaceDetectionService,
  Face3DReconstructionService,
  TextureGenerationService,
  AIProcessingManager
} from './ai';
export type {
  FaceDetectionConfig,
  FaceDetectionResult,
  FaceInfo,
  FaceLandmark,
  LandmarkType,
  Face3DReconstructionConfig,
  ProcessedPhoto,
  FaceFeatures,
  FaceMesh,
  TextureGenerationConfig,
  FaceTexture,
  UVMapping,
  AIProcessingManagerConfig,
  DigitalHumanGenerationRequest as AIDigitalHumanGenerationRequest,
  DigitalHumanGenerationResult
} from './ai';

// BIP骨骼支持
export {
  BIPSkeletonParser,
  BIPToStandardMapping
} from './bip';
export type {
  BIPBone,
  BIPBoneType,
  BIPSkeletonData,
  BIPBoneHierarchy,
  BIPBoneNode,
  BIPParserConfig,
  DOFLimits,
  StandardBoneType,
  StandardBone,
  StandardSkeletonData,
  StandardBoneHierarchy,
  StandardBoneNode,
  BoneMappingRule,
  MappingConfig
} from './bip';

// 数字人文件格式
export {
  DIGITAL_HUMAN_PACKAGE_VERSION,
  DIGITAL_HUMAN_PACKAGE_EXTENSION,
  DIGITAL_HUMAN_PACKAGE_MIME_TYPE,
  PACKAGE_CONSTANTS,
  DigitalHumanPackageManager
} from './formats';
export type {
  DigitalHumanPackageHeader,
  DigitalHumanMetadata as DHPackageMetadata, // 重命名避免冲突
  GeometryInfo,
  TextureInfo,
  SkeletonInfo,
  AnimationInfo,
  ClothingInfo,
  ExpressionInfo,
  DigitalHumanPackageContent,
  PackageManifest,
  PackageFile,
  PackageValidationResult,
  PackageBuildOptions,
  PackageParseOptions,
  PackageManagerConfig
} from './formats';

// 高级换装系统
export { ClothingSystem } from './clothing/ClothingSystem';
export type {
  ClothingSystemConfig,
  ClothingCategory,
  ClothingMaterialType,
  ClothingPhysics,
  ClothingFittingParams,
  ClothingItem,
  FittedClothing,
  ClothingOutfit
} from './clothing/ClothingSystem';

// 交互系统
export { SceneInteractionSystem } from './interaction/SceneInteractionSystem';

// 其他重要导出
export type { BIPImportResult } from './systems/BIPIntegrationSystem';
