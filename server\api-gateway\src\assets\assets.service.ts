/**
 * 资产服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AssetsService {
  private readonly logger = new Logger(AssetsService.name);

  constructor(@Inject('ASSET_SERVICE') private readonly assetService: ClientProxy) {}

  /**
   * 创建资产
   */
  async create(userId: string, createAssetDto: any) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'createAsset' }, { userId, ...createAssetDto }),
      );
    } catch (error) {
      this.logger.error('创建资产失败', error);
      throw error;
    }
  }

  /**
   * 上传资产
   */
  async upload(userId: string, file: any, createAssetDto: any) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'uploadAsset' }, { userId, file, ...createAssetDto }),
      );
    } catch (error) {
      this.logger.error('上传资产失败', error);
      throw error;
    }
  }

  /**
   * 获取所有资产
   */
  async findAll(userId: string, projectId?: string, type?: string, tags?: string[]) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'findAllAssets' }, { userId, projectId, type, tags }),
      );
    } catch (error) {
      this.logger.error('获取所有资产失败', error);
      throw error;
    }
  }

  /**
   * 搜索资产
   */
  async search(userId: string, query: string, type?: string) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'searchAssets' }, { userId, query, type }),
      );
    } catch (error) {
      this.logger.error('搜索资产失败', error);
      throw error;
    }
  }

  /**
   * 根据ID获取资产
   */
  async findOne(id: string, userId: string) {
    try {
      return await firstValueFrom(this.assetService.send({ cmd: 'findAssetById' }, { id, userId }));
    } catch (error) {
      this.logger.error(`获取资产ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 更新资产
   */
  async update(id: string, userId: string, updateAssetDto: any) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'updateAsset' }, { id, userId, ...updateAssetDto }),
      );
    } catch (error) {
      this.logger.error(`更新资产ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 删除资产
   */
  async remove(id: string, userId: string) {
    try {
      return await firstValueFrom(this.assetService.send({ cmd: 'removeAsset' }, { id, userId }));
    } catch (error) {
      this.logger.error(`删除资产ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 上传新版本
   */
  async uploadVersion(id: string, userId: string, file: any) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'uploadAssetVersion' }, { id, userId, file }),
      );
    } catch (error) {
      this.logger.error(`上传资产ID ${id} 新版本失败`, error);
      throw error;
    }
  }

  /**
   * 获取资产版本
   */
  async getVersion(id: string, versionId: string, userId: string) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'getAssetVersion' }, { id, versionId, userId }),
      );
    } catch (error) {
      this.logger.error(`获取资产ID ${id} 版本ID ${versionId} 失败`, error);
      throw error;
    }
  }

  /**
   * 获取资产文件路径
   */
  async getAssetFilePath(id: string, versionId: string, userId: string) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'getAssetFilePath' }, { id, versionId, userId }),
      );
    } catch (error) {
      this.logger.error(`获取资产ID ${id} 版本ID ${versionId} 文件路径失败`, error);
      throw error;
    }
  }

  /**
   * 获取所有模型
   */
  async findAllModels(userId: string, projectId?: string, tags?: string[]) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'findAllModels' }, { userId, projectId, tags }),
      );
    } catch (error) {
      this.logger.error('获取所有模型失败', error);
      throw error;
    }
  }

  /**
   * 搜索模型
   */
  async searchModels(userId: string, query: string) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'searchModels' }, { userId, query }),
      );
    } catch (error) {
      this.logger.error('搜索模型失败', error);
      throw error;
    }
  }

  /**
   * 获取所有纹理
   */
  async findAllTextures(userId: string, projectId?: string, tags?: string[]) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'findAllTextures' }, { userId, projectId, tags }),
      );
    } catch (error) {
      this.logger.error('获取所有纹理失败', error);
      throw error;
    }
  }

  /**
   * 搜索纹理
   */
  async searchTextures(userId: string, query: string) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'searchTextures' }, { userId, query }),
      );
    } catch (error) {
      this.logger.error('搜索纹理失败', error);
      throw error;
    }
  }

  /**
   * 获取所有音频
   */
  async findAllAudio(userId: string, projectId?: string, tags?: string[]) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'findAllAudio' }, { userId, projectId, tags }),
      );
    } catch (error) {
      this.logger.error('获取所有音频失败', error);
      throw error;
    }
  }

  /**
   * 搜索音频
   */
  async searchAudio(userId: string, query: string) {
    try {
      return await firstValueFrom(
        this.assetService.send({ cmd: 'searchAudio' }, { userId, query }),
      );
    } catch (error) {
      this.logger.error('搜索音频失败', error);
      throw error;
    }
  }
}
