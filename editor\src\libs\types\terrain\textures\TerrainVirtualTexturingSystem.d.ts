/**
 * 地形虚拟纹理系统
 * 管理地形虚拟纹理，集成到引擎系统中
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { TerrainComponent } from '../components/TerrainComponent';
import { TerrainVirtualTexturing, VirtualTextureConfig } from './TerrainVirtualTexturing';
/**
 * 地形虚拟纹理系统事件类型
 */
export declare enum TerrainVirtualTexturingSystemEventType {
    /** 系统初始化 */
    SYSTEM_INITIALIZED = "system_initialized",
    /** 地形实体添加 */
    TERRAIN_ENTITY_ADDED = "terrain_entity_added",
    /** 地形实体移除 */
    TERRAIN_ENTITY_REMOVED = "terrain_entity_removed",
    /** 虚拟纹理更新 */
    VIRTUAL_TEXTURE_UPDATED = "virtual_texture_updated",
    /** 错误 */
    ERROR = "error"
}
/**
 * 地形虚拟纹理系统配置
 */
export interface TerrainVirtualTexturingSystemConfig extends VirtualTextureConfig {
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
}
/**
 * 地形虚拟纹理系统
 */
export declare class TerrainVirtualTexturingSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 帧计数器 */
    private frameCount;
    /** 虚拟纹理 */
    private virtualTexturing;
    /** 事件发射器 */
    private eventEmitter;
    /** 地形实体映射 */
    private terrainEntities;
    /** 活跃相机 */
    private activeCamera;
    /** 活跃场景 */
    private activeScene;
    /** 渲染器 */
    private renderer;
    /** 反馈场景 */
    private feedbackScene;
    /** 反馈相机 */
    private feedbackCamera;
    /** 反馈四边形 */
    private feedbackQuad;
    /**
     * 创建地形虚拟纹理系统
     * @param config 配置
     */
    constructor(config?: TerrainVirtualTexturingSystemConfig);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 注册虚拟纹理事件
     */
    private registerVirtualTexturingEvents;
    /**
     * 查找活跃相机
     */
    private findActiveCamera;
    /**
     * 查找活跃场景
     */
    private findActiveScene;
    /**
     * 查找地形实体
     */
    private findTerrainEntities;
    /**
     * 创建反馈渲染设置
     */
    private createFeedbackRenderSetup;
    /**
     * 添加地形实体
     * @param entity 实体
     * @param component 地形组件
     */
    addTerrainEntity(entity: Entity, component: TerrainComponent): void;
    /**
     * 移除地形实体
     * @param entity 实体
     */
    removeTerrainEntity(entity: Entity): void;
    /**
     * 设置渲染器
     * @param renderer 渲染器
     */
    setRenderer(renderer: THREE.WebGLRenderer): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新反馈纹理
     */
    private updateFeedbackTexture;
    /**
     * 获取虚拟纹理
     * @returns 虚拟纹理
     */
    getVirtualTexturing(): TerrainVirtualTexturing;
    /**
     * 获取物理纹理
     * @returns 物理纹理
     */
    getPhysicalTexture(): THREE.Texture | null;
    /**
     * 注册事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: TerrainVirtualTexturingSystemEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: TerrainVirtualTexturingSystemEventType, listener: (...args: any[]) => void): void;
}
