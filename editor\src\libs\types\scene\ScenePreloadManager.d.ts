/**
 * 场景预加载管理器
 * 负责场景资源的依赖分析、预加载和按需加载
 */
import * as THREE from 'three';
import { AssetManager } from '../assets/AssetManager';
import { ScenePreloader, ScenePreloadProgressInfo } from './ScenePreloader';
import { Scene } from './Scene';
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 预加载策略类型
 */
export declare enum PreloadStrategyType {
    /** 距离策略 - 基于相机距离预加载 */
    DISTANCE = "distance",
    /** 视锥体策略 - 基于视锥体可见性预加载 */
    FRUSTUM = "frustum",
    /** 方向策略 - 基于相机朝向预加载 */
    DIRECTION = "direction",
    /** 层级策略 - 基于场景层级预加载 */
    HIERARCHY = "hierarchy",
    /** 兴趣点策略 - 基于兴趣点预加载 */
    POINT_OF_INTEREST = "poi",
    /** 预测策略 - 基于移动预测预加载 */
    PREDICTION = "prediction",
    /** 自定义策略 */
    CUSTOM = "custom"
}
/**
 * 预加载配置
 */
export interface PreloadConfig {
    /** 是否启用预加载 */
    enabled: boolean;
    /** 预加载优先级 */
    priority: number;
    /** 预加载策略类型 */
    strategyType: PreloadStrategyType;
    /** 预加载距离 */
    distance: number;
    /** 方向预加载角度（度） */
    directionAngle?: number;
    /** 预测时间（秒） */
    predictionTime?: number;
    /** 是否预加载子实体 */
    includeChildren: boolean;
    /** 是否预加载材质 */
    includeMaterials: boolean;
    /** 是否预加载纹理 */
    includeTextures: boolean;
    /** 是否预加载音频 */
    includeAudio: boolean;
    /** 是否预加载模型 */
    includeModels: boolean;
    /** 是否使用LOD */
    useLOD?: boolean;
    /** 是否使用纹理压缩 */
    useTextureCompression?: boolean;
    /** 是否使用几何体简化 */
    useGeometrySimplification?: boolean;
    /** 是否使用实例化 */
    useInstancing?: boolean;
    /** 最大同时加载资源数 */
    maxConcurrentLoads?: number;
    /** 内存限制（MB） */
    memoryLimit?: number;
    /** 自定义数据 */
    userData?: Record<string, any>;
}
/**
 * 资源依赖信息
 */
export interface ResourceDependencyInfo {
    /** 资源ID */
    id: string;
    /** 资源URL */
    url: string;
    /** 资源类型 */
    type: string;
    /** 依赖的资源ID列表 */
    dependencies: string[];
    /** 资源大小（字节） */
    size: number;
    /** 是否已加载 */
    loaded: boolean;
    /** 是否正在加载 */
    loading: boolean;
    /** 加载优先级 */
    priority: number;
}
/**
 * 预加载区域
 */
export interface PreloadRegion {
    /** 区域ID */
    id: string;
    /** 区域名称 */
    name: string;
    /** 区域中心位置 */
    position: THREE.Vector3;
    /** 区域半径 */
    radius: number;
    /** 预加载配置 */
    config: PreloadConfig;
    /** 区域内的实体ID列表 */
    entityIds: string[];
    /** 区域内的资源ID列表 */
    resourceIds: string[];
    /** 是否已加载 */
    loaded: boolean;
    /** 加载进度（0-1） */
    progress: number;
}
/**
 * 场景预加载管理器选项
 */
export interface ScenePreloadManagerOptions {
    /** 资产管理器 */
    assetManager: AssetManager;
    /** 场景预加载器 */
    scenePreloader?: ScenePreloader;
    /** 是否启用自动预加载 */
    enableAutoPreload?: boolean;
    /** 是否启用资源依赖分析 */
    enableDependencyAnalysis?: boolean;
    /** 是否启用按需加载 */
    enableOnDemandLoading?: boolean;
    /** 是否启用调试模式 */
    debug?: boolean;
}
/**
 * 场景预加载管理器
 */
export declare class ScenePreloadManager extends EventEmitter {
    /** 资产管理器 */
    private assetManager;
    /** 场景预加载器 */
    private scenePreloader;
    /** 是否启用自动预加载 */
    private enableAutoPreload;
    /** 是否启用资源依赖分析 */
    private enableDependencyAnalysis;
    /** 是否启用按需加载 */
    private enableOnDemandLoading;
    /** 是否启用调试模式 */
    private debug;
    /** 预加载区域映射 */
    private regions;
    /** 资源依赖映射 */
    private resourceDependencies;
    /** 实体资源映射 */
    private entityResources;
    /** 当前活动场景 */
    private activeScene;
    /** 当前相机位置 */
    private cameraPosition;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建场景预加载管理器
     * @param options 选项
     */
    constructor(options: ScenePreloadManagerOptions);
    /**
     * 初始化预加载管理器
     */
    initialize(): void;
    /**
     * 设置活动场景
     * @param scene 场景
     */
    setActiveScene(scene: Scene | null): void;
    /**
     * 设置相机位置
     * @param position 位置
     */
    setCameraPosition(position: THREE.Vector3): void;
    /**
     * 分析场景资源
     * @param scene 场景
     */
    analyzeSceneResources(scene: Scene): void;
    /**
     * 分析资源依赖
     * @param resources 资源信息数组
     */
    private analyzeResourceDependencies;
    /**
     * 分析实体资源
     * @param scene 场景
     */
    private analyzeEntityResources;
    /**
     * 递归分析实体资源
     * @param entity 实体
     */
    private analyzeEntityResourcesRecursive;
    /**
     * 创建预加载区域
     * @param options 区域选项
     * @returns 区域ID
     */
    createPreloadRegion(options: Partial<PreloadRegion>): string;
    /**
     * 更新预加载区域
     */
    private updatePreloadRegions;
    /**
     * 基于距离更新区域
     * @param region 区域
     * @param camera 相机
     */
    private updateRegionByDistance;
    /**
     * 基于视锥体更新区域
     * @param region 区域
     * @param frustum 视锥体
     */
    private updateRegionByFrustum;
    /**
     * 基于方向更新区域
     * @param region 区域
     * @param camera 相机
     * @param cameraDirection 相机方向
     */
    private updateRegionByDirection;
    /**
     * 基于层级更新区域
     * @param region 区域
     */
    private updateRegionByHierarchy;
    /**
     * 基于兴趣点更新区域
     * @param region 区域
     */
    private updateRegionByPOI;
    /**
     * 基于预测更新区域
     * @param region 区域
     * @param camera 相机
     */
    private updateRegionByPrediction;
    /**
     * 基于自定义策略更新区域
     * @param region 区域
     */
    private updateRegionByCustom;
    /**
     * 加载区域
     * @param regionId 区域ID
     */
    loadRegion(regionId: string): void;
    /**
     * 加载区域资源
     * @param region 区域
     */
    private loadRegionResources;
    /**
     * 卸载区域
     * @param regionId 区域ID
     */
    unloadRegion(regionId: string): void;
    /**
     * 预加载场景
     * @param scene 场景
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    preloadScene(scene: Scene, onProgress?: (progress: ScenePreloadProgressInfo) => void): Promise<ScenePreloadProgressInfo>;
    /**
     * 销毁预加载管理器
     */
    dispose(): void;
}
