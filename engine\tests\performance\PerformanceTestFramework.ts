/**
 * DL（Digital Learning）引擎性能测试框架
 * 用于测试引擎各个模块的性能
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Camera } from '../../src/rendering/Camera';
import { Time } from '../../src/utils/Time';
import { Debug } from '../../src/utils/Debug';

/**
 * 性能测试结果
 */
export interface PerformanceTestResult {
  /** 测试名称 */
  name: string;
  /** 测试是否通过 */
  passed: boolean;
  /** 错误信息 */
  errorMessage?: string;
  /** 性能数据 */
  performanceData: {
    /** 帧率 */
    fps: number;
    /** 渲染时间（毫秒） */
    renderTime: number;
    /** CPU使用率 */
    cpuUsage?: number;
    /** GPU使用率 */
    gpuUsage?: number;
    /** 内存使用（MB） */
    memoryUsage: number;
    /** 其他自定义指标 */
    [key: string]: any;
  };
}

/**
 * 性能测试配置
 */
export interface PerformanceTestConfig {
  /** 测试名称 */
  name: string;
  /** 测试描述 */
  description?: string;
  /** 测试持续时间（毫秒） */
  duration?: number;
  /** 预热时间（毫秒） */
  warmupDuration?: number;
  /** 采样间隔（毫秒） */
  sampleInterval?: number;
  /** 性能阈值 */
  thresholds?: {
    /** 最低帧率 */
    minFPS?: number;
    /** 最大渲染时间（毫秒） */
    maxRenderTime?: number;
    /** 最大内存使用（MB） */
    maxMemoryUsage?: number;
    /** 其他自定义阈值 */
    [key: string]: any;
  };
}

/**
 * 性能测试基类
 */
export abstract class PerformanceTest {
  /** 测试配置 */
  protected config: PerformanceTestConfig;
  /** 引擎实例 */
  protected engine: Engine;
  /** 世界实例 */
  protected world: World;
  /** 场景实例 */
  protected scene: Scene;
  /** 相机实例 */
  protected camera: Camera;
  /** 性能数据 */
  protected performanceData: {
    /** 帧率历史 */
    fpsHistory: number[];
    /** 渲染时间历史（毫秒） */
    renderTimeHistory: number[];
    /** 内存使用历史（MB） */
    memoryUsageHistory: number[];
    /** 其他自定义指标历史 */
    [key: string]: any[];
  };
  /** 测试是否运行中 */
  protected running: boolean = false;
  /** 测试开始时间 */
  protected startTime: number = 0;
  /** 上一次采样时间 */
  protected lastSampleTime: number = 0;
  /** 帧计数器 */
  protected frameCount: number = 0;
  /** 测试结果回调 */
  protected onResultCallback?: (result: PerformanceTestResult) => void;

  /**
   * 创建性能测试
   * @param config 测试配置
   */
  constructor(config: PerformanceTestConfig) {
    this.config = {
      ...config,
      duration: config.duration || 5000,
      warmupDuration: config.warmupDuration || 1000,
      sampleInterval: config.sampleInterval || 100,
    };

    // 初始化性能数据
    this.performanceData = {
      fpsHistory: [],
      renderTimeHistory: [],
      memoryUsageHistory: [],
    };

    // 创建引擎
    this.engine = new Engine({
      debug: false,
    });

    // 获取世界
    this.world = this.engine.getWorld();

    // 创建场景
    this.scene = new Scene();
    this.world.addScene(this.scene);

    // 创建相机
    this.camera = new Camera();
    const cameraEntity = new Entity();
    cameraEntity.addComponent(this.camera);
    cameraEntity.transform.setPosition(0, 2, 5);
    this.scene.addEntity(cameraEntity);
  }

  /**
   * 运行测试
   * @param onResult 测试结果回调
   */
  public run(onResult?: (result: PerformanceTestResult) => void): void {
    if (this.running) {
      return;
    }

    this.onResultCallback = onResult;
    this.running = true;
    this.startTime = performance.now();
    this.lastSampleTime = this.startTime;
    this.frameCount = 0;

    // 初始化测试
    this.initialize();

    // 启动引擎
    this.engine.start();

    // 设置更新回调
    this.engine.on('update', this.update.bind(this));
  }

  /**
   * 停止测试
   */
  public stop(): void {
    if (!this.running) {
      return;
    }

    this.running = false;
    this.engine.off('update', this.update.bind(this));
    this.engine.stop();

    // 清理测试
    this.cleanup();
  }

  /**
   * 初始化测试
   * 子类应该重写此方法以设置测试场景
   */
  protected abstract initialize(): void;

  /**
   * 更新测试
   * @param deltaTime 帧间隔时间（秒）
   */
  protected update(deltaTime: number): void {
    const currentTime = performance.now();
    const elapsedTime = currentTime - this.startTime;

    // 增加帧计数
    this.frameCount++;

    // 如果在预热阶段，不收集数据
    if (elapsedTime < this.config.warmupDuration!) {
      return;
    }

    // 采样间隔
    if (currentTime - this.lastSampleTime >= this.config.sampleInterval!) {
      this.collectSample(currentTime);
      this.lastSampleTime = currentTime;
    }

    // 测试时间到，结束测试
    if (elapsedTime >= this.config.warmupDuration! + this.config.duration!) {
      this.finishTest();
    }
  }

  /**
   * 收集性能样本
   * @param currentTime 当前时间
   */
  protected collectSample(currentTime: number): void {
    const sampleInterval = currentTime - this.lastSampleTime;
    const fps = (this.frameCount * 1000) / sampleInterval;
    const renderTime = this.engine.getLastFrameTime();
    const memoryUsage = this.getMemoryUsage();

    // 记录性能数据
    this.performanceData.fpsHistory.push(fps);
    this.performanceData.renderTimeHistory.push(renderTime);
    this.performanceData.memoryUsageHistory.push(memoryUsage);

    // 收集自定义性能数据
    this.collectCustomPerformanceData();

    // 重置帧计数
    this.frameCount = 0;

    // 输出调试信息
    Debug.log(`性能测试 ${this.config.name}`, `FPS: ${fps.toFixed(2)}, 渲染时间: ${renderTime.toFixed(2)}ms, 内存: ${memoryUsage.toFixed(2)}MB`);
  }

  /**
   * 收集自定义性能数据
   * 子类可以重写此方法以收集额外的性能数据
   */
  protected collectCustomPerformanceData(): void {
    // 子类实现
  }

  /**
   * 完成测试
   */
  protected finishTest(): void {
    // 停止测试
    this.stop();

    // 计算平均性能数据
    const avgFPS = this.calculateAverage(this.performanceData.fpsHistory);
    const avgRenderTime = this.calculateAverage(this.performanceData.renderTimeHistory);
    const avgMemoryUsage = this.calculateAverage(this.performanceData.memoryUsageHistory);

    // 检查是否通过阈值
    const thresholds = this.config.thresholds || {};
    let passed = true;
    let errorMessage = '';

    if (thresholds.minFPS && avgFPS < thresholds.minFPS) {
      passed = false;
      errorMessage += `平均帧率 ${avgFPS.toFixed(2)} 低于阈值 ${thresholds.minFPS}; `;
    }

    if (thresholds.maxRenderTime && avgRenderTime > thresholds.maxRenderTime) {
      passed = false;
      errorMessage += `平均渲染时间 ${avgRenderTime.toFixed(2)}ms 高于阈值 ${thresholds.maxRenderTime}ms; `;
    }

    if (thresholds.maxMemoryUsage && avgMemoryUsage > thresholds.maxMemoryUsage) {
      passed = false;
      errorMessage += `平均内存使用 ${avgMemoryUsage.toFixed(2)}MB 高于阈值 ${thresholds.maxMemoryUsage}MB; `;
    }

    // 创建测试结果
    const result: PerformanceTestResult = {
      name: this.config.name,
      passed,
      errorMessage: errorMessage || undefined,
      performanceData: {
        fps: avgFPS,
        renderTime: avgRenderTime,
        memoryUsage: avgMemoryUsage,
        ...this.calculateCustomPerformanceData(),
      },
    };

    // 调用结果回调
    if (this.onResultCallback) {
      this.onResultCallback(result);
    }
  }

  /**
   * 计算自定义性能数据
   * 子类可以重写此方法以计算额外的性能数据
   * @returns 自定义性能数据
   */
  protected calculateCustomPerformanceData(): { [key: string]: any } {
    return {};
  }

  /**
   * 清理测试
   * 子类可以重写此方法以清理测试资源
   */
  protected cleanup(): void {
    // 子类实现
  }

  /**
   * 计算数组平均值
   * @param array 数值数组
   * @returns 平均值
   */
  protected calculateAverage(array: number[]): number {
    if (array.length === 0) {
      return 0;
    }
    return array.reduce((sum, value) => sum + value, 0) / array.length;
  }

  /**
   * 获取内存使用（MB）
   * @returns 内存使用（MB）
   */
  protected getMemoryUsage(): number {
    // 在浏览器环境中
    if (typeof window !== 'undefined' && window.performance && window.performance.memory) {
      return (window.performance as any).memory.usedJSHeapSize / (1024 * 1024);
    }
    // 在Node.js环境中
    else if (typeof process !== 'undefined' && process.memoryUsage) {
      const memoryUsage = process.memoryUsage();
      return memoryUsage.heapUsed / (1024 * 1024);
    }
    return 0;
  }
}
