/**
 * RenderSystem类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import { Renderer } from '../../src/rendering/Renderer';
import { Camera, CameraType } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { Scene } from '../../src/scene/Scene';

describe('RenderSystem', () => {
  let engine: Engine;
  let world: World;
  let renderSystem: RenderSystem;
  let scene: Scene;
  let cameraEntity: Entity;
  let camera: Camera;
  
  // 在每个测试前创建一个新的渲染系统实例
  beforeEach(() => {
    // 创建模拟的canvas元素
    const canvas = document.createElement('canvas');
    
    // 创建引擎和世界
    engine = new Engine({
      canvas,
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建渲染系统
    renderSystem = new RenderSystem({
      canvas,
      antialias: true,
      shadows: true,
      pixelRatio: 1
    });
    
    // 添加渲染系统到引擎
    engine.addSystem(renderSystem);
    
    // 创建场景
    scene = new Scene('测试场景');
    world.setActiveScene(scene);
    
    // 创建相机实体
    cameraEntity = new Entity(world);
    cameraEntity.name = '相机';
    
    // 添加变换组件
    const cameraTransform = new Transform({
      position: { x: 0, y: 5, z: 10 },
      rotation: { x: -0.2, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    cameraEntity.addComponent(cameraTransform);
    
    // 创建相机组件
    camera = new Camera({
      type: CameraType.PERSPECTIVE,
      fov: 75,
      aspect: 1,
      near: 0.1,
      far: 1000
    });
    cameraEntity.addComponent(camera);
    
    // 添加相机实体到场景
    scene.addEntity(cameraEntity);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试渲染系统初始化
  it('应该正确初始化渲染系统', () => {
    expect(renderSystem).toBeDefined();
    expect(renderSystem['renderer']).toBeDefined();
    expect(renderSystem['activeCamera']).toBeDefined();
    expect(renderSystem['activeCamera']).toBe(camera);
  });
  
  // 测试设置活跃相机
  it('应该能够设置活跃相机', () => {
    // 创建另一个相机实体
    const cameraEntity2 = new Entity(world);
    cameraEntity2.name = '相机2';
    
    // 添加变换组件
    const cameraTransform2 = new Transform({
      position: { x: 10, y: 5, z: 10 },
      rotation: { x: -0.2, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    cameraEntity2.addComponent(cameraTransform2);
    
    // 创建相机组件
    const camera2 = new Camera({
      type: CameraType.PERSPECTIVE,
      fov: 60,
      aspect: 1,
      near: 0.1,
      far: 1000
    });
    cameraEntity2.addComponent(camera2);
    
    // 添加相机实体到场景
    scene.addEntity(cameraEntity2);
    
    // 设置活跃相机
    renderSystem.setActiveCamera(camera2);
    
    // 验证活跃相机已更改
    expect(renderSystem['activeCamera']).toBe(camera2);
  });
  
  // 测试渲染场景
  it('应该能够渲染场景', () => {
    // 创建渲染方法的模拟
    const renderSpy = vi.spyOn(renderSystem['renderer'], 'render');
    
    // 更新渲染系统
    renderSystem.update(0.016);
    
    // 验证渲染方法被调用
    expect(renderSpy).toHaveBeenCalled();
  });
  
  // 测试添加光源
  it('应该能够添加光源', () => {
    // 创建光源实体
    const lightEntity = new Entity(world);
    lightEntity.name = '光源';
    
    // 添加变换组件
    const lightTransform = new Transform({
      position: { x: 10, y: 10, z: 10 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    lightEntity.addComponent(lightTransform);
    
    // 创建光源组件
    const light = new Light({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1,
      castShadow: true
    });
    lightEntity.addComponent(light);
    
    // 添加光源实体到场景
    scene.addEntity(lightEntity);
    
    // 注册光源组件
    renderSystem.registerLightComponent(lightEntity, light);
    
    // 验证光源已添加
    expect(renderSystem['lights'].has(lightEntity.getId())).toBe(true);
    expect(renderSystem['lights'].get(lightEntity.getId())).toBe(light);
    
    // 验证光源已添加到THREE.js场景
    const threeLight = light.getThreeLight();
    expect(renderSystem['threeScene'].children).toContain(threeLight);
  });
  
  // 测试移除光源
  it('应该能够移除光源', () => {
    // 创建光源实体
    const lightEntity = new Entity(world);
    lightEntity.name = '光源';
    
    // 添加变换组件
    const lightTransform = new Transform({
      position: { x: 10, y: 10, z: 10 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    lightEntity.addComponent(lightTransform);
    
    // 创建光源组件
    const light = new Light({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1,
      castShadow: true
    });
    lightEntity.addComponent(light);
    
    // 添加光源实体到场景
    scene.addEntity(lightEntity);
    
    // 注册光源组件
    renderSystem.registerLightComponent(lightEntity, light);
    
    // 验证光源已添加
    expect(renderSystem['lights'].has(lightEntity.getId())).toBe(true);
    
    // 获取THREE.js光源
    const threeLight = light.getThreeLight();
    
    // 取消注册光源组件
    renderSystem.unregisterLightComponent(lightEntity);
    
    // 验证光源已移除
    expect(renderSystem['lights'].has(lightEntity.getId())).toBe(false);
    
    // 验证光源已从THREE.js场景移除
    expect(renderSystem['threeScene'].children).not.toContain(threeLight);
  });
  
  // 测试阴影设置
  it('应该能够设置阴影', () => {
    // 启用阴影
    renderSystem.setShadowsEnabled(true);
    
    // 验证阴影已启用
    expect(renderSystem['renderer'].shadowMap.enabled).toBe(true);
    
    // 禁用阴影
    renderSystem.setShadowsEnabled(false);
    
    // 验证阴影已禁用
    expect(renderSystem['renderer'].shadowMap.enabled).toBe(false);
  });
  
  // 测试后处理效果
  it('应该能够添加后处理效果', () => {
    // 创建后处理效果
    const effect = {
      name: 'TestEffect',
      pass: new THREE.ShaderPass(new THREE.ShaderMaterial()),
      enabled: true
    };
    
    // 添加后处理效果
    renderSystem.addPostProcessingEffect(effect);
    
    // 验证后处理效果已添加
    expect(renderSystem['postProcessingEffects']).toContain(effect);
    
    // 禁用后处理效果
    renderSystem.enablePostProcessingEffect('TestEffect', false);
    
    // 验证后处理效果已禁用
    expect(effect.enabled).toBe(false);
    
    // 启用后处理效果
    renderSystem.enablePostProcessingEffect('TestEffect', true);
    
    // 验证后处理效果已启用
    expect(effect.enabled).toBe(true);
    
    // 移除后处理效果
    renderSystem.removePostProcessingEffect('TestEffect');
    
    // 验证后处理效果已移除
    expect(renderSystem['postProcessingEffects']).not.toContain(effect);
  });
  
  // 测试渲染统计信息
  it('应该能够获取渲染统计信息', () => {
    // 获取渲染统计信息
    const stats = renderSystem.getStats();
    
    // 验证统计信息
    expect(stats).toBeDefined();
    expect(stats.fps).toBeDefined();
    expect(stats.triangles).toBeDefined();
    expect(stats.calls).toBeDefined();
  });
  
  // 测试渲染器调整大小
  it('应该能够调整渲染器大小', () => {
    // 创建调整大小方法的模拟
    const resizeSpy = vi.spyOn(renderSystem['renderer'], 'setSize');
    
    // 调整渲染器大小
    renderSystem.resize(800, 600);
    
    // 验证调整大小方法被调用
    expect(resizeSpy).toHaveBeenCalledWith(800, 600);
    
    // 验证相机宽高比已更新
    expect(camera.getAspect()).toBe(800 / 600);
  });
});
