/**
 * 地形片段着色器
 */
export const terrainFragmentShader = `
// 基础参数
uniform vec2 uTerrainSize;
uniform float uMaxHeight;

// 纹理参数
uniform int uLayerCount;
uniform sampler2D uTextures[8];
uniform sampler2D uNormalMaps[8];
uniform sampler2D uRoughnessMaps[8];
uniform sampler2D uDisplacementMaps[8];
uniform sampler2D uAOMaps[8];
uniform float uTilingFactors[8];
uniform float uMinHeights[8];
uniform float uMaxHeights[8];
uniform float uMinSlopes[8];
uniform float uMaxSlopes[8];

// 混合参数
uniform sampler2D uBlendMaps[8];
uniform bool uUseBlendMaps;

// 光照参数
uniform vec3 uLightPosition;
uniform vec3 uLightColor;
uniform vec3 uAmbientColor;

// 雾参数
uniform vec3 uFogColor;
uniform float uFogNear;
uniform float uFogFar;
uniform bool uUseFog;

// 从顶点着色器传递的变量
varying vec2 vUv;
varying vec3 vNormal;
varying vec3 vPosition;
varying vec3 vWorldPosition;
varying vec4 vTangent;
varying float vHeight;
varying float vSlope;
varying float vFogFactor;

// 计算混合权重
float calculateBlendWeight(float height, float slope, float minHeight, float maxHeight, float minSlope, float maxSlope) {
  // 高度混合权重
  float heightWeight = 1.0;
  if (height < minHeight || height > maxHeight) {
    heightWeight = 0.0;
  } else {
    // 平滑过渡
    float heightRange = maxHeight - minHeight;
    if (heightRange > 0.0) {
      float heightTransition = 0.1 * heightRange;
      if (height < minHeight + heightTransition) {
        heightWeight = smoothstep(minHeight, minHeight + heightTransition, height);
      } else if (height > maxHeight - heightTransition) {
        heightWeight = 1.0 - smoothstep(maxHeight - heightTransition, maxHeight, height);
      }
    }
  }
  
  // 斜度混合权重
  float slopeWeight = 1.0;
  if (slope < minSlope || slope > maxSlope) {
    slopeWeight = 0.0;
  } else {
    // 平滑过渡
    float slopeRange = maxSlope - minSlope;
    if (slopeRange > 0.0) {
      float slopeTransition = 0.1 * slopeRange;
      if (slope < minSlope + slopeTransition) {
        slopeWeight = smoothstep(minSlope, minSlope + slopeTransition, slope);
      } else if (slope > maxSlope - slopeTransition) {
        slopeWeight = 1.0 - smoothstep(maxSlope - slopeTransition, maxSlope, slope);
      }
    }
  }
  
  return heightWeight * slopeWeight;
}

// 计算法线贴图
vec3 calculateNormalFromMap(sampler2D normalMap, vec2 uv, vec3 normal, vec4 tangent) {
  // 从法线贴图获取法线
  vec3 normalColor = texture2D(normalMap, uv).rgb;
  vec3 normalMapValue = normalColor * 2.0 - 1.0;
  
  // 计算切线空间到世界空间的变换
  vec3 tangentValue = tangent.xyz;
  vec3 bitangent = cross(normal, tangentValue) * tangent.w;
  mat3 tbn = mat3(tangentValue, bitangent, normal);
  
  // 将法线从切线空间转换到世界空间
  return normalize(tbn * normalMapValue);
}

void main() {
  // 初始化颜色和法线
  vec4 finalColor = vec4(0.0);
  vec3 finalNormal = normalize(vNormal);
  float totalWeight = 0.0;
  
  // 计算每层的混合权重和颜色
  for (int i = 0; i < 8; i++) {
    if (i >= uLayerCount) break;
    
    // 计算纹理坐标
    vec2 tiledUv = vUv * uTilingFactors[i];
    
    // 获取混合权重
    float weight = 0.0;
    if (uUseBlendMaps) {
      // 使用混合贴图
      weight = texture2D(uBlendMaps[i], vUv).r;
    } else {
      // 使用高度和斜度计算混合权重
      weight = calculateBlendWeight(vHeight, vSlope, uMinHeights[i], uMaxHeights[i], uMinSlopes[i], uMaxSlopes[i]);
    }
    
    if (weight > 0.0) {
      // 获取纹理颜色
      vec4 textureColor = texture2D(uTextures[i], tiledUv);
      
      // 获取法线贴图
      vec3 normalMapValue = calculateNormalFromMap(uNormalMaps[i], tiledUv, vNormal, vTangent);
      
      // 获取粗糙度贴图
      float roughness = texture2D(uRoughnessMaps[i], tiledUv).r;
      
      // 获取环境光遮蔽贴图
      float ao = texture2D(uAOMaps[i], tiledUv).r;
      
      // 简单的光照计算
      vec3 lightDir = normalize(uLightPosition - vWorldPosition);
      float diffuse = max(dot(normalMapValue, lightDir), 0.0);
      vec3 ambient = uAmbientColor * ao;
      vec3 lighting = ambient + uLightColor * diffuse;
      
      // 混合颜色
      finalColor += vec4(textureColor.rgb * lighting, textureColor.a) * weight;
      
      // 混合法线
      finalNormal = normalize(mix(finalNormal, normalMapValue, weight));
      
      totalWeight += weight;
    }
  }
  
  // 归一化颜色
  if (totalWeight > 0.0) {
    finalColor /= totalWeight;
  } else {
    // 默认颜色
    finalColor = vec4(0.5, 0.5, 0.5, 1.0);
  }
  
  // 应用雾效果
  if (uUseFog) {
    finalColor.rgb = mix(finalColor.rgb, uFogColor, vFogFactor);
  }
  
  gl_FragColor = finalColor;
}
`;
