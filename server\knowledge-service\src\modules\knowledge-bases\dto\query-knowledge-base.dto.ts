import { IsOptional, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { KnowledgeBaseCategory } from './create-knowledge-base.dto';

export class QueryKnowledgeBaseDto {
  @ApiProperty({ description: '搜索关键词', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ 
    description: '分类过滤', 
    enum: KnowledgeBaseCategory,
    required: false 
  })
  @IsOptional()
  @IsEnum(KnowledgeBaseCategory)
  category?: KnowledgeBaseCategory;

  @ApiProperty({ description: '语言过滤', required: false })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({ description: '页码', default: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ description: '每页数量', default: 20, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}
