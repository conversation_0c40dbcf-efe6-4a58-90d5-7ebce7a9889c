/**
 * 资产加载器类
 * 负责加载各种类型的资产
 */
import * as THREE from 'three';
import { AssetType } from './ResourceManager';
export declare class AssetLoader {
    /** Three.js加载管理器 */
    private manager;
    /** 纹理加载器 */
    private textureLoader;
    /** GLTF加载器 */
    private gltfLoader;
    /** FBX加载器 */
    private fbxLoader;
    /** OBJ加载器 */
    private objLoader;
    /** 立方体纹理加载器 */
    private cubeTextureLoader;
    /** 音频加载器 */
    private audioLoader;
    /** 字体加载器 */
    private fontLoader;
    /** 文件加载器 */
    private fileLoader;
    /**
     * 创建资产加载器实例
     */
    constructor();
    /**
     * 加载资产
     * @param type 资产类型
     * @param url 资产URL
     * @returns Promise，解析为加载的资产数据
     */
    load(type: AssetType, url: string): Promise<any>;
    /**
     * 加载纹理
     * @param url 纹理URL
     * @returns Promise，解析为加载的纹理
     */
    private loadTexture;
    /**
     * 加载模型
     * @param url 模型URL
     * @returns Promise，解析为加载的模型
     */
    private loadModel;
    /**
     * 加载GLTF模型
     * @param url GLTF模型URL
     * @returns Promise，解析为加载的GLTF模型
     */
    private loadGLTF;
    /**
     * 加载FBX模型
     * @param url FBX模型URL
     * @returns Promise，解析为加载的FBX模型
     */
    private loadFBX;
    /**
     * 加载OBJ模型
     * @param url OBJ模型URL
     * @returns Promise，解析为加载的OBJ模型
     */
    private loadOBJ;
    /**
     * 加载音频
     * @param url 音频URL
     * @returns Promise，解析为加载的音频数据
     */
    private loadAudio;
    /**
     * 加载字体
     * @param url 字体URL
     * @returns Promise，解析为加载的字体
     */
    private loadFont;
    /**
     * 加载JSON
     * @param url JSON URL
     * @returns Promise，解析为加载的JSON数据
     */
    private loadJSON;
    /**
     * 加载文本
     * @param url 文本URL
     * @returns Promise，解析为加载的文本
     */
    private loadText;
    /**
     * 加载二进制数据
     * @param url 二进制数据URL
     * @returns Promise，解析为加载的二进制数据
     */
    private loadBinary;
    /**
     * 加载立方体纹理
     * @param urls 立方体纹理URL数组（顺序：右、左、上、下、前、后）
     * @returns Promise，解析为加载的立方体纹理
     */
    private loadCubeTexture;
    /**
     * 获取加载管理器
     * @returns 加载管理器
     */
    getManager(): THREE.LoadingManager;
    /**
     * 设置加载基础路径
     * @param path 基础路径
     */
    setPath(path: string): void;
    /**
     * 设置跨域
     * @param crossOrigin 跨域设置
     */
    setCrossOrigin(crossOrigin: string): void;
    /**
     * 销毁加载器
     */
    dispose(): void;
}
