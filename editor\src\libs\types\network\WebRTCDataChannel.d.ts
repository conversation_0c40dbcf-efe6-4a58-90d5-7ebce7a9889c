/**
 * WebRTC数据通道
 * 用于WebRTC点对点数据传输
 */
import { EventEmitter } from '../utils/EventEmitter';
export interface WebRTCDataChannelOptions {
    /** 通道标签 */
    label: string;
    /** 是否有序 */
    ordered?: boolean;
    /** 最大重传次数 */
    maxRetransmits?: number;
    /** 最大重传时间 */
    maxRetransmitTime?: number;
    /** 协议 */
    protocol?: string;
    /** 是否协商 */
    negotiated?: boolean;
    /** 通道ID */
    id?: number;
}
/**
 * WebRTC数据通道事件
 */
export interface WebRTCDataChannelEvents {
    /** 通道打开事件 */
    open: () => void;
    /** 通道关闭事件 */
    close: () => void;
    /** 通道错误事件 */
    error: (error: Event) => void;
    /** 消息接收事件 */
    message: (data: any) => void;
}
export declare class WebRTCDataChannel extends EventEmitter {
    private channel;
    private _options;
    private isDestroyed;
    constructor(channel: RTCDataChannel, options: WebRTCDataChannelOptions);
    private setupEventHandlers;
    private handleMessage;
    send(data: string | ArrayBuffer | Blob): void;
    close(): void;
    /**
     * 销毁数据通道
     */
    destroy(): void;
    /**
     * 检查通道是否已销毁
     */
    get destroyed(): boolean;
    /**
     * 获取通道状态
     */
    get readyState(): RTCDataChannelState;
    /**
     * 获取通道标签
     */
    get label(): string;
    /**
     * 获取通道ID
     */
    get id(): number | null;
    /**
     * 获取通道协议
     */
    get protocol(): string;
    /**
     * 获取通道配置选项
     */
    get options(): WebRTCDataChannelOptions;
    /**
     * 获取缓冲数据量
     */
    get bufferedAmount(): number;
    /**
     * 获取缓冲数据量低水位线
     */
    get bufferedAmountLowThreshold(): number;
    /**
     * 设置缓冲数据量低水位线
     */
    set bufferedAmountLowThreshold(threshold: number);
    /**
     * 检查通道是否已连接
     */
    get isConnected(): boolean;
    /**
     * 检查通道是否正在连接
     */
    get isConnecting(): boolean;
    /**
     * 检查通道是否已关闭
     */
    get isClosed(): boolean;
}
