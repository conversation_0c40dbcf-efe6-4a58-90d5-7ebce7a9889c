/**
 * 景深效果
 */
import * as THREE from 'three';
// 使用类型断言导入 BokehPass
// @ts-ignore
import { BokehPass } from 'three/examples/jsm/postprocessing/BokehPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 景深效果选项
 */
export interface DepthOfFieldEffectOptions extends PostProcessingEffectOptions {
  /** 焦距 */
  focus?: number;
  /** 光圈 */
  aperture?: number;
  /** 最大模糊 */
  maxBlur?: number;
  /** 是否使用景深贴图 */
  useDepthMap?: boolean;
}

/**
 * 景深效果
 */
export class DepthOfFieldEffect extends PostProcessingEffect {
  /** 焦距 */
  private focus: number;

  /** 光圈 */
  private aperture: number;

  /** 最大模糊 */
  private maxBlur: number;

  /** 是否使用景深贴图 */
  private useDepthMap: boolean;

  /** 景深通道 */
  private bokehPass: BokehPass | null = null;

  /** 场景 */
  private scene: THREE.Scene | null = null;

  /** 相机 */
  private camera: THREE.Camera | null = null;

  /**
   * 创建景深效果
   * @param options 景深效果选项
   */
  constructor(options: DepthOfFieldEffectOptions = { name: 'DepthOfField' }) {
    super(options);

    this.focus = options.focus || 1.0;
    this.aperture = options.aperture || 0.025;
    this.maxBlur = options.maxBlur || 1.0;
    this.useDepthMap = options.useDepthMap || false;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 如果没有场景或相机，则不创建通道
    if (!this.scene || !this.camera) return;

    // 创建景深通道
    // 使用类型断言处理参数类型问题
    this.bokehPass = new BokehPass(this.scene, this.camera, {
      focus: this.focus,
      aperture: this.aperture,
      maxblur: this.maxBlur // 注意：参数名是 maxblur 而不是 maxBlur
    } as any);

    // 设置通道
    this.pass = this.bokehPass;
  }

  /**
   * 设置场景和相机
   * @param scene 场景
   * @param camera 相机
   */
  public setSceneAndCamera(scene: THREE.Scene, camera: THREE.Camera): void {
    this.scene = scene;
    this.camera = camera;

    // 如果已初始化，则重新创建通道
    if (this.initialized) {
      this.createPass();
    }
  }

  /**
   * 设置焦距
   * @param focus 焦距
   */
  public setFocus(focus: number): void {
    this.focus = focus;

    // 使用类型断言访问 uniforms
    if (this.bokehPass && (this.bokehPass.uniforms as any).focus) {
      (this.bokehPass.uniforms as any).focus.value = focus;
    }
  }

  /**
   * 获取焦距
   * @returns 焦距
   */
  public getFocus(): number {
    return this.focus;
  }

  /**
   * 设置光圈
   * @param aperture 光圈
   */
  public setAperture(aperture: number): void {
    this.aperture = aperture;

    // 使用类型断言访问 uniforms
    if (this.bokehPass && (this.bokehPass.uniforms as any).aperture) {
      (this.bokehPass.uniforms as any).aperture.value = aperture;
    }
  }

  /**
   * 获取光圈
   * @returns 光圈
   */
  public getAperture(): number {
    return this.aperture;
  }

  /**
   * 设置最大模糊
   * @param maxBlur 最大模糊
   */
  public setMaxBlur(maxBlur: number): void {
    this.maxBlur = maxBlur;

    // 使用类型断言访问 uniforms
    if (this.bokehPass && (this.bokehPass.uniforms as any).maxblur) {
      (this.bokehPass.uniforms as any).maxblur.value = maxBlur;
    }
  }

  /**
   * 获取最大模糊
   * @returns 最大模糊
   */
  public getMaxBlur(): number {
    return this.maxBlur;
  }

  /**
   * 设置是否使用景深贴图
   * @param useDepthMap 是否使用景深贴图
   */
  public setUseDepthMap(useDepthMap: boolean): void {
    this.useDepthMap = useDepthMap;

    // 如果使用景深贴图，则需要重新创建通道
    if (this.initialized) {
      this.createPass();
    }
  }

  /**
   * 获取是否使用景深贴图
   * @returns 是否使用景深贴图
   */
  public isUseDepthMap(): boolean {
    return this.useDepthMap;
  }

  /**
   * 设置景深贴图
   * @param depthMap 景深贴图
   */
  public setDepthMap(depthMap: THREE.Texture): void {
    if (!this.bokehPass || !this.useDepthMap) return;

    // 设置景深贴图（使用类型断言访问 uniforms）
    if ((this.bokehPass.uniforms as any).depthTexture) {
      (this.bokehPass.uniforms as any).depthTexture.value = depthMap;
    }
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    super.resize(width, height);

    // 如果已初始化，则重新创建通道
    if (this.initialized) {
      this.createPass();
    }
  }
}
