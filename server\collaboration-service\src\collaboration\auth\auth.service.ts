/**
 * 认证服务
 */
import { Injectable, Inject, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
  ) {}

  /**
   * 验证JWT令牌
   * @param token JWT令牌
   * @returns 用户信息
   */
  async validateToken(token: string): Promise<any> {
    try {
      // 验证令牌
      const payload = this.jwtService.verify(token);
      
      // 从用户服务获取用户信息
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'validateJwt' }, payload),
      );
      
      if (!user) {
        throw new UnauthorizedException('无效的用户');
      }
      
      return user;
    } catch (error) {
      throw new UnauthorizedException('无效的认证令牌');
    }
  }

  /**
   * 检查用户是否有权限访问项目
   * @param userId 用户ID
   * @param projectId 项目ID
   * @returns 是否有权限
   */
  async checkProjectPermission(userId: string, projectId: string): Promise<boolean> {
    try {
      // 从用户服务检查项目权限
      const hasPermission = await firstValueFrom(
        this.userService.send(
          { cmd: 'checkProjectPermission' },
          { userId, projectId },
        ),
      );
      
      return hasPermission;
    } catch (error) {
      return false;
    }
  }
}
