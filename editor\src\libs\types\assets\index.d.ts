/**
 * 资源管理模块
 * 导出所有资源管理相关的类和接口
 */
export { AssetManager } from './AssetManager';
export type { AssetInfo, AssetManagerOptions } from './AssetManager';
export { AssetType } from './ResourceManager';
export { AssetLoader } from './AssetLoader';
export { ResourceManager } from './ResourceManager';
export type { ResourceState, ResourceInfo, ResourceManagerOptions } from './ResourceManager';
export { ResourceDependencyManager } from './ResourceDependencyManager';
export type { DependencyType, DependencyInfo } from './ResourceDependencyManager';
export { ResourcePreloader } from './ResourcePreloader';
export type { PreloadResourceInfo, PreloadGroupInfo, PreloadProgressInfo, ResourcePreloaderOptions } from './ResourcePreloader';
export { EnhancedAssetLoader } from './EnhancedAssetLoader';
export type { LoaderOptions } from './EnhancedAssetLoader';
export { EnhancedResourceManager } from './EnhancedResourceManager';
export type { EnhancedResourceManagerOptions, ResourceInfo as EnhancedResourceInfo } from './EnhancedResourceManager';
export { EnhancedResourceDependencyManager } from './EnhancedResourceDependencyManager';
export type { DependencyType as EnhancedDependencyType, EnhancedResourceDependencyManagerOptions, DependencyInfo as EnhancedDependencyInfo } from './EnhancedResourceDependencyManager';
export { EnhancedResourcePreloader } from './EnhancedResourcePreloader';
export type { EnhancedResourcePreloaderOptions, PreloadResourceInfo as EnhancedPreloadResourceInfo, PreloadGroupInfo as EnhancedPreloadGroupInfo, PreloadProgressInfo as EnhancedPreloadProgressInfo } from './EnhancedResourcePreloader';
export { EnhancedResourceSystem } from './EnhancedResourceSystem';
export type { EnhancedResourceSystemOptions } from './EnhancedResourceSystem';
export { ResourceSystemExample } from './examples/ResourceSystemExample';
/**
 * 创建默认资源管理系统实例
 * @returns 资源管理系统实例
 */
export declare function createResourceSystem(): any;
