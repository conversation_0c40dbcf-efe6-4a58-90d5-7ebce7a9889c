<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>高级面部动画示例</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      background-color: #2c3e50;
      color: white;
      padding: 20px;
      text-align: center;
      margin-bottom: 20px;
    }
    
    h1 {
      margin: 0;
      font-size: 2em;
    }
    
    .demo-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .demo-panel {
      flex: 1;
      min-width: 300px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }
    
    .canvas-container {
      width: 100%;
      height: 400px;
      background-color: #eee;
      border-radius: 4px;
      margin-bottom: 20px;
      position: relative;
    }
    
    canvas {
      width: 100%;
      height: 100%;
    }
    
    .controls {
      margin-bottom: 20px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    input, select, textarea, button {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-family: inherit;
    }
    
    button {
      background-color: #3498db;
      color: white;
      border: none;
      cursor: pointer;
      font-weight: bold;
      transition: background-color 0.3s;
    }
    
    button:hover {
      background-color: #2980b9;
    }
    
    .tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 1px solid #ddd;
    }
    
    .tab {
      padding: 10px 20px;
      cursor: pointer;
      border: 1px solid transparent;
      border-bottom: none;
      border-radius: 4px 4px 0 0;
      margin-right: 5px;
    }
    
    .tab.active {
      background-color: white;
      border-color: #ddd;
      color: #3498db;
    }
    
    .tab-content {
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
    
    .emotion-display {
      margin-top: 20px;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    
    .emotion-bar {
      height: 20px;
      background-color: #3498db;
      margin-bottom: 5px;
      border-radius: 2px;
      transition: width 0.3s;
    }
    
    .loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
    }
    
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-left-color: #3498db;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .model-selector {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    
    .model-option {
      margin-bottom: 10px;
    }
    
    .model-option input[type="radio"] {
      width: auto;
      margin-right: 10px;
    }
    
    .model-option label {
      display: inline;
      font-weight: normal;
    }
    
    .advanced-options {
      margin-top: 20px;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    
    .advanced-options summary {
      cursor: pointer;
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .example-prompts {
      margin-top: 10px;
    }
    
    .example-prompt {
      display: inline-block;
      margin: 5px;
      padding: 5px 10px;
      background-color: #eee;
      border-radius: 15px;
      cursor: pointer;
      font-size: 0.9em;
    }
    
    .example-prompt:hover {
      background-color: #ddd;
    }
    
    footer {
      text-align: center;
      margin-top: 30px;
      padding: 20px;
      background-color: #2c3e50;
      color: white;
    }
  </style>
</head>
<body>
  <header>
    <h1>高级面部动画示例</h1>
    <p>基于AI的情感分析和表情生成</p>
  </header>
  
  <div class="container">
    <div class="tabs">
      <div class="tab active" data-tab="basic">基础示例</div>
      <div class="tab" data-tab="advanced">高级示例</div>
      <div class="tab" data-tab="comparison">模型对比</div>
    </div>
    
    <div class="tab-content active" id="basic-tab">
      <div class="demo-container">
        <div class="demo-panel">
          <h2>基础面部动画</h2>
          <div class="canvas-container">
            <canvas id="basic-canvas"></canvas>
            <div class="loading" id="basic-loading">
              <div class="spinner"></div>
              <p>加载中...</p>
            </div>
          </div>
          
          <div class="controls">
            <label for="basic-prompt">情感描述：</label>
            <textarea id="basic-prompt" rows="3" placeholder="例如：角色感到非常开心"></textarea>
            
            <div class="example-prompts">
              <div class="example-prompt">角色感到非常开心</div>
              <div class="example-prompt">角色悲伤地低下头</div>
              <div class="example-prompt">角色突然感到惊讶</div>
              <div class="example-prompt">角色愤怒地皱眉</div>
            </div>
            
            <label for="basic-duration">持续时间（秒）：</label>
            <input type="number" id="basic-duration" min="1" max="10" step="0.5" value="5">
            
            <label for="basic-loop">循环播放：</label>
            <input type="checkbox" id="basic-loop">
            
            <button id="basic-generate">生成动画</button>
          </div>
          
          <div class="emotion-display" id="basic-emotion-display">
            <h3>情感分析结果</h3>
            <div id="basic-emotion-results"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="tab-content" id="advanced-tab">
      <div class="demo-container">
        <div class="demo-panel">
          <h2>高级面部动画</h2>
          <div class="canvas-container">
            <canvas id="advanced-canvas"></canvas>
            <div class="loading" id="advanced-loading">
              <div class="spinner"></div>
              <p>加载中...</p>
            </div>
          </div>
          
          <div class="controls">
            <label for="advanced-prompt">情感描述：</label>
            <textarea id="advanced-prompt" rows="3" placeholder="例如：角色先是平静，然后逐渐变得兴奋，最后露出满意的微笑"></textarea>
            
            <div class="example-prompts">
              <div class="example-prompt">角色先是平静，然后逐渐变得兴奋，最后露出满意的微笑</div>
              <div class="example-prompt">角色感到既开心又有些担忧</div>
              <div class="example-prompt">角色从惊讶转为恍然大悟</div>
              <div class="example-prompt">角色强忍着悲伤，努力微笑</div>
            </div>
            
            <label for="advanced-duration">持续时间（秒）：</label>
            <input type="number" id="advanced-duration" min="1" max="20" step="0.5" value="8">
            
            <label for="advanced-style">表情风格：</label>
            <select id="advanced-style">
              <option value="natural">自然</option>
              <option value="cartoon">卡通</option>
              <option value="exaggerated">夸张</option>
              <option value="subtle">微妙</option>
              <option value="dramatic">戏剧性</option>
            </select>
            
            <label for="advanced-intensity">表情强度：</label>
            <input type="range" id="advanced-intensity" min="0.1" max="1" step="0.1" value="0.8">
            
            <label for="advanced-loop">循环播放：</label>
            <input type="checkbox" id="advanced-loop">
            
            <details class="advanced-options">
              <summary>高级选项</summary>
              
              <label for="advanced-micro-expressions">启用微表情：</label>
              <input type="checkbox" id="advanced-micro-expressions" checked>
              
              <label for="advanced-model-type">模型类型：</label>
              <select id="advanced-model-type">
                <option value="bert">BERT</option>
                <option value="roberta">RoBERTa</option>
                <option value="distilbert">DistilBERT</option>
                <option value="albert">ALBERT</option>
                <option value="xlnet">XLNet</option>
              </select>
              
              <label for="advanced-seed">随机种子（可选）：</label>
              <input type="number" id="advanced-seed" min="0" max="9999999">
            </details>
            
            <button id="advanced-generate">生成动画</button>
          </div>
          
          <div class="emotion-display" id="advanced-emotion-display">
            <h3>情感分析结果</h3>
            <div id="advanced-emotion-results"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="tab-content" id="comparison-tab">
      <div class="demo-container">
        <div class="demo-panel">
          <h2>模型对比</h2>
          
          <div class="model-selector">
            <h3>选择要对比的模型：</h3>
            
            <div class="model-option">
              <input type="checkbox" id="compare-bert" checked>
              <label for="compare-bert">BERT</label>
            </div>
            
            <div class="model-option">
              <input type="checkbox" id="compare-roberta" checked>
              <label for="compare-roberta">RoBERTa</label>
            </div>
            
            <div class="model-option">
              <input type="checkbox" id="compare-distilbert">
              <label for="compare-distilbert">DistilBERT</label>
            </div>
            
            <div class="model-option">
              <input type="checkbox" id="compare-albert">
              <label for="compare-albert">ALBERT</label>
            </div>
            
            <div class="model-option">
              <input type="checkbox" id="compare-xlnet">
              <label for="compare-xlnet">XLNet</label>
            </div>
          </div>
          
          <div class="controls">
            <label for="comparison-prompt">情感描述：</label>
            <textarea id="comparison-prompt" rows="3" placeholder="输入情感描述以对比不同模型的分析结果"></textarea>
            
            <div class="example-prompts">
              <div class="example-prompt">角色感到既开心又有些担忧</div>
              <div class="example-prompt">角色强忍着悲伤，努力微笑</div>
              <div class="example-prompt">角色对未来充满希望但又有些焦虑</div>
              <div class="example-prompt">角色感到愤怒但试图保持冷静</div>
            </div>
            
            <button id="comparison-analyze">分析情感</button>
          </div>
          
          <div id="comparison-results">
            <h3>对比结果</h3>
            <div id="comparison-charts"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <footer>
    <p>© 2023 DL（Digital Learning）引擎 - 高级面部动画示例</p>
  </footer>
  
  <script src="./main.js"></script>
</body>
</html>
