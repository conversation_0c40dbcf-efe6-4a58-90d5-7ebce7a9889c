/**
 * 雨水水体示例
 * 展示如何使用雨水水体系统创建各种雨水效果
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { RainWaterComponent, RainWaterType } from '../../engine/src/physics/water/RainWaterComponent';
import { RainWaterPresets, RainWaterPresetType } from '../../engine/src/physics/water/RainWaterPresets';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../engine/src/physics/water/WaterInteractionSystem';
import { UnderwaterParticleSystem } from '../../engine/src/rendering/water/UnderwaterParticleSystem';
import { WaterWeatherSystem } from '../../engine/src/physics/water/WaterWeatherSystem';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { AudioSystem } from '../../engine/src/audio/AudioSystem';
import { Debug } from '../../engine/src/utils/Debug';

/**
 * 雨水水体示例
 */
export class RainWaterExample {
  /** 世界 */
  private world: World;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水体交互系统 */
  private waterInteractionSystem: WaterInteractionSystem;
  /** 水下粒子系统 */
  private underwaterParticleSystem: UnderwaterParticleSystem;
  /** 水体天气系统 */
  private waterWeatherSystem: WaterWeatherSystem;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 音频系统 */
  private audioSystem: AudioSystem;
  /** 雨水实体 */
  private rainWaterEntity: Entity;
  /** 雨水组件 */
  private rainWaterComponent: RainWaterComponent;
  /** 当前雨水类型 */
  private currentRainWaterType: RainWaterPresetType = RainWaterPresetType.MEDIUM;
  /** 雨水类型列表 */
  private rainWaterTypes: RainWaterPresetType[] = [
    RainWaterPresetType.LIGHT,
    RainWaterPresetType.MEDIUM,
    RainWaterPresetType.HEAVY,
    RainWaterPresetType.THUNDERSTORM,
    RainWaterPresetType.MONSOON,
    RainWaterPresetType.SPRING_RAIN,
    RainWaterPresetType.SUMMER_RAIN,
    RainWaterPresetType.AUTUMN_RAIN,
    RainWaterPresetType.WINTER_RAIN
  ];

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  private initialize(): void {
    // 初始化系统
    this.initializeSystems();

    // 初始化雨水
    this.initializeRainWater();

    // 初始化输入处理
    this.initializeInputHandling();

    // 启动
    this.start();
  }

  /**
   * 初始化系统
   */
  private initializeSystems(): void {
    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水体交互系统
    this.waterInteractionSystem = new WaterInteractionSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true
    });
    this.world.addSystem(this.waterInteractionSystem);

    // 创建水下粒子系统
    this.underwaterParticleSystem = new UnderwaterParticleSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      maxParticles: 10000,
      useGPUInstancing: true,
      useDebugVisualization: false
    });
    this.world.addSystem(this.underwaterParticleSystem);

    // 创建水体天气系统
    this.waterWeatherSystem = new WaterWeatherSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      weatherType: 'rainy',
      rainIntensity: 1.0,
      windSpeed: 1.0,
      windDirection: 0,
      useDebugVisualization: false
    });
    this.world.addSystem(this.waterWeatherSystem);

    // 创建音频系统
    this.audioSystem = new AudioSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      masterVolume: 1.0
    });
    this.world.addSystem(this.audioSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world, {
      enabled: true,
      autoUpdate: true
    });
    this.world.addSystem(this.inputSystem);
  }

  /**
   * 初始化雨水
   */
  private initializeRainWater(): void {
    // 创建雨水
    this.rainWaterEntity = RainWaterPresets.createPreset(this.world, {
      type: this.currentRainWaterType,
      position: new THREE.Vector3(0, 0, 0),
      size: { width: 50, height: 0.1, depth: 50 }
    });

    // 获取雨水组件
    this.rainWaterComponent = this.rainWaterEntity.getComponent(RainWaterComponent);
  }

  /**
   * 初始化输入处理
   */
  private initializeInputHandling(): void {
    // 监听键盘事件
    this.inputSystem.onKeyDown('Space', () => {
      this.switchRainWaterType();
    });

    // 监听鼠标事件
    this.inputSystem.onMouseDown('Left', (event) => {
      // 获取鼠标位置
      const mousePosition = event.position;
      
      // 创建水花效果
      this.waterInteractionSystem.createSplashEffect(
        this.rainWaterEntity.id,
        new THREE.Vector3(mousePosition.x, 0, mousePosition.y),
        1.0,
        new THREE.Vector3(1, 1, 1)
      );
    });
  }

  /**
   * 切换雨水类型
   */
  private switchRainWaterType(): void {
    // 获取当前类型索引
    const currentIndex = this.rainWaterTypes.indexOf(this.currentRainWaterType);
    
    // 计算下一个类型索引
    const nextIndex = (currentIndex + 1) % this.rainWaterTypes.length;
    
    // 更新当前类型
    this.currentRainWaterType = this.rainWaterTypes[nextIndex];
    
    // 移除旧的雨水实体
    this.world.removeEntity(this.rainWaterEntity);
    
    // 创建新的雨水实体
    this.rainWaterEntity = RainWaterPresets.createPreset(this.world, {
      type: this.currentRainWaterType,
      position: new THREE.Vector3(0, 0, 0),
      size: { width: 50, height: 0.1, depth: 50 }
    });
    
    // 获取雨水组件
    this.rainWaterComponent = this.rainWaterEntity.getComponent(RainWaterComponent);
    
    // 输出当前类型
    Debug.log('RainWaterExample', `切换雨水类型: ${this.getRainWaterTypeName(this.currentRainWaterType)}`);
  }

  /**
   * 获取雨水类型名称
   * @param type 雨水类型
   * @returns 雨水类型名称
   */
  private getRainWaterTypeName(type: RainWaterPresetType): string {
    switch (type) {
      case RainWaterPresetType.LIGHT:
        return '轻雨';
      case RainWaterPresetType.MEDIUM:
        return '中雨';
      case RainWaterPresetType.HEAVY:
        return '暴雨';
      case RainWaterPresetType.THUNDERSTORM:
        return '雷雨';
      case RainWaterPresetType.MONSOON:
        return '季风雨';
      case RainWaterPresetType.SPRING_RAIN:
        return '春雨';
      case RainWaterPresetType.SUMMER_RAIN:
        return '夏雨';
      case RainWaterPresetType.AUTUMN_RAIN:
        return '秋雨';
      case RainWaterPresetType.WINTER_RAIN:
        return '冬雨';
      default:
        return type;
    }
  }

  /**
   * 启动
   */
  private start(): void {
    // 启动世界
    this.world.start();
  }
}

// 创建示例实例
const example = new RainWaterExample();
