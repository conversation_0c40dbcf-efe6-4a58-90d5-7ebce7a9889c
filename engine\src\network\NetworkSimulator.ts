/**
 * 网络模拟器
 * 用于模拟各种网络条件，如延迟、丢包、抖动等
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';

/**
 * 网络模拟配置
 */
export interface NetworkSimulatorConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 延迟（毫秒） */
  latency?: number;
  /** 延迟抖动（毫秒） */
  latencyJitter?: number;
  /** 丢包率（0-1） */
  packetLoss?: number;
  /** 带宽限制（字节/秒） */
  bandwidthLimit?: number;
  /** 是否启用随机断线 */
  enableRandomDisconnect?: boolean;
  /** 随机断线概率（0-1） */
  disconnectProbability?: number;
  /** 断线后自动重连时间（毫秒） */
  reconnectTime?: number;
  /** 是否启用详细日志 */
  detailedLogging?: boolean;
}

/**
 * 网络模拟器
 * 用于模拟各种网络条件，如延迟、丢包、抖动等
 */
export class NetworkSimulator extends EventEmitter {
  /** 配置 */
  private config: Required<NetworkSimulatorConfig>;
  
  /** 待处理的消息队列 */
  private messageQueue: Array<{
    data: any;
    delay: number;
    sendTime: number;
    size: number;
  }> = [];
  
  /** 处理队列的定时器ID */
  private processTimerId: number | null = null;
  
  /** 当前是否断线 */
  private disconnected: boolean = false;
  
  /** 重连定时器ID */
  private reconnectTimerId: number | null = null;
  
  /** 发送的字节数 */
  private sentBytes: number = 0;
  
  /** 上次重置带宽计数的时间 */
  private lastBandwidthResetTime: number = 0;
  
  /**
   * 创建网络模拟器
   * @param config 配置
   */
  constructor(config: NetworkSimulatorConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      enabled: true,
      latency: 0,
      latencyJitter: 0,
      packetLoss: 0,
      bandwidthLimit: 0,
      enableRandomDisconnect: false,
      disconnectProbability: 0.01,
      reconnectTime: 3000,
      detailedLogging: false,
      ...config,
    };
    
    // 初始化
    this.initialize();
  }
  
  /**
   * 初始化
   */
  private initialize(): void {
    // 启动处理队列的定时器
    this.startProcessing();
    
    // 初始化带宽重置时间
    this.lastBandwidthResetTime = Date.now();
    
    if (this.config.detailedLogging) {
      Debug.log('NetworkSimulator', `网络模拟器已初始化: 延迟=${this.config.latency}ms, 抖动=${this.config.latencyJitter}ms, 丢包率=${this.config.packetLoss * 100}%, 带宽限制=${this.config.bandwidthLimit}B/s`);
    }
  }
  
  /**
   * 启动处理队列
   */
  private startProcessing(): void {
    if (this.processTimerId !== null) {
      return;
    }
    
    this.processTimerId = window.setInterval(() => {
      this.processQueue();
    }, 10); // 每10毫秒处理一次队列
  }
  
  /**
   * 停止处理队列
   */
  private stopProcessing(): void {
    if (this.processTimerId !== null) {
      clearInterval(this.processTimerId);
      this.processTimerId = null;
    }
  }
  
  /**
   * 处理消息队列
   */
  private processQueue(): void {
    if (!this.config.enabled || this.disconnected) {
      return;
    }
    
    const now = Date.now();
    const toRemove: number[] = [];
    
    // 检查带宽限制
    if (this.config.bandwidthLimit > 0) {
      // 每秒重置带宽计数
      if (now - this.lastBandwidthResetTime > 1000) {
        this.sentBytes = 0;
        this.lastBandwidthResetTime = now;
      }
      
      // 如果已经超过带宽限制，则不处理
      if (this.sentBytes >= this.config.bandwidthLimit) {
        return;
      }
    }
    
    // 处理队列中的消息
    for (let i = 0; i < this.messageQueue.length; i++) {
      const message = this.messageQueue[i];
      
      // 如果消息延迟时间已到，则发送
      if (now - message.sendTime >= message.delay) {
        // 检查带宽限制
        if (this.config.bandwidthLimit > 0) {
          // 如果发送这条消息会超过带宽限制，则跳过
          if (this.sentBytes + message.size > this.config.bandwidthLimit) {
            continue;
          }
          
          // 更新已发送字节数
          this.sentBytes += message.size;
        }
        
        // 触发消息接收事件
        this.emit('receive', message.data);
        
        // 标记为待移除
        toRemove.push(i);
      }
    }
    
    // 从队列中移除已处理的消息
    for (let i = toRemove.length - 1; i >= 0; i--) {
      this.messageQueue.splice(toRemove[i], 1);
    }
    
    // 检查是否需要随机断线
    if (this.config.enableRandomDisconnect && Math.random() < this.config.disconnectProbability) {
      this.simulateDisconnect();
    }
  }
  
  /**
   * 模拟发送消息
   * @param data 消息数据
   * @param size 消息大小（字节）
   */
  public send(data: any, size: number = 0): void {
    if (!this.config.enabled) {
      // 如果未启用模拟，则直接触发接收事件
      this.emit('receive', data);
      return;
    }
    
    if (this.disconnected) {
      // 如果已断线，则丢弃消息
      if (this.config.detailedLogging) {
        Debug.log('NetworkSimulator', '已断线，消息被丢弃');
      }
      return;
    }
    
    // 检查丢包
    if (Math.random() < this.config.packetLoss) {
      if (this.config.detailedLogging) {
        Debug.log('NetworkSimulator', '模拟丢包，消息被丢弃');
      }
      return;
    }
    
    // 计算延迟
    let delay = this.config.latency;
    
    // 添加抖动
    if (this.config.latencyJitter > 0) {
      // 生成-jitter到+jitter之间的随机数
      const jitter = (Math.random() * 2 - 1) * this.config.latencyJitter;
      delay += jitter;
    }
    
    // 确保延迟不为负数
    delay = Math.max(0, delay);
    
    // 添加到队列
    this.messageQueue.push({
      data,
      delay,
      sendTime: Date.now(),
      size: size > 0 ? size : (typeof data === 'string' ? data.length : 0),
    });
    
    if (this.config.detailedLogging) {
      Debug.log('NetworkSimulator', `消息已加入队列，延迟=${delay.toFixed(2)}ms, 大小=${size}字节`);
    }
  }
  
  /**
   * 模拟断线
   */
  public simulateDisconnect(): void {
    if (this.disconnected) {
      return;
    }
    
    this.disconnected = true;
    
    // 触发断线事件
    this.emit('disconnect');
    
    if (this.config.detailedLogging) {
      Debug.log('NetworkSimulator', '模拟断线');
    }
    
    // 设置重连定时器
    this.reconnectTimerId = window.setTimeout(() => {
      this.simulateReconnect();
    }, this.config.reconnectTime);
  }
  
  /**
   * 模拟重连
   */
  public simulateReconnect(): void {
    if (!this.disconnected) {
      return;
    }
    
    this.disconnected = false;
    
    // 清除重连定时器
    if (this.reconnectTimerId !== null) {
      clearTimeout(this.reconnectTimerId);
      this.reconnectTimerId = null;
    }
    
    // 触发重连事件
    this.emit('reconnect');
    
    if (this.config.detailedLogging) {
      Debug.log('NetworkSimulator', '模拟重连');
    }
  }
  
  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<NetworkSimulatorConfig>): void {
    this.config = {
      ...this.config,
      ...config,
    };
    
    if (this.config.detailedLogging) {
      Debug.log('NetworkSimulator', `配置已更新: 延迟=${this.config.latency}ms, 抖动=${this.config.latencyJitter}ms, 丢包率=${this.config.packetLoss * 100}%, 带宽限制=${this.config.bandwidthLimit}B/s`);
    }
  }
  
  /**
   * 清空消息队列
   */
  public clearQueue(): void {
    this.messageQueue = [];
    
    if (this.config.detailedLogging) {
      Debug.log('NetworkSimulator', '消息队列已清空');
    }
  }
  
  /**
   * 销毁模拟器
   */
  public dispose(): void {
    this.stopProcessing();
    
    if (this.reconnectTimerId !== null) {
      clearTimeout(this.reconnectTimerId);
      this.reconnectTimerId = null;
    }
    
    this.clearQueue();
    this.removeAllListeners();
    
    if (this.config.detailedLogging) {
      Debug.log('NetworkSimulator', '网络模拟器已销毁');
    }
  }
}
