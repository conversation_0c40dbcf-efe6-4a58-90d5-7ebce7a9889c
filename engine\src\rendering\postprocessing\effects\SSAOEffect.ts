/**
 * 屏幕空间环境光遮蔽效果
 */
import * as THREE from 'three';
// 使用类型断言导入 SSAOPass
// @ts-ignore
import { SSAOPass } from 'three/examples/jsm/postprocessing/SSAOPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * SSAO输出模式
 */
export enum SSAOOutputMode {
  Default = 0,
  SSAO = 1,
  Blur = 2,
  Beauty = 3,
  Depth = 4,
  Normal = 5
}

/**
 * SSAO效果选项
 */
export interface SSAOEffectOptions extends PostProcessingEffectOptions {
  /** 输出模式 */
  output?: SSAOOutputMode;
  /** 内核半径 */
  kernelRadius?: number;
  /** 最小距离 */
  minDistance?: number;
  /** 最大距离 */
  maxDistance?: number;
  /** 强度 */
  aoClamp?: number;
  /** 亮度 */
  lumInfluence?: number;
}

/**
 * 屏幕空间环境光遮蔽效果
 */
export class SSAOEffect extends PostProcessingEffect {
  /** 输出模式 */
  private output: SSAOOutputMode;

  /** 内核半径 */
  private kernelRadius: number;

  /** 最小距离 */
  private minDistance: number;

  /** 最大距离 */
  private maxDistance: number;

  /** 强度 */
  private aoClamp: number;

  /** 亮度 */
  private lumInfluence: number;

  /** SSAO通道 */
  private ssaoPass: SSAOPass | null = null;

  /** 场景 */
  private scene: THREE.Scene | null = null;

  /** 相机 */
  private camera: THREE.Camera | null = null;

  /**
   * 创建SSAO效果
   * @param options SSAO效果选项
   */
  constructor(options: SSAOEffectOptions = { name: 'SSAO' }) {
    super(options);

    this.output = options.output !== undefined ? options.output : SSAOOutputMode.Default;
    this.kernelRadius = options.kernelRadius || 8;
    this.minDistance = options.minDistance || 0.005;
    this.maxDistance = options.maxDistance || 0.1;
    this.aoClamp = options.aoClamp || 0.25;
    this.lumInfluence = options.lumInfluence || 0.7;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 如果没有场景或相机，则不创建通道
    if (!this.scene || !this.camera) return;

    // 创建SSAO通道
    this.ssaoPass = new SSAOPass(this.scene, this.camera, this.width, this.height);

    // 设置参数（使用类型断言）
    (this.ssaoPass as any).output = this.output;
    this.ssaoPass.kernelRadius = this.kernelRadius;
    this.ssaoPass.minDistance = this.minDistance;
    this.ssaoPass.maxDistance = this.maxDistance;

    // 设置强度和亮度（使用类型断言）
    const uniforms = (this.ssaoPass as any).uniforms;
    if (uniforms) {
      uniforms['aoClamp'].value = this.aoClamp;
      uniforms['lumInfluence'].value = this.lumInfluence;
    }

    // 设置通道
    this.pass = this.ssaoPass;
  }

  /**
   * 设置场景和相机
   * @param scene 场景
   * @param camera 相机
   */
  public setSceneAndCamera(scene: THREE.Scene, camera: THREE.Camera): void {
    this.scene = scene;
    this.camera = camera;

    // 如果已初始化，则重新创建通道
    if (this.initialized) {
      this.createPass();
    }
  }

  /**
   * 设置输出模式
   * @param output 输出模式
   */
  public setOutput(output: SSAOOutputMode): void {
    this.output = output;

    if (this.ssaoPass) {
      // 使用类型断言
      (this.ssaoPass as any).output = output;
    }
  }

  /**
   * 获取输出模式
   * @returns 输出模式
   */
  public getOutput(): SSAOOutputMode {
    return this.output;
  }

  /**
   * 设置内核半径
   * @param radius 内核半径
   */
  public setKernelRadius(radius: number): void {
    this.kernelRadius = radius;

    if (this.ssaoPass) {
      this.ssaoPass.kernelRadius = radius;
    }
  }

  /**
   * 获取内核半径
   * @returns 内核半径
   */
  public getKernelRadius(): number {
    return this.kernelRadius;
  }

  /**
   * 设置最小距离
   * @param distance 最小距离
   */
  public setMinDistance(distance: number): void {
    this.minDistance = distance;

    if (this.ssaoPass) {
      this.ssaoPass.minDistance = distance;
    }
  }

  /**
   * 获取最小距离
   * @returns 最小距离
   */
  public getMinDistance(): number {
    return this.minDistance;
  }

  /**
   * 设置最大距离
   * @param distance 最大距离
   */
  public setMaxDistance(distance: number): void {
    this.maxDistance = distance;

    if (this.ssaoPass) {
      this.ssaoPass.maxDistance = distance;
    }
  }

  /**
   * 获取最大距离
   * @returns 最大距离
   */
  public getMaxDistance(): number {
    return this.maxDistance;
  }

  /**
   * 设置强度
   * @param clamp 强度
   */
  public setAOClamp(clamp: number): void {
    this.aoClamp = clamp;

    // 使用类型断言访问 uniforms
    if (this.ssaoPass) {
      const uniforms = (this.ssaoPass as any).uniforms;
      if (uniforms && uniforms['aoClamp']) {
        uniforms['aoClamp'].value = clamp;
      }
    }
  }

  /**
   * 获取强度
   * @returns 强度
   */
  public getAOClamp(): number {
    return this.aoClamp;
  }

  /**
   * 设置亮度
   * @param influence 亮度
   */
  public setLumInfluence(influence: number): void {
    this.lumInfluence = influence;

    // 使用类型断言访问 uniforms
    if (this.ssaoPass) {
      const uniforms = (this.ssaoPass as any).uniforms;
      if (uniforms && uniforms['lumInfluence']) {
        uniforms['lumInfluence'].value = influence;
      }
    }
  }

  /**
   * 获取亮度
   * @returns 亮度
   */
  public getLumInfluence(): number {
    return this.lumInfluence;
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    super.resize(width, height);

    // 如果已初始化，则重新创建通道
    if (this.initialized) {
      this.createPass();
    }
  }

  /**
   * 更新效果
   * @param _deltaTime 帧间隔时间（秒）- 未使用，保留以符合接口
   */
  public update(_deltaTime: number): void {
    // SSAO 效果不需要每帧更新
  }
}
