import { Controller, Get, Post, Body, Query, Param, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { EnhancedLoggerService, LogEntry, LogLevel } from './enhanced-logger.service';
import { LogAggregatorService } from './log-aggregator.service';

@Controller('logging')
export class LoggingController {
  private readonly logger = new Logger(LoggingController.name);

  constructor(
    private readonly enhancedLogger: EnhancedLoggerService,
    private readonly logAggregator: LogAggregatorService,
  ) {}

  /**
   * 获取最近的日志
   */
  @Get('recent')
  getRecentLogs(
    @Query('limit') limit: string,
    @Query('level') level?: LogLevel,
  ): LogEntry[] {
    const limitNumber = limit ? parseInt(limit, 10) : 100;
    return this.enhancedLogger.getRecentLogs(limitNumber, level);
  }

  /**
   * 搜索日志
   */
  @Get('search')
  searchLogs(
    @Query('query') query: string,
    @Query('limit') limit: string,
    @Query('level') level?: LogLevel,
  ): LogEntry[] {
    if (!query) {
      throw new HttpException('查询参数不能为空', HttpStatus.BAD_REQUEST);
    }
    
    const limitNumber = limit ? parseInt(limit, 10) : 100;
    return this.enhancedLogger.searchLogs(query, limitNumber, level);
  }

  /**
   * 获取日志统计信息
   */
  @Get('stats')
  getLogStats(): any {
    return this.enhancedLogger.getLogStats();
  }

  /**
   * 获取可用的归档日期
   */
  @Get('archives/dates')
  getAvailableArchiveDates(): string[] {
    return this.logAggregator.getAvailableArchiveDates();
  }

  /**
   * 获取特定日期可用的日志级别
   */
  @Get('archives/dates/:date/levels')
  getAvailableLevelsForDate(@Param('date') date: string): LogLevel[] {
    return this.logAggregator.getAvailableLevelsForDate(date);
  }

  /**
   * 获取归档日志
   */
  @Get('archives/:date/:level')
  async getArchivedLogs(
    @Param('date') date: string,
    @Param('level') level: LogLevel,
  ): Promise<LogEntry[]> {
    return this.logAggregator.getArchivedLogs(date, level);
  }

  /**
   * 搜索归档日志
   */
  @Get('archives/search')
  async searchArchivedLogs(
    @Query('query') query: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('level') level?: LogLevel,
  ): Promise<LogEntry[]> {
    if (!query) {
      throw new HttpException('查询参数不能为空', HttpStatus.BAD_REQUEST);
    }
    
    if (!startDate || !endDate) {
      throw new HttpException('开始日期和结束日期不能为空', HttpStatus.BAD_REQUEST);
    }
    
    return this.logAggregator.searchArchivedLogs(query, startDate, endDate, level);
  }

  /**
   * 手动触发日志聚合
   */
  @Post('aggregate')
  async triggerLogAggregation(): Promise<{ success: boolean }> {
    try {
      await this.logAggregator.aggregateAndArchiveLogs();
      return { success: true };
    } catch (error) {
      this.logger.error(`触发日志聚合失败: ${error.message}`, error.stack);
      throw new HttpException(`触发日志聚合失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 添加测试日志
   */
  @Post('test')
  addTestLog(
    @Body() logDto: {
      level: LogLevel;
      message: string;
      context?: string;
      data?: any;
    },
  ): { success: boolean } {
    try {
      const { level, message, context, data } = logDto;
      
      switch (level) {
        case 'debug':
          this.enhancedLogger.debug(message, context, data);
          break;
        case 'info':
          this.enhancedLogger.info(message, context, data);
          break;
        case 'warn':
          this.enhancedLogger.warn(message, context, data);
          break;
        case 'error':
          this.enhancedLogger.error(message, null, context, data);
          break;
        case 'fatal':
          this.enhancedLogger.fatal(message, null, context, data);
          break;
        default:
          this.enhancedLogger.info(message, context, data);
      }
      
      return { success: true };
    } catch (error) {
      this.logger.error(`添加测试日志失败: ${error.message}`, error.stack);
      throw new HttpException(`添加测试日志失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
