/**
 * 动画组件
 * 基础动画组件类
 */
import { Component } from '../core/Component';
import { Animator } from './Animator';
export declare class AnimationComponent extends Component {
    static readonly type: string;
    private animator;
    private isPlaying;
    private currentClip;
    constructor();
    setAnimator(animator: Animator): void;
    getAnimator(): Animator | null;
    play(clipName: string): void;
    stop(): void;
    pause(): void;
    resume(): void;
    update(deltaTime: number): void;
    getIsPlaying(): boolean;
    getCurrentClip(): string | null;
    dispose(): void;
}
