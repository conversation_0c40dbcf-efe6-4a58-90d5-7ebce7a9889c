/**
 * NodeRegistry类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NodeRegistry } from '../../src/visualscript/NodeRegistry';
import { Node } from '../../src/visualscript/Node';

// 创建一个测试节点类
class TestNode extends Node {
  constructor() {
    super('TestNode');
    
    // 添加输入端口
    this.addInputPort('input', 'any');
    
    // 添加输出端口
    this.addOutputPort('output', 'any');
  }
  
  public execute(inputs: Map<string, any>): Map<string, any> {
    const outputs = new Map<string, any>();
    outputs.set('output', inputs.get('input'));
    
    return outputs;
  }
}

// 创建一个数学节点类
class MathNode extends Node {
  public operation: string;
  
  constructor(operation: string = 'add') {
    super('MathNode');
    this.operation = operation;
    
    // 添加输入端口
    this.addInputPort('a', 'number');
    this.addInputPort('b', 'number');
    
    // 添加输出端口
    this.addOutputPort('result', 'number');
  }
  
  public execute(inputs: Map<string, any>): Map<string, any> {
    const a = inputs.get('a') || 0;
    const b = inputs.get('b') || 0;
    let result = 0;
    
    switch (this.operation) {
      case 'add':
        result = a + b;
        break;
      case 'subtract':
        result = a - b;
        break;
      case 'multiply':
        result = a * b;
        break;
      case 'divide':
        result = b !== 0 ? a / b : 0;
        break;
      default:
        result = a + b;
    }
    
    const outputs = new Map<string, any>();
    outputs.set('result', result);
    
    return outputs;
  }
}

describe('NodeRegistry', () => {
  // 在每个测试前清理节点注册表
  beforeEach(() => {
    NodeRegistry.clear();
  });
  
  // 测试注册节点类型
  it('应该能够注册节点类型', () => {
    // 注册测试节点
    NodeRegistry.registerNodeType('TestNode', TestNode);
    
    // 验证节点类型已注册
    expect(NodeRegistry.hasNodeType('TestNode')).toBe(true);
  });
  
  // 测试获取节点类型
  it('应该能够获取节点类型', () => {
    // 注册测试节点
    NodeRegistry.registerNodeType('TestNode', TestNode);
    
    // 获取节点类型
    const nodeType = NodeRegistry.getNodeType('TestNode');
    
    // 验证节点类型
    expect(nodeType).toBe(TestNode);
  });
  
  // 测试创建节点实例
  it('应该能够创建节点实例', () => {
    // 注册测试节点
    NodeRegistry.registerNodeType('TestNode', TestNode);
    
    // 创建节点实例
    const node = NodeRegistry.createNode('TestNode');
    
    // 验证节点实例
    expect(node).toBeInstanceOf(TestNode);
    expect(node.getType()).toBe('TestNode');
  });
  
  // 测试创建带参数的节点实例
  it('应该能够创建带参数的节点实例', () => {
    // 注册数学节点
    NodeRegistry.registerNodeType('MathNode', MathNode);
    
    // 创建节点实例
    const node = NodeRegistry.createNode('MathNode', ['multiply']) as MathNode;
    
    // 验证节点实例
    expect(node).toBeInstanceOf(MathNode);
    expect(node.getType()).toBe('MathNode');
    expect(node.operation).toBe('multiply');
  });
  
  // 测试获取所有节点类型
  it('应该能够获取所有节点类型', () => {
    // 注册多个节点类型
    NodeRegistry.registerNodeType('TestNode', TestNode);
    NodeRegistry.registerNodeType('MathNode', MathNode);
    
    // 获取所有节点类型
    const nodeTypes = NodeRegistry.getAllNodeTypes();
    
    // 验证节点类型
    expect(nodeTypes.size).toBe(2);
    expect(nodeTypes.has('TestNode')).toBe(true);
    expect(nodeTypes.has('MathNode')).toBe(true);
  });
  
  // 测试注册节点类别
  it('应该能够注册节点类别', () => {
    // 注册节点类型
    NodeRegistry.registerNodeType('TestNode', TestNode);
    NodeRegistry.registerNodeType('MathNode', MathNode);
    
    // 注册节点类别
    NodeRegistry.registerNodeCategory('Math', ['MathNode']);
    NodeRegistry.registerNodeCategory('Test', ['TestNode']);
    
    // 验证节点类别
    expect(NodeRegistry.hasNodeCategory('Math')).toBe(true);
    expect(NodeRegistry.hasNodeCategory('Test')).toBe(true);
  });
  
  // 测试获取节点类别
  it('应该能够获取节点类别', () => {
    // 注册节点类型
    NodeRegistry.registerNodeType('TestNode', TestNode);
    NodeRegistry.registerNodeType('MathNode', MathNode);
    
    // 注册节点类别
    NodeRegistry.registerNodeCategory('Math', ['MathNode']);
    
    // 获取节点类别
    const category = NodeRegistry.getNodeCategory('Math');
    
    // 验证节点类别
    expect(category).toEqual(['MathNode']);
  });
  
  // 测试获取所有节点类别
  it('应该能够获取所有节点类别', () => {
    // 注册节点类型
    NodeRegistry.registerNodeType('TestNode', TestNode);
    NodeRegistry.registerNodeType('MathNode', MathNode);
    
    // 注册节点类别
    NodeRegistry.registerNodeCategory('Math', ['MathNode']);
    NodeRegistry.registerNodeCategory('Test', ['TestNode']);
    
    // 获取所有节点类别
    const categories = NodeRegistry.getAllNodeCategories();
    
    // 验证节点类别
    expect(categories.size).toBe(2);
    expect(categories.has('Math')).toBe(true);
    expect(categories.has('Test')).toBe(true);
    expect(categories.get('Math')).toEqual(['MathNode']);
    expect(categories.get('Test')).toEqual(['TestNode']);
  });
  
  // 测试添加节点类型到类别
  it('应该能够添加节点类型到类别', () => {
    // 注册节点类型
    NodeRegistry.registerNodeType('TestNode', TestNode);
    NodeRegistry.registerNodeType('MathNode', MathNode);
    
    // 注册节点类别
    NodeRegistry.registerNodeCategory('Common', ['TestNode']);
    
    // 添加节点类型到类别
    NodeRegistry.addNodeTypeToCategory('Common', 'MathNode');
    
    // 获取节点类别
    const category = NodeRegistry.getNodeCategory('Common');
    
    // 验证节点类别
    expect(category).toEqual(['TestNode', 'MathNode']);
  });
  
  // 测试从类别中移除节点类型
  it('应该能够从类别中移除节点类型', () => {
    // 注册节点类型
    NodeRegistry.registerNodeType('TestNode', TestNode);
    NodeRegistry.registerNodeType('MathNode', MathNode);
    
    // 注册节点类别
    NodeRegistry.registerNodeCategory('Common', ['TestNode', 'MathNode']);
    
    // 从类别中移除节点类型
    NodeRegistry.removeNodeTypeFromCategory('Common', 'MathNode');
    
    // 获取节点类别
    const category = NodeRegistry.getNodeCategory('Common');
    
    // 验证节点类别
    expect(category).toEqual(['TestNode']);
  });
  
  // 测试注册节点元数据
  it('应该能够注册节点元数据', () => {
    // 注册节点类型
    NodeRegistry.registerNodeType('TestNode', TestNode);
    
    // 注册节点元数据
    NodeRegistry.registerNodeMetadata('TestNode', {
      displayName: '测试节点',
      description: '这是一个测试节点',
      icon: 'test-icon',
      color: '#ff0000'
    });
    
    // 获取节点元数据
    const metadata = NodeRegistry.getNodeMetadata('TestNode');
    
    // 验证节点元数据
    expect(metadata).toEqual({
      displayName: '测试节点',
      description: '这是一个测试节点',
      icon: 'test-icon',
      color: '#ff0000'
    });
  });
  
  // 测试清理节点注册表
  it('应该能够清理节点注册表', () => {
    // 注册节点类型
    NodeRegistry.registerNodeType('TestNode', TestNode);
    NodeRegistry.registerNodeType('MathNode', MathNode);
    
    // 注册节点类别
    NodeRegistry.registerNodeCategory('Math', ['MathNode']);
    NodeRegistry.registerNodeCategory('Test', ['TestNode']);
    
    // 注册节点元数据
    NodeRegistry.registerNodeMetadata('TestNode', {
      displayName: '测试节点',
      description: '这是一个测试节点'
    });
    
    // 清理节点注册表
    NodeRegistry.clear();
    
    // 验证节点注册表已清理
    expect(NodeRegistry.hasNodeType('TestNode')).toBe(false);
    expect(NodeRegistry.hasNodeType('MathNode')).toBe(false);
    expect(NodeRegistry.hasNodeCategory('Math')).toBe(false);
    expect(NodeRegistry.hasNodeCategory('Test')).toBe(false);
    expect(NodeRegistry.getNodeMetadata('TestNode')).toBeUndefined();
  });
});
