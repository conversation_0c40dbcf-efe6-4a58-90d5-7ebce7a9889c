import type { Entity } from '../core/Entity';
import { InputSystem } from '../input/InputSystem';
import { AnimationBlender } from './AnimationBlender';
/**
 * 输入动画集成事件类型
 */
export declare enum InputAnimationEventType {
    /** 输入状态改变 */
    INPUT_STATE_CHANGED = "inputStateChanged",
    /** 动作触发 */
    ACTION_TRIGGERED = "actionTriggered",
    /** 动作结束 */
    ACTION_ENDED = "actionEnded"
}
/**
 * 输入动画集成配置
 */
export interface InputAnimationIntegrationConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否自动更新动画参数 */
    autoUpdateParameters?: boolean;
    /** 移动输入动作名称 */
    moveActionName?: string;
    /** 跳跃输入动作名称 */
    jumpActionName?: string;
    /** 奔跑输入动作名称 */
    runActionName?: string;
    /** 攻击输入动作名称 */
    attackActionName?: string;
    /** 防御输入动作名称 */
    defendActionName?: string;
    /** 交互输入动作名称 */
    interactActionName?: string;
    /** 自定义输入动作映射 */
    customActionMappings?: Map<string, string>;
    /** 是否使用手势输入 */
    useGestureInput?: boolean;
    /** 是否使用语音输入 */
    useVoiceInput?: boolean;
}
/**
 * 输入动画集成
 * 用于将输入系统与动画系统集成
 */
export declare class InputAnimationIntegration {
    /** 实体 */
    private entity;
    /** 输入系统 */
    private inputSystem;
    /** 动画混合器 */
    private blender;
    /** 动画控制器 */
    private animator;
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 输入动作映射 */
    private actionMappings;
    /** 当前输入状态 */
    private inputState;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /**
     * 创建输入动画集成
     * @param entity 实体
     * @param inputSystem 输入系统
     * @param blender 动画混合器
     * @param config 配置
     */
    constructor(entity: Entity, inputSystem: InputSystem, blender: AnimationBlender, config?: InputAnimationIntegrationConfig);
    /**
     * 初始化
     */
    initialize(): void;
    /**
     * 销毁
     */
    destroy(): void;
    /**
     * 更新
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: InputAnimationEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: InputAnimationEventType, listener: (data: any) => void): void;
    /**
     * 初始化动作映射
     */
    private initializeActionMappings;
    /**
     * 添加输入事件监听器
     */
    private addInputEventListeners;
    /**
     * 移除输入事件监听器
     */
    private removeInputEventListeners;
    /**
     * 更新动画参数
     */
    private updateAnimationParameters;
    /**
     * 处理输入改变事件
     * @param actionName 动作名称
     * @param paramName 参数名称
     * @param action 输入动作
     */
    private handleInputChanged;
    /**
     * 处理输入开始事件
     * @param actionName 动作名称
     * @param paramName 参数名称
     * @param action 输入动作
     */
    private handleInputStarted;
    /**
     * 处理输入结束事件
     * @param actionName 动作名称
     * @param paramName 参数名称
     * @param action 输入动作
     */
    private handleInputEnded;
    /**
     * 处理手势输入
     * @param event 手势事件
     */
    private handleGestureInput;
    /**
     * 处理语音输入
     * @param event 语音事件
     */
    private handleVoiceInput;
}
