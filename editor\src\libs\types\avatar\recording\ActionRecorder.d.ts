/**
 * 动作录制器
 * 用于录制角色动作序列并保存为可回放的数据
 */
import type { Entity } from '../../core/Entity';
import { ActionControlSystem, ActionData } from '../controllers/ActionControlSystem';
import { AdvancedCharacterController } from '../controllers/AdvancedCharacterController';
/**
 * 录制的动作事件
 */
export interface RecordedActionEvent {
    /** 动作ID */
    actionId: string;
    /** 动作数据 */
    actionData: ActionData;
    /** 事件类型 */
    eventType: 'start' | 'stop';
    /** 时间戳 */
    timestamp: number;
    /** 参数 */
    params?: Record<string, any>;
}
/**
 * 录制的输入事件
 */
export interface RecordedInputEvent {
    /** 输入类型 */
    inputType: string;
    /** 输入值 */
    value: any;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 录制的变换事件
 */
export interface RecordedTransformEvent {
    /** 位置 */
    position: {
        x: number;
        y: number;
        z: number;
    };
    /** 旋转 */
    rotation: {
        x: number;
        y: number;
        z: number;
        w: number;
    };
    /** 时间戳 */
    timestamp: number;
}
/**
 * 动作录制数据
 */
export interface ActionRecording {
    /** 录制ID */
    id: string;
    /** 录制名称 */
    name: string;
    /** 开始时间戳 */
    startTimestamp: number;
    /** 结束时间戳 */
    endTimestamp: number;
    /** 动作事件 */
    actionEvents: RecordedActionEvent[];
    /** 输入事件 */
    inputEvents: RecordedInputEvent[];
    /** 变换事件 */
    transformEvents: RecordedTransformEvent[];
    /** 元数据 */
    metadata?: Record<string, any>;
}
/**
 * 动作录制器配置
 */
export interface ActionRecorderConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否记录输入 */
    recordInput?: boolean;
    /** 是否记录变换 */
    recordTransform?: boolean;
    /** 变换记录频率（毫秒） */
    transformRecordFrequency?: number;
    /** 是否自动开始录制 */
    autoStart?: boolean;
}
/**
 * 动作录制器
 */
export declare class ActionRecorder {
    /** 实体 */
    private entity;
    /** 动作控制系统 */
    private actionControlSystem;
    /** 角色控制器 */
    private characterController;
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否正在录制 */
    private isRecording;
    /** 当前录制 */
    private currentRecording;
    /** 变换记录定时器 */
    private transformRecordTimer;
    /** 动作开始事件处理器 */
    private actionStartHandler;
    /** 动作结束事件处理器 */
    private actionEndHandler;
    /** 输入事件处理器 */
    private inputHandler;
    /**
     * 构造函数
     * @param entity 实体
     * @param actionControlSystem 动作控制系统
     * @param config 配置
     * @param characterController 角色控制器（可选）
     */
    constructor(entity: Entity, actionControlSystem: ActionControlSystem, config?: ActionRecorderConfig, characterController?: AdvancedCharacterController);
    /**
     * 设置角色控制器
     * @param characterController 角色控制器
     */
    setCharacterController(characterController: AdvancedCharacterController | null): void;
    /**
     * 获取角色控制器
     * @returns 角色控制器
     */
    getCharacterController(): AdvancedCharacterController | null;
    /**
     * 开始录制
     * @param name 录制名称
     * @returns 是否成功开始录制
     */
    startRecording(name?: string): boolean;
    /**
     * 停止录制
     * @returns 录制数据
     */
    stopRecording(): ActionRecording | null;
    /**
     * 添加事件监听器
     */
    private addEventListeners;
    /**
     * 移除事件监听器
     */
    private removeEventListeners;
    /**
     * 开始记录变换
     */
    private startRecordingTransform;
    /**
     * 记录变换
     */
    private recordTransform;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    on(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    off(event: string, callback: (...args: any[]) => void): void;
    /**
     * 保存录制到文件
     * @param recording 录制数据
     * @param filename 文件名
     */
    static saveToFile(recording: ActionRecording, filename?: string): void;
    /**
     * 从文件加载录制
     * @param file 文件
     * @returns Promise<ActionRecording>
     */
    static loadFromFile(file: File): Promise<ActionRecording>;
}
