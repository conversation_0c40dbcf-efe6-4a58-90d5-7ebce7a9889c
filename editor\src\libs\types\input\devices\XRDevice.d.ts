/// <reference types="webxr" />
/**
 * XR输入设备
 */
import { BaseInputDevice } from '../InputDevice';
/**
 * XR输入设备
 */
export declare class XRDevice extends BaseInputDevice {
    /** XR会话 */
    private session;
    /** 输入源映射 */
    private inputSources;
    /** XR事件处理器 */
    private xrEventHandlers;
    /**
     * 创建XR输入设备
     */
    constructor();
    /**
     * 初始化事件处理器
     */
    private initEventHandlers;
    /**
     * 初始化设备
     */
    initialize(): void;
    /**
     * 销毁设备
     */
    destroy(): void;
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新输入源状态
     */
    private updateInputSources;
    /**
     * 开始XR会话
     * @param sessionType 会话类型
     * @param sessionOptions 会话选项
     * @returns 是否成功
     */
    startSession(sessionType?: XRSessionMode, sessionOptions?: XRSessionInit): Promise<boolean>;
    /**
     * 结束XR会话
     */
    endSession(): Promise<void>;
    /**
     * 添加事件监听器
     */
    private addEventListeners;
    /**
     * 移除事件监听器
     */
    private removeEventListeners;
    /**
     * 处理输入源变化事件
     * @param event 输入源变化事件
     */
    private handleInputSourcesChange;
    /**
     * 处理选择开始事件
     * @param event 选择事件
     */
    private handleSelectStart;
    /**
     * 处理选择结束事件
     * @param event 选择事件
     */
    private handleSelectEnd;
    /**
     * 处理选择事件
     * @param event 选择事件
     */
    private handleSelect;
    /**
     * 处理挤压开始事件
     * @param event 挤压事件
     */
    private handleSqueezeStart;
    /**
     * 处理挤压结束事件
     * @param event 挤压事件
     */
    private handleSqueezeEnd;
    /**
     * 处理挤压事件
     * @param event 挤压事件
     */
    private handleSqueeze;
    /**
     * 获取输入源ID
     * @param inputSource 输入源
     * @returns 输入源ID
     */
    private getInputSourceId;
    /**
     * 获取XR会话
     * @returns XR会话
     */
    getSession(): XRSession | null;
    /**
     * 获取输入源
     * @param id 输入源ID
     * @returns 输入源
     */
    getInputSource(id: number): XRInputSource | undefined;
    /**
     * 获取所有输入源
     * @returns 输入源列表
     */
    getInputSources(): XRInputSource[];
    /**
     * 检查输入源是否活跃
     * @param id 输入源ID
     * @returns 是否活跃
     */
    isInputSourceActive(id: number): boolean;
    /**
     * 检查输入源是否选择中
     * @param id 输入源ID
     * @returns 是否选择中
     */
    isInputSourceSelecting(id: number): boolean;
    /**
     * 检查输入源是否挤压中
     * @param id 输入源ID
     * @returns 是否挤压中
     */
    isInputSourceSqueezing(id: number): boolean;
    /**
     * 检查按钮是否按下
     * @param inputSourceId 输入源ID
     * @param buttonIndex 按钮索引
     * @returns 是否按下
     */
    isButtonPressed(inputSourceId: number, buttonIndex: number): boolean;
    /**
     * 获取按钮值
     * @param inputSourceId 输入源ID
     * @param buttonIndex 按钮索引
     * @returns 按钮值
     */
    getButtonValue(inputSourceId: number, buttonIndex: number): number;
    /**
     * 获取轴值
     * @param inputSourceId 输入源ID
     * @param axisIndex 轴索引
     * @returns 轴值
     */
    getAxisValue(inputSourceId: number, axisIndex: number): number;
}
