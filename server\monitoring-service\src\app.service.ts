import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { v4 as uuidv4 } from 'uuid';
import { firstValueFrom } from 'rxjs';

/**
 * 应用主服务
 * 提供基本的应用信息和健康状态
 */
@Injectable()
export class AppService implements OnModuleInit {
  private readonly logger = new Logger(AppService.name);
  private readonly instanceId = uuidv4();

  constructor(
    private readonly configService: ConfigService,
    @Inject('SERVICE_REGISTRY') private readonly serviceRegistry: ClientProxy,
  ) {}

  async onModuleInit() {
    await this.registerService();
    this.startHeartbeat();
  }
  /**
   * 获取欢迎信息
   */
  getHello(): string {
    return '监控服务运行正常！';
  }

  /**
   * 获取健康状态
   */
  getHealth(): object {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'monitoring-service',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
    };
  }

  /**
   * 获取版本信息
   */
  getVersion(): object {
    return {
      service: 'monitoring-service',
      version: '1.0.0',
      buildTime: new Date().toISOString(),
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development',
      instanceId: this.instanceId,
    };
  }

  private async registerService() {
    try {
      const host = this.configService.get<string>('MONITORING_SERVICE_HOST', 'localhost');
      const port = this.configService.get<number>('MONITORING_SERVICE_PORT', 3013);
      const httpPort = this.configService.get<number>('MONITORING_SERVICE_HTTP_PORT', 3013);

      await firstValueFrom(
        this.serviceRegistry.send({ cmd: 'register' }, {
          name: 'monitoring-service',
          description: 'DL（Digital Learning）引擎监控服务',
          instanceId: this.instanceId,
          host,
          port,
          httpPort,
          metadata: {
            version: '1.0.0',
            environment: this.configService.get<string>('NODE_ENV', 'development'),
          },
        }),
      );

      this.logger.log('监控服务已注册到服务注册中心');
    } catch (error) {
      this.logger.error('注册服务失败', error);
    }
  }

  private startHeartbeat() {
    setInterval(async () => {
      try {
        await firstValueFrom(
          this.serviceRegistry.send({ cmd: 'heartbeat' }, {
            name: 'monitoring-service',
            instanceId: this.instanceId,
            status: {
              uptime: process.uptime(),
              memory: process.memoryUsage(),
            },
          }),
        );
        this.logger.debug('发送心跳成功');
      } catch (error) {
        this.logger.error('发送心跳失败', error);
      }
    }, 30000); // 每30秒发送一次心跳
  }
}
