<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>动画系统演示</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      font-family: 'Microsoft YaHei', sans-serif;
    }
    #canvas-container {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    #ui-container {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 10px;
      border-radius: 5px;
      width: 300px;
      z-index: 10;
    }
    h2 {
      margin-top: 0;
      color: #333;
    }
    .demo-group {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 5px;
    }
    .demo-title {
      font-weight: bold;
      color: #9c27b0;
      margin-bottom: 5px;
    }
    .demo-description {
      font-size: 14px;
      color: #555;
      margin-bottom: 10px;
    }
    .control-group {
      margin-bottom: 10px;
    }
    label {
      display: block;
      margin-bottom: 3px;
      font-size: 14px;
    }
    input[type="range"] {
      width: 100%;
    }
    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 10px;
    }
    button {
      background-color: #9c27b0;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 3px;
      cursor: pointer;
      flex-grow: 1;
      min-width: 80px;
    }
    button:hover {
      background-color: #7b1fa2;
    }
    .info-box {
      background-color: #f3e5f5;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      font-size: 14px;
      color: #4a148c;
      border-left: 4px solid #9c27b0;
    }
    .animation-controls {
      margin-top: 15px;
    }
    .tab-buttons {
      display: flex;
      margin-bottom: 10px;
    }
    .tab-button {
      flex-grow: 1;
      padding: 8px;
      background-color: #e0e0e0;
      border: none;
      cursor: pointer;
      text-align: center;
    }
    .tab-button.active {
      background-color: #9c27b0;
      color: white;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    .animation-timeline {
      height: 60px;
      background-color: #e0e0e0;
      border-radius: 3px;
      margin-top: 10px;
      position: relative;
      overflow: hidden;
    }
    .timeline-marker {
      position: absolute;
      top: 0;
      height: 100%;
      width: 2px;
      background-color: #f44336;
      z-index: 2;
    }
    .timeline-keyframes {
      position: absolute;
      top: 10px;
      height: 40px;
      width: 100%;
    }
    .keyframe {
      position: absolute;
      width: 10px;
      height: 10px;
      background-color: #9c27b0;
      border-radius: 50%;
      top: 15px;
      transform: translateX(-50%);
    }
    .timeline-segment {
      position: absolute;
      height: 5px;
      top: 20px;
      background-color: #9c27b0;
    }
  </style>
</head>
<body>
  <div id="canvas-container"></div>
  <div id="ui-container">
    <h2>动画系统演示</h2>
    
    <div class="info-box">
      本示例展示了DL（Digital Learning）引擎的动画系统功能，包括关键帧动画、骨骼动画、混合动画等。
      使用下方的控制面板来尝试不同的动画效果。
    </div>
    
    <div class="tab-buttons">
      <button class="tab-button active" data-tab="keyframe">关键帧动画</button>
      <button class="tab-button" data-tab="skeletal">骨骼动画</button>
      <button class="tab-button" data-tab="blending">动画混合</button>
    </div>
    
    <div id="keyframe-tab" class="tab-content active">
      <div class="demo-group">
        <div class="demo-title">基础关键帧动画</div>
        <div class="demo-description">创建和控制基于属性的关键帧动画。</div>
        
        <div class="button-group">
          <button id="play-rotation">旋转动画</button>
          <button id="play-scale">缩放动画</button>
          <button id="play-color">颜色动画</button>
        </div>
        
        <div class="control-group">
          <label for="keyframe-duration">动画时长: <span id="keyframe-duration-value">2.0</span>秒</label>
          <input type="range" id="keyframe-duration" min="0.5" max="5" step="0.1" value="2.0">
        </div>
        
        <div class="control-group">
          <label for="keyframe-easing">缓动函数:</label>
          <select id="keyframe-easing">
            <option value="linear">线性</option>
            <option value="easeInOut">缓入缓出</option>
            <option value="easeIn">缓入</option>
            <option value="easeOut">缓出</option>
            <option value="bounce">弹跳</option>
            <option value="elastic">弹性</option>
          </select>
        </div>
        
        <div class="animation-timeline">
          <div class="timeline-marker" style="left: 0%;"></div>
          <div class="timeline-keyframes">
            <div class="keyframe" style="left: 0%;"></div>
            <div class="timeline-segment" style="left: 0%; width: 50%;"></div>
            <div class="keyframe" style="left: 50%;"></div>
            <div class="timeline-segment" style="left: 50%; width: 50%;"></div>
            <div class="keyframe" style="left: 100%;"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div id="skeletal-tab" class="tab-content">
      <div class="demo-group">
        <div class="demo-title">骨骼动画</div>
        <div class="demo-description">加载和播放骨骼动画，控制动画状态。</div>
        
        <div class="button-group">
          <button id="load-character">加载角色</button>
          <button id="play-walk">行走</button>
          <button id="play-run">奔跑</button>
          <button id="play-jump">跳跃</button>
        </div>
        
        <div class="control-group">
          <label for="animation-speed">播放速度: <span id="animation-speed-value">1.0</span>x</label>
          <input type="range" id="animation-speed" min="0.1" max="2" step="0.1" value="1.0">
        </div>
        
        <div class="control-group">
          <label for="animation-weight">动画权重: <span id="animation-weight-value">1.0</span></label>
          <input type="range" id="animation-weight" min="0" max="1" step="0.01" value="1.0">
        </div>
      </div>
    </div>
    
    <div id="blending-tab" class="tab-content">
      <div class="demo-group">
        <div class="demo-title">动画混合</div>
        <div class="demo-description">混合多个动画，创建平滑过渡和复杂动作。</div>
        
        <div class="button-group">
          <button id="blend-walk-run">行走-奔跑混合</button>
          <button id="blend-upper-lower">上下身混合</button>
          <button id="blend-additive">叠加混合</button>
        </div>
        
        <div class="control-group">
          <label for="blend-factor">混合因子: <span id="blend-factor-value">0.5</span></label>
          <input type="range" id="blend-factor" min="0" max="1" step="0.01" value="0.5">
        </div>
        
        <div class="control-group">
          <label for="blend-time">过渡时间: <span id="blend-time-value">0.5</span>秒</label>
          <input type="range" id="blend-time" min="0" max="2" step="0.1" value="0.5">
        </div>
      </div>
      
      <div class="demo-group">
        <div class="demo-title">动画状态机</div>
        <div class="demo-description">使用状态机管理复杂的动画逻辑和过渡。</div>
        
        <div class="button-group">
          <button id="state-idle">待机</button>
          <button id="state-walk">行走</button>
          <button id="state-run">奔跑</button>
          <button id="state-jump">跳跃</button>
        </div>
        
        <div class="control-group">
          <label for="state-parameter">状态参数 (速度): <span id="state-parameter-value">0.0</span></label>
          <input type="range" id="state-parameter" min="0" max="1" step="0.01" value="0.0">
        </div>
      </div>
    </div>
    
    <div class="button-group">
      <button id="reset-animation">重置动画</button>
      <button id="toggle-debug">显示/隐藏骨骼</button>
    </div>
  </div>

  <script type="module">
    import { Engine, World, Entity, Scene, Transform, Camera, Mesh, Material, Light, AnimationSystem, AnimationComponent, KeyframeAnimation, SkeletalAnimation, AnimationMixer, AnimationStateMachine } from '/engine/dist/index.js';

    // 初始化引擎
    const engine = new Engine({
      container: document.getElementById('canvas-container'),
      antialias: true,
      shadows: true,
    });

    // 创建世界
    const world = new World(engine);

    // 创建动画系统
    const animationSystem = new AnimationSystem(world);
    world.addSystem(animationSystem);

    // 创建场景
    const scene = new Scene(world, {
      name: '动画系统演示场景',
      background: { type: 'color', value: '#87CEEB' },
    });

    // 创建相机
    const camera = new Entity(world)
      .addComponent(new Camera({
        type: 'perspective',
        fov: 60,
        near: 0.1,
        far: 1000,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 2, z: 5 },
        rotation: { x: -0.2, y: 0, z: 0 },
      }));

    // 添加相机到场景
    scene.addEntity(camera);

    // 创建环境光
    const ambientLight = new Entity(world)
      .addComponent(new Light({
        type: 'ambient',
        color: '#ffffff',
        intensity: 0.3,
      }));

    // 添加环境光到场景
    scene.addEntity(ambientLight);

    // 创建平行光
    const directionalLight = new Entity(world)
      .addComponent(new Light({
        type: 'directional',
        color: '#ffffff',
        intensity: 0.8,
        castShadow: true,
      }))
      .addComponent(new Transform({
        position: { x: 5, y: 10, z: 5 },
        rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
      }));

    // 添加平行光到场景
    scene.addEntity(directionalLight);

    // 创建地面
    const ground = new Entity(world)
      .addComponent(new Mesh({
        geometry: { type: 'plane', width: 20, height: 20 },
        receiveShadow: true,
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#cccccc',
        roughness: 0.9,
        metalness: 0.1,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: -Math.PI / 2, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      }));

    // 添加地面到场景
    scene.addEntity(ground);

    // 创建演示立方体
    const cube = new Entity(world)
      .addComponent(new Mesh({
        geometry: { type: 'box', width: 1, height: 1, depth: 1 },
        castShadow: true,
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#9c27b0',
        roughness: 0.5,
        metalness: 0.5,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 1, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      }))
      .addComponent(new AnimationComponent());

    // 添加立方体到场景
    scene.addEntity(cube);

    // 创建关键帧动画
    const rotationAnimation = new KeyframeAnimation({
      name: 'rotation',
      duration: 2.0,
      loop: true,
      keyframes: [
        { time: 0.0, property: 'rotation', value: { x: 0, y: 0, z: 0 } },
        { time: 1.0, property: 'rotation', value: { x: 0, y: Math.PI, z: 0 } },
        { time: 2.0, property: 'rotation', value: { x: 0, y: Math.PI * 2, z: 0 } }
      ]
    });

    const scaleAnimation = new KeyframeAnimation({
      name: 'scale',
      duration: 2.0,
      loop: true,
      keyframes: [
        { time: 0.0, property: 'scale', value: { x: 1, y: 1, z: 1 } },
        { time: 1.0, property: 'scale', value: { x: 1.5, y: 0.5, z: 1.5 } },
        { time: 2.0, property: 'scale', value: { x: 1, y: 1, z: 1 } }
      ]
    });

    const colorAnimation = new KeyframeAnimation({
      name: 'color',
      duration: 2.0,
      loop: true,
      keyframes: [
        { time: 0.0, property: 'material.color', value: '#9c27b0' },
        { time: 1.0, property: 'material.color', value: '#2196f3' },
        { time: 2.0, property: 'material.color', value: '#9c27b0' }
      ]
    });

    // 添加动画到动画组件
    const cubeAnimator = cube.getComponent(AnimationComponent);
    cubeAnimator.addAnimation(rotationAnimation);
    cubeAnimator.addAnimation(scaleAnimation);
    cubeAnimator.addAnimation(colorAnimation);

    // 启动引擎
    engine.start();

    // UI控制
    const keyframeDurationInput = document.getElementById('keyframe-duration');
    const keyframeDurationValue = document.getElementById('keyframe-duration-value');
    const keyframeEasingSelect = document.getElementById('keyframe-easing');
    const timelineMarker = document.querySelector('.timeline-marker');

    // 更新动画时长
    keyframeDurationInput.addEventListener('input', (e) => {
      const duration = parseFloat(e.target.value);
      keyframeDurationValue.textContent = duration.toFixed(1);
      
      // 更新动画时长
      rotationAnimation.duration = duration;
      scaleAnimation.duration = duration;
      colorAnimation.duration = duration;
    });

    // 更新缓动函数
    keyframeEasingSelect.addEventListener('change', (e) => {
      const easing = e.target.value;
      
      // 更新动画缓动函数
      rotationAnimation.easing = easing;
      scaleAnimation.easing = easing;
      colorAnimation.easing = easing;
    });

    // 播放旋转动画
    document.getElementById('play-rotation').addEventListener('click', () => {
      cubeAnimator.stopAllAnimations();
      cubeAnimator.playAnimation('rotation');
    });

    // 播放缩放动画
    document.getElementById('play-scale').addEventListener('click', () => {
      cubeAnimator.stopAllAnimations();
      cubeAnimator.playAnimation('scale');
    });

    // 播放颜色动画
    document.getElementById('play-color').addEventListener('click', () => {
      cubeAnimator.stopAllAnimations();
      cubeAnimator.playAnimation('color');
    });

    // 重置动画
    document.getElementById('reset-animation').addEventListener('click', () => {
      cubeAnimator.stopAllAnimations();
      
      // 重置立方体属性
      cube.getComponent(Transform).rotation = { x: 0, y: 0, z: 0 };
      cube.getComponent(Transform).scale = { x: 1, y: 1, z: 1 };
      cube.getComponent(Material).color = '#9c27b0';
    });

    // 标签切换
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.getAttribute('data-tab');
        
        // 更新按钮状态
        tabButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        // 更新内容状态
        tabContents.forEach(content => content.classList.remove('active'));
        document.getElementById(`${tabId}-tab`).classList.add('active');
      });
    });

    // 更新时间轴标记
    let lastTime = 0;
    engine.onUpdate = (deltaTime) => {
      // 更新时间轴标记位置
      if (cubeAnimator.isPlaying) {
        const currentAnimation = cubeAnimator.getCurrentAnimation();
        if (currentAnimation) {
          const progress = (currentAnimation.currentTime / currentAnimation.duration) * 100;
          timelineMarker.style.left = `${progress}%`;
        }
      }
    };
  </script>
</body>
</html>
