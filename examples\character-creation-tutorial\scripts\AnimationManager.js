/**
 * 动画管理器
 * 负责处理角色动画的导入、设置和预览
 */
export class AnimationManager {
  /**
   * 构造函数
   */
  constructor() {
    this.character = null;
    this.animations = [];
    this.currentAnimation = null;
    this.isPlaying = false;
    this.loopMode = 'loop'; // 'loop', 'once', 'pingpong'
    this.playbackSpeed = 1.0;
    this.currentTime = 0;
    
    // 示例动画列表
    this.sampleAnimations = [
      {
        name: '待机',
        path: '../assets/animations/idle.fbx',
        thumbnail: '../assets/images/thumbnails/idle.jpg',
        description: '角色待机动画，适合作为默认状态'
      },
      {
        name: '行走',
        path: '../assets/animations/walk.fbx',
        thumbnail: '../assets/images/thumbnails/walk.jpg',
        description: '角色行走动画，适合低速移动'
      },
      {
        name: '跑步',
        path: '../assets/animations/run.fbx',
        thumbnail: '../assets/images/thumbnails/run.jpg',
        description: '角色跑步动画，适合高速移动'
      },
      {
        name: '跳跃',
        path: '../assets/animations/jump.fbx',
        thumbnail: '../assets/images/thumbnails/jump.jpg',
        description: '角色跳跃动画，包含起跳和落地阶段'
      },
      {
        name: '攻击',
        path: '../assets/animations/attack.fbx',
        thumbnail: '../assets/images/thumbnails/attack.jpg',
        description: '角色攻击动画，适合战斗场景'
      }
    ];
  }
  
  /**
   * 初始化
   * @param {Object} character 角色对象
   */
  initialize(character) {
    this.character = character;
  }
  
  /**
   * 导入动画
   * @param {string} path 动画文件路径
   * @param {Object} options 导入选项
   */
  importAnimation(path, options = {}) {
    // 在实际应用中，这里应该使用引擎的动画导入功能
    // 这里使用模拟代码
    
    console.log('导入动画:', path, options);
    
    // 创建加载提示
    this.showLoadingIndicator();
    
    // 模拟加载延迟
    setTimeout(() => {
      // 创建动画对象
      const animation = {
        id: `anim_${this.animations.length + 1}`,
        name: options.name || `动画 ${this.animations.length + 1}`,
        path: path,
        duration: options.duration || 2.0,
        frameRate: options.frameRate || 30,
        startFrame: options.startFrame || 0,
        endFrame: options.endFrame || 60,
        loopable: options.loopable !== undefined ? options.loopable : true,
        events: options.events || []
      };
      
      // 添加到动画列表
      this.animations.push(animation);
      
      // 隐藏加载提示
      this.hideLoadingIndicator();
      
      // 显示成功消息
      this.showSuccessMessage('动画导入成功！');
      
      // 触发动画导入完成事件
      this.onAnimationImported(animation);
      
      return animation;
    }, 1500);
  }
  
  /**
   * 使用示例动画
   */
  useSampleAnimation() {
    // 显示示例动画选择对话框
    this.showSampleAnimationDialog();
  }
  
  /**
   * 显示示例动画选择对话框
   */
  showSampleAnimationDialog() {
    // 创建对话框
    const dialog = document.createElement('div');
    dialog.className = 'sample-animation-dialog';
    dialog.innerHTML = `
      <div class="dialog-header">
        <h2>选择示例动画</h2>
        <button class="close-button">&times;</button>
      </div>
      <div class="dialog-content">
        <div class="animation-grid">
          ${this.sampleAnimations.map((animation, index) => `
            <div class="animation-card" data-index="${index}">
              <div class="animation-thumbnail" style="background-image: url('${animation.thumbnail}')"></div>
              <div class="animation-info">
                <h3>${animation.name}</h3>
                <p>${animation.description}</p>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
      <div class="dialog-footer">
        <button class="cancel-button">取消</button>
        <button class="select-button">选择</button>
      </div>
    `;
    
    // 添加到文档
    document.body.appendChild(dialog);
    
    // 设置事件
    const closeButton = dialog.querySelector('.close-button');
    const cancelButton = dialog.querySelector('.cancel-button');
    const selectButton = dialog.querySelector('.select-button');
    const animationCards = dialog.querySelectorAll('.animation-card');
    
    let selectedIndex = -1;
    
    // 关闭对话框
    const closeDialog = () => {
      document.body.removeChild(dialog);
    };
    
    // 选择动画
    const selectAnimation = (index) => {
      selectedIndex = index;
      animationCards.forEach((card, i) => {
        if (i === index) {
          card.classList.add('selected');
        } else {
          card.classList.remove('selected');
        }
      });
    };
    
    // 确认选择
    const confirmSelection = () => {
      if (selectedIndex >= 0) {
        const animation = this.sampleAnimations[selectedIndex];
        this.importAnimation(animation.path, {
          name: animation.name
        });
        closeDialog();
      }
    };
    
    // 绑定事件
    closeButton.addEventListener('click', closeDialog);
    cancelButton.addEventListener('click', closeDialog);
    selectButton.addEventListener('click', confirmSelection);
    
    animationCards.forEach((card, index) => {
      card.addEventListener('click', () => selectAnimation(index));
    });
  }
  
  /**
   * 打开动画选择器
   */
  openAnimationSelector() {
    // 创建文件输入元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.fbx,.gltf,.glb,.bvh';
    fileInput.style.display = 'none';
    
    // 添加到文档
    document.body.appendChild(fileInput);
    
    // 设置事件
    fileInput.addEventListener('change', (event) => {
      if (event.target.files.length > 0) {
        const file = event.target.files[0];
        const path = URL.createObjectURL(file);
        this.importAnimation(path, {
          name: file.name.split('.')[0]
        });
      }
      
      // 移除元素
      document.body.removeChild(fileInput);
    });
    
    // 触发点击
    fileInput.click();
  }
  
  /**
   * 播放动画
   * @param {string} animationId 动画ID
   * @param {Object} options 播放选项
   */
  playAnimation(animationId, options = {}) {
    const animation = this.animations.find(anim => anim.id === animationId);
    
    if (animation) {
      this.currentAnimation = animation;
      this.isPlaying = true;
      this.loopMode = options.loopMode || this.loopMode;
      this.playbackSpeed = options.playbackSpeed || this.playbackSpeed;
      this.currentTime = options.startTime || 0;
      
      console.log('播放动画:', animation.name, options);
      
      // 在实际应用中，这里应该使用引擎的动画播放功能
      // 这里使用模拟代码
      
      // 更新UI
      this.updatePlaybackUI();
    }
  }
  
  /**
   * 停止动画
   */
  stopAnimation() {
    this.isPlaying = false;
    this.currentTime = 0;
    
    console.log('停止动画');
    
    // 更新UI
    this.updatePlaybackUI();
  }
  
  /**
   * 暂停动画
   */
  pauseAnimation() {
    this.isPlaying = false;
    
    console.log('暂停动画');
    
    // 更新UI
    this.updatePlaybackUI();
  }
  
  /**
   * 恢复动画
   */
  resumeAnimation() {
    this.isPlaying = true;
    
    console.log('恢复动画');
    
    // 更新UI
    this.updatePlaybackUI();
  }
  
  /**
   * 设置播放速度
   * @param {number} speed 播放速度
   */
  setPlaybackSpeed(speed) {
    this.playbackSpeed = speed;
    
    console.log('设置播放速度:', speed);
    
    // 更新UI
    this.updatePlaybackUI();
  }
  
  /**
   * 设置循环模式
   * @param {string} mode 循环模式
   */
  setLoopMode(mode) {
    this.loopMode = mode;
    
    console.log('设置循环模式:', mode);
    
    // 更新UI
    this.updatePlaybackUI();
  }
  
  /**
   * 更新播放UI
   */
  updatePlaybackUI() {
    // 在实际应用中，这里应该更新UI显示当前播放状态
    console.log('更新播放UI:', {
      animation: this.currentAnimation ? this.currentAnimation.name : 'None',
      isPlaying: this.isPlaying,
      loopMode: this.loopMode,
      playbackSpeed: this.playbackSpeed,
      currentTime: this.currentTime
    });
  }
  
  /**
   * 显示加载提示
   */
  showLoadingIndicator() {
    const loadingIndicator = document.createElement('div');
    loadingIndicator.id = 'loading-indicator';
    loadingIndicator.innerHTML = `
      <div class="spinner"></div>
      <div class="loading-text">正在导入动画...</div>
    `;
    
    document.body.appendChild(loadingIndicator);
  }
  
  /**
   * 隐藏加载提示
   */
  hideLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
      document.body.removeChild(loadingIndicator);
    }
  }
  
  /**
   * 显示成功消息
   * @param {string} message 消息内容
   */
  showSuccessMessage(message) {
    const messageElement = document.createElement('div');
    messageElement.className = 'success-message';
    messageElement.textContent = message;
    
    document.body.appendChild(messageElement);
    
    // 自动隐藏
    setTimeout(() => {
      document.body.removeChild(messageElement);
    }, 3000);
  }
  
  /**
   * 动画导入完成事件
   * @param {Object} animation 导入的动画
   */
  onAnimationImported(animation) {
    // 这里可以触发事件或回调
    console.log('动画导入完成:', animation);
    
    // 在实际应用中，这里应该通知其他组件动画已导入完成
  }
}
