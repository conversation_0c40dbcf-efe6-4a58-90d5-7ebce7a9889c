import { Controller, Post, Body, Get, Param, Delete, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { WebRTCService } from './webrtc.service';
import { DataChannelService } from './data-channel.service';

@Controller('webrtc')
export class WebRTCController {
  private readonly logger = new Logger(WebRTCController.name);

  constructor(
    private readonly webrtcService: WebRTCService,
    private readonly dataChannelService: DataChannelService,
  ) {}

  /**
   * 获取WebRTC服务器信息
   */
  @Get('info')
  getWebRTCInfo() {
    return {
      workers: this.webrtcService.getWorkers().length,
      routers: this.webrtcService.getRouters().length,
      webRtcServers: this.webrtcService.getWebRtcServers().length,
      transports: this.webrtcService.getAllTransports().size,
      dataChannels: this.dataChannelService.getChannelStats(),
    };
  }

  /**
   * 获取路由器的RTP能力
   */
  @Get('router/:routerIndex/capabilities')
  getRtpCapabilities(@Param('routerIndex') routerIndex: string) {
    const capabilities = this.webrtcService.getRtpCapabilities(parseInt(routerIndex, 10) || 0);

    if (!capabilities) {
      throw new HttpException('获取RTP能力失败', HttpStatus.NOT_FOUND);
    }

    return capabilities;
  }

  /**
   * 创建WebRTC传输
   */
  @Post('transport')
  async createTransport(@Body() createTransportDto: { routerIndex?: number }) {
    try {
      const transport = await this.webrtcService.createWebRtcTransport(
        createTransportDto.routerIndex || 0
      );

      return {
        id: transport.id,
        iceParameters: transport.iceParameters,
        iceCandidates: transport.iceCandidates,
        dtlsParameters: transport.dtlsParameters,
        sctpParameters: transport.sctpParameters,
      };
    } catch (error) {
      this.logger.error(`创建WebRTC传输失败: ${error.message}`, error.stack);
      throw new HttpException(`创建WebRTC传输失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 连接WebRTC传输
   */
  @Post('transport/:transportId/connect')
  async connectTransport(
    @Param('transportId') transportId: string,
    @Body() connectTransportDto: { dtlsParameters: any },
  ) {
    try {
      const success = await this.webrtcService.connectTransport(
        transportId,
        connectTransportDto.dtlsParameters,
      );

      if (!success) {
        throw new HttpException('连接传输失败', HttpStatus.BAD_REQUEST);
      }

      return { success: true };
    } catch (error) {
      this.logger.error(`连接WebRTC传输失败: ${error.message}`, error.stack);
      throw new HttpException(`连接WebRTC传输失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 创建生产者
   */
  @Post('transport/:transportId/produce')
  async createProducer(
    @Param('transportId') transportId: string,
    @Body() createProducerDto: {
      kind: 'audio' | 'video';
      rtpParameters: any;
      appData?: any;
    },
  ) {
    try {
      const producer = await this.webrtcService.createProducer(
        transportId,
        createProducerDto.rtpParameters,
        createProducerDto.kind,
        createProducerDto.appData,
      );

      if (!producer) {
        throw new HttpException('创建生产者失败', HttpStatus.BAD_REQUEST);
      }

      return {
        id: producer.id,
      };
    } catch (error) {
      this.logger.error(`创建生产者失败: ${error.message}`, error.stack);
      throw new HttpException(`创建生产者失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 创建消费者
   */
  @Post('transport/:transportId/consume')
  async createConsumer(
    @Param('transportId') transportId: string,
    @Body() createConsumerDto: {
      producerId: string;
      rtpCapabilities: any;
      appData?: any;
    },
  ) {
    try {
      const consumer = await this.webrtcService.createConsumer(
        transportId,
        createConsumerDto.producerId,
        createConsumerDto.rtpCapabilities,
        createConsumerDto.appData,
      );

      if (!consumer) {
        throw new HttpException('创建消费者失败', HttpStatus.BAD_REQUEST);
      }

      return {
        id: consumer.id,
        producerId: consumer.producerId,
        kind: consumer.kind,
        rtpParameters: consumer.rtpParameters,
      };
    } catch (error) {
      this.logger.error(`创建消费者失败: ${error.message}`, error.stack);
      throw new HttpException(`创建消费者失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 创建数据生产者
   */
  @Post('transport/:transportId/produce-data')
  async createDataProducer(
    @Param('transportId') transportId: string,
    @Body() createDataProducerDto: {
      label?: string;
      protocol?: string;
      appData?: any;
    },
  ) {
    try {
      const dataProducer = await this.dataChannelService.createDataProducer(
        transportId,
        createDataProducerDto,
      );

      if (!dataProducer) {
        throw new HttpException('创建数据生产者失败', HttpStatus.BAD_REQUEST);
      }

      return {
        id: dataProducer.id,
      };
    } catch (error) {
      this.logger.error(`创建数据生产者失败: ${error.message}`, error.stack);
      throw new HttpException(`创建数据生产者失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 创建数据消费者
   */
  @Post('transport/:transportId/consume-data')
  async createDataConsumer(
    @Param('transportId') transportId: string,
    @Body() createDataConsumerDto: {
      dataProducerId: string;
      appData?: any;
    },
  ) {
    try {
      const dataConsumer = await this.dataChannelService.createDataConsumer(
        transportId,
        createDataConsumerDto.dataProducerId,
        createDataConsumerDto.appData,
      );

      if (!dataConsumer) {
        throw new HttpException('创建数据消费者失败', HttpStatus.BAD_REQUEST);
      }

      return {
        id: dataConsumer.id,
        dataProducerId: dataConsumer.dataProducerId,
        label: dataConsumer.label,
        protocol: dataConsumer.protocol,
      };
    } catch (error) {
      this.logger.error(`创建数据消费者失败: ${error.message}`, error.stack);
      throw new HttpException(`创建数据消费者失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 关闭传输
   */
  @Delete('transport/:transportId')
  closeTransport(@Param('transportId') transportId: string) {
    try {
      this.webrtcService.closeTransport(transportId);
      return { success: true };
    } catch (error) {
      this.logger.error(`关闭传输失败: ${error.message}`, error.stack);
      throw new HttpException(`关闭传输失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 关闭WebRTC传输（POST方式）
   */
  @Post('transport/:id/close')
  closeTransportPost(@Param('id') id: string) {
    try {
      this.webrtcService.closeTransport(id);
      return { success: true, message: `传输已关闭: ${id}` };
    } catch (error) {
      this.logger.error(`关闭WebRTC传输失败: ${error.message}`, error.stack);
      throw new HttpException(`关闭WebRTC传输失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
