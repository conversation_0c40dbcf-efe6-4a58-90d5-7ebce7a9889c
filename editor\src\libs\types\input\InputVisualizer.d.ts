import { InputComponent } from './components/InputComponent';
/**
 * 输入可视化器选项
 */
export interface InputVisualizerOptions {
    /** 容器元素 */
    container?: HTMLElement;
    /** 是否显示设备状态 */
    showDevices?: boolean;
    /** 是否显示动作状态 */
    showActions?: boolean;
    /** 是否显示事件日志 */
    showEventLog?: boolean;
    /** 最大事件日志条数 */
    maxEventLogEntries?: number;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新间隔（毫秒） */
    updateInterval?: number;
}
/**
 * 输入可视化器
 */
export declare class InputVisualizer {
    /** 输入管理器 */
    private inputManager;
    /** 容器元素 */
    private container;
    /** 设备状态元素 */
    private devicesElement;
    /** 动作状态元素 */
    private actionsElement;
    /** 事件日志元素 */
    private eventLogElement;
    /** 是否显示设备状态 */
    private showDevices;
    /** 是否显示动作状态 */
    private showActions;
    /** 是否显示事件日志 */
    private showEventLog;
    /** 最大事件日志条数 */
    private maxEventLogEntries;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新间隔（毫秒） */
    private updateInterval;
    /** 更新定时器 */
    private updateTimer;
    /** 事件日志 */
    private eventLog;
    /** 设备事件处理器 */
    private deviceEventHandlers;
    /** 输入组件列表 */
    private inputComponents;
    /**
     * 创建输入可视化器
     * @param options 选项
     */
    constructor(options?: InputVisualizerOptions);
    /**
     * 创建UI元素
     */
    private createUI;
    /**
     * 添加设备事件监听器
     */
    private addDeviceEventListeners;
    /**
     * 移除设备事件监听器
     */
    private removeDeviceEventListeners;
    /**
     * 获取所有设备
     * @returns 设备列表
     */
    private getAllDevices;
    /**
     * 添加输入组件
     * @param component 输入组件
     */
    addInputComponent(component: InputComponent): void;
    /**
     * 移除输入组件
     * @param component 输入组件
     */
    removeInputComponent(component: InputComponent): void;
    /**
     * 添加事件日志条目
     * @param deviceName 设备名称
     * @param eventType 事件类型
     * @param eventData 事件数据
     */
    private addEventLogEntry;
    /**
     * 清除事件日志
     */
    clearEventLog(): void;
    /**
     * 开始自动更新
     */
    startAutoUpdate(): void;
    /**
     * 停止自动更新
     */
    stopAutoUpdate(): void;
    /**
     * 更新
     */
    update(): void;
    /**
     * 更新设备状态UI
     */
    private updateDevicesUI;
    /**
     * 更新动作状态UI
     */
    private updateActionsUI;
    /**
     * 更新事件日志UI
     */
    private updateEventLogUI;
    /**
     * 格式化值
     * @param value 值
     * @returns 格式化后的值
     */
    private formatValue;
    /**
     * 销毁
     */
    destroy(): void;
}
