/**
 * WebRTC连接管理器
 * 负责管理WebRTC连接的创建、维护和销毁
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';
import { WebRTCConnection, WebRTCConnectionState, WebRTCConnectionConfig } from './WebRTCConnection';
import { NetworkQualityMonitor } from './NetworkQualityMonitor';
import { BandwidthController } from './BandwidthController';

/**
 * WebRTC连接管理器配置
 */
export interface WebRTCConnectionManagerConfig {
  /** ICE服务器配置 */
  iceServers?: RTCIceServer[];
  /** 是否启用数据通道 */
  enableDataChannel?: boolean;
  /** 是否启用音频 */
  enableAudio?: boolean;
  /** 是否启用视频 */
  enableVideo?: boolean;
  /** 是否启用屏幕共享 */
  enableScreenShare?: boolean;
  /** 数据通道配置 */
  dataChannelConfig?: RTCDataChannelInit;
  /** 音频约束 */
  audioConstraints?: MediaTrackConstraints;
  /** 视频约束 */
  videoConstraints?: MediaTrackConstraints;
  /** 是否使用压缩 */
  useCompression?: boolean;
  /** 最大重连次数 */
  maxReconnectAttempts?: number;
  /** 重连间隔（毫秒） */
  reconnectInterval?: number;
  /** 心跳间隔（毫秒） */
  heartbeatInterval?: number;
  /** 连接超时（毫秒） */
  connectionTimeout?: number;
  /** 是否自动重连 */
  autoReconnect?: boolean;
  /** 是否使用网络质量监控 */
  useNetworkQualityMonitor?: boolean;
  /** 是否使用带宽控制 */
  useBandwidthController?: boolean;
}

/**
 * WebRTC连接管理器
 * 负责管理WebRTC连接的创建、维护和销毁
 */
export class WebRTCConnectionManager extends EventEmitter {
  /** 配置 */
  private config: Required<WebRTCConnectionManagerConfig>;
  
  /** WebRTC连接映射表 */
  private connections: Map<string, WebRTCConnection> = new Map();
  
  /** 重连尝试次数映射表 */
  private reconnectAttempts: Map<string, number> = new Map();
  
  /** 重连定时器ID映射表 */
  private reconnectTimers: Map<string, number> = new Map();
  
  /** 连接超时定时器ID映射表 */
  private connectionTimeoutTimers: Map<string, number> = new Map();
  
  /** 网络质量监控器 */
  private networkQualityMonitor: NetworkQualityMonitor | null = null;
  
  /** 带宽控制器 */
  private bandwidthController: BandwidthController | null = null;
  
  /** 本地用户ID */
  private localUserId: string | null = null;
  
  /**
   * 创建WebRTC连接管理器
   * @param config 配置
   */
  constructor(config: WebRTCConnectionManagerConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
      enableDataChannel: true,
      enableAudio: false,
      enableVideo: false,
      enableScreenShare: false,
      dataChannelConfig: {
        ordered: true,
      },
      audioConstraints: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
      videoConstraints: {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        frameRate: { ideal: 30 },
      },
      useCompression: true,
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      heartbeatInterval: 10000,
      connectionTimeout: 30000,
      autoReconnect: true,
      useNetworkQualityMonitor: true,
      useBandwidthController: true,
      ...config,
    };
    
    // 如果使用网络质量监控，则创建监控器
    if (this.config.useNetworkQualityMonitor) {
      this.networkQualityMonitor = new NetworkQualityMonitor();
    }
    
    // 如果使用带宽控制，则创建控制器
    if (this.config.useBandwidthController) {
      this.bandwidthController = new BandwidthController();
    }
  }
  
  /**
   * 初始化管理器
   * @param localUserId 本地用户ID
   */
  public initialize(localUserId: string): void {
    this.localUserId = localUserId;
    
    // 启动网络质量监控
    if (this.networkQualityMonitor) {
      this.networkQualityMonitor.startSampling();
    }
    
    // 启动带宽控制
    if (this.bandwidthController) {
      this.bandwidthController.startAutoAdjust();
    }
  }
  
  /**
   * 创建WebRTC连接
   * @param userId 远程用户ID
   * @param config 连接配置
   * @returns WebRTC连接
   */
  public createConnection(userId: string, config?: WebRTCConnectionConfig): WebRTCConnection {
    // 检查是否已存在连接
    if (this.connections.has(userId)) {
      return this.connections.get(userId)!;
    }
    
    // 合并配置
    const connectionConfig: WebRTCConnectionConfig = {
      enableDataChannel: this.config.enableDataChannel,
      enableAudio: this.config.enableAudio,
      enableVideo: this.config.enableVideo,
      enableScreenShare: this.config.enableScreenShare,
      dataChannelConfig: this.config.dataChannelConfig,
      audioConstraints: this.config.audioConstraints,
      videoConstraints: this.config.videoConstraints,
      useCompression: this.config.useCompression,
      ...config,
    };
    
    // 创建WebRTC连接
    const connection = new WebRTCConnection(userId, this.config.iceServers, connectionConfig);
    
    // 设置心跳间隔
    connection.setHeartbeatInterval(this.config.heartbeatInterval);
    
    // 设置事件监听器
    this.setupConnectionListeners(connection);
    
    // 添加到连接映射表
    this.connections.set(userId, connection);
    
    // 初始化重连尝试次数
    this.reconnectAttempts.set(userId, 0);
    
    // 创建对等连接
    connection.createConnection();
    
    // 设置连接超时定时器
    this.setConnectionTimeout(userId);
    
    // 触发连接创建事件
    this.emit('connectionCreated', userId, connection);
    
    return connection;
  }
  
  /**
   * 获取WebRTC连接
   * @param userId 远程用户ID
   * @returns WebRTC连接
   */
  public getConnection(userId: string): WebRTCConnection | undefined {
    return this.connections.get(userId);
  }
  
  /**
   * 关闭WebRTC连接
   * @param userId 远程用户ID
   * @returns 是否成功关闭
   */
  public closeConnection(userId: string): boolean {
    const connection = this.connections.get(userId);
    if (!connection) {
      return false;
    }
    
    // 清除重连定时器
    this.clearReconnectTimer(userId);
    
    // 清除连接超时定时器
    this.clearConnectionTimeout(userId);
    
    // 断开连接
    connection.disconnect();
    
    // 从连接映射表中移除
    this.connections.delete(userId);
    
    // 从重连尝试次数映射表中移除
    this.reconnectAttempts.delete(userId);
    
    // 触发连接关闭事件
    this.emit('connectionClosed', userId);
    
    return true;
  }
  
  /**
   * 关闭所有WebRTC连接
   */
  public closeAllConnections(): void {
    for (const userId of this.connections.keys()) {
      this.closeConnection(userId);
    }
  }
  
  /**
   * 设置连接事件监听器
   * @param connection WebRTC连接
   */
  private setupConnectionListeners(connection: WebRTCConnection): void {
    const userId = connection.getUserId();
    
    // 连接成功事件
    connection.on('connected', () => {
      // 清除连接超时定时器
      this.clearConnectionTimeout(userId);
      
      // 重置重连尝试次数
      this.reconnectAttempts.set(userId, 0);
      
      // 触发连接成功事件
      this.emit('connectionConnected', userId, connection);
    });
    
    // 连接断开事件
    connection.on('disconnected', () => {
      // 如果启用自动重连，则尝试重连
      if (this.config.autoReconnect) {
        this.attemptReconnect(userId);
      }
      
      // 触发连接断开事件
      this.emit('connectionDisconnected', userId, connection);
    });
    
    // 连接错误事件
    connection.on('error', (error) => {
      Debug.error('WebRTCConnectionManager', `Connection error for user ${userId}:`, error);
      
      // 如果启用自动重连，则尝试重连
      if (this.config.autoReconnect) {
        this.attemptReconnect(userId);
      }
      
      // 触发连接错误事件
      this.emit('connectionError', userId, error);
    });
    
    // 提议事件
    connection.on('offer', (offer) => {
      this.emit('offer', userId, offer);
    });
    
    // 应答事件
    connection.on('answer', (answer) => {
      this.emit('answer', userId, answer);
    });
    
    // ICE候选事件
    connection.on('iceCandidate', (candidate) => {
      this.emit('iceCandidate', userId, candidate);
    });
    
    // 数据通道打开事件
    connection.on('dataChannelOpen', () => {
      this.emit('dataChannelOpen', userId);
    });
    
    // 数据通道关闭事件
    connection.on('dataChannelClose', () => {
      this.emit('dataChannelClose', userId);
    });
    
    // 数据通道消息事件
    connection.on('dataChannelMessage', (message) => {
      this.emit('dataChannelMessage', userId, message);
    });
    
    // 媒体流添加事件
    connection.on('streamAdded', (stream) => {
      this.emit('streamAdded', userId, stream);
    });
    
    // 媒体流移除事件
    connection.on('streamRemoved', (stream) => {
      this.emit('streamRemoved', userId, stream);
    });
  }
  
  /**
   * 尝试重连
   * @param userId 远程用户ID
   */
  private attemptReconnect(userId: string): void {
    // 检查是否已达到最大重连次数
    const attempts = this.reconnectAttempts.get(userId) || 0;
    if (attempts >= this.config.maxReconnectAttempts) {
      Debug.warn('WebRTCConnectionManager', `Max reconnect attempts reached for user ${userId}`);
      
      // 关闭连接
      this.closeConnection(userId);
      
      // 触发重连失败事件
      this.emit('reconnectFailed', userId);
      
      return;
    }
    
    // 增加重连尝试次数
    this.reconnectAttempts.set(userId, attempts + 1);
    
    // 清除之前的重连定时器
    this.clearReconnectTimer(userId);
    
    // 设置重连定时器
    const timerId = window.setTimeout(() => {
      // 获取连接
      const connection = this.connections.get(userId);
      if (!connection) {
        return;
      }
      
      // 重新创建连接
      connection.createConnection();
      
      // 设置连接超时定时器
      this.setConnectionTimeout(userId);
      
      // 触发重连尝试事件
      this.emit('reconnectAttempt', userId, attempts + 1);
    }, this.config.reconnectInterval);
    
    // 保存定时器ID
    this.reconnectTimers.set(userId, timerId);
  }
  
  /**
   * 清除重连定时器
   * @param userId 远程用户ID
   */
  private clearReconnectTimer(userId: string): void {
    const timerId = this.reconnectTimers.get(userId);
    if (timerId !== undefined) {
      clearTimeout(timerId);
      this.reconnectTimers.delete(userId);
    }
  }
  
  /**
   * 设置连接超时定时器
   * @param userId 远程用户ID
   */
  private setConnectionTimeout(userId: string): void {
    // 清除之前的超时定时器
    this.clearConnectionTimeout(userId);
    
    // 设置超时定时器
    const timerId = window.setTimeout(() => {
      // 获取连接
      const connection = this.connections.get(userId);
      if (!connection) {
        return;
      }
      
      // 检查连接状态
      if (connection.getState() !== WebRTCConnectionState.CONNECTED) {
        Debug.warn('WebRTCConnectionManager', `Connection timeout for user ${userId}`);
        
        // 如果启用自动重连，则尝试重连
        if (this.config.autoReconnect) {
          this.attemptReconnect(userId);
        } else {
          // 否则关闭连接
          this.closeConnection(userId);
        }
        
        // 触发连接超时事件
        this.emit('connectionTimeout', userId);
      }
    }, this.config.connectionTimeout);
    
    // 保存定时器ID
    this.connectionTimeoutTimers.set(userId, timerId);
  }
  
  /**
   * 清除连接超时定时器
   * @param userId 远程用户ID
   */
  private clearConnectionTimeout(userId: string): void {
    const timerId = this.connectionTimeoutTimers.get(userId);
    if (timerId !== undefined) {
      clearTimeout(timerId);
      this.connectionTimeoutTimers.delete(userId);
    }
  }
  
  /**
   * 处理WebRTC提议
   * @param userId 远程用户ID
   * @param offer 提议
   */
  public handleOffer(userId: string, offer: RTCSessionDescriptionInit): void {
    // 获取连接
    let connection = this.connections.get(userId);
    
    // 如果连接不存在，则创建连接
    if (!connection) {
      connection = this.createConnection(userId);
    }
    
    // 处理提议
    connection.handleOffer(offer);
  }
  
  /**
   * 处理WebRTC应答
   * @param userId 远程用户ID
   * @param answer 应答
   */
  public handleAnswer(userId: string, answer: RTCSessionDescriptionInit): void {
    // 获取连接
    const connection = this.connections.get(userId);
    if (!connection) {
      Debug.warn('WebRTCConnectionManager', `No connection found for user ${userId}`);
      return;
    }
    
    // 处理应答
    connection.handleAnswer(answer);
  }
  
  /**
   * 处理WebRTC ICE候选
   * @param userId 远程用户ID
   * @param candidate ICE候选
   */
  public handleIceCandidate(userId: string, candidate: RTCIceCandidateInit): void {
    // 获取连接
    const connection = this.connections.get(userId);
    if (!connection) {
      Debug.warn('WebRTCConnectionManager', `No connection found for user ${userId}`);
      return;
    }
    
    // 处理ICE候选
    connection.handleIceCandidate(candidate);
  }
  
  /**
   * 发送消息到用户
   * @param userId 远程用户ID
   * @param message 消息
   * @returns 是否成功发送
   */
  public sendMessage(userId: string, message: any): boolean {
    // 获取连接
    const connection = this.connections.get(userId);
    if (!connection) {
      Debug.warn('WebRTCConnectionManager', `No connection found for user ${userId}`);
      return false;
    }
    
    // 发送消息
    connection.send(message);
    
    return true;
  }
  
  /**
   * 广播消息到所有用户
   * @param message 消息
   * @param excludeUserIds 排除的用户ID列表
   */
  public broadcastMessage(message: any, excludeUserIds: string[] = []): void {
    for (const [userId, connection] of this.connections.entries()) {
      // 跳过排除的用户
      if (excludeUserIds.includes(userId)) {
        continue;
      }
      
      // 发送消息
      connection.send(message);
    }
  }
  
  /**
   * 获取所有连接的用户ID
   * @returns 用户ID列表
   */
  public getConnectedUserIds(): string[] {
    const userIds: string[] = [];
    
    for (const [userId, connection] of this.connections.entries()) {
      if (connection.getState() === WebRTCConnectionState.CONNECTED) {
        userIds.push(userId);
      }
    }
    
    return userIds;
  }
  
  /**
   * 获取连接数量
   * @returns 连接数量
   */
  public getConnectionCount(): number {
    return this.connections.size;
  }
  
  /**
   * 获取已连接的连接数量
   * @returns 已连接的连接数量
   */
  public getConnectedCount(): number {
    let count = 0;
    
    for (const connection of this.connections.values()) {
      if (connection.getState() === WebRTCConnectionState.CONNECTED) {
        count++;
      }
    }
    
    return count;
  }
  
  /**
   * 销毁管理器
   */
  public dispose(): void {
    // 关闭所有连接
    this.closeAllConnections();
    
    // 销毁网络质量监控器
    if (this.networkQualityMonitor) {
      (this.networkQualityMonitor as any).dispose();
      this.networkQualityMonitor = null;
    }
    
    // 销毁带宽控制器
    if (this.bandwidthController) {
      (this.bandwidthController as any).dispose();
      this.bandwidthController = null;
    }
    
    // 移除所有监听器
    this.removeAllListeners();
  }
}
