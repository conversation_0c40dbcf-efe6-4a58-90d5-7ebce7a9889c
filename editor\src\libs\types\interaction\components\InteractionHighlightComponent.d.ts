/**
 * InteractionHighlightComponent.ts
 *
 * 交互高亮组件，用于高亮可交互对象
 */
import { Component } from '../../core/Component';
import { Color } from 'three';
/**
 * 高亮类型枚举
 */
export declare enum HighlightType {
    /** 轮廓高亮 */
    OUTLINE = "outline",
    /** 发光高亮 */
    GLOW = "glow",
    /** 颜色高亮 */
    COLOR = "color",
    /** 自定义高亮 */
    CUSTOM = "custom"
}
/**
 * 交互高亮组件配置
 */
export interface InteractionHighlightComponentConfig {
    /** 高亮类型 */
    highlightType?: HighlightType;
    /** 高亮颜色 */
    highlightColor?: Color | string;
    /** 高亮强度 */
    highlightIntensity?: number;
    /** 高亮宽度（轮廓高亮） */
    outlineWidth?: number;
    /** 高亮脉冲（是否呼吸效果） */
    pulse?: boolean;
    /** 脉冲速度 */
    pulseSpeed?: number;
    /** 是否启用 */
    enabled?: boolean;
    /** 是否高亮 */
    highlighted?: boolean;
}
/**
 * 交互高亮组件
 * 用于高亮可交互对象
 */
export declare class InteractionHighlightComponent extends Component {
    /** 高亮类型 */
    private _highlightType;
    /** 高亮颜色 */
    private _highlightColor;
    /** 高亮强度 */
    private _highlightIntensity;
    /** 高亮宽度（轮廓高亮） */
    private _outlineWidth;
    /** 高亮脉冲（是否呼吸效果） */
    private _pulse;
    /** 脉冲速度 */
    private _pulseSpeed;
    /** 是否高亮 */
    private _highlighted;
    /** 原始材质映射 */
    private originalMaterials;
    /** 高亮材质映射 */
    private highlightMaterials;
    /** 脉冲计时器 */
    private pulseTime;
    /** 当前脉冲值（0-1） */
    private pulseValue;
    /**
     * 构造函数
     * @param config 组件配置
     */
    constructor(config?: InteractionHighlightComponentConfig);
    /**
     * 获取高亮类型
     */
    get highlightType(): HighlightType;
    /**
     * 设置高亮类型
     */
    set highlightType(value: HighlightType);
    /**
     * 获取高亮颜色
     */
    get highlightColor(): Color;
    /**
     * 设置高亮颜色
     */
    set highlightColor(value: Color | string);
    /**
     * 获取高亮强度
     */
    get highlightIntensity(): number;
    /**
     * 设置高亮强度
     */
    set highlightIntensity(value: number);
    /**
     * 设置是否启用
     */
    setEnabled(value: boolean): void;
    /**
     * 获取是否高亮
     */
    get highlighted(): boolean;
    /**
     * 设置是否高亮
     */
    set highlighted(value: boolean);
    /**
     * 应用高亮
     */
    private applyHighlight;
    /**
     * 应用轮廓高亮
     * @param object3D 3D对象
     */
    private applyOutlineHighlight;
    /**
     * 应用发光高亮
     * @param object3D 3D对象
     */
    private applyGlowHighlight;
    /**
     * 应用颜色高亮
     * @param object3D 3D对象
     */
    private applyColorHighlight;
    /**
     * 应用自定义高亮
     * @param object3D 3D对象
     */
    private applyCustomHighlight;
    /**
     * 移除高亮
     */
    private removeHighlight;
    /**
     * 更新高亮材质
     */
    private updateHighlightMaterials;
    /**
     * 获取对象的3D表示
     * @returns 3D对象
     */
    private getObject3D;
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁组件
     */
    dispose(): void;
}
