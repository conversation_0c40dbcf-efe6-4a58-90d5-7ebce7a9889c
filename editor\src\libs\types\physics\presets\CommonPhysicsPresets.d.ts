import { PhysicsPreset } from './PhysicsPreset';
/**
 * 常用物理预设
 */
export declare class CommonPhysicsPresets {
    /**
     * 获取所有常用预设
     * @returns 常用预设数组
     */
    static getAllPresets(): PhysicsPreset[];
    /**
     * 获取世界预设
     * @returns 世界预设数组
     */
    static getWorldPresets(): PhysicsPreset[];
    /**
     * 获取物理体预设
     * @returns 物理体预设数组
     */
    static getBodyPresets(): PhysicsPreset[];
    /**
     * 获取车辆预设
     * @returns 车辆预设数组
     */
    static getVehiclePresets(): PhysicsPreset[];
    /**
     * 获取角色预设
     * @returns 角色预设数组
     */
    static getCharacterPresets(): PhysicsPreset[];
    /**
     * 获取布娃娃预设
     * @returns 布娃娃预设数组
     */
    static getRagdollPresets(): PhysicsPreset[];
    /**
     * 获取软体预设
     * @returns 软体预设数组
     */
    static getSoftBodyPresets(): PhysicsPreset[];
}
