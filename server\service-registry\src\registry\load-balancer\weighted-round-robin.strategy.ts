/**
 * 加权轮询负载均衡策略
 */
import { Injectable } from '@nestjs/common';
import { BaseLoadBalancerStrategy } from './base-load-balancer.strategy';
import { LoadBalancerContext, LoadBalancerAlgorithm } from './load-balancer.interface';
import { ServiceInstanceEntity } from '../entities/service-instance.entity';

interface WeightedInstance {
  instance: ServiceInstanceEntity;
  currentWeight: number;
  effectiveWeight: number;
}

@Injectable()
export class WeightedRoundRobinLoadBalancerStrategy extends BaseLoadBalancerStrategy {
  private weightedInstances = new Map<string, WeightedInstance[]>();
  
  constructor() {
    super(LoadBalancerAlgorithm.WEIGHTED_ROUND_ROBIN);
    this.config.algorithm = LoadBalancerAlgorithm.WEIGHTED_ROUND_ROBIN;
  }
  
  /**
   * 加权轮询选择服务实例
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  protected async doSelect(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): Promise<ServiceInstanceEntity | null> {
    if (!instances || instances.length === 0) {
      return null;
    }
    
    // 获取或初始化加权实例列表
    const serviceKey = context.serviceName;
    let weightedInstances = this.weightedInstances.get(serviceKey);
    
    // 如果不存在或实例列表已变化，则重新初始化
    if (!weightedInstances || this.isInstanceListChanged(weightedInstances, instances)) {
      weightedInstances = this.initWeightedInstances(instances);
      this.weightedInstances.set(serviceKey, weightedInstances);
    }
    
    // 使用平滑加权轮询算法选择实例
    return this.smoothWeightedRoundRobin(weightedInstances);
  }
  
  /**
   * 初始化加权实例列表
   * @param instances 服务实例列表
   */
  private initWeightedInstances(instances: ServiceInstanceEntity[]): WeightedInstance[] {
    return instances.map(instance => ({
      instance,
      currentWeight: 0,
      effectiveWeight: instance.weight || 100, // 默认权重为100
    }));
  }
  
  /**
   * 检查实例列表是否已变化
   * @param weightedInstances 加权实例列表
   * @param instances 服务实例列表
   */
  private isInstanceListChanged(
    weightedInstances: WeightedInstance[],
    instances: ServiceInstanceEntity[],
  ): boolean {
    if (weightedInstances.length !== instances.length) {
      return true;
    }
    
    // 检查实例ID是否匹配
    const weightedInstanceIds = new Set(
      weightedInstances.map(wi => wi.instance.id)
    );
    
    for (const instance of instances) {
      if (!weightedInstanceIds.has(instance.id)) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * 平滑加权轮询算法
   * @param weightedInstances 加权实例列表
   */
  private smoothWeightedRoundRobin(
    weightedInstances: WeightedInstance[],
  ): ServiceInstanceEntity | null {
    // 计算总权重
    let totalWeight = 0;
    
    // 更新当前权重和有效权重
    for (const wi of weightedInstances) {
      // 累加当前权重和有效权重
      wi.currentWeight += wi.effectiveWeight;
      totalWeight += wi.effectiveWeight;
    }
    
    // 如果总权重为0，则随机选择
    if (totalWeight <= 0) {
      const index = Math.floor(Math.random() * weightedInstances.length);
      return weightedInstances[index].instance;
    }
    
    // 选择当前权重最大的实例
    let selected = weightedInstances[0];
    for (let i = 1; i < weightedInstances.length; i++) {
      if (weightedInstances[i].currentWeight > selected.currentWeight) {
        selected = weightedInstances[i];
      }
    }
    
    // 减去总权重
    selected.currentWeight -= totalWeight;
    
    return selected.instance;
  }
  
  /**
   * 重置策略状态
   */
  override reset(): void {
    this.weightedInstances.clear();
  }
}
