import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { AlertService } from './alert.service';
import { AlertRuleService } from './alert-rule.service';
import { AlertStatus, AlertSeverity } from './entities/alert.types';

/**
 * 告警控制器
 * 提供告警管理的API接口
 */
@Controller('alerts')
export class AlertController {
  constructor(
    private readonly alertService: AlertService,
    private readonly alertRuleService: AlertRuleService,
  ) {}

  /**
   * 获取所有告警
   */
  @Get()
  async getAllAlerts(
    @Query('status') status?: AlertStatus,
    @Query('severity') severity?: AlertSeverity,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return await this.alertService.getAllAlerts({
      status,
      severity,
      page: page || 1,
      limit: limit || 20,
    });
  }

  /**
   * 获取特定告警
   */
  @Get(':id')
  async getAlert(@Param('id') id: string) {
    return await this.alertService.getAlert(id);
  }

  /**
   * 确认告警
   */
  @Put(':id/acknowledge')
  async acknowledgeAlert(@Param('id') id: string, @Body() body: { userId: string; comment?: string }) {
    return await this.alertService.acknowledgeAlert(id, body.userId);
  }

  /**
   * 解决告警
   */
  @Put(':id/resolve')
  async resolveAlert(@Param('id') id: string, @Body() body: { userId: string; comment?: string }) {
    return await this.alertService.resolveAlert(id, body.userId);
  }

  /**
   * 获取所有告警规则
   */
  @Get('rules')
  async getAllAlertRules() {
    return await this.alertRuleService.getAllRules();
  }

  /**
   * 创建告警规则
   */
  @Post('rules')
  async createAlertRule(@Body() ruleData: any) {
    return await this.alertRuleService.createRule(ruleData);
  }

  /**
   * 更新告警规则
   */
  @Put('rules/:id')
  async updateAlertRule(@Param('id') id: string, @Body() ruleData: any) {
    return await this.alertRuleService.updateRule(id, ruleData);
  }

  /**
   * 删除告警规则
   */
  @Delete('rules/:id')
  async deleteAlertRule(@Param('id') id: string) {
    return await this.alertRuleService.deleteRule(id);
  }

  /**
   * 启用/禁用告警规则
   */
  @Put('rules/:id/toggle')
  async toggleAlertRule(@Param('id') id: string, @Body() body: { enabled: boolean }) {
    return await this.alertRuleService.toggleRule(id, body.enabled);
  }
}
