/**
 * 权限管理器
 * 负责管理用户权限和访问控制
 */
export class PermissionManager {
  /**
   * 构造函数
   */
  constructor() {
    // 权限角色定义
    this.Roles = {
      OWNER: 'owner',
      ADMIN: 'admin',
      EDITOR: 'editor',
      VIEWER: 'viewer'
    };
    
    // 权限定义
    this.Permissions = {
      READ: 'read',
      WRITE: 'write',
      DELETE: 'delete',
      MANAGE_USERS: 'manage_users',
      MANAGE_PERMISSIONS: 'manage_permissions'
    };
    
    // 角色权限映射
    this.RolePermissions = {
      [this.Roles.OWNER]: [
        this.Permissions.READ,
        this.Permissions.WRITE,
        this.Permissions.DELETE,
        this.Permissions.MANAGE_USERS,
        this.Permissions.MANAGE_PERMISSIONS
      ],
      [this.Roles.ADMIN]: [
        this.Permissions.READ,
        this.Permissions.WRITE,
        this.Permissions.DELETE,
        this.Permissions.MANAGE_USERS
      ],
      [this.Roles.EDITOR]: [
        this.Permissions.READ,
        this.Permissions.WRITE
      ],
      [this.Roles.VIEWER]: [
        this.Permissions.READ
      ]
    };
    
    // 操作权限映射
    this.OperationPermissions = {
      'create': this.Permissions.WRITE,
      'delete': this.Permissions.DELETE,
      'transform': this.Permissions.WRITE,
      'property': this.Permissions.WRITE
    };
    
    // 实体权限覆盖
    this.entityPermissionOverrides = new Map();
    
    // 初始化权限面板UI
    this.initializePermissionPanelUI();
  }
  
  /**
   * 检查用户是否有权限
   * @param {Object} user 用户对象
   * @param {string} permission 权限
   * @returns {boolean} 是否有权限
   */
  hasPermission(user, permission) {
    if (!user || !user.role) {
      return false;
    }
    
    const role = user.role;
    return this.RolePermissions[role].includes(permission);
  }
  
  /**
   * 检查用户是否可以执行操作
   * @param {Object} user 用户对象
   * @param {Object} operation 操作对象
   * @returns {boolean} 是否可以执行操作
   */
  canPerformOperation(user, operation) {
    if (!user || !operation) {
      return false;
    }
    
    const operationType = operation.type;
    const requiredPermission = this.OperationPermissions[operationType];
    
    if (!requiredPermission) {
      return false;
    }
    
    // 检查实体特定权限覆盖
    if (operation.data && operation.data.entityId) {
      const entityId = operation.data.entityId;
      const entityPermissions = this.entityPermissionOverrides.get(entityId);
      
      if (entityPermissions && entityPermissions[user.id]) {
        const userEntityPermissions = entityPermissions[user.id];
        
        // 如果有明确的允许或拒绝，优先使用
        if (userEntityPermissions.includes(requiredPermission)) {
          return true;
        } else if (userEntityPermissions.includes(`!${requiredPermission}`)) {
          return false;
        }
      }
    }
    
    // 使用角色权限
    return this.hasPermission(user, requiredPermission);
  }
  
  /**
   * 设置用户角色
   * @param {string} userId 用户ID
   * @param {string} role 角色
   * @param {Object} currentUser 当前用户
   * @returns {boolean} 是否设置成功
   */
  setUserRole(userId, role, currentUser) {
    // 检查当前用户是否有权限管理用户
    if (!this.hasPermission(currentUser, this.Permissions.MANAGE_PERMISSIONS)) {
      console.warn('没有权限管理用户角色');
      return false;
    }
    
    // 在实际应用中，这里应该调用API更新用户角色
    console.log(`设置用户 ${userId} 的角色为 ${role}`);
    
    // 更新权限面板UI
    this.updatePermissionPanelUI();
    
    return true;
  }
  
  /**
   * 设置实体权限覆盖
   * @param {string} entityId 实体ID
   * @param {string} userId 用户ID
   * @param {Array} permissions 权限数组
   * @param {Object} currentUser 当前用户
   * @returns {boolean} 是否设置成功
   */
  setEntityPermissionOverride(entityId, userId, permissions, currentUser) {
    // 检查当前用户是否有权限管理权限
    if (!this.hasPermission(currentUser, this.Permissions.MANAGE_PERMISSIONS)) {
      console.warn('没有权限管理实体权限');
      return false;
    }
    
    // 获取或创建实体权限映射
    if (!this.entityPermissionOverrides.has(entityId)) {
      this.entityPermissionOverrides.set(entityId, {});
    }
    
    const entityPermissions = this.entityPermissionOverrides.get(entityId);
    entityPermissions[userId] = permissions;
    
    // 更新权限面板UI
    this.updatePermissionPanelUI();
    
    return true;
  }
  
  /**
   * 清除实体权限覆盖
   * @param {string} entityId 实体ID
   * @param {string} userId 用户ID
   * @param {Object} currentUser 当前用户
   * @returns {boolean} 是否清除成功
   */
  clearEntityPermissionOverride(entityId, userId, currentUser) {
    // 检查当前用户是否有权限管理权限
    if (!this.hasPermission(currentUser, this.Permissions.MANAGE_PERMISSIONS)) {
      console.warn('没有权限管理实体权限');
      return false;
    }
    
    // 检查是否存在实体权限映射
    if (!this.entityPermissionOverrides.has(entityId)) {
      return true;
    }
    
    const entityPermissions = this.entityPermissionOverrides.get(entityId);
    
    if (entityPermissions[userId]) {
      delete entityPermissions[userId];
    }
    
    // 如果实体权限映射为空，删除它
    if (Object.keys(entityPermissions).length === 0) {
      this.entityPermissionOverrides.delete(entityId);
    }
    
    // 更新权限面板UI
    this.updatePermissionPanelUI();
    
    return true;
  }
  
  /**
   * 获取角色名称
   * @param {string} role 角色代码
   * @returns {string} 角色名称
   */
  getRoleName(role) {
    switch (role) {
      case this.Roles.OWNER:
        return '所有者';
      case this.Roles.ADMIN:
        return '管理员';
      case this.Roles.EDITOR:
        return '编辑者';
      case this.Roles.VIEWER:
        return '查看者';
      default:
        return role;
    }
  }
  
  /**
   * 获取权限名称
   * @param {string} permission 权限代码
   * @returns {string} 权限名称
   */
  getPermissionName(permission) {
    switch (permission) {
      case this.Permissions.READ:
        return '读取';
      case this.Permissions.WRITE:
        return '写入';
      case this.Permissions.DELETE:
        return '删除';
      case this.Permissions.MANAGE_USERS:
        return '管理用户';
      case this.Permissions.MANAGE_PERMISSIONS:
        return '管理权限';
      default:
        return permission;
    }
  }
  
  /**
   * 初始化权限面板UI
   */
  initializePermissionPanelUI() {
    const permissionsPanel = document.getElementById('permissions-panel');
    if (permissionsPanel) {
      permissionsPanel.innerHTML = `
        <h3>权限管理</h3>
        <div class="permissions-content">
          <p>选择用户以管理权限</p>
        </div>
      `;
    }
  }
  
  /**
   * 更新权限面板UI
   */
  updatePermissionPanelUI() {
    const permissionsPanel = document.getElementById('permissions-panel');
    if (!permissionsPanel) {
      return;
    }
    
    // 在实际应用中，这里应该根据当前选择的用户和实体更新权限面板
    // 这里使用模拟数据
    
    const selectedUser = {
      id: 'user2',
      username: '张三',
      role: 'editor'
    };
    
    const selectedEntity = {
      id: 'entity1',
      name: '立方体'
    };
    
    let html = `
      <h3>权限管理</h3>
      <div class="permissions-content">
    `;
    
    if (selectedUser) {
      html += `
        <div class="permission-section">
          <h4>用户角色</h4>
          <div class="user-role">
            <span class="user-name">${selectedUser.username}</span>
            <select class="role-select">
      `;
      
      // 添加角色选项
      for (const role in this.Roles) {
        const selected = this.Roles[role] === selectedUser.role ? 'selected' : '';
        html += `<option value="${this.Roles[role]}" ${selected}>${this.getRoleName(this.Roles[role])}</option>`;
      }
      
      html += `
            </select>
          </div>
        </div>
      `;
    }
    
    if (selectedEntity) {
      html += `
        <div class="permission-section">
          <h4>实体权限</h4>
          <div class="entity-permissions">
            <div class="entity-name">${selectedEntity.name}</div>
            <div class="permission-checkboxes">
      `;
      
      // 添加权限复选框
      for (const permission in this.Permissions) {
        const checked = this.hasPermission(selectedUser, this.Permissions[permission]) ? 'checked' : '';
        html += `
          <div class="permission-checkbox">
            <input type="checkbox" id="perm_${this.Permissions[permission]}" ${checked}>
            <label for="perm_${this.Permissions[permission]}">${this.getPermissionName(this.Permissions[permission])}</label>
          </div>
        `;
      }
      
      html += `
            </div>
          </div>
        </div>
      `;
    }
    
    html += `
      </div>
    `;
    
    permissionsPanel.innerHTML = html;
  }
}
