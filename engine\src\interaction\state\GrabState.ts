/**
 * 抓取状态
 * 用于管理抓取系统的状态
 */
import { EventEmitter } from '../../utils/EventEmitter';
import type { Entity } from '../../core/Entity';
import { Hand } from '../components/GrabbableComponent';

/**
 * 抓取事件类型
 */
export enum GrabEventType {
  /** 抓取开始 */
  GRAB_START = 'grabStart',
  /** 抓取结束 */
  GRAB_END = 'grabEnd',
  /** 抓取更新 */
  GRAB_UPDATE = 'grabUpdate',
  /** 抓取状态变化 */
  STATE_CHANGE = 'stateChange'
}

/**
 * 抓取事件数据
 */
export interface GrabEventData {
  /** 抓取者 */
  grabber: Entity;
  /** 被抓取实体 */
  grabbed: Entity;
  /** 抓取手 */
  hand: Hand;
  /** 时间戳 */
  timestamp: number;
  /** 其他数据 */
  [key: string]: any;
}

/**
 * 抓取状态
 */
export class GrabState {
  /** 单例实例 */
  private static instance: GrabState;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 当前被抓取的实体映射 - 实体ID到抓取数据 */
  private grabbedEntities: Map<string, {
    entity: Entity;
    grabber: Entity;
    hand: Hand;
    timestamp: number;
  }> = new Map();

  /** 当前抓取者映射 - 实体ID到被抓取实体 */
  private grabbers: Map<string, {
    entity: Entity;
    leftGrabbed?: Entity;
    rightGrabbed?: Entity;
  }> = new Map();

  /**
   * 私有构造函数
   */
  private constructor() {
    // 私有构造函数，防止直接实例化
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): GrabState {
    if (!GrabState.instance) {
      GrabState.instance = new GrabState();
    }
    return GrabState.instance;
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器函数
   */
  public addEventListener(event: GrabEventType, listener: (data: GrabEventData) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器函数
   */
  public removeEventListener(event: GrabEventType, listener?: (data: GrabEventData) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 触发事件
   * @param event 事件类型
   * @param data 事件数据
   */
  private dispatchEvent(event: GrabEventType, data: GrabEventData): void {
    this.eventEmitter.emit(event, data);
  }

  /**
   * 注册抓取
   * @param grabbed 被抓取实体
   * @param grabber 抓取者
   * @param hand 抓取手
   */
  public registerGrab(grabbed: Entity, grabber: Entity, hand: Hand): void {
    // 创建抓取数据
    const grabData = {
      entity: grabbed,
      grabber: grabber,
      hand: hand,
      timestamp: Date.now()
    };

    // 添加到被抓取实体映射
    this.grabbedEntities.set(grabbed.id, grabData);

    // 更新抓取者映射
    let grabberData = this.grabbers.get(grabber.id);
    if (!grabberData) {
      grabberData = {
        entity: grabber
      };
      this.grabbers.set(grabber.id, grabberData);
    }

    if (hand === Hand.LEFT) {
      grabberData.leftGrabbed = grabbed;
    } else if (hand === Hand.RIGHT) {
      grabberData.rightGrabbed = grabbed;
    }

    // 触发事件
    this.dispatchEvent(GrabEventType.GRAB_START, {
      grabber: grabber,
      grabbed: grabbed,
      hand: hand,
      timestamp: grabData.timestamp
    });

    // 触发状态变化事件
    this.dispatchEvent(GrabEventType.STATE_CHANGE, {
      grabber: grabber,
      grabbed: grabbed,
      hand: hand,
      timestamp: grabData.timestamp,
      type: 'register'
    });
  }

  /**
   * 注销抓取
   * @param grabbed 被抓取实体
   */
  public unregisterGrab(grabbed: Entity): void {
    // 获取抓取数据
    const grabData = this.grabbedEntities.get(grabbed.id);
    if (!grabData) return;

    // 从被抓取实体映射中移除
    this.grabbedEntities.delete(grabbed.id);

    // 更新抓取者映射
    const grabber = grabData.grabber;
    const hand = grabData.hand;
    const grabberData = this.grabbers.get(grabber.id);
    if (grabberData) {
      if (hand === Hand.LEFT) {
        grabberData.leftGrabbed = undefined;
      } else if (hand === Hand.RIGHT) {
        grabberData.rightGrabbed = undefined;
      }

      // 如果抓取者没有抓取任何实体，则从映射中移除
      if (!grabberData.leftGrabbed && !grabberData.rightGrabbed) {
        this.grabbers.delete(grabber.id);
      }
    }

    // 触发事件
    this.dispatchEvent(GrabEventType.GRAB_END, {
      grabber: grabber,
      grabbed: grabbed,
      hand: hand,
      timestamp: Date.now(),
      grabDuration: Date.now() - grabData.timestamp
    });

    // 触发状态变化事件
    this.dispatchEvent(GrabEventType.STATE_CHANGE, {
      grabber: grabber,
      grabbed: grabbed,
      hand: hand,
      timestamp: Date.now(),
      type: 'unregister'
    });
  }

  /**
   * 更新抓取
   * @param grabbed 被抓取实体
   * @param data 更新数据
   */
  public updateGrab(grabbed: Entity, data: any = {}): void {
    // 获取抓取数据
    const grabData = this.grabbedEntities.get(grabbed.id);
    if (!grabData) return;

    // 触发更新事件
    this.dispatchEvent(GrabEventType.GRAB_UPDATE, {
      grabber: grabData.grabber,
      grabbed: grabbed,
      hand: grabData.hand,
      timestamp: Date.now(),
      grabDuration: Date.now() - grabData.timestamp,
      ...data
    });
  }

  /**
   * 检查实体是否被抓取
   * @param entity 实体
   * @returns 是否被抓取
   */
  public isGrabbed(entity: Entity): boolean {
    return this.grabbedEntities.has(entity.id);
  }

  /**
   * 获取抓取者
   * @param grabbed 被抓取实体
   * @returns 抓取者
   */
  public getGrabber(grabbed: Entity): Entity | undefined {
    const grabData = this.grabbedEntities.get(grabbed.id);
    return grabData ? grabData.grabber : undefined;
  }

  /**
   * 获取抓取手
   * @param grabbed 被抓取实体
   * @returns 抓取手
   */
  public getGrabHand(grabbed: Entity): Hand | undefined {
    const grabData = this.grabbedEntities.get(grabbed.id);
    return grabData ? grabData.hand : undefined;
  }

  /**
   * 获取被抓取实体
   * @param grabber 抓取者
   * @param hand 抓取手
   * @returns 被抓取实体
   */
  public getGrabbedEntity(grabber: Entity, hand: Hand): Entity | undefined {
    const grabberData = this.grabbers.get(grabber.id);
    if (!grabberData) return undefined;

    if (hand === Hand.LEFT) {
      return grabberData.leftGrabbed;
    } else if (hand === Hand.RIGHT) {
      return grabberData.rightGrabbed;
    }

    return undefined;
  }

  /**
   * 获取所有被抓取的实体
   * @returns 被抓取的实体数组
   */
  public getAllGrabbedEntities(): Entity[] {
    return Array.from(this.grabbedEntities.values()).map(data => data.entity);
  }

  /**
   * 获取所有抓取者
   * @returns 抓取者数组
   */
  public getAllGrabbers(): Entity[] {
    return Array.from(this.grabbers.values()).map(data => data.entity);
  }

  /**
   * 清除所有抓取状态
   */
  public clear(): void {
    // 保存当前状态的副本，用于触发事件
    const currentGrabs = Array.from(this.grabbedEntities.values());

    // 清除映射
    this.grabbedEntities.clear();
    this.grabbers.clear();

    // 触发事件
    for (const grabData of currentGrabs) {
      this.dispatchEvent(GrabEventType.GRAB_END, {
        grabber: grabData.grabber,
        grabbed: grabData.entity,
        hand: grabData.hand,
        timestamp: Date.now(),
        grabDuration: Date.now() - grabData.timestamp,
        reason: 'clear'
      });
    }

    // 触发状态变化事件
    this.dispatchEvent(GrabEventType.STATE_CHANGE, {
      grabber: undefined as any,
      grabbed: undefined as any,
      hand: undefined as any,
      timestamp: Date.now(),
      type: 'clear'
    });
  }
}
