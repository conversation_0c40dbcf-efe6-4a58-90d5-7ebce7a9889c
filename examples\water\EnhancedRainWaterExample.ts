/**
 * 增强版雨水示例
 * 展示如何使用增强版雨水组件创建各种雨水效果
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { Camera } from '../../engine/src/core/Camera';
import { OrbitControls } from '../../engine/src/controls/OrbitControls';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { AudioSystem } from '../../engine/src/audio/AudioSystem';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../engine/src/physics/water/WaterInteractionSystem';
import { UnderwaterParticleSystem } from '../../engine/src/rendering/water/UnderwaterParticleSystem';
import { WaterInstancedRenderer } from '../../engine/src/rendering/water/WaterInstancedRenderer';
import { WaterWeatherSystem } from '../../engine/src/physics/water/WaterWeatherSystem';
import { RainWaterType } from '../../engine/src/physics/water/RainWaterComponent';
import { EnhancedRainWaterComponent, EnhancedRainWaterType, RaindropShape } from '../../engine/src/physics/water/EnhancedRainWaterComponent';
import { EnhancedRainWaterPresets } from '../../engine/src/physics/water/EnhancedRainWaterPresets';
import { Debug } from '../../engine/src/utils/Debug';
import { PerformanceMonitor } from '../../engine/src/utils/PerformanceMonitor';

/**
 * 增强版雨水示例
 */
export class EnhancedRainWaterExample {
  /** 世界 */
  private world: World;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水体交互系统 */
  private waterInteractionSystem: WaterInteractionSystem;
  /** 水下粒子系统 */
  private underwaterParticleSystem: UnderwaterParticleSystem;
  /** 水体实例化渲染器 */
  private waterInstancedRenderer: WaterInstancedRenderer;
  /** 水体天气系统 */
  private waterWeatherSystem: WaterWeatherSystem;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 音频系统 */
  private audioSystem: AudioSystem;
  /** 雨水实体 */
  private rainWaterEntity: Entity;
  /** 雨水组件 */
  private rainWaterComponent: EnhancedRainWaterComponent;
  /** 当前雨水类型 */
  private currentRainWaterType: EnhancedRainWaterType = EnhancedRainWaterType.MEDIUM;
  /** 雨水类型列表 */
  private rainWaterTypes: EnhancedRainWaterType[] = [
    EnhancedRainWaterType.LIGHT,
    EnhancedRainWaterType.MEDIUM,
    EnhancedRainWaterType.HEAVY,
    EnhancedRainWaterType.THUNDERSTORM,
    EnhancedRainWaterType.MONSOON,
    EnhancedRainWaterType.SPRING_RAIN,
    EnhancedRainWaterType.SUMMER_RAIN,
    EnhancedRainWaterType.AUTUMN_RAIN,
    EnhancedRainWaterType.WINTER_RAIN,
    EnhancedRainWaterType.SLEET,
    EnhancedRainWaterType.ACID_RAIN,
    EnhancedRainWaterType.TROPICAL_RAIN
  ];
  /** 当前雨水类型索引 */
  private currentRainWaterTypeIndex: number = 1;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();
  /** 风力方向 */
  private windDirection: THREE.Vector2 = new THREE.Vector2(1, 0);
  /** 风力强度 */
  private windStrength: number = 1.0;
  /** 风力方向变化计时器 */
  private windDirectionChangeTimer: number = 0;
  /** 风力方向变化间隔 */
  private windDirectionChangeInterval: number = 10.0;
  /** 地面实体 */
  private groundEntity: Entity;
  /** 相机 */
  private camera: Camera;
  /** 轨道控制器 */
  private controls: OrbitControls;
  /** 是否显示UI */
  private showUI: boolean = true;
  /** UI元素 */
  private uiElements: HTMLElement[] = [];

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 初始化系统
    this.initializeSystems();

    // 创建场景
    this.createScene();

    // 创建雨水
    this.createRainWater();

    // 创建UI
    this.createUI();

    // 开始渲染循环
    this.startRenderLoop();

    Debug.log('EnhancedRainWaterExample', '增强版雨水示例初始化完成');
  }

  /**
   * 初始化系统
   */
  private initializeSystems(): void {
    // 创建相机
    this.camera = new Camera();
    this.camera.position.set(0, 10, 20);
    this.camera.lookAt(0, 0, 0);
    this.world.setCamera(this.camera);

    // 创建轨道控制器
    this.controls = new OrbitControls(this.camera.getThreeCamera(), this.world.getRenderer().domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.screenSpacePanning = false;
    this.controls.minDistance = 5;
    this.controls.maxDistance = 50;
    this.controls.maxPolarAngle = Math.PI / 2 - 0.1;

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world);
    this.world.addSystem(this.inputSystem);

    // 创建音频系统
    this.audioSystem = new AudioSystem(this.world);
    this.world.addSystem(this.audioSystem);

    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水体交互系统
    this.waterInteractionSystem = new WaterInteractionSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true
    });
    this.world.addSystem(this.waterInteractionSystem);

    // 创建水下粒子系统
    this.underwaterParticleSystem = new UnderwaterParticleSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1
    });
    this.world.addSystem(this.underwaterParticleSystem);

    // 创建水体实例化渲染器
    this.waterInstancedRenderer = new WaterInstancedRenderer(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      maxInstances: 10000,
      useGPUCulling: true,
      useInstancedShadows: true
    });
    this.world.addSystem(this.waterInstancedRenderer);

    // 创建水体天气系统
    this.waterWeatherSystem = new WaterWeatherSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1
    });
    this.world.addSystem(this.waterWeatherSystem);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 1);
    this.world.getScene().add(ambientLight);

    // 创建方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(10, 20, 10);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -20;
    directionalLight.shadow.camera.right = 20;
    directionalLight.shadow.camera.top = 20;
    directionalLight.shadow.camera.bottom = -20;
    this.world.getScene().add(directionalLight);

    // 创建地面
    this.groundEntity = new Entity();
    this.groundEntity.name = 'Ground';
    const groundGeometry = new THREE.PlaneGeometry(100, 100);
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0x555555,
      roughness: 0.8,
      metalness: 0.2,
      side: THREE.DoubleSide
    });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.rotation.x = -Math.PI / 2;
    groundMesh.receiveShadow = true;
    this.groundEntity.addObject(groundMesh);
    this.world.addEntity(this.groundEntity);

    // 创建一些物体
    this.createObjects();
  }

  /**
   * 创建一些物体
   */
  private createObjects(): void {
    // 创建一些盒子
    for (let i = 0; i < 10; i++) {
      const boxEntity = new Entity();
      boxEntity.name = `Box_${i}`;
      const boxGeometry = new THREE.BoxGeometry(1, 1, 1);
      const boxMaterial = new THREE.MeshStandardMaterial({
        color: Math.random() * 0xffffff,
        roughness: 0.7,
        metalness: 0.3
      });
      const boxMesh = new THREE.Mesh(boxGeometry, boxMaterial);
      boxMesh.position.set(
        (Math.random() - 0.5) * 20,
        0.5,
        (Math.random() - 0.5) * 20
      );
      boxMesh.castShadow = true;
      boxMesh.receiveShadow = true;
      boxEntity.addObject(boxMesh);
      this.world.addEntity(boxEntity);
    }

    // 创建一些球体
    for (let i = 0; i < 5; i++) {
      const sphereEntity = new Entity();
      sphereEntity.name = `Sphere_${i}`;
      const sphereGeometry = new THREE.SphereGeometry(0.7, 32, 32);
      const sphereMaterial = new THREE.MeshStandardMaterial({
        color: Math.random() * 0xffffff,
        roughness: 0.5,
        metalness: 0.5
      });
      const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial);
      sphereMesh.position.set(
        (Math.random() - 0.5) * 20,
        0.7,
        (Math.random() - 0.5) * 20
      );
      sphereMesh.castShadow = true;
      sphereMesh.receiveShadow = true;
      sphereEntity.addObject(sphereMesh);
      this.world.addEntity(sphereEntity);
    }
  }

  /**
   * 创建雨水
   */
  private createRainWater(): void {
    // 使用预设创建雨水
    this.rainWaterEntity = EnhancedRainWaterPresets.createPreset(this.world, {
      type: this.currentRainWaterType,
      position: new THREE.Vector3(0, 10, 0),
      size: { width: 50, height: 20, depth: 50 }
    });

    // 获取雨水组件
    this.rainWaterComponent = this.rainWaterEntity.getComponent(EnhancedRainWaterComponent) as EnhancedRainWaterComponent;
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    uiContainer.style.color = 'white';
    uiContainer.style.fontFamily = 'Arial, sans-serif';
    uiContainer.style.fontSize = '14px';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.zIndex = '1000';
    document.body.appendChild(uiContainer);
    this.uiElements.push(uiContainer);

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '增强版雨水示例';
    title.style.margin = '0 0 10px 0';
    title.style.fontSize = '18px';
    uiContainer.appendChild(title);

    // 创建雨水类型信息
    const rainTypeInfo = document.createElement('div');
    rainTypeInfo.id = 'rain-type-info';
    rainTypeInfo.textContent = `当前雨水类型: ${this.currentRainWaterType}`;
    rainTypeInfo.style.marginBottom = '10px';
    uiContainer.appendChild(rainTypeInfo);

    // 创建风力信息
    const windInfo = document.createElement('div');
    windInfo.id = 'wind-info';
    windInfo.textContent = `风力方向: (${this.windDirection.x.toFixed(2)}, ${this.windDirection.y.toFixed(2)}), 强度: ${this.windStrength.toFixed(2)}`;
    windInfo.style.marginBottom = '10px';
    uiContainer.appendChild(windInfo);

    // 创建性能信息
    const performanceInfo = document.createElement('div');
    performanceInfo.id = 'performance-info';
    performanceInfo.textContent = '性能信息加载中...';
    performanceInfo.style.marginBottom = '10px';
    uiContainer.appendChild(performanceInfo);

    // 创建控制说明
    const controls = document.createElement('div');
    controls.innerHTML = `
      <p>控制说明:</p>
      <ul style="padding-left: 20px; margin: 5px 0;">
        <li>空格键: 切换雨水类型</li>
        <li>W/S: 增加/减少风力强度</li>
        <li>A/D: 旋转风力方向</li>
        <li>H: 显示/隐藏UI</li>
      </ul>
    `;
    uiContainer.appendChild(controls);

    // 注册键盘事件
    window.addEventListener('keydown', this.handleKeyDown.bind(this));
  }

  /**
   * 处理键盘事件
   * @param event 键盘事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case ' ': // 空格键
        this.changeRainWaterType();
        break;
      case 'w': // W键
      case 'W':
        this.increaseWindStrength();
        break;
      case 's': // S键
      case 'S':
        this.decreaseWindStrength();
        break;
      case 'a': // A键
      case 'A':
        this.rotateWindDirectionLeft();
        break;
      case 'd': // D键
      case 'D':
        this.rotateWindDirectionRight();
        break;
      case 'h': // H键
      case 'H':
        this.toggleUI();
        break;
    }
  }

  /**
   * 切换雨水类型
   */
  private changeRainWaterType(): void {
    // 更新索引
    this.currentRainWaterTypeIndex = (this.currentRainWaterTypeIndex + 1) % this.rainWaterTypes.length;
    this.currentRainWaterType = this.rainWaterTypes[this.currentRainWaterTypeIndex];

    // 移除旧的雨水实体
    this.world.removeEntity(this.rainWaterEntity);

    // 创建新的雨水实体
    this.rainWaterEntity = EnhancedRainWaterPresets.createPreset(this.world, {
      type: this.currentRainWaterType,
      position: new THREE.Vector3(0, 10, 0),
      size: { width: 50, height: 20, depth: 50 },
      windStrength: this.windStrength,
      windDirection: this.windDirection
    });

    // 获取雨水组件
    this.rainWaterComponent = this.rainWaterEntity.getComponent(EnhancedRainWaterComponent) as EnhancedRainWaterComponent;

    // 更新UI
    const rainTypeInfo = document.getElementById('rain-type-info');
    if (rainTypeInfo) {
      rainTypeInfo.textContent = `当前雨水类型: ${this.currentRainWaterType}`;
    }
  }

  /**
   * 增加风力强度
   */
  private increaseWindStrength(): void {
    this.windStrength = Math.min(5.0, this.windStrength + 0.2);
    this.updateWindSettings();
  }

  /**
   * 减少风力强度
   */
  private decreaseWindStrength(): void {
    this.windStrength = Math.max(0.0, this.windStrength - 0.2);
    this.updateWindSettings();
  }

  /**
   * 向左旋转风力方向
   */
  private rotateWindDirectionLeft(): void {
    const angle = Math.atan2(this.windDirection.y, this.windDirection.x);
    const newAngle = angle + 0.1;
    this.windDirection.x = Math.cos(newAngle);
    this.windDirection.y = Math.sin(newAngle);
    this.updateWindSettings();
  }

  /**
   * 向右旋转风力方向
   */
  private rotateWindDirectionRight(): void {
    const angle = Math.atan2(this.windDirection.y, this.windDirection.x);
    const newAngle = angle - 0.1;
    this.windDirection.x = Math.cos(newAngle);
    this.windDirection.y = Math.sin(newAngle);
    this.updateWindSettings();
  }

  /**
   * 更新风力设置
   */
  private updateWindSettings(): void {
    // 更新雨水组件
    if (this.rainWaterComponent) {
      this.rainWaterComponent.setWindStrength(this.windStrength);
      this.rainWaterComponent.setWindDirection(this.windDirection);
    }

    // 更新UI
    const windInfo = document.getElementById('wind-info');
    if (windInfo) {
      windInfo.textContent = `风力方向: (${this.windDirection.x.toFixed(2)}, ${this.windDirection.y.toFixed(2)}), 强度: ${this.windStrength.toFixed(2)}`;
    }
  }

  /**
   * 切换UI显示
   */
  private toggleUI(): void {
    this.showUI = !this.showUI;
    for (const element of this.uiElements) {
      element.style.display = this.showUI ? 'block' : 'none';
    }
  }

  /**
   * 更新性能信息
   */
  private updatePerformanceInfo(): void {
    const performanceInfo = document.getElementById('performance-info');
    if (performanceInfo) {
      const fps = Math.round(1000 / this.performanceMonitor.getAverageTime('frame'));
      const raindropCount = this.rainWaterComponent ? this.rainWaterComponent['currentRaindropCount'] : 0;
      const updateTime = this.performanceMonitor.getAverageTime('enhancedRainWaterUpdate').toFixed(2);
      
      performanceInfo.innerHTML = `
        FPS: ${fps}<br>
        雨滴数量: ${raindropCount}<br>
        更新时间: ${updateTime}ms
      `;
    }
  }

  /**
   * 更新风力
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWind(deltaTime: number): void {
    // 更新风力方向变化计时器
    this.windDirectionChangeTimer += deltaTime;

    // 如果达到变化间隔，随机变化风力方向
    if (this.windDirectionChangeTimer >= this.windDirectionChangeInterval) {
      this.windDirectionChangeTimer = 0;

      // 随机风力方向
      const angle = Math.random() * Math.PI * 2;
      this.windDirection.x = Math.cos(angle);
      this.windDirection.y = Math.sin(angle);

      // 随机风力强度
      this.windStrength = 0.5 + Math.random() * 2.0;

      // 更新风力设置
      this.updateWindSettings();
    }
  }

  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    const animate = () => {
      requestAnimationFrame(animate);

      // 开始帧计时
      this.performanceMonitor.start('frame');

      // 更新控制器
      this.controls.update();

      // 更新风力
      this.updateWind(0.016); // 假设60fps

      // 更新世界
      this.world.update();

      // 更新性能信息
      this.updatePerformanceInfo();

      // 结束帧计时
      this.performanceMonitor.end('frame');
    };

    animate();
  }
}
