/**
 * 服务实体
 */
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { ServiceInstanceEntity } from './service-instance.entity';

@Entity('services')
export class ServiceEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  name: string;

  /**
   * 服务版本
   */
  @Column({ default: '1.0.0' })
  version: string;

  /**
   * 服务最小兼容版本
   */
  @Column({ nullable: true })
  minCompatibleVersion: string;

  /**
   * 服务最大兼容版本
   */
  @Column({ nullable: true })
  maxCompatibleVersion: string;

  @Column({ nullable: true })
  description: string;

  /**
   * 服务类型
   */
  @Column({ default: 'service' })
  type: string;

  /**
   * 服务API路径
   */
  @Column({ nullable: true })
  apiPath: string;

  /**
   * 服务文档URL
   */
  @Column({ nullable: true })
  docsUrl: string;

  /**
   * 服务标签
   */
  @Column({ type: 'json', nullable: true })
  tags: string[];

  /**
   * 服务元数据
   */
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ default: true })
  isActive: boolean;

  @OneToMany(() => ServiceInstanceEntity, instance => instance.service, { cascade: true })
  instances: ServiceInstanceEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
