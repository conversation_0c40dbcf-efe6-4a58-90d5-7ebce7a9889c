/**
 * 角色控制器模块
 *
 * 提供角色控制器相关的类和接口
 */
export { AdvancedCharacterController, CharacterMovementMode, CharacterState } from './AdvancedCharacterController';
export type { AdvancedCharacterControllerConfig } from './AdvancedCharacterController';
export { ActionControlSystem, ActionType, ActionPriority } from './ActionControlSystem';
export type { ActionControlSystemConfig, ActionData, ActionEvent, ActionInstance } from './ActionControlSystem';
export { CharacterControllerPresetManager, ControllerPresetType } from './CharacterControllerPresetManager';
export type { CharacterControllerPresetManagerConfig, ControllerPresetData, ControllerTemplateData, ControllerTemplateParameter } from './CharacterControllerPresetManager';
export { EmotionBlendController } from './EmotionBlendController';
export type { EmotionBlendControllerConfig, EmotionExpressionData } from './EmotionBlendController';
