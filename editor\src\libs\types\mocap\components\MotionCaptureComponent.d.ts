/**
 * 动作捕捉组件
 * 用于存储动作捕捉数据
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { LandmarkData, WorldLandmarkData } from '../types/LandmarkData';
import { VRMHumanBoneName } from '../../avatar/types/VRMHumanBoneName';
import { Quaternion, Vector3 } from 'three';
/**
 * 动作捕捉组件配置
 */
export interface MotionCaptureComponentConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 平滑系数 */
    smoothingFactor?: number;
    /** 可见度阈值 */
    visibilityThreshold?: number;
}
/**
 * 动作捕捉组件
 * 存储动作捕捉数据和骨骼映射
 */
export declare class MotionCaptureComponent extends Component {
    /** 组件类型 */
    static readonly TYPE = "MotionCaptureComponent";
    /** 平滑系数 */
    smoothingFactor: number;
    /** 可见度阈值 */
    visibilityThreshold: number;
    /** 世界坐标系中的关键点数据 */
    worldLandmarks: WorldLandmarkData[];
    /** 屏幕坐标系中的关键点数据 */
    landmarks: LandmarkData[];
    /** 上一帧的世界坐标系关键点数据 */
    prevWorldLandmarks: WorldLandmarkData[];
    /** 上一帧的屏幕坐标系关键点数据 */
    prevLandmarks: LandmarkData[];
    /** 骨骼旋转数据 */
    boneRotations: Map<VRMHumanBoneName, Quaternion>;
    /** 平滑后的骨骼旋转数据 */
    smoothedBoneRotations: Map<VRMHumanBoneName, Quaternion>;
    /** 髋关节位置 */
    hipPosition: Vector3;
    /** 是否正在解算下半身 */
    solvingLowerBody: boolean;
    /** 脚部偏移 */
    footOffset: number;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 组件配置
     */
    constructor(entity: Entity, config?: MotionCaptureComponentConfig);
    /**
     * 初始化骨骼旋转数据
     */
    private initBoneRotations;
    /**
     * 设置世界坐标系关键点数据
     * @param landmarks 关键点数据
     */
    setWorldLandmarks(landmarks: WorldLandmarkData[]): void;
    /**
     * 设置屏幕坐标系关键点数据
     * @param landmarks 关键点数据
     */
    setLandmarks(landmarks: LandmarkData[]): void;
    /**
     * 设置骨骼旋转
     * @param boneName 骨骼名称
     * @param rotation 旋转四元数
     */
    setBoneRotation(boneName: VRMHumanBoneName, rotation: Quaternion): void;
    /**
     * 平滑骨骼旋转
     * @param boneName 骨骼名称
     */
    private smoothBoneRotation;
    /**
     * 获取平滑后的骨骼旋转
     * @param boneName 骨骼名称
     * @returns 平滑后的骨骼旋转
     */
    getSmoothedBoneRotation(boneName: VRMHumanBoneName): Quaternion | undefined;
    /**
     * 设置髋关节位置
     * @param position 位置向量
     */
    setHipPosition(position: Vector3): void;
    /**
     * 设置是否正在解算下半身
     * @param solving 是否正在解算
     */
    setSolvingLowerBody(solving: boolean): void;
    /**
     * 设置脚部偏移
     * @param offset 偏移值
     */
    setFootOffset(offset: number): void;
    /**
     * 克隆组件
     * @param entity 目标实体
     * @returns 克隆的组件
     */
    clone(entity: Entity): MotionCaptureComponent;
}
