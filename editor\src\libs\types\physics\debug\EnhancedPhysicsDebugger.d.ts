/**
 * 增强型物理调试器
 * 提供更多物理调试功能和性能监控
 */
import * as THREE from 'three';
import { PhysicsSystem } from '../PhysicsSystem';
import { PhysicsDebugger, PhysicsDebuggerOptions } from './PhysicsDebugger';
/**
 * 增强型物理调试器选项
 */
export interface EnhancedPhysicsDebuggerOptions extends PhysicsDebuggerOptions {
    /** 是否显示速度向量 */
    showVelocities?: boolean;
    /** 是否显示力向量 */
    showForces?: boolean;
    /** 是否显示质心 */
    showCenterOfMass?: boolean;
    /** 是否显示睡眠状态 */
    showSleepState?: boolean;
    /** 是否显示物理性能统计 */
    showPerformanceStats?: boolean;
    /** 是否显示碰撞点法线 */
    showContactNormals?: boolean;
    /** 是否显示碰撞点力 */
    showContactForces?: boolean;
    /** 是否显示碰撞点摩擦力 */
    showFrictionForces?: boolean;
    /** 速度向量颜色 */
    velocityColor?: THREE.Color | number;
    /** 力向量颜色 */
    forceColor?: THREE.Color | number;
    /** 质心颜色 */
    centerOfMassColor?: THREE.Color | number;
    /** 睡眠状态颜色 */
    sleepStateColor?: THREE.Color | number;
    /** 碰撞法线颜色 */
    contactNormalColor?: THREE.Color | number;
    /** 碰撞力颜色 */
    contactForceColor?: THREE.Color | number;
    /** 摩擦力颜色 */
    frictionForceColor?: THREE.Color | number;
    /** 向量缩放因子 */
    vectorScale?: number;
    /** 碰撞力缩放因子 */
    contactForceScale?: number;
}
/**
 * 物理性能统计
 */
interface PhysicsPerformanceStats {
    /** 物理更新时间 (ms) */
    updateTime: number;
    /** 碰撞检测时间 (ms) */
    collisionTime: number;
    /** 约束求解时间 (ms) */
    constraintTime: number;
    /** 物理体数量 */
    bodyCount: number;
    /** 约束数量 */
    constraintCount: number;
    /** 碰撞对数量 */
    contactCount: number;
    /** 帧率 */
    fps: number;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 增强型物理调试器
 */
export declare class EnhancedPhysicsDebugger extends PhysicsDebugger {
    /** 是否显示速度向量 */
    private showVelocities;
    /** 是否显示力向量 */
    private showForces;
    /** 是否显示质心 */
    private showCenterOfMass;
    /** 是否显示睡眠状态 */
    private showSleepState;
    /** 是否显示物理性能统计 */
    private showPerformanceStats;
    /** 是否显示碰撞点法线 */
    private showContactNormals;
    /** 是否显示碰撞点力 */
    private showContactForces;
    /** 是否显示碰撞点摩擦力 */
    private showFrictionForces;
    /** 速度向量颜色 */
    private velocityColor;
    /** 力向量颜色 */
    private forceColor;
    /** 质心颜色 */
    private centerOfMassColor;
    /** 睡眠状态颜色 */
    private sleepStateColor;
    /** 碰撞法线颜色 */
    private contactNormalColor;
    /** 碰撞力颜色 */
    private contactForceColor;
    /** 摩擦力颜色（暂时未使用，保留以备将来扩展） */
    private frictionForceColor;
    /** 向量缩放因子 */
    private vectorScale;
    /** 碰撞力缩放因子 */
    private contactForceScale;
    /** 速度向量映射 */
    private velocityArrows;
    /** 力向量映射 */
    private forceArrows;
    /** 碰撞法线向量 */
    private contactNormalArrows;
    /** 碰撞力向量 */
    private contactForceArrows;
    /** 摩擦力向量 */
    private frictionForceArrows;
    /** 质心网格映射 */
    private centerOfMassMeshes;
    /** 睡眠状态指示器映射 */
    private sleepStateMeshes;
    /** 性能统计数据 */
    private performanceStats;
    /** 性能统计面板 */
    private statsPanel;
    /** 性能监控开始时间 */
    private monitorStartTime;
    /** 性能监控结束时间 */
    private monitorEndTime;
    /** 上一帧时间 */
    private lastFrameTime;
    /** 帧计数器 */
    private frameCount;
    /** 帧率计算间隔 (ms) */
    private fpsInterval;
    /** 帧率计算时间戳 */
    private fpsTimestamp;
    /** 当前帧率 */
    private currentFps;
    /**
     * 创建增强型物理调试器
     * @param physicsSystem 物理系统
     * @param options 调试器选项
     */
    constructor(physicsSystem: PhysicsSystem, options?: EnhancedPhysicsDebuggerOptions);
    /**
     * 初始化性能统计面板
     */
    private initPerformanceStats;
    /**
     * 更新性能统计
     */
    private updatePerformanceStats;
    /**
     * 获取物理世界
     * @returns 物理世界
     */
    private getPhysicsWorld;
    /**
     * 获取物理系统
     * @returns 物理系统
     */
    getPhysicsSystem(): PhysicsSystem;
    /**
     * 开始性能监控
     */
    startPerformanceMonitor(): void;
    /**
     * 结束性能监控
     * @returns 监控时间 (ms)
     */
    endPerformanceMonitor(): number;
    /**
     * 获取性能统计数据
     * @returns 性能统计数据
     */
    getPerformanceStats(): PhysicsPerformanceStats[];
    /**
     * 清除性能统计数据
     */
    clearPerformanceStats(): void;
    /**
     * 设置是否显示速度向量
     * @param show 是否显示
     */
    setShowVelocities(show: boolean): void;
    /**
     * 设置是否显示力向量
     * @param show 是否显示
     */
    setShowForces(show: boolean): void;
    /**
     * 设置是否显示质心
     * @param show 是否显示
     */
    setShowCenterOfMass(show: boolean): void;
    /**
     * 设置是否显示睡眠状态
     * @param show 是否显示
     */
    setShowSleepState(show: boolean): void;
    /**
     * 设置是否显示物理性能统计
     * @param show 是否显示
     */
    setShowPerformanceStats(show: boolean): void;
    /**
     * 设置是否显示碰撞法线
     * @param show 是否显示
     */
    setShowContactNormals(show: boolean): void;
    /**
     * 设置是否显示碰撞力
     * @param show 是否显示
     */
    setShowContactForces(show: boolean): void;
    /**
     * 设置是否显示摩擦力
     * @param show 是否显示
     */
    setShowFrictionForces(show: boolean): void;
    /**
     * 获取调试场景
     * @returns 调试场景
     */
    getScene(): THREE.Scene;
    /**
     * 初始化调试器
     */
    initialize(): void;
    /**
     * 更新调试器
     */
    update(): void;
    /**
     * 更新碰撞可视化
     */
    private updateContactVisualization;
    /**
     * 创建速度向量
     * @param body 物理体
     * @returns 速度向量
     */
    private createVelocityArrow;
    /**
     * 更新速度向量
     * @param body 物理体
     * @param arrow 速度向量
     */
    private updateVelocityArrow;
    /**
     * 创建力向量
     * @param body 物理体
     * @returns 力向量
     */
    private createForceArrow;
    /**
     * 更新力向量
     * @param body 物理体
     * @param arrow 力向量
     */
    private updateForceArrow;
    /**
     * 创建质心网格
     * @param body 物理体
     * @returns 质心网格
     */
    private createCenterOfMassMesh;
    /**
     * 更新质心网格
     * @param body 物理体
     * @param mesh 质心网格
     */
    private updateCenterOfMassMesh;
    /**
     * 创建睡眠状态网格
     * @param body 物理体
     * @returns 睡眠状态网格
     */
    private createSleepStateMesh;
    /**
     * 更新睡眠状态网格
     * @param body 物理体
     * @param sprite 睡眠状态精灵
     */
    private updateSleepStateMesh;
    /**
     * 销毁调试器
     */
    dispose(): void;
}
export {};
