/**
 * 优化历史记录
 * 用于记录和比较场景优化历史
 */
import { SceneAnalysisResult, OptimizationType } from './SceneOptimizer';
/**
 * 优化历史记录项
 */
export interface OptimizationHistoryEntry {
    /** 记录ID */
    id: string;
    /** 场景ID */
    sceneId: string;
    /** 场景名称 */
    sceneName: string;
    /** 分析结果 */
    analysisResult: SceneAnalysisResult;
    /** 应用的优化类型 */
    appliedOptimizations: OptimizationType[];
    /** 优化前的总体得分 */
    scoreBefore: number;
    /** 优化后的总体得分 */
    scoreAfter: number;
    /** 时间戳 */
    timestamp: number;
    /** 描述 */
    description: string;
}
/**
 * 优化比较结果
 */
export interface OptimizationComparisonResult {
    /** 基准记录 */
    baseEntry: OptimizationHistoryEntry;
    /** 比较记录 */
    compareEntry: OptimizationHistoryEntry;
    /** 得分变化 */
    scoreDifference: number;
    /** 得分变化百分比 */
    scorePercentChange: number;
    /** 统计数据变化 */
    statsChanges: {
        /** 字段名称 */
        field: string;
        /** 字段显示名称 */
        displayName: string;
        /** 基准值 */
        baseValue: number;
        /** 比较值 */
        compareValue: number;
        /** 差值 */
        difference: number;
        /** 变化百分比 */
        percentChange: number;
    }[];
    /** 应用的优化 */
    appliedOptimizations: OptimizationType[];
}
/**
 * 优化历史记录
 */
export declare class OptimizationHistory {
    /** 单例实例 */
    private static instance;
    /** 历史记录 */
    private history;
    /** 最大历史记录数量 */
    private maxHistoryEntries;
    /**
     * 获取单例实例
     */
    static getInstance(): OptimizationHistory;
    /**
     * 构造函数
     */
    private constructor();
    /**
     * 添加历史记录
     * @param entry 历史记录项
     */
    addEntry(entry: Omit<OptimizationHistoryEntry, 'id'>): OptimizationHistoryEntry;
    /**
     * 获取历史记录
     * @param sceneId 可选的场景ID过滤
     * @returns 历史记录数组
     */
    getHistory(sceneId?: string): OptimizationHistoryEntry[];
    /**
     * 获取历史记录项
     * @param id 记录ID
     * @returns 历史记录项
     */
    getEntry(id: string): OptimizationHistoryEntry | undefined;
    /**
     * 删除历史记录项
     * @param id 记录ID
     * @returns 是否成功删除
     */
    deleteEntry(id: string): boolean;
    /**
     * 清空历史记录
     */
    clearHistory(): void;
    /**
     * 比较两个历史记录
     * @param baseEntryId 基准记录ID
     * @param compareEntryId 比较记录ID
     * @returns 比较结果
     */
    compareEntries(baseEntryId: string, compareEntryId: string): OptimizationComparisonResult | null;
    /**
     * 计算统计数据变化
     * @param baseResult 基准分析结果
     * @param compareResult 比较分析结果
     * @returns 统计数据变化
     */
    private calculateStatsChanges;
    /**
     * 从本地存储加载历史记录
     */
    private loadFromStorage;
    /**
     * 保存历史记录到本地存储
     */
    private saveToStorage;
}
