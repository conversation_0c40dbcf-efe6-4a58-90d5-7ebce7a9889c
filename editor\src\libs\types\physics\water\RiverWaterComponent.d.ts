/**
 * 河流水体组件
 * 用于模拟河流水体，包括流动、波动等特性
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyConfig } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
/**
 * 河流水体配置
 */
export interface RiverWaterConfig extends WaterBodyConfig {
    /** 河流路径点 */
    pathPoints?: THREE.Vector3[];
    /** 河流宽度 */
    width?: number;
    /** 河流深度 */
    depth?: number;
    /** 河流流速 */
    flowSpeed?: number;
    /** 河流弯曲度 */
    curvature?: number;
    /** 河床高度变化 */
    bedHeightVariation?: number;
    /** 河岸高度 */
    bankHeight?: number;
    /** 是否生成河岸 */
    generateBanks?: boolean;
    /** 是否使用样条曲线 */
    useSpline?: boolean;
}
/**
 * 河流水体组件
 */
export declare class RiverWaterComponent extends WaterBodyComponent {
    /** 河流路径点 */
    private pathPoints;
    /** 河流宽度 */
    private width;
    /** 河流深度 */
    private depth;
    /** 河流流速（重命名以避免与父类冲突） */
    private riverFlowSpeed;
    /** 河流弯曲度 */
    private curvature;
    /** 河床高度变化 */
    private bedHeightVariation;
    /** 河岸高度 */
    private bankHeight;
    /** 是否生成河岸 */
    private generateBanks;
    /** 是否使用样条曲线 */
    private useSpline;
    /** 河流曲线 */
    private riverCurve;
    /** 河流几何体 */
    private riverGeometry;
    /** 河岸几何体 */
    private bankGeometry;
    /** 河床几何体 */
    private bedGeometry;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: RiverWaterConfig);
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 创建河流曲线
     */
    private createRiverCurve;
    /**
     * 创建河流几何体
     */
    private createRiverGeometry;
    /**
     * 创建河岸几何体
     */
    private createBankGeometry;
    /**
     * 创建河床几何体
     */
    private createBedGeometry;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新河流流动效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateRiverFlow;
    /**
     * 设置河流路径点
     * @param points 路径点
     */
    setPathPoints(points: THREE.Vector3[]): void;
    /**
     * 设置河流宽度
     * @param width 宽度
     */
    setWidth(width: number): void;
    /**
     * 设置河流深度
     * @param depth 深度
     */
    setDepth(depth: number): void;
    /**
     * 获取河流几何体
     * @returns 河流几何体
     */
    getRiverGeometry(): THREE.BufferGeometry | null;
    /**
     * 获取河岸几何体
     * @returns 河岸几何体
     */
    getBankGeometry(): THREE.BufferGeometry | null;
    /**
     * 获取河床几何体
     * @returns 河床几何体
     */
    getBedGeometry(): THREE.BufferGeometry | null;
    /**
     * 获取河流曲线
     * @returns 河流曲线
     */
    getRiverCurve(): THREE.CatmullRomCurve3 | null;
    /**
     * 获取河流流速
     * @returns 河流流速
     */
    getRiverFlowSpeed(): number;
    /**
     * 设置河流流速
     * @param speed 流速
     */
    setRiverFlowSpeed(speed: number): void;
}
