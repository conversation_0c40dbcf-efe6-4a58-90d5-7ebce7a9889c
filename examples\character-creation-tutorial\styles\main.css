/* 角色创建教程样式 */

/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  background-color: #1e1e1e;
  color: #f0f0f0;
  overflow: hidden;
}

/* 容器样式 */
#canvas-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

#ui-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 20px;
}

/* 面板样式 */
#tutorial-panel {
  width: 400px;
  background-color: rgba(30, 30, 30, 0.8);
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  margin-bottom: 20px;
  pointer-events: auto;
  overflow: hidden;
}

#properties-panel {
  width: 400px;
  background-color: rgba(30, 30, 30, 0.8);
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  pointer-events: auto;
  overflow: hidden;
  max-height: calc(100vh - 400px);
  overflow-y: auto;
}

.panel-header {
  background-color: #333;
  padding: 12px 16px;
  border-bottom: 1px solid #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h2 {
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  margin: 0;
}

.panel-content {
  padding: 16px;
}

.panel-footer {
  padding: 12px 16px;
  border-top: 1px solid #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 步骤指示器 */
.step-indicators {
  display: flex;
  gap: 8px;
}

.step-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #555;
  transition: background-color 0.3s ease;
}

.step-indicator.active {
  background-color: #4CAF50;
}

/* 步骤内容 */
.step-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #4CAF50;
}

.step-description {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.step-description p {
  margin-bottom: 12px;
}

.step-description ol, .step-description ul {
  margin-left: 20px;
  margin-bottom: 12px;
}

.step-description li {
  margin-bottom: 6px;
}

.step-description .tip {
  background-color: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4CAF50;
  padding: 8px 12px;
  margin: 12px 0;
  font-size: 13px;
}

.step-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
}

/* 按钮样式 */
.nav-button, .action-button {
  background-color: #333;
  color: #fff;
  border: 1px solid #555;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.nav-button:hover, .action-button:hover {
  background-color: #444;
}

.nav-button:active, .action-button:active {
  background-color: #2a2a2a;
}

.nav-button:disabled {
  background-color: #2a2a2a;
  color: #666;
  cursor: not-allowed;
}

.action-button {
  background-color: #4CAF50;
  border-color: #43A047;
}

.action-button:hover {
  background-color: #43A047;
}

.action-button:active {
  background-color: #388E3C;
}

/* 属性面板样式 */
.properties-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.property-group {
  border: 1px solid #444;
  border-radius: 4px;
  overflow: hidden;
}

.property-group h3 {
  background-color: #333;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  border-bottom: 1px solid #444;
}

.property-row {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #333;
}

.property-row:last-child {
  border-bottom: none;
}

.property-row label {
  flex: 0 0 100px;
  font-size: 13px;
}

.property-row input[type="text"],
.property-row input[type="number"],
.property-row select {
  background-color: #333;
  border: 1px solid #555;
  border-radius: 4px;
  padding: 6px 8px;
  color: #fff;
  font-size: 13px;
  flex: 1;
}

.property-row input[type="range"] {
  flex: 1;
  margin-right: 8px;
}

.property-row span {
  font-size: 13px;
  margin-left: 8px;
  color: #aaa;
}

.vector-input {
  display: flex;
  gap: 4px;
  flex: 1;
}

.vector-input input {
  flex: 1;
  width: 0;
}

/* 对话框样式 */
.sample-model-dialog,
.sample-animation-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  background-color: #2a2a2a;
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  z-index: 1000;
  overflow: hidden;
}

.dialog-header {
  background-color: #333;
  padding: 12px 16px;
  border-bottom: 1px solid #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-header h2 {
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: #aaa;
  font-size: 20px;
  cursor: pointer;
}

.dialog-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.dialog-footer {
  padding: 12px 16px;
  border-top: 1px solid #444;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.cancel-button, .select-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.cancel-button {
  background-color: #555;
  color: #fff;
  border: 1px solid #666;
}

.select-button {
  background-color: #4CAF50;
  color: #fff;
  border: 1px solid #43A047;
}

/* 模型/动画卡片样式 */
.model-grid, .animation-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.model-card, .animation-card {
  background-color: #333;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.model-card:hover, .animation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.model-card.selected, .animation-card.selected {
  border: 2px solid #4CAF50;
}

.model-thumbnail, .animation-thumbnail {
  height: 150px;
  background-size: cover;
  background-position: center;
  background-color: #222;
}

.model-info, .animation-info {
  padding: 12px;
}

.model-info h3, .animation-info h3 {
  font-size: 16px;
  margin: 0 0 8px 0;
}

.model-info p, .animation-info p {
  font-size: 13px;
  color: #aaa;
  margin: 0;
}

/* 加载指示器 */
#loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #4CAF50;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #fff;
}

/* 成功消息 */
.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #4CAF50;
  color: #fff;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1500;
  animation: fadeIn 0.3s ease, fadeOut 0.3s ease 2.7s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-20px); }
}
