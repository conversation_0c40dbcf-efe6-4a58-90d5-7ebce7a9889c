/**
 * 端到端性能测试
 * 模拟真实用户场景的完整流程
 */
import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';
import { CONFIG, LOAD_PROFILES, TEST_USERS, TEST_PROJECTS, randomSleep, getRandomItem } from './config.js';

// 自定义指标
const scenarioSuccessRate = new Rate('scenario_success');
const scenarioLatency = new Trend('scenario_latency');
const apiCallsPerScenario = new Counter('api_calls_per_scenario');

// 测试配置
export const options = LOAD_PROFILES.light;

// 测试数据
const testData = {
  users: new Map(), // 用户名 -> {token, userId, projects}
};

// 初始化函数 - 在测试开始前运行一次
export function setup() {
  console.log('开始端到端性能测试');
  return testData;
}

// 默认函数 - 每个虚拟用户都会运行
export default function(data) {
  // 场景开始时间
  const scenarioStartTime = new Date().getTime();
  let scenarioSuccess = true;
  let apiCalls = 0;
  
  // 随机选择一个测试用户
  const user = getRandomItem(TEST_USERS);
  
  // 场景1: 用户注册和登录
  group('用户注册和登录', () => {
    // 检查用户是否已存在
    if (!data.users.has(user.username)) {
      // 注册新用户
      const registerRes = http.post(`${CONFIG.apiGateway}/auth/register`, JSON.stringify({
        username: user.username,
        password: user.password,
        email: user.email,
      }), {
        headers: { 'Content-Type': 'application/json' },
      });
      
      apiCalls++;
      
      const registerSuccess = check(registerRes, {
        '用户注册成功': (r) => r.status === 201 || r.status === 409, // 409表示用户已存在
      });
      
      scenarioSuccess = scenarioSuccess && registerSuccess;
    }
    
    // 登录
    const loginRes = http.post(`${CONFIG.apiGateway}/auth/login`, JSON.stringify({
      username: user.username,
      password: user.password,
    }), {
      headers: { 'Content-Type': 'application/json' },
    });
    
    apiCalls++;
    
    const loginSuccess = check(loginRes, {
      '用户登录成功': (r) => r.status === 200,
    });
    
    scenarioSuccess = scenarioSuccess && loginSuccess;
    
    if (loginSuccess) {
      const body = JSON.parse(loginRes.body);
      
      // 保存用户数据
      if (!data.users.has(user.username)) {
        data.users.set(user.username, {
          token: body.accessToken,
          userId: body.user.id,
          projects: [],
        });
      } else {
        data.users.get(user.username).token = body.accessToken;
      }
    } else {
      // 如果登录失败，跳过后续场景
      return;
    }
    
    randomSleep(1, 3);
  });
  
  // 获取当前用户数据
  const userData = data.users.get(user.username);
  if (!userData) {
    scenarioSuccess = false;
    return;
  }
  
  // 场景2: 创建和管理项目
  group('创建和管理项目', () => {
    // 如果用户还没有项目，创建一个新项目
    if (userData.projects.length === 0) {
      const project = getRandomItem(TEST_PROJECTS);
      
      const createProjectRes = http.post(`${CONFIG.apiGateway}/projects`, JSON.stringify({
        name: `${project.name}_${user.username}`,
        description: project.description,
      }), {
        headers: {
          'Authorization': `Bearer ${userData.token}`,
          'Content-Type': 'application/json',
        },
      });
      
      apiCalls++;
      
      const createProjectSuccess = check(createProjectRes, {
        '项目创建成功': (r) => r.status === 201,
      });
      
      scenarioSuccess = scenarioSuccess && createProjectSuccess;
      
      if (createProjectSuccess) {
        const body = JSON.parse(createProjectRes.body);
        userData.projects.push({
          id: body.id,
          name: body.name,
          scenes: [],
          assets: [],
        });
      }
    }
    
    // 获取项目列表
    const getProjectsRes = http.get(`${CONFIG.apiGateway}/projects`, {
      headers: {
        'Authorization': `Bearer ${userData.token}`,
        'Content-Type': 'application/json',
      },
    });
    
    apiCalls++;
    
    const getProjectsSuccess = check(getProjectsRes, {
      '获取项目列表成功': (r) => r.status === 200,
    });
    
    scenarioSuccess = scenarioSuccess && getProjectsSuccess;
    
    randomSleep(1, 3);
  });
  
  // 如果用户没有项目，跳过后续场景
  if (userData.projects.length === 0) {
    scenarioSuccess = false;
    return;
  }
  
  // 随机选择一个项目
  const project = getRandomItem(userData.projects);
  
  // 场景3: 创建和管理场景
  group('创建和管理场景', () => {
    // 如果项目还没有场景，创建一个新场景
    if (project.scenes.length === 0) {
      const createSceneRes = http.post(`${CONFIG.apiGateway}/projects/${project.id}/scenes`, JSON.stringify({
        name: `测试场景_${Date.now()}`,
        data: {
          entities: [
            {
              id: 'entity1',
              name: '测试实体',
              components: [
                {
                  type: 'transform',
                  position: { x: 0, y: 0, z: 0 },
                  rotation: { x: 0, y: 0, z: 0 },
                  scale: { x: 1, y: 1, z: 1 },
                },
              ],
            },
          ],
        },
      }), {
        headers: {
          'Authorization': `Bearer ${userData.token}`,
          'Content-Type': 'application/json',
        },
      });
      
      apiCalls++;
      
      const createSceneSuccess = check(createSceneRes, {
        '场景创建成功': (r) => r.status === 201,
      });
      
      scenarioSuccess = scenarioSuccess && createSceneSuccess;
      
      if (createSceneSuccess) {
        const body = JSON.parse(createSceneRes.body);
        project.scenes.push({
          id: body.id,
          name: body.name,
        });
      }
    }
    
    // 获取场景列表
    const getScenesRes = http.get(`${CONFIG.apiGateway}/projects/${project.id}/scenes`, {
      headers: {
        'Authorization': `Bearer ${userData.token}`,
        'Content-Type': 'application/json',
      },
    });
    
    apiCalls++;
    
    const getScenesSuccess = check(getScenesRes, {
      '获取场景列表成功': (r) => r.status === 200,
    });
    
    scenarioSuccess = scenarioSuccess && getScenesSuccess;
    
    randomSleep(1, 3);
  });
  
  // 场景4: 上传和管理资产
  group('上传和管理资产', () => {
    // 创建一个简单的JSON资产
    const assetData = JSON.stringify({
      name: `测试资产_${Date.now()}`,
      type: 'json',
      data: { test: 'data' },
    });
    
    // 上传资产
    const uploadAssetRes = http.post(`${CONFIG.apiGateway}/assets`, assetData, {
      headers: {
        'Authorization': `Bearer ${userData.token}`,
        'Content-Type': 'application/json',
      },
      params: {
        projectId: project.id,
      },
    });
    
    apiCalls++;
    
    const uploadAssetSuccess = check(uploadAssetRes, {
      '资产上传成功': (r) => r.status === 201,
    });
    
    scenarioSuccess = scenarioSuccess && uploadAssetSuccess;
    
    if (uploadAssetSuccess) {
      const body = JSON.parse(uploadAssetRes.body);
      project.assets.push({
        id: body.id,
        name: body.name,
      });
    }
    
    // 获取资产列表
    const getAssetsRes = http.get(`${CONFIG.apiGateway}/assets`, {
      headers: {
        'Authorization': `Bearer ${userData.token}`,
        'Content-Type': 'application/json',
      },
      params: {
        projectId: project.id,
      },
    });
    
    apiCalls++;
    
    const getAssetsSuccess = check(getAssetsRes, {
      '获取资产列表成功': (r) => r.status === 200,
    });
    
    scenarioSuccess = scenarioSuccess && getAssetsSuccess;
    
    randomSleep(1, 3);
  });
  
  // 记录场景执行结果
  const scenarioEndTime = new Date().getTime();
  const scenarioDuration = scenarioEndTime - scenarioStartTime;
  
  scenarioSuccessRate.add(scenarioSuccess);
  scenarioLatency.add(scenarioDuration);
  apiCallsPerScenario.add(apiCalls);
  
  // 在场景之间添加随机休眠
  randomSleep(3, 8);
}

// 清理函数 - 在测试结束后运行一次
export function teardown(data) {
  console.log('端到端性能测试完成');
}
