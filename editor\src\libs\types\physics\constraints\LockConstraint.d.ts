import type { Entity } from '../../core/Entity';
import { PhysicsConstraint } from './PhysicsConstraint';
/**
 * 锁定约束选项
 */
export interface LockConstraintOptions {
    /** 最大力 */
    maxForce?: number;
    /** 是否允许连接的物体之间碰撞 */
    collideConnected?: boolean;
}
/**
 * 锁定约束
 */
export declare class LockConstraint extends PhysicsConstraint {
    /** 组件类型 */
    static readonly type: string;
    /** 最大力 */
    private maxForce;
    /**
     * 创建锁定约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    constructor(targetEntity?: Entity | null, options?: LockConstraintOptions);
    /**
     * 创建约束
     */
    protected createConstraint(): void;
    /**
     * 设置最大力
     * @param maxForce 最大力
     */
    setMaxForce(maxForce: number): void;
    /**
     * 获取最大力
     * @returns 最大力
     */
    getMaxForce(): number;
    /**
     * 重新创建约束
     */
    private recreateConstraint;
}
