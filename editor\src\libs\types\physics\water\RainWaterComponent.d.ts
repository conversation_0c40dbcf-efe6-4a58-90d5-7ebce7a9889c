/**
 * 雨水水体组件
 * 用于表示雨水水体及其特殊物理属性和效果
 */
import * as THREE from 'three';
import { WaterBodyComponent } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
/**
 * 雨水类型
 */
export declare enum RainWaterType {
    /** 轻雨 */
    LIGHT = "light",
    /** 中雨 */
    MEDIUM = "medium",
    /** 暴雨 */
    HEAVY = "heavy",
    /** 雷雨 */
    THUNDERSTORM = "thunderstorm",
    /** 季风雨 */
    MONSOON = "monsoon"
}
/**
 * 雨水水体配置
 */
export interface RainWaterConfig {
    /** 宽度 */
    width?: number;
    /** 高度 */
    height?: number;
    /** 深度 */
    depth?: number;
    /** 位置 */
    position?: THREE.Vector3;
    /** 旋转 */
    rotation?: THREE.Euler;
    /** 颜色 */
    color?: THREE.Color;
    /** 不透明度 */
    opacity?: number;
    /** 流速 */
    flowSpeed?: number;
    /** 雨水类型 */
    rainWaterType?: RainWaterType;
    /** 雨强度 */
    rainIntensity?: number;
    /** 雨滴大小 */
    raindropSize?: number;
    /** 雨滴频率 */
    raindropFrequency?: number;
    /** 雨滴寿命 */
    raindropLifetime?: number;
    /** 是否启用水花效果 */
    enableSplashEffect?: boolean;
    /** 水花效果强度 */
    splashEffectStrength?: number;
    /** 是否启用水波纹效果 */
    enableRippleEffect?: boolean;
    /** 水波纹效果强度 */
    rippleEffectStrength?: number;
    /** 是否启用水流效果 */
    enableFlowEffect?: boolean;
    /** 水流效果强度 */
    flowEffectStrength?: number;
    /** 是否启用声音效果 */
    enableSoundEffect?: boolean;
    /** 声音效果音量 */
    soundEffectVolume?: number;
    /** 是否启用水流动力学 */
    enableFluidDynamics?: boolean;
    /** 是否启用 */
    enabled?: boolean;
}
/**
 * 雨水水体组件
 */
export declare class RainWaterComponent extends WaterBodyComponent {
    /** 雨水类型 */
    private rainWaterType;
    /** 雨强度 */
    private rainIntensity;
    /** 雨滴大小 */
    private raindropSize;
    /** 雨滴频率 */
    private raindropFrequency;
    /** 雨滴寿命 */
    private raindropLifetime;
    /** 是否启用水花效果 */
    private enableSplashEffect;
    /** 水花效果强度 */
    private splashEffectStrength;
    /** 是否启用水波纹效果 */
    private enableRippleEffect;
    /** 水波纹效果强度 */
    private rippleEffectStrength;
    /** 是否启用水流效果 */
    private enableFlowEffect;
    /** 水流效果强度 */
    private flowEffectStrength;
    /** 是否启用声音效果 */
    private enableSoundEffect;
    /** 声音效果音量 */
    private soundEffectVolume;
    /** 是否启用水流动力学 */
    private enableFluidDynamics;
    /** 音频源 */
    private audioSource;
    /** 雨滴粒子系统 */
    private raindropParticleSystem;
    /** 水花粒子系统 */
    private splashParticleSystem;
    /** 水波纹粒子系统 */
    private rippleParticleSystem;
    /** 噪声生成器 */
    private noiseGenerator;
    /** 雨滴计时器 */
    private raindropTimer;
    /** 水流网格 */
    private flowMesh;
    /**
     * 创建雨水水体组件
     * @param entity 实体
     * @param config 雨水配置
     */
    constructor(entity: Entity, config?: RainWaterConfig);
    /**
     * 获取雨水类型
     * @returns 雨水类型
     */
    getRainWaterType(): RainWaterType;
    /**
     * 设置雨水类型
     * @param type 雨水类型
     */
    setRainWaterType(type: RainWaterType): void;
    /**
     * 获取雨强度
     * @returns 雨强度
     */
    getRainIntensity(): number;
    /**
     * 设置雨强度
     * @param intensity 雨强度
     */
    setRainIntensity(intensity: number): void;
    /**
     * 初始化雨水组件
     */
    initialize(): void;
    /**
     * 应用配置
     * @param config 雨水配置
     */
    private applyConfig;
    /**
     * 更新雨水参数
     * 根据雨水类型设置相应的参数
     */
    private updateRainParameters;
    /**
     * 创建水流网格
     */
    private createFlowMesh;
    /**
     * 初始化音频
     */
    private initializeAudio;
    /**
     * 初始化粒子系统
     */
    private initializeParticleSystems;
    /**
     * 更新雨水组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新雨滴生成
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateRaindrops;
    /**
     * 生成雨滴
     */
    private generateRaindrops;
    /**
     * 获取雨滴乘数（基于雨水类型）
     * @returns 雨滴乘数
     */
    private getRaindropMultiplier;
    /**
     * 获取风效应
     * @returns 风效应向量
     */
    private getWindEffect;
    /**
     * 更新水流动力学
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateFluidDynamics;
    /**
     * 更新水面波动
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterSurface;
    /**
     * 更新音频
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateAudio;
    /**
     * 更新粒子效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateParticleEffects;
    /**
     * 设置雨滴寿命
     * @param lifetime 寿命（秒）
     */
    setRaindropLifetime(lifetime: number): void;
    /**
     * 获取雨滴寿命
     * @returns 雨滴寿命（秒）
     */
    getRaindropLifetime(): number;
    /**
     * 设置水花效果强度
     * @param strength 强度（0-1）
     */
    setSplashEffectStrength(strength: number): void;
    /**
     * 设置水波纹效果强度
     * @param strength 强度（0-1）
     */
    setRippleEffectStrength(strength: number): void;
    /**
     * 设置水流效果强度
     * @param strength 强度（0-1）
     */
    setFlowEffectStrength(strength: number): void;
    /**
     * 使用噪声生成器创建自然的雨滴分布
     * @param x X坐标
     * @param z Z坐标
     * @param time 时间
     * @returns 噪声值
     */
    private generateRaindropNoise;
    /**
     * 创建雨滴粒子系统
     */
    private createRaindropParticleSystem;
    /**
     * 创建水花粒子系统
     */
    private createSplashParticleSystem;
    /**
     * 创建水波纹粒子系统
     */
    private createRippleParticleSystem;
    /**
     * 销毁组件，清理资源
     */
    destroy(): void;
}
