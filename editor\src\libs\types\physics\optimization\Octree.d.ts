/**
 * 八叉树空间分区
 * 用于加速碰撞检测
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { ISpatialPartitioning } from './SpatialPartitioning';
/**
 * 八叉树配置接口
 */
export interface OctreeOptions {
    /** 最大深度 */
    maxDepth?: number;
    /** 最大物体数量 */
    maxObjects?: number;
    /** 最小节点大小 */
    minNodeSize?: number;
    /** 世界大小 */
    worldSize?: number;
    /** 世界中心 */
    worldCenter?: CANNON.Vec3;
    /** 是否使用松散八叉树 */
    useLooseOctree?: boolean;
    /** 松散因子 */
    looseFactor?: number;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 八叉树节点接口
 */
export interface OctreeNode {
    /** 节点中心 */
    center: CANNON.Vec3;
    /** 节点半尺寸 */
    halfSize: number;
    /** 节点深度 */
    depth: number;
    /** 节点物体 */
    bodies: Set<CANNON.Body>;
    /** 子节点 */
    children: OctreeNode[] | null;
    /** 节点AABB */
    aabb: CANNON.AABB;
    /** 是否为叶子节点 */
    isLeaf: boolean;
}
/**
 * 八叉树类
 */
export declare class Octree implements ISpatialPartitioning {
    /** 最大深度 */
    private maxDepth;
    /** 最大物体数量 */
    private maxObjects;
    /** 最小节点大小 */
    private minNodeSize;
    /** 世界大小 */
    private worldSize;
    /** 世界中心 */
    private worldCenter;
    /** 是否使用松散八叉树 */
    private useLooseOctree;
    /** 松散因子 */
    private looseFactor;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 根节点 */
    private root;
    /** 物体到节点的映射 */
    private bodyToNodes;
    /** 物体列表 */
    private bodies;
    /** 调试网格 */
    private debugMesh;
    /**
     * 创建八叉树
     * @param options 八叉树配置
     */
    constructor(options?: OctreeOptions);
    /**
     * 创建节点
     * @param center 节点中心
     * @param halfSize 节点半尺寸
     * @param depth 节点深度
     * @returns 节点
     */
    private createNode;
    /**
     * 创建调试网格
     */
    private createDebugMesh;
    /**
     * 创建节点调试网格
     * @param node 节点
     * @param parent 父对象
     */
    private createNodeDebugMesh;
    /**
     * 更新调试网格
     */
    private updateDebugMesh;
    /**
     * 添加物体
     * @param body 物体
     */
    add(body: CANNON.Body): void;
    /**
     * 获取物体的AABB
     * @param body 物体
     * @returns AABB
     */
    private getBodyAABB;
    /**
     * 将物体插入节点
     * @param body 物体
     * @param aabb 物体AABB
     * @param node 节点
     * @param nodes 物体所在的节点集合
     */
    private insertBody;
    /**
     * 移除物体
     * @param body 物体
     */
    remove(body: CANNON.Body): void;
    /**
     * 更新物体
     * @param body 物体
     */
    update(body: CANNON.Body): void;
    /**
     * 更新所有物体
     */
    updateAll(): void;
    /**
     * 查询区域内的物体
     * @param min 最小坐标
     * @param max 最大坐标
     * @returns 区域内的物体
     */
    queryRegion(min: CANNON.Vec3, max: CANNON.Vec3): CANNON.Body[];
    /**
     * 查询节点区域内的物体
     * @param node 节点
     * @param queryAABB 查询AABB
     * @param bodies 物体集合
     */
    private queryNodeRegion;
    /**
     * 检查两个AABB是否重叠
     * @param minA AABB A的最小坐标
     * @param maxA AABB A的最大坐标
     * @param minB AABB B的最小坐标
     * @param maxB AABB B的最大坐标
     * @returns 是否重叠
     */
    private aabbOverlap;
    /**
     * 查询射线碰撞的物体
     * @param from 射线起点
     * @param to 射线终点
     * @returns 射线碰撞的物体
     */
    queryRay(from: CANNON.Vec3, to: CANNON.Vec3): CANNON.Body[];
    /**
     * 查询节点射线碰撞的物体
     * @param node 节点
     * @param from 射线起点
     * @param direction 射线方向
     * @param length 射线长度
     * @param bodies 物体集合
     */
    private queryNodeRay;
    /**
     * 检查射线是否与AABB相交
     * @param from 射线起点
     * @param direction 射线方向
     * @param length 射线长度
     * @param min AABB的最小坐标
     * @param max AABB的最大坐标
     * @returns 是否相交
     */
    private rayAabbIntersect;
    /**
     * 查询球体碰撞的物体
     * @param center 球体中心
     * @param radius 球体半径
     * @returns 球体碰撞的物体
     */
    querySphere(center: CANNON.Vec3, radius: number): CANNON.Body[];
    /**
     * 查询节点球体碰撞的物体
     * @param node 节点
     * @param center 球体中心
     * @param radius 球体半径
     * @param queryAABB 查询AABB
     * @param bodies 物体集合
     */
    private queryNodeSphere;
    /**
     * 检查球体是否与AABB相交
     * @param center 球体中心
     * @param radius 球体半径
     * @param min AABB的最小坐标
     * @param max AABB的最大坐标
     * @returns 是否相交
     */
    private sphereAabbIntersect;
    /**
     * 查询与物体可能碰撞的物体
     * @param body 物体
     * @returns 可能碰撞的物体
     */
    queryPotentialCollisions(body: CANNON.Body): CANNON.Body[];
    /**
     * 查询节点与物体可能碰撞的物体
     * @param node 节点
     * @param body 物体
     * @param bodyAABB 物体AABB
     * @param bodies 物体集合
     */
    private queryNodePotentialCollisions;
    /**
     * 获取所有物体
     * @returns 所有物体
     */
    getBodies(): CANNON.Body[];
    /**
     * 获取物体数量
     * @returns 物体数量
     */
    getBodyCount(): number;
    /**
     * 清空
     */
    clear(): void;
    /**
     * 销毁
     */
    dispose(): void;
    /**
     * 获取调试信息
     * @returns 调试信息
     */
    getDebugInfo(): any;
    /**
     * 获取节点信息
     * @param node 节点
     * @returns 节点信息
     */
    private getNodeInfo;
    /**
     * 获取调试网格
     * @returns 调试网格
     */
    getDebugMesh(): THREE.Object3D;
    /**
     * 分割节点
     * @param node 节点
     */
    private splitNode;
}
