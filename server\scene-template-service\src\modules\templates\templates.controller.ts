import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { TemplatesService, TemplateSearchResult, TemplateInstantiationResult } from './templates.service';
import { CreateTemplateDto, UpdateTemplateDto, TemplateQueryDto } from './dto/create-template.dto';
import { SceneTemplate } from './entities/scene-template.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../auth/entities/user.entity';

@ApiTags('templates')
@Controller('templates')
export class TemplatesController {
  constructor(private readonly templatesService: TemplatesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建场景模板' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '模板创建成功',
    type: SceneTemplate,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '未授权',
  })
  async create(
    @Body() createTemplateDto: CreateTemplateDto,
    @Request() req: any,
  ): Promise<SceneTemplate> {
    return await this.templatesService.create(createTemplateDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: '获取模板列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '模板列表获取成功',
  })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiQuery({ name: 'type', required: false, description: '模板类型' })
  @ApiQuery({ name: 'categoryId', required: false, description: '分类ID' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  async findAll(@Query() query: TemplateQueryDto): Promise<TemplateSearchResult> {
    return await this.templatesService.findAll(query);
  }

  @Get('popular')
  @ApiOperation({ summary: '获取热门模板' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '热门模板获取成功',
    type: [SceneTemplate],
  })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async getPopular(@Query('limit') limit?: number): Promise<SceneTemplate[]> {
    return await this.templatesService.getPopularTemplates(limit);
  }

  @Get('latest')
  @ApiOperation({ summary: '获取最新模板' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '最新模板获取成功',
    type: [SceneTemplate],
  })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async getLatest(@Query('limit') limit?: number): Promise<SceneTemplate[]> {
    return await this.templatesService.getLatestTemplates(limit);
  }

  @Get('featured')
  @ApiOperation({ summary: '获取精选模板' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '精选模板获取成功',
    type: [SceneTemplate],
  })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async getFeatured(@Query('limit') limit?: number): Promise<SceneTemplate[]> {
    return await this.templatesService.getFeaturedTemplates(limit);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取模板详情' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '模板详情获取成功',
    type: SceneTemplate,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '模板不存在',
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<SceneTemplate> {
    return await this.templatesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新模板' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '模板更新成功',
    type: SceneTemplate,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '模板不存在',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '无权限修改',
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTemplateDto: UpdateTemplateDto,
    @Request() req: any,
  ): Promise<SceneTemplate> {
    return await this.templatesService.update(id, updateTemplateDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除模板' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '模板删除成功',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '模板不存在',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '无权限删除',
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.templatesService.remove(id, req.user.id);
  }

  @Post(':id/instantiate')
  @ApiOperation({ summary: '实例化模板' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '模板实例化成功',
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  @ApiBody({
    description: '模板参数',
    schema: {
      type: 'object',
      properties: {
        parameters: {
          type: 'object',
          description: '参数键值对',
          example: {
            deskColor: '#8B4513',
            lightIntensity: 1.2,
            roomSize: [10, 3, 8],
          },
        },
      },
    },
  })
  async instantiate(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('parameters') parameters: Record<string, any> = {},
  ): Promise<TemplateInstantiationResult> {
    return await this.templatesService.instantiate(id, parameters);
  }

  @Post(':id/download')
  @ApiOperation({ summary: '下载模板' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '下载链接获取成功',
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  async download(@Param('id', ParseUUIDPipe) id: string): Promise<{ downloadUrl: string }> {
    // 增加下载次数
    await this.templatesService.incrementDownloadCount(id);
    
    // 这里应该生成临时下载链接
    // 实际实现中需要集成存储服务
    return {
      downloadUrl: `/api/templates/${id}/file`,
    };
  }

  @Post(':id/publish')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '发布模板' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '模板发布成功',
    type: SceneTemplate,
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  async publish(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<SceneTemplate> {
    return await this.templatesService.publish(id, req.user.id);
  }

  @Post(':id/clone')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '克隆模板' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '模板克隆成功',
    type: SceneTemplate,
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  @ApiBody({
    description: '克隆选项',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: '新模板名称',
          example: '我的办公室模板',
        },
      },
    },
  })
  async clone(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('name') name: string,
    @Request() req: any,
  ): Promise<SceneTemplate> {
    return await this.templatesService.clone(id, req.user.id, name);
  }
}
