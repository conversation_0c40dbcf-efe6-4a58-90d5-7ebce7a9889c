/**
 * 渲染服务
 */
import { Injectable, NotFoundException, ForbiddenException, BadRequestException, Inject, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { RenderJob, RenderJobStatus, RenderJobType } from './entities/render-job.entity';
import { RenderResult } from './entities/render-result.entity';
import { CreateRenderJobDto } from './dto/create-render-job.dto';

@Injectable()
export class RenderService {
  private readonly logger = new Logger(RenderService.name);

  constructor(
    @InjectRepository(RenderJob)
    private readonly renderJobRepository: Repository<RenderJob>,
    @InjectRepository(RenderResult)
    private readonly renderResultRepository: Repository<RenderResult>,
    @InjectQueue('render') private readonly renderQueue: Queue,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
  ) {}

  /**
   * 创建渲染任务
   */
  async create(userId: string, createRenderJobDto: CreateRenderJobDto): Promise<RenderJob> {
    // 验证用户是否存在
    try {
      await firstValueFrom(this.userService.send({ cmd: 'findUserById' }, userId));
    } catch (error) {
      throw new BadRequestException('用户不存在');
    }

    // 验证项目和场景是否存在，并检查权限
    try {
      await firstValueFrom(
        this.projectService.send(
          { cmd: 'checkProjectPermission' }, 
          { 
            projectId: createRenderJobDto.projectId, 
            userId, 
            roles: ['owner', 'admin', 'editor'] 
          }
        )
      );

      await firstValueFrom(
        this.projectService.send(
          { cmd: 'findSceneById' }, 
          { 
            id: createRenderJobDto.sceneId, 
            userId 
          }
        )
      );
    } catch (error) {
      throw new ForbiddenException('您没有权限在此项目中创建渲染任务或场景不存在');
    }

    // 创建渲染任务
    const renderJob = this.renderJobRepository.create({
      ...createRenderJobDto,
      userId,
      status: RenderJobStatus.PENDING,
      progress: 0,
    });

    // 保存渲染任务
    const savedJob = await this.renderJobRepository.save(renderJob);

    // 添加到渲染队列
    await this.renderQueue.add(
      'render',
      {
        jobId: savedJob.id,
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
      }
    );

    return this.findOne(savedJob.id, userId);
  }

  /**
   * 查找所有渲染任务
   */
  async findAll(userId: string, status?: RenderJobStatus, type?: RenderJobType): Promise<RenderJob[]> {
    const queryBuilder = this.renderJobRepository.createQueryBuilder('job')
      .leftJoinAndSelect('job.results', 'result')
      .where('job.userId = :userId', { userId });

    if (status) {
      queryBuilder.andWhere('job.status = :status', { status });
    }

    if (type) {
      queryBuilder.andWhere('job.type = :type', { type });
    }

    queryBuilder.orderBy('job.createdAt', 'DESC');

    return queryBuilder.getMany();
  }

  /**
   * 查找单个渲染任务
   */
  async findOne(id: string, userId: string): Promise<RenderJob> {
    const job = await this.renderJobRepository.findOne({
      where: { id },
      relations: ['results'],
    });

    if (!job) {
      throw new NotFoundException(`渲染任务ID ${id} 不存在`);
    }

    // 检查权限
    if (job.userId !== userId) {
      throw new ForbiddenException('您没有权限访问此渲染任务');
    }

    return job;
  }

  /**
   * 取消渲染任务
   */
  async cancel(id: string, userId: string): Promise<RenderJob> {
    const job = await this.findOne(id, userId);

    // 只能取消待处理或处理中的任务
    if (job.status !== RenderJobStatus.PENDING && job.status !== RenderJobStatus.PROCESSING) {
      throw new BadRequestException('只能取消待处理或处理中的任务');
    }

    // 更新任务状态
    job.status = RenderJobStatus.CANCELED;
    
    // 从队列中移除任务
    const jobsInQueue = await this.renderQueue.getJobs(['waiting', 'active', 'delayed']);
    for (const queueJob of jobsInQueue) {
      const data = await queueJob.data;
      if (data.jobId === id) {
        await queueJob.remove();
        break;
      }
    }

    return this.renderJobRepository.save(job);
  }

  /**
   * 删除渲染任务
   */
  async remove(id: string, userId: string): Promise<void> {
    const job = await this.findOne(id, userId);

    // 不能删除处理中的任务
    if (job.status === RenderJobStatus.PROCESSING) {
      throw new BadRequestException('不能删除处理中的任务');
    }

    // 从队列中移除任务
    const jobsInQueue = await this.renderQueue.getJobs(['waiting', 'active', 'delayed']);
    for (const queueJob of jobsInQueue) {
      const data = await queueJob.data;
      if (data.jobId === id) {
        await queueJob.remove();
        break;
      }
    }

    await this.renderJobRepository.remove(job);
  }

  /**
   * 更新渲染任务状态
   */
  async updateStatus(id: string, status: RenderJobStatus, progress?: number, errorMessage?: string): Promise<RenderJob> {
    const job = await this.renderJobRepository.findOne({
      where: { id },
    });

    if (!job) {
      throw new NotFoundException(`渲染任务ID ${id} 不存在`);
    }

    job.status = status;
    
    if (progress !== undefined) {
      job.progress = progress;
    }
    
    if (errorMessage) {
      job.errorMessage = errorMessage;
    }

    return this.renderJobRepository.save(job);
  }

  /**
   * 添加渲染结果
   */
  async addResult(jobId: string, resultData: Partial<RenderResult>): Promise<RenderResult> {
    const job = await this.renderJobRepository.findOne({
      where: { id: jobId },
    });

    if (!job) {
      throw new NotFoundException(`渲染任务ID ${jobId} 不存在`);
    }

    const result = this.renderResultRepository.create({
      ...resultData,
      jobId,
    });

    return this.renderResultRepository.save(result);
  }

  /**
   * 获取渲染结果
   */
  async getResult(id: string, userId: string): Promise<RenderResult> {
    const result = await this.renderResultRepository.findOne({
      where: { id },
      relations: ['job'],
    });

    if (!result) {
      throw new NotFoundException(`渲染结果ID ${id} 不存在`);
    }

    // 检查权限
    if (result.job.userId !== userId) {
      throw new ForbiddenException('您没有权限访问此渲染结果');
    }

    return result;
  }
}
