/**
 * 心跳DTO
 */
import { IsString, IsNotEmpty, IsOptional, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class HeartbeatDto {
  @ApiProperty({ description: '服务名称' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '实例ID' })
  @IsString()
  @IsNotEmpty()
  instanceId: string;

  @ApiProperty({ description: '状态信息', required: false })
  @IsObject()
  @IsOptional()
  status?: Record<string, any>;
}
