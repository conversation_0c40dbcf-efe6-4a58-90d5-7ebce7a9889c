import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ElasticsearchModule } from '@nestjs/elasticsearch';



// 模块
import { DatabaseModule } from './database/database.module';
import { CacheModule } from './cache/cache.module';
import { StorageModule } from './storage/storage.module';
import { ProcessingModule } from './processing/processing.module';
import { UploadModule } from './upload/upload.module';
import { KnowledgeBasesModule } from './modules/knowledge-bases/knowledge-bases.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { DigitalHumansModule } from './modules/digital-humans/digital-humans.module';
import { BindingsModule } from './modules/bindings/bindings.module';
import { AuthModule } from './modules/auth/auth.module';
import { HealthModule } from './modules/health/health.module';



// 配置
import productionConfig from './config/production.config';

// 服务
import { LoggerService } from './common/services/logger.service';
import { AppService } from './app.service';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      load: [productionConfig],
    }),
    

    
    // Elasticsearch模块
    ElasticsearchModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const config: any = {
          node: configService.get('ELASTICSEARCH_URL', 'http://localhost:9200'),
        };

        // 只有在用户名和密码都存在且不为空时才添加认证信息
        const username = configService.get('ELASTICSEARCH_USERNAME');
        const password = configService.get('ELASTICSEARCH_PASSWORD');

        if (username && password && username.trim() !== '' && password.trim() !== '') {
          config.auth = {
            username,
            password,
          };
        }

        return config;
      },
      inject: [ConfigService],
    }),
    
    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET', 'knowledge-service-secret'),
        signOptions: { expiresIn: '24h' },
      }),
      inject: [ConfigService],
    }),
    
    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),
    
    // 微服务客户端
    ClientsModule.registerAsync([
      {
        name: 'SERVICE_REGISTRY',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('SERVICE_REGISTRY_HOST', 'localhost'),
            port: configService.get<number>('SERVICE_REGISTRY_PORT', 3010),
          },
        }),
        inject: [ConfigService],
      },
    ]),
    
    // 核心模块
    DatabaseModule,
    CacheModule,
    StorageModule,
    ProcessingModule,
    UploadModule,
    
    // 业务模块
    KnowledgeBasesModule,
    DocumentsModule,
    DigitalHumansModule,
    BindingsModule,
    AuthModule,
    HealthModule,
  ],
  controllers: [],
  providers: [AppService, LoggerService],
  exports: [LoggerService],
})
export class AppModule {}
