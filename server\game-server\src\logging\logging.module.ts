import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EnhancedLoggerService } from './enhanced-logger.service';
import { LogAggregatorService } from './log-aggregator.service';
import { LoggingController } from './logging.controller';

@Global()
@Module({
  imports: [
    ConfigModule,
  ],
  controllers: [LoggingController],
  providers: [
    EnhancedLoggerService,
    LogAggregatorService,
  ],
  exports: [
    EnhancedLoggerService,
    LogAggregatorService,
  ],
})
export class LoggingModule {}
