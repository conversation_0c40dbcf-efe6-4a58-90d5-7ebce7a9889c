/**
 * 网络实体组件
 * 用于标识和管理网络同步的实体
 */
import { Component } from '../../core/Component';
import { NetworkEntityOwnershipMode, NetworkEntitySyncMode, NetworkEntityType } from '../NetworkEntity';
/**
 * 网络实体组件属性
 */
export interface NetworkEntityComponentProps {
    /** 实体ID */
    entityId: string;
    /** 实体类型 */
    type?: NetworkEntityType;
    /** 实体所有者ID */
    ownerId: string;
    /** 实体同步模式 */
    syncMode?: NetworkEntitySyncMode;
    /** 实体所有权模式 */
    ownershipMode?: NetworkEntityOwnershipMode;
    /** 同步间隔（毫秒） */
    syncInterval?: number;
    /** 是否自动同步 */
    autoSync?: boolean;
    /** 是否是本地拥有 */
    isLocallyOwned?: boolean;
    /** 是否可转移所有权 */
    canTransferOwnership?: boolean;
    /** 同步优先级 */
    syncPriority?: number;
    /** 同步距离 */
    syncDistance?: number;
    /** 同步组 */
    syncGroup?: string;
    /** 同步标签 */
    syncTags?: string[];
    /** 自定义数据 */
    customData?: Record<string, any>;
}
/**
 * 网络实体组件
 * 用于标识和管理网络同步的实体
 */
export declare class NetworkEntityComponent extends Component {
    /** 组件类型 */
    static readonly type = "NetworkEntity";
    /** 实体ID */
    entityId: string;
    /** 实体类型 */
    entityType: NetworkEntityType;
    /** 实体所有者ID */
    ownerId: string;
    /** 实体同步模式 */
    syncMode: NetworkEntitySyncMode;
    /** 实体所有权模式 */
    ownershipMode: NetworkEntityOwnershipMode;
    /** 同步间隔（毫秒） */
    syncInterval: number;
    /** 是否自动同步 */
    autoSync: boolean;
    /** 是否是本地拥有 */
    isLocallyOwned: boolean;
    /** 是否可转移所有权 */
    canTransferOwnership: boolean;
    /** 同步优先级 */
    syncPriority: number;
    /** 同步距离 */
    syncDistance: number;
    /** 同步组 */
    syncGroup: string;
    /** 同步标签 */
    syncTags: string[];
    /** 自定义数据 */
    customData: Record<string, any>;
    /** 上次同步时间 */
    private lastSyncTime;
    /** 是否有待同步的更改 */
    private hasPendingChanges;
    /** 待同步的属性 */
    private pendingProperties;
    /**
     * 创建网络实体组件
     * @param props 组件属性
     */
    constructor(props: NetworkEntityComponentProps);
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 同步实体
     */
    sync(): void;
    /**
     * 获取同步数据
     * @returns 同步数据
     */
    getSyncData(): any;
    /**
     * 应用同步数据
     * @param data 同步数据
     */
    applySyncData(data: any): void;
    /**
     * 标记属性为待同步
     * @param property 属性名
     */
    markPropertyDirty(property: string): void;
    /**
     * 标记所有属性为待同步
     */
    markAllPropertiesDirty(): void;
    /**
     * 请求所有权
     * @returns 是否成功
     */
    requestOwnership(): boolean;
    /**
     * 转移所有权
     * @param newOwnerId 新所有者ID
     * @returns 是否成功
     */
    transferOwnership(newOwnerId: string): boolean;
    /**
     * 设置所有者
     * @param ownerId 所有者ID
     * @param isLocallyOwned 是否是本地拥有
     */
    setOwner(ownerId: string, isLocallyOwned: boolean): void;
    /**
     * 销毁组件
     */
    dispose(): void;
}
