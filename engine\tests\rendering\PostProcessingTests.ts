/**
 * 后处理效果测试
 * 用于测试渲染系统的后处理效果
 */
import * as THREE from 'three';
import { RenderTestFramework, RenderTestSuite, TestScene } from './RenderTestFramework';
import { Scene } from '../../src/scene/Scene';
import { Camera, CameraType } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { Transform } from '../../src/scene/Transform';
import { Entity } from '../../src/core/Entity';
import { 
  PostProcessingSystem,
  BloomEffect,
  SSAOEffect,
  SSREffect,
  ToneMappingEffect,
  ToneMappingType,
  FXAAEffect
} from '../../src/rendering/postprocessing';

/**
 * 创建后处理测试套件
 * @returns 后处理测试套件
 */
export function createPostProcessingTestSuite(): RenderTestSuite {
  return {
    name: '后处理效果测试套件',
    scenes: [
      createBloomTest(),
      createSSAOTest(),
      createSSRTest(),
      createToneMappingTest(),
      createFXAATest(),
      createMultipleEffectsTest()
    ],
    rendererOptions: {
      width: 800,
      height: 600,
      antialias: true,
      shadows: true
    },
    renderSystemOptions: {
      enableShadows: true,
      enablePostProcessing: true
    }
  };
}

/**
 * 创建基础场景
 * @returns 基础场景
 */
function createBaseScene(): Scene {
  // 创建场景
  const scene = new Scene();
  
  // 创建地面
  const groundEntity = new Entity('地面');
  groundEntity.addComponent(new Transform({
    position: { x: 0, y: -1, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    scale: { x: 10, y: 0.1, z: 10 }
  }));
  
  // 创建地面几何体和材质
  const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
  const groundMaterial = new THREE.MeshStandardMaterial({
    color: 0x808080,
    roughness: 0.7,
    metalness: 0.0
  });
  
  // 创建地面网格
  const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
  groundMesh.castShadow = false;
  groundMesh.receiveShadow = true;
  
  // 添加网格到变换组件
  const groundTransform = groundEntity.getComponent('Transform') as Transform;
  groundTransform.getObject3D().add(groundMesh);
  
  // 添加地面实体到场景
  scene.addEntity(groundEntity);
  
  // 创建不同形状的物体
  const shapes = [
    { name: '立方体', geometry: new THREE.BoxGeometry(1, 1, 1), position: { x: -2, y: 0.5, z: 0 }, color: 0xff0000 },
    { name: '球体', geometry: new THREE.SphereGeometry(0.5, 32, 32), position: { x: 0, y: 0.5, z: 0 }, color: 0x00ff00 },
    { name: '圆柱体', geometry: new THREE.CylinderGeometry(0.5, 0.5, 1, 32), position: { x: 2, y: 0.5, z: 0 }, color: 0x0000ff }
  ];
  
  // 创建物体实体
  for (const shape of shapes) {
    const entity = new Entity(shape.name);
    entity.addComponent(new Transform({
      position: shape.position,
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    
    // 创建材质
    const material = new THREE.MeshStandardMaterial({
      color: shape.color,
      roughness: 0.5,
      metalness: 0.5,
      emissive: shape.color,
      emissiveIntensity: 0.2
    });
    
    // 创建网格
    const mesh = new THREE.Mesh(shape.geometry, material);
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    
    // 添加网格到变换组件
    const transform = entity.getComponent('Transform') as Transform;
    transform.getObject3D().add(mesh);
    
    // 添加实体到场景
    scene.addEntity(entity);
  }
  
  // 创建方向光
  const directionalLightEntity = new Entity('方向光');
  directionalLightEntity.addComponent(new Transform({
    position: { x: 5, y: 5, z: 5 },
    rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
    scale: { x: 1, y: 1, z: 1 }
  }));
  
  // 创建方向光组件
  const directionalLight = new Light({
    type: LightType.DIRECTIONAL,
    color: 0xffffff,
    intensity: 1.0,
    castShadow: true
  });
  directionalLightEntity.addComponent(directionalLight);
  
  // 添加方向光实体到场景
  scene.addEntity(directionalLightEntity);
  
  // 创建环境光
  const ambientLightEntity = new Entity('环境光');
  
  // 创建环境光组件
  const ambientLight = new Light({
    type: LightType.AMBIENT,
    color: 0x404040,
    intensity: 0.5
  });
  ambientLightEntity.addComponent(ambientLight);
  
  // 添加环境光实体到场景
  scene.addEntity(ambientLightEntity);
  
  return scene;
}

/**
 * 创建基础相机
 * @returns 基础相机
 */
function createBaseCamera(): Camera {
  // 创建相机
  const camera = new Camera({
    type: CameraType.PERSPECTIVE,
    fov: 75,
    aspect: 800 / 600,
    near: 0.1,
    far: 1000,
    position: { x: 5, y: 5, z: 5 },
    lookAt: { x: 0, y: 0, z: 0 }
  });
  
  return camera;
}

/**
 * 创建泛光效果测试
 * @returns 泛光效果测试
 */
function createBloomTest(): TestScene {
  return {
    name: '泛光效果测试',
    createScene: () => {
      const scene = createBaseScene();
      
      // 创建后处理系统实体
      const postProcessingEntity = new Entity('后处理系统');
      
      // 创建后处理系统
      const postProcessingSystem = new PostProcessingSystem({
        enabled: true,
        autoResize: true
      });
      
      // 创建泛光效果
      const bloomEffect = new BloomEffect({
        name: 'Bloom',
        enabled: true,
        intensity: 1.5,
        threshold: 0.6,
        radius: 0.5
      });
      
      // 添加效果到后处理系统
      postProcessingSystem.addEffect(bloomEffect);
      
      // 添加后处理系统到实体
      postProcessingEntity.addComponent(postProcessingSystem);
      
      // 添加后处理系统实体到场景
      scene.addEntity(postProcessingEntity);
      
      return scene;
    },
    createCamera: createBaseCamera
  };
}

/**
 * 创建SSAO效果测试
 * @returns SSAO效果测试
 */
function createSSAOTest(): TestScene {
  return {
    name: 'SSAO效果测试',
    createScene: () => {
      const scene = createBaseScene();
      
      // 创建后处理系统实体
      const postProcessingEntity = new Entity('后处理系统');
      
      // 创建后处理系统
      const postProcessingSystem = new PostProcessingSystem({
        enabled: true,
        autoResize: true
      });
      
      // 创建SSAO效果
      const ssaoEffect = new SSAOEffect({
        name: 'SSAO',
        enabled: true,
        radius: 0.1825,
        bias: 0.025,
        intensity: 1.0,
        samples: 16,
        kernelRadius: 16
      });
      
      // 添加效果到后处理系统
      postProcessingSystem.addEffect(ssaoEffect);
      
      // 添加后处理系统到实体
      postProcessingEntity.addComponent(postProcessingSystem);
      
      // 添加后处理系统实体到场景
      scene.addEntity(postProcessingEntity);
      
      return scene;
    },
    createCamera: createBaseCamera
  };
}

/**
 * 创建SSR效果测试
 * @returns SSR效果测试
 */
function createSSRTest(): TestScene {
  return {
    name: 'SSR效果测试',
    createScene: () => {
      const scene = createBaseScene();
      
      // 创建后处理系统实体
      const postProcessingEntity = new Entity('后处理系统');
      
      // 创建后处理系统
      const postProcessingSystem = new PostProcessingSystem({
        enabled: true,
        autoResize: true
      });
      
      // 创建SSR效果
      const ssrEffect = new SSREffect({
        name: 'SSR',
        enabled: true,
        intensity: 0.5,
        maxSteps: 20,
        stride: 1,
        refineSteps: 5,
        thickness: 0.5,
        maxDistance: 50
      });
      
      // 添加效果到后处理系统
      postProcessingSystem.addEffect(ssrEffect);
      
      // 添加后处理系统到实体
      postProcessingEntity.addComponent(postProcessingSystem);
      
      // 添加后处理系统实体到场景
      scene.addEntity(postProcessingEntity);
      
      return scene;
    },
    createCamera: createBaseCamera
  };
}

/**
 * 创建色调映射效果测试
 * @returns 色调映射效果测试
 */
function createToneMappingTest(): TestScene {
  return {
    name: '色调映射效果测试',
    createScene: () => {
      const scene = createBaseScene();
      
      // 创建后处理系统实体
      const postProcessingEntity = new Entity('后处理系统');
      
      // 创建后处理系统
      const postProcessingSystem = new PostProcessingSystem({
        enabled: true,
        autoResize: true
      });
      
      // 创建色调映射效果
      const toneMappingEffect = new ToneMappingEffect({
        name: 'ToneMapping',
        enabled: true,
        type: ToneMappingType.ACES_FILMIC,
        exposure: 1.0
      });
      
      // 添加效果到后处理系统
      postProcessingSystem.addEffect(toneMappingEffect);
      
      // 添加后处理系统到实体
      postProcessingEntity.addComponent(postProcessingSystem);
      
      // 添加后处理系统实体到场景
      scene.addEntity(postProcessingEntity);
      
      return scene;
    },
    createCamera: createBaseCamera
  };
}

/**
 * 创建FXAA效果测试
 * @returns FXAA效果测试
 */
function createFXAATest(): TestScene {
  return {
    name: 'FXAA效果测试',
    createScene: () => {
      const scene = createBaseScene();
      
      // 创建后处理系统实体
      const postProcessingEntity = new Entity('后处理系统');
      
      // 创建后处理系统
      const postProcessingSystem = new PostProcessingSystem({
        enabled: true,
        autoResize: true
      });
      
      // 创建FXAA效果
      const fxaaEffect = new FXAAEffect({
        name: 'FXAA',
        enabled: true
      });
      
      // 添加效果到后处理系统
      postProcessingSystem.addEffect(fxaaEffect);
      
      // 添加后处理系统到实体
      postProcessingEntity.addComponent(postProcessingSystem);
      
      // 添加后处理系统实体到场景
      scene.addEntity(postProcessingEntity);
      
      return scene;
    },
    createCamera: createBaseCamera
  };
}

/**
 * 创建多效果组合测试
 * @returns 多效果组合测试
 */
function createMultipleEffectsTest(): TestScene {
  return {
    name: '多效果组合测试',
    createScene: () => {
      const scene = createBaseScene();
      
      // 创建后处理系统实体
      const postProcessingEntity = new Entity('后处理系统');
      
      // 创建后处理系统
      const postProcessingSystem = new PostProcessingSystem({
        enabled: true,
        autoResize: true
      });
      
      // 创建FXAA效果
      const fxaaEffect = new FXAAEffect({
        name: 'FXAA',
        enabled: true
      });
      
      // 创建泛光效果
      const bloomEffect = new BloomEffect({
        name: 'Bloom',
        enabled: true,
        intensity: 1.0,
        threshold: 0.7,
        radius: 0.4
      });
      
      // 创建色调映射效果
      const toneMappingEffect = new ToneMappingEffect({
        name: 'ToneMapping',
        enabled: true,
        type: ToneMappingType.ACES_FILMIC,
        exposure: 1.0
      });
      
      // 添加效果到后处理系统
      postProcessingSystem.addEffect(fxaaEffect);
      postProcessingSystem.addEffect(bloomEffect);
      postProcessingSystem.addEffect(toneMappingEffect);
      
      // 添加后处理系统到实体
      postProcessingEntity.addComponent(postProcessingSystem);
      
      // 添加后处理系统实体到场景
      scene.addEntity(postProcessingEntity);
      
      return scene;
    },
    createCamera: createBaseCamera
  };
}

/**
 * 运行后处理测试
 */
export async function runPostProcessingTests(): Promise<void> {
  // 创建测试框架
  const testFramework = new RenderTestFramework(
    {
      width: 800,
      height: 600,
      antialias: true,
      shadows: true
    },
    {
      enableShadows: true,
      enablePostProcessing: true
    }
  );
  
  // 创建测试套件
  const testSuite = createPostProcessingTestSuite();
  
  // 运行测试套件
  const results = await testFramework.runTestSuite(testSuite);
  
  // 输出测试结果
  console.log('测试结果:', results);
}
