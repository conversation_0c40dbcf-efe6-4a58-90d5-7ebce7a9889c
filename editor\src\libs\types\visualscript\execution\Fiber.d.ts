/**
 * 视觉脚本执行纤程
 * 负责执行视觉脚本的一条执行路径
 */
import { Node } from '../nodes/Node';
import { VisualScriptEngine } from '../VisualScriptEngine';
/**
 * 纤程执行步骤结果
 */
export interface FiberStepResult {
    /** 是否已完成 */
    completed: boolean;
    /** 是否需要暂停 */
    pause: boolean;
    /** 执行的节点 */
    node?: Node;
    /** 执行结果 */
    result?: any;
    /** 错误信息 */
    error?: any;
}
/**
 * 纤程选项
 */
export interface FiberOptions {
    /** 视觉脚本引擎 */
    engine: VisualScriptEngine;
    /** 源节点 */
    sourceNode: Node;
    /** 输出名称 */
    outputName: string;
    /** 完成回调 */
    callback?: () => void;
}
/**
 * 视觉脚本执行纤程
 * 负责执行视觉脚本的一条执行路径
 */
export declare class Fiber {
    /** 视觉脚本引擎 */
    private engine;
    /** 当前节点 */
    private currentNode;
    /** 当前输出名称 */
    private currentOutputName;
    /** 执行步数 */
    private steps;
    /** 最大执行步数 */
    private maxSteps;
    /** 开始时间 */
    private startTime;
    /** 最大执行时间（毫秒） */
    private maxTime;
    /** 是否已完成 */
    private completed;
    /** 是否已暂停 */
    private paused;
    /** 完成回调 */
    private callback?;
    /**
     * 创建纤程
     * @param options 纤程选项
     */
    constructor(options: FiberOptions);
    /**
     * 执行一步
     * @returns 执行结果
     */
    executeStep(): FiberStepResult;
    /**
     * 获取输出连接
     * @returns 输出连接列表
     */
    private getOutputConnections;
    /**
     * 暂停执行
     */
    pause(): void;
    /**
     * 恢复执行
     */
    resume(): void;
    /**
     * 重置执行状态
     */
    reset(): void;
    /**
     * 获取当前节点
     * @returns 当前节点
     */
    getCurrentNode(): Node;
    /**
     * 获取当前输出名称
     * @returns 当前输出名称
     */
    getCurrentOutputName(): string;
    /**
     * 获取执行步数
     * @returns 执行步数
     */
    getSteps(): number;
    /**
     * 获取执行时间（毫秒）
     * @returns 执行时间
     */
    getExecutionTime(): number;
    /**
     * 是否已完成
     * @returns 是否已完成
     */
    isCompleted(): boolean;
    /**
     * 是否已暂停
     * @returns 是否已暂停
     */
    isPaused(): boolean;
    /**
     * 设置最大执行步数
     * @param steps 最大执行步数
     */
    setMaxSteps(steps: number): void;
    /**
     * 设置最大执行时间（毫秒）
     * @param time 最大执行时间
     */
    setMaxTime(time: number): void;
}
