/**
 * 批量地形导入导出服务
 * 用于批量处理地形数据的导入和导出
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';
import { HeightMapExportOptions, HeightMapImportOptions } from './HeightMapImportExport';
import { TerrainExportOptions, TerrainImportOptions } from './TerrainImportExportService';
import { ThirdPartyTerrainExportOptions, ThirdPartyTerrainFormat, ThirdPartyTerrainImportOptions } from './ThirdPartyTerrainImportExport';
import { Debug } from '../../utils/Debug';

/**
 * 批量导入结果
 */
export interface BatchImportResult {
  /** 文件名 */
  fileName: string;
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
}

/**
 * 批量导出结果
 */
export interface BatchExportResult {
  /** 文件名 */
  fileName: string;
  /** 导出数据 */
  data: Blob | string;
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
}

/**
 * 批量地形导入导出服务
 */
export class BatchTerrainImportExport {
  /**
   * 批量导入高度图
   * @param terrain 地形组件
   * @param files 文件列表
   * @param options 导入选项
   * @returns Promise，解析为导入结果列表
   */
  public async batchImportHeightMaps(
    terrain: TerrainComponent,
    files: File[],
    options: HeightMapImportOptions = {}
  ): Promise<BatchImportResult[]> {
    const results: BatchImportResult[] = [];

    // 保存原始地形数据
    const originalHeightData = terrain.heightData.slice();
    const originalResolution = terrain.resolution;

    // 处理每个文件
    for (const file of files) {
      try {
        // 导入高度图
        const success = await terrain.importFromHeightMap(file, options);

        // 记录结果
        results.push({
          fileName: file.name,
          success
        });

        // 如果导入失败，恢复原始数据
        if (!success) {
          terrain.heightData = originalHeightData.slice();
          terrain.resolution = originalResolution;
          terrain.needsUpdate = true;
          terrain.needsPhysicsUpdate = true;
        }
      } catch (error) {
        // 记录错误
        results.push({
          fileName: file.name,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });

        // 恢复原始数据
        terrain.heightData = originalHeightData.slice();
        terrain.resolution = originalResolution;
        terrain.needsUpdate = true;
        terrain.needsPhysicsUpdate = true;
      }
    }

    return results;
  }

  /**
   * 批量导入JSON
   * @param terrain 地形组件
   * @param files 文件列表
   * @param options 导入选项
   * @returns Promise，解析为导入结果列表
   */
  public async batchImportJSON(
    terrain: TerrainComponent,
    files: File[],
    options: TerrainImportOptions = {}
  ): Promise<BatchImportResult[]> {
    const results: BatchImportResult[] = [];

    // 保存原始地形数据
    const originalData = terrain.exportToJSON();

    // 处理每个文件
    for (const file of files) {
      try {
        // 读取文件
        const json = await this.readFileAsText(file);

        // 导入JSON
        const success = terrain.importFromJSON(json, options);

        // 记录结果
        results.push({
          fileName: file.name,
          success
        });

        // 如果导入失败，恢复原始数据
        if (!success) {
          terrain.importFromJSON(originalData);
        }
      } catch (error) {
        // 记录错误
        results.push({
          fileName: file.name,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });

        // 恢复原始数据
        terrain.importFromJSON(originalData);
      }
    }

    return results;
  }

  /**
   * 批量导入第三方格式
   * @param terrain 地形组件
   * @param files 文件列表
   * @param format 格式
   * @param options 导入选项
   * @returns Promise，解析为导入结果列表
   */
  public async batchImportThirdPartyFormat(
    terrain: TerrainComponent,
    files: File[],
    format: ThirdPartyTerrainFormat,
    options: Omit<ThirdPartyTerrainImportOptions, 'format'> = {}
  ): Promise<BatchImportResult[]> {
    const results: BatchImportResult[] = [];

    // 保存原始地形数据
    const originalData = terrain.exportToJSON();

    // 处理每个文件
    for (const file of files) {
      try {
        // 读取文件
        const data = await this.readFileAsArrayBuffer(file);

        // 导入第三方格式
        const success = await terrain.importFromThirdPartyFormat(format, data, options);

        // 记录结果
        results.push({
          fileName: file.name,
          success
        });

        // 如果导入失败，恢复原始数据
        if (!success) {
          terrain.importFromJSON(originalData);
        }
      } catch (error) {
        // 记录错误
        results.push({
          fileName: file.name,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });

        // 恢复原始数据
        terrain.importFromJSON(originalData);
      }
    }

    return results;
  }

  /**
   * 读取文件为文本
   * @param file 文件
   * @returns Promise，解析为文本内容
   */
  private readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = (e) => {
        reject(e);
      };
      reader.readAsText(file);
    });
  }

  /**
   * 读取文件为ArrayBuffer
   * @param file 文件
   * @returns Promise，解析为ArrayBuffer
   */
  private readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as ArrayBuffer);
      };
      reader.onerror = (e) => {
        reject(e);
      };
      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * 批量导出高度图
   * @param terrain 地形组件
   * @param formats 格式列表
   * @param options 导出选项
   * @returns Promise，解析为导出结果列表
   */
  public async batchExportHeightMaps(
    terrain: TerrainComponent,
    formats: HeightMapExportOptions[],
    baseFileName: string = 'terrain_heightmap'
  ): Promise<BatchExportResult[]> {
    const results: BatchExportResult[] = [];

    // 处理每个格式
    for (const options of formats) {
      try {
        // 导出高度图
        const blob = await terrain.exportToHeightMap(options);

        // 生成文件名
        const fileName = `${baseFileName}_${Date.now()}.${options.format.toLowerCase()}`;

        // 记录结果
        results.push({
          fileName,
          data: blob,
          success: true
        });
      } catch (error) {
        // 记录错误
        results.push({
          fileName: `${baseFileName}_${Date.now()}.${options.format.toLowerCase()}`,
          data: new Blob(),
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return results;
  }

  /**
   * 批量导出JSON
   * @param terrain 地形组件
   * @param options 导出选项列表
   * @param baseFileName 基础文件名
   * @returns 导出结果列表
   */
  public batchExportJSON(
    terrain: TerrainComponent,
    options: TerrainExportOptions[],
    baseFileName: string = 'terrain_data'
  ): BatchExportResult[] {
    const results: BatchExportResult[] = [];

    // 处理每个选项
    for (const opt of options) {
      try {
        // 导出JSON
        const json = terrain.exportToJSON(opt);

        // 生成文件名
        const fileName = `${baseFileName}_${Date.now()}.json`;

        // 记录结果
        results.push({
          fileName,
          data: json,
          success: true
        });
      } catch (error) {
        // 记录错误
        results.push({
          fileName: `${baseFileName}_${Date.now()}.json`,
          data: '',
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return results;
  }

  /**
   * 批量导出第三方格式
   * @param terrain 地形组件
   * @param formats 格式和选项列表
   * @param baseFileName 基础文件名
   * @returns Promise，解析为导出结果列表
   */
  public async batchExportThirdPartyFormat(
    terrain: TerrainComponent,
    formats: { format: ThirdPartyTerrainFormat; options?: Omit<ThirdPartyTerrainExportOptions, 'format'> }[],
    baseFileName: string = 'terrain'
  ): Promise<BatchExportResult[]> {
    const results: BatchExportResult[] = [];

    // 处理每个格式
    for (const { format, options } of formats) {
      try {
        // 导出第三方格式
        const data = await terrain.exportToThirdPartyFormat(format, options);

        // 生成文件名
        const fileName = `${baseFileName}_${format.toLowerCase()}_${Date.now()}.${this.getFileExtension(format)}`;

        // 记录结果
        results.push({
          fileName,
          data: data instanceof ArrayBuffer ? new Blob([data]) : data,
          success: true
        });
      } catch (error) {
        // 记录错误
        results.push({
          fileName: `${baseFileName}_${format.toLowerCase()}_${Date.now()}.${this.getFileExtension(format)}`,
          data: '',
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return results;
  }

  /**
   * 获取文件扩展名
   * @param format 格式
   * @returns 文件扩展名
   */
  private getFileExtension(format: ThirdPartyTerrainFormat): string {
    switch (format) {
      case ThirdPartyTerrainFormat.UNITY:
        return 'json';
      case ThirdPartyTerrainFormat.UNREAL:
        return 'json';
      case ThirdPartyTerrainFormat.WORLD_MACHINE:
        return 'tmd';
      case ThirdPartyTerrainFormat.TERRAGEN:
        return 'ter';
      case ThirdPartyTerrainFormat.L3DT:
        return 'l3dt';
      case ThirdPartyTerrainFormat.GEOTIFF:
        return 'tif';
      case ThirdPartyTerrainFormat.USGS_DEM:
        return 'dem';
      case ThirdPartyTerrainFormat.SRTM:
        return 'hgt';
      default:
        return 'dat';
    }
  }
}
