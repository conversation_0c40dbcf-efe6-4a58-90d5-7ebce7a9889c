/**
 * 动作捕捉系统
 * 用于处理动作捕捉数据并应用到角色骨骼上
 */
import { System } from '../core/System';
import type { World } from '../core/World';
import type { Entity } from '../core/Entity';
import { MotionCaptureComponent } from './components/MotionCaptureComponent';
import { MotionCapturePoseComponent } from './components/MotionCapturePoseComponent';
import { LandmarkData, WorldLandmarkData } from './types/LandmarkData';
/**
 * 动作捕捉系统配置
 */
export interface MotionCaptureSystemConfig {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 是否启用网络同步 */
    enableNetworkSync?: boolean;
    /** 是否启用姿势评估 */
    enablePoseEvaluation?: boolean;
    /** 平滑系数 */
    smoothingFactor?: number;
    /** 可见度阈值 */
    visibilityThreshold?: number;
}
/**
 * 动作捕捉结果
 */
export interface MotionCaptureResults {
    /** 世界坐标系中的关键点数据 */
    worldLandmarks: WorldLandmarkData[];
    /** 屏幕坐标系中的关键点数据 */
    landmarks: LandmarkData[];
}
/**
 * 动作捕捉系统
 * 处理动作捕捉数据并应用到角色骨骼上
 */
export declare class MotionCaptureSystem extends System {
    /** 系统名称 */
    static readonly NAME = "MotionCaptureSystem";
    /** 系统配置 */
    private config;
    /** 网络系统引用 */
    private networkSystem;
    /** 动作捕捉组件映射 */
    private motionCaptureComponents;
    /** 姿势组件映射 */
    private poseComponents;
    /** 事件发射器 */
    private eventEmitter;
    /** 动作捕捉数据缓存 */
    private captureDataCache;
    /** 最后一次数据接收时间 */
    private lastDataReceiveTime;
    /** 数据通道类型 */
    static readonly DATA_CHANNEL_TYPE = "mocap.dataChannel";
    /**
     * 构造函数
     * @param world 世界引用
     * @param config 系统配置
     */
    constructor(world: World, config?: MotionCaptureSystemConfig);
    /**
     * 注册网络处理器
     * TODO: 网络功能暂时禁用
     * @private
     * @unused 此方法暂时未使用，等待网络系统完善后启用
     */
    private registerNetworkHandlers;
    /**
     * 处理动作捕捉数据
     * @param fromPeerId 发送者ID
     * @param message 消息数据
     * @private
     * @unused 此方法暂时未使用，等待网络系统完善后启用
     */
    private handleMotionCaptureData;
    /**
     * 解码动作捕捉数据
     * @param data 二进制数据
     * @returns 解码后的数据
     */
    private decodeMotionCaptureData;
    /**
     * 编码动作捕捉数据
     * @param results 动作捕捉结果
     * @returns 编码后的二进制数据
     */
    encodeMotionCaptureData(results: MotionCaptureResults): ArrayBuffer;
    /**
     * 发送动作捕捉结果
     * @param results 动作捕捉结果
     * TODO: 网络发送功能暂时禁用
     */
    sendMotionCaptureResults(results: MotionCaptureResults): void;
    /**
     * 注册动作捕捉组件
     * @param entity 实体
     * @param component 动作捕捉组件
     */
    registerMotionCaptureComponent(entity: Entity, component: MotionCaptureComponent): void;
    /**
     * 注册姿势组件
     * @param entity 实体
     * @param component 姿势组件
     */
    registerPoseComponent(entity: Entity, component: MotionCapturePoseComponent): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 清理过期数据
     */
    private cleanupExpiredData;
    /**
     * 处理动作捕捉数据
     */
    private processMotionCaptureData;
    /**
     * 解算实体姿势
     * @param entity 实体
     * @param captureData 捕捉数据
     */
    private solveEntityPose;
    /**
     * 评估姿势
     */
    private evaluatePoses;
    /**
     * 销毁系统
     */
    dispose(): void;
}
