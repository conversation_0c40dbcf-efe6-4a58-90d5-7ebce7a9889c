#!/usr/bin/env node

/**
 * 验证editor项目中的导入是否正确使用了src/libs库
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 递归查找所有 .tsx 和 .ts 文件
function findAllFiles(dir, extensions = ['.tsx', '.ts']) {
  let results = [];
  try {
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
      const filePath = path.join(dir, file);
      try {
        const stat = fs.statSync(filePath);
        
        if (stat && stat.isDirectory()) {
          // 跳过不需要的目录
          if (!['node_modules', '.git', 'dist', 'build', 'coverage', '__tests__'].includes(file)) {
            results = results.concat(findAllFiles(filePath, extensions));
          }
        } else {
          const ext = path.extname(file);
          if (extensions.includes(ext)) {
            results.push(filePath);
          }
        }
      } catch (err) {
        // 忽略无法访问的文件
      }
    });
  } catch (err) {
    // 忽略无法访问的目录
  }
  
  return results;
}

// 检查文件中的导入问题
function checkFileImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 检查是否还有engine源代码导入
    const engineImports = content.match(/import\s*[^'"]*from\s*['"][^'"]*engine\/src\/[^'"]*['"]/g);
    if (engineImports) {
      issues.push({
        type: 'engine-source',
        imports: engineImports,
        message: '仍然从engine源代码导入'
      });
    }
    
    // 检查是否有dl-engine-core导入
    const coreImports = content.match(/import\s*[^'"]*from\s*['"][^'"]*dl-engine-core[^'"]*['"]/g);
    if (coreImports) {
      issues.push({
        type: 'dl-engine-core',
        imports: coreImports,
        message: '仍然从dl-engine-core导入'
      });
    }
    
    // 检查是否有.mjs导入
    const mjsImports = content.match(/import\s*[^'"]*from\s*['"][^'"]*\.mjs['"]/g);
    if (mjsImports) {
      issues.push({
        type: 'mjs-import',
        imports: mjsImports,
        message: '仍然使用.mjs导入'
      });
    }
    
    // 检查是否有直接的dl-engine导入（不带路径）
    const directImports = content.match(/import\s*[^'"]*from\s*['"]dl-engine['"]/g);
    if (directImports) {
      issues.push({
        type: 'direct-import',
        imports: directImports,
        message: '使用直接dl-engine导入（应该使用相对路径）'
      });
    }
    
    // 检查正确的libs导入
    const libsImports = content.match(/import\s*[^'"]*from\s*['"][^'"]*libs\/dl-engine['"]/g);
    
    return {
      issues,
      hasLibsImports: !!libsImports,
      libsImports: libsImports || []
    };
    
  } catch (error) {
    return {
      issues: [{
        type: 'file-error',
        message: `无法读取文件: ${error.message}`
      }],
      hasLibsImports: false,
      libsImports: []
    };
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src 目录不存在');
    process.exit(1);
  }
  
  console.log('🔍 验证editor项目中的导入...');
  console.log('📁 扫描目录:', srcDir);
  
  const files = findAllFiles(srcDir);
  let totalFiles = files.length;
  let filesWithIssues = 0;
  let filesWithLibsImports = 0;
  let totalIssues = 0;
  
  console.log(`📄 找到 ${totalFiles} 个文件需要检查`);
  console.log('');
  
  files.forEach((file, index) => {
    const relativePath = path.relative(__dirname, file);
    const result = checkFileImports(file);
    
    if (result.issues.length > 0) {
      filesWithIssues++;
      totalIssues += result.issues.length;
      
      console.log(`❌ ${relativePath}:`);
      result.issues.forEach(issue => {
        console.log(`   ${issue.message}`);
        if (issue.imports) {
          issue.imports.forEach(imp => {
            console.log(`     - ${imp}`);
          });
        }
      });
      console.log('');
    }
    
    if (result.hasLibsImports) {
      filesWithLibsImports++;
    }
  });
  
  console.log('📊 验证结果:');
  console.log(`   总文件数: ${totalFiles}`);
  console.log(`   有问题的文件: ${filesWithIssues}`);
  console.log(`   总问题数: ${totalIssues}`);
  console.log(`   使用libs导入的文件: ${filesWithLibsImports}`);
  
  if (filesWithIssues === 0) {
    console.log('');
    console.log('✅ 所有导入都已正确修复！');
    console.log('🎉 editor项目现在完全依赖src/libs库而不是engine源代码');
  } else {
    console.log('');
    console.log('⚠️  仍有一些导入需要修复');
    console.log('请检查上述问题并手动修复');
  }
}

// 运行主函数
main();
