/**
 * 地形片段着色器
 */
export declare const terrainFragmentShader = "\n// \u57FA\u7840\u53C2\u6570\nuniform vec2 uTerrainSize;\nuniform float uMaxHeight;\n\n// \u7EB9\u7406\u53C2\u6570\nuniform int uLayerCount;\nuniform sampler2D uTextures[8];\nuniform sampler2D uNormalMaps[8];\nuniform sampler2D uRoughnessMaps[8];\nuniform sampler2D uDisplacementMaps[8];\nuniform sampler2D uAOMaps[8];\nuniform float uTilingFactors[8];\nuniform float uMinHeights[8];\nuniform float uMaxHeights[8];\nuniform float uMinSlopes[8];\nuniform float uMaxSlopes[8];\n\n// \u6DF7\u5408\u53C2\u6570\nuniform sampler2D uBlendMaps[8];\nuniform bool uUseBlendMaps;\n\n// \u5149\u7167\u53C2\u6570\nuniform vec3 uLightPosition;\nuniform vec3 uLightColor;\nuniform vec3 uAmbientColor;\n\n// \u96FE\u53C2\u6570\nuniform vec3 uFogColor;\nuniform float uFogNear;\nuniform float uFogFar;\nuniform bool uUseFog;\n\n// \u4ECE\u9876\u70B9\u7740\u8272\u5668\u4F20\u9012\u7684\u53D8\u91CF\nvarying vec2 vUv;\nvarying vec3 vNormal;\nvarying vec3 vPosition;\nvarying vec3 vWorldPosition;\nvarying vec4 vTangent;\nvarying float vHeight;\nvarying float vSlope;\nvarying float vFogFactor;\n\n// \u8BA1\u7B97\u6DF7\u5408\u6743\u91CD\nfloat calculateBlendWeight(float height, float slope, float minHeight, float maxHeight, float minSlope, float maxSlope) {\n  // \u9AD8\u5EA6\u6DF7\u5408\u6743\u91CD\n  float heightWeight = 1.0;\n  if (height < minHeight || height > maxHeight) {\n    heightWeight = 0.0;\n  } else {\n    // \u5E73\u6ED1\u8FC7\u6E21\n    float heightRange = maxHeight - minHeight;\n    if (heightRange > 0.0) {\n      float heightTransition = 0.1 * heightRange;\n      if (height < minHeight + heightTransition) {\n        heightWeight = smoothstep(minHeight, minHeight + heightTransition, height);\n      } else if (height > maxHeight - heightTransition) {\n        heightWeight = 1.0 - smoothstep(maxHeight - heightTransition, maxHeight, height);\n      }\n    }\n  }\n  \n  // \u659C\u5EA6\u6DF7\u5408\u6743\u91CD\n  float slopeWeight = 1.0;\n  if (slope < minSlope || slope > maxSlope) {\n    slopeWeight = 0.0;\n  } else {\n    // \u5E73\u6ED1\u8FC7\u6E21\n    float slopeRange = maxSlope - minSlope;\n    if (slopeRange > 0.0) {\n      float slopeTransition = 0.1 * slopeRange;\n      if (slope < minSlope + slopeTransition) {\n        slopeWeight = smoothstep(minSlope, minSlope + slopeTransition, slope);\n      } else if (slope > maxSlope - slopeTransition) {\n        slopeWeight = 1.0 - smoothstep(maxSlope - slopeTransition, maxSlope, slope);\n      }\n    }\n  }\n  \n  return heightWeight * slopeWeight;\n}\n\n// \u8BA1\u7B97\u6CD5\u7EBF\u8D34\u56FE\nvec3 calculateNormalFromMap(sampler2D normalMap, vec2 uv, vec3 normal, vec4 tangent) {\n  // \u4ECE\u6CD5\u7EBF\u8D34\u56FE\u83B7\u53D6\u6CD5\u7EBF\n  vec3 normalColor = texture2D(normalMap, uv).rgb;\n  vec3 normalMapValue = normalColor * 2.0 - 1.0;\n  \n  // \u8BA1\u7B97\u5207\u7EBF\u7A7A\u95F4\u5230\u4E16\u754C\u7A7A\u95F4\u7684\u53D8\u6362\n  vec3 tangentValue = tangent.xyz;\n  vec3 bitangent = cross(normal, tangentValue) * tangent.w;\n  mat3 tbn = mat3(tangentValue, bitangent, normal);\n  \n  // \u5C06\u6CD5\u7EBF\u4ECE\u5207\u7EBF\u7A7A\u95F4\u8F6C\u6362\u5230\u4E16\u754C\u7A7A\u95F4\n  return normalize(tbn * normalMapValue);\n}\n\nvoid main() {\n  // \u521D\u59CB\u5316\u989C\u8272\u548C\u6CD5\u7EBF\n  vec4 finalColor = vec4(0.0);\n  vec3 finalNormal = normalize(vNormal);\n  float totalWeight = 0.0;\n  \n  // \u8BA1\u7B97\u6BCF\u5C42\u7684\u6DF7\u5408\u6743\u91CD\u548C\u989C\u8272\n  for (int i = 0; i < 8; i++) {\n    if (i >= uLayerCount) break;\n    \n    // \u8BA1\u7B97\u7EB9\u7406\u5750\u6807\n    vec2 tiledUv = vUv * uTilingFactors[i];\n    \n    // \u83B7\u53D6\u6DF7\u5408\u6743\u91CD\n    float weight = 0.0;\n    if (uUseBlendMaps) {\n      // \u4F7F\u7528\u6DF7\u5408\u8D34\u56FE\n      weight = texture2D(uBlendMaps[i], vUv).r;\n    } else {\n      // \u4F7F\u7528\u9AD8\u5EA6\u548C\u659C\u5EA6\u8BA1\u7B97\u6DF7\u5408\u6743\u91CD\n      weight = calculateBlendWeight(vHeight, vSlope, uMinHeights[i], uMaxHeights[i], uMinSlopes[i], uMaxSlopes[i]);\n    }\n    \n    if (weight > 0.0) {\n      // \u83B7\u53D6\u7EB9\u7406\u989C\u8272\n      vec4 textureColor = texture2D(uTextures[i], tiledUv);\n      \n      // \u83B7\u53D6\u6CD5\u7EBF\u8D34\u56FE\n      vec3 normalMapValue = calculateNormalFromMap(uNormalMaps[i], tiledUv, vNormal, vTangent);\n      \n      // \u83B7\u53D6\u7C97\u7CD9\u5EA6\u8D34\u56FE\n      float roughness = texture2D(uRoughnessMaps[i], tiledUv).r;\n      \n      // \u83B7\u53D6\u73AF\u5883\u5149\u906E\u853D\u8D34\u56FE\n      float ao = texture2D(uAOMaps[i], tiledUv).r;\n      \n      // \u7B80\u5355\u7684\u5149\u7167\u8BA1\u7B97\n      vec3 lightDir = normalize(uLightPosition - vWorldPosition);\n      float diffuse = max(dot(normalMapValue, lightDir), 0.0);\n      vec3 ambient = uAmbientColor * ao;\n      vec3 lighting = ambient + uLightColor * diffuse;\n      \n      // \u6DF7\u5408\u989C\u8272\n      finalColor += vec4(textureColor.rgb * lighting, textureColor.a) * weight;\n      \n      // \u6DF7\u5408\u6CD5\u7EBF\n      finalNormal = normalize(mix(finalNormal, normalMapValue, weight));\n      \n      totalWeight += weight;\n    }\n  }\n  \n  // \u5F52\u4E00\u5316\u989C\u8272\n  if (totalWeight > 0.0) {\n    finalColor /= totalWeight;\n  } else {\n    // \u9ED8\u8BA4\u989C\u8272\n    finalColor = vec4(0.5, 0.5, 0.5, 1.0);\n  }\n  \n  // \u5E94\u7528\u96FE\u6548\u679C\n  if (uUseFog) {\n    finalColor.rgb = mix(finalColor.rgb, uFogColor, vFogFactor);\n  }\n  \n  gl_FragColor = finalColor;\n}\n";
