/**
 * 网络消息
 * 定义网络消息的结构
 */

/**
 * 网络消息
 */
export interface NetworkMessage {
  /** 消息类型 */
  type: string;
  
  /** 消息数据 */
  data: any;
  
  /** 发送者ID */
  senderId?: string;
  
  /** 接收者ID */
  receiverId?: string;
  
  /** 消息时间戳 */
  timestamp: number;
  
  /** 消息ID */
  id?: string;
  
  /** 是否需要确认 */
  requireAck?: boolean;
  
  /** 确认ID */
  ackId?: string;
  
  /** 消息优先级 */
  priority?: number;
  
  /** 消息过期时间 */
  expireTime?: number;
  
  /** 消息序列号 */
  sequence?: number;
  
  /** 是否是可靠消息 */
  reliable?: boolean;
  
  /** 重试次数 */
  retryCount?: number;
  
  /** 最大重试次数 */
  maxRetries?: number;
  
  /** 重试间隔（毫秒） */
  retryInterval?: number;
  
  /** 是否是压缩消息 */
  compressed?: boolean;
  
  /** 是否是加密消息 */
  encrypted?: boolean;
  
  /** 加密算法 */
  encryptionAlgorithm?: string;
  
  /** 消息签名 */
  signature?: string;
  
  /** 消息版本 */
  version?: string;
  
  /** 附加元数据 */
  metadata?: Record<string, any>;
}
