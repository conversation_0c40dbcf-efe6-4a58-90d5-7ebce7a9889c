/**
 * 水体物理优化系统
 * 用于优化水体物理模拟性能，特别是在移动设备上
 */
import { System } from '../../core/System';
import { WaterBodyComponent } from './WaterBodyComponent';
import { DevicePerformanceLevel } from '../../utils/DeviceCapabilities';
import type { Entity } from '../../core/Entity';
/**
 * 水体物理优化系统配置
 */
export interface WaterPhysicsOptimizationSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用调试可视化 */
    enableDebugVisualization?: boolean;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
    /** 是否启用自动性能优化 */
    enableAutoOptimization?: boolean;
    /** 是否启用电池优化 */
    enableBatteryOptimization?: boolean;
    /** 是否启用温度优化 */
    enableTemperatureOptimization?: boolean;
    /** 目标帧率 */
    targetFPS?: number;
    /** 最小可接受帧率 */
    minAcceptableFPS?: number;
    /** 默认性能级别 */
    defaultPerformanceLevel?: DevicePerformanceLevel;
    /** 低电量阈值（百分比） */
    lowBatteryThreshold?: number;
    /** 高温阈值（摄氏度） */
    highTemperatureThreshold?: number;
    /** 是否启用空间分区 */
    enableSpatialPartitioning?: boolean;
    /** 是否启用多线程 */
    enableMultithreading?: boolean;
    /** 是否启用自适应物理更新 */
    enableAdaptivePhysicsUpdate?: boolean;
    /** 是否启用物理LOD */
    enablePhysicsLOD?: boolean;
    /** 是否启用物理休眠 */
    enablePhysicsSleeping?: boolean;
    /** 是否启用物理缓存 */
    enablePhysicsCaching?: boolean;
    /** 是否启用物理预测 */
    enablePhysicsPrediction?: boolean;
    /** 是否启用物理插值 */
    enablePhysicsInterpolation?: boolean;
    /** 是否启用物理简化 */
    enablePhysicsSimplification?: boolean;
    /** 是否启用物理实例化 */
    enablePhysicsInstancing?: boolean;
    /** 是否启用物理批处理 */
    enablePhysicsBatching?: boolean;
    /** 是否启用物理剔除 */
    enablePhysicsCulling?: boolean;
    /** 是否启用物理优先级 */
    enablePhysicsPriority?: boolean;
    /** 是否启用物理延迟更新 */
    enablePhysicsDeferredUpdate?: boolean;
    /** 是否启用物理异步更新 */
    enablePhysicsAsyncUpdate?: boolean;
    /** 是否启用物理GPU加速 */
    enablePhysicsGPUAcceleration?: boolean;
    /** 是否启用物理SIMD加速 */
    enablePhysicsSIMDAcceleration?: boolean;
    /** 是否启用物理WebAssembly加速 */
    enablePhysicsWebAssemblyAcceleration?: boolean;
    /** 是否启用物理SharedArrayBuffer加速 */
    enablePhysicsSharedArrayBufferAcceleration?: boolean;
    /** 是否启用物理WebWorker加速 */
    enablePhysicsWebWorkerAcceleration?: boolean;
    /** 是否启用物理WebGL加速 */
    enablePhysicsWebGLAcceleration?: boolean;
    /** 是否启用物理WebCL加速 */
    enablePhysicsWebCLAcceleration?: boolean;
    /** 是否启用物理WebNN加速 */
    enablePhysicsWebNNAcceleration?: boolean;
    /** 是否启用物理WebML加速 */
    enablePhysicsWebMLAcceleration?: boolean;
    /** 是否启用物理WebXR加速 */
    enablePhysicsWebXRAcceleration?: boolean;
    /** 是否启用物理WebVR加速 */
    enablePhysicsWebVRAcceleration?: boolean;
    /** 是否启用物理WebAR加速 */
    enablePhysicsWebARAcceleration?: boolean;
    /** 是否启用物理WebAudio加速 */
    enablePhysicsWebAudioAcceleration?: boolean;
    /** 是否启用物理WebRTC加速 */
    enablePhysicsWebRTCAcceleration?: boolean;
    /** 是否启用物理WebSockets加速 */
    enablePhysicsWebSocketsAcceleration?: boolean;
    /** 是否启用物理WebTransport加速 */
    enablePhysicsWebTransportAcceleration?: boolean;
    /** 是否启用物理WebCodecs加速 */
    enablePhysicsWebCodecsAcceleration?: boolean;
    /** 是否启用物理WebUSB加速 */
    enablePhysicsWebUSBAcceleration?: boolean;
    /** 是否启用物理WebSerial加速 */
    enablePhysicsWebSerialAcceleration?: boolean;
    /** 是否启用物理WebHID加速 */
    enablePhysicsWebHIDAcceleration?: boolean;
    /** 是否启用物理WebNFC加速 */
    enablePhysicsWebNFCAcceleration?: boolean;
    /** 是否启用物理WebMIDI加速 */
    enablePhysicsWebMIDIAcceleration?: boolean;
    /** 是否启用物理WebShare加速 */
    enablePhysicsWebShareAcceleration?: boolean;
    /** 是否启用物理WebPayment加速 */
    enablePhysicsWebPaymentAcceleration?: boolean;
    /** 是否启用物理WebCredential加速 */
    enablePhysicsWebCredentialAcceleration?: boolean;
    /** 是否启用物理WebAuthentication加速 */
    enablePhysicsWebAuthenticationAcceleration?: boolean;
    /** 是否启用物理WebCrypto加速 */
    enablePhysicsWebCryptoAcceleration?: boolean;
    /** 是否启用物理WebSQL加速 */
    enablePhysicsWebSQLAcceleration?: boolean;
    /** 是否启用物理WebIndexedDB加速 */
    enablePhysicsWebIndexedDBAcceleration?: boolean;
    /** 是否启用物理WebServiceWorker加速 */
    enablePhysicsWebServiceWorkerAcceleration?: boolean;
    /** 是否启用物理WebPush加速 */
    enablePhysicsWebPushAcceleration?: boolean;
    /** 是否启用物理WebNotification加速 */
    enablePhysicsWebNotificationAcceleration?: boolean;
    /** 是否启用物理WebBackground加速 */
    enablePhysicsWebBackgroundAcceleration?: boolean;
    /** 是否启用物理WebSync加速 */
    enablePhysicsWebSyncAcceleration?: boolean;
    /** 是否启用物理WebPeriodicSync加速 */
    enablePhysicsWebPeriodicSyncAcceleration?: boolean;
    /** 是否启用物理WebBudget加速 */
    enablePhysicsWebBudgetAcceleration?: boolean;
    /** 是否启用物理WebPermission加速 */
    enablePhysicsWebPermissionAcceleration?: boolean;
    /** 是否启用物理WebLock加速 */
    enablePhysicsWebLockAcceleration?: boolean;
    /** 是否启用物理WebWakeLock加速 */
    enablePhysicsWebWakeLockAcceleration?: boolean;
    /** 是否启用物理WebVibration加速 */
    enablePhysicsWebVibrationAcceleration?: boolean;
    /** 是否启用物理WebGeolocation加速 */
    enablePhysicsWebGeolocationAcceleration?: boolean;
    /** 是否启用物理WebDeviceOrientation加速 */
    enablePhysicsWebDeviceOrientationAcceleration?: boolean;
    /** 是否启用物理WebDeviceMotion加速 */
    enablePhysicsWebDeviceMotionAcceleration?: boolean;
    /** 是否启用物理WebAmbientLight加速 */
    enablePhysicsWebAmbientLightAcceleration?: boolean;
    /** 是否启用物理WebProximity加速 */
    enablePhysicsWebProximityAcceleration?: boolean;
    /** 是否启用物理WebBattery加速 */
    enablePhysicsWebBatteryAcceleration?: boolean;
    /** 是否启用物理WebNetwork加速 */
    enablePhysicsWebNetworkAcceleration?: boolean;
    /** 是否启用物理WebCPU加速 */
    enablePhysicsWebCPUAcceleration?: boolean;
    /** 是否启用物理WebHardware加速 */
    enablePhysicsWebHardwareAcceleration?: boolean;
    /** 是否启用物理WebSoftware加速 */
    enablePhysicsWebSoftwareAcceleration?: boolean;
    /** 是否启用物理WebOS加速 */
    enablePhysicsWebOSAcceleration?: boolean;
    /** 是否启用物理WebBrowser加速 */
    enablePhysicsWebBrowserAcceleration?: boolean;
    /** 是否启用物理WebEngine加速 */
    enablePhysicsWebEngineAcceleration?: boolean;
    /** 是否启用物理WebRuntime加速 */
    enablePhysicsWebRuntimeAcceleration?: boolean;
    /** 是否启用物理WebVM加速 */
    enablePhysicsWebVMAcceleration?: boolean;
    /** 是否启用物理WebJIT加速 */
    enablePhysicsWebJITAcceleration?: boolean;
    /** 是否启用物理WebAOT加速 */
    enablePhysicsWebAOTAcceleration?: boolean;
    /** 是否启用物理WebInterpreter加速 */
    enablePhysicsWebInterpreterAcceleration?: boolean;
    /** 是否启用物理WebCompiler加速 */
    enablePhysicsWebCompilerAcceleration?: boolean;
    /** 是否启用物理WebOptimizer加速 */
    enablePhysicsWebOptimizerAcceleration?: boolean;
    /** 是否启用物理WebDebugger加速 */
    enablePhysicsWebDebuggerAcceleration?: boolean;
    /** 是否启用物理WebProfiler加速 */
    enablePhysicsWebProfilerAcceleration?: boolean;
    /** 是否启用物理WebAnalyzer加速 */
    enablePhysicsWebAnalyzerAcceleration?: boolean;
    /** 是否启用物理WebMonitor加速 */
    enablePhysicsWebMonitorAcceleration?: boolean;
    /** 是否启用物理WebLogger加速 */
    enablePhysicsWebLoggerAcceleration?: boolean;
    /** 是否启用物理WebTracer加速 */
    enablePhysicsWebTracerAcceleration?: boolean;
    /** 是否启用物理WebInspector加速 */
    enablePhysicsWebInspectorAcceleration?: boolean;
    /** 是否启用物理WebConsole加速 */
    enablePhysicsWebConsoleAcceleration?: boolean;
    /** 是否启用物理WebTerminal加速 */
    enablePhysicsWebTerminalAcceleration?: boolean;
    /** 是否启用物理WebShell加速 */
    enablePhysicsWebShellAcceleration?: boolean;
    /** 是否启用物理WebCommand加速 */
    enablePhysicsWebCommandAcceleration?: boolean;
    /** 是否启用物理WebScript加速 */
    enablePhysicsWebScriptAcceleration?: boolean;
    /** 是否启用物理WebModule加速 */
    enablePhysicsWebModuleAcceleration?: boolean;
    /** 是否启用物理WebPackage加速 */
    enablePhysicsWebPackageAcceleration?: boolean;
    /** 是否启用物理WebLibrary加速 */
    enablePhysicsWebLibraryAcceleration?: boolean;
    /** 是否启用物理WebFramework加速 */
    enablePhysicsWebFrameworkAcceleration?: boolean;
    /** 是否启用物理WebPlatform加速 */
    enablePhysicsWebPlatformAcceleration?: boolean;
    /** 是否启用物理WebAPI加速 */
    enablePhysicsWebAPIAcceleration?: boolean;
    /** 是否启用物理WebSDK加速 */
    enablePhysicsWebSDKAcceleration?: boolean;
    /** 是否启用物理WebIDE加速 */
    enablePhysicsWebIDEAcceleration?: boolean;
    /** 是否启用物理WebEditor加速 */
    enablePhysicsWebEditorAcceleration?: boolean;
    /** 是否启用物理WebDesigner加速 */
    enablePhysicsWebDesignerAcceleration?: boolean;
    /** 是否启用物理WebBuilder加速 */
    enablePhysicsWebBuilderAcceleration?: boolean;
    /** 是否启用物理WebTester加速 */
    enablePhysicsWebTesterAcceleration?: boolean;
    /** 是否启用物理WebDeployer加速 */
    enablePhysicsWebDeployerAcceleration?: boolean;
    /** 是否启用物理WebPublisher加速 */
    enablePhysicsWebPublisherAcceleration?: boolean;
    /** 是否启用物理WebDistributor加速 */
    enablePhysicsWebDistributorAcceleration?: boolean;
    /** 是否启用物理WebInstaller加速 */
    enablePhysicsWebInstallerAcceleration?: boolean;
    /** 是否启用物理WebUpdater加速 */
    enablePhysicsWebUpdaterAcceleration?: boolean;
    /** 是否启用物理WebLauncher加速 */
    enablePhysicsWebLauncherAcceleration?: boolean;
    /** 是否启用物理WebRunner加速 */
    enablePhysicsWebRunnerAcceleration?: boolean;
    /** 是否启用物理WebExecutor加速 */
    enablePhysicsWebExecutorAcceleration?: boolean;
    /** 是否启用物理WebScheduler加速 */
    enablePhysicsWebSchedulerAcceleration?: boolean;
    /** 是否启用物理WebTimer加速 */
    enablePhysicsWebTimerAcceleration?: boolean;
    /** 是否启用物理WebClock加速 */
    enablePhysicsWebClockAcceleration?: boolean;
    /** 是否启用物理WebCalendar加速 */
    enablePhysicsWebCalendarAcceleration?: boolean;
    /** 是否启用物理WebDate加速 */
    enablePhysicsWebDateAcceleration?: boolean;
    /** 是否启用物理WebTime加速 */
    enablePhysicsWebTimeAcceleration?: boolean;
    /** 是否启用物理WebDuration加速 */
    enablePhysicsWebDurationAcceleration?: boolean;
    /** 是否启用物理WebInterval加速 */
    enablePhysicsWebIntervalAcceleration?: boolean;
    /** 是否启用物理WebTimeout加速 */
    enablePhysicsWebTimeoutAcceleration?: boolean;
    /** 是否启用物理WebDelay加速 */
    enablePhysicsWebDelayAcceleration?: boolean;
    /** 是否启用物理WebSleep加速 */
    enablePhysicsWebSleepAcceleration?: boolean;
    /** 是否启用物理WebWait加速 */
    enablePhysicsWebWaitAcceleration?: boolean;
    /** 是否启用物理WebAsync加速 */
    enablePhysicsWebAsyncAcceleration?: boolean;
    /** 是否启用物理WebPromise加速 */
    enablePhysicsWebPromiseAcceleration?: boolean;
    /** 是否启用物理WebFuture加速 */
    enablePhysicsWebFutureAcceleration?: boolean;
    /** 是否启用物理WebTask加速 */
    enablePhysicsWebTaskAcceleration?: boolean;
    /** 是否启用物理WebJob加速 */
    enablePhysicsWebJobAcceleration?: boolean;
    /** 是否启用物理WebWork加速 */
    enablePhysicsWebWorkAcceleration?: boolean;
    /** 是否启用物理WebProcess加速 */
    enablePhysicsWebProcessAcceleration?: boolean;
    /** 是否启用物理WebThread加速 */
    enablePhysicsWebThreadAcceleration?: boolean;
    /** 是否启用物理WebFiber加速 */
    enablePhysicsWebFiberAcceleration?: boolean;
    /** 是否启用物理WebCoroutine加速 */
    enablePhysicsWebCoroutineAcceleration?: boolean;
    /** 是否启用物理WebGenerator加速 */
    enablePhysicsWebGeneratorAcceleration?: boolean;
    /** 是否启用物理WebIterator加速 */
    enablePhysicsWebIteratorAcceleration?: boolean;
    /** 是否启用物理WebStream加速 */
    enablePhysicsWebStreamAcceleration?: boolean;
    /** 是否启用物理WebPipe加速 */
    enablePhysicsWebPipeAcceleration?: boolean;
    /** 是否启用物理WebChannel加速 */
    enablePhysicsWebChannelAcceleration?: boolean;
    /** 是否启用物理WebQueue加速 */
    enablePhysicsWebQueueAcceleration?: boolean;
    /** 是否启用物理WebStack加速 */
    enablePhysicsWebStackAcceleration?: boolean;
    /** 是否启用物理WebHeap加速 */
    enablePhysicsWebHeapAcceleration?: boolean;
    /** 是否启用物理WebList加速 */
    enablePhysicsWebListAcceleration?: boolean;
    /** 是否启用物理WebArray加速 */
    enablePhysicsWebArrayAcceleration?: boolean;
    /** 是否启用物理WebVector加速 */
    enablePhysicsWebVectorAcceleration?: boolean;
    /** 是否启用物理WebMatrix加速 */
    enablePhysicsWebMatrixAcceleration?: boolean;
    /** 是否启用物理WebTensor加速 */
    enablePhysicsWebTensorAcceleration?: boolean;
    /** 是否启用物理WebGraph加速 */
    enablePhysicsWebGraphAcceleration?: boolean;
    /** 是否启用物理WebTree加速 */
    enablePhysicsWebTreeAcceleration?: boolean;
    /** 是否启用物理WebMap加速 */
    enablePhysicsWebMapAcceleration?: boolean;
    /** 是否启用物理WebSet加速 */
    enablePhysicsWebSetAcceleration?: boolean;
    /** 是否启用物理WebDictionary加速 */
    enablePhysicsWebDictionaryAcceleration?: boolean;
    /** 是否启用物理WebTable加速 */
    enablePhysicsWebTableAcceleration?: boolean;
    /** 是否启用物理WebDatabase加速 */
    enablePhysicsWebDatabaseAcceleration?: boolean;
    /** 是否启用物理WebDisk加速 */
    enablePhysicsWebDiskAcceleration?: boolean;
    /** 是否启用物理WebFile加速 */
    enablePhysicsWebFileAcceleration?: boolean;
    /** 是否启用物理WebDirectory加速 */
    enablePhysicsWebDirectoryAcceleration?: boolean;
    /** 是否启用物理WebPath加速 */
    enablePhysicsWebPathAcceleration?: boolean;
    /** 是否启用物理WebURL加速 */
    enablePhysicsWebURLAcceleration?: boolean;
    /** 是否启用物理WebURI加速 */
    enablePhysicsWebURIAcceleration?: boolean;
    /** 是否启用物理WebHTTP加速 */
    enablePhysicsWebHTTPAcceleration?: boolean;
    /** 是否启用物理WebHTTPS加速 */
    enablePhysicsWebHTTPSAcceleration?: boolean;
    /** 是否启用物理WebFTP加速 */
    enablePhysicsWebFTPAcceleration?: boolean;
    /** 是否启用物理WebSFTP加速 */
    enablePhysicsWebSFTPAcceleration?: boolean;
    /** 是否启用物理WebSSH加速 */
    enablePhysicsWebSSHAcceleration?: boolean;
    /** 是否启用物理WebTelnet加速 */
    enablePhysicsWebTelnetAcceleration?: boolean;
    /** 是否启用物理WebRDP加速 */
    enablePhysicsWebRDPAcceleration?: boolean;
    /** 是否启用物理WebVNC加速 */
    enablePhysicsWebVNCAcceleration?: boolean;
    /** 是否启用物理WebSMB加速 */
    enablePhysicsWebSMBAcceleration?: boolean;
    /** 是否启用物理WebNFS加速 */
    enablePhysicsWebNFSAcceleration?: boolean;
    /** 是否启用物理WebCIFS加速 */
    enablePhysicsWebCIFSAcceleration?: boolean;
    /** 是否启用物理WebWebDAV加速 */
    enablePhysicsWebWebDAVAcceleration?: boolean;
    /** 是否启用物理WebCalDAV加速 */
    enablePhysicsWebCalDAVAcceleration?: boolean;
    /** 是否启用物理WebCardDAV加速 */
    enablePhysicsWebCardDAVAcceleration?: boolean;
    /** 是否启用物理WebIMAPAcceleration */
    enablePhysicsWebIMAPAcceleration?: boolean;
    /** 是否启用物理WebPOP3Acceleration */
    enablePhysicsWebPOP3Acceleration?: boolean;
    /** 是否启用物理WebSMTPAcceleration */
    enablePhysicsWebSMTPAcceleration?: boolean;
    /** 是否启用物理WebDNSAcceleration */
    enablePhysicsWebDNSAcceleration?: boolean;
    /** 是否启用物理WebDHCPAcceleration */
    enablePhysicsWebDHCPAcceleration?: boolean;
    /** 是否启用物理WebNTPAcceleration */
    enablePhysicsWebNTPAcceleration?: boolean;
    /** 是否启用物理WebSNMPAcceleration */
    enablePhysicsWebSNMPAcceleration?: boolean;
    /** 是否启用物理WebSNTPAcceleration */
    enablePhysicsWebSNTPAcceleration?: boolean;
    /** 是否启用物理WebSyslogAcceleration */
    enablePhysicsWebSyslogAcceleration?: boolean;
    /** 是否启用物理WebTFTPAcceleration */
    enablePhysicsWebTFTPAcceleration?: boolean;
    /** 是否启用物理WebUDPAcceleration */
    enablePhysicsWebUDPAcceleration?: boolean;
    /** 是否启用物理WebTCPAcceleration */
    enablePhysicsWebTCPAcceleration?: boolean;
    /** 是否启用物理WebICMPAcceleration */
    enablePhysicsWebICMPAcceleration?: boolean;
    /** 是否启用物理WebIPAcceleration */
    enablePhysicsWebIPAcceleration?: boolean;
    /** 是否启用物理WebIPv4Acceleration */
    enablePhysicsWebIPv4Acceleration?: boolean;
    /** 是否启用物理WebIPv6Acceleration */
    enablePhysicsWebIPv6Acceleration?: boolean;
    /** 是否启用物理WebARPAcceleration */
    enablePhysicsWebARPAcceleration?: boolean;
    /** 是否启用物理WebRARPAcceleration */
    enablePhysicsWebRARPAcceleration?: boolean;
    /** 是否启用物理WebMACAcceleration */
    enablePhysicsWebMACAcceleration?: boolean;
    /** 是否启用物理WebEthernetAcceleration */
    enablePhysicsWebEthernetAcceleration?: boolean;
    /** 是否启用物理WebWiFiAcceleration */
    enablePhysicsWebWiFiAcceleration?: boolean;
    /** 是否启用物理WebBluetoothAcceleration */
    enablePhysicsWebBluetoothAcceleration?: boolean;
    /** 是否启用物理WebZigBeeAcceleration */
    enablePhysicsWebZigBeeAcceleration?: boolean;
    /** 是否启用物理WebZ-WaveAcceleration */
    enablePhysicsWebZ_WaveAcceleration?: boolean;
    /** 是否启用物理WebLoRaAcceleration */
    enablePhysicsWebLoRaAcceleration?: boolean;
    /** 是否启用物理WebSigFoxAcceleration */
    enablePhysicsWebSigFoxAcceleration?: boolean;
    /** 是否启用物理WebNB-IoTAcceleration */
    enablePhysicsWebNB_IoTAcceleration?: boolean;
    /** 是否启用物理WebLTE-MAcceleration */
    enablePhysicsWebLTE_MAcceleration?: boolean;
    /** 是否启用物理Web5GAcceleration */
    enablePhysicsWeb5GAcceleration?: boolean;
    /** 是否启用物理Web4GAcceleration */
    enablePhysicsWeb4GAcceleration?: boolean;
    /** 是否启用物理Web3GAcceleration */
    enablePhysicsWeb3GAcceleration?: boolean;
    /** 是否启用物理Web2GAcceleration */
    enablePhysicsWeb2GAcceleration?: boolean;
    /** 是否启用物理Web1GAcceleration */
    enablePhysicsWeb1GAcceleration?: boolean;
    /** 是否启用物理WebGSMAcceleration */
    enablePhysicsWebGSMAcceleration?: boolean;
    /** 是否启用物理WebCDMAAcceleration */
    enablePhysicsWebCDMAAcceleration?: boolean;
    /** 是否启用物理WebTDMAAcceleration */
    enablePhysicsWebTDMAAcceleration?: boolean;
    /** 是否启用物理WebFDMAAcceleration */
    enablePhysicsWebFDMAAcceleration?: boolean;
    /** 是否启用物理WebOFDMAAcceleration */
    enablePhysicsWebOFDMAAcceleration?: boolean;
    /** 是否启用物理WebSCFDMAAcceleration */
    enablePhysicsWebSCFDMAAcceleration?: boolean;
    /** 是否启用物理WebCDMA2000Acceleration */
    enablePhysicsWebCDMA2000Acceleration?: boolean;
    /** 是否启用物理WebWCDMAAcceleration */
    enablePhysicsWebWCDMAAcceleration?: boolean;
    /** 是否启用物理WebHSDPAAcceleration */
    enablePhysicsWebHSDPAAcceleration?: boolean;
    /** 是否启用物理WebHSUPAAcceleration */
    enablePhysicsWebHSUPAAcceleration?: boolean;
    /** 是否启用物理WebHSPAAcceleration */
    enablePhysicsWebHSPAAcceleration?: boolean;
    /** 是否启用物理WebHSPA+Acceleration */
    enablePhysicsWebHSPAplusAcceleration?: boolean;
    /** 是否启用物理WebDC-HSDPAAcceleration */
    enablePhysicsWebDC_HSDPAAcceleration?: boolean;
    /** 是否启用物理WebDC-HSPAAcceleration */
    enablePhysicsWebDC_HSPAAcceleration?: boolean;
    /** 是否启用物理WebDC-HSPA+Acceleration */
    enablePhysicsWebDC_HSPAplusAcceleration?: boolean;
    /** 是否启用物理WebLTEAcceleration */
    enablePhysicsWebLTEAcceleration?: boolean;
    /** 是否启用物理WebLTE-AAcceleration */
    enablePhysicsWebLTE_AAcceleration?: boolean;
    /** 是否启用物理WebLTE-BAcceleration */
    enablePhysicsWebLTE_BAcceleration?: boolean;
    /** 是否启用物理WebLTE-UAcceleration */
    enablePhysicsWebLTE_UAcceleration?: boolean;
    /** 是否启用物理WebLTE-LAA */
    enablePhysicsWebLTE_LAA?: boolean;
}
/**
 * 水体物理优化系统
 * 用于优化水体物理模拟性能，特别是在移动设备上
 */
export declare class WaterPhysicsOptimizationSystem extends System {
    /** 配置 */
    private config;
    /** 水体组件映射 */
    private waterBodies;
    /** 水体物理系统 */
    private waterPhysicsSystem;
    /** 设备能力 */
    private deviceCapabilities;
    /** 性能监控 */
    private performanceMonitor;
    /** 当前性能级别 */
    private currentPerformanceLevel;
    /** 帧计数器 */
    private frameCount;
    /** 监控数据 */
    private monitorData;
    /** 性能配置 */
    private performanceConfigs;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: WaterPhysicsOptimizationSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 更新监控数据
     */
    private updateMonitorData;
    /**
     * 计算水体粒子数量
     * @returns 水体粒子数量
     */
    private calculateWaterParticleCount;
    /**
     * 计算水体碰撞数量
     * @returns 水体碰撞数量
     */
    private calculateWaterCollisionCount;
    /**
     * 根据性能调整性能级别
     */
    private adjustPerformance;
    /**
     * 根据电池电量调整性能级别
     */
    private adjustPerformanceByBattery;
    /**
     * 根据设备温度调整性能级别
     */
    private adjustPerformanceByTemperature;
    /**
     * 提高性能级别
     */
    private increasePerformanceLevel;
    /**
     * 降低性能级别
     */
    private decreasePerformanceLevel;
    /**
     * 设置性能级别
     * @param level 性能级别
     */
    setPerformanceLevel(level: DevicePerformanceLevel): void;
    /**
     * 应用性能配置
     * @param level 性能级别
     */
    private applyPerformanceConfig;
    /**
     * 更新水体组件以适应移动设备
     * @param waterBody 水体组件
     * @param level 性能级别
     */
    private updateWaterBodyForMobile;
    /**
     * 添加水体组件
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterBody(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体组件
     * @param entity 实体
     */
    removeWaterBody(entity: Entity): void;
    /**
     * 获取当前性能级别
     * @returns 当前性能级别
     */
    getCurrentPerformanceLevel(): DevicePerformanceLevel;
    /**
     * 获取监控数据
     * @returns 监控数据
     */
    getMonitorData(): any;
    /**
     * 获取性能配置
     * @param level 性能级别
     * @returns 性能配置
     */
    getPerformanceConfig(level: DevicePerformanceLevel): any;
    /**
     * 设置性能配置
     * @param level 性能级别
     * @param config 性能配置
     */
    setPerformanceConfig(level: DevicePerformanceLevel, config: any): void;
    /**
     * 配置系统
     * @param config 配置
     */
    configure(config: Partial<WaterPhysicsOptimizationSystemConfig>): void;
    /**
     * 启用系统
     */
    enable(): void;
    /**
     * 禁用系统
     */
    disable(): void;
    /**
     * 启用自动更新
     */
    enableAutoUpdate(): void;
    /**
     * 禁用自动更新
     */
    disableAutoUpdate(): void;
    /**
     * 启用自动优化
     */
    enableAutoOptimization(): void;
    /**
     * 禁用自动优化
     */
    disableAutoOptimization(): void;
    /**
     * 启用电池优化
     */
    enableBatteryOptimization(): void;
    /**
     * 禁用电池优化
     */
    disableBatteryOptimization(): void;
    /**
     * 启用温度优化
     */
    enableTemperatureOptimization(): void;
    /**
     * 禁用温度优化
     */
    disableTemperatureOptimization(): void;
    /**
     * 启用性能监控
     */
    enablePerformanceMonitoring(): void;
    /**
     * 禁用性能监控
     */
    disablePerformanceMonitoring(): void;
    /**
     * 启用调试可视化
     */
    enableDebugVisualization(): void;
    /**
     * 禁用调试可视化
     */
    disableDebugVisualization(): void;
    /**
     * 设置更新频率
     * @param frequency 更新频率
     */
    setUpdateFrequency(frequency: number): void;
    /**
     * 设置目标帧率
     * @param fps 目标帧率
     */
    setTargetFPS(fps: number): void;
    /**
     * 设置最小可接受帧率
     * @param fps 最小可接受帧率
     */
    setMinAcceptableFPS(fps: number): void;
    /**
     * 设置低电量阈值
     * @param threshold 低电量阈值（百分比）
     */
    setLowBatteryThreshold(threshold: number): void;
    /**
     * 设置高温阈值
     * @param threshold 高温阈值（摄氏度）
     */
    setHighTemperatureThreshold(threshold: number): void;
}
