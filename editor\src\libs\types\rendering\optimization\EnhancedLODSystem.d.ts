/**
 * 增强的LOD系统
 * 提供高质量、高效率的LOD管理和平滑过渡
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { LODSystem, LODSystemOptions } from './LODSystem';
import { SimplificationAlgorithm } from './EnhancedLODGenerator';
import { TransitionType } from './LODTransitionController';
/**
 * 增强的LOD系统配置接口
 */
export interface EnhancedLODSystemOptions extends LODSystemOptions {
    /** 是否启用平滑过渡 */
    enableSmoothTransition?: boolean;
    /** 过渡持续时间（毫秒） */
    transitionDuration?: number;
    /** 过渡类型 */
    transitionType?: TransitionType;
    /** 是否使用增强的LOD生成器 */
    useEnhancedGenerator?: boolean;
    /** 简化算法 */
    simplificationAlgorithm?: SimplificationAlgorithm;
    /** 是否使用GPU加速 */
    useGPU?: boolean;
    /** 是否使用自适应LOD生成 */
    useAdaptiveLOD?: boolean;
    /** 是否使用纹理LOD */
    useTextureLOD?: boolean;
    /** 是否使用LOD缓存 */
    useLODCache?: boolean;
    /** 最大缓存大小（字节） */
    maxCacheSize?: number;
}
/**
 * 增强的LOD系统事件类型
 */
export declare enum EnhancedLODSystemEventType {
    /** LOD级别变更开始 */
    LEVEL_CHANGE_START = "level_change_start",
    /** LOD级别变更完成 */
    LEVEL_CHANGE_COMPLETE = "level_change_complete",
    /** LOD生成开始 */
    GENERATION_START = "generation_start",
    /** LOD生成完成 */
    GENERATION_COMPLETE = "generation_complete",
    /** LOD生成错误 */
    GENERATION_ERROR = "generation_error"
}
/**
 * 增强的LOD系统类
 */
export declare class EnhancedLODSystem extends LODSystem {
    /** 系统类型 */
    static readonly ENHANCED_TYPE: string;
    /** 增强的LOD生成器 */
    private enhancedLODGenerator;
    /** LOD过渡控制器 */
    private transitionController;
    /** 是否启用平滑过渡 */
    private enableSmoothTransition;
    /** 是否使用增强的LOD生成器 */
    private useEnhancedGenerator;
    /** 是否使用自适应LOD生成 */
    private useAdaptiveLOD;
    /** 是否使用纹理LOD */
    private useTextureLOD;
    /** 是否使用LOD缓存 */
    private useLODCache;
    /** 最大缓存大小（字节） */
    private maxCacheSize;
    /** 当前缓存大小（字节） */
    private currentCacheSize;
    /** LOD缓存 */
    private lodCache;
    /** 增强事件发射器 */
    private enhancedEventEmitter;
    /**
     * 创建增强的LOD系统
     * @param options 增强的LOD系统配置
     */
    constructor(options?: EnhancedLODSystemOptions);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 处理LOD级别变更事件
     * @param entity 实体
     * @param component LOD组件
     * @param level 新级别
     */
    private handleLevelChanged;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 自动生成LOD
     * @param entity 实体
     * @param mesh 网格
     * @param distances 距离配置
     * @returns 是否成功生成
     */
    autoGenerateLOD(entity: Entity, mesh: THREE.Mesh, distances?: number[]): boolean;
    /**
     * 使用增强的LOD生成器生成LOD
     * @param entity 实体
     * @param mesh 网格
     * @param distances 距离配置
     * @returns 是否成功生成
     */
    private autoGenerateEnhancedLOD;
    /**
     * 生成缓存键
     * @param mesh 网格
     * @returns 缓存键
     */
    private generateCacheKey;
    /**
     * 从缓存创建LOD
     * @param entity 实体
     * @param cacheKey 缓存键
     * @param distances 距离配置
     * @returns 是否成功创建
     */
    private createLODFromCache;
    /**
     * 添加到缓存
     * @param cacheKey 缓存键
     * @param result LOD生成结果
     */
    private addToCache;
    /**
     * 清理缓存
     * @param requiredSize 需要的大小（字节）
     */
    private cleanCache;
    /**
     * 计算内存大小
     * @param result LOD生成结果
     * @returns 内存大小（字节）
     */
    private calculateMemorySize;
    /**
     * 计算几何体大小
     * @param geometry 几何体
     * @returns 大小（字节）
     */
    private calculateGeometrySize;
    /**
     * 清除缓存
     */
    clearCache(): void;
    /**
     * 获取缓存大小
     * @returns 缓存大小（字节）
     */
    getCacheSize(): number;
    /**
     * 获取缓存项数量
     * @returns 缓存项数量
     */
    getCacheItemCount(): number;
    /**
     * 注册事件监听器
     * @param event 事件类型
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 注销事件监听器
     * @param event 事件类型
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
