# 服务注册中心部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: service-registry
  namespace: dl-engine
  labels:
    app: service-registry
    component: registry
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: service-registry
  template:
    metadata:
      labels:
        app: service-registry
        component: registry
        version: v1
    spec:
      containers:
      - name: service-registry
        image: your-registry/dl-engine-service-registry:latest
        ports:
        - containerPort: 3010
          name: tcp
          protocol: TCP
        - containerPort: 4010
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: SERVICE_REGISTRY_HOST
          value: "0.0.0.0"
        - name: SERVICE_REGISTRY_PORT
          value: "3010"
        - name: SERVICE_REGISTRY_HTTP_PORT
          value: "4010"
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_PORT
        - name: DB_USERNAME
          value: "root"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: DB_DATABASE
          value: "ir_engine_registry"
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: REDIS_PORT
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 4010
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 4010
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: service-registry
  namespace: dl-engine
  labels:
    app: service-registry
    component: registry
spec:
  type: ClusterIP
  ports:
  - port: 3010
    targetPort: 3010
    protocol: TCP
    name: tcp
  - port: 4010
    targetPort: 4010
    protocol: TCP
    name: http
  selector:
    app: service-registry
---
# 服务注册中心的HPA配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: service-registry-hpa
  namespace: dl-engine
  labels:
    app: service-registry
    component: registry
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: service-registry
  minReplicas: 1
  maxReplicas: 3
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
---
# 网络策略 - 限制服务注册中心的网络访问
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: service-registry-netpol
  namespace: dl-engine
  labels:
    app: service-registry
    component: registry
spec:
  podSelector:
    matchLabels:
      app: service-registry
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: dl-engine
    ports:
    - protocol: TCP
      port: 3010
    - protocol: TCP
      port: 4010
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: dl-engine
    ports:
    - protocol: TCP
      port: 3306  # MySQL
    - protocol: TCP
      port: 6379  # Redis
  - to: []  # 允许DNS查询
    ports:
    - protocol: UDP
      port: 53
---
# Pod Disruption Budget - 确保服务可用性
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: service-registry-pdb
  namespace: dl-engine
  labels:
    app: service-registry
    component: registry
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: service-registry
---
# ServiceMonitor - Prometheus监控配置
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: service-registry-monitor
  namespace: dl-engine
  labels:
    app: service-registry
    component: registry
spec:
  selector:
    matchLabels:
      app: service-registry
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
