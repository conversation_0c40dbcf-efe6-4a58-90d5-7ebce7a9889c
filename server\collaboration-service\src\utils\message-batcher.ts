/**
 * 消息批处理工具
 * 用于批量处理WebSocket消息，减少网络请求次数
 */
import { Logger } from '@nestjs/common';

/**
 * 批处理配置接口
 */
export interface BatchOptions {
  /**
   * 最大批处理大小（消息数量）
   */
  maxBatchSize: number;

  /**
   * 最大等待时间（毫秒）
   */
  maxWaitTime: number;

  /**
   * 是否启用批处理
   */
  enabled: boolean;
}

/**
 * 消息批处理器类
 */
export class MessageBatcher<T> {
  private queue: T[] = [];
  private timer: NodeJS.Timeout | null = null;
  private logger = new Logger(MessageBatcher.name);
  private options: BatchOptions;

  /**
   * 创建消息批处理器
   * @param processCallback 处理批量消息的回调函数
   * @param options 批处理配置
   */
  constructor(
    private readonly processCallback: (messages: T[]) => Promise<void>,
    options?: Partial<BatchOptions>,
  ) {
    this.options = {
      maxBatchSize: 100,
      maxWaitTime: 50, // 50毫秒
      enabled: true,
      ...options,
    };
  }

  /**
   * 添加消息到批处理队列
   * @param message 要添加的消息
   */
  add(message: T): void {
    if (!this.options.enabled) {
      // 如果批处理被禁用，直接处理消息
      this.processCallback([message]).catch(err => {
        this.logger.error('处理单个消息时出错:', err);
      });
      return;
    }

    // 添加消息到队列
    this.queue.push(message);

    // 如果队列达到最大批处理大小，立即处理
    if (this.queue.length >= this.options.maxBatchSize) {
      this.flush();
      return;
    }

    // 如果定时器未启动，启动定时器
    if (this.timer === null) {
      this.timer = setTimeout(() => {
        this.flush();
      }, this.options.maxWaitTime);
    }
  }

  /**
   * 立即处理队列中的所有消息
   */
  flush(): void {
    // 清除定时器
    if (this.timer !== null) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    // 如果队列为空，不处理
    if (this.queue.length === 0) {
      return;
    }

    // 复制队列并清空
    const messages = [...this.queue];
    this.queue = [];

    // 处理消息
    this.processCallback(messages).catch(err => {
      this.logger.error('处理批量消息时出错:', err);
    });
  }

  /**
   * 获取当前队列中的消息数量
   */
  get queueSize(): number {
    return this.queue.length;
  }

  /**
   * 获取当前队列长度
   * @returns 队列长度
   */
  getQueueLength(): number {
    return this.queue.length;
  }

  /**
   * 更新批处理配置
   * @param options 新的批处理配置
   */
  updateOptions(options: Partial<BatchOptions>): void {
    this.options = {
      ...this.options,
      ...options,
    };

    // 如果批处理被禁用，立即处理队列中的消息
    if (!this.options.enabled) {
      this.flush();
    }
  }

  /**
   * 销毁批处理器
   */
  destroy(): void {
    // 清除定时器
    if (this.timer !== null) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    // 处理剩余的消息
    this.flush();
  }
}
