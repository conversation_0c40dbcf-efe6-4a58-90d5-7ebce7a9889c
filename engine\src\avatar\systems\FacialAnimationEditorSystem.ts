/**
 * 面部动画编辑器系统
 * 用于编辑和管理面部动画
 */
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import type { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationComponent, FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';
import { FacialAnimationEditorComponent } from '../components/FacialAnimationEditorComponent';
import { FacialAnimationClip } from '../animation/FacialAnimationClip';

/**
 * 面部动画编辑器系统配置
 */
export interface FacialAnimationEditorSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 自动保存间隔（毫秒） */
  autoSaveInterval?: number;
  /** 是否启用撤销/重做 */
  enableUndoRedo?: boolean;
  /** 最大撤销步数 */
  maxUndoSteps?: number;
}

/**
 * 面部动画编辑器系统
 */
export class FacialAnimationEditorSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'FacialAnimationEditorSystem';

  /** 配置 */
  private config: FacialAnimationEditorSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 编辑器组件映射 */
  private editors: Map<Entity, FacialAnimationEditorComponent> = new Map();

  /** 当前选中的编辑器 */
  private activeEditor: FacialAnimationEditorComponent | null = null;

  /** 自动保存计时器 */
  private autoSaveTimer: number = 0;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: FacialAnimationEditorSystemConfig = {}) {
    super(290); // 设置优先级

    this.config = {
      debug: false,
      autoSaveInterval: 60000, // 默认60秒
      enableUndoRedo: true,
      maxUndoSteps: 20,
      ...config
    };

    if (this.config.debug) {
      console.log('面部动画编辑器系统初始化');
    }
  }

  /**
   * 创建编辑器
   * @param entity 实体
   * @returns 编辑器组件
   */
  public createEditor(entity: Entity): FacialAnimationEditorComponent {
    // 检查实体是否已有编辑器
    if (this.editors.has(entity)) {
      return this.editors.get(entity)!;
    }

    // 创建编辑器组件
    const editor = new FacialAnimationEditorComponent(entity);

    // 添加组件到实体
    entity.addComponent(editor);

    // 添加组件到映射
    this.editors.set(entity, editor);

    if (this.config.debug) {
      console.log('创建面部动画编辑器', entity);
    }

    return editor;
  }

  /**
   * 获取编辑器
   * @param entity 实体
   * @returns 编辑器组件
   */
  public getEditor(entity: Entity): FacialAnimationEditorComponent | null {
    return this.editors.get(entity) || null;
  }

  /**
   * 移除编辑器
   * @param entity 实体
   */
  public removeEditor(entity: Entity): void {
    const editor = this.editors.get(entity);
    if (editor) {
      entity.removeComponent(editor);
      this.editors.delete(entity);

      if (this.activeEditor === editor) {
        this.activeEditor = null;
      }

      if (this.config.debug) {
        console.log('移除面部动画编辑器', entity);
      }
    }
  }

  /**
   * 设置活动编辑器
   * @param entity 实体
   * @returns 是否成功设置
   */
  public setActiveEditor(entity: Entity): boolean {
    const editor = this.editors.get(entity);
    if (editor) {
      this.activeEditor = editor;

      if (this.config.debug) {
        console.log('设置活动编辑器', entity);
      }

      return true;
    }

    return false;
  }

  /**
   * 获取活动编辑器
   * @returns 活动编辑器
   */
  public getActiveEditor(): FacialAnimationEditorComponent | null {
    return this.activeEditor;
  }

  /**
   * 创建新动画片段
   * @param entity 实体
   * @param name 名称
   * @param duration 持续时间（秒）
   * @returns 动画片段
   */
  public createClip(entity: Entity, name: string, duration: number = 5.0): FacialAnimationClip | null {
    const editor = this.editors.get(entity);
    if (!editor) return null;

    // 创建新片段
    const clip = new FacialAnimationClip(name);
    clip.duration = duration;

    // 添加到编辑器
    editor.addClip(clip);

    // 设置为当前片段
    editor.setCurrentClip(name);

    if (this.config.debug) {
      console.log('创建动画片段', name, entity);
    }

    return clip;
  }

  /**
   * 导入动画片段
   * @param entity 实体
   * @param json JSON对象
   * @returns 动画片段
   */
  public importClipFromJSON(entity: Entity, json: any): FacialAnimationClip | null {
    const editor = this.editors.get(entity);
    if (!editor) return null;

    try {
      // 从JSON创建片段
      const clip = FacialAnimationClip.fromJSON(json);

      // 添加到编辑器
      editor.addClip(clip);

      if (this.config.debug) {
        console.log('导入动画片段', clip.name, entity);
      }

      return clip;
    } catch (error) {
      console.error('导入动画片段失败:', error);
      return null;
    }
  }

  /**
   * 导出动画片段为JSON
   * @param entity 实体
   * @param clipName 片段名称
   * @returns JSON对象
   */
  public exportClipToJSON(entity: Entity, clipName: string): any | null {
    const editor = this.editors.get(entity);
    if (!editor) return null;

    // 获取片段
    const clip = editor.getClip(clipName);
    if (!clip) return null;

    // 导出为JSON
    return clip.toJSON();
  }

  /**
   * 播放动画片段
   * @param entity 实体
   * @param clipName 片段名称
   * @returns 是否成功播放
   */
  public playClip(entity: Entity, clipName?: string): boolean {
    const editor = this.editors.get(entity);
    if (!editor) return false;

    // 如果没有指定片段名称，使用当前片段
    if (!clipName) {
      return editor.play();
    }

    // 设置当前片段并播放
    if (editor.setCurrentClip(clipName)) {
      return editor.play();
    }

    return false;
  }

  /**
   * 停止播放
   * @param entity 实体
   * @returns 是否成功停止
   */
  public stopClip(entity: Entity): boolean {
    const editor = this.editors.get(entity);
    if (!editor) return false;

    return editor.stop();
  }

  /**
   * 暂停播放
   * @param entity 实体
   * @returns 是否成功暂停
   */
  public pauseClip(entity: Entity): boolean {
    const editor = this.editors.get(entity);
    if (!editor) return false;

    return editor.pause();
  }

  /**
   * 设置播放速度
   * @param entity 实体
   * @param speed 速度
   * @returns 是否成功设置
   */
  public setPlaybackSpeed(entity: Entity, speed: number): boolean {
    const editor = this.editors.get(entity);
    if (!editor) return false;

    return editor.setPlaybackSpeed(speed);
  }

  /**
   * 设置当前时间
   * @param entity 实体
   * @param time 时间（秒）
   * @returns 是否成功设置
   */
  public setCurrentTime(entity: Entity, time: number): boolean {
    const editor = this.editors.get(entity);
    if (!editor) return false;

    return editor.setCurrentTime(time);
  }

  /**
   * 添加表情关键帧
   * @param entity 实体
   * @param time 时间（秒）
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功添加
   */
  public addExpressionKeyframe(
    entity: Entity,
    time: number,
    expression: FacialExpressionType,
    weight: number = 1.0
  ): boolean {
    const editor = this.editors.get(entity);
    if (!editor) return false;

    return editor.addExpressionKeyframe(time, expression, weight);
  }

  /**
   * 添加口型关键帧
   * @param entity 实体
   * @param time 时间（秒）
   * @param viseme 口型类型
   * @param weight 权重
   * @returns 是否成功添加
   */
  public addVisemeKeyframe(
    entity: Entity,
    time: number,
    viseme: VisemeType,
    weight: number = 1.0
  ): boolean {
    const editor = this.editors.get(entity);
    if (!editor) return false;

    return editor.addVisemeKeyframe(time, viseme, weight);
  }

  /**
   * 移除表情关键帧
   * @param entity 实体
   * @param time 时间（秒）
   * @returns 是否成功移除
   */
  public removeExpressionKeyframe(entity: Entity, time: number): boolean {
    const editor = this.editors.get(entity);
    if (!editor) return false;

    return editor.removeExpressionKeyframe(time);
  }

  /**
   * 移除口型关键帧
   * @param entity 实体
   * @param time 时间（秒）
   * @returns 是否成功移除
   */
  public removeVisemeKeyframe(entity: Entity, time: number): boolean {
    const editor = this.editors.get(entity);
    if (!editor) return false;

    return editor.removeVisemeKeyframe(time);
  }

  /**
   * 自动保存
   */
  private autoSave(): void {
    for (const [entity, editor] of this.editors.entries()) {
      // 保存当前编辑状态
      editor.saveState();

      if (this.config.debug) {
        console.log('自动保存编辑器状态', entity);
      }
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新自动保存计时器
    if (this.config.autoSaveInterval! > 0) {
      this.autoSaveTimer += deltaTime * 1000;

      if (this.autoSaveTimer >= this.config.autoSaveInterval!) {
        this.autoSave();
        this.autoSaveTimer = 0;
      }
    }

    // 更新所有编辑器
    for (const [entity, editor] of this.editors.entries()) {
      editor.update(deltaTime);
    }
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清除编辑器映射
    this.editors.clear();

    if (this.config.debug) {
      console.log('面部动画编辑器系统销毁');
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }
}
