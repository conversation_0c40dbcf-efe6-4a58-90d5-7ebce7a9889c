import type { Entity } from '../../core/Entity';
import type { Camera } from '../../rendering/Camera';
import { TerrainComponent } from '../components/TerrainComponent';
/**
 * 地形物理LOD级别
 */
export declare enum TerrainPhysicsLODLevel {
    /** 高精度 */
    HIGH = 0,
    /** 中精度 */
    MEDIUM = 1,
    /** 低精度 */
    LOW = 2,
    /** 极低精度 */
    VERY_LOW = 3,
    /** 禁用物理 */
    DISABLED = 4
}
/**
 * 地形物理LOD配置
 */
export interface TerrainPhysicsLODConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 高精度距离 */
    highLevelDistance?: number;
    /** 中精度距离 */
    mediumLevelDistance?: number;
    /** 低精度距离 */
    lowLevelDistance?: number;
    /** 极低精度距离 */
    veryLowLevelDistance?: number;
    /** 高精度分辨率 */
    highLevelResolution?: number;
    /** 中精度分辨率 */
    mediumLevelResolution?: number;
    /** 低精度分辨率 */
    lowLevelResolution?: number;
    /** 极低精度分辨率 */
    veryLowLevelResolution?: number;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 地形物理LOD事件类型
 */
export declare enum TerrainPhysicsLODEventType {
    /** LOD级别变更 */
    LOD_LEVEL_CHANGED = "lod_level_changed"
}
/**
 * 地形物理LOD系统
 */
export declare class TerrainPhysicsLOD {
    /** 是否启用 */
    private enabled;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 高精度距离 */
    private highLevelDistance;
    /** 中精度距离 */
    private mediumLevelDistance;
    /** 低精度距离 */
    private lowLevelDistance;
    /** 极低精度距离 */
    private veryLowLevelDistance;
    /** 高精度分辨率 */
    private highLevelResolution;
    /** 中精度分辨率 */
    private mediumLevelResolution;
    /** 低精度分辨率 */
    private lowLevelResolution;
    /** 极低精度分辨率 */
    private veryLowLevelResolution;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 地形实体映射 */
    private terrainEntities;
    /** LOD状态映射 */
    private lodStates;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 调试网格 */
    private debugMeshes;
    /** 活跃相机 */
    private activeCamera;
    /**
     * 创建地形物理LOD系统
     * @param config 配置
     */
    constructor(config?: TerrainPhysicsLODConfig);
    /**
     * 添加地形实体
     * @param entity 实体
     * @param component 地形组件
     */
    addTerrainEntity(entity: Entity, component: TerrainComponent): void;
    /**
     * 移除地形实体
     * @param entity 实体
     */
    removeTerrainEntity(entity: Entity): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 设置活跃相机
     * @param camera 相机
     */
    setActiveCamera(camera: Camera): void;
    /**
     * 更新所有地形实体
     * @param camera 相机
     */
    private updateTerrainEntities;
    /**
     * 确定LOD级别
     * @param distance 距离
     * @returns LOD级别
     */
    private determineLODLevel;
    /**
     * 获取级别对应的分辨率
     * @param level LOD级别
     * @returns 分辨率
     */
    private getResolutionForLevel;
    /**
     * 更新地形组件
     * @param entity 实体
     * @param component 地形组件
     * @param level LOD级别
     * @param resolution 分辨率
     */
    private updateTerrainComponent;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 清除调试网格
     */
    private clearDebugMeshes;
    /**
     * 设置配置
     * @param config 配置
     */
    setConfig(config: TerrainPhysicsLODConfig): void;
    /**
     * 获取配置
     * @returns 配置
     */
    getConfig(): TerrainPhysicsLODConfig;
    /**
     * 注册事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    on(event: TerrainPhysicsLODEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    off(event: TerrainPhysicsLODEventType, listener: (...args: any[]) => void): void;
}
