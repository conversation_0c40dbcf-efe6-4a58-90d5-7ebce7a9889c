/**
 * 视觉脚本变量
 * 用于在视觉脚本中存储和共享数据
 */
import { EventEmitter } from '../../utils/EventEmitter';
/**
 * 变量选项
 */
export interface VariableOptions {
    /** 变量ID */
    id: string;
    /** 变量名称 */
    name: string;
    /** 变量类型 */
    type: string;
    /** 变量值 */
    value?: any;
    /** 变量描述 */
    description?: string;
    /** 是否为常量 */
    constant?: boolean;
    /** 是否为全局变量 */
    global?: boolean;
}
/**
 * 视觉脚本变量
 * 用于在视觉脚本中存储和共享数据
 */
export declare class Variable extends EventEmitter {
    /** 变量ID */
    readonly id: string;
    /** 变量名称 */
    private _name;
    /** 变量类型 */
    private _type;
    /** 变量值 */
    private _value;
    /** 变量描述 */
    private _description;
    /** 是否为常量 */
    private _constant;
    /** 是否为全局变量 */
    private _global;
    /**
     * 创建变量
     * @param options 变量选项
     */
    constructor(options: VariableOptions);
    /**
     * 获取变量名称
     * @returns 变量名称
     */
    get name(): string;
    /**
     * 设置变量名称
     * @param value 变量名称
     */
    set name(value: string);
    /**
     * 获取变量类型
     * @returns 变量类型
     */
    get type(): string;
    /**
     * 设置变量类型
     * @param value 变量类型
     */
    set type(value: string);
    /**
     * 获取变量值
     * @returns 变量值
     */
    get value(): any;
    /**
     * 设置变量值
     * @param value 变量值
     */
    set value(value: any);
    /**
     * 获取变量描述
     * @returns 变量描述
     */
    get description(): string;
    /**
     * 设置变量描述
     * @param value 变量描述
     */
    set description(value: string);
    /**
     * 获取是否为常量
     * @returns 是否为常量
     */
    get constant(): boolean;
    /**
     * 设置是否为常量
     * @param value 是否为常量
     */
    set constant(value: boolean);
    /**
     * 获取是否为全局变量
     * @returns 是否为全局变量
     */
    get global(): boolean;
    /**
     * 设置是否为全局变量
     * @param value 是否为全局变量
     */
    set global(value: boolean);
    /**
     * 重置变量值
     */
    reset(): void;
    /**
     * 克隆变量
     * @returns 克隆的变量
     */
    clone(): Variable;
    /**
     * 序列化变量
     * @returns 序列化数据
     */
    serialize(): any;
    /**
     * 反序列化变量
     * @param data 序列化数据
     */
    deserialize(data: any): void;
}
