/**
 * 网络事件分发器
 * 负责处理网络事件的分发
 */
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkEvent } from './NetworkEvent';
import { EventPriority } from './NetworkEventBuffer';
/**
 * 事件处理器
 */
export type EventHandler = (event: NetworkEvent) => void;
/**
 * 事件过滤器
 */
export type EventFilter = (event: NetworkEvent) => boolean;
/**
 * 网络事件分发器配置
 */
export interface NetworkEventDispatcherConfig {
    /** 是否使用事件缓冲 */
    useEventBuffer?: boolean;
    /** 事件缓冲配置 */
    eventBufferConfig?: {
        /** 最大缓冲事件数量 */
        maxBufferSize?: number;
        /** 事件处理间隔（毫秒） */
        processInterval?: number;
        /** 是否自动处理事件 */
        autoProcess?: boolean;
        /** 每次处理的最大事件数量 */
        maxEventsPerProcess?: number;
    };
    /** 是否启用事件日志 */
    enableEventLogging?: boolean;
    /** 日志级别 */
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    /** 是否允许默认处理器 */
    allowDefaultHandlers?: boolean;
}
/**
 * 网络事件分发器
 * 负责处理网络事件的分发
 */
export declare class NetworkEventDispatcher extends EventEmitter {
    /** 配置 */
    private config;
    /** 事件缓冲器 */
    private eventBuffer;
    /** 事件订阅映射表 */
    private subscriptions;
    /** 默认事件处理器映射表 */
    private defaultHandlers;
    /** 下一个订阅ID */
    private nextSubscriptionId;
    /**
     * 创建网络事件分发器
     * @param config 配置
     */
    constructor(config?: NetworkEventDispatcherConfig);
    /**
     * 初始化默认事件处理器
     */
    private initDefaultHandlers;
    /**
     * 设置默认事件处理器
     * @param eventType 事件类型
     * @param handler 处理器
     */
    setDefaultHandler(eventType: string, handler: EventHandler): void;
    /**
     * 移除默认事件处理器
     * @param eventType 事件类型
     */
    removeDefaultHandler(eventType: string): void;
    /**
     * 分发事件
     * @param event 网络事件
     */
    dispatchEvent(event: NetworkEvent): void;
    /**
     * 处理事件
     * @param event 网络事件
     */
    private processEvent;
    /**
     * 记录事件日志
     * @param event 网络事件
     */
    private logEvent;
    /**
     * 订阅事件
     * @param eventType 事件类型
     * @param handler 处理器
     * @param options 选项
     * @returns 订阅ID
     */
    subscribe(eventType: string, handler: EventHandler, options?: {
        filter?: EventFilter;
        priority?: EventPriority;
        once?: boolean;
    }): string;
    /**
     * 取消订阅
     * @param eventType 事件类型
     * @param handler 处理器
     * @returns 是否成功取消
     */
    unsubscribe(eventType: string, handler: EventHandler): boolean;
    /**
     * 通过ID取消订阅
     * @param id 订阅ID
     * @returns 是否成功取消
     */
    unsubscribeById(id: string): boolean;
    /**
     * 取消所有订阅
     * @param eventType 事件类型（可选，如果不指定则取消所有事件类型的订阅）
     */
    unsubscribeAll(eventType?: string): void;
    /**
     * 创建事件
     * @param type 事件类型
     * @param data 事件数据
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param priority 优先级
     * @returns 网络事件
     */
    createEvent(type: string, data?: any, senderId?: string, receiverId?: string, priority?: EventPriority): NetworkEvent;
    /**
     * 获取事件订阅数量
     * @param eventType 事件类型（可选，如果不指定则返回所有事件类型的订阅数量）
     * @returns 订阅数量
     */
    getSubscriptionCount(eventType?: string): number;
    /**
     * 获取事件类型列表
     * @returns 事件类型列表
     */
    getEventTypes(): string[];
    /**
     * 销毁分发器
     */
    dispose(): void;
}
