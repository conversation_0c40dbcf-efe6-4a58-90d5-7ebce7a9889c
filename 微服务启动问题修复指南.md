# 微服务启动问题修复指南

## 问题概述

在使用 `.\start-windows.ps1` 启动微服务时遇到以下问题：

1. **rag-engine 和 monitoring-service 构建失败** - 缺少 `@nestjs/microservices` 依赖
2. **digital-human-chroma 容器不健康** - 健康检查配置问题
3. **前端编辑器构建失败** - Dockerfile 文件复制冲突

## 已修复的问题

### 1. 缺失的 @nestjs/microservices 依赖

**问题描述：**
```
error TS2307: Cannot find module '@nestjs/microservices' or its corresponding type declarations.
```

**修复方案：**
- 在 `server/rag-engine/package.json` 中添加了 `"@nestjs/microservices": "^10.0.0"` 依赖
- 在 `server/monitoring-service/package.json` 中添加了 `"@nestjs/microservices": "^10.0.0"` 依赖

### 2. Chroma 容器健康检查问题

**问题描述：**
```
Container digital-human-chroma is unhealthy
```

**修复方案：**
- 更新了 `server/docker-compose.windows.yml` 中的 Chroma 健康检查配置
- 将 `wget` 命令替换为更可靠的 Python 脚本检查方法

**修复前：**
```yaml
healthcheck:
  test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8000/api/v1/heartbeat || exit 1"]
```

**修复后：**
```yaml
healthcheck:
  test: ["CMD-SHELL", "python -c \"import requests; requests.get('http://localhost:8000/api/v1/heartbeat')\" || exit 1"]
```

### 3. 前端编辑器构建问题

**问题描述：**
```
failed to solve: cannot copy to non-directory: /var/lib/docker/buildkit/containerd-overlayfs/cachemounts/buildkit526402255/app/node_modules/dl-engine-core
```

**修复方案：**
- 更新了 `editor/Dockerfile`，避免覆盖已安装的 node_modules
- 改为选择性复制源代码文件

**修复前：**
```dockerfile
COPY editor .
```

**修复后：**
```dockerfile
COPY editor/src ./src
COPY editor/public ./public
COPY editor/index.html ./
COPY editor/vite.config.ts ./
COPY editor/tsconfig.json ./
COPY editor/tsconfig.node.json ./
COPY editor/jest.config.js ./
```

## 使用修复脚本

### 快速修复（推荐）

运行综合修复脚本：
```powershell
.\fix-all-microservices-issues.ps1
```

### 分步修复

1. **修复依赖问题：**
```powershell
.\fix-microservices-dependencies.ps1
```

2. **修复 Chroma 健康检查：**
```powershell
.\fix-chroma-healthcheck.ps1
```

## 重新启动服务

修复完成后，重新构建并启动服务：

```powershell
# 清理并重新构建
.\start-windows.ps1 -Clean -Build

# 或者分步启动
.\start-windows.ps1 -Profile basic    # 先启动基础设施
.\start-windows.ps1 -Profile full     # 启动所有服务
```

## 验证修复结果

### 检查服务状态
```powershell
# 切换到 server 目录
cd server

# 检查所有服务状态
docker-compose -f docker-compose.windows.yml ps

# 检查特定服务日志
docker-compose -f docker-compose.windows.yml logs chroma
docker-compose -f docker-compose.windows.yml logs rag-engine
docker-compose -f docker-compose.windows.yml logs monitoring-service
```

### 测试服务连接
```powershell
# 测试 Chroma 服务
curl http://localhost:8000/api/v1/heartbeat

# 测试其他服务
curl http://localhost:8080/health  # API Gateway
curl http://localhost:8761/health  # Service Registry
```

## 常见问题排查

### 1. Chroma 服务仍然不健康

**解决方案：**
```powershell
# 重启 Chroma 服务
docker-compose -f server/docker-compose.windows.yml restart chroma

# 检查 Chroma 日志
docker-compose -f server/docker-compose.windows.yml logs chroma

# 手动测试 Chroma 连接
curl http://localhost:8000/api/v1/heartbeat
```

### 2. 依赖安装失败

**解决方案：**
```powershell
# 清理 npm 缓存
cd server/rag-engine
npm cache clean --force
npm install

cd ../monitoring-service
npm cache clean --force
npm install
```

### 3. 前端构建仍然失败

**解决方案：**
```powershell
# 清理前端构建缓存
cd editor
rm -rf node_modules dist
npm install
npm run build
```

## 服务启动顺序

为确保服务正常启动，建议按以下顺序：

1. **基础设施服务**
   - MySQL
   - Redis
   - MinIO
   - Elasticsearch
   - Chroma

2. **核心服务**
   - Service Registry
   - API Gateway

3. **业务服务**
   - User Service
   - Project Service
   - Asset Service
   - Knowledge Service
   - RAG Engine
   - Monitoring Service

4. **前端服务**
   - Editor

## 监控和日志

### 实时监控
```powershell
# 查看所有服务状态
.\start-windows.ps1 -Logs

# 查看特定服务日志
docker-compose -f server/docker-compose.windows.yml logs -f chroma
```

### 健康检查
```powershell
# 运行健康检查测试
.\fix-all-microservices-issues.ps1 -Test
```

## 总结

通过以上修复，解决了微服务启动过程中的主要问题：
- ✅ 修复了缺失的 NestJS 微服务依赖
- ✅ 改进了 Chroma 容器的健康检查机制
- ✅ 解决了前端编辑器的构建冲突问题

现在各个微服务应该能够正常注册到服务注册中心，并协同工作。
