/**
 * 输入管理器单元测试
 */
import { InputManager } from '../../src/input/InputManager';
import { InputDevice, BaseInputDevice } from '../../src/input/InputDevice';
import { ButtonInputAction } from '../../src/input/InputAction';
import { ButtonInputMapping } from '../../src/input/InputMapping';
import { InputBinding } from '../../src/input/InputBinding';

// 模拟输入设备
class MockInputDevice extends BaseInputDevice {
  constructor(name: string) {
    super(name);
  }
}

describe('InputManager', () => {
  let inputManager: InputManager;
  let device: MockInputDevice;
  let action: ButtonInputAction;
  let mapping: ButtonInputMapping;
  let binding: InputBinding;

  beforeEach(() => {
    // 清除单例实例
    (InputManager as any).instance = undefined;
    
    inputManager = new InputManager({
      enableKeyboard: false,
      enableMouse: false,
      enableTouch: false,
      enableGamepad: false,
      enableXR: false
    });
    
    device = new MockInputDevice('test');
    action = new ButtonInputAction('testAction');
    mapping = new ButtonInputMapping('testMapping', 'test', 'button1');
    binding = new InputBinding('testAction', 'testMapping');
  });

  test('应该正确获取单例实例', () => {
    const instance = InputManager.getInstance();
    expect(instance).toBe(inputManager);
  });

  test('应该正确添加和获取设备', () => {
    inputManager.addDevice(device);
    expect(inputManager.getDevice('test')).toBe(device);
  });

  test('应该正确添加和获取动作', () => {
    inputManager.addAction(action);
    expect(inputManager.getAction('testAction')).toBe(action);
  });

  test('应该正确添加和获取映射', () => {
    inputManager.addMapping(mapping);
    expect(inputManager.getMapping('testMapping')).toBe(mapping);
  });

  test('应该正确添加和获取绑定', () => {
    inputManager.addBinding(binding);
    expect(inputManager.getBinding('testAction')).toBe(binding);
  });

  test('应该正确初始化和销毁', () => {
    inputManager.addDevice(device);
    
    // 初始化前设备未初始化
    expect(device['initialized']).toBe(false);
    
    // 初始化
    inputManager.initialize();
    expect(device['initialized']).toBe(true);
    
    // 销毁
    inputManager.destroy();
    expect(device['destroyed']).toBe(true);
  });

  test('应该正确处理事件', () => {
    const mockCallback = jest.fn();
    
    inputManager.addDevice(device);
    inputManager.addAction(action);
    inputManager.addMapping(mapping);
    inputManager.addBinding(binding);
    
    inputManager.on('testAction', mockCallback);
    
    // 初始化
    inputManager.initialize();
    
    // 设置设备值
    device.setValue('button1', true);
    
    // 更新
    inputManager.update(0.016);
    
    // 检查动作状态
    expect(action.getValue()).toBe(true);
    
    // 检查事件回调
    expect(mockCallback).toHaveBeenCalled();
  });

  test('应该正确移除事件监听器', () => {
    const mockCallback = jest.fn();
    
    inputManager.addDevice(device);
    inputManager.addAction(action);
    inputManager.addMapping(mapping);
    inputManager.addBinding(binding);
    
    inputManager.on('testAction', mockCallback);
    
    // 初始化
    inputManager.initialize();
    
    // 设置设备值
    device.setValue('button1', true);
    
    // 更新
    inputManager.update(0.016);
    
    // 检查事件回调
    expect(mockCallback).toHaveBeenCalledTimes(1);
    
    // 移除事件监听器
    inputManager.off('testAction', mockCallback);
    
    // 重置设备值
    device.setValue('button1', false);
    device.setValue('button1', true);
    
    // 更新
    inputManager.update(0.016);
    
    // 检查事件回调
    expect(mockCallback).toHaveBeenCalledTimes(1);
  });
});
