/* 数据可视化示例样式 */

:root {
  --primary-color: #2196F3;
  --primary-dark: #0b7dda;
  --secondary-color: #4CAF50;
  --secondary-dark: #3e8e41;
  --accent-color: #FF9800;
  --accent-dark: #e68a00;
  --text-color: #f0f0f0;
  --background-color: #1a1a2e;
  --panel-background: rgba(0, 0, 0, 0.7);
  --panel-border: 1px solid rgba(255, 255, 255, 0.1);
  --input-background: #333;
  --input-border: 1px solid #555;
}

body {
  margin: 0;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
}

/* 布局 */
.example-container {
  display: flex;
  height: 100vh;
}

.control-panel {
  width: 300px;
  background-color: var(--panel-background);
  padding: 15px;
  overflow-y: auto;
  border-right: var(--panel-border);
}

.visualization-container {
  flex: 1;
  position: relative;
}

.info-panel {
  width: 300px;
  background-color: var(--panel-background);
  padding: 15px;
  overflow-y: auto;
  border-left: var(--panel-border);
}

/* 头部 */
.example-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: var(--panel-background);
  display: flex;
  align-items: center;
  padding: 0 20px;
  z-index: 100;
  border-bottom: var(--panel-border);
}

.example-header .logo {
  font-weight: bold;
  font-size: 1.2em;
  margin-right: 20px;
  color: var(--primary-color);
}

.example-header h1 {
  flex: 1;
  margin: 0;
  font-size: 1.5em;
}

.example-header .actions {
  display: flex;
  gap: 10px;
}

/* 场景容器 */
#scene-container {
  width: 100%;
  height: 100%;
}

/* 面板部分 */
.panel-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: var(--panel-border);
}

.panel-section:last-child {
  border-bottom: none;
}

.panel-section h2 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2em;
  color: var(--primary-color);
}

/* 表单元素 */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
}

.form-group input[type="range"] {
  width: 100%;
  background: var(--input-background);
}

.form-group select {
  width: 100%;
  padding: 8px;
  background: var(--input-background);
  color: var(--text-color);
  border: var(--input-border);
  border-radius: 4px;
}

/* 按钮 */
.btn {
  padding: 8px 15px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn:hover {
  background: var(--primary-dark);
}

.btn-primary {
  background: var(--secondary-color);
}

.btn-primary:hover {
  background: var(--secondary-dark);
}

.btn-icon {
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.radio-group label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-group input[type="radio"] {
  margin-right: 10px;
}

/* 滑块 */
.slider-container {
  margin-bottom: 15px;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 0.8em;
  color: #aaa;
}

.time-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 10px;
}

/* 工具提示 */
.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 4px;
  font-size: 0.9em;
  pointer-events: none;
  z-index: 1000;
  display: none;
  max-width: 300px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

/* 图例 */
.legend {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 4px;
  font-size: 0.9em;
  z-index: 100;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border-radius: 3px;
}

/* 统计面板 */
.stats-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.7);
  padding: 15px;
  border-radius: 4px;
  z-index: 100;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  max-width: 300px;
}

.stat-item {
  flex: 1 0 45%;
}

.stat-label {
  font-size: 0.8em;
  color: #aaa;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 1.2em;
  font-weight: bold;
}

/* 对话框 */
.dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.dialog-content {
  background: #222;
  padding: 20px;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.dialog-content h2 {
  margin-top: 0;
  color: var(--primary-color);
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* 帮助内容 */
.help-content {
  max-height: 60vh;
  overflow-y: auto;
  margin-bottom: 20px;
}

.help-content h3 {
  color: var(--accent-color);
  margin-top: 20px;
  margin-bottom: 10px;
}

.help-content ul {
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 5px;
}

/* 指令列表 */
.instructions {
  list-style-type: none;
  padding: 0;
}

.instructions li {
  margin-bottom: 10px;
  padding-left: 20px;
  position: relative;
}

.instructions li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--accent-color);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .example-container {
    flex-direction: column;
  }
  
  .control-panel, .info-panel {
    width: 100%;
    max-height: 300px;
  }
}

/* 加载动画 */
#loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.spinner {
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-top: 5px solid var(--primary-color);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
