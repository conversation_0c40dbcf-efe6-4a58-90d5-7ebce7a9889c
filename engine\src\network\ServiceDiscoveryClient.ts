/**
 * 服务发现客户端
 * 负责与服务注册中心通信，发现和注册服务
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';

/**
 * 服务发现客户端配置
 */
export interface ServiceDiscoveryClientConfig {
  /** 服务注册中心URL */
  registryUrl?: string;
  /** 心跳间隔（毫秒） */
  heartbeatInterval?: number;
  /** 服务发现缓存时间（毫秒） */
  discoveryCache?: number;
  /** 是否启用自动心跳 */
  enableAutoHeartbeat?: boolean;
  /** 是否启用服务发现缓存 */
  enableDiscoveryCache?: boolean;
  /** 重试次数 */
  retryCount?: number;
  /** 重试间隔（毫秒） */
  retryInterval?: number;
}

/**
 * 服务实例接口
 */
export interface ServiceInstance {
  /** 服务名称 */
  serviceName: string;
  /** 实例ID */
  instanceId: string;
  /** 主机名 */
  host: string;
  /** 端口 */
  port: number;
  /** 是否安全连接 */
  secure: boolean;
  /** 元数据 */
  metadata?: Record<string, any>;
  /** 健康状态 */
  status: 'UP' | 'DOWN' | 'UNKNOWN';
  /** 注册时间 */
  registrationTime: number;
  /** 最后心跳时间 */
  lastHeartbeatTime: number;
}

/**
 * 服务发现客户端
 * 负责与服务注册中心通信，发现和注册服务
 */
export class ServiceDiscoveryClient extends EventEmitter {
  /** 配置 */
  private config: Required<ServiceDiscoveryClientConfig>;
  
  /** 服务实例缓存 */
  private serviceCache: Map<string, { instances: ServiceInstance[]; timestamp: number }> = new Map();
  
  /** 已注册的服务实例 */
  private registeredInstances: Map<string, ServiceInstance> = new Map();
  
  /** 心跳定时器ID */
  private heartbeatTimerId: number | null = null;
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /**
   * 创建服务发现客户端
   * @param config 配置
   */
  constructor(config: ServiceDiscoveryClientConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      registryUrl: 'http://localhost:4010/api/registry',
      heartbeatInterval: 30000, // 30秒
      discoveryCache: 60000, // 1分钟
      enableAutoHeartbeat: true,
      enableDiscoveryCache: true,
      retryCount: 3,
      retryInterval: 1000,
      ...config,
    };
  }
  
  /**
   * 初始化客户端
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }
    
    // 如果启用自动心跳，则启动心跳定时器
    if (this.config.enableAutoHeartbeat) {
      this.startHeartbeat();
    }
    
    this.initialized = true;
    
    Debug.log('ServiceDiscoveryClient', 'Service discovery client initialized');
  }
  
  /**
   * 注册服务实例
   * @param serviceName 服务名称
   * @param host 主机名
   * @param port 端口
   * @param secure 是否安全连接
   * @param metadata 元数据
   * @returns 服务实例
   */
  public async registerService(
    serviceName: string,
    host: string,
    port: number,
    secure: boolean = false,
    metadata: Record<string, any> = {}
  ): Promise<ServiceInstance> {
    try {
      const instanceId = `${serviceName}-${host}-${port}-${Date.now()}`;
      
      const instance: ServiceInstance = {
        serviceName,
        instanceId,
        host,
        port,
        secure,
        metadata,
        status: 'UP',
        registrationTime: Date.now(),
        lastHeartbeatTime: Date.now(),
      };
      
      // 发送注册请求
      const response = await fetch(`${this.config.registryUrl}/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(instance),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to register service: ${response.statusText}`);
      }
      
      const registeredInstance = await response.json();
      
      // 添加到已注册实例映射表
      this.registeredInstances.set(instanceId, registeredInstance);
      
      // 触发注册事件
      this.emit('serviceRegistered', registeredInstance);
      
      Debug.log('ServiceDiscoveryClient', `Service ${serviceName} registered with instance ID ${instanceId}`);
      
      return registeredInstance;
    } catch (error) {
      Debug.error('ServiceDiscoveryClient', 'Failed to register service:', error);
      throw error;
    }
  }
  
  /**
   * 发送心跳
   * @param instanceId 实例ID
   */
  public async sendHeartbeat(instanceId: string): Promise<void> {
    const instance = this.registeredInstances.get(instanceId);
    
    if (!instance) {
      Debug.warn('ServiceDiscoveryClient', `Instance ${instanceId} not found for heartbeat`);
      return;
    }
    
    try {
      const response = await fetch(`${this.config.registryUrl}/heartbeat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serviceName: instance.serviceName,
          instanceId,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to send heartbeat: ${response.statusText}`);
      }
      
      // 更新最后心跳时间
      instance.lastHeartbeatTime = Date.now();
      
      Debug.log('ServiceDiscoveryClient', `Heartbeat sent for instance ${instanceId}`);
    } catch (error) {
      Debug.error('ServiceDiscoveryClient', 'Failed to send heartbeat:', error);
      
      // 尝试重新注册
      this.emit('heartbeatFailed', instanceId, error);
    }
  }
  
  /**
   * 发现服务实例
   * @param serviceName 服务名称
   * @returns 服务实例列表
   */
  public async discoverService(serviceName: string): Promise<ServiceInstance[]> {
    // 检查缓存
    if (this.config.enableDiscoveryCache) {
      const cached = this.serviceCache.get(serviceName);
      
      if (cached && Date.now() - cached.timestamp < this.config.discoveryCache) {
        return cached.instances;
      }
    }
    
    try {
      const response = await fetch(`${this.config.registryUrl}/discover`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serviceName,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to discover service: ${response.statusText}`);
      }
      
      const instances: ServiceInstance[] = await response.json();
      
      // 更新缓存
      if (this.config.enableDiscoveryCache) {
        this.serviceCache.set(serviceName, {
          instances,
          timestamp: Date.now(),
        });
      }
      
      Debug.log('ServiceDiscoveryClient', `Discovered ${instances.length} instances of service ${serviceName}`);
      
      return instances;
    } catch (error) {
      Debug.error('ServiceDiscoveryClient', 'Failed to discover service:', error);
      
      // 如果有缓存，则返回缓存的实例
      const cached = this.serviceCache.get(serviceName);
      if (cached) {
        Debug.warn('ServiceDiscoveryClient', `Using cached instances for service ${serviceName}`);
        return cached.instances;
      }
      
      throw error;
    }
  }
  
  /**
   * 注销服务实例
   * @param instanceId 实例ID
   */
  public async deregisterService(instanceId: string): Promise<void> {
    const instance = this.registeredInstances.get(instanceId);
    
    if (!instance) {
      Debug.warn('ServiceDiscoveryClient', `Instance ${instanceId} not found for deregistration`);
      return;
    }
    
    try {
      const response = await fetch(`${this.config.registryUrl}/services/${instance.serviceName}/instances/${instanceId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to deregister service: ${response.statusText}`);
      }
      
      // 从已注册实例映射表中移除
      this.registeredInstances.delete(instanceId);
      
      // 触发注销事件
      this.emit('serviceDeregistered', instanceId, instance);
      
      Debug.log('ServiceDiscoveryClient', `Service instance ${instanceId} deregistered`);
    } catch (error) {
      Debug.error('ServiceDiscoveryClient', 'Failed to deregister service:', error);
      throw error;
    }
  }
  
  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (this.heartbeatTimerId !== null) {
      return;
    }
    
    this.heartbeatTimerId = window.setInterval(() => {
      // 为所有已注册的实例发送心跳
      for (const instanceId of this.registeredInstances.keys()) {
        this.sendHeartbeat(instanceId).catch(error => {
          Debug.error('ServiceDiscoveryClient', `Heartbeat error for instance ${instanceId}:`, error);
        });
      }
    }, this.config.heartbeatInterval);
    
    Debug.log('ServiceDiscoveryClient', `Heartbeat started with interval ${this.config.heartbeatInterval}ms`);
  }
  
  /**
   * 停止心跳
   */
  public stopHeartbeat(): void {
    if (this.heartbeatTimerId !== null) {
      clearInterval(this.heartbeatTimerId);
      this.heartbeatTimerId = null;
      
      Debug.log('ServiceDiscoveryClient', 'Heartbeat stopped');
    }
  }
  
  /**
   * 销毁客户端
   */
  public destroy(): void {
    // 停止心跳
    this.stopHeartbeat();
    
    // 注销所有已注册的实例
    for (const instanceId of this.registeredInstances.keys()) {
      this.deregisterService(instanceId).catch(error => {
        Debug.error('ServiceDiscoveryClient', `Deregistration error for instance ${instanceId}:`, error);
      });
    }
    
    // 清除缓存
    this.serviceCache.clear();
    this.registeredInstances.clear();
    
    // 移除所有事件监听器
    this.removeAllListeners();
    
    this.initialized = false;
    
    Debug.log('ServiceDiscoveryClient', 'Service discovery client destroyed');
  }
}
