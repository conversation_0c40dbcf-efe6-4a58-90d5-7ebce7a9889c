/**
 * 资源版本控制示例
 */
export declare class ResourceVersioningExample {
    /** 资源管理器 */
    private resourceManager;
    /** 版本管理器 */
    private versionManager;
    /** 示例资源ID */
    private exampleResourceId;
    /** 示例资源URL */
    private exampleResourceUrl;
    /** 示例资源类型 */
    private exampleResourceType;
    /**
     * 创建资源版本控制示例实例
     */
    constructor();
    /**
     * 运行示例
     */
    run(): Promise<void>;
    /**
     * 加载资源
     */
    private loadResource;
    /**
     * 创建版本
     */
    private createVersions;
    /**
     * 模拟资源修改
     */
    private simulateResourceModification;
    /**
     * 比较版本
     */
    private compareVersions;
    /**
     * 回滚版本
     */
    private rollbackVersion;
    /**
     * 显示所有版本
     */
    private displayAllVersions;
    /**
     * 格式化大小
     * @param bytes 字节数
     * @returns 格式化后的大小字符串
     */
    private formatSize;
}
export declare const resourceVersioningExample: ResourceVersioningExample;
