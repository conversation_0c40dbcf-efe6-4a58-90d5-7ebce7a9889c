/**
 * 基础渲染测试
 * 用于测试渲染系统的基本功能
 */
import * as THREE from 'three';
import { RenderTestFramework, RenderTestSuite, TestScene } from './RenderTestFramework';
import { Scene } from '../../src/scene/Scene';
import { Camera, CameraType } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { Transform } from '../../src/scene/Transform';
import { Entity } from '../../src/core/Entity';

/**
 * 创建基础测试套件
 * @returns 基础测试套件
 */
export function createBasicRenderTestSuite(): RenderTestSuite {
  return {
    name: '基础渲染测试套件',
    scenes: [
      createBasicSceneTest(),
      createMaterialTest(),
      createLightingTest(),
      createShadowTest()
    ],
    rendererOptions: {
      width: 800,
      height: 600,
      antialias: true,
      shadows: true
    },
    renderSystemOptions: {
      enableShadows: true,
      enablePostProcessing: false
    }
  };
}

/**
 * 创建基础场景测试
 * @returns 基础场景测试
 */
function createBasicSceneTest(): TestScene {
  return {
    name: '基础场景测试',
    createScene: () => {
      // 创建场景
      const scene = new Scene();
      
      // 创建地面
      const groundEntity = new Entity('地面');
      groundEntity.addComponent(new Transform({
        position: { x: 0, y: -1, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 10, y: 0.1, z: 10 }
      }));
      
      // 创建地面几何体和材质
      const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
      const groundMaterial = new THREE.MeshStandardMaterial({
        color: 0x808080,
        roughness: 0.7,
        metalness: 0.0
      });
      
      // 创建地面网格
      const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
      groundMesh.castShadow = false;
      groundMesh.receiveShadow = true;
      
      // 添加网格到变换组件
      const groundTransform = groundEntity.getComponent('Transform') as Transform;
      groundTransform.getObject3D().add(groundMesh);
      
      // 添加地面实体到场景
      scene.addEntity(groundEntity);
      
      // 创建立方体
      const cubeEntity = new Entity('立方体');
      cubeEntity.addComponent(new Transform({
        position: { x: 0, y: 0.5, z: 0 },
        rotation: { x: 0, y: Math.PI / 4, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建立方体几何体和材质
      const cubeGeometry = new THREE.BoxGeometry(1, 1, 1);
      const cubeMaterial = new THREE.MeshStandardMaterial({
        color: 0x0000ff,
        roughness: 0.5,
        metalness: 0.5
      });
      
      // 创建立方体网格
      const cubeMesh = new THREE.Mesh(cubeGeometry, cubeMaterial);
      cubeMesh.castShadow = true;
      cubeMesh.receiveShadow = true;
      
      // 添加网格到变换组件
      const cubeTransform = cubeEntity.getComponent('Transform') as Transform;
      cubeTransform.getObject3D().add(cubeMesh);
      
      // 添加立方体实体到场景
      scene.addEntity(cubeEntity);
      
      // 创建光源
      const lightEntity = new Entity('光源');
      lightEntity.addComponent(new Transform({
        position: { x: 5, y: 5, z: 5 },
        rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建光源组件
      const light = new Light({
        type: LightType.DIRECTIONAL,
        color: 0xffffff,
        intensity: 1.0,
        castShadow: true
      });
      lightEntity.addComponent(light);
      
      // 添加光源实体到场景
      scene.addEntity(lightEntity);
      
      // 创建环境光
      const ambientLightEntity = new Entity('环境光');
      
      // 创建环境光组件
      const ambientLight = new Light({
        type: LightType.AMBIENT,
        color: 0x404040,
        intensity: 0.5
      });
      ambientLightEntity.addComponent(ambientLight);
      
      // 添加环境光实体到场景
      scene.addEntity(ambientLightEntity);
      
      return scene;
    },
    createCamera: () => {
      // 创建相机
      const camera = new Camera({
        type: CameraType.PERSPECTIVE,
        fov: 75,
        aspect: 800 / 600,
        near: 0.1,
        far: 1000,
        position: { x: 3, y: 3, z: 3 },
        lookAt: { x: 0, y: 0, z: 0 }
      });
      
      return camera;
    }
  };
}

/**
 * 创建材质测试
 * @returns 材质测试
 */
function createMaterialTest(): TestScene {
  return {
    name: '材质测试',
    createScene: () => {
      // 创建场景
      const scene = new Scene();
      
      // 创建地面
      const groundEntity = new Entity('地面');
      groundEntity.addComponent(new Transform({
        position: { x: 0, y: -1, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 10, y: 0.1, z: 10 }
      }));
      
      // 创建地面几何体和材质
      const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
      const groundMaterial = new THREE.MeshStandardMaterial({
        color: 0x808080,
        roughness: 0.7,
        metalness: 0.0
      });
      
      // 创建地面网格
      const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
      groundMesh.castShadow = false;
      groundMesh.receiveShadow = true;
      
      // 添加网格到变换组件
      const groundTransform = groundEntity.getComponent('Transform') as Transform;
      groundTransform.getObject3D().add(groundMesh);
      
      // 添加地面实体到场景
      scene.addEntity(groundEntity);
      
      // 创建不同材质的球体
      const materials = [
        { name: '标准材质', material: new THREE.MeshStandardMaterial({ color: 0xff0000, roughness: 0.5, metalness: 0.5 }) },
        { name: '物理材质', material: new THREE.MeshPhysicalMaterial({ color: 0x00ff00, roughness: 0.5, metalness: 0.5, clearcoat: 1.0 }) },
        { name: '基础材质', material: new THREE.MeshBasicMaterial({ color: 0x0000ff }) },
        { name: 'Lambert材质', material: new THREE.MeshLambertMaterial({ color: 0xffff00 }) },
        { name: 'Phong材质', material: new THREE.MeshPhongMaterial({ color: 0xff00ff, shininess: 100 }) }
      ];
      
      // 创建球体几何体
      const sphereGeometry = new THREE.SphereGeometry(0.5, 32, 32);
      
      // 创建球体实体
      for (let i = 0; i < materials.length; i++) {
        const x = (i - 2) * 1.5;
        
        const sphereEntity = new Entity(`球体_${materials[i].name}`);
        sphereEntity.addComponent(new Transform({
          position: { x, y: 0.5, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          scale: { x: 1, y: 1, z: 1 }
        }));
        
        // 创建球体网格
        const sphereMesh = new THREE.Mesh(sphereGeometry, materials[i].material);
        sphereMesh.castShadow = true;
        sphereMesh.receiveShadow = true;
        
        // 添加网格到变换组件
        const sphereTransform = sphereEntity.getComponent('Transform') as Transform;
        sphereTransform.getObject3D().add(sphereMesh);
        
        // 添加球体实体到场景
        scene.addEntity(sphereEntity);
      }
      
      // 创建光源
      const lightEntity = new Entity('光源');
      lightEntity.addComponent(new Transform({
        position: { x: 5, y: 5, z: 5 },
        rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建光源组件
      const light = new Light({
        type: LightType.DIRECTIONAL,
        color: 0xffffff,
        intensity: 1.0,
        castShadow: true
      });
      lightEntity.addComponent(light);
      
      // 添加光源实体到场景
      scene.addEntity(lightEntity);
      
      // 创建环境光
      const ambientLightEntity = new Entity('环境光');
      
      // 创建环境光组件
      const ambientLight = new Light({
        type: LightType.AMBIENT,
        color: 0x404040,
        intensity: 0.5
      });
      ambientLightEntity.addComponent(ambientLight);
      
      // 添加环境光实体到场景
      scene.addEntity(ambientLightEntity);
      
      return scene;
    },
    createCamera: () => {
      // 创建相机
      const camera = new Camera({
        type: CameraType.PERSPECTIVE,
        fov: 75,
        aspect: 800 / 600,
        near: 0.1,
        far: 1000,
        position: { x: 0, y: 3, z: 5 },
        lookAt: { x: 0, y: 0, z: 0 }
      });
      
      return camera;
    }
  };
}

/**
 * 创建光照测试
 * @returns 光照测试
 */
function createLightingTest(): TestScene {
  return {
    name: '光照测试',
    createScene: () => {
      // 创建场景
      const scene = new Scene();
      
      // 创建地面
      const groundEntity = new Entity('地面');
      groundEntity.addComponent(new Transform({
        position: { x: 0, y: -1, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 10, y: 0.1, z: 10 }
      }));
      
      // 创建地面几何体和材质
      const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
      const groundMaterial = new THREE.MeshStandardMaterial({
        color: 0x808080,
        roughness: 0.7,
        metalness: 0.0
      });
      
      // 创建地面网格
      const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
      groundMesh.castShadow = false;
      groundMesh.receiveShadow = true;
      
      // 添加网格到变换组件
      const groundTransform = groundEntity.getComponent('Transform') as Transform;
      groundTransform.getObject3D().add(groundMesh);
      
      // 添加地面实体到场景
      scene.addEntity(groundEntity);
      
      // 创建球体
      const sphereEntity = new Entity('球体');
      sphereEntity.addComponent(new Transform({
        position: { x: 0, y: 0.5, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建球体几何体和材质
      const sphereGeometry = new THREE.SphereGeometry(0.5, 32, 32);
      const sphereMaterial = new THREE.MeshStandardMaterial({
        color: 0xffffff,
        roughness: 0.5,
        metalness: 0.5
      });
      
      // 创建球体网格
      const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial);
      sphereMesh.castShadow = true;
      sphereMesh.receiveShadow = true;
      
      // 添加网格到变换组件
      const sphereTransform = sphereEntity.getComponent('Transform') as Transform;
      sphereTransform.getObject3D().add(sphereMesh);
      
      // 添加球体实体到场景
      scene.addEntity(sphereEntity);
      
      // 创建不同类型的光源
      const lightTypes = [
        { type: LightType.DIRECTIONAL, position: { x: 5, y: 5, z: 5 }, color: 0xff0000, intensity: 1.0 },
        { type: LightType.POINT, position: { x: -3, y: 2, z: 0 }, color: 0x00ff00, intensity: 1.0 },
        { type: LightType.SPOT, position: { x: 0, y: 3, z: -3 }, color: 0x0000ff, intensity: 1.0 },
        { type: LightType.AMBIENT, position: { x: 0, y: 0, z: 0 }, color: 0x404040, intensity: 0.2 }
      ];
      
      // 创建光源实体
      for (let i = 0; i < lightTypes.length; i++) {
        const lightEntity = new Entity(`光源_${lightTypes[i].type}`);
        
        if (lightTypes[i].type !== LightType.AMBIENT) {
          lightEntity.addComponent(new Transform({
            position: lightTypes[i].position,
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1, y: 1, z: 1 }
          }));
        }
        
        // 创建光源组件
        const light = new Light({
          type: lightTypes[i].type,
          color: lightTypes[i].color,
          intensity: lightTypes[i].intensity,
          castShadow: lightTypes[i].type === LightType.DIRECTIONAL || lightTypes[i].type === LightType.SPOT
        });
        lightEntity.addComponent(light);
        
        // 添加光源实体到场景
        scene.addEntity(lightEntity);
      }
      
      return scene;
    },
    createCamera: () => {
      // 创建相机
      const camera = new Camera({
        type: CameraType.PERSPECTIVE,
        fov: 75,
        aspect: 800 / 600,
        near: 0.1,
        far: 1000,
        position: { x: 5, y: 5, z: 5 },
        lookAt: { x: 0, y: 0, z: 0 }
      });
      
      return camera;
    }
  };
}

/**
 * 创建阴影测试
 * @returns 阴影测试
 */
function createShadowTest(): TestScene {
  return {
    name: '阴影测试',
    createScene: () => {
      // 创建场景
      const scene = new Scene();
      
      // 创建地面
      const groundEntity = new Entity('地面');
      groundEntity.addComponent(new Transform({
        position: { x: 0, y: -1, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 10, y: 0.1, z: 10 }
      }));
      
      // 创建地面几何体和材质
      const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
      const groundMaterial = new THREE.MeshStandardMaterial({
        color: 0x808080,
        roughness: 0.7,
        metalness: 0.0
      });
      
      // 创建地面网格
      const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
      groundMesh.castShadow = false;
      groundMesh.receiveShadow = true;
      
      // 添加网格到变换组件
      const groundTransform = groundEntity.getComponent('Transform') as Transform;
      groundTransform.getObject3D().add(groundMesh);
      
      // 添加地面实体到场景
      scene.addEntity(groundEntity);
      
      // 创建不同形状的物体
      const shapes = [
        { name: '立方体', geometry: new THREE.BoxGeometry(1, 1, 1), position: { x: -2, y: 0.5, z: 0 } },
        { name: '球体', geometry: new THREE.SphereGeometry(0.5, 32, 32), position: { x: 0, y: 0.5, z: 0 } },
        { name: '圆柱体', geometry: new THREE.CylinderGeometry(0.5, 0.5, 1, 32), position: { x: 2, y: 0.5, z: 0 } }
      ];
      
      // 创建物体实体
      for (const shape of shapes) {
        const entity = new Entity(shape.name);
        entity.addComponent(new Transform({
          position: shape.position,
          rotation: { x: 0, y: 0, z: 0 },
          scale: { x: 1, y: 1, z: 1 }
        }));
        
        // 创建材质
        const material = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          roughness: 0.5,
          metalness: 0.5
        });
        
        // 创建网格
        const mesh = new THREE.Mesh(shape.geometry, material);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        
        // 添加网格到变换组件
        const transform = entity.getComponent('Transform') as Transform;
        transform.getObject3D().add(mesh);
        
        // 添加实体到场景
        scene.addEntity(entity);
      }
      
      // 创建方向光
      const directionalLightEntity = new Entity('方向光');
      directionalLightEntity.addComponent(new Transform({
        position: { x: 5, y: 5, z: 5 },
        rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建方向光组件
      const directionalLight = new Light({
        type: LightType.DIRECTIONAL,
        color: 0xffffff,
        intensity: 1.0,
        castShadow: true
      });
      directionalLightEntity.addComponent(directionalLight);
      
      // 添加方向光实体到场景
      scene.addEntity(directionalLightEntity);
      
      // 创建环境光
      const ambientLightEntity = new Entity('环境光');
      
      // 创建环境光组件
      const ambientLight = new Light({
        type: LightType.AMBIENT,
        color: 0x404040,
        intensity: 0.5
      });
      ambientLightEntity.addComponent(ambientLight);
      
      // 添加环境光实体到场景
      scene.addEntity(ambientLightEntity);
      
      return scene;
    },
    createCamera: () => {
      // 创建相机
      const camera = new Camera({
        type: CameraType.PERSPECTIVE,
        fov: 75,
        aspect: 800 / 600,
        near: 0.1,
        far: 1000,
        position: { x: 5, y: 5, z: 5 },
        lookAt: { x: 0, y: 0, z: 0 }
      });
      
      return camera;
    }
  };
}

/**
 * 运行基础渲染测试
 */
export async function runBasicRenderTests(): Promise<void> {
  // 创建测试框架
  const testFramework = new RenderTestFramework();
  
  // 创建测试套件
  const testSuite = createBasicRenderTestSuite();
  
  // 运行测试套件
  const results = await testFramework.runTestSuite(testSuite);
  
  // 输出测试结果
  console.log('测试结果:', results);
}
