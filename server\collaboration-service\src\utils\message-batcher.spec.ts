import { MessageBatcher } from './message-batcher';

describe('MessageBatcher', () => {
  let processCallback: jest.Mock;
  let batcher: MessageBatcher<any>;

  beforeEach(() => {
    jest.useFakeTimers();
    processCallback = jest.fn().mockResolvedValue(undefined);
    
    batcher = new MessageBatcher(processCallback, {
      maxBatchSize: 3,
      maxWaitTime: 100,
      enabled: true,
    });
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should batch messages and process them when reaching maxBatchSize', () => {
    // 添加消息到批处理器
    batcher.add({ id: 1 });
    batcher.add({ id: 2 });
    
    // 验证回调尚未被调用
    expect(processCallback).not.toHaveBeenCalled();
    
    // 添加第三条消息，达到最大批处理大小
    batcher.add({ id: 3 });
    
    // 验证回调被调用，并且传入了所有三条消息
    expect(processCallback).toHaveBeenCalledWith([
      { id: 1 },
      { id: 2 },
      { id: 3 },
    ]);
  });

  it('should batch messages and process them after maxWaitTime', () => {
    // 添加消息到批处理器
    batcher.add({ id: 1 });
    batcher.add({ id: 2 });
    
    // 验证回调尚未被调用
    expect(processCallback).not.toHaveBeenCalled();
    
    // 前进时间，超过最大等待时间
    jest.advanceTimersByTime(101);
    
    // 验证回调被调用，并且传入了两条消息
    expect(processCallback).toHaveBeenCalledWith([
      { id: 1 },
      { id: 2 },
    ]);
  });

  it('should process messages immediately when batching is disabled', () => {
    // 禁用批处理
    batcher.updateOptions({ enabled: false });
    
    // 添加消息到批处理器
    batcher.add({ id: 1 });
    
    // 验证回调立即被调用
    expect(processCallback).toHaveBeenCalledWith([{ id: 1 }]);
    
    // 添加另一条消息
    batcher.add({ id: 2 });
    
    // 验证回调再次被调用
    expect(processCallback).toHaveBeenCalledWith([{ id: 2 }]);
    expect(processCallback).toHaveBeenCalledTimes(2);
  });

  it('should flush messages when calling flush', () => {
    // 添加消息到批处理器
    batcher.add({ id: 1 });
    batcher.add({ id: 2 });
    
    // 验证回调尚未被调用
    expect(processCallback).not.toHaveBeenCalled();
    
    // 手动刷新
    batcher.flush();
    
    // 验证回调被调用，并且传入了两条消息
    expect(processCallback).toHaveBeenCalledWith([
      { id: 1 },
      { id: 2 },
    ]);
  });

  it('should flush messages when calling destroy', () => {
    // 添加消息到批处理器
    batcher.add({ id: 1 });
    
    // 验证回调尚未被调用
    expect(processCallback).not.toHaveBeenCalled();
    
    // 销毁批处理器
    batcher.destroy();
    
    // 验证回调被调用
    expect(processCallback).toHaveBeenCalledWith([{ id: 1 }]);
  });

  it('should update options correctly', () => {
    // 更新选项
    batcher.updateOptions({
      maxBatchSize: 5,
      maxWaitTime: 200,
    });
    
    // 添加消息到批处理器
    batcher.add({ id: 1 });
    batcher.add({ id: 2 });
    batcher.add({ id: 3 });
    
    // 验证回调尚未被调用（因为新的maxBatchSize是5）
    expect(processCallback).not.toHaveBeenCalled();
    
    // 添加更多消息，达到新的最大批处理大小
    batcher.add({ id: 4 });
    batcher.add({ id: 5 });
    
    // 验证回调被调用
    expect(processCallback).toHaveBeenCalledWith([
      { id: 1 },
      { id: 2 },
      { id: 3 },
      { id: 4 },
      { id: 5 },
    ]);
  });
});
