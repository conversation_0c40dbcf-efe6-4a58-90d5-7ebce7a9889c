import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AgonesService } from './agones/agones.service';
import { InstanceService } from './instance/instance.service';
import { WebRTCService } from './webrtc/webrtc.service';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);
  private startTime: Date;

  constructor(
    private readonly configService: ConfigService,
    private readonly agonesService: AgonesService,
    private readonly instanceService: InstanceService,
    private readonly webrtcService: WebRTCService,
  ) {
    this.startTime = new Date();
  }

  getStatus() {
    const uptime = Math.floor((Date.now() - this.startTime.getTime()) / 1000);
    
    return {
      status: 'running',
      version: this.configService.get<string>('APP_VERSION', '1.0.0'),
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      uptime,
      startTime: this.startTime.toISOString(),
    };
  }

  getHealth() {
    const instances = this.instanceService.getAllInstances();
    const availableInstances = this.instanceService.getAvailableInstances();
    
    return {
      status: 'ok',
      agones: {
        connected: true, // 简化，实际应该从AgonesService获取
      },
      instances: {
        total: instances.length,
        available: availableInstances.length,
      },
      webrtc: {
        workers: this.webrtcService.getWorkers().length,
        routers: this.webrtcService.getRouters().length,
      },
    };
  }
}
