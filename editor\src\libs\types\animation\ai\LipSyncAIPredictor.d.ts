/**
 * 口型同步AI预测器
 * 使用AI模型预测音频对应的口型，提高口型识别的准确性
 */
import { VisemeType } from '../FacialAnimation';
/**
 * 口型同步AI预测器配置
 */
export interface LipSyncAIPredictorConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 模型路径 */
    modelPath?: string;
    /** 是否使用本地模型 */
    useLocalModel?: boolean;
    /** 是否使用在线学习 */
    useOnlineLearning?: boolean;
    /** 批处理大小 */
    batchSize?: number;
    /** 上下文窗口大小 */
    contextWindowSize?: number;
    /** 预测置信度阈值 */
    confidenceThreshold?: number;
    /** 是否使用MFCC特征 */
    useMFCC?: boolean;
    /** 是否使用频谱图特征 */
    useSpectrogram?: boolean;
    /** 是否使用上下文信息 */
    useContext?: boolean;
}
/**
 * 预测结果
 */
export interface PredictionResult {
    /** 预测的口型 */
    viseme: VisemeType;
    /** 置信度 */
    confidence: number;
    /** 备选口型及其置信度 */
    alternatives?: Map<VisemeType, number>;
}
/**
 * 口型同步AI预测器
 * 使用AI模型预测音频对应的口型
 */
export declare class LipSyncAIPredictor {
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 事件发射器 */
    private eventEmitter;
    /** 模型是否已加载 */
    private modelLoaded;
    /** 模型加载进度 */
    private modelLoadProgress;
    /** 模型 */
    private model;
    /** 上下文历史 */
    private contextHistory;
    /** 口型历史 */
    private visemeHistory;
    /** 音素到口型映射 */
    private phonemeToVisemeMap;
    /** 是否支持WebGL */
    private supportsWebGL;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<LipSyncAIPredictorConfig>);
    /**
     * 初始化音素到口型映射
     */
    private initPhonemeToVisemeMap;
    /**
     * 检查WebGL支持
     */
    private checkWebGLSupport;
    /**
     * 初始化模型
     * @returns 是否成功
     */
    initialize(): Promise<boolean>;
    /**
     * 模拟预测
     * @param features 特征
     * @returns 预测结果
     */
    private mockPredict;
    /**
     * 预测口型
     * @param features 音频特征
     * @returns 预测结果
     */
    predict(features: Float32Array): PredictionResult;
    /**
     * 更新上下文历史
     * @param features 特征
     */
    private updateContextHistory;
    /**
     * 更新口型历史
     * @param viseme 口型
     */
    private updateVisemeHistory;
    /**
     * 基于上下文预测口型
     * @param currentViseme 当前口型
     * @param predictions 预测结果
     * @returns 预测结果
     */
    private predictFromContext;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    on(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    off(event: string, callback: (...args: any[]) => void): void;
}
