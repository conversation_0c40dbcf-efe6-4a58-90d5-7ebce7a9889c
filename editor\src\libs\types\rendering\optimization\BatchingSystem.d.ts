/**
 * 批处理系统
 * 用于合并多个网格以减少绘制调用
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { Scene } from '../../scene/Scene';
/**
 * 批处理系统配置接口
 */
export interface BatchingSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率（帧） */
    updateFrequency?: number;
    /** 是否使用静态批处理 */
    useStaticBatching?: boolean;
    /** 是否使用动态批处理 */
    useDynamicBatching?: boolean;
    /** 是否使用GPU实例化 */
    useGPUInstancing?: boolean;
    /** 最大批处理大小 */
    maxBatchSize?: number;
    /** 最大实例数量 */
    maxInstanceCount?: number;
    /** 是否优化几何体 */
    optimizeGeometry?: boolean;
    /** 是否合并材质 */
    mergeMaterials?: boolean;
    /** 是否使用八叉树 */
    useOctree?: boolean;
}
/**
 * 批处理系统事件类型
 */
export declare enum BatchingSystemEventType {
    /** 批处理创建 */
    BATCH_CREATED = "batch_created",
    /** 批处理更新 */
    BATCH_UPDATED = "batch_updated",
    /** 批处理销毁 */
    BATCH_DESTROYED = "batch_destroyed",
    /** 实例添加 */
    INSTANCE_ADDED = "instance_added",
    /** 实例更新 */
    INSTANCE_UPDATED = "instance_updated",
    /** 实例移除 */
    INSTANCE_REMOVED = "instance_removed"
}
/**
 * 批处理组
 */
export interface BatchGroup {
    /** 批处理ID */
    id: string;
    /** 批处理名称 */
    name: string;
    /** 合并后的网格 */
    mesh: THREE.Mesh | THREE.InstancedMesh;
    /** 原始网格列表 */
    originalMeshes: THREE.Mesh[];
    /** 原始实体列表 */
    originalEntities: Entity[];
    /** 是否是静态批处理 */
    isStatic: boolean;
    /** 是否是实例化批处理 */
    isInstanced: boolean;
    /** 变换矩阵 */
    matrices?: THREE.Matrix4[];
    /** 颜色数组 */
    colors?: THREE.Color[];
    /** 是否可见 */
    visible: boolean;
    /** 是否已更新 */
    isDirty: boolean;
}
/**
 * 批处理系统
 */
export declare class BatchingSystem extends System {
    /** 系统类型 */
    private static readonly TYPE;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率（帧） */
    private updateFrequency;
    /** 当前帧计数 */
    private frameCount;
    /** 是否使用静态批处理 */
    private useStaticBatching;
    /** 是否使用动态批处理 */
    private useDynamicBatching;
    /** 是否使用GPU实例化 */
    private useGPUInstancing;
    /** 最大批处理大小 */
    private maxBatchSize;
    /** 最大实例数量 */
    private maxInstanceCount;
    /** 是否优化几何体 */
    private optimizeGeometry;
    /** 是否合并材质 */
    private shouldMergeMaterials;
    /** 是否使用八叉树 */
    private useOctree;
    /** 活跃场景 */
    private activeScene;
    /** 批处理组列表 */
    private batchGroups;
    /** 实体到批处理组的映射 */
    private entityToBatchMap;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 创建批处理系统
     * @param options 批处理系统配置
     */
    constructor(options?: BatchingSystemOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 设置活跃场景
     * @param scene 场景
     */
    setActiveScene(scene: Scene): void;
    /**
     * 获取活跃场景
     * @returns 活跃场景
     */
    getActiveScene(): Scene | null;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新所有批处理组
     */
    private updateBatchGroups;
    /**
     * 更新批处理组
     * @param group 批处理组
     */
    private updateBatchGroup;
    /**
     * 更新实例化批处理组
     * @param group 批处理组
     */
    private updateInstancedBatchGroup;
    /**
     * 更新合并批处理组
     * @param group 批处理组
     */
    private updateMergedBatchGroup;
    /**
     * 创建静态批处理组
     * @param entities 实体列表
     * @param name 批处理组名称
     * @returns 批处理组ID
     */
    createStaticBatch(entities: Entity[], name?: string): string | null;
    /**
     * 创建动态批处理组
     * @param entities 实体列表
     * @param name 批处理组名称
     * @returns 批处理组ID
     */
    createDynamicBatch(entities: Entity[], name?: string): string | null;
    /**
     * 创建实例化批处理组
     * @param entities 实体列表
     * @param name 批处理组名称
     * @returns 批处理组ID
     */
    createInstancedBatch(entities: Entity[], name?: string): string | null;
    /**
     * 创建合并批处理组
     * @param entities 实体列表
     * @param name 批处理组名称
     * @param isStatic 是否是静态批处理
     * @returns 批处理组ID
     */
    private createMergedBatch;
    /**
     * 创建合并网格
     * @param meshes 网格列表
     * @param entities 实体列表
     * @returns 合并后的网格
     */
    private createMergedMesh;
    /**
     * 合并几何体
     * @param geometries 几何体列表
     * @returns 合并后的几何体
     */
    private mergeGeometries;
    /**
     * 合并材质
     * @param materials 材质列表
     * @returns 合并后的材质
     */
    private mergeMaterials;
    /**
     * 检查是否可以使用实例化
     * @param entities 实体列表
     * @returns 是否可以使用实例化
     */
    private canUseInstancing;
    /**
     * 销毁批处理组
     * @param batchId 批处理组ID
     * @returns 是否成功销毁
     */
    destroyBatch(batchId: string): boolean;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: BatchingSystemEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: BatchingSystemEventType, listener: (...args: any[]) => void): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
