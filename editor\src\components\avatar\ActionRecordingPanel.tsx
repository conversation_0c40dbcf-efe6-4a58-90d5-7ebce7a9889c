/**
 * 动作录制面板
 * 用于录制和回放角色动作
 */
import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Button, 
  Input, 
  Space, 
  Divider, 
  Slider, 
  Switch, 
  List, 
  Typography, 
  Tooltip, 
  Modal, 
  Form, 
  message,
  Tag
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  DownloadOutlined,
  ClockCircleOutlined,
  SettingOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Upload } from 'antd';

const { Text, Title } = Typography;
const { Dragger } = Upload;

/**
 * 动作事件接口
 */
interface ActionEvent {
  id: string;
  timestamp: number;
  type: string;
  data: any;
}

/**
 * 输入事件接口
 */
interface InputEvent {
  id: string;
  timestamp: number;
  type: string;
  data: any;
}

/**
 * 变换事件接口
 */
interface TransformEvent {
  id: string;
  timestamp: number;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number; w: number };
  scale: { x: number; y: number; z: number };
}

/**
 * 动作录制接口
 */
interface ActionRecording {
  id: string;
  name: string;
  startTimestamp: number;
  endTimestamp: number;
  actionEvents: ActionEvent[];
  inputEvents: InputEvent[];
  transformEvents: TransformEvent[];
}

/**
 * 录制状态枚举
 */
enum RecordingState {
  IDLE = 'idle',
  RECORDING = 'recording',
  PAUSED = 'paused',
  PLAYING = 'playing',
  PLAY_PAUSED = 'play_paused'
}

/**
 * 动作录制面板属性
 */
interface ActionRecordingPanelProps {
  entityId?: string;
  onRecord?: (entityId: string) => void;
  onStopRecord?: (entityId: string) => ActionRecording | null;
  onPlay?: (entityId: string, recordingId: string, speed: number) => void;
  onPause?: (entityId: string) => void;
  onResume?: (entityId: string) => void;
  onStop?: (entityId: string) => void;
  onLoad?: (recording: ActionRecording) => void;
  onDelete?: (recordingId: string) => void;
}

/**
 * 动作录制面板组件
 */
const ActionRecordingPanel: React.FC<ActionRecordingPanelProps> = ({
  entityId,
  onRecord,
  onStopRecord,
  onPlay,
  onPause,
  onResume,
  onStop,
  onLoad,
  onDelete
}) => {
  const { t } = useTranslation();

  // 状态
  const [recordingState, setRecordingState] = useState<RecordingState>(RecordingState.IDLE);
  const [recordingName, setRecordingName] = useState<string>('');
  const [playbackSpeed, setPlaybackSpeed] = useState<number>(1.0);
  const [elapsedTime, setElapsedTime] = useState<number>(0);
  const [recordings, setRecordings] = useState<ActionRecording[]>([]);
  const [selectedRecording, setSelectedRecording] = useState<ActionRecording | null>(null);
  const [isSettingsVisible, setIsSettingsVisible] = useState<boolean>(false);
  const [isImportModalVisible, setIsImportModalVisible] = useState<boolean>(false);
  const [isExportModalVisible, setIsExportModalVisible] = useState<boolean>(false);
  const [recordSettings, setRecordSettings] = useState({
    recordInput: true,
    recordTransform: true,
    transformFrequency: 100
  });
  const [playSettings, setPlaySettings] = useState({
    playInput: true,
    playTransform: true,
    loop: false
  });

  // 引用
  const timerRef = useRef<number | null>(null);
  const startTimeRef = useRef<number>(0);
  const currentRecordingRef = useRef<ActionRecording | null>(null);
  
  // 初始化
  useEffect(() => {
    // 加载保存的录制
    loadSavedRecordings();
    
    // 清理函数
    return () => {
      if (timerRef.current !== null) {
        clearInterval(timerRef.current);
      }
    };
  }, []);
  
  // 加载保存的录制
  const loadSavedRecordings = () => {
    try {
      const savedRecordings = localStorage.getItem('actionRecordings');
      if (savedRecordings) {
        setRecordings(JSON.parse(savedRecordings));
      }
    } catch (error) {
      console.error('加载保存的录制失败:', error);
    }
  };
  
  // 保存录制列表
  const saveRecordingsList = (list: ActionRecording[]) => {
    try {
      localStorage.setItem('actionRecordings', JSON.stringify(list));
    } catch (error) {
      console.error('保存录制列表失败:', error);
    }
  };
  
  // 开始录制
  const handleStartRecording = () => {
    if (!entityId) {
      message.error(t('editor.avatar.recording.noEntitySelected'));
      return;
    }
    
    // 设置录制名称
    const name = recordingName.trim() || `录制_${new Date().toLocaleString()}`;
    setRecordingName(name);
    
    // 开始计时
    startTimeRef.current = Date.now();
    setElapsedTime(0);
    
    if (timerRef.current !== null) {
      clearInterval(timerRef.current);
    }
    
    timerRef.current = window.setInterval(() => {
      setElapsedTime(Date.now() - startTimeRef.current);
    }, 100);
    
    // 更新状态
    setRecordingState(RecordingState.RECORDING);
    
    // 调用录制回调
    if (onRecord) {
      onRecord(entityId);
    }
    
    message.success(t('editor.avatar.recording.startedRecording'));
  };
  
  // 暂停录制
  const handlePauseRecording = () => {
    if (recordingState !== RecordingState.RECORDING) return;
    
    // 暂停计时
    if (timerRef.current !== null) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    
    // 更新状态
    setRecordingState(RecordingState.PAUSED);
    
    message.info(t('editor.avatar.recording.pausedRecording'));
  };
  
  // 继续录制
  const handleResumeRecording = () => {
    if (recordingState !== RecordingState.PAUSED) return;
    
    // 调整开始时间以考虑暂停时间
    startTimeRef.current = Date.now() - elapsedTime;
    
    // 重新开始计时
    timerRef.current = window.setInterval(() => {
      setElapsedTime(Date.now() - startTimeRef.current);
    }, 100);
    
    // 更新状态
    setRecordingState(RecordingState.RECORDING);
    
    message.info(t('editor.avatar.recording.resumedRecording'));
  };
  
  // 停止录制
  const handleStopRecording = () => {
    if (recordingState !== RecordingState.RECORDING && recordingState !== RecordingState.PAUSED) return;
    
    // 停止计时
    if (timerRef.current !== null) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    
    // 调用停止录制回调
    if (onStopRecord && entityId) {
      const recording = onStopRecord(entityId);
      if (recording) {
        currentRecordingRef.current = recording;
        
        // 添加到录制列表
        const updatedRecordings = [...recordings, recording];
        setRecordings(updatedRecordings);
        saveRecordingsList(updatedRecordings);
        
        // 选择当前录制
        setSelectedRecording(recording);
      }
    }
    
    // 更新状态
    setRecordingState(RecordingState.IDLE);
    
    message.success(t('editor.avatar.recording.stoppedRecording'));
  };
  
  // 播放录制
  const handlePlayRecording = () => {
    if (!entityId || !selectedRecording) {
      message.error(t('editor.avatar.recording.noRecordingSelected'));
      return;
    }
    
    // 更新状态
    setRecordingState(RecordingState.PLAYING);
    
    // 调用播放回调
    if (onPlay) {
      onPlay(entityId, selectedRecording.id, playbackSpeed);
    }
    
    message.success(t('editor.avatar.recording.startedPlayback'));
  };
  
  // 暂停播放
  const handlePausePlayback = () => {
    if (recordingState !== RecordingState.PLAYING) return;
    
    // 更新状态
    setRecordingState(RecordingState.PLAY_PAUSED);
    
    // 调用暂停回调
    if (onPause && entityId) {
      onPause(entityId);
    }
    
    message.info(t('editor.avatar.recording.pausedPlayback'));
  };
  
  // 继续播放
  const handleResumePlayback = () => {
    if (recordingState !== RecordingState.PLAY_PAUSED) return;
    
    // 更新状态
    setRecordingState(RecordingState.PLAYING);
    
    // 调用继续回调
    if (onResume && entityId) {
      onResume(entityId);
    }
    
    message.info(t('editor.avatar.recording.resumedPlayback'));
  };
  
  // 停止播放
  const handleStopPlayback = () => {
    if (recordingState !== RecordingState.PLAYING && recordingState !== RecordingState.PLAY_PAUSED) return;
    
    // 更新状态
    setRecordingState(RecordingState.IDLE);
    
    // 调用停止回调
    if (onStop && entityId) {
      onStop(entityId);
    }
    
    message.success(t('editor.avatar.recording.stoppedPlayback'));
  };
  

  
  // 删除录制
  const handleDeleteRecording = (recordingId: string) => {
    Modal.confirm({
      title: t('editor.avatar.recording.confirmDelete'),
      content: t('editor.avatar.recording.deleteWarning'),
      okText: t('editor.common.delete'),
      okType: 'danger',
      cancelText: t('editor.common.cancel'),
      onOk: () => {
        // 从列表中删除
        const updatedRecordings = recordings.filter(r => r.id !== recordingId);
        setRecordings(updatedRecordings);
        saveRecordingsList(updatedRecordings);
        
        // 如果删除的是当前选中的录制，清除选择
        if (selectedRecording && selectedRecording.id === recordingId) {
          setSelectedRecording(null);
        }
        
        // 调用删除回调
        if (onDelete) {
          onDelete(recordingId);
        }
        
        message.success(t('editor.avatar.recording.deletedRecording'));
      }
    });
  };
  
  // 导入录制
  const handleImportRecording = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const json = e.target?.result as string;
        const recording = JSON.parse(json) as ActionRecording;
        
        // 添加到录制列表
        const updatedRecordings = [...recordings, recording];
        setRecordings(updatedRecordings);
        saveRecordingsList(updatedRecordings);
        
        // 选择导入的录制
        setSelectedRecording(recording);
        
        // 调用加载回调
        if (onLoad) {
          onLoad(recording);
        }
        
        message.success(t('editor.avatar.recording.importedRecording'));
      } catch (error) {
        console.error('导入录制失败:', error);
        message.error(t('editor.avatar.recording.importFailed'));
      }
    };
    reader.onerror = () => {
      message.error(t('editor.avatar.recording.importFailed'));
    };
    reader.readAsText(file);
    
    // 关闭导入模态框
    setIsImportModalVisible(false);
    
    // 阻止上传
    return false;
  };
  
  // 导出录制
  const handleExportRecording = () => {
    if (!selectedRecording) {
      message.error(t('editor.avatar.recording.noRecordingSelected'));
      return;
    }
    
    const json = JSON.stringify(selectedRecording, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${selectedRecording.name.replace(/\s+/g, '_')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success(t('editor.avatar.recording.exportedRecording'));
    
    // 关闭导出模态框
    setIsExportModalVisible(false);
  };
  
  // 格式化时间
  const formatTime = (ms: number): string => {
    const totalSeconds = Math.floor(ms / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const milliseconds = Math.floor((ms % 1000) / 10);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(2, '0')}`;
  };
  
  // 渲染录制控制按钮
  const renderRecordingControls = () => {
    switch (recordingState) {
      case RecordingState.IDLE:
        return (
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={handleStartRecording}
            disabled={!entityId}
          >
            {t('editor.avatar.recording.startRecording')}
          </Button>
        );
      case RecordingState.RECORDING:
        return (
          <Space>
            <Button 
              icon={<PauseCircleOutlined />} 
              onClick={handlePauseRecording}
            >
              {t('editor.avatar.recording.pauseRecording')}
            </Button>
            <Button 
              icon={<StopOutlined />} 
              onClick={handleStopRecording}
            >
              {t('editor.avatar.recording.stopRecording')}
            </Button>
          </Space>
        );
      case RecordingState.PAUSED:
        return (
          <Space>
            <Button 
              icon={<PlayCircleOutlined />} 
              onClick={handleResumeRecording}
            >
              {t('editor.avatar.recording.resumeRecording')}
            </Button>
            <Button 
              icon={<StopOutlined />} 
              onClick={handleStopRecording}
            >
              {t('editor.avatar.recording.stopRecording')}
            </Button>
          </Space>
        );
      default:
        return null;
    }
  };
  
  // 渲染播放控制按钮
  const renderPlaybackControls = () => {
    switch (recordingState) {
      case RecordingState.IDLE:
        return (
          <Button 
            icon={<PlayCircleOutlined />} 
            onClick={handlePlayRecording}
            disabled={!selectedRecording || !entityId}
          >
            {t('editor.avatar.recording.playRecording')}
          </Button>
        );
      case RecordingState.PLAYING:
        return (
          <Space>
            <Button 
              icon={<PauseCircleOutlined />} 
              onClick={handlePausePlayback}
            >
              {t('editor.avatar.recording.pausePlayback')}
            </Button>
            <Button 
              icon={<StopOutlined />} 
              onClick={handleStopPlayback}
            >
              {t('editor.avatar.recording.stopPlayback')}
            </Button>
          </Space>
        );
      case RecordingState.PLAY_PAUSED:
        return (
          <Space>
            <Button 
              icon={<PlayCircleOutlined />} 
              onClick={handleResumePlayback}
            >
              {t('editor.avatar.recording.resumePlayback')}
            </Button>
            <Button 
              icon={<StopOutlined />} 
              onClick={handleStopPlayback}
            >
              {t('editor.avatar.recording.stopPlayback')}
            </Button>
          </Space>
        );
      default:
        return null;
    }
  };
  
  return (
    <div className="action-recording-panel">
      <Card title={t('editor.avatar.recording.title')} extra={
        <Space>
          <Tooltip title={t('editor.avatar.recording.settings')}>
            <Button 
              icon={<SettingOutlined />} 
              onClick={() => setIsSettingsVisible(true)}
            />
          </Tooltip>
          <Tooltip title={t('editor.avatar.recording.import')}>
            <Button 
              icon={<UploadOutlined />} 
              onClick={() => setIsImportModalVisible(true)}
            />
          </Tooltip>
          <Tooltip title={t('editor.avatar.recording.export')}>
            <Button 
              icon={<DownloadOutlined />} 
              onClick={() => setIsExportModalVisible(true)}
              disabled={!selectedRecording}
            />
          </Tooltip>
        </Space>
      }>
        {/* 录制部分 */}
        <div className="recording-section">
          <Title level={5}>{t('editor.avatar.recording.recordSection')}</Title>
          
          {/* 录制名称输入 */}
          {recordingState === RecordingState.IDLE && (
            <Input
              placeholder={t('editor.avatar.recording.enterName') as string}
              value={recordingName}
              onChange={(e) => setRecordingName(e.target.value)}
              style={{ marginBottom: 16 }}
              disabled={recordingState !== RecordingState.IDLE}
            />
          )}
          
          {/* 录制时间显示 */}
          {(recordingState === RecordingState.RECORDING || recordingState === RecordingState.PAUSED) && (
            <div className="recording-time">
              <ClockCircleOutlined /> {formatTime(elapsedTime)}
              {recordingState === RecordingState.PAUSED && (
                <Tag color="orange" style={{ marginLeft: 8 }}>
                  {t('editor.avatar.recording.paused')}
                </Tag>
              )}
            </div>
          )}
          
          {/* 录制控制按钮 */}
          <div className="recording-controls">
            {renderRecordingControls()}
          </div>
        </div>
        
        <Divider />
        
        {/* 播放部分 */}
        <div className="playback-section">
          <Title level={5}>{t('editor.avatar.recording.playbackSection')}</Title>
          
          {/* 播放速度滑块 */}
          <div className="playback-speed">
            <Text>{t('editor.avatar.recording.playbackSpeed')}: {playbackSpeed.toFixed(1)}x</Text>
            <Slider
              min={0.1}
              max={2.0}
              step={0.1}
              value={playbackSpeed}
              onChange={setPlaybackSpeed}
              disabled={recordingState === RecordingState.PLAYING || recordingState === RecordingState.PLAY_PAUSED}
            />
          </div>
          
          {/* 播放控制按钮 */}
          <div className="playback-controls">
            {renderPlaybackControls()}
          </div>
        </div>
        
        <Divider />
        
        {/* 录制列表 */}
        <div className="recordings-list">
          <Title level={5}>{t('editor.avatar.recording.recordingsList')}</Title>
          
          <List
            dataSource={recordings}
            renderItem={(recording) => (
              <List.Item
                key={recording.id}
                className={selectedRecording?.id === recording.id ? 'selected-recording' : ''}
                actions={[
                  <Tooltip title={t('editor.common.delete')}>
                    <Button 
                      icon={<DeleteOutlined />} 
                      size="small" 
                      danger
                      onClick={() => handleDeleteRecording(recording.id)}
                    />
                  </Tooltip>
                ]}
              >
                <List.Item.Meta
                  title={
                    <Button 
                      type="link" 
                      onClick={() => setSelectedRecording(recording)}
                    >
                      {recording.name}
                    </Button>
                  }
                  description={
                    <Space>
                      <Text type="secondary">
                        {formatTime(recording.endTimestamp - recording.startTimestamp)}
                      </Text>
                      <Text type="secondary">
                        {new Date(recording.startTimestamp).toLocaleDateString()}
                      </Text>
                    </Space>
                  }
                />
              </List.Item>
            )}
            locale={{ emptyText: t('editor.avatar.recording.noRecordings') }}
          />
        </div>
      </Card>
      
      {/* 设置模态框 */}
      <Modal
        title={t('editor.avatar.recording.settings')}
        open={isSettingsVisible}
        onCancel={() => setIsSettingsVisible(false)}
        onOk={() => setIsSettingsVisible(false)}
      >
        <Form layout="vertical">
          <Form.Item label={t('editor.avatar.recording.recordSettings')}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Switch 
                  checked={recordSettings.recordInput} 
                  onChange={(checked) => setRecordSettings({...recordSettings, recordInput: checked})}
                />
                <span style={{ marginLeft: 8 }}>{t('editor.avatar.recording.recordInput')}</span>
              </div>
              <div>
                <Switch
                  checked={recordSettings.recordTransform}
                  onChange={(checked) => setRecordSettings({...recordSettings, recordTransform: checked})}
                />
                <span style={{ marginLeft: 8 }}>{t('editor.avatar.recording.recordTransform')}</span>
              </div>
              <div>
                <span>{t('editor.avatar.recording.transformFrequency')}: {recordSettings.transformFrequency}ms</span>
                <Slider
                  min={10}
                  max={500}
                  step={10}
                  value={recordSettings.transformFrequency}
                  onChange={(value) => setRecordSettings({...recordSettings, transformFrequency: value})}
                />
              </div>
            </Space>
          </Form.Item>
          
          <Form.Item label={t('editor.avatar.recording.playSettings')}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Switch 
                  checked={playSettings.playInput} 
                  onChange={(checked) => setPlaySettings({...playSettings, playInput: checked})}
                />
                <span style={{ marginLeft: 8 }}>{t('editor.avatar.recording.playInput')}</span>
              </div>
              <div>
                <Switch
                  checked={playSettings.playTransform}
                  onChange={(checked) => setPlaySettings({...playSettings, playTransform: checked})}
                />
                <span style={{ marginLeft: 8 }}>{t('editor.avatar.recording.playTransform')}</span>
              </div>
              <div>
                <Switch 
                  checked={playSettings.loop} 
                  onChange={(checked) => setPlaySettings({...playSettings, loop: checked})}
                />
                <span style={{ marginLeft: 8 }}>{t('editor.avatar.recording.loop')}</span>
              </div>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 导入模态框 */}
      <Modal
        title={t('editor.avatar.recording.import')}
        open={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        footer={null}
      >
        <Dragger
          accept=".json"
          beforeUpload={handleImportRecording}
          showUploadList={false}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">{t('editor.avatar.recording.clickOrDrag')}</p>
          <p className="ant-upload-hint">{t('editor.avatar.recording.importHint')}</p>
        </Dragger>
      </Modal>
      
      {/* 导出模态框 */}
      <Modal
        title={t('editor.avatar.recording.export')}
        open={isExportModalVisible}
        onCancel={() => setIsExportModalVisible(false)}
        onOk={handleExportRecording}
        okText={t('editor.avatar.recording.download')}
      >
        {selectedRecording && (
          <div>
            <p>{t('editor.avatar.recording.exportConfirm')}</p>
            <p><strong>{t('editor.common.name')}:</strong> {selectedRecording.name}</p>
            <p><strong>{t('editor.common.duration')}:</strong> {formatTime(selectedRecording.endTimestamp - selectedRecording.startTimestamp)}</p>
            <p><strong>{t('editor.avatar.recording.actionEvents')}:</strong> {selectedRecording.actionEvents.length}</p>
            <p><strong>{t('editor.avatar.recording.inputEvents')}:</strong> {selectedRecording.inputEvents.length}</p>
            <p><strong>{t('editor.avatar.recording.transformEvents')}:</strong> {selectedRecording.transformEvents.length}</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ActionRecordingPanel;
