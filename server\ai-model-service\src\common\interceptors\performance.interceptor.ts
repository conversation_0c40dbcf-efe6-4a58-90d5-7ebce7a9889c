/**
 * 性能监控拦截器
 * 监控请求处理时间和系统资源使用情况
 */
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

interface PerformanceMetrics {
  requestId: string;
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  timestamp: number;
  userAgent?: string;
  ip?: string;
}

@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PerformanceInterceptor.name);
  private readonly performanceThresholds = {
    warning: 1000, // 1秒
    error: 5000,   // 5秒
  };

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    const startTime = Date.now();
    const startCpuUsage = process.cpuUsage();
    const requestId = request.headers['x-request-id'] as string || this.generateRequestId();

    return next.handle().pipe(
      tap(() => {
        this.recordMetrics(request, response, startTime, startCpuUsage, requestId);
      }),
      catchError((error) => {
        this.recordMetrics(request, response, startTime, startCpuUsage, requestId, error);
        throw error;
      }),
    );
  }

  /**
   * 记录性能指标
   */
  private recordMetrics(
    request: Request,
    response: Response,
    startTime: number,
    startCpuUsage: NodeJS.CpuUsage,
    requestId: string,
    error?: any,
  ): void {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    const endCpuUsage = process.cpuUsage(startCpuUsage);

    const metrics: PerformanceMetrics = {
      requestId,
      method: request.method,
      url: request.url,
      statusCode: error ? (error.status || 500) : response.statusCode,
      responseTime,
      memoryUsage: process.memoryUsage(),
      cpuUsage: endCpuUsage,
      timestamp: startTime,
      userAgent: request.get('User-Agent'),
      ip: this.getClientIP(request),
    };

    // 记录性能日志
    this.logPerformance(metrics);

    // 检查性能阈值
    this.checkPerformanceThresholds(metrics);

    // 在开发环境下输出详细的性能信息
    if (process.env.NODE_ENV === 'development') {
      this.logDetailedMetrics(metrics);
    }
  }

  /**
   * 记录性能日志
   */
  private logPerformance(metrics: PerformanceMetrics): void {
    const { method, url, statusCode, responseTime, requestId } = metrics;
    
    this.logger.log(
      `[${requestId}] ${method} ${url} ${statusCode} - ${responseTime}ms`,
    );
  }

  /**
   * 检查性能阈值
   */
  private checkPerformanceThresholds(metrics: PerformanceMetrics): void {
    const { responseTime, method, url, requestId } = metrics;

    if (responseTime > this.performanceThresholds.error) {
      this.logger.error(
        `[${requestId}] 性能警告: ${method} ${url} 响应时间过长 ${responseTime}ms`,
      );
    } else if (responseTime > this.performanceThresholds.warning) {
      this.logger.warn(
        `[${requestId}] 性能提醒: ${method} ${url} 响应时间较长 ${responseTime}ms`,
      );
    }
  }

  /**
   * 记录详细的性能指标
   */
  private logDetailedMetrics(metrics: PerformanceMetrics): void {
    const { memoryUsage, cpuUsage, requestId } = metrics;
    
    this.logger.debug(
      `[${requestId}] 内存使用: RSS=${this.formatBytes(memoryUsage.rss)}, ` +
      `Heap=${this.formatBytes(memoryUsage.heapUsed)}/${this.formatBytes(memoryUsage.heapTotal)}, ` +
      `External=${this.formatBytes(memoryUsage.external)}`,
    );

    this.logger.debug(
      `[${requestId}] CPU使用: User=${cpuUsage.user}μs, System=${cpuUsage.system}μs`,
    );
  }

  /**
   * 获取客户端IP地址
   */
  private getClientIP(request: Request): string {
    return (
      request.ip ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      (request.connection as any)?.socket?.remoteAddress ||
      'unknown'
    );
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
