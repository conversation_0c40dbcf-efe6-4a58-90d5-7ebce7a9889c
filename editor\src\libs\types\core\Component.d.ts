/**
 * 组件基类
 * 实体的功能模块
 */
import { Entity } from './Entity';
import { EventEmitter } from '../utils/EventEmitter';
export declare abstract class Component extends EventEmitter {
    /** 组件类型 */
    private type;
    /** 实体引用 */
    protected entity: Entity | null;
    /** 是否启用 */
    protected enabled: boolean;
    /**
     * 创建组件实例
     * @param type 组件类型
     */
    constructor(type: string);
    /**
     * 获取组件类型
     * @returns 组件类型
     */
    getType(): string;
    /**
     * 设置实体引用
     * @param entity 实体实例
     */
    setEntity(entity: Entity): void;
    /**
     * 获取实体引用
     * @returns 实体实例
     */
    getEntity(): Entity | null;
    /**
     * 设置启用状态
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 当组件附加到实体时调用
     */
    protected onAttach(): void;
    /**
     * 当组件启用时调用
     */
    protected onEnable(): void;
    /**
     * 当组件禁用时调用
     */
    protected onDisable(): void;
    /**
     * 更新组件
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 固定时间步长更新
     * @param _fixedDeltaTime 固定帧间隔时间（秒）
     */
    fixedUpdate(_fixedDeltaTime: number): void;
    /**
     * 销毁组件
     */
    dispose(): void;
}
