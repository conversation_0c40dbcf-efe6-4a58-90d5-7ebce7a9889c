/**
 * 面部动画系统
 * 用于管理和更新面部动画组件
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { System } from '../core/System';
import { FacialAnimationComponent, FacialAnimationConfig } from './FacialAnimation';
import { FacialAnimationModelAdapterSystem } from './adapters/FacialAnimationModelAdapterSystem';
/**
 * 面部动画系统
 */
export declare class FacialAnimationSystem extends System {
    /** 系统类型 */
    static readonly type = "FacialAnimation";
    /** 面部动画组件 */
    private components;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 音频分析配置 */
    private audioConfig;
    /** 音频上下文 */
    private audioContext;
    /** 音频分析器 */
    private audioAnalyser;
    /** 频谱数据 */
    private spectrum;
    /** 是否正在跟踪口型 */
    private lipsyncTracking;
    /** 事件发射器 */
    private eventEmitter;
    /** 模型适配器系统 */
    private modelAdapterSystem;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(config?: Partial<FacialAnimationConfig>);
    /**
     * 设置模型适配器系统
     * @param system 模型适配器系统
     */
    setModelAdapterSystem(system: FacialAnimationModelAdapterSystem): void;
    /**
     * 获取模型适配器系统
     * @returns 模型适配器系统
     */
    getModelAdapterSystem(): FacialAnimationModelAdapterSystem | null;
    /**
     * 创建面部动画组件
     * @param entity 实体
     * @returns 面部动画组件
     */
    createFacialAnimation(entity: Entity): FacialAnimationComponent;
    /**
     * 移除面部动画组件
     * @param entity 实体
     */
    removeFacialAnimation(entity: Entity): void;
    /**
     * 获取面部动画组件
     * @param entity 实体
     * @returns 面部动画组件，如果不存在则返回null
     */
    getFacialAnimation(entity: Entity): FacialAnimationComponent | null;
    /**
     * 启动口型同步跟踪
     */
    startLipsyncTracking(): void;
    /**
     * 停止口型同步跟踪
     */
    stopLipsyncTracking(): void;
    /**
     * 为所有实体设置口型
     * @param viseme 口型类型
     * @param weight 权重
     */
    private setVisemeForAllEntities;
    /**
     * 计算RMS（均方根）
     * @param spectrum 频谱数据
     * @returns RMS值
     */
    private getRMS;
    /**
     * 获取灵敏度映射
     * @param spectrum 频谱数据
     * @returns 灵敏度映射
     */
    private getSensitivityMap;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 将面部动画组件与模型适配器关联
     * @param entity 实体
     * @param mesh 骨骼网格
     * @returns 是否成功关联
     */
    linkToModel(entity: Entity, mesh: THREE.SkinnedMesh): boolean;
}
