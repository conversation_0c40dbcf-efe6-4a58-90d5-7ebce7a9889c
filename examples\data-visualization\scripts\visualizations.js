/**
 * 数据可视化示例 - 可视化类型实现
 */

import { Entity, Transform, Mesh, Material } from '/engine/dist/index.js';

/**
 * 创建3D柱状图
 * @param {World} world - 世界实例
 * @param {Array} data - 数据数组
 * @param {Object} options - 配置选项
 * @returns {Map<string, Entity>} 实体映射
 */
export function createBarChart(world, data, options = {}) {
  const {
    width = 20,
    depth = 20,
    heightScale = 1,
    spacing = 0.2,
    colorScheme = 'default',
    showLabels = true,
    minValue = null,
    maxValue = null,
  } = options;

  // 计算数据范围
  const dataMin = minValue !== null ? minValue : Math.min(...data.map(item => item.value));
  const dataMax = maxValue !== null ? maxValue : Math.max(...data.map(item => item.value));
  
  // 创建实体映射
  const entities = new Map();
  
  // 计算网格大小
  const gridSize = Math.ceil(Math.sqrt(data.length));
  const cellSize = Math.min(width, depth) / gridSize;
  const barWidth = cellSize * (1 - spacing);
  
  // 创建每个数据点的实体
  data.forEach((item, index) => {
    // 计算位置
    const row = Math.floor(index / gridSize);
    const col = index % gridSize;
    const x = col * cellSize - width / 2 + cellSize / 2;
    const z = row * cellSize - depth / 2 + cellSize / 2;
    
    // 计算高度（归一化）
    const normalizedValue = (item.value - dataMin) / (dataMax - dataMin);
    const height = normalizedValue * 10 * heightScale;
    
    // 获取颜色
    const color = getColorForValue(normalizedValue, colorScheme);
    
    // 创建柱体实体
    const barEntity = new Entity(world)
      .addComponent(new Transform({
        position: { x, y: height / 2, z },
        scale: { x: barWidth, y: height, z: barWidth },
      }))
      .addComponent(new Mesh({
        geometry: { type: 'box' },
      }))
      .addComponent(new Material({
        type: 'standard',
        color: item.color || color,
        roughness: 0.3,
        metalness: 0.7,
        transparent: true,
        opacity: 0.8,
      }));
    
    // 存储实体
    entities.set(item.id, barEntity);
    
    // 添加标签（如果启用）
    if (showLabels) {
      const labelEntity = createLabel(world, item, { x, y: height + 0.5, z });
      entities.set(`${item.id}-label`, labelEntity);
    }
  });
  
  return entities;
}

/**
 * 创建3D散点图
 * @param {World} world - 世界实例
 * @param {Array} data - 数据数组
 * @param {Object} options - 配置选项
 * @returns {Map<string, Entity>} 实体映射
 */
export function createScatterPlot(world, data, options = {}) {
  const {
    width = 20,
    height = 20,
    depth = 20,
    pointSize = 0.5,
    colorScheme = 'default',
    showLabels = true,
    minValue = null,
    maxValue = null,
  } = options;
  
  // 计算数据范围
  const dataMin = minValue !== null ? minValue : Math.min(...data.map(item => item.value));
  const dataMax = maxValue !== null ? maxValue : Math.max(...data.map(item => item.value));
  
  // 创建实体映射
  const entities = new Map();
  
  // 创建每个数据点的实体
  data.forEach((item) => {
    // 计算位置（使用数据的位置或随机位置）
    const x = item.position ? item.position.x : (Math.random() - 0.5) * width;
    const z = item.position ? item.position.z : (Math.random() - 0.5) * depth;
    
    // 计算高度（归一化）
    const normalizedValue = (item.value - dataMin) / (dataMax - dataMin);
    const y = normalizedValue * height;
    
    // 获取颜色
    const color = getColorForValue(normalizedValue, colorScheme);
    
    // 创建点实体
    const pointEntity = new Entity(world)
      .addComponent(new Transform({
        position: { x, y, z },
        scale: { x: pointSize, y: pointSize, z: pointSize },
      }))
      .addComponent(new Mesh({
        geometry: { type: 'sphere' },
      }))
      .addComponent(new Material({
        type: 'standard',
        color: item.color || color,
        roughness: 0.1,
        metalness: 0.9,
        emissive: item.color || color,
        emissiveIntensity: 0.3,
      }));
    
    // 存储实体
    entities.set(item.id, pointEntity);
    
    // 添加标签（如果启用）
    if (showLabels) {
      const labelEntity = createLabel(world, item, { x, y: y + pointSize + 0.3, z });
      entities.set(`${item.id}-label`, labelEntity);
    }
  });
  
  return entities;
}

/**
 * 创建热力图
 * @param {World} world - 世界实例
 * @param {Array} data - 数据数组
 * @param {Object} options - 配置选项
 * @returns {Map<string, Entity>} 实体映射
 */
export function createHeatMap(world, data, options = {}) {
  const {
    width = 20,
    depth = 20,
    heightScale = 0.1,
    resolution = 20,
    colorScheme = 'heatmap',
    showLabels = false,
    minValue = null,
    maxValue = null,
  } = options;
  
  // 计算数据范围
  const dataMin = minValue !== null ? minValue : Math.min(...data.map(item => item.value));
  const dataMax = maxValue !== null ? maxValue : Math.max(...data.map(item => item.value));
  
  // 创建实体映射
  const entities = new Map();
  
  // 创建热力图网格
  const cellSize = Math.min(width, depth) / resolution;
  
  // 初始化热力图数据
  const heatmapData = Array(resolution).fill().map(() => Array(resolution).fill(0));
  
  // 填充热力图数据
  data.forEach((item) => {
    const x = item.position ? item.position.x : (Math.random() - 0.5) * width;
    const z = item.position ? item.position.z : (Math.random() - 0.5) * depth;
    
    // 转换为网格坐标
    const gridX = Math.floor((x + width / 2) / cellSize);
    const gridZ = Math.floor((z + depth / 2) / cellSize);
    
    // 确保在网格范围内
    if (gridX >= 0 && gridX < resolution && gridZ >= 0 && gridZ < resolution) {
      heatmapData[gridZ][gridX] += item.value;
    }
  });
  
  // 找出热力图数据的最大值和最小值
  let heatMin = Infinity;
  let heatMax = -Infinity;
  
  for (let z = 0; z < resolution; z++) {
    for (let x = 0; x < resolution; x++) {
      heatMin = Math.min(heatMin, heatmapData[z][x]);
      heatMax = Math.max(heatMax, heatmapData[z][x]);
    }
  }
  
  // 创建热力图单元格
  for (let z = 0; z < resolution; z++) {
    for (let x = 0; x < resolution; x++) {
      const value = heatmapData[z][x];
      
      // 跳过零值单元格
      if (value === 0) continue;
      
      // 归一化值
      const normalizedValue = (value - heatMin) / (heatMax - heatMin);
      
      // 计算位置
      const posX = x * cellSize - width / 2 + cellSize / 2;
      const posZ = z * cellSize - depth / 2 + cellSize / 2;
      const height = normalizedValue * heightScale;
      
      // 获取颜色
      const color = getColorForValue(normalizedValue, colorScheme);
      
      // 创建单元格实体
      const cellEntity = new Entity(world)
        .addComponent(new Transform({
          position: { x: posX, y: height / 2, z: posZ },
          scale: { x: cellSize * 0.95, y: height, z: cellSize * 0.95 },
        }))
        .addComponent(new Mesh({
          geometry: { type: 'box' },
        }))
        .addComponent(new Material({
          type: 'standard',
          color: color,
          roughness: 0.2,
          metalness: 0.5,
          transparent: true,
          opacity: 0.7,
        }));
      
      // 存储实体
      entities.set(`cell-${z}-${x}`, cellEntity);
    }
  }
  
  return entities;
}

/**
 * 创建地理数据可视化
 * @param {World} world - 世界实例
 * @param {Array} data - 数据数组
 * @param {Object} options - 配置选项
 * @returns {Map<string, Entity>} 实体映射
 */
export function createGeoMap(world, data, options = {}) {
  // 地理数据可视化的实现
  // 这里简化为在平面上显示数据点
  return createScatterPlot(world, data, {
    ...options,
    pointSize: 0.3,
    height: 5,
  });
}

/**
 * 创建标签实体
 * @param {World} world - 世界实例
 * @param {Object} item - 数据项
 * @param {Object} position - 位置
 * @returns {Entity} 标签实体
 */
function createLabel(world, item, position) {
  // 这里假设UISystem.components.TextLabel存在
  // 实际实现可能需要根据引擎API调整
  const labelEntity = new Entity(world)
    .addComponent(new Transform({
      position: position,
      rotation: { x: -Math.PI / 2, y: 0, z: 0 },
      scale: { x: 2, y: 2, z: 2 },
    }));
  
  // 添加文本标签组件（如果存在）
  if (world.getSystem('UISystem') && world.getSystem('UISystem').components.TextLabel) {
    labelEntity.addComponent(new world.getSystem('UISystem').components.TextLabel({
      text: `${item.name}\n${item.value.toLocaleString()}`,
      fontSize: 0.5,
      color: '#ffffff',
      alignment: 'center',
      billboard: true,
    }));
  }
  
  return labelEntity;
}

/**
 * 根据值和配色方案获取颜色
 * @param {number} value - 归一化值 (0-1)
 * @param {string} scheme - 配色方案
 * @returns {string} 颜色值
 */
function getColorForValue(value, scheme = 'default') {
  switch (scheme) {
    case 'rainbow':
      return `hsl(${value * 270}, 100%, 50%)`;
    case 'heatmap':
      return interpolateColor('#00ff00', '#ffff00', '#ff0000', value);
    case 'blueRed':
      return interpolateColor('#0000ff', '#ffffff', '#ff0000', value);
    case 'greenPurple':
      return interpolateColor('#00ff00', '#ffffff', '#800080', value);
    case 'default':
    default:
      return `hsl(${210 - value * 210}, 80%, 50%)`;
  }
}

/**
 * 在三个颜色之间插值
 * @param {string} color1 - 起始颜色
 * @param {string} color2 - 中间颜色
 * @param {string} color3 - 结束颜色
 * @param {number} value - 插值值 (0-1)
 * @returns {string} 插值后的颜色
 */
function interpolateColor(color1, color2, color3, value) {
  if (value < 0.5) {
    return interpolateTwoColors(color1, color2, value * 2);
  } else {
    return interpolateTwoColors(color2, color3, (value - 0.5) * 2);
  }
}

/**
 * 在两个颜色之间插值
 * @param {string} color1 - 起始颜色
 * @param {string} color2 - 结束颜色
 * @param {number} value - 插值值 (0-1)
 * @returns {string} 插值后的颜色
 */
function interpolateTwoColors(color1, color2, value) {
  // 解析颜色
  const c1 = parseColor(color1);
  const c2 = parseColor(color2);
  
  // 插值RGB值
  const r = Math.round(c1.r + (c2.r - c1.r) * value);
  const g = Math.round(c1.g + (c2.g - c1.g) * value);
  const b = Math.round(c1.b + (c2.b - c1.b) * value);
  
  // 返回颜色字符串
  return `rgb(${r}, ${g}, ${b})`;
}

/**
 * 解析颜色字符串为RGB对象
 * @param {string} color - 颜色字符串
 * @returns {Object} RGB对象
 */
function parseColor(color) {
  // 处理十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.substring(1);
    return {
      r: parseInt(hex.substring(0, 2), 16),
      g: parseInt(hex.substring(2, 4), 16),
      b: parseInt(hex.substring(4, 6), 16)
    };
  }
  
  // 处理rgb颜色
  if (color.startsWith('rgb')) {
    const match = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (match) {
      return {
        r: parseInt(match[1]),
        g: parseInt(match[2]),
        b: parseInt(match[3])
      };
    }
  }
  
  // 默认返回黑色
  return { r: 0, g: 0, b: 0 };
}
