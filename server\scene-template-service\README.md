# 场景模板微服务 (Scene Template Service)

基于NestJS构建的独立场景模板微服务，支持预定义场景模板系统、模板参数化和定制、模板分享和评价、模板版本管理。

## 功能特性

### 核心功能
- ✅ **模板管理**: 完整的场景模板CRUD操作
- ✅ **参数化系统**: 灵活的模板参数配置和验证
- ✅ **版本控制**: 模板版本管理和历史记录
- ✅ **分享机制**: 公开/私有分享，权限控制
- ✅ **评价系统**: 用户评分和评论功能
- ✅ **模板市场**: 模板发现和推荐
- ✅ **实例化引擎**: 参数应用和场景生成

### 高级特性
- 🎨 **可视化编辑**: 支持参数可视化配置
- 🔄 **模板克隆**: 基于现有模板创建新模板
- 📊 **统计分析**: 使用量、下载量、评分统计
- 🏷️ **分类标签**: 多维度模板分类和标签
- 🔍 **智能搜索**: 基于内容和元数据的搜索
- 📈 **推荐算法**: 个性化模板推荐

## 技术栈

- **框架**: NestJS + TypeScript
- **数据库**: PostgreSQL + TypeORM
- **缓存**: Redis
- **存储**: MinIO (S3兼容)
- **认证**: JWT + Passport
- **文档**: Swagger/OpenAPI
- **容器**: Docker + Docker Compose

## 快速开始

### 环境要求
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 13+
- Redis 6+
- MinIO

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 启动开发环境
```bash
# 启动基础设施服务
docker-compose up -d postgres redis minio

# 启动开发服务器
npm run start:dev
```

### 构建生产版本
```bash
# 构建应用
npm run build

# 启动生产服务
npm run start:prod
```

## API 文档

启动服务后访问 Swagger 文档：
- 开发环境: http://localhost:8004/api/docs
- 生产环境: https://your-domain/api/docs

### 主要API端点

#### 模板管理
- `GET /templates` - 获取模板列表
- `GET /templates/:id` - 获取模板详情
- `POST /templates` - 创建模板
- `PATCH /templates/:id` - 更新模板
- `DELETE /templates/:id` - 删除模板

#### 模板操作
- `POST /templates/:id/instantiate` - 实例化模板
- `POST /templates/:id/clone` - 克隆模板
- `POST /templates/:id/publish` - 发布模板
- `POST /templates/:id/download` - 下载模板

#### 模板发现
- `GET /templates/popular` - 热门模板
- `GET /templates/latest` - 最新模板
- `GET /templates/featured` - 精选模板

## 数据库设计

### 核心实体

#### SceneTemplate (场景模板)
```typescript
{
  id: string;
  name: string;
  description: string;
  type: TemplateType;
  status: TemplateStatus;
  complexity: TemplateComplexity;
  sceneData: object;        // 场景配置数据
  defaultParameters: object; // 默认参数值
  parameters: TemplateParameter[]; // 参数配置
  category: TemplateCategory;
  creator: User;
  downloadCount: number;
  rating: number;
}
```

#### TemplateParameter (模板参数)
```typescript
{
  id: string;
  name: string;
  key: string;
  type: ParameterType;      // string, number, color, vector3等
  category: ParameterCategory;
  defaultValue: any;
  validation: object;       // 验证规则
  showCondition: object;    // 显示条件
}
```

#### TemplateShare (模板分享)
```typescript
{
  id: string;
  shareType: ShareType;     // public, private, team
  permission: SharePermission;
  shareToken: string;
  expiresAt: Date;
  template: SceneTemplate;
  sharedBy: User;
}
```

## 模板参数系统

### 参数类型
- **基础类型**: string, number, boolean
- **颜色类型**: color (十六进制)
- **向量类型**: vector3 (3D坐标)
- **枚举类型**: enum (选项列表)
- **范围类型**: range (数值范围)
- **引用类型**: asset_reference, material_reference

### 参数验证
```typescript
{
  required: boolean;
  pattern: string;          // 正则表达式
  min: number;             // 最小值
  max: number;             // 最大值
  options: any[];          // 枚举选项
}
```

### 条件显示
```typescript
{
  showCondition: {
    parameter: "enableLighting",
    value: true
  },
  hideCondition: {
    parameter: "useDefaultMaterial",
    value: true
  }
}
```

## 模板实例化

### 实例化流程
1. **参数验证**: 验证用户输入的参数
2. **参数合并**: 合并默认参数和用户参数
3. **场景生成**: 将参数应用到场景数据
4. **结果返回**: 返回最终的场景配置

### 参数应用
```typescript
// 场景数据中的参数占位符
{
  "lighting": {
    "ambient": "${ambientLight}",
    "directional": {
      "intensity": "${lightIntensity}",
      "color": "${lightColor}"
    }
  }
}

// 参数值
{
  "ambientLight": 0.3,
  "lightIntensity": 1.2,
  "lightColor": "#FFFFFF"
}

// 应用后的结果
{
  "lighting": {
    "ambient": 0.3,
    "directional": {
      "intensity": 1.2,
      "color": "#FFFFFF"
    }
  }
}
```

## 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t scene-template-service .

# 运行容器
docker run -d \
  --name scene-template \
  -p 8004:8004 \
  -e NODE_ENV=production \
  scene-template-service
```

### Docker Compose部署
```bash
# 启动完整服务栈
docker-compose -f docker-compose.production.yml up -d
```

## 监控和运维

### 健康检查
- `GET /health` - 服务健康状态
- `GET /health/ready` - 就绪检查
- `GET /health/live` - 存活检查

### 监控指标
- 模板创建/使用统计
- 参数验证成功率
- 实例化性能指标
- 用户活跃度统计

## 开发指南

### 项目结构
```
src/
├── common/           # 公共模块
├── modules/          # 业务模块
│   ├── templates/    # 模板管理
│   ├── parameters/   # 参数管理
│   ├── categories/   # 分类管理
│   ├── versions/     # 版本管理
│   ├── sharing/      # 分享管理
│   ├── ratings/      # 评价管理
│   ├── marketplace/  # 模板市场
│   └── auth/         # 认证模块
├── app.module.ts     # 应用主模块
└── main.ts           # 应用入口
```

### 添加新参数类型
1. 在 `ParameterType` 枚举中添加新类型
2. 在 `TemplateParameter.validateValue()` 中添加验证逻辑
3. 在前端组件中添加对应的输入控件

### 扩展模板功能
1. 修改 `SceneTemplate` 实体添加新字段
2. 更新相关的DTO和验证规则
3. 在服务中实现新的业务逻辑

## 安全考虑

### 参数安全
- 参数值验证和清理
- 防止代码注入攻击
- 限制参数复杂度

### 模板安全
- 模板内容扫描
- 恶意代码检测
- 访问权限控制

## 性能优化

### 缓存策略
- 热门模板缓存
- 参数验证结果缓存
- 实例化结果缓存

### 数据库优化
- 索引优化
- 查询优化
- 分页优化

## 故障排除

### 常见问题
1. **参数验证失败**: 检查参数类型和验证规则
2. **模板实例化错误**: 检查场景数据格式和参数占位符
3. **分享链接失效**: 检查分享配置和过期时间

## 许可证

MIT License

## 联系方式

- 项目维护者: DL Engine Team
- 邮箱: <EMAIL>
- 文档: https://docs.dlengine.com
