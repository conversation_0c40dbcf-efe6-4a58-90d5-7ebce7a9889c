/**
 * 服务管理器
 * 统一管理所有系统服务的生命周期和依赖关系
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetLibraryService } from './AssetLibraryService';
import { SceneTemplateService } from './SceneTemplateService';
import { UserManagementService } from './UserManagementService';
import { CacheService } from './CacheService';
import { SceneGenerationAIManager } from '../ai/scene/SceneGenerationAIManager';
import { VoiceSceneGenerationController } from '../ai/scene/VoiceSceneGenerationController';
import { ConversationalSceneOptimizer } from '../ai/scene/ConversationalSceneOptimizer';
import { ScenePerformanceOptimizer } from '../ai/scene/ScenePerformanceOptimizer';

/**
 * 服务配置
 */
export interface ServiceConfig {
  /** 缓存服务配置 */
  cache?: {
    type: 'redis' | 'memory';
    redis?: any;
    memory?: any;
  };
  /** 数据库配置 */
  database?: {
    type: 'mongodb' | 'postgresql';
    url: string;
    options?: any;
  };
  /** 存储配置 */
  storage?: {
    type: 'local' | 's3' | 'minio';
    config: any;
  };
  /** AI服务配置 */
  ai?: {
    sceneGeneration?: any;
    voiceProcessing?: any;
    performance?: any;
  };
}

/**
 * 服务状态
 */
export interface ServiceStatus {
  /** 服务名称 */
  name: string;
  /** 是否已初始化 */
  initialized: boolean;
  /** 是否健康 */
  healthy: boolean;
  /** 最后检查时间 */
  lastCheckAt: Date;
  /** 错误信息 */
  error?: string;
}

/**
 * 服务管理器
 */
export class ServiceManager extends EventEmitter {
  private config: ServiceConfig;
  private services: Map<string, any> = new Map();
  private serviceStatus: Map<string, ServiceStatus> = new Map();
  private initialized: boolean = false;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  // 核心服务实例
  private cacheService: CacheService;
  private userManagementService: UserManagementService;
  private assetLibraryService: AssetLibraryService;
  private sceneTemplateService: SceneTemplateService;
  private sceneGenerationAIManager: SceneGenerationAIManager;
  private voiceSceneController: VoiceSceneGenerationController;
  private conversationalOptimizer: ConversationalSceneOptimizer;
  private performanceOptimizer: ScenePerformanceOptimizer;

  constructor(config: ServiceConfig = {}) {
    super();
    this.config = config;
    this.initializeServices();
  }

  /**
   * 初始化所有服务实例
   */
  private initializeServices(): void {
    // 创建服务实例
    this.cacheService = new CacheService(this.config.cache);
    this.userManagementService = new UserManagementService();
    this.assetLibraryService = new AssetLibraryService();
    this.sceneTemplateService = new SceneTemplateService();
    // 创建一个临时的 world 对象，实际使用时应该传入真实的 world 实例
    const tempWorld = null; // 这里应该传入实际的 world 实例
    this.sceneGenerationAIManager = new SceneGenerationAIManager(tempWorld, this.config.ai?.sceneGeneration);
    this.voiceSceneController = new VoiceSceneGenerationController(tempWorld);
    this.conversationalOptimizer = new ConversationalSceneOptimizer(tempWorld);
    this.performanceOptimizer = new ScenePerformanceOptimizer(this.config.ai?.performance);

    // 注册服务
    this.registerService('cache', this.cacheService);
    this.registerService('userManagement', this.userManagementService);
    this.registerService('assetLibrary', this.assetLibraryService);
    this.registerService('sceneTemplate', this.sceneTemplateService);
    this.registerService('sceneGenerationAI', this.sceneGenerationAIManager);
    this.registerService('voiceScene', this.voiceSceneController);
    this.registerService('conversationalOptimizer', this.conversationalOptimizer);
    this.registerService('performanceOptimizer', this.performanceOptimizer);
  }

  /**
   * 注册服务
   */
  private registerService(name: string, service: any): void {
    this.services.set(name, service);
    this.serviceStatus.set(name, {
      name,
      initialized: false,
      healthy: false,
      lastCheckAt: new Date()
    });

    // 监听服务事件
    if (service instanceof EventEmitter) {
      service.on('error', (error) => {
        this.handleServiceError(name, error);
      });
    }
  }

  /**
   * 初始化所有服务
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      console.log('开始初始化服务管理器...');

      // 按依赖顺序初始化服务
      await this.initializeServicesByDependency();

      // 启动健康检查
      this.startHealthCheck();

      this.initialized = true;
      this.emit('initialized');
      
      console.log('服务管理器初始化完成');
    } catch (error) {
      console.error('服务管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 按依赖顺序初始化服务
   */
  private async initializeServicesByDependency(): Promise<void> {
    // 定义服务依赖关系
    const initializationOrder = [
      'cache',              // 缓存服务 - 基础服务
      'userManagement',     // 用户管理 - 依赖缓存
      'assetLibrary',       // 资产库 - 依赖用户管理
      'sceneTemplate',      // 场景模板 - 依赖资产库
      'sceneGenerationAI',  // AI场景生成 - 依赖模板和资产
      'conversationalOptimizer', // 对话优化 - 依赖AI生成
      'voiceScene',         // 语音场景 - 依赖AI生成和优化
      'performanceOptimizer' // 性能优化 - 独立服务
    ];

    for (const serviceName of initializationOrder) {
      await this.initializeService(serviceName);
    }
  }

  /**
   * 初始化单个服务
   */
  private async initializeService(serviceName: string): Promise<void> {
    const service = this.services.get(serviceName);
    const status = this.serviceStatus.get(serviceName);

    if (!service || !status) {
      throw new Error(`服务 ${serviceName} 不存在`);
    }

    try {
      console.log(`初始化服务: ${serviceName}`);
      
      // 调用服务的初始化方法
      if (typeof service.initialize === 'function') {
        await service.initialize();
      }

      // 更新状态
      status.initialized = true;
      status.healthy = true;
      status.lastCheckAt = new Date();
      status.error = undefined;

      this.emit('serviceInitialized', { serviceName, status });
      console.log(`服务 ${serviceName} 初始化成功`);
    } catch (error) {
      status.initialized = false;
      status.healthy = false;
      status.error = error instanceof Error ? error.message : '未知错误';
      
      console.error(`服务 ${serviceName} 初始化失败:`, error);
      this.emit('serviceError', { serviceName, error });
      
      throw new Error(`服务 ${serviceName} 初始化失败: ${status.error}`);
    }
  }

  /**
   * 获取服务实例
   */
  getService<T = any>(serviceName: string): T | null {
    return this.services.get(serviceName) || null;
  }

  /**
   * 获取缓存服务
   */
  getCacheService(): CacheService {
    return this.cacheService;
  }

  /**
   * 获取用户管理服务
   */
  getUserManagementService(): UserManagementService {
    return this.userManagementService;
  }

  /**
   * 获取资产库服务
   */
  getAssetLibraryService(): AssetLibraryService {
    return this.assetLibraryService;
  }

  /**
   * 获取场景模板服务
   */
  getSceneTemplateService(): SceneTemplateService {
    return this.sceneTemplateService;
  }

  /**
   * 获取场景生成AI管理器
   */
  getSceneGenerationAIManager(): SceneGenerationAIManager {
    return this.sceneGenerationAIManager;
  }

  /**
   * 获取语音场景控制器
   */
  getVoiceSceneController(): VoiceSceneGenerationController {
    return this.voiceSceneController;
  }

  /**
   * 获取对话优化器
   */
  getConversationalOptimizer(): ConversationalSceneOptimizer {
    return this.conversationalOptimizer;
  }

  /**
   * 获取性能优化器
   */
  getPerformanceOptimizer(): ScenePerformanceOptimizer {
    return this.performanceOptimizer;
  }

  /**
   * 获取服务状态
   */
  getServiceStatus(serviceName?: string): ServiceStatus | ServiceStatus[] {
    if (serviceName) {
      return this.serviceStatus.get(serviceName) || null;
    }
    return Array.from(this.serviceStatus.values());
  }

  /**
   * 检查所有服务健康状态
   */
  async checkHealth(): Promise<{ healthy: boolean; services: ServiceStatus[] }> {
    const services: ServiceStatus[] = [];
    let allHealthy = true;

    for (const [serviceName, service] of this.services.entries()) {
      const status = await this.checkServiceHealth(serviceName, service);
      services.push(status);
      
      if (!status.healthy) {
        allHealthy = false;
      }
    }

    return { healthy: allHealthy, services };
  }

  /**
   * 检查单个服务健康状态
   */
  private async checkServiceHealth(serviceName: string, service: any): Promise<ServiceStatus> {
    const status = this.serviceStatus.get(serviceName)!;
    
    try {
      // 如果服务有健康检查方法，调用它
      if (typeof service.healthCheck === 'function') {
        await service.healthCheck();
      }
      
      status.healthy = true;
      status.error = undefined;
    } catch (error) {
      status.healthy = false;
      status.error = error instanceof Error ? error.message : '健康检查失败';
    }
    
    status.lastCheckAt = new Date();
    return status;
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    // 每30秒检查一次健康状态
    this.healthCheckInterval = setInterval(async () => {
      try {
        const healthResult = await this.checkHealth();
        this.emit('healthCheck', healthResult);
        
        if (!healthResult.healthy) {
          console.warn('检测到不健康的服务:', 
            healthResult.services.filter(s => !s.healthy).map(s => s.name)
          );
        }
      } catch (error) {
        console.error('健康检查失败:', error);
      }
    }, 30000);
  }

  /**
   * 处理服务错误
   */
  private handleServiceError(serviceName: string, error: any): void {
    const status = this.serviceStatus.get(serviceName);
    if (status) {
      status.healthy = false;
      status.error = error instanceof Error ? error.message : '服务错误';
      status.lastCheckAt = new Date();
    }

    console.error(`服务 ${serviceName} 发生错误:`, error);
    this.emit('serviceError', { serviceName, error });
  }

  /**
   * 重启服务
   */
  async restartService(serviceName: string): Promise<void> {
    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`服务 ${serviceName} 不存在`);
    }

    try {
      console.log(`重启服务: ${serviceName}`);
      
      // 停止服务
      if (typeof service.destroy === 'function') {
        await service.destroy();
      }

      // 重新初始化
      await this.initializeService(serviceName);
      
      console.log(`服务 ${serviceName} 重启成功`);
      this.emit('serviceRestarted', { serviceName });
    } catch (error) {
      console.error(`服务 ${serviceName} 重启失败:`, error);
      throw error;
    }
  }

  /**
   * 停止服务
   */
  async stopService(serviceName: string): Promise<void> {
    const service = this.services.get(serviceName);
    const status = this.serviceStatus.get(serviceName);
    
    if (!service || !status) {
      throw new Error(`服务 ${serviceName} 不存在`);
    }

    try {
      console.log(`停止服务: ${serviceName}`);
      
      if (typeof service.destroy === 'function') {
        await service.destroy();
      }

      status.initialized = false;
      status.healthy = false;
      status.lastCheckAt = new Date();
      
      console.log(`服务 ${serviceName} 已停止`);
      this.emit('serviceStopped', { serviceName });
    } catch (error) {
      console.error(`停止服务 ${serviceName} 失败:`, error);
      throw error;
    }
  }

  /**
   * 获取系统统计信息
   */
  getSystemStats(): {
    totalServices: number;
    initializedServices: number;
    healthyServices: number;
    uptime: number;
  } {
    const statuses = Array.from(this.serviceStatus.values());
    
    return {
      totalServices: statuses.length,
      initializedServices: statuses.filter(s => s.initialized).length,
      healthyServices: statuses.filter(s => s.healthy).length,
      uptime: this.initialized ? Date.now() - this.serviceStatus.get('cache')!.lastCheckAt.getTime() : 0
    };
  }

  /**
   * 销毁服务管理器
   */
  async destroy(): Promise<void> {
    try {
      console.log('开始销毁服务管理器...');

      // 停止健康检查
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      // 按相反顺序销毁服务
      const serviceNames = Array.from(this.services.keys()).reverse();
      
      for (const serviceName of serviceNames) {
        try {
          await this.stopService(serviceName);
        } catch (error) {
          console.error(`销毁服务 ${serviceName} 失败:`, error);
        }
      }

      // 清理资源
      this.services.clear();
      this.serviceStatus.clear();
      this.initialized = false;
      
      // 移除所有事件监听器
      this.removeAllListeners();
      
      console.log('服务管理器已销毁');
      this.emit('destroyed');
    } catch (error) {
      console.error('销毁服务管理器失败:', error);
      throw error;
    }
  }

  /**
   * 获取服务依赖图
   */
  getServiceDependencyGraph(): Record<string, string[]> {
    return {
      cache: [],
      userManagement: ['cache'],
      assetLibrary: ['userManagement', 'cache'],
      sceneTemplate: ['assetLibrary', 'userManagement', 'cache'],
      sceneGenerationAI: ['sceneTemplate', 'assetLibrary'],
      conversationalOptimizer: ['sceneGenerationAI'],
      voiceScene: ['sceneGenerationAI', 'conversationalOptimizer'],
      performanceOptimizer: []
    };
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 等待服务初始化完成
   */
  async waitForInitialization(timeout: number = 30000): Promise<void> {
    if (this.initialized) {
      return;
    }

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('服务初始化超时'));
      }, timeout);

      this.once('initialized', () => {
        clearTimeout(timer);
        resolve();
      });
    });
  }
}
