/**
 * 三维向量类
 * 表示三维空间中的点或向量
 */
export declare class Vector3 {
    /** X分量 */
    x: number;
    /** Y分量 */
    y: number;
    /** Z分量 */
    z: number;
    /**
     * 创建三维向量
     * @param x X分量
     * @param y Y分量
     * @param z Z分量
     */
    constructor(x?: number, y?: number, z?: number);
    /**
     * 设置向量分量
     * @param x X分量
     * @param y Y分量
     * @param z Z分量
     * @returns 当前向量
     */
    set(x: number, y: number, z: number): Vector3;
    /**
     * 复制另一个向量的值
     * @param v 要复制的向量
     * @returns 当前向量
     */
    copy(v: Vector3): Vector3;
    /**
     * 克隆向量
     * @returns 新的向量实例
     */
    clone(): Vector3;
    /**
     * 向量加法
     * @param v 要加的向量
     * @returns 当前向量
     */
    add(v: Vector3): Vector3;
    /**
     * 向量减法
     * @param v 要减的向量
     * @returns 当前向量
     */
    subtract(v: Vector3): Vector3;
    /**
     * 向量乘以标量
     * @param s 标量
     * @returns 当前向量
     */
    multiplyScalar(s: number): Vector3;
    /**
     * 向量除以标量
     * @param s 标量
     * @returns 当前向量
     */
    divideScalar(s: number): Vector3;
    /**
     * 向量点积
     * @param v 另一个向量
     * @returns 点积结果
     */
    dot(v: Vector3): number;
    /**
     * 向量叉积
     * @param v 另一个向量
     * @returns 当前向量
     */
    cross(v: Vector3): Vector3;
    /**
     * 计算向量长度
     * @returns 向量长度
     */
    length(): number;
    /**
     * 计算向量长度的平方
     * @returns 向量长度的平方
     */
    lengthSquared(): number;
    /**
     * 归一化向量
     * @returns 当前向量
     */
    normalize(): Vector3;
    /**
     * 计算与另一个向量的距离
     * @param v 另一个向量
     * @returns 距离
     */
    distanceTo(v: Vector3): number;
    /**
     * 计算与另一个向量的距离的平方
     * @param v 另一个向量
     * @returns 距离的平方
     */
    distanceToSquared(v: Vector3): number;
    /**
     * 设置向量为零向量
     * @returns 当前向量
     */
    setZero(): Vector3;
    /**
     * 判断向量是否为零向量
     * @returns 是否为零向量
     */
    isZero(): boolean;
    /**
     * 判断向量是否约等于另一个向量
     * @param v 另一个向量
     * @param epsilon 误差范围
     * @returns 是否约等于
     */
    equals(v: Vector3, epsilon?: number): boolean;
}
