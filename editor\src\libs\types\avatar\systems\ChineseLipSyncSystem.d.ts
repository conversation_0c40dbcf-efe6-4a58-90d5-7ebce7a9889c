/**
 * 中文口型同步系统
 * 专门针对中文语音的口型同步
 */
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { LipSyncComponent } from '../components/LipSyncComponent';
/**
 * 中文音素类型
 */
export declare enum ChinesePhonemeType {
    B = "b",
    P = "p",
    M = "m",
    F = "f",
    D = "d",
    T = "t",
    N = "n",
    L = "l",
    G = "g",
    K = "k",
    H = "h",
    J = "j",
    Q = "q",
    X = "x",
    ZH = "zh",
    CH = "ch",
    SH = "sh",
    R = "r",
    Z = "z",
    C = "c",
    S = "s",
    A = "a",
    O = "o",
    E = "e",
    I = "i",
    U = "u",
    V = "v",
    AI = "ai",
    EI = "ei",
    UI = "ui",
    AO = "ao",
    OU = "ou",
    IU = "iu",
    IE = "ie",
    VE = "ve",
    ER = "er",
    AN = "an",
    EN = "en",
    IN = "in",
    UN = "un",
    VN = "vn",
    ANG = "ang",
    ENG = "eng",
    ING = "ing",
    ONG = "ong",
    SILENT = "silent"
}
/**
 * 中文口型同步系统配置
 */
export interface ChineseLipSyncSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用拼音分析 */
    usePinyinAnalysis?: boolean;
    /** 是否使用声调分析 */
    useToneAnalysis?: boolean;
    /** 是否使用语音识别 */
    useSpeechRecognition?: boolean;
    /** 语音识别语言 */
    speechRecognitionLang?: string;
    /** 是否使用音素分析 */
    usePhonemeAnalysis?: boolean;
    /** 是否使用平滑过渡 */
    useSmoothTransition?: boolean;
    /** 过渡时间（秒） */
    transitionTime?: number;
    /** 上下文窗口大小 */
    contextWindowSize?: number;
    /** 是否使用上下文预测 */
    useContextPrediction?: boolean;
}
/**
 * 中文口型同步系统
 */
export declare class ChineseLipSyncSystem extends System {
    /** 系统名称 */
    static readonly NAME = "ChineseLipSyncSystem";
    /** 配置 */
    private config;
    /** 组件映射 */
    private components;
    /** 事件发射器 */
    private eventEmitter;
    /** 拼音到口型映射 */
    private pinyinToVisemeMap;
    /** 音素到口型映射 */
    private phonemeToVisemeMap;
    /** 当前音素序列 */
    private currentPhonemes;
    /** 当前音素索引 */
    private currentPhonemeIndex;
    /** 语音识别器 */
    private speechRecognition;
    /** 当前口型 */
    private currentViseme;
    /** 上一个口型 */
    private previousViseme;
    /** 口型历史 */
    private visemeHistory;
    /** 口型转换矩阵 */
    private visemeTransitionMatrix;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: ChineseLipSyncSystemConfig);
    /**
     * 初始化拼音到口型映射
     */
    private initPinyinToVisemeMap;
    /**
     * 初始化音素到口型映射
     */
    private initPhonemeToVisemeMap;
    /**
     * 初始化口型转换矩阵
     */
    private initVisemeTransitionMatrix;
    /**
     * 初始化语音识别
     */
    private initSpeechRecognition;
    /**
     * 分析音素
     * @param text 文本
     */
    private analyzePhonemes;
    /**
     * 分析中文音素
     * @param text 中文文本
     */
    private analyzeChinesePhonemes;
    /**
     * 分析英文音素
     * @param text 英文文本
     */
    private analyzeEnglishPhonemes;
    /**
     * 更新
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 设置口型
     * @param viseme 口型类型
     * @param weight 权重
     */
    private setViseme;
    /**
     * 更新口型历史
     * @param viseme 口型
     */
    private updateVisemeHistory;
    /**
     * 添加组件
     * @param entity 实体
     * @param component 组件
     */
    addComponent(entity: Entity, component: LipSyncComponent): void;
    /**
     * 移除组件
     * @param entity 实体
     */
    removeComponent(entity: Entity): void;
    /**
     * 获取组件
     * @param entity 实体
     * @returns 组件
     */
    getComponent(entity: Entity): LipSyncComponent | undefined;
    /**
     * 从上下文预测口型
     * @param currentViseme 当前口型
     * @returns 预测的口型
     */
    private predictVisemeFromContext;
}
