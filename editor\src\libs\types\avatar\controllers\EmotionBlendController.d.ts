/**
 * 情感混合控制器
 * 用于控制多种情感的混合和过渡
 */
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { FacialExpressionType } from '../components/FacialAnimationComponent';
import { type EventCallback } from '../../utils/EventEmitter';
/**
 * 情感表情数据
 */
export interface EmotionExpressionData {
    /** 表情类型 */
    expression: FacialExpressionType;
    /** 目标权重 */
    targetWeight: number;
    /** 当前权重 */
    currentWeight: number;
    /** 过渡速度 */
    transitionSpeed: number;
    /** 开始时间 */
    startTime: number;
    /** 持续时间 */
    duration: number;
    /** 是否活跃 */
    active: boolean;
    /** 优先级 */
    priority: number;
}
/**
 * 情感混合控制器配置
 */
export interface EmotionBlendControllerConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 默认过渡时间（秒） */
    defaultTransitionTime?: number;
    /** 是否启用微表情 */
    enableMicroExpressions?: boolean;
    /** 微表情频率（每分钟） */
    microExpressionFrequency?: number;
    /** 微表情强度 */
    microExpressionIntensity?: number;
    /** 微表情持续时间（秒） */
    microExpressionDuration?: number;
    /** 是否启用自然变化 */
    enableNaturalVariation?: boolean;
    /** 自然变化幅度 */
    naturalVariationAmount?: number;
    /** 自然变化频率（每秒） */
    naturalVariationFrequency?: number;
    /** 混合模式 */
    blendMode?: 'add' | 'multiply' | 'override' | 'weighted';
}
/**
 * 情感混合控制器
 */
export declare class EmotionBlendController {
    /** 实体 */
    private entity;
    /** 世界 */
    private world;
    /** 配置 */
    private config;
    /** 面部动画系统 */
    private facialAnimationSystem;
    /** 表情数据映射 */
    private expressions;
    /** 事件发射器 */
    private eventEmitter;
    /** 微表情计时器 */
    private microExpressionTimer;
    /** 自然变化计时器 */
    private naturalVariationTimer;
    /** 自然变化噪声种子 */
    private naturalVariationSeed;
    /**
     * 构造函数
     * @param entity 实体
     * @param world 世界
     * @param config 配置
     */
    constructor(entity: Entity, world: World, config?: EmotionBlendControllerConfig);
    /**
     * 初始化表情
     */
    private initializeExpressions;
    /**
     * 更新控制器
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新表情权重
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateExpressionWeights;
    /**
     * 更新微表情
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateMicroExpressions;
    /**
     * 触发随机微表情
     */
    private triggerRandomMicroExpression;
    /**
     * 更新自然变化
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateNaturalVariation;
    /**
     * 应用自然变化
     */
    private applyNaturalVariation;
    /**
     * 应用混合表情
     */
    private applyBlendedExpression;
    /**
     * 应用覆盖混合
     * @param facialAnimation 面部动画组件
     */
    private applyOverrideBlend;
    /**
     * 应用加法混合
     * @param facialAnimation 面部动画组件
     */
    private applyAdditiveBlend;
    /**
     * 应用乘法混合
     * @param facialAnimation 面部动画组件
     */
    private applyMultiplicativeBlend;
    /**
     * 应用加权混合
     * @param facialAnimation 面部动画组件
     */
    private applyWeightedBlend;
    /**
     * 添加表情
     * @param expression 表情类型
     * @param weight 权重
     * @param duration 持续时间（秒）
     * @param transitionTime 过渡时间（秒）
     * @param priority 优先级
     * @returns 是否成功添加
     */
    addExpression(expression: FacialExpressionType, weight?: number, duration?: number, transitionTime?: number, priority?: number): boolean;
    /**
     * 移除表情
     * @param expression 表情类型
     * @param transitionTime 过渡时间（秒）
     * @returns 是否成功移除
     */
    removeExpression(expression: FacialExpressionType, transitionTime?: number): boolean;
    /**
     * 清除所有表情
     * @param transitionTime 过渡时间（秒）
     */
    clearExpressions(transitionTime?: number): void;
    /**
     * 获取表情数据
     * @param expression 表情类型
     * @returns 表情数据
     */
    getExpressionData(expression: FacialExpressionType): EmotionExpressionData | null;
    /**
     * 获取所有活跃表情
     * @returns 活跃表情数组
     */
    getActiveExpressions(): EmotionExpressionData[];
    /**
     * 获取主要表情
     * @returns 主要表情数据
     */
    getPrimaryExpression(): EmotionExpressionData | null;
    /**
     * 是否有活跃表情
     * @returns 是否有活跃表情
     */
    hasActiveExpressions(): boolean;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    addEventListener(event: string, listener: EventCallback): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    removeEventListener(event: string, listener: EventCallback): void;
}
