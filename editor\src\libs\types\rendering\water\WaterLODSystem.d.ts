import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
/**
 * 水体LOD级别
 */
export declare enum WaterLODLevel {
    /** 高质量 */
    HIGH = 0,
    /** 中高质量 */
    MEDIUM_HIGH = 1,
    /** 中质量 */
    MEDIUM = 2,
    /** 中低质量 */
    MEDIUM_LOW = 3,
    /** 低质量 */
    LOW = 4
}
/**
 * 水体LOD配置
 */
export interface WaterLODConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** LOD距离 */
    lodDistances?: number[];
    /** 是否使用平滑过渡 */
    useSmoothTransition?: boolean;
    /** 过渡时间（秒） */
    transitionTime?: number;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 水体LOD系统事件类型
 */
export declare enum WaterLODSystemEventType {
    /** LOD级别变化 */
    LOD_LEVEL_CHANGED = "lodLevelChanged"
}
/**
 * 水体LOD系统
 */
export declare class WaterLODSystem extends System {
    /** 系统类型 */
    static readonly TYPE = "WaterLODSystem";
    /** 配置 */
    private config;
    /** 水体实体映射 */
    private waterEntities;
    /** 实体LOD级别映射 */
    private entityLODLevels;
    /** 实体过渡状态映射 */
    private entityTransitions;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 调试对象 */
    private debugObjects;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: WaterLODConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 添加水体实体
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterEntity(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体实体
     * @param entity 实体
     */
    removeWaterEntity(entity: Entity): void;
    /**
     * 更新水体LOD级别
     * @param camera 相机
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterLODLevels;
    /**
     * 确定LOD级别
     * @param distance 距离
     * @returns LOD级别
     */
    private determineLODLevel;
    /**
     * 设置实体LOD级别
     * @param entityId 实体ID
     * @param level LOD级别
     */
    private setEntityLODLevel;
    /**
     * 开始过渡
     * @param entityId 实体ID
     * @param fromLevel 起始LOD级别
     * @param toLevel 目标LOD级别
     */
    private startTransition;
    /**
     * 更新过渡状态
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateTransitions;
    /**
     * 更新水体材质
     * @param component 水体组件
     * @param level LOD级别
     */
    private updateWaterMaterial;
    /**
     * 更新水体材质过渡
     * @param component 水体组件
     * @param fromLevel 起始LOD级别
     * @param toLevel 目标LOD级别
     * @param progress 过渡进度
     */
    private updateWaterMaterialTransition;
    /**
     * 更新水体几何体
     * @param component 水体组件
     * @param level LOD级别
     */
    private updateWaterGeometry;
    /**
     * 获取几何体分辨率
     * @param geometry 几何体
     * @returns 分辨率
     */
    private getGeometryResolution;
    /**
     * 根据LOD级别获取分辨率
     * @param level LOD级别
     * @returns 分辨率
     */
    private getResolutionForLevel;
    /**
     * 根据LOD级别获取波浪强度
     * @param level LOD级别
     * @returns 波浪强度
     */
    private getWaveStrengthForLevel;
    /**
     * 根据LOD级别获取反射强度
     * @param level LOD级别
     * @returns 反射强度
     */
    private getReflectionStrengthForLevel;
    /**
     * 根据LOD级别获取折射强度
     * @param level LOD级别
     * @returns 折射强度
     */
    private getRefractionStrengthForLevel;
    /**
     * 根据LOD级别获取法线贴图缩放
     * @param level LOD级别
     * @returns 法线贴图缩放
     */
    private getNormalScaleForLevel;
    /**
     * 根据LOD级别获取水深颜色混合因子
     * @param level LOD级别
     * @returns 水深颜色混合因子
     */
    private getWaterDepthFactorForLevel;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 获取LOD级别颜色
     * @param level LOD级别
     * @returns 颜色
     */
    private getLODLevelColor;
}
