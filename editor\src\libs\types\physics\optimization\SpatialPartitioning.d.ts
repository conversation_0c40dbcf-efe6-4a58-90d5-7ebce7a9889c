/**
 * 物理系统空间分区
 * 用于加速碰撞检测
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
/**
 * 空间分区策略枚举
 */
export declare enum SpatialPartitioningStrategy {
    /** 均匀网格 */
    UNIFORM_GRID = "uniform_grid",
    /** 八叉树 */
    OCTREE = "octree",
    /** BVH树 */
    BVH = "bvh",
    /** 松散八叉树 */
    LOOSE_OCTREE = "loose_octree",
    /** 空间哈希 */
    SPATIAL_HASH = "spatial_hash"
}
/**
 * 空间分区接口
 */
export interface ISpatialPartitioning {
    /** 添加物体 */
    add(body: CANNON.Body): void;
    /** 移除物体 */
    remove(body: CANNON.Body): void;
    /** 更新物体 */
    update(body: CANNON.Body): void;
    /** 更新所有物体 */
    updateAll(): void;
    /** 查询区域内的物体 */
    queryRegion(min: CANNON.Vec3, max: CANNON.Vec3): CANNON.Body[];
    /** 查询射线碰撞的物体 */
    queryRay(from: CANNON.Vec3, to: CANNON.Vec3): CANNON.Body[];
    /** 查询球体碰撞的物体 */
    querySphere(center: CANNON.Vec3, radius: number): CANNON.Body[];
    /** 查询与物体可能碰撞的物体 */
    queryPotentialCollisions(body: CANNON.Body): CANNON.Body[];
    /** 获取所有物体 */
    getBodies(): CANNON.Body[];
    /** 获取物体数量 */
    getBodyCount(): number;
    /** 清空 */
    clear(): void;
    /** 销毁 */
    dispose(): void;
    /** 获取调试信息 */
    getDebugInfo(): any;
    /** 获取调试网格 */
    getDebugMesh(): THREE.Object3D;
}
/**
 * 碰撞对接口
 */
export interface CollisionPair {
    /** 物体A */
    bodyA: CANNON.Body;
    /** 物体B */
    bodyB: CANNON.Body;
}
/**
 * 空间分区工厂
 */
export declare class SpatialPartitioningFactory {
    /**
     * 创建空间分区
     * @param strategy 空间分区策略
     * @param options 空间分区选项
     * @returns 空间分区
     */
    static create(strategy: SpatialPartitioningStrategy, options?: any): ISpatialPartitioning;
}
/**
 * 均匀网格配置接口
 */
export interface UniformGridOptions {
    /** 网格大小 */
    cellSize?: number;
    /** 世界大小 */
    worldSize?: number;
    /** 世界中心 */
    worldCenter?: CANNON.Vec3;
    /** 是否自动调整大小 */
    autoResize?: boolean;
    /** 是否使用动态网格 */
    useDynamicGrid?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 均匀网格类
 */
export declare class UniformGrid implements ISpatialPartitioning {
    /** 网格大小 */
    private cellSize;
    /** 世界大小 */
    private worldSize;
    /** 世界中心 */
    private worldCenter;
    /** 是否自动调整大小 */
    private autoResize;
    /** 是否使用动态网格 */
    private useDynamicGrid;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 网格单元数量 */
    private cellCount;
    /** 网格单元 */
    private cells;
    /** 物体到单元的映射 */
    private bodyToCells;
    /** 物体列表 */
    private bodies;
    /** 调试网格 */
    private debugMesh;
    /**
     * 创建均匀网格
     * @param options 均匀网格配置
     */
    constructor(options?: UniformGridOptions);
    /**
     * 创建调试网格
     */
    private createDebugMesh;
    /**
     * 获取单元索引
     * @param position 位置
     * @returns 单元索引
     */
    private getCellIndex;
    /**
     * 获取单元键
     * @param x X索引
     * @param y Y索引
     * @param z Z索引
     * @returns 单元键
     */
    private getCellKey;
    /**
     * 获取物体占据的单元
     * @param body 物体
     * @returns 单元键列表
     */
    private getBodyCells;
    /**
     * 获取物体的AABB
     * @param body 物体
     * @returns AABB
     */
    private getBodyAABB;
    /**
     * 添加物体
     * @param body 物体
     */
    add(body: CANNON.Body): void;
    /**
     * 检查是否需要调整网格大小
     * @param body 物体
     */
    private checkResize;
    /**
     * 更新调试网格
     */
    private updateDebugMesh;
    /**
     * 重建网格
     */
    private rebuild;
    /**
     * 移除物体
     * @param body 物体
     */
    remove(body: CANNON.Body): void;
    /**
     * 更新物体
     * @param body 物体
     */
    update(body: CANNON.Body): void;
    /**
     * 比较两个单元集合是否相等
     * @param a 单元集合A
     * @param b 单元集合B
     * @returns 是否相等
     */
    private areCellsEqual;
    /**
     * 更新所有物体
     */
    updateAll(): void;
    /**
     * 查询区域内的物体
     * @param min 最小坐标
     * @param max 最大坐标
     * @returns 区域内的物体
     */
    queryRegion(min: CANNON.Vec3, max: CANNON.Vec3): CANNON.Body[];
    /**
     * 检查两个AABB是否重叠
     * @param minA AABB A的最小坐标
     * @param maxA AABB A的最大坐标
     * @param minB AABB B的最小坐标
     * @param maxB AABB B的最大坐标
     * @returns 是否重叠
     */
    private aabbOverlap;
    /**
     * 查询射线碰撞的物体
     * @param from 射线起点
     * @param to 射线终点
     * @returns 射线碰撞的物体
     */
    queryRay(from: CANNON.Vec3, to: CANNON.Vec3): CANNON.Body[];
    /**
     * 遍历射线经过的单元
     * @param from 射线起点
     * @param direction 射线方向
     * @param length 射线长度
     * @param callback 回调函数
     */
    private traverseRay;
    /**
     * 检查射线是否与AABB相交
     * @param from 射线起点
     * @param direction 射线方向
     * @param length 射线长度
     * @param min AABB的最小坐标
     * @param max AABB的最大坐标
     * @returns 是否相交
     */
    private rayAabbIntersect;
    /**
     * 查询球体碰撞的物体
     * @param center 球体中心
     * @param radius 球体半径
     * @returns 球体碰撞的物体
     */
    querySphere(center: CANNON.Vec3, radius: number): CANNON.Body[];
    /**
     * 检查球体是否与AABB相交
     * @param center 球体中心
     * @param radius 球体半径
     * @param min AABB的最小坐标
     * @param max AABB的最大坐标
     * @returns 是否相交
     */
    private sphereAabbIntersect;
    /**
     * 查询与物体可能碰撞的物体
     * @param body 物体
     * @returns 可能碰撞的物体
     */
    queryPotentialCollisions(body: CANNON.Body): CANNON.Body[];
    /**
     * 获取所有物体
     * @returns 所有物体
     */
    getBodies(): CANNON.Body[];
    /**
     * 获取物体数量
     * @returns 物体数量
     */
    getBodyCount(): number;
    /**
     * 清空
     */
    clear(): void;
    /**
     * 销毁
     */
    dispose(): void;
    /**
     * 获取调试信息
     * @returns 调试信息
     */
    getDebugInfo(): any;
    /**
     * 获取调试网格
     * @returns 调试网格
     */
    getDebugMesh(): THREE.Object3D;
}
