/**
 * 子片段测试
 */
import * as THREE from 'three';
import { SubClip } from '../../src/animation/SubClip';
import { AnimationSubClip } from '../../src/animation/AnimationSubClip';
import { SubClipSequence, SubClipSequenceEventType } from '../../src/animation/SubClipSequence';
import { SubClipTransition, TransitionType } from '../../src/animation/SubClipTransition';
import { SubClipModifier, ModifierType } from '../../src/animation/SubClipModifier';
import { SubClipEvent, EventTriggerType } from '../../src/animation/SubClipEvent';

describe('子片段测试', () => {
  let walkClip: THREE.AnimationClip;
  let runClip: THREE.AnimationClip;
  let jumpClip: THREE.AnimationClip;

  // 在每个测试前创建测试动画
  beforeEach(() => {
    // 创建行走动画
    walkClip = new THREE.AnimationClip('walk', 1, [
      // 左腿动画
      new THREE.QuaternionKeyframeTrack(
        'leftLeg.quaternion',
        [0, 0.5, 1],
        [
          0, 0, 0, 1, // 初始姿势
          0.2, 0, 0, 0.98, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      ),
      // 右腿动画
      new THREE.QuaternionKeyframeTrack(
        'rightLeg.quaternion',
        [0, 0.5, 1],
        [
          0, 0, 0, 1, // 初始姿势
          -0.2, 0, 0, 0.98, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);

    // 创建跑步动画
    runClip = new THREE.AnimationClip('run', 0.6, [
      // 左腿动画
      new THREE.QuaternionKeyframeTrack(
        'leftLeg.quaternion',
        [0, 0.3, 0.6],
        [
          0, 0, 0, 1, // 初始姿势
          0.4, 0, 0, 0.92, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      ),
      // 右腿动画
      new THREE.QuaternionKeyframeTrack(
        'rightLeg.quaternion',
        [0, 0.3, 0.6],
        [
          0, 0, 0, 1, // 初始姿势
          -0.4, 0, 0, 0.92, // 抬腿
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);

    // 创建跳跃动画
    jumpClip = new THREE.AnimationClip('jump', 1, [
      // 根骨骼动画
      new THREE.VectorKeyframeTrack(
        'root.position',
        [0, 0.5, 1],
        [
          0, 0, 0, // 初始位置
          0, 0.5, 0, // 跳起
          0, 0, 0 // 回到初始位置
        ]
      ),
      // 左腿动画
      new THREE.QuaternionKeyframeTrack(
        'leftLeg.quaternion',
        [0, 0.25, 0.75, 1],
        [
          0, 0, 0, 1, // 初始姿势
          0.3, 0, 0, 0.95, // 弯曲
          0.3, 0, 0, 0.95, // 保持弯曲
          0, 0, 0, 1 // 回到初始姿势
        ]
      ),
      // 右腿动画
      new THREE.QuaternionKeyframeTrack(
        'rightLeg.quaternion',
        [0, 0.25, 0.75, 1],
        [
          0, 0, 0, 1, // 初始姿势
          0.3, 0, 0, 0.95, // 弯曲
          0.3, 0, 0, 0.95, // 保持弯曲
          0, 0, 0, 1 // 回到初始姿势
        ]
      )
    ]);
  });

  // 测试基本子片段功能
  test('基本子片段功能', () => {
    // 创建子片段
    const subClip = new SubClip({
      name: 'walkPart',
      originalClip: walkClip,
      startTime: 0.2,
      endTime: 0.8,
      loop: true
    });

    // 验证子片段属性
    expect(subClip.getName()).toBe('walkPart');
    expect(subClip.getStartTime()).toBe(0.2);
    expect(subClip.getEndTime()).toBe(0.8);
    expect(subClip.getDuration()).toBe(0.6);
    expect(subClip.getLoop()).toBe(true);

    // 获取子片段
    const clip = subClip.getClip();
    
    // 验证子片段
    expect(clip).toBeDefined();
    expect(clip?.name).toBe('walkPart');
    expect(clip?.duration).toBeCloseTo(0.6);
  });

  // 测试高级子片段功能
  test('高级子片段功能', () => {
    // 创建高级子片段
    const advancedSubClip = new AnimationSubClip({
      name: 'runPart',
      originalClipName: 'run',
      startTime: 0.1,
      endTime: 0.5,
      loop: true,
      reverse: true,
      timeScale: 0.8
    });

    // 设置原始剪辑
    advancedSubClip.setOriginalClip(runClip);

    // 验证高级子片段属性
    expect(advancedSubClip.getName()).toBe('runPart');
    expect(advancedSubClip.getStartTime()).toBe(0.1);
    expect(advancedSubClip.getEndTime()).toBe(0.5);
    expect(advancedSubClip.getLoop()).toBe(true);
    expect(advancedSubClip.getReverse()).toBe(true);
    expect(advancedSubClip.getTimeScale()).toBe(0.8);

    // 获取子片段
    const clip = advancedSubClip.getSubClip();
    
    // 验证子片段
    expect(clip).toBeDefined();
    expect(clip?.name).toBe('runPart');
    expect(clip?.duration).toBeCloseTo(0.4);
  });

  // 测试子片段序列功能
  test('子片段序列功能', () => {
    // 创建子片段
    const walkSubClip = new SubClip({
      name: 'walkPart',
      originalClip: walkClip,
      startTime: 0.2,
      endTime: 0.8,
      loop: false
    });

    const runSubClip = new SubClip({
      name: 'runPart',
      originalClip: runClip,
      startTime: 0.1,
      endTime: 0.5,
      loop: false
    });

    const jumpSubClip = new SubClip({
      name: 'jumpPart',
      originalClip: jumpClip,
      startTime: 0.0,
      endTime: 1.0,
      loop: false
    });

    // 创建子片段序列
    const sequence = new SubClipSequence({
      name: 'testSequence',
      loop: true,
      autoPlay: false
    });

    // 添加子片段到序列
    sequence.addSubClip(walkSubClip, 1.0, 0.3);
    sequence.addSubClip(runSubClip, 0.8, 0.2);
    sequence.addSubClip(jumpSubClip, 1.0, 0.5);

    // 验证序列属性
    expect(sequence.getName()).toBe('testSequence');
    expect(sequence.getLoop()).toBe(true);
    expect(sequence.getAutoPlay()).toBe(false);
    expect(sequence.getItemCount()).toBe(3);
    expect(sequence.getTotalDuration()).toBeCloseTo(2.8);

    // 测试事件监听
    let eventFired = false;
    sequence.addEventListener(SubClipSequenceEventType.SEQUENCE_START, () => {
      eventFired = true;
    });

    // 播放序列
    sequence.play();

    // 验证事件触发
    expect(eventFired).toBe(true);
  });

  // 测试子片段过渡功能
  test('子片段过渡功能', () => {
    // 创建子片段
    const walkSubClip = new SubClip({
      name: 'walkPart',
      originalClip: walkClip,
      startTime: 0.0,
      endTime: 1.0,
      loop: false
    });

    const runSubClip = new SubClip({
      name: 'runPart',
      originalClip: runClip,
      startTime: 0.0,
      endTime: 0.6,
      loop: false
    });

    // 创建子片段过渡
    const transition = new SubClipTransition({
      name: 'walkToRun',
      fromClip: walkSubClip,
      toClip: runSubClip,
      duration: 0.5,
      type: TransitionType.EASE_IN_OUT
    });

    // 验证过渡属性
    expect(transition.getName()).toBe('walkToRun');
    expect(transition.getFromClip()).toBe(walkSubClip);
    expect(transition.getToClip()).toBe(runSubClip);
    expect(transition.getDuration()).toBe(0.5);
    expect(transition.getType()).toBe(TransitionType.EASE_IN_OUT);
    expect(transition.isInTransition()).toBe(false);

    // 开始过渡
    transition.start();

    // 验证过渡状态
    expect(transition.isInTransition()).toBe(true);
    expect(transition.getProgress()).toBe(0);
  });

  // 测试子片段变形功能
  test('子片段变形功能', () => {
    // 创建子片段
    const walkSubClip = new SubClip({
      name: 'walkPart',
      originalClip: walkClip,
      startTime: 0.0,
      endTime: 1.0,
      loop: true
    });

    // 获取原始子片段
    const originalClip = walkSubClip.getClip();

    // 创建子片段变形器
    const modifier = new SubClipModifier({
      name: 'speedUp',
      type: ModifierType.TIME_SCALE,
      params: { scale: 2.0 },
      enabled: true
    });

    // 应用变形
    const modifiedClip = modifier.apply(originalClip!);

    // 验证变形结果
    expect(modifiedClip.name).toBe(`${originalClip?.name}_speedUp`);
    expect(modifiedClip.duration).toBeCloseTo(originalClip!.duration / 2);
  });

  // 测试子片段事件功能
  test('子片段事件功能', () => {
    // 创建子片段
    const jumpSubClip = new SubClip({
      name: 'jumpPart',
      originalClip: jumpClip,
      startTime: 0.0,
      endTime: 1.0,
      loop: false
    });

    // 创建事件回调
    let eventTriggered = false;
    const callback = () => {
      eventTriggered = true;
    };

    // 创建子片段事件
    const event = new SubClipEvent({
      name: 'jumpPeak',
      triggerType: EventTriggerType.TIME,
      triggerValue: 0.5,
      callback,
      once: true,
      enabled: true
    });

    // 验证事件属性
    expect(event.getName()).toBe('jumpPeak');
    expect(event.getTriggerType()).toBe(EventTriggerType.TIME);
    expect(event.getTriggerValue()).toBe(0.5);
    expect(event.isOnce()).toBe(true);
    expect(event.isEnabled()).toBe(true);
    expect(event.isTriggered()).toBe(false);

    // 检查是否应该触发
    expect(event.shouldTrigger(0.4, 0.4, jumpSubClip)).toBe(false);
    expect(event.shouldTrigger(0.5, 0.5, jumpSubClip)).toBe(true);

    // 触发事件
    event.trigger(0.5, 0.5, jumpSubClip);

    // 验证事件触发
    expect(eventTriggered).toBe(true);
    expect(event.isTriggered()).toBe(true);

    // 重置事件
    event.reset();

    // 验证事件重置
    expect(event.isTriggered()).toBe(false);
  });
});
