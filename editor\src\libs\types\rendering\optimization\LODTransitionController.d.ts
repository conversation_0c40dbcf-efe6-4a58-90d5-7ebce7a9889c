/**
 * LOD过渡控制器
 * 用于实现LOD级别之间的平滑过渡
 */
import * as THREE from 'three';
import { LODLevel } from './LODComponent';
/**
 * 过渡类型枚举
 */
export declare enum TransitionType {
    /** 透明度过渡 */
    OPACITY = "opacity",
    /** 顶点混合过渡 */
    VERTEX_BLEND = "vertex_blend",
    /** 交叉淡入淡出过渡 */
    CROSS_FADE = "cross_fade",
    /** 形态过渡 */
    MORPH = "morph"
}
/**
 * LOD过渡控制器配置接口
 */
export interface LODTransitionControllerOptions {
    /** 过渡持续时间（毫秒） */
    duration?: number;
    /** 过渡类型 */
    type?: TransitionType;
    /** 是否使用缓动 */
    useEasing?: boolean;
    /** 缓动函数 */
    easingFunction?: (t: number) => number;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 过渡状态接口
 */
export interface TransitionState {
    /** 起始网格 */
    fromMesh: THREE.Mesh;
    /** 目标网格 */
    toMesh: THREE.Mesh;
    /** 起始级别 */
    fromLevel: LODLevel;
    /** 目标级别 */
    toLevel: LODLevel;
    /** 开始时间 */
    startTime: number;
    /** 持续时间 */
    duration: number;
    /** 过渡类型 */
    type: TransitionType;
    /** 过渡进度（0-1） */
    progress: number;
    /** 是否完成 */
    completed: boolean;
    /** 完成回调 */
    onComplete?: () => void;
    /** 过渡网格 */
    transitionMesh?: THREE.Mesh;
}
/**
 * LOD过渡控制器类
 */
export declare class LODTransitionController {
    /** 默认过渡持续时间（毫秒） */
    private static readonly DEFAULT_DURATION;
    /** 过渡持续时间（毫秒） */
    private duration;
    /** 过渡类型 */
    private type;
    /** 是否使用缓动 */
    private useEasing;
    /** 缓动函数 */
    private easingFunction;
    /** 是否启用调试 */
    private debug;
    /** 活跃过渡列表 */
    private activeTransitions;
    /** 是否支持顶点混合 */
    private supportsVertexBlending;
    /** 是否支持形态目标 */
    private supportsMorphTargets;
    /**
     * 创建LOD过渡控制器
     * @param options LOD过渡控制器配置
     */
    constructor(options?: LODTransitionControllerOptions);
    /**
     * 检查是否支持顶点混合
     * @returns 是否支持顶点混合
     */
    private checkVertexBlendingSupport;
    /**
     * 检查是否支持形态目标
     * @returns 是否支持形态目标
     */
    private checkMorphTargetsSupport;
    /**
     * 默认缓动函数（缓入缓出）
     * @param t 时间（0-1）
     * @returns 缓动值（0-1）
     */
    private defaultEasingFunction;
    /**
     * 开始LOD级别过渡
     * @param parent 父对象
     * @param fromMesh 起始网格
     * @param toMesh 目标网格
     * @param fromLevel 起始级别
     * @param toLevel 目标级别
     * @param onComplete 完成回调
     * @returns 是否成功开始过渡
     */
    startTransition(parent: THREE.Object3D, fromMesh: THREE.Mesh, toMesh: THREE.Mesh, fromLevel: LODLevel, toLevel: LODLevel, onComplete?: () => void): boolean;
    /**
     * 初始化透明度过渡
     * @param _parent 父对象
     * @param state 过渡状态
     */
    private initializeOpacityTransition;
    /**
     * 确保材质支持透明度
     * @param mesh 网格
     */
    private ensureMaterialTransparency;
    /**
     * 设置网格透明度
     * @param mesh 网格
     * @param opacity 透明度（0-1）
     */
    private setMeshOpacity;
    /**
     * 初始化顶点混合过渡
     * @param parent 父对象
     * @param state 过渡状态
     */
    private initializeVertexBlendTransition;
    /**
     * 创建顶点混合几何体
     * @param fromGeometry 起始几何体
     * @param toGeometry 目标几何体
     * @returns 混合几何体
     */
    private createVertexBlendGeometry;
    /**
     * 创建顶点混合材质
     * @param fromMesh 起始网格
     * @param toMesh 目标网格
     * @returns 混合材质
     */
    private createVertexBlendMaterial;
    /**
     * 初始化交叉淡入淡出过渡
     * @param _parent 父对象
     * @param state 过渡状态
     */
    private initializeCrossFadeTransition;
    /**
     * 初始化形态过渡
     * @param parent 父对象
     * @param state 过渡状态
     */
    private initializeMorphTransition;
    /**
     * 创建形态几何体
     * @param fromGeometry 起始几何体
     * @param toGeometry 目标几何体
     * @returns 形态几何体
     */
    private createMorphGeometry;
    /**
     * 更新过渡
     * @param _deltaTime 时间增量（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 更新透明度过渡
     * @param state 过渡状态
     */
    private updateOpacityTransition;
    /**
     * 更新顶点混合过渡
     * @param state 过渡状态
     */
    private updateVertexBlendTransition;
    /**
     * 更新交叉淡入淡出过渡
     * @param state 过渡状态
     */
    private updateCrossFadeTransition;
    /**
     * 更新形态过渡
     * @param state 过渡状态
     */
    private updateMorphTransition;
    /**
     * 完成过渡
     * @param parent 父对象
     * @param state 过渡状态
     */
    private completeTransition;
    /**
     * 取消过渡
     * @param parent 父对象
     */
    cancelTransition(parent: THREE.Object3D): void;
    /**
     * 获取活跃过渡数量
     * @returns 活跃过渡数量
     */
    getActiveTransitionCount(): number;
    /**
     * 清除所有过渡
     */
    clearAllTransitions(): void;
}
