/**
 * 动作捕捉模块
 * 导出动作捕捉系统的所有组件和工具
 */

// 导出系统
export { MotionCaptureSystem } from './MotionCaptureSystem';

// 导出组件
export { MotionCaptureComponent } from './components/MotionCaptureComponent';
export { MotionCapturePoseComponent, MotionCapturePoseType } from './components/MotionCapturePoseComponent';

// 导出配置类型
export type { MotionCaptureSystemConfig, MotionCaptureResults } from './MotionCaptureSystem';
export type { MotionCaptureComponentConfig } from './components/MotionCaptureComponent';
export type { MotionCapturePoseComponentConfig, MotionCapturePoseState } from './components/MotionCapturePoseComponent';

// 导出工具函数
export { solveMotionCapturePose } from './utils/solveMotionCapturePose';
export { evaluatePose } from './utils/evaluatePose';

// 导出类型
export type { LandmarkData, WorldLandmarkData, LandmarkConnection, LandmarkConnections as LandmarkConnectionsType } from './types/LandmarkData';

// 导出工具函数
export {
  createEmptyLandmark,
  createEmptyWorldLandmark,
  cloneLandmark,
  cloneWorldLandmark,
  lerpLandmark,
  lerpWorldLandmark,
  landmarkDistance,
  landmarkVisibilityWeight,
  filterLandmarksByVisibility,
  smoothLandmarks
} from './types/LandmarkData';

// 导出常量
export { LandmarkIndices, LandmarkConnections, LandmarkGroups } from './constants/LandmarkIndices';

/**
 * 动作捕捉模块
 *
 * 动作捕捉模块提供了一套完整的动作捕捉系统，用于处理动作捕捉数据并将其应用到角色骨骼上。
 * 该模块支持从摄像头或其他输入设备获取动作捕捉数据，解算姿势，评估姿势类型，并通过网络同步动作捕捉数据。
 *
 * 主要功能：
 * - 动作捕捉数据处理
 * - 姿势解算
 * - 姿势评估
 * - 网络同步
 * - 平滑处理
 * - 可视化调试
 *
 * 使用示例：
 * ```typescript
 * import {
 *   MotionCaptureSystem,
 *   MotionCaptureComponent,
 *   MotionCapturePoseComponent,
 *   MotionCapturePoseType
 * } from '../mocap';
 *
 * // 创建动作捕捉系统
 * const motionCaptureSystem = new MotionCaptureSystem(world, {
 *   debug: true,
 *   enableNetworkSync: true,
 *   enablePoseEvaluation: true,
 *   smoothingFactor: 0.5,
 *   visibilityThreshold: 0.1
 * });
 *
 * // 添加动作捕捉系统到世界
 * world.addSystem(motionCaptureSystem);
 *
 * // 创建动作捕捉组件
 * const motionCaptureComponent = new MotionCaptureComponent(entity, {
 *   smoothingFactor: 0.5,
 *   visibilityThreshold: 0.1
 * });
 *
 * // 添加动作捕捉组件到实体
 * entity.addComponent(motionCaptureComponent);
 *
 * // 创建姿势组件
 * const motionCapturePoseComponent = new MotionCapturePoseComponent(entity, {
 *   poseHoldThreshold: 0.25,
 *   poseAngleThreshold: 1.25
 * });
 *
 * // 添加姿势组件到实体
 * entity.addComponent(motionCapturePoseComponent);
 *
 * // 注册动作捕捉组件到系统
 * motionCaptureSystem.registerMotionCaptureComponent(entity, motionCaptureComponent);
 * motionCaptureSystem.registerPoseComponent(entity, motionCapturePoseComponent);
 *
 * // 获取动作捕捉数据
 * const { worldLandmarks, landmarks } = getMotionCaptureData();
 *
 * // 设置动作捕捉数据
 * motionCaptureComponent.setWorldLandmarks(worldLandmarks);
 * motionCaptureComponent.setLandmarks(landmarks);
 *
 * // 发送动作捕捉数据
 * motionCaptureSystem.sendMotionCaptureResults({
 *   worldLandmarks,
 *   landmarks
 * });
 *
 * // 获取当前姿势类型
 * const currentPose = motionCapturePoseComponent.currentPose;
 *
 * // 检查是否是特定姿势
 * const isSitting = motionCapturePoseComponent.getPoseState(MotionCapturePoseType.SITTING)?.begun;
 * ```
 */
