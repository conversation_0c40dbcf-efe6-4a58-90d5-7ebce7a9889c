import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface RedisOptions {
  url: string;
  password?: string;
  database: number;
  keyPrefix: string;
  retryAttempts: number;
  retryDelay: number;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
  keepAlive: number;
  family: number;
  connectTimeout: number;
  commandTimeout: number;
}

@Injectable()
export class RedisConfig {
  constructor(private configService: ConfigService) {}

  createRedisOptions(): RedisOptions {
    return {
      url: this.configService.get('REDIS_URL', 'redis://localhost:6379'),
      password: this.configService.get('REDIS_PASSWORD'),
      database: this.configService.get('REDIS_DB', 0),
      keyPrefix: this.configService.get('REDIS_KEY_PREFIX', 'asset-lib:'),
      retryAttempts: this.configService.get('REDIS_RETRY_ATTEMPTS', 3),
      retryDelay: this.configService.get('REDIS_RETRY_DELAY', 1000),
      maxRetriesPerRequest: this.configService.get('REDIS_MAX_RETRIES_PER_REQUEST', 3),
      lazyConnect: true,
      keepAlive: this.configService.get('REDIS_KEEP_ALIVE', 30000),
      family: 4,
      connectTimeout: this.configService.get('REDIS_CONNECT_TIMEOUT', 10000),
      commandTimeout: this.configService.get('REDIS_COMMAND_TIMEOUT', 5000),
    };
  }
}
