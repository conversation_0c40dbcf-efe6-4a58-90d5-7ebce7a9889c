/**
 * RoBERTa情感分析模型
 * 基于RoBERTa预训练模型的情感分析实现
 */
import { EmotionAnalysisResult } from './EmotionBasedAnimationGenerator';
import { EmotionAnalysisRequest } from './AIModel';
/**
 * RoBERTa情感分析模型配置
 */
export interface RoBERTaEmotionModelConfig {
    /** 是否使用远程API */
    useRemoteAPI?: boolean;
    /** 远程API URL */
    remoteAPIUrl?: string;
    /** API密钥 */
    apiKey?: string;
    /** 模型路径 */
    modelPath?: string;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存大小 */
    cacheSize?: number;
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用量化模型 */
    useQuantized?: boolean;
    /** 量化位数 */
    quantizationBits?: 8 | 16 | 32;
    /** 批处理大小 */
    batchSize?: number;
    /** 模型变体 */
    modelVariant?: 'base' | 'large' | 'distilled';
    /** 情感类别 */
    emotionCategories?: string[];
    /** 是否使用多标签分类 */
    useMultiLabel?: boolean;
    /** 置信度阈值 */
    confidenceThreshold?: number;
}
/**
 * RoBERTa情感分析模型
 */
export declare class RoBERTaEmotionModel {
    /** 配置 */
    private config;
    /** 是否已初始化 */
    private initialized;
    /** 是否正在初始化 */
    private initializing;
    /** 模型 */
    private model;
    /** 分词器 */
    private tokenizer;
    /** 缓存 */
    private cache;
    /** 调试模式 */
    private debug;
    /** 默认情感类别 */
    private static readonly DEFAULT_EMOTION_CATEGORIES;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: RoBERTaEmotionModelConfig);
    /**
     * 初始化模型
     * @returns 是否成功初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 加载本地模型
     */
    private loadLocalModel;
    /**
     * 分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    analyzeEmotion(text: string, options?: Partial<Omit<EmotionAnalysisRequest, 'text'>>): Promise<EmotionAnalysisResult>;
    /**
     * 使用本地模型分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    private analyzeEmotionWithLocalModel;
    /**
     * 使用远程API分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    private analyzeEmotionWithRemoteAPI;
    /**
     * 处理情感预测结果
     * @param prediction 预测结果
     * @param options 选项
     * @returns 情感分析结果
     */
    private processEmotionPrediction;
    /**
     * 生成情感变化
     * @param scores 情感分数
     * @returns 情感变化
     */
    private generateEmotionChanges;
    /**
     * 生成详细情感
     * @param scores 情感分数
     * @returns 详细情感
     */
    private generateDetailedEmotions;
    /**
     * 规范化文本
     * @param text 文本
     * @returns 规范化后的文本
     */
    private normalizeText;
    /**
     * 模拟预测
     * @param input 输入
     * @returns 预测结果
     */
    private mockPredict;
}
