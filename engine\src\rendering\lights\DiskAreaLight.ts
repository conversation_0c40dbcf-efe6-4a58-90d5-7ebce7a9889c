/**
 * 圆盘区域光
 * 物理精确的圆盘区域光
 */
import * as THREE from 'three';
import { AreaLight, AreaLightOptions, AreaLightType } from './AreaLight';

/**
 * 圆盘区域光选项接口
 */
export interface DiskAreaLightOptions extends AreaLightOptions {
  /** 光源类型 */
  type: AreaLightType.DISK;
  /** 半径 */
  radius?: number;
  /** 是否使用物理单位 */
  usePhysicalUnits?: boolean;
  /** 功率（瓦特） */
  power?: number;
  /** 发光效率（流明/瓦特） */
  efficacy?: number;
  /** 辅助对象细分数 */
  helperSegments?: number;
}

/**
 * 圆盘区域光组件类
 */
export class DiskAreaLightComponent extends AreaLight {
  /** 半径 */
  private radius: number;

  /** 是否使用物理单位 */
  private usePhysicalUnits: boolean;

  /** 功率（瓦特） */
  private power: number;

  /** 发光效率（流明/瓦特） */
  private efficacy: number;

  /** 辅助对象细分数 */
  private helperSegments: number;

  /** 辅助对象材质 */
  private helperMaterial: THREE.MeshBasicMaterial | null = null;

  /**
   * 创建圆盘区域光组件
   * @param options 圆盘区域光选项
   */
  constructor(options: DiskAreaLightOptions) {
    super(options);

    this.radius = options.radius !== undefined ? options.radius : 1;
    this.usePhysicalUnits = options.usePhysicalUnits !== undefined ? options.usePhysicalUnits : false;
    this.power = options.power !== undefined ? options.power : 60;
    this.efficacy = options.efficacy !== undefined ? options.efficacy : 80;
    this.helperSegments = options.helperSegments !== undefined ? options.helperSegments : 32;

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 创建光源
   * @param options 圆盘区域光选项
   * @returns Three.js聚光灯
   */
  protected createLight(options: DiskAreaLightOptions): THREE.SpotLight {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    const distance = 0; // 无限距离
    const angle = Math.PI / 3; // 60度
    const penumbra = 0.5; // 半影
    const decay = 2; // 物理衰减
    
    const light = new THREE.SpotLight(color, intensity, distance, angle, penumbra, decay);
    
    // 设置阴影
    light.castShadow = options.castShadow !== undefined ? options.castShadow : false;
    
    if (light.castShadow) {
      // 设置阴影贴图大小
      light.shadow.mapSize.width = 1024;
      light.shadow.mapSize.height = 1024;
      
      // 设置阴影相机
      light.shadow.camera.near = 0.1;
      light.shadow.camera.far = 500;
      
      // 设置阴影偏移
      light.shadow.bias = -0.0005;
    }
    
    return light;
  }

  /**
   * 创建辅助对象
   * @returns Three.js圆盘区域光辅助对象
   */
  protected createHelper(): THREE.Object3D {
    // 创建圆盘几何体
    const geometry = new THREE.CircleGeometry(this.radius, this.helperSegments);
    
    // 创建材质
    this.helperMaterial = new THREE.MeshBasicMaterial({
      color: this.helperColor,
      wireframe: true,
      transparent: true,
      opacity: 0.5,
      side: THREE.DoubleSide
    });
    
    // 创建网格
    const mesh = new THREE.Mesh(geometry, this.helperMaterial);
    mesh.visible = this.showHelper;
    
    // 创建组
    const group = new THREE.Group();
    group.add(mesh);
    
    // 创建法线辅助对象
    const arrowHelper = new THREE.ArrowHelper(
      new THREE.Vector3(0, 0, 1),
      new THREE.Vector3(0, 0, 0),
      this.radius,
      this.helperColor.getHex(),
      0.2 * this.radius,
      0.1 * this.radius
    );
    group.add(arrowHelper);
    
    return group;
  }

  /**
   * 更新辅助对象颜色
   */
  protected updateHelperColor(): void {
    if (this.helperMaterial) {
      this.helperMaterial.color.copy(this.helperColor);
    }
    
    if (this.helper && this.helper instanceof THREE.Group) {
      const arrowHelper = this.helper.children[1];
      if (arrowHelper && arrowHelper instanceof THREE.ArrowHelper) {
        arrowHelper.setColor(this.helperColor);
      }
    }
  }

  /**
   * 更新物理强度
   */
  private updatePhysicalIntensity(): void {
    if (!this.usePhysicalUnits) return;

    // 计算流明
    const lumens = this.power * this.efficacy;
    
    // 计算面积
    const area = Math.PI * this.radius * this.radius;
    
    // 计算强度（流明/面积）
    const intensity = lumens / area;
    
    // 设置光源强度
    if (this.light instanceof THREE.SpotLight) {
      this.light.intensity = intensity;
    }
  }

  /**
   * 设置半径
   * @param radius 半径
   */
  public setRadius(radius: number): void {
    this.radius = radius;
    
    // 更新辅助对象
    this.updateHelper();
    
    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取半径
   * @returns 半径
   */
  public getRadius(): number {
    return this.radius;
  }

  /**
   * 设置是否使用物理单位
   * @param use 是否使用
   */
  public setUsePhysicalUnits(use: boolean): void {
    this.usePhysicalUnits = use;
    
    // 如果使用物理单位，则更新光源强度
    if (use) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取是否使用物理单位
   * @returns 是否使用
   */
  public isUsePhysicalUnits(): boolean {
    return this.usePhysicalUnits;
  }

  /**
   * 设置功率
   * @param power 功率（瓦特）
   */
  public setPower(power: number): void {
    this.power = power;
    
    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取功率
   * @returns 功率（瓦特）
   */
  public getPower(): number {
    return this.power;
  }

  /**
   * 设置发光效率
   * @param efficacy 发光效率（流明/瓦特）
   */
  public setEfficacy(efficacy: number): void {
    this.efficacy = efficacy;
    
    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取发光效率
   * @returns 发光效率（流明/瓦特）
   */
  public getEfficacy(): number {
    return this.efficacy;
  }

  /**
   * 设置辅助对象细分数
   * @param segments 细分数
   */
  public setHelperSegments(segments: number): void {
    this.helperSegments = segments;
    
    // 更新辅助对象
    this.updateHelper();
  }

  /**
   * 获取辅助对象细分数
   * @returns 细分数
   */
  public getHelperSegments(): number {
    return this.helperSegments;
  }

  /**
   * 更新辅助对象
   */
  private updateHelper(): void {
    if (this.helper) {
      // 更新辅助对象
      const parent = this.helper.parent;
      
      if (parent) {
        parent.remove(this.helper);
      }
      
      // 如果辅助对象有dispose方法，则调用
      if (this.helper instanceof THREE.Group) {
        for (const child of this.helper.children) {
          if (child instanceof THREE.Mesh && child.geometry) {
            (child.geometry as any).dispose();
          }
          if (child instanceof THREE.Mesh && child.material) {
            if (Array.isArray(child.material)) {
              for (const material of child.material) {
                (material as any).dispose();
              }
            } else {
              (child.material as any).dispose();
            }
          }
        }
      }
      
      this.helper = this.createHelper();
      
      if (parent) {
        parent.add(this.helper);
      }
    }
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 圆盘区域光不需要每帧更新
  }
}
