# 环境感知和响应示例

## 简介

本示例展示了如何使用DL（Digital Learning）引擎的环境感知和响应系统，使角色能够感知周围的环境并做出相应的反应。通过本示例，您可以了解如何设置环境感知组件、创建环境响应规则、使用预设以及可视化环境状态。

## 功能特性

- **环境感知**：角色可以感知周围的环境类型、天气、光照、温度等参数
- **环境响应**：角色可以根据环境变化做出相应的反应，如播放动画、音效等
- **环境预设**：提供常见环境交互场景的预设，如雨天、雪天、炎热天气等
- **环境可视化**：可视化展示环境参数的变化，帮助理解角色的感知和响应

## 使用说明

1. 打开示例项目
2. 在场景中选择角色实体
3. 添加环境感知组件和环境响应组件
4. 配置环境感知参数和响应规则
5. 运行场景，观察角色对环境变化的反应

## 步骤详解

### 步骤1：添加环境感知组件

首先，我们需要为角色添加环境感知组件，使其能够感知周围的环境。

```typescript
// 导入环境感知组件
import { EnvironmentAwarenessComponent } from '../../engine/src/environment';

// 获取角色实体
const character = scene.getEntityByName('Character');

// 添加环境感知组件
const awarenessComponent = new EnvironmentAwarenessComponent(character, {
  awarenessRange: 50, // 感知范围（米）
  updateFrequency: 1000, // 更新频率（毫秒）
  debug: true, // 启用调试模式
  autoDetect: true // 自动检测环境
});

// 添加组件到角色
character.addComponent(awarenessComponent);
```

### 步骤2：添加环境响应组件

接下来，我们需要添加环境响应组件，定义角色如何响应环境变化。

```typescript
// 导入环境响应组件和相关类型
import { 
  EnvironmentResponseComponent, 
  ResponseType, 
  ResponsePriority 
} from '../../engine/src/environment';

// 创建环境响应组件
const responseComponent = new EnvironmentResponseComponent(character, {
  autoRespond: true, // 自动响应
  debug: true // 启用调试模式
});

// 添加组件到角色
character.addComponent(responseComponent);
```

### 步骤3：创建响应规则

现在，我们需要创建响应规则，定义角色在特定环境条件下的行为。

```typescript
// 导入环境类型和天气类型
import { EnvironmentType, WeatherType } from '../../engine/src/environment';

// 创建雨天响应规则
const rainyRule = {
  id: 'rainy_weather_response',
  name: '雨天响应',
  description: '角色在雨天的响应行为',
  responseType: ResponseType.ANIMATION,
  priority: ResponsePriority.MEDIUM,
  conditions: [
    {
      type: 'weather',
      params: { weatherType: WeatherType.RAINY },
      evaluate: (data) => data.weatherType === WeatherType.RAINY
    },
    {
      type: 'environment',
      params: { environmentType: EnvironmentType.OUTDOOR },
      evaluate: (data) => data.environmentType === EnvironmentType.OUTDOOR
    }
  ],
  actions: [
    {
      type: ResponseType.ANIMATION,
      params: { animationName: 'character_rainy_idle', blendTime: 0.5 },
      execute: (entity) => {
        const animator = entity.getAnimator();
        if (animator) {
          animator.playAnimation('character_rainy_idle', { blendTime: 0.5, loop: true });
        }
      },
      stop: (entity) => {
        const animator = entity.getAnimator();
        if (animator) {
          animator.stopAnimation('character_rainy_idle', { blendTime: 0.5 });
        }
      }
    },
    {
      type: ResponseType.SOUND,
      params: { soundName: 'character_rain_reaction', volume: 0.8 },
      execute: (entity) => {
        const audioSource = entity.getAudioSource();
        if (audioSource) {
          audioSource.play('character_rain_reaction', { volume: 0.8, loop: false });
        }
      },
      stop: (entity) => {
        const audioSource = entity.getAudioSource();
        if (audioSource) {
          audioSource.stop('character_rain_reaction');
        }
      }
    }
  ],
  cooldown: 10000, // 10秒冷却时间
  enabled: true
};

// 添加规则到响应组件
responseComponent.addRule(rainyRule);
```

### 步骤4：使用预设

DL（Digital Learning）引擎提供了一系列预定义的环境响应预设，可以快速应用到角色上。

```typescript
// 导入预设
import { 
  createRainyWeatherResponse, 
  createSnowyWeatherResponse, 
  createHotWeatherResponse 
} from '../../engine/src/environment';

// 应用雨天预设
responseComponent.addRule(createRainyWeatherResponse());

// 应用雪天预设
responseComponent.addRule(createSnowyWeatherResponse());

// 应用炎热天气预设
responseComponent.addRule(createHotWeatherResponse());
```

### 步骤5：添加环境感知系统

最后，我们需要添加环境感知系统到世界中，使其能够检测和更新环境信息。

```typescript
// 导入环境感知系统
import { EnvironmentAwarenessSystem } from '../../engine/src/environment';

// 创建环境感知系统
const environmentSystem = new EnvironmentAwarenessSystem(world, {
  debug: true, // 启用调试模式
  updateFrequency: 1000, // 更新频率（毫秒）
  autoDetect: true, // 自动检测环境
  enableResponse: true, // 启用环境响应
  enableVisualization: true // 启用环境可视化
});

// 添加系统到世界
world.addSystem(environmentSystem);
```

## 技术要点

- **实体组件系统**：环境感知和响应系统基于DL（Digital Learning）引擎的实体组件系统架构
- **事件驱动**：环境变化会触发相应的事件，响应组件根据事件做出反应
- **条件评估**：响应规则包含条件和动作，条件满足时执行相应的动作
- **优先级系统**：响应规则具有优先级，高优先级的规则会覆盖低优先级的规则
- **冷却机制**：响应规则具有冷却时间，防止频繁触发

## 学习要点

- 了解环境感知组件的配置和使用方法
- 掌握环境响应规则的创建和管理
- 学习如何使用预设快速应用常见的环境交互行为
- 理解环境感知系统的工作原理和配置方法
- 掌握环境可视化工具的使用方法

## 扩展建议

- **添加更多环境类型**：扩展环境类型枚举，添加更多特定场景的环境类型
- **创建自定义响应**：根据项目需求创建自定义的环境响应规则
- **增强环境检测**：使用物理射线检测或其他方法增强环境检测能力
- **添加环境转换效果**：在环境变化时添加过渡效果，使响应更加自然
- **集成AI系统**：将环境感知系统与AI系统集成，使角色能够根据环境做出更智能的决策

## 相关资源

- [环境感知组件API文档](../../docs/api/environment/EnvironmentAwarenessComponent.md)
- [环境响应组件API文档](../../docs/api/environment/EnvironmentResponseComponent.md)
- [环境感知系统API文档](../../docs/api/environment/EnvironmentAwarenessSystem.md)
- [环境预设API文档](../../docs/api/environment/EnvironmentPresets.md)
- [环境编辑器使用指南](../../docs/user-manual/environment-editor.md)
