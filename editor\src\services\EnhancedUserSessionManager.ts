/**
 * 增强用户会话管理器
 * 扩展UserSessionManager功能，提供更强大的会话管理能力
 */
import { EventEmitter } from '../utils/EventEmitter';
import { permissionLogService } from './PermissionLogService';
import { userInteractionLogService, UserActivityType as InteractionActivityType } from './UserInteractionLogService';

/**
 * 用户角色
 */
export enum UserRole {
  /** 访客 */
  GUEST = 'guest',
  /** 用户 */
  USER = 'user',
  /** 管理员 */
  ADMIN = 'admin',
  /** 超级管理员 */
  SUPER_ADMIN = 'super_admin'
}

/**
 * 用户权限
 */
export enum UserPermission {
  /** 查看 */
  VIEW = 'view',
  /** 编辑 */
  EDIT = 'edit',
  /** 创建 */
  CREATE = 'create',
  /** 删除 */
  DELETE = 'delete',
  /** 管理用户 */
  MANAGE_USERS = 'manage_users',
  /** 管理权限 */
  MANAGE_PERMISSIONS = 'manage_permissions',
  /** 管理系统 */
  MANAGE_SYSTEM = 'manage_system'
}

/**
 * 用户会话接口
 */
export interface UserSession {
  /** 用户ID */
  userId: string;
  /** 用户名 */
  username: string;
  /** 角色 */
  role: UserRole;
  /** 权限集合 */
  permissions: Set<UserPermission>;
  /** 连接时间 */
  connectionTime: number;
  /** 最后活动时间 */
  lastActivityTime: number;
  /** 是否在线 */
  isOnline: boolean;
  /** 是否已验证 */
  isAuthenticated: boolean;
  /** 会话令牌 */
  sessionToken?: string;
  /** 客户端信息 */
  clientInfo?: {
    /** IP地址 */
    ip?: string;
    /** 用户代理 */
    userAgent?: string;
    /** 设备类型 */
    deviceType?: string;
    /** 浏览器 */
    browser?: string;
    /** 操作系统 */
    os?: string;
  };
}

/**
 * 用户会话状态
 */
export enum UserSessionState {
  /** 活跃 */
  ACTIVE = 'active',
  /** 空闲 */
  IDLE = 'idle',
  /** 离开 */
  AWAY = 'away',
  /** 离线 */
  OFFLINE = 'offline',
  /** 忙碌 */
  BUSY = 'busy',
  /** 请勿打扰 */
  DO_NOT_DISTURB = 'do_not_disturb'
}

/**
 * 用户活动类型
 */
export enum UserActivityType {
  /** 编辑 */
  EDIT = 'edit',
  /** 查看 */
  VIEW = 'view',
  /** 评论 */
  COMMENT = 'comment',
  /** 协作 */
  COLLABORATE = 'collaborate',
  /** 系统操作 */
  SYSTEM = 'system'
}

/**
 * 用户活动记录
 */
export interface UserActivity {
  /** 活动ID */
  id: string;
  /** 用户ID */
  userId: string;
  /** 活动类型 */
  type: UserActivityType;
  /** 活动时间 */
  timestamp: number;
  /** 活动详情 */
  details?: any;
  /** 相关资源ID */
  resourceId?: string;
  /** 相关资源类型 */
  resourceType?: string;
}

/**
 * 增强用户会话
 */
export interface EnhancedUserSession extends UserSession {
  /** 会话状态 */
  state: UserSessionState;
  /** 最后活动时间 */
  lastActivityTime: number;
  /** 最后活动类型 */
  lastActivityType?: UserActivityType;
  /** 活动历史 */
  activityHistory?: UserActivity[];
  /** 会话标签 */
  tags?: string[];
  /** 会话元数据 */
  metadata?: Record<string, any>;
  /** 组织ID */
  organizationId?: string;
  /** 部门ID */
  departmentId?: string;
  /** 团队ID */
  teamId?: string;
  /** 是否是临时会话 */
  isTemporary?: boolean;
  /** 会话过期时间 */
  expiresAt?: number;
  /** 会话创建时间 */
  createdAt: number;
  /** 会话更新时间 */
  updatedAt: number;
}

/**
 * 增强用户会话管理器配置
 */
export interface EnhancedUserSessionManagerConfig {
  /** 会话超时时间（毫秒） */
  sessionTimeout?: number;
  /** 空闲超时时间（毫秒） */
  idleTimeout?: number;
  /** 是否启用会话超时 */
  enableSessionTimeout?: boolean;
  /** 是否启用权限检查 */
  enablePermissionCheck?: boolean;
  /** 默认角色 */
  defaultRole?: UserRole;
  /** 是否允许匿名用户 */
  allowAnonymous?: boolean;
  /** 最大用户数量 */
  maxUsers?: number;
  /** 是否记录活动历史 */
  recordActivityHistory?: boolean;
  /** 活动历史最大记录数 */
  maxActivityHistory?: number;
  /** 是否启用用户状态自动更新 */
  enableAutoStateUpdate?: boolean;
  /** 是否启用会话恢复 */
  enableSessionRecovery?: boolean;
  /** 是否启用会话同步 */
  enableSessionSync?: boolean;
}

/**
 * 增强用户会话管理器
 */
export class EnhancedUserSessionManager extends EventEmitter {
  /** 配置 */
  private config: Required<EnhancedUserSessionManagerConfig>;
  
  /** 用户会话映射表 */
  private sessions: Map<string, EnhancedUserSession> = new Map();
  
  /** 会话清理定时器ID */
  private cleanupTimerId: number | null = null;
  
  /** 状态更新定时器ID */
  private stateUpdateTimerId: number | null = null;
  
  /** 会话同步定时器ID */
  private syncTimerId: number | null = null;
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: EnhancedUserSessionManagerConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      sessionTimeout: 30 * 60 * 1000, // 30分钟
      idleTimeout: 5 * 60 * 1000, // 5分钟
      enableSessionTimeout: true,
      enablePermissionCheck: true,
      defaultRole: UserRole.USER,
      allowAnonymous: true,
      maxUsers: 100,
      recordActivityHistory: true,
      maxActivityHistory: 50,
      enableAutoStateUpdate: true,
      enableSessionRecovery: true,
      enableSessionSync: true,
      ...config
    };
    
    // 初始化定时器
    this.initializeTimers();
  }
  
  /**
   * 初始化定时器
   * @private
   */
  private initializeTimers(): void {
    // 会话清理定时器
    if (this.config.enableSessionTimeout) {
      this.cleanupTimerId = window.setInterval(() => {
        this.cleanupSessions();
      }, 60 * 1000); // 每分钟检查一次
    }
    
    // 状态更新定时器
    if (this.config.enableAutoStateUpdate) {
      this.stateUpdateTimerId = window.setInterval(() => {
        this.updateUserStates();
      }, 30 * 1000); // 每30秒更新一次
    }
    
    // 会话同步定时器
    if (this.config.enableSessionSync) {
      this.syncTimerId = window.setInterval(() => {
        this.syncSessions();
      }, 5 * 60 * 1000); // 每5分钟同步一次
    }
  }
  
  /**
   * 清理过期会话
   * @private
   */
  private cleanupSessions(): void {
    const now = Date.now();
    
    for (const [userId, session] of this.sessions.entries()) {
      // 检查会话是否过期
      if (session.expiresAt && session.expiresAt < now) {
        this.removeSession(userId);
        continue;
      }
      
      // 检查会话是否超时
      if (this.config.enableSessionTimeout && 
          session.isOnline && 
          now - session.lastActivityTime > this.config.sessionTimeout) {
        // 将用户标记为离线
        session.isOnline = false;
        session.state = UserSessionState.OFFLINE;
        
        // 触发用户离线事件
        this.emit('userOffline', userId, session);
        
        // 记录用户活动
        this.recordUserActivity(userId, UserActivityType.SYSTEM, {
          action: 'session_timeout',
          previousState: UserSessionState.IDLE,
          newState: UserSessionState.OFFLINE
        });
      }
      // 检查用户是否空闲
      else if (this.config.enableAutoStateUpdate && 
               session.isOnline && 
               session.state === UserSessionState.ACTIVE && 
               now - session.lastActivityTime > this.config.idleTimeout) {
        // 将用户标记为空闲
        session.state = UserSessionState.IDLE;
        
        // 触发用户状态变更事件
        this.emit('userStateChanged', userId, session);
        
        // 记录用户活动
        this.recordUserActivity(userId, UserActivityType.SYSTEM, {
          action: 'state_changed',
          previousState: UserSessionState.ACTIVE,
          newState: UserSessionState.IDLE
        });
      }
    }
  }
  
  /**
   * 更新用户状态
   * @private
   */
  private updateUserStates(): void {
    // 实现用户状态自动更新逻辑
  }
  
  /**
   * 同步会话
   * @private
   */
  private syncSessions(): void {
    // 实现会话同步逻辑
  }

  /**
   * 转换活动类型以匹配UserInteractionLogService的枚举
   * @param activityType 本地活动类型
   * @returns 转换后的活动类型
   * @private
   */
  private convertActivityType(activityType: UserActivityType): InteractionActivityType {
    switch (activityType) {
      case UserActivityType.EDIT:
        return InteractionActivityType.EDIT;
      case UserActivityType.VIEW:
        return InteractionActivityType.VIEW;
      case UserActivityType.COLLABORATE:
        return InteractionActivityType.COLLABORATION;
      case UserActivityType.SYSTEM:
        return InteractionActivityType.NAVIGATE; // 映射到最接近的类型
      case UserActivityType.COMMENT:
        return InteractionActivityType.CREATE; // 评论可以视为创建操作
      default:
        return InteractionActivityType.VIEW; // 默认映射
    }
  }
  
  /**
   * 创建用户会话
   * @param userId 用户ID
   * @param username 用户名
   * @param role 角色
   * @param isAuthenticated 是否已验证
   * @param sessionToken 会话令牌
   * @param clientInfo 客户端信息
   * @returns 用户会话
   */
  public createSession(
    userId: string,
    username: string,
    role: UserRole = this.config.defaultRole,
    isAuthenticated: boolean = true,
    sessionToken?: string,
    clientInfo?: UserSession['clientInfo']
  ): EnhancedUserSession {
    // 检查是否已存在会话
    if (this.sessions.has(userId)) {
      return this.updateExistingSession(userId, {
        isOnline: true,
        lastActivityTime: Date.now(),
        isAuthenticated,
        sessionToken,
        clientInfo,
        state: UserSessionState.ACTIVE,
        updatedAt: Date.now()
      });
    }
    
    // 检查是否超出最大用户数量
    if (this.sessions.size >= this.config.maxUsers) {
      throw new Error(`Maximum number of users (${this.config.maxUsers}) reached`);
    }
    
    // 检查是否允许匿名用户
    if (!isAuthenticated && !this.config.allowAnonymous) {
      throw new Error('Anonymous users are not allowed');
    }
    
    // 创建新会话
    const now = Date.now();
    const session: EnhancedUserSession = {
      userId,
      username,
      role,
      permissions: new Set([]),
      connectionTime: now,
      lastActivityTime: now,
      isOnline: true,
      isAuthenticated,
      sessionToken,
      clientInfo,
      state: UserSessionState.ACTIVE,
      activityHistory: [],
      createdAt: now,
      updatedAt: now
    };
    
    // 添加到会话映射表
    this.sessions.set(userId, session);
    
    // 触发用户创建事件
    this.emit('userCreated', userId, session);
    
    // 记录用户活动
    this.recordUserActivity(userId, UserActivityType.SYSTEM, {
      action: 'session_created'
    });
    
    // 记录权限日志
    permissionLogService.logUserSessionCreated(userId, role);
    
    return session;
  }
  
  /**
   * 更新现有会话
   * @param userId 用户ID
   * @param updates 更新数据
   * @returns 更新后的会话
   * @private
   */
  private updateExistingSession(userId: string, updates: Partial<EnhancedUserSession>): EnhancedUserSession {
    const session = this.sessions.get(userId)!;
    
    // 更新会话
    Object.assign(session, updates);
    
    // 触发用户更新事件
    this.emit('userUpdated', userId, session);
    
    return session;
  }
  
  /**
   * 记录用户活动
   * @param userId 用户ID
   * @param type 活动类型
   * @param details 活动详情
   * @param resourceId 相关资源ID
   * @param resourceType 相关资源类型
   */
  public recordUserActivity(
    userId: string,
    type: UserActivityType,
    details?: any,
    resourceId?: string,
    resourceType?: string
  ): void {
    if (!this.config.recordActivityHistory) {
      return;
    }
    
    const session = this.sessions.get(userId);
    if (!session) {
      return;
    }
    
    // 创建活动记录
    const activity: UserActivity = {
      id: `activity_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
      userId,
      type,
      timestamp: Date.now(),
      details,
      resourceId,
      resourceType
    };
    
    // 初始化活动历史（如果不存在）
    if (!session.activityHistory) {
      session.activityHistory = [];
    }
    
    // 添加到活动历史
    session.activityHistory.push(activity);
    
    // 限制活动历史大小
    if (session.activityHistory.length > this.config.maxActivityHistory) {
      session.activityHistory.shift();
    }
    
    // 更新最后活动时间和类型
    session.lastActivityTime = activity.timestamp;
    session.lastActivityType = type;
    session.updatedAt = activity.timestamp;
    
    // 如果用户状态是空闲或离开，且有新活动，则更新为活跃
    if ((session.state === UserSessionState.IDLE || session.state === UserSessionState.AWAY) && 
        type !== UserActivityType.SYSTEM) {
      const previousState = session.state;
      session.state = UserSessionState.ACTIVE;
      
      // 触发用户状态变更事件
      this.emit('userStateChanged', userId, session);
      
      // 记录状态变更
      this.recordUserActivity(userId, UserActivityType.SYSTEM, {
        action: 'state_changed',
        previousState,
        newState: UserSessionState.ACTIVE
      });
    }
    
    // 触发用户活动事件
    this.emit('userActivity', userId, activity);
    
    // 记录到用户交互日志服务
    // 转换活动类型以匹配UserInteractionLogService的枚举
    const convertedActivity = {
      ...activity,
      type: this.convertActivityType(activity.type)
    };
    userInteractionLogService.logUserActivity(convertedActivity);
  }
  
  /**
   * 设置用户状态
   * @param userId 用户ID
   * @param state 状态
   * @returns 是否成功
   */
  public setUserState(userId: string, state: UserSessionState): boolean {
    const session = this.sessions.get(userId);
    if (!session) {
      return false;
    }
    
    const previousState = session.state;
    
    // 更新状态
    session.state = state;
    session.updatedAt = Date.now();
    
    // 如果设置为离线，更新在线状态
    if (state === UserSessionState.OFFLINE) {
      session.isOnline = false;
    } else if (!session.isOnline) {
      session.isOnline = true;
    }
    
    // 触发用户状态变更事件
    this.emit('userStateChanged', userId, session);
    
    // 记录用户活动
    this.recordUserActivity(userId, UserActivityType.SYSTEM, {
      action: 'state_changed',
      previousState,
      newState: state
    });
    
    return true;
  }
  
  /**
   * 移除会话
   * @param userId 用户ID
   * @returns 是否成功
   */
  public removeSession(userId: string): boolean {
    const session = this.sessions.get(userId);
    if (!session) {
      return false;
    }
    
    // 从会话映射表中移除
    this.sessions.delete(userId);
    
    // 触发用户移除事件
    this.emit('userRemoved', userId, session);
    
    // 记录权限日志
    permissionLogService.logUserSessionRemoved(userId);
    
    return true;
  }
  
  /**
   * 获取所有会话
   * @returns 会话列表
   */
  public getAllSessions(): EnhancedUserSession[] {
    return Array.from(this.sessions.values());
  }
  
  /**
   * 获取会话
   * @param userId 用户ID
   * @returns 会话
   */
  public getSession(userId: string): EnhancedUserSession | undefined {
    return this.sessions.get(userId);
  }
  
  /**
   * 销毁
   */
  public destroy(): void {
    // 清除定时器
    if (this.cleanupTimerId !== null) {
      window.clearInterval(this.cleanupTimerId);
      this.cleanupTimerId = null;
    }
    
    if (this.stateUpdateTimerId !== null) {
      window.clearInterval(this.stateUpdateTimerId);
      this.stateUpdateTimerId = null;
    }
    
    if (this.syncTimerId !== null) {
      window.clearInterval(this.syncTimerId);
      this.syncTimerId = null;
    }
    
    // 清空会话
    this.sessions.clear();
    
    // 移除所有监听器
    this.removeAllListeners();
  }
}

// 创建单例实例
export const enhancedUserSessionManager = new EnhancedUserSessionManager();
