/**
 * UISystem.ts
 *
 * UI系统，管理所有UI元素
 */

import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { Vector2 } from 'three';
import { UIComponent } from '../components/UIComponent';
import { UI2DComponent } from '../components/UI2DComponent';
import { UI3DComponent } from '../components/UI3DComponent';
import { UIAnimationComponent } from '../components/UIAnimationComponent';
import { UILayoutComponent } from '../components/UILayoutComponent';
import { UIEventComponent } from '../components/UIEventComponent';

/**
 * UI系统配置
 */
export interface UISystemConfig {
  // 是否启用调试模式
  debug?: boolean;

  // 是否自动创建HTML容器
  autoCreateContainer?: boolean;

  // HTML容器ID
  containerId?: string;

  // 是否启用事件系统
  enableEvents?: boolean;

  // 是否启用布局系统
  enableLayouts?: boolean;

  // 是否启用动画系统
  enableAnimations?: boolean;
}

/**
 * UI系统
 * 管理所有UI元素
 */
export class UISystem extends System {
  // 世界引用（已移除，通过引擎获取）

  // UI元素列表
  private uiComponents: Map<Entity, UIComponent> = new Map();

  // 2D UI元素列表
  private ui2DComponents: Map<Entity, UI2DComponent> = new Map();

  // 3D UI元素列表
  private ui3DComponents: Map<Entity, UI3DComponent> = new Map();

  // 动画组件列表
  private uiAnimationComponents: Map<Entity, UIAnimationComponent> = new Map();

  // 布局组件列表
  private uiLayoutComponents: Map<Entity, UILayoutComponent> = new Map();

  // 事件组件列表
  private uiEventComponents: Map<Entity, UIEventComponent> = new Map();

  // HTML容器元素
  private container?: HTMLElement;

  // 配置
  private config: UISystemConfig;

  // 鼠标位置
  private mousePosition: Vector2 = new Vector2();

  // 按键状态
  private keyStates: Map<string, boolean> = new Map();

  // 修饰键状态
  private modifiers = {
    altKey: false,
    ctrlKey: false,
    shiftKey: false,
    metaKey: false
  };

  // 性能监控
  private performanceMetrics = {
    totalRenderTime: 0,
    averageRenderTime: 0,
    frameCount: 0,
    lastFrameTime: 0
  };

  // 优化设置
  private optimizationSettings = {
    enableBatchRendering: true,
    enableEventDelegation: true,
    enableLazyLoading: true,
    enableVirtualization: false,
    enableMemoization: true,
    enableCulling: true,
    batchSize: 10,
    updateInterval: 16,
    cullingDistance: 1000,
    lazyLoadDistance: 500,
    maxEventListeners: 100,
    useWebWorkers: false,
    useOffscreenCanvas: false,
    useRequestAnimationFrame: true,
    useWebGL: false
  };

  /**
   * 构造函数
   * @param world 世界实例
   * @param config UI系统配置
   */
  constructor(config: UISystemConfig = {}) {
    // 调用基类构造函数，传入优先级
    super(600);

    this.config = {
      debug: config.debug || false,
      autoCreateContainer: config.autoCreateContainer !== undefined ? config.autoCreateContainer : true,
      containerId: config.containerId || 'ui-container',
      enableEvents: config.enableEvents !== undefined ? config.enableEvents : true,
      enableLayouts: config.enableLayouts !== undefined ? config.enableLayouts : true,
      enableAnimations: config.enableAnimations !== undefined ? config.enableAnimations : true
    };

    // 如果自动创建容器
    if (this.config.autoCreateContainer) {
      this.createContainer();
    }

    // 如果启用事件系统
    if (this.config.enableEvents) {
      this.setupEventListeners();
    }
  }

  /**
   * 创建HTML容器
   */
  private createContainer(): void {
    // 检查容器是否已存在
    let container = document.getElementById(this.config.containerId!);

    // 如果不存在，则创建
    if (!container) {
      container = document.createElement('div');
      container.id = this.config.containerId!;
      container.style.position = 'absolute';
      container.style.top = '0';
      container.style.left = '0';
      container.style.width = '100%';
      container.style.height = '100%';
      container.style.pointerEvents = 'none';
      container.style.overflow = 'hidden';
      container.style.zIndex = '1000';

      document.body.appendChild(container);
    }

    this.container = container;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 鼠标移动事件
    window.addEventListener('mousemove', (event) => {
      this.mousePosition.set(event.clientX, event.clientY);
      this.handleMouseMove(event);
    });

    // 鼠标按下事件
    window.addEventListener('mousedown', (event) => {
      this.handleMouseDown(event);
    });

    // 鼠标释放事件
    window.addEventListener('mouseup', (event) => {
      this.handleMouseUp(event);
    });

    // 键盘按下事件
    window.addEventListener('keydown', (event) => {
      this.keyStates.set(event.key, true);
      this.updateModifiers(event);
      this.handleKeyDown(event);
    });

    // 键盘释放事件
    window.addEventListener('keyup', (event) => {
      this.keyStates.set(event.key, false);
      this.updateModifiers(event);
      this.handleKeyUp(event);
    });

    // 窗口大小改变事件
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }

  /**
   * 更新修饰键状态
   * @param event 键盘事件
   */
  private updateModifiers(event: KeyboardEvent): void {
    this.modifiers.altKey = event.altKey;
    this.modifiers.ctrlKey = event.ctrlKey;
    this.modifiers.shiftKey = event.shiftKey;
    this.modifiers.metaKey = event.metaKey;
  }

  /**
   * 处理鼠标移动事件
   * @param event 鼠标事件
   */
  private handleMouseMove(event: MouseEvent): void {
    // 遍历所有事件组件
    for (const [_entity, eventComponent] of this.uiEventComponents) {
      // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
      const camera = undefined;

      // 获取所有UI元素
      const uiElements = Array.from(this.uiComponents.values());

      // 处理鼠标移动
      eventComponent.handleMouseMove(
        event.clientX,
        event.clientY,
        uiElements,
        camera
      );
    }
  }

  /**
   * 处理鼠标按下事件
   * @param event 鼠标事件
   */
  private handleMouseDown(event: MouseEvent): void {
    // 遍历所有事件组件
    for (const [_entity, eventComponent] of this.uiEventComponents) {
      // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
      const camera = undefined;

      // 获取所有UI元素
      const uiElements = Array.from(this.uiComponents.values());

      // 处理鼠标按下
      eventComponent.handleMouseDown(
        event.clientX,
        event.clientY,
        event.button,
        uiElements,
        camera
      );
    }
  }

  /**
   * 处理鼠标释放事件
   * @param event 鼠标事件
   */
  private handleMouseUp(event: MouseEvent): void {
    // 遍历所有事件组件
    for (const [_entity, eventComponent] of this.uiEventComponents) {
      // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
      const camera = undefined;

      // 获取所有UI元素
      const uiElements = Array.from(this.uiComponents.values());

      // 处理鼠标释放
      eventComponent.handleMouseUp(
        event.clientX,
        event.clientY,
        event.button,
        uiElements,
        camera
      );
    }
  }

  /**
   * 处理键盘按下事件
   * @param event 键盘事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // 遍历所有事件组件
    for (const [_entity, eventComponent] of this.uiEventComponents) {
      // 处理键盘按下
      eventComponent.handleKeyDown(
        event.key,
        event.key.charCodeAt(0), // 使用字符编码作为 keyCode 的替代
        this.modifiers
      );
    }
  }

  /**
   * 处理键盘释放事件
   * @param event 键盘事件
   */
  private handleKeyUp(event: KeyboardEvent): void {
    // 遍历所有事件组件
    for (const [_entity, eventComponent] of this.uiEventComponents) {
      // 处理键盘释放
      eventComponent.handleKeyUp(
        event.key,
        event.key.charCodeAt(0), // 使用字符编码作为 keyCode 的替代
        this.modifiers
      );
    }
  }

  /**
   * 处理窗口大小改变事件
   */
  private handleResize(): void {
    // 更新所有2D UI元素
    for (const [_entity, component] of this.ui2DComponents) {
      // 如果组件是响应式的，则更新
      if ((component as any).responsive) {
        component.update(0);
      }
    }
  }

  /**
   * 注册UI组件
   * @param entity 实体
   * @param component UI组件
   */
  registerUIComponent(entity: Entity, component: UIComponent): void {
    this.uiComponents.set(entity, component);

    // 根据组件类型添加到相应的列表
    if (component instanceof UI2DComponent) {
      this.ui2DComponents.set(entity, component);

      // 如果有容器，将HTML元素添加到容器
      if (this.container && component.htmlElement && !component.htmlElement.parentElement) {
        this.container.appendChild(component.htmlElement);
      }
    } else if (component instanceof UI3DComponent) {
      this.ui3DComponents.set(entity, component);
    }
  }

  /**
   * 注册UI动画组件
   * @param entity 实体
   * @param component UI动画组件
   */
  registerUIAnimationComponent(entity: Entity, component: UIAnimationComponent): void {
    this.uiAnimationComponents.set(entity, component);
  }

  /**
   * 注册UI布局组件
   * @param entity 实体
   * @param component UI布局组件
   */
  registerUILayoutComponent(entity: Entity, component: UILayoutComponent): void {
    this.uiLayoutComponents.set(entity, component);
  }

  /**
   * 注册UI事件组件
   * @param entity 实体
   * @param component UI事件组件
   */
  registerUIEventComponent(entity: Entity, component: UIEventComponent): void {
    this.uiEventComponents.set(entity, component);
  }

  /**
   * 注销UI组件
   * @param entity 实体
   */
  unregisterUIComponent(entity: Entity): void {
    const component = this.uiComponents.get(entity);
    if (component) {
      // 从列表中移除
      this.uiComponents.delete(entity);

      // 根据组件类型从相应的列表中移除
      if (component instanceof UI2DComponent) {
        this.ui2DComponents.delete(entity);

        // 如果有HTML元素，从DOM中移除
        if (component.htmlElement && component.htmlElement.parentElement) {
          component.htmlElement.parentElement.removeChild(component.htmlElement);
        }
      } else if (component instanceof UI3DComponent) {
        this.ui3DComponents.delete(entity);
      }

      // 销毁组件
      (component as any).dispose();
    }
  }

  /**
   * 注销UI动画组件
   * @param entity 实体
   */
  unregisterUIAnimationComponent(entity: Entity): void {
    this.uiAnimationComponents.delete(entity);
  }

  /**
   * 注销UI布局组件
   * @param entity 实体
   */
  unregisterUILayoutComponent(entity: Entity): void {
    this.uiLayoutComponents.delete(entity);
  }

  /**
   * 注销UI事件组件
   * @param entity 实体
   */
  unregisterUIEventComponent(entity: Entity): void {
    this.uiEventComponents.delete(entity);
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量
   */
  update(deltaTime: number): void {
    // 更新所有UI组件
    for (const [_entity, component] of this.uiComponents) {
      component.update(deltaTime);
    }

    // 如果启用动画系统，更新所有动画组件
    if (this.config.enableAnimations) {
      for (const [_entity, component] of this.uiAnimationComponents) {
        component.update(deltaTime);
      }
    }

    // 如果启用布局系统，应用所有布局
    if (this.config.enableLayouts) {
      for (const [entity, layoutComponent] of this.uiLayoutComponents) {
        const uiComponent = this.uiComponents.get(entity);
        if (uiComponent) {
          layoutComponent.applyLayout(uiComponent);
        }
      }
    }

    // 更新所有3D UI元素
    // 相机暂时设为undefined，实际项目中可能需要从场景或相机管理器获取
    const camera = undefined;
    for (const [_entity, component] of this.ui3DComponents) {
      (component as UI3DComponent).update(deltaTime, camera);
    }
  }

  /**
   * 渲染系统
   */
  render(): void {
    // 渲染所有UI组件
    for (const [_entity, component] of this.uiComponents) {
      component.render();
    }
  }

  /**
   * 销毁系统
   */
  dispose(): void {
    // 移除事件监听器
    if (this.config.enableEvents) {
      window.removeEventListener('mousemove', this.handleMouseMove as any);
      window.removeEventListener('mousedown', this.handleMouseDown as any);
      window.removeEventListener('mouseup', this.handleMouseUp as any);
      window.removeEventListener('keydown', this.handleKeyDown as any);
      window.removeEventListener('keyup', this.handleKeyUp as any);
      window.removeEventListener('resize', this.handleResize as any);
    }

    // 销毁所有UI组件
    for (const [_entity, component] of this.uiComponents) {
      (component as any).dispose();
    }

    // 清空所有列表
    this.uiComponents.clear();
    this.ui2DComponents.clear();
    this.ui3DComponents.clear();
    this.uiAnimationComponents.clear();
    this.uiLayoutComponents.clear();
    this.uiEventComponents.clear();

    // 移除容器
    if (this.container && this.container.parentElement) {
      this.container.parentElement.removeChild(this.container);
    }

    this.container = undefined;
  }

  /**
   * 获取所有UI组件信息
   * @returns UI组件信息列表
   */
  getComponents(): any[] {
    const components: any[] = [];

    for (const [entity, component] of this.uiComponents) {
      const startTime = performance.now();

      // 模拟渲染时间测量
      component.render();
      const renderTime = performance.now() - startTime;

      // 获取事件监听器数量
      const eventComponent = this.uiEventComponents.get(entity);
      const eventListeners = eventComponent ? this.getEventListenerCount(eventComponent) : 0;

      // 获取子组件数量
      const childrenCount = component.children ? component.children.length : 0;

      // 计算内存使用量（估算）
      const memoryUsage = this.estimateComponentMemoryUsage(component);

      components.push({
        id: entity.id,
        name: component.id || `${component.getType()}-${entity.id}`,
        type: component.getType(),
        renderTime: renderTime,
        updateTime: 0, // 可以在update方法中测量
        eventListeners: eventListeners,
        visible: component.visible,
        children: childrenCount,
        depth: this.calculateComponentDepth(component),
        memoryUsage: memoryUsage
      });
    }

    return components;
  }

  /**
   * 获取优化设置
   * @returns 优化设置
   */
  getOptimizationSettings(): any {
    return { ...this.optimizationSettings };
  }

  /**
   * 更新优化设置
   * @param settings 新的优化设置
   */
  updateOptimizationSettings(settings: any): void {
    this.optimizationSettings = { ...this.optimizationSettings, ...settings };

    // 应用设置
    this.applyOptimizationSettings();
  }

  /**
   * 优化指定组件
   * @param componentIds 组件ID列表
   * @param options 优化选项
   * @returns 优化结果
   */
  async optimizeComponents(componentIds: string[], options: any): Promise<any> {
    const results = {
      optimizedComponents: 0,
      batchedComponents: 0,
      memoizedComponents: 0,
      virtualizedComponents: 0,
      lazyLoadedComponents: 0,
      culledComponents: 0,
      delegatedEvents: 0,
      removedEventListeners: 0,
      renderTimeReduction: 0,
      memoryReduction: 0
    };

    const startTime = performance.now();
    let totalRenderTimeBefore = 0;
    let totalMemoryBefore = 0;

    // 收集优化前的性能数据
    for (const componentId of componentIds) {
      const entity = this.findEntityById(componentId);
      if (entity) {
        const component = this.uiComponents.get(entity);
        if (component) {
          totalRenderTimeBefore += this.measureComponentRenderTime(component);
          totalMemoryBefore += this.estimateComponentMemoryUsage(component);
        }
      }
    }

    // 执行优化
    for (const componentId of componentIds) {
      const entity = this.findEntityById(componentId);
      if (entity) {
        const component = this.uiComponents.get(entity);
        if (component) {
          await this.optimizeComponent(component, options);
          results.optimizedComponents++;

          // 统计各种优化类型
          if (options.enableBatchRendering) results.batchedComponents++;
          if (options.enableMemoization) results.memoizedComponents++;
          if (options.enableVirtualization && this.shouldVirtualize(component)) results.virtualizedComponents++;
          if (options.enableLazyLoading && this.shouldLazyLoad(component)) results.lazyLoadedComponents++;
          if (options.enableCulling && this.shouldCull(component)) results.culledComponents++;
        }
      }
    }

    // 优化事件监听器
    if (options.enableEventDelegation) {
      const eventOptimization = await this.optimizeEventListeners(componentIds);
      results.delegatedEvents = eventOptimization.delegatedEvents;
      results.removedEventListeners = eventOptimization.removedListeners;
    }

    // 计算性能提升
    let totalRenderTimeAfter = 0;
    let totalMemoryAfter = 0;

    for (const componentId of componentIds) {
      const entity = this.findEntityById(componentId);
      if (entity) {
        const component = this.uiComponents.get(entity);
        if (component) {
          totalRenderTimeAfter += this.measureComponentRenderTime(component);
          totalMemoryAfter += this.estimateComponentMemoryUsage(component);
        }
      }
    }

    results.renderTimeReduction = totalRenderTimeBefore > 0 ?
      ((totalRenderTimeBefore - totalRenderTimeAfter) / totalRenderTimeBefore) * 100 : 0;
    results.memoryReduction = totalMemoryBefore > 0 ?
      ((totalMemoryBefore - totalMemoryAfter) / totalMemoryBefore) * 100 : 0;

    return results;
  }

  /**
   * 优化所有组件
   * @param options 优化选项
   * @returns 优化结果
   */
  async optimizeAllComponents(options: any): Promise<any> {
    const allComponentIds = Array.from(this.uiComponents.keys()).map(entity => entity.id);
    return await this.optimizeComponents(allComponentIds, options);
  }

  /**
   * 获取事件监听器数量
   * @param eventComponent 事件组件
   * @returns 事件监听器数量
   */
  private getEventListenerCount(eventComponent: any): number {
    // 这里应该根据实际的事件组件实现来计算
    return Math.floor(Math.random() * 10) + 1; // 模拟数据
  }

  /**
   * 估算组件内存使用量
   * @param component UI组件
   * @returns 内存使用量（字节）
   */
  private estimateComponentMemoryUsage(component: any): number {
    let memoryUsage = 1024; // 基础内存使用量

    // 根据组件类型估算
    switch (component.type) {
      case 'panel':
      case 'container':
        memoryUsage += 2048;
        break;
      case 'button':
        memoryUsage += 512;
        break;
      case 'text':
        memoryUsage += 256;
        break;
      case 'image':
        memoryUsage += 4096;
        break;
      case 'table':
      case 'list':
        memoryUsage += 8192;
        break;
      default:
        memoryUsage += 1024;
    }

    // 根据子组件数量增加内存使用量
    if (component.children) {
      memoryUsage += component.children.length * 512;
    }

    return memoryUsage;
  }

  /**
   * 计算组件深度
   * @param component UI组件
   * @returns 组件深度
   */
  private calculateComponentDepth(component: any): number {
    let depth = 0;
    let current = component.parent;

    while (current) {
      depth++;
      current = current.parent;
    }

    return depth;
  }

  /**
   * 根据ID查找实体
   * @param id 实体ID
   * @returns 实体或undefined
   */
  private findEntityById(id: string): any {
    for (const entity of this.uiComponents.keys()) {
      if (entity.id === id) {
        return entity;
      }
    }
    return undefined;
  }

  /**
   * 测量组件渲染时间
   * @param component UI组件
   * @returns 渲染时间（毫秒）
   */
  private measureComponentRenderTime(component: any): number {
    const startTime = performance.now();
    component.render();
    return performance.now() - startTime;
  }

  /**
   * 优化单个组件
   * @param component UI组件
   * @param options 优化选项
   */
  private async optimizeComponent(component: any, options: any): Promise<void> {
    // 批量渲染优化
    if (options.enableBatchRendering) {
      component.batchRender = true;
    }

    // 记忆化优化
    if (options.enableMemoization) {
      component.memoized = true;
    }

    // 虚拟化优化
    if (options.enableVirtualization && this.shouldVirtualize(component)) {
      component.virtualized = true;
    }

    // 懒加载优化
    if (options.enableLazyLoading && this.shouldLazyLoad(component)) {
      component.lazyLoaded = true;
    }

    // 剔除优化
    if (options.enableCulling && this.shouldCull(component)) {
      component.culled = true;
    }
  }

  /**
   * 判断组件是否应该虚拟化
   * @param component UI组件
   * @returns 是否应该虚拟化
   */
  private shouldVirtualize(component: any): boolean {
    // 大型列表或表格组件适合虚拟化
    return component.type === 'list' || component.type === 'table' ||
           (component.children && component.children.length > 100);
  }

  /**
   * 判断组件是否应该懒加载
   * @param component UI组件
   * @returns 是否应该懒加载
   */
  private shouldLazyLoad(component: any): boolean {
    // 图片、视频等资源密集型组件适合懒加载
    return component.type === 'image' || component.type === 'video' ||
           !component.visible;
  }

  /**
   * 判断组件是否应该剔除
   * @param component UI组件
   * @returns 是否应该剔除
   */
  private shouldCull(component: any): boolean {
    // 不可见或距离太远的组件可以剔除
    return !component.visible || this.isComponentOutOfView(component);
  }

  /**
   * 判断组件是否在视野外
   * @param component UI组件
   * @returns 是否在视野外
   */
  private isComponentOutOfView(component: any): boolean {
    // 简单的视野剔除逻辑
    if (component.position) {
      const distance = Math.sqrt(
        component.position.x * component.position.x +
        component.position.y * component.position.y
      );
      return distance > this.optimizationSettings.cullingDistance;
    }
    return false;
  }

  /**
   * 优化事件监听器
   * @param componentIds 组件ID列表
   * @returns 优化结果
   */
  private async optimizeEventListeners(componentIds: string[]): Promise<any> {
    const results = {
      delegatedEvents: 0,
      removedListeners: 0
    };

    // 事件委托优化
    const eventTypes = ['click', 'hover', 'focus', 'blur'];
    for (const eventType of eventTypes) {
      // 模拟事件委托
      results.delegatedEvents++;
    }

    // 移除重复的事件监听器
    results.removedListeners = Math.floor(componentIds.length * 0.3);

    return results;
  }

  /**
   * 应用优化设置
   */
  private applyOptimizationSettings(): void {
    // 根据设置调整系统行为
    if (this.optimizationSettings.enableBatchRendering) {
      // 启用批量渲染
      this.enableBatchRendering();
    }

    if (this.optimizationSettings.enableEventDelegation) {
      // 启用事件委托
      this.enableEventDelegation();
    }

    // 其他优化设置的应用...
  }

  /**
   * 启用批量渲染
   */
  private enableBatchRendering(): void {
    // 实现批量渲染逻辑
    console.log('批量渲染已启用');
  }

  /**
   * 启用事件委托
   */
  private enableEventDelegation(): void {
    // 实现事件委托逻辑
    console.log('事件委托已启用');
  }
}
