import { System } from '../../core/System';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
/**
 * 遮挡剔除系统配置接口
 */
export interface OcclusionCullingSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 遮挡剔除算法 */
    algorithm?: OcclusionCullingAlgorithm;
    /** 遮挡查询精度 */
    precision?: number;
    /** 是否使用层次Z缓冲区 */
    useHierarchicalZBuffer?: boolean;
    /** 是否使用遮挡查询 */
    useOcclusionQuery?: boolean;
    /** 是否使用GPU加速 */
    useGPU?: boolean;
    /** 是否使用保守遮挡剔除 */
    useConservativeOcclusion?: boolean;
    /** 是否使用时间一致性 */
    useTemporalCoherence?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 遮挡查询间隔（帧） */
    occlusionQueryInterval?: number;
    /** 遮挡查询超时（毫秒） */
    occlusionQueryTimeout?: number;
    /** 遮挡查询批处理大小 */
    occlusionQueryBatchSize?: number;
}
/**
 * 遮挡剔除算法枚举
 */
export declare enum OcclusionCullingAlgorithm {
    /** 层次Z缓冲区 */
    HIERARCHICAL_Z_BUFFER = "hierarchical_z_buffer",
    /** 遮挡查询 */
    OCCLUSION_QUERY = "occlusion_query",
    /** 软件光栅化 */
    SOFTWARE_RASTERIZATION = "software_rasterization",
    /** 硬件遮挡查询 */
    HARDWARE_OCCLUSION_QUERY = "hardware_occlusion_query"
}
/**
 * 遮挡剔除状态枚举
 */
export declare enum OcclusionCullingState {
    /** 可见 */
    VISIBLE = "visible",
    /** 不可见 */
    INVISIBLE = "invisible",
    /** 未知 */
    UNKNOWN = "unknown",
    /** 查询中 */
    QUERYING = "querying"
}
/**
 * 遮挡剔除结果接口
 */
export interface OcclusionCullingResult {
    /** 可见物体数量 */
    visibleCount: number;
    /** 不可见物体数量 */
    invisibleCount: number;
    /** 查询中物体数量 */
    queryingCount: number;
    /** 未知物体数量 */
    unknownCount: number;
    /** 总物体数量 */
    totalCount: number;
    /** 剔除率 */
    cullingRate: number;
    /** 查询时间（毫秒） */
    queryTime: number;
}
/**
 * 遮挡剔除系统类
 */
export declare class OcclusionCullingSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 遮挡剔除算法 */
    protected algorithm: OcclusionCullingAlgorithm;
    /** 遮挡查询精度 */
    protected precision: number;
    /** 是否使用层次Z缓冲区 */
    private useHierarchicalZBuffer;
    /** 是否使用遮挡查询 */
    private useOcclusionQuery;
    /** 是否使用GPU加速 */
    protected useGPU: boolean;
    /** 是否使用保守遮挡剔除 */
    protected useConservativeOcclusion: boolean;
    /** 是否使用时间一致性 */
    protected useTemporalCoherence: boolean;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 遮挡查询间隔（帧） */
    private occlusionQueryInterval;
    /** 遮挡查询超时（毫秒） */
    private occlusionQueryTimeout;
    /** 遮挡查询批处理大小 */
    protected occlusionQueryBatchSize: number;
    /** 当前帧计数 */
    private frameCount;
    /** 遮挡剔除状态映射 */
    private occlusionStates;
    /** 遮挡查询映射 */
    private occlusionQueries;
    /** 遮挡查询时间映射 */
    private occlusionQueryTimes;
    /** 层次Z缓冲区 */
    private hierarchicalZBuffer;
    /** 层次Z缓冲区宽度 */
    private hzbWidth;
    /** 层次Z缓冲区高度 */
    private hzbHeight;
    /** 层次Z缓冲区级别数 */
    protected hzbLevels: number;
    /** 遮挡渲染器 */
    private occlusionRenderer;
    /** 遮挡相机 */
    private occlusionCamera;
    /** 遮挡场景 */
    private occlusionScene;
    /** 遮挡渲染目标 */
    private occlusionRenderTarget;
    /** 遮挡查询着色器 */
    private occlusionQueryShader;
    /** 遮挡查询几何体 */
    private occlusionQueryGeometry;
    /** 调试可视化材质 */
    private debugMaterial;
    /** 调试可视化网格 */
    private debugMeshes;
    /** 是否支持WebGL2 */
    private supportsWebGL2;
    /** 是否支持遮挡查询 */
    private supportsOcclusionQuery;
    /**
     * 创建遮挡剔除系统
     * @param options 遮挡剔除系统配置
     */
    constructor(options?: OcclusionCullingSystemOptions);
    /**
     * 检查WebGL支持
     */
    private checkWebGLSupport;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化层次Z缓冲区
     */
    private initializeHierarchicalZBuffer;
    /**
     * 初始化遮挡查询
     */
    private initializeOcclusionQuery;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 获取场景
     * @returns 场景
     */
    private getScene;
    /**
     * 使用层次Z缓冲区进行遮挡剔除
     * @param camera 相机
     * @param scene 场景
     */
    cullWithHierarchicalZBuffer(camera: Camera, scene: Scene): void;
    /**
     * 更新层次Z缓冲区
     * @param camera 相机
     * @param scene 场景
     */
    private updateHierarchicalZBuffer;
    /**
     * 获取潜在遮挡物
     * @param camera 相机
     * @param scene 场景
     * @returns 潜在遮挡物列表
     */
    private getPotentialOccluders;
    /**
     * 检查是否是潜在遮挡物
     * @param object 物体
     * @param camera 相机
     * @returns 是否是潜在遮挡物
     */
    private isPotentialOccluder;
    /**
     * 渲染遮挡物到层次Z缓冲区
     * @param _occluder 遮挡物
     * @param _camera 相机
     */
    private renderOccluderToHierarchicalZBuffer;
    /**
     * 构建层次Z缓冲区的层次结构
     */
    private buildHierarchicalZBufferLevels;
    /**
     * 检查是否被层次Z缓冲区遮挡
     * @param _boundingBox 包围盒
     * @param _camera 相机
     * @param _transform 变换
     * @returns 是否被遮挡
     */
    private isOccludedByHierarchicalZBuffer;
    /**
     * 使用遮挡查询进行遮挡剔除
     * @param camera 相机
     * @param scene 场景
     */
    cullWithOcclusionQuery(camera: Camera, scene: Scene): void;
    /**
     * 准备遮挡查询
     * @param camera 相机
     * @param scene 场景
     */
    private prepareOcclusionQuery;
    /**
     * 检查是否应该执行遮挡查询
     * @param entity 实体
     * @returns 是否应该执行遮挡查询
     */
    private shouldPerformOcclusionQuery;
    /**
     * 执行遮挡查询
     * @param entity 实体
     * @param component 可剔除组件
     * @param transform 变换组件
     * @param _camera 相机
     */
    private performOcclusionQuery;
    /**
     * 处理遮挡查询结果
     */
    private processOcclusionQueryResults;
    /**
     * 使用软件光栅化进行遮挡剔除
     * @param _camera 相机
     * @param _scene 场景
     */
    cullWithSoftwareRasterization(_camera: Camera, _scene: Scene): void;
    /**
     * 使用硬件遮挡查询进行遮挡剔除
     * @param _camera 相机
     * @param _scene 场景
     */
    cullWithHardwareOcclusionQuery(_camera: Camera, _scene: Scene): void;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 获取遮挡剔除结果
     * @returns 遮挡剔除结果
     */
    getOcclusionCullingResult(): OcclusionCullingResult;
    /**
     * 设置算法
     * @param algorithm 算法
     */
    setAlgorithm(algorithm: OcclusionCullingAlgorithm): void;
    /**
     * 获取算法
     * @returns 算法
     */
    getAlgorithm(): OcclusionCullingAlgorithm;
    /**
     * 销毁系统
     */
    dispose(): void;
}
