/**
 * 动画模块导出
 */

// 多动作融合系统
export * from './MultiActionFusionTypes';
export * from './MultiActionFusionManager';
export * from './ActionConflictResolver';
export * from './AnimationStateMachine';

// 现有动画系统 - 避免命名冲突，使用具体导出
export { AnimationBlendingSystem } from './AnimationBlendingSystem';
export type {
  AnimationBlendingSystemConfig,
  AnimationTransition,
  AnimationCondition,
  AnimationBlender,
  AnimationKeyframe
} from './AnimationBlendingSystem';

export * from './BIPAnimationLoader';

export { FacialAnimationClip } from './FacialAnimationClip';
export type {
  FacialAnimationKeyframe,
  ExpressionKeyframe,
  VisemeKeyframe,
  BlendShapeKeyframe
} from './FacialAnimationClip';

export * from './FacialAnimationPresetSystem';
