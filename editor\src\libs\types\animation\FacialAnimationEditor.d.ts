import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { FacialExpressionType, VisemeType } from './FacialAnimation';
/**
 * 面部动画关键帧
 */
export interface FacialAnimationKeyframe {
    /** 时间（秒） */
    time: number;
    /** 表情类型 */
    expression?: FacialExpressionType;
    /** 表情权重 */
    expressionWeight?: number;
    /** 口型类型 */
    viseme?: VisemeType;
    /** 口型权重 */
    visemeWeight?: number;
}
/**
 * 面部动画片段
 */
export interface FacialAnimationClip {
    /** 名称 */
    name: string;
    /** 持续时间（秒） */
    duration: number;
    /** 关键帧 */
    keyframes: FacialAnimationKeyframe[];
    /** 是否循环 */
    loop: boolean;
    /** 用户数据 */
    userData?: any;
}
/**
 * 面部动画编辑器配置
 */
export interface FacialAnimationEditorConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 默认帧率 */
    defaultFrameRate?: number;
    /** 默认持续时间（秒） */
    defaultDuration?: number;
    /** 默认混合时间（秒） */
    defaultBlendTime?: number;
}
/**
 * 面部动画编辑器组件
 */
export declare class FacialAnimationEditorComponent extends Component {
    /** 组件类型 */
    static readonly type = "FacialAnimationEditor";
    /** 动画片段 */
    private clips;
    /** 当前片段 */
    private currentClip;
    /** 当前时间 */
    private currentTime;
    /** 是否播放中 */
    private isPlaying;
    /** 播放速度 */
    private playbackSpeed;
    /** 帧率 */
    private frameRate;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param entity 实体
     */
    constructor(entity: Entity);
    /**
     * 创建动画片段
     * @param name 名称
     * @param duration 持续时间（秒）
     * @param loop 是否循环
     * @returns 动画片段
     */
    createClip(name: string, duration?: number, loop?: boolean): FacialAnimationClip;
    /**
     * 删除动画片段
     * @param name 名称
     * @returns 是否成功删除
     */
    deleteClip(name: string): boolean;
    /**
     * 获取动画片段
     * @param name 名称
     * @returns 动画片段，如果不存在则返回null
     */
    getClip(name: string): FacialAnimationClip | null;
    /**
     * 获取所有动画片段
     * @returns 动画片段数组
     */
    getClips(): FacialAnimationClip[];
    /**
     * 设置当前片段
     * @param name 名称
     * @returns 是否成功设置
     */
    setCurrentClip(name: string): boolean;
    /**
     * 获取当前片段
     * @returns 当前片段，如果不存在则返回null
     */
    getCurrentClip(): FacialAnimationClip | null;
    /**
     * 添加关键帧
     * @param time 时间（秒）
     * @param keyframe 关键帧数据
     * @returns 是否成功添加
     */
    addKeyframe(time: number, keyframe: Partial<FacialAnimationKeyframe>): boolean;
    /**
     * 移除关键帧
     * @param time 时间（秒）
     * @returns 是否成功移除
     */
    removeKeyframe(time: number): boolean;
    /**
     * 获取关键帧
     * @param time 时间（秒）
     * @returns 关键帧，如果不存在则返回null
     */
    getKeyframe(time: number): FacialAnimationKeyframe | null;
    /**
     * 获取当前时间的插值关键帧
     * @returns 插值关键帧
     */
    getInterpolatedKeyframe(): FacialAnimationKeyframe | null;
    /**
     * 线性插值
     * @param a 起始值
     * @param b 结束值
     * @param t 插值因子[0,1]
     * @returns 插值结果
     */
    private lerp;
    /**
     * 播放动画
     * @returns 是否成功开始播放
     */
    play(): boolean;
    /**
     * 暂停动画
     */
    pause(): void;
    /**
     * 停止动画
     */
    stop(): void;
    /**
     * 设置当前时间
     * @param time 时间（秒）
     */
    setTime(time: number): void;
    /**
     * 获取当前时间
     * @returns 当前时间（秒）
     */
    getTime(): number;
    /**
     * 设置播放速度
     * @param speed 播放速度
     */
    setPlaybackSpeed(speed: number): void;
    /**
     * 获取播放速度
     * @returns 播放速度
     */
    getPlaybackSpeed(): number;
    /**
     * 设置帧率
     * @param frameRate 帧率
     */
    setFrameRate(frameRate: number): void;
    /**
     * 获取帧率
     * @returns 帧率
     */
    getFrameRate(): number;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
