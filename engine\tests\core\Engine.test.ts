/**
 * Engine类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Engine, EngineOptions } from '../../src/core/Engine';
import { System } from '../../src/core/System';
import { World } from '../../src/core/World';
import { Time } from '../../src/utils/Time';

// 创建一个测试系统类
class TestSystem extends System {
  public initialized: boolean = false;
  public updated: boolean = false;
  public fixedUpdated: boolean = false;
  public lateUpdated: boolean = false;
  public disposed: boolean = false;
  
  constructor(priority: number = 0) {
    super(priority);
  }
  
  public initialize(): void {
    this.initialized = true;
  }
  
  public update(deltaTime: number): void {
    this.updated = true;
  }
  
  public fixedUpdate(fixedDeltaTime: number): void {
    this.fixedUpdated = true;
  }
  
  public lateUpdate(deltaTime: number): void {
    this.lateUpdated = true;
  }
  
  public dispose(): void {
    this.disposed = true;
  }
}

describe('Engine', () => {
  let engine: Engine;
  
  // 在每个测试前创建一个新的引擎实例
  beforeEach(() => {
    // 创建一个模拟的canvas元素
    const canvas = document.createElement('canvas');
    
    // 创建引擎实例
    engine = new Engine({
      canvas,
      autoStart: false,
      debug: true
    });
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试引擎初始化
  it('应该正确初始化引擎', () => {
    expect(engine).toBeDefined();
    expect(engine.isRunning()).toBe(false);
    
    // 初始化引擎
    engine.initialize();
    
    // 验证引擎已初始化
    expect(engine['initialized']).toBe(true);
  });
  
  // 测试系统管理
  it('应该能够添加和获取系统', () => {
    // 创建测试系统
    const testSystem = new TestSystem();
    
    // 添加系统到引擎
    engine.addSystem(testSystem);
    
    // 验证系统已添加
    expect(engine['systems']).toContain(testSystem);
    
    // 初始化引擎
    engine.initialize();
    
    // 验证系统已初始化
    expect(testSystem.initialized).toBe(true);
  });
  
  // 测试系统优先级
  it('应该按优先级排序系统', () => {
    // 创建测试系统
    const system1 = new TestSystem(1);
    const system2 = new TestSystem(0);
    const system3 = new TestSystem(2);
    
    // 添加系统到引擎
    engine.addSystem(system1);
    engine.addSystem(system2);
    engine.addSystem(system3);
    
    // 验证系统已按优先级排序
    expect(engine['systems'][0]).toBe(system2);
    expect(engine['systems'][1]).toBe(system1);
    expect(engine['systems'][2]).toBe(system3);
  });
  
  // 测试引擎启动和停止
  it('应该能够启动和停止引擎', () => {
    // 启动引擎
    engine.start();
    
    // 验证引擎正在运行
    expect(engine.isRunning()).toBe(true);
    
    // 停止引擎
    engine.stop();
    
    // 验证引擎已停止
    expect(engine.isRunning()).toBe(false);
  });
  
  // 测试更新循环
  it('应该调用系统的更新方法', () => {
    // 创建测试系统
    const testSystem = new TestSystem();
    
    // 添加系统到引擎
    engine.addSystem(testSystem);
    
    // 初始化引擎
    engine.initialize();
    
    // 手动调用更新方法
    engine['update'](16.67); // 模拟60fps
    
    // 验证系统的更新方法被调用
    expect(testSystem.updated).toBe(true);
    expect(testSystem.lateUpdated).toBe(true);
  });
  
  // 测试固定更新
  it('应该调用系统的固定更新方法', () => {
    // 创建测试系统
    const testSystem = new TestSystem();
    
    // 添加系统到引擎
    engine.addSystem(testSystem);
    
    // 初始化引擎
    engine.initialize();
    
    // 设置累积器大于固定更新时间步长
    engine['fixedUpdateAccumulator'] = engine['fixedUpdateTimeStep'] + 0.01;
    
    // 手动调用更新方法
    engine['update'](16.67); // 模拟60fps
    
    // 验证系统的固定更新方法被调用
    expect(testSystem.fixedUpdated).toBe(true);
  });
  
  // 测试引擎销毁
  it('应该正确销毁引擎', () => {
    // 创建测试系统
    const testSystem = new TestSystem();
    
    // 添加系统到引擎
    engine.addSystem(testSystem);
    
    // 初始化并启动引擎
    engine.initialize();
    engine.start();
    
    // 销毁引擎
    engine.dispose();
    
    // 验证引擎已停止运行
    expect(engine.isRunning()).toBe(false);
    
    // 验证系统已销毁
    expect(testSystem.disposed).toBe(true);
    
    // 验证系统列表已清空
    expect(engine['systems'].length).toBe(0);
  });
  
  // 测试事件发射
  it('应该能够发射和监听事件', () => {
    // 创建事件监听器
    const listener = vi.fn();
    
    // 添加事件监听器
    engine.on('test', listener);
    
    // 发射事件
    engine.emit('test', { data: 'test' });
    
    // 验证事件监听器被调用
    expect(listener).toHaveBeenCalledWith({ data: 'test' });
  });
});
