/**
 * 模型加载器
 * 用于加载3D模型
 */
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
// 移除 BufferGeometryUtils 导入，使用内置方法
import { Debug } from '../utils/Debug';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 模型加载器事件类型
 */
export enum ModelLoaderEventType {
  LOAD_START = 'load_start',
  LOAD_PROGRESS = 'load_progress',
  LOAD_COMPLETE = 'load_complete',
  LOAD_ERROR = 'load_error'
}

/**
 * 模型加载器选项接口
 */
export interface ModelLoaderOptions {
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 是否启用纹理压缩 */
  enableTextureCompression?: boolean;
  /** 是否启用几何体优化 */
  enableGeometryOptimization?: boolean;
  /** 是否启用材质优化 */
  enableMaterialOptimization?: boolean;
  /** 是否启用LOD生成 */
  enableLODGeneration?: boolean;
  /** 是否启用调试 */
  enableDebug?: boolean;
}

/**
 * 模型加载器类
 */
export class ModelLoader {
  /** 是否启用缓存 */
  private enableCache: boolean;

  /** 是否启用纹理压缩 */
  private enableTextureCompression: boolean;

  /** 是否启用几何体优化 */
  private enableGeometryOptimization: boolean;

  /** 是否启用材质优化 */
  private enableMaterialOptimization: boolean;

  /** 是否启用LOD生成 */
  private enableLODGeneration: boolean;

  /** 是否启用调试 */
  private enableDebug: boolean;

  /** GLTF加载器 */
  private gltfLoader: GLTFLoader;

  /** OBJ加载器 */
  private objLoader: OBJLoader;

  /** FBX加载器 */
  private fbxLoader: FBXLoader;

  /** 模型缓存 */
  private modelCache: Map<string, THREE.Object3D>;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 加载管理器 */
  private loadingManager: THREE.LoadingManager;

  /**
   * 创建模型加载器
   * @param options 选项
   */
  constructor(options: ModelLoaderOptions = {}) {
    // 初始化选项
    this.enableCache = options.enableCache !== undefined ? options.enableCache : true;
    this.enableTextureCompression = options.enableTextureCompression !== undefined ? options.enableTextureCompression : false;
    this.enableGeometryOptimization = options.enableGeometryOptimization !== undefined ? options.enableGeometryOptimization : false;
    this.enableMaterialOptimization = options.enableMaterialOptimization !== undefined ? options.enableMaterialOptimization : false;
    this.enableLODGeneration = options.enableLODGeneration !== undefined ? options.enableLODGeneration : false;
    this.enableDebug = options.enableDebug !== undefined ? options.enableDebug : false;

    // 初始化缓存
    this.modelCache = new Map();

    // 初始化事件发射器
    this.eventEmitter = new EventEmitter();

    // 创建加载管理器
    this.loadingManager = new THREE.LoadingManager();
    this.loadingManager.onStart = (url, itemsLoaded, itemsTotal) => {
      if (this.enableDebug) {
        Debug.log('ModelLoader', `开始加载: ${url}`);
      }
      this.eventEmitter.emit(ModelLoaderEventType.LOAD_START, url, itemsLoaded, itemsTotal);
    };
    this.loadingManager.onProgress = (url, itemsLoaded, itemsTotal) => {
      if (this.enableDebug) {
        Debug.log('ModelLoader', `加载进度: ${url} (${itemsLoaded}/${itemsTotal})`);
      }
      this.eventEmitter.emit(ModelLoaderEventType.LOAD_PROGRESS, url, itemsLoaded, itemsTotal);
    };
    this.loadingManager.onLoad = () => {
      if (this.enableDebug) {
        Debug.log('ModelLoader', '加载完成');
      }
      this.eventEmitter.emit(ModelLoaderEventType.LOAD_COMPLETE);
    };
    this.loadingManager.onError = (url) => {
      Debug.error('ModelLoader', `加载错误: ${url}`);
      this.eventEmitter.emit(ModelLoaderEventType.LOAD_ERROR, url);
    };

    // 创建加载器
    this.gltfLoader = new GLTFLoader(this.loadingManager);
    this.objLoader = new OBJLoader(this.loadingManager);
    this.fbxLoader = new FBXLoader(this.loadingManager);
  }

  /**
   * 加载模型
   * @param url 模型URL
   * @returns 模型
   */
  public async load(url: string): Promise<THREE.Object3D | null> {
    // 检查缓存
    if (this.enableCache && this.modelCache.has(url)) {
      return this.modelCache.get(url)!.clone();
    }

    try {
      // 根据文件扩展名选择加载器
      const extension = url.split('.').pop()?.toLowerCase();
      let model: THREE.Object3D | null = null;

      switch (extension) {
        case 'gltf':
        case 'glb':
          model = await this.loadGLTF(url);
          break;
        case 'obj':
          model = await this.loadOBJ(url);
          break;
        case 'fbx':
          model = await this.loadFBX(url);
          break;
        default:
          Debug.warn('ModelLoader', `不支持的文件格式: ${extension}`);
          return null;
      }

      if (model) {
        // 优化模型
        this.optimizeModel(model);

        // 添加到缓存
        if (this.enableCache) {
          this.modelCache.set(url, model.clone());
        }

        return model;
      }
    } catch (error) {
      Debug.error('ModelLoader', `加载模型失败: ${url}`, error);
    }

    return null;
  }

  /**
   * 加载GLTF模型
   * @param url 模型URL
   * @returns 模型
   */
  private loadGLTF(url: string): Promise<THREE.Object3D> {
    return new Promise((resolve, reject) => {
      this.gltfLoader.load(
        url,
        (gltf) => {
          resolve(gltf.scene);
        },
        undefined,
        (error) => {
          reject(error);
        }
      );
    });
  }

  /**
   * 加载OBJ模型
   * @param url 模型URL
   * @returns 模型
   */
  private loadOBJ(url: string): Promise<THREE.Object3D> {
    return new Promise((resolve, reject) => {
      this.objLoader.load(
        url,
        (obj) => {
          resolve(obj);
        },
        undefined,
        (error) => {
          reject(error);
        }
      );
    });
  }

  /**
   * 加载FBX模型
   * @param url 模型URL
   * @returns 模型
   */
  private loadFBX(url: string): Promise<THREE.Object3D> {
    return new Promise((resolve, reject) => {
      this.fbxLoader.load(
        url,
        (fbx) => {
          resolve(fbx);
        },
        undefined,
        (error) => {
          reject(error);
        }
      );
    });
  }

  /**
   * 优化模型
   * @param model 模型
   */
  private optimizeModel(model: THREE.Object3D): void {
    // 遍历模型
    model.traverse((object) => {
      // 优化几何体
      if (this.enableGeometryOptimization && object instanceof THREE.Mesh && object.geometry) {
        this.optimizeGeometry(object.geometry);
      }

      // 优化材质
      if (this.enableMaterialOptimization && object instanceof THREE.Mesh && object.material) {
        this.optimizeMaterial(object.material);
      }
    });

    // 生成LOD
    if (this.enableLODGeneration) {
      this.generateLOD(model);
    }
  }

  /**
   * 优化几何体
   * @param geometry 几何体
   */
  private optimizeGeometry(geometry: THREE.BufferGeometry): void {
    // 注意：mergeVertices 在较新版本的 Three.js 中可能不可用
    // 这里我们跳过顶点合并，直接进行其他优化

    // 计算法线
    if (!geometry.attributes.normal) {
      geometry.computeVertexNormals();
    }

    // 计算切线
    if (!geometry.attributes.tangent && geometry.attributes.uv) {
      geometry.computeTangents();
    }

    // 计算包围盒
    geometry.computeBoundingBox();

    // 计算包围球
    geometry.computeBoundingSphere();

    // 如果需要顶点合并功能，可以在这里实现自定义的合并逻辑
    // 或者使用其他几何体优化方法
  }

  /**
   * 优化材质
   * @param material 材质
   */
  private optimizeMaterial(material: THREE.Material | THREE.Material[]): void {
    if (Array.isArray(material)) {
      material.forEach(mat => this.optimizeSingleMaterial(mat));
    } else {
      this.optimizeSingleMaterial(material);
    }
  }

  /**
   * 优化单个材质
   * @param material 材质
   */
  private optimizeSingleMaterial(material: THREE.Material): void {
    // 启用副面剔除
    material.side = THREE.FrontSide;

    // 禁用阴影
    material.shadowSide = THREE.FrontSide;

    // 优化纹理
    if (this.enableTextureCompression) {
      if (material instanceof THREE.MeshStandardMaterial) {
        this.optimizeTexture(material.map);
        this.optimizeTexture(material.normalMap);
        this.optimizeTexture(material.roughnessMap);
        this.optimizeTexture(material.metalnessMap);
        this.optimizeTexture(material.aoMap);
        this.optimizeTexture(material.emissiveMap);
      } else if (material instanceof THREE.MeshPhongMaterial) {
        this.optimizeTexture(material.map);
        this.optimizeTexture(material.normalMap);
        this.optimizeTexture(material.specularMap);
        this.optimizeTexture(material.emissiveMap);
      }
    }
  }

  /**
   * 优化纹理
   * @param texture 纹理
   */
  private optimizeTexture(texture: THREE.Texture | null): void {
    if (!texture) {
      return;
    }

    // 设置纹理过滤
    texture.minFilter = THREE.LinearMipmapLinearFilter;
    texture.magFilter = THREE.LinearFilter;

    // 设置纹理包装
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;

    // 启用各向异性过滤
    texture.anisotropy = 4;
  }

  /**
   * 生成LOD
   * @param _model 模型
   */
  private generateLOD(_model: THREE.Object3D): void {
    // 这里需要实现LOD生成逻辑
    // 可以使用 SimplifyModifier 或其他几何体简化算法
    // 目前暂未实现，预留功能
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.modelCache.clear();
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public on(type: ModelLoaderEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public off(type: ModelLoaderEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(type, listener);
  }
}
