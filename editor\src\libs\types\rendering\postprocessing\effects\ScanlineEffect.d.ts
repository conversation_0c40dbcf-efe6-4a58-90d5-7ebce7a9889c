/**
 * 扫描线效果
 * 模拟CRT显示器的扫描线
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 扫描线效果选项
 */
export interface ScanlineEffectOptions extends PostProcessingEffectOptions {
    /** 线条数量 */
    count?: number;
    /** 线条强度 */
    intensity?: number;
    /** 是否使用动画 */
    animated?: boolean;
    /** 动画速度 */
    speed?: number;
    /** 线条颜色 */
    color?: THREE.Color;
    /** 是否使用噪点 */
    noise?: boolean;
    /** 噪点强度 */
    noiseIntensity?: number;
}
/**
 * 扫描线效果
 */
export declare class ScanlineEffect extends PostProcessingEffect {
    /** 线条数量 */
    private count;
    /** 线条强度 */
    private intensity;
    /** 是否使用动画 */
    private animated;
    /** 动画速度 */
    private speed;
    /** 线条颜色 */
    private color;
    /** 是否使用噪点 */
    private noise;
    /** 噪点强度 */
    private noiseIntensity;
    /** 扫描线通道 */
    private scanlinePass;
    /** 时间 */
    private time;
    /**
     * 创建扫描线效果
     * @param options 扫描线效果选项
     */
    constructor(options?: ScanlineEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 更新效果
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 设置线条数量
     * @param count 线条数量
     */
    setCount(count: number): void;
    /**
     * 获取线条数量
     * @returns 线条数量
     */
    getCount(): number;
    /**
     * 设置线条强度
     * @param intensity 线条强度
     */
    setIntensity(intensity: number): void;
    /**
     * 获取线条强度
     * @returns 线条强度
     */
    getIntensity(): number;
    /**
     * 设置是否使用动画
     * @param animated 是否使用动画
     */
    setAnimated(animated: boolean): void;
    /**
     * 获取是否使用动画
     * @returns 是否使用动画
     */
    isAnimated(): boolean;
    /**
     * 设置动画速度
     * @param speed 动画速度
     */
    setSpeed(speed: number): void;
    /**
     * 获取动画速度
     * @returns 动画速度
     */
    getSpeed(): number;
    /**
     * 设置线条颜色
     * @param color 线条颜色
     */
    setColor(color: THREE.Color): void;
    /**
     * 获取线条颜色
     * @returns 线条颜色
     */
    getColor(): THREE.Color;
    /**
     * 设置是否使用噪点
     * @param noise 是否使用噪点
     */
    setNoise(noise: boolean): void;
    /**
     * 获取是否使用噪点
     * @returns 是否使用噪点
     */
    isNoise(): boolean;
    /**
     * 设置噪点强度
     * @param intensity 噪点强度
     */
    setNoiseIntensity(intensity: number): void;
    /**
     * 获取噪点强度
     * @returns 噪点强度
     */
    getNoiseIntensity(): number;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
}
