import { IAIAnimationModel, AnimationGenerationRequest, AnimationGenerationResult, EmotionAnalysisResult } from './IAIAnimationModel';
/**
 * 本地AI动画模型配置
 */
export interface LocalAIAnimationModelConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 模型路径 */
    modelPath?: string;
    /** 词汇表路径 */
    vocabPath?: string;
    /** 批处理大小 */
    batchSize?: number;
}
/**
 * 本地AI动画模型
 */
export declare class LocalAIAnimationModel implements IAIAnimationModel {
    /** 配置 */
    private config;
    /** 是否已初始化 */
    private initialized;
    /** 是否正在初始化 */
    private initializing;
    /** 情感词典 */
    private emotionDictionary;
    /** 请求映射 */
    private requests;
    /** 情感动画生成器 */
    private emotionGenerator;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: LocalAIAnimationModelConfig);
    /**
     * 初始化情感词典
     */
    private initEmotionDictionary;
    /**
     * 初始化模型
     * @returns 是否成功初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 生成身体动画
     * @param request 生成请求
     * @returns 生成结果
     */
    generateBodyAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    /**
     * 生成面部动画
     * @param request 生成请求
     * @returns 生成结果
     */
    generateFacialAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    /**
     * 生成组合动画
     * @param request 生成请求
     * @returns 生成结果
     */
    generateCombinedAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    /**
     * 分析文本情感
     * @param text 文本
     * @returns 情感分析结果
     */
    analyzeEmotion(text: string): Promise<EmotionAnalysisResult>;
    /**
     * 取消请求
     * @param id 请求ID
     * @returns 是否成功取消
     */
    cancelRequest(id: string): boolean;
    /**
     * 释放资源
     */
    dispose(): void;
}
