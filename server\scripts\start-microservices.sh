#!/bin/bash

# 微服务架构启动脚本
# 用于启动完整的文本语音场景生成系统微服务架构

set -e

echo "🚀 启动文本语音场景生成系统微服务架构..."

# 检查Docker和Docker Compose是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p uploads/{images,audio,models,other}
mkdir -p monitoring

# 复制环境配置文件
if [ ! -f .env ]; then
    echo "📋 复制环境配置文件..."
    cp .env.microservices.example .env
    echo "⚠️  请编辑 .env 文件以配置您的环境变量"
fi

# 创建Prometheus配置文件
if [ ! -f monitoring/prometheus.yml ]; then
    echo "📊 创建Prometheus配置文件..."
    cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8000']
    metrics_path: '/metrics'

  - job_name: 'service-registry'
    static_configs:
      - targets: ['service-registry:8010']
    metrics_path: '/metrics'

  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:8001']
    metrics_path: '/metrics'

  - job_name: 'ai-model-service'
    static_configs:
      - targets: ['ai-model-service:8002']
    metrics_path: '/metrics'

  - job_name: 'asset-library-service'
    static_configs:
      - targets: ['asset-library-service:8003']
    metrics_path: '/metrics'

  - job_name: 'scene-template-service'
    static_configs:
      - targets: ['scene-template-service:8004']
    metrics_path: '/metrics'

  - job_name: 'scene-generation-service'
    static_configs:
      - targets: ['scene-generation-service:8005']
    metrics_path: '/metrics'

  - job_name: 'project-service'
    static_configs:
      - targets: ['project-service:8006']
    metrics_path: '/metrics'

  - job_name: 'render-service'
    static_configs:
      - targets: ['render-service:8007']
    metrics_path: '/metrics'

  - job_name: 'knowledge-service'
    static_configs:
      - targets: ['knowledge-service:8008']
    metrics_path: '/metrics'

  - job_name: 'rag-engine'
    static_configs:
      - targets: ['rag-engine:8009']
    metrics_path: '/metrics'
EOF
fi

# 构建所有服务
echo "🔨 构建微服务镜像..."
docker-compose -f docker-compose.microservices.yml build

# 启动基础设施服务
echo "🏗️  启动基础设施服务..."
docker-compose -f docker-compose.microservices.yml up -d postgres redis minio elasticsearch

# 等待基础设施服务启动
echo "⏳ 等待基础设施服务启动..."
sleep 30

# 启动监控服务
echo "📊 启动监控服务..."
docker-compose -f docker-compose.microservices.yml up -d prometheus grafana jaeger

# 启动服务注册中心
echo "🗂️  启动服务注册中心..."
docker-compose -f docker-compose.microservices.yml up -d service-registry

# 等待服务注册中心启动
echo "⏳ 等待服务注册中心启动..."
sleep 15

# 启动核心微服务
echo "🚀 启动核心微服务..."
docker-compose -f docker-compose.microservices.yml up -d \
  user-service \
  ai-model-service \
  asset-library-service \
  scene-template-service \
  project-service \
  render-service \
  knowledge-service \
  rag-engine

# 等待核心服务启动
echo "⏳ 等待核心服务启动..."
sleep 30

# 启动场景生成服务（依赖其他服务）
echo "🎨 启动场景生成服务..."
docker-compose -f docker-compose.microservices.yml up -d scene-generation-service

# 等待场景生成服务启动
echo "⏳ 等待场景生成服务启动..."
sleep 15

# 启动API网关
echo "🌐 启动API网关..."
docker-compose -f docker-compose.microservices.yml up -d api-gateway

# 等待API网关启动
echo "⏳ 等待API网关启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose.microservices.yml ps

# 显示服务访问信息
echo ""
echo "✅ 微服务架构启动完成！"
echo ""
echo "🌐 服务访问地址："
echo "  API网关:           http://localhost:8000"
echo "  API文档:           http://localhost:8000/api/docs"
echo "  服务注册中心:       http://localhost:8010"
echo "  用户服务:          http://localhost:8001"
echo "  AI模型服务:        http://localhost:8002"
echo "  资源库服务:        http://localhost:8003"
echo "  场景模板服务:      http://localhost:8004"
echo "  场景生成服务:      http://localhost:8005"
echo "  项目服务:          http://localhost:8006"
echo "  渲染服务:          http://localhost:8007"
echo "  知识库服务:        http://localhost:8008"
echo "  RAG引擎:           http://localhost:8009"
echo ""
echo "📊 监控服务："
echo "  Prometheus:        http://localhost:9090"
echo "  Grafana:           http://localhost:3000 (admin/admin)"
echo "  Jaeger:            http://localhost:16686"
echo ""
echo "🗄️  基础设施："
echo "  PostgreSQL:        localhost:5432"
echo "  Redis:             localhost:6379"
echo "  MinIO:             http://localhost:9001 (minioadmin/minioadmin)"
echo "  Elasticsearch:     http://localhost:9200"
echo ""
echo "📝 查看日志："
echo "  docker-compose -f docker-compose.microservices.yml logs -f [service-name]"
echo ""
echo "🛑 停止服务："
echo "  ./scripts/stop-microservices.sh"
echo ""

# 健康检查
echo "🏥 执行健康检查..."
sleep 5

services=(
  "http://localhost:8000/health"
  "http://localhost:8010/health"
  "http://localhost:8001/health"
  "http://localhost:8002/health"
  "http://localhost:8003/health"
  "http://localhost:8004/health"
  "http://localhost:8005/health"
  "http://localhost:8006/health"
  "http://localhost:8007/health"
  "http://localhost:8008/health"
  "http://localhost:8009/health"
)

for service in "${services[@]}"; do
  if curl -s "$service" > /dev/null; then
    echo "✅ $service - 健康"
  else
    echo "❌ $service - 不健康"
  fi
done

echo ""
echo "🎉 微服务架构启动完成！开始使用文本语音场景生成系统吧！"
