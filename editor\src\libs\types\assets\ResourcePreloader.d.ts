/**
 * 资源预加载器
 * 用于预加载资源，提高游戏性能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetManager } from './AssetManager';
import { AssetType } from './ResourceManager';
/**
 * 预加载资源信息
 */
export interface PreloadResourceInfo {
    /** 资源ID */
    id: string;
    /** 资源类型 */
    type: AssetType;
    /** 资源URL */
    url: string;
    /** 资源优先级（0-100，数值越大优先级越高） */
    priority?: number;
    /** 资源组 */
    group?: string;
    /** 资源元数据 */
    metadata?: Record<string, any>;
}
/**
 * 预加载组信息
 */
export interface PreloadGroupInfo {
    /** 组名 */
    name: string;
    /** 组优先级（0-100，数值越大优先级越高） */
    priority: number;
    /** 组依赖 */
    dependencies?: string[];
    /** 组资源 */
    resources: PreloadResourceInfo[];
}
/**
 * 预加载进度信息
 */
export interface PreloadProgressInfo {
    /** 组名 */
    group: string;
    /** 已加载资源数 */
    loaded: number;
    /** 总资源数 */
    total: number;
    /** 加载进度（0-1） */
    progress: number;
    /** 已加载资源 */
    loadedResources: string[];
    /** 失败资源 */
    failedResources: string[];
}
/**
 * 资源预加载器选项
 */
export interface ResourcePreloaderOptions {
    /** 资产管理器 */
    assetManager: AssetManager;
    /** 是否自动注册资源 */
    autoRegisterAssets?: boolean;
    /** 是否自动加载依赖组 */
    autoLoadDependencies?: boolean;
    /** 最大并发加载数 */
    maxConcurrentLoads?: number;
    /** 重试次数 */
    retryCount?: number;
    /** 重试延迟（毫秒） */
    retryDelay?: number;
}
/**
 * 资源预加载器
 */
export declare class ResourcePreloader extends EventEmitter {
    /** 资产管理器 */
    private assetManager;
    /** 是否自动注册资源 */
    private autoRegisterAssets;
    /** 是否自动加载依赖组 */
    private autoLoadDependencies;
    /** 最大并发加载数 */
    private maxConcurrentLoads;
    /** 重试次数 */
    private retryCount;
    /** 重试延迟（毫秒） */
    private retryDelay;
    /** 预加载组映射 */
    private groups;
    /** 预加载进度映射 */
    private progress;
    /** 当前加载组 */
    private currentGroup;
    /** 加载队列 */
    private loadQueue;
    /** 当前并发加载数 */
    private currentConcurrentLoads;
    /** 是否正在加载 */
    private loading;
    /** 是否已暂停 */
    private paused;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建资源预加载器实例
     * @param options 资源预加载器选项
     */
    constructor(options: ResourcePreloaderOptions);
    /**
     * 初始化资源预加载器
     */
    initialize(): void;
    /**
     * 添加预加载组
     * @param group 预加载组信息
     * @returns 是否成功添加
     */
    addGroup(group: PreloadGroupInfo): boolean;
    /**
     * 移除预加载组
     * @param name 组名
     * @returns 是否成功移除
     */
    removeGroup(name: string): boolean;
    /**
     * 注册组中的资源
     * @param name 组名
     * @returns 是否成功注册
     */
    private registerGroupAssets;
    /**
     * 加载预加载组
     * @param name 组名
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    loadGroup(name: string, onProgress?: (progress: PreloadProgressInfo) => void): Promise<PreloadProgressInfo>;
    /**
     * 重置进度信息
     * @param name 组名
     */
    private resetProgress;
    /**
     * 准备加载队列
     * @param name 组名
     */
    private prepareLoadQueue;
    /**
     * 开始加载
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    private startLoading;
    /**
     * 加载资源
     * @param resource 资源信息
     * @param retryCount 当前重试次数
     * @returns Promise
     */
    private loadResource;
    /**
     * 更新进度信息
     * @param group 组名
     * @param resourceId 资源ID
     * @param success 是否成功
     */
    private updateProgress;
    /**
     * 暂停加载
     */
    pause(): void;
    /**
     * 恢复加载
     */
    resume(): void;
    /**
     * 获取预加载组
     * @param name 组名
     * @returns 预加载组信息
     */
    getGroup(name: string): PreloadGroupInfo | null;
    /**
     * 获取所有预加载组
     * @returns 预加载组信息数组
     */
    getAllGroups(): PreloadGroupInfo[];
    /**
     * 获取预加载进度
     * @param name 组名
     * @returns 预加载进度信息
     */
    getProgress(name: string): PreloadProgressInfo | null;
    /**
     * 获取当前加载组
     * @returns 当前加载组名
     */
    getCurrentGroup(): string | null;
    /**
     * 是否正在加载
     * @returns 是否正在加载
     */
    isLoading(): boolean;
    /**
     * 是否已暂停
     * @returns 是否已暂停
     */
    isPaused(): boolean;
    /**
     * 清空所有预加载组
     */
    clear(): void;
    /**
     * 销毁资源预加载器
     */
    dispose(): void;
}
