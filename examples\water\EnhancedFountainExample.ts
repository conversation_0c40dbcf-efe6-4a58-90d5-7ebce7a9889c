/**
 * 增强版喷泉示例
 * 展示如何使用增强版喷泉组件创建各种喷泉效果
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { Camera } from '../../engine/src/core/Camera';
import { OrbitControls } from '../../engine/src/controls/OrbitControls';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { AudioSystem } from '../../engine/src/audio/AudioSystem';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../engine/src/physics/water/WaterInteractionSystem';
import { UnderwaterParticleSystem } from '../../engine/src/rendering/water/UnderwaterParticleSystem';
import { WaterInstancedRenderer } from '../../engine/src/rendering/water/WaterInstancedRenderer';
import { FountainType } from '../../engine/src/physics/water/FountainComponent';
import { EnhancedFountainComponent, EnhancedFountainType, FountainJetShape } from '../../engine/src/physics/water/EnhancedFountainComponent';
import { EnhancedFountainPresets } from '../../engine/src/physics/water/EnhancedFountainPresets';
import { Debug } from '../../engine/src/utils/Debug';
import { PerformanceMonitor } from '../../engine/src/utils/PerformanceMonitor';

/**
 * 增强版喷泉示例
 */
export class EnhancedFountainExample {
  /** 世界 */
  private world: World;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水体交互系统 */
  private waterInteractionSystem: WaterInteractionSystem;
  /** 水下粒子系统 */
  private underwaterParticleSystem: UnderwaterParticleSystem;
  /** 水体实例化渲染器 */
  private waterInstancedRenderer: WaterInstancedRenderer;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 音频系统 */
  private audioSystem: AudioSystem;
  /** 喷泉实体 */
  private fountainEntity: Entity;
  /** 喷泉组件 */
  private fountainComponent: EnhancedFountainComponent;
  /** 当前喷泉类型 */
  private currentFountainType: EnhancedFountainType = EnhancedFountainType.STANDARD;
  /** 喷泉类型列表 */
  private fountainTypes: EnhancedFountainType[] = [
    EnhancedFountainType.STANDARD,
    EnhancedFountainType.HIGH,
    EnhancedFountainType.WIDE,
    EnhancedFountainType.MULTI_JET,
    EnhancedFountainType.DANCING,
    EnhancedFountainType.MUSICAL,
    EnhancedFountainType.PULSE,
    EnhancedFountainType.ALTERNATING,
    EnhancedFountainType.SEQUENCE,
    EnhancedFountainType.RANDOM,
    EnhancedFountainType.SPIRAL,
    EnhancedFountainType.CONE,
    EnhancedFountainType.FLOWER,
    EnhancedFountainType.RAINBOW,
    EnhancedFountainType.INTERACTIVE
  ];
  /** 当前喷泉类型索引 */
  private currentFountainTypeIndex: number = 0;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();
  /** 水池实体 */
  private poolEntity: Entity;
  /** 地面实体 */
  private groundEntity: Entity;
  /** 相机 */
  private camera: Camera;
  /** 轨道控制器 */
  private controls: OrbitControls;
  /** 是否显示UI */
  private showUI: boolean = true;
  /** UI元素 */
  private uiElements: HTMLElement[] = [];

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 初始化系统
    this.initializeSystems();

    // 创建场景
    this.createScene();

    // 创建喷泉
    this.createFountain();

    // 创建UI
    this.createUI();

    // 开始渲染循环
    this.startRenderLoop();

    Debug.log('EnhancedFountainExample', '增强版喷泉示例初始化完成');
  }

  /**
   * 初始化系统
   */
  private initializeSystems(): void {
    // 创建相机
    this.camera = new Camera();
    this.camera.position.set(0, 5, 10);
    this.camera.lookAt(0, 0, 0);
    this.world.setCamera(this.camera);

    // 创建轨道控制器
    this.controls = new OrbitControls(this.camera.getThreeCamera(), this.world.getRenderer().domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.screenSpacePanning = false;
    this.controls.minDistance = 3;
    this.controls.maxDistance = 20;
    this.controls.maxPolarAngle = Math.PI / 2 - 0.1;

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world);
    this.world.addSystem(this.inputSystem);

    // 创建音频系统
    this.audioSystem = new AudioSystem(this.world);
    this.world.addSystem(this.audioSystem);

    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水体交互系统
    this.waterInteractionSystem = new WaterInteractionSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true
    });
    this.world.addSystem(this.waterInteractionSystem);

    // 创建水下粒子系统
    this.underwaterParticleSystem = new UnderwaterParticleSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1
    });
    this.world.addSystem(this.underwaterParticleSystem);

    // 创建水体实例化渲染器
    this.waterInstancedRenderer = new WaterInstancedRenderer(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      maxInstances: 10000,
      useGPUCulling: true,
      useInstancedShadows: true
    });
    this.world.addSystem(this.waterInstancedRenderer);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 1);
    this.world.getScene().add(ambientLight);

    // 创建方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -10;
    directionalLight.shadow.camera.right = 10;
    directionalLight.shadow.camera.top = 10;
    directionalLight.shadow.camera.bottom = -10;
    this.world.getScene().add(directionalLight);

    // 创建地面
    this.groundEntity = new Entity();
    this.groundEntity.name = 'Ground';
    const groundGeometry = new THREE.PlaneGeometry(50, 50);
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0x555555,
      roughness: 0.8,
      metalness: 0.2,
      side: THREE.DoubleSide
    });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.rotation.x = -Math.PI / 2;
    groundMesh.receiveShadow = true;
    this.groundEntity.addObject(groundMesh);
    this.world.addEntity(this.groundEntity);

    // 创建水池
    this.createPool();
  }

  /**
   * 创建水池
   */
  private createPool(): void {
    // 创建水池实体
    this.poolEntity = new Entity();
    this.poolEntity.name = 'Pool';

    // 创建水池底部
    const poolBottomGeometry = new THREE.CircleGeometry(3, 32);
    const poolBottomMaterial = new THREE.MeshStandardMaterial({
      color: 0x6688aa,
      roughness: 0.2,
      metalness: 0.1
    });
    const poolBottomMesh = new THREE.Mesh(poolBottomGeometry, poolBottomMaterial);
    poolBottomMesh.rotation.x = -Math.PI / 2;
    poolBottomMesh.position.y = -0.5;
    poolBottomMesh.receiveShadow = true;
    this.poolEntity.addObject(poolBottomMesh);

    // 创建水池边缘
    const poolEdgeGeometry = new THREE.TorusGeometry(3, 0.2, 16, 100);
    const poolEdgeMaterial = new THREE.MeshStandardMaterial({
      color: 0x888888,
      roughness: 0.5,
      metalness: 0.3
    });
    const poolEdgeMesh = new THREE.Mesh(poolEdgeGeometry, poolEdgeMaterial);
    poolEdgeMesh.rotation.x = Math.PI / 2;
    poolEdgeMesh.position.y = -0.4;
    poolEdgeMesh.castShadow = true;
    poolEdgeMesh.receiveShadow = true;
    this.poolEntity.addObject(poolEdgeMesh);

    // 创建水池壁
    const poolWallGeometry = new THREE.CylinderGeometry(3, 3, 0.5, 32, 1, true);
    const poolWallMaterial = new THREE.MeshStandardMaterial({
      color: 0x6688aa,
      roughness: 0.2,
      metalness: 0.1,
      side: THREE.BackSide
    });
    const poolWallMesh = new THREE.Mesh(poolWallGeometry, poolWallMaterial);
    poolWallMesh.position.y = -0.25;
    poolWallMesh.receiveShadow = true;
    this.poolEntity.addObject(poolWallMesh);

    // 创建水面
    const waterGeometry = new THREE.CircleGeometry(3, 32);
    const waterMaterial = new THREE.MeshStandardMaterial({
      color: 0x88ccff,
      transparent: true,
      opacity: 0.8,
      roughness: 0.0,
      metalness: 0.1
    });
    const waterMesh = new THREE.Mesh(waterGeometry, waterMaterial);
    waterMesh.rotation.x = -Math.PI / 2;
    waterMesh.position.y = 0.05;
    this.poolEntity.addObject(waterMesh);

    // 添加到世界
    this.world.addEntity(this.poolEntity);
  }

  /**
   * 创建喷泉
   */
  private createFountain(): void {
    // 使用预设创建喷泉
    this.fountainEntity = EnhancedFountainPresets.createPreset(this.world, {
      type: this.currentFountainType,
      position: new THREE.Vector3(0, 0, 0),
      size: { width: 1, height: 5, depth: 1 }
    });

    // 获取喷泉组件
    this.fountainComponent = this.fountainEntity.getComponent(EnhancedFountainComponent) as EnhancedFountainComponent;
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    uiContainer.style.color = 'white';
    uiContainer.style.fontFamily = 'Arial, sans-serif';
    uiContainer.style.fontSize = '14px';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.zIndex = '1000';
    document.body.appendChild(uiContainer);
    this.uiElements.push(uiContainer);

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '增强版喷泉示例';
    title.style.margin = '0 0 10px 0';
    title.style.fontSize = '18px';
    uiContainer.appendChild(title);

    // 创建喷泉类型信息
    const fountainTypeInfo = document.createElement('div');
    fountainTypeInfo.id = 'fountain-type-info';
    fountainTypeInfo.textContent = `当前喷泉类型: ${this.currentFountainType}`;
    fountainTypeInfo.style.marginBottom = '10px';
    uiContainer.appendChild(fountainTypeInfo);

    // 创建性能信息
    const performanceInfo = document.createElement('div');
    performanceInfo.id = 'performance-info';
    performanceInfo.textContent = '性能信息加载中...';
    performanceInfo.style.marginBottom = '10px';
    uiContainer.appendChild(performanceInfo);

    // 创建控制说明
    const controls = document.createElement('div');
    controls.innerHTML = `
      <p>控制说明:</p>
      <ul style="padding-left: 20px; margin: 5px 0;">
        <li>空格键: 切换喷泉类型</li>
        <li>H: 显示/隐藏UI</li>
      </ul>
    `;
    uiContainer.appendChild(controls);

    // 注册键盘事件
    window.addEventListener('keydown', this.handleKeyDown.bind(this));
  }

  /**
   * 处理键盘事件
   * @param event 键盘事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case ' ': // 空格键
        this.changeFountainType();
        break;
      case 'h': // H键
      case 'H':
        this.toggleUI();
        break;
    }
  }

  /**
   * 切换喷泉类型
   */
  private changeFountainType(): void {
    // 更新索引
    this.currentFountainTypeIndex = (this.currentFountainTypeIndex + 1) % this.fountainTypes.length;
    this.currentFountainType = this.fountainTypes[this.currentFountainTypeIndex];

    // 移除旧的喷泉实体
    this.world.removeEntity(this.fountainEntity);

    // 创建新的喷泉实体
    this.fountainEntity = EnhancedFountainPresets.createPreset(this.world, {
      type: this.currentFountainType,
      position: new THREE.Vector3(0, 0, 0),
      size: { width: 1, height: 5, depth: 1 }
    });

    // 获取喷泉组件
    this.fountainComponent = this.fountainEntity.getComponent(EnhancedFountainComponent) as EnhancedFountainComponent;

    // 更新UI
    const fountainTypeInfo = document.getElementById('fountain-type-info');
    if (fountainTypeInfo) {
      fountainTypeInfo.textContent = `当前喷泉类型: ${this.currentFountainType}`;
    }
  }

  /**
   * 切换UI显示
   */
  private toggleUI(): void {
    this.showUI = !this.showUI;
    for (const element of this.uiElements) {
      element.style.display = this.showUI ? 'block' : 'none';
    }
  }

  /**
   * 更新性能信息
   */
  private updatePerformanceInfo(): void {
    const performanceInfo = document.getElementById('performance-info');
    if (performanceInfo) {
      const fps = Math.round(1000 / this.performanceMonitor.getAverageTime('frame'));
      const particleCount = this.fountainComponent ? this.fountainComponent['currentParticleCount'] : 0;
      const updateTime = this.performanceMonitor.getAverageTime('enhancedFountainUpdate').toFixed(2);
      
      performanceInfo.innerHTML = `
        FPS: ${fps}<br>
        粒子数量: ${particleCount}<br>
        更新时间: ${updateTime}ms
      `;
    }
  }

  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    const animate = () => {
      requestAnimationFrame(animate);

      // 开始帧计时
      this.performanceMonitor.start('frame');

      // 更新控制器
      this.controls.update();

      // 更新世界
      this.world.update();

      // 更新性能信息
      this.updatePerformanceInfo();

      // 结束帧计时
      this.performanceMonitor.end('frame');
    };

    animate();
  }
}
