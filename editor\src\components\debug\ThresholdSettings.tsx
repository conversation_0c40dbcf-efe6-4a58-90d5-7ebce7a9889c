/**
 * 优化阈值设置组件
 * 用于配置场景优化的阈值参数
 */
import React, { useState, useEffect } from 'react';
import { InputNumber, Button, Card, Tabs, Space, Select, Switch, Collapse, Alert, Typography, Row, Col, Tooltip, Form, message } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  ImportOutlined,
  ExportOutlined} from '@ant-design/icons';
// SceneOptimizer导入已移除
import './ThresholdSettings.less';

const { Paragraph } = Typography;
const { Panel } = Collapse;
const { Option } = Select;

// 阈值配置接口
interface ThresholdConfig {
  low: number;
  medium: number;
  high: number;
}

// 场景优化器配置接口
interface SceneOptimizerConfig {
  enableAutoLOD?: boolean;
  enableAutoInstancing?: boolean;
  enableAutoBatching?: boolean;
  enableAutoMaterialOptimization?: boolean;
  enableAutoTextureOptimization?: boolean;
  enableAutoGeometryOptimization?: boolean;
  enableAutoLightOptimization?: boolean;
  enableAutoShadowOptimization?: boolean;
  enableAutoMemoryOptimization?: boolean;
  enableOcclusionCulling?: boolean;
  enableDetailTexture?: boolean;
  enableMeshSimplification?: boolean;
  enableParticleOptimization?: boolean;
  enableAnimationOptimization?: boolean;
  thresholds?: {
    triangles?: ThresholdConfig;
    drawCalls?: ThresholdConfig;
    memory?: ThresholdConfig;
    particles?: ThresholdConfig;
    animations?: ThresholdConfig;
    textureResolution?: ThresholdConfig;
  };
}

// 模拟场景优化器类
class SceneOptimizer {
  private static instance: SceneOptimizer;
  public config: SceneOptimizerConfig = {};

  static getInstance(): SceneOptimizer {
    if (!SceneOptimizer.instance) {
      SceneOptimizer.instance = new SceneOptimizer();
    }
    return SceneOptimizer.instance;
  }

  configure(config: SceneOptimizerConfig): void {
    this.config = { ...this.config, ...config };
    console.log('场景优化器配置已更新:', this.config);
  }

  getConfig(): SceneOptimizerConfig {
    return this.config;
  }
}

interface ThresholdSettingsProps {
  className?: string;
}

/**
 * 优化阈值设置组件
 */
const ThresholdSettings: React.FC<ThresholdSettingsProps> = ({ className }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 状态
  const [config, setConfig] = useState<SceneOptimizerConfig>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [saveSuccess, setSaveSuccess] = useState<boolean>(false);
  const [selectedPreset, setSelectedPreset] = useState<string>('custom');

  // 预设配置
  const presets = {
    low: {
      name: t('debug.threshold.presets.low'),
      description: t('debug.threshold.presets.lowDesc'),
      config: {
        thresholds: {
          triangles: { low: 50000, medium: 200000, high: 500000 },
          drawCalls: { low: 50, medium: 200, high: 500 },
          memory: { low: 50, medium: 150, high: 300 },
          particles: { low: 500, medium: 2000, high: 5000 },
          animations: { low: 5, medium: 15, high: 30 },
          textureResolution: { low: 512, medium: 1024, high: 2048 }
        }
      }
    },
    medium: {
      name: t('debug.threshold.presets.medium'),
      description: t('debug.threshold.presets.mediumDesc'),
      config: {
        thresholds: {
          triangles: { low: 100000, medium: 500000, high: 1000000 },
          drawCalls: { low: 100, medium: 500, high: 1000 },
          memory: { low: 100, medium: 250, high: 500 },
          particles: { low: 1000, medium: 5000, high: 10000 },
          animations: { low: 10, medium: 30, high: 50 },
          textureResolution: { low: 1024, medium: 2048, high: 4096 }
        }
      }
    },
    high: {
      name: t('debug.threshold.presets.high'),
      description: t('debug.threshold.presets.highDesc'),
      config: {
        thresholds: {
          triangles: { low: 200000, medium: 1000000, high: 2000000 },
          drawCalls: { low: 200, medium: 1000, high: 2000 },
          memory: { low: 200, medium: 500, high: 1000 },
          particles: { low: 2000, medium: 10000, high: 20000 },
          animations: { low: 20, medium: 50, high: 100 },
          textureResolution: { low: 2048, medium: 4096, high: 8192 }
        }
      }
    },
    mobile: {
      name: t('debug.threshold.presets.mobile'),
      description: t('debug.threshold.presets.mobileDesc'),
      config: {
        thresholds: {
          triangles: { low: 30000, medium: 100000, high: 300000 },
          drawCalls: { low: 30, medium: 100, high: 300 },
          memory: { low: 30, medium: 100, high: 200 },
          particles: { low: 300, medium: 1000, high: 3000 },
          animations: { low: 3, medium: 10, high: 20 },
          textureResolution: { low: 256, medium: 512, high: 1024 }
        }
      }
    },
    custom: {
      name: t('debug.threshold.presets.custom'),
      description: t('debug.threshold.presets.customDesc'),
      config: {}
    }
  };

  // 初始化
  useEffect(() => {
    loadConfig();
  }, []);

  // 预设配置
  const presetConfigs = {
    performance: {
      name: t('debug.threshold.presets.performance'),
      config: {
        enableAutoLOD: true,
        enableAutoInstancing: true,
        enableAutoBatching: true,
        enableAutoMaterialOptimization: true,
        enableAutoTextureOptimization: true,
        enableAutoGeometryOptimization: true,
        enableAutoLightOptimization: true,
        enableAutoShadowOptimization: true,
        enableAutoMemoryOptimization: true,
        enableOcclusionCulling: true,
        enableDetailTexture: false,
        enableMeshSimplification: true,
        enableParticleOptimization: true,
        enableAnimationOptimization: true,
        thresholds: {
          triangles: { low: 30000, medium: 60000, high: 120000 },
          drawCalls: { low: 30, medium: 60, high: 120 },
          memory: { low: 80, medium: 150, high: 300 },
          particles: { low: 500, medium: 2000, high: 5000 },
          animations: { low: 5, medium: 10, high: 20 },
          textureResolution: { low: 512, medium: 1024, high: 2048 }
        }
      }
    },
    quality: {
      name: t('debug.threshold.presets.quality'),
      config: {
        enableAutoLOD: false,
        enableAutoInstancing: true,
        enableAutoBatching: true,
        enableAutoMaterialOptimization: false,
        enableAutoTextureOptimization: false,
        enableAutoGeometryOptimization: false,
        enableAutoLightOptimization: false,
        enableAutoShadowOptimization: false,
        enableAutoMemoryOptimization: false,
        enableOcclusionCulling: false,
        enableDetailTexture: true,
        enableMeshSimplification: false,
        enableParticleOptimization: false,
        enableAnimationOptimization: false,
        thresholds: {
          triangles: { low: 100000, medium: 200000, high: 500000 },
          drawCalls: { low: 100, medium: 200, high: 500 },
          memory: { low: 200, medium: 500, high: 1000 },
          particles: { low: 2000, medium: 10000, high: 20000 },
          animations: { low: 20, medium: 50, high: 100 },
          textureResolution: { low: 2048, medium: 4096, high: 8192 }
        }
      }
    },
    balanced: {
      name: t('debug.threshold.presets.balanced'),
      config: {
        enableAutoLOD: true,
        enableAutoInstancing: true,
        enableAutoBatching: true,
        enableAutoMaterialOptimization: true,
        enableAutoTextureOptimization: true,
        enableAutoGeometryOptimization: false,
        enableAutoLightOptimization: true,
        enableAutoShadowOptimization: true,
        enableAutoMemoryOptimization: true,
        enableOcclusionCulling: true,
        enableDetailTexture: true,
        enableMeshSimplification: false,
        enableParticleOptimization: true,
        enableAnimationOptimization: true,
        thresholds: {
          triangles: { low: 50000, medium: 100000, high: 200000 },
          drawCalls: { low: 50, medium: 100, high: 200 },
          memory: { low: 100, medium: 200, high: 500 },
          particles: { low: 1000, medium: 5000, high: 10000 },
          animations: { low: 10, medium: 20, high: 50 },
          textureResolution: { low: 1024, medium: 2048, high: 4096 }
        }
      }
    }
  };

  // 应用预设配置
  const applyPreset = (presetKey: string) => {
    if (presetKey === 'custom') {
      setSelectedPreset('custom');
      return;
    }

    const preset = presetConfigs[presetKey as keyof typeof presetConfigs];
    if (preset) {
      setConfig(preset.config);
      form.setFieldsValue(preset.config);
      setSelectedPreset(presetKey);
      message.success(t('debug.threshold.presetApplied', { name: preset.name }));
    }
  };

  // 加载配置
  const loadConfig = () => {
    setLoading(true);

    try {
      // 获取场景优化器实例
      const optimizer = SceneOptimizer.getInstance();

      // 获取当前配置，提供默认值
      const defaultThresholds = {
        triangles: { low: 100000, medium: 500000, high: 1000000 },
        drawCalls: { low: 100, medium: 500, high: 1000 },
        memory: { low: 100, medium: 250, high: 500 },
        particles: { low: 1000, medium: 5000, high: 10000 },
        animations: { low: 10, medium: 30, high: 50 },
        textureResolution: { low: 1024, medium: 2048, high: 4096 }
      };

      const currentConfig: SceneOptimizerConfig = {
        enableAutoLOD: optimizer.config.enableAutoLOD ?? true,
        enableAutoInstancing: optimizer.config.enableAutoInstancing ?? true,
        enableAutoBatching: optimizer.config.enableAutoBatching ?? true,
        enableAutoMaterialOptimization: optimizer.config.enableAutoMaterialOptimization ?? true,
        enableAutoTextureOptimization: optimizer.config.enableAutoTextureOptimization ?? true,
        enableAutoGeometryOptimization: optimizer.config.enableAutoGeometryOptimization ?? true,
        enableAutoLightOptimization: optimizer.config.enableAutoLightOptimization ?? true,
        enableAutoShadowOptimization: optimizer.config.enableAutoShadowOptimization ?? true,
        enableAutoMemoryOptimization: optimizer.config.enableAutoMemoryOptimization ?? true,
        enableOcclusionCulling: optimizer.config.enableOcclusionCulling ?? true,
        enableDetailTexture: optimizer.config.enableDetailTexture ?? true,
        enableMeshSimplification: optimizer.config.enableMeshSimplification ?? true,
        enableParticleOptimization: optimizer.config.enableParticleOptimization ?? true,
        enableAnimationOptimization: optimizer.config.enableAnimationOptimization ?? true,
        thresholds: {
          triangles: optimizer.config.thresholds?.triangles ?? defaultThresholds.triangles,
          drawCalls: optimizer.config.thresholds?.drawCalls ?? defaultThresholds.drawCalls,
          memory: optimizer.config.thresholds?.memory ?? defaultThresholds.memory,
          particles: optimizer.config.thresholds?.particles ?? defaultThresholds.particles,
          animations: optimizer.config.thresholds?.animations ?? defaultThresholds.animations,
          textureResolution: optimizer.config.thresholds?.textureResolution ?? defaultThresholds.textureResolution
        }
      };

      setConfig(currentConfig);
      form.setFieldsValue(currentConfig);
      setSelectedPreset('custom');
    } catch (error) {
      console.error('加载配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存配置
  const saveConfig = (values: any) => {
    setLoading(true);
    setSaveSuccess(false);

    try {
      // 获取场景优化器实例
      const optimizer = SceneOptimizer.getInstance();

      // 更新配置
      optimizer.configure(values);

      // 保存到本地存储
      localStorage.setItem('optimizerConfig', JSON.stringify(values));

      // 更新状态
      setConfig(values);
      setSaveSuccess(true);

      // 3秒后隐藏成功提示
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('保存配置失败:', error);
    } finally {
      setLoading(false);
    }
  };



  // 导出配置
  const exportConfig = () => {
    const configJson = JSON.stringify(config, null, 2);
    const blob = new Blob([configJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'optimizer-config.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 导入配置
  const importConfig = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          try {
            const importedConfig = JSON.parse(event.target?.result as string);
            setConfig(importedConfig);
            form.setFieldsValue(importedConfig);
            setSelectedPreset('custom');
          } catch (error) {
            console.error('导入配置失败:', error);
          }
        };
        reader.readAsText(file);
      }
    };

    input.click();
  };

  // 渲染预设选择器
  const renderPresetSelector = () => {
    return (
      <Card title={t('debug.threshold.presets.title')} className="preset-card">
        <Paragraph>{t('debug.threshold.presets.description')}</Paragraph>

        <Space direction="vertical" style={{ width: '100%' }}>
          <Select
            value={selectedPreset}
            onChange={applyPreset}
            style={{ width: '100%' }}
          >
            <Option value="low">{presets.low.name}</Option>
            <Option value="medium">{presets.medium.name}</Option>
            <Option value="high">{presets.high.name}</Option>
            <Option value="mobile">{presets.mobile.name}</Option>
            <Option value="custom">{presets.custom.name}</Option>
          </Select>

          <Alert
            message={(presets as any)[selectedPreset].description}
            type="info"
            showIcon
          />

          <Space>
            <Button icon={<ImportOutlined />} onClick={importConfig}>
              {t('debug.threshold.import')}
            </Button>
            <Button icon={<ExportOutlined />} onClick={exportConfig}>
              {t('debug.threshold.export')}
            </Button>
          </Space>
        </Space>
      </Card>
    );
  };

  return (
    <div className={`threshold-settings ${className || ''}`}>
      <div className="threshold-toolbar">
        <Space wrap>
          <Space>
            <span>{t('debug.threshold.preset')}:</span>
            <Select
              value={selectedPreset}
              onChange={applyPreset}
              style={{ width: 150 }}
            >
              <Option value="custom">{t('debug.threshold.presets.custom')}</Option>
              <Option value="performance">{t('debug.threshold.presets.performance')}</Option>
              <Option value="quality">{t('debug.threshold.presets.quality')}</Option>
              <Option value="balanced">{t('debug.threshold.presets.balanced')}</Option>
            </Select>
          </Space>

          <Button
            icon={<ReloadOutlined />}
            onClick={loadConfig}
            loading={loading}
          >
            {t('debug.threshold.reset')}
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => form.submit()}
            loading={loading}
          >
            {t('debug.threshold.save')}
          </Button>

          {saveSuccess && (
            <Alert
              message={t('debug.threshold.saveSuccess')}
              type="success"
              showIcon
              closable
            />
          )}
        </Space>
      </div>

      <div className="threshold-content">
        <Row gutter={[16, 16]}>
          <Col span={24} lg={8}>
            {renderPresetSelector()}
          </Col>

          <Col span={24} lg={16}>
            <Form
              form={form}
              layout="vertical"
              onFinish={saveConfig}
              initialValues={config}
            >
              <Tabs
                defaultActiveKey="general"
                type="card"
                items={[
                  {
                    key: 'general',
                    label: (
                      <span>
                        <SettingOutlined />
                        {t('debug.threshold.tabs.general')}
                      </span>
                    ),
                    children: (
                      <Card title={t('debug.threshold.optimizationToggles')}>
                        <Row gutter={[16, 8]}>
                          <Col span={12}>
                            <Form.Item
                              name="enableAutoLOD"
                              label={
                                <Space>
                                  {t('debug.threshold.enableAutoLOD')}
                                  <Tooltip title={t('debug.threshold.enableAutoLODDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableAutoInstancing"
                              label={
                                <Space>
                                  {t('debug.threshold.enableAutoInstancing')}
                                  <Tooltip title={t('debug.threshold.enableAutoInstancingDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableAutoBatching"
                              label={
                                <Space>
                                  {t('debug.threshold.enableAutoBatching')}
                                  <Tooltip title={t('debug.threshold.enableAutoBatchingDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableAutoMaterialOptimization"
                              label={
                                <Space>
                                  {t('debug.threshold.enableAutoMaterialOptimization')}
                                  <Tooltip title={t('debug.threshold.enableAutoMaterialOptimizationDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableAutoTextureOptimization"
                              label={
                                <Space>
                                  {t('debug.threshold.enableAutoTextureOptimization')}
                                  <Tooltip title={t('debug.threshold.enableAutoTextureOptimizationDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableAutoGeometryOptimization"
                              label={
                                <Space>
                                  {t('debug.threshold.enableAutoGeometryOptimization')}
                                  <Tooltip title={t('debug.threshold.enableAutoGeometryOptimizationDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableAutoLightOptimization"
                              label={
                                <Space>
                                  {t('debug.threshold.enableAutoLightOptimization')}
                                  <Tooltip title={t('debug.threshold.enableAutoLightOptimizationDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableAutoShadowOptimization"
                              label={
                                <Space>
                                  {t('debug.threshold.enableAutoShadowOptimization')}
                                  <Tooltip title={t('debug.threshold.enableAutoShadowOptimizationDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableAutoMemoryOptimization"
                              label={
                                <Space>
                                  {t('debug.threshold.enableAutoMemoryOptimization')}
                                  <Tooltip title={t('debug.threshold.enableAutoMemoryOptimizationDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableOcclusionCulling"
                              label={
                                <Space>
                                  {t('debug.threshold.enableOcclusionCulling')}
                                  <Tooltip title={t('debug.threshold.enableOcclusionCullingDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableDetailTexture"
                              label={
                                <Space>
                                  {t('debug.threshold.enableDetailTexture')}
                                  <Tooltip title={t('debug.threshold.enableDetailTextureDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableMeshSimplification"
                              label={
                                <Space>
                                  {t('debug.threshold.enableMeshSimplification')}
                                  <Tooltip title={t('debug.threshold.enableMeshSimplificationDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableParticleOptimization"
                              label={
                                <Space>
                                  {t('debug.threshold.enableParticleOptimization')}
                                  <Tooltip title={t('debug.threshold.enableParticleOptimizationDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              name="enableAnimationOptimization"
                              label={
                                <Space>
                                  {t('debug.threshold.enableAnimationOptimization')}
                                  <Tooltip title={t('debug.threshold.enableAnimationOptimizationDesc')}>
                                    <QuestionCircleOutlined />
                                  </Tooltip>
                                </Space>
                              }
                              valuePropName="checked"
                            >
                              <Switch />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    )
                  },
                  {
                    key: 'thresholds',
                    label: (
                      <span>
                        <WarningOutlined />
                        {t('debug.threshold.tabs.thresholds')}
                      </span>
                    ),
                    children: (
                      <Collapse defaultActiveKey={['triangles']} accordion>
                        <Panel
                          header={
                            <Space>
                              <span>{t('debug.threshold.triangles')}</span>
                              <InfoCircleOutlined />
                            </Space>
                          }
                          key="triangles"
                        >
                          <Row gutter={[16, 8]}>
                            <Col span={8}>
                              <Form.Item
                                name={['thresholds', 'triangles', 'low']}
                                label={t('debug.threshold.lowThreshold')}
                              >
                                <InputNumber min={0} step={10000} style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                            <Col span={8}>
                              <Form.Item
                                name={['thresholds', 'triangles', 'medium']}
                                label={t('debug.threshold.mediumThreshold')}
                              >
                                <InputNumber min={0} step={10000} style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                            <Col span={8}>
                              <Form.Item
                                name={['thresholds', 'triangles', 'high']}
                                label={t('debug.threshold.highThreshold')}
                              >
                                <InputNumber min={0} step={10000} style={{ width: '100%' }} />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Panel>

                    <Panel
                      header={
                        <Space>
                          <span>{t('debug.threshold.drawCalls')}</span>
                          <InfoCircleOutlined />
                        </Space>
                      }
                      key="drawCalls"
                    >
                      <Row gutter={[16, 8]}>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'drawCalls', 'low']}
                            label={t('debug.threshold.lowThreshold')}
                          >
                            <InputNumber min={0} step={10} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'drawCalls', 'medium']}
                            label={t('debug.threshold.mediumThreshold')}
                          >
                            <InputNumber min={0} step={10} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'drawCalls', 'high']}
                            label={t('debug.threshold.highThreshold')}
                          >
                            <InputNumber min={0} step={10} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Panel>

                    <Panel
                      header={
                        <Space>
                          <span>{t('debug.threshold.memory')}</span>
                          <InfoCircleOutlined />
                        </Space>
                      }
                      key="memory"
                    >
                      <Row gutter={[16, 8]}>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'memory', 'low']}
                            label={t('debug.threshold.lowThreshold')}
                          >
                            <InputNumber min={0} step={10} style={{ width: '100%' }} addonAfter="MB" />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'memory', 'medium']}
                            label={t('debug.threshold.mediumThreshold')}
                          >
                            <InputNumber min={0} step={10} style={{ width: '100%' }} addonAfter="MB" />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'memory', 'high']}
                            label={t('debug.threshold.highThreshold')}
                          >
                            <InputNumber min={0} step={10} style={{ width: '100%' }} addonAfter="MB" />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Panel>

                    <Panel
                      header={
                        <Space>
                          <span>{t('debug.threshold.particles')}</span>
                          <InfoCircleOutlined />
                        </Space>
                      }
                      key="particles"
                    >
                      <Row gutter={[16, 8]}>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'particles', 'low']}
                            label={t('debug.threshold.lowThreshold')}
                          >
                            <InputNumber min={0} step={100} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'particles', 'medium']}
                            label={t('debug.threshold.mediumThreshold')}
                          >
                            <InputNumber min={0} step={100} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'particles', 'high']}
                            label={t('debug.threshold.highThreshold')}
                          >
                            <InputNumber min={0} step={100} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Panel>

                    <Panel
                      header={
                        <Space>
                          <span>{t('debug.threshold.animations')}</span>
                          <InfoCircleOutlined />
                        </Space>
                      }
                      key="animations"
                    >
                      <Row gutter={[16, 8]}>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'animations', 'low']}
                            label={t('debug.threshold.lowThreshold')}
                          >
                            <InputNumber min={0} step={1} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'animations', 'medium']}
                            label={t('debug.threshold.mediumThreshold')}
                          >
                            <InputNumber min={0} step={1} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'animations', 'high']}
                            label={t('debug.threshold.highThreshold')}
                          >
                            <InputNumber min={0} step={1} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Panel>

                    <Panel
                      header={
                        <Space>
                          <span>{t('debug.threshold.textureResolution')}</span>
                          <InfoCircleOutlined />
                        </Space>
                      }
                      key="textureResolution"
                    >
                      <Row gutter={[16, 8]}>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'textureResolution', 'low']}
                            label={t('debug.threshold.lowThreshold')}
                          >
                            <InputNumber min={0} step={128} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'textureResolution', 'medium']}
                            label={t('debug.threshold.mediumThreshold')}
                          >
                            <InputNumber min={0} step={128} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item
                            name={['thresholds', 'textureResolution', 'high']}
                            label={t('debug.threshold.highThreshold')}
                          >
                            <InputNumber min={0} step={128} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                          </Row>
                        </Panel>
                      </Collapse>
                    )
                  }
                ]}
              />
            </Form>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default ThresholdSettings;
