/**
 * 动作回放器
 * 用于回放录制的角色动作序列
 */
import type { Entity } from '../../core/Entity';
import { ActionControlSystem } from '../controllers/ActionControlSystem';
import { AdvancedCharacterController } from '../controllers/AdvancedCharacterController';
import { ActionRecording } from './ActionRecorder';
/**
 * 动作回放配置
 */
export interface ActionPlaybackConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 回放速度 (1.0 = 正常速度) */
    playbackSpeed?: number;
    /** 是否回放输入 */
    playbackInput?: boolean;
    /** 是否回放变换 */
    playbackTransform?: boolean;
    /** 是否循环回放 */
    loop?: boolean;
    /** 是否自动开始回放 */
    autoPlay?: boolean;
}
/**
 * 动作回放器
 */
export declare class ActionPlayback {
    /** 实体 */
    private entity;
    /** 动作控制系统 */
    private actionControlSystem;
    /** 角色控制器 */
    private characterController;
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否正在回放 */
    private isPlaying;
    /** 是否暂停 */
    private isPaused;
    /** 当前录制 */
    private recording;
    /** 回放开始时间戳 */
    private playbackStartTimestamp;
    /** 回放暂停时间戳 */
    private playbackPauseTimestamp;
    /** 回放暂停总时长 */
    private totalPauseDuration;
    /** 动作事件索引 */
    private actionEventIndex;
    /** 输入事件索引 */
    private inputEventIndex;
    /** 变换事件索引 */
    private transformEventIndex;
    /** 回放完成回调 */
    private onCompleteCallback;
    /** 动画帧请求ID */
    private animationFrameId;
    /**
     * 构造函数
     * @param entity 实体
     * @param actionControlSystem 动作控制系统
     * @param config 配置
     * @param characterController 角色控制器（可选）
     */
    constructor(entity: Entity, actionControlSystem: ActionControlSystem, config?: ActionPlaybackConfig, characterController?: AdvancedCharacterController);
    /**
     * 设置角色控制器
     * @param characterController 角色控制器
     */
    setCharacterController(characterController: AdvancedCharacterController | null): void;
    /**
     * 获取角色控制器
     * @returns 角色控制器
     */
    getCharacterController(): AdvancedCharacterController | null;
    /**
     * 加载录制
     * @param recording 录制数据
     * @returns 是否成功加载
     */
    loadRecording(recording: ActionRecording): boolean;
    /**
     * 开始回放
     * @param onComplete 完成回调
     * @returns 是否成功开始回放
     */
    startPlayback(onComplete?: () => void): boolean;
    /**
     * 暂停回放
     * @returns 是否成功暂停
     */
    pausePlayback(): boolean;
    /**
     * 停止回放
     * @returns 是否成功停止
     */
    stopPlayback(): boolean;
    /**
     * 设置回放速度
     * @param speed 速度 (1.0 = 正常速度)
     */
    setPlaybackSpeed(speed: number): void;
    /**
     * 重置回放状态
     */
    private resetPlaybackState;
    /**
     * 开始回放循环
     */
    private startPlaybackLoop;
    /**
     * 获取当前回放时间
     * @returns 当前回放时间（毫秒）
     */
    private getCurrentPlaybackTime;
    /**
     * 处理动作事件
     * @param currentTime 当前回放时间
     */
    private processActionEvents;
    /**
     * 播放动作事件
     * @param event 动作事件
     */
    private playActionEvent;
    /**
     * 处理输入事件
     * @param currentTime 当前回放时间
     */
    private processInputEvents;
    /**
     * 播放输入事件
     * @param event 输入事件
     */
    private playInputEvent;
    /**
     * 处理变换事件
     * @param currentTime 当前回放时间
     */
    private processTransformEvents;
    /**
     * 应用变换
     * @param event 变换事件
     */
    private applyTransform;
    /**
     * 应用插值变换
     * @param event1 变换事件1
     * @param event2 变换事件2
     * @param t 插值因子 (0-1)
     */
    private applyInterpolatedTransform;
    /**
     * 重置回放索引
     */
    private resetPlaybackIndices;
    /**
     * 完成回放
     */
    private completePlayback;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    on(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    off(event: string, callback: (...args: any[]) => void): void;
}
