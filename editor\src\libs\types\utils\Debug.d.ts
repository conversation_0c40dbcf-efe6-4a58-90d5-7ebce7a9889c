/**
 * 调试工具类
 * 提供调试相关的功能
 */
export declare enum LogLevel {
    NONE = 0,
    ERROR = 1,
    WARN = 2,
    INFO = 3,
    DEBUG = 4,
    ALL = 5
}
export declare class Debug {
    /** 日志级别 */
    private static logLevel;
    /** 是否启用调试模式 */
    private static debugMode;
    /** 日志回调函数 */
    private static logCallback;
    /**
     * 设置日志级别
     * @param level 日志级别
     */
    static setLogLevel(level: LogLevel): void;
    /**
     * 获取日志级别
     * @returns 日志级别
     */
    static getLogLevel(): LogLevel;
    /**
     * 设置调试模式
     * @param enabled 是否启用
     */
    static setDebugMode(enabled: boolean): void;
    /**
     * 是否处于调试模式
     * @returns 是否处于调试模式
     */
    static isDebugMode(): boolean;
    /**
     * 设置日志回调函数
     * @param callback 回调函数
     */
    static setLogCallback(callback: (level: LogLevel, message: string, ...args: any[]) => void): void;
    /**
     * 清除日志回调函数
     */
    static clearLogCallback(): void;
    /**
     * 记录调试日志
     * @param module 模块名或日志消息
     * @param message 日志消息（可选）
     * @param args 附加参数
     */
    static log(module: string, message?: string, ...args: any[]): void;
    /**
     * 记录信息日志
     * @param module 模块名或日志消息
     * @param message 日志消息（可选）
     * @param args 附加参数
     */
    static info(module: string, message?: string, ...args: any[]): void;
    /**
     * 记录警告日志
     * @param module 模块名或日志消息
     * @param message 日志消息（可选）
     * @param args 附加参数
     */
    static warn(module: string, message?: string, ...args: any[]): void;
    /**
     * 记录错误日志
     * @param module 模块名或日志消息
     * @param message 日志消息（可选）
     * @param args 附加参数
     */
    static error(module: string, message?: string, ...args: any[]): void;
    /**
     * 断言
     * @param condition 条件
     * @param message 断言失败时的消息
     * @throws 如果条件为假，则抛出错误
     */
    static assert(condition: boolean, message: string): void;
    /**
     * 开始计时
     * @param label 计时标签
     */
    static time(label: string): void;
    /**
     * 结束计时并输出结果
     * @param label 计时标签
     */
    static timeEnd(label: string): void;
    /**
     * 输出对象的详细信息
     * @param obj 要检查的对象
     * @param label 标签
     */
    static inspect(obj: any, label?: string): void;
}
