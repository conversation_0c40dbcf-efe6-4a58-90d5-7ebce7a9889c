#!/usr/bin/env pwsh
# 修复 Chroma 健康检查问题的脚本

param(
    [switch]$Help,
    [switch]$Test
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔧 $message"
    Write-Host "=" * 60
}

# 显示帮助信息
function Show-Help {
    Write-Host "Chroma 健康检查修复脚本"
    Write-Host ""
    Write-Host "用法: .\fix-chroma-healthcheck.ps1 [选项]"
    Write-Host ""
    Write-Host "此脚本将："
    Write-Host "  1. 修复 Chroma 容器的健康检查配置"
    Write-Host "  2. 使用更可靠的健康检查方法"
    Write-Host "  3. 可选择测试 Chroma 连接"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -Help           显示此帮助信息"
    Write-Host "  -Test           测试 Chroma 服务连接"
}

# 测试 Chroma 连接
function Test-ChromaConnection {
    Write-Info "测试 Chroma 服务连接..."
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/heartbeat" -Method GET -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Success "Chroma 服务连接正常"
            return $true
        } else {
            Write-Warning "Chroma 服务响应异常: $($response.StatusCode)"
            return $false
        }
    } catch {
        Write-Warning "无法连接到 Chroma 服务: $_"
        Write-Info "这可能是因为服务尚未启动或健康检查配置有问题"
        return $false
    }
}

# 创建改进的 Chroma 健康检查脚本
function Create-ChromaHealthcheckScript {
    $scriptContent = @"
#!/bin/sh
# Chroma 健康检查脚本

# 尝试多种方法检查 Chroma 服务
check_chroma() {
    # 方法1: 使用 curl (如果可用)
    if command -v curl >/dev/null 2>&1; then
        curl -f http://localhost:8000/api/v1/heartbeat >/dev/null 2>&1 && return 0
    fi
    
    # 方法2: 使用 wget (如果可用)
    if command -v wget >/dev/null 2>&1; then
        wget --quiet --tries=1 --spider http://localhost:8000/api/v1/heartbeat >/dev/null 2>&1 && return 0
    fi
    
    # 方法3: 使用 python (通常在 Chroma 镜像中可用)
    if command -v python >/dev/null 2>&1; then
        python -c "
import sys
try:
    import urllib.request
    urllib.request.urlopen('http://localhost:8000/api/v1/heartbeat', timeout=5)
    sys.exit(0)
except:
    sys.exit(1)
" && return 0
    fi
    
    # 方法4: 使用 nc (netcat) 检查端口
    if command -v nc >/dev/null 2>&1; then
        nc -z localhost 8000 >/dev/null 2>&1 && return 0
    fi
    
    return 1
}

# 执行检查
check_chroma
exit $?
"@
    
    $scriptPath = "chroma-healthcheck.sh"
    $scriptContent | Out-File -FilePath $scriptPath -Encoding UTF8
    Write-Success "创建了改进的健康检查脚本: $scriptPath"
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Header "修复 Chroma 健康检查问题"
    
    # 检查是否在正确的目录
    if (-not (Test-Path "server")) {
        Write-Error "server目录不存在，请确保在项目根目录运行此脚本"
        exit 1
    }
    
    # 创建改进的健康检查脚本
    Create-ChromaHealthcheckScript
    
    Write-Info "健康检查配置已在以下文件中更新："
    Write-Info "  - server/docker-compose.windows.yml"
    Write-Info "  - docker-compose.windows.yml"
    
    Write-Success "修复完成！新的健康检查配置使用了更可靠的方法"
    
    if ($Test) {
        Write-Header "测试 Chroma 连接"
        Test-ChromaConnection
    }
    
    Write-Info "建议的下一步操作："
    Write-Info "  1. 重新启动 Chroma 服务: docker-compose restart chroma"
    Write-Info "  2. 检查服务状态: docker-compose ps"
    Write-Info "  3. 查看服务日志: docker-compose logs chroma"
}

# 运行主函数
Main
