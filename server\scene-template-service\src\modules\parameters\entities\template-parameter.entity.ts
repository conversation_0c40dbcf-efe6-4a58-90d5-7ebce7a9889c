import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { SceneTemplate } from '../../templates/entities/scene-template.entity';

export enum ParameterType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  COLOR = 'color',
  VECTOR3 = 'vector3',
  ENUM = 'enum',
  RANGE = 'range',
  ASSET_REFERENCE = 'asset_reference',
  MATERIAL_REFERENCE = 'material_reference',
  TEXTURE_REFERENCE = 'texture_reference',
}

export enum ParameterCategory {
  LIGHTING = 'lighting',
  MATERIALS = 'materials',
  GEOMETRY = 'geometry',
  ENVIRONMENT = 'environment',
  ANIMATION = 'animation',
  PHYSICS = 'physics',
  AUDIO = 'audio',
  INTERACTION = 'interaction',
  CUSTOM = 'custom',
}

@Entity('template_parameters')
@Index(['template', 'name'])
@Index(['type', 'category'])
export class TemplateParameter {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  @Index()
  name: string; // 参数名称

  @Column({ length: 100 })
  key: string; // 参数键名（用于代码中引用）

  @Column({ length: 255, nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ParameterType,
    default: ParameterType.STRING,
  })
  @Index()
  type: ParameterType;

  @Column({
    type: 'enum',
    enum: ParameterCategory,
    default: ParameterCategory.CUSTOM,
  })
  @Index()
  category: ParameterCategory;

  // 参数配置
  @Column({ type: 'jsonb', nullable: true })
  defaultValue: any; // 默认值

  @Column({ type: 'jsonb', nullable: true })
  minValue: any; // 最小值（用于数值类型）

  @Column({ type: 'jsonb', nullable: true })
  maxValue: any; // 最大值（用于数值类型）

  @Column({ type: 'jsonb', nullable: true })
  step: any; // 步长（用于数值类型）

  @Column({ type: 'jsonb', nullable: true })
  options: any[]; // 选项列表（用于枚举类型）

  @Column({ type: 'jsonb', nullable: true })
  validation: Record<string, any>; // 验证规则

  // 显示配置
  @Column({ length: 100, nullable: true })
  label: string; // 显示标签

  @Column({ length: 500, nullable: true })
  tooltip: string; // 提示信息

  @Column({ length: 50, nullable: true })
  unit: string; // 单位（如：px, %, deg等）

  @Column({ length: 100, nullable: true })
  group: string; // 参数分组

  @Column({ name: 'sort_order', default: 0 })
  sortOrder: number; // 排序顺序

  @Column({ name: 'is_required', default: false })
  isRequired: boolean; // 是否必填

  @Column({ name: 'is_advanced', default: false })
  isAdvanced: boolean; // 是否为高级参数

  @Column({ name: 'is_visible', default: true })
  isVisible: boolean; // 是否可见

  @Column({ name: 'is_readonly', default: false })
  isReadonly: boolean; // 是否只读

  // 条件显示
  @Column({ type: 'jsonb', nullable: true })
  showCondition: Record<string, any>; // 显示条件

  @Column({ type: 'jsonb', nullable: true })
  hideCondition: Record<string, any>; // 隐藏条件

  // 关联关系
  @ManyToOne(() => SceneTemplate, template => template.parameters, { onDelete: 'CASCADE' })
  template: SceneTemplate;

  @Column({ name: 'template_id' })
  @Index()
  templateId: string;

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get hasValidation(): boolean {
    return this.validation && Object.keys(this.validation).length > 0;
  }

  get hasOptions(): boolean {
    return this.options && this.options.length > 0;
  }

  get hasRange(): boolean {
    return this.minValue !== null || this.maxValue !== null;
  }

  get isNumeric(): boolean {
    return [ParameterType.NUMBER, ParameterType.RANGE].includes(this.type);
  }

  get isReference(): boolean {
    return [
      ParameterType.ASSET_REFERENCE,
      ParameterType.MATERIAL_REFERENCE,
      ParameterType.TEXTURE_REFERENCE,
    ].includes(this.type);
  }

  get displayLabel(): string {
    return this.label || this.name;
  }

  get fullKey(): string {
    return this.group ? `${this.group}.${this.key}` : this.key;
  }

  // 验证参数值
  validateValue(value: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 必填验证
    if (this.isRequired && (value === null || value === undefined || value === '')) {
      errors.push(`${this.displayLabel}是必填参数`);
    }

    // 类型验证
    if (value !== null && value !== undefined) {
      switch (this.type) {
        case ParameterType.NUMBER:
          if (typeof value !== 'number' || isNaN(value)) {
            errors.push(`${this.displayLabel}必须是数字`);
          } else {
            if (this.minValue !== null && value < this.minValue) {
              errors.push(`${this.displayLabel}不能小于${this.minValue}`);
            }
            if (this.maxValue !== null && value > this.maxValue) {
              errors.push(`${this.displayLabel}不能大于${this.maxValue}`);
            }
          }
          break;

        case ParameterType.STRING:
          if (typeof value !== 'string') {
            errors.push(`${this.displayLabel}必须是字符串`);
          }
          break;

        case ParameterType.BOOLEAN:
          if (typeof value !== 'boolean') {
            errors.push(`${this.displayLabel}必须是布尔值`);
          }
          break;

        case ParameterType.COLOR:
          if (typeof value !== 'string' || !/^#[0-9A-Fa-f]{6}$/.test(value)) {
            errors.push(`${this.displayLabel}必须是有效的颜色值`);
          }
          break;

        case ParameterType.ENUM:
          if (this.hasOptions && !this.options.includes(value)) {
            errors.push(`${this.displayLabel}必须是有效的选项值`);
          }
          break;

        case ParameterType.VECTOR3:
          if (!Array.isArray(value) || value.length !== 3 || !value.every(v => typeof v === 'number')) {
            errors.push(`${this.displayLabel}必须是包含3个数字的数组`);
          }
          break;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
