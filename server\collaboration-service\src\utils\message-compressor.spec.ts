import { MessageCompressor, CompressionAlgorithm } from './message-compressor';

describe('MessageCompressor', () => {
  let compressor: MessageCompressor;

  beforeEach(() => {
    compressor = new MessageCompressor({
      algorithm: CompressionAlgorithm.DEFLATE,
      level: 6,
      minSize: 100,
    });
  });

  describe('compress', () => {
    it('should not compress small messages', async () => {
      const message = { type: 'test', data: 'small message' };
      const result = await compressor.compress(message);
      
      expect(result.algorithm).toBe(CompressionAlgorithm.NONE);
      expect(Buffer.isBuffer(result.data)).toBe(true);
      
      // 解析回原始消息
      const jsonString = result.data.toString('utf-8');
      const decompressedMessage = JSON.parse(jsonString);
      
      expect(decompressedMessage).toEqual(message);
    });

    it('should compress large messages', async () => {
      // 创建一个大消息
      const largeData = Array(1000).fill('test data').join(' ');
      const message = { type: 'test', data: largeData };
      
      const result = await compressor.compress(message);
      
      expect(result.algorithm).toBe(CompressionAlgorithm.DEFLATE);
      expect(Buffer.isBuffer(result.data)).toBe(true);
      
      // 压缩后的大小应该小于原始大小
      const originalSize = Buffer.from(JSON.stringify(message), 'utf-8').length;
      expect(result.data.length).toBeLessThan(originalSize);
    });
  });

  describe('decompress', () => {
    it('should decompress compressed data', async () => {
      // 创建一个大消息
      const largeData = Array(1000).fill('test data').join(' ');
      const message = { type: 'test', data: largeData };
      
      // 压缩消息
      const compressed = await compressor.compress(message);
      
      // 解压缩消息
      const decompressed = await compressor.decompress(compressed.data, compressed.algorithm);
      
      // 解压缩后的消息应该与原始消息相同
      expect(decompressed).toEqual(message);
    });

    it('should handle uncompressed data', async () => {
      const message = { type: 'test', data: 'small message' };
      const jsonBuffer = Buffer.from(JSON.stringify(message), 'utf-8');
      
      // 解压缩未压缩的数据
      const decompressed = await compressor.decompress(jsonBuffer, CompressionAlgorithm.NONE);
      
      // 结果应该与原始消息相同
      expect(decompressed).toEqual(message);
    });

    it('should throw error for invalid compression algorithm', async () => {
      const message = { type: 'test', data: 'test message' };
      const jsonBuffer = Buffer.from(JSON.stringify(message), 'utf-8');
      
      // 使用无效的压缩算法
      await expect(compressor.decompress(jsonBuffer, 'invalid' as CompressionAlgorithm))
        .rejects.toThrow('不支持的压缩算法');
    });
  });
});
