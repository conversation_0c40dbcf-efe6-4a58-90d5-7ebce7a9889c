/**
 * LOD（细节层次）组件
 * 用于为实体添加多个细节级别
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import type { Transform } from '../../scene/Transform';

/**
 * LOD级别枚举
 */
export enum LODLevel {
  /** 高细节 */
  HIGH = 'high',
  /** 中细节 */
  MEDIUM = 'medium',
  /** 低细节 */
  LOW = 'low',
  /** 极低细节 */
  VERY_LOW = 'very_low'
}

/**
 * LOD级别配置接口
 */
export interface LODLevelConfig {
  /** 级别 */
  level: LODLevel;
  /** 距离阈值 */
  distance: number;
  /** 原始距离阈值（用于动态LOD系统） */
  originalDistance?: number;
  /** 网格 */
  mesh: THREE.Mesh | THREE.Group;
  /** 是否可见 */
  visible?: boolean;
}

/**
 * LOD组件选项接口
 */
export interface LODComponentOptions {
  /** 级别配置列表 */
  levels?: LODLevelConfig[];
  /** 包围半径 */
  boundingRadius?: number;
  /** 是否自动计算包围半径 */
  autoComputeBoundingRadius?: boolean;
  /** 是否自动排序级别 */
  autoSortLevels?: boolean;
}

/**
 * LOD组件类
 */
export class LODComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'LODComponent';

  /** 级别配置列表 */
  private levels: LODLevelConfig[] = [];

  /** 当前级别 */
  private currentLevel: LODLevel | null = null;

  /** 包围半径 */
  private boundingRadius: number;

  /** 是否自动计算包围半径 */
  private autoComputeBoundingRadius: boolean;

  /** 是否自动排序级别 */
  private autoSortLevels: boolean;

  /**
   * 创建LOD组件
   * @param options LOD组件选项
   */
  constructor(options: LODComponentOptions = {}) {
    super(LODComponent.type);

    this.levels = options.levels || [];
    this.boundingRadius = options.boundingRadius !== undefined ? options.boundingRadius : 1.0;
    this.autoComputeBoundingRadius = options.autoComputeBoundingRadius !== undefined ? options.autoComputeBoundingRadius : true;
    this.autoSortLevels = options.autoSortLevels !== undefined ? options.autoSortLevels : true;

    // 如果提供了级别配置，则初始化级别
    if (this.levels.length > 0) {
      // 如果启用自动排序，则按距离排序
      if (this.autoSortLevels) {
        this.sortLevels();
      }

      // 设置初始级别为第一个级别
      this.currentLevel = this.levels[0].level;

      // 设置所有级别的可见性
      this.updateLevelsVisibility();
    }
  }

  /**
   * 当组件附加到实体时调用
   */
  protected onAttach(): void {
    if (!this.entity) return;

    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any as any as Transform;
    if (!transform) return;

    // 将所有级别的网格添加到变换的Three.js对象
    for (const level of this.levels) {
      transform.getObject3D().add(level.mesh);
    }

    // 如果启用自动计算包围半径，则计算包围半径
    if (this.autoComputeBoundingRadius) {
      this.computeBoundingRadius();
    }

    // 更新级别可见性
    this.updateLevelsVisibility();
  }

  /**
   * 当组件从实体分离时调用
   */
  protected onDetach(): void {
    if (!this.entity) return;

    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any as any as Transform;
    if (!transform) return;

    // 从变换的Three.js对象中移除所有级别的网格
    for (const level of this.levels) {
      transform.getObject3D().remove(level.mesh);
    }
  }

  /**
   * 添加级别
   * @param level 级别配置
   */
  public addLevel(level: LODLevelConfig): void {
    // 添加级别
    this.levels.push(level);

    // 如果启用自动排序，则按距离排序
    if (this.autoSortLevels) {
      this.sortLevels();
    }

    // 如果实体已附加，则将网格添加到变换的Three.js对象
    if (this.entity) {
      const transform = this.entity.getComponent('Transform') as any as any as any as Transform;
      if (transform) {
        transform.getObject3D().add(level.mesh);
      }
    }

    // 如果没有当前级别，则设置为第一个级别
    if (this.currentLevel === null && this.levels.length > 0) {
      this.currentLevel = this.levels[0].level;
    }

    // 更新级别可见性
    this.updateLevelsVisibility();

    // 如果启用自动计算包围半径，则计算包围半径
    if (this.autoComputeBoundingRadius) {
      this.computeBoundingRadius();
    }
  }

  /**
   * 移除级别
   * @param level 级别
   */
  public removeLevel(level: LODLevel): void {
    // 查找级别索引
    const index = this.levels.findIndex(l => l.level === level);
    if (index === -1) {
      return;
    }

    // 获取级别配置
    const levelConfig = this.levels[index];

    // 如果实体已附加，则从变换的Three.js对象中移除网格
    if (this.entity) {
      const transform = this.entity.getComponent('Transform') as any as any as any as Transform;
      if (transform) {
        transform.getObject3D().remove(levelConfig.mesh);
      }
    }

    // 移除级别
    this.levels.splice(index, 1);

    // 如果当前级别是被移除的级别，则重置当前级别
    if (this.currentLevel === level) {
      this.currentLevel = this.levels.length > 0 ? this.levels[0].level : null;
      this.updateLevelsVisibility();
    }

    // 如果启用自动计算包围半径，则计算包围半径
    if (this.autoComputeBoundingRadius) {
      this.computeBoundingRadius();
    }
  }

  /**
   * 获取级别
   * @param level 级别
   * @returns 级别配置
   */
  public getLevel(level: LODLevel): LODLevelConfig | null {
    return this.levels.find(l => l.level === level) || null;
  }

  /**
   * 获取所有级别
   * @returns 级别配置列表
   */
  public getLevels(): LODLevelConfig[] {
    return this.levels;
  }

  /**
   * 设置当前级别
   * @param level 级别
   */
  public setCurrentLevel(level: LODLevel | null): void {
    // 如果级别没有变化，则不更新
    if (this.currentLevel === level) {
      return;
    }

    // 设置当前级别
    this.currentLevel = level;

    // 更新级别可见性
    this.updateLevelsVisibility();
  }

  /**
   * 获取当前级别
   * @returns 当前级别
   */
  public getCurrentLevel(): LODLevel | null {
    return this.currentLevel;
  }

  /**
   * 设置包围半径
   * @param radius 半径
   */
  public setBoundingRadius(radius: number): void {
    this.boundingRadius = radius;
  }

  /**
   * 获取包围半径
   * @returns 包围半径
   */
  public getBoundingRadius(): number {
    return this.boundingRadius;
  }

  /**
   * 计算包围半径
   */
  public computeBoundingRadius(): void {
    // 如果没有级别，则使用默认半径
    if (this.levels.length === 0) {
      this.boundingRadius = 1.0;
      return;
    }

    // 计算所有级别的包围半径的最大值
    let maxRadius = 0;

    for (const level of this.levels) {
      const mesh = level.mesh;

      // 如果网格没有包围球，则计算包围球
      if (mesh instanceof THREE.Mesh && !mesh.geometry.boundingSphere) {
        mesh.geometry.computeBoundingSphere();
      }

      // 获取包围球半径
      let radius = 0;

      if (mesh instanceof THREE.Mesh && mesh.geometry.boundingSphere) {
        radius = mesh.geometry.boundingSphere.radius;
      } else if (mesh instanceof THREE.Group) {
        // 对于组，遍历所有子网格并计算最大半径
        mesh.traverse(child => {
          if (child instanceof THREE.Mesh && child.geometry.boundingSphere) {
            radius = Math.max(radius, child.geometry.boundingSphere.radius);
          }
        });
      }

      // 更新最大半径
      maxRadius = Math.max(maxRadius, radius);
    }

    // 设置包围半径
    this.boundingRadius = maxRadius > 0 ? maxRadius : 1.0;
  }

  /**
   * 更新级别可见性
   */
  private updateLevelsVisibility(): void {
    // 遍历所有级别
    for (const level of this.levels) {
      // 设置可见性
      level.visible = level.level === this.currentLevel;
      level.mesh.visible = level.visible;
    }
  }

  /**
   * 设置所有级别的可见性
   * @param visible 是否可见
   */
  public setAllLevelsVisible(visible: boolean): void {
    // 遍历所有级别
    for (const level of this.levels) {
      // 设置可见性
      level.visible = visible;
      level.mesh.visible = visible;
    }
  }

  /**
   * 按距离排序级别
   */
  private sortLevels(): void {
    // 按距离升序排序
    this.levels.sort((a, b) => a.distance - b.distance);
  }

  /**
   * 设置是否自动计算包围半径
   * @param auto 是否自动计算
   */
  public setAutoComputeBoundingRadius(auto: boolean): void {
    this.autoComputeBoundingRadius = auto;

    // 如果启用自动计算，则立即计算
    if (auto) {
      this.computeBoundingRadius();
    }
  }

  /**
   * 获取是否自动计算包围半径
   * @returns 是否自动计算
   */
  public isAutoComputeBoundingRadius(): boolean {
    return this.autoComputeBoundingRadius;
  }

  /**
   * 设置是否自动排序级别
   * @param auto 是否自动排序
   */
  public setAutoSortLevels(auto: boolean): void {
    this.autoSortLevels = auto;

    // 如果启用自动排序，则立即排序
    if (auto) {
      this.sortLevels();
    }
  }

  /**
   * 获取是否自动排序级别
   * @returns 是否自动排序
   */
  public isAutoSortLevels(): boolean {
    return this.autoSortLevels;
  }
}
