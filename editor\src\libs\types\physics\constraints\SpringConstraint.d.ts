/**
 * 弹簧约束
 * 在两个物体之间创建一个弹簧
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { PhysicsConstraint } from './PhysicsConstraint';
/**
 * 弹簧约束选项
 */
export interface SpringConstraintOptions {
    /** 源物体上的连接点（局部坐标） */
    localAnchorA?: THREE.Vector3;
    /** 目标物体上的连接点（局部坐标） */
    localAnchorB?: THREE.Vector3;
    /** 弹簧静止长度 */
    restLength?: number;
    /** 弹簧刚度 */
    stiffness?: number;
    /** 阻尼系数 */
    damping?: number;
    /** 世界坐标系中的方向 */
    worldAxis?: THREE.Vector3;
    /** 是否允许连接的物体之间碰撞 */
    collideConnected?: boolean;
}
/**
 * 弹簧约束
 */
export declare class SpringConstraint extends PhysicsConstraint {
    /** 组件类型 */
    static readonly type: string;
    /** 源物体上的连接点（局部坐标） */
    private localAnchorA;
    /** 目标物体上的连接点（局部坐标） */
    private localAnchorB;
    /** 弹簧静止长度 */
    private restLength;
    /** 弹簧刚度 */
    private stiffness;
    /** 阻尼系数 */
    private damping;
    /** 世界坐标系中的方向 */
    private worldAxis;
    /** 最大力 */
    private maxForce;
    /**
     * 创建弹簧约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    constructor(targetEntity?: Entity | null, options?: SpringConstraintOptions);
    /**
     * 创建约束
     */
    protected createConstraint(): void;
    /**
     * 设置源物体上的连接点
     * @param anchor 连接点（局部坐标）
     */
    setLocalAnchorA(anchor: THREE.Vector3): void;
    /**
     * 获取源物体上的连接点
     * @returns 连接点（局部坐标）
     */
    getLocalAnchorA(): THREE.Vector3;
    /**
     * 设置目标物体上的连接点
     * @param anchor 连接点（局部坐标）
     */
    setLocalAnchorB(anchor: THREE.Vector3): void;
    /**
     * 获取目标物体上的连接点
     * @returns 连接点（局部坐标）
     */
    getLocalAnchorB(): THREE.Vector3;
    /**
     * 设置弹簧静止长度
     * @param length 静止长度
     */
    setRestLength(length: number): void;
    /**
     * 获取弹簧静止长度
     * @returns 静止长度
     */
    getRestLength(): number;
    /**
     * 设置弹簧刚度
     * @param stiffness 刚度
     */
    setStiffness(stiffness: number): void;
    /**
     * 获取弹簧刚度
     * @returns 刚度
     */
    getStiffness(): number;
    /**
     * 设置阻尼系数
     * @param damping 阻尼系数
     */
    setDamping(damping: number): void;
    /**
     * 获取阻尼系数
     * @returns 阻尼系数
     */
    getDamping(): number;
    /**
     * 设置世界坐标系中的方向
     * @param axis 方向
     */
    setWorldAxis(axis: THREE.Vector3 | null): void;
    /**
     * 获取世界坐标系中的方向
     * @returns 方向
     */
    getWorldAxis(): THREE.Vector3 | null;
    /**
     * 应用约束
     * 弹簧约束需要在每一帧手动应用
     */
    applyConstraint(): void;
    /**
     * 更新约束
     * 在物理系统的每一帧调用
     */
    update(): void;
}
