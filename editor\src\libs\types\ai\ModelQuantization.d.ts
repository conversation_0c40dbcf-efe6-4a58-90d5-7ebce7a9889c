/**
 * 模型量化工具
 * 用于减小模型大小并提高推理速度
 */
/**
 * 量化位数
 */
export type QuantizationBits = 8 | 16 | 32;
/**
 * 量化配置
 */
export interface QuantizationConfig {
    /** 是否启用量化 */
    enabled: boolean;
    /** 量化位数 */
    bits: QuantizationBits;
    /** 是否使用对称量化 */
    symmetric?: boolean;
    /** 是否使用通道量化 */
    perChannel?: boolean;
    /** 是否使用动态量化 */
    dynamic?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 量化统计
 */
export interface QuantizationStats {
    /** 原始模型大小（字节） */
    originalSize: number;
    /** 量化后模型大小（字节） */
    quantizedSize: number;
    /** 压缩比 */
    compressionRatio: number;
    /** 量化时间（毫秒） */
    quantizationTime: number;
    /** 精度损失 */
    precisionLoss?: number;
}
/**
 * 模型量化工具
 */
export declare class ModelQuantization {
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 量化统计 */
    private stats;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<QuantizationConfig>);
    /**
     * 量化模型
     * @param model 模型数据
     * @returns 量化后的模型数据
     */
    quantize(model: ArrayBuffer): ArrayBuffer;
    /**
     * 8位量化
     * @param model 模型数据
     * @returns 量化后的模型数据
     */
    private quantize8Bit;
    /**
     * 16位量化
     * @param model 模型数据
     * @returns 量化后的模型数据
     */
    private quantize16Bit;
    /**
     * 反量化模型
     * @param quantizedModel 量化后的模型数据
     * @returns 反量化后的模型数据
     */
    dequantize(quantizedModel: ArrayBuffer): ArrayBuffer;
    /**
     * 8位反量化
     * @param quantizedModel 量化后的模型数据
     * @param scale 缩放因子
     * @param zeroPoint 零点
     * @returns 反量化后的模型数据
     */
    private dequantize8Bit;
    /**
     * 16位反量化
     * @param quantizedModel 量化后的模型数据
     * @param scale 缩放因子
     * @param zeroPoint 零点
     * @returns 反量化后的模型数据
     */
    private dequantize16Bit;
    /**
     * 获取量化统计
     * @returns 量化统计
     */
    getStats(): QuantizationStats | null;
    /**
     * 获取配置
     * @returns 配置
     */
    getConfig(): QuantizationConfig;
    /**
     * 设置配置
     * @param config 配置
     */
    setConfig(config: Partial<QuantizationConfig>): void;
}
