/**
 * 渲染系统
 * 负责管理渲染过程和渲染相关组件
 */
import { System } from '../core/System';
import { Renderer } from './Renderer';
import type { Camera } from './Camera';
import { Scene } from '../scene/Scene';
import { ShadowSystem } from './shadows/ShadowSystem';
import { PostProcessingSystem } from './postprocessing/PostProcessingSystem';
/**
 * 渲染系统
 */
export declare class RenderSystem extends System {
    /** 系统类型 */
    private static readonly TYPE;
    /** 渲染器 */
    private renderer;
    /** 活跃相机 */
    private activeCamera;
    /** 活跃场景 */
    private activeScene;
    /** 是否自动清除 */
    private autoClear;
    /** 是否启用后处理 */
    private postProcessingEnabled;
    /** 阴影系统 */
    private shadowSystem;
    /** 后处理系统 */
    private postProcessingSystem;
    /**
     * 创建渲染系统
     * @param renderer 渲染器
     * @param options 渲染系统选项
     */
    constructor(renderer: Renderer, options?: {
        enableShadows?: boolean;
        enablePostProcessing?: boolean;
    });
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 处理窗口大小变化
     */
    private handleResize;
    /**
     * 设置活跃相机
     * @param camera 相机
     */
    setActiveCamera(camera: Camera): void;
    /**
     * 获取活跃相机
     * @returns 活跃相机
     */
    getActiveCamera(): Camera | null;
    /**
     * 设置活跃场景
     * @param scene 场景
     */
    setActiveScene(scene: Scene): void;
    /**
     * 获取活跃场景
     * @returns 活跃场景
     */
    getActiveScene(): Scene | null;
    /**
     * 设置是否自动清除
     * @param autoClear 是否自动清除
     */
    setAutoClear(autoClear: boolean): void;
    /**
     * 是否自动清除
     * @returns 是否自动清除
     */
    isAutoClear(): boolean;
    /**
     * 设置是否启用后处理
     * @param enabled 是否启用
     */
    setPostProcessingEnabled(enabled: boolean): void;
    /**
     * 是否启用后处理
     * @returns 是否启用后处理
     */
    isPostProcessingEnabled(): boolean;
    /**
     * 获取阴影系统
     * @returns 阴影系统
     */
    getShadowSystem(): ShadowSystem | null;
    /**
     * 获取后处理系统
     * @returns 后处理系统
     */
    getPostProcessingSystem(): PostProcessingSystem | null;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
