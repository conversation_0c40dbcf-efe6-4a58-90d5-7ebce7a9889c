upstream collaboration_backend {
    # 使用IP哈希算法，确保同一客户端始终连接到同一服务器
    ip_hash;
    
    # 协作服务实例
    server collaboration-service-1:3005;
    server collaboration-service-2:3006;
    
    # 保持连接
    keepalive 32;
}

# 健康检查配置
server {
    listen 80;
    
    # 健康检查端点
    location /health {
        access_log off;
        add_header Content-Type text/plain;
        return 200 'OK';
    }
    
    # WebSocket代理
    location / {
        proxy_pass http://collaboration_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket设置
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # 缓冲设置
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
        
        # 启用压缩
        gzip on;
        gzip_comp_level 6;
        gzip_min_length 1000;
        gzip_types text/plain application/json;
    }
}
