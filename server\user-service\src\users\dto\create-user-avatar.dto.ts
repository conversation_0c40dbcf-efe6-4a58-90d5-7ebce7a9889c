/**
 * 创建用户头像DTO
 */
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserAvatarDto {
  @ApiProperty({ description: '头像URL' })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({ description: '缩略图URL', required: false })
  @IsString()
  @IsOptional()
  thumbnailUrl?: string;

  @ApiProperty({ description: '3D模型URL', required: false })
  @IsString()
  @IsOptional()
  modelUrl?: string;

  @ApiProperty({ description: '头像类型', required: false })
  @IsString()
  @IsOptional()
  type?: string;
}
