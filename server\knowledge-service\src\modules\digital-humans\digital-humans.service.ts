import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DigitalHuman } from '../../entities/digital-human.entity';

@Injectable()
export class DigitalHumansService {
  constructor(
    @InjectRepository(DigitalHuman)
    private readonly digitalHumanRepository: Repository<DigitalHuman>,
  ) {}

  async findAll() {
    return this.digitalHumanRepository.find();
  }

  async findOne(id: string) {
    return this.digitalHumanRepository.findOne({ where: { id } });
  }
}
