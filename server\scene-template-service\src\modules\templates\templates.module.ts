import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TemplatesService } from './templates.service';
import { TemplatesController } from './templates.controller';
import { SceneTemplate } from './entities/scene-template.entity';
import { TemplateCategory } from '../categories/entities/template-category.entity';
import { TemplateParameter } from '../parameters/entities/template-parameter.entity';
import { CacheService } from '../../common/services/cache.service';
import { LoggerService } from '../../common/services/logger.service';
import { StorageService } from '../../common/services/storage.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([SceneTemplate, TemplateCategory, TemplateParameter]),
  ],
  controllers: [TemplatesController],
  providers: [
    TemplatesService,
    CacheService,
    LoggerService,
    StorageService,
  ],
  exports: [TemplatesService],
})
export class TemplatesModule {}
