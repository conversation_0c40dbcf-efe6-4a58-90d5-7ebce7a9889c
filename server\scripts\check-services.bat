@echo off
echo ========================================
echo 数字人RAG系统服务状态检查
echo ========================================

REM 设置环境变量
set COMPOSE_PROJECT_NAME=digital-human-rag

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker服务未运行
    pause
    exit /b 1
)

echo 检查容器状态...
echo ========================================
docker-compose -f docker-compose.windows.yml ps

echo.
echo ========================================
echo 检查服务健康状态
echo ========================================

REM 定义服务健康检查函数
setlocal enabledelayedexpansion

REM 基础设施服务
echo 基础设施服务:
call :check_service "PostgreSQL数据库" "http://localhost:5432" "postgres"
call :check_service "Redis缓存" "http://localhost:6379" "redis"
call :check_service "MinIO存储" "http://localhost:9000/minio/health/live" "minio"
call :check_service "Elasticsearch" "http://localhost:9200/_cluster/health" "elasticsearch"

echo.
echo 监控服务:
call :check_service "Prometheus" "http://localhost:9090/-/healthy" "prometheus"
call :check_service "Grafana" "http://localhost:3001/api/health" "grafana"
call :check_service "Jaeger" "http://localhost:16686/" "jaeger"

echo.
echo 核心微服务:
call :check_service "服务注册中心" "http://localhost:8761/health" "service-registry"
call :check_service "API网关" "http://localhost:8080/health" "api-gateway"

echo.
echo 业务微服务:
call :check_service "用户服务" "http://localhost:4001/health" "user-service"
call :check_service "项目服务" "http://localhost:3002/health" "project-service"
call :check_service "资产服务" "http://localhost:4003/health" "asset-service"
call :check_service "场景生成服务" "http://localhost:3004/health" "scene-generation-service"
call :check_service "资产库服务" "http://localhost:3005/health" "asset-library-service"
call :check_service "渲染服务" "http://localhost:3006/health" "render-service"
call :check_service "协作服务" "http://localhost:3007/health" "collaboration-service"
call :check_service "知识服务" "http://localhost:3008/health" "knowledge-service"
call :check_service "RAG引擎" "http://localhost:3009/health" "rag-engine"
call :check_service "AI模型服务" "http://localhost:3010/health" "ai-model-service"
call :check_service "绑定服务" "http://localhost:3011/health" "binding-service"
call :check_service "游戏服务器" "http://localhost:3012/health" "game-server"
call :check_service "监控服务" "http://localhost:3013/health" "monitoring-service"
call :check_service "场景模板服务" "http://localhost:8004/health" "scene-template-service"

echo.
echo ========================================
echo 资源使用情况
echo ========================================
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

echo.
echo ========================================
echo 检查完成
echo ========================================

pause
goto :eof

:check_service
set service_name=%~1
set service_url=%~2
set container_name=%~3

REM 检查容器是否运行
docker ps --filter "name=%container_name%" --format "{{.Names}}" | findstr /C:"%container_name%" >nul
if %errorlevel% equ 0 (
    REM 容器运行中，检查健康状态
    curl -s -o nul -w "%%{http_code}" %service_url% >temp_status.txt 2>nul
    set /p status=<temp_status.txt
    del temp_status.txt 2>nul
    
    if "!status!"=="200" (
        echo [✓] %service_name% - 运行正常
    ) else if "!status!"=="000" (
        echo [?] %service_name% - 容器运行中，但健康检查失败
    ) else (
        echo [!] %service_name% - HTTP状态码: !status!
    )
) else (
    echo [✗] %service_name% - 容器未运行
)
goto :eof
