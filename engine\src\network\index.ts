/**
 * 网络模块索引文件
 * 导出所有网络相关的类和接口
 */

// 类型和枚举（优先导出，避免冲突）
export * from './types';

// 核心系统
export { NetworkSystem } from './NetworkSystem';
export { NetworkManager } from './NetworkManager';
export { NetworkEventDispatcher } from './NetworkEventDispatcher';
export { NetworkEventBuffer } from './NetworkEventBuffer';

// WebRTC相关
export { WebRTCConnection } from './WebRTCConnection';
export { WebRTCConnectionManager } from './WebRTCConnectionManager';

// 实体同步
export { EntitySyncManager } from './EntitySyncManager';
export { NetworkEntityComponent } from './components/NetworkEntityComponent';

// 用户会话
export { UserSessionManager } from './UserSessionManager';

// 媒体流
export { MediaStreamManager } from './MediaStreamManager';

// 网络质量和带宽
export { NetworkQualityMonitor } from './NetworkQualityMonitor';
export { BandwidthController } from './BandwidthController';

// 数据压缩
export { DataCompressor } from './DataCompressor';

// 服务发现和微服务通信
export { ServiceDiscoveryClient } from './ServiceDiscoveryClient';
export { MicroserviceClient } from './MicroserviceClient';

// 高级网络功能
export { EnhancedNetworkSystem } from './EnhancedNetworkSystem';
export { AdvancedBandwidthController } from './AdvancedBandwidthController';
export { NetworkAdaptiveController } from './NetworkAdaptiveController';
export { NetworkPredictor } from './NetworkPredictor';
export { SyncPriorityManager } from './SyncPriorityManager';

// 网络连接
export { NetworkConnection } from './NetworkConnection';
export { WebSocketConnection } from './WebSocketConnection';
export { WebRTCDataChannel } from './WebRTCDataChannel';

// 消息和序列化
export { MessageSerializer } from './MessageSerializer';
export type { NetworkMessage } from './NetworkMessage';
export type { NetworkEvent } from './NetworkEvent';

// 网络实体和用户
export type { NetworkEntity } from './NetworkEntity';
export type { NetworkUser } from './NetworkUser';

// 组件
export { NetworkTransformComponent } from './components/NetworkTransformComponent';
export { NetworkUserComponent } from './components/NetworkUserComponent';

// 空间分区
export { QuadtreePartitioning } from './spatial/QuadtreePartitioning';

// 网络安全和测试
export { NetworkSecuritySystem } from './NetworkSecuritySystem';
export { BandwidthTester } from './BandwidthTester';
export { ServerConnectionTester } from './ServerConnectionTester';
export { NetworkSimulator } from './NetworkSimulator';
export { NetworkTracer } from './NetworkTracer';
