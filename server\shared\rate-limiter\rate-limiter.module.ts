/**
 * 限流器模块
 */
import { Module, DynamicModule, Global } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { RateLimiterService } from './rate-limiter.service';
import { RateLimiterOptions } from './rate-limiter.interface';

/**
 * 限流器模块配置
 */
export interface RateLimiterModuleOptions {
  /**
   * 是否全局注册
   */
  isGlobal?: boolean;
  
  /**
   * 默认限流器配置
   */
  defaultOptions?: Partial<RateLimiterOptions>;
}

/**
 * 限流器模块
 */
@Global()
@Module({
  imports: [EventEmitterModule.forRoot()],
  providers: [RateLimiterService],
  exports: [RateLimiterService],
})
export class RateLimiterModule {
  /**
   * 注册限流器模块
   * @param options 模块配置
   */
  static register(options: RateLimiterModuleOptions = {}): DynamicModule {
    return {
      module: RateLimiterModule,
      global: options.isGlobal,
      providers: [
        {
          provide: RateLimiterService,
          useFactory: (eventEmitter) => {
            const service = new RateLimiterService(eventEmitter);
            if (options.defaultOptions) {
              Object.assign(service['defaultOptions'], options.defaultOptions);
            }
            return service;
          },
          inject: ['EventEmitter2'],
        },
      ],
      exports: [RateLimiterService],
    };
  }
}

/**
 * 导出所有限流器相关类和接口
 */
export * from './rate-limiter.service';
export * from './rate-limiter.interface';
export * from './token-bucket-rate-limiter';
export * from './sliding-window-rate-limiter';
