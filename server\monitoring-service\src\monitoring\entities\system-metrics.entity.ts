import { En<PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, Index } from 'typeorm';

@Entity('system_metrics')
export class SystemMetricsEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  hostname: string;

  @Column('float')
  cpuUsage: number;

  @Column('float')
  memoryUsage: number;

  @Column('bigint')
  totalMemory: number;

  @Column('bigint')
  freeMemory: number;

  @Column('float')
  diskUsage: number;

  @Column('bigint')
  totalDisk: number;

  @Column('bigint')
  freeDisk: number;

  @Column('simple-array')
  loadAverage: number[];

  @Column('int')
  networkConnections: number;

  @Column('timestamp')
  @Index()
  timestamp: Date;

  @CreateDateColumn()
  createdAt: Date;
}
