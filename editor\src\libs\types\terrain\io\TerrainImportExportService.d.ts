import { TerrainComponent } from '../components/TerrainComponent';
/**
 * 高度图格式
 */
export declare enum HeightMapFormat {
    /** PNG格式 */
    PNG = "png",
    /** JPEG格式 */
    JPEG = "jpeg",
    /** RAW格式 */
    RAW = "raw",
    /** R16格式 (16位灰度图) */
    R16 = "r16",
    /** R32格式 (32位浮点数) */
    R32 = "r32",
    /** ASC格式 (ESRI ASCII Grid) */
    ASC = "asc",
    /** HGT格式 (SRTM) */
    HGT = "hgt",
    /** TER格式 (Terragen) */
    TER = "ter",
    /** BT格式 (Binary Terrain) */
    BT = "bt"
}
/**
 * 地形导出选项
 */
export interface TerrainExportOptions {
    /** 是否包含纹理 */
    includeTextures?: boolean;
    /** 是否包含法线 */
    includeNormals?: boolean;
    /** 是否包含物理属性 */
    includePhysics?: boolean;
    /** 是否美化JSON输出 */
    prettyPrint?: boolean;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 地形导入选项
 */
export interface TerrainImportOptions {
    /** 是否保留现有纹理 */
    keepTextures?: boolean;
    /** 是否保留现有物理属性 */
    keepPhysics?: boolean;
    /** 是否自动计算法线 */
    computeNormals?: boolean;
    /** 是否应用平滑 */
    applySmoothing?: boolean;
    /** 平滑强度 */
    smoothingStrength?: number;
    /** 高度缩放 */
    heightScale?: number;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 地形数据
 */
export interface TerrainData {
    /** 宽度 */
    width: number;
    /** 高度 */
    height: number;
    /** 分辨率 */
    resolution: number;
    /** 最大高度 */
    maxHeight: number;
    /** 高度数据 */
    heightData: Float32Array;
    /** 纹理层 */
    layers?: any[];
    /** 物理属性 */
    physics?: any;
    /** 元数据 */
    metadata?: Record<string, any>;
}
/**
 * 地形导入导出服务
 */
export declare class TerrainImportExportService {
    /**
     * 导出地形为JSON
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns JSON字符串
     */
    exportToJSON(terrain: TerrainComponent, options?: TerrainExportOptions): string;
    /**
     * 从JSON导入地形
     * @param terrain 地形组件
     * @param json JSON字符串
     * @param options 导入选项
     * @returns 是否导入成功
     */
    importFromJSON(terrain: TerrainComponent, json: string, options?: TerrainImportOptions): boolean;
    /**
     * 平滑地形
     * @param terrain 地形组件
     * @param strength 平滑强度
     */
    smoothTerrain(terrain: TerrainComponent, strength: number): void;
}
