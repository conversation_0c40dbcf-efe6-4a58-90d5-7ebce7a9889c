/**
 * AI模型缓存
 * 用于缓存AI模型的结果，提高性能
 */
import { AIModelType } from './AIModelType';
/**
 * 缓存配置
 */
export interface AIModelCacheConfig {
    /** 最大缓存大小 */
    maxSize?: number;
    /** 缓存过期时间（毫秒） */
    expireTime?: number;
    /** 是否启用调试 */
    debug?: boolean;
    /** 清理间隔（毫秒） */
    cleanupInterval?: number;
    /** 缓存命中率阈值 */
    hitRateThreshold?: number;
    /** 缓存命中率窗口大小 */
    hitRateWindowSize?: number;
}
/**
 * 缓存统计
 */
interface CacheStats {
    /** 缓存大小 */
    size: number;
    /** 最大缓存大小 */
    maxSize: number;
    /** 缓存命中次数 */
    hits: number;
    /** 缓存未命中次数 */
    misses: number;
    /** 缓存命中率 */
    hitRate: number;
    /** 缓存过期次数 */
    expirations: number;
    /** 缓存驱逐次数 */
    evictions: number;
}
/**
 * AI模型缓存
 */
export declare class AIModelCache<T> {
    /** 缓存 */
    private cache;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 缓存命中次数 */
    private hits;
    /** 缓存未命中次数 */
    private misses;
    /** 缓存过期次数 */
    private expirations;
    /** 缓存驱逐次数 */
    private evictions;
    /** 清理定时器 */
    private cleanupTimer;
    /** 最近的访问历史 */
    private recentAccesses;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: AIModelCacheConfig);
    /**
     * 生成缓存键
     * @param modelType 模型类型
     * @param input 输入
     * @param options 选项
     * @returns 缓存键
     */
    generateKey(modelType: AIModelType, input: string, options?: any): string;
    /**
     * 获取缓存项
     * @param key 缓存键
     * @returns 缓存值
     */
    get(key: string): T | null;
    /**
     * 设置缓存项
     * @param key 缓存键
     * @param value 缓存值
     * @param expireTime 过期时间（毫秒）
     * @returns 是否成功
     */
    set(key: string, value: T, expireTime?: number): boolean;
    /**
     * 删除缓存项
     * @param key 缓存键
     * @returns 是否成功
     */
    delete(key: string): boolean;
    /**
     * 清空缓存
     */
    clear(): void;
    /**
     * 获取缓存大小
     * @returns 缓存大小
     */
    size(): number;
    /**
     * 获取缓存统计
     * @returns 缓存统计
     */
    getStats(): CacheStats;
    /**
     * 清理过期项
     */
    cleanup(): void;
    /**
     * 驱逐项
     */
    private evictItems;
    /**
     * 启动清理定时器
     */
    private startCleanupTimer;
    /**
     * 停止清理定时器
     */
    stopCleanupTimer(): void;
    /**
     * 更新最近访问历史
     * @param hit 是否命中
     */
    private updateRecentAccesses;
    /**
     * 检查缓存命中率
     */
    private checkHitRate;
    /**
     * 销毁
     */
    dispose(): void;
}
export {};
