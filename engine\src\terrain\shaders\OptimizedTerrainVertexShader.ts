/**
 * 优化的地形顶点着色器
 * 用于提高地形渲染性能
 */
export const optimizedTerrainVertexShader = `
// 属性
attribute vec3 position;
attribute vec3 normal;
attribute vec2 uv;
attribute vec4 tangent;

// 变换矩阵
uniform mat4 modelMatrix;
uniform mat4 viewMatrix;
uniform mat4 projectionMatrix;
uniform mat4 modelViewMatrix;
uniform mat3 normalMatrix;

// 相机位置
uniform vec3 cameraPosition;

// 地形参数
uniform float uMaxHeight;
uniform bool uUseLOD;
uniform float uLODDistance;
uniform float uLODFactor;
uniform bool uUseFog;
uniform float uFogNear;
uniform float uFogFar;
uniform bool uUseClipPlane;
uniform vec4 uClipPlane;

// 输出变量
varying vec2 vUv;
varying vec3 vNormal;
varying vec4 vTangent;
varying vec3 vWorldPosition;
varying vec3 vPosition;
varying float vHeight;
varying float vSlope;
varying float vFogFactor;
varying float vLODBlendFactor;

// 计算斜度
float calculateSlope(vec3 normal) {
  return acos(normal.y) / 3.14159265359 * 2.0;
}

// 计算LOD混合因子
float calculateLODBlendFactor(float distance, float lodDistance, float lodFactor) {
  return clamp((distance - lodDistance) * lodFactor, 0.0, 1.0);
}

void main() {
  // 计算UV坐标
  vUv = uv;
  
  // 计算法线和切线
  vNormal = normalize(normalMatrix * normal);
  vTangent = vec4(normalize(normalMatrix * tangent.xyz), tangent.w);
  
  // 计算顶点位置
  vec4 worldPosition = modelMatrix * vec4(position, 1.0);
  vWorldPosition = worldPosition.xyz;
  vPosition = position;
  
  // 计算高度和斜度
  vHeight = position.y / uMaxHeight;
  vSlope = calculateSlope(vNormal);
  
  // 计算到相机的距离
  float cameraDistance = length(worldPosition.xyz - cameraPosition);
  
  // 计算LOD混合因子
  if (uUseLOD) {
    vLODBlendFactor = calculateLODBlendFactor(cameraDistance, uLODDistance, uLODFactor);
  } else {
    vLODBlendFactor = 0.0;
  }
  
  // 计算雾因子
  if (uUseFog) {
    vFogFactor = smoothstep(uFogNear, uFogFar, cameraDistance);
  } else {
    vFogFactor = 0.0;
  }
  
  // 输出裁剪空间坐标
  gl_Position = projectionMatrix * viewMatrix * worldPosition;
  
  // 应用裁剪平面
  if (uUseClipPlane) {
    gl_ClipDistance[0] = dot(worldPosition, uClipPlane);
  }
}
`;
