
# TabPane 修复指南

## 问题描述
Ant Design 4.x 中的 `TabPane` 组件已被弃用，需要替换为新的 `items` 属性格式。

## 修复步骤

### 1. 移除 TabPane 导入
```typescript
// 修复前
const { TabPane } = Tabs;

// 修复后
// 移除这行代码
```

### 2. 将 TabPane 转换为 items 配置

#### 修复前:
```tsx
<Tabs activeKey={activeTab} onChange={setActiveTab}>
  <TabPane tab="标签1" key="tab1">
    内容1
  </TabPane>
  <TabPane tab="标签2" key="tab2">
    内容2
  </TabPane>
</Tabs>
```

#### 修复后:
```tsx
const tabItems = [
  {
    key: 'tab1',
    label: '标签1',
    children: <div>内容1</div>
  },
  {
    key: 'tab2', 
    label: '标签2',
    children: <div>内容2</div>
  }
];

<Tabs 
  activeKey={activeTab} 
  onChange={setActiveTab}
  items={tabItems}
/>
```

### 3. 处理复杂标签
```tsx
// 修复前
<TabPane 
  tab={
    <span>
      <Icon /> 标签名
    </span>
  } 
  key="tab1"
>
  内容
</TabPane>

// 修复后
{
  key: 'tab1',
  label: (
    <span>
      <Icon /> 标签名
    </span>
  ),
  children: <div>内容</div>
}
```

## 需要手动修复的文件
- src/components/achievements/AchievementPanel.tsx
- src/components/PropertiesPanel/index.tsx
- src/components/scripting/ScriptTemplates.tsx
- src/components/tutorials/TutorialPanel.tsx
- src/components/panels/InspectorPanel.tsx
- src/components/ExampleBrowser/ExampleBrowserEnhanced.tsx
- src/components/optimization/PerformanceOptimizationPanel.tsx

## 参考文档
- [Ant Design Tabs 组件文档](https://ant.design/components/tabs-cn)
- [TabPane 迁移指南](https://ant.design/components/tabs-cn#tabs-tabpane-已废弃)
