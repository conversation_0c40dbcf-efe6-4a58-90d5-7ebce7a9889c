/**
 * 增强型口型同步系统
 * 提供更精确的语音与口型同步功能
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import type { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationComponent, FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';
import { LipSyncComponent } from '../components/LipSyncComponent';

/**
 * 增强型口型同步系统配置
 */
export interface EnhancedLipSyncSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** FFT大小 */
  fftSize?: number;
  /** 音量阈值 */
  volumeThreshold?: number;
  /** 分析间隔（毫秒） */
  analysisInterval?: number;
  /** 是否使用Web Worker */
  useWorker?: boolean;
  /** 频率带数量 */
  numFrequencyBands?: number;
  /** 是否使用高级分析器 */
  useAdvancedAnalyzer?: boolean;
  /** 是否使用AI预测 */
  useAIPrediction?: boolean;
  /** 是否使用GPU */
  useGPU?: boolean;
  /** 是否使用语音识别 */
  useSpeechRecognition?: boolean;
  /** 语音识别语言 */
  speechRecognitionLang?: string;
  /** 是否使用音素分析 */
  usePhonemeAnalysis?: boolean;
  /** 是否使用上下文预测 */
  useContextPrediction?: boolean;
  /** 上下文窗口大小 */
  contextWindowSize?: number;
  /** 是否使用平滑过渡 */
  useSmoothTransition?: boolean;
  /** 过渡时间（秒） */
  transitionTime?: number;
  /** 是否使用中文音素映射 */
  useChinesePhonemeMapping?: boolean;
}

/**
 * 音素类型
 */
export enum PhonemeType {
  A = 'a',
  E = 'e',
  I = 'i',
  O = 'o',
  U = 'u',
  B = 'b',
  P = 'p',
  M = 'm',
  F = 'f',
  V = 'v',
  TH = 'th',
  DH = 'dh',
  T = 't',
  D = 'd',
  N = 'n',
  L = 'l',
  R = 'r',
  S = 's',
  Z = 'z',
  SH = 'sh',
  ZH = 'zh',
  CH = 'ch',
  J = 'j',
  K = 'k',
  G = 'g',
  NG = 'ng',
  H = 'h',
  W = 'w',
  Y = 'y',
  SILENCE = 'silence'
}

/**
 * 中文音素类型
 */
export enum ChinesePhonemeType {
  A = 'a',
  O = 'o',
  E = 'e',
  I = 'i',
  U = 'u',
  V = 'v',
  AI = 'ai',
  EI = 'ei',
  AO = 'ao',
  OU = 'ou',
  AN = 'an',
  EN = 'en',
  ANG = 'ang',
  ENG = 'eng',
  ER = 'er',
  B = 'b',
  P = 'p',
  M = 'm',
  F = 'f',
  D = 'd',
  T = 't',
  N = 'n',
  L = 'l',
  G = 'g',
  K = 'k',
  H = 'h',
  J = 'j',
  Q = 'q',
  X = 'x',
  ZH = 'zh',
  CH = 'ch',
  SH = 'sh',
  R = 'r',
  Z = 'z',
  C = 'c',
  S = 's',
  SILENCE = 'silence'
}

/**
 * 音素到口型映射
 */
const phonemeToVisemeMap: Map<PhonemeType, VisemeType> = new Map([
  [PhonemeType.A, VisemeType.AA],
  [PhonemeType.E, VisemeType.EE],
  [PhonemeType.I, VisemeType.IH],
  [PhonemeType.O, VisemeType.OH],
  [PhonemeType.U, VisemeType.OU],
  [PhonemeType.B, VisemeType.PP],
  [PhonemeType.P, VisemeType.PP],
  [PhonemeType.M, VisemeType.MM],
  [PhonemeType.F, VisemeType.FF],
  [PhonemeType.V, VisemeType.FF],
  [PhonemeType.TH, VisemeType.TH],
  [PhonemeType.DH, VisemeType.TH],
  [PhonemeType.T, VisemeType.DD],
  [PhonemeType.D, VisemeType.DD],
  [PhonemeType.N, VisemeType.NN],
  [PhonemeType.L, VisemeType.NN],
  [PhonemeType.R, VisemeType.RR],
  [PhonemeType.S, VisemeType.SS],
  [PhonemeType.Z, VisemeType.SS],
  [PhonemeType.SH, VisemeType.SS],
  [PhonemeType.ZH, VisemeType.SS],
  [PhonemeType.CH, VisemeType.CH],
  [PhonemeType.J, VisemeType.CH],
  [PhonemeType.K, VisemeType.KK],
  [PhonemeType.G, VisemeType.KK],
  [PhonemeType.NG, VisemeType.NN],
  [PhonemeType.H, VisemeType.KK],
  [PhonemeType.W, VisemeType.OU],
  [PhonemeType.Y, VisemeType.IH],
  [PhonemeType.SILENCE, VisemeType.SILENT]
]);

/**
 * 中文音素到口型映射
 */
const chinesePhonemeToVisemeMap: Map<ChinesePhonemeType, VisemeType> = new Map([
  [ChinesePhonemeType.A, VisemeType.AA],
  [ChinesePhonemeType.O, VisemeType.OH],
  [ChinesePhonemeType.E, VisemeType.EE],
  [ChinesePhonemeType.I, VisemeType.IH],
  [ChinesePhonemeType.U, VisemeType.OU],
  [ChinesePhonemeType.V, VisemeType.IH],
  [ChinesePhonemeType.AI, VisemeType.AA],
  [ChinesePhonemeType.EI, VisemeType.EE],
  [ChinesePhonemeType.AO, VisemeType.AA],
  [ChinesePhonemeType.OU, VisemeType.OU],
  [ChinesePhonemeType.AN, VisemeType.AA],
  [ChinesePhonemeType.EN, VisemeType.EE],
  [ChinesePhonemeType.ANG, VisemeType.AA],
  [ChinesePhonemeType.ENG, VisemeType.EE],
  [ChinesePhonemeType.ER, VisemeType.RR],
  [ChinesePhonemeType.B, VisemeType.PP],
  [ChinesePhonemeType.P, VisemeType.PP],
  [ChinesePhonemeType.M, VisemeType.MM],
  [ChinesePhonemeType.F, VisemeType.FF],
  [ChinesePhonemeType.D, VisemeType.DD],
  [ChinesePhonemeType.T, VisemeType.DD],
  [ChinesePhonemeType.N, VisemeType.NN],
  [ChinesePhonemeType.L, VisemeType.NN],
  [ChinesePhonemeType.G, VisemeType.KK],
  [ChinesePhonemeType.K, VisemeType.KK],
  [ChinesePhonemeType.H, VisemeType.KK],
  [ChinesePhonemeType.J, VisemeType.CH],
  [ChinesePhonemeType.Q, VisemeType.CH],
  [ChinesePhonemeType.X, VisemeType.SS],
  [ChinesePhonemeType.ZH, VisemeType.SS],
  [ChinesePhonemeType.CH, VisemeType.CH],
  [ChinesePhonemeType.SH, VisemeType.SS],
  [ChinesePhonemeType.R, VisemeType.RR],
  [ChinesePhonemeType.Z, VisemeType.SS],
  [ChinesePhonemeType.C, VisemeType.SS],
  [ChinesePhonemeType.S, VisemeType.SS],
  [ChinesePhonemeType.SILENCE, VisemeType.SILENT]
]);

/**
 * 增强型口型同步系统
 */
export class EnhancedLipSyncSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'EnhancedLipSyncSystem';

  /** 配置 */
  private config: EnhancedLipSyncSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 口型同步组件映射 */
  private components: Map<Entity, LipSyncComponent> = new Map();

  /** 音频上下文 */
  private audioContext: AudioContext | null = null;

  /** 音频分析器 */
  private audioAnalyser: AnalyserNode | null = null;

  /** 音频源 */
  private audioSource: MediaStreamAudioSourceNode | MediaElementAudioSourceNode | null = null;

  /** 频谱数据 */
  private spectrum: Float32Array | null = null;

  /** 是否正在跟踪 */
  private isTracking: boolean = false;

  /** 语音识别器 */
  private speechRecognition: SpeechRecognition | null = null;

  /** 当前识别的文本 */
  private recognizedText: string = '';

  /** 当前音素序列 */
  private currentPhonemes: PhonemeType[] = [];

  /** 当前口型 */
  private currentViseme: VisemeType = VisemeType.SILENT;

  /** 上一个口型 */
  private previousViseme: VisemeType = VisemeType.SILENT;

  /** 口型历史 */
  private visemeHistory: VisemeType[] = [];

  /** 过渡计时器 */
  private transitionTimer: number = 0;

  /** 过渡开始时间 */
  private transitionStartTime: number = 0;

  /** Web Worker */
  private worker: Worker | null = null;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: EnhancedLipSyncSystemConfig = {}) {
    super(275); // 设置优先级

    this.config = {
      debug: false,
      fftSize: 1024,
      volumeThreshold: 0.01,
      analysisInterval: 100,
      useWorker: false,
      numFrequencyBands: 8,
      useAdvancedAnalyzer: false,
      useAIPrediction: false,
      useGPU: false,
      useSpeechRecognition: false,
      speechRecognitionLang: 'zh-CN',
      usePhonemeAnalysis: true,
      useContextPrediction: true,
      contextWindowSize: 5,
      useSmoothTransition: true,
      transitionTime: 0.1,
      useChinesePhonemeMapping: false,
      ...config
    };

    if (this.config.debug) {
      console.log('增强型口型同步系统初始化');
    }

    // 初始化音频上下文
    this.initAudioContext();

    // 初始化语音识别
    if (this.config.useSpeechRecognition) {
      this.initSpeechRecognition();
    }

    // 初始化Web Worker
    if (this.config.useWorker) {
      this.initWorker();
    }
  }

  /**
   * 初始化音频上下文
   */
  private initAudioContext(): void {
    try {
      // 创建音频上下文
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      if (this.config.debug) {
        console.log('音频上下文已创建');
      }
    } catch (error) {
      console.error('创建音频上下文失败:', error);
    }
  }

  /**
   * 初始化语音识别
   */
  private initSpeechRecognition(): void {
    try {
      // 检查浏览器是否支持语音识别
      const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition;

      if (!SpeechRecognition) {
        console.warn('浏览器不支持语音识别');
        return;
      }

      // 创建语音识别实例
      this.speechRecognition = new SpeechRecognition();

      // 配置语音识别
      this.speechRecognition.continuous = true;
      this.speechRecognition.interimResults = true;
      this.speechRecognition.lang = this.config.speechRecognitionLang || 'zh-CN';

      // 设置事件处理程序
      this.speechRecognition.onresult = this.handleSpeechRecognitionResult.bind(this);
      this.speechRecognition.onerror = (event) => {
        console.error('语音识别错误:', event.error);
      };

      if (this.config.debug) {
        console.log('语音识别已初始化');
      }
    } catch (error) {
      console.error('初始化语音识别失败:', error);
    }
  }

  /**
   * 初始化Web Worker
   */
  private initWorker(): void {
    try {
      // 创建Web Worker
      this.worker = new Worker('workers/LipSyncWorker.js');

      // 设置消息处理程序
      this.worker.onmessage = this.handleWorkerMessage.bind(this);

      // 发送初始化消息
      this.worker.postMessage({
        type: 'init',
        config: {
          fftSize: this.config.fftSize,
          volumeThreshold: this.config.volumeThreshold,
          numFrequencyBands: this.config.numFrequencyBands,
          usePhonemeAnalysis: this.config.usePhonemeAnalysis,
          useContextPrediction: this.config.useContextPrediction,
          contextWindowSize: this.config.contextWindowSize,
          useChinesePhonemeMapping: this.config.useChinesePhonemeMapping
        }
      });

      if (this.config.debug) {
        console.log('Web Worker已初始化');
      }
    } catch (error) {
      console.error('初始化Web Worker失败:', error);
    }
  }

  /**
   * 处理语音识别结果
   * @param event 语音识别事件
   */
  private handleSpeechRecognitionResult(event: SpeechRecognitionEvent): void {
    // 获取最新的识别结果
    const result = event.results[event.results.length - 1];

    // 获取识别文本
    const text = result[0].transcript;

    // 更新识别文本
    this.recognizedText = text;

    if (this.config.debug) {
      console.log('识别文本:', text);
    }

    // 分析音素
    if (this.config.usePhonemeAnalysis) {
      this.analyzePhonemes(text);
    }
  }

  /**
   * 处理Worker消息
   * @param event 消息事件
   */
  private handleWorkerMessage(event: MessageEvent): void {
    const data = event.data;

    if (data.type === 'viseme') {
      // 设置口型
      this.setVisemeForAllEntities(data.viseme, data.weight);
    }
  }

  /**
   * 分析音素
   * @param text 文本
   */
  private analyzePhonemes(text: string): void {
    // 根据语言选择不同的音素分析方法
    if (this.config.speechRecognitionLang?.startsWith('zh')) {
      this.analyzeChinesePhonemes(text);
    } else {
      this.analyzeEnglishPhonemes(text);
    }
  }

  /**
   * 分析中文音素
   * @param text 中文文本
   */
  private analyzeChinesePhonemes(text: string): void {
    // 这里是简化的中文音素分析
    // 实际应用中应使用专业的中文音素分析库

    // 清空当前音素序列
    this.currentPhonemes = [];

    // 简单地将每个字符映射为一个音素
    for (const char of text) {
      // 根据字符选择音素
      // 这是一个非常简化的实现
      let phoneme: PhonemeType;

      if (/[aeiouü]/.test(char.toLowerCase())) {
        // 元音
        switch (char.toLowerCase()) {
          case 'a': phoneme = PhonemeType.A; break;
          case 'e': phoneme = PhonemeType.E; break;
          case 'i': phoneme = PhonemeType.I; break;
          case 'o': phoneme = PhonemeType.O; break;
          case 'u': phoneme = PhonemeType.U; break;
          default: phoneme = PhonemeType.A;
        }
      } else if (/[bpmfdtnlgkhjqxzcsryw]/.test(char.toLowerCase())) {
        // 辅音
        switch (char.toLowerCase()) {
          case 'b': case 'p': phoneme = PhonemeType.B; break;
          case 'm': phoneme = PhonemeType.M; break;
          case 'f': phoneme = PhonemeType.F; break;
          case 'd': case 't': phoneme = PhonemeType.D; break;
          case 'n': case 'l': phoneme = PhonemeType.N; break;
          case 'g': case 'k': case 'h': phoneme = PhonemeType.K; break;
          case 'j': case 'q': case 'x': phoneme = PhonemeType.J; break;
          case 'z': case 'c': case 's': phoneme = PhonemeType.S; break;
          case 'r': phoneme = PhonemeType.R; break;
          case 'y': phoneme = PhonemeType.Y; break;
          case 'w': phoneme = PhonemeType.W; break;
          default: phoneme = PhonemeType.K;
        }
      } else {
        // 其他字符
        phoneme = PhonemeType.SILENCE;
      }

      // 添加到音素序列
      this.currentPhonemes.push(phoneme);
    }

    // 根据音素序列设置口型
    this.setVisemeFromPhonemes();
  }

  /**
   * 分析英文音素
   * @param text 英文文本
   */
  private analyzeEnglishPhonemes(text: string): void {
    // 这里是简化的英文音素分析
    // 实际应用中应使用专业的英文音素分析库

    // 清空当前音素序列
    this.currentPhonemes = [];

    // 简单地将每个字符映射为一个音素
    for (const char of text) {
      // 根据字符选择音素
      let phoneme: PhonemeType;

      if (/[aeiou]/.test(char.toLowerCase())) {
        // 元音
        switch (char.toLowerCase()) {
          case 'a': phoneme = PhonemeType.A; break;
          case 'e': phoneme = PhonemeType.E; break;
          case 'i': phoneme = PhonemeType.I; break;
          case 'o': phoneme = PhonemeType.O; break;
          case 'u': phoneme = PhonemeType.U; break;
          default: phoneme = PhonemeType.A;
        }
      } else if (/[bcdfghjklmnpqrstvwxyz]/.test(char.toLowerCase())) {
        // 辅音
        switch (char.toLowerCase()) {
          case 'b': phoneme = PhonemeType.B; break;
          case 'c': phoneme = PhonemeType.K; break;
          case 'd': phoneme = PhonemeType.D; break;
          case 'f': phoneme = PhonemeType.F; break;
          case 'g': phoneme = PhonemeType.G; break;
          case 'h': phoneme = PhonemeType.H; break;
          case 'j': phoneme = PhonemeType.J; break;
          case 'k': phoneme = PhonemeType.K; break;
          case 'l': phoneme = PhonemeType.L; break;
          case 'm': phoneme = PhonemeType.M; break;
          case 'n': phoneme = PhonemeType.N; break;
          case 'p': phoneme = PhonemeType.P; break;
          case 'q': phoneme = PhonemeType.K; break;
          case 'r': phoneme = PhonemeType.R; break;
          case 's': phoneme = PhonemeType.S; break;
          case 't': phoneme = PhonemeType.T; break;
          case 'v': phoneme = PhonemeType.V; break;
          case 'w': phoneme = PhonemeType.W; break;
          case 'x': phoneme = PhonemeType.K; break;
          case 'y': phoneme = PhonemeType.Y; break;
          case 'z': phoneme = PhonemeType.Z; break;
          default: phoneme = PhonemeType.K;
        }
      } else {
        // 其他字符
        phoneme = PhonemeType.SILENCE;
      }

      // 添加到音素序列
      this.currentPhonemes.push(phoneme);
    }

    // 根据音素序列设置口型
    this.setVisemeFromPhonemes();
  }

  /**
   * 根据音素序列设置口型
   */
  private setVisemeFromPhonemes(): void {
    if (this.currentPhonemes.length === 0) {
      this.setVisemeForAllEntities(VisemeType.SILENT);
      return;
    }

    // 获取当前音素
    const currentPhoneme = this.currentPhonemes[0];

    // 获取对应的口型
    let viseme: VisemeType;

    if (this.config.useChinesePhonemeMapping) {
      // 使用中文音素映射
      const chinesePhoneme = currentPhoneme as unknown as ChinesePhonemeType;
      viseme = chinesePhonemeToVisemeMap.get(chinesePhoneme) || VisemeType.SILENT;
    } else {
      // 使用标准音素映射
      viseme = phonemeToVisemeMap.get(currentPhoneme) || VisemeType.SILENT;
    }

    // 设置口型
    this.setVisemeForAllEntities(viseme);

    // 移除已处理的音素
    this.currentPhonemes.shift();
  }

  /**
   * 为所有实体设置口型
   * @param viseme 口型
   * @param weight 权重
   */
  private setVisemeForAllEntities(viseme: VisemeType, weight: number = 1.0): void {
    // 更新口型历史
    this.updateVisemeHistory(viseme);

    // 保存上一个口型
    this.previousViseme = this.currentViseme;
    this.currentViseme = viseme;

    // 重置过渡计时器
    if (this.config.useSmoothTransition && this.previousViseme !== this.currentViseme) {
      this.transitionStartTime = Date.now();
      this.transitionTimer = 0;
    }

    // 为每个实体设置口型
    for (const [entity, component] of this.components.entries()) {
      // 获取面部动画组件
      const facialAnimation = entity.getComponent(FacialAnimationComponent.TYPE) as FacialAnimationComponent;

      if (facialAnimation) {
        // 设置口型
        if (this.config.useSmoothTransition && this.previousViseme !== this.currentViseme) {
          // 使用平滑过渡
          const transitionTime = this.config.transitionTime || 0.1;
          facialAnimation.setViseme(viseme, weight, transitionTime);
        } else {
          // 直接设置
          facialAnimation.setViseme(viseme, weight);
        }
      }
    }
  }

  /**
   * 更新口型历史
   * @param viseme 口型
   */
  private updateVisemeHistory(viseme: VisemeType): void {
    // 添加到历史
    this.visemeHistory.push(viseme);

    // 限制历史大小
    if (this.visemeHistory.length > (this.config.contextWindowSize || 5)) {
      this.visemeHistory.shift();
    }
  }

  /**
   * 创建口型同步组件
   * @param entity 实体
   * @returns 口型同步组件
   */
  public createLipSync(entity: Entity): LipSyncComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建新组件
    const component = new LipSyncComponent(entity);
    this.components.set(entity, component);

    if (this.config.debug) {
      console.log(`创建口型同步组件: ${entity.id}`);
    }

    return component;
  }

  /**
   * 移除口型同步组件
   * @param entity 实体
   */
  public removeLipSync(entity: Entity): void {
    if (this.components.has(entity)) {
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`移除口型同步组件: ${entity.id}`);
      }
    }
  }

  /**
   * 获取口型同步组件
   * @param entity 实体
   * @returns 口型同步组件
   */
  public getLipSync(entity: Entity): LipSyncComponent | undefined {
    return this.components.get(entity);
  }

  /**
   * 启动口型同步跟踪
   * @param audioElement 音频元素
   */
  public startTracking(audioElement: HTMLAudioElement | HTMLVideoElement): void {
    if (this.isTracking) {
      this.stopTracking();
    }

    if (!this.audioContext) {
      this.initAudioContext();
    }

    if (!this.audioContext) {
      console.error('无法创建音频上下文');
      return;
    }

    try {
      // 创建音频源
      this.audioSource = this.audioContext.createMediaElementSource(audioElement);

      // 创建分析器
      this.audioAnalyser = this.audioContext.createAnalyser();
      this.audioAnalyser.fftSize = this.config.fftSize || 1024;

      // 连接节点
      this.audioSource.connect(this.audioAnalyser);
      this.audioAnalyser.connect(this.audioContext.destination);

      // 创建频谱数据数组
      this.spectrum = new Float32Array(this.audioAnalyser.frequencyBinCount);

      // 设置跟踪状态
      this.isTracking = true;

      // 启动语音识别
      if (this.config.useSpeechRecognition && this.speechRecognition) {
        this.speechRecognition.start();
      }

      // 启动分析定时器
      this.startAnalysisTimer();

      if (this.config.debug) {
        console.log('口型同步跟踪已启动');
      }
    } catch (error) {
      console.error('启动口型同步跟踪失败:', error);
    }
  }

  /**
   * 启动麦克风跟踪
   */
  public async startMicrophoneTracking(): Promise<boolean> {
    if (this.isTracking) {
      this.stopTracking();
    }

    if (!this.audioContext) {
      this.initAudioContext();
    }

    if (!this.audioContext) {
      console.error('无法创建音频上下文');
      return false;
    }

    try {
      // 获取麦克风流
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // 创建音频源
      this.audioSource = this.audioContext.createMediaStreamSource(stream);

      // 创建分析器
      this.audioAnalyser = this.audioContext.createAnalyser();
      this.audioAnalyser.fftSize = this.config.fftSize || 1024;

      // 连接节点
      this.audioSource.connect(this.audioAnalyser);

      // 创建频谱数据数组
      this.spectrum = new Float32Array(this.audioAnalyser.frequencyBinCount);

      // 设置跟踪状态
      this.isTracking = true;

      // 启动语音识别
      if (this.config.useSpeechRecognition && this.speechRecognition) {
        this.speechRecognition.start();
      }

      // 启动分析定时器
      this.startAnalysisTimer();

      if (this.config.debug) {
        console.log('麦克风口型同步跟踪已启动');
      }

      return true;
    } catch (error) {
      console.error('启动麦克风口型同步跟踪失败:', error);
      return false;
    }
  }

  /**
   * 停止口型同步跟踪
   */
  public stopTracking(): void {
    // 停止语音识别
    if (this.speechRecognition) {
      try {
        this.speechRecognition.stop();
      } catch (error) {
        // 忽略错误
      }
    }

    // 断开音频连接
    if (this.audioSource) {
      try {
        this.audioSource.disconnect();
      } catch (error) {
        // 忽略错误
      }
      this.audioSource = null;
    }

    if (this.audioAnalyser) {
      try {
        this.audioAnalyser.disconnect();
      } catch (error) {
        // 忽略错误
      }
      this.audioAnalyser = null;
    }

    // 设置跟踪状态
    this.isTracking = false;

    // 重置口型
    this.setVisemeForAllEntities(VisemeType.SILENT);

    if (this.config.debug) {
      console.log('口型同步跟踪已停止');
    }
  }

  /**
   * 启动分析定时器
   */
  private startAnalysisTimer(): void {
    // 设置定时器
    const interval = this.config.analysisInterval || 100;

    const analyze = () => {
      if (!this.isTracking) return;

      // 分析音频
      this.analyzeAudio();

      // 继续定时器
      setTimeout(analyze, interval);
    };

    // 启动定时器
    analyze();
  }

  /**
   * 分析音频
   */
  private analyzeAudio(): void {
    if (!this.audioAnalyser || !this.spectrum) return;

    // 获取频谱数据
    this.audioAnalyser.getFloatFrequencyData(this.spectrum);

    // 使用Web Worker处理
    if (this.config.useWorker && this.worker) {
      this.worker.postMessage({
        type: 'analyze',
        spectrum: this.spectrum
      });
      return;
    }

    // 计算RMS
    const rms = this.calculateRMS(this.spectrum);

    // 如果音量太小，则设置为静默
    if (rms < (this.config.volumeThreshold || 0.01)) {
      this.setVisemeForAllEntities(VisemeType.SILENT);
      return;
    }

    // 如果使用语音识别和音素分析，则跳过频谱分析
    if (this.config.useSpeechRecognition && this.config.usePhonemeAnalysis && this.currentPhonemes.length > 0) {
      return;
    }

    // 分析频谱并确定口型
    const viseme = this.analyzeSpectrum(this.spectrum);
    this.setVisemeForAllEntities(viseme);
  }

  /**
   * 计算RMS
   * @param spectrum 频谱数据
   * @returns RMS值
   */
  private calculateRMS(spectrum: Float32Array): number {
    let sum = 0;

    // 计算平方和
    for (let i = 0; i < spectrum.length; i++) {
      // 将dB转换为线性值
      const linear = Math.pow(10, spectrum[i] / 20);
      sum += linear * linear;
    }

    // 计算均方根
    const rms = Math.sqrt(sum / spectrum.length);

    return rms;
  }

  /**
   * 分析频谱
   * @param spectrum 频谱数据
   * @returns 口型
   */
  private analyzeSpectrum(spectrum: Float32Array): VisemeType {
    // 简单的频谱分析
    // 实际应用中应使用更复杂的算法

    // 计算频率带能量
    const bands = this.calculateFrequencyBands(spectrum);

    // 找出能量最大的频率带
    let maxEnergy = -Infinity;
    let maxBandIndex = 0;

    for (let i = 0; i < bands.length; i++) {
      if (bands[i] > maxEnergy) {
        maxEnergy = bands[i];
        maxBandIndex = i;
      }
    }

    // 根据频率带选择口型
    let viseme: VisemeType;

    switch (maxBandIndex) {
      case 0: // 低频 (0-500Hz)
        viseme = VisemeType.MM;
        break;
      case 1: // 中低频 (500-1000Hz)
        viseme = VisemeType.AA;
        break;
      case 2: // 中频 (1000-2000Hz)
        viseme = VisemeType.EE;
        break;
      case 3: // 中高频 (2000-4000Hz)
        viseme = VisemeType.IH;
        break;
      case 4: // 高频 (4000-8000Hz)
        viseme = VisemeType.SS;
        break;
      case 5: // 超高频 (8000-16000Hz)
        viseme = VisemeType.FF;
        break;
      default:
        viseme = VisemeType.SILENT;
    }

    // 使用上下文预测改进结果
    if (this.config.useContextPrediction && this.visemeHistory.length > 0) {
      viseme = this.predictVisemeFromContext(viseme);
    }

    return viseme;
  }

  /**
   * 计算频率带能量
   * @param spectrum 频谱数据
   * @returns 频率带能量
   */
  private calculateFrequencyBands(spectrum: Float32Array): number[] {
    const numBands = this.config.numFrequencyBands || 6;
    const bands: number[] = new Array(numBands).fill(0);

    // 计算每个频率带的能量
    const bandSize = Math.floor(spectrum.length / numBands);

    for (let i = 0; i < numBands; i++) {
      const startIndex = i * bandSize;
      const endIndex = (i + 1) * bandSize;

      let sum = 0;

      for (let j = startIndex; j < endIndex; j++) {
        // 将dB转换为线性值
        const linear = Math.pow(10, spectrum[j] / 20);
        sum += linear;
      }

      bands[i] = sum / bandSize;
    }

    return bands;
  }

  /**
   * 根据上下文预测口型
   * @param currentViseme 当前口型
   * @returns 预测的口型
   */
  private predictVisemeFromContext(currentViseme: VisemeType): VisemeType {
    if (this.visemeHistory.length === 0) {
      return currentViseme;
    }

    // 简单的上下文预测
    // 如果当前口型与上一个口型不同，且上一个口型不是静默，则有一定概率保持上一个口型
    const lastViseme = this.visemeHistory[this.visemeHistory.length - 1];

    if (currentViseme !== lastViseme && lastViseme !== VisemeType.SILENT) {
      // 50%的概率保持上一个口型
      if (Math.random() < 0.5) {
        return lastViseme;
      }
    }

    return currentViseme;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新过渡计时器
    if (this.config.useSmoothTransition && this.previousViseme !== this.currentViseme) {
      this.transitionTimer += deltaTime;

      // 如果过渡完成，重置计时器
      if (this.transitionTimer >= (this.config.transitionTime || 0.1)) {
        this.transitionTimer = 0;
        this.previousViseme = this.currentViseme;
      }
    }
  }
}
