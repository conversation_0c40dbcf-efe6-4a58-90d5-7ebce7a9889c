/**
 * 视觉脚本WebRTC节点
 * 提供WebRTC相关功能，如媒体流控制、数据通道管理等
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { EventNode } from '../nodes/EventNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 创建WebRTC连接节点
 * 创建与远程对等方的WebRTC连接
 */
export declare class CreateWebRTCConnectionNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 发送数据通道消息节点
 * 通过WebRTC连接发送数据
 */
export declare class SendDataChannelMessageNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 数据通道消息事件节点
 * 监听数据通道消息事件
 */
export declare class DataChannelMessageEventNode extends EventNode {
    /** 数据通道 */
    private dataChannel;
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 初始化事件
     */
    initialize(): void;
    /**
     * 消息事件处理
     * @param message 消息数据
     */
    private onMessage;
    /**
     * 清理事件
     */
    cleanup(): void;
}
/**
 * 注册WebRTC节点
 * @param registry 节点注册表
 */
export declare function registerWebRTCNodes(registry: NodeRegistry): void;
