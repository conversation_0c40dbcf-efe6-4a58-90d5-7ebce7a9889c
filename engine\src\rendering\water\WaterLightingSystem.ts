/**
 * 水体光照系统
 * 用于处理水体的光照效果，包括水面反射、折射、焦散、体积光等
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 水体光照系统事件类型
 */
export enum WaterLightingSystemEventType {
  /** 水面反射更新 */
  REFLECTION_UPDATED = 'reflection_updated',
  /** 水面折射更新 */
  REFRACTION_UPDATED = 'refraction_updated',
  /** 焦散更新 */
  CAUSTICS_UPDATED = 'caustics_updated',
  /** 体积光更新 */
  VOLUMETRIC_LIGHT_UPDATED = 'volumetric_light_updated',
  /** 水下雾效更新 */
  UNDERWATER_FOG_UPDATED = 'underwater_fog_updated'
}

/**
 * 水体光照系统配置
 */
export interface WaterLightingSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用水面反射 */
  enableReflection?: boolean;
  /** 是否启用水面折射 */
  enableRefraction?: boolean;
  /** 是否启用焦散 */
  enableCaustics?: boolean;
  /** 是否启用体积光 */
  enableVolumetricLight?: boolean;
  /** 是否启用水下雾效 */
  enableUnderwaterFog?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 反射贴图分辨率 */
  reflectionMapResolution?: number;
  /** 折射贴图分辨率 */
  refractionMapResolution?: number;
  /** 焦散贴图分辨率 */
  causticsMapResolution?: number;
  /** 体积光贴图分辨率 */
  volumetricLightMapResolution?: number;
  /** 反射强度 */
  reflectionIntensity?: number;
  /** 折射强度 */
  refractionIntensity?: number;
  /** 焦散强度 */
  causticsIntensity?: number;
  /** 体积光强度 */
  volumetricLightIntensity?: number;
  /** 水下雾效强度 */
  underwaterFogIntensity?: number;
  /** 水下雾效颜色 */
  underwaterFogColor?: THREE.Color;
  /** 水下雾效密度 */
  underwaterFogDensity?: number;
  /** 是否启用高质量反射 */
  enableHighQualityReflection?: boolean;
  /** 是否启用高质量折射 */
  enableHighQualityRefraction?: boolean;
  /** 是否启用高质量焦散 */
  enableHighQualityCaustics?: boolean;
  /** 是否启用高质量体积光 */
  enableHighQualityVolumetricLight?: boolean;
}

/**
 * 水体光照系统
 */
export class WaterLightingSystem extends System {
  /** 配置 */
  private config: WaterLightingSystemConfig;
  /** 水体组件 */
  private waterBodies: Map<string, WaterBodyComponent> = new Map();
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 当前更新频率 */
  private currentUpdateFrequency: number = 1;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 反射相机 */
  private reflectionCamera: THREE.PerspectiveCamera | null = null;
  /** 折射相机 */
  private refractionCamera: THREE.PerspectiveCamera | null = null;
  /** 反射渲染目标 */
  private reflectionRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 折射渲染目标 */
  private refractionRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 焦散渲染目标 */
  private causticsRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 体积光渲染目标 */
  private volumetricLightRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 焦散材质 */
  private causticsMaterial: THREE.ShaderMaterial | null = null;
  /** 体积光材质 */
  private volumetricLightMaterial: THREE.ShaderMaterial | null = null;
  /** 水下雾效材质 */
  private underwaterFogMaterial: THREE.ShaderMaterial | null = null;
  /** 反射平面 */
  private reflectionPlane: THREE.Plane = new THREE.Plane();
  /** 折射平面 */
  private refractionPlane: THREE.Plane = new THREE.Plane();
  /** 是否在水下 */
  private isUnderwater: boolean = false;
  /** 当前水体 */
  private currentWaterBody: WaterBodyComponent | null = null;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: WaterLightingSystemConfig = {}) {
    super(0);

    // 设置配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      enableReflection: config.enableReflection !== undefined ? config.enableReflection : true,
      enableRefraction: config.enableRefraction !== undefined ? config.enableRefraction : true,
      enableCaustics: config.enableCaustics !== undefined ? config.enableCaustics : true,
      enableVolumetricLight: config.enableVolumetricLight !== undefined ? config.enableVolumetricLight : true,
      enableUnderwaterFog: config.enableUnderwaterFog !== undefined ? config.enableUnderwaterFog : true,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== undefined ? config.enablePerformanceMonitoring : false,
      reflectionMapResolution: config.reflectionMapResolution || 512,
      refractionMapResolution: config.refractionMapResolution || 512,
      causticsMapResolution: config.causticsMapResolution || 512,
      volumetricLightMapResolution: config.volumetricLightMapResolution || 256,
      reflectionIntensity: config.reflectionIntensity || 1.0,
      refractionIntensity: config.refractionIntensity || 1.0,
      causticsIntensity: config.causticsIntensity || 1.0,
      volumetricLightIntensity: config.volumetricLightIntensity || 1.0,
      underwaterFogIntensity: config.underwaterFogIntensity || 1.0,
      underwaterFogColor: config.underwaterFogColor || new THREE.Color(0x0055aa),
      underwaterFogDensity: config.underwaterFogDensity || 0.1,
      enableHighQualityReflection: config.enableHighQualityReflection !== undefined ? config.enableHighQualityReflection : false,
      enableHighQualityRefraction: config.enableHighQualityRefraction !== undefined ? config.enableHighQualityRefraction : false,
      enableHighQualityCaustics: config.enableHighQualityCaustics !== undefined ? config.enableHighQualityCaustics : false,
      enableHighQualityVolumetricLight: config.enableHighQualityVolumetricLight !== undefined ? config.enableHighQualityVolumetricLight : false
    };

    // 设置当前更新频率
    this.currentUpdateFrequency = this.config.updateFrequency || 1;

    // 初始化渲染目标和相机
    this.initializeRenderTargets();
    this.initializeCameras();
    this.initializeShaders();
  }

  /**
   * 初始化渲染目标
   */
  private initializeRenderTargets(): void {
    // 创建反射渲染目标
    if (this.config.enableReflection) {
      this.reflectionRenderTarget = new THREE.WebGLRenderTarget(
        this.config.reflectionMapResolution!,
        this.config.reflectionMapResolution!,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }

    // 创建折射渲染目标
    if (this.config.enableRefraction) {
      this.refractionRenderTarget = new THREE.WebGLRenderTarget(
        this.config.refractionMapResolution!,
        this.config.refractionMapResolution!,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }

    // 创建焦散渲染目标
    if (this.config.enableCaustics) {
      this.causticsRenderTarget = new THREE.WebGLRenderTarget(
        this.config.causticsMapResolution!,
        this.config.causticsMapResolution!,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }

    // 创建体积光渲染目标
    if (this.config.enableVolumetricLight) {
      this.volumetricLightRenderTarget = new THREE.WebGLRenderTarget(
        this.config.volumetricLightMapResolution!,
        this.config.volumetricLightMapResolution!,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }
  }

  /**
   * 初始化相机
   */
  private initializeCameras(): void {
    // 创建反射相机
    if (this.config.enableReflection) {
      this.reflectionCamera = new THREE.PerspectiveCamera(50, 1, 0.1, 1000);
    }

    // 创建折射相机
    if (this.config.enableRefraction) {
      this.refractionCamera = new THREE.PerspectiveCamera(50, 1, 0.1, 1000);
    }
  }

  /**
   * 初始化着色器
   */
  private initializeShaders(): void {
    // 初始化焦散材质
    if (this.config.enableCaustics) {
      this.initializeCausticsMaterial();
    }

    // 初始化体积光材质
    if (this.config.enableVolumetricLight) {
      this.initializeVolumetricLightMaterial();
    }

    // 初始化水下雾效材质
    if (this.config.enableUnderwaterFog) {
      this.initializeUnderwaterFogMaterial();
    }
  }

  /**
   * 初始化焦散材质
   */
  private initializeCausticsMaterial(): void {
    // 焦散着色器
    const causticsVertexShader = `
      uniform mat4 textureMatrix;
      uniform float time;

      varying vec4 vUv;
      varying vec2 vUv2;

      void main() {
        vUv = textureMatrix * vec4(position, 1.0);
        vUv2 = uv;

        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const causticsFragmentShader = `
      uniform sampler2D reflectionSampler;
      uniform sampler2D refractionSampler;
      uniform sampler2D normalSampler;
      uniform float time;
      uniform float causticsIntensity;

      varying vec4 vUv;
      varying vec2 vUv2;

      // 焦散计算函数
      vec3 calculateCaustics(vec2 uv, float time) {
        // 水面法线扰动
        vec2 distortion1 = texture2D(normalSampler, uv * 0.5 + vec2(time * 0.01, time * 0.02)).rg * 2.0 - 1.0;
        vec2 distortion2 = texture2D(normalSampler, uv * 0.4 - vec2(time * 0.02, time * 0.01)).rg * 2.0 - 1.0;
        vec2 distortion = (distortion1 + distortion2) * 0.5;

        // 计算焦散
        float caustic = 0.0;

        // 多层焦散
        for (int i = 0; i < 3; i++) {
          float scale = 1.0 - float(i) * 0.2;
          float intensity = 1.0 / (float(i) + 1.0);

          vec2 causticsUv = uv * scale + distortion * 0.1 * float(i + 1);
          float pattern = texture2D(normalSampler, causticsUv).r;

          // 计算焦散强度
          float causticsIntensity = smoothstep(0.4, 0.6, pattern) * intensity;
          caustic += causticsIntensity;
        }

        // 应用颜色
        vec3 causticsColor = vec3(1.0, 0.95, 0.8) * caustic * causticsIntensity;

        return causticsColor;
      }

      void main() {
        // 计算焦散
        vec3 caustics = calculateCaustics(vUv2, time);

        // 输出焦散颜色
        gl_FragColor = vec4(caustics, 1.0);
      }
    `;

    // 创建焦散材质
    this.causticsMaterial = new THREE.ShaderMaterial({
      uniforms: {
        reflectionSampler: { value: this.reflectionRenderTarget?.texture || null },
        refractionSampler: { value: this.refractionRenderTarget?.texture || null },
        normalSampler: { value: null },
        time: { value: 0 },
        causticsIntensity: { value: this.config.causticsIntensity }
      },
      vertexShader: causticsVertexShader,
      fragmentShader: causticsFragmentShader
    });
  }

  /**
   * 初始化体积光材质
   */
  private initializeVolumetricLightMaterial(): void {
    // 体积光着色器
    const volumetricLightVertexShader = `
      uniform mat4 textureMatrix;
      uniform float time;

      varying vec4 vUv;
      varying vec2 vUv2;
      varying vec3 vPosition;
      varying vec3 vNormal;

      void main() {
        vUv = textureMatrix * vec4(position, 1.0);
        vUv2 = uv;
        vPosition = position;
        vNormal = normal;

        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const volumetricLightFragmentShader = `
      uniform sampler2D reflectionSampler;
      uniform sampler2D refractionSampler;
      uniform sampler2D causticsMap;
      uniform sampler2D depthMap;
      uniform vec3 lightPosition;
      uniform vec3 lightColor;
      uniform float time;
      uniform float volumetricLightIntensity;

      varying vec4 vUv;
      varying vec2 vUv2;
      varying vec3 vPosition;
      varying vec3 vNormal;

      // 体积光计算函数
      vec3 calculateVolumetricLight(vec3 position, vec3 viewDir, vec3 lightPos, vec3 lightCol) {
        // 计算从位置到光源的方向
        vec3 lightDir = normalize(lightPos - position);

        // 计算视线方向和光线方向的夹角
        float cosAngle = max(0.0, dot(viewDir, lightDir));

        // 体积光强度随角度变化
        float intensity = pow(cosAngle, 8.0) * volumetricLightIntensity;

        // 添加随时间变化的扰动
        float noise = sin(time * 0.5 + position.y * 10.0) * 0.05 + 0.95;

        // 计算体积光颜色
        vec3 volumetricLight = lightCol * intensity * noise;

        return volumetricLight;
      }

      void main() {
        // 计算视线方向
        vec3 viewDir = normalize(-vPosition);

        // 计算体积光
        vec3 volumetricLight = calculateVolumetricLight(vPosition, viewDir, lightPosition, lightColor);

        // 获取焦散
        vec3 caustics = texture2D(causticsMap, vUv2).rgb;

        // 混合体积光和焦散
        vec3 finalColor = volumetricLight + caustics * 0.3;

        // 输出体积光颜色
        gl_FragColor = vec4(finalColor, length(finalColor) * 0.5);
      }
    `;

    // 创建体积光材质
    this.volumetricLightMaterial = new THREE.ShaderMaterial({
      uniforms: {
        reflectionSampler: { value: this.reflectionRenderTarget?.texture || null },
        refractionSampler: { value: this.refractionRenderTarget?.texture || null },
        causticsMap: { value: this.causticsRenderTarget?.texture || null },
        depthMap: { value: null },
        lightPosition: { value: new THREE.Vector3(0, 10, 0) },
        lightColor: { value: new THREE.Color(1, 1, 1) },
        time: { value: 0 },
        volumetricLightIntensity: { value: this.config.volumetricLightIntensity }
      },
      vertexShader: volumetricLightVertexShader,
      fragmentShader: volumetricLightFragmentShader,
      transparent: true,
      blending: THREE.AdditiveBlending
    });
  }

  /**
   * 初始化水下雾效材质
   */
  private initializeUnderwaterFogMaterial(): void {
    // 水下雾效着色器
    const underwaterFogVertexShader = `
      varying vec2 vUv;
      varying vec4 vWorldPosition;

      void main() {
        vUv = uv;
        vWorldPosition = modelMatrix * vec4(position, 1.0);

        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const underwaterFogFragmentShader = `
      uniform sampler2D colorTexture;
      uniform sampler2D depthTexture;
      uniform vec3 fogColor;
      uniform float fogDensity;
      uniform float underwaterFogIntensity;
      uniform float time;
      uniform vec3 cameraPosition;
      uniform mat4 projectionMatrixInverse;
      uniform mat4 viewMatrixInverse;

      varying vec2 vUv;
      varying vec4 vWorldPosition;

      // 水下雾效计算函数
      vec3 calculateUnderwaterFog(vec3 color, float depth, vec3 worldPos) {
        // 计算从相机到片段的距离
        float distance = length(worldPos - cameraPosition);

        // 计算雾效因子
        float fogFactor = 1.0 - exp(-fogDensity * distance * underwaterFogIntensity);

        // 添加随时间变化的扰动
        float noise = sin(time * 0.2 + worldPos.y * 5.0 + worldPos.x * 3.0 + worldPos.z * 4.0) * 0.05 + 0.95;
        fogFactor *= noise;

        // 混合颜色和雾效
        return mix(color, fogColor, fogFactor);
      }

      // 从深度纹理重建世界坐标
      vec3 getWorldPositionFromDepth(float depth, vec2 uv) {
        // 计算NDC坐标
        vec4 clipSpacePosition = vec4(uv * 2.0 - 1.0, depth * 2.0 - 1.0, 1.0);

        // 转换到视图空间
        vec4 viewSpacePosition = projectionMatrixInverse * clipSpacePosition;
        viewSpacePosition /= viewSpacePosition.w;

        // 转换到世界空间
        vec4 worldSpacePosition = viewMatrixInverse * viewSpacePosition;

        return worldSpacePosition.xyz;
      }

      void main() {
        // 获取原始颜色
        vec4 color = texture2D(colorTexture, vUv);

        // 获取深度
        float depth = texture2D(depthTexture, vUv).r;

        // 获取世界坐标
        vec3 worldPos = getWorldPositionFromDepth(depth, vUv);

        // 计算水下雾效
        vec3 foggedColor = calculateUnderwaterFog(color.rgb, depth, worldPos);

        // 输出最终颜色
        gl_FragColor = vec4(foggedColor, color.a);
      }
    `;

    // 创建水下雾效材质
    this.underwaterFogMaterial = new THREE.ShaderMaterial({
      uniforms: {
        colorTexture: { value: null },
        depthTexture: { value: null },
        fogColor: { value: this.config.underwaterFogColor },
        fogDensity: { value: this.config.underwaterFogDensity },
        underwaterFogIntensity: { value: this.config.underwaterFogIntensity },
        time: { value: 0 },
        cameraPosition: { value: new THREE.Vector3() },
        projectionMatrixInverse: { value: new THREE.Matrix4() },
        viewMatrixInverse: { value: new THREE.Matrix4() }
      },
      vertexShader: underwaterFogVertexShader,
      fragmentShader: underwaterFogFragmentShader
    });
  }

  /**
   * 添加水体
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  public addWaterBody(entityId: string, waterBody: WaterBodyComponent): void {
    this.waterBodies.set(entityId, waterBody);
  }

  /**
   * 移除水体
   * @param entityId 实体ID
   */
  public removeWaterBody(entityId: string): void {
    this.waterBodies.delete(entityId);
  }

  /**
   * 获取水体
   * @param entityId 实体ID
   * @returns 水体组件
   */
  public getWaterBody(entityId: string): WaterBodyComponent | undefined {
    return this.waterBodies.get(entityId);
  }

  /**
   * 获取所有水体
   * @returns 水体组件映射
   */
  public getWaterBodies(): Map<string, WaterBodyComponent> {
    return this.waterBodies;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }

    // 按照当前更新频率更新
    this.frameCount++;
    if (this.frameCount % this.currentUpdateFrequency !== 0) {
      return;
    }

    // 更新水体光照效果
    this.updateWaterLighting(deltaTime);
  }

  /**
   * 更新水体光照效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterLighting(deltaTime: number): void {
    // 获取引擎和世界
    const engine = this.getEngine();
    if (!engine) return;

    const world = engine.getWorld();
    if (!world) return;

    // 获取活跃场景
    const scene = world.getActiveScene();
    if (!scene) return;

    // 获取Three.js场景
    const threeScene = scene.getThreeScene();
    if (!threeScene) return;

    // 获取渲染器（需要从引擎获取）
    const renderSystem = engine.getSystem('RenderSystem') as any;
    if (!renderSystem) return;

    const renderer = renderSystem.getRenderer()?.getThreeRenderer();
    if (!renderer) return;

    // 获取活跃相机
    const camera = scene.getActiveCamera();
    if (!camera) return;

    // 更新时间
    if (this.causticsMaterial) {
      this.causticsMaterial.uniforms.time.value += deltaTime;
    }
    if (this.volumetricLightMaterial) {
      this.volumetricLightMaterial.uniforms.time.value += deltaTime;
    }
    if (this.underwaterFogMaterial) {
      this.underwaterFogMaterial.uniforms.time.value += deltaTime;
    }

    // 检查相机是否在水下
    this.checkCameraUnderwater(camera);

    // 遍历所有水体
    for (const [_entityId, waterBody] of this.waterBodies) {
      // 更新水体光照效果
      this.updateWaterBodyLighting(waterBody, camera, threeScene, renderer, deltaTime);
    }

    // 如果相机在水下，应用水下效果
    if (this.isUnderwater && this.currentWaterBody) {
      this.applyUnderwaterEffects(camera, threeScene, renderer, deltaTime);
    }
  }

  /**
   * 检查相机是否在水下
   * @param camera 相机
   */
  private checkCameraUnderwater(camera: any): void {
    const cameraPosition = camera.getPosition();
    let isUnderwater = false;
    let currentWaterBody = null;

    // 遍历所有水体
    for (const [_entityId, waterBody] of this.waterBodies) {
      const waterPosition = waterBody.getPosition();
      const waterSize = waterBody.getSize();

      // 检查相机是否在水体范围内
      const isInWaterX = cameraPosition.x >= waterPosition.x - waterSize.width / 2 &&
                         cameraPosition.x <= waterPosition.x + waterSize.width / 2;
      const isInWaterZ = cameraPosition.z >= waterPosition.z - waterSize.depth / 2 &&
                         cameraPosition.z <= waterPosition.z + waterSize.depth / 2;

      if (isInWaterX && isInWaterZ) {
        // 获取水面高度
        const waterHeight = waterPosition.y;

        // 检查相机是否在水面以下
        if (cameraPosition.y < waterHeight) {
          isUnderwater = true;
          currentWaterBody = waterBody;
          break;
        }
      }
    }

    // 更新状态
    this.isUnderwater = isUnderwater;
    this.currentWaterBody = currentWaterBody;
  }

  /**
   * 更新水体光照效果
   * @param waterBody 水体组件
   * @param camera 相机
   * @param scene 场景
   * @param renderer 渲染器
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterBodyLighting(
    waterBody: WaterBodyComponent,
    camera: any,
    scene: THREE.Scene,
    renderer: THREE.WebGLRenderer,
    deltaTime: number
  ): void {
    // 更新反射
    if (this.config.enableReflection && this.reflectionCamera && this.reflectionRenderTarget) {
      this.updateReflection(waterBody, camera, scene, renderer);
    }

    // 更新折射
    if (this.config.enableRefraction && this.refractionCamera && this.refractionRenderTarget) {
      this.updateRefraction(waterBody, camera, scene, renderer);
    }

    // 更新焦散
    if (this.config.enableCaustics && this.causticsMaterial && this.causticsRenderTarget) {
      this.updateCaustics(waterBody, camera, scene, renderer, deltaTime);
    }

    // 更新体积光
    if (this.config.enableVolumetricLight && this.volumetricLightMaterial && this.volumetricLightRenderTarget) {
      this.updateVolumetricLight(waterBody, camera, scene, renderer, deltaTime);
    }

    // 更新水体材质
    this.updateWaterMaterial(waterBody);
  }

  /**
   * 更新反射
   * @param waterBody 水体组件
   * @param camera 相机
   * @param scene 场景
   * @param renderer 渲染器
   */
  private updateReflection(
    waterBody: WaterBodyComponent,
    camera: any,
    scene: THREE.Scene,
    renderer: THREE.WebGLRenderer
  ): void {
    if (!this.reflectionCamera || !this.reflectionRenderTarget) return;

    // 获取水体位置和法线
    const waterPosition = waterBody.getPosition();
    const waterNormal = new THREE.Vector3(0, 1, 0);

    // 设置反射平面
    this.reflectionPlane.setFromNormalAndCoplanarPoint(waterNormal, waterPosition);

    // 设置反射相机
    const cameraPosition = camera.getPosition();
    const cameraRotation = camera.getRotation();
    const cameraFov = camera.getFov();
    const cameraAspect = camera.getAspect();
    const cameraNear = camera.getNear();
    const cameraFar = camera.getFar();

    // 复制相机属性
    this.reflectionCamera.fov = cameraFov;
    this.reflectionCamera.aspect = cameraAspect;
    this.reflectionCamera.near = cameraNear;
    this.reflectionCamera.far = cameraFar;
    this.reflectionCamera.position.copy(cameraPosition);
    this.reflectionCamera.rotation.copy(cameraRotation);
    this.reflectionCamera.updateMatrixWorld();

    // 应用反射矩阵
    const reflectionMatrix = new THREE.Matrix4();
    // Three.js 没有 makeReflection 方法，需要手动计算反射矩阵
    const normal = this.reflectionPlane.normal;
    const constant = this.reflectionPlane.constant;

    reflectionMatrix.set(
      1 - 2 * normal.x * normal.x, -2 * normal.x * normal.y, -2 * normal.x * normal.z, -2 * normal.x * constant,
      -2 * normal.y * normal.x, 1 - 2 * normal.y * normal.y, -2 * normal.y * normal.z, -2 * normal.y * constant,
      -2 * normal.z * normal.x, -2 * normal.z * normal.y, 1 - 2 * normal.z * normal.z, -2 * normal.z * constant,
      0, 0, 0, 1
    );

    this.reflectionCamera.applyMatrix4(reflectionMatrix);

    // 设置剪裁平面
    const clipPlane = new THREE.Plane();
    clipPlane.copy(this.reflectionPlane);
    clipPlane.negate();

    // 保存当前渲染目标
    const currentRenderTarget = renderer.getRenderTarget();
    const currentXrEnabled = renderer.xr.enabled;
    const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate;

    // 禁用XR和阴影自动更新
    renderer.xr.enabled = false;
    renderer.shadowMap.autoUpdate = false;

    // 设置渲染目标
    renderer.setRenderTarget(this.reflectionRenderTarget);
    renderer.clear();

    // 渲染反射
    renderer.clippingPlanes = [clipPlane];
    renderer.render(scene, this.reflectionCamera);
    renderer.clippingPlanes = [];

    // 恢复渲染目标和设置
    renderer.setRenderTarget(currentRenderTarget);
    renderer.xr.enabled = currentXrEnabled;
    renderer.shadowMap.autoUpdate = currentShadowAutoUpdate;

    // 发射反射更新事件
    this.eventEmitter.emit(WaterLightingSystemEventType.REFLECTION_UPDATED, {
      waterBody,
      reflectionTexture: this.reflectionRenderTarget.texture
    });
  }

  /**
   * 更新折射
   * @param waterBody 水体组件
   * @param camera 相机
   * @param scene 场景
   * @param renderer 渲染器
   */
  private updateRefraction(
    waterBody: WaterBodyComponent,
    camera: any,
    scene: THREE.Scene,
    renderer: THREE.WebGLRenderer
  ): void {
    if (!this.refractionCamera || !this.refractionRenderTarget) return;

    // 获取水体位置和法线
    const waterPosition = waterBody.getPosition();
    const waterNormal = new THREE.Vector3(0, 1, 0);

    // 设置折射平面
    this.refractionPlane.setFromNormalAndCoplanarPoint(waterNormal, waterPosition);

    // 设置折射相机
    const cameraPosition = camera.getPosition();
    const cameraRotation = camera.getRotation();
    const cameraFov = camera.getFov();
    const cameraAspect = camera.getAspect();
    const cameraNear = camera.getNear();
    const cameraFar = camera.getFar();

    // 复制相机属性
    this.refractionCamera.fov = cameraFov;
    this.refractionCamera.aspect = cameraAspect;
    this.refractionCamera.near = cameraNear;
    this.refractionCamera.far = cameraFar;
    this.refractionCamera.position.copy(cameraPosition);
    this.refractionCamera.rotation.copy(cameraRotation);
    this.refractionCamera.updateMatrixWorld();

    // 设置剪裁平面
    const clipPlane = new THREE.Plane();
    clipPlane.copy(this.refractionPlane);

    // 保存当前渲染目标
    const currentRenderTarget = renderer.getRenderTarget();
    const currentXrEnabled = renderer.xr.enabled;
    const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate;

    // 禁用XR和阴影自动更新
    renderer.xr.enabled = false;
    renderer.shadowMap.autoUpdate = false;

    // 设置渲染目标
    renderer.setRenderTarget(this.refractionRenderTarget);
    renderer.clear();

    // 渲染折射
    renderer.clippingPlanes = [clipPlane];
    renderer.render(scene, this.refractionCamera);
    renderer.clippingPlanes = [];

    // 恢复渲染目标和设置
    renderer.setRenderTarget(currentRenderTarget);
    renderer.xr.enabled = currentXrEnabled;
    renderer.shadowMap.autoUpdate = currentShadowAutoUpdate;

    // 发射折射更新事件
    this.eventEmitter.emit(WaterLightingSystemEventType.REFRACTION_UPDATED, {
      waterBody,
      refractionTexture: this.refractionRenderTarget.texture
    });
  }

  /**
   * 更新焦散
   * @param waterBody 水体组件
   * @param camera 相机
   * @param scene 场景
   * @param renderer 渲染器
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateCaustics(
    waterBody: WaterBodyComponent,
    _camera: any,
    _scene: THREE.Scene,
    renderer: THREE.WebGLRenderer,
    deltaTime: number
  ): void {
    if (!this.causticsMaterial || !this.causticsRenderTarget) return;

    // 获取水体法线贴图
    const normalMap = waterBody.getNormalMap();
    if (!normalMap) return;

    // 更新焦散材质
    this.causticsMaterial.uniforms.normalSampler.value = normalMap;
    this.causticsMaterial.uniforms.time.value += deltaTime;

    // 保存当前渲染目标
    const currentRenderTarget = renderer.getRenderTarget();
    const currentXrEnabled = renderer.xr.enabled;
    const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate;

    // 禁用XR和阴影自动更新
    renderer.xr.enabled = false;
    renderer.shadowMap.autoUpdate = false;

    // 设置渲染目标
    renderer.setRenderTarget(this.causticsRenderTarget);
    renderer.clear();

    // 创建一个全屏四边形
    const quad = new THREE.Mesh(
      new THREE.PlaneGeometry(2, 2),
      this.causticsMaterial
    );
    quad.frustumCulled = false;

    // 创建一个临时场景
    const tempScene = new THREE.Scene();
    tempScene.add(quad);

    // 创建一个正交相机
    const orthoCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);

    // 渲染焦散
    renderer.render(tempScene, orthoCamera);

    // 恢复渲染目标和设置
    renderer.setRenderTarget(currentRenderTarget);
    renderer.xr.enabled = currentXrEnabled;
    renderer.shadowMap.autoUpdate = currentShadowAutoUpdate;

    // 发射焦散更新事件
    this.eventEmitter.emit(WaterLightingSystemEventType.CAUSTICS_UPDATED, {
      waterBody,
      causticsTexture: this.causticsRenderTarget.texture
    });
  }

  /**
   * 更新体积光
   * @param waterBody 水体组件
   * @param camera 相机
   * @param scene 场景
   * @param renderer 渲染器
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateVolumetricLight(
    waterBody: WaterBodyComponent,
    camera: any,
    scene: THREE.Scene,
    renderer: THREE.WebGLRenderer,
    deltaTime: number
  ): void {
    if (!this.volumetricLightMaterial || !this.volumetricLightRenderTarget) return;

    // 获取场景中的光源
    const lights = scene.children.filter(child =>
      child instanceof THREE.DirectionalLight ||
      child instanceof THREE.PointLight ||
      child instanceof THREE.SpotLight
    );

    if (lights.length === 0) return;

    // 使用第一个光源
    const light = lights[0];
    const lightPosition = new THREE.Vector3();
    light.getWorldPosition(lightPosition);
    const lightColor = (light as THREE.Light).color;

    // 更新体积光材质
    this.volumetricLightMaterial.uniforms.lightPosition.value = lightPosition;
    this.volumetricLightMaterial.uniforms.lightColor.value = lightColor;
    this.volumetricLightMaterial.uniforms.time.value += deltaTime;
    this.volumetricLightMaterial.uniforms.causticsMap.value = this.causticsRenderTarget?.texture || null;

    // 保存当前渲染目标
    const currentRenderTarget = renderer.getRenderTarget();
    const currentXrEnabled = renderer.xr.enabled;
    const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate;

    // 禁用XR和阴影自动更新
    renderer.xr.enabled = false;
    renderer.shadowMap.autoUpdate = false;

    // 设置渲染目标
    renderer.setRenderTarget(this.volumetricLightRenderTarget);
    renderer.clear();

    // 创建一个体积光网格
    const waterPosition = waterBody.getPosition();
    const waterSize = waterBody.getSize();
    const volumeGeometry = new THREE.BoxGeometry(waterSize.width, waterSize.height, waterSize.depth);
    const volumeMesh = new THREE.Mesh(volumeGeometry, this.volumetricLightMaterial);
    volumeMesh.position.copy(waterPosition);

    // 创建一个临时场景
    const tempScene = new THREE.Scene();
    tempScene.add(volumeMesh);

    // 渲染体积光
    renderer.render(tempScene, camera.getThreeJSCamera());

    // 恢复渲染目标和设置
    renderer.setRenderTarget(currentRenderTarget);
    renderer.xr.enabled = currentXrEnabled;
    renderer.shadowMap.autoUpdate = currentShadowAutoUpdate;

    // 发射体积光更新事件
    this.eventEmitter.emit(WaterLightingSystemEventType.VOLUMETRIC_LIGHT_UPDATED, {
      waterBody,
      volumetricLightTexture: this.volumetricLightRenderTarget.texture
    });
  }

  /**
   * 更新水体材质
   * @param waterBody 水体组件
   */
  private updateWaterMaterial(waterBody: WaterBodyComponent): void {
    // 获取水体材质（通过实体获取渲染组件）
    const entity = waterBody.getEntity();
    if (!entity) return;

    const renderComponent = entity.getComponent('RenderComponent') as any;
    if (!renderComponent) return;

    const mesh = renderComponent.getMesh?.();
    if (!mesh || !mesh.material) return;

    const material = mesh.material as any;
    if (!material.uniforms) return;

    // 更新材质贴图
    if (this.reflectionRenderTarget && material.uniforms.reflectionSampler) {
      material.uniforms.reflectionSampler.value = this.reflectionRenderTarget.texture;
    }
    if (this.refractionRenderTarget && material.uniforms.refractionSampler) {
      material.uniforms.refractionSampler.value = this.refractionRenderTarget.texture;
    }
    if (this.causticsRenderTarget && material.uniforms.causticsMap) {
      material.uniforms.causticsMap.value = this.causticsRenderTarget.texture;
    }
    if (this.volumetricLightRenderTarget && material.uniforms.volumetricLightMap) {
      material.uniforms.volumetricLightMap.value = this.volumetricLightRenderTarget.texture;
    }
  }

  /**
   * 应用水下效果
   * @param camera 相机
   * @param scene 场景
   * @param renderer 渲染器
   * @param deltaTime 帧间隔时间（秒）
   */
  private applyUnderwaterEffects(
    camera: any,
    _scene: THREE.Scene,
    renderer: THREE.WebGLRenderer,
    deltaTime: number
  ): void {
    if (!this.underwaterFogMaterial || !this.currentWaterBody) return;

    // 更新水下雾效材质
    this.underwaterFogMaterial.uniforms.time.value += deltaTime;
    this.underwaterFogMaterial.uniforms.cameraPosition.value = camera.getPosition();
    this.underwaterFogMaterial.uniforms.projectionMatrixInverse.value = camera.getThreeJSCamera().projectionMatrixInverse;
    this.underwaterFogMaterial.uniforms.viewMatrixInverse.value = camera.getThreeJSCamera().matrixWorld;

    // 获取渲染目标
    const renderTarget = renderer.getRenderTarget();
    if (!renderTarget) return;

    // 设置颜色和深度纹理
    this.underwaterFogMaterial.uniforms.colorTexture.value = renderTarget.texture;
    this.underwaterFogMaterial.uniforms.depthTexture.value = renderTarget.depthTexture;

    // 创建一个全屏四边形
    const quad = new THREE.Mesh(
      new THREE.PlaneGeometry(2, 2),
      this.underwaterFogMaterial
    );
    quad.frustumCulled = false;

    // 创建一个临时场景
    const tempScene = new THREE.Scene();
    tempScene.add(quad);

    // 创建一个正交相机
    const orthoCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);

    // 保存当前渲染目标
    const currentRenderTarget = renderer.getRenderTarget();
    const currentXrEnabled = renderer.xr.enabled;
    const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate;

    // 禁用XR和阴影自动更新
    renderer.xr.enabled = false;
    renderer.shadowMap.autoUpdate = false;

    // 渲染水下雾效
    renderer.setRenderTarget(null);
    renderer.render(tempScene, orthoCamera);

    // 恢复渲染目标和设置
    renderer.setRenderTarget(currentRenderTarget);
    renderer.xr.enabled = currentXrEnabled;
    renderer.shadowMap.autoUpdate = currentShadowAutoUpdate;

    // 发射水下雾效更新事件
    this.eventEmitter.emit(WaterLightingSystemEventType.UNDERWATER_FOG_UPDATED, {
      waterBody: this.currentWaterBody
    });
  }

  /**
   * 注册事件监听器
   * @param eventType 事件类型
   * @param callback 回调函数
   */
  public addEventListener(eventType: WaterLightingSystemEventType, callback: (data: any) => void): void {
    this.eventEmitter.on(eventType, callback);
  }

  /**
   * 移除事件监听器
   * @param eventType 事件类型
   * @param callback 回调函数
   */
  public removeEventListener(eventType: WaterLightingSystemEventType, callback: (data: any) => void): void {
    this.eventEmitter.off(eventType, callback);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 销毁渲染目标
    if (this.reflectionRenderTarget) {
      (this.reflectionRenderTarget as any).dispose();
      this.reflectionRenderTarget = null;
    }
    if (this.refractionRenderTarget) {
      (this.refractionRenderTarget as any).dispose();
      this.refractionRenderTarget = null;
    }
    if (this.causticsRenderTarget) {
      (this.causticsRenderTarget as any).dispose();
      this.causticsRenderTarget = null;
    }
    if (this.volumetricLightRenderTarget) {
      (this.volumetricLightRenderTarget as any).dispose();
      this.volumetricLightRenderTarget = null;
    }

    // 销毁材质
    if (this.causticsMaterial) {
      (this.causticsMaterial as any).dispose();
      this.causticsMaterial = null;
    }
    if (this.volumetricLightMaterial) {
      (this.volumetricLightMaterial as any).dispose();
      this.volumetricLightMaterial = null;
    }
    if (this.underwaterFogMaterial) {
      (this.underwaterFogMaterial as any).dispose();
      this.underwaterFogMaterial = null;
    }

    // 清除事件监听器
    this.eventEmitter.removeAllListeners?.();

    // 清除水体
    this.waterBodies.clear();
  }
}
