/**
 * 协作管理器
 * 负责管理协作会话、WebSocket连接和消息处理
 */
export class CollaborationManager {
  /**
   * 构造函数
   * @param {Object} options 选项
   * @param {UserManager} options.userManager 用户管理器
   * @param {OperationManager} options.operationManager 操作管理器
   * @param {ConflictResolver} options.conflictResolver 冲突解决器
   * @param {PermissionManager} options.permissionManager 权限管理器
   * @param {HistoryManager} options.historyManager 历史管理器
   * @param {ChatManager} options.chatManager 聊天管理器
   */
  constructor(options) {
    this.userManager = options.userManager;
    this.operationManager = options.operationManager;
    this.conflictResolver = options.conflictResolver;
    this.permissionManager = options.permissionManager;
    this.historyManager = options.historyManager;
    this.chatManager = options.chatManager;
    
    this.socket = null;
    this.connected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.currentUser = null;
    
    // 绑定方法
    this.onSocketOpen = this.onSocketOpen.bind(this);
    this.onSocketMessage = this.onSocketMessage.bind(this);
    this.onSocketClose = this.onSocketClose.bind(this);
    this.onSocketError = this.onSocketError.bind(this);
    
    // 设置事件监听器
    this.operationManager.on('operation', this.sendOperation.bind(this));
    this.chatManager.on('message', this.sendChatMessage.bind(this));
  }
  
  /**
   * 连接到协作会话
   * @param {Object} user 当前用户
   * @returns {Promise<void>}
   */
  async connect(user) {
    this.currentUser = user;
    
    try {
      // 在实际应用中，这里应该连接到真实的WebSocket服务器
      // 这里使用模拟的WebSocket连接
      this.socket = this.createMockWebSocket();
      
      // 设置连接状态
      this.connected = true;
      this.reconnectAttempts = 0;
      
      // 更新UI
      this.updateConnectionStatus('已连接');
      
      // 通知用户管理器
      this.userManager.setCurrentUser(user);
      
      // 加载模拟数据
      this.loadMockData();
      
      return true;
    } catch (error) {
      console.error('连接失败:', error);
      this.updateConnectionStatus('连接失败');
      throw error;
    }
  }
  
  /**
   * 断开协作会话
   * @returns {Promise<void>}
   */
  async disconnect() {
    if (this.socket) {
      // 发送用户断开连接消息
      if (this.connected && this.currentUser) {
        this.sendMessage({
          type: 'USER_DISCONNECTED',
          data: {
            userId: this.currentUser.id
          }
        });
      }
      
      // 关闭WebSocket连接
      this.socket.close();
      this.socket = null;
    }
    
    // 设置连接状态
    this.connected = false;
    this.currentUser = null;
    
    // 更新UI
    this.updateConnectionStatus('未连接');
    
    // 通知用户管理器
    this.userManager.clearCurrentUser();
    
    return true;
  }
  
  /**
   * 发送操作
   * @param {Object} operation 操作对象
   */
  sendOperation(operation) {
    if (!this.connected || !this.currentUser) {
      console.warn('未连接到协作会话，无法发送操作');
      return;
    }
    
    // 检查权限
    if (!this.permissionManager.canPerformOperation(this.currentUser, operation)) {
      console.warn('没有权限执行此操作:', operation);
      return;
    }
    
    // 发送操作消息
    this.sendMessage({
      type: 'OPERATION',
      data: {
        userId: this.currentUser.id,
        operation: operation
      }
    });
  }
  
  /**
   * 发送聊天消息
   * @param {Object} user 用户
   * @param {string} message 消息内容
   */
  sendChatMessage(user, message) {
    if (!this.connected || !this.currentUser) {
      console.warn('未连接到协作会话，无法发送聊天消息');
      return;
    }
    
    // 发送聊天消息
    this.sendMessage({
      type: 'CHAT_MESSAGE',
      data: {
        userId: user.id,
        message: message,
        timestamp: Date.now()
      }
    });
  }
  
  /**
   * 发送鼠标位置
   * @param {number} x X坐标
   * @param {number} y Y坐标
   */
  sendMousePosition(x, y) {
    if (!this.connected || !this.currentUser) {
      return;
    }
    
    // 发送鼠标位置消息
    this.sendMessage({
      type: 'MOUSE_POSITION',
      data: {
        userId: this.currentUser.id,
        position: { x, y }
      }
    });
  }
  
  /**
   * 发送消息
   * @param {Object} message 消息对象
   */
  sendMessage(message) {
    if (!this.socket || !this.connected) {
      console.warn('未连接到协作会话，无法发送消息');
      return;
    }
    
    try {
      this.socket.send(JSON.stringify(message));
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  }
  
  /**
   * WebSocket连接打开事件处理
   * @param {Event} event 事件对象
   */
  onSocketOpen(event) {
    console.log('WebSocket连接已建立');
    this.connected = true;
    this.reconnectAttempts = 0;
    this.updateConnectionStatus('已连接');
    
    // 发送用户连接消息
    if (this.currentUser) {
      this.sendMessage({
        type: 'USER_CONNECTED',
        data: {
          user: this.currentUser
        }
      });
    }
  }
  
  /**
   * WebSocket消息事件处理
   * @param {MessageEvent} event 事件对象
   */
  onSocketMessage(event) {
    try {
      const message = JSON.parse(event.data);
      
      switch (message.type) {
        case 'USER_CONNECTED':
          this.handleUserConnected(message.data);
          break;
        case 'USER_DISCONNECTED':
          this.handleUserDisconnected(message.data);
          break;
        case 'OPERATION':
          this.handleOperation(message.data);
          break;
        case 'CHAT_MESSAGE':
          this.handleChatMessage(message.data);
          break;
        case 'MOUSE_POSITION':
          this.handleMousePosition(message.data);
          break;
        case 'CONFLICT_DETECTED':
          this.handleConflictDetected(message.data);
          break;
        default:
          console.log('未知消息类型:', message.type);
      }
    } catch (error) {
      console.error('处理消息失败:', error);
    }
  }
  
  /**
   * WebSocket连接关闭事件处理
   * @param {CloseEvent} event 事件对象
   */
  onSocketClose(event) {
    console.log('WebSocket连接已关闭:', event.code, event.reason);
    this.connected = false;
    this.updateConnectionStatus('已断开');
    
    // 尝试重新连接
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      this.updateConnectionStatus(`正在重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        if (this.currentUser) {
          this.connect(this.currentUser).catch(error => {
            console.error('重新连接失败:', error);
          });
        }
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }
  
  /**
   * WebSocket错误事件处理
   * @param {Event} event 事件对象
   */
  onSocketError(event) {
    console.error('WebSocket错误:', event);
    this.updateConnectionStatus('连接错误');
  }
  
  /**
   * 处理用户连接消息
   * @param {Object} data 消息数据
   */
  handleUserConnected(data) {
    const user = data.user;
    this.userManager.addUser(user);
    
    // 更新UI
    this.updateOnlineUsersCount();
    
    // 显示通知
    this.showNotification(`${user.username} 已加入协作会话`);
  }
  
  /**
   * 处理用户断开连接消息
   * @param {Object} data 消息数据
   */
  handleUserDisconnected(data) {
    const userId = data.userId;
    const user = this.userManager.getUserById(userId);
    
    if (user) {
      this.userManager.removeUser(userId);
      
      // 更新UI
      this.updateOnlineUsersCount();
      
      // 显示通知
      this.showNotification(`${user.username} 已离开协作会话`);
    }
  }
  
  /**
   * 处理操作消息
   * @param {Object} data 消息数据
   */
  handleOperation(data) {
    const userId = data.userId;
    const operation = data.operation;
    
    // 如果是当前用户的操作，忽略
    if (this.currentUser && userId === this.currentUser.id) {
      return;
    }
    
    // 应用操作
    this.operationManager.applyRemoteOperation(userId, operation);
    
    // 添加到历史记录
    this.historyManager.addOperation(userId, operation);
  }
  
  /**
   * 处理聊天消息
   * @param {Object} data 消息数据
   */
  handleChatMessage(data) {
    const userId = data.userId;
    const message = data.message;
    const timestamp = data.timestamp;
    
    const user = this.userManager.getUserById(userId);
    
    if (user) {
      this.chatManager.receiveMessage(user, message, timestamp);
    }
  }
  
  /**
   * 处理鼠标位置消息
   * @param {Object} data 消息数据
   */
  handleMousePosition(data) {
    const userId = data.userId;
    const position = data.position;
    
    // 如果是当前用户的鼠标位置，忽略
    if (this.currentUser && userId === this.currentUser.id) {
      return;
    }
    
    const user = this.userManager.getUserById(userId);
    
    if (user) {
      this.userManager.updateUserCursor(userId, position);
    }
  }
  
  /**
   * 处理冲突检测消息
   * @param {Object} data 消息数据
   */
  handleConflictDetected(data) {
    const conflict = data.conflict;
    this.conflictResolver.handleConflict(conflict);
  }
  
  /**
   * 更新连接状态UI
   * @param {string} status 状态文本
   */
  updateConnectionStatus(status) {
    const statusElement = document.getElementById('connection-status');
    if (statusElement) {
      statusElement.textContent = status;
    }
  }
  
  /**
   * 更新在线用户数量UI
   */
  updateOnlineUsersCount() {
    const countElement = document.getElementById('online-users-count');
    if (countElement) {
      const count = this.userManager.getOnlineUsersCount();
      countElement.textContent = `在线用户: ${count}`;
    }
  }
  
  /**
   * 显示通知
   * @param {string} message 通知消息
   */
  showNotification(message) {
    // 在实际应用中，这里应该显示一个通知
    console.log('通知:', message);
  }
  
  /**
   * 创建模拟WebSocket
   * @returns {Object} 模拟的WebSocket对象
   */
  createMockWebSocket() {
    // 创建一个模拟的WebSocket对象
    const mockSocket = {
      send: (data) => {
        // 模拟发送消息
        console.log('发送消息:', JSON.parse(data));
        
        // 模拟接收消息
        setTimeout(() => {
          this.mockReceiveMessage(JSON.parse(data));
        }, 100);
      },
      close: () => {
        // 模拟关闭连接
        console.log('关闭连接');
      }
    };
    
    // 模拟连接成功
    setTimeout(() => {
      this.onSocketOpen({ target: mockSocket });
    }, 500);
    
    return mockSocket;
  }
  
  /**
   * 模拟接收消息
   * @param {Object} sentMessage 发送的消息
   */
  mockReceiveMessage(sentMessage) {
    // 根据发送的消息类型模拟接收消息
    switch (sentMessage.type) {
      case 'USER_CONNECTED':
        // 不需要处理，因为用户已经在connect方法中添加了
        break;
      case 'USER_DISCONNECTED':
        // 不需要处理，因为用户已经在disconnect方法中移除了
        break;
      case 'OPERATION':
        // 不需要处理，因为操作已经在sendOperation方法中应用了
        break;
      case 'CHAT_MESSAGE':
        // 模拟接收聊天消息
        this.onSocketMessage({
          data: JSON.stringify(sentMessage)
        });
        break;
      case 'MOUSE_POSITION':
        // 不需要处理，因为鼠标位置已经在sendMousePosition方法中更新了
        break;
    }
  }
  
  /**
   * 加载模拟数据
   */
  loadMockData() {
    // 模拟其他用户
    const mockUsers = [
      {
        id: 'user2',
        username: '张三',
        role: 'admin',
        avatar: 'assets/images/avatar2.png'
      },
      {
        id: 'user3',
        username: '李四',
        role: 'editor',
        avatar: 'assets/images/avatar3.png'
      },
      {
        id: 'user4',
        username: '王五',
        role: 'viewer',
        avatar: 'assets/images/avatar4.png'
      }
    ];
    
    // 添加模拟用户
    mockUsers.forEach(user => {
      this.userManager.addUser(user);
    });
    
    // 更新UI
    this.updateOnlineUsersCount();
    
    // 模拟聊天消息
    setTimeout(() => {
      this.handleChatMessage({
        userId: 'user2',
        message: '大家好，我是张三',
        timestamp: Date.now()
      });
    }, 2000);
    
    setTimeout(() => {
      this.handleChatMessage({
        userId: 'user3',
        message: '你好，张三，我是李四',
        timestamp: Date.now()
      });
    }, 4000);
  }
}
