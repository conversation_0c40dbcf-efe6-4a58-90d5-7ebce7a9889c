/**
 * 地形连续距离相关LOD（CDLOD）系统
 * 实现基于连续距离的LOD算法，提供平滑的LOD过渡
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import type { Camera } from '../../rendering/Camera';
import { TerrainComponent } from '../components/TerrainComponent';
import { TerrainChunk } from '../TerrainChunkSystem';
/**
 * 地形CDLOD配置接口
 */
export interface TerrainCDLODOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否使用视锥体剔除 */
    useFrustumCulling?: boolean;
    /** 四叉树最大深度 */
    maxQuadTreeDepth?: number;
    /** 基础块大小 */
    baseChunkSize?: number;
    /** 过渡区域大小比例 */
    morphRegionRatio?: number;
    /** LOD级别距离因子 */
    lodDistanceFactor?: number;
    /** 是否使用GPU变形 */
    useGPUMorphing?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 四叉树节点接口
 */
export interface QuadTreeNode {
    /** 节点ID */
    id: string;
    /** 节点级别 */
    level: number;
    /** 节点边界 */
    bounds: THREE.Box2;
    /** 节点中心 */
    center: THREE.Vector2;
    /** 节点大小 */
    size: number;
    /** 子节点 */
    children: QuadTreeNode[] | null;
    /** 是否可见 */
    visible: boolean;
    /** 是否在过渡区域 */
    inMorphRegion: boolean;
    /** 过渡因子 */
    morphFactor: number;
    /** 地形块 */
    chunk: TerrainChunk | null;
}
/**
 * CDLOD事件类型
 */
export declare enum CDLODEventType {
    /** 节点可见性变更 */
    NODE_VISIBILITY_CHANGED = "node_visibility_changed",
    /** 节点过渡因子变更 */
    NODE_MORPH_FACTOR_CHANGED = "node_morph_factor_changed",
    /** 四叉树更新 */
    QUADTREE_UPDATED = "quadtree_updated"
}
/**
 * 地形CDLOD系统
 */
export declare class TerrainCDLOD {
    /** 是否启用 */
    private enabled;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 是否使用视锥体剔除 */
    private useFrustumCulling;
    /** 四叉树最大深度 */
    private maxQuadTreeDepth;
    /** 基础块大小 */
    private baseChunkSize;
    /** 过渡区域大小比例 */
    private morphRegionRatio;
    /** LOD级别距离因子 */
    private lodDistanceFactor;
    /** 是否使用GPU变形 */
    private useGPUMorphing;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 地形实体映射 */
    private terrainEntities;
    /** 四叉树根节点映射 */
    private quadTrees;
    /** 视锥体 */
    private frustum;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 调试网格 */
    private debugMeshes;
    /** 实体管理器 */
    private entityManager;
    /** 活跃相机 */
    private activeCamera;
    /** 活跃场景 */
    private activeScene;
    /**
     * 创建地形CDLOD系统
     * @param options 配置选项
     */
    constructor(options?: TerrainCDLODOptions);
    /**
     * 添加地形实体
     * @param entity 实体
     * @param component 地形组件
     */
    addTerrainEntity(entity: Entity, component: TerrainComponent): void;
    /**
     * 移除地形实体
     * @param entity 实体
     */
    removeTerrainEntity(entity: Entity): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 更新视锥体
     * @param camera 相机
     */
    private updateFrustum;
    /**
     * 更新四叉树
     * @param camera 相机
     */
    private updateQuadTrees;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 可视化四叉树节点
     * @param node 节点
     * @param scene 场景
     * @param visibleMaterial 可见材质
     * @param hiddenMaterial 隐藏材质
     * @param morphMaterial 过渡材质
     */
    private visualizeQuadTreeNode;
    /**
     * 清除调试网格
     */
    private clearDebugMeshes;
    /**
     * 获取场景
     * @returns 场景
     */
    private getScene;
    /**
     * 注册事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    on(event: CDLODEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    off(event: CDLODEventType, listener: (...args: any[]) => void): void;
    /**
     * 设置实体管理器
     * @param entityManager 实体管理器
     */
    setEntityManager(entityManager: any): void;
    /**
     * 设置活跃相机
     * @param camera 相机
     */
    setActiveCamera(camera: Camera): void;
    /**
     * 设置活跃场景
     * @param scene 场景
     */
    setActiveScene(scene: any): void;
    /**
     * 更新四叉树节点
     * @param node 节点
     * @param cameraPosition 相机位置
     * @returns 是否应该渲染该节点
     */
    private updateQuadTreeNode;
    /**
     * 设置节点可见性
     * @param node 节点
     * @param visible 是否可见
     */
    private setNodeVisible;
    /**
     * 设置节点过渡因子
     * @param node 节点
     * @param factor 过渡因子
     */
    private setNodeMorphFactor;
    /**
     * 为地形创建四叉树
     * @param entity 实体
     * @param component 地形组件
     */
    private createQuadTreeForTerrain;
    /**
     * 递归构建四叉树
     * @param node 当前节点
     * @param entity 实体
     * @param component 地形组件
     * @param level 当前级别
     */
    private buildQuadTree;
}
