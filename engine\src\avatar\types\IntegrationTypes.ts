/**
 * 数字人创建数据类型定义
 * 支持M4阶段的系统集成功能
 */

/**
 * 集成配置
 */
export interface IntegrationConfig {
  enablePerformanceMonitoring: boolean;
  enableAutoOptimization: boolean;
  enableCloudSync: boolean;
  maxConcurrentDigitalHumans: number;
  performanceThresholds: {
    maxMemoryUsage: number; // MB
    minFPS: number;
    maxCPUUsage: number; // %
  };
  storageConfig: {
    endpoint: string;
    accessKey: string;
    secretKey: string;
    bucket: string;
  };
  aiConfig: {
    apiKey: string;
    endpoint: string;
    model: string;
  };
}

/**
 * 数字人创建类型
 */
export type DigitalHumanCreationType = 'photo' | 'upload' | 'marketplace' | 'bip_skeleton';

/**
 * 照片创建数据
 */
export interface PhotoCreationData {
  photo: File | ImageData;
  userId: string;
  name?: string;
  options?: {
    quality: 'low' | 'medium' | 'high';
    generateBody: boolean;
    autoRigging: boolean;
  };
}

/**
 * 文件创建数据
 */
export interface FileCreationData {
  file: File;
  userId: string;
  name?: string;
  overrideCompatibility?: boolean;
}

/**
 * BIP创建数据
 */
export interface BIPCreationData {
  bipFile: File;
  additionalBipFiles?: File[];
  targetDigitalHuman: any; // DigitalHumanComponent
  userId: string;
  name?: string;
  options?: {
    autoMapping: boolean;
    resolveConflicts: boolean;
    generateMissingBones: boolean;
  };
}

/**
 * 数字人创建数据联合类型
 */
export interface DigitalHumanCreationData {
  type: DigitalHumanCreationType;
  userId: string;
  name?: string;
  photoData?: PhotoCreationData;
  fileData?: FileCreationData;
  marketplaceId?: string;
  bipData?: BIPCreationData;
}

/**
 * 验证结果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 兼容性检查结果
 */
export interface CompatibilityResult {
  hasErrors: boolean;
  errors: string[];
  warnings: string[];
  missingFeatures?: string[];
  versionMismatch?: {
    required: string;
    current: string;
  };
}

/**
 * 动作冲突类型
 */
export interface ActionConflict {
  type: 'name_collision' | 'bone_conflict' | 'timing_conflict';
  animation1: any; // AnimationClip
  animation2: any; // AnimationClip
  conflictBones?: string[];
  description: string;
}

/**
 * 冲突解决方案
 */
export interface ConflictResolution {
  type: 'rename' | 'bone_remapping' | 'timing_adjustment' | 'merge';
  originalName?: string;
  newName?: string;
  conflictBones?: string[];
  remapping?: BoneRemapping[];
  reason: string;
}

/**
 * 骨骼重映射
 */
export interface BoneRemapping {
  sourceBone: string;
  targetBone: string;
  confidence: number;
}

/**
 * 性能影响评估
 */
export interface PerformanceImpact {
  memoryDelta: number; // MB
  renderingImpact: number; // 0-1, 渲染复杂度影响
  cpuImpact?: number; // 0-1, CPU使用影响
  networkImpact?: number; // KB, 网络传输影响
}

/**
 * 系统健康检查结果
 */
export interface SystemHealthCheck {
  overall: 'healthy' | 'warning' | 'error';
  components: {
    storage: 'healthy' | 'warning' | 'error';
    ai: 'healthy' | 'warning' | 'error';
    bip: 'healthy' | 'warning' | 'error';
    marketplace: 'healthy' | 'warning' | 'error';
    versioning: 'healthy' | 'warning' | 'error';
  };
  issues: HealthIssue[];
}

/**
 * 健康问题
 */
export interface HealthIssue {
  component: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  suggestion?: string;
  timestamp: number;
}

/**
 * 系统优化建议
 */
export interface OptimizationSuggestion {
  type: 'memory' | 'performance' | 'quality' | 'network';
  priority: 'low' | 'medium' | 'high';
  description: string;
  action: string;
  estimatedImpact: {
    performance?: number; // 0-1
    memory?: number; // MB
    quality?: number; // 0-1
  };
}

/**
 * 集成结果
 */
export interface IntegrationResult {
  success: boolean;
  digitalHumanId?: string;
  warnings?: string[];
  errors?: string[];
  performanceImpact?: {
    memoryDelta: number;
    renderingImpact: number;
  };
}

/**
 * 批量操作结果
 */
export interface BatchOperationResult<T> {
  totalCount: number;
  successCount: number;
  failureCount: number;
  results: Array<{
    id: string;
    success: boolean;
    result?: T;
    error?: string;
  }>;
  summary: {
    duration: number; // ms
    averageTime: number; // ms per item
    errors: string[];
    warnings: string[];
  };
}

/**
 * 数字人导出选项
 */
export interface ExportOptions {
  format: 'dhp' | 'gltf' | 'fbx' | 'obj';
  includeAnimations: boolean;
  includeTextures: boolean;
  compressionLevel: 'none' | 'low' | 'medium' | 'high';
  optimizeForWeb: boolean;
  targetPlatform?: 'web' | 'mobile' | 'desktop' | 'vr';
}

/**
 * 导出结果
 */
export interface ExportResult {
  success: boolean;
  filePath?: string;
  downloadUrl?: string;
  fileSize: number;
  format: string;
  warnings?: string[];
  errors?: string[];
  metadata: {
    exportTime: number;
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
  };
}

/**
 * 云同步状态
 */
export interface CloudSyncStatus {
  isEnabled: boolean;
  lastSyncTime?: number;
  syncInProgress: boolean;
  pendingUploads: number;
  pendingDownloads: number;
  errors: string[];
  totalSynced: number;
  totalSize: number; // bytes
}

/**
 * 系统统计信息
 */
export interface SystemStatistics {
  totalDigitalHumans: number;
  activeDigitalHumans: number;
  totalMemoryUsage: number; // MB
  averageRenderTime: number; // ms
  totalStorageUsed: number; // MB
  networkTraffic: {
    uploaded: number; // MB
    downloaded: number; // MB
  };
  userActivity: {
    activeUsers: number;
    totalSessions: number;
    averageSessionDuration: number; // minutes
  };
  systemUptime: number; // seconds
}

/**
 * 自动优化配置
 */
export interface AutoOptimizationConfig {
  enabled: boolean;
  triggers: {
    memoryThreshold: number; // MB
    fpsThreshold: number;
    cpuThreshold: number; // %
  };
  actions: {
    enableLOD: boolean;
    reduceTextureQuality: boolean;
    limitConcurrentAnimations: boolean;
    enableBatching: boolean;
    cleanupInactive: boolean;
  };
  schedule: {
    interval: number; // seconds
    maintenanceWindow?: {
      start: string; // HH:MM
      end: string; // HH:MM
    };
  };
}

/**
 * 质量设置
 */
export interface QualitySettings {
  rendering: {
    resolution: 'low' | 'medium' | 'high' | 'ultra';
    antialiasing: boolean;
    shadows: 'off' | 'low' | 'medium' | 'high';
    reflections: boolean;
    postProcessing: boolean;
  };
  textures: {
    maxSize: number; // pixels
    compression: 'none' | 'low' | 'medium' | 'high';
    mipmaps: boolean;
  };
  animations: {
    maxFPS: number;
    interpolation: 'linear' | 'cubic';
    compression: boolean;
  };
  physics: {
    enabled: boolean;
    precision: 'low' | 'medium' | 'high';
    maxObjects: number;
  };
}

/**
 * 系统事件类型
 */
export type SystemEventType = 
  | 'systemInitialized'
  | 'digitalHumanCreated'
  | 'digitalHumanLoaded'
  | 'digitalHumanUnloaded'
  | 'performanceWarning'
  | 'healthStatusChanged'
  | 'configUpdated'
  | 'syncCompleted'
  | 'optimizationApplied'
  | 'errorOccurred';

/**
 * 系统事件数据
 */
export interface SystemEventData {
  type: SystemEventType;
  timestamp: number;
  data: any;
  source: string;
  severity?: 'info' | 'warning' | 'error';
}
