/**
 * 状态机编辑器
 * 负责创建和编辑角色动画状态机
 */
export class StateMachineEditor {
  /**
   * 构造函数
   */
  constructor() {
    this.character = null;
    this.animationManager = null;
    this.stateMachine = null;
    this.selectedState = null;
    this.selectedTransition = null;
    
    // 编辑模式
    this.editMode = 'states'; // 'states', 'transitions', 'parameters'
    
    // 画布设置
    this.canvasElement = null;
    this.canvasContext = null;
    this.canvasWidth = 800;
    this.canvasHeight = 600;
    this.gridSize = 20;
    this.nodeWidth = 150;
    this.nodeHeight = 40;
    this.isDragging = false;
    this.dragStartX = 0;
    this.dragStartY = 0;
    this.draggedNode = null;
    this.isCreatingTransition = false;
    this.transitionStartState = null;
    this.transitionEndState = null;
  }
  
  /**
   * 初始化
   * @param {Object} character 角色对象
   * @param {AnimationManager} animationManager 动画管理器
   */
  initialize(character, animationManager) {
    this.character = character;
    this.animationManager = animationManager;
    
    // 创建默认状态机
    this.createDefaultStateMachine();
    
    // 初始化画布
    this.initCanvas();
  }
  
  /**
   * 创建默认状态机
   */
  createDefaultStateMachine() {
    this.stateMachine = {
      name: '角色状态机',
      states: [
        {
          id: 'idle',
          name: '待机',
          isDefault: true,
          position: { x: 100, y: 100 },
          animation: null,
          behaviors: []
        }
      ],
      transitions: [],
      parameters: [
        {
          id: 'speed',
          name: '速度',
          type: 'float',
          defaultValue: 0
        },
        {
          id: 'isJumping',
          name: '跳跃中',
          type: 'bool',
          defaultValue: false
        }
      ]
    };
  }
  
  /**
   * 初始化画布
   */
  initCanvas() {
    // 创建画布元素
    this.canvasElement = document.createElement('canvas');
    this.canvasElement.width = this.canvasWidth;
    this.canvasElement.height = this.canvasHeight;
    this.canvasElement.className = 'state-machine-canvas';
    
    // 获取绘图上下文
    this.canvasContext = this.canvasElement.getContext('2d');
    
    // 设置事件监听
    this.canvasElement.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.canvasElement.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.canvasElement.addEventListener('mouseup', this.handleMouseUp.bind(this));
    this.canvasElement.addEventListener('dblclick', this.handleDoubleClick.bind(this));
    
    // 绘制状态机
    this.drawStateMachine();
  }
  
  /**
   * 处理鼠标按下事件
   * @param {MouseEvent} event 鼠标事件
   */
  handleMouseDown(event) {
    const rect = this.canvasElement.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // 检查是否点击了状态节点
    const clickedState = this.findStateAtPosition(x, y);
    
    if (clickedState) {
      // 选中状态
      this.selectedState = clickedState;
      this.selectedTransition = null;
      
      // 开始拖动
      this.isDragging = true;
      this.dragStartX = x;
      this.dragStartY = y;
      this.draggedNode = clickedState;
      
      // 如果正在创建过渡
      if (this.isCreatingTransition) {
        if (this.transitionStartState) {
          // 设置过渡结束状态
          this.transitionEndState = clickedState;
          
          // 创建过渡
          this.createTransition(this.transitionStartState, this.transitionEndState);
          
          // 重置创建过渡状态
          this.isCreatingTransition = false;
          this.transitionStartState = null;
          this.transitionEndState = null;
        }
      } else if (event.ctrlKey) {
        // Ctrl+点击开始创建过渡
        this.isCreatingTransition = true;
        this.transitionStartState = clickedState;
      }
    } else {
      // 检查是否点击了过渡
      const clickedTransition = this.findTransitionAtPosition(x, y);
      
      if (clickedTransition) {
        // 选中过渡
        this.selectedState = null;
        this.selectedTransition = clickedTransition;
      } else {
        // 取消选中
        this.selectedState = null;
        this.selectedTransition = null;
      }
    }
    
    // 重绘状态机
    this.drawStateMachine();
  }
  
  /**
   * 处理鼠标移动事件
   * @param {MouseEvent} event 鼠标事件
   */
  handleMouseMove(event) {
    if (this.isDragging && this.draggedNode) {
      const rect = this.canvasElement.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      // 计算偏移量
      const dx = x - this.dragStartX;
      const dy = y - this.dragStartY;
      
      // 更新节点位置
      this.draggedNode.position.x += dx;
      this.draggedNode.position.y += dy;
      
      // 更新拖动起点
      this.dragStartX = x;
      this.dragStartY = y;
      
      // 重绘状态机
      this.drawStateMachine();
    }
  }
  
  /**
   * 处理鼠标松开事件
   * @param {MouseEvent} event 鼠标事件
   */
  handleMouseUp(event) {
    // 结束拖动
    this.isDragging = false;
    this.draggedNode = null;
  }
  
  /**
   * 处理双击事件
   * @param {MouseEvent} event 鼠标事件
   */
  handleDoubleClick(event) {
    const rect = this.canvasElement.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // 检查是否双击了状态节点
    const clickedState = this.findStateAtPosition(x, y);
    
    if (clickedState) {
      // 打开状态编辑对话框
      this.openStateEditDialog(clickedState);
    } else {
      // 检查是否双击了过渡
      const clickedTransition = this.findTransitionAtPosition(x, y);
      
      if (clickedTransition) {
        // 打开过渡编辑对话框
        this.openTransitionEditDialog(clickedTransition);
      } else {
        // 在空白处双击，创建新状态
        this.createState(x, y);
      }
    }
  }
  
  /**
   * 查找指定位置的状态
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @returns {Object|null} 找到的状态或null
   */
  findStateAtPosition(x, y) {
    for (const state of this.stateMachine.states) {
      const stateX = state.position.x;
      const stateY = state.position.y;
      
      if (x >= stateX && x <= stateX + this.nodeWidth &&
          y >= stateY && y <= stateY + this.nodeHeight) {
        return state;
      }
    }
    
    return null;
  }
  
  /**
   * 查找指定位置的过渡
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @returns {Object|null} 找到的过渡或null
   */
  findTransitionAtPosition(x, y) {
    for (const transition of this.stateMachine.transitions) {
      const fromState = this.stateMachine.states.find(state => state.id === transition.from);
      const toState = this.stateMachine.states.find(state => state.id === transition.to);
      
      if (fromState && toState) {
        const fromX = fromState.position.x + this.nodeWidth / 2;
        const fromY = fromState.position.y + this.nodeHeight / 2;
        const toX = toState.position.x + this.nodeWidth / 2;
        const toY = toState.position.y + this.nodeHeight / 2;
        
        // 简单的线段点击检测
        const distance = this.distanceToLine(x, y, fromX, fromY, toX, toY);
        
        if (distance < 10) {
          return transition;
        }
      }
    }
    
    return null;
  }
  
  /**
   * 计算点到线段的距离
   * @param {number} x 点的X坐标
   * @param {number} y 点的Y坐标
   * @param {number} x1 线段起点X坐标
   * @param {number} y1 线段起点Y坐标
   * @param {number} x2 线段终点X坐标
   * @param {number} y2 线段终点Y坐标
   * @returns {number} 距离
   */
  distanceToLine(x, y, x1, y1, x2, y2) {
    const A = x - x1;
    const B = y - y1;
    const C = x2 - x1;
    const D = y2 - y1;
    
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    let param = -1;
    
    if (lenSq !== 0) {
      param = dot / lenSq;
    }
    
    let xx, yy;
    
    if (param < 0) {
      xx = x1;
      yy = y1;
    } else if (param > 1) {
      xx = x2;
      yy = y2;
    } else {
      xx = x1 + param * C;
      yy = y1 + param * D;
    }
    
    const dx = x - xx;
    const dy = y - yy;
    
    return Math.sqrt(dx * dx + dy * dy);
  }
  
  /**
   * 创建状态
   * @param {number} x X坐标
   * @param {number} y Y坐标
   */
  createState(x, y) {
    // 生成唯一ID
    const id = `state_${Date.now()}`;
    
    // 创建新状态
    const newState = {
      id: id,
      name: `状态 ${this.stateMachine.states.length + 1}`,
      isDefault: this.stateMachine.states.length === 0,
      position: { x: x - this.nodeWidth / 2, y: y - this.nodeHeight / 2 },
      animation: null,
      behaviors: []
    };
    
    // 添加到状态机
    this.stateMachine.states.push(newState);
    
    // 选中新状态
    this.selectedState = newState;
    this.selectedTransition = null;
    
    // 打开状态编辑对话框
    this.openStateEditDialog(newState);
    
    // 重绘状态机
    this.drawStateMachine();
  }
  
  /**
   * 创建过渡
   * @param {Object} fromState 起始状态
   * @param {Object} toState 目标状态
   */
  createTransition(fromState, toState) {
    // 检查是否已存在相同的过渡
    const existingTransition = this.stateMachine.transitions.find(
      t => t.from === fromState.id && t.to === toState.id
    );
    
    if (existingTransition) {
      // 选中已存在的过渡
      this.selectedState = null;
      this.selectedTransition = existingTransition;
      
      // 打开过渡编辑对话框
      this.openTransitionEditDialog(existingTransition);
    } else {
      // 生成唯一ID
      const id = `transition_${Date.now()}`;
      
      // 创建新过渡
      const newTransition = {
        id: id,
        from: fromState.id,
        to: toState.id,
        conditions: []
      };
      
      // 添加到状态机
      this.stateMachine.transitions.push(newTransition);
      
      // 选中新过渡
      this.selectedState = null;
      this.selectedTransition = newTransition;
      
      // 打开过渡编辑对话框
      this.openTransitionEditDialog(newTransition);
    }
    
    // 重绘状态机
    this.drawStateMachine();
  }
  
  /**
   * 打开状态编辑对话框
   * @param {Object} state 状态对象
   */
  openStateEditDialog(state) {
    console.log('打开状态编辑对话框:', state);
    
    // 在实际应用中，这里应该显示状态编辑对话框
    // 这里使用模拟代码
    
    // 更新UI
    this.updateStatePropertiesUI(state);
  }
  
  /**
   * 打开过渡编辑对话框
   * @param {Object} transition 过渡对象
   */
  openTransitionEditDialog(transition) {
    console.log('打开过渡编辑对话框:', transition);
    
    // 在实际应用中，这里应该显示过渡编辑对话框
    // 这里使用模拟代码
    
    // 更新UI
    this.updateTransitionPropertiesUI(transition);
  }
  
  /**
   * 更新状态属性UI
   * @param {Object} state 状态对象
   */
  updateStatePropertiesUI(state) {
    // 在实际应用中，这里应该更新UI显示状态属性
    console.log('更新状态属性UI:', state);
  }
  
  /**
   * 更新过渡属性UI
   * @param {Object} transition 过渡对象
   */
  updateTransitionPropertiesUI(transition) {
    // 在实际应用中，这里应该更新UI显示过渡属性
    console.log('更新过渡属性UI:', transition);
  }
  
  /**
   * 绘制状态机
   */
  drawStateMachine() {
    // 清空画布
    this.canvasContext.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
    
    // 绘制网格
    this.drawGrid();
    
    // 绘制过渡
    this.drawTransitions();
    
    // 绘制状态
    this.drawStates();
  }
  
  /**
   * 绘制网格
   */
  drawGrid() {
    this.canvasContext.strokeStyle = '#e0e0e0';
    this.canvasContext.lineWidth = 1;
    
    // 绘制垂直线
    for (let x = 0; x < this.canvasWidth; x += this.gridSize) {
      this.canvasContext.beginPath();
      this.canvasContext.moveTo(x, 0);
      this.canvasContext.lineTo(x, this.canvasHeight);
      this.canvasContext.stroke();
    }
    
    // 绘制水平线
    for (let y = 0; y < this.canvasHeight; y += this.gridSize) {
      this.canvasContext.beginPath();
      this.canvasContext.moveTo(0, y);
      this.canvasContext.lineTo(this.canvasWidth, y);
      this.canvasContext.stroke();
    }
  }
  
  /**
   * 绘制状态
   */
  drawStates() {
    for (const state of this.stateMachine.states) {
      const x = state.position.x;
      const y = state.position.y;
      const width = this.nodeWidth;
      const height = this.nodeHeight;
      
      // 设置样式
      if (state === this.selectedState) {
        this.canvasContext.fillStyle = '#4CAF50';
      } else if (state.isDefault) {
        this.canvasContext.fillStyle = '#2196F3';
      } else {
        this.canvasContext.fillStyle = '#607D8B';
      }
      
      // 绘制节点背景
      this.canvasContext.fillRect(x, y, width, height);
      
      // 绘制节点边框
      this.canvasContext.strokeStyle = '#000000';
      this.canvasContext.lineWidth = 2;
      this.canvasContext.strokeRect(x, y, width, height);
      
      // 绘制节点文本
      this.canvasContext.fillStyle = '#FFFFFF';
      this.canvasContext.font = '14px Arial';
      this.canvasContext.textAlign = 'center';
      this.canvasContext.textBaseline = 'middle';
      this.canvasContext.fillText(state.name, x + width / 2, y + height / 2);
    }
  }
  
  /**
   * 绘制过渡
   */
  drawTransitions() {
    for (const transition of this.stateMachine.transitions) {
      const fromState = this.stateMachine.states.find(state => state.id === transition.from);
      const toState = this.stateMachine.states.find(state => state.id === transition.to);
      
      if (fromState && toState) {
        const fromX = fromState.position.x + this.nodeWidth / 2;
        const fromY = fromState.position.y + this.nodeHeight / 2;
        const toX = toState.position.x + this.nodeWidth / 2;
        const toY = toState.position.y + this.nodeHeight / 2;
        
        // 设置样式
        if (transition === this.selectedTransition) {
          this.canvasContext.strokeStyle = '#FF5722';
          this.canvasContext.lineWidth = 3;
        } else {
          this.canvasContext.strokeStyle = '#9E9E9E';
          this.canvasContext.lineWidth = 2;
        }
        
        // 绘制线段
        this.canvasContext.beginPath();
        this.canvasContext.moveTo(fromX, fromY);
        this.canvasContext.lineTo(toX, toY);
        this.canvasContext.stroke();
        
        // 绘制箭头
        this.drawArrow(toX, toY, fromX, fromY);
      }
    }
  }
  
  /**
   * 绘制箭头
   * @param {number} x 箭头位置X坐标
   * @param {number} y 箭头位置Y坐标
   * @param {number} fromX 线段起点X坐标
   * @param {number} fromY 线段起点Y坐标
   */
  drawArrow(x, y, fromX, fromY) {
    const headLength = 10;
    const angle = Math.atan2(y - fromY, x - fromX);
    
    // 计算箭头位置
    const arrowX = x - 20 * Math.cos(angle);
    const arrowY = y - 20 * Math.sin(angle);
    
    // 绘制箭头
    this.canvasContext.beginPath();
    this.canvasContext.moveTo(arrowX, arrowY);
    this.canvasContext.lineTo(
      arrowX - headLength * Math.cos(angle - Math.PI / 6),
      arrowY - headLength * Math.sin(angle - Math.PI / 6)
    );
    this.canvasContext.lineTo(
      arrowX - headLength * Math.cos(angle + Math.PI / 6),
      arrowY - headLength * Math.sin(angle + Math.PI / 6)
    );
    this.canvasContext.closePath();
    this.canvasContext.fillStyle = this.canvasContext.strokeStyle;
    this.canvasContext.fill();
  }
}
