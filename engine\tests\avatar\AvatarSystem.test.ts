/**
 * AvatarSystem类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import { AvatarSystem } from '../../src/avatar/AvatarSystem';
import { AvatarComponent } from '../../src/avatar/AvatarComponent';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import { AssetManager } from '../../src/assets/AssetManager';

describe('AvatarSystem', () => {
  let engine: Engine;
  let world: World;
  let renderSystem: RenderSystem;
  let avatarSystem: AvatarSystem;
  let entity: Entity;
  let avatarComponent: AvatarComponent;
  let mockCanvas: HTMLCanvasElement;
  
  // 在每个测试前创建一个新的头像系统实例
  beforeEach(() => {
    // 创建模拟的canvas元素
    mockCanvas = document.createElement('canvas');
    mockCanvas.width = 800;
    mockCanvas.height = 600;
    document.body.appendChild(mockCanvas);
    
    // 创建引擎和世界
    engine = new Engine({
      canvas: mockCanvas,
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建渲染系统
    renderSystem = new RenderSystem({
      canvas: mockCanvas,
      antialias: true,
      shadows: true,
      pixelRatio: 1
    });
    
    // 添加渲染系统到引擎
    engine.addSystem(renderSystem);
    
    // 创建头像系统
    avatarSystem = new AvatarSystem();
    
    // 添加头像系统到引擎
    engine.addSystem(avatarSystem);
    
    // 创建实体
    entity = new Entity(world);
    entity.name = '头像实体';
    
    // 添加变换组件
    const transform = new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity.addComponent(transform);
    
    // 创建头像组件
    avatarComponent = new AvatarComponent(entity, {
      modelUrl: 'models/avatar.glb',
      animationsUrl: 'animations/avatar_animations.glb',
      skeletonMapping: {
        hips: 'Hips',
        spine: 'Spine',
        head: 'Head',
        leftArm: 'LeftArm',
        rightArm: 'RightArm',
        leftHand: 'LeftHand',
        rightHand: 'RightHand',
        leftLeg: 'LeftLeg',
        rightLeg: 'RightLeg'
      }
    });
    entity.addComponent(avatarComponent);
    
    // 添加实体到世界
    world.addEntity(entity);
    
    // 注册头像组件
    avatarSystem.registerAvatarComponent(entity, avatarComponent);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
    
    // 清理DOM
    if (mockCanvas && mockCanvas.parentNode) {
      mockCanvas.parentNode.removeChild(mockCanvas);
    }
  });
  
  // 测试头像系统初始化
  it('应该正确初始化头像系统', () => {
    expect(avatarSystem).toBeDefined();
    expect(avatarSystem['avatars'].size).toBe(1);
    expect(avatarSystem['avatars'].get(entity.getId())).toBe(avatarComponent);
  });
  
  // 测试注册和取消注册头像组件
  it('应该能够注册和取消注册头像组件', () => {
    // 创建另一个实体
    const entity2 = new Entity(world);
    entity2.name = '头像实体2';
    
    // 添加变换组件
    const transform2 = new Transform({
      position: { x: 5, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity2.addComponent(transform2);
    
    // 创建头像组件
    const avatarComponent2 = new AvatarComponent(entity2, {
      modelUrl: 'models/avatar2.glb'
    });
    entity2.addComponent(avatarComponent2);
    
    // 添加实体到世界
    world.addEntity(entity2);
    
    // 注册头像组件
    avatarSystem.registerAvatarComponent(entity2, avatarComponent2);
    
    // 验证组件已注册
    expect(avatarSystem['avatars'].has(entity2.getId())).toBe(true);
    expect(avatarSystem['avatars'].get(entity2.getId())).toBe(avatarComponent2);
    
    // 取消注册头像组件
    avatarSystem.unregisterAvatarComponent(entity2);
    
    // 验证组件已取消注册
    expect(avatarSystem['avatars'].has(entity2.getId())).toBe(false);
  });
  
  // 测试加载头像模型
  it('应该能够加载头像模型', async () => {
    // 模拟AssetManager.loadGLTF方法
    const mockModel = {
      scene: new THREE.Group(),
      animations: []
    };
    
    const loadGLTFSpy = vi.spyOn(AssetManager.prototype, 'loadGLTF').mockResolvedValue(mockModel);
    
    // 加载头像模型
    await avatarComponent.loadModel();
    
    // 验证loadGLTF方法被调用
    expect(loadGLTFSpy).toHaveBeenCalledWith('models/avatar.glb');
    
    // 验证模型已加载
    expect(avatarComponent['model']).toBe(mockModel.scene);
    
    // 恢复模拟
    loadGLTFSpy.mockRestore();
  });
  
  // 测试加载头像动画
  it('应该能够加载头像动画', async () => {
    // 模拟AssetManager.loadGLTF方法
    const mockAnimations = {
      scene: new THREE.Group(),
      animations: [
        new THREE.AnimationClip('idle', 1, []),
        new THREE.AnimationClip('walk', 1, []),
        new THREE.AnimationClip('run', 1, [])
      ]
    };
    
    const loadGLTFSpy = vi.spyOn(AssetManager.prototype, 'loadGLTF').mockResolvedValue(mockAnimations);
    
    // 加载头像动画
    await avatarComponent.loadAnimations();
    
    // 验证loadGLTF方法被调用
    expect(loadGLTFSpy).toHaveBeenCalledWith('animations/avatar_animations.glb');
    
    // 验证动画已加载
    expect(avatarComponent['animations'].size).toBe(3);
    expect(avatarComponent['animations'].has('idle')).toBe(true);
    expect(avatarComponent['animations'].has('walk')).toBe(true);
    expect(avatarComponent['animations'].has('run')).toBe(true);
    
    // 恢复模拟
    loadGLTFSpy.mockRestore();
  });
  
  // 测试播放动画
  it('应该能够播放动画', async () => {
    // 模拟AnimationMixer
    const mockMixer = {
      clipAction: vi.fn().mockReturnValue({
        play: vi.fn(),
        stop: vi.fn(),
        reset: vi.fn(),
        setLoop: vi.fn(),
        setEffectiveWeight: vi.fn(),
        crossFadeTo: vi.fn()
      }),
      update: vi.fn()
    };
    
    // 设置动画混合器
    (avatarComponent as any).mixer = mockMixer;
    
    // 模拟动画剪辑
    const mockClip = new THREE.AnimationClip('walk', 1, []);
    (avatarComponent as any).animations.set('walk', mockClip);
    
    // 播放动画
    avatarComponent.playAnimation('walk', {
      loop: true,
      weight: 1,
      fadeIn: 0.5
    });
    
    // 验证clipAction方法被调用
    expect(mockMixer.clipAction).toHaveBeenCalledWith(mockClip);
    
    // 验证play方法被调用
    expect(mockMixer.clipAction().play).toHaveBeenCalled();
    
    // 验证setLoop方法被调用
    expect(mockMixer.clipAction().setLoop).toHaveBeenCalled();
    
    // 验证setEffectiveWeight方法被调用
    expect(mockMixer.clipAction().setEffectiveWeight).toHaveBeenCalledWith(1);
  });
  
  // 测试停止动画
  it('应该能够停止动画', async () => {
    // 模拟AnimationMixer
    const mockMixer = {
      clipAction: vi.fn().mockReturnValue({
        play: vi.fn(),
        stop: vi.fn(),
        reset: vi.fn(),
        setLoop: vi.fn(),
        setEffectiveWeight: vi.fn(),
        crossFadeTo: vi.fn()
      }),
      update: vi.fn()
    };
    
    // 设置动画混合器
    (avatarComponent as any).mixer = mockMixer;
    
    // 模拟动画剪辑
    const mockClip = new THREE.AnimationClip('walk', 1, []);
    (avatarComponent as any).animations.set('walk', mockClip);
    
    // 播放动画
    avatarComponent.playAnimation('walk');
    
    // 停止动画
    avatarComponent.stopAnimation('walk');
    
    // 验证stop方法被调用
    expect(mockMixer.clipAction().stop).toHaveBeenCalled();
  });
  
  // 测试动画混合
  it('应该能够混合动画', async () => {
    // 模拟AnimationMixer
    const mockMixer = {
      clipAction: vi.fn().mockReturnValue({
        play: vi.fn(),
        stop: vi.fn(),
        reset: vi.fn(),
        setLoop: vi.fn(),
        setEffectiveWeight: vi.fn(),
        crossFadeTo: vi.fn()
      }),
      update: vi.fn()
    };
    
    // 设置动画混合器
    (avatarComponent as any).mixer = mockMixer;
    
    // 模拟动画剪辑
    const walkClip = new THREE.AnimationClip('walk', 1, []);
    const runClip = new THREE.AnimationClip('run', 1, []);
    (avatarComponent as any).animations.set('walk', walkClip);
    (avatarComponent as any).animations.set('run', runClip);
    
    // 播放第一个动画
    avatarComponent.playAnimation('walk');
    
    // 混合到第二个动画
    avatarComponent.crossFade('walk', 'run', 0.5);
    
    // 验证crossFadeTo方法被调用
    expect(mockMixer.clipAction().crossFadeTo).toHaveBeenCalled();
  });
  
  // 测试更新头像系统
  it('应该能够更新头像系统', () => {
    // 模拟AnimationMixer
    const mockMixer = {
      update: vi.fn()
    };
    
    // 设置动画混合器
    (avatarComponent as any).mixer = mockMixer;
    
    // 更新头像系统
    avatarSystem.update(0.016);
    
    // 验证update方法被调用
    expect(mockMixer.update).toHaveBeenCalledWith(0.016);
  });
});
