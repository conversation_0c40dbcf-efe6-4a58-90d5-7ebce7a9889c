/**
 * 测试运行器
 * 负责执行M4阶段的完整测试流程
 */

import { Logger } from '../../core/Logger';
import { EventEmitter } from '../../utils/EventEmitter';
import { SystemTestSuite } from './SystemTestSuite';
import { TestConfiguration } from './TestConfiguration';
import { DigitalHumanIntegrationSystem } from '../systems/DigitalHumanIntegrationSystem';
import { PerformanceOptimizer } from '../optimization/PerformanceOptimizer';
import { DigitalHumanMarketplaceService } from '../marketplace/DigitalHumanMarketplaceService';

/**
 * 测试运行状态
 */
interface TestRunStatus {
  isRunning: boolean;
  currentSuite: string | null;
  currentTest: string | null;
  startTime: number;
  progress: {
    completed: number;
    total: number;
    percentage: number;
  };
  results: {
    passed: number;
    failed: number;
    skipped: number;
  };
}

/**
 * 测试运行选项
 */
interface TestRunOptions {
  suites?: string[];           // 要运行的测试套件
  parallel?: boolean;          // 是否并行运行
  stopOnFailure?: boolean;     // 失败时是否停止
  generateReport?: boolean;    // 是否生成报告
  cleanup?: boolean;           // 运行后是否清理
  verbose?: boolean;           // 详细输出
}

/**
 * 测试运行结果
 */
interface TestRunResult {
  success: boolean;
  duration: number;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  suiteResults: Map<string, any>;
  reportPath?: string;
  errors: string[];
  warnings: string[];
}

/**
 * 测试运行器
 */
export class TestRunner extends EventEmitter {
  private logger: Logger;
  private config: TestConfiguration;
  private testSuite: SystemTestSuite;
  private status: TestRunStatus;
  
  // 系统组件
  private integrationSystem: DigitalHumanIntegrationSystem;
  private performanceOptimizer: PerformanceOptimizer;
  private marketplaceService: DigitalHumanMarketplaceService;

  constructor() {
    super();
    this.logger = new Logger('TestRunner');
    this.config = TestConfiguration.getInstance();
    
    this.status = {
      isRunning: false,
      currentSuite: null,
      currentTest: null,
      startTime: 0,
      progress: {
        completed: 0,
        total: 0,
        percentage: 0
      },
      results: {
        passed: 0,
        failed: 0,
        skipped: 0
      }
    };
  }

  /**
   * 初始化测试环境
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('初始化测试环境...');

      // 验证配置
      const validation = this.config.validateConfiguration();
      if (!validation.isValid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
      }

      // 初始化系统组件
      await this.initializeSystemComponents();

      // 初始化测试套件
      this.testSuite = new SystemTestSuite(
        this.integrationSystem,
        this.performanceOptimizer,
        this.marketplaceService
      );

      // 设置事件监听
      this.setupEventListeners();

      this.logger.info('测试环境初始化完成');

    } catch (error) {
      this.logger.error('测试环境初始化失败:', error);
      throw error;
    }
  }

  /**
   * 运行测试
   */
  public async runTests(options: TestRunOptions = {}): Promise<TestRunResult> {
    if (this.status.isRunning) {
      throw new Error('测试正在运行中');
    }

    try {
      this.logger.info('开始运行测试...');
      this.status.isRunning = true;
      this.status.startTime = Date.now();

      const result = await this.executeTests(options);
      
      this.logger.info(`测试完成，总耗时: ${result.duration}ms`);
      this.emit('testRunCompleted', result);

      return result;

    } catch (error) {
      this.logger.error('测试运行失败:', error);
      throw error;
    } finally {
      this.status.isRunning = false;
    }
  }

  /**
   * 执行测试
   */
  private async executeTests(options: TestRunOptions): Promise<TestRunResult> {
    const startTime = Date.now();
    const suiteConfig = this.config.getSuiteConfig();
    const suiteResults = new Map<string, any>();
    const errors: string[] = [];
    const warnings: string[] = [];

    // 确定要运行的测试套件
    const suitesToRun = options.suites || this.getEnabledSuites(suiteConfig);
    this.status.progress.total = suitesToRun.length;

    this.logger.info(`将运行 ${suitesToRun.length} 个测试套件: ${suitesToRun.join(', ')}`);

    // 运行测试套件
    for (const suiteName of suitesToRun) {
      try {
        this.status.currentSuite = suiteName;
        this.emit('suiteStarted', { suiteName });

        const suiteResult = await this.runTestSuite(suiteName, options);
        suiteResults.set(suiteName, suiteResult);

        this.status.results.passed += suiteResult.passedTests;
        this.status.results.failed += suiteResult.failedTests;
        this.status.progress.completed++;
        this.status.progress.percentage = (this.status.progress.completed / this.status.progress.total) * 100;

        this.emit('suiteCompleted', { suiteName, result: suiteResult });

        // 如果设置了失败时停止，且有失败的测试
        if (options.stopOnFailure && suiteResult.failedTests > 0) {
          warnings.push(`测试套件 ${suiteName} 有失败的测试，停止后续测试`);
          break;
        }

      } catch (error) {
        errors.push(`测试套件 ${suiteName} 执行失败: ${error.message}`);
        this.status.results.failed++;
        
        if (options.stopOnFailure) {
          break;
        }
      }
    }

    const duration = Date.now() - startTime;
    const totalTests = this.status.results.passed + this.status.results.failed + this.status.results.skipped;

    const result: TestRunResult = {
      success: errors.length === 0 && this.status.results.failed === 0,
      duration,
      totalTests,
      passedTests: this.status.results.passed,
      failedTests: this.status.results.failed,
      skippedTests: this.status.results.skipped,
      suiteResults,
      errors,
      warnings
    };

    // 生成测试报告
    if (options.generateReport !== false) {
      try {
        result.reportPath = await this.generateTestReport(result);
      } catch (error) {
        warnings.push(`生成测试报告失败: ${error.message}`);
      }
    }

    // 清理测试环境
    if (options.cleanup !== false) {
      await this.cleanup();
    }

    return result;
  }

  /**
   * 运行单个测试套件
   */
  private async runTestSuite(suiteName: string, options: TestRunOptions): Promise<any> {
    this.logger.info(`运行测试套件: ${suiteName}`);

    switch (suiteName) {
      case 'functional':
        return await this.testSuite.runFunctionalTests();
      case 'performance':
        return await this.testSuite.runPerformanceTests();
      case 'integration':
        return await this.testSuite.runIntegrationTests();
      case 'compatibility':
        return await this.testSuite.runCompatibilityTests();
      case 'acceptance':
        return await this.testSuite.runUserAcceptanceTests();
      case 'benchmark':
        return await this.testSuite.runBenchmarkTests();
      default:
        throw new Error(`未知的测试套件: ${suiteName}`);
    }
  }

  /**
   * 获取启用的测试套件
   */
  private getEnabledSuites(config: any): string[] {
    const suites: string[] = [];
    
    if (config.functional.enabled) suites.push('functional');
    if (config.performance.enabled) suites.push('performance');
    if (config.integration.enabled) suites.push('integration');
    if (config.compatibility.enabled) suites.push('compatibility');
    if (config.acceptance.enabled) suites.push('acceptance');

    return suites;
  }

  /**
   * 生成测试报告
   */
  private async generateTestReport(result: TestRunResult): Promise<string> {
    const reportConfig = this.config.getReportConfig();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = reportConfig.outputDir;
    
    // 确保报告目录存在
    // TODO: 实现目录创建逻辑

    const reportData = {
      timestamp: new Date().toISOString(),
      environment: this.config.getCurrentEnvironment().name,
      summary: {
        success: result.success,
        duration: result.duration,
        totalTests: result.totalTests,
        passedTests: result.passedTests,
        failedTests: result.failedTests,
        skippedTests: result.skippedTests,
        passRate: result.totalTests > 0 ? (result.passedTests / result.totalTests) * 100 : 0
      },
      suites: Array.from(result.suiteResults.entries()).map(([name, data]) => ({
        name,
        ...data
      })),
      errors: result.errors,
      warnings: result.warnings,
      configuration: this.config.exportConfiguration()
    };

    // 生成不同格式的报告
    const reportPaths: string[] = [];

    for (const format of reportConfig.formats) {
      const fileName = `test-report-${timestamp}.${format}`;
      const filePath = `${reportDir}/${fileName}`;

      switch (format) {
        case 'json':
          await this.writeJsonReport(filePath, reportData);
          break;
        case 'html':
          await this.writeHtmlReport(filePath, reportData);
          break;
        case 'xml':
          await this.writeXmlReport(filePath, reportData);
          break;
        case 'csv':
          await this.writeCsvReport(filePath, reportData);
          break;
      }

      reportPaths.push(filePath);
    }

    this.logger.info(`测试报告已生成: ${reportPaths.join(', ')}`);
    return reportPaths[0]; // 返回第一个报告路径
  }

  /**
   * 写入JSON报告
   */
  private async writeJsonReport(filePath: string, data: any): Promise<void> {
    // TODO: 实现JSON报告写入
    this.logger.info(`生成JSON报告: ${filePath}`);
  }

  /**
   * 写入HTML报告
   */
  private async writeHtmlReport(filePath: string, data: any): Promise<void> {
    // TODO: 实现HTML报告写入
    this.logger.info(`生成HTML报告: ${filePath}`);
  }

  /**
   * 写入XML报告
   */
  private async writeXmlReport(filePath: string, data: any): Promise<void> {
    // TODO: 实现XML报告写入
    this.logger.info(`生成XML报告: ${filePath}`);
  }

  /**
   * 写入CSV报告
   */
  private async writeCsvReport(filePath: string, data: any): Promise<void> {
    // TODO: 实现CSV报告写入
    this.logger.info(`生成CSV报告: ${filePath}`);
  }

  /**
   * 初始化系统组件
   */
  private async initializeSystemComponents(): Promise<void> {
    const env = this.config.getCurrentEnvironment();

    // 初始化集成系统
    this.integrationSystem = new DigitalHumanIntegrationSystem();

    // 初始化性能优化器
    this.performanceOptimizer = new PerformanceOptimizer();

    // 初始化市场服务
    this.marketplaceService = new DigitalHumanMarketplaceService(
      env.baseUrl,
      env.apiKey
    );

    this.logger.info('系统组件初始化完成');
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听测试套件事件
    this.testSuite.on('functionalTestsCompleted', (result) => {
      this.emit('testProgress', { suite: 'functional', result });
    });

    this.testSuite.on('performanceTestsCompleted', (result) => {
      this.emit('testProgress', { suite: 'performance', result });
    });

    this.testSuite.on('integrationTestsCompleted', (result) => {
      this.emit('testProgress', { suite: 'integration', result });
    });

    this.testSuite.on('compatibilityTestsCompleted', (result) => {
      this.emit('testProgress', { suite: 'compatibility', result });
    });

    this.testSuite.on('acceptanceTestsCompleted', (result) => {
      this.emit('testProgress', { suite: 'acceptance', result });
    });
  }

  /**
   * 获取测试状态
   */
  public getStatus(): TestRunStatus {
    return { ...this.status };
  }

  /**
   * 停止测试
   */
  public async stopTests(): Promise<void> {
    if (!this.status.isRunning) {
      return;
    }

    this.logger.info('停止测试运行...');
    this.status.isRunning = false;
    this.emit('testRunStopped');
  }

  /**
   * 清理测试环境
   */
  private async cleanup(): Promise<void> {
    try {
      this.logger.info('清理测试环境...');

      // 清理系统组件
      if (this.integrationSystem) {
        this.integrationSystem.dispose();
      }

      if (this.performanceOptimizer) {
        this.performanceOptimizer.dispose();
      }

      if (this.marketplaceService) {
        this.marketplaceService.dispose();
      }

      // 重置状态
      this.status = {
        isRunning: false,
        currentSuite: null,
        currentTest: null,
        startTime: 0,
        progress: {
          completed: 0,
          total: 0,
          percentage: 0
        },
        results: {
          passed: 0,
          failed: 0,
          skipped: 0
        }
      };

      this.logger.info('测试环境清理完成');

    } catch (error) {
      this.logger.error('测试环境清理失败:', error);
    }
  }

  /**
   * 销毁测试运行器
   */
  public dispose(): void {
    this.cleanup();
    this.removeAllListeners();
    this.logger.info('测试运行器已销毁');
  }
}
