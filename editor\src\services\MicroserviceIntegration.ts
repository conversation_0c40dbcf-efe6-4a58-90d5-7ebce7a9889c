/**
 * 微服务集成管理器
 * 统一管理与后端微服务的集成
 */
import { apiClient, ApiClient } from './ApiClient';
import { config } from '../config/environment';
import { EventEmitter } from '../utils/EventEmitter';

export interface ServiceHealth {
  name: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  lastCheck: Date;
  responseTime?: number;
  error?: string;
}

export interface ServiceRegistry {
  userService: ServiceHealth;
  projectService: ServiceHealth;
  assetService: ServiceHealth;
  renderService: ServiceHealth;
  collaborationService: ServiceHealth;
  apiGateway: ServiceHealth;
}

/**
 * 微服务集成管理器
 */
export class MicroserviceIntegration extends EventEmitter {
  private client: ApiClient;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private serviceRegistry: ServiceRegistry;
  private healthCheckIntervalMs = 30000; // 30秒

  constructor(client?: ApiClient) {
    super();
    this.client = client || apiClient;
    
    // 初始化服务注册表
    this.serviceRegistry = {
      userService: { name: 'user-service', status: 'unknown', lastCheck: new Date() },
      projectService: { name: 'project-service', status: 'unknown', lastCheck: new Date() },
      assetService: { name: 'asset-service', status: 'unknown', lastCheck: new Date() },
      renderService: { name: 'render-service', status: 'unknown', lastCheck: new Date() },
      collaborationService: { name: 'collaboration-service', status: 'unknown', lastCheck: new Date() },
      apiGateway: { name: 'api-gateway', status: 'unknown', lastCheck: new Date() },
    };

    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听API客户端事件
    this.client.on('auth:unauthorized', () => {
      this.emit('auth:required');
    });

    this.client.on('network:error', (error) => {
      this.emit('service:error', { service: 'network', error });
    });

    this.client.on('server:error', (error) => {
      this.emit('service:error', { service: 'server', error });
    });
  }

  /**
   * 初始化微服务集成
   */
  public async initialize(): Promise<void> {
    try {
      // 输出调试信息
      if (config.enableDebug) {
        console.log('🚀 初始化微服务集成，API URL:', config.apiUrl);
      }

      // 检查API网关连接
      await this.checkApiGatewayHealth();

      // 开始健康检查
      this.startHealthCheck();

      this.emit('integration:initialized');
    } catch (error) {
      console.error('微服务集成初始化失败:', error);
      this.emit('integration:error', error);
      throw error;
    }
  }

  /**
   * 检查API网关健康状态
   */
  private async checkApiGatewayHealth(): Promise<void> {
    try {
      const startTime = Date.now();
      await this.client.get('/health', { useCache: false });
      const responseTime = Date.now() - startTime;
      
      this.updateServiceHealth('apiGateway', 'healthy', responseTime);
    } catch (error) {
      this.updateServiceHealth('apiGateway', 'unhealthy', undefined, error as Error);
      throw error;
    }
  }

  /**
   * 开始健康检查
   */
  public startHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.healthCheckIntervalMs);

    // 立即执行一次健康检查
    this.performHealthCheck();
  }

  /**
   * 停止健康检查
   */
  public stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    const services = [
      { key: 'userService', endpoint: '/users/health' },
      { key: 'projectService', endpoint: '/projects/health' },
      { key: 'assetService', endpoint: '/assets/health' },
      { key: 'renderService', endpoint: '/render/health' },
      { key: 'collaborationService', endpoint: '/collaboration/health' },
      { key: 'apiGateway', endpoint: '/health' },
    ];

    for (const service of services) {
      try {
        const startTime = Date.now();
        await this.client.get(service.endpoint, { 
          useCache: false,
          timeout: 5000 // 5秒超时
        });
        const responseTime = Date.now() - startTime;
        
        this.updateServiceHealth(service.key as keyof ServiceRegistry, 'healthy', responseTime);
      } catch (error) {
        this.updateServiceHealth(service.key as keyof ServiceRegistry, 'unhealthy', undefined, error as Error);
      }
    }

    this.emit('health:checked', this.serviceRegistry);
  }

  /**
   * 更新服务健康状态
   */
  private updateServiceHealth(
    serviceKey: keyof ServiceRegistry,
    status: 'healthy' | 'unhealthy',
    responseTime?: number,
    error?: Error
  ): void {
    const service = this.serviceRegistry[serviceKey];
    const previousStatus = service.status;
    
    service.status = status;
    service.lastCheck = new Date();
    service.responseTime = responseTime;
    service.error = error?.message;

    // 如果状态发生变化，发出事件
    if (previousStatus !== status) {
      this.emit('service:status:changed', {
        service: serviceKey,
        previousStatus,
        currentStatus: status,
        serviceInfo: service
      });
    }
  }

  /**
   * 获取服务健康状态
   */
  public getServiceHealth(): ServiceRegistry {
    return { ...this.serviceRegistry };
  }

  /**
   * 获取特定服务健康状态
   */
  public getServiceHealthByName(serviceName: keyof ServiceRegistry): ServiceHealth {
    return { ...this.serviceRegistry[serviceName] };
  }

  /**
   * 检查所有服务是否健康
   */
  public areAllServicesHealthy(): boolean {
    return Object.values(this.serviceRegistry).every(service => service.status === 'healthy');
  }

  /**
   * 获取不健康的服务列表
   */
  public getUnhealthyServices(): ServiceHealth[] {
    return Object.values(this.serviceRegistry).filter(service => service.status !== 'healthy');
  }

  /**
   * 用户服务API
   */
  public get userService() {
    return {
      login: (credentials: { email: string; password: string }) =>
        this.client.post('/auth/login', credentials),
      
      register: (userData: { username: string; email: string; password: string }) =>
        this.client.post('/auth/register', userData),
      
      getProfile: () =>
        this.client.get('/auth/profile'),
      
      updateProfile: (data: any) =>
        this.client.patch('/auth/profile', data),
      
      logout: () =>
        this.client.post('/auth/logout'),
    };
  }

  /**
   * 项目服务API
   */
  public get projectService() {
    return {
      getProjects: (params?: any) =>
        this.client.get('/projects', { params }),
      
      getProject: (id: string) =>
        this.client.get(`/projects/${id}`),
      
      createProject: (data: any) =>
        this.client.post('/projects', data),
      
      updateProject: (id: string, data: any) =>
        this.client.patch(`/projects/${id}`, data),
      
      deleteProject: (id: string) =>
        this.client.delete(`/projects/${id}`),
    };
  }

  /**
   * 资产服务API
   */
  public get assetService() {
    return {
      getAssets: (projectId: string, params?: any) =>
        this.client.get(`/projects/${projectId}/assets`, { params }),
      
      uploadAsset: (projectId: string, file: File) =>
        this.client.upload(`/projects/${projectId}/assets`, file),
      
      deleteAsset: (projectId: string, assetId: string) =>
        this.client.delete(`/projects/${projectId}/assets/${assetId}`),
    };
  }

  /**
   * 渲染服务API
   */
  public get renderService() {
    return {
      createRenderJob: (data: any) =>
        this.client.post('/render/jobs', data),
      
      getRenderJobs: (params?: any) =>
        this.client.get('/render/jobs', { params }),
      
      getRenderJob: (id: string) =>
        this.client.get(`/render/jobs/${id}`),
      
      cancelRenderJob: (id: string) =>
        this.client.delete(`/render/jobs/${id}`),
    };
  }

  /**
   * 销毁实例
   */
  public destroy(): void {
    this.stopHealthCheck();
    this.removeAllListeners();
  }
}

// 创建默认实例
export const microserviceIntegration = new MicroserviceIntegration();

export default MicroserviceIntegration;
