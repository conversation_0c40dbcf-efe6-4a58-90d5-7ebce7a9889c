/**
 * 视觉脚本函数节点
 * 函数节点用于执行纯函数，不影响执行流程
 */
import { Node, NodeCategory, NodeOptions, NodeType } from './Node';
/**
 * 函数节点选项
 */
export interface FunctionNodeOptions extends NodeOptions {
    /** 函数名称 */
    functionName?: string;
}
/**
 * 函数节点基类
 */
export declare class FunctionNode extends Node {
    /** 节点类型 */
    readonly nodeType: NodeType;
    /** 节点类别 */
    readonly category: NodeCategory;
    /** 函数名称 */
    protected functionName: string;
    /** 是否已执行 */
    protected executed: boolean;
    /** 执行结果 */
    protected result: any;
    /**
     * 创建函数节点
     * @param options 节点选项
     */
    constructor(options: FunctionNodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
    /**
     * 计算函数结果
     * @param inputs 输入值
     * @returns 计算结果
     */
    protected compute(inputs: Record<string, any>): any;
    /**
     * 重置执行状态
     */
    reset(): void;
}
