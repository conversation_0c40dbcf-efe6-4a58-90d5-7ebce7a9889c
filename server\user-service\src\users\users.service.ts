/**
 * 用户服务
 */
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from './entities/user.entity';
import { UserAvatar } from './entities/user-avatar.entity';
import { UserSetting } from './entities/user-setting.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserAvatarDto } from './dto/create-user-avatar.dto';
import { CreateUserSettingDto } from './dto/create-user-setting.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserAvatar)
    private readonly userAvatarRepository: Repository<UserAvatar>,
    @InjectRepository(UserSetting)
    private readonly userSettingRepository: Repository<UserSetting>,
  ) {}

  /**
   * 创建用户
   */
  async create(createUserDto: CreateUserDto): Promise<User> {
    // 检查用户名是否已存在
    const existingUsername = await this.userRepository.findOne({ where: { username: createUserDto.username } });
    if (existingUsername) {
      throw new ConflictException('用户名已存在');
    }

    // 检查邮箱是否已存在
    const existingEmail = await this.userRepository.findOne({ where: { email: createUserDto.email } });
    if (existingEmail) {
      throw new ConflictException('邮箱已存在');
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    // 创建用户
    const user = this.userRepository.create({
      ...createUserDto,
      password: hashedPassword,
    });

    return this.userRepository.save(user);
  }

  /**
   * 查找所有用户
   */
  async findAll(): Promise<User[]> {
    return this.userRepository.find({
      relations: ['avatar', 'settings'],
    });
  }

  /**
   * 根据ID查找用户
   */
  async findOne(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['avatar', 'settings'],
    });

    if (!user) {
      throw new NotFoundException(`用户ID ${id} 不存在`);
    }

    return user;
  }

  /**
   * 根据用户名查找用户
   */
  async findByUsername(username: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { username },
      relations: ['avatar', 'settings'],
    });

    if (!user) {
      throw new NotFoundException(`用户名 ${username} 不存在`);
    }

    return user;
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['avatar', 'settings'],
    });

    if (!user) {
      throw new NotFoundException(`邮箱 ${email} 不存在`);
    }

    return user;
  }

  /**
   * 更新用户
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);

    // 检查用户名是否已存在
    if (updateUserDto.username && updateUserDto.username !== user.username) {
      const existingUsername = await this.userRepository.findOne({ where: { username: updateUserDto.username } });
      if (existingUsername) {
        throw new ConflictException('用户名已存在');
      }
    }

    // 检查邮箱是否已存在
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingEmail = await this.userRepository.findOne({ where: { email: updateUserDto.email } });
      if (existingEmail) {
        throw new ConflictException('邮箱已存在');
      }
    }

    // 如果更新密码，需要加密
    if (updateUserDto.password) {
      updateUserDto.password = await bcrypt.hash(updateUserDto.password, 10);
    }

    // 更新用户
    Object.assign(user, updateUserDto);
    return this.userRepository.save(user);
  }

  /**
   * 删除用户
   */
  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);
    await this.userRepository.remove(user);
  }

  /**
   * 验证用户密码
   */
  async validateUser(usernameOrEmail: string, password: string): Promise<User> {
    // 查询用户，包括密码字段
    const user = await this.userRepository
      .createQueryBuilder('user')
      .where('user.username = :usernameOrEmail OR user.email = :usernameOrEmail', { usernameOrEmail })
      .addSelect('user.password')
      .getOne();

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new BadRequestException('密码错误');
    }

    // 更新最后登录时间
    user.lastLoginAt = new Date();
    await this.userRepository.save(user);

    // 不返回密码
    delete user.password;
    return user;
  }

  /**
   * 创建用户头像
   */
  async createAvatar(userId: string, createUserAvatarDto: CreateUserAvatarDto): Promise<UserAvatar> {
    const user = await this.findOne(userId);

    // 检查用户是否已有头像
    if (user.avatar) {
      // 更新现有头像
      Object.assign(user.avatar, createUserAvatarDto);
      return this.userAvatarRepository.save(user.avatar);
    }

    // 创建新头像
    const avatar = this.userAvatarRepository.create({
      ...createUserAvatarDto,
      userId,
    });

    return this.userAvatarRepository.save(avatar);
  }

  /**
   * 创建用户设置
   */
  async createSetting(userId: string, createUserSettingDto: CreateUserSettingDto): Promise<UserSetting> {
    const user = await this.findOne(userId);

    // 检查设置是否已存在
    const existingSetting = await this.userSettingRepository.findOne({
      where: {
        userId,
        key: createUserSettingDto.key,
      },
    });

    if (existingSetting) {
      // 更新现有设置
      existingSetting.value = createUserSettingDto.value;
      return this.userSettingRepository.save(existingSetting);
    }

    // 创建新设置
    const setting = this.userSettingRepository.create({
      ...createUserSettingDto,
      userId,
    });

    return this.userSettingRepository.save(setting);
  }

  /**
   * 获取用户设置
   */
  async getSetting(userId: string, key: string): Promise<UserSetting> {
    const setting = await this.userSettingRepository.findOne({
      where: {
        userId,
        key,
      },
    });

    if (!setting) {
      throw new NotFoundException(`设置 ${key} 不存在`);
    }

    return setting;
  }

  /**
   * 删除用户设置
   */
  async removeSetting(userId: string, key: string): Promise<void> {
    const setting = await this.getSetting(userId, key);
    await this.userSettingRepository.remove(setting);
  }
}
