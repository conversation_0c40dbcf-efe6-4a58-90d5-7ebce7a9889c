/**
 * UI2DComponent.ts
 *
 * 2D UI元素组件，用于创建和管理2D界面元素
 */
import { UIComponent, UIComponentProps } from './UIComponent';
/**
 * 2D UI元素属性
 */
export interface UI2DComponentProps extends UIComponentProps {
    htmlElement?: HTMLElement;
    cssClass?: string;
    cssStyle?: Partial<CSSStyleDeclaration>;
    innerHTML?: string;
    textContent?: string;
    fontSize?: number | string;
    fontFamily?: string;
    fontWeight?: string | number;
    textAlign?: 'left' | 'center' | 'right' | 'justify';
    textColor?: string;
    lineHeight?: number | string;
    onFocus?: (event: any) => void;
    onBlur?: (event: any) => void;
    onKeyDown?: (event: any) => void;
    onKeyUp?: (event: any) => void;
    onChange?: (event: any) => void;
}
/**
 * 2D UI元素组件
 * 用于创建和管理2D界面元素
 */
export declare class UI2DComponent extends UIComponent {
    htmlElement?: HTMLElement;
    cssClass: string;
    cssStyle: Partial<CSSStyleDeclaration>;
    innerHTML: string;
    textContent: string;
    fontSize?: number | string;
    fontFamily?: string;
    fontWeight?: string | number;
    textAlign?: 'left' | 'center' | 'right' | 'justify';
    textColor?: string;
    lineHeight?: number | string;
    /**
     * 构造函数
     * @param props 2D UI元素属性
     */
    constructor(props?: UI2DComponentProps);
    /**
     * 创建HTML元素
     */
    private createHTMLElement;
    /**
     * 应用样式
     */
    private applyStyles;
    /**
     * 添加HTML事件监听器
     */
    private addHTMLEventListeners;
    /**
     * 更新UI元素
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 渲染UI元素
     */
    render(): void;
    /**
     * 销毁UI元素
     */
    dispose(): void;
    /**
     * 设置HTML内容
     * @param html HTML内容
     */
    setHTML(html: string): void;
    /**
     * 设置文本内容
     * @param text 文本内容
     */
    setText(text: string): void;
    /**
     * 设置CSS类
     * @param cssClass CSS类名
     */
    setCSSClass(cssClass: string): void;
    /**
     * 添加CSS类
     * @param cssClass CSS类名
     */
    addCSSClass(cssClass: string): void;
    /**
     * 移除CSS类
     * @param cssClass CSS类名
     */
    removeCSSClass(cssClass: string): void;
}
