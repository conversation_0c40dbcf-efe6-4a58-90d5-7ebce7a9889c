/**
 * 输入管理器
 * 用于管理输入设备和处理输入映射
 */
import { EventCallback } from '../utils/EventEmitter';
import { InputDevice } from './InputDevice';
import { InputMapping } from './InputMapping';
import { InputAction } from './InputAction';
import { InputBinding } from './InputBinding';
/**
 * 输入管理器选项
 */
export interface InputManagerOptions {
    /** 目标元素 */
    element?: HTMLElement;
    /** 是否阻止默认行为 */
    preventDefault?: boolean;
    /** 是否阻止事件传播 */
    stopPropagation?: boolean;
    /** 是否启用键盘输入 */
    enableKeyboard?: boolean;
    /** 是否启用鼠标输入 */
    enableMouse?: boolean;
    /** 是否启用触摸输入 */
    enableTouch?: boolean;
    /** 是否启用游戏手柄输入 */
    enableGamepad?: boolean;
    /** 是否启用XR输入 */
    enableXR?: boolean;
    /** 是否启用手势输入 */
    enableGesture?: boolean;
    /** 是否启用语音输入 */
    enableVoice?: boolean;
    /** 手势设备选项 */
    gestureOptions?: any;
    /** 语音设备选项 */
    voiceOptions?: any;
}
/**
 * 输入管理器
 * 用于管理输入设备和处理输入映射
 */
export declare class InputManager {
    /** 单例实例 */
    private static instance;
    /** 事件发射器 */
    private eventEmitter;
    /** 输入设备列表 */
    private devices;
    /** 输入映射列表 */
    private mappings;
    /** 输入动作列表 */
    private actions;
    /** 输入绑定列表 */
    private bindings;
    /** 目标元素 */
    private element;
    /** 是否阻止默认行为 */
    private preventDefault;
    /** 是否阻止事件传播 */
    private stopPropagation;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /**
     * 创建输入管理器
     * @param options 输入管理器选项
     */
    constructor(options?: InputManagerOptions);
    /**
     * 获取单例实例
     * @returns 输入管理器实例
     */
    static getInstance(): InputManager;
    /**
     * 初始化
     */
    initialize(): void;
    /**
     * 更新
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁
     */
    destroy(): void;
    /**
     * 添加设备
     * @param device 输入设备
     */
    addDevice(device: InputDevice): void;
    /**
     * 获取设备
     * @param name 设备名称
     * @returns 输入设备
     */
    getDevice<T extends InputDevice>(name: string): T | undefined;
    /**
     * 添加映射
     * @param mapping 输入映射
     */
    addMapping(mapping: InputMapping): void;
    /**
     * 获取映射
     * @param name 映射名称
     * @returns 输入映射
     */
    getMapping(name: string): InputMapping | undefined;
    /**
     * 添加动作
     * @param action 输入动作
     */
    addAction(action: InputAction): void;
    /**
     * 获取动作
     * @param name 动作名称
     * @returns 输入动作
     */
    getAction(name: string): InputAction | undefined;
    /**
     * 添加绑定
     * @param binding 输入绑定
     */
    addBinding(binding: InputBinding): void;
    /**
     * 获取绑定
     * @param name 绑定名称
     * @returns 输入绑定
     */
    getBinding(name: string): InputBinding | undefined;
    /**
     * 处理输入动作
     */
    private processActions;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @returns 当前实例，用于链式调用
     */
    on(event: string, callback: EventCallback): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @returns 当前实例，用于链式调用
     */
    off(event: string, callback: EventCallback): this;
}
