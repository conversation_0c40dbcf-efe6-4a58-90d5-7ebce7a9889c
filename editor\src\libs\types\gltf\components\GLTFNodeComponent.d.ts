/**
 * GLTF节点组件
 * 用于存储GLTF节点数据
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
/**
 * GLTF节点组件
 */
export declare class GLTFNodeComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** Three.js对象 */
    private object;
    /** 节点索引 */
    private nodeIndex;
    /** 节点名称 */
    private nodeName;
    /** 节点类型 */
    private nodeType;
    /** 节点额外数据 */
    private userData;
    /**
     * 创建GLTF节点组件
     * @param object Three.js对象
     */
    constructor(object: THREE.Object3D);
    /**
     * 确定节点类型
     * @param object Three.js对象
     * @returns 节点类型
     */
    private determineNodeType;
    /**
     * 获取Three.js对象
     * @returns Three.js对象
     */
    getObject(): THREE.Object3D;
    /**
     * 设置Three.js对象
     * @param object Three.js对象
     */
    setObject(object: THREE.Object3D): void;
    /**
     * 获取节点索引
     * @returns 节点索引
     */
    getNodeIndex(): number;
    /**
     * 设置节点索引
     * @param index 节点索引
     */
    setNodeIndex(index: number): void;
    /**
     * 获取节点名称
     * @returns 节点名称
     */
    getNodeName(): string;
    /**
     * 设置节点名称
     * @param name 节点名称
     */
    setNodeName(name: string): void;
    /**
     * 获取节点类型
     * @returns 节点类型
     */
    getNodeType(): string;
    /**
     * 获取节点额外数据
     * @returns 节点额外数据
     */
    getUserData(): Record<string, any>;
    /**
     * 设置节点额外数据
     * @param userData 节点额外数据
     */
    setUserData(userData: Record<string, any>): void;
    /**
     * 克隆组件
     * @returns 克隆的组件
     */
    clone(): GLTFNodeComponent;
}
