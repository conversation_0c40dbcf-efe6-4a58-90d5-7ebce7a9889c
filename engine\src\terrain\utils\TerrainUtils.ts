/**
 * 地形工具类
 * 提供地形操作的工具函数
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';

/**
 * 笔刷类型
 */
export enum BrushType {
  /** 提升 */
  RAISE = 'raise',
  /** 降低 */
  LOWER = 'lower',
  /** 平滑 */
  SMOOTH = 'smooth',
  /** 平坦化 */
  FLATTEN = 'flatten',
  /** 噪声 */
  NOISE = 'noise',
  /** 绘制 */
  PAINT = 'paint'
}

/**
 * 笔刷形状
 */
export enum BrushShape {
  /** 圆形 */
  CIRCLE = 'circle',
  /** 方形 */
  SQUARE = 'square',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 笔刷参数
 */
export interface BrushParams {
  /** 笔刷类型 */
  type: BrushType;
  /** 笔刷形状 */
  shape: BrushShape;
  /** 笔刷大小 */
  size: number;
  /** 笔刷强度 */
  strength: number;
  /** 笔刷衰减 */
  falloff: number;
  /** 目标高度（用于平坦化） */
  targetHeight?: number;
  /** 噪声种子（用于噪声） */
  noiseSeed?: number;
  /** 噪声比例（用于噪声） */
  noiseScale?: number;
  /** 纹理索引（用于绘制） */
  textureIndex?: number;
}

/**
 * 地形生成参数
 */
export interface TerrainGenerationParams {
  /** 种子 */
  seed: number;
  /** 比例 */
  scale: number;
  /** 持久度 */
  persistence: number;
  /** 八度 */
  octaves: number;
  /** 频率 */
  frequency: number;
  /** 幅度 */
  amplitude: number;
  /** 侵蚀迭代次数 */
  erosionIterations: number;
  /** 侵蚀强度 */
  erosionStrength: number;
}

/**
 * 地形工具类
 */
export class TerrainUtils {
  /**
   * 创建地形工具类
   */
  constructor() {
    // 初始化
  }

  /**
   * 应用笔刷
   * @param terrain 地形组件
   * @param worldX 世界X坐标
   * @param worldZ 世界Z坐标
   * @param params 笔刷参数
   */
  public applyBrush(terrain: TerrainComponent, worldX: number, worldZ: number, params: BrushParams): void {
    // 将世界坐标转换为地形坐标
    const terrainX = ((worldX + terrain.width / 2) / terrain.width) * (terrain.resolution - 1);
    const terrainZ = ((worldZ + terrain.height / 2) / terrain.height) * (terrain.resolution - 1);
    
    // 计算笔刷半径（地形坐标系）
    const brushRadius = (params.size / terrain.width) * terrain.resolution;
    
    // 计算受影响的区域
    const minX = Math.max(0, Math.floor(terrainX - brushRadius));
    const maxX = Math.min(terrain.resolution - 1, Math.ceil(terrainX + brushRadius));
    const minZ = Math.max(0, Math.floor(terrainZ - brushRadius));
    const maxZ = Math.min(terrain.resolution - 1, Math.ceil(terrainZ + brushRadius));
    
    // 应用笔刷效果
    switch (params.type) {
      case BrushType.RAISE:
        this.applyRaiseBrush(terrain, terrainX, terrainZ, minX, maxX, minZ, maxZ, params);
        break;
      case BrushType.LOWER:
        this.applyLowerBrush(terrain, terrainX, terrainZ, minX, maxX, minZ, maxZ, params);
        break;
      case BrushType.SMOOTH:
        this.applySmoothBrush(terrain, terrainX, terrainZ, minX, maxX, minZ, maxZ, params);
        break;
      case BrushType.FLATTEN:
        this.applyFlattenBrush(terrain, terrainX, terrainZ, minX, maxX, minZ, maxZ, params);
        break;
      case BrushType.NOISE:
        this.applyNoiseBrush(terrain, terrainX, terrainZ, minX, maxX, minZ, maxZ, params);
        break;
      case BrushType.PAINT:
        this.applyPaintBrush(terrain, terrainX, terrainZ, minX, maxX, minZ, maxZ, params);
        break;
    }
    
    // 标记地形需要更新
    terrain.needsUpdate = true;
    terrain.needsPhysicsUpdate = true;
  }

  /**
   * 应用提升笔刷
   */
  private applyRaiseBrush(
    terrain: TerrainComponent,
    centerX: number,
    centerZ: number,
    minX: number,
    maxX: number,
    minZ: number,
    maxZ: number,
    params: BrushParams
  ): void {
    const brushRadius = (params.size / terrain.width) * terrain.resolution;
    
    for (let z = minZ; z <= maxZ; z++) {
      for (let x = minX; x <= maxX; x++) {
        const distance = this.getBrushDistance(x, z, centerX, centerZ, params.shape);
        if (distance <= brushRadius) {
          const falloff = this.calculateFalloff(distance, brushRadius, params.falloff);
          const index = z * terrain.resolution + x;
          terrain.heightData[index] += params.strength * falloff * 0.01;
          
          // 限制高度范围
          terrain.heightData[index] = Math.min(1.0, Math.max(0.0, terrain.heightData[index]));
        }
      }
    }
  }

  /**
   * 应用降低笔刷
   */
  private applyLowerBrush(
    terrain: TerrainComponent,
    centerX: number,
    centerZ: number,
    minX: number,
    maxX: number,
    minZ: number,
    maxZ: number,
    params: BrushParams
  ): void {
    const brushRadius = (params.size / terrain.width) * terrain.resolution;
    
    for (let z = minZ; z <= maxZ; z++) {
      for (let x = minX; x <= maxX; x++) {
        const distance = this.getBrushDistance(x, z, centerX, centerZ, params.shape);
        if (distance <= brushRadius) {
          const falloff = this.calculateFalloff(distance, brushRadius, params.falloff);
          const index = z * terrain.resolution + x;
          terrain.heightData[index] -= params.strength * falloff * 0.01;
          
          // 限制高度范围
          terrain.heightData[index] = Math.min(1.0, Math.max(0.0, terrain.heightData[index]));
        }
      }
    }
  }

  /**
   * 应用平滑笔刷
   */
  private applySmoothBrush(
    terrain: TerrainComponent,
    centerX: number,
    centerZ: number,
    minX: number,
    maxX: number,
    minZ: number,
    maxZ: number,
    params: BrushParams
  ): void {
    const brushRadius = (params.size / terrain.width) * terrain.resolution;
    const tempHeightData = new Float32Array(terrain.heightData);
    
    for (let z = minZ; z <= maxZ; z++) {
      for (let x = minX; x <= maxX; x++) {
        const distance = this.getBrushDistance(x, z, centerX, centerZ, params.shape);
        if (distance <= brushRadius) {
          const falloff = this.calculateFalloff(distance, brushRadius, params.falloff);
          const index = z * terrain.resolution + x;
          
          // 计算周围高度平均值
          let sum = 0;
          let count = 0;
          
          for (let nz = Math.max(0, z - 1); nz <= Math.min(terrain.resolution - 1, z + 1); nz++) {
            for (let nx = Math.max(0, x - 1); nx <= Math.min(terrain.resolution - 1, x + 1); nx++) {
              sum += terrain.heightData[nz * terrain.resolution + nx];
              count++;
            }
          }
          
          const average = sum / count;
          tempHeightData[index] = terrain.heightData[index] * (1 - falloff * params.strength * 0.1) + average * falloff * params.strength * 0.1;
        }
      }
    }
    
    // 更新高度数据
    for (let z = minZ; z <= maxZ; z++) {
      for (let x = minX; x <= maxX; x++) {
        const index = z * terrain.resolution + x;
        terrain.heightData[index] = tempHeightData[index];
      }
    }
  }

  /**
   * 应用平坦化笔刷
   */
  private applyFlattenBrush(
    terrain: TerrainComponent,
    centerX: number,
    centerZ: number,
    minX: number,
    maxX: number,
    minZ: number,
    maxZ: number,
    params: BrushParams
  ): void {
    const brushRadius = (params.size / terrain.width) * terrain.resolution;
    const targetHeight = params.targetHeight !== undefined ? params.targetHeight : terrain.heightData[Math.floor(centerZ) * terrain.resolution + Math.floor(centerX)];
    
    for (let z = minZ; z <= maxZ; z++) {
      for (let x = minX; x <= maxX; x++) {
        const distance = this.getBrushDistance(x, z, centerX, centerZ, params.shape);
        if (distance <= brushRadius) {
          const falloff = this.calculateFalloff(distance, brushRadius, params.falloff);
          const index = z * terrain.resolution + x;
          terrain.heightData[index] = terrain.heightData[index] * (1 - falloff * params.strength * 0.1) + targetHeight * falloff * params.strength * 0.1;
        }
      }
    }
  }

  /**
   * 应用噪声笔刷
   */
  private applyNoiseBrush(
    terrain: TerrainComponent,
    centerX: number,
    centerZ: number,
    minX: number,
    maxX: number,
    minZ: number,
    maxZ: number,
    params: BrushParams
  ): void {
    const brushRadius = (params.size / terrain.width) * terrain.resolution;
    const seed = params.noiseSeed || Math.random() * 1000;
    const scale = params.noiseScale || 0.1;
    
    for (let z = minZ; z <= maxZ; z++) {
      for (let x = minX; x <= maxX; x++) {
        const distance = this.getBrushDistance(x, z, centerX, centerZ, params.shape);
        if (distance <= brushRadius) {
          const falloff = this.calculateFalloff(distance, brushRadius, params.falloff);
          const index = z * terrain.resolution + x;
          
          // 生成噪声值
          const noise = this.perlinNoise(x * scale, z * scale, seed);
          const noiseValue = (noise * 2 - 1) * params.strength * 0.01;
          
          terrain.heightData[index] += noiseValue * falloff;
          
          // 限制高度范围
          terrain.heightData[index] = Math.min(1.0, Math.max(0.0, terrain.heightData[index]));
        }
      }
    }
  }

  /**
   * 应用绘制笔刷
   */
  private applyPaintBrush(
    terrain: TerrainComponent,
    centerX: number,
    centerZ: number,
    minX: number,
    maxX: number,
    minZ: number,
    maxZ: number,
    params: BrushParams
  ): void {
    // 这里需要实现纹理绘制逻辑
    // 需要修改地形材质的混合权重贴图
  }

  /**
   * 获取笔刷距离
   */
  private getBrushDistance(x: number, z: number, centerX: number, centerZ: number, shape: BrushShape): number {
    switch (shape) {
      case BrushShape.CIRCLE:
        return Math.sqrt((x - centerX) * (x - centerX) + (z - centerZ) * (z - centerZ));
      case BrushShape.SQUARE:
        return Math.max(Math.abs(x - centerX), Math.abs(z - centerZ));
      case BrushShape.CUSTOM:
        // 自定义形状距离计算
        return Math.sqrt((x - centerX) * (x - centerX) + (z - centerZ) * (z - centerZ));
      default:
        return Math.sqrt((x - centerX) * (x - centerX) + (z - centerZ) * (z - centerZ));
    }
  }

  /**
   * 计算衰减
   */
  private calculateFalloff(distance: number, radius: number, falloff: number): number {
    if (distance >= radius) {
      return 0;
    }
    
    const normalized = distance / radius;
    return Math.pow(1 - normalized, falloff);
  }

  /**
   * Perlin噪声
   */
  private perlinNoise(x: number, y: number, seed: number): number {
    // 简化的Perlin噪声实现
    // 实际应用中应使用更完整的噪声库
    const X = Math.floor(x) & 255;
    const Y = Math.floor(y) & 255;
    const xf = x - Math.floor(x);
    const yf = y - Math.floor(y);
    
    const topRight = this.randomGradient(X + 1, Y + 1, seed);
    const topLeft = this.randomGradient(X, Y + 1, seed);
    const bottomRight = this.randomGradient(X + 1, Y, seed);
    const bottomLeft = this.randomGradient(X, Y, seed);
    
    const u = this.fade(xf);
    const v = this.fade(yf);
    
    const result = this.lerp(
      this.lerp(bottomLeft, bottomRight, u),
      this.lerp(topLeft, topRight, u),
      v
    );
    
    return (result + 1) / 2;
  }

  /**
   * 随机梯度
   */
  private randomGradient(x: number, y: number, seed: number): number {
    const random = Math.sin(x * 12.9898 + y * 78.233 + seed) * 43758.5453;
    return random - Math.floor(random);
  }

  /**
   * 缓动函数
   */
  private fade(t: number): number {
    return t * t * t * (t * (t * 6 - 15) + 10);
  }

  /**
   * 线性插值
   */
  private lerp(a: number, b: number, t: number): number {
    return a + t * (b - a);
  }
}
