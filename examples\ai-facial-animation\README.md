# AI面部动画生成示例

本示例展示了如何使用AI面部动画生成功能，通过情感分析和AI模型生成自然的面部动画。

## 功能特点

- 基于文本的情感分析
- 多种AI模型支持（基础模型、高级模型、BERT模型）
- 自然的面部表情生成
- 微表情和自然变化
- 情感过渡和混合
- 实时生成和预览

## 使用方法

1. 打开面部动画编辑器
2. 点击"使用AI生成"按钮
3. 输入情感描述文本
4. 选择模型类型和参数
5. 点击"生成"按钮
6. 预览生成的动画
7. 应用到角色

## 示例代码

```typescript
// 创建AI面部动画适配器系统
const aiAnimationSystem = new AIFacialAnimationAdapterSystem(world, {
  debug: true,
  useLocalModel: true,
  modelPath: 'models/bert-emotion',
  useGPU: false,
  enableAdvancedFeatures: true
});

// 注册系统
world.registerSystem(aiAnimationSystem);

// 创建角色实体
const character = world.createEntity('character');

// 添加面部动画组件
const facialAnimation = new FacialAnimationComponent(character);
character.addComponent(facialAnimation);

// 生成AI面部动画
const request = {
  id: 'animation1',
  prompt: '角色感到非常开心，然后突然惊讶，最后回到平静状态',
  duration: 5.0,
  loop: true,
  style: 'natural',
  intensity: 0.8,
  modelType: 'advanced',
  useAdvancedFeatures: true
};

// 生成动画
aiAnimationSystem.generateAIFacialAnimation(character, request);

// 监听生成完成事件
aiAnimationSystem.addEventListener('generationComplete', (result) => {
  if (result.success) {
    console.log('动画生成成功:', result.clipId);
    
    // 应用生成的动画
    aiAnimationSystem.applyGeneratedAnimation(character, result.clipId);
  } else {
    console.error('动画生成失败:', result.error);
  }
});
```

## 高级用法

### 使用BERT模型进行更精确的情感分析

```typescript
// 使用BERT模型生成动画
const request = {
  id: 'animation2',
  prompt: '角色先是平静地思考，然后逐渐变得兴奋，最后露出满意的微笑',
  duration: 8.0,
  loop: false,
  style: 'natural',
  intensity: 0.7,
  modelType: 'bert',
  useAdvancedFeatures: true
};

// 生成动画
aiAnimationSystem.generateAIFacialAnimation(character, request);
```

### 自定义生成参数

```typescript
// 创建高级情感动画生成器
const generator = new AdvancedEmotionBasedAnimationGenerator({
  debug: true,
  useLocalModel: true,
  modelPath: 'models/emotion',
  useGPU: false,
  keyframeDensity: 6,
  enableExpressionBlending: true,
  enableMicroExpressions: true,
  enableEmotionTransitions: true,
  enableNaturalVariation: true
});

// 初始化生成器
await generator.initialize();

// 创建生成请求
const request = {
  id: 'custom1',
  prompt: '角色表现出复杂的情感变化，从悲伤到愤怒再到释然',
  duration: 10.0,
  loop: false,
  intensity: 0.9
};

// 生成动画
const result = await generator.generateFacialAnimation(request);

if (result.success && result.clip) {
  // 使用生成的动画片段
  facialAnimation.addClip(result.clip);
  facialAnimation.playClip(result.clip.name);
}
```

## 注意事项

1. 生成质量取决于提示文本的质量和详细程度
2. BERT模型需要更多的计算资源，但提供更精确的情感分析
3. 对于复杂的情感变化，建议使用更长的持续时间
4. 可以通过调整强度参数控制表情的夸张程度
5. 生成的动画可以在编辑器中进一步调整和优化

## 相关文件

- `engine/src/avatar/ai/AdvancedEmotionBasedAnimationGenerator.ts` - 高级情感动画生成器
- `engine/src/avatar/ai/BERTEmotionModel.ts` - BERT情感分析模型
- `engine/src/avatar/adapters/AIFacialAnimationAdapter.ts` - AI面部动画适配器
- `editor/src/components/FacialAnimationEditor/AIFacialAnimationGenerator.tsx` - AI面部动画生成面板
- `editor/src/services/AIFacialAnimationService.ts` - AI面部动画服务
