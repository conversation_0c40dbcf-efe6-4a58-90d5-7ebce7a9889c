/**
 * 后处理效果
 * 后处理效果的基类
 */
import * as THREE from 'three';
import { Pass } from 'three/examples/jsm/postprocessing/Pass.js';
/**
 * 后处理效果选项
 */
export interface PostProcessingEffectOptions {
    /** 效果名称 */
    name: string;
    /** 是否启用 */
    enabled?: boolean;
}
/**
 * 后处理效果
 */
export declare abstract class PostProcessingEffect {
    /** 效果名称 */
    protected name: string;
    /** 是否启用 */
    protected enabled: boolean;
    /** 渲染器 */
    protected renderer: THREE.WebGLRenderer | null;
    /** 通道 */
    protected pass: Pass | null;
    /** 宽度 */
    protected width: number;
    /** 高度 */
    protected height: number;
    /** 是否已初始化 */
    protected initialized: boolean;
    /** 是否已销毁 */
    protected destroyed: boolean;
    /**
     * 创建后处理效果
     * @param options 后处理效果选项
     */
    constructor(options: PostProcessingEffectOptions);
    /**
     * 初始化效果
     * @param renderer 渲染器
     * @param width 宽度
     * @param height 高度
     */
    initialize(renderer: THREE.WebGLRenderer, width: number, height: number): void;
    /**
     * 创建通道
     */
    protected abstract createPass(): void;
    /**
     * 更新效果
     * @param _deltaTime 帧间隔时间（秒）- 未使用，保留以符合接口
     */
    update(_deltaTime: number): void;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
    /**
     * 获取效果名称
     * @returns 效果名称
     */
    getName(): string;
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 获取是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 获取通道
     * @returns 通道
     */
    getPass(): Pass | null;
    /**
     * 销毁效果
     */
    dispose(): void;
}
