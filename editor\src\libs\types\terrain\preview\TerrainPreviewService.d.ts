import { HeightMapImportOptions } from '../io/HeightMapImportExport';
import { TerrainImportOptions } from '../io/TerrainImportExportService';
import { ThirdPartyTerrainFormat, ThirdPartyTerrainImportOptions } from '../io/ThirdPartyTerrainImportExport';
/**
 * 预览渲染器选项
 */
export interface PreviewRendererOptions {
    /** 宽度 */
    width: number;
    /** 高度 */
    height: number;
    /** 背景颜色 */
    backgroundColor?: number;
    /** 是否使用环境光遮蔽 */
    useAO?: boolean;
    /** 是否使用阴影 */
    useShadows?: boolean;
    /** 是否使用线框 */
    useWireframe?: boolean;
    /** 是否使用网格 */
    useGrid?: boolean;
}
/**
 * 地形预览服务
 */
export declare class TerrainPreviewService {
    /** 场景 */
    private scene;
    /** 相机 */
    private camera;
    /** 渲染器 */
    private renderer;
    /** 地形网格 */
    private terrainMesh;
    /** 地形材质 */
    private terrainMaterial;
    /** 地形几何体 */
    private terrainGeometry;
    /** 网格辅助对象 */
    private gridHelper;
    /** 环境光 */
    private ambientLight;
    /** 方向光 */
    private directionalLight;
    /** 是否已初始化 */
    private initialized;
    /** 渲染器选项 */
    private options;
    /**
     * 创建地形预览服务
     * @param options 渲染器选项
     */
    constructor(options: PreviewRendererOptions);
    /**
     * 获取渲染器DOM元素
     * @returns 渲染器DOM元素
     */
    getDomElement(): HTMLCanvasElement;
    /**
     * 预览高度图
     * @param heightMap 高度图（URL、File、Blob、ImageData、Canvas、Image）
     * @param options 导入选项
     * @returns Promise，解析为是否预览成功
     */
    previewHeightMap(heightMap: string | File | Blob | ImageData | HTMLCanvasElement | HTMLImageElement, options?: HeightMapImportOptions): Promise<boolean>;
    /**
     * 预览JSON
     * @param json JSON字符串
     * @param options 导入选项
     * @returns 是否预览成功
     */
    previewJSON(json: string, options?: TerrainImportOptions): boolean;
    /**
     * 预览第三方格式
     * @param format 格式
     * @param data 地形数据
     * @param options 导入选项
     * @returns Promise，解析为是否预览成功
     */
    previewThirdPartyFormat(format: ThirdPartyTerrainFormat, data: ArrayBuffer | string, options?: Omit<ThirdPartyTerrainImportOptions, 'format'>): Promise<boolean>;
    /**
     * 创建预览网格
     * @param terrain 地形组件
     */
    private createPreviewMesh;
    /**
     * 渲染
     */
    render(): void;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
    /**
     * 销毁
     */
    dispose(): void;
}
