/**
 * 主题管理服务
 * 管理应用程序的主题切换、自定义主题和主题配置
 */

export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

export interface ThemeConfig {
  id: string;
  name: string;
  description: string;
  type: 'light' | 'dark' | 'custom';
  colors: ThemeColors;
  fontFamily?: string;
  fontSize?: number;
  borderRadius?: number;
  shadows?: boolean;
  animations?: boolean;
  customCSS?: string;
}

export interface ThemePreferences {
  currentTheme: string;
  autoSwitch: boolean;
  switchTime?: {
    lightStart: string;
    darkStart: string;
  };
  customThemes: ThemeConfig[];
  accessibility: {
    highContrast: boolean;
    reducedMotion: boolean;
    largeText: boolean;
  };
}

class ThemeService {
  private static instance: ThemeService;
  private currentTheme: ThemeConfig | null = null;
  private preferences: ThemePreferences;
  private predefinedThemes: Map<string, ThemeConfig> = new Map();
  private customThemes: Map<string, ThemeConfig> = new Map();
  private observers: Set<(theme: ThemeConfig) => void> = new Set();

  constructor() {
    this.preferences = this.getDefaultPreferences();
    this.initializePredefinedThemes();
    this.loadUserPreferences();
    this.applyTheme();
    this.setupAutoSwitch();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): ThemeService {
    if (!ThemeService.instance) {
      ThemeService.instance = new ThemeService();
    }
    return ThemeService.instance;
  }

  /**
   * 获取默认偏好设置
   */
  private getDefaultPreferences(): ThemePreferences {
    return {
      currentTheme: 'light',
      autoSwitch: false,
      switchTime: {
        lightStart: '06:00',
        darkStart: '18:00'
      },
      customThemes: [],
      accessibility: {
        highContrast: false,
        reducedMotion: false,
        largeText: false
      }
    };
  }

  /**
   * 初始化预定义主题
   */
  private initializePredefinedThemes(): void {
    // 亮色主题
    this.predefinedThemes.set('light', {
      id: 'light',
      name: '亮色主题',
      description: '经典的亮色主题，适合白天使用',
      type: 'light',
      colors: {
        primary: '#1890ff',
        secondary: '#722ed1',
        background: '#ffffff',
        surface: '#f5f5f5',
        text: '#262626',
        textSecondary: '#8c8c8c',
        border: '#d9d9d9',
        success: '#52c41a',
        warning: '#faad14',
        error: '#ff4d4f',
        info: '#1890ff'
      },
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 14,
      borderRadius: 6,
      shadows: true,
      animations: true
    });

    // 暗色主题
    this.predefinedThemes.set('dark', {
      id: 'dark',
      name: '暗色主题',
      description: '护眼的暗色主题，适合夜间使用',
      type: 'dark',
      colors: {
        primary: '#177ddc',
        secondary: '#642ab5',
        background: '#141414',
        surface: '#1f1f1f',
        text: '#ffffff',
        textSecondary: '#a6a6a6',
        border: '#434343',
        success: '#49aa19',
        warning: '#d89614',
        error: '#dc4446',
        info: '#177ddc'
      },
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 14,
      borderRadius: 6,
      shadows: true,
      animations: true
    });

    // 高对比度主题
    this.predefinedThemes.set('high-contrast', {
      id: 'high-contrast',
      name: '高对比度',
      description: '高对比度主题，提升可访问性',
      type: 'dark',
      colors: {
        primary: '#00ff00',
        secondary: '#ffff00',
        background: '#000000',
        surface: '#1a1a1a',
        text: '#ffffff',
        textSecondary: '#cccccc',
        border: '#ffffff',
        success: '#00ff00',
        warning: '#ffff00',
        error: '#ff0000',
        info: '#00ffff'
      },
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 16,
      borderRadius: 2,
      shadows: false,
      animations: false
    });

    // 蓝色主题
    this.predefinedThemes.set('blue', {
      id: 'blue',
      name: '蓝色主题',
      description: '以蓝色为主色调的清新主题',
      type: 'light',
      colors: {
        primary: '#0066cc',
        secondary: '#4d94ff',
        background: '#f0f8ff',
        surface: '#e6f3ff',
        text: '#003366',
        textSecondary: '#666666',
        border: '#b3d9ff',
        success: '#00cc66',
        warning: '#ff9900',
        error: '#cc0000',
        info: '#0066cc'
      },
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 14,
      borderRadius: 8,
      shadows: true,
      animations: true
    });

    // 绿色主题
    this.predefinedThemes.set('green', {
      id: 'green',
      name: '绿色主题',
      description: '以绿色为主色调的自然主题',
      type: 'light',
      colors: {
        primary: '#52c41a',
        secondary: '#73d13d',
        background: '#f6ffed',
        surface: '#f0f9e8',
        text: '#135200',
        textSecondary: '#666666',
        border: '#b7eb8f',
        success: '#52c41a',
        warning: '#faad14',
        error: '#ff4d4f',
        info: '#1890ff'
      },
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 14,
      borderRadius: 6,
      shadows: true,
      animations: true
    });
  }

  /**
   * 加载用户偏好设置
   */
  private loadUserPreferences(): void {
    try {
      const saved = localStorage.getItem('themePreferences');
      if (saved) {
        const preferences = JSON.parse(saved);
        this.preferences = { ...this.preferences, ...preferences };
        
        // 加载自定义主题
        if (preferences.customThemes) {
          preferences.customThemes.forEach((theme: ThemeConfig) => {
            this.customThemes.set(theme.id, theme);
          });
        }
      }
    } catch (error) {
      console.error('加载主题偏好设置失败:', error);
    }
  }

  /**
   * 保存用户偏好设置
   */
  private saveUserPreferences(): void {
    try {
      const preferences = {
        ...this.preferences,
        customThemes: Array.from(this.customThemes.values())
      };
      localStorage.setItem('themePreferences', JSON.stringify(preferences));
    } catch (error) {
      console.error('保存主题偏好设置失败:', error);
    }
  }

  /**
   * 应用主题
   */
  private applyTheme(): void {
    const theme = this.getTheme(this.preferences.currentTheme);
    if (!theme) return;

    this.currentTheme = theme;

    // 应用CSS变量
    const root = document.documentElement;
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--theme-${key}`, value);
    });

    // 应用其他样式
    if (theme.fontFamily) {
      root.style.setProperty('--theme-font-family', theme.fontFamily);
    }
    if (theme.fontSize) {
      root.style.setProperty('--theme-font-size', `${theme.fontSize}px`);
    }
    if (theme.borderRadius) {
      root.style.setProperty('--theme-border-radius', `${theme.borderRadius}px`);
    }

    // 应用可访问性设置
    this.applyAccessibilitySettings();

    // 应用自定义CSS
    if (theme.customCSS) {
      this.applyCustomCSS(theme.customCSS);
    }

    // 通知观察者
    this.notifyObservers(theme);
  }

  /**
   * 应用可访问性设置
   */
  private applyAccessibilitySettings(): void {
    const root = document.documentElement;
    
    if (this.preferences.accessibility.reducedMotion) {
      root.style.setProperty('--theme-animation-duration', '0s');
    } else {
      root.style.removeProperty('--theme-animation-duration');
    }

    if (this.preferences.accessibility.largeText) {
      root.style.setProperty('--theme-font-size-multiplier', '1.2');
    } else {
      root.style.removeProperty('--theme-font-size-multiplier');
    }
  }

  /**
   * 应用自定义CSS
   */
  private applyCustomCSS(css: string): void {
    let styleElement = document.getElementById('custom-theme-css') as HTMLStyleElement;
    
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = 'custom-theme-css';
      document.head.appendChild(styleElement);
    }
    
    styleElement.textContent = css;
  }

  /**
   * 设置自动切换
   */
  private setupAutoSwitch(): void {
    if (!this.preferences.autoSwitch) return;

    const checkTime = () => {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      
      const { lightStart, darkStart } = this.preferences.switchTime!;
      
      if (currentTime >= lightStart && currentTime < darkStart) {
        if (this.currentTheme?.type !== 'light') {
          this.setTheme('light');
        }
      } else {
        if (this.currentTheme?.type !== 'dark') {
          this.setTheme('dark');
        }
      }
    };

    // 每分钟检查一次
    setInterval(checkTime, 60000);
    checkTime(); // 立即检查一次
  }

  /**
   * 获取主题
   */
  private getTheme(themeId: string): ThemeConfig | null {
    return this.predefinedThemes.get(themeId) || this.customThemes.get(themeId) || null;
  }

  /**
   * 通知观察者
   */
  private notifyObservers(theme: ThemeConfig): void {
    this.observers.forEach(observer => {
      try {
        observer(theme);
      } catch (error) {
        console.error('主题观察者执行失败:', error);
      }
    });
  }

  /**
   * 设置主题
   */
  public setTheme(themeId: string): void {
    const theme = this.getTheme(themeId);
    if (!theme) {
      console.warn(`主题 ${themeId} 不存在`);
      return;
    }

    this.preferences.currentTheme = themeId;
    this.applyTheme();
    this.saveUserPreferences();
  }

  /**
   * 切换主题
   */
  public toggleTheme(): void {
    const currentType = this.currentTheme?.type;
    const newThemeId = currentType === 'light' ? 'dark' : 'light';
    this.setTheme(newThemeId);
  }

  /**
   * 获取当前主题
   */
  public getCurrentTheme(): ThemeConfig | null {
    return this.currentTheme;
  }

  /**
   * 获取所有可用主题
   */
  public getAllThemes(): ThemeConfig[] {
    return [
      ...Array.from(this.predefinedThemes.values()),
      ...Array.from(this.customThemes.values())
    ];
  }

  /**
   * 创建自定义主题
   */
  public createCustomTheme(theme: Omit<ThemeConfig, 'id'>): string {
    const id = `custom-${Date.now()}`;
    const customTheme: ThemeConfig = { ...theme, id };
    
    this.customThemes.set(id, customTheme);
    this.saveUserPreferences();
    
    return id;
  }

  /**
   * 更新自定义主题
   */
  public updateCustomTheme(themeId: string, updates: Partial<ThemeConfig>): void {
    const theme = this.customThemes.get(themeId);
    if (theme) {
      Object.assign(theme, updates);
      this.saveUserPreferences();
      
      if (this.preferences.currentTheme === themeId) {
        this.applyTheme();
      }
    }
  }

  /**
   * 删除自定义主题
   */
  public deleteCustomTheme(themeId: string): void {
    if (this.customThemes.has(themeId)) {
      this.customThemes.delete(themeId);
      
      if (this.preferences.currentTheme === themeId) {
        this.setTheme('light');
      }
      
      this.saveUserPreferences();
    }
  }

  /**
   * 设置自动切换
   */
  public setAutoSwitch(enabled: boolean, switchTime?: { lightStart: string; darkStart: string }): void {
    this.preferences.autoSwitch = enabled;
    if (switchTime) {
      this.preferences.switchTime = switchTime;
    }
    
    this.saveUserPreferences();
    
    if (enabled) {
      this.setupAutoSwitch();
    }
  }

  /**
   * 设置可访问性选项
   */
  public setAccessibilityOptions(options: Partial<ThemePreferences['accessibility']>): void {
    this.preferences.accessibility = { ...this.preferences.accessibility, ...options };
    this.applyAccessibilitySettings();
    this.saveUserPreferences();
  }

  /**
   * 添加主题观察者
   */
  public addObserver(observer: (theme: ThemeConfig) => void): void {
    this.observers.add(observer);
  }

  /**
   * 移除主题观察者
   */
  public removeObserver(observer: (theme: ThemeConfig) => void): void {
    this.observers.delete(observer);
  }

  /**
   * 导出主题配置
   */
  public exportTheme(themeId: string): string | null {
    const theme = this.getTheme(themeId);
    return theme ? JSON.stringify(theme, null, 2) : null;
  }

  /**
   * 导入主题配置
   */
  public importTheme(themeData: string): string | null {
    try {
      const theme: ThemeConfig = JSON.parse(themeData);
      
      // 验证主题数据
      if (!theme.id || !theme.name || !theme.colors) {
        throw new Error('无效的主题数据');
      }
      
      // 生成新的ID避免冲突
      const newId = `imported-${Date.now()}`;
      theme.id = newId;
      
      this.customThemes.set(newId, theme);
      this.saveUserPreferences();
      
      return newId;
    } catch (error) {
      console.error('导入主题失败:', error);
      return null;
    }
  }

  /**
   * 重置为默认设置
   */
  public resetToDefaults(): void {
    this.preferences = this.getDefaultPreferences();
    this.customThemes.clear();
    this.setTheme('light');
    this.saveUserPreferences();
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.observers.clear();
    
    // 移除自定义CSS
    const styleElement = document.getElementById('custom-theme-css');
    if (styleElement) {
      styleElement.remove();
    }
  }
}

export default ThemeService;
