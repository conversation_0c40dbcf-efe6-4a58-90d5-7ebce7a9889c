/**
 * 模型管理模块
 */
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AIModel } from '../../entities/ai-model.entity';
import { ModelVersion } from '../../entities/model-version.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([AIModel, ModelVersion]),
  ],
  providers: [],
  controllers: [],
  exports: [TypeOrmModule],
})
export class ModelsModule {}
