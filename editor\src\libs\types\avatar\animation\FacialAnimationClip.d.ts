/**
 * 面部动画片段
 * 用于存储和管理面部动画数据
 */
import { FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';
/**
 * 面部动画关键帧
 */
export interface FacialAnimationKeyframe {
    /** 时间（秒） */
    time: number;
    /** 值 */
    value: any;
    /** 权重 */
    weight: number;
    /** 插值类型 */
    interpolation?: 'linear' | 'step' | 'cubic';
    /** 进入切线 */
    inTangent?: number;
    /** 退出切线 */
    outTangent?: number;
}
/**
 * 表情关键帧
 */
export interface ExpressionKeyframe extends FacialAnimationKeyframe {
    /** 表情类型 */
    expression: FacialExpressionType;
}
/**
 * 口型关键帧
 */
export interface VisemeKeyframe extends FacialAnimationKeyframe {
    /** 口型类型 */
    viseme: VisemeType;
}
/**
 * 混合形状关键帧
 */
export interface BlendShapeKeyframe extends FacialAnimationKeyframe {
    /** 混合形状名称 */
    blendShapeName: string;
}
/**
 * 面部动画片段
 */
export declare class FacialAnimationClip {
    /** 名称 */
    name: string;
    /** 持续时间（秒） */
    duration: number;
    /** 是否循环 */
    loop: boolean;
    /** 表情关键帧 */
    private expressionKeyframes;
    /** 口型关键帧 */
    private visemeKeyframes;
    /** 混合形状关键帧 */
    private blendShapeKeyframes;
    /** 自定义数据 */
    private userData;
    /**
     * 构造函数
     * @param name 名称
     */
    constructor(name: string);
    /**
     * 添加表情关键帧
     * @param time 时间（秒）
     * @param expression 表情类型
     * @param weight 权重
     * @param interpolation 插值类型
     * @returns 关键帧索引
     */
    addExpressionKeyframe(time: number, expression: FacialExpressionType, weight?: number, interpolation?: 'linear' | 'step' | 'cubic'): number;
    /**
     * 添加口型关键帧
     * @param time 时间（秒）
     * @param viseme 口型类型
     * @param weight 权重
     * @param interpolation 插值类型
     * @returns 关键帧索引
     */
    addVisemeKeyframe(time: number, viseme: VisemeType, weight?: number, interpolation?: 'linear' | 'step' | 'cubic'): number;
    /**
     * 添加混合形状关键帧
     * @param time 时间（秒）
     * @param blendShapeName 混合形状名称
     * @param weight 权重
     * @param interpolation 插值类型
     * @returns 关键帧索引
     */
    addBlendShapeKeyframe(time: number, blendShapeName: string, weight?: number, interpolation?: 'linear' | 'step' | 'cubic'): number;
    /**
     * 移除表情关键帧
     * @param index 索引
     * @returns 是否成功移除
     */
    removeExpressionKeyframe(index: number): boolean;
    /**
     * 移除口型关键帧
     * @param index 索引
     * @returns 是否成功移除
     */
    removeVisemeKeyframe(index: number): boolean;
    /**
     * 移除混合形状关键帧
     * @param index 索引
     * @returns 是否成功移除
     */
    removeBlendShapeKeyframe(index: number): boolean;
    /**
     * 获取表情关键帧
     * @param index 索引
     * @returns 表情关键帧
     */
    getExpressionKeyframe(index: number): ExpressionKeyframe | null;
    /**
     * 获取口型关键帧
     * @param index 索引
     * @returns 口型关键帧
     */
    getVisemeKeyframe(index: number): VisemeKeyframe | null;
    /**
     * 获取混合形状关键帧
     * @param index 索引
     * @returns 混合形状关键帧
     */
    getBlendShapeKeyframe(index: number): BlendShapeKeyframe | null;
    /**
     * 获取所有表情关键帧
     * @returns 表情关键帧数组
     */
    getAllExpressionKeyframes(): ExpressionKeyframe[];
    /**
     * 获取所有口型关键帧
     * @returns 口型关键帧数组
     */
    getAllVisemeKeyframes(): VisemeKeyframe[];
    /**
     * 获取所有混合形状关键帧
     * @returns 混合形状关键帧数组
     */
    getAllBlendShapeKeyframes(): BlendShapeKeyframe[];
    /**
     * 在指定时间获取表情
     * @param time 时间（秒）
     * @returns 表情和权重
     */
    getExpressionAtTime(time: number): {
        expression: FacialExpressionType;
        weight: number;
    };
    /**
     * 在指定时间获取口型
     * @param time 时间（秒）
     * @returns 口型和权重
     */
    getVisemeAtTime(time: number): {
        viseme: VisemeType;
        weight: number;
    };
    /**
     * 设置自定义数据
     * @param key 键
     * @param value 值
     */
    setUserData(key: string, value: any): void;
    /**
     * 获取自定义数据
     * @param key 键
     * @returns 值
     */
    getUserData(key: string): any;
    /**
     * 清除所有关键帧
     */
    clear(): void;
    /**
     * 克隆
     * @returns 克隆的面部动画片段
     */
    clone(): FacialAnimationClip;
    /**
     * 转换为JSON
     * @returns JSON对象
     */
    toJSON(): any;
    /**
     * 从JSON创建
     * @param json JSON对象
     * @returns 面部动画片段
     */
    static fromJSON(json: any): FacialAnimationClip;
}
