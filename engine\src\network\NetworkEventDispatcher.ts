/**
 * 网络事件分发器
 * 负责处理网络事件的分发
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';
import { NetworkEvent, NetworkEventType } from './NetworkEvent';
import { NetworkEventBuffer, EventPriority } from './NetworkEventBuffer';

/**
 * 事件处理器
 */
export type EventHandler = (event: NetworkEvent) => void;

/**
 * 事件过滤器
 */
export type EventFilter = (event: NetworkEvent) => boolean;

/**
 * 事件订阅
 */
interface EventSubscription {
  /** 事件类型 */
  type: string;
  /** 处理器 */
  handler: EventHandler;
  /** 过滤器 */
  filter?: EventFilter;
  /** 优先级 */
  priority: EventPriority;
  /** 是否只处理一次 */
  once: boolean;
  /** 订阅ID */
  id: string;
}

/**
 * 网络事件分发器配置
 */
export interface NetworkEventDispatcherConfig {
  /** 是否使用事件缓冲 */
  useEventBuffer?: boolean;
  /** 事件缓冲配置 */
  eventBufferConfig?: {
    /** 最大缓冲事件数量 */
    maxBufferSize?: number;
    /** 事件处理间隔（毫秒） */
    processInterval?: number;
    /** 是否自动处理事件 */
    autoProcess?: boolean;
    /** 每次处理的最大事件数量 */
    maxEventsPerProcess?: number;
  };
  /** 是否启用事件日志 */
  enableEventLogging?: boolean;
  /** 日志级别 */
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  /** 是否允许默认处理器 */
  allowDefaultHandlers?: boolean;
}

/**
 * 网络事件分发器
 * 负责处理网络事件的分发
 */
export class NetworkEventDispatcher extends EventEmitter {
  /** 配置 */
  private config: Required<NetworkEventDispatcherConfig>;
  
  /** 事件缓冲器 */
  private eventBuffer: NetworkEventBuffer | null = null;
  
  /** 事件订阅映射表 */
  private subscriptions: Map<string, EventSubscription[]> = new Map();
  
  /** 默认事件处理器映射表 */
  private defaultHandlers: Map<string, EventHandler> = new Map();
  
  /** 下一个订阅ID */
  private nextSubscriptionId: number = 1;
  
  /**
   * 创建网络事件分发器
   * @param config 配置
   */
  constructor(config: NetworkEventDispatcherConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      useEventBuffer: true,
      eventBufferConfig: {
        maxBufferSize: 1000,
        processInterval: 16, // 约60fps
        autoProcess: true,
        maxEventsPerProcess: 10,
      },
      enableEventLogging: false,
      logLevel: 'info',
      allowDefaultHandlers: true,
      ...config,
    };
    
    // 如果使用事件缓冲，则创建事件缓冲器
    if (this.config.useEventBuffer) {
      this.eventBuffer = new NetworkEventBuffer(this.config.eventBufferConfig);
      this.eventBuffer.setEventHandler(this.processEvent.bind(this));
    }
    
    // 初始化默认事件处理器
    this.initDefaultHandlers();
  }
  
  /**
   * 初始化默认事件处理器
   */
  private initDefaultHandlers(): void {
    if (!this.config.allowDefaultHandlers) {
      return;
    }
    
    // 系统错误处理器
    this.setDefaultHandler(NetworkEventType.SYSTEM_ERROR, (event) => {
      Debug.error('NetworkEventDispatcher', 'System error:', event.data);
    });
    
    // 系统警告处理器
    this.setDefaultHandler(NetworkEventType.SYSTEM_WARNING, (event) => {
      Debug.warn('NetworkEventDispatcher', 'System warning:', event.data);
    });
    
    // 系统信息处理器
    this.setDefaultHandler(NetworkEventType.SYSTEM_INFO, (event) => {
      Debug.log('NetworkEventDispatcher', 'System info:', event.data);
    });
  }
  
  /**
   * 设置默认事件处理器
   * @param eventType 事件类型
   * @param handler 处理器
   */
  public setDefaultHandler(eventType: string, handler: EventHandler): void {
    if (!this.config.allowDefaultHandlers) {
      Debug.warn('NetworkEventDispatcher', 'Default handlers are not allowed');
      return;
    }
    
    this.defaultHandlers.set(eventType, handler);
  }
  
  /**
   * 移除默认事件处理器
   * @param eventType 事件类型
   */
  public removeDefaultHandler(eventType: string): void {
    this.defaultHandlers.delete(eventType);
  }
  
  /**
   * 分发事件
   * @param event 网络事件
   */
  public dispatchEvent(event: NetworkEvent): void {
    // 记录事件日志
    this.logEvent(event);
    
    // 如果使用事件缓冲，则添加到缓冲区
    if (this.eventBuffer) {
      this.eventBuffer.addEvent(event);
    } else {
      // 否则直接处理事件
      this.processEvent(event);
    }
  }
  
  /**
   * 处理事件
   * @param event 网络事件
   */
  private processEvent(event: NetworkEvent): void {
    // 获取事件类型的订阅
    const subscriptions = this.subscriptions.get(event.type) || [];
    
    // 按优先级排序
    subscriptions.sort((a, b) => a.priority - b.priority);
    
    // 标记是否已处理
    let handled = false;
    
    // 处理订阅
    const removeSubscriptions: string[] = [];
    
    for (const subscription of subscriptions) {
      // 检查过滤器
      if (subscription.filter && !subscription.filter(event)) {
        continue;
      }
      
      try {
        // 调用处理器
        subscription.handler(event);
        
        // 标记为已处理
        handled = true;
        
        // 如果是一次性订阅，则标记为移除
        if (subscription.once) {
          removeSubscriptions.push(subscription.id);
        }
      } catch (error) {
        Debug.error('NetworkEventDispatcher', `Error in event handler for ${event.type}:`, error);
      }
    }
    
    // 移除一次性订阅
    for (const id of removeSubscriptions) {
      this.unsubscribeById(id);
    }
    
    // 如果未处理且存在默认处理器，则调用默认处理器
    if (!handled && this.config.allowDefaultHandlers) {
      const defaultHandler = this.defaultHandlers.get(event.type);
      if (defaultHandler) {
        try {
          defaultHandler(event);
        } catch (error) {
          Debug.error('NetworkEventDispatcher', `Error in default handler for ${event.type}:`, error);
        }
      }
    }
    
    // 标记事件为已处理
    event.handled = true;
  }
  
  /**
   * 记录事件日志
   * @param event 网络事件
   */
  private logEvent(event: NetworkEvent): void {
    if (!this.config.enableEventLogging) {
      return;
    }
    
    const message = `Event: ${event.type}, Sender: ${event.senderId || 'unknown'}`;
    
    switch (this.config.logLevel) {
      case 'debug':
        Debug.log('NetworkEventDispatcher', message, event);
        break;
        
      case 'info':
        Debug.log('NetworkEventDispatcher', message);
        break;
        
      case 'warn':
        Debug.warn('NetworkEventDispatcher', message);
        break;
        
      case 'error':
        Debug.error('NetworkEventDispatcher', message);
        break;
    }
  }
  
  /**
   * 订阅事件
   * @param eventType 事件类型
   * @param handler 处理器
   * @param options 选项
   * @returns 订阅ID
   */
  public subscribe(
    eventType: string,
    handler: EventHandler,
    options: {
      filter?: EventFilter;
      priority?: EventPriority;
      once?: boolean;
    } = {}
  ): string {
    // 生成订阅ID
    const id = `sub_${this.nextSubscriptionId++}`;
    
    // 创建订阅
    const subscription: EventSubscription = {
      type: eventType,
      handler,
      filter: options.filter,
      priority: options.priority !== undefined ? options.priority : EventPriority.MEDIUM,
      once: options.once || false,
      id,
    };
    
    // 添加到订阅映射表
    if (!this.subscriptions.has(eventType)) {
      this.subscriptions.set(eventType, []);
    }
    
    this.subscriptions.get(eventType)!.push(subscription);
    
    return id;
  }
  
  /**
   * 取消订阅
   * @param eventType 事件类型
   * @param handler 处理器
   * @returns 是否成功取消
   */
  public unsubscribe(eventType: string, handler: EventHandler): boolean {
    const subscriptions = this.subscriptions.get(eventType);
    if (!subscriptions) {
      return false;
    }
    
    // 查找处理器的索引
    const index = subscriptions.findIndex(sub => sub.handler === handler);
    if (index === -1) {
      return false;
    }
    
    // 移除订阅
    subscriptions.splice(index, 1);
    
    // 如果没有订阅了，则移除事件类型
    if (subscriptions.length === 0) {
      this.subscriptions.delete(eventType);
    }
    
    return true;
  }
  
  /**
   * 通过ID取消订阅
   * @param id 订阅ID
   * @returns 是否成功取消
   */
  public unsubscribeById(id: string): boolean {
    for (const [eventType, subscriptions] of this.subscriptions.entries()) {
      // 查找订阅的索引
      const index = subscriptions.findIndex(sub => sub.id === id);
      if (index !== -1) {
        // 移除订阅
        subscriptions.splice(index, 1);
        
        // 如果没有订阅了，则移除事件类型
        if (subscriptions.length === 0) {
          this.subscriptions.delete(eventType);
        }
        
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * 取消所有订阅
   * @param eventType 事件类型（可选，如果不指定则取消所有事件类型的订阅）
   */
  public unsubscribeAll(eventType?: string): void {
    if (eventType) {
      // 移除指定事件类型的所有订阅
      this.subscriptions.delete(eventType);
    } else {
      // 移除所有订阅
      this.subscriptions.clear();
    }
  }
  
  /**
   * 创建事件
   * @param type 事件类型
   * @param data 事件数据
   * @param senderId 发送者ID
   * @param receiverId 接收者ID
   * @param priority 优先级
   * @returns 网络事件
   */
  public createEvent(
    type: string,
    data?: any,
    senderId?: string,
    receiverId?: string,
    priority?: EventPriority
  ): NetworkEvent {
    return {
      type,
      data,
      senderId,
      receiverId,
      timestamp: Date.now(),
      priority,
      handled: false,
    };
  }
  
  /**
   * 获取事件订阅数量
   * @param eventType 事件类型（可选，如果不指定则返回所有事件类型的订阅数量）
   * @returns 订阅数量
   */
  public getSubscriptionCount(eventType?: string): number {
    if (eventType) {
      // 返回指定事件类型的订阅数量
      return this.subscriptions.get(eventType)?.length || 0;
    } else {
      // 返回所有事件类型的订阅数量
      let count = 0;
      for (const subscriptions of this.subscriptions.values()) {
        count += subscriptions.length;
      }
      return count;
    }
  }
  
  /**
   * 获取事件类型列表
   * @returns 事件类型列表
   */
  public getEventTypes(): string[] {
    return Array.from(this.subscriptions.keys());
  }
  
  /**
   * 销毁分发器
   */
  public dispose(): void {
    // 销毁事件缓冲器
    if (this.eventBuffer) {
      (this.eventBuffer as any).dispose();
      this.eventBuffer = null;
    }
    
    // 清空订阅
    this.subscriptions.clear();
    
    // 清空默认处理器
    this.defaultHandlers.clear();
    
    // 移除所有监听器
    this.removeAllListeners();
  }
}
