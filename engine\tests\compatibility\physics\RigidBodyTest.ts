/**
 * 刚体兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 刚体兼容性测试
 */
export const rigidBodyTest: TestCase = {
  name: '刚体兼容性测试',
  description: '测试刚体功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目物理系统实例
      const originalPhysicsSystem = new original.PhysicsSystem();
      originalPhysicsSystem.initialize();
      
      // 创建重构后项目物理系统实例
      const refactoredPhysicsSystem = new refactored.PhysicsSystem();
      refactoredPhysicsSystem.initialize();
      
      // 创建原有项目实体
      const originalEntity = new original.Entity('TestEntity');
      
      // 创建重构后项目实体
      const refactoredEntity = new refactored.Entity('TestEntity');
      
      // 创建原有项目刚体组件
      const originalBodyComponent = new original.PhysicsBodyComponent(originalEntity, {
        mass: 1,
        type: 'dynamic'
      });
      
      // 创建重构后项目刚体组件
      const refactoredBodyComponent = new refactored.PhysicsBodyComponent(refactoredEntity, {
        mass: 1,
        type: 'dynamic'
      });
      
      // 添加组件到实体
      originalEntity.addComponent(originalBodyComponent);
      refactoredEntity.addComponent(refactoredBodyComponent);
      
      // 注册组件到物理系统
      originalPhysicsSystem.registerPhysicsBodyComponent(originalEntity, originalBodyComponent);
      refactoredPhysicsSystem.registerPhysicsBodyComponent(refactoredEntity, refactoredBodyComponent);
      
      // 检查刚体属性
      if (originalBodyComponent.mass !== refactoredBodyComponent.mass) {
        return {
          name: '刚体兼容性测试',
          passed: false,
          errorMessage: `刚体质量不一致: 原有项目=${originalBodyComponent.mass}, 重构后项目=${refactoredBodyComponent.mass}`,
          details: {
            originalMass: originalBodyComponent.mass,
            refactoredMass: refactoredBodyComponent.mass
          }
        };
      }
      
      if (originalBodyComponent.type !== refactoredBodyComponent.type) {
        return {
          name: '刚体兼容性测试',
          passed: false,
          errorMessage: `刚体类型不一致: 原有项目=${originalBodyComponent.type}, 重构后项目=${refactoredBodyComponent.type}`,
          details: {
            originalType: originalBodyComponent.type,
            refactoredType: refactoredBodyComponent.type
          }
        };
      }
      
      // 设置刚体位置
      const position = { x: 1, y: 2, z: 3 };
      originalBodyComponent.setPosition(position);
      refactoredBodyComponent.setPosition(position);
      
      // 检查刚体位置
      const originalPosition = originalBodyComponent.getPosition();
      const refactoredPosition = refactoredBodyComponent.getPosition();
      
      if (
        Math.abs(originalPosition.x - refactoredPosition.x) > 0.001 ||
        Math.abs(originalPosition.y - refactoredPosition.y) > 0.001 ||
        Math.abs(originalPosition.z - refactoredPosition.z) > 0.001
      ) {
        return {
          name: '刚体兼容性测试',
          passed: false,
          errorMessage: `刚体位置不一致: 原有项目=${JSON.stringify(originalPosition)}, 重构后项目=${JSON.stringify(refactoredPosition)}`,
          details: {
            originalPosition,
            refactoredPosition
          }
        };
      }
      
      // 设置刚体旋转
      const rotation = { x: 0, y: 0.707, z: 0, w: 0.707 };
      originalBodyComponent.setRotation(rotation);
      refactoredBodyComponent.setRotation(rotation);
      
      // 检查刚体旋转
      const originalRotation = originalBodyComponent.getRotation();
      const refactoredRotation = refactoredBodyComponent.getRotation();
      
      if (
        Math.abs(originalRotation.x - refactoredRotation.x) > 0.001 ||
        Math.abs(originalRotation.y - refactoredRotation.y) > 0.001 ||
        Math.abs(originalRotation.z - refactoredRotation.z) > 0.001 ||
        Math.abs(originalRotation.w - refactoredRotation.w) > 0.001
      ) {
        return {
          name: '刚体兼容性测试',
          passed: false,
          errorMessage: `刚体旋转不一致: 原有项目=${JSON.stringify(originalRotation)}, 重构后项目=${JSON.stringify(refactoredRotation)}`,
          details: {
            originalRotation,
            refactoredRotation
          }
        };
      }
      
      // 应用力
      const force = { x: 10, y: 0, z: 0 };
      originalBodyComponent.applyForce(force);
      refactoredBodyComponent.applyForce(force);
      
      // 更新物理系统
      originalPhysicsSystem.update(0.016);
      refactoredPhysicsSystem.update(0.016);
      
      // 检查刚体速度
      const originalVelocity = originalBodyComponent.getLinearVelocity();
      const refactoredVelocity = refactoredBodyComponent.getLinearVelocity();
      
      // 注意：由于物理引擎的实现差异，速度可能不完全相同，所以我们只检查速度方向是否一致
      const originalVelocityDirection = Math.sign(originalVelocity.x);
      const refactoredVelocityDirection = Math.sign(refactoredVelocity.x);
      
      if (originalVelocityDirection !== refactoredVelocityDirection) {
        return {
          name: '刚体兼容性测试',
          passed: false,
          errorMessage: `刚体速度方向不一致: 原有项目=${originalVelocityDirection}, 重构后项目=${refactoredVelocityDirection}`,
          details: {
            originalVelocity,
            refactoredVelocity
          }
        };
      }
      
      // 销毁物理系统
      originalPhysicsSystem.destroy();
      refactoredPhysicsSystem.destroy();
      
      return {
        name: '刚体兼容性测试',
        passed: true,
        details: {
          originalPosition,
          refactoredPosition,
          originalRotation,
          refactoredRotation,
          originalVelocity,
          refactoredVelocity
        }
      };
    } catch (error) {
      return {
        name: '刚体兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
