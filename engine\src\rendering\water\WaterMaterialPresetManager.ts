/**
 * 水体材质预设管理器
 * 用于管理和应用水体材质预设
 */
import * as THREE from 'three';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { WaterMaterial, WaterMaterialConfig } from './WaterMaterial';
import { WaterBodyType } from '../../physics/water/WaterBodyComponent';

/**
 * 水体材质预设接口
 */
export interface WaterMaterialPreset {
  /** 预设ID */
  id: string;
  /** 预设名称 */
  name: string;
  /** 预设描述 */
  description: string;
  /** 预设类别 */
  category: string;
  /** 预设标签 */
  tags: string[];
  /** 预设缩略图 */
  thumbnail?: string;
  /** 预设作者 */
  author?: string;
  /** 水体材质配置 */
  config: WaterMaterialConfig;
}

/**
 * 水体材质预设管理器配置
 */
export interface WaterMaterialPresetManagerConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否从服务器加载预设 */
  loadFromServer?: boolean;
  /** 服务器URL */
  serverUrl?: string;
}

/**
 * 水体材质预设管理器
 */
export class WaterMaterialPresetManager extends EventEmitter {
  /** 预设映射 */
  private presets: Map<string, WaterMaterialPreset> = new Map();
  /** 预设类别集合 */
  private categories: Set<string> = new Set();
  /** 预设标签集合 */
  private tags: Set<string> = new Set();
  /** 配置 */
  private config: WaterMaterialPresetManagerConfig;
  /** 单例实例 */
  private static instance: WaterMaterialPresetManager;

  /**
   * 构造函数
   * @param config 配置
   */
  private constructor(config: WaterMaterialPresetManagerConfig = {}) {
    super();
    this.config = {
      debug: config.debug !== undefined ? config.debug : false,
      loadFromServer: config.loadFromServer !== undefined ? config.loadFromServer : false,
      serverUrl: config.serverUrl || ''
    };

    // 初始化默认预设
    this.initializeDefaultPresets();

    // 如果启用从服务器加载，则从服务器加载预设
    if (this.config.loadFromServer) {
      this.loadFromServer();
    }
  }

  /**
   * 获取单例实例
   * @param config 配置
   * @returns 水体材质预设管理器实例
   */
  public static getInstance(config: WaterMaterialPresetManagerConfig = {}): WaterMaterialPresetManager {
    if (!WaterMaterialPresetManager.instance) {
      WaterMaterialPresetManager.instance = new WaterMaterialPresetManager(config);
    }
    return WaterMaterialPresetManager.instance;
  }

  /**
   * 初始化默认预设
   */
  private initializeDefaultPresets(): void {
    // 添加海洋水体预设
    this.addPreset({
      id: 'ocean_deep_blue',
      name: '深蓝海洋',
      description: '深蓝色海洋水体预设，适合开阔海域',
      category: '海洋',
      tags: ['海洋', '蓝色', '波浪'],
      config: {
        type: WaterBodyType.OCEAN,
        color: new THREE.Color(0x0055aa),
        opacity: 0.8,
        reflectivity: 0.6,
        refractionRatio: 0.98,
        waveStrength: 0.2,
        waveSpeed: 0.8,
        waveScale: 5.0,
        depth: 10.0,
        depthColor: new THREE.Color(0x000033),
        shallowColor: new THREE.Color(0x0077cc),
        enableCaustics: true,
        enableFoam: true,
        enableUnderwaterFog: true,
        enableUnderwaterDistortion: true
      }
    });

    // 添加湖泊水体预设
    this.addPreset({
      id: 'lake_clear',
      name: '清澈湖泊',
      description: '清澈的湖泊水体预设，适合平静的湖面',
      category: '湖泊',
      tags: ['湖泊', '清澈', '平静'],
      config: {
        type: WaterBodyType.LAKE,
        color: new THREE.Color(0x3399ff),
        opacity: 0.7,
        reflectivity: 0.5,
        refractionRatio: 0.98,
        waveStrength: 0.05,
        waveSpeed: 0.3,
        waveScale: 3.0,
        depth: 5.0,
        depthColor: new THREE.Color(0x0055aa),
        shallowColor: new THREE.Color(0x66ccff),
        enableCaustics: true,
        enableFoam: false,
        enableUnderwaterFog: true,
        enableUnderwaterDistortion: true
      }
    });

    // 添加河流水体预设
    this.addPreset({
      id: 'river_flowing',
      name: '流动河流',
      description: '流动的河流水体预设，适合有流动感的河流',
      category: '河流',
      tags: ['河流', '流动', '清澈'],
      config: {
        type: WaterBodyType.RIVER,
        color: new THREE.Color(0x66aaff),
        opacity: 0.7,
        reflectivity: 0.4,
        refractionRatio: 0.97,
        waveStrength: 0.1,
        waveSpeed: 1.0,
        waveScale: 2.0,
        depth: 3.0,
        depthColor: new THREE.Color(0x3377aa),
        shallowColor: new THREE.Color(0x99ccff),
        enableCaustics: true,
        enableFoam: true,
        enableUnderwaterFog: false,
        enableUnderwaterDistortion: true
      }
    });

    // 添加地下湖泊水体预设
    this.addPreset({
      id: 'underground_lake',
      name: '地下湖泊',
      description: '地下湖泊水体预设，适合洞穴中的湖泊',
      category: '地下水体',
      tags: ['地下', '湖泊', '黑暗'],
      config: {
        type: WaterBodyType.UNDERGROUND_LAKE,
        color: new THREE.Color(0x225577),
        opacity: 0.9,
        reflectivity: 0.3,
        refractionRatio: 0.98,
        waveStrength: 0.02,
        waveSpeed: 0.2,
        waveScale: 4.0,
        depth: 8.0,
        depthColor: new THREE.Color(0x001122),
        shallowColor: new THREE.Color(0x336688),
        enableCaustics: false,
        enableFoam: false,
        enableUnderwaterFog: true,
        enableUnderwaterDistortion: true
      }
    });

    // 添加地下河流水体预设
    this.addPreset({
      id: 'underground_river',
      name: '地下河流',
      description: '地下河流水体预设，适合洞穴中的河流',
      category: '地下水体',
      tags: ['地下', '河流', '流动'],
      config: {
        type: WaterBodyType.UNDERGROUND_RIVER,
        color: new THREE.Color(0x224466),
        opacity: 0.85,
        reflectivity: 0.3,
        refractionRatio: 0.97,
        waveStrength: 0.08,
        waveSpeed: 0.6,
        waveScale: 2.0,
        depth: 4.0,
        depthColor: new THREE.Color(0x001133),
        shallowColor: new THREE.Color(0x335577),
        enableCaustics: false,
        enableFoam: true,
        enableUnderwaterFog: true,
        enableUnderwaterDistortion: true
      }
    });

    // 添加温泉水体预设
    this.addPreset({
      id: 'hot_spring',
      name: '温泉',
      description: '温泉水体预设，适合温泉场景',
      category: '特殊水体',
      tags: ['温泉', '热', '气泡'],
      config: {
        type: WaterBodyType.HOT_SPRING,
        color: new THREE.Color(0x77aacc),
        opacity: 0.7,
        reflectivity: 0.4,
        refractionRatio: 0.96,
        waveStrength: 0.03,
        waveSpeed: 0.4,
        waveScale: 1.5,
        depth: 2.0,
        depthColor: new THREE.Color(0x5588aa),
        shallowColor: new THREE.Color(0x99ccee),
        enableCaustics: false,
        enableFoam: true,
        enableUnderwaterFog: true,
        enableUnderwaterDistortion: true
      }
    });

    // 添加瀑布水体预设
    this.addPreset({
      id: 'waterfall',
      name: '瀑布',
      description: '瀑布水体预设，适合瀑布场景',
      category: '特殊水体',
      tags: ['瀑布', '流动', '白色'],
      config: {
        type: WaterBodyType.WATERFALL,
        color: new THREE.Color(0xccddff),
        opacity: 0.6,
        reflectivity: 0.3,
        refractionRatio: 0.95,
        waveStrength: 0.3,
        waveSpeed: 2.0,
        waveScale: 1.0,
        depth: 1.0,
        depthColor: new THREE.Color(0x99bbdd),
        shallowColor: new THREE.Color(0xeeffff),
        enableCaustics: false,
        enableFoam: true,
        enableUnderwaterFog: false,
        enableUnderwaterDistortion: true
      }
    });
  }

  /**
   * 从服务器加载预设
   */
  private loadFromServer(): void {
    // 实现从服务器加载预设的逻辑
    // 这里只是一个占位，实际实现需要根据服务器API进行
    if (this.config.debug) {
      Debug.log('WaterMaterialPresetManager', '从服务器加载预设...');
    }
  }

  /**
   * 添加预设
   * @param preset 水体材质预设
   */
  public addPreset(preset: WaterMaterialPreset): void {
    if (this.presets.has(preset.id)) {
      Debug.warn(`预设 "${preset.id}" 已存在，将被覆盖`);
    }
    
    this.presets.set(preset.id, preset);
    this.categories.add(preset.category);
    
    // 添加标签
    preset.tags.forEach(tag => this.tags.add(tag));
    
    // 发出预设添加事件
    this.emit('presetAdded', preset);
    
    if (this.config.debug) {
      Debug.log('WaterMaterialPresetManager', `添加预设: ${preset.id}`);
    }
  }

  /**
   * 获取预设
   * @param id 预设ID
   * @returns 水体材质预设
   */
  public getPreset(id: string): WaterMaterialPreset | undefined {
    return this.presets.get(id);
  }

  /**
   * 获取所有预设
   * @returns 所有水体材质预设
   */
  public getAllPresets(): WaterMaterialPreset[] {
    return Array.from(this.presets.values());
  }

  /**
   * 获取指定类别的预设
   * @param category 预设类别
   * @returns 指定类别的水体材质预设
   */
  public getPresetsByCategory(category: string): WaterMaterialPreset[] {
    return this.getAllPresets().filter(preset => preset.category === category);
  }

  /**
   * 获取包含指定标签的预设
   * @param tag 预设标签
   * @returns 包含指定标签的水体材质预设
   */
  public getPresetsByTag(tag: string): WaterMaterialPreset[] {
    return this.getAllPresets().filter(preset => preset.tags.includes(tag));
  }

  /**
   * 获取所有类别
   * @returns 所有预设类别
   */
  public getAllCategories(): string[] {
    return Array.from(this.categories);
  }

  /**
   * 获取所有标签
   * @returns 所有预设标签
   */
  public getAllTags(): string[] {
    return Array.from(this.tags);
  }

  /**
   * 应用预设到水体材质
   * @param material 水体材质
   * @param presetId 预设ID
   * @returns 是否应用成功
   */
  public applyPreset(material: WaterMaterial, presetId: string): boolean {
    const preset = this.presets.get(presetId);
    if (!preset) {
      Debug.warn(`无法应用预设 "${presetId}": 预设不存在`);
      return false;
    }

    // 应用预设配置到材质
    const config = preset.config;
    
    // 设置基本属性
    if (config.color) material.setColor(config.color);
    if (config.opacity !== undefined) material.setOpacity(config.opacity);
    if (config.reflectivity !== undefined) material.setReflectivity(config.reflectivity);
    if (config.refractionRatio !== undefined) material.setRefractionRatio(config.refractionRatio);
    
    // 设置波动属性
    if (config.waveStrength !== undefined) material.setWaveStrength(config.waveStrength);
    if (config.waveSpeed !== undefined) material.setWaveSpeed(config.waveSpeed);
    if (config.waveScale !== undefined) material.setWaveScale(config.waveScale);
    if (config.waveDirection) material.setWaveDirection(config.waveDirection);
    
    // 设置深度属性
    if (config.depth !== undefined) material.setDepth(config.depth);
    if (config.depthColor) material.setDepthColor(config.depthColor);
    if (config.shallowColor) material.setShallowColor(config.shallowColor);
    
    // 设置特效
    if (config.enableCaustics !== undefined) material.enableCaustics(config.enableCaustics);
    if (config.enableFoam !== undefined) material.enableFoam(config.enableFoam);
    if (config.enableUnderwaterFog !== undefined) material.enableUnderwaterFog(config.enableUnderwaterFog);
    if (config.enableUnderwaterDistortion !== undefined) material.enableUnderwaterDistortion(config.enableUnderwaterDistortion);
    
    // 发出预设应用事件
    this.emit('presetApplied', { material, preset });
    
    if (this.config.debug) {
      Debug.log('WaterMaterialPresetManager', `应用预设 "${presetId}" 到材质`);
    }
    
    return true;
  }

  /**
   * 创建基于预设的水体材质
   * @param presetId 预设ID
   * @returns 水体材质
   */
  public createMaterialFromPreset(presetId: string): WaterMaterial | null {
    const preset = this.presets.get(presetId);
    if (!preset) {
      Debug.warn(`无法创建材质: 预设 "${presetId}" 不存在`);
      return null;
    }

    // 创建新的水体材质
    const material = new WaterMaterial(preset.config);
    
    // 发出材质创建事件
    this.emit('materialCreated', { material, preset });
    
    if (this.config.debug) {
      Debug.log('WaterMaterialPresetManager', `从预设 "${presetId}" 创建材质`);
    }
    
    return material;
  }
}
