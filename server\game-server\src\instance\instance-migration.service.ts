import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InstanceService } from './instance.service';
import { AgonesService } from '../agones/agones.service';
import { Instance } from './instance.interface';

/**
 * 实例迁移服务
 * 负责处理游戏实例的迁移，包括实例状态转移和用户重定向
 */
@Injectable()
export class InstanceMigrationService {
  private readonly logger = new Logger(InstanceMigrationService.name);
  private migrationInProgress: Map<string, {
    sourceInstanceId: string;
    targetInstanceId: string;
    startTime: Date;
    status: 'preparing' | 'transferring' | 'redirecting' | 'completed' | 'failed';
    users: string[];
    error?: string;
  }> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly instanceService: InstanceService,
    private readonly agonesService: AgonesService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // 监听实例关闭事件，自动迁移用户
    this.eventEmitter.on('instance.closing', this.handleInstanceClosing.bind(this));
    
    // 监听迁移完成事件
    this.eventEmitter.on('instance.migration.completed', this.handleMigrationCompleted.bind(this));
    
    // 监听迁移失败事件
    this.eventEmitter.on('instance.migration.failed', this.handleMigrationFailed.bind(this));
  }

  /**
   * 处理实例关闭事件
   * @param data 事件数据
   */
  private async handleInstanceClosing(data: { instance: Instance }): Promise<void> {
    const { instance } = data;
    
    // 如果实例中有用户，则需要迁移
    if (instance.currentUsers > 0) {
      this.logger.log(`实例 ${instance.id} 正在关闭，需要迁移 ${instance.currentUsers} 个用户`);
      
      try {
        // 查找可用的目标实例
        const targetInstance = await this.findTargetInstance(instance);
        
        if (targetInstance) {
          // 开始迁移
          await this.startMigration(instance.id, targetInstance.id);
        } else {
          this.logger.error(`无法为实例 ${instance.id} 找到合适的目标实例进行迁移`);
        }
      } catch (error) {
        this.logger.error(`处理实例关闭事件时出错: ${error.message}`, error.stack);
      }
    }
  }

  /**
   * 处理迁移完成事件
   * @param data 事件数据
   */
  private handleMigrationCompleted(data: { migrationId: string }): void {
    const { migrationId } = data;
    const migration = this.migrationInProgress.get(migrationId);
    
    if (migration) {
      this.logger.log(`迁移 ${migrationId} 已完成，从 ${migration.sourceInstanceId} 到 ${migration.targetInstanceId}`);
      
      // 更新迁移状态
      migration.status = 'completed';
      this.migrationInProgress.set(migrationId, migration);
      
      // 一段时间后清理迁移记录
      setTimeout(() => {
        this.migrationInProgress.delete(migrationId);
      }, 60000); // 1分钟后清理
    }
  }

  /**
   * 处理迁移失败事件
   * @param data 事件数据
   */
  private handleMigrationFailed(data: { migrationId: string; error: string }): void {
    const { migrationId, error } = data;
    const migration = this.migrationInProgress.get(migrationId);
    
    if (migration) {
      this.logger.error(`迁移 ${migrationId} 失败: ${error}`);
      
      // 更新迁移状态
      migration.status = 'failed';
      migration.error = error;
      this.migrationInProgress.set(migrationId, migration);
      
      // 一段时间后清理迁移记录
      setTimeout(() => {
        this.migrationInProgress.delete(migrationId);
      }, 300000); // 5分钟后清理
    }
  }

  /**
   * 查找目标实例
   * @param sourceInstance 源实例
   * @returns 目标实例
   */
  private async findTargetInstance(sourceInstance: Instance): Promise<Instance | null> {
    // 获取可用实例
    const availableInstances = this.instanceService.getAvailableInstances();
    
    // 过滤出符合条件的实例
    const suitableInstances = availableInstances.filter(instance => 
      // 不是源实例
      instance.id !== sourceInstance.id &&
      // 相同的场景ID
      instance.sceneId === sourceInstance.sceneId &&
      // 有足够的容量
      instance.currentUsers + sourceInstance.currentUsers <= instance.maxUsers
    );
    
    if (suitableInstances.length === 0) {
      // 如果没有合适的实例，创建一个新实例
      try {
        return await this.instanceService.createInstance({
          sceneId: sourceInstance.sceneId,
          locationId: sourceInstance.locationId,
          channelId: sourceInstance.channelId,
          isMediaInstance: sourceInstance.isMediaInstance,
        });
      } catch (error) {
        this.logger.error(`创建目标实例失败: ${error.message}`, error.stack);
        return null;
      }
    }
    
    // 选择负载最小的实例
    return suitableInstances.reduce((prev, curr) => 
      prev.currentUsers < curr.currentUsers ? prev : curr
    );
  }

  /**
   * 开始迁移
   * @param sourceInstanceId 源实例ID
   * @param targetInstanceId 目标实例ID
   * @returns 迁移ID
   */
  async startMigration(sourceInstanceId: string, targetInstanceId: string): Promise<string> {
    const sourceInstance = this.instanceService.getInstance(sourceInstanceId);
    const targetInstance = this.instanceService.getInstance(targetInstanceId);
    
    if (!sourceInstance) {
      throw new Error(`源实例 ${sourceInstanceId} 不存在`);
    }
    
    if (!targetInstance) {
      throw new Error(`目标实例 ${targetInstanceId} 不存在`);
    }
    
    // 生成迁移ID
    const migrationId = `migration-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    
    // 获取源实例中的用户
    const users = await this.instanceService.getInstanceUsers(sourceInstanceId);
    
    // 创建迁移记录
    this.migrationInProgress.set(migrationId, {
      sourceInstanceId,
      targetInstanceId,
      startTime: new Date(),
      status: 'preparing',
      users,
    });
    
    // 触发迁移开始事件
    this.eventEmitter.emit('instance.migration.started', {
      migrationId,
      sourceInstanceId,
      targetInstanceId,
      users,
    });
    
    // 开始迁移过程
    this.processMigration(migrationId).catch(error => {
      this.logger.error(`迁移过程失败: ${error.message}`, error.stack);
      
      // 触发迁移失败事件
      this.eventEmitter.emit('instance.migration.failed', {
        migrationId,
        error: error.message,
      });
    });
    
    return migrationId;
  }

  /**
   * 处理迁移过程
   * @param migrationId 迁移ID
   */
  private async processMigration(migrationId: string): Promise<void> {
    const migration = this.migrationInProgress.get(migrationId);
    
    if (!migration) {
      throw new Error(`迁移 ${migrationId} 不存在`);
    }
    
    try {
      // 更新迁移状态
      migration.status = 'transferring';
      this.migrationInProgress.set(migrationId, migration);
      
      // 触发状态转移事件
      this.eventEmitter.emit('instance.migration.transferring', {
        migrationId,
        sourceInstanceId: migration.sourceInstanceId,
        targetInstanceId: migration.targetInstanceId,
      });
      
      // 等待状态转移完成
      await this.transferState(migration.sourceInstanceId, migration.targetInstanceId);
      
      // 更新迁移状态
      migration.status = 'redirecting';
      this.migrationInProgress.set(migrationId, migration);
      
      // 触发用户重定向事件
      this.eventEmitter.emit('instance.migration.redirecting', {
        migrationId,
        sourceInstanceId: migration.sourceInstanceId,
        targetInstanceId: migration.targetInstanceId,
        users: migration.users,
      });
      
      // 重定向用户
      await this.redirectUsers(migration.sourceInstanceId, migration.targetInstanceId, migration.users);
      
      // 触发迁移完成事件
      this.eventEmitter.emit('instance.migration.completed', {
        migrationId,
        sourceInstanceId: migration.sourceInstanceId,
        targetInstanceId: migration.targetInstanceId,
        users: migration.users,
      });
    } catch (error) {
      // 更新迁移状态
      migration.status = 'failed';
      migration.error = error.message;
      this.migrationInProgress.set(migrationId, migration);
      
      // 触发迁移失败事件
      this.eventEmitter.emit('instance.migration.failed', {
        migrationId,
        error: error.message,
      });
      
      throw error;
    }
  }

  /**
   * 转移状态
   * @param sourceInstanceId 源实例ID
   * @param targetInstanceId 目标实例ID
   */
  private async transferState(sourceInstanceId: string, targetInstanceId: string): Promise<void> {
    // 这里实现状态转移逻辑
    // 例如，可以通过WebSocket或HTTP请求将源实例的状态发送到目标实例
    
    // 模拟状态转移过程
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    this.logger.log(`已完成从实例 ${sourceInstanceId} 到实例 ${targetInstanceId} 的状态转移`);
  }

  /**
   * 重定向用户
   * @param sourceInstanceId 源实例ID
   * @param targetInstanceId 目标实例ID
   * @param users 用户列表
   */
  private async redirectUsers(sourceInstanceId: string, targetInstanceId: string, users: string[]): Promise<void> {
    // 获取目标实例信息
    const targetInstance = this.instanceService.getInstance(targetInstanceId);
    
    if (!targetInstance) {
      throw new Error(`目标实例 ${targetInstanceId} 不存在`);
    }
    
    // 向每个用户发送重定向消息
    for (const userId of users) {
      // 触发用户重定向事件
      this.eventEmitter.emit('user.redirect', {
        userId,
        sourceInstanceId,
        targetInstanceId,
        targetInstanceInfo: {
          ipAddress: targetInstance.ipAddress,
          port: targetInstance.port,
        },
      });
    }
    
    this.logger.log(`已向 ${users.length} 个用户发送从实例 ${sourceInstanceId} 到实例 ${targetInstanceId} 的重定向消息`);
  }

  /**
   * 获取迁移状态
   * @param migrationId 迁移ID
   */
  getMigrationStatus(migrationId: string): any {
    const migration = this.migrationInProgress.get(migrationId);
    
    if (!migration) {
      return null;
    }
    
    return {
      id: migrationId,
      sourceInstanceId: migration.sourceInstanceId,
      targetInstanceId: migration.targetInstanceId,
      startTime: migration.startTime,
      status: migration.status,
      userCount: migration.users.length,
      error: migration.error,
    };
  }

  /**
   * 获取所有迁移
   */
  getAllMigrations(): any[] {
    const migrations = [];
    
    for (const [id, migration] of this.migrationInProgress.entries()) {
      migrations.push({
        id,
        sourceInstanceId: migration.sourceInstanceId,
        targetInstanceId: migration.targetInstanceId,
        startTime: migration.startTime,
        status: migration.status,
        userCount: migration.users.length,
        error: migration.error,
      });
    }
    
    return migrations;
  }
}
