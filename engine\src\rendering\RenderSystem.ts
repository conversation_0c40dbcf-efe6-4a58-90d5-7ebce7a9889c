/**
 * 渲染系统
 * 负责管理渲染过程和渲染相关组件
 */
import { System } from '../core/System';
import { Renderer } from './Renderer';
import type { Camera   } from './Camera';
import { Scene } from '../scene/Scene';
import { ShadowSystem } from './shadows/ShadowSystem';
import { PostProcessingSystem } from './postprocessing/PostProcessingSystem';

/**
 * 渲染系统
 */
export class RenderSystem extends System {
  /** 系统类型 */
  private static readonly TYPE: string = 'RenderSystem';

  /** 渲染器 */
  private renderer: Renderer;

  /** 活跃相机 */
  private activeCamera: Camera | null = null;

  /** 活跃场景 */
  private activeScene: Scene | null = null;

  /** 是否自动清除 */
  private autoClear: boolean = true;

  /** 是否启用后处理 */
  private postProcessingEnabled: boolean = false;

  /** 阴影系统 */
  private shadowSystem: ShadowSystem | null = null;

  /** 后处理系统 */
  private postProcessingSystem: PostProcessingSystem | null = null;

  /**
   * 创建渲染系统
   * @param renderer 渲染器
   * @param options 渲染系统选项
   */
  constructor(renderer: Renderer, options: {
    enableShadows?: boolean;
    enablePostProcessing?: boolean;
  } = {}) {
    super(-100); // 渲染系统优先级较低，在其他系统之后执行
    this.renderer = renderer;

    // 创建阴影系统
    if (options.enableShadows !== false) {
      this.shadowSystem = new ShadowSystem({
        enabled: true,
        useCSM: true
      });
      this.shadowSystem.setRenderer(renderer.getThreeRenderer());
    }

    // 创建后处理系统
    if (options.enablePostProcessing !== false) {
      this.postProcessingSystem = new PostProcessingSystem({
        enabled: true,
        autoResize: true
      });
    }
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return RenderSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize.bind(this));

    // 初始化时调整大小
    this.handleResize();
  }

  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 设置活跃相机
   * @param camera 相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;

    // 更新阴影系统的相机
    if (this.shadowSystem) {
      this.shadowSystem.setActiveCamera(camera);
    }

    // 更新后处理系统的相机
    if (this.postProcessingSystem && this.postProcessingSystem.isInitialized() && this.activeScene) {
      this.postProcessingSystem.setup(
        this.renderer.getThreeRenderer(),
        this.activeScene.getThreeScene(),
        camera.getThreeCamera()
      );
    }
  }

  /**
   * 获取活跃相机
   * @returns 活跃相机
   */
  public getActiveCamera(): Camera | null {
    return this.activeCamera;
  }

  /**
   * 设置活跃场景
   * @param scene 场景
   */
  public setActiveScene(scene: Scene): void {
    this.activeScene = scene;

    // 更新阴影系统的场景
    if (this.shadowSystem) {
      this.shadowSystem.setActiveScene(scene);
    }

    // 更新后处理系统的场景
    if (this.postProcessingSystem && this.postProcessingSystem.isInitialized() && this.activeCamera) {
      this.postProcessingSystem.setup(
        this.renderer.getThreeRenderer(),
        scene.getThreeScene(),
        this.activeCamera.getThreeCamera()
      );
    }
  }

  /**
   * 获取活跃场景
   * @returns 活跃场景
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 设置是否自动清除
   * @param autoClear 是否自动清除
   */
  public setAutoClear(autoClear: boolean): void {
    this.autoClear = autoClear;
  }

  /**
   * 是否自动清除
   * @returns 是否自动清除
   */
  public isAutoClear(): boolean {
    return this.autoClear;
  }

  /**
   * 设置是否启用后处理
   * @param enabled 是否启用
   */
  public setPostProcessingEnabled(enabled: boolean): void {
    this.postProcessingEnabled = enabled;

    // 更新后处理系统状态
    if (this.postProcessingSystem) {
      this.postProcessingSystem.setEnabled(enabled);
    }
  }

  /**
   * 是否启用后处理
   * @returns 是否启用后处理
   */
  public isPostProcessingEnabled(): boolean {
    return this.postProcessingEnabled;
  }

  /**
   * 获取阴影系统
   * @returns 阴影系统
   */
  public getShadowSystem(): ShadowSystem | null {
    return this.shadowSystem;
  }

  /**
   * 获取后处理系统
   * @returns 后处理系统
   */
  public getPostProcessingSystem(): PostProcessingSystem | null {
    return this.postProcessingSystem;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 如果没有活跃相机或场景，则不渲染
    if (!this.activeCamera || !this.activeScene) {
      return;
    }

    // 更新阴影系统
    if (this.shadowSystem) {
      this.shadowSystem.update(deltaTime);
    }

    // 如果启用了后处理且后处理系统已初始化
    if (this.postProcessingEnabled && this.postProcessingSystem && this.postProcessingSystem.isInitialized()) {
      // 使用后处理系统渲染
      this.postProcessingSystem.update(deltaTime);
    } else {
      // 直接渲染场景
      this.renderer.render(this.activeScene, this.activeCamera);
    }

    // 发出渲染事件
    this.emit('render', this.activeScene, this.activeCamera);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize.bind(this));

    // 销毁阴影系统
    if (this.shadowSystem) {
      (this.shadowSystem as any).dispose();
      this.shadowSystem = null;
    }

    // 销毁后处理系统
    if (this.postProcessingSystem) {
      (this.postProcessingSystem as any).dispose();
      this.postProcessingSystem = null;
    }

    // 调用父类销毁方法
    super.dispose();
  }
}
