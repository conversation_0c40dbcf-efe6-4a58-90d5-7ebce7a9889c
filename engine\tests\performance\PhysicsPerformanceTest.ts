/**
 * 物理性能测试
 * 测试物理系统在不同场景复杂度下的性能
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { PerformanceTest, PerformanceTestConfig } from './PerformanceTestFramework';
import { Entity } from '../../src/core/Entity';
import { PhysicsSystem } from '../../src/physics/PhysicsSystem';
import { PhysicsBody } from '../../src/physics/PhysicsBody';
import { PhysicsCollider } from '../../src/physics/PhysicsCollider';
import { MeshComponent } from '../../src/rendering/MeshComponent';
import { MaterialComponent } from '../../src/rendering/MaterialComponent';
import { Light, LightType } from '../../src/rendering/Light';
import { Debug } from '../../src/utils/Debug';

/**
 * 物理性能测试配置
 */
export interface PhysicsPerformanceTestConfig extends PerformanceTestConfig {
  /** 场景复杂度 */
  complexity?: 'low' | 'medium' | 'high' | 'extreme';
  /** 是否启用连续碰撞检测 */
  enableCCD?: boolean;
  /** 是否启用休眠 */
  allowSleep?: boolean;
  /** 物理更新频率 */
  updateFrequency?: number;
  /** 迭代次数 */
  iterations?: number;
  /** 是否启用调试渲染 */
  enableDebugRenderer?: boolean;
}

/**
 * 物理性能测试
 */
export class PhysicsPerformanceTest extends PerformanceTest {
  /** 测试配置 */
  protected config: PhysicsPerformanceTestConfig;
  /** 物理系统 */
  private physicsSystem?: PhysicsSystem;
  /** 实体列表 */
  private entities: Entity[] = [];
  /** 物理统计信息 */
  private physicsStats: {
    /** 物理更新时间（毫秒） */
    updateTimes: number[];
    /** 碰撞对数量 */
    collisionPairs: number[];
    /** 接触点数量 */
    contactPoints: number[];
    /** 刚体数量 */
    rigidBodies: number;
    /** 碰撞体数量 */
    colliders: number;
  };

  /**
   * 创建物理性能测试
   * @param config 测试配置
   */
  constructor(config: PhysicsPerformanceTestConfig) {
    super({
      ...config,
      name: config.name || '物理性能测试',
    });

    this.config = {
      ...config,
      complexity: config.complexity || 'medium',
      enableCCD: config.enableCCD !== undefined ? config.enableCCD : false,
      allowSleep: config.allowSleep !== undefined ? config.allowSleep : true,
      updateFrequency: config.updateFrequency || 60,
      iterations: config.iterations || 10,
      enableDebugRenderer: config.enableDebugRenderer !== undefined ? config.enableDebugRenderer : false,
    };

    // 初始化物理统计信息
    this.physicsStats = {
      updateTimes: [],
      collisionPairs: [],
      contactPoints: [],
      rigidBodies: 0,
      colliders: 0,
    };

    // 扩展性能数据
    this.performanceData.physicsUpdateTimeHistory = [];
    this.performanceData.collisionPairsHistory = [];
    this.performanceData.contactPointsHistory = [];
  }

  /**
   * 初始化测试
   */
  protected initialize(): void {
    Debug.log('物理性能测试', `初始化测试: ${this.config.name}, 复杂度: ${this.config.complexity}`);

    // 设置相机位置
    this.camera.getThreeCamera().position.set(0, 10, 20);
    this.camera.getThreeCamera().lookAt(0, 0, 0);

    // 添加光照
    this.setupLights();

    // 创建物理系统
    this.createPhysicsSystem();

    // 创建场景
    this.createScene();

    Debug.log('物理性能测试', '测试初始化完成');
  }

  /**
   * 设置光照
   */
  private setupLights(): void {
    // 添加环境光
    const ambientLight = new Light(LightType.AMBIENT, { color: 0x404040, intensity: 0.5 });
    const ambientLightEntity = new Entity();
    ambientLightEntity.addComponent(ambientLight);
    this.scene.addEntity(ambientLightEntity);

    // 添加方向光
    const directionalLight = new Light(LightType.DIRECTIONAL, { 
      color: 0xffffff, 
      intensity: 1.0,
      castShadow: true,
      shadowMapSize: 2048,
    });
    const directionalLightEntity = new Entity();
    directionalLightEntity.addComponent(directionalLight);
    directionalLightEntity.transform.setPosition(5, 10, 5);
    directionalLightEntity.transform.lookAt(0, 0, 0);
    this.scene.addEntity(directionalLightEntity);
  }

  /**
   * 创建物理系统
   */
  private createPhysicsSystem(): void {
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      allowSleep: this.config.allowSleep,
      updateFrequency: this.config.updateFrequency,
      iterations: this.config.iterations,
      enableCCD: this.config.enableCCD,
      debug: this.config.enableDebugRenderer,
    });

    // 添加到世界
    this.world.addSystem(this.physicsSystem);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 根据复杂度创建不同数量的对象
    let objectCount = 0;
    switch (this.config.complexity) {
      case 'low':
        objectCount = 50;
        break;
      case 'medium':
        objectCount = 200;
        break;
      case 'high':
        objectCount = 500;
        break;
      case 'extreme':
        objectCount = 1000;
        break;
    }

    Debug.log('物理性能测试', `创建 ${objectCount} 个物理对象`);

    // 创建地面
    this.createGround();

    // 创建多个物理对象
    this.createPhysicsObjects(objectCount);

    // 更新物理统计信息
    this.physicsStats.rigidBodies = objectCount + 1; // +1 for ground
    this.physicsStats.colliders = objectCount + 1; // +1 for ground
  }

  /**
   * 创建地面
   */
  private createGround(): void {
    const groundEntity = new Entity();
    
    // 添加网格组件
    const groundMesh = new MeshComponent();
    groundMesh.setGeometry(new THREE.PlaneGeometry(100, 100, 1, 1));
    groundEntity.addComponent(groundMesh);
    
    // 添加材质组件
    const groundMaterial = new MaterialComponent();
    groundMaterial.createStandardMaterial({
      color: 0x999999,
      roughness: 0.8,
      metalness: 0.2,
    });
    groundEntity.addComponent(groundMaterial);
    
    // 设置变换
    groundEntity.transform.setRotation(-Math.PI / 2, 0, 0);
    groundEntity.transform.setPosition(0, 0, 0);
    
    // 添加物理组件
    const groundBody = new PhysicsBody({
      type: 'static',
      mass: 0,
    });
    groundEntity.addComponent(groundBody);
    
    const groundCollider = new PhysicsCollider({
      shape: 'plane',
      material: {
        friction: 0.3,
        restitution: 0.3,
      },
    });
    groundEntity.addComponent(groundCollider);
    
    // 添加到场景
    this.scene.addEntity(groundEntity);
    this.entities.push(groundEntity);
  }

  /**
   * 创建多个物理对象
   * @param count 对象数量
   */
  private createPhysicsObjects(count: number): void {
    // 创建几何体
    const geometries = [
      new THREE.BoxGeometry(1, 1, 1),
      new THREE.SphereGeometry(0.5, 16, 16),
    ];

    // 创建材质
    const materials = [];
    for (let i = 0; i < 5; i++) {
      const material = new MaterialComponent();
      material.createStandardMaterial({
        color: Math.random() * 0xffffff,
      });
      materials.push(material);
    }

    // 创建物理对象
    const areaSize = Math.sqrt(count) * 2;
    for (let i = 0; i < count; i++) {
      const entity = new Entity();
      
      // 选择几何体
      const geometryIndex = Math.floor(Math.random() * geometries.length);
      const geometry = geometries[geometryIndex];
      
      // 添加网格组件
      const mesh = new MeshComponent();
      mesh.setGeometry(geometry);
      entity.addComponent(mesh);
      
      // 添加材质组件
      const material = materials[Math.floor(Math.random() * materials.length)].clone();
      entity.addComponent(material);
      
      // 设置位置
      const x = (Math.random() - 0.5) * areaSize;
      const z = (Math.random() - 0.5) * areaSize;
      const y = Math.random() * 10 + 5;
      entity.transform.setPosition(x, y, z);
      
      // 设置旋转
      entity.transform.setRotation(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
      );
      
      // 设置缩放
      const scale = Math.random() * 0.5 + 0.5;
      entity.transform.setScale(scale, scale, scale);
      
      // 添加物理组件
      const physicsBody = new PhysicsBody({
        type: 'dynamic',
        mass: Math.random() * 10 + 1,
        linearDamping: 0.1,
        angularDamping: 0.1,
      });
      entity.addComponent(physicsBody);
      
      // 添加碰撞体
      const collider = new PhysicsCollider({
        shape: geometryIndex === 0 ? 'box' : 'sphere',
        size: geometryIndex === 0 ? { x: 1 * scale, y: 1 * scale, z: 1 * scale } : undefined,
        radius: geometryIndex === 1 ? 0.5 * scale : undefined,
        material: {
          friction: Math.random() * 0.5 + 0.1,
          restitution: Math.random() * 0.5 + 0.1,
        },
      });
      entity.addComponent(collider);
      
      // 添加到场景
      this.scene.addEntity(entity);
      this.entities.push(entity);
    }
  }

  /**
   * 收集自定义性能数据
   */
  protected collectCustomPerformanceData(): void {
    super.collectCustomPerformanceData();

    if (!this.physicsSystem) {
      return;
    }

    // 获取物理系统性能数据
    const updateTime = this.physicsSystem.getLastUpdateTime();
    const collisionPairs = this.physicsSystem.getCollisionPairsCount();
    const contactPoints = this.physicsSystem.getContactPointsCount();

    // 记录物理统计信息
    this.physicsStats.updateTimes.push(updateTime);
    this.physicsStats.collisionPairs.push(collisionPairs);
    this.physicsStats.contactPoints.push(contactPoints);

    // 添加到性能数据历史
    this.performanceData.physicsUpdateTimeHistory.push(updateTime);
    this.performanceData.collisionPairsHistory.push(collisionPairs);
    this.performanceData.contactPointsHistory.push(contactPoints);
  }

  /**
   * 计算自定义性能数据
   */
  protected calculateCustomPerformanceData(): { [key: string]: any } {
    return {
      physicsUpdateTime: this.calculateAverage(this.physicsStats.updateTimes),
      collisionPairs: this.calculateAverage(this.physicsStats.collisionPairs),
      contactPoints: this.calculateAverage(this.physicsStats.contactPoints),
      rigidBodies: this.physicsStats.rigidBodies,
      colliders: this.physicsStats.colliders,
    };
  }

  /**
   * 清理测试
   */
  protected cleanup(): void {
    super.cleanup();

    // 清理实体
    for (const entity of this.entities) {
      this.scene.removeEntity(entity);
    }
    this.entities = [];

    // 清理物理系统
    if (this.physicsSystem) {
      this.world.removeSystem(this.physicsSystem);
      this.physicsSystem = undefined;
    }

    Debug.log('物理性能测试', '测试清理完成');
  }
}
