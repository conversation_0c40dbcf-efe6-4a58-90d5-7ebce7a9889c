/**
 * 虚拟纹理着色器
 * 用于实现虚拟纹理技术的着色器代码
 */
/**
 * 虚拟纹理顶点着色器
 */
export declare const virtualTextureVertexShader = "\n// \u8F93\u5165\u5C5E\u6027\nattribute vec3 position;\nattribute vec2 uv;\nattribute vec3 normal;\n\n// \u8F93\u51FA\u53D8\u91CF\nvarying vec2 vUv;\nvarying vec3 vNormal;\nvarying vec3 vPosition;\nvarying vec4 vViewPosition;\n\n// \u77E9\u9635\nuniform mat4 modelMatrix;\nuniform mat4 viewMatrix;\nuniform mat4 projectionMatrix;\nuniform mat4 modelViewMatrix;\nuniform mat3 normalMatrix;\n\nvoid main() {\n  // \u8BA1\u7B97\u9876\u70B9\u4F4D\u7F6E\n  vec4 worldPosition = modelMatrix * vec4(position, 1.0);\n  vec4 viewPosition = viewMatrix * worldPosition;\n\n  // \u4F20\u9012\u53D8\u91CF\n  vUv = uv;\n  vNormal = normalize(normalMatrix * normal);\n  vPosition = worldPosition.xyz;\n  vViewPosition = viewPosition;\n\n  // \u8F93\u51FA\u88C1\u526A\u7A7A\u95F4\u4F4D\u7F6E\n  gl_Position = projectionMatrix * viewPosition;\n}\n";
/**
 * 虚拟纹理片段着色器
 */
export declare const virtualTextureFragmentShader = "\n// \u7CBE\u5EA6\nprecision highp float;\nprecision highp int;\nprecision highp sampler2D;\n\n// \u8F93\u5165\u53D8\u91CF\nvarying vec2 vUv;\nvarying vec3 vNormal;\nvarying vec3 vPosition;\nvarying vec4 vViewPosition;\n\n// \u7269\u7406\u7EB9\u7406\nuniform sampler2D uPhysicalTexture;\nuniform float uPhysicalTextureSize;\n\n// \u9875\u9762\u8868\nuniform sampler2D uPageTable;\nuniform float uPageTableSize;\n\n// \u9875\u9762\u5927\u5C0F\nuniform float uPageSize;\nuniform float uPageBorderSize;\n\n// MIP\u7EA7\u522B\nuniform float uMaxMipLevel;\nuniform float uMipBias;\n\n// \u8C03\u8BD5\nuniform bool uDebugMode;\nuniform vec4 uDebugColor;\n\n// \u8BA1\u7B97MIP\u7EA7\u522B\nfloat calculateMipLevel(vec2 uv, vec2 textureSize) {\n  // \u8BA1\u7B97\u7EB9\u7406\u5750\u6807\u7684\u5BFC\u6570\n  vec2 dx = dFdx(uv * textureSize);\n  vec2 dy = dFdy(uv * textureSize);\n\n  // \u8BA1\u7B97\u6700\u5927\u5BFC\u6570\u957F\u5EA6\n  float maxLength = max(length(dx), length(dy));\n\n  // \u8BA1\u7B97MIP\u7EA7\u522B\n  float mipLevel = log2(maxLength);\n\n  // \u5E94\u7528MIP\u504F\u79FB\n  mipLevel += uMipBias;\n\n  // \u9650\u5236MIP\u7EA7\u522B\u8303\u56F4\n  mipLevel = clamp(mipLevel, 0.0, uMaxMipLevel);\n\n  return mipLevel;\n}\n\n// \u83B7\u53D6\u9875\u9762\u5750\u6807\nvec4 getPageCoord(vec2 uv, float mipLevel) {\n  // \u8BA1\u7B97MIP\u7EA7\u522B\u7684\u7F29\u653E\u56E0\u5B50\n  float mipScale = pow(2.0, mipLevel);\n\n  // \u8BA1\u7B97\u9875\u9762\u5750\u6807\n  float pageX = floor(uv.x * uPhysicalTextureSize / (uPageSize * mipScale));\n  float pageY = floor(uv.y * uPhysicalTextureSize / (uPageSize * mipScale));\n\n  // \u8BA1\u7B97\u9875\u9762\u5185\u5750\u6807\n  float localX = fract(uv.x * uPhysicalTextureSize / (uPageSize * mipScale));\n  float localY = fract(uv.y * uPhysicalTextureSize / (uPageSize * mipScale));\n\n  return vec4(pageX, pageY, localX, localY);\n}\n\n// \u83B7\u53D6\u9875\u9762\u8868\u6761\u76EE\nvec4 getPageTableEntry(float pageX, float pageY, float mipLevel) {\n  // \u8BA1\u7B97\u9875\u9762\u8868\u5750\u6807\n  float u = (pageX + 0.5) / uPageTableSize;\n  float v = (pageY + 0.5) / uPageTableSize;\n\n  // \u83B7\u53D6\u9875\u9762\u8868\u6761\u76EE\n  return texture2D(uPageTable, vec2(u, v));\n}\n\n// \u83B7\u53D6\u7269\u7406\u7EB9\u7406\u5750\u6807\nvec2 getPhysicalTextureCoord(vec4 pageTableEntry, vec2 localUV) {\n  // \u89E3\u7801\u9875\u9762\u8868\u6761\u76EE\n  float physicalPageX = pageTableEntry.r * 255.0;\n  float physicalPageY = pageTableEntry.g * 255.0;\n\n  // \u8BA1\u7B97\u7269\u7406\u7EB9\u7406\u5750\u6807\n  float u = (physicalPageX * uPageSize + localUV.x * (uPageSize - 2.0 * uPageBorderSize) + uPageBorderSize) / uPhysicalTextureSize;\n  float v = (physicalPageY * uPageSize + localUV.y * (uPageSize - 2.0 * uPageBorderSize) + uPageBorderSize) / uPhysicalTextureSize;\n\n  return vec2(u, v);\n}\n\n// \u83B7\u53D6\u9875\u9762\u5750\u6807\nvec4 getPageCoord(vec2 uv, float mipLevel) {\n  // \u8BA1\u7B97MIP\u7EA7\u522B\u7684\u7F29\u653E\u56E0\u5B50\n  float mipScale = pow(2.0, mipLevel);\n\n  // \u8BA1\u7B97\u9875\u9762\u5927\u5C0F\n  float scaledPageSize = uPageSize * mipScale;\n\n  // \u8BA1\u7B97\u9875\u9762\u5750\u6807\n  float pageX = floor(uv.x * uPhysicalTextureSize / scaledPageSize);\n  float pageY = floor(uv.y * uPhysicalTextureSize / scaledPageSize);\n\n  // \u8BA1\u7B97\u9875\u9762\u5185\u7684\u5C40\u90E8\u5750\u6807\n  float localX = fract(uv.x * uPhysicalTextureSize / scaledPageSize);\n  float localY = fract(uv.y * uPhysicalTextureSize / scaledPageSize);\n\n  return vec4(pageX, pageY, localX, localY);\n}\n\n// \u83B7\u53D6\u9875\u9762\u8868\u6761\u76EE\nvec4 getPageTableEntry(float pageX, float pageY, float mipLevel) {\n  // \u8BA1\u7B97\u9875\u9762\u8868\u5927\u5C0F\n  float pageTableSize = ceil(uPhysicalTextureSize / uPageSize);\n\n  // \u8BA1\u7B97\u9875\u9762\u8868\u5750\u6807\n  vec2 pageTableUV = vec2(\n    (pageX + 0.5) / pageTableSize,\n    (pageY + 0.5) / pageTableSize\n  );\n\n  // \u91C7\u6837\u9875\u9762\u8868\n  return texture2D(uPageTable, pageTableUV);\n}\n\n// \u83B7\u53D6\u7269\u7406\u7EB9\u7406\u5750\u6807\nvec2 getPhysicalTextureCoord(vec4 pageTableEntry, vec2 localUV) {\n  // \u89E3\u6790\u9875\u9762\u8868\u6761\u76EE\n  float physicalPageX = pageTableEntry.r;\n  float physicalPageY = pageTableEntry.g;\n\n  // \u8BA1\u7B97\u7269\u7406\u7EB9\u7406\u5750\u6807\n  float borderSize = uPageBorderSize / uPageSize;\n  vec2 physicalUV = vec2(\n    (physicalPageX * uPageSize + localUV.x * uPageSize + uPageBorderSize) / uPhysicalTextureSize,\n    (physicalPageY * uPageSize + localUV.y * uPageSize + uPageBorderSize) / uPhysicalTextureSize\n  );\n\n  return physicalUV;\n}\n\n// \u91C7\u6837\u865A\u62DF\u7EB9\u7406\nvec4 sampleVirtualTexture(vec2 uv) {\n  // \u8BA1\u7B97MIP\u7EA7\u522B\n  float mipLevel = calculateMipLevel(uv, vec2(uPhysicalTextureSize));\n\n  // \u83B7\u53D6\u9875\u9762\u5750\u6807\n  vec4 pageCoord = getPageCoord(uv, mipLevel);\n  float pageX = pageCoord.x;\n  float pageY = pageCoord.y;\n  vec2 localUV = pageCoord.zw;\n\n  // \u83B7\u53D6\u9875\u9762\u8868\u6761\u76EE\n  vec4 pageTableEntry = getPageTableEntry(pageX, pageY, mipLevel);\n\n  // \u68C0\u67E5\u9875\u9762\u662F\u5426\u6709\u6548\n  if (pageTableEntry.a < 0.5) {\n    // \u9875\u9762\u65E0\u6548\uFF0C\u5C1D\u8BD5\u4F7F\u7528\u66F4\u4F4E\u5206\u8FA8\u7387\u7684MIP\u7EA7\u522B\n    for (float i = 1.0; i <= uMaxMipLevel; i += 1.0) {\n      float fallbackMip = mipLevel + i;\n      if (fallbackMip > uMaxMipLevel) {\n        break;\n      }\n\n      // \u83B7\u53D6\u66F4\u4F4E\u5206\u8FA8\u7387\u7684\u9875\u9762\u5750\u6807\n      vec4 fallbackPageCoord = getPageCoord(uv, fallbackMip);\n      float fallbackPageX = fallbackPageCoord.x;\n      float fallbackPageY = fallbackPageCoord.y;\n      vec2 fallbackLocalUV = fallbackPageCoord.zw;\n\n      // \u83B7\u53D6\u9875\u9762\u8868\u6761\u76EE\n      vec4 fallbackPageTableEntry = getPageTableEntry(fallbackPageX, fallbackPageY, fallbackMip);\n\n      // \u5982\u679C\u627E\u5230\u6709\u6548\u9875\u9762\uFF0C\u5219\u4F7F\u7528\u5B83\n      if (fallbackPageTableEntry.a >= 0.5) {\n        // \u83B7\u53D6\u7269\u7406\u7EB9\u7406\u5750\u6807\n        vec2 fallbackPhysicalUV = getPhysicalTextureCoord(fallbackPageTableEntry, fallbackLocalUV);\n\n        // \u91C7\u6837\u7269\u7406\u7EB9\u7406\n        return texture2D(uPhysicalTexture, fallbackPhysicalUV);\n      }\n    }\n\n    // \u5982\u679C\u6240\u6709MIP\u7EA7\u522B\u90FD\u65E0\u6548\uFF0C\u5219\u8FD4\u56DE\u7D2B\u8272\n    return vec4(1.0, 0.0, 1.0, 1.0); // \u7D2B\u8272\u8868\u793A\u7F3A\u5931\u7684\u9875\u9762\n  }\n\n  // \u83B7\u53D6\u7269\u7406\u7EB9\u7406\u5750\u6807\n  vec2 physicalUV = getPhysicalTextureCoord(pageTableEntry, localUV);\n\n  // \u91C7\u6837\u7269\u7406\u7EB9\u7406\n  return texture2D(uPhysicalTexture, physicalUV);\n}\n\nvoid main() {\n  // \u91C7\u6837\u865A\u62DF\u7EB9\u7406\n  vec4 color = sampleVirtualTexture(vUv);\n\n  // \u8C03\u8BD5\u6A21\u5F0F\n  if (uDebugMode) {\n    // \u8BA1\u7B97MIP\u7EA7\u522B\n    float mipLevel = calculateMipLevel(vUv, vec2(uPhysicalTextureSize));\n\n    // \u6839\u636EMIP\u7EA7\u522B\u7740\u8272\n    float t = mipLevel / uMaxMipLevel;\n    vec3 debugColor = mix(vec3(0.0, 1.0, 0.0), vec3(1.0, 0.0, 0.0), t);\n\n    // \u6DF7\u5408\u989C\u8272\n    color.rgb = mix(color.rgb, debugColor, 0.5);\n  }\n\n  // \u8F93\u51FA\u989C\u8272\n  gl_FragColor = color;\n}\n";
/**
 * 虚拟纹理反馈着色器
 */
export declare const virtualTextureFeedbackShader = "\n// \u7CBE\u5EA6\nprecision highp float;\nprecision highp int;\nprecision highp sampler2D;\n\n// \u8F93\u5165\u53D8\u91CF\nvarying vec2 vUv;\n\n// \u9875\u9762\u5927\u5C0F\nuniform float uPageSize;\nuniform float uPhysicalTextureSize;\nuniform float uMaxMipLevel;\n\n// \u8BA1\u7B97MIP\u7EA7\u522B\nfloat calculateMipLevel(vec2 uv, vec2 textureSize) {\n  // \u8BA1\u7B97\u7EB9\u7406\u5750\u6807\u7684\u5BFC\u6570\n  vec2 dx = dFdx(uv * textureSize);\n  vec2 dy = dFdy(uv * textureSize);\n\n  // \u8BA1\u7B97\u6700\u5927\u5BFC\u6570\u957F\u5EA6\n  float maxLength = max(length(dx), length(dy));\n\n  // \u8BA1\u7B97MIP\u7EA7\u522B\n  float mipLevel = log2(maxLength);\n\n  // \u5E94\u7528MIP\u504F\u79FB\n  mipLevel += uMipBias;\n\n  // \u9650\u5236MIP\u7EA7\u522B\u8303\u56F4\n  mipLevel = clamp(mipLevel, 0.0, uMaxMipLevel);\n\n  return mipLevel;\n}\n\nvoid main() {\n  // \u8BA1\u7B97MIP\u7EA7\u522B\n  float mipLevel = calculateMipLevel(vUv, vec2(uPhysicalTextureSize));\n\n  // \u8BA1\u7B97MIP\u7EA7\u522B\u7684\u7F29\u653E\u56E0\u5B50\n  float mipScale = pow(2.0, mipLevel);\n\n  // \u8BA1\u7B97\u9875\u9762\u5750\u6807\n  float pageX = floor(vUv.x * uPhysicalTextureSize / (uPageSize * mipScale));\n  float pageY = floor(vUv.y * uPhysicalTextureSize / (uPageSize * mipScale));\n\n  // \u8F93\u51FA\u9875\u9762\u8BF7\u6C42\u4FE1\u606F\n  gl_FragColor = vec4(\n    pageX / 255.0,\n    pageY / 255.0,\n    mipLevel / uMaxMipLevel,\n    1.0\n  );\n}\n";
