import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KnowledgeDocument } from '../../entities/knowledge-document.entity';
import { DocumentChunk } from '../../entities/document-chunk.entity';
import { KnowledgeBase } from '../../entities/knowledge-base.entity';
import { DocumentsController } from './documents.controller';
import { DocumentsService } from './documents.service';
import { CacheModule } from '../../cache/cache.module';
import { StorageModule } from '../../storage/storage.module';
import { ProcessingModule } from '../../processing/processing.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([KnowledgeDocument, DocumentChunk, KnowledgeBase]),
    CacheModule,
    StorageModule,
    ProcessingModule,
  ],
  controllers: [DocumentsController],
  providers: [DocumentsService],
  exports: [DocumentsService],
})
export class DocumentsModule {}
