/**
 * 软体切割系统
 * 实现软体的切割和撕裂功能
 */
import * as THREE from 'three';
import { SoftBodyComponent } from '../SoftBodyComponent';
/**
 * 切割平面
 */
export interface CuttingPlane {
    /** 平面法线 */
    normal: THREE.Vector3;
    /** 平面点 */
    point: THREE.Vector3;
}
/**
 * 切割射线
 */
export interface CuttingRay {
    /** 射线起点 */
    origin: THREE.Vector3;
    /** 射线方向 */
    direction: THREE.Vector3;
    /** 射线长度 */
    length: number;
}
/**
 * 软体切割系统选项
 */
export interface SoftBodyCutterOptions {
    /** 是否启用切割 */
    enabled?: boolean;
    /** 是否启用撕裂 */
    tearingEnabled?: boolean;
    /** 撕裂阈值（约束拉伸比例） */
    tearingThreshold?: number;
}
/**
 * 软体切割系统
 * 提供软体的切割和撕裂功能
 */
export declare class SoftBodyCutter {
    /** 是否启用切割 */
    private enabled;
    /** 是否启用撕裂 */
    private tearingEnabled;
    /** 撕裂阈值（约束拉伸比例） */
    private tearingThreshold;
    /**
     * 创建软体切割系统
     * @param options 软体切割系统选项
     */
    constructor(options?: SoftBodyCutterOptions);
    /**
     * 使用平面切割软体
     * @param softBody 软体组件
     * @param plane 切割平面
     * @returns 是否成功切割
     */
    cutWithPlane(softBody: SoftBodyComponent, plane: CuttingPlane): boolean;
    /**
     * 使用射线切割软体
     * @param softBody 软体组件
     * @param ray 切割射线
     * @returns 是否成功切割
     */
    cutWithRay(softBody: SoftBodyComponent, ray: CuttingRay): boolean;
    /**
     * 检查软体是否需要撕裂
     * @param softBody 软体组件
     * @returns 是否发生撕裂
     */
    checkTearing(softBody: SoftBodyComponent): boolean;
    /**
     * 切割布料
     * @param softBody 软体组件
     * @param particleSides 粒子在平面的哪一侧
     * @returns 是否成功切割
     */
    private cutCloth;
    /**
     * 切割绳索
     * @param softBody 软体组件
     * @param particleSides 粒子在平面的哪一侧
     * @returns 是否成功切割
     */
    private cutRope;
    /**
     * 切割体积软体
     * @param softBody 软体组件
     * @param particleSides 粒子在平面的哪一侧
     * @returns 是否成功切割
     */
    private cutVolume;
    /**
     * 启用切割
     */
    enable(): void;
    /**
     * 禁用切割
     */
    disable(): void;
    /**
     * 启用撕裂
     */
    enableTearing(): void;
    /**
     * 禁用撕裂
     */
    disableTearing(): void;
    /**
     * 设置撕裂阈值
     * @param threshold 撕裂阈值
     */
    setTearingThreshold(threshold: number): void;
}
