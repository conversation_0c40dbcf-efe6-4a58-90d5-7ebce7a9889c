/**
 * 输入映射单元测试
 */
import { InputDevice } from '../../src/input/InputDevice';
import { ButtonInputMapping, AxisInputMapping, VectorInputMapping, InputMappingType } from '../../src/input/InputMapping';

// 创建测试用的输入设备
class MockInputDevice implements InputDevice {
  private values: Map<string, any> = new Map();

  constructor(private name: string) {}

  getName(): string {
    return this.name;
  }

  initialize(): void {}
  update(deltaTime: number): void {}
  destroy(): void {}

  getValue(key: string): any {
    return this.values.get(key);
  }

  setValue(key: string, value: any): void {
    this.values.set(key, value);
  }

  hasKey(key: string): boolean {
    return this.values.has(key);
  }

  getKeys(): string[] {
    return Array.from(this.values.keys());
  }

  on(event: string, callback: Function): void {}
  off(event: string, callback: Function): void {}
}

describe('InputMapping', () => {
  let device: MockInputDevice;

  beforeEach(() => {
    device = new MockInputDevice('test');
  });

  describe('ButtonInputMapping', () => {
    let mapping: ButtonInputMapping;

    beforeEach(() => {
      mapping = new ButtonInputMapping('testButton', 'test', 'button1');
    });

    test('应该正确初始化按钮映射', () => {
      expect(mapping.getName()).toBe('testButton');
      expect(mapping.getType()).toBe(InputMappingType.BUTTON);
      expect(mapping.getDeviceName()).toBe('test');
    });

    test('应该正确评估按钮映射', () => {
      // 按钮未按下
      device.setValue('button1', false);
      expect(mapping.evaluate(device)).toBe(false);

      // 按钮按下
      device.setValue('button1', true);
      expect(mapping.evaluate(device)).toBe(true);

      // 按钮值为数字
      device.setValue('button1', 1);
      expect(mapping.evaluate(device)).toBe(true);

      // 按钮值为0
      device.setValue('button1', 0);
      expect(mapping.evaluate(device)).toBe(false);

      // 按钮值为undefined
      device.setValue('button1', undefined);
      expect(mapping.evaluate(device)).toBe(false);
    });
  });

  describe('AxisInputMapping', () => {
    let mapping: AxisInputMapping;

    beforeEach(() => {
      mapping = new AxisInputMapping('testAxis', 'test', 'axis1', 2, 0, 0.1);
    });

    test('应该正确初始化轴映射', () => {
      expect(mapping.getName()).toBe('testAxis');
      expect(mapping.getType()).toBe(InputMappingType.AXIS);
      expect(mapping.getDeviceName()).toBe('test');
    });

    test('应该正确评估轴映射', () => {
      // 轴值为0
      device.setValue('axis1', 0);
      expect(mapping.evaluate(device)).toBe(0);

      // 轴值小于死区
      device.setValue('axis1', 0.05);
      expect(mapping.evaluate(device)).toBe(0);

      // 轴值大于死区
      device.setValue('axis1', 0.5);
      const expected = ((0.5 - 0.1) / 0.9) * 2;
      expect(mapping.evaluate(device)).toBeCloseTo(expected, 5);

      // 轴值为负数
      device.setValue('axis1', -0.5);
      const expectedNegative = ((-0.5 + 0.1) / 0.9) * 2;
      expect(mapping.evaluate(device)).toBeCloseTo(expectedNegative, 5);

      // 轴值为undefined
      device.setValue('axis1', undefined);
      expect(mapping.evaluate(device)).toBe(0);
    });
  });

  describe('VectorInputMapping', () => {
    let mapping: VectorInputMapping;

    beforeEach(() => {
      mapping = new VectorInputMapping('testVector', 'test', 'axisX', 'axisY', 2, 0.1);
    });

    test('应该正确初始化向量映射', () => {
      expect(mapping.getName()).toBe('testVector');
      expect(mapping.getType()).toBe(InputMappingType.VECTOR);
      expect(mapping.getDeviceName()).toBe('test');
    });

    test('应该正确评估向量映射', () => {
      // 向量值为0
      device.setValue('axisX', 0);
      device.setValue('axisY', 0);
      expect(mapping.evaluate(device)).toEqual([0, 0]);

      // 向量长度小于死区
      device.setValue('axisX', 0.05);
      device.setValue('axisY', 0.05);
      expect(mapping.evaluate(device)).toEqual([0, 0]);

      // 向量长度大于死区
      device.setValue('axisX', 0.5);
      device.setValue('axisY', 0.5);
      const length = Math.sqrt(0.5 * 0.5 + 0.5 * 0.5);
      const normalizedLength = (length - 0.1) / 0.9;
      const scale = normalizedLength / length * 2;
      expect(mapping.evaluate(device)).toEqual([0.5 * scale, 0.5 * scale]);

      // 向量值为undefined
      device.setValue('axisX', undefined);
      device.setValue('axisY', undefined);
      expect(mapping.evaluate(device)).toEqual([0, 0]);
    });
  });
});
