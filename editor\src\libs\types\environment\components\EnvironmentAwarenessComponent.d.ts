/**
 * 环境感知组件
 *
 * 该组件使实体能够感知周围的环境，包括环境类型、天气、光照条件、地形等。
 * 可以附加到角色或其他需要环境感知能力的实体上。
 */
import { Component } from '../../core/Component';
import { Vector3 } from 'three';
/**
 * 环境类型枚举
 */
export declare enum EnvironmentType {
    INDOOR = "indoor",
    OUTDOOR = "outdoor",
    UNDERWATER = "underwater",
    SPACE = "space",
    CAVE = "cave",
    FOREST = "forest",
    DESERT = "desert",
    SNOW = "snow",
    URBAN = "urban",
    CUSTOM = "custom"
}
/**
 * 天气类型枚举
 */
export declare enum WeatherType {
    CLEAR = "clear",
    CLOUDY = "cloudy",
    RAINY = "rainy",
    STORMY = "stormy",
    SNOWY = "snowy",
    FOGGY = "foggy",
    CUSTOM = "custom"
}
/**
 * 地形类型枚举
 */
export declare enum TerrainType {
    FLAT = "flat",
    HILLS = "hills",
    MOUNTAINS = "mountains",
    WATER = "water",
    URBAN = "urban",
    CUSTOM = "custom"
}
/**
 * 环境感知数据接口
 */
export interface EnvironmentAwarenessData {
    environmentType: EnvironmentType;
    weatherType: WeatherType;
    terrainType: TerrainType;
    lightIntensity: number;
    temperature: number;
    humidity: number;
    windSpeed: number;
    windDirection: Vector3;
    noiseLevel: number;
    airQuality: number;
    waterLevel: number;
    visibility: number;
    timeOfDay: number;
    customParameters: Map<string, any>;
    lastEnvironmentChangeTime: number;
    awarenessRange: number;
}
/**
 * 环境感知组件配置接口
 */
export interface EnvironmentAwarenessComponentConfig {
    awarenessRange?: number;
    updateFrequency?: number;
    debug?: boolean;
    autoDetect?: boolean;
    changeThreshold?: number;
}
/**
 * 环境感知组件
 */
export declare class EnvironmentAwarenessComponent extends Component {
    data: EnvironmentAwarenessData;
    config: EnvironmentAwarenessComponentConfig;
    private onEnvironmentChangeCallbacks;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: EnvironmentAwarenessComponentConfig);
    /**
     * 更新环境感知数据
     * @param data 新的环境感知数据
     */
    updateEnvironmentData(data: Partial<EnvironmentAwarenessData>): void;
    /**
     * 检查是否有显著变化
     * @param newData 新数据
     * @returns 是否有显著变化
     */
    private hasSignificantChange;
    /**
     * 注册环境变化回调
     * @param callback 回调函数
     */
    onEnvironmentChange(callback: (data: EnvironmentAwarenessData) => void): void;
    /**
     * 移除环境变化回调
     * @param callback 回调函数
     */
    removeEnvironmentChangeCallback(callback: (data: EnvironmentAwarenessData) => void): void;
    /**
     * 通知环境变化
     */
    private notifyEnvironmentChange;
    /**
     * 获取当前环境数据
     * @returns 环境数据
     */
    getEnvironmentData(): EnvironmentAwarenessData;
    /**
     * 设置自定义环境参数
     * @param key 参数键
     * @param value 参数值
     */
    setCustomParameter(key: string, value: any): void;
    /**
     * 获取自定义环境参数
     * @param key 参数键
     * @returns 参数值
     */
    getCustomParameter(key: string): any;
}
