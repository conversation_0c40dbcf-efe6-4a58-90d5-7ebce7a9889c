/**
 * 动画混合性能监控器单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { BlendPerformanceMonitor } from '../../src/animation/utils/BlendPerformanceMonitor';

describe('BlendPerformanceMonitor', () => {
  let monitor: BlendPerformanceMonitor;

  beforeEach(() => {
    // 获取单例实例
    monitor = BlendPerformanceMonitor.getInstance();
    
    // 清除之前的记录
    monitor.clear();
    
    // 启用监控
    monitor.enable(100);
  });

  afterEach(() => {
    // 禁用监控
    monitor.disable();
  });

  it('应该正确创建单例实例', () => {
    // 获取另一个实例
    const anotherMonitor = BlendPerformanceMonitor.getInstance();
    
    // 验证是同一个实例
    expect(monitor).toBe(anotherMonitor);
  });

  it('应该能够启用和禁用监控', () => {
    // 禁用监控
    monitor.disable();
    
    // 尝试记录操作
    const opId = monitor.startOperation('test');
    
    // 验证操作ID为-1（表示未记录）
    expect(opId).toBe(-1);
    
    // 重新启用监控
    monitor.enable();
    
    // 尝试记录操作
    const newOpId = monitor.startOperation('test');
    
    // 验证操作ID不为-1
    expect(newOpId).not.toBe(-1);
  });

  it('应该能够记录操作', () => {
    // 记录操作
    const opId = monitor.startOperation('testOperation', { key: 'value' });
    
    // 结束操作
    monitor.endOperation(opId, { result: 'success' });
    
    // 获取记录
    const records = monitor.getRecords();
    
    // 验证记录
    expect(records.length).toBe(1);
    expect(records[0].operation).toBe('testOperation');
    expect(records[0].metadata).toEqual({ key: 'value' });
    expect(records[0].duration).toBeGreaterThanOrEqual(0);
  });

  it('应该能够限制记录数量', () => {
    // 设置最大记录数为10
    monitor.enable(10);
    
    // 记录15个操作
    for (let i = 0; i < 15; i++) {
      const opId = monitor.startOperation(`op${i}`);
      monitor.endOperation(opId);
    }
    
    // 获取记录
    const records = monitor.getRecords();
    
    // 验证记录数量为10
    expect(records.length).toBe(10);
    
    // 验证最早的记录被移除
    expect(records[0].operation).toBe('op5');
  });

  it('应该能够生成正确的统计信息', () => {
    // 记录不同类型的操作
    const op1Id = monitor.startOperation('type1', { layerCount: 5 });
    vi.advanceTimersByTime(10);
    monitor.endOperation(op1Id, { useCache: true });
    
    const op2Id = monitor.startOperation('type1', { layerCount: 10 });
    vi.advanceTimersByTime(20);
    monitor.endOperation(op2Id, { useCache: false });
    
    const op3Id = monitor.startOperation('type2', { maskCount: 3 });
    vi.advanceTimersByTime(15);
    monitor.endOperation(op3Id, { useObjectPool: true });
    
    // 获取统计信息
    const stats = monitor.getStats();
    
    // 验证总体统计
    expect(stats.totalOperations).toBe(3);
    expect(stats.byOperation).toHaveProperty('type1');
    expect(stats.byOperation).toHaveProperty('type2');
    expect(stats.byOperation.type1.count).toBe(2);
    expect(stats.byOperation.type2.count).toBe(1);
  });

  it('应该能够导出JSON数据', () => {
    // 记录操作
    const opId = monitor.startOperation('exportTest');
    monitor.endOperation(opId);
    
    // 导出JSON
    const json = monitor.exportToJSON();
    
    // 解析JSON
    const data = JSON.parse(json);
    
    // 验证数据
    expect(data).toHaveProperty('records');
    expect(data).toHaveProperty('stats');
    expect(data.records.length).toBe(1);
    expect(data.records[0].operation).toBe('exportTest');
  });

  it('应该能够记录混合层数量', () => {
    // 记录不同层数量的操作
    const op1Id = monitor.startOperation('blend', { layerCount: 5 });
    monitor.endOperation(op1Id);
    
    const op2Id = monitor.startOperation('blend', { layerCount: 10 });
    monitor.endOperation(op2Id);
    
    // 获取统计信息
    const stats = monitor.getStats();
    
    // 验证按层数分组的统计
    expect(stats.byLayerCount).toBeDefined();
    expect(stats.byLayerCount![5].count).toBe(1);
    expect(stats.byLayerCount![10].count).toBe(1);
  });

  it('应该能够比较缓存性能', () => {
    // 记录使用缓存的操作
    const op1Id = monitor.startOperation('withCache');
    vi.advanceTimersByTime(10);
    monitor.endOperation(op1Id, { useCache: true });
    
    // 记录不使用缓存的操作
    const op2Id = monitor.startOperation('withoutCache');
    vi.advanceTimersByTime(20);
    monitor.endOperation(op2Id, { useCache: false });
    
    // 获取统计信息
    const stats = monitor.getStats();
    
    // 验证缓存比较
    expect(stats.cacheComparison).toBeDefined();
    expect(stats.cacheComparison!.withCache.count).toBe(1);
    expect(stats.cacheComparison!.withoutCache.count).toBe(1);
    expect(stats.cacheComparison!.withCache.averageDuration).toBeLessThan(
      stats.cacheComparison!.withoutCache.averageDuration
    );
  });

  it('应该能够比较对象池性能', () => {
    // 记录使用对象池的操作
    const op1Id = monitor.startOperation('withObjectPool');
    vi.advanceTimersByTime(10);
    monitor.endOperation(op1Id, { useObjectPool: true });
    
    // 记录不使用对象池的操作
    const op2Id = monitor.startOperation('withoutObjectPool');
    vi.advanceTimersByTime(20);
    monitor.endOperation(op2Id, { useObjectPool: false });
    
    // 获取统计信息
    const stats = monitor.getStats();
    
    // 验证对象池比较
    expect(stats.objectPoolComparison).toBeDefined();
    expect(stats.objectPoolComparison!.withObjectPool.count).toBe(1);
    expect(stats.objectPoolComparison!.withoutObjectPool.count).toBe(1);
    expect(stats.objectPoolComparison!.withObjectPool.averageDuration).toBeLessThan(
      stats.objectPoolComparison!.withoutObjectPool.averageDuration
    );
  });

  it('应该能够比较批处理性能', () => {
    // 记录使用批处理的操作
    const op1Id = monitor.startOperation('withBatchProcessing');
    vi.advanceTimersByTime(10);
    monitor.endOperation(op1Id, { useBatchProcessing: true });
    
    // 记录不使用批处理的操作
    const op2Id = monitor.startOperation('withoutBatchProcessing');
    vi.advanceTimersByTime(20);
    monitor.endOperation(op2Id, { useBatchProcessing: false });
    
    // 获取统计信息
    const stats = monitor.getStats();
    
    // 验证批处理比较
    expect(stats.batchProcessingComparison).toBeDefined();
    expect(stats.batchProcessingComparison!.withBatchProcessing.count).toBe(1);
    expect(stats.batchProcessingComparison!.withoutBatchProcessing.count).toBe(1);
    expect(stats.batchProcessingComparison!.withBatchProcessing.averageDuration).toBeLessThan(
      stats.batchProcessingComparison!.withoutBatchProcessing.averageDuration
    );
  });
});
