import { Injectable, NotFoundException, BadRequestException, ForbiddenException, Optional } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, In } from 'typeorm';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { Asset, AssetStatus, AssetType } from './entities/asset.entity';
import { Category } from '../categories/entities/category.entity';
import { Tag } from '../tags/entities/tag.entity';
import { CreateAssetDto, UpdateAssetDto, AssetQueryDto } from './dto/create-asset.dto';
import { CacheService } from '../../common/services/cache.service';
import { LoggerService } from '../../common/services/logger.service';
import { StorageService } from '../../common/services/storage.service';

export interface AssetSearchResult {
  assets: Asset[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

@Injectable()
export class AssetsService {
  constructor(
    @InjectRepository(Asset)
    private readonly assetRepository: Repository<Asset>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(Tag)
    private readonly tagRepository: Repository<Tag>,
    @Optional() private readonly elasticsearchService: ElasticsearchService,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService,
    private readonly storageService: StorageService,
  ) {}

  /**
   * 创建资产
   */
  async create(createAssetDto: CreateAssetDto, userId: string): Promise<Asset> {
    const { categoryId, tagIds, ...assetData } = createAssetDto;

    // 验证分类是否存在
    const category = await this.categoryRepository.findOne({
      where: { id: categoryId },
    });
    if (!category) {
      throw new NotFoundException('分类不存在');
    }

    // 验证标签是否存在
    let tags: Tag[] = [];
    if (tagIds && tagIds.length > 0) {
      tags = await this.tagRepository.findBy({
        id: In(tagIds),
      });
      if (tags.length !== tagIds.length) {
        throw new BadRequestException('部分标签不存在');
      }
    }

    // 创建资产
    const asset = this.assetRepository.create({
      ...assetData,
      category,
      tags,
      creator: { id: userId } as any,
    });

    const savedAsset = await this.assetRepository.save(asset);

    // 索引到Elasticsearch
    await this.indexAssetToElasticsearch(savedAsset);

    // 清除相关缓存
    await this.clearRelatedCache(savedAsset);

    this.logger.log(`资产创建成功: ${savedAsset.id}`, 'AssetsService');
    return savedAsset;
  }

  /**
   * 查找所有资产（分页）
   */
  async findAll(query: AssetQueryDto): Promise<AssetSearchResult> {
    const cacheKey = `assets:list:${JSON.stringify(query)}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const queryBuilder = this.createQueryBuilder(query);
    
    // 计算总数
    const total = await queryBuilder.getCount();
    
    // 分页查询
    const { page = 1, limit = 20 } = query;
    const skip = (page - 1) * limit;
    
    const assets = await queryBuilder
      .skip(skip)
      .take(limit)
      .getMany();

    const result: AssetSearchResult = {
      assets,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };

    // 缓存结果
    await this.cacheService.set(cacheKey, result, 300); // 5分钟缓存

    return result;
  }

  /**
   * 根据ID查找资产
   */
  async findOne(id: string): Promise<Asset> {
    const cacheKey = `asset:${id}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const asset = await this.assetRepository.findOne({
      where: { id },
      relations: ['category', 'tags', 'creator', 'versions'],
    });

    if (!asset) {
      throw new NotFoundException('资产不存在');
    }

    // 增加查看次数
    await this.incrementViewCount(id);

    // 缓存结果
    await this.cacheService.set(cacheKey, asset, 600); // 10分钟缓存

    return asset;
  }

  /**
   * 更新资产
   */
  async update(id: string, updateAssetDto: UpdateAssetDto, userId: string): Promise<Asset> {
    const asset = await this.findOne(id);

    // 权限检查
    if (asset.creator.id !== userId) {
      throw new ForbiddenException('无权限修改此资产');
    }

    const { categoryId, tagIds, ...updateData } = updateAssetDto;

    // 更新分类
    if (categoryId) {
      const category = await this.categoryRepository.findOne({
        where: { id: categoryId },
      });
      if (!category) {
        throw new NotFoundException('分类不存在');
      }
      asset.category = category;
    }

    // 更新标签
    if (tagIds) {
      const tags = await this.tagRepository.findBy({
        id: In(tagIds),
      });
      if (tags.length !== tagIds.length) {
        throw new BadRequestException('部分标签不存在');
      }
      asset.tags = tags;
    }

    // 更新其他字段
    Object.assign(asset, updateData);

    const updatedAsset = await this.assetRepository.save(asset);

    // 更新Elasticsearch索引
    await this.indexAssetToElasticsearch(updatedAsset);

    // 清除相关缓存
    await this.clearRelatedCache(updatedAsset);

    this.logger.log(`资产更新成功: ${updatedAsset.id}`, 'AssetsService');
    return updatedAsset;
  }

  /**
   * 删除资产（软删除）
   */
  async remove(id: string, userId: string): Promise<void> {
    const asset = await this.findOne(id);

    // 权限检查
    if (asset.creator.id !== userId) {
      throw new ForbiddenException('无权限删除此资产');
    }

    // 软删除
    asset.deletedAt = new Date();
    await this.assetRepository.save(asset);

    // 从Elasticsearch中删除
    await this.removeAssetFromElasticsearch(id);

    // 清除相关缓存
    await this.clearRelatedCache(asset);

    this.logger.log(`资产删除成功: ${id}`, 'AssetsService');
  }

  /**
   * 搜索资产
   */
  async search(query: string, filters: any = {}): Promise<Asset[]> {
    // 如果没有 Elasticsearch 服务，直接使用数据库搜索
    if (!this.elasticsearchService) {
      return await this.fallbackSearch(query, filters);
    }

    try {
      const searchBody = {
        query: {
          bool: {
            must: [
              {
                multi_match: {
                  query,
                  fields: ['name^2', 'description', 'tags.name'],
                  fuzziness: 'AUTO',
                },
              },
            ],
            filter: [],
          },
        },
        highlight: {
          fields: {
            name: {},
            description: {},
          },
        },
      };

      // 添加过滤条件
      if (filters.type) {
        searchBody.query.bool.filter.push({ term: { type: filters.type } });
      }
      if (filters.categoryId) {
        searchBody.query.bool.filter.push({ term: { 'category.id': filters.categoryId } });
      }

      const response = await this.elasticsearchService.search({
        index: 'assets',
        body: searchBody,
      });

      const assetIds = response.hits.hits.map((hit: any) => hit._source.id);

      if (assetIds.length === 0) {
        return [];
      }

      return await this.assetRepository.findBy({
        id: In(assetIds),
      });
    } catch (error) {
      this.logger.error('Elasticsearch搜索失败', error, 'AssetsService');
      // 降级到数据库搜索
      return await this.fallbackSearch(query, filters);
    }
  }

  /**
   * 增加下载次数
   */
  async incrementDownloadCount(id: string): Promise<void> {
    await this.assetRepository.increment({ id }, 'downloadCount', 1);
    await this.cacheService.del(`asset:${id}`);
  }

  /**
   * 增加查看次数
   */
  private async incrementViewCount(id: string): Promise<void> {
    await this.assetRepository.increment({ id }, 'viewCount', 1);
  }

  /**
   * 创建查询构建器
   */
  private createQueryBuilder(query: AssetQueryDto): SelectQueryBuilder<Asset> {
    const queryBuilder = this.assetRepository
      .createQueryBuilder('asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('asset.tags', 'tags')
      .leftJoinAndSelect('asset.creator', 'creator')
      .where('asset.deletedAt IS NULL')
      .andWhere('asset.status = :status', { status: AssetStatus.APPROVED });

    // 搜索条件
    if (query.search) {
      queryBuilder.andWhere(
        '(asset.name ILIKE :search OR asset.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // 类型过滤
    if (query.type) {
      queryBuilder.andWhere('asset.type = :type', { type: query.type });
    }

    // 分类过滤
    if (query.categoryId) {
      queryBuilder.andWhere('category.id = :categoryId', { categoryId: query.categoryId });
    }

    // 标签过滤
    if (query.tagIds && query.tagIds.length > 0) {
      queryBuilder.andWhere('tags.id IN (:...tagIds)', { tagIds: query.tagIds });
    }

    // 许可证过滤
    if (query.license) {
      queryBuilder.andWhere('asset.license = :license', { license: query.license });
    }

    // 排序
    const { sortBy = 'createdAt', sortOrder = 'DESC' } = query;
    queryBuilder.orderBy(`asset.${sortBy}`, sortOrder);

    return queryBuilder;
  }

  /**
   * 索引资产到Elasticsearch
   */
  private async indexAssetToElasticsearch(asset: Asset): Promise<void> {
    // 如果没有 Elasticsearch 服务，跳过索引
    if (!this.elasticsearchService) {
      return;
    }

    try {
      await this.elasticsearchService.index({
        index: 'assets',
        id: asset.id,
        body: {
          id: asset.id,
          name: asset.name,
          description: asset.description,
          type: asset.type,
          status: asset.status,
          license: asset.license,
          category: {
            id: asset.category.id,
            name: asset.category.name,
          },
          tags: asset.tags.map(tag => ({
            id: tag.id,
            name: tag.name,
          })),
          createdAt: asset.createdAt,
          updatedAt: asset.updatedAt,
        },
      });
    } catch (error) {
      this.logger.error('Elasticsearch索引失败', error, 'AssetsService');
    }
  }

  /**
   * 从Elasticsearch删除资产
   */
  private async removeAssetFromElasticsearch(id: string): Promise<void> {
    try {
      await this.elasticsearchService.delete({
        index: 'assets',
        id,
      });
    } catch (error) {
      this.logger.error('Elasticsearch删除失败', error, 'AssetsService');
    }
  }

  /**
   * 降级搜索（数据库）
   */
  private async fallbackSearch(query: string, filters: any): Promise<Asset[]> {
    const queryBuilder = this.assetRepository
      .createQueryBuilder('asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('asset.tags', 'tags')
      .where('asset.deletedAt IS NULL')
      .andWhere('asset.status = :status', { status: AssetStatus.APPROVED })
      .andWhere('(asset.name ILIKE :search OR asset.description ILIKE :search)', {
        search: `%${query}%`,
      });

    if (filters.type) {
      queryBuilder.andWhere('asset.type = :type', { type: filters.type });
    }

    return await queryBuilder.limit(50).getMany();
  }

  /**
   * 清除相关缓存
   */
  private async clearRelatedCache(asset: Asset): Promise<void> {
    const patterns = [
      `asset:${asset.id}`,
      'assets:list:*',
      `category:${asset.category.id}:assets`,
    ];

    for (const pattern of patterns) {
      await this.cacheService.del(pattern);
    }
  }

  /**
   * 获取热门资产
   */
  async getPopularAssets(limit: number = 10): Promise<Asset[]> {
    const cacheKey = `assets:popular:${limit}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const assets = await this.assetRepository.find({
      where: {
        status: AssetStatus.APPROVED,
        deletedAt: null,
      },
      relations: ['category', 'tags', 'creator'],
      order: {
        downloadCount: 'DESC',
        rating: 'DESC',
      },
      take: limit,
    });

    await this.cacheService.set(cacheKey, assets, 1800); // 30分钟缓存
    return assets;
  }

  /**
   * 获取最新资产
   */
  async getLatestAssets(limit: number = 10): Promise<Asset[]> {
    const cacheKey = `assets:latest:${limit}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const assets = await this.assetRepository.find({
      where: {
        status: AssetStatus.APPROVED,
        deletedAt: null,
      },
      relations: ['category', 'tags', 'creator'],
      order: {
        publishedAt: 'DESC',
      },
      take: limit,
    });

    await this.cacheService.set(cacheKey, assets, 600); // 10分钟缓存
    return assets;
  }

  /**
   * 获取推荐资产
   */
  async getRecommendedAssets(userId: string, limit: number = 10): Promise<Asset[]> {
    // 这里可以实现基于用户行为的推荐算法
    // 暂时返回高评分资产
    return await this.assetRepository.find({
      where: {
        status: AssetStatus.APPROVED,
        deletedAt: null,
      },
      relations: ['category', 'tags', 'creator'],
      order: {
        rating: 'DESC',
        downloadCount: 'DESC',
      },
      take: limit,
    });
  }
}
