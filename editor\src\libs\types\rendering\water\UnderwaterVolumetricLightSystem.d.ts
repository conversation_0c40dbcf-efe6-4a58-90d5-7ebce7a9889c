/**
 * 水下体积光渲染系统
 * 创建更真实的水下光束效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
/**
 * 水下体积光配置
 */
export interface UnderwaterVolumetricLightConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 光线密度 */
    density?: number;
    /** 光线衰减 */
    decay?: number;
    /** 光线权重 */
    weight?: number;
    /** 光线曝光 */
    exposure?: number;
    /** 光线采样数 */
    samples?: number;
    /** 光线颜色 */
    color?: THREE.Color;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 水下体积光系统事件类型
 */
export declare enum UnderwaterVolumetricLightEventType {
    /** 体积光更新 */
    VOLUMETRIC_LIGHT_UPDATED = "volumetricLightUpdated",
    /** 体积光参数变化 */
    VOLUMETRIC_LIGHT_PARAMS_CHANGED = "volumetricLightParamsChanged"
}
/**
 * 水下体积光系统
 */
export declare class UnderwaterVolumetricLightSystem extends System {
    /** 系统类型 */
    static readonly TYPE = "UnderwaterVolumetricLightSystem";
    /** 配置 */
    private config;
    /** 水体实体映射 */
    private waterEntities;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监视器 */
    private performanceMonitor;
    /** 调试对象 */
    private debugObjects;
    /** 体积光渲染目标 */
    private renderTarget;
    /** 体积光场景 */
    private volumetricScene;
    /** 体积光相机 */
    private volumetricCamera;
    /** 体积光材质 */
    private volumetricMaterial;
    /** 体积光网格 */
    private volumetricMesh;
    /** 光源列表 */
    private lightSources;
    /** 是否在水下 */
    private isUnderwater;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: UnderwaterVolumetricLightConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 窗口大小变化处理
     */
    private onWindowResize;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 检查是否在水下
     */
    private checkIfUnderwater;
    /**
     * 渲染体积光
     */
    private renderVolumetricLight;
    /**
     * 更新体积光相机
     * @param camera 相机
     */
    private updateVolumetricCamera;
    /**
     * 更新光源
     * @param scene 场景
     */
    private updateLightSources;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 添加水体实体
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterEntity(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体实体
     * @param entity 实体
     */
    removeWaterEntity(entity: Entity): void;
    /**
     * 设置体积光参数
     * @param params 参数
     */
    setVolumetricLightParams(params: Partial<UnderwaterVolumetricLightConfig>): void;
    /**
     * 获取体积光顶点着色器
     * @returns 顶点着色器代码
     */
    private getVolumetricLightVertexShader;
    /**
     * 获取体积光片段着色器
     * @returns 片段着色器代码
     */
    private getVolumetricLightFragmentShader;
    /**
     * 获取渲染目标
     * @returns 渲染目标
     */
    getRenderTarget(): THREE.WebGLRenderTarget;
    /**
     * 是否在水下
     * @returns 是否在水下
     */
    isInUnderwater(): boolean;
}
