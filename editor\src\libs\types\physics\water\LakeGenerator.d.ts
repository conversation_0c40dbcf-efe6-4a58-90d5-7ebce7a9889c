/**
 * 湖泊生成器
 * 用于生成湖泊水体
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { TerrainComponent } from '../../terrain/components/TerrainComponent';
/**
 * 湖泊形状类型
 */
export declare enum LakeShapeType {
    /** 圆形 */
    CIRCLE = "circle",
    /** 椭圆形 */
    ELLIPSE = "ellipse",
    /** 不规则形状 */
    IRREGULAR = "irregular",
    /** 自定义形状 */
    CUSTOM = "custom"
}
/**
 * 湖泊生成配置
 */
export interface LakeGeneratorConfig {
    /** 湖泊位置 */
    position: THREE.Vector3;
    /** 湖泊尺寸 */
    size: {
        width: number;
        depth: number;
    };
    /** 湖泊深度 */
    depth: number;
    /** 湖泊形状类型 */
    shapeType?: LakeShapeType;
    /** 湖泊形状参数（用于不规则形状） */
    shapeParams?: {
        /** 不规则度（0-1） */
        irregularity?: number;
        /** 种子 */
        seed?: number;
        /** 控制点数量 */
        controlPoints?: number;
    };
    /** 自定义形状点（用于自定义形状） */
    customShapePoints?: THREE.Vector2[];
    /** 分辨率 */
    resolution?: number;
    /** 是否跟随地形 */
    followTerrain?: boolean;
    /** 地形偏移 */
    terrainOffset?: number;
    /** 是否生成湖岸 */
    generateShore?: boolean;
    /** 湖岸宽度 */
    shoreWidth?: number;
    /** 湖岸高度 */
    shoreHeight?: number;
    /** 是否生成湖床 */
    generateLakeBed?: boolean;
    /** 湖床材质 */
    lakeBedMaterial?: any;
    /** 是否生成水下植被 */
    generateUnderwaterVegetation?: boolean;
    /** 水下植被密度 */
    underwaterVegetationDensity?: number;
    /** 是否生成水下粒子 */
    generateUnderwaterParticles?: boolean;
    /** 水下粒子数量 */
    underwaterParticleCount?: number;
}
/**
 * 湖泊生成器
 */
export declare class LakeGenerator {
    /** 世界 */
    private world;
    /** 配置 */
    private config;
    /** 地形组件 */
    private terrainComponent;
    /** 湖泊实体 */
    private lakeEntity;
    /** 湖泊水体组件 */
    private lakeWaterBody;
    /** 湖泊几何体 */
    private lakeGeometry;
    /** 湖岸几何体 */
    private shoreGeometry;
    /** 湖床几何体 */
    private lakeBedGeometry;
    /** 形状点 */
    private shapePoints;
    /** 粒子更新函数 */
    private particleUpdateFunction;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config: LakeGeneratorConfig);
    /**
     * 设置地形组件
     * @param terrainComponent 地形组件
     */
    setTerrainComponent(terrainComponent: TerrainComponent): void;
    /**
     * 生成湖泊
     * @returns 湖泊实体
     */
    generate(): Entity;
    /**
     * 生成形状点
     */
    private generateShapePoints;
    /**
     * 生成圆形形状点
     */
    private generateCircleShapePoints;
    /**
     * 生成椭圆形状点
     */
    private generateEllipseShapePoints;
    /**
     * 生成不规则形状点
     */
    private generateIrregularShapePoints;
    /**
     * 基于种子的随机数生成器
     * @param seed 种子
     * @returns 随机数生成函数
     */
    private seededRandom;
    /**
     * 创建湖泊几何体
     */
    private createLakeGeometry;
    /**
     * 创建湖泊水体
     */
    private createLakeWaterBody;
    /**
     * 创建湖岸
     */
    private createShore;
    /**
     * 创建湖床
     */
    private createLakeBed;
    /**
     * 创建水下植被
     */
    private createUnderwaterVegetation;
    /**
     * 创建水下粒子
     */
    private createUnderwaterParticles;
    /**
     * 获取粒子更新函数
     * @returns 粒子更新函数，如果没有粒子则返回null
     */
    getParticleUpdateFunction(): ((deltaTime: number) => void) | null;
    /**
     * 获取指定位置的地形高度
     * @param x X坐标
     * @param z Z坐标
     * @returns 地形高度，如果无法获取则返回null
     */
    private getTerrainHeight;
}
