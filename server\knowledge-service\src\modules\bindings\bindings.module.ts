import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DigitalHumanKnowledgeBinding } from '../../entities/digital-human-knowledge-binding.entity';
import { BindingsController } from './bindings.controller';
import { BindingsService } from './bindings.service';

@Module({
  imports: [TypeOrmModule.forFeature([DigitalHumanKnowledgeBinding])],
  controllers: [BindingsController],
  providers: [BindingsService],
  exports: [BindingsService],
})
export class BindingsModule {}
