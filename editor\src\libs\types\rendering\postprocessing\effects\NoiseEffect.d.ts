import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * 噪点效果选项
 */
export interface NoiseEffectOptions extends PostProcessingEffectOptions {
    /** 强度 */
    intensity?: number;
    /** 是否使用彩色噪点 */
    colored?: boolean;
    /** 是否使用动态噪点 */
    animated?: boolean;
    /** 是否预乘 */
    premultiply?: boolean;
}
/**
 * 噪点效果
 */
export declare class NoiseEffect extends PostProcessingEffect {
    /** 强度 */
    private intensity;
    /** 是否使用彩色噪点 */
    private colored;
    /** 是否使用动态噪点 */
    private animated;
    /** 是否预乘 */
    private premultiply;
    /** 噪点通道 */
    private noisePass;
    /** 时间 */
    private time;
    /**
     * 创建噪点效果
     * @param options 噪点效果选项
     */
    constructor(options?: NoiseEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 更新效果
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 设置强度
     * @param intensity 强度
     */
    setIntensity(intensity: number): void;
    /**
     * 获取强度
     * @returns 强度
     */
    getIntensity(): number;
    /**
     * 设置是否使用彩色噪点
     * @param colored 是否使用彩色噪点
     */
    setColored(colored: boolean): void;
    /**
     * 获取是否使用彩色噪点
     * @returns 是否使用彩色噪点
     */
    isColored(): boolean;
    /**
     * 设置是否使用动态噪点
     * @param animated 是否使用动态噪点
     */
    setAnimated(animated: boolean): void;
    /**
     * 获取是否使用动态噪点
     * @returns 是否使用动态噪点
     */
    isAnimated(): boolean;
    /**
     * 设置是否预乘
     * @param premultiply 是否预乘
     */
    setPremultiply(premultiply: boolean): void;
    /**
     * 获取是否预乘
     * @returns 是否预乘
     */
    isPremultiply(): boolean;
}
