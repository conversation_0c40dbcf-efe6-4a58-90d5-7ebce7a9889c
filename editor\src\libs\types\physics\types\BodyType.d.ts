/**
 * 物理体类型定义
 */
export declare enum BodyType {
    /** 静态物体 - 不会移动，无限质量 */
    STATIC = "static",
    /** 动态物体 - 可以移动，受力影响 */
    DYNAMIC = "dynamic",
    /** 运动学物体 - 可以移动，但不受力影响 */
    KINEMATIC = "kinematic"
}
export interface BodyTypeConfig {
    /** 物体类型 */
    type: BodyType;
    /** 质量 */
    mass?: number;
    /** 是否受重力影响 */
    useGravity?: boolean;
    /** 是否为触发器 */
    isTrigger?: boolean;
    /** 线性阻尼 */
    linearDamping?: number;
    /** 角阻尼 */
    angularDamping?: number;
}
export declare class BodyTypeHelper {
    /**
     * 检查物体类型是否为静态
     */
    static isStatic(type: BodyType): boolean;
    /**
     * 检查物体类型是否为动态
     */
    static isDynamic(type: BodyType): boolean;
    /**
     * 检查物体类型是否为运动学
     */
    static isKinematic(type: BodyType): boolean;
    /**
     * 检查物体是否可以移动
     */
    static canMove(type: BodyType): boolean;
    /**
     * 检查物体是否受力影响
     */
    static isAffectedByForces(type: BodyType): boolean;
    /**
     * 获取默认配置
     */
    static getDefaultConfig(type: BodyType): BodyTypeConfig;
}
