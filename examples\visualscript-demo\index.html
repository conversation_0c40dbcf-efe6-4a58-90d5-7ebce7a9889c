<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>视觉脚本演示</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      font-family: 'Microsoft YaHei', sans-serif;
      display: flex;
    }
    #canvas-container {
      flex: 1;
      height: 100vh;
    }
    #editor-container {
      width: 400px;
      height: 100vh;
      background-color: #2a2a2a;
      color: #fff;
      overflow-y: auto;
      box-shadow: -2px 0 5px rgba(0, 0, 0, 0.3);
    }
    .editor-header {
      padding: 10px;
      background-color: #1a1a1a;
      border-bottom: 1px solid #3a3a3a;
    }
    h2 {
      margin: 0;
      font-size: 18px;
      color: #fff;
    }
    .editor-content {
      padding: 10px;
    }
    .node-list {
      margin-bottom: 20px;
    }
    .node-category {
      margin-bottom: 10px;
    }
    .category-title {
      font-weight: bold;
      color: #4fc3f7;
      margin-bottom: 5px;
      padding-bottom: 3px;
      border-bottom: 1px solid #3a3a3a;
    }
    .node-item {
      padding: 5px 10px;
      margin-bottom: 3px;
      background-color: #3a3a3a;
      border-radius: 3px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    .node-item:hover {
      background-color: #4a4a4a;
    }
    .script-view {
      background-color: #1a1a1a;
      border-radius: 3px;
      padding: 10px;
      margin-bottom: 20px;
    }
    .script-title {
      font-weight: bold;
      color: #4fc3f7;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .script-actions {
      display: flex;
      gap: 5px;
    }
    .script-action {
      background-color: #333;
      color: #fff;
      border: none;
      border-radius: 3px;
      padding: 3px 8px;
      font-size: 12px;
      cursor: pointer;
    }
    .script-action:hover {
      background-color: #444;
    }
    .script-content {
      font-family: monospace;
      font-size: 12px;
      line-height: 1.4;
      white-space: pre-wrap;
      color: #ddd;
      max-height: 200px;
      overflow-y: auto;
    }
    .node-connection {
      margin-left: 20px;
      padding-left: 10px;
      border-left: 1px dashed #4a4a4a;
    }
    .node {
      margin-bottom: 5px;
    }
    .node-header {
      display: flex;
      align-items: center;
      margin-bottom: 3px;
    }
    .node-type {
      color: #ff9800;
      margin-right: 5px;
    }
    .node-name {
      color: #4fc3f7;
    }
    .node-params {
      margin-left: 20px;
      font-size: 11px;
      color: #aaa;
    }
    .param {
      margin-bottom: 2px;
    }
    .param-name {
      color: #9ccc65;
    }
    .param-value {
      color: #ce93d8;
    }
    .console {
      background-color: #1a1a1a;
      border-radius: 3px;
      padding: 10px;
      margin-top: 20px;
      max-height: 150px;
      overflow-y: auto;
    }
    .console-title {
      font-weight: bold;
      color: #4fc3f7;
      margin-bottom: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .console-clear {
      background-color: #333;
      color: #fff;
      border: none;
      border-radius: 3px;
      padding: 3px 8px;
      font-size: 12px;
      cursor: pointer;
    }
    .console-content {
      font-family: monospace;
      font-size: 12px;
      line-height: 1.4;
      color: #ddd;
    }
    .log {
      margin-bottom: 3px;
      padding-bottom: 3px;
      border-bottom: 1px solid #333;
    }
    .log-time {
      color: #9e9e9e;
      font-size: 10px;
    }
    .log-message {
      color: #fff;
    }
    .log-error {
      color: #f44336;
    }
    .log-warning {
      color: #ffeb3b;
    }
    .log-info {
      color: #2196f3;
    }
    .button-group {
      display: flex;
      gap: 5px;
      margin-bottom: 10px;
    }
    .button {
      background-color: #4fc3f7;
      color: #fff;
      border: none;
      border-radius: 3px;
      padding: 8px 12px;
      cursor: pointer;
      flex-grow: 1;
      text-align: center;
    }
    .button:hover {
      background-color: #29b6f6;
    }
    .info-box {
      background-color: #1a1a1a;
      padding: 10px;
      border-radius: 3px;
      margin-bottom: 15px;
      font-size: 14px;
      color: #fff;
      border-left: 4px solid #4fc3f7;
    }
  </style>
</head>
<body>
  <div id="canvas-container"></div>
  <div id="editor-container">
    <div class="editor-header">
      <h2>视觉脚本演示</h2>
    </div>
    <div class="editor-content">
      <div class="info-box">
        本示例展示了DL（Digital Learning）引擎的视觉脚本系统功能，包括节点创建、连接、调试和执行。
        点击下方的按钮来加载和运行不同的示例脚本。
      </div>
      
      <div class="button-group">
        <div class="button" id="load-basic">基础示例</div>
        <div class="button" id="load-animation">动画示例</div>
        <div class="button" id="load-physics">物理示例</div>
      </div>
      
      <div class="script-view">
        <div class="script-title">
          <span>当前脚本: <span id="current-script-name">基础示例</span></span>
          <div class="script-actions">
            <button class="script-action" id="run-script">运行</button>
            <button class="script-action" id="stop-script">停止</button>
            <button class="script-action" id="debug-script">调试</button>
          </div>
        </div>
        <div class="script-content" id="script-content">
          <!-- 脚本内容将在这里显示 -->
        </div>
      </div>
      
      <div class="node-list">
        <div class="category-title">可用节点</div>
        
        <div class="node-category">
          <div class="category-title">事件节点</div>
          <div class="node-item">开始 (onStart)</div>
          <div class="node-item">更新 (onUpdate)</div>
          <div class="node-item">点击 (onClick)</div>
          <div class="node-item">碰撞 (onCollision)</div>
        </div>
        
        <div class="node-category">
          <div class="category-title">实体节点</div>
          <div class="node-item">创建实体 (createEntity)</div>
          <div class="node-item">获取实体 (getEntity)</div>
          <div class="node-item">添加组件 (addComponent)</div>
          <div class="node-item">获取组件 (getComponent)</div>
        </div>
        
        <div class="node-category">
          <div class="category-title">变换节点</div>
          <div class="node-item">设置位置 (setPosition)</div>
          <div class="node-item">设置旋转 (setRotation)</div>
          <div class="node-item">设置缩放 (setScale)</div>
          <div class="node-item">朝向 (lookAt)</div>
        </div>
        
        <div class="node-category">
          <div class="category-title">物理节点</div>
          <div class="node-item">添加刚体 (addRigidBody)</div>
          <div class="node-item">添加碰撞体 (addCollider)</div>
          <div class="node-item">应用力 (applyForce)</div>
          <div class="node-item">应用冲量 (applyImpulse)</div>
        </div>
        
        <div class="node-category">
          <div class="category-title">动画节点</div>
          <div class="node-item">播放动画 (playAnimation)</div>
          <div class="node-item">停止动画 (stopAnimation)</div>
          <div class="node-item">混合动画 (blendAnimation)</div>
          <div class="node-item">设置动画参数 (setAnimationParam)</div>
        </div>
        
        <div class="node-category">
          <div class="category-title">逻辑节点</div>
          <div class="node-item">分支 (branch)</div>
          <div class="node-item">循环 (loop)</div>
          <div class="node-item">延迟 (delay)</div>
          <div class="node-item">序列 (sequence)</div>
        </div>
      </div>
      
      <div class="console">
        <div class="console-title">
          <span>控制台</span>
          <button class="console-clear" id="clear-console">清除</button>
        </div>
        <div class="console-content" id="console-content">
          <!-- 控制台输出将在这里显示 -->
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import { Engine, World, Entity, Scene, Transform, Camera, Mesh, Material, Light, VisualScriptSystem, VisualScriptComponent, Graph, Node } from '/engine/dist/index.js';

    // 初始化引擎
    const engine = new Engine({
      container: document.getElementById('canvas-container'),
      antialias: true,
      shadows: true,
    });

    // 创建世界
    const world = new World(engine);

    // 创建视觉脚本系统
    const visualScriptSystem = new VisualScriptSystem(world);
    world.addSystem(visualScriptSystem);

    // 创建场景
    const scene = new Scene(world, {
      name: '视觉脚本演示场景',
      background: { type: 'color', value: '#87CEEB' },
    });

    // 创建相机
    const camera = new Entity(world)
      .addComponent(new Camera({
        type: 'perspective',
        fov: 60,
        near: 0.1,
        far: 1000,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 2, z: 5 },
        rotation: { x: -0.2, y: 0, z: 0 },
      }));

    // 添加相机到场景
    scene.addEntity(camera);

    // 创建环境光
    const ambientLight = new Entity(world)
      .addComponent(new Light({
        type: 'ambient',
        color: '#ffffff',
        intensity: 0.3,
      }));

    // 添加环境光到场景
    scene.addEntity(ambientLight);

    // 创建平行光
    const directionalLight = new Entity(world)
      .addComponent(new Light({
        type: 'directional',
        color: '#ffffff',
        intensity: 0.8,
        castShadow: true,
      }))
      .addComponent(new Transform({
        position: { x: 5, y: 10, z: 5 },
        rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
      }));

    // 添加平行光到场景
    scene.addEntity(directionalLight);

    // 创建地面
    const ground = new Entity(world)
      .addComponent(new Mesh({
        geometry: { type: 'plane', width: 20, height: 20 },
        receiveShadow: true,
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#cccccc',
        roughness: 0.9,
        metalness: 0.1,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: -Math.PI / 2, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      }));

    // 添加地面到场景
    scene.addEntity(ground);

    // 启动引擎
    engine.start();

    // 示例脚本
    const scripts = {
      basic: {
        name: '基础示例',
        content: `
{
  "nodes": [
    {
      "id": "start",
      "type": "core/events/onStart",
      "position": { "x": 100, "y": 100 },
      "data": { "label": "开始" },
      "flows": {
        "flow": { "nodeId": "create-cube", "socket": "flow" }
      }
    },
    {
      "id": "create-cube",
      "type": "entity/create",
      "position": { "x": 300, "y": 100 },
      "data": { "label": "创建立方体" },
      "parameters": {
        "name": { "value": "立方体" }
      },
      "flows": {
        "flow": { "nodeId": "add-mesh", "socket": "flow" }
      },
      "outputs": {
        "entity": { "nodeId": "add-mesh", "socket": "entity" }
      }
    },
    {
      "id": "add-mesh",
      "type": "entity/addMesh",
      "position": { "x": 500, "y": 100 },
      "data": { "label": "添加网格" },
      "parameters": {
        "geometry": { "value": { "type": "box", "width": 1, "height": 1, "depth": 1 } },
        "material": { "value": { "type": "standard", "color": "#ff0000", "roughness": 0.5, "metalness": 0.5 } }
      },
      "flows": {
        "flow": { "nodeId": "set-position", "socket": "flow" }
      },
      "outputs": {
        "entity": { "nodeId": "set-position", "socket": "entity" }
      }
    },
    {
      "id": "set-position",
      "type": "transform/setPosition",
      "position": { "x": 700, "y": 100 },
      "data": { "label": "设置位置" },
      "parameters": {
        "position": { "value": { "x": 0, "y": 1, "z": 0 } }
      },
      "flows": {
        "flow": { "nodeId": "log", "socket": "flow" }
      }
    },
    {
      "id": "log",
      "type": "core/debug/log",
      "position": { "x": 900, "y": 100 },
      "data": { "label": "输出日志" },
      "parameters": {
        "message": { "value": "立方体创建完成！" },
        "level": { "value": "info" }
      }
    }
  ]
}`,
      },
      animation: {
        name: '动画示例',
        content: `
{
  "nodes": [
    {
      "id": "start",
      "type": "core/events/onStart",
      "position": { "x": 100, "y": 100 },
      "data": { "label": "开始" },
      "flows": {
        "flow": { "nodeId": "create-cube", "socket": "flow" }
      }
    },
    {
      "id": "create-cube",
      "type": "entity/create",
      "position": { "x": 300, "y": 100 },
      "data": { "label": "创建立方体" },
      "parameters": {
        "name": { "value": "动画立方体" }
      },
      "flows": {
        "flow": { "nodeId": "add-mesh", "socket": "flow" }
      },
      "outputs": {
        "entity": { "nodeId": "add-mesh", "socket": "entity" }
      }
    },
    {
      "id": "add-mesh",
      "type": "entity/addMesh",
      "position": { "x": 500, "y": 100 },
      "data": { "label": "添加网格" },
      "parameters": {
        "geometry": { "value": { "type": "box", "width": 1, "height": 1, "depth": 1 } },
        "material": { "value": { "type": "standard", "color": "#00ff00", "roughness": 0.5, "metalness": 0.5 } }
      },
      "flows": {
        "flow": { "nodeId": "set-position", "socket": "flow" }
      },
      "outputs": {
        "entity": { "nodeId": "set-position", "socket": "entity" }
      }
    },
    {
      "id": "set-position",
      "type": "transform/setPosition",
      "position": { "x": 700, "y": 100 },
      "data": { "label": "设置位置" },
      "parameters": {
        "position": { "value": { "x": 0, "y": 1, "z": 0 } }
      },
      "flows": {
        "flow": { "nodeId": "add-animation", "socket": "flow" }
      },
      "outputs": {
        "entity": { "nodeId": "add-animation", "socket": "entity" }
      }
    },
    {
      "id": "add-animation",
      "type": "animation/addAnimator",
      "position": { "x": 900, "y": 100 },
      "data": { "label": "添加动画器" },
      "flows": {
        "flow": { "nodeId": "create-animation", "socket": "flow" }
      },
      "outputs": {
        "entity": { "nodeId": "create-animation", "socket": "entity" }
      }
    },
    {
      "id": "create-animation",
      "type": "animation/createAnimation",
      "position": { "x": 1100, "y": 100 },
      "data": { "label": "创建动画" },
      "parameters": {
        "name": { "value": "旋转动画" },
        "duration": { "value": 2.0 },
        "loop": { "value": true }
      },
      "flows": {
        "flow": { "nodeId": "add-keyframe1", "socket": "flow" }
      },
      "outputs": {
        "animation": { "nodeId": "add-keyframe1", "socket": "animation" }
      }
    },
    {
      "id": "add-keyframe1",
      "type": "animation/addKeyframe",
      "position": { "x": 1300, "y": 100 },
      "data": { "label": "添加关键帧1" },
      "parameters": {
        "time": { "value": 0.0 },
        "property": { "value": "rotation" },
        "value": { "value": { "x": 0, "y": 0, "z": 0 } }
      },
      "flows": {
        "flow": { "nodeId": "add-keyframe2", "socket": "flow" }
      },
      "outputs": {
        "animation": { "nodeId": "add-keyframe2", "socket": "animation" }
      }
    },
    {
      "id": "add-keyframe2",
      "type": "animation/addKeyframe",
      "position": { "x": 1500, "y": 100 },
      "data": { "label": "添加关键帧2" },
      "parameters": {
        "time": { "value": 2.0 },
        "property": { "value": "rotation" },
        "value": { "value": { "x": 0, "y": 6.28, "z": 0 } }
      },
      "flows": {
        "flow": { "nodeId": "play-animation", "socket": "flow" }
      },
      "outputs": {
        "animation": { "nodeId": "play-animation", "socket": "animation" }
      }
    },
    {
      "id": "play-animation",
      "type": "animation/playAnimation",
      "position": { "x": 1700, "y": 100 },
      "data": { "label": "播放动画" },
      "flows": {
        "flow": { "nodeId": "log", "socket": "flow" }
      }
    },
    {
      "id": "log",
      "type": "core/debug/log",
      "position": { "x": 1900, "y": 100 },
      "data": { "label": "输出日志" },
      "parameters": {
        "message": { "value": "动画开始播放！" },
        "level": { "value": "info" }
      }
    }
  ]
}`,
      },
      physics: {
        name: '物理示例',
        content: `
{
  "nodes": [
    {
      "id": "start",
      "type": "core/events/onStart",
      "position": { "x": 100, "y": 100 },
      "data": { "label": "开始" },
      "flows": {
        "flow": { "nodeId": "create-cube", "socket": "flow" }
      }
    },
    {
      "id": "create-cube",
      "type": "entity/create",
      "position": { "x": 300, "y": 100 },
      "data": { "label": "创建立方体" },
      "parameters": {
        "name": { "value": "物理立方体" }
      },
      "flows": {
        "flow": { "nodeId": "add-mesh", "socket": "flow" }
      },
      "outputs": {
        "entity": { "nodeId": "add-mesh", "socket": "entity" }
      }
    },
    {
      "id": "add-mesh",
      "type": "entity/addMesh",
      "position": { "x": 500, "y": 100 },
      "data": { "label": "添加网格" },
      "parameters": {
        "geometry": { "value": { "type": "box", "width": 1, "height": 1, "depth": 1 } },
        "material": { "value": { "type": "standard", "color": "#0000ff", "roughness": 0.5, "metalness": 0.5 } }
      },
      "flows": {
        "flow": { "nodeId": "set-position", "socket": "flow" }
      },
      "outputs": {
        "entity": { "nodeId": "set-position", "socket": "entity" }
      }
    },
    {
      "id": "set-position",
      "type": "transform/setPosition",
      "position": { "x": 700, "y": 100 },
      "data": { "label": "设置位置" },
      "parameters": {
        "position": { "value": { "x": 0, "y": 5, "z": 0 } }
      },
      "flows": {
        "flow": { "nodeId": "add-rigidbody", "socket": "flow" }
      },
      "outputs": {
        "entity": { "nodeId": "add-rigidbody", "socket": "entity" }
      }
    },
    {
      "id": "add-rigidbody",
      "type": "physics/addRigidbody",
      "position": { "x": 900, "y": 100 },
      "data": { "label": "添加刚体" },
      "parameters": {
        "type": { "value": "dynamic" },
        "mass": { "value": 1.0 }
      },
      "flows": {
        "flow": { "nodeId": "add-collider", "socket": "flow" }
      },
      "outputs": {
        "entity": { "nodeId": "add-collider", "socket": "entity" }
      }
    },
    {
      "id": "add-collider",
      "type": "physics/addCollider",
      "position": { "x": 1100, "y": 100 },
      "data": { "label": "添加碰撞体" },
      "parameters": {
        "type": { "value": "box" },
        "width": { "value": 1.0 },
        "height": { "value": 1.0 },
        "depth": { "value": 1.0 },
        "restitution": { "value": 0.7 }
      },
      "flows": {
        "flow": { "nodeId": "log", "socket": "flow" }
      }
    },
    {
      "id": "log",
      "type": "core/debug/log",
      "position": { "x": 1300, "y": 100 },
      "data": { "label": "输出日志" },
      "parameters": {
        "message": { "value": "物理立方体创建完成！" },
        "level": { "value": "info" }
      }
    }
  ]
}`,
      },
    };

    // UI控制
    const scriptContentElement = document.getElementById('script-content');
    const currentScriptNameElement = document.getElementById('current-script-name');
    const consoleContentElement = document.getElementById('console-content');
    
    // 加载脚本
    function loadScript(scriptName) {
      const script = scripts[scriptName];
      if (script) {
        currentScriptNameElement.textContent = script.name;
        scriptContentElement.textContent = script.content;
        addConsoleLog(`已加载脚本: ${script.name}`);
      }
    }
    
    // 添加控制台日志
    function addConsoleLog(message, level = 'log') {
      const logElement = document.createElement('div');
      logElement.className = 'log';
      
      const now = new Date();
      const timeString = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
      
      logElement.innerHTML = `
        <span class="log-time">[${timeString}]</span>
        <span class="log-message ${level !== 'log' ? 'log-' + level : ''}">${message}</span>
      `;
      
      consoleContentElement.appendChild(logElement);
      consoleContentElement.scrollTop = consoleContentElement.scrollHeight;
    }
    
    // 事件监听
    document.getElementById('load-basic').addEventListener('click', () => loadScript('basic'));
    document.getElementById('load-animation').addEventListener('click', () => loadScript('animation'));
    document.getElementById('load-physics').addEventListener('click', () => loadScript('physics'));
    
    document.getElementById('run-script').addEventListener('click', () => {
      addConsoleLog('运行脚本...', 'info');
      // 这里应该实际运行脚本，但为了演示，我们只是添加一些日志
      setTimeout(() => addConsoleLog('创建实体...'), 500);
      setTimeout(() => addConsoleLog('添加组件...'), 1000);
      setTimeout(() => addConsoleLog('设置属性...'), 1500);
      setTimeout(() => addConsoleLog('脚本执行完成！', 'info'), 2000);
    });
    
    document.getElementById('stop-script').addEventListener('click', () => {
      addConsoleLog('停止脚本执行', 'warning');
    });
    
    document.getElementById('debug-script').addEventListener('click', () => {
      addConsoleLog('进入调试模式', 'info');
    });
    
    document.getElementById('clear-console').addEventListener('click', () => {
      consoleContentElement.innerHTML = '';
    });
    
    // 初始加载基础脚本
    loadScript('basic');
  </script>
</body>
</html>
