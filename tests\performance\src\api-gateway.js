/**
 * API网关性能测试
 */
import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';
import { CONFIG, LOAD_PROFILES, TEST_USERS, randomSleep } from './config.js';

// 自定义指标
const authFailRate = new Rate('auth_failures');
const routingLatency = new Trend('routing_latency');
const requestsPerSecond = new Counter('requests_per_second');

// 测试配置
export const options = LOAD_PROFILES.medium;

// 测试数据
const testData = {
  tokens: new Map(),
};

// 初始化函数 - 在测试开始前运行一次
export function setup() {
  console.log('开始API网关性能测试');
  
  // 预先创建一些测试用户并获取令牌
  const preAuthUsers = TEST_USERS.slice(0, 10);
  
  for (const user of preAuthUsers) {
    const loginRes = http.post(`${CONFIG.apiGateway}/auth/login`, JSON.stringify({
      username: user.username,
      password: user.password,
    }), {
      headers: { 'Content-Type': 'application/json' },
    });
    
    if (loginRes.status === 200) {
      const body = JSON.parse(loginRes.body);
      testData.tokens.set(user.username, body.accessToken);
    } else {
      // 如果登录失败，尝试注册
      const registerRes = http.post(`${CONFIG.apiGateway}/auth/register`, JSON.stringify({
        username: user.username,
        password: user.password,
        email: user.email,
      }), {
        headers: { 'Content-Type': 'application/json' },
      });
      
      if (registerRes.status === 201) {
        // 注册成功后登录
        const loginRes = http.post(`${CONFIG.apiGateway}/auth/login`, JSON.stringify({
          username: user.username,
          password: user.password,
        }), {
          headers: { 'Content-Type': 'application/json' },
        });
        
        if (loginRes.status === 200) {
          const body = JSON.parse(loginRes.body);
          testData.tokens.set(user.username, body.accessToken);
        }
      }
    }
  }
  
  return testData;
}

// 默认函数 - 每个虚拟用户都会运行
export default function(data) {
  // 随机选择一个已认证的用户
  const usernames = Array.from(data.tokens.keys());
  const username = usernames[Math.floor(Math.random() * usernames.length)];
  const token = data.tokens.get(username);
  
  // 测试API网关路由和负载均衡
  group('API网关路由测试', () => {
    // 测试用户服务路由
    const userRes = http.get(`${CONFIG.apiGateway}/users/me`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    check(userRes, {
      '用户服务路由成功': (r) => r.status === 200,
    });
    
    authFailRate.add(userRes.status !== 200);
    routingLatency.add(userRes.timings.waiting);
    requestsPerSecond.add(1);
    
    randomSleep(1, 3);
    
    // 测试项目服务路由
    const projectsRes = http.get(`${CONFIG.apiGateway}/projects`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    check(projectsRes, {
      '项目服务路由成功': (r) => r.status === 200,
    });
    
    routingLatency.add(projectsRes.timings.waiting);
    requestsPerSecond.add(1);
    
    randomSleep(1, 3);
    
    // 测试资产服务路由
    const assetsRes = http.get(`${CONFIG.apiGateway}/assets`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      params: {
        limit: 10,
      },
    });
    
    check(assetsRes, {
      '资产服务路由成功': (r) => r.status === 200,
    });
    
    routingLatency.add(assetsRes.timings.waiting);
    requestsPerSecond.add(1);
    
    randomSleep(1, 3);
  });
  
  // 测试API网关认证和授权
  group('API网关认证测试', () => {
    // 测试无效令牌
    const invalidTokenRes = http.get(`${CONFIG.apiGateway}/users/me`, {
      headers: {
        'Authorization': 'Bearer invalid-token',
        'Content-Type': 'application/json',
      },
    });
    
    check(invalidTokenRes, {
      '无效令牌被拒绝': (r) => r.status === 401,
    });
    
    authFailRate.add(invalidTokenRes.status !== 401);
    requestsPerSecond.add(1);
    
    randomSleep(1, 3);
    
    // 测试令牌刷新
    const refreshRes = http.post(`${CONFIG.apiGateway}/auth/refresh`, null, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    check(refreshRes, {
      '令牌刷新成功': (r) => r.status === 200,
    });
    
    if (refreshRes.status === 200) {
      const body = JSON.parse(refreshRes.body);
      data.tokens.set(username, body.accessToken);
    }
    
    authFailRate.add(refreshRes.status !== 200);
    requestsPerSecond.add(1);
    
    randomSleep(1, 3);
  });
  
  // 测试API网关限流
  group('API网关限流测试', () => {
    // 发送多个快速请求测试限流
    const requests = [];
    for (let i = 0; i < 10; i++) {
      requests.push({
        method: 'GET',
        url: `${CONFIG.apiGateway}/health`,
        params: {
          ts: Date.now() + i, // 添加时间戳防止缓存
        },
      });
    }
    
    const responses = http.batch(requests);
    
    let rateLimited = false;
    for (const res of responses) {
      if (res.status === 429) {
        rateLimited = true;
        break;
      }
      requestsPerSecond.add(1);
    }
    
    check(null, {
      '限流机制正常工作': () => rateLimited,
    });
    
    randomSleep(3, 5);
  });
}

// 清理函数 - 在测试结束后运行一次
export function teardown(data) {
  console.log('API网关性能测试完成');
}
