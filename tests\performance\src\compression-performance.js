/**
 * 压缩性能测试
 * 测试消息压缩服务的性能
 */
import { check } from 'k6';
import http from 'k6/http';
import { sleep } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';
import { CONFIG, randomSleep, getRandomItem } from './config.js';

// 性能指标
const compressionRatio = new Trend('compression_ratio');
const compressionTime = new Trend('compression_time');
const requestLatency = new Trend('request_latency');
const requestsPerSecond = new Rate('requests_per_second');
const errorRate = new Rate('error_rate');
const messageSize = new Trend('message_size');

// 测试数据
const testData = {
  messages: [],
};

// 初始化函数 - 在测试开始前运行一次
export function setup() {
  // 生成测试消息
  for (let i = 0; i < 10; i++) {
    // 小消息
    testData.messages.push({
      type: 'small',
      data: {
        id: `small-${i}`,
        value: 'small message',
      },
    });
    
    // 中等消息
    testData.messages.push({
      type: 'medium',
      data: {
        id: `medium-${i}`,
        value: 'medium message'.repeat(10),
        items: Array(10).fill(0).map((_, j) => ({ id: j, name: `item-${j}` })),
      },
    });
    
    // 大消息
    testData.messages.push({
      type: 'large',
      data: {
        id: `large-${i}`,
        value: 'large message'.repeat(100),
        items: Array(100).fill(0).map((_, j) => ({ id: j, name: `item-${j}`, description: `description-${j}`.repeat(10) })),
      },
    });
  }

  return testData;
}

// 默认函数 - 在测试期间为每个虚拟用户运行
export default function(data) {
  // 随机选择一个消息
  const message = getRandomItem(data.messages);
  
  // 发送请求，测试压缩性能
  const startTime = Date.now();
  
  const res = http.post(`${CONFIG.apiGateway}/api/compression-test`, JSON.stringify(message), {
    headers: {
      'Content-Type': 'application/json',
      'Accept-Encoding': 'gzip, deflate, br',
    },
  });
  
  const duration = Date.now() - startTime;
  
  // 记录请求延迟
  requestLatency.add(duration);
  
  // 记录请求成功/失败
  errorRate.add(res.status !== 200);
  
  // 记录每秒请求数
  requestsPerSecond.add(1);
  
  // 检查响应
  check(res, {
    '请求成功': (r) => r.status === 200,
  });
  
  // 记录压缩比率
  if (res.headers['X-Compression-Ratio']) {
    compressionRatio.add(parseFloat(res.headers['X-Compression-Ratio']));
  }
  
  // 记录压缩时间
  if (res.headers['X-Compression-Time']) {
    compressionTime.add(parseFloat(res.headers['X-Compression-Time']));
  }
  
  // 记录消息大小
  const originalSize = JSON.stringify(message).length;
  messageSize.add(originalSize);
  
  // 短暂休息
  randomSleep(0.1, 0.5);
}

// 测试配置
export const options = {
  scenarios: {
    // 小消息测试
    small_messages: {
      executor: 'constant-vus',
      vus: 10,
      duration: '30s',
      env: { MESSAGE_TYPE: 'small' },
    },
    // 中等消息测试
    medium_messages: {
      executor: 'constant-vus',
      vus: 10,
      duration: '30s',
      startTime: '30s',
      env: { MESSAGE_TYPE: 'medium' },
    },
    // 大消息测试
    large_messages: {
      executor: 'constant-vus',
      vus: 10,
      duration: '30s',
      startTime: '1m',
      env: { MESSAGE_TYPE: 'large' },
    },
    // 混合消息测试
    mixed_messages: {
      executor: 'constant-vus',
      vus: 30,
      duration: '1m',
      startTime: '1m30s',
    },
  },
  thresholds: {
    'request_latency': ['p(95)<200'], // 95%的请求延迟小于200ms
    'requests_per_second': ['avg>100'], // 平均每秒请求数大于100
    'error_rate': ['rate<0.01'], // 错误率小于1%
    'compression_ratio': ['avg<0.5'], // 平均压缩比率小于0.5
  },
};
