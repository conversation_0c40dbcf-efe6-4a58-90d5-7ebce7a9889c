/**
 * 网络多用户示例
 * 演示如何使用网络系统实现多用户交互
 */
import { Engine } from '../../engine/src/core/Engine';
import { Scene } from '../../engine/src/core/Scene';
import { Entity } from '../../engine/src/core/Entity';
import { NetworkSystem, NetworkState } from '../../engine/src/network/NetworkSystem';
import { NetworkEntityComponent } from '../../engine/src/network/components/NetworkEntityComponent';
import { NetworkTransformComponent } from '../../engine/src/network/components/NetworkTransformComponent';
import { NetworkUserComponent } from '../../engine/src/network/components/NetworkUserComponent';
import { MediaStreamType } from '../../engine/src/network/MediaStreamManager';
import { BandwidthControlStrategy, DataPriority } from '../../engine/src/network/BandwidthController';
import { UserRole, UserPermission } from '../../engine/src/network/UserSessionManager';
import { ServiceDiscoveryClient, ServiceInstance } from '../../engine/src/network/ServiceDiscoveryClient';
import { MicroserviceClient } from '../../engine/src/network/MicroserviceClient';
import { NetworkEntitySyncMode, NetworkQualityLevel } from '../../engine/src/network/types';
import { Debug } from '../../engine/src/utils/Debug';

/**
 * 网络多用户示例
 */
export class NetworkMultiUserExample {
  /** 引擎实例 */
  private engine: Engine;

  /** 场景 */
  private scene: Scene;

  /** 网络系统 */
  private networkSystem: NetworkSystem;

  /** 本地用户实体 */
  private localUserEntity: Entity | null = null;

  /** 远程用户实体映射表 */
  private remoteUserEntities: Map<string, Entity> = new Map();

  /** 网络实体映射表 */
  private networkEntities: Map<string, Entity> = new Map();

  /** 是否已连接 */
  private isConnected: boolean = false;

  /** 房间ID */
  private roomId: string = '';

  /** 用户名 */
  private username: string = '';

  /** 服务器URL */
  private serverUrl: string = 'wss://localhost:8080';

  /** 服务发现客户端 */
  private serviceDiscoveryClient: ServiceDiscoveryClient | null = null;

  /** 微服务客户端 */
  private microserviceClient: MicroserviceClient | null = null;

  /** 服务实例缓存 */
  private serviceInstances: Map<string, ServiceInstance[]> = new Map();

  /**
   * 创建网络多用户示例
   * @param engine 引擎实例
   */
  constructor(engine: Engine) {
    this.engine = engine;
    this.scene = engine.scene;

    // 创建服务发现客户端
    this.serviceDiscoveryClient = new ServiceDiscoveryClient({
      registryUrl: 'http://localhost:4010/api/registry',
      enableAutoHeartbeat: true,
      enableDiscoveryCache: true,
    });

    // 创建微服务客户端
    this.microserviceClient = new MicroserviceClient({
      serviceDiscoveryClient: this.serviceDiscoveryClient,
      apiGatewayUrl: 'http://localhost:3000/api',
      useApiGateway: true,
      useServiceDiscovery: true,
    });

    // 创建网络系统
    this.networkSystem = new NetworkSystem({
      autoConnect: false,
      serverUrl: this.serverUrl,
      enableWebRTC: true,
      enableMediaStream: true,
      enableAudio: true,
      enableVideo: false,
      enableNetworkQualityMonitor: true,
      enableBandwidthControl: true,
      bandwidthControlStrategy: BandwidthControlStrategy.ADAPTIVE,
      enableEntitySync: true,
      enableUserSessionManagement: true,
      defaultUserRole: UserRole.USER,
      enablePermissionCheck: true,
      enableEventBuffer: true,
      enableServiceDiscovery: true,
      serviceRegistryUrl: 'http://localhost:4010/api/registry',
      enableMicroserviceClient: true,
      apiGatewayUrl: 'http://localhost:3000/api',
      useApiGateway: true,
    });

    // 添加网络系统到引擎
    this.engine.addSystem(this.networkSystem);

    // 设置网络事件监听器
    this.setupNetworkEventListeners();

    // 创建UI
    this.createUI();
  }

  /**
   * 设置网络事件监听器
   */
  private setupNetworkEventListeners(): void {
    // 连接事件
    this.networkSystem.on('connected', () => {
      Debug.log('NetworkMultiUserExample', 'Connected to server');
      this.isConnected = true;
      this.updateUI();

      // 创建本地用户实体
      this.createLocalUserEntity();

      // 注册服务实例
      this.registerServiceInstance();

      // 发现服务实例
      this.discoverServices();
    });

    // 断开连接事件
    this.networkSystem.on('disconnected', () => {
      Debug.log('NetworkMultiUserExample', 'Disconnected from server');
      this.isConnected = false;
      this.updateUI();

      // 清理实体
      this.cleanupEntities();
    });

    // 连接错误事件
    this.networkSystem.on('error', (error) => {
      Debug.error('NetworkMultiUserExample', 'Connection error:', error);
      this.updateUI();
    });

    // 用户加入事件
    this.networkSystem.on('userJoined', (userId, username, userEntity) => {
      Debug.log('NetworkMultiUserExample', `User joined: ${username} (${userId})`);

      // 添加到远程用户实体映射表
      this.remoteUserEntities.set(userId, userEntity);

      // 创建远程用户的3D表示
      this.createRemoteUserRepresentation(userId, username, userEntity);

      // 更新UI
      this.updateUI();
    });

    // 用户离开事件
    this.networkSystem.on('userLeft', (userId) => {
      Debug.log('NetworkMultiUserExample', `User left: ${userId}`);

      // 从远程用户实体映射表中移除
      this.remoteUserEntities.delete(userId);

      // 更新UI
      this.updateUI();
    });

    // 实体创建事件
    this.networkSystem.on('entityCreated', (entityId, entity) => {
      Debug.log('NetworkMultiUserExample', `Entity created: ${entityId}`);

      // 添加到网络实体映射表
      this.networkEntities.set(entityId, entity);
    });

    // 实体更新事件
    this.networkSystem.on('entityUpdated', (entityId, entity) => {
      // 实体已更新
    });

    // 实体删除事件
    this.networkSystem.on('entityDeleted', (entityId) => {
      Debug.log('NetworkMultiUserExample', `Entity deleted: ${entityId}`);

      // 从网络实体映射表中移除
      this.networkEntities.delete(entityId);
    });

    // WebRTC连接事件
    this.networkSystem.on('webrtcConnected', (userId) => {
      Debug.log('NetworkMultiUserExample', `WebRTC connected with user: ${userId}`);

      // 如果启用了音频，则获取本地音频流
      if (this.networkSystem.options.enableAudio) {
        this.setupAudioStream(userId);
      }
    });

    // 媒体流添加事件
    this.networkSystem.on('streamAdded', (userId, stream) => {
      Debug.log('NetworkMultiUserExample', `Stream added from user: ${userId}`);

      // 处理远程媒体流
      this.handleRemoteStream(userId, stream);
    });

    // 网络质量更新事件
    this.networkSystem.on('networkQualityUpdate', (quality) => {
      // 更新网络质量UI
      this.updateNetworkQualityUI(quality);
    });
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 在实际项目中，这里应该创建UI元素
    // 例如连接按钮、用户列表、聊天框等

    // 这里只是模拟UI创建
    Debug.log('NetworkMultiUserExample', 'Creating UI');

    // 模拟UI事件
    // 连接按钮点击
    // this.onConnectButtonClick();

    // 断开连接按钮点击
    // this.onDisconnectButtonClick();

    // 发送消息按钮点击
    // this.onSendMessageButtonClick('Hello, world!');

    // 创建实体按钮点击
    // this.onCreateEntityButtonClick();

    // 服务发现按钮点击
    // this.onDiscoverServicesButtonClick();

    // 服务注册按钮点击
    // this.onRegisterServiceButtonClick();

    // 微服务请求按钮点击
    // this.onRequestServiceButtonClick();
  }

  /**
   * 更新UI
   */
  private updateUI(): void {
    // 在实际项目中，这里应该更新UI元素
    // 例如连接状态、用户列表等

    // 这里只是模拟UI更新
    Debug.log('NetworkMultiUserExample', `Updating UI, connected: ${this.isConnected}`);

    // 更新连接状态
    const connectionStatus = this.isConnected ? 'Connected' : 'Disconnected';
    Debug.log('NetworkMultiUserExample', `Connection status: ${connectionStatus}`);

    // 更新用户列表
    const userCount = this.remoteUserEntities.size;
    Debug.log('NetworkMultiUserExample', `Users online: ${userCount}`);

    // 更新实体列表
    const entityCount = this.networkEntities.size;
    Debug.log('NetworkMultiUserExample', `Network entities: ${entityCount}`);

    // 更新服务实例列表
    let serviceCount = 0;
    for (const instances of this.serviceInstances.values()) {
      serviceCount += instances.length;
    }
    Debug.log('NetworkMultiUserExample', `Service instances: ${serviceCount}`);
  }

  /**
   * 更新网络质量UI
   * @param quality 网络质量数据
   */
  private updateNetworkQualityUI(quality: any): void {
    // 在实际项目中，这里应该更新网络质量UI元素
    // 例如RTT、丢包率、带宽等

    // 这里只是模拟UI更新
    Debug.log('NetworkMultiUserExample', `Network quality: ${quality.level}`);
    Debug.log('NetworkMultiUserExample', `RTT: ${quality.rtt}ms, Packet loss: ${quality.packetLoss * 100}%`);
  }

  /**
   * 连接到服务器
   * @param serverUrl 服务器URL
   * @param roomId 房间ID
   * @param username 用户名
   */
  public connect(serverUrl: string = this.serverUrl, roomId: string = 'default', username: string = 'User'): void {
    if (this.isConnected) {
      Debug.warn('NetworkMultiUserExample', 'Already connected');
      return;
    }

    this.serverUrl = serverUrl;
    this.roomId = roomId;
    this.username = username;

    // 设置用户名
    this.networkSystem.options.username = username;

    // 连接到服务器
    this.networkSystem.connect(serverUrl, roomId);
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    if (!this.isConnected) {
      Debug.warn('NetworkMultiUserExample', 'Not connected');
      return;
    }

    // 断开连接
    this.networkSystem.disconnect();
  }

  /**
   * 创建本地用户实体
   */
  private createLocalUserEntity(): void {
    // 创建本地用户实体
    const entity = this.engine.createEntity();

    // 添加变换组件
    const transform = entity.addComponent('Transform');
    transform.position.set(0, 1.7, 0);

    // 添加网络用户组件
    const networkUser = entity.addComponent(NetworkUserComponent, {
      userId: this.networkSystem.getLocalUserId()!,
      username: this.username,
      isLocal: true,
    });

    // 添加网络实体组件
    const networkEntity = entity.addComponent(NetworkEntityComponent, {
      entityId: `user_${this.networkSystem.getLocalUserId()}`,
      ownerId: this.networkSystem.getLocalUserId()!,
    });

    // 添加网络变换组件
    const networkTransform = entity.addComponent(NetworkTransformComponent, {
      syncPosition: true,
      syncRotation: true,
      syncScale: false,
      interpolate: true,
    });

    // 添加模型组件（在实际项目中，这里应该添加用户的3D模型）
    // const model = entity.addComponent('Model', {
    //   url: 'models/avatar.glb',
    // });

    // 保存本地用户实体
    this.localUserEntity = entity;

    // 添加到网络系统
    this.networkSystem.addNetworkEntity(networkEntity.entityId, entity);
  }

  /**
   * 创建远程用户的3D表示
   * @param userId 用户ID
   * @param username 用户名
   * @param userEntity 用户实体
   */
  private createRemoteUserRepresentation(userId: string, username: string, userEntity: Entity): void {
    // 在实际项目中，这里应该创建远程用户的3D表示
    // 例如加载用户的3D模型、设置位置等

    // 这里只是模拟创建远程用户的3D表示
    Debug.log('NetworkMultiUserExample', `Creating 3D representation for user: ${username}`);

    // 添加变换组件
    const transform = userEntity.getComponent('Transform') || userEntity.addComponent('Transform');
    transform.position.set(Math.random() * 5 - 2.5, 1.7, Math.random() * 5 - 2.5);

    // 添加网络变换组件
    const networkTransform = userEntity.getComponent(NetworkTransformComponent) || userEntity.addComponent(NetworkTransformComponent, {
      syncPosition: true,
      syncRotation: true,
      syncScale: false,
      interpolate: true,
    });

    // 添加模型组件（在实际项目中，这里应该添加用户的3D模型）
    // const model = userEntity.addComponent('Model', {
    //   url: 'models/avatar.glb',
    // });
  }

  /**
   * 设置音频流
   * @param userId 用户ID
   */
  private async setupAudioStream(userId: string): Promise<void> {
    try {
      // 获取本地音频流
      const streamInfo = await this.networkSystem.getLocalMediaStream(MediaStreamType.AUDIO, {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      });

      if (!streamInfo) {
        Debug.warn('NetworkMultiUserExample', 'Failed to get local audio stream');
        return;
      }

      Debug.log('NetworkMultiUserExample', `Got local audio stream: ${streamInfo.id}`);

      // 获取WebRTC连接
      const connection = this.networkSystem.createWebRTCConnection(userId);

      if (!connection) {
        Debug.warn('NetworkMultiUserExample', `No WebRTC connection for user: ${userId}`);
        return;
      }

      // 添加音频轨道到连接
      if (streamInfo.audioTrack) {
        connection.addTrack(streamInfo.audioTrack, streamInfo.stream);
        Debug.log('NetworkMultiUserExample', `Added audio track to connection with user: ${userId}`);
      }
    } catch (error) {
      Debug.error('NetworkMultiUserExample', 'Error setting up audio stream:', error);
    }
  }

  /**
   * 处理远程媒体流
   * @param userId 用户ID
   * @param stream 媒体流
   */
  private handleRemoteStream(userId: string, stream: MediaStream): void {
    // 添加远程媒体流
    const streamInfo = this.networkSystem.addRemoteMediaStream(stream, userId, MediaStreamType.AUDIO);

    if (!streamInfo) {
      Debug.warn('NetworkMultiUserExample', `Failed to add remote stream for user: ${userId}`);
      return;
    }

    Debug.log('NetworkMultiUserExample', `Added remote stream: ${streamInfo.id} from user: ${userId}`);

    // 在实际项目中，这里应该创建音频元素并播放远程音频流
    // 例如：
    // const audioElement = document.createElement('audio');
    // audioElement.srcObject = stream;
    // audioElement.autoplay = true;
    // document.body.appendChild(audioElement);
  }

  /**
   * 发送消息
   * @param message 消息内容
   * @param targetUserId 目标用户ID（如果不指定，则发送给所有用户）
   */
  public sendMessage(message: string, targetUserId?: string): void {
    if (!this.isConnected) {
      Debug.warn('NetworkMultiUserExample', 'Not connected');
      return;
    }

    const messageData = {
      text: message,
      timestamp: Date.now(),
      sender: this.username,
    };

    if (targetUserId) {
      // 发送私聊消息
      this.networkSystem.sendToUser(targetUserId, 'chat_message', messageData);
      Debug.log('NetworkMultiUserExample', `Sent private message to ${targetUserId}: ${message}`);
    } else {
      // 发送广播消息
      this.networkSystem.sendToAll('chat_message', messageData);
      Debug.log('NetworkMultiUserExample', `Sent broadcast message: ${message}`);
    }
  }

  /**
   * 创建网络实体
   * @param position 位置
   * @param type 实体类型
   * @returns 实体
   */
  public createNetworkEntity(position: { x: number; y: number; z: number }, type: string = 'default'): Entity {
    if (!this.isConnected) {
      Debug.warn('NetworkMultiUserExample', 'Not connected');
      throw new Error('Not connected');
    }

    // 创建实体
    const entity = this.engine.createEntity();

    // 添加变换组件
    const transform = entity.addComponent('Transform');
    transform.position.set(position.x, position.y, position.z);

    // 生成实体ID
    const entityId = `entity_${Math.random().toString(36).substring(2, 9)}`;

    // 添加网络实体组件
    const networkEntity = entity.addComponent(NetworkEntityComponent, {
      entityId,
      ownerId: this.networkSystem.getLocalUserId()!,
      type,
    });

    // 添加网络变换组件
    const networkTransform = entity.addComponent(NetworkTransformComponent, {
      syncPosition: true,
      syncRotation: true,
      syncScale: true,
      interpolate: true,
    });

    // 根据类型添加不同的组件
    switch (type) {
      case 'cube':
        // 添加立方体组件（在实际项目中，这里应该添加立方体的3D模型）
        // const cube = entity.addComponent('Cube', {
        //   width: 1,
        //   height: 1,
        //   depth: 1,
        // });
        break;

      case 'sphere':
        // 添加球体组件（在实际项目中，这里应该添加球体的3D模型）
        // const sphere = entity.addComponent('Sphere', {
        //   radius: 0.5,
        // });
        break;

      default:
        // 添加默认组件（在实际项目中，这里应该添加默认的3D模型）
        // const model = entity.addComponent('Model', {
        //   url: 'models/default.glb',
        // });
        break;
    }

    // 添加到网络系统
    this.networkSystem.addNetworkEntity(entityId, entity);

    // 添加到网络实体映射表
    this.networkEntities.set(entityId, entity);

    return entity;
  }

  /**
   * 清理实体
   */
  private cleanupEntities(): void {
    // 清理本地用户实体
    if (this.localUserEntity) {
      this.engine.destroyEntity(this.localUserEntity);
      this.localUserEntity = null;
    }

    // 清理远程用户实体
    for (const entity of this.remoteUserEntities.values()) {
      this.engine.destroyEntity(entity);
    }
    this.remoteUserEntities.clear();

    // 清理网络实体
    for (const entity of this.networkEntities.values()) {
      this.engine.destroyEntity(entity);
    }
    this.networkEntities.clear();
  }

  /**
   * 注册服务实例
   */
  private async registerServiceInstance(): Promise<void> {
    if (!this.isConnected) {
      Debug.warn('NetworkMultiUserExample', 'Not connected');
      return;
    }

    try {
      // 注册客户端服务实例
      const instance = await this.networkSystem.registerService(
        'client',
        window.location.hostname,
        parseInt(window.location.port || '80'),
        window.location.protocol === 'https:',
        {
          userId: this.networkSystem.getLocalUserId(),
          username: this.username,
          roomId: this.roomId,
          clientInfo: {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
          },
        }
      );

      Debug.log('NetworkMultiUserExample', `Registered service instance: ${instance.instanceId}`);
    } catch (error) {
      Debug.error('NetworkMultiUserExample', 'Failed to register service instance:', error);
    }
  }

  /**
   * 发现服务
   */
  private async discoverServices(): Promise<void> {
    if (!this.isConnected) {
      Debug.warn('NetworkMultiUserExample', 'Not connected');
      return;
    }

    try {
      // 发现服务实例
      const services = ['user-service', 'project-service', 'asset-service', 'render-service'];

      for (const serviceName of services) {
        const instances = await this.networkSystem.discoverService(serviceName);

        // 更新服务实例缓存
        this.serviceInstances.set(serviceName, instances);

        Debug.log('NetworkMultiUserExample', `Discovered ${instances.length} instances of service: ${serviceName}`);

        // 打印服务实例信息
        for (const instance of instances) {
          Debug.log('NetworkMultiUserExample', `  - ${instance.instanceId}: ${instance.host}:${instance.port} (${instance.status})`);
        }
      }

      // 更新UI
      this.updateUI();
    } catch (error) {
      Debug.error('NetworkMultiUserExample', 'Failed to discover services:', error);
    }
  }

  /**
   * 发送请求到微服务
   * @param serviceName 服务名称
   * @param endpoint 端点
   * @param data 请求数据
   * @returns 响应数据
   */
  public async requestService<T = any>(serviceName: string, endpoint: string, data: any = {}): Promise<T> {
    if (!this.isConnected) {
      Debug.warn('NetworkMultiUserExample', 'Not connected');
      throw new Error('Not connected');
    }

    try {
      // 发送请求到微服务
      const response = await this.networkSystem.requestService<T>(serviceName, endpoint, {
        method: 'POST',
        body: data,
      });

      Debug.log('NetworkMultiUserExample', `Received response from ${serviceName}/${endpoint}:`, response);

      return response;
    } catch (error) {
      Debug.error('NetworkMultiUserExample', `Failed to request ${serviceName}/${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * 销毁示例
   */
  public dispose(): void {
    // 断开连接
    if (this.isConnected) {
      this.disconnect();
    }

    // 清理实体
    this.cleanupEntities();

    // 移除网络系统
    this.engine.removeSystem(this.networkSystem);
  }
}
