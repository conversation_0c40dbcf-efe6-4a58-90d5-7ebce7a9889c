# 服务注册中心监控和告警指南

本文档提供了服务注册中心的监控和告警指南，帮助您了解如何监控服务注册中心的运行状态，以及如何配置和处理告警。

## 监控概述

服务注册中心提供了全面的监控功能，包括：

1. **系统指标监控**：CPU使用率、内存使用率、负载等系统级指标
2. **服务指标监控**：服务数量、实例数量、健康状态等服务级指标
3. **缓存指标监控**：缓存命中率、缓存大小、内存使用等缓存级指标
4. **负载均衡指标监控**：请求数、错误率、响应时间等负载均衡级指标

这些监控指标可以通过API获取，也可以集成到Prometheus等监控系统中。

## 监控API

### 获取监控指标

```http
GET /monitoring/metrics?limit=10
```

**参数**:

- `limit`：返回的指标数量，默认为全部

**响应**:

```json
[
  {
    "timestamp": 1683025200000,
    "cpuUsage": 0.25,
    "memoryUsage": 0.4,
    "totalMemory": **********,
    "freeMemory": **********,
    "uptime": 3600,
    "loadAverage": [0.5, 0.3, 0.2],
    "serviceCount": 5,
    "instanceCount": 10,
    "healthyInstanceCount": 9,
    "unhealthyInstanceCount": 1,
    "cacheStats": {
      "hits": 1000,
      "misses": 200,
      "hitRate": 0.83,
      "size": 50,
      "memoryUsage": 1024000
    },
    "loadBalancerStats": {
      "requestCount": 5000,
      "errorCount": 10,
      "avgResponseTime": 5.2
    }
  }
]
```

### 获取最新监控指标

```http
GET /monitoring/metrics/latest
```

**响应**:

与获取监控指标相同，但只返回最新的一条指标。

### 获取健康状态

```http
GET /monitoring/health
```

**响应**:

```json
{
  "status": "UP",
  "details": {
    "cpu": { "status": "UP", "usage": 0.25 },
    "memory": { "status": "UP", "usage": 0.4 },
    "services": { "status": "UP", "count": 5, "healthy": 9, "unhealthy": 1 },
    "cache": { "status": "UP", "hitRate": 0.83, "size": 50 }
  }
}
```

## 告警机制

服务注册中心提供了自动告警功能，当监控指标超过阈值时，会生成告警。告警可以通过API查询，也可以配置通知方式（如邮件、Slack等）。

### 告警类型

服务注册中心支持以下类型的告警：

1. **CPU告警**：当CPU使用率超过阈值时触发
2. **内存告警**：当内存使用率超过阈值时触发
3. **服务告警**：当不健康实例比例超过阈值时触发
4. **缓存告警**：当缓存命中率低于阈值时触发
5. **负载均衡告警**：当负载均衡错误率超过阈值时触发

### 告警级别

告警分为以下几个级别：

1. **信息（info）**：提供信息性质的告警，不需要立即处理
2. **警告（warning）**：表示潜在问题，建议关注
3. **错误（error）**：表示严重问题，需要及时处理
4. **严重（critical）**：表示非常严重的问题，需要立即处理

### 告警API

#### 获取告警

```http
GET /monitoring/alerts?onlyUnresolved=true
```

**参数**:

- `onlyUnresolved`：是否只返回未解决的告警，默认为false

**响应**:

```json
[
  {
    "id": "cpu-1683025200000",
    "type": "cpu",
    "severity": "warning",
    "message": "CPU使用率过高: 85.5%",
    "timestamp": 1683025200000,
    "value": 0.855,
    "threshold": 0.8,
    "resolved": false
  }
]
```

#### 获取特定告警

```http
GET /monitoring/alerts/{alertId}
```

**响应**:

```json
{
  "id": "cpu-1683025200000",
  "type": "cpu",
  "severity": "warning",
  "message": "CPU使用率过高: 85.5%",
  "timestamp": 1683025200000,
  "value": 0.855,
  "threshold": 0.8,
  "resolved": false
}
```

#### 手动解决告警

```http
POST /monitoring/alerts/{alertId}/resolve
```

**响应**:

```json
{
  "success": true,
  "message": "告警已手动解决"
}
```

## 配置告警阈值

告警阈值可以通过环境变量配置：

```env
# 监控配置
MONITORING_MAX_METRICS_HISTORY=1440
MONITORING_MAX_ALERTS_HISTORY=1000
MONITORING_CPU_THRESHOLD=0.8
MONITORING_MEMORY_THRESHOLD=0.8
MONITORING_UNHEALTHY_INSTANCE_THRESHOLD=0.2
MONITORING_CACHE_HIT_RATE_THRESHOLD=0.5
MONITORING_LOAD_BALANCER_ERROR_RATE_THRESHOLD=0.05
```

## 告警通知

服务注册中心支持多种告警通知方式，可以通过配置启用：

### 邮件通知

```env
# 邮件通知配置
ALERT_NOTIFICATION_EMAIL_ENABLED=true
ALERT_NOTIFICATION_EMAIL_HOST=smtp.example.com
ALERT_NOTIFICATION_EMAIL_PORT=587
ALERT_NOTIFICATION_EMAIL_USER=<EMAIL>
ALERT_NOTIFICATION_EMAIL_PASSWORD=password
ALERT_NOTIFICATION_EMAIL_FROM=<EMAIL>
ALERT_NOTIFICATION_EMAIL_TO=<EMAIL>
```

### Slack通知

```env
# Slack通知配置
ALERT_NOTIFICATION_SLACK_ENABLED=true
ALERT_NOTIFICATION_SLACK_WEBHOOK_URL=https://hooks.slack.com/services/xxx/yyy/zzz
ALERT_NOTIFICATION_SLACK_CHANNEL=#alerts
```

### 钉钉通知

```env
# 钉钉通知配置
ALERT_NOTIFICATION_DINGTALK_ENABLED=true
ALERT_NOTIFICATION_DINGTALK_TOKEN=xxx
ALERT_NOTIFICATION_DINGTALK_SECRET=yyy
```

## 集成Prometheus

服务注册中心支持集成Prometheus，可以通过以下步骤配置：

1. 启用Prometheus导出器：

```env
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
```

2. 配置Prometheus抓取目标：

```yaml
scrape_configs:
  - job_name: 'service-registry'
    scrape_interval: 15s
    static_configs:
      - targets: ['localhost:9090']
```

3. 导入Grafana仪表板：

服务注册中心提供了预配置的Grafana仪表板，可以从`/monitoring/grafana-dashboard.json`下载。

## 监控最佳实践

### 系统监控

1. **设置合理的告警阈值**：根据系统实际情况设置告警阈值，避免过多的误报
2. **配置告警通知**：配置多种告警通知方式，确保及时收到告警
3. **定期检查监控指标**：定期检查监控指标，了解系统运行状态
4. **关注趋势变化**：关注指标的趋势变化，及时发现潜在问题

### 服务监控

1. **关注不健康实例**：及时处理不健康的服务实例
2. **监控服务注册和注销**：关注服务注册和注销的频率，异常的频率可能表示问题
3. **检查服务依赖**：了解服务之间的依赖关系，关注关键服务的健康状态

### 缓存监控

1. **关注缓存命中率**：低缓存命中率可能表示缓存配置不合理
2. **监控缓存大小**：过大的缓存可能导致内存压力
3. **定期清理缓存**：定期清理不常用的缓存，避免缓存过大

### 负载均衡监控

1. **关注请求分布**：确保请求均匀分布到各个实例
2. **监控响应时间**：关注响应时间的变化，及时发现性能问题
3. **检查错误率**：高错误率可能表示服务实例有问题

## 故障排除

### 常见告警及处理方法

#### CPU使用率过高

**可能原因**：
- 请求量过大
- 后台任务过多
- 代码效率问题

**处理方法**：
- 增加服务实例
- 优化代码
- 调整后台任务调度

#### 内存使用率过高

**可能原因**：
- 内存泄漏
- 缓存过大
- 大量并发请求

**处理方法**：
- 检查内存泄漏
- 调整缓存大小
- 增加服务实例

#### 不健康实例比例过高

**可能原因**：
- 服务实例崩溃
- 网络问题
- 健康检查配置不合理

**处理方法**：
- 检查服务实例日志
- 检查网络连接
- 调整健康检查配置

#### 缓存命中率过低

**可能原因**：
- 缓存TTL过短
- 缓存键生成不合理
- 数据变化频繁

**处理方法**：
- 调整缓存TTL
- 优化缓存键生成
- 调整缓存策略

#### 负载均衡错误率过高

**可能原因**：
- 服务实例异常
- 负载均衡策略不合理
- 网络问题

**处理方法**：
- 检查服务实例
- 调整负载均衡策略
- 检查网络连接
