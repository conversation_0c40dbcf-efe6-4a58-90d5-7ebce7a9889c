/**
 * 场景实体对象
 */
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Scene } from './scene.entity';

@Entity('scene_entities')
export class SceneEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  type: string;

  @Column({ type: 'json' })
  transform: {
    position: [number, number, number];
    rotation: [number, number, number];
    scale: [number, number, number];
  };

  @Column({ type: 'json', nullable: true })
  components: Record<string, any>;

  @Column({ nullable: true })
  parentId: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @ManyToOne(() => Scene, scene => scene.entities, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sceneId' })
  scene: Scene;

  @Column()
  sceneId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
