/**
 * GPU性能分析器
 * 用于分析GPU性能指标和瓶颈
 */
import * as THREE from 'three';
import { Debug } from './Debug';
import { EventEmitter } from './EventEmitter';
import { PerformanceMonitor, PerformanceMetricType } from './PerformanceMonitor';

/**
 * GPU性能分析器事件类型
 */
export enum GPUPerformanceAnalyzerEventType {
  /** 分析开始 */
  ANALYSIS_STARTED = 'analysis_started',
  /** 分析完成 */
  ANALYSIS_COMPLETED = 'analysis_completed',
  /** 分析错误 */
  ANALYSIS_ERROR = 'analysis_error',
  /** 瓶颈检测 */
  BOTTLENECK_DETECTED = 'bottleneck_detected',
  /** 优化建议 */
  OPTIMIZATION_SUGGESTION = 'optimization_suggestion'
}

/**
 * GPU性能分析器配置
 */
export interface GPUPerformanceAnalyzerConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 采样间隔（毫秒） */
  sampleInterval?: number;
  /** 历史记录长度 */
  historyLength?: number;
  /** 是否自动采样 */
  autoSample?: boolean;
  /** 是否检测瓶颈 */
  detectBottlenecks?: boolean;
  /** 是否生成优化建议 */
  generateSuggestions?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * GPU性能指标
 */
export interface GPUPerformanceMetrics {
  /** 帧率 */
  fps: number;
  /** 绘制调用次数 */
  drawCalls: number;
  /** 三角形数量 */
  triangles: number;
  /** 点数量 */
  points: number;
  /** 线段数量 */
  lines: number;
  /** 几何体数量 */
  geometries: number;
  /** 纹理数量 */
  textures: number;
  /** 着色器程序数量 */
  programs: number;
  /** 渲染目标数量 */
  renderTargets: number;
  /** 渲染时间（毫秒） */
  renderTime: number;
  /** 后处理时间（毫秒） */
  postProcessingTime: number;
  /** 内存使用量（MB） */
  memoryUsage: number;
  /** 纹理内存（MB） */
  textureMemory: number;
  /** 几何体内存（MB） */
  geometryMemory: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 瓶颈类型
 */
export enum BottleneckType {
  /** CPU瓶颈 */
  CPU = 'cpu',
  /** GPU瓶颈 */
  GPU = 'gpu',
  /** 内存瓶颈 */
  MEMORY = 'memory',
  /** 绘制调用瓶颈 */
  DRAW_CALLS = 'draw_calls',
  /** 几何体瓶颈 */
  GEOMETRY = 'geometry',
  /** 纹理瓶颈 */
  TEXTURE = 'texture',
  /** 着色器瓶颈 */
  SHADER = 'shader',
  /** 后处理瓶颈 */
  POST_PROCESSING = 'post_processing'
}

/**
 * 瓶颈信息
 */
export interface BottleneckInfo {
  /** 瓶颈类型 */
  type: BottleneckType;
  /** 严重程度（0-1） */
  severity: number;
  /** 描述 */
  description: string;
  /** 相关指标 */
  metrics: Record<string, number>;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 优化建议
 */
export interface OptimizationSuggestion {
  /** 建议ID */
  id: string;
  /** 标题 */
  title: string;
  /** 描述 */
  description: string;
  /** 优先级（0-1） */
  priority: number;
  /** 难度（0-1） */
  difficulty: number;
  /** 预期改进（0-1） */
  expectedImprovement: number;
  /** 相关瓶颈类型 */
  bottleneckType: BottleneckType;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 分析报告
 */
export interface AnalysisReport {
  /** 当前指标 */
  currentMetrics: GPUPerformanceMetrics;
  /** 平均指标 */
  averageMetrics: GPUPerformanceMetrics;
  /** 最小指标 */
  minMetrics: GPUPerformanceMetrics;
  /** 最大指标 */
  maxMetrics: GPUPerformanceMetrics;
  /** 指标历史 */
  metricsHistory: GPUPerformanceMetrics[];
  /** 检测到的瓶颈 */
  detectedBottlenecks: BottleneckInfo[];
  /** 优化建议 */
  optimizationSuggestions: OptimizationSuggestion[];
  /** 性能评分（0-100） */
  performanceScore: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * GPU性能分析器
 */
export class GPUPerformanceAnalyzer {
  /** 单例实例 */
  private static instance: GPUPerformanceAnalyzer;

  /** 配置 */
  private config: GPUPerformanceAnalyzerConfig;
  /** 是否运行中 */
  private running: boolean;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor;
  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 指标历史 */
  private metricsHistory: GPUPerformanceMetrics[];
  /** 检测到的瓶颈 */
  private detectedBottlenecks: BottleneckInfo[];
  /** 优化建议 */
  private optimizationSuggestions: OptimizationSuggestion[];
  /** 采样定时器ID */
  private sampleTimerId: number | null;
  /** 上次采样时间 */
  private lastSampleTime: number;
  /** 性能评分 */
  private performanceScore: number;

  /**
   * 获取单例实例
   * @returns GPU性能分析器实例
   */
  public static getInstance(): GPUPerformanceAnalyzer {
    if (!GPUPerformanceAnalyzer.instance) {
      GPUPerformanceAnalyzer.instance = new GPUPerformanceAnalyzer();
    }
    return GPUPerformanceAnalyzer.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    // 默认配置
    this.config = {
      enabled: true,
      sampleInterval: 1000,
      historyLength: 60,
      autoSample: true,
      detectBottlenecks: true,
      generateSuggestions: true,
      debug: false
    };

    this.running = false;
    this.renderer = null;
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.eventEmitter = new EventEmitter();
    this.metricsHistory = [];
    this.detectedBottlenecks = [];
    this.optimizationSuggestions = [];
    this.sampleTimerId = null;
    this.lastSampleTime = 0;
    this.performanceScore = 100;
  }

  /**
   * 配置分析器
   * @param config 配置
   */
  public configure(config: GPUPerformanceAnalyzerConfig): void {
    this.config = {
      ...this.config,
      ...config
    };

    if (this.config.debug) {
      Debug.log('GPUPerformanceAnalyzer', '配置已更新', this.config);
    }
  }

  /**
   * 设置渲染器
   * @param renderer 渲染器
   */
  public setRenderer(renderer: THREE.WebGLRenderer): void {
    this.renderer = renderer;

    if (this.config.debug) {
      Debug.log('GPUPerformanceAnalyzer', '渲染器已设置');
    }
  }

  /**
   * 启动分析
   */
  public start(): void {
    if (!this.config.enabled || this.running) {
      return;
    }

    this.running = true;
    this.lastSampleTime = performance.now();

    // 发出分析开始事件
    this.eventEmitter.emit(GPUPerformanceAnalyzerEventType.ANALYSIS_STARTED);

    // 启动自动采样
    if (this.config.autoSample) {
      this.startAutoSampling();
    }

    if (this.config.debug) {
      Debug.log('GPUPerformanceAnalyzer', '分析已启动');
    }
  }

  /**
   * 停止分析
   */
  public stop(): void {
    if (!this.running) {
      return;
    }

    this.running = false;

    // 停止自动采样
    this.stopAutoSampling();

    if (this.config.debug) {
      Debug.log('GPUPerformanceAnalyzer', '分析已停止');
    }
  }

  /**
   * 启动自动采样
   */
  private startAutoSampling(): void {
    // 停止现有定时器
    this.stopAutoSampling();

    // 创建新定时器
    this.sampleTimerId = window.setInterval(() => {
      this.sample();
    }, this.config.sampleInterval);

    if (this.config.debug) {
      Debug.log('GPUPerformanceAnalyzer', `自动采样已启动，间隔: ${this.config.sampleInterval}ms`);
    }
  }

  /**
   * 停止自动采样
   */
  private stopAutoSampling(): void {
    if (this.sampleTimerId !== null) {
      window.clearInterval(this.sampleTimerId);
      this.sampleTimerId = null;
    }
  }

  /**
   * 采样
   */
  public sample(): void {
    if (!this.running || !this.renderer) {
      return;
    }

    try {
      // 获取当前时间
      const now = performance.now();

      // 计算采样间隔（用于调试）
      const sampleInterval = this.lastSampleTime > 0 ? now - this.lastSampleTime : 0;

      // 获取渲染器信息
      const info = this.renderer.info;

      // 获取性能监控器指标
      const fpsMetric = this.performanceMonitor.getMetric(PerformanceMetricType.FPS);
      const renderTimeMetric = this.performanceMonitor.getMetric(PerformanceMetricType.RENDER_TIME);
      const postProcessingTimeMetric = this.performanceMonitor.getMetric(PerformanceMetricType.POST_PROCESS_TIME);
      const memoryUsageMetric = this.performanceMonitor.getMetric(PerformanceMetricType.MEMORY_USAGE);

      const fps = fpsMetric?.value || 0;
      const renderTime = renderTimeMetric?.value || 0;
      const postProcessingTime = postProcessingTimeMetric?.value || 0;
      const memoryUsage = memoryUsageMetric?.value || 0;

      // 创建指标对象
      const metrics: GPUPerformanceMetrics = {
        fps,
        drawCalls: info.render.calls,
        triangles: info.render.triangles,
        points: info.render.points,
        lines: info.render.lines,
        geometries: info.memory.geometries,
        textures: info.memory.textures,
        programs: info.programs?.length || 0,
        renderTargets: 0, // THREE.js info.render 不包含 renderTargets 属性
        renderTime,
        postProcessingTime,
        memoryUsage,
        textureMemory: this.estimateTextureMemory(),
        geometryMemory: this.estimateGeometryMemory(),
        timestamp: now
      };

      // 添加到历史
      this.metricsHistory.push(metrics);

      // 限制历史长度
      if (this.metricsHistory.length > this.config.historyLength!) {
        this.metricsHistory.shift();
      }

      // 检测瓶颈
      if (this.config.detectBottlenecks) {
        this.detectBottlenecks(metrics);
      }

      // 生成优化建议
      if (this.config.generateSuggestions) {
        this.generateOptimizationSuggestions();
      }

      // 计算性能评分
      this.calculatePerformanceScore();

      // 更新上次采样时间
      this.lastSampleTime = now;

      if (this.config.debug) {
        Debug.log('GPUPerformanceAnalyzer', '采样完成', {
          metrics,
          sampleInterval: sampleInterval.toFixed(2) + 'ms'
        });
      }
    } catch (error) {
      // 发出分析错误事件
      this.eventEmitter.emit(GPUPerformanceAnalyzerEventType.ANALYSIS_ERROR, error);
      Debug.error('GPUPerformanceAnalyzer', '采样失败:', error);
    }
  }

  /**
   * 估算纹理内存
   * @returns 纹理内存（MB）
   */
  private estimateTextureMemory(): number {
    if (!this.renderer) {
      return 0;
    }

    const info = this.renderer.info;
    const textureCount = info.memory.textures;

    // 假设每个纹理平均大小为2MB
    const averageTextureSize = 2;

    return textureCount * averageTextureSize;
  }

  /**
   * 估算几何体内存
   * @returns 几何体内存（MB）
   */
  private estimateGeometryMemory(): number {
    if (!this.renderer) {
      return 0;
    }

    const info = this.renderer.info;
    const triangleCount = info.render.triangles;

    // 假设每个三角形平均大小为50字节
    const averageTriangleSize = 50 / (1024 * 1024);

    return triangleCount * averageTriangleSize;
  }

  /**
   * 检测瓶颈
   * @param metrics 性能指标
   */
  private detectBottlenecks(metrics: GPUPerformanceMetrics): void {
    // 清除旧的瓶颈
    this.detectedBottlenecks = [];

    // 检测FPS瓶颈
    if (metrics.fps < 30) {
      const severity = 1 - (metrics.fps / 60);
      this.addBottleneck(BottleneckType.CPU, severity, '帧率过低，可能存在CPU瓶颈', {
        fps: metrics.fps,
        renderTime: metrics.renderTime
      });
    }

    // 检测绘制调用瓶颈
    if (metrics.drawCalls > 1000) {
      const severity = Math.min(1, (metrics.drawCalls - 1000) / 4000);
      this.addBottleneck(BottleneckType.DRAW_CALLS, severity, '绘制调用次数过多，可能影响性能', {
        drawCalls: metrics.drawCalls
      });
    }

    // 检测三角形数量瓶颈
    if (metrics.triangles > 1000000) {
      const severity = Math.min(1, (metrics.triangles - 1000000) / 9000000);
      this.addBottleneck(BottleneckType.GEOMETRY, severity, '三角形数量过多，可能影响性能', {
        triangles: metrics.triangles
      });
    }

    // 检测纹理数量瓶颈
    if (metrics.textures > 100) {
      const severity = Math.min(1, (metrics.textures - 100) / 900);
      this.addBottleneck(BottleneckType.TEXTURE, severity, '纹理数量过多，可能影响性能和内存使用', {
        textures: metrics.textures,
        textureMemory: metrics.textureMemory
      });
    }

    // 检测着色器程序数量瓶颈
    if (metrics.programs > 50) {
      const severity = Math.min(1, (metrics.programs - 50) / 450);
      this.addBottleneck(BottleneckType.SHADER, severity, '着色器程序数量过多，可能影响性能', {
        programs: metrics.programs
      });
    }

    // 检测后处理时间瓶颈
    if (metrics.postProcessingTime > 5) {
      const severity = Math.min(1, (metrics.postProcessingTime - 5) / 15);
      this.addBottleneck(BottleneckType.POST_PROCESSING, severity, '后处理时间过长，可能影响性能', {
        postProcessingTime: metrics.postProcessingTime
      });
    }

    // 检测内存使用瓶颈
    if (metrics.memoryUsage > 1000) {
      const severity = Math.min(1, (metrics.memoryUsage - 1000) / 3000);
      this.addBottleneck(BottleneckType.MEMORY, severity, '内存使用过高，可能导致性能问题', {
        memoryUsage: metrics.memoryUsage,
        textureMemory: metrics.textureMemory,
        geometryMemory: metrics.geometryMemory
      });
    }
  }

  /**
   * 添加瓶颈
   * @param type 瓶颈类型
   * @param severity 严重程度
   * @param description 描述
   * @param metrics 相关指标
   */
  private addBottleneck(
    type: BottleneckType,
    severity: number,
    description: string,
    metrics: Record<string, number>
  ): void {
    const bottleneck: BottleneckInfo = {
      type,
      severity,
      description,
      metrics,
      timestamp: performance.now()
    };

    this.detectedBottlenecks.push(bottleneck);

    // 发出瓶颈检测事件
    this.eventEmitter.emit(GPUPerformanceAnalyzerEventType.BOTTLENECK_DETECTED, bottleneck);

    if (this.config.debug) {
      Debug.log('GPUPerformanceAnalyzer', `检测到瓶颈: ${type}`, bottleneck);
    }
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationSuggestions(): void {
    // 清除旧的建议
    this.optimizationSuggestions = [];

    // 根据检测到的瓶颈生成建议
    for (const bottleneck of this.detectedBottlenecks) {
      const suggestions = this.getSuggestionsForBottleneck(bottleneck);

      for (const suggestion of suggestions) {
        // 检查是否已存在相同ID的建议
        const existingIndex = this.optimizationSuggestions.findIndex(s => s.id === suggestion.id);

        if (existingIndex >= 0) {
          // 更新现有建议
          this.optimizationSuggestions[existingIndex] = suggestion;
        } else {
          // 添加新建议
          this.optimizationSuggestions.push(suggestion);

          // 发出优化建议事件
          this.eventEmitter.emit(GPUPerformanceAnalyzerEventType.OPTIMIZATION_SUGGESTION, suggestion);

          if (this.config.debug) {
            Debug.log('GPUPerformanceAnalyzer', `生成优化建议: ${suggestion.title}`, suggestion);
          }
        }
      }
    }
  }

  /**
   * 获取瓶颈的优化建议
   * @param bottleneck 瓶颈信息
   * @returns 优化建议数组
   */
  private getSuggestionsForBottleneck(bottleneck: BottleneckInfo): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];
    const timestamp = performance.now();

    switch (bottleneck.type) {
      case BottleneckType.CPU:
        suggestions.push({
          id: 'cpu_optimize_js',
          title: '优化JavaScript代码',
          description: '检查并优化主线程上的JavaScript代码，减少计算密集型操作，考虑使用Web Worker进行并行计算。',
          priority: bottleneck.severity * 0.9,
          difficulty: 0.7,
          expectedImprovement: 0.6,
          bottleneckType: BottleneckType.CPU,
          timestamp
        });

        suggestions.push({
          id: 'cpu_reduce_draw_calls',
          title: '减少绘制调用',
          description: '合并网格、使用实例化渲染或静态批处理来减少绘制调用次数。',
          priority: bottleneck.severity * 0.8,
          difficulty: 0.5,
          expectedImprovement: 0.7,
          bottleneckType: BottleneckType.CPU,
          timestamp
        });
        break;

      case BottleneckType.DRAW_CALLS:
        suggestions.push({
          id: 'draw_calls_instancing',
          title: '使用实例化渲染',
          description: '对于重复的对象，使用THREE.InstancedMesh进行实例化渲染，大幅减少绘制调用。',
          priority: bottleneck.severity * 0.9,
          difficulty: 0.6,
          expectedImprovement: 0.8,
          bottleneckType: BottleneckType.DRAW_CALLS,
          timestamp
        });

        suggestions.push({
          id: 'draw_calls_merge_geometries',
          title: '合并几何体',
          description: '合并使用相同材质的几何体，减少绘制调用次数。',
          priority: bottleneck.severity * 0.8,
          difficulty: 0.4,
          expectedImprovement: 0.7,
          bottleneckType: BottleneckType.DRAW_CALLS,
          timestamp
        });
        break;

      case BottleneckType.GEOMETRY:
        suggestions.push({
          id: 'geometry_lod',
          title: '实现LOD（细节层次）',
          description: '使用THREE.LOD为远处的对象使用简化的几何体，减少总三角形数量。',
          priority: bottleneck.severity * 0.9,
          difficulty: 0.5,
          expectedImprovement: 0.7,
          bottleneckType: BottleneckType.GEOMETRY,
          timestamp
        });

        suggestions.push({
          id: 'geometry_simplify',
          title: '简化几何体',
          description: '使用几何体简化算法减少模型的三角形数量，特别是对于复杂模型。',
          priority: bottleneck.severity * 0.8,
          difficulty: 0.4,
          expectedImprovement: 0.6,
          bottleneckType: BottleneckType.GEOMETRY,
          timestamp
        });
        break;

      case BottleneckType.TEXTURE:
        suggestions.push({
          id: 'texture_compression',
          title: '使用纹理压缩',
          description: '使用压缩纹理格式（如DXT、ETC、ASTC）减少纹理内存使用。',
          priority: bottleneck.severity * 0.9,
          difficulty: 0.3,
          expectedImprovement: 0.7,
          bottleneckType: BottleneckType.TEXTURE,
          timestamp
        });

        suggestions.push({
          id: 'texture_atlas',
          title: '使用纹理图集',
          description: '将多个小纹理合并为一个大纹理图集，减少纹理切换和内存使用。',
          priority: bottleneck.severity * 0.7,
          difficulty: 0.6,
          expectedImprovement: 0.6,
          bottleneckType: BottleneckType.TEXTURE,
          timestamp
        });
        break;

      case BottleneckType.SHADER:
        suggestions.push({
          id: 'shader_simplify',
          title: '简化着色器',
          description: '简化复杂的着色器程序，减少计算量和分支语句。',
          priority: bottleneck.severity * 0.8,
          difficulty: 0.7,
          expectedImprovement: 0.6,
          bottleneckType: BottleneckType.SHADER,
          timestamp
        });

        suggestions.push({
          id: 'shader_share',
          title: '共享着色器程序',
          description: '尽可能使用相同的着色器程序，减少着色器切换和编译次数。',
          priority: bottleneck.severity * 0.7,
          difficulty: 0.5,
          expectedImprovement: 0.5,
          bottleneckType: BottleneckType.SHADER,
          timestamp
        });
        break;

      case BottleneckType.POST_PROCESSING:
        suggestions.push({
          id: 'post_reduce_effects',
          title: '减少后处理效果',
          description: '减少或简化后处理效果，特别是在移动设备上。',
          priority: bottleneck.severity * 0.9,
          difficulty: 0.3,
          expectedImprovement: 0.7,
          bottleneckType: BottleneckType.POST_PROCESSING,
          timestamp
        });

        suggestions.push({
          id: 'post_optimize_resolution',
          title: '优化后处理分辨率',
          description: '降低后处理效果的渲染分辨率，然后上采样到屏幕分辨率。',
          priority: bottleneck.severity * 0.8,
          difficulty: 0.4,
          expectedImprovement: 0.6,
          bottleneckType: BottleneckType.POST_PROCESSING,
          timestamp
        });
        break;

      case BottleneckType.MEMORY:
        suggestions.push({
          id: 'memory_dispose',
          title: '及时释放资源',
          description: '确保不再使用的几何体、材质和纹理被正确释放，避免内存泄漏。',
          priority: bottleneck.severity * 0.9,
          difficulty: 0.5,
          expectedImprovement: 0.7,
          bottleneckType: BottleneckType.MEMORY,
          timestamp
        });

        suggestions.push({
          id: 'memory_texture_size',
          title: '减小纹理尺寸',
          description: '减小纹理分辨率或使用mipmap，降低内存使用。',
          priority: bottleneck.severity * 0.8,
          difficulty: 0.3,
          expectedImprovement: 0.6,
          bottleneckType: BottleneckType.MEMORY,
          timestamp
        });
        break;
    }

    return suggestions;
  }

  /**
   * 计算性能评分
   */
  private calculatePerformanceScore(): void {
    if (this.metricsHistory.length === 0) {
      this.performanceScore = 100;
      return;
    }

    // 获取最新指标
    const metrics = this.metricsHistory[this.metricsHistory.length - 1];

    // 基础分数
    let score = 100;

    // 根据FPS扣分
    if (metrics.fps < 60) {
      score -= (60 - metrics.fps) * 1.5;
    }

    // 根据绘制调用扣分
    if (metrics.drawCalls > 1000) {
      score -= Math.min(20, (metrics.drawCalls - 1000) / 200);
    }

    // 根据三角形数量扣分
    if (metrics.triangles > 1000000) {
      score -= Math.min(15, (metrics.triangles - 1000000) / 1000000);
    }

    // 根据纹理数量扣分
    if (metrics.textures > 100) {
      score -= Math.min(10, (metrics.textures - 100) / 20);
    }

    // 根据着色器程序数量扣分
    if (metrics.programs > 50) {
      score -= Math.min(10, (metrics.programs - 50) / 10);
    }

    // 根据渲染时间扣分
    if (metrics.renderTime > 16) {
      score -= Math.min(20, (metrics.renderTime - 16) * 2);
    }

    // 根据后处理时间扣分
    if (metrics.postProcessingTime > 5) {
      score -= Math.min(10, (metrics.postProcessingTime - 5) * 2);
    }

    // 根据内存使用扣分
    if (metrics.memoryUsage > 1000) {
      score -= Math.min(15, (metrics.memoryUsage - 1000) / 200);
    }

    // 确保分数在0-100范围内
    this.performanceScore = Math.max(0, Math.min(100, score));

    if (this.config.debug) {
      Debug.log('GPUPerformanceAnalyzer', `性能评分: ${this.performanceScore.toFixed(1)}`);
    }
  }

  /**
   * 获取分析报告
   * @returns 分析报告
   */
  public getAnalysisReport(): AnalysisReport {
    // 计算平均指标
    const averageMetrics = this.calculateAverageMetrics();

    // 计算最小指标
    const minMetrics = this.calculateMinMetrics();

    // 计算最大指标
    const maxMetrics = this.calculateMaxMetrics();

    // 获取当前指标
    const currentMetrics = this.metricsHistory.length > 0
      ? this.metricsHistory[this.metricsHistory.length - 1]
      : this.createEmptyMetrics();

    // 创建报告
    const report: AnalysisReport = {
      currentMetrics,
      averageMetrics,
      minMetrics,
      maxMetrics,
      metricsHistory: [...this.metricsHistory],
      detectedBottlenecks: [...this.detectedBottlenecks],
      optimizationSuggestions: [...this.optimizationSuggestions],
      performanceScore: this.performanceScore,
      timestamp: performance.now()
    };

    if (this.config.debug) {
      Debug.log('GPUPerformanceAnalyzer', '生成分析报告', report);
    }

    // 发出分析完成事件
    this.eventEmitter.emit(GPUPerformanceAnalyzerEventType.ANALYSIS_COMPLETED, report);

    return report;
  }

  /**
   * 计算平均指标
   * @returns 平均指标
   */
  private calculateAverageMetrics(): GPUPerformanceMetrics {
    if (this.metricsHistory.length === 0) {
      return this.createEmptyMetrics();
    }

    // 初始化累加器
    const sum: Partial<GPUPerformanceMetrics> = {
      fps: 0,
      drawCalls: 0,
      triangles: 0,
      points: 0,
      lines: 0,
      geometries: 0,
      textures: 0,
      programs: 0,
      renderTargets: 0,
      renderTime: 0,
      postProcessingTime: 0,
      memoryUsage: 0,
      textureMemory: 0,
      geometryMemory: 0
    };

    // 累加所有指标
    for (const metrics of this.metricsHistory) {
      sum.fps! += metrics.fps;
      sum.drawCalls! += metrics.drawCalls;
      sum.triangles! += metrics.triangles;
      sum.points! += metrics.points;
      sum.lines! += metrics.lines;
      sum.geometries! += metrics.geometries;
      sum.textures! += metrics.textures;
      sum.programs! += metrics.programs;
      sum.renderTargets! += metrics.renderTargets;
      sum.renderTime! += metrics.renderTime;
      sum.postProcessingTime! += metrics.postProcessingTime;
      sum.memoryUsage! += metrics.memoryUsage;
      sum.textureMemory! += metrics.textureMemory;
      sum.geometryMemory! += metrics.geometryMemory;
    }

    // 计算平均值
    const count = this.metricsHistory.length;

    return {
      fps: sum.fps! / count,
      drawCalls: Math.round(sum.drawCalls! / count),
      triangles: Math.round(sum.triangles! / count),
      points: Math.round(sum.points! / count),
      lines: Math.round(sum.lines! / count),
      geometries: Math.round(sum.geometries! / count),
      textures: Math.round(sum.textures! / count),
      programs: Math.round(sum.programs! / count),
      renderTargets: Math.round(sum.renderTargets! / count),
      renderTime: sum.renderTime! / count,
      postProcessingTime: sum.postProcessingTime! / count,
      memoryUsage: sum.memoryUsage! / count,
      textureMemory: sum.textureMemory! / count,
      geometryMemory: sum.geometryMemory! / count,
      timestamp: performance.now()
    };
  }

  /**
   * 计算最小指标
   * @returns 最小指标
   */
  private calculateMinMetrics(): GPUPerformanceMetrics {
    if (this.metricsHistory.length === 0) {
      return this.createEmptyMetrics();
    }

    // 初始化最小值
    const min: GPUPerformanceMetrics = { ...this.metricsHistory[0] };

    // 查找最小值
    for (const metrics of this.metricsHistory) {
      min.fps = Math.min(min.fps, metrics.fps);
      min.drawCalls = Math.min(min.drawCalls, metrics.drawCalls);
      min.triangles = Math.min(min.triangles, metrics.triangles);
      min.points = Math.min(min.points, metrics.points);
      min.lines = Math.min(min.lines, metrics.lines);
      min.geometries = Math.min(min.geometries, metrics.geometries);
      min.textures = Math.min(min.textures, metrics.textures);
      min.programs = Math.min(min.programs, metrics.programs);
      min.renderTargets = Math.min(min.renderTargets, metrics.renderTargets);
      min.renderTime = Math.min(min.renderTime, metrics.renderTime);
      min.postProcessingTime = Math.min(min.postProcessingTime, metrics.postProcessingTime);
      min.memoryUsage = Math.min(min.memoryUsage, metrics.memoryUsage);
      min.textureMemory = Math.min(min.textureMemory, metrics.textureMemory);
      min.geometryMemory = Math.min(min.geometryMemory, metrics.geometryMemory);
    }

    min.timestamp = performance.now();

    return min;
  }

  /**
   * 计算最大指标
   * @returns 最大指标
   */
  private calculateMaxMetrics(): GPUPerformanceMetrics {
    if (this.metricsHistory.length === 0) {
      return this.createEmptyMetrics();
    }

    // 初始化最大值
    const max: GPUPerformanceMetrics = { ...this.metricsHistory[0] };

    // 查找最大值
    for (const metrics of this.metricsHistory) {
      max.fps = Math.max(max.fps, metrics.fps);
      max.drawCalls = Math.max(max.drawCalls, metrics.drawCalls);
      max.triangles = Math.max(max.triangles, metrics.triangles);
      max.points = Math.max(max.points, metrics.points);
      max.lines = Math.max(max.lines, metrics.lines);
      max.geometries = Math.max(max.geometries, metrics.geometries);
      max.textures = Math.max(max.textures, metrics.textures);
      max.programs = Math.max(max.programs, metrics.programs);
      max.renderTargets = Math.max(max.renderTargets, metrics.renderTargets);
      max.renderTime = Math.max(max.renderTime, metrics.renderTime);
      max.postProcessingTime = Math.max(max.postProcessingTime, metrics.postProcessingTime);
      max.memoryUsage = Math.max(max.memoryUsage, metrics.memoryUsage);
      max.textureMemory = Math.max(max.textureMemory, metrics.textureMemory);
      max.geometryMemory = Math.max(max.geometryMemory, metrics.geometryMemory);
    }

    max.timestamp = performance.now();

    return max;
  }

  /**
   * 创建空指标
   * @returns 空指标
   */
  private createEmptyMetrics(): GPUPerformanceMetrics {
    return {
      fps: 0,
      drawCalls: 0,
      triangles: 0,
      points: 0,
      lines: 0,
      geometries: 0,
      textures: 0,
      programs: 0,
      renderTargets: 0,
      renderTime: 0,
      postProcessingTime: 0,
      memoryUsage: 0,
      textureMemory: 0,
      geometryMemory: 0,
      timestamp: performance.now()
    };
  }

  /**
   * 添加事件监听器
   * @param eventType 事件类型
   * @param listener 监听器
   */
  public addEventListener(eventType: GPUPerformanceAnalyzerEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(eventType, listener);
  }

  /**
   * 移除事件监听器
   * @param eventType 事件类型
   * @param listener 监听器
   */
  public removeEventListener(eventType: GPUPerformanceAnalyzerEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(eventType, listener);
  }

  /**
   * 清除所有数据
   */
  public clearData(): void {
    this.metricsHistory = [];
    this.detectedBottlenecks = [];
    this.optimizationSuggestions = [];
    this.performanceScore = 100;

    if (this.config.debug) {
      Debug.log('GPUPerformanceAnalyzer', '数据已清除');
    }
  }
}
