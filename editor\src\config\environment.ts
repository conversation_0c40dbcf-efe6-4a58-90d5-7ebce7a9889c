/**
 * 环境配置管理
 * 统一管理开发、测试、生产环境的配置
 */

export interface EnvironmentConfig {
  // API配置
  apiUrl: string;
  collaborationServerUrl: string;
  
  // 功能开关
  enableDebug: boolean;
  enableMockData: boolean;
  enableAnalytics: boolean;
  enableHotReload: boolean;
  
  // 性能配置
  requestTimeout: number;
  cacheTimeout: number;
  maxRetries: number;
  
  // 安全配置
  enableCSRF: boolean;
  enableCORS: boolean;
  
  // 日志配置
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  enableConsoleLog: boolean;
  enableRemoteLog: boolean;
  
  // 微服务配置
  services: {
    userService: string;
    projectService: string;
    assetService: string;
    renderService: string;
    collaborationService: string;
  };
}

/**
 * 环境类型
 */
export type Environment = 'development' | 'test' | 'staging' | 'production';

/**
 * 获取当前环境
 */
export function getCurrentEnvironment(): Environment {
  // 优先使用环境变量
  if (typeof window !== 'undefined') {
    const env = window.__ENV__?.NODE_ENV;
    if (env) return env as Environment;
  }

  // 使用Vite环境变量
  if (typeof import.meta !== 'undefined' && import.meta.env?.MODE) {
    switch (import.meta.env.MODE) {
      case 'development':
        return 'development';
      case 'test':
        return 'test';
      case 'staging':
        return 'staging';
      case 'production':
        return 'production';
      default:
        return 'development';
    }
  }

  // 回退到 NODE_ENV
  if (typeof process !== 'undefined' && process.env?.NODE_ENV) {
    return process.env.NODE_ENV as Environment;
  }

  return 'development';
}

/**
 * 开发环境配置
 */
const developmentConfig: EnvironmentConfig = {
  apiUrl: 'http://localhost:3000/api',
  collaborationServerUrl: 'ws://localhost:3007',

  enableDebug: true,
  enableMockData: false,
  enableAnalytics: false,
  enableHotReload: true,

  requestTimeout: 30000,
  cacheTimeout: 5 * 60 * 1000,
  maxRetries: 3,

  enableCSRF: false,
  enableCORS: true,

  logLevel: 'debug',
  enableConsoleLog: true,
  enableRemoteLog: false,

  services: {
    userService: 'http://localhost:3000/api',
    projectService: 'http://localhost:3000/api',
    assetService: 'http://localhost:3000/api',
    renderService: 'http://localhost:3000/api',
    collaborationService: 'http://localhost:3007',
  },
};

/**
 * 测试环境配置
 */
const testConfig: EnvironmentConfig = {
  apiUrl: 'http://localhost:3000/api',
  collaborationServerUrl: 'ws://localhost:3007',

  enableDebug: true,
  enableMockData: false,
  enableAnalytics: false,
  enableHotReload: false,

  requestTimeout: 10000,
  cacheTimeout: 1 * 60 * 1000,
  maxRetries: 1,

  enableCSRF: false,
  enableCORS: true,

  logLevel: 'warn',
  enableConsoleLog: true,
  enableRemoteLog: false,

  services: {
    userService: 'http://localhost:3000/api',
    projectService: 'http://localhost:3000/api',
    assetService: 'http://localhost:3000/api',
    renderService: 'http://localhost:3000/api',
    collaborationService: 'http://localhost:3007',
  },
};

/**
 * 预发布环境配置
 */
const stagingConfig: EnvironmentConfig = {
  apiUrl: '/api',
  collaborationServerUrl: 'ws://staging.example.com:3007',
  
  enableDebug: false,
  enableMockData: false,
  enableAnalytics: true,
  enableHotReload: false,
  
  requestTimeout: 30000,
  cacheTimeout: 10 * 60 * 1000,
  maxRetries: 3,
  
  enableCSRF: true,
  enableCORS: true,
  
  logLevel: 'info',
  enableConsoleLog: false,
  enableRemoteLog: true,
  
  services: {
    userService: 'https://staging-api.example.com/user',
    projectService: 'https://staging-api.example.com/project',
    assetService: 'https://staging-api.example.com/asset',
    renderService: 'https://staging-api.example.com/render',
    collaborationService: 'https://staging-api.example.com/collaboration',
  },
};

/**
 * 生产环境配置
 */
const productionConfig: EnvironmentConfig = {
  apiUrl: '/api',
  collaborationServerUrl: 'wss://api.example.com:3007',
  
  enableDebug: false,
  enableMockData: false,
  enableAnalytics: true,
  enableHotReload: false,
  
  requestTimeout: 30000,
  cacheTimeout: 15 * 60 * 1000,
  maxRetries: 5,
  
  enableCSRF: true,
  enableCORS: false,
  
  logLevel: 'error',
  enableConsoleLog: false,
  enableRemoteLog: true,
  
  services: {
    userService: 'https://api.example.com/user',
    projectService: 'https://api.example.com/project',
    assetService: 'https://api.example.com/asset',
    renderService: 'https://api.example.com/render',
    collaborationService: 'https://api.example.com/collaboration',
  },
};

/**
 * 环境配置映射
 */
const environmentConfigs: Record<Environment, EnvironmentConfig> = {
  development: developmentConfig,
  test: testConfig,
  staging: stagingConfig,
  production: productionConfig,
};

/**
 * 获取当前环境配置
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const env = getCurrentEnvironment();
  const config = environmentConfigs[env];
  
  // 合并环境变量覆盖
  return mergeWithEnvVars(config);
}

/**
 * 合并环境变量覆盖
 */
function mergeWithEnvVars(config: EnvironmentConfig): EnvironmentConfig {
  const merged = { ...config };
  
  // 检查window上的环境变量
  if (typeof window !== 'undefined') {
    const env = window.__ENV__;
    if (env) {
      if (env.REACT_APP_API_URL) {
        merged.apiUrl = env.REACT_APP_API_URL;
      }
      if (env.REACT_APP_COLLABORATION_SERVER_URL) {
        merged.collaborationServerUrl = env.REACT_APP_COLLABORATION_SERVER_URL;
      }
      if (env.REACT_APP_ENABLE_DEBUG) {
        merged.enableDebug = env.REACT_APP_ENABLE_DEBUG === 'true';
      }
      if (env.REACT_APP_LOG_LEVEL) {
        merged.logLevel = env.REACT_APP_LOG_LEVEL as 'debug' | 'info' | 'warn' | 'error';
      }
    }
  }
  
  return merged;
}

/**
 * 验证配置
 */
export function validateConfig(config: EnvironmentConfig): boolean {
  try {
    // 验证必需的配置项
    if (!config.apiUrl) {
      console.error('API URL is required');
      return false;
    }
    
    if (!config.collaborationServerUrl) {
      console.error('Collaboration server URL is required');
      return false;
    }
    
    // 验证URL格式
    try {
      new URL(config.collaborationServerUrl);
    } catch {
      console.error('Invalid collaboration server URL format');
      return false;
    }
    
    // 验证数值配置
    if (config.requestTimeout <= 0) {
      console.error('Request timeout must be positive');
      return false;
    }
    
    if (config.cacheTimeout <= 0) {
      console.error('Cache timeout must be positive');
      return false;
    }
    
    if (config.maxRetries < 0) {
      console.error('Max retries must be non-negative');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Config validation error:', error);
    return false;
  }
}

// 导出当前配置
export const config = getEnvironmentConfig();

// 验证配置
if (!validateConfig(config)) {
  console.warn('Configuration validation failed, using defaults');
}

export default config;
