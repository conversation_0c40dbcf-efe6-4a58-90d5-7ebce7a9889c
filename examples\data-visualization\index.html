<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DL（Digital Learning）引擎 - 数据可视化示例</title>
  <link rel="stylesheet" href="../assets/css/example.css">
  <link rel="stylesheet" href="./styles/main.css">
  <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
</head>
<body>
  <header class="example-header">
    <div class="logo">DL（Digital Learning）引擎</div>
    <h1>数据可视化示例</h1>
    <div class="actions">
      <button id="importBtn" class="btn">导入数据</button>
      <button id="exportBtn" class="btn">导出结果</button>
      <button id="helpBtn" class="btn">帮助</button>
    </div>
  </header>

  <main class="example-container">
    <aside class="control-panel">
      <section class="panel-section">
        <h2>可视化类型</h2>
        <div class="radio-group">
          <label>
            <input type="radio" name="visualType" value="barChart" checked>
            3D柱状图
          </label>
          <label>
            <input type="radio" name="visualType" value="scatterPlot">
            3D散点图
          </label>
          <label>
            <input type="radio" name="visualType" value="heatMap">
            热力图
          </label>
          <label>
            <input type="radio" name="visualType" value="geoMap">
            地理数据
          </label>
        </div>
      </section>

      <section class="panel-section">
        <h2>数据集</h2>
        <select id="datasetSelect">
          <option value="population">人口数据 (1990-2020)</option>
          <option value="economy">经济数据 (1990-2020)</option>
          <option value="climate">气候数据 (1990-2020)</option>
          <option value="custom">自定义数据</option>
        </select>
      </section>

      <section class="panel-section">
        <h2>时间控制</h2>
        <div class="slider-container">
          <input type="range" id="timeSlider" min="0" max="3" step="1" value="3">
          <div class="slider-labels">
            <span>1990</span>
            <span>2000</span>
            <span>2010</span>
            <span>2020</span>
          </div>
        </div>
        <div class="time-controls">
          <button id="playBtn" class="btn-icon">▶</button>
          <button id="pauseBtn" class="btn-icon">⏸</button>
          <button id="resetBtn" class="btn-icon">⏮</button>
        </div>
      </section>

      <section class="panel-section">
        <h2>可视化参数</h2>
        <div class="form-group">
          <label for="scaleSlider">比例</label>
          <input type="range" id="scaleSlider" min="0.5" max="2" step="0.1" value="1">
          <span id="scaleValue">1.0</span>
        </div>
        <div class="form-group">
          <label for="heightSlider">高度</label>
          <input type="range" id="heightSlider" min="0.5" max="2" step="0.1" value="1">
          <span id="heightValue">1.0</span>
        </div>
        <div class="form-group">
          <label for="colorScheme">颜色方案</label>
          <select id="colorScheme">
            <option value="default">默认</option>
            <option value="rainbow">彩虹</option>
            <option value="heatmap">热力图</option>
            <option value="blueRed">蓝红</option>
            <option value="greenPurple">绿紫</option>
          </select>
        </div>
        <div class="form-group">
          <label for="show-labels">显示标签</label>
          <input type="checkbox" id="show-labels" checked>
        </div>
        <div class="form-group">
          <label for="show-grid">显示网格</label>
          <input type="checkbox" id="show-grid" checked>
        </div>
      </section>

      <section class="panel-section">
        <h2>视图控制</h2>
        <div class="form-group">
          <button id="view-3d" class="btn">3D视图</button>
          <button id="view-top" class="btn">俯视图</button>
          <button id="view-side" class="btn">侧视图</button>
        </div>
      </section>
    </aside>

    <div class="visualization-container">
      <div id="scene-container"></div>
      <div id="tooltip" class="tooltip"></div>
      <div class="legend" id="legend"></div>
      <div class="stats-panel">
        <div class="stat-item">
          <div class="stat-label">数据点数</div>
          <div class="stat-value" id="dataPointCount">0</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">平均值</div>
          <div class="stat-value" id="averageValue">0</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">最大值</div>
          <div class="stat-value" id="maxDataValue">0</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">最小值</div>
          <div class="stat-value" id="minDataValue">0</div>
        </div>
      </div>
    </div>

    <aside class="info-panel">
      <section class="panel-section">
        <h2>数据详情</h2>
        <div id="dataDetails">
          <p>选择一个数据点以查看详情</p>
        </div>
      </section>
      <section class="panel-section">
        <h2>数据统计</h2>
        <div id="dataStats"></div>
      </section>
      <section class="panel-section">
        <h2>操作说明</h2>
        <ul class="instructions">
          <li><strong>旋转视图</strong>：按住鼠标左键并拖动</li>
          <li><strong>缩放视图</strong>：使用鼠标滚轮</li>
          <li><strong>平移视图</strong>：按住鼠标右键并拖动</li>
          <li><strong>选择数据</strong>：点击数据点</li>
          <li><strong>重置视图</strong>：双击场景</li>
        </ul>
      </section>
    </aside>
  </main>

  <div id="importDialog" class="dialog">
    <div class="dialog-content">
      <h2>导入数据</h2>
      <div class="form-group">
        <label for="dataFile">选择数据文件</label>
        <input type="file" id="dataFile" accept=".csv,.json,.xlsx">
      </div>
      <div class="form-group">
        <label for="dataFormat">数据格式</label>
        <select id="dataFormat">
          <option value="csv">CSV</option>
          <option value="json">JSON</option>
          <option value="excel">Excel</option>
        </select>
      </div>
      <div class="dialog-buttons">
        <button id="importCancelBtn" class="btn">取消</button>
        <button id="importConfirmBtn" class="btn btn-primary">导入</button>
      </div>
    </div>
  </div>

  <div id="exportDialog" class="dialog">
    <div class="dialog-content">
      <h2>导出结果</h2>
      <div class="form-group">
        <label for="exportFormat">导出格式</label>
        <select id="exportFormat">
          <option value="png">PNG图片</option>
          <option value="jpg">JPEG图片</option>
          <option value="scene">场景文件</option>
        </select>
      </div>
      <div class="form-group">
        <label for="exportFilename">文件名</label>
        <input type="text" id="exportFilename" value="data-visualization">
      </div>
      <div class="dialog-buttons">
        <button id="exportCancelBtn" class="btn">取消</button>
        <button id="exportConfirmBtn" class="btn btn-primary">导出</button>
      </div>
    </div>
  </div>

  <div id="helpDialog" class="dialog">
    <div class="dialog-content">
      <h2>帮助</h2>
      <div class="help-content">
        <h3>关于本示例</h3>
        <p>本示例展示了如何使用DL（Digital Learning）引擎进行数据可视化，包括3D柱状图、散点图、热力图和地理数据可视化等。</p>

        <h3>基本操作</h3>
        <ul>
          <li><strong>选择可视化类型</strong>：在左侧面板选择不同的可视化类型</li>
          <li><strong>选择数据集</strong>：在左侧面板选择不同的数据集</li>
          <li><strong>调整时间</strong>：使用时间滑块查看不同时间点的数据</li>
          <li><strong>调整参数</strong>：使用左侧面板的参数控制可视化效果</li>
          <li><strong>导入数据</strong>：点击顶部的"导入数据"按钮导入自定义数据</li>
          <li><strong>导出结果</strong>：点击顶部的"导出结果"按钮导出可视化结果</li>
        </ul>

        <h3>更多资源</h3>
        <p>查看<a href="../docs/data-visualization/README.md" target="_blank">数据可视化文档</a>了解更多信息。</p>
      </div>
      <div class="dialog-buttons">
        <button id="helpCloseBtn" class="btn">关闭</button>
      </div>
    </div>
  </div>

  <div id="loading-overlay">
    <div class="spinner"></div>
  </div>

  <script type="module" src="./scripts/main.js"></script>
</body>
</html>
