import { System } from '../../core/System';
import type { World } from '../../core/World';
/**
 * 水体交互系统事件类型
 */
export declare enum WaterInteractionSystemEventType {
    /** 水花效果 */
    WATER_SPLASH = "water_splash",
    /** 水波纹效果 */
    WATER_RIPPLE = "water_ripple",
    /** 水滴效果 */
    WATER_DROPLET = "water_droplet",
    /** 水流效果 */
    WATER_FLOW = "water_flow",
    /** 水体分裂效果 */
    WATER_SPLITTING = "water_splitting",
    /** 物体浮力效果 */
    OBJECT_BUOYANCY = "object_buoyancy",
    /** 物体阻力效果 */
    OBJECT_DRAG = "object_drag",
    /** 物体旋转阻力效果 */
    OBJECT_ROTATIONAL_DRAG = "object_rotational_drag",
    /** 物体进入水体 */
    OBJECT_ENTER_WATER = "object_enter_water",
    /** 物体离开水体 */
    OBJECT_EXIT_WATER = "object_exit_water"
}
/**
 * 水体交互系统配置
 */
export interface WaterInteractionSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用水花效果 */
    enableSplashEffect?: boolean;
    /** 是否启用水波纹效果 */
    enableRippleEffect?: boolean;
    /** 是否启用水滴效果 */
    enableDropletEffect?: boolean;
    /** 是否启用水流效果 */
    enableFlowEffect?: boolean;
    /** 是否启用水体分裂效果 */
    enableSplittingEffect?: boolean;
    /** 是否启用物体浮力效果 */
    enableBuoyancyEffect?: boolean;
    /** 是否启用物体阻力效果 */
    enableDragEffect?: boolean;
    /** 是否使用体素浮力计算 */
    useVoxelBuoyancy?: boolean;
    /** 体素分辨率 */
    voxelResolution?: number;
    /** 是否启用浮力稳定化 */
    enableBuoyancyStabilization?: boolean;
    /** 浮力稳定化强度 */
    buoyancyStabilizationStrength?: number;
    /** 是否使用方向性阻力 */
    useDirectionalDrag?: boolean;
    /** 是否启用湍流阻力 */
    enableTurbulenceDrag?: boolean;
    /** 是否启用旋转阻力 */
    enableRotationalDrag?: boolean;
    /** 旋转阻力强度 */
    rotationalDragStrength?: number;
    /** 旋转阻力系数 */
    rotationalDragCoefficient?: number;
    /** X方向阻力系数 */
    dragCoefficientX?: number;
    /** Y方向阻力系数 */
    dragCoefficientY?: number;
    /** Z方向阻力系数 */
    dragCoefficientZ?: number;
    /** 是否使用高级水流交互 */
    useAdvancedFlowInteraction?: boolean;
    /** 是否启用湍流水流 */
    enableTurbulentFlow?: boolean;
    /** X方向水流系数 */
    flowCoefficientX?: number;
    /** Y方向水流系数 */
    flowCoefficientY?: number;
    /** Z方向水流系数 */
    flowCoefficientZ?: number;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
    /** 是否启用调试可视化 */
    enableDebugVisualization?: boolean;
    /** 水花效果强度 */
    splashEffectStrength?: number;
    /** 水波纹效果强度 */
    rippleEffectStrength?: number;
    /** 水滴效果强度 */
    dropletEffectStrength?: number;
    /** 水流效果强度 */
    flowEffectStrength?: number;
    /** 水体分裂效果强度 */
    splittingEffectStrength?: number;
    /** 物体浮力效果强度 */
    buoyancyEffectStrength?: number;
    /** 物体阻力效果强度 */
    dragEffectStrength?: number;
}
/**
 * 水体交互系统
 */
export declare class WaterInteractionSystem extends System {
    /** 配置 */
    private config;
    /** 水体物理系统 */
    private waterPhysicsSystem;
    /** 物理系统 */
    private physicsSystem;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监视器 */
    private performanceMonitor;
    /** 调试渲染器 */
    private debugRenderer;
    /** 当前更新频率 */
    private currentUpdateFrequency;
    /** 物体水体交互状态 */
    private objectWaterInteractionStates;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: WaterInteractionSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 注册水体物理系统事件
     */
    private registerWaterPhysicsSystemEvents;
    /**
     * 创建调试渲染器
     */
    private createDebugRenderer;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新水体交互
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterInteractions;
    /**
     * 更新物体与水体的交互
     * @param entityId 物体实体ID
     * @param waterEntityId 水体实体ID
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyPosition 物体位置
     * @param bodyVelocity 物体速度
     * @param bodySize 物体尺寸
     * @param bodyMass 物体质量
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateObjectWaterInteraction;
    /**
     * 更新物体在水中的效果
     * @param entityId 物体实体ID
     * @param waterEntityId 水体实体ID
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyPosition 物体位置
     * @param bodyVelocity 物体速度
     * @param bodySize 物体尺寸
     * @param bodyMass 物体质量
     * @param waterHeight 水面高度
     * @param submergedRatio 浸入比例
     * @param state 交互状态
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateInWaterEffects;
    /**
     * 应用浮力效果
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyPosition 物体位置
     * @param bodySize 物体尺寸
     * @param bodyMass 物体质量
     * @param waterHeight 水面高度
     * @param submergedRatio 浸入比例
     */
    private applyBuoyancyEffect;
    /**
     * 计算体素浮力
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyPosition 物体位置
     * @param bodySize 物体尺寸
     * @param bodyMass 物体质量
     * @param waterHeight 水面高度
     * @returns 浮力和扭矩
     */
    private calculateVoxelBuoyancy;
    /**
     * 应用浮力稳定化
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param submergedRatio 浸入比例
     */
    private applyBuoyancyStabilization;
    /**
     * 应用阻力效果
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyVelocity 物体速度
     * @param bodySize 物体尺寸
     * @param submergedRatio 浸入比例
     */
    private applyDragEffect;
    /**
     * 应用方向性阻力
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyVelocity 物体速度
     * @param bodySize 物体尺寸
     * @param rotation 物体旋转
     * @param submergedRatio 浸入比例
     */
    private applyDirectionalDrag;
    /**
     * 应用旋转阻力
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param angularVelocity 角速度
     * @param bodySize 物体尺寸
     * @param submergedRatio 浸入比例
     */
    private applyRotationalDrag;
    /**
     * 应用水流效果
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyPosition 物体位置
     * @param bodySize 物体尺寸
     * @param bodyMass 物体质量
     * @param submergedRatio 浸入比例
     */
    private applyFlowEffect;
    /**
     * 计算高级水流交互
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyPosition 物体位置
     * @param bodySize 物体尺寸
     * @param rotation 物体旋转
     * @param flowDirection 水流方向
     * @param flowSpeed 水流速度
     * @param relativeVelocity 相对速度
     * @param submergedRatio 浸入比例
     * @returns 水流力和扭矩
     */
    private calculateAdvancedFlowInteraction;
    /**
     * 检查并创建水花效果
     * @param entityId 物体实体ID
     * @param waterEntityId 水体实体ID
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyPosition 物体位置
     * @param bodyVelocity 物体速度
     * @param bodySize 物体尺寸
     * @param waterHeight 水面高度
     * @param state 交互状态
     */
    private checkAndCreateSplashEffect;
    /**
     * 检查并创建水波纹效果
     * @param entityId 物体实体ID
     * @param waterEntityId 水体实体ID
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyPosition 物体位置
     * @param bodyVelocity 物体速度
     * @param bodySize 物体尺寸
     * @param waterHeight 水面高度
     * @param state 交互状态
     */
    private checkAndCreateRippleEffect;
    /**
     * 检查并创建水滴效果
     * @param entityId 物体实体ID
     * @param waterEntityId 水体实体ID
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyPosition 物体位置
     * @param bodyVelocity 物体速度
     * @param bodySize 物体尺寸
     * @param waterHeight 水面高度
     * @param submergedRatio 浸入比例
     * @param state 交互状态
     */
    private checkAndCreateDropletEffect;
    /**
     * 检查并创建水体分裂效果
     * @param entityId 物体实体ID
     * @param waterEntityId 水体实体ID
     * @param physicsBody 物理体
     * @param waterBody 水体
     * @param bodyPosition 物体位置
     * @param bodyVelocity 物体速度
     * @param bodySize 物体尺寸
     * @param waterHeight 水面高度
     * @param state 交互状态
     */
    private checkAndCreateSplittingEffect;
    /**
     * 处理物体进入水体
     * @param data 事件数据
     */
    private handleObjectEnterWater;
    /**
     * 处理物体离开水体
     * @param data 事件数据
     */
    private handleObjectExitWater;
    /**
     * 处理物体离开水体（如果需要）
     * @param entityId 物体实体ID
     * @param waterEntityId 水体实体ID
     */
    private handleObjectExitWaterIfNeeded;
    /**
     * 处理水体碰撞
     * @param data 事件数据
     */
    private handleWaterCollision;
    /**
     * 处理水流冲击
     * @param data 事件数据
     */
    private handleWaterFlowImpact;
    /**
     * 处理水体分裂
     * @param data 事件数据
     */
    private handleWaterSplitting;
    /**
     * 更新调试渲染器
     */
    private updateDebugRenderer;
    /**
     * 注册事件监听器
     * @param eventType 事件类型
     * @param callback 回调函数
     */
    addEventListener(eventType: WaterInteractionSystemEventType, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param eventType 事件类型
     * @param callback 回调函数
     */
    removeEventListener(eventType: WaterInteractionSystemEventType, callback: (data: any) => void): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
