# DL引擎渲染服务部署运维指南

## 概述

本文档提供DL引擎渲染服务的详细部署、配置和运维指南，包括Docker部署、Kubernetes集群部署、监控配置和故障排除等内容。

## 1. 环境要求

### 1.1 硬件要求
- **CPU**: 最低4核，推荐8核以上
- **内存**: 最低8GB，推荐16GB以上
- **存储**: 最低100GB SSD，推荐500GB以上
- **GPU**: 可选，支持CUDA的显卡可提升渲染性能
- **网络**: 千兆网络，低延迟

### 1.2 软件依赖
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+)
- **容器运行时**: Docker 20.10+ 或 Podman
- **编排工具**: Docker Compose 2.0+ 或 Kubernetes 1.20+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **Node.js**: 18.x+ (开发环境)

## 2. Docker单机部署

### 2.1 构建镜像
```bash
# 进入渲染服务目录
cd server/render-service

# 构建Docker镜像
docker build -t dl-engine/render-service:latest .

# 验证镜像
docker images | grep render-service
```

### 2.2 运行容器
```bash
# 创建网络
docker network create dl-engine-network

# 启动MySQL
docker run -d \
  --name mysql \
  --network dl-engine-network \
  -e MYSQL_ROOT_PASSWORD=password \
  -e MYSQL_DATABASE=dl_engine \
  -p 3306:3306 \
  mysql:8.0

# 启动Redis
docker run -d \
  --name redis \
  --network dl-engine-network \
  -p 6379:6379 \
  redis:7-alpine

# 启动渲染服务
docker run -d \
  --name render-service \
  --network dl-engine-network \
  -p 3004:3004 \
  -p 4004:4004 \
  -e DATABASE_HOST=mysql \
  -e REDIS_HOST=redis \
  -e USER_SERVICE_HOST=user-service \
  -e PROJECT_SERVICE_HOST=project-service \
  -e ASSET_SERVICE_HOST=asset-service \
  -v /opt/renders:/app/renders \
  dl-engine/render-service:latest
```

### 2.3 Docker Compose部署
```yaml
# docker-compose.render.yml
version: '3.8'

services:
  render-service:
    build: ./server/render-service
    container_name: dl-render-service
    ports:
      - "3004:3004"
      - "4004:4004"
    environment:
      - NODE_ENV=production
      - DATABASE_HOST=mysql
      - DATABASE_PORT=3306
      - DATABASE_USERNAME=root
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=dl_engine
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - JWT_SECRET=${JWT_SECRET}
      - MAX_RENDER_TIME=300000
      - MAX_CONCURRENT_JOBS=5
    volumes:
      - render-outputs:/app/renders
      - ./logs/render-service:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - dl-engine-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4004/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  mysql:
    image: mysql:8.0
    container_name: dl-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=dl_engine
      - MYSQL_USER=dluser
      - MYSQL_PASSWORD=dlpassword
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./config/mysql:/etc/mysql/conf.d
    networks:
      - dl-engine-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: dl-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - dl-engine-network
    restart: unless-stopped

volumes:
  mysql-data:
  redis-data:
  render-outputs:

networks:
  dl-engine-network:
    driver: bridge
```

## 3. Kubernetes集群部署

### 3.1 命名空间和配置
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine
  labels:
    name: dl-engine

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: render-service-config
  namespace: dl-engine
data:
  NODE_ENV: "production"
  DATABASE_HOST: "mysql-service"
  DATABASE_PORT: "3306"
  DATABASE_NAME: "dl_engine"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  USER_SERVICE_HOST: "user-service"
  USER_SERVICE_PORT: "3001"
  PROJECT_SERVICE_HOST: "project-service"
  PROJECT_SERVICE_PORT: "3002"
  ASSET_SERVICE_HOST: "asset-service"
  ASSET_SERVICE_PORT: "3003"
  MAX_RENDER_TIME: "300000"
  MAX_CONCURRENT_JOBS: "5"

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: render-service-secret
  namespace: dl-engine
type: Opaque
data:
  DATABASE_USERNAME: cm9vdA==  # root
  DATABASE_PASSWORD: cGFzc3dvcmQ=  # password
  JWT_SECRET: eW91ci1zZWNyZXQta2V5  # your-secret-key
```

### 3.2 部署配置
```yaml
# render-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: render-service
  namespace: dl-engine
  labels:
    app: render-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: render-service
  template:
    metadata:
      labels:
        app: render-service
    spec:
      containers:
      - name: render-service
        image: dl-engine/render-service:latest
        ports:
        - containerPort: 3004
          name: tcp-port
        - containerPort: 4004
          name: http-port
        envFrom:
        - configMapRef:
            name: render-service-config
        - secretRef:
            name: render-service-secret
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        volumeMounts:
        - name: render-outputs
          mountPath: /app/renders
        - name: logs
          mountPath: /app/logs
        livenessProbe:
          httpGet:
            path: /api/health
            port: 4004
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 4004
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: render-outputs
        persistentVolumeClaim:
          claimName: render-outputs-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: render-logs-pvc

---
# render-service-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: render-service
  namespace: dl-engine
  labels:
    app: render-service
spec:
  selector:
    app: render-service
  ports:
  - name: tcp-port
    port: 3004
    targetPort: 3004
    protocol: TCP
  - name: http-port
    port: 4004
    targetPort: 4004
    protocol: TCP
  type: ClusterIP

---
# render-service-hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: render-service-hpa
  namespace: dl-engine
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: render-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 3.3 存储配置
```yaml
# storage.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: render-outputs-pvc
  namespace: dl-engine
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 500Gi
  storageClassName: nfs-storage

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: render-logs-pvc
  namespace: dl-engine
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: nfs-storage
```

## 4. 监控和日志

### 4.1 Prometheus监控配置
```yaml
# prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'render-service'
      static_configs:
      - targets: ['render-service.dl-engine:4004']
      metrics_path: '/api/metrics'
      scrape_interval: 30s
```

### 4.2 Grafana仪表板
```json
{
  "dashboard": {
    "title": "DL引擎渲染服务监控",
    "panels": [
      {
        "title": "渲染任务吞吐量",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(render_jobs_completed_total[5m])",
            "legendFormat": "完成任务/秒"
          }
        ]
      },
      {
        "title": "平均渲染时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(render_duration_seconds_bucket[5m]))",
            "legendFormat": "95%分位数"
          }
        ]
      },
      {
        "title": "队列长度",
        "type": "singlestat",
        "targets": [
          {
            "expr": "render_queue_length",
            "legendFormat": "待处理任务"
          }
        ]
      }
    ]
  }
}
```

### 4.3 日志收集配置
```yaml
# fluentd-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: logging
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/render-service-*.log
      pos_file /var/log/fluentd-render-service.log.pos
      tag kubernetes.render-service
      format json
    </source>
    
    <filter kubernetes.render-service>
      @type parser
      key_name log
      <parse>
        @type json
      </parse>
    </filter>
    
    <match kubernetes.render-service>
      @type elasticsearch
      host elasticsearch.logging.svc.cluster.local
      port 9200
      index_name render-service-logs
    </match>
```

## 5. 性能调优

### 5.1 JVM参数优化
```bash
# Node.js内存优化
export NODE_OPTIONS="--max-old-space-size=4096 --max-semi-space-size=256"

# 垃圾回收优化
export NODE_OPTIONS="$NODE_OPTIONS --expose-gc --optimize-for-size"
```

### 5.2 数据库优化
```sql
-- MySQL配置优化
[mysqld]
innodb_buffer_pool_size = 4G
innodb_log_file_size = 512M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 256M
max_connections = 1000
```

### 5.3 Redis优化
```conf
# redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

---

*本文档提供了DL引擎渲染服务的完整部署和运维指南，确保服务的高可用性和最佳性能。*
