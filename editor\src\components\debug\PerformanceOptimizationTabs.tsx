/**
 * 性能优化标签页组件
 * 集成所有性能优化相关面板
 */
import React, { useState } from 'react';
import { Tabs } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  DashboardOutlined,
  AppstoreOutlined,
  DesktopOutlined} from '@ant-design/icons';
import PerformanceOptimizationPanel from './PerformanceOptimizationPanel';
import ResourceOptimizationPanel from './ResourceOptimizationPanel';
import UIOptimizationPanel from './UIOptimizationPanel';
import './PerformanceOptimizationTabs.less';



/**
 * 性能优化标签页组件
 */
const PerformanceOptimizationTabs: React.FC = () => {
  const { t } = useTranslation();
  const [activeKey, setActiveKey] = useState('performance');
  
  const tabItems = [
    {
      key: 'performance',
      label: (
        <span>
          <DashboardOutlined />
          {t('debug.tabs.performance')}
        </span>
      ),
      children: <PerformanceOptimizationPanel />
    },
    {
      key: 'resources',
      label: (
        <span>
          <AppstoreOutlined />
          {t('debug.tabs.resources')}
        </span>
      ),
      children: <ResourceOptimizationPanel />
    },
    {
      key: 'ui',
      label: (
        <span>
          <DesktopOutlined />
          {t('debug.tabs.ui')}
        </span>
      ),
      children: <UIOptimizationPanel />
    }
  ];

  return (
    <div className="performance-optimization-tabs">
      <Tabs
        activeKey={activeKey}
        onChange={setActiveKey}
        items={tabItems}
      />
    </div>
  );
};

export default PerformanceOptimizationTabs;
