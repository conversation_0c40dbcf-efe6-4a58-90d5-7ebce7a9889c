/**
 * GPU蒙皮系统
 * 使用GPU加速骨骼动画计算
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
/**
 * GPU蒙皮配置
 */
export interface GPUSkinningConfig {
    /** 最大骨骼数量 */
    maxBones?: number;
    /** 是否使用计算着色器 */
    useComputeShader?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * GPU蒙皮组件
 */
export declare class GPUSkinningComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 骨骼网格 */
    private skinnedMesh;
    /** 骨骼 */
    private skeleton;
    /** 骨骼数量 */
    private boneCount;
    /** 骨骼矩阵纹理 */
    private boneTexture;
    /** 骨骼矩阵 */
    private boneMatrices;
    /** 是否启用 */
    protected enabled: boolean;
    /** 是否需要更新 */
    private needsUpdate;
    /** 自定义着色器材质 */
    private gpuMaterial;
    /** 原始材质 */
    private originalMaterial;
    /**
     * 创建GPU蒙皮组件
     */
    constructor();
    /**
     * 设置骨骼网格
     * @param mesh 骨骼网格
     */
    setSkinnedMesh(mesh: THREE.SkinnedMesh): void;
    /**
     * 获取骨骼网格
     * @returns 骨骼网格
     */
    getSkinnedMesh(): THREE.SkinnedMesh | null;
    /**
     * 获取骨骼
     * @returns 骨骼
     */
    getSkeleton(): THREE.Skeleton | null;
    /**
     * 获取骨骼数量
     * @returns 骨骼数量
     */
    getBoneCount(): number;
    /**
     * 是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 设置启用状态
     * @param enabled 启用状态
     */
    setEnabled(enabled: boolean): void;
    /**
     * 是否需要更新
     * @returns 是否需要更新
     */
    needsUpdateBones(): boolean;
    /**
     * 设置需要更新
     * @param needsUpdate 是否需要更新
     */
    setNeedsUpdate(needsUpdate: boolean): void;
    /**
     * 获取骨骼矩阵纹理
     * @returns 骨骼矩阵纹理
     */
    getBoneTexture(): THREE.DataTexture | null;
    /**
     * 获取GPU材质
     * @returns GPU材质
     */
    getGPUMaterial(): THREE.ShaderMaterial | null;
    /**
     * 设置GPU材质
     * @param material GPU材质
     */
    setGPUMaterial(material: THREE.ShaderMaterial): void;
    /**
     * 获取原始材质
     * @returns 原始材质
     */
    getOriginalMaterial(): THREE.Material | THREE.Material[] | null;
    /**
     * 更新骨骼矩阵
     */
    updateBoneMatrices(): void;
}
/**
 * GPU蒙皮系统
 */
export declare class GPUSkinningSystem extends System {
    /** 配置 */
    private config;
    /** 组件列表 */
    private components;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否支持GPU蒙皮 */
    private supportsGPUSkinning;
    /** 是否支持计算着色器 */
    private supportsComputeShader;
    /** 顶点着色器 */
    private vertexShader;
    /** 片段着色器 */
    private fragmentShader;
    /** 计算着色器 */
    private computeShader;
    /**
     * 创建GPU蒙皮系统
     * @param config 配置
     */
    constructor(config?: GPUSkinningConfig);
    /**
     * 检查是否支持GPU蒙皮
     */
    private checkGPUSkinningSupport;
    /**
     * 初始化着色器
     */
    private initShaders;
    /**
     * 创建GPU蒙皮组件
     * @param entity 实体
     * @param skinnedMesh 骨骼网格
     * @returns GPU蒙皮组件
     */
    createGPUSkinning(entity: Entity, skinnedMesh: THREE.SkinnedMesh): GPUSkinningComponent;
    /**
     * 初始化GPU蒙皮
     * @param component GPU蒙皮组件
     */
    private initGPUSkinning;
    /**
     * 移除GPU蒙皮组件
     * @param entity 实体
     * @returns 是否成功移除
     */
    removeGPUSkinning(entity: Entity): boolean;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
