import { Controller, Get, Post, Body, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BindingsService } from './bindings.service';

@ApiTags('bindings')
@Controller('digital-humans/:digitalHumanId/knowledge-bases')
export class BindingsController {
  constructor(private readonly bindingsService: BindingsService) {}

  @Post()
  @ApiOperation({ summary: '绑定知识库到数字人' })
  @ApiResponse({ status: 201, description: '绑定成功' })
  async bindKnowledgeBase(
    @Param('digitalHumanId') digitalHumanId: string,
    @Body() body: { knowledgeBaseId: string },
  ) {
    return this.bindingsService.bindKnowledgeBase(digitalHumanId, body.knowledgeBaseId);
  }

  @Get()
  @ApiOperation({ summary: '获取数字人绑定的知识库' })
  @ApiResponse({ status: 200, description: '获取绑定列表成功' })
  async getBindings(@Param('digitalHumanId') digitalHumanId: string) {
    return this.bindingsService.getBindings(digitalHumanId);
  }

  @Delete(':knowledgeBaseId')
  @ApiOperation({ summary: '解绑知识库' })
  @ApiResponse({ status: 200, description: '解绑成功' })
  async unbindKnowledgeBase(
    @Param('digitalHumanId') digitalHumanId: string,
    @Param('knowledgeBaseId') knowledgeBaseId: string,
  ) {
    return this.bindingsService.unbindKnowledgeBase(digitalHumanId, knowledgeBaseId);
  }
}
