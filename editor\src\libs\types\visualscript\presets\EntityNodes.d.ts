/**
 * 视觉脚本实体节点
 * 提供实体操作相关的节点
 */
import { Entity } from '../../core/Entity';
import { FlowNode } from '../nodes/FlowNode';
import { NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 注册组件类
 * @param name 组件类型名称
 * @param componentClass 组件类
 */
export declare function registerComponent(name: string, componentClass: any): void;
/**
 * 获取组件类
 * @param name 组件类型名称
 * @returns 组件类
 */
export declare function getComponentClass(name: string): any;
/**
 * 实体管理器接口
 */
interface EntityManager {
    getEntity(id: number): Entity | null;
    createEntity(): Entity;
    destroyEntity(entity: Entity): boolean;
}
/**
 * 设置实体管理器
 * @param manager 实体管理器
 */
export declare function setEntityManager(manager: EntityManager): void;
/**
 * 获取实体节点
 * 根据ID获取实体
 */
export declare class GetEntityNode extends FlowNode {
    /**
     * 构造函数
     * @param options 节点选项
     */
    constructor(options: NodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 获取组件节点
 * 获取实体上的组件
 */
export declare class GetComponentNode extends FlowNode {
    /**
     * 构造函数
     * @param options 节点选项
     */
    constructor(options: NodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 添加组件节点
 * 向实体添加组件
 */
export declare class AddComponentNode extends FlowNode {
    /**
     * 构造函数
     * @param options 节点选项
     */
    constructor(options: NodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 移除组件节点
 * 从实体移除组件
 */
export declare class RemoveComponentNode extends FlowNode {
    /**
     * 构造函数
     * @param options 节点选项
     */
    constructor(options: NodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 检查组件节点
 * 检查实体是否有指定组件
 */
export declare class HasComponentNode extends FlowNode {
    /**
     * 构造函数
     * @param options 节点选项
     */
    constructor(options: NodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 注册实体节点
 * @param registry 节点注册表
 */
export declare function registerEntityNodes(registry: NodeRegistry): void;
export {};
