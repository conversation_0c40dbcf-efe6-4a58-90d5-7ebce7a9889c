/**
 * 引擎核心类
 * 负责管理世界、系统和渲染循环
 */
import { World } from './World';
import { System } from './System';
import { Time } from '../utils/Time';
import { EventEmitter } from '../utils/EventEmitter';
import { RenderSystem } from '../rendering/RenderSystem';
import { Renderer } from '../rendering/Renderer';
import { Camera } from '../rendering/Camera';
import { AssetManager } from '../assets/AssetManager';
import { I18n } from '../i18n/I18n';

export interface EngineOptions {
  /** 画布元素或ID */
  canvas?: HTMLCanvasElement | string;
  /** 是否自动开始渲染循环 */
  autoStart?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 默认语言 */
  language?: string;
}

export class Engine extends EventEmitter {
  /** 世界实例 */
  private world: World;

  /** 系统列表 */
  private systems: System[] = [];

  /** 渲染器 */
  private renderer: Renderer;

  /** 活跃相机 */
  private activeCamera: Camera | null = null;

  /** 资产管理器 */
  private assetManager: AssetManager;

  /** 国际化实例 */
  private i18n: I18n;

  /** 是否正在运行 */
  private running: boolean = false;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否处于调试模式 */
  private debug: boolean;

  /** 动画帧ID */
  private animationFrameId: number = 0;

  /** 上一帧时间戳 */
  private lastFrameTime: number = 0;

  /** 固定更新累积时间 */
  private fixedUpdateAccumulator: number = 0;

  /** 固定更新时间步长（秒） */
  private fixedUpdateTimeStep: number = 1 / 60;

  /**
   * 创建引擎实例
   * @param options 引擎选项
   */
  constructor(options: EngineOptions = {}) {
    super();

    // 创建世界
    this.world = new World(this);

    // 创建渲染器
    this.renderer = new Renderer({
      canvas: options.canvas,
    });

    // 创建资产管理器
    this.assetManager = new AssetManager();

    // 创建国际化实例
    this.i18n = new I18n({
      language: options.language || 'zh-CN',
    });

    // 设置调试模式
    this.debug = options.debug || false;

    // 如果设置了自动开始，则初始化并开始渲染循环
    if (options.autoStart) {
      this.initialize();
      this.start();
    }
  }

  /**
   * 初始化引擎
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 初始化时间
    Time.initialize();

    // 初始化资产管理器
    this.assetManager.initialize();

    // 添加默认系统
    this.addSystem(new RenderSystem(this.renderer));

    // 初始化所有系统
    for (const system of this.systems) {
      system.initialize();
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 开始渲染循环
   */
  public start(): void {
    if (this.running) {
      return;
    }

    // 确保引擎已初始化
    if (!this.initialized) {
      this.initialize();
    }

    this.running = true;
    this.lastFrameTime = performance.now();
    this.animationFrameId = requestAnimationFrame(this.update.bind(this));

    this.emit('started');
  }

  /**
   * 停止渲染循环
   */
  public stop(): void {
    if (!this.running) {
      return;
    }

    this.running = false;
    cancelAnimationFrame(this.animationFrameId);

    this.emit('stopped');
  }

  /**
   * 更新循环
   * @param timestamp 当前时间戳
   */
  private update(timestamp: number): void {
    if (!this.running) {
      return;
    }

    // 计算帧间隔时间
    const deltaTime = (timestamp - this.lastFrameTime) / 1000;
    this.lastFrameTime = timestamp;

    // 更新时间
    Time.update(deltaTime);

    // 更新世界
    this.world.update(deltaTime);

    // 固定时间步长更新
    this.fixedUpdateAccumulator += deltaTime;
    while (this.fixedUpdateAccumulator >= this.fixedUpdateTimeStep) {
      // 固定更新世界
      this.world.fixedUpdate(this.fixedUpdateTimeStep);

      // 固定更新系统
      for (const system of this.systems) {
        system.fixedUpdate(this.fixedUpdateTimeStep);
      }

      this.fixedUpdateAccumulator -= this.fixedUpdateTimeStep;
    }

    // 更新系统
    for (const system of this.systems) {
      system.update(deltaTime);
    }

    // 后更新系统
    for (const system of this.systems) {
      system.lateUpdate(deltaTime);
    }

    // 发出更新事件
    this.emit('update', deltaTime);

    // 继续渲染循环
    this.animationFrameId = requestAnimationFrame(this.update.bind(this));
  }

  /**
   * 添加系统
   * @param system 系统实例
   * @returns 添加的系统
   */
  public addSystem<T extends System>(system: T): T {
    // 设置系统的引擎引用
    system.setEngine(this);

    // 添加到系统列表
    this.systems.push(system);

    // 如果引擎已初始化，则初始化系统
    if (this.initialized) {
      system.initialize();
    }

    // 按优先级排序系统
    this.systems.sort((a, b) => a.getPriority() - b.getPriority());

    return system;
  }

  /**
   * 获取系统
   * @param type 系统类型
   * @returns 系统实例，如果不存在则返回null
   */
  public getSystem<T extends System>(type: string): T | null {
    for (const system of this.systems) {
      if (system.getType() === type) {
        return system as T;
      }
    }

    return null;
  }

  /**
   * 移除系统
   * @param system 系统实例或类型
   * @returns 是否成功移除
   */
  public removeSystem(system: System | string): boolean {
    const index = typeof system === 'string'
      ? this.systems.findIndex(s => s.getType() === system)
      : this.systems.indexOf(system);

    if (index !== -1) {
      const removedSystem = this.systems[index];
      (removedSystem as any).dispose();
      this.systems.splice(index, 1);
      return true;
    }

    return false;
  }

  /**
   * 获取世界
   * @returns 世界实例
   */
  public getWorld(): World {
    return this.world;
  }

  /**
   * 获取渲染器
   * @returns 渲染器实例
   */
  public getRenderer(): Renderer {
    return this.renderer;
  }

  /**
   * 设置活跃相机
   * @param camera 相机实例
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 获取活跃相机
   * @returns 活跃相机实例
   */
  public getActiveCamera(): Camera | null {
    return this.activeCamera;
  }

  /**
   * 获取资产管理器
   * @returns 资产管理器实例
   */
  public getAssetManager(): AssetManager {
    return this.assetManager;
  }

  /**
   * 获取国际化实例
   * @returns 国际化实例
   */
  public getI18n(): I18n {
    return this.i18n;
  }

  /**
   * 是否处于调试模式
   * @returns 是否处于调试模式
   */
  public isDebug(): boolean {
    return this.debug;
  }

  /**
   * 设置调试模式
   * @param debug 是否启用调试模式
   */
  public setDebug(debug: boolean): void {
    this.debug = debug;
  }

  /**
   * 是否正在运行
   * @returns 是否正在运行
   */
  public isRunning(): boolean {
    return this.running;
  }

  /**
   * 销毁引擎
   */
  public dispose(): void {
    // 停止渲染循环
    this.stop();

    // 销毁所有系统
    for (const system of this.systems) {
      (system as any).dispose();
    }
    this.systems = [];

    // 销毁世界
    (this.world as any).dispose();

    // 销毁渲染器
    (this.renderer as any).dispose();

    // 销毁资产管理器
    (this.assetManager as any).dispose();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
