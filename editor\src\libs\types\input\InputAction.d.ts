/**
 * 输入动作
 * 表示一个可以被触发的输入动作
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 输入动作类型
 */
export declare enum InputActionType {
    /** 按钮动作 */
    BUTTON = "button",
    /** 值动作 */
    VALUE = "value",
    /** 向量动作 */
    VECTOR = "vector"
}
/**
 * 输入动作接口
 */
export interface InputAction {
    /**
     * 获取动作名称
     * @returns 动作名称
     */
    getName(): string;
    /**
     * 获取动作类型
     * @returns 动作类型
     */
    getType(): InputActionType;
    /**
     * 更新动作状态
     * @param value 动作值
     */
    update(value: any): void;
    /**
     * 检查动作状态是否发生变化
     * @returns 是否发生变化
     */
    hasChanged(): boolean;
    /**
     * 获取动作值
     * @returns 动作值
     */
    getValue(): any;
    /**
     * 重置动作状态
     */
    reset(): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
}
/**
 * 输入动作基类
 */
export declare abstract class BaseInputAction implements InputAction {
    /** 动作名称 */
    protected name: string;
    /** 动作类型 */
    protected type: InputActionType;
    /** 动作值 */
    protected value: any;
    /** 上一次动作值 */
    protected previousValue: any;
    /** 是否发生变化 */
    protected changed: boolean;
    /** 事件发射器 */
    protected eventEmitter: EventEmitter;
    /**
     * 创建输入动作
     * @param name 动作名称
     * @param type 动作类型
     */
    constructor(name: string, type: InputActionType);
    /**
     * 获取动作名称
     * @returns 动作名称
     */
    getName(): string;
    /**
     * 获取动作类型
     * @returns 动作类型
     */
    getType(): InputActionType;
    /**
     * 更新动作状态
     * @param value 动作值
     */
    update(value: any): void;
    /**
     * 检查动作状态是否发生变化
     * @returns 是否发生变化
     */
    hasChanged(): boolean;
    /**
     * 获取动作值
     * @returns 动作值
     */
    getValue(): any;
    /**
     * 重置动作状态
     */
    reset(): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
}
/**
 * 按钮动作
 */
export declare class ButtonInputAction extends BaseInputAction {
    /** 是否按下 */
    private pressed;
    /** 是否刚按下 */
    private justPressed;
    /** 是否刚释放 */
    private justReleased;
    /**
     * 创建按钮动作
     * @param name 动作名称
     */
    constructor(name: string);
    /**
     * 更新动作状态
     * @param value 动作值
     */
    update(value: boolean): void;
    /**
     * 检查按钮是否按下
     * @returns 是否按下
     */
    isPressed(): boolean;
    /**
     * 检查按钮是否刚按下
     * @returns 是否刚按下
     */
    isJustPressed(): boolean;
    /**
     * 检查按钮是否刚释放
     * @returns 是否刚释放
     */
    isJustReleased(): boolean;
    /**
     * 重置动作状态
     */
    reset(): void;
}
/**
 * 值动作
 */
export declare class ValueInputAction extends BaseInputAction {
    /**
     * 创建值动作
     * @param name 动作名称
     */
    constructor(name: string);
}
/**
 * 向量动作
 */
export declare class VectorInputAction extends BaseInputAction {
    /**
     * 创建向量动作
     * @param name 动作名称
     */
    constructor(name: string);
    /**
     * 更新动作状态
     * @param value 动作值
     */
    update(value: [number, number]): void;
    /**
     * 获取X轴值
     * @returns X轴值
     */
    getX(): number;
    /**
     * 获取Y轴值
     * @returns Y轴值
     */
    getY(): number;
    /**
     * 获取向量长度
     * @returns 向量长度
     */
    getLength(): number;
    /**
     * 获取向量方向
     * @returns 向量方向（弧度）
     */
    getDirection(): number;
}
