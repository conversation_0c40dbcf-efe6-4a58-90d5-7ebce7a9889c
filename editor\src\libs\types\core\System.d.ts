/**
 * 系统基类
 * 负责处理特定类型的组件和功能
 */
import { Engine } from './Engine';
import { World } from './World';
import { EventEmitter } from '../utils/EventEmitter';
export declare abstract class System extends EventEmitter {
    /** 系统类型 */
    private type;
    /** 引擎引用 */
    protected engine: Engine | null;
    /** 世界引用 */
    protected world: World | null;
    /** 优先级（数字越小优先级越高） */
    private priority;
    /** 是否启用 */
    private enabled;
    /**
     * 创建系统实例
     * @param priority 优先级（数字越小优先级越高）
     */
    constructor(priority?: number);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 设置引擎引用
     * @param engine 引擎实例
     */
    setEngine(engine: Engine): void;
    /**
     * 获取引擎引用
     * @returns 引擎实例
     */
    getEngine(): Engine | null;
    /**
     * 设置世界引用
     * @param world 世界实例
     */
    setWorld(world: World): void;
    /**
     * 获取世界引用
     * @returns 世界实例
     */
    getWorld(): World | null;
    /**
     * 获取优先级
     * @returns 优先级
     */
    getPriority(): number;
    /**
     * 设置优先级
     * @param priority 优先级
     */
    setPriority(priority: number): void;
    /**
     * 设置启用状态
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 当系统启用时调用
     */
    protected onEnable(): void;
    /**
     * 当系统禁用时调用
     */
    protected onDisable(): void;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 固定时间步长更新
     * @param _fixedDeltaTime 固定帧间隔时间（秒）
     */
    fixedUpdate(_fixedDeltaTime: number): void;
    /**
     * 后更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    lateUpdate(_deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
