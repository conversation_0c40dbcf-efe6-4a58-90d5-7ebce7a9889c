/**
 * 动作捕捉姿势组件
 * 用于存储和管理动作捕捉姿势状态
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
/**
 * 动作捕捉姿势类型
 */
export declare enum MotionCapturePoseType {
    /** 站立 */
    STANDING = "standing",
    /** 坐姿 */
    SITTING = "sitting",
    /** T姿势 */
    T_POSE = "tpose",
    /** A姿势 */
    A_POSE = "apose",
    /** 行走 */
    WALKING = "walking",
    /** 跑步 */
    RUNNING = "running",
    /** 跳跃 */
    JUMPING = "jumping",
    /** 蹲下 */
    CROUCHING = "crouching",
    /** 趴下 */
    PRONE = "prone",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 动作捕捉姿势状态
 */
export interface MotionCapturePoseState {
    /** 是否开始 */
    begun: boolean;
    /** 持续时间（秒） */
    duration?: number;
    /** 强度（0-1） */
    intensity?: number;
    /** 置信度（0-1） */
    confidence?: number;
}
/**
 * 动作捕捉姿势组件配置
 */
export interface MotionCapturePoseComponentConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 姿势持续阈值（秒） */
    poseHoldThreshold?: number;
    /** 姿势角度阈值（弧度） */
    poseAngleThreshold?: number;
}
/**
 * 动作捕捉姿势组件
 * 存储和管理动作捕捉姿势状态
 */
export declare class MotionCapturePoseComponent extends Component {
    /** 组件类型 */
    static readonly TYPE = "MotionCapturePoseComponent";
    /** 姿势持续阈值（秒） */
    poseHoldThreshold: number;
    /** 姿势角度阈值（弧度） */
    poseAngleThreshold: number;
    /** 当前姿势类型 */
    currentPose: MotionCapturePoseType;
    /** 站立姿势状态 */
    standing?: MotionCapturePoseState;
    /** 坐姿状态 */
    sitting?: MotionCapturePoseState;
    /** T姿势状态 */
    tpose?: MotionCapturePoseState;
    /** A姿势状态 */
    apose?: MotionCapturePoseState;
    /** 行走状态 */
    walking?: MotionCapturePoseState;
    /** 跑步状态 */
    running?: MotionCapturePoseState;
    /** 跳跃状态 */
    jumping?: MotionCapturePoseState;
    /** 蹲下状态 */
    crouching?: MotionCapturePoseState;
    /** 趴下状态 */
    prone?: MotionCapturePoseState;
    /** 自定义姿势状态 */
    custom?: MotionCapturePoseState;
    /** 姿势持续计时器 */
    private poseHoldTimer;
    /** 上一帧姿势类型 */
    private previousPose;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 组件配置
     */
    constructor(entity: Entity, config?: MotionCapturePoseComponentConfig);
    /**
     * 初始化姿势状态
     */
    private initPoseStates;
    /**
     * 设置姿势状态
     * @param poseType 姿势类型
     * @param state 姿势状态
     */
    setPoseState(poseType: MotionCapturePoseType, state: Partial<MotionCapturePoseState>): void;
    /**
     * 获取姿势状态
     * @param poseType 姿势类型
     * @returns 姿势状态
     */
    getPoseState(poseType: MotionCapturePoseType): MotionCapturePoseState | undefined;
    /**
     * 更新姿势持续时间
     * @param deltaTime 时间增量（秒）
     */
    updatePoseDurations(deltaTime: number): void;
    /**
     * 检查姿势是否持续足够长时间
     * @param poseType 姿势类型
     * @returns 是否持续足够长时间
     */
    isPoseHeld(poseType: MotionCapturePoseType): boolean;
    /**
     * 重置所有姿势状态
     */
    resetAllPoses(): void;
    /**
     * 克隆组件
     * @param entity 目标实体
     * @returns 克隆的组件
     */
    clone(entity: Entity): MotionCapturePoseComponent;
}
