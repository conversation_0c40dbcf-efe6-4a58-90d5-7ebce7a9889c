/**
 * 实例化组件
 * 用于为实体添加实例化渲染功能
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import type { Transform } from '../../scene/Transform';
import { InstancedRenderingSystem } from './InstancedRenderingSystem';

/**
 * 实例数据接口
 */
export interface InstanceData {
  /** 位置 */
  position: THREE.Vector3;
  /** 旋转（四元数） */
  quaternion: THREE.Quaternion;
  /** 缩放 */
  scale: THREE.Vector3;
  /** 颜色 */
  color?: THREE.Color;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 实例化组件选项接口
 */
export interface InstancedComponentOptions {
  /** 原始网格 */
  originalMesh?: THREE.Mesh;
  /** 最大实例数量 */
  maxInstanceCount?: number;
  /** 是否使用颜色 */
  useColor?: boolean;
  /** 是否动态更新 */
  dynamic?: boolean;
  /** 是否可见 */
  visible?: boolean;
}

/**
 * 实例化组件类
 */
export class InstancedComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'InstancedComponent';

  /** 原始网格 */
  private originalMesh: THREE.Mesh | null = null;

  /** 实例化网格 */
  private instancedMesh: THREE.InstancedMesh | null = null;

  /** 最大实例数量 */
  private maxInstanceCount: number;

  /** 是否使用颜色 */
  private useColor: boolean;

  /** 是否动态更新 */
  private dynamic: boolean;

  /** 是否可见 */
  private visible: boolean;

  /** 实例列表 */
  private instances: InstanceData[] = [];

  /** 实例ID到索引的映射 */
  private instanceIdToIndex: Map<number, number> = new Map();

  /** 下一个实例ID */
  private nextInstanceId: number = 0;

  /** 是否已更新 */
  private updated: boolean = false;

  /** 临时矩阵 */
  private tempMatrix: THREE.Matrix4 = new THREE.Matrix4();

  /**
   * 创建实例化组件
   * @param options 实例化组件选项
   */
  constructor(options: InstancedComponentOptions = {}) {
    super(InstancedComponent.type);

    this.originalMesh = options.originalMesh || null;
    this.maxInstanceCount = options.maxInstanceCount !== undefined ? options.maxInstanceCount : 1000;
    this.useColor = options.useColor !== undefined ? options.useColor : true;
    this.dynamic = options.dynamic !== undefined ? options.dynamic : true;
    this.visible = options.visible !== undefined ? options.visible : true;
  }

  /**
   * 当组件附加到实体时调用
   */
  protected onAttach(): void {
    if (!this.entity) return;

    // 如果没有原始网格，则尝试从实体获取
    if (!this.originalMesh) {
      const transform = this.entity.getComponent('Transform') as any as any as any as Transform;
      if (transform) {
        const object = transform.getObject3D();
        object.traverse((child) => {
          if (child instanceof THREE.Mesh && !this.originalMesh) {
            this.originalMesh = child;
          }
        });
      }
    }

    // 通知系统
    if (this.entity.getWorld()) {
      const system = this.entity.getWorld().getSystem<InstancedRenderingSystem>(InstancedRenderingSystem);
      if (system) {
        system.registerInstancedComponent(this.entity, this);
      }
    }
  }

  /**
   * 当组件从实体分离时调用
   */
  protected onDetach(): void {
    // 通知系统
    if (this.entity && this.entity.getWorld()) {
      const system = this.entity.getWorld().getSystem<InstancedRenderingSystem>(InstancedRenderingSystem);
      if (system) {
        system.unregisterInstancedComponent(this.entity);
      }
    }

    // 清理资源
    this.instancedMesh = null;
    this.instances = [];
    this.instanceIdToIndex.clear();
    this.nextInstanceId = 0;
  }

  /**
   * 设置原始网格
   * @param mesh 网格
   */
  public setOriginalMesh(mesh: THREE.Mesh): void {
    this.originalMesh = mesh;
    this.updated = true;

    // 通知系统
    if (this.entity && this.entity.getWorld()) {
      const system = this.entity.getWorld().getSystem<InstancedRenderingSystem>(InstancedRenderingSystem);
      if (system) {
        system.registerInstancedComponent(this.entity, this);
      }
    }
  }

  /**
   * 获取原始网格
   * @returns 网格
   */
  public getOriginalMesh(): THREE.Mesh | null {
    return this.originalMesh;
  }

  /**
   * 设置实例化网格
   * @param mesh 实例化网格
   */
  public setInstancedMesh(mesh: THREE.InstancedMesh | null): void {
    this.instancedMesh = mesh;
  }

  /**
   * 获取实例化网格
   * @returns 实例化网格
   */
  public getInstancedMesh(): THREE.InstancedMesh | null {
    return this.instancedMesh;
  }

  /**
   * 设置最大实例数量
   * @param count 数量
   */
  public setMaxInstanceCount(count: number): void {
    this.maxInstanceCount = count;
    this.updated = true;

    // 通知系统
    if (this.entity && this.entity.getWorld()) {
      const system = this.entity.getWorld().getSystem<InstancedRenderingSystem>(InstancedRenderingSystem);
      if (system) {
        system.registerInstancedComponent(this.entity, this);
      }
    }
  }

  /**
   * 获取最大实例数量
   * @returns 数量
   */
  public getMaxInstanceCount(): number {
    return this.maxInstanceCount;
  }

  /**
   * 设置是否使用颜色
   * @param use 是否使用
   */
  public setUseColor(use: boolean): void {
    this.useColor = use;
    this.updated = true;
  }

  /**
   * 获取是否使用颜色
   * @returns 是否使用
   */
  public isUseColor(): boolean {
    return this.useColor;
  }

  /**
   * 设置是否动态更新
   * @param dynamic 是否动态
   */
  public setDynamic(dynamic: boolean): void {
    this.dynamic = dynamic;
  }

  /**
   * 获取是否动态更新
   * @returns 是否动态
   */
  public isDynamic(): boolean {
    return this.dynamic;
  }

  /**
   * 设置是否可见
   * @param visible 是否可见
   */
  public setVisible(visible: boolean): void {
    this.visible = visible;

    // 更新实例化网格的可见性
    if (this.instancedMesh) {
      this.instancedMesh.visible = visible;
    }
  }

  /**
   * 获取是否可见
   * @returns 是否可见
   */
  public isVisible(): boolean {
    return this.visible;
  }

  /**
   * 添加实例
   * @param instance 实例数据
   * @returns 实例ID
   */
  public addInstance(instance: InstanceData): number {
    // 如果实例数量已达到最大值，则返回-1
    if (this.instances.length >= this.maxInstanceCount) {
      return -1;
    }

    // 创建实例ID
    const instanceId = this.nextInstanceId++;

    // 添加实例
    this.instances.push(instance);
    this.instanceIdToIndex.set(instanceId, this.instances.length - 1);

    // 标记为已更新
    this.updated = true;

    return instanceId;
  }

  /**
   * 移除实例
   * @param instanceId 实例ID
   * @returns 是否成功
   */
  public removeInstance(instanceId: number): boolean {
    // 获取实例索引
    const index = this.instanceIdToIndex.get(instanceId);
    if (index === undefined) {
      return false;
    }

    // 移除实例
    this.instances.splice(index, 1);
    this.instanceIdToIndex.delete(instanceId);

    // 更新索引映射
    for (const [id, idx] of Array.from(this.instanceIdToIndex.entries())) {
      if (idx > index) {
        this.instanceIdToIndex.set(id, idx - 1);
      }
    }

    // 标记为已更新
    this.updated = true;

    return true;
  }

  /**
   * 更新实例
   * @param instanceId 实例ID
   * @param instance 实例数据
   * @returns 是否成功
   */
  public updateInstance(instanceId: number, instance: Partial<InstanceData>): boolean {
    // 获取实例索引
    const index = this.instanceIdToIndex.get(instanceId);
    if (index === undefined) {
      return false;
    }

    // 获取实例
    const currentInstance = this.instances[index];

    // 更新实例
    if (instance.position) {
      currentInstance.position.copy(instance.position);
    }

    if (instance.quaternion) {
      currentInstance.quaternion.copy(instance.quaternion);
    }

    if (instance.scale) {
      currentInstance.scale.copy(instance.scale);
    }

    if (instance.color && this.useColor) {
      if (!currentInstance.color) {
        currentInstance.color = new THREE.Color();
      }
      currentInstance.color.copy(instance.color);
    }

    if (instance.userData) {
      currentInstance.userData = { ...currentInstance.userData, ...instance.userData };
    }

    // 标记为已更新
    this.updated = true;

    return true;
  }

  /**
   * 获取实例
   * @param instanceId 实例ID
   * @returns 实例数据
   */
  public getInstance(instanceId: number): InstanceData | null {
    // 获取实例索引
    const index = this.instanceIdToIndex.get(instanceId);
    if (index === undefined) {
      return null;
    }

    // 返回实例的副本
    const instance = this.instances[index];
    return {
      position: instance.position.clone(),
      quaternion: instance.quaternion.clone(),
      scale: instance.scale.clone(),
      color: instance.color ? instance.color.clone() : undefined,
      userData: instance.userData ? { ...instance.userData } : undefined
    };
  }

  /**
   * 获取所有实例
   * @returns 实例列表
   */
  public getInstances(): InstanceData[] {
    return this.instances;
  }

  /**
   * 获取实例数量
   * @returns 实例数量
   */
  public getInstanceCount(): number {
    return this.instances.length;
  }

  /**
   * 清空实例
   */
  public clearInstances(): void {
    this.instances = [];
    this.instanceIdToIndex.clear();
    this.nextInstanceId = 0;
    this.updated = true;
  }

  /**
   * 设置是否已更新
   * @param updated 是否已更新
   */
  public setUpdated(updated: boolean): void {
    this.updated = updated;
  }

  /**
   * 获取是否已更新
   * @returns 是否已更新
   */
  public isUpdated(): boolean {
    return this.updated;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 如果不是动态更新，则不更新
    if (!this.dynamic) {
      return;
    }

    // 如果没有实例化网格，则不更新
    if (!this.instancedMesh) {
      return;
    }

    // 如果没有更新，则不更新
    if (!this.updated) {
      return;
    }

    // 更新实例化网格
    const instanceCount = this.instances.length;
    this.instancedMesh.count = instanceCount;

    for (let i = 0; i < instanceCount; i++) {
      const instance = this.instances[i];

      // 更新矩阵
      this.tempMatrix.compose(
        instance.position,
        instance.quaternion,
        instance.scale
      );
      this.instancedMesh.setMatrixAt(i, this.tempMatrix);

      // 更新颜色
      if (this.useColor && instance.color) {
        this.instancedMesh.setColorAt(i, instance.color);
      }
    }

    // 更新实例化网格
    if (this.instancedMesh.instanceMatrix) {
      this.instancedMesh.instanceMatrix.needsUpdate = true;
    }

    if (this.useColor && this.instancedMesh.instanceColor) {
      this.instancedMesh.instanceColor.needsUpdate = true;
    }

    // 重置更新标志
    this.updated = false;
  }
}
