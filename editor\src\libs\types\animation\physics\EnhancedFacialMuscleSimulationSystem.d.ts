/**
 * 增强版面部肌肉模拟系统
 * 支持更多的物理驱动功能
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { FacialMuscleSimulationComponent, FacialMuscleSimulationConfig } from './FacialMuscleSimulation';
import { FacialExpressionType } from '../FacialAnimation';
/**
 * 软体类型
 */
export declare enum SoftBodyType {
    /** 布料 */
    CLOTH = "cloth",
    /** 绳索 */
    ROPE = "rope",
    /** 可变形物体 */
    DEFORMABLE = "deformable",
    /** 气球 */
    BALLOON = "balloon",
    /** 果冻 */
    JELLY = "jelly"
}
/**
 * 软体配置
 */
export interface SoftBodyConfig {
    /** 软体类型 */
    type: SoftBodyType;
    /** 软体名称 */
    name: string;
    /** 位置 */
    position: THREE.Vector3;
    /** 尺寸 */
    size: THREE.Vector3;
    /** 质量 */
    mass?: number;
    /** 弹性 */
    stiffness?: number;
    /** 阻尼 */
    damping?: number;
    /** 压力（仅适用于气球类型） */
    pressure?: number;
    /** 分辨率 */
    resolution?: THREE.Vector2;
    /** 是否固定边缘 */
    fixedEdges?: boolean;
    /** 是否使用重力 */
    useGravity?: boolean;
    /** 是否可撕裂（仅适用于布料和可变形物体） */
    tearable?: boolean;
    /** 撕裂阈值 */
    tearThreshold?: number;
}
/**
 * 增强版面部肌肉模拟系统配置
 */
export interface EnhancedFacialMuscleSimulationSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 重力 */
    gravity?: THREE.Vector3;
    /** 迭代次数 */
    iterations?: number;
    /** 是否使用软体 */
    useSoftBodies?: boolean;
    /** 是否使用GPU加速 */
    useGPU?: boolean;
    /** 是否使用自适应时间步长 */
    useAdaptiveTimeStep?: boolean;
    /** 最小时间步长 */
    minTimeStep?: number;
    /** 最大时间步长 */
    maxTimeStep?: number;
    /** 是否使用碰撞检测 */
    useCollisionDetection?: boolean;
    /** 是否使用连续碰撞检测 */
    useContinuousCollisionDetection?: boolean;
    /** 是否使用睡眠状态 */
    useSleepState?: boolean;
    /** 是否使用约束求解器 */
    useConstraintSolver?: boolean;
    /** 是否使用并行计算 */
    useParallelComputing?: boolean;
    /** 是否使用物理材质 */
    usePhysicsMaterials?: boolean;
    /** 是否使用物理调试渲染 */
    usePhysicsDebugRenderer?: boolean;
}
/**
 * 增强版面部肌肉模拟系统
 */
export declare class EnhancedFacialMuscleSimulationSystem extends System {
    /** 系统类型 */
    static readonly type = "EnhancedFacialMuscleSimulation";
    /** 面部肌肉模拟组件 */
    private components;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 物理引擎 */
    private physicsEngine;
    /** 事件发射器 */
    private eventEmitter;
    /** 软体对象 */
    private softBodies;
    /** 物理调试渲染器 */
    private physicsDebugRenderer;
    /** 累积时间 */
    private accumulatedTime;
    /** 固定时间步长 */
    private fixedTimeStep;
    /** 是否使用GPU加速 */
    private useGPU;
    /** 是否使用自适应时间步长 */
    private useAdaptiveTimeStep;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<EnhancedFacialMuscleSimulationSystemConfig>);
    /**
     * 创建物理调试渲染器
     */
    private createPhysicsDebugRenderer;
    /**
     * 创建面部肌肉模拟组件
     * @param entity 实体
     * @param config 配置
     * @returns 面部肌肉模拟组件
     */
    createFacialMuscleSimulation(entity: Entity, config?: Partial<FacialMuscleSimulationConfig>): FacialMuscleSimulationComponent;
    /**
     * 移除面部肌肉模拟组件
     * @param entity 实体
     */
    removeFacialMuscleSimulation(entity: Entity): void;
    /**
     * 获取面部肌肉模拟组件
     * @param entity 实体
     * @returns 面部肌肉模拟组件，如果不存在则返回null
     */
    getFacialMuscleSimulation(entity: Entity): FacialMuscleSimulationComponent | null;
    /**
     * 创建软体
     * @param entity 实体
     * @param config 软体配置
     * @returns 软体ID
     */
    createSoftBody(entity: Entity, config: SoftBodyConfig): string;
    /**
     * 创建布料软体
     * @param entity 实体
     * @param config 软体配置
     * @param softBodyId 软体ID
     */
    private createClothSoftBody;
    /**
     * 创建绳索软体
     * @param entity 实体
     * @param config 软体配置
     * @param softBodyId 软体ID
     */
    private createRopeSoftBody;
    /**
     * 创建可变形软体
     * @param entity 实体
     * @param config 软体配置
     * @param softBodyId 软体ID
     */
    private createDeformableSoftBody;
    /**
     * 创建气球软体
     * @param entity 实体
     * @param config 软体配置
     * @param softBodyId 软体ID
     */
    private createBalloonSoftBody;
    /**
     * 创建果冻软体
     * @param entity 实体
     * @param config 软体配置
     * @param softBodyId 软体ID
     */
    private createJellySoftBody;
    /**
     * 移除软体
     * @param softBodyId 软体ID
     * @returns 是否成功移除
     */
    removeSoftBody(softBodyId: string): boolean;
    /**
     * 获取软体
     * @param softBodyId 软体ID
     * @returns 软体对象
     */
    getSoftBody(softBodyId: string): any;
    /**
     * 应用力到软体
     * @param softBodyId 软体ID
     * @param force 力
     * @param position 位置（可选，默认为软体中心）
     * @param radius 影响半径（可选，默认为整个软体）
     * @returns 是否成功应用
     */
    applySoftBodyForce(softBodyId: string, force: THREE.Vector3, position?: THREE.Vector3, radius?: number): boolean;
    /**
     * 应用表情到软体
     * @param entity 实体
     * @param expression 表情类型
     * @param weight 权重
     * @returns 是否成功应用
     */
    applySoftBodyExpression(entity: Entity, expression: FacialExpressionType, weight: number): boolean;
    /**
     * 应用开心表情到软体
     * @param softBodies 软体数组
     * @param weight 权重
     */
    private applySoftBodyHappyExpression;
    /**
     * 应用悲伤表情到软体
     * @param softBodies 软体数组
     * @param weight 权重
     */
    private applySoftBodySadExpression;
    /**
     * 应用愤怒表情到软体
     * @param softBodies 软体数组
     * @param weight 权重
     */
    private applySoftBodyAngryExpression;
    /**
     * 应用惊讶表情到软体
     * @param softBodies 软体数组
     * @param weight 权重
     */
    private applySoftBodySurprisedExpression;
    /**
     * 应用恐惧表情到软体
     * @param softBodies 软体数组
     * @param weight 权重
     */
    private applySoftBodyFearfulExpression;
    /**
     * 应用厌恶表情到软体
     * @param softBodies 软体数组
     * @param weight 权重
     */
    private applySoftBodyDisgustedExpression;
    /**
     * 应用蔑视表情到软体
     * @param softBodies 软体数组
     * @param weight 权重
     */
    private applySoftBodyContemptExpression;
    /**
     * 重置软体
     * @param softBodies 软体数组
     */
    private resetSoftBodies;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
