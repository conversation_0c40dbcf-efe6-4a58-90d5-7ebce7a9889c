/**
 * 时间相关的可视化脚本节点
 */
import { VisualScriptNode } from '../VisualScriptNode';
/**
 * 获取当前时间节点
 */
export declare class GetTimeNode extends VisualScriptNode {
    constructor();
    execute(): any;
}
/**
 * 延迟节点
 */
export declare class DelayNode extends VisualScriptNode {
    private startTime;
    private isWaiting;
    constructor();
    execute(inputs: any): any;
}
/**
 * 计时器节点
 */
export declare class TimerNode extends VisualScriptNode {
    private startTime;
    private isRunning;
    constructor();
    execute(inputs: any): any;
}
/**
 * 注册时间节点
 */
export declare function registerTimeNodes(): void;
