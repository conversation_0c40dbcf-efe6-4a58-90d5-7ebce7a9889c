/**
 * 面部动画模型适配器
 * 用于将面部动画系统与具体的3D模型绑定
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { FacialExpressionType, VisemeType } from '../FacialAnimation';
/**
 * 面部动画模型类型
 */
export declare enum FacialAnimationModelType {
    /** 通用模型 */
    GENERIC = "generic",
    /** VRM模型 */
    VRM = "vrm",
    /** FBX模型 */
    FBX = "fbx",
    /** GLTF模型 */
    GLTF = "gltf"
}
/**
 * 表情映射
 */
export interface ExpressionMapping {
    /** 表情类型 */
    expression: FacialExpressionType;
    /** 混合形状名称 */
    blendShapeName: string;
    /** 混合形状索引 */
    blendShapeIndex?: number;
    /** 权重缩放 */
    weightScale?: number;
    /** 权重偏移 */
    weightOffset?: number;
}
/**
 * 口型映射
 */
export interface VisemeMapping {
    /** 口型类型 */
    viseme: VisemeType;
    /** 混合形状名称 */
    blendShapeName: string;
    /** 混合形状索引 */
    blendShapeIndex?: number;
    /** 权重缩放 */
    weightScale?: number;
    /** 权重偏移 */
    weightOffset?: number;
}
/**
 * 面部动画模型适配器配置
 */
export interface FacialAnimationModelAdapterConfig {
    /** 模型类型 */
    modelType?: FacialAnimationModelType;
    /** 表情映射 */
    expressionMappings?: ExpressionMapping[];
    /** 口型映射 */
    visemeMappings?: VisemeMapping[];
    /** 是否自动检测混合形状 */
    autoDetectBlendShapes?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 面部动画模型适配器组件
 */
export declare class FacialAnimationModelAdapterComponent extends Component {
    /** 组件类型 */
    static readonly type = "FacialAnimationModelAdapter";
    /** 模型类型 */
    private modelType;
    /** 表情映射 */
    private expressionMappings;
    /** 口型映射 */
    private visemeMappings;
    /** 骨骼网格 */
    private skinnedMesh;
    /** 是否启用调试 */
    private debug;
    /** 是否已初始化 */
    private initialized;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: FacialAnimationModelAdapterConfig);
    /**
     * 设置骨骼网格
     * @param mesh 骨骼网格
     */
    setSkinnedMesh(mesh: THREE.SkinnedMesh): void;
    /**
     * 获取骨骼网格
     * @returns 骨骼网格
     */
    getSkinnedMesh(): THREE.SkinnedMesh | null;
    /**
     * 自动检测混合形状
     */
    autoDetectBlendShapes(): void;
    /**
     * 检测VRM混合形状
     * @param morphTargetDictionary 混合形状字典
     */
    private detectVRMBlendShapes;
    /**
     * 检测FBX混合形状
     * @param morphTargetDictionary 混合形状字典
     */
    private detectFBXBlendShapes;
    /**
     * 检测GLTF混合形状
     * @param morphTargetDictionary 混合形状字典
     */
    private detectGLTFBlendShapes;
    /**
     * 检测通用混合形状
     * @param morphTargetDictionary 混合形状字典
     */
    private detectGenericBlendShapes;
    /**
     * 应用表情
     * @param expression 表情类型
     * @param weight 权重
     * @returns 是否成功应用
     */
    applyExpression(expression: FacialExpressionType, weight: number): boolean;
    /**
     * 应用口型
     * @param viseme 口型类型
     * @param weight 权重
     * @returns 是否成功应用
     */
    applyViseme(viseme: VisemeType, weight: number): boolean;
    /**
     * 重置所有表情和口型
     */
    resetAll(): void;
}
