<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>增强水体交互示例</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
    }
    canvas {
      display: block;
    }
    #info {
      position: absolute;
      bottom: 10px;
      width: 100%;
      text-align: center;
      color: white;
      font-size: 14px;
      pointer-events: none;
      text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    }
    #loading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      font-size: 18px;
      z-index: 1000;
    }
    #loading .spinner {
      width: 50px;
      height: 50px;
      border: 5px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 20px;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="loading">
    <div class="spinner"></div>
    <div>正在加载增强水体交互示例...</div>
  </div>
  
  <div id="info">
    增强水体交互示例 - 展示体素浮力计算、方向性阻力和高级水流交互
  </div>
  
  <script type="module">
    import { EnhancedWaterInteractionExample } from './EnhancedWaterInteractionExample.ts';
    
    // 等待DOM加载完成
    window.addEventListener('DOMContentLoaded', () => {
      // 创建示例
      const example = new EnhancedWaterInteractionExample();
      
      // 启动示例
      example.start();
      
      // 隐藏加载界面
      setTimeout(() => {
        const loadingElement = document.getElementById('loading');
        if (loadingElement) {
          loadingElement.style.display = 'none';
        }
      }, 1000);
    });
  </script>
</body>
</html>
