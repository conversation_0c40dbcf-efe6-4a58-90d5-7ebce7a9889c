/**
 * 事务恢复服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { TransactionCoordinatorService } from '../transaction-coordinator.service';
import { TransactionLogStorageService, TransactionLogEntry } from '../storage/transaction-log-storage.service';
import { Transaction, TransactionStatus } from '../transaction.interface';

/**
 * 事务恢复选项
 */
export interface TransactionRecoveryOptions {
  /** 是否启用并行恢复 */
  enableParallelRecovery?: boolean;
  /** 并行恢复的最大并发数 */
  maxParallelRecovery?: number;
  /** 恢复超时时间（毫秒） */
  recoveryTimeout?: number;
  /** 恢复重试次数 */
  recoveryRetries?: number;
  /** 恢复重试间隔（毫秒） */
  recoveryRetryInterval?: number;
  /** 恢复检查点间隔（毫秒） */
  checkpointInterval?: number;
}

/**
 * 事务恢复服务
 */
@Injectable()
export class TransactionRecoveryService {
  private readonly logger = new Logger(TransactionRecoveryService.name);
  private readonly options: TransactionRecoveryOptions;
  private lastCheckpointTime: number = 0;
  private isRecovering: boolean = false;
  
  constructor(
    private readonly transactionCoordinatorService: TransactionCoordinatorService,
    private readonly transactionLogStorageService: TransactionLogStorageService,
  ) {
    this.options = {
      enableParallelRecovery: true,
      maxParallelRecovery: 5,
      recoveryTimeout: 60000, // 1分钟
      recoveryRetries: 3,
      recoveryRetryInterval: 5000, // 5秒
      checkpointInterval: 300000, // 5分钟
    };
  }
  
  /**
   * 恢复事务
   */
  async recoverTransactions(): Promise<{
    recovered: number;
    failed: number;
    skipped: number;
  }> {
    if (this.isRecovering) {
      this.logger.warn('事务恢复已在进行中');
      return { recovered: 0, failed: 0, skipped: 0 };
    }
    
    this.isRecovering = true;
    
    try {
      this.logger.log('开始恢复事务');
      
      // 读取事务日志
      const logEntries = await this.transactionLogStorageService.readTransactionLog();
      
      // 按事务ID分组
      const transactionMap = new Map<string, TransactionLogEntry[]>();
      
      for (const entry of logEntries) {
        const transactionId = entry.transaction.id;
        
        if (!transactionMap.has(transactionId)) {
          transactionMap.set(transactionId, []);
        }
        
        transactionMap.get(transactionId).push(entry);
      }
      
      // 获取最新的事务状态
      const latestTransactions = new Map<string, Transaction>();
      
      for (const [transactionId, entries] of transactionMap.entries()) {
        // 按时间戳排序
        entries.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
        
        // 获取最新的事务状态
        latestTransactions.set(transactionId, entries[0].transaction);
      }
      
      // 过滤出需要恢复的事务
      const transactionsToRecover = Array.from(latestTransactions.values()).filter(
        transaction => 
          transaction.status !== TransactionStatus.COMMITTED && 
          transaction.status !== TransactionStatus.ABORTED
      );
      
      this.logger.log(`找到 ${transactionsToRecover.length} 个需要恢复的事务`);
      
      // 恢复事务
      const results = await this.recoverTransactionBatch(transactionsToRecover);
      
      // 更新检查点时间
      this.lastCheckpointTime = Date.now();
      
      return results;
    } catch (error) {
      this.logger.error(`恢复事务失败: ${error.message}`, error.stack);
      return { recovered: 0, failed: 0, skipped: 0 };
    } finally {
      this.isRecovering = false;
    }
  }
  
  /**
   * 恢复事务批次
   * @param transactions 事务列表
   */
  private async recoverTransactionBatch(
    transactions: Transaction[],
  ): Promise<{
    recovered: number;
    failed: number;
    skipped: number;
  }> {
    const results = {
      recovered: 0,
      failed: 0,
      skipped: 0,
    };
    
    if (this.options.enableParallelRecovery) {
      // 并行恢复
      const batches = this.splitIntoBatches(
        transactions,
        this.options.maxParallelRecovery,
      );
      
      for (const batch of batches) {
        const batchResults = await Promise.all(
          batch.map(transaction => this.recoverTransaction(transaction)),
        );
        
        for (const result of batchResults) {
          if (result === 'recovered') {
            results.recovered++;
          } else if (result === 'failed') {
            results.failed++;
          } else {
            results.skipped++;
          }
        }
      }
    } else {
      // 串行恢复
      for (const transaction of transactions) {
        const result = await this.recoverTransaction(transaction);
        
        if (result === 'recovered') {
          results.recovered++;
        } else if (result === 'failed') {
          results.failed++;
        } else {
          results.skipped++;
        }
      }
    }
    
    return results;
  }
  
  /**
   * 恢复单个事务
   * @param transaction 事务
   */
  private async recoverTransaction(
    transaction: Transaction,
  ): Promise<'recovered' | 'failed' | 'skipped'> {
    try {
      // 检查事务是否已经存在于内存中
      const existingTransaction = this.transactionCoordinatorService.getTransaction(transaction.id);
      
      if (existingTransaction) {
        this.logger.debug(`事务 ${transaction.id} 已存在于内存中，跳过恢复`);
        return 'skipped';
      }
      
      // 检查事务是否已经完成
      if (
        transaction.status === TransactionStatus.COMMITTED || 
        transaction.status === TransactionStatus.ABORTED
      ) {
        this.logger.debug(`事务 ${transaction.id} 已完成，跳过恢复`);
        return 'skipped';
      }
      
      // 检查事务是否已超时
      const now = new Date();
      const elapsedTime = now.getTime() - new Date(transaction.startTime).getTime();
      
      if (elapsedTime > transaction.timeout) {
        // 事务已超时，执行回滚
        this.logger.warn(`恢复超时事务 ${transaction.id}，执行回滚`);
        await this.transactionCoordinatorService.abortTransaction(
          transaction.id,
          '事务恢复时已超时',
        );
        return 'recovered';
      }
      
      // 根据事务状态继续执行
      switch (transaction.status) {
        case TransactionStatus.STARTED:
          // 重新发送准备请求
          await this.transactionCoordinatorService.prepareTransaction(transaction.id);
          break;
        case TransactionStatus.PREPARED:
          // 重新发送提交请求
          await this.transactionCoordinatorService.commitTransaction(transaction.id);
          break;
        case TransactionStatus.PREPARING:
        case TransactionStatus.COMMITTING:
        case TransactionStatus.ABORTING:
          // 这些状态需要重新检查参与者状态
          await this.transactionCoordinatorService.checkTransactionStatus(transaction.id);
          break;
      }
      
      return 'recovered';
    } catch (error) {
      this.logger.error(`恢复事务 ${transaction.id} 失败: ${error.message}`, error.stack);
      return 'failed';
    }
  }
  
  /**
   * 将数组分割成批次
   * @param items 数组
   * @param batchSize 批次大小
   */
  private splitIntoBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    
    return batches;
  }
  
  /**
   * 检查是否需要创建检查点
   */
  shouldCreateCheckpoint(): boolean {
    const now = Date.now();
    return now - this.lastCheckpointTime >= this.options.checkpointInterval;
  }
  
  /**
   * 创建检查点
   */
  async createCheckpoint(): Promise<void> {
    if (this.isRecovering) {
      return;
    }
    
    this.lastCheckpointTime = Date.now();
    
    // 恢复事务
    await this.recoverTransactions();
  }
}
