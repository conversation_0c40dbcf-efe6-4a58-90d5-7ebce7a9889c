/**
 * 渲染测试框架
 * 用于自动化测试渲染功能
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Scene } from '../../src/scene/Scene';
import { Camera } from '../../src/rendering/Camera';
import { Renderer } from '../../src/rendering/Renderer';
import { RenderSystem } from '../../src/rendering/RenderSystem';

/**
 * 测试结果接口
 */
export interface TestResult {
  /** 测试名称 */
  name: string;
  /** 是否通过 */
  passed: boolean;
  /** 错误信息 */
  errorMessage?: string;
  /** 性能数据 */
  performanceData?: {
    /** 帧率 */
    fps: number;
    /** 渲染时间 */
    renderTime: number;
    /** 内存使用 */
    memoryUsage: number;
  };
  /** 渲染结果 */
  renderResult?: RenderResult;
}

/**
 * 渲染结果接口
 */
export interface RenderResult {
  /** 图像数据 */
  imageData: ImageData;
  /** 渲染时间 */
  renderTime: number;
}

/**
 * 比较结果接口
 */
export interface ComparisonResult {
  /** 是否匹配 */
  matches: boolean;
  /** 差异百分比 */
  differencePercentage: number;
  /** 差异图像 */
  differenceImage?: ImageData;
}

/**
 * 测试场景接口
 */
export interface TestScene {
  /** 场景名称 */
  name: string;
  /** 创建场景函数 */
  createScene: () => Scene;
  /** 创建相机函数 */
  createCamera: () => Camera;
  /** 预期结果 */
  expectedResult?: RenderResult;
}

/**
 * 测试套件接口
 */
export interface RenderTestSuite {
  /** 套件名称 */
  name: string;
  /** 测试场景列表 */
  scenes: TestScene[];
  /** 渲染器选项 */
  rendererOptions?: {
    /** 宽度 */
    width?: number;
    /** 高度 */
    height?: number;
    /** 是否开启抗锯齿 */
    antialias?: boolean;
    /** 是否开启阴影 */
    shadows?: boolean;
  };
  /** 渲染系统选项 */
  renderSystemOptions?: {
    /** 是否开启阴影 */
    enableShadows?: boolean;
    /** 是否开启后处理 */
    enablePostProcessing?: boolean;
  };
}

/**
 * 渲染测试框架类
 */
export class RenderTestFramework {
  /** 引擎实例 */
  private engine: Engine;
  /** 世界实例 */
  private world: World;
  /** 渲染器 */
  private renderer: Renderer;
  /** 渲染系统 */
  private renderSystem: RenderSystem;
  /** 测试结果 */
  private results: TestResult[] = [];
  /** 是否正在运行测试 */
  private isRunning: boolean = false;

  /**
   * 创建渲染测试框架
   * @param rendererOptions 渲染器选项
   * @param renderSystemOptions 渲染系统选项
   */
  constructor(
    rendererOptions?: {
      width?: number;
      height?: number;
      antialias?: boolean;
      shadows?: boolean;
    },
    renderSystemOptions?: {
      enableShadows?: boolean;
      enablePostProcessing?: boolean;
    }
  ) {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);
    
    // 创建渲染器
    this.renderer = new Renderer({
      width: rendererOptions?.width || 800,
      height: rendererOptions?.height || 600,
      antialias: rendererOptions?.antialias !== undefined ? rendererOptions.antialias : true,
      shadows: rendererOptions?.shadows !== undefined ? rendererOptions.shadows : true
    });
    
    // 创建渲染系统
    this.renderSystem = new RenderSystem(this.renderer, {
      enableShadows: renderSystemOptions?.enableShadows !== undefined ? renderSystemOptions.enableShadows : true,
      enablePostProcessing: renderSystemOptions?.enablePostProcessing !== undefined ? renderSystemOptions.enablePostProcessing : true
    });
    
    // 添加渲染系统到世界
    this.world.addSystem(this.renderSystem);
  }

  /**
   * 运行测试套件
   * @param testSuite 测试套件
   * @returns 测试结果
   */
  public async runTestSuite(testSuite: RenderTestSuite): Promise<TestResult[]> {
    if (this.isRunning) {
      throw new Error('测试框架已在运行中');
    }
    
    this.isRunning = true;
    this.results = [];
    
    console.log(`开始运行测试套件: ${testSuite.name}`);
    
    // 更新渲染器和渲染系统选项
    if (testSuite.rendererOptions) {
      this.updateRendererOptions(testSuite.rendererOptions);
    }
    
    if (testSuite.renderSystemOptions) {
      this.updateRenderSystemOptions(testSuite.renderSystemOptions);
    }
    
    // 运行每个测试场景
    for (const scene of testSuite.scenes) {
      try {
        const result = await this.runTestScene(scene);
        this.results.push(result);
        console.log(`测试场景 ${scene.name} ${result.passed ? '通过' : '失败'}`);
      } catch (error) {
        this.results.push({
          name: scene.name,
          passed: false,
          errorMessage: error.message
        });
        console.error(`测试场景 ${scene.name} 出错:`, error);
      }
    }
    
    this.isRunning = false;
    return this.results;
  }

  /**
   * 运行测试场景
   * @param testScene 测试场景
   * @returns 测试结果
   */
  private async runTestScene(testScene: TestScene): Promise<TestResult> {
    console.log(`运行测试场景: ${testScene.name}`);
    
    // 创建场景和相机
    const scene = testScene.createScene();
    const camera = testScene.createCamera();
    
    // 设置活跃场景和相机
    this.renderSystem.setActiveScene(scene);
    this.renderSystem.setActiveCamera(camera);
    
    // 渲染场景
    const startTime = performance.now();
    this.renderSystem.update(0);
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    // 获取渲染结果
    const renderResult = this.captureRenderResult(renderTime);
    
    // 比较结果
    let passed = true;
    let errorMessage = undefined;
    
    if (testScene.expectedResult) {
      const comparisonResult = this.compareRenderResults(renderResult, testScene.expectedResult);
      passed = comparisonResult.matches;
      if (!passed) {
        errorMessage = `渲染结果与预期不符，差异百分比: ${comparisonResult.differencePercentage.toFixed(2)}%`;
      }
    }
    
    // 收集性能数据
    const performanceData = {
      fps: 1000 / renderTime,
      renderTime,
      memoryUsage: this.getMemoryUsage()
    };
    
    return {
      name: testScene.name,
      passed,
      errorMessage,
      performanceData,
      renderResult
    };
  }

  /**
   * 捕获渲染结果
   * @param renderTime 渲染时间
   * @returns 渲染结果
   */
  private captureRenderResult(renderTime: number): RenderResult {
    const canvas = this.renderer.getCanvas();
    const context = canvas.getContext('2d');
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
    
    return {
      imageData,
      renderTime
    };
  }

  /**
   * 比较渲染结果
   * @param actual 实际渲染结果
   * @param expected 预期渲染结果
   * @param threshold 容差阈值
   * @returns 比较结果
   */
  public compareRenderResults(
    actual: RenderResult,
    expected: RenderResult,
    threshold: number = 0.05
  ): ComparisonResult {
    const actualData = actual.imageData.data;
    const expectedData = expected.imageData.data;
    
    if (actualData.length !== expectedData.length) {
      return {
        matches: false,
        differencePercentage: 100
      };
    }
    
    let diffCount = 0;
    const diffImage = new Uint8ClampedArray(actualData.length);
    
    for (let i = 0; i < actualData.length; i += 4) {
      const rDiff = Math.abs(actualData[i] - expectedData[i]);
      const gDiff = Math.abs(actualData[i + 1] - expectedData[i + 1]);
      const bDiff = Math.abs(actualData[i + 2] - expectedData[i + 2]);
      const aDiff = Math.abs(actualData[i + 3] - expectedData[i + 3]);
      
      const pixelDiff = (rDiff + gDiff + bDiff + aDiff) / (4 * 255);
      
      if (pixelDiff > threshold) {
        diffCount++;
        diffImage[i] = 255;
        diffImage[i + 1] = 0;
        diffImage[i + 2] = 0;
        diffImage[i + 3] = 255;
      } else {
        diffImage[i] = actualData[i];
        diffImage[i + 1] = actualData[i + 1];
        diffImage[i + 2] = actualData[i + 2];
        diffImage[i + 3] = actualData[i + 3];
      }
    }
    
    const differencePercentage = (diffCount / (actualData.length / 4)) * 100;
    const matches = differencePercentage <= threshold * 100;
    
    return {
      matches,
      differencePercentage,
      differenceImage: new ImageData(diffImage, actual.imageData.width, actual.imageData.height)
    };
  }

  /**
   * 获取内存使用情况
   * @returns 内存使用量（MB）
   */
  private getMemoryUsage(): number {
    if (window.performance && window.performance.memory) {
      return window.performance.memory.usedJSHeapSize / (1024 * 1024);
    }
    return 0;
  }

  /**
   * 更新渲染器选项
   * @param options 渲染器选项
   */
  private updateRendererOptions(options: {
    width?: number;
    height?: number;
    antialias?: boolean;
    shadows?: boolean;
  }): void {
    if (options.width && options.height) {
      this.renderer.setSize(options.width, options.height);
    }
    
    if (options.shadows !== undefined) {
      this.renderer.setShadowsEnabled(options.shadows);
    }
  }

  /**
   * 更新渲染系统选项
   * @param options 渲染系统选项
   */
  private updateRenderSystemOptions(options: {
    enableShadows?: boolean;
    enablePostProcessing?: boolean;
  }): void {
    if (options.enableShadows !== undefined) {
      this.renderSystem.setShadowsEnabled(options.enableShadows);
    }
    
    if (options.enablePostProcessing !== undefined) {
      this.renderSystem.setPostProcessingEnabled(options.enablePostProcessing);
    }
  }
}
