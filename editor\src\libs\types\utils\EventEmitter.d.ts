/**
 * 事件发射器类
 * 提供事件订阅和发布功能
 */
export type EventCallback = (...args: any[]) => void;
export declare class EventEmitter {
    /** 事件监听器映射 */
    private _listeners;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @returns 当前实例，用于链式调用
     */
    on(event: string, callback: EventCallback): this;
    /**
     * 添加一次性事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @returns 当前实例，用于链式调用
     */
    once(event: string, callback: EventCallback): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数（可选，如果不提供则移除该事件的所有监听器）
     * @returns 当前实例，用于链式调用
     */
    off(event: string, callback?: EventCallback): this;
    /**
     * 发射事件
     * @param event 事件名称
     * @param args 事件参数
     * @returns 是否有监听器处理了该事件
     */
    emit(event: string, ...args: any[]): boolean;
    /**
     * 获取事件监听器数量
     * @param event 事件名称（可选，如果不提供则返回所有事件的监听器总数）
     * @returns 监听器数量
     */
    listenerCount(event?: string): number;
    /**
     * 获取事件名称列表
     * @returns 事件名称数组
     */
    eventNames(): string[];
    /**
     * 获取事件监听器列表
     * @param event 事件名称
     * @returns 监听器数组
     */
    listeners(event: string): EventCallback[];
    /**
     * 移除所有事件监听器
     * @returns 当前实例，用于链式调用
     */
    removeAllListeners(): this;
}
