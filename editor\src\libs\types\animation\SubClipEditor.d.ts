import { SubClip } from './SubClip';
import { AnimationSubClip } from './AnimationSubClip';
import { SubClipEvent } from './SubClipEvent';
import { SubClipSequence } from './SubClipSequence';
import { SubClipTransition } from './SubClipTransition';
import { SubClipModifier } from './SubClipModifier';
/**
 * 子片段编辑器事件类型
 */
export declare enum SubClipEditorEventType {
    /** 选择改变 */
    SELECTION_CHANGED = "selectionChanged",
    /** 时间改变 */
    TIME_CHANGED = "timeChanged",
    /** 编辑操作 */
    EDIT_OPERATION = "editOperation",
    /** 预览状态改变 */
    PREVIEW_STATE_CHANGED = "previewStateChanged"
}
/**
 * 编辑操作类型
 */
export declare enum EditOperationType {
    /** 添加 */
    ADD = "add",
    /** 移除 */
    REMOVE = "remove",
    /** 修改 */
    MODIFY = "modify",
    /** 复制 */
    DUPLICATE = "duplicate",
    /** 分割 */
    SPLIT = "split",
    /** 合并 */
    MERGE = "merge"
}
/**
 * 子片段编辑器配置
 */
export interface SubClipEditorConfig {
    /** 编辑器名称 */
    name?: string;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 子片段编辑器
 */
export declare class SubClipEditor {
    /** 编辑器名称 */
    private name;
    /** 是否启用调试 */
    private debug;
    /** 事件发射器 */
    private eventEmitter;
    /** 子片段列表 */
    private subClips;
    /** 事件列表 */
    private events;
    /** 序列列表 */
    private sequences;
    /** 过渡列表 */
    private transitions;
    /** 变形器列表 */
    private modifiers;
    /** 当前选中的子片段 */
    private selectedSubClip;
    /** 当前选中的事件 */
    private selectedEvent;
    /** 当前选中的序列 */
    private selectedSequence;
    /** 当前选中的过渡 */
    private selectedTransition;
    /** 当前选中的变形器 */
    private selectedModifier;
    /** 当前时间 */
    private currentTime;
    /** 是否正在预览 */
    private isPreviewPlaying;
    /** 预览速度 */
    private previewSpeed;
    /** 撤销栈 */
    private undoStack;
    /** 重做栈 */
    private redoStack;
    /** 最大撤销步数 */
    private maxUndoSteps;
    /**
     * 创建子片段编辑器
     * @param config 配置
     */
    constructor(config?: SubClipEditorConfig);
    /**
     * 获取编辑器名称
     * @returns 编辑器名称
     */
    getName(): string;
    /**
     * 设置编辑器名称
     * @param name 编辑器名称
     */
    setName(name: string): void;
    /**
     * 添加子片段
     * @param subClip 子片段
     */
    addSubClip(subClip: SubClip | AnimationSubClip): void;
    /**
     * 移除子片段
     * @param subClip 子片段
     */
    removeSubClip(subClip: SubClip | AnimationSubClip): void;
    /**
     * 获取子片段列表
     * @returns 子片段列表
     */
    getSubClips(): (SubClip | AnimationSubClip)[];
    /**
     * 添加事件
     * @param event 事件
     */
    addEvent(event: SubClipEvent): void;
    /**
     * 移除事件
     * @param event 事件
     */
    removeEvent(event: SubClipEvent): void;
    /**
     * 获取事件列表
     * @returns 事件列表
     */
    getEvents(): SubClipEvent[];
    /**
     * 添加序列
     * @param sequence 序列
     */
    addSequence(sequence: SubClipSequence): void;
    /**
     * 移除序列
     * @param sequence 序列
     */
    removeSequence(sequence: SubClipSequence): void;
    /**
     * 获取序列列表
     * @returns 序列列表
     */
    getSequences(): SubClipSequence[];
    /**
     * 添加过渡
     * @param transition 过渡
     */
    addTransition(transition: SubClipTransition): void;
    /**
     * 移除过渡
     * @param transition 过渡
     */
    removeTransition(transition: SubClipTransition): void;
    /**
     * 获取过渡列表
     * @returns 过渡列表
     */
    getTransitions(): SubClipTransition[];
    /**
     * 添加变形器
     * @param modifier 变形器
     */
    addModifier(modifier: SubClipModifier): void;
    /**
     * 移除变形器
     * @param modifier 变形器
     */
    removeModifier(modifier: SubClipModifier): void;
    /**
     * 获取变形器列表
     * @returns 变形器列表
     */
    getModifiers(): SubClipModifier[];
    /**
     * 选择子片段
     * @param subClip 子片段
     */
    selectSubClip(subClip: SubClip | AnimationSubClip | null): void;
    /**
     * 获取当前选中的子片段
     * @returns 当前选中的子片段
     */
    getSelectedSubClip(): SubClip | AnimationSubClip | null;
    /**
     * 选择事件
     * @param event 事件
     */
    selectEvent(event: SubClipEvent | null): void;
    /**
     * 获取当前选中的事件
     * @returns 当前选中的事件
     */
    getSelectedEvent(): SubClipEvent | null;
    /**
     * 选择序列
     * @param sequence 序列
     */
    selectSequence(sequence: SubClipSequence | null): void;
    /**
     * 获取当前选中的序列
     * @returns 当前选中的序列
     */
    getSelectedSequence(): SubClipSequence | null;
    /**
     * 选择过渡
     * @param transition 过渡
     */
    selectTransition(transition: SubClipTransition | null): void;
    /**
     * 获取当前选中的过渡
     * @returns 当前选中的过渡
     */
    getSelectedTransition(): SubClipTransition | null;
    /**
     * 选择变形器
     * @param modifier 变形器
     */
    selectModifier(modifier: SubClipModifier | null): void;
    /**
     * 获取当前选中的变形器
     * @returns 当前选中的变形器
     */
    getSelectedModifier(): SubClipModifier | null;
    /**
     * 设置当前时间
     * @param time 时间
     */
    setCurrentTime(time: number): void;
    /**
     * 获取当前时间
     * @returns 当前时间
     */
    getCurrentTime(): number;
    /**
     * 开始预览
     */
    startPreview(): void;
    /**
     * 停止预览
     */
    stopPreview(): void;
    /**
     * 是否正在预览
     * @returns 是否正在预览
     */
    getIsPreviewPlaying(): boolean;
    /**
     * 设置预览速度
     * @param speed 速度
     */
    setPreviewSpeed(speed: number): void;
    /**
     * 获取预览速度
     * @returns 预览速度
     */
    getPreviewSpeed(): number;
    /**
     * 保存当前状态
     */
    private saveState;
    /**
     * 撤销
     */
    undo(): void;
    /**
     * 重做
     */
    redo(): void;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: SubClipEditorEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: SubClipEditorEventType, listener: (data: any) => void): void;
}
