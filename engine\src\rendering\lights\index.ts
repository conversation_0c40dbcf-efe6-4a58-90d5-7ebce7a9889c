/**
 * 光照系统模块
 * 导出所有光源相关的类和接口
 */

// 导出区域光基类
export { AreaLight } from './AreaLight';
export type { AreaLightType, AreaLightOptions } from './AreaLight';

// 导出矩形区域光
export { RectAreaLightComponent } from './RectAreaLight';
export type { RectAreaLightOptions } from './RectAreaLight';

// 导出球形区域光
export { SphereAreaLightComponent } from './SphereAreaLight';
export type { SphereAreaLightOptions } from './SphereAreaLight';

// 导出圆盘区域光
export { DiskAreaLightComponent } from './DiskAreaLight';
export type { DiskAreaLightOptions } from './DiskAreaLight';

// 导出管状区域光
export { TubeAreaLightComponent } from './TubeAreaLight';
export type { TubeAreaLightOptions } from './TubeAreaLight';

// 导出IES光源
export { IESLight } from './IESLight';
export type { IESLightType, IESLightOptions } from './IESLight';

// 导出IES加载器
export { IESLoader } from './IESLoader';
