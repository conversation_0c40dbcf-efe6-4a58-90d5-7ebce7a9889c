/* 面部动画预设示例样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ddd;
}

.header-controls {
  display: flex;
  gap: 10px;
}

button, select, input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  font-family: inherit;
}

button {
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover {
  background-color: #f0f0f0;
}

main {
  display: flex;
  gap: 20px;
  height: calc(100vh - 100px);
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#preview {
  flex: 1;
  background-color: #1a1a1a;
}

.preview-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;
}

.control-btn {
  min-width: 80px;
}

.presets-container {
  width: 400px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.tab-btn {
  flex: 1;
  padding: 12px;
  border: none;
  background-color: #f9f9f9;
  font-weight: 500;
}

.tab-btn.active {
  background-color: #fff;
  border-bottom: 2px solid #1890ff;
}

.tab-content {
  display: none;
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.tab-content.active {
  display: flex;
  flex-direction: column;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.presets-grid,
.templates-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  overflow-y: auto;
}

.preset-card,
.template-card {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.preset-card:hover,
.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-image {
  height: 120px;
  background-color: #f0f0f0;
  background-size: cover;
  background-position: center;
}

.card-content {
  padding: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.card-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  font-size: 12px;
  padding: 2px 6px;
  background-color: #f0f0f0;
  border-radius: 4px;
  color: #666;
}

/* 模态框 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
}

.modal.active {
  display: flex;
}

.modal-content {
  width: 800px;
  max-width: 90%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.modal-body {
  display: flex;
  min-height: 400px;
}

.modal-preview {
  flex: 3;
  background-color: #1a1a1a;
}

.modal-info {
  flex: 2;
  padding: 16px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 16px;
  border-top: 1px solid #eee;
}

.primary-btn {
  background-color: #1890ff;
  color: #fff;
  border: none;
}

.primary-btn:hover {
  background-color: #40a9ff;
}

.cancel-btn:hover {
  background-color: #f5f5f5;
}

.parameters-container {
  margin-top: 16px;
}

.parameter-item {
  margin-bottom: 12px;
}

.parameter-label {
  font-weight: 500;
  margin-bottom: 4px;
}

.parameter-control {
  width: 100%;
}

/* 响应式布局 */
@media (max-width: 768px) {
  main {
    flex-direction: column;
    height: auto;
  }
  
  .preview-container,
  .presets-container {
    width: 100%;
  }
  
  .preview-container {
    height: 300px;
  }
  
  .modal-body {
    flex-direction: column;
  }
  
  .modal-preview {
    height: 300px;
  }
}
