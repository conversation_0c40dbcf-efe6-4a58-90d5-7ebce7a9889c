<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DL（Digital Learning）引擎示例项目库</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #2196f3;
      color: white;
      padding: 20px;
      text-align: center;
      margin-bottom: 30px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    h1 {
      margin: 0;
      font-size: 28px;
    }
    .subtitle {
      font-size: 16px;
      margin-top: 10px;
      opacity: 0.8;
    }
    .search-bar {
      display: flex;
      margin-bottom: 20px;
    }
    .search-input {
      flex-grow: 1;
      padding: 10px 15px;
      border: 1px solid #ddd;
      border-radius: 5px 0 0 5px;
      font-size: 16px;
    }
    .search-button {
      background-color: #2196f3;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 0 5px 5px 0;
      cursor: pointer;
      font-size: 16px;
    }
    .search-button:hover {
      background-color: #1976d2;
    }
    .filters {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 20px;
    }
    .filter-button {
      background-color: #e0e0e0;
      border: none;
      padding: 8px 15px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s;
    }
    .filter-button:hover {
      background-color: #d0d0d0;
    }
    .filter-button.active {
      background-color: #2196f3;
      color: white;
    }
    .examples-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
    }
    .example-card {
      background-color: white;
      border-radius: 5px;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .example-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    .example-image {
      width: 100%;
      height: 160px;
      background-color: #e0e0e0;
      background-size: cover;
      background-position: center;
    }
    .example-content {
      padding: 15px;
    }
    .example-title {
      font-size: 18px;
      margin: 0 0 10px 0;
      color: #333;
    }
    .example-description {
      font-size: 14px;
      color: #666;
      margin: 0 0 15px 0;
      line-height: 1.4;
    }
    .example-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-bottom: 15px;
    }
    .example-tag {
      background-color: #e3f2fd;
      color: #1976d2;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 12px;
    }
    .example-actions {
      display: flex;
      justify-content: space-between;
    }
    .example-button {
      background-color: #2196f3;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 14px;
      flex-grow: 1;
      text-align: center;
      margin-right: 5px;
      text-decoration: none;
    }
    .example-button:last-child {
      margin-right: 0;
    }
    .example-button:hover {
      background-color: #1976d2;
    }
    .example-button.secondary {
      background-color: #e0e0e0;
      color: #333;
    }
    .example-button.secondary:hover {
      background-color: #d0d0d0;
    }
    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      color: #666;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>DL（Digital Learning）引擎示例项目库</h1>
      <div class="subtitle">探索各种功能演示和最佳实践示例</div>
    </header>
    
    <div class="search-bar">
      <input type="text" class="search-input" placeholder="搜索示例项目...">
      <button class="search-button">搜索</button>
    </div>
    
    <div class="filters">
      <button class="filter-button active" data-filter="all">全部</button>
      <button class="filter-button" data-filter="basic">基础功能</button>
      <button class="filter-button" data-filter="material">材质系统</button>
      <button class="filter-button" data-filter="animation">动画系统</button>
      <button class="filter-button" data-filter="physics">物理系统</button>
      <button class="filter-button" data-filter="visualscript">视觉脚本</button>
      <button class="filter-button" data-filter="advanced">高级示例</button>
    </div>
    
    <div class="examples-grid">
      <!-- 基础编辑器功能演示 -->
      <div class="example-card" data-category="basic">
        <div class="example-image" style="background-image: url('assets/images/editor-basics.jpg');"></div>
        <div class="example-content">
          <h3 class="example-title">基础编辑器功能演示</h3>
          <p class="example-description">展示编辑器的基本功能，包括场景创建、对象操作和属性编辑。</p>
          <div class="example-tags">
            <span class="example-tag">基础功能</span>
            <span class="example-tag">编辑器</span>
            <span class="example-tag">入门</span>
          </div>
          <div class="example-actions">
            <a href="editor-basics/index.html" class="example-button" target="_blank">查看演示</a>
            <button class="example-button secondary" data-import="editor-basics">导入项目</button>
          </div>
        </div>
      </div>
      
      <!-- 材质编辑器演示 -->
      <div class="example-card" data-category="material">
        <div class="example-image" style="background-image: url('assets/images/material-editor.jpg');"></div>
        <div class="example-content">
          <h3 class="example-title">材质编辑器演示</h3>
          <p class="example-description">展示如何创建和编辑各种材质类型，包括PBR材质、基础材质和特殊效果材质。</p>
          <div class="example-tags">
            <span class="example-tag">材质系统</span>
            <span class="example-tag">PBR</span>
            <span class="example-tag">着色器</span>
          </div>
          <div class="example-actions">
            <a href="material-editor/index.html" class="example-button" target="_blank">查看演示</a>
            <button class="example-button secondary" data-import="material-editor">导入项目</button>
          </div>
        </div>
      </div>
      
      <!-- 动画系统演示 -->
      <div class="example-card" data-category="animation">
        <div class="example-image" style="background-image: url('assets/images/animation-demo.jpg');"></div>
        <div class="example-content">
          <h3 class="example-title">动画系统演示</h3>
          <p class="example-description">展示关键帧动画、骨骼动画和动画混合功能，以及动画状态机的使用。</p>
          <div class="example-tags">
            <span class="example-tag">动画系统</span>
            <span class="example-tag">骨骼动画</span>
            <span class="example-tag">动画混合</span>
          </div>
          <div class="example-actions">
            <a href="animation-demo/index.html" class="example-button" target="_blank">查看演示</a>
            <button class="example-button secondary" data-import="animation-demo">导入项目</button>
          </div>
        </div>
      </div>
      
      <!-- 物理系统演示 -->
      <div class="example-card" data-category="physics">
        <div class="example-image" style="background-image: url('assets/images/physics-demo.jpg');"></div>
        <div class="example-content">
          <h3 class="example-title">物理系统演示</h3>
          <p class="example-description">展示刚体物理、碰撞检测、物理约束和物理材质等功能。</p>
          <div class="example-tags">
            <span class="example-tag">物理系统</span>
            <span class="example-tag">刚体</span>
            <span class="example-tag">碰撞</span>
            <span class="example-tag">约束</span>
          </div>
          <div class="example-actions">
            <a href="physics-demo/index.html" class="example-button" target="_blank">查看演示</a>
            <button class="example-button secondary" data-import="physics-demo">导入项目</button>
          </div>
        </div>
      </div>
      
      <!-- 视觉脚本演示 -->
      <div class="example-card" data-category="visualscript">
        <div class="example-image" style="background-image: url('assets/images/visualscript-demo.jpg');"></div>
        <div class="example-content">
          <h3 class="example-title">视觉脚本演示</h3>
          <p class="example-description">展示视觉脚本系统的节点创建、连接、调试和执行功能。</p>
          <div class="example-tags">
            <span class="example-tag">视觉脚本</span>
            <span class="example-tag">节点编辑器</span>
            <span class="example-tag">逻辑编程</span>
          </div>
          <div class="example-actions">
            <a href="visualscript-demo/index.html" class="example-button" target="_blank">查看演示</a>
            <button class="example-button secondary" data-import="visualscript-demo">导入项目</button>
          </div>
        </div>
      </div>
      
      <!-- AI和网络安全示例 -->
      <div class="example-card" data-category="advanced">
        <div class="example-image" style="background-image: url('assets/images/ai-security-debug.jpg');"></div>
        <div class="example-content">
          <h3 class="example-title">AI和网络安全示例</h3>
          <p class="example-description">展示AI模型集成、网络安全和调试工具的高级应用。</p>
          <div class="example-tags">
            <span class="example-tag">AI</span>
            <span class="example-tag">网络安全</span>
            <span class="example-tag">调试</span>
            <span class="example-tag">高级</span>
          </div>
          <div class="example-actions">
            <a href="ai-security-debug/index.html" class="example-button" target="_blank">查看演示</a>
            <button class="example-button secondary" data-import="ai-security-debug">导入项目</button>
          </div>
        </div>
      </div>
    </div>
    
    <footer>
      <p>© 2023 DL（Digital Learning）引擎团队 版权所有</p>
    </footer>
  </div>

  <script>
    // 过滤功能
    const filterButtons = document.querySelectorAll('.filter-button');
    const exampleCards = document.querySelectorAll('.example-card');
    
    filterButtons.forEach(button => {
      button.addEventListener('click', () => {
        // 更新按钮状态
        filterButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        const filter = button.getAttribute('data-filter');
        
        // 过滤示例卡片
        exampleCards.forEach(card => {
          if (filter === 'all' || card.getAttribute('data-category') === filter) {
            card.style.display = 'block';
          } else {
            card.style.display = 'none';
          }
        });
      });
    });
    
    // 搜索功能
    const searchInput = document.querySelector('.search-input');
    const searchButton = document.querySelector('.search-button');
    
    function performSearch() {
      const searchTerm = searchInput.value.toLowerCase();
      
      exampleCards.forEach(card => {
        const title = card.querySelector('.example-title').textContent.toLowerCase();
        const description = card.querySelector('.example-description').textContent.toLowerCase();
        const tags = Array.from(card.querySelectorAll('.example-tag')).map(tag => tag.textContent.toLowerCase());
        
        if (
          searchTerm === '' || 
          title.includes(searchTerm) || 
          description.includes(searchTerm) || 
          tags.some(tag => tag.includes(searchTerm))
        ) {
          card.style.display = 'block';
        } else {
          card.style.display = 'none';
        }
      });
    }
    
    searchButton.addEventListener('click', performSearch);
    searchInput.addEventListener('keyup', (e) => {
      if (e.key === 'Enter') {
        performSearch();
      }
    });
    
    // 导入项目功能
    const importButtons = document.querySelectorAll('[data-import]');
    
    importButtons.forEach(button => {
      button.addEventListener('click', () => {
        const projectName = button.getAttribute('data-import');
        alert(`导入项目: ${projectName}\n\n此功能需要与编辑器集成，在实际应用中将打开编辑器并导入示例项目。`);
      });
    });
  </script>
</body>
</html>
