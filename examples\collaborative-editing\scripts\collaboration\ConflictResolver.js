/**
 * 冲突解决器
 * 负责检测和解决编辑冲突
 */
export class ConflictResolver {
  /**
   * 构造函数
   * @param {OperationManager} operationManager 操作管理器
   */
  constructor(operationManager) {
    this.operationManager = operationManager;
    this.currentConflict = null;
    this.conflictDialogElement = document.getElementById('conflict-dialog');
    
    // 绑定方法
    this.handleConflict = this.handleConflict.bind(this);
    this.resolveConflict = this.resolveConflict.bind(this);
    
    // 设置事件监听器
    this.operationManager.on('conflict', this.handleConflict);
  }
  
  /**
   * 处理冲突
   * @param {Object} conflict 冲突对象
   */
  handleConflict(conflict) {
    console.log('检测到冲突:', conflict);
    
    // 保存当前冲突
    this.currentConflict = conflict;
    
    // 显示冲突对话框
    this.showConflictDialog(conflict);
  }
  
  /**
   * 解决冲突
   * @param {string} strategy 解决策略 ('local', 'remote', 'merge')
   */
  resolveConflict(strategy) {
    if (!this.currentConflict) {
      console.warn('没有待解决的冲突');
      return;
    }
    
    const { userId, localOperation, remoteOperation } = this.currentConflict;
    
    switch (strategy) {
      case 'local':
        // 保留本地更改，忽略远程更改
        console.log('保留本地更改:', localOperation);
        break;
      
      case 'remote':
        // 采用远程更改，应用远程操作
        console.log('采用远程更改:', remoteOperation);
        this.operationManager.applyOperation(remoteOperation);
        break;
      
      case 'merge':
        // 合并更改，使用操作转换算法
        console.log('合并更改');
        const mergedOperation = this.mergeOperations(localOperation, remoteOperation);
        this.operationManager.applyOperation(mergedOperation);
        break;
      
      default:
        console.warn('未知的冲突解决策略:', strategy);
    }
    
    // 清除当前冲突
    this.currentConflict = null;
    
    // 隐藏冲突对话框
    this.hideConflictDialog();
  }
  
  /**
   * 合并操作
   * @param {Object} localOperation 本地操作
   * @param {Object} remoteOperation 远程操作
   * @returns {Object} 合并后的操作
   */
  mergeOperations(localOperation, remoteOperation) {
    // 根据操作类型进行合并
    switch (localOperation.type) {
      case 'transform':
        return this.mergeTransformOperations(localOperation, remoteOperation);
      
      case 'property':
        return this.mergePropertyOperations(localOperation, remoteOperation);
      
      default:
        // 对于其他类型的操作，默认采用远程操作
        return remoteOperation;
    }
  }
  
  /**
   * 合并变换操作
   * @param {Object} localOperation 本地操作
   * @param {Object} remoteOperation 远程操作
   * @returns {Object} 合并后的操作
   */
  mergeTransformOperations(localOperation, remoteOperation) {
    const { entityId, transformType, value: localValue } = localOperation.data;
    const { value: remoteValue } = remoteOperation.data;
    
    // 创建合并后的操作
    const mergedOperation = { ...remoteOperation };
    
    // 根据变换类型进行合并
    switch (transformType) {
      case 'position':
      case 'scale':
        // 对于位置和缩放，取平均值
        mergedOperation.data.value = {
          x: (localValue.x + remoteValue.x) / 2,
          y: (localValue.y + remoteValue.y) / 2,
          z: (localValue.z + remoteValue.z) / 2
        };
        break;
      
      case 'rotation':
        // 对于旋转，使用球面线性插值（SLERP）
        // 在实际应用中，这里应该使用四元数插值
        // 这里简化为取平均值
        mergedOperation.data.value = {
          x: (localValue.x + remoteValue.x) / 2,
          y: (localValue.y + remoteValue.y) / 2,
          z: (localValue.z + remoteValue.z) / 2
        };
        break;
      
      default:
        // 对于其他类型的变换，默认采用远程值
        mergedOperation.data.value = remoteValue;
    }
    
    return mergedOperation;
  }
  
  /**
   * 合并属性操作
   * @param {Object} localOperation 本地操作
   * @param {Object} remoteOperation 远程操作
   * @returns {Object} 合并后的操作
   */
  mergePropertyOperations(localOperation, remoteOperation) {
    const { entityId, propertyPath, value: localValue } = localOperation.data;
    const { value: remoteValue } = remoteOperation.data;
    
    // 创建合并后的操作
    const mergedOperation = { ...remoteOperation };
    
    // 根据属性类型进行合并
    if (typeof localValue === 'number' && typeof remoteValue === 'number') {
      // 对于数值类型，取平均值
      mergedOperation.data.value = (localValue + remoteValue) / 2;
    } else if (typeof localValue === 'string' && typeof remoteValue === 'string') {
      // 对于字符串类型，如果不同则保留远程值
      mergedOperation.data.value = remoteValue;
    } else if (Array.isArray(localValue) && Array.isArray(remoteValue)) {
      // 对于数组类型，合并数组
      mergedOperation.data.value = [...new Set([...localValue, ...remoteValue])];
    } else if (typeof localValue === 'object' && typeof remoteValue === 'object') {
      // 对于对象类型，递归合并
      mergedOperation.data.value = this.mergeObjects(localValue, remoteValue);
    } else {
      // 对于其他类型，默认采用远程值
      mergedOperation.data.value = remoteValue;
    }
    
    return mergedOperation;
  }
  
  /**
   * 合并对象
   * @param {Object} obj1 对象1
   * @param {Object} obj2 对象2
   * @returns {Object} 合并后的对象
   */
  mergeObjects(obj1, obj2) {
    const result = { ...obj1 };
    
    for (const key in obj2) {
      if (obj2.hasOwnProperty(key)) {
        if (obj1.hasOwnProperty(key)) {
          if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
            // 递归合并子对象
            result[key] = this.mergeObjects(obj1[key], obj2[key]);
          } else {
            // 对于其他类型，采用obj2的值
            result[key] = obj2[key];
          }
        } else {
          // 如果obj1没有该属性，直接采用obj2的值
          result[key] = obj2[key];
        }
      }
    }
    
    return result;
  }
  
  /**
   * 显示冲突对话框
   * @param {Object} conflict 冲突对象
   */
  showConflictDialog(conflict) {
    if (!this.conflictDialogElement) {
      console.warn('冲突对话框元素不存在');
      return;
    }
    
    // 设置冲突预览
    const localPreviewElement = document.getElementById('local-change-preview');
    const remotePreviewElement = document.getElementById('remote-change-preview');
    
    if (localPreviewElement) {
      localPreviewElement.innerHTML = this.formatOperationPreview(conflict.localOperation);
    }
    
    if (remotePreviewElement) {
      remotePreviewElement.innerHTML = this.formatOperationPreview(conflict.remoteOperation);
    }
    
    // 显示对话框
    this.conflictDialogElement.classList.add('active');
  }
  
  /**
   * 隐藏冲突对话框
   */
  hideConflictDialog() {
    if (!this.conflictDialogElement) {
      return;
    }
    
    this.conflictDialogElement.classList.remove('active');
  }
  
  /**
   * 格式化操作预览
   * @param {Object} operation 操作对象
   * @returns {string} 格式化后的HTML
   */
  formatOperationPreview(operation) {
    if (!operation) {
      return '<div class="empty-preview">无操作数据</div>';
    }
    
    let html = '';
    
    // 添加操作类型
    html += `<div class="preview-type">操作类型: <strong>${this.getOperationTypeName(operation.type)}</strong></div>`;
    
    // 添加操作数据
    html += '<div class="preview-data">';
    
    switch (operation.type) {
      case 'create':
        html += `<div>实体名称: <strong>${operation.data.entityData.name}</strong></div>`;
        html += `<div>实体类型: <strong>${operation.data.entityData.type}</strong></div>`;
        break;
      
      case 'delete':
        html += `<div>实体ID: <strong>${operation.data.entityId}</strong></div>`;
        break;
      
      case 'transform':
        html += `<div>实体ID: <strong>${operation.data.entityId}</strong></div>`;
        html += `<div>变换类型: <strong>${this.getTransformTypeName(operation.data.transformType)}</strong></div>`;
        html += `<div>变换值: <strong>${this.formatValue(operation.data.value)}</strong></div>`;
        break;
      
      case 'property':
        html += `<div>实体ID: <strong>${operation.data.entityId}</strong></div>`;
        html += `<div>属性路径: <strong>${operation.data.propertyPath}</strong></div>`;
        html += `<div>属性值: <strong>${this.formatValue(operation.data.value)}</strong></div>`;
        break;
    }
    
    html += '</div>';
    
    // 添加操作时间
    const time = new Date(operation.timestamp).toLocaleTimeString();
    html += `<div class="preview-time">操作时间: ${time}</div>`;
    
    return html;
  }
  
  /**
   * 获取操作类型名称
   * @param {string} type 操作类型
   * @returns {string} 类型名称
   */
  getOperationTypeName(type) {
    switch (type) {
      case 'create':
        return '创建';
      case 'delete':
        return '删除';
      case 'transform':
        return '变换';
      case 'property':
        return '属性';
      default:
        return type;
    }
  }
  
  /**
   * 获取变换类型名称
   * @param {string} type 变换类型
   * @returns {string} 类型名称
   */
  getTransformTypeName(type) {
    switch (type) {
      case 'position':
        return '位置';
      case 'rotation':
        return '旋转';
      case 'scale':
        return '缩放';
      default:
        return type;
    }
  }
  
  /**
   * 格式化值
   * @param {*} value 值
   * @returns {string} 格式化后的字符串
   */
  formatValue(value) {
    if (value === null || value === undefined) {
      return '无';
    }
    
    if (typeof value === 'object') {
      if (value.x !== undefined && value.y !== undefined && value.z !== undefined) {
        // 向量值
        return `X: ${value.x.toFixed(2)}, Y: ${value.y.toFixed(2)}, Z: ${value.z.toFixed(2)}`;
      } else {
        // 其他对象
        return JSON.stringify(value);
      }
    }
    
    return String(value);
  }
}
