/**
 * 屏幕空间全局光照效果
 * 提升场景的整体光照质量
 */
import * as THREE from 'three';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * SSGI效果选项
 */
export interface SSGIEffectOptions extends PostProcessingEffectOptions {
    /** 强度 */
    intensity?: number;
    /** 半径 */
    radius?: number;
    /** 偏移 */
    bias?: number;
    /** 采样数 */
    samples?: number;
    /** 衰减 */
    falloff?: number;
    /** 是否使用法线贴图 */
    useNormalMap?: boolean;
    /** 是否使用深度贴图 */
    useDepthMap?: boolean;
    /** 去噪迭代次数 */
    denoiseIterations?: number;
    /** 去噪核大小 */
    denoiseKernel?: number;
    /** 去噪漫反射强度 */
    denoiseDiffuse?: number;
    /** 去噪高光强度 */
    denoiseSpecular?: number;
    /** Phi值 */
    phi?: number;
    /** 亮度Phi值 */
    lumaPhi?: number;
    /** 深度Phi值 */
    depthPhi?: number;
    /** 法线Phi值 */
    normalPhi?: number;
    /** 粗糙度Phi值 */
    roughnessPhi?: number;
    /** 高光Phi值 */
    specularPhi?: number;
    /** 环境模糊 */
    envBlur?: number;
    /** 是否使用重要性采样 */
    importanceSampling?: boolean;
    /** 精细步数 */
    refineSteps?: number;
    /** 分辨率缩放 */
    resolutionScale?: number;
    /** 是否显示丢失的光线 */
    missedRays?: boolean;
}
/**
 * 屏幕空间全局光照效果
 */
export declare class SSGIEffect extends PostProcessingEffect {
    /** 强度 */
    private intensity;
    /** 半径 */
    private radius;
    /** 偏移 */
    private bias;
    /** 采样数 */
    private samples;
    /** 衰减 */
    private falloff;
    /** 是否使用法线贴图 */
    private useNormalMap;
    /** 是否使用深度贴图 */
    private useDepthMap;
    /** 去噪迭代次数 */
    private denoiseIterations;
    /** 去噪核大小 */
    private denoiseKernel;
    /** 去噪漫反射强度 */
    private denoiseDiffuse;
    /** 去噪高光强度 */
    private denoiseSpecular;
    /** Phi值 */
    private phi;
    /** 亮度Phi值 */
    private lumaPhi;
    /** 深度Phi值 */
    private depthPhi;
    /** 法线Phi值 */
    private normalPhi;
    /** 粗糙度Phi值 */
    private roughnessPhi;
    /** 高光Phi值 */
    private specularPhi;
    /** 环境模糊 */
    private envBlur;
    /** 是否使用重要性采样 */
    private importanceSampling;
    /** 精细步数 */
    private refineSteps;
    /** 分辨率缩放 */
    private resolutionScale;
    /** 是否显示丢失的光线 */
    private missedRays;
    /** SSGI通道 */
    private ssgiPass;
    /** 场景 */
    private scene;
    /** 相机 */
    private camera;
    /** 深度纹理 */
    private depthTexture;
    /** 法线纹理 */
    private normalTexture;
    /** 渲染目标 */
    private renderTarget;
    /**
     * 创建SSGI效果
     * @param options SSGI效果选项
     */
    constructor(options?: SSGIEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 设置场景和相机
     * @param scene 场景
     * @param camera 相机
     */
    setSceneAndCamera(scene: THREE.Scene, camera: THREE.PerspectiveCamera | THREE.OrthographicCamera): void;
    /**
     * 设置法线纹理
     * @param texture 法线纹理
     */
    setNormalTexture(texture: THREE.Texture): void;
    /**
     * 设置强度
     * @param intensity 强度
     */
    setIntensity(intensity: number): void;
    /**
     * 获取强度
     * @returns 强度
     */
    getIntensity(): number;
    /**
     * 设置半径
     * @param radius 半径
     */
    setRadius(radius: number): void;
    /**
     * 获取半径
     * @returns 半径
     */
    getRadius(): number;
    /**
     * 设置偏移
     * @param bias 偏移
     */
    setBias(bias: number): void;
    /**
     * 获取偏移
     * @returns 偏移
     */
    getBias(): number;
    /**
     * 设置采样数
     * @param samples 采样数
     */
    setSamples(samples: number): void;
    /**
     * 获取采样数
     * @returns 采样数
     */
    getSamples(): number;
    /**
     * 设置衰减
     * @param falloff 衰减
     */
    setFalloff(falloff: number): void;
    /**
     * 获取衰减
     * @returns 衰减
     */
    getFalloff(): number;
    /**
     * 设置是否使用法线贴图
     * @param use 是否使用
     */
    setUseNormalMap(use: boolean): void;
    /**
     * 获取是否使用法线贴图
     * @returns 是否使用
     */
    isUseNormalMap(): boolean;
    /**
     * 设置是否使用深度贴图
     * @param use 是否使用
     */
    setUseDepthMap(use: boolean): void;
    /**
     * 获取是否使用深度贴图
     * @returns 是否使用
     */
    isUseDepthMap(): boolean;
    /**
     * 更新效果
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
    /**
     * 销毁效果
     */
    dispose(): void;
}
