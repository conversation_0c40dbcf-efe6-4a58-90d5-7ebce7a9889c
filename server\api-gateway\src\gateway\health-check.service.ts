import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { ServiceDiscoveryService } from './service-discovery.service';

export interface HealthCheckResult {
  serviceName: string;
  instanceId: string;
  healthy: boolean;
  responseTime: number;
  lastCheck: Date;
  error?: string;
  details?: Record<string, any>;
}

@Injectable()
export class HealthCheckService implements OnModuleDestroy {
  private readonly logger = new Logger(HealthCheckService.name);
  private readonly httpClient: AxiosInstance;
  private readonly healthCheckIntervals = new Map<string, NodeJS.Timeout>();
  private readonly healthResults = new Map<string, HealthCheckResult>();
  private readonly checkInterval: number;
  private readonly checkTimeout: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly serviceDiscovery: ServiceDiscoveryService,
  ) {
    this.checkInterval = this.configService.get('HEALTH_CHECK_INTERVAL', 30000); // 30秒
    this.checkTimeout = this.configService.get('HEALTH_CHECK_TIMEOUT', 5000); // 5秒

    this.httpClient = axios.create({
      timeout: this.checkTimeout,
      headers: {
        'User-Agent': 'API-Gateway-Health-Checker/1.0',
      },
    });
  }

  onModuleDestroy() {
    // 清理所有健康检查定时器
    for (const interval of this.healthCheckIntervals.values()) {
      clearInterval(interval);
    }
    this.healthCheckIntervals.clear();
    this.logger.log('健康检查服务已关闭');
  }

  /**
   * 开始对指定服务的健康检查
   */
  startHealthCheck(serviceName: string): void {
    // 如果已经在检查，先停止
    this.stopHealthCheck(serviceName);

    const interval = setInterval(async () => {
      await this.checkServiceHealth(serviceName);
    }, this.checkInterval);

    this.healthCheckIntervals.set(serviceName, interval);
    this.logger.log(`开始健康检查: ${serviceName}, 间隔: ${this.checkInterval}ms`);

    // 立即执行一次检查
    this.checkServiceHealth(serviceName).catch(error => {
      this.logger.error(`初始健康检查失败: ${serviceName}`, error);
    });
  }

  /**
   * 停止对指定服务的健康检查
   */
  stopHealthCheck(serviceName: string): void {
    const interval = this.healthCheckIntervals.get(serviceName);
    if (interval) {
      clearInterval(interval);
      this.healthCheckIntervals.delete(serviceName);
      this.logger.log(`停止健康检查: ${serviceName}`);
    }
  }

  /**
   * 检查服务健康状态
   */
  async checkServiceHealth(serviceName: string): Promise<void> {
    try {
      const instances = await this.serviceDiscovery.getServiceInstances(serviceName);
      
      for (const instance of instances) {
        await this.checkInstanceHealth(serviceName, instance);
      }
    } catch (error) {
      this.logger.error(`检查服务健康状态失败: ${serviceName}`, error);
    }
  }

  /**
   * 检查单个实例健康状态
   */
  private async checkInstanceHealth(serviceName: string, instance: any): Promise<void> {
    const startTime = Date.now();
    const instanceKey = `${serviceName}:${instance.id}`;
    
    try {
      const healthUrl = `http://${instance.address}:${instance.port}/health`;
      const response = await this.httpClient.get(healthUrl);
      
      const responseTime = Date.now() - startTime;
      const isHealthy = response.status === 200 && response.data?.status === 'ok';

      const result: HealthCheckResult = {
        serviceName,
        instanceId: instance.id,
        healthy: isHealthy,
        responseTime,
        lastCheck: new Date(),
        details: response.data,
      };

      this.healthResults.set(instanceKey, result);
      
      // 更新服务发现中的健康状态
      this.serviceDiscovery.updateServiceHealth(serviceName, instance.id, isHealthy);

      if (!isHealthy) {
        this.logger.warn(`服务实例不健康: ${instanceKey}`, {
          status: response.status,
          data: response.data,
        });
      } else {
        this.logger.debug(`服务实例健康: ${instanceKey}, 响应时间: ${responseTime}ms`);
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      const result: HealthCheckResult = {
        serviceName,
        instanceId: instance.id,
        healthy: false,
        responseTime,
        lastCheck: new Date(),
        error: error.message,
      };

      this.healthResults.set(instanceKey, result);
      
      // 更新服务发现中的健康状态
      this.serviceDiscovery.updateServiceHealth(serviceName, instance.id, false);

      this.logger.warn(`服务实例健康检查失败: ${instanceKey}`, {
        error: error.message,
        responseTime,
      });
    }
  }

  /**
   * 获取服务健康状态
   */
  async getServiceHealth(serviceName: string): Promise<HealthCheckResult[]> {
    const results: HealthCheckResult[] = [];
    
    for (const [key, result] of this.healthResults) {
      if (result.serviceName === serviceName) {
        results.push(result);
      }
    }

    return results;
  }

  /**
   * 检查服务是否健康
   */
  async isServiceHealthy(serviceName: string): Promise<boolean> {
    const results = await this.getServiceHealth(serviceName);
    
    if (results.length === 0) {
      return false; // 没有实例
    }

    // 至少有一个健康的实例
    return results.some(result => result.healthy);
  }

  /**
   * 获取所有健康检查结果
   */
  getAllHealthResults(): HealthCheckResult[] {
    return Array.from(this.healthResults.values());
  }

  /**
   * 获取健康检查统计信息
   */
  getHealthStats(): {
    totalServices: number;
    totalInstances: number;
    healthyInstances: number;
    unhealthyInstances: number;
    averageResponseTime: number;
    serviceDetails: Array<{
      serviceName: string;
      instanceCount: number;
      healthyCount: number;
      averageResponseTime: number;
    }>;
  } {
    const results = this.getAllHealthResults();
    const serviceMap = new Map<string, HealthCheckResult[]>();

    // 按服务分组
    for (const result of results) {
      const serviceResults = serviceMap.get(result.serviceName) || [];
      serviceResults.push(result);
      serviceMap.set(result.serviceName, serviceResults);
    }

    const totalInstances = results.length;
    const healthyInstances = results.filter(r => r.healthy).length;
    const unhealthyInstances = totalInstances - healthyInstances;
    const averageResponseTime = results.length > 0 
      ? results.reduce((sum, r) => sum + r.responseTime, 0) / results.length 
      : 0;

    const serviceDetails = Array.from(serviceMap.entries()).map(([serviceName, serviceResults]) => ({
      serviceName,
      instanceCount: serviceResults.length,
      healthyCount: serviceResults.filter(r => r.healthy).length,
      averageResponseTime: serviceResults.reduce((sum, r) => sum + r.responseTime, 0) / serviceResults.length,
    }));

    return {
      totalServices: serviceMap.size,
      totalInstances,
      healthyInstances,
      unhealthyInstances,
      averageResponseTime,
      serviceDetails,
    };
  }

  /**
   * 手动触发健康检查
   */
  async triggerHealthCheck(serviceName?: string): Promise<void> {
    if (serviceName) {
      await this.checkServiceHealth(serviceName);
    } else {
      // 检查所有服务
      const services = Array.from(this.healthCheckIntervals.keys());
      await Promise.all(services.map(service => this.checkServiceHealth(service)));
    }
  }

  /**
   * 清理过期的健康检查结果
   */
  cleanupStaleResults(): void {
    const staleThreshold = this.configService.get('HEALTH_RESULT_STALE_THRESHOLD', 300000); // 5分钟
    const now = new Date();

    for (const [key, result] of this.healthResults) {
      const timeSinceLastCheck = now.getTime() - result.lastCheck.getTime();
      if (timeSinceLastCheck > staleThreshold) {
        this.healthResults.delete(key);
      }
    }
  }

  /**
   * 获取服务的健康趋势
   */
  getHealthTrend(serviceName: string, instanceId?: string): {
    serviceName: string;
    instanceId?: string;
    healthyChecks: number;
    totalChecks: number;
    healthRate: number;
    averageResponseTime: number;
  } {
    // 这里应该从历史数据中计算趋势
    // 简化实现，只返回当前状态
    const results = instanceId 
      ? this.getAllHealthResults().filter(r => r.serviceName === serviceName && r.instanceId === instanceId)
      : this.getAllHealthResults().filter(r => r.serviceName === serviceName);

    const healthyChecks = results.filter(r => r.healthy).length;
    const totalChecks = results.length;
    const healthRate = totalChecks > 0 ? healthyChecks / totalChecks : 0;
    const averageResponseTime = totalChecks > 0 
      ? results.reduce((sum, r) => sum + r.responseTime, 0) / totalChecks 
      : 0;

    return {
      serviceName,
      instanceId,
      healthyChecks,
      totalChecks,
      healthRate,
      averageResponseTime,
    };
  }
}
