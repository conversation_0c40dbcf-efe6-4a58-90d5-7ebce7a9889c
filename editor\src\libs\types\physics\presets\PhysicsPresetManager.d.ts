import type { Entity } from '../../core/Entity';
import { PhysicsPreset } from './PhysicsPreset';
import { EventEmitter } from '../../utils/EventEmitter';
/**
 * 物理预设管理器
 */
export declare class PhysicsPresetManager extends EventEmitter {
    /** 预设映射 */
    private presets;
    /** 预设类别 */
    private categories;
    /** 单例实例 */
    private static instance;
    /**
     * 获取单例实例
     * @returns 物理预设管理器实例
     */
    static getInstance(): PhysicsPresetManager;
    /**
     * 创建物理预设管理器
     */
    private constructor();
    /**
     * 初始化默认预设
     */
    private initializeDefaultPresets;
    /**
     * 添加预设
     * @param preset 物理预设
     */
    addPreset(preset: PhysicsPreset): void;
    /**
     * 获取预设
     * @param name 预设名称
     * @returns 物理预设
     */
    getPreset(name: string): PhysicsPreset | undefined;
    /**
     * 获取所有预设
     * @returns 所有物理预设
     */
    getAllPresets(): PhysicsPreset[];
    /**
     * 获取指定类别的预设
     * @param category 预设类别
     * @returns 指定类别的物理预设
     */
    getPresetsByCategory(category: string): PhysicsPreset[];
    /**
     * 获取所有预设类别
     * @returns 所有预设类别
     */
    getAllCategories(): string[];
    /**
     * 删除预设
     * @param name 预设名称
     * @returns 是否删除成功
     */
    removePreset(name: string): boolean;
    /**
     * 更新类别
     */
    private updateCategories;
    /**
     * 应用物理体预设到实体
     * @param entity 实体
     * @param presetName 预设名称
     * @returns 是否应用成功
     */
    applyBodyPreset(entity: Entity, presetName: string): boolean;
    /**
     * 应用碰撞器预设到实体
     * @param entity 实体
     * @param presetName 预设名称
     * @returns 是否应用成功
     */
    applyColliderPreset(entity: Entity, presetName: string): boolean;
    /**
     * 应用物理世界预设到实体
     * @param entity 实体
     * @param presetName 预设名称
     * @returns 是否应用成功
     */
    applyWorldPreset(entity: Entity, presetName: string): boolean;
    /**
     * 从实体创建物理体预设
     * @param entity 实体
     * @param name 预设名称
     * @param description 预设描述
     * @param category 预设类别
     * @returns 创建的预设
     */
    createBodyPresetFromEntity(entity: Entity, name: string, description?: string, category?: string): PhysicsPreset | null;
    /**
     * 从实体创建物理世界预设
     * @param entity 实体
     * @param name 预设名称
     * @param description 预设描述
     * @param category 预设类别
     * @returns 创建的预设
     */
    createWorldPresetFromEntity(entity: Entity, name: string, description?: string, category?: string): PhysicsPreset | null;
    /**
     * 序列化预设为JSON
     * @param name 预设名称
     * @returns JSON字符串
     */
    serializePreset(name: string): string;
    /**
     * 从JSON反序列化预设
     * @param json JSON字符串
     * @returns 反序列化的预设
     */
    deserializePreset(json: string): PhysicsPreset | null;
    /**
     * 导出所有预设为JSON
     * @returns JSON字符串
     */
    exportAllPresets(): string;
    /**
     * 导入预设
     * @param json JSON字符串
     * @returns 导入的预设数量
     */
    importPresets(json: string): number;
}
