/**
 * 输入绑定
 * 用于将输入映射绑定到输入动作
 */

/**
 * 输入绑定接口
 */
export interface IInputBinding {
  /**
   * 获取绑定名称
   * @returns 绑定名称
   */
  getName(): string;

  /**
   * 获取映射名称
   * @returns 映射名称
   */
  getMappingName(): string;
}

/**
 * 输入绑定
 */
export class InputBinding implements IInputBinding {
  /** 绑定名称 */
  private name: string;

  /** 映射名称 */
  private mappingName: string;

  /**
   * 创建输入绑定
   * @param name 绑定名称
   * @param mappingName 映射名称
   */
  constructor(name: string, mappingName: string) {
    this.name = name;
    this.mappingName = mappingName;
  }

  /**
   * 获取绑定名称
   * @returns 绑定名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 获取映射名称
   * @returns 映射名称
   */
  public getMappingName(): string {
    return this.mappingName;
  }
}

/**
 * 组合输入绑定
 * 用于将多个输入映射绑定到一个输入动作
 */
export class CompositeInputBinding implements IInputBinding {
  /** 绑定名称 */
  private name: string;

  /** 映射名称列表 */
  private mappingNames: string[];

  /** 组合类型 */
  private compositeType: CompositeBindingType;

  /**
   * 创建组合输入绑定
   * @param name 绑定名称
   * @param mappingNames 映射名称列表
   * @param compositeType 组合类型
   */
  constructor(name: string, mappingNames: string[], compositeType: CompositeBindingType = CompositeBindingType.ANY) {
    this.name = name;
    this.mappingNames = mappingNames;
    this.compositeType = compositeType;
  }

  /**
   * 获取绑定名称
   * @returns 绑定名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 获取映射名称
   * @returns 映射名称
   */
  public getMappingName(): string {
    return this.mappingNames[0];
  }

  /**
   * 获取所有映射名称
   * @returns 映射名称列表
   */
  public getMappingNames(): string[] {
    return this.mappingNames;
  }

  /**
   * 获取组合类型
   * @returns 组合类型
   */
  public getCompositeType(): CompositeBindingType {
    return this.compositeType;
  }
}

/**
 * 组合绑定类型
 */
export enum CompositeBindingType {
  /** 任意一个映射满足条件即可 */
  ANY = 'any',
  /** 所有映射都必须满足条件 */
  ALL = 'all',
  /** 按顺序优先使用第一个满足条件的映射 */
  PRIORITY = 'priority'
}
