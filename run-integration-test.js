#!/usr/bin/env node

/**
 * 前后端联调综合测试脚本
 * 先检查前端配置，再测试前后端连接
 */

const { runChecks } = require('./check-frontend-config');
const { runTests } = require('./test-frontend-backend-integration');

async function main() {
  console.log('🚀 开始前后端联调综合测试\n');
  
  // 第一步：检查前端配置
  console.log('📋 第一步：检查前端配置');
  console.log('=' * 50);
  
  const configOk = runChecks();
  
  if (!configOk) {
    console.log('\n❌ 前端配置检查失败，请先修复配置问题');
    process.exit(1);
  }
  
  console.log('\n' + '=' * 50);
  console.log('📋 第二步：测试前后端连接');
  console.log('=' * 50);
  
  // 第二步：测试前后端连接
  try {
    await runTests();
    console.log('\n🎉 前后端联调测试全部通过！');
  } catch (error) {
    console.log('\n❌ 前后端联调测试失败');
    console.error(error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  });
}
