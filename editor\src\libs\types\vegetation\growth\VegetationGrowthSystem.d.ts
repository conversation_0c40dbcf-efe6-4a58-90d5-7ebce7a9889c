/**
 * 植被生长系统
 * 用于实现植物的动态生长系统
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { VegetationComponent } from '../components/VegetationComponent';
import { TerrainComponent } from '../../terrain/components/TerrainComponent';
import { VegetationType, VegetationGrowthStage, SeasonType } from '../ecosystem/EcosystemSimulationSystem';
/**
 * 植被生长系统配置
 */
export interface VegetationGrowthSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 是否使用季节变化 */
    useSeasonalChanges?: boolean;
    /** 是否使用生长阶段 */
    useGrowthStages?: boolean;
    /** 是否使用年龄系统 */
    useAgeSystem?: boolean;
    /** 是否使用健康系统 */
    useHealthSystem?: boolean;
    /** 是否使用环境影响 */
    useEnvironmentInfluence?: boolean;
    /** 生长速度倍率 */
    growthSpeedMultiplier?: number;
}
/**
 * 植被生长系统事件类型
 */
export declare enum VegetationGrowthSystemEventType {
    /** 植被生长 */
    VEGETATION_GROWTH = "vegetation_growth",
    /** 植被生长阶段变化 */
    GROWTH_STAGE_CHANGED = "growth_stage_changed",
    /** 植被死亡 */
    VEGETATION_DEATH = "vegetation_death",
    /** 植被繁殖 */
    VEGETATION_REPRODUCTION = "vegetation_reproduction"
}
/**
 * 植被实例数据
 */
export interface VegetationInstanceData {
    /** 实例ID */
    id: string;
    /** 植被类型 */
    type: VegetationType;
    /** 生长阶段 */
    growthStage: VegetationGrowthStage;
    /** 年龄（天） */
    age: number;
    /** 健康度 */
    health: number;
    /** 最大寿命（天） */
    maxAge: number;
    /** 生长速度 */
    growthRate: number;
    /** 当前高度 */
    currentHeight: number;
    /** 最大高度 */
    maxHeight: number;
    /** 当前宽度 */
    currentWidth: number;
    /** 最大宽度 */
    maxWidth: number;
    /** 当前缩放 */
    currentScale: THREE.Vector3;
    /** 最大缩放 */
    maxScale: THREE.Vector3;
    /** 位置 */
    position: THREE.Vector3;
    /** 旋转 */
    rotation: THREE.Euler;
    /** 是否常绿 */
    evergreen: boolean;
    /** 是否开花 */
    flowering: boolean;
    /** 开花季节 */
    floweringSeason: SeasonType[];
    /** 是否结果 */
    fruiting: boolean;
    /** 结果季节 */
    fruitingSeason: SeasonType[];
    /** 是否落叶 */
    deciduous: boolean;
    /** 落叶季节 */
    leafFallSeason: SeasonType[];
    /** 自定义数据 */
    userData: any;
}
/**
 * 植被生长系统
 */
export declare class VegetationGrowthSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 配置 */
    private config;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 帧计数器 */
    private frameCount;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 是否使用季节变化 */
    private useSeasonalChanges;
    /** 是否使用生长阶段 */
    private useGrowthStages;
    /** 是否使用年龄系统 */
    private useAgeSystem;
    /** 是否使用健康系统 */
    private useHealthSystem;
    /** 是否使用环境影响 */
    private useEnvironmentInfluence;
    /** 生长速度倍率 */
    private growthSpeedMultiplier;
    /** 植被组件列表 */
    private vegetationComponents;
    /** 地形组件列表 */
    private terrainComponents;
    /** 植被实例数据映射 */
    private instanceDataMap;
    /** 当前季节 */
    private currentSeason;
    /** 季节变化计时器 */
    private seasonChangeTimer;
    /** 季节持续时间（秒） */
    private seasonDuration;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监控器 */
    private performanceMonitor;
    /** 调试网格列表 */
    private debugMeshes;
    /** 当前时间 */
    private time;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: VegetationGrowthSystemConfig);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 销毁系统
     */
    destroy(): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 实体添加事件处理
     * @param entity 实体
     */
    private onEntityAdded;
    /**
     * 实体移除事件处理
     * @param entity 实体
     */
    private onEntityRemoved;
    /**
     * 添加植被组件
     * @param entity 实体
     * @param component 植被组件
     */
    addVegetationComponent(entity: Entity, component: VegetationComponent): void;
    /**
     * 移除植被组件
     * @param entity 实体
     */
    removeVegetationComponent(entity: Entity): void;
    /**
     * 添加地形组件
     * @param entity 实体
     * @param component 地形组件
     */
    addTerrainComponent(entity: Entity, component: TerrainComponent): void;
    /**
     * 移除地形组件
     * @param entity 实体
     */
    removeTerrainComponent(entity: Entity): void;
    /**
     * 初始化植被实例数据
     * @param _entity 实体
     * @param component 植被组件
     */
    private initVegetationInstanceData;
    /**
     * 更新植被组件
     * @param entity 实体
     * @param component 植被组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateVegetationComponent;
    /**
     * 更新植被实例
     * @param entity 实体
     * @param component 植被组件
     * @param instanceId 实例ID
     * @param instance 实例
     * @param instanceData 实例数据
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateVegetationInstance;
    /**
     * 更新年龄
     * @param instanceData 实例数据
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateAge;
    /**
     * 更新健康度
     * @param instanceData 实例数据
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateHealth;
    /**
     * 更新生长阶段
     * @param instanceData 实例数据
     */
    private updateGrowthStage;
    /**
     * 更新季节性特征
     * @param instanceData 实例数据
     * @param season 季节
     */
    private updateSeasonalFeatures;
    /**
     * 更新大小
     * @param instanceData 实例数据
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateSize;
    /**
     * 更新实例变换
     * @param _entity 实体
     * @param _component 植被组件
     * @param _instanceId 实例ID
     * @param instance 实例
     * @param instanceData 实例数据
     */
    private updateInstanceTransform;
    /**
     * 更新季节
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateSeason;
    /**
     * 从模型路径推断植被类型
     * @param modelPath 模型路径
     * @returns 植被类型枚举
     */
    private getVegetationTypeFromModel;
    /**
     * 获取最大寿命
     * @param type 植被类型
     * @returns 最大寿命（天）
     */
    private getMaxAge;
    /**
     * 是否常绿
     * @param type 植被类型
     * @returns 是否常绿
     */
    private isEvergreen;
    /**
     * 获取开花季节
     * @param type 植被类型
     * @returns 开花季节
     */
    private getFloweringSeason;
    /**
     * 获取结果季节
     * @param type 植被类型
     * @returns 结果季节
     */
    private getFruitingSeason;
    /**
     * 初始化调试可视化
     */
    private initDebugVisualization;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 清除调试网格
     */
    private clearDebugMeshes;
    /**
     * 获取季节颜色
     * @param season 季节
     * @returns 颜色
     */
    private getSeasonColor;
}
