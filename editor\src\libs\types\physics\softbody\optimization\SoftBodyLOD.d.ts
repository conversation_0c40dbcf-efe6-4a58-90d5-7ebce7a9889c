import { SoftBodyComponent } from '../SoftBodyComponent';
import type { Entity } from '../../../core/Entity';
/**
 * LOD级别
 */
export declare enum LODLevel {
    /** 高细节 */
    HIGH = 0,
    /** 中细节 */
    MEDIUM = 1,
    /** 低细节 */
    LOW = 2,
    /** 极低细节 */
    VERY_LOW = 3
}
/**
 * LOD配置
 */
export interface LODConfig {
    /** 高细节距离阈值 */
    highDetailDistance: number;
    /** 中细节距离阈值 */
    mediumDetailDistance: number;
    /** 低细节距离阈值 */
    lowDetailDistance: number;
    /** 高细节网格分辨率 */
    highDetailResolution: {
        x: number;
        y: number;
    };
    /** 中细节网格分辨率 */
    mediumDetailResolution: {
        x: number;
        y: number;
    };
    /** 低细节网格分辨率 */
    lowDetailResolution: {
        x: number;
        y: number;
    };
    /** 极低细节网格分辨率 */
    veryLowDetailResolution: {
        x: number;
        y: number;
    };
    /** 高细节迭代次数 */
    highDetailIterations: number;
    /** 中细节迭代次数 */
    mediumDetailIterations: number;
    /** 低细节迭代次数 */
    lowDetailIterations: number;
    /** 极低细节迭代次数 */
    veryLowDetailIterations: number;
}
/**
 * 软体物理LOD系统选项
 */
export interface SoftBodyLODOptions {
    /** 是否启用LOD */
    enabled?: boolean;
    /** LOD配置 */
    config?: Partial<LODConfig>;
    /** 相机实体 */
    cameraEntity?: Entity;
}
/**
 * 软体物理LOD系统
 * 根据与相机的距离动态调整软体物理的细节层次
 */
export declare class SoftBodyLOD {
    /** 是否启用LOD */
    private enabled;
    /** LOD配置 */
    private config;
    /** 相机实体 */
    private cameraEntity;
    /** 软体组件映射 */
    private softBodies;
    /** 默认LOD配置 */
    private static readonly DEFAULT_CONFIG;
    /**
     * 创建软体物理LOD系统
     * @param options 软体物理LOD系统选项
     */
    constructor(options?: SoftBodyLODOptions);
    /**
     * 设置相机实体
     * @param cameraEntity 相机实体
     */
    setCameraEntity(cameraEntity: Entity): void;
    /**
     * 添加软体组件
     * @param softBody 软体组件
     */
    addSoftBody(softBody: SoftBodyComponent): void;
    /**
     * 移除软体组件
     * @param softBody 软体组件
     */
    removeSoftBody(softBody: SoftBodyComponent): void;
    /**
     * 获取软体组件的LOD级别
     * @param softBody 软体组件
     * @returns LOD级别
     */
    getLODLevel(softBody: SoftBodyComponent): LODLevel;
    /**
     * 获取LOD级别的迭代次数
     * @param level LOD级别
     * @returns 迭代次数
     */
    getIterationsForLevel(level: LODLevel): number;
    /**
     * 获取LOD级别的网格分辨率
     * @param level LOD级别
     * @returns 网格分辨率
     */
    getResolutionForLevel(level: LODLevel): {
        x: number;
        y: number;
    };
    /**
     * 更新软体组件的LOD级别
     * @param softBody 软体组件
     */
    updateSoftBodyLOD(softBody: SoftBodyComponent): void;
    /**
     * 应用软体组件的LOD级别
     * @param softBody 软体组件
     * @param level LOD级别
     */
    private applySoftBodyLOD;
    /**
     * 应用布料的LOD级别
     * @param _softBody 软体组件（暂未使用）
     * @param _level LOD级别（暂未使用）
     */
    private applyClothLOD;
    /**
     * 应用绳索的LOD级别
     * @param _softBody 软体组件（暂未使用）
     * @param _level LOD级别（暂未使用）
     */
    private applyRopeLOD;
    /**
     * 应用体积软体的LOD级别
     * @param _softBody 软体组件（暂未使用）
     * @param _level LOD级别（暂未使用）
     */
    private applyVolumeLOD;
    /**
     * 更新所有软体组件
     */
    update(): void;
    /**
     * 启用LOD系统
     */
    enable(): void;
    /**
     * 禁用LOD系统
     */
    disable(): void;
    /**
     * 设置LOD配置
     * @param config LOD配置
     */
    setConfig(config: Partial<LODConfig>): void;
}
