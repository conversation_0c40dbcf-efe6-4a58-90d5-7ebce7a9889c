/**
 * 视觉脚本网络节点
 * 提供网络通信和多用户交互相关的节点
 */
import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { EventNode, EventNodeOptions } from '../nodes/EventNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 网络连接节点
 * 连接到网络服务器
 */
export declare class ConnectToServerNode extends FlowNode {
    /**
     * 构造函数
     * @param options 节点选项
     */
    constructor(options: FlowNodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 发送网络消息节点
 * 向其他用户发送网络消息
 */
export declare class SendNetworkMessageNode extends FlowNode {
    /**
     * 构造函数
     * @param options 节点选项
     */
    constructor(options: FlowNodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 网络消息接收事件节点
 * 当接收到网络消息时触发
 */
export declare class OnNetworkMessageNode extends EventNode {
    /**
     * 构造函数
     * @param options 节点选项
     */
    constructor(options: EventNodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 初始化事件
     */
    initialize(): void;
    /**
     * 网络消息处理
     * @param message 网络消息
     * @param senderId 发送者ID
     */
    private onNetworkMessage;
    /**
     * 清理事件
     */
    cleanup(): void;
}
/**
 * 注册网络节点
 * @param registry 节点注册表
 */
export declare function registerNetworkNodes(registry: NodeRegistry): void;
