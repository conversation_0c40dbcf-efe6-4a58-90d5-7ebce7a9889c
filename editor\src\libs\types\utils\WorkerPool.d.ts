/**
 * 工作线程池
 * 用于管理和分配工作线程
 */
export declare class WorkerPool {
    /** 最大工作线程数量 */
    private limit;
    /** 任务队列 */
    private queue;
    /** 工作线程 */
    private workers;
    /** 工作线程解析函数 */
    private workersResolve;
    /** 工作线程状态 */
    private workerStatus;
    /** 工作线程创建器 */
    private workerCreator?;
    /**
     * 构造函数
     * @param pool 工作线程数量
     */
    constructor(pool?: number);
    /**
     * 初始化工作线程
     * @param workerId 工作线程ID
     */
    private _initWorker;
    /**
     * 获取空闲工作线程
     * @returns 工作线程ID
     */
    private _getIdleWorker;
    /**
     * 消息处理函数
     * @param workerId 工作线程ID
     * @param msg 消息
     */
    private _onMessage;
    /**
     * 设置工作线程创建器
     * @param workerCreator 工作线程创建器
     */
    setWorkerCreator(workerCreator: () => Worker): void;
    /**
     * 设置工作线程数量限制
     * @param pool 工作线程数量
     */
    setWorkerLimit(pool: number): void;
    /**
     * 发送消息
     * @param msg 消息
     * @param transfer 可转移对象
     * @returns Promise
     */
    postMessage<T = any>(msg: any, transfer?: Transferable[]): Promise<MessageEvent<T>>;
    /**
     * 销毁工作线程池
     */
    dispose(): void;
}
