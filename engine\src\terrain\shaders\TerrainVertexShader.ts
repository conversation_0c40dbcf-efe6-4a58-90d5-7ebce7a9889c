/**
 * 地形顶点着色器
 */
export const terrainVertexShader = `
// 基础参数
uniform vec2 uTerrainSize;
uniform float uMaxHeight;

// 纹理参数
uniform int uLayerCount;
uniform sampler2D uTextures[8];
uniform sampler2D uNormalMaps[8];
uniform sampler2D uRoughnessMaps[8];
uniform sampler2D uDisplacementMaps[8];
uniform sampler2D uAOMaps[8];
uniform float uTilingFactors[8];
uniform float uMinHeights[8];
uniform float uMaxHeights[8];
uniform float uMinSlopes[8];
uniform float uMaxSlopes[8];

// 混合参数
uniform sampler2D uBlendMaps[8];
uniform bool uUseBlendMaps;

// 光照参数
uniform vec3 uLightPosition;
uniform vec3 uLightColor;
uniform vec3 uAmbientColor;

// 雾参数
uniform vec3 uFogColor;
uniform float uFogNear;
uniform float uFogFar;
uniform bool uUseFog;

// 顶点属性
attribute vec3 position;
attribute vec3 normal;
attribute vec2 uv;
attribute vec4 tangent;

// 传递给片段着色器的变量
varying vec2 vUv;
varying vec3 vNormal;
varying vec3 vPosition;
varying vec3 vWorldPosition;
varying vec4 vTangent;
varying float vHeight;
varying float vSlope;
varying float vFogFactor;

// 计算斜度
float calculateSlope(vec3 normal) {
  // 计算法线与垂直向上方向的夹角
  float dotProduct = dot(normal, vec3(0.0, 1.0, 0.0));
  float angle = acos(dotProduct) * 180.0 / 3.14159265359;
  return angle;
}

void main() {
  // 计算UV坐标
  vUv = uv;
  
  // 计算法线和切线
  vNormal = normalize(normalMatrix * normal);
  vTangent = vec4(normalize(normalMatrix * tangent.xyz), tangent.w);
  
  // 计算顶点位置
  vec4 worldPosition = modelMatrix * vec4(position, 1.0);
  vWorldPosition = worldPosition.xyz;
  vPosition = position;
  
  // 计算高度和斜度
  vHeight = position.y / uMaxHeight;
  vSlope = calculateSlope(vNormal);
  
  // 计算雾因子
  if (uUseFog) {
    float fogDistance = length(worldPosition.xyz - cameraPosition);
    vFogFactor = smoothstep(uFogNear, uFogFar, fogDistance);
  } else {
    vFogFactor = 0.0;
  }
  
  // 输出裁剪空间坐标
  gl_Position = projectionMatrix * viewMatrix * worldPosition;
}
`;
