/**
 * 渲染系统兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';
import * as THREE from 'three';

/**
 * 渲染系统兼容性测试
 */
export const renderSystemTest: TestCase = {
  name: '渲染系统兼容性测试',
  description: '测试渲染系统功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目渲染系统实例
      const originalRenderSystem = new original.RenderSystem();
      
      // 创建重构后项目渲染系统实例
      const refactoredRenderSystem = new refactored.RenderSystem();
      
      // 检查渲染系统实例是否创建成功
      if (!originalRenderSystem || !refactoredRenderSystem) {
        return {
          name: '渲染系统兼容性测试',
          passed: false,
          errorMessage: '渲染系统实例创建失败'
        };
      }
      
      // 初始化渲染系统
      const canvas = document.createElement('canvas');
      canvas.width = 800;
      canvas.height = 600;
      
      originalRenderSystem.initialize({
        canvas,
        antialias: true,
        alpha: true
      });
      
      refactoredRenderSystem.initialize({
        canvas,
        antialias: true,
        alpha: true
      });
      
      // 检查渲染系统是否初始化成功
      if (!originalRenderSystem.isInitialized() || !refactoredRenderSystem.isInitialized()) {
        return {
          name: '渲染系统兼容性测试',
          passed: false,
          errorMessage: '渲染系统初始化失败',
          details: {
            originalInitialized: originalRenderSystem.isInitialized(),
            refactoredInitialized: refactoredRenderSystem.isInitialized()
          }
        };
      }
      
      // 创建场景
      const originalScene = new THREE.Scene();
      const refactoredScene = new THREE.Scene();
      
      // 创建相机
      const originalCamera = new THREE.PerspectiveCamera(75, 800 / 600, 0.1, 1000);
      const refactoredCamera = new THREE.PerspectiveCamera(75, 800 / 600, 0.1, 1000);
      
      originalCamera.position.z = 5;
      refactoredCamera.position.z = 5;
      
      // 创建几何体
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
      
      const originalCube = new THREE.Mesh(geometry, material);
      const refactoredCube = new THREE.Mesh(geometry.clone(), material.clone());
      
      originalScene.add(originalCube);
      refactoredScene.add(refactoredCube);
      
      // 设置场景和相机
      originalRenderSystem.setScene(originalScene);
      originalRenderSystem.setCamera(originalCamera);
      
      refactoredRenderSystem.setScene(refactoredScene);
      refactoredRenderSystem.setCamera(refactoredCamera);
      
      // 渲染场景
      originalRenderSystem.render();
      refactoredRenderSystem.render();
      
      // 检查渲染器属性
      const originalRenderer = originalRenderSystem.getRenderer();
      const refactoredRenderer = refactoredRenderSystem.getRenderer();
      
      if (!originalRenderer || !refactoredRenderer) {
        return {
          name: '渲染系统兼容性测试',
          passed: false,
          errorMessage: '获取渲染器失败',
          details: {
            originalRenderer,
            refactoredRenderer
          }
        };
      }
      
      // 检查渲染器尺寸
      const originalSize = originalRenderer.getSize(new THREE.Vector2());
      const refactoredSize = refactoredRenderer.getSize(new THREE.Vector2());
      
      if (originalSize.width !== refactoredSize.width || originalSize.height !== refactoredSize.height) {
        return {
          name: '渲染系统兼容性测试',
          passed: false,
          errorMessage: `渲染器尺寸不一致: 原有项目=${originalSize.width}x${originalSize.height}, 重构后项目=${refactoredSize.width}x${refactoredSize.height}`,
          details: {
            originalSize,
            refactoredSize
          }
        };
      }
      
      // 检查渲染器像素比
      const originalPixelRatio = originalRenderer.getPixelRatio();
      const refactoredPixelRatio = refactoredRenderer.getPixelRatio();
      
      if (originalPixelRatio !== refactoredPixelRatio) {
        return {
          name: '渲染系统兼容性测试',
          passed: false,
          errorMessage: `渲染器像素比不一致: 原有项目=${originalPixelRatio}, 重构后项目=${refactoredPixelRatio}`,
          details: {
            originalPixelRatio,
            refactoredPixelRatio
          }
        };
      }
      
      // 测试调整尺寸
      originalRenderSystem.resize(1024, 768);
      refactoredRenderSystem.resize(1024, 768);
      
      // 检查调整后的尺寸
      const originalResizedSize = originalRenderer.getSize(new THREE.Vector2());
      const refactoredResizedSize = refactoredRenderer.getSize(new THREE.Vector2());
      
      if (originalResizedSize.width !== refactoredResizedSize.width || originalResizedSize.height !== refactoredResizedSize.height) {
        return {
          name: '渲染系统兼容性测试',
          passed: false,
          errorMessage: `调整尺寸后不一致: 原有项目=${originalResizedSize.width}x${originalResizedSize.height}, 重构后项目=${refactoredResizedSize.width}x${refactoredResizedSize.height}`,
          details: {
            originalResizedSize,
            refactoredResizedSize
          }
        };
      }
      
      // 销毁渲染系统
      originalRenderSystem.dispose();
      refactoredRenderSystem.dispose();
      
      return {
        name: '渲染系统兼容性测试',
        passed: true,
        details: {
          originalSize,
          refactoredSize,
          originalResizedSize,
          refactoredResizedSize
        }
      };
    } catch (error) {
      return {
        name: '渲染系统兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
