/**
 * 事件类型定义
 */
import { Event } from '../event-bus.interface';

/**
 * 用户创建事件
 */
export interface UserCreatedEvent extends Event {
  data: {
    userId: string;
    username: string;
    email: string;
    createdAt: Date;
  };
}

/**
 * 用户更新事件
 */
export interface UserUpdatedEvent extends Event {
  data: {
    userId: string;
    username?: string;
    email?: string;
    updatedAt: Date;
  };
}

/**
 * 用户删除事件
 */
export interface UserDeletedEvent extends Event {
  data: {
    userId: string;
    deletedAt: Date;
  };
}

/**
 * 项目创建事件
 */
export interface ProjectCreatedEvent extends Event {
  data: {
    projectId: string;
    name: string;
    description?: string;
    ownerId: string;
    createdAt: Date;
  };
}

/**
 * 项目更新事件
 */
export interface ProjectUpdatedEvent extends Event {
  data: {
    projectId: string;
    name?: string;
    description?: string;
    updatedAt: Date;
  };
}

/**
 * 项目删除事件
 */
export interface ProjectDeletedEvent extends Event {
  data: {
    projectId: string;
    deletedAt: Date;
  };
}

/**
 * 资产上传事件
 */
export interface AssetUploadedEvent extends Event {
  data: {
    assetId: string;
    name: string;
    type: string;
    size: number;
    ownerId: string;
    projectId?: string;
    url: string;
    uploadedAt: Date;
  };
}

/**
 * 资产处理事件
 */
export interface AssetProcessedEvent extends Event {
  data: {
    assetId: string;
    processingResult: {
      success: boolean;
      metadata?: any;
      thumbnailUrl?: string;
      processedUrl?: string;
    };
    processedAt: Date;
  };
}

/**
 * 渲染请求事件
 */
export interface RenderRequestedEvent extends Event {
  data: {
    renderId: string;
    projectId: string;
    sceneId: string;
    userId: string;
    settings: {
      width: number;
      height: number;
      quality: string;
      format: string;
    };
    requestedAt: Date;
  };
}

/**
 * 渲染完成事件
 */
export interface RenderCompletedEvent extends Event {
  data: {
    renderId: string;
    outputUrl: string;
    duration: number;
    completedAt: Date;
  };
}

/**
 * 服务注册事件
 */
export interface ServiceRegisteredEvent extends Event {
  data: {
    serviceId: string;
    name: string;
    host: string;
    port: number;
    metadata?: any;
    registeredAt: Date;
  };
}

/**
 * 服务健康状态变更事件
 */
export interface ServiceHealthChangedEvent extends Event {
  data: {
    serviceId: string;
    status: 'healthy' | 'unhealthy';
    details?: string;
    timestamp: Date;
  };
}

/**
 * 事务开始事件
 */
export interface TransactionStartedEvent extends Event {
  data: {
    transactionId: string;
    initiator: string;
    participants: string[];
    startedAt: Date;
  };
}

/**
 * 事务准备事件
 */
export interface TransactionPreparedEvent extends Event {
  data: {
    transactionId: string;
    participantId: string;
    prepared: boolean;
    preparedAt: Date;
  };
}

/**
 * 事务提交事件
 */
export interface TransactionCommittedEvent extends Event {
  data: {
    transactionId: string;
    committedAt: Date;
  };
}

/**
 * 事务中止事件
 */
export interface TransactionAbortedEvent extends Event {
  data: {
    transactionId: string;
    reason: string;
    abortedAt: Date;
  };
}
