/**
 * 移动设备优化水体示例
 * 展示如何使用水体移动设备优化系统
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { RenderingSystem } from '../../engine/src/rendering/RenderingSystem';
import { CameraSystem } from '../../engine/src/camera/CameraSystem';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { PhysicsSystem } from '../../engine/src/physics/PhysicsSystem';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../engine/src/physics/water/WaterInteractionSystem';
import { UnderwaterParticleSystem } from '../../engine/src/rendering/water/UnderwaterParticleSystem';
import { WaterLightingSystem } from '../../engine/src/rendering/water/WaterLightingSystem';
import { WaterMobileOptimizationSystem } from '../../engine/src/rendering/water/WaterMobileOptimizationSystem';
import { WaterPresets, WaterPresetType } from '../../engine/src/physics/water/WaterPresets';
import { WaterBodyComponent } from '../../engine/src/physics/water/WaterBodyComponent';
import { WaterfallComponent } from '../../engine/src/physics/water/WaterfallComponent';
import { HotSpringComponent } from '../../engine/src/physics/water/HotSpringComponent';
import { PhysicsBodyComponent } from '../../engine/src/physics/PhysicsBodyComponent';
import { MeshComponent } from '../../engine/src/rendering/MeshComponent';
import { MaterialComponent } from '../../engine/src/rendering/MaterialComponent';
import { TransformComponent } from '../../engine/src/core/TransformComponent';
import { DeviceCapabilities, DevicePerformanceLevel } from '../../engine/src/utils/DeviceCapabilities';
import { Debug } from '../../engine/src/utils/Debug';

/**
 * 移动设备优化水体示例
 */
export class MobileOptimizedWaterExample {
  /** 世界 */
  private world: World;
  /** 渲染系统 */
  private renderingSystem: RenderingSystem;
  /** 相机系统 */
  private cameraSystem: CameraSystem;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水体交互系统 */
  private waterInteractionSystem: WaterInteractionSystem;
  /** 水下粒子系统 */
  private underwaterParticleSystem: UnderwaterParticleSystem;
  /** 水体光照系统 */
  private waterLightingSystem: WaterLightingSystem;
  /** 水体移动设备优化系统 */
  private waterMobileOptimizationSystem: WaterMobileOptimizationSystem;
  /** 水体列表 */
  private waterBodies: WaterBodyComponent[] = [];
  /** 测试物体列表 */
  private testObjects: Entity[] = [];
  /** 设备能力 */
  private deviceCapabilities: DeviceCapabilities;
  /** 性能级别 */
  private performanceLevel: DevicePerformanceLevel;
  /** 性能监控UI */
  private performanceMonitorUI: any;

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 获取设备能力
    this.deviceCapabilities = DeviceCapabilities.getInstance();

    // 获取性能级别
    this.performanceLevel = this.deviceCapabilities.getPerformanceLevel();

    // 创建系统
    this.createSystems();

    // 创建场景
    this.createScene();

    // 创建水体
    this.createWaterBodies();

    // 创建测试物体
    this.createTestObjects();

    // 创建性能监控UI
    this.createPerformanceMonitorUI();

    // 启动世界
    this.world.start();

    Debug.log('MobileOptimizedWaterExample', '移动设备优化水体示例已创建');
  }

  /**
   * 创建系统
   */
  private createSystems(): void {
    // 创建渲染系统
    this.renderingSystem = new RenderingSystem(this.world);
    this.world.addSystem(this.renderingSystem);

    // 创建相机系统
    this.cameraSystem = new CameraSystem(this.world);
    this.world.addSystem(this.cameraSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world);
    this.world.addSystem(this.inputSystem);

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem(this.world);
    this.world.addSystem(this.physicsSystem);

    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4,
      enableSpatialPartitioning: true,
      spatialGridSize: 10,
      enableAdaptiveUpdate: true,
      minUpdateFrequency: 1,
      maxUpdateFrequency: 10
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水体交互系统
    this.waterInteractionSystem = new WaterInteractionSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true
    });
    this.world.addSystem(this.waterInteractionSystem);

    // 创建水下粒子系统
    this.underwaterParticleSystem = new UnderwaterParticleSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      maxParticles: 1000
    });
    this.world.addSystem(this.underwaterParticleSystem);

    // 创建水体光照系统
    this.waterLightingSystem = new WaterLightingSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableCaustics: true,
      enableVolumetricLight: true,
      enableUnderwaterFog: true
    });
    this.world.addSystem(this.waterLightingSystem);

    // 创建水体移动设备优化系统
    this.waterMobileOptimizationSystem = new WaterMobileOptimizationSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 60,
      enableDebugVisualization: true,
      enablePerformanceMonitoring: true,
      enableAutoOptimization: true,
      enableBatteryOptimization: true,
      enableTemperatureOptimization: true,
      targetFPS: 60,
      minAcceptableFPS: 30,
      defaultPerformanceLevel: this.performanceLevel,
      lowBatteryThreshold: 20,
      highTemperatureThreshold: 40
    });
    this.world.addSystem(this.waterMobileOptimizationSystem);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建地面
    this.createGround();

    // 创建光源
    this.createLights();

    // 创建相机
    this.createCamera();
  }

  /**
   * 创建地面
   */
  private createGround(): void {
    // 创建地面实体
    const groundEntity = new Entity();
    groundEntity.setName('ground');

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(new THREE.Vector3(0, -5, 0));
    groundEntity.addComponent(transform);

    // 添加网格组件
    const mesh = new MeshComponent();
    mesh.setGeometry(new THREE.PlaneGeometry(200, 200));
    groundEntity.addComponent(mesh);

    // 添加材质组件
    const material = new MaterialComponent();
    material.setMaterial(new THREE.MeshStandardMaterial({
      color: 0x555555,
      roughness: 0.8,
      metalness: 0.2,
      side: THREE.DoubleSide
    }));
    groundEntity.addComponent(material);

    // 添加物理体组件
    const groundPhysics = new PhysicsBodyComponent();
    groundPhysics.setMass(0); // 静态物体
    groundPhysics.setShape('PLANE');
    groundEntity.addComponent(groundPhysics);

    // 添加到世界
    this.world.addEntity(groundEntity);
  }

  /**
   * 创建光源
   */
  private createLights(): void {
    // 创建环境光
    const ambientLightEntity = new Entity();
    ambientLightEntity.setName('ambient_light');
    const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
    ambientLightEntity.addObject(ambientLight);
    this.world.addEntity(ambientLightEntity);

    // 创建方向光
    const directionalLightEntity = new Entity();
    directionalLightEntity.setName('directional_light');
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 20, 10);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -25;
    directionalLight.shadow.camera.right = 25;
    directionalLight.shadow.camera.top = 25;
    directionalLight.shadow.camera.bottom = -25;
    directionalLightEntity.addObject(directionalLight);
    this.world.addEntity(directionalLightEntity);
  }

  /**
   * 创建相机
   */
  private createCamera(): void {
    // 创建相机实体
    const cameraEntity = new Entity();
    cameraEntity.setName('main_camera');

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(new THREE.Vector3(0, 10, 30));
    transform.setRotation(new THREE.Euler(-0.3, 0, 0));
    cameraEntity.addComponent(transform);

    // 创建相机
    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 1000);
    cameraEntity.addObject(camera);

    // 添加到世界
    this.world.addEntity(cameraEntity);

    // 设置为主相机
    this.cameraSystem.setMainCamera(camera);
  }

  /**
   * 创建水体
   */
  private createWaterBodies(): void {
    // 创建湖泊
    this.createLake();

    // 创建河流
    this.createRiver();

    // 创建瀑布
    this.createWaterfall();

    // 创建温泉
    this.createHotSpring();
  }

  /**
   * 创建湖泊
   */
  private createLake(): void {
    // 使用预设创建湖泊
    const lakeWaterBody = WaterPresets.createPreset(this.world, {
      type: WaterPresetType.LAKE,
      size: { width: 20, height: 5, depth: 20 },
      position: new THREE.Vector3(0, 0, 0)
    });

    // 添加到水体列表
    this.waterBodies.push(lakeWaterBody);

    // 添加到水体移动设备优化系统
    this.waterMobileOptimizationSystem.addWaterBody(lakeWaterBody.getEntity(), lakeWaterBody);
  }

  /**
   * 创建河流
   */
  private createRiver(): void {
    // 使用预设创建河流
    const riverWaterBody = WaterPresets.createPreset(this.world, {
      type: WaterPresetType.RIVER,
      size: { width: 5, height: 2, depth: 30 },
      position: new THREE.Vector3(-20, -1, 0)
    });

    // 设置流向和流速
    riverWaterBody.setFlowDirection(new THREE.Vector3(0, 0, 1));
    riverWaterBody.setFlowSpeed(1.0);

    // 添加到水体列表
    this.waterBodies.push(riverWaterBody);

    // 添加到水体移动设备优化系统
    this.waterMobileOptimizationSystem.addWaterBody(riverWaterBody.getEntity(), riverWaterBody);
  }

  /**
   * 创建瀑布
   */
  private createWaterfall(): void {
    // 创建瀑布实体
    const waterfallEntity = new Entity();
    waterfallEntity.setName('waterfall');

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(new THREE.Vector3(20, 5, 0));
    waterfallEntity.addComponent(transform);

    // 添加瀑布组件
    const waterfallComponent = new WaterfallComponent(waterfallEntity, {
      width: 5,
      height: 10,
      depth: 2,
      flowSpeed: 2.0,
      enableMistEffect: true,
      mistEffectStrength: 1.0,
      enableSplashEffect: true,
      splashEffectStrength: 1.0,
      enableSoundEffect: true,
      soundEffectVolume: 0.5
    });
    waterfallEntity.addComponent(waterfallComponent);

    // 添加到世界
    this.world.addEntity(waterfallEntity);

    // 添加到水体列表
    this.waterBodies.push(waterfallComponent);

    // 添加到水体移动设备优化系统
    this.waterMobileOptimizationSystem.addWaterBody(waterfallEntity, waterfallComponent);
  }

  /**
   * 创建温泉
   */
  private createHotSpring(): void {
    // 创建温泉实体
    const hotSpringEntity = new Entity();
    hotSpringEntity.setName('hot_spring');

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(new THREE.Vector3(20, -2, -20));
    hotSpringEntity.addComponent(transform);

    // 添加温泉组件
    const hotSpringComponent = new HotSpringComponent(hotSpringEntity, {
      width: 8,
      height: 1,
      depth: 8,
      temperature: 60,
      color: new THREE.Color(0x66ccff),
      opacity: 0.8,
      enableBubbleEffect: true,
      bubbleEffectStrength: 1.0,
      enableSteamEffect: true,
      steamEffectStrength: 1.0,
      enableSoundEffect: true,
      soundEffectVolume: 0.5,
      enableHeatDiffusion: true,
      heatDiffusionRange: 5.0,
      enableMineralEffect: true,
      mineralColor: new THREE.Color(0xc0a080)
    });
    hotSpringEntity.addComponent(hotSpringComponent);

    // 添加到世界
    this.world.addEntity(hotSpringEntity);

    // 添加到水体列表
    this.waterBodies.push(hotSpringComponent);

    // 添加到水体移动设备优化系统
    this.waterMobileOptimizationSystem.addWaterBody(hotSpringEntity, hotSpringComponent);
  }

  /**
   * 创建测试物体
   */
  private createTestObjects(): void {
    // 创建不同形状的测试物体
    this.createBoxObject(new THREE.Vector3(0, 5, 0), new THREE.Vector3(1, 1, 1), 1);
    this.createSphereObject(new THREE.Vector3(3, 5, 0), 0.5, 1);
    this.createCylinderObject(new THREE.Vector3(-3, 5, 0), 0.5, 1, 10);
    this.createBoatObject(new THREE.Vector3(0, 5, 5));
  }

  /**
   * 创建盒子物体
   * @param position 位置
   * @param size 尺寸
   * @param mass 质量
   */
  private createBoxObject(position: THREE.Vector3, size: THREE.Vector3, mass: number): void {
    // 创建盒子实体
    const boxEntity = new Entity();
    boxEntity.setName(`box_${this.testObjects.length}`);

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(position);
    boxEntity.addComponent(transform);

    // 添加网格组件
    const mesh = new MeshComponent();
    mesh.setGeometry(new THREE.BoxGeometry(size.x, size.y, size.z));
    boxEntity.addComponent(mesh);

    // 添加材质组件
    const material = new MaterialComponent();
    material.setMaterial(new THREE.MeshStandardMaterial({
      color: 0xff0000,
      roughness: 0.7,
      metalness: 0.3
    }));
    boxEntity.addComponent(material);

    // 添加物理体组件
    const physics = new PhysicsBodyComponent();
    physics.setMass(mass);
    physics.setShape('BOX');
    physics.setSize(size);
    boxEntity.addComponent(physics);

    // 添加到世界
    this.world.addEntity(boxEntity);

    // 添加到测试物体列表
    this.testObjects.push(boxEntity);
  }

  /**
   * 创建球体物体
   * @param position 位置
   * @param radius 半径
   * @param mass 质量
   */
  private createSphereObject(position: THREE.Vector3, radius: number, mass: number): void {
    // 创建球体实体
    const sphereEntity = new Entity();
    sphereEntity.setName(`sphere_${this.testObjects.length}`);

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(position);
    sphereEntity.addComponent(transform);

    // 添加网格组件
    const mesh = new MeshComponent();
    mesh.setGeometry(new THREE.SphereGeometry(radius, 16, 16));
    sphereEntity.addComponent(mesh);

    // 添加材质组件
    const material = new MaterialComponent();
    material.setMaterial(new THREE.MeshStandardMaterial({
      color: 0x00ff00,
      roughness: 0.7,
      metalness: 0.3
    }));
    sphereEntity.addComponent(material);

    // 添加物理体组件
    const physics = new PhysicsBodyComponent();
    physics.setMass(mass);
    physics.setShape('SPHERE');
    physics.setRadius(radius);
    sphereEntity.addComponent(physics);

    // 添加到世界
    this.world.addEntity(sphereEntity);

    // 添加到测试物体列表
    this.testObjects.push(sphereEntity);
  }

  /**
   * 创建圆柱体物体
   * @param position 位置
   * @param radius 半径
   * @param height 高度
   * @param mass 质量
   */
  private createCylinderObject(position: THREE.Vector3, radius: number, height: number, mass: number): void {
    // 创建圆柱体实体
    const cylinderEntity = new Entity();
    cylinderEntity.setName(`cylinder_${this.testObjects.length}`);

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(position);
    cylinderEntity.addComponent(transform);

    // 添加网格组件
    const mesh = new MeshComponent();
    mesh.setGeometry(new THREE.CylinderGeometry(radius, radius, height, 16));
    cylinderEntity.addComponent(mesh);

    // 添加材质组件
    const material = new MaterialComponent();
    material.setMaterial(new THREE.MeshStandardMaterial({
      color: 0x0000ff,
      roughness: 0.7,
      metalness: 0.3
    }));
    cylinderEntity.addComponent(material);

    // 添加物理体组件
    const physics = new PhysicsBodyComponent();
    physics.setMass(mass);
    physics.setShape('CYLINDER');
    physics.setRadius(radius);
    physics.setHeight(height);
    cylinderEntity.addComponent(physics);

    // 添加到世界
    this.world.addEntity(cylinderEntity);

    // 添加到测试物体列表
    this.testObjects.push(cylinderEntity);
  }

  /**
   * 创建船体物体
   * @param position 位置
   */
  private createBoatObject(position: THREE.Vector3): void {
    // 创建船体实体
    const boatEntity = new Entity();
    boatEntity.setName(`boat_${this.testObjects.length}`);

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(position);
    boatEntity.addComponent(transform);

    // 创建船体几何体
    const hullGeometry = new THREE.BoxGeometry(3, 0.5, 1.5);
    const cabinGeometry = new THREE.BoxGeometry(1, 0.8, 1);

    // 创建船体材质
    const hullMaterial = new THREE.MeshStandardMaterial({
      color: 0x8b4513,
      roughness: 0.7,
      metalness: 0.3
    });
    const cabinMaterial = new THREE.MeshStandardMaterial({
      color: 0xd2b48c,
      roughness: 0.7,
      metalness: 0.3
    });

    // 创建船体网格
    const hullMesh = new THREE.Mesh(hullGeometry, hullMaterial);
    const cabinMesh = new THREE.Mesh(cabinGeometry, cabinMaterial);
    cabinMesh.position.set(0, 0.65, 0);

    // 创建船体组
    const boatGroup = new THREE.Group();
    boatGroup.add(hullMesh);
    boatGroup.add(cabinMesh);

    // 添加到实体
    boatEntity.addObject(boatGroup);

    // 添加物理体组件
    const physics = new PhysicsBodyComponent();
    physics.setMass(5);
    physics.setShape('BOX');
    physics.setSize(new THREE.Vector3(3, 1.3, 1.5));
    boatEntity.addComponent(physics);

    // 添加到世界
    this.world.addEntity(boatEntity);

    // 添加到测试物体列表
    this.testObjects.push(boatEntity);
  }

  /**
   * 创建性能监控UI
   */
  private createPerformanceMonitorUI(): void {
    // 创建性能监控UI容器
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.top = '10px';
    container.style.left = '10px';
    container.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    container.style.color = 'white';
    container.style.padding = '10px';
    container.style.borderRadius = '5px';
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.fontSize = '14px';
    container.style.zIndex = '1000';
    container.style.width = '300px';

    // 创建标题
    const title = document.createElement('h3');
    title.textContent = '水体移动设备优化';
    title.style.margin = '0 0 10px 0';
    title.style.textAlign = 'center';
    container.appendChild(title);

    // 创建性能信息
    const fpsInfo = document.createElement('div');
    fpsInfo.id = 'fps-info';
    fpsInfo.textContent = 'FPS: --';
    container.appendChild(fpsInfo);

    const cpuInfo = document.createElement('div');
    cpuInfo.id = 'cpu-info';
    cpuInfo.textContent = 'CPU: --';
    container.appendChild(cpuInfo);

    const gpuInfo = document.createElement('div');
    gpuInfo.id = 'gpu-info';
    gpuInfo.textContent = 'GPU: --';
    container.appendChild(gpuInfo);

    const memoryInfo = document.createElement('div');
    memoryInfo.id = 'memory-info';
    memoryInfo.textContent = '内存: --';
    container.appendChild(memoryInfo);

    const batteryInfo = document.createElement('div');
    batteryInfo.id = 'battery-info';
    batteryInfo.textContent = '电池: --';
    container.appendChild(batteryInfo);

    const temperatureInfo = document.createElement('div');
    temperatureInfo.id = 'temperature-info';
    temperatureInfo.textContent = '温度: --';
    container.appendChild(temperatureInfo);

    const performanceLevelInfo = document.createElement('div');
    performanceLevelInfo.id = 'performance-level-info';
    performanceLevelInfo.textContent = '性能级别: --';
    container.appendChild(performanceLevelInfo);

    // 创建性能级别按钮
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.justifyContent = 'space-between';
    buttonContainer.style.marginTop = '10px';
    container.appendChild(buttonContainer);

    const lowButton = document.createElement('button');
    lowButton.textContent = '低';
    lowButton.style.flex = '1';
    lowButton.style.margin = '0 5px';
    lowButton.style.padding = '5px';
    lowButton.style.backgroundColor = '#ff5555';
    lowButton.style.border = 'none';
    lowButton.style.borderRadius = '3px';
    lowButton.style.color = 'white';
    lowButton.style.cursor = 'pointer';
    lowButton.onclick = () => this.setPerformanceLevel(DevicePerformanceLevel.LOW);
    buttonContainer.appendChild(lowButton);

    const mediumButton = document.createElement('button');
    mediumButton.textContent = '中';
    mediumButton.style.flex = '1';
    mediumButton.style.margin = '0 5px';
    mediumButton.style.padding = '5px';
    mediumButton.style.backgroundColor = '#ffaa55';
    mediumButton.style.border = 'none';
    mediumButton.style.borderRadius = '3px';
    mediumButton.style.color = 'white';
    mediumButton.style.cursor = 'pointer';
    mediumButton.onclick = () => this.setPerformanceLevel(DevicePerformanceLevel.MEDIUM);
    buttonContainer.appendChild(mediumButton);

    const highButton = document.createElement('button');
    highButton.textContent = '高';
    highButton.style.flex = '1';
    highButton.style.margin = '0 5px';
    highButton.style.padding = '5px';
    highButton.style.backgroundColor = '#55aa55';
    highButton.style.border = 'none';
    highButton.style.borderRadius = '3px';
    highButton.style.color = 'white';
    highButton.style.cursor = 'pointer';
    highButton.onclick = () => this.setPerformanceLevel(DevicePerformanceLevel.HIGH);
    buttonContainer.appendChild(highButton);

    const autoButton = document.createElement('button');
    autoButton.textContent = '自动';
    autoButton.style.flex = '1';
    autoButton.style.margin = '0 5px';
    autoButton.style.padding = '5px';
    autoButton.style.backgroundColor = '#5555aa';
    autoButton.style.border = 'none';
    autoButton.style.borderRadius = '3px';
    autoButton.style.color = 'white';
    autoButton.style.cursor = 'pointer';
    autoButton.onclick = () => this.enableAutoOptimization();
    buttonContainer.appendChild(autoButton);

    // 添加到文档
    document.body.appendChild(container);

    // 保存UI引用
    this.performanceMonitorUI = {
      container,
      fpsInfo,
      cpuInfo,
      gpuInfo,
      memoryInfo,
      batteryInfo,
      temperatureInfo,
      performanceLevelInfo
    };

    // 开始更新UI
    this.updatePerformanceMonitorUI();
  }

  /**
   * 更新性能监控UI
   */
  private updatePerformanceMonitorUI(): void {
    // 如果没有性能监控UI，则不更新
    if (!this.performanceMonitorUI) return;

    // 获取监控数据
    const monitorData = this.waterMobileOptimizationSystem.getMonitorData();
    const performanceLevel = this.waterMobileOptimizationSystem.getCurrentPerformanceLevel();

    // 更新UI
    this.performanceMonitorUI.fpsInfo.textContent = `FPS: ${monitorData.fps.toFixed(1)}`;
    this.performanceMonitorUI.cpuInfo.textContent = `CPU: ${(monitorData.cpuUsage * 100).toFixed(1)}%`;
    this.performanceMonitorUI.gpuInfo.textContent = `GPU: ${(monitorData.gpuUsage * 100).toFixed(1)}%`;
    this.performanceMonitorUI.memoryInfo.textContent = `内存: ${(monitorData.memoryUsage * 100).toFixed(1)}%`;
    this.performanceMonitorUI.batteryInfo.textContent = `电池: ${monitorData.batteryLevel.toFixed(1)}%`;
    this.performanceMonitorUI.temperatureInfo.textContent = `温度: ${monitorData.temperature.toFixed(1)}°C`;

    // 更新性能级别
    let performanceLevelText = '';
    switch (performanceLevel) {
      case DevicePerformanceLevel.LOW:
        performanceLevelText = '低';
        break;
      case DevicePerformanceLevel.MEDIUM:
        performanceLevelText = '中';
        break;
      case DevicePerformanceLevel.HIGH:
        performanceLevelText = '高';
        break;
      default:
        performanceLevelText = '未知';
        break;
    }
    this.performanceMonitorUI.performanceLevelInfo.textContent = `性能级别: ${performanceLevelText}`;

    // 定时更新
    requestAnimationFrame(() => this.updatePerformanceMonitorUI());
  }

  /**
   * 设置性能级别
   * @param level 性能级别
   */
  private setPerformanceLevel(level: DevicePerformanceLevel): void {
    // 设置性能级别
    this.waterMobileOptimizationSystem.setPerformanceLevel(level);
  }

  /**
   * 启用自动优化
   */
  private enableAutoOptimization(): void {
    // 配置水体移动设备优化系统
    this.waterMobileOptimizationSystem.configure({
      enableAutoOptimization: true
    });
  }
}
