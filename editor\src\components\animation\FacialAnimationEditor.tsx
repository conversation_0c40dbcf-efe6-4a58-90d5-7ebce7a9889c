/**
 * 面部动画编辑器组件
 * 用于编辑和预览面部动画
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Tabs,
  Form,
  Input,
  Slider,
  Button,
  Select,
  Card,
  Divider,
  Typography,
  Tooltip,
  Switch,
  Upload,
  message
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  SaveOutlined,
  UploadOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { UploadFile } from 'antd/lib/upload/interface';
import './FacialAnimationEditor.less';


const { Option } = Select;
const { Title, Text } = Typography;

// 表情类型枚举
export enum FacialExpressionType {
  NEUTRAL = 'neutral',
  HAPPY = 'happy',
  SAD = 'sad',
  ANGRY = 'angry',
  SURPRISED = 'surprised',
  FEAR = 'fear',
  DISGUST = 'disgust',
  CONTEMPT = 'contempt',
  CUSTOM = 'custom'
}

// 口型类型枚举
export enum VisemeType {
  NEUTRAL = 'neutral',
  AA = 'aa',
  CH = 'ch',
  DD = 'dd',
  E = 'e',
  FF = 'ff',
  IH = 'ih',
  K = 'k',
  NN = 'nn',
  OH = 'oh',
  OU = 'ou',
  PP = 'pp',
  R = 'r',
  SS = 'ss',
  TH = 'th',
  CUSTOM = 'custom'
}

// 面部动画关键帧
interface FacialAnimationKeyframe {
  time: number;
  expressionType: FacialExpressionType;
  expressionWeight: number;
  visemeType: VisemeType;
  visemeWeight: number;
  customBlendShapes?: Record<string, number>;
}

// 面部动画片段
interface FacialAnimationClip {
  id: string;
  name: string;
  duration: number;
  keyframes: FacialAnimationKeyframe[];
  loop: boolean;
  audioFile?: string;
}

// 混合形状定义
interface BlendShapeDefinition {
  name: string;
  category: 'expression' | 'viseme' | 'other';
  defaultValue: number;
  minValue: number;
  maxValue: number;
}

interface FacialAnimationEditorProps {
  entityId?: string;
  onSave?: (clip: FacialAnimationClip) => void;
  onPreview?: (keyframe: FacialAnimationKeyframe) => void;
  availableBlendShapes?: BlendShapeDefinition[];
  initialClip?: FacialAnimationClip;
}

const FacialAnimationEditor: React.FC<FacialAnimationEditorProps> = ({
  entityId: _entityId,
  onSave,
  onPreview,
  availableBlendShapes = [],
  initialClip
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('expressions');
  const [currentClip, setCurrentClip] = useState<FacialAnimationClip>(initialClip || {
    id: `clip_${Date.now()}`,
    name: '新建面部动画',
    duration: 5.0,
    keyframes: [],
    loop: true
  });
  const [currentTime, setCurrentTime] = useState(0);
  const [playing, setPlaying] = useState(false);
  const [currentExpression, setCurrentExpression] = useState<FacialExpressionType>(FacialExpressionType.NEUTRAL);
  const [expressionWeight, setExpressionWeight] = useState(1.0);
  const [currentViseme, setCurrentViseme] = useState<VisemeType>(VisemeType.NEUTRAL);
  const [visemeWeight, setVisemeWeight] = useState(1.0);
  const [customBlendShapes, setCustomBlendShapes] = useState<Record<string, number>>({});
  const [audioFile, setAudioFile] = useState<UploadFile | null>(null);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);
  const [lipSyncEnabled, setLipSyncEnabled] = useState(false);
  const animationFrameRef = useRef<number | null>(null);
  const lastTimeRef = useRef<number>(0);

  // 初始化
  useEffect(() => {
    if (initialClip) {
      setCurrentClip(initialClip);
      
      // 如果有音频文件，创建音频元素
      if (initialClip.audioFile) {
        const audio = new Audio(initialClip.audioFile);
        setAudioElement(audio);
        setAudioFile({
          uid: '-1',
          name: initialClip.audioFile.split('/').pop() || 'audio.mp3',
          status: 'done',
          url: initialClip.audioFile
        });
      }
    }
    
    // 初始化自定义混合形状
    const initialCustomBlendShapes: Record<string, number> = {};
    availableBlendShapes.forEach(blendShape => {
      initialCustomBlendShapes[blendShape.name] = blendShape.defaultValue;
    });
    setCustomBlendShapes(initialCustomBlendShapes);
    
    return () => {
      // 清理
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      
      if (audioElement) {
        audioElement.pause();
        audioElement.currentTime = 0;
      }
    };
  }, [initialClip, availableBlendShapes]);

  // 动画循环
  const animate = (time: number) => {
    if (!lastTimeRef.current) {
      lastTimeRef.current = time;
    }
    
    const deltaTime = (time - lastTimeRef.current) / 1000;
    lastTimeRef.current = time;
    
    if (playing) {
      // 更新当前时间
      let newTime = currentTime + deltaTime;
      
      // 处理循环
      if (newTime >= currentClip.duration) {
        if (currentClip.loop) {
          newTime = newTime % currentClip.duration;
          
          // 如果有音频，重新开始播放
          if (audioElement) {
            audioElement.currentTime = 0;
          }
        } else {
          newTime = currentClip.duration;
          setPlaying(false);
          
          // 如果有音频，暂停播放
          if (audioElement) {
            audioElement.pause();
          }
        }
      }
      
      setCurrentTime(newTime);
      
      // 更新音频时间
      if (audioElement && Math.abs(audioElement.currentTime - newTime) > 0.1) {
        audioElement.currentTime = newTime;
      }
      
      // 查找当前关键帧
      updateCurrentKeyframe(newTime);
    }
    
    animationFrameRef.current = requestAnimationFrame(animate);
  };

  // 开始动画循环
  useEffect(() => {
    animationFrameRef.current = requestAnimationFrame(animate);
    return () => {
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [playing, currentTime, currentClip]);

  // 更新当前关键帧
  const updateCurrentKeyframe = (time: number) => {
    // 查找当前时间之前的最近关键帧和之后的最近关键帧
    const keyframes = [...currentClip.keyframes].sort((a, b) => a.time - b.time);
    
    if (keyframes.length === 0) return;
    
    let prevKeyframe: FacialAnimationKeyframe | null = null;
    let nextKeyframe: FacialAnimationKeyframe | null = null;
    
    for (let i = 0; i < keyframes.length; i++) {
      if (keyframes[i].time <= time) {
        prevKeyframe = keyframes[i];
      } else {
        nextKeyframe = keyframes[i];
        break;
      }
    }
    
    // 如果没有前一个关键帧，使用第一个关键帧
    if (!prevKeyframe && keyframes.length > 0) {
      prevKeyframe = keyframes[0];
    }
    
    // 如果没有后一个关键帧，使用最后一个关键帧
    if (!nextKeyframe && keyframes.length > 0) {
      nextKeyframe = keyframes[keyframes.length - 1];
    }
    
    // 如果只有一个关键帧，直接使用它
    if (prevKeyframe && !nextKeyframe) {
      setCurrentExpression(prevKeyframe.expressionType);
      setExpressionWeight(prevKeyframe.expressionWeight);
      setCurrentViseme(prevKeyframe.visemeType);
      setVisemeWeight(prevKeyframe.visemeWeight);
      
      if (prevKeyframe.customBlendShapes) {
        setCustomBlendShapes(prevKeyframe.customBlendShapes);
      }
      
      // 预览
      if (onPreview) {
        onPreview(prevKeyframe);
      }
      
      return;
    }
    
    // 如果有两个关键帧，进行插值
    if (prevKeyframe && nextKeyframe) {
      const t1 = prevKeyframe.time;
      const t2 = nextKeyframe.time;
      const t = (time - t1) / (t2 - t1);
      
      // 插值表情
      setCurrentExpression(prevKeyframe.expressionType);
      setExpressionWeight(lerp(prevKeyframe.expressionWeight, nextKeyframe.expressionWeight, t));
      
      // 插值口型
      setCurrentViseme(prevKeyframe.visemeType);
      setVisemeWeight(lerp(prevKeyframe.visemeWeight, nextKeyframe.visemeWeight, t));
      
      // 插值自定义混合形状
      if (prevKeyframe.customBlendShapes && nextKeyframe.customBlendShapes) {
        const interpolatedBlendShapes: Record<string, number> = {};
        
        for (const key in prevKeyframe.customBlendShapes) {
          if (nextKeyframe.customBlendShapes[key] !== undefined) {
            interpolatedBlendShapes[key] = lerp(
              prevKeyframe.customBlendShapes[key],
              nextKeyframe.customBlendShapes[key],
              t
            );
          } else {
            interpolatedBlendShapes[key] = prevKeyframe.customBlendShapes[key];
          }
        }
        
        setCustomBlendShapes(interpolatedBlendShapes);
      }
      
      // 创建插值关键帧用于预览
      const interpolatedKeyframe: FacialAnimationKeyframe = {
        time,
        expressionType: prevKeyframe.expressionType,
        expressionWeight: lerp(prevKeyframe.expressionWeight, nextKeyframe.expressionWeight, t),
        visemeType: prevKeyframe.visemeType,
        visemeWeight: lerp(prevKeyframe.visemeWeight, nextKeyframe.visemeWeight, t),
        customBlendShapes: { ...customBlendShapes }
      };
      
      // 预览
      if (onPreview) {
        onPreview(interpolatedKeyframe);
      }
    }
  };

  // 线性插值
  const lerp = (a: number, b: number, t: number): number => {
    return a + (b - a) * t;
  };

  // 播放/暂停
  const togglePlay = () => {
    const newPlayingState = !playing;
    setPlaying(newPlayingState);
    
    if (audioElement) {
      if (newPlayingState) {
        audioElement.currentTime = currentTime;
        audioElement.play();
      } else {
        audioElement.pause();
      }
    }
  };

  // 添加关键帧
  const addKeyframe = () => {
    const newKeyframe: FacialAnimationKeyframe = {
      time: currentTime,
      expressionType: currentExpression,
      expressionWeight: expressionWeight,
      visemeType: currentViseme,
      visemeWeight: visemeWeight,
      customBlendShapes: { ...customBlendShapes }
    };
    
    // 检查是否已存在相同时间的关键帧
    const existingIndex = currentClip.keyframes.findIndex(kf => Math.abs(kf.time - currentTime) < 0.01);
    
    if (existingIndex >= 0) {
      // 更新现有关键帧
      const updatedKeyframes = [...currentClip.keyframes];
      updatedKeyframes[existingIndex] = newKeyframe;
      
      setCurrentClip({
        ...currentClip,
        keyframes: updatedKeyframes
      });
      
      message.success(t('facialAnimation.keyframeUpdated'));
    } else {
      // 添加新关键帧
      setCurrentClip({
        ...currentClip,
        keyframes: [...currentClip.keyframes, newKeyframe].sort((a, b) => a.time - b.time)
      });
      
      message.success(t('facialAnimation.keyframeAdded'));
    }
  };

  // 删除关键帧
  const deleteKeyframe = (time: number) => {
    const updatedKeyframes = currentClip.keyframes.filter(kf => Math.abs(kf.time - time) >= 0.01);
    
    setCurrentClip({
      ...currentClip,
      keyframes: updatedKeyframes
    });
    
    message.success(t('facialAnimation.keyframeDeleted'));
  };

  // 保存动画片段
  const saveClip = () => {
    if (onSave) {
      const clipToSave: FacialAnimationClip = {
        ...currentClip,
        audioFile: audioFile?.url as string
      };
      
      onSave(clipToSave);
      message.success(t('facialAnimation.clipSaved'));
    }
  };

  // 处理音频上传
  const handleAudioUpload = (info: any) => {
    if (info.file.status === 'done') {
      setAudioFile(info.file);
      
      // 创建音频元素
      const audio = new Audio(info.file.response.url || URL.createObjectURL(info.file.originFileObj));
      setAudioElement(audio);
      
      message.success(t('facialAnimation.audioUploaded'));
    } else if (info.file.status === 'error') {
      message.error(t('facialAnimation.audioUploadFailed'));
    }
  };

  // 启用/禁用口型同步
  const toggleLipSync = (checked: boolean) => {
    setLipSyncEnabled(checked);
    
    // 这里可以添加启用/禁用口型同步的逻辑
    // 例如，调用引擎API启用/禁用口型同步
  };

  // 渲染表情编辑器
  const renderExpressionEditor = () => (
    <div className="expression-editor">
      <Form layout="vertical">
        <Form.Item label={t('facialAnimation.expressionType')}>
          <Select
            value={currentExpression}
            onChange={value => setCurrentExpression(value)}
            style={{ width: '100%' }}
          >
            {Object.values(FacialExpressionType).map(type => (
              <Option key={type} value={type}>
                {t(`facialAnimation.expressions.${type}`)}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item label={t('facialAnimation.expressionWeight')}>
          <Slider
            min={0}
            max={1}
            step={0.01}
            value={expressionWeight}
            onChange={value => setExpressionWeight(value)}
          />
        </Form.Item>
        
        <Divider />
        
        <Form.Item>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={addKeyframe}
            block
          >
            {t('facialAnimation.addExpressionKeyframe')}
          </Button>
        </Form.Item>
      </Form>
      
      <div className="expression-presets">
        <Title level={5}>{t('facialAnimation.expressionPresets')}</Title>
        <div className="preset-grid">
          {Object.values(FacialExpressionType).filter(type => type !== FacialExpressionType.CUSTOM).map(type => (
            <Card
              key={type}
              className={`preset-card ${currentExpression === type ? 'selected' : ''}`}
              onClick={() => {
                setCurrentExpression(type);
                setExpressionWeight(1.0);
              }}
            >
              <div className="preset-name">{t(`facialAnimation.expressions.${type}`)}</div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );

  // 渲染口型编辑器
  const renderVisemeEditor = () => (
    <div className="viseme-editor">
      <Form layout="vertical">
        <Form.Item label={t('facialAnimation.visemeType')}>
          <Select
            value={currentViseme}
            onChange={value => setCurrentViseme(value)}
            style={{ width: '100%' }}
          >
            {Object.values(VisemeType).map(type => (
              <Option key={type} value={type}>
                {t(`facialAnimation.visemes.${type}`)}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item label={t('facialAnimation.visemeWeight')}>
          <Slider
            min={0}
            max={1}
            step={0.01}
            value={visemeWeight}
            onChange={value => setVisemeWeight(value)}
          />
        </Form.Item>
        
        <Divider />
        
        <Form.Item>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={addKeyframe}
            block
          >
            {t('facialAnimation.addVisemeKeyframe')}
          </Button>
        </Form.Item>
      </Form>
      
      <div className="viseme-presets">
        <Title level={5}>{t('facialAnimation.visemePresets')}</Title>
        <div className="preset-grid">
          {Object.values(VisemeType).filter(type => type !== VisemeType.CUSTOM).map(type => (
            <Card
              key={type}
              className={`preset-card ${currentViseme === type ? 'selected' : ''}`}
              onClick={() => {
                setCurrentViseme(type);
                setVisemeWeight(1.0);
              }}
            >
              <div className="preset-name">{t(`facialAnimation.visemes.${type}`)}</div>
            </Card>
          ))}
        </div>
      </div>
      
      <Divider />
      
      <div className="lip-sync-section">
        <Title level={5}>{t('facialAnimation.lipSync')}</Title>
        
        <Form layout="vertical">
          <Form.Item label={t('facialAnimation.audioFile')}>
            <Upload
              name="audio"
              listType="text"
              maxCount={1}
              fileList={audioFile ? [audioFile] : []}
              onChange={handleAudioUpload}
              action="/api/upload/audio"
            >
              <Button icon={<UploadOutlined />}>
                {t('facialAnimation.uploadAudio')}
              </Button>
            </Upload>
          </Form.Item>
          
          <Form.Item label={t('facialAnimation.enableLipSync')}>
            <Switch
              checked={lipSyncEnabled}
              onChange={toggleLipSync}
            />
          </Form.Item>
        </Form>
      </div>
    </div>
  );

  // 渲染自定义混合形状编辑器
  const renderCustomBlendShapesEditor = () => (
    <div className="blend-shapes-editor">
      <Form layout="vertical">
        {availableBlendShapes.map(blendShape => (
          <Form.Item
            key={blendShape.name}
            label={
              <Tooltip title={`${blendShape.category} (${blendShape.minValue}-${blendShape.maxValue})`}>
                {blendShape.name}
              </Tooltip>
            }
          >
            <Slider
              min={blendShape.minValue}
              max={blendShape.maxValue}
              step={0.01}
              value={customBlendShapes[blendShape.name] || blendShape.defaultValue}
              onChange={value => {
                setCustomBlendShapes({
                  ...customBlendShapes,
                  [blendShape.name]: value
                });
              }}
            />
          </Form.Item>
        ))}
        
        <Divider />
        
        <Form.Item>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={addKeyframe}
            block
          >
            {t('facialAnimation.addBlendShapeKeyframe')}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );

  // 渲染时间轴
  const renderTimeline = () => (
    <div className="timeline">
      <div className="timeline-controls">
        <Button
          type="primary"
          shape="circle"
          icon={playing ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          onClick={togglePlay}
        />
        
        <Slider
          min={0}
          max={currentClip.duration}
          step={0.01}
          value={currentTime}
          onChange={value => {
            setCurrentTime(value);
            if (audioElement) {
              audioElement.currentTime = value;
            }
            updateCurrentKeyframe(value);
          }}
          style={{ flex: 1, margin: '0 16px' }}
        />
        
        <Text>{currentTime.toFixed(2)}s / {currentClip.duration.toFixed(2)}s</Text>
      </div>
      
      <div className="timeline-keyframes">
        {currentClip.keyframes.map((keyframe, index) => (
          <Tooltip
            key={index}
            title={`${t(`facialAnimation.expressions.${keyframe.expressionType}`)} (${keyframe.expressionWeight.toFixed(2)}) / ${t(`facialAnimation.visemes.${keyframe.visemeType}`)} (${keyframe.visemeWeight.toFixed(2)})`}
          >
            <div
              className={`keyframe-marker ${Math.abs(keyframe.time - currentTime) < 0.01 ? 'current' : ''}`}
              style={{ left: `${(keyframe.time / currentClip.duration) * 100}%` }}
              onClick={() => {
                setCurrentTime(keyframe.time);
                if (audioElement) {
                  audioElement.currentTime = keyframe.time;
                }
                updateCurrentKeyframe(keyframe.time);
              }}
              onDoubleClick={() => deleteKeyframe(keyframe.time)}
            />
          </Tooltip>
        ))}
      </div>
    </div>
  );

  // 渲染片段设置
  const renderClipSettings = () => (
    <div className="clip-settings">
      <Form layout="vertical">
        <Form.Item label={t('facialAnimation.clipName')}>
          <Input
            value={currentClip.name}
            onChange={e => setCurrentClip({ ...currentClip, name: e.target.value })}
          />
        </Form.Item>
        
        <Form.Item label={t('facialAnimation.clipDuration')}>
          <Input
            type="number"
            min={0.1}
            step={0.1}
            value={currentClip.duration}
            onChange={e => setCurrentClip({ ...currentClip, duration: parseFloat(e.target.value) })}
          />
        </Form.Item>
        
        <Form.Item label={t('facialAnimation.loop')}>
          <Switch
            checked={currentClip.loop}
            onChange={checked => setCurrentClip({ ...currentClip, loop: checked })}
          />
        </Form.Item>
        
        <Divider />
        
        <Form.Item>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={saveClip}
            block
          >
            {t('facialAnimation.saveClip')}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );

  return (
    <div className="facial-animation-editor">
      <div className="editor-header">
        <Title level={4}>{currentClip.name}</Title>
        <div className="header-actions">
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={saveClip}
          >
            {t('facialAnimation.save')}
          </Button>
        </div>
      </div>
      
      {renderTimeline()}
      
      <div className="editor-content">
        <div className="editor-tabs">
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: 'expressions',
                label: t('facialAnimation.expressions'),
                children: renderExpressionEditor()
              },
              {
                key: 'visemes',
                label: t('facialAnimation.visemes'),
                children: renderVisemeEditor()
              },
              {
                key: 'blendShapes',
                label: t('facialAnimation.blendShapes'),
                children: renderCustomBlendShapesEditor()
              },
              {
                key: 'settings',
                label: t('facialAnimation.settings'),
                children: renderClipSettings()
              }
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default FacialAnimationEditor;
