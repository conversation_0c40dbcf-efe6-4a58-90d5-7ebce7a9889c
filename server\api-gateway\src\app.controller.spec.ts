import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigService } from '@nestjs/config';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const mockServiceRegistry = {
      connect: jest.fn().mockResolvedValue(true),
      send: jest.fn().mockReturnValue({ subscribe: jest.fn() }),
    };

    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        {
          provide: AppService,
          useValue: {
            getInfo: jest.fn().mockReturnValue({
              name: 'DL（Digital Learning）引擎API网关',
              version: '1.0.0',
              description: 'DL（Digital Learning）引擎API网关，作为前端和微服务之间的中介',
              environment: 'test',
            }),
            healthCheck: jest.fn().mockResolvedValue({
              status: 'up',
              timestamp: new Date().toISOString(),
              services: {},
            }),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test'),
          },
        },
        {
          provide: 'SERVICE_REGISTRY',
          useValue: mockServiceRegistry,
        },
        {
          provide: 'USER_SERVICE',
          useValue: mockServiceRegistry,
        },
        {
          provide: 'PROJECT_SERVICE',
          useValue: mockServiceRegistry,
        },
        {
          provide: 'ASSET_SERVICE',
          useValue: mockServiceRegistry,
        },
        {
          provide: 'RENDER_SERVICE',
          useValue: mockServiceRegistry,
        },
      ],
    }).compile();

    appController = app.get<AppController>(AppController);
    appService = app.get<AppService>(AppService);
  });

  describe('getInfo', () => {
    it('should return API gateway info', () => {
      const result = appController.getInfo();
      expect(result).toEqual({
        name: 'DL（Digital Learning）引擎API网关',
        version: '1.0.0',
        description: 'DL（Digital Learning）引擎API网关，作为前端和微服务之间的中介',
        environment: 'test',
      });
      expect(appService.getInfo).toHaveBeenCalled();
    });
  });

  describe('healthCheck', () => {
    it('should return health status', async () => {
      const result = await appController.healthCheck();
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('services');
      expect(appService.healthCheck).toHaveBeenCalled();
    });
  });
});
