/**
 * 消息序列化器
 * 负责序列化和反序列化网络消息
 */
import { NetworkMessage } from './NetworkMessage';
import { Debug } from '../utils/Debug';

/**
 * 压缩级别
 */
export enum CompressionLevel {
  /** 不压缩 */
  NONE = 0,
  /** 快速压缩 */
  FAST = 1,
  /** 标准压缩 */
  STANDARD = 2,
  /** 最大压缩 */
  MAX = 3,
}

/**
 * 消息序列化器
 * 负责序列化和反序列化网络消息
 */
export class MessageSerializer {
  /** 是否使用压缩 */
  private useCompression: boolean;

  /** 压缩级别 */
  private compressionLevel: CompressionLevel;

  /** 文本编码器 */
  private encoder: TextEncoder;

  /** 文本解码器 */
  private decoder: TextDecoder;

  /** 消息缓存 */
  private messageCache: Map<string, ArrayBuffer> = new Map();

  /** 缓存大小限制 */
  private cacheSizeLimit: number = 100;

  /** 是否启用缓存 */
  private enableCache: boolean = true;

  /**
   * 创建消息序列化器
   * @param useCompression 是否使用压缩
   * @param compressionLevel 压缩级别
   * @param enableCache 是否启用缓存
   */
  constructor(useCompression: boolean = true, compressionLevel: CompressionLevel = CompressionLevel.STANDARD, enableCache: boolean = true) {
    this.useCompression = useCompression;
    this.compressionLevel = compressionLevel;
    this.enableCache = enableCache;
    this.encoder = new TextEncoder();
    this.decoder = new TextDecoder();
  }

  /**
   * 序列化消息
   * @param message 消息对象
   * @returns 序列化后的数据
   */
  public async serialize(message: NetworkMessage): Promise<ArrayBuffer | string> {
    try {
      // 将消息转换为JSON字符串
      const json = JSON.stringify(message);

      // 计算缓存键
      const cacheKey = this.enableCache ? this.calculateCacheKey(message) : '';

      // 检查缓存
      if (this.enableCache && cacheKey && this.messageCache.has(cacheKey)) {
        return this.messageCache.get(cacheKey)!;
      }

      if (this.useCompression) {
        // 将JSON字符串转换为Uint8Array
        const data = this.encoder.encode(json);

        // 压缩数据
        const compressed = await this.compress(data);

        // 添加到缓存
        if (this.enableCache && cacheKey) {
          this.addToCache(cacheKey, compressed);
        }

        return compressed;
      } else {
        // 不使用压缩，直接返回JSON字符串
        return json;
      }
    } catch (error) {
      Debug.error('MessageSerializer', 'Failed to serialize message:', error);
      throw error;
    }
  }

  /**
   * 反序列化消息
   * @param data 序列化后的数据
   * @returns 消息对象
   */
  public async deserialize(data: ArrayBuffer | string): Promise<NetworkMessage> {
    try {
      let json: string;

      if (data instanceof ArrayBuffer) {
        // 如果是ArrayBuffer，则假设是压缩数据
        // 解压缩数据
        const decompressed = await this.decompress(data);

        // 将Uint8Array转换为JSON字符串
        json = this.decoder.decode(decompressed);
      } else {
        // 如果是字符串，则直接使用
        json = data;
      }

      // 将JSON字符串转换为消息对象
      return JSON.parse(json) as NetworkMessage;
    } catch (error) {
      Debug.error('MessageSerializer', 'Failed to deserialize message:', error);
      throw error;
    }
  }

  /**
   * 压缩数据
   * @param data 原始数据
   * @returns 压缩后的数据
   */
  private async compress(data: Uint8Array): Promise<ArrayBuffer> {
    try {
      // 使用内置的CompressionStream API进行压缩（如果浏览器支持）
      if (typeof CompressionStream !== 'undefined') {
        // 创建压缩流
        const cs = new CompressionStream('deflate');
        const writer = cs.writable.getWriter();
        const reader = cs.readable.getReader();

        // 写入数据
        await writer.write(data);
        await writer.close();

        // 读取压缩后的数据
        const chunks: Uint8Array[] = [];

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          chunks.push(value);
        }

        // 合并所有块
        const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of chunks) {
          result.set(chunk, offset);
          offset += chunk.length;
        }

        return result.buffer;
      } else {
        // 如果浏览器不支持CompressionStream，则使用简单的RLE压缩
        return this.compressRLE(data);
      }
    } catch (error) {
      Debug.warn('MessageSerializer', 'Compression failed, using uncompressed data:', error);
      return data.buffer;
    }
  }

  /**
   * 使用RLE算法压缩数据
   * @param data 原始数据
   * @returns 压缩后的数据
   */
  private compressRLE(data: Uint8Array): ArrayBuffer {
    if (data.length === 0) {
      return new ArrayBuffer(0);
    }

    const result: number[] = [];
    let currentByte = data[0];
    let count = 1;

    for (let i = 1; i < data.length; i++) {
      if (data[i] === currentByte && count < 255) {
        count++;
      } else {
        result.push(count);
        result.push(currentByte);
        currentByte = data[i];
        count = 1;
      }
    }

    result.push(count);
    result.push(currentByte);

    const compressed = new Uint8Array(result);

    // 只有当压缩后的大小小于原始大小时才使用压缩数据
    if (compressed.length < data.length) {
      // 添加标记以表示这是RLE压缩数据
      const output = new Uint8Array(compressed.length + 1);
      output[0] = 1; // 1表示RLE压缩
      output.set(compressed, 1);
      return output.buffer;
    } else {
      // 否则使用原始数据
      const output = new Uint8Array(data.length + 1);
      output[0] = 0; // 0表示未压缩
      output.set(data, 1);
      return output.buffer;
    }
  }

  /**
   * 解压缩数据
   * @param data 压缩后的数据
   * @returns 原始数据
   */
  private async decompress(data: ArrayBuffer): Promise<Uint8Array> {
    try {
      const dataView = new Uint8Array(data);

      // 检查压缩类型
      const compressionType = dataView[0];

      if (compressionType === 0) {
        // 未压缩数据
        return dataView.slice(1);
      } else if (compressionType === 1) {
        // RLE压缩数据
        return this.decompressRLE(dataView.slice(1));
      } else {
        // 使用内置的DecompressionStream API进行解压缩（如果浏览器支持）
        if (typeof DecompressionStream !== 'undefined') {
          // 创建解压缩流
          const ds = new DecompressionStream('deflate');
          const writer = ds.writable.getWriter();
          const reader = ds.readable.getReader();

          // 写入数据
          await writer.write(dataView);
          await writer.close();

          // 读取解压缩后的数据
          const chunks: Uint8Array[] = [];

          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            chunks.push(value);
          }

          // 合并所有块
          const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
          const result = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
          }

          return result;
        } else {
          // 如果无法识别压缩类型或浏览器不支持，则返回原始数据
          return dataView;
        }
      }
    } catch (error) {
      Debug.warn('MessageSerializer', 'Decompression failed, using raw data:', error);
      return new Uint8Array(data);
    }
  }

  /**
   * 使用RLE算法解压缩数据
   * @param data 压缩后的数据
   * @returns 原始数据
   */
  private decompressRLE(data: Uint8Array): Uint8Array {
    if (data.length === 0) {
      return new Uint8Array(0);
    }

    const result: number[] = [];

    for (let i = 0; i < data.length; i += 2) {
      const count = data[i];
      const value = data[i + 1];

      for (let j = 0; j < count; j++) {
        result.push(value);
      }
    }

    return new Uint8Array(result);
  }

  /**
   * 计算缓存键
   * @param message 消息对象
   * @returns 缓存键
   */
  private calculateCacheKey(message: NetworkMessage): string {
    // 对于心跳消息，不使用缓存
    if (message.type === 'heartbeat') {
      return '';
    }

    // 对于包含时间戳的消息，移除时间戳再计算缓存键
    const { timestamp, ...rest } = message;

    // 计算缓存键
    return message.type + '_' + JSON.stringify(rest);
  }

  /**
   * 添加到缓存
   * @param key 缓存键
   * @param value 缓存值
   */
  private addToCache(key: string, value: ArrayBuffer): void {
    // 如果缓存已满，则移除最早添加的项
    if (this.messageCache.size >= this.cacheSizeLimit) {
      const firstKey = this.messageCache.keys().next().value;
      this.messageCache.delete(firstKey);
    }

    // 添加到缓存
    this.messageCache.set(key, value);
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.messageCache.clear();
  }

  /**
   * 设置是否使用压缩
   * @param useCompression 是否使用压缩
   */
  public setUseCompression(useCompression: boolean): void {
    this.useCompression = useCompression;
  }

  /**
   * 是否使用压缩
   * @returns 是否使用压缩
   */
  public isUsingCompression(): boolean {
    return this.useCompression;
  }

  /**
   * 设置压缩级别
   * @param level 压缩级别
   */
  public setCompressionLevel(level: CompressionLevel): void {
    this.compressionLevel = level;
  }

  /**
   * 获取压缩级别
   * @returns 压缩级别
   */
  public getCompressionLevel(): CompressionLevel {
    return this.compressionLevel;
  }

  /**
   * 设置是否启用缓存
   * @param enable 是否启用缓存
   */
  public setEnableCache(enable: boolean): void {
    this.enableCache = enable;
  }

  /**
   * 是否启用缓存
   * @returns 是否启用缓存
   */
  public isEnableCache(): boolean {
    return this.enableCache;
  }

  /**
   * 设置缓存大小限制
   * @param limit 缓存大小限制
   */
  public setCacheSizeLimit(limit: number): void {
    this.cacheSizeLimit = limit;
  }

  /**
   * 获取缓存大小限制
   * @returns 缓存大小限制
   */
  public getCacheSizeLimit(): number {
    return this.cacheSizeLimit;
  }
}
