/**
 * 视觉脚本调试器
 * 提供视觉脚本的调试功能，包括断点、单步执行、变量监视等
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Graph } from '../graph/Graph';
import { Node } from '../nodes/Node';
import { VisualScriptEngine } from '../VisualScriptEngine';
/**
 * 断点类型
 */
export declare enum BreakpointType {
    /**
     * 普通断点
     */
    NORMAL = "normal",
    /**
     * 条件断点
     */
    CONDITIONAL = "conditional"
}
/**
 * 断点信息
 */
export interface Breakpoint {
    /**
     * 断点ID
     */
    id: string;
    /**
     * 节点ID
     */
    nodeId: string;
    /**
     * 图ID
     */
    graphId: string;
    /**
     * 断点类型
     */
    type: BreakpointType;
    /**
     * 条件表达式（仅条件断点）
     */
    condition?: string;
    /**
     * 是否启用
     */
    enabled: boolean;
}
/**
 * 执行状态
 */
export declare enum ExecutionState {
    /**
     * 运行中
     */
    RUNNING = "running",
    /**
     * 暂停
     */
    PAUSED = "paused",
    /**
     * 停止
     */
    STOPPED = "stopped"
}
/**
 * 执行步骤类型
 */
export declare enum StepType {
    /**
     * 单步执行（进入子图）
     */
    STEP_INTO = "step_into",
    /**
     * 单步执行（不进入子图）
     */
    STEP_OVER = "step_over",
    /**
     * 单步执行（跳出当前子图）
     */
    STEP_OUT = "step_out"
}
/**
 * 执行路径项
 */
export interface ExecutionPathItem {
    /**
     * 节点ID
     */
    nodeId: string;
    /**
     * 图ID
     */
    graphId: string;
    /**
     * 时间戳
     */
    timestamp: number;
}
/**
 * 视觉脚本调试器
 */
export declare class VisualScriptDebugger extends EventEmitter {
    /**
     * 断点列表
     */
    private breakpoints;
    /**
     * 当前执行状态
     */
    private executionState;
    /**
     * 当前暂停的节点
     */
    private currentNode;
    /**
     * 当前暂停的图
     */
    private currentGraph;
    /**
     * 执行路径历史
     */
    private executionPath;
    /**
     * 最大执行路径历史长度
     */
    private maxExecutionPathLength;
    /**
     * 变量监视列表
     */
    private watchedVariables;
    /**
     * 视觉脚本引擎
     */
    private engine;
    /**
     * 构造函数
     * @param engine 视觉脚本引擎
     */
    constructor(engine: VisualScriptEngine);
    /**
     * 添加断点
     * @param nodeId 节点ID
     * @param graphId 图ID
     * @param type 断点类型
     * @param condition 条件表达式
     * @returns 断点ID
     */
    addBreakpoint(nodeId: string, graphId: string, type?: BreakpointType, condition?: string): string;
    /**
     * 移除断点
     * @param id 断点ID
     * @returns 是否成功
     */
    removeBreakpoint(id: string): boolean;
    /**
     * 启用/禁用断点
     * @param id 断点ID
     * @param enabled 是否启用
     * @returns 是否成功
     */
    setBreakpointEnabled(id: string, enabled: boolean): boolean;
    /**
     * 更新断点条件
     * @param id 断点ID
     * @param condition 条件表达式
     * @returns 是否成功
     */
    updateBreakpointCondition(id: string, condition: string): boolean;
    /**
     * 获取所有断点
     * @returns 断点列表
     */
    getBreakpoints(): Breakpoint[];
    /**
     * 获取节点的断点
     * @param nodeId 节点ID
     * @param graphId 图ID
     * @returns 断点
     */
    getBreakpoint(nodeId: string, graphId: string): Breakpoint | undefined;
    /**
     * 继续执行
     */
    continue(): void;
    /**
     * 暂停执行
     */
    pause(): void;
    /**
     * 停止执行
     */
    stop(): void;
    /**
     * 单步执行
     * @param type 步骤类型
     */
    step(type: StepType): void;
    /**
     * 添加变量监视
     * @param variableName 变量名
     */
    addWatch(variableName: string): void;
    /**
     * 移除变量监视
     * @param variableName 变量名
     */
    removeWatch(variableName: string): void;
    /**
     * 获取所有监视变量
     * @returns 监视变量列表
     */
    getWatches(): string[];
    /**
     * 获取当前暂停的节点
     * @returns 当前节点
     */
    getCurrentNode(): Node | null;
    /**
     * 获取当前暂停的图
     * @returns 当前图
     */
    getCurrentGraph(): Graph | null;
    /**
     * 获取变量值
     * @param variableName 变量名
     * @returns 变量值
     */
    getVariableValue(variableName: string): any;
    /**
     * 设置变量值
     * @param variableName 变量名
     * @param value 变量值
     * @returns 是否成功
     */
    setVariableValue(variableName: string, value: any): boolean;
    /**
     * 获取执行路径
     * @returns 执行路径
     */
    getExecutionPath(): ExecutionPathItem[];
    /**
     * 清除执行路径
     */
    clearExecutionPath(): void;
    /**
     * 设置最大执行路径长度
     * @param length 长度
     */
    setMaxExecutionPathLength(length: number): void;
    /**
     * 节点执行前事件处理
     * @param node 节点
     * @param graph 图
     */
    private onNodeBeforeExecute;
    /**
     * 节点执行后事件处理
     * @param _node 节点
     * @param graph 图
     * @param _result 执行结果
     */
    private onNodeAfterExecute;
    /**
     * 图开始执行事件处理
     * @param graph 图
     */
    private onGraphStart;
    /**
     * 图结束执行事件处理
     * @param graph 图
     */
    private onGraphEnd;
    /**
     * 添加执行路径项
     * @param nodeId 节点ID
     * @param graphId 图ID
     */
    private addExecutionPathItem;
}
