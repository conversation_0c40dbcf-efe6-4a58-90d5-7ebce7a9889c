/**
 * 物理约束组件
 * 为实体提供物理约束
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
/**
 * 约束类型
 */
export type ConstraintType = 'point' | 'distance' | 'hinge' | 'lock' | 'spring';
/**
 * 物理约束选项
 */
export interface PhysicsConstraintOptions {
    /** 约束类型 */
    type: ConstraintType;
    /** 约束的第一个实体 */
    entityA: Entity;
    /** 约束的第二个实体（可选） */
    entityB?: Entity;
    /** 第一个实体的枢轴点 */
    pivotA?: THREE.Vector3;
    /** 第二个实体的枢轴点 */
    pivotB?: THREE.Vector3;
    /** 第一个实体的轴向 */
    axisA?: THREE.Vector3;
    /** 第二个实体的轴向 */
    axisB?: THREE.Vector3;
    /** 距离约束的距离 */
    distance?: number;
    /** 弹簧约束的刚度 */
    stiffness?: number;
    /** 弹簧约束的阻尼 */
    damping?: number;
    /** 铰链约束的最大角度 */
    maxAngle?: number;
    /** 铰链约束的最小角度 */
    minAngle?: number;
    /** 铰链约束的马达启用 */
    enableMotor?: boolean;
    /** 铰链约束的马达速度 */
    motorSpeed?: number;
    /** 铰链约束的马达最大力 */
    motorMaxForce?: number;
    /** 碰撞启用 */
    collideConnected?: boolean;
}
/**
 * 物理约束组件
 */
export declare class PhysicsConstraintComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 约束类型 */
    constraintType: ConstraintType;
    /** 约束的第一个实体 */
    entityA: Entity;
    /** 约束的第二个实体 */
    entityB: Entity | null;
    /** 第一个实体的枢轴点 */
    pivotA: THREE.Vector3;
    /** 第二个实体的枢轴点 */
    pivotB: THREE.Vector3;
    /** 第一个实体的轴向 */
    axisA: THREE.Vector3;
    /** 第二个实体的轴向 */
    axisB: THREE.Vector3;
    /** 距离约束的距离 */
    distance: number;
    /** 弹簧约束的刚度 */
    stiffness: number;
    /** 弹簧约束的阻尼 */
    damping: number;
    /** 铰链约束的最大角度 */
    maxAngle: number | null;
    /** 铰链约束的最小角度 */
    minAngle: number | null;
    /** 铰链约束的马达启用 */
    enableMotor: boolean;
    /** 铰链约束的马达速度 */
    motorSpeed: number;
    /** 铰链约束的马达最大力 */
    motorMaxForce: number;
    /** 碰撞启用 */
    collideConnected: boolean;
    /** CANNON.js约束 */
    private constraint;
    /** 物理世界 */
    private world;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /**
     * 创建物理约束组件
     * @param options 约束选项
     */
    constructor(options: PhysicsConstraintOptions);
    /**
     * 初始化约束
     * @param world 物理世界
     */
    initialize(world: CANNON.World): void;
    /**
     * 获取实体的物理体
     * @param entity 实体
     * @returns 物理体
     */
    private getBody;
    /**
     * 创建点约束
     * @param bodyA 第一个物理体
     * @param bodyB 第二个物理体
     */
    private createPointConstraint;
    /**
     * 创建距离约束
     * @param bodyA 第一个物理体
     * @param bodyB 第二个物理体
     */
    private createDistanceConstraint;
    /**
     * 创建铰链约束
     * @param bodyA 第一个物理体
     * @param bodyB 第二个物理体
     */
    private createHingeConstraint;
    /**
     * 创建锁约束
     * @param bodyA 第一个物理体
     * @param bodyB 第二个物理体
     */
    private createLockConstraint;
    /**
     * 创建弹簧约束
     * @param bodyA 第一个物理体
     * @param bodyB 第二个物理体
     */
    private createSpringConstraint;
    /**
     * 获取CANNON.js约束
     * @returns CANNON.js约束
     */
    getConstraint(): CANNON.Constraint | null;
    /**
     * 启用马达（仅适用于铰链约束）
     */
    enableMotorFunc(): void;
    /**
     * 禁用马达（仅适用于铰链约束）
     */
    disableMotor(): void;
    /**
     * 设置马达速度（仅适用于铰链约束）
     * @param speed 马达速度
     */
    setMotorSpeed(speed: number): void;
    /**
     * 设置马达最大力（仅适用于铰链约束）
     * @param maxForce 马达最大力
     */
    setMotorMaxForce(maxForce: number): void;
    /**
     * 设置角度限制（仅适用于铰链约束）
     * @param min 最小角度
     * @param max 最大角度
     */
    setLimits(min: number, max: number): void;
    /**
     * 销毁约束
     */
    dispose(): void;
}
