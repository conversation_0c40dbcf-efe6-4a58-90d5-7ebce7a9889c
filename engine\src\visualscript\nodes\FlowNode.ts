/**
 * 视觉脚本流程节点
 * 流程节点用于控制执行流程
 */
import { Node, NodeCategory, NodeOptions, NodeType, SocketDirection, SocketType } from './Node';

/**
 * 流程节点选项
 */
export interface FlowNodeOptions extends NodeOptions {
  /** 输入流程插槽名称 */
  inputFlowName?: string;
  /** 输出流程插槽名称列表 */
  outputFlowNames?: string[];
}

/**
 * 流程节点基类
 */
export class FlowNode extends Node {
  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.NORMAL;
  
  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.FLOW;
  
  /** 输入流程插槽名称 */
  protected inputFlowName: string;
  
  /** 输出流程插槽名称列表 */
  protected outputFlowNames: string[];
  
  /**
   * 创建流程节点
   * @param options 节点选项
   */
  constructor(options: FlowNodeOptions) {
    super(options);
    
    this.inputFlowName = options.inputFlowName || 'flow';
    this.outputFlowNames = options.outputFlowNames || ['flow'];
  }
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: this.inputFlowName,
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    // 添加输出流程插槽
    for (const name of this.outputFlowNames) {
      this.addOutput({
        name: name,
        type: SocketType.FLOW,
        direction: SocketDirection.OUTPUT,
        description: '执行输出'
      });
    }
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取所有输入值
    const inputs: Record<string, any> = {};
    
    for (const [name, socket] of this.inputs.entries()) {
      if (socket.type === SocketType.DATA) {
        inputs[name] = this.getInputValue(name);
      }
    }
    
    // 处理输入并确定输出流程
    const outputFlowName = this.process(inputs);
    
    // 如果有输出流程，触发它
    if (outputFlowName && this.outputs.has(outputFlowName)) {
      this.triggerFlow(outputFlowName);
    }
    
    return outputFlowName;
  }
  
  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    // 默认返回第一个输出流程
    return this.outputFlowNames.length > 0 ? this.outputFlowNames[0] : null;
  }
}
