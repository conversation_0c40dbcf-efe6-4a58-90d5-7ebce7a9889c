/**
 * 天空盒类
 * 创建和管理场景的天空盒
 */
import * as THREE from 'three';

export enum SkyboxType {
  CUBEMAP = 'cubemap',
  EQUIRECTANGULAR = 'equirectangular',
  PROCEDURAL = 'procedural',
}

export interface SkyboxOptions {
  /** 天空盒类型 */
  type: SkyboxType;
  /** 立方体贴图路径（6个面的贴图路径） */
  cubemapPaths?: string[];
  /** 等距矩形贴图路径 */
  equirectangularPath?: string;
  /** 程序化天空盒参数 */
  proceduralParams?: {
    /** 顶部颜色 */
    topColor?: THREE.Color | string | number;
    /** 底部颜色 */
    bottomColor?: THREE.Color | string | number;
    /** 指数 */
    exponent?: number;
  };
  /** 是否旋转 */
  rotate?: boolean;
  /** 旋转速度 */
  rotationSpeed?: number;
}

export class Skybox {
  /** 天空盒类型 */
  private type: SkyboxType;
  
  /** 天空盒网格 */
  private mesh: THREE.Mesh | null = null;
  
  /** 天空盒材质 */
  private material: THREE.Material | null = null;
  
  /** 天空盒几何体 */
  private geometry: THREE.BufferGeometry | null = null;
  
  /** 是否旋转 */
  private rotate: boolean;
  
  /** 旋转速度 */
  private rotationSpeed: number;

  /**
   * 创建天空盒实例
   * @param options 天空盒选项
   */
  constructor(options: SkyboxOptions) {
    this.type = options.type;
    this.rotate = options.rotate || false;
    this.rotationSpeed = options.rotationSpeed || 0.001;
    
    // 根据类型创建天空盒
    switch (this.type) {
      case SkyboxType.CUBEMAP:
        this.createCubemapSkybox(options.cubemapPaths || []);
        break;
        
      case SkyboxType.EQUIRECTANGULAR:
        this.createEquirectangularSkybox(options.equirectangularPath || '');
        break;
        
      case SkyboxType.PROCEDURAL:
        this.createProceduralSkybox(options.proceduralParams || {});
        break;
        
      default:
        console.error(`不支持的天空盒类型: ${this.type}`);
        break;
    }
  }

  /**
   * 创建立方体贴图天空盒
   * @param paths 6个面的贴图路径（顺序：右、左、上、下、前、后）
   */
  private createCubemapSkybox(paths: string[]): void {
    if (paths.length !== 6) {
      console.error('立方体贴图需要6个面的贴图路径');
      return;
    }
    
    // 加载立方体贴图
    const loader = new THREE.CubeTextureLoader();
    const texture = loader.load(paths);
    
    // 创建几何体和材质
    this.geometry = new THREE.BoxGeometry(1, 1, 1);
    this.material = new THREE.MeshBasicMaterial({
      envMap: texture,
      side: THREE.BackSide,
    });
    
    // 创建网格
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    (this.mesh as any).setScale(1000, 1000, 1000);
  }

  /**
   * 创建等距矩形贴图天空盒
   * @param path 等距矩形贴图路径
   */
  private createEquirectangularSkybox(path: string): void {
    if (!path) {
      console.error('等距矩形贴图需要提供贴图路径');
      return;
    }
    
    // 加载等距矩形贴图
    const loader = new THREE.TextureLoader();
    const texture = loader.load(path);
    texture.mapping = THREE.EquirectangularReflectionMapping;
    
    // 创建几何体和材质
    this.geometry = new THREE.SphereGeometry(1, 64, 32);
    this.material = new THREE.MeshBasicMaterial({
      map: texture,
      side: THREE.BackSide,
    });
    
    // 创建网格
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    (this.mesh as any).setScale(1000, 1000, 1000);
  }

  /**
   * 创建程序化天空盒
   * @param params 程序化天空盒参数
   */
  private createProceduralSkybox(params: any): void {
    // 默认参数
    const topColor = params.topColor !== undefined ? params.topColor : 0x0077ff;
    const bottomColor = params.bottomColor !== undefined ? params.bottomColor : 0xffffff;
    const exponent = params.exponent !== undefined ? params.exponent : 0.6;
    
    // 创建着色器材质
    const vertexShader = `
      varying vec3 vWorldPosition;
      
      void main() {
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;
    
    const fragmentShader = `
      uniform vec3 topColor;
      uniform vec3 bottomColor;
      uniform float exponent;
      
      varying vec3 vWorldPosition;
      
      void main() {
        float h = normalize(vWorldPosition).y;
        float t = max(0.0, min(1.0, pow(max(0.0, h), exponent)));
        
        gl_FragColor = vec4(mix(bottomColor, topColor, t), 1.0);
      }
    `;
    
    // 创建几何体和材质
    this.geometry = new THREE.SphereGeometry(1, 64, 32);
    this.material = new THREE.ShaderMaterial({
      uniforms: {
        topColor: { value: new THREE.Color(topColor) },
        bottomColor: { value: new THREE.Color(bottomColor) },
        exponent: { value: exponent },
      },
      vertexShader: vertexShader,
      fragmentShader: fragmentShader,
      side: THREE.BackSide,
    });
    
    // 创建网格
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    (this.mesh as any).setScale(1000, 1000, 1000);
  }

  /**
   * 更新天空盒
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (this.rotate && this.mesh) {
      this.mesh.rotation.y += this.rotationSpeed * deltaTime;
    }
  }

  /**
   * 设置旋转
   * @param rotate 是否旋转
   */
  public setRotate(rotate: boolean): void {
    this.rotate = rotate;
  }

  /**
   * 设置旋转速度
   * @param speed 旋转速度
   */
  public setRotationSpeed(speed: number): void {
    this.rotationSpeed = speed;
  }

  /**
   * 获取天空盒网格
   * @returns 天空盒网格
   */
  public getMesh(): THREE.Mesh | null {
    return this.mesh;
  }

  /**
   * 获取天空盒类型
   * @returns 天空盒类型
   */
  public getType(): SkyboxType {
    return this.type;
  }

  /**
   * 销毁天空盒
   */
  public dispose(): void {
    // 销毁几何体
    if (this.geometry) {
      (this.geometry as any).dispose();
      this.geometry = null;
    }
    
    // 销毁材质
    if (this.material) {
      if (Array.isArray(this.material)) {
        for (const mat of this.material) {
          (mat as any).dispose();
        }
      } else {
        (this.material as any).dispose();
      }
      this.material = null;
    }
    
    this.mesh = null;
  }
}
