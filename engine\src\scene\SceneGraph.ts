/**
 * 场景图类
 * 管理场景中实体的层级结构
 */
import type { Entity } from '../core/Entity';
import { Scene } from './Scene';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 场景图节点接口
 */
export interface SceneGraphNode {
  /** 节点ID */
  id: string;
  /** 节点名称 */
  name: string;
  /** 节点类型 */
  type: string;
  /** 子节点列表 */
  children: SceneGraphNode[];
  /** 组件列表 */
  components: string[];
  /** 是否可见 */
  visible: boolean;
  /** 是否锁定 */
  locked: boolean;
  /** 是否展开 */
  expanded?: boolean;
  /** 自定义数据 */
  userData?: Record<string, any>;
}

/**
 * 场景图查询选项
 */
export interface SceneGraphQueryOptions {
  /** 是否包含不可见节点 */
  includeInvisible?: boolean;
  /** 是否包含锁定节点 */
  includeLocked?: boolean;
  /** 是否递归查询子节点 */
  recursive?: boolean;
  /** 最大递归深度 */
  maxDepth?: number;
  /** 组件过滤器 */
  componentFilter?: string[];
  /** 名称过滤器（支持正则表达式） */
  nameFilter?: string | RegExp;
  /** 标签过滤器 */
  tagFilter?: string[];
  /** 自定义过滤函数 */
  customFilter?: (node: SceneGraphNode) => boolean;
}

/**
 * 场景图类
 */
export class SceneGraph extends EventEmitter {
  /** 场景实例 */
  private scene: Scene;
  
  /** 根节点 */
  private rootNode: SceneGraphNode | null = null;
  
  /** 节点映射 */
  private nodeMap: Map<string, SceneGraphNode> = new Map();
  
  /** 实体映射 */
  private entityMap: Map<string, Entity> = new Map();
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景图实例
   * @param scene 场景实例
   */
  constructor(scene: Scene) {
    super();
    this.scene = scene;
  }

  /**
   * 初始化场景图
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }
    
    // 构建场景图
    this.buildSceneGraph();
    
    // 监听场景实体变化事件
    this.scene.on('entityAdded', (entity: Entity) => {
      this.onEntityAdded(entity);
    });
    
    this.scene.on('entityRemoved', (entity: Entity) => {
      this.onEntityRemoved(entity);
    });
    
    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 构建场景图
   */
  private buildSceneGraph(): void {
    // 清空节点映射
    this.nodeMap.clear();
    this.entityMap.clear();
    
    // 获取场景中的所有实体
    const entities = this.scene.getEntities();
    
    // 找出根实体（没有父实体的实体）
    const rootEntities = entities.filter(entity => !entity.getParent());
    
    // 创建根节点
    this.rootNode = {
      id: 'root',
      name: '场景根节点',
      type: 'Root',
      children: [],
      components: [],
      visible: true,
      locked: false,
      expanded: true
    };
    
    // 添加到节点映射
    this.nodeMap.set('root', this.rootNode);
    
    // 递归构建场景图
    for (const entity of rootEntities) {
      const node = this.buildSceneGraphNode(entity);
      this.rootNode.children.push(node);
    }
    
    // 发出场景图构建完成事件
    this.emit('built', this.rootNode);
  }

  /**
   * 构建场景图节点
   * @param entity 实体实例
   * @returns 场景图节点
   */
  private buildSceneGraphNode(entity: Entity): SceneGraphNode {
    // 获取组件列表
    const components = entity.getAllComponents().map(component => component.getType());
    
    // 创建节点
    const node: SceneGraphNode = {
      id: entity.id,
      name: entity.name,
      type: 'Entity',
      children: [],
      components,
      visible: entity.isActive(),
      locked: false, // 默认不锁定
      expanded: false // 默认不展开
    };
    
    // 添加到节点映射
    this.nodeMap.set(entity.id, node);
    this.entityMap.set(entity.id, entity);
    
    // 递归处理子实体
    const children = entity.getChildren();
    for (const child of children) {
      const childNode = this.buildSceneGraphNode(child);
      node.children.push(childNode);
    }
    
    return node;
  }

  /**
   * 实体添加事件处理
   * @param entity 添加的实体
   */
  private onEntityAdded(entity: Entity): void {
    // 获取父实体
    const parent = entity.getParent();
    
    if (parent) {
      // 如果有父实体，则添加到父节点
      const parentNode = this.nodeMap.get(parent.id);
      
      if (parentNode) {
        const node = this.buildSceneGraphNode(entity);
        parentNode.children.push(node);
      }
    } else {
      // 如果没有父实体，则添加到根节点
      if (this.rootNode) {
        const node = this.buildSceneGraphNode(entity);
        this.rootNode.children.push(node);
      }
    }
    
    // 发出节点添加事件
    this.emit('nodeAdded', this.nodeMap.get(entity.id));
  }

  /**
   * 实体移除事件处理
   * @param entity 移除的实体
   */
  private onEntityRemoved(entity: Entity): void {
    const node = this.nodeMap.get(entity.id);
    
    if (!node) {
      return;
    }
    
    // 从节点映射中移除
    this.nodeMap.delete(entity.id);
    this.entityMap.delete(entity.id);
    
    // 从父节点中移除
    const parent = entity.getParent();
    
    if (parent) {
      const parentNode = this.nodeMap.get(parent.id);
      
      if (parentNode) {
        const index = parentNode.children.findIndex(child => child.id === entity.id);
        
        if (index !== -1) {
          parentNode.children.splice(index, 1);
        }
      }
    } else if (this.rootNode) {
      // 如果没有父实体，则从根节点移除
      const index = this.rootNode.children.findIndex(child => child.id === entity.id);
      
      if (index !== -1) {
        this.rootNode.children.splice(index, 1);
      }
    }
    
    // 发出节点移除事件
    this.emit('nodeRemoved', node);
  }

  /**
   * 获取根节点
   * @returns 根节点
   */
  public getRootNode(): SceneGraphNode | null {
    return this.rootNode;
  }

  /**
   * 获取节点
   * @param id 节点ID
   * @returns 节点实例
   */
  public getNode(id: string): SceneGraphNode | null {
    return this.nodeMap.get(id) || null;
  }

  /**
   * 获取实体
   * @param id 节点ID
   * @returns 实体实例
   */
  public getEntity(id: string): Entity | null {
    return this.entityMap.get(id) || null;
  }

  /**
   * 查询节点
   * @param options 查询选项
   * @returns 匹配的节点数组
   */
  public queryNodes(options: SceneGraphQueryOptions = {}): SceneGraphNode[] {
    // 合并选项
    const mergedOptions: SceneGraphQueryOptions = {
      includeInvisible: options.includeInvisible !== undefined ? options.includeInvisible : false,
      includeLocked: options.includeLocked !== undefined ? options.includeLocked : true,
      recursive: options.recursive !== undefined ? options.recursive : true,
      maxDepth: options.maxDepth !== undefined ? options.maxDepth : Infinity,
      componentFilter: options.componentFilter || [],
      nameFilter: options.nameFilter,
      tagFilter: options.tagFilter || [],
      customFilter: options.customFilter
    };
    
    // 从根节点开始查询
    if (!this.rootNode) {
      return [];
    }
    
    return this.queryNodeRecursive(this.rootNode, mergedOptions, 0);
  }

  /**
   * 递归查询节点
   * @param node 当前节点
   * @param options 查询选项
   * @param depth 当前深度
   * @returns 匹配的节点数组
   */
  private queryNodeRecursive(
    node: SceneGraphNode,
    options: SceneGraphQueryOptions,
    depth: number
  ): SceneGraphNode[] {
    const result: SceneGraphNode[] = [];
    
    // 检查深度
    if (depth > (options.maxDepth || Infinity)) {
      return result;
    }
    
    // 检查可见性
    if (!options.includeInvisible && !node.visible) {
      return result;
    }
    
    // 检查锁定状态
    if (!options.includeLocked && node.locked) {
      return result;
    }
    
    // 检查组件过滤器
    if (options.componentFilter && options.componentFilter.length > 0) {
      const hasAllComponents = options.componentFilter.every(component => 
        node.components.includes(component)
      );
      
      if (!hasAllComponents) {
        return result;
      }
    }
    
    // 检查名称过滤器
    if (options.nameFilter) {
      if (typeof options.nameFilter === 'string') {
        if (!node.name.includes(options.nameFilter)) {
          return result;
        }
      } else if (options.nameFilter instanceof RegExp) {
        if (!options.nameFilter.test(node.name)) {
          return result;
        }
      }
    }
    
    // 检查标签过滤器
    if (options.tagFilter && options.tagFilter.length > 0) {
      const entity = this.entityMap.get(node.id);
      
      if (entity) {
        const hasAnyTag = options.tagFilter.some(tag => entity.hasTag(tag));
        
        if (!hasAnyTag) {
          return result;
        }
      }
    }
    
    // 检查自定义过滤器
    if (options.customFilter && !options.customFilter(node)) {
      return result;
    }
    
    // 添加当前节点
    result.push(node);
    
    // 递归处理子节点
    if (options.recursive && node.children.length > 0) {
      for (const child of node.children) {
        const childResults = this.queryNodeRecursive(child, options, depth + 1);
        result.push(...childResults);
      }
    }
    
    return result;
  }

  /**
   * 更新场景图
   */
  public update(): void {
    this.buildSceneGraph();
  }

  /**
   * 销毁场景图
   */
  public dispose(): void {
    // 清空节点映射
    this.nodeMap.clear();
    this.entityMap.clear();
    
    // 清空根节点
    this.rootNode = null;
    
    // 移除所有事件监听器
    this.removeAllListeners();
    
    this.initialized = false;
  }
}
