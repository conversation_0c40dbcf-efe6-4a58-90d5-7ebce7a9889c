# 基础编辑器功能演示

## 简介

本示例项目展示了DL（Digital Learning）引擎编辑器的基本功能，包括场景创建、对象操作、属性编辑、材质设置和灯光调整。通过本示例，您可以快速了解和掌握编辑器的核心功能和基本操作流程。

## 功能特性

- **场景创建与管理**：创建新场景、设置场景属性、保存和加载场景
- **对象操作**：选择、移动、旋转和缩放3D对象
- **属性编辑**：通过属性面板修改对象的各种属性
- **材质编辑**：创建和编辑基本材质属性
- **灯光设置**：添加和调整不同类型的灯光
- **相机控制**：设置和调整场景相机

## 使用说明

1. 打开示例项目
2. 按照界面上的步骤指引操作
3. 使用鼠标左键选择对象
4. 使用鼠标右键拖动旋转视图
5. 使用键盘快捷键进行操作：
   - W: 移动工具
   - E: 旋转工具
   - R: 缩放工具
6. 在右侧属性面板中修改选中对象的属性
7. 尝试修改材质和灯光设置

## 步骤详解

### 步骤1：场景创建

本示例展示了如何创建一个基本场景，包括添加相机、灯光和基本几何体。

**关键代码**：

```javascript
// 创建场景
const scene = new Scene(world, {
  name: '编辑器基础功能演示场景',
  background: { type: 'color', value: '#87CEEB' },
});

// 创建相机
const camera = new Entity(world)
  .addComponent(new Camera({
    type: 'perspective',
    fov: 60,
    near: 0.1,
    far: 1000,
  }))
  .addComponent(new Transform({
    position: { x: 0, y: 2, z: 5 },
    rotation: { x: -0.2, y: 0, z: 0 },
  }));

// 添加相机到场景
scene.addEntity(camera);
```

### 步骤2：对象操作

本步骤展示了如何选择和操作场景中的对象，包括移动、旋转和缩放。

**操作方法**：
- 使用鼠标左键选择对象
- 使用键盘快捷键切换工具：
  - W: 移动工具
  - E: 旋转工具
  - R: 缩放工具
- 使用鼠标拖动操作对象

**关键代码**：

```javascript
// 创建立方体
const cube = new Entity(world)
  .addComponent(new Mesh({
    geometry: { type: 'box', width: 1, height: 1, depth: 1 },
    castShadow: true,
  }))
  .addComponent(new Material({
    type: 'standard',
    color: '#ff0000',
    roughness: 0.5,
    metalness: 0.5,
  }))
  .addComponent(new Transform({
    position: { x: -2, y: 0.5, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    scale: { x: 1, y: 1, z: 1 },
  }));

// 添加立方体到场景
scene.addEntity(cube);
```

### 步骤3：属性编辑

本步骤展示了如何使用属性面板编辑对象的各种属性。

**编辑内容**：
- 位置（Position）
- 旋转（Rotation）
- 缩放（Scale）
- 可见性（Visibility）
- 名称（Name）

**属性编辑流程**：
1. 选择要编辑的对象
2. 在属性面板中找到要修改的属性
3. 修改属性值
4. 观察场景中的变化

### 步骤4：材质编辑

本步骤展示了如何编辑对象的材质属性。

**编辑内容**：
- 颜色（Color）
- 金属度（Metalness）
- 粗糙度（Roughness）
- 透明度（Opacity）
- 贴图（Maps）

**关键代码**：

```javascript
// 创建球体
const sphere = new Entity(world)
  .addComponent(new Mesh({
    geometry: { type: 'sphere', radius: 0.5, widthSegments: 32, heightSegments: 32 },
    castShadow: true,
  }))
  .addComponent(new Material({
    type: 'standard',
    color: '#00ff00',
    roughness: 0.2,
    metalness: 0.8,
  }))
  .addComponent(new Transform({
    position: { x: 0, y: 0.5, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    scale: { x: 1, y: 1, z: 1 },
  }));
```

### 步骤5：灯光设置

本步骤展示了如何添加和调整场景中的灯光。

**灯光类型**：
- 环境光（Ambient Light）
- 平行光（Directional Light）
- 点光源（Point Light）
- 聚光灯（Spot Light）

**编辑内容**：
- 颜色（Color）
- 强度（Intensity）
- 位置和方向（Position & Direction）
- 阴影设置（Shadow Settings）

**关键代码**：

```javascript
// 创建环境光
const ambientLight = new Entity(world)
  .addComponent(new Light({
    type: 'ambient',
    color: '#ffffff',
    intensity: 0.3,
  }));

// 添加环境光到场景
scene.addEntity(ambientLight);

// 创建平行光
const directionalLight = new Entity(world)
  .addComponent(new Light({
    type: 'directional',
    color: '#ffffff',
    intensity: 0.8,
    castShadow: true,
  }))
  .addComponent(new Transform({
    position: { x: 5, y: 10, z: 5 },
    rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
  }));

// 添加平行光到场景
scene.addEntity(directionalLight);
```

## 技术要点

- **实体组件系统**：DL（Digital Learning）引擎使用实体组件系统（ECS）架构，所有对象都是由实体和组件组成
- **场景管理**：场景是对象的容器，管理所有实体和组件
- **变换系统**：处理对象的位置、旋转和缩放
- **渲染系统**：处理对象的可视化表示
- **材质系统**：管理对象的外观和表面属性
- **灯光系统**：提供场景照明和阴影

## 学习要点

- 了解DL（Digital Learning）引擎编辑器的基本界面和操作方式
- 掌握3D对象的基本操作（选择、移动、旋转、缩放）
- 学习如何通过属性面板修改对象属性
- 了解基本材质的创建和编辑方法
- 掌握不同类型灯光的添加和调整方法
- 理解实体组件系统的基本概念和使用方法

## 扩展建议

- **添加更多几何体**：尝试添加更多类型的几何体，如圆锥、圆环、多面体等
- **创建复合对象**：将多个基本几何体组合成复杂对象
- **添加交互功能**：为对象添加点击、拖拽等交互功能
- **实现动画效果**：为对象添加简单的动画效果，如旋转、移动等
- **添加物理效果**：为对象添加物理属性，实现碰撞、重力等效果
- **自定义材质**：创建自定义着色器材质，实现特殊的视觉效果

## 相关资源

- [DL（Digital Learning）引擎文档](../../docs/README.md)
- [编辑器用户手册](../../docs/editor/README.md)
- [实体组件系统教程](../../docs/engine/ecs.md)
- [材质系统教程](../../docs/engine/materials.md)
- [灯光系统教程](../../docs/engine/lighting.md)

## 常见问题

### 为什么我无法选择某些对象？

确保对象没有被锁定，并且当前视图中可见。某些对象可能因为太小或被其他对象遮挡而难以选择。

### 如何同时操作多个对象？

按住Ctrl键（Windows）或Command键（Mac）的同时点击多个对象进行多选，然后可以同时操作它们。

### 如何精确设置对象的位置？

使用属性面板中的数值输入框可以精确设置对象的位置、旋转和缩放值。

### 如何撤销操作？

使用Ctrl+Z（Windows）或Command+Z（Mac）撤销上一步操作，使用Ctrl+Y（Windows）或Command+Shift+Z（Mac）重做操作。

### 如何保存我的修改？

使用编辑器顶部菜单中的"文件 > 保存"或按下Ctrl+S（Windows）或Command+S（Mac）保存当前场景。
