/**
 * 扫描线效果
 * 模拟CRT显示器的扫描线
 */
import * as THREE from 'three';
// 使用类型断言导入 ShaderPass
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 扫描线效果选项
 */
export interface ScanlineEffectOptions extends PostProcessingEffectOptions {
  /** 线条数量 */
  count?: number;
  /** 线条强度 */
  intensity?: number;
  /** 是否使用动画 */
  animated?: boolean;
  /** 动画速度 */
  speed?: number;
  /** 线条颜色 */
  color?: THREE.Color;
  /** 是否使用噪点 */
  noise?: boolean;
  /** 噪点强度 */
  noiseIntensity?: number;
}

/**
 * 扫描线效果
 */
export class ScanlineEffect extends PostProcessingEffect {
  /** 线条数量 */
  private count: number;

  /** 线条强度 */
  private intensity: number;

  /** 是否使用动画 */
  private animated: boolean;

  /** 动画速度 */
  private speed: number;

  /** 线条颜色 */
  private color: THREE.Color;

  /** 是否使用噪点 */
  private noise: boolean;

  /** 噪点强度 */
  private noiseIntensity: number;

  /** 扫描线通道 */
  private scanlinePass: ShaderPass | null = null;

  /** 时间 */
  private time: number = 0;

  /**
   * 创建扫描线效果
   * @param options 扫描线效果选项
   */
  constructor(options: ScanlineEffectOptions = { name: 'Scanline' }) {
    super(options);

    this.count = options.count || 1024;
    this.intensity = options.intensity !== undefined ? options.intensity : 0.5;
    this.animated = options.animated !== undefined ? options.animated : true;
    this.speed = options.speed || 1.0;
    this.color = options.color || new THREE.Color(0x000000);
    this.noise = options.noise !== undefined ? options.noise : true;
    this.noiseIntensity = options.noiseIntensity !== undefined ? options.noiseIntensity : 0.2;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 创建扫描线着色器
    const scanlineShader = {
      uniforms: {
        'tDiffuse': { value: null },
        'count': { value: this.count },
        'intensity': { value: this.intensity },
        'time': { value: 0 },
        'speed': { value: this.speed },
        'scanlineColor': { value: this.color },
        'noise': { value: this.noise ? 1 : 0 },
        'noiseIntensity': { value: this.noiseIntensity },
        'resolution': { value: new THREE.Vector2(this.width, this.height) }
      },
      vertexShader: /* glsl */`
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: /* glsl */`
        uniform sampler2D tDiffuse;
        uniform float count;
        uniform float intensity;
        uniform float time;
        uniform float speed;
        uniform vec3 scanlineColor;
        uniform int noise;
        uniform float noiseIntensity;
        uniform vec2 resolution;
        
        varying vec2 vUv;
        
        // 随机函数
        float random(vec2 co) {
          return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);
        }
        
        void main() {
          // 获取原始颜色
          vec4 texel = texture2D(tDiffuse, vUv);
          
          // 计算扫描线
          float scanline = 0.0;
          
          // 静态扫描线
          float staticLine = sin(vUv.y * count) * 0.5 + 0.5;
          staticLine = pow(staticLine, 1.0);
          
          // 动态扫描线
          float dynamicLine = 0.0;
          if (speed > 0.0) {
            dynamicLine = sin(vUv.y * count + time * speed) * 0.5 + 0.5;
            dynamicLine = pow(dynamicLine, 1.0);
          }
          
          // 合并静态和动态扫描线
          scanline = mix(staticLine, dynamicLine, step(0.0, speed));
          
          // 应用噪点
          float noiseValue = 1.0;
          if (noise == 1) {
            noiseValue = random(vUv + vec2(time * 0.01, 0.0));
            noiseValue = mix(1.0, noiseValue, noiseIntensity);
          }
          
          // 应用扫描线效果
          vec3 result = mix(texel.rgb, scanlineColor, (1.0 - scanline) * intensity * noiseValue);
          
          gl_FragColor = vec4(result, texel.a);
        }
      `
    };

    // 创建扫描线通道
    this.scanlinePass = new ShaderPass(scanlineShader);

    // 设置通道
    this.pass = this.scanlinePass;
  }

  /**
   * 更新效果
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.animated || !this.scanlinePass) return;

    // 更新时间
    this.time += deltaTime;

    // 更新时间 uniform
    const uniforms = this.scanlinePass.uniforms;
    if (uniforms.time) {
      uniforms.time.value = this.time;
    }
  }

  /**
   * 设置线条数量
   * @param count 线条数量
   */
  public setCount(count: number): void {
    this.count = count;

    if (this.scanlinePass) {
      const uniforms = this.scanlinePass.uniforms;
      if (uniforms.count) {
        uniforms.count.value = count;
      }
    }
  }

  /**
   * 获取线条数量
   * @returns 线条数量
   */
  public getCount(): number {
    return this.count;
  }

  /**
   * 设置线条强度
   * @param intensity 线条强度
   */
  public setIntensity(intensity: number): void {
    this.intensity = intensity;

    if (this.scanlinePass) {
      const uniforms = this.scanlinePass.uniforms;
      if (uniforms.intensity) {
        uniforms.intensity.value = intensity;
      }
    }
  }

  /**
   * 获取线条强度
   * @returns 线条强度
   */
  public getIntensity(): number {
    return this.intensity;
  }

  /**
   * 设置是否使用动画
   * @param animated 是否使用动画
   */
  public setAnimated(animated: boolean): void {
    this.animated = animated;

    if (this.scanlinePass) {
      const uniforms = this.scanlinePass.uniforms;
      if (uniforms.speed) {
        uniforms.speed.value = animated ? this.speed : 0;
      }
    }
  }

  /**
   * 获取是否使用动画
   * @returns 是否使用动画
   */
  public isAnimated(): boolean {
    return this.animated;
  }

  /**
   * 设置动画速度
   * @param speed 动画速度
   */
  public setSpeed(speed: number): void {
    this.speed = speed;

    if (this.scanlinePass && this.animated) {
      const uniforms = this.scanlinePass.uniforms;
      if (uniforms.speed) {
        uniforms.speed.value = speed;
      }
    }
  }

  /**
   * 获取动画速度
   * @returns 动画速度
   */
  public getSpeed(): number {
    return this.speed;
  }

  /**
   * 设置线条颜色
   * @param color 线条颜色
   */
  public setColor(color: THREE.Color): void {
    this.color.copy(color);

    if (this.scanlinePass) {
      const uniforms = this.scanlinePass.uniforms;
      if (uniforms.scanlineColor) {
        uniforms.scanlineColor.value = this.color;
      }
    }
  }

  /**
   * 获取线条颜色
   * @returns 线条颜色
   */
  public getColor(): THREE.Color {
    return this.color.clone();
  }

  /**
   * 设置是否使用噪点
   * @param noise 是否使用噪点
   */
  public setNoise(noise: boolean): void {
    this.noise = noise;

    if (this.scanlinePass) {
      const uniforms = this.scanlinePass.uniforms;
      if (uniforms.noise) {
        uniforms.noise.value = noise ? 1 : 0;
      }
    }
  }

  /**
   * 获取是否使用噪点
   * @returns 是否使用噪点
   */
  public isNoise(): boolean {
    return this.noise;
  }

  /**
   * 设置噪点强度
   * @param intensity 噪点强度
   */
  public setNoiseIntensity(intensity: number): void {
    this.noiseIntensity = intensity;

    if (this.scanlinePass) {
      const uniforms = this.scanlinePass.uniforms;
      if (uniforms.noiseIntensity) {
        uniforms.noiseIntensity.value = intensity;
      }
    }
  }

  /**
   * 获取噪点强度
   * @returns 噪点强度
   */
  public getNoiseIntensity(): number {
    return this.noiseIntensity;
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    super.resize(width, height);

    if (this.scanlinePass) {
      const uniforms = this.scanlinePass.uniforms;
      if (uniforms.resolution) {
        uniforms.resolution.value.set(width, height);
      }
    }
  }
}
