/**
 * 物理系统模块
 * 导出所有物理系统相关的类和接口
 */
export { PhysicsSystem } from './PhysicsSystem';
export type { PhysicsSystemOptions } from './PhysicsSystem';
export { PhysicsBody } from './PhysicsBody';
export { BodyType } from './PhysicsBody';
export type { PhysicsBodyOptions } from './PhysicsBody';
export { PhysicsCollider, ColliderType } from './PhysicsCollider';
export type { ColliderOptions as PhysicsColliderOptions } from './PhysicsCollider';
export { CollisionEvent, CollisionEventType } from './collision/CollisionEvent';
export { CollisionDetector } from './collision/CollisionDetector';
export * from './constraints';
export declare enum PhysicsEventType {
    COLLISION_START = "collisionStart",
    COLLISION_END = "collisionEnd",
    COLLISION_STAY = "collisionStay",
    TRIGGER_ENTER = "triggerEnter",
    TRIGGER_STAY = "triggerStay",
    TRIGGER_EXIT = "triggerExit",
    BODY_ADDED = "bodyAdded",
    BODY_REMOVED = "bodyRemoved"
}
export { PhysicsRaycastResult } from './PhysicsRaycastResult';
export * from './PhysicsMaterialFactory';
export { PhysicsBodyComponent } from './components/PhysicsBodyComponent';
export { PhysicsColliderComponent } from './components/PhysicsColliderComponent';
export { PhysicsConstraintComponent } from './components/PhysicsConstraintComponent';
export { PhysicsWorldComponent } from './components/PhysicsWorldComponent';
export { CharacterControllerComponent } from './components/CharacterControllerComponent';
export { PhysicsPresetManager } from './presets/PhysicsPresetManager';
export * from './presets/PhysicsPreset';
export { CommonPhysicsPresets } from './presets/CommonPhysicsPresets';
export { PhysicsSceneExporter } from './io/PhysicsSceneExporter';
export { PhysicsSceneImporter } from './io/PhysicsSceneImporter';
export { PhysicsDebugger } from './debug/PhysicsDebugger';
export { EnhancedPhysicsDebugger } from './debug/EnhancedPhysicsDebugger';
