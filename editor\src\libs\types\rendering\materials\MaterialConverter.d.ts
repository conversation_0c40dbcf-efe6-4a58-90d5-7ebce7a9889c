/**
 * 材质转换器
 * 用于在不同类型的材质之间进行转换
 */
import * as THREE from 'three';
import { DeviceCapabilities } from '../../utils/DeviceCapabilities';
/**
 * 材质转换器配置接口
 */
export interface MaterialConverterOptions {
    /** 设备能力检测 */
    deviceCapabilities?: DeviceCapabilities;
    /** 是否保留原始材质 */
    preserveOriginal?: boolean;
}
/**
 * 材质转换器类
 */
export declare class MaterialConverter {
    /** 设备能力检测 */
    private deviceCapabilities;
    /** 是否保留原始材质 */
    private preserveOriginal;
    /** 材质转换映射 */
    private conversionMap;
    /** 材质降级映射 */
    private downgradeMap;
    /** 材质升级映射 */
    private upgradeMap;
    /**
     * 创建材质转换器
     * @param options 材质转换器配置
     */
    constructor(options?: MaterialConverterOptions);
    /**
     * 注册默认转换
     */
    private registerDefaultConversions;
    /**
     * 注册默认降级
     */
    private registerDefaultDowngrades;
    /**
     * 注册默认升级
     */
    private registerDefaultUpgrades;
    /**
     * 注册材质转换
     * @param fromType 源材质类型
     * @param toType 目标材质类型
     * @param converter 转换函数
     */
    registerConversion(fromType: string, toType: string, converter: (material: THREE.Material) => THREE.Material): void;
    /**
     * 注册材质降级
     * @param type 材质类型
     * @param downgrader 降级函数
     */
    registerDowngrade(type: string, downgrader: (material: THREE.Material) => THREE.Material): void;
    /**
     * 注册材质升级
     * @param type 材质类型
     * @param upgrader 升级函数
     */
    registerUpgrade(type: string, upgrader: (material: THREE.Material) => THREE.Material): void;
    /**
     * 转换材质
     * @param material 材质
     * @param targetType 目标类型
     * @returns 转换后的材质
     */
    convert(material: THREE.Material, targetType: string): THREE.Material;
    /**
     * 降级材质
     * @param material 材质
     * @returns 降级后的材质
     */
    downgrade(material: THREE.Material): THREE.Material;
    /**
     * 升级材质
     * @param material 材质
     * @returns 升级后的材质
     */
    upgrade(material: THREE.Material): THREE.Material;
    /**
     * 获取材质类型
     * @param material 材质
     * @returns 材质类型
     */
    private getMaterialType;
    /**
     * 设置是否保留原始材质
     * @param preserve 是否保留
     */
    setPreserveOriginal(preserve: boolean): void;
}
