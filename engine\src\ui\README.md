# UI系统

UI系统是DL（Digital Learning）引擎的核心模块之一，用于创建和管理用户界面元素。它支持2D和3D混合UI，以及UI事件和动画效果。

## 主要特性

- **2D和3D混合UI**：支持传统的2D界面元素和3D空间中的界面元素
- **事件处理**：支持鼠标、触摸和键盘事件
- **动画效果**：支持淡入淡出、移动、缩放、旋转和颜色变化等动画效果
- **布局系统**：支持网格、弹性、绝对和相对布局
- **响应式设计**：支持自动适应不同屏幕尺寸

## 系统架构

UI系统由以下几个主要部分组成：

1. **UISystem**：管理所有UI元素的主系统
2. **UI2DSystem**：管理2D界面元素的系统
3. **UI3DSystem**：管理3D空间中的界面元素的系统
4. **UIAnimationSystem**：管理UI元素动画效果的系统
5. **UILayoutSystem**：管理UI元素布局的系统

## 组件

UI系统包含以下主要组件：

1. **UIComponent**：所有UI元素的基础组件
2. **UI2DComponent**：2D界面元素组件
3. **UI3DComponent**：3D界面元素组件
4. **UIAnimationComponent**：UI动画组件
5. **UIEventComponent**：UI事件组件
6. **UILayoutComponent**：UI布局组件

## 使用示例

### 初始化UI系统

```typescript
import { World, UISystem, UI2DSystem, UI3DSystem, UIAnimationSystem, UILayoutSystem } from 'dl-engine';

// 创建世界
const world = new World();

// 创建UI系统
const uiSystem = new UISystem(world);
const ui2DSystem = new UI2DSystem(world, uiSystem);
const ui3DSystem = new UI3DSystem(world, uiSystem);
const uiAnimationSystem = new UIAnimationSystem(world, uiSystem);
const uiLayoutSystem = new UILayoutSystem(world, uiSystem);

// 添加系统到世界
world.addSystem(uiSystem);
world.addSystem(ui2DSystem);
world.addSystem(ui3DSystem);
world.addSystem(uiAnimationSystem);
world.addSystem(uiLayoutSystem);
```

### 创建2D UI元素

```typescript
import { Entity, Vector2, UIComponentType } from 'dl-engine';

// 创建实体
const entity = new Entity();

// 创建按钮
const button = ui2DSystem.createButton(entity, '点击我', {
  position: new Vector2(100, 100),
  size: new Vector2(200, 50),
  backgroundColor: '#3498db',
  borderRadius: 8,
  textColor: '#ffffff',
  fontSize: 18,
  onClick: (event) => {
    console.log('按钮被点击了！');
  }
});

// 创建文本
const text = ui2DSystem.createText(entity, '这是一段文本', {
  position: new Vector2(100, 200),
  size: new Vector2(300, 30),
  textColor: '#333333',
  fontSize: 16
});

// 创建面板
const panel = ui2DSystem.createPanel(entity, {
  position: new Vector2(400, 300),
  size: new Vector2(400, 300),
  backgroundColor: '#ffffff',
  borderColor: '#cccccc',
  borderWidth: 1,
  borderRadius: 10
});
```

### 创建3D UI元素

```typescript
import { Entity, Vector3, UIComponentType, BillboardMode } from 'dl-engine';

// 创建实体
const entity = new Entity();

// 创建3D按钮
const button3D = ui3DSystem.createButton(entity, '3D按钮', {
  position: new Vector3(0, 1.5, -2),
  size: new Vector2(200, 50),
  billboardMode: BillboardMode.Y_AXIS,
  onClick: (event) => {
    console.log('3D按钮被点击了！');
  }
});

// 创建3D面板
const panel3D = ui3DSystem.createPanel(entity, {
  position: new Vector3(1, 1.5, -2),
  size: new Vector2(400, 300),
  rotation: new Vector3(0, Math.PI / 6, 0)
});
```

### 添加动画效果

```typescript
// 创建淡入动画
const fadeInAnimation = uiAnimationSystem.createFadeInAnimation(entity, button, 500);

// 创建移动动画
const moveAnimation = uiAnimationSystem.createMoveAnimation(
  entity,
  button,
  new Vector2(300, 100),
  1000,
  0,
  UIEasing.cubicInOut
);

// 创建序列动画
uiAnimationSystem.createSequenceAnimation(entity, [fadeInAnimation, moveAnimation]);
```

### 应用布局

```typescript
// 创建网格布局
uiLayoutSystem.createGridLayout(entity, panel, {
  columns: 3,
  rows: 2,
  cellWidth: 100,
  cellHeight: 100,
  columnGap: 10,
  rowGap: 10
});

// 创建弹性布局
uiLayoutSystem.createFlexLayout(entity, panel, {
  direction: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  gap: 10
});
```

## 事件处理

UI系统支持以下事件类型：

- **点击事件**：鼠标点击或触摸
- **悬停事件**：鼠标悬停
- **拖拽事件**：拖拽开始、拖拽中、拖拽结束
- **焦点事件**：获取焦点、失去焦点
- **键盘事件**：按键按下、按键释放

示例：

```typescript
// 添加点击事件监听器
button.addEventListener('click', (event) => {
  console.log('按钮被点击了！');
});

// 添加悬停事件监听器
button.addEventListener('hover', (event) => {
  if (event.data.type === 'enter') {
    console.log('鼠标进入按钮');
  } else {
    console.log('鼠标离开按钮');
  }
});

// 添加拖拽事件监听器
button.addEventListener('dragstart', (event) => {
  console.log('开始拖拽按钮');
});

button.addEventListener('drag', (event) => {
  console.log('正在拖拽按钮', event.data.delta);
});

button.addEventListener('dragend', (event) => {
  console.log('结束拖拽按钮');
});
```

## 响应式设计

UI系统支持响应式设计，可以自动适应不同屏幕尺寸：

```typescript
// 创建响应式面板
const responsivePanel = ui2DSystem.createPanel(entity, {
  position: new Vector2(0, 0),
  size: new Vector2('100%', '100%'), // 使用百分比尺寸
  responsive: true
});

// 创建响应式布局
uiLayoutSystem.createFlexLayout(entity, responsivePanel, {
  direction: 'column',
  wrap: 'wrap',
  justifyContent: 'center',
  alignItems: 'center'
});
```

## 国际化支持

UI系统支持国际化，可以轻松切换不同语言：

```typescript
// 设置文本的国际化键
text.setI18nKey('ui.welcome', { name: '用户' });

// 切换语言时，文本会自动更新
```

## 性能优化

UI系统采用了多种优化技术，以确保高性能：

1. **批处理渲染**：减少渲染调用次数
2. **延迟更新**：只在必要时更新UI元素
3. **自动剔除**：不可见的UI元素不会被渲染
4. **事件委托**：减少事件监听器数量

## 扩展

UI系统设计为可扩展的，可以轻松添加新的UI元素类型、布局类型和动画效果。
