/**
 * 河流生成器
 * 用于生成河流水体
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { TerrainComponent } from '../../terrain/components/TerrainComponent';
/**
 * 河流路径点
 */
export interface RiverPathPoint {
    /** 位置 */
    position: THREE.Vector3;
    /** 宽度 */
    width: number;
    /** 深度 */
    depth: number;
    /** 流速 */
    flowSpeed?: number;
}
/**
 * 河流生成配置
 */
export interface RiverGeneratorConfig {
    /** 河流路径点 */
    pathPoints: RiverPathPoint[];
    /** 河流分段数 */
    segments?: number;
    /** 是否跟随地形 */
    followTerrain?: boolean;
    /** 地形偏移 */
    terrainOffset?: number;
    /** 是否使用样条曲线 */
    useSpline?: boolean;
    /** 是否生成河岸 */
    generateRiverBanks?: boolean;
    /** 河岸高度 */
    riverBankHeight?: number;
    /** 河岸宽度 */
    riverBankWidth?: number;
    /** 是否生成河床 */
    generateRiverBed?: boolean;
    /** 河床材质 */
    riverBedMaterial?: any;
    /** 是否生成水流粒子 */
    generateFlowParticles?: boolean;
    /** 水流粒子数量 */
    flowParticleCount?: number;
}
/**
 * 河流生成器
 */
export declare class RiverGenerator {
    /** 世界 */
    private world;
    /** 配置 */
    private config;
    /** 地形组件 */
    private terrainComponent;
    /** 河流实体 */
    private riverEntity;
    /** 河流水体组件 */
    private riverWaterBody;
    /** 河流路径点 */
    private pathPoints;
    /** 河流路径 */
    private path;
    /** 河流几何体 */
    private riverGeometry;
    /** 河岸几何体 */
    private riverBankGeometry;
    /** 河床几何体 */
    private riverBedGeometry;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config: RiverGeneratorConfig);
    /**
     * 设置地形组件
     * @param terrainComponent 地形组件
     */
    setTerrainComponent(terrainComponent: TerrainComponent): void;
    /**
     * 生成河流
     * @returns 河流实体
     */
    generate(): Entity;
    /**
     * 创建河流路径
     */
    private createRiverPath;
    /**
     * 创建河流几何体
     */
    private createRiverGeometry;
    /**
     * 创建河流水体
     */
    private createRiverWaterBody;
    /**
     * 创建河岸
     */
    private createRiverBanks;
    /**
     * 创建河床
     */
    private createRiverBed;
    /**
     * 创建水流粒子
     */
    private createFlowParticles;
    /**
     * 获取指定位置的地形高度
     * @param x X坐标
     * @param z Z坐标
     * @returns 地形高度，如果无法获取则返回null
     */
    private getTerrainHeight;
    /**
     * 获取指定t值处的宽度
     * @param t 路径参数（0-1）
     * @returns 宽度
     */
    private getWidthAtT;
    /**
     * 获取指定t值处的深度
     * @param t 路径参数（0-1）
     * @returns 深度
     */
    private getDepthAtT;
    /**
     * 获取指定t值处的流速
     * @param t 路径参数（0-1）
     * @returns 流速
     */
    private getFlowSpeedAtT;
    /**
     * 获取平均流向
     * @returns 平均流向
     */
    private getAverageFlowDirection;
    /**
     * 获取平均流速
     * @returns 平均流速
     */
    private getAverageFlowSpeed;
}
