import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe, Logger } from '@nestjs/common';
import { Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  // 创建Nest应用实例
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('Bootstrap');
  
  // 配置微服务
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('GAME_SERVER_HOST', 'localhost'),
      port: configService.get<number>('GAME_SERVER_MICROSERVICE_PORT', 3003),
    },
  });
  
  // 配置HTTP服务
  // 全局前缀
  app.setGlobalPrefix('api');
  
  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );
  
  // 启用CORS
  app.enableCors({
    origin: configService.get<string>('CORS_ORIGIN', '*'),
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });
  
  // 启动微服务
  await app.startAllMicroservices();
  
  // 启动HTTP服务
  const port = configService.get<number>('GAME_SERVER_PORT', 3030);
  await app.listen(port);
  
  logger.log(`游戏服务器已启动，HTTP端口: ${port}`);
  logger.log(`微服务端口: ${configService.get<number>('GAME_SERVER_MICROSERVICE_PORT', 3003)}`);
}

bootstrap().catch((err) => {
  console.error('启动游戏服务器失败:', err);
  process.exit(1);
});
