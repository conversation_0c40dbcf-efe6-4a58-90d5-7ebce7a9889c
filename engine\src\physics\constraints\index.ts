/**
 * 物理约束模块
 * 导出所有物理约束相关的类和接口
 */

// 导出物理约束基类
export { PhysicsConstraint, ConstraintType } from './PhysicsConstraint';

// 导出点对点约束
export { PointToPointConstraint } from './PointToPointConstraint';
export type { PointToPointConstraintOptions } from './PointToPointConstraint';

// 导出铰链约束
export { HingeConstraint } from './HingeConstraint';
export type { HingeConstraintOptions } from './HingeConstraint';

// 导出距离约束
export { DistanceConstraint } from './DistanceConstraint';
export type { DistanceConstraintOptions } from './DistanceConstraint';

// 导出锁定约束
export { LockConstraint } from './LockConstraint';
export type { LockConstraintOptions } from './LockConstraint';

// 导出弹簧约束
export { SpringConstraint } from './SpringConstraint';
export type { SpringConstraintOptions } from './SpringConstraint';

// 导出圆锥扭转约束
export { ConeTwistConstraint } from './ConeTwistConstraint';
export type { ConeTwistConstraintOptions } from './ConeTwistConstraint';

// 导出滑动约束
export { SliderConstraint } from './SliderConstraint';
export type { SliderConstraintOptions } from './SliderConstraint';

// 导出固定约束
export { FixedConstraint } from './FixedConstraint';
export type { FixedConstraintOptions } from './FixedConstraint';

// 导出车轮约束
export { WheelConstraint } from './WheelConstraint';
export type { WheelConstraintOptions } from './WheelConstraint';
