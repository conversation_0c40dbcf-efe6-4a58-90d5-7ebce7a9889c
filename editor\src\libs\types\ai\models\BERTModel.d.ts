/**
 * BERT模型
 * 用于文本分类、命名实体识别、文本摘要等任务
 */
import { AIModelType } from '../AIModelType';
import { AIModelConfig } from '../AIModelConfig';
import { IAIModel, TextGenerationOptions, TextClassificationResult, NamedEntityRecognitionResult, TextSummaryResult, EmotionAnalysisResult } from './IAIModel';
/**
 * BERT模型
 */
export declare class BERTModel implements IAIModel {
    /** 模型类型 */
    private readonly type;
    /** 模型配置 */
    private config;
    /** 全局配置 */
    private globalConfig;
    /** 是否已初始化 */
    private initialized;
    /** 事件发射器 */
    private eventEmitter;
    /** 模型实例 */
    private model;
    /** 分词器 */
    private tokenizer;
    /** 模型加载进度 */
    private loadProgress;
    /**
     * 构造函数
     * @param config 模型配置
     * @param globalConfig 全局配置
     */
    constructor(config?: AIModelConfig, globalConfig?: AIModelConfig);
    /**
     * 获取模型类型
     * @returns 模型类型
     */
    getType(): AIModelType;
    /**
     * 获取模型配置
     * @returns 模型配置
     */
    getConfig(): AIModelConfig;
    /**
     * 初始化模型
     * @returns 是否成功
     */
    initialize(): Promise<boolean>;
    /**
     * 生成文本
     * @param prompt 提示文本
     * @param options 生成选项
     * @returns 生成的文本
     */
    generateText(prompt: string, options?: TextGenerationOptions): Promise<string>;
    /**
     * 分类文本
     * @param text 要分类的文本
     * @param categories 可选的分类类别
     * @returns 分类结果
     */
    classifyText(text: string, categories?: string[]): Promise<TextClassificationResult>;
    /**
     * 命名实体识别
     * @param text 要识别的文本
     * @returns 识别结果
     */
    recognizeEntities(text: string): Promise<NamedEntityRecognitionResult>;
    /**
     * 文本摘要
     * @param text 要摘要的文本
     * @param maxLength 最大长度
     * @returns 摘要结果
     */
    summarizeText(text: string, maxLength?: number): Promise<TextSummaryResult>;
    /**
     * 分析情感
     * @param text 要分析的文本
     * @returns 情感分析结果
     */
    analyzeEmotion(text: string): Promise<EmotionAnalysisResult>;
    /**
     * 模拟分类文本
     * @param text 要分类的文本
     * @param categories 可选的分类类别
     * @returns 分类结果
     */
    private mockClassify;
    /**
     * 模拟命名实体识别
     * @param text 要识别的文本
     * @returns 识别结果
     */
    private mockRecognizeEntities;
    /**
     * 模拟文本摘要
     * @param text 要摘要的文本
     * @param maxLength 最大长度
     * @returns 摘要结果
     */
    private mockSummarize;
    /**
     * 模拟情感分析
     * @param text 要分析的文本
     * @returns 情感分析结果
     */
    private mockAnalyzeEmotion;
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    on(event: string, listener: (...args: any[]) => void): void;
    /**
     * 取消监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    off(event: string, listener: (...args: any[]) => void): void;
    /**
     * 释放资源
     */
    dispose(): void;
}
