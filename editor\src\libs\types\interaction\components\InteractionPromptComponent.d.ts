/**
 * InteractionPromptComponent.ts
 *
 * 交互提示组件，用于显示交互提示
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector3 } from 'three';
/**
 * 提示位置类型枚举
 */
export declare enum PromptPositionType {
    /** 世界空间 */
    WORLD = "world",
    /** 屏幕空间 */
    SCREEN = "screen",
    /** 跟随对象 */
    FOLLOW = "follow"
}
/**
 * 交互提示组件配置
 */
export interface InteractionPromptComponentConfig {
    /** 提示文本 */
    text?: string;
    /** 提示图标 */
    icon?: string;
    /** 提示位置类型 */
    positionType?: PromptPositionType;
    /** 提示位置偏移 */
    offset?: Vector3;
    /** 提示显示时间（毫秒，0表示一直显示） */
    duration?: number;
    /** 提示淡入时间（毫秒） */
    fadeInTime?: number;
    /** 提示淡出时间（毫秒） */
    fadeOutTime?: number;
    /** 提示背景颜色 */
    backgroundColor?: string;
    /** 提示文本颜色 */
    textColor?: string;
    /** 提示边框颜色 */
    borderColor?: string;
    /** 提示边框宽度 */
    borderWidth?: number;
    /** 提示边框圆角 */
    borderRadius?: number;
    /** 提示字体大小 */
    fontSize?: number;
    /** 提示字体 */
    fontFamily?: string;
    /** 提示内边距 */
    padding?: number;
    /** 是否自动隐藏 */
    autoHide?: boolean;
    /** 是否可见 */
    visible?: boolean;
}
/**
 * 交互提示组件
 * 用于显示交互提示
 */
export declare class InteractionPromptComponent extends Component {
    /** 提示文本 */
    private _text;
    /** 提示图标 */
    private _icon?;
    /** 提示位置类型 */
    private _positionType;
    /** 提示位置偏移 */
    private _offset;
    /** 提示显示时间（毫秒，0表示一直显示） */
    private _duration;
    /** 提示淡入时间（毫秒） */
    private _fadeInTime;
    /** 提示淡出时间（毫秒） */
    private _fadeOutTime;
    /** 提示背景颜色 */
    private _backgroundColor;
    /** 提示文本颜色 */
    private _textColor;
    /** 提示边框颜色 */
    private _borderColor;
    /** 提示边框宽度 */
    private _borderWidth;
    /** 提示边框圆角 */
    private _borderRadius;
    /** 提示字体大小 */
    private _fontSize;
    /** 提示字体 */
    private _fontFamily;
    /** 提示内边距 */
    private _padding;
    /** 是否自动隐藏 */
    private _autoHide;
    /** 是否可见 */
    private _visible;
    /** HTML元素 */
    private element?;
    /** 显示计时器 */
    private showTimer?;
    /** 当前不透明度 */
    private opacity;
    /** 是否正在淡入 */
    private isFadingIn;
    /** 是否正在淡出 */
    private isFadingOut;
    /** 淡入开始时间 */
    private fadeInStartTime;
    /** 淡出开始时间 */
    private fadeOutStartTime;
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param config 组件配置
     */
    constructor(entity: Entity, config?: InteractionPromptComponentConfig);
    /**
     * 获取提示文本
     */
    get text(): string;
    /**
     * 设置提示文本
     */
    set text(value: string);
    /**
     * 获取提示图标
     */
    get icon(): string | undefined;
    /**
     * 设置提示图标
     */
    set icon(value: string | undefined);
    /**
     * 获取提示位置类型
     */
    get positionType(): PromptPositionType;
    /**
     * 设置提示位置类型
     */
    set positionType(value: PromptPositionType);
    /**
     * 获取提示位置偏移
     */
    get offset(): Vector3;
    /**
     * 设置提示位置偏移
     */
    set offset(value: Vector3);
    /**
     * 获取是否可见
     */
    get visible(): boolean;
    /**
     * 设置是否可见
     */
    set visible(value: boolean);
    /**
     * 创建HTML元素
     */
    private createHTMLElement;
    /**
     * 更新文本
     */
    private updateText;
    /**
     * 更新图标
     */
    private updateIcon;
    /**
     * 更新位置
     */
    private updatePosition;
    /**
     * 显示提示
     */
    show(): void;
    /**
     * 隐藏提示
     */
    hide(): void;
    /**
     * 开始淡入
     */
    private startFadeIn;
    /**
     * 开始淡出
     */
    private startFadeOut;
    /**
     * 更新组件
     * @param deltaTime 时间增量（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁组件
     */
    dispose(): void;
}
