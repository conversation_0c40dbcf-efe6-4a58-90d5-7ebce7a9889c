/**
 * 面部动画系统
 * 用于管理角色的面部表情和口型同步
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { FacialAnimationComponent } from '../components/FacialAnimationComponent';
/**
 * 面部动画系统配置
 */
export interface FacialAnimationSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否自动检测音频 */
    autoDetectAudio?: boolean;
    /** 是否使用摄像头 */
    useWebcam?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
}
/**
 * 面部动画系统
 */
export declare class FacialAnimationSystem extends System {
    /** 系统名称 */
    static readonly NAME = "FacialAnimationSystem";
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 面部动画组件映射 */
    private components;
    /** 模型适配器系统 */
    private modelAdapterSystem;
    /** 音频上下文 */
    private audioContext;
    /** 音频分析器 */
    private audioAnalyser;
    /** 音频源 */
    private audioSource;
    /** 摄像头视频元素 */
    private videoElement;
    /** 摄像头流 */
    private webcamStream;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: FacialAnimationSystemConfig);
    /**
     * 初始化音频上下文
     */
    private initAudioContext;
    /**
     * 初始化摄像头
     */
    private initWebcam;
    /**
     * 设置模型适配器系统
     * @param system 模型适配器系统
     */
    setModelAdapterSystem(system: any): void;
    /**
     * 创建面部动画组件
     * @param entity 实体
     * @returns 面部动画组件
     */
    createFacialAnimation(entity: Entity): FacialAnimationComponent;
    /**
     * 获取面部动画组件
     * @param entity 实体
     * @returns 面部动画组件
     */
    getFacialAnimation(entity: Entity): FacialAnimationComponent | null;
    /**
     * 移除面部动画组件
     * @param entity 实体
     */
    removeFacialAnimation(entity: Entity): void;
    /**
     * 将面部动画组件与模型绑定
     * @param entity 实体
     * @param mesh 骨骼网格
     * @returns 是否成功绑定
     */
    linkToModel(entity: Entity, mesh: THREE.SkinnedMesh): boolean;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
}
