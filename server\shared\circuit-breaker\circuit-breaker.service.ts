/**
 * 熔断器服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { CircuitBreaker } from './circuit-breaker';
import { CircuitBreakerOptions, CircuitBreakerState, CircuitBreakerStats } from './circuit-breaker.interface';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * 熔断器事件
 */
export enum CircuitBreakerEvent {
  /** 状态变更 */
  STATE_CHANGED = 'circuit-breaker.state-changed',
  /** 执行成功 */
  EXECUTION_SUCCESS = 'circuit-breaker.execution-success',
  /** 执行失败 */
  EXECUTION_FAILURE = 'circuit-breaker.execution-failure',
  /** 执行拒绝 */
  EXECUTION_REJECTED = 'circuit-breaker.execution-rejected',
  /** 执行超时 */
  EXECUTION_TIMEOUT = 'circuit-breaker.execution-timeout',
  /** 执行回退 */
  EXECUTION_FALLBACK = 'circuit-breaker.execution-fallback',
}

/**
 * 熔断器服务
 * 管理多个熔断器实例
 */
@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);
  private readonly circuitBreakers = new Map<string, CircuitBreaker>();
  private readonly defaultOptions: Partial<CircuitBreakerOptions> = {
    failureThreshold: 5,
    successThreshold: 3,
    timeout: 10000,
    resetTimeout: 30000,
    enableFallback: true,
    enableMonitoring: true,
  };

  /**
   * 创建熔断器服务
   * @param eventEmitter 事件发射器
   */
  constructor(private readonly eventEmitter: EventEmitter2) {
    this.logger.log('熔断器服务已初始化');
  }

  /**
   * 创建熔断器
   * @param name 熔断器名称
   * @param options 熔断器配置
   */
  create(name: string, options: Partial<CircuitBreakerOptions> = {}): CircuitBreaker {
    if (this.circuitBreakers.has(name)) {
      return this.circuitBreakers.get(name);
    }

    const circuitBreakerOptions: CircuitBreakerOptions = {
      name,
      ...this.defaultOptions,
      ...options,
      onStateChange: (from, to) => {
        this.handleStateChange(name, from, to);
        if (options.onStateChange) {
          options.onStateChange(from, to);
        }
      },
    };

    const circuitBreaker = new CircuitBreaker(circuitBreakerOptions);
    this.circuitBreakers.set(name, circuitBreaker);

    return circuitBreaker;
  }

  /**
   * 获取熔断器
   * @param name 熔断器名称
   */
  get(name: string): CircuitBreaker | undefined {
    return this.circuitBreakers.get(name);
  }

  /**
   * 获取或创建熔断器
   * @param name 熔断器名称
   * @param options 熔断器配置
   */
  getOrCreate(name: string, options: Partial<CircuitBreakerOptions> = {}): CircuitBreaker {
    return this.get(name) || this.create(name, options);
  }

  /**
   * 删除熔断器
   * @param name 熔断器名称
   */
  remove(name: string): boolean {
    return this.circuitBreakers.delete(name);
  }

  /**
   * 获取所有熔断器
   */
  getAll(): Map<string, CircuitBreaker> {
    return this.circuitBreakers;
  }

  /**
   * 获取所有熔断器统计信息
   */
  getAllStats(): CircuitBreakerStats[] {
    return Array.from(this.circuitBreakers.values()).map(cb => cb.getStats());
  }

  /**
   * 重置所有熔断器
   */
  resetAll(): void {
    for (const circuitBreaker of this.circuitBreakers.values()) {
      circuitBreaker.reset();
    }
  }

  /**
   * 执行受熔断器保护的函数
   * @param name 熔断器名称
   * @param fn 要执行的函数
   * @param args 函数参数
   */
  async execute<T>(
    name: string,
    fn: (...args: any[]) => Promise<T>,
    ...args: any[]
  ): Promise<T> {
    const circuitBreaker = this.getOrCreate(name);
    return circuitBreaker.execute(fn, ...args);
  }

  /**
   * 处理状态变更
   * @param name 熔断器名称
   * @param from 原状态
   * @param to 新状态
   */
  private handleStateChange(name: string, from: CircuitBreakerState, to: CircuitBreakerState): void {
    this.logger.log(`熔断器 ${name} 状态从 ${from} 变为 ${to}`);
    
    this.eventEmitter.emit(CircuitBreakerEvent.STATE_CHANGED, {
      name,
      from,
      to,
      timestamp: new Date(),
    });
  }
}
