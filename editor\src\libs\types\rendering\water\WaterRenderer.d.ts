import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
/**
 * 水体渲染器配置
 */
export interface WaterRendererConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用反射 */
    enableReflection?: boolean;
    /** 是否启用折射 */
    enableRefraction?: boolean;
    /** 是否启用因果波纹 */
    enableCaustics?: boolean;
    /** 是否启用水下雾效 */
    enableUnderwaterFog?: boolean;
    /** 是否启用水下扭曲 */
    enableUnderwaterDistortion?: boolean;
    /** 是否启用深度测试 */
    enableDepthTest?: boolean;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
    /** 反射贴图分辨率 */
    reflectionMapResolution?: number;
    /** 折射贴图分辨率 */
    refractionMapResolution?: number;
    /** 是否使用延迟渲染 */
    useDeferredRendering?: boolean;
    /** 是否使用屏幕空间反射 */
    useScreenSpaceReflection?: boolean;
}
/**
 * 水体渲染器
 */
export declare class WaterRenderer extends System {
    /** 配置 */
    private config;
    /** 水体组件映射 */
    private waterBodies;
    /** 水体材质映射 */
    private waterMaterials;
    /** 反射相机 */
    private reflectionCamera;
    /** 折射相机 */
    private refractionCamera;
    /** 反射渲染目标 */
    private reflectionRenderTarget;
    /** 折射渲染目标 */
    private refractionRenderTarget;
    /** 深度渲染目标 */
    private depthRenderTarget;
    /** 因果波纹渲染目标 */
    private causticsRenderTarget;
    /** 帧计数器 */
    private frameCount;
    /** 活跃相机 */
    private activeCamera;
    /** 活跃场景 */
    private activeScene;
    /** 渲染器 */
    private renderer;
    /** 性能监视器 */
    private performanceMonitor;
    /** 反射平面 */
    private reflectionPlane;
    /** 折射平面 */
    private refractionPlane;
    /** 临时矩阵 */
    private tempMatrix;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: WaterRendererConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 创建相机
     */
    private createCameras;
    /**
     * 创建渲染目标
     */
    private createRenderTargets;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 查找活跃相机
     */
    private findActiveCamera;
    /**
     * 查找活跃场景
     */
    private findActiveScene;
    /**
     * 更新水体
     * @param deltaTime 帧间隔时间
     */
    private updateWaterBodies;
    /**
     * 创建水体材质
     * @param _component 水体组件
     * @returns 水体材质
     */
    private createWaterMaterial;
    /**
     * 添加水体组件
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterBody(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体组件
     * @param entity 实体
     */
    removeWaterBody(entity: Entity): void;
}
