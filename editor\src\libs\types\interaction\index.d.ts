/**
 * interaction/index.ts
 *
 * 导出交互系统的所有组件和类
 */
export { InteractionSystem } from './InteractionSystem';
export type { InteractionSystemConfig } from './InteractionSystem';
export { InteractableComponent, InteractionType } from './components/InteractableComponent';
export type { InteractableComponentConfig, InteractionCallback } from './components/InteractableComponent';
export { InteractionEventComponent, InteractionEventType, InteractionEvent } from './components/InteractionEventComponent';
export type { InteractionEventComponentConfig, InteractionEventData, InteractionEventListener } from './components/InteractionEventComponent';
export { InteractionPromptComponent, PromptPositionType } from './components/InteractionPromptComponent';
export type { InteractionPromptComponentConfig } from './components/InteractionPromptComponent';
export { InteractionHighlightComponent, HighlightType } from './components/InteractionHighlightComponent';
export type { InteractionHighlightComponentConfig } from './components/InteractionHighlightComponent';
export { GrabSystem } from './systems/GrabSystem';
export type { GrabSystemConfig } from './systems/GrabSystem';
export { GrabbableComponent, GrabType, Hand } from './components/GrabbableComponent';
export type { GrabbableComponentConfig } from './components/GrabbableComponent';
export { GrabberComponent } from './components/GrabberComponent';
export type { GrabberComponentConfig } from './components/GrabberComponent';
export { GrabbedComponent } from './components/GrabbedComponent';
export type { GrabbedComponentConfig } from './components/GrabbedComponent';
export { PhysicsGrabComponent } from './components/PhysicsGrabComponent';
export type { PhysicsGrabComponentConfig } from './components/PhysicsGrabComponent';
export { GrabState, GrabEventType } from './state/GrabState';
export type { GrabEventData } from './state/GrabState';
