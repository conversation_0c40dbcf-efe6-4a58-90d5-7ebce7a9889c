/**
 * 角色控制器
 * 基于Cannon.js实现的角色控制器，提供类似于Rapier3D的角色控制器功能
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
/**
 * 角色控制器选项
 */
export interface CharacterControllerOptions {
    /** 偏移量 - 用于防止角色与地面发生穿透 */
    offset?: number;
    /** 最大爬坡角度（弧度） */
    maxSlopeClimbAngle?: number;
    /** 最小滑坡角度（弧度） */
    minSlopeSlideAngle?: number;
    /** 自动台阶设置 */
    autoStep?: {
        /** 最大台阶高度 */
        maxHeight: number;
        /** 最小台阶宽度 */
        minWidth: number;
        /** 是否可以踏上动态物体 */
        stepOverDynamic: boolean;
    };
    /** 地面吸附距离（设为false禁用） */
    enableSnapToGround?: number | false;
}
/**
 * 角色控制器
 * 提供角色移动、碰撞检测、斜坡爬升、自动台阶等功能
 */
export declare class CharacterController {
    /** 关联的实体 */
    private entity;
    /** 物理世界 */
    private world;
    /** 物理体 */
    private body;
    /** 碰撞器 */
    private collider;
    /** 偏移量 */
    private offset;
    /** 最大爬坡角度（弧度） */
    private maxSlopeClimbAngle;
    /** 最小滑坡角度（弧度） */
    private minSlopeSlideAngle;
    /** 自动台阶设置 */
    private autoStep;
    /** 地面吸附 */
    private snapToGround;
    /** 计算出的移动向量 */
    private computedMovement;
    /** 是否在地面上 */
    private isGrounded;
    /** 上一次接触的地面法线 */
    private groundNormal;
    /** 射线检测起点偏移 */
    private raycastOffset;
    /** 射线检测结果缓存 */
    private raycastResults;
    /**
     * 创建角色控制器
     * @param entity 关联的实体
     * @param world 物理世界
     * @param options 控制器选项
     */
    constructor(entity: Entity, world: CANNON.World, options?: CharacterControllerOptions);
    /**
     * 计算碰撞器移动
     * @param desiredTranslation 期望的移动向量
     * @param filterGroups 碰撞组过滤
     * @param filterPredicate 碰撞过滤谓词函数
     */
    computeColliderMovement(desiredTranslation: THREE.Vector3, filterGroups?: number, filterPredicate?: (body: CANNON.Body) => boolean): void;
    /**
     * 获取计算出的移动向量
     * @returns 计算出的移动向量
     */
    getComputedMovement(): THREE.Vector3;
    /**
     * 获取偏移量
     * @returns 偏移量
     */
    getOffset(): number;
    /**
     * 设置最大爬坡角度
     * @param angle 角度（弧度）
     */
    setMaxSlopeClimbAngle(angle: number): void;
    /**
     * 获取最大爬坡角度
     * @returns 角度（弧度）
     */
    getMaxSlopeClimbAngle(): number;
    /**
     * 设置最小滑坡角度
     * @param angle 角度（弧度）
     */
    setMinSlopeSlideAngle(angle: number): void;
    /**
     * 获取最小滑坡角度
     * @returns 角度（弧度）
     */
    getMinSlopeSlideAngle(): number;
    /**
     * 启用自动台阶
     * @param maxHeight 最大台阶高度
     * @param minWidth 最小台阶宽度
     * @param stepOverDynamic 是否可以踏上动态物体
     */
    enableAutoStep(maxHeight: number, minWidth: number, stepOverDynamic: boolean): void;
    /**
     * 禁用自动台阶
     */
    disableAutoStep(): void;
    /**
     * 启用地面吸附
     * @param distance 吸附距离
     */
    enableSnapToGround(distance: number): void;
    /**
     * 禁用地面吸附
     */
    disableSnapToGround(): void;
    /**
     * 是否启用自动台阶
     * @returns 是否启用
     */
    isAutoStepEnabled(): boolean;
    /**
     * 是否启用地面吸附
     * @returns 是否启用
     */
    isSnapToGroundEnabled(): boolean;
    /**
     * 获取自动台阶最大高度
     * @returns 最大高度
     */
    getAutoStepMaxHeight(): number;
    /**
     * 获取自动台阶最小宽度
     * @returns 最小宽度
     */
    getAutoStepMinWidth(): number;
    /**
     * 是否可以踏上动态物体
     * @returns 是否可以
     */
    canStepOverDynamic(): boolean;
    /**
     * 是否在地面上
     * @returns 是否在地面上
     */
    isOnGround(): boolean;
    /**
     * 获取地面法线
     * @returns 地面法线
     */
    getGroundNormal(): THREE.Vector3;
    /**
     * 更新是否在地面上的状态
     * @private
     */
    private updateGroundedState;
    /**
     * 处理斜坡
     * @param desiredTranslation 期望的移动向量
     * @private
     */
    private handleSlopes;
    /**
     * 处理自动台阶
     * @param desiredTranslation 期望的移动向量
     * @param filterGroups 碰撞组过滤
     * @param filterPredicate 碰撞过滤谓词函数
     * @private
     */
    private handleAutoStep;
    /**
     * 处理地面吸附
     * @private
     */
    private handleSnapToGround;
    /**
     * 处理碰撞
     * @param movement 移动向量
     * @param filterGroups 碰撞组过滤
     * @param filterPredicate 碰撞过滤谓词函数
     * @private
     */
    private handleCollisions;
}
