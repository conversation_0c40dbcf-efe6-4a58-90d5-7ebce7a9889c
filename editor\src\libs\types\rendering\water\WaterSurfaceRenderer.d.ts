import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
/**
 * 水面渲染器配置
 */
export interface WaterSurfaceRendererConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用反射 */
    enableReflection?: boolean;
    /** 是否启用折射 */
    enableRefraction?: boolean;
    /** 是否启用因果波纹 */
    enableCaustics?: boolean;
    /** 是否启用水下雾效 */
    enableUnderwaterFog?: boolean;
    /** 是否启用水下扭曲 */
    enableUnderwaterDistortion?: boolean;
    /** 是否启用深度测试 */
    enableDepthTest?: boolean;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
    /** 反射贴图分辨率 */
    reflectionMapResolution?: number;
    /** 折射贴图分辨率 */
    refractionMapResolution?: number;
    /** 是否使用屏幕空间反射 */
    useScreenSpaceReflection?: boolean;
    /** 是否使用高质量波动 */
    useHighQualityWaves?: boolean;
    /** 是否使用GPU加速 */
    useGPUAcceleration?: boolean;
}
/**
 * 水面渲染器
 */
export declare class WaterSurfaceRenderer extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 配置 */
    private config;
    /** 水体组件映射 */
    private waterBodies;
    /** 水体材质映射 */
    private waterMaterials;
    /** 反射相机 */
    private reflectionCamera;
    /** 折射相机 */
    private refractionCamera;
    /** 反射渲染目标 */
    private reflectionRenderTarget;
    /** 折射渲染目标 */
    private refractionRenderTarget;
    /** 深度渲染目标 */
    private depthRenderTarget;
    /** 因果波纹渲染目标 */
    private causticsRenderTarget;
    /** 帧计数器 */
    private frameCount;
    /** 活跃相机 */
    private activeCamera;
    /** 活跃场景 */
    private activeScene;
    /** 渲染器 */
    private renderer;
    /** 性能监视器 */
    private performanceMonitor;
    /** 反射平面 */
    private reflectionPlane;
    /** 折射平面 */
    private refractionPlane;
    /** 临时矩阵 */
    private tempMatrix;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: WaterSurfaceRendererConfig);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化渲染目标
     */
    private initializeRenderTargets;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 创建相机
     */
    private createCameras;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新活跃相机
     */
    private updateActiveCamera;
    /**
     * 更新活跃场景
     */
    private updateActiveScene;
    /**
     * 更新所有水体
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterBodies;
    /**
     * 更新水体
     * @param entityId 实体ID
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterBody;
    /**
     * 更新反射贴图
     * @param waterBody 水体组件
     */
    private updateReflectionMap;
    /**
     * 更新折射贴图
     * @param waterBody 水体组件
     */
    private updateRefractionMap;
    /**
     * 更新因果波纹贴图
     * @param waterBody 水体组件
     */
    private updateCausticsMap;
    /**
     * 更新水体材质
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    private updateWaterMaterial;
    /**
     * 创建水体材质
     * @param waterBody 水体组件
     * @returns 水体材质
     */
    private createWaterMaterial;
    /**
     * 添加水体组件
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterBody(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体组件
     * @param entity 实体
     */
    removeWaterBody(entity: Entity): void;
}
