/**
 * 运动模糊效果
 * 增加动态场景的真实感
 */
import * as THREE from 'three';
// 使用类型断言导入 ShaderPass
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * 运动模糊效果选项
 */
export interface MotionBlurEffectOptions extends PostProcessingEffectOptions {
  /** 强度 */
  intensity?: number;
  /** 采样数 */
  samples?: number;
  /** 是否使用相机运动 */
  useCameraMotion?: boolean;
  /** 是否使用对象运动 */
  useObjectMotion?: boolean;
}

/**
 * 运动模糊效果
 */
export class MotionBlurEffect extends PostProcessingEffect {
  /** 强度 */
  private intensity: number;

  /** 采样数 */
  private samples: number;

  /** 是否使用相机运动 */
  private useCameraMotion: boolean;

  /** 是否使用对象运动 */
  private useObjectMotion: boolean;

  /** 运动模糊通道 */
  private motionBlurPass: ShaderPass | null = null;

  /** 场景 */
  private scene: THREE.Scene | null = null;

  /** 相机 */
  private camera: THREE.Camera | null = null;

  /** 上一帧相机位置 */
  private previousCameraPosition: THREE.Vector3 = new THREE.Vector3();

  /** 上一帧相机四元数 */
  private previousCameraQuaternion: THREE.Quaternion = new THREE.Quaternion();

  /** 上一帧相机投影矩阵 */
  private previousCameraProjectionMatrix: THREE.Matrix4 = new THREE.Matrix4();

  /** 上一帧相机视图矩阵 */
  private previousCameraViewMatrix: THREE.Matrix4 = new THREE.Matrix4();

  /** 速度缓冲区 */
  private velocityRenderTarget: THREE.WebGLRenderTarget | null = null;

  /** 速度材质 */
  private velocityMaterial: THREE.ShaderMaterial | null = null;

  /** 对象上一帧矩阵映射 */
  private previousMatrixMap: Map<THREE.Object3D, THREE.Matrix4> = new Map();

  /**
   * 创建运动模糊效果
   * @param options 运动模糊效果选项
   */
  constructor(options: MotionBlurEffectOptions = { name: 'MotionBlur' }) {
    super(options);

    this.intensity = options.intensity !== undefined ? options.intensity : 1.0;
    this.samples = options.samples || 32;
    this.useCameraMotion = options.useCameraMotion !== undefined ? options.useCameraMotion : true;
    this.useObjectMotion = options.useObjectMotion !== undefined ? options.useObjectMotion : true;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 如果没有场景或相机，则不创建通道
    if (!this.scene || !this.camera || !this.renderer) return;

    // 创建速度缓冲区
    this.velocityRenderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {
      minFilter: THREE.NearestFilter,
      magFilter: THREE.NearestFilter,
      format: THREE.RGBAFormat,
      type: THREE.HalfFloatType
    });

    // 创建速度材质
    this.velocityMaterial = new THREE.ShaderMaterial({
      uniforms: {
        'previousModelViewMatrix': { value: new THREE.Matrix4() },
        'previousProjectionMatrix': { value: new THREE.Matrix4() },
        'currentModelViewMatrix': { value: new THREE.Matrix4() },
        'currentProjectionMatrix': { value: new THREE.Matrix4() }
      },
      vertexShader: /* glsl */`
        uniform mat4 previousModelViewMatrix;
        uniform mat4 currentModelViewMatrix;
        uniform mat4 previousProjectionMatrix;
        uniform mat4 currentProjectionMatrix;
        
        varying vec4 vPrevPosition;
        varying vec4 vCurrentPosition;
        
        void main() {
          vPrevPosition = previousProjectionMatrix * previousModelViewMatrix * vec4(position, 1.0);
          vCurrentPosition = currentProjectionMatrix * currentModelViewMatrix * vec4(position, 1.0);
          gl_Position = vCurrentPosition;
        }
      `,
      fragmentShader: /* glsl */`
        varying vec4 vPrevPosition;
        varying vec4 vCurrentPosition;
        
        void main() {
          // 计算NDC空间中的速度
          vec2 prevPos = vPrevPosition.xy / vPrevPosition.w;
          vec2 currPos = vCurrentPosition.xy / vCurrentPosition.w;
          
          // 速度向量
          vec2 velocity = (currPos - prevPos) * 0.5;
          
          // 输出速度到颜色
          gl_FragColor = vec4(velocity, 0.0, 1.0);
        }
      `
    });

    // 创建运动模糊着色器
    const motionBlurShader = {
      uniforms: {
        'tDiffuse': { value: null },
        'tVelocity': { value: this.velocityRenderTarget.texture },
        'intensity': { value: this.intensity },
        'samples': { value: this.samples },
        'resolution': { value: new THREE.Vector2(this.width, this.height) }
      },
      vertexShader: /* glsl */`
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: /* glsl */`
        uniform sampler2D tDiffuse;
        uniform sampler2D tVelocity;
        uniform float intensity;
        uniform int samples;
        uniform vec2 resolution;
        
        varying vec2 vUv;
        
        void main() {
          // 获取速度
          vec2 velocity = texture2D(tVelocity, vUv).rg;
          
          // 应用强度
          velocity *= intensity;
          
          // 初始颜色
          vec4 color = texture2D(tDiffuse, vUv);
          
          // 如果速度很小，则不应用模糊
          float speed = length(velocity);
          if (speed < 0.001) {
            gl_FragColor = color;
            return;
          }
          
          // 应用运动模糊
          float samples_float = float(samples);
          for (int i = 1; i < 64; i++) {
            if (i >= samples) break;
            
            // 计算采样偏移
            float offset = float(i) / samples_float;
            vec2 uv = vUv - velocity * offset;
            
            // 采样并累加颜色
            color += texture2D(tDiffuse, uv);
          }
          
          // 平均颜色
          color /= samples_float;
          
          gl_FragColor = color;
        }
      `
    };

    // 创建运动模糊通道
    this.motionBlurPass = new ShaderPass(motionBlurShader);

    // 设置通道
    this.pass = this.motionBlurPass;

    // 保存初始相机状态
    if (this.camera) {
      this.previousCameraPosition.copy(this.camera.position);
      if (this.camera instanceof THREE.PerspectiveCamera || this.camera instanceof THREE.OrthographicCamera) {
        this.previousCameraQuaternion.copy(this.camera.quaternion);
      }
      this.previousCameraProjectionMatrix.copy(this.camera.projectionMatrix);
      this.previousCameraViewMatrix.copy(this.camera.matrixWorldInverse);
    }
  }

  /**
   * 设置场景和相机
   * @param scene 场景
   * @param camera 相机
   */
  public setSceneAndCamera(scene: THREE.Scene, camera: THREE.Camera): void {
    this.scene = scene;
    this.camera = camera;

    // 如果已初始化，则重新创建通道
    if (this.initialized) {
      this.createPass();
    }
  }

  /**
   * 设置强度
   * @param intensity 强度
   */
  public setIntensity(intensity: number): void {
    this.intensity = intensity;

    // 更新通道的强度
    if (this.motionBlurPass && this.motionBlurPass.uniforms.intensity) {
      this.motionBlurPass.uniforms.intensity.value = intensity;
    }
  }

  /**
   * 获取强度
   * @returns 强度
   */
  public getIntensity(): number {
    return this.intensity;
  }

  /**
   * 设置采样数
   * @param samples 采样数
   */
  public setSamples(samples: number): void {
    this.samples = samples;

    // 更新通道的采样数
    if (this.motionBlurPass && this.motionBlurPass.uniforms.samples) {
      this.motionBlurPass.uniforms.samples.value = samples;
    }
  }

  /**
   * 获取采样数
   * @returns 采样数
   */
  public getSamples(): number {
    return this.samples;
  }

  /**
   * 设置是否使用相机运动
   * @param use 是否使用
   */
  public setUseCameraMotion(use: boolean): void {
    this.useCameraMotion = use;
  }

  /**
   * 获取是否使用相机运动
   * @returns 是否使用
   */
  public isUseCameraMotion(): boolean {
    return this.useCameraMotion;
  }

  /**
   * 设置是否使用对象运动
   * @param use 是否使用
   */
  public setUseObjectMotion(use: boolean): void {
    this.useObjectMotion = use;
  }

  /**
   * 获取是否使用对象运动
   * @returns 是否使用
   */
  public isUseObjectMotion(): boolean {
    return this.useObjectMotion;
  }

  /**
   * 更新速度缓冲区
   */
  private updateVelocityBuffer(): void {
    if (!this.scene || !this.camera || !this.renderer || !this.velocityRenderTarget || !this.velocityMaterial) return;

    // 保存当前渲染器状态
    const currentRenderTarget = this.renderer.getRenderTarget();
    const currentAutoClear = this.renderer.autoClear;

    // 设置渲染目标
    this.renderer.setRenderTarget(this.velocityRenderTarget);
    this.renderer.autoClear = true;
    this.renderer.clear();

    // 遍历场景中的所有网格
    this.scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        // 保存原始材质
        const originalMaterial = object.material;

        // 设置速度材质
        object.material = this.velocityMaterial!;

        // 设置当前矩阵
        this.velocityMaterial!.uniforms.currentModelViewMatrix.value.multiplyMatrices(
          this.camera!.matrixWorldInverse,
          object.matrixWorld
        );
        this.velocityMaterial!.uniforms.currentProjectionMatrix.value.copy(this.camera!.projectionMatrix);

        // 设置上一帧矩阵
        const previousMatrix = this.previousMatrixMap.get(object);
        if (previousMatrix) {
          this.velocityMaterial!.uniforms.previousModelViewMatrix.value.multiplyMatrices(
            this.previousCameraViewMatrix,
            previousMatrix
          );
        } else {
          this.velocityMaterial!.uniforms.previousModelViewMatrix.value.multiplyMatrices(
            this.previousCameraViewMatrix,
            object.matrixWorld
          );
        }
        this.velocityMaterial!.uniforms.previousProjectionMatrix.value.copy(this.previousCameraProjectionMatrix);

        // 渲染对象
        this.renderer.render(object, this.camera);

        // 恢复原始材质
        object.material = originalMaterial;

        // 保存当前矩阵
        if (this.useObjectMotion) {
          if (!this.previousMatrixMap.has(object)) {
            this.previousMatrixMap.set(object, new THREE.Matrix4());
          }
          this.previousMatrixMap.get(object)!.copy(object.matrixWorld);
        }
      }
    });

    // 恢复渲染器状态
    this.renderer.setRenderTarget(currentRenderTarget);
    this.renderer.autoClear = currentAutoClear;
  }

  /**
   * 更新效果
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    if (!this.camera) return;

    // 更新速度缓冲区
    this.updateVelocityBuffer();

    // 保存当前相机状态
    if (this.useCameraMotion) {
      this.previousCameraPosition.copy(this.camera.position);
      if (this.camera instanceof THREE.PerspectiveCamera || this.camera instanceof THREE.OrthographicCamera) {
        this.previousCameraQuaternion.copy(this.camera.quaternion);
      }
      this.previousCameraProjectionMatrix.copy(this.camera.projectionMatrix);
      this.previousCameraViewMatrix.copy(this.camera.matrixWorldInverse);
    }
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    super.resize(width, height);

    // 更新速度缓冲区大小
    if (this.velocityRenderTarget) {
      this.velocityRenderTarget.setSize(width, height);
    }

    // 更新分辨率Uniform
    if (this.motionBlurPass && this.motionBlurPass.uniforms.resolution) {
      this.motionBlurPass.uniforms.resolution.value.set(width, height);
    }
  }

  /**
   * 销毁效果
   */
  public dispose(): void {
    // 销毁速度缓冲区
    if (this.velocityRenderTarget) {
      (this.velocityRenderTarget as any).dispose();
      this.velocityRenderTarget = null;
    }

    // 销毁速度材质
    if (this.velocityMaterial) {
      (this.velocityMaterial as any).dispose();
      this.velocityMaterial = null;
    }

    // 清空矩阵映射
    this.previousMatrixMap.clear();

    super.dispose();
  }
}
