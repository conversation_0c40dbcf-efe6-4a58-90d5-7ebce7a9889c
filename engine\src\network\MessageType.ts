/**
 * 消息类型
 * 定义网络消息的类型常量
 */
export class MessageType {
  // 系统消息
  /** 系统消息 */
  public static readonly SYSTEM = 'system';
  /** 心跳消息 */
  public static readonly HEARTBEAT = 'heartbeat';
  /** 确认消息 */
  public static readonly ACK = 'ack';
  /** 错误消息 */
  public static readonly ERROR = 'error';
  
  // 连接消息
  /** 连接请求 */
  public static readonly CONNECT = 'connect';
  /** 连接成功 */
  public static readonly CONNECT_SUCCESS = 'connect_success';
  /** 连接失败 */
  public static readonly CONNECT_FAILED = 'connect_failed';
  /** 断开连接 */
  public static readonly DISCONNECT = 'disconnect';
  
  // 房间消息
  /** 加入房间 */
  public static readonly JOIN_ROOM = 'join_room';
  /** 加入房间成功 */
  public static readonly JOIN_ROOM_SUCCESS = 'join_room_success';
  /** 加入房间失败 */
  public static readonly JOIN_ROOM_FAILED = 'join_room_failed';
  /** 离开房间 */
  public static readonly LEAVE_ROOM = 'leave_room';
  /** 用户加入 */
  public static readonly USER_JOINED = 'user_joined';
  /** 用户离开 */
  public static readonly USER_LEFT = 'user_left';
  /** 房间列表 */
  public static readonly ROOM_LIST = 'room_list';
  /** 房间信息 */
  public static readonly ROOM_INFO = 'room_info';
  
  // 用户消息
  /** 用户列表 */
  public static readonly USER_LIST = 'user_list';
  /** 用户信息 */
  public static readonly USER_INFO = 'user_info';
  /** 用户状态 */
  public static readonly USER_STATUS = 'user_status';
  
  // 聊天消息
  /** 聊天消息 */
  public static readonly CHAT = 'chat';
  /** 私聊消息 */
  public static readonly PRIVATE_CHAT = 'private_chat';
  /** 广播消息 */
  public static readonly BROADCAST = 'broadcast';
  /** 直接消息 */
  public static readonly DIRECT = 'direct';
  
  // 实体消息
  /** 创建实体 */
  public static readonly ENTITY_CREATE = 'entity_create';
  /** 更新实体 */
  public static readonly ENTITY_UPDATE = 'entity_update';
  /** 删除实体 */
  public static readonly ENTITY_DELETE = 'entity_delete';
  /** 实体列表 */
  public static readonly ENTITY_LIST = 'entity_list';
  /** 实体事件 */
  public static readonly ENTITY_EVENT = 'entity_event';
  /** 实体同步 */
  public static readonly ENTITY_SYNC = 'entity_sync';
  /** 实体所有权 */
  public static readonly ENTITY_OWNERSHIP = 'entity_ownership';
  /** 实体所有权请求 */
  public static readonly ENTITY_OWNERSHIP_REQUEST = 'entity_ownership_request';
  /** 实体所有权转移 */
  public static readonly ENTITY_OWNERSHIP_TRANSFER = 'entity_ownership_transfer';
  
  // WebRTC消息
  /** WebRTC提议 */
  public static readonly WEBRTC_OFFER = 'webrtc_offer';
  /** WebRTC应答 */
  public static readonly WEBRTC_ANSWER = 'webrtc_answer';
  /** WebRTC ICE候选 */
  public static readonly WEBRTC_ICE_CANDIDATE = 'webrtc_ice_candidate';
  /** WebRTC连接状态 */
  public static readonly WEBRTC_CONNECTION_STATE = 'webrtc_connection_state';
  
  // 媒体消息
  /** 媒体流 */
  public static readonly MEDIA_STREAM = 'media_stream';
  /** 音频流 */
  public static readonly AUDIO_STREAM = 'audio_stream';
  /** 视频流 */
  public static readonly VIDEO_STREAM = 'video_stream';
  /** 屏幕共享流 */
  public static readonly SCREEN_SHARE_STREAM = 'screen_share_stream';
  /** 媒体控制 */
  public static readonly MEDIA_CONTROL = 'media_control';
  
  // 游戏消息
  /** 游戏状态 */
  public static readonly GAME_STATE = 'game_state';
  /** 游戏事件 */
  public static readonly GAME_EVENT = 'game_event';
  /** 游戏命令 */
  public static readonly GAME_COMMAND = 'game_command';
  /** 游戏同步 */
  public static readonly GAME_SYNC = 'game_sync';
  
  // 自定义消息
  /** 自定义消息 */
  public static readonly CUSTOM = 'custom';
}
