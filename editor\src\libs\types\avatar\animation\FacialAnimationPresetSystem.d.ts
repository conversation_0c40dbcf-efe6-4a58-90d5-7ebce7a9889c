/**
 * 面部动画预设系统
 * 提供预定义的面部动画模板和预设
 */
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';
/**
 * 面部动画预设系统配置
 */
export interface FacialAnimationPresetSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否自动加载预设 */
    autoLoadPresets?: boolean;
    /** 预设路径 */
    presetsPath?: string;
    /** 是否使用本地存储 */
    useLocalStorage?: boolean;
}
/**
 * 面部动画关键帧
 */
export interface FacialAnimationKeyframe {
    /** 时间（秒） */
    time: number;
    /** 表情类型 */
    expression?: FacialExpressionType;
    /** 表情权重 */
    expressionWeight?: number;
    /** 口型 */
    viseme?: VisemeType;
    /** 口型权重 */
    visemeWeight?: number;
    /** 混合形状权重 */
    blendShapeWeights?: {
        [key: string]: number;
    };
    /** 缓动类型 */
    easingType?: string;
    /** 缓动参数 */
    easingParams?: any;
}
/**
 * 面部动画预设
 */
export interface FacialAnimationPreset {
    /** 预设ID */
    id: string;
    /** 预设名称 */
    name: string;
    /** 预设描述 */
    description?: string;
    /** 预设类别 */
    category?: string;
    /** 预设标签 */
    tags?: string[];
    /** 预设作者 */
    author?: string;
    /** 预设版本 */
    version?: string;
    /** 预设创建时间 */
    createdAt?: string;
    /** 预设更新时间 */
    updatedAt?: string;
    /** 预设缩略图 */
    thumbnail?: string;
    /** 预设持续时间（秒） */
    duration: number;
    /** 是否循环 */
    loop?: boolean;
    /** 关键帧 */
    keyframes: FacialAnimationKeyframe[];
    /** 元数据 */
    metadata?: any;
}
/**
 * 面部动画模板
 */
export interface FacialAnimationTemplate {
    /** 模板ID */
    id: string;
    /** 模板名称 */
    name: string;
    /** 模板描述 */
    description?: string;
    /** 模板类别 */
    category?: string;
    /** 模板标签 */
    tags?: string[];
    /** 模板作者 */
    author?: string;
    /** 模板版本 */
    version?: string;
    /** 模板创建时间 */
    createdAt?: string;
    /** 模板更新时间 */
    updatedAt?: string;
    /** 模板缩略图 */
    thumbnail?: string;
    /** 模板参数 */
    parameters: {
        /** 参数ID */
        id: string;
        /** 参数名称 */
        name: string;
        /** 参数描述 */
        description?: string;
        /** 参数类型 */
        type: 'number' | 'boolean' | 'string' | 'enum';
        /** 参数默认值 */
        defaultValue: any;
        /** 参数最小值（数字类型） */
        min?: number;
        /** 参数最大值（数字类型） */
        max?: number;
        /** 参数步长（数字类型） */
        step?: number;
        /** 参数选项（枚举类型） */
        options?: {
            value: any;
            label: string;
        }[];
    }[];
    /** 模板生成函数 */
    generate: (parameters: any) => FacialAnimationPreset;
    /** 元数据 */
    metadata?: any;
}
/**
 * 面部动画预设系统
 */
export declare class FacialAnimationPresetSystem extends System {
    /** 系统名称 */
    static readonly NAME = "FacialAnimationPresetSystem";
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 预设映射 */
    private presets;
    /** 模板映射 */
    private templates;
    /** 活动预设映射 */
    private activePresets;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: FacialAnimationPresetSystemConfig);
    /**
     * 加载预设
     */
    private loadPresets;
    /**
     * 从本地存储加载预设
     */
    private loadPresetsFromLocalStorage;
    /**
     * 从服务器加载预设
     */
    private loadPresetsFromServer;
    /**
     * 初始化默认模板
     */
    private initDefaultTemplates;
    /**
     * 创建基本表情模板
     * @returns 基本表情模板
     */
    private createBasicExpressionTemplate;
    /**
     * 创建说话模板
     * @returns 说话模板
     */
    private createTalkingTemplate;
    /**
     * 创建情感说话模板
     * @returns 情感说话模板
     */
    private createEmotionalTalkingTemplate;
    /**
     * 创建眨眼模板
     * @returns 眨眼模板
     */
    private createBlinkTemplate;
    /**
     * 创建呼吸模板
     * @returns 呼吸模板
     */
    private createBreathingTemplate;
    /**
     * 创建待机模板
     * @returns 待机模板
     */
    private createIdleTemplate;
    /**
     * 添加预设
     * @param preset 预设
     */
    addPreset(preset: FacialAnimationPreset): void;
    /**
     * 移除预设
     * @param presetId 预设ID
     * @returns 是否成功移除
     */
    removePreset(presetId: string): boolean;
    /**
     * 获取预设
     * @param presetId 预设ID
     * @returns 预设
     */
    getPreset(presetId: string): FacialAnimationPreset | undefined;
    /**
     * 获取所有预设
     * @returns 预设数组
     */
    getAllPresets(): FacialAnimationPreset[];
    /**
     * 添加模板
     * @param template 模板
     */
    addTemplate(template: FacialAnimationTemplate): void;
    /**
     * 移除模板
     * @param templateId 模板ID
     * @returns 是否成功移除
     */
    removeTemplate(templateId: string): boolean;
    /**
     * 获取模板
     * @param templateId 模板ID
     * @returns 模板
     */
    getTemplate(templateId: string): FacialAnimationTemplate | undefined;
    /**
     * 获取所有模板
     * @returns 模板数组
     */
    getAllTemplates(): FacialAnimationTemplate[];
    /**
     * 从模板生成预设
     * @param templateId 模板ID
     * @param parameters 参数
     * @returns 生成的预设
     */
    generatePresetFromTemplate(templateId: string, parameters: any): FacialAnimationPreset | null;
    /**
     * 应用预设到实体
     * @param entity 实体
     * @param presetId 预设ID
     * @param loop 是否循环
     * @returns 是否成功应用
     */
    applyPreset(entity: Entity, presetId: string, loop?: boolean): boolean;
    /**
     * 停止实体的预设
     * @param entity 实体
     * @returns 是否成功停止
     */
    stopPreset(entity: Entity): boolean;
    /**
     * 应用关键帧到面部动画组件
     * @param component 面部动画组件
     * @param keyframes 关键帧数组
     */
    private applyKeyframes;
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    update(_deltaTime: number): void;
    /**
     * 销毁系统
     */
    destroy(): void;
    /**
     * 获取事件发射器
     * @returns 事件发射器
     */
    getEventEmitter(): EventEmitter;
}
