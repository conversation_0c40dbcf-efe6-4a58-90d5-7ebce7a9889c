import { Scene } from './Scene';
import { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import type { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 场景序列化选项
 */
export interface SceneSerializeOptions {
    /** 是否包含实体 */
    includeEntities?: boolean;
    /** 是否包含组件 */
    includeComponents?: boolean;
    /** 是否包含天空盒 */
    includeSkybox?: boolean;
    /** 是否包含环境光 */
    includeAmbientLight?: boolean;
    /** 是否包含雾效 */
    includeFog?: boolean;
    /** 是否美化JSON输出 */
    prettyPrint?: boolean;
    /** 是否包含元数据 */
    includeMetadata?: boolean;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 场景反序列化选项
 */
export interface SceneDeserializeOptions {
    /** 是否包含实体 */
    includeEntities?: boolean;
    /** 是否包含组件 */
    includeComponents?: boolean;
    /** 是否包含天空盒 */
    includeSkybox?: boolean;
    /** 是否包含环境光 */
    includeAmbientLight?: boolean;
    /** 是否包含雾效 */
    includeFog?: boolean;
    /** 是否保留现有实体 */
    keepExistingEntities?: boolean;
    /** 是否合并到现有场景 */
    mergeWithExisting?: boolean;
    /** 组件反序列化器映射 */
    componentDeserializers?: Map<string, (data: any, entity: Entity) => Component | null>;
}
/**
 * 场景序列化数据
 */
export interface SceneSerializedData {
    /** 版本 */
    version: string;
    /** 场景ID */
    id: string;
    /** 场景名称 */
    name: string;
    /** 实体数据 */
    entities?: any[];
    /** 天空盒数据 */
    skybox?: any;
    /** 环境光数据 */
    ambientLight?: any;
    /** 雾效数据 */
    fog?: any;
    /** 元数据 */
    metadata?: Record<string, any>;
}
/**
 * 场景序列化器
 */
export declare class SceneSerializer extends EventEmitter {
    /** 当前版本 */
    private static readonly VERSION;
    /** 世界实例 */
    private world;
    /** 组件序列化器映射 */
    private componentSerializers;
    /** 组件反序列化器映射 */
    private componentDeserializers;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建场景序列化器实例
     * @param world 世界实例
     */
    constructor(world?: World);
    /**
     * 初始化场景序列化器
     * @param world 世界实例
     */
    initialize(world?: World): void;
    /**
     * 注册默认组件序列化器
     */
    private registerDefaultComponentSerializers;
    /**
     * 注册组件序列化器
     * @param componentType 组件类型
     * @param serializer 序列化器函数
     */
    registerComponentSerializer(componentType: string, serializer: (component: Component) => any): void;
    /**
     * 注册组件反序列化器
     * @param componentType 组件类型
     * @param deserializer 反序列化器函数
     */
    registerComponentDeserializer(componentType: string, deserializer: (data: any, entity: Entity) => Component | null): void;
    /**
     * 序列化场景
     * @param scene 场景实例
     * @param options 序列化选项
     * @returns 序列化数据
     */
    serializeScene(scene: Scene, options?: SceneSerializeOptions): SceneSerializedData;
    /**
     * 序列化实体
     * @param scene 场景实例
     * @param options 序列化选项
     * @returns 实体数据数组
     */
    private serializeEntities;
    /**
     * 序列化组件
     * @param entity 实体实例
     * @returns 组件数据数组
     */
    private serializeComponents;
    /**
     * 默认序列化组件
     * @param component 组件实例
     * @returns 组件数据
     */
    private defaultSerializeComponent;
    /**
     * 序列化值
     * @param value 值
     * @returns 序列化后的值
     */
    private serializeValue;
    /**
     * 序列化天空盒
     * @param skybox 天空盒实例
     * @returns 天空盒数据
     */
    private serializeSkybox;
    /**
     * 序列化环境光
     * @param ambientLight 环境光实例
     * @returns 环境光数据
     */
    private serializeAmbientLight;
    /**
     * 序列化雾效
     * @param fog 雾效实例
     * @returns 雾效数据
     */
    private serializeFog;
    /**
     * 将场景序列化为对象
     * @param scene 场景实例
     * @param options 序列化选项
     * @returns 序列化数据对象
     */
    serializeToObject(scene: Scene, options?: SceneSerializeOptions): SceneSerializedData;
    /**
     * 将场景序列化为JSON字符串
     * @param scene 场景实例
     * @param options 序列化选项
     * @returns JSON字符串
     */
    serializeToJSON(scene: Scene, options?: SceneSerializeOptions): string;
    /**
     * 反序列化场景
     * @param data 序列化数据
     * @param targetScene 目标场景
     * @param options 反序列化选项
     * @returns 反序列化后的场景
     */
    deserializeScene(data: SceneSerializedData, targetScene?: Scene, options?: SceneDeserializeOptions): Scene;
    /**
     * 检查版本兼容性
     * @param version 版本字符串
     * @returns 是否兼容
     */
    private checkVersionCompatibility;
    /**
     * 反序列化实体
     * @param entitiesData 实体数据数组
     * @param scene 场景实例
     * @param options 反序列化选项
     */
    private deserializeEntities;
    /**
     * 反序列化组件
     * @param componentsData 组件数据数组
     * @param entity 实体实例
     * @param options 反序列化选项
     */
    private deserializeComponents;
    /**
     * 默认反序列化组件
     * @param componentType 组件类型
     * @param data 组件数据
     * @param entity 实体实例
     * @returns 组件实例
     */
    private defaultDeserializeComponent;
    /**
     * 反序列化值
     * @param value 序列化后的值
     * @returns 原始值
     */
    private deserializeValue;
    /**
     * 反序列化天空盒
     * @param skyboxData 天空盒数据
     * @returns 天空盒实例
     */
    private deserializeSkybox;
    /**
     * 反序列化环境光
     * @param ambientLightData 环境光数据
     * @param scene 场景实例
     */
    private deserializeAmbientLight;
    /**
     * 反序列化雾效
     * @param fogData 雾效数据
     * @param scene 场景实例
     */
    private deserializeFog;
    /**
     * 反序列化单个实体
     * @param data 序列化数据
     * @param scene 目标场景
     * @param options 反序列化选项
     * @returns 反序列化后的实体
     */
    deserializeEntity(data: SceneSerializedData, scene: Scene, options?: SceneDeserializeOptions): Entity | null;
    /**
     * 从JSON字符串反序列化场景
     * @param json JSON字符串
     * @param targetScene 目标场景
     * @param options 反序列化选项
     * @returns 反序列化后的场景
     */
    deserializeFromJSON(json: string, targetScene?: Scene, options?: SceneDeserializeOptions): Scene;
    /**
     * 设置世界实例
     * @param world 世界实例
     */
    setWorld(world: World): void;
    /**
     * 获取世界实例
     * @returns 世界实例
     */
    getWorld(): World | null;
    /**
     * 销毁场景序列化器
     */
    dispose(): void;
}
