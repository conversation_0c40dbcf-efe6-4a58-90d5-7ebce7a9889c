/**
 * 批处理系统
 * 用于合并多个网格以减少绘制调用
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 批处理系统配置接口
 */
export interface BatchingSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率（帧） */
  updateFrequency?: number;
  /** 是否使用静态批处理 */
  useStaticBatching?: boolean;
  /** 是否使用动态批处理 */
  useDynamicBatching?: boolean;
  /** 是否使用GPU实例化 */
  useGPUInstancing?: boolean;
  /** 最大批处理大小 */
  maxBatchSize?: number;
  /** 最大实例数量 */
  maxInstanceCount?: number;
  /** 是否优化几何体 */
  optimizeGeometry?: boolean;
  /** 是否合并材质 */
  mergeMaterials?: boolean;
  /** 是否使用八叉树 */
  useOctree?: boolean;
}

/**
 * 批处理系统事件类型
 */
export enum BatchingSystemEventType {
  /** 批处理创建 */
  BATCH_CREATED = 'batch_created',
  /** 批处理更新 */
  BATCH_UPDATED = 'batch_updated',
  /** 批处理销毁 */
  BATCH_DESTROYED = 'batch_destroyed',
  /** 实例添加 */
  INSTANCE_ADDED = 'instance_added',
  /** 实例更新 */
  INSTANCE_UPDATED = 'instance_updated',
  /** 实例移除 */
  INSTANCE_REMOVED = 'instance_removed'
}

/**
 * 批处理组
 */
export interface BatchGroup {
  /** 批处理ID */
  id: string;
  /** 批处理名称 */
  name: string;
  /** 合并后的网格 */
  mesh: THREE.Mesh | THREE.InstancedMesh;
  /** 原始网格列表 */
  originalMeshes: THREE.Mesh[];
  /** 原始实体列表 */
  originalEntities: Entity[];
  /** 是否是静态批处理 */
  isStatic: boolean;
  /** 是否是实例化批处理 */
  isInstanced: boolean;
  /** 变换矩阵 */
  matrices?: THREE.Matrix4[];
  /** 颜色数组 */
  colors?: THREE.Color[];
  /** 是否可见 */
  visible: boolean;
  /** 是否已更新 */
  isDirty: boolean;
}

/**
 * 批处理系统
 */
export class BatchingSystem extends System {
  /** 系统类型 */
  private static readonly TYPE: string = 'BatchingSystem';

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率（帧） */
  private updateFrequency: number;

  /** 当前帧计数 */
  private frameCount: number = 0;

  /** 是否使用静态批处理 */
  private useStaticBatching: boolean;

  /** 是否使用动态批处理 */
  private useDynamicBatching: boolean;

  /** 是否使用GPU实例化 */
  private useGPUInstancing: boolean;

  /** 最大批处理大小 */
  private maxBatchSize: number;

  /** 最大实例数量 */
  private maxInstanceCount: number;

  /** 是否优化几何体 */
  private optimizeGeometry: boolean;

  /** 是否合并材质 */
  private shouldMergeMaterials: boolean;

  /** 是否使用八叉树 */
  private useOctree: boolean;

  /** 活跃场景 */
  private activeScene: Scene | null = null;

  /** 批处理组列表 */
  private batchGroups: Map<string, BatchGroup> = new Map();

  /** 实体到批处理组的映射 */
  private entityToBatchMap: Map<Entity, string> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 创建批处理系统
   * @param options 批处理系统配置
   */
  constructor(options: BatchingSystemOptions = {}) {
    super();

    this.setEnabled(options.enabled !== undefined ? options.enabled : true);
    this.autoUpdate = options.autoUpdate !== undefined ? options.autoUpdate : true;
    this.updateFrequency = options.updateFrequency !== undefined ? options.updateFrequency : 1;
    this.useStaticBatching = options.useStaticBatching !== undefined ? options.useStaticBatching : true;
    this.useDynamicBatching = options.useDynamicBatching !== undefined ? options.useDynamicBatching : true;
    this.useGPUInstancing = options.useGPUInstancing !== undefined ? options.useGPUInstancing : true;
    this.maxBatchSize = options.maxBatchSize !== undefined ? options.maxBatchSize : 1000;
    this.maxInstanceCount = options.maxInstanceCount !== undefined ? options.maxInstanceCount : 1000;
    this.optimizeGeometry = options.optimizeGeometry !== undefined ? options.optimizeGeometry : true;
    this.shouldMergeMaterials = options.mergeMaterials !== undefined ? options.mergeMaterials : false;
    this.useOctree = options.useOctree !== undefined ? options.useOctree : false;

    // 设置优先级
    this.setPriority(20); // 批处理系统优先级较高，在LOD系统之后执行
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return BatchingSystem.TYPE;
  }

  /**
   * 设置活跃场景
   * @param scene 场景
   */
  public setActiveScene(scene: Scene): void {
    this.activeScene = scene;
  }

  /**
   * 获取活跃场景
   * @returns 活跃场景
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled() || !this.autoUpdate) {
      return;
    }

    // 如果没有活跃场景，则不更新
    if (!this.activeScene) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 更新所有批处理组
    this.updateBatchGroups();
  }

  /**
   * 更新所有批处理组
   */
  private updateBatchGroups(): void {
    // 更新动态批处理组
    for (const [id, group] of Array.from(this.batchGroups.entries())) {
      if (!group.isStatic && group.isDirty) {
        this.updateBatchGroup(group);
        group.isDirty = false;
      }
    }
  }

  /**
   * 更新批处理组
   * @param group 批处理组
   */
  private updateBatchGroup(group: BatchGroup): void {
    if (group.isInstanced) {
      this.updateInstancedBatchGroup(group);
    } else {
      this.updateMergedBatchGroup(group);
    }

    // 发出批处理更新事件
    this.eventEmitter.emit(BatchingSystemEventType.BATCH_UPDATED, group);
  }

  /**
   * 更新实例化批处理组
   * @param group 批处理组
   */
  private updateInstancedBatchGroup(group: BatchGroup): void {
    if (!group.isInstanced || !(group.mesh instanceof THREE.InstancedMesh)) {
      return;
    }

    // 更新实例矩阵
    for (let i = 0; i < group.originalEntities.length; i++) {
      const entity = group.originalEntities[i];
      const transform = entity.getComponent('Transform') as any as any as any as Transform;

      if (transform && group.matrices && i < group.matrices.length) {
        group.matrices[i].copy(transform.getWorldMatrix());
        group.mesh.setMatrixAt(i, group.matrices[i]);
      }

      // 如果有颜色属性，也更新颜色
      if (group.colors && i < group.colors.length) {
        const meshComponent = entity.getComponent('MeshComponent') as any as any;
        if (meshComponent && meshComponent.getMaterial() instanceof THREE.MeshBasicMaterial) {
          const material = meshComponent.getMaterial() as THREE.MeshBasicMaterial;
          if (material.color) {
            group.colors[i].copy(material.color);
            group.mesh.setColorAt(i, group.colors[i]);
          }
        }
      }
    }

    // 更新实例缓冲区
    if (group.mesh.instanceMatrix) {
      group.mesh.instanceMatrix.needsUpdate = true;
    }

    if (group.mesh.instanceColor) {
      group.mesh.instanceColor.needsUpdate = true;
    }
  }

  /**
   * 更新合并批处理组
   * @param group 批处理组
   */
  private updateMergedBatchGroup(group: BatchGroup): void {
    // 对于合并的批处理组，需要重新创建合并网格
    const newMesh = this.createMergedMesh(group.originalMeshes, group.originalEntities);

    if (newMesh) {
      // 替换旧网格
      const oldMesh = group.mesh;
      group.mesh = newMesh;

      // 如果旧网格在场景中，则替换
      if (this.activeScene) {
        const threeScene = this.activeScene.getThreeScene();
        if (oldMesh.parent === threeScene) {
          threeScene.remove(oldMesh);
          threeScene.add(newMesh);
        }
      }

      // 释放旧网格资源
      if (oldMesh.geometry) {
        (oldMesh.geometry as any).dispose();
      }

      if (Array.isArray(oldMesh.material)) {
        for (const material of oldMesh.material) {
          (material as any).dispose();
        }
      } else if (oldMesh.material) {
        (oldMesh.material as any).dispose();
      }
    }
  }

  /**
   * 创建静态批处理组
   * @param entities 实体列表
   * @param name 批处理组名称
   * @returns 批处理组ID
   */
  public createStaticBatch(entities: Entity[], name: string = '静态批处理组'): string | null {
    if (!this.useStaticBatching || entities.length === 0) {
      return null;
    }

    // 收集网格
    const meshes: THREE.Mesh[] = [];
    for (const entity of entities) {
      const meshComponent = entity.getComponent('MeshComponent') as any as any;
      if (meshComponent && meshComponent.getMesh() instanceof THREE.Mesh) {
        meshes.push(meshComponent.getMesh() as THREE.Mesh);
      }
    }

    if (meshes.length === 0) {
      return null;
    }

    // 创建合并网格
    const mergedMesh = this.createMergedMesh(meshes, entities);
    if (!mergedMesh) {
      return null;
    }

    // 创建批处理组
    const batchId = `static_batch_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    const batchGroup: BatchGroup = {
      id: batchId,
      name,
      mesh: mergedMesh,
      originalMeshes: meshes,
      originalEntities: entities,
      isStatic: true,
      isInstanced: false,
      visible: true,
      isDirty: false
    };

    // 添加到批处理组列表
    this.batchGroups.set(batchId, batchGroup);

    // 更新实体到批处理组的映射
    for (const entity of entities) {
      this.entityToBatchMap.set(entity, batchId);
    }

    // 如果有活跃场景，则添加到场景
    if (this.activeScene) {
      this.activeScene.getThreeScene().add(mergedMesh);
    }

    // 发出批处理创建事件
    this.eventEmitter.emit(BatchingSystemEventType.BATCH_CREATED, batchGroup);

    return batchId;
  }

  /**
   * 创建动态批处理组
   * @param entities 实体列表
   * @param name 批处理组名称
   * @returns 批处理组ID
   */
  public createDynamicBatch(entities: Entity[], name: string = '动态批处理组'): string | null {
    if (!this.useDynamicBatching || entities.length === 0) {
      return null;
    }

    // 如果支持GPU实例化，则优先使用实例化
    if (this.useGPUInstancing && this.canUseInstancing(entities)) {
      return this.createInstancedBatch(entities, name);
    }

    // 否则使用合并网格
    return this.createMergedBatch(entities, name, false);
  }

  /**
   * 创建实例化批处理组
   * @param entities 实体列表
   * @param name 批处理组名称
   * @returns 批处理组ID
   */
  public createInstancedBatch(entities: Entity[], name: string = '实例化批处理组'): string | null {
    if (!this.useGPUInstancing || entities.length === 0) {
      return null;
    }

    // 检查是否可以使用实例化
    if (!this.canUseInstancing(entities)) {
      return null;
    }

    // 获取第一个实体的网格作为模板
    const firstEntity = entities[0];
    const firstMeshComponent = firstEntity.getComponent('MeshComponent') as any as any;
    if (!firstMeshComponent || !(firstMeshComponent.getMesh() instanceof THREE.Mesh)) {
      return null;
    }

    const templateMesh = firstMeshComponent.getMesh() as THREE.Mesh;
    const geometry = templateMesh.geometry.clone();
    const material = templateMesh.material instanceof THREE.Material
      ? templateMesh.material.clone()
      : Array.isArray(templateMesh.material)
        ? templateMesh.material.map(m => m.clone())
        : new THREE.MeshBasicMaterial();

    // 创建实例化网格
    const instanceCount = Math.min(entities.length, this.maxInstanceCount);
    const instancedMesh = new THREE.InstancedMesh(geometry, material, instanceCount);
    instancedMesh.name = name;
    instancedMesh.castShadow = templateMesh.castShadow;
    instancedMesh.receiveShadow = templateMesh.receiveShadow;

    // 创建矩阵和颜色数组
    const matrices: THREE.Matrix4[] = [];
    const colors: THREE.Color[] = [];

    // 初始化实例
    for (let i = 0; i < instanceCount; i++) {
      const entity = entities[i];
      const transform = entity.getComponent('Transform') as any as Transform;

      // 创建矩阵
      const matrix = new THREE.Matrix4();
      if (transform) {
        matrix.copy(transform.getWorldMatrix());
      }
      matrices.push(matrix);
      instancedMesh.setMatrixAt(i, matrix);

      // 创建颜色
      const color = new THREE.Color(0xffffff);
      const meshComponent = entity.getComponent('MeshComponent') as any as any;
      if (meshComponent && meshComponent.getMaterial() instanceof THREE.MeshBasicMaterial) {
        const material = meshComponent.getMaterial() as THREE.MeshBasicMaterial;
        if (material.color) {
          color.copy(material.color);
        }
      }
      colors.push(color);
      instancedMesh.setColorAt(i, color);
    }

    // 更新实例缓冲区
    instancedMesh.instanceMatrix.needsUpdate = true;
    if (instancedMesh.instanceColor) {
      instancedMesh.instanceColor.needsUpdate = true;
    }

    // 创建批处理组
    const batchId = `instanced_batch_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    const batchGroup: BatchGroup = {
      id: batchId,
      name,
      mesh: instancedMesh,
      originalMeshes: entities.map(e => {
        const meshComponent = e.getComponent('MeshComponent') as any as any;
        return meshComponent ? meshComponent.getMesh() as THREE.Mesh : null;
      }).filter(m => m !== null) as THREE.Mesh[],
      originalEntities: entities,
      isStatic: false,
      isInstanced: true,
      matrices,
      colors,
      visible: true,
      isDirty: false
    };

    // 添加到批处理组列表
    this.batchGroups.set(batchId, batchGroup);

    // 更新实体到批处理组的映射
    for (const entity of entities) {
      this.entityToBatchMap.set(entity, batchId);
    }

    // 如果有活跃场景，则添加到场景
    if (this.activeScene) {
      this.activeScene.getThreeScene().add(instancedMesh);
    }

    // 发出批处理创建事件
    this.eventEmitter.emit(BatchingSystemEventType.BATCH_CREATED, batchGroup);

    return batchId;
  }

  /**
   * 创建合并批处理组
   * @param entities 实体列表
   * @param name 批处理组名称
   * @param isStatic 是否是静态批处理
   * @returns 批处理组ID
   */
  private createMergedBatch(entities: Entity[], name: string, isStatic: boolean): string | null {
    // 收集网格
    const meshes: THREE.Mesh[] = [];
    for (const entity of entities) {
      const meshComponent = entity.getComponent('MeshComponent') as any as any;
      if (meshComponent && meshComponent.getMesh() instanceof THREE.Mesh) {
        meshes.push(meshComponent.getMesh() as THREE.Mesh);
      }
    }

    if (meshes.length === 0) {
      return null;
    }

    // 创建合并网格
    const mergedMesh = this.createMergedMesh(meshes, entities);
    if (!mergedMesh) {
      return null;
    }

    // 创建批处理组
    const batchId = `${isStatic ? 'static' : 'dynamic'}_batch_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    const batchGroup: BatchGroup = {
      id: batchId,
      name,
      mesh: mergedMesh,
      originalMeshes: meshes,
      originalEntities: entities,
      isStatic,
      isInstanced: false,
      visible: true,
      isDirty: !isStatic // 动态批处理默认为脏
    };

    // 添加到批处理组列表
    this.batchGroups.set(batchId, batchGroup);

    // 更新实体到批处理组的映射
    for (const entity of entities) {
      this.entityToBatchMap.set(entity, batchId);
    }

    // 如果有活跃场景，则添加到场景
    if (this.activeScene) {
      this.activeScene.getThreeScene().add(mergedMesh);
    }

    // 发出批处理创建事件
    this.eventEmitter.emit(BatchingSystemEventType.BATCH_CREATED, batchGroup);

    return batchId;
  }

  /**
   * 创建合并网格
   * @param meshes 网格列表
   * @param entities 实体列表
   * @returns 合并后的网格
   */
  private createMergedMesh(meshes: THREE.Mesh[], entities: Entity[]): THREE.Mesh | null {
    if (meshes.length === 0) {
      return null;
    }

    try {
      // 创建几何体和材质列表
      const geometries: THREE.BufferGeometry[] = [];
      const materials: THREE.Material[] = [];

      // 收集几何体和材质
      for (let i = 0; i < meshes.length; i++) {
        const mesh = meshes[i];
        const entity = entities[i];
        const transform = entity.getComponent('Transform') as any as any as any as Transform;

        if (mesh.geometry && transform) {
          // 克隆几何体
          const geometry = mesh.geometry.clone();

          // 应用变换
          const matrix = new THREE.Matrix4();
          matrix.copy(transform.getWorldMatrix());
          geometry.applyMatrix4(matrix);

          // 添加到列表
          geometries.push(geometry);

          // 收集材质
          if (mesh.material) {
            if (Array.isArray(mesh.material)) {
              materials.push(...mesh.material);
            } else {
              materials.push(mesh.material);
            }
          }
        }
      }

      if (geometries.length === 0) {
        return null;
      }

      // 合并几何体
      const mergedGeometry = this.mergeGeometries(geometries);
      if (!mergedGeometry) {
        return null;
      }

      // 处理材质
      let mergedMaterial: THREE.Material | THREE.Material[];
      if (this.shouldMergeMaterials) {
        // 尝试合并材质
        mergedMaterial = this.mergeMaterials(materials);
      } else {
        // 使用第一个材质
        mergedMaterial = materials[0].clone();
      }

      // 创建合并网格
      const mergedMesh = new THREE.Mesh(mergedGeometry, mergedMaterial);
      mergedMesh.name = `BatchedMesh_${Date.now()}`;
      mergedMesh.castShadow = meshes[0].castShadow;
      mergedMesh.receiveShadow = meshes[0].receiveShadow;

      // 优化几何体
      if (this.optimizeGeometry) {
        mergedGeometry.computeBoundingBox();
        mergedGeometry.computeBoundingSphere();
      }

      return mergedMesh;
    } catch (error) {
      Debug.error('创建合并网格失败:', error);
      return null;
    }
  }

  /**
   * 合并几何体
   * @param geometries 几何体列表
   * @returns 合并后的几何体
   */
  private mergeGeometries(geometries: THREE.BufferGeometry[]): THREE.BufferGeometry | null {
    if (geometries.length === 0) {
      return null;
    }

    if (geometries.length === 1) {
      return geometries[0].clone();
    }

    try {
      // 使用Three.js的BufferGeometryUtils.mergeBufferGeometries
      // 注意：在实际应用中，应该使用Three.js的BufferGeometryUtils
      // 这里简化实现
      const mergedGeometry = new THREE.BufferGeometry();

      // 计算顶点总数
      let vertexCount = 0;
      let indexCount = 0;

      for (const geometry of geometries) {
        if (geometry.index) {
          indexCount += geometry.index.count;
        } else {
          indexCount += geometry.attributes.position.count;
        }
        vertexCount += geometry.attributes.position.count;
      }

      // 创建合并后的属性
      const positions = new Float32Array(vertexCount * 3);
      const normals = new Float32Array(vertexCount * 3);
      const uvs = new Float32Array(vertexCount * 2);
      const indices = new Uint32Array(indexCount);

      // 合并属性
      let positionOffset = 0;
      let normalOffset = 0;
      let uvOffset = 0;
      let indexOffset = 0;
      let vertexOffset = 0;

      for (const geometry of geometries) {
        // 合并位置
        const positionAttribute = geometry.attributes.position;
        for (let i = 0; i < positionAttribute.count; i++) {
          positions[positionOffset++] = positionAttribute.getX(i);
          positions[positionOffset++] = positionAttribute.getY(i);
          positions[positionOffset++] = positionAttribute.getZ(i);
        }

        // 合并法线
        if (geometry.attributes.normal) {
          const normalAttribute = geometry.attributes.normal;
          for (let i = 0; i < normalAttribute.count; i++) {
            normals[normalOffset++] = normalAttribute.getX(i);
            normals[normalOffset++] = normalAttribute.getY(i);
            normals[normalOffset++] = normalAttribute.getZ(i);
          }
        } else {
          // 如果没有法线，则创建默认法线
          for (let i = 0; i < positionAttribute.count; i++) {
            normals[normalOffset++] = 0;
            normals[normalOffset++] = 1;
            normals[normalOffset++] = 0;
          }
        }

        // 合并UV
        if (geometry.attributes.uv) {
          const uvAttribute = geometry.attributes.uv;
          for (let i = 0; i < uvAttribute.count; i++) {
            uvs[uvOffset++] = uvAttribute.getX(i);
            uvs[uvOffset++] = uvAttribute.getY(i);
          }
        } else {
          // 如果没有UV，则创建默认UV
          for (let i = 0; i < positionAttribute.count; i++) {
            uvs[uvOffset++] = 0;
            uvs[uvOffset++] = 0;
          }
        }

        // 合并索引
        if (geometry.index) {
          const indexAttribute = geometry.index;
          for (let i = 0; i < indexAttribute.count; i++) {
            indices[indexOffset++] = indexAttribute.getX(i) + vertexOffset;
          }
        } else {
          // 如果没有索引，则创建默认索引
          for (let i = 0; i < positionAttribute.count; i++) {
            indices[indexOffset++] = i + vertexOffset;
          }
        }

        vertexOffset += positionAttribute.count;
      }

      // 设置属性
      mergedGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      mergedGeometry.setAttribute('normal', new THREE.BufferAttribute(normals, 3));
      mergedGeometry.setAttribute('uv', new THREE.BufferAttribute(uvs, 2));
      mergedGeometry.setIndex(new THREE.BufferAttribute(indices, 1));

      return mergedGeometry;
    } catch (error) {
      Debug.error('合并几何体失败:', error);
      return null;
    }
  }

  /**
   * 合并材质
   * @param materials 材质列表
   * @returns 合并后的材质
   */
  private mergeMaterials(materials: THREE.Material[]): THREE.Material {
    if (materials.length === 0) {
      return new THREE.MeshBasicMaterial();
    }

    if (materials.length === 1) {
      return materials[0].clone();
    }

    // 简单实现：使用第一个材质
    // 在实际应用中，应该根据材质类型和属性进行更复杂的合并
    return materials[0].clone();
  }

  /**
   * 检查是否可以使用实例化
   * @param entities 实体列表
   * @returns 是否可以使用实例化
   */
  private canUseInstancing(entities: Entity[]): boolean {
    if (entities.length === 0) {
      return false;
    }

    // 获取第一个实体的网格
    const firstEntity = entities[0];
    const firstMeshComponent = firstEntity.getComponent('MeshComponent') as any as any;
    if (!firstMeshComponent || !(firstMeshComponent.getMesh() instanceof THREE.Mesh)) {
      return false;
    }

    const firstMesh = firstMeshComponent.getMesh() as THREE.Mesh;
    const firstGeometry = firstMesh.geometry;

    // 检查所有实体是否使用相同的几何体
    for (let i = 1; i < entities.length; i++) {
      const entity = entities[i];
      const meshComponent = entity.getComponent('MeshComponent') as any as any;

      if (!meshComponent || !(meshComponent.getMesh() instanceof THREE.Mesh)) {
        return false;
      }

      const mesh = meshComponent.getMesh() as THREE.Mesh;

      // 检查几何体是否相同
      if (mesh.geometry !== firstGeometry) {
        return false;
      }
    }

    return true;
  }

  /**
   * 销毁批处理组
   * @param batchId 批处理组ID
   * @returns 是否成功销毁
   */
  public destroyBatch(batchId: string): boolean {
    const batchGroup = this.batchGroups.get(batchId);
    if (!batchGroup) {
      return false;
    }

    // 从场景中移除网格
    if (this.activeScene) {
      this.activeScene.getThreeScene().remove(batchGroup.mesh);
    }

    // 释放资源
    if (batchGroup.mesh.geometry) {
      (batchGroup.mesh.geometry as any).dispose();
    }

    if (Array.isArray(batchGroup.mesh.material)) {
      for (const material of batchGroup.mesh.material) {
        (material as any).dispose();
      }
    } else if (batchGroup.mesh.material) {
      (batchGroup.mesh.material as any).dispose();
    }

    // 清除实体到批处理组的映射
    for (const entity of batchGroup.originalEntities) {
      this.entityToBatchMap.delete(entity);
    }

    // 从批处理组列表中移除
    this.batchGroups.delete(batchId);

    // 发出批处理销毁事件
    this.eventEmitter.emit(BatchingSystemEventType.BATCH_DESTROYED, batchGroup);

    return true;
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: BatchingSystemEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: BatchingSystemEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 销毁所有批处理组
    for (const batchId of Array.from(this.batchGroups.keys())) {
      this.destroyBatch(batchId);
    }

    // 清空映射
    this.entityToBatchMap.clear();

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();
  }
}
