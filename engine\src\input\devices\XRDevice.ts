/**
 * XR输入设备
 */
import { BaseInputDevice } from '../InputDevice';

/**
 * XR输入设备
 */
export class XRDevice extends BaseInputDevice {
  /** XR会话 */
  private session: XRSession | null = null;

  /** 输入源映射 */
  private inputSources: Map<number, XRInputSource> = new Map();

  /** XR事件处理器 */
  private xrEventHandlers: { [key: string]: (event: Event) => void } = {};

  /**
   * 创建XR输入设备
   */
  constructor() {
    super('xr');

    // 初始化事件处理器
    this.initEventHandlers();
  }

  /**
   * 初始化事件处理器
   */
  private initEventHandlers(): void {
    // 输入源变化事件
    this.xrEventHandlers.inputsourceschange = this.handleInputSourcesChange.bind(this);

    // 选择开始事件
    this.xrEventHandlers.selectstart = this.handleSelectStart.bind(this);

    // 选择结束事件
    this.xrEventHandlers.selectend = this.handleSelectEnd.bind(this);

    // 选择事件
    this.xrEventHandlers.select = this.handleSelect.bind(this);

    // 挤压开始事件
    this.xrEventHandlers.squeezestart = this.handleSqueezeStart.bind(this);

    // 挤压结束事件
    this.xrEventHandlers.squeezeend = this.handleSqueezeEnd.bind(this);

    // 挤压事件
    this.xrEventHandlers.squeeze = this.handleSqueeze.bind(this);
  }

  /**
   * 初始化设备
   */
  public initialize(): void {
    if (this.initialized) return;

    // 检查XR支持
    if (navigator.xr) {
      navigator.xr.isSessionSupported('immersive-vr').then((supported) => {
        if (supported) {
          this.eventEmitter.emit('supported', { type: 'immersive-vr' });
        }
      });

      navigator.xr.isSessionSupported('immersive-ar').then((supported) => {
        if (supported) {
          this.eventEmitter.emit('supported', { type: 'immersive-ar' });
        }
      });
    }

    super.initialize();
  }

  /**
   * 销毁设备
   */
  public destroy(): void {
    if (this.destroyed) return;

    // 结束XR会话
    this.endSession();

    super.destroy();
  }

  /**
   * 更新设备状态
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || this.destroyed) return;

    // 更新输入源状态
    this.updateInputSources();

    super.update(deltaTime);
  }

  /**
   * 更新输入源状态
   */
  private updateInputSources(): void {
    if (!this.session) return;

    // 更新输入源状态
    for (const inputSource of this.session.inputSources) {
      // 更新输入源映射
      this.inputSources.set(this.getInputSourceId(inputSource), inputSource);

      // 更新游戏手柄状态
      if (inputSource.gamepad) {
        const gamepad = inputSource.gamepad;
        const inputSourceId = this.getInputSourceId(inputSource);

        // 更新按钮状态
        for (let i = 0; i < gamepad.buttons.length; i++) {
          const button = gamepad.buttons[i];
          const buttonKey = `${inputSourceId}:button:${i}`;
          const pressed = button.pressed || button.value > 0.5;
          const wasPressed = this.getValue(buttonKey) || false;

          // 更新按钮状态
          this.setValue(buttonKey, pressed);
          this.setValue(`${inputSourceId}:button:${i}:value`, button.value);

          // 触发按钮事件
          if (pressed && !wasPressed) {
            this.eventEmitter.emit(`${buttonKey}:down`, {
              inputSource,
              button: i,
              value: button.value
            });
          } else if (!pressed && wasPressed) {
            this.eventEmitter.emit(`${buttonKey}:up`, {
              inputSource,
              button: i,
              value: button.value
            });
          }
        }

        // 更新轴状态
        for (let i = 0; i < gamepad.axes.length; i++) {
          const axisKey = `${inputSourceId}:axis:${i}`;
          const value = gamepad.axes[i];
          const oldValue = this.getValue(axisKey) || 0;

          // 更新轴状态
          this.setValue(axisKey, value);

          // 触发轴事件
          if (value !== oldValue) {
            this.eventEmitter.emit(`${axisKey}:change`, {
              inputSource,
              axis: i,
              value: value
            });
          }
        }
      }
    }
  }

  /**
   * 开始XR会话
   * @param sessionType 会话类型
   * @param sessionOptions 会话选项
   * @returns 是否成功
   */
  public async startSession(sessionType: XRSessionMode = 'immersive-vr', sessionOptions: XRSessionInit = {}): Promise<boolean> {
    if (!navigator.xr) return false;

    try {
      // 检查会话支持
      const supported = await navigator.xr.isSessionSupported(sessionType);
      if (!supported) return false;

      // 请求会话
      this.session = await navigator.xr.requestSession(sessionType, sessionOptions);

      // 添加事件监听器
      this.addEventListeners();

      // 触发会话开始事件
      this.eventEmitter.emit('sessionstart', { session: this.session });

      return true;
    } catch (error) {
      console.error('Failed to start XR session:', error);
      return false;
    }
  }

  /**
   * 结束XR会话
   */
  public async endSession(): Promise<void> {
    if (!this.session) return;

    try {
      // 移除事件监听器
      this.removeEventListeners();

      // 结束会话
      await this.session.end();

      // 清空输入源映射
      this.inputSources.clear();

      // 触发会话结束事件
      this.eventEmitter.emit('sessionend', { session: this.session });

      this.session = null;
    } catch (error) {
      console.error('Failed to end XR session:', error);
    }
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    if (!this.session) return;

    // 添加XR事件监听器
    for (const [event, handler] of Object.entries(this.xrEventHandlers)) {
      this.session.addEventListener(event, handler as EventListener);
    }
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    if (!this.session) return;

    // 移除XR事件监听器
    for (const [event, handler] of Object.entries(this.xrEventHandlers)) {
      this.session.removeEventListener(event, handler as EventListener);
    }
  }

  /**
   * 处理输入源变化事件
   * @param event 输入源变化事件
   */
  private handleInputSourcesChange(event: XRInputSourcesChangeEvent): void {
    // 添加新输入源
    for (const inputSource of event.added) {
      const inputSourceId = this.getInputSourceId(inputSource);
      this.inputSources.set(inputSourceId, inputSource);
      this.setValue(`${inputSourceId}:active`, true);
      this.setValue(`${inputSourceId}:handedness`, inputSource.handedness);
      this.setValue(`${inputSourceId}:targetRayMode`, inputSource.targetRayMode);

      // 触发输入源添加事件
      this.eventEmitter.emit('inputsourceadded', { inputSource });
    }

    // 移除旧输入源
    for (const inputSource of event.removed) {
      const inputSourceId = this.getInputSourceId(inputSource);
      this.inputSources.delete(inputSourceId);
      this.setValue(`${inputSourceId}:active`, false);

      // 触发输入源移除事件
      this.eventEmitter.emit('inputsourceremoved', { inputSource });
    }
  }

  /**
   * 处理选择开始事件
   * @param event 选择事件
   */
  private handleSelectStart(event: XRInputSourceEvent): void {
    const inputSource = event.inputSource;
    const inputSourceId = this.getInputSourceId(inputSource);
    this.setValue(`${inputSourceId}:select`, true);

    // 触发选择开始事件
    this.eventEmitter.emit('selectstart', { inputSource, frame: event.frame });
  }

  /**
   * 处理选择结束事件
   * @param event 选择事件
   */
  private handleSelectEnd(event: XRInputSourceEvent): void {
    const inputSource = event.inputSource;
    const inputSourceId = this.getInputSourceId(inputSource);
    this.setValue(`${inputSourceId}:select`, false);

    // 触发选择结束事件
    this.eventEmitter.emit('selectend', { inputSource, frame: event.frame });
  }

  /**
   * 处理选择事件
   * @param event 选择事件
   */
  private handleSelect(event: XRInputSourceEvent): void {
    const inputSource = event.inputSource;

    // 触发选择事件
    this.eventEmitter.emit('select', { inputSource, frame: event.frame });
  }

  /**
   * 处理挤压开始事件
   * @param event 挤压事件
   */
  private handleSqueezeStart(event: XRInputSourceEvent): void {
    const inputSource = event.inputSource;
    const inputSourceId = this.getInputSourceId(inputSource);
    this.setValue(`${inputSourceId}:squeeze`, true);

    // 触发挤压开始事件
    this.eventEmitter.emit('squeezestart', { inputSource, frame: event.frame });
  }

  /**
   * 处理挤压结束事件
   * @param event 挤压事件
   */
  private handleSqueezeEnd(event: XRInputSourceEvent): void {
    const inputSource = event.inputSource;
    const inputSourceId = this.getInputSourceId(inputSource);
    this.setValue(`${inputSourceId}:squeeze`, false);

    // 触发挤压结束事件
    this.eventEmitter.emit('squeezeend', { inputSource, frame: event.frame });
  }

  /**
   * 处理挤压事件
   * @param event 挤压事件
   */
  private handleSqueeze(event: XRInputSourceEvent): void {
    const inputSource = event.inputSource;

    // 触发挤压事件
    this.eventEmitter.emit('squeeze', { inputSource, frame: event.frame });
  }

  /**
   * 获取输入源ID
   * @param inputSource 输入源
   * @returns 输入源ID
   */
  private getInputSourceId(inputSource: XRInputSource): number {
    // 使用输入源的索引作为ID
    return Array.from(this.session!.inputSources).indexOf(inputSource);
  }

  /**
   * 获取XR会话
   * @returns XR会话
   */
  public getSession(): XRSession | null {
    return this.session;
  }

  /**
   * 获取输入源
   * @param id 输入源ID
   * @returns 输入源
   */
  public getInputSource(id: number): XRInputSource | undefined {
    return this.inputSources.get(id);
  }

  /**
   * 获取所有输入源
   * @returns 输入源列表
   */
  public getInputSources(): XRInputSource[] {
    return Array.from(this.inputSources.values());
  }

  /**
   * 检查输入源是否活跃
   * @param id 输入源ID
   * @returns 是否活跃
   */
  public isInputSourceActive(id: number): boolean {
    return !!this.getValue(`${id}:active`);
  }

  /**
   * 检查输入源是否选择中
   * @param id 输入源ID
   * @returns 是否选择中
   */
  public isInputSourceSelecting(id: number): boolean {
    return !!this.getValue(`${id}:select`);
  }

  /**
   * 检查输入源是否挤压中
   * @param id 输入源ID
   * @returns 是否挤压中
   */
  public isInputSourceSqueezing(id: number): boolean {
    return !!this.getValue(`${id}:squeeze`);
  }

  /**
   * 检查按钮是否按下
   * @param inputSourceId 输入源ID
   * @param buttonIndex 按钮索引
   * @returns 是否按下
   */
  public isButtonPressed(inputSourceId: number, buttonIndex: number): boolean {
    return !!this.getValue(`${inputSourceId}:button:${buttonIndex}`);
  }

  /**
   * 获取按钮值
   * @param inputSourceId 输入源ID
   * @param buttonIndex 按钮索引
   * @returns 按钮值
   */
  public getButtonValue(inputSourceId: number, buttonIndex: number): number {
    return this.getValue(`${inputSourceId}:button:${buttonIndex}:value`) || 0;
  }

  /**
   * 获取轴值
   * @param inputSourceId 输入源ID
   * @param axisIndex 轴索引
   * @returns 轴值
   */
  public getAxisValue(inputSourceId: number, axisIndex: number): number {
    return this.getValue(`${inputSourceId}:axis:${axisIndex}`) || 0;
  }
}
