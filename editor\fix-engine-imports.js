#!/usr/bin/env node

/**
 * 修复editor项目中依赖engine源代码的导入
 * 将它们修复为依赖src/libs库
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 递归查找所有 .tsx 和 .ts 文件
function findAllFiles(dir, extensions = ['.tsx', '.ts']) {
  let results = [];
  try {
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
      const filePath = path.join(dir, file);
      try {
        const stat = fs.statSync(filePath);
        
        if (stat && stat.isDirectory()) {
          // 跳过不需要的目录
          if (!['node_modules', '.git', 'dist', 'build', 'coverage', '__tests__'].includes(file)) {
            results = results.concat(findAllFiles(filePath, extensions));
          }
        } else {
          const ext = path.extname(file);
          if (extensions.includes(ext)) {
            results.push(filePath);
          }
        }
      } catch (err) {
        // 忽略无法访问的文件
      }
    });
  } catch (err) {
    // 忽略无法访问的目录
  }
  
  return results;
}

// 修复引擎导入的规则
const importFixRules = [
  // 1. 修复直接从engine源代码导入的情况
  {
    name: '修复engine源代码导入',
    pattern: /import\s*\{([^}]+)\}\s*from\s*['"]([^'"]*engine\/src\/[^'"]*)['"]/g,
    replacement: (match, imports, path) => {
      console.log(`  - 发现engine源代码导入: ${match}`);
      return `import { ${imports} } from '../libs/dl-engine'`;
    }
  },
  
  // 2. 修复相对路径的engine导入
  {
    name: '修复相对路径engine导入',
    pattern: /import\s*\{([^}]+)\}\s*from\s*['"]\.\.\/\.\.\/\.\.\/\.\.\/engine\/src\/[^'"]*['"]/g,
    replacement: (match, imports) => {
      console.log(`  - 发现相对路径engine导入: ${match}`);
      return `import { ${imports} } from '../../libs/dl-engine'`;
    }
  },

  // 2.1 修复其他相对路径的engine导入
  {
    name: '修复其他相对路径engine导入',
    pattern: /import\s*\{([^}]+)\}\s*from\s*['"][^'"]*\/engine\/src\/[^'"]*['"]/g,
    replacement: (match, imports) => {
      console.log(`  - 发现其他相对路径engine导入: ${match}`);
      return `import { ${imports} } from '../../libs/dl-engine'`;
    }
  },
  
  // 3. 修复dl-engine-core导入
  {
    name: '修复dl-engine-core导入',
    pattern: /import\s*\{([^}]+)\}\s*from\s*['"]([^'"]*dl-engine-core[^'"]*)['"]/g,
    replacement: (match, imports) => {
      console.log(`  - 发现dl-engine-core导入: ${match}`);
      return `import { ${imports} } from '../../libs/dl-engine'`;
    }
  },
  
  // 4. 修复直接的dl-engine导入（不带路径）
  {
    name: '修复直接dl-engine导入',
    pattern: /import\s*\{([^}]+)\}\s*from\s*['"]dl-engine['"]/g,
    replacement: (match, imports) => {
      console.log(`  - 发现直接dl-engine导入: ${match}`);
      return `import { ${imports} } from '../../libs/dl-engine'`;
    }
  },
  
  // 5. 修复.mjs导入为正确的路径
  {
    name: '修复.mjs导入',
    pattern: /import\s*\{([^}]+)\}\s*from\s*['"]([^'"]*dl-engine\.mjs)['"]/g,
    replacement: (match, imports, path) => {
      console.log(`  - 发现.mjs导入: ${match}`);
      // 根据文件位置确定正确的相对路径
      return `import { ${imports} } from '../../libs/dl-engine'`;
    }
  },
  
  // 6. 修复动态导入
  {
    name: '修复动态导入',
    pattern: /import\(['"]([^'"]*dl-engine\.mjs)['"]\)/g,
    replacement: (match, path) => {
      console.log(`  - 发现动态导入: ${match}`);
      return `import('../../libs/dl-engine')`;
    }
  }
];

// 修复单个文件
function fixFileImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const originalContent = content;
    
    // 应用所有修复规则
    importFixRules.forEach(rule => {
      const newContent = content.replace(rule.pattern, rule.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    // 如果有修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`修复文件 ${filePath} 时出错:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src 目录不存在');
    process.exit(1);
  }
  
  console.log('🔧 开始修复editor项目中的engine导入...');
  console.log('📁 扫描目录:', srcDir);
  
  const files = findAllFiles(srcDir);
  let fixedCount = 0;
  let totalFiles = files.length;
  
  console.log(`📄 找到 ${totalFiles} 个文件需要检查`);
  console.log('');
  
  files.forEach((file, index) => {
    const relativePath = path.relative(__dirname, file);
    
    if (fixFileImports(file)) {
      console.log(`✅ [${index + 1}/${totalFiles}] 修复了 ${relativePath}`);
      fixedCount++;
    } else {
      // 只显示前5个未修复的文件，避免输出过多
      if (index < 5) {
        console.log(`⏭️  [${index + 1}/${totalFiles}] 跳过 ${relativePath}`);
      }
    }
  });
  
  console.log('');
  console.log('🎉 修复完成!');
  console.log(`📊 总计: ${totalFiles} 个文件，修复了 ${fixedCount} 个文件`);
  
  if (fixedCount > 0) {
    console.log('');
    console.log('✨ 修复内容:');
    console.log('  - 将engine源代码导入改为从src/libs/dl-engine导入');
    console.log('  - 修复了相对路径的engine导入');
    console.log('  - 修复了dl-engine-core导入');
    console.log('  - 修复了.mjs文件导入');
    console.log('  - 修复了动态导入语句');
  }
}

// 运行主函数
main();
