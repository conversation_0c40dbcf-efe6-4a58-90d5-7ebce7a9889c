#!/usr/bin/env node

/**
 * DL（Digital Learning）引擎性能测试运行脚本
 * 用于在命令行中运行性能测试
 */

const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

// 测试类型
const TEST_TYPES = ['rendering', 'physics', 'full'];

// 输出目录
const OUTPUT_DIR = path.join(__dirname, 'reports');

/**
 * 打印帮助信息
 */
function printHelp() {
  console.log('DL（Digital Learning）引擎性能测试运行脚本');
  console.log('用法: node run-performance-tests.js [选项]');
  console.log('');
  console.log('选项:');
  console.log('  --type=TYPE    指定测试类型 (rendering, physics, full)');
  console.log('  --output=DIR   指定输出目录');
  console.log('  --help         显示帮助信息');
  console.log('');
  console.log('示例:');
  console.log('  node run-performance-tests.js --type=rendering');
  console.log('  node run-performance-tests.js --type=physics --output=./my-reports');
  console.log('  node run-performance-tests.js --type=full');
}

/**
 * 解析命令行参数
 * @returns {Object} 解析后的参数
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const result = {
    type: 'full',
    output: OUTPUT_DIR,
  };

  for (const arg of args) {
    if (arg === '--help') {
      printHelp();
      process.exit(0);
    } else if (arg.startsWith('--type=')) {
      const type = arg.substring('--type='.length);
      if (!TEST_TYPES.includes(type)) {
        console.error(`错误: 未知的测试类型 "${type}"`);
        console.error(`有效的测试类型: ${TEST_TYPES.join(', ')}`);
        process.exit(1);
      }
      result.type = type;
    } else if (arg.startsWith('--output=')) {
      result.output = arg.substring('--output='.length);
    } else {
      console.error(`错误: 未知的参数 "${arg}"`);
      printHelp();
      process.exit(1);
    }
  }

  return result;
}

/**
 * 确保目录存在
 * @param {string} dir 目录路径
 */
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

/**
 * 运行性能测试
 * @param {string} type 测试类型
 * @param {string} outputDir 输出目录
 */
function runPerformanceTests(type, outputDir) {
  console.log(`开始运行 ${type} 性能测试...`);
  
  // 确保输出目录存在
  ensureDir(outputDir);
  
  // 构建命令
  const indexPath = path.join(__dirname, 'index.ts');
  const command = `ts-node ${indexPath} ${type}`;
  
  try {
    // 运行测试
    execSync(command, {
      stdio: 'inherit',
      env: {
        ...process.env,
        OUTPUT_DIR: outputDir,
      },
    });
    
    console.log(`${type} 性能测试完成`);
    console.log(`测试报告已保存到 ${outputDir}`);
  } catch (error) {
    console.error(`运行 ${type} 性能测试失败:`);
    console.error(error.message);
    process.exit(1);
  }
}

/**
 * 主函数
 */
function main() {
  // 解析命令行参数
  const args = parseArgs();
  
  // 运行性能测试
  runPerformanceTests(args.type, args.output);
}

// 执行主函数
main();
