/**
 * 口型同步系统
 * 用于根据音频实时生成口型动画
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import type { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationComponent, VisemeType } from '../components/FacialAnimationComponent';
import { LipSyncComponent } from '../components/LipSyncComponent';

/**
 * 口型同步系统配置
 */
export interface LipSyncSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** FFT大小 */
  fftSize?: number;
  /** 音量阈值 */
  volumeThreshold?: number;
  /** 分析间隔（毫秒） */
  analysisInterval?: number;
  /** 是否使用Web Worker */
  useWorker?: boolean;
  /** 频率带数量 */
  numFrequencyBands?: number;
  /** 是否使用高级分析器 */
  useAdvancedAnalyzer?: boolean;
  /** 是否使用AI预测 */
  useAIPrediction?: boolean;
  /** 是否使用GPU */
  useGPU?: boolean;
}

/**
 * 频率带
 */
interface FrequencyBand {
  /** 开始频率 */
  start: number;
  /** 结束频率 */
  end: number;
  /** 能量 */
  energy: number;
}

/**
 * 口型同步系统
 */
export class LipSyncSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'LipSyncSystem';

  /** 配置 */
  private config: LipSyncSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 口型同步组件映射 */
  private components: Map<Entity, LipSyncComponent> = new Map();

  /** 音频上下文 */
  private audioContext: AudioContext | null = null;

  /** 音频分析器 */
  private audioAnalyser: AnalyserNode | null = null;

  /** 音频源 */
  private audioSource: MediaStreamAudioSourceNode | null = null;

  /** 频谱数据 */
  private spectrum: Float32Array | null = null;

  /** 频率带 */
  private frequencyBands: FrequencyBand[] = [];

  /** 分析计时器 */
  private analysisTimer: number = 0;

  /** 是否支持GPU */
  private supportsGPU: boolean = false;

  /** GPU计算着色器 */
  private gpuComputeShader: any = null;

  /** 高级分析器 */
  private advancedAnalyzer: any = null;

  /** AI预测器 */
  private aiPredictor: any = null;

  /** Web Worker */
  private worker: Worker | null = null;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: LipSyncSystemConfig = {}) {
    super(280); // 设置优先级

    this.config = {
      debug: false,
      fftSize: 1024,
      volumeThreshold: 0.01,
      analysisInterval: 100, // 默认100ms分析一次
      useWorker: false,
      numFrequencyBands: 8,
      useAdvancedAnalyzer: false,
      useAIPrediction: false,
      useGPU: false,
      ...config
    };

    if (this.config.debug) {
      console.log('口型同步系统初始化');
    }

    // 初始化音频上下文
    this.initAudioContext();

    // 初始化频率带
    this.initFrequencyBands();

    // 检查GPU支持
    this.checkGPUSupport();

    // 初始化高级分析器
    if (this.config.useAdvancedAnalyzer) {
      this.initAdvancedAnalyzer();
    }

    // 初始化AI预测器
    if (this.config.useAIPrediction) {
      this.initAIPredictor();
    }

    // 初始化Web Worker
    if (this.config.useWorker) {
      this.initWorker();
    }
  }

  /**
   * 初始化音频上下文
   */
  private initAudioContext(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.audioAnalyser = this.audioContext.createAnalyser();
      this.audioAnalyser.fftSize = this.config.fftSize!;
      this.audioAnalyser.smoothingTimeConstant = 0.8;

      // 获取麦克风
      navigator.mediaDevices.getUserMedia({ audio: true, video: false })
        .then((stream) => {
          if (this.audioContext) {
            this.audioSource = this.audioContext.createMediaStreamSource(stream);
            this.audioSource.connect(this.audioAnalyser);

            if (this.config.debug) {
              console.log('音频上下文初始化成功');
            }
          }
        })
        .catch((error) => {
          console.error('获取麦克风失败:', error);
        });
    } catch (error) {
      console.error('初始化音频上下文失败:', error);
    }
  }

  /**
   * 初始化频率带
   */
  private initFrequencyBands(): void {
    const numBands = this.config.numFrequencyBands!;
    const minFreq = 80; // 最小频率
    const maxFreq = 8000; // 最大频率

    // 使用对数分布创建频率带
    for (let i = 0; i < numBands; i++) {
      const startFreq = minFreq * Math.pow(maxFreq / minFreq, i / numBands);
      const endFreq = minFreq * Math.pow(maxFreq / minFreq, (i + 1) / numBands);

      this.frequencyBands.push({
        start: startFreq,
        end: endFreq,
        energy: 0
      });
    }

    if (this.config.debug) {
      console.log('频率带初始化完成:', this.frequencyBands);
    }
  }

  /**
   * 检查GPU支持
   */
  private checkGPUSupport(): void {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');

      if (gl) {
        this.supportsGPU = true;

        if (this.config.useGPU) {
          // 初始化GPU计算着色器
          // 这里是GPU计算的占位代码，实际实现需要根据WebGL环境
          // TODO: 实现GPU计算着色器
        }
      } else {
        this.supportsGPU = false;

        if (this.config.useGPU) {
          console.warn('不支持GPU加速，将使用CPU处理');
        }
      }
    } catch (error) {
      this.supportsGPU = false;
      console.error('检查GPU支持失败:', error);
    }
  }

  /**
   * 初始化高级分析器
   */
  private initAdvancedAnalyzer(): void {
    try {
      // 这里是高级分析器的占位代码，实际实现需要根据具体需求
      // TODO: 实现高级分析器
      this.advancedAnalyzer = {
        analyzeAudio: (spectrum: Float32Array) => {
          // 分析音频并返回口型
          return VisemeType.AA;
        }
      };

      if (this.config.debug) {
        console.log('高级分析器初始化成功');
      }
    } catch (error) {
      console.error('初始化高级分析器失败:', error);
    }
  }

  /**
   * 初始化AI预测器
   */
  private initAIPredictor(): void {
    try {
      // 这里是AI预测器的占位代码，实际实现需要根据具体需求
      // TODO: 实现AI预测器
      this.aiPredictor = {
        predict: (spectrum: Float32Array) => {
          // 预测口型并返回结果
          return {
            viseme: VisemeType.AA,
            confidence: 0.8
          };
        }
      };

      if (this.config.debug) {
        console.log('AI预测器初始化成功');
      }
    } catch (error) {
      console.error('初始化AI预测器失败:', error);
    }
  }

  /**
   * 初始化Web Worker
   */
  private initWorker(): void {
    try {
      // 创建Web Worker
      // 这里是Web Worker的占位代码，实际实现需要根据具体需求
      // TODO: 实现Web Worker

      if (this.config.debug) {
        console.log('Web Worker初始化成功');
      }
    } catch (error) {
      console.error('初始化Web Worker失败:', error);
    }
  }

  /**
   * 创建口型同步组件
   * @param entity 实体
   * @returns 口型同步组件
   */
  public createLipSync(entity: Entity): LipSyncComponent {
    // 检查实体是否已有口型同步组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建口型同步组件
    const component = new LipSyncComponent(entity);

    // 添加组件到实体
    entity.addComponent(component);

    // 添加组件到映射
    this.components.set(entity, component);

    if (this.config.debug) {
      console.log('创建口型同步组件', entity);
    }

    return component;
  }

  /**
   * 获取口型同步组件
   * @param entity 实体
   * @returns 口型同步组件
   */
  public getLipSync(entity: Entity): LipSyncComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 移除口型同步组件
   * @param entity 实体
   */
  public removeLipSync(entity: Entity): void {
    const component = this.components.get(entity);
    if (component) {
      entity.removeComponent(component);
      this.components.delete(entity);

      if (this.config.debug) {
        console.log('移除口型同步组件', entity);
      }
    }
  }

  /**
   * 设置所有实体的口型
   * @param viseme 口型类型
   * @param weight 权重
   */
  private setVisemeForAllEntities(viseme: VisemeType, weight: number = 1.0): void {
    for (const [entity, component] of this.components.entries()) {
      // 获取面部动画组件
      const facialComponent = entity.getComponent(FacialAnimationComponent.TYPE) as FacialAnimationComponent;
      if (facialComponent) {
        // 设置口型
        facialComponent.setViseme(viseme, weight, 0.1);
      }
    }
  }

  /**
   * 计算RMS（均方根）
   * @param spectrum 频谱数据
   * @returns RMS值
   */
  private calculateRMS(spectrum: Float32Array): number {
    let sum = 0;
    for (let i = 0; i < spectrum.length; i++) {
      // 将dB转换为线性值
      const linear = Math.pow(10, spectrum[i] / 20);
      sum += linear * linear;
    }
    return Math.sqrt(sum / spectrum.length);
  }

  /**
   * 计算频率带能量
   */
  private calculateFrequencyBands(): void {
    if (!this.audioAnalyser || !this.spectrum) return;

    const sampleRate = this.audioContext!.sampleRate;
    const binCount = this.audioAnalyser.frequencyBinCount;
    const binWidth = sampleRate / (2 * binCount);

    // 重置能量
    for (const band of this.frequencyBands) {
      band.energy = 0;
    }

    // 计算每个频率带的能量
    for (let i = 0; i < binCount; i++) {
      const frequency = i * binWidth;
      const amplitude = this.spectrum[i];

      // 将dB转换为线性值
      const linear = Math.pow(10, amplitude / 20);

      // 将能量添加到相应的频率带
      for (const band of this.frequencyBands) {
        if (frequency >= band.start && frequency <= band.end) {
          band.energy += linear * linear;
        }
      }
    }

    // 归一化能量
    let maxEnergy = 0;
    for (const band of this.frequencyBands) {
      maxEnergy = Math.max(maxEnergy, band.energy);
    }

    if (maxEnergy > 0) {
      for (const band of this.frequencyBands) {
        band.energy /= maxEnergy;
      }
    }
  }

  /**
   * 分析频率带并确定口型
   * @returns 口型类型
   */
  private analyzeFrequencyBands(): VisemeType {
    if (!this.frequencyBands.length) return VisemeType.SILENT;

    // 查找能量最大的频率带
    let maxEnergyBand = this.frequencyBands[0];
    for (let i = 1; i < this.frequencyBands.length; i++) {
      if (this.frequencyBands[i].energy > maxEnergyBand.energy) {
        maxEnergyBand = this.frequencyBands[i];
      }
    }

    // 根据频率带确定口型
    const centerFreq = (maxEnergyBand.start + maxEnergyBand.end) / 2;

    if (centerFreq < 200) {
      return VisemeType.MM;
    } else if (centerFreq < 500) {
      return VisemeType.OU;
    } else if (centerFreq < 1000) {
      return VisemeType.AA;
    } else if (centerFreq < 2000) {
      return VisemeType.EE;
    } else if (centerFreq < 4000) {
      return VisemeType.SS;
    } else {
      return VisemeType.FF;
    }
  }

  /**
   * 处理音频
   */
  private processAudio(): void {
    if (!this.audioAnalyser) return;

    // 使用Web Worker
    if (this.config.useWorker && this.worker) {
      // 获取频率数据
      this.spectrum = new Float32Array(this.audioAnalyser.frequencyBinCount);
      this.audioAnalyser.getFloatFrequencyData(this.spectrum);

      // 发送数据到Worker
      this.worker.postMessage({
        spectrum: this.spectrum,
        volumeThreshold: this.config.volumeThreshold
      });
    }
    // 使用GPU加速
    else if (this.config.useGPU && this.supportsGPU && this.gpuComputeShader) {
      this.processAudioWithGPU();
    }
    // 使用CPU处理
    else {
      this.processAudioWithCPU();
    }
  }

  /**
   * 使用CPU处理音频数据
   */
  private processAudioWithCPU(): void {
    if (!this.audioAnalyser) return;

    // 获取频率数据
    this.spectrum = new Float32Array(this.audioAnalyser.frequencyBinCount);
    this.audioAnalyser.getFloatFrequencyData(this.spectrum);

    // 使用AI预测
    if (this.config.useAIPrediction && this.aiPredictor) {
      try {
        const prediction = this.aiPredictor.predict(this.spectrum);

        // 如果置信度足够高，使用AI预测结果
        if (prediction.confidence > 0.7) {
          this.setVisemeForAllEntities(prediction.viseme, prediction.confidence);
          return;
        }
      } catch (error) {
        if (this.config.debug) {
          console.warn('AI预测失败，回退到传统方法:', error);
        }
      }
    }

    // 使用高级分析器
    if (this.config.useAdvancedAnalyzer && this.advancedAnalyzer) {
      try {
        const viseme = this.advancedAnalyzer.analyzeAudio(this.spectrum);
        this.setVisemeForAllEntities(viseme);
        return;
      } catch (error) {
        if (this.config.debug) {
          console.warn('高级分析器失败，回退到传统方法:', error);
        }
      }
    }

    // 计算RMS
    const rms = this.calculateRMS(this.spectrum);

    // 如果音量太小，则设置为静默
    if (rms < this.config.volumeThreshold!) {
      this.setVisemeForAllEntities(VisemeType.SILENT);
      return;
    }

    // 计算频率带能量
    this.calculateFrequencyBands();

    // 分析频谱并确定口型
    const viseme = this.analyzeFrequencyBands();
    this.setVisemeForAllEntities(viseme);
  }

  /**
   * 使用GPU处理音频数据
   */
  private processAudioWithGPU(): void {
    // 这里是GPU处理的占位代码，实际实现需要根据WebGL环境
    // TODO: 实现GPU处理
    this.processAudioWithCPU();
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新分析计时器
    this.analysisTimer += deltaTime * 1000;

    // 如果达到分析间隔，处理音频
    if (this.analysisTimer >= this.config.analysisInterval!) {
      this.processAudio();
      this.analysisTimer = 0;
    }

    // 更新所有口型同步组件
    for (const [entity, component] of this.components.entries()) {
      component.update(deltaTime);
    }
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 关闭音频上下文
    if (this.audioSource) {
      this.audioSource.disconnect();
      this.audioSource = null;
    }

    if (this.audioAnalyser) {
      this.audioAnalyser = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    // 关闭Web Worker
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    // 清除组件映射
    this.components.clear();

    if (this.config.debug) {
      console.log('口型同步系统销毁');
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }
}
