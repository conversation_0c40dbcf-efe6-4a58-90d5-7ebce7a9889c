/**
 * 渲染器类
 * 负责渲染场景
 */
import * as THREE from 'three';
import { Scene } from '../scene/Scene';
import type { Camera } from './Camera';
import { EventEmitter } from '../utils/EventEmitter';
export interface RendererOptions {
    /** 画布元素或ID */
    canvas?: HTMLCanvasElement | string;
    /** 宽度 */
    width?: number;
    /** 高度 */
    height?: number;
    /** 是否抗锯齿 */
    antialias?: boolean;
    /** 是否使用Alpha通道 */
    alpha?: boolean;
    /** 是否使用深度测试 */
    depth?: boolean;
    /** 是否使用模板测试 */
    stencil?: boolean;
    /** 是否使用对数深度缓冲 */
    logarithmicDepthBuffer?: boolean;
    /** 是否自动清除 */
    autoClear?: boolean;
    /** 是否自动调整大小 */
    autoResize?: boolean;
    /** 像素比例 */
    pixelRatio?: number;
    /** 阴影类型 */
    shadowMapType?: THREE.ShadowMapType;
    /** 是否启用阴影 */
    shadows?: boolean;
    /** 色调映射 */
    toneMapping?: THREE.ToneMapping;
    /** 色调映射曝光 */
    toneMappingExposure?: number;
    /** 输出颜色空间 */
    outputColorSpace?: THREE.ColorSpace;
}
export declare class Renderer extends EventEmitter {
    /** Three.js渲染器 */
    private renderer;
    /** 画布元素 */
    private canvas;
    /** 是否自动调整大小 */
    private autoResize;
    /** 调整大小监听器 */
    private resizeListener;
    /**
     * 创建渲染器实例
     * @param options 渲染器选项
     */
    constructor(options?: RendererOptions);
    /**
     * 处理窗口调整大小事件
     */
    private handleResize;
    /**
     * 设置渲染器大小
     * @param width 宽度
     * @param height 高度
     * @param updateStyle 是否更新样式
     */
    setSize(width: number, height: number, updateStyle?: boolean): void;
    /**
     * 获取渲染器大小
     * @returns 渲染器大小
     */
    getSize(): {
        width: number;
        height: number;
    };
    /**
     * 设置像素比例
     * @param pixelRatio 像素比例
     */
    setPixelRatio(pixelRatio: number): void;
    /**
     * 获取像素比例
     * @returns 像素比例
     */
    getPixelRatio(): number;
    /**
     * 设置清除颜色
     * @param color 颜色
     * @param alpha 透明度
     */
    setClearColor(color: THREE.ColorRepresentation, alpha?: number): void;
    /**
     * 获取清除颜色
     * @returns 清除颜色
     */
    getClearColor(): THREE.Color;
    /**
     * 设置自动清除
     * @param autoClear 是否自动清除
     */
    setAutoClear(autoClear: boolean): void;
    /**
     * 是否自动清除
     * @returns 是否自动清除
     */
    isAutoClear(): boolean;
    /**
     * 清除渲染器
     */
    clear(): void;
    /**
     * 渲染场景
     * @param scene 场景
     * @param camera 相机
     */
    render(scene: Scene, camera: Camera): void;
    /**
     * 获取Three.js渲染器
     * @returns Three.js渲染器
     */
    getThreeRenderer(): THREE.WebGLRenderer;
    /**
     * 获取画布元素
     * @returns 画布元素
     */
    getCanvas(): HTMLCanvasElement;
    /**
     * 设置自动调整大小
     * @param autoResize 是否自动调整大小
     */
    setAutoResize(autoResize: boolean): void;
    /**
     * 是否自动调整大小
     * @returns 是否自动调整大小
     */
    isAutoResize(): boolean;
    /**
     * 销毁渲染器
     */
    dispose(): void;
}
