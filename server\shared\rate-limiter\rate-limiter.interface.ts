/**
 * 限流器接口定义
 */

/**
 * 限流算法类型
 */
export enum RateLimiterType {
  /** 固定窗口 */
  FIXED_WINDOW = 'fixed_window',
  /** 滑动窗口 */
  SLIDING_WINDOW = 'sliding_window',
  /** 令牌桶 */
  TOKEN_BUCKET = 'token_bucket',
  /** 漏桶 */
  LEAKY_BUCKET = 'leaky_bucket',
}

/**
 * 限流器配置
 */
export interface RateLimiterOptions {
  /**
   * 限流器名称
   */
  name: string;

  /**
   * 限流算法类型
   */
  type?: RateLimiterType;

  /**
   * 时间窗口（毫秒）
   */
  windowMs?: number;

  /**
   * 最大请求数
   */
  maxRequests?: number;

  /**
   * 是否启用分布式限流
   */
  distributed?: boolean;

  /**
   * 分布式存储键前缀
   */
  keyPrefix?: string;

  /**
   * 是否启用监控
   */
  enableMonitoring?: boolean;

  /**
   * 监控回调函数
   */
  onLimitReached?: (key: string, limit: number) => void;
}

/**
 * 限流器统计信息
 */
export interface RateLimiterStats {
  /**
   * 限流器名称
   */
  name: string;

  /**
   * 限流算法类型
   */
  type: RateLimiterType;

  /**
   * 总请求数
   */
  totalRequests: number;

  /**
   * 限流请求数
   */
  limitedRequests: number;

  /**
   * 通过请求数
   */
  passedRequests: number;

  /**
   * 当前请求速率（请求/秒）
   */
  currentRate: number;

  /**
   * 上次重置时间
   */
  lastResetTime: Date;
}

/**
 * 限流器接口
 */
export interface IRateLimiter {
  /**
   * 消费令牌
   * @param key 键
   * @param tokens 令牌数
   */
  consume(key: string, tokens?: number): Promise<boolean>;

  /**
   * 获取剩余令牌数
   * @param key 键
   */
  getRemainingTokens(key: string): Promise<number>;

  /**
   * 获取限流器统计信息
   */
  getStats(): RateLimiterStats;

  /**
   * 重置限流器
   */
  reset(): Promise<void>;
}
