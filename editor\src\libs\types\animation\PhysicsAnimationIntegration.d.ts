import type { Entity } from '../core/Entity';
import { PhysicsSystem } from '../physics/PhysicsSystem';
import { AnimationBlender } from './AnimationBlender';
/**
 * 物理动画集成事件类型
 */
export declare enum PhysicsAnimationEventType {
    /** 物理状态改变 */
    PHYSICS_STATE_CHANGED = "physicsStateChanged",
    /** 碰撞开始 */
    COLLISION_ENTER = "collisionEnter",
    /** 碰撞结束 */
    COLLISION_EXIT = "collisionExit",
    /** 接触地面 */
    GROUNDED = "grounded",
    /** 离开地面 */
    AIRBORNE = "airborne"
}
/**
 * 物理动画集成配置
 */
export interface PhysicsAnimationIntegrationConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否自动更新动画参数 */
    autoUpdateParameters?: boolean;
    /** 是否使用角色控制器 */
    useCharacterController?: boolean;
    /** 是否使用物理驱动骨骼 */
    usePhysicsDrivenBones?: boolean;
    /** 物理驱动骨骼列表 */
    physicsDrivenBones?: string[];
    /** 物理骨骼阻尼 */
    physicsBonesDamping?: number;
    /** 物理骨骼弹性 */
    physicsBonesRestitution?: number;
    /** 物理骨骼质量 */
    physicsBonesMass?: number;
}
/**
 * 物理动画集成
 * 用于将物理系统与动画系统集成
 */
export declare class PhysicsAnimationIntegration {
    /** 实体 */
    private entity;
    /** 物理系统 */
    private physicsSystem;
    /** 动画混合器 */
    private blender;
    /** 动画控制器 */
    private animator;
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否在地面上 */
    private isGrounded;
    /** 上一次速度 */
    private lastVelocity;
    /** 物理驱动骨骼映射 */
    private physicsBones;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /**
     * 创建物理动画集成
     * @param entity 实体
     * @param physicsSystem 物理系统
     * @param blender 动画混合器
     * @param config 配置
     */
    constructor(entity: Entity, physicsSystem: PhysicsSystem, blender: AnimationBlender, config?: PhysicsAnimationIntegrationConfig);
    /**
     * 初始化
     */
    initialize(): void;
    /**
     * 销毁
     */
    destroy(): void;
    /**
     * 更新
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: PhysicsAnimationEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: PhysicsAnimationEventType, listener: (data: any) => void): void;
    /**
     * 初始化物理驱动骨骼
     */
    private initializePhysicsDrivenBones;
    /**
     * 更新物理驱动骨骼
     * @param deltaTime 帧间隔时间（秒）
     */
    private updatePhysicsDrivenBones;
    /**
     * 更新动画参数
     * @param velocity 速度
     */
    private updateAnimationParameters;
    /**
     * 处理碰撞开始事件
     * @param event 碰撞事件
     */
    private handleCollisionStart;
    /**
     * 处理碰撞结束事件
     * @param event 碰撞事件
     */
    private handleCollisionEnd;
}
