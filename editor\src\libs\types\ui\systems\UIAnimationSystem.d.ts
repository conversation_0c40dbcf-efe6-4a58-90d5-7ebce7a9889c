/**
 * UIAnimationSystem.ts
 *
 * UI动画系统，管理UI元素的动画效果
 */
import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { Vector2, Vector3, Color } from 'three';
import { UIComponent } from '../components/UIComponent';
import { UIAnimationComponent, UIAnimation } from '../components/UIAnimationComponent';
import { UISystem } from './UISystem';
/**
 * UI动画系统配置
 */
export interface UIAnimationSystemConfig {
    debug?: boolean;
    defaultDuration?: number;
    defaultDelay?: number;
    defaultEasing?: (t: number) => number;
}
/**
 * UI动画系统
 * 管理UI元素的动画效果
 */
export declare class UIAnimationSystem extends System {
    private uiSystem;
    private config;
    private animationComponents;
    /**
     * 构造函数
     * @param world 世界实例
     * @param uiSystem UI系统实例
     * @param config UI动画系统配置
     */
    constructor(world: World, uiSystem: UISystem, config?: UIAnimationSystemConfig);
    /**
     * 注册动画组件
     * @param entity 实体
     * @param component 动画组件
     */
    registerAnimationComponent(entity: Entity, component: UIAnimationComponent): void;
    /**
     * 注销动画组件
     * @param entity 实体
     */
    unregisterAnimationComponent(entity: Entity): void;
    /**
     * 获取或创建动画组件
     * @param entity 实体
     * @returns 动画组件
     */
    getOrCreateAnimationComponent(entity: Entity): UIAnimationComponent;
    /**
     * 创建淡入动画
     * @param entity 实体
     * @param uiComponent UI组件
     * @param duration 持续时间（毫秒）
     * @param delay 延迟（毫秒）
     * @param easing 缓动函数
     * @returns 创建的动画
     */
    createFadeInAnimation(entity: Entity, uiComponent: UIComponent, duration?: number, delay?: number, easing?: (t: number) => number): UIAnimation;
    /**
     * 创建淡出动画
     * @param entity 实体
     * @param uiComponent UI组件
     * @param duration 持续时间（毫秒）
     * @param delay 延迟（毫秒）
     * @param easing 缓动函数
     * @returns 创建的动画
     */
    createFadeOutAnimation(entity: Entity, uiComponent: UIComponent, duration?: number, delay?: number, easing?: (t: number) => number): UIAnimation;
    /**
     * 创建移动动画
     * @param entity 实体
     * @param uiComponent UI组件
     * @param to 目标位置
     * @param duration 持续时间（毫秒）
     * @param delay 延迟（毫秒）
     * @param easing 缓动函数
     * @returns 创建的动画
     */
    createMoveAnimation(entity: Entity, uiComponent: UIComponent, to: Vector2 | Vector3, duration?: number, delay?: number, easing?: (t: number) => number): UIAnimation;
    /**
     * 创建缩放动画
     * @param entity 实体
     * @param uiComponent UI组件
     * @param to 目标尺寸
     * @param duration 持续时间（毫秒）
     * @param delay 延迟（毫秒）
     * @param easing 缓动函数
     * @returns 创建的动画
     */
    createScaleAnimation(entity: Entity, uiComponent: UIComponent, to: Vector2, duration?: number, delay?: number, easing?: (t: number) => number): UIAnimation;
    /**
     * 创建颜色动画
     * @param entity 实体
     * @param uiComponent UI组件
     * @param property 目标属性
     * @param to 目标颜色
     * @param duration 持续时间（毫秒）
     * @param delay 延迟（毫秒）
     * @param easing 缓动函数
     * @returns 创建的动画
     */
    createColorAnimation(entity: Entity, uiComponent: UIComponent, property: 'backgroundColor' | 'borderColor' | 'fontColor', to: string | Color, duration?: number, delay?: number, easing?: (t: number) => number): UIAnimation;
    /**
     * 创建序列动画
     * @param _entity 实体 - 未使用，但保留以保持API一致性
     * @param animations 动画列表
     * @returns 动画列表
     */
    createSequenceAnimation(_entity: Entity, animations: UIAnimation[]): UIAnimation[];
    /**
     * 创建并行动画
     * @param _entity 实体 - 未使用，但保留以保持API一致性
     * @param animations 动画列表
     * @returns 动画列表
     */
    createParallelAnimation(_entity: Entity, animations: UIAnimation[]): UIAnimation[];
    /**
     * 停止所有动画
     * @param entity 实体
     */
    stopAllAnimations(entity: Entity): void;
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
