/**
 * GLTF加载器
 * 用于加载和处理GLTF模型
 */
import * as THREE from 'three';
import { Entity } from '../core/Entity';
import { Transform } from '../scene/Transform';
import { EventEmitter } from '../utils/EventEmitter';
import { GLTFAnimationComponent } from './components/GLTFAnimationComponent';
import { GLTFModelComponent, GLTF } from './components/GLTFModelComponent';

// 简化的加载器类型定义
declare class ThreeGLTFLoader {
  load(
    url: string,
    onLoad: (gltf: GLTF) => void,
    onProgress?: (progress: any) => void,
    onError?: (error: any) => void
  ): void;
  setPath(path: string): void;
  setResourcePath(path: string): void;
  setCrossOrigin(crossOrigin: string): void;
  setDRACOLoader(loader: any): void;
  setKTX2Loader(loader: any): void;
}

declare class DRACOLoader {
  setDecoderPath(path: string): void;
  dispose(): void;
}

declare class KTX2Loader {
  setTranscoderPath(path: string): void;
  dispose(): void;
}
// import { GLTFNodeComponent } from './components/GLTFNodeComponent';

/**
 * GLTF加载器选项
 */
export interface GLTFLoaderOptions {
  /** 是否使用Draco压缩 */
  useDraco?: boolean;
  /** Draco解码器路径 */
  dracoDecoderPath?: string;
  /** 是否使用KTX2纹理 */
  useKTX2?: boolean;
  /** KTX2解码器路径 */
  ktx2DecoderPath?: string;
  /** 是否加载动画 */
  loadAnimations?: boolean;
  /** 是否加载相机 */
  loadCameras?: boolean;
  /** 是否加载灯光 */
  loadLights?: boolean;
  /** 是否优化几何体 */
  optimizeGeometry?: boolean;
}

/**
 * GLTF加载器
 */
export class GLTFLoader extends EventEmitter {
  /** Three.js GLTF加载器 */
  private loader: ThreeGLTFLoader;

  /** Draco加载器 */
  private dracoLoader: DRACOLoader | null = null;

  /** KTX2加载器 */
  private ktx2Loader: KTX2Loader | null = null;

  /** 加载选项 */
  private options: GLTFLoaderOptions;

  /**
   * 创建GLTF加载器
   * @param options 加载选项
   */
  constructor(options: GLTFLoaderOptions = {}) {
    super();

    this.options = {
      useDraco: options.useDraco !== undefined ? options.useDraco : true,
      dracoDecoderPath: options.dracoDecoderPath || 'https://www.gstatic.com/draco/versioned/decoders/1.5.6/',
      useKTX2: options.useKTX2 !== undefined ? options.useKTX2 : true,
      ktx2DecoderPath: options.ktx2DecoderPath || 'https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/',
      loadAnimations: options.loadAnimations !== undefined ? options.loadAnimations : true,
      loadCameras: options.loadCameras !== undefined ? options.loadCameras : true,
      loadLights: options.loadLights !== undefined ? options.loadLights : true,
      optimizeGeometry: options.optimizeGeometry !== undefined ? options.optimizeGeometry : true,
    };

    // 创建Three.js GLTF加载器
    this.loader = new (THREE as any).GLTFLoader();

    // 设置Draco加载器
    if (this.options.useDraco) {
      this.dracoLoader = new (THREE as any).DRACOLoader();
      this.dracoLoader.setDecoderPath(this.options.dracoDecoderPath);
      this.loader.setDRACOLoader(this.dracoLoader);
    }

    // 设置KTX2加载器
    if (this.options.useKTX2) {
      this.ktx2Loader = new (THREE as any).KTX2Loader();
      this.ktx2Loader.setTranscoderPath(this.options.ktx2DecoderPath);
      this.loader.setKTX2Loader(this.ktx2Loader);
    }
  }

  /**
   * 加载GLTF模型
   * @param url 模型URL
   * @returns Promise，解析为加载的GLTF模型
   */
  public async load(url: string): Promise<GLTF> {
    return new Promise((resolve, reject) => {
      this.loader.load(
        url,
        (gltf) => {
          // 优化几何体
          if (this.options.optimizeGeometry) {
            this.optimizeGeometry(gltf);
          }

          resolve(gltf);
        },
        (progress) => {
          this.emit('progress', {
            url,
            loaded: progress.loaded,
            total: progress.total,
            progress: progress.total > 0 ? progress.loaded / progress.total : 0
          });
        },
        (error) => {
          reject(new Error(`加载GLTF模型失败: ${error.message}`));
        }
      );
    });
  }

  /**
   * 优化几何体
   * @param gltf GLTF模型
   */
  private optimizeGeometry(gltf: GLTF): void {
    // 遍历所有网格
    gltf.scene.traverse((node: THREE.Object3D) => {
      if (node instanceof THREE.Mesh) {
        const geometry = node.geometry;

        // 确保几何体有法线
        if (!geometry.attributes.normal) {
          geometry.computeVertexNormals();
        }

        // 确保几何体有切线
        if (!geometry.attributes.tangent && node.material instanceof THREE.MeshStandardMaterial) {
          geometry.computeTangents();
        }
      }
    });
  }

  /**
   * 创建实体
   * @param gltf GLTF模型
   * @param entity 父实体
   * @returns 创建的根实体
   */
  public createEntity(gltf: GLTF, entity: Entity): Entity {
    // 添加GLTF模型组件
    const modelComponent = new GLTFModelComponent(gltf);
    entity.addComponent(modelComponent);

    // 添加变换组件
    if (!entity.getTransform()) {
      entity.addComponent(new Transform());
    }

    // 处理场景节点
    this.processNode(gltf.scene, entity, gltf);

    // 处理动画
    if (this.options.loadAnimations && gltf.animations && gltf.animations.length > 0) {
      const animationComponent = new GLTFAnimationComponent(gltf.animations);
      entity.addComponent(animationComponent);
    }

    return entity;
  }

  /**
   * 处理节点
   * @param node Three.js对象
   * @param parentEntity 父实体
   * @param gltf GLTF模型
   */
  private processNode(node: THREE.Object3D, parentEntity: Entity, gltf: GLTF): void {
    // 获取父实体的Transform组件
    const parentTransform = parentEntity.getTransform();
    if (parentTransform) {
      // 将Three.js对象添加到父Transform中
      parentTransform.getObject3D().add(node);
    }

    // 处理子节点
    for (const child of node.children) {
      // 创建子实体
      const childEntity = new Entity(child.name || 'GLTFNode');

      // 添加变换组件
      const transform = new Transform();
      // 从Three.js对象设置变换
      transform.getObject3D().position.copy(child.position);
      transform.getObject3D().rotation.copy(child.rotation);
      transform.getObject3D().scale.copy(child.scale);
      childEntity.addComponent(transform);

      // 处理子节点
      this.processNode(child, childEntity, gltf);

      // 添加到父实体
      parentEntity.addChild(childEntity);
    }
  }

  /**
   * 设置基础路径
   * @param path 基础路径
   */
  public setPath(path: string): void {
    this.loader.setPath(path);
  }

  /**
   * 设置资源路径
   * @param path 资源路径
   */
  public setResourcePath(path: string): void {
    this.loader.setResourcePath(path);
  }

  /**
   * 设置跨域
   * @param crossOrigin 跨域设置
   */
  public setCrossOrigin(crossOrigin: string): void {
    this.loader.setCrossOrigin(crossOrigin);
  }

  /**
   * 销毁加载器
   */
  public dispose(): void {
    if (this.dracoLoader) {
      (this.dracoLoader as any).dispose();
    }

    if (this.ktx2Loader) {
      (this.ktx2Loader as any).dispose();
    }
  }
}
