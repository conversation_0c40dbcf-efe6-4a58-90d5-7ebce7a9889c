/**
 * 测试运行器
 * 用于运行所有渲染测试
 */
import { runBasicRenderTests } from './BasicRenderTests';
import { runPostProcessingTests } from './PostProcessingTests';
import { runShadowTests } from './ShadowTests';
import { runLODTests } from './LODTests';
import { runInstancedRenderingTests } from './InstancedRenderingTests';

/**
 * 运行所有测试
 */
export async function runAllTests(): Promise<void> {
  console.log('开始运行所有测试...');
  
  try {
    // 运行基础渲染测试
    console.log('运行基础渲染测试...');
    await runBasicRenderTests();
    
    // 运行后处理测试
    console.log('运行后处理测试...');
    await runPostProcessingTests();
    
    // 运行阴影测试
    console.log('运行阴影测试...');
    await runShadowTests();
    
    // 运行LOD测试
    console.log('运行LOD测试...');
    await runLODTests();
    
    // 运行实例化渲染测试
    console.log('运行实例化渲染测试...');
    await runInstancedRenderingTests();
    
    console.log('所有测试完成！');
  } catch (error) {
    console.error('测试运行出错:', error);
  }
}

/**
 * 运行指定测试
 * @param testName 测试名称
 */
export async function runTest(testName: string): Promise<void> {
  console.log(`开始运行测试: ${testName}...`);
  
  try {
    switch (testName) {
      case 'basic':
        await runBasicRenderTests();
        break;
      case 'postprocessing':
        await runPostProcessingTests();
        break;
      case 'shadow':
        await runShadowTests();
        break;
      case 'lod':
        await runLODTests();
        break;
      case 'instanced':
        await runInstancedRenderingTests();
        break;
      default:
        console.error(`未知的测试: ${testName}`);
        break;
    }
    
    console.log(`测试 ${testName} 完成！`);
  } catch (error) {
    console.error(`测试 ${testName} 运行出错:`, error);
  }
}

// 如果直接运行此文件，则运行所有测试
if (require.main === module) {
  const testName = process.argv[2];
  
  if (testName) {
    runTest(testName);
  } else {
    runAllTests();
  }
}
