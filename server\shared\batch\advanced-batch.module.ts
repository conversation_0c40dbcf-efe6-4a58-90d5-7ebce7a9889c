/**
 * 高级批处理模块
 */
import { DynamicModule, Module, Provider } from '@nestjs/common';
import { EventEmitterModule, EventEmitter2 } from '@nestjs/event-emitter';
import { AdvancedBatchProcessorService, AdvancedBatchProcessorOptions } from './advanced-batch-processor.service';

/**
 * 高级批处理模块配置
 */
export interface AdvancedBatchModuleOptions<T, R> {
  /**
   * 是否全局注册
   */
  isGlobal?: boolean;
  
  /**
   * 批处理服务名称
   */
  name?: string;
  
  /**
   * 批处理配置
   */
  config: AdvancedBatchProcessorOptions<T, R>;
}

@Module({})
export class AdvancedBatchModule {
  /**
   * 注册高级批处理模块
   * @param options 模块配置
   */
  static register<T, R>(options: AdvancedBatchModuleOptions<T, R>): DynamicModule {
    const serviceName = options.name || 'ADVANCED_BATCH_PROCESSOR';
    
    const providers: Provider[] = [
      {
        provide: serviceName,
        useFactory: (eventEmitter) => {
          return new AdvancedBatchProcessorService<T, R>(options.config, eventEmitter);
        },
        inject: [EventEmitter2],
      },
    ];
    
    return {
      global: options.isGlobal,
      imports: [EventEmitterModule.forRoot()],
      module: AdvancedBatchModule,
      providers,
      exports: [serviceName],
    };
  }
}
