/**
 * 音频控制器
 */
import { Controller, Get, UseGuards, Request, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AssetsService } from './assets.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('音频')
@Controller('audio')
export class AudioController {
  constructor(private readonly assetsService: AssetsService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有音频' })
  @ApiResponse({ status: 200, description: '返回所有音频' })
  async findAll(
    @Request() req,
    @Query('projectId') projectId?: string,
    @Query('tags') tags?: string,
  ) {
    const tagArray = tags ? tags.split(',') : undefined;
    return this.assetsService.findAllAudio(req.user.id, projectId, tagArray);
  }

  @Get('search')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索音频' })
  @ApiResponse({ status: 200, description: '返回搜索结果' })
  async search(@Request() req, @Query('query') query: string) {
    return this.assetsService.searchAudio(req.user.id, query);
  }
}
