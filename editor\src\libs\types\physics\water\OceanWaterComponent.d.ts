import { WaterBodyComponent, WaterBodyConfig } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
/**
 * 海洋波浪类型
 */
export declare enum OceanWaveType {
    /** 柔和波浪 */
    GENTLE = "gentle",
    /** 中等波浪 */
    MODERATE = "moderate",
    /** 剧烈波浪 */
    ROUGH = "rough",
    /** 风暴波浪 */
    STORMY = "stormy"
}
/**
 * 海洋水体配置
 */
export interface OceanWaterConfig extends WaterBodyConfig {
    /** 海洋尺寸 */
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    /** 海洋深度 */
    depth?: number;
    /** 海洋分辨率（几何体细分数） */
    resolution?: {
        width: number;
        depth: number;
    };
    /** 波浪类型 */
    waveType?: OceanWaveType;
    /** 波浪高度 */
    waveHeight?: number;
    /** 波浪频率 */
    waveFrequency?: number;
    /** 波浪方向 */
    waveDirection?: {
        x: number;
        z: number;
    };
    /** 是否启用FFT波浪模拟 */
    useFFTWaves?: boolean;
    /** 是否启用潮汐效果 */
    enableTides?: boolean;
    /** 潮汐周期（秒） */
    tidePeriod?: number;
    /** 潮汐高度 */
    tideHeight?: number;
    /** 是否启用海底地形 */
    enableSeabed?: boolean;
    /** 海底地形高度变化 */
    seabedHeightVariation?: number;
}
/**
 * 海洋水体组件
 */
export declare class OceanWaterComponent extends WaterBodyComponent {
    /** 海洋尺寸 */
    private oceanSize;
    /** 海洋深度 */
    private depth;
    /** 海洋分辨率 */
    private resolution;
    /** 波浪类型 */
    private waveType;
    /** 波浪高度 */
    private waveHeight;
    /** 波浪频率 */
    private waveFrequency;
    /** 波浪方向 */
    private waveDirection;
    /** 是否启用FFT波浪模拟 */
    private useFFTWaves;
    /** 是否启用潮汐效果 */
    private enableTides;
    /** 潮汐周期 */
    private tidePeriod;
    /** 潮汐高度 */
    private tideHeight;
    /** 是否启用海底地形 */
    private enableSeabed;
    /** 海底地形高度变化 */
    private seabedHeightVariation;
    /** 海洋几何体 */
    private oceanGeometry;
    /** 海底几何体 */
    private seabedGeometry;
    /** 潮汐时间 */
    private tideTime;
    /** 海洋波浪时间 */
    private oceanWaveTime;
    /** 波浪顶点位置 */
    private waveVertices;
    /** 波浪法线 */
    private waveNormals;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: OceanWaterConfig);
    /**
     * 根据波浪类型设置波浪参数
     * @param waveType 波浪类型
     */
    private setWaveParametersByType;
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 创建海洋几何体
     */
    private createOceanGeometry;
    /**
     * 创建海底几何体
     */
    private createSeabedGeometry;
    /**
     * 初始化波浪数组
     */
    private initializeWaveArrays;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新波浪
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaves;
    /**
     * 更新简单波浪
     * @param _deltaTime 帧间隔时间（秒）- 暂时未使用
     * @param positions 顶点位置数组
     * @param vertexCount 顶点数量
     */
    private updateSimpleWaves;
    /**
     * 更新FFT波浪
     * @param _deltaTime 帧间隔时间（秒）- 暂时未使用
     * @param positions 顶点位置数组
     * @param vertexCount 顶点数量
     */
    private updateFFTWaves;
    /**
     * 更新潮汐
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateTides;
    /**
     * 设置波浪类型
     * @param waveType 波浪类型
     */
    setWaveType(waveType: OceanWaveType): void;
    /**
     * 设置波浪参数
     * @param height 波浪高度
     * @param frequency 波浪频率
     * @param direction 波浪方向
     */
    setWaveParameters(height?: number, frequency?: number, direction?: {
        x: number;
        z: number;
    }): void;
    /**
     * 设置海洋尺寸
     * @param size 尺寸对象
     */
    setSize(size: {
        width: number;
        height: number;
        depth: number;
    }): void;
    /**
     * 设置海洋尺寸（便捷方法）
     * @param width 宽度
     * @param depth 深度
     */
    setOceanSize(width: number, depth: number): void;
    /**
     * 设置海洋深度
     * @param depth 深度
     */
    setDepth(depth: number): void;
    /**
     * 设置潮汐参数
     * @param enable 是否启用潮汐
     * @param period 潮汐周期
     * @param height 潮汐高度
     */
    setTideParameters(enable: boolean, period?: number, height?: number): void;
    /**
     * 设置海底参数
     * @param enable 是否启用海底地形
     * @param heightVariation 高度变化
     */
    setSeabedParameters(enable: boolean, heightVariation?: number): void;
    /**
     * 设置FFT波浪模拟
     * @param useFFT 是否使用FFT波浪模拟
     */
    setUseFFTWaves(useFFT: boolean): void;
}
