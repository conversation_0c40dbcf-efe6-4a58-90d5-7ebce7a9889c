/**
 * 水体光照系统
 * 用于处理水体的光照效果，包括水面反射、折射、焦散、体积光等
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
/**
 * 水体光照系统事件类型
 */
export declare enum WaterLightingSystemEventType {
    /** 水面反射更新 */
    REFLECTION_UPDATED = "reflection_updated",
    /** 水面折射更新 */
    REFRACTION_UPDATED = "refraction_updated",
    /** 焦散更新 */
    CAUSTICS_UPDATED = "caustics_updated",
    /** 体积光更新 */
    VOLUMETRIC_LIGHT_UPDATED = "volumetric_light_updated",
    /** 水下雾效更新 */
    UNDERWATER_FOG_UPDATED = "underwater_fog_updated"
}
/**
 * 水体光照系统配置
 */
export interface WaterLightingSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用水面反射 */
    enableReflection?: boolean;
    /** 是否启用水面折射 */
    enableRefraction?: boolean;
    /** 是否启用焦散 */
    enableCaustics?: boolean;
    /** 是否启用体积光 */
    enableVolumetricLight?: boolean;
    /** 是否启用水下雾效 */
    enableUnderwaterFog?: boolean;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
    /** 反射贴图分辨率 */
    reflectionMapResolution?: number;
    /** 折射贴图分辨率 */
    refractionMapResolution?: number;
    /** 焦散贴图分辨率 */
    causticsMapResolution?: number;
    /** 体积光贴图分辨率 */
    volumetricLightMapResolution?: number;
    /** 反射强度 */
    reflectionIntensity?: number;
    /** 折射强度 */
    refractionIntensity?: number;
    /** 焦散强度 */
    causticsIntensity?: number;
    /** 体积光强度 */
    volumetricLightIntensity?: number;
    /** 水下雾效强度 */
    underwaterFogIntensity?: number;
    /** 水下雾效颜色 */
    underwaterFogColor?: THREE.Color;
    /** 水下雾效密度 */
    underwaterFogDensity?: number;
    /** 是否启用高质量反射 */
    enableHighQualityReflection?: boolean;
    /** 是否启用高质量折射 */
    enableHighQualityRefraction?: boolean;
    /** 是否启用高质量焦散 */
    enableHighQualityCaustics?: boolean;
    /** 是否启用高质量体积光 */
    enableHighQualityVolumetricLight?: boolean;
}
/**
 * 水体光照系统
 */
export declare class WaterLightingSystem extends System {
    /** 配置 */
    private config;
    /** 水体组件 */
    private waterBodies;
    /** 帧计数器 */
    private frameCount;
    /** 当前更新频率 */
    private currentUpdateFrequency;
    /** 事件发射器 */
    private eventEmitter;
    /** 反射相机 */
    private reflectionCamera;
    /** 折射相机 */
    private refractionCamera;
    /** 反射渲染目标 */
    private reflectionRenderTarget;
    /** 折射渲染目标 */
    private refractionRenderTarget;
    /** 焦散渲染目标 */
    private causticsRenderTarget;
    /** 体积光渲染目标 */
    private volumetricLightRenderTarget;
    /** 焦散材质 */
    private causticsMaterial;
    /** 体积光材质 */
    private volumetricLightMaterial;
    /** 水下雾效材质 */
    private underwaterFogMaterial;
    /** 反射平面 */
    private reflectionPlane;
    /** 折射平面 */
    private refractionPlane;
    /** 是否在水下 */
    private isUnderwater;
    /** 当前水体 */
    private currentWaterBody;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: WaterLightingSystemConfig);
    /**
     * 初始化渲染目标
     */
    private initializeRenderTargets;
    /**
     * 初始化相机
     */
    private initializeCameras;
    /**
     * 初始化着色器
     */
    private initializeShaders;
    /**
     * 初始化焦散材质
     */
    private initializeCausticsMaterial;
    /**
     * 初始化体积光材质
     */
    private initializeVolumetricLightMaterial;
    /**
     * 初始化水下雾效材质
     */
    private initializeUnderwaterFogMaterial;
    /**
     * 添加水体
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    addWaterBody(entityId: string, waterBody: WaterBodyComponent): void;
    /**
     * 移除水体
     * @param entityId 实体ID
     */
    removeWaterBody(entityId: string): void;
    /**
     * 获取水体
     * @param entityId 实体ID
     * @returns 水体组件
     */
    getWaterBody(entityId: string): WaterBodyComponent | undefined;
    /**
     * 获取所有水体
     * @returns 水体组件映射
     */
    getWaterBodies(): Map<string, WaterBodyComponent>;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新水体光照效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterLighting;
    /**
     * 检查相机是否在水下
     * @param camera 相机
     */
    private checkCameraUnderwater;
    /**
     * 更新水体光照效果
     * @param waterBody 水体组件
     * @param camera 相机
     * @param scene 场景
     * @param renderer 渲染器
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterBodyLighting;
    /**
     * 更新反射
     * @param waterBody 水体组件
     * @param camera 相机
     * @param scene 场景
     * @param renderer 渲染器
     */
    private updateReflection;
    /**
     * 更新折射
     * @param waterBody 水体组件
     * @param camera 相机
     * @param scene 场景
     * @param renderer 渲染器
     */
    private updateRefraction;
    /**
     * 更新焦散
     * @param waterBody 水体组件
     * @param camera 相机
     * @param scene 场景
     * @param renderer 渲染器
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateCaustics;
    /**
     * 更新体积光
     * @param waterBody 水体组件
     * @param camera 相机
     * @param scene 场景
     * @param renderer 渲染器
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateVolumetricLight;
    /**
     * 更新水体材质
     * @param waterBody 水体组件
     */
    private updateWaterMaterial;
    /**
     * 应用水下效果
     * @param camera 相机
     * @param scene 场景
     * @param renderer 渲染器
     * @param deltaTime 帧间隔时间（秒）
     */
    private applyUnderwaterEffects;
    /**
     * 注册事件监听器
     * @param eventType 事件类型
     * @param callback 回调函数
     */
    addEventListener(eventType: WaterLightingSystemEventType, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param eventType 事件类型
     * @param callback 回调函数
     */
    removeEventListener(eventType: WaterLightingSystemEventType, callback: (data: any) => void): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
