# DL（Digital Learning）引擎兼容性测试框架

本目录包含DL（Digital Learning）引擎底层引擎部分的兼容性测试框架。这些测试用于比较原有项目和重构后项目的功能，确保重构后的项目与原有项目功能一致。

## 测试结构

测试按照引擎的模块结构组织，每个主要模块都有对应的测试目录：

- `core/` - 核心模块兼容性测试
- `rendering/` - 渲染系统兼容性测试
- `physics/` - 物理系统兼容性测试
- `animation/` - 动画系统兼容性测试
- `input/` - 输入系统兼容性测试
- `scene/` - 场景管理系统兼容性测试
- `network/` - 网络系统兼容性测试
- `visualscript/` - 视觉脚本系统兼容性测试
- `interaction/` - 交互系统兼容性测试
- `avatar/` - 头像系统兼容性测试
- `mocap/` - 动作捕捉系统兼容性测试

## 运行测试

可以使用以下命令运行兼容性测试：

```bash
# 运行所有兼容性测试
npm run test:compatibility

# 运行特定模块的兼容性测试
npm run test:compatibility -- --testPathPattern=core
npm run test:compatibility -- --testPathPattern=physics

# 运行特定测试文件
npm run test:compatibility -- physics/PhysicsSystem.compatibility.test.ts
```

## 测试框架

兼容性测试框架使用以下工具：

- **Vitest** - 测试运行器和断言库
- **CompatibilityTestFramework** - 自定义兼容性测试框架
- **Mock对象** - 用于模拟外部依赖

## 测试报告

兼容性测试完成后，会生成一个HTML报告，显示原有项目和重构后项目的功能对比结果。报告包括以下内容：

- 测试结果摘要
- 功能对比详情
- 兼容性问题列表
- 性能对比数据

## 注意事项

1. 兼容性测试需要同时加载原有项目和重构后项目的代码，因此可能会占用较多内存
2. 某些功能可能无法直接比较，需要使用特殊的比较策略
3. 兼容性测试的目的是确保功能一致性，而不是代码一致性
