/**
 * 角色控制器组件
 * 为实体提供角色控制器功能
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Component } from '../../core/Component';
import { CharacterControllerOptions } from '../character/CharacterController';
/**
 * 角色控制器组件
 */
export declare class CharacterControllerComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 角色控制器 */
    private controller;
    /** 控制器选项 */
    private options;
    /** 物理世界 */
    private world;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /** 计算出的移动向量 */
    private computedMovement;
    /**
     * 创建角色控制器组件
     * @param options 控制器选项
     */
    constructor(options?: CharacterControllerOptions);
    /**
     * 初始化角色控制器
     * @param world 物理世界
     */
    initialize(world: CANNON.World): void;
    /**
     * 计算碰撞器移动
     * @param desiredTranslation 期望的移动向量
     * @param filterGroups 碰撞组过滤
     * @param filterPredicate 碰撞过滤谓词函数
     */
    computeColliderMovement(desiredTranslation: THREE.Vector3, filterGroups?: number, filterPredicate?: (body: CANNON.Body) => boolean): void;
    /**
     * 获取计算出的移动向量
     * @returns 计算出的移动向量
     */
    getComputedMovement(): THREE.Vector3;
    /**
     * 获取偏移量
     * @returns 偏移量
     */
    getOffset(): number;
    /**
     * 设置最大爬坡角度
     * @param angle 角度（弧度）
     */
    setMaxSlopeClimbAngle(angle: number): void;
    /**
     * 获取最大爬坡角度
     * @returns 角度（弧度）
     */
    getMaxSlopeClimbAngle(): number;
    /**
     * 设置最小滑坡角度
     * @param angle 角度（弧度）
     */
    setMinSlopeSlideAngle(angle: number): void;
    /**
     * 获取最小滑坡角度
     * @returns 角度（弧度）
     */
    getMinSlopeSlideAngle(): number;
    /**
     * 启用自动台阶
     * @param maxHeight 最大台阶高度
     * @param minWidth 最小台阶宽度
     * @param stepOverDynamic 是否可以踏上动态物体
     */
    enableAutoStep(maxHeight: number, minWidth: number, stepOverDynamic: boolean): void;
    /**
     * 禁用自动台阶
     */
    disableAutoStep(): void;
    /**
     * 启用地面吸附
     * @param distance 吸附距离
     */
    enableSnapToGround(distance: number): void;
    /**
     * 禁用地面吸附
     */
    disableSnapToGround(): void;
    /**
     * 是否启用自动台阶
     * @returns 是否启用
     */
    isAutoStepEnabled(): boolean;
    /**
     * 是否启用地面吸附
     * @returns 是否启用
     */
    isSnapToGroundEnabled(): boolean;
    /**
     * 获取自动台阶最大高度
     * @returns 最大高度
     */
    getAutoStepMaxHeight(): number;
    /**
     * 获取自动台阶最小宽度
     * @returns 最小宽度
     */
    getAutoStepMinWidth(): number;
    /**
     * 是否可以踏上动态物体
     * @returns 是否可以
     */
    canStepOverDynamic(): boolean;
    /**
     * 是否在地面上
     * @returns 是否在地面上
     */
    isOnGround(): boolean;
    /**
     * 获取地面法线
     * @returns 地面法线
     */
    getGroundNormal(): THREE.Vector3;
    /**
     * 销毁组件
     */
    dispose(): void;
}
