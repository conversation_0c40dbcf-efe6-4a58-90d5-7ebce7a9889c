/**
 * 瀑布示例
 * 展示如何使用瀑布组件和预设创建各种瀑布效果
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { RenderingSystem } from '../../engine/src/rendering/RenderingSystem';
import { CameraSystem } from '../../engine/src/camera/CameraSystem';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { PhysicsSystem } from '../../engine/src/physics/PhysicsSystem';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../engine/src/physics/water/WaterInteractionSystem';
import { UnderwaterParticleSystem } from '../../engine/src/rendering/water/UnderwaterParticleSystem';
import { WaterLightingSystem } from '../../engine/src/rendering/water/WaterLightingSystem';
import { AudioSystem } from '../../engine/src/audio/AudioSystem';
import { WaterfallComponent } from '../../engine/src/physics/water/WaterfallComponent';
import { WaterfallPresets, WaterfallPresetType } from '../../engine/src/physics/water/WaterfallPresets';
import { TerrainSystem } from '../../engine/src/terrain/TerrainSystem';
import { TerrainComponent } from '../../engine/src/terrain/TerrainComponent';
import { Debug } from '../../engine/src/utils/Debug';

/**
 * 瀑布示例
 */
export class WaterfallExample {
  /** 世界 */
  private world: World;
  /** 渲染系统 */
  private renderingSystem: RenderingSystem;
  /** 相机系统 */
  private cameraSystem: CameraSystem;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水体交互系统 */
  private waterInteractionSystem: WaterInteractionSystem;
  /** 水下粒子系统 */
  private underwaterParticleSystem: UnderwaterParticleSystem;
  /** 水体光照系统 */
  private waterLightingSystem: WaterLightingSystem;
  /** 音频系统 */
  private audioSystem: AudioSystem;
  /** 地形系统 */
  private terrainSystem: TerrainSystem;
  /** 当前瀑布类型 */
  private currentWaterfallType: WaterfallPresetType = WaterfallPresetType.STANDARD;
  /** 当前瀑布实体 */
  private currentWaterfallEntity: Entity | null = null;

  /**
   * 创建瀑布示例
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 创建系统
    this.createSystems();

    // 创建场景
    this.createScene();

    // 创建UI
    this.createUI();

    // 启动
    this.start();
  }

  /**
   * 创建系统
   */
  private createSystems(): void {
    // 创建渲染系统
    this.renderingSystem = new RenderingSystem(this.world);
    this.world.addSystem(this.renderingSystem);

    // 创建相机系统
    this.cameraSystem = new CameraSystem(this.world);
    this.world.addSystem(this.cameraSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world);
    this.world.addSystem(this.inputSystem);

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem(this.world);
    this.world.addSystem(this.physicsSystem);

    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水体交互系统
    this.waterInteractionSystem = new WaterInteractionSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true
    });
    this.world.addSystem(this.waterInteractionSystem);

    // 创建水下粒子系统
    this.underwaterParticleSystem = new UnderwaterParticleSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      maxParticles: 5000
    });
    this.world.addSystem(this.underwaterParticleSystem);

    // 创建水体光照系统
    this.waterLightingSystem = new WaterLightingSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableVolumetricLight: true
    });
    this.world.addSystem(this.waterLightingSystem);

    // 创建音频系统
    this.audioSystem = new AudioSystem(this.world, {
      enabled: true,
      autoUpdate: true
    });
    this.world.addSystem(this.audioSystem);

    // 创建地形系统
    this.terrainSystem = new TerrainSystem(this.world, {
      enabled: true,
      autoUpdate: true
    });
    this.world.addSystem(this.terrainSystem);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建相机
    const cameraEntity = new Entity();
    cameraEntity.setName('main_camera');
    cameraEntity.setPosition(new THREE.Vector3(0, 10, 30));
    cameraEntity.setRotation(new THREE.Euler(-0.2, 0, 0));
    this.world.addEntity(cameraEntity);

    // 创建灯光
    const lightEntity = new Entity();
    lightEntity.setName('main_light');
    lightEntity.setPosition(new THREE.Vector3(10, 20, 10));
    this.world.addEntity(lightEntity);

    // 创建地形
    this.createTerrain();

    // 创建瀑布
    this.createWaterfall(this.currentWaterfallType);
  }

  /**
   * 创建地形
   */
  private createTerrain(): void {
    // 创建地形实体
    const terrainEntity = new Entity();
    terrainEntity.setName('terrain');

    // 创建地形组件
    const terrainComponent = new TerrainComponent(terrainEntity);
    terrainComponent.setSize(200, 50, 200);
    terrainComponent.setPosition(new THREE.Vector3(0, -10, 0));

    // 生成地形
    terrainComponent.generateTerrain({
      algorithm: 'perlin',
      seed: 12345,
      frequency: 0.02,
      octaves: 4,
      persistence: 0.5
    });

    // 添加到世界
    this.world.addEntity(terrainEntity);
  }

  /**
   * 创建瀑布
   * @param type 瀑布类型
   */
  private createWaterfall(type: WaterfallPresetType): void {
    // 如果已有瀑布，则移除
    if (this.currentWaterfallEntity) {
      this.world.removeEntity(this.currentWaterfallEntity);
      this.currentWaterfallEntity = null;
    }

    // 创建瀑布
    this.currentWaterfallEntity = WaterfallPresets.createPreset(this.world, {
      type,
      position: new THREE.Vector3(0, 20, -20),
      rotation: new THREE.Euler(-Math.PI / 6, 0, 0)
    });

    // 更新当前瀑布类型
    this.currentWaterfallType = type;

    Debug.log('WaterfallExample', `创建瀑布: ${type}`);
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.top = '10px';
    container.style.left = '10px';
    container.style.padding = '10px';
    container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    container.style.color = 'white';
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.borderRadius = '5px';
    document.body.appendChild(container);

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '瀑布示例';
    title.style.margin = '0 0 10px 0';
    container.appendChild(title);

    // 创建瀑布类型选择器
    const typeSelector = document.createElement('select');
    typeSelector.style.marginBottom = '10px';
    typeSelector.style.padding = '5px';
    typeSelector.style.width = '100%';
    container.appendChild(typeSelector);

    // 添加瀑布类型选项
    const types = Object.values(WaterfallPresetType);
    types.forEach(type => {
      const option = document.createElement('option');
      option.value = type;
      option.textContent = this.getWaterfallTypeName(type);
      typeSelector.appendChild(option);
    });

    // 设置默认选项
    typeSelector.value = this.currentWaterfallType;

    // 添加选择事件
    typeSelector.addEventListener('change', () => {
      const type = typeSelector.value as WaterfallPresetType;
      this.createWaterfall(type);
    });
  }

  /**
   * 获取瀑布类型名称
   * @param type 瀑布类型
   * @returns 瀑布类型名称
   */
  private getWaterfallTypeName(type: WaterfallPresetType): string {
    switch (type) {
      case WaterfallPresetType.STANDARD:
        return '标准瀑布';
      case WaterfallPresetType.HIGH:
        return '高瀑布';
      case WaterfallPresetType.WIDE:
        return '宽瀑布';
      case WaterfallPresetType.MULTI_LEVEL:
        return '多级瀑布';
      case WaterfallPresetType.SMALL:
        return '小瀑布';
      case WaterfallPresetType.MOUNTAIN:
        return '山涧瀑布';
      case WaterfallPresetType.VEIL:
        return '薄纱瀑布';
      case WaterfallPresetType.FOUNTAIN:
        return '喷泉';
      default:
        return type;
    }
  }

  /**
   * 启动
   */
  private start(): void {
    // 启动世界
    this.world.start();
  }
}
