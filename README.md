# DL（Digital Learning）引擎

DL（Digital Learning）引擎是一个强大的3D引擎和编辑器平台，采用微服务架构，支持大规模并发用户访问。

## 项目结构

项目分为三大部分：

1. **底层引擎（Engine）**：基于TypeScript和Three.js的3D渲染引擎
2. **编辑器（Editor）**：基于React、Redux和Ant Design的可视化编辑器
3. **服务器端（Server）**：基于Nest.js和MySQL的微服务架构

## 技术栈

- **前端**：TypeScript、React、Redux、Ant Design、Three.js、Vite
- **后端**：Nest.js、TypeORM、MySQL、Redis、Docker
- **微服务**：服务注册与发现、API网关、负载均衡

## 开发环境设置

### 前提条件

- Node.js >= 18
- npm >= 9
- Docker和Docker Compose
- MySQL 8.0

### 安装步骤

1. 克隆仓库：

```bash
git clone https://github.com/your-username/dl-engine.git
cd dl-engine/newsystem
```

2. 安装依赖：

```bash
# 安装引擎依赖
cd engine
npm install

# 安装编辑器依赖
cd ../editor
npm install

# 安装服务器依赖（各个微服务）
cd ../server/api-gateway
npm install
cd ../service-registry
npm install
cd ../user-service
npm install
# ... 安装其他微服务依赖
```

3. 配置环境变量：

```bash
cp .env.example .env
# 编辑.env文件，设置必要的环境变量
```

4. 启动开发环境：

```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 或者单独启动各个组件
# 启动引擎开发服务器
cd engine
npm run dev

# 启动编辑器开发服务器
cd ../editor
npm run dev

# 启动各个微服务
cd ../server/api-gateway
npm run start:dev
# ... 启动其他微服务
```

## 生产环境部署

使用Docker Compose进行部署：

```bash
# 构建并启动所有服务
docker-compose -f docker-compose.yml up -d --build
```

## 文档

- [引擎文档](./engine/docs/README.md)
- [编辑器文档](./editor/docs/README.md)
- [服务器文档](./server/docs/README.md)
- [API文档](http://localhost:3000/api/docs)（启动API网关后访问）

## 贡献指南

1. Fork项目
2. 创建特性分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add some amazing feature'`
4. 推送到分支：`git push origin feature/amazing-feature`
5. 提交Pull Request

## 许可证

本项目采用MIT许可证 - 详情请参阅[LICENSE](./LICENSE)文件。
