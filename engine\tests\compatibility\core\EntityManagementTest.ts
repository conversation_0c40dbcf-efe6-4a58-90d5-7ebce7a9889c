/**
 * 实体管理兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 实体管理兼容性测试
 */
export const entityManagementTest: TestCase = {
  name: '实体管理兼容性测试',
  description: '测试实体管理功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目世界实例
      const originalWorld = new original.World();
      
      // 创建重构后项目世界实例
      const refactoredWorld = new refactored.World();
      
      // 检查世界实例是否创建成功
      if (!originalWorld || !refactoredWorld) {
        return {
          name: '实体管理兼容性测试',
          passed: false,
          errorMessage: '世界实例创建失败'
        };
      }
      
      // 创建实体
      const originalEntity = new original.Entity('TestEntity');
      const refactoredEntity = new refactored.Entity('TestEntity');
      
      // 检查实体是否创建成功
      if (!originalEntity || !refactoredEntity) {
        return {
          name: '实体管理兼容性测试',
          passed: false,
          errorMessage: '实体创建失败'
        };
      }
      
      // 添加实体到世界
      originalWorld.addEntity(originalEntity);
      refactoredWorld.addEntity(refactoredEntity);
      
      // 检查实体是否添加成功
      const originalEntityCount = originalWorld.getEntityCount();
      const refactoredEntityCount = refactoredWorld.getEntityCount();
      
      if (originalEntityCount !== refactoredEntityCount) {
        return {
          name: '实体管理兼容性测试',
          passed: false,
          errorMessage: `实体数量不一致: 原有项目=${originalEntityCount}, 重构后项目=${refactoredEntityCount}`,
          details: {
            originalEntityCount,
            refactoredEntityCount
          }
        };
      }
      
      // 通过ID获取实体
      const originalEntityById = originalWorld.getEntityById(originalEntity.id);
      const refactoredEntityById = refactoredWorld.getEntityById(refactoredEntity.id);
      
      if (!originalEntityById || !refactoredEntityById) {
        return {
          name: '实体管理兼容性测试',
          passed: false,
          errorMessage: '通过ID获取实体失败',
          details: {
            originalEntityById,
            refactoredEntityById
          }
        };
      }
      
      // 通过名称获取实体
      const originalEntityByName = originalWorld.getEntityByName('TestEntity');
      const refactoredEntityByName = refactoredWorld.getEntityByName('TestEntity');
      
      if (!originalEntityByName || !refactoredEntityByName) {
        return {
          name: '实体管理兼容性测试',
          passed: false,
          errorMessage: '通过名称获取实体失败',
          details: {
            originalEntityByName,
            refactoredEntityByName
          }
        };
      }
      
      // 移除实体
      originalWorld.removeEntity(originalEntity);
      refactoredWorld.removeEntity(refactoredEntity);
      
      // 检查实体是否移除成功
      const originalEntityCountAfterRemove = originalWorld.getEntityCount();
      const refactoredEntityCountAfterRemove = refactoredWorld.getEntityCount();
      
      if (originalEntityCountAfterRemove !== refactoredEntityCountAfterRemove) {
        return {
          name: '实体管理兼容性测试',
          passed: false,
          errorMessage: `移除实体后数量不一致: 原有项目=${originalEntityCountAfterRemove}, 重构后项目=${refactoredEntityCountAfterRemove}`,
          details: {
            originalEntityCountAfterRemove,
            refactoredEntityCountAfterRemove
          }
        };
      }
      
      return {
        name: '实体管理兼容性测试',
        passed: true,
        details: {
          originalEntityCount,
          refactoredEntityCount,
          originalEntityCountAfterRemove,
          refactoredEntityCountAfterRemove
        }
      };
    } catch (error) {
      return {
        name: '实体管理兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
