/**
 * 地下河生成器
 * 用于生成地下河系统
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';
import { Debug } from '../../utils/Debug';

/**
 * 地下河生成参数
 */
export interface UndergroundRiverParams {
  /** 河流数量 */
  count: number;
  /** 河流宽度 */
  width: number;
  /** 河流深度 */
  depth: number;
  /** 河流曲折度 */
  sinuosity: number;
  /** 河流分支概率 */
  branchProbability: number;
  /** 最小长度 */
  minLength: number;
  /** 最大长度 */
  maxLength: number;
  /** 最小深度（相对于地形高度的比例） */
  minDepthRatio: number;
  /** 最大深度（相对于地形高度的比例） */
  maxDepthRatio: number;
  /** 连接到洞穴的概率 */
  caveProbability: number;
  /** 种子 */
  seed: number;
}

/**
 * 地下河节点
 */
interface UndergroundRiverNode {
  /** X坐标 */
  x: number;
  /** Z坐标 */
  z: number;
  /** 深度（相对于地形高度的比例） */
  depthRatio: number;
  /** 宽度 */
  width: number;
}

/**
 * 地下河
 */
interface UndergroundRiver {
  /** 节点列表 */
  nodes: UndergroundRiverNode[];
  /** 分支列表 */
  branches: UndergroundRiver[];
}

/**
 * 地下河生成器
 */
export class UndergroundRiverGenerator {
  /**
   * 生成地下河系统
   * @param terrain 地形组件
   * @param params 地下河生成参数
   */
  public static generateUndergroundRivers(terrain: TerrainComponent, params: UndergroundRiverParams): void {
    const { count, width, depth, sinuosity, branchProbability, minLength, maxLength, minDepthRatio, maxDepthRatio, caveProbability, seed } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 生成多条地下河
    const rivers: UndergroundRiver[] = [];
    for (let i = 0; i < count; i++) {
      // 随机选择起点
      const startX = Math.floor(random() * resolution);
      const startZ = Math.floor(random() * resolution);

      // 随机选择初始方向
      const angle = random() * Math.PI * 2;
      const dirX = Math.cos(angle);
      const dirZ = Math.sin(angle);

      // 随机选择深度
      const startDepthRatio = minDepthRatio + random() * (maxDepthRatio - minDepthRatio);

      // 生成地下河
      const river = this.generateRiver(
        terrain,
        startX,
        startZ,
        dirX,
        dirZ,
        width,
        depth,
        sinuosity,
        branchProbability,
        minLength,
        maxLength,
        startDepthRatio,
        minDepthRatio,
        maxDepthRatio,
        random
      );

      rivers.push(river);
    }

    // 应用地下河到地形
    this.applyRiversToTerrain(terrain, rivers, depth);

    // 连接地下河到洞穴
    if (caveProbability > 0) {
      this.connectRiversToCaves(terrain, rivers, caveProbability, random);
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 生成单条地下河
   * @param terrain 地形组件
   * @param startX 起点X坐标
   * @param startZ 起点Z坐标
   * @param dirX X方向
   * @param dirZ Z方向
   * @param width 宽度
   * @param depth 深度
   * @param sinuosity 曲折度
   * @param branchProbability 分支概率
   * @param minLength 最小长度
   * @param maxLength 最大长度
   * @param startDepthRatio 起始深度比例
   * @param minDepthRatio 最小深度比例
   * @param maxDepthRatio 最大深度比例
   * @param random 随机数生成器
   * @returns 地下河
   */
  private static generateRiver(
    terrain: TerrainComponent,
    startX: number,
    startZ: number,
    dirX: number,
    dirZ: number,
    width: number,
    depth: number,
    sinuosity: number,
    branchProbability: number,
    minLength: number,
    maxLength: number,
    startDepthRatio: number,
    minDepthRatio: number,
    maxDepthRatio: number,
    random: () => number
  ): UndergroundRiver {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建地下河
    const river: UndergroundRiver = {
      nodes: [],
      branches: []
    };

    // 添加起点
    river.nodes.push({
      x: startX,
      z: startZ,
      depthRatio: startDepthRatio,
      width: width
    });

    // 当前位置
    let currentX = startX;
    let currentZ = startZ;
    let currentDirX = dirX;
    let currentDirZ = dirZ;
    let currentDepthRatio = startDepthRatio;

    // 河流长度
    const length = minLength + random() * (maxLength - minLength);

    // 生成河流路径
    for (let i = 0; i < length; i++) {
      // 更新方向（添加随机偏移）
      const angle = (random() - 0.5) * sinuosity;
      const newDirX = currentDirX * Math.cos(angle) - currentDirZ * Math.sin(angle);
      const newDirZ = currentDirX * Math.sin(angle) + currentDirZ * Math.cos(angle);

      currentDirX = newDirX;
      currentDirZ = newDirZ;

      // 更新位置
      currentX += currentDirX;
      currentZ += currentDirZ;

      // 确保位置在地形范围内
      if (currentX < 0 || currentX >= resolution || currentZ < 0 || currentZ >= resolution) {
        break;
      }

      // 更新深度
      currentDepthRatio += (random() - 0.5) * 0.02;
      currentDepthRatio = Math.max(minDepthRatio, Math.min(maxDepthRatio, currentDepthRatio));

      // 添加节点
      river.nodes.push({
        x: Math.floor(currentX),
        z: Math.floor(currentZ),
        depthRatio: currentDepthRatio,
        width: width * (0.8 + random() * 0.4) // 随机变化宽度
      });

      // 随机生成分支
      if (random() < branchProbability && river.nodes.length > 5) {
        // 分支方向
        const branchAngle = (random() - 0.5) * Math.PI;
        const branchDirX = currentDirX * Math.cos(branchAngle) - currentDirZ * Math.sin(branchAngle);
        const branchDirZ = currentDirX * Math.sin(branchAngle) + currentDirZ * Math.cos(branchAngle);

        // 生成分支
        const branch = this.generateRiver(
          terrain,
          currentX,
          currentZ,
          branchDirX,
          branchDirZ,
          width * 0.7, // 分支宽度减小
          depth,
          sinuosity * 1.2, // 分支曲折度增加
          branchProbability * 0.5, // 分支概率减小
          minLength * 0.5,
          maxLength * 0.5,
          currentDepthRatio,
          minDepthRatio,
          maxDepthRatio,
          random
        );

        river.branches.push(branch);
      }
    }

    return river;
  }

  /**
   * 应用地下河到地形
   * @param terrain 地形组件
   * @param rivers 地下河列表
   * @param depth 深度
   */
  private static applyRiversToTerrain(terrain: TerrainComponent, rivers: UndergroundRiver[], depth: number): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建地下河数据
    if (!terrain.metadata) {
      terrain.metadata = {};
    }

    // 存储地下河数据
    terrain.metadata.undergroundRivers = rivers;

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 连接地下河到洞穴
   * @param terrain 地形组件
   * @param rivers 地下河列表
   * @param caveProbability 洞穴概率
   * @param random 随机数生成器
   */
  private static connectRiversToCaves(terrain: TerrainComponent, rivers: UndergroundRiver[], caveProbability: number, random: () => number): void {
    // 如果没有洞穴数据，则返回
    if (!terrain.metadata || !terrain.metadata.caves) {
      return;
    }

    // 获取洞穴数据
    const caves = terrain.metadata.caves;

    // 遍历所有地下河
    for (const river of rivers) {
      // 根据概率决定是否连接到洞穴
      if (random() < caveProbability) {
        this.connectRiverToCave(terrain, river, caves, random);
      }

      // 递归处理分支
      for (const branch of river.branches) {
        if (random() < caveProbability * 0.5) { // 分支连接概率降低
          this.connectRiverToCave(terrain, branch, caves, random);
        }
      }
    }
  }

  /**
   * 连接单条地下河到洞穴
   * @param terrain 地形组件
   * @param river 地下河
   * @param caves 洞穴数据
   * @param random 随机数生成器
   */
  private static connectRiverToCave(terrain: TerrainComponent, river: UndergroundRiver, caves: any, random: () => number): void {
    const resolution = terrain.resolution;

    // 如果洞穴数据是数组形式
    if (Array.isArray(caves)) {
      // 找到最近的洞穴
      let closestCave = null;
      let minDistance = Infinity;

      // 随机选择河流上的一个节点作为连接点
      const riverNodeIndex = Math.floor(random() * river.nodes.length);
      const riverNode = river.nodes[riverNodeIndex];

      // 遍历所有洞穴，找到最近的洞穴
      for (const cave of caves) {
        // 假设洞穴数据包含中心点坐标
        if (cave.center) {
          const dx = cave.center.x - riverNode.x;
          const dz = cave.center.z - riverNode.z;
          const distance = Math.sqrt(dx * dx + dz * dz);

          if (distance < minDistance) {
            minDistance = distance;
            closestCave = cave;
          }
        }
      }

      // 如果找到了最近的洞穴，创建连接通道
      if (closestCave && minDistance < resolution * 0.3) { // 限制最大连接距离
        this.createConnectionTunnel(terrain, riverNode, closestCave, random);
      }
    }
    // 如果洞穴数据是其他形式，根据实际数据结构调整连接逻辑
    else if (typeof caves === 'object') {
      // 根据实际洞穴数据结构实现连接逻辑
      // 这里只是一个示例，实际实现应该根据洞穴数据的具体结构来调整
      if (caves.positions && caves.positions.length > 0) {
        // 随机选择一个洞穴位置
        const caveIndex = Math.floor(random() * caves.positions.length);
        const cavePosition = caves.positions[caveIndex];

        // 随机选择河流上的一个节点作为连接点
        const riverNodeIndex = Math.floor(random() * river.nodes.length);
        const riverNode = river.nodes[riverNodeIndex];

        // 创建连接通道
        this.createConnectionTunnel(terrain, riverNode, { center: cavePosition }, random);
      }
    }
  }

  /**
   * 创建连接通道
   * @param terrain 地形组件
   * @param riverNode 河流节点
   * @param cave 洞穴
   * @param random 随机数生成器
   */
  private static createConnectionTunnel(terrain: TerrainComponent, riverNode: UndergroundRiverNode, cave: any, random: () => number): void {
    // 创建从河流节点到洞穴的连接通道
    // 这里只是记录连接信息，不直接修改地形高度
    // 实际渲染时可以根据这些连接信息来渲染连接通道

    // 确保元数据中有连接数据
    if (!terrain.metadata) {
      terrain.metadata = {};
    }

    if (!terrain.metadata.riverCaveConnections) {
      terrain.metadata.riverCaveConnections = [];
    }

    // 添加连接信息
    terrain.metadata.riverCaveConnections.push({
      riverPoint: {
        x: riverNode.x,
        z: riverNode.z,
        depthRatio: riverNode.depthRatio
      },
      cavePoint: {
        x: cave.center.x,
        z: cave.center.z,
        // 假设洞穴深度是随机的
        depthRatio: 0.3 + random() * 0.4
      },
      width: riverNode.width * 0.8, // 连接通道宽度略小于河流宽度
      sinuosity: 0.3 + random() * 0.3 // 连接通道的曲折度
    });
  }

  /**
   * 创建随机数生成器
   * @param seed 种子
   * @returns 随机数生成器函数
   */
  private static createRandomGenerator(seed: number): () => number {
    // 简单的伪随机数生成器
    let s = seed;
    return () => {
      s = (s * 9301 + 49297) % 233280;
      return s / 233280;
    };
  }
}
