import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { AgonesModule } from '../agones/agones.module';
import { InstanceService } from './instance.service';
import { InstanceController } from './instance.controller';
import { InstanceMigrationService } from './instance-migration.service';
import { LoadBalancerService } from './load-balancer.service';

@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(),
    AgonesModule,
  ],
  controllers: [InstanceController],
  providers: [
    InstanceService,
    InstanceMigrationService,
    LoadBalancerService,
  ],
  exports: [
    InstanceService,
    InstanceMigrationService,
    LoadBalancerService,
  ],
})
export class InstanceModule {}
