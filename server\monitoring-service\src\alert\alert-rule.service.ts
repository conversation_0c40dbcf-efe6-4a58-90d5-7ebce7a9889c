import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AlertRuleEntity } from './entities/alert-rule.entity';
import { AlertRuleStatus } from './entities/alert.types';

@Injectable()
export class AlertRuleService {
  private readonly logger = new Logger(AlertRuleService.name);

  constructor(
    @InjectRepository(AlertRuleEntity)
    private readonly alertRuleRepository: Repository<AlertRuleEntity>,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 创建告警规则
   */
  async createAlertRule(ruleData: Partial<AlertRuleEntity>): Promise<AlertRuleEntity> {
    try {
      const rule = this.alertRuleRepository.create({
        ...ruleData,
        status: AlertRuleStatus.ACTIVE,
      });
      
      const savedRule = await this.alertRuleRepository.save(rule);
      
      // 触发告警规则创建事件
      this.eventEmitter.emit('alert.rule.created', savedRule);
      
      return savedRule;
    } catch (error) {
      this.logger.error(`创建告警规则失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新告警规则
   */
  async updateAlertRule(id: string, ruleData: Partial<AlertRuleEntity>): Promise<AlertRuleEntity> {
    try {
      const rule = await this.alertRuleRepository.findOne({ where: { id } });
      
      if (!rule) {
        throw new Error(`未找到告警规则: ${id}`);
      }
      
      // 更新规则
      Object.assign(rule, ruleData);
      
      const updatedRule = await this.alertRuleRepository.save(rule);
      
      // 触发告警规则更新事件
      this.eventEmitter.emit('alert.rule.updated', updatedRule);
      
      return updatedRule;
    } catch (error) {
      this.logger.error(`更新告警规则失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除告警规则
   */
  async deleteAlertRule(id: string): Promise<void> {
    try {
      const rule = await this.alertRuleRepository.findOne({ where: { id } });
      
      if (!rule) {
        throw new Error(`未找到告警规则: ${id}`);
      }
      
      await this.alertRuleRepository.remove(rule);
      
      // 触发告警规则删除事件
      this.eventEmitter.emit('alert.rule.deleted', id);
    } catch (error) {
      this.logger.error(`删除告警规则失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取所有告警规则 (别名方法)
   */
  async getAllRules(): Promise<AlertRuleEntity[]> {
    return this.getAllAlertRules({});
  }

  /**
   * 创建规则 (别名方法)
   */
  async createRule(ruleData: Partial<AlertRuleEntity>): Promise<AlertRuleEntity> {
    return this.createAlertRule(ruleData);
  }

  /**
   * 更新规则 (别名方法)
   */
  async updateRule(id: string, ruleData: Partial<AlertRuleEntity>): Promise<AlertRuleEntity> {
    return this.updateAlertRule(id, ruleData);
  }

  /**
   * 删除规则 (别名方法)
   */
  async deleteRule(id: string): Promise<void> {
    return this.deleteAlertRule(id);
  }

  /**
   * 切换规则状态
   */
  async toggleRule(id: string, enabled: boolean): Promise<AlertRuleEntity> {
    try {
      const rule = await this.alertRuleRepository.findOne({ where: { id } });

      if (!rule) {
        throw new Error(`未找到告警规则: ${id}`);
      }

      rule.enabled = enabled;
      rule.status = enabled ? AlertRuleStatus.ACTIVE : AlertRuleStatus.PAUSED;

      const updatedRule = await this.alertRuleRepository.save(rule);

      // 触发告警规则状态切换事件
      this.eventEmitter.emit('alert.rule.toggled', updatedRule);

      return updatedRule;
    } catch (error) {
      this.logger.error(`切换告警规则状态失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取所有告警规则
   */
  async getAllAlertRules(filters: {
    status?: AlertRuleStatus;
    serviceType?: string;
    metricName?: string;
    enabled?: boolean;
  }): Promise<AlertRuleEntity[]> {
    try {
      const query = this.alertRuleRepository.createQueryBuilder('rule');
      
      if (filters.status) {
        query.andWhere('rule.status = :status', { status: filters.status });
      }
      
      if (filters.serviceType) {
        query.andWhere('rule.serviceType = :serviceType', { serviceType: filters.serviceType });
      }
      
      if (filters.metricName) {
        query.andWhere('rule.metricName = :metricName', { metricName: filters.metricName });
      }
      
      if (filters.enabled !== undefined) {
        query.andWhere('rule.enabled = :enabled', { enabled: filters.enabled });
      }
      
      query.orderBy('rule.createdAt', 'DESC');
      
      return query.getMany();
    } catch (error) {
      this.logger.error(`获取告警规则失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取活跃的告警规则
   */
  async getActiveAlertRules(): Promise<AlertRuleEntity[]> {
    return this.getAllAlertRules({
      status: AlertRuleStatus.ACTIVE,
      enabled: true,
    });
  }

  /**
   * 暂停告警规则
   */
  async pauseAlertRule(id: string): Promise<AlertRuleEntity> {
    try {
      const rule = await this.alertRuleRepository.findOne({ where: { id } });
      
      if (!rule) {
        throw new Error(`未找到告警规则: ${id}`);
      }
      
      rule.status = AlertRuleStatus.PAUSED;
      
      const updatedRule = await this.alertRuleRepository.save(rule);
      
      // 触发告警规则暂停事件
      this.eventEmitter.emit('alert.rule.paused', updatedRule);
      
      return updatedRule;
    } catch (error) {
      this.logger.error(`暂停告警规则失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 激活告警规则
   */
  async activateAlertRule(id: string): Promise<AlertRuleEntity> {
    try {
      const rule = await this.alertRuleRepository.findOne({ where: { id } });
      
      if (!rule) {
        throw new Error(`未找到告警规则: ${id}`);
      }
      
      rule.status = AlertRuleStatus.ACTIVE;
      
      const updatedRule = await this.alertRuleRepository.save(rule);
      
      // 触发告警规则激活事件
      this.eventEmitter.emit('alert.rule.activated', updatedRule);
      
      return updatedRule;
    } catch (error) {
      this.logger.error(`激活告警规则失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新规则的最后触发时间
   */
  async updateLastTriggered(id: string): Promise<void> {
    try {
      await this.alertRuleRepository.update(id, {
        lastTriggered: new Date(),
      });
    } catch (error) {
      this.logger.error(`更新规则最后触发时间失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
