/**
 * 场景服务
 * 负责场景的加载、保存和管理
 */
import axios from 'axios';
import EngineService, { EngineEventType } from './EngineService';
import { EventEmitter } from "../utils/EventEmitter";

// 使用 any 类型避免复杂的类型推导和冲突
type EngineScene = any;
type EngineEntity = any;

// 场景事件类型
export enum SceneEventType {
  LOADING_START = 'loadingStart',
  LOADING_PROGRESS = 'loadingProgress',
  LOADING_COMPLETE = 'loadingComplete',
  LOADING_ERROR = 'loadingError',
  SAVING_START = 'savingStart',
  SAVING_PROGRESS = 'savingProgress',
  SAVING_COMPLETE = 'savingComplete',
  SAVING_ERROR = 'savingError',
  SCENE_CHANGED = 'sceneChanged',
  SCENE_GRAPH_CHANGED = 'sceneGraphChanged',
  SCENE_CREATED = 'sceneCreated',
  SCENE_DELETED = 'sceneDeleted',
  SCENE_RENAMED = 'sceneRenamed',
  SCENE_COPIED = 'sceneCopied',
  SCENE_EXPORTED = 'sceneExported',
  SCENE_IMPORTED = 'sceneImported',
  NODE_SELECTED = 'nodeSelected',
  NODE_DESELECTED = 'nodeDeselected',
  VALIDATION_ERROR = 'validationError',
  UNDO_PERFORMED = 'undoPerformed',
  REDO_PERFORMED = 'redoPerformed'}

// 场景图节点
export interface SceneGraphNode {
  id: string;
  name: string;
  type: string;
  children: SceneGraphNode[];
  components: string[];
  visible: boolean;
  locked: boolean;
  selected?: boolean;
  expanded?: boolean;
  position?: { x: number; y: number; z: number };
  rotation?: { x: number; y: number; z: number };
  scale?: { x: number; y: number; z: number };
}

// 场景信息
export interface SceneInfo {
  id: string;
  name: string;
  description?: string;
  thumbnail?: string;
  createdAt: Date;
  updatedAt: Date;
  version: number;
  template: string;
  tags: string[];
  metadata: Record<string, any>;
}

// 场景模板
export interface SceneTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  data: any;
  category: string;
  isBuiltIn: boolean;
}

// 撤销/重做操作
export interface UndoRedoOperation {
  id: string;
  type: string;
  description: string;
  timestamp: Date;
  data: any;
  inverse: any;
}

// 场景验证结果
export interface SceneValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// 导出选项
export interface SceneExportOptions {
  format: 'json' | 'gltf' | 'fbx' | 'obj';
  includeAssets: boolean;
  compressAssets: boolean;
  optimizeForWeb: boolean;
  exportPath?: string;
}

// 场景加载选项（与底层引擎一致）
export interface SceneLoadOptions {
  /** 是否设置为活跃场景 */
  setActive?: boolean;
  /** 是否显示加载界面 */
  showLoadingScreen?: boolean;
  /** 是否预加载资源 */
  preloadAssets?: boolean;
  /** 是否初始化场景图 */
  initializeSceneGraph?: boolean;
  /** 过渡选项 */
  transition?: SceneTransitionOptions;
  /** 加载完成回调 */
  onLoaded?: (scene: any) => void;
  /** 加载进度回调 */
  onProgress?: (progress: number) => void;
  /** 加载错误回调 */
  onError?: (error: Error) => void;
}

// 场景过渡选项
export interface SceneTransitionOptions {
  /** 过渡类型 */
  type: 'fade' | 'slide' | 'zoom' | 'dissolve' | 'none';
  /** 过渡持续时间（毫秒） */
  duration: number;
  /** 缓动函数 */
  easing?: string;
  /** 过渡方向（用于slide类型） */
  direction?: 'left' | 'right' | 'up' | 'down';
}

// 场景序列化选项（与底层引擎一致）
export interface SceneSerializeOptions {
  /** 是否包含实体 */
  includeEntities?: boolean;
  /** 是否包含组件 */
  includeComponents?: boolean;
  /** 是否包含天空盒 */
  includeSkybox?: boolean;
  /** 是否包含环境光 */
  includeAmbientLight?: boolean;
  /** 是否包含雾效 */
  includeFog?: boolean;
  /** 是否美化JSON输出 */
  prettyPrint?: boolean;
  /** 是否包含元数据 */
  includeMetadata?: boolean;
  /** 自定义元数据 */
  metadata?: Record<string, any>;
}

// 天空盒配置
export interface SkyboxConfig {
  type: 'color' | 'gradient' | 'cubemap' | 'hdri' | 'procedural';
  color?: string;
  gradientTop?: string;
  gradientBottom?: string;
  textures?: string[];
  hdriUrl?: string;
  proceduralSettings?: {
    sunPosition?: { x: number; y: number; z: number };
    cloudCoverage?: number;
    timeOfDay?: number;
  };
}

// 环境光配置
export interface AmbientLightConfig {
  color: string;
  intensity: number;
}

// 雾效配置
export interface FogConfig {
  enabled: boolean;
  type: 'linear' | 'exponential' | 'exponentialSquared';
  color: string;
  near?: number;
  far?: number;
  density?: number;
}

// 场景环境配置
export interface SceneEnvironmentConfig {
  skybox?: SkyboxConfig;
  ambientLight?: AmbientLightConfig;
  fog?: FogConfig;
  globalIllumination?: boolean;
  environmentMap?: string;
}

// 场景服务类
export class SceneService extends EventEmitter {
  private static instance: SceneService;

  private currentSceneId: string | null = null;
  private currentProjectId: string | null = null;
  private sceneGraph: SceneGraphNode | null = null;
  private isDirty: boolean = false;
  private autoSaveInterval: number | null = null;

  // 新增属性
  private selectedNodes: Set<string> = new Set();
  private undoStack: UndoRedoOperation[] = [];
  private redoStack: UndoRedoOperation[] = [];
  private maxUndoStackSize: number = 50;
  private sceneTemplates: Map<string, SceneTemplate> = new Map();
  private sceneInfoCache: Map<string, SceneInfo> = new Map();
  private validationErrors: string[] = [];
  private lastValidationTime: Date | null = null;

  // 底层引擎集成属性
  private sceneManager: any = null;
  private sceneSerializer: any = null;
  private scenePreloader: any = null;
  private sceneTransitionManager: any = null;
  private currentEnvironmentConfig: SceneEnvironmentConfig | null = null;
  private isTransitioning: boolean = false;
  private loadingProgress: number = 0;
  
  private constructor() {
    super();

    // 监听引擎事件
    EngineService.addEventListener(EngineEventType.SCENE_LOADED, this.handleSceneLoaded.bind(this));
    EngineService.addEventListener(EngineEventType.SCENE_UNLOADED, this.handleSceneUnloaded.bind(this));
    EngineService.addEventListener(EngineEventType.OBJECT_ADDED, this.handleObjectChanged.bind(this));
    EngineService.addEventListener(EngineEventType.OBJECT_REMOVED, this.handleObjectChanged.bind(this));
    EngineService.addEventListener(EngineEventType.OBJECT_CHANGED, this.handleObjectChanged.bind(this));
    EngineService.addEventListener(EngineEventType.COMPONENT_ADDED, this.handleObjectChanged.bind(this));
    EngineService.addEventListener(EngineEventType.COMPONENT_REMOVED, this.handleObjectChanged.bind(this));
    EngineService.addEventListener(EngineEventType.COMPONENT_CHANGED, this.handleObjectChanged.bind(this));

    // 初始化底层引擎组件
    this.initializeEngineComponents();
  }

  /**
   * 初始化底层引擎组件
   */
  private initializeEngineComponents(): void {
    try {
      // 获取场景管理器
      this.sceneManager = EngineService.getSceneManager();

      // 获取场景转换管理器
      this.sceneTransitionManager = EngineService.getSceneTransitionManager();

      // 初始化场景序列化器和预加载器（使用类型断言避免类型错误）
      const engine = EngineService.getEngine() as any;
      if (engine) {
        // 尝试获取场景序列化器
        this.sceneSerializer = engine.getSceneSerializer?.() || null;

        // 尝试获取场景预加载器
        this.scenePreloader = engine.getScenePreloader?.() || null;

        // 如果没有直接的方法，尝试从其他地方获取
        if (!this.sceneSerializer) {
          // 创建默认的场景序列化器
          this.sceneSerializer = {
            serialize: this.serializeScene.bind(this),
            deserialize: this.loadSceneFromData.bind(this)
          };
        }
      }

      console.log('底层引擎组件初始化完成');
    } catch (error) {
      console.warn('底层引擎组件初始化失败:', error);
    }
  }
  
  /**
   * 获取场景服务实例
   */
  public static getInstance(): SceneService {
    if (!SceneService.instance) {
      SceneService.instance = new SceneService();
    }
    return SceneService.instance;
  }
  
  /**
   * 加载场景
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @param options 加载选项
   */
  public async loadScene(projectId: string, sceneId: string, options: SceneLoadOptions = {}): Promise<EngineScene> {
    try {
      // 检查是否正在过渡
      if (this.isTransitioning) {
        throw new Error('场景正在切换中，请稍后再试');
      }

      // 合并默认选项
      const mergedOptions: SceneLoadOptions = {
        setActive: true,
        showLoadingScreen: true,
        preloadAssets: true,
        initializeSceneGraph: true,
        transition: {
          type: 'fade',
          duration: 500
        },
        ...options
      };

      // 发出加载开始事件
      this.emit(SceneEventType.LOADING_START, { projectId, sceneId, options: mergedOptions });

      // 显示加载界面
      if (mergedOptions.showLoadingScreen) {
        this.showLoadingScreen();
      }

      // 从API获取场景数据
      const response = await axios.get(`/api/projects/${projectId}/scenes/${sceneId}/data`);
      const sceneData = response.data;

      // 预加载资源
      if (mergedOptions.preloadAssets && this.scenePreloader) {
        try {
          await this.preloadSceneAssets(sceneData, mergedOptions.onProgress);
        } catch (error) {
          console.warn('预加载资源失败:', error);
        }
      }

      // 使用自定义方法加载场景数据
      await this.loadSceneFromData(sceneData);

      // 加载场景到引擎
      const scene = await EngineService.loadScene(sceneData);

      // 如果需要场景过渡
      if (mergedOptions.setActive && this.currentSceneId && mergedOptions.transition) {
        await this.transitionToScene(scene, mergedOptions.transition);
      }

      // 设置当前场景和项目ID
      this.currentSceneId = sceneId;
      this.currentProjectId = projectId;
      this.isDirty = false;

      // 初始化场景图
      if (mergedOptions.initializeSceneGraph) {
        this.updateSceneGraph();
      }

      // 调用加载完成回调
      if (mergedOptions.onLoaded) {
        mergedOptions.onLoaded(scene);
      }

      // 隐藏加载界面
      if (mergedOptions.showLoadingScreen) {
        this.hideLoadingScreen();
      }

      // 发出加载完成事件
      this.emit(SceneEventType.LOADING_COMPLETE, { scene, projectId, sceneId, options: mergedOptions });

      // 设置自动保存
      this.setupAutoSave();

      return scene;
    } catch (error) {
      console.error('加载场景失败:', error);

      // 调用错误回调
      if (options.onError) {
        options.onError(error as Error);
      }

      // 隐藏加载界面
      if (options.showLoadingScreen) {
        this.hideLoadingScreen();
      }

      this.emit(SceneEventType.LOADING_ERROR, { error, projectId, sceneId });
      throw error;
    }
  }
  
  /**
   * 保存场景
   */
  public async saveScene(): Promise<void> {
    if (!this.currentProjectId || !this.currentSceneId) {
      throw new Error('没有活动场景');
    }
    
    try {
      // 发出保存开始事件
      this.emit(SceneEventType.SAVING_START, { projectId: this.currentProjectId, sceneId: this.currentSceneId });

      // 使用自定义方法序列化场景
      const customSceneData = await this.serializeScene();

      // 同时获取引擎的场景数据
      const engineSceneData = await EngineService.saveScene();

      // 合并数据
      const sceneData = {
        ...engineSceneData,
        customData: customSceneData
      };
      
      // 保存到API
      await axios.put(`/api/projects/${this.currentProjectId}/scenes/${this.currentSceneId}/data`, sceneData);
      
      // 更新状态
      this.isDirty = false;
      
      // 发出保存完成事件
      this.emit(SceneEventType.SAVING_COMPLETE, { projectId: this.currentProjectId, sceneId: this.currentSceneId });
    } catch (error) {
      console.error('保存场景失败:', error);
      this.emit(SceneEventType.SAVING_ERROR, { error, projectId: this.currentProjectId, sceneId: this.currentSceneId });
      throw error;
    }
  }
  
  /**
   * 创建新场景
   * @param projectId 项目ID
   * @param name 场景名称
   * @param template 场景模板
   */
  public async createScene(projectId: string, name: string, template: string = 'empty'): Promise<any> {
    try {
      // 如果有底层场景管理器，优先使用它创建场景
      if (this.sceneManager && this.sceneManager.createScene) {
        const engineScene = this.sceneManager.createScene(name);
        console.log('使用底层场景管理器创建场景:', engineScene);
      }

      // 创建场景
      const response = await axios.post(`/api/projects/${projectId}/scenes`, {
        name,
        template});

      const sceneData = response.data;

      // 加载新场景
      await this.loadScene(projectId, sceneData.id);

      return sceneData;
    } catch (error) {
      console.error('创建场景失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新场景图
   */
  public updateSceneGraph(): void {
    // 优先使用底层场景管理器获取场景
    let scene = null;
    if (this.sceneManager && this.sceneManager.getActiveScene) {
      scene = this.sceneManager.getActiveScene();
    } else {
      scene = EngineService.getActiveScene();
    }

    if (!scene) {
      this.sceneGraph = null;
      return;
    }

    // 构建场景图 - 获取所有实体
    const entities = scene.getEntities();
    if (entities.length > 0) {
      // 假设第一个实体是根实体，或者创建一个虚拟根节点
      this.sceneGraph = this.buildSceneGraphFromEntities(entities);
    } else {
      this.sceneGraph = null;
    }

    // 发出场景图变化事件
    this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
  }
  
  /**
   * 从实体列表构建场景图
   * @param entities 实体列表
   */
  private buildSceneGraphFromEntities(entities: EngineEntity[]): SceneGraphNode {
    // 创建虚拟根节点
    const rootNode: SceneGraphNode = {
      id: 'root',
      name: 'Scene Root',
      type: 'Root',
      children: [],
      components: [],
      visible: true,
      locked: false};

    // 将所有实体作为根节点的子节点
    for (const entity of entities) {
      rootNode.children.push(this.buildSceneGraphNode(entity));
    }

    return rootNode;
  }

  /**
   * 构建场景图节点
   * @param entity 实体
   */
  private buildSceneGraphNode(entity: EngineEntity): SceneGraphNode {
    // 由于 dl-engine.d.ts 中的 Entity 类方法有限，我们使用基本属性
    const node: SceneGraphNode = {
      id: entity.id || 'unknown',
      name: entity.name || 'Entity',
      type: 'Entity',
      children: [],
      components: [], // 暂时为空，因为 getAllComponents 方法不存在
      visible: true, // 默认可见
      locked: false, // 默认未锁定
    };

    return node;
  }
  
  /**
   * 获取场景图
   */
  public getSceneGraph(): SceneGraphNode | null {
    return this.sceneGraph;
  }
  
  /**
   * 获取当前场景ID
   */
  public getCurrentSceneId(): string | null {
    return this.currentSceneId;
  }
  
  /**
   * 获取当前项目ID
   */
  public getCurrentProjectId(): string | null {
    return this.currentProjectId;
  }
  
  /**
   * 检查场景是否有未保存的更改
   */
  public isDirtyScene(): boolean {
    return this.isDirty;
  }
  
  /**
   * 设置自动保存
   * @param interval 自动保存间隔（毫秒），如果为0则禁用自动保存
   */
  public setupAutoSave(interval: number = 300000): void {
    // 清除现有的自动保存
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
    
    // 如果间隔大于0，设置新的自动保存
    if (interval > 0) {
      this.autoSaveInterval = window.setInterval(async () => {
        if (this.isDirty) {
          try {
            await this.saveScene();
            console.log('自动保存成功');
          } catch (error) {
            console.error('自动保存失败:', error);
          }
        }
      }, interval);
    }
  }
  
  /**
   * 处理场景加载事件
   */
  private handleSceneLoaded(scene: EngineScene): void {
    this.updateSceneGraph();
    this.isDirty = false;
    this.emit(SceneEventType.SCENE_CHANGED, scene);
  }

  /**
   * 处理场景卸载事件
   */
  private handleSceneUnloaded(_scene: EngineScene): void {
    this.sceneGraph = null;
    this.isDirty = false;
    this.emit(SceneEventType.SCENE_CHANGED, null);
  }
  
  /**
   * 处理对象变化事件
   */
  private handleObjectChanged(): void {
    this.updateSceneGraph();
    this.isDirty = true;
    this.emit(SceneEventType.SCENE_CHANGED, EngineService.getActiveScene());
  }

  // ==================== 场景管理功能 ====================

  /**
   * 删除场景
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  public async deleteScene(projectId: string, sceneId: string): Promise<void> {
    try {
      // 如果删除的是当前场景，先卸载
      if (this.currentSceneId === sceneId) {
        await this.unloadCurrentScene();
      }

      // 从API删除场景
      await axios.delete(`/api/projects/${projectId}/scenes/${sceneId}`);

      // 从缓存中移除
      this.sceneInfoCache.delete(sceneId);

      // 发出删除事件
      this.emit(SceneEventType.SCENE_DELETED, { projectId, sceneId });

      console.log('场景删除成功');
    } catch (error) {
      console.error('删除场景失败:', error);
      throw error;
    }
  }

  /**
   * 重命名场景
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @param newName 新名称
   */
  public async renameScene(projectId: string, sceneId: string, newName: string): Promise<void> {
    try {
      // 更新场景名称
      await axios.patch(`/api/projects/${projectId}/scenes/${sceneId}`, {
        name: newName
      });

      // 更新缓存
      const sceneInfo = this.sceneInfoCache.get(sceneId);
      if (sceneInfo) {
        sceneInfo.name = newName;
        sceneInfo.updatedAt = new Date();
      }

      // 发出重命名事件
      this.emit(SceneEventType.SCENE_RENAMED, { projectId, sceneId, newName });

      console.log('场景重命名成功');
    } catch (error) {
      console.error('重命名场景失败:', error);
      throw error;
    }
  }

  /**
   * 复制场景
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @param newName 新场景名称
   */
  public async copyScene(projectId: string, sceneId: string, newName: string): Promise<any> {
    try {
      // 复制场景
      const response = await axios.post(`/api/projects/${projectId}/scenes/${sceneId}/copy`, {
        name: newName
      });

      const newSceneData = response.data;

      // 发出复制事件
      this.emit(SceneEventType.SCENE_COPIED, {
        projectId,
        originalSceneId: sceneId,
        newSceneId: newSceneData.id,
        newName
      });

      console.log('场景复制成功');
      return newSceneData;
    } catch (error) {
      console.error('复制场景失败:', error);
      throw error;
    }
  }

  /**
   * 获取项目中的所有场景
   * @param projectId 项目ID
   */
  public async getScenes(projectId: string): Promise<SceneInfo[]> {
    try {
      const response = await axios.get(`/api/projects/${projectId}/scenes`);
      const scenes = response.data.map((scene: any) => ({
        ...scene,
        createdAt: new Date(scene.createdAt),
        updatedAt: new Date(scene.updatedAt)
      }));

      // 更新缓存
      scenes.forEach((scene: SceneInfo) => {
        this.sceneInfoCache.set(scene.id, scene);
      });

      return scenes;
    } catch (error) {
      console.error('获取场景列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取场景信息
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  public async getSceneInfo(projectId: string, sceneId: string): Promise<SceneInfo> {
    try {
      // 先检查缓存
      const cached = this.sceneInfoCache.get(sceneId);
      if (cached) {
        return cached;
      }

      // 从API获取
      const response = await axios.get(`/api/projects/${projectId}/scenes/${sceneId}/info`);
      const sceneInfo: SceneInfo = {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        updatedAt: new Date(response.data.updatedAt)
      };

      // 更新缓存
      this.sceneInfoCache.set(sceneId, sceneInfo);

      return sceneInfo;
    } catch (error) {
      console.error('获取场景信息失败:', error);
      throw error;
    }
  }

  /**
   * 卸载当前场景
   */
  public async unloadCurrentScene(): Promise<void> {
    if (!this.currentSceneId) {
      return;
    }

    try {
      // 如果有未保存的更改，提示用户
      if (this.isDirty) {
        console.warn('场景有未保存的更改');
      }

      // 优先使用底层场景管理器卸载场景
      if (this.sceneManager && this.sceneManager.unloadScene) {
        await this.sceneManager.unloadScene(this.currentSceneId);
        console.log('使用底层场景管理器卸载场景');
      } else {
        // 使用引擎服务卸载场景
        await EngineService.unloadScene();
      }

      // 清理状态
      this.currentSceneId = null;
      this.currentProjectId = null;
      this.sceneGraph = null;
      this.isDirty = false;
      this.selectedNodes.clear();
      this.clearUndoRedoStack();

      console.log('当前场景已卸载');
    } catch (error) {
      console.error('卸载场景失败:', error);
      throw error;
    }
  }

  // ==================== 场景图节点操作 ====================

  /**
   * 选择节点
   * @param nodeId 节点ID
   * @param multiSelect 是否多选
   */
  public selectNode(nodeId: string, multiSelect: boolean = false): void {
    if (!multiSelect) {
      this.selectedNodes.clear();
    }

    this.selectedNodes.add(nodeId);
    this.updateNodeSelection(nodeId, true);
    this.emit(SceneEventType.NODE_SELECTED, { nodeId, selectedNodes: Array.from(this.selectedNodes) });
  }

  /**
   * 取消选择节点
   * @param nodeId 节点ID
   */
  public deselectNode(nodeId: string): void {
    this.selectedNodes.delete(nodeId);
    this.updateNodeSelection(nodeId, false);
    this.emit(SceneEventType.NODE_DESELECTED, { nodeId, selectedNodes: Array.from(this.selectedNodes) });
  }

  /**
   * 清除所有选择
   */
  public clearSelection(): void {
    const selectedNodes = Array.from(this.selectedNodes);
    this.selectedNodes.clear();

    // 更新场景图中的选择状态
    selectedNodes.forEach(nodeId => {
      this.updateNodeSelection(nodeId, false);
    });

    this.emit(SceneEventType.NODE_DESELECTED, { nodeId: null, selectedNodes: [] });
  }

  /**
   * 获取选中的节点
   */
  public getSelectedNodes(): string[] {
    return Array.from(this.selectedNodes);
  }

  /**
   * 更新节点选择状态
   * @param nodeId 节点ID
   * @param selected 是否选中
   */
  private updateNodeSelection(nodeId: string, selected: boolean): void {
    if (!this.sceneGraph) return;

    const updateNode = (node: SceneGraphNode): boolean => {
      if (node.id === nodeId) {
        node.selected = selected;
        return true;
      }

      for (const child of node.children) {
        if (updateNode(child)) {
          return true;
        }
      }

      return false;
    };

    updateNode(this.sceneGraph);
    this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
  }

  /**
   * 重命名节点
   * @param nodeId 节点ID
   * @param newName 新名称
   */
  public renameNode(nodeId: string, newName: string): void {
    if (!this.sceneGraph) return;

    const renameNodeRecursive = (node: SceneGraphNode): boolean => {
      if (node.id === nodeId) {
        const oldName = node.name;
        node.name = newName;

        // 记录撤销操作
        this.addUndoOperation({
          id: this.generateOperationId(),
          type: 'renameNode',
          description: `重命名节点 "${oldName}" 为 "${newName}"`,
          timestamp: new Date(),
          data: { nodeId, newName },
          inverse: { nodeId, newName: oldName }
        });

        return true;
      }

      for (const child of node.children) {
        if (renameNodeRecursive(child)) {
          return true;
        }
      }

      return false;
    };

    if (renameNodeRecursive(this.sceneGraph)) {
      this.isDirty = true;
      this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
    }
  }

  /**
   * 删除节点
   * @param nodeId 节点ID
   */
  public deleteNode(nodeId: string): void {
    if (!this.sceneGraph) return;

    const deleteNodeRecursive = (parent: SceneGraphNode, index: number): SceneGraphNode | null => {
      const node = parent.children[index];
      if (node.id === nodeId) {
        // 记录撤销操作
        this.addUndoOperation({
          id: this.generateOperationId(),
          type: 'deleteNode',
          description: `删除节点 "${node.name}"`,
          timestamp: new Date(),
          data: { nodeId },
          inverse: { parentId: parent.id, index, nodeData: { ...node } }
        });

        // 从选择中移除
        this.selectedNodes.delete(nodeId);

        // 删除节点
        return parent.children.splice(index, 1)[0];
      }

      for (let i = 0; i < node.children.length; i++) {
        const deleted = deleteNodeRecursive(node, i);
        if (deleted) {
          return deleted;
        }
      }

      return null;
    };

    // 检查根节点
    if (this.sceneGraph.id === nodeId) {
      console.warn('无法删除根节点');
      return;
    }

    for (let i = 0; i < this.sceneGraph.children.length; i++) {
      const deleted = deleteNodeRecursive(this.sceneGraph, i);
      if (deleted) {
        this.isDirty = true;
        this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
        break;
      }
    }
  }

  // ==================== 撤销/重做功能 ====================

  /**
   * 添加撤销操作
   * @param operation 操作
   */
  private addUndoOperation(operation: UndoRedoOperation): void {
    this.undoStack.push(operation);

    // 限制撤销栈大小
    if (this.undoStack.length > this.maxUndoStackSize) {
      this.undoStack.shift();
    }

    // 清空重做栈
    this.redoStack = [];
  }

  /**
   * 撤销操作
   */
  public undo(): boolean {
    if (this.undoStack.length === 0) {
      return false;
    }

    const operation = this.undoStack.pop()!;

    try {
      this.executeOperation(operation.inverse, operation.type);
      this.redoStack.push(operation);
      this.emit(SceneEventType.UNDO_PERFORMED, operation);
      return true;
    } catch (error) {
      console.error('撤销操作失败:', error);
      return false;
    }
  }

  /**
   * 重做操作
   */
  public redo(): boolean {
    if (this.redoStack.length === 0) {
      return false;
    }

    const operation = this.redoStack.pop()!;

    try {
      this.executeOperation(operation.data, operation.type);
      this.undoStack.push(operation);
      this.emit(SceneEventType.REDO_PERFORMED, operation);
      return true;
    } catch (error) {
      console.error('重做操作失败:', error);
      return false;
    }
  }

  /**
   * 执行操作
   * @param data 操作数据
   * @param type 操作类型
   */
  private executeOperation(data: any, type: string): void {
    switch (type) {
      case 'renameNode':
        this.renameNodeDirect(data.nodeId, data.newName);
        break;
      case 'deleteNode':
        // 重新插入节点
        if (data.parentId && data.nodeData) {
          this.insertNodeDirect(data.parentId, data.index, data.nodeData);
        }
        break;
      // 可以添加更多操作类型
      default:
        console.warn('未知的操作类型:', type);
    }
  }

  /**
   * 直接重命名节点（不记录撤销）
   * @param nodeId 节点ID
   * @param newName 新名称
   */
  private renameNodeDirect(nodeId: string, newName: string): void {
    if (!this.sceneGraph) return;

    const renameNodeRecursive = (node: SceneGraphNode): boolean => {
      if (node.id === nodeId) {
        node.name = newName;
        return true;
      }

      for (const child of node.children) {
        if (renameNodeRecursive(child)) {
          return true;
        }
      }

      return false;
    };

    if (renameNodeRecursive(this.sceneGraph)) {
      this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
    }
  }

  /**
   * 直接插入节点（不记录撤销）
   * @param parentId 父节点ID
   * @param index 插入位置
   * @param nodeData 节点数据
   */
  private insertNodeDirect(parentId: string, index: number, nodeData: SceneGraphNode): void {
    if (!this.sceneGraph) return;

    const insertNodeRecursive = (node: SceneGraphNode): boolean => {
      if (node.id === parentId) {
        node.children.splice(index, 0, nodeData);
        return true;
      }

      for (const child of node.children) {
        if (insertNodeRecursive(child)) {
          return true;
        }
      }

      return false;
    };

    if (insertNodeRecursive(this.sceneGraph)) {
      this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
    }
  }

  /**
   * 清空撤销重做栈
   */
  private clearUndoRedoStack(): void {
    this.undoStack = [];
    this.redoStack = [];
  }

  /**
   * 生成操作ID
   */
  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 获取撤销栈信息
   */
  public getUndoStackInfo(): { canUndo: boolean; canRedo: boolean; undoCount: number; redoCount: number } {
    return {
      canUndo: this.undoStack.length > 0,
      canRedo: this.redoStack.length > 0,
      undoCount: this.undoStack.length,
      redoCount: this.redoStack.length
    };
  }

  // ==================== 场景验证功能 ====================

  /**
   * 验证场景
   * @param scene 场景数据（可选，默认使用当前场景）
   */
  public async validateScene(scene?: any): Promise<SceneValidationResult> {
    const result: SceneValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    try {
      const sceneToValidate = scene || EngineService.getActiveScene();
      if (!sceneToValidate) {
        result.isValid = false;
        result.errors.push('没有可验证的场景');
        return result;
      }

      // 验证场景图结构
      this.validateSceneGraph(result);

      // 验证实体和组件
      this.validateEntities(sceneToValidate, result);

      // 验证资源引用
      await this.validateAssetReferences(sceneToValidate, result);

      // 验证性能相关
      this.validatePerformance(sceneToValidate, result);

      // 更新验证状态
      this.validationErrors = result.errors;
      this.lastValidationTime = new Date();

      if (result.errors.length > 0) {
        result.isValid = false;
        this.emit(SceneEventType.VALIDATION_ERROR, result);
      }

      return result;
    } catch (error) {
      result.isValid = false;
      result.errors.push(`验证过程中发生错误: ${error}`);
      return result;
    }
  }

  /**
   * 验证场景图结构
   * @param result 验证结果
   */
  private validateSceneGraph(result: SceneValidationResult): void {
    if (!this.sceneGraph) {
      result.warnings.push('场景图为空');
      return;
    }

    // 检查重复ID
    const ids = new Set<string>();
    const checkDuplicateIds = (node: SceneGraphNode): void => {
      if (ids.has(node.id)) {
        result.errors.push(`发现重复的节点ID: ${node.id}`);
      } else {
        ids.add(node.id);
      }

      node.children.forEach(checkDuplicateIds);
    };

    checkDuplicateIds(this.sceneGraph);

    // 检查节点名称
    const checkNodeNames = (node: SceneGraphNode): void => {
      if (!node.name || node.name.trim() === '') {
        result.warnings.push(`节点 ${node.id} 没有名称`);
      }

      if (node.name && node.name.length > 100) {
        result.warnings.push(`节点 ${node.id} 名称过长`);
      }

      node.children.forEach(checkNodeNames);
    };

    checkNodeNames(this.sceneGraph);
  }

  /**
   * 验证实体和组件
   * @param scene 场景
   * @param result 验证结果
   */
  private validateEntities(scene: any, result: SceneValidationResult): void {
    try {
      const entities = scene.getEntities ? scene.getEntities() : [];

      if (entities.length === 0) {
        result.warnings.push('场景中没有实体');
        return;
      }

      entities.forEach((entity: any, index: number) => {
        // 检查实体基本属性
        if (!entity.id) {
          result.errors.push(`实体 ${index} 缺少ID`);
        }

        if (!entity.name) {
          result.warnings.push(`实体 ${entity.id || index} 缺少名称`);
        }

        // 检查组件
        if (entity.getComponents) {
          const components = entity.getComponents();
          if (components.size === 0) {
            result.warnings.push(`实体 ${entity.name || entity.id} 没有组件`);
          }
        }
      });

      if (entities.length > 1000) {
        result.warnings.push(`场景中实体数量较多 (${entities.length})，可能影响性能`);
      }
    } catch (error) {
      result.errors.push(`验证实体时发生错误: ${error}`);
    }
  }

  /**
   * 验证资源引用
   * @param scene 场景
   * @param result 验证结果
   */
  private async validateAssetReferences(scene: any, result: SceneValidationResult): Promise<void> {
    try {
      // 检查场景中引用的资源是否存在
      const entities = scene.getEntities ? scene.getEntities() : [];
      let assetCount = 0;

      entities.forEach((entity: any) => {
        // 检查实体的组件中是否有资源引用
        if (entity.getComponents) {
          const components = entity.getComponents();
          components.forEach((component: any) => {
            // 检查常见的资源属性
            if (component.texture || component.material || component.mesh || component.audio) {
              assetCount++;
            }
          });
        }
      });

      if (assetCount > 0) {
        result.suggestions.push(`场景中发现 ${assetCount} 个资源引用，建议定期检查资源的有效性`);
      } else {
        result.suggestions.push('建议定期检查资源引用的有效性');
      }
    } catch (error) {
      result.errors.push(`验证资源引用时发生错误: ${error}`);
    }
  }

  /**
   * 验证性能相关
   * @param scene 场景
   * @param result 验证结果
   */
  private validatePerformance(scene: any, result: SceneValidationResult): void {
    try {
      const entities = scene.getEntities ? scene.getEntities() : [];

      // 检查实体数量
      if (entities.length > 500) {
        result.warnings.push(`实体数量较多 (${entities.length})，建议使用LOD或实例化渲染`);
      }

      // 检查场景图深度
      const getMaxDepth = (node: SceneGraphNode, depth: number = 0): number => {
        if (node.children.length === 0) {
          return depth;
        }
        return Math.max(...node.children.map(child => getMaxDepth(child, depth + 1)));
      };

      if (this.sceneGraph) {
        const maxDepth = getMaxDepth(this.sceneGraph);
        if (maxDepth > 10) {
          result.warnings.push(`场景图层级过深 (${maxDepth})，可能影响性能`);
        }
      }
    } catch (error) {
      result.errors.push(`验证性能时发生错误: ${error}`);
    }
  }

  /**
   * 获取最后验证结果
   */
  public getLastValidationResult(): { errors: string[]; lastValidationTime: Date | null } {
    return {
      errors: [...this.validationErrors],
      lastValidationTime: this.lastValidationTime
    };
  }

  // ==================== 场景导入导出功能 ====================

  /**
   * 导出场景
   * @param options 导出选项
   */
  public async exportScene(options: SceneExportOptions): Promise<Blob> {
    if (!this.currentProjectId || !this.currentSceneId) {
      throw new Error('没有活动场景');
    }

    try {
      // 获取场景数据
      const sceneData = await EngineService.saveScene();

      let exportData: any;

      switch (options.format) {
        case 'json':
          exportData = {
            version: '1.0',
            scene: sceneData,
            metadata: {
              exportedAt: new Date().toISOString(),
              exportOptions: options
            }
          };
          break;

        case 'gltf':
          // 这里需要实现GLTF导出逻辑
          exportData = await this.exportToGLTF(sceneData, options);
          break;

        default:
          throw new Error(`不支持的导出格式: ${options.format}`);
      }

      // 创建Blob
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });

      // 发出导出事件
      this.emit(SceneEventType.SCENE_EXPORTED, {
        projectId: this.currentProjectId,
        sceneId: this.currentSceneId,
        format: options.format,
        size: blob.size
      });

      return blob;
    } catch (error) {
      console.error('导出场景失败:', error);
      throw error;
    }
  }

  /**
   * 导入场景
   * @param file 场景文件
   * @param projectId 项目ID
   */
  public async importScene(file: File, projectId: string): Promise<any> {
    try {
      // 读取文件内容
      const content = await this.readFileContent(file);
      let sceneData: any;

      try {
        sceneData = JSON.parse(content);
      } catch (error) {
        throw new Error('无效的场景文件格式');
      }

      // 验证场景数据
      if (!sceneData.scene) {
        throw new Error('场景文件缺少场景数据');
      }

      // 创建新场景
      const sceneName = file.name.replace(/\.[^/.]+$/, ''); // 移除文件扩展名
      const newScene = await this.createScene(projectId, sceneName, 'imported');

      // 加载场景数据
      await EngineService.loadScene(sceneData.scene);

      // 发出导入事件
      this.emit(SceneEventType.SCENE_IMPORTED, {
        projectId,
        sceneId: newScene.id,
        fileName: file.name,
        fileSize: file.size
      });

      return newScene;
    } catch (error) {
      console.error('导入场景失败:', error);
      throw error;
    }
  }

  /**
   * 读取文件内容
   * @param file 文件
   */
  private readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsText(file);
    });
  }

  /**
   * 导出为GLTF格式
   * @param sceneData 场景数据
   * @param options 导出选项
   */
  private async exportToGLTF(sceneData: any, options: SceneExportOptions): Promise<any> {
    // 这里需要实现GLTF导出逻辑
    // 由于复杂性，这里只是一个占位符
    console.warn('GLTF导出功能尚未实现', {
      includeAssets: options.includeAssets,
      compressAssets: options.compressAssets,
      optimizeForWeb: options.optimizeForWeb,
      sceneDataType: typeof sceneData
    });

    // 基于选项创建不同的GLTF结构
    const gltfData = {
      asset: {
        version: '2.0',
        generator: 'DL Engine Scene Exporter',
        copyright: options.optimizeForWeb ? 'Optimized for Web' : 'Standard Export'
      },
      scene: 0,
      scenes: [
        {
          name: sceneData?.name || 'Scene',
          nodes: []
        }
      ],
      nodes: [],
      meshes: [],
      materials: [],
      textures: options.includeAssets ? [] : undefined,
      images: options.includeAssets ? [] : undefined
    };

    // 移除undefined属性
    Object.keys(gltfData).forEach(key => {
      if (gltfData[key as keyof typeof gltfData] === undefined) {
        delete gltfData[key as keyof typeof gltfData];
      }
    });

    return gltfData;
  }

  // ==================== 场景模板管理 ====================

  /**
   * 获取所有场景模板
   */
  public async getSceneTemplates(): Promise<SceneTemplate[]> {
    try {
      // 如果缓存为空，从API加载
      if (this.sceneTemplates.size === 0) {
        await this.loadSceneTemplates();
      }

      return Array.from(this.sceneTemplates.values());
    } catch (error) {
      console.error('获取场景模板失败:', error);
      throw error;
    }
  }

  /**
   * 从API加载场景模板
   */
  private async loadSceneTemplates(): Promise<void> {
    try {
      const response = await axios.get('/api/scene-templates');
      const templates = response.data;

      // 清空缓存并重新加载
      this.sceneTemplates.clear();

      templates.forEach((template: SceneTemplate) => {
        this.sceneTemplates.set(template.id, template);
      });
    } catch (error) {
      console.error('加载场景模板失败:', error);
      throw error;
    }
  }

  /**
   * 获取指定模板
   * @param templateId 模板ID
   */
  public async getSceneTemplate(templateId: string): Promise<SceneTemplate | null> {
    try {
      // 先检查缓存
      const cached = this.sceneTemplates.get(templateId);
      if (cached) {
        return cached;
      }

      // 从API获取
      const response = await axios.get(`/api/scene-templates/${templateId}`);
      const template = response.data;

      // 更新缓存
      this.sceneTemplates.set(templateId, template);

      return template;
    } catch (error) {
      console.error('获取场景模板失败:', error);
      return null;
    }
  }

  /**
   * 创建自定义模板
   * @param name 模板名称
   * @param description 模板描述
   * @param category 模板分类
   */
  public async createSceneTemplate(name: string, description: string, category: string = 'custom'): Promise<SceneTemplate> {
    if (!this.currentProjectId || !this.currentSceneId) {
      throw new Error('没有活动场景');
    }

    try {
      // 获取当前场景数据
      const sceneData = await EngineService.saveScene();

      // 生成缩略图（这里是占位符）
      const thumbnail = await this.generateSceneThumbnail();

      // 创建模板
      const templateData = {
        name,
        description,
        category,
        data: sceneData,
        thumbnail,
        isBuiltIn: false
      };

      const response = await axios.post('/api/scene-templates', templateData);
      const template = response.data;

      // 更新缓存
      this.sceneTemplates.set(template.id, template);

      console.log('场景模板创建成功');
      return template;
    } catch (error) {
      console.error('创建场景模板失败:', error);
      throw error;
    }
  }

  /**
   * 删除场景模板
   * @param templateId 模板ID
   */
  public async deleteSceneTemplate(templateId: string): Promise<void> {
    try {
      const template = this.sceneTemplates.get(templateId);
      if (template && template.isBuiltIn) {
        throw new Error('无法删除内置模板');
      }

      // 从API删除
      await axios.delete(`/api/scene-templates/${templateId}`);

      // 从缓存中移除
      this.sceneTemplates.delete(templateId);

      console.log('场景模板删除成功');
    } catch (error) {
      console.error('删除场景模板失败:', error);
      throw error;
    }
  }

  /**
   * 生成场景缩略图
   */
  private async generateSceneThumbnail(): Promise<string> {
    try {
      // 这里应该实现场景截图功能
      // 由于复杂性，这里返回一个占位符
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    } catch (error) {
      console.error('生成缩略图失败:', error);
      return '';
    }
  }

  // ==================== 场景预览功能 ====================

  /**
   * 生成场景预览图
   * @param width 宽度
   * @param height 高度
   */
  public async generateScenePreview(width: number = 256, height: number = 256): Promise<string> {
    try {
      // 这里应该调用引擎的截图功能
      // 由于没有具体的API，这里是占位符
      console.log(`生成场景预览图: ${width}x${height}`);

      // 返回占位符图片
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    } catch (error) {
      console.error('生成场景预览图失败:', error);
      throw error;
    }
  }

  /**
   * 更新场景信息
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @param updates 更新数据
   */
  public async updateSceneInfo(projectId: string, sceneId: string, updates: Partial<SceneInfo>): Promise<void> {
    try {
      // 更新场景信息
      await axios.patch(`/api/projects/${projectId}/scenes/${sceneId}/info`, updates);

      // 更新缓存
      const sceneInfo = this.sceneInfoCache.get(sceneId);
      if (sceneInfo) {
        Object.assign(sceneInfo, updates);
        sceneInfo.updatedAt = new Date();
      }

      console.log('场景信息更新成功');
    } catch (error) {
      console.error('更新场景信息失败:', error);
      throw error;
    }
  }

  // ==================== 实用工具方法 ====================

  /**
   * 序列化场景数据（缺失的方法实现）
   */
  private async serializeScene(): Promise<any> {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) {
        throw new Error('没有活动场景');
      }

      // 这里应该实现场景序列化逻辑
      // 由于没有具体的API，这里返回基本数据
      return {
        version: '1.0',
        timestamp: new Date().toISOString(),
        sceneGraph: this.sceneGraph,
        entities: scene.getEntities ? scene.getEntities() : [],
        metadata: {
          projectId: this.currentProjectId,
          sceneId: this.currentSceneId
        }
      };
    } catch (error) {
      console.error('序列化场景失败:', error);
      throw error;
    }
  }

  /**
   * 从数据加载场景（缺失的方法实现）
   * @param sceneData 场景数据
   */
  private async loadSceneFromData(sceneData: any): Promise<void> {
    try {
      // 这里应该实现从数据加载场景的逻辑
      // 由于没有具体的API，这里只是占位符
      console.log('从数据加载场景:', sceneData);

      // 模拟加载过程
      if (sceneData.entities) {
        // 加载实体
        console.log(`加载 ${sceneData.entities.length} 个实体`);
      }

      if (sceneData.sceneGraph) {
        // 恢复场景图
        this.sceneGraph = sceneData.sceneGraph;
      }
    } catch (error) {
      console.error('从数据加载场景失败:', error);
      throw error;
    }
  }

  /**
   * 获取场景统计信息
   */
  public getSceneStatistics(): any {
    // 优先使用底层场景管理器获取场景
    let scene = null;
    if (this.sceneManager && this.sceneManager.getActiveScene) {
      scene = this.sceneManager.getActiveScene();
    } else {
      scene = EngineService.getActiveScene();
    }

    const entities = scene?.getEntities ? scene.getEntities() : [];

    return {
      entityCount: entities.length,
      nodeCount: this.sceneGraph ? this.countNodes(this.sceneGraph) : 0,
      selectedNodeCount: this.selectedNodes.size,
      undoStackSize: this.undoStack.length,
      redoStackSize: this.redoStack.length,
      isDirty: this.isDirty,
      lastValidationTime: this.lastValidationTime,
      validationErrorCount: this.validationErrors.length,
      sceneManagerAvailable: !!this.sceneManager,
      sceneSerializerAvailable: !!this.sceneSerializer,
      scenePreloaderAvailable: !!this.scenePreloader,
      sceneTransitionManagerAvailable: !!this.sceneTransitionManager
    };
  }

  /**
   * 获取底层引擎组件状态
   */
  public getEngineComponentsStatus(): any {
    return {
      sceneManager: {
        available: !!this.sceneManager,
        methods: this.sceneManager ? Object.getOwnPropertyNames(this.sceneManager) : []
      },
      sceneSerializer: {
        available: !!this.sceneSerializer,
        methods: this.sceneSerializer ? Object.getOwnPropertyNames(this.sceneSerializer) : []
      },
      scenePreloader: {
        available: !!this.scenePreloader,
        methods: this.scenePreloader ? Object.getOwnPropertyNames(this.scenePreloader) : []
      },
      sceneTransitionManager: {
        available: !!this.sceneTransitionManager,
        methods: this.sceneTransitionManager ? Object.getOwnPropertyNames(this.sceneTransitionManager) : []
      }
    };
  }

  /**
   * 递归计算节点数量
   * @param node 节点
   */
  private countNodes(node: SceneGraphNode): number {
    let count = 1; // 当前节点
    for (const child of node.children) {
      count += this.countNodes(child);
    }
    return count;
  }

  // ==================== 场景加载和过渡功能 ====================

  /**
   * 显示加载界面
   */
  private showLoadingScreen(): void {
    try {
      // 创建或显示加载界面
      let loadingElement = document.getElementById('scene-loading-screen');

      if (!loadingElement) {
        loadingElement = document.createElement('div');
        loadingElement.id = 'scene-loading-screen';
        loadingElement.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          z-index: 10000;
          color: white;
          font-family: Arial, sans-serif;
        `;

        loadingElement.innerHTML = `
          <div style="text-align: center;">
            <div style="font-size: 18px; margin-bottom: 20px;">正在加载场景...</div>
            <div style="width: 200px; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px; overflow: hidden;">
              <div id="scene-loading-progress" style="width: 0%; height: 100%; background: #4CAF50; transition: width 0.3s;"></div>
            </div>
            <div id="scene-loading-text" style="font-size: 14px; margin-top: 10px; opacity: 0.8;">准备中...</div>
          </div>
        `;

        document.body.appendChild(loadingElement);
      }

      loadingElement.style.display = 'flex';
      this.loadingProgress = 0;
      this.updateLoadingProgress(0);
    } catch (error) {
      console.warn('显示加载界面失败:', error);
    }
  }

  /**
   * 隐藏加载界面
   */
  private hideLoadingScreen(): void {
    try {
      const loadingElement = document.getElementById('scene-loading-screen');
      if (loadingElement) {
        loadingElement.style.display = 'none';
      }
    } catch (error) {
      console.warn('隐藏加载界面失败:', error);
    }
  }

  /**
   * 更新加载进度
   * @param progress 进度值 (0-1)
   * @param text 进度文本
   */
  private updateLoadingProgress(progress: number, text?: string): void {
    try {
      this.loadingProgress = Math.max(0, Math.min(1, progress));

      const progressBar = document.getElementById('scene-loading-progress');
      if (progressBar) {
        progressBar.style.width = `${this.loadingProgress * 100}%`;
      }

      const progressText = document.getElementById('scene-loading-text');
      if (progressText && text) {
        progressText.textContent = text;
      }
    } catch (error) {
      console.warn('更新加载进度失败:', error);
    }
  }

  /**
   * 预加载场景资源
   * @param sceneData 场景数据
   * @param onProgress 进度回调
   */
  private async preloadSceneAssets(sceneData: any, onProgress?: (progress: number) => void): Promise<void> {
    try {
      if (!this.scenePreloader) {
        console.warn('场景预加载器未初始化');
        return;
      }

      // 分析场景中的资源
      const assets = this.analyzeSceneAssets(sceneData);

      if (assets.length === 0) {
        if (onProgress) onProgress(1);
        return;
      }

      let loadedCount = 0;
      const totalCount = assets.length;

      // 预加载每个资源
      for (const asset of assets) {
        try {
          await this.loadAsset(asset);
          loadedCount++;

          const progress = loadedCount / totalCount;
          this.updateLoadingProgress(progress, `加载资源 ${loadedCount}/${totalCount}`);

          if (onProgress) {
            onProgress(progress);
          }
        } catch (error) {
          console.warn(`加载资源失败: ${asset.url}`, error);
          loadedCount++;
        }
      }
    } catch (error) {
      console.error('预加载场景资源失败:', error);
      throw error;
    }
  }

  /**
   * 分析场景中的资源
   * @param sceneData 场景数据
   */
  private analyzeSceneAssets(sceneData: any): Array<{ url: string; type: string }> {
    const assets: Array<{ url: string; type: string }> = [];

    try {
      // 分析实体中的资源
      if (sceneData.entities) {
        sceneData.entities.forEach((entity: any) => {
          if (entity.components) {
            entity.components.forEach((component: any) => {
              // 检查网格组件
              if (component.type === 'MeshComponent' && component.data?.meshUrl) {
                assets.push({ url: component.data.meshUrl, type: 'model' });
              }

              // 检查材质组件
              if (component.type === 'MaterialComponent' && component.data) {
                if (component.data.diffuseMap) {
                  assets.push({ url: component.data.diffuseMap, type: 'texture' });
                }
                if (component.data.normalMap) {
                  assets.push({ url: component.data.normalMap, type: 'texture' });
                }
              }

              // 检查音频组件
              if (component.type === 'AudioComponent' && component.data?.audioUrl) {
                assets.push({ url: component.data.audioUrl, type: 'audio' });
              }
            });
          }
        });
      }

      // 分析天空盒资源
      if (sceneData.skybox) {
        if (sceneData.skybox.type === 'cubemap' && sceneData.skybox.textures) {
          sceneData.skybox.textures.forEach((url: string) => {
            assets.push({ url, type: 'texture' });
          });
        } else if (sceneData.skybox.type === 'hdri' && sceneData.skybox.hdriUrl) {
          assets.push({ url: sceneData.skybox.hdriUrl, type: 'texture' });
        }
      }
    } catch (error) {
      console.warn('分析场景资源失败:', error);
    }

    return assets;
  }

  /**
   * 加载单个资源
   * @param asset 资源信息
   */
  private async loadAsset(asset: { url: string; type: string }): Promise<any> {
    try {
      const resourceManager = EngineService.getResourceManager();
      if (resourceManager) {
        return await resourceManager.loadResource(asset.url, asset.type);
      } else {
        // 使用资源管理器加载
        const assetManager = EngineService.getAssetManager();
        if (assetManager) {
          switch (asset.type) {
            case 'model':
              return await assetManager.loadModel(asset.url);
            case 'texture':
              return await assetManager.loadTexture(asset.url);
            case 'audio':
              return await assetManager.loadAudio(asset.url);
            default:
              console.warn(`不支持的资源类型: ${asset.type}`);
              return null;
          }
        }
      }
    } catch (error) {
      console.error(`加载资源失败: ${asset.url}`, error);
      throw error;
    }
  }

  /**
   * 场景过渡
   * @param scene 目标场景
   * @param options 过渡选项
   */
  private async transitionToScene(scene: any, options: SceneTransitionOptions): Promise<void> {
    try {
      this.isTransitioning = true;

      // 如果有场景转换管理器，使用它
      if (this.sceneTransitionManager) {
        await this.sceneTransitionManager.transitionToScene(scene.id || scene.name, options.type, options.duration);
      } else {
        // 使用简单的过渡效果
        await this.simpleSceneTransition(options);
      }

      this.isTransitioning = false;
    } catch (error) {
      this.isTransitioning = false;
      console.error('场景过渡失败:', error);
      throw error;
    }
  }

  /**
   * 简单的场景过渡效果
   * @param options 过渡选项
   */
  private async simpleSceneTransition(options: SceneTransitionOptions): Promise<void> {
    return new Promise((resolve) => {
      const duration = options.duration || 500;

      switch (options.type) {
        case 'fade':
          this.fadeTransition(duration, resolve);
          break;
        case 'none':
          resolve();
          break;
        default:
          // 默认使用淡入淡出
          this.fadeTransition(duration, resolve);
          break;
      }
    });
  }

  /**
   * 淡入淡出过渡效果
   * @param duration 持续时间
   * @param callback 完成回调
   */
  private fadeTransition(duration: number, callback: () => void): void {
    try {
      // 创建过渡遮罩
      const overlay = document.createElement('div');
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: black;
        z-index: 9999;
        opacity: 0;
        transition: opacity ${duration / 2}ms ease-in-out;
        pointer-events: none;
      `;

      document.body.appendChild(overlay);

      // 淡入
      setTimeout(() => {
        overlay.style.opacity = '1';
      }, 10);

      // 中间点，切换场景
      setTimeout(() => {
        // 这里可以进行实际的场景切换
        // 淡出
        overlay.style.opacity = '0';
      }, duration / 2);

      // 完成，移除遮罩
      setTimeout(() => {
        document.body.removeChild(overlay);
        callback();
      }, duration);
    } catch (error) {
      console.error('淡入淡出过渡失败:', error);
      callback();
    }
  }

  // ==================== 场景环境配置功能 ====================

  /**
   * 设置场景环境配置
   * @param config 环境配置
   */
  public async setSceneEnvironment(config: SceneEnvironmentConfig): Promise<void> {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) {
        throw new Error('没有活动场景');
      }

      // 保存当前环境配置
      this.currentEnvironmentConfig = { ...config };

      // 设置天空盒
      if (config.skybox) {
        await this.setSkybox(config.skybox);
      }

      // 设置环境光
      if (config.ambientLight) {
        this.setAmbientLight(config.ambientLight);
      }

      // 设置雾效
      if (config.fog) {
        this.setFog(config.fog);
      }

      // 设置全局光照
      if (config.globalIllumination !== undefined) {
        this.setGlobalIllumination(config.globalIllumination);
      }

      // 设置环境贴图
      if (config.environmentMap) {
        await this.setEnvironmentMap(config.environmentMap);
      }

      // 标记场景为已修改
      this.isDirty = true;

      console.log('场景环境配置设置成功');
    } catch (error) {
      console.error('设置场景环境失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前场景环境配置
   */
  public getSceneEnvironment(): SceneEnvironmentConfig | null {
    return this.currentEnvironmentConfig ? { ...this.currentEnvironmentConfig } : null;
  }

  /**
   * 设置天空盒
   * @param config 天空盒配置
   */
  public async setSkybox(config: SkyboxConfig): Promise<void> {
    try {
      const skyboxSystem = EngineService.getSkyboxSystem();
      if (!skyboxSystem) {
        console.warn('天空盒系统未初始化');
        return;
      }

      switch (config.type) {
        case 'color':
          skyboxSystem.setSkybox('color', { color: config.color });
          break;

        case 'gradient':
          skyboxSystem.setSkybox('gradient', {
            topColor: config.gradientTop,
            bottomColor: config.gradientBottom
          });
          break;

        case 'cubemap':
          if (config.textures && config.textures.length === 6) {
            const textures = await skyboxSystem.loadSkyboxTextures(config.textures);
            skyboxSystem.setSkybox('cubemap', { textures });
          }
          break;

        case 'hdri':
          if (config.hdriUrl) {
            const texture = await EngineService.loadTexture(config.hdriUrl);
            skyboxSystem.setSkybox('hdri', { texture });
          }
          break;

        case 'procedural':
          const proceduralOptions = {
            sunPosition: config.proceduralSettings?.sunPosition,
            cloudCoverage: config.proceduralSettings?.cloudCoverage || 0.5,
            timeOfDay: config.proceduralSettings?.timeOfDay || 12
          };
          skyboxSystem.createProceduralSky(proceduralOptions);
          break;

        default:
          console.warn(`不支持的天空盒类型: ${config.type}`);
      }

      console.log('天空盒设置成功');
    } catch (error) {
      console.error('设置天空盒失败:', error);
      throw error;
    }
  }

  /**
   * 设置环境光
   * @param config 环境光配置
   */
  public setAmbientLight(config: AmbientLightConfig): void {
    try {
      const environmentSystem = EngineService.getEnvironmentSystem();
      if (environmentSystem) {
        // 解析颜色
        const color = this.parseColor(config.color);
        environmentSystem.setAmbientLight(color, config.intensity);
      } else {
        // 直接操作场景
        const scene = EngineService.getActiveScene() as any;
        if (scene && scene.setAmbientLight) {
          const color = this.parseColor(config.color);
          scene.setAmbientLight(color, config.intensity);
        }
      }

      console.log('环境光设置成功');
    } catch (error) {
      console.error('设置环境光失败:', error);
      throw error;
    }
  }

  /**
   * 设置雾效
   * @param config 雾效配置
   */
  public setFog(config: FogConfig): void {
    try {
      const scene = EngineService.getActiveScene() as any;
      if (!scene) {
        throw new Error('没有活动场景');
      }

      if (config.enabled) {
        const color = this.parseColor(config.color);

        switch (config.type) {
          case 'linear':
            if (scene.setLinearFog) {
              scene.setLinearFog(color, config.near || 1, config.far || 1000);
            }
            break;

          case 'exponential':
            if (scene.setExponentialFog) {
              scene.setExponentialFog(color, config.density || 0.00025);
            }
            break;

          case 'exponentialSquared':
            if (scene.setExponentialSquaredFog) {
              scene.setExponentialSquaredFog(color, config.density || 0.00025);
            }
            break;

          default:
            console.warn(`不支持的雾效类型: ${config.type}`);
        }
      } else {
        // 禁用雾效
        if (scene.disableFog) {
          scene.disableFog();
        }
      }

      console.log('雾效设置成功');
    } catch (error) {
      console.error('设置雾效失败:', error);
      throw error;
    }
  }

  /**
   * 设置全局光照
   * @param enabled 是否启用
   */
  public setGlobalIllumination(enabled: boolean): void {
    try {
      const environmentSystem = EngineService.getEnvironmentSystem();
      if (environmentSystem) {
        environmentSystem.setGlobalIllumination(enabled);
        console.log(`全局光照${enabled ? '启用' : '禁用'}成功`);
      } else {
        console.warn('环境系统未初始化，无法设置全局光照');
      }
    } catch (error) {
      console.error('设置全局光照失败:', error);
      throw error;
    }
  }

  /**
   * 设置环境贴图
   * @param textureUrl 环境贴图URL
   */
  public async setEnvironmentMap(textureUrl: string): Promise<void> {
    try {
      const texture = await EngineService.loadTexture(textureUrl);
      const environmentSystem = EngineService.getEnvironmentSystem();

      if (environmentSystem) {
        environmentSystem.setEnvironmentMap(texture);
        console.log('环境贴图设置成功');
      } else {
        console.warn('环境系统未初始化，无法设置环境贴图');
      }
    } catch (error) {
      console.error('设置环境贴图失败:', error);
      throw error;
    }
  }

  /**
   * 解析颜色字符串
   * @param colorStr 颜色字符串（如 "#ffffff" 或 "rgb(255,255,255)"）
   */
  private parseColor(colorStr: string): any {
    try {
      // 如果是十六进制颜色
      if (colorStr.startsWith('#')) {
        const hex = colorStr.substring(1);
        const r = parseInt(hex.substring(0, 2), 16) / 255;
        const g = parseInt(hex.substring(2, 4), 16) / 255;
        const b = parseInt(hex.substring(4, 6), 16) / 255;
        return { r, g, b };
      }

      // 如果是 rgb 格式
      if (colorStr.startsWith('rgb')) {
        const matches = colorStr.match(/\d+/g);
        if (matches && matches.length >= 3) {
          const r = parseInt(matches[0]) / 255;
          const g = parseInt(matches[1]) / 255;
          const b = parseInt(matches[2]) / 255;
          return { r, g, b };
        }
      }

      // 默认返回白色
      return { r: 1, g: 1, b: 1 };
    } catch (error) {
      console.warn('解析颜色失败:', error);
      return { r: 1, g: 1, b: 1 };
    }
  }

  /**
   * 销毁场景服务
   */
  public dispose(): void {
    // 清除自动保存
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
    
    // 移除事件监听
    EngineService.removeEventListener(EngineEventType.SCENE_LOADED, this.handleSceneLoaded);
    EngineService.removeEventListener(EngineEventType.SCENE_UNLOADED, this.handleSceneUnloaded);
    EngineService.removeEventListener(EngineEventType.OBJECT_ADDED, this.handleObjectChanged);
    EngineService.removeEventListener(EngineEventType.OBJECT_REMOVED, this.handleObjectChanged);
    EngineService.removeEventListener(EngineEventType.OBJECT_CHANGED, this.handleObjectChanged);
    EngineService.removeEventListener(EngineEventType.COMPONENT_ADDED, this.handleObjectChanged);
    EngineService.removeEventListener(EngineEventType.COMPONENT_REMOVED, this.handleObjectChanged);
    EngineService.removeEventListener(EngineEventType.COMPONENT_CHANGED, this.handleObjectChanged);

    // 清除所有事件监听器
    this.removeAllListeners();
  }
}

export default SceneService.getInstance();
