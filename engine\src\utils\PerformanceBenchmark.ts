/**
 * 性能基准测试
 * 用于测试和比较不同优化技术的性能效果
 */
import * as THREE from 'three';
import { Debug } from './Debug';
import { EventEmitter } from './EventEmitter';
import { GPUPerformanceAnalyzer, AnalysisReport } from './GPUPerformanceAnalyzer';

/**
 * 基准测试事件类型
 */
export enum BenchmarkEventType {
  /** 测试开始 */
  TEST_STARTED = 'test_started',
  /** 测试完成 */
  TEST_COMPLETED = 'test_completed',
  /** 测试错误 */
  TEST_ERROR = 'test_error',
  /** 测试进度更新 */
  TEST_PROGRESS = 'test_progress',
  /** 所有测试完成 */
  ALL_TESTS_COMPLETED = 'all_tests_completed'
}

/**
 * 基准测试配置
 */
export interface BenchmarkConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 每个测试的持续时间（毫秒） */
  testDuration?: number;
  /** 每个测试的预热时间（毫秒） */
  warmupDuration?: number;
  /** 每个测试的采样间隔（毫秒） */
  sampleInterval?: number;
  /** 是否自动运行所有测试 */
  autoRunAll?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 测试场景
 */
export interface TestScene {
  /** 场景ID */
  id: string;
  /** 场景名称 */
  name: string;
  /** 场景描述 */
  description: string;
  /** 场景创建函数 */
  create: () => THREE.Scene;
  /** 场景清理函数 */
  cleanup: () => void;
  /** 场景更新函数 */
  update?: (deltaTime: number) => void;
  /** 场景配置 */
  config?: Record<string, any>;
}

/**
 * 测试结果
 */
export interface TestResult {
  /** 测试ID */
  id: string;
  /** 场景ID */
  sceneId: string;
  /** 优化技术ID */
  techniqueId: string;
  /** 分析报告 */
  report: AnalysisReport;
  /** 平均FPS */
  averageFps: number;
  /** 最小FPS */
  minFps: number;
  /** 最大FPS */
  maxFps: number;
  /** 平均渲染时间（毫秒） */
  averageRenderTime: number;
  /** 平均内存使用（MB） */
  averageMemoryUsage: number;
  /** 平均绘制调用次数 */
  averageDrawCalls: number;
  /** 平均三角形数量 */
  averageTriangles: number;
  /** 测试持续时间（毫秒） */
  duration: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 优化技术
 */
export interface OptimizationTechnique {
  /** 技术ID */
  id: string;
  /** 技术名称 */
  name: string;
  /** 技术描述 */
  description: string;
  /** 应用函数 */
  apply: (scene: THREE.Scene, renderer: THREE.WebGLRenderer, config?: Record<string, any>) => void;
  /** 清理函数 */
  cleanup: (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => void;
  /** 技术配置 */
  config?: Record<string, any>;
}

/**
 * 比较结果
 */
export interface ComparisonResult {
  /** 基准测试ID */
  baselineId: string;
  /** 比较测试ID */
  comparisonId: string;
  /** FPS改进（百分比） */
  fpsImprovement: number;
  /** 渲染时间改进（百分比） */
  renderTimeImprovement: number;
  /** 内存使用改进（百分比） */
  memoryUsageImprovement: number;
  /** 绘制调用改进（百分比） */
  drawCallsImprovement: number;
  /** 三角形数量改进（百分比） */
  trianglesImprovement: number;
  /** 总体改进（百分比） */
  overallImprovement: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 性能基准测试
 */
export class PerformanceBenchmark {
  /** 单例实例 */
  private static instance: PerformanceBenchmark;

  /** 配置 */
  private config: BenchmarkConfig;
  /** 是否运行中 */
  private running: boolean;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null;
  /** 相机 */
  private camera: THREE.Camera | null;
  /** 性能分析器 */
  private analyzer: GPUPerformanceAnalyzer;
  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 测试场景 */
  private scenes: Map<string, TestScene>;
  /** 优化技术 */
  private techniques: Map<string, OptimizationTechnique>;
  /** 测试结果 */
  private results: Map<string, TestResult>;
  /** 当前测试ID */
  private currentTestId: string | null;
  /** 当前场景 */
  private currentScene: THREE.Scene | null;
  /** 当前场景配置 */
  private currentSceneConfig: TestScene | null;
  /** 当前技术 */
  private currentTechnique: OptimizationTechnique | null;
  /** 测试开始时间 */
  private testStartTime: number;
  /** 测试结束时间 */
  private testEndTime: number;
  /** 动画帧ID */
  private animationFrameId: number | null;
  /** 上一帧时间 */
  private lastFrameTime: number;
  /** 测试队列 */
  private testQueue: { sceneId: string, techniqueId: string }[];
  /** 是否在预热阶段 */
  private isWarmup: boolean;
  /** 是否在采样阶段 */
  private isSampling: boolean;
  /** 采样定时器ID */
  private sampleTimerId: number | null;

  /**
   * 获取单例实例
   * @returns 性能基准测试实例
   */
  public static getInstance(): PerformanceBenchmark {
    if (!PerformanceBenchmark.instance) {
      PerformanceBenchmark.instance = new PerformanceBenchmark();
    }
    return PerformanceBenchmark.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    // 默认配置
    this.config = {
      enabled: true,
      testDuration: 10000,
      warmupDuration: 2000,
      sampleInterval: 100,
      autoRunAll: false,
      debug: false
    };

    this.running = false;
    this.renderer = null;
    this.camera = null;
    this.analyzer = GPUPerformanceAnalyzer.getInstance();
    this.eventEmitter = new EventEmitter();
    this.scenes = new Map();
    this.techniques = new Map();
    this.results = new Map();
    this.currentTestId = null;
    this.currentScene = null;
    this.currentSceneConfig = null;
    this.currentTechnique = null;
    this.testStartTime = 0;
    this.testEndTime = 0;
    this.animationFrameId = null;
    this.lastFrameTime = 0;
    this.testQueue = [];
    this.isWarmup = false;
    this.isSampling = false;
    this.sampleTimerId = null;
  }

  /**
   * 配置基准测试
   * @param config 配置
   */
  public configure(config: BenchmarkConfig): void {
    this.config = {
      ...this.config,
      ...config
    };

    if (this.config.debug) {
      Debug.log('PerformanceBenchmark', '配置已更新', this.config);
    }
  }

  /**
   * 设置渲染器
   * @param renderer 渲染器
   */
  public setRenderer(renderer: THREE.WebGLRenderer): void {
    this.renderer = renderer;
    this.analyzer.setRenderer(renderer);

    if (this.config.debug) {
      Debug.log('PerformanceBenchmark', '渲染器已设置');
    }
  }

  /**
   * 设置相机
   * @param camera 相机
   */
  public setCamera(camera: THREE.Camera): void {
    this.camera = camera;

    if (this.config.debug) {
      Debug.log('PerformanceBenchmark', '相机已设置');
    }
  }

  /**
   * 注册测试场景
   * @param scene 测试场景
   */
  public registerScene(scene: TestScene): void {
    this.scenes.set(scene.id, scene);

    if (this.config.debug) {
      Debug.log('PerformanceBenchmark', `场景已注册: ${scene.id} - ${scene.name}`);
    }
  }

  /**
   * 注册优化技术
   * @param technique 优化技术
   */
  public registerTechnique(technique: OptimizationTechnique): void {
    this.techniques.set(technique.id, technique);

    if (this.config.debug) {
      Debug.log('PerformanceBenchmark', `优化技术已注册: ${technique.id} - ${technique.name}`);
    }
  }

  /**
   * 运行测试
   * @param sceneId 场景ID
   * @param techniqueId 技术ID
   * @returns 测试ID
   */
  public runTest(sceneId: string, techniqueId: string): string {
    if (!this.config.enabled) {
      Debug.warn('PerformanceBenchmark', '基准测试未启用');
      return '';
    }

    if (this.running) {
      Debug.warn('PerformanceBenchmark', '已有测试正在运行');
      return '';
    }

    if (!this.renderer) {
      Debug.error('PerformanceBenchmark', '未设置渲染器');
      return '';
    }

    if (!this.camera) {
      Debug.error('PerformanceBenchmark', '未设置相机');
      return '';
    }

    // 获取场景
    const sceneConfig = this.scenes.get(sceneId);
    if (!sceneConfig) {
      Debug.error('PerformanceBenchmark', `未找到场景: ${sceneId}`);
      return '';
    }

    // 获取技术
    const technique = this.techniques.get(techniqueId);
    if (!technique) {
      Debug.error('PerformanceBenchmark', `未找到优化技术: ${techniqueId}`);
      return '';
    }

    // 生成测试ID
    const testId = `${sceneId}_${techniqueId}_${Date.now()}`;

    // 设置当前测试
    this.currentTestId = testId;
    this.currentSceneConfig = sceneConfig;
    this.currentTechnique = technique;

    // 创建场景
    this.currentScene = sceneConfig.create();

    // 应用优化技术
    technique.apply(this.currentScene, this.renderer, technique.config);

    // 设置状态
    this.running = true;
    this.isWarmup = true;
    this.isSampling = false;
    this.testStartTime = performance.now();
    this.lastFrameTime = this.testStartTime;

    // 配置分析器
    this.analyzer.configure({
      enabled: true,
      sampleInterval: this.config.sampleInterval,
      autoSample: false
    });

    // 清除分析器数据
    this.analyzer.clearData();

    // 启动分析器
    this.analyzer.start();

    // 发出测试开始事件
    this.eventEmitter.emit(BenchmarkEventType.TEST_STARTED, {
      testId,
      sceneId,
      techniqueId
    });

    if (this.config.debug) {
      Debug.log('PerformanceBenchmark', `测试开始: ${testId}`);
    }

    // 启动渲染循环
    this.startRenderLoop();

    // 启动预热定时器
    setTimeout(() => {
      this.isWarmup = false;
      this.isSampling = true;

      // 启动采样
      this.startSampling();

      // 启动测试结束定时器
      setTimeout(() => {
        this.endTest();
      }, this.config.testDuration);

    }, this.config.warmupDuration);

    return testId;
  }

  /**
   * 启动渲染循环
   */
  private startRenderLoop(): void {
    if (!this.renderer || !this.camera || !this.currentScene) {
      return;
    }

    const animate = () => {
      this.animationFrameId = requestAnimationFrame(animate);

      const now = performance.now();
      const deltaTime = (now - this.lastFrameTime) / 1000;
      this.lastFrameTime = now;

      // 更新场景
      if (this.currentSceneConfig && this.currentSceneConfig.update) {
        this.currentSceneConfig.update(deltaTime);
      }

      // 渲染场景
      this.renderer.render(this.currentScene, this.camera);

      // 发出进度事件
      if (this.isWarmup) {
        const progress = Math.min(1, (now - this.testStartTime) / this.config.warmupDuration!);
        this.eventEmitter.emit(BenchmarkEventType.TEST_PROGRESS, {
          testId: this.currentTestId,
          phase: 'warmup',
          progress
        });
      } else if (this.isSampling) {
        const elapsed = now - (this.testStartTime + this.config.warmupDuration!);
        const progress = Math.min(1, elapsed / this.config.testDuration!);
        this.eventEmitter.emit(BenchmarkEventType.TEST_PROGRESS, {
          testId: this.currentTestId,
          phase: 'sampling',
          progress
        });
      }
    };

    animate();
  }

  /**
   * 启动采样
   */
  private startSampling(): void {
    // 停止现有定时器
    this.stopSampling();

    // 创建新定时器
    this.sampleTimerId = window.setInterval(() => {
      this.analyzer.sample();
    }, this.config.sampleInterval);

    if (this.config.debug) {
      Debug.log('PerformanceBenchmark', `采样开始，间隔: ${this.config.sampleInterval}ms`);
    }
  }

  /**
   * 停止采样
   */
  private stopSampling(): void {
    if (this.sampleTimerId !== null) {
      window.clearInterval(this.sampleTimerId);
      this.sampleTimerId = null;
    }
  }

  /**
   * 结束测试
   */
  private endTest(): void {
    if (!this.running || !this.currentTestId || !this.currentSceneConfig || !this.currentTechnique) {
      return;
    }

    // 停止渲染循环
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    // 停止采样
    this.stopSampling();

    // 停止分析器
    this.analyzer.stop();

    // 获取分析报告
    const report = this.analyzer.getAnalysisReport();

    // 计算测试结果
    const result: TestResult = {
      id: this.currentTestId,
      sceneId: this.currentSceneConfig.id,
      techniqueId: this.currentTechnique.id,
      report,
      averageFps: report.averageMetrics.fps,
      minFps: report.minMetrics.fps,
      maxFps: report.maxMetrics.fps,
      averageRenderTime: report.averageMetrics.renderTime,
      averageMemoryUsage: report.averageMetrics.memoryUsage,
      averageDrawCalls: report.averageMetrics.drawCalls,
      averageTriangles: report.averageMetrics.triangles,
      duration: this.config.testDuration!,
      timestamp: performance.now()
    };

    // 存储结果
    this.results.set(this.currentTestId, result);

    // 清理场景
    if (this.currentTechnique) {
      this.currentTechnique.cleanup(this.currentScene!, this.renderer!);
    }

    if (this.currentSceneConfig) {
      this.currentSceneConfig.cleanup();
    }

    // 重置状态
    this.testEndTime = performance.now();
    this.running = false;
    this.isWarmup = false;
    this.isSampling = false;

    // 发出测试完成事件
    this.eventEmitter.emit(BenchmarkEventType.TEST_COMPLETED, result);

    if (this.config.debug) {
      Debug.log('PerformanceBenchmark', `测试完成: ${this.currentTestId}`, result);
    }

    // 检查测试队列
    if (this.testQueue.length > 0) {
      const nextTest = this.testQueue.shift()!;
      setTimeout(() => {
        this.runTest(nextTest.sceneId, nextTest.techniqueId);
      }, 1000);
    } else {
      // 所有测试完成
      this.eventEmitter.emit(BenchmarkEventType.ALL_TESTS_COMPLETED, Array.from(this.results.values()));
    }

    // 清除当前测试
    this.currentTestId = null;
    this.currentScene = null;
    this.currentSceneConfig = null;
    this.currentTechnique = null;
  }

  /**
   * 运行所有测试
   */
  public runAllTests(): void {
    if (!this.config.enabled) {
      Debug.warn('PerformanceBenchmark', '基准测试未启用');
      return;
    }

    if (this.running) {
      Debug.warn('PerformanceBenchmark', '已有测试正在运行');
      return;
    }

    // 清空测试队列
    this.testQueue = [];

    // 添加所有场景和技术组合到队列
    for (const scene of this.scenes.values()) {
      for (const technique of this.techniques.values()) {
        this.testQueue.push({
          sceneId: scene.id,
          techniqueId: technique.id
        });
      }
    }

    // 开始第一个测试
    if (this.testQueue.length > 0) {
      const firstTest = this.testQueue.shift()!;
      this.runTest(firstTest.sceneId, firstTest.techniqueId);
    }
  }

  /**
   * 获取测试结果
   * @param testId 测试ID
   * @returns 测试结果
   */
  public getTestResult(testId: string): TestResult | null {
    return this.results.get(testId) || null;
  }

  /**
   * 获取所有测试结果
   * @returns 所有测试结果
   */
  public getAllTestResults(): TestResult[] {
    return Array.from(this.results.values());
  }

  /**
   * 获取场景的测试结果
   * @param sceneId 场景ID
   * @returns 场景的测试结果
   */
  public getSceneResults(sceneId: string): TestResult[] {
    return Array.from(this.results.values()).filter(result => result.sceneId === sceneId);
  }

  /**
   * 获取技术的测试结果
   * @param techniqueId 技术ID
   * @returns 技术的测试结果
   */
  public getTechniqueResults(techniqueId: string): TestResult[] {
    return Array.from(this.results.values()).filter(result => result.techniqueId === techniqueId);
  }

  /**
   * 比较测试结果
   * @param baselineId 基准测试ID
   * @param comparisonId 比较测试ID
   * @returns 比较结果
   */
  public compareResults(baselineId: string, comparisonId: string): ComparisonResult | null {
    const baseline = this.results.get(baselineId);
    const comparison = this.results.get(comparisonId);

    if (!baseline || !comparison) {
      Debug.error('PerformanceBenchmark', `无法比较结果: 未找到测试 ${baselineId} 或 ${comparisonId}`);
      return null;
    }

    // 计算改进百分比
    const calculateImprovement = (baseValue: number, compValue: number): number => {
      if (baseValue === 0) {
        return compValue > 0 ? 100 : 0;
      }
      return ((compValue - baseValue) / baseValue) * 100;
    };

    // 计算改进百分比（对于越低越好的指标）
    const calculateInverseImprovement = (baseValue: number, compValue: number): number => {
      if (baseValue === 0) {
        return compValue > 0 ? -100 : 0;
      }
      return ((baseValue - compValue) / baseValue) * 100;
    };

    // 计算各项指标的改进
    const fpsImprovement = calculateImprovement(baseline.averageFps, comparison.averageFps);
    const renderTimeImprovement = calculateInverseImprovement(baseline.averageRenderTime, comparison.averageRenderTime);
    const memoryUsageImprovement = calculateInverseImprovement(baseline.averageMemoryUsage, comparison.averageMemoryUsage);
    const drawCallsImprovement = calculateInverseImprovement(baseline.averageDrawCalls, comparison.averageDrawCalls);
    const trianglesImprovement = calculateInverseImprovement(baseline.averageTriangles, comparison.averageTriangles);

    // 计算总体改进（加权平均）
    const weights = {
      fps: 0.3,
      renderTime: 0.3,
      memoryUsage: 0.15,
      drawCalls: 0.15,
      triangles: 0.1
    };

    const overallImprovement =
      fpsImprovement * weights.fps +
      renderTimeImprovement * weights.renderTime +
      memoryUsageImprovement * weights.memoryUsage +
      drawCallsImprovement * weights.drawCalls +
      trianglesImprovement * weights.triangles;

    // 创建比较结果
    const result: ComparisonResult = {
      baselineId,
      comparisonId,
      fpsImprovement,
      renderTimeImprovement,
      memoryUsageImprovement,
      drawCallsImprovement,
      trianglesImprovement,
      overallImprovement,
      timestamp: performance.now()
    };

    if (this.config.debug) {
      Debug.log('PerformanceBenchmark', `比较结果: ${baselineId} vs ${comparisonId}`, result);
    }

    return result;
  }

  /**
   * 添加事件监听器
   * @param eventType 事件类型
   * @param listener 监听器
   */
  public addEventListener(eventType: BenchmarkEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(eventType, listener);
  }

  /**
   * 移除事件监听器
   * @param eventType 事件类型
   * @param listener 监听器
   */
  public removeEventListener(eventType: BenchmarkEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(eventType, listener);
  }

  /**
   * 清除所有数据
   */
  public clearData(): void {
    this.results.clear();

    if (this.config.debug) {
      Debug.log('PerformanceBenchmark', '数据已清除');
    }
  }
}
