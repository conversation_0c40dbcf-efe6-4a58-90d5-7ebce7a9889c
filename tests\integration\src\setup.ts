/**
 * 集成测试环境设置
 */
import dotenv from 'dotenv';
import axios from 'axios';
import waitForExpect from 'wait-for-expect';

// 加载环境变量
dotenv.config();

// 设置更长的超时时间
jest.setTimeout(30000);

// 测试环境配置
export const TEST_CONFIG = {
  serviceRegistry: {
    url: process.env.SERVICE_REGISTRY_URL || 'http://localhost:4020',
  },
  userService: {
    url: process.env.USER_SERVICE_URL || 'http://localhost:4021',
  },
  projectService: {
    url: process.env.PROJECT_SERVICE_URL || 'http://localhost:4022',
  },
  assetService: {
    url: process.env.ASSET_SERVICE_URL || 'http://localhost:4023',
  },
};

/**
 * 等待服务健康检查通过
 * @param serviceUrl 服务URL
 * @param maxRetries 最大重试次数
 * @param retryInterval 重试间隔（毫秒）
 */
export const waitForServiceHealth = async (
  serviceUrl: string,
  maxRetries = 10,
  retryInterval = 2000
): Promise<boolean> => {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      const response = await axios.get(`${serviceUrl}/health`);
      if (response.status === 200 && response.data.status === 'ok') {
        console.log(`服务 ${serviceUrl} 健康检查通过`);
        return true;
      }
    } catch (error) {
      console.log(`等待服务 ${serviceUrl} 就绪...（${retries + 1}/${maxRetries}）`);
    }
    
    retries++;
    await new Promise(resolve => setTimeout(resolve, retryInterval));
  }
  
  throw new Error(`服务 ${serviceUrl} 健康检查失败，超过最大重试次数`);
};

/**
 * 创建测试用户
 * @param username 用户名
 * @param password 密码
 * @param email 邮箱
 */
export const createTestUser = async (
  username: string,
  password: string,
  email: string
) => {
  try {
    const response = await axios.post(`${TEST_CONFIG.userService.url}/users`, {
      username,
      password,
      email,
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 409) {
      // 用户已存在，尝试登录
      const loginResponse = await axios.post(`${TEST_CONFIG.userService.url}/auth/login`, {
        username,
        password,
      });
      return loginResponse.data;
    }
    throw error;
  }
};

/**
 * 登录测试用户
 * @param username 用户名
 * @param password 密码
 */
export const loginTestUser = async (username: string, password: string) => {
  const response = await axios.post(`${TEST_CONFIG.userService.url}/auth/login`, {
    username,
    password,
  });
  return response.data;
};

/**
 * 创建测试项目
 * @param token 认证令牌
 * @param name 项目名称
 * @param description 项目描述
 */
export const createTestProject = async (
  token: string,
  name: string,
  description?: string
) => {
  const response = await axios.post(
    `${TEST_CONFIG.projectService.url}/projects`,
    {
      name,
      description,
    },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

/**
 * 清理测试数据
 * @param token 认证令牌
 * @param projectId 项目ID
 */
export const cleanupTestData = async (token: string, projectId?: string) => {
  if (projectId) {
    try {
      await axios.delete(`${TEST_CONFIG.projectService.url}/projects/${projectId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      console.error('清理项目数据失败:', error);
    }
  }
};

// 在所有测试开始前等待服务就绪
beforeAll(async () => {
  try {
    await waitForExpect(async () => {
      await Promise.all([
        waitForServiceHealth(TEST_CONFIG.serviceRegistry.url),
        waitForServiceHealth(TEST_CONFIG.userService.url),
        waitForServiceHealth(TEST_CONFIG.projectService.url),
        waitForServiceHealth(TEST_CONFIG.assetService.url),
      ]);
    }, 60000, 5000);
  } catch (error) {
    console.error('服务健康检查失败:', error);
    throw error;
  }
});
