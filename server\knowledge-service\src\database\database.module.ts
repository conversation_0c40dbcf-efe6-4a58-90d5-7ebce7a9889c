import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  DigitalHuman,
  KnowledgeBase,
  KnowledgeDocument,
  DocumentChunk,
  DigitalHumanKnowledgeBinding,
} from '../entities';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', 'password'),
        database: configService.get('DB_DATABASE', 'knowledge_service'),
        charset: 'utf8mb4',
        timezone: '+08:00',
        entities: [
          DigitalHuman,
          KnowledgeBase,
          KnowledgeDocument,
          DocumentChunk,
          DigitalHumanKnowledgeBinding,
        ],
        synchronize: process.env.NODE_ENV !== 'production',
        logging: process.env.NODE_ENV === 'development',
        retryAttempts: 3,
        retryDelay: 3000,
        autoLoadEntities: true,
        keepConnectionAlive: true,
        extra: {
          ssl: false,
          connectionLimit: 20,
          acquireTimeout: 30000,
          timeout: 10000,
        },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      DigitalHuman,
      KnowledgeBase,
      KnowledgeDocument,
      DocumentChunk,
      DigitalHumanKnowledgeBinding,
    ]),
  ],
  exports: [TypeOrmModule],
})
export class DatabaseModule {}
