/**
 * FXAA抗锯齿效果
 */
// 使用类型断言导入 ShaderPass 和 FXAAShader
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
// @ts-ignore
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * FXAA抗锯齿效果选项
 */
export interface FXAAEffectOptions extends PostProcessingEffectOptions {
  /** 边缘阈值 */
  edgeThreshold?: number;
  /** 边缘阈值最小值 */
  edgeThresholdMin?: number;
}

/**
 * FXAA抗锯齿效果
 */
export class FXAAEffect extends PostProcessingEffect {
  /** 边缘阈值 */
  private edgeThreshold: number;

  /** 边缘阈值最小值 */
  private edgeThresholdMin: number;

  /** FXAA通道 */
  private fxaaPass: ShaderPass | null = null;

  /**
   * 创建FXAA抗锯齿效果
   * @param options FXAA抗锯齿效果选项
   */
  constructor(options: FXAAEffectOptions = { name: 'FXAA' }) {
    super(options);

    this.edgeThreshold = options.edgeThreshold || 0.125;
    this.edgeThresholdMin = options.edgeThresholdMin || 0.0312;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 创建FXAA通道
    this.fxaaPass = new ShaderPass(FXAAShader);

    // 设置通道参数
    this.updateUniforms();

    // 设置通道
    this.pass = this.fxaaPass;
  }

  /**
   * 更新Uniforms
   */
  private updateUniforms(): void {
    if (!this.fxaaPass) return;

    // 使用类型断言处理 uniforms 类型问题
    const uniforms = this.fxaaPass.material.uniforms;

    // 设置分辨率
    if (uniforms.resolution && uniforms.resolution.value) {
      uniforms.resolution.value.set(1 / this.width, 1 / this.height);
    }

    // 设置边缘阈值
    if (uniforms.edgeThreshold) {
      uniforms.edgeThreshold.value = this.edgeThreshold;
    }

    // 设置边缘阈值最小值
    if (uniforms.edgeThresholdMin) {
      uniforms.edgeThresholdMin.value = this.edgeThresholdMin;
    }
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    super.resize(width, height);

    // 更新分辨率
    this.updateUniforms();
  }

  /**
   * 设置边缘阈值
   * @param threshold 边缘阈值
   */
  public setEdgeThreshold(threshold: number): void {
    this.edgeThreshold = threshold;

    // 更新Uniforms
    if (this.fxaaPass && this.fxaaPass.material && this.fxaaPass.material.uniforms) {
      const uniforms = this.fxaaPass.material.uniforms;
      if (uniforms.edgeThreshold) {
        uniforms.edgeThreshold.value = threshold;
      }
    }
  }

  /**
   * 获取边缘阈值
   * @returns 边缘阈值
   */
  public getEdgeThreshold(): number {
    return this.edgeThreshold;
  }

  /**
   * 设置边缘阈值最小值
   * @param threshold 边缘阈值最小值
   */
  public setEdgeThresholdMin(threshold: number): void {
    this.edgeThresholdMin = threshold;

    // 更新Uniforms
    if (this.fxaaPass && this.fxaaPass.material && this.fxaaPass.material.uniforms) {
      const uniforms = this.fxaaPass.material.uniforms;
      if (uniforms.edgeThresholdMin) {
        uniforms.edgeThresholdMin.value = threshold;
      }
    }
  }

  /**
   * 获取边缘阈值最小值
   * @returns 边缘阈值最小值
   */
  public getEdgeThresholdMin(): number {
    return this.edgeThresholdMin;
  }

  /**
   * 更新效果
   * @param _deltaTime 帧间隔时间（秒）- 未使用，保留以符合接口
   */
  public update(_deltaTime: number): void {
    // FXAA 效果不需要每帧更新
  }
}
