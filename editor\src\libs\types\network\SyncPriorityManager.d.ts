import type { Entity } from '../core/Entity';
import { Vector3 } from '../math/Vector3';
/**
 * 同步优先级类型
 */
export declare enum SyncPriorityType {
    /** 距离优先级 */
    DISTANCE = "distance",
    /** 视野优先级 */
    VISIBILITY = "visibility",
    /** 重要性优先级 */
    IMPORTANCE = "importance",
    /** 活动优先级 */
    ACTIVITY = "activity",
    /** 自定义优先级 */
    CUSTOM = "custom"
}
/**
 * 同步优先级配置
 */
export interface SyncPriorityConfig {
    /** 是否启用距离优先级 */
    useDistancePriority?: boolean;
    /** 距离优先级权重 */
    distancePriorityWeight?: number;
    /** 最大距离（超过此距离的实体优先级为0） */
    maxDistance?: number;
    /** 是否启用视野优先级 */
    useVisibilityPriority?: boolean;
    /** 视野优先级权重 */
    visibilityPriorityWeight?: number;
    /** 视野角度（度） */
    visibilityAngle?: number;
    /** 是否启用重要性优先级 */
    useImportancePriority?: boolean;
    /** 重要性优先级权重 */
    importancePriorityWeight?: number;
    /** 是否启用活动优先级 */
    useActivityPriority?: boolean;
    /** 活动优先级权重 */
    activityPriorityWeight?: number;
    /** 活动检测阈值 */
    activityThreshold?: number;
    /** 活动衰减时间（毫秒） */
    activityDecayTime?: number;
    /** 是否启用自定义优先级 */
    useCustomPriority?: boolean;
    /** 自定义优先级权重 */
    customPriorityWeight?: number;
    /** 自定义优先级计算函数 */
    customPriorityFunction?: (entity: Entity, observerPosition: Vector3) => number;
    /** 优先级更新间隔（毫秒） */
    priorityUpdateInterval?: number;
    /** 优先级范围（0-1） */
    priorityRange?: [number, number];
    /** 是否启用自适应同步频率 */
    useAdaptiveSync?: boolean;
    /** 最小同步间隔（毫秒） */
    minSyncInterval?: number;
    /** 最大同步间隔（毫秒） */
    maxSyncInterval?: number;
    /** 基准同步间隔（毫秒） */
    baseSyncInterval?: number;
    /** 优先级对同步间隔的影响系数（0-1） */
    priorityToIntervalFactor?: number;
}
/**
 * 同步优先级管理器
 */
export declare class SyncPriorityManager {
    /** 配置 */
    private config;
    /** 本地用户ID */
    private localUserId;
    /** 观察者位置 */
    private observerPosition;
    /** 观察者方向 */
    private observerDirection;
    /** 实体优先级状态映射表 */
    private entityPriorityStates;
    /** 优先级更新计时器 */
    private priorityUpdateTimer;
    /** 是否正在更新优先级 */
    private isUpdatingPriority;
    /**
     * 创建同步优先级管理器
     * @param config 配置
     */
    constructor(config?: SyncPriorityConfig);
    /**
     * 初始化管理器
     * @param localUserId 本地用户ID
     */
    initialize(localUserId: string): void;
    /**
     * 设置观察者位置和方向
     * @param position 位置
     * @param direction 方向
     */
    setObserverTransform(position: Vector3, direction: Vector3): void;
    /**
     * 注册实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    registerEntity(entityId: string, entity: Entity): void;
    /**
     * 注销实体
     * @param entityId 实体ID
     */
    unregisterEntity(entityId: string): void;
    /**
     * 更新
     * @param deltaTime 时间增量（毫秒）
     * @param entities 实体映射表
     */
    update(deltaTime: number, entities: Map<string, Entity>): void;
    /**
     * 更新所有实体的优先级
     * @param entities 实体映射表
     */
    private updateAllPriorities;
    /**
     * 更新实体优先级
     * @param entityId 实体ID
     * @param entity 实体
     */
    private updateEntityPriority;
    /**
     * 计算推荐同步间隔
     * @param priorityState 优先级状态
     */
    private calculateRecommendedSyncInterval;
    /**
     * 计算距离优先级
     * @param position 实体位置
     * @returns 距离优先级（0-1）
     */
    private calculateDistancePriority;
    /**
     * 计算视野优先级
     * @param position 实体位置
     * @returns 视野优先级（0-1）
     */
    private calculateVisibilityPriority;
    /**
     * 计算活动优先级
     * @param position 实体位置
     * @param priorityState 优先级状态
     * @returns 活动优先级（0-1）
     */
    private calculateActivityPriority;
    /**
     * 计算总优先级
     * @param priorityState 优先级状态
     * @returns 总优先级（0-1）
     */
    private calculateTotalPriority;
    /**
     * 获取实体优先级
     * @param entityId 实体ID
     * @returns 优先级（0-1）
     */
    getEntityPriority(entityId: string): number;
    /**
     * 获取实体详细优先级状态
     * @param entityId 实体ID
     * @returns 优先级状态
     */
    getEntityPriorityState(entityId: string): any;
    /**
     * 获取实体推荐同步间隔
     * @param entityId 实体ID
     * @returns 推荐同步间隔（毫秒）
     */
    getEntityRecommendedSyncInterval(entityId: string): number;
    /**
     * 获取所有实体的优先级
     * @returns 实体优先级映射表
     */
    getAllEntityPriorities(): Map<string, number>;
    /**
     * 销毁管理器
     */
    dispose(): void;
}
