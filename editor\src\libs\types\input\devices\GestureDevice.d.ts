/**
 * 手势识别设备
 * 用于识别用户的手势输入
 */
import { BaseInputDevice } from '../InputDevice';
/**
 * 手势类型
 */
export declare enum GestureType {
    /** 点击 */
    TAP = "tap",
    /** 双击 */
    DOUBLE_TAP = "doubleTap",
    /** 长按 */
    LONG_PRESS = "longPress",
    /** 滑动 */
    SWIPE = "swipe",
    /** 捏合 */
    PINCH = "pinch",
    /** 旋转 */
    ROTATE = "rotate",
    /** 平移 */
    PAN = "pan"
}
/**
 * 手势方向
 */
export declare enum GestureDirection {
    /** 无方向 */
    NONE = "none",
    /** 上 */
    UP = "up",
    /** 下 */
    DOWN = "down",
    /** 左 */
    LEFT = "left",
    /** 右 */
    RIGHT = "right"
}
/**
 * 手势状态
 */
export declare enum GestureState {
    /** 开始 */
    BEGIN = "begin",
    /** 更新 */
    UPDATE = "update",
    /** 结束 */
    END = "end"
}
/**
 * 手势事件数据
 */
export interface GestureEventData {
    /** 手势类型 */
    type: GestureType;
    /** 手势状态 */
    state: GestureState;
    /** 手势方向 */
    direction: GestureDirection;
    /** 手势位置X */
    x: number;
    /** 手势位置Y */
    y: number;
    /** 手势缩放比例（捏合手势） */
    scale?: number;
    /** 手势旋转角度（旋转手势） */
    rotation?: number;
    /** 手势速度 */
    velocity?: number;
    /** 手势持续时间（毫秒） */
    duration?: number;
    /** 原始事件 */
    originalEvent?: Event;
}
/**
 * 手势识别器选项
 */
export interface GestureRecognizerOptions {
    /** 目标元素 */
    element?: HTMLElement;
    /** 是否阻止默认行为 */
    preventDefault?: boolean;
    /** 是否阻止事件传播 */
    stopPropagation?: boolean;
    /** 长按阈值（毫秒） */
    longPressThreshold?: number;
    /** 双击阈值（毫秒） */
    doubleTapThreshold?: number;
    /** 滑动阈值（像素） */
    swipeThreshold?: number;
    /** 滑动速度阈值（像素/毫秒） */
    swipeVelocityThreshold?: number;
}
/**
 * 手势识别设备
 */
export declare class GestureDevice extends BaseInputDevice {
    /** 目标元素 */
    private element;
    /** 是否阻止默认行为 */
    private preventDefault;
    /** 是否阻止事件传播 */
    private stopPropagation;
    /** 长按阈值（毫秒） */
    private longPressThreshold;
    /** 双击阈值（毫秒） */
    private doubleTapThreshold;
    /** 滑动阈值（像素） */
    private swipeThreshold;
    /** 滑动速度阈值（像素/毫秒） */
    private swipeVelocityThreshold;
    /** 触摸事件处理器 */
    private touchEventHandlers;
    /** 鼠标事件处理器 */
    private mouseEventHandlers;
    /** 指针事件处理器 */
    private pointerEventHandlers;
    /** 当前触摸点 */
    private touches;
    /** 上一次触摸点 */
    private previousTouches;
    /** 触摸开始时间 */
    private touchStartTime;
    /** 上一次点击时间 */
    private lastTapTime;
    /** 长按定时器 */
    private longPressTimer;
    /** 是否正在进行手势 */
    private isGesturing;
    /** 当前手势类型 */
    private currentGestureType;
    /** 手势开始位置 */
    private gestureStartPosition;
    /** 手势当前位置 */
    private gestureCurrentPosition;
    /** 手势开始时间 */
    private gestureStartTime;
    /** 手势缩放初始值 */
    private initialScale;
    /** 手势旋转初始值 */
    private initialRotation;
    /**
     * 创建手势识别设备
     * @param options 选项
     */
    constructor(options?: GestureRecognizerOptions);
    /**
     * 初始化事件处理器
     */
    private initEventHandlers;
    /**
     * 初始化设备
     */
    initialize(): void;
    /**
     * 销毁设备
     */
    destroy(): void;
    /**
     * 移除事件监听器
     */
    private removeEventListeners;
    /**
     * 处理触摸开始事件
     * @param event 触摸事件
     */
    private handleTouchStart;
    /**
     * 处理触摸移动事件
     * @param event 触摸事件
     */
    private handleTouchMove;
    /**
     * 处理触摸结束事件
     * @param event 触摸事件
     */
    private handleTouchEnd;
    /**
     * 处理触摸取消事件
     * @param event 触摸事件
     */
    private handleTouchCancel;
    /**
     * 处理鼠标按下事件
     * @param event 鼠标事件
     */
    private handleMouseDown;
    /**
     * 处理鼠标移动事件
     * @param event 鼠标事件
     */
    private handleMouseMove;
    /**
     * 处理鼠标释放事件
     * @param event 鼠标事件
     */
    private handleMouseUp;
    /**
     * 处理指针按下事件
     * @param event 指针事件
     */
    private handlePointerDown;
    /**
     * 处理指针移动事件
     * @param event 指针事件
     */
    private handlePointerMove;
    /**
     * 处理指针释放事件
     * @param event 指针事件
     */
    private handlePointerUp;
    /**
     * 处理指针取消事件
     * @param event 指针事件
     */
    private handlePointerCancel;
    /**
     * 启动长按定时器
     * @param touch 触摸点
     */
    private startLongPressTimer;
    /**
     * 检测滑动手势
     */
    private detectSwipeGesture;
    /**
     * 获取滑动方向
     * @returns 滑动方向
     */
    private getSwipeDirection;
    /**
     * 处理多点触摸开始
     * @param event 触摸事件
     */
    private handleMultiTouchStart;
    /**
     * 处理多点触摸移动
     * @param event 触摸事件
     */
    private handleMultiTouchMove;
    /**
     * 处理多点触摸结束
     * @param event 触摸事件
     */
    private handleMultiTouchEnd;
    /**
     * 触发手势事件
     * @param type 手势类型
     * @param state 手势状态
     * @param direction 手势方向
     * @param x X坐标
     * @param y Y坐标
     * @param scale 缩放比例
     * @param rotation 旋转角度
     * @param velocity 速度
     * @param originalEvent 原始事件
     */
    private triggerGestureEvent;
    /**
     * 添加事件监听器
     */
    private addEventListeners;
}
