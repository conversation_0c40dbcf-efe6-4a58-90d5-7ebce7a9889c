/**
 * 服务缓存服务
 */
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServiceEntity } from '../entities/service.entity';
import { ServiceInstanceEntity } from '../entities/service-instance.entity';
// import { EventBusService } from '@shared/event-bus';
// import { SERVICE_REGISTERED, SERVICE_DEREGISTERED, SERVICE_HEALTH_CHANGED } from '@shared/event-bus/events';
import { Redis } from 'ioredis';

/**
 * 缓存级别
 */
export enum CacheLevel {
  /** 内存缓存 */
  MEMORY = 'memory',
  /** Redis缓存 */
  REDIS = 'redis',
}

/**
 * 缓存条目
 */
interface CacheEntry<T> {
  /** 数据 */
  data: T;
  /** 过期时间 */
  expireAt: number;
  /** 最后访问时间 */
  lastAccessed: number;
  /** 访问次数 */
  accessCount: number;
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  /** 缓存命中次数 */
  hits: number;
  /** 缓存未命中次数 */
  misses: number;
  /** 缓存条目数量 */
  size: number;
  /** 缓存命中率 */
  hitRate: number;
  /** 平均访问时间（毫秒） */
  avgAccessTime: number;
  /** 缓存使用内存（字节） */
  memoryUsage: number;
}

/**
 * 服务缓存配置
 */
export interface ServiceCacheConfig {
  /** 是否启用缓存 */
  enabled: boolean;
  /** 缓存级别 */
  levels: CacheLevel[];
  /** 内存缓存TTL（毫秒） */
  memoryTtl: number;
  /** Redis缓存TTL（毫秒） */
  redisTtl: number;
  /** 最大缓存条目数 */
  maxEntries: number;
  /** Redis连接配置 */
  redis?: {
    /** 主机 */
    host: string;
    /** 端口 */
    port: number;
    /** 密码 */
    password?: string;
    /** 数据库索引 */
    db?: number;
    /** 键前缀 */
    keyPrefix?: string;
  };
}

/**
 * 服务缓存服务
 */
@Injectable()
export class ServiceCacheService implements OnModuleInit {
  private readonly logger = new Logger(ServiceCacheService.name);
  private readonly config: ServiceCacheConfig;
  private readonly memoryCache = new Map<string, CacheEntry<any>>();
  private redisClient: Redis;
  private readonly stats: CacheStats = {
    hits: 0,
    misses: 0,
    size: 0,
    hitRate: 0,
    avgAccessTime: 0,
    memoryUsage: 0,
  };
  private totalAccessTime = 0;
  
  constructor(
    private readonly configService: ConfigService,
    // private readonly eventBusService: EventBusService,
  ) {
    // 加载配置
    this.config = {
      enabled: this.configService.get<boolean>('CACHE_ENABLED', true),
      levels: this.parseConfigLevels(this.configService.get<string>('CACHE_LEVELS', 'memory,redis')),
      memoryTtl: this.configService.get<number>('CACHE_MEMORY_TTL', 60000), // 1分钟
      redisTtl: this.configService.get<number>('CACHE_REDIS_TTL', 300000), // 5分钟
      maxEntries: this.configService.get<number>('CACHE_MAX_ENTRIES', 10000),
      redis: {
        host: this.configService.get<string>('REDIS_HOST', 'localhost'),
        port: this.configService.get<number>('REDIS_PORT', 6379),
        password: this.configService.get<string>('REDIS_PASSWORD', ''),
        db: this.configService.get<number>('REDIS_DB', 0),
        keyPrefix: this.configService.get<string>('REDIS_KEY_PREFIX', 'service-registry:'),
      },
    };
  }
  
  /**
   * 模块初始化
   */
  async onModuleInit() {
    if (!this.config.enabled) {
      this.logger.log('服务缓存已禁用');
      return;
    }
    
    // 初始化Redis客户端
    if (this.config.levels.includes(CacheLevel.REDIS)) {
      try {
        this.redisClient = new Redis({
          host: this.config.redis.host,
          port: this.config.redis.port,
          password: this.config.redis.password || undefined,
          db: this.config.redis.db,
          keyPrefix: this.config.redis.keyPrefix,
        });
        
        this.logger.log(`已连接到Redis服务器: ${this.config.redis.host}:${this.config.redis.port}`);
      } catch (error) {
        this.logger.error(`连接Redis服务器失败: ${error.message}`, error.stack);
      }
    }
    
    // 订阅事件
    // this.subscribeToEvents();
    
    this.logger.log(`服务缓存已初始化，启用级别: ${this.config.levels.join(', ')}`);
  }
  
  /**
   * 解析缓存级别配置
   * @param levelsStr 缓存级别字符串
   */
  private parseConfigLevels(levelsStr: string): CacheLevel[] {
    const levels: CacheLevel[] = [];
    
    if (!levelsStr) {
      return [CacheLevel.MEMORY];
    }
    
    const parts = levelsStr.split(',').map(p => p.trim().toLowerCase());
    
    for (const part of parts) {
      if (part === 'memory') {
        levels.push(CacheLevel.MEMORY);
      } else if (part === 'redis') {
        levels.push(CacheLevel.REDIS);
      }
    }
    
    return levels.length > 0 ? levels : [CacheLevel.MEMORY];
  }
  
  /**
   * 订阅事件
   */
  private subscribeToEvents() {
    // 暂时禁用事件订阅，因为EventBusService未配置
    /*
    // 服务注册事件
    this.eventBusService.subscribe(SERVICE_REGISTERED, async (event) => {
      this.invalidateServiceCache(event.data.name);
    });

    // 服务注销事件
    this.eventBusService.subscribe(SERVICE_DEREGISTERED, async (event) => {
      this.invalidateServiceCache(event.data.name);
    });

    // 服务健康状态变更事件
    this.eventBusService.subscribe(SERVICE_HEALTH_CHANGED, async (event) => {
      this.invalidateServiceCache(event.data.serviceName);
    });
    */
  }
  
  /**
   * 获取缓存键
   * @param key 原始键
   */
  private getCacheKey(key: string): string {
    return `service-cache:${key}`;
  }
  
  /**
   * 获取Redis缓存键
   * @param key 原始键
   */
  private getRedisKey(key: string): string {
    return `cache:${key}`;
  }
  
  /**
   * 从缓存获取数据
   * @param key 缓存键
   * @param fetchFn 获取数据的函数
   * @param ttl 过期时间（毫秒）
   */
  async get<T>(key: string, fetchFn: () => Promise<T>, ttl?: number): Promise<T> {
    if (!this.config.enabled) {
      return fetchFn();
    }
    
    const cacheKey = this.getCacheKey(key);
    const startTime = Date.now();
    
    // 尝试从内存缓存获取
    if (this.config.levels.includes(CacheLevel.MEMORY)) {
      const memoryEntry = this.memoryCache.get(cacheKey);
      
      if (memoryEntry && memoryEntry.expireAt > Date.now()) {
        // 更新访问统计
        memoryEntry.lastAccessed = Date.now();
        memoryEntry.accessCount++;
        
        // 更新缓存统计
        this.stats.hits++;
        this.totalAccessTime += Date.now() - startTime;
        this.updateHitRate();
        
        return memoryEntry.data;
      }
    }
    
    // 尝试从Redis缓存获取
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const redisKey = this.getRedisKey(key);
        const redisData = await this.redisClient.get(redisKey);
        
        if (redisData) {
          const data = JSON.parse(redisData);
          
          // 更新内存缓存
          if (this.config.levels.includes(CacheLevel.MEMORY)) {
            this.setMemoryCache(cacheKey, data, ttl || this.config.memoryTtl);
          }
          
          // 更新缓存统计
          this.stats.hits++;
          this.totalAccessTime += Date.now() - startTime;
          this.updateHitRate();
          
          return data;
        }
      } catch (error) {
        this.logger.warn(`从Redis获取缓存失败: ${error.message}`);
      }
    }
    
    // 缓存未命中，获取新数据
    this.stats.misses++;
    this.updateHitRate();
    
    try {
      const data = await fetchFn();
      
      // 更新缓存
      this.set(key, data, ttl);
      
      return data;
    } catch (error) {
      this.logger.error(`获取数据失败: ${error.message}`, error.stack);
      throw error;
    }
  }
  
  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 数据
   * @param ttl 过期时间（毫秒）
   */
  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    if (!this.config.enabled) {
      return;
    }
    
    const cacheKey = this.getCacheKey(key);
    
    // 设置内存缓存
    if (this.config.levels.includes(CacheLevel.MEMORY)) {
      this.setMemoryCache(cacheKey, data, ttl || this.config.memoryTtl);
    }
    
    // 设置Redis缓存
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const redisKey = this.getRedisKey(key);
        const redisTtl = ttl || this.config.redisTtl;
        
        await this.redisClient.set(
          redisKey,
          JSON.stringify(data),
          'PX',
          redisTtl,
        );
      } catch (error) {
        this.logger.warn(`设置Redis缓存失败: ${error.message}`);
      }
    }
  }
  
  /**
   * 设置内存缓存
   * @param key 缓存键
   * @param data 数据
   * @param ttl 过期时间（毫秒）
   */
  private setMemoryCache<T>(key: string, data: T, ttl: number): void {
    // 检查缓存大小
    if (this.memoryCache.size >= this.config.maxEntries) {
      this.evictCache();
    }
    
    // 设置缓存
    this.memoryCache.set(key, {
      data,
      expireAt: Date.now() + ttl,
      lastAccessed: Date.now(),
      accessCount: 0,
    });
    
    // 更新统计
    this.stats.size = this.memoryCache.size;
  }
  
  /**
   * 清除过期缓存
   */
  private evictCache(): void {
    const now = Date.now();
    let evicted = 0;
    
    // 首先清除过期条目
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.expireAt <= now) {
        this.memoryCache.delete(key);
        evicted++;
      }
    }
    
    // 如果仍然超过最大条目数，则按LRU策略清除
    if (this.memoryCache.size >= this.config.maxEntries) {
      // 按最后访问时间排序
      const entries = Array.from(this.memoryCache.entries())
        .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
      
      // 清除20%的条目
      const toEvict = Math.ceil(this.config.maxEntries * 0.2);
      
      for (let i = 0; i < toEvict && i < entries.length; i++) {
        this.memoryCache.delete(entries[i][0]);
        evicted++;
      }
    }
    
    this.logger.debug(`已清除 ${evicted} 个缓存条目`);
    this.stats.size = this.memoryCache.size;
  }
  
  /**
   * 使缓存失效
   * @param key 缓存键
   */
  async invalidate(key: string): Promise<void> {
    if (!this.config.enabled) {
      return;
    }
    
    const cacheKey = this.getCacheKey(key);
    
    // 清除内存缓存
    this.memoryCache.delete(cacheKey);
    
    // 清除Redis缓存
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const redisKey = this.getRedisKey(key);
        await this.redisClient.del(redisKey);
      } catch (error) {
        this.logger.warn(`清除Redis缓存失败: ${error.message}`);
      }
    }
    
    this.stats.size = this.memoryCache.size;
  }
  
  /**
   * 使服务相关缓存失效
   * @param serviceName 服务名称
   */
  async invalidateServiceCache(serviceName: string): Promise<void> {
    // 清除与服务相关的所有缓存
    const keysToInvalidate = [
      `service:${serviceName}`,
      `instances:${serviceName}`,
      `instances:${serviceName}:healthy`,
    ];
    
    for (const key of keysToInvalidate) {
      await this.invalidate(key);
    }
    
    // 清除包含服务名称的缓存键
    for (const key of this.memoryCache.keys()) {
      if (key.includes(serviceName)) {
        this.memoryCache.delete(key);
      }
    }
    
    // 清除Redis中包含服务名称的缓存键
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const pattern = this.getRedisKey(`*${serviceName}*`);
        const keys = await this.redisClient.keys(pattern);
        
        if (keys.length > 0) {
          await this.redisClient.del(...keys);
        }
      } catch (error) {
        this.logger.warn(`清除Redis服务缓存失败: ${error.message}`);
      }
    }
    
    this.stats.size = this.memoryCache.size;
    this.logger.debug(`已清除服务 ${serviceName} 的缓存`);
  }
  
  /**
   * 清除所有缓存
   */
  async clear(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }
    
    // 清除内存缓存
    this.memoryCache.clear();
    
    // 清除Redis缓存
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const pattern = this.getRedisKey('*');
        const keys = await this.redisClient.keys(pattern);
        
        if (keys.length > 0) {
          await this.redisClient.del(...keys);
        }
      } catch (error) {
        this.logger.warn(`清除Redis缓存失败: ${error.message}`);
      }
    }
    
    this.stats.size = 0;
    this.logger.log('已清除所有缓存');
  }
  
  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    // 更新内存使用情况
    this.updateMemoryUsage();
    
    return { ...this.stats };
  }
  
  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
    this.stats.avgAccessTime = total > 0 ? this.totalAccessTime / total : 0;
  }
  
  /**
   * 更新内存使用情况
   */
  private updateMemoryUsage(): void {
    try {
      // 估算内存使用量
      let size = 0;
      
      for (const [key, entry] of this.memoryCache.entries()) {
        // 键的大小
        size += key.length * 2; // 假设每个字符占2字节
        
        // 值的大小（粗略估计）
        size += JSON.stringify(entry.data).length * 2;
        
        // 元数据大小
        size += 32; // 时间戳和计数器
      }
      
      this.stats.memoryUsage = size;
    } catch (error) {
      this.logger.warn(`计算内存使用量失败: ${error.message}`);
    }
  }
}
