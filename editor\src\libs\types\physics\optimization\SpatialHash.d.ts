/**
 * 空间哈希分区
 * 用于加速碰撞检测
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { ISpatialPartitioning } from './SpatialPartitioning';
/**
 * 空间哈希配置接口
 */
export interface SpatialHashOptions {
    /** 单元大小 */
    cellSize?: number;
    /** 世界大小 */
    worldSize?: number;
    /** 世界中心 */
    worldCenter?: CANNON.Vec3;
    /** 是否使用动态哈希 */
    useDynamicHash?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 空间哈希类
 */
export declare class SpatialHash implements ISpatialPartitioning {
    /** 单元大小 */
    private cellSize;
    /** 世界大小 */
    private worldSize;
    /** 世界中心 */
    private worldCenter;
    /** 是否使用动态哈希 */
    private useDynamicHash;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 哈希表 */
    private hashTable;
    /** 物体到哈希的映射 */
    private bodyToHashes;
    /** 物体列表 */
    private bodies;
    /** 调试网格 */
    private debugMesh;
    /**
     * 创建空间哈希
     * @param options 空间哈希配置
     */
    constructor(options?: SpatialHashOptions);
    /**
     * 创建调试网格
     */
    private createDebugMesh;
    /**
     * 计算哈希值
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     * @returns 哈希值
     */
    private computeHash;
    /**
     * 获取物体占据的哈希值
     * @param body 物体
     * @returns 哈希值列表
     */
    private getBodyHashes;
    /**
     * 获取物体的AABB
     * @param body 物体
     * @returns AABB
     */
    private getBodyAABB;
    /**
     * 添加物体
     * @param body 物体
     */
    add(body: CANNON.Body): void;
    /**
     * 移除物体
     * @param body 物体
     */
    remove(body: CANNON.Body): void;
    /**
     * 更新物体
     * @param body 物体
     */
    update(body: CANNON.Body): void;
    /**
     * 比较两个哈希值集合是否相等
     * @param a 哈希值集合A
     * @param b 哈希值集合B
     * @returns 是否相等
     */
    private areHashesEqual;
    /**
     * 更新所有物体
     */
    updateAll(): void;
    /**
     * 查询区域内的物体
     * @param min 最小坐标
     * @param max 最大坐标
     * @returns 区域内的物体
     */
    queryRegion(min: CANNON.Vec3, max: CANNON.Vec3): CANNON.Body[];
    /**
     * 检查两个AABB是否重叠
     * @param minA AABB A的最小坐标
     * @param maxA AABB A的最大坐标
     * @param minB AABB B的最小坐标
     * @param maxB AABB B的最大坐标
     * @returns 是否重叠
     */
    private aabbOverlap;
    /**
     * 查询射线碰撞的物体
     * @param from 射线起点
     * @param to 射线终点
     * @returns 射线碰撞的物体
     */
    queryRay(from: CANNON.Vec3, to: CANNON.Vec3): CANNON.Body[];
    /**
     * 遍历射线经过的单元
     * @param from 射线起点
     * @param direction 射线方向
     * @param length 射线长度
     * @param callback 回调函数
     */
    private traverseRay;
    /**
     * 检查射线是否与AABB相交
     * @param from 射线起点
     * @param direction 射线方向
     * @param length 射线长度
     * @param min AABB的最小坐标
     * @param max AABB的最大坐标
     * @returns 是否相交
     */
    private rayAabbIntersect;
    /**
     * 查询球体碰撞的物体
     * @param center 球体中心
     * @param radius 球体半径
     * @returns 球体碰撞的物体
     */
    querySphere(center: CANNON.Vec3, radius: number): CANNON.Body[];
    /**
     * 检查球体是否与AABB相交
     * @param center 球体中心
     * @param radius 球体半径
     * @param min AABB的最小坐标
     * @param max AABB的最大坐标
     * @returns 是否相交
     */
    private sphereAabbIntersect;
    /**
     * 查询与物体可能碰撞的物体
     * @param body 物体
     * @returns 可能碰撞的物体
     */
    queryPotentialCollisions(body: CANNON.Body): CANNON.Body[];
    /**
     * 获取所有物体
     * @returns 所有物体
     */
    getBodies(): CANNON.Body[];
    /**
     * 获取物体数量
     * @returns 物体数量
     */
    getBodyCount(): number;
    /**
     * 清空
     */
    clear(): void;
    /**
     * 销毁
     */
    dispose(): void;
    /**
     * 获取调试信息
     * @returns 调试信息
     */
    getDebugInfo(): any;
    /**
     * 获取调试网格
     * @returns 调试网格
     */
    getDebugMesh(): THREE.Object3D;
}
