# DL（Digital Learning）引擎项目架构图 (Mermaid版)

## 1. 整体架构

```mermaid
graph TD
    [DL（Digital Learning）引擎项目] --> Engine[Engine 底层引擎]
    DL --> Editor[Editor 编辑器]
    DL --> Server[Server 服务器]
    
    Engine --> CoreSystems[核心系统]
    Editor --> UIComponents[UI组件]
    Server --> Microservices[微服务]
    
    CoreSystems --> ECS[实体组件系统]
    CoreSystems --> Rendering[渲染系统]
    CoreSystems --> Physics[物理系统]
    CoreSystems --> Animation[动画系统]
    CoreSystems --> Network[网络系统]
    
    UIComponents --> Panels[面板系统]
    UIComponents --> Viewport[视口]
    UIComponents --> Tools[工具系统]
    
    Microservices --> APIGateway[API网关]
    Microservices --> ServiceRegistry[服务注册中心]
    Microservices --> UserService[用户服务]
    Microservices --> ProjectService[项目服务]
    Microservices --> AssetService[资产服务]
```

## 2. 底层引擎(Engine)架构

```mermaid
graph TD
    Engine[Engine 底层引擎] --> Core[核心模块]
    Engine --> Rendering[渲染系统]
    Engine --> Physics[物理系统]
    Engine --> Animation[动画系统]
    Engine --> Scene[场景系统]
    Engine --> Network[网络系统]
    Engine --> Input[输入系统]
    Engine --> Particles[粒子系统]
    Engine --> Interaction[交互系统]
    Engine --> Avatar[头像系统]
    Engine --> VisualScript[视觉脚本系统]
    Engine --> Terrain[地形系统]
    
    Core --> EngineClass[Engine类]
    Core --> World[World类]
    Core --> Entity[Entity类]
    Core --> Component[Component类]
    Core --> System[System类]
    
    Rendering --> Renderer[渲染器]
    Rendering --> Camera[相机]
    Rendering --> Light[光照]
    Rendering --> Material[材质]
    Rendering --> Mesh[网格]
    Rendering --> PostProcessing[后处理]
    
    Physics --> PhysicsSystem[物理系统]
    Physics --> RigidBody[刚体]
    Physics --> Collider[碰撞体]
    Physics --> Constraints[约束]
    Physics --> SoftBody[软体]
    
    Animation --> AnimationSystem[动画系统]
    Animation --> Skeleton[骨骼]
    Animation --> StateMachine[状态机]
    Animation --> Blending[混合]
    Animation --> FacialAnimation[面部动画]
    
    Network --> NetworkSystem[网络系统]
    Network --> WebRTC[WebRTC]
    Network --> EntitySync[实体同步]
    Network --> MicroserviceClient[微服务客户端]
```

## 3. 编辑器(Editor)架构

```mermaid
graph TD
    Editor[Editor 编辑器] --> UILayer[UI层]
    Editor --> StateManagement[状态管理]
    Editor --> Services[服务层]
    Editor --> Tools[工具层]
    Editor --> EngineInterface[引擎接口层]
    Editor --> ExtensionSystem[扩展系统]
    
    UILayer --> LayoutComponents[布局组件]
    UILayer --> PanelComponents[面板组件]
    UILayer --> ViewportComponent[视口组件]
    UILayer --> ToolComponents[工具组件]
    UILayer --> DialogComponents[对话框组件]
    
    StateManagement --> ReduxStore[Redux Store]
    StateManagement --> AuthSlice[认证状态]
    StateManagement --> ProjectSlice[项目状态]
    StateManagement --> SceneSlice[场景状态]
    StateManagement --> EntitySlice[实体状态]
    StateManagement --> AssetSlice[资产状态]
    
    Services --> EngineService[引擎服务]
    Services --> ProjectService[项目服务]
    Services --> AssetService[资产服务]
    Services --> AuthService[认证服务]
    Services --> CollaborationService[协作服务]
    
    PanelComponents --> ScenePanel[场景面板]
    PanelComponents --> HierarchyPanel[层级面板]
    PanelComponents --> InspectorPanel[检查器面板]
    PanelComponents --> AssetsPanel[资产面板]
    PanelComponents --> ConsolePanel[控制台面板]
    
    ExtensionSystem --> PanelExtension[面板扩展]
    ExtensionSystem --> ToolExtension[工具扩展]
    ExtensionSystem --> ComponentEditorExtension[组件编辑器扩展]
    ExtensionSystem --> AssetTypeExtension[资产类型扩展]
```

## 4. 服务器(Server)架构

```mermaid
graph TD
    Server[Server 服务器] --> APIGateway[API网关]
    Server --> ServiceRegistry[服务注册中心]
    Server --> UserService[用户服务]
    Server --> ProjectService[项目服务]
    Server --> AssetService[资产服务]
    Server --> RenderService[渲染服务]
    Server --> GameServer[游戏服务器]
    Server --> InstanceServer[实例服务器]
    Server --> MonitoringService[监控服务]
    
    APIGateway --> RouteForwarding[路由转发]
    APIGateway --> LoadBalancing[负载均衡]
    APIGateway --> RequestFiltering[请求过滤]
    APIGateway --> CircuitBreaker[熔断器]
    APIGateway --> RateLimiter[限流器]
    
    ServiceRegistry --> ServiceRegistration[服务注册]
    ServiceRegistry --> ServiceDiscovery[服务发现]
    ServiceRegistry --> HealthCheck[健康检查]
    ServiceRegistry --> LoadBalancer[负载均衡]
    ServiceRegistry --> ServiceCache[服务缓存]
    
    UserService --> UserManagement[用户管理]
    UserService --> Authentication[认证授权]
    UserService --> PermissionManagement[权限管理]
    
    ProjectService --> ProjectManagement[项目管理]
    ProjectService --> SceneManagement[场景管理]
    ProjectService --> VersionControl[版本控制]
    ProjectService --> CollaborationManagement[协作管理]
```

## 5. 系统间通信

```mermaid
graph TD
    Editor[Editor 编辑器] <--> |EngineService| Engine[Engine 底层引擎]
    Editor <--> |HTTP/WebSocket| Server[Server 服务器]
    Engine <--> |WebSocket/WebRTC| Server
    
    Engine --> |NetworkSystem| WebRTC[WebRTC]
    Engine --> |NetworkSystem| WebSocket[WebSocket]
    Engine --> |MicroserviceClient| ServiceDiscovery[服务发现]
    
    Editor --> |ProjectService| APIGateway[API网关]
    Editor --> |AssetService| APIGateway
    Editor --> |AuthService| APIGateway
    
    APIGateway --> ServiceRegistry[服务注册中心]
    ServiceRegistry --> UserService[用户服务]
    ServiceRegistry --> ProjectService[项目服务]
    ServiceRegistry --> AssetService[资产服务]
```

## 6. 微服务架构

```mermaid
graph TD
    Client[客户端] --> APIGateway[API网关]
    APIGateway --> ServiceRegistry[服务注册中心]
    
    ServiceRegistry --> UserService[用户服务]
    ServiceRegistry --> ProjectService[项目服务]
    ServiceRegistry --> AssetService[资产服务]
    ServiceRegistry --> RenderService[渲染服务]
    ServiceRegistry --> GameServer[游戏服务器]
    ServiceRegistry --> InstanceServer[实例服务器]
    
    UserService --> UserDB[(用户数据库)]
    ProjectService --> ProjectDB[(项目数据库)]
    AssetService --> AssetDB[(资产数据库)]
    AssetService --> AssetStorage[(资产存储)]
    
    ServiceRegistry --> LoadBalancer[负载均衡器]
    LoadBalancer --> Random[随机策略]
    LoadBalancer --> RoundRobin[轮询策略]
    LoadBalancer --> WeightedRoundRobin[加权轮询]
    LoadBalancer --> LeastResponseTime[最少响应时间]
    LoadBalancer --> ConsistentHash[一致性哈希]
    LoadBalancer --> ZoneAware[区域感知]
    
    ServiceRegistry --> Cache[服务缓存]
    Cache --> MemoryCache[内存缓存]
    Cache --> RedisCache[Redis缓存]
```

## 7. 编辑器面板系统

```mermaid
graph TD
    EditorLayout[编辑器布局] --> Header[顶部菜单栏]
    EditorLayout --> Sidebar[侧边栏]
    EditorLayout --> Content[内容区域]
    EditorLayout --> PropertiesSider[属性侧边栏]
    
    Sidebar --> ScenePanel[场景面板]
    Sidebar --> HierarchyPanel[层级面板]
    Sidebar --> AssetsPanel[资产面板]
    
    Content --> Viewport[视口]
    Content --> Timeline[时间线]
    
    PropertiesSider --> InspectorPanel[检查器面板]
    PropertiesSider --> MaterialEditor[材质编辑器]
    PropertiesSider --> AnimationEditor[动画编辑器]
    
    PanelRegistry[面板注册表] --> PanelAdapter[面板适配器]
    PanelAdapter --> PanelContainer[面板容器]
    PanelContainer --> PanelContent[面板内容]
```

## 8. 引擎核心系统

```mermaid
graph TD
    Engine[引擎] --> World[世界]
    World --> EntityManager[实体管理器]
    World --> SystemManager[系统管理器]
    World --> ComponentManager[组件管理器]
    
    EntityManager --> Entity[实体]
    Entity --> Component[组件]
    
    SystemManager --> System[系统]
    System --> RenderSystem[渲染系统]
    System --> PhysicsSystem[物理系统]
    System --> AnimationSystem[动画系统]
    System --> NetworkSystem[网络系统]
    System --> InputSystem[输入系统]
    
    Engine --> EventEmitter[事件发射器]
    Engine --> ResourceManager[资源管理器]
    Engine --> TimeManager[时间管理器]
```
