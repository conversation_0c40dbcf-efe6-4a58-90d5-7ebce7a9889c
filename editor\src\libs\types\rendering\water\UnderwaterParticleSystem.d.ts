/**
 * 水下粒子系统
 * 用于实现水下气泡和悬浮物效果
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
/**
 * 粒子类型
 */
export declare enum UnderwaterParticleType {
    /** 气泡 */
    BUBBLE = "bubble",
    /** 悬浮物 */
    DEBRIS = "debris",
    /** 光束 */
    LIGHT_SHAFT = "light_shaft",
    /** 鱼群 */
    FISH_SCHOOL = "fish_school",
    /** 水草 */
    SEAWEED = "seaweed",
    /** 浮游生物 */
    PLANKTON = "plankton",
    /** 水流 */
    WATER_CURRENT = "water_current",
    /** 沉淀物 */
    SEDIMENT = "sediment",
    /** 水泡沫 */
    FOAM = "foam",
    /** 水雾 */
    MIST = "mist",
    /** 水花 */
    SPLASH = "splash",
    /** 水滴 */
    DROPLET = "droplet",
    /** 水波纹 */
    RIPPLE = "ripple",
    /** 水藻 */
    ALGAE = "algae"
}
/**
 * 粒子配置
 */
export interface UnderwaterParticleConfig {
    /** 粒子类型 */
    type: UnderwaterParticleType;
    /** 粒子数量 */
    count: number;
    /** 粒子大小 */
    size: number | [number, number];
    /** 粒子颜色 */
    color?: THREE.Color | number | string;
    /** 粒子透明度 */
    opacity?: number;
    /** 粒子生命周期 */
    lifetime?: number | [number, number];
    /** 粒子速度 */
    speed?: number | [number, number];
    /** 粒子加速度 */
    acceleration?: THREE.Vector3;
    /** 粒子旋转 */
    rotation?: boolean;
    /** 粒子旋转速度 */
    rotationSpeed?: number | [number, number];
    /** 粒子贴图 */
    texture?: THREE.Texture | string;
    /** 粒子混合模式 */
    blending?: THREE.Blending;
    /** 粒子发射区域 */
    emissionArea?: {
        /** 形状 */
        shape: 'box' | 'sphere' | 'cylinder';
        /** 尺寸 */
        size: THREE.Vector3 | number;
        /** 位置 */
        position?: THREE.Vector3;
    };
    /** 自定义着色器 */
    customShader?: {
        /** 顶点着色器 */
        vertex?: string;
        /** 片段着色器 */
        fragment?: string;
        /** 统一变量 */
        uniforms?: {
            [key: string]: {
                value: any;
            };
        };
    };
}
/**
 * 水下粒子系统配置
 */
export interface UnderwaterParticleSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 最大粒子数量 */
    maxParticles?: number;
    /** 是否使用GPU加速 */
    useGPU?: boolean;
    /** 是否自动调整性能 */
    autoAdjustPerformance?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 水下粒子系统
 */
export declare class UnderwaterParticleSystem extends System {
    /** 配置 */
    private config;
    /** 粒子组 */
    private particleGroups;
    /** 粒子配置 */
    private particleConfigs;
    /** 粒子几何体 */
    private particleGeometries;
    /** 粒子材质 */
    private particleMaterials;
    /** 粒子属性 */
    private particleAttributes;
    /** 场景 */
    private scene;
    /** 相机 */
    private _camera;
    /** 水体组件 */
    private waterBodies;
    /** 是否初始化 */
    private initialized;
    /** 时间 */
    private time;
    /** 性能监视器 */
    private performanceMonitor;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: UnderwaterParticleSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 设置场景和相机
     * @param scene 场景
     * @param camera 相机
     */
    setSceneAndCamera(scene: THREE.Scene, camera: THREE.Camera): void;
    /**
     * 添加水体
     * @param entity 实体
     * @param waterBody 水体组件
     */
    addWaterBody(entity: Entity, waterBody: WaterBodyComponent): void;
    /**
     * 移除水体
     * @param entityId 实体ID
     */
    removeWaterBody(entityId: string): void;
    /**
     * 为水体创建默认粒子
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    private createDefaultParticlesForWaterBody;
    /**
     * 创建海洋粒子
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    private createOceanParticles;
    /**
     * 创建湖泊粒子
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    private createLakeParticles;
    /**
     * 创建河流粒子
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    private createRiverParticles;
    /**
     * 创建地下湖泊粒子
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    private createUndergroundLakeParticles;
    /**
     * 创建地下河流粒子
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    private createUndergroundRiverParticles;
    /**
     * 创建温泉粒子
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    private createHotSpringParticles;
    /**
     * 创建通用水体粒子
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    private createGenericWaterParticles;
    /**
     * 添加粒子组
     * @param entityId 实体ID
     * @param groupId 组ID
     * @param config 粒子配置
     */
    addParticleGroup(entityId: string, groupId: string, config: UnderwaterParticleConfig): void;
    /**
     * 初始化粒子属性
     */
    private initializeParticleAttributes;
    /**
     * 在指定区域生成随机位置
     */
    private generateRandomPositionInArea;
    /**
     * 从粒子ID获取水体组件
     */
    private getWaterBodyFromParticleId;
    /**
     * 移除粒子组
     * @param entityId 实体ID
     * @param groupId 组ID
     */
    removeParticleGroup(entityId: string, groupId: string): void;
    /**
     * 移除水体相关的粒子
     * @param entityId 实体ID
     */
    private removeParticlesForWaterBody;
    /**
     * 更新
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;
    /**
     * 更新粒子组
     * @param particleId 粒子ID
     * @param particles 粒子对象
     * @param deltaTime 时间增量
     */
    private updateParticleGroup;
    /**
     * 重置粒子
     */
    private resetParticle;
    /**
     * 更新性能监视器
     * @param _deltaTime 时间增量
     */
    private updatePerformanceMonitor;
    /**
     * 自动调整性能
     */
    private adjustPerformance;
}
