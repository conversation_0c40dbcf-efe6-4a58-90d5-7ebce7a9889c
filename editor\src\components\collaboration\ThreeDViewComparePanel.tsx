/**
 * 3D视图比较面板组件
 * 用于在3D视图中比较两个场景版本
 */
import React, { useEffect, useRef, useState } from 'react';
import {
  Card,
  Space,
  Button,
  Divider,
  Typography,
  Tooltip,
  Switch,
  Radio,
  message
} from 'antd';
import {
  DiffOutlined,
  CloseOutlined,
  SyncOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  HighlightOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  CameraOutlined
} from '@ant-design/icons';
// import { useTranslation } from 'react-i18next'; // 暂时注释掉未使用的导入
import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import './ThreeDViewComparePanel.less';

const { Text } = Typography;

// 组件属性接口
interface ThreeDViewComparePanelProps {
  version1: any;
  version2: any;
  comparisonResult: any;
  onClose: () => void;
}

/**
 * 3D视图比较面板组件
 */
const ThreeDViewComparePanel: React.FC<ThreeDViewComparePanelProps> = ({
  version1,
  version2,
  comparisonResult,
  onClose
}) => {
  // const { t } = useTranslation(); // 暂时注释掉未使用的翻译
  const containerRef = useRef<HTMLDivElement>(null);
  const leftViewRef = useRef<HTMLDivElement>(null);
  const rightViewRef = useRef<HTMLDivElement>(null);

  // 场景和渲染器
  const [, setLeftScene] = useState<THREE.Scene | null>(null);
  const [, setRightScene] = useState<THREE.Scene | null>(null);
  const [leftRenderer, setLeftRenderer] = useState<THREE.WebGLRenderer | null>(null);
  const [rightRenderer, setRightRenderer] = useState<THREE.WebGLRenderer | null>(null);
  const [leftCamera, setLeftCamera] = useState<THREE.PerspectiveCamera | null>(null);
  const [rightCamera, setRightCamera] = useState<THREE.PerspectiveCamera | null>(null);
  const [leftControls, setLeftControls] = useState<OrbitControls | null>(null);
  const [rightControls, setRightControls] = useState<OrbitControls | null>(null);

  // 视图设置
  const [viewMode, setViewMode] = useState<'side-by-side' | 'overlay'>('side-by-side');
  const [syncCameras, setSyncCameras] = useState<boolean>(true);
  const [highlightDifferences, setHighlightDifferences] = useState<boolean>(true);
  const [highlightIntensity] = useState<number>(0.8);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [showGrid] = useState<boolean>(true);
  const [showAxes] = useState<boolean>(true);
  
  // 初始化3D视图
  useEffect(() => {
    if (!leftViewRef.current || !rightViewRef.current) return;
    
    // 创建场景
    const leftSceneObj = new THREE.Scene();
    const rightSceneObj = new THREE.Scene();
    
    // 设置背景色
    leftSceneObj.background = new THREE.Color(0xf0f0f0);
    rightSceneObj.background = new THREE.Color(0xf0f0f0);
    
    // 创建相机
    const leftCameraObj = new THREE.PerspectiveCamera(
      75,
      leftViewRef.current.clientWidth / leftViewRef.current.clientHeight,
      0.1,
      1000
    );
    const rightCameraObj = new THREE.PerspectiveCamera(
      75,
      rightViewRef.current.clientWidth / rightViewRef.current.clientHeight,
      0.1,
      1000
    );

    // 设置相机位置
    leftCameraObj.position.set(5, 5, 5);
    rightCameraObj.position.set(5, 5, 5);
    
    // 创建渲染器
    const leftRendererObj = new THREE.WebGLRenderer({ antialias: true });
    const rightRendererObj = new THREE.WebGLRenderer({ antialias: true });
    
    // 设置渲染器大小
    leftRendererObj.setSize(leftViewRef.current.clientWidth, leftViewRef.current.clientHeight);
    rightRendererObj.setSize(rightViewRef.current.clientWidth, rightViewRef.current.clientHeight);
    
    // 添加渲染器到DOM
    leftViewRef.current.appendChild(leftRendererObj.domElement);
    rightViewRef.current.appendChild(rightRendererObj.domElement);
    
    // 创建控制器
    const leftControlsObj = new OrbitControls(leftCameraObj, leftRendererObj.domElement);
    const rightControlsObj = new OrbitControls(rightCameraObj, rightRendererObj.domElement);

    // 添加网格和坐标轴
    const gridHelper = new THREE.GridHelper(10, 10);
    const axesHelper = new THREE.AxesHelper(5);

    leftSceneObj.add(gridHelper.clone());
    leftSceneObj.add(axesHelper.clone());
    rightSceneObj.add(gridHelper.clone());
    rightSceneObj.add(axesHelper.clone());

    // 添加灯光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight.position.set(10, 10, 10);

    leftSceneObj.add(ambientLight.clone());
    leftSceneObj.add(directionalLight.clone());
    rightSceneObj.add(ambientLight.clone());
    rightSceneObj.add(directionalLight.clone());

    // 保存场景和渲染器
    setLeftScene(leftSceneObj);
    setRightScene(rightSceneObj);
    setLeftRenderer(leftRendererObj);
    setRightRenderer(rightRendererObj);
    setLeftCamera(leftCameraObj);
    setRightCamera(rightCameraObj);
    setLeftControls(leftControlsObj);
    setRightControls(rightControlsObj);
    
    // 加载场景数据
    loadSceneData(leftSceneObj, version1?.snapshot);
    loadSceneData(rightSceneObj, version2?.snapshot);
    
    // 高亮差异
    if (highlightDifferences) {
      highlightDifferencesInScene(leftSceneObj, rightSceneObj, comparisonResult);
    }
    
    // 动画循环
    const animate = () => {
      requestAnimationFrame(animate);

      // 同步相机
      if (syncCameras) {
        rightCameraObj.position.copy(leftCameraObj.position);
        rightCameraObj.quaternion.copy(leftCameraObj.quaternion);
        rightControlsObj.target.copy(leftControlsObj.target);
        rightControlsObj.update();
      }

      // 渲染场景
      leftRendererObj.render(leftSceneObj, leftCameraObj);
      rightRendererObj.render(rightSceneObj, rightCameraObj);
    };
    
    animate();
    
    // 处理窗口大小变化
    const handleResize = () => {
      if (!leftViewRef.current || !rightViewRef.current) return;

      // 更新相机
      leftCameraObj.aspect = leftViewRef.current.clientWidth / leftViewRef.current.clientHeight;
      rightCameraObj.aspect = rightViewRef.current.clientWidth / rightViewRef.current.clientHeight;
      leftCameraObj.updateProjectionMatrix();
      rightCameraObj.updateProjectionMatrix();

      // 更新渲染器
      leftRendererObj.setSize(leftViewRef.current.clientWidth, leftViewRef.current.clientHeight);
      rightRendererObj.setSize(rightViewRef.current.clientWidth, rightViewRef.current.clientHeight);
    };
    
    window.addEventListener('resize', handleResize);
    
    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      
      if (leftViewRef.current) {
        leftViewRef.current.removeChild(leftRendererObj.domElement);
      }
      
      if (rightViewRef.current) {
        rightViewRef.current.removeChild(rightRendererObj.domElement);
      }
      
      leftRendererObj.dispose();
      rightRendererObj.dispose();
    };
  }, []);
  
  // 加载场景数据
  const loadSceneData = (scene: THREE.Scene, data: any) => {
    if (!data || !data.entities) {
      console.warn('没有场景数据可加载');
      return;
    }
    
    try {
      // 清除现有实体
      while (scene.children.length > 0) {
        scene.remove(scene.children[0]);
      }
      
      // 添加网格和坐标轴
      if (showGrid) {
        scene.add(new THREE.GridHelper(10, 10));
      }
      
      if (showAxes) {
        scene.add(new THREE.AxesHelper(5));
      }
      
      // 添加灯光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
      directionalLight.position.set(10, 10, 10);
      
      scene.add(ambientLight);
      scene.add(directionalLight);
      
      // 加载实体
      for (const entityId in data.entities) {
        const entityData = data.entities[entityId];
        const entity = createEntityFromData(entityData);
        
        if (entity) {
          entity.userData.id = entityId;
          scene.add(entity);
        }
      }
      
      console.log('场景数据加载完成');
    } catch (error) {
      console.error('加载场景数据时出错:', error);
    }
  };
  
  // 从数据创建实体
  const createEntityFromData = (data: any) => {
    if (!data || !data.components) {
      return null;
    }
    
    // 创建一个组作为实体容器
    const entity = new THREE.Group();
    
    // 设置名称
    if (data.name) {
      entity.name = data.name;
    }
    
    // 处理变换组件
    if (data.components.transform) {
      const transform = data.components.transform;
      
      if (transform.position) {
        entity.position.set(
          transform.position.x || 0,
          transform.position.y || 0,
          transform.position.z || 0
        );
      }
      
      if (transform.rotation) {
        entity.rotation.set(
          transform.rotation.x || 0,
          transform.rotation.y || 0,
          transform.rotation.z || 0
        );
      }
      
      if (transform.scale) {
        entity.scale.set(
          transform.scale.x || 1,
          transform.scale.y || 1,
          transform.scale.z || 1
        );
      }
    }
    
    // 处理网格渲染器组件
    if (data.components.meshRenderer) {
      const meshRenderer = data.components.meshRenderer;
      
      // 创建一个简单的立方体作为示例
      // 在实际应用中，应该根据网格数据创建更复杂的几何体
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      
      // 创建材质
      const material = new THREE.MeshStandardMaterial({
        color: meshRenderer.material?.color || 0xffffff,
        metalness: meshRenderer.material?.metalness || 0,
        roughness: meshRenderer.material?.roughness || 1,
        emissive: meshRenderer.material?.emissive || 0x000000
      });
      
      // 创建网格
      const mesh = new THREE.Mesh(geometry, material);
      entity.add(mesh);
    }
    
    return entity;
  };
  
  // 高亮场景中的差异
  const highlightDifferencesInScene = (scene1: THREE.Scene, scene2: THREE.Scene, comparisonResult: any) => {
    if (!comparisonResult) {
      return;
    }
    
    const { added, removed, modified } = comparisonResult;
    
    // 高亮添加的实体
    if (added && added.length > 0) {
      added.forEach((item: any) => {
        const entityId = item.id;
        const entity = findEntityById(scene2, entityId);
        
        if (entity) {
          highlightEntity(entity, 'added');
        }
      });
    }
    
    // 高亮删除的实体
    if (removed && removed.length > 0) {
      removed.forEach((item: any) => {
        const entityId = item.id;
        const entity = findEntityById(scene1, entityId);
        
        if (entity) {
          highlightEntity(entity, 'removed');
        }
      });
    }
    
    // 高亮修改的实体
    if (modified && modified.length > 0) {
      modified.forEach((item: any) => {
        const entityId = item.id;
        const entity1 = findEntityById(scene1, entityId);
        const entity2 = findEntityById(scene2, entityId);
        
        if (entity1) {
          highlightEntity(entity1, 'modified');
        }
        
        if (entity2) {
          highlightEntity(entity2, 'modified');
        }
      });
    }
  };
  
  // 查找实体
  const findEntityById = (scene: THREE.Scene, id: string) => {
    let found: THREE.Object3D | null = null;
    
    scene.traverse((object) => {
      if (object.userData.id === id) {
        found = object;
      }
    });
    
    return found;
  };
  
  // 高亮实体
  const highlightEntity = (entity: THREE.Object3D, type: 'added' | 'removed' | 'modified') => {
    // 创建高亮材质
    const highlightMaterial = new THREE.MeshBasicMaterial({
      color: type === 'added' ? 0x00ff00 : type === 'removed' ? 0xff0000 : 0x0000ff,
      transparent: true,
      opacity: highlightIntensity,
      wireframe: false
    });
    
    // 遍历实体的所有子对象
    entity.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // 保存原始材质
        child.userData.originalMaterial = child.material;
        
        // 应用高亮材质
        child.material = highlightMaterial;
      }
    });
  };
  
  // 切换全屏
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };
  
  // 重置视图
  const resetView = () => {
    if (leftCamera && rightCamera && leftControls && rightControls) {
      leftCamera.position.set(5, 5, 5);
      leftCamera.lookAt(0, 0, 0);
      leftControls.target.set(0, 0, 0);
      leftControls.update();

      if (syncCameras) {
        rightCamera.position.copy(leftCamera.position);
        rightCamera.lookAt(0, 0, 0);
        rightControls.target.set(0, 0, 0);
        rightControls.update();
      }
    }
  };
  
  // 拍摄快照
  const takeSnapshot = () => {
    if (leftRenderer && rightRenderer) {
      // 创建链接
      const leftLink = document.createElement('a');
      leftLink.download = `version1_snapshot_${Date.now()}.png`;
      leftLink.href = leftRenderer.domElement.toDataURL('image/png');
      leftLink.click();
      
      const rightLink = document.createElement('a');
      rightLink.download = `version2_snapshot_${Date.now()}.png`;
      rightLink.href = rightRenderer.domElement.toDataURL('image/png');
      rightLink.click();
      
      message.success('已保存场景快照');
    }
  };
  
  return (
    <div className={`three-d-view-compare-panel ${isFullscreen ? 'fullscreen' : ''}`}>
      <Card
        title={
          <Space>
            <DiffOutlined />
            <span>3D视图比较</span>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="重置视图">
              <Button 
                type="text" 
                icon={<SyncOutlined />} 
                onClick={resetView}
              />
            </Tooltip>
            <Tooltip title="拍摄快照">
              <Button
                type="text"
                icon={<CameraOutlined />}
                onClick={takeSnapshot}
              />
            </Tooltip>
            <Tooltip title={isFullscreen ? "退出全屏" : "全屏"}>
              <Button 
                type="text" 
                icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />} 
                onClick={toggleFullscreen}
              />
            </Tooltip>
            <Button 
              type="text" 
              icon={<CloseOutlined />} 
              onClick={onClose}
            />
          </Space>
        }
        className="compare-card"
      >
        <div className="view-settings">
          <Space>
            <Text>视图模式:</Text>
            <Radio.Group 
              value={viewMode} 
              onChange={(e) => setViewMode(e.target.value)}
              buttonStyle="solid"
            >
              <Radio.Button value="side-by-side">并排视图</Radio.Button>
              <Radio.Button value="overlay">叠加视图</Radio.Button>
            </Radio.Group>
            
            <Divider type="vertical" />
            
            <Tooltip title="同步相机">
              <Switch
                checked={syncCameras}
                onChange={setSyncCameras}
                checkedChildren={<EyeOutlined />}
                unCheckedChildren={<EyeInvisibleOutlined />}
              />
            </Tooltip>
            
            <Tooltip title="高亮差异">
              <Switch 
                checked={highlightDifferences} 
                onChange={setHighlightDifferences} 
                checkedChildren={<HighlightOutlined />}
                unCheckedChildren={<HighlightOutlined />}
              />
            </Tooltip>
          </Space>
        </div>
        
        <div className="view-container" ref={containerRef}>
          <div className="view-column">
            <div className="view-header">
              <Text strong>版本1: {version1?.description}</Text>
            </div>
            <div className="view-content" ref={leftViewRef}></div>
          </div>
          
          <div className="view-column">
            <div className="view-header">
              <Text strong>版本2: {version2?.description}</Text>
            </div>
            <div className="view-content" ref={rightViewRef}></div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ThreeDViewComparePanel;
