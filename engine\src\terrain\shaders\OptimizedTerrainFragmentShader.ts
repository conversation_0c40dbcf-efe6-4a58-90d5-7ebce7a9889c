/**
 * 优化的地形片段着色器
 * 用于提高地形渲染性能
 */
export const optimizedTerrainFragmentShader = `
// 精度
precision highp float;
precision highp int;

// 输入变量
varying vec2 vUv;
varying vec3 vNormal;
varying vec4 vTangent;
varying vec3 vWorldPosition;
varying vec3 vPosition;
varying float vHeight;
varying float vSlope;
varying float vFogFactor;
varying float vLODBlendFactor;

// 地形参数
uniform vec3 uAmbientColor;
uniform vec3 uLightColor;
uniform vec3 uLightPosition;
uniform vec3 uFogColor;
uniform bool uUseFog;
uniform bool uUseBlendMaps;
uniform int uLayerCount;
uniform float uMaxHeight;

// 纹理层参数
uniform sampler2D uTextures[8];
uniform sampler2D uNormalMaps[8];
uniform sampler2D uRoughnessMaps[8];
uniform sampler2D uAOMaps[8];
uniform sampler2D uBlendMaps[8];
uniform float uTilingFactors[8];
uniform float uMinHeights[8];
uniform float uMaxHeights[8];
uniform float uMinSlopes[8];
uniform float uMaxSlopes[8];

// LOD参数
uniform sampler2D uLowResTexture;
uniform bool uUseLOD;

// 计算混合权重
float calculateBlendWeight(float height, float slope, float minHeight, float maxHeight, float minSlope, float maxSlope) {
  // 计算高度权重
  float heightWeight = smoothstep(minHeight, maxHeight, height);
  if (height > maxHeight) {
    heightWeight = 1.0 - smoothstep(maxHeight, maxHeight + 0.1, height);
  }
  
  // 计算斜度权重
  float slopeWeight = smoothstep(minSlope, maxSlope, slope);
  if (slope > maxSlope) {
    slopeWeight = 1.0 - smoothstep(maxSlope, maxSlope + 0.1, slope);
  }
  
  // 组合权重
  return heightWeight * slopeWeight;
}

// 从法线贴图计算法线
vec3 calculateNormalFromMap(sampler2D normalMap, vec2 uv, vec3 normal, vec4 tangent) {
  // 获取法线贴图值
  vec3 normalMapValue = texture2D(normalMap, uv).xyz * 2.0 - 1.0;
  
  // 计算切线空间到世界空间的转换
  vec3 tangentNormal = normalMapValue;
  vec3 T = normalize(tangent.xyz);
  vec3 B = normalize(cross(normal, T) * tangent.w);
  mat3 TBN = mat3(T, B, normal);
  
  // 转换法线到世界空间
  return normalize(TBN * tangentNormal);
}

// 优化的纹理采样
vec4 optimizedTextureSample(sampler2D tex, vec2 uv, float lodBias) {
  #ifdef GL_EXT_shader_texture_lod
    return texture2DLodEXT(tex, uv, lodBias);
  #else
    return texture2D(tex, uv, lodBias);
  #endif
}

void main() {
  // 初始化颜色和权重
  vec4 finalColor = vec4(0.0);
  vec3 finalNormal = normalize(vNormal);
  float totalWeight = 0.0;
  
  // 计算LOD偏移
  float lodBias = vLODBlendFactor * 4.0;
  
  // 如果使用LOD并且LOD混合因子大于0.99，直接使用低分辨率纹理
  if (uUseLOD && vLODBlendFactor > 0.99) {
    finalColor = texture2D(uLowResTexture, vUv);
  } else {
    // 计算每层的混合权重和颜色
    for (int i = 0; i < 8; i++) {
      if (i >= uLayerCount) break;
      
      // 计算纹理坐标
      vec2 tiledUv = vUv * uTilingFactors[i];
      
      // 获取混合权重
      float weight = 0.0;
      if (uUseBlendMaps) {
        // 使用混合贴图
        weight = texture2D(uBlendMaps[i], vUv).r;
      } else {
        // 使用高度和斜度计算混合权重
        weight = calculateBlendWeight(vHeight, vSlope, uMinHeights[i], uMaxHeights[i], uMinSlopes[i], uMaxSlopes[i]);
      }
      
      if (weight > 0.0) {
        // 获取纹理颜色（使用LOD偏移）
        vec4 textureColor = optimizedTextureSample(uTextures[i], tiledUv, lodBias);
        
        // 获取法线贴图
        vec3 normalMapValue = calculateNormalFromMap(uNormalMaps[i], tiledUv, vNormal, vTangent);
        
        // 获取粗糙度贴图
        float roughness = texture2D(uRoughnessMaps[i], tiledUv).r;
        
        // 获取环境光遮蔽贴图
        float ao = texture2D(uAOMaps[i], tiledUv).r;
        
        // 简单的光照计算
        vec3 lightDir = normalize(uLightPosition - vWorldPosition);
        float diffuse = max(dot(normalMapValue, lightDir), 0.0);
        vec3 ambient = uAmbientColor * ao;
        vec3 lighting = ambient + uLightColor * diffuse;
        
        // 混合颜色
        finalColor += vec4(textureColor.rgb * lighting, textureColor.a) * weight;
        
        // 混合法线
        finalNormal = normalize(mix(finalNormal, normalMapValue, weight));
        
        totalWeight += weight;
      }
    }
  }
  
  // 标准化颜色
  if (totalWeight > 0.0) {
    finalColor /= totalWeight;
  }
  
  // 应用雾效果
  if (uUseFog) {
    finalColor.rgb = mix(finalColor.rgb, uFogColor, vFogFactor);
  }
  
  // 输出最终颜色
  gl_FragColor = finalColor;
}
`;
