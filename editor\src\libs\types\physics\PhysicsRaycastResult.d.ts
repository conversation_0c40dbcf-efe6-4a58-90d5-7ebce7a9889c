/**
 * 物理射线检测结果
 * 用于存储射线检测的结果
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../core/Entity';
/**
 * 物理射线检测结果
 */
export declare class PhysicsRaycastResult {
    /** 是否命中 */
    private hit;
    /** 命中点（世界坐标） */
    private hitPoint;
    /** 命中法线（世界坐标） */
    private hitNormal;
    /** 命中距离 */
    private hitDistance;
    /** 命中的物理体 */
    private hitBody;
    /** 命中的实体 */
    private hitEntity;
    /** 命中的形状 */
    private hitShape;
    /** 原始CANNON射线检测结果 */
    private cannonResult;
    /**
     * 创建物理射线检测结果
     * @param cannonResult CANNON射线检测结果
     * @param entityMap 物理体到实体的映射
     */
    constructor(cannonResult?: CANNON.RaycastResult, entityMap?: Map<CANNON.Body, Entity>);
    /**
     * 是否命中
     * @returns 是否命中
     */
    hasHit(): boolean;
    /**
     * 获取命中点（世界坐标）
     * @returns 命中点
     */
    getHitPoint(): THREE.Vector3;
    /**
     * 获取命中法线（世界坐标）
     * @returns 命中法线
     */
    getHitNormal(): THREE.Vector3;
    /**
     * 获取命中距离
     * @returns 命中距离
     */
    getHitDistance(): number;
    /**
     * 获取命中的物理体
     * @returns 命中的物理体
     */
    getHitBody(): CANNON.Body | null;
    /**
     * 获取命中的实体
     * @returns 命中的实体
     */
    getHitEntity(): Entity | null;
    /**
     * 获取命中的形状
     * @returns 命中的形状
     */
    getHitShape(): CANNON.Shape | null;
    /**
     * 获取原始CANNON射线检测结果
     * @returns 原始CANNON射线检测结果
     */
    getCannonResult(): CANNON.RaycastResult | null;
    /**
     * 重置结果
     */
    reset(): void;
    /**
     * 从CANNON射线检测结果更新
     * @param cannonResult CANNON射线检测结果
     * @param entityMap 物理体到实体的映射
     */
    updateFromCannonResult(cannonResult: CANNON.RaycastResult, entityMap?: Map<CANNON.Body, Entity>): void;
    /**
     * 从物理体获取实体
     * @param body 物理体
     * @returns 实体
     */
    private getEntityFromBody;
}
