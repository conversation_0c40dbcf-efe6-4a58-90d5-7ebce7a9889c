/**
 * 用户服务和项目服务集成测试
 */
import axios from 'axios';
import {
  TEST_CONFIG,
  createTestUser,
  loginTestUser,
  createTestProject,
  cleanupTestData,
} from '../setup';

describe('用户服务和项目服务集成测试', () => {
  // 测试数据
  const testUser = {
    username: `test_user_${Date.now()}`,
    password: 'Test@123456',
    email: `test_user_${Date.now()}@example.com`,
  };
  
  const testProject = {
    name: `test_project_${Date.now()}`,
    description: '测试项目描述',
  };
  
  let authToken: string;
  let userId: string;
  let projectId: string;
  
  // 在所有测试开始前创建测试用户
  beforeAll(async () => {
    try {
      // 创建测试用户
      const userData = await createTestUser(
        testUser.username,
        testUser.password,
        testUser.email
      );
      
      // 登录获取令牌
      const loginData = await loginTestUser(testUser.username, testUser.password);
      authToken = loginData.accessToken;
      userId = loginData.user.id;
      
      expect(authToken).toBeDefined();
      expect(userId).toBeDefined();
    } catch (error) {
      console.error('测试准备失败:', error);
      throw error;
    }
  });
  
  // 在所有测试结束后清理测试数据
  afterAll(async () => {
    await cleanupTestData(authToken, projectId);
  });
  
  // 测试用户创建和认证
  describe('用户认证', () => {
    it('应该能够成功登录', async () => {
      const response = await axios.post(`${TEST_CONFIG.userService.url}/auth/login`, {
        username: testUser.username,
        password: testUser.password,
      });
      
      expect(response.status).toBe(200);
      expect(response.data.accessToken).toBeDefined();
      expect(response.data.user.username).toBe(testUser.username);
    });
    
    it('使用错误的密码应该登录失败', async () => {
      try {
        await axios.post(`${TEST_CONFIG.userService.url}/auth/login`, {
          username: testUser.username,
          password: 'wrong_password',
        });
        // 如果没有抛出错误，测试失败
        fail('应该抛出认证错误');
      } catch (error) {
        if (axios.isAxiosError(error)) {
          expect(error.response?.status).toBe(401);
        } else {
          throw error;
        }
      }
    });
    
    it('应该能够获取当前用户信息', async () => {
      const response = await axios.get(`${TEST_CONFIG.userService.url}/users/me`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });
      
      expect(response.status).toBe(200);
      expect(response.data.id).toBe(userId);
      expect(response.data.username).toBe(testUser.username);
      expect(response.data.email).toBe(testUser.email);
    });
  });
  
  // 测试项目创建和管理
  describe('项目管理', () => {
    it('应该能够创建新项目', async () => {
      const response = await axios.post(
        `${TEST_CONFIG.projectService.url}/projects`,
        testProject,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(response.status).toBe(201);
      expect(response.data.name).toBe(testProject.name);
      expect(response.data.description).toBe(testProject.description);
      expect(response.data.ownerId).toBe(userId);
      
      projectId = response.data.id;
    });
    
    it('应该能够获取用户的项目列表', async () => {
      const response = await axios.get(`${TEST_CONFIG.projectService.url}/projects`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      
      const createdProject = response.data.find((p: any) => p.id === projectId);
      expect(createdProject).toBeDefined();
      expect(createdProject.name).toBe(testProject.name);
    });
    
    it('应该能够获取项目详情', async () => {
      const response = await axios.get(
        `${TEST_CONFIG.projectService.url}/projects/${projectId}`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(response.status).toBe(200);
      expect(response.data.id).toBe(projectId);
      expect(response.data.name).toBe(testProject.name);
      expect(response.data.ownerId).toBe(userId);
    });
    
    it('应该能够更新项目信息', async () => {
      const updatedDescription = '更新后的项目描述';
      
      const response = await axios.patch(
        `${TEST_CONFIG.projectService.url}/projects/${projectId}`,
        {
          description: updatedDescription,
        },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(response.status).toBe(200);
      expect(response.data.description).toBe(updatedDescription);
      
      // 验证更新是否持久化
      const getResponse = await axios.get(
        `${TEST_CONFIG.projectService.url}/projects/${projectId}`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(getResponse.data.description).toBe(updatedDescription);
    });
  });
  
  // 测试用户和项目之间的权限关系
  describe('权限和访问控制', () => {
    let secondUser = {
      username: `test_user2_${Date.now()}`,
      password: 'Test@123456',
      email: `test_user2_${Date.now()}@example.com`,
    };
    
    let secondUserToken: string;
    
    beforeAll(async () => {
      // 创建第二个测试用户
      await createTestUser(
        secondUser.username,
        secondUser.password,
        secondUser.email
      );
      
      // 登录获取令牌
      const loginData = await loginTestUser(secondUser.username, secondUser.password);
      secondUserToken = loginData.accessToken;
    });
    
    it('未授权用户不应该能够访问其他用户的项目', async () => {
      try {
        await axios.get(
          `${TEST_CONFIG.projectService.url}/projects/${projectId}`,
          {
            headers: {
              Authorization: `Bearer ${secondUserToken}`,
            },
          }
        );
        // 如果没有抛出错误，测试失败
        fail('应该抛出权限错误');
      } catch (error) {
        if (axios.isAxiosError(error)) {
          expect(error.response?.status).toBe(403);
        } else {
          throw error;
        }
      }
    });
    
    it('应该能够共享项目给其他用户', async () => {
      // 共享项目
      const shareResponse = await axios.post(
        `${TEST_CONFIG.projectService.url}/projects/${projectId}/share`,
        {
          username: secondUser.username,
          permission: 'read',
        },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(shareResponse.status).toBe(200);
      
      // 验证第二个用户现在可以访问项目
      const getResponse = await axios.get(
        `${TEST_CONFIG.projectService.url}/projects/${projectId}`,
        {
          headers: {
            Authorization: `Bearer ${secondUserToken}`,
          },
        }
      );
      
      expect(getResponse.status).toBe(200);
      expect(getResponse.data.id).toBe(projectId);
    });
    
    it('只读用户不应该能够修改项目', async () => {
      try {
        await axios.patch(
          `${TEST_CONFIG.projectService.url}/projects/${projectId}`,
          {
            description: '尝试修改项目描述',
          },
          {
            headers: {
              Authorization: `Bearer ${secondUserToken}`,
            },
          }
        );
        // 如果没有抛出错误，测试失败
        fail('应该抛出权限错误');
      } catch (error) {
        if (axios.isAxiosError(error)) {
          expect(error.response?.status).toBe(403);
        } else {
          throw error;
        }
      }
    });
  });
});
