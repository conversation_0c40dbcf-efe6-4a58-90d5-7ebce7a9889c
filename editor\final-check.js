#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 检查单个文件的错误
function checkFileErrors(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const errors = [];
    const lines = content.split('\n');

    // 检查重复的import
    const imports = new Set();
    
    lines.forEach((line, index) => {
      const trimmed = line.trim();
      
      // 检查重复的import
      if (trimmed.startsWith('import ') && trimmed.includes('from ')) {
        if (imports.has(trimmed)) {
          errors.push(`Line ${index + 1}: 重复的import - ${trimmed}`);
        } else {
          imports.add(trimmed);
        }
      }
      
      // 检查重复的antd import
      if (trimmed === "import { Select, Switch, InputNumber, Form } from 'antd';") {
        errors.push(`Line ${index + 1}: 重复的antd组件导入 - ${trimmed}`);
      }
      
      // 检查未使用的图标导入
      const iconImports = [
        'ThunderboltOutlined', 'ReloadOutlined', 'RocketOutlined', 
        'InfoCircleOutlined', 'CheckCircleOutlined', 'CloseCircleOutlined',
        'ExclamationCircleOutlined', 'SoundOutlined', 'FileOutlined',
        'SettingOutlined', 'PlayCircleOutlined', 'PauseCircleOutlined',
        'SyncOutlined', 'BulbOutlined', 'FireOutlined', 'ClockCircleOutlined'
      ];
      
      iconImports.forEach(icon => {
        if (trimmed.includes(icon) && trimmed.includes('import')) {
          // 检查是否在代码中使用了这个图标
          const iconUsage = new RegExp(`<${icon}|${icon}\\s*/>`, 'g');
          if (!iconUsage.test(content)) {
            errors.push(`Line ${index + 1}: 未使用的图标导入 - ${icon}`);
          }
        }
      });
    });

    return errors;
  } catch (error) {
    return [`文件读取错误: ${error.message}`];
  }
}

// 检查目录中的所有文件
function checkDirectory(dir) {
  const results = {};
  
  try {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'build'].includes(file)) {
        Object.assign(results, checkDirectory(filePath));
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        const errors = checkFileErrors(filePath);
        if (errors.length > 0) {
          results[path.relative(__dirname, filePath)] = errors;
        }
      }
    });
  } catch (error) {
    console.error(`检查目录 ${dir} 时出错:`, error.message);
  }
  
  return results;
}

// 主函数
function main() {
  console.log('开始最终错误检查...\n');
  
  const srcDir = path.join(__dirname, 'src');
  const results = checkDirectory(srcDir);
  
  const fileCount = Object.keys(results).length;
  
  if (fileCount === 0) {
    console.log('🎉 太好了！没有发现任何错误！');
    console.log('所有文件都已正确修复。');
  } else {
    console.log(`❌ 发现 ${fileCount} 个文件仍有错误:\n`);
    
    Object.entries(results).forEach(([file, errors]) => {
      console.log(`📁 ${file}:`);
      errors.forEach(error => {
        console.log(`   ${error}`);
      });
      console.log('');
    });
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkFileErrors, checkDirectory };
