/**
 * UI2DSystem.ts
 *
 * 2D UI系统，管理2D界面元素
 */
import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { UI2DComponent, UI2DComponentProps } from '../components/UI2DComponent';
import { UIComponentType } from '../components/UIComponent';
import { UISystem } from './UISystem';
/**
 * 2D UI系统配置
 */
export interface UI2DSystemConfig {
    debug?: boolean;
    autoCreateContainer?: boolean;
    containerId?: string;
    defaultFont?: string;
    defaultFontSize?: number;
    defaultTextColor?: string;
    defaultBackgroundColor?: string;
    defaultBorderColor?: string;
    defaultBorderWidth?: number;
    defaultBorderRadius?: number;
    defaultPadding?: number;
    defaultMargin?: number;
}
/**
 * 2D UI系统
 * 管理2D界面元素
 */
export declare class UI2DSystem extends System {
    private uiSystem;
    private config;
    private container?;
    /**
     * 构造函数
     * @param world 世界实例
     * @param uiSystem UI系统实例
     * @param config 2D UI系统配置
     */
    constructor(world: World, uiSystem: UISystem, config?: UI2DSystemConfig);
    /**
     * 创建HTML容器
     */
    private createContainer;
    /**
     * 创建2D UI元素
     * @param entity 实体
     * @param type UI元素类型
     * @param props UI元素属性
     * @returns 创建的2D UI组件
     */
    createUIElement(entity: Entity, type: UIComponentType, props?: UI2DComponentProps): UI2DComponent;
    /**
     * 创建按钮
     * @param entity 实体
     * @param text 按钮文本
     * @param props 按钮属性
     * @returns 创建的按钮组件
     */
    createButton(entity: Entity, text: string, props?: UI2DComponentProps): UI2DComponent;
    /**
     * 创建文本
     * @param entity 实体
     * @param text 文本内容
     * @param props 文本属性
     * @returns 创建的文本组件
     */
    createText(entity: Entity, text: string, props?: UI2DComponentProps): UI2DComponent;
    /**
     * 创建图像
     * @param entity 实体
     * @param src 图像源
     * @param props 图像属性
     * @returns 创建的图像组件
     */
    createImage(entity: Entity, src: string, props?: UI2DComponentProps): UI2DComponent;
    /**
     * 创建输入框
     * @param entity 实体
     * @param placeholder 占位文本
     * @param props 输入框属性
     * @returns 创建的输入框组件
     */
    createInput(entity: Entity, placeholder?: string, props?: UI2DComponentProps): UI2DComponent;
    /**
     * 创建复选框
     * @param entity 实体
     * @param label 标签文本
     * @param checked 是否选中
     * @param props 复选框属性
     * @returns 创建的复选框组件
     */
    createCheckbox(entity: Entity, label?: string, checked?: boolean, props?: UI2DComponentProps): UI2DComponent;
    /**
     * 创建滑块
     * @param entity 实体
     * @param min 最小值
     * @param max 最大值
     * @param value 当前值
     * @param props 滑块属性
     * @returns 创建的滑块组件
     */
    createSlider(entity: Entity, min?: number, max?: number, value?: number, props?: UI2DComponentProps): UI2DComponent;
    /**
     * 创建下拉框
     * @param entity 实体
     * @param options 选项列表
     * @param selectedIndex 选中项索引
     * @param props 下拉框属性
     * @returns 创建的下拉框组件
     */
    createDropdown(entity: Entity, options?: string[], selectedIndex?: number, props?: UI2DComponentProps): UI2DComponent;
    /**
     * 创建面板
     * @param entity 实体
     * @param props 面板属性
     * @returns 创建的面板组件
     */
    createPanel(entity: Entity, props?: UI2DComponentProps): UI2DComponent;
    /**
     * 创建窗口
     * @param entity 实体
     * @param title 窗口标题
     * @param props 窗口属性
     * @returns 创建的窗口组件
     */
    createWindow(entity: Entity, title?: string, props?: UI2DComponentProps): UI2DComponent;
    /**
     * 更新系统
     * @param _deltaTime 时间增量 - 未使用，因为UI系统会更新所有UI组件
     */
    update(_deltaTime: number): void;
    /**
     * 渲染系统
     */
    render(): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
