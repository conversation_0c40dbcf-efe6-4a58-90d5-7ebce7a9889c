/**
 * 国际化类
 * 提供多语言支持
 */
import { EventEmitter } from '../utils/EventEmitter';

export interface I18nOptions {
  /** 默认语言 */
  language?: string;
  /** 回退语言 */
  fallbackLanguage?: string;
  /** 语言资源 */
  resources?: Record<string, Record<string, string>>;
}

export class I18n extends EventEmitter {
  /** 当前语言 */
  private language: string;

  /** 回退语言 */
  private fallbackLanguage: string;

  /** 语言资源 */
  private resources: Map<string, Map<string, string>> = new Map();

  /**
   * 创建国际化实例
   * @param options 国际化选项
   */
  constructor(options: I18nOptions = {}) {
    super();

    this.language = options.language || 'zh-CN';
    this.fallbackLanguage = options.fallbackLanguage || 'en-US';

    // 加载语言资源
    if (options.resources) {
      for (const [lang, translations] of Object.entries(options.resources)) {
        this.addResources(lang, translations);
      }
    }

    // 加载默认语言资源
    this.loadDefaultResources();
  }

  /**
   * 加载默认语言资源
   */
  private loadDefaultResources(): void {
    // 中文资源
    this.addResources('zh-CN', {
      'common.yes': '是',
      'common.no': '否',
      'common.ok': '确定',
      'common.cancel': '取消',
      'common.save': '保存',
      'common.delete': '删除',
      'common.edit': '编辑',
      'common.create': '创建',
      'common.update': '更新',
      'common.search': '搜索',
      'common.loading': '加载中...',
      'common.error': '错误',
      'common.success': '成功',
      'common.warning': '警告',
      'common.info': '信息',

      'engine.initialized': '引擎已初始化',
      'engine.started': '引擎已启动',
      'engine.stopped': '引擎已停止',
      'engine.error': '引擎错误',

      'scene.created': '场景已创建',
      'scene.loaded': '场景已加载',
      'scene.unloaded': '场景已卸载',
      'scene.cleared': '场景已清空',

      'entity.created': '实体已创建',
      'entity.deleted': '实体已删除',
      'entity.duplicated': '实体已复制',
      'entity.renamed': '实体已重命名',

      'component.added': '组件已添加',
      'component.removed': '组件已移除',
      'component.enabled': '组件已启用',
      'component.disabled': '组件已禁用',

      'asset.loaded': '资源已加载',
      'asset.unloaded': '资源已卸载',
      'asset.error': '资源加载错误',

      'physics.collision': '物理碰撞',
      'physics.trigger': '物理触发器',

      'input.keyDown': '按键按下',
      'input.keyUp': '按键抬起',
      'input.mouseDown': '鼠标按下',
      'input.mouseUp': '鼠标抬起',
      'input.mouseMove': '鼠标移动',
      'input.mouseWheel': '鼠标滚轮',
      'input.touchStart': '触摸开始',
      'input.touchEnd': '触摸结束',
      'input.touchMove': '触摸移动',

      'error.notFound': '未找到',
      'error.invalidParameter': '无效参数',
      'error.missingParameter': '缺少参数',
      'error.notSupported': '不支持',
      'error.notImplemented': '未实现',
      'error.outOfMemory': '内存不足',
      'error.timeout': '超时',
    });

    // 英文资源
    this.addResources('en-US', {
      'common.yes': 'Yes',
      'common.no': 'No',
      'common.ok': 'OK',
      'common.cancel': 'Cancel',
      'common.save': 'Save',
      'common.delete': 'Delete',
      'common.edit': 'Edit',
      'common.create': 'Create',
      'common.update': 'Update',
      'common.search': 'Search',
      'common.loading': 'Loading...',
      'common.error': 'Error',
      'common.success': 'Success',
      'common.warning': 'Warning',
      'common.info': 'Info',

      'engine.initialized': 'Engine initialized',
      'engine.started': 'Engine started',
      'engine.stopped': 'Engine stopped',
      'engine.error': 'Engine error',

      'scene.created': 'Scene created',
      'scene.loaded': 'Scene loaded',
      'scene.unloaded': 'Scene unloaded',
      'scene.cleared': 'Scene cleared',

      'entity.created': 'Entity created',
      'entity.deleted': 'Entity deleted',
      'entity.duplicated': 'Entity duplicated',
      'entity.renamed': 'Entity renamed',

      'component.added': 'Component added',
      'component.removed': 'Component removed',
      'component.enabled': 'Component enabled',
      'component.disabled': 'Component disabled',

      'asset.loaded': 'Asset loaded',
      'asset.unloaded': 'Asset unloaded',
      'asset.error': 'Asset loading error',

      'physics.collision': 'Physics collision',
      'physics.trigger': 'Physics trigger',

      'input.keyDown': 'Key down',
      'input.keyUp': 'Key up',
      'input.mouseDown': 'Mouse down',
      'input.mouseUp': 'Mouse up',
      'input.mouseMove': 'Mouse move',
      'input.mouseWheel': 'Mouse wheel',
      'input.touchStart': 'Touch start',
      'input.touchEnd': 'Touch end',
      'input.touchMove': 'Touch move',

      'error.notFound': 'Not found',
      'error.invalidParameter': 'Invalid parameter',
      'error.missingParameter': 'Missing parameter',
      'error.notSupported': 'Not supported',
      'error.notImplemented': 'Not implemented',
      'error.outOfMemory': 'Out of memory',
      'error.timeout': 'Timeout',
    });
  }

  /**
   * 添加语言资源
   * @param language 语言
   * @param resources 资源
   */
  public addResources(language: string, resources: Record<string, string>): void {
    if (!this.resources.has(language)) {
      this.resources.set(language, new Map());
    }

    const langResources = this.resources.get(language)!;

    for (const [key, value] of Object.entries(resources)) {
      langResources.set(key, value);
    }
  }

  /**
   * 设置当前语言
   * @param language 语言
   */
  public setLanguage(language: string): void {
    if (this.language === language) {
      return;
    }

    const oldLanguage = this.language;
    this.language = language;

    // 发出语言变更事件
    this.emit('languageChanged', language, oldLanguage);
  }

  /**
   * 获取当前语言
   * @returns 当前语言
   */
  public getLanguage(): string {
    return this.language;
  }

  /**
   * 设置回退语言
   * @param language 语言
   */
  public setFallbackLanguage(language: string): void {
    this.fallbackLanguage = language;
  }

  /**
   * 获取回退语言
   * @returns 回退语言
   */
  public getFallbackLanguage(): string {
    return this.fallbackLanguage;
  }

  /**
   * 翻译文本
   * @param key 翻译键
   * @param params 参数
   * @returns 翻译后的文本
   */
  public translate(key: string, params: Record<string, any> = {}): string {
    // 尝试从当前语言获取翻译
    let translation = this.getTranslation(this.language, key);

    // 如果没有找到，尝试从回退语言获取翻译
    if (!translation && this.fallbackLanguage !== this.language) {
      translation = this.getTranslation(this.fallbackLanguage, key);
    }

    // 如果仍然没有找到，返回键名
    if (!translation) {
      return key;
    }

    // 替换参数
    return this.replaceParams(translation, params);
  }

  /**
   * 获取翻译
   * @param language 语言
   * @param key 翻译键
   * @returns 翻译文本
   */
  private getTranslation(language: string, key: string): string | undefined {
    const langResources = this.resources.get(language);

    if (!langResources) {
      return undefined;
    }

    return langResources.get(key);
  }

  /**
   * 替换参数
   * @param text 文本
   * @param params 参数
   * @returns 替换后的文本
   */
  private replaceParams(text: string, params: Record<string, any>): string {
    return text.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key] !== undefined ? String(params[key]) : match;
    });
  }

  /**
   * 获取支持的语言列表
   * @returns 语言列表
   */
  public getLanguages(): string[] {
    return Array.from(this.resources.keys());
  }

  /**
   * 是否支持指定语言
   * @param language 语言
   * @returns 是否支持
   */
  public hasLanguage(language: string): boolean {
    return this.resources.has(language);
  }

  /**
   * 获取语言资源
   * @param language 语言
   * @returns 语言资源
   */
  public getResources(language: string): Record<string, string> {
    const langResources = this.resources.get(language);

    if (!langResources) {
      return {};
    }

    const result: Record<string, string> = {};

    for (const [key, value] of Array.from(langResources.entries())) {
      result[key] = value;
    }

    return result;
  }
}
