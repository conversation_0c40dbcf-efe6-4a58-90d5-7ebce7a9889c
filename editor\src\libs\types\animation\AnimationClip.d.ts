/**
 * 动画片段
 * 表示一段可播放的动画数据
 */
import * as THREE from 'three';
/**
 * 动画轨道类型
 */
export declare enum TrackType {
    /** 位置轨道 */
    POSITION = "position",
    /** 旋转轨道 */
    ROTATION = "rotation",
    /** 缩放轨道 */
    SCALE = "scale",
    /** 变换矩阵轨道 */
    MATRIX = "matrix",
    /** 颜色轨道 */
    COLOR = "color",
    /** 透明度轨道 */
    OPACITY = "opacity",
    /** 可见性轨道 */
    VISIBILITY = "visibility",
    /** 权重轨道 */
    WEIGHT = "weight",
    /** 变形目标轨道 */
    MORPH_TARGET = "morphTarget",
    /** 自定义属性轨道 */
    CUSTOM = "custom"
}
/**
 * 动画插值类型
 */
export declare enum InterpolationType {
    /** 线性插值 */
    LINEAR = "linear",
    /** 阶梯插值 */
    STEP = "step",
    /** 立方样条插值 */
    CUBIC = "cubic",
    /** 贝塞尔曲线插值 */
    BEZIER = "bezier"
}
/**
 * 动画循环模式
 */
export declare enum LoopMode {
    /** 不循环 */
    NONE = "none",
    /** 重复循环 */
    REPEAT = "repeat",
    /** 往复循环 */
    PING_PONG = "pingpong"
}
/**
 * 关键帧接口
 */
export interface Keyframe {
    /** 时间点（秒） */
    time: number;
    /** 值 */
    value: any;
    /** 插值类型 */
    interpolation?: InterpolationType;
    /** 入切线（用于曲线插值） */
    inTangent?: any;
    /** 出切线（用于曲线插值） */
    outTangent?: any;
}
/**
 * 动画轨道接口
 */
export interface AnimationTrack {
    /** 轨道类型 */
    type: TrackType;
    /** 目标路径（如骨骼名称或属性路径） */
    targetPath: string;
    /** 关键帧数组 */
    keyframes: Keyframe[];
    /** 默认插值类型 */
    defaultInterpolation: InterpolationType;
}
/**
 * 动画片段选项
 */
export interface AnimationClipOptions {
    /** 动画名称 */
    name?: string;
    /** 动画轨道 */
    tracks?: AnimationTrack[];
    /** 动画持续时间（秒） */
    duration?: number;
    /** 循环模式 */
    loopMode?: LoopMode;
    /** 播放速度 */
    speed?: number;
    /** 是否启用 */
    enabled?: boolean;
    /** 权重 */
    weight?: number;
    /** 混合时间（秒） */
    blendTime?: number;
}
/**
 * 动画片段类
 */
export declare class AnimationClip {
    /** 动画名称 */
    name: string;
    /** 动画轨道 */
    tracks: AnimationTrack[];
    /** 动画持续时间（秒） */
    duration: number;
    /** 循环模式 */
    loopMode: LoopMode;
    /** 播放速度 */
    speed: number;
    /** 是否启用 */
    enabled: boolean;
    /** 权重 */
    weight: number;
    /** 混合时间（秒） */
    blendTime: number;
    /** Three.js动画片段（用于兼容） */
    private _threeClip;
    /** 缓存的时间 */
    private _cachedTime;
    /** 缓存的结果 */
    private _cachedResult;
    /** 是否启用缓存 */
    private _cacheEnabled;
    /**
     * 转换为Three.js动画片段
     * @returns Three.js动画片段
     */
    toThreeAnimationClip(): THREE.AnimationClip;
    /**
     * 获取Three.js属性名称
     * @param trackType 轨道类型
     * @returns Three.js属性名称
     */
    private getThreePropertyName;
    /**
     * 创建动画片段
     * @param options 动画片段选项
     */
    constructor(options?: AnimationClipOptions);
    /**
     * 从轨道计算动画持续时间
     */
    private calculateDuration;
    /**
     * 添加动画轨道
     * @param track 动画轨道
     */
    addTrack(track: AnimationTrack): void;
    /**
     * 移除动画轨道
     * @param index 轨道索引
     * @returns 是否成功移除
     */
    removeTrack(index: number): boolean;
    /**
     * 获取指定类型和目标的轨道
     * @param type 轨道类型
     * @param targetPath 目标路径
     * @returns 匹配的轨道，如果不存在则返回null
     */
    getTrack(type: TrackType, targetPath: string): AnimationTrack | null;
    /**
     * 获取指定时间点的动画状态
     * @param time 时间点（秒）
     * @param result 结果对象，用于存储计算结果
     * @returns 动画状态映射，键为目标路径，值为计算的属性值
     */
    getStateAtTime(time: number, result?: Map<string, any>): Map<string, any>;
    /**
     * 设置是否启用缓存
     * @param enabled 是否启用
     */
    setCacheEnabled(enabled: boolean): void;
    /**
     * 清除缓存
     */
    clearCache(): void;
    /**
     * 克隆值
     * @param value 要克隆的值
     * @returns 克隆的值
     */
    private _cloneValue;
    /**
     * 计算轨道在指定时间点的值
     * @param track 动画轨道
     * @param time 时间点（秒）
     * @returns 计算的值
     */
    private evaluateTrack;
    /**
     * 根据轨道类型获取默认值
     * @param type 轨道类型
     * @returns 默认值
     */
    private getDefaultValueForTrackType;
    /**
     * 在两个值之间进行插值
     * @param start 起始值
     * @param end 结束值
     * @param t 插值因子（0-1）
     * @param interpolationType 插值类型
     * @param trackType 轨道类型
     * @param outTangent 出切线
     * @param inTangent 入切线
     * @returns 插值结果
     */
    private interpolate;
    /**
     * 线性插值
     * @param start 起始值
     * @param end 结束值
     * @param t 插值因子（0-1）
     * @param trackType 轨道类型
     * @returns 插值结果
     */
    private linearInterpolate;
    /**
     * 阶梯插值
     * @param start 起始值
     * @param end 结束值
     * @param t 插值因子（0-1）
     * @returns 插值结果
     */
    private stepInterpolate;
    /**
     * 三次样条插值
     * @param start 起始值
     * @param end 结束值
     * @param t 插值因子（0-1）
     * @param outTangent 出切线
     * @param inTangent 入切线
     * @param trackType 轨道类型
     * @returns 插值结果
     */
    private cubicInterpolate;
    /**
     * 贝塞尔曲线插值
     * @param start 起始值
     * @param end 结束值
     * @param t 插值因子（0-1）
     * @param outTangent 出切线（控制点1）
     * @param inTangent 入切线（控制点2）
     * @param trackType 轨道类型
     * @returns 插值结果
     */
    private bezierInterpolate;
    /**
     * 从Three.js动画片段创建动画片段
     * @param threeClip Three.js动画片段
     * @returns 创建的动画片段
     */
    static fromThreeAnimationClip(threeClip: THREE.AnimationClip): AnimationClip;
}
