/**
 * PhysicsBody类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { PhysicsBody } from '../../src/physics/PhysicsBody';
import { PhysicsSystem } from '../../src/physics/PhysicsSystem';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { BodyType } from '../../src/physics/types';

describe('PhysicsBody', () => {
  let engine: Engine;
  let world: World;
  let physicsSystem: PhysicsSystem;
  let entity: Entity;
  let transform: Transform;
  let physicsBody: PhysicsBody;
  
  // 在每个测试前创建一个新的物理体实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建物理系统
    physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: false
    });
    
    // 添加物理系统到引擎
    engine.addSystem(physicsSystem);
    
    // 初始化引擎
    engine.initialize();
    
    // 创建实体
    entity = new Entity(world);
    
    // 添加变换组件
    transform = new Transform({
      position: { x: 0, y: 5, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity.addComponent(transform);
    
    // 创建物理体
    physicsBody = new PhysicsBody(entity, {
      type: BodyType.DYNAMIC,
      mass: 1,
      position: { x: 0, y: 5, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      linearVelocity: { x: 0, y: 0, z: 0 },
      angularVelocity: { x: 0, y: 0, z: 0 },
      linearDamping: 0.01,
      angularDamping: 0.01,
      fixedRotation: false
    });
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试物理体初始化
  it('应该正确初始化物理体', () => {
    expect(physicsBody).toBeDefined();
    expect(physicsBody.getEntity()).toBe(entity);
    expect(physicsBody.getType()).toBe(BodyType.DYNAMIC);
    expect(physicsBody.getMass()).toBe(1);
    expect(physicsBody.getPosition()).toEqual({ x: 0, y: 5, z: 0 });
    expect(physicsBody.getRotation()).toEqual({ x: 0, y: 0, z: 0 });
    expect(physicsBody.getLinearVelocity()).toEqual({ x: 0, y: 0, z: 0 });
    expect(physicsBody.getAngularVelocity()).toEqual({ x: 0, y: 0, z: 0 });
    expect(physicsBody.getLinearDamping()).toBe(0.01);
    expect(physicsBody.getAngularDamping()).toBe(0.01);
    expect(physicsBody.isFixedRotation()).toBe(false);
  });
  
  // 测试物理体类型
  it('应该能够设置和获取物理体类型', () => {
    // 验证初始类型
    expect(physicsBody.getType()).toBe(BodyType.DYNAMIC);
    
    // 设置新类型
    physicsBody.setType(BodyType.STATIC);
    
    // 验证类型已更改
    expect(physicsBody.getType()).toBe(BodyType.STATIC);
    
    // 设置另一个类型
    physicsBody.setType(BodyType.KINEMATIC);
    
    // 验证类型已更改
    expect(physicsBody.getType()).toBe(BodyType.KINEMATIC);
  });
  
  // 测试物理体质量
  it('应该能够设置和获取物理体质量', () => {
    // 验证初始质量
    expect(physicsBody.getMass()).toBe(1);
    
    // 设置新质量
    physicsBody.setMass(5);
    
    // 验证质量已更改
    expect(physicsBody.getMass()).toBe(5);
    
    // 设置零质量（静态物体）
    physicsBody.setMass(0);
    
    // 验证质量已更改
    expect(physicsBody.getMass()).toBe(0);
    expect(physicsBody.getType()).toBe(BodyType.STATIC);
  });
  
  // 测试物理体位置
  it('应该能够设置和获取物理体位置', () => {
    // 验证初始位置
    expect(physicsBody.getPosition()).toEqual({ x: 0, y: 5, z: 0 });
    
    // 设置新位置
    physicsBody.setPosition({ x: 10, y: 20, z: 30 });
    
    // 验证位置已更改
    expect(physicsBody.getPosition()).toEqual({ x: 10, y: 20, z: 30 });
  });
  
  // 测试物理体旋转
  it('应该能够设置和获取物理体旋转', () => {
    // 验证初始旋转
    expect(physicsBody.getRotation()).toEqual({ x: 0, y: 0, z: 0 });
    
    // 设置新旋转
    physicsBody.setRotation({ x: 0.1, y: 0.2, z: 0.3 });
    
    // 验证旋转已更改
    const rotation = physicsBody.getRotation();
    expect(rotation.x).toBeCloseTo(0.1);
    expect(rotation.y).toBeCloseTo(0.2);
    expect(rotation.z).toBeCloseTo(0.3);
  });
  
  // 测试物理体线性速度
  it('应该能够设置和获取物理体线性速度', () => {
    // 验证初始线性速度
    expect(physicsBody.getLinearVelocity()).toEqual({ x: 0, y: 0, z: 0 });
    
    // 设置新线性速度
    physicsBody.setLinearVelocity({ x: 1, y: 2, z: 3 });
    
    // 验证线性速度已更改
    expect(physicsBody.getLinearVelocity()).toEqual({ x: 1, y: 2, z: 3 });
  });
  
  // 测试物理体角速度
  it('应该能够设置和获取物理体角速度', () => {
    // 验证初始角速度
    expect(physicsBody.getAngularVelocity()).toEqual({ x: 0, y: 0, z: 0 });
    
    // 设置新角速度
    physicsBody.setAngularVelocity({ x: 1, y: 2, z: 3 });
    
    // 验证角速度已更改
    expect(physicsBody.getAngularVelocity()).toEqual({ x: 1, y: 2, z: 3 });
  });
  
  // 测试物理体线性阻尼
  it('应该能够设置和获取物理体线性阻尼', () => {
    // 验证初始线性阻尼
    expect(physicsBody.getLinearDamping()).toBe(0.01);
    
    // 设置新线性阻尼
    physicsBody.setLinearDamping(0.5);
    
    // 验证线性阻尼已更改
    expect(physicsBody.getLinearDamping()).toBe(0.5);
  });
  
  // 测试物理体角阻尼
  it('应该能够设置和获取物理体角阻尼', () => {
    // 验证初始角阻尼
    expect(physicsBody.getAngularDamping()).toBe(0.01);
    
    // 设置新角阻尼
    physicsBody.setAngularDamping(0.5);
    
    // 验证角阻尼已更改
    expect(physicsBody.getAngularDamping()).toBe(0.5);
  });
  
  // 测试物理体固定旋转
  it('应该能够设置和获取物理体固定旋转', () => {
    // 验证初始固定旋转
    expect(physicsBody.isFixedRotation()).toBe(false);
    
    // 设置固定旋转
    physicsBody.setFixedRotation(true);
    
    // 验证固定旋转已更改
    expect(physicsBody.isFixedRotation()).toBe(true);
  });
  
  // 测试应用力
  it('应该能够应用力', () => {
    // 创建Cannon.js物理体的模拟
    const cannonBody = new CANNON.Body({
      mass: 1,
      position: new CANNON.Vec3(0, 5, 0)
    });
    
    // 设置物理体的Cannon.js物理体
    (physicsBody as any).body = cannonBody;
    
    // 创建applyForce方法的模拟
    const applyForceSpy = vi.spyOn(cannonBody, 'applyForce');
    
    // 应用力
    physicsBody.applyForce(1, 2, 3);
    
    // 验证applyForce方法被调用
    expect(applyForceSpy).toHaveBeenCalledWith(
      new CANNON.Vec3(1, 2, 3),
      new CANNON.Vec3(0, 0, 0)
    );
  });
  
  // 测试应用局部力
  it('应该能够应用局部力', () => {
    // 创建Cannon.js物理体的模拟
    const cannonBody = new CANNON.Body({
      mass: 1,
      position: new CANNON.Vec3(0, 5, 0)
    });
    
    // 设置物理体的Cannon.js物理体
    (physicsBody as any).body = cannonBody;
    
    // 创建applyLocalForce方法的模拟
    const applyLocalForceSpy = vi.spyOn(cannonBody, 'applyLocalForce');
    
    // 应用局部力
    physicsBody.applyLocalForce(1, 2, 3);
    
    // 验证applyLocalForce方法被调用
    expect(applyLocalForceSpy).toHaveBeenCalledWith(
      new CANNON.Vec3(1, 2, 3),
      new CANNON.Vec3(0, 0, 0)
    );
  });
  
  // 测试应用冲量
  it('应该能够应用冲量', () => {
    // 创建Cannon.js物理体的模拟
    const cannonBody = new CANNON.Body({
      mass: 1,
      position: new CANNON.Vec3(0, 5, 0)
    });
    
    // 设置物理体的Cannon.js物理体
    (physicsBody as any).body = cannonBody;
    
    // 创建applyImpulse方法的模拟
    const applyImpulseSpy = vi.spyOn(cannonBody, 'applyImpulse');
    
    // 应用冲量
    physicsBody.applyImpulse(1, 2, 3);
    
    // 验证applyImpulse方法被调用
    expect(applyImpulseSpy).toHaveBeenCalledWith(
      new CANNON.Vec3(1, 2, 3),
      new CANNON.Vec3(0, 0, 0)
    );
  });
  
  // 测试应用局部冲量
  it('应该能够应用局部冲量', () => {
    // 创建Cannon.js物理体的模拟
    const cannonBody = new CANNON.Body({
      mass: 1,
      position: new CANNON.Vec3(0, 5, 0)
    });
    
    // 设置物理体的Cannon.js物理体
    (physicsBody as any).body = cannonBody;
    
    // 创建applyLocalImpulse方法的模拟
    const applyLocalImpulseSpy = vi.spyOn(cannonBody, 'applyLocalImpulse');
    
    // 应用局部冲量
    physicsBody.applyLocalImpulse(1, 2, 3);
    
    // 验证applyLocalImpulse方法被调用
    expect(applyLocalImpulseSpy).toHaveBeenCalledWith(
      new CANNON.Vec3(1, 2, 3),
      new CANNON.Vec3(0, 0, 0)
    );
  });
  
  // 测试应用扭矩
  it('应该能够应用扭矩', () => {
    // 创建Cannon.js物理体的模拟
    const cannonBody = new CANNON.Body({
      mass: 1,
      position: new CANNON.Vec3(0, 5, 0)
    });
    
    // 设置物理体的Cannon.js物理体
    (physicsBody as any).body = cannonBody;
    
    // 创建torque属性
    cannonBody.torque = new CANNON.Vec3(0, 0, 0);
    
    // 应用扭矩
    physicsBody.applyTorque(1, 2, 3);
    
    // 验证扭矩已应用
    expect(cannonBody.torque.x).toBe(1);
    expect(cannonBody.torque.y).toBe(2);
    expect(cannonBody.torque.z).toBe(3);
  });
});
