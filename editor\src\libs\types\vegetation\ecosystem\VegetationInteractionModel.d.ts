import { VegetationType, VegetationGrowthStage, VegetationData } from './EcosystemSimulationSystem';
/**
 * 植被交互类型
 */
export declare enum VegetationInteractionType {
    /** 竞争 */
    COMPETITION = "competition",
    /** 促进 */
    FACILITATION = "facilitation",
    /** 中性 */
    NEUTRAL = "neutral",
    /** 寄生 */
    PARASITISM = "parasitism",
    /** 共生 */
    SYMBIOSIS = "symbiosis"
}
/**
 * 植被交互规则
 */
export interface VegetationInteractionRule {
    /** 源植被类型 */
    sourceType: VegetationType;
    /** 目标植被类型 */
    targetType: VegetationType;
    /** 交互类型 */
    interactionType: VegetationInteractionType;
    /** 交互强度 */
    strength: number;
    /** 交互距离 */
    distance: number;
    /** 是否考虑生长阶段 */
    considerGrowthStage: boolean;
    /** 源植被生长阶段 */
    sourceGrowthStage?: VegetationGrowthStage;
    /** 目标植被生长阶段 */
    targetGrowthStage?: VegetationGrowthStage;
    /** 是否考虑高度差异 */
    considerHeightDifference: boolean;
    /** 是否考虑宽度差异 */
    considerWidthDifference: boolean;
    /** 是否考虑竞争力差异 */
    considerCompetitivenessDifference: boolean;
    /** 是否考虑健康度差异 */
    considerHealthDifference: boolean;
    /** 是否考虑年龄差异 */
    considerAgeDifference: boolean;
    /** 自定义数据 */
    userData?: any;
}
/**
 * 植被交互结果
 */
export interface VegetationInteractionResult {
    /** 源植被 */
    source: VegetationData;
    /** 目标植被 */
    target: VegetationData;
    /** 交互类型 */
    interactionType: VegetationInteractionType;
    /** 交互强度 */
    strength: number;
    /** 源植被健康度变化 */
    sourceHealthChange: number;
    /** 目标植被健康度变化 */
    targetHealthChange: number;
    /** 源植被生长速度变化 */
    sourceGrowthRateChange: number;
    /** 目标植被生长速度变化 */
    targetGrowthRateChange: number;
    /** 源植被竞争力变化 */
    sourceCompetitivenessChange: number;
    /** 目标植被竞争力变化 */
    targetCompetitivenessChange: number;
    /** 自定义数据 */
    userData?: any;
}
/**
 * 植被交互模型
 */
export declare class VegetationInteractionModel {
    /** 交互规则列表 */
    private rules;
    /** 默认交互规则 */
    private defaultRules;
    /**
     * 构造函数
     * @param useDefaultRules 是否使用默认规则
     */
    constructor(useDefaultRules?: boolean);
    /**
     * 添加交互规则
     * @param rule 交互规则
     */
    addRule(rule: VegetationInteractionRule): void;
    /**
     * 移除交互规则
     * @param index 规则索引
     */
    removeRule(index: number): void;
    /**
     * 获取交互规则
     * @returns 交互规则列表
     */
    getRules(): VegetationInteractionRule[];
    /**
     * 清除所有交互规则
     */
    clearRules(): void;
    /**
     * 重置为默认交互规则
     */
    resetToDefaultRules(): void;
    /**
     * 计算植被交互
     * @param source 源植被
     * @param target 目标植被
     * @param distance 距离
     * @returns 交互结果
     */
    calculateInteraction(source: VegetationData, target: VegetationData, distance: number): VegetationInteractionResult | null;
    /**
     * 应用交互结果
     * @param result 交互结果
     */
    applyInteractionResult(result: VegetationInteractionResult): void;
    /**
     * 查找适用的交互规则
     * @param source 源植被
     * @param target 目标植被
     * @returns 交互规则
     */
    private findApplicableRule;
    /**
     * 计算交互强度
     * @param source 源植被
     * @param target 目标植被
     * @param rule 交互规则
     * @param distance 距离
     * @returns 交互强度
     */
    private calculateInteractionStrength;
    /**
     * 计算健康度变化
     * @param source 源植被
     * @param target 目标植被
     * @param rule 交互规则
     * @param strength 交互强度
     * @returns 健康度变化
     */
    private calculateHealthChanges;
    /**
     * 计算生长速度变化
     * @param source 源植被
     * @param target 目标植被
     * @param rule 交互规则
     * @param strength 交互强度
     * @returns 生长速度变化
     */
    private calculateGrowthRateChanges;
    /**
     * 计算竞争力变化
     * @param source 源植被
     * @param target 目标植被
     * @param rule 交互规则
     * @param strength 交互强度
     * @returns 竞争力变化
     */
    private calculateCompetitivenessChanges;
}
