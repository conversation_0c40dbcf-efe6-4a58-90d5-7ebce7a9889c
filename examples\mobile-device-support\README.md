# 移动设备支持示例

本示例展示了编辑器对移动设备的支持功能，包括移动UI布局优化、触控操作优化、性能优化和网络适应性。

## 功能概述

### 1. 移动设备检测

- 自动检测设备类型（手机、平板、桌面）
- 自动检测屏幕方向（横屏、竖屏）
- 自动检测设备能力（触摸支持、网络类型等）
- 提供设备信息API

### 2. 移动UI布局优化

- 根据设备类型和屏幕方向自动切换布局
- 提供移动设备专用布局
- 支持触控友好的UI元素尺寸和间距
- 支持折叠/展开面板功能

### 3. 触控操作优化

- 支持多种触控手势（点击、双击、长按、滑动、捏合、旋转）
- 提供触控友好的变换控制器
- 支持触控反馈效果
- 提供触控辅助功能

### 4. 性能优化

- 根据设备性能自动调整渲染质量
- 支持低电量模式
- 支持后台暂停功能
- 提供性能监控和分析工具

### 5. 网络适应性

- 根据网络质量自动调整数据传输策略
- 支持离线工作模式
- 提供网络状态指示和提示
- 优化移动网络下的同步频率和优先级

## 使用方法

### 移动设备检测

```typescript
import MobileDeviceService, { DeviceType, ScreenOrientation } from '../../services/MobileDeviceService';

// 获取设备信息
const deviceInfo = MobileDeviceService.getDeviceInfo();
console.log('设备类型:', deviceInfo.type);
console.log('是否是移动设备:', deviceInfo.isMobile);
console.log('是否是平板设备:', deviceInfo.isTablet);
console.log('是否是触摸设备:', deviceInfo.isTouch);
console.log('屏幕方向:', deviceInfo.orientation);
console.log('屏幕尺寸:', deviceInfo.screenWidth, 'x', deviceInfo.screenHeight);

// 监听设备变化
MobileDeviceService.on('deviceChanged', (info) => {
  console.log('设备类型已变化:', info.type);
});

MobileDeviceService.on('orientationChanged', (info) => {
  console.log('屏幕方向已变化:', info.orientation);
});
```

### 移动UI布局优化

```typescript
import MobileLayoutService from '../../services/MobileLayoutService';

// 应用移动布局
MobileLayoutService.applyMobileLayout('mobilePortrait');

// 优化当前布局
MobileLayoutService.optimizeCurrentLayout();

// 获取当前移动布局
const currentLayout = MobileLayoutService.getCurrentMobileLayout();
console.log('当前移动布局:', currentLayout);

// 监听布局变化
MobileLayoutService.on('layoutChanged', (data) => {
  console.log('布局已变化:', data.layoutName);
});
```

### 触控操作优化

```typescript
import TouchInteractionService, { GestureType, GestureState } from '../../services/TouchInteractionService';

// 初始化触控交互服务
TouchInteractionService.initialize(document.getElementById('scene-container'));

// 监听手势事件
TouchInteractionService.on(GestureType.TAP, (data) => {
  if (data.state === GestureState.ENDED) {
    console.log('点击位置:', data.position);
  }
});

TouchInteractionService.on(GestureType.PINCH, (data) => {
  if (data.state === GestureState.UPDATED) {
    console.log('缩放比例:', data.scale);
  }
});

TouchInteractionService.on(GestureType.ROTATE, (data) => {
  if (data.state === GestureState.UPDATED) {
    console.log('旋转角度:', data.rotation);
  }
});

// 销毁触控交互服务
TouchInteractionService.destroy();
```

### 性能优化

```typescript
import MobilePerformanceService, { PerformanceLevel } from '../../services/MobilePerformanceService';

// 设置性能级别
MobilePerformanceService.setPerformanceLevel(PerformanceLevel.AUTO);

// 获取当前性能配置
const config = MobilePerformanceService.getCurrentConfig();
console.log('渲染分辨率缩放比例:', config.renderScale);
console.log('最大纹理尺寸:', config.maxTextureSize);
console.log('阴影质量:', config.shadowQuality);

// 监听性能变化
MobilePerformanceService.on('performanceLevelChanged', (data) => {
  console.log('性能级别已变化:', data.oldLevel, '->', data.newLevel);
});

// 获取性能监控数据
const monitorData = MobilePerformanceService.getMonitorData();
console.log('帧率:', monitorData.fps);
console.log('电池电量:', monitorData.batteryLevel, '%');
```

### 网络适应性

```typescript
import MobileNetworkService, { NetworkQualityLevel } from '../../services/MobileNetworkService';

// 获取网络状态
const networkStatus = MobileNetworkService.getNetworkStatus();
console.log('网络类型:', networkStatus.type);
console.log('网络质量级别:', networkStatus.qualityLevel);
console.log('下载速度:', networkStatus.downloadSpeed, 'Mbps');

// 设置网络适应性配置
MobileNetworkService.setAdaptivityConfig({
  syncInterval: 200,
  compressionLevel: 9,
  useDeltaSync: true
});

// 监听网络变化
MobileNetworkService.on('networkQualityChanged', (data) => {
  console.log('网络质量已变化:', data.oldLevel, '->', data.newLevel);
});

// 缓存离线数据
MobileNetworkService.cacheOfflineData('scene-state', { position: [0, 0, 0], rotation: [0, 0, 0] });
```

## 注意事项

1. 移动设备支持功能需要在支持触摸的设备上测试
2. 性能优化功能会根据设备性能自动调整，可能会影响视觉效果
3. 网络适应性功能需要在不同网络环境下测试
4. 触控操作优化功能可能会与浏览器默认手势冲突，需要适当处理

## 示例代码

完整的示例代码可以在以下文件中找到：

- `newsystem/editor/src/services/MobileDeviceService.ts`
- `newsystem/editor/src/services/MobileLayoutService.ts`
- `newsystem/editor/src/services/TouchInteractionService.ts`
- `newsystem/editor/src/services/MobilePerformanceService.ts`
- `newsystem/editor/src/services/MobileNetworkService.ts`
- `newsystem/editor/src/components/mobile/MobileToolbar.tsx`
- `newsystem/editor/src/components/mobile/TouchControlPanel.tsx`
- `newsystem/editor/src/components/layout/MobileAdaptiveLayout.tsx`
