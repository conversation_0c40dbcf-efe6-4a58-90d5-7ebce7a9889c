# 监控服务 Dockerfile
FROM node:22-alpine AS base

# 设置工作目录
WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制监控服务代码
COPY monitoring-service/package*.json ./monitoring-service/
WORKDIR /app/monitoring-service

# 安装系统依赖
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 开发阶段
FROM base AS development
WORKDIR /app/monitoring-service
RUN npm ci
COPY monitoring-service/ ./
RUN npm run build

# 生产阶段
FROM base AS production

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S monitoring -u 1001

# 复制构建产物
COPY --from=development --chown=monitoring:nodejs /app/monitoring-service/dist ./dist
COPY --from=development --chown=monitoring:nodejs /app/monitoring-service/node_modules ./node_modules

# 切换到非root用户
USER monitoring

# 暴露端口
EXPOSE 3003

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3003/api/v1/health || exit 1

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/main.js"]
