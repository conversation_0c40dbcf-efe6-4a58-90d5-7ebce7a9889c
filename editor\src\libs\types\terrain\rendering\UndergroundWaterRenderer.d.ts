/**
 * 地下水体渲染器
 * 用于优化地下河流和湖泊的渲染性能
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';
/**
 * 地下水体类型
 */
export declare enum UndergroundWaterType {
    /** 地下河流 */
    RIVER = "river",
    /** 地下湖泊 */
    LAKE = "lake"
}
/**
 * 地下水体渲染参数
 */
export interface UndergroundWaterRenderParams {
    /** 水体类型 */
    type: UndergroundWaterType;
    /** 水体颜色 */
    color: THREE.Color;
    /** 水体透明度 */
    opacity: number;
    /** 水体反射率 */
    reflectivity: number;
    /** 水体折射率 */
    refractionRatio: number;
    /** 水体波动速度 */
    waveSpeed: number;
    /** 水体波动强度 */
    waveStrength: number;
    /** 水体深度 */
    depth: number;
    /** 是否启用LOD */
    enableLOD: boolean;
    /** LOD距离 */
    lodDistance: number[];
    /** 是否启用视锥体剔除 */
    enableFrustumCulling: boolean;
    /** 是否启用遮挡剔除 */
    enableOcclusionCulling: boolean;
    /** 是否启用实例化渲染 */
    enableInstancing: boolean;
}
/**
 * 地下水体渲染器
 */
export declare class UndergroundWaterRenderer {
    /** 水体材质 */
    private static waterMaterial;
    /** 水体几何体缓存 */
    private static geometryCache;
    /** 水体网格缓存 */
    private static meshCache;
    /** 性能监视器 */
    private static performanceMonitor;
    /**
     * 初始化水体渲染器
     */
    static initialize(): void;
    /**
     * 渲染地下河流
     * @param terrain 地形组件
     * @param scene 场景
     * @param params 渲染参数
     */
    static renderUndergroundRivers(terrain: TerrainComponent, scene: THREE.Scene, params: UndergroundWaterRenderParams): void;
    /**
     * 渲染地下湖泊
     * @param terrain 地形组件
     * @param scene 场景
     * @param params 渲染参数
     */
    static renderUndergroundLakes(terrain: TerrainComponent, scene: THREE.Scene, params: UndergroundWaterRenderParams): void;
    /**
     * 使用标准方式渲染河流
     * @param terrain 地形组件
     * @param scene 场景
     * @param rivers 河流数据
     * @param params 渲染参数
     */
    private static renderRiversStandard;
    /**
     * 使用实例化渲染河流
     * @param terrain 地形组件
     * @param scene 场景
     * @param rivers 河流数据
     * @param params 渲染参数
     */
    private static renderRiversInstanced;
    /**
     * 使用标准方式渲染湖泊
     * @param terrain 地形组件
     * @param scene 场景
     * @param lakes 湖泊数据
     * @param params 渲染参数
     */
    private static renderLakesStandard;
    /**
     * 使用实例化渲染湖泊
     * @param terrain 地形组件
     * @param scene 场景
     * @param lakes 湖泊数据
     * @param params 渲染参数
     */
    private static renderLakesInstanced;
    /**
     * 创建河流几何体
     * @param terrain 地形组件
     * @param river 河流数据
     * @param params 渲染参数
     * @returns 河流几何体
     */
    private static createRiverGeometry;
    /**
     * 创建湖泊几何体
     * @param terrain 地形组件
     * @param lake 湖泊数据
     * @param params 渲染参数
     * @returns 湖泊几何体
     */
    private static createLakeGeometry;
    /**
     * 应用LOD
     * @param mesh 网格
     * @param params 渲染参数
     */
    private static applyLOD;
    /**
     * 更新水体材质
     * @param params 渲染参数
     */
    private static updateWaterMaterial;
    /**
     * 清除水体网格
     * @param scene 场景
     * @param type 水体类型
     */
    private static clearWaterMeshes;
    /**
     * 设置地形的地下水体数据
     * @param terrain 地形组件
     * @param data 地下水体数据
     */
    static setTerrainUndergroundWaterData(terrain: TerrainComponent, data: {
        undergroundRivers?: any[];
        undergroundLakes?: any[];
    }): void;
    /**
     * 获取地形的地下水体数据
     * @param terrain 地形组件
     * @returns 地下水体数据
     */
    static getTerrainUndergroundWaterData(terrain: TerrainComponent): {
        undergroundRivers?: any[];
        undergroundLakes?: any[];
    };
    /**
     * 清理所有缓存
     */
    static clearCache(): void;
}
