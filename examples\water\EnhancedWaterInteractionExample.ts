/**
 * 增强水体交互示例
 * 展示如何使用增强的水体交互系统
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { WaterBodyComponent } from '../../engine/src/physics/water/WaterBodyComponent';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterInteractionSystem } from '../../engine/src/physics/water/WaterInteractionSystem';
import { UnderwaterParticleSystem } from '../../engine/src/rendering/water/UnderwaterParticleSystem';
import { WaterLightingSystem } from '../../engine/src/rendering/water/WaterLightingSystem';
import { WaterPresets, WaterPresetType } from '../../engine/src/physics/water/WaterPresets';
import { RenderingSystem } from '../../engine/src/rendering/RenderingSystem';
import { CameraSystem } from '../../engine/src/camera/CameraSystem';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { PhysicsSystem } from '../../engine/src/physics/PhysicsSystem';
import { TransformComponent } from '../../engine/src/core/TransformComponent';
import { MeshComponent } from '../../engine/src/rendering/MeshComponent';
import { PhysicsBodyComponent } from '../../engine/src/physics/PhysicsBodyComponent';
import { Debug } from '../../engine/src/utils/Debug';

/**
 * 增强水体交互示例
 */
export class EnhancedWaterInteractionExample {
  /** 世界 */
  private world: World;
  /** 渲染系统 */
  private renderingSystem: RenderingSystem;
  /** 相机系统 */
  private cameraSystem: CameraSystem;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水体交互系统 */
  private waterInteractionSystem: WaterInteractionSystem;
  /** 水下粒子系统 */
  private underwaterParticleSystem: UnderwaterParticleSystem;
  /** 水体光照系统 */
  private waterLightingSystem: WaterLightingSystem;
  /** 测试物体 */
  private testObjects: Entity[] = [];
  /** 水体 */
  private waterBodies: WaterBodyComponent[] = [];
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 创建系统
    this.createSystems();

    // 创建场景
    this.createScene();

    // 创建UI
    this.createUI();

    // 标记为已初始化
    this.initialized = true;
  }

  /**
   * 创建系统
   */
  private createSystems(): void {
    // 创建渲染系统
    this.renderingSystem = new RenderingSystem(this.world);
    this.world.addSystem(this.renderingSystem);

    // 创建相机系统
    this.cameraSystem = new CameraSystem(this.world);
    this.world.addSystem(this.cameraSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world);
    this.world.addSystem(this.inputSystem);

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem(this.world);
    this.world.addSystem(this.physicsSystem);

    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水体交互系统（使用增强配置）
    this.waterInteractionSystem = new WaterInteractionSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      
      // 启用所有基本效果
      enableSplashEffect: true,
      enableRippleEffect: true,
      enableDropletEffect: true,
      enableFlowEffect: true,
      enableSplittingEffect: true,
      enableBuoyancyEffect: true,
      enableDragEffect: true,
      
      // 启用高级浮力计算
      useVoxelBuoyancy: true,
      voxelResolution: 5,
      enableBuoyancyStabilization: true,
      buoyancyStabilizationStrength: 0.5,
      
      // 启用高级阻力计算
      useDirectionalDrag: true,
      enableTurbulenceDrag: true,
      enableRotationalDrag: true,
      rotationalDragStrength: 0.5,
      dragCoefficientX: 0.4, // 前后方向阻力系数
      dragCoefficientY: 0.8, // 上下方向阻力系数
      dragCoefficientZ: 0.6, // 左右方向阻力系数
      
      // 启用高级水流交互
      useAdvancedFlowInteraction: true,
      enableTurbulentFlow: true,
      
      // 启用调试可视化
      enableDebugVisualization: true,
      
      // 设置效果强度
      buoyancyEffectStrength: 1.2,
      dragEffectStrength: 1.0,
      flowEffectStrength: 1.0
    });
    this.world.addSystem(this.waterInteractionSystem);

    // 创建水下粒子系统
    this.underwaterParticleSystem = new UnderwaterParticleSystem(this.world, {
      enabled: true,
      autoUpdate: true
    });
    this.world.addSystem(this.underwaterParticleSystem);

    // 创建水体光照系统
    this.waterLightingSystem = new WaterLightingSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableVolumetricLight: true
    });
    this.world.addSystem(this.waterLightingSystem);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建地面
    this.createGround();

    // 创建水体
    this.createWaterBodies();

    // 创建测试物体
    this.createTestObjects();

    // 创建光源
    this.createLights();

    // 创建相机
    this.createCamera();
  }

  /**
   * 创建地面
   */
  private createGround(): void {
    // 创建地面实体
    const groundEntity = new Entity('ground');

    // 添加变换组件
    const groundTransform = new TransformComponent();
    groundTransform.setPosition(new THREE.Vector3(0, -5, 0));
    groundTransform.setRotation(new THREE.Quaternion().setFromEuler(new THREE.Euler(-Math.PI / 2, 0, 0)));
    groundTransform.setScale(new THREE.Vector3(100, 100, 1));
    groundEntity.addComponent(groundTransform);

    // 添加网格组件
    const groundMesh = new MeshComponent();
    groundMesh.setGeometry(new THREE.PlaneGeometry(1, 1));
    groundMesh.setMaterial(new THREE.MeshStandardMaterial({ color: 0x555555 }));
    groundEntity.addComponent(groundMesh);

    // 添加物理体组件
    const groundPhysics = new PhysicsBodyComponent();
    groundPhysics.setMass(0); // 静态物体
    groundPhysics.setShape('PLANE');
    groundEntity.addComponent(groundPhysics);

    // 添加到世界
    this.world.addEntity(groundEntity);
  }

  /**
   * 创建水体
   */
  private createWaterBodies(): void {
    // 创建湖泊
    this.createLake();

    // 创建河流
    this.createRiver();

    // 创建海洋
    this.createOcean();
  }

  /**
   * 创建湖泊
   */
  private createLake(): void {
    // 使用预设创建湖泊
    const lakeWaterBody = WaterPresets.createPreset(this.world, {
      type: WaterPresetType.LAKE,
      size: { width: 20, height: 5, depth: 20 },
      position: new THREE.Vector3(0, 0, 0)
    });

    // 添加到水体列表
    this.waterBodies.push(lakeWaterBody);
  }

  /**
   * 创建河流
   */
  private createRiver(): void {
    // 使用预设创建河流
    const riverWaterBody = WaterPresets.createPreset(this.world, {
      type: WaterPresetType.RIVER,
      size: { width: 5, height: 2, depth: 30 },
      position: new THREE.Vector3(30, -1, 0)
    });

    // 设置水流方向和速度
    riverWaterBody.setFlowDirection(new THREE.Vector3(0, 0, 1));
    riverWaterBody.setFlowSpeed(2.0);

    // 添加到水体列表
    this.waterBodies.push(riverWaterBody);
  }

  /**
   * 创建海洋
   */
  private createOcean(): void {
    // 使用预设创建海洋
    const oceanWaterBody = WaterPresets.createPreset(this.world, {
      type: WaterPresetType.OCEAN,
      size: { width: 50, height: 10, depth: 50 },
      position: new THREE.Vector3(-40, -2, 0)
    });

    // 设置波动参数
    oceanWaterBody.setWaveParams({
      amplitude: 0.5,
      frequency: 0.1,
      speed: 0.5,
      direction: { x: 1, z: 1 }
    });

    // 添加到水体列表
    this.waterBodies.push(oceanWaterBody);
  }

  /**
   * 创建测试物体
   */
  private createTestObjects(): void {
    // 创建不同形状的测试物体
    this.createBoxObject(new THREE.Vector3(0, 5, 0), new THREE.Vector3(1, 1, 1), 1);
    this.createSphereObject(new THREE.Vector3(3, 5, 0), 0.5, 1);
    this.createCylinderObject(new THREE.Vector3(-3, 5, 0), 0.5, 1, 10);
    this.createBoatObject(new THREE.Vector3(0, 5, 5));
  }

  /**
   * 创建盒子物体
   * @param position 位置
   * @param size 尺寸
   * @param mass 质量
   */
  private createBoxObject(position: THREE.Vector3, size: THREE.Vector3, mass: number): void {
    // 创建实体
    const entity = new Entity(`box_${this.testObjects.length}`);

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(position);
    entity.addComponent(transform);

    // 添加网格组件
    const mesh = new MeshComponent();
    mesh.setGeometry(new THREE.BoxGeometry(size.x, size.y, size.z));
    mesh.setMaterial(new THREE.MeshStandardMaterial({ color: 0xff0000 }));
    entity.addComponent(mesh);

    // 添加物理体组件
    const physics = new PhysicsBodyComponent();
    physics.setMass(mass);
    physics.setShape('BOX');
    physics.setSize(size);
    entity.addComponent(physics);

    // 添加到世界
    this.world.addEntity(entity);

    // 添加到测试物体列表
    this.testObjects.push(entity);
  }

  /**
   * 创建球体物体
   * @param position 位置
   * @param radius 半径
   * @param mass 质量
   */
  private createSphereObject(position: THREE.Vector3, radius: number, mass: number): void {
    // 创建实体
    const entity = new Entity(`sphere_${this.testObjects.length}`);

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(position);
    entity.addComponent(transform);

    // 添加网格组件
    const mesh = new MeshComponent();
    mesh.setGeometry(new THREE.SphereGeometry(radius, 16, 16));
    mesh.setMaterial(new THREE.MeshStandardMaterial({ color: 0x00ff00 }));
    entity.addComponent(mesh);

    // 添加物理体组件
    const physics = new PhysicsBodyComponent();
    physics.setMass(mass);
    physics.setShape('SPHERE');
    physics.setRadius(radius);
    entity.addComponent(physics);

    // 添加到世界
    this.world.addEntity(entity);

    // 添加到测试物体列表
    this.testObjects.push(entity);
  }

  /**
   * 创建圆柱体物体
   * @param position 位置
   * @param radius 半径
   * @param height 高度
   * @param mass 质量
   */
  private createCylinderObject(position: THREE.Vector3, radius: number, height: number, mass: number): void {
    // 创建实体
    const entity = new Entity(`cylinder_${this.testObjects.length}`);

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(position);
    entity.addComponent(transform);

    // 添加网格组件
    const mesh = new MeshComponent();
    mesh.setGeometry(new THREE.CylinderGeometry(radius, radius, height, 16));
    mesh.setMaterial(new THREE.MeshStandardMaterial({ color: 0x0000ff }));
    entity.addComponent(mesh);

    // 添加物理体组件
    const physics = new PhysicsBodyComponent();
    physics.setMass(mass);
    physics.setShape('CYLINDER');
    physics.setRadius(radius);
    physics.setHeight(height);
    entity.addComponent(physics);

    // 添加到世界
    this.world.addEntity(entity);

    // 添加到测试物体列表
    this.testObjects.push(entity);
  }

  /**
   * 创建船形物体
   * @param position 位置
   */
  private createBoatObject(position: THREE.Vector3): void {
    // 创建实体
    const entity = new Entity(`boat_${this.testObjects.length}`);

    // 添加变换组件
    const transform = new TransformComponent();
    transform.setPosition(position);
    entity.addComponent(transform);

    // 添加网格组件
    const mesh = new MeshComponent();
    
    // 创建船形几何体
    const boatGeometry = new THREE.BufferGeometry();
    
    // 船的顶点（简化的船形）
    const vertices = new Float32Array([
      // 底部
      -1.0, -0.2, -0.5,
      1.0, -0.2, -0.5,
      1.0, -0.2, 0.5,
      -1.0, -0.2, 0.5,
      
      // 侧面
      -1.0, 0.0, -0.5,
      1.0, 0.0, -0.5,
      1.0, 0.0, 0.5,
      -1.0, 0.0, 0.5,
      
      // 前部
      -1.0, -0.2, -0.5,
      -1.0, 0.0, -0.5,
      -1.0, 0.0, 0.5,
      -1.0, -0.2, 0.5,
      
      // 后部
      1.0, -0.2, -0.5,
      1.0, 0.0, -0.5,
      1.0, 0.0, 0.5,
      1.0, -0.2, 0.5,
      
      // 左侧
      -1.0, -0.2, -0.5,
      -1.0, 0.0, -0.5,
      1.0, 0.0, -0.5,
      1.0, -0.2, -0.5,
      
      // 右侧
      -1.0, -0.2, 0.5,
      -1.0, 0.0, 0.5,
      1.0, 0.0, 0.5,
      1.0, -0.2, 0.5
    ]);
    
    // 索引
    const indices = [
      // 底部
      0, 1, 2,
      0, 2, 3,
      
      // 侧面
      4, 5, 6,
      4, 6, 7,
      
      // 前部
      8, 9, 10,
      8, 10, 11,
      
      // 后部
      12, 13, 14,
      12, 14, 15,
      
      // 左侧
      16, 17, 18,
      16, 18, 19,
      
      // 右侧
      20, 21, 22,
      20, 22, 23
    ];
    
    boatGeometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));
    boatGeometry.setIndex(indices);
    boatGeometry.computeVertexNormals();
    
    mesh.setGeometry(boatGeometry);
    mesh.setMaterial(new THREE.MeshStandardMaterial({ color: 0xffff00 }));
    entity.addComponent(mesh);

    // 添加物理体组件
    const physics = new PhysicsBodyComponent();
    physics.setMass(5);
    physics.setShape('CONVEX');
    physics.setConvexHull(vertices);
    entity.addComponent(physics);

    // 添加到世界
    this.world.addEntity(entity);

    // 添加到测试物体列表
    this.testObjects.push(entity);
  }

  /**
   * 创建光源
   */
  private createLights(): void {
    // 创建环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
    this.world.getScene().add(ambientLight);

    // 创建平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 20, 10);
    directionalLight.castShadow = true;
    this.world.getScene().add(directionalLight);
  }

  /**
   * 创建相机
   */
  private createCamera(): void {
    // 创建相机实体
    const cameraEntity = new Entity('camera');

    // 添加变换组件
    const cameraTransform = new TransformComponent();
    cameraTransform.setPosition(new THREE.Vector3(0, 10, 20));
    cameraTransform.lookAt(new THREE.Vector3(0, 0, 0));
    cameraEntity.addComponent(cameraTransform);

    // 添加相机组件
    const camera = this.cameraSystem.createCamera('perspective');
    camera.setPerspective(60, window.innerWidth / window.innerHeight, 0.1, 1000);
    cameraEntity.addComponent(camera);

    // 添加到世界
    this.world.addEntity(cameraEntity);

    // 设置为活动相机
    this.cameraSystem.setActiveCamera(camera);
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    uiContainer.style.padding = '10px';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.color = 'white';
    uiContainer.style.fontFamily = 'Arial, sans-serif';
    document.body.appendChild(uiContainer);

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '增强水体交互示例';
    title.style.margin = '0 0 10px 0';
    uiContainer.appendChild(title);

    // 创建说明
    const description = document.createElement('p');
    description.textContent = '这个示例展示了增强的水体交互系统，包括体素浮力计算、方向性阻力和高级水流交互。';
    description.style.margin = '0 0 10px 0';
    uiContainer.appendChild(description);

    // 创建控制说明
    const controls = document.createElement('p');
    controls.textContent = '点击"添加物体"按钮可以在水面上方添加更多测试物体。';
    controls.style.margin = '0 0 10px 0';
    uiContainer.appendChild(controls);

    // 创建按钮
    const addObjectButton = document.createElement('button');
    addObjectButton.textContent = '添加物体';
    addObjectButton.style.padding = '5px 10px';
    addObjectButton.style.marginRight = '10px';
    addObjectButton.addEventListener('click', () => this.addRandomObject());
    uiContainer.appendChild(addObjectButton);

    // 创建重置按钮
    const resetButton = document.createElement('button');
    resetButton.textContent = '重置场景';
    resetButton.style.padding = '5px 10px';
    resetButton.addEventListener('click', () => this.resetScene());
    uiContainer.appendChild(resetButton);
  }

  /**
   * 添加随机物体
   */
  private addRandomObject(): void {
    // 随机位置
    const x = Math.random() * 20 - 10;
    const y = 10;
    const z = Math.random() * 20 - 10;
    const position = new THREE.Vector3(x, y, z);

    // 随机形状
    const shapeType = Math.floor(Math.random() * 3);
    switch (shapeType) {
      case 0:
        // 随机盒子
        const boxSize = new THREE.Vector3(
          Math.random() * 1 + 0.5,
          Math.random() * 1 + 0.5,
          Math.random() * 1 + 0.5
        );
        this.createBoxObject(position, boxSize, Math.random() * 5 + 1);
        break;
      case 1:
        // 随机球体
        const radius = Math.random() * 0.5 + 0.3;
        this.createSphereObject(position, radius, Math.random() * 5 + 1);
        break;
      case 2:
        // 随机圆柱体
        const cylinderRadius = Math.random() * 0.5 + 0.3;
        const height = Math.random() * 1 + 0.5;
        this.createCylinderObject(position, cylinderRadius, height, Math.random() * 5 + 1);
        break;
    }
  }

  /**
   * 重置场景
   */
  private resetScene(): void {
    // 移除所有测试物体
    for (const entity of this.testObjects) {
      this.world.removeEntity(entity);
    }
    this.testObjects = [];

    // 重新创建测试物体
    this.createTestObjects();
  }

  /**
   * 启动示例
   */
  public start(): void {
    if (!this.initialized) {
      Debug.error('EnhancedWaterInteractionExample', '示例尚未初始化');
      return;
    }

    // 启动世界
    this.world.start();

    // 启动渲染循环
    this.animate();
  }

  /**
   * 渲染循环
   */
  private animate(): void {
    requestAnimationFrame(() => this.animate());

    // 更新世界
    this.world.update();
  }
}
