import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';
/**
 * FXAA抗锯齿效果选项
 */
export interface FXAAEffectOptions extends PostProcessingEffectOptions {
    /** 边缘阈值 */
    edgeThreshold?: number;
    /** 边缘阈值最小值 */
    edgeThresholdMin?: number;
}
/**
 * FXAA抗锯齿效果
 */
export declare class FXAAEffect extends PostProcessingEffect {
    /** 边缘阈值 */
    private edgeThreshold;
    /** 边缘阈值最小值 */
    private edgeThresholdMin;
    /** FXAA通道 */
    private fxaaPass;
    /**
     * 创建FXAA抗锯齿效果
     * @param options FXAA抗锯齿效果选项
     */
    constructor(options?: FXAAEffectOptions);
    /**
     * 创建通道
     */
    protected createPass(): void;
    /**
     * 更新Uniforms
     */
    private updateUniforms;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
    /**
     * 设置边缘阈值
     * @param threshold 边缘阈值
     */
    setEdgeThreshold(threshold: number): void;
    /**
     * 获取边缘阈值
     * @returns 边缘阈值
     */
    getEdgeThreshold(): number;
    /**
     * 设置边缘阈值最小值
     * @param threshold 边缘阈值最小值
     */
    setEdgeThresholdMin(threshold: number): void;
    /**
     * 获取边缘阈值最小值
     * @returns 边缘阈值最小值
     */
    getEdgeThresholdMin(): number;
    /**
     * 更新效果
     * @param _deltaTime 帧间隔时间（秒）- 未使用，保留以符合接口
     */
    update(_deltaTime: number): void;
}
