/**
 * 资源类型枚举
 */
export declare enum ResourceType {
    TEXTURE = "texture",
    GEOMETRY = "geometry",
    MATERIAL = "material",
    SHADER = "shader",
    AUDIO = "audio",
    MODEL = "model",
    ANIMATION = "animation",
    OTHER = "other"
}
/**
 * 资源信息接口
 */
export interface ResourceInfo {
    /** 资源ID */
    id: string;
    /** 资源名称 */
    name: string;
    /** 资源类型 */
    type: ResourceType;
    /** 资源大小（字节） */
    size: number;
    /** 引用计数 */
    refCount: number;
    /** 创建时间 */
    createdAt: number;
    /** 最后访问时间 */
    lastAccessTime: number;
    /** 是否已释放 */
    disposed: boolean;
    /** 所有者/创建者 */
    owner?: string;
    /** 额外元数据 */
    metadata?: Record<string, any>;
}
/**
 * 内存快照接口
 */
export interface MemorySnapshot {
    /** 快照ID */
    id: string;
    /** 快照时间 */
    timestamp: number;
    /** 总内存使用量（字节） */
    totalMemory: number;
    /** JS堆内存使用量（字节） */
    jsHeapMemory: number;
    /** 资源内存使用量（字节） */
    resourceMemory: number;
    /** 按类型分类的内存使用量 */
    memoryByType: Record<ResourceType, number>;
    /** 资源列表 */
    resources: ResourceInfo[];
}
/**
 * 内存分析器配置接口
 */
export interface MemoryAnalyzerConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否启用自动内存快照 */
    enableAutoSnapshot?: boolean;
    /** 自动快照间隔（毫秒） */
    autoSnapshotInterval?: number;
    /** 是否启用内存泄漏检测 */
    enableLeakDetection?: boolean;
    /** 泄漏检测阈值（毫秒） */
    leakDetectionThreshold?: number;
    /** 是否启用调试输出 */
    debug?: boolean;
    /** 是否收集详细资源信息 */
    collectDetailedInfo?: boolean;
    /** 是否启用警告 */
    enableWarnings?: boolean;
}
/**
 * 内存分析器类
 */
export declare class MemoryAnalyzer {
    private static instance;
    /** 配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 资源映射 */
    private resources;
    /** 内存快照列表 */
    private snapshots;
    /** 自动快照定时器ID */
    private autoSnapshotTimerId;
    /** 内存泄漏检测定时器ID */
    private leakDetectionTimerId;
    /** 是否正在运行 */
    private running;
    /**
     * 获取单例实例
     */
    static getInstance(): MemoryAnalyzer;
    /**
     * 私有构造函数
     */
    private constructor();
    /**
     * 配置内存分析器
     * @param config 配置
     */
    configure(config: MemoryAnalyzerConfig): void;
    /**
     * 启动内存分析器
     */
    start(): void;
    /**
     * 停止内存分析器
     */
    stop(): void;
    /**
     * 启动自动快照
     */
    private startAutoSnapshot;
    /**
     * 启动内存泄漏检测
     */
    private startLeakDetection;
    /**
     * 注册资源
     * @param resource 资源信息
     */
    registerResource(resource: Omit<ResourceInfo, 'createdAt' | 'lastAccessTime' | 'disposed'>): void;
    /**
     * 更新资源
     * @param id 资源ID
     * @param updates 更新内容
     */
    updateResource(id: string, updates: Partial<ResourceInfo>): void;
    /**
     * 释放资源
     * @param id 资源ID
     */
    disposeResource(id: string): void;
    /**
     * 获取资源
     * @param id 资源ID
     */
    getResource(id: string): ResourceInfo | undefined;
    /**
     * 获取所有资源
     */
    getAllResources(): ResourceInfo[];
    /**
     * 获取活跃资源（未释放的）
     */
    getActiveResources(): ResourceInfo[];
    /**
     * 获取按类型分组的资源
     */
    getResourcesByType(): Record<ResourceType, ResourceInfo[]>;
    /**
     * 获取按类型分组的内存使用量
     */
    getMemoryByType(): Record<ResourceType, number>;
    /**
     * 获取总内存使用量
     */
    getTotalMemory(): number;
    /**
     * 获取JS堆内存使用量
     */
    getJsHeapMemory(): number;
    /**
     * 获取当前内存使用情况
     */
    getMemoryUsage(): {
        total: number;
        jsHeap: number;
        resources: number;
        byType: Record<ResourceType, number>;
    };
    /**
     * 拍摄内存快照
     */
    takeSnapshot(): MemorySnapshot;
    /**
     * 获取所有快照
     */
    getSnapshots(): MemorySnapshot[];
    /**
     * 获取最新快照
     */
    getLatestSnapshot(): MemorySnapshot | undefined;
    /**
     * 比较两个快照
     * @param snapshotId1 快照1 ID
     * @param snapshotId2 快照2 ID
     */
    compareSnapshots(snapshotId1: string, snapshotId2: string): {
        totalDiff: number;
        jsHeapDiff: number;
        resourceDiff: number;
        typeDiffs: Record<ResourceType, number>;
        newResources: ResourceInfo[];
        disposedResources: ResourceInfo[];
    };
    /**
     * 检测内存泄漏
     */
    detectLeaks(): {
        potentialLeaks: ResourceInfo[];
        totalLeakedMemory: number;
    };
    /**
     * 清理资源
     */
    clearResources(): void;
    /**
     * 清理快照
     */
    clearSnapshots(): void;
    /**
     * 重置内存分析器
     */
    reset(): void;
    /**
     * 监听事件
     * @param event 事件名称
     * @param listener 监听器
     */
    on(event: string, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    off(event: string, listener: (...args: any[]) => void): void;
    /**
     * 格式化字节数
     * @param bytes 字节数
     * @returns 格式化后的字符串
     */
    private formatBytes;
}
