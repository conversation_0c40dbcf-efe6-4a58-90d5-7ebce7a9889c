import { AnimationBlender } from './AnimationBlender';
import { Animator } from './Animator';
/**
 * 优化器事件类型
 */
export declare enum OptimizerEventType {
    /** 优化开始 */
    OPTIMIZE_START = "optimizeStart",
    /** 优化结束 */
    OPTIMIZE_END = "optimizeEnd",
    /** 优化进度 */
    OPTIMIZE_PROGRESS = "optimizeProgress",
    /** 优化错误 */
    OPTIMIZE_ERROR = "optimizeError"
}
/**
 * 优化级别
 */
export declare enum OptimizationLevel {
    /** 无优化 */
    NONE = "none",
    /** 低级优化 */
    LOW = "low",
    /** 中级优化 */
    MEDIUM = "medium",
    /** 高级优化 */
    HIGH = "high",
    /** 极限优化 */
    EXTREME = "extreme"
}
/**
 * 优化器配置
 */
export interface OptimizerConfig {
    /** 优化级别 */
    level?: OptimizationLevel;
    /** 是否启用缓存 */
    enableCache?: boolean;
    /** 是否启用对象池 */
    enableObjectPool?: boolean;
    /** 是否启用批处理 */
    enableBatchProcessing?: boolean;
    /** 是否启用多线程 */
    enableMultiThreading?: boolean;
    /** 是否启用GPU加速 */
    enableGPUAcceleration?: boolean;
    /** 是否启用LOD */
    enableLOD?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 动画优化器
 */
export declare class AnimationOptimizer {
    /** 优化级别 */
    private level;
    /** 是否启用缓存 */
    private enableCache;
    /** 是否启用对象池 */
    private enableObjectPool;
    /** 是否启用批处理 */
    private enableBatchProcessing;
    /** 是否启用多线程 */
    private enableMultiThreading;
    /** 是否启用GPU加速 */
    private enableGPUAcceleration;
    /** 是否启用LOD */
    private enableLOD;
    /** 是否启用调试 */
    private debug;
    /** 事件发射器 */
    private eventEmitter;
    /** 缓存 */
    private cache;
    /** 对象池 */
    private objectPool;
    /** 工作线程 */
    private workers;
    /** 性能监控数据 */
    private performanceData;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: OptimizerConfig);
    /**
     * 应用优化级别
     */
    private applyOptimizationLevel;
    /**
     * 初始化优化器
     */
    private initialize;
    /**
     * 初始化缓存
     */
    private initializeCache;
    /**
     * 初始化对象池
     */
    private initializeObjectPool;
    /**
     * 初始化工作线程
     */
    private initializeWorkers;
    /**
     * 初始化GPU加速
     */
    private initializeGPUAcceleration;
    /**
     * 初始化LOD
     */
    private initializeLOD;
    /**
     * 优化混合器
     * @param blender 混合器
     */
    optimizeBlender(blender: AnimationBlender): void;
    /**
     * 优化混合层
     * @param blender 混合器
     */
    private optimizeLayers;
    /**
     * 优化遮罩
     * @param blender 混合器
     */
    private optimizeMasks;
    /**
     * 优化动画控制器
     * @param animator 动画控制器
     */
    optimizeAnimator(animator: Animator): void;
    /**
     * 优化动画片段
     * @param animator 动画控制器
     */
    private optimizeClips;
    /**
     * 移除未使用的轨道
     * @param clip 动画片段
     */
    private removeUnusedTracks;
    /**
     * 简化关键帧
     * @param clip 动画片段
     */
    private simplifyKeyframes;
    /**
     * 优化轨道数据
     * @param clip 动画片段
     */
    private optimizeTrackData;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: OptimizerEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: OptimizerEventType, listener: (data: any) => void): void;
    /**
     * 获取性能数据
     * @returns 性能数据
     */
    getPerformanceData(): any;
    /**
     * 更新性能数据
     */
    updatePerformanceData(): void;
    /**
     * 销毁优化器
     */
    dispose(): void;
}
