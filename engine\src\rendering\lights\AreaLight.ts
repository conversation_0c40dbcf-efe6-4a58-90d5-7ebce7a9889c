/**
 * 区域光
 * 物理精确的区域光基类
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';

/**
 * 区域光类型枚举
 */
export enum AreaLightType {
  RECT = 'rect',
  SPHERE = 'sphere',
  DISK = 'disk',
  TUBE = 'tube'
}

/**
 * 区域光基础选项接口
 */
export interface AreaLightOptions {
  /** 光源类型 */
  type: AreaLightType;
  /** 光源颜色 */
  color?: THREE.ColorRepresentation;
  /** 光源强度 */
  intensity?: number;
  /** 是否投射阴影 */
  castShadow?: boolean;
  /** 是否显示辅助对象 */
  showHelper?: boolean;
  /** 辅助对象颜色 */
  helperColor?: THREE.ColorRepresentation;
}

/**
 * 区域光组件基类
 */
export abstract class AreaLight extends Component {
  /** 组件类型 */
  public static readonly type: string = 'AreaLight';

  /** 光源类型 */
  protected lightType: AreaLightType;

  /** Three.js光源 */
  protected light: THREE.Light | THREE.Group;

  /** 辅助对象 */
  protected helper: THREE.Object3D | null = null;

  /** 是否显示辅助对象 */
  protected showHelper: boolean;

  /** 辅助对象颜色 */
  protected helperColor: THREE.Color;

  /**
   * 创建区域光组件
   * @param options 区域光选项
   */
  constructor(options: AreaLightOptions) {
    super(AreaLight.type);

    this.lightType = options.type;
    this.showHelper = options.showHelper !== undefined ? options.showHelper : true;
    this.helperColor = new THREE.Color(options.helperColor !== undefined ? options.helperColor : 0xffffff);

    // 创建光源（由子类实现）
    this.light = this.createLight(options);

    // 创建辅助对象（由子类实现）
    if (this.showHelper) {
      this.helper = this.createHelper();
    }
  }

  /**
   * 创建光源
   * @param options 区域光选项
   * @returns Three.js光源
   */
  protected abstract createLight(options: AreaLightOptions): THREE.Light | THREE.Group;

  /**
   * 创建辅助对象
   * @returns Three.js辅助对象
   */
  protected abstract createHelper(): THREE.Object3D;

  /**
   * 当组件附加到实体时调用
   */
  protected onAttach(): void {
    if (!this.entity) return;

    // 获取实体的变换组件
    const transform = this.entity.getTransform();
    if (transform) {
      // 将光源添加到变换的Three.js对象
      transform.getObject3D().add(this.light);

      // 添加辅助对象
      if (this.helper && this.showHelper) {
        transform.getObject3D().add(this.helper);
      }
    }
  }

  /**
   * 当组件从实体分离时调用
   */
  protected onDetach(): void {
    // 清理资源
    if (this.helper) {
      if (this.helper.parent) {
        this.helper.parent.remove(this.helper);
      }
      
      // 如果辅助对象有dispose方法，则调用
      if ((this.helper as any).dispose) {
        (this.helper as any).dispose();
      }
      
      this.helper = null;
    }
  }

  /**
   * 获取光源类型
   * @returns 光源类型
   */
  public getType(): AreaLightType {
    return this.lightType;
  }

  /**
   * 获取Three.js光源
   * @returns Three.js光源
   */
  public getThreeLight(): THREE.Light | THREE.Group {
    return this.light;
  }

  /**
   * 获取辅助对象
   * @returns Three.js辅助对象
   */
  public getHelper(): THREE.Object3D | null {
    return this.helper;
  }

  /**
   * 设置是否显示辅助对象
   * @param show 是否显示
   */
  public setShowHelper(show: boolean): void {
    this.showHelper = show;

    if (this.helper) {
      this.helper.visible = show;
    } else if (show) {
      this.helper = this.createHelper();
      
      if (this.entity) {
        const transform = this.entity.getTransform();
        if (transform) {
          transform.getObject3D().add(this.helper);
        }
      }
    }
  }

  /**
   * 设置辅助对象颜色
   * @param color 颜色
   */
  public setHelperColor(color: THREE.ColorRepresentation): void {
    this.helperColor.set(color);

    // 更新辅助对象颜色（由子类实现）
    this.updateHelperColor();
  }

  /**
   * 更新辅助对象颜色
   */
  protected abstract updateHelperColor(): void;

  /**
   * 设置光源颜色
   * @param color 颜色
   */
  public setColor(color: THREE.ColorRepresentation): void {
    if (this.light instanceof THREE.Light) {
      this.light.color.set(color);
    } else if (this.light instanceof THREE.Group) {
      // 对于组合光源，设置所有子光源的颜色
      this.light.children.forEach(child => {
        if (child instanceof THREE.Light) {
          child.color.set(color);
        }
      });
    }
  }

  /**
   * 设置光源强度
   * @param intensity 强度
   */
  public setIntensity(intensity: number): void {
    if (this.light instanceof THREE.Light) {
      this.light.intensity = intensity;
    } else if (this.light instanceof THREE.Group) {
      // 对于组合光源，平均分配强度给所有子光源
      const lightCount = this.light.children.filter(child => child instanceof THREE.Light).length;
      const lightIntensity = lightCount > 0 ? intensity / lightCount : 0;

      this.light.children.forEach(child => {
        if (child instanceof THREE.Light) {
          child.intensity = lightIntensity;
        }
      });
    }
  }

  /**
   * 设置是否投射阴影
   * @param castShadow 是否投射阴影
   */
  public setCastShadow(castShadow: boolean): void {
    if (this.light instanceof THREE.Light && 'castShadow' in this.light) {
      (this.light as any).castShadow = castShadow;
    } else if (this.light instanceof THREE.Group) {
      // 对于组合光源，设置所有子光源的阴影
      this.light.children.forEach(child => {
        if (child instanceof THREE.Light && 'castShadow' in child) {
          (child as any).castShadow = castShadow;
        }
      });
    }
  }

  /**
   * 更新组件
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 由子类实现
  }
}
