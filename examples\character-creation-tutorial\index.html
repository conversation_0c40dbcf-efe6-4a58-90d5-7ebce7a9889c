<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>角色创建教程 - DL（Digital Learning）引擎</title>
  <link rel="stylesheet" href="styles/main.css">
</head>
<body>
  <div id="canvas-container"></div>
  <div id="ui-container">
    <div id="tutorial-panel">
      <div class="panel-header">
        <h2>角色创建教程</h2>
        <div class="step-indicators">
          <div id="step-indicator-1" class="step-indicator active"></div>
          <div id="step-indicator-2" class="step-indicator"></div>
          <div id="step-indicator-3" class="step-indicator"></div>
          <div id="step-indicator-4" class="step-indicator"></div>
          <div id="step-indicator-5" class="step-indicator"></div>
          <div id="step-indicator-6" class="step-indicator"></div>
          <div id="step-indicator-7" class="step-indicator"></div>
        </div>
      </div>
      <div class="panel-content">
        <div id="step-title" class="step-title"></div>
        <div id="step-description" class="step-description"></div>
        <div id="step-actions" class="step-actions"></div>
      </div>
      <div class="panel-footer">
        <button id="prev-btn" class="nav-button">上一步</button>
        <button id="reset-btn" class="nav-button">重置</button>
        <button id="next-btn" class="nav-button">下一步</button>
      </div>
    </div>
    
    <div id="properties-panel">
      <div class="panel-header">
        <h2>属性面板</h2>
      </div>
      <div class="panel-content">
        <div id="properties-content" class="properties-content">
          <div class="property-group">
            <h3>基本属性</h3>
            <div class="property-row">
              <label for="character-name">名称:</label>
              <input type="text" id="character-name" value="角色">
            </div>
            <div class="property-row">
              <label for="character-scale">缩放:</label>
              <input type="range" id="character-scale" min="0.5" max="2" step="0.1" value="1">
              <span id="character-scale-value">1.0</span>
            </div>
          </div>
          
          <div class="property-group">
            <h3>变换</h3>
            <div class="property-row">
              <label>位置:</label>
              <div class="vector-input">
                <input type="number" id="position-x" step="0.1" value="0">
                <input type="number" id="position-y" step="0.1" value="0">
                <input type="number" id="position-z" step="0.1" value="0">
              </div>
            </div>
            <div class="property-row">
              <label>旋转:</label>
              <div class="vector-input">
                <input type="number" id="rotation-x" step="1" value="0">
                <input type="number" id="rotation-y" step="1" value="0">
                <input type="number" id="rotation-z" step="1" value="0">
              </div>
            </div>
          </div>
          
          <div class="property-group">
            <h3>物理属性</h3>
            <div class="property-row">
              <label for="collision-type">碰撞类型:</label>
              <select id="collision-type">
                <option value="capsule">胶囊体</option>
                <option value="box">盒体</option>
                <option value="sphere">球体</option>
                <option value="mesh">网格</option>
              </select>
            </div>
            <div class="property-row">
              <label for="mass">质量:</label>
              <input type="number" id="mass" min="0" step="1" value="70">
              <span>kg</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script type="module">
    import { TutorialManager } from './scripts/TutorialManager.js';
    import { CharacterImporter } from './scripts/CharacterImporter.js';
    import { SkeletonSetup } from './scripts/SkeletonSetup.js';
    import { AnimationManager } from './scripts/AnimationManager.js';
    import { StateMachineEditor } from './scripts/StateMachineEditor.js';
    import { CharacterController } from './scripts/CharacterController.js';
    import { InteractionManager } from './scripts/InteractionManager.js';
    
    // 初始化引擎
    const engine = {
      container: document.getElementById('canvas-container'),
      width: window.innerWidth,
      height: window.innerHeight,
      antialias: true,
      shadows: true
    };
    
    // 创建世界
    const world = {
      gravity: { x: 0, y: -9.8, z: 0 },
      timeScale: 1.0
    };
    
    // 创建场景
    const scene = {
      name: '角色创建教程场景',
      background: { type: 'color', value: '#87CEEB' }
    };
    
    // 初始化教程管理器
    const tutorialManager = new TutorialManager();
    
    // 更新属性面板
    function updatePropertiesPanel() {
      // 获取输入元素
      const characterNameInput = document.getElementById('character-name');
      const characterScaleInput = document.getElementById('character-scale');
      const characterScaleValue = document.getElementById('character-scale-value');
      const positionXInput = document.getElementById('position-x');
      const positionYInput = document.getElementById('position-y');
      const positionZInput = document.getElementById('position-z');
      const rotationXInput = document.getElementById('rotation-x');
      const rotationYInput = document.getElementById('rotation-y');
      const rotationZInput = document.getElementById('rotation-z');
      const collisionTypeSelect = document.getElementById('collision-type');
      const massInput = document.getElementById('mass');
      
      // 设置事件监听器
      characterScaleInput.addEventListener('input', () => {
        characterScaleValue.textContent = characterScaleInput.value;
      });
    }
    
    // 初始化UI
    function initUI() {
      updatePropertiesPanel();
      
      // 设置窗口大小变化事件
      window.addEventListener('resize', () => {
        // 更新引擎大小
        engine.width = window.innerWidth;
        engine.height = window.innerHeight;
      });
    }
    
    // 初始化应用
    function init() {
      // 初始化UI
      initUI();
    }
    
    // 启动应用
    init();
  </script>
</body>
</html>
