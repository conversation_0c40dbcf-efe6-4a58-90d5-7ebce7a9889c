/**
 * 输入映射
 * 用于将输入设备的原始输入映射到逻辑输入
 */
import { InputDevice } from './InputDevice';
/**
 * 输入映射类型
 */
export declare enum InputMappingType {
    /** 按钮映射 */
    BUTTON = "button",
    /** 轴映射 */
    AXIS = "axis",
    /** 向量映射 */
    VECTOR = "vector",
    /** 组合映射 */
    COMPOSITE = "composite"
}
/**
 * 输入映射接口
 */
export interface InputMapping {
    /**
     * 获取映射名称
     * @returns 映射名称
     */
    getName(): string;
    /**
     * 获取映射类型
     * @returns 映射类型
     */
    getType(): InputMappingType;
    /**
     * 获取设备名称
     * @returns 设备名称
     */
    getDeviceName(): string;
    /**
     * 评估映射
     * @param device 输入设备
     * @returns 映射值
     */
    evaluate(device: InputDevice): any;
}
/**
 * 输入映射基类
 */
export declare abstract class BaseInputMapping implements InputMapping {
    /** 映射名称 */
    protected name: string;
    /** 映射类型 */
    protected type: InputMappingType;
    /** 设备名称 */
    protected deviceName: string;
    /**
     * 创建输入映射
     * @param name 映射名称
     * @param type 映射类型
     * @param deviceName 设备名称
     */
    constructor(name: string, type: InputMappingType, deviceName: string);
    /**
     * 获取映射名称
     * @returns 映射名称
     */
    getName(): string;
    /**
     * 获取映射类型
     * @returns 映射类型
     */
    getType(): InputMappingType;
    /**
     * 获取设备名称
     * @returns 设备名称
     */
    getDeviceName(): string;
    /**
     * 评估映射
     * @param device 输入设备
     * @returns 映射值
     */
    abstract evaluate(device: InputDevice): any;
}
/**
 * 按钮映射
 */
export declare class ButtonInputMapping extends BaseInputMapping {
    /** 按钮键名 */
    private buttonKey;
    /**
     * 创建按钮映射
     * @param name 映射名称
     * @param deviceName 设备名称
     * @param buttonKey 按钮键名
     */
    constructor(name: string, deviceName: string, buttonKey: string);
    /**
     * 评估映射
     * @param device 输入设备
     * @returns 按钮状态
     */
    evaluate(device: InputDevice): boolean;
}
/**
 * 轴映射
 */
export declare class AxisInputMapping extends BaseInputMapping {
    /** 轴键名 */
    private axisKey;
    /** 轴缩放 */
    private scale;
    /** 轴偏移 */
    private offset;
    /** 轴死区 */
    private deadZone;
    /**
     * 创建轴映射
     * @param name 映射名称
     * @param deviceName 设备名称
     * @param axisKey 轴键名
     * @param scale 轴缩放
     * @param offset 轴偏移
     * @param deadZone 轴死区
     */
    constructor(name: string, deviceName: string, axisKey: string, scale?: number, offset?: number, deadZone?: number);
    /**
     * 评估映射
     * @param device 输入设备
     * @returns 轴值
     */
    evaluate(device: InputDevice): number;
}
/**
 * 向量映射
 */
export declare class VectorInputMapping extends BaseInputMapping {
    /** X轴键名 */
    private xAxisKey;
    /** Y轴键名 */
    private yAxisKey;
    /** 轴缩放 */
    private scale;
    /** 轴死区 */
    private deadZone;
    /**
     * 创建向量映射
     * @param name 映射名称
     * @param deviceName 设备名称
     * @param xAxisKey X轴键名
     * @param yAxisKey Y轴键名
     * @param scale 轴缩放
     * @param deadZone 轴死区
     */
    constructor(name: string, deviceName: string, xAxisKey: string, yAxisKey: string, scale?: number, deadZone?: number);
    /**
     * 评估映射
     * @param device 输入设备
     * @returns 向量值 [x, y]
     */
    evaluate(device: InputDevice): [number, number];
}
