/**
 * 输入绑定
 * 用于将输入映射绑定到输入动作
 */
/**
 * 输入绑定接口
 */
export interface IInputBinding {
    /**
     * 获取绑定名称
     * @returns 绑定名称
     */
    getName(): string;
    /**
     * 获取映射名称
     * @returns 映射名称
     */
    getMappingName(): string;
}
/**
 * 输入绑定
 */
export declare class InputBinding implements IInputBinding {
    /** 绑定名称 */
    private name;
    /** 映射名称 */
    private mappingName;
    /**
     * 创建输入绑定
     * @param name 绑定名称
     * @param mappingName 映射名称
     */
    constructor(name: string, mappingName: string);
    /**
     * 获取绑定名称
     * @returns 绑定名称
     */
    getName(): string;
    /**
     * 获取映射名称
     * @returns 映射名称
     */
    getMappingName(): string;
}
/**
 * 组合输入绑定
 * 用于将多个输入映射绑定到一个输入动作
 */
export declare class CompositeInputBinding implements IInputBinding {
    /** 绑定名称 */
    private name;
    /** 映射名称列表 */
    private mappingNames;
    /** 组合类型 */
    private compositeType;
    /**
     * 创建组合输入绑定
     * @param name 绑定名称
     * @param mappingNames 映射名称列表
     * @param compositeType 组合类型
     */
    constructor(name: string, mappingNames: string[], compositeType?: CompositeBindingType);
    /**
     * 获取绑定名称
     * @returns 绑定名称
     */
    getName(): string;
    /**
     * 获取映射名称
     * @returns 映射名称
     */
    getMappingName(): string;
    /**
     * 获取所有映射名称
     * @returns 映射名称列表
     */
    getMappingNames(): string[];
    /**
     * 获取组合类型
     * @returns 组合类型
     */
    getCompositeType(): CompositeBindingType;
}
/**
 * 组合绑定类型
 */
export declare enum CompositeBindingType {
    /** 任意一个映射满足条件即可 */
    ANY = "any",
    /** 所有映射都必须满足条件 */
    ALL = "all",
    /** 按顺序优先使用第一个满足条件的映射 */
    PRIORITY = "priority"
}
