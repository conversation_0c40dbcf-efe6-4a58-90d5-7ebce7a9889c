/**
 * 性能测试配置
 */
import { SharedArray } from 'k6/data';
import { sleep } from 'k6';

// 测试环境配置
export const CONFIG = {
  apiGateway: __ENV.API_GATEWAY_URL || 'http://localhost:3000',
  userService: __ENV.USER_SERVICE_URL || 'http://localhost:4001',
  projectService: __ENV.PROJECT_SERVICE_URL || 'http://localhost:4002',
  assetService: __ENV.ASSET_SERVICE_URL || 'http://localhost:4003',
  renderService: __ENV.RENDER_SERVICE_URL || 'http://localhost:4004',
};

// 测试用户数据
export const TEST_USERS = new SharedArray('test-users', function () {
  return Array(100).fill(0).map((_, i) => ({
    username: `perf_user_${i}`,
    password: 'Test@123456',
    email: `perf_user_${i}@example.com`,
  }));
});

// 测试项目数据
export const TEST_PROJECTS = new SharedArray('test-projects', function () {
  return Array(50).fill(0).map((_, i) => ({
    name: `perf_project_${i}`,
    description: `性能测试项目 ${i}`,
  }));
});

// 负载配置
export const LOAD_PROFILES = {
  // 轻量级负载 - 适合开发环境测试
  light: {
    stages: [
      { duration: '30s', target: 10 }, // 逐渐增加到10个虚拟用户
      { duration: '1m', target: 10 },  // 保持10个虚拟用户1分钟
      { duration: '30s', target: 0 },  // 逐渐减少到0个虚拟用户
    ],
    thresholds: {
      http_req_duration: ['p(95)<500'], // 95%的请求应该在500ms内完成
      http_req_failed: ['rate<0.01'],   // 失败率应该小于1%
    },
  },
  
  // 中等负载 - 适合测试环境测试
  medium: {
    stages: [
      { duration: '1m', target: 50 },   // 逐渐增加到50个虚拟用户
      { duration: '3m', target: 50 },   // 保持50个虚拟用户3分钟
      { duration: '1m', target: 0 },    // 逐渐减少到0个虚拟用户
    ],
    thresholds: {
      http_req_duration: ['p(95)<1000'], // 95%的请求应该在1000ms内完成
      http_req_failed: ['rate<0.05'],    // 失败率应该小于5%
    },
  },
  
  // 高负载 - 适合生产环境测试
  heavy: {
    stages: [
      { duration: '2m', target: 100 },  // 逐渐增加到100个虚拟用户
      { duration: '5m', target: 100 },  // 保持100个虚拟用户5分钟
      { duration: '2m', target: 200 },  // 逐渐增加到200个虚拟用户
      { duration: '5m', target: 200 },  // 保持200个虚拟用户5分钟
      { duration: '2m', target: 0 },    // 逐渐减少到0个虚拟用户
    ],
    thresholds: {
      http_req_duration: ['p(95)<2000'], // 95%的请求应该在2000ms内完成
      http_req_failed: ['rate<0.1'],     // 失败率应该小于10%
    },
  },
  
  // 压力测试 - 测试系统极限
  stress: {
    stages: [
      { duration: '2m', target: 100 },  // 逐渐增加到100个虚拟用户
      { duration: '5m', target: 100 },  // 保持100个虚拟用户5分钟
      { duration: '2m', target: 200 },  // 逐渐增加到200个虚拟用户
      { duration: '5m', target: 200 },  // 保持200个虚拟用户5分钟
      { duration: '2m', target: 300 },  // 逐渐增加到300个虚拟用户
      { duration: '5m', target: 300 },  // 保持300个虚拟用户5分钟
      { duration: '2m', target: 400 },  // 逐渐增加到400个虚拟用户
      { duration: '5m', target: 400 },  // 保持400个虚拟用户5分钟
      { duration: '10m', target: 0 },   // 逐渐减少到0个虚拟用户
    ],
    thresholds: {
      http_req_duration: ['p(95)<5000'], // 95%的请求应该在5000ms内完成
    },
  },
  
  // 耐久测试 - 长时间运行测试系统稳定性
  endurance: {
    stages: [
      { duration: '5m', target: 100 },  // 逐渐增加到100个虚拟用户
      { duration: '2h', target: 100 },  // 保持100个虚拟用户2小时
      { duration: '5m', target: 0 },    // 逐渐减少到0个虚拟用户
    ],
    thresholds: {
      http_req_duration: ['p(95)<2000'], // 95%的请求应该在2000ms内完成
      http_req_failed: ['rate<0.05'],    // 失败率应该小于5%
    },
  },
};

// 辅助函数
export function randomSleep(min = 1, max = 5) {
  sleep(Math.random() * (max - min) + min);
}

export function getRandomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// 检查响应
export function checkResponse(response, expectedStatus = 200) {
  if (response.status !== expectedStatus) {
    console.error(`请求失败: ${response.status} ${response.body}`);
    return false;
  }
  return true;
}
