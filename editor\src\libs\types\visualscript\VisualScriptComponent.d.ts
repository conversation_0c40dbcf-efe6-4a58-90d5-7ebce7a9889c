/**
 * 视觉脚本组件
 * 用于将视觉脚本附加到实体上
 */
import { Component } from '../core/Component';
import { GraphJSON } from './graph/GraphJSON';
/**
 * 视觉脚本组件配置
 */
export interface VisualScriptComponentOptions {
    /** 视觉脚本JSON数据 */
    script?: GraphJSON;
    /** 是否自动运行 */
    autoRun?: boolean;
    /** 是否禁用 */
    disabled?: boolean;
    /** 脚本域 */
    domain?: string;
}
/**
 * 视觉脚本组件
 * 用于将视觉脚本附加到实体上
 */
export declare class VisualScriptComponent extends Component {
    /** 组件类型 */
    static readonly TYPE = "VisualScript";
    /** 视觉脚本JSON数据 */
    private _script;
    /** 是否正在运行 */
    private _running;
    /** 是否禁用 */
    private _disabled;
    /** 脚本域 */
    private _domain;
    /**
     * 创建视觉脚本组件
     * @param options 组件选项
     */
    constructor(options?: VisualScriptComponentOptions);
    /**
     * 获取视觉脚本JSON数据
     */
    get script(): GraphJSON | null;
    /**
     * 设置视觉脚本JSON数据
     */
    set script(value: GraphJSON | null);
    /**
     * 获取是否正在运行
     */
    get running(): boolean;
    /**
     * 设置是否运行
     */
    set running(value: boolean);
    /**
     * 获取是否禁用
     */
    get disabled(): boolean;
    /**
     * 设置是否禁用
     */
    set disabled(value: boolean);
    /**
     * 获取脚本域
     */
    get domain(): string;
    /**
     * 设置脚本域
     */
    set domain(value: string);
    /**
     * 开始运行视觉脚本
     */
    play(): void;
    /**
     * 暂停运行视觉脚本
     */
    pause(): void;
    /**
     * 切换运行状态
     */
    toggle(): void;
    /**
     * 序列化组件
     */
    serialize(): any;
    /**
     * 反序列化组件
     * @param data 序列化数据
     */
    deserialize(data: any): void;
}
