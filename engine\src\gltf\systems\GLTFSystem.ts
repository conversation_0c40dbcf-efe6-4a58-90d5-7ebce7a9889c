/**
 * GLTF系统
 * 用于管理GLTF模型的加载、处理和动画
 */
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { GLTFLoader } from '../GLTFLoader';
import { GLTFExporter } from '../GLTFExporter';
import { GLTFModelComponent } from '../components/GLTFModelComponent';
import { GLTFAnimationComponent } from '../components/GLTFAnimationComponent';

/**
 * GLTF系统选项
 */
export interface GLTFSystemOptions {
  /** 是否自动播放动画 */
  autoPlayAnimations?: boolean;
  /** 是否使用Draco压缩 */
  useDraco?: boolean;
  /** Draco解码器路径 */
  dracoDecoderPath?: string;
  /** 是否使用KTX2纹理 */
  useKTX2?: boolean;
  /** KTX2解码器路径 */
  ktx2DecoderPath?: string;
  /** 是否优化几何体 */
  optimizeGeometry?: boolean;
}

/**
 * GLTF系统
 */
export class GLTFSystem extends System {
  /** 系统类型 */
  public static readonly type: string = 'GLTFSystem';

  /** GLTF加载器 */
  private loader: GLTFLoader;

  /** GLTF导出器 */
  private exporter: GLTFExporter;

  /** 是否自动播放动画 */
  private autoPlayAnimations: boolean;

  /** 加载中的模型 */
  private loadingModels: Map<string, Promise<any>> = new Map();

  /** 已加载的模型 */
  private loadedModels: Map<string, any> = new Map();

  /** 实体到URL的映射 */
  private entityToUrl: Map<string, string> = new Map();

  /**
   * 创建GLTF系统
   * @param options GLTF系统选项
   */
  constructor(options: GLTFSystemOptions = {}) {
    super(5); // 优先级5，在渲染系统之前，物理系统之后更新

    // 创建GLTF加载器
    this.loader = new GLTFLoader({
      useDraco: options.useDraco !== undefined ? options.useDraco : true,
      dracoDecoderPath: options.dracoDecoderPath,
      useKTX2: options.useKTX2 !== undefined ? options.useKTX2 : true,
      ktx2DecoderPath: options.ktx2DecoderPath,
      optimizeGeometry: options.optimizeGeometry !== undefined ? options.optimizeGeometry : true,
    });

    // 创建GLTF导出器
    this.exporter = new GLTFExporter();

    // 设置选项
    this.autoPlayAnimations = options.autoPlayAnimations !== undefined ? options.autoPlayAnimations : true;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.engine) {
      // 获取世界中的所有实体
      const world = this.engine.getWorld();

      // 查找具有GLTF组件的实体
      const entities = world.getAllEntities();
      for (const entity of entities) {
        this.setupEntityGLTF(entity);
      }

      // 监听实体创建事件
      world.on('entityCreated', this.handleEntityCreated.bind(this));

      // 监听实体移除事件
      world.on('entityRemoved', this.handleEntityRemoved.bind(this));
    }
  }

  /**
   * 处理实体创建事件
   * @param entity 创建的实体
   */
  private handleEntityCreated(entity: Entity): void {
    this.setupEntityGLTF(entity);
  }

  /**
   * 处理实体移除事件
   * @param entity 移除的实体
   */
  private handleEntityRemoved(entity: Entity): void {
    // 移除实体到URL的映射
    this.entityToUrl.delete(entity.id);
  }

  /**
   * 设置实体的GLTF
   * @param entity 实体
   */
  private setupEntityGLTF(entity: Entity): void {
    if (!entity) return;

    // 检查实体是否有GLTF模型组件
    const modelComponent = entity.getComponent<GLTFModelComponent>(GLTFModelComponent.type);
    if (modelComponent) {
      // 如果已加载，则不需要再次加载
      if (modelComponent.isLoaded()) {
        return;
      }

      // 获取URL
      const url = modelComponent.getURL();
      if (url) {
        // 记录实体到URL的映射
        this.entityToUrl.set(entity.id, url);

        // 加载模型
        this.loadModel(url).then((gltf) => {
          // 设置GLTF模型
          modelComponent.setGLTF(gltf);

          // 创建实体
          this.loader.createEntity(gltf, entity);

          // 初始化动画
          const animationComponent = entity.getComponent<GLTFAnimationComponent>(GLTFAnimationComponent.type);
          if (animationComponent) {
            // 获取实体的变换组件
            const transform = entity.getTransform();
            if (transform) {
              // 初始化动画混合器
              animationComponent.initMixer(transform.getObject3D());

              // 如果启用自动播放动画，则播放第一个动画
              if (this.autoPlayAnimations && gltf.animations && gltf.animations.length > 0) {
                animationComponent.play(gltf.animations[0].name);
              }
            }
          }
        }).catch((error) => {
          console.error(`加载GLTF模型失败: ${error.message}`);
          modelComponent.setError(error);
        });
      }
    }
  }

  /**
   * 加载模型
   * @param url 模型URL
   * @returns Promise，解析为加载的GLTF模型
   */
  public async loadModel(url: string): Promise<any> {
    // 检查是否已加载
    if (this.loadedModels.has(url)) {
      return this.loadedModels.get(url)!;
    }

    // 检查是否正在加载
    if (this.loadingModels.has(url)) {
      return this.loadingModels.get(url)!;
    }

    // 加载模型
    const loadPromise = this.loader.load(url).then((gltf) => {
      // 缓存模型
      this.loadedModels.set(url, gltf);

      // 移除加载中的记录
      this.loadingModels.delete(url);

      return gltf;
    });

    // 记录加载中的模型
    this.loadingModels.set(url, loadPromise);

    return loadPromise;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.engine) return;

    // 获取世界中的所有实体
    const world = this.engine.getWorld();

    // 更新所有具有GLTF动画组件的实体
    const entities = world.getAllEntities();
    for (const entity of entities) {
      const animationComponent = entity.getComponent<GLTFAnimationComponent>(GLTFAnimationComponent.type);
      if (animationComponent && animationComponent.isEnabled()) {
        animationComponent.update(deltaTime);
      }
    }
  }

  /**
   * 导出场景
   * @param scene 场景
   * @param options 导出选项
   * @returns Promise，解析为导出的GLTF数据
   */
  public async exportScene(scene: any, options: any = {}): Promise<ArrayBuffer | object> {
    return this.exporter.exportScene(scene, options);
  }

  /**
   * 导出实体
   * @param entity 实体
   * @param options 导出选项
   * @returns Promise，解析为导出的GLTF数据
   */
  public async exportEntity(entity: Entity, options: any = {}): Promise<ArrayBuffer | object> {
    return this.exporter.exportEntity(entity, options);
  }

  /**
   * 获取GLTF加载器
   * @returns GLTF加载器
   */
  public getLoader(): GLTFLoader {
    return this.loader;
  }

  /**
   * 获取GLTF导出器
   * @returns GLTF导出器
   */
  public getExporter(): GLTFExporter {
    return this.exporter;
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清除加载中的模型
    this.loadingModels.clear();

    // 清除已加载的模型
    this.loadedModels.clear();

    // 清除实体到URL的映射
    this.entityToUrl.clear();

    // 销毁加载器
    (this.loader as any).dispose();

    // 调用父类销毁方法
    super.dispose();
  }
}
