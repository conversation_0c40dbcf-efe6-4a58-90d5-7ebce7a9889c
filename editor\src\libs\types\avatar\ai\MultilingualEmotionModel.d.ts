/**
 * 多语言情感分析模型
 * 支持多种语言的情感分析
 */
import { EmotionAnalysisResult } from './EmotionBasedAnimationGenerator';
import { EmotionAnalysisRequest } from './BERTEmotionModel';
import { BERTEmotionModel } from './BERTEmotionModel';
import { ChineseDialectType } from './ChineseBERTEmotionModel';
/**
 * 支持的语言类型
 */
export declare enum SupportedLanguage {
    /** 英语 */
    ENGLISH = "en",
    /** 中文 */
    CHINESE = "zh",
    /** 日语 */
    JAPANESE = "ja",
    /** 韩语 */
    KOREAN = "ko",
    /** 法语 */
    FRENCH = "fr",
    /** 德语 */
    GERMAN = "de",
    /** 西班牙语 */
    SPANISH = "es",
    /** 俄语 */
    RUSSIAN = "ru",
    /** 阿拉伯语 */
    ARABIC = "ar",
    /** 自动检测 */
    AUTO = "auto"
}
/**
 * 多语言情感分析模型配置
 */
export interface MultilingualEmotionModelConfig {
    /** 是否使用远程API */
    useRemoteAPI?: boolean;
    /** 远程API URL */
    remoteAPIUrl?: string;
    /** API密钥 */
    apiKey?: string;
    /** 模型路径 */
    modelPath?: string;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存大小 */
    cacheSize?: number;
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用量化模型 */
    useQuantized?: boolean;
    /** 量化位数 */
    quantizationBits?: 8 | 16 | 32;
    /** 批处理大小 */
    batchSize?: number;
    /** 模型变体 */
    modelVariant?: 'base' | 'large' | 'distilled';
    /** 情感类别 */
    emotionCategories?: string[];
    /** 是否使用多标签分类 */
    useMultiLabel?: boolean;
    /** 置信度阈值 */
    confidenceThreshold?: number;
    /** 默认语言 */
    defaultLanguage?: SupportedLanguage;
    /** 是否自动检测语言 */
    autoDetectLanguage?: boolean;
    /** 是否使用上下文分析 */
    useContextAnalysis?: boolean;
    /** 上下文窗口大小 */
    contextWindowSize?: number;
    /** 中文方言类型 */
    chineseDialectType?: ChineseDialectType;
}
/**
 * 多语言情感分析模型
 */
export declare class MultilingualEmotionModel extends BERTEmotionModel {
    /** 语言检测器 */
    private languageDetector;
    /** 语言特定模型 */
    private languageModels;
    /** 默认语言 */
    private defaultLanguage;
    /** 是否自动检测语言 */
    private autoDetectLanguage;
    /** 上下文历史 */
    private contextHistory;
    /** 上下文窗口大小 */
    private contextWindowSize;
    /** 中文方言类型 */
    private chineseDialectType;
    /** 多语言配置 */
    private multilingualConfig;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: MultilingualEmotionModelConfig);
    /**
     * 初始化
     * @returns 是否成功初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 初始化语言检测器
     */
    private initLanguageDetector;
    /**
     * 检测语言
     * @param text 文本
     * @returns 检测到的语言
     */
    private detectLanguage;
    /**
     * 分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    analyzeEmotion(text: string, options?: Partial<Omit<EmotionAnalysisRequest, 'text'>>): Promise<EmotionAnalysisResult>;
    /**
     * 更新上下文历史
     * @param text 文本
     * @param language 语言
     * @param emotion 情感
     */
    private updateContextHistory;
}
