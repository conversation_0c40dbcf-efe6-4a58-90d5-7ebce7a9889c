/**
 * 二维混合空间
 * 用于在二维参数空间中混合多个动画
 */
import * as THREE from 'three';
import type { AnimationClip } from './AnimationClip';
/**
 * 混合空间节点
 */
export interface BlendSpaceNode {
    /** 动画片段 */
    clip: AnimationClip;
    /** 位置 */
    position: THREE.Vector2;
    /** 权重 */
    weight: number;
    /** 用户数据 */
    userData?: any;
}
/**
 * 混合空间配置
 */
export interface BlendSpace2DConfig {
    /** X轴最小值 */
    minX: number;
    /** X轴最大值 */
    maxX: number;
    /** Y轴最小值 */
    minY: number;
    /** Y轴最大值 */
    maxY: number;
    /** 是否规范化输入值 */
    normalizeInput?: boolean;
    /** 是否使用三角形混合 */
    useTriangulation?: boolean;
}
/**
 * 二维混合空间
 */
export declare class BlendSpace2D {
    /** 配置 */
    private config;
    /** 节点列表 */
    private nodes;
    /** 当前位置 */
    private position;
    /** 临时向量 */
    private tempVec2;
    /** 三角形列表（用于三角形混合） */
    private triangles;
    /** 是否已三角化 */
    private triangulated;
    /**
     * 构造函数
     * @param config 混合空间配置
     */
    constructor(config: BlendSpace2DConfig);
    /**
     * 添加节点
     * @param clip 动画片段
     * @param position 位置
     * @param userData 用户数据
     * @returns 添加的节点
     */
    addNode(clip: AnimationClip, position: THREE.Vector2, userData?: any): BlendSpaceNode;
    /**
     * 移除节点
     * @param node 要移除的节点
     * @returns 是否成功移除
     */
    removeNode(node: BlendSpaceNode): boolean;
    /**
     * 设置当前位置
     * @param x X坐标
     * @param y Y坐标
     */
    setPosition(x: number, y: number): void;
    /**
     * 获取当前位置
     * @returns 当前位置
     */
    getPosition(): THREE.Vector2;
    /**
     * 更新混合权重
     */
    update(): void;
    /**
     * 使用三角形混合更新权重
     */
    private updateTriangulationWeights;
    /**
     * 使用距离加权更新权重
     */
    private updateDistanceWeights;
    /**
     * 三角化节点
     */
    private triangulate;
    /**
     * 检查点是否在三角形内
     * @param p 点
     * @param a 三角形顶点A
     * @param b 三角形顶点B
     * @param c 三角形顶点C
     * @returns 是否在三角形内
     */
    private pointInTriangle;
    /**
     * 计算重心坐标
     * @param p 点
     * @param a 三角形顶点A
     * @param b 三角形顶点B
     * @param c 三角形顶点C
     * @returns 重心坐标 (u, v, w)
     */
    private calculateBarycentricWeights;
    /**
     * 获取所有节点
     * @returns 节点列表
     */
    getNodes(): BlendSpaceNode[];
    /**
     * 获取活跃节点（权重大于0的节点）
     * @returns 活跃节点列表
     */
    getActiveNodes(): BlendSpaceNode[];
}
