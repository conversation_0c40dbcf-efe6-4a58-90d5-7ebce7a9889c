import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { MediaStreamType } from './MediaStreamManager';
import { UserRole, UserPermission } from './UserSessionManager';
import { BandwidthControlStrategy } from './BandwidthController';
import { CompressionAlgorithm, CompressionLevel } from './DataCompressor';
import { ServiceInstance } from './ServiceDiscoveryClient';
/**
 * 网络系统配置选项
 */
export interface NetworkSystemOptions {
    /** 是否自动连接到服务器 */
    autoConnect?: boolean;
    /** 服务器URL */
    serverUrl?: string;
    /** 房间ID */
    roomId?: string;
    /** 用户ID */
    userId?: string;
    /** 用户名 */
    username?: string;
    /** 是否启用WebRTC */
    enableWebRTC?: boolean;
    /** ICE服务器配置 */
    iceServers?: RTCIceServer[];
    /** 最大重连次数 */
    maxReconnectAttempts?: number;
    /** 重连间隔（毫秒） */
    reconnectInterval?: number;
    /** 数据同步间隔（毫秒） */
    syncInterval?: number;
    /** 是否启用数据压缩 */
    enableCompression?: boolean;
    /** 是否启用媒体流 */
    enableMediaStream?: boolean;
    /** 是否启用音频 */
    enableAudio?: boolean;
    /** 是否启用视频 */
    enableVideo?: boolean;
    /** 是否启用屏幕共享 */
    enableScreenShare?: boolean;
    /** 是否启用网络质量监控 */
    enableNetworkQualityMonitor?: boolean;
    /** 是否启用带宽控制 */
    enableBandwidthControl?: boolean;
    /** 带宽控制策略 */
    bandwidthControlStrategy?: BandwidthControlStrategy;
    /** 最大上行带宽（字节/秒） */
    maxUploadBandwidth?: number;
    /** 最大下行带宽（字节/秒） */
    maxDownloadBandwidth?: number;
    /** 压缩算法 */
    compressionAlgorithm?: CompressionAlgorithm;
    /** 压缩级别 */
    compressionLevel?: CompressionLevel;
    /** 是否启用实体同步 */
    enableEntitySync?: boolean;
    /** 是否启用用户会话管理 */
    enableUserSessionManagement?: boolean;
    /** 默认用户角色 */
    defaultUserRole?: UserRole;
    /** 是否启用权限检查 */
    enablePermissionCheck?: boolean;
    /** 是否启用事件缓冲 */
    enableEventBuffer?: boolean;
    /** 是否启用事件日志 */
    enableEventLogging?: boolean;
    /** 是否启用服务发现 */
    enableServiceDiscovery?: boolean;
    /** 服务注册中心URL */
    serviceRegistryUrl?: string;
    /** 是否启用微服务通信 */
    enableMicroserviceClient?: boolean;
    /** API网关URL */
    apiGatewayUrl?: string;
    /** 是否使用API网关 */
    useApiGateway?: boolean;
}
/**
 * 网络系统状态
 */
export declare enum NetworkState {
    /** 已断开连接 */
    DISCONNECTED = "disconnected",
    /** 正在连接 */
    CONNECTING = "connecting",
    /** 已连接 */
    CONNECTED = "connected",
    /** 正在断开连接 */
    DISCONNECTING = "disconnecting",
    /** 连接错误 */
    ERROR = "error"
}
/**
 * 网络系统
 * 负责管理网络连接、数据同步和多用户支持
 */
export declare class NetworkSystem extends System {
    /** 网络管理器 */
    private networkManager;
    /** 网络状态 */
    private state;
    /** 配置选项 */
    private options;
    /** 重连尝试次数 */
    private reconnectAttempts;
    /** 重连定时器ID */
    private reconnectTimerId;
    /** 同步定时器ID */
    private syncTimerId;
    /** 网络实体映射表 */
    private networkEntities;
    /** 本地用户ID */
    private localUserId;
    /** 远程用户映射表 */
    private remoteUsers;
    /** 事件发射器 */
    private eventEmitter;
    /** WebRTC连接管理器 */
    private webRTCConnectionManager;
    /** 媒体流管理器 */
    private mediaStreamManager;
    /** 实体同步管理器 */
    private entitySyncManager;
    /** 用户会话管理器 */
    private userSessionManager;
    /** 网络事件分发器 */
    private networkEventDispatcher;
    /** 带宽控制器 */
    private bandwidthController;
    /** 网络质量监控器 */
    private networkQualityMonitor;
    /** 数据压缩器 */
    private dataCompressor;
    /** 服务发现客户端 */
    private serviceDiscoveryClient;
    /** 微服务客户端 */
    private microserviceClient;
    /**
     * 创建网络系统
     * @param options 配置选项
     */
    constructor(options?: NetworkSystemOptions);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 连接到服务器
     * @param serverUrl 服务器URL
     * @param roomId 房间ID
     */
    connect(serverUrl: string, roomId?: string): void;
    /**
     * 断开连接
     */
    disconnect(): void;
    /**
     * 获取网络状态
     * @returns 网络状态
     */
    getState(): NetworkState;
    /**
     * 获取本地用户ID
     * @returns 本地用户ID
     */
    getLocalUserId(): string | null;
    /**
     * 设置本地用户ID
     * @param userId 用户ID
     */
    setLocalUserId(userId: string): void;
    /**
     * 获取远程用户
     * @param userId 用户ID
     * @returns 用户实体
     */
    getRemoteUser(userId: string): Entity | undefined;
    /**
     * 获取所有远程用户
     * @returns 用户实体映射表
     */
    getRemoteUsers(): Map<string, Entity>;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    on(event: string, listener: (...args: any[]) => void): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器函数
     */
    off(event: string, listener: (...args: any[]) => void): this;
    /**
     * 发送消息到所有用户
     * @param type 消息类型
     * @param data 消息数据
     */
    sendToAll(type: string, data: any): Promise<void>;
    /**
     * 发送消息到特定用户
     * @param userId 用户ID
     * @param type 消息类型
     * @param data 消息数据
     */
    sendToUser(userId: string, type: string, data: any): Promise<void>;
    /**
     * 创建WebRTC连接
     * @param userId 远程用户ID
     * @returns WebRTC连接
     */
    createWebRTCConnection(userId: string): any;
    /**
     * 处理WebRTC提议
     * @param userId 远程用户ID
     * @param offer 提议
     */
    handleWebRTCOffer(userId: string, offer: RTCSessionDescriptionInit): void;
    /**
     * 处理WebRTC应答
     * @param userId 远程用户ID
     * @param answer 应答
     */
    handleWebRTCAnswer(userId: string, answer: RTCSessionDescriptionInit): void;
    /**
     * 处理WebRTC ICE候选
     * @param userId 远程用户ID
     * @param candidate ICE候选
     */
    handleWebRTCIceCandidate(userId: string, candidate: RTCIceCandidateInit): void;
    /**
     * 获取本地媒体流
     * @param type 媒体流类型
     * @param config 媒体流配置
     * @returns 媒体流信息
     */
    getLocalMediaStream(type: MediaStreamType, config?: any): Promise<any>;
    /**
     * 停止本地媒体流
     * @param streamId 流ID
     * @returns 是否成功停止
     */
    stopLocalMediaStream(streamId: string): boolean;
    /**
     * 添加远程媒体流
     * @param stream 媒体流
     * @param userId 用户ID
     * @param type 媒体流类型
     * @param config 媒体流配置
     * @returns 媒体流信息
     */
    addRemoteMediaStream(stream: MediaStream, userId: string, type: MediaStreamType, config?: any): any;
    /**
     * 移除远程媒体流
     * @param streamId 流ID
     * @returns 是否成功移除
     */
    removeRemoteMediaStream(streamId: string): boolean;
    /**
     * 获取网络质量
     * @returns 网络质量数据
     */
    getNetworkQuality(): any;
    /**
     * 获取带宽使用情况
     * @returns 带宽使用数据
     */
    getBandwidthUsage(): any;
    /**
     * 设置带宽控制策略
     * @param strategy 带宽控制策略
     */
    setBandwidthControlStrategy(strategy: BandwidthControlStrategy): void;
    /**
     * 设置最大上行带宽
     * @param bandwidth 带宽（字节/秒）
     */
    setMaxUploadBandwidth(bandwidth: number): void;
    /**
     * 设置最大下行带宽
     * @param bandwidth 带宽（字节/秒）
     */
    setMaxDownloadBandwidth(bandwidth: number): void;
    /**
     * 添加网络实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    addNetworkEntity(entityId: string, entity: Entity): void;
    /**
     * 移除网络实体
     * @param entityId 实体ID
     */
    removeNetworkEntity(entityId: string): void;
    /**
     * 更新网络实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    updateNetworkEntity(entityId: string, entity: Entity): void;
    /**
     * 标记实体需要同步
     * @param entityId 实体ID
     */
    markEntityForSync(entityId: string): void;
    /**
     * 设置实体同步优先级
     * @param entityId 实体ID
     * @param priority 优先级
     */
    setEntitySyncPriority(entityId: string, priority: number): void;
    /**
     * 设置实体同步间隔
     * @param entityId 实体ID
     * @param interval 同步间隔（毫秒）
     */
    setEntitySyncInterval(entityId: string, interval: number): void;
    /**
     * 创建用户会话
     * @param userId 用户ID
     * @param username 用户名
     * @param role 角色
     * @param isAuthenticated 是否已验证
     * @returns 用户会话
     */
    createUserSession(userId: string, username: string, role?: UserRole, isAuthenticated?: boolean): any;
    /**
     * 获取用户会话
     * @param userId 用户ID
     * @returns 用户会话
     */
    getUserSession(userId: string): any;
    /**
     * 更新用户会话
     * @param userId 用户ID
     * @param updates 更新数据
     * @returns 更新后的会话
     */
    updateUserSession(userId: string, updates: any): any;
    /**
     * 移除用户会话
     * @param userId 用户ID
     * @returns 是否成功移除
     */
    removeUserSession(userId: string): boolean;
    /**
     * 检查用户是否有权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否有权限
     */
    hasPermission(userId: string, permission: UserPermission): boolean;
    /**
     * 授予用户权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否成功
     */
    grantPermission(userId: string, permission: UserPermission): boolean;
    /**
     * 撤销用户权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否成功
     */
    revokePermission(userId: string, permission: UserPermission): boolean;
    /**
     * 设置用户角色
     * @param userId 用户ID
     * @param role 角色
     * @returns 是否成功
     */
    setUserRole(userId: string, role: UserRole): boolean;
    /**
     * 销毁系统
     */
    dispose(): void;
    /**
     * 设置网络事件监听器
     */
    private setupEventListeners;
    /**
     * 处理用户加入事件
     * @param userId 用户ID
     * @param username 用户名
     */
    private handleUserJoined;
    /**
     * 处理用户离开事件
     * @param userId 用户ID
     */
    private handleUserLeft;
    /**
     * 处理实体创建事件
     * @param entityId 实体ID
     * @param data 实体数据
     */
    private handleEntityCreated;
    /**
     * 处理实体更新事件
     * @param entityId 实体ID
     * @param data 实体数据
     */
    private handleEntityUpdated;
    /**
     * 处理实体删除事件
     * @param entityId 实体ID
     */
    private handleEntityDeleted;
    /**
     * 应用实体数据
     * @param entity 实体
     * @param data 实体数据
     */
    private applyEntityData;
    /**
     * 尝试重连
     */
    private attemptReconnect;
    /**
     * 停止重连定时器
     */
    private stopReconnectTimer;
    /**
     * 启动同步定时器
     */
    private startSyncTimer;
    /**
     * 停止同步定时器
     */
    private stopSyncTimer;
    /**
     * 同步网络实体
     */
    private syncNetworkEntities;
    /**
     * 发送请求到微服务
     * @param serviceName 服务名称
     * @param endpoint 端点
     * @param options 请求选项
     * @returns 响应数据
     */
    requestService<T = any>(serviceName: string, endpoint: string, options?: any): Promise<T>;
    /**
     * 注册服务实例
     * @param serviceName 服务名称
     * @param host 主机名
     * @param port 端口
     * @param secure 是否安全连接
     * @param metadata 元数据
     * @returns 服务实例
     */
    registerService(serviceName: string, host: string, port: number, secure?: boolean, metadata?: Record<string, any>): Promise<ServiceInstance>;
    /**
     * 发现服务实例
     * @param serviceName 服务名称
     * @returns 服务实例列表
     */
    discoverService(serviceName: string): Promise<ServiceInstance[]>;
    /**
     * 设置认证令牌
     * @param token 认证令牌
     */
    setAuthToken(token: string): void;
}
