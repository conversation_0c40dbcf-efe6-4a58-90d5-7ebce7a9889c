import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export enum NotificationChannelType {
  EMAIL = 'email',
  WEBHOOK = 'webhook',
  SLACK = 'slack',
  DINGTALK = 'dingtalk',
  WECHAT = 'wechat',
}

export enum NotificationChannelStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  ERROR = 'error',
}

@Entity('notification_channels')
export class NotificationChannelEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: NotificationChannelType,
  })
  @Index()
  type: NotificationChannelType;

  @Column({
    type: 'enum',
    enum: NotificationChannelStatus,
    default: NotificationChannelStatus.ACTIVE,
  })
  @Index()
  status: NotificationChannelStatus;

  @Column('json')
  config: Record<string, any>;

  @Column('simple-array', { default: '' })
  alertSeverities: string[];

  @Column('simple-array', { default: '' })
  serviceTypes: string[];

  @Column('boolean', { default: true })
  enabled: boolean;

  @Column('int', { default: 0 })
  rateLimitPerMinute: number;

  @Column('timestamp', { nullable: true })
  lastNotificationTime: Date;

  @Column('int', { default: 0 })
  notificationCount: number;

  @Column('int', { default: 0 })
  errorCount: number;

  @Column('text', { nullable: true })
  lastError: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
