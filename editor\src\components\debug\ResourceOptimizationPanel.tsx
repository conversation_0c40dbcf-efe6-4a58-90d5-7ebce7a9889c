/**
 * 资源优化面板组件
 * 用于优化资源加载和卸载策略
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Row,
  Col,
  Tabs,
  Button,
  Slider,
  Space,
  Typography,
  Table,
  Collapse,
  Statistic,
  Tag,
  Tooltip,
  message,
  Checkbox,
  Input
} from 'antd';
import { useTranslation } from 'react-i18next';
import { Pie } from '@ant-design/charts';
import {
  ReloadOutlined,
  DeleteOutlined,
  FileImageOutlined,
  FileOutlined,
  AppstoreOutlined,
  SettingOutlined,
  RocketOutlined,
  SoundOutlined,
  BgColorsOutlined,
  CodeOutlined,
  ClearOutlined,
  AreaChartOutlined
} from '@ant-design/icons';
import EngineService from '../../services/EngineService';
import './ResourceOptimizationPanel.less';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Search } = Input;

/**
 * 资源类型
 */
enum ResourceType {
  TEXTURE = 'texture',
  MODEL = 'model',
  AUDIO = 'audio',
  MATERIAL = 'material',
  SHADER = 'shader',
  OTHER = 'other'
}

/**
 * 资源优先级
 */
enum ResourcePriority {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low'
}

/**
 * 资源状态
 */
enum ResourceState {
  LOADED = 'loaded',
  LOADING = 'loading',
  UNLOADED = 'unloaded',
  ERROR = 'error'
}

/**
 * 资源信息
 */
interface ResourceInfo {
  id: string;
  name: string;
  type: ResourceType;
  size: number;
  state: ResourceState;
  priority: ResourcePriority;
  lastAccessed: number;
  refCount: number;
  url: string;
}

/**
 * 资源优化面板组件
 */
const ResourceOptimizationPanel: React.FC = () => {
  const { t } = useTranslation();

  // 状态
  const [resources, setResources] = useState<ResourceInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedResources, setSelectedResources] = useState<string[]>([]);
  const [searchText, setSearchText] = useState('');

  const [memoryUsage, setMemoryUsage] = useState({
    total: 0,
    textures: 0,
    models: 0,
    audio: 0,
    materials: 0,
    shaders: 0,
    other: 0
  });
  const [optimizationSettings, setOptimizationSettings] = useState({
    maxCacheSize: 512, // MB
    cleanupThreshold: 0.8, // 80%
    cleanupInterval: 60, // 秒
    maxConcurrentLoads: 6,
    preloadDistance: 100, // 单位
    unloadDistance: 200, // 单位
    priorityBasedLoading: true,
    compressTextures: true,
    useTextureAtlas: true,
    useGeometryInstancing: true,
    useMaterialInstancing: true
  });

  // 引用
  const timerRef = useRef<number | null>(null);

  // 初始化
  useEffect(() => {
    // 加载资源列表
    loadResources();

    // 设置定时器定期刷新
    timerRef.current = window.setInterval(() => {
      loadResources();
    }, 5000);

    // 清理函数
    return () => {
      if (timerRef.current !== null) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);

  // 加载资源列表
  const loadResources = async () => {
    setLoading(true);

    try {
      // 获取引擎
      const engine = EngineService.getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取资源管理器
      const resourceManager = EngineService.getResourceManager();
      if (!resourceManager) {
        throw new Error('资源管理器未初始化');
      }

      // 获取资源列表 - 使用实际的资源管理器方法
      let resourceList: any[] = [];

      // 检查资源管理器是否有 getResources 方法
      if (typeof resourceManager.getResources === 'function') {
        resourceList = await resourceManager.getResources();
      } else if (typeof resourceManager.getLoadedResources === 'function') {
        // 使用 getLoadedResources 方法
        const resourceMap = resourceManager.getLoadedResources();
        const entries = Array.from(resourceMap.entries()) as [string, any][];
        resourceList = entries.map(([id, data]) => ({
          id,
          data,
          type: 'unknown',
          size: 0,
          state: 'loaded',
          priority: 0,
          lastAccessTime: Date.now(),
          refCount: 1,
          url: id
        }));
      } else {
        // 如果没有可用的方法，创建模拟数据
        resourceList = [];
      }

      // 转换为组件使用的格式
      const formattedResources: ResourceInfo[] = resourceList.map((resource: any) => ({
        id: resource.id || resource.url || 'unknown',
        name: resource.name || resource.id || resource.url || 'unknown',
        type: mapResourceType(resource.type || 'unknown'),
        size: resource.size || 0,
        state: mapResourceState(resource.state || 'loaded'),
        priority: mapResourcePriority(resource.priority || 0),
        lastAccessed: resource.lastAccessTime || Date.now(),
        refCount: resource.refCount || 1,
        url: resource.url || resource.id || 'unknown'
      }));

      // 更新资源列表
      setResources(formattedResources);

      // 更新内存使用情况
      updateMemoryUsage(formattedResources);

      // 获取优化设置 - 使用默认设置，因为资源管理器可能没有 getSettings 方法
      try {
        if (typeof resourceManager.getSettings === 'function') {
          const settings = await resourceManager.getSettings();
          if (settings) {
            setOptimizationSettings({
              maxCacheSize: settings.maxCacheSize / (1024 * 1024), // 转换为MB
              cleanupThreshold: settings.cleanupThreshold,
              cleanupInterval: settings.cleanupInterval / 1000, // 转换为秒
              maxConcurrentLoads: settings.maxConcurrentLoads,
              preloadDistance: settings.preloadDistance || 100,
              unloadDistance: settings.unloadDistance || 200,
              priorityBasedLoading: settings.priorityBasedLoading !== false,
              compressTextures: settings.compressTextures !== false,
              useTextureAtlas: settings.useTextureAtlas !== false,
              useGeometryInstancing: settings.useGeometryInstancing !== false,
              useMaterialInstancing: settings.useMaterialInstancing !== false
            });
          }
        }
      } catch (error) {
        console.warn('无法获取资源管理器设置:', error);
      }
    } catch (error) {
      console.error('加载资源列表失败:', error);
      message.error(`加载资源列表失败: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // 更新内存使用情况
  const updateMemoryUsage = (resourceList: ResourceInfo[]) => {
    const usage = {
      total: 0,
      textures: 0,
      models: 0,
      audio: 0,
      materials: 0,
      shaders: 0,
      other: 0
    };

    // 只计算已加载的资源
    const loadedResources = resourceList.filter(r => r.state === ResourceState.LOADED);

    // 计算总内存使用
    usage.total = loadedResources.reduce((sum, r) => sum + r.size, 0);

    // 按类型计算内存使用
    usage.textures = loadedResources.filter(r => r.type === ResourceType.TEXTURE).reduce((sum, r) => sum + r.size, 0);
    usage.models = loadedResources.filter(r => r.type === ResourceType.MODEL).reduce((sum, r) => sum + r.size, 0);
    usage.audio = loadedResources.filter(r => r.type === ResourceType.AUDIO).reduce((sum, r) => sum + r.size, 0);
    usage.materials = loadedResources.filter(r => r.type === ResourceType.MATERIAL).reduce((sum, r) => sum + r.size, 0);
    usage.shaders = loadedResources.filter(r => r.type === ResourceType.SHADER).reduce((sum, r) => sum + r.size, 0);
    usage.other = loadedResources.filter(r => r.type === ResourceType.OTHER).reduce((sum, r) => sum + r.size, 0);

    // 更新内存使用状态
    setMemoryUsage(usage);
  };

  // 映射资源类型
  const mapResourceType = (type: string): ResourceType => {
    switch (type.toLowerCase()) {
      case 'texture':
      case 'image':
      case 'textures':
        return ResourceType.TEXTURE;
      case 'model':
      case 'mesh':
      case 'geometry':
        return ResourceType.MODEL;
      case 'audio':
      case 'sound':
        return ResourceType.AUDIO;
      case 'material':
        return ResourceType.MATERIAL;
      case 'shader':
      case 'program':
        return ResourceType.SHADER;
      default:
        return ResourceType.OTHER;
    }
  };

  // 映射资源状态
  const mapResourceState = (state: string): ResourceState => {
    switch (state.toLowerCase()) {
      case 'loaded':
        return ResourceState.LOADED;
      case 'loading':
        return ResourceState.LOADING;
      case 'unloaded':
        return ResourceState.UNLOADED;
      case 'error':
        return ResourceState.ERROR;
      default:
        return ResourceState.UNLOADED;
    }
  };

  // 映射资源优先级
  const mapResourcePriority = (priority: number): ResourcePriority => {
    if (priority >= 70) {
      return ResourcePriority.HIGH;
    } else if (priority >= 30) {
      return ResourcePriority.MEDIUM;
    } else {
      return ResourcePriority.LOW;
    }
  };

  // 格式化字节大小
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化时间
  const formatTime = (timestamp: number): string => {
    if (!timestamp) return '-';
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  // 获取资源类型图标
  const getResourceTypeIcon = (type: ResourceType) => {
    switch (type) {
      case ResourceType.TEXTURE:
        return <FileImageOutlined />;
      case ResourceType.MODEL:
        return <AppstoreOutlined />;
      case ResourceType.AUDIO:
        return <SoundOutlined />;
      case ResourceType.MATERIAL:
        return <BgColorsOutlined />;
      case ResourceType.SHADER:
        return <CodeOutlined />;
      default:
        return <FileOutlined />;
    }
  };

  // 获取资源状态标签
  const getResourceStateTag = (state: ResourceState) => {
    switch (state) {
      case ResourceState.LOADED:
        return <Tag color="success">{t('debug.resource.loaded')}</Tag>;
      case ResourceState.LOADING:
        return <Tag color="processing">{t('debug.resource.loading')}</Tag>;
      case ResourceState.UNLOADED:
        return <Tag color="default">{t('debug.resource.unloaded')}</Tag>;
      case ResourceState.ERROR:
        return <Tag color="error">{t('debug.resource.error')}</Tag>;
      default:
        return <Tag color="default">{t('debug.resource.unknown')}</Tag>;
    }
  };

  // 获取资源优先级标签
  const getResourcePriorityTag = (priority: ResourcePriority) => {
    switch (priority) {
      case ResourcePriority.HIGH:
        return <Tag color="red">{t('debug.resource.highPriority')}</Tag>;
      case ResourcePriority.MEDIUM:
        return <Tag color="orange">{t('debug.resource.mediumPriority')}</Tag>;
      case ResourcePriority.LOW:
        return <Tag color="blue">{t('debug.resource.lowPriority')}</Tag>;
      default:
        return <Tag color="default">{t('debug.resource.unknownPriority')}</Tag>;
    }
  };

  // 过滤资源
  const getFilteredResources = () => {
    return resources.filter(resource => {
      // 搜索文本过滤
      if (searchText && !resource.name.toLowerCase().includes(searchText.toLowerCase()) && !resource.id.toLowerCase().includes(searchText.toLowerCase())) {
        return false;
      }

      return true;
    });
  };

  // 卸载选定资源
  const unloadSelectedResources = async () => {
    if (selectedResources.length === 0) {
      message.info(t('debug.resource.noResourceSelected'));
      return;
    }

    try {
      // 获取引擎
      const engine = EngineService.getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取资源管理器
      const resourceManager = EngineService.getResourceManager();
      if (!resourceManager) {
        throw new Error('资源管理器未初始化');
      }

      // 卸载选定资源
      let unloadedCount = 0;
      for (const resourceId of selectedResources) {
        try {
          // 检查资源管理器是否有 release 方法
          if (typeof resourceManager.release === 'function') {
            const success = await resourceManager.release(resourceId);
            if (success) {
              unloadedCount++;
            }
          } else if (typeof resourceManager.unloadResource === 'function') {
            // 使用 unloadResource 方法
            resourceManager.unloadResource(resourceId);
            unloadedCount++;
          } else {
            console.warn('资源管理器不支持资源卸载');
          }
        } catch (error) {
          console.warn(`卸载资源 ${resourceId} 失败:`, error);
        }
      }

      // 刷新资源列表
      loadResources();

      // 清除选择
      setSelectedResources([]);

      // 显示成功消息
      message.success(`${t('debug.resource.unloadedResources')}: ${unloadedCount}`);
    } catch (error) {
      console.error('卸载资源失败:', error);
      message.error(`${t('debug.resource.unloadFailed')}: ${(error as Error).message}`);
    }
  };

  // 清理缓存
  const cleanupCache = async () => {
    try {
      // 获取引擎
      const engine = EngineService.getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取资源管理器
      const resourceManager = EngineService.getResourceManager();
      if (!resourceManager) {
        throw new Error('资源管理器未初始化');
      }

      // 执行缓存清理
      let freedSize = 0;
      if (typeof resourceManager.cleanup === 'function') {
        freedSize = await resourceManager.cleanup();
      } else if (typeof resourceManager.clearCache === 'function') {
        resourceManager.clearCache();
        freedSize = 1024 * 1024; // 模拟释放的大小
      } else {
        console.warn('资源管理器不支持缓存清理');
      }

      // 刷新资源列表
      loadResources();

      // 显示成功消息
      message.success(`${t('debug.resource.cacheCleanupSuccess')}: ${formatBytes(freedSize)}`);
    } catch (error) {
      console.error('清理缓存失败:', error);
      message.error(`${t('debug.resource.cacheCleanupFailed')}: ${(error as Error).message}`);
    }
  };

  // 应用优化设置
  const applyOptimizationSettings = async () => {
    try {
      // 获取引擎
      const engine = EngineService.getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取资源管理器
      const resourceManager = EngineService.getResourceManager();
      if (!resourceManager) {
        throw new Error('资源管理器未初始化');
      }

      // 应用设置
      if (typeof resourceManager.updateSettings === 'function') {
        await resourceManager.updateSettings({
          maxCacheSize: optimizationSettings.maxCacheSize * 1024 * 1024, // 转换为字节
          cleanupThreshold: optimizationSettings.cleanupThreshold,
          cleanupInterval: optimizationSettings.cleanupInterval * 1000, // 转换为毫秒
          maxConcurrentLoads: optimizationSettings.maxConcurrentLoads,
          preloadDistance: optimizationSettings.preloadDistance,
          unloadDistance: optimizationSettings.unloadDistance,
          priorityBasedLoading: optimizationSettings.priorityBasedLoading,
          compressTextures: optimizationSettings.compressTextures,
          useTextureAtlas: optimizationSettings.useTextureAtlas,
          useGeometryInstancing: optimizationSettings.useGeometryInstancing,
          useMaterialInstancing: optimizationSettings.useMaterialInstancing
        });
      } else {
        console.warn('资源管理器不支持设置更新');
      }

      // 显示成功消息
      message.success(t('debug.resource.settingsApplied'));
    } catch (error) {
      console.error('应用设置失败:', error);
      message.error(`${t('debug.resource.settingsApplyFailed')}: ${(error as Error).message}`);
    }
  };

  // 优化资源
  const optimizeResources = async () => {
    try {
      // 获取引擎
      const engine = EngineService.getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取资源管理器
      const resourceManager = EngineService.getResourceManager();
      if (!resourceManager) {
        throw new Error('资源管理器未初始化');
      }

      // 执行资源优化
      let result = { optimizedCount: 0 };
      if (typeof resourceManager.optimizeResources === 'function') {
        result = await resourceManager.optimizeResources();
      } else {
        // 模拟优化结果
        result = { optimizedCount: Math.floor(Math.random() * 10) };
        console.warn('资源管理器不支持资源优化，使用模拟结果');
      }

      // 刷新资源列表
      loadResources();

      // 显示成功消息
      message.success(`${t('debug.resource.optimizationSuccess')}: ${result.optimizedCount} ${t('debug.resource.resources')}`);
    } catch (error) {
      console.error('优化资源失败:', error);
      message.error(`${t('debug.resource.optimizationFailed')}: ${(error as Error).message}`);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: t('debug.resource.name'),
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: ResourceInfo) => (
        <Space>
          {getResourceTypeIcon(record.type)}
          <Tooltip title={record.id}>
            <Text>{text}</Text>
          </Tooltip>
        </Space>
      )},
    {
      title: t('debug.resource.type'),
      dataIndex: 'type',
      key: 'type',
      filters: [
        { text: t('debug.resource.texture'), value: ResourceType.TEXTURE },
        { text: t('debug.resource.model'), value: ResourceType.MODEL },
        { text: t('debug.resource.audio'), value: ResourceType.AUDIO },
        { text: t('debug.resource.material'), value: ResourceType.MATERIAL },
        { text: t('debug.resource.shader'), value: ResourceType.SHADER },
        { text: t('debug.resource.other'), value: ResourceType.OTHER },
      ],
      onFilter: (value: boolean | React.Key, record: ResourceInfo) => record.type === value,
      render: (type: ResourceType) => t(`debug.resource.${type}`)
    },
    {
      title: t('debug.resource.size'),
      dataIndex: 'size',
      key: 'size',
      sorter: (a: ResourceInfo, b: ResourceInfo) => a.size - b.size,
      render: (size: number) => formatBytes(size)
    },
    {
      title: t('debug.resource.state'),
      dataIndex: 'state',
      key: 'state',
      filters: [
        { text: t('debug.resource.loaded'), value: ResourceState.LOADED },
        { text: t('debug.resource.loading'), value: ResourceState.LOADING },
        { text: t('debug.resource.unloaded'), value: ResourceState.UNLOADED },
        { text: t('debug.resource.error'), value: ResourceState.ERROR },
      ],
      onFilter: (value: boolean | React.Key, record: ResourceInfo) => record.state === value,
      render: (state: ResourceState) => getResourceStateTag(state)
    },
    {
      title: t('debug.resource.priority'),
      dataIndex: 'priority',
      key: 'priority',
      filters: [
        { text: t('debug.resource.highPriority'), value: ResourcePriority.HIGH },
        { text: t('debug.resource.mediumPriority'), value: ResourcePriority.MEDIUM },
        { text: t('debug.resource.lowPriority'), value: ResourcePriority.LOW },
      ],
      onFilter: (value: boolean | React.Key, record: ResourceInfo) => record.priority === value,
      render: (priority: ResourcePriority) => getResourcePriorityTag(priority)
    },
    {
      title: t('debug.resource.lastAccessed'),
      dataIndex: 'lastAccessed',
      key: 'lastAccessed',
      sorter: (a: ResourceInfo, b: ResourceInfo) => a.lastAccessed - b.lastAccessed,
      render: (time: number) => formatTime(time)
    },
    {
      title: t('debug.resource.refCount'),
      dataIndex: 'refCount',
      key: 'refCount',
      sorter: (a: ResourceInfo, b: ResourceInfo) => a.refCount - b.refCount
    }
  ];

  // 渲染内存使用图表
  const renderMemoryUsageChart = () => {
    const data = [
      { type: t('debug.resource.texture'), value: memoryUsage.textures },
      { type: t('debug.resource.model'), value: memoryUsage.models },
      { type: t('debug.resource.audio'), value: memoryUsage.audio },
      { type: t('debug.resource.material'), value: memoryUsage.materials },
      { type: t('debug.resource.shader'), value: memoryUsage.shaders },
      { type: t('debug.resource.other'), value: memoryUsage.other },
    ].filter(item => item.value > 0);

    const config = {
      appendPadding: 10,
      data,
      angleField: 'value',
      colorField: 'type',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name}: {percentage}'
      },
      interactions: [{ type: 'element-active' }]
    };

    return (
      <div className="memory-usage-chart">
        <Card title={t('debug.resource.memoryUsage')}>
          <Statistic
            title={t('debug.resource.totalMemory')}
            value={formatBytes(memoryUsage.total)}
            precision={2}
          />
          <Pie {...config} />
        </Card>
      </div>
    );
  };

  // 渲染设置面板
  const renderSettingsPanel = () => {
    return (
      <div className="settings-panel">
        <Card title={t('debug.resource.optimizationSettings')}>
          <Collapse defaultActiveKey={['cacheSettings', 'loadingSettings', 'optimizationSettings']}>
            <Panel header={t('debug.resource.cacheSettings')} key="cacheSettings">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="setting-item">
                    <Text>{t('debug.resource.maxCacheSize')}</Text>
                    <Slider
                      min={64}
                      max={2048}
                      step={64}
                      value={optimizationSettings.maxCacheSize}
                      onChange={(value) => setOptimizationSettings({ ...optimizationSettings, maxCacheSize: value })}
                    />
                    <Text type="secondary">{optimizationSettings.maxCacheSize} MB</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="setting-item">
                    <Text>{t('debug.resource.cleanupThreshold')}</Text>
                    <Slider
                      min={0.5}
                      max={0.95}
                      step={0.05}
                      value={optimizationSettings.cleanupThreshold}
                      onChange={(value) => setOptimizationSettings({ ...optimizationSettings, cleanupThreshold: value })}
                    />
                    <Text type="secondary">{(optimizationSettings.cleanupThreshold * 100).toFixed(0)}%</Text>
                  </div>
                </Col>
              </Row>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="setting-item">
                    <Text>{t('debug.resource.cleanupInterval')}</Text>
                    <Slider
                      min={10}
                      max={300}
                      step={10}
                      value={optimizationSettings.cleanupInterval}
                      onChange={(value) => setOptimizationSettings({ ...optimizationSettings, cleanupInterval: value })}
                    />
                    <Text type="secondary">{optimizationSettings.cleanupInterval} {t('debug.resource.seconds')}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="setting-item">
                    <Text>{t('debug.resource.maxConcurrentLoads')}</Text>
                    <Slider
                      min={1}
                      max={16}
                      step={1}
                      value={optimizationSettings.maxConcurrentLoads}
                      onChange={(value) => setOptimizationSettings({ ...optimizationSettings, maxConcurrentLoads: value })}
                    />
                    <Text type="secondary">{optimizationSettings.maxConcurrentLoads}</Text>
                  </div>
                </Col>
              </Row>
            </Panel>
            <Panel header={t('debug.resource.loadingSettings')} key="loadingSettings">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="setting-item">
                    <Text>{t('debug.resource.preloadDistance')}</Text>
                    <Slider
                      min={10}
                      max={500}
                      step={10}
                      value={optimizationSettings.preloadDistance}
                      onChange={(value) => setOptimizationSettings({ ...optimizationSettings, preloadDistance: value })}
                    />
                    <Text type="secondary">{optimizationSettings.preloadDistance} {t('debug.resource.units')}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="setting-item">
                    <Text>{t('debug.resource.unloadDistance')}</Text>
                    <Slider
                      min={50}
                      max={1000}
                      step={50}
                      value={optimizationSettings.unloadDistance}
                      onChange={(value) => setOptimizationSettings({ ...optimizationSettings, unloadDistance: value })}
                    />
                    <Text type="secondary">{optimizationSettings.unloadDistance} {t('debug.resource.units')}</Text>
                  </div>
                </Col>
              </Row>
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Checkbox
                    checked={optimizationSettings.priorityBasedLoading}
                    onChange={(e) => setOptimizationSettings({ ...optimizationSettings, priorityBasedLoading: e.target.checked })}
                  >
                    {t('debug.resource.priorityBasedLoading')}
                  </Checkbox>
                </Col>
              </Row>
            </Panel>
            <Panel header={t('debug.resource.optimizationSettings')} key="optimizationSettings">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Checkbox
                    checked={optimizationSettings.compressTextures}
                    onChange={(e) => setOptimizationSettings({ ...optimizationSettings, compressTextures: e.target.checked })}
                  >
                    {t('debug.resource.compressTextures')}
                  </Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox
                    checked={optimizationSettings.useTextureAtlas}
                    onChange={(e) => setOptimizationSettings({ ...optimizationSettings, useTextureAtlas: e.target.checked })}
                  >
                    {t('debug.resource.useTextureAtlas')}
                  </Checkbox>
                </Col>
              </Row>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Checkbox
                    checked={optimizationSettings.useGeometryInstancing}
                    onChange={(e) => setOptimizationSettings({ ...optimizationSettings, useGeometryInstancing: e.target.checked })}
                  >
                    {t('debug.resource.useGeometryInstancing')}
                  </Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox
                    checked={optimizationSettings.useMaterialInstancing}
                    onChange={(e) => setOptimizationSettings({ ...optimizationSettings, useMaterialInstancing: e.target.checked })}
                  >
                    {t('debug.resource.useMaterialInstancing')}
                  </Checkbox>
                </Col>
              </Row>
            </Panel>
          </Collapse>
          <div className="settings-actions">
            <Button type="primary" onClick={applyOptimizationSettings}>
              {t('debug.resource.applySettings')}
            </Button>
          </div>
        </Card>
      </div>
    );
  };

  return (
    <div className="resource-optimization-panel">
      <div className="panel-header">
        <Space>
          <Title level={4}>{t('debug.resource.title')}</Title>
          <Tooltip title={t('debug.resource.refresh')}>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadResources}
              loading={loading}
            />
          </Tooltip>
          <Tooltip title={t('debug.resource.unloadSelected')}>
            <Button
              icon={<DeleteOutlined />}
              onClick={unloadSelectedResources}
              disabled={selectedResources.length === 0}
            />
          </Tooltip>
          <Tooltip title={t('debug.resource.cleanupCache')}>
            <Button
              icon={<ClearOutlined />}
              onClick={cleanupCache}
            />
          </Tooltip>
          <Tooltip title={t('debug.resource.optimizeResources')}>
            <Button
              type="primary"
              icon={<RocketOutlined />}
              onClick={optimizeResources}
            />
          </Tooltip>
        </Space>
        <div className="search-container">
          <Search
            placeholder={t('debug.resource.searchPlaceholder') as string}
            onSearch={value => setSearchText(value)}
            style={{ width: 250 }}
          />
        </div>
      </div>

      <Tabs defaultActiveKey="resources">
        <TabPane tab={<span><AppstoreOutlined />{t('debug.resource.resourceList')}</span>} key="resources">
          <Table
            dataSource={getFilteredResources()}
            columns={columns}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
            rowSelection={{
              selectedRowKeys: selectedResources,
              onChange: (selectedRowKeys) => setSelectedResources(selectedRowKeys as string[])
            }}
          />
        </TabPane>
        <TabPane tab={<span><AreaChartOutlined />{t('debug.resource.memoryUsage')}</span>} key="memory">
          {renderMemoryUsageChart()}
        </TabPane>
        <TabPane tab={<span><SettingOutlined />{t('debug.resource.settings')}</span>} key="settings">
          {renderSettingsPanel()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ResourceOptimizationPanel;
