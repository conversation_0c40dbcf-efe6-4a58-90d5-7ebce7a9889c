import { TerrainComponent } from '../components/TerrainComponent';
/**
 * 洞穴系统生成参数
 */
export interface CaveSystemParams {
    /** 洞穴数量 */
    count: number;
    /** 洞穴最小半径 */
    minRadius: number;
    /** 洞穴最大半径 */
    maxRadius: number;
    /** 洞穴深度 */
    depth: number;
    /** 洞穴复杂度（形状不规则程度） */
    complexity: number;
    /** 洞穴连接概率 */
    connectionProbability: number;
    /** 钟乳石生成概率 */
    stalactiteProbability: number;
    /** 石笋生成概率 */
    stalagmiteProbability: number;
    /** 最小深度（相对于地形高度的比例） */
    minDepthRatio: number;
    /** 最大深度（相对于地形高度的比例） */
    maxDepthRatio: number;
    /** 种子 */
    seed: number;
}
/**
 * 洞穴节点
 */
export interface CaveNode {
    /** 中心X坐标 */
    x: number;
    /** 中心Y坐标（深度） */
    y: number;
    /** 中心Z坐标 */
    z: number;
    /** 半径 */
    radius: number;
}
/**
 * 洞穴连接
 */
export interface CaveConnection {
    /** 起始洞穴索引 */
    fromIndex: number;
    /** 目标洞穴索引 */
    toIndex: number;
    /** 连接半径 */
    radius: number;
}
/**
 * 钟乳石/石笋
 */
export interface CaveFormation {
    /** 类型（钟乳石/石笋） */
    type: 'stalactite' | 'stalagmite';
    /** X坐标 */
    x: number;
    /** Y坐标（深度） */
    y: number;
    /** Z坐标 */
    z: number;
    /** 高度 */
    height: number;
    /** 基础半径 */
    baseRadius: number;
    /** 尖端半径 */
    tipRadius: number;
}
/**
 * 洞穴系统
 */
export interface CaveSystem {
    /** 洞穴节点 */
    nodes: CaveNode[];
    /** 洞穴连接 */
    connections: CaveConnection[];
    /** 钟乳石/石笋 */
    formations: CaveFormation[];
}
/**
 * 洞穴系统生成器
 */
export declare class CaveSystemGenerator {
    /**
     * 生成洞穴系统
     * @param terrain 地形组件
     * @param params 洞穴系统生成参数
     */
    static generateCaveSystem(terrain: TerrainComponent, params: CaveSystemParams): void;
    /**
     * 生成洞穴节点
     * @param caveSystem 洞穴系统
     * @param terrain 地形组件
     * @param count 洞穴数量
     * @param minRadius 最小半径
     * @param maxRadius 最大半径
     * @param minDepthRatio 最小深度比例
     * @param maxDepthRatio 最大深度比例
     * @param random 随机数生成器
     */
    private static generateCaveNodes;
    /**
     * 生成洞穴连接
     * @param caveSystem 洞穴系统
     * @param connectionProbability 连接概率
     * @param random 随机数生成器
     */
    private static generateCaveConnections;
    /**
     * 生成钟乳石和石笋
     * @param caveSystem 洞穴系统
     * @param stalactiteProbability 钟乳石概率
     * @param stalagmiteProbability 石笋概率
     * @param random 随机数生成器
     */
    private static generateCaveFormations;
    /**
     * 生成钟乳石
     * @param caveSystem 洞穴系统
     * @param node 洞穴节点
     * @param random 随机数生成器
     */
    private static generateStalactites;
    /**
     * 生成石笋
     * @param caveSystem 洞穴系统
     * @param node 洞穴节点
     * @param random 随机数生成器
     */
    private static generateStalagmites;
    /**
     * 应用洞穴系统到地形
     * @param terrain 地形组件
     * @param caveSystem 洞穴系统
     * @param depth 深度
     * @param complexity 复杂度
     * @param random 随机数生成器
     */
    private static applyCaveSystemToTerrain;
    /**
     * 应用洞穴节点到地形
     * @param terrain 地形组件
     * @param nodes 洞穴节点
     * @param depth 深度
     * @param complexity 复杂度
     * @param random 随机数生成器
     */
    private static applyCaveNodesToTerrain;
    /**
     * 应用洞穴连接到地形
     * @param terrain 地形组件
     * @param caveSystem 洞穴系统
     * @param depth 深度
     * @param random 随机数生成器
     */
    private static applyCaveConnectionsToTerrain;
    /**
     * 应用钟乳石和石笋到地形
     * @param terrain 地形组件
     * @param formations 钟乳石和石笋
     */
    private static applyCaveFormationsToTerrain;
    /**
     * 创建随机数生成器
     * @param seed 种子
     * @returns 随机数生成器函数
     */
    private static createRandomGenerator;
}
