import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import type { PhysicsBody } from '../PhysicsBody';
import { PhysicsCollider } from '../PhysicsCollider';
import { EventEmitter } from '../../utils/EventEmitter';
/**
 * 碰撞检测器
 */
export declare class CollisionDetector extends EventEmitter {
    /** 物理世界 */
    private world;
    /** 活跃碰撞对 */
    private activePairs;
    /** 实体到物理体的映射 */
    private entityToBody;
    /** 物理体到实体的映射 */
    private bodyToEntity;
    /** 实体到碰撞器的映射 */
    private entityToCollider;
    /**
     * 创建碰撞检测器
     * @param world 物理世界
     */
    constructor(world: CANNON.World);
    /**
     * 注册实体和物理体
     * @param entity 实体
     * @param body 物理体
     */
    registerBody(entity: Entity, body: PhysicsBody): void;
    /**
     * 注销实体和物理体
     * @param entity 实体
     */
    unregisterBody(entity: Entity): void;
    /**
     * 注册实体和碰撞器
     * @param entity 实体
     * @param collider 碰撞器
     */
    registerCollider(entity: Entity, collider: PhysicsCollider): void;
    /**
     * 注销实体和碰撞器
     * @param entity 实体
     */
    unregisterCollider(entity: Entity): void;
    /**
     * 处理碰撞开始事件
     * @param event CANNON碰撞事件
     */
    private handleBeginContact;
    /**
     * 处理碰撞结束事件
     * @param event CANNON碰撞事件
     */
    private handleEndContact;
    /**
     * 更新碰撞检测器
     * @param _deltaTime 时间增量（未使用）
     */
    update(_deltaTime: number): void;
    /**
     * 通知实体碰撞事件
     * @param entity 实体
     * @param event 碰撞事件
     */
    private notifyEntity;
    /**
     * 创建碰撞对ID
     * @param idA 实体A的ID
     * @param idB 实体B的ID
     * @returns 碰撞对ID
     */
    private createPairId;
    /**
     * 清除所有碰撞对
     */
    clear(): void;
    /**
     * 销毁检测器
     */
    dispose(): void;
}
