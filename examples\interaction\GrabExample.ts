/**
 * GrabExample.ts
 * 
 * 抓取系统示例
 */

import { World, Entity, Scene, Transform, Camera } from '../../engine/src/core';
import { 
  GrabSystem, 
  GrabbableComponent, 
  GrabberComponent,
  GrabType,
  Hand,
  PhysicsGrabComponent
} from '../../engine/src/interaction';
import { InputSystem, KeyCode, MouseButton } from '../../engine/src/input';
import { PhysicsSystem, PhysicsBodyComponent, BodyType, ColliderType } from '../../engine/src/physics';
import { Debug } from '../../engine/src/utils/Debug';
import { Vector3, BoxGeometry, SphereGeometry, MeshBasicMaterial, Mesh, Color } from 'three';

/**
 * 抓取示例
 */
export class GrabExample {
  /** 世界 */
  private world: World;

  /** 场景 */
  private scene: Scene;

  /** 相机实体 */
  private cameraEntity: Entity;

  /** 玩家实体 */
  private playerEntity: Entity;

  /** 可抓取对象列表 */
  private grabbableEntities: Entity[] = [];

  /** 输入系统 */
  private inputSystem: InputSystem;

  /** 物理系统 */
  private physicsSystem: PhysicsSystem;

  /** 抓取系统 */
  private grabSystem: GrabSystem;

  /** 是否初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 创建输入系统
    this.inputSystem = new InputSystem({
      enableKeyboard: true,
      enableMouse: true
    });
    this.world.addSystem(this.inputSystem);

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.8, z: 0 },
      debug: true
    });
    this.world.addSystem(this.physicsSystem);

    // 创建抓取系统
    this.grabSystem = new GrabSystem(this.world, {
      debug: true,
      enablePhysicsGrab: true
    });
    this.world.addSystem(this.grabSystem);

    // 创建场景
    this.scene = new Scene(this.world, {
      name: '抓取示例场景'
    });

    // 创建相机
    this.cameraEntity = this.createCamera();

    // 创建玩家
    this.playerEntity = this.createPlayer();

    // 创建地面
    this.createGround();

    // 创建可抓取对象
    this.createGrabbableObjects();

    // 设置活跃场景
    this.world.setActiveScene(this.scene);

    // 设置活跃相机
    this.world.setActiveCamera(this.cameraEntity);

    // 初始化完成
    this.initialized = true;

    // 开始游戏循环
    this.gameLoop();
  }

  /**
   * 创建相机
   * @returns 相机实体
   */
  private createCamera(): Entity {
    // 创建相机实体
    const cameraEntity = new Entity(this.world);
    
    // 添加变换组件
    cameraEntity.addComponent(new Transform({
      position: new Vector3(0, 5, 10),
      rotation: new Vector3(-0.3, 0, 0)
    }));
    
    // 添加相机组件
    cameraEntity.addComponent(new Camera({
      fov: 60,
      near: 0.1,
      far: 1000,
      aspect: window.innerWidth / window.innerHeight
    }));
    
    // 添加到场景
    this.scene.addEntity(cameraEntity);
    
    return cameraEntity;
  }

  /**
   * 创建玩家
   * @returns 玩家实体
   */
  private createPlayer(): Entity {
    // 创建玩家实体
    const playerEntity = new Entity(this.world);
    
    // 添加变换组件
    playerEntity.addComponent(new Transform({
      position: new Vector3(0, 1.8, 0),
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(1, 1, 1)
    }));
    
    // 添加抓取者组件
    playerEntity.addComponent(new GrabberComponent(playerEntity, {
      maxGrabDistance: 3.0,
      onGrab: (grabber, grabbed, hand) => {
        Debug.log('抓取示例', `玩家抓取了对象 ${grabbed.id}，使用${hand === Hand.LEFT ? '左手' : '右手'}`);
      },
      onRelease: (grabber, released, hand) => {
        Debug.log('抓取示例', `玩家释放了对象 ${released.id}，使用${hand === Hand.LEFT ? '左手' : '右手'}`);
      }
    }));
    
    // 添加到场景
    this.scene.addEntity(playerEntity);
    
    return playerEntity;
  }

  /**
   * 创建地面
   */
  private createGround(): void {
    // 创建地面实体
    const groundEntity = new Entity(this.world);
    
    // 添加变换组件
    groundEntity.addComponent(new Transform({
      position: new Vector3(0, 0, 0),
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(20, 0.1, 20)
    }));
    
    // 添加物理体组件
    groundEntity.addComponent(new PhysicsBodyComponent(groundEntity, {
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 创建几何体和材质
    const geometry = new BoxGeometry(20, 0.1, 20);
    const material = new MeshBasicMaterial({ color: 0x808080 });
    const mesh = new Mesh(geometry, material);
    
    // 添加网格到实体（这里简化处理，实际应该使用渲染组件）
    (groundEntity as any).mesh = mesh;
    
    // 添加到场景
    this.scene.addEntity(groundEntity);
  }

  /**
   * 创建可抓取对象
   */
  private createGrabbableObjects(): void {
    // 创建不同类型的可抓取对象
    this.createGrabbableBox(new Vector3(-2, 1, 0), GrabType.DIRECT, 0xff0000);
    this.createGrabbableBox(new Vector3(0, 1, 0), GrabType.DISTANCE, 0x00ff00);
    this.createGrabbableBox(new Vector3(2, 1, 0), GrabType.SPRING, 0x0000ff);
    
    // 创建物理可抓取对象
    this.createPhysicsGrabbableSphere(new Vector3(-2, 3, 0), 0xff00ff);
    this.createPhysicsGrabbableSphere(new Vector3(0, 3, 0), 0xffff00);
    this.createPhysicsGrabbableSphere(new Vector3(2, 3, 0), 0x00ffff);
  }

  /**
   * 创建可抓取盒子
   * @param position 位置
   * @param grabType 抓取类型
   * @param color 颜色
   * @returns 实体
   */
  private createGrabbableBox(position: Vector3, grabType: GrabType, color: number): Entity {
    // 创建实体
    const entity = new Entity(this.world);
    
    // 添加变换组件
    entity.addComponent(new Transform({
      position: position,
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(1, 1, 1)
    }));
    
    // 添加可抓取组件
    entity.addComponent(new GrabbableComponent(entity, {
      grabType: grabType,
      allowedHands: [Hand.ANY],
      grabDistance: 1.0,
      onGrab: (entity, grabber) => {
        Debug.log('抓取示例', `盒子 ${entity.id} 被抓取`);
      },
      onRelease: (entity, grabber) => {
        Debug.log('抓取示例', `盒子 ${entity.id} 被释放`);
      }
    }));
    
    // 创建几何体和材质
    const geometry = new BoxGeometry(1, 1, 1);
    const material = new MeshBasicMaterial({ color: color });
    const mesh = new Mesh(geometry, material);
    
    // 添加网格到实体（这里简化处理，实际应该使用渲染组件）
    (entity as any).mesh = mesh;
    
    // 添加到场景
    this.scene.addEntity(entity);
    
    // 添加到可抓取对象列表
    this.grabbableEntities.push(entity);
    
    return entity;
  }

  /**
   * 创建物理可抓取球体
   * @param position 位置
   * @param color 颜色
   * @returns 实体
   */
  private createPhysicsGrabbableSphere(position: Vector3, color: number): Entity {
    // 创建实体
    const entity = new Entity(this.world);
    
    // 添加变换组件
    entity.addComponent(new Transform({
      position: position,
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(1, 1, 1)
    }));
    
    // 添加物理体组件
    entity.addComponent(new PhysicsBodyComponent(entity, {
      type: BodyType.DYNAMIC,
      mass: 1.0
    }));
    
    // 添加可抓取组件
    entity.addComponent(new GrabbableComponent(entity, {
      grabType: GrabType.DIRECT,
      allowedHands: [Hand.ANY],
      grabDistance: 1.0,
      onGrab: (entity, grabber) => {
        Debug.log('抓取示例', `球体 ${entity.id} 被抓取`);
      },
      onRelease: (entity, grabber) => {
        Debug.log('抓取示例', `球体 ${entity.id} 被释放`);
      }
    }));
    
    // 添加物理抓取组件
    entity.addComponent(new PhysicsGrabComponent(entity, {
      grabForce: 20.0,
      grabDamping: 0.8,
      grabBodyType: BodyType.KINEMATIC
    }));
    
    // 创建几何体和材质
    const geometry = new SphereGeometry(0.5, 32, 32);
    const material = new MeshBasicMaterial({ color: color });
    const mesh = new Mesh(geometry, material);
    
    // 添加网格到实体（这里简化处理，实际应该使用渲染组件）
    (entity as any).mesh = mesh;
    
    // 添加到场景
    this.scene.addEntity(entity);
    
    // 添加到可抓取对象列表
    this.grabbableEntities.push(entity);
    
    return entity;
  }

  /**
   * 游戏循环
   */
  private gameLoop(): void {
    // 如果未初始化，则返回
    if (!this.initialized) return;
    
    // 处理输入
    this.handleInput();
    
    // 更新世界
    this.world.update();
    
    // 渲染场景
    this.render();
    
    // 请求下一帧
    requestAnimationFrame(() => this.gameLoop());
  }

  /**
   * 处理输入
   */
  private handleInput(): void {
    // 获取抓取者组件
    const grabberComponent = this.playerEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    if (!grabberComponent) return;
    
    // 检测左键点击 - 左手抓取
    if (this.inputSystem.isMouseButtonPressed(MouseButton.LEFT)) {
      // 获取最近的可抓取对象
      const nearestEntity = this.getNearestGrabbableEntity();
      if (nearestEntity) {
        // 尝试抓取
        grabberComponent.grab(nearestEntity, Hand.LEFT);
      }
    }
    
    // 检测右键点击 - 右手抓取
    if (this.inputSystem.isMouseButtonPressed(MouseButton.RIGHT)) {
      // 获取最近的可抓取对象
      const nearestEntity = this.getNearestGrabbableEntity();
      if (nearestEntity) {
        // 尝试抓取
        grabberComponent.grab(nearestEntity, Hand.RIGHT);
      }
    }
    
    // 检测Q键 - 左手释放
    if (this.inputSystem.isKeyPressed(KeyCode.KEY_Q)) {
      grabberComponent.release(Hand.LEFT);
    }
    
    // 检测E键 - 右手释放
    if (this.inputSystem.isKeyPressed(KeyCode.KEY_E)) {
      grabberComponent.release(Hand.RIGHT);
    }
  }

  /**
   * 获取最近的可抓取对象
   * @returns 最近的可抓取对象
   */
  private getNearestGrabbableEntity(): Entity | undefined {
    // 获取相机位置
    const cameraTransform = this.cameraEntity.getComponent<Transform>('Transform');
    if (!cameraTransform) return undefined;
    
    // 获取玩家位置
    const playerTransform = this.playerEntity.getComponent<Transform>('Transform');
    if (!playerTransform) return undefined;
    
    // 获取抓取者组件
    const grabberComponent = this.playerEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    if (!grabberComponent) return undefined;
    
    // 最近的实体和距离
    let nearestEntity: Entity | undefined;
    let nearestDistance = grabberComponent.maxGrabDistance;
    
    // 遍历所有可抓取对象
    for (const entity of this.grabbableEntities) {
      // 获取可抓取组件
      const grabbableComponent = entity.getComponent<GrabbableComponent>(GrabbableComponent.TYPE);
      if (!grabbableComponent || !grabbableComponent.grabbable || grabbableComponent.isGrabbed) continue;
      
      // 获取实体位置
      const entityTransform = entity.getComponent<Transform>('Transform');
      if (!entityTransform) continue;
      
      // 计算距离
      const distance = new Vector3(
        entityTransform.position.x - playerTransform.position.x,
        entityTransform.position.y - playerTransform.position.y,
        entityTransform.position.z - playerTransform.position.z
      ).length();
      
      // 如果距离更近，则更新最近的实体
      if (distance < nearestDistance) {
        nearestEntity = entity;
        nearestDistance = distance;
      }
    }
    
    return nearestEntity;
  }

  /**
   * 渲染场景
   */
  private render(): void {
    // 这里应该调用渲染器渲染场景
    // 在实际项目中，这部分应该由引擎的渲染系统处理
  }
}

// 创建示例实例
new GrabExample();
