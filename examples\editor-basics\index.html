<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>编辑器基础功能演示</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      font-family: 'Microsoft YaHei', sans-serif;
    }
    #canvas-container {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    #ui-container {
      position: absolute;
      top: 10px;
      left: 10px;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 10px;
      border-radius: 5px;
      max-width: 300px;
      z-index: 10;
    }
    h2 {
      margin-top: 0;
      color: #333;
    }
    .step {
      margin-bottom: 10px;
      padding: 5px;
      background-color: #f0f0f0;
      border-left: 3px solid #4caf50;
    }
    .step-title {
      font-weight: bold;
      color: #4caf50;
    }
    .step-description {
      font-size: 14px;
      color: #555;
    }
    .controls {
      margin-top: 10px;
    }
    button {
      background-color: #4caf50;
      color: white;
      border: none;
      padding: 5px 10px;
      margin-right: 5px;
      border-radius: 3px;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <div id="canvas-container"></div>
  <div id="ui-container">
    <h2>编辑器基础功能演示</h2>
    <div id="step-1" class="step">
      <div class="step-title">步骤 1: 场景创建</div>
      <div class="step-description">
        本示例展示了如何创建一个基本场景，包括添加相机、灯光和基本几何体。
        点击"下一步"按钮继续。
      </div>
    </div>
    <div id="step-2" class="step hidden">
      <div class="step-title">步骤 2: 对象操作</div>
      <div class="step-description">
        使用鼠标左键选择对象，使用鼠标右键拖动旋转视图。
        选中对象后，可以使用键盘快捷键进行操作：
        - W: 移动工具
        - E: 旋转工具
        - R: 缩放工具
      </div>
    </div>
    <div id="step-3" class="step hidden">
      <div class="step-title">步骤 3: 属性编辑</div>
      <div class="step-description">
        选中对象后，右侧面板会显示对象的属性。
        尝试修改立方体的位置、旋转和缩放属性。
      </div>
    </div>
    <div id="step-4" class="step hidden">
      <div class="step-title">步骤 4: 材质编辑</div>
      <div class="step-description">
        选中对象后，可以在材质面板中编辑对象的材质属性。
        尝试修改立方体的颜色、金属度和粗糙度。
      </div>
    </div>
    <div id="step-5" class="step hidden">
      <div class="step-title">步骤 5: 灯光设置</div>
      <div class="step-description">
        场景中包含环境光和平行光。
        尝试修改灯光的颜色、强度和方向。
      </div>
    </div>
    <div class="controls">
      <button id="prev-btn" disabled>上一步</button>
      <button id="next-btn">下一步</button>
      <button id="reset-btn">重置场景</button>
    </div>
  </div>

  <script type="module">
    import { Engine, World, Entity, Scene, Transform, Camera, Mesh, Material, Light, PhysicsSystem } from '/engine/dist/index.js';

    // 初始化引擎
    const engine = new Engine({
      container: document.getElementById('canvas-container'),
      antialias: true,
      shadows: true,
    });

    // 创建世界
    const world = new World(engine);

    // 创建场景
    const scene = new Scene(world, {
      name: '编辑器基础功能演示场景',
      background: { type: 'color', value: '#87CEEB' },
    });

    // 创建相机
    const camera = new Entity(world)
      .addComponent(new Camera({
        type: 'perspective',
        fov: 60,
        near: 0.1,
        far: 1000,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 2, z: 5 },
        rotation: { x: -0.2, y: 0, z: 0 },
      }));

    // 添加相机到场景
    scene.addEntity(camera);

    // 创建环境光
    const ambientLight = new Entity(world)
      .addComponent(new Light({
        type: 'ambient',
        color: '#ffffff',
        intensity: 0.3,
      }));

    // 添加环境光到场景
    scene.addEntity(ambientLight);

    // 创建平行光
    const directionalLight = new Entity(world)
      .addComponent(new Light({
        type: 'directional',
        color: '#ffffff',
        intensity: 0.8,
        castShadow: true,
      }))
      .addComponent(new Transform({
        position: { x: 5, y: 10, z: 5 },
        rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
      }));

    // 添加平行光到场景
    scene.addEntity(directionalLight);

    // 创建地面
    const ground = new Entity(world)
      .addComponent(new Mesh({
        geometry: { type: 'plane', width: 20, height: 20 },
        receiveShadow: true,
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#cccccc',
        roughness: 0.9,
        metalness: 0.1,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: -Math.PI / 2, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      }));

    // 添加地面到场景
    scene.addEntity(ground);

    // 创建立方体
    const cube = new Entity(world)
      .addComponent(new Mesh({
        geometry: { type: 'box', width: 1, height: 1, depth: 1 },
        castShadow: true,
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#ff0000',
        roughness: 0.5,
        metalness: 0.5,
      }))
      .addComponent(new Transform({
        position: { x: -2, y: 0.5, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      }));

    // 添加立方体到场景
    scene.addEntity(cube);

    // 创建球体
    const sphere = new Entity(world)
      .addComponent(new Mesh({
        geometry: { type: 'sphere', radius: 0.5, widthSegments: 32, heightSegments: 32 },
        castShadow: true,
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#00ff00',
        roughness: 0.2,
        metalness: 0.8,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 0.5, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      }));

    // 添加球体到场景
    scene.addEntity(sphere);

    // 创建圆柱体
    const cylinder = new Entity(world)
      .addComponent(new Mesh({
        geometry: { type: 'cylinder', radiusTop: 0.5, radiusBottom: 0.5, height: 1, radialSegments: 32 },
        castShadow: true,
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#0000ff',
        roughness: 0.3,
        metalness: 0.7,
      }))
      .addComponent(new Transform({
        position: { x: 2, y: 0.5, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      }));

    // 添加圆柱体到场景
    scene.addEntity(cylinder);

    // 启动引擎
    engine.start();

    // 步骤导航
    let currentStep = 1;
    const totalSteps = 5;

    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const resetBtn = document.getElementById('reset-btn');

    function updateSteps() {
      for (let i = 1; i <= totalSteps; i++) {
        const stepElement = document.getElementById(`step-${i}`);
        if (i === currentStep) {
          stepElement.classList.remove('hidden');
        } else {
          stepElement.classList.add('hidden');
        }
      }

      prevBtn.disabled = currentStep === 1;
      nextBtn.disabled = currentStep === totalSteps;
    }

    prevBtn.addEventListener('click', () => {
      if (currentStep > 1) {
        currentStep--;
        updateSteps();
      }
    });

    nextBtn.addEventListener('click', () => {
      if (currentStep < totalSteps) {
        currentStep++;
        updateSteps();
      }
    });

    resetBtn.addEventListener('click', () => {
      // 重置场景
      location.reload();
    });
  </script>
</body>
</html>
