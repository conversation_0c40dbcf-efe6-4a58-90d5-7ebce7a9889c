/**
 * 国际化类
 * 提供多语言支持
 */
import { EventEmitter } from '../utils/EventEmitter';
export interface I18nOptions {
    /** 默认语言 */
    language?: string;
    /** 回退语言 */
    fallbackLanguage?: string;
    /** 语言资源 */
    resources?: Record<string, Record<string, string>>;
}
export declare class I18n extends EventEmitter {
    /** 当前语言 */
    private language;
    /** 回退语言 */
    private fallbackLanguage;
    /** 语言资源 */
    private resources;
    /**
     * 创建国际化实例
     * @param options 国际化选项
     */
    constructor(options?: I18nOptions);
    /**
     * 加载默认语言资源
     */
    private loadDefaultResources;
    /**
     * 添加语言资源
     * @param language 语言
     * @param resources 资源
     */
    addResources(language: string, resources: Record<string, string>): void;
    /**
     * 设置当前语言
     * @param language 语言
     */
    setLanguage(language: string): void;
    /**
     * 获取当前语言
     * @returns 当前语言
     */
    getLanguage(): string;
    /**
     * 设置回退语言
     * @param language 语言
     */
    setFallbackLanguage(language: string): void;
    /**
     * 获取回退语言
     * @returns 回退语言
     */
    getFallbackLanguage(): string;
    /**
     * 翻译文本
     * @param key 翻译键
     * @param params 参数
     * @returns 翻译后的文本
     */
    translate(key: string, params?: Record<string, any>): string;
    /**
     * 获取翻译
     * @param language 语言
     * @param key 翻译键
     * @returns 翻译文本
     */
    private getTranslation;
    /**
     * 替换参数
     * @param text 文本
     * @param params 参数
     * @returns 替换后的文本
     */
    private replaceParams;
    /**
     * 获取支持的语言列表
     * @returns 语言列表
     */
    getLanguages(): string[];
    /**
     * 是否支持指定语言
     * @param language 语言
     * @returns 是否支持
     */
    hasLanguage(language: string): boolean;
    /**
     * 获取语言资源
     * @param language 语言
     * @returns 语言资源
     */
    getResources(language: string): Record<string, string>;
}
