/**
 * 喷泉组件
 * 用于表示喷泉及其特殊物理属性和效果
 */
import * as THREE from 'three';
import { WaterBodyComponent } from './WaterBodyComponent';
import { Entity } from '../../core/Entity';
/**
 * 喷泉配置
 */
export interface FountainConfig {
    /** 喷泉宽度 */
    width?: number;
    /** 喷泉高度 */
    height?: number;
    /** 喷泉深度 */
    depth?: number;
    /** 喷泉位置 */
    position?: THREE.Vector3;
    /** 喷泉旋转 */
    rotation?: THREE.Euler;
    /** 喷泉颜色 */
    color?: THREE.Color;
    /** 喷泉不透明度 */
    opacity?: number;
    /** 喷泉流速 */
    flowSpeed?: number;
    /** 喷泉流向 */
    flowDirection?: THREE.Vector3;
    /** 喷泉湍流强度 */
    turbulenceStrength?: number;
    /** 喷泉湍流频率 */
    turbulenceFrequency?: number;
    /** 喷泉湍流速度 */
    turbulenceSpeed?: number;
    /** 是否启用水雾效果 */
    enableMistEffect?: boolean;
    /** 水雾效果强度 */
    mistEffectStrength?: number;
    /** 是否启用水花效果 */
    enableSplashEffect?: boolean;
    /** 水花效果强度 */
    splashEffectStrength?: number;
    /** 是否启用水滴效果 */
    enableDropletEffect?: boolean;
    /** 水滴效果强度 */
    dropletEffectStrength?: number;
    /** 是否启用声音效果 */
    enableSoundEffect?: boolean;
    /** 声音效果音量 */
    soundEffectVolume?: number;
    /** 是否启用水流动力学 */
    enableFluidDynamics?: boolean;
    /** 喷泉类型 */
    fountainType?: FountainType;
    /** 喷泉模式 */
    fountainMode?: FountainMode;
    /** 喷泉喷射高度 */
    jetHeight?: number;
    /** 喷泉喷射角度 */
    jetAngle?: number;
    /** 喷泉喷射数量 */
    jetCount?: number;
    /** 喷泉喷射间隔 */
    jetInterval?: number;
    /** 喷泉喷射持续时间 */
    jetDuration?: number;
    /** 喷泉喷射延迟 */
    jetDelay?: number;
    /** 喷泉喷射随机性 */
    jetRandomness?: number;
    /** 是否启用 */
    enabled?: boolean;
}
/**
 * 喷泉类型
 */
export declare enum FountainType {
    /** 标准喷泉 */
    STANDARD = "standard",
    /** 多喷头喷泉 */
    MULTI_JET = "multi_jet",
    /** 舞蹈喷泉 */
    DANCING = "dancing",
    /** 音乐喷泉 */
    MUSICAL = "musical",
    /** 交互式喷泉 */
    INTERACTIVE = "interactive",
    /** 螺旋喷泉 */
    SPIRAL = "spiral",
    /** 花朵喷泉 */
    FLOWER = "flower",
    /** 瀑布喷泉 */
    WATERFALL = "waterfall",
    /** 圆顶喷泉 */
    DOME = "dome",
    /** 交叉喷泉 */
    CROSSING = "crossing"
}
/**
 * 喷泉模式
 */
export declare enum FountainMode {
    /** 连续模式 */
    CONTINUOUS = "continuous",
    /** 脉冲模式 */
    PULSE = "pulse",
    /** 交替模式 */
    ALTERNATING = "alternating",
    /** 序列模式 */
    SEQUENCE = "sequence",
    /** 随机模式 */
    RANDOM = "random",
    /** 波浪模式 */
    WAVE = "wave",
    /** 级联模式 */
    CASCADE = "cascade",
    /** 爆发模式 */
    BURST = "burst",
    /** 和谐模式 */
    HARMONIC = "harmonic",
    /** 混沌模式 */
    CHAOTIC = "chaotic"
}
/**
 * 喷泉组件
 */
export declare class FountainComponent extends WaterBodyComponent {
    /** 喷泉湍流强度 */
    private turbulenceStrength;
    /** 喷泉湍流频率 */
    private turbulenceFrequency;
    /** 喷泉湍流速度 */
    private turbulenceSpeed;
    /** 是否启用水雾效果 */
    private enableMistEffect;
    /** 水雾效果强度 */
    private mistEffectStrength;
    /** 是否启用水花效果 */
    private enableSplashEffect;
    /** 水花效果强度 */
    private splashEffectStrength;
    /** 是否启用水滴效果 */
    private enableDropletEffect;
    /** 水滴效果强度 */
    private dropletEffectStrength;
    /** 是否启用声音效果 */
    private enableSoundEffect;
    /** 声音效果音量 */
    private soundEffectVolume;
    /** 是否启用水流动力学 */
    private enableFluidDynamics;
    /** 喷泉类型 */
    private fountainType;
    /** 喷泉模式 */
    private fountainMode;
    /** 喷泉喷射高度 */
    private jetHeight;
    /** 喷泉喷射角度 */
    private jetAngle;
    /** 喷泉喷射数量 */
    private jetCount;
    /** 喷泉喷射间隔 */
    private jetInterval;
    /** 喷泉喷射持续时间 */
    private jetDuration;
    /** 喷泉喷射延迟 */
    private jetDelay;
    /** 喷泉喷射随机性 */
    private jetRandomness;
    /** 音频源 */
    private audioSource;
    /** 水雾粒子系统 */
    private mistParticleSystem;
    /** 水花粒子系统 */
    private splashParticleSystem;
    /** 水滴粒子系统 */
    private dropletParticleSystem;
    /** 水流路径点 */
    private flowPathPoints;
    /** 水流网格 */
    private flowMesh;
    /** 底部水体 */
    private bottomWaterBody;
    /** 喷泉喷射计时器 */
    private jetTimer;
    /** 喷泉喷射状态 */
    private jetActive;
    /** 喷泉喷射持续计时器 */
    private jetDurationTimer;
    /** 喷泉模式计时器 */
    private modeTimer;
    /** 上次随机时间 */
    private lastRandomTime;
    /** 喷泉喷射索引 */
    private currentJetIndex;
    /** 喷泉喷射活跃状态数组 */
    private jetActiveArray;
    /** 噪声生成器 */
    private noiseGenerator;
    /** 喷泉喷射点 */
    private jetPoints;
    /** 喷泉喷射方向 */
    private jetDirections;
    /** 喷泉喷射强度 */
    private jetStrengths;
    /**
     * 创建喷泉组件
     * @param entity 实体
     * @param config 喷泉配置
     */
    constructor(entity: Entity, config?: FountainConfig);
    /**
     * 应用配置
     * @param config 喷泉配置
     */
    private applyConfig;
    /**
     * 初始化喷泉组件
     */
    initialize(): void;
    /**
     * 初始化喷泉喷射点
     */
    private initializeJetPoints;
    /**
     * 创建水流网格
     */
    private createFlowMesh;
    /**
     * 初始化音频
     */
    private initializeAudio;
    /**
     * 初始化粒子系统
     */
    private initializeParticleSystems;
    /**
     * 创建底部水体
     */
    private createBottomWaterBody;
    /**
     * 更新喷泉组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新喷泉喷射
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateJets;
    /**
     * 发射喷射粒子
     */
    private emitJetParticles;
    /**
     * 更新水流动力学
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateFluidDynamics;
    /**
     * 应用湍流效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private applyTurbulence;
    /**
     * 更新音频
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateAudio;
    /**
     * 更新粒子效果
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateParticleEffects;
    /**
     * 3D Simplex噪声函数
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     * @returns 噪声值
     */
    private simplex3D;
    /**
     * 设置喷泉类型
     * @param type 喷泉类型
     */
    setFountainType(type: FountainType): void;
    /**
     * 设置喷泉模式
     * @param mode 喷泉模式
     */
    setFountainMode(mode: FountainMode): void;
    /**
     * 设置喷泉喷射高度
     * @param height 喷射高度
     */
    setJetHeight(height: number): void;
    /**
     * 设置喷泉喷射角度
     * @param angle 喷射角度
     */
    setJetAngle(angle: number): void;
    /**
     * 设置喷泉喷射数量
     * @param count 喷射数量
     */
    setJetCount(count: number): void;
    /**
     * 设置喷泉喷射间隔
     * @param interval 喷射间隔
     */
    setJetInterval(interval: number): void;
    /**
     * 设置喷泉喷射持续时间
     * @param duration 喷射持续时间
     */
    setJetDuration(duration: number): void;
    /**
     * 设置喷泉喷射随机性
     * @param randomness 喷射随机性
     */
    setJetRandomness(randomness: number): void;
    /**
     * 设置湍流强度
     * @param strength 湍流强度
     */
    setTurbulenceStrength(strength: number): void;
    /**
     * 设置湍流频率
     * @param frequency 湍流频率
     */
    setTurbulenceFrequency(frequency: number): void;
    /**
     * 设置湍流速度
     * @param speed 湍流速度
     */
    setTurbulenceSpeed(speed: number): void;
    /**
     * 设置是否启用水雾效果
     * @param enable 是否启用
     */
    setEnableMistEffect(enable: boolean): void;
    /**
     * 设置水雾效果强度
     * @param strength 效果强度
     */
    setMistEffectStrength(strength: number): void;
    /**
     * 设置是否启用水花效果
     * @param enable 是否启用
     */
    setEnableSplashEffect(enable: boolean): void;
    /**
     * 设置水花效果强度
     * @param strength 效果强度
     */
    setSplashEffectStrength(strength: number): void;
    /**
     * 设置是否启用水滴效果
     * @param enable 是否启用
     */
    setEnableDropletEffect(enable: boolean): void;
    /**
     * 设置水滴效果强度
     * @param strength 效果强度
     */
    setDropletEffectStrength(strength: number): void;
    /**
     * 设置是否启用声音效果
     * @param enable 是否启用
     */
    setEnableSoundEffect(enable: boolean): void;
    /**
     * 设置声音效果音量
     * @param volume 音量
     */
    setSoundEffectVolume(volume: number): void;
    /**
     * 设置是否启用水流动力学
     * @param enable 是否启用
     */
    setEnableFluidDynamics(enable: boolean): void;
}
