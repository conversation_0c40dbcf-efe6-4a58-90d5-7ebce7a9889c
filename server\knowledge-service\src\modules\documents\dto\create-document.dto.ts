import { IsString, IsUUID, IsOptional, IsEnum } from 'class-validator';

export class CreateDocumentDto {
  @IsString()
  filename: string;

  @IsString()
  @IsOptional()
  originalFilename?: string;

  @IsUUID()
  knowledgeBaseId: string;

  @IsString()
  @IsOptional()
  filePath?: string;

  @IsString()
  @IsOptional()
  fileType?: string;

  @IsOptional()
  fileSize?: number;

  @IsOptional()
  metadata?: any;

  @IsEnum(['pending', 'processing', 'completed', 'failed'])
  @IsOptional()
  processingStatus?: string;

  @IsUUID()
  @IsOptional()
  uploadedBy?: string;
}
