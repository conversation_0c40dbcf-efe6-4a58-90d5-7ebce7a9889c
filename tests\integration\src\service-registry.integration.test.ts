import axios from 'axios';
import waitForExpect from 'wait-for-expect';
import { TEST_CONFIG } from './setup';

describe('服务注册与发现集成测试', () => {
  const testServiceName = `test-service-${Date.now()}`;
  const testInstances = [
    {
      instanceId: `test-instance-1-${Date.now()}`,
      host: 'localhost',
      port: 3001,
      metadata: { version: '1.0.0', weight: 1 },
    },
    {
      instanceId: `test-instance-2-${Date.now()}`,
      host: 'localhost',
      port: 3002,
      metadata: { version: '1.0.0', weight: 2 },
    },
    {
      instanceId: `test-instance-3-${Date.now()}`,
      host: 'localhost',
      port: 3003,
      metadata: { version: '1.0.0', weight: 1 },
    },
  ];

  // 注册的实例ID列表
  const registeredInstanceIds = [];

  // 在所有测试开始前注册测试服务实例
  beforeAll(async () => {
    for (const instance of testInstances) {
      try {
        const response = await axios.post(
          `${TEST_CONFIG.serviceRegistry.url}/registry/services`,
          {
            name: testServiceName,
            instanceId: instance.instanceId,
            host: instance.host,
            port: instance.port,
            metadata: instance.metadata,
          }
        );

        if (response.status === 201 || response.status === 200) {
          registeredInstanceIds.push(instance.instanceId);
          console.log(`已注册服务实例: ${instance.instanceId}`);
        }
      } catch (error) {
        console.error(`注册服务实例失败: ${error.message}`);
      }
    }

    // 等待服务注册完成
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  // 在所有测试结束后注销测试服务实例
  afterAll(async () => {
    for (const instanceId of registeredInstanceIds) {
      try {
        await axios.delete(
          `${TEST_CONFIG.serviceRegistry.url}/registry/services/${testServiceName}/instances/${instanceId}`
        );
        console.log(`已注销服务实例: ${instanceId}`);
      } catch (error) {
        console.error(`注销服务实例失败: ${error.message}`);
      }
    }
  });

  test('应该能够发现所有注册的服务实例', async () => {
    const response = await axios.get(
      `${TEST_CONFIG.serviceRegistry.url}/registry/services/${testServiceName}/instances`
    );

    expect(response.status).toBe(200);
    expect(response.data).toBeInstanceOf(Array);
    expect(response.data.length).toBe(testInstances.length);

    // 验证所有注册的实例都能被发现
    for (const instance of testInstances) {
      const found = response.data.some(i => i.instanceId === instance.instanceId);
      expect(found).toBe(true);
    }
  });

  test('应该能够使用随机负载均衡策略', async () => {
    const instanceCounts = {};
    const totalRequests = 100;

    // 发送多个请求，使用随机负载均衡
    for (let i = 0; i < totalRequests; i++) {
      const response = await axios.get(
        `${TEST_CONFIG.serviceRegistry.url}/registry/services/${testServiceName}/instances`,
        {
          params: {
            algorithm: 'random',
          },
        }
      );

      expect(response.status).toBe(200);
      expect(response.data).toBeInstanceOf(Array);
      expect(response.data.length).toBeGreaterThan(0);

      const instanceId = response.data[0].instanceId;
      instanceCounts[instanceId] = (instanceCounts[instanceId] || 0) + 1;
    }

    // 验证所有实例都被选择过
    for (const instance of testInstances) {
      expect(instanceCounts[instance.instanceId]).toBeGreaterThan(0);
    }
  });

  test('应该能够使用一致性哈希负载均衡策略', async () => {
    const sessionId = `test-session-${Date.now()}`;
    const instanceIds = [];

    // 使用相同的会话ID发送多个请求
    for (let i = 0; i < 10; i++) {
      const response = await axios.get(
        `${TEST_CONFIG.serviceRegistry.url}/registry/services/${testServiceName}/instances`,
        {
          headers: {
            'X-Session-ID': sessionId,
          },
          params: {
            algorithm: 'consistent-hash',
          },
        }
      );

      expect(response.status).toBe(200);
      expect(response.data).toBeInstanceOf(Array);
      expect(response.data.length).toBeGreaterThan(0);

      instanceIds.push(response.data[0].instanceId);
    }

    // 验证所有请求都被路由到相同的实例
    const firstInstanceId = instanceIds[0];
    for (const id of instanceIds) {
      expect(id).toBe(firstInstanceId);
    }
  });

  test('应该能够使用加权轮询负载均衡策略', async () => {
    const instanceCounts = {};
    const totalRequests = 100;

    // 发送多个请求，使用加权轮询负载均衡
    for (let i = 0; i < totalRequests; i++) {
      const response = await axios.get(
        `${TEST_CONFIG.serviceRegistry.url}/registry/services/${testServiceName}/instances`,
        {
          params: {
            algorithm: 'weighted-round-robin',
          },
        }
      );

      expect(response.status).toBe(200);
      expect(response.data).toBeInstanceOf(Array);
      expect(response.data.length).toBeGreaterThan(0);

      const instanceId = response.data[0].instanceId;
      instanceCounts[instanceId] = (instanceCounts[instanceId] || 0) + 1;
    }

    // 验证权重为2的实例被选择的次数约为权重为1的实例的两倍
    const instance1Count = instanceCounts[testInstances[0].instanceId] || 0;
    const instance2Count = instanceCounts[testInstances[1].instanceId] || 0;
    const instance3Count = instanceCounts[testInstances[2].instanceId] || 0;

    // 允许一定的误差范围
    const ratio = instance2Count / ((instance1Count + instance3Count) / 2);
    expect(ratio).toBeGreaterThan(1.5);
    expect(ratio).toBeLessThan(2.5);
  });
});
