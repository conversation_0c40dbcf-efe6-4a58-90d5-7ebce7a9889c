/**
 * 基于FFT的海洋波浪模拟
 * 使用快速傅里叶变换算法模拟真实的海洋波浪
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { World } from '../../core/World';
/**
 * 海洋波浪模拟配置
 */
export interface OceanFFTWaveSimulationConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 分辨率 */
    resolution?: number;
    /** 尺寸 */
    size?: number;
    /** 风速 */
    windSpeed?: number;
    /** 风向 */
    windDirection?: number;
    /** 波浪尺度 */
    waveScale?: number;
    /** 波浪高度 */
    waveHeight?: number;
    /** 波浪速度 */
    waveSpeed?: number;
    /** 是否使用GPU计算 */
    useGPUCompute?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 海洋波浪模拟事件类型
 */
export declare enum OceanFFTWaveSimulationEventType {
    /** 波浪更新 */
    WAVE_UPDATED = "waveUpdated",
    /** 波浪参数变化 */
    WAVE_PARAMS_CHANGED = "waveParamsChanged"
}
/**
 * 海洋波浪模拟系统
 */
export declare class OceanFFTWaveSimulation extends System {
    /** 系统类型 */
    static readonly TYPE = "OceanFFTWaveSimulation";
    /** 配置 */
    private config;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监视器 */
    private performanceMonitor;
    /** 调试对象 */
    private debugObjects;
    /** 时间 */
    private time;
    /** 波浪高度场 */
    private heightField;
    /** 波浪法线场 */
    private normalField;
    /** 波浪位移场 */
    private displacementField;
    /** 频谱 */
    private spectrum;
    /** 频谱共轭 */
    private spectrumConjugate;
    /** 波浪高度纹理 */
    private heightTexture;
    /** 波浪法线纹理 */
    private normalTexture;
    /** 波浪位移纹理 */
    private displacementTexture;
    /** GPU计算渲染器 */
    private gpuCompute;
    /** 波浪高度计算着色器 */
    private heightComputeShader;
    /** 波浪法线计算着色器 */
    private normalComputeShader;
    /** 波浪位移计算着色器 */
    private displacementComputeShader;
    /** 波浪高度计算目标 */
    private heightRenderTarget;
    /** 波浪法线计算目标 */
    private normalRenderTarget;
    /** 波浪位移计算目标 */
    private displacementRenderTarget;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config?: OceanFFTWaveSimulationConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 创建数据纹理
     * @param data 数据
     * @param itemSize 每个元素的大小
     * @returns 数据纹理
     */
    private createDataTexture;
    /**
     * 初始化GPU计算
     */
    private initializeGPUCompute;
    /**
     * 初始化频谱
     */
    private initializeSpectrum;
    /**
     * 计算Phillips频谱
     */
    private computePhillipsSpectrum;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 使用GPU更新波浪
     */
    private updateWavesGPU;
    /**
     * 使用CPU更新波浪
     */
    private updateWavesCPU;
    /**
     * 计算波浪高度场
     */
    private computeWaveHeightField;
    /**
     * 计算波浪法线场
     */
    private computeWaveNormalField;
    /**
     * 计算波浪位移场
     */
    private computeWaveDisplacementField;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 获取高度计算顶点着色器
     * @returns 顶点着色器代码
     */
    private getHeightComputeVertexShader;
    /**
     * 获取高度计算片段着色器
     * @returns 片段着色器代码
     */
    private getHeightComputeFragmentShader;
    /**
     * 获取法线计算顶点着色器
     * @returns 顶点着色器代码
     */
    private getNormalComputeVertexShader;
    /**
     * 获取法线计算片段着色器
     * @returns 片段着色器代码
     */
    private getNormalComputeFragmentShader;
    /**
     * 获取位移计算顶点着色器
     * @returns 顶点着色器代码
     */
    private getDisplacementComputeVertexShader;
    /**
     * 获取位移计算片段着色器
     * @returns 片段着色器代码
     */
    private getDisplacementComputeFragmentShader;
    /**
     * 设置风速
     * @param windSpeed 风速
     */
    setWindSpeed(windSpeed: number): void;
    /**
     * 设置风向
     * @param windDirection 风向
     */
    setWindDirection(windDirection: number): void;
    /**
     * 设置波浪尺度
     * @param waveScale 波浪尺度
     */
    setWaveScale(waveScale: number): void;
    /**
     * 设置波浪高度
     * @param waveHeight 波浪高度
     */
    setWaveHeight(waveHeight: number): void;
    /**
     * 设置波浪速度
     * @param waveSpeed 波浪速度
     */
    setWaveSpeed(waveSpeed: number): void;
    /**
     * 获取高度纹理
     * @returns 高度纹理
     */
    getHeightTexture(): THREE.DataTexture;
    /**
     * 获取法线纹理
     * @returns 法线纹理
     */
    getNormalTexture(): THREE.DataTexture;
    /**
     * 获取位移纹理
     * @returns 位移纹理
     */
    getDisplacementTexture(): THREE.DataTexture;
}
