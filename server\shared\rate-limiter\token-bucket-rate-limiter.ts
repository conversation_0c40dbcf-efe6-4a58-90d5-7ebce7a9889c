/**
 * 令牌桶限流器
 */
import { Injectable, Logger } from '@nestjs/common';
import {
  IRateLimiter,
  RateLimiterOptions,
  RateLimiterStats,
  RateLimiterType,
} from './rate-limiter.interface';

/**
 * 令牌桶
 */
interface TokenBucket {
  /**
   * 令牌数
   */
  tokens: number;

  /**
   * 上次更新时间
   */
  lastRefillTime: number;
}

/**
 * 令牌桶限流器
 * 实现了令牌桶算法，用于限制请求速率
 */
@Injectable()
export class TokenBucketRateLimiter implements IRateLimiter {
  private readonly logger = new Logger(TokenBucketRateLimiter.name);
  private readonly buckets = new Map<string, TokenBucket>();
  private totalRequests = 0;
  private limitedRequests = 0;
  private passedRequests = 0;
  private lastResetTime = new Date();
  private readonly refillRate: number;

  /**
   * 创建令牌桶限流器
   * @param options 限流器配置
   */
  constructor(private readonly options: RateLimiterOptions) {
    // 计算令牌填充速率（令牌/毫秒）
    this.refillRate = this.options.maxRequests / this.options.windowMs;
    this.logger.log(`令牌桶限流器 ${options.name} 已创建，填充速率: ${this.refillRate * 1000} 令牌/秒`);
  }

  /**
   * 消费令牌
   * @param key 键
   * @param tokens 令牌数，默认为1
   */
  async consume(key: string, tokens: number = 1): Promise<boolean> {
    this.totalRequests++;
    
    // 获取或创建令牌桶
    const bucket = this.getOrCreateBucket(key);
    
    // 填充令牌
    this.refillBucket(bucket);
    
    // 检查是否有足够的令牌
    if (bucket.tokens >= tokens) {
      // 消费令牌
      bucket.tokens -= tokens;
      this.passedRequests++;
      return true;
    } else {
      // 令牌不足，限流
      this.limitedRequests++;
      
      // 触发限流回调
      if (this.options.onLimitReached) {
        this.options.onLimitReached(key, this.options.maxRequests);
      }
      
      return false;
    }
  }

  /**
   * 获取剩余令牌数
   * @param key 键
   */
  async getRemainingTokens(key: string): Promise<number> {
    // 获取或创建令牌桶
    const bucket = this.getOrCreateBucket(key);
    
    // 填充令牌
    this.refillBucket(bucket);
    
    return bucket.tokens;
  }

  /**
   * 获取限流器统计信息
   */
  getStats(): RateLimiterStats {
    return {
      name: this.options.name,
      type: RateLimiterType.TOKEN_BUCKET,
      totalRequests: this.totalRequests,
      limitedRequests: this.limitedRequests,
      passedRequests: this.passedRequests,
      currentRate: this.calculateCurrentRate(),
      lastResetTime: this.lastResetTime,
    };
  }

  /**
   * 重置限流器
   */
  async reset(): Promise<void> {
    this.buckets.clear();
    this.totalRequests = 0;
    this.limitedRequests = 0;
    this.passedRequests = 0;
    this.lastResetTime = new Date();
  }

  /**
   * 获取或创建令牌桶
   * @param key 键
   */
  private getOrCreateBucket(key: string): TokenBucket {
    if (!this.buckets.has(key)) {
      this.buckets.set(key, {
        tokens: this.options.maxRequests,
        lastRefillTime: Date.now(),
      });
    }
    
    return this.buckets.get(key);
  }

  /**
   * 填充令牌桶
   * @param bucket 令牌桶
   */
  private refillBucket(bucket: TokenBucket): void {
    const now = Date.now();
    const elapsedTime = now - bucket.lastRefillTime;
    
    if (elapsedTime > 0) {
      // 计算新增令牌数
      const newTokens = elapsedTime * this.refillRate;
      
      // 更新令牌数，不超过最大值
      bucket.tokens = Math.min(this.options.maxRequests, bucket.tokens + newTokens);
      
      // 更新上次填充时间
      bucket.lastRefillTime = now;
    }
  }

  /**
   * 计算当前请求速率
   */
  private calculateCurrentRate(): number {
    const now = new Date();
    const elapsedSeconds = (now.getTime() - this.lastResetTime.getTime()) / 1000;
    
    if (elapsedSeconds <= 0) {
      return 0;
    }
    
    return this.passedRequests / elapsedSeconds;
  }
}
