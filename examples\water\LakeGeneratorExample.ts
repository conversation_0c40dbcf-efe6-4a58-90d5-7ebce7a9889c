/**
 * 湖泊生成器示例
 * 展示如何使用湖泊生成器创建湖泊
 */
import * as THREE from 'three';
import { World } from '../../engine/src/core/World';
import { Entity } from '../../engine/src/core/Entity';
import { Camera } from '../../engine/src/rendering/Camera';
import { Transform } from '../../engine/src/core/Transform';
import { Light, LightType } from '../../engine/src/rendering/Light';
import { RenderSystem } from '../../engine/src/rendering/RenderSystem';
import { CameraSystem } from '../../engine/src/rendering/CameraSystem';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { PhysicsSystem } from '../../engine/src/physics/PhysicsSystem';
import { TerrainComponent } from '../../engine/src/terrain/components/TerrainComponent';
import { TerrainSystem } from '../../engine/src/terrain/TerrainSystem';
import { TerrainGenerationAlgorithms } from '../../engine/src/terrain/generation/TerrainGenerationAlgorithms';
import { LakeGenerator, LakeShapeType } from '../../engine/src/physics/water/LakeGenerator';
import { WaterPhysicsSystem } from '../../engine/src/physics/water/WaterPhysicsSystem';
import { WaterSurfaceRenderer } from '../../engine/src/rendering/water/WaterSurfaceRenderer';
import { Debug } from '../../engine/src/utils/Debug';

/**
 * 湖泊生成器示例
 */
export class LakeGeneratorExample {
  /** 世界 */
  private world: World;
  /** 渲染系统 */
  private renderingSystem: RenderSystem;
  /** 相机系统 */
  private cameraSystem: CameraSystem;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 地形系统 */
  private terrainSystem: TerrainSystem;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem;
  /** 水面渲染器 */
  private waterSurfaceRenderer: WaterSurfaceRenderer;
  /** 地形实体 */
  private terrainEntity: Entity;
  /** 地形组件 */
  private terrainComponent: TerrainComponent;
  /** 湖泊实体 */
  private lakeEntity: Entity;

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 创建系统
    this.createSystems();

    // 创建场景
    this.createScene();

    // 创建地形
    this.createTerrain();

    // 创建湖泊
    this.createLake();

    // 启动渲染循环
    this.startRenderLoop();
  }

  /**
   * 创建系统
   */
  private createSystems(): void {
    // 创建渲染系统
    this.renderingSystem = new RenderSystem(this.world);
    this.world.addSystem(this.renderingSystem);

    // 创建相机系统
    this.cameraSystem = new CameraSystem(this.world);
    this.world.addSystem(this.cameraSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem(this.world);
    this.world.addSystem(this.inputSystem);

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem(this.world);
    this.world.addSystem(this.physicsSystem);

    // 创建地形系统
    this.terrainSystem = new TerrainSystem(this.world);
    this.world.addSystem(this.terrainSystem);

    // 创建水体物理系统
    this.waterPhysicsSystem = new WaterPhysicsSystem(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableBuoyancy: true,
      enableDrag: true,
      enableFlow: true,
      enableWaves: true,
      enableCollision: true,
      enableParticles: true,
      enableMultithreading: true,
      workerCount: 4
    });
    this.world.addSystem(this.waterPhysicsSystem);

    // 创建水面渲染器
    this.waterSurfaceRenderer = new WaterSurfaceRenderer(this.world, {
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      enableReflection: true,
      enableRefraction: true,
      enableCaustics: true,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true,
      enableDepthTest: true,
      reflectionMapResolution: 512,
      refractionMapResolution: 512,
      useHighQualityWaves: true
    });
    this.world.addSystem(this.waterSurfaceRenderer);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建相机实体
    const cameraEntity = new Entity();
    cameraEntity.addComponent(new Transform());
    cameraEntity.addComponent(new Camera());
    cameraEntity.getComponent<Transform>('Transform').setPosition(new THREE.Vector3(0, 50, 100));
    cameraEntity.getComponent<Transform>('Transform').lookAt(new THREE.Vector3(0, 0, 0));
    this.world.addEntity(cameraEntity);

    // 创建光源实体
    const lightEntity = new Entity();
    lightEntity.addComponent(new Transform());
    lightEntity.addComponent(new Light(LightType.DIRECTIONAL));
    lightEntity.getComponent<Transform>('Transform').setPosition(new THREE.Vector3(50, 100, 50));
    lightEntity.getComponent<Transform>('Transform').lookAt(new THREE.Vector3(0, 0, 0));
    lightEntity.getComponent<Light>('Light').setIntensity(1.0);
    lightEntity.getComponent<Light>('Light').setCastShadow(true);
    this.world.addEntity(lightEntity);

    // 创建环境光实体
    const ambientLightEntity = new Entity();
    ambientLightEntity.addComponent(new Transform());
    ambientLightEntity.addComponent(new Light(LightType.AMBIENT));
    ambientLightEntity.getComponent<Light>('Light').setIntensity(0.3);
    this.world.addEntity(ambientLightEntity);
  }

  /**
   * 创建地形
   */
  private createTerrain(): void {
    // 创建地形实体
    this.terrainEntity = new Entity();
    this.terrainEntity.addComponent(new Transform());

    // 创建地形组件
    this.terrainComponent = new TerrainComponent({
      width: 1000,
      height: 100,
      depth: 1000,
      widthSegments: 100,
      heightSegments: 1,
      depthSegments: 100,
      usePhysics: true
    });

    // 生成地形
    TerrainGenerationAlgorithms.generatePerlinNoiseTerrain(this.terrainComponent, {
      scale: 0.01,
      octaves: 6,
      persistence: 0.5,
      lacunarity: 2.0,
      seed: 12345
    });

    // 添加地形组件
    this.terrainEntity.addComponent(this.terrainComponent);

    // 添加到世界
    this.world.addEntity(this.terrainEntity);
  }

  /**
   * 创建湖泊
   */
  private createLake(): void {
    // 创建湖泊生成器
    const lakeGenerator = new LakeGenerator(this.world, {
      position: new THREE.Vector3(0, 0, 0),
      size: {
        width: 200,
        depth: 150
      },
      depth: 20,
      shapeType: LakeShapeType.IRREGULAR,
      shapeParams: {
        irregularity: 0.3,
        seed: 12345,
        controlPoints: 16
      },
      resolution: 32,
      followTerrain: true,
      terrainOffset: 0.5,
      generateShore: true,
      shoreWidth: 10.0,
      shoreHeight: 2.0,
      generateLakeBed: true,
      generateUnderwaterVegetation: true,
      underwaterVegetationDensity: 0.01,
      generateUnderwaterParticles: true,
      underwaterParticleCount: 1000
    });

    // 设置地形组件
    lakeGenerator.setTerrainComponent(this.terrainComponent);

    // 生成湖泊
    this.lakeEntity = lakeGenerator.generate();

    Debug.log('LakeGeneratorExample', '湖泊生成完成');
  }

  /**
   * 启动渲染循环
   */
  private startRenderLoop(): void {
    // 更新世界
    this.world.update();

    // 请求下一帧
    requestAnimationFrame(() => this.startRenderLoop());
  }
}
