/**
 * 增强资源版本管理器
 * 负责管理资源的版本控制、比较和回滚功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetType } from './ResourceManager';
/**
 * 资源版本接口
 */
export interface ResourceVersion {
    /** 版本ID */
    id: string;
    /** 资源ID */
    resourceId: string;
    /** 版本号 */
    version: number;
    /** 创建时间戳 */
    timestamp: number;
    /** 资源URL */
    url: string;
    /** 资源类型 */
    type: AssetType;
    /** 资源哈希 */
    hash: string;
    /** 资源大小（字节） */
    size: number;
    /** 资源元数据 */
    metadata?: Record<string, any>;
    /** 版本描述 */
    description: string;
    /** 创建用户ID */
    userId: string;
    /** 创建用户名 */
    userName: string;
    /** 版本标签 */
    tags?: string[];
}
/**
 * 版本比较结果接口
 */
export interface VersionComparisonResult {
    /** 版本1 */
    version1: ResourceVersion;
    /** 版本2 */
    version2: ResourceVersion;
    /** 是否有差异 */
    hasDifferences: boolean;
    /** 差异类型 */
    differenceType: 'content' | 'metadata' | 'both' | 'none';
    /** 内容差异（取决于资源类型） */
    contentDifferences?: any;
    /** 元数据差异 */
    metadataDifferences?: {
        added: Record<string, any>;
        removed: Record<string, any>;
        modified: Record<string, {
            from: any;
            to: any;
        }>;
    };
    /** 比较时间戳 */
    comparisonTimestamp: number;
}
/**
 * 增强资源版本管理器选项
 */
export interface EnhancedResourceVersionManagerOptions {
    /** 是否启用版本控制 */
    enabled?: boolean;
    /** 最大版本数量（每个资源） */
    maxVersionsPerResource?: number;
    /** 是否自动创建初始版本 */
    autoCreateInitialVersion?: boolean;
    /** 是否保存完整资源副本 */
    saveFullCopy?: boolean;
    /** 是否启用版本比较 */
    enableComparison?: boolean;
    /** 是否启用版本标记 */
    enableTags?: boolean;
    /** 是否启用调试模式 */
    debug?: boolean;
}
/**
 * 增强资源版本管理器事件类型
 */
export declare enum ResourceVersionEventType {
    VERSION_CREATED = "versionCreated",
    VERSION_DELETED = "versionDeleted",
    VERSION_ROLLBACK = "versionRollback",
    VERSION_TAGGED = "versionTagged",
    VERSION_COMPARED = "versionCompared"
}
/**
 * 增强资源版本管理器
 * 负责管理资源的版本控制、比较和回滚功能
 */
export declare class EnhancedResourceVersionManager extends EventEmitter {
    /** 是否启用版本控制 */
    private enabled;
    /** 最大版本数量（每个资源） */
    private maxVersionsPerResource;
    /** 是否自动创建初始版本 */
    private autoCreateInitialVersion;
    /** 是否保存完整资源副本 */
    private saveFullCopy;
    /** 是否启用版本比较 */
    private enableComparison;
    /** 是否启用版本标记 */
    private enableTags;
    /** 是否启用调试模式 */
    private debug;
    /** 资源版本映射（resourceId -> 版本数组） */
    private resourceVersions;
    /** 最近比较结果 */
    private lastComparisonResult;
    /** 单例实例 */
    private static instance;
    /**
     * 获取单例实例
     * @param options 选项
     * @returns 单例实例
     */
    static getInstance(options?: EnhancedResourceVersionManagerOptions): EnhancedResourceVersionManager;
    /**
     * 创建增强资源版本管理器实例
     * @param options 选项
     */
    constructor(options?: EnhancedResourceVersionManagerOptions);
    /**
     * 创建资源版本
     * @param resourceId 资源ID
     * @param url 资源URL
     * @param type 资源类型
     * @param hash 资源哈希
     * @param size 资源大小
     * @param metadata 资源元数据
     * @param description 版本描述
     * @param userId 用户ID
     * @param userName 用户名
     * @param tags 标签
     * @returns 创建的版本
     */
    createVersion(resourceId: string, url: string, type: AssetType, hash: string, size: number, metadata?: Record<string, any>, description?: string, userId?: string, userName?: string, tags?: string[]): ResourceVersion;
    /**
     * 获取资源的所有版本
     * @param resourceId 资源ID
     * @returns 版本数组
     */
    getVersions(resourceId: string): ResourceVersion[];
    /**
     * 获取资源的特定版本
     * @param resourceId 资源ID
     * @param versionId 版本ID
     * @returns 版本对象，如果不存在则返回null
     */
    getVersion(resourceId: string, versionId: string): ResourceVersion | null;
    /**
     * 获取资源的最新版本
     * @param resourceId 资源ID
     * @returns 最新版本，如果不存在则返回null
     */
    getLatestVersion(resourceId: string): ResourceVersion | null;
    /**
     * 删除资源版本
     * @param resourceId 资源ID
     * @param versionId 版本ID
     * @returns 是否成功删除
     */
    deleteVersion(resourceId: string, versionId: string): boolean;
    /**
     * 比较两个版本
     * @param resourceId 资源ID
     * @param versionId1 版本1 ID
     * @param versionId2 版本2 ID
     * @returns 比较结果
     */
    compareVersions(resourceId: string, versionId1: string, versionId2: string): VersionComparisonResult | null;
    /**
     * 获取最近的比较结果
     * @returns 最近的比较结果
     */
    getLastComparisonResult(): VersionComparisonResult | null;
    /**
     * 添加版本标签
     * @param resourceId 资源ID
     * @param versionId 版本ID
     * @param tag 标签
     * @returns 是否成功添加
     */
    addVersionTag(resourceId: string, versionId: string, tag: string): boolean;
    /**
     * 移除版本标签
     * @param resourceId 资源ID
     * @param versionId 版本ID
     * @param tag 标签
     * @returns 是否成功移除
     */
    removeVersionTag(resourceId: string, versionId: string, tag: string): boolean;
    /**
     * 更新版本描述
     * @param resourceId 资源ID
     * @param versionId 版本ID
     * @param description 新描述
     * @returns 是否成功更新
     */
    updateVersionDescription(resourceId: string, versionId: string, description: string): boolean;
    /**
     * 生成唯一ID
     * @returns 唯一ID
     */
    private generateId;
    /**
     * 比较两个对象
     * @param obj1 对象1
     * @param obj2 对象2
     * @returns 比较结果
     */
    private compareObjects;
}
