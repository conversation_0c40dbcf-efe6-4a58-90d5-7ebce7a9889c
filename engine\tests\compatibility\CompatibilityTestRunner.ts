/**
 * 兼容性测试运行器
 * 用于运行所有兼容性测试
 */
import { CompatibilityTestFramework, TestReport } from './CompatibilityTestFramework';
import { Debug } from '../../src/utils/Debug';
import * as fs from 'fs';
import * as path from 'path';

// 导入测试配置
import { coreTestConfig } from './core/CoreTestConfig';
import { physicsTestConfig } from './physics/PhysicsTestConfig';
import { renderingTestConfig } from './rendering/RenderingTestConfig';
import { animationTestConfig } from './animation/AnimationTestConfig';
import { inputTestConfig } from './input/InputTestConfig';
import { sceneTestConfig } from './scene/SceneTestConfig';
import { networkTestConfig } from './network/NetworkTestConfig';
import { visualscriptTestConfig } from './visualscript/VisualScriptTestConfig';
import { interactionTestConfig } from './interaction/InteractionTestConfig';
import { avatarTestConfig } from './avatar/AvatarTestConfig';
import { mocapTestConfig } from './mocap/MocapTestConfig';

// 报告输出目录
const REPORTS_DIR = path.join(__dirname, '../../../reports/compatibility');

/**
 * 确保目录存在
 * @param dir 目录路径
 */
function ensureDir(dir: string): void {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

/**
 * 保存测试报告
 * @param report 测试报告
 * @param filename 文件名
 */
function saveTestReport(report: TestReport, filename: string): void {
  ensureDir(REPORTS_DIR);
  
  // 保存HTML报告
  const htmlReport = CompatibilityTestFramework.generateHTMLReport(report);
  fs.writeFileSync(path.join(REPORTS_DIR, filename), htmlReport);
  
  // 保存JSON报告
  fs.writeFileSync(
    path.join(REPORTS_DIR, filename.replace('.html', '.json')), 
    JSON.stringify(report, null, 2)
  );
  
  Debug.log('兼容性测试', `测试报告已保存到 ${path.join(REPORTS_DIR, filename)}`);
}

/**
 * 运行核心模块兼容性测试
 */
export async function runCoreTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行核心模块兼容性测试');
  
  const framework = new CompatibilityTestFramework(coreTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'core-compatibility-report.html');
}

/**
 * 运行物理系统兼容性测试
 */
export async function runPhysicsTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行物理系统兼容性测试');
  
  const framework = new CompatibilityTestFramework(physicsTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'physics-compatibility-report.html');
}

/**
 * 运行渲染系统兼容性测试
 */
export async function runRenderingTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行渲染系统兼容性测试');
  
  const framework = new CompatibilityTestFramework(renderingTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'rendering-compatibility-report.html');
}

/**
 * 运行动画系统兼容性测试
 */
export async function runAnimationTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行动画系统兼容性测试');
  
  const framework = new CompatibilityTestFramework(animationTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'animation-compatibility-report.html');
}

/**
 * 运行输入系统兼容性测试
 */
export async function runInputTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行输入系统兼容性测试');
  
  const framework = new CompatibilityTestFramework(inputTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'input-compatibility-report.html');
}

/**
 * 运行场景管理系统兼容性测试
 */
export async function runSceneTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行场景管理系统兼容性测试');
  
  const framework = new CompatibilityTestFramework(sceneTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'scene-compatibility-report.html');
}

/**
 * 运行网络系统兼容性测试
 */
export async function runNetworkTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行网络系统兼容性测试');
  
  const framework = new CompatibilityTestFramework(networkTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'network-compatibility-report.html');
}

/**
 * 运行视觉脚本系统兼容性测试
 */
export async function runVisualScriptTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行视觉脚本系统兼容性测试');
  
  const framework = new CompatibilityTestFramework(visualscriptTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'visualscript-compatibility-report.html');
}

/**
 * 运行交互系统兼容性测试
 */
export async function runInteractionTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行交互系统兼容性测试');
  
  const framework = new CompatibilityTestFramework(interactionTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'interaction-compatibility-report.html');
}

/**
 * 运行头像系统兼容性测试
 */
export async function runAvatarTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行头像系统兼容性测试');
  
  const framework = new CompatibilityTestFramework(avatarTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'avatar-compatibility-report.html');
}

/**
 * 运行动作捕捉系统兼容性测试
 */
export async function runMocapTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行动作捕捉系统兼容性测试');
  
  const framework = new CompatibilityTestFramework(mocapTestConfig);
  const report = await framework.run();
  
  saveTestReport(report, 'mocap-compatibility-report.html');
}

/**
 * 运行所有兼容性测试
 */
export async function runAllTests(): Promise<void> {
  Debug.log('兼容性测试', '开始运行所有兼容性测试');
  
  // 运行所有测试
  await runCoreTests();
  await runPhysicsTests();
  await runRenderingTests();
  await runAnimationTests();
  await runInputTests();
  await runSceneTests();
  await runNetworkTests();
  await runVisualScriptTests();
  await runInteractionTests();
  await runAvatarTests();
  await runMocapTests();
  
  Debug.log('兼容性测试', '所有兼容性测试完成');
}
