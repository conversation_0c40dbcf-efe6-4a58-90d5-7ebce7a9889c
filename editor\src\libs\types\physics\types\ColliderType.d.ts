/**
 * 碰撞体类型定义
 */
export declare enum ColliderType {
    /** 盒子碰撞体 */
    BOX = "box",
    /** 球体碰撞体 */
    SPHERE = "sphere",
    /** 胶囊碰撞体 */
    CAPSULE = "capsule",
    /** 圆柱体碰撞体 */
    CYLINDER = "cylinder",
    /** 圆锥体碰撞体 */
    CONE = "cone",
    /** 平面碰撞体 */
    PLANE = "plane",
    /** 网格碰撞体 */
    MESH = "mesh",
    /** 凸包碰撞体 */
    CONVEX_HULL = "convexHull",
    /** 高度场碰撞体 */
    HEIGHTFIELD = "heightfield",
    /** 复合碰撞体 */
    COMPOUND = "compound"
}
export interface ColliderConfig {
    /** 碰撞体类型 */
    type: ColliderType;
    /** 尺寸参数 */
    size?: {
        x?: number;
        y?: number;
        z?: number;
        radius?: number;
        height?: number;
    };
    /** 材质属性 */
    material?: {
        friction?: number;
        restitution?: number;
        density?: number;
    };
    /** 是否为触发器 */
    isTrigger?: boolean;
    /** 碰撞层 */
    layer?: number;
    /** 碰撞掩码 */
    mask?: number;
}
export declare class ColliderTypeHelper {
    /**
     * 检查是否为基础几何体
     */
    static isPrimitive(type: ColliderType): boolean;
    /**
     * 检查是否为复杂几何体
     */
    static isComplex(type: ColliderType): boolean;
    /**
     * 检查是否需要网格数据
     */
    static requiresMeshData(type: ColliderType): boolean;
    /**
     * 获取默认尺寸
     */
    static getDefaultSize(type: ColliderType): any;
    /**
     * 获取默认配置
     */
    static getDefaultConfig(type: ColliderType): ColliderConfig;
}
