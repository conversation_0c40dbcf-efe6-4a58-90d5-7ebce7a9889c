/**
 * 用户模块
 */
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User } from './entities/user.entity';
import { UserAvatar } from './entities/user-avatar.entity';
import { UserSetting } from './entities/user-setting.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, UserAvatar, UserSetting]),
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
