/**
 * 限流器服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  IRateLimiter,
  RateLimiterOptions,
  RateLimiterStats,
  RateLimiterType,
} from './rate-limiter.interface';
import { TokenBucketRateLimiter } from './token-bucket-rate-limiter';
import { SlidingWindowRateLimiter } from './sliding-window-rate-limiter';

/**
 * 限流器事件
 */
export enum RateLimiterEvent {
  /** 限流 */
  RATE_LIMITED = 'rate-limiter.limited',
  /** 重置 */
  RATE_LIMITER_RESET = 'rate-limiter.reset',
}

/**
 * 限流器服务
 * 管理多个限流器实例
 */
@Injectable()
export class RateLimiterService {
  private readonly logger = new Logger(RateLimiterService.name);
  private readonly rateLimiters = new Map<string, IRateLimiter>();
  private readonly defaultOptions: Partial<RateLimiterOptions> = {
    type: RateLimiterType.TOKEN_BUCKET,
    windowMs: 60000, // 1分钟
    maxRequests: 100, // 每分钟100个请求
    distributed: false,
    enableMonitoring: true,
  };

  /**
   * 创建限流器服务
   * @param eventEmitter 事件发射器
   */
  constructor(private readonly eventEmitter: EventEmitter2) {
    this.logger.log('限流器服务已初始化');
  }

  /**
   * 创建限流器
   * @param name 限流器名称
   * @param options 限流器配置
   */
  create(name: string, options: Partial<RateLimiterOptions> = {}): IRateLimiter {
    if (this.rateLimiters.has(name)) {
      return this.rateLimiters.get(name);
    }

    const rateLimiterOptions: RateLimiterOptions = {
      name,
      ...this.defaultOptions,
      ...options,
      onLimitReached: (key, limit) => {
        this.handleLimitReached(name, key, limit);
        if (options.onLimitReached) {
          options.onLimitReached(key, limit);
        }
      },
    };

    let rateLimiter: IRateLimiter;

    switch (rateLimiterOptions.type) {
      case RateLimiterType.TOKEN_BUCKET:
        rateLimiter = new TokenBucketRateLimiter(rateLimiterOptions);
        break;
      case RateLimiterType.SLIDING_WINDOW:
        rateLimiter = new SlidingWindowRateLimiter(rateLimiterOptions);
        break;
      default:
        rateLimiter = new TokenBucketRateLimiter(rateLimiterOptions);
    }

    this.rateLimiters.set(name, rateLimiter);

    return rateLimiter;
  }

  /**
   * 获取限流器
   * @param name 限流器名称
   */
  get(name: string): IRateLimiter | undefined {
    return this.rateLimiters.get(name);
  }

  /**
   * 获取或创建限流器
   * @param name 限流器名称
   * @param options 限流器配置
   */
  getOrCreate(name: string, options: Partial<RateLimiterOptions> = {}): IRateLimiter {
    return this.get(name) || this.create(name, options);
  }

  /**
   * 删除限流器
   * @param name 限流器名称
   */
  remove(name: string): boolean {
    return this.rateLimiters.delete(name);
  }

  /**
   * 获取所有限流器
   */
  getAll(): Map<string, IRateLimiter> {
    return this.rateLimiters;
  }

  /**
   * 获取所有限流器统计信息
   */
  getAllStats(): RateLimiterStats[] {
    return Array.from(this.rateLimiters.values()).map(rl => rl.getStats());
  }

  /**
   * 重置所有限流器
   */
  async resetAll(): Promise<void> {
    for (const rateLimiter of this.rateLimiters.values()) {
      await rateLimiter.reset();
    }
    
    this.eventEmitter.emit(RateLimiterEvent.RATE_LIMITER_RESET, {
      timestamp: new Date(),
    });
  }

  /**
   * 消费令牌
   * @param name 限流器名称
   * @param key 键
   * @param tokens 令牌数
   */
  async consume(name: string, key: string, tokens: number = 1): Promise<boolean> {
    const rateLimiter = this.getOrCreate(name);
    return rateLimiter.consume(key, tokens);
  }

  /**
   * 获取剩余令牌数
   * @param name 限流器名称
   * @param key 键
   */
  async getRemainingTokens(name: string, key: string): Promise<number> {
    const rateLimiter = this.getOrCreate(name);
    return rateLimiter.getRemainingTokens(key);
  }

  /**
   * 处理限流事件
   * @param name 限流器名称
   * @param key 键
   * @param limit 限制
   */
  private handleLimitReached(name: string, key: string, limit: number): void {
    this.logger.warn(`限流器 ${name} 限流请求: ${key}, 限制: ${limit}`);
    
    this.eventEmitter.emit(RateLimiterEvent.RATE_LIMITED, {
      name,
      key,
      limit,
      timestamp: new Date(),
    });
  }
}
