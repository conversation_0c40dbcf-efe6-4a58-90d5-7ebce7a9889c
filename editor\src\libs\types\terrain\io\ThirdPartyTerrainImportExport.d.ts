import { TerrainComponent } from '../components/TerrainComponent';
/**
 * 第三方地形格式
 */
export declare enum ThirdPartyTerrainFormat {
    /** Unity地形 */
    UNITY = "unity",
    /** Unreal地形 */
    UNREAL = "unreal",
    /** World Machine地形 */
    WORLD_MACHINE = "world_machine",
    /** Terragen地形 */
    TERRAGEN = "terragen",
    /** L3DT地形 */
    L3DT = "l3dt",
    /** GeoTIFF地形 */
    GEOTIFF = "geotiff",
    /** USGS DEM地形 */
    USGS_DEM = "usgs_dem",
    /** SRTM地形 */
    SRTM = "srtm"
}
/**
 * 第三方地形导出选项
 */
export interface ThirdPartyTerrainExportOptions {
    /** 格式 */
    format: ThirdPartyTerrainFormat;
    /** 是否包含纹理 */
    includeTextures?: boolean;
    /** 是否包含法线 */
    includeNormals?: boolean;
    /** 是否包含物理属性 */
    includePhysics?: boolean;
    /** 高度缩放 */
    heightScale?: number;
    /** 高度偏移 */
    heightOffset?: number;
    /** 源坐标系 */
    sourceCoordinateSystem?: CoordinateSystem;
    /** 目标坐标系 */
    targetCoordinateSystem?: CoordinateSystem;
    /** 是否反转X轴 */
    flipX?: boolean;
    /** 是否反转Y轴 */
    flipY?: boolean;
    /** 是否反转Z轴 */
    flipZ?: boolean;
    /** 导出宽度 */
    width?: number;
    /** 导出高度 */
    height?: number;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 坐标系类型
 */
export declare enum CoordinateSystem {
    /** 左手坐标系 */
    LEFT_HANDED = "left_handed",
    /** 右手坐标系 */
    RIGHT_HANDED = "right_handed"
}
/**
 * 第三方地形导入选项
 */
export interface ThirdPartyTerrainImportOptions {
    /** 格式 */
    format: ThirdPartyTerrainFormat;
    /** 是否保留现有纹理 */
    keepTextures?: boolean;
    /** 是否保留现有物理属性 */
    keepPhysics?: boolean;
    /** 是否自动计算法线 */
    computeNormals?: boolean;
    /** 是否应用平滑 */
    applySmoothing?: boolean;
    /** 平滑强度 */
    smoothingStrength?: number;
    /** 高度缩放 */
    heightScale?: number;
    /** 高度偏移 */
    heightOffset?: number;
    /** 源坐标系 */
    sourceCoordinateSystem?: CoordinateSystem;
    /** 目标坐标系 */
    targetCoordinateSystem?: CoordinateSystem;
    /** 是否反转X轴 */
    flipX?: boolean;
    /** 是否反转Y轴 */
    flipY?: boolean;
    /** 是否反转Z轴 */
    flipZ?: boolean;
    /** 自定义元数据 */
    metadata?: Record<string, any>;
}
/**
 * 第三方地形导入导出
 */
export declare class ThirdPartyTerrainImportExport {
    /**
     * 从第三方地形格式导入
     * @param terrain 地形组件
     * @param data 地形数据
     * @param options 导入选项
     * @returns Promise，解析为是否导入成功
     */
    importFromThirdParty(terrain: TerrainComponent, data: ArrayBuffer | string, options: ThirdPartyTerrainImportOptions): Promise<boolean>;
    /**
     * 导出为第三方地形格式
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns Promise，解析为导出数据
     */
    exportToThirdParty(terrain: TerrainComponent, options: ThirdPartyTerrainExportOptions): Promise<ArrayBuffer | string>;
    /**
     * 从Unity地形导入
     * @param terrain 地形组件
     * @param data 地形数据
     * @param options 导入选项
     * @returns 是否导入成功
     */
    private importFromUnity;
    /**
     * 导出为Unity地形
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns 导出数据
     */
    private exportToUnity;
    /**
     * 从Unreal地形导入
     * @param terrain 地形组件
     * @param data 地形数据
     * @param options 导入选项
     * @returns 是否导入成功
     */
    private importFromUnreal;
    /**
     * 导出为Unreal地形
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns 导出数据
     */
    private exportToUnreal;
    /**
     * 从World Machine地形导入
     * @param terrain 地形组件
     * @param data 地形数据
     * @param options 导入选项
     * @returns 是否导入成功
     */
    private importFromWorldMachine;
    /**
     * 导出为World Machine地形
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns 导出数据
     */
    private exportToWorldMachine;
    /**
     * 从Terragen地形导入
     * @param terrain 地形组件
     * @param data 地形数据
     * @param options 导入选项
     * @returns 是否导入成功
     */
    private importFromTerragen;
    /**
     * 导出为Terragen地形
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns 导出数据
     */
    private exportToTerragen;
    /**
     * 从L3DT地形导入
     * @param terrain 地形组件
     * @param data 地形数据
     * @param options 导入选项
     * @returns 是否导入成功
     */
    private importFromL3DT;
    /**
     * 导出为L3DT地形
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns 导出数据
     */
    private exportToL3DT;
    /**
     * 从GeoTIFF地形导入
     * @param terrain 地形组件
     * @param data 地形数据
     * @param options 导入选项
     * @returns 是否导入成功
     */
    private importFromGeoTIFF;
    /**
     * 将Base64字符串转换为ArrayBuffer
     * @param base64 Base64字符串
     * @returns ArrayBuffer
     */
    private base64ToArrayBuffer;
    /**
     * 应用坐标系转换
     * @param heightData 高度数据
     * @param resolution 分辨率
     * @param options 转换选项
     * @returns 转换后的高度数据
     */
    private applyCoordinateTransform;
    /**
     * 导出为GeoTIFF地形
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns 导出数据
     */
    private exportToGeoTIFF;
    /**
     * 写入IFD条目
     * @param dataView DataView
     * @param offset 偏移量
     * @param tag 标签
     * @param type 类型
     * @param count 计数
     * @param value 值
     */
    private writeIFDEntry;
    /**
     * 从USGS DEM地形导入
     * @param terrain 地形组件
     * @param data 地形数据
     * @param options 导入选项
     * @returns 是否导入成功
     */
    private importFromUSGSDEM;
    /**
     * 导出为USGS DEM地形
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns 导出数据
     */
    private exportToUSGSDEM;
    /**
     * 从SRTM地形导入
     * @param terrain 地形组件
     * @param data 地形数据
     * @param options 导入选项
     * @returns 是否导入成功
     */
    private importFromSRTM;
    /**
     * 导出为SRTM地形
     * @param terrain 地形组件
     * @param options 导出选项
     * @returns 导出数据
     */
    private exportToSRTM;
}
