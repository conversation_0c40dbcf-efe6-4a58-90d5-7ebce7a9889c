/**
 * 动画子片段
 * 用于从完整的动画片段中提取部分时间段
 */
import * as THREE from 'three';
export interface SubClipConfig {
    /** 子片段名称 */
    name: string;
    /** 开始时间（秒） */
    startTime: number;
    /** 结束时间（秒） */
    endTime: number;
    /** 是否循环 */
    loop?: boolean;
    /** 播放速度 */
    speed?: number;
    /** 原始片段名称 */
    originalClipName?: string;
    /** 是否反向播放 */
    reverse?: boolean;
    /** 时间缩放 */
    timeScale?: number;
}
export declare class AnimationSubClip {
    /** 子片段名称 */
    readonly name: string;
    /** 原始动画片段 */
    private originalClip;
    /** 开始时间 */
    private startTime;
    /** 结束时间 */
    private endTime;
    /** 是否循环 */
    private loop;
    /** 播放速度 */
    private speed;
    /** 原始片段名称 */
    private originalClipName;
    /** 是否反向播放 */
    private reverse;
    /** 时间缩放 */
    private timeScale;
    /** 子片段列表（用于复合子片段） */
    private subClips;
    /** 缓存的子片段 */
    private cachedSubClip;
    constructor(config: SubClipConfig);
    /**
     * 设置原始动画片段
     */
    setOriginalClip(clip: THREE.AnimationClip): void;
    /**
     * 获取原始动画片段
     */
    getOriginalClip(): THREE.AnimationClip | null;
    /**
     * 设置时间范围
     */
    setTimeRange(startTime: number, endTime: number): void;
    /**
     * 获取开始时间
     */
    getStartTime(): number;
    /**
     * 获取结束时间
     */
    getEndTime(): number;
    /**
     * 获取持续时间
     */
    getDuration(): number;
    /**
     * 设置循环模式
     */
    setLoop(loop: boolean): void;
    /**
     * 获取循环模式
     */
    getLoop(): boolean;
    /**
     * 设置播放速度
     */
    setSpeed(speed: number): void;
    /**
     * 获取播放速度
     */
    getSpeed(): number;
    /**
     * 创建子片段
     */
    createFromClip(clip: THREE.AnimationClip): THREE.AnimationClip;
    /**
     * 获取子片段
     */
    getSubClip(): THREE.AnimationClip | null;
    /**
     * 提取轨道片段
     */
    private extractTrackSegment;
    /**
     * 插值计算指定时间的值
     */
    private interpolateValue;
    /**
     * 获取原始片段名称
     */
    getOriginalClipName(): string;
    /**
     * 设置原始片段名称
     */
    setOriginalClipName(name: string): void;
    /**
     * 获取反向播放状态
     */
    getReverse(): boolean;
    /**
     * 设置反向播放状态
     */
    setReverse(reverse: boolean): void;
    /**
     * 获取时间缩放
     */
    getTimeScale(): number;
    /**
     * 设置时间缩放
     */
    setTimeScale(timeScale: number): void;
    /**
     * 添加子片段
     */
    addSubClip(subClip: AnimationSubClip): void;
    /**
     * 移除子片段
     */
    removeSubClip(subClip: AnimationSubClip): boolean;
    /**
     * 获取所有子片段
     */
    getSubClips(): AnimationSubClip[];
    /**
     * 混合子片段
     */
    blendSubClips(weight: number): THREE.AnimationClip | null;
    /**
     * 克隆子片段
     */
    clone(): AnimationSubClip;
}
