/**
 * AvatarAnimationComponent单元测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AvatarAnimationComponent, AnimationGraphNodeType } from '../../src/avatar/components/AvatarAnimationComponent';
import { Animator } from '../../src/animation/Animator';
import { AnimationClip, LoopMode } from '../../src/animation/AnimationClip';
import * as THREE from 'three';

// 模拟Animator类
vi.mock('../../src/animation/Animator', () => {
  return {
    Animator: vi.fn().mockImplementation(() => {
      return {
        play: vi.fn(),
        setLoop: vi.fn(),
        update: vi.fn(),
        getTime: vi.fn().mockReturnValue(0),
        getClips: vi.fn().mockReturnValue([])
      };
    })
  };
});

describe('AvatarAnimationComponent', () => {
  let component: AvatarAnimationComponent;
  let animator: Animator;

  beforeEach(() => {
    // 创建模拟的Animator
    animator = new Animator();
    
    // 创建测试用的组件
    component = new AvatarAnimationComponent(animator);
  });

  it('应该正确创建组件', () => {
    expect(component).toBeDefined();
    expect(component.getAnimator()).toBe(animator);
    expect(component.isEnabled()).toBe(true);
  });

  it('应该能够设置和获取动画控制器', () => {
    // 创建新的Animator
    const newAnimator = new Animator();
    
    // 设置新的Animator
    component.setAnimator(newAnimator);
    
    // 验证Animator已更新
    expect(component.getAnimator()).toBe(newAnimator);
  });

  it('应该能够添加和设置动画图节点', () => {
    // 添加节点
    component.addGraphNode({
      name: 'Idle',
      type: AnimationGraphNodeType.SINGLE,
      data: {
        clipName: 'idle',
        loop: true
      }
    });

    component.addGraphNode({
      name: 'Walk',
      type: AnimationGraphNodeType.SINGLE,
      data: {
        clipName: 'walk',
        loop: true
      }
    });

    // 设置当前节点
    component.setCurrentNode('Idle');
    
    // 验证animator.play被调用
    expect(animator.play).toHaveBeenCalledWith('idle', 0);
  });

  it('应该能够移除动画图节点', () => {
    // 添加节点
    component.addGraphNode({
      name: 'Idle',
      type: AnimationGraphNodeType.SINGLE,
      data: {
        clipName: 'idle',
        loop: true
      }
    });

    // 设置当前节点
    component.setCurrentNode('Idle');
    
    // 验证animator.play被调用
    expect(animator.play).toHaveBeenCalledWith('idle', 0);
    
    // 移除节点
    component.removeGraphNode('Idle');
    
    // 尝试设置已移除的节点
    component.setCurrentNode('Idle');
    
    // 验证animator.play没有再次被调用
    expect(animator.play).toHaveBeenCalledTimes(1);
  });

  it('应该能够设置和获取运动向量', () => {
    // 设置运动向量
    component.setLocomotion(1, 2, 3);
    
    // 获取运动向量
    const locomotion = component.getLocomotion();
    expect(locomotion).toBeInstanceOf(THREE.Vector3);
    expect(locomotion.x).toBe(1);
    expect(locomotion.y).toBe(2);
    expect(locomotion.z).toBe(3);
  });

  it('应该能够设置启用状态', () => {
    // 默认应该是启用的
    expect(component.isEnabled()).toBe(true);
    
    // 禁用组件
    component.setEnabled(false);
    expect(component.isEnabled()).toBe(false);
    
    // 再次启用组件
    component.setEnabled(true);
    expect(component.isEnabled()).toBe(true);
  });

  it('应该能够更新组件', () => {
    // 添加节点
    component.addGraphNode({
      name: 'Idle',
      type: AnimationGraphNodeType.SINGLE,
      data: {
        clipName: 'idle',
        loop: true
      }
    });

    component.addGraphNode({
      name: 'Walk',
      type: AnimationGraphNodeType.SINGLE,
      data: {
        clipName: 'walk',
        loop: true
      }
    });

    // 设置当前节点
    component.setCurrentNode('Idle');
    
    // 更新组件
    component.update(0.1);
    
    // 验证animator.update被调用
    expect(animator.update).toHaveBeenCalledWith(0.1);
  });

  it('应该能够处理混合状态', () => {
    // 添加节点
    component.addGraphNode({
      name: 'Idle',
      type: AnimationGraphNodeType.SINGLE,
      data: {
        clipName: 'idle',
        loop: true
      }
    });

    component.addGraphNode({
      name: 'Walk',
      type: AnimationGraphNodeType.SINGLE,
      data: {
        clipName: 'walk',
        loop: true
      }
    });

    // 设置当前节点
    component.setCurrentNode('Idle');
    
    // 设置混合到新节点
    component.setCurrentNode('Walk', 0.3);
    
    // 验证animator.play被调用
    expect(animator.play).toHaveBeenCalledTimes(2);
    expect(animator.play).toHaveBeenLastCalledWith('walk', 0.3);
    
    // 更新组件
    component.update(0.1);
    
    // 验证animator.update被调用
    expect(animator.update).toHaveBeenCalledWith(0.1);
  });

  it('应该能够添加和移除事件监听器', () => {
    // 创建事件处理函数
    const handler = vi.fn();
    
    // 添加事件监听器
    component.addEventListener('test', handler);
    
    // 触发事件（通过私有方法，这里只是测试）
    (component as any).eventEmitter.emit('test', { data: 'test' });
    
    // 验证事件处理函数被调用
    expect(handler).toHaveBeenCalledWith({ data: 'test' });
    
    // 移除事件监听器
    component.removeEventListener('test', handler);
    
    // 再次触发事件
    (component as any).eventEmitter.emit('test', { data: 'test2' });
    
    // 验证事件处理函数没有再次被调用
    expect(handler).toHaveBeenCalledTimes(1);
  });

  it('应该能够克隆组件', () => {
    // 设置运动向量
    component.setLocomotion(1, 2, 3);
    
    // 禁用组件
    component.setEnabled(false);
    
    // 克隆组件
    const clone = component.clone();
    
    // 验证克隆的组件
    expect(clone).toBeInstanceOf(AvatarAnimationComponent);
    expect(clone.getAnimator()).toBe(animator);
    expect(clone.isEnabled()).toBe(false);
    
    const locomotion = clone.getLocomotion();
    expect(locomotion.x).toBe(1);
    expect(locomotion.y).toBe(2);
    expect(locomotion.z).toBe(3);
  });
});
