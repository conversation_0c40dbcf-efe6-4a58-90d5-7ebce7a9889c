/**
 * 视觉脚本网络安全节点
 * 提供数据加密/解密、用户认证、权限验证等功能
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
/**
 * 数据加密节点
 * 加密数据
 */
export declare class EncryptDataNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 数据解密节点
 * 解密数据
 */
export declare class DecryptDataNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 用户认证节点
 * 验证用户身份
 */
export declare class UserAuthenticationNode extends AsyncNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): Promise<any>;
}
/**
 * 计算哈希节点
 * 计算数据的哈希值
 */
export declare class ComputeHashNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 生成签名节点
 * 生成数据签名
 */
export declare class GenerateSignatureNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 验证签名节点
 * 验证数据签名
 */
export declare class VerifySignatureNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 创建会话节点
 * 创建用户会话
 */
export declare class CreateSessionNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 验证会话节点
 * 验证用户会话
 */
export declare class ValidateSessionNode extends FunctionNode {
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
}
/**
 * 注册网络安全节点
 * @param registry 节点注册表
 */
export declare function registerNetworkSecurityNodes(registry: NodeRegistry): void;
