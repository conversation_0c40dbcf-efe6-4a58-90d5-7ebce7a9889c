import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AlertRuleEntity } from './entities/alert-rule.entity';
import { AlertEntity } from './entities/alert.entity';
import { AlertRuleOperator, AlertRuleType, AlertSeverity } from './entities/alert.types';
import { AlertRuleService } from './alert-rule.service';
import { AlertService } from './alert.service';
import { ServiceMetricsEntity } from '../monitoring/entities/service-metrics.entity';
import { SystemMetricsEntity } from '../monitoring/entities/system-metrics.entity';

@Injectable()
export class AlertEvaluatorService implements OnModuleInit {
  private readonly logger = new Logger(AlertEvaluatorService.name);
  private readonly alertStateCache = new Map<string, {
    violationCount: number;
    lastEvaluationTime: Date;
    alertId?: string;
  }>();

  constructor(
    private readonly alertRuleService: AlertRuleService,
    private readonly alertService: AlertService,
    private readonly eventEmitter: EventEmitter2,
    @InjectRepository(ServiceMetricsEntity)
    private readonly serviceMetricsRepository: Repository<ServiceMetricsEntity>,
    @InjectRepository(SystemMetricsEntity)
    private readonly systemMetricsRepository: Repository<SystemMetricsEntity>,
    @InjectRepository(AlertEntity)
    private readonly alertRepository: Repository<AlertEntity>,
  ) {}

  async onModuleInit() {
    // 初始化告警状态缓存
    await this.initAlertStateCache();
  }

  /**
   * 初始化告警状态缓存
   */
  private async initAlertStateCache(): Promise<void> {
    try {
      // 获取所有活跃的告警
      const activeAlerts = await this.alertService.getActiveAlerts();
      
      for (const alert of activeAlerts) {
        if (alert.ruleId) {
          const cacheKey = this.getCacheKey(alert.ruleId, alert.serviceId, alert.instanceId);
          
          this.alertStateCache.set(cacheKey, {
            violationCount: 1, // 假设已经违反了一次
            lastEvaluationTime: new Date(),
            alertId: alert.id,
          });
        }
      }
      
      this.logger.log(`已初始化 ${this.alertStateCache.size} 个告警状态缓存`);
    } catch (error) {
      this.logger.error(`初始化告警状态缓存失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 定时评估告警规则
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async evaluateAlertRules(): Promise<void> {
    try {
      this.logger.debug('开始评估告警规则');
      
      // 获取所有活跃的告警规则
      const rules = await this.alertRuleService.getActiveAlertRules();
      
      for (const rule of rules) {
        try {
          await this.evaluateRule(rule);
        } catch (error) {
          this.logger.error(`评估告警规则 ${rule.id} 失败: ${error.message}`, error.stack);
        }
      }
      
      this.logger.debug('告警规则评估完成');
    } catch (error) {
      this.logger.error(`评估告警规则失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 评估单个告警规则
   */
  private async evaluateRule(rule: AlertRuleEntity): Promise<void> {
    // 检查冷却时间
    if (rule.lastTriggered && rule.cooldown > 0) {
      const cooldownEndTime = new Date(rule.lastTriggered.getTime() + rule.cooldown * 1000);
      
      if (new Date() < cooldownEndTime) {
        this.logger.debug(`规则 ${rule.id} 在冷却期内，跳过评估`);
        return;
      }
    }
    
    // 根据规则类型评估
    switch (rule.type) {
      case AlertRuleType.THRESHOLD:
        await this.evaluateThresholdRule(rule);
        break;
      case AlertRuleType.ANOMALY:
        await this.evaluateAnomalyRule(rule);
        break;
      case AlertRuleType.ABSENCE:
        await this.evaluateAbsenceRule(rule);
        break;
      case AlertRuleType.RATE_OF_CHANGE:
        await this.evaluateRateOfChangeRule(rule);
        break;
      case AlertRuleType.COMPOSITE:
        await this.evaluateCompositeRule(rule);
        break;
      default:
        this.logger.warn(`未知的规则类型: ${rule.type}`);
    }
  }

  /**
   * 评估阈值规则
   */
  private async evaluateThresholdRule(rule: AlertRuleEntity): Promise<void> {
    // 获取最新的指标数据
    const metrics = await this.getLatestMetrics(rule);
    
    if (!metrics || metrics.length === 0) {
      this.logger.debug(`规则 ${rule.id} 没有找到指标数据`);
      return;
    }
    
    for (const metric of metrics) {
      // 获取指标值
      const value = this.getMetricValue(metric, rule.metricName);
      
      if (value === undefined) {
        this.logger.debug(`规则 ${rule.id} 在指标中未找到 ${rule.metricName}`);
        continue;
      }
      
      // 检查是否违反规则
      const violated = this.checkThresholdViolation(value, rule.operator, rule.threshold);
      
      if (violated) {
        // 更新违反计数
        const serviceId = metric.serviceId || '';
        const instanceId = metric.instanceId || '';
        const cacheKey = this.getCacheKey(rule.id, serviceId, instanceId);
        
        const state = this.alertStateCache.get(cacheKey) || {
          violationCount: 0,
          lastEvaluationTime: new Date(),
        };
        
        state.violationCount++;
        state.lastEvaluationTime = new Date();
        
        this.alertStateCache.set(cacheKey, state);
        
        // 检查是否需要触发告警
        if (state.violationCount >= rule.forDuration) {
          if (!state.alertId) {
            // 创建新告警
            await this.createAlertFromRule(rule, metric, value);
            
            // 更新规则的最后触发时间
            await this.alertRuleService.updateLastTriggered(rule.id);
          }
        }
      } else {
        // 检查是否需要解决告警
        const serviceId = metric.serviceId || '';
        const instanceId = metric.instanceId || '';
        const cacheKey = this.getCacheKey(rule.id, serviceId, instanceId);
        
        const state = this.alertStateCache.get(cacheKey);
        
        if (state && state.alertId) {
          // 解决告警
          await this.alertService.resolveAlert(state.alertId);
          
          // 重置状态
          this.alertStateCache.set(cacheKey, {
            violationCount: 0,
            lastEvaluationTime: new Date(),
          });
        }
      }
    }
  }

  /**
   * 评估异常规则
   */
  private async evaluateAnomalyRule(rule: AlertRuleEntity): Promise<void> {
    // 异常检测规则的实现
    this.logger.debug(`异常检测规则 ${rule.id} 的评估尚未实现`);
  }

  /**
   * 评估缺失规则
   */
  private async evaluateAbsenceRule(rule: AlertRuleEntity): Promise<void> {
    // 缺失检测规则的实现
    this.logger.debug(`缺失检测规则 ${rule.id} 的评估尚未实现`);
  }

  /**
   * 评估变化率规则
   */
  private async evaluateRateOfChangeRule(rule: AlertRuleEntity): Promise<void> {
    // 变化率规则的实现
    this.logger.debug(`变化率规则 ${rule.id} 的评估尚未实现`);
  }

  /**
   * 评估复合规则
   */
  private async evaluateCompositeRule(rule: AlertRuleEntity): Promise<void> {
    // 复合规则的实现
    this.logger.debug(`复合规则 ${rule.id} 的评估尚未实现`);
  }

  /**
   * 获取最新的指标数据
   */
  private async getLatestMetrics(rule: AlertRuleEntity): Promise<any[]> {
    try {
      const now = new Date();
      const startTime = new Date(now.getTime() - 5 * 60 * 1000); // 5分钟前
      
      if (rule.metricName.startsWith('system.')) {
        // 系统指标
        return this.systemMetricsRepository.find({
          where: {
            timestamp: Between(startTime, now),
          },
          order: { timestamp: 'DESC' },
        });
      } else {
        // 服务指标
        const query = this.serviceMetricsRepository.createQueryBuilder('metrics')
          .where('metrics.timestamp BETWEEN :startTime AND :endTime', { startTime, endTime: now })
          .orderBy('metrics.timestamp', 'DESC');
        
        if (rule.serviceType) {
          query.andWhere('metrics.serviceType = :serviceType', { serviceType: rule.serviceType });
        }
        
        if (rule.labels) {
          for (const [key, value] of Object.entries(rule.labels)) {
            query.andWhere(`metrics.labels->>'$.${key}' = :${key}`, { [key]: value });
          }
        }
        
        return query.getMany();
      }
    } catch (error) {
      this.logger.error(`获取指标数据失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 获取指标值
   */
  private getMetricValue(metric: any, metricName: string): number | undefined {
    // 处理嵌套路径，如 'cpu.usage'
    const parts = metricName.split('.');
    
    let value: any = metric;
    
    for (const part of parts) {
      if (value === undefined || value === null) {
        return undefined;
      }
      
      if (part === 'customMetrics' && value.customMetrics) {
        // 特殊处理自定义指标
        const remainingParts = parts.slice(parts.indexOf(part) + 1);
        const customMetricName = remainingParts.join('.');
        
        return value.customMetrics[customMetricName];
      }
      
      value = value[part];
    }
    
    return typeof value === 'number' ? value : undefined;
  }

  /**
   * 检查阈值违反
   */
  private checkThresholdViolation(value: number, operator: AlertRuleOperator, threshold: number): boolean {
    switch (operator) {
      case AlertRuleOperator.GT:
        return value > threshold;
      case AlertRuleOperator.GTE:
        return value >= threshold;
      case AlertRuleOperator.LT:
        return value < threshold;
      case AlertRuleOperator.LTE:
        return value <= threshold;
      case AlertRuleOperator.EQ:
        return value === threshold;
      case AlertRuleOperator.NEQ:
        return value !== threshold;
      default:
        return false;
    }
  }

  /**
   * 从规则创建告警
   */
  private async createAlertFromRule(rule: AlertRuleEntity, metric: any, value: number): Promise<AlertEntity> {
    const alert: Partial<AlertEntity> = {
      name: rule.name,
      description: this.formatAlertDescription(rule, value),
      severity: rule.severity,
      serviceId: metric.serviceId,
      serviceType: metric.serviceType,
      instanceId: metric.instanceId,
      hostname: metric.hostname,
      labels: { ...rule.labels },
      annotations: { ...rule.annotations },
      value: { [rule.metricName]: value },
      ruleId: rule.id,
    };
    
    const createdAlert = await this.alertService.createAlert(alert);
    
    // 更新缓存
    const cacheKey = this.getCacheKey(rule.id, metric.serviceId, metric.instanceId);
    
    this.alertStateCache.set(cacheKey, {
      violationCount: rule.forDuration,
      lastEvaluationTime: new Date(),
      alertId: createdAlert.id,
    });
    
    return createdAlert;
  }

  /**
   * 格式化告警描述
   */
  private formatAlertDescription(rule: AlertRuleEntity, value: number): string {
    return rule.description
      .replace('{{value}}', value.toString())
      .replace('{{threshold}}', rule.threshold.toString())
      .replace('{{operator}}', rule.operator);
  }

  /**
   * 获取缓存键
   */
  private getCacheKey(ruleId: string, serviceId: string, instanceId: string): string {
    return `${ruleId}:${serviceId}:${instanceId}`;
  }
}
