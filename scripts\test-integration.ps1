# 集成测试脚本 (PowerShell版本)
# 用于测试编辑器与服务端微服务的集成

param(
    [switch]$SkipBuild,
    [switch]$Verbose
)

# 错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Info {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 检查依赖
function Test-Dependencies {
    Write-Info "检查依赖..."
    
    # 检查Docker
    try {
        docker --version | Out-Null
        Write-Success "Docker已安装"
    }
    catch {
        Write-Error "Docker未安装或不可用"
        exit 1
    }
    
    # 检查Docker Compose
    try {
        docker-compose --version | Out-Null
        Write-Success "Docker Compose已安装"
    }
    catch {
        Write-Error "Docker Compose未安装或不可用"
        exit 1
    }
    
    Write-Success "依赖检查通过"
}

# 检查服务状态
function Test-Service {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$MaxAttempts = 30
    )
    
    Write-Info "检查服务: $ServiceName"
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -Method Get -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Success "$ServiceName 服务正常"
                return $true
            }
        }
        catch {
            # 继续尝试
        }
        
        Write-Info "等待 $ServiceName 启动... (尝试 $attempt/$MaxAttempts)"
        Start-Sleep -Seconds 2
    }
    
    Write-Error "$ServiceName 服务启动失败"
    return $false
}

# 启动服务
function Start-Services {
    Write-Info "启动微服务..."
    
    # 检查.env文件
    if (-not (Test-Path ".env")) {
        Write-Warning ".env文件不存在，复制.env.example"
        Copy-Item ".env.example" ".env"
    }
    
    # 启动服务
    docker-compose up -d
    
    Write-Info "等待服务启动..."
    Start-Sleep -Seconds 10
}

# 测试服务连接
function Test-Services {
    Write-Info "测试服务连接..."
    
    $failed = 0
    
    # 测试各个服务
    $services = @(
        @{ Name = "API网关"; Url = "http://localhost:3000/api/health" },
        @{ Name = "用户服务"; Url = "http://localhost:4001/health" },
        @{ Name = "项目服务"; Url = "http://localhost:4002/health" },
        @{ Name = "资产服务"; Url = "http://localhost:4003/health" },
        @{ Name = "渲染服务"; Url = "http://localhost:4004/health" },
        @{ Name = "协作服务"; Url = "http://localhost:3007/health" },
        @{ Name = "前端编辑器"; Url = "http://localhost" }
    )
    
    foreach ($service in $services) {
        if (-not (Test-Service -ServiceName $service.Name -Url $service.Url)) {
            $failed++
        }
    }
    
    if ($failed -eq 0) {
        Write-Success "所有服务连接测试通过"
        return $true
    }
    else {
        Write-Error "$failed 个服务连接测试失败"
        return $false
    }
}

# 测试API端点
function Test-ApiEndpoints {
    Write-Info "测试API端点..."
    
    $baseUrl = "http://localhost:3000/api"
    $failed = 0
    
    # 测试健康检查
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/health" -Method Get -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "健康检查端点正常"
        }
        else {
            Write-Error "健康检查端点返回状态码: $($response.StatusCode)"
            $failed++
        }
    }
    catch {
        Write-Error "健康检查端点失败: $($_.Exception.Message)"
        $failed++
    }
    
    # 测试API文档
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/docs" -Method Get -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "API文档端点正常"
        }
    }
    catch {
        Write-Warning "API文档端点不可用"
    }
    
    if ($failed -eq 0) {
        Write-Success "API端点测试通过"
        return $true
    }
    else {
        Write-Error "$failed 个API端点测试失败"
        return $false
    }
}

# 生成测试报告
function New-TestReport {
    param(
        [datetime]$StartTime,
        [datetime]$EndTime
    )
    
    $duration = ($EndTime - $StartTime).TotalSeconds
    
    Write-Info "生成测试报告..."
    
    $report = @"
集成测试报告
====================
测试时间: $(Get-Date)
测试耗时: $([math]::Round($duration, 2))秒

服务状态:
- API网关: http://localhost:3000
- 用户服务: http://localhost:4001
- 项目服务: http://localhost:4002
- 资产服务: http://localhost:4003
- 渲染服务: http://localhost:4004
- 协作服务: http://localhost:3007
- 前端编辑器: http://localhost

测试结果: 请查看控制台输出
"@
    
    $report | Out-File -FilePath "integration_test_report.txt" -Encoding UTF8
    
    Write-Success "测试报告已生成: integration_test_report.txt"
}

# 主函数
function Main {
    $startTime = Get-Date
    
    Write-Host "🔍 DL引擎集成测试" -ForegroundColor Cyan
    Write-Host "==================" -ForegroundColor Cyan
    
    try {
        # 检查依赖
        Test-Dependencies
        
        # 启动服务
        Start-Services
        
        # 测试服务
        $testFailed = 0
        
        if (-not (Test-Services)) {
            $testFailed++
        }
        
        if (-not (Test-ApiEndpoints)) {
            $testFailed++
        }
        
        $endTime = Get-Date
        
        # 生成报告
        New-TestReport -StartTime $startTime -EndTime $endTime
        
        # 输出结果
        Write-Host ""
        if ($testFailed -eq 0) {
            Write-Success "🎉 所有集成测试通过！"
            Write-Host ""
            Write-Host "您可以访问以下地址："
            Write-Host "- 前端编辑器: http://localhost"
            Write-Host "- API文档: http://localhost:3000/api/docs"
            Write-Host "- 服务监控: 在编辑器中点击右下角的调试按钮"
        }
        else {
            Write-Error "❌ $testFailed 个测试失败"
            Write-Host ""
            Write-Host "请检查服务日志："
            Write-Host "docker-compose logs [service-name]"
            exit 1
        }
    }
    catch {
        Write-Error "测试过程中发生错误: $($_.Exception.Message)"
        exit 1
    }
}

# 运行主函数
Main
