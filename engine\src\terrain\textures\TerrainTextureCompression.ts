/**
 * 地形纹理压缩
 * 提供地形纹理压缩功能，支持多种压缩格式
 */
import * as THREE from 'three';
import { Debug } from '../../utils/Debug';

/**
 * 纹理压缩格式
 */
export enum TextureCompressionFormat {
  /** 不压缩 */
  NONE = 'none',
  /** DXT1 (BC1) - RGB压缩，无alpha */
  DXT1 = 'dxt1',
  /** DXT5 (BC3) - RGBA压缩 */
  DXT5 = 'dxt5',
  /** ETC1 - RGB压缩，无alpha，适用于Android */
  ETC1 = 'etc1',
  /** ETC2 - RGBA压缩，适用于Android */
  ETC2 = 'etc2',
  /** ASTC - 高级压缩，支持多种块大小 */
  ASTC = 'astc',
  /** PVRTC - iOS设备压缩格式 */
  PVRTC = 'pvrtc',
  /** BASIS - 通用超级压缩格式 */
  BASIS = 'basis',
  /** KTX2 - 基于Khronos的容器格式 */
  KTX2 = 'ktx2'
}

/**
 * 纹理压缩选项
 */
export interface TextureCompressionOptions {
  /** 压缩格式 */
  format?: TextureCompressionFormat;
  /** 压缩质量 (0-1) */
  quality?: number;
  /** 是否生成mipmap */
  generateMipmaps?: boolean;
  /** 是否使用GPU加速 */
  useGPUCompression?: boolean;
  /** ASTC块大小 (仅ASTC格式) */
  astcBlockSize?: '4x4' | '5x5' | '6x6' | '8x8' | '10x10' | '12x12';
  /** 是否使用sRGB颜色空间 */
  sRGB?: boolean;
  /** 最大纹理尺寸 */
  maxTextureSize?: number;
  /** 是否保留原始纹理 */
  keepOriginal?: boolean;
}

/**
 * 地形纹理压缩类
 */
export class TerrainTextureCompression {
  /** 默认压缩选项 */
  private static readonly DEFAULT_OPTIONS: TextureCompressionOptions = {
    format: TextureCompressionFormat.KTX2,
    quality: 0.8,
    generateMipmaps: true,
    useGPUCompression: true,
    astcBlockSize: '8x8',
    sRGB: true,
    maxTextureSize: 2048,
    keepOriginal: false
  };

  /** 压缩选项 */
  private options: TextureCompressionOptions;

  /** KTX2编码器 */
  private ktx2Encoder: any;

  /** 是否支持压缩纹理 */
  private supportsCompressedTextures: boolean;

  /** 支持的压缩格式 */
  private supportedFormats: TextureCompressionFormat[];

  /**
   * 创建地形纹理压缩
   * @param options 压缩选项
   */
  constructor(options: TextureCompressionOptions = {}) {
    this.options = { ...TerrainTextureCompression.DEFAULT_OPTIONS, ...options };
    this.ktx2Encoder = null;
    this.supportsCompressedTextures = this.checkCompressedTextureSupport();
    this.supportedFormats = this.getSupportedFormatsInternal();

    // 初始化KTX2编码器
    this.initializeKTX2Encoder();
  }

  /**
   * 检查是否支持压缩纹理
   * @returns 是否支持
   */
  private checkCompressedTextureSupport(): boolean {
    try {
      // 检查WebGL支持
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');

      if (!gl) {
        Debug.warn('TerrainTextureCompression', '不支持WebGL，无法使用压缩纹理');
        return false;
      }

      // 检查扩展
      const extensions = [
        'WEBGL_compressed_texture_s3tc',
        'WEBGL_compressed_texture_etc',
        'WEBGL_compressed_texture_astc',
        'WEBGL_compressed_texture_pvrtc'
      ];

      let hasAnyExtension = false;
      for (const ext of extensions) {
        if (gl.getExtension(ext)) {
          hasAnyExtension = true;
          break;
        }
      }

      return hasAnyExtension;
    } catch (error) {
      Debug.error('TerrainTextureCompression', '检查压缩纹理支持时出错:', error);
      return false;
    }
  }

  /**
   * 获取支持的压缩格式（私有方法）
   * @returns 支持的格式列表
   */
  private getSupportedFormatsInternal(): TextureCompressionFormat[] {
    if (!this.supportsCompressedTextures) {
      return [TextureCompressionFormat.NONE];
    }

    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');

      if (!gl) {
        return [TextureCompressionFormat.NONE];
      }

      const formats: TextureCompressionFormat[] = [TextureCompressionFormat.NONE];

      // 检查S3TC (DXT)
      if (gl.getExtension('WEBGL_compressed_texture_s3tc')) {
        formats.push(TextureCompressionFormat.DXT1, TextureCompressionFormat.DXT5);
      }

      // 检查ETC
      if (gl.getExtension('WEBGL_compressed_texture_etc')) {
        formats.push(TextureCompressionFormat.ETC1, TextureCompressionFormat.ETC2);
      }

      // 检查ASTC
      if (gl.getExtension('WEBGL_compressed_texture_astc')) {
        formats.push(TextureCompressionFormat.ASTC);
      }

      // 检查PVRTC
      if (gl.getExtension('WEBGL_compressed_texture_pvrtc')) {
        formats.push(TextureCompressionFormat.PVRTC);
      }

      // KTX2和BASIS总是可用，因为它们是通过转码器支持的
      formats.push(TextureCompressionFormat.KTX2, TextureCompressionFormat.BASIS);

      return formats;
    } catch (error) {
      Debug.error('TerrainTextureCompression', '获取支持的压缩格式时出错:', error);
      return [TextureCompressionFormat.NONE];
    }
  }

  /**
   * 初始化KTX2编码器
   */
  private initializeKTX2Encoder(): void {
    // 这里应该初始化KTX2编码器
    // 实际实现需要加载KTX2编码器库
    this.ktx2Encoder = null;
  }

  /**
   * 压缩纹理
   * @param texture 原始纹理
   * @param options 压缩选项
   * @returns 压缩后的纹理
   */
  public async compressTexture(
    texture: THREE.Texture,
    options?: TextureCompressionOptions
  ): Promise<THREE.CompressedTexture | THREE.Texture> {
    // 合并选项
    const mergedOptions = { ...this.options, ...options };

    // 如果不支持压缩纹理或格式为NONE，则返回原始纹理
    if (!this.supportsCompressedTextures || mergedOptions.format === TextureCompressionFormat.NONE) {
      Debug.log('TerrainTextureCompression', '不使用压缩，返回原始纹理');
      return texture;
    }

    try {
      // 获取图像数据
      const imageData = this.getImageDataFromTexture(texture);
      if (!imageData) {
        Debug.warn('TerrainTextureCompression', '无法获取图像数据，返回原始纹理');
        return texture;
      }

      // 调整图像大小
      const resizedImageData = this.resizeImageIfNeeded(imageData, mergedOptions.maxTextureSize);

      // 根据格式压缩纹理
      switch (mergedOptions.format) {
        case TextureCompressionFormat.KTX2:
          return await this.compressToKTX2(resizedImageData, mergedOptions);
        case TextureCompressionFormat.BASIS:
          return await this.compressToBasis(resizedImageData, mergedOptions);
        case TextureCompressionFormat.DXT1:
        case TextureCompressionFormat.DXT5:
          return this.compressToS3TC(resizedImageData, mergedOptions);
        case TextureCompressionFormat.ETC1:
        case TextureCompressionFormat.ETC2:
          return this.compressToETC(resizedImageData, mergedOptions);
        case TextureCompressionFormat.ASTC:
          return this.compressToASTC(resizedImageData, mergedOptions);
        case TextureCompressionFormat.PVRTC:
          return this.compressToPVRTC(resizedImageData, mergedOptions);
        default:
          Debug.warn('TerrainTextureCompression', `不支持的压缩格式: ${mergedOptions.format}，返回原始纹理`);
          return texture;
      }
    } catch (error) {
      Debug.error('TerrainTextureCompression', '压缩纹理时出错:', error);
      return texture;
    }
  }

  /**
   * 从纹理获取图像数据
   * @param texture 纹理
   * @returns 图像数据
   */
  private getImageDataFromTexture(texture: THREE.Texture): ImageData | null {
    try {
      // 创建画布
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        Debug.warn('TerrainTextureCompression', '无法创建2D上下文');
        return null;
      }

      // 获取图像
      const image = texture.image;
      if (!image) {
        Debug.warn('TerrainTextureCompression', '纹理没有图像数据');
        return null;
      }

      // 设置画布大小
      canvas.width = image.width;
      canvas.height = image.height;

      // 绘制图像
      ctx.drawImage(image, 0, 0);

      // 获取图像数据
      return ctx.getImageData(0, 0, canvas.width, canvas.height);
    } catch (error) {
      Debug.error('TerrainTextureCompression', '获取图像数据时出错:', error);
      return null;
    }
  }

  /**
   * 如果需要，调整图像大小
   * @param imageData 图像数据
   * @param maxSize 最大尺寸
   * @returns 调整后的图像数据
   */
  private resizeImageIfNeeded(imageData: ImageData, maxSize?: number): ImageData {
    if (!maxSize) {
      return imageData;
    }

    const { width, height } = imageData;

    // 如果图像已经小于最大尺寸，则不需要调整
    if (width <= maxSize && height <= maxSize) {
      return imageData;
    }

    try {
      // 计算新尺寸
      let newWidth = width;
      let newHeight = height;

      if (width > height) {
        newWidth = maxSize;
        newHeight = Math.floor(height * (maxSize / width));
      } else {
        newHeight = maxSize;
        newWidth = Math.floor(width * (maxSize / height));
      }

      // 创建画布
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        Debug.warn('TerrainTextureCompression', '无法创建2D上下文');
        return imageData;
      }

      // 设置画布大小
      canvas.width = newWidth;
      canvas.height = newHeight;

      // 创建临时画布来绘制原始图像数据
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');

      if (!tempCtx) {
        Debug.warn('TerrainTextureCompression', '无法创建临时2D上下文');
        return imageData;
      }

      tempCanvas.width = width;
      tempCanvas.height = height;
      tempCtx.putImageData(imageData, 0, 0);

      // 绘制调整大小的图像
      ctx.drawImage(tempCanvas, 0, 0, width, height, 0, 0, newWidth, newHeight);

      // 获取调整后的图像数据
      return ctx.getImageData(0, 0, newWidth, newHeight);
    } catch (error) {
      Debug.error('TerrainTextureCompression', '调整图像大小时出错:', error);
      return imageData;
    }
  }

  /**
   * 压缩为KTX2格式
   * @param imageData 图像数据
   * @param options 压缩选项
   * @returns 压缩后的纹理
   */
  private async compressToKTX2(
    imageData: ImageData,
    options: TextureCompressionOptions
  ): Promise<THREE.CompressedTexture | THREE.Texture> {
    if (!this.ktx2Encoder) {
      Debug.warn('TerrainTextureCompression', 'KTX2编码器未初始化，返回未压缩纹理');
      return this.createUncompressedTexture(imageData);
    }

    try {
      // 编码为KTX2
      const ktx2Data = await this.ktx2Encoder.encode(imageData, {
        uastc: true,
        qualityLevel: Math.floor(options.quality! * 255),
        srgb: options.sRGB,
        mipmaps: options.generateMipmaps
      });

      // 创建压缩纹理
      const texture = new THREE.CompressedTexture(
        [], // mipmaps 数组，暂时为空
        imageData.width,
        imageData.height,
        THREE.RGBA_ASTC_8x8_Format, // 这里应该根据实际格式设置
        THREE.UnsignedByteType
      );

      // 手动设置压缩数据
      (texture as any).mipmaps = [{
        data: new Uint8Array(ktx2Data),
        width: imageData.width,
        height: imageData.height
      }];

      // 设置纹理属性
      texture.wrapS = THREE.ClampToEdgeWrapping;
      texture.wrapT = THREE.ClampToEdgeWrapping;
      texture.minFilter = THREE.LinearMipmapLinearFilter;
      texture.magFilter = THREE.LinearFilter;
      texture.generateMipmaps = false; // KTX2已经包含mipmap
      texture.needsUpdate = true;

      return texture;
    } catch (error) {
      Debug.error('TerrainTextureCompression', '压缩为KTX2时出错:', error);
      return this.createUncompressedTexture(imageData);
    }
  }

  /**
   * 压缩为Basis格式
   * @param imageData 图像数据
   * @param options 压缩选项
   * @returns 压缩后的纹理
   */
  private async compressToBasis(
    imageData: ImageData,
    options: TextureCompressionOptions
  ): Promise<THREE.CompressedTexture | THREE.Texture> {
    // Basis压缩通常通过KTX2编码器实现
    return this.compressToKTX2(imageData, options);
  }

  /**
   * 压缩为S3TC格式(DXT1/DXT5)
   * @param imageData 图像数据
   * @param _options 压缩选项（暂未使用）
   * @returns 压缩后的纹理
   */
  private compressToS3TC(
    imageData: ImageData,
    _options: TextureCompressionOptions
  ): THREE.CompressedTexture | THREE.Texture {
    // 浏览器环境中无法直接生成S3TC压缩纹理
    // 通常需要预先压缩好的纹理
    Debug.warn('TerrainTextureCompression', 'S3TC压缩需要预先压缩的纹理，返回未压缩纹理');
    return this.createUncompressedTexture(imageData);
  }

  /**
   * 压缩为ETC格式(ETC1/ETC2)
   * @param imageData 图像数据
   * @param _options 压缩选项（暂未使用）
   * @returns 压缩后的纹理
   */
  private compressToETC(
    imageData: ImageData,
    _options: TextureCompressionOptions
  ): THREE.CompressedTexture | THREE.Texture {
    // 浏览器环境中无法直接生成ETC压缩纹理
    // 通常需要预先压缩好的纹理
    Debug.warn('TerrainTextureCompression', 'ETC压缩需要预先压缩的纹理，返回未压缩纹理');
    return this.createUncompressedTexture(imageData);
  }

  /**
   * 压缩为ASTC格式
   * @param imageData 图像数据
   * @param _options 压缩选项（暂未使用）
   * @returns 压缩后的纹理
   */
  private compressToASTC(
    imageData: ImageData,
    _options: TextureCompressionOptions
  ): THREE.CompressedTexture | THREE.Texture {
    // 浏览器环境中无法直接生成ASTC压缩纹理
    // 通常需要预先压缩好的纹理
    Debug.warn('TerrainTextureCompression', 'ASTC压缩需要预先压缩的纹理，返回未压缩纹理');
    return this.createUncompressedTexture(imageData);
  }

  /**
   * 压缩为PVRTC格式
   * @param imageData 图像数据
   * @param _options 压缩选项（暂未使用）
   * @returns 压缩后的纹理
   */
  private compressToPVRTC(
    imageData: ImageData,
    _options: TextureCompressionOptions
  ): THREE.CompressedTexture | THREE.Texture {
    // 浏览器环境中无法直接生成PVRTC压缩纹理
    // 通常需要预先压缩好的纹理
    Debug.warn('TerrainTextureCompression', 'PVRTC压缩需要预先压缩的纹理，返回未压缩纹理');
    return this.createUncompressedTexture(imageData);
  }

  /**
   * 创建未压缩纹理
   * @param imageData 图像数据
   * @returns 未压缩纹理
   */
  private createUncompressedTexture(imageData: ImageData): THREE.Texture {
    // 创建画布
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('无法创建2D上下文');
    }

    // 设置画布大小
    canvas.width = imageData.width;
    canvas.height = imageData.height;

    // 绘制图像数据
    ctx.putImageData(imageData, 0, 0);

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas);

    // 设置纹理属性
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;
    texture.minFilter = THREE.LinearMipmapLinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.generateMipmaps = true;
    texture.needsUpdate = true;

    return texture;
  }

  /**
   * 获取支持的压缩格式
   * @returns 支持的格式列表
   */
  public getSupportedFormats(): TextureCompressionFormat[] {
    return this.supportedFormats;
  }

  /**
   * 是否支持压缩纹理
   * @returns 是否支持
   */
  public isCompressionSupported(): boolean {
    return this.supportsCompressedTextures;
  }

  /**
   * 设置压缩选项
   * @param options 压缩选项
   */
  public setOptions(options: TextureCompressionOptions): void {
    this.options = { ...this.options, ...options };
  }
}
