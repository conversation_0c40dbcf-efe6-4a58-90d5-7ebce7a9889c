/**
 * 知识库管理面板
 * 支持文档上传、分类管理、知识点绑定
 */

import React, { useState, useEffect } from 'react';
import {
  Layout,
  Button,
  Upload,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  Space,
  Card,
  List,
  Tooltip,
  message,
  Popconfirm,
  Progress,
  Tabs,
  Divider
} from 'antd';
import {
  FolderOutlined,
  FileTextOutlined,
  FileImageOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  UploadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  LinkOutlined,
  SearchOutlined,
  TagsOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd/es/upload/interface';

const { Sider, Content } = Layout;
const { TextArea } = Input;
const { Option } = Select;

/**
 * 知识点数据接口
 */
interface KnowledgeData {
  id: string;
  title: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document';
  tags: string[];
  category: string;
  priority: number;
  relatedTopics: string[];
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  fileUrl?: string;
  fileSize?: number;
}

/**
 * 知识库类别
 */
interface KnowledgeCategory {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  parentId?: string;
  children?: KnowledgeCategory[];
}

/**
 * 知识库面板属性
 */
interface KnowledgeBasePanelProps {
  onKnowledgeSelected?: (knowledge: KnowledgeData) => void;
  onKnowledgeUpdated?: (knowledge: KnowledgeData) => void;
  onKnowledgeDeleted?: (knowledgeId: string) => void;
  selectedKnowledgeId?: string;
}

/**
 * 知识库管理面板组件
 */
export const KnowledgeBasePanel: React.FC<KnowledgeBasePanelProps> = ({
  onKnowledgeSelected,
  onKnowledgeUpdated,
  onKnowledgeDeleted,
  selectedKnowledgeId
}) => {
  
  // 状态管理
  const [knowledgeList, setKnowledgeList] = useState<KnowledgeData[]>([]);
  const [categories, setCategories] = useState<KnowledgeCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchText, setSearchText] = useState<string>('');
  const [selectedKnowledge, setSelectedKnowledge] = useState<KnowledgeData | null>(null);
  
  // 模态框状态
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  
  // 表单
  const [uploadForm] = Form.useForm();
  const [editForm] = Form.useForm();
  
  // 上传状态
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  /**
   * 初始化数据
   */
  useEffect(() => {
    loadKnowledgeList();
    loadCategories();
  }, []);

  /**
   * 加载知识列表
   */
  const loadKnowledgeList = async () => {
    try {
      // 这里应该调用实际的API
      const mockData: KnowledgeData[] = [
        {
          id: '1',
          title: '数字人技术介绍',
          content: '数字人是基于计算机图形学、人工智能等技术创建的虚拟人物...',
          type: 'text',
          tags: ['数字人', '技术', '介绍'],
          category: 'technical',
          priority: 1,
          relatedTopics: ['AI', '3D建模'],
          createdAt: '2025-01-01T00:00:00Z',
          updatedAt: '2025-01-01T00:00:00Z'
        },
        {
          id: '2',
          title: '虚拟展厅设计指南',
          content: '虚拟展厅是利用3D技术构建的数字化展示空间...',
          type: 'document',
          tags: ['展厅', '设计', '指南'],
          category: 'design',
          priority: 2,
          relatedTopics: ['3D设计', '用户体验'],
          createdAt: '2025-01-02T00:00:00Z',
          updatedAt: '2025-01-02T00:00:00Z',
          fileUrl: '/documents/virtual-gallery-guide.pdf',
          fileSize: 2048000
        }
      ];
      
      setKnowledgeList(mockData);
    } catch (error) {
      message.error('加载知识列表失败');
    }
  };

  /**
   * 加载类别列表
   */
  const loadCategories = async () => {
    try {
      const mockCategories: KnowledgeCategory[] = [
        {
          id: 'technical',
          name: '技术文档',
          description: '技术相关的知识内容',
          color: '#1890ff',
          icon: 'code'
        },
        {
          id: 'design',
          name: '设计指南',
          description: '设计相关的知识内容',
          color: '#52c41a',
          icon: 'design'
        },
        {
          id: 'tutorial',
          name: '教程指导',
          description: '教程和指导类内容',
          color: '#faad14',
          icon: 'book'
        },
        {
          id: 'faq',
          name: '常见问题',
          description: '常见问题和解答',
          color: '#f5222d',
          icon: 'question'
        }
      ];
      
      setCategories(mockCategories);
    } catch (error) {
      message.error('加载类别列表失败');
    }
  };

  /**
   * 获取文件类型图标
   */
  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'text':
        return <FileTextOutlined />;
      case 'image':
        return <FileImageOutlined />;
      case 'video':
        return <VideoCameraOutlined />;
      case 'audio':
        return <AudioOutlined />;
      case 'document':
        return <FileTextOutlined />;
      default:
        return <FileTextOutlined />;
    }
  };

  /**
   * 获取类别颜色
   */
  const getCategoryColor = (categoryId: string): string => {
    const category = categories.find(c => c.id === categoryId);
    return category?.color || '#666666';
  };

  /**
   * 过滤知识列表
   */
  const filteredKnowledgeList = knowledgeList.filter(knowledge => {
    const matchesCategory = selectedCategory === 'all' || knowledge.category === selectedCategory;
    const matchesSearch = !searchText || 
      knowledge.title.toLowerCase().includes(searchText.toLowerCase()) ||
      knowledge.content.toLowerCase().includes(searchText.toLowerCase()) ||
      knowledge.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  /**
   * 渲染知识列表标签页
   */
  const renderKnowledgeList = () => (
    <>
      {/* 搜索栏 */}
      <div style={{ marginBottom: '16px' }}>
        <Input
          placeholder="搜索知识点..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: '300px' }}
        />
      </div>

      {/* 知识列表 */}
      <List
        dataSource={filteredKnowledgeList}
        renderItem={(knowledge) => (
          <List.Item
            style={{
              backgroundColor: selectedKnowledgeId === knowledge.id ? '#e6f7ff' : 'transparent',
              padding: '12px',
              borderRadius: '4px',
              marginBottom: '8px',
              border: '1px solid #f0f0f0',
              cursor: 'pointer'
            }}
            onClick={() => handleKnowledgeSelect(knowledge)}
            actions={[
              <Tooltip title="编辑">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleKnowledgeEdit(knowledge);
                  }}
                />
              </Tooltip>,
              <Tooltip title="删除">
                <Popconfirm
                  title="确定要删除这个知识点吗？"
                  onConfirm={(e) => {
                    e?.stopPropagation();
                    handleKnowledgeDelete(knowledge.id);
                  }}
                >
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={(e) => e?.stopPropagation()}
                  />
                </Popconfirm>
              </Tooltip>
            ]}
          >
            <List.Item.Meta
              avatar={getFileTypeIcon(knowledge.type)}
              title={
                <Space>
                  {knowledge.title}
                  <Tag color={getCategoryColor(knowledge.category)}>
                    {categories.find(c => c.id === knowledge.category)?.name || knowledge.category}
                  </Tag>
                </Space>
              }
              description={
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    {knowledge.content.length > 100
                      ? `${knowledge.content.substring(0, 100)}...`
                      : knowledge.content
                    }
                  </div>
                  <Space wrap>
                    {knowledge.tags.map(tag => (
                      <Tag key={tag}>
                        {tag}
                      </Tag>
                    ))}
                  </Space>
                </div>
              }
            />
          </List.Item>
        )}
      />
    </>
  );

  /**
   * 渲染知识详情标签页
   */
  const renderKnowledgeDetail = () => (
    selectedKnowledge ? (
      <Card title={selectedKnowledge.title}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <strong>类型：</strong>
            {getFileTypeIcon(selectedKnowledge.type)} {selectedKnowledge.type}
          </div>

          <div>
            <strong>类别：</strong>
            <Tag color={getCategoryColor(selectedKnowledge.category)}>
              {categories.find(c => c.id === selectedKnowledge.category)?.name || selectedKnowledge.category}
            </Tag>
          </div>

          <div>
            <strong>标签：</strong>
            <Space wrap>
              {selectedKnowledge.tags.map(tag => (
                <Tag key={tag} icon={<TagsOutlined />}>
                  {tag}
                </Tag>
              ))}
            </Space>
          </div>

          <div>
            <strong>优先级：</strong>
            {selectedKnowledge.priority}
          </div>

          <div>
            <strong>内容：</strong>
            <div style={{
              marginTop: '8px',
              padding: '12px',
              backgroundColor: '#f9f9f9',
              borderRadius: '4px',
              whiteSpace: 'pre-wrap'
            }}>
              {selectedKnowledge.content}
            </div>
          </div>

          {selectedKnowledge.relatedTopics.length > 0 && (
            <div>
              <strong>相关主题：</strong>
              <Space wrap>
                {selectedKnowledge.relatedTopics.map(topic => (
                  <Tag key={topic} icon={<LinkOutlined />}>
                    {topic}
                  </Tag>
                ))}
              </Space>
            </div>
          )}

          <div>
            <strong>创建时间：</strong>
            {new Date(selectedKnowledge.createdAt).toLocaleString()}
          </div>

          <div>
            <strong>更新时间：</strong>
            {new Date(selectedKnowledge.updatedAt).toLocaleString()}
          </div>
        </Space>
      </Card>
    ) : (
      <div style={{
        textAlign: 'center',
        padding: '60px',
        color: '#999'
      }}>
        请选择一个知识点查看详情
      </div>
    )
  );

  /**
   * 处理文件上传
   */
  const handleUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;
    
    try {
      setUploading(true);
      setUploadProgress(0);
      
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);
      
      // 这里应该调用实际的上传API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      // 模拟上传成功
      const uploadedFile = {
        id: Date.now().toString(),
        name: (file as File).name,
        url: URL.createObjectURL(file as File),
        size: (file as File).size
      };
      
      onSuccess?.(uploadedFile);
      message.success('文件上传成功');
      
    } catch (error) {
      onError?.(error as Error);
      message.error('文件上传失败');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  /**
   * 处理知识点选择
   */
  const handleKnowledgeSelect = (knowledge: KnowledgeData) => {
    setSelectedKnowledge(knowledge);
    if (onKnowledgeSelected) {
      onKnowledgeSelected(knowledge);
    }
  };

  /**
   * 处理知识点编辑
   */
  const handleKnowledgeEdit = (knowledge: KnowledgeData) => {
    setSelectedKnowledge(knowledge);
    editForm.setFieldsValue(knowledge);
    setEditModalVisible(true);
  };

  /**
   * 处理知识点删除
   */
  const handleKnowledgeDelete = async (knowledgeId: string) => {
    try {
      // 这里应该调用实际的删除API
      setKnowledgeList(prev => prev.filter(k => k.id !== knowledgeId));
      
      if (onKnowledgeDeleted) {
        onKnowledgeDeleted(knowledgeId);
      }
      
      message.success('知识点删除成功');
    } catch (error) {
      message.error('知识点删除失败');
    }
  };

  /**
   * 提交上传表单
   */
  const handleUploadSubmit = async (values: any) => {
    try {
      const newKnowledge: KnowledgeData = {
        id: Date.now().toString(),
        ...values,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      setKnowledgeList(prev => [...prev, newKnowledge]);
      setUploadModalVisible(false);
      uploadForm.resetFields();
      message.success('知识点创建成功');
    } catch (error) {
      message.error('知识点创建失败');
    }
  };

  /**
   * 提交编辑表单
   */
  const handleEditSubmit = async (values: any) => {
    try {
      if (!selectedKnowledge) return;

      const updatedKnowledge: KnowledgeData = {
        ...selectedKnowledge,
        ...values,
        updatedAt: new Date().toISOString()
      };

      setKnowledgeList(prev =>
        prev.map(k => k.id === selectedKnowledge.id ? updatedKnowledge : k)
      );

      if (onKnowledgeUpdated) {
        onKnowledgeUpdated(updatedKnowledge);
      }

      setEditModalVisible(false);
      editForm.resetFields();
      message.success('知识点更新成功');
    } catch (error) {
      message.error('知识点更新失败');
    }
  };

  return (
    <Layout style={{ height: '100%' }}>
      {/* 左侧类别树 */}
      <Sider width={200} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
        <div style={{ padding: '16px 8px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setUploadModalVisible(true)}
              style={{ width: '100%' }}
            >
              添加知识
            </Button>

            <Button
              icon={<SettingOutlined />}
              onClick={() => setCategoryModalVisible(true)}
              style={{ width: '100%' }}
            >
              管理类别
            </Button>
          </Space>
        </div>

        <Divider style={{ margin: '8px 0' }} />

        <div style={{ padding: '0 8px' }}>
          <div
            style={{
              padding: '8px 12px',
              cursor: 'pointer',
              backgroundColor: selectedCategory === 'all' ? '#e6f7ff' : 'transparent',
              borderRadius: '4px',
              marginBottom: '4px'
            }}
            onClick={() => setSelectedCategory('all')}
          >
            <FolderOutlined style={{ marginRight: '8px' }} />
            全部知识
          </div>

          {categories.map(category => (
            <div
              key={category.id}
              style={{
                padding: '8px 12px',
                cursor: 'pointer',
                backgroundColor: selectedCategory === category.id ? '#e6f7ff' : 'transparent',
                borderRadius: '4px',
                marginBottom: '4px'
              }}
              onClick={() => setSelectedCategory(category.id)}
            >
              <FolderOutlined
                style={{
                  marginRight: '8px',
                  color: category.color
                }}
              />
              {category.name}
            </div>
          ))}
        </div>
      </Sider>

      {/* 右侧内容区 */}
      <Content style={{ padding: '16px' }}>
        <Tabs
          defaultActiveKey="list"
          items={[
            {
              key: 'list',
              label: '知识列表',
              children: renderKnowledgeList()
            },
            {
              key: 'detail',
              label: '知识详情',
              children: renderKnowledgeDetail()
            }
          ]}
        />
      </Content>

      {/* 上传知识模态框 */}
      <Modal
        title="添加知识"
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false);
          uploadForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={uploadForm}
          layout="vertical"
          onFinish={handleUploadSubmit}
        >
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input placeholder="请输入知识点标题" />
          </Form.Item>

          <Form.Item
            name="type"
            label="类型"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Select placeholder="请选择知识类型">
              <Option value="text">文本</Option>
              <Option value="image">图片</Option>
              <Option value="video">视频</Option>
              <Option value="audio">音频</Option>
              <Option value="document">文档</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="category"
            label="类别"
            rules={[{ required: true, message: '请选择类别' }]}
          >
            <Select placeholder="请选择知识类别">
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="content"
            label="内容"
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <TextArea
              rows={6}
              placeholder="请输入知识点内容"
            />
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Select
              mode="tags"
              placeholder="请输入标签，按回车添加"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            initialValue={1}
          >
            <Select>
              <Option value={1}>低</Option>
              <Option value={2}>中</Option>
              <Option value={3}>高</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="relatedTopics"
            label="相关主题"
          >
            <Select
              mode="tags"
              placeholder="请输入相关主题，按回车添加"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item label="文件上传">
            <Upload
              customRequest={handleUpload}
              showUploadList={false}
              accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.mp4,.mp3,.wav"
            >
              <Button icon={<UploadOutlined />} loading={uploading}>
                {uploading ? '上传中...' : '选择文件'}
              </Button>
            </Upload>
            {uploading && (
              <Progress percent={uploadProgress} style={{ marginTop: '8px' }} />
            )}
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={uploading}>
                创建
              </Button>
              <Button onClick={() => {
                setUploadModalVisible(false);
                uploadForm.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑知识模态框 */}
      <Modal
        title="编辑知识"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditSubmit}
        >
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input placeholder="请输入知识点标题" />
          </Form.Item>

          <Form.Item
            name="type"
            label="类型"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Select placeholder="请选择知识类型">
              <Option value="text">文本</Option>
              <Option value="image">图片</Option>
              <Option value="video">视频</Option>
              <Option value="audio">音频</Option>
              <Option value="document">文档</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="category"
            label="类别"
            rules={[{ required: true, message: '请选择类别' }]}
          >
            <Select placeholder="请选择知识类别">
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="content"
            label="内容"
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <TextArea
              rows={6}
              placeholder="请输入知识点内容"
            />
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Select
              mode="tags"
              placeholder="请输入标签，按回车添加"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
          >
            <Select>
              <Option value={1}>低</Option>
              <Option value={2}>中</Option>
              <Option value={3}>高</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="relatedTopics"
            label="相关主题"
          >
            <Select
              mode="tags"
              placeholder="请输入相关主题，按回车添加"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                更新
              </Button>
              <Button onClick={() => {
                setEditModalVisible(false);
                editForm.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 类别管理模态框 */}
      <Modal
        title="管理类别"
        open={categoryModalVisible}
        onCancel={() => setCategoryModalVisible(false)}
        footer={null}
        width={500}
      >
        <div>
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            style={{ width: '100%', marginBottom: '16px' }}
          >
            添加新类别
          </Button>

          <List
            dataSource={categories}
            renderItem={(category) => (
              <List.Item
                actions={[
                  <Button type="text" icon={<EditOutlined />} />,
                  <Button type="text" danger icon={<DeleteOutlined />} />
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <div
                      style={{
                        width: '20px',
                        height: '20px',
                        backgroundColor: category.color,
                        borderRadius: '4px'
                      }}
                    />
                  }
                  title={category.name}
                  description={category.description}
                />
              </List.Item>
            )}
          />
        </div>
      </Modal>
    </Layout>
  );
};

export default KnowledgeBasePanel;
