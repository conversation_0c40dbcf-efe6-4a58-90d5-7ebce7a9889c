/**
 * 八叉树实现
 * 用于空间分割和快速查询
 */
import * as THREE from 'three';
/**
 * 八叉树节点接口
 */
export interface OctreeNode {
    /** 节点边界框 */
    bounds: THREE.Box3;
    /** 节点中心点 */
    center: THREE.Vector3;
    /** 节点大小 */
    size: number;
    /** 节点深度 */
    depth: number;
    /** 子节点 */
    children: OctreeNode[] | null;
    /** 节点中的对象 */
    objects: string[];
    /** 对象位置映射 */
    objectPositions: Map<string, THREE.Vector3>;
    /** 对象半径映射 */
    objectRadii: Map<string, number>;
}
/**
 * 八叉树配置选项
 */
export interface OctreeOptions {
    /** 八叉树大小 */
    size: number;
    /** 最大深度 */
    maxDepth: number;
    /** 每个节点最大对象数 */
    maxObjects: number;
    /** 八叉树中心点 */
    center?: THREE.Vector3;
}
/**
 * 八叉树类
 */
export declare class Octree {
    /** 根节点 */
    private root;
    /** 最大深度 */
    private maxDepth;
    /** 每个节点最大对象数 */
    private maxObjects;
    /** 对象到节点的映射 */
    private objectToNode;
    /**
     * 创建八叉树
     * @param options 配置选项
     */
    constructor(options: OctreeOptions);
    /**
     * 插入对象
     * @param objectId 对象ID
     * @param position 对象位置
     * @param radius 对象半径
     */
    insert(objectId: string, position: THREE.Vector3, radius?: number): void;
    /**
     * 移除对象
     * @param objectId 对象ID
     */
    remove(objectId: string): void;
    /**
     * 更新对象位置
     * @param objectId 对象ID
     * @param newPosition 新位置
     * @param radius 对象半径
     */
    update(objectId: string, newPosition: THREE.Vector3, radius?: number): void;
    /**
     * 获取与视锥体相交的节点
     * @param frustum 视锥体
     * @returns 相交的节点数组
     */
    getFrustumIntersectedNodes(frustum: THREE.Frustum): OctreeNode[];
    /**
     * 获取与包围盒相交的对象
     * @param box 包围盒
     * @returns 相交的对象ID数组
     */
    getObjectsInBox(box: THREE.Box3): string[];
    /**
     * 获取与球体相交的对象
     * @param sphere 球体
     * @returns 相交的对象ID数组
     */
    getObjectsInSphere(sphere: THREE.Sphere): string[];
    /**
     * 清空八叉树
     */
    clear(): void;
    /**
     * 获取统计信息
     */
    getStats(): {
        totalNodes: number;
        totalObjects: number;
        maxDepth: number;
        averageObjectsPerNode: number;
    };
    /**
     * 将对象插入到节点中
     * @param node 节点
     * @param objectId 对象ID
     * @param position 对象位置
     * @param radius 对象半径
     */
    private insertIntoNode;
    /**
     * 检查对象是否在节点内
     * @param node 节点
     * @param position 对象位置
     * @param radius 对象半径
     * @returns 是否在节点内
     */
    private isObjectInNode;
    /**
     * 分割节点
     * @param node 要分割的节点
     */
    private subdivideNode;
    /**
     * 尝试合并节点
     * @param node 节点
     */
    private tryMergeNode;
    /**
     * 递归获取与视锥体相交的节点
     * @param node 当前节点
     * @param frustum 视锥体
     * @param result 结果数组
     */
    private getFrustumIntersectedNodesRecursive;
    /**
     * 递归获取与包围盒相交的对象
     * @param node 当前节点
     * @param box 包围盒
     * @param result 结果数组
     */
    private getObjectsInBoxRecursive;
    /**
     * 递归获取与球体相交的对象
     * @param node 当前节点
     * @param sphere 球体
     * @param result 结果数组
     */
    private getObjectsInSphereRecursive;
    /**
     * 清空节点
     * @param node 节点
     */
    private clearNode;
    /**
     * 递归获取统计信息
     * @param node 当前节点
     * @param stats 统计信息对象
     */
    private getStatsRecursive;
}
