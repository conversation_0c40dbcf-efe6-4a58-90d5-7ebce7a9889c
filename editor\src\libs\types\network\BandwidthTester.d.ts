/**
 * 带宽测试器
 * 用于测试网络带宽和延迟
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 带宽测试配置
 */
export interface BandwidthTestConfig {
    /** 测试服务器URL */
    serverUrl: string;
    /** 上传测试数据大小（字节） */
    uploadSize?: number;
    /** 下载测试数据大小（字节） */
    downloadSize?: number;
    /** 测试超时时间（毫秒） */
    timeout?: number;
    /** 测试重试次数 */
    retries?: number;
    /** 是否启用详细日志 */
    detailedLogging?: boolean;
    /** 测试并发连接数 */
    concurrentConnections?: number;
    /** 是否使用WebSocket进行测试 */
    useWebSocket?: boolean;
    /** 是否使用HTTP进行测试 */
    useHttp?: boolean;
    /** 是否测试上传带宽 */
    testUpload?: boolean;
    /** 是否测试下载带宽 */
    testDownload?: boolean;
    /** 是否测试延迟 */
    testLatency?: boolean;
    /** 延迟测试次数 */
    latencyTestCount?: number;
}
/**
 * 带宽测试结果
 */
export interface BandwidthTestResult {
    /** 上传带宽（字节/秒） */
    uploadBandwidth: number;
    /** 下载带宽（字节/秒） */
    downloadBandwidth: number;
    /** 延迟（毫秒） */
    latency: number;
    /** 抖动（毫秒） */
    jitter: number;
    /** 测试是否成功 */
    success: boolean;
    /** 错误信息 */
    error?: string;
    /** 测试开始时间 */
    startTime: number;
    /** 测试结束时间 */
    endTime: number;
    /** 测试持续时间（毫秒） */
    duration: number;
    /** 上传测试详情 */
    uploadDetails?: {
        /** 上传的字节数 */
        bytes: number;
        /** 上传时间（毫秒） */
        time: number;
        /** 上传速度（字节/秒） */
        speed: number;
    };
    /** 下载测试详情 */
    downloadDetails?: {
        /** 下载的字节数 */
        bytes: number;
        /** 下载时间（毫秒） */
        time: number;
        /** 下载速度（字节/秒） */
        speed: number;
    };
    /** 延迟测试详情 */
    latencyDetails?: {
        /** 最小延迟（毫秒） */
        min: number;
        /** 最大延迟（毫秒） */
        max: number;
        /** 平均延迟（毫秒） */
        avg: number;
        /** 延迟标准差（毫秒） */
        stdDev: number;
        /** 延迟测量值列表（毫秒） */
        values: number[];
    };
}
/**
 * 带宽测试器
 * 用于测试网络带宽和延迟
 */
export declare class BandwidthTester extends EventEmitter {
    /** 配置 */
    private config;
    /** 当前测试结果 */
    private currentResult;
    /** 测试是否正在进行 */
    private testing;
    /** WebSocket连接 */
    private ws;
    /** 测试超时定时器ID */
    private timeoutId;
    /** 延迟测试结果 */
    private latencyResults;
    /** 上传测试开始时间 */
    private uploadStartTime;
    /** 下载测试开始时间 */
    private downloadStartTime;
    /**
     * 创建带宽测试器
     * @param config 配置
     */
    constructor(config: BandwidthTestConfig);
    /**
     * 开始测试
     * @returns 测试结果Promise
     */
    startTest(): Promise<BandwidthTestResult>;
    /**
     * 取消测试
     */
    cancelTest(): void;
    /**
     * 设置测试超时
     */
    private setTestTimeout;
    /**
     * 清除测试超时
     */
    private clearTestTimeout;
    /**
     * 重置测试结果
     */
    private resetTestResult;
    /**
     * 获取测试结果
     * @returns 测试结果
     */
    getTestResult(): BandwidthTestResult;
    /**
     * 测试延迟
     */
    private testLatency;
    /**
     * 测试下载带宽
     */
    private testDownloadBandwidth;
    /**
     * 测试上传带宽
     */
    private testUploadBandwidth;
    /**
     * 测量延迟
     * @returns 延迟（毫秒）
     */
    private measureLatency;
    /**
     * 销毁测试器
     */
    dispose(): void;
}
