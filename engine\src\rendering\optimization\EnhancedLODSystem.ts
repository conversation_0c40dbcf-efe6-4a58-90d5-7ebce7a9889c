/**
 * 增强的LOD系统
 * 提供高质量、高效率的LOD管理和平滑过渡
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { LODComponent, LODLevel, LODLevelConfig } from './LODComponent';
import { LODSystem, LODSystemOptions, LODSystemEventType } from './LODSystem';
import { EnhancedLODGenerator, SimplificationAlgorithm } from './EnhancedLODGenerator';
import { LODTransitionController, TransitionType } from './LODTransitionController';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 增强的LOD系统配置接口
 */
export interface EnhancedLODSystemOptions extends LODSystemOptions {
  /** 是否启用平滑过渡 */
  enableSmoothTransition?: boolean;
  /** 过渡持续时间（毫秒） */
  transitionDuration?: number;
  /** 过渡类型 */
  transitionType?: TransitionType;
  /** 是否使用增强的LOD生成器 */
  useEnhancedGenerator?: boolean;
  /** 简化算法 */
  simplificationAlgorithm?: SimplificationAlgorithm;
  /** 是否使用GPU加速 */
  useGPU?: boolean;
  /** 是否使用自适应LOD生成 */
  useAdaptiveLOD?: boolean;
  /** 是否使用纹理LOD */
  useTextureLOD?: boolean;
  /** 是否使用LOD缓存 */
  useLODCache?: boolean;
  /** 最大缓存大小（字节） */
  maxCacheSize?: number;
}

/**
 * 增强的LOD系统事件类型
 */
export enum EnhancedLODSystemEventType {
  /** LOD级别变更开始 */
  LEVEL_CHANGE_START = 'level_change_start',
  /** LOD级别变更完成 */
  LEVEL_CHANGE_COMPLETE = 'level_change_complete',
  /** LOD生成开始 */
  GENERATION_START = 'generation_start',
  /** LOD生成完成 */
  GENERATION_COMPLETE = 'generation_complete',
  /** LOD生成错误 */
  GENERATION_ERROR = 'generation_error'
}

/**
 * LOD缓存项接口
 */
interface LODCacheItem {
  /** 原始网格 */
  original: THREE.Mesh;
  /** LOD级别映射 */
  levels: Map<LODLevel, THREE.Mesh>;
  /** 最后访问时间 */
  lastAccessTime: number;
  /** 内存大小（字节） */
  memorySize: number;
}

/**
 * 增强的LOD系统类
 */
export class EnhancedLODSystem extends LODSystem {
  /** 系统类型 */
  public static readonly ENHANCED_TYPE: string = 'EnhancedLODSystem';

  /** 增强的LOD生成器 */
  private enhancedLODGenerator: EnhancedLODGenerator | null = null;

  /** LOD过渡控制器 */
  private transitionController: LODTransitionController | null = null;

  /** 是否启用平滑过渡 */
  private enableSmoothTransition: boolean;

  /** 是否使用增强的LOD生成器 */
  private useEnhancedGenerator: boolean;

  /** 是否使用自适应LOD生成 */
  private useAdaptiveLOD: boolean;

  /** 是否使用纹理LOD */
  private useTextureLOD: boolean;

  /** 是否使用LOD缓存 */
  private useLODCache: boolean;

  /** 最大缓存大小（字节） */
  private maxCacheSize: number;

  /** 当前缓存大小（字节） */
  private currentCacheSize: number = 0;

  /** LOD缓存 */
  private lodCache: Map<string, LODCacheItem> = new Map();

  /** 增强事件发射器 */
  private enhancedEventEmitter: EventEmitter = new EventEmitter();

  /**
   * 创建增强的LOD系统
   * @param options 增强的LOD系统配置
   */
  constructor(options: EnhancedLODSystemOptions = {}) {
    super(options);

    this.enableSmoothTransition = options.enableSmoothTransition !== undefined ? options.enableSmoothTransition : true;
    this.useEnhancedGenerator = options.useEnhancedGenerator !== undefined ? options.useEnhancedGenerator : true;
    this.useAdaptiveLOD = options.useAdaptiveLOD !== undefined ? options.useAdaptiveLOD : true;
    this.useTextureLOD = options.useTextureLOD !== undefined ? options.useTextureLOD : false;
    this.useLODCache = options.useLODCache !== undefined ? options.useLODCache : true;
    this.maxCacheSize = options.maxCacheSize !== undefined ? options.maxCacheSize : 1024 * 1024 * 100; // 100 MB

    // 创建增强的LOD生成器
    if (this.useEnhancedGenerator) {
      this.enhancedLODGenerator = new EnhancedLODGenerator({
        algorithm: options.simplificationAlgorithm || SimplificationAlgorithm.QUADRIC_EDGE_COLLAPSE,
        useGPU: options.useGPU !== undefined ? options.useGPU : true,
        preserveFeatures: true,
        useProgressiveMesh: false,
        adaptiveSimplification: this.useAdaptiveLOD,
        useTextureLOD: this.useTextureLOD,
        highDetailRatio: 0.8,
        mediumDetailRatio: 0.5,
        lowDetailRatio: 0.25,
        veryLowDetailRatio: 0.1,
        preserveUVs: true,
        preserveNormals: true,
        preserveColors: true
      });
    }

    // 创建LOD过渡控制器
    if (this.enableSmoothTransition) {
      this.transitionController = new LODTransitionController({
        duration: options.transitionDuration || 500,
        type: options.transitionType || TransitionType.OPACITY,
        useEasing: true,
        debug: false
      });
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 注册事件监听器
    this.addEventListener(LODSystemEventType.LEVEL_CHANGED, this.handleLevelChanged.bind(this));
  }

  /**
   * 处理LOD级别变更事件
   * @param entity 实体
   * @param component LOD组件
   * @param level 新级别
   */
  private handleLevelChanged(entity: Entity, component: LODComponent, level: LODLevel): void {
    // 如果启用平滑过渡，则开始过渡
    if (this.enableSmoothTransition && this.transitionController) {
      const transform = entity.getComponent('Transform') as any as any as any as Transform;
      if (!transform) {
        return;
      }

      // 获取当前级别和新级别的网格
      const currentLevel = component.getCurrentLevel();
      if (!currentLevel) {
        return;
      }

      const currentLevelConfig = component.getLevel(currentLevel);
      const newLevelConfig = component.getLevel(level);

      if (!currentLevelConfig || !newLevelConfig) {
        return;
      }

      const currentMesh = currentLevelConfig.mesh;
      const newMesh = newLevelConfig.mesh;

      if (!currentMesh || !newMesh) {
        return;
      }

      // 发出级别变更开始事件
      this.enhancedEventEmitter.emit(EnhancedLODSystemEventType.LEVEL_CHANGE_START, entity, component, currentLevel, level);

      // 开始过渡
      this.transitionController.startTransition(
        transform.getObject3D(),
        currentMesh as THREE.Mesh,
        newMesh as THREE.Mesh,
        currentLevel,
        level,
        () => {
          // 发出级别变更完成事件
          this.enhancedEventEmitter.emit(EnhancedLODSystemEventType.LEVEL_CHANGE_COMPLETE, entity, component, level);
        }
      );
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    super.update(deltaTime);

    // 更新过渡控制器
    if (this.enableSmoothTransition && this.transitionController) {
      this.transitionController.update(deltaTime);
    }
  }

  /**
   * 自动生成LOD
   * @param entity 实体
   * @param mesh 网格
   * @param distances 距离配置
   * @returns 是否成功生成
   */
  public autoGenerateLOD(entity: Entity, mesh: THREE.Mesh, distances: number[] = [10, 30, 60, 100]): boolean {
    // 如果使用增强的LOD生成器，则使用它生成LOD
    if (this.useEnhancedGenerator && this.enhancedLODGenerator) {
      return this.autoGenerateEnhancedLOD(entity, mesh, distances);
    }

    // 否则使用基础LOD生成器
    return super.autoGenerateLOD(entity, mesh, distances);
  }

  /**
   * 使用增强的LOD生成器生成LOD
   * @param entity 实体
   * @param mesh 网格
   * @param distances 距离配置
   * @returns 是否成功生成
   */
  private autoGenerateEnhancedLOD(entity: Entity, mesh: THREE.Mesh, distances: number[] = [10, 30, 60, 100]): boolean {
    if (!this.enhancedLODGenerator) {
      return false;
    }

    // 检查缓存
    const cacheKey = this.generateCacheKey(mesh);
    if (this.useLODCache && this.lodCache.has(cacheKey)) {
      return this.createLODFromCache(entity, cacheKey, distances);
    }

    try {
      // 发出生成开始事件
      this.enhancedEventEmitter.emit(EnhancedLODSystemEventType.GENERATION_START, entity, mesh);

      // 生成LOD
      const result = this.useAdaptiveLOD
        ? this.enhancedLODGenerator.generateAdaptive(mesh)
        : this.enhancedLODGenerator.generateEnhanced(mesh);

      // 创建LOD组件
      const lodComponent = new LODComponent();

      // 添加LOD级别
      lodComponent.addLevel({
        level: LODLevel.HIGH,
        distance: distances[0],
        mesh: result.high,
        visible: true
      });

      lodComponent.addLevel({
        level: LODLevel.MEDIUM,
        distance: distances[1],
        mesh: result.medium,
        visible: false
      });

      lodComponent.addLevel({
        level: LODLevel.LOW,
        distance: distances[2],
        mesh: result.low,
        visible: false
      });

      lodComponent.addLevel({
        level: LODLevel.VERY_LOW,
        distance: distances[3],
        mesh: result.veryLow,
        visible: false
      });

      // 添加LOD组件到实体
      entity.addComponent(lodComponent);

      // 注册LOD组件
      this.registerLODComponent(entity, lodComponent);

      // 添加到缓存
      if (this.useLODCache) {
        this.addToCache(cacheKey, result);
      }

      // 发出生成完成事件
      this.enhancedEventEmitter.emit(EnhancedLODSystemEventType.GENERATION_COMPLETE, entity, lodComponent, result);

      return true;
    } catch (error) {
      Debug.error('EnhancedLODSystem', '自动生成LOD失败:', error);

      // 发出生成错误事件
      this.enhancedEventEmitter.emit(EnhancedLODSystemEventType.GENERATION_ERROR, entity, mesh, error);

      return false;
    }
  }

  /**
   * 生成缓存键
   * @param mesh 网格
   * @returns 缓存键
   */
  private generateCacheKey(mesh: THREE.Mesh): string {
    // 使用网格的UUID作为缓存键
    return `lod_${mesh.uuid}`;
  }

  /**
   * 从缓存创建LOD
   * @param entity 实体
   * @param cacheKey 缓存键
   * @param distances 距离配置
   * @returns 是否成功创建
   */
  private createLODFromCache(entity: Entity, cacheKey: string, distances: number[] = [10, 30, 60, 100]): boolean {
    const cacheItem = this.lodCache.get(cacheKey);
    if (!cacheItem) {
      return false;
    }

    try {
      // 更新最后访问时间
      cacheItem.lastAccessTime = Date.now();

      // 创建LOD组件
      const lodComponent = new LODComponent();

      // 添加LOD级别
      if (cacheItem.levels.has(LODLevel.HIGH)) {
        lodComponent.addLevel({
          level: LODLevel.HIGH,
          distance: distances[0],
          mesh: cacheItem.levels.get(LODLevel.HIGH)!.clone(),
          visible: true
        });
      }

      if (cacheItem.levels.has(LODLevel.MEDIUM)) {
        lodComponent.addLevel({
          level: LODLevel.MEDIUM,
          distance: distances[1],
          mesh: cacheItem.levels.get(LODLevel.MEDIUM)!.clone(),
          visible: false
        });
      }

      if (cacheItem.levels.has(LODLevel.LOW)) {
        lodComponent.addLevel({
          level: LODLevel.LOW,
          distance: distances[2],
          mesh: cacheItem.levels.get(LODLevel.LOW)!.clone(),
          visible: false
        });
      }

      if (cacheItem.levels.has(LODLevel.VERY_LOW)) {
        lodComponent.addLevel({
          level: LODLevel.VERY_LOW,
          distance: distances[3],
          mesh: cacheItem.levels.get(LODLevel.VERY_LOW)!.clone(),
          visible: false
        });
      }

      // 添加LOD组件到实体
      entity.addComponent(lodComponent);

      // 注册LOD组件
      this.registerLODComponent(entity, lodComponent);

      return true;
    } catch (error) {
      Debug.error('EnhancedLODSystem', '从缓存创建LOD失败:', error);
      return false;
    }
  }

  /**
   * 添加到缓存
   * @param cacheKey 缓存键
   * @param result LOD生成结果
   */
  private addToCache(cacheKey: string, result: any): void {
    // 计算内存大小
    const memorySize = this.calculateMemorySize(result);

    // 如果缓存已满，则清理一些项目
    if (this.currentCacheSize + memorySize > this.maxCacheSize) {
      this.cleanCache(memorySize);
    }

    // 创建缓存项
    const cacheItem: LODCacheItem = {
      original: result.original,
      levels: new Map(),
      lastAccessTime: Date.now(),
      memorySize
    };

    // 添加级别
    cacheItem.levels.set(LODLevel.HIGH, result.high);
    cacheItem.levels.set(LODLevel.MEDIUM, result.medium);
    cacheItem.levels.set(LODLevel.LOW, result.low);
    cacheItem.levels.set(LODLevel.VERY_LOW, result.veryLow);

    // 添加到缓存
    this.lodCache.set(cacheKey, cacheItem);
    this.currentCacheSize += memorySize;
  }

  /**
   * 清理缓存
   * @param requiredSize 需要的大小（字节）
   */
  private cleanCache(requiredSize: number): void {
    // 如果缓存为空，则直接返回
    if (this.lodCache.size === 0) {
      return;
    }

    // 按最后访问时间排序
    const sortedItems = Array.from(this.lodCache.entries()).sort((a, b) => {
      return a[1].lastAccessTime - b[1].lastAccessTime;
    });

    // 清理缓存，直到有足够的空间
    let freedSize = 0;
    for (const [key, item] of sortedItems) {
      // 从缓存中移除
      this.lodCache.delete(key);
      this.currentCacheSize -= item.memorySize;
      freedSize += item.memorySize;

      // 如果已经释放了足够的空间，则停止
      if (freedSize >= requiredSize) {
        break;
      }
    }
  }

  /**
   * 计算内存大小
   * @param result LOD生成结果
   * @returns 内存大小（字节）
   */
  private calculateMemorySize(result: any): number {
    let size = 0;

    // 计算几何体内存大小
    size += this.calculateGeometrySize(result.original.geometry);
    size += this.calculateGeometrySize(result.high.geometry);
    size += this.calculateGeometrySize(result.medium.geometry);
    size += this.calculateGeometrySize(result.low.geometry);
    size += this.calculateGeometrySize(result.veryLow.geometry);

    return size;
  }

  /**
   * 计算几何体大小
   * @param geometry 几何体
   * @returns 大小（字节）
   */
  private calculateGeometrySize(geometry: THREE.BufferGeometry): number {
    let size = 0;

    // 计算顶点属性大小
    for (const _name in geometry.attributes) {
      const attribute = geometry.attributes[_name];
      if (attribute.array instanceof ArrayBuffer || (attribute.array as any).byteLength !== undefined) {
        size += (attribute.array as any).byteLength;
      } else {
        // 如果没有byteLength属性，估算内存使用
        size += attribute.array.length * 4; // 假设每个元素4字节
      }
    }

    // 计算索引大小
    if (geometry.index) {
      if (geometry.index.array instanceof ArrayBuffer || (geometry.index.array as any).byteLength !== undefined) {
        size += (geometry.index.array as any).byteLength;
      } else {
        // 如果没有byteLength属性，估算内存使用
        size += geometry.index.array.length * 4; // 假设每个元素4字节
      }
    }

    return size;
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.lodCache.clear();
    this.currentCacheSize = 0;
  }

  /**
   * 获取缓存大小
   * @returns 缓存大小（字节）
   */
  public getCacheSize(): number {
    return this.currentCacheSize;
  }

  /**
   * 获取缓存项数量
   * @returns 缓存项数量
   */
  public getCacheItemCount(): number {
    return this.lodCache.size;
  }

  /**
   * 注册事件监听器
   * @param event 事件类型
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    // 同时注册到基类和增强事件发射器
    super.addEventListener(event as any, callback);
    this.enhancedEventEmitter.on(event, callback);
  }

  /**
   * 注销事件监听器
   * @param event 事件类型
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    // 同时从基类和增强事件发射器移除
    super.removeEventListener(event as any, callback);
    this.enhancedEventEmitter.off(event, callback);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    super.dispose();

    // 清除缓存
    this.clearCache();

    // 清除增强事件监听器
    this.enhancedEventEmitter.removeAllListeners();
  }
}
