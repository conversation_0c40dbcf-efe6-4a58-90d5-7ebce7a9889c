/**
 * 优化的水体材质
 * 提供更高性能的水体渲染
 */
import * as THREE from 'three';
import { WaterBodyType } from '../../physics/water/WaterBodyComponent';

/**
 * 优化的水体材质配置接口
 */
export interface OptimizedWaterMaterialConfig {
  /** 颜色 */
  color?: THREE.Color;
  /** 不透明度 */
  opacity?: number;
  /** 反射率 */
  reflectivity?: number;
  /** 折射率 */
  refractionRatio?: number;
  /** 波动强度 */
  waveStrength?: number;
  /** 波动速度 */
  waveSpeed?: number;
  /** 波动缩放 */
  waveScale?: number;
  /** 波动方向 */
  waveDirection?: THREE.Vector2;
  /** 深度 */
  depth?: number;
  /** 深水颜色 */
  depthColor?: THREE.Color;
  /** 浅水颜色 */
  shallowColor?: THREE.Color;
  /** 法线贴图 */
  normalMap?: THREE.Texture;
  /** 反射贴图 */
  reflectionMap?: THREE.Texture;
  /** 折射贴图 */
  refractionMap?: THREE.Texture;
  /** 深度贴图 */
  depthMap?: THREE.Texture;
  /** 因果波纹贴图 */
  causticsMap?: THREE.Texture;
  /** 泡沫贴图 */
  foamMap?: THREE.Texture;
  /** 是否启用反射 */
  enableReflection?: boolean;
  /** 是否启用折射 */
  enableRefraction?: boolean;
  /** 是否启用因果波纹 */
  enableCaustics?: boolean;
  /** 是否启用泡沫 */
  enableFoam?: boolean;
  /** 是否启用水下雾效 */
  enableUnderwaterFog?: boolean;
  /** 是否启用水下扭曲 */
  enableUnderwaterDistortion?: boolean;
  /** 是否透明 */
  transparent?: boolean;
  /** 渲染面 */
  side?: THREE.Side;
  /** 是否写入深度 */
  depthWrite?: boolean;
  /** 性能级别 (0-低, 1-中, 2-高) */
  qualityLevel?: number;
}

/**
 * 优化的水体材质
 */
export class OptimizedWaterMaterial extends THREE.ShaderMaterial {
  /** 时间 */
  private time: number = 0;
  /** 质量级别 */
  private qualityLevel: number = 1;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: OptimizedWaterMaterialConfig = {}) {
    // 设置默认值
    const color = config.color || new THREE.Color(0x0055ff);
    const opacity = config.opacity !== undefined ? config.opacity : 0.8;
    const reflectivity = config.reflectivity !== undefined ? config.reflectivity : 0.5;
    const refractionRatio = config.refractionRatio !== undefined ? config.refractionRatio : 0.98;
    const waveStrength = config.waveStrength !== undefined ? config.waveStrength : 0.1;
    const waveSpeed = config.waveSpeed !== undefined ? config.waveSpeed : 1.0;
    const waveScale = config.waveScale !== undefined ? config.waveScale : 1.0;
    const waveDirection = config.waveDirection || new THREE.Vector2(1, 0);
    const depth = config.depth !== undefined ? config.depth : 5.0;
    const depthColor = config.depthColor || new THREE.Color(0x001e0f);
    const shallowColor = config.shallowColor || new THREE.Color(0x0077ff);
    const normalMap = config.normalMap || null;
    const reflectionMap = config.reflectionMap || null;
    const refractionMap = config.refractionMap || null;
    const depthMap = config.depthMap || null;
    const causticsMap = config.causticsMap || null;
    const foamMap = config.foamMap || null;
    const enableReflection = config.enableReflection !== undefined ? config.enableReflection : true;
    const enableRefraction = config.enableRefraction !== undefined ? config.enableRefraction : true;
    const enableCaustics = config.enableCaustics !== undefined ? config.enableCaustics : true;
    const enableFoam = config.enableFoam !== undefined ? config.enableFoam : true;
    const enableUnderwaterFog = config.enableUnderwaterFog !== undefined ? config.enableUnderwaterFog : true;
    const enableUnderwaterDistortion = config.enableUnderwaterDistortion !== undefined ? config.enableUnderwaterDistortion : true;
    const qualityLevel = config.qualityLevel !== undefined ? config.qualityLevel : 1;

    // 创建着色器
    const shader = OptimizedWaterMaterial.createShader(
      WaterBodyType.LAKE, // 默认使用湖泊类型
      enableReflection,
      enableRefraction,
      enableCaustics,
      enableFoam,
      enableUnderwaterFog,
      enableUnderwaterDistortion,
      qualityLevel
    );

    // 创建材质
    super({
      uniforms: {
        time: { value: 0 },
        waterColor: { value: color },
        opacity: { value: opacity },
        reflectivity: { value: reflectivity },
        refractionRatio: { value: refractionRatio },
        waveStrength: { value: waveStrength },
        waveSpeed: { value: waveSpeed },
        waveScale: { value: waveScale },
        waveDirection: { value: waveDirection },
        depth: { value: depth },
        depthColor: { value: depthColor },
        shallowColor: { value: shallowColor },
        normalMap: { value: normalMap },
        reflectionMap: { value: reflectionMap },
        refractionMap: { value: refractionMap },
        depthMap: { value: depthMap },
        causticsMap: { value: causticsMap },
        foamMap: { value: foamMap },
        cameraPosition: { value: new THREE.Vector3() },
        viewMatrix: { value: new THREE.Matrix4() },
        projectionMatrix: { value: new THREE.Matrix4() },
        modelMatrix: { value: new THREE.Matrix4() },
        normalMatrix: { value: new THREE.Matrix3() },
        heightMap: { value: null },
        useHeightMap: { value: false },
        heightMapScale: { value: 1.0 },
        enableCaustics: { value: enableCaustics },
        enableFoam: { value: enableFoam },
        enableUnderwater: { value: enableUnderwaterFog },
        causticsIntensity: { value: 0.3 },
        foamIntensity: { value: 0.5 },
        underwaterFogDensity: { value: 0.1 },
        underwaterFogColor: { value: new THREE.Color(0x0055aa) },
        qualityLevel: { value: qualityLevel }
      },
      vertexShader: shader.vertexShader,
      fragmentShader: shader.fragmentShader,
      transparent: config.transparent !== undefined ? config.transparent : true,
      side: config.side !== undefined ? config.side : THREE.DoubleSide,
      depthWrite: config.depthWrite !== undefined ? config.depthWrite : false
    });

    // 设置质量级别
    this.qualityLevel = qualityLevel;
  }

  /**
   * 更新材质
   * @param deltaTime 帧间隔时间（秒）
   * @param camera 相机
   */
  public update(deltaTime: number, camera: THREE.Camera): void {
    // 更新时间
    this.time += deltaTime * this.uniforms.waveSpeed.value;
    this.uniforms.time.value = this.time;

    // 更新相机相关参数
    this.uniforms.cameraPosition.value.copy(camera.position);
    this.uniforms.viewMatrix.value.copy(camera.matrixWorldInverse);
    this.uniforms.projectionMatrix.value.copy(camera.projectionMatrix);
  }

  /**
   * 设置反射贴图
   * @param texture 反射贴图
   */
  public setReflectionMap(texture: THREE.Texture): void {
    this.uniforms.reflectionMap.value = texture;
  }

  /**
   * 设置折射贴图
   * @param texture 折射贴图
   */
  public setRefractionMap(texture: THREE.Texture): void {
    this.uniforms.refractionMap.value = texture;
  }

  /**
   * 设置深度贴图
   * @param texture 深度贴图
   */
  public setDepthMap(texture: THREE.Texture): void {
    this.uniforms.depthMap.value = texture;
  }

  /**
   * 设置因果波纹贴图
   * @param texture 因果波纹贴图
   */
  public setCausticsMap(texture: THREE.Texture): void {
    this.uniforms.causticsMap.value = texture;
  }

  /**
   * 设置泡沫贴图
   * @param texture 泡沫贴图
   */
  public setFoamMap(texture: THREE.Texture): void {
    this.uniforms.foamMap.value = texture;
  }

  /**
   * 设置高度图
   * @param texture 高度图
   */
  public setHeightMap(texture: THREE.Texture): void {
    this.uniforms.heightMap.value = texture;
    this.uniforms.useHeightMap.value = texture !== null;
  }

  /**
   * 设置法线贴图
   * @param texture 法线贴图
   */
  public setNormalMap(texture: THREE.Texture): void {
    this.uniforms.normalMap.value = texture;
  }

  /**
   * 设置质量级别
   * @param level 质量级别 (0-低, 1-中, 2-高)
   */
  public setQualityLevel(level: number): void {
    this.qualityLevel = Math.max(0, Math.min(2, level));
    this.uniforms.qualityLevel.value = this.qualityLevel;
  }

  /**
   * 获取质量级别
   * @returns 质量级别
   */
  public getQualityLevel(): number {
    return this.qualityLevel;
  }

  /**
   * 设置因果波纹强度
   * @param intensity 强度
   */
  public setCausticsIntensity(intensity: number): void {
    this.uniforms.causticsIntensity.value = intensity;
  }

  /**
   * 设置泡沫强度
   * @param intensity 强度
   */
  public setFoamIntensity(intensity: number): void {
    this.uniforms.foamIntensity.value = intensity;
  }

  /**
   * 设置水下雾效密度
   * @param density 密度
   */
  public setUnderwaterFogDensity(density: number): void {
    this.uniforms.underwaterFogDensity.value = density;
  }

  /**
   * 设置水下雾效颜色
   * @param color 颜色
   */
  public setUnderwaterFogColor(color: THREE.Color): void {
    this.uniforms.underwaterFogColor.value = color;
  }

  /**
   * 创建着色器
   * @param type 水体类型
   * @param enableReflection 是否启用反射
   * @param enableRefraction 是否启用折射
   * @param enableCaustics 是否启用因果波纹
   * @param enableFoam 是否启用泡沫
   * @param enableUnderwaterFog 是否启用水下雾效
   * @param enableUnderwaterDistortion 是否启用水下扭曲
   * @param qualityLevel 质量级别 (0-低, 1-中, 2-高)
   * @returns 着色器
   */
  private static createShader(
    type: WaterBodyType,
    enableReflection: boolean,
    enableRefraction: boolean,
    enableCaustics: boolean,
    enableFoam: boolean,
    enableUnderwaterFog: boolean,
    enableUnderwaterDistortion: boolean,
    qualityLevel: number
  ): { vertexShader: string, fragmentShader: string } {
    // 根据水体类型和配置生成着色器
    // 这里只是一个简化版本，实际项目中应该有更复杂的着色器

    // 顶点着色器
    const vertexShader = `
      uniform float time;
      uniform float waveStrength;
      uniform float waveScale;
      uniform vec2 waveDirection;
      uniform sampler2D heightMap;
      uniform bool useHeightMap;
      uniform float heightMapScale;
      uniform float qualityLevel;

      varying vec2 vUv;
      varying vec3 vPosition;
      varying vec3 vNormal;
      varying vec3 vViewPosition;
      varying vec3 vWorldNormal;
      varying vec4 vWorldPosition;

      // 优化的波浪计算函数
      float calculateWave(vec2 position, float time, float scale, float strength) {
        // 根据质量级别调整波浪复杂度
        if (qualityLevel < 0.5) {
          // 低质量 - 只使用一个简单的正弦波
          return sin(position.x * scale + time) * cos(position.y * scale + time) * strength;
        } else if (qualityLevel < 1.5) {
          // 中质量 - 使用两个正弦波
          float wave1 = sin(position.x * scale + time) * cos(position.y * scale + time) * strength;
          float wave2 = sin(position.x * scale * 2.0 + time * 0.8) * cos(position.y * scale * 2.0 + time * 0.8) * strength * 0.5;
          return wave1 + wave2;
        } else {
          // 高质量 - 使用三个正弦波
          float wave1 = sin(position.x * scale + time) * cos(position.y * scale + time) * strength;
          float wave2 = sin(position.x * scale * 2.0 + time * 0.8) * cos(position.y * scale * 2.0 + time * 0.8) * strength * 0.5;
          float wave3 = sin(position.x * scale * 3.0 + time * 0.6) * cos(position.y * scale * 3.0 + time * 0.6) * strength * 0.25;
          return wave1 + wave2 + wave3;
        }
      }

      void main() {
        vUv = uv;

        // 计算波动
        vec3 newPosition = position;

        // 如果使用高度图，从高度图获取高度
        if (useHeightMap) {
          float height = texture2D(heightMap, uv).r;
          newPosition.y += height * heightMapScale;
        } else {
          // 否则使用优化的波浪计算
          float waveHeight = calculateWave(uv * waveScale, time, waveScale, waveStrength);
          newPosition.y += waveHeight;
        }

        // 计算法线
        vec3 tangent = vec3(1.0, 0.0, 0.0);
        vec3 bitangent = vec3(0.0, 0.0, 1.0);

        // 计算相邻点的高度差
        float eps = 0.01;
        vec2 uvRight = uv + vec2(eps, 0.0);
        vec2 uvUp = uv + vec2(0.0, eps);

        float heightCenter = newPosition.y;
        float heightRight, heightUp;

        // 根据使用的高度计算方法获取相邻点的高度
        if (useHeightMap) {
          heightRight = texture2D(heightMap, uvRight).r * heightMapScale + position.y;
          heightUp = texture2D(heightMap, uvUp).r * heightMapScale + position.y;
        } else {
          heightRight = calculateWave(uvRight * waveScale, time, waveScale, waveStrength) + position.y;
          heightUp = calculateWave(uvUp * waveScale, time, waveScale, waveStrength) + position.y;
        }

        // 计算切线和副切线
        tangent.y = (heightRight - heightCenter) / eps;
        bitangent.y = (heightUp - heightCenter) / eps;

        // 计算法线
        vec3 computedNormal = normalize(cross(tangent, bitangent));
        vNormal = normalize(normalMatrix * computedNormal);
        vWorldNormal = normalize(mat3(modelMatrix) * computedNormal);

        // 计算世界坐标
        vec4 worldPosition = modelMatrix * vec4(newPosition, 1.0);
        vPosition = worldPosition.xyz;
        vWorldPosition = worldPosition;

        // 计算视图坐标
        vec4 viewPosition = viewMatrix * worldPosition;
        vViewPosition = viewPosition.xyz;

        // 计算裁剪坐标
        gl_Position = projectionMatrix * viewPosition;
      }
    `;

    // 片元着色器
    const fragmentShader = `
      uniform vec3 waterColor;
      uniform float opacity;
      uniform float reflectivity;
      uniform float refractionRatio;
      uniform float depth;
      uniform vec3 depthColor;
      uniform vec3 shallowColor;
      uniform sampler2D normalMap;
      uniform sampler2D reflectionMap;
      uniform sampler2D refractionMap;
      uniform sampler2D depthMap;
      uniform sampler2D causticsMap;
      uniform sampler2D foamMap;
      uniform vec3 cameraPosition;
      uniform float time;
      uniform bool enableCaustics;
      uniform bool enableFoam;
      uniform bool enableUnderwater;
      uniform float causticsIntensity;
      uniform float foamIntensity;
      uniform float underwaterFogDensity;
      uniform vec3 underwaterFogColor;
      uniform float qualityLevel;

      varying vec2 vUv;
      varying vec3 vPosition;
      varying vec3 vNormal;
      varying vec3 vViewPosition;
      varying vec3 vWorldNormal;
      varying vec4 vWorldPosition;

      // 扰动UV坐标
      vec2 distort(vec2 uv, float factor) {
        return uv + factor * vec2(
          sin(uv.y * 10.0 + time),
          cos(uv.x * 10.0 + time)
        );
      }

      // 计算水下雾效
      vec3 calculateUnderwaterFog(vec3 color, float distance) {
        float fogFactor = 1.0 - exp(-distance * underwaterFogDensity);
        return mix(color, underwaterFogColor, fogFactor);
      }

      // 优化的因果波纹计算
      vec3 calculateCaustics(vec2 uv) {
        // 根据质量级别调整计算复杂度
        if (qualityLevel < 0.5) {
          // 低质量 - 简单采样
          vec2 distortedUv = distort(uv, 0.02);
          vec3 caustics = texture2D(causticsMap, distortedUv + time * 0.01).rgb;
          return caustics * causticsIntensity;
        } else {
          // 中高质量 - 多重采样
          vec2 distortedUv1 = distort(uv, 0.02);
          vec2 distortedUv2 = distort(uv, 0.03);

          vec3 caustics1 = texture2D(causticsMap, distortedUv1 + time * 0.01).rgb;
          vec3 caustics2 = texture2D(causticsMap, distortedUv2 - time * 0.01).rgb;

          return max(caustics1, caustics2) * causticsIntensity;
        }
      }

      // 优化的泡沫计算
      float calculateFoam(vec2 uv) {
        // 根据质量级别调整计算复杂度
        if (qualityLevel < 0.5) {
          // 低质量 - 简单边缘泡沫
          return (1.0 - smoothstep(0.0, 0.1, vWorldPosition.y - 0.0)) * foamIntensity;
        } else {
          // 中高质量 - 贴图采样和边缘泡沫
          vec2 distortedUv = distort(uv, 0.05);
          float foam = texture2D(foamMap, distortedUv).r;

          // 添加时间变化
          foam *= 0.5 + 0.5 * sin(time * 0.5);

          // 边缘泡沫
          float edgeFoam = 1.0 - smoothstep(0.0, 0.1, vWorldPosition.y - 0.0);

          return max(foam, edgeFoam) * foamIntensity;
        }
      }

      void main() {
        // 基础颜色
        vec3 baseColor = waterColor;

        // 视线方向
        vec3 viewDirection = normalize(cameraPosition - vPosition);

        // 使用世界法线计算反射和折射
        vec3 worldNormal = vWorldNormal;

        // 反射向量
        vec3 reflectionVector = reflect(-viewDirection, worldNormal);

        // 折射向量
        vec3 refractionVector = refract(-viewDirection, worldNormal, refractionRatio);

        // 菲涅尔系数
        float fresnel = pow(1.0 - max(0.0, dot(viewDirection, worldNormal)), 5.0);

        // 最终颜色
        vec3 finalColor = baseColor;

        // 添加反射
        #ifdef ENABLE_REFLECTION
        vec2 reflectionUv = gl_FragCoord.xy / vec2(1024.0, 1024.0); // 这里应该使用实际的屏幕尺寸
        vec3 reflectionColor = texture2D(reflectionMap, reflectionUv).rgb;
        finalColor = mix(finalColor, reflectionColor, reflectivity * fresnel);
        #endif

        // 添加折射
        #ifdef ENABLE_REFRACTION
        vec2 refractionUv = gl_FragCoord.xy / vec2(1024.0, 1024.0); // 这里应该使用实际的屏幕尺寸
        vec3 refractionColor = texture2D(refractionMap, refractionUv).rgb;
        finalColor = mix(finalColor, refractionColor, 1.0 - fresnel);
        #endif

        // 添加深度颜色
        float depthFactor = clamp(depth / 10.0, 0.0, 1.0);
        finalColor = mix(shallowColor, depthColor, depthFactor) * finalColor;

        // 添加因果波纹
        #ifdef ENABLE_CAUSTICS
        if (enableCaustics) {
          vec3 caustics = calculateCaustics(vUv);
          finalColor += caustics * (1.0 - depthFactor); // 浅水区域更明显
        }
        #endif

        // 添加泡沫
        #ifdef ENABLE_FOAM
        if (enableFoam) {
          float foam = calculateFoam(vUv);
          finalColor = mix(finalColor, vec3(1.0), foam);
        }
        #endif

        // 添加水下雾效
        #ifdef ENABLE_UNDERWATER_FOG
        if (enableUnderwater && cameraPosition.y < vWorldPosition.y) {
          float viewDistance = length(vViewPosition);
          finalColor = calculateUnderwaterFog(finalColor, viewDistance);
        }
        #endif

        // 输出颜色
        gl_FragColor = vec4(finalColor, opacity);
      }
    `;

    // 根据配置添加预处理指令
    let processedFragmentShader = fragmentShader;
    if (enableReflection) {
      processedFragmentShader = '#define ENABLE_REFLECTION\n' + processedFragmentShader;
    }
    if (enableRefraction) {
      processedFragmentShader = '#define ENABLE_REFRACTION\n' + processedFragmentShader;
    }
    if (enableCaustics) {
      processedFragmentShader = '#define ENABLE_CAUSTICS\n' + processedFragmentShader;
    }
    if (enableFoam) {
      processedFragmentShader = '#define ENABLE_FOAM\n' + processedFragmentShader;
    }
    if (enableUnderwaterFog) {
      processedFragmentShader = '#define ENABLE_UNDERWATER_FOG\n' + processedFragmentShader;
    }
    if (enableUnderwaterDistortion) {
      processedFragmentShader = '#define ENABLE_UNDERWATER_DISTORTION\n' + processedFragmentShader;
    }

    return {
      vertexShader,
      fragmentShader: processedFragmentShader
    };
  }
}
