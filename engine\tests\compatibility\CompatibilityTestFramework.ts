/**
 * 兼容性测试框架
 * 用于比较原有项目和重构后项目的功能
 */
import { Debug } from '../../src/utils/Debug';

/**
 * 测试结果类型
 */
export type TestResult = {
  name: string;
  passed: boolean;
  errorMessage?: string;
  details?: any;
};

/**
 * 测试报告类型
 */
export type TestReport = {
  name: string;
  description: string;
  startTime: number;
  endTime: number;
  duration: number;
  results: TestResult[];
  passedCount: number;
  failedCount: number;
  passRate: number;
};

/**
 * 测试配置类型
 */
export type TestConfig = {
  name: string;
  description: string;
  originalModulePath: string;
  refactoredModulePath: string;
  testCases: TestCase[];
};

/**
 * 测试用例类型
 */
export type TestCase = {
  name: string;
  description: string;
  testFunction: (original: any, refactored: any) => Promise<TestResult>;
};

/**
 * 兼容性测试框架类
 */
export class CompatibilityTestFramework {
  private config: TestConfig;
  private originalModule: any;
  private refactoredModule: any;
  private results: TestResult[] = [];
  private startTime: number = 0;
  private endTime: number = 0;

  /**
   * 构造函数
   * @param config 测试配置
   */
  constructor(config: TestConfig) {
    this.config = config;
  }

  /**
   * 加载模块
   */
  private async loadModules(): Promise<void> {
    try {
      // 加载原有项目模块
      this.originalModule = await import(this.config.originalModulePath);
      Debug.log('兼容性测试', `已加载原有项目模块: ${this.config.originalModulePath}`);

      // 加载重构后项目模块
      this.refactoredModule = await import(this.config.refactoredModulePath);
      Debug.log('兼容性测试', `已加载重构后项目模块: ${this.config.refactoredModulePath}`);
    } catch (error) {
      Debug.error('兼容性测试', `加载模块失败: ${error}`);
      throw error;
    }
  }

  /**
   * 运行测试
   * @returns 测试报告
   */
  public async run(): Promise<TestReport> {
    this.startTime = performance.now();
    this.results = [];

    Debug.log('兼容性测试', `开始运行测试套件: ${this.config.name}`);
    Debug.log('兼容性测试', `共 ${this.config.testCases.length} 个测试用例`);

    try {
      // 加载模块
      await this.loadModules();

      // 运行测试用例
      for (const testCase of this.config.testCases) {
        Debug.log('兼容性测试', `运行测试用例: ${testCase.name}`);
        
        try {
          const result = await testCase.testFunction(this.originalModule, this.refactoredModule);
          this.results.push(result);
          
          if (result.passed) {
            Debug.log('兼容性测试', `测试用例通过: ${testCase.name}`);
          } else {
            Debug.warn('兼容性测试', `测试用例失败: ${testCase.name}, 错误: ${result.errorMessage}`);
          }
        } catch (error) {
          Debug.error('兼容性测试', `测试用例执行失败: ${testCase.name}, 错误: ${error}`);
          
          this.results.push({
            name: testCase.name,
            passed: false,
            errorMessage: `执行失败: ${error}`
          });
        }
      }
    } catch (error) {
      Debug.error('兼容性测试', `测试套件执行失败: ${error}`);
    }

    this.endTime = performance.now();
    
    // 生成测试报告
    const passedCount = this.results.filter(r => r.passed).length;
    const failedCount = this.results.length - passedCount;
    const passRate = this.results.length > 0 ? passedCount / this.results.length : 0;
    
    const report: TestReport = {
      name: this.config.name,
      description: this.config.description,
      startTime: this.startTime,
      endTime: this.endTime,
      duration: this.endTime - this.startTime,
      results: this.results,
      passedCount,
      failedCount,
      passRate
    };
    
    Debug.log('兼容性测试', `测试套件完成: ${this.config.name}`);
    Debug.log('兼容性测试', `通过: ${passedCount}, 失败: ${failedCount}, 通过率: ${(passRate * 100).toFixed(2)}%`);
    
    return report;
  }

  /**
   * 生成HTML报告
   * @param report 测试报告
   * @returns HTML报告
   */
  public static generateHTMLReport(report: TestReport): string {
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>兼容性测试报告 - ${report.name}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1, h2 { color: #333; }
          .summary { margin: 20px 0; padding: 10px; background-color: #f5f5f5; border-radius: 5px; }
          .pass-rate { font-size: 24px; font-weight: bold; }
          .pass { color: green; }
          .fail { color: red; }
          table { border-collapse: collapse; width: 100%; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          tr.passed { background-color: #dff0d8; }
          tr.failed { background-color: #f2dede; }
          .details { margin-top: 5px; font-size: 14px; color: #666; }
        </style>
      </head>
      <body>
        <h1>兼容性测试报告</h1>
        <div class="summary">
          <h2>${report.name}</h2>
          <p>${report.description}</p>
          <p>开始时间: ${new Date(report.startTime).toLocaleString()}</p>
          <p>结束时间: ${new Date(report.endTime).toLocaleString()}</p>
          <p>耗时: ${(report.duration / 1000).toFixed(2)}秒</p>
          <p class="pass-rate">通过率: <span class="${report.passRate >= 1 ? 'pass' : 'fail'}">${(report.passRate * 100).toFixed(2)}%</span></p>
          <p>通过: <span class="pass">${report.passedCount}</span>, 失败: <span class="fail">${report.failedCount}</span>, 总计: ${report.results.length}</p>
        </div>
        
        <h2>测试结果详情</h2>
        <table>
          <tr>
            <th>测试名称</th>
            <th>结果</th>
            <th>详情</th>
          </tr>
          ${report.results.map(result => `
            <tr class="${result.passed ? 'passed' : 'failed'}">
              <td>${result.name}</td>
              <td>${result.passed ? '通过' : '失败'}</td>
              <td>${result.errorMessage || ''}</td>
            </tr>
          `).join('')}
        </table>
      </body>
      </html>
    `;
    
    return html;
  }
}
