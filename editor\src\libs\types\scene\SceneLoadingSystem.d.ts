/**
 * 场景加载系统
 * 支持智能预加载和资源管理
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Camera } from '../rendering/Camera';
import { Scene } from './Scene';
/**
 * 资源类型枚举
 */
export declare enum ResourceType {
    /** 纹理 */
    TEXTURE = "texture",
    /** 模型 */
    MODEL = "model",
    /** 音频 */
    AUDIO = "audio",
    /** 视频 */
    VIDEO = "video",
    /** 材质 */
    MATERIAL = "material",
    /** 着色器 */
    SHADER = "shader",
    /** 字体 */
    FONT = "font",
    /** 场景 */
    SCENE = "scene",
    /** 其他 */
    OTHER = "other"
}
/**
 * 资源优先级枚举
 */
export declare enum ResourcePriority {
    /** 非常高 */
    VERY_HIGH = 0,
    /** 高 */
    HIGH = 1,
    /** 中 */
    MEDIUM = 2,
    /** 低 */
    LOW = 3,
    /** 非常低 */
    VERY_LOW = 4
}
/**
 * 资源状态枚举
 */
export declare enum ResourceState {
    /** 未加载 */
    UNLOADED = "unloaded",
    /** 加载中 */
    LOADING = "loading",
    /** 已加载 */
    LOADED = "loaded",
    /** 错误 */
    ERROR = "error"
}
/**
 * 资源接口
 */
export interface Resource {
    /** 资源ID */
    id: string;
    /** 资源URL */
    url: string;
    /** 资源类型 */
    type: ResourceType;
    /** 资源优先级 */
    priority: ResourcePriority;
    /** 资源状态 */
    state: ResourceState;
    /** 资源数据 */
    data: any;
    /** 资源大小（字节） */
    size: number;
    /** 加载进度（0-1） */
    progress: number;
    /** 加载开始时间 */
    startTime: number;
    /** 加载结束时间 */
    endTime: number;
    /** 最后访问时间 */
    lastAccessTime: number;
    /** 引用计数 */
    referenceCount: number;
    /** 是否持久化 */
    persistent: boolean;
    /** 是否预加载 */
    preload: boolean;
    /** 依赖资源列表 */
    dependencies: string[];
    /** 用户数据 */
    userData: any;
}
/**
 * 场景区域接口
 */
export interface SceneRegion {
    /** 区域ID */
    id: string;
    /** 区域名称 */
    name: string;
    /** 区域包围盒 */
    boundingBox: THREE.Box3;
    /** 区域资源列表 */
    resources: string[];
    /** 区域优先级 */
    priority: ResourcePriority;
    /** 是否可见 */
    visible: boolean;
    /** 是否已加载 */
    loaded: boolean;
    /** 用户数据 */
    userData: any;
}
/**
 * 场景加载系统配置接口
 */
export interface SceneLoadingSystemOptions {
    /** 最大并发加载数 */
    maxConcurrentLoads?: number;
    /** 最大内存使用量（字节） */
    maxMemoryUsage?: number;
    /** 预加载距离 */
    preloadDistance?: number;
    /** 卸载距离 */
    unloadDistance?: number;
    /** 是否使用预测加载 */
    usePredictiveLoading?: boolean;
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 是否使用压缩 */
    useCompression?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 加载超时时间（毫秒） */
    loadTimeout?: number;
    /** 重试次数 */
    retryCount?: number;
    /** 重试延迟（毫秒） */
    retryDelay?: number;
}
/**
 * 加载任务接口
 */
export interface LoadTask {
    /** 任务ID */
    id: string;
    /** 资源ID */
    resourceId: string;
    /** 任务优先级 */
    priority: ResourcePriority;
    /** 任务状态 */
    state: ResourceState;
    /** 加载器 */
    loader: THREE.Loader;
    /** 加载进度 */
    progress: number;
    /** 开始时间 */
    startTime: number;
    /** 结束时间 */
    endTime: number;
    /** 重试次数 */
    retryCount: number;
    /** 错误信息 */
    error: Error | null;
    /** 取消函数 */
    cancel: () => void;
    /** 完成回调 */
    onComplete: (resource: Resource) => void;
    /** 进度回调 */
    onProgress: (progress: number) => void;
    /** 错误回调 */
    onError: (error: Error) => void;
}
/**
 * 场景加载系统类
 */
export declare class SceneLoadingSystem extends System {
    /** 系统类型 */
    private static readonly TYPE;
    /** 最大并发加载数 */
    private maxConcurrentLoads;
    /** 最大内存使用量（字节） */
    protected maxMemoryUsage: number;
    /** 预加载距离 */
    protected preloadDistance: number;
    /** 卸载距离 */
    protected unloadDistance: number;
    /** 是否使用预测加载 */
    protected usePredictiveLoading: boolean;
    /** 是否使用缓存 */
    protected useCache: boolean;
    /** 是否使用压缩 */
    private useCompression;
    /** 是否使用调试可视化 */
    protected useDebugVisualization: boolean;
    /** 加载超时时间（毫秒） */
    private loadTimeout;
    /** 重试次数 */
    private retryCount;
    /** 重试延迟（毫秒） */
    private retryDelay;
    /** 资源列表 */
    private resources;
    /** 场景区域列表 */
    private regions;
    /** 加载任务队列 */
    private loadQueue;
    /** 活动加载任务列表 */
    private activeLoadTasks;
    /** 当前内存使用量（字节） */
    protected currentMemoryUsage: number;
    /** 加载器映射 */
    private loaders;
    /** 缓存 */
    private cache;
    /** 调试可视化材质 */
    private debugMaterial;
    /** 调试可视化网格 */
    protected debugMeshes: THREE.Mesh[];
    /** 是否已初始化 */
    private initialized;
    /** 任务计数器 */
    private taskCounter;
    /** 资源计数器 */
    private resourceCounter;
    /** 区域计数器 */
    private regionCounter;
    /** 上一次相机位置 */
    private lastCameraPosition;
    /** 相机移动方向 */
    private cameraDirection;
    /** 相机移动速度 */
    private cameraSpeed;
    /** 相机位置历史 */
    private cameraPositionHistory;
    /** 相机位置历史最大长度 */
    private cameraPositionHistoryMaxLength;
    /**
     * 创建场景加载系统
     * @param options 场景加载系统配置
     */
    constructor(options?: SceneLoadingSystemOptions);
    /**
     * 初始化加载器
     */
    private initializeLoaders;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    protected getCamera(): Camera | null;
    /**
     * 获取场景
     * @returns 场景
     */
    protected getScene(): Scene | null;
    /**
     * 更新相机信息
     * @param camera 相机
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateCameraInfo;
    /**
     * 更新场景区域
     * @param camera 相机
     */
    private updateRegions;
    /**
     * 预测相机移动并预加载区域
     * @param camera 相机
     */
    private predictAndPreload;
    /**
     * 加载区域
     * @param region 区域
     */
    private loadRegion;
    /**
     * 预加载区域
     * @param region 区域
     */
    private preloadRegion;
    /**
     * 卸载区域
     * @param region 区域
     */
    private unloadRegion;
    /**
     * 加载资源
     * @param resourceId 资源ID
     * @param priority 优先级
     * @param callbacks 可选的回调函数
     */
    protected loadResource(resourceId: string, priority: ResourcePriority, callbacks?: {
        onComplete?: () => void;
        onProgress?: (progress: number) => void;
        onError?: (error: Error) => void;
    }): void;
    /**
     * 卸载资源
     * @param resourceId 资源ID
     */
    protected unloadResource(resourceId: string): void;
    /**
     * 更新加载队列
     */
    protected updateLoadQueue(): void;
    /**
     * 启动加载任务
     * @param task 加载任务
     */
    private startLoadTask;
    /**
     * 处理加载任务
     */
    protected processLoadTasks(): void;
    /**
     * 管理资源
     * @param camera 相机
     */
    private manageResources;
    /**
     * 释放资源
     */
    private releaseResources;
    /**
     * 格式化字节数
     * @param bytes 字节数
     * @returns 格式化后的字符串
     */
    private formatBytes;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 添加资源
     * @param resource 资源
     * @returns 资源ID
     */
    addResource(resource: Partial<Resource>): string;
    /**
     * 添加场景区域
     * @param region 场景区域
     * @returns 区域ID
     */
    addRegion(region: Partial<SceneRegion>): string;
    /**
     * 获取资源
     * @param resourceId 资源ID
     * @returns 资源
     */
    getResource(resourceId: string): Resource | null;
    /**
     * 获取区域
     * @param regionId 区域ID
     * @returns 区域
     */
    getRegion(regionId: string): SceneRegion | null;
    /**
     * 获取加载状态
     * @returns 加载状态
     */
    getLoadingStatus(): {
        totalResources: number;
        loadedResources: number;
        loadingResources: number;
        errorResources: number;
        totalRegions: number;
        loadedRegions: number;
        memoryUsage: number;
        maxMemoryUsage: number;
        activeLoadTasks: number;
        queuedLoadTasks: number;
    };
    /**
     * 销毁系统
     */
    destroy(): void;
}
