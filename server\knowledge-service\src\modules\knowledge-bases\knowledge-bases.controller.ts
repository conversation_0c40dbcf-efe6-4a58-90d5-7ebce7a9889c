import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { KnowledgeBasesService } from './knowledge-bases.service';
import { CreateKnowledgeBaseDto } from './dto/create-knowledge-base.dto';
import { UpdateKnowledgeBaseDto } from './dto/update-knowledge-base.dto';
import { QueryKnowledgeBaseDto } from './dto/query-knowledge-base.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('knowledge-bases')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('knowledge-bases')
export class KnowledgeBasesController {
  constructor(private readonly knowledgeBasesService: KnowledgeBasesService) {}

  @Post()
  @ApiOperation({ summary: '创建知识库' })
  @ApiResponse({ status: 201, description: '知识库创建成功' })
  async create(
    @Body() createKnowledgeBaseDto: CreateKnowledgeBaseDto,
    @Request() req: any,
  ) {
    return this.knowledgeBasesService.create(createKnowledgeBaseDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: '获取知识库列表' })
  @ApiResponse({ status: 200, description: '获取知识库列表成功' })
  async findAll(@Query() query: QueryKnowledgeBaseDto, @Request() req: any) {
    return this.knowledgeBasesService.findAll(query, req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取知识库详情' })
  @ApiResponse({ status: 200, description: '获取知识库详情成功' })
  async findOne(@Param('id') id: string, @Request() req: any) {
    return this.knowledgeBasesService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新知识库' })
  @ApiResponse({ status: 200, description: '知识库更新成功' })
  async update(
    @Param('id') id: string,
    @Body() updateKnowledgeBaseDto: UpdateKnowledgeBaseDto,
    @Request() req: any,
  ) {
    return this.knowledgeBasesService.update(id, updateKnowledgeBaseDto, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除知识库' })
  @ApiResponse({ status: 200, description: '知识库删除成功' })
  async remove(@Param('id') id: string, @Request() req: any) {
    return this.knowledgeBasesService.remove(id, req.user.id);
  }

  @Get(':id/statistics')
  @ApiOperation({ summary: '获取知识库统计信息' })
  @ApiResponse({ status: 200, description: '获取统计信息成功' })
  async getStatistics(@Param('id') id: string, @Request() req: any) {
    return this.knowledgeBasesService.getStatistics(id, req.user.id);
  }
}
