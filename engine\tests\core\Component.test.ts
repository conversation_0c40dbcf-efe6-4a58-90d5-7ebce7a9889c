/**
 * Component类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Component } from '../../src/core/Component';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';

// 创建一个测试组件类
class TestComponent extends Component {
  public initialized: boolean = false;
  public enabled: boolean = true;
  public disposed: boolean = false;
  public value: number = 0;
  
  constructor(entity: Entity, value: number = 0) {
    super(entity);
    this.value = value;
  }
  
  public static readonly TYPE: string = 'TestComponent';
  
  public getType(): string {
    return TestComponent.TYPE;
  }
  
  public initialize(): void {
    this.initialized = true;
  }
  
  public dispose(): void {
    this.disposed = true;
    super.dispose();
  }
  
  public isEnabled(): boolean {
    return this.enabled;
  }
  
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
  
  public setValue(value: number): void {
    this.value = value;
    this.emit('valueChanged', { value });
  }
  
  public getValue(): number {
    return this.value;
  }
  
  public serialize(): any {
    return {
      ...super.serialize(),
      value: this.value
    };
  }
  
  public deserialize(data: any): void {
    super.deserialize(data);
    if (data.value !== undefined) {
      this.value = data.value;
    }
  }
}

// 创建一个依赖组件类
class DependentComponent extends Component {
  public initialized: boolean = false;
  
  constructor(entity: Entity) {
    super(entity);
  }
  
  public static readonly TYPE: string = 'DependentComponent';
  
  public getType(): string {
    return DependentComponent.TYPE;
  }
  
  public initialize(): void {
    this.initialized = true;
  }
  
  public getDependencies(): string[] {
    return [TestComponent.TYPE];
  }
}

describe('Component', () => {
  let engine: Engine;
  let world: World;
  let entity: Entity;
  let component: TestComponent;
  
  // 在每个测试前创建一个新的组件实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建实体
    entity = new Entity(world);
    
    // 创建组件
    component = new TestComponent(entity, 42);
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试组件初始化
  it('应该正确初始化组件', () => {
    expect(component).toBeDefined();
    expect(component.getEntity()).toBe(entity);
    expect(component.getType()).toBe(TestComponent.TYPE);
    expect(component.getValue()).toBe(42);
    expect(component.isEnabled()).toBe(true);
    expect(component.initialized).toBe(false); // 尚未调用initialize方法
    
    // 初始化组件
    component.initialize();
    
    // 验证组件已初始化
    expect(component.initialized).toBe(true);
  });
  
  // 测试组件启用/禁用
  it('应该能够启用和禁用组件', () => {
    // 验证组件已启用
    expect(component.isEnabled()).toBe(true);
    
    // 禁用组件
    component.setEnabled(false);
    
    // 验证组件已禁用
    expect(component.isEnabled()).toBe(false);
    
    // 启用组件
    component.setEnabled(true);
    
    // 验证组件已启用
    expect(component.isEnabled()).toBe(true);
  });
  
  // 测试组件事件
  it('应该能够发射和监听组件事件', () => {
    // 创建事件监听器
    const listener = vi.fn();
    
    // 添加事件监听器
    component.on('valueChanged', listener);
    
    // 更改组件值
    component.setValue(100);
    
    // 验证事件监听器被调用
    expect(listener).toHaveBeenCalledWith({ value: 100 });
    
    // 移除事件监听器
    component.off('valueChanged', listener);
    
    // 再次更改组件值
    component.setValue(200);
    
    // 验证事件监听器未被再次调用
    expect(listener).toHaveBeenCalledTimes(1);
  });
  
  // 测试组件序列化
  it('应该能够序列化和反序列化组件', () => {
    // 序列化组件
    const data = component.serialize();
    
    // 验证序列化数据
    expect(data).toBeDefined();
    expect(data.type).toBe(TestComponent.TYPE);
    expect(data.enabled).toBe(true);
    expect(data.value).toBe(42);
    
    // 创建新组件
    const newComponent = new TestComponent(entity);
    
    // 反序列化数据到新组件
    newComponent.deserialize(data);
    
    // 验证反序列化结果
    expect(newComponent.getType()).toBe(TestComponent.TYPE);
    expect(newComponent.isEnabled()).toBe(true);
    expect(newComponent.getValue()).toBe(42);
  });
  
  // 测试组件依赖
  it('应该能够处理组件依赖', () => {
    // 创建依赖组件
    const dependentComponent = new DependentComponent(entity);
    
    // 验证依赖
    const dependencies = dependentComponent.getDependencies();
    expect(dependencies).toContain(TestComponent.TYPE);
    
    // 尝试添加依赖组件（应该失败，因为依赖的组件尚未添加）
    let error: Error | null = null;
    try {
      entity.addComponent(dependentComponent);
    } catch (e) {
      error = e as Error;
    }
    
    // 验证添加失败
    expect(error).not.toBeNull();
    expect(error?.message).toContain('依赖组件未满足');
    
    // 添加被依赖的组件
    entity.addComponent(component);
    
    // 再次尝试添加依赖组件（应该成功）
    error = null;
    try {
      entity.addComponent(dependentComponent);
    } catch (e) {
      error = e as Error;
    }
    
    // 验证添加成功
    expect(error).toBeNull();
    expect(entity.hasComponent(DependentComponent.TYPE)).toBe(true);
  });
  
  // 测试组件销毁
  it('应该能够正确销毁组件', () => {
    // 添加组件到实体
    entity.addComponent(component);
    
    // 创建事件监听器
    const listener = vi.fn();
    
    // 添加事件监听器
    component.on('disposed', listener);
    
    // 销毁组件
    component.dispose();
    
    // 验证组件已销毁
    expect(component.disposed).toBe(true);
    
    // 验证事件监听器被调用
    expect(listener).toHaveBeenCalled();
    
    // 验证组件已从实体中移除
    expect(entity.hasComponent(TestComponent.TYPE)).toBe(false);
  });
  
  // 测试组件克隆
  it('应该能够克隆组件', () => {
    // 设置组件属性
    component.setValue(100);
    component.setEnabled(false);
    
    // 克隆组件
    const clonedComponent = component.clone(entity) as TestComponent;
    
    // 验证克隆的组件属性
    expect(clonedComponent.getType()).toBe(component.getType());
    expect(clonedComponent.isEnabled()).toBe(component.isEnabled());
    expect(clonedComponent.getValue()).toBe(component.getValue());
    
    // 验证克隆的组件与原始组件是不同的实例
    expect(clonedComponent).not.toBe(component);
  });
  
  // 测试组件更新
  it('应该能够更新组件', () => {
    // 创建更新方法的模拟
    const updateMock = vi.fn();
    component.update = updateMock;
    
    // 更新组件
    const deltaTime = 0.016;
    component.update(deltaTime);
    
    // 验证更新方法被调用
    expect(updateMock).toHaveBeenCalledWith(deltaTime);
  });
  
  // 测试组件固定更新
  it('应该能够固定更新组件', () => {
    // 创建固定更新方法的模拟
    const fixedUpdateMock = vi.fn();
    component.fixedUpdate = fixedUpdateMock;
    
    // 固定更新组件
    const fixedDeltaTime = 0.02;
    component.fixedUpdate(fixedDeltaTime);
    
    // 验证固定更新方法被调用
    expect(fixedUpdateMock).toHaveBeenCalledWith(fixedDeltaTime);
  });
  
  // 测试组件后更新
  it('应该能够后更新组件', () => {
    // 创建后更新方法的模拟
    const lateUpdateMock = vi.fn();
    component.lateUpdate = lateUpdateMock;
    
    // 后更新组件
    const deltaTime = 0.016;
    component.lateUpdate(deltaTime);
    
    // 验证后更新方法被调用
    expect(lateUpdateMock).toHaveBeenCalledWith(deltaTime);
  });
});
