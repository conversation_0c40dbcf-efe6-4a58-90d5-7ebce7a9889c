/**
 * 车轮约束
 * 模拟车轮的悬挂和转向
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { PhysicsConstraint } from './PhysicsConstraint';
/**
 * 车轮约束选项
 */
export interface WheelConstraintOptions {
    /** 悬挂轴向 */
    axisA?: THREE.Vector3;
    /** 转向轴向 */
    axisB?: THREE.Vector3;
    /** 悬挂刚度 */
    suspensionStiffness?: number;
    /** 悬挂阻尼 */
    suspensionDamping?: number;
    /** 悬挂长度 */
    suspensionLength?: number;
    /** 悬挂最大长度 */
    suspensionMaxLength?: number;
    /** 悬挂最小长度 */
    suspensionMinLength?: number;
    /** 转向角度 */
    steeringAngle?: number;
    /** 是否允许连接的物体之间碰撞 */
    collideConnected?: boolean;
    /** 最大力 */
    maxForce?: number;
}
/**
 * 车轮约束
 */
export declare class WheelConstraint extends PhysicsConstraint {
    /** 组件类型 */
    static readonly type: string;
    /** 悬挂轴向 */
    private axisA;
    /** 转向轴向 */
    private axisB;
    /** 悬挂刚度 */
    private suspensionStiffness;
    /** 悬挂阻尼 */
    private suspensionDamping;
    /** 悬挂长度 */
    private suspensionLength;
    /** 悬挂最大长度 */
    private suspensionMaxLength;
    /** 悬挂最小长度 */
    private suspensionMinLength;
    /** 转向角度 */
    private steeringAngle;
    /** 最大力 */
    private maxForce;
    /** 悬挂约束 */
    private suspensionConstraint;
    /** 转向约束 */
    private steeringConstraint;
    /**
     * 创建车轮约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    constructor(targetEntity?: Entity | null, options?: WheelConstraintOptions);
    /**
     * 创建约束
     */
    protected createConstraint(): void;
    /**
     * 更新约束
     */
    update(): void;
    /**
     * 设置悬挂轴向
     * @param axis 轴向
     */
    setAxisA(axis: THREE.Vector3): void;
    /**
     * 获取悬挂轴向
     * @returns 轴向
     */
    getAxisA(): THREE.Vector3;
    /**
     * 设置转向轴向
     * @param axis 轴向
     */
    setAxisB(axis: THREE.Vector3): void;
    /**
     * 获取转向轴向
     * @returns 轴向
     */
    getAxisB(): THREE.Vector3;
    /**
     * 设置悬挂刚度
     * @param stiffness 刚度
     */
    setSuspensionStiffness(stiffness: number): void;
    /**
     * 获取悬挂刚度
     * @returns 刚度
     */
    getSuspensionStiffness(): number;
    /**
     * 设置悬挂阻尼
     * @param damping 阻尼
     */
    setSuspensionDamping(damping: number): void;
    /**
     * 获取悬挂阻尼
     * @returns 阻尼
     */
    getSuspensionDamping(): number;
    /**
     * 设置悬挂长度
     * @param length 长度
     */
    setSuspensionLength(length: number): void;
    /**
     * 获取悬挂长度
     * @returns 长度
     */
    getSuspensionLength(): number;
    /**
     * 重新创建约束
     */
    private recreateConstraint;
    /**
     * 设置转向角度
     * @param angle 角度
     */
    setSteeringAngle(angle: number): void;
    /**
     * 获取转向角度
     * @returns 角度
     */
    getSteeringAngle(): number;
    /**
     * 销毁约束
     */
    dispose(): void;
}
