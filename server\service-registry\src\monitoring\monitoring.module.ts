import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MonitoringService } from './monitoring.service';
import { MonitoringController } from './monitoring.controller';
import { RegistryModule } from '../registry/registry.module';
import { AuthModule } from '../auth/auth.module';
import { ServiceCacheModule } from '../registry/cache/service-cache.module';
import { LoadBalancerModule } from '../registry/load-balancer/load-balancer.module';

@Module({
  imports: [RegistryModule, AuthModule, ServiceCacheModule, LoadBalancerModule],
  controllers: [MonitoringController],
  providers: [MonitoringService],
  exports: [MonitoringService],
})
export class MonitoringModule {}
