/**
 * TCP健康检查策略
 */
import { Injectable, Logger } from '@nestjs/common';
import { 
  HealthCheckStrategy, 
  HealthCheckConfig, 
  HealthCheckResult, 
  HealthCheckStatus,
  TcpHealthCheckConfig
} from './health-check.interface';
import * as net from 'net';
import { URL } from 'url';

@Injectable()
export class TcpHealthCheckStrategy implements HealthCheckStrategy {
  private readonly logger = new Logger(TcpHealthCheckStrategy.name);

  /**
   * 执行TCP健康检查
   * @param config 健康检查配置
   */
  async check(config: HealthCheckConfig): Promise<HealthCheckResult> {
    const tcpConfig = config as TcpHealthCheckConfig;
    const startTime = Date.now();
    
    return new Promise<HealthCheckResult>((resolve) => {
      try {
        // 解析目标地址
        const { host, port } = this.parseTarget(tcpConfig.target);
        
        // 创建TCP连接
        const socket = new net.Socket();
        let responseData = '';
        
        // 设置超时
        socket.setTimeout(tcpConfig.timeout);
        
        // 连接成功
        socket.on('connect', () => {
          // 如果需要发送数据
          if (tcpConfig.send) {
            socket.write(tcpConfig.send);
          } else {
            // 如果不需要检查响应，则直接认为健康
            if (!tcpConfig.expect) {
              const responseTime = Date.now() - startTime;
              socket.destroy();
              
              resolve({
                status: HealthCheckStatus.HEALTHY,
                details: `TCP连接成功: ${host}:${port}`,
                timestamp: new Date(),
                responseTime,
              });
            }
          }
        });
        
        // 接收数据
        socket.on('data', (data) => {
          responseData += data.toString();
          
          // 如果期望的响应已收到，则认为健康
          if (tcpConfig.expect && responseData.includes(tcpConfig.expect)) {
            const responseTime = Date.now() - startTime;
            socket.destroy();
            
            resolve({
              status: HealthCheckStatus.HEALTHY,
              details: `TCP检查成功: ${host}:${port}`,
              timestamp: new Date(),
              responseTime,
              metadata: {
                response: responseData,
              },
            });
          }
        });
        
        // 连接结束
        socket.on('end', () => {
          const responseTime = Date.now() - startTime;
          
          // 如果期望的响应未收到，则认为不健康
          if (tcpConfig.expect && !responseData.includes(tcpConfig.expect)) {
            resolve({
              status: HealthCheckStatus.UNHEALTHY,
              details: `TCP响应不匹配: ${host}:${port}`,
              timestamp: new Date(),
              responseTime,
              metadata: {
                response: responseData,
                expected: tcpConfig.expect,
              },
            });
          }
        });
        
        // 连接错误
        socket.on('error', (error) => {
          const responseTime = Date.now() - startTime;
          socket.destroy();
          
          this.logger.error(`TCP连接错误: ${error.message}`, error.stack);
          
          resolve({
            status: HealthCheckStatus.UNHEALTHY,
            details: `TCP连接错误: ${error.message}`,
            timestamp: new Date(),
            responseTime,
            metadata: {
              error: error.message,
            },
          });
        });
        
        // 连接超时
        socket.on('timeout', () => {
          const responseTime = Date.now() - startTime;
          socket.destroy();
          
          this.logger.warn(`TCP连接超时: ${host}:${port}`);
          
          resolve({
            status: HealthCheckStatus.UNHEALTHY,
            details: `TCP连接超时: ${host}:${port}`,
            timestamp: new Date(),
            responseTime,
          });
        });
        
        // 连接关闭
        socket.on('close', (hadError) => {
          if (!hadError && !tcpConfig.expect) {
            const responseTime = Date.now() - startTime;
            
            resolve({
              status: HealthCheckStatus.HEALTHY,
              details: `TCP连接成功并关闭: ${host}:${port}`,
              timestamp: new Date(),
              responseTime,
            });
          }
        });
        
        // 建立连接
        socket.connect(port, host);
      } catch (error) {
        const responseTime = Date.now() - startTime;
        
        this.logger.error(`TCP健康检查失败: ${error.message}`, error.stack);
        
        resolve({
          status: HealthCheckStatus.UNHEALTHY,
          details: `TCP检查异常: ${error.message}`,
          timestamp: new Date(),
          responseTime,
          metadata: {
            error: error.message,
          },
        });
      }
    });
  }
  
  /**
   * 解析目标地址
   * @param target 目标地址
   */
  private parseTarget(target: string): { host: string; port: number } {
    try {
      // 尝试解析为URL
      if (target.includes('://')) {
        const url = new URL(target);
        return {
          host: url.hostname,
          port: parseInt(url.port, 10) || 80,
        };
      }
      
      // 解析为host:port格式
      const [host, portStr] = target.split(':');
      const port = parseInt(portStr, 10);
      
      if (!host || isNaN(port)) {
        throw new Error(`无效的TCP目标地址: ${target}`);
      }
      
      return { host, port };
    } catch (error) {
      throw new Error(`解析TCP目标地址失败: ${error.message}`);
    }
  }
}
