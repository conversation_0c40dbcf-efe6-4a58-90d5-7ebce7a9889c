/**
 * AI模型
 * 用于情感分析和面部动画生成
 */
import { EmotionAnalysisResult } from './EmotionBasedAnimationGenerator';
/**
 * AI模型配置
 */
export interface AIModelConfig {
    /** 是否使用本地模型 */
    useLocalModel?: boolean;
    /** 模型路径 */
    modelPath?: string;
    /** 是否使用GPU */
    useGPU?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
    /** 模型类型 */
    modelType?: 'bert' | 'roberta' | 'distilbert' | 'albert' | 'xlnet' | 'custom';
    /** 模型变体 */
    modelVariant?: string;
    /** 是否使用量化模型 */
    useQuantized?: boolean;
    /** 量化位数 */
    quantizationBits?: 8 | 16 | 32;
    /** 批处理大小 */
    batchSize?: number;
    /** 情感类别 */
    emotionCategories?: string[];
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存大小 */
    cacheSize?: number;
}
/**
 * 情感分析请求
 */
export interface EmotionAnalysisRequest {
    /** 文本 */
    text: string;
    /** 详细程度 */
    detail?: 'low' | 'medium' | 'high';
    /** 是否包含次要情感 */
    includeSecondary?: boolean;
    /** 是否包含情感变化 */
    includeChanges?: boolean;
}
/**
 * AI模型
 */
export declare class AIModel {
    /** 配置 */
    private config;
    /** 是否已初始化 */
    private initialized;
    /** 是否启用调试 */
    private debug;
    /** 模型实例 */
    private model;
    /** 情感映射 */
    private emotionMap;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: AIModelConfig);
    /**
     * 初始化
     */
    initialize(): Promise<boolean>;
    /**
     * 加载本地模型
     */
    private loadLocalModel;
    /**
     * 初始化远程API
     */
    private initializeRemoteAPI;
    /**
     * 分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    analyzeEmotion(text: string, options?: Partial<Omit<EmotionAnalysisRequest, 'text'>>): Promise<EmotionAnalysisResult>;
    /**
     * 使用本地模型分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    private analyzeEmotionWithLocalModel;
    /**
     * 使用远程API分析情感
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    private analyzeEmotionWithRemoteAPI;
    /**
     * 模拟情感分析
     * @param text 文本
     * @param options 选项
     * @returns 情感分析结果
     */
    private simulateEmotionAnalysis;
}
