import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DigitalHumansService } from './digital-humans.service';

@ApiTags('digital-humans')
@Controller('digital-humans')
export class DigitalHumansController {
  constructor(private readonly digitalHumansService: DigitalHumansService) {}

  @Get()
  @ApiOperation({ summary: '获取数字人列表' })
  @ApiResponse({ status: 200, description: '获取数字人列表成功' })
  async findAll() {
    return this.digitalHumansService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: '获取数字人详情' })
  @ApiResponse({ status: 200, description: '获取数字人详情成功' })
  async findOne(@Param('id') id: string) {
    return this.digitalHumansService.findOne(id);
  }
}
