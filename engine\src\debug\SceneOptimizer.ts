/**
 * 场景优化器
 * 提供场景分析和自动优化功能
 */
import * as THREE from 'three';
import { Scene } from '../scene/Scene';
import type { Entity } from '../core/Entity';
import type { Transform } from '../scene/Transform';
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';
import { LODSystem } from '../rendering/optimization/LODSystem';
import { BatchingSystem } from '../rendering/optimization/BatchingSystem';
import { FrustumCullingSystem } from '../rendering/optimization/FrustumCullingSystem';
import { InstancedRenderingSystem } from '../rendering/optimization/InstancedRenderingSystem';
import { LODComponent } from '../rendering/optimization/LODComponent';
import { CullableComponent } from '../rendering/optimization/CullableComponent';
import { InstancedComponent } from '../rendering/optimization/InstancedComponent';
import { OptimizationHistory } from './OptimizationHistory';

/**
 * 优化类型
 */
export enum OptimizationType {
  /** LOD优化 */
  LOD = 'lod',
  /** 视锥体剔除优化 */
  FRUSTUM_CULLING = 'frustumCulling',
  /** 实例化渲染优化 */
  INSTANCED_RENDERING = 'instancedRendering',
  /** 批处理优化 */
  BATCHING = 'batching',
  /** 材质优化 */
  MATERIAL_OPTIMIZATION = 'materialOptimization',
  /** 纹理优化 */
  TEXTURE_OPTIMIZATION = 'textureOptimization',
  /** 几何体优化 */
  GEOMETRY_OPTIMIZATION = 'geometryOptimization',
  /** 灯光优化 */
  LIGHT_OPTIMIZATION = 'lightOptimization',
  /** 阴影优化 */
  SHADOW_OPTIMIZATION = 'shadowOptimization',
  /** 内存优化 */
  MEMORY_OPTIMIZATION = 'memoryOptimization',
  /** 遮挡剔除优化 */
  OCCLUSION_CULLING = 'occlusionCulling',
  /** 细节纹理优化 */
  DETAIL_TEXTURE = 'detailTexture',
  /** 网格简化优化 */
  MESH_SIMPLIFICATION = 'meshSimplification',
  /** 粒子系统优化 */
  PARTICLE_OPTIMIZATION = 'particleOptimization',
  /** 动画优化 */
  ANIMATION_OPTIMIZATION = 'animationOptimization'
}

/**
 * 优化严重程度
 */
export enum OptimizationSeverity {
  /** 低 */
  LOW = 'low',
  /** 中 */
  MEDIUM = 'medium',
  /** 高 */
  HIGH = 'high'
}

/**
 * 优化建议
 */
export interface OptimizationSuggestion {
  /** 类型 */
  type: OptimizationType;
  /** 严重程度 */
  severity: OptimizationSeverity;
  /** 标题 */
  title: string;
  /** 描述 */
  description: string;
  /** 提示 */
  tips: string[];
  /** 分数 */
  score: number;
  /** 目标实体 */
  targetEntities?: Entity[];
  /** 自动优化函数 */
  autoOptimize?: () => Promise<boolean>;
}

/**
 * 场景分析结果
 */
export interface SceneAnalysisResult {
  /** 场景ID */
  sceneId: string;
  /** 场景名称 */
  sceneName: string;
  /** 实体数量 */
  entityCount: number;
  /** 可渲染对象数量 */
  renderableCount: number;
  /** 三角形数量 */
  triangleCount: number;
  /** 顶点数量 */
  vertexCount: number;
  /** 材质数量 */
  materialCount: number;
  /** 纹理数量 */
  textureCount: number;
  /** 纹理内存 (MB) */
  textureMemory: number;
  /** 灯光数量 */
  lightCount: number;
  /** 绘制调用数量 */
  drawCalls: number;
  /** 内存使用 (MB) */
  memoryUsage: number;
  /** 优化建议 */
  suggestions: OptimizationSuggestion[];
  /** 总体得分 */
  overallScore: number;
  /** 分析时间戳 */
  timestamp: number;
}

/**
 * 场景优化器配置
 */
export interface SceneOptimizerConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否启用自动LOD生成 */
  enableAutoLOD?: boolean;
  /** 是否启用自动实例化 */
  enableAutoInstancing?: boolean;
  /** 是否启用自动批处理 */
  enableAutoBatching?: boolean;
  /** 是否启用自动材质优化 */
  enableAutoMaterialOptimization?: boolean;
  /** 是否启用自动纹理优化 */
  enableAutoTextureOptimization?: boolean;
  /** 是否启用自动几何体优化 */
  enableAutoGeometryOptimization?: boolean;
  /** 是否启用自动灯光优化 */
  enableAutoLightOptimization?: boolean;
  /** 是否启用自动阴影优化 */
  enableAutoShadowOptimization?: boolean;
  /** 是否启用自动内存优化 */
  enableAutoMemoryOptimization?: boolean;
  /** 是否启用遮挡剔除优化 */
  enableOcclusionCulling?: boolean;
  /** 是否启用细节纹理优化 */
  enableDetailTexture?: boolean;
  /** 是否启用网格简化优化 */
  enableMeshSimplification?: boolean;
  /** 是否启用粒子系统优化 */
  enableParticleOptimization?: boolean;
  /** 是否启用动画优化 */
  enableAnimationOptimization?: boolean;
  /** 优化阈值 */
  thresholds?: {
    /** 三角形数量阈值 */
    triangles?: {
      /** 低严重度阈值 */
      low: number;
      /** 中严重度阈值 */
      medium: number;
      /** 高严重度阈值 */
      high: number;
    };
    /** 绘制调用阈值 */
    drawCalls?: {
      /** 低严重度阈值 */
      low: number;
      /** 中严重度阈值 */
      medium: number;
      /** 高严重度阈值 */
      high: number;
    };
    /** 内存使用阈值 (MB) */
    memory?: {
      /** 低严重度阈值 */
      low: number;
      /** 中严重度阈值 */
      medium: number;
      /** 高严重度阈值 */
      high: number;
    };
    /** 粒子数量阈值 */
    particles?: {
      /** 低严重度阈值 */
      low: number;
      /** 中严重度阈值 */
      medium: number;
      /** 高严重度阈值 */
      high: number;
    };
    /** 动画数量阈值 */
    animations?: {
      /** 低严重度阈值 */
      low: number;
      /** 中严重度阈值 */
      medium: number;
      /** 高严重度阈值 */
      high: number;
    };
    /** 纹理分辨率阈值 */
    textureResolution?: {
      /** 低严重度阈值 */
      low: number;
      /** 中严重度阈值 */
      medium: number;
      /** 高严重度阈值 */
      high: number;
    };
  };
}

/**
 * 场景优化器事件类型
 */
export enum SceneOptimizerEventType {
  /** 分析开始 */
  ANALYSIS_START = 'analysisStart',
  /** 分析完成 */
  ANALYSIS_COMPLETE = 'analysisComplete',
  /** 优化开始 */
  OPTIMIZATION_START = 'optimizationStart',
  /** 优化进度 */
  OPTIMIZATION_PROGRESS = 'optimizationProgress',
  /** 优化完成 */
  OPTIMIZATION_COMPLETE = 'optimizationComplete',
  /** 优化失败 */
  OPTIMIZATION_FAILED = 'optimizationFailed'
}

/**
 * 场景优化器
 */
export class SceneOptimizer extends EventEmitter {
  /** 单例实例 */
  private static instance: SceneOptimizer;

  /** 配置 */
  private config: Required<SceneOptimizerConfig>;

  /** 最后一次分析结果 */
  private lastAnalysisResult: SceneAnalysisResult | null = null;

  /** LOD系统 */
  private lodSystem: LODSystem | null = null;

  /** 批处理系统 */
  private batchingSystem: BatchingSystem | null = null;

  /** 视锥体剔除系统 */
  private frustumCullingSystem: FrustumCullingSystem | null = null;

  /** 实例化渲染系统 */
  private instancedRenderingSystem: InstancedRenderingSystem | null = null;

  /**
   * 获取单例实例
   */
  public static getInstance(): SceneOptimizer {
    if (!SceneOptimizer.instance) {
      SceneOptimizer.instance = new SceneOptimizer();
    }
    return SceneOptimizer.instance;
  }

  /**
   * 构造函数
   * @param config 配置
   */
  private constructor(config: SceneOptimizerConfig = {}) {
    super();

    // 设置默认配置
    this.config = {
      debug: config.debug ?? false,
      enableAutoLOD: config.enableAutoLOD ?? true,
      enableAutoInstancing: config.enableAutoInstancing ?? true,
      enableAutoBatching: config.enableAutoBatching ?? true,
      enableAutoMaterialOptimization: config.enableAutoMaterialOptimization ?? true,
      enableAutoTextureOptimization: config.enableAutoTextureOptimization ?? true,
      enableAutoGeometryOptimization: config.enableAutoGeometryOptimization ?? true,
      enableAutoLightOptimization: config.enableAutoLightOptimization ?? true,
      enableAutoShadowOptimization: config.enableAutoShadowOptimization ?? true,
      enableAutoMemoryOptimization: config.enableAutoMemoryOptimization ?? true,
      enableOcclusionCulling: config.enableOcclusionCulling ?? true,
      enableDetailTexture: config.enableDetailTexture ?? true,
      enableMeshSimplification: config.enableMeshSimplification ?? true,
      enableParticleOptimization: config.enableParticleOptimization ?? true,
      enableAnimationOptimization: config.enableAnimationOptimization ?? true,
      thresholds: {
        triangles: {
          low: config.thresholds?.triangles?.low ?? 100000,
          medium: config.thresholds?.triangles?.medium ?? 500000,
          high: config.thresholds?.triangles?.high ?? 1000000
        },
        drawCalls: {
          low: config.thresholds?.drawCalls?.low ?? 100,
          medium: config.thresholds?.drawCalls?.medium ?? 500,
          high: config.thresholds?.drawCalls?.high ?? 1000
        },
        memory: {
          low: config.thresholds?.memory?.low ?? 100,
          medium: config.thresholds?.memory?.medium ?? 250,
          high: config.thresholds?.memory?.high ?? 500
        },
        particles: {
          low: config.thresholds?.particles?.low ?? 1000,
          medium: config.thresholds?.particles?.medium ?? 5000,
          high: config.thresholds?.particles?.high ?? 10000
        },
        animations: {
          low: config.thresholds?.animations?.low ?? 10,
          medium: config.thresholds?.animations?.medium ?? 30,
          high: config.thresholds?.animations?.high ?? 50
        },
        textureResolution: {
          low: config.thresholds?.textureResolution?.low ?? 1024,
          medium: config.thresholds?.textureResolution?.medium ?? 2048,
          high: config.thresholds?.textureResolution?.high ?? 4096
        }
      }
    };

    if (this.config.debug) {
      Debug.log('SceneOptimizer', '初始化完成', this.config);
    }
  }

  /**
   * 配置优化器
   * @param config 配置
   */
  public configure(config: SceneOptimizerConfig): void {
    // 更新配置
    this.config = {
      ...this.config,
      ...config,
      thresholds: {
        ...this.config.thresholds,
        ...config.thresholds,
        triangles: {
          ...this.config.thresholds.triangles,
          ...config.thresholds?.triangles
        },
        drawCalls: {
          ...this.config.thresholds.drawCalls,
          ...config.thresholds?.drawCalls
        },
        memory: {
          ...this.config.thresholds.memory,
          ...config.thresholds?.memory
        },
        particles: {
          ...this.config.thresholds.particles,
          ...config.thresholds?.particles
        },
        animations: {
          ...this.config.thresholds.animations,
          ...config.thresholds?.animations
        },
        textureResolution: {
          ...this.config.thresholds.textureResolution,
          ...config.thresholds?.textureResolution
        }
      }
    };

    if (this.config.debug) {
      Debug.log('SceneOptimizer', '配置已更新', this.config);
    }
  }

  /**
   * 设置优化系统
   * @param lodSystem LOD系统
   * @param batchingSystem 批处理系统
   * @param frustumCullingSystem 视锥体剔除系统
   * @param instancedRenderingSystem 实例化渲染系统
   */
  public setOptimizationSystems(
    lodSystem: LODSystem | null,
    batchingSystem: BatchingSystem | null,
    frustumCullingSystem: FrustumCullingSystem | null,
    instancedRenderingSystem: InstancedRenderingSystem | null
  ): void {
    this.lodSystem = lodSystem;
    this.batchingSystem = batchingSystem;
    this.frustumCullingSystem = frustumCullingSystem;
    this.instancedRenderingSystem = instancedRenderingSystem;

    if (this.config.debug) {
      Debug.log('SceneOptimizer', '优化系统已设置');
    }
  }

  /**
   * 分析场景
   * @param scene 场景
   * @returns 分析结果Promise
   */
  public async analyzeScene(scene: Scene): Promise<SceneAnalysisResult> {
    if (!scene) {
      throw new Error('场景不能为空');
    }

    // 发出分析开始事件
    this.emit(SceneOptimizerEventType.ANALYSIS_START, { scene });

    if (this.config.debug) {
      Debug.log('SceneOptimizer', `开始分析场景: ${scene.getName()}`);
    }

    // 收集场景统计信息
    const stats = this.collectSceneStats(scene);

    // 生成优化建议
    const suggestions = this.generateOptimizationSuggestions(scene, stats);

    // 计算总体得分
    const overallScore = this.calculateOverallScore(stats, suggestions);

    // 创建分析结果
    const result: SceneAnalysisResult = {
      sceneId: scene.getId() || '',
      sceneName: scene.getName() || '',
      ...stats,
      suggestions,
      overallScore,
      timestamp: Date.now()
    };

    // 保存最后一次分析结果
    this.lastAnalysisResult = result;

    // 发出分析完成事件
    this.emit(SceneOptimizerEventType.ANALYSIS_COMPLETE, { scene, result });

    if (this.config.debug) {
      Debug.log('SceneOptimizer', `场景分析完成: ${scene.getName()}`, result);
    }

    return result;
  }

  /**
   * 收集场景统计信息
   * @param scene 场景
   * @returns 场景统计信息
   */
  private collectSceneStats(scene: Scene): {
    entityCount: number;
    renderableCount: number;
    triangleCount: number;
    vertexCount: number;
    materialCount: number;
    textureCount: number;
    textureMemory: number;
    lightCount: number;
    drawCalls: number;
    memoryUsage: number;
  } {
    // 获取场景中的所有实体
    const entities = scene.getEntities();

    // 初始化统计信息
    let renderableCount = 0;
    let triangleCount = 0;
    let vertexCount = 0;
    let materialCount = 0;
    let textureCount = 0;
    let textureMemory = 0;
    let lightCount = 0;
    let drawCalls = 0;
    let memoryUsage = 0;

    // 材质和纹理集合（用于去重）
    const materials = new Set<THREE.Material>();
    const textures = new Set<THREE.Texture>();

    // 遍历所有实体
    for (const entity of entities) {
      // 检查是否有Transform组件
      const transform = entity.getComponent('Transform') as any as Transform;
      if (!transform) continue;

      // 获取Three.js对象
      const object3D = transform.getObject3D();
      if (!object3D) continue;

      // 收集渲染统计信息
      object3D.traverse((obj) => {
        // 检查是否是网格
        if (obj instanceof THREE.Mesh) {
          renderableCount++;

          // 收集几何体信息
          const geometry = obj.geometry;
          if (geometry) {
            // 计算三角形数量
            if (geometry.index) {
              triangleCount += geometry.index.count / 3;
            } else if (geometry.attributes.position) {
              triangleCount += geometry.attributes.position.count / 3;
            }

            // 计算顶点数量
            if (geometry.attributes.position) {
              vertexCount += geometry.attributes.position.count;
            }

            // 估算几何体内存
            const geometryMemory = this.estimateGeometryMemory(geometry);
            memoryUsage += geometryMemory / (1024 * 1024); // 转换为MB
          }

          // 收集材质信息
          if (obj.material) {
            // 处理材质数组
            const mats = Array.isArray(obj.material) ? obj.material : [obj.material];

            for (const mat of mats) {
              // 如果是新材质，添加到集合
              if (!materials.has(mat)) {
                materials.add(mat);
                materialCount++;

                // 估算材质内存
                const materialMemory = this.estimateMaterialMemory(mat);
                memoryUsage += materialMemory / (1024 * 1024); // 转换为MB

                // 收集纹理信息
                this.collectTextureInfo(mat, textures, (tex) => {
                  textureCount++;
                  const texMemory = this.estimateTextureMemory(tex);
                  textureMemory += texMemory / (1024 * 1024); // 转换为MB
                });
              }
            }

            // 估算绘制调用
            drawCalls += mats.length;
          }
        }
        // 检查是否是灯光
        else if (obj instanceof THREE.Light) {
          lightCount++;
        }
      });
    }

    // 返回统计信息
    return {
      entityCount: entities.length,
      renderableCount,
      triangleCount,
      vertexCount,
      materialCount,
      textureCount,
      textureMemory,
      lightCount,
      drawCalls,
      memoryUsage
    };
  }

  /**
   * 收集纹理信息
   * @param material 材质
   * @param textureSet 纹理集合
   * @param callback 回调函数
   */
  private collectTextureInfo(
    material: THREE.Material,
    textureSet: Set<THREE.Texture>,
    callback: (texture: THREE.Texture) => void
  ): void {
    // 检查常见的纹理属性
    const textureProps = [
      'map',
      'normalMap',
      'bumpMap',
      'displacementMap',
      'roughnessMap',
      'metalnessMap',
      'alphaMap',
      'aoMap',
      'emissiveMap',
      'envMap',
      'lightMap',
      'specularMap'
    ];

    // 遍历所有可能的纹理属性
    for (const prop of textureProps) {
      if ((material as any)[prop] instanceof THREE.Texture) {
        const texture = (material as any)[prop] as THREE.Texture;

        // 如果是新纹理，添加到集合并调用回调
        if (!textureSet.has(texture)) {
          textureSet.add(texture);
          callback(texture);
        }
      }
    }
  }

  /**
   * 估算几何体内存
   * @param geometry 几何体
   * @returns 内存大小（字节）
   */
  private estimateGeometryMemory(geometry: THREE.BufferGeometry): number {
    let memory = 0;

    // 计算索引内存
    if (geometry.index) {
      const indexArray = geometry.index.array as any;
      memory += indexArray.byteLength || (indexArray.length * 4); // 假设每个索引4字节
    }

    // 计算属性内存
    for (const name in geometry.attributes) {
      const attribute = geometry.attributes[name];
      const attributeArray = attribute.array as any;
      memory += attributeArray.byteLength || (attributeArray.length * 4); // 假设每个值4字节
    }

    return memory;
  }

  /**
   * 估算材质内存
   * @param material 材质
   * @returns 内存大小（字节）
   */
  private estimateMaterialMemory(_material: THREE.Material): number {
    // 简单估算，实际上材质本身占用的内存很小
    // 主要内存消耗来自于纹理
    return 1024; // 假设每个材质占用1KB
  }

  /**
   * 估算纹理内存
   * @param texture 纹理
   * @returns 内存大小（字节）
   */
  private estimateTextureMemory(texture: THREE.Texture): number {
    if (!texture.image) {
      return 0;
    }

    // 获取图像尺寸
    const width = texture.image.width || 0;
    const height = texture.image.height || 0;

    // 估算每像素字节数（根据格式）
    let bytesPerPixel = 4; // 默认RGBA

    // 计算总内存
    return width * height * bytesPerPixel;
  }

  /**
   * 生成优化建议
   * @param scene 场景
   * @param stats 统计信息
   * @returns 优化建议数组
   */
  private generateOptimizationSuggestions(
    scene: Scene,
    stats: {
      entityCount: number;
      renderableCount: number;
      triangleCount: number;
      vertexCount: number;
      materialCount: number;
      textureCount: number;
      textureMemory: number;
      lightCount: number;
      drawCalls: number;
      memoryUsage: number;
    }
  ): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // 检查三角形数量
    if (stats.triangleCount > this.config.thresholds.triangles.low) {
      const severity = stats.triangleCount > this.config.thresholds.triangles.high
        ? OptimizationSeverity.HIGH
        : stats.triangleCount > this.config.thresholds.triangles.medium
          ? OptimizationSeverity.MEDIUM
          : OptimizationSeverity.LOW;

      const score = this.calculateScore(
        stats.triangleCount,
        this.config.thresholds.triangles.high * 2,
        this.config.thresholds.triangles.low / 2
      );

      suggestions.push({
        type: OptimizationType.LOD,
        severity,
        title: '模型复杂度过高',
        description: `场景中的三角形数量（${Math.round(stats.triangleCount)}）超过了推荐值。过多的三角形会降低渲染性能，特别是在移动设备上。`,
        tips: [
          '使用LOD（细节层次）技术来根据距离显示不同细节级别的模型',
          '简化远处或不重要的模型',
          '考虑使用法线贴图代替高多边形模型'
        ],
        score,
        autoOptimize: this.config.enableAutoLOD && this.lodSystem
          ? () => this.optimizeLOD(scene)
          : undefined
      });
    }

    // 检查绘制调用数量
    if (stats.drawCalls > this.config.thresholds.drawCalls.low) {
      const severity = stats.drawCalls > this.config.thresholds.drawCalls.high
        ? OptimizationSeverity.HIGH
        : stats.drawCalls > this.config.thresholds.drawCalls.medium
          ? OptimizationSeverity.MEDIUM
          : OptimizationSeverity.LOW;

      const score = this.calculateScore(
        stats.drawCalls,
        this.config.thresholds.drawCalls.high * 2,
        this.config.thresholds.drawCalls.low / 2
      );

      suggestions.push({
        type: OptimizationType.BATCHING,
        severity,
        title: '绘制调用过多',
        description: `场景中的绘制调用数量（${stats.drawCalls}）过多。过多的绘制调用会导致CPU瓶颈。`,
        tips: [
          '合并使用相同材质的网格',
          '使用实例化渲染技术',
          '减少场景中的材质数量',
          '使用纹理图集代替多个小纹理'
        ],
        score,
        autoOptimize: this.config.enableAutoBatching && this.batchingSystem
          ? () => this.optimizeBatching(scene)
          : undefined
      });
    }

    // 检查内存使用
    if (stats.memoryUsage > this.config.thresholds.memory.low) {
      const severity = stats.memoryUsage > this.config.thresholds.memory.high
        ? OptimizationSeverity.HIGH
        : stats.memoryUsage > this.config.thresholds.memory.medium
          ? OptimizationSeverity.MEDIUM
          : OptimizationSeverity.LOW;

      const score = this.calculateScore(
        stats.memoryUsage,
        this.config.thresholds.memory.high * 2,
        this.config.thresholds.memory.low / 2
      );

      suggestions.push({
        type: OptimizationType.MEMORY_OPTIMIZATION,
        severity,
        title: '内存使用过高',
        description: `场景内存使用（${Math.round(stats.memoryUsage)} MB）过高。过高的内存使用可能导致性能问题和崩溃。`,
        tips: [
          '减少纹理分辨率或使用纹理压缩',
          '简化几何体',
          '使用实例化渲染',
          '实现资源管理系统，动态加载和卸载资源'
        ],
        score,
        autoOptimize: this.config.enableAutoMemoryOptimization
          ? () => this.optimizeMemory(scene)
          : undefined
      });
    }

    // 检查纹理内存
    if (stats.textureMemory > 100) { // 超过100MB的纹理内存
      const severity = stats.textureMemory > 500
        ? OptimizationSeverity.HIGH
        : stats.textureMemory > 250
          ? OptimizationSeverity.MEDIUM
          : OptimizationSeverity.LOW;

      const score = this.calculateScore(stats.textureMemory, 1000, 50);

      suggestions.push({
        type: OptimizationType.TEXTURE_OPTIMIZATION,
        severity,
        title: '纹理内存过高',
        description: `场景纹理内存使用（${Math.round(stats.textureMemory)} MB）过高。大量的高分辨率纹理会消耗大量GPU内存。`,
        tips: [
          '减少纹理分辨率',
          '使用纹理压缩格式（如DXT, ETC, ASTC）',
          '使用mipmap减少远处纹理的内存使用',
          '合并多个纹理到纹理图集'
        ],
        score,
        autoOptimize: this.config.enableAutoTextureOptimization
          ? () => this.optimizeTextures(scene)
          : undefined
      });
    }

    // 检查灯光数量
    if (stats.lightCount > 4) { // 超过4个灯光可能导致性能问题
      const severity = stats.lightCount > 8
        ? OptimizationSeverity.HIGH
        : stats.lightCount > 6
          ? OptimizationSeverity.MEDIUM
          : OptimizationSeverity.LOW;

      const score = this.calculateScore(stats.lightCount, 16, 2);

      suggestions.push({
        type: OptimizationType.LIGHT_OPTIMIZATION,
        severity,
        title: '灯光数量过多',
        description: `场景灯光数量（${stats.lightCount}）过多。过多的动态灯光会显著降低性能。`,
        tips: [
          '减少动态灯光数量',
          '使用光照贴图代替实时灯光',
          '合并相似的灯光',
          '使用环境光遮蔽（AO）贴图增强环境光效果'
        ],
        score,
        autoOptimize: this.config.enableAutoLightOptimization
          ? () => this.optimizeLights(scene)
          : undefined
      });
    }

    // 检查是否需要遮挡剔除
    if (stats.renderableCount > 100) { // 如果场景中有大量可渲染对象
      const severity = stats.renderableCount > 500
        ? OptimizationSeverity.HIGH
        : stats.renderableCount > 300
          ? OptimizationSeverity.MEDIUM
          : OptimizationSeverity.LOW;

      const score = this.calculateScore(stats.renderableCount, 1000, 50);

      suggestions.push({
        type: OptimizationType.OCCLUSION_CULLING,
        severity,
        title: '可渲染对象过多，建议启用遮挡剔除',
        description: `场景中的可渲染对象数量（${stats.renderableCount}）较多。启用遮挡剔除可以减少不可见对象的渲染，提高性能。`,
        tips: [
          '启用遮挡剔除功能',
          '将静态对象标记为遮挡物',
          '优化场景层次结构，便于剔除整组对象',
          '考虑使用预计算可见性来进一步优化'
        ],
        score,
        autoOptimize: this.config.enableOcclusionCulling
          ? () => this.optimizeOcclusionCulling(scene)
          : undefined
      });
    }

    // 检查是否需要网格简化
    if (stats.triangleCount > this.config.thresholds.triangles.low * 1.5) {
      const severity = stats.triangleCount > this.config.thresholds.triangles.high * 1.5
        ? OptimizationSeverity.HIGH
        : stats.triangleCount > this.config.thresholds.triangles.medium * 1.5
          ? OptimizationSeverity.MEDIUM
          : OptimizationSeverity.LOW;

      const score = this.calculateScore(
        stats.triangleCount,
        this.config.thresholds.triangles.high * 3,
        this.config.thresholds.triangles.low
      );

      suggestions.push({
        type: OptimizationType.MESH_SIMPLIFICATION,
        severity,
        title: '网格过于复杂，建议简化',
        description: `场景中的三角形总数（${Math.round(stats.triangleCount).toLocaleString()}）过高。对网格进行简化可以减少渲染负担。`,
        tips: [
          '使用网格简化算法减少远处或不重要对象的多边形数量',
          '为复杂模型创建多级LOD',
          '使用法线贴图和置换贴图代替高多边形模型',
          '优化模型拓扑结构，移除不必要的细节'
        ],
        score,
        autoOptimize: this.config.enableMeshSimplification
          ? () => this.optimizeMeshSimplification(scene)
          : undefined
      });
    }

    // 检查是否需要细节纹理优化
    if (stats.textureMemory > 200) { // 如果纹理内存超过200MB
      const severity = stats.textureMemory > 500
        ? OptimizationSeverity.HIGH
        : stats.textureMemory > 350
          ? OptimizationSeverity.MEDIUM
          : OptimizationSeverity.LOW;

      const score = this.calculateScore(stats.textureMemory, 1000, 100);

      suggestions.push({
        type: OptimizationType.DETAIL_TEXTURE,
        severity,
        title: '纹理内存使用过高，建议优化细节纹理',
        description: `场景纹理内存使用（${Math.round(stats.textureMemory)} MB）过高。使用细节纹理技术可以在保持视觉质量的同时减少内存使用。`,
        tips: [
          '使用细节纹理技术，用小纹理为大表面添加细节',
          '对远处物体使用较低分辨率的纹理',
          '使用程序化纹理生成技术',
          '优化纹理UV映射，减少纹理浪费'
        ],
        score,
        autoOptimize: this.config.enableDetailTexture
          ? () => this.optimizeDetailTexture(scene)
          : undefined
      });
    }

    // 检查是否需要粒子系统优化
    // 注意：这里假设我们能够从场景中获取粒子系统信息
    // 实际实现可能需要根据具体的粒子系统组件来收集信息
    const particleSystemCount = 0; // 这里应该是实际的粒子系统数量
    const totalParticleCount = 0; // 这里应该是实际的粒子总数

    if (particleSystemCount > 0 && totalParticleCount > this.config.thresholds.particles.low) {
      const severity = totalParticleCount > this.config.thresholds.particles.high
        ? OptimizationSeverity.HIGH
        : totalParticleCount > this.config.thresholds.particles.medium
          ? OptimizationSeverity.MEDIUM
          : OptimizationSeverity.LOW;

      const score = this.calculateScore(
        totalParticleCount,
        this.config.thresholds.particles.high * 2,
        this.config.thresholds.particles.low / 2
      );

      suggestions.push({
        type: OptimizationType.PARTICLE_OPTIMIZATION,
        severity,
        title: '粒子系统过于复杂',
        description: `场景中的粒子系统（${particleSystemCount}个系统，共${totalParticleCount}个粒子）过于复杂。优化粒子系统可以显著提高性能。`,
        tips: [
          '减少粒子数量',
          '使用粒子LOD技术，根据距离调整粒子数量',
          '优化粒子纹理和材质',
          '合并相似的粒子系统',
          '对远处的粒子系统降低更新频率'
        ],
        score,
        autoOptimize: this.config.enableParticleOptimization
          ? () => this.optimizeParticleSystem(scene)
          : undefined
      });
    }

    // 检查是否需要动画优化
    // 注意：这里假设我们能够从场景中获取动画信息
    // 实际实现可能需要根据具体的动画组件来收集信息
    const animationCount = 0; // 这里应该是实际的动画数量

    if (animationCount > this.config.thresholds.animations.low) {
      const severity = animationCount > this.config.thresholds.animations.high
        ? OptimizationSeverity.HIGH
        : animationCount > this.config.thresholds.animations.medium
          ? OptimizationSeverity.MEDIUM
          : OptimizationSeverity.LOW;

      const score = this.calculateScore(
        animationCount,
        this.config.thresholds.animations.high * 2,
        this.config.thresholds.animations.low / 2
      );

      suggestions.push({
        type: OptimizationType.ANIMATION_OPTIMIZATION,
        severity,
        title: '动画数量过多',
        description: `场景中的动画数量（${animationCount}）过多。优化动画系统可以减少CPU负担。`,
        tips: [
          '根据距离调整动画更新频率',
          '对不可见对象暂停动画',
          '简化远处对象的动画骨骼',
          '使用动画LOD技术',
          '预计算部分动画，减少实时计算'
        ],
        score,
        autoOptimize: this.config.enableAnimationOptimization
          ? () => this.optimizeAnimationSystem(scene)
          : undefined
      });
    }

    return suggestions;
  }

  /**
   * 计算分数
   * @param value 当前值
   * @param max 最大值（对应0分）
   * @param min 最小值（对应100分）
   * @returns 0-100的分数
   */
  private calculateScore(value: number, max: number, min: number): number {
    // 确保值在范围内
    const clampedValue = Math.max(min, Math.min(max, value));

    // 计算分数（线性插值）
    const score = 100 - ((clampedValue - min) / (max - min)) * 100;

    // 确保分数在0-100范围内
    return Math.max(0, Math.min(100, score));
  }

  /**
   * 计算总体得分
   * @param stats 统计信息
   * @param suggestions 优化建议
   * @returns 总体得分
   */
  private calculateOverallScore(
    _stats: {
      entityCount: number;
      renderableCount: number;
      triangleCount: number;
      vertexCount: number;
      materialCount: number;
      textureCount: number;
      textureMemory: number;
      lightCount: number;
      drawCalls: number;
      memoryUsage: number;
    },
    suggestions: OptimizationSuggestion[]
  ): number {
    if (suggestions.length === 0) {
      return 100; // 没有优化建议，满分
    }

    // 计算加权平均分
    let totalWeight = 0;
    let weightedSum = 0;

    for (const suggestion of suggestions) {
      let weight = 1;

      // 根据严重程度调整权重
      switch (suggestion.severity) {
        case OptimizationSeverity.HIGH:
          weight = 3;
          break;
        case OptimizationSeverity.MEDIUM:
          weight = 2;
          break;
        case OptimizationSeverity.LOW:
          weight = 1;
          break;
      }

      weightedSum += suggestion.score * weight;
      totalWeight += weight;
    }

    return weightedSum / totalWeight;
  }

  /**
   * 优化场景
   * @param scene 场景
   * @param description 优化描述（可选）
   * @returns 优化结果Promise
   */
  public async optimizeScene(scene: Scene, description: string = ''): Promise<boolean> {
    if (!scene) {
      throw new Error('场景不能为空');
    }

    // 如果没有分析结果，先分析场景
    if (!this.lastAnalysisResult || this.lastAnalysisResult.sceneId !== scene.getId()) {
      await this.analyzeScene(scene);
    }

    if (!this.lastAnalysisResult) {
      throw new Error('分析结果不可用');
    }

    // 保存优化前的分析结果
    const beforeAnalysisResult = { ...this.lastAnalysisResult };
    const scoreBefore = beforeAnalysisResult.overallScore;

    // 发出优化开始事件
    this.emit(SceneOptimizerEventType.OPTIMIZATION_START, { scene });

    if (this.config.debug) {
      Debug.log('SceneOptimizer', `开始优化场景: ${scene.getName()}`);
    }

    // 获取优化建议
    const suggestions = this.lastAnalysisResult.suggestions;

    // 按严重程度排序
    suggestions.sort((a, b) => {
      const severityOrder = {
        [OptimizationSeverity.HIGH]: 0,
        [OptimizationSeverity.MEDIUM]: 1,
        [OptimizationSeverity.LOW]: 2
      };
      return severityOrder[a.severity] - severityOrder[b.severity];
    });

    // 执行优化
    let success = true;
    let progress = 0;
    const totalSuggestions = suggestions.length;
    const appliedOptimizations: OptimizationType[] = [];

    for (const suggestion of suggestions) {
      // 检查是否有自动优化函数
      if (suggestion.autoOptimize) {
        try {
          // 执行优化
          const result = await suggestion.autoOptimize();

          // 更新进度
          progress++;

          // 发出进度事件
          this.emit(SceneOptimizerEventType.OPTIMIZATION_PROGRESS, {
            scene,
            suggestion,
            progress: progress / totalSuggestions,
            success: result
          });

          // 如果优化成功，记录应用的优化类型
          if (result) {
            appliedOptimizations.push(suggestion.type);
          }
          // 如果优化失败，记录但继续执行其他优化
          else {
            success = false;
            if (this.config.debug) {
              Debug.warn('SceneOptimizer', `优化失败: ${suggestion.title}`);
            }
          }
        } catch (error) {
          success = false;
          if (this.config.debug) {
            Debug.error('SceneOptimizer', `优化错误: ${suggestion.title}`, error);
          }

          // 发出失败事件
          this.emit(SceneOptimizerEventType.OPTIMIZATION_FAILED, {
            scene,
            suggestion,
            error
          });
        }
      }
    }

    // 重新分析场景，获取优化后的结果
    await this.analyzeScene(scene);
    const afterAnalysisResult = this.lastAnalysisResult;
    const scoreAfter = afterAnalysisResult.overallScore;

    // 记录优化历史
    if (appliedOptimizations.length > 0) {
      const history = OptimizationHistory.getInstance();
      history.addEntry({
        sceneId: scene.getId() || '',
        sceneName: scene.getName() || '',
        analysisResult: afterAnalysisResult,
        appliedOptimizations,
        scoreBefore,
        scoreAfter,
        timestamp: Date.now(),
        description: description || `应用了${appliedOptimizations.length}项优化`
      });
    }

    // 发出优化完成事件
    this.emit(SceneOptimizerEventType.OPTIMIZATION_COMPLETE, {
      scene,
      success,
      appliedOptimizations,
      scoreBefore,
      scoreAfter
    });

    if (this.config.debug) {
      Debug.log('SceneOptimizer', `场景优化完成: ${scene.getName()}, 成功: ${success}, 得分变化: ${scoreBefore} -> ${scoreAfter}`);
    }

    return success;
  }

  /**
   * 优化LOD
   * @param scene 场景
   * @returns 优化结果Promise
   */
  private async optimizeLOD(scene: Scene): Promise<boolean> {
    if (!this.lodSystem) {
      return false;
    }

    try {
      // 获取场景中的所有实体
      const entities = scene.getEntities();

      // 遍历所有实体
      for (const entity of entities) {
        // 检查是否已有LOD组件
        if (entity.hasComponent('LODComponent')) {
          continue;
        }

        // 检查是否有Transform组件
        const transform = entity.getComponent('Transform') as any as Transform;
        if (!transform) continue;

        // 获取Three.js对象
        const object3D = transform.getObject3D();
        if (!object3D) continue;

        // 查找网格
        let mesh: THREE.Mesh | null = null;
        object3D.traverse((obj) => {
          if (obj instanceof THREE.Mesh && !mesh) {
            mesh = obj;
          }
        });

        // 如果找到网格，生成LOD
        if (mesh) {
          // 计算包围球半径
          const geometry = mesh.geometry;
          if (geometry) {
            geometry.computeBoundingSphere();
            const radius = geometry.boundingSphere?.radius || 1;

            // 根据几何体大小设置LOD距离
            const distances = [
              radius * 5,  // 高细节
              radius * 15, // 中等细节
              radius * 30, // 低细节
              radius * 50  // 极低细节
            ];

            // 自动生成LOD
            this.lodSystem.autoGenerateLOD(entity, mesh, distances);
          }
        }
      }

      return true;
    } catch (error) {
      Debug.error('SceneOptimizer', '优化LOD失败:', error);
      return false;
    }
  }

  /**
   * 优化批处理
   * @param scene 场景
   * @returns 优化结果Promise
   */
  private async optimizeBatching(scene: Scene): Promise<boolean> {
    if (!this.batchingSystem) {
      return false;
    }

    try {
      // 启用批处理系统
      this.batchingSystem.setEnabled(true);

      // 设置批处理系统的活跃场景
      this.batchingSystem.setActiveScene(scene);

      // 收集静态实体并创建批处理
      const entities = scene.getEntities();
      const staticEntities = entities.filter(entity =>
        !entity.hasComponent('Animation') && !entity.hasComponent('RigidBody')
      );

      if (staticEntities.length > 0) {
        // 创建静态批处理
        this.batchingSystem.createStaticBatch(staticEntities, '自动静态批处理');
      }

      return true;
    } catch (error) {
      Debug.error('SceneOptimizer', '优化批处理失败:', error);
      return false;
    }
  }

  /**
   * 优化内存
   * @param scene 场景
   * @returns 优化结果Promise
   */
  private async optimizeMemory(scene: Scene): Promise<boolean> {
    try {
      // 获取场景中的所有实体
      const entities = scene.getEntities();

      // 遍历所有实体
      for (const entity of entities) {
        // 检查是否有Transform组件
        const transform = entity.getComponent('Transform') as any as Transform;
        if (!transform) continue;

        // 获取Three.js对象
        const object3D = transform.getObject3D();
        if (!object3D) continue;

        // 优化几何体
        object3D.traverse((obj) => {
          if (obj instanceof THREE.Mesh) {
            const geometry = obj.geometry;

            // 合并几何体缓冲区
            if (geometry && !geometry.attributes.uv2) {
              (geometry as any).dispose();
              obj.geometry = geometry.clone().toNonIndexed();
            }
          }
        });
      }

      // 强制垃圾回收
      if (typeof window !== 'undefined' && (window as any).gc) {
        (window as any).gc();
      }

      return true;
    } catch (error) {
      Debug.error('SceneOptimizer', '优化内存失败:', error);
      return false;
    }
  }

  /**
   * 优化纹理
   * @param scene 场景
   * @returns 优化结果Promise
   */
  private async optimizeTextures(scene: Scene): Promise<boolean> {
    try {
      // 获取场景中的所有实体
      const entities = scene.getEntities();

      // 纹理集合（用于去重）
      const textures = new Set<THREE.Texture>();

      // 遍历所有实体
      for (const entity of entities) {
        // 检查是否有Transform组件
        const transform = entity.getComponent('Transform') as any as Transform;
        if (!transform) continue;

        // 获取Three.js对象
        const object3D = transform.getObject3D();
        if (!object3D) continue;

        // 收集和优化纹理
        object3D.traverse((obj) => {
          if (obj instanceof THREE.Mesh && obj.material) {
            // 处理材质数组
            const materials = Array.isArray(obj.material) ? obj.material : [obj.material];

            for (const material of materials) {
              // 收集纹理
              this.collectTextureInfo(material, textures, (texture) => {
                // 优化纹理
                if (texture.image && (texture.image.width > 2048 || texture.image.height > 2048)) {
                  // 对大纹理启用mipmap
                  texture.generateMipmaps = true;
                  texture.needsUpdate = true;
                }
              });
            }
          }
        });
      }

      return true;
    } catch (error) {
      Debug.error('SceneOptimizer', '优化纹理失败:', error);
      return false;
    }
  }

  /**
   * 优化灯光
   * @param scene 场景
   * @returns 优化结果Promise
   */
  private async optimizeLights(scene: Scene): Promise<boolean> {
    try {
      // 获取场景中的所有实体
      const entities = scene.getEntities();

      // 灯光列表
      const lights: THREE.Light[] = [];

      // 收集所有灯光
      for (const entity of entities) {
        // 检查是否有Transform组件
        const transform = entity.getComponent('Transform') as any as Transform;
        if (!transform) continue;

        // 获取Three.js对象
        const object3D = transform.getObject3D();
        if (!object3D) continue;

        // 收集灯光
        object3D.traverse((obj) => {
          if (obj instanceof THREE.Light) {
            lights.push(obj);
          }
        });
      }

      // 如果灯光数量超过8个，优化灯光
      if (lights.length > 8) {
        // 按重要性排序（点光源和聚光灯比方向光和环境光更可能被优化）
        lights.sort((a, b) => {
          // 环境光最重要
          if (a instanceof THREE.AmbientLight) return -1;
          if (b instanceof THREE.AmbientLight) return 1;

          // 方向光次之
          if (a instanceof THREE.DirectionalLight) return -1;
          if (b instanceof THREE.DirectionalLight) return 1;

          // 其他灯光按强度排序
          return b.intensity - a.intensity;
        });

        // 保留前8个灯光，禁用其余灯光
        for (let i = 8; i < lights.length; i++) {
          lights[i].visible = false;
        }
      }

      return true;
    } catch (error) {
      Debug.error('SceneOptimizer', '优化灯光失败:', error);
      return false;
    }
  }

  /**
   * 优化遮挡剔除
   * @param scene 场景
   * @returns 优化结果Promise
   */
  private async optimizeOcclusionCulling(scene: Scene): Promise<boolean> {
    if (!this.config.enableOcclusionCulling) {
      return false;
    }

    try {
      // 获取场景中的所有实体
      const entities = scene.getEntities();

      // 静态对象列表（可能被用作遮挡物）
      const staticObjects: THREE.Mesh[] = [];

      // 动态对象列表（需要进行遮挡检测的对象）
      const dynamicObjects: THREE.Mesh[] = [];

      // 收集静态和动态对象
      for (const entity of entities) {
        // 检查是否有Transform组件
        const transform = entity.getComponent('Transform') as any as Transform;
        if (!transform) continue;

        // 获取Three.js对象
        const object3D = transform.getObject3D();
        if (!object3D) continue;

        // 收集网格
        object3D.traverse((obj) => {
          if (obj instanceof THREE.Mesh) {
            // 简单判断：如果对象没有动画或物理组件，则视为静态对象
            const isStatic = !entity.hasComponent('Animation') && !entity.hasComponent('RigidBody');

            if (isStatic) {
              staticObjects.push(obj);
            } else {
              dynamicObjects.push(obj);
            }
          }
        });
      }

      // 为静态对象添加CullableComponent组件
      for (const entity of entities) {
        // 检查是否已有CullableComponent组件
        if (entity.hasComponent('CullableComponent')) {
          continue;
        }

        // 检查是否有Transform组件
        const transform = entity.getComponent('Transform') as any as Transform;
        if (!transform) continue;

        // 获取Three.js对象
        const object3D = transform.getObject3D();
        if (!object3D) continue;

        // 检查是否是静态对象
        let isStatic = false;
        object3D.traverse((obj) => {
          if (obj instanceof THREE.Mesh && staticObjects.includes(obj)) {
            isStatic = true;
          }
        });

        // 如果是静态对象，添加CullableComponent组件
        if (isStatic) {
          const cullable = new CullableComponent();
          entity.addComponent(cullable);
        }
      }

      // 如果有视锥体剔除系统，启用它
      if (this.frustumCullingSystem) {
        this.frustumCullingSystem.setEnabled(true);
        this.frustumCullingSystem.setActiveScene(scene);
      }

      return true;
    } catch (error) {
      Debug.error('SceneOptimizer', '优化遮挡剔除失败:', error);
      return false;
    }
  }

  /**
   * 优化细节纹理
   * @param scene 场景
   * @returns 优化结果Promise
   */
  private async optimizeDetailTexture(scene: Scene): Promise<boolean> {
    if (!this.config.enableDetailTexture) {
      return false;
    }

    try {
      // 获取场景中的所有实体
      const entities = scene.getEntities();

      // 纹理集合（用于去重）
      const textures = new Set<THREE.Texture>();

      // 遍历所有实体
      for (const entity of entities) {
        // 检查是否有Transform组件
        const transform = entity.getComponent('Transform') as any as Transform;
        if (!transform) continue;

        // 获取Three.js对象
        const object3D = transform.getObject3D();
        if (!object3D) continue;

        // 收集和优化纹理
        object3D.traverse((obj) => {
          if (obj instanceof THREE.Mesh && obj.material) {
            // 处理材质数组
            const materials = Array.isArray(obj.material) ? obj.material : [obj.material];

            for (const material of materials) {
              // 检查是否是MeshStandardMaterial或MeshPhysicalMaterial
              if (material instanceof THREE.MeshStandardMaterial || material instanceof THREE.MeshPhysicalMaterial) {
                // 收集纹理
                this.collectTextureInfo(material, textures, (texture) => {
                  // 检查纹理分辨率
                  if (texture.image &&
                      (texture.image.width > this.config.thresholds.textureResolution.medium ||
                       texture.image.height > this.config.thresholds.textureResolution.medium)) {

                    // 对于大纹理，添加细节纹理
                    // 这里只是模拟添加细节纹理的过程
                    // 实际实现需要创建或加载细节纹理

                    // 启用mipmap
                    texture.generateMipmaps = true;
                    texture.needsUpdate = true;

                    // 设置各向异性过滤
                    texture.anisotropy = 16;
                  }
                });
              }
            }
          }
        });
      }

      return true;
    } catch (error) {
      Debug.error('SceneOptimizer', '优化细节纹理失败:', error);
      return false;
    }
  }

  /**
   * 优化网格简化
   * @param scene 场景
   * @returns 优化结果Promise
   */
  private async optimizeMeshSimplification(scene: Scene): Promise<boolean> {
    if (!this.config.enableMeshSimplification) {
      return false;
    }

    try {
      // 获取场景中的所有实体
      const entities = scene.getEntities();

      // 遍历所有实体
      for (const entity of entities) {
        // 检查是否有Transform组件
        const transform = entity.getComponent('Transform') as any as Transform;
        if (!transform) continue;

        // 获取Three.js对象
        const object3D = transform.getObject3D();
        if (!object3D) continue;

        // 收集和简化网格
        object3D.traverse((obj) => {
          if (obj instanceof THREE.Mesh) {
            const geometry = obj.geometry;

            // 检查几何体是否适合简化
            if (geometry && geometry.attributes.position) {
              const vertexCount = geometry.attributes.position.count;

              // 如果顶点数量超过阈值，考虑简化
              if (vertexCount > 10000) {
                // 这里只是模拟网格简化的过程
                // 实际实现需要使用网格简化算法

                // 如果没有LOD组件，添加一个
                if (!entity.hasComponent('LODComponent') && this.lodSystem) {
                  // 计算包围球半径
                  geometry.computeBoundingSphere();
                  const radius = geometry.boundingSphere?.radius || 1;

                  // 根据几何体大小设置LOD距离
                  const distances = [
                    radius * 5,  // 高细节
                    radius * 15, // 中等细节
                    radius * 30, // 低细节
                    radius * 50  // 极低细节
                  ];

                  // 自动生成LOD
                  this.lodSystem.autoGenerateLOD(entity, obj, distances);
                }
              }
            }
          }
        });
      }

      return true;
    } catch (error) {
      Debug.error('SceneOptimizer', '优化网格简化失败:', error);
      return false;
    }
  }

  /**
   * 优化粒子系统
   * @param scene 场景
   * @returns 优化结果Promise
   */
  private async optimizeParticleSystem(scene: Scene): Promise<boolean> {
    if (!this.config.enableParticleOptimization) {
      return false;
    }

    try {
      // 获取场景中的所有实体
      const entities = scene.getEntities();

      // 粒子系统计数
      let particleSystemCount = 0;
      let totalParticleCount = 0;

      // 遍历所有实体
      for (const entity of entities) {
        // 检查是否有粒子系统组件
        // 注意：这里假设有一个ParticleSystem组件
        const particleSystem = entity.getComponent('ParticleSystem') as any as any;
        if (particleSystem) {
          particleSystemCount++;

          // 获取粒子数量
          const particleCount = (particleSystem as any).getParticleCount?.() || 0;
          totalParticleCount += particleCount;

          // 如果粒子数量超过阈值，减少粒子数量
          if (particleCount > this.config.thresholds.particles.medium) {
            // 减少粒子数量
            const reductionFactor = this.config.thresholds.particles.medium / particleCount;
            (particleSystem as any).setParticleCount?.(Math.floor(particleCount * reductionFactor));
          }
        }
      }

      return true;
    } catch (error) {
      Debug.error('SceneOptimizer', '优化粒子系统失败:', error);
      return false;
    }
  }

  /**
   * 优化动画系统
   * @param scene 场景
   * @returns 优化结果Promise
   */
  private async optimizeAnimationSystem(scene: Scene): Promise<boolean> {
    if (!this.config.enableAnimationOptimization) {
      return false;
    }

    try {
      // 获取场景中的所有实体
      const entities = scene.getEntities();

      // 动画计数
      let animationCount = 0;

      // 遍历所有实体
      for (const entity of entities) {
        // 检查是否有动画组件
        // 注意：这里假设有一个Animation组件
        const animation = entity.getComponent('Animation') as any as any;
        if (animation) {
          animationCount++;

          // 获取Transform组件
          const transform = entity.getComponent('Transform') as any as Transform;
          if (!transform) continue;

          // 获取Three.js对象
          const object3D = transform.getObject3D();
          if (!object3D) continue;

          // 计算到相机的距离
          const camera = scene.getActiveCamera();
          if (camera) {
            const cameraPosition = camera.getPosition();
            const objectPosition = transform.getPosition();

            // 计算距离
            const distance = Math.sqrt(
              Math.pow(cameraPosition.x - objectPosition.x, 2) +
              Math.pow(cameraPosition.y - objectPosition.y, 2) +
              Math.pow(cameraPosition.z - objectPosition.z, 2)
            );

            // 根据距离调整动画更新频率
            if (distance > 50) {
              // 远距离对象降低动画更新频率
              (animation as any).setUpdateFrequency?.(0.5); // 每两帧更新一次
            } else if (distance > 20) {
              // 中等距离对象稍微降低动画更新频率
              (animation as any).setUpdateFrequency?.(0.75); // 每4帧更新3次
            } else {
              // 近距离对象保持正常更新频率
              (animation as any).setUpdateFrequency?.(1.0); // 每帧更新
            }
          }
        }
      }

      return true;
    } catch (error) {
      Debug.error('SceneOptimizer', '优化动画系统失败:', error);
      return false;
    }
  }

  /**
   * 获取最后一次分析结果
   * @returns 分析结果
   */
  public getLastAnalysisResult(): SceneAnalysisResult | null {
    return this.lastAnalysisResult;
  }
}
