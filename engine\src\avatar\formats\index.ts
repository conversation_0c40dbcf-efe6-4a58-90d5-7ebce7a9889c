/**
 * 数字人格式模块导出
 */

// 数字人包格式
export {
  DIGITAL_HUMAN_PACKAGE_VERSION,
  DIGITAL_HUMAN_PACKAGE_EXTENSION,
  DIGITAL_HUMAN_PACKAGE_MIME_TYPE,
  PACKAGE_CONSTANTS
} from './DigitalHumanPackage';

export type {
  DigitalHumanPackageHeader,
  DigitalHumanMetadata,
  GeometryInfo,
  TextureInfo,
  SkeletonInfo,
  AnimationInfo,
  ClothingInfo,
  ExpressionInfo,
  DigitalHumanPackageContent,
  PackageManifest,
  PackageFile,
  PackageValidationResult,
  PackageBuildOptions,
  PackageParseOptions
} from './DigitalHumanPackage';

// 数字人包管理器
export { DigitalHumanPackageManager } from './DigitalHumanPackageManager';
export type { PackageManagerConfig } from './DigitalHumanPackageManager';
