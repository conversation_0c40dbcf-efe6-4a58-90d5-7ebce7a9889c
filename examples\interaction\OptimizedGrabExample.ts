/**
 * 优化的抓取系统示例
 * 演示如何使用优化的抓取系统处理大量对象的抓取
 */
import { World, Entity, Scene, Transform, Camera } from '../../engine/src/core';
import {
  OptimizedGrabSystem,
  GrabbableComponent,
  GrabberComponent,
  GrabType,
  Hand,
  ThrowableComponent,
  GestureGrabComponent,
  XRGrabComponent
} from '../../engine/src/interaction';
import { InputSystem, KeyCode, MouseButton } from '../../engine/src/input';
import { PhysicsSystem, PhysicsBodyComponent, BodyType, ColliderType } from '../../engine/src/physics';
import { Debug } from '../../engine/src/utils/Debug';
import { Vector3, BoxGeometry, SphereGeometry, MeshBasicMaterial, Mesh, Color } from 'three';
import { RenderingSystem } from '../../engine/src/rendering/RenderingSystem';
import { MeshComponent } from '../../engine/src/rendering/components/MeshComponent';

/**
 * 优化的抓取示例
 */
export class OptimizedGrabExample {
  /** 世界 */
  private world: World;

  /** 引擎 */
  private engine: any;

  /** 场景 */
  private scene: Scene;

  /** 输入系统 */
  private inputSystem: InputSystem;

  /** 物理系统 */
  private physicsSystem: PhysicsSystem;

  /** 渲染系统 */
  private renderingSystem: RenderingSystem;

  /** 抓取系统 */
  private grabSystem: OptimizedGrabSystem;

  /** 玩家实体 */
  private playerEntity: Entity;

  /** 可抓取实体列表 */
  private grabbableEntities: Entity[] = [];

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 对象数量 */
  private objectCount: number = 100;

  /** 是否启用VR */
  private enableVR: boolean = false;

  /** 是否启用手势 */
  private enableGesture: boolean = true;

  /** 是否启用网络 */
  private enableNetwork: boolean = false;

  /**
   * 构造函数
   * @param engine 引擎实例
   */
  constructor(engine: any) {
    this.engine = engine;
    this.world = engine.getWorld();
  }

  /**
   * 初始化示例
   */
  async initialize(): Promise<void> {
    // 创建场景
    this.scene = new Scene(this.world, {
      name: '优化的抓取示例场景'
    });

    // 创建输入系统
    this.inputSystem = new InputSystem();

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem();

    // 创建渲染系统
    this.renderingSystem = new RenderingSystem();

    // 创建优化的抓取系统
    this.grabSystem = new OptimizedGrabSystem(this.world, {
      debug: true,
      enablePhysicsGrab: true,
      enableNetworkSync: this.enableNetwork,
      enableGestureGrab: this.enableGesture,
      enableXRGrab: this.enableVR,
      enableSpatialPartitioning: true,
      enableObjectPool: true,
      enableMultithreading: false,
      enableLOD: true,
      updateFrequency: 1
    });

    // 添加系统到世界
    this.world.addSystem(this.inputSystem);
    this.world.addSystem(this.physicsSystem);
    this.world.addSystem(this.renderingSystem);
    this.world.addSystem(this.grabSystem);

    // 创建相机
    const cameraEntity = new Entity(this.world);
    cameraEntity.addComponent(new Transform({
      position: new Vector3(0, 5, 10),
      rotation: new Vector3(-0.3, 0, 0),
      scale: new Vector3(1, 1, 1)
    }));
    cameraEntity.addComponent(new Camera({
      fov: 60,
      near: 0.1,
      far: 1000,
      aspect: window.innerWidth / window.innerHeight
    }));
    this.scene.addEntity(cameraEntity);

    // 创建玩家
    this.playerEntity = this.createPlayer();

    // 创建地面
    this.createGround();

    // 创建可抓取对象
    this.createGrabbableObjects();

    // 设置为已初始化
    this.initialized = true;

    // 开始游戏循环
    this.gameLoop();

    Debug.log('优化的抓取示例', '初始化完成');
  }

  /**
   * 创建玩家
   * @returns 玩家实体
   */
  private createPlayer(): Entity {
    // 创建玩家实体
    const playerEntity = new Entity(this.world);

    // 添加变换组件
    playerEntity.addComponent(new Transform({
      position: new Vector3(0, 1.8, 0),
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(1, 1, 1)
    }));

    // 添加抓取者组件
    playerEntity.addComponent(new GrabberComponent(playerEntity, {
      maxGrabDistance: 5.0,
      onGrab: (grabber, grabbed, hand) => {
        Debug.log('优化的抓取示例', `玩家抓取了对象 ${grabbed.id}，使用${hand === Hand.LEFT ? '左手' : '右手'}`);
      },
      onRelease: (grabber, released, hand) => {
        Debug.log('优化的抓取示例', `玩家释放了对象 ${released.id}，使用${hand === Hand.LEFT ? '左手' : '右手'}`);
      }
    }));

    // 如果启用手势，添加手势抓取组件
    if (this.enableGesture) {
      playerEntity.addComponent(new GestureGrabComponent(playerEntity, {
        enabled: true,
        grabGestureType: 'pinch',
        releaseGestureType: 'release',
        rotateGestureType: 'rotate',
        scaleGestureType: 'pinch',
        grabDistance: 3.0,
        useRaycasting: true
      }));
    }

    // 如果启用VR，添加XR抓取组件
    if (this.enableVR) {
      playerEntity.addComponent(new XRGrabComponent(playerEntity, {
        enabled: true,
        controllerType: 'right',
        grabButtonIndex: 0,
        releaseButtonIndex: 0,
        grabDistance: 0.1,
        useRaycasting: true,
        useHapticFeedback: true
      }));
    }

    // 添加到场景
    this.scene.addEntity(playerEntity);

    return playerEntity;
  }

  /**
   * 创建地面
   */
  private createGround(): void {
    // 创建地面实体
    const groundEntity = new Entity(this.world);

    // 添加变换组件
    groundEntity.addComponent(new Transform({
      position: new Vector3(0, -0.5, 0),
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(50, 1, 50)
    }));

    // 添加物理体组件
    groundEntity.addComponent(new PhysicsBodyComponent(groundEntity, {
      bodyType: BodyType.STATIC,
      mass: 0,
      colliderType: ColliderType.BOX,
      colliderSize: new Vector3(50, 1, 50)
    }));

    // 创建几何体和材质
    const geometry = new BoxGeometry(50, 1, 50);
    const material = new MeshBasicMaterial({ color: 0x808080 });
    const mesh = new Mesh(geometry, material);

    // 添加网格组件
    groundEntity.addComponent(new MeshComponent(groundEntity, {
      mesh: mesh
    }));

    // 添加到场景
    this.scene.addEntity(groundEntity);
  }

  /**
   * 创建可抓取对象
   */
  private createGrabbableObjects(): void {
    // 创建多个可抓取对象
    for (let i = 0; i < this.objectCount; i++) {
      // 随机位置
      const position = new Vector3(
        (Math.random() - 0.5) * 20,
        Math.random() * 5 + 1,
        (Math.random() - 0.5) * 20
      );

      // 随机抓取类型
      const grabTypes = [GrabType.DIRECT, GrabType.DISTANCE, GrabType.SPRING];
      const grabType = grabTypes[Math.floor(Math.random() * grabTypes.length)];

      // 随机颜色
      const color = new Color(Math.random(), Math.random(), Math.random()).getHex();

      // 创建可抓取对象
      const entity = this.createGrabbableObject(position, grabType, color);

      // 添加到列表
      this.grabbableEntities.push(entity);
    }
  }

  /**
   * 创建可抓取对象
   * @param position 位置
   * @param grabType 抓取类型
   * @param color 颜色
   * @returns 实体
   */
  private createGrabbableObject(position: Vector3, grabType: GrabType, color: number): Entity {
    // 创建实体
    const entity = new Entity(this.world);

    // 添加变换组件
    entity.addComponent(new Transform({
      position: position,
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(1, 1, 1)
    }));

    // 添加可抓取组件
    entity.addComponent(new GrabbableComponent(entity, {
      grabType: grabType,
      allowedHands: [Hand.ANY],
      grabDistance: 1.0,
      onGrab: (entity, grabber) => {
        Debug.log('优化的抓取示例', `对象 ${entity.id} 被抓取`);
      },
      onRelease: (entity, grabber) => {
        Debug.log('优化的抓取示例', `对象 ${entity.id} 被释放`);
      }
    }));

    // 添加可抛掷组件
    entity.addComponent(new ThrowableComponent(entity, {
      throwable: true,
      throwType: 'physics',
      throwForceMultiplier: 1.5,
      throwAngularForceMultiplier: 1.0,
      throwVelocitySmoothingFactor: 0.5,
      throwHistoryLength: 5,
      preserveRotationOnThrow: true,
      onThrow: (entity, velocity, angularVelocity) => {
        Debug.log('优化的抓取示例', `对象 ${entity.id} 被抛掷，速度=${velocity.toArray()}`);
      }
    }));

    // 添加物理体组件
    entity.addComponent(new PhysicsBodyComponent(entity, {
      bodyType: BodyType.DYNAMIC,
      mass: 1.0,
      colliderType: ColliderType.SPHERE,
      colliderRadius: 0.5,
      useGravity: true,
      friction: 0.5,
      restitution: 0.7
    }));

    // 创建几何体和材质
    const geometry = new SphereGeometry(0.5, 32, 32);
    const material = new MeshBasicMaterial({ color: color });
    const mesh = new Mesh(geometry, material);

    // 添加网格组件
    entity.addComponent(new MeshComponent(entity, {
      mesh: mesh
    }));

    // 添加到场景
    this.scene.addEntity(entity);

    return entity;
  }

  /**
   * 游戏循环
   */
  private gameLoop(): void {
    // 如果未初始化，则返回
    if (!this.initialized) return;

    // 处理输入
    this.handleInput();

    // 更新世界
    this.world.update();

    // 渲染场景
    this.render();

    // 请求下一帧
    requestAnimationFrame(() => this.gameLoop());
  }

  /**
   * 处理输入
   */
  private handleInput(): void {
    // 获取抓取者组件
    const grabberComponent = this.playerEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    if (!grabberComponent) return;

    // 检测左键点击 - 左手抓取
    if (this.inputSystem.isMouseButtonPressed(MouseButton.LEFT)) {
      // 获取最近的可抓取对象
      const nearestEntity = this.getNearestGrabbableEntity();
      if (nearestEntity) {
        // 尝试抓取
        grabberComponent.grab(nearestEntity, Hand.LEFT);
      }
    }

    // 检测右键点击 - 右手抓取
    if (this.inputSystem.isMouseButtonPressed(MouseButton.RIGHT)) {
      // 获取最近的可抓取对象
      const nearestEntity = this.getNearestGrabbableEntity();
      if (nearestEntity) {
        // 尝试抓取
        grabberComponent.grab(nearestEntity, Hand.RIGHT);
      }
    }

    // 检测Q键 - 左手释放
    if (this.inputSystem.isKeyPressed(KeyCode.KEY_Q)) {
      grabberComponent.release(Hand.LEFT);
    }

    // 检测E键 - 右手释放
    if (this.inputSystem.isKeyPressed(KeyCode.KEY_E)) {
      grabberComponent.release(Hand.RIGHT);
    }

    // 检测R键 - 重置场景
    if (this.inputSystem.isKeyPressed(KeyCode.KEY_R)) {
      this.resetScene();
    }

    // 检测+键 - 增加对象数量
    if (this.inputSystem.isKeyPressed(KeyCode.KEY_PLUS) || this.inputSystem.isKeyPressed(KeyCode.KEY_EQUALS)) {
      this.addMoreObjects(10);
    }

    // 检测-键 - 减少对象数量
    if (this.inputSystem.isKeyPressed(KeyCode.KEY_MINUS)) {
      this.removeObjects(10);
    }
  }

  /**
   * 获取最近的可抓取实体
   * @returns 最近的可抓取实体
   */
  private getNearestGrabbableEntity(): Entity | undefined {
    // 获取玩家位置
    const playerTransform = this.playerEntity.getComponent<Transform>('Transform');
    if (!playerTransform) return undefined;

    // 获取玩家位置
    const playerPosition = playerTransform.getWorldPosition(new Vector3());

    // 查找最近的可抓取实体
    let nearestEntity: Entity | undefined = undefined;
    let nearestDistance = Infinity;

    for (const entity of this.grabbableEntities) {
      // 获取实体位置
      const entityTransform = entity.getComponent<Transform>('Transform');
      if (!entityTransform) continue;

      // 计算距离
      const distance = playerPosition.distanceTo(entityTransform.getWorldPosition(new Vector3()));

      // 如果距离小于最近距离，则更新最近实体
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestEntity = entity;
      }
    }

    // 如果最近距离大于最大抓取距离，则返回undefined
    const grabberComponent = this.playerEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
    if (grabberComponent && nearestDistance > grabberComponent.maxGrabDistance) {
      return undefined;
    }

    return nearestEntity;
  }

  /**
   * 渲染场景
   */
  private render(): void {
    // 这里应该调用渲染系统渲染场景
    // 简单起见，这里不实现具体的渲染逻辑
  }

  /**
   * 重置场景
   */
  private resetScene(): void {
    // 移除所有可抓取对象
    for (const entity of this.grabbableEntities) {
      this.scene.removeEntity(entity);
    }

    // 清空可抓取对象列表
    this.grabbableEntities = [];

    // 重新创建可抓取对象
    this.createGrabbableObjects();

    Debug.log('优化的抓取示例', '场景已重置');
  }

  /**
   * 添加更多对象
   * @param count 添加数量
   */
  private addMoreObjects(count: number): void {
    // 更新对象数量
    this.objectCount += count;

    // 创建新对象
    for (let i = 0; i < count; i++) {
      // 随机位置
      const position = new Vector3(
        (Math.random() - 0.5) * 20,
        Math.random() * 5 + 1,
        (Math.random() - 0.5) * 20
      );

      // 随机抓取类型
      const grabTypes = [GrabType.DIRECT, GrabType.DISTANCE, GrabType.SPRING];
      const grabType = grabTypes[Math.floor(Math.random() * grabTypes.length)];

      // 随机颜色
      const color = new Color(Math.random(), Math.random(), Math.random()).getHex();

      // 创建可抓取对象
      const entity = this.createGrabbableObject(position, grabType, color);

      // 添加到列表
      this.grabbableEntities.push(entity);
    }

    Debug.log('优化的抓取示例', `添加了 ${count} 个对象，当前总数: ${this.objectCount}`);
  }

  /**
   * 移除对象
   * @param count 移除数量
   */
  private removeObjects(count: number): void {
    // 确保不会移除所有对象
    count = Math.min(count, this.grabbableEntities.length);

    // 更新对象数量
    this.objectCount -= count;

    // 移除对象
    for (let i = 0; i < count; i++) {
      const entity = this.grabbableEntities.pop();
      if (entity) {
        this.scene.removeEntity(entity);
      }
    }

    Debug.log('优化的抓取示例', `移除了 ${count} 个对象，当前总数: ${this.objectCount}`);
  }

  /**
   * 销毁示例
   */
  destroy(): void {
    // 移除所有可抓取对象
    for (const entity of this.grabbableEntities) {
      this.scene.removeEntity(entity);
    }

    // 移除玩家
    this.scene.removeEntity(this.playerEntity);

    // 移除系统
    this.world.removeSystem(this.grabSystem);
    this.world.removeSystem(this.physicsSystem);
    this.world.removeSystem(this.inputSystem);
    this.world.removeSystem(this.renderingSystem);

    // 重置状态
    this.initialized = false;
    this.grabbableEntities = [];

    Debug.log('优化的抓取示例', '已销毁');
  }
}
