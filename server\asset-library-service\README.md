# 资源库微服务 (Asset Library Service)

基于NestJS构建的独立资源库微服务，支持3D资产管理、分类、搜索、版本控制和分布式存储。

## 功能特性

### 核心功能
- ✅ **资产管理**: 完整的3D资产CRUD操作
- ✅ **分类系统**: 树形分类结构，支持多级分类
- ✅ **标签系统**: 灵活的标签管理和关联
- ✅ **版本控制**: 资产版本管理和历史记录
- ✅ **搜索功能**: 基于Elasticsearch的全文搜索
- ✅ **文件存储**: 基于MinIO的分布式对象存储
- ✅ **缓存优化**: Redis缓存提升性能
- ✅ **权限控制**: 基于JWT的认证和RBAC授权

### 高级特性
- 🔍 **智能搜索**: 支持模糊搜索、自动补全、高亮显示
- 📊 **统计分析**: 下载量、浏览量、评分统计
- 🏷️ **元数据管理**: 丰富的资产元数据支持
- 🔄 **实时同步**: 数据库与搜索引擎实时同步
- 📈 **性能监控**: 完整的健康检查和监控指标
- 🛡️ **安全防护**: 限流、CORS、文件类型验证

## 技术栈

- **框架**: NestJS + TypeScript
- **数据库**: PostgreSQL + TypeORM
- **缓存**: Redis
- **搜索**: Elasticsearch
- **存储**: MinIO (S3兼容)
- **认证**: JWT + Passport
- **文档**: Swagger/OpenAPI
- **容器**: Docker + Docker Compose

## 快速开始

### 环境要求
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 13+
- Redis 6+
- Elasticsearch 8+
- MinIO

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 启动开发环境
```bash
# 启动基础设施服务
docker-compose up -d postgres redis elasticsearch minio

# 启动开发服务器
npm run start:dev
```

### 构建生产版本
```bash
# 构建应用
npm run build

# 启动生产服务
npm run start:prod
```

## API 文档

启动服务后访问 Swagger 文档：
- 开发环境: http://localhost:8003/api/docs
- 生产环境: https://your-domain/api/docs

### 主要API端点

#### 资产管理
- `GET /assets` - 获取资产列表
- `GET /assets/:id` - 获取资产详情
- `POST /assets` - 创建资产
- `PATCH /assets/:id` - 更新资产
- `DELETE /assets/:id` - 删除资产

#### 搜索功能
- `GET /assets/search?q=keyword` - 搜索资产
- `GET /assets/popular` - 热门资产
- `GET /assets/latest` - 最新资产
- `GET /assets/recommended` - 推荐资产

#### 文件操作
- `POST /assets/:id/upload` - 上传资产文件
- `POST /assets/:id/download` - 下载资产文件

## 数据库设计

### 核心实体

#### Asset (资产)
```typescript
{
  id: string;           // UUID主键
  name: string;         // 资产名称
  description: string;  // 描述
  type: AssetType;      // 资产类型
  status: AssetStatus;  // 状态
  license: AssetLicense; // 许可证
  filePath: string;     // 文件路径
  fileSize: number;     // 文件大小
  metadata: object;     // 元数据
  downloadCount: number; // 下载次数
  rating: number;       // 评分
  category: Category;   // 分类
  tags: Tag[];          // 标签
  versions: AssetVersion[]; // 版本
  creator: User;        // 创建者
}
```

#### Category (分类)
```typescript
{
  id: string;
  name: string;
  slug: string;
  description: string;
  parent: Category;
  children: Category[];
  assets: Asset[];
}
```

#### Tag (标签)
```typescript
{
  id: string;
  name: string;
  type: TagType;
  color: string;
  usageCount: number;
  assets: Asset[];
}
```

## 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t asset-library-service .

# 运行容器
docker run -d \
  --name asset-library \
  -p 8003:8003 \
  -e NODE_ENV=production \
  asset-library-service
```

### Docker Compose部署
```bash
# 启动完整服务栈
docker-compose -f docker-compose.production.yml up -d
```

### Kubernetes部署
```bash
# 应用Kubernetes配置
kubectl apply -f k8s/
```

## 监控和运维

### 健康检查
- `GET /health` - 服务健康状态
- `GET /health/ready` - 就绪检查
- `GET /health/live` - 存活检查

### 监控指标
- 服务响应时间
- 请求成功率
- 数据库连接状态
- 缓存命中率
- 存储空间使用

### 日志管理
- 结构化JSON日志
- 不同级别日志分离
- 错误堆栈追踪
- 性能指标记录

## 开发指南

### 项目结构
```
src/
├── common/           # 公共模块
│   ├── services/     # 公共服务
│   └── controllers/  # 公共控制器
├── config/           # 配置文件
├── modules/          # 业务模块
│   ├── assets/       # 资产模块
│   ├── categories/   # 分类模块
│   ├── tags/         # 标签模块
│   ├── versions/     # 版本模块
│   ├── auth/         # 认证模块
│   ├── search/       # 搜索模块
│   └── upload/       # 上传模块
├── app.module.ts     # 应用主模块
└── main.ts           # 应用入口
```

### 代码规范
- 使用TypeScript严格模式
- 遵循NestJS最佳实践
- 使用ESLint和Prettier
- 编写单元测试和集成测试

### 测试
```bash
# 运行单元测试
npm run test

# 运行集成测试
npm run test:e2e

# 生成测试覆盖率
npm run test:coverage
```

## 性能优化

### 缓存策略
- Redis缓存热点数据
- 分层缓存架构
- 缓存失效策略
- 缓存预热机制

### 数据库优化
- 索引优化
- 查询优化
- 连接池配置
- 读写分离

### 搜索优化
- Elasticsearch索引优化
- 搜索结果缓存
- 分页优化
- 聚合查询优化

## 安全考虑

### 认证授权
- JWT Token认证
- RBAC权限控制
- API密钥管理
- 会话管理

### 数据安全
- 数据加密传输
- 敏感数据加密存储
- SQL注入防护
- XSS防护

### 网络安全
- CORS配置
- 限流保护
- DDoS防护
- 安全头设置

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查数据库配置和网络连接
2. **Redis连接超时**: 检查Redis服务状态和配置
3. **MinIO上传失败**: 检查存储桶权限和网络配置
4. **Elasticsearch搜索异常**: 检查索引状态和映射配置

### 日志分析
```bash
# 查看应用日志
docker logs asset-library-service

# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/combined.log
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 编写测试用例
5. 提交Pull Request

## 许可证

MIT License

## 联系方式

- 项目维护者: DL Engine Team
- 邮箱: <EMAIL>
- 文档: https://docs.dlengine.com
