/**
 * 动画重定向器
 * 用于将一个骨骼结构的动画应用到另一个骨骼结构上
 */
import * as THREE from 'three';
/**
 * 骨骼映射
 */
export interface BoneMapping {
    /** 源骨骼名称 */
    source: string;
    /** 目标骨骼名称 */
    target: string;
    /** 旋转偏移 */
    rotationOffset?: THREE.Quaternion;
    /** 位置缩放 */
    positionScale?: number;
    /** 是否镜像 */
    mirror?: boolean;
}
/**
 * 重定向配置
 */
export interface RetargetConfig {
    /** 骨骼映射 */
    boneMapping: BoneMapping[];
    /** 是否保留位置轨道 */
    preservePositionTracks?: boolean;
    /** 是否保留缩放轨道 */
    preserveScaleTracks?: boolean;
    /** 是否规范化旋转 */
    normalizeRotations?: boolean;
    /** 是否调整根骨骼高度 */
    adjustRootHeight?: boolean;
    /** 是否调整骨骼长度 */
    adjustBoneLength?: boolean;
    /** 是否使用四元数球面线性插值 */
    useQuaternionSlerp?: boolean;
    /** 是否自动创建骨骼映射 */
    autoCreateMapping?: boolean;
    /** 是否忽略未映射的骨骼 */
    ignoreUnmappedBones?: boolean;
}
/**
 * 重定向事件类型
 */
export declare enum RetargetEventType {
    /** 重定向开始 */
    RETARGET_START = "retargetStart",
    /** 重定向完成 */
    RETARGET_COMPLETE = "retargetComplete",
    /** 重定向错误 */
    RETARGET_ERROR = "retargetError"
}
/**
 * 动画重定向器
 */
export declare class AnimationRetargeter {
    /** 源骨骼 */
    private sourceSkeleton;
    /** 目标骨骼 */
    private targetSkeleton;
    /** 重定向配置 */
    private config;
    /** 骨骼映射缓存 */
    private boneMappingCache;
    /** 骨骼索引映射缓存 */
    private boneIndexMappingCache;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否已初始化 */
    private initialized;
    /**
     * 构造函数
     * @param sourceSkeleton 源骨骼
     * @param targetSkeleton 目标骨骼
     * @param config 重定向配置
     */
    constructor(sourceSkeleton: THREE.Skeleton | THREE.Bone[], targetSkeleton: THREE.Skeleton | THREE.Bone[], config: RetargetConfig);
    /**
     * 初始化
     */
    private initialize;
    /**
     * 创建骨骼映射缓存
     */
    private createBoneMappingCache;
    /**
     * 自动创建骨骼映射
     */
    autoCreateBoneMapping(): void;
    /**
     * 获取源骨骼数组
     * @returns 骨骼数组
     */
    private getSourceBones;
    /**
     * 获取目标骨骼数组
     * @returns 骨骼数组
     */
    private getTargetBones;
    /**
     * 重定向动画片段
     * @param clip 动画片段
     * @returns 重定向后的动画片段
     */
    retarget(clip: THREE.AnimationClip): THREE.AnimationClip;
    /**
     * 重定向旋转轨道
     * @param track 旋转轨道
     * @param sourceBoneName 源骨骼名称
     * @param targetBoneName 目标骨骼名称
     * @returns 重定向后的旋转轨道
     */
    private retargetRotationTrack;
    /**
     * 重定向位置轨道
     * @param track 位置轨道
     * @param sourceBoneName 源骨骼名称
     * @param targetBoneName 目标骨骼名称
     * @returns 重定向后的位置轨道
     */
    private retargetPositionTrack;
    /**
     * 重定向缩放轨道
     * @param track 缩放轨道
     * @param sourceBoneName 源骨骼名称
     * @param targetBoneName 目标骨骼名称
     * @returns 重定向后的缩放轨道
     */
    private retargetScaleTrack;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    addEventListener(type: RetargetEventType, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    removeEventListener(type: RetargetEventType, callback: (data: any) => void): void;
    /**
     * 获取配置
     * @returns 重定向配置
     */
    getConfig(): RetargetConfig;
}
