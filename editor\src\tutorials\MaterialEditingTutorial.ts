/**
 * 材质编辑教程
 * 引导用户学习如何创建和编辑材质
 */
import { Tutorial } from '../services/TutorialService';
import i18n from '../i18n';

export const MaterialEditingTutorial: Tutorial = {
  id: 'material-editing',
  title: i18n.t('tutorials.materialEditing.title') as string,
  description: i18n.t('tutorials.materialEditing.description') as string,
  category: 'materials',
  difficulty: 'intermediate',
  duration: 15,
  prerequisites: ['editor-basics'],
  tags: ['材质', '着色器', 'PBR', '纹理'],
  steps: [
    // 步骤1：介绍
    {
      id: 'introduction',
      title: i18n.t('tutorials.materialEditing.steps.introduction.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.introduction.description') as string,
      nextButtonText: i18n.t('tutorials.next') as string,
      skipButtonText: i18n.t('tutorials.skip') as string
    },
    
    // 步骤2：创建新材质
    {
      id: 'create-material',
      title: i18n.t('tutorials.materialEditing.steps.createMaterial.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.createMaterial.description') as string,
      targetElement: 'project-panel',
      highlightElement: true,
      action: 'right-click',
      completionCriteria: 'material-created',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤3：选择材质类型
    {
      id: 'select-material-type',
      title: i18n.t('tutorials.materialEditing.steps.selectMaterialType.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.selectMaterialType.description') as string,
      targetElement: 'material-type-dropdown',
      highlightElement: true,
      action: 'select',
      completionCriteria: 'material-type-selected',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤4：设置基本属性
    {
      id: 'set-basic-properties',
      title: i18n.t('tutorials.materialEditing.steps.setBasicProperties.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.setBasicProperties.description') as string,
      targetElement: 'material-basic-properties',
      highlightElement: true,
      action: 'edit',
      completionCriteria: 'basic-properties-set',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },
    
    // 步骤5：添加纹理
    {
      id: 'add-textures',
      title: i18n.t('tutorials.materialEditing.steps.addTextures.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.addTextures.description') as string,
      targetElement: 'material-textures',
      highlightElement: true,
      action: 'drag-drop',
      completionCriteria: 'textures-added',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤6：调整纹理设置
    {
      id: 'adjust-texture-settings',
      title: i18n.t('tutorials.materialEditing.steps.adjustTextureSettings.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.adjustTextureSettings.description') as string,
      targetElement: 'texture-settings',
      highlightElement: true,
      action: 'edit',
      completionCriteria: 'texture-settings-adjusted',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤7：设置着色器变体
    {
      id: 'set-shader-variants',
      title: i18n.t('tutorials.materialEditing.steps.setShaderVariants.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.setShaderVariants.description') as string,
      targetElement: 'shader-variants',
      highlightElement: true,
      action: 'toggle',
      completionCriteria: 'shader-variants-set',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },
    
    // 步骤8：预览材质
    {
      id: 'preview-material',
      title: i18n.t('tutorials.materialEditing.steps.previewMaterial.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.previewMaterial.description') as string,
      targetElement: 'material-preview',
      highlightElement: true,
      action: 'interact',
      completionCriteria: 'material-previewed',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤9：应用到对象
    {
      id: 'apply-to-object',
      title: i18n.t('tutorials.materialEditing.steps.applyToObject.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.applyToObject.description') as string,
      targetElement: 'scene-hierarchy',
      highlightElement: true,
      action: 'drag-drop',
      completionCriteria: 'material-applied',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤10：保存材质
    {
      id: 'save-material',
      title: i18n.t('tutorials.materialEditing.steps.saveMaterial.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.saveMaterial.description') as string,
      targetElement: 'save-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'material-saved',
      nextButtonText: i18n.t('tutorials.finish') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤11：完成
    {
      id: 'completion',
      title: i18n.t('tutorials.materialEditing.steps.completion.title') as string,
      description: i18n.t('tutorials.materialEditing.steps.completion.description') as string,
      nextButtonText: i18n.t('tutorials.close') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    }
  ]
};

export default MaterialEditingTutorial;
