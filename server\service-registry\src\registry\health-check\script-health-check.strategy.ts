/**
 * 脚本健康检查策略
 */
import { Injectable, Logger } from '@nestjs/common';
import { 
  HealthCheckStrategy, 
  HealthCheckConfig, 
  HealthCheckResult, 
  HealthCheckStatus,
  ScriptHealthCheckConfig
} from './health-check.interface';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

@Injectable()
export class ScriptHealthCheckStrategy implements HealthCheckStrategy {
  private readonly logger = new Logger(ScriptHealthCheckStrategy.name);

  /**
   * 执行脚本健康检查
   * @param config 健康检查配置
   */
  async check(config: HealthCheckConfig): Promise<HealthCheckResult> {
    const scriptConfig = config as ScriptHealthCheckConfig;
    const startTime = Date.now();
    
    try {
      // 构建命令
      const command = this.buildCommand(scriptConfig);
      
      // 执行脚本
      const { stdout, stderr } = await execAsync(command, {
        timeout: scriptConfig.scriptTimeout || scriptConfig.timeout,
      });
      
      const responseTime = Date.now() - startTime;
      const exitCode = 0; // 成功执行的退出码为0
      
      // 检查退出码
      const isExitCodeValid = this.checkExitCode(exitCode, scriptConfig);
      
      // 判断健康状态
      const isHealthy = isExitCodeValid;
      
      const result: HealthCheckResult = {
        status: isHealthy ? HealthCheckStatus.HEALTHY : HealthCheckStatus.UNHEALTHY,
        details: isHealthy 
          ? `脚本检查成功: 退出码 ${exitCode}` 
          : `脚本检查失败: 退出码 ${exitCode}, 期望: ${scriptConfig.expectedExitCode?.join(', ')}`,
        timestamp: new Date(),
        responseTime,
        metadata: {
          exitCode,
          stdout: stdout.trim(),
          stderr: stderr.trim(),
        },
      };
      
      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      // 检查是否是脚本执行错误
      if (error.code !== undefined) {
        const exitCode = error.code;
        
        // 检查退出码
        const isExitCodeValid = this.checkExitCode(exitCode, scriptConfig);
        
        if (isExitCodeValid) {
          return {
            status: HealthCheckStatus.HEALTHY,
            details: `脚本检查成功: 退出码 ${exitCode}`,
            timestamp: new Date(),
            responseTime,
            metadata: {
              exitCode,
              stdout: error.stdout?.trim(),
              stderr: error.stderr?.trim(),
            },
          };
        }
        
        return {
          status: HealthCheckStatus.UNHEALTHY,
          details: `脚本检查失败: 退出码 ${exitCode}, 期望: ${scriptConfig.expectedExitCode?.join(', ')}`,
          timestamp: new Date(),
          responseTime,
          metadata: {
            exitCode,
            stdout: error.stdout?.trim(),
            stderr: error.stderr?.trim(),
          },
        };
      }
      
      this.logger.error(`脚本健康检查失败: ${error.message}`, error.stack);
      
      return {
        status: HealthCheckStatus.UNHEALTHY,
        details: `脚本检查异常: ${error.message}`,
        timestamp: new Date(),
        responseTime,
        metadata: {
          error: error.message,
        },
      };
    }
  }
  
  /**
   * 构建命令
   * @param config 脚本健康检查配置
   */
  private buildCommand(config: ScriptHealthCheckConfig): string {
    // 如果target是脚本路径，则使用target
    if (config.target) {
      const args = config.args ? config.args.join(' ') : '';
      return `${config.target} ${args}`.trim();
    }
    
    // 否则使用script字段
    const args = config.args ? config.args.join(' ') : '';
    return `${config.script} ${args}`.trim();
  }
  
  /**
   * 检查退出码
   * @param exitCode 退出码
   * @param config 健康检查配置
   */
  private checkExitCode(exitCode: number, config: ScriptHealthCheckConfig): boolean {
    // 如果未指定期望的退出码，则默认0为健康
    const expectedExitCode = config.expectedExitCode || [0];
    
    return expectedExitCode.includes(exitCode);
  }
}
