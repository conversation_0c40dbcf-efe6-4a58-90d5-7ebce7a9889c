/**
 * 视觉脚本变量
 * 用于在视觉脚本中存储和共享数据
 */
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 变量选项
 */
export interface VariableOptions {
  /** 变量ID */
  id: string;
  /** 变量名称 */
  name: string;
  /** 变量类型 */
  type: string;
  /** 变量值 */
  value?: any;
  /** 变量描述 */
  description?: string;
  /** 是否为常量 */
  constant?: boolean;
  /** 是否为全局变量 */
  global?: boolean;
}

/**
 * 视觉脚本变量
 * 用于在视觉脚本中存储和共享数据
 */
export class Variable extends EventEmitter {
  /** 变量ID */
  public readonly id: string;
  
  /** 变量名称 */
  private _name: string;
  
  /** 变量类型 */
  private _type: string;
  
  /** 变量值 */
  private _value: any;
  
  /** 变量描述 */
  private _description: string;
  
  /** 是否为常量 */
  private _constant: boolean;
  
  /** 是否为全局变量 */
  private _global: boolean;
  
  /**
   * 创建变量
   * @param options 变量选项
   */
  constructor(options: VariableOptions) {
    super();
    
    this.id = options.id;
    this._name = options.name;
    this._type = options.type;
    this._value = options.value;
    this._description = options.description || '';
    this._constant = options.constant || false;
    this._global = options.global || false;
  }
  
  /**
   * 获取变量名称
   * @returns 变量名称
   */
  public get name(): string {
    return this._name;
  }
  
  /**
   * 设置变量名称
   * @param value 变量名称
   */
  public set name(value: string) {
    if (this._name !== value) {
      const oldValue = this._name;
      this._name = value;
      this.emit('nameChanged', value, oldValue);
    }
  }
  
  /**
   * 获取变量类型
   * @returns 变量类型
   */
  public get type(): string {
    return this._type;
  }
  
  /**
   * 设置变量类型
   * @param value 变量类型
   */
  public set type(value: string) {
    if (this._type !== value) {
      const oldValue = this._type;
      this._type = value;
      this.emit('typeChanged', value, oldValue);
    }
  }
  
  /**
   * 获取变量值
   * @returns 变量值
   */
  public get value(): any {
    return this._value;
  }
  
  /**
   * 设置变量值
   * @param value 变量值
   */
  public set value(value: any) {
    // 如果是常量，不允许修改值
    if (this._constant) {
      console.warn(`无法修改常量变量的值: ${this._name}`);
      return;
    }
    
    if (this._value !== value) {
      const oldValue = this._value;
      this._value = value;
      this.emit('valueChanged', value, oldValue);
    }
  }
  
  /**
   * 获取变量描述
   * @returns 变量描述
   */
  public get description(): string {
    return this._description;
  }
  
  /**
   * 设置变量描述
   * @param value 变量描述
   */
  public set description(value: string) {
    if (this._description !== value) {
      const oldValue = this._description;
      this._description = value;
      this.emit('descriptionChanged', value, oldValue);
    }
  }
  
  /**
   * 获取是否为常量
   * @returns 是否为常量
   */
  public get constant(): boolean {
    return this._constant;
  }
  
  /**
   * 设置是否为常量
   * @param value 是否为常量
   */
  public set constant(value: boolean) {
    if (this._constant !== value) {
      const oldValue = this._constant;
      this._constant = value;
      this.emit('constantChanged', value, oldValue);
    }
  }
  
  /**
   * 获取是否为全局变量
   * @returns 是否为全局变量
   */
  public get global(): boolean {
    return this._global;
  }
  
  /**
   * 设置是否为全局变量
   * @param value 是否为全局变量
   */
  public set global(value: boolean) {
    if (this._global !== value) {
      const oldValue = this._global;
      this._global = value;
      this.emit('globalChanged', value, oldValue);
    }
  }
  
  /**
   * 重置变量值
   */
  public reset(): void {
    // 如果是常量，不允许重置值
    if (this._constant) {
      console.warn(`无法重置常量变量的值: ${this._name}`);
      return;
    }
    
    const oldValue = this._value;
    this._value = undefined;
    this.emit('valueChanged', undefined, oldValue);
    this.emit('reset');
  }
  
  /**
   * 克隆变量
   * @returns 克隆的变量
   */
  public clone(): Variable {
    return new Variable({
      id: this.id,
      name: this._name,
      type: this._type,
      value: this._value,
      description: this._description,
      constant: this._constant,
      global: this._global
    });
  }
  
  /**
   * 序列化变量
   * @returns 序列化数据
   */
  public serialize(): any {
    return {
      id: this.id,
      name: this._name,
      type: this._type,
      value: this._value,
      description: this._description,
      constant: this._constant,
      global: this._global
    };
  }
  
  /**
   * 反序列化变量
   * @param data 序列化数据
   */
  public deserialize(data: any): void {
    if (data.name !== undefined) {
      this._name = data.name;
    }
    
    if (data.type !== undefined) {
      this._type = data.type;
    }
    
    if (data.value !== undefined) {
      this._value = data.value;
    }
    
    if (data.description !== undefined) {
      this._description = data.description;
    }
    
    if (data.constant !== undefined) {
      this._constant = data.constant;
    }
    
    if (data.global !== undefined) {
      this._global = data.global;
    }
  }
}
