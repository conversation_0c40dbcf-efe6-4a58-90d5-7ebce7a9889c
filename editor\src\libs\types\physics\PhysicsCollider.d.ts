/**
 * 物理碰撞器组件
 * 为实体提供碰撞形状
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Component } from '../core/Component';
export declare enum ColliderType {
    BOX = "box",
    SPHERE = "sphere",
    CYLINDER = "cylinder",
    CAPSULE = "capsule",
    CONVEX = "convex",
    TRIMESH = "trimesh",
    PLANE = "plane",
    COMPOUND = "compound"
}
export interface ColliderOptions {
    /** 碰撞器类型 */
    type: ColliderType;
    /** 碰撞器参数 */
    params?: any;
    /** 碰撞器位置偏移 */
    offset?: {
        x: number;
        y: number;
        z: number;
    };
    /** 碰撞器旋转偏移 */
    rotation?: {
        x: number;
        y: number;
        z: number;
    };
    /** 是否为触发器 */
    isTrigger?: boolean;
}
export declare class PhysicsCollider extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 碰撞器类型 */
    private colliderType;
    /** 碰撞器参数 */
    private params;
    /** 碰撞器位置偏移 */
    private offset;
    /** 碰撞器旋转偏移 */
    private rotation;
    /** 碰撞形状 */
    private shapes;
    /** 物理世界 */
    private world;
    /** 是否已初始化 */
    private initialized;
    /** 是否为触发器 */
    private trigger;
    /**
     * 创建物理碰撞器组件
     * @param options 碰撞器选项
     */
    constructor(options: ColliderOptions);
    /**
     * 初始化碰撞器
     * @param world 物理世界
     */
    initialize(world: CANNON.World): void;
    /**
     * 创建碰撞形状
     */
    private createShape;
    /**
     * 创建盒体碰撞形状
     * @returns 盒体碰撞形状
     */
    private createBoxShape;
    /**
     * 创建球体碰撞形状
     * @returns 球体碰撞形状
     */
    private createSphereShape;
    /**
     * 创建圆柱体碰撞形状
     * @returns 圆柱体碰撞形状
     */
    private createCylinderShape;
    /**
     * 创建胶囊体碰撞形状
     * @returns 胶囊体碰撞形状（使用复合形状模拟）
     */
    private createCapsuleShape;
    /**
     * 创建凸包碰撞形状
     * @returns 凸包碰撞形状
     */
    private createConvexShape;
    /**
     * 创建三角网格碰撞形状
     * @returns 三角网格碰撞形状
     */
    private createTrimeshShape;
    /**
     * 创建平面碰撞形状
     * @returns 平面碰撞形状
     */
    private createPlaneShape;
    /**
     * 创建复合碰撞形状
     */
    private createCompoundShape;
    /**
     * 从网格创建碰撞形状
     * @param mesh Three.js网格
     * @returns 碰撞形状
     */
    static createFromMesh(mesh: THREE.Mesh): PhysicsCollider;
    /**
     * 获取碰撞形状
     * @returns 碰撞形状数组
     */
    getShapes(): CANNON.Shape[];
    /**
     * 获取碰撞器类型
     * @returns 碰撞器类型
     */
    getColliderType(): ColliderType;
    /**
     * 获取碰撞器参数
     * @returns 碰撞器参数
     */
    getParams(): any;
    /**
     * 获取碰撞器位置偏移
     * @returns 碰撞器位置偏移
     */
    getOffset(): CANNON.Vec3;
    /**
     * 获取碰撞器旋转偏移
     * @returns 碰撞器旋转偏移
     */
    getRotation(): CANNON.Quaternion;
    /**
     * 检查是否为触发器
     * @returns 是否为触发器
     */
    isTrigger(): boolean;
    /**
     * 设置是否为触发器
     * @param isTrigger 是否为触发器
     */
    setTrigger(isTrigger: boolean): void;
    /**
     * 销毁碰撞器
     */
    dispose(): void;
}
