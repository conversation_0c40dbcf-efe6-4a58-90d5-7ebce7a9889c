# DL引擎渲染服务快速参考

## 🚀 服务概览

**渲染服务 (render-service)** 是DL引擎的核心3D渲染微服务，负责处理场景渲染、图像生成、视频输出和动画制作。

### 基本信息
- **微服务端口**: 3004 (TCP通信)
- **HTTP端口**: 4004 (REST API)
- **技术栈**: NestJS + TypeScript + Three.js
- **数据库**: MySQL + Redis队列
- **容器镜像**: `dl-engine/render-service:latest`

## 📋 快速部署

### Docker单机部署
```bash
# 启动渲染服务
docker run -d \
  --name render-service \
  -p 3004:3004 -p 4004:4004 \
  -e DATABASE_HOST=mysql \
  -e REDIS_HOST=redis \
  -e USER_SERVICE_HOST=user-service \
  -e PROJECT_SERVICE_HOST=project-service \
  -e ASSET_SERVICE_HOST=asset-service \
  -v /opt/renders:/app/renders \
  dl-engine/render-service:latest
```

### Docker Compose部署
```yaml
render-service:
  build: ./server/render-service
  ports:
    - "3004:3004"
    - "4004:4004"
  environment:
    - DATABASE_HOST=mysql
    - REDIS_HOST=redis
    - MAX_CONCURRENT_JOBS=5
  volumes:
    - render-outputs:/app/renders
  depends_on:
    - mysql
    - redis
```

### Kubernetes部署
```bash
# 部署渲染服务
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: render-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: render-service
  template:
    metadata:
      labels:
        app: render-service
    spec:
      containers:
      - name: render-service
        image: dl-engine/render-service:latest
        ports:
        - containerPort: 3004
        - containerPort: 4004
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
EOF
```

## 🔧 环境变量配置

### 必需配置
```bash
# 数据库配置
DATABASE_HOST=mysql
DATABASE_PORT=3306
DATABASE_USERNAME=root
DATABASE_PASSWORD=your-password
DATABASE_NAME=dl_engine

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379

# 微服务依赖
USER_SERVICE_HOST=user-service
USER_SERVICE_PORT=3001
PROJECT_SERVICE_HOST=project-service
PROJECT_SERVICE_PORT=3002
ASSET_SERVICE_HOST=asset-service
ASSET_SERVICE_PORT=3003

# JWT配置
JWT_SECRET=your-secret-key
```

### 可选配置
```bash
# 渲染配置
MAX_RENDER_TIME=300000          # 最大渲染时间(ms)
MAX_CONCURRENT_JOBS=5           # 最大并发任务数
RENDER_OUTPUT_DIR=/app/renders  # 渲染输出目录

# 性能配置
NODE_OPTIONS="--max-old-space-size=4096"
```

## 📡 API快速参考

### 认证
所有API需要JWT令牌认证：
```bash
Authorization: Bearer <your-jwt-token>
```

### 核心端点

#### 创建渲染任务
```http
POST /api/render/jobs
Content-Type: application/json

{
  "name": "场景渲染",
  "type": "image",
  "projectId": "project-uuid",
  "sceneId": "scene-uuid",
  "settings": {
    "width": 1920,
    "height": 1080,
    "quality": 90,
    "format": "png"
  }
}
```

#### 查询任务状态
```http
GET /api/render/jobs/{jobId}
```

#### 获取所有任务
```http
GET /api/render/jobs?status=processing&type=image
```

#### 取消任务
```http
POST /api/render/jobs/{jobId}/cancel
```

#### 删除任务
```http
DELETE /api/render/jobs/{jobId}
```

#### 获取渲染结果
```http
GET /api/render/results/{resultId}
```

## 🔍 健康检查

### 服务健康状态
```bash
# HTTP健康检查
curl http://localhost:4004/api/health

# 响应示例
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "checks": [
    {"name": "database", "status": "fulfilled"},
    {"name": "redis", "status": "fulfilled"},
    {"name": "services", "status": "fulfilled"}
  ]
}
```

### 服务状态检查
```bash
# 检查容器状态
docker ps | grep render-service

# 检查端口监听
netstat -tlnp | grep :3004
netstat -tlnp | grep :4004

# 检查日志
docker logs render-service --tail 50
```

## 📊 监控指标

### 关键指标
- **render_jobs_total** - 渲染任务总数
- **render_duration_seconds** - 渲染耗时分布
- **render_queue_length** - 队列长度
- **render_error_rate** - 错误率

### Prometheus查询示例
```promql
# 渲染任务完成率
rate(render_jobs_total{status="completed"}[5m])

# 平均渲染时间
histogram_quantile(0.95, rate(render_duration_seconds_bucket[5m]))

# 队列积压情况
render_queue_length > 50
```

## 🚨 常见问题

### 服务启动失败
```bash
# 检查依赖服务
docker ps | grep -E "(mysql|redis)"

# 检查环境变量
docker exec render-service env | grep DATABASE

# 查看启动日志
docker logs render-service
```

### 渲染任务失败
```bash
# 检查队列状态
docker exec redis redis-cli LLEN bull:render:waiting
docker exec redis redis-cli LLEN bull:render:failed

# 检查系统资源
docker stats render-service

# 查看错误日志
docker exec render-service tail -f /app/logs/render.log
```

### 性能问题
```bash
# 调整并发数
docker run -e MAX_CONCURRENT_JOBS=3 render-service

# 增加内存限制
docker run --memory=8g render-service

# 检查磁盘空间
df -h /app/renders
```

## 🛠️ 维护操作

### 清理过期数据
```sql
-- 清理30天前的已完成任务
DELETE FROM render_jobs 
WHERE status = 'completed' 
  AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 清理渲染文件
```bash
# 清理7天前的文件
find /app/renders -type f -mtime +7 -delete

# 清理空目录
find /app/renders -type d -empty -delete
```

### 重启服务
```bash
# Docker重启
docker restart render-service

# Kubernetes重启
kubectl rollout restart deployment/render-service
```

## 📚 相关文档

- [渲染服务功能分析](./render-service-analysis.md) - 详细技术架构
- [渲染服务部署指南](./render-service-deployment.md) - 完整部署流程
- [渲染服务故障排除](./render-service-troubleshooting.md) - 问题诊断指南
- [渲染服务API示例](./render-service-api-examples.md) - API使用示例

## 🆘 获取帮助

1. **查看日志**: `docker logs render-service`
2. **检查健康状态**: `curl http://localhost:4004/api/health`
3. **查看API文档**: `http://localhost:4004/api/docs`
4. **联系技术支持**: 提交GitHub Issue

---

*快速参考卡片 - DL引擎渲染服务 v1.0*
