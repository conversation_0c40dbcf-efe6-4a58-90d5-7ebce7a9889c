/**
 * 动态LOD系统
 * 根据性能和视距动态调整LOD级别
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { LODComponent, LODLevel } from './LODComponent';
import { EnhancedLODSystem, EnhancedLODSystemOptions } from './EnhancedLODSystem';
import { PerformanceMonitor, PerformanceMetricType } from '../../utils/PerformanceMonitor';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 动态LOD系统事件类型
 */
export enum DynamicLODSystemEventType {
  /** LOD级别调整 */
  LOD_LEVEL_ADJUSTED = 'lod_level_adjusted',
  /** 性能目标调整 */
  PERFORMANCE_TARGET_ADJUSTED = 'performance_target_adjusted',
  /** 质量级别调整 */
  QUALITY_LEVEL_ADJUSTED = 'quality_level_adjusted'
}

/**
 * 动态LOD系统配置接口
 */
export interface DynamicLODSystemOptions extends EnhancedLODSystemOptions {
  /** 是否启用动态LOD */
  useDynamicLOD?: boolean;
  /** 目标帧率 */
  targetFPS?: number;
  /** 最小帧率 */
  minFPS?: number;
  /** 最大帧率 */
  maxFPS?: number;
  /** 调整灵敏度 */
  adjustmentSensitivity?: number;
  /** 调整间隔（毫秒） */
  adjustmentInterval?: number;
  /** 是否使用平滑过渡 */
  useSmoothTransition?: boolean;
  /** 过渡时间（毫秒） */
  transitionTime?: number;
  /** 质量级别 (0-1) */
  qualityLevel?: number;
  /** 是否使用自动质量调整 */
  useAutoQualityAdjustment?: boolean;
  /** 是否使用距离偏移 */
  useDistanceOffset?: boolean;
  /** 距离偏移系数 */
  distanceOffsetFactor?: number;
}

/**
 * 动态LOD系统
 * 根据性能和视距动态调整LOD级别
 */
export class DynamicLODSystem extends EnhancedLODSystem {

  /** 是否启用动态LOD */
  private useDynamicLOD: boolean;
  /** 目标帧率 */
  private targetFPS: number;
  /** 最小帧率 */
  private minFPS: number;
  /** 最大帧率 */
  private maxFPS: number;
  /** 调整灵敏度 */
  private adjustmentSensitivity: number;
  /** 调整间隔（毫秒） */
  private adjustmentInterval: number;
  /** 上次调整时间 */
  private lastAdjustmentTime: number;
  /** 是否使用平滑过渡 */
  private useSmoothTransition: boolean;
  /** 过渡时间（毫秒） */
  private transitionTime: number;
  /** 质量级别 (0-1) */
  private qualityLevel: number;
  /** 是否使用自动质量调整 */
  private useAutoQualityAdjustment: boolean;
  /** 是否使用距离偏移 */
  private useDistanceOffset: boolean;
  /** 距离偏移系数 */
  private distanceOffsetFactor: number;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor;
  /** 当前距离偏移 */
  private currentDistanceOffset: number;
  /** 动态LOD事件发射器 */
  private dynamicLODEventEmitter: EventEmitter;

  /**
   * 创建动态LOD系统
   * @param options 系统选项
   */
  constructor(options: DynamicLODSystemOptions = {}) {
    super(options);

    // 设置动态LOD选项
    this.useDynamicLOD = options.useDynamicLOD !== undefined ? options.useDynamicLOD : true;
    this.targetFPS = options.targetFPS || 60;
    this.minFPS = options.minFPS || 30;
    this.maxFPS = options.maxFPS || 120;
    this.adjustmentSensitivity = options.adjustmentSensitivity || 0.1;
    this.adjustmentInterval = options.adjustmentInterval || 1000;
    this.lastAdjustmentTime = 0;
    this.useSmoothTransition = options.useSmoothTransition !== undefined ? options.useSmoothTransition : true;
    this.transitionTime = options.transitionTime || 500;
    this.qualityLevel = options.qualityLevel !== undefined ? options.qualityLevel : 0.5;
    this.useAutoQualityAdjustment = options.useAutoQualityAdjustment !== undefined ? options.useAutoQualityAdjustment : true;
    this.useDistanceOffset = options.useDistanceOffset !== undefined ? options.useDistanceOffset : true;
    this.distanceOffsetFactor = options.distanceOffsetFactor || 1.0;
    this.currentDistanceOffset = 0;

    // 获取性能监控器实例
    this.performanceMonitor = PerformanceMonitor.getInstance();

    // 创建动态LOD事件发射器
    this.dynamicLODEventEmitter = new EventEmitter();
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return 'DynamicLODSystem';
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled()) {
      return;
    }

    // 调用父类更新方法
    super.update(deltaTime);

    // 如果启用了动态LOD，则更新动态LOD
    if (this.useDynamicLOD) {
      this.updateDynamicLOD();
    }
  }

  /**
   * 更新动态LOD
   */
  private updateDynamicLOD(): void {
    const currentTime = performance.now();

    // 检查是否需要调整
    if (currentTime - this.lastAdjustmentTime < this.adjustmentInterval) {
      return;
    }

    // 获取当前FPS
    const fpsMetric = this.performanceMonitor.getMetric(PerformanceMetricType.FPS);
    const currentFPS = fpsMetric ? fpsMetric.value : 60;

    // 如果启用了自动质量调整，则根据FPS调整质量级别
    if (this.useAutoQualityAdjustment) {
      this.adjustQualityLevel(currentFPS);
    }

    // 如果启用了距离偏移，则根据质量级别调整距离偏移
    if (this.useDistanceOffset) {
      this.adjustDistanceOffset();
    }

    // 更新上次调整时间
    this.lastAdjustmentTime = currentTime;
  }

  /**
   * 调整质量级别
   * @param currentFPS 当前FPS
   */
  private adjustQualityLevel(currentFPS: number): void {
    // 计算FPS差异
    const fpsDiff = currentFPS - this.targetFPS;
    
    // 计算调整量
    const adjustment = fpsDiff * this.adjustmentSensitivity / (this.maxFPS - this.minFPS);
    
    // 调整质量级别
    const oldQualityLevel = this.qualityLevel;
    this.qualityLevel = Math.max(0, Math.min(1, this.qualityLevel + adjustment));
    
    // 如果质量级别发生变化，则发出事件
    if (this.qualityLevel !== oldQualityLevel) {
      this.dynamicLODEventEmitter.emit(DynamicLODSystemEventType.QUALITY_LEVEL_ADJUSTED, this.qualityLevel, oldQualityLevel);
      Debug.log('DynamicLODSystem', `质量级别调整: ${oldQualityLevel.toFixed(2)} -> ${this.qualityLevel.toFixed(2)}`);
    }
  }

  /**
   * 调整距离偏移
   */
  private adjustDistanceOffset(): void {
    // 根据质量级别计算距离偏移
    // 质量级别越低，距离偏移越大，LOD切换越早
    const oldDistanceOffset = this.currentDistanceOffset;
    this.currentDistanceOffset = (1 - this.qualityLevel) * this.distanceOffsetFactor;
    
    // 如果距离偏移发生变化，则更新所有LOD组件
    if (this.currentDistanceOffset !== oldDistanceOffset) {
      this.updateLODDistances();
    }
  }

  /**
   * 更新LOD距离
   */
  private updateLODDistances(): void {
    // 遍历所有LOD组件
    for (const [, lodComponent] of Array.from(this.lodComponents.entries())) {
      // 获取所有级别
      const levels = lodComponent.getLevels();
      
      // 更新每个级别的距离
      for (const level of levels) {
        // 获取原始距离
        const originalDistance = level.originalDistance !== undefined ? level.originalDistance : level.distance;
        
        // 如果没有保存原始距离，则保存
        if (level.originalDistance === undefined) {
          level.originalDistance = originalDistance;
        }
        
        // 计算新距离
        const newDistance = originalDistance * (1 - this.currentDistanceOffset);
        
        // 设置新距离
        level.distance = newDistance;
      }
    }
  }

  /**
   * 设置目标帧率
   * @param fps 目标帧率
   */
  public setTargetFPS(fps: number): void {
    const oldTargetFPS = this.targetFPS;
    this.targetFPS = Math.max(this.minFPS, Math.min(this.maxFPS, fps));
    
    // 发出事件
    this.dynamicLODEventEmitter.emit(DynamicLODSystemEventType.PERFORMANCE_TARGET_ADJUSTED, this.targetFPS, oldTargetFPS);
    Debug.log('DynamicLODSystem', `目标帧率调整: ${oldTargetFPS} -> ${this.targetFPS}`);
  }

  /**
   * 获取目标帧率
   * @returns 目标帧率
   */
  public getTargetFPS(): number {
    return this.targetFPS;
  }

  /**
   * 设置质量级别
   * @param level 质量级别 (0-1)
   */
  public setQualityLevel(level: number): void {
    const oldQualityLevel = this.qualityLevel;
    this.qualityLevel = Math.max(0, Math.min(1, level));
    
    // 发出事件
    this.dynamicLODEventEmitter.emit(DynamicLODSystemEventType.QUALITY_LEVEL_ADJUSTED, this.qualityLevel, oldQualityLevel);
    Debug.log('DynamicLODSystem', `质量级别设置: ${oldQualityLevel.toFixed(2)} -> ${this.qualityLevel.toFixed(2)}`);
    
    // 调整距离偏移
    if (this.useDistanceOffset) {
      this.adjustDistanceOffset();
    }
  }

  /**
   * 获取质量级别
   * @returns 质量级别 (0-1)
   */
  public getQualityLevel(): number {
    return this.qualityLevel;
  }

  /**
   * 启用/禁用动态LOD
   * @param enabled 是否启用
   */
  public setDynamicLODEnabled(enabled: boolean): void {
    this.useDynamicLOD = enabled;
  }

  /**
   * 获取动态LOD是否启用
   * @returns 是否启用
   */
  public isDynamicLODEnabled(): boolean {
    return this.useDynamicLOD;
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: string, listener: (...args: any[]) => void): void {
    this.dynamicLODEventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: string, listener: (...args: any[]) => void): void {
    this.dynamicLODEventEmitter.off(type, listener);
  }
}
