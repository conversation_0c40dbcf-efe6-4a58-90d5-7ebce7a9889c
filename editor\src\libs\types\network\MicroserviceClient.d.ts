/**
 * 微服务客户端
 * 负责与后端微服务进行通信
 */
import { EventEmitter } from '../utils/EventEmitter';
import { ServiceDiscoveryClient } from './ServiceDiscoveryClient';
/**
 * 微服务客户端配置
 */
export interface MicroserviceClientConfig {
    /** 服务发现客户端 */
    serviceDiscoveryClient?: ServiceDiscoveryClient;
    /** API网关URL */
    apiGatewayUrl?: string;
    /** 是否使用API网关 */
    useApiGateway?: boolean;
    /** 是否使用服务发现 */
    useServiceDiscovery?: boolean;
    /** 请求超时（毫秒） */
    requestTimeout?: number;
    /** 重试次数 */
    retryCount?: number;
    /** 重试间隔（毫秒） */
    retryInterval?: number;
    /** 是否启用请求缓存 */
    enableRequestCache?: boolean;
    /** 请求缓存时间（毫秒） */
    requestCacheTime?: number;
    /** 认证令牌 */
    authToken?: string;
}
/**
 * 请求选项
 */
export interface RequestOptions {
    /** 请求方法 */
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    /** 请求头 */
    headers?: Record<string, string>;
    /** 请求体 */
    body?: any;
    /** 是否使用缓存 */
    useCache?: boolean;
    /** 缓存时间（毫秒） */
    cacheTime?: number;
    /** 超时时间（毫秒） */
    timeout?: number;
    /** 重试次数 */
    retryCount?: number;
    /** 重试间隔（毫秒） */
    retryInterval?: number;
}
/**
 * 微服务客户端
 * 负责与后端微服务进行通信
 */
export declare class MicroserviceClient extends EventEmitter {
    /** 配置 */
    private config;
    /** 服务发现客户端 */
    private serviceDiscoveryClient;
    /** 请求缓存 */
    private requestCache;
    /** 服务实例缓存 */
    private serviceInstanceCache;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建微服务客户端
     * @param config 配置
     */
    constructor(config?: MicroserviceClientConfig);
    /**
     * 初始化客户端
     */
    initialize(): void;
    /**
     * 设置认证令牌
     * @param token 认证令牌
     */
    setAuthToken(token: string): void;
    /**
     * 发送请求到服务
     * @param serviceName 服务名称
     * @param endpoint 端点
     * @param options 请求选项
     * @returns 响应数据
     */
    request<T = any>(serviceName: string, endpoint: string, options?: RequestOptions): Promise<T>;
    /**
     * 发送请求
     * @param url URL
     * @param options 请求选项
     * @returns 响应数据
     */
    private sendRequest;
    /**
     * 发现服务实例
     * @param serviceName 服务名称
     * @returns 服务实例列表
     */
    private discoverServiceInstances;
    /**
     * 选择服务实例
     * @param instances 服务实例列表
     * @returns 选择的服务实例
     */
    private selectServiceInstance;
    /**
     * 生成缓存键
     * @param serviceName 服务名称
     * @param endpoint 端点
     * @param options 请求选项
     * @returns 缓存键
     */
    private generateCacheKey;
    /**
     * 清除请求缓存
     * @param serviceName 服务名称（可选，如果提供则只清除该服务的缓存）
     * @param endpoint 端点（可选，如果提供则只清除该端点的缓存）
     */
    clearRequestCache(serviceName?: string, endpoint?: string): void;
    /**
     * 销毁客户端
     */
    destroy(): void;
}
