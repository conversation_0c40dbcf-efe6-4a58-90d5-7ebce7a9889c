/**
 * UI3DSystem.ts
 *
 * 3D UI系统，管理3D空间中的界面元素
 */

import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { Vector3, Euler, Group, Scene, MeshBasicMaterial, Texture } from 'three';
import { UI3DComponent, UI3DComponentProps, BillboardMode } from '../components/UI3DComponent';
import { UIComponentType } from '../components/UIComponent';
import { UISystem } from './UISystem';

/**
 * 3D UI系统配置
 */
export interface UI3DSystemConfig {
  // 是否启用调试模式
  debug?: boolean;

  // 默认字体
  defaultFont?: string;

  // 默认字体大小
  defaultFontSize?: number;

  // 默认字体颜色
  defaultFontColor?: string;

  // 默认背景颜色
  defaultBackgroundColor?: string;

  // 默认边框颜色
  defaultBorderColor?: string;

  // 默认边框宽度
  defaultBorderWidth?: number;

  // 默认边框圆角
  defaultBorderRadius?: number;

  // 默认内边距
  defaultPadding?: number;

  // 默认交互距离
  defaultInteractionDistance?: number;

  // 默认悬停颜色
  defaultHoverColor?: string;

  // 默认激活颜色
  defaultActiveColor?: string;

  // 默认广告牌模式
  defaultBillboardMode?: BillboardMode;
}

/**
 * 3D UI系统
 * 管理3D空间中的界面元素
 */
export class UI3DSystem extends System {
  // UI系统引用
  private uiSystem: UISystem;

  // 配置
  private config: UI3DSystemConfig;

  // 3D UI根节点
  private root: Group;

  // 场景引用
  private scene?: Scene;

  /**
   * 构造函数
   * @param world 世界实例
   * @param uiSystem UI系统实例
   * @param config 3D UI系统配置
   */
  constructor(world: World, uiSystem: UISystem, config: UI3DSystemConfig = {}) {
    // 调用基类构造函数，传入优先级（默认为0）
    super(0);

    // 设置世界引用（使用基类方法）
    this.setWorld(world);

    this.uiSystem = uiSystem;

    this.config = {
      debug: config.debug || false,
      defaultFont: config.defaultFont || 'Arial',
      defaultFontSize: config.defaultFontSize || 24,
      defaultFontColor: config.defaultFontColor || '#ffffff',
      defaultBackgroundColor: config.defaultBackgroundColor || 'rgba(0, 0, 0, 0.5)',
      defaultBorderColor: config.defaultBorderColor || '#ffffff',
      defaultBorderWidth: config.defaultBorderWidth || 2,
      defaultBorderRadius: config.defaultBorderRadius || 8,
      defaultPadding: config.defaultPadding || 16,
      defaultInteractionDistance: config.defaultInteractionDistance || 10,
      defaultHoverColor: config.defaultHoverColor || '#aaaaff',
      defaultActiveColor: config.defaultActiveColor || '#5555ff',
      defaultBillboardMode: config.defaultBillboardMode || BillboardMode.Y_AXIS
    };

    // 创建3D UI根节点
    this.root = new Group();
    this.root.name = 'UI3D-Root';

    // 获取活跃场景
    const activeScene = this.world?.getActiveScene();
    if (activeScene) {
      this.scene = activeScene.getThreeScene();

      // 将根节点添加到场景
      if (this.scene) {
        this.scene.add(this.root);
      }
    }
  }

  /**
   * 创建3D UI元素
   * @param entity 实体
   * @param type UI元素类型
   * @param props UI元素属性
   * @returns 创建的3D UI组件
   */
  createUIElement(entity: Entity, type: UIComponentType, props: UI3DComponentProps = {}): UI3DComponent {
    // 合并默认属性和提供的属性
    const mergedProps: UI3DComponentProps = {
      type,
      fontFamily: this.config.defaultFont,
      fontSize: this.config.defaultFontSize,
      fontColor: this.config.defaultFontColor,
      backgroundColor: this.config.defaultBackgroundColor,
      borderColor: this.config.defaultBorderColor,
      borderWidth: this.config.defaultBorderWidth,
      borderRadius: this.config.defaultBorderRadius,
      padding: this.config.defaultPadding,
      interactionDistance: this.config.defaultInteractionDistance,
      hoverColor: this.config.defaultHoverColor,
      activeColor: this.config.defaultActiveColor,
      billboardMode: this.config.defaultBillboardMode,
      ...props
    };

    // 创建3D UI组件
    const component = new UI3DComponent(mergedProps);

    // 注册到UI系统
    this.uiSystem.registerUIComponent(entity, component);

    // 将3D对象添加到根节点
    if (component.group) {
      this.root.add(component.group);
    }

    return component;
  }

  /**
   * 创建3D按钮
   * @param entity 实体
   * @param text 按钮文本
   * @param props 按钮属性
   * @returns 创建的按钮组件
   */
  createButton(entity: Entity, text: string, props: UI3DComponentProps = {}): UI3DComponent {
    return this.createUIElement(entity, UIComponentType.BUTTON, {
      textContent: text,
      backgroundColor: props.backgroundColor || 'rgba(60, 60, 60, 0.8)',
      ...props
    });
  }

  /**
   * 创建3D文本
   * @param entity 实体
   * @param text 文本内容
   * @param props 文本属性
   * @returns 创建的文本组件
   */
  createText(entity: Entity, text: string, props: UI3DComponentProps = {}): UI3DComponent {
    return this.createUIElement(entity, UIComponentType.TEXT, {
      textContent: text,
      backgroundColor: 'transparent',
      ...props
    });
  }

  /**
   * 创建3D图像
   * @param entity 实体
   * @param texture 纹理
   * @param props 图像属性
   * @returns 创建的图像组件
   */
  createImage(entity: Entity, texture: Texture, props: UI3DComponentProps = {}): UI3DComponent {
    const component = this.createUIElement(entity, UIComponentType.IMAGE, {
      backgroundColor: 'transparent',
      ...props
    });

    // 设置纹理
    if (component.mesh) {
      const material = component.mesh.material as MeshBasicMaterial;
      material.map = texture;
      material.needsUpdate = true;
    }

    return component;
  }

  /**
   * 创建3D面板
   * @param entity 实体
   * @param props 面板属性
   * @returns 创建的面板组件
   */
  createPanel(entity: Entity, props: UI3DComponentProps = {}): UI3DComponent {
    return this.createUIElement(entity, UIComponentType.PANEL, {
      backgroundColor: props.backgroundColor || 'rgba(30, 30, 30, 0.8)',
      ...props
    });
  }

  /**
   * 创建3D窗口
   * @param entity 实体
   * @param title 窗口标题
   * @param props 窗口属性
   * @returns 创建的窗口组件
   */
  createWindow(entity: Entity, title: string = '', props: UI3DComponentProps = {}): UI3DComponent {
    const component = this.createUIElement(entity, UIComponentType.WINDOW, {
      backgroundColor: props.backgroundColor || 'rgba(30, 30, 30, 0.8)',
      ...props
    });

    // 如果有标题，更新画布内容
    if (title && component.canvas && component.context) {
      const { canvas, context } = component;

      // 保存当前内容
      const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

      // 绘制标题栏
      context.fillStyle = 'rgba(50, 50, 50, 0.9)';
      context.fillRect(0, 0, canvas.width, 40);

      // 绘制标题
      context.fillStyle = component.fontColor;
      context.font = `bold ${component.fontSize}px ${component.fontFamily}`;
      context.textAlign = 'center';
      context.textBaseline = 'middle';
      context.fillText(title, canvas.width / 2, 20);

      // 恢复内容（在标题栏下方）
      context.putImageData(imageData, 0, 40);

      // 更新纹理
      if (component.texture) {
        component.texture.needsUpdate = true;
      }
    }

    return component;
  }

  /**
   * 创建3D UI容器
   * @param entity 实体
   * @param position 位置
   * @param rotation 旋转
   * @param scale 缩放
   * @returns 创建的容器
   */
  createContainer(entity: Entity, position?: Vector3, rotation?: Vector3 | Euler, scale?: Vector3): Group {
    // 创建容器
    const container = new Group();
    container.name = `UI3D-Container-${entity.id}`;

    // 设置变换
    if (position) container.position.copy(position);
    if (rotation) {
      if (rotation instanceof Euler) {
        container.rotation.copy(rotation);
      } else {
        (container as any).setRotationQuaternion(rotation.x, rotation.y, rotation.z);
      }
    }
    if (scale) container.scale.copy(scale);

    // 添加到根节点
    this.root.add(container);

    return container;
  }

  /**
   * 将UI元素添加到容器
   * @param component UI组件
   * @param container 容器
   * @param localPosition 本地位置
   */
  addToContainer(component: UI3DComponent, container: Group, localPosition?: Vector3): void {
    if (component.group) {
      // 从当前父节点移除
      if (component.group.parent) {
        component.group.parent.remove(component.group);
      }

      // 设置本地位置
      if (localPosition) {
        component.group.position.copy(localPosition);
      }

      // 添加到容器
      container.add(component.group);
    }
  }

  /**
   * 从容器中移除UI元素
   * @param component UI组件
   */
  removeFromContainer(component: UI3DComponent): void {
    if (component.group && component.group.parent) {
      component.group.parent.remove(component.group);
      this.root.add(component.group);
    }
  }

  /**
   * 创建3D UI布局
   * @param _entity 实体 - 未使用，但保留以便将来扩展
   * @param container 容器
   * @param elements UI元素列表
   * @param layout 布局类型
   * @param spacing 元素间距
   */
  createLayout(_entity: Entity, container: Group, elements: UI3DComponent[], layout: 'horizontal' | 'vertical' | 'grid' = 'vertical', spacing: number = 0.1): void {
    if (elements.length === 0) return;

    // 计算布局
    switch (layout) {
      case 'horizontal':
        // 水平布局
        let offsetX = 0;
        for (const element of elements) {
          if (element.group) {
            // 计算元素宽度
            const width = element.size.x / 100; // 转换为米

            // 设置位置
            (element.group as any).setPosition(offsetX + width / 2, 0, 0);

            // 更新偏移
            offsetX += width + spacing;

            // 添加到容器
            this.addToContainer(element, container);
          }
        }
        break;

      case 'vertical':
        // 垂直布局
        let offsetY = 0;
        for (const element of elements) {
          if (element.group) {
            // 计算元素高度
            const height = element.size.y / 100; // 转换为米

            // 设置位置
            (element.group as any).setPosition(0, -offsetY - height / 2, 0);

            // 更新偏移
            offsetY += height + spacing;

            // 添加到容器
            this.addToContainer(element, container);
          }
        }
        break;

      case 'grid':
        // 网格布局
        const columns = Math.ceil(Math.sqrt(elements.length));
        let row = 0, col = 0;

        for (const element of elements) {
          if (element.group) {
            // 计算元素尺寸
            const width = element.size.x / 100; // 转换为米
            const height = element.size.y / 100; // 转换为米

            // 设置位置
            (element.group as any).setPosition(
              col * (width + spacing),
              -row * (height + spacing),
              0
            );

            // 更新行列
            col++;
            if (col >= columns) {
              col = 0;
              row++;
            }

            // 添加到容器
            this.addToContainer(element, container);
          }
        }
        break;
    }
  }

  /**
   * 更新系统
   * @param _deltaTime 时间增量 - 未使用，因为UI系统会更新所有UI组件
   */
  update(_deltaTime: number): void {
    // 3D UI系统不需要额外的更新逻辑，因为UI系统会更新所有UI组件
  }

  /**
   * 渲染系统
   */
  render(): void {
    // 3D UI系统不需要额外的渲染逻辑，因为UI系统会渲染所有UI组件
  }

  /**
   * 销毁系统
   */
  dispose(): void {
    // 从场景中移除根节点
    if (this.scene) {
      this.scene.remove(this.root);
    }

    // 清空根节点
    while (this.root.children.length > 0) {
      this.root.remove(this.root.children[0]);
    }
  }
}
