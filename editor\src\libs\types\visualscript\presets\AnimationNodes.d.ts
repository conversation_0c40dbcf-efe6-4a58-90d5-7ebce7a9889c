/**
 * 动画相关的可视化脚本节点
 */
import { VisualScriptNode } from '../VisualScriptNode';
/**
 * 播放动画节点
 */
export declare class PlayAnimationNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 停止动画节点
 */
export declare class StopAnimationNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 设置动画速度节点
 */
export declare class SetAnimationSpeedNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 获取动画状态节点
 */
export declare class GetAnimationStateNode extends VisualScriptNode {
    constructor();
    execute(inputs: any): any;
}
/**
 * 注册动画节点
 */
export declare function registerAnimationNodes(): void;
