/**
 * 水体生成器
 * 用于快速生成各种类型的水体
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import { WaterBodyComponent } from './WaterBodyComponent';
import { WaterPresetType } from './WaterPresets';
/**
 * 水体生成配置
 */
export interface WaterGenerationConfig {
    /** 预设类型 */
    preset: WaterPresetType;
    /** 位置 */
    position?: THREE.Vector3;
    /** 旋转 */
    rotation?: THREE.Euler;
    /** 缩放 */
    scale?: THREE.Vector3;
    /** 自定义尺寸 */
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    /** 自定义物理参数 */
    physicsParams?: {
        density?: number;
        viscosity?: number;
        surfaceTension?: number;
    };
    /** 自定义波动参数 */
    waveParams?: {
        amplitude?: number;
        frequency?: number;
        speed?: number;
        direction?: THREE.Vector2;
    };
    /** 自定义流动参数 */
    flowParams?: {
        direction?: THREE.Vector3;
        speed?: number;
    };
    /** 自定义视觉参数 */
    visualParams?: {
        color?: THREE.Color;
        opacity?: number;
        reflectivity?: number;
        refractivity?: number;
    };
    /** 实体名称 */
    entityName?: string;
    /** 实体ID */
    entityId?: string;
}
/**
 * 水体生成器类
 */
export declare class WaterGenerator {
    private static nextId;
    /**
     * 生成水体
     * @param config 生成配置
     * @returns 生成的水体实体和组件
     */
    static generateWater(config: WaterGenerationConfig): {
        entity: Entity;
        waterBody: WaterBodyComponent;
    };
    /**
     * 快速生成湖泊
     * @param position 位置
     * @param size 尺寸
     * @returns 生成的水体
     */
    static generateLake(position?: THREE.Vector3, size?: {
        width: number;
        height: number;
        depth: number;
    }): {
        entity: Entity;
        waterBody: WaterBodyComponent;
    };
    /**
     * 快速生成河流
     * @param position 位置
     * @param size 尺寸
     * @param flowDirection 流动方向
     * @returns 生成的水体
     */
    static generateRiver(position?: THREE.Vector3, size?: {
        width: number;
        height: number;
        depth: number;
    }, flowDirection?: THREE.Vector3): {
        entity: Entity;
        waterBody: WaterBodyComponent;
    };
    /**
     * 快速生成海洋
     * @param position 位置
     * @param size 尺寸
     * @returns 生成的水体
     */
    static generateOcean(position?: THREE.Vector3, size?: {
        width: number;
        height: number;
        depth: number;
    }): {
        entity: Entity;
        waterBody: WaterBodyComponent;
    };
    /**
     * 快速生成游泳池
     * @param position 位置
     * @param size 尺寸
     * @returns 生成的水体
     */
    static generatePool(position?: THREE.Vector3, size?: {
        width: number;
        height: number;
        depth: number;
    }): {
        entity: Entity;
        waterBody: WaterBodyComponent;
    };
    /**
     * 快速生成温泉
     * @param position 位置
     * @param size 尺寸
     * @returns 生成的水体
     */
    static generateHotSpring(position?: THREE.Vector3, size?: {
        width: number;
        height: number;
        depth: number;
    }): {
        entity: Entity;
        waterBody: WaterBodyComponent;
    };
    /**
     * 快速生成瀑布
     * @param position 位置
     * @param size 尺寸
     * @returns 生成的水体
     */
    static generateWaterfall(position?: THREE.Vector3, size?: {
        width: number;
        height: number;
        depth: number;
    }): {
        entity: Entity;
        waterBody: WaterBodyComponent;
    };
    /**
     * 批量生成水体
     * @param configs 配置数组
     * @returns 生成的水体数组
     */
    static generateMultipleWaters(configs: WaterGenerationConfig[]): Array<{
        entity: Entity;
        waterBody: WaterBodyComponent;
    }>;
    /**
     * 生成水体场景
     * @param sceneType 场景类型
     * @returns 生成的水体数组
     */
    static generateWaterScene(sceneType: 'lake_scene' | 'river_scene' | 'ocean_scene' | 'pool_scene'): Array<{
        entity: Entity;
        waterBody: WaterBodyComponent;
    }>;
    /**
     * 获取推荐的水体配置
     * @param environment 环境类型
     * @returns 推荐配置
     */
    static getRecommendedConfig(environment: 'indoor' | 'outdoor' | 'underground' | 'fantasy'): WaterGenerationConfig[];
}
