/**
 * 物理系统模块
 * 导出所有物理系统相关的类和接口
 */

// 导出物理系统
export { PhysicsSystem } from './PhysicsSystem';
export type { PhysicsSystemOptions } from './PhysicsSystem';

// 导出物理体
export { PhysicsBody } from './PhysicsBody';
export { BodyType } from './PhysicsBody';
export type { PhysicsBodyOptions } from './PhysicsBody';

// 导出碰撞器
export { PhysicsCollider, ColliderType } from './PhysicsCollider';
export type { ColliderOptions as PhysicsColliderOptions } from './PhysicsCollider';

// 导出碰撞事件
export { CollisionEvent, CollisionEventType } from './collision/CollisionEvent';
export { CollisionDetector } from './collision/CollisionDetector';

// 导出约束
export * from './constraints';

// 定义物理事件类型
export enum PhysicsEventType {
  COLLISION_START = 'collisionStart',
  COLLISION_END = 'collisionEnd',
  COLLISION_STAY = 'collisionStay',
  TRIGGER_ENTER = 'triggerEnter',
  TRIGGER_STAY = 'triggerStay',
  TRIGGER_EXIT = 'triggerExit',
  BODY_ADDED = 'bodyAdded',
  BODY_REMOVED = 'bodyRemoved'
}

// 导出物理射线检测结果
export { PhysicsRaycastResult } from './PhysicsRaycastResult';

// 导出物理材质工厂
export * from './PhysicsMaterialFactory';

// 导出物理组件
export { PhysicsBodyComponent } from './components/PhysicsBodyComponent';
export { PhysicsColliderComponent } from './components/PhysicsColliderComponent';
export { PhysicsConstraintComponent } from './components/PhysicsConstraintComponent';
export { PhysicsWorldComponent } from './components/PhysicsWorldComponent';
export { CharacterControllerComponent } from './components/CharacterControllerComponent';

// 导出物理预设系统
export { PhysicsPresetManager } from './presets/PhysicsPresetManager';
export * from './presets/PhysicsPreset';
export { CommonPhysicsPresets } from './presets/CommonPhysicsPresets';

// 导出物理场景导入/导出
export { PhysicsSceneExporter } from './io/PhysicsSceneExporter';
export { PhysicsSceneImporter } from './io/PhysicsSceneImporter';

// 导出物理调试器
export { PhysicsDebugger } from './debug/PhysicsDebugger';
export { EnhancedPhysicsDebugger } from './debug/EnhancedPhysicsDebugger';
