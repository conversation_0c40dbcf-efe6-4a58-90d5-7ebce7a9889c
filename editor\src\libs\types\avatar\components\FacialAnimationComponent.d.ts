/**
 * 面部动画组件
 * 用于控制角色的面部表情和口型同步
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { type EventCallback } from '../../utils/EventEmitter';
/**
 * 面部表情类型
 */
export declare enum FacialExpressionType {
    NEUTRAL = "neutral",
    HAPPY = "happy",
    SAD = "sad",
    ANGRY = "angry",
    SURPRISED = "surprised",
    FEAR = "fear",
    FEARFUL = "fearful",
    DISGUST = "disgust",
    DISGUSTED = "disgusted",
    CONTEMPT = "contempt"
}
/**
 * 口型类型
 */
export declare enum VisemeType {
    NEUTRAL = "neutral",
    SILENT = "silent",
    AA = "aa",
    E = "e",
    EE = "ee",
    EH = "eh",
    IH = "ih",
    OH = "oh",
    OU = "ou",
    PP = "pp",
    FF = "ff",
    TH = "th",
    DD = "dd",
    K = "k",
    KK = "kk",
    CH = "ch",
    SS = "ss",
    NN = "nn",
    R = "r",
    RR = "rr",
    MM = "mm",
    ER = "er"
}
/**
 * 面部表情数据
 */
export interface FacialExpression {
    expression: FacialExpressionType;
    weight: number;
}
/**
 * 口型数据
 */
export interface Viseme {
    viseme: VisemeType;
    weight: number;
}
/**
 * 面部动画组件类型
 */
export declare const FacialAnimationComponentType = "FacialAnimationComponent";
/**
 * 面部动画组件
 */
export declare class FacialAnimationComponent extends Component {
    /** 组件类型 */
    static readonly TYPE = "FacialAnimationComponent";
    /** 事件发射器 */
    private eventEmitter;
    /** 当前表情 */
    private currentExpression;
    /** 当前口型 */
    private currentViseme;
    /** 表情混合映射 */
    private expressionBlendMap;
    /** 口型混合映射 */
    private visemeBlendMap;
    /** 表情混合速度 */
    private expressionBlendSpeed;
    /** 口型混合速度 */
    private visemeBlendSpeed;
    /**
     * 构造函数
     * @param entity 实体
     */
    constructor(entity: Entity);
    /**
     * 获取组件类型
     */
    getType(): string;
    /**
     * 设置表情
     * @param expression 表情类型
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     */
    setExpression(expression: FacialExpressionType, weight?: number, blendTime?: number): void;
    /**
     * 重置表情
     */
    resetExpression(): void;
    /**
     * 获取当前表情
     */
    getCurrentExpression(): FacialExpression;
    /**
     * 设置口型
     * @param viseme 口型类型
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     */
    setViseme(viseme: VisemeType, weight?: number, blendTime?: number): void;
    /**
     * 重置口型
     */
    resetViseme(): void;
    /**
     * 获取当前口型
     */
    getCurrentViseme(): Viseme;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新混合
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateBlending;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: EventCallback): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: EventCallback): void;
}
