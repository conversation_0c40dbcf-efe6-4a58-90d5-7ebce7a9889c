/**
 * 视觉脚本节点基类
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { ExecutionContext } from '../execution/ExecutionContext';
import { Graph } from '../graph/Graph';
import { NodeMetadata } from '../graph/GraphJSON';
/**
 * 节点插槽类型
 */
export declare enum SocketType {
    /** 流程插槽 */
    FLOW = "flow",
    /** 数据插槽 */
    DATA = "data"
}
/**
 * 节点插槽方向
 */
export declare enum SocketDirection {
    /** 输入插槽 */
    INPUT = "input",
    /** 输出插槽 */
    OUTPUT = "output"
}
/**
 * 节点插槽定义
 */
export interface SocketDefinition {
    /** 插槽名称 */
    name: string;
    /** 插槽类型 */
    type: SocketType;
    /** 插槽方向 */
    direction: SocketDirection;
    /** 数据类型（对于数据插槽） */
    dataType?: string;
    /** 默认值（对于数据插槽） */
    defaultValue?: any;
    /** 插槽描述 */
    description?: string;
    /** 是否可选 */
    optional?: boolean;
}
/**
 * 节点插槽
 */
export interface Socket extends SocketDefinition {
    /** 连接的节点ID */
    connectedNodeId?: string;
    /** 连接的插槽名称 */
    connectedSocketName?: string;
    /** 当前值 */
    value?: any;
}
/**
 * 节点连接
 */
export interface NodeConnection {
    /** 源节点 */
    sourceNode: Node;
    /** 源插槽名称 */
    sourceSocketName: string;
    /** 目标节点 */
    targetNode: Node;
    /** 目标插槽名称 */
    targetSocketName: string;
}
/**
 * 节点类型
 */
export declare enum NodeType {
    /** 普通节点 */
    NORMAL = "normal",
    /** 事件节点 */
    EVENT = "event",
    /** 函数节点 */
    FUNCTION = "function",
    /** 异步节点 */
    ASYNC = "async"
}
/**
 * 节点类别
 */
export declare enum NodeCategory {
    /** 流程控制 */
    FLOW = "flow",
    /** 数学运算 */
    MATH = "math",
    /** 逻辑运算 */
    LOGIC = "logic",
    /** 字符串操作 */
    STRING = "string",
    /** 数组操作 */
    ARRAY = "array",
    /** 对象操作 */
    OBJECT = "object",
    /** 变量操作 */
    VARIABLE = "variable",
    /** 函数操作 */
    FUNCTION = "function",
    /** 事件操作 */
    EVENT = "event",
    /** 实体操作 */
    ENTITY = "entity",
    /** 组件操作 */
    COMPONENT = "component",
    /** 物理操作 */
    PHYSICS = "physics",
    /** 动画操作 */
    ANIMATION = "animation",
    /** 输入操作 */
    INPUT = "input",
    /** 音频操作 */
    AUDIO = "audio",
    /** 网络操作 */
    NETWORK = "network",
    /** AI操作 */
    AI = "ai",
    /** 调试操作 */
    DEBUG = "debug",
    /** 自定义操作 */
    CUSTOM = "custom"
}
/**
 * 节点选项
 */
export interface NodeOptions {
    /** 节点ID */
    id: string;
    /** 节点类型名称 */
    type: string;
    /** 节点元数据 */
    metadata?: NodeMetadata;
    /** 所属图形 */
    graph: Graph;
    /** 执行上下文 */
    context: ExecutionContext;
}
/**
 * 节点基类
 */
export declare class Node extends EventEmitter {
    /** 节点ID */
    readonly id: string;
    /** 节点类型名称 */
    readonly type: string;
    /** 节点类型 */
    readonly nodeType: NodeType;
    /** 节点类别 */
    readonly category: NodeCategory;
    /** 节点元数据 */
    metadata: NodeMetadata;
    /** 所属图形 */
    protected graph: Graph;
    /** 执行上下文 */
    protected context: ExecutionContext;
    /** 输入插槽 */
    protected inputs: Map<string, Socket>;
    /** 输出插槽 */
    protected outputs: Map<string, Socket>;
    /** 输入连接 */
    protected inputConnections: Map<string, NodeConnection>;
    /** 输出连接 */
    protected outputConnections: Map<string, NodeConnection[]>;
    /**
     * 创建节点
     * @param options 节点选项
     */
    constructor(options: NodeOptions);
    /**
     * 初始化插槽
     */
    protected initializeSockets(): void;
    /**
     * 添加输入插槽
     * @param definition 插槽定义
     * @returns 添加的插槽
     */
    protected addInput(definition: SocketDefinition): Socket;
    /**
     * 添加输出插槽
     * @param definition 插槽定义
     * @returns 添加的插槽
     */
    protected addOutput(definition: SocketDefinition): Socket;
    /**
     * 获取输入插槽
     * @param name 插槽名称
     * @returns 插槽
     */
    getInput(name: string): Socket | undefined;
    /**
     * 获取输出插槽
     * @param name 插槽名称
     * @returns 插槽
     */
    getOutput(name: string): Socket | undefined;
    /**
     * 获取所有输入插槽
     * @returns 输入插槽映射
     */
    getInputs(): Map<string, Socket>;
    /**
     * 获取所有输出插槽
     * @returns 输出插槽映射
     */
    getOutputs(): Map<string, Socket>;
    /**
     * 获取输入插槽数组
     * @returns 输入插槽数组
     */
    getInputSockets(): Socket[];
    /**
     * 获取节点元数据
     * @returns 节点元数据
     */
    getMetadata(): NodeMetadata;
    /**
     * 获取节点位置
     * @returns 节点位置
     */
    getPosition(): {
        x: number;
        y: number;
    };
    /**
     * 检查节点是否已执行
     * @returns 是否已执行
     */
    isExecuted(): boolean;
    /**
     * 设置参数值
     * @param name 参数名称
     * @param value 参数值
     */
    setParameterValue(name: string, value: any): void;
    /**
     * 获取参数值
     * @param name 参数名称
     * @returns 参数值
     */
    getParameterValue(name: string): any;
    /**
     * 连接输入
     * @param inputName 输入插槽名称
     * @param sourceNode 源节点
     * @param sourceOutputName 源输出插槽名称
     * @returns 是否连接成功
     */
    connectInput(inputName: string, sourceNode: Node, sourceOutputName: string): boolean;
    /**
     * 连接流程
     * @param outputName 输出插槽名称
     * @param targetNode 目标节点
     * @param targetInputName 目标输入插槽名称
     * @returns 是否连接成功
     */
    connectFlow(outputName: string, targetNode: Node, targetInputName: string): boolean;
    /**
     * 断开输入连接
     * @param inputName 输入插槽名称
     * @returns 是否断开成功
     */
    disconnectInput(inputName: string): boolean;
    /**
     * 断开输出连接
     * @param outputName 输出插槽名称
     * @param targetNode 目标节点
     * @param targetInputName 目标输入插槽名称
     * @returns 是否断开成功
     */
    disconnectOutput(outputName: string, targetNode: Node, targetInputName: string): boolean;
    /**
     * 断开所有连接
     */
    disconnectAll(): void;
    /**
     * 执行节点
     * @returns 执行结果
     */
    execute(): any;
    /**
     * 获取输入值
     * @param name 输入名称
     * @returns 输入值
     */
    getInputValue(name: string): any;
    /**
     * 设置输出值
     * @param name 输出名称
     * @param value 输出值
     */
    setOutputValue(name: string, value: any): void;
    /**
     * 触发流程输出
     * @param name 输出名称
     */
    triggerFlow(name: string): void;
    /**
     * 初始化节点
     */
    initialize(): void;
    /**
     * 销毁节点
     */
    dispose(): void;
}
