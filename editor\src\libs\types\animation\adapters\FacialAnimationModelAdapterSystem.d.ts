/**
 * 面部动画模型适配器系统
 * 用于管理面部动画模型适配器组件
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { FacialExpressionType, VisemeType } from '../FacialAnimation';
import { FacialAnimationModelAdapterComponent, FacialAnimationModelAdapterConfig, FacialAnimationModelType } from './FacialAnimationModelAdapter';
/**
 * 面部动画模型适配器系统配置
 */
export interface FacialAnimationModelAdapterSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 默认模型类型 */
    defaultModelType?: FacialAnimationModelType;
    /** 是否自动检测混合形状 */
    autoDetectBlendShapes?: boolean;
}
/**
 * 面部动画模型适配器系统
 */
export declare class FacialAnimationModelAdapterSystem extends System {
    /** 系统类型 */
    static readonly type = "FacialAnimationModelAdapter";
    /** 面部动画模型适配器组件 */
    private components;
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<FacialAnimationModelAdapterSystemConfig>);
    /**
     * 创建面部动画模型适配器
     * @param entity 实体
     * @param config 配置
     * @returns 面部动画模型适配器组件
     */
    createModelAdapter(entity: Entity, config?: Partial<FacialAnimationModelAdapterConfig>): FacialAnimationModelAdapterComponent;
    /**
     * 移除面部动画模型适配器
     * @param entity 实体
     */
    removeModelAdapter(entity: Entity): void;
    /**
     * 获取面部动画模型适配器
     * @param entity 实体
     * @returns 面部动画模型适配器组件，如果不存在则返回null
     */
    getModelAdapter(entity: Entity): FacialAnimationModelAdapterComponent | null;
    /**
     * 设置骨骼网格
     * @param entity 实体
     * @param mesh 骨骼网格
     * @returns 是否成功设置
     */
    setSkinnedMesh(entity: Entity, mesh: THREE.SkinnedMesh): boolean;
    /**
     * 应用表情
     * @param entity 实体
     * @param expression 表情类型
     * @param weight 权重
     * @returns 是否成功应用
     */
    applyExpression(entity: Entity, expression: FacialExpressionType, weight: number): boolean;
    /**
     * 应用口型
     * @param entity 实体
     * @param viseme 口型类型
     * @param weight 权重
     * @returns 是否成功应用
     */
    applyViseme(entity: Entity, viseme: VisemeType, weight: number): boolean;
    /**
     * 重置所有表情和口型
     * @param entity 实体
     * @returns 是否成功重置
     */
    resetAll(entity: Entity): boolean;
    /**
     * 创建VRM模型适配器
     * @param entity 实体
     * @param mesh 骨骼网格
     * @returns 面部动画模型适配器组件
     */
    createVRMModelAdapter(entity: Entity, mesh: THREE.SkinnedMesh): FacialAnimationModelAdapterComponent;
    /**
     * 创建FBX模型适配器
     * @param entity 实体
     * @param mesh 骨骼网格
     * @returns 面部动画模型适配器组件
     */
    createFBXModelAdapter(entity: Entity, mesh: THREE.SkinnedMesh): FacialAnimationModelAdapterComponent;
    /**
     * 创建GLTF模型适配器
     * @param entity 实体
     * @param mesh 骨骼网格
     * @returns 面部动画模型适配器组件
     */
    createGLTFModelAdapter(entity: Entity, mesh: THREE.SkinnedMesh): FacialAnimationModelAdapterComponent;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
}
