/**
 * GPU实例更新器
 * 使用WebGL计算着色器加速实例更新
 */
import * as THREE from 'three';
import { EnhancedInstanceData } from './EnhancedInstancedRenderingSystem';
/**
 * GPU实例更新器配置接口
 */
export interface GPUInstanceUpdaterOptions {
    /** 是否使用WebGL2 */
    useWebGL2?: boolean;
    /** 是否使用计算着色器 */
    useComputeShader?: boolean;
    /** 是否使用批处理 */
    useBatching?: boolean;
    /** 批处理大小 */
    batchSize?: number;
    /** 是否使用双缓冲 */
    useDoubleBuffering?: boolean;
    /** 是否使用并行更新 */
    useParallelUpdate?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * GPU实例更新器类
 */
export declare class GPUInstanceUpdater {
    /** 是否使用WebGL2 */
    private useWebGL2;
    /** 是否使用计算着色器 */
    private useComputeShader;
    /** 是否使用批处理 */
    private useBatching;
    /** 批处理大小 */
    private batchSize;
    /** 是否使用双缓冲 */
    private useDoubleBuffering;
    /** 是否使用并行更新 */
    private useParallelUpdate;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** WebGL渲染器 */
    private renderer;
    /** 计算场景 */
    private computeScene;
    /** 计算相机 */
    private computeCamera;
    /** 计算渲染目标 */
    private computeRenderTarget;
    /** 计算着色器 */
    private computeShader;
    /** 计算网格 */
    private computeMesh;
    /** 实例矩阵纹理 */
    private instanceMatrixTexture;
    /** 实例矩阵纹理宽度 */
    private instanceMatrixTextureWidth;
    /** 实例矩阵纹理高度 */
    private instanceMatrixTextureHeight;
    /** 是否支持WebGL2 */
    private supportsWebGL2;
    /** 是否支持计算着色器 */
    private supportsComputeShader;
    /** 是否已初始化 */
    private initialized;
    /** 工作线程 */
    private workers;
    /** 工作线程数量 */
    private workerCount;
    /**
     * 创建GPU实例更新器
     * @param options GPU实例更新器配置
     */
    constructor(options?: GPUInstanceUpdaterOptions);
    /**
     * 检查WebGL支持
     */
    private checkWebGLSupport;
    /**
     * 检查计算着色器支持
     * @param gl WebGL2渲染上下文
     * @returns 是否支持计算着色器
     */
    private checkComputeShaderSupport;
    /**
     * 初始化工作线程
     */
    private initializeWorkers;
    /**
     * 初始化GPU实例更新器
     * @param instanceCount 实例数量
     */
    private initialize;
    /**
     * 计算实例矩阵纹理尺寸
     * @param instanceCount 实例数量
     */
    private calculateInstanceMatrixTextureSize;
    /**
     * 创建实例矩阵纹理
     * @param instanceCount 实例数量
     */
    private createInstanceMatrixTexture;
    /**
     * 创建计算着色器
     */
    private createComputeShader;
    /**
     * 创建计算网格
     */
    private createComputeMesh;
    /**
     * 创建计算渲染目标
     */
    private createComputeRenderTarget;
    /**
     * 使用GPU更新实例
     * @param instancedMesh 实例化网格
     * @param instances 实例数据
     */
    updateWithGPU(instancedMesh: THREE.InstancedMesh, instances: EnhancedInstanceData[]): void;
    /**
     * 使用批处理更新实例
     * @param instancedMesh 实例化网格
     * @param instances 实例数据
     */
    private updateWithBatching;
    /**
     * 使用GPU更新实例
     * @param instancedMesh 实例化网格
     * @param instances 实例数据
     * @param startIndex 起始索引
     */
    private updateInstancesWithGPU;
    /**
     * 创建实例数据纹理
     * @param instances 实例数据
     * @returns 实例数据纹理
     */
    private createInstanceDataTexture;
    /**
     * 更新实例矩阵
     * @param instancedMesh 实例化网格
     * @param pixelBuffer 像素缓冲区
     * @param instanceCount 实例数量
     * @param startIndex 起始索引
     */
    private updateInstanceMatrices;
    /**
     * 使用CPU更新实例
     * @param instancedMesh 实例化网格
     * @param instances 实例数据
     */
    private updateWithCPU;
    /**
     * 使用并行更新实例
     * @param instancedMesh 实例化网格
     * @param instances 实例数据
     */
    private updateWithParallel;
    /**
     * 销毁GPU实例更新器
     */
    dispose(): void;
}
