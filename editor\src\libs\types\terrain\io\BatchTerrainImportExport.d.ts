import { TerrainComponent } from '../components/TerrainComponent';
import { HeightMapExportOptions, HeightMapImportOptions } from './HeightMapImportExport';
import { TerrainExportOptions, TerrainImportOptions } from './TerrainImportExportService';
import { ThirdPartyTerrainExportOptions, ThirdPartyTerrainFormat, ThirdPartyTerrainImportOptions } from './ThirdPartyTerrainImportExport';
/**
 * 批量导入结果
 */
export interface BatchImportResult {
    /** 文件名 */
    fileName: string;
    /** 是否成功 */
    success: boolean;
    /** 错误信息 */
    error?: string;
}
/**
 * 批量导出结果
 */
export interface BatchExportResult {
    /** 文件名 */
    fileName: string;
    /** 导出数据 */
    data: Blob | string;
    /** 是否成功 */
    success: boolean;
    /** 错误信息 */
    error?: string;
}
/**
 * 批量地形导入导出服务
 */
export declare class BatchTerrainImportExport {
    /**
     * 批量导入高度图
     * @param terrain 地形组件
     * @param files 文件列表
     * @param options 导入选项
     * @returns Promise，解析为导入结果列表
     */
    batchImportHeightMaps(terrain: TerrainComponent, files: File[], options?: HeightMapImportOptions): Promise<BatchImportResult[]>;
    /**
     * 批量导入JSON
     * @param terrain 地形组件
     * @param files 文件列表
     * @param options 导入选项
     * @returns Promise，解析为导入结果列表
     */
    batchImportJSON(terrain: TerrainComponent, files: File[], options?: TerrainImportOptions): Promise<BatchImportResult[]>;
    /**
     * 批量导入第三方格式
     * @param terrain 地形组件
     * @param files 文件列表
     * @param format 格式
     * @param options 导入选项
     * @returns Promise，解析为导入结果列表
     */
    batchImportThirdPartyFormat(terrain: TerrainComponent, files: File[], format: ThirdPartyTerrainFormat, options?: Omit<ThirdPartyTerrainImportOptions, 'format'>): Promise<BatchImportResult[]>;
    /**
     * 读取文件为文本
     * @param file 文件
     * @returns Promise，解析为文本内容
     */
    private readFileAsText;
    /**
     * 读取文件为ArrayBuffer
     * @param file 文件
     * @returns Promise，解析为ArrayBuffer
     */
    private readFileAsArrayBuffer;
    /**
     * 批量导出高度图
     * @param terrain 地形组件
     * @param formats 格式列表
     * @param options 导出选项
     * @returns Promise，解析为导出结果列表
     */
    batchExportHeightMaps(terrain: TerrainComponent, formats: HeightMapExportOptions[], baseFileName?: string): Promise<BatchExportResult[]>;
    /**
     * 批量导出JSON
     * @param terrain 地形组件
     * @param options 导出选项列表
     * @param baseFileName 基础文件名
     * @returns 导出结果列表
     */
    batchExportJSON(terrain: TerrainComponent, options: TerrainExportOptions[], baseFileName?: string): BatchExportResult[];
    /**
     * 批量导出第三方格式
     * @param terrain 地形组件
     * @param formats 格式和选项列表
     * @param baseFileName 基础文件名
     * @returns Promise，解析为导出结果列表
     */
    batchExportThirdPartyFormat(terrain: TerrainComponent, formats: {
        format: ThirdPartyTerrainFormat;
        options?: Omit<ThirdPartyTerrainExportOptions, 'format'>;
    }[], baseFileName?: string): Promise<BatchExportResult[]>;
    /**
     * 获取文件扩展名
     * @param format 格式
     * @returns 文件扩展名
     */
    private getFileExtension;
}
