/**
 * 输入绑定单元测试
 */
import { InputBinding, CompositeInputBinding, CompositeBindingType } from '../../src/input/InputBinding';

describe('InputBinding', () => {
  describe('InputBinding', () => {
    let binding: InputBinding;

    beforeEach(() => {
      binding = new InputBinding('testBinding', 'testMapping');
    });

    test('应该正确初始化输入绑定', () => {
      expect(binding.getName()).toBe('testBinding');
      expect(binding.getMappingName()).toBe('testMapping');
    });
  });

  describe('CompositeInputBinding', () => {
    let binding: CompositeInputBinding;

    beforeEach(() => {
      binding = new CompositeInputBinding('testComposite', ['mapping1', 'mapping2'], CompositeBindingType.ANY);
    });

    test('应该正确初始化组合输入绑定', () => {
      expect(binding.getName()).toBe('testComposite');
      expect(binding.getMappingName()).toBe('mapping1');
      expect(binding.getMappingNames()).toEqual(['mapping1', 'mapping2']);
      expect(binding.getCompositeType()).toBe(CompositeBindingType.ANY);
    });

    test('应该支持不同的组合类型', () => {
      const anyBinding = new CompositeInputBinding('anyBinding', ['mapping1', 'mapping2'], CompositeBindingType.ANY);
      expect(anyBinding.getCompositeType()).toBe(CompositeBindingType.ANY);

      const allBinding = new CompositeInputBinding('allBinding', ['mapping1', 'mapping2'], CompositeBindingType.ALL);
      expect(allBinding.getCompositeType()).toBe(CompositeBindingType.ALL);

      const priorityBinding = new CompositeInputBinding('priorityBinding', ['mapping1', 'mapping2'], CompositeBindingType.PRIORITY);
      expect(priorityBinding.getCompositeType()).toBe(CompositeBindingType.PRIORITY);
    });
  });
});
