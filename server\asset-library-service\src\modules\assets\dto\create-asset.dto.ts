import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsUUID,
  IsArray,
  IsObject,
  MaxLength,
  MinLength,
  IsNotEmpty,
} from 'class-validator';
import { AssetType, AssetLicense } from '../entities/asset.entity';

export class CreateAssetDto {
  @ApiProperty({
    description: '资产名称',
    example: '现代办公桌',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({
    description: '资产描述',
    example: '一张现代风格的办公桌，适用于办公室场景',
  })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  description?: string;

  @ApiProperty({
    description: '资产类型',
    enum: AssetType,
    example: AssetType.MODEL_3D,
  })
  @IsEnum(AssetType)
  type: AssetType;

  @ApiPropertyOptional({
    description: '许可证类型',
    enum: AssetLicense,
    example: AssetLicense.FREE,
  })
  @IsOptional()
  @IsEnum(AssetLicense)
  license?: AssetLicense;

  @ApiProperty({
    description: '分类ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  categoryId: string;

  @ApiPropertyOptional({
    description: '标签ID列表',
    type: [String],
    example: ['tag1-uuid', 'tag2-uuid'],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  tagIds?: string[];

  @ApiPropertyOptional({
    description: '资产元数据',
    example: {
      dimensions: { width: 120, height: 75, depth: 60 },
      materials: ['wood', 'metal'],
      polyCount: 1500,
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class UpdateAssetDto {
  @ApiPropertyOptional({
    description: '资产名称',
    example: '现代办公桌 v2',
    minLength: 1,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  name?: string;

  @ApiPropertyOptional({
    description: '资产描述',
    example: '更新后的现代风格办公桌',
  })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  description?: string;

  @ApiPropertyOptional({
    description: '资产类型',
    enum: AssetType,
  })
  @IsOptional()
  @IsEnum(AssetType)
  type?: AssetType;

  @ApiPropertyOptional({
    description: '许可证类型',
    enum: AssetLicense,
  })
  @IsOptional()
  @IsEnum(AssetLicense)
  license?: AssetLicense;

  @ApiPropertyOptional({
    description: '分类ID',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({
    description: '标签ID列表',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  tagIds?: string[];

  @ApiPropertyOptional({
    description: '资产元数据',
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class AssetQueryDto {
  @ApiPropertyOptional({
    description: '搜索关键词',
    example: '办公桌',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: '资产类型过滤',
    enum: AssetType,
  })
  @IsOptional()
  @IsEnum(AssetType)
  type?: AssetType;

  @ApiPropertyOptional({
    description: '分类ID过滤',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({
    description: '标签ID过滤',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  tagIds?: string[];

  @ApiPropertyOptional({
    description: '许可证类型过滤',
    enum: AssetLicense,
  })
  @IsOptional()
  @IsEnum(AssetLicense)
  license?: AssetLicense;

  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    description: '每页数量',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  limit?: number = 20;

  @ApiPropertyOptional({
    description: '排序字段',
    example: 'createdAt',
    enum: ['name', 'createdAt', 'updatedAt', 'downloadCount', 'rating'],
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    description: '排序方向',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
