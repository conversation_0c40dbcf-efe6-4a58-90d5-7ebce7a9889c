/**
 * 事务API控制器
 */
import { Controller, Get, Post, Body, Param, Query, HttpException, HttpStatus } from '@nestjs/common';
import { TransactionApiService } from './transaction-api.service';
import { TransactionCoordinatorService } from '../transaction-coordinator.service';
import { TransactionMonitorService } from '../monitoring/transaction-monitor.service';

@Controller('transactions')
export class TransactionApiController {
  constructor(
    private readonly transactionApiService: TransactionApiService,
    private readonly transactionCoordinatorService: TransactionCoordinatorService,
    private readonly transactionMonitorService: TransactionMonitorService,
  ) {}
  
  /**
   * 获取事务统计信息
   */
  @Get('stats')
  getTransactionStats() {
    return this.transactionMonitorService.getTransactionStats();
  }
  
  /**
   * 获取活跃事务列表
   */
  @Get('active')
  getActiveTransactions() {
    return this.transactionMonitorService.getActiveTransactions();
  }
  
  /**
   * 获取事务历史
   * @param limit 限制数量
   * @param offset 偏移量
   */
  @Get('history')
  getTransactionHistory(
    @Query('limit') limit: number = 100,
    @Query('offset') offset: number = 0,
  ) {
    return this.transactionMonitorService.getTransactionHistory(limit, offset);
  }
  
  /**
   * 获取事务详情
   * @param id 事务ID
   */
  @Get(':id')
  getTransactionDetails(@Param('id') id: string) {
    const details = this.transactionMonitorService.getTransactionDetails(id);
    
    if (!details) {
      throw new HttpException(`事务 ${id} 不存在`, HttpStatus.NOT_FOUND);
    }
    
    return details;
  }
  
  /**
   * 手动提交事务
   * @param id 事务ID
   */
  @Post(':id/commit')
  async commitTransaction(@Param('id') id: string) {
    try {
      const result = await this.transactionApiService.commitTransaction(id);
      return result;
    } catch (error) {
      throw new HttpException(
        `提交事务失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  
  /**
   * 手动回滚事务
   * @param id 事务ID
   * @param reason 回滚原因
   */
  @Post(':id/rollback')
  async rollbackTransaction(
    @Param('id') id: string,
    @Body('reason') reason: string,
  ) {
    try {
      const result = await this.transactionApiService.rollbackTransaction(id, reason);
      return result;
    } catch (error) {
      throw new HttpException(
        `回滚事务失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  
  /**
   * 清理过期事务历史
   * @param maxAge 最大保留时间（毫秒）
   */
  @Post('cleanup')
  cleanupTransactionHistory(@Body('maxAge') maxAge: number = 24 * 60 * 60 * 1000) {
    this.transactionMonitorService.cleanupTransactionHistory(maxAge);
    return { success: true, message: `已清理过期事务历史，保留时间: ${maxAge}ms` };
  }
}
