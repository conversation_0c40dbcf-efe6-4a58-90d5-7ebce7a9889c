/**
 * 屏幕空间反射效果
 * 实现逼真的反射效果
 */
import * as THREE from 'three';
// 使用类型断言导入 ShaderPass
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { PostProcessingEffect, PostProcessingEffectOptions } from '../PostProcessingEffect';

/**
 * SSR效果选项
 */
export interface SSREffectOptions extends PostProcessingEffectOptions {
  /** 反射强度 */
  intensity?: number;
  /** 最大步数 */
  maxSteps?: number;
  /** 最大距离 */
  maxDistance?: number;
  /** 步长 */
  stride?: number;
  /** 粗糙度 */
  roughness?: number;
  /** 厚度 */
  thickness?: number;
  /** 是否使用法线贴图 */
  useNormalMap?: boolean;
  /** 是否使用粗糙度贴图 */
  useRoughnessMap?: boolean;
  /** 去噪迭代次数 */
  denoiseIterations?: number;
  /** 去噪核大小 */
  denoiseKernel?: number;
  /** 去噪漫反射强度 */
  denoiseDiffuse?: number;
  /** 去噪高光强度 */
  denoiseSpecular?: number;
  /** 半径 */
  radius?: number;
  /** Phi值 */
  phi?: number;
  /** 亮度Phi值 */
  lumaPhi?: number;
  /** 深度Phi值 */
  depthPhi?: number;
  /** 法线Phi值 */
  normalPhi?: number;
  /** 粗糙度Phi值 */
  roughnessPhi?: number;
  /** 高光Phi值 */
  specularPhi?: number;
  /** 环境模糊 */
  envBlur?: number;
  /** 是否使用重要性采样 */
  importanceSampling?: boolean;
  /** 精细步数 */
  refineSteps?: number;
  /** 分辨率缩放 */
  resolutionScale?: number;
  /** 是否显示丢失的光线 */
  missedRays?: boolean;
}

/**
 * 屏幕空间反射效果
 */
export class SSREffect extends PostProcessingEffect {
  /** 反射强度 */
  private intensity: number;

  /** 最大步数 */
  private maxSteps: number;

  /** 最大距离 */
  private maxDistance: number;

  /** 步长 */
  private stride: number;

  /** 粗糙度 */
  private roughness: number;

  /** 厚度 */
  private thickness: number;

  /** 是否使用法线贴图 */
  private useNormalMap: boolean;

  /** 是否使用粗糙度贴图 */
  private useRoughnessMap: boolean;

  /** 去噪迭代次数 */
  private denoiseIterations: number;

  /** 去噪核大小 */
  private denoiseKernel: number;

  /** 去噪漫反射强度 */
  private denoiseDiffuse: number;

  /** 去噪高光强度 */
  private denoiseSpecular: number;

  /** 半径 */
  private radius: number;

  /** Phi值 */
  private phi: number;

  /** 亮度Phi值 */
  private lumaPhi: number;

  /** 深度Phi值 */
  private depthPhi: number;

  /** 法线Phi值 */
  private normalPhi: number;

  /** 粗糙度Phi值 */
  private roughnessPhi: number;

  /** 高光Phi值 */
  private specularPhi: number;

  /** 环境模糊 */
  private envBlur: number;

  /** 是否使用重要性采样 */
  private importanceSampling: boolean;

  /** 精细步数 */
  private refineSteps: number;

  /** 分辨率缩放 */
  private resolutionScale: number;

  /** 是否显示丢失的光线 */
  private missedRays: boolean;

  /** SSR通道 */
  private ssrPass: ShaderPass | null = null;

  /** 场景 */
  private scene: THREE.Scene | null = null;

  /** 相机 */
  private camera: THREE.PerspectiveCamera | THREE.OrthographicCamera | null = null;

  /** 深度纹理 */
  private depthTexture: THREE.DepthTexture | null = null;

  /** 法线纹理 */
  private normalTexture: THREE.Texture | null = null;

  /** 粗糙度纹理 */
  private roughnessTexture: THREE.Texture | null = null;

  /** 渲染目标 */
  private renderTarget: THREE.WebGLRenderTarget | null = null;

  /**
   * 创建SSR效果
   * @param options SSR效果选项
   */
  constructor(options: SSREffectOptions = { name: 'SSR' }) {
    super(options);

    this.intensity = options.intensity !== undefined ? options.intensity : 1.0;
    this.maxSteps = options.maxSteps || 20;
    this.maxDistance = options.maxDistance || 50;
    this.stride = options.stride || 1;
    this.roughness = options.roughness !== undefined ? options.roughness : 0.5;
    this.thickness = options.thickness || 0.5;
    this.useNormalMap = options.useNormalMap !== undefined ? options.useNormalMap : true;
    this.useRoughnessMap = options.useRoughnessMap !== undefined ? options.useRoughnessMap : true;

    // 高级选项
    this.denoiseIterations = options.denoiseIterations || 1;
    this.denoiseKernel = options.denoiseKernel || 2;
    this.denoiseDiffuse = options.denoiseDiffuse || 10;
    this.denoiseSpecular = options.denoiseSpecular || 10;
    this.radius = options.radius || 3;
    this.phi = options.phi || 0.5;
    this.lumaPhi = options.lumaPhi || 5;
    this.depthPhi = options.depthPhi || 2;
    this.normalPhi = options.normalPhi || 50;
    this.roughnessPhi = options.roughnessPhi || 50;
    this.specularPhi = options.specularPhi || 50;
    this.envBlur = options.envBlur || 0.5;
    this.importanceSampling = options.importanceSampling !== undefined ? options.importanceSampling : true;
    this.refineSteps = options.refineSteps || 5;
    this.resolutionScale = options.resolutionScale || 1;
    this.missedRays = options.missedRays !== undefined ? options.missedRays : false;
  }

  /**
   * 创建通道
   */
  protected createPass(): void {
    // 如果没有场景或相机，则不创建通道
    if (!this.scene || !this.camera || !this.renderer) return;

    // 创建深度纹理
    this.depthTexture = new THREE.DepthTexture(this.width, this.height);
    this.depthTexture.type = THREE.UnsignedShortType;

    // 创建渲染目标
    this.renderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {
      minFilter: THREE.LinearFilter,
      magFilter: THREE.LinearFilter,
      format: THREE.RGBAFormat,
      depthTexture: this.depthTexture,
      depthBuffer: true
    });

    // 创建SSR着色器
    const ssrShader = {
      uniforms: {
        'tDiffuse': { value: null },
        'tDepth': { value: this.depthTexture },
        'tNormal': { value: this.normalTexture },
        'tRoughness': { value: this.roughnessTexture },
        'cameraNear': { value: this.camera.near },
        'cameraFar': { value: this.camera.far },
        'resolution': { value: new THREE.Vector2(this.width, this.height) },
        'cameraProjectionMatrix': { value: this.camera.projectionMatrix },
        'cameraInverseProjectionMatrix': { value: new THREE.Matrix4().copy(this.camera.projectionMatrix).invert() },
        'cameraWorldMatrix': { value: this.camera.matrixWorld },
        'intensity': { value: this.intensity },
        'maxSteps': { value: this.maxSteps },
        'maxDistance': { value: this.maxDistance },
        'stride': { value: this.stride },
        'roughness': { value: this.roughness },
        'thickness': { value: this.thickness },
        'useNormalMap': { value: this.useNormalMap ? 1 : 0 },
        'useRoughnessMap': { value: this.useRoughnessMap ? 1 : 0 },
        'denoiseIterations': { value: this.denoiseIterations },
        'denoiseKernel': { value: this.denoiseKernel },
        'denoiseDiffuse': { value: this.denoiseDiffuse },
        'denoiseSpecular': { value: this.denoiseSpecular },
        'radius': { value: this.radius },
        'phi': { value: this.phi },
        'lumaPhi': { value: this.lumaPhi },
        'depthPhi': { value: this.depthPhi },
        'normalPhi': { value: this.normalPhi },
        'roughnessPhi': { value: this.roughnessPhi },
        'specularPhi': { value: this.specularPhi },
        'envBlur': { value: this.envBlur },
        'importanceSampling': { value: this.importanceSampling ? 1 : 0 },
        'refineSteps': { value: this.refineSteps },
        'resolutionScale': { value: this.resolutionScale },
        'missedRays': { value: this.missedRays ? 1 : 0 },
        'time': { value: 0 }
      },
      vertexShader: /* glsl */`
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: /* glsl */`
        uniform sampler2D tDiffuse;
        uniform sampler2D tDepth;
        uniform sampler2D tNormal;
        uniform sampler2D tRoughness;
        uniform float cameraNear;
        uniform float cameraFar;
        uniform vec2 resolution;
        uniform mat4 cameraProjectionMatrix;
        uniform mat4 cameraInverseProjectionMatrix;
        uniform mat4 cameraWorldMatrix;
        uniform float intensity;
        uniform int maxSteps;
        uniform float maxDistance;
        uniform float stride;
        uniform float roughness;
        uniform float thickness;
        uniform int useNormalMap;
        uniform int useRoughnessMap;
        uniform int denoiseIterations;
        uniform int denoiseKernel;
        uniform float denoiseDiffuse;
        uniform float denoiseSpecular;
        uniform float radius;
        uniform float phi;
        uniform float lumaPhi;
        uniform float depthPhi;
        uniform float normalPhi;
        uniform float roughnessPhi;
        uniform float specularPhi;
        uniform float envBlur;
        uniform int importanceSampling;
        uniform int refineSteps;
        uniform float resolutionScale;
        uniform int missedRays;
        uniform float time;

        varying vec2 vUv;

        const float PI = 3.14159265359;

        // 从深度纹理中获取深度值
        float getDepth(vec2 uv) {
          return texture2D(tDepth, uv).r;
        }

        // 从法线纹理中获取法线
        vec3 getNormal(vec2 uv) {
          if (useNormalMap == 1) {
            return normalize(texture2D(tNormal, uv).rgb * 2.0 - 1.0);
          } else {
            // 使用深度纹理计算法线
            float depth = getDepth(uv);
            vec2 size = vec2(1.0 / resolution.x, 1.0 / resolution.y);

            float dx = getDepth(uv + vec2(size.x, 0.0)) - getDepth(uv - vec2(size.x, 0.0));
            float dy = getDepth(uv + vec2(0.0, size.y)) - getDepth(uv - vec2(0.0, size.y));

            vec3 normal = normalize(vec3(dx, dy, 0.1));
            return normal;
          }
        }

        // 从粗糙度纹理中获取粗糙度
        float getRoughness(vec2 uv) {
          if (useRoughnessMap == 1) {
            return texture2D(tRoughness, uv).r;
          } else {
            return roughness;
          }
        }

        // 将屏幕空间坐标转换为世界空间坐标
        vec3 screenToWorld(vec2 uv, float depth) {
          vec4 clipPos = vec4(uv * 2.0 - 1.0, depth * 2.0 - 1.0, 1.0);
          vec4 viewPos = cameraInverseProjectionMatrix * clipPos;
          viewPos /= viewPos.w;
          vec4 worldPos = cameraWorldMatrix * viewPos;
          return worldPos.xyz;
        }

        // 将世界空间坐标转换为屏幕空间坐标
        vec2 worldToScreen(vec3 worldPos) {
          vec4 clipPos = cameraProjectionMatrix * cameraWorldMatrix * vec4(worldPos, 1.0);
          clipPos.xy /= clipPos.w;
          return clipPos.xy * 0.5 + 0.5;
        }

        // 随机函数
        float random(vec2 co) {
          return fract(sin(dot(co, vec2(12.9898, 78.233))) * 43758.5453);
        }

        // 生成随机方向
        vec3 randomDirection(vec2 seed, vec3 normal, float roughnessFactor) {
          // 使用球面坐标生成随机方向
          float u = random(seed);
          float v = random(seed * 2.0);

          float theta = 2.0 * PI * u;
          float phi = acos(2.0 * v - 1.0);

          vec3 dir = vec3(
            sin(phi) * cos(theta),
            sin(phi) * sin(theta),
            cos(phi)
          );

          // 确保方向在法线半球内
          if (dot(dir, normal) < 0.0) {
            dir = -dir;
          }

          // 根据粗糙度调整方向
          if (roughnessFactor > 0.0) {
            vec3 reflectDir = reflect(normalize(cameraWorldMatrix[3].xyz - screenToWorld(vUv, getDepth(vUv))), normal);
            dir = normalize(mix(reflectDir, dir, roughnessFactor));
          }

          return dir;
        }

        // 重要性采样
        vec3 importanceSampleGGX(vec2 Xi, float roughnessFactor, vec3 normal) {
          float a = roughnessFactor * roughnessFactor;

          float phi = 2.0 * PI * Xi.x;
          float cosTheta = sqrt((1.0 - Xi.y) / (1.0 + (a*a - 1.0) * Xi.y));
          float sinTheta = sqrt(1.0 - cosTheta * cosTheta);

          // 从球面坐标转换为笛卡尔坐标
          vec3 H = vec3(
            sinTheta * cos(phi),
            sinTheta * sin(phi),
            cosTheta
          );

          // 从切线空间转换到世界空间
          vec3 up = abs(normal.z) < 0.999 ? vec3(0.0, 0.0, 1.0) : vec3(1.0, 0.0, 0.0);
          vec3 tangent = normalize(cross(up, normal));
          vec3 bitangent = cross(normal, tangent);

          return tangent * H.x + bitangent * H.y + normal * H.z;
        }

        // 光线行进
        vec4 traceReflection(vec3 origin, vec3 direction, float roughnessFactor) {
          vec3 currentPos = origin;
          vec2 currentUV = vUv;
          float currentDepth = getDepth(currentUV);

          // 添加随机偏移以减少走样
          float randomOffset = random(vUv + vec2(time, 0.0));
          float stepSize = stride * (1.0 + roughnessFactor * 2.0);

          // 使用重要性采样生成多个方向
          vec4 reflectionColor = vec4(0.0);
          int sampleCount = importanceSampling == 1 ? 4 : 1;

          for (int s = 0; s < sampleCount; s++) {
            vec3 sampleDir = direction;

            // 如果使用重要性采样，则生成多个采样方向
            if (importanceSampling == 1 && s > 0) {
              vec2 Xi = vec2(random(vUv + vec2(float(s), time)), random(vUv + vec2(time, float(s))));
              vec3 H = importanceSampleGGX(Xi, roughnessFactor, getNormal(vUv));
              sampleDir = reflect(normalize(origin - cameraWorldMatrix[3].xyz), H);
            }

            vec3 rayPos = origin;
            bool hitFound = false;

            // 主要光线追踪
            for (int i = 0; i < maxSteps; i++) {
              // 前进一步
              rayPos += sampleDir * stepSize * (1.0 + float(i) * 0.1);

              // 将当前位置转换为屏幕空间
              vec2 rayUV = worldToScreen(rayPos);

              // 检查是否超出屏幕
              if (rayUV.x < 0.0 || rayUV.x > 1.0 || rayUV.y < 0.0 || rayUV.y > 1.0) {
                break;
              }

              // 获取当前深度
              float sampledDepth = getDepth(rayUV);

              // 计算深度差异
              vec3 samplePos = screenToWorld(rayUV, sampledDepth);
              float depthDiff = rayPos.z - samplePos.z;

              // 如果深度差异在阈值内，则找到了交点
              if (depthDiff > 0.0 && depthDiff < thickness) {
                // 精细步骤，提高精度
                if (refineSteps > 0) {
                  vec3 refinedPos = rayPos;
                  float refinedStepSize = stepSize / float(refineSteps);

                  for (int j = 0; j < refineSteps; j++) {
                    refinedPos -= sampleDir * refinedStepSize;
                    vec2 refinedUV = worldToScreen(refinedPos);

                    if (refinedUV.x < 0.0 || refinedUV.x > 1.0 || refinedUV.y < 0.0 || refinedUV.y > 1.0) {
                      continue;
                    }

                    float refinedDepth = getDepth(refinedUV);
                    vec3 refinedSamplePos = screenToWorld(refinedUV, refinedDepth);
                    float refinedDepthDiff = refinedPos.z - refinedSamplePos.z;

                    if (refinedDepthDiff < 0.0) {
                      break;
                    }

                    if (refinedDepthDiff < thickness) {
                      rayPos = refinedPos;
                      rayUV = refinedUV;
                      break;
                    }
                  }
                }

                // 计算反射颜色
                vec4 sampleColor = texture2D(tDiffuse, rayUV);

                // 根据距离和粗糙度衰减反射强度
                float dist = distance(origin, rayPos);
                float attenuation = 1.0 - clamp(dist / maxDistance, 0.0, 1.0);
                attenuation *= 1.0 - roughnessFactor;

                reflectionColor += sampleColor * attenuation;
                hitFound = true;
                break;
              }
            }

            // 如果没有找到交点且启用了显示丢失的光线
            if (!hitFound && missedRays == 1) {
              // 使用环境模糊作为丢失光线的颜色
              reflectionColor += vec4(0.5, 0.5, 0.5, 1.0) * (1.0 - roughnessFactor) * envBlur;
            }
          }

          // 平均多个采样结果
          reflectionColor /= float(sampleCount);

          return reflectionColor;
        }

        // 去噪函数
        vec4 denoise(vec2 uv, vec4 color) {
          if (denoiseIterations <= 0) return color;

          vec4 result = color;
          float totalWeight = 1.0;

          // 获取中心像素的属性
          float centerDepth = getDepth(uv);
          vec3 centerNormal = getNormal(uv);
          float centerRoughness = getRoughness(uv);
          float centerLuma = dot(color.rgb, vec3(0.299, 0.587, 0.114));

          // 多次迭代去噪
          for (int iter = 0; iter < denoiseIterations; iter++) {
            // 在每次迭代中使用不同的核大小
            float kernelSize = float(denoiseKernel) * (1.0 - float(iter) / float(denoiseIterations));

            // 采样周围像素
            for (int y = -denoiseKernel; y <= denoiseKernel; y++) {
              for (int x = -denoiseKernel; x <= denoiseKernel; x++) {
                if (x == 0 && y == 0) continue;

                vec2 offset = vec2(float(x), float(y)) / resolution * kernelSize;
                vec2 sampleUV = uv + offset;

                // 检查采样点是否在屏幕内
                if (sampleUV.x < 0.0 || sampleUV.x > 1.0 || sampleUV.y < 0.0 || sampleUV.y > 1.0) {
                  continue;
                }

                // 获取采样点的属性
                vec4 sampleColor = texture2D(tDiffuse, sampleUV);
                float sampleDepth = getDepth(sampleUV);
                vec3 sampleNormal = getNormal(sampleUV);
                float sampleRoughness = getRoughness(sampleUV);
                float sampleLuma = dot(sampleColor.rgb, vec3(0.299, 0.587, 0.114));

                // 计算各种差异
                float depthDiff = abs(centerDepth - sampleDepth);
                float normalDiff = 1.0 - dot(centerNormal, sampleNormal);
                float roughnessDiff = abs(centerRoughness - sampleRoughness);
                float lumaDiff = abs(centerLuma - sampleLuma);

                // 计算权重
                float w_depth = exp(-depthDiff * depthPhi);
                float w_normal = exp(-normalDiff * normalPhi);
                float w_roughness = exp(-roughnessDiff * roughnessPhi);
                float w_luma = exp(-lumaDiff * lumaPhi);

                // 组合权重
                float weight = w_depth * w_normal * w_roughness * w_luma;

                // 累加结果
                result += sampleColor * weight;
                totalWeight += weight;
              }
            }
          }

          // 归一化结果
          if (totalWeight > 0.0) {
            result /= totalWeight;
          }

          return result;
        }

        void main() {
          // 获取原始颜色
          vec4 diffuse = texture2D(tDiffuse, vUv);

          // 获取深度
          float depth = getDepth(vUv);

          // 如果是背景，则不计算反射
          if (depth >= 1.0) {
            gl_FragColor = diffuse;
            return;
          }

          // 获取世界空间位置
          vec3 worldPos = screenToWorld(vUv, depth);

          // 获取法线
          vec3 normal = getNormal(vUv);

          // 获取视线方向
          vec3 viewDir = normalize(worldPos - cameraWorldMatrix[3].xyz);

          // 计算反射方向
          vec3 reflectionDir = reflect(viewDir, normal);

          // 获取粗糙度
          float roughnessFactor = getRoughness(vUv);

          // 追踪反射
          vec4 reflection = traceReflection(worldPos, reflectionDir, roughnessFactor);

          // 去噪
          reflection = denoise(vUv, reflection);

          // 混合原始颜色和反射颜色
          vec4 finalColor = mix(diffuse, reflection, reflection.a * intensity);

          // 应用去噪
          finalColor = mix(
            finalColor,
            denoise(vUv, finalColor),
            mix(denoiseDiffuse, denoiseSpecular, roughnessFactor) * 0.01
          );

          gl_FragColor = finalColor;
        }
      `
    };

    // 创建SSR通道
    this.ssrPass = new ShaderPass(ssrShader);

    // 设置通道
    this.pass = this.ssrPass;
  }

  /**
   * 设置场景和相机
   * @param scene 场景
   * @param camera 相机
   */
  public setSceneAndCamera(scene: THREE.Scene, camera: THREE.PerspectiveCamera | THREE.OrthographicCamera): void {
    this.scene = scene;
    this.camera = camera;

    // 如果已初始化，则重新创建通道
    if (this.initialized) {
      this.createPass();
    }
  }

  /**
   * 设置法线纹理
   * @param texture 法线纹理
   */
  public setNormalTexture(texture: THREE.Texture): void {
    this.normalTexture = texture;

    // 更新通道的法线纹理
    if (this.ssrPass && this.ssrPass.uniforms.tNormal) {
      this.ssrPass.uniforms.tNormal.value = texture;
    }
  }

  /**
   * 设置粗糙度纹理
   * @param texture 粗糙度纹理
   */
  public setRoughnessTexture(texture: THREE.Texture): void {
    this.roughnessTexture = texture;

    // 更新通道的粗糙度纹理
    if (this.ssrPass && this.ssrPass.uniforms.tRoughness) {
      this.ssrPass.uniforms.tRoughness.value = texture;
    }
  }

  /**
   * 设置反射强度
   * @param intensity 反射强度
   */
  public setIntensity(intensity: number): void {
    this.intensity = intensity;

    // 更新通道的反射强度
    if (this.ssrPass && this.ssrPass.uniforms.intensity) {
      this.ssrPass.uniforms.intensity.value = intensity;
    }
  }

  /**
   * 获取反射强度
   * @returns 反射强度
   */
  public getIntensity(): number {
    return this.intensity;
  }

  /**
   * 设置最大步数
   * @param steps 最大步数
   */
  public setMaxSteps(steps: number): void {
    this.maxSteps = steps;

    // 更新通道的最大步数
    if (this.ssrPass && this.ssrPass.uniforms.maxSteps) {
      this.ssrPass.uniforms.maxSteps.value = steps;
    }
  }

  /**
   * 获取最大步数
   * @returns 最大步数
   */
  public getMaxSteps(): number {
    return this.maxSteps;
  }

  /**
   * 设置最大距离
   * @param distance 最大距离
   */
  public setMaxDistance(distance: number): void {
    this.maxDistance = distance;

    // 更新通道的最大距离
    if (this.ssrPass && this.ssrPass.uniforms.maxDistance) {
      this.ssrPass.uniforms.maxDistance.value = distance;
    }
  }

  /**
   * 获取最大距离
   * @returns 最大距离
   */
  public getMaxDistance(): number {
    return this.maxDistance;
  }

  /**
   * 设置步长
   * @param stride 步长
   */
  public setStride(stride: number): void {
    this.stride = stride;

    // 更新通道的步长
    if (this.ssrPass && this.ssrPass.uniforms.stride) {
      this.ssrPass.uniforms.stride.value = stride;
    }
  }

  /**
   * 获取步长
   * @returns 步长
   */
  public getStride(): number {
    return this.stride;
  }

  /**
   * 设置粗糙度
   * @param roughness 粗糙度
   */
  public setRoughness(roughness: number): void {
    this.roughness = roughness;

    // 更新通道的粗糙度
    if (this.ssrPass && this.ssrPass.uniforms.roughness) {
      this.ssrPass.uniforms.roughness.value = roughness;
    }
  }

  /**
   * 获取粗糙度
   * @returns 粗糙度
   */
  public getRoughness(): number {
    return this.roughness;
  }

  /**
   * 设置厚度
   * @param thickness 厚度
   */
  public setThickness(thickness: number): void {
    this.thickness = thickness;

    // 更新通道的厚度
    if (this.ssrPass && this.ssrPass.uniforms.thickness) {
      this.ssrPass.uniforms.thickness.value = thickness;
    }
  }

  /**
   * 获取厚度
   * @returns 厚度
   */
  public getThickness(): number {
    return this.thickness;
  }

  /**
   * 设置是否使用法线贴图
   * @param use 是否使用
   */
  public setUseNormalMap(use: boolean): void {
    this.useNormalMap = use;

    // 更新通道的法线贴图使用标志
    if (this.ssrPass && this.ssrPass.uniforms.useNormalMap) {
      this.ssrPass.uniforms.useNormalMap.value = use ? 1 : 0;
    }
  }

  /**
   * 获取是否使用法线贴图
   * @returns 是否使用
   */
  public isUseNormalMap(): boolean {
    return this.useNormalMap;
  }

  /**
   * 设置是否使用粗糙度贴图
   * @param use 是否使用
   */
  public setUseRoughnessMap(use: boolean): void {
    this.useRoughnessMap = use;

    // 更新通道的粗糙度贴图使用标志
    if (this.ssrPass && this.ssrPass.uniforms.useRoughnessMap) {
      this.ssrPass.uniforms.useRoughnessMap.value = use ? 1 : 0;
    }
  }

  /**
   * 获取是否使用粗糙度贴图
   * @returns 是否使用
   */
  public isUseRoughnessMap(): boolean {
    return this.useRoughnessMap;
  }

  /**
   * 更新效果
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.camera || !this.ssrPass) return;

    // 更新相机相关的Uniforms
    const uniforms = this.ssrPass.uniforms;
    if (uniforms.cameraNear) uniforms.cameraNear.value = this.camera.near;
    if (uniforms.cameraFar) uniforms.cameraFar.value = this.camera.far;
    if (uniforms.cameraProjectionMatrix) uniforms.cameraProjectionMatrix.value = this.camera.projectionMatrix;
    if (uniforms.cameraInverseProjectionMatrix) {
      uniforms.cameraInverseProjectionMatrix.value.copy(this.camera.projectionMatrix).invert();
    }
    if (uniforms.cameraWorldMatrix) uniforms.cameraWorldMatrix.value = this.camera.matrixWorld;

    // 更新时间，用于随机化采样
    if (uniforms.time) {
      uniforms.time.value = (uniforms.time.value || 0) + deltaTime;
    }
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    super.resize(width, height);

    // 更新渲染目标大小
    if (this.renderTarget) {
      this.renderTarget.setSize(width, height);
    }

    // 更新分辨率Uniform
    if (this.ssrPass && this.ssrPass.uniforms.resolution) {
      this.ssrPass.uniforms.resolution.value.set(width, height);
    }
  }

  /**
   * 销毁效果
   */
  public dispose(): void {
    // 销毁渲染目标
    if (this.renderTarget) {
      (this.renderTarget as any).dispose();
      this.renderTarget = null;
    }

    // 销毁深度纹理
    if (this.depthTexture) {
      (this.depthTexture as any).dispose();
      this.depthTexture = null;
    }

    super.dispose();
  }
}
