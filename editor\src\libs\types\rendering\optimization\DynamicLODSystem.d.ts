import { EnhancedLODSystem, EnhancedLODSystemOptions } from './EnhancedLODSystem';
/**
 * 动态LOD系统事件类型
 */
export declare enum DynamicLODSystemEventType {
    /** LOD级别调整 */
    LOD_LEVEL_ADJUSTED = "lod_level_adjusted",
    /** 性能目标调整 */
    PERFORMANCE_TARGET_ADJUSTED = "performance_target_adjusted",
    /** 质量级别调整 */
    QUALITY_LEVEL_ADJUSTED = "quality_level_adjusted"
}
/**
 * 动态LOD系统配置接口
 */
export interface DynamicLODSystemOptions extends EnhancedLODSystemOptions {
    /** 是否启用动态LOD */
    useDynamicLOD?: boolean;
    /** 目标帧率 */
    targetFPS?: number;
    /** 最小帧率 */
    minFPS?: number;
    /** 最大帧率 */
    maxFPS?: number;
    /** 调整灵敏度 */
    adjustmentSensitivity?: number;
    /** 调整间隔（毫秒） */
    adjustmentInterval?: number;
    /** 是否使用平滑过渡 */
    useSmoothTransition?: boolean;
    /** 过渡时间（毫秒） */
    transitionTime?: number;
    /** 质量级别 (0-1) */
    qualityLevel?: number;
    /** 是否使用自动质量调整 */
    useAutoQualityAdjustment?: boolean;
    /** 是否使用距离偏移 */
    useDistanceOffset?: boolean;
    /** 距离偏移系数 */
    distanceOffsetFactor?: number;
}
/**
 * 动态LOD系统
 * 根据性能和视距动态调整LOD级别
 */
export declare class DynamicLODSystem extends EnhancedLODSystem {
    /** 是否启用动态LOD */
    private useDynamicLOD;
    /** 目标帧率 */
    private targetFPS;
    /** 最小帧率 */
    private minFPS;
    /** 最大帧率 */
    private maxFPS;
    /** 调整灵敏度 */
    private adjustmentSensitivity;
    /** 调整间隔（毫秒） */
    private adjustmentInterval;
    /** 上次调整时间 */
    private lastAdjustmentTime;
    /** 是否使用平滑过渡 */
    private useSmoothTransition;
    /** 过渡时间（毫秒） */
    private transitionTime;
    /** 质量级别 (0-1) */
    private qualityLevel;
    /** 是否使用自动质量调整 */
    private useAutoQualityAdjustment;
    /** 是否使用距离偏移 */
    private useDistanceOffset;
    /** 距离偏移系数 */
    private distanceOffsetFactor;
    /** 性能监控器 */
    private performanceMonitor;
    /** 当前距离偏移 */
    private currentDistanceOffset;
    /** 动态LOD事件发射器 */
    private dynamicLODEventEmitter;
    /**
     * 创建动态LOD系统
     * @param options 系统选项
     */
    constructor(options?: DynamicLODSystemOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新动态LOD
     */
    private updateDynamicLOD;
    /**
     * 调整质量级别
     * @param currentFPS 当前FPS
     */
    private adjustQualityLevel;
    /**
     * 调整距离偏移
     */
    private adjustDistanceOffset;
    /**
     * 更新LOD距离
     */
    private updateLODDistances;
    /**
     * 设置目标帧率
     * @param fps 目标帧率
     */
    setTargetFPS(fps: number): void;
    /**
     * 获取目标帧率
     * @returns 目标帧率
     */
    getTargetFPS(): number;
    /**
     * 设置质量级别
     * @param level 质量级别 (0-1)
     */
    setQualityLevel(level: number): void;
    /**
     * 获取质量级别
     * @returns 质量级别 (0-1)
     */
    getQualityLevel(): number;
    /**
     * 启用/禁用动态LOD
     * @param enabled 是否启用
     */
    setDynamicLODEnabled(enabled: boolean): void;
    /**
     * 获取动态LOD是否启用
     * @returns 是否启用
     */
    isDynamicLODEnabled(): boolean;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: string, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: string, listener: (...args: any[]) => void): void;
}
