/**
 * 分布式事务集成测试
 */
import axios from 'axios';
import {
  TEST_CONFIG,
  createTestUser,
  loginTestUser,
  createTestProject,
  cleanupTestData,
} from '../setup';

describe('分布式事务集成测试', () => {
  // 测试数据
  const testUser = {
    username: `test_user_${Date.now()}`,
    password: 'Test@123456',
    email: `test_user_${Date.now()}@example.com`,
  };
  
  let authToken: string;
  let userId: string;
  
  // 在所有测试开始前创建测试用户
  beforeAll(async () => {
    try {
      // 创建测试用户
      await createTestUser(
        testUser.username,
        testUser.password,
        testUser.email
      );
      
      // 登录获取令牌
      const loginData = await loginTestUser(testUser.username, testUser.password);
      authToken = loginData.accessToken;
      userId = loginData.user.id;
      
      expect(authToken).toBeDefined();
      expect(userId).toBeDefined();
    } catch (error) {
      console.error('测试准备失败:', error);
      throw error;
    }
  });
  
  // 测试成功的事务
  describe('成功的事务', () => {
    it('应该能够成功完成跨服务事务', async () => {
      // 创建项目和资产的事务
      const transactionData = {
        projectData: {
          name: `transaction_project_${Date.now()}`,
          description: '事务测试项目',
        },
        assetData: {
          name: `transaction_asset_${Date.now()}`,
          type: 'texture',
          metadata: {
            width: 512,
            height: 512,
            format: 'png',
          },
        },
      };
      
      // 开始事务
      const startResponse = await axios.post(
        `${TEST_CONFIG.serviceRegistry.url}/transactions/start`,
        {
          participants: ['project-service', 'asset-service'],
          data: transactionData,
        },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(startResponse.status).toBe(200);
      expect(startResponse.data.transactionId).toBeDefined();
      
      const transactionId = startResponse.data.transactionId;
      
      // 等待事务完成
      let transactionStatus = 'started';
      let retries = 0;
      const maxRetries = 10;
      
      while (transactionStatus !== 'committed' && transactionStatus !== 'aborted' && retries < maxRetries) {
        const statusResponse = await axios.get(
          `${TEST_CONFIG.serviceRegistry.url}/transactions/${transactionId}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
          }
        );
        
        transactionStatus = statusResponse.data.status;
        retries++;
        
        if (transactionStatus !== 'committed' && transactionStatus !== 'aborted') {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      expect(transactionStatus).toBe('committed');
      
      // 验证项目是否创建成功
      const projectsResponse = await axios.get(
        `${TEST_CONFIG.projectService.url}/projects`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      const createdProject = projectsResponse.data.find(
        (p: any) => p.name === transactionData.projectData.name
      );
      
      expect(createdProject).toBeDefined();
      expect(createdProject.description).toBe(transactionData.projectData.description);
      
      // 验证资产是否创建成功
      const assetsResponse = await axios.get(
        `${TEST_CONFIG.assetService.url}/assets`,
        {
          params: { projectId: createdProject.id },
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      const createdAsset = assetsResponse.data.find(
        (a: any) => a.name === transactionData.assetData.name
      );
      
      expect(createdAsset).toBeDefined();
      expect(createdAsset.type).toBe(transactionData.assetData.type);
      expect(createdAsset.projectId).toBe(createdProject.id);
      
      // 清理测试数据
      await cleanupTestData(authToken, createdProject.id);
    });
  });
  
  // 测试失败的事务
  describe('失败的事务', () => {
    it('应该能够在准备阶段失败时回滚事务', async () => {
      // 创建无效数据的事务（项目名称为空）
      const transactionData = {
        projectData: {
          name: '', // 无效的项目名称
          description: '事务测试项目',
        },
        assetData: {
          name: `transaction_asset_${Date.now()}`,
          type: 'texture',
          metadata: {
            width: 512,
            height: 512,
            format: 'png',
          },
        },
      };
      
      // 开始事务
      const startResponse = await axios.post(
        `${TEST_CONFIG.serviceRegistry.url}/transactions/start`,
        {
          participants: ['project-service', 'asset-service'],
          data: transactionData,
        },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      expect(startResponse.status).toBe(200);
      expect(startResponse.data.transactionId).toBeDefined();
      
      const transactionId = startResponse.data.transactionId;
      
      // 等待事务完成
      let transactionStatus = 'started';
      let retries = 0;
      const maxRetries = 10;
      
      while (transactionStatus !== 'committed' && transactionStatus !== 'aborted' && retries < maxRetries) {
        const statusResponse = await axios.get(
          `${TEST_CONFIG.serviceRegistry.url}/transactions/${transactionId}`,
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
          }
        );
        
        transactionStatus = statusResponse.data.status;
        retries++;
        
        if (transactionStatus !== 'committed' && transactionStatus !== 'aborted') {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      expect(transactionStatus).toBe('aborted');
      
      // 验证项目是否未创建
      const projectsResponse = await axios.get(
        `${TEST_CONFIG.projectService.url}/projects`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      const createdProject = projectsResponse.data.find(
        (p: any) => p.description === transactionData.projectData.description
      );
      
      expect(createdProject).toBeUndefined();
    });
  });
  
  // 测试并发事务
  describe('并发事务', () => {
    it('应该能够正确处理并发事务', async () => {
      // 创建多个并发事务
      const transactionCount = 5;
      const transactions = [];
      
      for (let i = 0; i < transactionCount; i++) {
        transactions.push({
          projectData: {
            name: `concurrent_project_${Date.now()}_${i}`,
            description: `并发事务测试项目 ${i}`,
          },
          assetData: {
            name: `concurrent_asset_${Date.now()}_${i}`,
            type: 'texture',
            metadata: {
              width: 512,
              height: 512,
              format: 'png',
            },
          },
        });
      }
      
      // 并发启动事务
      const startPromises = transactions.map(transactionData =>
        axios.post(
          `${TEST_CONFIG.serviceRegistry.url}/transactions/start`,
          {
            participants: ['project-service', 'asset-service'],
            data: transactionData,
          },
          {
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
          }
        )
      );
      
      const startResponses = await Promise.all(startPromises);
      const transactionIds = startResponses.map(response => response.data.transactionId);
      
      // 等待所有事务完成
      const checkTransactionStatus = async (transactionId: string) => {
        let status = 'started';
        let retries = 0;
        const maxRetries = 10;
        
        while (status !== 'committed' && status !== 'aborted' && retries < maxRetries) {
          const statusResponse = await axios.get(
            `${TEST_CONFIG.serviceRegistry.url}/transactions/${transactionId}`,
            {
              headers: {
                Authorization: `Bearer ${authToken}`,
              },
            }
          );
          
          status = statusResponse.data.status;
          retries++;
          
          if (status !== 'committed' && status !== 'aborted') {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
        
        return status;
      };
      
      const statusPromises = transactionIds.map(checkTransactionStatus);
      const statuses = await Promise.all(statusPromises);
      
      // 验证所有事务都已完成
      expect(statuses.every(status => status === 'committed' || status === 'aborted')).toBe(true);
      
      // 验证成功的事务数量
      const committedCount = statuses.filter(status => status === 'committed').length;
      expect(committedCount).toBeGreaterThan(0);
      
      // 清理测试数据
      const projectsResponse = await axios.get(
        `${TEST_CONFIG.projectService.url}/projects`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      );
      
      for (let i = 0; i < transactionCount; i++) {
        const createdProject = projectsResponse.data.find(
          (p: any) => p.name === transactions[i].projectData.name
        );
        
        if (createdProject) {
          await cleanupTestData(authToken, createdProject.id);
        }
      }
    });
  });
});
