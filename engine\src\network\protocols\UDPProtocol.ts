/**
 * UDP协议实现
 * 使用WebRTC数据通道的无序、不可靠模式模拟UDP行为
 */
import { NetworkProtocol, NetworkProtocolType, NetworkProtocolState, NetworkProtocolOptions } from './NetworkProtocol';
import { Debug } from '../../utils/Debug';

/**
 * UDP协议配置选项
 */
export interface UDPProtocolOptions extends NetworkProtocolOptions {
  /** ICE服务器配置 */
  iceServers?: RTCIceServer[];
  /** 信令服务器URL */
  signalingServerUrl?: string;
  /** 最大消息大小（字节） */
  maxMessageSize?: number;
  /** 是否启用有序传输 */
  ordered?: boolean;
  /** 最大重传次数 */
  maxRetransmits?: number;
}

/**
 * UDP协议实现
 * 使用WebRTC数据通道的无序、不可靠模式模拟UDP行为
 */
export class UDPProtocol extends NetworkProtocol {
  /** 对等连接 */
  private peerConnection: RTCPeerConnection | null = null;
  
  /** 数据通道 */
  private dataChannel: RTCDataChannel | null = null;
  
  /** 信令WebSocket */
  private signalingSocket: WebSocket | null = null;
  
  /** 远程对等ID */
  private remotePeerId: string | null = null;
  
  /** 本地对等ID */
  private localPeerId: string | null = null;
  
  /** 服务器URL */
  private serverUrl: string = '';
  
  /** 待发送的消息队列 */
  private messageQueue: any[] = [];
  
  /** 是否正在协商 */
  private isNegotiating: boolean = false;
  
  /** 是否是发起方 */
  private isInitiator: boolean = false;
  
  /**
   * 创建UDP协议
   * @param options 配置选项
   */
  constructor(options: UDPProtocolOptions = {}) {
    super(NetworkProtocolType.UDP, options);
    
    // 扩展默认配置
    this.options = {
      ...this.options,
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
      signalingServerUrl: 'wss://localhost:8080/signaling',
      maxMessageSize: 65536, // 64KB
      ordered: false, // 无序传输
      maxRetransmits: 0, // 不重传
      ...options,
    };
  }
  
  /**
   * 连接到服务器
   * @param url 服务器URL（信令服务器URL）
   * @returns 连接Promise
   */
  public async connect(url: string): Promise<void> {
    if (this.isConnected() || this.isConnecting()) {
      return;
    }
    
    this.serverUrl = url || this.options.signalingServerUrl as string;
    
    if (!this.serverUrl) {
      throw new Error('Signaling server URL is required');
    }
    
    this.setState(NetworkProtocolState.CONNECTING);
    
    // 设置连接超时
    this.setConnectionTimeout();
    
    try {
      // 连接到信令服务器
      await this.connectToSignalingServer();
      
      // 创建对等连接
      this.createPeerConnection();
      
      // 如果是发起方，则创建数据通道
      if (this.isInitiator) {
        this.createDataChannel();
      }
    } catch (error) {
      this.setState(NetworkProtocolState.ERROR);
      this.cancelConnectionTimeout();
      throw error;
    }
  }
  
  /**
   * 断开连接
   * @returns 断开连接Promise
   */
  public async disconnect(): Promise<void> {
    if (this.isDisconnected()) {
      return;
    }
    
    this.setState(NetworkProtocolState.DISCONNECTING);
    
    // 关闭数据通道
    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }
    
    // 关闭对等连接
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    
    // 关闭信令WebSocket
    if (this.signalingSocket) {
      this.signalingSocket.close();
      this.signalingSocket = null;
    }
    
    // 重置状态
    this.remotePeerId = null;
    this.isNegotiating = false;
    this.messageQueue = [];
    
    // 停止心跳
    this.stopHeartbeat();
    
    // 取消重连
    this.cancelReconnect();
    
    // 取消连接超时
    this.cancelConnectionTimeout();
    
    this.setState(NetworkProtocolState.DISCONNECTED);
  }
  
  /**
   * 发送数据
   * @param data 要发送的数据
   * @returns 是否成功发送
   */
  public send(data: any): boolean {
    if (!this.isConnected() || !this.dataChannel) {
      // 如果未连接，则将消息加入队列
      this.messageQueue.push(data);
      return false;
    }
    
    try {
      // 序列化数据
      const serializedData = typeof data === 'string' ? data : JSON.stringify(data);
      
      // 发送数据
      this.dataChannel.send(serializedData);
      
      // 记录发送的数据
      this.recordSentData(serializedData);
      
      return true;
    } catch (error) {
      if (this.options.enableDebugLog) {
        Debug.error('UDPProtocol', 'Failed to send data:', error);
      }
      
      // 将消息加入队列
      this.messageQueue.push(data);
      
      return false;
    }
  }
  
  /**
   * 连接到信令服务器
   * @returns 连接Promise
   */
  private connectToSignalingServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 创建WebSocket连接
      this.signalingSocket = new WebSocket(this.serverUrl);
      
      // 连接打开事件
      this.signalingSocket.onopen = () => {
        if (this.options.enableDebugLog) {
          Debug.log('UDPProtocol', 'Connected to signaling server');
        }
        
        // 生成本地对等ID
        this.localPeerId = `peer_${Math.random().toString(36).substring(2, 9)}`;
        
        // 发送加入消息
        this.sendSignalingMessage({
          type: 'join',
          peerId: this.localPeerId,
        });
        
        resolve();
      };
      
      // 连接错误事件
      this.signalingSocket.onerror = (error) => {
        if (this.options.enableDebugLog) {
          Debug.error('UDPProtocol', 'Signaling server connection error:', error);
        }
        
        reject(error);
      };
      
      // 连接关闭事件
      this.signalingSocket.onclose = () => {
        if (this.options.enableDebugLog) {
          Debug.log('UDPProtocol', 'Disconnected from signaling server');
        }
        
        // 如果不是主动断开连接，则尝试重连
        if (this.state !== NetworkProtocolState.DISCONNECTING && this.state !== NetworkProtocolState.DISCONNECTED) {
          this.setState(NetworkProtocolState.DISCONNECTED);
          
          if (this.options.autoReconnect) {
            this.attemptReconnect();
          }
        }
      };
      
      // 消息事件
      this.signalingSocket.onmessage = (event) => {
        this.handleSignalingMessage(event.data);
      };
    });
  }
  
  /**
   * 发送信令消息
   * @param message 信令消息
   */
  private sendSignalingMessage(message: any): void {
    if (!this.signalingSocket || this.signalingSocket.readyState !== WebSocket.OPEN) {
      return;
    }
    
    try {
      this.signalingSocket.send(JSON.stringify(message));
    } catch (error) {
      if (this.options.enableDebugLog) {
        Debug.error('UDPProtocol', 'Failed to send signaling message:', error);
      }
    }
  }
  
  /**
   * 处理信令消息
   * @param data 消息数据
   */
  private handleSignalingMessage(data: string): void {
    try {
      const message = JSON.parse(data);
      
      switch (message.type) {
        case 'joined':
          // 加入成功
          if (this.options.enableDebugLog) {
            Debug.log('UDPProtocol', 'Joined signaling server');
          }
          break;
          
        case 'peer_joined':
          // 对等方加入
          this.remotePeerId = message.peerId;
          this.isInitiator = true;
          
          if (this.options.enableDebugLog) {
            Debug.log('UDPProtocol', `Peer joined: ${this.remotePeerId}`);
          }
          
          // 创建提议
          this.createOffer();
          break;
          
        case 'offer':
          // 收到提议
          if (this.options.enableDebugLog) {
            Debug.log('UDPProtocol', 'Received offer');
          }
          
          this.remotePeerId = message.peerId;
          this.isInitiator = false;
          
          // 设置远程描述
          this.setRemoteDescription(message.sdp);
          break;
          
        case 'answer':
          // 收到应答
          if (this.options.enableDebugLog) {
            Debug.log('UDPProtocol', 'Received answer');
          }
          
          // 设置远程描述
          this.setRemoteDescription(message.sdp);
          break;
          
        case 'candidate':
          // 收到ICE候选
          if (this.options.enableDebugLog) {
            Debug.log('UDPProtocol', 'Received ICE candidate');
          }
          
          // 添加ICE候选
          this.addIceCandidate(message.candidate);
          break;
      }
    } catch (error) {
      if (this.options.enableDebugLog) {
        Debug.error('UDPProtocol', 'Failed to handle signaling message:', error);
      }
    }
  }
  
  /**
   * 创建对等连接
   */
  private createPeerConnection(): void {
    // 创建RTCPeerConnection
    this.peerConnection = new RTCPeerConnection({
      iceServers: this.options.iceServers as RTCIceServer[],
    });
    
    // ICE候选事件
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        // 发送ICE候选到对等方
        this.sendSignalingMessage({
          type: 'candidate',
          peerId: this.localPeerId,
          candidate: event.candidate,
        });
      }
    };
    
    // ICE连接状态变更事件
    this.peerConnection.oniceconnectionstatechange = () => {
      if (this.options.enableDebugLog) {
        Debug.log('UDPProtocol', `ICE connection state: ${this.peerConnection?.iceConnectionState}`);
      }
      
      switch (this.peerConnection?.iceConnectionState) {
        case 'connected':
        case 'completed':
          if (this.state !== NetworkProtocolState.CONNECTED) {
            this.setState(NetworkProtocolState.CONNECTED);
            this.cancelConnectionTimeout();
            this.reconnectAttempts = 0;
            
            // 启动心跳
            this.startHeartbeat();
            
            // 发送队列中的消息
            this.flushMessageQueue();
          }
          break;
          
        case 'failed':
        case 'disconnected':
        case 'closed':
          if (this.state === NetworkProtocolState.CONNECTED) {
            this.setState(NetworkProtocolState.DISCONNECTED);
            
            // 尝试重连
            if (this.options.autoReconnect) {
              this.attemptReconnect();
            }
          }
          break;
      }
    };
    
    // 协商需要事件
    this.peerConnection.onnegotiationneeded = () => {
      if (this.isInitiator && !this.isNegotiating) {
        this.isNegotiating = true;
        this.createOffer();
      }
    };
    
    // 数据通道事件
    this.peerConnection.ondatachannel = (event) => {
      if (this.options.enableDebugLog) {
        Debug.log('UDPProtocol', 'Data channel received');
      }
      
      this.dataChannel = event.channel;
      this.setupDataChannel();
    };
  }
  
  /**
   * 创建数据通道
   */
  private createDataChannel(): void {
    if (!this.peerConnection) {
      return;
    }
    
    // 创建数据通道
    this.dataChannel = this.peerConnection.createDataChannel('udp', {
      ordered: this.options.ordered as boolean,
      maxRetransmits: this.options.maxRetransmits as number,
    });
    
    this.setupDataChannel();
  }
  
  /**
   * 设置数据通道事件
   */
  private setupDataChannel(): void {
    if (!this.dataChannel) {
      return;
    }
    
    // 打开事件
    this.dataChannel.onopen = () => {
      if (this.options.enableDebugLog) {
        Debug.log('UDPProtocol', 'Data channel opened');
      }
      
      if (this.state !== NetworkProtocolState.CONNECTED) {
        this.setState(NetworkProtocolState.CONNECTED);
        this.cancelConnectionTimeout();
        this.reconnectAttempts = 0;
        
        // 启动心跳
        this.startHeartbeat();
        
        // 发送队列中的消息
        this.flushMessageQueue();
      }
    };
    
    // 关闭事件
    this.dataChannel.onclose = () => {
      if (this.options.enableDebugLog) {
        Debug.log('UDPProtocol', 'Data channel closed');
      }
      
      if (this.state === NetworkProtocolState.CONNECTED) {
        this.setState(NetworkProtocolState.DISCONNECTED);
        
        // 尝试重连
        if (this.options.autoReconnect) {
          this.attemptReconnect();
        }
      }
    };
    
    // 错误事件
    this.dataChannel.onerror = (error) => {
      if (this.options.enableDebugLog) {
        Debug.error('UDPProtocol', 'Data channel error:', error);
      }
      
      if (this.state === NetworkProtocolState.CONNECTED) {
        this.setState(NetworkProtocolState.ERROR);
        
        // 尝试重连
        if (this.options.autoReconnect) {
          this.attemptReconnect();
        }
      }
    };
    
    // 消息事件
    this.dataChannel.onmessage = (event) => {
      // 记录接收的数据
      this.recordReceivedData(event.data);
      
      try {
        // 解析数据
        const data = JSON.parse(event.data);
        
        // 处理心跳消息
        if (data.type === 'heartbeat') {
          // 发送心跳响应
          this.send({ type: 'heartbeat_response', timestamp: data.timestamp });
          return;
        }
        
        // 处理心跳响应消息
        if (data.type === 'heartbeat_response') {
          const rtt = Date.now() - data.timestamp;
          
          // 更新RTT统计
          this.updateRttStats(rtt);
          return;
        }
        
        // 触发消息事件
        this.emit('message', data);
      } catch (error) {
        // 如果不是JSON，则直接触发消息事件
        this.emit('message', event.data);
      }
    };
  }
  
  /**
   * 创建提议
   */
  private async createOffer(): Promise<void> {
    if (!this.peerConnection) {
      return;
    }
    
    try {
      // 创建提议
      const offer = await this.peerConnection.createOffer();
      
      // 设置本地描述
      await this.peerConnection.setLocalDescription(offer);
      
      // 发送提议到对等方
      this.sendSignalingMessage({
        type: 'offer',
        peerId: this.localPeerId,
        sdp: offer,
      });
    } catch (error) {
      if (this.options.enableDebugLog) {
        Debug.error('UDPProtocol', 'Failed to create offer:', error);
      }
      
      this.setState(NetworkProtocolState.ERROR);
    } finally {
      this.isNegotiating = false;
    }
  }
  
  /**
   * 创建应答
   */
  private async createAnswer(): Promise<void> {
    if (!this.peerConnection) {
      return;
    }
    
    try {
      // 创建应答
      const answer = await this.peerConnection.createAnswer();
      
      // 设置本地描述
      await this.peerConnection.setLocalDescription(answer);
      
      // 发送应答到对等方
      this.sendSignalingMessage({
        type: 'answer',
        peerId: this.localPeerId,
        sdp: answer,
      });
    } catch (error) {
      if (this.options.enableDebugLog) {
        Debug.error('UDPProtocol', 'Failed to create answer:', error);
      }
      
      this.setState(NetworkProtocolState.ERROR);
    } finally {
      this.isNegotiating = false;
    }
  }
  
  /**
   * 设置远程描述
   * @param sdp 会话描述
   */
  private async setRemoteDescription(sdp: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection) {
      return;
    }
    
    try {
      // 设置远程描述
      await this.peerConnection.setRemoteDescription(new RTCSessionDescription(sdp));
      
      // 如果是提议，则创建应答
      if (sdp.type === 'offer') {
        this.createAnswer();
      }
    } catch (error) {
      if (this.options.enableDebugLog) {
        Debug.error('UDPProtocol', 'Failed to set remote description:', error);
      }
      
      this.setState(NetworkProtocolState.ERROR);
    }
  }
  
  /**
   * 添加ICE候选
   * @param candidate ICE候选
   */
  private async addIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {
    if (!this.peerConnection) {
      return;
    }
    
    try {
      // 添加ICE候选
      await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
    } catch (error) {
      if (this.options.enableDebugLog) {
        Debug.error('UDPProtocol', 'Failed to add ICE candidate:', error);
      }
    }
  }
  
  /**
   * 更新RTT统计
   * @param rtt 往返时间（毫秒）
   */
  private updateRttStats(rtt: number): void {
    // 更新最小RTT
    if (rtt < this.stats.minRtt) {
      this.stats.minRtt = rtt;
    }
    
    // 更新最大RTT
    if (rtt > this.stats.maxRtt) {
      this.stats.maxRtt = rtt;
    }
    
    // 更新平均RTT
    const oldAverage = this.stats.averageRtt;
    const messageCount = this.stats.messagesReceived;
    
    if (messageCount > 0) {
      this.stats.averageRtt = oldAverage + (rtt - oldAverage) / messageCount;
    } else {
      this.stats.averageRtt = rtt;
    }
    
    // 触发RTT更新事件
    this.emit('rttUpdate', rtt);
  }
  
  /**
   * 发送队列中的消息
   */
  private flushMessageQueue(): void {
    if (!this.isConnected() || !this.dataChannel) {
      return;
    }
    
    // 发送队列中的所有消息
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }
}
