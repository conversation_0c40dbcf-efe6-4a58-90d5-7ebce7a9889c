/**
 * 点对点约束
 * 将两个物体通过一个点连接起来
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { PhysicsConstraint } from './PhysicsConstraint';
/**
 * 点对点约束选项
 */
export interface PointToPointConstraintOptions {
    /** 源物体上的连接点（局部坐标） */
    pivotA?: THREE.Vector3;
    /** 目标物体上的连接点（局部坐标） */
    pivotB?: THREE.Vector3;
    /** 最大力 */
    maxForce?: number;
    /** 是否允许连接的物体之间碰撞 */
    collideConnected?: boolean;
}
/**
 * 点对点约束
 */
export declare class PointToPointConstraint extends PhysicsConstraint {
    /** 组件类型 */
    static readonly type: string;
    /** 源物体上的连接点（局部坐标） */
    private pivotA;
    /** 目标物体上的连接点（局部坐标） */
    private pivotB;
    /** 最大力 */
    private maxForce;
    /**
     * 创建点对点约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    constructor(targetEntity?: Entity | null, options?: PointToPointConstraintOptions);
    /**
     * 创建约束
     */
    protected createConstraint(): void;
    /**
     * 设置源物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    setPivotA(pivot: THREE.Vector3): void;
    /**
     * 获取源物体上的连接点
     * @returns 连接点（局部坐标）
     */
    getPivotA(): THREE.Vector3;
    /**
     * 设置目标物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    setPivotB(pivot: THREE.Vector3): void;
    /**
     * 获取目标物体上的连接点
     * @returns 连接点（局部坐标）
     */
    getPivotB(): THREE.Vector3;
    /**
     * 设置最大力
     * @param maxForce 最大力
     */
    setMaxForce(maxForce: number): void;
    /**
     * 获取最大力
     * @returns 最大力
     */
    getMaxForce(): number;
}
