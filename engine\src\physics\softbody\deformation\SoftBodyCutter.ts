/**
 * 软体切割系统
 * 实现软体的切割和撕裂功能
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { SoftBodyComponent, SoftBodyType } from '../SoftBodyComponent';

/**
 * 切割平面
 */
export interface CuttingPlane {
  /** 平面法线 */
  normal: THREE.Vector3;
  /** 平面点 */
  point: THREE.Vector3;
}

/**
 * 切割射线
 */
export interface CuttingRay {
  /** 射线起点 */
  origin: THREE.Vector3;
  /** 射线方向 */
  direction: THREE.Vector3;
  /** 射线长度 */
  length: number;
}

/**
 * 软体切割系统选项
 */
export interface SoftBodyCutterOptions {
  /** 是否启用切割 */
  enabled?: boolean;
  /** 是否启用撕裂 */
  tearingEnabled?: boolean;
  /** 撕裂阈值（约束拉伸比例） */
  tearingThreshold?: number;
}

/**
 * 软体切割系统
 * 提供软体的切割和撕裂功能
 */
export class SoftBodyCutter {
  /** 是否启用切割 */
  private enabled: boolean;
  
  /** 是否启用撕裂 */
  private tearingEnabled: boolean;
  
  /** 撕裂阈值（约束拉伸比例） */
  private tearingThreshold: number;
  
  /**
   * 创建软体切割系统
   * @param options 软体切割系统选项
   */
  constructor(options: SoftBodyCutterOptions = {}) {
    this.enabled = options.enabled !== undefined ? options.enabled : true;
    this.tearingEnabled = options.tearingEnabled !== undefined ? options.tearingEnabled : true;
    this.tearingThreshold = options.tearingThreshold || 1.5; // 默认拉伸1.5倍时撕裂
  }
  
  /**
   * 使用平面切割软体
   * @param softBody 软体组件
   * @param plane 切割平面
   * @returns 是否成功切割
   */
  public cutWithPlane(softBody: SoftBodyComponent, plane: CuttingPlane): boolean {
    if (!this.enabled || !softBody.isInitialized()) return false;
    
    // 获取粒子数组
    const particles = softBody.getParticles();
    if (!particles) return false;
    
    // 创建平面法线和点的CANNON向量
    const normal = new CANNON.Vec3(plane.normal.x, plane.normal.y, plane.normal.z);
    const point = new CANNON.Vec3(plane.point.x, plane.point.y, plane.point.z);
    
    // 标记每个粒子在平面的哪一侧
    const particleSides: boolean[] = [];
    for (const particle of particles) {
      // 计算粒子到平面的有向距离
      const toParticle = new CANNON.Vec3();
      toParticle.copy(particle.position);
      toParticle.vsub(point, toParticle);
      
      // 点积确定在哪一侧（正值在正面，负值在背面）
      const side = normal.dot(toParticle) >= 0;
      particleSides.push(side);
    }
    
    // 根据软体类型执行不同的切割策略
    switch (softBody.getType()) {
      case SoftBodyType.CLOTH:
        return this.cutCloth(softBody, particleSides);
      case SoftBodyType.ROPE:
        return this.cutRope(softBody, particleSides);
      case SoftBodyType.VOLUME:
      case SoftBodyType.BALLOON:
      case SoftBodyType.JELLY:
        return this.cutVolume(softBody, particleSides);
      default:
        return false;
    }
  }
  
  /**
   * 使用射线切割软体
   * @param softBody 软体组件
   * @param ray 切割射线
   * @returns 是否成功切割
   */
  public cutWithRay(softBody: SoftBodyComponent, ray: CuttingRay): boolean {
    if (!this.enabled || !softBody.isInitialized()) return false;
    
    // 创建THREE射线
    const threeRay = new THREE.Ray(ray.origin, ray.direction.normalize());
    
    // 获取粒子数组
    const particles = softBody.getParticles();
    if (!particles) return false;
    
    // 找出被射线穿过的约束
    const cutConstraints: number[] = [];
    
    // 获取约束数组
    const constraints = softBody.getConstraints();
    if (!constraints) return false;
    
    // 检查每个约束是否被射线穿过
    for (let i = 0; i < constraints.length; i++) {
      const constraint = constraints[i];
      const particleA = particles[constraint.particleA];
      const particleB = particles[constraint.particleB];
      
      // 创建线段
      const segmentStart = new THREE.Vector3(
        particleA.position.x,
        particleA.position.y,
        particleA.position.z
      );
      
      const segmentEnd = new THREE.Vector3(
        particleB.position.x,
        particleB.position.y,
        particleB.position.z
      );
      
      // 计算射线与线段的最近点
      const closestPoint = new THREE.Vector3();
      const lineSegment = new THREE.Line3(segmentStart, segmentEnd);
      lineSegment.closestPointToPoint(ray.origin, true, closestPoint);
      
      // 计算最近点到射线起点的距离
      const distance = ray.origin.distanceTo(closestPoint);
      
      // 如果距离小于阈值，认为射线穿过了约束
      const threshold = 0.1; // 可调整的阈值
      if (distance < threshold && distance < ray.length) {
        cutConstraints.push(i);
      }
    }
    
    // 如果找到要切割的约束，执行切割
    if (cutConstraints.length > 0) {
      return softBody.removeConstraints(cutConstraints);
    }
    
    return false;
  }
  
  /**
   * 检查软体是否需要撕裂
   * @param softBody 软体组件
   * @returns 是否发生撕裂
   */
  public checkTearing(softBody: SoftBodyComponent): boolean {
    if (!this.enabled || !this.tearingEnabled || !softBody.isInitialized()) return false;
    
    // 获取约束数组
    const constraints = softBody.getConstraints();
    if (!constraints) return false;
    
    // 获取粒子数组
    const particles = softBody.getParticles();
    if (!particles) return false;
    
    // 检查每个约束是否超过撕裂阈值
    const tornConstraints: number[] = [];
    
    for (let i = 0; i < constraints.length; i++) {
      const constraint = constraints[i];
      const particleA = particles[constraint.particleA];
      const particleB = particles[constraint.particleB];
      
      // 计算当前长度
      const currentLength = particleA.position.distanceTo(particleB.position);
      
      // 计算拉伸比例
      const stretchRatio = currentLength / constraint.restLength;
      
      // 如果超过撕裂阈值，添加到撕裂列表
      if (stretchRatio > this.tearingThreshold) {
        tornConstraints.push(i);
      }
    }
    
    // 如果有约束需要撕裂，执行撕裂
    if (tornConstraints.length > 0) {
      return softBody.removeConstraints(tornConstraints);
    }
    
    return false;
  }
  
  /**
   * 切割布料
   * @param softBody 软体组件
   * @param particleSides 粒子在平面的哪一侧
   * @returns 是否成功切割
   */
  private cutCloth(softBody: SoftBodyComponent, particleSides: boolean[]): boolean {
    // 获取约束数组
    const constraints = softBody.getConstraints();
    if (!constraints) return false;
    
    // 找出跨越平面的约束
    const cutConstraints: number[] = [];
    
    for (let i = 0; i < constraints.length; i++) {
      const constraint = constraints[i];
      const sideA = particleSides[constraint.particleA];
      const sideB = particleSides[constraint.particleB];
      
      // 如果约束的两个粒子在平面的不同侧，切割该约束
      if (sideA !== sideB) {
        cutConstraints.push(i);
      }
    }
    
    // 如果找到要切割的约束，执行切割
    if (cutConstraints.length > 0) {
      return softBody.removeConstraints(cutConstraints);
    }
    
    return false;
  }
  
  /**
   * 切割绳索
   * @param softBody 软体组件
   * @param particleSides 粒子在平面的哪一侧
   * @returns 是否成功切割
   */
  private cutRope(softBody: SoftBodyComponent, particleSides: boolean[]): boolean {
    // 对于绳索，只需要切割一个约束即可分离
    // 获取约束数组
    const constraints = softBody.getConstraints();
    if (!constraints) return false;
    
    // 找出跨越平面的约束
    for (let i = 0; i < constraints.length; i++) {
      const constraint = constraints[i];
      const sideA = particleSides[constraint.particleA];
      const sideB = particleSides[constraint.particleB];
      
      // 如果约束的两个粒子在平面的不同侧，切割该约束
      if (sideA !== sideB) {
        return softBody.removeConstraints([i]);
      }
    }
    
    return false;
  }
  
  /**
   * 切割体积软体
   * @param softBody 软体组件
   * @param particleSides 粒子在平面的哪一侧
   * @returns 是否成功切割
   */
  private cutVolume(softBody: SoftBodyComponent, particleSides: boolean[]): boolean {
    // 获取约束数组
    const constraints = softBody.getConstraints();
    if (!constraints) return false;
    
    // 找出跨越平面的约束
    const cutConstraints: number[] = [];
    
    for (let i = 0; i < constraints.length; i++) {
      const constraint = constraints[i];
      const sideA = particleSides[constraint.particleA];
      const sideB = particleSides[constraint.particleB];
      
      // 如果约束的两个粒子在平面的不同侧，切割该约束
      if (sideA !== sideB) {
        cutConstraints.push(i);
      }
    }
    
    // 如果找到要切割的约束，执行切割
    if (cutConstraints.length > 0) {
      return softBody.removeConstraints(cutConstraints);
    }
    
    return false;
  }
  
  /**
   * 启用切割
   */
  public enable(): void {
    this.enabled = true;
  }
  
  /**
   * 禁用切割
   */
  public disable(): void {
    this.enabled = false;
  }
  
  /**
   * 启用撕裂
   */
  public enableTearing(): void {
    this.tearingEnabled = true;
  }
  
  /**
   * 禁用撕裂
   */
  public disableTearing(): void {
    this.tearingEnabled = false;
  }
  
  /**
   * 设置撕裂阈值
   * @param threshold 撕裂阈值
   */
  public setTearingThreshold(threshold: number): void {
    this.tearingThreshold = threshold;
  }
}
