/**
 * 面部肌肉模拟
 * 使用物理引擎模拟面部肌肉运动
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { CannonPhysicsEngine } from './CannonPhysicsEngine';
import { FacialExpressionType } from '../FacialExpressionType';
/**
 * 肌肉类型
 */
export declare enum MuscleType {
    /** 眉毛 */
    EYEBROW = "eyebrow",
    /** 眼睑 */
    EYELID = "eyelid",
    /** 眼角 */
    EYE_CORNER = "eye_corner",
    /** 鼻子 */
    NOSE = "nose",
    /** 嘴唇 */
    LIP = "lip",
    /** 嘴角 */
    MOUTH_CORNER = "mouth_corner",
    /** 下巴 */
    JAW = "jaw",
    /** 脸颊 */
    CHEEK = "cheek"
}
/**
 * 肌肉配置
 */
export interface MuscleConfig {
    /** 肌肉类型 */
    type: MuscleType;
    /** 肌肉名称 */
    name: string;
    /** 起点 */
    start: THREE.Vector3;
    /** 终点 */
    end: THREE.Vector3;
    /** 质量 */
    mass?: number;
    /** 半径 */
    radius?: number;
    /** 弹性 */
    stiffness?: number;
    /** 阻尼 */
    damping?: number;
    /** 最大力 */
    maxForce?: number;
    /** 是否固定起点 */
    fixedStart?: boolean;
    /** 是否固定终点 */
    fixedEnd?: boolean;
}
/**
 * 面部肌肉模拟配置
 */
export interface FacialMuscleSimulationConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 物理引擎 */
    physicsEngine?: CannonPhysicsEngine;
    /** 重力 */
    gravity?: THREE.Vector3;
    /** 迭代次数 */
    iterations?: number;
    /** 是否使用软体 */
    useSoftBodies?: boolean;
}
/**
 * 面部肌肉模拟组件
 */
export declare class FacialMuscleSimulationComponent extends Component {
    /** 组件类型 */
    static readonly type = "FacialMuscleSimulation";
    /** 物理引擎 */
    private physicsEngine;
    /** 肌肉配置 */
    private muscles;
    /** 肌肉物理对象 */
    private muscleBodies;
    /** 肌肉约束 */
    private muscleConstraints;
    /** 是否启用模拟 */
    private simulationEnabled;
    /** 是否已初始化 */
    private initialized;
    /** 是否使用软体 */
    private useSoftBodies;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否启用调试 */
    private debug;
    /**
     * 构造函数
     * @param physicsEngine 物理引擎
     * @param config 配置
     */
    constructor(physicsEngine: CannonPhysicsEngine, config?: FacialMuscleSimulationConfig);
    /**
     * 添加肌肉
     * @param config 肌肉配置
     */
    addMuscle(config: MuscleConfig): void;
    /**
     * 移除肌肉
     * @param name 肌肉名称
     * @returns 是否成功移除
     */
    removeMuscle(name: string): boolean;
    /**
     * 获取肌肉
     * @param name 肌肉名称
     * @returns 肌肉配置
     */
    getMuscle(name: string): MuscleConfig | null;
    /**
     * 获取所有肌肉
     * @returns 肌肉配置数组
     */
    getMuscles(): MuscleConfig[];
    /**
     * 初始化
     */
    initialize(): void;
    /**
     * 创建肌肉物理对象
     * @param muscle 肌肉配置
     */
    private createMusclePhysics;
    /**
     * 应用表情
     * @param expression 表情类型
     * @param weight 权重
     */
    applyExpression(expression: FacialExpressionType, weight: number): void;
    /**
     * 应用开心表情
     * @param weight 权重
     */
    private applyHappyExpression;
    /**
     * 应用悲伤表情
     * @param weight 权重
     */
    private applySadExpression;
    /**
     * 应用愤怒表情
     * @param weight 权重
     */
    private applyAngryExpression;
    /**
     * 应用惊讶表情
     * @param weight 权重
     */
    private applySurprisedExpression;
    /**
     * 应用恐惧表情
     * @param weight 权重
     */
    private applyFearfulExpression;
    /**
     * 应用厌恶表情
     * @param weight 权重
     */
    private applyDisgustedExpression;
    /**
     * 应用蔑视表情
     * @param weight 权重
     */
    private applyContemptExpression;
    /**
     * 应用肌肉力
     * @param muscleName 肌肉名称
     * @param force 力
     */
    private applyMuscleForce;
    /**
     * 重置肌肉
     */
    private resetMuscles;
    /**
     * 启用组件
     */
    enableSimulation(): void;
    /**
     * 禁用组件
     */
    disableSimulation(): void;
    /**
     * 是否已启用
     * @returns 是否已启用
     */
    isSimulationEnabled(): boolean;
    /**
     * 是否已初始化
     * @returns 是否已初始化
     */
    isInitialized(): boolean;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 设置参数
     * @param name 参数名称
     * @param value 参数值
     */
    setParameter(name: string, value: any): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
