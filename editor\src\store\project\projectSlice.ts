/**
 * 项目状态切片
 */
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

// 定义项目类型
export interface Project {
  id: string;
  name: string;
  description: string;
  thumbnail?: string;
  createdAt: string;
  updatedAt: string;
  scenes: Scene[];
  isPublic: boolean;
  ownerId: string;
}

// 定义场景类型
export interface Scene {
  id: string;
  name: string;
  description?: string;
  thumbnail?: string;
  createdAt: string;
  updatedAt: string;
  projectId: string;
}

// 定义项目状态
interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  currentScene: Scene | null;
  isLoading: boolean;
  error: string | null;
}

// 初始状态
const initialState: ProjectState = {
  projects: [],
  currentProject: null,
  currentScene: null,
  isLoading: false,
  error: null};

// 获取项目列表
export const fetchProjects = createAsyncThunk('project/fetchProjects', async (_, { rejectWithValue }) => {
  try {
    const response = await axios.get('http://localhost:3000/api/projects');
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || '获取项目列表失败');
  }
});

// 获取项目详情
export const fetchProjectById = createAsyncThunk(
  'project/fetchProjectById',
  async (projectId: string, { rejectWithValue }) => {
    try {
      const response = await axios.get(`http://localhost:3000/api/projects/${projectId}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取项目详情失败');
    }
  }
);

// 创建新项目
export const createProject = createAsyncThunk(
  'project/createProject',
  async (
    { name, description, isPublic = false }: { name: string; description: string; isPublic?: boolean },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.post('http://localhost:3000/api/projects', { name, description, isPublic });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建项目失败');
    }
  }
);

// 更新项目
export const updateProject = createAsyncThunk(
  'project/updateProject',
  async (
    {
      projectId,
      data}: {
      projectId: string;
      data: { name?: string; description?: string; isPublic?: boolean };
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.patch(`http://localhost:3000/api/projects/${projectId}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新项目失败');
    }
  }
);

// 删除项目
export const deleteProject = createAsyncThunk(
  'project/deleteProject',
  async (projectId: string, { rejectWithValue }) => {
    try {
      await axios.delete(`http://localhost:3000/api/projects/${projectId}`);
      return projectId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '删除项目失败');
    }
  }
);

// 创建场景
export const createScene = createAsyncThunk(
  'project/createScene',
  async (
    {
      projectId,
      name,
      description}: {
      projectId: string;
      name: string;
      description?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.post(`/api/projects/${projectId}/scenes`, { name, description });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建场景失败');
    }
  }
);

// 创建项目切片
const projectSlice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    setCurrentProject: (state, action: PayloadAction<Project>) => {
      state.currentProject = action.payload;
    },
    setCurrentScene: (state, action: PayloadAction<Scene>) => {
      state.currentScene = action.payload;
    },
    clearCurrentProject: (state) => {
      state.currentProject = null;
      state.currentScene = null;
    },
    clearError: (state) => {
      state.error = null;
    }},
  extraReducers: (builder) => {
    // 获取项目列表
    builder
      .addCase(fetchProjects.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProjects.fulfilled, (state, action) => {
        state.isLoading = false;
        state.projects = action.payload;
      })
      .addCase(fetchProjects.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取项目详情
    builder
      .addCase(fetchProjectById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProjectById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentProject = action.payload;
      })
      .addCase(fetchProjectById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 创建项目
    builder
      .addCase(createProject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createProject.fulfilled, (state, action) => {
        state.isLoading = false;
        state.projects.push(action.payload);
        state.currentProject = action.payload;
      })
      .addCase(createProject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 更新项目
    builder
      .addCase(updateProject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProject.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.projects.findIndex((p) => p.id === action.payload.id);
        if (index !== -1) {
          state.projects[index] = action.payload;
        }
        if (state.currentProject?.id === action.payload.id) {
          state.currentProject = action.payload;
        }
      })
      .addCase(updateProject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 删除项目
    builder
      .addCase(deleteProject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteProject.fulfilled, (state, action) => {
        state.isLoading = false;
        state.projects = state.projects.filter((p) => p.id !== action.payload);
        if (state.currentProject?.id === action.payload) {
          state.currentProject = null;
          state.currentScene = null;
        }
      })
      .addCase(deleteProject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 创建场景
    builder
      .addCase(createScene.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createScene.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.currentProject) {
          state.currentProject.scenes.push(action.payload);
          state.currentScene = action.payload;
        }
      })
      .addCase(createScene.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  }});

export const { setCurrentProject, setCurrentScene, clearCurrentProject, clearError } = projectSlice.actions;
export default projectSlice.reducer;
