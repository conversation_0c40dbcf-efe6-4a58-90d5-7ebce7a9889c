/**
 * ComplexInteractionExample.ts
 * 
 * 复杂交互示例，展示交互系统的高级用法
 */

import { World, Entity, Scene, Transform, Camera } from '../../engine/src/core';
import { 
  InteractionSystem, 
  InteractableComponent, 
  InteractionType,
  InteractionEventComponent,
  InteractionEventType,
  InteractionPromptComponent,
  PromptPositionType,
  InteractionHighlightComponent,
  HighlightType
} from '../../engine/src/interaction';
import { InputSystem } from '../../engine/src/input/InputSystem';
import { Debug } from '../../engine/src/utils/Debug';
import { 
  Vector3, 
  BoxGeometry, 
  SphereGeometry, 
  CylinderGeometry, 
  PlaneGeometry,
  MeshBasicMaterial, 
  Mesh, 
  Color,
  Group
} from 'three';

/**
 * 复杂交互示例类
 */
export class ComplexInteractionExample {
  /** 世界 */
  private world: World;

  /** 场景 */
  private scene: Scene;

  /** 输入系统 */
  private inputSystem: InputSystem;

  /** 交互系统 */
  private interactionSystem: InteractionSystem;

  /** 可交互对象列表 */
  private interactableObjects: Entity[] = [];

  /** 交互状态 */
  private interactionState: {
    doorOpen: boolean;
    lightsOn: boolean;
    machineActive: boolean;
  } = {
    doorOpen: false,
    lightsOn: false,
    machineActive: false
  };

  /** 门实体 */
  private doorEntity?: Entity;

  /** 灯实体 */
  private lightEntity?: Entity;

  /** 机器实体 */
  private machineEntity?: Entity;

  /**
   * 构造函数
   */
  constructor() {
    // 创建世界
    this.world = new World();

    // 创建输入系统
    this.inputSystem = new InputSystem({
      enableKeyboard: true,
      enableMouse: true,
      enableTouch: true
    });

    // 添加输入系统到世界
    this.world.addSystem(this.inputSystem);

    // 创建交互系统
    this.interactionSystem = new InteractionSystem(this.world, {
      debug: true,
      maxInteractionDistance: 10,
      enableFrustumCheck: true,
      enableHighlight: true,
      enablePrompt: true
    });

    // 添加交互系统到世界
    this.world.addSystem(this.interactionSystem);

    // 创建场景
    this.scene = new Scene(this.world, {
      name: '复杂交互示例场景'
    });

    // 初始化场景
    this.initializeScene();

    // 创建可交互对象
    this.createInteractableObjects();

    // 启动游戏循环
    this.startGameLoop();
  }

  /**
   * 初始化场景
   */
  private initializeScene(): void {
    // 创建相机
    const camera = new Camera(this.world, {
      fov: 60,
      aspect: window.innerWidth / window.innerHeight,
      near: 0.1,
      far: 1000,
      position: new Vector3(0, 5, 10),
      lookAt: new Vector3(0, 0, 0)
    });

    // 设置为主相机
    this.world.setMainCamera(camera);

    // 添加相机到场景
    this.scene.addEntity(camera.entity);

    // 创建地面
    const ground = new Entity(this.world);
    ground.name = '地面';
    ground.addComponent(new Transform({
      position: new Vector3(0, -1, 0),
      scale: new Vector3(20, 0.1, 20)
    }));

    // 添加地面到场景
    this.scene.addEntity(ground);

    // 创建房间墙壁
    this.createWalls();
  }

  /**
   * 创建房间墙壁
   */
  private createWalls(): void {
    // 创建墙壁
    const wallMaterial = new MeshBasicMaterial({ color: 0xcccccc });
    
    // 后墙
    const backWall = new Entity(this.world);
    backWall.name = '后墙';
    backWall.addComponent(new Transform({
      position: new Vector3(0, 3, -5),
      scale: new Vector3(10, 8, 0.1)
    }));
    (backWall as any).mesh = new Mesh(new BoxGeometry(1, 1, 1), wallMaterial);
    this.scene.addEntity(backWall);
    
    // 左墙
    const leftWall = new Entity(this.world);
    leftWall.name = '左墙';
    leftWall.addComponent(new Transform({
      position: new Vector3(-5, 3, 0),
      scale: new Vector3(0.1, 8, 10)
    }));
    (leftWall as any).mesh = new Mesh(new BoxGeometry(1, 1, 1), wallMaterial);
    this.scene.addEntity(leftWall);
    
    // 右墙
    const rightWall = new Entity(this.world);
    rightWall.name = '右墙';
    rightWall.addComponent(new Transform({
      position: new Vector3(5, 3, 0),
      scale: new Vector3(0.1, 8, 10)
    }));
    (rightWall as any).mesh = new Mesh(new BoxGeometry(1, 1, 1), wallMaterial);
    this.scene.addEntity(rightWall);
  }

  /**
   * 创建可交互对象
   */
  private createInteractableObjects(): void {
    // 创建门
    this.createDoor();
    
    // 创建灯
    this.createLight();
    
    // 创建机器
    this.createMachine();
  }

  /**
   * 创建门
   */
  private createDoor(): void {
    // 创建门实体
    const entity = new Entity(this.world);
    entity.name = '门';
    
    // 添加变换组件
    entity.addComponent(new Transform({
      position: new Vector3(0, 1, 5),
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(2, 4, 0.2)
    }));
    
    // 创建门网格
    const doorMaterial = new MeshBasicMaterial({ color: 0x8b4513 });
    const doorMesh = new Mesh(new BoxGeometry(1, 1, 1), doorMaterial);
    (entity as any).mesh = doorMesh;
    
    // 添加可交互组件
    const interactable = new InteractableComponent(entity, {
      interactionType: InteractionType.CLICK,
      label: '门',
      prompt: '按E键开关门',
      interactionDistance: 5,
      onInteract: (e) => {
        // 切换门的状态
        this.toggleDoor();
      }
    });
    entity.addComponent(interactable);
    
    // 添加交互事件组件
    const interactionEvent = new InteractionEventComponent(entity);
    entity.addComponent(interactionEvent);
    
    // 添加交互提示组件
    const interactionPrompt = new InteractionPromptComponent(entity, {
      text: '按E键开关门',
      positionType: PromptPositionType.FOLLOW,
      offset: new Vector3(0, 2, 0),
      visible: false
    });
    entity.addComponent(interactionPrompt);
    
    // 添加交互高亮组件
    const interactionHighlight = new InteractionHighlightComponent(entity, {
      highlightType: HighlightType.OUTLINE,
      highlightColor: new Color(0xffff00),
      pulse: true,
      highlighted: false
    });
    entity.addComponent(interactionHighlight);
    
    // 将实体添加到场景
    this.scene.addEntity(entity);
    
    // 将实体添加到可交互对象列表
    this.interactableObjects.push(entity);
    
    // 注册组件到交互系统
    this.interactionSystem.registerInteractableComponent(entity, interactable);
    this.interactionSystem.registerInteractionEventComponent(entity, interactionEvent);
    
    // 保存门实体引用
    this.doorEntity = entity;
  }

  /**
   * 创建灯
   */
  private createLight(): void {
    // 创建灯实体
    const entity = new Entity(this.world);
    entity.name = '灯';
    
    // 添加变换组件
    entity.addComponent(new Transform({
      position: new Vector3(0, 6, 0),
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(1, 0.2, 1)
    }));
    
    // 创建灯网格
    const lightMaterial = new MeshBasicMaterial({ color: 0x888888 });
    const lightMesh = new Mesh(new CylinderGeometry(1, 1, 1, 16), lightMaterial);
    (entity as any).mesh = lightMesh;
    
    // 添加可交互组件
    const interactable = new InteractableComponent(entity, {
      interactionType: InteractionType.CLICK,
      label: '灯',
      prompt: '按E键开关灯',
      interactionDistance: 10,
      onInteract: (e) => {
        // 切换灯的状态
        this.toggleLight();
      }
    });
    entity.addComponent(interactable);
    
    // 添加交互事件组件
    const interactionEvent = new InteractionEventComponent(entity);
    entity.addComponent(interactionEvent);
    
    // 添加交互提示组件
    const interactionPrompt = new InteractionPromptComponent(entity, {
      text: '按E键开关灯',
      positionType: PromptPositionType.FOLLOW,
      offset: new Vector3(0, 1, 0),
      visible: false
    });
    entity.addComponent(interactionPrompt);
    
    // 添加交互高亮组件
    const interactionHighlight = new InteractionHighlightComponent(entity, {
      highlightType: HighlightType.GLOW,
      highlightColor: new Color(0xffff00),
      pulse: true,
      highlighted: false
    });
    entity.addComponent(interactionHighlight);
    
    // 将实体添加到场景
    this.scene.addEntity(entity);
    
    // 将实体添加到可交互对象列表
    this.interactableObjects.push(entity);
    
    // 注册组件到交互系统
    this.interactionSystem.registerInteractableComponent(entity, interactable);
    this.interactionSystem.registerInteractionEventComponent(entity, interactionEvent);
    
    // 保存灯实体引用
    this.lightEntity = entity;
  }

  /**
   * 创建机器
   */
  private createMachine(): void {
    // 创建机器实体
    const entity = new Entity(this.world);
    entity.name = '机器';
    
    // 添加变换组件
    entity.addComponent(new Transform({
      position: new Vector3(-3, 1, -3),
      rotation: new Vector3(0, Math.PI / 4, 0),
      scale: new Vector3(1, 1, 1)
    }));
    
    // 创建机器网格组
    const machineGroup = new Group();
    
    // 机器主体
    const bodyMaterial = new MeshBasicMaterial({ color: 0x444444 });
    const bodyMesh = new Mesh(new BoxGeometry(2, 2, 1), bodyMaterial);
    machineGroup.add(bodyMesh);
    
    // 机器屏幕
    const screenMaterial = new MeshBasicMaterial({ color: 0x222222 });
    const screenMesh = new Mesh(new PlaneGeometry(1.5, 1), screenMaterial);
    screenMesh.position.set(0, 0.2, 0.51);
    machineGroup.add(screenMesh);
    
    // 机器按钮
    const buttonMaterial = new MeshBasicMaterial({ color: 0xff0000 });
    const buttonMesh = new Mesh(new CylinderGeometry(0.2, 0.2, 0.1, 16), buttonMaterial);
    buttonMesh.position.set(0, -0.5, 0.55);
    buttonMesh.rotation.set(Math.PI / 2, 0, 0);
    machineGroup.add(buttonMesh);
    
    (entity as any).mesh = machineGroup;
    (entity as any).screenMesh = screenMesh;
    (entity as any).buttonMesh = buttonMesh;
    
    // 添加可交互组件
    const interactable = new InteractableComponent(entity, {
      interactionType: InteractionType.CLICK,
      label: '机器',
      prompt: '按E键操作机器',
      interactionDistance: 3,
      onInteract: (e) => {
        // 切换机器的状态
        this.toggleMachine();
      }
    });
    entity.addComponent(interactable);
    
    // 添加交互事件组件
    const interactionEvent = new InteractionEventComponent(entity);
    entity.addComponent(interactionEvent);
    
    // 添加交互提示组件
    const interactionPrompt = new InteractionPromptComponent(entity, {
      text: '按E键操作机器',
      positionType: PromptPositionType.FOLLOW,
      offset: new Vector3(0, 1.5, 0),
      visible: false
    });
    entity.addComponent(interactionPrompt);
    
    // 添加交互高亮组件
    const interactionHighlight = new InteractionHighlightComponent(entity, {
      highlightType: HighlightType.COLOR,
      highlightColor: new Color(0x00ffff),
      pulse: true,
      highlighted: false
    });
    entity.addComponent(interactionHighlight);
    
    // 将实体添加到场景
    this.scene.addEntity(entity);
    
    // 将实体添加到可交互对象列表
    this.interactableObjects.push(entity);
    
    // 注册组件到交互系统
    this.interactionSystem.registerInteractableComponent(entity, interactable);
    this.interactionSystem.registerInteractionEventComponent(entity, interactionEvent);
    
    // 保存机器实体引用
    this.machineEntity = entity;
  }

  /**
   * 切换门的状态
   */
  private toggleDoor(): void {
    if (!this.doorEntity) return;
    
    // 切换状态
    this.interactionState.doorOpen = !this.interactionState.doorOpen;
    
    // 获取变换组件
    const transform = this.doorEntity.getComponent('Transform');
    if (!transform) return;
    
    // 更新门的位置和旋转
    if (this.interactionState.doorOpen) {
      // 打开门（旋转）
      transform.setRotation(0, Math.PI / 2, 0);
      transform.setPosition(3.9, 1, 3);
      Debug.log('复杂交互示例', '门已打开');
    } else {
      // 关闭门
      transform.setRotation(0, 0, 0);
      transform.setPosition(0, 1, 5);
      Debug.log('复杂交互示例', '门已关闭');
    }
    
    // 更新提示文本
    const promptComponent = this.doorEntity.getComponent('InteractionPrompt');
    if (promptComponent) {
      promptComponent.text = this.interactionState.doorOpen ? '按E键关门' : '按E键开门';
    }
  }

  /**
   * 切换灯的状态
   */
  private toggleLight(): void {
    if (!this.lightEntity) return;
    
    // 切换状态
    this.interactionState.lightsOn = !this.interactionState.lightsOn;
    
    // 更新灯的颜色
    const mesh = (this.lightEntity as any).mesh;
    if (mesh && mesh.material) {
      mesh.material.color = new Color(this.interactionState.lightsOn ? 0xffff00 : 0x888888);
    }
    
    Debug.log('复杂交互示例', this.interactionState.lightsOn ? '灯已打开' : '灯已关闭');
    
    // 更新提示文本
    const promptComponent = this.lightEntity.getComponent('InteractionPrompt');
    if (promptComponent) {
      promptComponent.text = this.interactionState.lightsOn ? '按E键关灯' : '按E键开灯';
    }
  }

  /**
   * 切换机器的状态
   */
  private toggleMachine(): void {
    if (!this.machineEntity) return;
    
    // 切换状态
    this.interactionState.machineActive = !this.interactionState.machineActive;
    
    // 更新机器的外观
    const screenMesh = (this.machineEntity as any).screenMesh;
    const buttonMesh = (this.machineEntity as any).buttonMesh;
    
    if (screenMesh) {
      screenMesh.material.color = new Color(this.interactionState.machineActive ? 0x00ff00 : 0x222222);
    }
    
    if (buttonMesh) {
      buttonMesh.material.color = new Color(this.interactionState.machineActive ? 0x00ff00 : 0xff0000);
    }
    
    Debug.log('复杂交互示例', this.interactionState.machineActive ? '机器已激活' : '机器已停用');
    
    // 更新提示文本
    const promptComponent = this.machineEntity.getComponent('InteractionPrompt');
    if (promptComponent) {
      promptComponent.text = this.interactionState.machineActive ? '按E键停用机器' : '按E键激活机器';
    }
  }

  /**
   * 启动游戏循环
   */
  private startGameLoop(): void {
    // 上一帧时间
    let lastTime = 0;

    // 游戏循环函数
    const gameLoop = (time: number) => {
      // 计算时间增量（秒）
      const deltaTime = (time - lastTime) / 1000;
      lastTime = time;

      // 更新世界
      this.world.update(deltaTime);

      // 请求下一帧
      requestAnimationFrame(gameLoop);
    };

    // 开始游戏循环
    requestAnimationFrame(gameLoop);
  }
}

// 创建并运行示例
new ComplexInteractionExample();
