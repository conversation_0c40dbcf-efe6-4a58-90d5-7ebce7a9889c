/**
 * 增强版雨水组件
 * 提供更真实的雨水物理模拟和渲染效果
 */
import * as THREE from 'three';
import { RainWaterComponent, RainWaterConfig, RainWaterType } from './RainWaterComponent';
import type { Entity } from '../../core/Entity';
/**
 * 增强版雨水类型
 */
export declare enum EnhancedRainWaterType {
    /** 轻雨 */
    LIGHT = "light",
    /** 中雨 */
    MEDIUM = "medium",
    /** 暴雨 */
    HEAVY = "heavy",
    /** 雷雨 */
    THUNDERSTORM = "thunderstorm",
    /** 季风雨 */
    MONSOON = "monsoon",
    /** 春雨 */
    SPRING_RAIN = "spring_rain",
    /** 夏雨 */
    SUMMER_RAIN = "summer_rain",
    /** 秋雨 */
    AUTUMN_RAIN = "autumn_rain",
    /** 冬雨 */
    WINTER_RAIN = "winter_rain",
    /** 雪雨混合 */
    SLEET = "sleet",
    /** 酸雨 */
    ACID_RAIN = "acid_rain",
    /** 热带雨 */
    TROPICAL_RAIN = "tropical_rain"
}
/**
 * 雨滴形状
 */
export declare enum RaindropShape {
    /** 球形 */
    SPHERE = "sphere",
    /** 泪滴形 */
    TEARDROP = "teardrop",
    /** 扁平 */
    FLAT = "flat",
    /** 不规则 */
    IRREGULAR = "irregular"
}
/**
 * 增强版雨水配置
 */
export interface EnhancedRainWaterConfig extends RainWaterConfig {
    /** 雨滴形状 */
    raindropShape?: RaindropShape;
    /** 雨滴变形系数 */
    raindropDeformationFactor?: number;
    /** 雨滴碰撞精度 */
    raindropCollisionPrecision?: number;
    /** 是否启用风力影响 */
    enableWindEffect?: boolean;
    /** 风力强度 */
    windStrength?: number;
    /** 风力方向 */
    windDirection?: THREE.Vector2;
    /** 是否启用雨滴轨迹 */
    enableRaindropTrails?: boolean;
    /** 雨滴轨迹长度 */
    raindropTrailLength?: number;
    /** 是否启用雨滴涟漪 */
    enableRaindropRipples?: boolean;
    /** 雨滴涟漪强度 */
    raindropRippleStrength?: number;
    /** 是否启用雨滴积水 */
    enableRaindropPuddles?: boolean;
    /** 雨滴积水最大深度 */
    raindropPuddleMaxDepth?: number;
    /** 是否启用雨滴反射 */
    enableRaindropReflections?: boolean;
    /** 雨滴反射强度 */
    raindropReflectionStrength?: number;
    /** 是否启用雨滴折射 */
    enableRaindropRefractions?: boolean;
    /** 雨滴折射强度 */
    raindropRefractionStrength?: number;
    /** 是否启用雨滴光散射 */
    enableRaindropScattering?: boolean;
    /** 雨滴光散射强度 */
    raindropScatteringStrength?: number;
    /** 是否启用雨滴声音 */
    enableRaindropSounds?: boolean;
    /** 雨滴声音音量 */
    raindropSoundVolume?: number;
    /** 是否启用雨滴性能优化 */
    enableRaindropOptimization?: boolean;
    /** 雨滴LOD距离 */
    raindropLODDistances?: number[];
    /** 是否启用GPU加速 */
    enableGPUAcceleration?: boolean;
}
/**
 * 增强版雨水组件
 */
export declare class EnhancedRainWaterComponent extends RainWaterComponent {
    /** 雨滴形状 */
    private raindropShape;
    /** 雨滴变形系数 */
    private raindropDeformationFactor;
    /** 雨滴碰撞精度 */
    private raindropCollisionPrecision;
    /** 是否启用风力影响 */
    private enableWindEffect;
    /** 风力强度 */
    private windStrength;
    /** 风力方向 */
    private windDirection;
    /** 是否启用雨滴轨迹 */
    private enableRaindropTrails;
    /** 雨滴轨迹长度 */
    private raindropTrailLength;
    /** 是否启用雨滴涟漪 */
    private enableRaindropRipples;
    /** 雨滴涟漪强度 */
    private raindropRippleStrength;
    /** 是否启用雨滴积水 */
    private enableRaindropPuddles;
    /** 雨滴积水最大深度 */
    private raindropPuddleMaxDepth;
    /** 是否启用雨滴反射 */
    private enableRaindropReflections;
    /** 雨滴反射强度 */
    private raindropReflectionStrength;
    /** 是否启用雨滴折射 */
    private enableRaindropRefractions;
    /** 雨滴折射强度 */
    private raindropRefractionStrength;
    /** 是否启用雨滴光散射 */
    private enableRaindropScattering;
    /** 雨滴光散射强度 */
    private raindropScatteringStrength;
    /** 是否启用雨滴声音 */
    private enableRaindropSounds;
    /** 雨滴声音音量 */
    private raindropSoundVolume;
    /** 是否启用雨滴性能优化 */
    private enableRaindropOptimization;
    /** 雨滴LOD距离 */
    private raindropLODDistances;
    /** 是否启用GPU加速 */
    private enableGPUAcceleration;
    /** 性能监控器 */
    private performanceMonitor;
    /** 雨滴实例ID列表 */
    private raindropInstanceIds;
    /** 雨滴积水网格 */
    private puddleMeshes;
    /** 雨滴轨迹实例ID列表 */
    private raindropTrailInstanceIds;
    /** 雨滴涟漪实例ID列表 */
    private raindropRippleInstanceIds;
    /** 雨滴碰撞检测器 */
    private raindropCollisionDetector;
    /** 雨滴生成计时器 */
    private raindropGenerationTimer;
    /** 雨滴生成间隔 */
    private raindropGenerationInterval;
    /** 雨滴生成区域 */
    private raindropGenerationArea;
    /** 雨滴生成高度 */
    private raindropGenerationHeight;
    /** 雨滴最大数量 */
    private maxRaindrops;
    /** 当前雨滴数量 */
    private currentRaindropCount;
    /** 水体实例化渲染器 */
    private waterInstancedRenderer;
    /** 水下粒子系统 */
    private underwaterParticleSystem;
    /** 音频系统 */
    private audioSystem;
    /** 音频源ID */
    private audioSourceId;
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    constructor(entity: Entity, config?: EnhancedRainWaterConfig);
    /**
     * 应用增强配置
     * @param config 配置
     */
    private applyEnhancedConfig;
    /**
     * 初始化组件
     */
    initialize(): void;
    /**
     * 初始化雨滴生成区域
     */
    private initializeRaindropGenerationArea;
    /**
     * 更新雨滴生成间隔
     */
    private updateRaindropGenerationInterval;
    /**
     * 初始化增强音频
     */
    private initializeEnhancedAudio;
    /**
     * 初始化积水
     */
    private initializePuddles;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新雨滴生成
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateRaindropGeneration;
    /**
     * 生成增强雨滴
     */
    private generateEnhancedRaindrops;
    /**
     * 计算雨滴数量
     * @returns 雨滴数量
     */
    private calculateRaindropCount;
    /**
     * 创建雨滴
     * @param position 位置
     */
    private createRaindrop;
    /**
     * 计算雨滴大小
     * @returns 雨滴大小
     */
    private calculateRaindropSize;
    /**
     * 计算雨滴速度
     * @returns 雨滴速度
     */
    private calculateRaindropVelocity;
    /**
     * 计算雨滴寿命
     * @returns 雨滴寿命
     */
    private calculateRaindropLifetime;
    /**
     * 更新增强雨滴
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateEnhancedRaindrops;
    /**
     * 检测雨滴碰撞
     * @param id 雨滴ID
     * @param instance 雨滴实例
     */
    private checkRaindropCollision;
    /**
     * 创建水花效果
     * @param _position 位置（未使用）
     * @param intersection 碰撞结果
     */
    private createSplashEffect;
    /**
     * 创建涟漪效果
     * @param position 位置
     */
    private createRippleEffect;
    /**
     * 添加水到积水
     * @param position 位置
     */
    private addWaterToPuddle;
    /**
     * 更新积水
     * @param deltaTime 帧间隔时间（秒）
     */
    private updatePuddles;
    /**
     * 移除雨滴
     * @param id 雨滴ID
     */
    private removeRaindrop;
    /**
     * 设置雨水类型
     * @param type 雨水类型
     */
    setRainWaterType(type: RainWaterType): void;
    /**
     * 更新增强音频
     */
    private updateEnhancedAudio;
    /**
     * 设置风力强度
     * @param strength 风力强度
     */
    setWindStrength(strength: number): void;
    /**
     * 设置风力方向
     * @param direction 风力方向
     */
    setWindDirection(direction: THREE.Vector2): void;
    /**
     * 设置是否启用风力影响
     * @param enable 是否启用
     */
    setEnableWindEffect(enable: boolean): void;
    /**
     * 设置雨滴形状
     * @param shape 雨滴形状
     */
    setRaindropShape(shape: RaindropShape): void;
    /**
     * 设置雨滴变形系数
     * @param factor 变形系数
     */
    setRaindropDeformationFactor(factor: number): void;
    /**
     * 设置雨滴碰撞精度
     * @param precision 碰撞精度
     */
    setRaindropCollisionPrecision(precision: number): void;
    /**
     * 设置是否启用雨滴轨迹
     * @param enable 是否启用
     */
    setEnableRaindropTrails(enable: boolean): void;
    /**
     * 设置雨滴轨迹长度
     * @param length 轨迹长度
     */
    setRaindropTrailLength(length: number): void;
    /**
     * 设置是否启用雨滴涟漪
     * @param enable 是否启用
     */
    setEnableRaindropRipples(enable: boolean): void;
    /**
     * 设置雨滴涟漪强度
     * @param strength 涟漪强度
     */
    setRaindropRippleStrength(strength: number): void;
    /**
     * 设置是否启用雨滴积水
     * @param enable 是否启用
     */
    setEnableRaindropPuddles(enable: boolean): void;
    /**
     * 设置雨滴积水最大深度
     * @param depth 最大深度
     */
    setRaindropPuddleMaxDepth(depth: number): void;
    /**
     * 销毁组件
     */
    dispose(): void;
    /**
     * 清理雨滴
     */
    private clearRaindrops;
    /**
     * 清理积水
     */
    private clearPuddles;
    /**
     * 清理音频
     */
    private clearAudio;
}
