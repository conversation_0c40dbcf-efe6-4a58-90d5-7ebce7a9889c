/**
 * 实例化渲染系统
 * 用于高效渲染大量相同物体
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
import { InstancedComponent, InstanceData } from './InstancedComponent';
/**
 * 实例化渲染系统配置接口
 */
export interface InstancedRenderingSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率（帧） */
    updateFrequency?: number;
    /** 是否使用视锥体剔除 */
    useFrustumCulling?: boolean;
    /** 是否使用遮挡剔除 */
    useOcclusionCulling?: boolean;
    /** 是否使用距离剔除 */
    useDistanceCulling?: boolean;
    /** 最大剔除距离 */
    maxCullingDistance?: number;
    /** 是否使用动态批处理 */
    useDynamicBatching?: boolean;
    /** 是否使用GPU实例化 */
    useGPUInstancing?: boolean;
}
/**
 * 实例化渲染系统事件类型
 */
export declare enum InstancedRenderingSystemEventType {
    /** 组件添加 */
    COMPONENT_ADDED = "component_added",
    /** 组件移除 */
    COMPONENT_REMOVED = "component_removed",
    /** 实例添加 */
    INSTANCE_ADDED = "instance_added",
    /** 实例移除 */
    INSTANCE_REMOVED = "instance_removed",
    /** 实例更新 */
    INSTANCE_UPDATED = "instance_updated"
}
/**
 * 实例化渲染系统类
 */
export declare class InstancedRenderingSystem extends System {
    /** 系统类型 */
    protected static readonly TYPE: string;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率（帧） */
    private updateFrequency;
    /** 当前帧计数 */
    private frameCount;
    /** 是否使用视锥体剔除 */
    private useFrustumCulling;
    /** 是否使用遮挡剔除 */
    protected useOcclusionCulling: boolean;
    /** 是否使用距离剔除 */
    protected useDistanceCulling: boolean;
    /** 最大剔除距离 */
    protected maxCullingDistance: number;
    /** 是否使用动态批处理 */
    protected useDynamicBatching: boolean;
    /** 是否使用GPU实例化 */
    protected useGPUInstancing: boolean;
    /** 活跃相机 */
    private activeCamera;
    /** 活跃场景 */
    private activeScene;
    /** 实例化组件列表 */
    private instancedComponents;
    /** 视锥体 */
    private frustum;
    /** 事件发射器 */
    private eventEmitter;
    /** 临时向量 */
    protected tempVector: THREE.Vector3;
    /** 临时矩阵 */
    protected tempMatrix: THREE.Matrix4;
    /** 临时四元数 */
    protected tempQuaternion: THREE.Quaternion;
    /** 临时欧拉角 */
    protected tempEuler: THREE.Euler;
    /** 临时颜色 */
    protected tempColor: THREE.Color;
    /**
     * 创建实例化渲染系统
     * @param options 实例化渲染系统配置
     */
    constructor(options?: InstancedRenderingSystemOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 设置活跃相机
     * @param camera 相机
     */
    setActiveCamera(camera: Camera): void;
    /**
     * 获取活跃相机
     * @returns 活跃相机
     */
    getActiveCamera(): Camera | null;
    /**
     * 设置活跃场景
     * @param scene 场景
     */
    setActiveScene(scene: Scene): void;
    /**
     * 获取活跃场景
     * @returns 活跃场景
     */
    getActiveScene(): Scene | null;
    /**
     * 注册实例化组件
     * @param entity 实体
     * @param component 实例化组件
     */
    registerInstancedComponent(entity: Entity, component: InstancedComponent): void;
    /**
     * 注销实例化组件
     * @param entity 实体
     */
    unregisterInstancedComponent(entity: Entity): void;
    /**
     * 创建实例化网格
     * @param entity 实体
     * @param component 实例化组件
     */
    private createInstancedMesh;
    /**
     * 移除实例化网格
     * @param _entity 实体
     * @param component 实例化组件
     */
    private removeInstancedMesh;
    /**
     * 更新实例
     * @param component 实例化组件
     */
    private updateInstances;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 更新视锥体
     */
    private updateFrustum;
    /**
     * 添加实例
     * @param entity 实体
     * @param instance 实例数据
     * @returns 实例ID
     */
    addInstance(entity: Entity, instance: InstanceData): number;
    /**
     * 移除实例
     * @param entity 实体
     * @param instanceId 实例ID
     * @returns 是否成功
     */
    removeInstance(entity: Entity, instanceId: number): boolean;
    /**
     * 更新实例
     * @param entity 实体
     * @param instanceId 实例ID
     * @param instance 实例数据
     * @returns 是否成功
     */
    updateInstance(entity: Entity, instanceId: number, instance: Partial<InstanceData>): boolean;
    /**
     * 获取实例
     * @param entity 实体
     * @param instanceId 实例ID
     * @returns 实例数据
     */
    getInstance(entity: Entity, instanceId: number): InstanceData | null;
    /**
     * 清空实例
     * @param entity 实体
     * @returns 是否成功
     */
    clearInstances(entity: Entity): boolean;
    /**
     * 手动更新
     */
    manualUpdate(): void;
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 设置是否自动更新
     * @param autoUpdate 是否自动更新
     */
    setAutoUpdate(autoUpdate: boolean): void;
    /**
     * 获取是否自动更新
     * @returns 是否自动更新
     */
    isAutoUpdate(): boolean;
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    addEventListener(type: InstancedRenderingSystemEventType, listener: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    removeEventListener(type: InstancedRenderingSystemEventType, listener: (...args: any[]) => void): void;
    /**
     * 销毁系统
     */
    dispose(): void;
}
