import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
/**
 * 物理角色交互系统配置
 */
export interface PhysicsCharacterInteractionSystemConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 最大交互距离 */
    maxInteractionDistance?: number;
    /** 最大交互力 */
    maxInteractionForce?: number;
    /** 交互力衰减系数 */
    interactionForceDamping?: number;
    /** 是否启用力反馈 */
    enableForceFeedback?: boolean;
    /** 是否启用交互动画 */
    enableInteractionAnimation?: boolean;
}
/**
 * 物理角色交互系统
 * 管理角色与物理对象之间的交互，包括物理约束和反馈
 */
export declare class PhysicsCharacterInteractionSystem extends System {
    /** 系统类型 */
    static readonly type: string;
    /** 物理系统 */
    private physicsSystem;
    /** 交互组件映射 */
    private interactionComponents;
    /** 交互约束映射 */
    private interactionConstraints;
    /** 当前活动交互 */
    private activeInteractions;
    /** 系统配置 */
    private config;
    /** 事件发射器 */
    private eventEmitter;
    /** 调试渲染器 */
    private debugRenderer;
    /**
     * 创建物理角色交互系统
     * @param config 系统配置
     */
    constructor(config?: PhysicsCharacterInteractionSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 处理实体创建事件
     * @param entity 创建的实体
     */
    private handleEntityCreated;
    /**
     * 处理实体移除事件
     * @param entity 移除的实体
     */
    private handleEntityRemoved;
    /**
     * 设置实体交互
     * @param entity 实体
     */
    private setupEntityInteraction;
    /**
     * 移除实体交互
     * @param entity 实体
     */
    private removeEntityInteraction;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新调试渲染器
     */
    private updateDebugRenderer;
    /**
     * 开始交互
     * @param characterEntity 角色实体
     * @param targetEntity 目标实体
     * @param interactionType 交互类型
     * @returns 是否成功开始交互
     */
    startInteraction(characterEntity: Entity, targetEntity: Entity, interactionType: string): boolean;
    /**
     * 终止交互
     * @param characterId 角色实体ID
     * @param targetId 目标实体ID
     * @returns 是否成功终止交互
     */
    terminateInteraction(characterId: string, targetId: string): boolean;
    /**
     * 终止所有交互
     * @param characterEntity 角色实体
     */
    terminateAllInteractions(characterEntity: Entity): void;
    /**
     * 获取角色的当前交互目标
     * @param characterEntity 角色实体
     * @returns 交互目标实体，如果没有则返回null
     */
    getInteractionTarget(characterEntity: Entity): Entity | null;
    /**
     * 检查角色是否正在交互
     * @param characterEntity 角色实体
     * @returns 是否正在交互
     */
    isInteracting(characterEntity: Entity): boolean;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (...args: any[]) => void): void;
    /**
     * 销毁系统
     */
    destroy(): void;
}
