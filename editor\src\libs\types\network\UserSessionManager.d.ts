/**
 * 用户会话管理器
 * 负责管理用户会话和权限
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 用户角色
 */
export declare enum UserRole {
    /** 访客 */
    GUEST = "guest",
    /** 用户 */
    USER = "user",
    /** 管理员 */
    ADMIN = "admin",
    /** 超级管理员 */
    SUPER_ADMIN = "super_admin"
}
/**
 * 用户权限
 */
export declare enum UserPermission {
    /** 查看 */
    VIEW = "view",
    /** 编辑 */
    EDIT = "edit",
    /** 创建 */
    CREATE = "create",
    /** 删除 */
    DELETE = "delete",
    /** 管理用户 */
    MANAGE_USERS = "manage_users",
    /** 管理权限 */
    MANAGE_PERMISSIONS = "manage_permissions",
    /** 管理系统 */
    MANAGE_SYSTEM = "manage_system"
}
/**
 * 用户会话数据
 */
export interface UserSession {
    /** 用户ID */
    userId: string;
    /** 用户名 */
    username: string;
    /** 显示名称 */
    displayName?: string;
    /** 头像URL */
    avatarUrl?: string;
    /** 角色 */
    role: UserRole;
    /** 权限 */
    permissions: Set<UserPermission>;
    /** 自定义数据 */
    customData?: Record<string, any>;
    /** 连接时间 */
    connectionTime: number;
    /** 上次活动时间 */
    lastActivityTime: number;
    /** 是否在线 */
    isOnline: boolean;
    /** 是否已验证 */
    isAuthenticated: boolean;
    /** 会话令牌 */
    sessionToken?: string;
    /** 客户端信息 */
    clientInfo?: {
        /** IP地址 */
        ip?: string;
        /** 用户代理 */
        userAgent?: string;
        /** 设备类型 */
        deviceType?: string;
        /** 浏览器 */
        browser?: string;
        /** 操作系统 */
        os?: string;
    };
}
/**
 * 用户会话管理器配置
 */
export interface UserSessionManagerConfig {
    /** 会话超时时间（毫秒） */
    sessionTimeout?: number;
    /** 是否启用会话超时 */
    enableSessionTimeout?: boolean;
    /** 是否启用权限检查 */
    enablePermissionCheck?: boolean;
    /** 默认角色 */
    defaultRole?: UserRole;
    /** 角色权限映射 */
    rolePermissions?: Record<UserRole, UserPermission[]>;
    /** 是否允许匿名用户 */
    allowAnonymous?: boolean;
    /** 最大用户数量 */
    maxUsers?: number;
}
/**
 * 用户会话管理器
 * 负责管理用户会话和权限
 */
export declare class UserSessionManager extends EventEmitter {
    /** 配置 */
    private config;
    /** 用户会话映射表 */
    private sessions;
    /** 角色权限映射表 */
    private rolePermissions;
    /** 会话清理定时器ID */
    private cleanupTimerId;
    /**
     * 创建用户会话管理器
     * @param config 配置
     */
    constructor(config?: UserSessionManagerConfig);
    /**
     * 初始化角色权限映射
     */
    private initRolePermissions;
    /**
     * 启动会话清理
     */
    private startSessionCleanup;
    /**
     * 停止会话清理
     */
    private stopSessionCleanup;
    /**
     * 清理过期会话
     */
    private cleanupSessions;
    /**
     * 创建用户会话
     * @param userId 用户ID
     * @param username 用户名
     * @param role 角色
     * @param isAuthenticated 是否已验证
     * @param sessionToken 会话令牌
     * @param clientInfo 客户端信息
     * @returns 用户会话
     */
    createSession(userId: string, username: string, role?: UserRole, isAuthenticated?: boolean, sessionToken?: string, clientInfo?: UserSession['clientInfo']): UserSession;
    /**
     * 获取用户会话
     * @param userId 用户ID
     * @returns 用户会话
     */
    getSession(userId: string): UserSession | undefined;
    /**
     * 更新用户会话
     * @param userId 用户ID
     * @param updates 更新数据
     * @returns 更新后的会话
     */
    updateSession(userId: string, updates: Partial<UserSession>): UserSession | undefined;
    /**
     * 移除用户会话
     * @param userId 用户ID
     * @returns 是否成功移除
     */
    removeSession(userId: string): boolean;
    /**
     * 标记用户在线
     * @param userId 用户ID
     * @returns 是否成功
     */
    markUserOnline(userId: string): boolean;
    /**
     * 标记用户离线
     * @param userId 用户ID
     * @returns 是否成功
     */
    markUserOffline(userId: string): boolean;
    /**
     * 更新用户活动时间
     * @param userId 用户ID
     * @returns 是否成功
     */
    updateUserActivity(userId: string): boolean;
    /**
     * 检查用户是否有权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否有权限
     */
    hasPermission(userId: string, permission: UserPermission): boolean;
    /**
     * 授予用户权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否成功
     */
    grantPermission(userId: string, permission: UserPermission): boolean;
    /**
     * 撤销用户权限
     * @param userId 用户ID
     * @param permission 权限
     * @returns 是否成功
     */
    revokePermission(userId: string, permission: UserPermission): boolean;
    /**
     * 设置用户角色
     * @param userId 用户ID
     * @param role 角色
     * @returns 是否成功
     */
    setUserRole(userId: string, role: UserRole): boolean;
    /**
     * 获取角色的权限
     * @param role 角色
     * @returns 权限列表
     */
    getPermissionsForRole(role: UserRole): UserPermission[];
    /**
     * 设置角色的权限
     * @param role 角色
     * @param permissions 权限列表
     */
    setRolePermissions(role: UserRole, permissions: UserPermission[]): void;
    /**
     * 获取所有在线用户
     * @returns 在线用户列表
     */
    getOnlineUsers(): UserSession[];
    /**
     * 获取所有用户
     * @returns 用户列表
     */
    getAllUsers(): UserSession[];
    /**
     * 获取用户数量
     * @returns 用户数量
     */
    getUserCount(): number;
    /**
     * 获取在线用户数量
     * @returns 在线用户数量
     */
    getOnlineUserCount(): number;
    /**
     * 清空所有会话
     */
    clearSessions(): void;
    /**
     * 销毁管理器
     */
    dispose(): void;
}
