/**
 * 植被交互模型
 * 用于模拟植被之间的相互影响和竞争
 */
import * as THREE from 'three';
import { VegetationType, VegetationGrowthStage, VegetationData } from './EcosystemSimulationSystem';

/**
 * 植被交互类型
 */
export enum VegetationInteractionType {
  /** 竞争 */
  COMPETITION = 'competition',
  /** 促进 */
  FACILITATION = 'facilitation',
  /** 中性 */
  NEUTRAL = 'neutral',
  /** 寄生 */
  PARASITISM = 'parasitism',
  /** 共生 */
  SYMBIOSIS = 'symbiosis'
}

/**
 * 植被交互规则
 */
export interface VegetationInteractionRule {
  /** 源植被类型 */
  sourceType: VegetationType;
  /** 目标植被类型 */
  targetType: VegetationType;
  /** 交互类型 */
  interactionType: VegetationInteractionType;
  /** 交互强度 */
  strength: number;
  /** 交互距离 */
  distance: number;
  /** 是否考虑生长阶段 */
  considerGrowthStage: boolean;
  /** 源植被生长阶段 */
  sourceGrowthStage?: VegetationGrowthStage;
  /** 目标植被生长阶段 */
  targetGrowthStage?: VegetationGrowthStage;
  /** 是否考虑高度差异 */
  considerHeightDifference: boolean;
  /** 是否考虑宽度差异 */
  considerWidthDifference: boolean;
  /** 是否考虑竞争力差异 */
  considerCompetitivenessDifference: boolean;
  /** 是否考虑健康度差异 */
  considerHealthDifference: boolean;
  /** 是否考虑年龄差异 */
  considerAgeDifference: boolean;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 植被交互结果
 */
export interface VegetationInteractionResult {
  /** 源植被 */
  source: VegetationData;
  /** 目标植被 */
  target: VegetationData;
  /** 交互类型 */
  interactionType: VegetationInteractionType;
  /** 交互强度 */
  strength: number;
  /** 源植被健康度变化 */
  sourceHealthChange: number;
  /** 目标植被健康度变化 */
  targetHealthChange: number;
  /** 源植被生长速度变化 */
  sourceGrowthRateChange: number;
  /** 目标植被生长速度变化 */
  targetGrowthRateChange: number;
  /** 源植被竞争力变化 */
  sourceCompetitivenessChange: number;
  /** 目标植被竞争力变化 */
  targetCompetitivenessChange: number;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 植被交互模型
 */
export class VegetationInteractionModel {
  /** 交互规则列表 */
  private rules: VegetationInteractionRule[] = [];

  /** 默认交互规则 */
  private defaultRules: VegetationInteractionRule[] = [
    // 树木与树木的竞争
    {
      sourceType: VegetationType.TREE,
      targetType: VegetationType.TREE,
      interactionType: VegetationInteractionType.COMPETITION,
      strength: 0.5,
      distance: 10,
      considerGrowthStage: true,
      considerHeightDifference: true,
      considerWidthDifference: true,
      considerCompetitivenessDifference: true,
      considerHealthDifference: true,
      considerAgeDifference: true
    },
    // 树木与灌木的竞争
    {
      sourceType: VegetationType.TREE,
      targetType: VegetationType.SHRUB,
      interactionType: VegetationInteractionType.COMPETITION,
      strength: 0.7,
      distance: 8,
      considerGrowthStage: true,
      considerHeightDifference: true,
      considerWidthDifference: true,
      considerCompetitivenessDifference: true,
      considerHealthDifference: true,
      considerAgeDifference: true
    },
    // 树木与草地的竞争
    {
      sourceType: VegetationType.TREE,
      targetType: VegetationType.GRASS,
      interactionType: VegetationInteractionType.COMPETITION,
      strength: 0.9,
      distance: 5,
      considerGrowthStage: true,
      considerHeightDifference: true,
      considerWidthDifference: true,
      considerCompetitivenessDifference: true,
      considerHealthDifference: true,
      considerAgeDifference: true
    },
    // 灌木与灌木的竞争
    {
      sourceType: VegetationType.SHRUB,
      targetType: VegetationType.SHRUB,
      interactionType: VegetationInteractionType.COMPETITION,
      strength: 0.4,
      distance: 5,
      considerGrowthStage: true,
      considerHeightDifference: true,
      considerWidthDifference: true,
      considerCompetitivenessDifference: true,
      considerHealthDifference: true,
      considerAgeDifference: true
    },
    // 灌木与草地的竞争
    {
      sourceType: VegetationType.SHRUB,
      targetType: VegetationType.GRASS,
      interactionType: VegetationInteractionType.COMPETITION,
      strength: 0.6,
      distance: 3,
      considerGrowthStage: true,
      considerHeightDifference: true,
      considerWidthDifference: true,
      considerCompetitivenessDifference: true,
      considerHealthDifference: true,
      considerAgeDifference: true
    },
    // 草地与草地的竞争
    {
      sourceType: VegetationType.GRASS,
      targetType: VegetationType.GRASS,
      interactionType: VegetationInteractionType.COMPETITION,
      strength: 0.3,
      distance: 2,
      considerGrowthStage: true,
      considerHeightDifference: true,
      considerWidthDifference: true,
      considerCompetitivenessDifference: true,
      considerHealthDifference: true,
      considerAgeDifference: true
    },
    // 水生植物与水生植物的竞争
    {
      sourceType: VegetationType.AQUATIC,
      targetType: VegetationType.AQUATIC,
      interactionType: VegetationInteractionType.COMPETITION,
      strength: 0.4,
      distance: 3,
      considerGrowthStage: true,
      considerHeightDifference: true,
      considerWidthDifference: true,
      considerCompetitivenessDifference: true,
      considerHealthDifference: true,
      considerAgeDifference: true
    },
    // 季节性植物与季节性植物的竞争
    {
      sourceType: VegetationType.SEASONAL,
      targetType: VegetationType.SEASONAL,
      interactionType: VegetationInteractionType.COMPETITION,
      strength: 0.3,
      distance: 2,
      considerGrowthStage: true,
      considerHeightDifference: true,
      considerWidthDifference: true,
      considerCompetitivenessDifference: true,
      considerHealthDifference: true,
      considerAgeDifference: true
    }
  ];

  /**
   * 构造函数
   * @param useDefaultRules 是否使用默认规则
   */
  constructor(useDefaultRules: boolean = true) {
    if (useDefaultRules) {
      this.rules = [...this.defaultRules];
    }
  }

  /**
   * 添加交互规则
   * @param rule 交互规则
   */
  public addRule(rule: VegetationInteractionRule): void {
    this.rules.push(rule);
  }

  /**
   * 移除交互规则
   * @param index 规则索引
   */
  public removeRule(index: number): void {
    if (index >= 0 && index < this.rules.length) {
      this.rules.splice(index, 1);
    }
  }

  /**
   * 获取交互规则
   * @returns 交互规则列表
   */
  public getRules(): VegetationInteractionRule[] {
    return this.rules;
  }

  /**
   * 清除所有交互规则
   */
  public clearRules(): void {
    this.rules = [];
  }

  /**
   * 重置为默认交互规则
   */
  public resetToDefaultRules(): void {
    this.rules = [...this.defaultRules];
  }

  /**
   * 计算植被交互
   * @param source 源植被
   * @param target 目标植被
   * @param distance 距离
   * @returns 交互结果
   */
  public calculateInteraction(source: VegetationData, target: VegetationData, distance: number): VegetationInteractionResult | null {
    // 查找适用的交互规则
    const rule = this.findApplicableRule(source, target);
    if (!rule) {
      return null;
    }

    // 检查距离
    if (distance > rule.distance) {
      return null;
    }

    // 计算交互强度
    const strength = this.calculateInteractionStrength(source, target, rule, distance);

    // 计算健康度变化
    const { sourceHealthChange, targetHealthChange } = this.calculateHealthChanges(source, target, rule, strength);

    // 计算生长速度变化
    const { sourceGrowthRateChange, targetGrowthRateChange } = this.calculateGrowthRateChanges(source, target, rule, strength);

    // 计算竞争力变化
    const { sourceCompetitivenessChange, targetCompetitivenessChange } = this.calculateCompetitivenessChanges(source, target, rule, strength);

    // 创建交互结果
    return {
      source,
      target,
      interactionType: rule.interactionType,
      strength,
      sourceHealthChange,
      targetHealthChange,
      sourceGrowthRateChange,
      targetGrowthRateChange,
      sourceCompetitivenessChange,
      targetCompetitivenessChange,
      userData: {}
    };
  }

  /**
   * 应用交互结果
   * @param result 交互结果
   */
  public applyInteractionResult(result: VegetationInteractionResult): void {
    // 应用健康度变化
    result.source.health = Math.max(0, Math.min(1, result.source.health + result.sourceHealthChange));
    result.target.health = Math.max(0, Math.min(1, result.target.health + result.targetHealthChange));

    // 应用生长速度变化
    result.source.growthRate = Math.max(0, result.source.growthRate + result.sourceGrowthRateChange);
    result.target.growthRate = Math.max(0, result.target.growthRate + result.targetGrowthRateChange);

    // 应用竞争力变化
    result.source.competitiveness = Math.max(0, Math.min(1, result.source.competitiveness + result.sourceCompetitivenessChange));
    result.target.competitiveness = Math.max(0, Math.min(1, result.target.competitiveness + result.targetCompetitivenessChange));
  }

  /**
   * 查找适用的交互规则
   * @param source 源植被
   * @param target 目标植被
   * @returns 交互规则
   */
  private findApplicableRule(source: VegetationData, target: VegetationData): VegetationInteractionRule | null {
    // 遍历所有规则
    for (const rule of this.rules) {
      // 检查植被类型
      if (rule.sourceType !== source.type || rule.targetType !== target.type) {
        continue;
      }

      // 检查生长阶段
      if (rule.considerGrowthStage &&
          rule.sourceGrowthStage !== undefined &&
          rule.targetGrowthStage !== undefined &&
          (rule.sourceGrowthStage !== source.growthStage || rule.targetGrowthStage !== target.growthStage)) {
        continue;
      }

      // 找到适用的规则
      return rule;
    }

    return null;
  }

  /**
   * 计算交互强度
   * @param source 源植被
   * @param target 目标植被
   * @param rule 交互规则
   * @param distance 距离
   * @returns 交互强度
   */
  private calculateInteractionStrength(source: VegetationData, target: VegetationData, rule: VegetationInteractionRule, distance: number): number {
    // 基础强度
    let strength = rule.strength;

    // 距离衰减
    const distanceFactor = 1 - (distance / rule.distance);
    strength *= distanceFactor;

    // 考虑高度差异
    if (rule.considerHeightDifference) {
      const heightDifference = source.currentHeight - target.currentHeight;
      const heightFactor = heightDifference > 0 ? 1 + (heightDifference / source.maxHeight) * 0.5 : 1 - (Math.abs(heightDifference) / source.maxHeight) * 0.5;
      strength *= heightFactor;
    }

    // 考虑宽度差异
    if (rule.considerWidthDifference) {
      const widthDifference = source.currentWidth - target.currentWidth;
      const widthFactor = widthDifference > 0 ? 1 + (widthDifference / source.maxWidth) * 0.3 : 1 - (Math.abs(widthDifference) / source.maxWidth) * 0.3;
      strength *= widthFactor;
    }

    // 考虑竞争力差异
    if (rule.considerCompetitivenessDifference) {
      const competitivenessDifference = source.competitiveness - target.competitiveness;
      const competitivenessFactor = competitivenessDifference > 0 ? 1 + competitivenessDifference * 0.5 : 1 - Math.abs(competitivenessDifference) * 0.5;
      strength *= competitivenessFactor;
    }

    // 考虑健康度差异
    if (rule.considerHealthDifference) {
      const healthDifference = source.health - target.health;
      const healthFactor = healthDifference > 0 ? 1 + healthDifference * 0.3 : 1 - Math.abs(healthDifference) * 0.3;
      strength *= healthFactor;
    }

    // 考虑年龄差异
    if (rule.considerAgeDifference) {
      const ageRatioSource = source.age / source.maxAge;
      const ageRatioTarget = target.age / target.maxAge;
      const ageDifference = ageRatioSource - ageRatioTarget;
      const ageFactor = ageDifference > 0 ? 1 + ageDifference * 0.2 : 1 - Math.abs(ageDifference) * 0.2;
      strength *= ageFactor;
    }

    // 确保强度在有效范围内
    return Math.max(0, Math.min(1, strength));
  }

  /**
   * 计算健康度变化
   * @param source 源植被
   * @param target 目标植被
   * @param rule 交互规则
   * @param strength 交互强度
   * @returns 健康度变化
   */
  private calculateHealthChanges(source: VegetationData, target: VegetationData, rule: VegetationInteractionRule, strength: number): { sourceHealthChange: number, targetHealthChange: number } {
    let sourceHealthChange = 0;
    let targetHealthChange = 0;

    switch (rule.interactionType) {
      case VegetationInteractionType.COMPETITION:
        // 竞争：双方健康度都下降，但强者下降得少
        sourceHealthChange = -strength * 0.01 * (1 - source.competitiveness);
        targetHealthChange = -strength * 0.01 * (1 - target.competitiveness);
        break;
      case VegetationInteractionType.FACILITATION:
        // 促进：目标健康度上升，源健康度略微下降
        sourceHealthChange = -strength * 0.005;
        targetHealthChange = strength * 0.02;
        break;
      case VegetationInteractionType.NEUTRAL:
        // 中性：双方健康度不变
        sourceHealthChange = 0;
        targetHealthChange = 0;
        break;
      case VegetationInteractionType.PARASITISM:
        // 寄生：源健康度上升，目标健康度下降
        sourceHealthChange = strength * 0.02;
        targetHealthChange = -strength * 0.03;
        break;
      case VegetationInteractionType.SYMBIOSIS:
        // 共生：双方健康度都上升
        sourceHealthChange = strength * 0.01;
        targetHealthChange = strength * 0.01;
        break;
    }

    return { sourceHealthChange, targetHealthChange };
  }

  /**
   * 计算生长速度变化
   * @param source 源植被
   * @param target 目标植被
   * @param rule 交互规则
   * @param strength 交互强度
   * @returns 生长速度变化
   */
  private calculateGrowthRateChanges(source: VegetationData, target: VegetationData, rule: VegetationInteractionRule, strength: number): { sourceGrowthRateChange: number, targetGrowthRateChange: number } {
    let sourceGrowthRateChange = 0;
    let targetGrowthRateChange = 0;

    switch (rule.interactionType) {
      case VegetationInteractionType.COMPETITION:
        // 竞争：双方生长速度都下降，但强者下降得少
        sourceGrowthRateChange = -strength * 0.1 * (1 - source.competitiveness);
        targetGrowthRateChange = -strength * 0.1 * (1 - target.competitiveness);
        break;
      case VegetationInteractionType.FACILITATION:
        // 促进：目标生长速度上升，源生长速度略微下降
        sourceGrowthRateChange = -strength * 0.05;
        targetGrowthRateChange = strength * 0.2;
        break;
      case VegetationInteractionType.NEUTRAL:
        // 中性：双方生长速度不变
        sourceGrowthRateChange = 0;
        targetGrowthRateChange = 0;
        break;
      case VegetationInteractionType.PARASITISM:
        // 寄生：源生长速度上升，目标生长速度下降
        sourceGrowthRateChange = strength * 0.2;
        targetGrowthRateChange = -strength * 0.3;
        break;
      case VegetationInteractionType.SYMBIOSIS:
        // 共生：双方生长速度都上升
        sourceGrowthRateChange = strength * 0.1;
        targetGrowthRateChange = strength * 0.1;
        break;
    }

    return { sourceGrowthRateChange, targetGrowthRateChange };
  }

  /**
   * 计算竞争力变化
   * @param source 源植被
   * @param target 目标植被
   * @param rule 交互规则
   * @param strength 交互强度
   * @returns 竞争力变化
   */
  private calculateCompetitivenessChanges(source: VegetationData, target: VegetationData, rule: VegetationInteractionRule, strength: number): { sourceCompetitivenessChange: number, targetCompetitivenessChange: number } {
    let sourceCompetitivenessChange = 0;
    let targetCompetitivenessChange = 0;

    switch (rule.interactionType) {
      case VegetationInteractionType.COMPETITION:
        // 竞争：强者竞争力略微上升，弱者竞争力下降
        if (source.competitiveness > target.competitiveness) {
          sourceCompetitivenessChange = strength * 0.005;
          targetCompetitivenessChange = -strength * 0.01;
        } else {
          sourceCompetitivenessChange = -strength * 0.01;
          targetCompetitivenessChange = strength * 0.005;
        }
        break;
      case VegetationInteractionType.FACILITATION:
        // 促进：目标竞争力上升，源竞争力不变
        sourceCompetitivenessChange = 0;
        targetCompetitivenessChange = strength * 0.01;
        break;
      case VegetationInteractionType.NEUTRAL:
        // 中性：双方竞争力不变
        sourceCompetitivenessChange = 0;
        targetCompetitivenessChange = 0;
        break;
      case VegetationInteractionType.PARASITISM:
        // 寄生：源竞争力上升，目标竞争力下降
        sourceCompetitivenessChange = strength * 0.01;
        targetCompetitivenessChange = -strength * 0.02;
        break;
      case VegetationInteractionType.SYMBIOSIS:
        // 共生：双方竞争力都略微上升
        sourceCompetitivenessChange = strength * 0.005;
        targetCompetitivenessChange = strength * 0.005;
        break;
    }

    return { sourceCompetitivenessChange, targetCompetitivenessChange };
  }
}
