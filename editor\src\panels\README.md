# 面部动画预设面板 (FacialAnimationPresetPanel)

完善后的面部动画预设面板，提供了丰富的功能和优秀的用户体验。

## 🚀 主要功能

### 1. 统计信息展示
- **预设总数**：显示系统中所有可用预设的数量
- **模板总数**：显示可用模板的数量
- **收藏预设**：显示用户收藏的预设数量
- **最近使用**：显示最近使用的预设数量

### 2. 智能搜索与筛选
- **文本搜索**：支持按预设名称进行模糊搜索
- **类型筛选**：按预设类型筛选（标准、文化特定、情感组合等）
- **文化筛选**：按文化背景筛选预设
- **收藏筛选**：快速查看收藏的预设

### 3. 收藏功能
- **一键收藏**：点击星标图标即可收藏/取消收藏预设
- **收藏管理**：统一管理所有收藏的预设
- **快速访问**：收藏的预设可以快速访问和应用

### 4. 历史记录
- **自动记录**：自动记录每次应用预设的历史
- **历史查看**：可以查看完整的使用历史
- **快速重用**：从历史记录中快速重新应用预设
- **历史清理**：支持清空历史记录

### 5. 批量操作
- **批量选择**：支持选择多个预设进行批量操作
- **批量导出**：一次性导出多个预设
- **批量收藏**：批量添加预设到收藏
- **批量删除**：批量删除不需要的预设

### 6. 快速预览
- **悬停预览**：鼠标悬停即可快速预览预设效果
- **模态预览**：点击预览按钮打开详细预览窗口
- **实时渲染**：基于Three.js的实时3D预览

### 7. 最近使用标签页
- **最近预设**：显示最近使用的预设，方便快速重用
- **卡片展示**：以卡片形式展示，包含预设信息和操作按钮
- **时间排序**：按使用时间倒序排列

## 📋 组件属性

```typescript
interface FacialAnimationPresetPanelProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 默认活动标签 */
  defaultActiveTab?: string;
  /** 默认预设类型 */
  defaultPresetType?: FacialAnimationPresetType;
  /** 默认文化 */
  defaultCulture?: string;
  /** 是否显示统计信息 */
  showStatistics?: boolean;
  /** 是否显示快速操作栏 */
  showQuickActions?: boolean;
  /** 是否显示收藏功能 */
  showFavorites?: boolean;
  /** 是否显示历史记录 */
  showHistory?: boolean;
  /** 是否启用批量操作 */
  enableBatchOperations?: boolean;
  /** 应用预设回调 */
  onPresetApply?: (preset: any) => void;
  /** 应用模板回调 */
  onTemplateApply?: (template: any, parameters: any) => void;
  /** 预设收藏状态变更回调 */
  onPresetFavoriteChange?: (presetId: string, isFavorite: boolean) => void;
  /** 批量操作回调 */
  onBatchOperation?: (operation: string, presetIds: string[]) => void;
}
```

## 🎯 使用示例

### 基础使用

```tsx
import { FacialAnimationPresetPanel } from './panels/FacialAnimationPresetPanel';

<FacialAnimationPresetPanel
  entityId="character-001"
  editable={true}
  onPresetApply={(preset) => {
    console.log('应用预设:', preset);
  }}
  onTemplateApply={(template, parameters) => {
    console.log('应用模板:', template, parameters);
  }}
/>
```

### 完整配置

```tsx
<FacialAnimationPresetPanel
  entityId="character-001"
  editable={true}
  defaultActiveTab="presets"
  defaultPresetType={FacialAnimationPresetType.STANDARD}
  defaultCulture="global"
  showStatistics={true}
  showQuickActions={true}
  showFavorites={true}
  showHistory={true}
  enableBatchOperations={true}
  onPresetApply={handlePresetApply}
  onTemplateApply={handleTemplateApply}
  onPresetFavoriteChange={handleFavoriteChange}
  onBatchOperation={handleBatchOperation}
/>
```

## 🎨 样式定制

面板支持通过CSS类名进行样式定制：

```less
.facial-animation-preset-panel {
  // 自定义面板整体样式
  
  .ant-card.statistics-card {
    // 自定义统计信息卡片样式
  }
  
  .ant-card.quick-actions-card {
    // 自定义快速操作栏样式
  }
  
  .recent-tab {
    // 自定义最近使用标签页样式
  }
  
  &.batch-mode {
    // 自定义批量操作模式样式
  }
}
```

## 📱 响应式设计

面板完全支持响应式设计，在不同屏幕尺寸下都能提供良好的用户体验：

- **桌面端**：完整功能展示，多列布局
- **平板端**：适配中等屏幕，调整布局密度
- **移动端**：单列布局，优化触摸操作

## 🌙 主题支持

支持明暗主题切换，通过添加CSS类名实现：

```tsx
<div className={`facial-animation-preset-panel ${isDark ? 'dark-theme' : ''}`}>
  <FacialAnimationPresetPanel {...props} />
</div>
```

## 🔧 高级功能

### 数据持久化

- **本地存储**：历史记录和用户偏好设置自动保存到localStorage
- **设置同步**：支持跨会话保持用户设置
- **数据导入导出**：支持预设和模板的导入导出

### 性能优化

- **虚拟滚动**：大量预设时使用虚拟滚动提升性能
- **懒加载**：预设缩略图按需加载
- **缓存机制**：智能缓存减少重复请求

### 国际化

完全支持国际化，所有文本都通过i18n系统管理：

```typescript
// 支持的语言键
'editor.animation.presets'
'editor.animation.templates'
'editor.animation.favorites'
'editor.animation.history'
'editor.animation.batchOperations'
// ... 更多
```

## 🚀 未来计划

- [ ] AI智能推荐预设
- [ ] 预设版本管理
- [ ] 协作编辑功能
- [ ] 云端同步
- [ ] 更多预设模板
- [ ] 高级动画编辑器集成
- [ ] 性能监控和分析
- [ ] 插件系统支持

## 📄 许可证

MIT License
