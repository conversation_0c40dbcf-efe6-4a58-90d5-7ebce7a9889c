/**
 * 优化的地形材质
 * 用于提高地形渲染性能
 */
import * as THREE from 'three';
import { TerrainTextureLayer } from '../components/TerrainComponent';
/**
 * 优化的地形材质选项接口
 */
export interface OptimizedTerrainMaterialOptions {
    /** 纹理层 */
    layers?: TerrainTextureLayer[];
    /** 最大高度 */
    maxHeight?: number;
    /** 是否使用LOD */
    useLOD?: boolean;
    /** LOD距离 */
    lodDistance?: number;
    /** LOD因子 */
    lodFactor?: number;
    /** 是否使用雾 */
    useFog?: boolean;
    /** 雾颜色 */
    fogColor?: THREE.Color;
    /** 雾近距离 */
    fogNear?: number;
    /** 雾远距离 */
    fogFar?: number;
    /** 环境光颜色 */
    ambientColor?: THREE.Color;
    /** 光源颜色 */
    lightColor?: THREE.Color;
    /** 光源位置 */
    lightPosition?: THREE.Vector3;
    /** 是否使用裁剪平面 */
    useClipPlane?: boolean;
    /** 裁剪平面 */
    clipPlane?: THREE.Vector4;
    /** 是否使用混合贴图 */
    useBlendMaps?: boolean;
    /** 是否使用线框 */
    wireframe?: boolean;
}
/**
 * 优化的地形材质类
 */
export declare class OptimizedTerrainMaterial extends THREE.ShaderMaterial {
    /** 纹理层 */
    private layers;
    /** 低分辨率纹理渲染目标 */
    private lowResRenderTarget;
    /** 低分辨率相机 */
    private lowResCamera;
    /** 低分辨率场景 */
    private lowResScene;
    /** 低分辨率网格 */
    private lowResMesh;
    /** 是否需要更新低分辨率纹理 */
    private needsLowResUpdate;
    /** 渲染器 */
    private renderer;
    /**
     * 创建优化的地形材质
     * @param options 选项
     */
    constructor(options?: OptimizedTerrainMaterialOptions);
    /**
     * 初始化低分辨率渲染
     */
    private initLowResRendering;
    /**
     * 获取纹理层
     * @returns 纹理层数组
     */
    getLayers(): TerrainTextureLayer[];
    /**
     * 设置渲染器
     * @param renderer 渲染器
     */
    setRenderer(renderer: THREE.WebGLRenderer): void;
    /**
     * 更新低分辨率纹理
     * @param geometry 几何体
     */
    updateLowResTexture(geometry: THREE.BufferGeometry): void;
    /**
     * 标记需要更新低分辨率纹理
     */
    markLowResTextureNeedsUpdate(): void;
    /**
     * 检查是否需要更新低分辨率纹理
     * @returns 是否需要更新
     */
    needsLowResTextureUpdate(): boolean;
    /**
     * 设置LOD参数
     * @param distance LOD距离
     * @param factor LOD因子
     */
    setLODParams(distance: number, factor: number): void;
    /**
     * 设置雾参数
     * @param useFog 是否使用雾
     * @param fogColor 雾颜色
     * @param fogNear 雾近距离
     * @param fogFar 雾远距离
     */
    setFogParams(useFog: boolean, fogColor?: THREE.Color, fogNear?: number, fogFar?: number): void;
    /**
     * 设置光照参数
     * @param ambientColor 环境光颜色
     * @param lightColor 光源颜色
     * @param lightPosition 光源位置
     */
    setLightParams(ambientColor?: THREE.Color, lightColor?: THREE.Color, lightPosition?: THREE.Vector3): void;
    /**
     * 设置裁剪平面
     * @param useClipPlane 是否使用裁剪平面
     * @param clipPlane 裁剪平面
     */
    setClipPlane(useClipPlane: boolean, clipPlane?: THREE.Vector4): void;
    /**
     * 更新纹理层
     * @param layers 纹理层
     */
    updateLayers(layers: TerrainTextureLayer[]): void;
    /**
     * 释放资源
     */
    dispose(): void;
}
