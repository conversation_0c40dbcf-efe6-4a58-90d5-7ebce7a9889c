/**
 * 心脏移植手术流程管理
 */
import { Entity } from '../core/Entity';
import { System } from '../core/System';
import { VascularSystem } from '../physics/softbody/specialized/VascularSystem';
import { Vector3 } from '../math/Vector3';
/**
 * 手术阶段枚举
 */
export declare enum SurgeryStage {
    /** 准备阶段 */
    PREPARATION = "preparation",
    /** 切开胸腔 */
    CHEST_OPENING = "chest_opening",
    /** 连接体外循环 */
    BYPASS_CONNECTION = "bypass_connection",
    /** 移除病心 */
    HEART_REMOVAL = "heart_removal",
    /** 植入新心脏 */
    HEART_IMPLANTATION = "heart_implantation",
    /** 血管吻合 */
    VASCULAR_ANASTOMOSIS = "vascular_anastomosis",
    /** 断开体外循环 */
    BYPASS_DISCONNECTION = "bypass_disconnection",
    /** 关闭胸腔 */
    CHEST_CLOSURE = "chest_closure",
    /** 完成 */
    COMPLETED = "completed"
}
/**
 * 手术工具类型
 */
export declare enum SurgicalToolType {
    /** 手术刀 */
    SCALPEL = "scalpel",
    /** 镊子 */
    FORCEPS = "forceps",
    /** 持针器 */
    NEEDLE_HOLDER = "needle_holder",
    /** 缝合线 */
    SUTURE = "suture",
    /** 夹子 */
    CLAMP = "clamp"
}
/**
 * 手术工具接口
 */
export interface SurgicalTool {
    /** 工具ID */
    id: string;
    /** 工具名称 */
    name: string;
    /** 工具类型 */
    type: SurgicalToolType;
    /** 是否可用 */
    available: boolean;
    /** 工具实体 */
    entity: Entity;
}
/**
 * 工具交互管理器
 */
export declare class ToolInteractionManager {
    constructor(world: any);
}
/**
 * 手术工具系统
 */
export declare class SurgicalToolSystem extends System {
    /** 可用工具 */
    private tools;
    /** 当前选中的工具 */
    private activeTool;
    /** 工具交互管理器 */
    private interactionManager;
    /**
     * 初始化手术工具系统
     */
    initialize(): void;
    /**
     * 创建手术刀
     */
    private createScalpel;
    /**
     * 创建镊子
     */
    private createForceps;
    /**
     * 创建持针器
     */
    private createNeedleHolder;
    /**
     * 创建缝合线
     */
    private createSuture;
    /**
     * 创建夹子
     */
    private createClamp;
    /**
     * 执行切割操作
     * @param targetEntity 目标实体
     * @param cutPath 切割路径
     */
    performIncision(targetEntity: Entity, cutPath: Vector3[]): void;
    /**
     * 从路径创建切割平面
     * @param cutPath 切割路径
     * @returns 切割平面数组
     */
    private createCutPlanesFromPath;
}
/**
 * 患者监控系统
 */
export declare class PatientMonitoringSystem extends System {
    /** 心率 */
    private heartRate;
    /** 血压 */
    private bloodPressure;
    /** 体温 */
    private bodyTemperature;
    /**
     * 获取心率
     */
    getHeartRate(): number;
    /**
     * 获取血压
     */
    getBloodPressure(): {
        systolic: number;
        diastolic: number;
    };
    /**
     * 获取体温
     */
    getBodyTemperature(): number;
}
/**
 * 血管信息接口
 */
export interface VesselInfo {
    /** 供体血管 */
    donor: any;
    /** 患者血管 */
    patient: any;
}
export declare class HeartTransplantProcedure {
    /** 手术阶段 */
    private currentStage;
    /** 患者实体 */
    private patientEntity;
    /** 供体心脏实体 */
    private donorHeartEntity;
    /** 手术工具系统 */
    private toolSystem;
    /** 血管系统 */
    private vascularSystem;
    /** 监控系统 */
    private monitoringSystem;
    /**
     * 构造函数
     */
    constructor(patientEntity: Entity, donorHeartEntity: Entity, toolSystem: SurgicalToolSystem, vascularSystem: VascularSystem, monitoringSystem: PatientMonitoringSystem);
    /**
     * 执行心脏移植手术的主要阶段
     */
    performTransplantStages(): void;
    /**
     * 准备患者
     */
    private preparePatient;
    /**
     * 切开胸腔
     */
    private openChestCavity;
    /**
     * 连接体外循环
     */
    private connectBypass;
    /**
     * 移除病心
     */
    private removeDiseasedHeart;
    /**
     * 植入新心脏
     */
    private implantDonorHeart;
    /**
     * 断开体外循环
     */
    private disconnectBypass;
    /**
     * 关闭胸腔
     */
    private closeChestCavity;
    /**
     * 执行血管吻合 - 连接新心脏与患者血管
     */
    private performVascularAnastomosis;
    /**
     * 根据名称获取血管信息
     * @param vesselName 血管名称
     * @returns 血管信息
     */
    private getVesselByName;
    /**
     * 获取当前手术阶段
     */
    getCurrentStage(): SurgeryStage;
    /**
     * 获取手术进度（0-1）
     */
    getProgress(): number;
}
