import { SubClip } from './SubClip';
import { AnimationSubClip } from './AnimationSubClip';
/**
 * 子片段过渡事件类型
 */
export declare enum SubClipTransitionEventType {
    /** 过渡开始 */
    TRANSITION_START = "transitionStart",
    /** 过渡结束 */
    TRANSITION_END = "transitionEnd",
    /** 过渡更新 */
    TRANSITION_UPDATE = "transitionUpdate",
    /** 过渡取消 */
    TRANSITION_CANCEL = "transitionCancel"
}
/**
 * 过渡类型
 */
export declare enum TransitionType {
    /** 线性 */
    LINEAR = "linear",
    /** 缓入 */
    EASE_IN = "easeIn",
    /** 缓出 */
    EASE_OUT = "easeOut",
    /** 缓入缓出 */
    EASE_IN_OUT = "easeInOut",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 子片段过渡配置
 */
export interface SubClipTransitionConfig {
    /** 过渡名称 */
    name?: string;
    /** 源子片段 */
    fromClip?: SubClip | AnimationSubClip;
    /** 目标子片段 */
    toClip?: SubClip | AnimationSubClip;
    /** 过渡时间（秒） */
    duration?: number;
    /** 过渡类型 */
    type?: TransitionType;
    /** 自定义过渡函数 */
    customTransition?: (t: number) => number;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * 子片段过渡
 */
export declare class SubClipTransition {
    /** 过渡名称 */
    private name;
    /** 源子片段 */
    private fromClip;
    /** 目标子片段 */
    private toClip;
    /** 过渡时间（秒） */
    private duration;
    /** 过渡类型 */
    private type;
    /** 自定义过渡函数 */
    private customTransition?;
    /** 是否启用调试 */
    private debug;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否正在过渡 */
    private isTransitioning;
    /** 当前过渡时间 */
    private currentTime;
    /** 过渡进度（0-1） */
    private progress;
    /** 源动作 */
    private fromAction;
    /** 目标动作 */
    private toAction;
    /** 混合后的片段 */
    private blendedClip;
    /**
     * 创建子片段过渡
     * @param config 配置
     */
    constructor(config?: SubClipTransitionConfig);
    /**
     * 获取过渡名称
     * @returns 过渡名称
     */
    getName(): string;
    /**
     * 设置过渡名称
     * @param name 过渡名称
     */
    setName(name: string): void;
    /**
     * 获取源子片段
     * @returns 源子片段
     */
    getFromClip(): SubClip | AnimationSubClip | null;
    /**
     * 设置源子片段
     * @param clip 源子片段
     */
    setFromClip(clip: SubClip | AnimationSubClip): void;
    /**
     * 获取目标子片段
     * @returns 目标子片段
     */
    getToClip(): SubClip | AnimationSubClip | null;
    /**
     * 设置目标子片段
     * @param clip 目标子片段
     */
    setToClip(clip: SubClip | AnimationSubClip): void;
    /**
     * 获取过渡时间
     * @returns 过渡时间（秒）
     */
    getDuration(): number;
    /**
     * 设置过渡时间
     * @param duration 过渡时间（秒）
     */
    setDuration(duration: number): void;
    /**
     * 获取过渡类型
     * @returns 过渡类型
     */
    getType(): TransitionType;
    /**
     * 设置过渡类型
     * @param type 过渡类型
     */
    setType(type: TransitionType): void;
    /**
     * 设置自定义过渡函数
     * @param func 自定义过渡函数
     */
    setCustomTransition(func: (t: number) => number): void;
    /**
     * 是否正在过渡
     * @returns 是否正在过渡
     */
    isInTransition(): boolean;
    /**
     * 获取过渡进度
     * @returns 过渡进度（0-1）
     */
    getProgress(): number;
    /**
     * 开始过渡
     * @param fromClip 源子片段
     * @param toClip 目标子片段
     * @param duration 过渡时间（秒）
     */
    start(fromClip?: SubClip | AnimationSubClip, toClip?: SubClip | AnimationSubClip, duration?: number): void;
    /**
     * 停止过渡
     */
    stop(): void;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEventListener(event: SubClipTransitionEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEventListener(event: SubClipTransitionEventType, listener: (data: any) => void): void;
}
