/**
 * 增强水体交互示例入口
 */
import { EnhancedWaterInteractionExample } from './EnhancedWaterInteractionExample';

// 等待DOM加载完成
window.addEventListener('DOMContentLoaded', () => {
  // 创建示例
  const example = new EnhancedWaterInteractionExample();
  
  // 启动示例
  example.start();
  
  // 输出提示信息
  console.log('增强水体交互示例已启动');
  console.log('这个示例展示了以下增强功能：');
  console.log('1. 体素浮力计算 - 更精确的浮力模拟');
  console.log('2. 浮力稳定化 - 防止物体在水面上不自然抖动');
  console.log('3. 方向性阻力 - 考虑物体形状的阻力计算');
  console.log('4. 湍流阻力 - 在湍流水域中的真实阻力表现');
  console.log('5. 旋转阻力 - 物体在水中旋转受到的阻力');
  console.log('6. 高级水流交互 - 更真实的水流冲击力和扭矩效应');
});
