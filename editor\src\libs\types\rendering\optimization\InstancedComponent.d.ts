/**
 * 实例化组件
 * 用于为实体添加实例化渲染功能
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
/**
 * 实例数据接口
 */
export interface InstanceData {
    /** 位置 */
    position: THREE.Vector3;
    /** 旋转（四元数） */
    quaternion: THREE.Quaternion;
    /** 缩放 */
    scale: THREE.Vector3;
    /** 颜色 */
    color?: THREE.Color;
    /** 自定义数据 */
    userData?: any;
}
/**
 * 实例化组件选项接口
 */
export interface InstancedComponentOptions {
    /** 原始网格 */
    originalMesh?: THREE.Mesh;
    /** 最大实例数量 */
    maxInstanceCount?: number;
    /** 是否使用颜色 */
    useColor?: boolean;
    /** 是否动态更新 */
    dynamic?: boolean;
    /** 是否可见 */
    visible?: boolean;
}
/**
 * 实例化组件类
 */
export declare class InstancedComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 原始网格 */
    private originalMesh;
    /** 实例化网格 */
    private instancedMesh;
    /** 最大实例数量 */
    private maxInstanceCount;
    /** 是否使用颜色 */
    private useColor;
    /** 是否动态更新 */
    private dynamic;
    /** 是否可见 */
    private visible;
    /** 实例列表 */
    private instances;
    /** 实例ID到索引的映射 */
    private instanceIdToIndex;
    /** 下一个实例ID */
    private nextInstanceId;
    /** 是否已更新 */
    private updated;
    /** 临时矩阵 */
    private tempMatrix;
    /**
     * 创建实例化组件
     * @param options 实例化组件选项
     */
    constructor(options?: InstancedComponentOptions);
    /**
     * 当组件附加到实体时调用
     */
    protected onAttach(): void;
    /**
     * 当组件从实体分离时调用
     */
    protected onDetach(): void;
    /**
     * 设置原始网格
     * @param mesh 网格
     */
    setOriginalMesh(mesh: THREE.Mesh): void;
    /**
     * 获取原始网格
     * @returns 网格
     */
    getOriginalMesh(): THREE.Mesh | null;
    /**
     * 设置实例化网格
     * @param mesh 实例化网格
     */
    setInstancedMesh(mesh: THREE.InstancedMesh | null): void;
    /**
     * 获取实例化网格
     * @returns 实例化网格
     */
    getInstancedMesh(): THREE.InstancedMesh | null;
    /**
     * 设置最大实例数量
     * @param count 数量
     */
    setMaxInstanceCount(count: number): void;
    /**
     * 获取最大实例数量
     * @returns 数量
     */
    getMaxInstanceCount(): number;
    /**
     * 设置是否使用颜色
     * @param use 是否使用
     */
    setUseColor(use: boolean): void;
    /**
     * 获取是否使用颜色
     * @returns 是否使用
     */
    isUseColor(): boolean;
    /**
     * 设置是否动态更新
     * @param dynamic 是否动态
     */
    setDynamic(dynamic: boolean): void;
    /**
     * 获取是否动态更新
     * @returns 是否动态
     */
    isDynamic(): boolean;
    /**
     * 设置是否可见
     * @param visible 是否可见
     */
    setVisible(visible: boolean): void;
    /**
     * 获取是否可见
     * @returns 是否可见
     */
    isVisible(): boolean;
    /**
     * 添加实例
     * @param instance 实例数据
     * @returns 实例ID
     */
    addInstance(instance: InstanceData): number;
    /**
     * 移除实例
     * @param instanceId 实例ID
     * @returns 是否成功
     */
    removeInstance(instanceId: number): boolean;
    /**
     * 更新实例
     * @param instanceId 实例ID
     * @param instance 实例数据
     * @returns 是否成功
     */
    updateInstance(instanceId: number, instance: Partial<InstanceData>): boolean;
    /**
     * 获取实例
     * @param instanceId 实例ID
     * @returns 实例数据
     */
    getInstance(instanceId: number): InstanceData | null;
    /**
     * 获取所有实例
     * @returns 实例列表
     */
    getInstances(): InstanceData[];
    /**
     * 获取实例数量
     * @returns 实例数量
     */
    getInstanceCount(): number;
    /**
     * 清空实例
     */
    clearInstances(): void;
    /**
     * 设置是否已更新
     * @param updated 是否已更新
     */
    setUpdated(updated: boolean): void;
    /**
     * 获取是否已更新
     * @returns 是否已更新
     */
    isUpdated(): boolean;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
}
