/**
 * 面部动画模型适配器系统
 * 用于管理面部动画模型适配器
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationModelAdapterComponent, FacialAnimationModelType } from '../adapters/FacialAnimationModelAdapter';

/**
 * 面部动画模型适配器系统配置
 */
export interface FacialAnimationModelAdapterSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 默认模型类型 */
  defaultModelType?: FacialAnimationModelType;
  /** 是否自动检测混合形状 */
  autoDetectBlendShapes?: boolean;
}

/**
 * 面部动画模型适配器系统
 */
export class FacialAnimationModelAdapterSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'FacialAnimationModelAdapterSystem';

  /** 配置 */
  private config: FacialAnimationModelAdapterSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 模型适配器组件映射 */
  private adapters: Map<Entity, FacialAnimationModelAdapterComponent> = new Map();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: FacialAnimationModelAdapterSystemConfig = {}) {
    super(260); // 设置优先级

    this.config = {
      debug: false,
      defaultModelType: FacialAnimationModelType.GENERIC,
      autoDetectBlendShapes: true,
      ...config
    };

    if (this.config.debug) {
      console.log('面部动画模型适配器系统初始化');
    }
  }

  /**
   * 创建模型适配器
   * @param entity 实体
   * @param mesh 骨骼网格
   * @param modelType 模型类型
   * @returns 是否成功创建
   */
  public createModelAdapter(entity: Entity, mesh: THREE.SkinnedMesh, modelType?: FacialAnimationModelType): boolean {
    // 检查实体是否已有模型适配器
    if (this.adapters.has(entity)) {
      if (this.config.debug) {
        console.warn('实体已有模型适配器', entity);
      }
      return false;
    }

    // 检查是否为骨骼网格
    if (!(mesh instanceof THREE.SkinnedMesh)) {
      console.warn('不是骨骼网格', mesh);
      return false;
    }

    // 使用默认模型类型
    const type = modelType || this.config.defaultModelType!;

    // 创建模型适配器组件
    const adapter = new FacialAnimationModelAdapterComponent(mesh, type, this.config.debug);

    // 添加组件到实体
    entity.addComponent(adapter);

    // 添加组件到映射
    this.adapters.set(entity, adapter);

    if (this.config.debug) {
      console.log('创建模型适配器', entity, type);
    }

    return true;
  }

  /**
   * 获取模型适配器
   * @param entity 实体
   * @returns 模型适配器
   */
  public getModelAdapter(entity: Entity): FacialAnimationModelAdapterComponent | null {
    return this.adapters.get(entity) || null;
  }

  /**
   * 移除模型适配器
   * @param entity 实体
   */
  public removeModelAdapter(entity: Entity): void {
    const adapter = this.adapters.get(entity);
    if (adapter) {
      entity.removeComponent(adapter);
      this.adapters.delete(entity);

      if (this.config.debug) {
        console.log('移除模型适配器', entity);
      }
    }
  }

  /**
   * 自动检测模型类型
   * @param mesh 骨骼网格
   * @returns 模型类型
   */
  public detectModelType(mesh: THREE.SkinnedMesh): FacialAnimationModelType {
    // 检查是否为VRM模型
    if (mesh.userData && mesh.userData.vrm) {
      return FacialAnimationModelType.VRM;
    }

    // 检查是否为FBX模型
    if (mesh.userData && mesh.userData.type === 'fbx') {
      return FacialAnimationModelType.FBX;
    }

    // 检查是否为GLTF模型
    if (mesh.userData && (mesh.userData.type === 'gltf' || mesh.userData.type === 'glb')) {
      return FacialAnimationModelType.GLTF;
    }

    // 默认为通用模型
    return FacialAnimationModelType.GENERIC;
  }

  /**
   * 更新系统
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // 模型适配器系统不需要每帧更新
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清除适配器映射
    this.adapters.clear();

    if (this.config.debug) {
      console.log('面部动画模型适配器系统销毁');
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }
}
