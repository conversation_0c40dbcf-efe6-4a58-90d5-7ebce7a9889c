/**
 * 地形实例化渲染系统
 * 用于优化地形上大量相同对象的渲染
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { TerrainComponent } from './components/TerrainComponent';
/**
 * 地形实例数据接口
 */
export interface TerrainInstanceData {
    /** 位置 */
    position: THREE.Vector3;
    /** 旋转 */
    rotation: THREE.Euler;
    /** 缩放 */
    scale: THREE.Vector3;
    /** 颜色 */
    color: THREE.Color;
    /** 可见性 */
    visible: boolean;
    /** 用户数据 */
    userData: any;
}
/**
 * 地形实例组接口
 */
export interface TerrainInstanceGroup {
    /** 组ID */
    id: string;
    /** 几何体 */
    geometry: THREE.BufferGeometry;
    /** 材质 */
    material: THREE.Material;
    /** 实例化网格 */
    instancedMesh: THREE.InstancedMesh;
    /** 实例数据 */
    instances: TerrainInstanceData[];
    /** 可用索引 */
    availableIndices: number[];
    /** 实例ID到索引的映射 */
    instanceIdToIndex: Map<string, number>;
    /** 是否可见 */
    visible: boolean;
    /** 是否需要更新 */
    needsUpdate: boolean;
    /** 用户数据 */
    userData: any;
}
/**
 * 地形实例化渲染系统事件类型
 */
export declare enum TerrainInstancedRenderingSystemEventType {
    /** 实例组创建 */
    GROUP_CREATED = "group_created",
    /** 实例组销毁 */
    GROUP_DESTROYED = "group_destroyed",
    /** 实例添加 */
    INSTANCE_ADDED = "instance_added",
    /** 实例移除 */
    INSTANCE_REMOVED = "instance_removed",
    /** 实例更新 */
    INSTANCE_UPDATED = "instance_updated"
}
/**
 * 地形实例化渲染系统配置接口
 */
export interface TerrainInstancedRenderingSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 最大批处理大小 */
    maxBatchSize?: number;
    /** 是否使用视锥体剔除 */
    useFrustumCulling?: boolean;
    /** 是否使用八叉树 */
    useOctree?: boolean;
    /** 是否使用LOD */
    useInstanceLOD?: boolean;
    /** 是否使用阴影 */
    useInstanceShadow?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
}
/**
 * 地形实例化渲染系统类
 */
export declare class TerrainInstancedRenderingSystem extends System {
    /** 系统类型 */
    static readonly TYPE: string;
    /** 是否自动更新 */
    private autoUpdate;
    /** 更新频率 */
    private updateFrequency;
    /** 最大批处理大小 */
    private maxBatchSize;
    /** 是否使用视锥体剔除 */
    private useFrustumCulling;
    /** 是否使用八叉树 */
    private useOctree;
    /** 是否使用LOD */
    private useInstanceLOD;
    /** 是否使用阴影 */
    private useInstanceShadow;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 地形实体映射 */
    private terrainEntities;
    /** 实例组映射 */
    private instanceGroups;
    /** 几何体到实例组的映射 */
    private geometryToGroup;
    /** 八叉树 */
    private octree;
    /** 视锥体 */
    private frustum;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 调试网格 */
    private debugMeshes;
    /** 实例组计数器 */
    private groupCounter;
    /** 实例计数器 */
    private instanceCounter;
    /**
     * 创建地形实例化渲染系统
     * @param options 配置选项
     */
    constructor(options?: TerrainInstancedRenderingSystemOptions);
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    getType(): string;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化八叉树
     */
    private initializeOctree;
    /**
     * 更新系统
     * @param _deltaTime 帧间隔时间（秒）
     */
    update(_deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 获取场景
     * @returns 场景
     */
    private getScene;
    /**
     * 更新视锥体
     * @param camera 相机
     */
    private updateFrustum;
    /**
     * 更新所有实例组
     * @param camera 相机
     */
    private updateInstanceGroups;
    /**
     * 使用八叉树更新实例
     * @param camera 相机
     */
    private updateInstancesWithOctree;
    /**
     * 使用暴力方法更新实例
     * @param _camera 相机（未使用，依赖已更新的视锥体）
     */
    private updateInstancesWithBruteForce;
    /**
     * 根据索引获取实例ID
     * @param group 实例组
     * @param index 索引
     * @returns 实例ID
     */
    private getInstanceIdByIndex;
    /**
     * 更新实例LOD
     * @param instanceData 实例数据
     * @param distance 距离
     */
    private updateInstanceLOD;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 添加地形实体
     * @param entity 实体
     * @param component 地形组件
     */
    addTerrainEntity(entity: Entity, component: TerrainComponent): void;
    /**
     * 移除地形实体
     * @param entity 实体
     */
    removeTerrainEntity(entity: Entity): void;
    /**
     * 创建实例组
     * @param geometry 几何体
     * @param material 材质
     * @param maxInstances 最大实例数
     * @returns 实例组
     */
    createInstanceGroup(geometry: THREE.BufferGeometry, material: THREE.Material, maxInstances?: number): TerrainInstanceGroup;
    /**
     * 销毁实例组
     * @param groupId 组ID
     */
    destroyInstanceGroup(groupId: string): void;
    /**
     * 获取或创建实例组
     * @param geometry 几何体
     * @param material 材质
     * @returns 实例组
     */
    getOrCreateInstanceGroup(geometry: THREE.BufferGeometry, material: THREE.Material): TerrainInstanceGroup;
    /**
     * 添加实例
     * @param geometry 几何体
     * @param material 材质
     * @param instanceData 实例数据
     * @returns 实例ID
     */
    addInstance(geometry: THREE.BufferGeometry, material: THREE.Material, instanceData: TerrainInstanceData): string;
    /**
     * 移除实例
     * @param groupId 组ID
     * @param instanceId 实例ID
     */
    removeInstance(groupId: string, instanceId: string): void;
    /**
     * 更新实例
     * @param groupId 组ID
     * @param instanceId 实例ID
     * @param instanceData 实例数据
     */
    updateInstance(groupId: string, instanceId: string, instanceData: Partial<TerrainInstanceData>): void;
    /**
     * 更新实例矩阵
     * @param group 实例组
     * @param index 索引
     * @param instanceData 实例数据
     */
    private updateInstanceMatrix;
    /**
     * 获取八叉树统计信息
     * @returns 八叉树统计信息
     */
    getOctreeStats(): any;
    /**
     * 清空八叉树
     */
    clearOctree(): void;
    /**
     * 在指定区域内查找实例
     * @param box 包围盒
     * @returns 实例ID数组
     */
    getInstancesInBox(box: THREE.Box3): string[];
    /**
     * 在指定球体内查找实例
     * @param sphere 球体
     * @returns 实例ID数组
     */
    getInstancesInSphere(sphere: THREE.Sphere): string[];
    /**
     * 重建八叉树
     * 当有大量实例位置变化时，重建八叉树可以提高性能
     */
    rebuildOctree(): void;
    /**
     * 设置八叉树启用状态
     * @param enabled 是否启用
     */
    setOctreeEnabled(enabled: boolean): void;
    /**
     * 获取八叉树启用状态
     * @returns 是否启用八叉树
     */
    isOctreeEnabled(): boolean;
}
