/**
 * GPU蒙皮系统
 * 使用GPU加速蒙皮动画计算
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { AnimationComponent } from './AnimationComponent';
import { SkinnedMeshComponent } from './SkinnedMeshComponent';
/**
 * GPU蒙皮系统配置接口
 */
export interface GPUSkinningSystemOptions {
    /** 是否使用GPU蒙皮 */
    useGPUSkinning?: boolean;
    /** 是否使用动画实例化 */
    useAnimationInstancing?: boolean;
    /** 是否使用动画合并 */
    useAnimationMerging?: boolean;
    /** 是否使用动画LOD */
    useAnimationLOD?: boolean;
    /** 是否使用动画缓存 */
    useAnimationCache?: boolean;
    /** 是否使用动画压缩 */
    useAnimationCompression?: boolean;
    /** 是否使用调试可视化 */
    useDebugVisualization?: boolean;
    /** 最大骨骼数量 */
    maxBones?: number;
    /** 最大实例数量 */
    maxInstances?: number;
    /** 更新间隔（帧） */
    updateInterval?: number;
}
/**
 * 动画实例数据接口
 */
export interface AnimationInstanceData {
    /** 实例ID */
    id: string;
    /** 实体 */
    entity: Entity;
    /** 动画组件 */
    animationComponent: AnimationComponent;
    /** 蒙皮网格组件 */
    skinnedMeshComponent: SkinnedMeshComponent;
    /** 骨骼矩阵 */
    boneMatrices: Float32Array;
    /** 动画时间 */
    animationTime: number;
    /** 动画权重 */
    animationWeight: number;
    /** 动画速度 */
    animationSpeed: number;
    /** 是否循环 */
    loop: boolean;
    /** 是否暂停 */
    paused: boolean;
    /** 是否可见 */
    visible: boolean;
    /** 是否需要更新 */
    needsUpdate: boolean;
    /** 用户数据 */
    userData: any;
}
/**
 * 动画批处理组接口
 */
export interface AnimationBatchGroup {
    /** 批处理组ID */
    id: string;
    /** 骨骼数量 */
    boneCount: number;
    /** 实例数量 */
    instanceCount: number;
    /** 骨骼纹理 */
    boneTexture: THREE.DataTexture | null;
    /** 骨骼纹理大小 */
    boneTextureSize: number;
    /** 骨骼矩阵数组 */
    boneMatrices: Float32Array;
    /** 实例数据列表 */
    instances: AnimationInstanceData[];
    /** 实例到索引的映射 */
    instanceToIndex: Map<string, number>;
    /** 可用索引列表 */
    availableIndices: number[];
    /** 是否需要更新 */
    needsUpdate: boolean;
    /** 是否可见 */
    visible: boolean;
    /** 用户数据 */
    userData: any;
}
/**
 * GPU蒙皮系统类
 */
export declare class GPUSkinningSystem extends System {
    /** 系统类型 */
    private static readonly TYPE;
    /** 是否使用GPU蒙皮 */
    private useGPUSkinning;
    /** 是否使用动画实例化 */
    private useAnimationInstancing;
    /** 是否使用动画合并 */
    private useAnimationMerging;
    /** 是否使用动画LOD */
    private useAnimationLOD;
    /** 是否使用动画缓存 */
    private useAnimationCache;
    /** 是否使用动画压缩 */
    private useAnimationCompression;
    /** 是否使用调试可视化 */
    private useDebugVisualization;
    /** 最大骨骼数量 */
    private maxBones;
    /** 最大实例数量 */
    private maxInstances;
    /** 更新间隔（帧） */
    private updateInterval;
    /** 当前帧计数 */
    private frameCount;
    /** 动画批处理组列表 */
    private batchGroups;
    /** 实体到批处理组的映射 */
    private entityToBatchGroup;
    /** 实体到实例数据的映射 */
    private entityToInstanceData;
    /** 实例计数器 */
    private instanceCounter;
    /** 批处理组计数器 */
    private batchGroupCounter;
    /** 骨骼矩阵计算着色器 */
    private boneMatrixShader;
    /** 骨骼矩阵计算渲染目标 */
    private boneMatrixRenderTarget;
    /** 骨骼矩阵计算场景 */
    private boneMatrixScene;
    /** 骨骼矩阵计算相机 */
    private boneMatrixCamera;
    /** 骨骼矩阵计算网格 */
    private boneMatrixMesh;
    /** 调试可视化材质 */
    private debugMaterial;
    /** 调试可视化网格 */
    private debugMeshes;
    /** 是否支持GPU蒙皮 */
    private supportsGPUSkinning;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建GPU蒙皮系统
     * @param options GPU蒙皮系统配置
     */
    constructor(options?: GPUSkinningSystemOptions);
    /**
     * 检查GPU蒙皮支持
     */
    private checkGPUSkinningSupport;
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化GPU蒙皮
     */
    private initializeGPUSkinning;
    /**
     * 初始化调试可视化
     */
    private initializeDebugVisualization;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 获取相机
     * @returns 相机
     */
    private getCamera;
    /**
     * 获取场景
     * @returns 场景
     */
    private getScene;
    /**
     * 更新动画组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateAnimationComponents;
    /**
     * 更新蒙皮网格组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateSkinnedMeshComponents;
    /**
     * 使用CPU蒙皮更新
     * @param entity 实体
     * @param animationComponent 动画组件
     * @param skinnedMeshComponent 蒙皮网格组件
     */
    private updateWithCPUSkinning;
    /**
     * 添加到批处理组
     * @param entity 实体
     * @param animationComponent 动画组件
     * @param skinnedMeshComponent 蒙皮网格组件
     */
    private addToBatchGroup;
    /**
     * 查找批处理组
     * @param boneCount 骨骼数量
     * @returns 批处理组
     */
    private findBatchGroup;
    /**
     * 创建批处理组
     * @param boneCount 骨骼数量
     * @returns 批处理组
     */
    private createBatchGroup;
    /**
     * 更新批处理组
     * @param camera 相机
     */
    private updateBatchGroups;
    /**
     * 更新批处理组
     * @param batchGroup 批处理组
     * @param camera 相机
     */
    private updateBatchGroup;
    /**
     * 使用GPU蒙皮更新
     * @param batchGroup 批处理组
     */
    private updateWithGPUSkinning;
    /**
     * 使用CPU批处理更新
     * @param batchGroup 批处理组
     */
    private updateWithCPUBatching;
    /**
     * 更新调试可视化
     */
    private updateDebugVisualization;
    /**
     * 销毁系统
     */
    destroy(): void;
}
