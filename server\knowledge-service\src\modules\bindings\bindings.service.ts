import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DigitalHumanKnowledgeBinding } from '../../entities/digital-human-knowledge-binding.entity';

@Injectable()
export class BindingsService {
  constructor(
    @InjectRepository(DigitalHumanKnowledgeBinding)
    private readonly bindingRepository: Repository<DigitalHumanKnowledgeBinding>,
  ) {}

  async bindKnowledgeBase(digitalHumanId: string, knowledgeBaseId: string) {
    const binding = this.bindingRepository.create({
      digitalHumanId,
      knowledgeBaseId,
    });
    return this.bindingRepository.save(binding);
  }

  async getBindings(digitalHumanId: string) {
    return this.bindingRepository.find({
      where: { digitalHumanId },
      relations: ['knowledgeBase'],
    });
  }

  async unbindKnowledgeBase(digitalHumanId: string, knowledgeBaseId: string) {
    await this.bindingRepository.delete({
      digitalHumanId,
      knowledgeBaseId,
    });
    return { message: '解绑成功' };
  }
}
