FROM node:22-alpine AS builder

WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制渲染服务代码
COPY render-service/package*.json ./render-service/
WORKDIR /app/render-service
RUN npm install

# 复制渲染服务源代码
COPY render-service/ ./

RUN npm run build

FROM node:22-alpine

WORKDIR /app

COPY --from=builder /app/render-service/package*.json ./
COPY --from=builder /app/render-service/dist ./dist

RUN npm install --only=production

# 创建渲染输出目录
RUN mkdir -p /app/renders

EXPOSE 3004 4004

CMD ["node", "dist/main.js"]
