/**
 * 抓取者组件
 * 用于标记可以抓取其他对象的实体
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventCallback } from '../../utils/EventEmitter';
import { Hand } from './GrabbableComponent';
/**
 * 抓取者组件配置
 */
export interface GrabberComponentConfig {
    /** 最大抓取距离 */
    maxGrabDistance?: number;
    /** 是否启用 */
    enabled?: boolean;
    /** 抓取回调 */
    onGrab?: (grabber: Entity, grabbed: Entity, hand: Hand) => void;
    /** 释放回调 */
    onRelease?: (grabber: Entity, released: Entity, hand: Hand) => void;
}
/**
 * 抓取者组件
 */
export declare class GrabberComponent extends Component {
    /** 组件类型 */
    static readonly TYPE: string;
    /** 最大抓取距离 */
    private _maxGrabDistance;
    /** 左手抓取的实体 */
    private _leftHandGrabbed?;
    /** 右手抓取的实体 */
    private _rightHandGrabbed?;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     * @param entity 关联的实体
     * @param config 组件配置
     */
    constructor(entity: Entity, config?: GrabberComponentConfig);
    /**
     * 获取最大抓取距离
     */
    get maxGrabDistance(): number;
    /**
     * 设置最大抓取距离
     */
    set maxGrabDistance(value: number);
    /**
     * 获取左手抓取的实体
     */
    get leftHandGrabbed(): Entity | undefined;
    /**
     * 获取右手抓取的实体
     */
    get rightHandGrabbed(): Entity | undefined;
    /**
     * 获取指定手抓取的实体
     * @param hand 手
     * @returns 抓取的实体
     */
    getGrabbedEntity(hand: Hand): Entity | undefined;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    on(event: string, callback: EventCallback): this;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    off(event: string, callback?: EventCallback): this;
    /**
     * 抓取实体
     * @param entity 要抓取的实体
     * @param hand 使用的手
     * @returns 是否抓取成功
     */
    grab(entity: Entity, hand: Hand): boolean;
    /**
     * 释放实体
     * @param hand 使用的手
     * @returns 是否释放成功
     */
    release(hand: Hand): boolean;
}
