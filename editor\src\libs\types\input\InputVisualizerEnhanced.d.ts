/**
 * 增强型输入可视化工具
 * 提供更丰富的输入可视化功能，包括图表、热图等
 */
import { InputVisualizer } from './InputVisualizer';
/**
 * 增强型输入可视化器选项
 */
export interface InputVisualizerEnhancedOptions {
    /** 容器元素 */
    container?: HTMLElement;
    /** 是否显示设备状态 */
    showDevices?: boolean;
    /** 是否显示动作状态 */
    showActions?: boolean;
    /** 是否显示事件日志 */
    showEventLog?: boolean;
    /** 最大事件日志条数 */
    maxEventLogEntries?: number;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新间隔（毫秒） */
    updateInterval?: number;
    /** 是否显示性能监控 */
    showPerformance?: boolean;
    /** 是否显示图表 */
    showCharts?: boolean;
    /** 是否显示热图 */
    showHeatmap?: boolean;
    /** 是否显示调试工具 */
    showDebugTools?: boolean;
    /** 是否使用暗色主题 */
    darkTheme?: boolean;
    /** 是否可拖动 */
    draggable?: boolean;
    /** 是否可调整大小 */
    resizable?: boolean;
    /** 是否可折叠 */
    collapsible?: boolean;
    /** 是否初始折叠 */
    initiallyCollapsed?: boolean;
    /** 是否显示设备详情 */
    showDeviceDetails?: boolean;
    /** 是否显示输入历史 */
    showInputHistory?: boolean;
    /** 输入历史记录长度 */
    inputHistoryLength?: number;
}
/**
 * 增强型输入可视化器
 */
export declare class InputVisualizerEnhanced extends InputVisualizer {
    /** 性能监控元素 */
    private performanceElement;
    /** 图表元素 */
    private chartsElement;
    /** 热图元素 */
    private heatmapElement;
    /** 调试工具元素 */
    private debugToolsElement;
    /** 设备详情元素 */
    private deviceDetailsElement;
    /** 输入历史元素 */
    private inputHistoryElement;
    /** 是否显示性能监控 */
    private showPerformance;
    /** 是否显示图表 */
    private showCharts;
    /** 是否显示热图 */
    private showHeatmap;
    /** 是否显示调试工具 */
    private showDebugTools;
    /** 是否使用暗色主题 */
    private darkTheme;
    /** 是否可拖动 */
    private draggable;
    /** 是否可调整大小 */
    private resizable;
    /** 是否可折叠 */
    private collapsible;
    /** 是否已折叠 */
    private collapsed;
    /** 是否显示设备详情 */
    private showDeviceDetails;
    /** 是否显示输入历史 */
    private showInputHistory;
    /** 输入历史记录长度 */
    private _inputHistoryLength;
    /** 性能数据历史 */
    private _performanceHistory;
    /** 性能数据历史长度 */
    private readonly _performanceHistoryLength;
    /** 上一帧时间 */
    private _lastFrameTime;
    /** 输入历史记录 */
    private _inputHistory;
    /** 热图数据 */
    private _heatmapData;
    /** 当前选中的设备 */
    private _selectedDevice;
    /** 拖动状态 */
    private dragState;
    /** 调整大小状态 */
    private resizeState;
    /**
     * 创建增强型输入可视化器
     * @param options 选项
     */
    constructor(options?: InputVisualizerEnhancedOptions);
    /**
     * 创建增强UI元素
     */
    private createEnhancedUI;
    /**
     * 设置可拖动
     * @param container 容器元素
     */
    private setupDraggable;
    /**
     * 设置可调整大小
     * @param container 容器元素
     */
    private setupResizable;
    /**
     * 设置可折叠
     * @param container 容器元素
     */
    private setupCollapsible;
    /**
     * 折叠容器
     * @param container 容器元素
     */
    private collapseContainer;
    /**
     * 展开容器
     * @param container 容器元素
     */
    private expandContainer;
    /**
     * 创建性能监控元素
     * @param container 容器元素
     */
    private createPerformanceElement;
    /**
     * 创建图表元素
     * @param container 容器元素
     */
    private createChartsElement;
    /**
     * 创建热图元素
     * @param container 容器元素
     */
    private createHeatmapElement;
    /**
     * 创建调试工具元素
     * @param container 容器元素
     */
    private createDebugToolsElement;
    /**
     * 创建设备详情元素
     * @param container 容器元素
     */
    private createDeviceDetailsElement;
    /**
     * 创建输入历史元素
     * @param container 容器元素
     */
    private createInputHistoryElement;
    /**
     * 获取所有设备
     * @returns 设备列表
     */
    private getDevices;
    /**
     * 更新图表
     */
    private updateChart;
    /**
     * 更新热图
     */
    private updateHeatmap;
    /**
     * 开始记录
     */
    private startRecording;
    /**
     * 停止记录
     */
    private stopRecording;
    /**
     * 导出数据
     */
    private exportData;
    /**
     * 清除数据
     */
    private clearData;
    /**
     * 模拟输入
     */
    private simulateInput;
    /**
     * 更新设备详情
     * @param deviceName 设备名称
     */
    private updateDeviceDetails;
    /**
     * 更新输入历史
     */
    private updateInputHistory;
}
