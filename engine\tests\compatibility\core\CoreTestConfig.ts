/**
 * 核心模块兼容性测试配置
 */
import { TestConfig } from '../CompatibilityTestFramework';
import { engineInitializationTest } from './EngineInitializationTest';
import { entityManagementTest } from './EntityManagementTest';
import { componentManagementTest } from './ComponentManagementTest';
import { systemManagementTest } from './SystemManagementTest';
import { eventSystemTest } from './EventSystemTest';
import { timeManagementTest } from './TimeManagementTest';

/**
 * 核心模块兼容性测试配置
 */
export const coreTestConfig: TestConfig = {
  name: '核心模块兼容性测试',
  description: '测试核心模块的功能兼容性，包括引擎初始化、实体管理、组件管理、系统管理、事件系统和时间管理',
  originalModulePath: '../../../src/core',
  refactoredModulePath: '../../../../newsystem/engine/src/core',
  testCases: [
    engineInitializationTest,
    entityManagementTest,
    componentManagementTest,
    systemManagementTest,
    eventSystemTest,
    timeManagementTest
  ]
};
