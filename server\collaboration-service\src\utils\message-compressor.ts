/**
 * 消息压缩工具
 * 用于压缩和解压缩WebSocket消息
 * 支持多种压缩算法和自适应压缩策略
 */
import * as zlib from 'zlib';
import { promisify } from 'util';
import { Logger } from '@nestjs/common';

// 压缩方法
const deflateAsync = promisify(zlib.deflate);
const gzipAsync = promisify(zlib.gzip);
const brotliCompressAsync = promisify(zlib.brotliCompress);
const deflateRawAsync = promisify(zlib.deflateRaw);

// 解压缩方法
const inflateAsync = promisify(zlib.inflate);
const gunzipAsync = promisify(zlib.gunzip);
const brotliDecompressAsync = promisify(zlib.brotliDecompress);
const inflateRawAsync = promisify(zlib.inflateRaw);

/**
 * 压缩算法枚举
 */
export enum CompressionAlgorithm {
  NONE = 'none',
  DEFLATE = 'deflate',
  GZIP = 'gzip',
  BROTLI = 'brotli',
  DEFLATE_RAW = 'deflate_raw',
  ADAPTIVE = 'adaptive',
}

/**
 * 压缩选项接口
 */
export interface CompressionOptions {
  /**
   * 压缩算法
   */
  algorithm: CompressionAlgorithm;

  /**
   * 压缩级别 (0-9)，数字越大压缩率越高，但速度越慢
   */
  level?: number;

  /**
   * 最小压缩大小（字节），小于此大小的消息不会被压缩
   */
  minSize?: number;

  /**
   * 是否启用自适应压缩
   * 如果启用，将根据消息大小和类型自动选择最佳压缩算法
   */
  enableAdaptive?: boolean;

  /**
   * 是否启用压缩缓存
   * 如果启用，将缓存相同消息的压缩结果，提高性能
   */
  enableCache?: boolean;

  /**
   * 压缩缓存大小
   * 缓存的最大条目数
   */
  cacheSize?: number;

  /**
   * 是否收集压缩统计信息
   */
  collectStats?: boolean;

  /**
   * 消息类型字段名
   * 用于自适应压缩时识别消息类型
   */
  messageTypeField?: string;

  /**
   * 消息类型与压缩算法映射
   * 用于自适应压缩时根据消息类型选择最佳压缩算法
   */
  messageTypeAlgorithmMap?: Record<string, CompressionAlgorithm>;
}

/**
 * 压缩统计信息接口
 */
export interface CompressionStats {
  /**
   * 压缩次数
   */
  compressionCount: number;

  /**
   * 解压缩次数
   */
  decompressionCount: number;

  /**
   * 原始数据总大小（字节）
   */
  totalOriginalSize: number;

  /**
   * 压缩后数据总大小（字节）
   */
  totalCompressedSize: number;

  /**
   * 压缩总耗时（毫秒）
   */
  totalCompressionTime: number;

  /**
   * 解压缩总耗时（毫秒）
   */
  totalDecompressionTime: number;

  /**
   * 按算法统计的压缩次数
   */
  algorithmCounts: Record<CompressionAlgorithm, number>;

  /**
   * 按算法统计的平均压缩率
   */
  algorithmCompressionRatios: Record<CompressionAlgorithm, number>;

  /**
   * 缓存命中次数
   */
  cacheHits: number;

  /**
   * 缓存未命中次数
   */
  cacheMisses: number;
}

/**
 * 缓存条目接口
 */
interface CacheEntry {
  /**
   * 压缩后的数据
   */
  data: Buffer;

  /**
   * 使用的压缩算法
   */
  algorithm: CompressionAlgorithm;

  /**
   * 最后访问时间
   */
  lastAccessed: number;

  /**
   * 访问次数
   */
  accessCount: number;
}

/**
 * 消息压缩器类
 */
export class MessageCompressor {
  private options: Required<CompressionOptions>;
  private logger = new Logger(MessageCompressor.name);
  private compressionCache: Map<string, CacheEntry> = new Map();
  private stats: CompressionStats;

  /**
   * 创建消息压缩器
   * @param options 压缩选项
   */
  constructor(options?: Partial<CompressionOptions>) {
    this.options = {
      algorithm: CompressionAlgorithm.DEFLATE,
      level: 6,
      minSize: 100,
      enableAdaptive: true,
      enableCache: true,
      cacheSize: 1000,
      collectStats: true,
      messageTypeField: 'type',
      messageTypeAlgorithmMap: {},
      ...options,
    };

    // 初始化统计信息
    this.stats = {
      compressionCount: 0,
      decompressionCount: 0,
      totalOriginalSize: 0,
      totalCompressedSize: 0,
      totalCompressionTime: 0,
      totalDecompressionTime: 0,
      algorithmCounts: {
        [CompressionAlgorithm.NONE]: 0,
        [CompressionAlgorithm.DEFLATE]: 0,
        [CompressionAlgorithm.GZIP]: 0,
        [CompressionAlgorithm.BROTLI]: 0,
        [CompressionAlgorithm.DEFLATE_RAW]: 0,
        [CompressionAlgorithm.ADAPTIVE]: 0,
      },
      algorithmCompressionRatios: {
        [CompressionAlgorithm.NONE]: 1,
        [CompressionAlgorithm.DEFLATE]: 1,
        [CompressionAlgorithm.GZIP]: 1,
        [CompressionAlgorithm.BROTLI]: 1,
        [CompressionAlgorithm.DEFLATE_RAW]: 1,
        [CompressionAlgorithm.ADAPTIVE]: 1,
      },
      cacheHits: 0,
      cacheMisses: 0,
    };
  }

  /**
   * 压缩消息
   * @param message 要压缩的消息
   * @returns 压缩后的消息和元数据
   */
  async compress(message: any): Promise<{ data: Buffer; algorithm: CompressionAlgorithm }> {
    const startTime = Date.now();

    // 将消息转换为JSON字符串
    const jsonString = typeof message === 'string' ? message : JSON.stringify(message);
    const messageBuffer = Buffer.from(jsonString, 'utf-8');
    const originalSize = messageBuffer.length;

    // 如果消息小于最小压缩大小，则不压缩
    if (originalSize < this.options.minSize) {
      if (this.options.collectStats) {
        this.updateCompressionStats(
          CompressionAlgorithm.NONE,
          originalSize,
          originalSize,
          Date.now() - startTime
        );
      }

      return {
        data: messageBuffer,
        algorithm: CompressionAlgorithm.NONE,
      };
    }

    // 检查缓存
    if (this.options.enableCache) {
      const cacheKey = this.generateCacheKey(jsonString);
      const cachedResult = this.compressionCache.get(cacheKey);

      if (cachedResult) {
        // 更新缓存条目
        cachedResult.lastAccessed = Date.now();
        cachedResult.accessCount++;

        if (this.options.collectStats) {
          this.stats.cacheHits++;
          this.updateCompressionStats(
            cachedResult.algorithm,
            originalSize,
            cachedResult.data.length,
            0
          );
        }

        return {
          data: cachedResult.data,
          algorithm: cachedResult.algorithm,
        };
      } else if (this.options.collectStats) {
        this.stats.cacheMisses++;
      }
    }

    try {
      let algorithm = this.options.algorithm;
      let compressedData: Buffer;

      // 如果启用自适应压缩，选择最佳算法
      if (this.options.enableAdaptive && this.options.algorithm === CompressionAlgorithm.ADAPTIVE) {
        algorithm = this.selectBestAlgorithm(message);
      }

      // 根据算法选择压缩方法
      switch (algorithm) {
        case CompressionAlgorithm.DEFLATE:
          compressedData = await deflateAsync(messageBuffer, { level: this.options.level });
          break;

        case CompressionAlgorithm.GZIP:
          compressedData = await gzipAsync(messageBuffer, { level: this.options.level });
          break;

        case CompressionAlgorithm.BROTLI:
          compressedData = await brotliCompressAsync(messageBuffer, {
            params: {
              [zlib.constants.BROTLI_PARAM_QUALITY]: this.options.level,
            },
          });
          break;

        case CompressionAlgorithm.DEFLATE_RAW:
          compressedData = await deflateRawAsync(messageBuffer, { level: this.options.level });
          break;

        case CompressionAlgorithm.NONE:
        default:
          if (this.options.collectStats) {
            this.updateCompressionStats(
              CompressionAlgorithm.NONE,
              originalSize,
              originalSize,
              Date.now() - startTime
            );
          }

          return {
            data: messageBuffer,
            algorithm: CompressionAlgorithm.NONE,
          };
      }

      // 如果压缩后的数据比原始数据大，则不使用压缩
      if (compressedData.length >= originalSize) {
        if (this.options.collectStats) {
          this.updateCompressionStats(
            CompressionAlgorithm.NONE,
            originalSize,
            originalSize,
            Date.now() - startTime
          );
        }

        return {
          data: messageBuffer,
          algorithm: CompressionAlgorithm.NONE,
        };
      }

      const compressionTime = Date.now() - startTime;

      // 更新统计信息
      if (this.options.collectStats) {
        this.updateCompressionStats(
          algorithm,
          originalSize,
          compressedData.length,
          compressionTime
        );
      }

      // 添加到缓存
      if (this.options.enableCache) {
        this.addToCache(jsonString, compressedData, algorithm);
      }

      return {
        data: compressedData,
        algorithm,
      };
    } catch (error) {
      this.logger.error(`压缩消息时出错: ${error.message}`, error.stack);

      if (this.options.collectStats) {
        this.updateCompressionStats(
          CompressionAlgorithm.NONE,
          originalSize,
          originalSize,
          Date.now() - startTime
        );
      }

      return {
        data: messageBuffer,
        algorithm: CompressionAlgorithm.NONE,
      };
    }
  }

  /**
   * 解压缩消息
   * @param data 压缩后的数据
   * @param algorithm 使用的压缩算法
   * @returns 解压缩后的消息
   */
  async decompress(data: Buffer, algorithm: CompressionAlgorithm): Promise<any> {
    const startTime = Date.now();

    // 如果没有压缩，直接返回
    if (algorithm === CompressionAlgorithm.NONE) {
      const jsonString = data.toString('utf-8');

      if (this.options.collectStats) {
        this.stats.decompressionCount++;
        this.stats.totalDecompressionTime += Date.now() - startTime;
      }

      return JSON.parse(jsonString);
    }

    try {
      let decompressedData: Buffer;

      // 根据算法选择解压缩方法
      switch (algorithm) {
        case CompressionAlgorithm.DEFLATE:
          decompressedData = await inflateAsync(data);
          break;

        case CompressionAlgorithm.GZIP:
          decompressedData = await gunzipAsync(data);
          break;

        case CompressionAlgorithm.BROTLI:
          decompressedData = await brotliDecompressAsync(data);
          break;

        case CompressionAlgorithm.DEFLATE_RAW:
          decompressedData = await inflateRawAsync(data);
          break;

        default:
          throw new Error(`不支持的压缩算法: ${algorithm}`);
      }

      // 更新统计信息
      if (this.options.collectStats) {
        this.stats.decompressionCount++;
        this.stats.totalDecompressionTime += Date.now() - startTime;
      }

      // 将解压缩后的数据转换为JSON对象
      const jsonString = decompressedData.toString('utf-8');
      return JSON.parse(jsonString);
    } catch (error) {
      this.logger.error(`解压缩消息时出错: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 生成缓存键
   * @param jsonString JSON字符串
   * @returns 缓存键
   */
  private generateCacheKey(jsonString: string): string {
    // 简单的哈希函数，实际应用中可以使用更高效的哈希算法
    let hash = 0;
    for (let i = 0; i < jsonString.length; i++) {
      const char = jsonString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(16);
  }

  /**
   * 添加到缓存
   * @param jsonString JSON字符串
   * @param compressedData 压缩后的数据
   * @param algorithm 使用的压缩算法
   */
  private addToCache(jsonString: string, compressedData: Buffer, algorithm: CompressionAlgorithm): void {
    const cacheKey = this.generateCacheKey(jsonString);

    // 添加到缓存
    this.compressionCache.set(cacheKey, {
      data: compressedData,
      algorithm,
      lastAccessed: Date.now(),
      accessCount: 1,
    });

    // 如果缓存超过大小限制，清理最不常用的条目
    if (this.compressionCache.size > this.options.cacheSize) {
      this.cleanCache();
    }
  }

  /**
   * 清理缓存
   * 移除最不常用的条目
   */
  private cleanCache(): void {
    // 将缓存条目转换为数组
    const entries = Array.from(this.compressionCache.entries());

    // 按访问次数和最后访问时间排序
    entries.sort((a, b) => {
      // 首先按访问次数排序
      const countDiff = a[1].accessCount - b[1].accessCount;
      if (countDiff !== 0) {
        return countDiff;
      }

      // 如果访问次数相同，按最后访问时间排序
      return a[1].lastAccessed - b[1].lastAccessed;
    });

    // 移除前25%的条目
    const removeCount = Math.ceil(this.options.cacheSize * 0.25);
    for (let i = 0; i < removeCount && i < entries.length; i++) {
      this.compressionCache.delete(entries[i][0]);
    }
  }

  /**
   * 选择最佳压缩算法
   * @param message 消息对象
   * @returns 最佳压缩算法
   */
  private selectBestAlgorithm(message: any): CompressionAlgorithm {
    // 如果消息有类型字段，检查是否有预设的算法映射
    if (typeof message === 'object' && message !== null && this.options.messageTypeField in message) {
      const messageType = message[this.options.messageTypeField];

      // 如果有预设的算法映射，使用它
      if (messageType in this.options.messageTypeAlgorithmMap) {
        return this.options.messageTypeAlgorithmMap[messageType];
      }
    }

    // 根据消息大小选择算法
    const jsonString = typeof message === 'string' ? message : JSON.stringify(message);
    const size = jsonString.length;

    if (size < 1024) {
      // 小消息使用DEFLATE_RAW，速度快
      return CompressionAlgorithm.DEFLATE_RAW;
    } else if (size < 10240) {
      // 中等消息使用DEFLATE，平衡速度和压缩率
      return CompressionAlgorithm.DEFLATE;
    } else {
      // 大消息使用BROTLI，压缩率高
      return CompressionAlgorithm.BROTLI;
    }
  }

  /**
   * 更新压缩统计信息
   * @param algorithm 使用的压缩算法
   * @param originalSize 原始大小
   * @param compressedSize 压缩后大小
   * @param compressionTime 压缩耗时
   */
  private updateCompressionStats(
    algorithm: CompressionAlgorithm,
    originalSize: number,
    compressedSize: number,
    compressionTime: number
  ): void {
    this.stats.compressionCount++;
    this.stats.totalOriginalSize += originalSize;
    this.stats.totalCompressedSize += compressedSize;
    this.stats.totalCompressionTime += compressionTime;
    this.stats.algorithmCounts[algorithm]++;

    // 更新压缩率
    const ratio = compressedSize / originalSize;
    const currentRatio = this.stats.algorithmCompressionRatios[algorithm];
    const count = this.stats.algorithmCounts[algorithm];

    // 计算移动平均值
    this.stats.algorithmCompressionRatios[algorithm] = (currentRatio * (count - 1) + ratio) / count;
  }

  /**
   * 获取压缩统计信息
   * @returns 压缩统计信息
   */
  public getStats(): CompressionStats {
    return { ...this.stats };
  }

  /**
   * 重置压缩统计信息
   */
  public resetStats(): void {
    this.stats = {
      compressionCount: 0,
      decompressionCount: 0,
      totalOriginalSize: 0,
      totalCompressedSize: 0,
      totalCompressionTime: 0,
      totalDecompressionTime: 0,
      algorithmCounts: {
        [CompressionAlgorithm.NONE]: 0,
        [CompressionAlgorithm.DEFLATE]: 0,
        [CompressionAlgorithm.GZIP]: 0,
        [CompressionAlgorithm.BROTLI]: 0,
        [CompressionAlgorithm.DEFLATE_RAW]: 0,
        [CompressionAlgorithm.ADAPTIVE]: 0,
      },
      algorithmCompressionRatios: {
        [CompressionAlgorithm.NONE]: 1,
        [CompressionAlgorithm.DEFLATE]: 1,
        [CompressionAlgorithm.GZIP]: 1,
        [CompressionAlgorithm.BROTLI]: 1,
        [CompressionAlgorithm.DEFLATE_RAW]: 1,
        [CompressionAlgorithm.ADAPTIVE]: 1,
      },
      cacheHits: 0,
      cacheMisses: 0,
    };
  }

  /**
   * 清空压缩缓存
   */
  public clearCache(): void {
    this.compressionCache.clear();
  }

  /**
   * 获取平均压缩率
   * @returns 平均压缩率
   */
  public getAverageCompressionRatio(): number {
    if (this.stats.totalOriginalSize === 0) {
      return 1;
    }

    return this.stats.totalCompressedSize / this.stats.totalOriginalSize;
  }

  /**
   * 获取缓存命中率
   * @returns 缓存命中率
   */
  public getCacheHitRatio(): number {
    const total = this.stats.cacheHits + this.stats.cacheMisses;
    if (total === 0) {
      return 0;
    }

    return this.stats.cacheHits / total;
  }
}
