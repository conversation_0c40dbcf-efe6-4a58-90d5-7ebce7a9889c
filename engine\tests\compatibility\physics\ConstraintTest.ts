/**
 * 约束兼容性测试
 */
import { TestCase, TestResult } from '../CompatibilityTestFramework';

/**
 * 约束兼容性测试
 */
export const constraintTest: TestCase = {
  name: '约束兼容性测试',
  description: '测试物理约束功能的兼容性',
  testFunction: async (original: any, refactored: any): Promise<TestResult> => {
    try {
      // 创建原有项目物理系统实例
      const originalPhysicsSystem = new original.PhysicsSystem();
      originalPhysicsSystem.initialize();
      
      // 创建重构后项目物理系统实例
      const refactoredPhysicsSystem = new refactored.PhysicsSystem();
      refactoredPhysicsSystem.initialize();
      
      // 创建原有项目实体
      const originalEntity1 = new original.Entity('Entity1');
      const originalEntity2 = new original.Entity('Entity2');
      
      // 创建重构后项目实体
      const refactoredEntity1 = new refactored.Entity('Entity1');
      const refactoredEntity2 = new refactored.Entity('Entity2');
      
      // 创建原有项目刚体组件
      const originalBodyComponent1 = new original.PhysicsBodyComponent(originalEntity1, {
        mass: 1,
        type: 'dynamic'
      });
      
      const originalBodyComponent2 = new original.PhysicsBodyComponent(originalEntity2, {
        mass: 1,
        type: 'dynamic'
      });
      
      // 创建重构后项目刚体组件
      const refactoredBodyComponent1 = new refactored.PhysicsBodyComponent(refactoredEntity1, {
        mass: 1,
        type: 'dynamic'
      });
      
      const refactoredBodyComponent2 = new refactored.PhysicsBodyComponent(refactoredEntity2, {
        mass: 1,
        type: 'dynamic'
      });
      
      // 创建原有项目碰撞体组件
      const originalColliderComponent1 = new original.PhysicsColliderComponent(originalEntity1, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      });
      
      const originalColliderComponent2 = new original.PhysicsColliderComponent(originalEntity2, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      });
      
      // 创建重构后项目碰撞体组件
      const refactoredColliderComponent1 = new refactored.PhysicsColliderComponent(refactoredEntity1, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      });
      
      const refactoredColliderComponent2 = new refactored.PhysicsColliderComponent(refactoredEntity2, {
        shape: 'box',
        size: { x: 1, y: 1, z: 1 }
      });
      
      // 添加组件到实体
      originalEntity1.addComponent(originalBodyComponent1);
      originalEntity1.addComponent(originalColliderComponent1);
      originalEntity2.addComponent(originalBodyComponent2);
      originalEntity2.addComponent(originalColliderComponent2);
      
      refactoredEntity1.addComponent(refactoredBodyComponent1);
      refactoredEntity1.addComponent(refactoredColliderComponent1);
      refactoredEntity2.addComponent(refactoredBodyComponent2);
      refactoredEntity2.addComponent(refactoredColliderComponent2);
      
      // 注册组件到物理系统
      originalPhysicsSystem.registerPhysicsBodyComponent(originalEntity1, originalBodyComponent1);
      originalPhysicsSystem.registerPhysicsColliderComponent(originalEntity1, originalColliderComponent1);
      originalPhysicsSystem.registerPhysicsBodyComponent(originalEntity2, originalBodyComponent2);
      originalPhysicsSystem.registerPhysicsColliderComponent(originalEntity2, originalColliderComponent2);
      
      refactoredPhysicsSystem.registerPhysicsBodyComponent(refactoredEntity1, refactoredBodyComponent1);
      refactoredPhysicsSystem.registerPhysicsColliderComponent(refactoredEntity1, refactoredColliderComponent1);
      refactoredPhysicsSystem.registerPhysicsBodyComponent(refactoredEntity2, refactoredBodyComponent2);
      refactoredPhysicsSystem.registerPhysicsColliderComponent(refactoredEntity2, refactoredColliderComponent2);
      
      // 设置实体位置
      originalBodyComponent1.setPosition({ x: 0, y: 0, z: 0 });
      originalBodyComponent2.setPosition({ x: 0, y: 2, z: 0 });
      
      refactoredBodyComponent1.setPosition({ x: 0, y: 0, z: 0 });
      refactoredBodyComponent2.setPosition({ x: 0, y: 2, z: 0 });
      
      // 更新物理系统
      originalPhysicsSystem.update(0.016);
      refactoredPhysicsSystem.update(0.016);
      
      // 创建约束
      const originalConstraint = originalPhysicsSystem.createConstraint({
        type: 'pointToPoint',
        bodyA: originalBodyComponent1,
        bodyB: originalBodyComponent2,
        pivotA: { x: 0, y: 0.5, z: 0 },
        pivotB: { x: 0, y: -0.5, z: 0 }
      });
      
      const refactoredConstraint = refactoredPhysicsSystem.createConstraint({
        type: 'pointToPoint',
        bodyA: refactoredBodyComponent1,
        bodyB: refactoredBodyComponent2,
        pivotA: { x: 0, y: 0.5, z: 0 },
        pivotB: { x: 0, y: -0.5, z: 0 }
      });
      
      // 检查约束是否创建成功
      if (!originalConstraint || !refactoredConstraint) {
        return {
          name: '约束兼容性测试',
          passed: false,
          errorMessage: '约束创建失败',
          details: {
            originalConstraint,
            refactoredConstraint
          }
        };
      }
      
      // 更新物理系统多次，让约束生效
      for (let i = 0; i < 100; i++) {
        originalPhysicsSystem.update(0.016);
        refactoredPhysicsSystem.update(0.016);
      }
      
      // 获取实体位置
      const originalPosition1 = originalBodyComponent1.getPosition();
      const originalPosition2 = originalBodyComponent2.getPosition();
      const refactoredPosition1 = refactoredBodyComponent1.getPosition();
      const refactoredPosition2 = refactoredBodyComponent2.getPosition();
      
      // 计算实体之间的距离
      const originalDistance = Math.sqrt(
        Math.pow(originalPosition1.x - originalPosition2.x, 2) +
        Math.pow(originalPosition1.y - originalPosition2.y, 2) +
        Math.pow(originalPosition1.z - originalPosition2.z, 2)
      );
      
      const refactoredDistance = Math.sqrt(
        Math.pow(refactoredPosition1.x - refactoredPosition2.x, 2) +
        Math.pow(refactoredPosition1.y - refactoredPosition2.y, 2) +
        Math.pow(refactoredPosition1.z - refactoredPosition2.z, 2)
      );
      
      // 注意：由于物理引擎的实现差异，距离可能不完全相同，所以我们允许一定的误差
      if (Math.abs(originalDistance - refactoredDistance) > 0.5) {
        return {
          name: '约束兼容性测试',
          passed: false,
          errorMessage: `约束后实体之间的距离相差过大: 原有项目=${originalDistance}, 重构后项目=${refactoredDistance}`,
          details: {
            originalDistance,
            refactoredDistance,
            originalPosition1,
            originalPosition2,
            refactoredPosition1,
            refactoredPosition2
          }
        };
      }
      
      // 销毁约束
      originalPhysicsSystem.removeConstraint(originalConstraint);
      refactoredPhysicsSystem.removeConstraint(refactoredConstraint);
      
      // 销毁物理系统
      originalPhysicsSystem.destroy();
      refactoredPhysicsSystem.destroy();
      
      return {
        name: '约束兼容性测试',
        passed: true,
        details: {
          originalDistance,
          refactoredDistance
        }
      };
    } catch (error) {
      return {
        name: '约束兼容性测试',
        passed: false,
        errorMessage: `测试执行失败: ${error}`
      };
    }
  }
};
