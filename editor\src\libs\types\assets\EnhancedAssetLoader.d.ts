import { AssetType } from './ResourceManager';
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 加载器选项
 */
export interface LoaderOptions {
    /** 基础路径 */
    basePath?: string;
    /** 跨域设置 */
    crossOrigin?: string;
    /** DRACO解码器路径 */
    dracoDecoderPath?: string;
    /** KTX2转码器路径 */
    ktx2TranscoderPath?: string;
    /** Basis转码器路径 */
    basisTranscoderPath?: string;
    /** 是否启用DRACO压缩 */
    enableDraco?: boolean;
    /** 是否启用KTX2压缩 */
    enableKTX2?: boolean;
    /** 是否启用Basis压缩 */
    enableBasis?: boolean;
    /** 最大纹理尺寸 */
    maxTextureSize?: number;
    /** 是否启用纹理压缩 */
    enableTextureCompression?: boolean;
    /** 是否启用HDR */
    enableHDR?: boolean;
}
/**
 * 增强资产加载器
 */
export declare class EnhancedAssetLoader extends EventEmitter {
    /** Three.js加载管理器 */
    private manager;
    /** 纹理加载器 */
    private textureLoader;
    /** GLTF加载器 */
    private gltfLoader;
    /** FBX加载器 */
    private fbxLoader;
    /** OBJ加载器 */
    private objLoader;
    /** 立方体纹理加载器 */
    private cubeTextureLoader;
    /** 音频加载器 */
    private audioLoader;
    /** 字体加载器 */
    private fontLoader;
    /** 文件加载器 */
    private fileLoader;
    /** TGA加载器 */
    private tgaLoader;
    /** DRACO加载器 */
    private dracoLoader;
    /** KTX2加载器 */
    private ktx2Loader;
    /** Basis加载器 */
    private basisLoader;
    /** 基础路径 */
    private basePath;
    /** 加载器选项 */
    private options;
    /**
     * 创建增强资产加载器实例
     * @param options 加载器选项
     */
    constructor(options?: LoaderOptions);
    /**
     * 初始化特殊加载器（DRACO, KTX2, Basis等）
     */
    private initSpecialLoaders;
    /**
     * 加载资产
     * @param type 资产类型
     * @param url 资产URL
     * @returns Promise，解析为加载的资产数据
     */
    load(type: AssetType, url: string): Promise<any>;
    /**
     * 解析URL
     * @param url 原始URL
     * @returns 解析后的完整URL
     */
    private resolveUrl;
    /**
     * 加载纹理
     * @param url 纹理URL
     * @returns Promise，解析为加载的纹理
     */
    private loadTexture;
    /**
     * 加载标准纹理
     * @param url 纹理URL
     * @returns Promise，解析为加载的纹理
     */
    private loadStandardTexture;
    /**
     * 加载TGA纹理
     * @param url 纹理URL
     * @returns Promise，解析为加载的纹理
     */
    private loadTGATexture;
    /**
     * 加载KTX2纹理
     * @param url 纹理URL
     * @returns Promise，解析为加载的纹理
     */
    private loadKTX2Texture;
    /**
     * 加载Basis纹理
     * @param url 纹理URL
     * @returns Promise，解析为加载的纹理
     */
    private loadBasisTexture;
    /**
     * 调整纹理大小
     * @param texture 纹理
     * @param maxSize 最大尺寸
     */
    private resizeTexture;
    /**
     * 加载模型
     * @param url 模型URL
     * @returns Promise，解析为加载的模型
     */
    private loadModel;
    /**
     * 加载GLTF模型
     * @param url GLTF模型URL
     * @returns Promise，解析为加载的GLTF模型
     */
    private loadGLTF;
    /**
     * 加载FBX模型
     * @param url FBX模型URL
     * @returns Promise，解析为加载的FBX模型
     */
    private loadFBX;
    /**
     * 加载OBJ模型
     * @param url OBJ模型URL
     * @returns Promise，解析为加载的OBJ模型
     */
    private loadOBJ;
    /**
     * 加载音频
     * @param url 音频URL
     * @returns Promise，解析为加载的音频数据
     */
    private loadAudio;
    /**
     * 加载字体
     * @param url 字体URL
     * @returns Promise，解析为加载的字体
     */
    private loadFont;
    /**
     * 加载JSON
     * @param url JSON URL
     * @returns Promise，解析为加载的JSON数据
     */
    private loadJSON;
    /**
     * 加载文本
     * @param url 文本URL
     * @returns Promise，解析为加载的文本
     */
    private loadText;
    /**
     * 加载二进制数据
     * @param url 二进制数据URL
     * @returns Promise，解析为加载的二进制数据
     */
    private loadBinary;
    /**
     * 加载立方体纹理
     * @param urls 立方体纹理URL数组或基础URL
     * @returns Promise，解析为加载的立方体纹理
     */
    private loadCubeTexture;
    /**
     * 设置加载基础路径
     * @param path 基础路径
     */
    setPath(path: string): void;
    /**
     * 设置跨域
     * @param crossOrigin 跨域设置
     */
    setCrossOrigin(crossOrigin: string): void;
    /**
     * 销毁加载器
     */
    dispose(): void;
}
