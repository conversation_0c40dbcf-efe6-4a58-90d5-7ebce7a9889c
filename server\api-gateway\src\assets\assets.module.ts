/**
 * 资产模块
 */
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AssetsController } from './assets.controller';
import { AssetsService } from './assets.service';
import { ModelsController } from './models.controller';
import { TexturesController } from './textures.controller';
import { AudioController } from './audio.controller';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'ASSET_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('ASSET_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('ASSET_SERVICE_PORT', 3003),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [AssetsController, ModelsController, TexturesController, AudioController],
  providers: [AssetsService],
  exports: [AssetsService],
})
export class AssetsModule {}
