import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export enum HealthCheckType {
  HTTP = 'http',
  TCP = 'tcp',
  SCRIPT = 'script',
  CUSTOM = 'custom',
}

export enum HealthCheckStatus {
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  DEGRADED = 'degraded',
  UNKNOWN = 'unknown',
}

@Entity('health_checks')
export class HealthCheckEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column()
  @Index()
  serviceId: string;

  @Column()
  @Index()
  serviceType: string;

  @Column()
  @Index()
  instanceId: string;

  @Column()
  @Index()
  hostname: string;

  @Column({
    type: 'enum',
    enum: HealthCheckType,
    default: HealthCheckType.HTTP,
  })
  type: HealthCheckType;

  @Column()
  target: string;

  @Column('int', { default: 60 })
  interval: number;

  @Column('int', { default: 5 })
  timeout: number;

  @Column('int', { default: 2 })
  healthyThreshold: number;

  @Column('int', { default: 3 })
  unhealthyThreshold: number;

  @Column('json', { nullable: true })
  options: Record<string, any>;

  @Column('boolean', { default: true })
  enabled: boolean;

  @Column({
    type: 'enum',
    enum: HealthCheckStatus,
    default: HealthCheckStatus.UNKNOWN,
  })
  @Index()
  status: HealthCheckStatus;

  @Column('int', { default: 0 })
  successCount: number;

  @Column('int', { default: 0 })
  failureCount: number;

  @Column('timestamp', { nullable: true })
  lastCheckTime: Date;

  @Column('text', { nullable: true })
  lastCheckResult: string;

  @Column('boolean', { default: false })
  autoRecoveryEnabled: boolean;

  @Column('json', { nullable: true })
  autoRecoveryConfig: Record<string, any>;

  @Column('int', { default: 0 })
  autoRecoveryAttempts: number;

  @Column('timestamp', { nullable: true })
  lastAutoRecoveryTime: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
