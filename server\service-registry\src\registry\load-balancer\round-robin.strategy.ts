/**
 * 轮询负载均衡策略
 */
import { Injectable } from '@nestjs/common';
import { BaseLoadBalancerStrategy } from './base-load-balancer.strategy';
import { LoadBalancerContext, LoadBalancerAlgorithm } from './load-balancer.interface';
import { ServiceInstanceEntity } from '../entities/service-instance.entity';

@Injectable()
export class RoundRobinLoadBalancerStrategy extends BaseLoadBalancerStrategy {
  private counters = new Map<string, number>();
  
  constructor() {
    super(LoadBalancerAlgorithm.ROUND_ROBIN);
    this.config.algorithm = LoadBalancerAlgorithm.ROUND_ROBIN;
  }
  
  /**
   * 轮询选择服务实例
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  protected async doSelect(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): Promise<ServiceInstanceEntity | null> {
    if (!instances || instances.length === 0) {
      return null;
    }
    
    // 获取服务的计数器
    const counterKey = context.serviceName;
    let counter = this.counters.get(counterKey) || 0;
    
    // 选择下一个实例
    const index = counter % instances.length;
    
    // 更新计数器
    counter = (counter + 1) % Number.MAX_SAFE_INTEGER;
    this.counters.set(counterKey, counter);
    
    return instances[index];
  }
  
  /**
   * 重置策略状态
   */
  override reset(): void {
    this.counters.clear();
  }
}
