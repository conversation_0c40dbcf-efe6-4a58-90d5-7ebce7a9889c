/**
 * 渲染性能测试
 * 测试渲染系统在不同场景复杂度下的性能
 */
import * as THREE from 'three';
import { PerformanceTest, PerformanceTestConfig } from './PerformanceTestFramework';
import { Entity } from '../../src/core/Entity';
import { Light, LightType } from '../../src/rendering/Light';
import { MeshComponent } from '../../src/rendering/MeshComponent';
import { MaterialComponent } from '../../src/rendering/MaterialComponent';
import { LODSystem } from '../../src/rendering/optimization/LODSystem';
import { FrustumCullingSystem } from '../../src/rendering/optimization/FrustumCullingSystem';
import { InstancedRenderingSystem } from '../../src/rendering/optimization/InstancedRenderingSystem';
import { BatchingSystem } from '../../src/rendering/optimization/BatchingSystem';
import { PostProcessingSystem } from '../../src/rendering/postprocessing/PostProcessingSystem';
import { BloomEffect } from '../../src/rendering/postprocessing/effects/BloomEffect';
import { SSAOEffect } from '../../src/rendering/postprocessing/effects/SSAOEffect';
import { Debug } from '../../src/utils/Debug';

/**
 * 渲染性能测试配置
 */
export interface RenderingPerformanceTestConfig extends PerformanceTestConfig {
  /** 场景复杂度 */
  complexity?: 'low' | 'medium' | 'high' | 'extreme';
  /** 是否启用后处理 */
  enablePostProcessing?: boolean;
  /** 是否启用优化 */
  enableOptimizations?: boolean;
  /** 是否启用阴影 */
  enableShadows?: boolean;
  /** 是否启用光照 */
  enableLights?: boolean;
  /** 是否使用PBR材质 */
  usePBRMaterials?: boolean;
}

/**
 * 渲染性能测试
 */
export class RenderingPerformanceTest extends PerformanceTest {
  /** 测试配置 */
  protected config: RenderingPerformanceTestConfig;
  /** 实体列表 */
  private entities: Entity[] = [];
  /** LOD系统 */
  private lodSystem?: LODSystem;
  /** 视锥体剔除系统 */
  private frustumCullingSystem?: FrustumCullingSystem;
  /** 实例化渲染系统 */
  private instancedRenderingSystem?: InstancedRenderingSystem;
  /** 批处理系统 */
  private batchingSystem?: BatchingSystem;
  /** 后处理系统 */
  private postProcessingSystem?: PostProcessingSystem;
  /** 渲染统计信息 */
  private renderStats: {
    /** 绘制调用次数 */
    drawCalls: number[];
    /** 三角形数量 */
    triangles: number[];
    /** 顶点数量 */
    vertices: number[];
    /** 纹理内存（MB） */
    textureMemory: number[];
    /** 几何体内存（MB） */
    geometryMemory: number[];
  };

  /**
   * 创建渲染性能测试
   * @param config 测试配置
   */
  constructor(config: RenderingPerformanceTestConfig) {
    super({
      ...config,
      name: config.name || '渲染性能测试',
    });

    this.config = {
      ...config,
      complexity: config.complexity || 'medium',
      enablePostProcessing: config.enablePostProcessing !== undefined ? config.enablePostProcessing : false,
      enableOptimizations: config.enableOptimizations !== undefined ? config.enableOptimizations : true,
      enableShadows: config.enableShadows !== undefined ? config.enableShadows : true,
      enableLights: config.enableLights !== undefined ? config.enableLights : true,
      usePBRMaterials: config.usePBRMaterials !== undefined ? config.usePBRMaterials : false,
    };

    // 初始化渲染统计信息
    this.renderStats = {
      drawCalls: [],
      triangles: [],
      vertices: [],
      textureMemory: [],
      geometryMemory: [],
    };

    // 扩展性能数据
    this.performanceData.drawCallsHistory = [];
    this.performanceData.trianglesHistory = [];
    this.performanceData.verticesHistory = [];
    this.performanceData.textureMemoryHistory = [];
    this.performanceData.geometryMemoryHistory = [];
  }

  /**
   * 初始化测试
   */
  protected initialize(): void {
    Debug.log('渲染性能测试', `初始化测试: ${this.config.name}, 复杂度: ${this.config.complexity}`);

    // 设置相机位置
    this.camera.getThreeCamera().position.set(0, 10, 20);
    this.camera.getThreeCamera().lookAt(0, 0, 0);

    // 添加光照
    if (this.config.enableLights) {
      this.setupLights();
    }

    // 创建场景
    this.createScene();

    // 设置优化系统
    if (this.config.enableOptimizations) {
      this.setupOptimizations();
    }

    // 设置后处理
    if (this.config.enablePostProcessing) {
      this.setupPostProcessing();
    }

    Debug.log('渲染性能测试', '测试初始化完成');
  }

  /**
   * 设置光照
   */
  private setupLights(): void {
    // 添加环境光
    const ambientLight = new Light(LightType.AMBIENT, { color: 0x404040, intensity: 0.5 });
    const ambientLightEntity = new Entity();
    ambientLightEntity.addComponent(ambientLight);
    this.scene.addEntity(ambientLightEntity);

    // 添加方向光
    const directionalLight = new Light(LightType.DIRECTIONAL, { 
      color: 0xffffff, 
      intensity: 1.0,
      castShadow: this.config.enableShadows,
      shadowMapSize: 2048,
    });
    const directionalLightEntity = new Entity();
    directionalLightEntity.addComponent(directionalLight);
    directionalLightEntity.transform.setPosition(5, 10, 5);
    directionalLightEntity.transform.lookAt(0, 0, 0);
    this.scene.addEntity(directionalLightEntity);

    // 添加点光源
    const pointLight = new Light(LightType.POINT, { 
      color: 0xff8800, 
      intensity: 1.0,
      distance: 20,
      decay: 2,
      castShadow: this.config.enableShadows,
    });
    const pointLightEntity = new Entity();
    pointLightEntity.addComponent(pointLight);
    pointLightEntity.transform.setPosition(-5, 5, 5);
    this.scene.addEntity(pointLightEntity);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 根据复杂度创建不同数量的对象
    let objectCount = 0;
    switch (this.config.complexity) {
      case 'low':
        objectCount = 100;
        break;
      case 'medium':
        objectCount = 1000;
        break;
      case 'high':
        objectCount = 5000;
        break;
      case 'extreme':
        objectCount = 10000;
        break;
    }

    Debug.log('渲染性能测试', `创建 ${objectCount} 个对象`);

    // 创建地面
    this.createGround();

    // 创建多个对象
    this.createObjects(objectCount);
  }

  /**
   * 创建地面
   */
  private createGround(): void {
    const groundEntity = new Entity();
    const groundMesh = new MeshComponent();
    groundMesh.setGeometry(new THREE.PlaneGeometry(100, 100, 1, 1));
    
    const groundMaterial = new MaterialComponent();
    if (this.config.usePBRMaterials) {
      groundMaterial.createPBRMaterial({
        color: 0x999999,
        roughness: 0.8,
        metalness: 0.2,
      });
    } else {
      groundMaterial.createStandardMaterial({
        color: 0x999999,
        roughness: 0.8,
        metalness: 0.2,
      });
    }
    
    groundEntity.addComponent(groundMesh);
    groundEntity.addComponent(groundMaterial);
    groundEntity.transform.setRotation(-Math.PI / 2, 0, 0);
    groundEntity.transform.setPosition(0, 0, 0);
    
    this.scene.addEntity(groundEntity);
    this.entities.push(groundEntity);
  }

  /**
   * 创建多个对象
   * @param count 对象数量
   */
  private createObjects(count: number): void {
    // 创建几何体
    const geometries = [
      new THREE.BoxGeometry(1, 1, 1),
      new THREE.SphereGeometry(0.5, 16, 16),
      new THREE.ConeGeometry(0.5, 1, 16),
      new THREE.TorusGeometry(0.5, 0.2, 16, 32),
    ];

    // 创建材质
    const materials = [];
    for (let i = 0; i < 5; i++) {
      const material = new MaterialComponent();
      if (this.config.usePBRMaterials) {
        material.createPBRMaterial({
          color: Math.random() * 0xffffff,
          roughness: Math.random(),
          metalness: Math.random(),
        });
      } else {
        material.createStandardMaterial({
          color: Math.random() * 0xffffff,
        });
      }
      materials.push(material);
    }

    // 创建对象
    const areaSize = Math.sqrt(count) * 2;
    for (let i = 0; i < count; i++) {
      const entity = new Entity();
      
      // 添加网格组件
      const mesh = new MeshComponent();
      const geometry = geometries[Math.floor(Math.random() * geometries.length)];
      mesh.setGeometry(geometry);
      entity.addComponent(mesh);
      
      // 添加材质组件
      const material = materials[Math.floor(Math.random() * materials.length)].clone();
      entity.addComponent(material);
      
      // 设置位置
      const x = (Math.random() - 0.5) * areaSize;
      const z = (Math.random() - 0.5) * areaSize;
      const y = Math.random() * 5 + 0.5;
      entity.transform.setPosition(x, y, z);
      
      // 设置旋转
      entity.transform.setRotation(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
      );
      
      // 设置缩放
      const scale = Math.random() * 0.5 + 0.5;
      entity.transform.setScale(scale, scale, scale);
      
      // 添加到场景
      this.scene.addEntity(entity);
      this.entities.push(entity);
    }
  }

  /**
   * 设置优化系统
   */
  private setupOptimizations(): void {
    // 创建LOD系统
    this.lodSystem = new LODSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 10,
    });
    this.world.addSystem(this.lodSystem);

    // 创建视锥体剔除系统
    this.frustumCullingSystem = new FrustumCullingSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 10,
      useOctree: true,
    });
    this.world.addSystem(this.frustumCullingSystem);

    // 创建实例化渲染系统
    this.instancedRenderingSystem = new InstancedRenderingSystem({
      enabled: true,
      maxInstancesPerBatch: 1000,
      useGPUInstancing: true,
    });
    this.world.addSystem(this.instancedRenderingSystem);

    // 创建批处理系统
    this.batchingSystem = new BatchingSystem({
      enabled: true,
      useStaticBatching: true,
      useDynamicBatching: true,
      maxVerticesPerBatch: 10000,
    });
    this.world.addSystem(this.batchingSystem);
  }

  /**
   * 设置后处理
   */
  private setupPostProcessing(): void {
    // 创建后处理系统
    this.postProcessingSystem = new PostProcessingSystem({
      enabled: true,
    });

    // 添加后处理效果
    const bloomEffect = new BloomEffect({
      enabled: true,
      intensity: 0.5,
      threshold: 0.7,
      radius: 0.4,
    });
    this.postProcessingSystem.addEffect(bloomEffect);

    const ssaoEffect = new SSAOEffect({
      enabled: true,
      radius: 4,
      intensity: 0.5,
      bias: 0.025,
    });
    this.postProcessingSystem.addEffect(ssaoEffect);

    // 创建后处理实体
    const postProcessingEntity = new Entity();
    postProcessingEntity.addComponent(this.postProcessingSystem);
    this.scene.addEntity(postProcessingEntity);
  }

  /**
   * 收集自定义性能数据
   */
  protected collectCustomPerformanceData(): void {
    super.collectCustomPerformanceData();

    // 获取渲染器
    const renderer = this.engine.getRenderer().getThreeRenderer();

    // 收集渲染统计信息
    const info = renderer.info;
    this.renderStats.drawCalls.push(info.render.calls);
    this.renderStats.triangles.push(info.render.triangles);
    this.renderStats.vertices.push(info.render.vertices || 0);
    this.renderStats.textureMemory.push((info.memory.textures || 0) / (1024 * 1024));
    this.renderStats.geometryMemory.push((info.memory.geometries || 0) / (1024 * 1024));

    // 添加到性能数据历史
    this.performanceData.drawCallsHistory.push(info.render.calls);
    this.performanceData.trianglesHistory.push(info.render.triangles);
    this.performanceData.verticesHistory.push(info.render.vertices || 0);
    this.performanceData.textureMemoryHistory.push((info.memory.textures || 0) / (1024 * 1024));
    this.performanceData.geometryMemoryHistory.push((info.memory.geometries || 0) / (1024 * 1024));
  }

  /**
   * 计算自定义性能数据
   */
  protected calculateCustomPerformanceData(): { [key: string]: any } {
    return {
      drawCalls: this.calculateAverage(this.renderStats.drawCalls),
      triangles: this.calculateAverage(this.renderStats.triangles),
      vertices: this.calculateAverage(this.renderStats.vertices),
      textureMemory: this.calculateAverage(this.renderStats.textureMemory),
      geometryMemory: this.calculateAverage(this.renderStats.geometryMemory),
    };
  }

  /**
   * 清理测试
   */
  protected cleanup(): void {
    super.cleanup();

    // 清理实体
    for (const entity of this.entities) {
      this.scene.removeEntity(entity);
    }
    this.entities = [];

    // 清理系统
    if (this.lodSystem) {
      this.world.removeSystem(this.lodSystem);
      this.lodSystem = undefined;
    }

    if (this.frustumCullingSystem) {
      this.world.removeSystem(this.frustumCullingSystem);
      this.frustumCullingSystem = undefined;
    }

    if (this.instancedRenderingSystem) {
      this.world.removeSystem(this.instancedRenderingSystem);
      this.instancedRenderingSystem = undefined;
    }

    if (this.batchingSystem) {
      this.world.removeSystem(this.batchingSystem);
      this.batchingSystem = undefined;
    }

    Debug.log('渲染性能测试', '测试清理完成');
  }
}
