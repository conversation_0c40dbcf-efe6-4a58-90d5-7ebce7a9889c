/**
 * 游戏手柄输入设备
 */
import { BaseInputDevice } from '../InputDevice';
/**
 * 游戏手柄输入设备
 */
export declare class GamepadDevice extends BaseInputDevice {
    /** 游戏手柄映射 */
    private gamepads;
    /** 游戏手柄事件处理器 */
    private gamepadEventHandlers;
    /**
     * 创建游戏手柄输入设备
     */
    constructor();
    /**
     * 初始化事件处理器
     */
    private initEventHandlers;
    /**
     * 初始化设备
     */
    initialize(): void;
    /**
     * 销毁设备
     */
    destroy(): void;
    /**
     * 更新设备状态
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加事件监听器
     */
    private addEventListeners;
    /**
     * 移除事件监听器
     */
    private removeEventListeners;
    /**
     * 初始化已连接的游戏手柄
     */
    private initGamepads;
    /**
     * 更新游戏手柄状态
     */
    private updateGamepads;
    /**
     * 添加游戏手柄
     * @param gamepad 游戏手柄
     */
    private addGamepad;
    /**
     * 移除游戏手柄
     * @param gamepad 游戏手柄
     */
    private removeGamepad;
    /**
     * 处理游戏手柄连接事件
     * @param event 游戏手柄事件
     */
    private handleGamepadConnected;
    /**
     * 处理游戏手柄断开事件
     * @param event 游戏手柄事件
     */
    private handleGamepadDisconnected;
    /**
     * 获取游戏手柄
     * @param index 游戏手柄索引
     * @returns 游戏手柄
     */
    getGamepad(index: number): Gamepad | undefined;
    /**
     * 获取所有游戏手柄
     * @returns 游戏手柄列表
     */
    getGamepads(): Gamepad[];
    /**
     * 检查游戏手柄按钮是否按下
     * @param gamepadIndex 游戏手柄索引
     * @param buttonIndex 按钮索引
     * @returns 是否按下
     */
    isButtonPressed(gamepadIndex: number, buttonIndex: number): boolean;
    /**
     * 获取游戏手柄按钮值
     * @param gamepadIndex 游戏手柄索引
     * @param buttonIndex 按钮索引
     * @returns 按钮值
     */
    getButtonValue(gamepadIndex: number, buttonIndex: number): number;
    /**
     * 获取游戏手柄轴值
     * @param gamepadIndex 游戏手柄索引
     * @param axisIndex 轴索引
     * @returns 轴值
     */
    getAxisValue(gamepadIndex: number, axisIndex: number): number;
}
