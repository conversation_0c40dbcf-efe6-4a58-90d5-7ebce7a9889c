/**
 * 物理碰撞器组件
 * 为实体提供碰撞形状
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';

/**
 * 碰撞器类型
 */
export enum ColliderType {
  /** 盒体 */
  BOX = 'box',
  /** 球体 */
  SPHERE = 'sphere',
  /** 圆柱体 */
  CYLINDER = 'cylinder',
  /** 胶囊体 */
  CAPSULE = 'capsule',
  /** 凸包 */
  CONVEX = 'convex',
  /** 三角网格 */
  TRIMESH = 'trimesh',
  /** 平面 */
  PLANE = 'plane',
  /** 复合 */
  COMPOUND = 'compound',
  /** 高度场 */
  HEIGHTFIELD = 'heightfield'
}

/**
 * 碰撞形状
 */
export interface ColliderShape {
  /** 碰撞形状 */
  shape: CANNON.Shape;
  /** 位置偏移 */
  offset: CANNON.Vec3;
  /** 旋转偏移 */
  orientation: CANNON.Quaternion;
}

/**
 * 碰撞器选项
 */
export interface ColliderOptions {
  /** 碰撞器类型 */
  type: ColliderType;
  /** 碰撞器参数 */
  params?: any;
  /** 位置偏移 */
  offset?: THREE.Vector3;
  /** 旋转偏移 */
  orientation?: THREE.Quaternion;
  /** 是否触发器 */
  isTrigger?: boolean;
}

/**
 * 物理碰撞器组件
 */
export class PhysicsColliderComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'PhysicsColliderComponent';

  /** 碰撞形状列表 */
  public shapes: ColliderShape[] = [];

  /** 碰撞器类型 */
  private colliderType: ColliderType;

  /** 碰撞器参数 */
  private params: any;

  /** 位置偏移 */
  private offset: THREE.Vector3;

  /** 旋转偏移 */
  private orientation: THREE.Quaternion;

  /** 是否触发器 */
  private isTrigger: boolean;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /**
   * 创建物理碰撞器组件
   * @param options 碰撞器选项
   */
  constructor(options: ColliderOptions) {
    super(PhysicsColliderComponent.type);

    this.colliderType = options.type;
    this.params = options.params || {};
    this.offset = options.offset ? options.offset.clone() : new THREE.Vector3();
    this.orientation = options.orientation ? options.orientation.clone() : new THREE.Quaternion();
    this.isTrigger = options.isTrigger || false;
  }

  /**
   * 初始化碰撞器
   * @param _world 物理世界（未使用）
   */
  public initialize(_world: CANNON.World): void {
    if (this.initialized || !this.getEntity() || this.destroyed) return;

    // 创建碰撞形状
    const shape = this.createShape();
    if (shape) {
      // 设置是否触发器
      if (this.isTrigger) {
        shape.collisionResponse = false;
      }

      // 添加到形状列表
      this.shapes.push({
        shape,
        offset: new CANNON.Vec3(this.offset.x, this.offset.y, this.offset.z),
        orientation: new CANNON.Quaternion(
          this.orientation.x,
          this.orientation.y,
          this.orientation.z,
          this.orientation.w
        )
      });
    }

    this.initialized = true;
  }

  /**
   * 创建碰撞形状
   * @returns 碰撞形状
   */
  private createShape(): CANNON.Shape | null {
    // 从实体的变换组件获取缩放
    const entity = this.getEntity();
    const transform = entity?.getTransform();
    const scale = transform ? transform.getScale() : new THREE.Vector3(1, 1, 1);

    switch (this.colliderType) {
      case ColliderType.BOX:
        return this.createBoxShape(scale);

      case ColliderType.SPHERE:
        return this.createSphereShape(scale);

      case ColliderType.CYLINDER:
        return this.createCylinderShape(scale);

      case ColliderType.CAPSULE:
        return this.createCapsuleShape(scale);

      case ColliderType.CONVEX:
        return this.createConvexShape();

      case ColliderType.TRIMESH:
        return this.createTrimeshShape();

      case ColliderType.PLANE:
        return this.createPlaneShape();

      case ColliderType.HEIGHTFIELD:
        return this.createHeightfieldShape();

      default:
        console.warn(`不支持的碰撞器类型: ${this.colliderType}`);
        return null;
    }
  }

  /**
   * 创建盒体形状
   * @param scale 缩放
   * @returns 盒体形状
   */
  private createBoxShape(scale: THREE.Vector3): CANNON.Box {
    const halfExtents = this.params.halfExtents || { x: 0.5, y: 0.5, z: 0.5 };

    return new CANNON.Box(new CANNON.Vec3(
      halfExtents.x * scale.x,
      halfExtents.y * scale.y,
      halfExtents.z * scale.z
    ));
  }

  /**
   * 创建球体形状
   * @param scale 缩放
   * @returns 球体形状
   */
  private createSphereShape(scale: THREE.Vector3): CANNON.Sphere {
    const radius = this.params.radius || 0.5;
    // 使用最大缩放值作为球体半径的缩放
    const maxScale = Math.max(scale.x, scale.y, scale.z);

    return new CANNON.Sphere(radius * maxScale);
  }

  /**
   * 创建圆柱体形状
   * @param scale 缩放
   * @returns 圆柱体形状
   */
  private createCylinderShape(scale: THREE.Vector3): CANNON.Cylinder {
    const radiusTop = (this.params.radiusTop || 0.5) * Math.max(scale.x, scale.z);
    const radiusBottom = (this.params.radiusBottom || 0.5) * Math.max(scale.x, scale.z);
    const height = (this.params.height || 1) * scale.y;
    const numSegments = this.params.numSegments || 8;

    return new CANNON.Cylinder(radiusTop, radiusBottom, height, numSegments);
  }

  /**
   * 创建胶囊体形状
   * @param scale 缩放
   * @returns 胶囊体形状
   */
  private createCapsuleShape(scale: THREE.Vector3): CANNON.Cylinder {
    // CANNON.js没有内置的胶囊体，使用圆柱体代替
    const radius = (this.params.radius || 0.5) * Math.max(scale.x, scale.z);
    const height = (this.params.height || 1) * scale.y;
    const numSegments = this.params.numSegments || 8;

    // 创建圆柱体
    return new CANNON.Cylinder(radius, radius, height, numSegments);
  }

  /**
   * 创建凸包形状
   * @returns 凸包形状
   */
  private createConvexShape(): CANNON.ConvexPolyhedron | null {
    // 需要顶点和面
    const vertices = this.params.vertices;
    const faces = this.params.faces;

    if (!vertices || !faces) {
      console.warn('创建凸包形状需要顶点和面数据');
      return null;
    }

    // 转换顶点为CANNON.Vec3
    const cannonVertices = vertices.map((v: any) => new CANNON.Vec3(v.x, v.y, v.z));

    return new CANNON.ConvexPolyhedron({
      vertices: cannonVertices,
      faces
    });
  }

  /**
   * 创建三角网格形状
   * @returns 三角网格形状
   */
  private createTrimeshShape(): CANNON.Trimesh | null {
    // 需要顶点和索引
    const vertices = this.params.vertices;
    const indices = this.params.indices;

    if (!vertices || !indices) {
      // 尝试从网格组件获取几何体
      const entity = this.getEntity();
      const meshComponent = entity?.getComponent('MeshComponent') as any as any;
      if (meshComponent) {
        // 使用类型断言访问 mesh 属性
        const mesh = (meshComponent as any).mesh;
        if (mesh && mesh.geometry) {
          return this.createTrimeshFromGeometry(mesh.geometry);
        }
      }

      console.warn('创建三角网格形状需要顶点和索引数据');
      return null;
    }

    // 创建三角网格
    return new CANNON.Trimesh(vertices, indices);
  }

  /**
   * 从Three.js几何体创建三角网格形状
   * @param geometry Three.js几何体
   * @returns 三角网格形状
   */
  private createTrimeshFromGeometry(geometry: THREE.BufferGeometry): CANNON.Trimesh | null {
    // 确保几何体已经索引化
    if (!geometry.index) {
      geometry = geometry.toNonIndexed();
    }

    // 获取顶点和索引
    const position = geometry.getAttribute('position');
    const index = geometry.index;

    if (!position || !index) {
      console.warn('几何体缺少位置或索引属性');
      return null;
    }

    // 创建顶点数组
    const vertices = new Float32Array(position.count * 3);
    for (let i = 0; i < position.count; i++) {
      vertices[i * 3] = position.getX(i);
      vertices[i * 3 + 1] = position.getY(i);
      vertices[i * 3 + 2] = position.getZ(i);
    }

    // 创建索引数组
    const indices = new Uint32Array(index.count);
    for (let i = 0; i < index.count; i++) {
      indices[i] = index.getX(i);
    }

    // 创建三角网格
    // 将 TypedArray 转换为普通数组
    const verticesArray = Array.from(vertices);
    const indicesArray = Array.from(indices);

    return new CANNON.Trimesh(verticesArray, indicesArray);
  }

  /**
   * 创建平面形状
   * @returns 平面形状
   */
  private createPlaneShape(): CANNON.Plane {
    return new CANNON.Plane();
  }

  /**
   * 创建高度场形状
   * @returns 高度场形状
   */
  private createHeightfieldShape(): CANNON.Heightfield | null {
    const data = this.params.data;
    const elementSize = this.params.elementSize || 1;

    if (!data) {
      console.warn('创建高度场形状需要高度数据');
      return null;
    }

    return new CANNON.Heightfield(data, {
      elementSize
    });
  }

  /**
   * 获取碰撞形状列表
   * @returns 碰撞形状列表
   */
  public getShapes(): ColliderShape[] {
    return this.shapes;
  }

  /**
   * 设置是否触发器
   * @param isTrigger 是否触发器
   */
  public setTrigger(isTrigger: boolean): void {
    this.isTrigger = isTrigger;

    // 更新所有形状
    for (const shape of this.shapes) {
      shape.shape.collisionResponse = !isTrigger;
    }
  }

  /**
   * 是否触发器
   * @returns 是否触发器
   */
  public isTriggerCollider(): boolean {
    return this.isTrigger;
  }

  /**
   * 销毁碰撞器
   */
  public dispose(): void {
    if (this.destroyed) return;

    // 清空形状列表
    this.shapes = [];
    this.initialized = false;

    this.destroyed = true;

    // 调用基类的 dispose 方法
    super.dispose();
  }
}
