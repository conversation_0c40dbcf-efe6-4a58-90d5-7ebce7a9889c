/**
 * 后处理系统
 * 用于管理和应用后处理效果
 */
import * as THREE from 'three';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
import { System } from '../../core/System';
import { PostProcessingEffect } from './PostProcessingEffect';
/**
 * 后处理系统选项
 */
export interface PostProcessingSystemOptions {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动调整大小 */
    autoResize?: boolean;
    /** 渲染目标选项 */
    renderTargetOptions?: {
        /** 宽度 */
        width?: number;
        /** 高度 */
        height?: number;
        /** 采样级别 */
        samples?: number;
        /** 是否使用深度纹理 */
        depthTexture?: boolean;
        /** 是否使用浮点纹理 */
        floatTexture?: boolean;
    };
}
/**
 * 后处理系统事件类型
 */
export declare enum PostProcessingEventType {
    /** 效果添加 */
    EFFECT_ADDED = "effectAdded",
    /** 效果移除 */
    EFFECT_REMOVED = "effectRemoved",
    /** 效果启用 */
    EFFECT_ENABLED = "effectEnabled",
    /** 效果禁用 */
    EFFECT_DISABLED = "effectDisabled",
    /** 效果顺序变更 */
    EFFECT_ORDER_CHANGED = "effectOrderChanged",
    /** 系统启用 */
    SYSTEM_ENABLED = "systemEnabled",
    /** 系统禁用 */
    SYSTEM_DISABLED = "systemDisabled",
    /** 渲染目标调整大小 */
    RESIZE = "resize"
}
/**
 * 后处理系统
 */
export declare class PostProcessingSystem extends System {
    /** 系统名称 */
    static readonly NAME: string;
    /** 系统名称（实例属性） */
    protected name: string;
    /** 是否启用后处理 */
    private postProcessingEnabled;
    /** 是否自动调整大小 */
    private autoResize;
    /** 渲染器 */
    private renderer;
    /** 效果合成器 */
    private composer;
    /** 渲染通道 */
    private renderPass;
    /** 效果列表 */
    private effects;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否已初始化 */
    private initialized;
    /** 是否已销毁 */
    private destroyed;
    /** 渲染目标宽度 */
    private width;
    /** 渲染目标高度 */
    private height;
    /** 渲染目标采样级别 */
    private samples;
    /** 是否使用深度纹理 */
    private useDepthTexture;
    /** 是否使用浮点纹理 */
    private useFloatTexture;
    /** 渲染目标 */
    private renderTarget;
    /**
     * 创建后处理系统
     * @param options 后处理系统选项
     */
    constructor(options?: PostProcessingSystemOptions);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 检查是否已初始化
     * @returns 是否已初始化
     */
    isInitialized(): boolean;
    /**
     * 设置渲染器、场景和相机
     * @param renderer 渲染器
     * @param scene 场景
     * @param camera 相机
     */
    setup(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera): void;
    /**
     * 创建渲染目标
     */
    private createRenderTarget;
    /**
     * 处理窗口大小变化
     */
    private handleResize;
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    resize(width: number, height: number): void;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加效果
     * @param effect 后处理效果
     * @returns 是否成功添加
     */
    addEffect(effect: PostProcessingEffect): boolean;
    /**
     * 添加效果通道
     * @param effect 后处理效果
     */
    private addEffectPass;
    /**
     * 移除效果
     * @param effect 后处理效果
     * @returns 是否成功移除
     */
    removeEffect(effect: PostProcessingEffect): boolean;
    /**
     * 移除效果通道
     * @param effect 后处理效果
     */
    private removeEffectPass;
    /**
     * 获取效果
     * @param name 效果名称
     * @returns 后处理效果
     */
    getEffect(name: string): PostProcessingEffect | null;
    /**
     * 获取所有效果
     * @returns 后处理效果列表
     */
    getEffects(): PostProcessingEffect[];
    /**
     * 启用效果
     * @param name 效果名称
     * @returns 是否成功启用
     */
    enableEffect(name: string): boolean;
    /**
     * 禁用效果
     * @param name 效果名称
     * @returns 是否成功禁用
     */
    disableEffect(name: string): boolean;
    /**
     * 设置效果顺序
     * @param names 效果名称列表
     * @returns 是否成功设置
     */
    setEffectOrder(names: string[]): boolean;
    /**
     * 重新构建通道
     */
    private rebuildPasses;
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): this;
    /**
     * 获取是否启用
     * @returns 是否启用
     */
    isEnabled(): boolean;
    /**
     * 获取渲染目标
     * @returns 渲染目标
     */
    getRenderTarget(): THREE.WebGLRenderTarget | null;
    /**
     * 获取效果合成器
     * @returns 效果合成器
     */
    getComposer(): EffectComposer | null;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param callback 监听器函数
     * @returns this 实例
     */
    on(event: string, callback: (...args: any[]) => void): this;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param callback 监听器函数
     * @returns this 实例
     */
    off(event: string, callback?: (...args: any[]) => void): this;
    /**
     * 销毁系统
     */
    dispose(): void;
}
