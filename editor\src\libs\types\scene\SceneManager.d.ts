/**
 * 场景管理器
 * 负责场景的加载、卸载和切换
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Scene } from './Scene';
import type { World } from '../core/World';
import { AssetManager } from '../assets/AssetManager';
/**
 * 场景过渡类型
 */
export declare enum SceneTransitionType {
    /** 无过渡 */
    NONE = "none",
    /** 淡入淡出 */
    FADE = "fade",
    /** 交叉淡入淡出 */
    CROSS_FADE = "crossFade",
    /** 滑动 */
    SLIDE = "slide",
    /** 缩放 */
    ZOOM = "zoom",
    /** 自定义 */
    CUSTOM = "custom"
}
/**
 * 场景过渡选项
 */
export interface SceneTransitionOptions {
    /** 过渡类型 */
    type: SceneTransitionType;
    /** 过渡持续时间（毫秒） */
    duration?: number;
    /** 过渡方向（对于滑动过渡） */
    direction?: 'left' | 'right' | 'up' | 'down';
    /** 过渡缓动函数 */
    easing?: string;
    /** 自定义过渡函数 */
    customTransition?: (fromScene: Scene | null, toScene: Scene, progress: number, onComplete: () => void) => void;
}
/**
 * 场景加载选项
 */
export interface SceneLoadOptions {
    /** 是否设置为活跃场景 */
    setActive?: boolean;
    /** 是否显示加载界面 */
    showLoadingScreen?: boolean;
    /** 是否预加载资源 */
    preloadAssets?: boolean;
    /** 是否初始化场景图 */
    initializeSceneGraph?: boolean;
    /** 过渡选项 */
    transition?: SceneTransitionOptions;
    /** 加载完成回调 */
    onLoaded?: (scene: Scene) => void;
    /** 加载进度回调 */
    onProgress?: (progress: number) => void;
    /** 加载错误回调 */
    onError?: (error: Error) => void;
}
/**
 * 场景管理器选项
 */
export interface SceneManagerOptions {
    /** 世界实例 */
    world: World;
    /** 资产管理器 */
    assetManager?: AssetManager;
    /** 默认过渡选项 */
    defaultTransition?: SceneTransitionOptions;
    /** 是否启用场景缓存 */
    enableSceneCache?: boolean;
    /** 最大场景缓存数量 */
    maxSceneCacheCount?: number;
}
/**
 * 场景管理器
 */
export declare class SceneManager extends EventEmitter {
    /** 世界实例 */
    private world;
    /** 资产管理器 */
    private assetManager;
    /** 场景预加载器 */
    private scenePreloader;
    /** 默认过渡选项 */
    private defaultTransition;
    /** 是否启用场景缓存 */
    private enableSceneCache;
    /** 最大场景缓存数量 */
    private maxSceneCacheCount;
    /** 场景缓存 */
    private sceneCache;
    /** 场景访问时间 */
    private sceneAccessTimes;
    /** 当前场景 */
    private currentScene;
    /** 上一个场景 */
    private previousScene;
    /** 是否正在切换场景 */
    private isTransitioning;
    /** 是否正在加载场景 */
    private isLoading;
    /** 加载界面元素 */
    private loadingScreen;
    /** 加载进度元素 */
    private loadingProgress;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建场景管理器实例
     * @param options 场景管理器选项
     */
    constructor(options: SceneManagerOptions);
    /**
     * 初始化场景管理器
     */
    initialize(): void;
    /**
     * 创建加载界面
     */
    private createLoadingScreen;
    /**
     * 显示加载界面
     */
    private showLoadingScreen;
    /**
     * 隐藏加载界面
     */
    private hideLoadingScreen;
    /**
     * 更新加载进度
     * @param progress 进度（0-1）
     */
    private updateLoadingProgress;
    /**
     * 加载场景
     * @param sceneId 场景ID
     * @param options 加载选项
     * @returns Promise，解析为加载的场景
     */
    loadScene(sceneId: string, options?: SceneLoadOptions): Promise<Scene>;
    /**
     * 切换到场景
     * @param scene 目标场景
     * @param transitionOptions 过渡选项
     * @returns Promise，解析为切换后的场景
     */
    private transitionToScene;
    /**
     * 执行场景过渡
     * @param fromScene 源场景
     * @param toScene 目标场景
     * @param options 过渡选项
     * @returns Promise
     */
    private executeTransition;
    /**
     * 执行淡入淡出过渡
     * @param duration 持续时间
     * @param onComplete 完成回调
     */
    private executeFadeTransition;
    /**
     * 执行交叉淡入淡出过渡
     * @param fromScene 源场景
     * @param toScene 目标场景
     * @param duration 持续时间
     * @param onComplete 完成回调
     */
    private executeCrossFadeTransition;
    /**
     * 执行滑动过渡
     * @param direction 方向
     * @param duration 持续时间
     * @param onComplete 完成回调
     */
    private executeSlideTransition;
    /**
     * 执行缩放过渡
     * @param duration 持续时间
     * @param onComplete 完成回调
     */
    private executeZoomTransition;
    /**
     * 添加场景到缓存
     * @param scene 场景实例
     */
    private addToSceneCache;
    /**
     * 从缓存中移除最久未访问的场景
     */
    private removeOldestSceneFromCache;
    /**
     * 从缓存中移除场景
     * @param sceneId 场景ID
     * @returns 是否成功移除
     */
    removeSceneFromCache(sceneId: string): boolean;
    /**
     * 清空场景缓存
     */
    clearSceneCache(): void;
    /**
     * 获取当前场景
     * @returns 当前场景
     */
    getCurrentScene(): Scene | null;
    /**
     * 获取上一个场景
     * @returns 上一个场景
     */
    getPreviousScene(): Scene | null;
    /**
     * 是否正在加载场景
     * @returns 是否正在加载
     */
    isSceneLoading(): boolean;
    /**
     * 是否正在切换场景
     * @returns 是否正在切换
     */
    isSceneTransitioning(): boolean;
    /**
     * 销毁场景管理器
     */
    dispose(): void;
}
