/**
 * SceneManager类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SceneManager } from '../../src/scene/SceneManager';
import { Scene } from '../../src/scene/Scene';
import { Entity } from '../../src/core/Entity';
import { Transform } from '../../src/scene/Transform';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { AssetManager } from '../../src/assets/AssetManager';
import * as THREE from 'three';

describe('SceneManager', () => {
  let engine: Engine;
  let world: World;
  let sceneManager: SceneManager;
  let assetManager: AssetManager;
  
  // 在每个测试前创建一个新的场景管理器
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({ autoStart: false });
    world = engine.getWorld();
    
    // 获取资产管理器
    assetManager = engine['assetManager'];
    
    // 创建场景管理器
    sceneManager = new SceneManager(world);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后清理
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试场景管理器初始化
  it('应该正确初始化场景管理器', () => {
    expect(sceneManager).toBeDefined();
    expect(sceneManager['world']).toBe(world);
    expect(sceneManager['scenes'].size).toBe(0);
    expect(sceneManager['activeScene']).toBeNull();
  });
  
  // 测试创建场景
  it('应该能够创建场景', () => {
    // 创建场景
    const scene = sceneManager.createScene('测试场景');
    
    // 验证场景已创建
    expect(scene).toBeDefined();
    expect(scene.getName()).toBe('测试场景');
    expect(sceneManager['scenes'].has(scene.getId())).toBe(true);
  });
  
  // 测试加载场景
  it('应该能够加载场景', async () => {
    // 模拟场景数据
    const sceneData = {
      id: 'test-scene',
      name: '测试场景',
      entities: [
        {
          id: 'entity1',
          name: '实体1',
          components: [
            {
              type: 'Transform',
              position: { x: 1, y: 2, z: 3 },
              rotation: { x: 0, y: 0, z: 0 },
              scale: { x: 1, y: 1, z: 1 }
            }
          ]
        }
      ]
    };
    
    // 模拟加载函数
    const loadSceneSpy = vi.spyOn(assetManager, 'loadJSON').mockResolvedValue(sceneData);
    
    // 加载场景
    const scene = await sceneManager.loadScene('test-scene.json');
    
    // 验证场景已加载
    expect(scene).toBeDefined();
    expect(scene.getName()).toBe('测试场景');
    expect(scene.getId()).toBe('test-scene');
    expect(sceneManager['scenes'].has(scene.getId())).toBe(true);
    
    // 验证实体已创建
    const entities = scene.getAllEntities();
    expect(entities.length).toBe(1);
    expect(entities[0].getId()).toBe('entity1');
    expect(entities[0].getName()).toBe('实体1');
    
    // 验证组件已创建
    const transform = entities[0].getComponent<Transform>('Transform');
    expect(transform).toBeDefined();
    expect(transform!.position.x).toBe(1);
    expect(transform!.position.y).toBe(2);
    expect(transform!.position.z).toBe(3);
    
    // 恢复模拟
    loadSceneSpy.mockRestore();
  });
  
  // 测试设置活跃场景
  it('应该能够设置活跃场景', () => {
    // 创建场景
    const scene1 = sceneManager.createScene('场景1');
    const scene2 = sceneManager.createScene('场景2');
    
    // 设置活跃场景
    sceneManager.setActiveScene(scene1);
    
    // 验证活跃场景
    expect(sceneManager.getActiveScene()).toBe(scene1);
    
    // 更改活跃场景
    sceneManager.setActiveScene(scene2);
    
    // 验证活跃场景已更改
    expect(sceneManager.getActiveScene()).toBe(scene2);
  });
  
  // 测试卸载场景
  it('应该能够卸载场景', () => {
    // 创建场景
    const scene = sceneManager.createScene('测试场景');
    
    // 添加实体到场景
    const entity = new Entity();
    entity.addComponent(new Transform());
    scene.addEntity(entity);
    
    // 设置为活跃场景
    sceneManager.setActiveScene(scene);
    
    // 卸载场景
    sceneManager.unloadScene(scene.getId());
    
    // 验证场景已卸载
    expect(sceneManager['scenes'].has(scene.getId())).toBe(false);
    expect(sceneManager.getActiveScene()).toBeNull();
  });
  
  // 测试场景切换
  it('应该能够切换场景', async () => {
    // 创建场景
    const scene1 = sceneManager.createScene('场景1');
    const scene2 = sceneManager.createScene('场景2');
    
    // 添加实体到场景1
    const entity1 = new Entity();
    entity1.addComponent(new Transform());
    scene1.addEntity(entity1);
    
    // 添加实体到场景2
    const entity2 = new Entity();
    entity2.addComponent(new Transform());
    scene2.addEntity(entity2);
    
    // 设置场景1为活跃场景
    sceneManager.setActiveScene(scene1);
    
    // 创建场景切换监听器
    const sceneChangeSpy = vi.fn();
    sceneManager.on('sceneChanged', sceneChangeSpy);
    
    // 切换到场景2
    await sceneManager.switchScene(scene2.getId());
    
    // 验证活跃场景已切换
    expect(sceneManager.getActiveScene()).toBe(scene2);
    
    // 验证场景切换事件被触发
    expect(sceneChangeSpy).toHaveBeenCalledWith({
      previousScene: scene1,
      newScene: scene2
    });
  });
  
  // 测试场景预加载
  it('应该能够预加载场景', async () => {
    // 模拟场景数据
    const sceneData = {
      id: 'preload-scene',
      name: '预加载场景',
      entities: []
    };
    
    // 模拟加载函数
    const loadSceneSpy = vi.spyOn(assetManager, 'loadJSON').mockResolvedValue(sceneData);
    
    // 预加载场景
    const scene = await sceneManager.preloadScene('preload-scene.json');
    
    // 验证场景已预加载
    expect(scene).toBeDefined();
    expect(scene.getName()).toBe('预加载场景');
    expect(sceneManager['scenes'].has(scene.getId())).toBe(true);
    
    // 验证场景未激活
    expect(sceneManager.getActiveScene()).not.toBe(scene);
    
    // 恢复模拟
    loadSceneSpy.mockRestore();
  });
  
  // 测试场景合并
  it('应该能够合并场景', () => {
    // 创建场景
    const scene1 = sceneManager.createScene('场景1');
    const scene2 = sceneManager.createScene('场景2');
    
    // 添加实体到场景1
    const entity1 = new Entity();
    entity1.addComponent(new Transform());
    entity1.name = '实体1';
    scene1.addEntity(entity1);
    
    // 添加实体到场景2
    const entity2 = new Entity();
    entity2.addComponent(new Transform());
    entity2.name = '实体2';
    scene2.addEntity(entity2);
    
    // 合并场景
    sceneManager.mergeScenes(scene1.getId(), scene2.getId());
    
    // 验证场景1包含两个实体
    const entities = scene1.getAllEntities();
    expect(entities.length).toBe(2);
    expect(entities.some(e => e.getName() === '实体1')).toBe(true);
    expect(entities.some(e => e.getName() === '实体2')).toBe(true);
    
    // 验证场景2已卸载
    expect(sceneManager['scenes'].has(scene2.getId())).toBe(false);
  });
});
