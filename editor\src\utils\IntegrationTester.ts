/**
 * 集成测试工具
 * 用于测试编辑器与服务端微服务的集成状况
 */
import { apiClient } from '../services/ApiClient';
import { microserviceIntegration } from '../services/MicroserviceIntegration';
import { config } from '../config/environment';

export interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'skip';
  message: string;
  duration: number;
  error?: Error;
}

export interface IntegrationTestReport {
  timestamp: Date;
  environment: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  duration: number;
  results: TestResult[];
}

/**
 * 集成测试器
 */
export class IntegrationTester {
  private results: TestResult[] = [];
  private startTime: number = 0;

  /**
   * 运行所有集成测试
   */
  public async runAllTests(): Promise<IntegrationTestReport> {
    this.results = [];
    this.startTime = Date.now();

    console.log('🚀 开始运行集成测试...');

    // 基础连接测试
    await this.testApiConnection();
    await this.testServiceHealth();
    
    // 认证测试
    await this.testAuthenticationFlow();
    
    // 微服务测试
    await this.testUserService();
    await this.testProjectService();
    await this.testAssetService();
    await this.testRenderService();
    
    // WebSocket测试
    await this.testWebSocketConnection();
    
    // 配置测试
    await this.testConfiguration();

    const duration = Date.now() - this.startTime;
    const report = this.generateReport(duration);
    
    console.log('✅ 集成测试完成');
    this.printReport(report);
    
    return report;
  }

  /**
   * 测试API连接
   */
  private async testApiConnection(): Promise<void> {
    await this.runTest('API连接测试', async () => {
      const response = await apiClient.get('/health', { 
        useCache: false,
        timeout: 5000 
      });
      
      if (!response.success) {
        throw new Error('API健康检查失败');
      }
    });
  }

  /**
   * 测试服务健康状态
   */
  private async testServiceHealth(): Promise<void> {
    await this.runTest('服务健康检查', async () => {
      await microserviceIntegration.initialize();
      
      // 等待健康检查完成
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const healthStatus = microserviceIntegration.getServiceHealth();
      const unhealthyServices = microserviceIntegration.getUnhealthyServices();

      // 输出健康状态信息
      console.log('服务健康状态:', Object.keys(healthStatus).length, '个服务');

      if (unhealthyServices.length > 0) {
        throw new Error(`发现不健康的服务: ${unhealthyServices.map(s => s.name).join(', ')}`);
      }
    });
  }

  /**
   * 测试认证流程
   */
  private async testAuthenticationFlow(): Promise<void> {
    await this.runTest('认证流程测试', async () => {
      // 测试未认证状态
      try {
        await apiClient.get('/auth/profile');
        throw new Error('应该返回401错误');
      } catch (error: any) {
        if (error.response?.status !== 401) {
          throw new Error('未正确处理未认证请求');
        }
      }
      
      // 注意：这里不进行实际登录测试，避免创建测试数据
      console.log('认证流程基础验证通过');
    });
  }

  /**
   * 测试用户服务
   */
  private async testUserService(): Promise<void> {
    await this.runTest('用户服务测试', async () => {
      // 测试健康检查端点
      try {
        await apiClient.get('/users/health', { useCache: false });
      } catch (error: any) {
        if (error.response?.status === 404) {
          // 如果健康检查端点不存在，跳过测试
          throw new Error('用户服务健康检查端点不可用');
        }
        throw error;
      }
    });
  }

  /**
   * 测试项目服务
   */
  private async testProjectService(): Promise<void> {
    await this.runTest('项目服务测试', async () => {
      try {
        await apiClient.get('/projects/health', { useCache: false });
      } catch (error: any) {
        if (error.response?.status === 404) {
          throw new Error('项目服务健康检查端点不可用');
        }
        throw error;
      }
    });
  }

  /**
   * 测试资产服务
   */
  private async testAssetService(): Promise<void> {
    await this.runTest('资产服务测试', async () => {
      try {
        await apiClient.get('/assets/health', { useCache: false });
      } catch (error: any) {
        if (error.response?.status === 404) {
          throw new Error('资产服务健康检查端点不可用');
        }
        throw error;
      }
    });
  }

  /**
   * 测试渲染服务
   */
  private async testRenderService(): Promise<void> {
    await this.runTest('渲染服务测试', async () => {
      try {
        await apiClient.get('/render/health', { useCache: false });
      } catch (error: any) {
        if (error.response?.status === 404) {
          throw new Error('渲染服务健康检查端点不可用');
        }
        throw error;
      }
    });
  }

  /**
   * 测试WebSocket连接
   */
  private async testWebSocketConnection(): Promise<void> {
    await this.runTest('WebSocket连接测试', async () => {
      const wsUrl = config.collaborationServerUrl;
      
      if (!wsUrl) {
        throw new Error('WebSocket URL未配置');
      }
      
      // 简单的WebSocket连接测试
      return new Promise((resolve, reject) => {
        const ws = new WebSocket(wsUrl);
        const timeout = setTimeout(() => {
          ws.close();
          reject(new Error('WebSocket连接超时'));
        }, 5000);
        
        ws.onopen = () => {
          clearTimeout(timeout);
          ws.close();
          resolve(undefined);
        };
        
        ws.onerror = () => {
          clearTimeout(timeout);
          reject(new Error('WebSocket连接失败'));
        };
      });
    });
  }

  /**
   * 测试配置
   */
  private async testConfiguration(): Promise<void> {
    await this.runTest('配置验证测试', async () => {
      // 验证必需的配置项
      if (!config.apiUrl) {
        throw new Error('API URL未配置');
      }
      
      if (!config.collaborationServerUrl) {
        throw new Error('协作服务器URL未配置');
      }
      
      // 验证URL格式
      try {
        new URL(config.collaborationServerUrl);
      } catch {
        throw new Error('协作服务器URL格式无效');
      }
      
      console.log('配置验证通过');
    });
  }

  /**
   * 运行单个测试
   */
  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        status: 'pass',
        message: '测试通过',
        duration
      });
      
      console.log(`✅ ${name} - 通过 (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      const err = error as Error;
      
      this.results.push({
        name,
        status: 'fail',
        message: err.message,
        duration,
        error: err
      });
      
      console.log(`❌ ${name} - 失败: ${err.message} (${duration}ms)`);
    }
  }

  /**
   * 生成测试报告
   */
  private generateReport(duration: number): IntegrationTestReport {
    const passedTests = this.results.filter(r => r.status === 'pass').length;
    const failedTests = this.results.filter(r => r.status === 'fail').length;
    const skippedTests = this.results.filter(r => r.status === 'skip').length;
    
    return {
      timestamp: new Date(),
      environment: config.apiUrl,
      totalTests: this.results.length,
      passedTests,
      failedTests,
      skippedTests,
      duration,
      results: this.results
    };
  }

  /**
   * 打印测试报告
   */
  private printReport(report: IntegrationTestReport): void {
    console.log('\n📊 集成测试报告');
    console.log('='.repeat(50));
    console.log(`时间: ${report.timestamp.toLocaleString()}`);
    console.log(`环境: ${report.environment}`);
    console.log(`总测试数: ${report.totalTests}`);
    console.log(`通过: ${report.passedTests}`);
    console.log(`失败: ${report.failedTests}`);
    console.log(`跳过: ${report.skippedTests}`);
    console.log(`总耗时: ${report.duration}ms`);
    console.log('='.repeat(50));
    
    if (report.failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      report.results
        .filter(r => r.status === 'fail')
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.message}`);
        });
    }
  }
}

// 创建默认实例
export const integrationTester = new IntegrationTester();

export default IntegrationTester;
