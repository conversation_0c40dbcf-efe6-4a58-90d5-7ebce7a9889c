/**
 * 八叉树
 * 用于高效组织场景中的物体
 */
import * as THREE from 'three';
import type { Entity } from '../../../core/Entity';

/**
 * 八叉树配置接口
 */
export interface OctreeOptions {
  /** 最大深度 */
  maxDepth?: number;
  /** 每个节点的最大对象数 */
  maxObjectsPerNode?: number;
  /** 最小节点大小 */
  minNodeSize?: number;
  /** 中心点 */
  center?: THREE.Vector3;
  /** 大小 */
  size?: number;
}

/**
 * 八叉树节点类
 */
class OctreeNode {
  /** 中心点 */
  public center: THREE.Vector3;

  /** 半尺寸 */
  public halfSize: number;

  /** 深度 */
  public depth: number;

  /** 子节点 */
  public children: OctreeNode[] | null = null;

  /** 对象列表 */
  public objects: Map<Entity, { position: THREE.Vector3; radius: number }> = new Map();

  /**
   * 创建八叉树节点
   * @param center 中心点
   * @param halfSize 半尺寸
   * @param depth 深度
   */
  constructor(center: THREE.Vector3, halfSize: number, depth: number) {
    this.center = center;
    this.halfSize = halfSize;
    this.depth = depth;
  }

  /**
   * 检查节点是否包含点
   * @param point 点
   * @returns 是否包含
   */
  public containsPoint(point: THREE.Vector3): boolean {
    return (
      point.x >= this.center.x - this.halfSize &&
      point.x <= this.center.x + this.halfSize &&
      point.y >= this.center.y - this.halfSize &&
      point.y <= this.center.y + this.halfSize &&
      point.z >= this.center.z - this.halfSize &&
      point.z <= this.center.z + this.halfSize
    );
  }

  /**
   * 检查节点是否与包围球相交
   * @param center 包围球中心
   * @param radius 包围球半径
   * @returns 是否相交
   */
  public intersectsSphere(center: THREE.Vector3, radius: number): boolean {
    // 计算包围球中心到节点的最近点
    const closestX = Math.max(this.center.x - this.halfSize, Math.min(center.x, this.center.x + this.halfSize));
    const closestY = Math.max(this.center.y - this.halfSize, Math.min(center.y, this.center.y + this.halfSize));
    const closestZ = Math.max(this.center.z - this.halfSize, Math.min(center.z, this.center.z + this.halfSize));

    // 计算最近点到包围球中心的距离
    const distance = Math.sqrt(
      (closestX - center.x) * (closestX - center.x) +
      (closestY - center.y) * (closestY - center.y) +
      (closestZ - center.z) * (closestZ - center.z)
    );

    // 如果距离小于等于半径，则相交
    return distance <= radius;
  }

  /**
   * 检查节点是否与视锥体相交
   * @param frustum 视锥体
   * @returns 是否相交
   */
  public intersectsFrustum(frustum: THREE.Frustum): boolean {
    // 创建包围盒
    const box = new THREE.Box3(
      new THREE.Vector3(
        this.center.x - this.halfSize,
        this.center.y - this.halfSize,
        this.center.z - this.halfSize
      ),
      new THREE.Vector3(
        this.center.x + this.halfSize,
        this.center.y + this.halfSize,
        this.center.z + this.halfSize
      )
    );

    // 检查包围盒是否与视锥体相交
    return frustum.intersectsBox(box);
  }

  /**
   * 获取包含点的子节点索引
   * @param point 点
   * @returns 子节点索引
   */
  public getChildIndexForPoint(point: THREE.Vector3): number {
    let index = 0;

    if (point.x > this.center.x) index |= 1;
    if (point.y > this.center.y) index |= 2;
    if (point.z > this.center.z) index |= 4;

    return index;
  }

  /**
   * 创建子节点
   */
  public createChildren(): void {
    const halfSize = this.halfSize * 0.5;
    const depth = this.depth + 1;

    this.children = [];

    for (let i = 0; i < 8; i++) {
      const x = this.center.x + (i & 1 ? halfSize : -halfSize);
      const y = this.center.y + (i & 2 ? halfSize : -halfSize);
      const z = this.center.z + (i & 4 ? halfSize : -halfSize);

      const center = new THREE.Vector3(x, y, z);
      this.children.push(new OctreeNode(center, halfSize, depth));
    }
  }
}

/**
 * 八叉树类
 */
export class Octree {
  /** 根节点 */
  private root: OctreeNode;

  /** 最大深度 */
  private maxDepth: number;

  /** 每个节点的最大对象数 */
  private maxObjectsPerNode: number;

  /** 最小节点大小 */
  private minNodeSize: number;

  /** 实体到节点的映射 */
  private entityToNode: Map<Entity, OctreeNode> = new Map();

  /** 临时向量 */
  private tempVector: THREE.Vector3 = new THREE.Vector3();

  /**
   * 创建八叉树
   * @param options 八叉树配置
   */
  constructor(options: OctreeOptions = {}) {
    this.maxDepth = options.maxDepth !== undefined ? options.maxDepth : 8;
    this.maxObjectsPerNode = options.maxObjectsPerNode !== undefined ? options.maxObjectsPerNode : 16;
    this.minNodeSize = options.minNodeSize !== undefined ? options.minNodeSize : 1;

    const center = options.center || new THREE.Vector3(0, 0, 0);
    const size = options.size !== undefined ? options.size : 1000;

    this.root = new OctreeNode(center, size * 0.5, 0);
  }

  /**
   * 插入实体
   * @param entity 实体
   * @param position 位置
   * @param radius 半径
   */
  public insert(entity: Entity, position: THREE.Vector3, radius: number = 1): void {
    // 如果实体已存在，则先移除
    if (this.entityToNode.has(entity)) {
      this.remove(entity);
    }

    // 插入实体
    this.insertIntoNode(this.root, entity, position, radius);
  }

  /**
   * 将实体插入节点
   * @param node 节点
   * @param entity 实体
   * @param position 位置
   * @param radius 半径
   * @returns 是否插入成功
   */
  private insertIntoNode(node: OctreeNode, entity: Entity, position: THREE.Vector3, radius: number): boolean {
    // 检查实体是否与节点相交
    if (!node.intersectsSphere(position, radius)) {
      return false;
    }

    // 如果节点没有子节点，并且对象数量小于最大值或者达到最大深度或者节点大小小于最小值，则将实体添加到当前节点
    if (
      node.children === null &&
      (node.objects.size < this.maxObjectsPerNode || node.depth >= this.maxDepth || node.halfSize <= this.minNodeSize)
    ) {
      node.objects.set(entity, { position: position.clone(), radius });
      this.entityToNode.set(entity, node);
      return true;
    }

    // 如果节点没有子节点，则创建子节点
    if (node.children === null) {
      node.createChildren();

      // 将当前节点的对象重新分配到子节点
      const objectsToReassign: Array<[Entity, { position: THREE.Vector3; radius: number }]> = [];
      node.objects.forEach((data, obj) => {
        objectsToReassign.push([obj, data]);
      });

      for (const [obj, data] of objectsToReassign) {
        let inserted = false;
        for (const child of node.children!) {
          if (this.insertIntoNode(child, obj, data.position, data.radius)) {
            inserted = true;
            break;
          }
        }

        // 如果对象插入到子节点，则从当前节点移除
        if (inserted) {
          node.objects.delete(obj);
        }
      }
    }

    // 尝试将实体插入到子节点
    let inserted = false;
    for (const child of node.children!) {
      if (this.insertIntoNode(child, entity, position, radius)) {
        inserted = true;
        break;
      }
    }

    // 如果实体没有插入到任何子节点，则添加到当前节点
    if (!inserted) {
      node.objects.set(entity, { position: position.clone(), radius });
      this.entityToNode.set(entity, node);
    }

    return true;
  }

  /**
   * 移除实体
   * @param entity 实体
   */
  public remove(entity: Entity): void {
    // 获取实体所在的节点
    const node = this.entityToNode.get(entity);
    if (!node) {
      return;
    }

    // 从节点中移除实体
    node.objects.delete(entity);

    // 从映射中移除实体
    this.entityToNode.delete(entity);
  }

  /**
   * 更新实体
   * @param entity 实体
   * @param position 位置
   * @param radius 半径
   */
  public update(entity: Entity, position: THREE.Vector3, radius: number = 1): void {
    // 移除实体
    this.remove(entity);

    // 重新插入实体
    this.insert(entity, position, radius);
  }

  /**
   * 查询包含点的实体
   * @param point 点
   * @returns 实体列表
   */
  public queryPoint(point: THREE.Vector3): Entity[] {
    const result: Entity[] = [];
    this.queryPointInNode(this.root, point, result);
    return result;
  }

  /**
   * 在节点中查询包含点的实体
   * @param node 节点
   * @param point 点
   * @param result 结果列表
   */
  private queryPointInNode(node: OctreeNode, point: THREE.Vector3, result: Entity[]): void {
    // 检查点是否在节点内
    if (!node.containsPoint(point)) {
      return;
    }

    // 添加节点中的对象
    node.objects.forEach((data, entity) => {
      const distance = data.position.distanceTo(point);
      if (distance <= data.radius) {
        result.push(entity);
      }
    });

    // 如果节点有子节点，则递归查询
    if (node.children !== null) {
      for (const child of node.children) {
        this.queryPointInNode(child, point, result);
      }
    }
  }

  /**
   * 查询与包围球相交的实体
   * @param center 包围球中心
   * @param radius 包围球半径
   * @returns 实体列表
   */
  public querySphere(center: THREE.Vector3, radius: number): Entity[] {
    const result: Entity[] = [];
    this.querySphereInNode(this.root, center, radius, result);
    return result;
  }

  /**
   * 在节点中查询与包围球相交的实体
   * @param node 节点
   * @param center 包围球中心
   * @param radius 包围球半径
   * @param result 结果列表
   */
  private querySphereInNode(node: OctreeNode, center: THREE.Vector3, radius: number, result: Entity[]): void {
    // 检查包围球是否与节点相交
    if (!node.intersectsSphere(center, radius)) {
      return;
    }

    // 添加节点中的对象
    node.objects.forEach((data, entity) => {
      const distance = data.position.distanceTo(center);
      if (distance <= radius + data.radius) {
        result.push(entity);
      }
    });

    // 如果节点有子节点，则递归查询
    if (node.children !== null) {
      for (const child of node.children) {
        this.querySphereInNode(child, center, radius, result);
      }
    }
  }

  /**
   * 查询与视锥体相交的实体
   * @param frustum 视锥体
   * @returns 实体列表
   */
  public queryFrustum(frustum: THREE.Frustum): Entity[] {
    const result: Entity[] = [];
    this.queryFrustumInNode(this.root, frustum, result);
    return result;
  }

  /**
   * 在节点中查询与视锥体相交的实体
   * @param node 节点
   * @param frustum 视锥体
   * @param result 结果列表
   */
  private queryFrustumInNode(node: OctreeNode, frustum: THREE.Frustum, result: Entity[]): void {
    // 检查视锥体是否与节点相交
    if (!node.intersectsFrustum(frustum)) {
      return;
    }

    // 添加节点中的对象
    node.objects.forEach((data, entity) => {
      // 创建包围球
      const sphere = new THREE.Sphere(data.position, data.radius);

      // 检查包围球是否与视锥体相交
      if (frustum.intersectsSphere(sphere)) {
        result.push(entity);
      }
    });

    // 如果节点有子节点，则递归查询
    if (node.children !== null) {
      for (const child of node.children) {
        this.queryFrustumInNode(child, frustum, result);
      }
    }
  }

  /**
   * 清空八叉树
   */
  public clear(): void {
    this.root.objects.clear();
    this.root.children = null;
    this.entityToNode.clear();
  }

  /**
   * 获取实体数量
   * @returns 实体数量
   */
  public getEntityCount(): number {
    return this.entityToNode.size;
  }

  /**
   * 获取节点数量
   * @returns 节点数量
   */
  public getNodeCount(): number {
    let count = 0;
    this.countNodes(this.root, count);
    return count;
  }

  /**
   * 计算节点数量
   * @param node 节点
   * @param count 计数
   */
  private countNodes(node: OctreeNode, count: number): void {
    count++;

    if (node.children !== null) {
      for (const child of node.children) {
        this.countNodes(child, count);
      }
    }
  }
}
