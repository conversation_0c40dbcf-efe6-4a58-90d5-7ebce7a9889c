/**
 * 时间工具类
 * 提供时间相关的功能
 */
export declare class Time {
    /** 游戏开始时间（毫秒） */
    private static startTime;
    /** 当前时间（秒） */
    private static currentTime;
    /** 帧间隔时间（秒） */
    private static deltaTime;
    /** 固定帧间隔时间（秒） */
    private static fixedDeltaTime;
    /** 时间缩放 */
    private static timeScale;
    /** 帧数 */
    private static frameCount;
    /** 每秒帧数 */
    private static fps;
    /** FPS更新间隔（秒） */
    private static fpsUpdateInterval;
    /** FPS更新计时器 */
    private static fpsUpdateTimer;
    /** FPS计数器 */
    private static fpsCounter;
    /**
     * 初始化时间系统
     */
    static initialize(): void;
    /**
     * 更新时间系统
     * @param deltaTime 帧间隔时间（秒）
     */
    static update(deltaTime: number): void;
    /**
     * 获取游戏运行时间（秒）
     * @returns 游戏运行时间
     */
    static getTime(): number;
    /**
     * 获取游戏开始以来的真实时间（秒）
     * @returns 真实时间
     */
    static getRealTime(): number;
    /**
     * 获取帧间隔时间（秒）
     * @returns 帧间隔时间
     */
    static getDeltaTime(): number;
    /**
     * 获取未缩放的帧间隔时间（秒）
     * @returns 未缩放的帧间隔时间
     */
    static getUnscaledDeltaTime(): number;
    /**
     * 获取固定帧间隔时间（秒）
     * @returns 固定帧间隔时间
     */
    static getFixedDeltaTime(): number;
    /**
     * 设置固定帧间隔时间（秒）
     * @param fixedDeltaTime 固定帧间隔时间
     */
    static setFixedDeltaTime(fixedDeltaTime: number): void;
    /**
     * 获取时间缩放
     * @returns 时间缩放
     */
    static getTimeScale(): number;
    /**
     * 设置时间缩放
     * @param timeScale 时间缩放
     */
    static setTimeScale(timeScale: number): void;
    /**
     * 获取帧数
     * @returns 帧数
     */
    static getFrameCount(): number;
    /**
     * 获取每秒帧数
     * @returns 每秒帧数
     */
    static getFPS(): number;
    /**
     * 获取当前时间戳（毫秒）
     * @returns 当前时间戳
     */
    static now(): number;
}
