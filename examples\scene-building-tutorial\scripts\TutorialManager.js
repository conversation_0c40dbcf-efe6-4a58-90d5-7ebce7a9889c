/**
 * 教程管理器
 * 管理场景构建教程的步骤和交互
 */
export class TutorialManager {
  /**
   * 构造函数
   * @param {SceneBuilder} sceneBuilder 场景构建器
   * @param {TerrainEditor} terrainEditor 地形编辑器
   * @param {ObjectLibrary} objectLibrary 对象库
   */
  constructor(sceneBuilder, terrainEditor, objectLibrary) {
    this.sceneBuilder = sceneBuilder;
    this.terrainEditor = terrainEditor;
    this.objectLibrary = objectLibrary;
    
    this.currentStep = 1;
    this.totalSteps = 9;
    
    // DOM元素
    this.prevButton = document.getElementById('prev-step');
    this.nextButton = document.getElementById('next-step');
    this.stepIndicator = document.getElementById('step-indicator');
    this.stepTitle = document.querySelector('.step-title h2');
    this.stepDescription = document.querySelector('.step-description');
    this.stepActions = document.querySelector('.step-actions');
    
    // 步骤内容
    this.steps = [
      {
        title: '步骤 1: 创建新场景',
        description: `
          <p>在本步骤中，我们将创建一个新的场景，并设置基本参数。</p>
          <ol>
            <li>点击右侧的"创建场景"按钮</li>
            <li>设置场景名称为"山谷村庄"</li>
            <li>设置场景大小为1000x1000</li>
            <li>点击"确认"按钮创建场景</li>
          </ol>
          <div class="tip">
            <strong>提示：</strong> 场景大小应根据项目需求设置，过大的场景可能会影响性能。
          </div>
        `,
        actions: `
          <button id="create-scene-btn" class="action-button">创建场景</button>
        `,
        onEnter: () => {
          document.getElementById('create-scene-btn').addEventListener('click', this.createScene.bind(this));
        },
        onExit: () => {
          document.getElementById('create-scene-btn').removeEventListener('click', this.createScene.bind(this));
        }
      },
      {
        title: '步骤 2: 创建地形',
        description: `
          <p>在本步骤中，我们将创建和编辑地形，设置地形的高度和形状。</p>
          <ol>
            <li>使用右侧的地形工具编辑地形</li>
            <li>创建山丘、山谷和平地</li>
            <li>使用平滑工具使地形过渡自然</li>
            <li>点击"完成地形"按钮进入下一步</li>
          </ol>
          <div class="tip">
            <strong>提示：</strong> 使用鼠标右键拖动可以旋转视图，使用鼠标滚轮可以缩放视图。
          </div>
        `,
        actions: `
          <button id="reset-terrain-btn" class="action-button">重置地形</button>
          <button id="complete-terrain-btn" class="action-button">完成地形</button>
        `,
        onEnter: () => {
          this.terrainEditor.enable();
          document.getElementById('reset-terrain-btn').addEventListener('click', this.resetTerrain.bind(this));
          document.getElementById('complete-terrain-btn').addEventListener('click', this.completeTerrain.bind(this));
        },
        onExit: () => {
          this.terrainEditor.disable();
          document.getElementById('reset-terrain-btn').removeEventListener('click', this.resetTerrain.bind(this));
          document.getElementById('complete-terrain-btn').removeEventListener('click', this.completeTerrain.bind(this));
        }
      },
      {
        title: '步骤 3: 添加水面',
        description: `
          <p>在本步骤中，我们将添加水面，创建河流、湖泊或海洋。</p>
          <ol>
            <li>点击"添加水面"按钮</li>
            <li>调整水面位置和大小</li>
            <li>设置水面材质和效果</li>
            <li>点击"完成水面"按钮进入下一步</li>
          </ol>
          <div class="tip">
            <strong>提示：</strong> 水面应该放置在地形的低洼处，可以使用地形工具创建适合水面的区域。
          </div>
        `,
        actions: `
          <button id="add-water-btn" class="action-button">添加水面</button>
          <button id="complete-water-btn" class="action-button">完成水面</button>
        `,
        onEnter: () => {
          document.getElementById('add-water-btn').addEventListener('click', this.addWater.bind(this));
          document.getElementById('complete-water-btn').addEventListener('click', this.completeWater.bind(this));
        },
        onExit: () => {
          document.getElementById('add-water-btn').removeEventListener('click', this.addWater.bind(this));
          document.getElementById('complete-water-btn').removeEventListener('click', this.completeWater.bind(this));
        }
      },
      // 其他步骤...
    ];
    
    // 绑定事件
    this.prevButton.addEventListener('click', this.prevStep.bind(this));
    this.nextButton.addEventListener('click', this.nextStep.bind(this));
  }
  
  /**
   * 初始化教程
   */
  init() {
    this.updateUI();
    this.enterStep(this.currentStep);
  }
  
  /**
   * 更新UI
   */
  updateUI() {
    this.stepIndicator.textContent = `步骤 ${this.currentStep}/${this.totalSteps}`;
    this.prevButton.disabled = this.currentStep === 1;
    this.nextButton.disabled = this.currentStep === this.totalSteps;
    
    const step = this.steps[this.currentStep - 1];
    this.stepTitle.textContent = step.title;
    this.stepDescription.innerHTML = step.description;
    this.stepActions.innerHTML = step.actions;
  }
  
  /**
   * 进入步骤
   * @param {number} stepNumber 步骤编号
   */
  enterStep(stepNumber) {
    const step = this.steps[stepNumber - 1];
    if (step.onEnter) {
      step.onEnter();
    }
  }
  
  /**
   * 退出步骤
   * @param {number} stepNumber 步骤编号
   */
  exitStep(stepNumber) {
    const step = this.steps[stepNumber - 1];
    if (step.onExit) {
      step.onExit();
    }
  }
  
  /**
   * 上一步
   */
  prevStep() {
    if (this.currentStep > 1) {
      this.exitStep(this.currentStep);
      this.currentStep--;
      this.updateUI();
      this.enterStep(this.currentStep);
    }
  }
  
  /**
   * 下一步
   */
  nextStep() {
    if (this.currentStep < this.totalSteps) {
      this.exitStep(this.currentStep);
      this.currentStep++;
      this.updateUI();
      this.enterStep(this.currentStep);
    }
  }
  
  /**
   * 创建场景
   */
  createScene() {
    this.sceneBuilder.createScene({
      name: '山谷村庄',
      width: 1000,
      height: 1000
    });
    
    // 自动进入下一步
    this.nextStep();
  }
  
  /**
   * 重置地形
   */
  resetTerrain() {
    this.terrainEditor.resetTerrain();
  }
  
  /**
   * 完成地形
   */
  completeTerrain() {
    // 自动进入下一步
    this.nextStep();
  }
  
  /**
   * 添加水面
   */
  addWater() {
    this.sceneBuilder.addWater({
      position: { x: 0, y: 5, z: 0 },
      scale: { x: 500, y: 1, z: 500 },
      color: { r: 0.1, g: 0.3, b: 0.5 },
      transparency: 0.8,
      reflectivity: 0.6,
      waveHeight: 0.2,
      waveSpeed: 0.5
    });
  }
  
  /**
   * 完成水面
   */
  completeWater() {
    // 自动进入下一步
    this.nextStep();
  }
  
  // 其他方法...
}
