/**
 * 地形基准测试示例
 * 用于测试不同地形优化技术的性能效果
 */
import * as THREE from 'three';
/**
 * 地形基准测试示例
 */
export declare class TerrainBenchmarkExample {
    /** 渲染器 */
    private renderer;
    /** 相机 */
    private camera;
    /** 控制器 */
    private controls;
    /** 基准测试 */
    private benchmark;
    /** 容器 */
    private container;
    /** 是否运行中 */
    private running;
    /** 动画帧ID */
    private animationFrameId;
    /** 当前场景 */
    private currentScene;
    /**
     * 构造函数
     * @param container 容器
     */
    constructor(container?: HTMLElement | null);
    /**
     * 注册测试场景
     */
    private registerTestScenes;
    /**
     * 注册优化技术
     */
    private registerOptimizationTechniques;
    /**
     * 添加事件监听器
     */
    private addEventListeners;
    /**
     * 窗口大小变化处理
     */
    private onWindowResize;
    /**
     * 创建基础地形场景
     * @returns 场景
     */
    private createBasicTerrainScene;
    /**
     * 创建复杂地形场景
     * @returns 场景
     */
    private createComplexTerrainScene;
    /**
     * 生成高度图
     * @param geometry 几何体
     * @param heightScale 高度缩放
     */
    private generateHeightMap;
    /**
     * 生成复杂高度图
     * @param geometry 几何体
     * @param heightScale 高度缩放
     * @param detailLevels 细节级别
     */
    private generateComplexHeightMap;
    /**
     * 柏林噪声
     * @param x X坐标
     * @param y Y坐标
     * @returns 噪声值
     */
    private perlinNoise;
    /**
     * 随机梯度
     * @param ix X索引
     * @param iy Y索引
     * @returns 随机值
     */
    private randomGradient;
    /**
     * 线性插值
     * @param a 值A
     * @param b 值B
     * @param t 插值因子
     * @returns 插值结果
     */
    private lerp;
    /**
     * 更新场景
     * @param deltaTime 时间增量
     */
    private updateScene;
    /**
     * 清理场景
     */
    private cleanupScene;
    /**
     * 释放材质
     * @param material 材质
     */
    private disposeMaterial;
    /**
     * 应用几何体压缩
     * @param scene 场景
     * @param renderer 渲染器
     * @param config 配置
     */
    private applyGeometryCompression;
    /**
     * 清理几何体压缩
     * @param scene 场景
     * @param renderer 渲染器
     */
    private cleanupGeometryCompression;
    /**
     * 应用法线贴图优化
     * @param scene 场景
     * @param renderer 渲染器
     * @param config 配置
     */
    private applyNormalMapOptimization;
    /**
     * 生成高度图纹理
     * @param geometry 几何体
     * @returns 高度图纹理
     */
    private generateHeightMapTexture;
    /**
     * 清理法线贴图优化
     * @param scene 场景
     * @param renderer 渲染器
     */
    private cleanupNormalMapOptimization;
    /**
     * 应用虚拟纹理
     * @param scene 场景
     * @param renderer 渲染器
     * @param config 配置
     */
    private applyVirtualTexturing;
    /**
     * 清理虚拟纹理
     * @param scene 场景
     * @param renderer 渲染器
     */
    private cleanupVirtualTexturing;
    /**
     * 应用组合优化
     * @param scene 场景
     * @param renderer 渲染器
     * @param config 配置
     */
    private applyCombinedOptimization;
    /**
     * 清理组合优化
     * @param scene 场景
     * @param renderer 渲染器
     */
    private cleanupCombinedOptimization;
    /**
     * 启动基准测试
     */
    start(): void;
    /**
     * 停止基准测试
     */
    stop(): void;
    /**
     * 启动渲染循环
     */
    private startRenderLoop;
    /**
     * 运行单个测试
     * @param sceneId 场景ID
     * @param techniqueId 技术ID
     */
    runTest(sceneId: string, techniqueId: string): void;
    /**
     * 运行所有测试
     */
    runAllTests(): void;
    /**
     * 获取当前场景
     * @returns 当前场景
     */
    getCurrentScene(): THREE.Scene | null;
    /**
     * 设置当前场景
     * @param scene 场景
     */
    setCurrentScene(scene: THREE.Scene): void;
}
