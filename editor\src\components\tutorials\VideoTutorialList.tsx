/**
 * 视频教程列表组件
 * 显示可用的视频教程
 */
import React, { useState, useEffect } from 'react';
import { Card, Tag, Button, Typography, Space, Modal, Tabs, Input, Empty } from 'antd';
import {
  PlayCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import VideoTutorialService, { VideoTutorial } from '../../services/VideoTutorialService';
import VideoTutorialPlayer from './VideoTutorialPlayer';
import './VideoTutorialList.less';

const { Title, Text, Paragraph } = Typography;
const { Meta } = Card;

interface VideoTutorialListProps {
  onTutorialSelect?: (tutorialId: string) => void;
}

const VideoTutorialList: React.FC<VideoTutorialListProps> = ({ onTutorialSelect }) => {
  const { t } = useTranslation();
  const [tutorials, setTutorials] = useState<VideoTutorial[]>([]);
  const [recommendedTutorials, setRecommendedTutorials] = useState<VideoTutorial[]>([]);
  const [selectedTutorial, setSelectedTutorial] = useState<VideoTutorial | null>(null);
  const [playerVisible, setPlayerVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTutorials, setFilteredTutorials] = useState<VideoTutorial[]>([]);

  // 初始化
  useEffect(() => {
    // 获取所有视频教程
    const allTutorials = VideoTutorialService.getTutorials();
    setTutorials(allTutorials);
    setFilteredTutorials(allTutorials);

    // 获取推荐视频教程
    const recommended = VideoTutorialService.getRecommendedTutorials();
    setRecommendedTutorials(recommended);

    // 监听视频教程事件
    VideoTutorialService.on('tutorialWatched', handleTutorialWatched);

    return () => {
      // 清理事件监听
      VideoTutorialService.off('tutorialWatched', handleTutorialWatched);
    };
  }, []);

  // 处理视频教程观看完成
  const handleTutorialWatched = () => {
    // 更新推荐视频教程
    const recommended = VideoTutorialService.getRecommendedTutorials();
    setRecommendedTutorials(recommended);
  };

  // 处理视频教程选择
  const handleTutorialSelect = (tutorial: VideoTutorial) => {
    setSelectedTutorial(tutorial);

    // 如果提供了选择回调，则调用它
    if (onTutorialSelect) {
      onTutorialSelect(tutorial.id);
    } else {
      // 否则显示内置播放器
      setPlayerVisible(true);
    }
  };

  // 处理播放器关闭
  const handlePlayerClose = () => {
    setPlayerVisible(false);
    setSelectedTutorial(null);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchQuery(value);

    if (!value.trim()) {
      setFilteredTutorials(tutorials);
      return;
    }

    const filtered = tutorials.filter(tutorial =>
      tutorial.title.toLowerCase().includes(value.toLowerCase()) ||
      tutorial.description.toLowerCase().includes(value.toLowerCase()) ||
      tutorial.tags?.some(tag => tag.toLowerCase().includes(value.toLowerCase()))
    );

    setFilteredTutorials(filtered);
    setActiveTab('search');
  };

  // 渲染视频教程卡片
  const renderTutorialCard = (tutorial: VideoTutorial) => {
    const isWatched = VideoTutorialService.isTutorialWatched(tutorial.id);
    const progress = VideoTutorialService.getTutorialProgress(tutorial.id);
    const progressPercent = tutorial.duration > 0 ? Math.round((progress / (tutorial.duration * 60)) * 100) : 0;

    return (
      <Card
        className="tutorial-card"
        cover={
          <div className="tutorial-thumbnail">
            <img alt={tutorial.title} src={tutorial.thumbnailUrl} />
            {progressPercent > 0 && progressPercent < 100 && (
              <div className="progress-bar">
                <div className="progress" style={{ width: `${progressPercent}%` }} />
              </div>
            )}
            <Button
              type="primary"
              shape="circle"
              icon={<PlayCircleOutlined />}
              size="large"
              className="play-button"
              onClick={() => handleTutorialSelect(tutorial)}
            />
          </div>
        }
        actions={[
          <Button
            type="link"
            icon={<PlayCircleOutlined />}
            onClick={() => handleTutorialSelect(tutorial)}
          >
            {isWatched ? t('videoTutorials.rewatch') : (progressPercent > 0 ? t('videoTutorials.continue') : t('videoTutorials.watch'))}
          </Button>
        ]}
      >
        <Meta
          title={
            <div className="tutorial-title">
              <Text ellipsis>{tutorial.title}</Text>
              {isWatched && (
                <CheckCircleOutlined className="watched-icon" />
              )}
            </div>
          }
          description={
            <div className="tutorial-meta">
              <Space direction="vertical" size={4}>
                <div className="tutorial-tags">
                  <Tag color="blue">{t(`videoTutorials.categories.${tutorial.category}`)}</Tag>
                  <Tag color={tutorial.difficulty === 'beginner' ? 'green' : (tutorial.difficulty === 'intermediate' ? 'orange' : 'red')}>
                    {t(`videoTutorials.difficulty.${tutorial.difficulty}`)}
                  </Tag>
                </div>
                <div className="tutorial-duration">
                  <ClockCircleOutlined /> {tutorial.duration} {t('videoTutorials.minutes')}
                </div>
                <Paragraph ellipsis={{ rows: 2 }}>{tutorial.description}</Paragraph>
              </Space>
            </div>
          }
        />
      </Card>
    );
  };

  return (
    <div className="video-tutorial-list">
      <div className="tutorial-list-header">
        <Title level={3}>{t('videoTutorials.title')}</Title>
        <div className="search-bar">
          <Input
            placeholder={t('videoTutorials.searchPlaceholder') as string}
            prefix={<SearchOutlined />}
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            allowClear
          />
        </div>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'all',
            label: t('videoTutorials.allTutorials'),
            children: (
              <div className="tutorial-grid">
                {filteredTutorials.map(tutorial => (
                  <div key={tutorial.id} className="tutorial-grid-item">
                    {renderTutorialCard(tutorial)}
                  </div>
                ))}
              </div>
            )
          },
          {
            key: 'recommended',
            label: t('videoTutorials.recommended'),
            children: recommendedTutorials.length > 0 ? (
              <div className="tutorial-grid">
                {recommendedTutorials.map(tutorial => (
                  <div key={tutorial.id} className="tutorial-grid-item">
                    {renderTutorialCard(tutorial)}
                  </div>
                ))}
              </div>
            ) : (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={t('videoTutorials.noRecommendations')}
              />
            )
          },
          {
            key: 'watched',
            label: t('videoTutorials.watched'),
            children: (
              <div className="tutorial-grid">
                {filteredTutorials
                  .filter(tutorial => VideoTutorialService.isTutorialWatched(tutorial.id))
                  .map(tutorial => (
                    <div key={tutorial.id} className="tutorial-grid-item">
                      {renderTutorialCard(tutorial)}
                    </div>
                  ))}
              </div>
            )
          },
          {
            key: 'search',
            label: t('videoTutorials.search'),
            children: activeTab === 'search' && searchQuery ? (
              <div className="search-results">
                <Text>{t('videoTutorials.searchResults', { count: filteredTutorials.length, query: searchQuery })}</Text>
                {filteredTutorials.length > 0 ? (
                  <div className="tutorial-grid">
                    {filteredTutorials.map(tutorial => (
                      <div key={tutorial.id} className="tutorial-grid-item">
                        {renderTutorialCard(tutorial)}
                      </div>
                    ))}
                  </div>
                ) : (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={t('videoTutorials.noSearchResults')}
                  />
                )}
              </div>
            ) : null
          }
        ]}
      />

      <Modal
        title={null}
        open={playerVisible}
        onCancel={handlePlayerClose}
        footer={null}
        width="80%"
        styles={{ body: { padding: 0, height: '80vh', overflow: 'hidden' } }}
        destroyOnClose={true}
      >
        {selectedTutorial && (
          <VideoTutorialPlayer
            tutorialId={selectedTutorial.id}
            onClose={handlePlayerClose}
          />
        )}
      </Modal>
    </div>
  );
};

export default VideoTutorialList;
