-- 创建各个微服务的数据库
-- 数字人系统MySQL数据库初始化脚本

-- 创建API网关数据库
CREATE DATABASE IF NOT EXISTS `api_gateway` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户服务数据库
CREATE DATABASE IF NOT EXISTS `ir_engine_users` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建项目服务数据库
CREATE DATABASE IF NOT EXISTS `ir_engine_projects` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建资产服务数据库
CREATE DATABASE IF NOT EXISTS `ir_engine_assets` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建渲染服务数据库
CREATE DATABASE IF NOT EXISTS `ir_engine_render` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建AI模型服务数据库
CREATE DATABASE IF NOT EXISTS `ai_model_service` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建服务注册中心数据库
CREATE DATABASE IF NOT EXISTS `ir_engine_registry` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建监控服务数据库
CREATE DATABASE IF NOT EXISTS `monitoring` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER IF NOT EXISTS 'dlengine'@'%' IDENTIFIED BY 'dlengine123';

-- 授权所有数据库权限
GRANT ALL PRIVILEGES ON `api_gateway`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `ir_engine_users`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `ir_engine_projects`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `ir_engine_assets`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `ir_engine_render`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `ai_model_service`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `ir_engine_registry`.* TO 'dlengine'@'%';
GRANT ALL PRIVILEGES ON `monitoring`.* TO 'dlengine'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示创建的数据库
SHOW DATABASES;
