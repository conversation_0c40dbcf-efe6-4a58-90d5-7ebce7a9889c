# 数据可视化示例项目

## 简介

本示例项目展示了如何使用DL（Digital Learning）引擎进行数据可视化，包括3D柱状图、散点图、热力图和地理数据可视化等。通过本示例，您可以了解如何将数据转换为直观的3D可视化表示，并添加交互功能以增强用户体验。

## 功能特性

- **3D柱状图**：将数据以3D柱状图形式展示，支持多维数据比较
- **3D散点图**：以3D空间中的点展示多维数据关系
- **热力图**：使用颜色渐变展示数据密度和分布
- **地理数据可视化**：在3D地图上展示地理相关数据
- **时间序列动画**：展示数据随时间变化的动态效果
- **交互式数据探索**：支持缩放、旋转、筛选和查询数据
- **数据导入导出**：支持从CSV、JSON等格式导入数据
- **自定义样式**：支持自定义可视化的颜色、大小、形状等属性

## 使用说明

1. 打开示例项目
2. 选择数据可视化类型（柱状图、散点图、热力图或地理数据）
3. 导入示例数据或使用内置数据集
4. 调整可视化参数（颜色、大小、比例等）
5. 使用交互工具探索数据（旋转、缩放、筛选等）
6. 尝试修改数据并观察可视化效果的变化
7. 导出可视化结果（图片或场景文件）

## 技术要点

### 数据处理

本示例使用以下方法处理和转换数据：

```javascript
// 数据归一化
function normalizeData(data, min, max) {
  const dataMin = Math.min(...data);
  const dataMax = Math.max(...data);
  return data.map(value => 
    min + (max - min) * (value - dataMin) / (dataMax - dataMin)
  );
}

// 数据分类
function categorizeData(data, categories) {
  return data.map(value => {
    for (let i = 0; i < categories.length; i++) {
      if (value <= categories[i].threshold) {
        return categories[i].category;
      }
    }
    return categories[categories.length - 1].category;
  });
}
```

### 3D可视化实现

本示例使用Three.js和DL（Digital Learning）引擎的渲染系统创建3D可视化：

```javascript
// 创建3D柱状图
function createBarChart(data, options) {
  const { width, height, depth, colors } = options;
  const group = new THREE.Group();
  
  // 计算柱子宽度和间距
  const barWidth = width / data.length;
  const spacing = barWidth * 0.2;
  
  // 创建每个柱子
  data.forEach((value, index) => {
    const barHeight = value * height;
    const geometry = new THREE.BoxGeometry(
      barWidth - spacing, 
      barHeight, 
      depth - spacing
    );
    const material = new THREE.MeshStandardMaterial({
      color: getColorForValue(value, colors),
      roughness: 0.7,
      metalness: 0.2
    });
    const bar = new THREE.Mesh(geometry, material);
    
    // 设置柱子位置
    bar.position.x = index * barWidth - width / 2 + barWidth / 2;
    bar.position.y = barHeight / 2;
    bar.position.z = 0;
    
    // 添加到组
    group.add(bar);
  });
  
  return group;
}
```

### 交互功能

本示例实现了以下交互功能：

```javascript
// 添加选择交互
function addSelectionInteraction(object, data, callback) {
  object.userData.selectable = true;
  object.userData.data = data;
  object.userData.onSelect = callback;
}

// 添加悬停交互
function addHoverInteraction(object, data, hoverCallback, leaveCallback) {
  object.userData.hoverable = true;
  object.userData.data = data;
  object.userData.onHover = hoverCallback;
  object.userData.onLeave = leaveCallback;
}
```

## 学习要点

- 了解如何将数据转换为3D可视化表示
- 掌握DL（Digital Learning）引擎的渲染系统和材质系统
- 学习如何添加交互功能以增强用户体验
- 理解数据处理和归一化的方法
- 掌握动态更新可视化的技术

## 扩展建议

- **添加更多可视化类型**：如3D饼图、网络图、树状图等
- **实现实时数据更新**：连接到实时数据源，动态更新可视化
- **添加高级交互功能**：如数据钻取、多维筛选等
- **集成机器学习功能**：展示数据聚类、分类或预测结果
- **添加VR/AR支持**：在VR/AR环境中探索数据可视化
- **实现协作数据分析**：支持多用户同时分析和讨论数据

## 相关资源

- [DL（Digital Learning）引擎数据可视化文档](../../docs/data-visualization/README.md)
- [Three.js文档](https://threejs.org/docs/)
- [D3.js文档](https://d3js.org/getting-started)
- [数据可视化最佳实践](../../docs/best-practices/data-visualization.md)

## 常见问题

### 如何导入自己的数据？

您可以使用导入功能加载CSV或JSON格式的数据。点击界面上的"导入数据"按钮，然后选择您的数据文件。确保数据格式符合要求，或者使用数据转换工具进行预处理。

### 如何自定义可视化样式？

在可视化参数面板中，您可以调整颜色、大小、比例等参数。对于更高级的自定义，您可以修改材质属性或添加自定义着色器。

### 如何处理大量数据？

对于大量数据，建议使用数据采样或聚合技术减少数据点数量，或者使用实例化渲染和LOD技术优化性能。

### 如何导出可视化结果？

点击界面上的"导出"按钮，选择导出格式（PNG、JPEG或场景文件）。您也可以使用屏幕捕获工具记录交互过程。
