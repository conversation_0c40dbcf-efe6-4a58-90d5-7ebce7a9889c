import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import compression from 'compression';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { AppModule } from './app.module';
import { LoggerService } from './common/services/logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // 获取配置服务
  const configService = app.get(ConfigService);
  const logger = app.get(LoggerService);
  
  // 全局中间件
  app.use(helmet());
  app.use(compression());
  
  // 限流配置
  app.use(
    rateLimit({
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 1000, // 限制每个IP 15分钟内最多1000个请求
      message: '请求过于频繁，请稍后再试',
    }),
  );
  
  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  
  // CORS配置
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  });
  
  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('资源库微服务 API')
    .setDescription('3D资产管理、分类、搜索、版本控制和分布式存储服务')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('assets', '资产管理')
    .addTag('categories', '分类管理')
    .addTag('tags', '标签管理')
    .addTag('versions', '版本控制')
    .addTag('search', '搜索服务')
    .addTag('upload', '上传服务')
    .build();
    
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  
  // 启动服务
  const port = configService.get('PORT', 3005);
  await app.listen(port);
  
  logger.log(`资源库微服务启动成功，端口: ${port}`);
  logger.log(`Swagger文档地址: http://localhost:${port}/api/docs`);
}

bootstrap().catch((error) => {
  console.error('服务启动失败:', error);
  process.exit(1);
});
