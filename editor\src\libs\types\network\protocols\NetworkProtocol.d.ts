/**
 * 网络协议抽象类
 * 定义网络协议的通用接口
 */
import { EventEmitter } from '../../utils/EventEmitter';
/**
 * 网络协议类型
 */
export declare enum NetworkProtocolType {
    /** WebSocket协议 */
    WEBSOCKET = "websocket",
    /** WebRTC数据通道 */
    WEBRTC = "webrtc",
    /** UDP协议 */
    UDP = "udp",
    /** TCP协议 */
    TCP = "tcp",
    /** HTTP长轮询 */
    HTTP_LONG_POLLING = "http_long_polling",
    /** Server-Sent Events */
    SSE = "sse",
    /** 自定义协议 */
    CUSTOM = "custom"
}
/**
 * 网络协议状态
 */
export declare enum NetworkProtocolState {
    /** 已断开连接 */
    DISCONNECTED = "disconnected",
    /** 正在连接 */
    CONNECTING = "connecting",
    /** 已连接 */
    CONNECTED = "connected",
    /** 正在断开连接 */
    DISCONNECTING = "disconnecting",
    /** 连接错误 */
    ERROR = "error"
}
/**
 * 网络协议配置选项
 */
export interface NetworkProtocolOptions {
    /** 自动重连 */
    autoReconnect?: boolean;
    /** 最大重连次数 */
    maxReconnectAttempts?: number;
    /** 重连间隔（毫秒） */
    reconnectInterval?: number;
    /** 心跳间隔（毫秒） */
    heartbeatInterval?: number;
    /** 心跳超时（毫秒） */
    heartbeatTimeout?: number;
    /** 连接超时（毫秒） */
    connectionTimeout?: number;
    /** 是否启用压缩 */
    enableCompression?: boolean;
    /** 是否启用加密 */
    enableEncryption?: boolean;
    /** 加密密钥 */
    encryptionKey?: string;
    /** 是否启用调试日志 */
    enableDebugLog?: boolean;
    /** 自定义协议选项 */
    [key: string]: any;
}
/**
 * 网络协议统计信息
 */
export interface NetworkProtocolStats {
    /** 发送的字节数 */
    bytesSent: number;
    /** 接收的字节数 */
    bytesReceived: number;
    /** 发送的消息数 */
    messagesSent: number;
    /** 接收的消息数 */
    messagesReceived: number;
    /** 丢失的消息数 */
    messagesLost: number;
    /** 平均往返时间（毫秒） */
    averageRtt: number;
    /** 最小往返时间（毫秒） */
    minRtt: number;
    /** 最大往返时间（毫秒） */
    maxRtt: number;
    /** 连接时间（毫秒） */
    connectionTime: number;
    /** 连接状态 */
    state: NetworkProtocolState;
    /** 重连次数 */
    reconnectCount: number;
    /** 上次活动时间 */
    lastActivityTime: number;
}
/**
 * 网络协议抽象类
 * 定义网络协议的通用接口
 */
export declare abstract class NetworkProtocol extends EventEmitter {
    /** 协议类型 */
    protected type: NetworkProtocolType;
    /** 协议状态 */
    protected state: NetworkProtocolState;
    /** 配置选项 */
    protected options: NetworkProtocolOptions;
    /** 统计信息 */
    protected stats: NetworkProtocolStats;
    /** 重连尝试次数 */
    protected reconnectAttempts: number;
    /** 重连定时器ID */
    protected reconnectTimerId: number | null;
    /** 心跳定时器ID */
    protected heartbeatTimerId: number | null;
    /** 连接超时定时器ID */
    protected connectionTimeoutTimerId: number | null;
    /**
     * 创建网络协议
     * @param type 协议类型
     * @param options 配置选项
     */
    constructor(type: NetworkProtocolType, options?: NetworkProtocolOptions);
    /**
     * 连接到服务器
     * @param url 服务器URL
     * @returns 连接Promise
     */
    abstract connect(url: string): Promise<void>;
    /**
     * 断开连接
     * @returns 断开连接Promise
     */
    abstract disconnect(): Promise<void>;
    /**
     * 发送数据
     * @param data 要发送的数据
     * @returns 是否成功发送
     */
    abstract send(data: any): boolean;
    /**
     * 获取协议类型
     * @returns 协议类型
     */
    getType(): NetworkProtocolType;
    /**
     * 获取协议状态
     * @returns 协议状态
     */
    getState(): NetworkProtocolState;
    /**
     * 获取统计信息
     * @returns 统计信息
     */
    getStats(): NetworkProtocolStats;
    /**
     * 重置统计信息
     */
    resetStats(): void;
    /**
     * 是否已连接
     * @returns 是否已连接
     */
    isConnected(): boolean;
    /**
     * 是否正在连接
     * @returns 是否正在连接
     */
    isConnecting(): boolean;
    /**
     * 是否已断开连接
     * @returns 是否已断开连接
     */
    isDisconnected(): boolean;
    /**
     * 设置状态
     * @param state 协议状态
     */
    protected setState(state: NetworkProtocolState): void;
    /**
     * 更新统计信息
     * @param stats 部分统计信息
     */
    protected updateStats(stats: Partial<NetworkProtocolStats>): void;
    /**
     * 记录发送的数据
     * @param data 发送的数据
     */
    protected recordSentData(data: any): void;
    /**
     * 记录接收的数据
     * @param data 接收的数据
     */
    protected recordReceivedData(data: any): void;
    /**
     * 获取数据大小
     * @param data 数据
     * @returns 数据大小（字节）
     */
    protected getDataSize(data: any): number;
    /**
     * 启动心跳
     */
    protected startHeartbeat(): void;
    /**
     * 停止心跳
     */
    protected stopHeartbeat(): void;
    /**
     * 发送心跳
     */
    protected sendHeartbeat(): void;
    /**
     * 尝试重连
     */
    protected attemptReconnect(): void;
    /**
     * 取消重连
     */
    protected cancelReconnect(): void;
    /**
     * 设置连接超时
     */
    protected setConnectionTimeout(): void;
    /**
     * 取消连接超时
     */
    protected cancelConnectionTimeout(): void;
    /**
     * 销毁协议
     */
    dispose(): void;
}
