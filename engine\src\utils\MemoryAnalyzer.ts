/**
 * 内存分析器
 * 用于跟踪和分析内存使用情况
 */
import { EventEmitter } from './EventEmitter';
import { Debug } from './Debug';

/**
 * 资源类型枚举
 */
export enum ResourceType {
  TEXTURE = 'texture',
  GEOMETRY = 'geometry',
  MATERIAL = 'material',
  SHADER = 'shader',
  AUDIO = 'audio',
  MODEL = 'model',
  ANIMATION = 'animation',
  OTHER = 'other'
}

/**
 * 资源信息接口
 */
export interface ResourceInfo {
  /** 资源ID */
  id: string;
  /** 资源名称 */
  name: string;
  /** 资源类型 */
  type: ResourceType;
  /** 资源大小（字节） */
  size: number;
  /** 引用计数 */
  refCount: number;
  /** 创建时间 */
  createdAt: number;
  /** 最后访问时间 */
  lastAccessTime: number;
  /** 是否已释放 */
  disposed: boolean;
  /** 所有者/创建者 */
  owner?: string;
  /** 额外元数据 */
  metadata?: Record<string, any>;
}

/**
 * 内存快照接口
 */
export interface MemorySnapshot {
  /** 快照ID */
  id: string;
  /** 快照时间 */
  timestamp: number;
  /** 总内存使用量（字节） */
  totalMemory: number;
  /** JS堆内存使用量（字节） */
  jsHeapMemory: number;
  /** 资源内存使用量（字节） */
  resourceMemory: number;
  /** 按类型分类的内存使用量 */
  memoryByType: Record<ResourceType, number>;
  /** 资源列表 */
  resources: ResourceInfo[];
}

/**
 * 内存分析器配置接口
 */
export interface MemoryAnalyzerConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用自动内存快照 */
  enableAutoSnapshot?: boolean;
  /** 自动快照间隔（毫秒） */
  autoSnapshotInterval?: number;
  /** 是否启用内存泄漏检测 */
  enableLeakDetection?: boolean;
  /** 泄漏检测阈值（毫秒） */
  leakDetectionThreshold?: number;
  /** 是否启用调试输出 */
  debug?: boolean;
  /** 是否收集详细资源信息 */
  collectDetailedInfo?: boolean;
  /** 是否启用警告 */
  enableWarnings?: boolean;
}

/**
 * 内存分析器类
 */
export class MemoryAnalyzer {
  private static instance: MemoryAnalyzer;
  
  /** 配置 */
  private config: MemoryAnalyzerConfig = {
    enabled: false,
    enableAutoSnapshot: false,
    autoSnapshotInterval: 60000, // 1分钟
    enableLeakDetection: true,
    leakDetectionThreshold: 300000, // 5分钟
    debug: false,
    collectDetailedInfo: true,
    enableWarnings: true
  };
  
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  
  /** 资源映射 */
  private resources: Map<string, ResourceInfo> = new Map();
  
  /** 内存快照列表 */
  private snapshots: MemorySnapshot[] = [];
  
  /** 自动快照定时器ID */
  private autoSnapshotTimerId: number | null = null;
  
  /** 内存泄漏检测定时器ID */
  private leakDetectionTimerId: number | null = null;
  
  /** 是否正在运行 */
  private running: boolean = false;
  
  /**
   * 获取单例实例
   */
  public static getInstance(): MemoryAnalyzer {
    if (!MemoryAnalyzer.instance) {
      MemoryAnalyzer.instance = new MemoryAnalyzer();
    }
    return MemoryAnalyzer.instance;
  }
  
  /**
   * 私有构造函数
   */
  private constructor() {
    // 初始化
  }
  
  /**
   * 配置内存分析器
   * @param config 配置
   */
  public configure(config: MemoryAnalyzerConfig): void {
    this.config = {
      ...this.config,
      ...config
    };
    
    if (this.config.debug) {
      Debug.log('内存分析器', '配置已更新', this.config);
    }
    
    // 如果已启动，则应用新配置
    if (this.running) {
      this.stop();
      this.start();
    }
  }
  
  /**
   * 启动内存分析器
   */
  public start(): void {
    if (!this.config.enabled || this.running) {
      return;
    }
    
    this.running = true;
    
    // 启动自动快照
    if (this.config.enableAutoSnapshot) {
      this.startAutoSnapshot();
    }
    
    // 启动内存泄漏检测
    if (this.config.enableLeakDetection) {
      this.startLeakDetection();
    }
    
    if (this.config.debug) {
      Debug.log('内存分析器', '已启动');
    }
    
    this.eventEmitter.emit('start');
  }
  
  /**
   * 停止内存分析器
   */
  public stop(): void {
    if (!this.running) {
      return;
    }
    
    this.running = false;
    
    // 停止自动快照
    if (this.autoSnapshotTimerId !== null) {
      clearInterval(this.autoSnapshotTimerId);
      this.autoSnapshotTimerId = null;
    }
    
    // 停止内存泄漏检测
    if (this.leakDetectionTimerId !== null) {
      clearInterval(this.leakDetectionTimerId);
      this.leakDetectionTimerId = null;
    }
    
    if (this.config.debug) {
      Debug.log('内存分析器', '已停止');
    }
    
    this.eventEmitter.emit('stop');
  }
  
  /**
   * 启动自动快照
   */
  private startAutoSnapshot(): void {
    if (this.autoSnapshotTimerId !== null) {
      clearInterval(this.autoSnapshotTimerId);
    }
    
    this.autoSnapshotTimerId = window.setInterval(() => {
      this.takeSnapshot();
    }, this.config.autoSnapshotInterval);
    
    if (this.config.debug) {
      Debug.log('内存分析器', '自动快照已启动');
    }
  }
  
  /**
   * 启动内存泄漏检测
   */
  private startLeakDetection(): void {
    if (this.leakDetectionTimerId !== null) {
      clearInterval(this.leakDetectionTimerId);
    }
    
    this.leakDetectionTimerId = window.setInterval(() => {
      this.detectLeaks();
    }, 30000); // 每30秒检测一次
    
    if (this.config.debug) {
      Debug.log('内存分析器', '内存泄漏检测已启动');
    }
  }
  
  /**
   * 注册资源
   * @param resource 资源信息
   */
  public registerResource(resource: Omit<ResourceInfo, 'createdAt' | 'lastAccessTime' | 'disposed'>): void {
    if (!this.running) {
      return;
    }
    
    const now = Date.now();
    const resourceInfo: ResourceInfo = {
      ...resource,
      createdAt: now,
      lastAccessTime: now,
      disposed: false
    };
    
    this.resources.set(resource.id, resourceInfo);
    
    if (this.config.debug) {
      Debug.log('内存分析器', `资源已注册: ${resource.id} (${resource.type}, ${this.formatBytes(resource.size)})`);
    }
    
    this.eventEmitter.emit('resourceRegistered', resourceInfo);
  }
  
  /**
   * 更新资源
   * @param id 资源ID
   * @param updates 更新内容
   */
  public updateResource(id: string, updates: Partial<ResourceInfo>): void {
    if (!this.running || !this.resources.has(id)) {
      return;
    }
    
    const resource = this.resources.get(id)!;
    const updatedResource = {
      ...resource,
      ...updates,
      lastAccessTime: Date.now()
    };
    
    this.resources.set(id, updatedResource);
    
    if (this.config.debug) {
      Debug.log('内存分析器', `资源已更新: ${id}`);
    }
    
    this.eventEmitter.emit('resourceUpdated', updatedResource);
  }
  
  /**
   * 释放资源
   * @param id 资源ID
   */
  public disposeResource(id: string): void {
    if (!this.running || !this.resources.has(id)) {
      return;
    }
    
    const resource = this.resources.get(id)!;
    resource.disposed = true;
    resource.lastAccessTime = Date.now();
    
    if (this.config.debug) {
      Debug.log('内存分析器', `资源已释放: ${id}`);
    }
    
    this.eventEmitter.emit('resourceDisposed', resource);
  }
  
  /**
   * 获取资源
   * @param id 资源ID
   */
  public getResource(id: string): ResourceInfo | undefined {
    const resource = this.resources.get(id);
    
    if (resource) {
      resource.lastAccessTime = Date.now();
    }
    
    return resource;
  }
  
  /**
   * 获取所有资源
   */
  public getAllResources(): ResourceInfo[] {
    return Array.from(this.resources.values());
  }
  
  /**
   * 获取活跃资源（未释放的）
   */
  public getActiveResources(): ResourceInfo[] {
    return Array.from(this.resources.values()).filter(resource => !resource.disposed);
  }
  
  /**
   * 获取按类型分组的资源
   */
  public getResourcesByType(): Record<ResourceType, ResourceInfo[]> {
    const result: Record<ResourceType, ResourceInfo[]> = {} as any;
    
    // 初始化所有类型的空数组
    Object.values(ResourceType).forEach(type => {
      result[type] = [];
    });
    
    // 按类型分组资源
    this.getActiveResources().forEach(resource => {
      result[resource.type].push(resource);
    });
    
    return result;
  }
  
  /**
   * 获取按类型分组的内存使用量
   */
  public getMemoryByType(): Record<ResourceType, number> {
    const result: Record<ResourceType, number> = {} as any;
    
    // 初始化所有类型的内存使用量为0
    Object.values(ResourceType).forEach(type => {
      result[type] = 0;
    });
    
    // 计算每种类型的内存使用量
    this.getActiveResources().forEach(resource => {
      result[resource.type] += resource.size;
    });
    
    return result;
  }
  
  /**
   * 获取总内存使用量
   */
  public getTotalMemory(): number {
    return this.getActiveResources().reduce((total, resource) => total + resource.size, 0);
  }
  
  /**
   * 获取JS堆内存使用量
   */
  public getJsHeapMemory(): number {
    if (typeof window !== 'undefined' && window.performance && (window.performance as any).memory) {
      return (window.performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }
  
  /**
   * 获取当前内存使用情况
   */
  public getMemoryUsage(): {
    total: number;
    jsHeap: number;
    resources: number;
    byType: Record<ResourceType, number>;
  } {
    const jsHeap = this.getJsHeapMemory();
    const resources = this.getTotalMemory();
    const byType = this.getMemoryByType();
    
    return {
      total: jsHeap,
      jsHeap,
      resources,
      byType
    };
  }
  
  /**
   * 拍摄内存快照
   */
  public takeSnapshot(): MemorySnapshot {
    const now = Date.now();
    const jsHeapMemory = this.getJsHeapMemory();
    const resourceMemory = this.getTotalMemory();
    const memoryByType = this.getMemoryByType();
    const resources = this.config.collectDetailedInfo ? this.getActiveResources() : [];
    
    const snapshot: MemorySnapshot = {
      id: `snapshot-${now}`,
      timestamp: now,
      totalMemory: jsHeapMemory,
      jsHeapMemory,
      resourceMemory,
      memoryByType,
      resources
    };
    
    this.snapshots.push(snapshot);
    
    // 限制快照数量，保留最新的100个
    if (this.snapshots.length > 100) {
      this.snapshots.shift();
    }
    
    if (this.config.debug) {
      Debug.log('内存分析器', `已创建内存快照: ${snapshot.id}`);
    }
    
    this.eventEmitter.emit('snapshotTaken', snapshot);
    
    return snapshot;
  }
  
  /**
   * 获取所有快照
   */
  public getSnapshots(): MemorySnapshot[] {
    return [...this.snapshots];
  }
  
  /**
   * 获取最新快照
   */
  public getLatestSnapshot(): MemorySnapshot | undefined {
    if (this.snapshots.length === 0) {
      return undefined;
    }
    return this.snapshots[this.snapshots.length - 1];
  }
  
  /**
   * 比较两个快照
   * @param snapshotId1 快照1 ID
   * @param snapshotId2 快照2 ID
   */
  public compareSnapshots(snapshotId1: string, snapshotId2: string): {
    totalDiff: number;
    jsHeapDiff: number;
    resourceDiff: number;
    typeDiffs: Record<ResourceType, number>;
    newResources: ResourceInfo[];
    disposedResources: ResourceInfo[];
  } {
    const snapshot1 = this.snapshots.find(s => s.id === snapshotId1);
    const snapshot2 = this.snapshots.find(s => s.id === snapshotId2);
    
    if (!snapshot1 || !snapshot2) {
      throw new Error('快照不存在');
    }
    
    // 计算差异
    const totalDiff = snapshot2.totalMemory - snapshot1.totalMemory;
    const jsHeapDiff = snapshot2.jsHeapMemory - snapshot1.jsHeapMemory;
    const resourceDiff = snapshot2.resourceMemory - snapshot1.resourceMemory;
    
    // 计算各类型内存差异
    const typeDiffs: Record<ResourceType, number> = {} as any;
    Object.values(ResourceType).forEach(type => {
      typeDiffs[type] = snapshot2.memoryByType[type] - snapshot1.memoryByType[type];
    });
    
    // 找出新增和释放的资源
    const resourceIds1 = new Set(snapshot1.resources.map(r => r.id));
    const resourceIds2 = new Set(snapshot2.resources.map(r => r.id));
    
    const newResources = snapshot2.resources.filter(r => !resourceIds1.has(r.id));
    const disposedResources = snapshot1.resources.filter(r => !resourceIds2.has(r.id));
    
    return {
      totalDiff,
      jsHeapDiff,
      resourceDiff,
      typeDiffs,
      newResources,
      disposedResources
    };
  }
  
  /**
   * 检测内存泄漏
   */
  public detectLeaks(): {
    potentialLeaks: ResourceInfo[];
    totalLeakedMemory: number;
  } {
    if (!this.running) {
      return { potentialLeaks: [], totalLeakedMemory: 0 };
    }
    
    const now = Date.now();
    const threshold = this.config.leakDetectionThreshold || 300000; // 默认5分钟
    
    // 查找长时间未访问但未释放的资源
    const potentialLeaks = this.getActiveResources().filter(resource => {
      return now - resource.lastAccessTime > threshold;
    });
    
    // 计算泄漏的总内存
    const totalLeakedMemory = potentialLeaks.reduce((total, resource) => total + resource.size, 0);
    
    if (potentialLeaks.length > 0 && this.config.enableWarnings) {
      Debug.warn('内存分析器', `检测到潜在内存泄漏: ${potentialLeaks.length}个资源, ${this.formatBytes(totalLeakedMemory)}`);
      
      if (this.config.debug) {
        potentialLeaks.forEach(leak => {
          Debug.log('内存分析器', `  - ${leak.id} (${leak.type}, ${this.formatBytes(leak.size)}, 最后访问: ${new Date(leak.lastAccessTime).toLocaleString()})`);
        });
      }
    }
    
    this.eventEmitter.emit('leaksDetected', { potentialLeaks, totalLeakedMemory });
    
    return { potentialLeaks, totalLeakedMemory };
  }
  
  /**
   * 清理资源
   */
  public clearResources(): void {
    this.resources.clear();
    
    if (this.config.debug) {
      Debug.log('内存分析器', '所有资源记录已清除');
    }
    
    this.eventEmitter.emit('resourcesCleared');
  }
  
  /**
   * 清理快照
   */
  public clearSnapshots(): void {
    this.snapshots = [];
    
    if (this.config.debug) {
      Debug.log('内存分析器', '所有快照已清除');
    }
    
    this.eventEmitter.emit('snapshotsCleared');
  }
  
  /**
   * 重置内存分析器
   */
  public reset(): void {
    this.clearResources();
    this.clearSnapshots();
    
    if (this.config.debug) {
      Debug.log('内存分析器', '已重置');
    }
    
    this.eventEmitter.emit('reset');
  }
  
  /**
   * 监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }
  
  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
  
  /**
   * 格式化字节数
   * @param bytes 字节数
   * @returns 格式化后的字符串
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
