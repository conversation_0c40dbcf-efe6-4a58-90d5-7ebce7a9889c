/**
 * 优化的水体材质
 * 提供更高性能的水体渲染
 */
import * as THREE from 'three';
/**
 * 优化的水体材质配置接口
 */
export interface OptimizedWaterMaterialConfig {
    /** 颜色 */
    color?: THREE.Color;
    /** 不透明度 */
    opacity?: number;
    /** 反射率 */
    reflectivity?: number;
    /** 折射率 */
    refractionRatio?: number;
    /** 波动强度 */
    waveStrength?: number;
    /** 波动速度 */
    waveSpeed?: number;
    /** 波动缩放 */
    waveScale?: number;
    /** 波动方向 */
    waveDirection?: THREE.Vector2;
    /** 深度 */
    depth?: number;
    /** 深水颜色 */
    depthColor?: THREE.Color;
    /** 浅水颜色 */
    shallowColor?: THREE.Color;
    /** 法线贴图 */
    normalMap?: THREE.Texture;
    /** 反射贴图 */
    reflectionMap?: THREE.Texture;
    /** 折射贴图 */
    refractionMap?: THREE.Texture;
    /** 深度贴图 */
    depthMap?: THREE.Texture;
    /** 因果波纹贴图 */
    causticsMap?: THREE.Texture;
    /** 泡沫贴图 */
    foamMap?: THREE.Texture;
    /** 是否启用反射 */
    enableReflection?: boolean;
    /** 是否启用折射 */
    enableRefraction?: boolean;
    /** 是否启用因果波纹 */
    enableCaustics?: boolean;
    /** 是否启用泡沫 */
    enableFoam?: boolean;
    /** 是否启用水下雾效 */
    enableUnderwaterFog?: boolean;
    /** 是否启用水下扭曲 */
    enableUnderwaterDistortion?: boolean;
    /** 是否透明 */
    transparent?: boolean;
    /** 渲染面 */
    side?: THREE.Side;
    /** 是否写入深度 */
    depthWrite?: boolean;
    /** 性能级别 (0-低, 1-中, 2-高) */
    qualityLevel?: number;
}
/**
 * 优化的水体材质
 */
export declare class OptimizedWaterMaterial extends THREE.ShaderMaterial {
    /** 时间 */
    private time;
    /** 质量级别 */
    private qualityLevel;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: OptimizedWaterMaterialConfig);
    /**
     * 更新材质
     * @param deltaTime 帧间隔时间（秒）
     * @param camera 相机
     */
    update(deltaTime: number, camera: THREE.Camera): void;
    /**
     * 设置反射贴图
     * @param texture 反射贴图
     */
    setReflectionMap(texture: THREE.Texture): void;
    /**
     * 设置折射贴图
     * @param texture 折射贴图
     */
    setRefractionMap(texture: THREE.Texture): void;
    /**
     * 设置深度贴图
     * @param texture 深度贴图
     */
    setDepthMap(texture: THREE.Texture): void;
    /**
     * 设置因果波纹贴图
     * @param texture 因果波纹贴图
     */
    setCausticsMap(texture: THREE.Texture): void;
    /**
     * 设置泡沫贴图
     * @param texture 泡沫贴图
     */
    setFoamMap(texture: THREE.Texture): void;
    /**
     * 设置高度图
     * @param texture 高度图
     */
    setHeightMap(texture: THREE.Texture): void;
    /**
     * 设置法线贴图
     * @param texture 法线贴图
     */
    setNormalMap(texture: THREE.Texture): void;
    /**
     * 设置质量级别
     * @param level 质量级别 (0-低, 1-中, 2-高)
     */
    setQualityLevel(level: number): void;
    /**
     * 获取质量级别
     * @returns 质量级别
     */
    getQualityLevel(): number;
    /**
     * 设置因果波纹强度
     * @param intensity 强度
     */
    setCausticsIntensity(intensity: number): void;
    /**
     * 设置泡沫强度
     * @param intensity 强度
     */
    setFoamIntensity(intensity: number): void;
    /**
     * 设置水下雾效密度
     * @param density 密度
     */
    setUnderwaterFogDensity(density: number): void;
    /**
     * 设置水下雾效颜色
     * @param color 颜色
     */
    setUnderwaterFogColor(color: THREE.Color): void;
    /**
     * 创建着色器
     * @param type 水体类型
     * @param enableReflection 是否启用反射
     * @param enableRefraction 是否启用折射
     * @param enableCaustics 是否启用因果波纹
     * @param enableFoam 是否启用泡沫
     * @param enableUnderwaterFog 是否启用水下雾效
     * @param enableUnderwaterDistortion 是否启用水下扭曲
     * @param qualityLevel 质量级别 (0-低, 1-中, 2-高)
     * @returns 着色器
     */
    private static createShader;
}
