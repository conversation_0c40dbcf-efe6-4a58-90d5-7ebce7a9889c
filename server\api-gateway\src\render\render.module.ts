/**
 * 渲染模块
 */
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RenderController } from './render.controller';
import { RenderService } from './render.service';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'RENDER_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('RENDER_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('RENDER_SERVICE_PORT', 3004),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [RenderController],
  providers: [RenderService],
  exports: [RenderService],
})
export class RenderModule {}
