/**
 * 地形CDLOD系统
 * 集成CDLOD算法到引擎系统中
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera   } from '../../rendering/Camera';
import { Scene } from '../../scene/Scene';
import { TerrainComponent } from '../components/TerrainComponent';
import { TerrainCDLOD, TerrainCDLODOptions, CDLODEventType } from './TerrainCDLOD';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 地形CDLOD系统事件类型
 */
export enum TerrainCDLODSystemEventType {
  /** 节点可见性变更 */
  NODE_VISIBILITY_CHANGED = 'node_visibility_changed',
  /** 节点过渡因子变更 */
  NODE_MORPH_FACTOR_CHANGED = 'node_morph_factor_changed',
  /** 四叉树更新 */
  QUADTREE_UPDATED = 'quadtree_updated'
}

/**
 * 地形CDLOD系统
 */
export class TerrainCDLODSystem extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'TerrainCDLODSystem';
  
  /** CDLOD实现 */
  private cdlod: TerrainCDLOD;

  /** 活跃相机 */
  private activeCamera: Camera | null = null;

  /** 活跃场景 */
  private activeScene: Scene | null = null;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 地形实体映射 */
  private terrainEntities: Map<Entity, TerrainComponent>;

  /**
   * 创建地形CDLOD系统
   * @param options CDLOD选项
   */
  constructor(options: TerrainCDLODOptions = {}) {
    super();

    this.cdlod = new TerrainCDLOD(options);
    this.eventEmitter = new EventEmitter();
    this.terrainEntities = new Map();

    // 注册CDLOD事件
    this.registerCDLODEvents();
  }
  
  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return TerrainCDLODSystem.TYPE;
  }
  
  /**
   * 初始化系统
   */
  public initialize(): void {
    // 查找活跃相机和场景
    this.findActiveCamera();
    this.findActiveScene();
    
    // 查找地形实体
    this.findTerrainEntities();
  }
  
  /**
   * 注册CDLOD事件
   */
  private registerCDLODEvents(): void {
    // 注册节点可见性变更事件
    this.cdlod.on(CDLODEventType.NODE_VISIBILITY_CHANGED, (node, visible) => {
      this.eventEmitter.emit(TerrainCDLODSystemEventType.NODE_VISIBILITY_CHANGED, node, visible);
    });
    
    // 注册节点过渡因子变更事件
    this.cdlod.on(CDLODEventType.NODE_MORPH_FACTOR_CHANGED, (node, factor) => {
      this.eventEmitter.emit(TerrainCDLODSystemEventType.NODE_MORPH_FACTOR_CHANGED, node, factor);
    });
    
    // 注册四叉树更新事件
    this.cdlod.on(CDLODEventType.QUADTREE_UPDATED, () => {
      this.eventEmitter.emit(TerrainCDLODSystemEventType.QUADTREE_UPDATED);
    });
  }
  
  /**
   * 查找活跃相机
   */
  private findActiveCamera(): void {
    if (!this.world) {
      Debug.warn('TerrainCDLODSystem', '世界未初始化');
      return;
    }

    // 获取所有实体
    const entities = this.world.getAllEntities();

    // 查找具有相机组件的实体
    for (const entity of entities) {
      const camera = entity.getComponent<Camera>('Camera');
      if (camera) {
        this.activeCamera = camera;
        Debug.log('TerrainCDLODSystem', `找到活跃相机: ${entity.id}`);
        return;
      }
    }

    Debug.warn('TerrainCDLODSystem', '没有找到相机');
  }
  
  /**
   * 查找活跃场景
   */
  private findActiveScene(): void {
    if (!this.world) {
      Debug.warn('TerrainCDLODSystem', '世界未初始化');
      return;
    }

    // 获取当前活跃场景
    const activeScene = this.world.getActiveScene();
    if (activeScene) {
      this.activeScene = activeScene;
      Debug.log('TerrainCDLODSystem', `找到活跃场景`);
    } else {
      Debug.warn('TerrainCDLODSystem', '没有找到活跃场景');
    }
  }
  
  /**
   * 查找地形实体
   */
  private findTerrainEntities(): void {
    if (!this.world) {
      Debug.warn('TerrainCDLODSystem', '世界未初始化');
      return;
    }

    // 获取所有实体
    const entities = this.world.getAllEntities();
    let foundCount = 0;

    // 查找具有地形组件的实体
    for (const entity of entities) {
      const terrainComponent = entity.getComponent<TerrainComponent>('TerrainComponent');
      if (terrainComponent) {
        this.addTerrainEntity(entity, terrainComponent);
        foundCount++;
      }
    }

    if (foundCount === 0) {
      Debug.warn('TerrainCDLODSystem', '没有找到地形组件');
    } else {
      Debug.log('TerrainCDLODSystem', `找到 ${foundCount} 个地形实体`);
    }
  }
  
  /**
   * 添加地形实体
   * @param entity 实体
   * @param component 地形组件
   */
  public addTerrainEntity(entity: Entity, component: TerrainComponent): void {
    // 存储地形实体
    this.terrainEntities.set(entity, component);
    
    // 添加到CDLOD
    this.cdlod.addTerrainEntity(entity, component);
    
    Debug.log('TerrainCDLODSystem', `添加地形实体: ${entity.id}`);
  }
  
  /**
   * 移除地形实体
   * @param entity 实体
   */
  public removeTerrainEntity(entity: Entity): void {
    // 从映射中移除
    this.terrainEntities.delete(entity);
    
    // 从CDLOD中移除
    this.cdlod.removeTerrainEntity(entity);
    
    Debug.log('TerrainCDLODSystem', `移除地形实体: ${entity.id}`);
  }
  
  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 如果没有活跃相机或场景，则尝试查找
    if (!this.activeCamera) {
      this.findActiveCamera();
    }
    
    if (!this.activeScene) {
      this.findActiveScene();
    }
    
    // 更新CDLOD
    this.cdlod.update(deltaTime);
  }
}
