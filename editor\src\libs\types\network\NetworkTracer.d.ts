/**
 * 网络路由跟踪器
 * 用于跟踪网络路由路径和分析网络延迟
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 网络路由跟踪配置
 */
export interface NetworkTracerConfig {
    /** 目标主机 */
    targetHost: string;
    /** 最大跳数 */
    maxHops?: number;
    /** 每跳超时时间（毫秒） */
    hopTimeout?: number;
    /** 每跳重试次数 */
    hopRetries?: number;
    /** 是否启用详细日志 */
    detailedLogging?: boolean;
    /** 是否解析主机名 */
    resolveHostnames?: boolean;
    /** 是否获取地理位置信息 */
    geoLocation?: boolean;
    /** 是否分析路由质量 */
    analyzeRouteQuality?: boolean;
    /** 是否检测网络瓶颈 */
    detectBottlenecks?: boolean;
    /** 是否使用ICMP协议 */
    useIcmp?: boolean;
    /** 是否使用UDP协议 */
    useUdp?: boolean;
    /** 是否使用TCP协议 */
    useTcp?: boolean;
}
/**
 * 网络路由节点
 */
export interface RouteNode {
    /** 跳数 */
    hop: number;
    /** IP地址 */
    ip: string;
    /** 主机名 */
    hostname?: string;
    /** 响应时间（毫秒） */
    responseTime: number;
    /** 丢包率（0-1） */
    packetLoss: number;
    /** 地理位置 */
    location?: {
        /** 国家 */
        country?: string;
        /** 城市 */
        city?: string;
        /** 经度 */
        longitude?: number;
        /** 纬度 */
        latitude?: number;
        /** ISP */
        isp?: string;
    };
    /** 是否是瓶颈节点 */
    isBottleneck?: boolean;
    /** 瓶颈原因 */
    bottleneckReason?: string;
    /** 节点质量评分（0-100） */
    qualityScore?: number;
}
/**
 * 网络路由跟踪结果
 */
export interface NetworkTraceResult {
    /** 目标主机 */
    targetHost: string;
    /** 目标IP */
    targetIp?: string;
    /** 跟踪是否成功 */
    success: boolean;
    /** 错误信息 */
    error?: string;
    /** 跟踪开始时间 */
    startTime: number;
    /** 跟踪结束时间 */
    endTime: number;
    /** 跟踪持续时间（毫秒） */
    duration: number;
    /** 总跳数 */
    hops: number;
    /** 路由节点列表 */
    nodes: RouteNode[];
    /** 端到端延迟（毫秒） */
    endToEndLatency: number;
    /** 路由质量评分（0-100） */
    routeQualityScore?: number;
    /** 瓶颈节点索引 */
    bottleneckNodeIndices?: number[];
    /** 路由分析 */
    routeAnalysis?: {
        /** 平均每跳延迟（毫秒） */
        avgHopLatency: number;
        /** 最大跳延迟（毫秒） */
        maxHopLatency: number;
        /** 最大跳延迟节点索引 */
        maxHopLatencyIndex: number;
        /** 平均丢包率（0-1） */
        avgPacketLoss: number;
        /** 最大丢包率 */
        maxPacketLoss: number;
        /** 最大丢包率节点索引 */
        maxPacketLossIndex: number;
        /** 跨国跳数 */
        internationalHops: number;
        /** 跨ISP跳数 */
        crossIspHops: number;
    };
}
/**
 * 网络路由跟踪器
 * 用于跟踪网络路由路径和分析网络延迟
 */
export declare class NetworkTracer extends EventEmitter {
    /** 配置 */
    private config;
    /** 当前跟踪结果 */
    private currentResult;
    /** 跟踪是否正在进行 */
    private tracing;
    /** 当前跳数 */
    private currentHop;
    /** 跟踪超时定时器ID */
    private timeoutId;
    /**
     * 创建网络路由跟踪器
     * @param config 配置
     */
    constructor(config: NetworkTracerConfig);
    /**
     * 开始跟踪
     * @returns 跟踪结果Promise
     */
    startTrace(): Promise<NetworkTraceResult>;
    /**
     * 取消跟踪
     */
    cancelTrace(): void;
    /**
     * 重置跟踪结果
     */
    private resetTraceResult;
    /**
     * 获取跟踪结果
     * @returns 跟踪结果
     */
    getTraceResult(): NetworkTraceResult;
    /**
     * 解析目标主机IP
     */
    private resolveTargetIp;
    /**
     * 跟踪路由
     */
    private traceRoute;
    /**
     * 跟踪单个跳
     * @param hop 跳数
     * @returns 路由节点
     */
    private traceHop;
    /**
     * 分析路由质量
     */
    private analyzeRouteQuality;
    /**
     * 检测瓶颈
     */
    private detectBottlenecks;
    /**
     * 为跳生成IP地址
     * @param hop 跳数
     * @returns IP地址
     */
    private generateIpForHop;
    /**
     * 为跳生成响应时间
     * @param hop 跳数
     * @returns 响应时间（毫秒）
     */
    private generateResponseTimeForHop;
    /**
     * 为跳生成丢包率
     * @param hop 跳数
     * @returns 丢包率（0-1）
     */
    private generatePacketLossForHop;
    /**
     * 为IP生成主机名
     * @param ip IP地址
     * @returns 主机名
     */
    private generateHostnameForIp;
    /**
     * 为IP生成地理位置
     * @param ip IP地址
     * @returns 地理位置
     */
    private generateLocationForIp;
    /**
     * 销毁跟踪器
     */
    dispose(): void;
}
