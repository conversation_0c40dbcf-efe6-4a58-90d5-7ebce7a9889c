/**
 * 矩形区域光
 * 物理精确的矩形区域光
 */
import * as THREE from 'three';
// 使用类型断言导入 RectAreaLightHelper
// @ts-ignore
import { RectAreaLightHelper } from 'three/examples/jsm/helpers/RectAreaLightHelper.js';
import { AreaLight, AreaLightOptions, AreaLightType } from './AreaLight';

/**
 * 矩形区域光选项接口
 */
export interface RectAreaLightOptions extends AreaLightOptions {
  /** 光源类型 */
  type: AreaLightType.RECT;
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
  /** 是否使用物理单位 */
  usePhysicalUnits?: boolean;
  /** 功率（瓦特） */
  power?: number;
  /** 发光效率（流明/瓦特） */
  efficacy?: number;
}

/**
 * 矩形区域光组件类
 */
export class RectAreaLightComponent extends AreaLight {
  /** 宽度 */
  private width: number;

  /** 高度 */
  private height: number;

  /** 是否使用物理单位 */
  private usePhysicalUnits: boolean;

  /** 功率（瓦特） */
  private power: number;

  /** 发光效率（流明/瓦特） */
  private efficacy: number;

  /**
   * 创建矩形区域光组件
   * @param options 矩形区域光选项
   */
  constructor(options: RectAreaLightOptions) {
    super(options);

    this.width = options.width !== undefined ? options.width : 10;
    this.height = options.height !== undefined ? options.height : 10;
    this.usePhysicalUnits = options.usePhysicalUnits !== undefined ? options.usePhysicalUnits : false;
    this.power = options.power !== undefined ? options.power : 60;
    this.efficacy = options.efficacy !== undefined ? options.efficacy : 80;

    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 创建光源
   * @param options 矩形区域光选项
   * @returns Three.js矩形区域光
   */
  protected createLight(options: RectAreaLightOptions): THREE.RectAreaLight {
    const color = options.color !== undefined ? options.color : 0xffffff;
    const intensity = options.intensity !== undefined ? options.intensity : 1;
    const width = options.width !== undefined ? options.width : 10;
    const height = options.height !== undefined ? options.height : 10;
    
    return new THREE.RectAreaLight(color, intensity, width, height);
  }

  /**
   * 创建辅助对象
   * @returns Three.js矩形区域光辅助对象
   */
  protected createHelper(): THREE.Object3D {
    const helper = new RectAreaLightHelper(this.light as THREE.RectAreaLight);
    helper.visible = this.showHelper;
    return helper;
  }

  /**
   * 更新辅助对象颜色
   */
  protected updateHelperColor(): void {
    if (this.helper && this.helper instanceof RectAreaLightHelper) {
      // 更新辅助对象颜色
      // 注意：RectAreaLightHelper没有直接的颜色设置方法，需要重新创建
      const parent = this.helper.parent;
      
      if (parent) {
        parent.remove(this.helper);
      }
      
      (this.helper as any).dispose();
      this.helper = this.createHelper();
      
      if (parent) {
        parent.add(this.helper);
      }
    }
  }

  /**
   * 更新物理强度
   */
  private updatePhysicalIntensity(): void {
    if (!this.usePhysicalUnits) return;

    // 计算流明
    const lumens = this.power * this.efficacy;
    
    // 计算面积
    const area = this.width * this.height;
    
    // 计算强度（流明/面积）
    const intensity = lumens / area;
    
    // 设置光源强度
    if (this.light instanceof THREE.RectAreaLight) {
      this.light.intensity = intensity;
    }
  }

  /**
   * 设置宽度
   * @param width 宽度
   */
  public setWidth(width: number): void {
    this.width = width;
    
    if (this.light instanceof THREE.RectAreaLight) {
      this.light.width = width;
    }
    
    // 更新辅助对象
    this.updateHelper();
    
    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取宽度
   * @returns 宽度
   */
  public getWidth(): number {
    return this.width;
  }

  /**
   * 设置高度
   * @param height 高度
   */
  public setHeight(height: number): void {
    this.height = height;
    
    if (this.light instanceof THREE.RectAreaLight) {
      this.light.height = height;
    }
    
    // 更新辅助对象
    this.updateHelper();
    
    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取高度
   * @returns 高度
   */
  public getHeight(): number {
    return this.height;
  }

  /**
   * 设置是否使用物理单位
   * @param use 是否使用
   */
  public setUsePhysicalUnits(use: boolean): void {
    this.usePhysicalUnits = use;
    
    // 如果使用物理单位，则更新光源强度
    if (use) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取是否使用物理单位
   * @returns 是否使用
   */
  public isUsePhysicalUnits(): boolean {
    return this.usePhysicalUnits;
  }

  /**
   * 设置功率
   * @param power 功率（瓦特）
   */
  public setPower(power: number): void {
    this.power = power;
    
    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取功率
   * @returns 功率（瓦特）
   */
  public getPower(): number {
    return this.power;
  }

  /**
   * 设置发光效率
   * @param efficacy 发光效率（流明/瓦特）
   */
  public setEfficacy(efficacy: number): void {
    this.efficacy = efficacy;
    
    // 如果使用物理单位，则更新光源强度
    if (this.usePhysicalUnits) {
      this.updatePhysicalIntensity();
    }
  }

  /**
   * 获取发光效率
   * @returns 发光效率（流明/瓦特）
   */
  public getEfficacy(): number {
    return this.efficacy;
  }

  /**
   * 更新辅助对象
   */
  private updateHelper(): void {
    if (this.helper && this.helper instanceof RectAreaLightHelper) {
      // 更新辅助对象
      // 注意：RectAreaLightHelper没有直接的更新方法，需要重新创建
      const parent = this.helper.parent;
      
      if (parent) {
        parent.remove(this.helper);
      }
      
      (this.helper as any).dispose();
      this.helper = this.createHelper();
      
      if (parent) {
        parent.add(this.helper);
      }
    }
  }

  /**
   * 更新组件
   * @param _deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    // RectAreaLightHelper 不需要手动更新
    // 它会自动跟随光源的变化
  }
}
