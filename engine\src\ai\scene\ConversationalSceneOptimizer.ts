/**
 * 多轮对话场景优化器
 * 处理用户反馈，实现场景的迭代优化和多轮对话交互
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { SceneGenerationResult, SceneUnderstanding, SceneLayout } from './SceneGenerationTypes';
import { SceneDescriptionUnderstanding } from './SceneDescriptionUnderstanding';
import { SceneGenerationAIManager } from './SceneGenerationAIManager';

/**
 * 反馈分析结果
 */
export interface FeedbackAnalysis {
  /** 反馈类型 */
  type: 'modification' | 'addition' | 'removal' | 'style_change' | 'layout_change';
  /** 目标对象 */
  targetObjects: string[];
  /** 修改内容 */
  modifications: SceneModification[];
  /** 置信度 */
  confidence: number;
  /** 情感倾向 */
  sentiment: 'positive' | 'negative' | 'neutral';
}

/**
 * 场景修改
 */
export interface SceneModification {
  /** 修改类型 */
  type: 'add' | 'remove' | 'move' | 'resize' | 'recolor' | 'replace';
  /** 目标对象 */
  target: string;
  /** 修改参数 */
  parameters: Record<string, any>;
  /** 优先级 */
  priority: number;
}

/**
 * 场景优化结果
 */
export interface SceneOptimizationResult {
  /** 优化后的场景 */
  optimizedScene: any;
  /** 应用的修改 */
  modifications: SceneModification[];
  /** 优化置信度 */
  confidence: number;
  /** 优化说明 */
  explanation: string;
  /** 建议的下一步操作 */
  suggestions: string[];
}

/**
 * 对话管理器
 */
export class DialogueManager {
  private conversationHistory: Map<string, ConversationContext> = new Map();
  private maxHistoryLength: number = 20;

  /**
   * 创建对话上下文
   */
  createContext(sessionId: string): ConversationContext {
    const context: ConversationContext = {
      sessionId,
      messages: [],
      currentScene: null,
      userPreferences: {},
      conversationState: 'initial'
    };
    
    this.conversationHistory.set(sessionId, context);
    return context;
  }

  /**
   * 获取对话上下文
   */
  getContext(sessionId: string): ConversationContext | undefined {
    return this.conversationHistory.get(sessionId);
  }

  /**
   * 添加消息
   */
  addMessage(sessionId: string, message: ConversationMessage): void {
    const context = this.conversationHistory.get(sessionId);
    if (context) {
      context.messages.push(message);
      
      // 限制历史长度
      if (context.messages.length > this.maxHistoryLength) {
        context.messages = context.messages.slice(-this.maxHistoryLength);
      }
      
      // 更新对话状态
      this.updateConversationState(context, message);
    }
  }

  /**
   * 更新对话状态
   */
  private updateConversationState(context: ConversationContext, message: ConversationMessage): void {
    if (message.type === 'user') {
      if (message.content.includes('完成') || message.content.includes('结束')) {
        context.conversationState = 'completed';
      } else if (message.content.includes('修改') || message.content.includes('调整')) {
        context.conversationState = 'modifying';
      } else {
        context.conversationState = 'active';
      }
    }
  }

  /**
   * 获取对话历史
   */
  getConversationHistory(sessionId: string): ConversationMessage[] {
    const context = this.conversationHistory.get(sessionId);
    return context ? context.messages : [];
  }

  /**
   * 清理对话
   */
  clearConversation(sessionId: string): void {
    this.conversationHistory.delete(sessionId);
  }
}

/**
 * 对话上下文
 */
export interface ConversationContext {
  /** 会话ID */
  sessionId: string;
  /** 消息历史 */
  messages: ConversationMessage[];
  /** 当前场景 */
  currentScene: any;
  /** 用户偏好 */
  userPreferences: Record<string, any>;
  /** 对话状态 */
  conversationState: 'initial' | 'active' | 'modifying' | 'completed';
}

/**
 * 对话消息
 */
export interface ConversationMessage {
  /** 消息ID */
  id: string;
  /** 消息类型 */
  type: 'user' | 'system';
  /** 消息内容 */
  content: string;
  /** 时间戳 */
  timestamp: number;
  /** 相关场景 */
  relatedScene?: any;
  /** 置信度 */
  confidence?: number;
}

/**
 * 反馈分析器
 */
export class FeedbackAnalyzer {
  private nlpProcessor: SceneDescriptionUnderstanding;

  constructor() {
    this.nlpProcessor = new SceneDescriptionUnderstanding();
  }

  /**
   * 分析用户反馈
   */
  async analyze(feedback: string): Promise<FeedbackAnalysis> {
    // 1. 基础NLP处理
    const understanding = await this.nlpProcessor.parseDescription(feedback);
    
    // 2. 识别反馈类型
    const feedbackType = this.identifyFeedbackType(feedback);
    
    // 3. 提取目标对象
    const targetObjects = this.extractTargetObjects(feedback, understanding);
    
    // 4. 分析修改内容
    const modifications = this.analyzeModifications(feedback, understanding);
    
    // 5. 计算置信度
    const confidence = this.calculateAnalysisConfidence(feedback, understanding);
    
    // 6. 分析情感倾向
    const sentiment = this.analyzeSentiment(feedback);

    return {
      type: feedbackType,
      targetObjects,
      modifications,
      confidence,
      sentiment
    };
  }

  /**
   * 识别反馈类型
   */
  private identifyFeedbackType(feedback: string): FeedbackAnalysis['type'] {
    if (feedback.includes('添加') || feedback.includes('增加') || feedback.includes('放一个')) {
      return 'addition';
    }
    
    if (feedback.includes('删除') || feedback.includes('移除') || feedback.includes('去掉')) {
      return 'removal';
    }
    
    if (feedback.includes('移动') || feedback.includes('换个位置') || feedback.includes('重新布局')) {
      return 'layout_change';
    }
    
    if (feedback.includes('风格') || feedback.includes('颜色') || feedback.includes('材质')) {
      return 'style_change';
    }
    
    return 'modification';
  }

  /**
   * 提取目标对象
   */
  private extractTargetObjects(feedback: string, understanding: SceneUnderstanding): string[] {
    const objects: string[] = [];
    
    // 从理解结果中提取对象
    understanding.elements.forEach(element => {
      if (feedback.includes(element.name)) {
        objects.push(element.name);
      }
    });
    
    // 通用对象关键词
    const objectKeywords = ['桌子', '椅子', '沙发', '床', '柜子', '灯', '电视', '电脑'];
    objectKeywords.forEach(keyword => {
      if (feedback.includes(keyword) && !objects.includes(keyword)) {
        objects.push(keyword);
      }
    });
    
    return objects;
  }

  /**
   * 分析修改内容
   */
  private analyzeModifications(feedback: string, understanding: SceneUnderstanding): SceneModification[] {
    const modifications: SceneModification[] = [];
    
    // 分析添加操作
    if (feedback.includes('添加') || feedback.includes('增加')) {
      understanding.elements.forEach(element => {
        if (feedback.includes(element.name)) {
          modifications.push({
            type: 'add',
            target: element.name,
            parameters: element.attributes,
            priority: 1
          });
        }
      });
    }
    
    // 分析移动操作
    if (feedback.includes('移动') || feedback.includes('换个位置')) {
      const movePattern = /把(\w+)移动?到(\w+)/g;
      let match;
      while ((match = movePattern.exec(feedback)) !== null) {
        modifications.push({
          type: 'move',
          target: match[1],
          parameters: { destination: match[2] },
          priority: 2
        });
      }
    }
    
    // 分析颜色修改
    if (feedback.includes('颜色') || feedback.includes('换成')) {
      const colorPattern = /把(\w+)换成(\w+色?)/g;
      let match;
      while ((match = colorPattern.exec(feedback)) !== null) {
        modifications.push({
          type: 'recolor',
          target: match[1],
          parameters: { color: match[2] },
          priority: 1
        });
      }
    }
    
    return modifications;
  }

  /**
   * 计算分析置信度
   */
  private calculateAnalysisConfidence(feedback: string, understanding: SceneUnderstanding): number {
    let confidence = 0.5;
    
    // 基于理解质量调整
    confidence += understanding.confidence * 0.3;
    
    // 基于反馈明确性调整
    if (feedback.length > 20) confidence += 0.1;
    if (feedback.includes('请') || feedback.includes('帮我')) confidence += 0.1;
    
    // 基于关键词匹配调整
    const actionKeywords = ['添加', '删除', '移动', '修改', '换成'];
    const matchedActions = actionKeywords.filter(keyword => feedback.includes(keyword));
    confidence += matchedActions.length * 0.1;
    
    return Math.min(confidence, 1.0);
  }

  /**
   * 分析情感倾向
   */
  private analyzeSentiment(feedback: string): 'positive' | 'negative' | 'neutral' {
    const positiveKeywords = ['好', '棒', '喜欢', '满意', '不错', '很好'];
    const negativeKeywords = ['不好', '不喜欢', '难看', '不满意', '糟糕'];
    
    const positiveCount = positiveKeywords.filter(keyword => feedback.includes(keyword)).length;
    const negativeCount = negativeKeywords.filter(keyword => feedback.includes(keyword)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }
}

/**
 * 迭代改进器
 */
export class IterativeImprover {
  private sceneVersions: Map<string, any[]> = new Map();
  private maxVersions: number = 10;

  /**
   * 改进场景
   */
  async improve(currentScene: any, optimizationPlan: SceneModification[]): Promise<any> {
    // 保存当前版本
    this.saveVersion(currentScene.id, currentScene);
    
    // 应用修改
    let improvedScene = this.cloneScene(currentScene);
    
    for (const modification of optimizationPlan) {
      improvedScene = await this.applyModification(improvedScene, modification);
    }
    
    return improvedScene;
  }

  /**
   * 保存场景版本
   */
  private saveVersion(sceneId: string, scene: any): void {
    if (!this.sceneVersions.has(sceneId)) {
      this.sceneVersions.set(sceneId, []);
    }
    
    const versions = this.sceneVersions.get(sceneId)!;
    versions.push(this.cloneScene(scene));
    
    // 限制版本数量
    if (versions.length > this.maxVersions) {
      versions.shift();
    }
  }

  /**
   * 克隆场景
   */
  private cloneScene(scene: any): any {
    return JSON.parse(JSON.stringify(scene));
  }

  /**
   * 应用修改
   */
  private async applyModification(scene: any, modification: SceneModification): Promise<any> {
    switch (modification.type) {
      case 'add':
        return this.addObject(scene, modification);
      case 'remove':
        return this.removeObject(scene, modification);
      case 'move':
        return this.moveObject(scene, modification);
      case 'recolor':
        return this.recolorObject(scene, modification);
      case 'resize':
        return this.resizeObject(scene, modification);
      case 'replace':
        return this.replaceObject(scene, modification);
      default:
        return scene;
    }
  }

  /**
   * 添加对象
   */
  private addObject(scene: any, modification: SceneModification): any {
    // 实现添加对象逻辑
    console.log(`添加对象: ${modification.target}`, modification.parameters);
    return scene;
  }

  /**
   * 移除对象
   */
  private removeObject(scene: any, modification: SceneModification): any {
    // 实现移除对象逻辑
    console.log(`移除对象: ${modification.target}`);
    return scene;
  }

  /**
   * 移动对象
   */
  private moveObject(scene: any, modification: SceneModification): any {
    // 实现移动对象逻辑
    console.log(`移动对象: ${modification.target}`, modification.parameters);
    return scene;
  }

  /**
   * 重新着色对象
   */
  private recolorObject(scene: any, modification: SceneModification): any {
    // 实现重新着色逻辑
    console.log(`重新着色: ${modification.target}`, modification.parameters);
    return scene;
  }

  /**
   * 调整对象大小
   */
  private resizeObject(scene: any, modification: SceneModification): any {
    // 实现调整大小逻辑
    console.log(`调整大小: ${modification.target}`, modification.parameters);
    return scene;
  }

  /**
   * 替换对象
   */
  private replaceObject(scene: any, modification: SceneModification): any {
    // 实现替换对象逻辑
    console.log(`替换对象: ${modification.target}`, modification.parameters);
    return scene;
  }

  /**
   * 获取场景版本历史
   */
  getVersionHistory(sceneId: string): any[] {
    return this.sceneVersions.get(sceneId) || [];
  }

  /**
   * 回滚到指定版本
   */
  rollbackToVersion(sceneId: string, versionIndex: number): any | null {
    const versions = this.sceneVersions.get(sceneId);
    if (versions && versionIndex >= 0 && versionIndex < versions.length) {
      return this.cloneScene(versions[versionIndex]);
    }
    return null;
  }
}

/**
 * 多轮对话场景优化器
 */
export class ConversationalSceneOptimizer extends EventEmitter {
  private dialogueManager: DialogueManager;
  private feedbackAnalyzer: FeedbackAnalyzer;
  private iterativeImprover: IterativeImprover;
  private sceneGenerator: SceneGenerationAIManager;
  private world: any;

  constructor(world: any) {
    super();
    this.world = world;
    this.dialogueManager = new DialogueManager();
    this.feedbackAnalyzer = new FeedbackAnalyzer();
    this.iterativeImprover = new IterativeImprover();
    this.sceneGenerator = new SceneGenerationAIManager(this.world);
  }

  /**
   * 处理用户反馈
   */
  async processFeedback(
    sessionId: string, 
    feedback: string, 
    currentScene: any
  ): Promise<SceneOptimizationResult> {
    try {
      // 1. 分析反馈内容
      const analysis = await this.feedbackAnalyzer.analyze(feedback);
      
      // 2. 识别需要修改的部分
      const modifications = this.identifyModifications(analysis, currentScene);
      
      // 3. 生成优化方案
      const optimizationPlan = await this.generateOptimizationPlan(modifications);
      
      // 4. 应用优化
      const optimizedScene = await this.iterativeImprover.improve(currentScene, optimizationPlan);
      
      // 5. 更新对话上下文
      this.updateConversationContext(sessionId, feedback, optimizedScene, analysis);
      
      // 6. 生成解释和建议
      const explanation = this.generateExplanation(modifications);
      const suggestions = await this.generateSuggestions(optimizedScene, analysis);

      const result: SceneOptimizationResult = {
        optimizedScene,
        modifications,
        confidence: analysis.confidence,
        explanation,
        suggestions
      };

      this.emit('optimizationComplete', { sessionId, result });
      return result;

    } catch (error) {
      console.error('处理反馈失败:', error);
      throw error;
    }
  }

  /**
   * 识别修改内容
   */
  private identifyModifications(analysis: FeedbackAnalysis, currentScene: any): SceneModification[] {
    return analysis.modifications;
  }

  /**
   * 生成优化方案
   */
  private async generateOptimizationPlan(modifications: SceneModification[]): Promise<SceneModification[]> {
    // 按优先级排序
    return modifications.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 更新对话上下文
   */
  private updateConversationContext(
    sessionId: string, 
    feedback: string, 
    optimizedScene: any, 
    analysis: FeedbackAnalysis
  ): void {
    let context = this.dialogueManager.getContext(sessionId);
    if (!context) {
      context = this.dialogueManager.createContext(sessionId);
    }

    // 添加用户消息
    this.dialogueManager.addMessage(sessionId, {
      id: `user_${Date.now()}`,
      type: 'user',
      content: feedback,
      timestamp: Date.now(),
      confidence: analysis.confidence
    });

    // 添加系统响应
    this.dialogueManager.addMessage(sessionId, {
      id: `system_${Date.now()}`,
      type: 'system',
      content: '已根据您的反馈优化场景',
      timestamp: Date.now(),
      relatedScene: optimizedScene
    });

    // 更新当前场景
    context.currentScene = optimizedScene;
  }

  /**
   * 生成解释
   */
  private generateExplanation(modifications: SceneModification[]): string {
    if (modifications.length === 0) {
      return '未检测到需要修改的内容。';
    }

    const explanations = modifications.map(mod => {
      switch (mod.type) {
        case 'add':
          return `添加了${mod.target}`;
        case 'remove':
          return `移除了${mod.target}`;
        case 'move':
          return `移动了${mod.target}的位置`;
        case 'recolor':
          return `修改了${mod.target}的颜色`;
        case 'resize':
          return `调整了${mod.target}的大小`;
        case 'replace':
          return `替换了${mod.target}`;
        default:
          return `修改了${mod.target}`;
      }
    });

    return `已完成以下修改：${explanations.join('，')}。`;
  }

  /**
   * 生成建议
   */
  private async generateSuggestions(optimizedScene: any, analysis: FeedbackAnalysis): Promise<string[]> {
    const suggestions: string[] = [];

    // 基于情感倾向生成建议
    if (analysis.sentiment === 'positive') {
      suggestions.push('您似乎对当前效果满意，是否需要进一步细化？');
    } else if (analysis.sentiment === 'negative') {
      suggestions.push('如果还有不满意的地方，请继续告诉我需要调整什么。');
    }

    // 基于修改类型生成建议
    if (analysis.type === 'addition') {
      suggestions.push('您可以继续添加其他元素来丰富场景。');
    } else if (analysis.type === 'layout_change') {
      suggestions.push('布局已调整，您可以查看整体效果是否协调。');
    }

    // 通用建议
    suggestions.push('您可以说"完成"来结束编辑，或继续描述需要的修改。');

    return suggestions;
  }

  /**
   * 生成语音指导
   */
  async generateVoiceGuidance(scene: any, userIntent: string): Promise<string> {
    const sceneAnalysis = await this.analyzeScene(scene);
    const suggestions = await this.generateContextualSuggestions(sceneAnalysis, userIntent);

    return this.formatVoiceGuidance(suggestions);
  }

  /**
   * 分析场景
   */
  private async analyzeScene(scene: any): Promise<any> {
    // 实现场景分析逻辑
    return {
      elementCount: scene.elements?.length || 0,
      complexity: 'medium',
      style: scene.style || 'modern',
      completeness: 0.8
    };
  }

  /**
   * 生成上下文建议
   */
  private async generateContextualSuggestions(sceneAnalysis: any, userIntent: string): Promise<string[]> {
    const suggestions: string[] = [];

    if (sceneAnalysis.elementCount < 3) {
      suggestions.push('场景元素较少，建议添加更多家具或装饰');
    }

    if (sceneAnalysis.completeness < 0.7) {
      suggestions.push('场景还可以进一步完善');
    }

    return suggestions;
  }

  /**
   * 格式化语音指导
   */
  private formatVoiceGuidance(suggestions: string[]): string {
    if (suggestions.length === 0) {
      return '场景看起来很不错！您还有其他需要调整的吗？';
    }

    return suggestions.join('。') + '。您觉得怎么样？';
  }

  /**
   * 获取对话历史
   */
  getConversationHistory(sessionId: string): ConversationMessage[] {
    return this.dialogueManager.getConversationHistory(sessionId);
  }

  /**
   * 清理会话
   */
  clearSession(sessionId: string): void {
    this.dialogueManager.clearConversation(sessionId);
  }
}
