<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>水体移动设备优化示例</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      background-color: #000;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
    }
    
    #loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #000;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #fff;
      z-index: 1000;
    }
    
    #loading h1 {
      font-size: 24px;
      margin-bottom: 20px;
    }
    
    #loading-progress {
      width: 300px;
      height: 20px;
      background-color: #333;
      border-radius: 10px;
      overflow: hidden;
    }
    
    #loading-bar {
      width: 0%;
      height: 100%;
      background-color: #0088ff;
      transition: width 0.3s ease;
    }
    
    #loading-text {
      margin-top: 10px;
      font-size: 14px;
    }
    
    #canvas-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    
    #info {
      position: fixed;
      bottom: 10px;
      right: 10px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-size: 12px;
      max-width: 300px;
    }

    #device-info {
      position: fixed;
      top: 10px;
      right: 10px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-size: 12px;
      max-width: 300px;
    }
  </style>
</head>
<body>
  <div id="loading">
    <h1>水体移动设备优化示例</h1>
    <div id="loading-progress">
      <div id="loading-bar"></div>
    </div>
    <div id="loading-text">加载中...</div>
  </div>
  
  <div id="canvas-container"></div>
  
  <div id="info">
    <h3>操作说明</h3>
    <p>拖动：旋转视角</p>
    <p>双指缩放：缩放视角</p>
    <p>双指拖动：平移视角</p>
    <p>点击性能级别按钮可以切换不同的性能级别</p>
  </div>

  <div id="device-info"></div>
  
  <script type="module">
    import { MobileOptimizedWaterExample } from './MobileOptimizedWaterExample.js';
    
    // 显示设备信息
    const deviceInfo = document.getElementById('device-info');
    deviceInfo.innerHTML = `
      <h3>设备信息</h3>
      <p>用户代理: ${navigator.userAgent}</p>
      <p>屏幕分辨率: ${window.screen.width} x ${window.screen.height}</p>
      <p>设备像素比: ${window.devicePixelRatio}</p>
      <p>硬件并发: ${navigator.hardwareConcurrency || '未知'}</p>
    `;
    
    // 模拟加载进度
    let progress = 0;
    const loadingBar = document.getElementById('loading-bar');
    const loadingText = document.getElementById('loading-text');
    const loading = document.getElementById('loading');
    
    const updateProgress = () => {
      progress += Math.random() * 5;
      if (progress > 100) progress = 100;
      
      loadingBar.style.width = `${progress}%`;
      loadingText.textContent = `加载中... ${Math.floor(progress)}%`;
      
      if (progress < 100) {
        setTimeout(updateProgress, 100);
      } else {
        setTimeout(() => {
          loading.style.opacity = '0';
          loading.style.transition = 'opacity 0.5s ease';
          setTimeout(() => {
            loading.style.display = 'none';
            
            // 创建示例
            try {
              const example = new MobileOptimizedWaterExample();
              
              // 添加窗口关闭事件
              window.addEventListener('beforeunload', () => {
                // 清理资源
                if (example.destroy) {
                  example.destroy();
                }
              });
            } catch (error) {
              console.error('启动示例时出错:', error);
              alert(`启动示例时出错: ${error.message}`);
            }
          }, 500);
        }, 500);
      }
    };
    
    // 开始更新进度
    updateProgress();
  </script>
</body>
</html>
