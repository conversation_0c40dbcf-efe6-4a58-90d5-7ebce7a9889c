import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServiceDiscoveryService } from './service-discovery.service';
import { LoadBalancerService } from './load-balancer.service';
import { HealthCheckService } from './health-check.service';

export interface ServiceRoute {
  path: string;
  serviceName: string;
  stripPath?: boolean;
  timeout?: number;
  retries?: number;
  circuitBreaker?: boolean;
  rateLimit?: {
    windowMs: number;
    max: number;
  };
}

@Injectable()
export class GatewayService implements OnModuleInit {
  private readonly logger = new Logger(GatewayService.name);
  private readonly routes = new Map<string, ServiceRoute>();

  constructor(
    private readonly configService: ConfigService,
    private readonly serviceDiscovery: ServiceDiscoveryService,
    private readonly loadBalancer: LoadBalancerService,
    private readonly healthCheck: HealthCheckService,
  ) {}

  async onModuleInit() {
    await this.initializeRoutes();
    await this.startHealthChecking();
    this.logger.log('网关服务初始化完成');
  }

  /**
   * 初始化路由配置
   */
  private async initializeRoutes(): Promise<void> {
    const routes: ServiceRoute[] = [
      // 用户服务路由
      {
        path: '/api/users',
        serviceName: 'user-service',
        stripPath: true,
        timeout: 30000,
        retries: 3,
        circuitBreaker: true,
        rateLimit: { windowMs: 60000, max: 100 },
      },
      
      // 项目服务路由
      {
        path: '/api/projects',
        serviceName: 'project-service',
        stripPath: true,
        timeout: 30000,
        retries: 3,
        circuitBreaker: true,
        rateLimit: { windowMs: 60000, max: 50 },
      },
      
      // 资源库服务路由
      {
        path: '/api/assets',
        serviceName: 'asset-library-service',
        stripPath: true,
        timeout: 60000,
        retries: 2,
        circuitBreaker: true,
        rateLimit: { windowMs: 60000, max: 200 },
      },
      
      // 场景模板服务路由
      {
        path: '/api/templates',
        serviceName: 'scene-template-service',
        stripPath: true,
        timeout: 30000,
        retries: 3,
        circuitBreaker: true,
        rateLimit: { windowMs: 60000, max: 100 },
      },
      
      // AI模型服务路由
      {
        path: '/api/ai-models',
        serviceName: 'ai-model-service',
        stripPath: true,
        timeout: 120000, // AI推理可能需要更长时间
        retries: 1,
        circuitBreaker: true,
        rateLimit: { windowMs: 60000, max: 20 },
      },
      
      // 场景生成服务路由
      {
        path: '/api/generation',
        serviceName: 'scene-generation-service',
        stripPath: true,
        timeout: 300000, // 场景生成需要很长时间
        retries: 1,
        circuitBreaker: true,
        rateLimit: { windowMs: 60000, max: 10 },
      },
      
      // 渲染服务路由
      {
        path: '/api/render',
        serviceName: 'render-service',
        stripPath: true,
        timeout: 180000,
        retries: 2,
        circuitBreaker: true,
        rateLimit: { windowMs: 60000, max: 30 },
      },
      
      // 知识库服务路由
      {
        path: '/api/knowledge',
        serviceName: 'knowledge-service',
        stripPath: true,
        timeout: 30000,
        retries: 3,
        circuitBreaker: true,
        rateLimit: { windowMs: 60000, max: 100 },
      },
      
      // RAG引擎路由
      {
        path: '/api/rag',
        serviceName: 'rag-engine',
        stripPath: true,
        timeout: 60000,
        retries: 2,
        circuitBreaker: true,
        rateLimit: { windowMs: 60000, max: 50 },
      },
    ];

    for (const route of routes) {
      this.routes.set(route.path, route);
      this.logger.log(`注册路由: ${route.path} -> ${route.serviceName}`);
    }
  }

  /**
   * 获取路由配置
   */
  getRoute(path: string): ServiceRoute | undefined {
    // 精确匹配
    if (this.routes.has(path)) {
      return this.routes.get(path);
    }

    // 前缀匹配
    for (const [routePath, route] of this.routes) {
      if (path.startsWith(routePath)) {
        return route;
      }
    }

    return undefined;
  }

  /**
   * 获取服务实例
   */
  async getServiceInstance(serviceName: string): Promise<string | null> {
    const instances = await this.serviceDiscovery.getServiceInstances(serviceName);
    if (instances.length === 0) {
      return null;
    }

    // 使用负载均衡选择实例
    return this.loadBalancer.selectInstance(serviceName, instances);
  }

  /**
   * 获取所有路由
   */
  getAllRoutes(): ServiceRoute[] {
    return Array.from(this.routes.values());
  }

  /**
   * 添加路由
   */
  addRoute(route: ServiceRoute): void {
    this.routes.set(route.path, route);
    this.logger.log(`添加路由: ${route.path} -> ${route.serviceName}`);
  }

  /**
   * 移除路由
   */
  removeRoute(path: string): boolean {
    const removed = this.routes.delete(path);
    if (removed) {
      this.logger.log(`移除路由: ${path}`);
    }
    return removed;
  }

  /**
   * 更新路由
   */
  updateRoute(path: string, route: Partial<ServiceRoute>): boolean {
    const existingRoute = this.routes.get(path);
    if (!existingRoute) {
      return false;
    }

    const updatedRoute = { ...existingRoute, ...route };
    this.routes.set(path, updatedRoute);
    this.logger.log(`更新路由: ${path}`);
    return true;
  }

  /**
   * 获取网关统计信息
   */
  async getGatewayStats(): Promise<{
    totalRoutes: number;
    activeServices: number;
    healthyServices: number;
    totalRequests: number;
    errorRate: number;
  }> {
    const totalRoutes = this.routes.size;
    const services = new Set(Array.from(this.routes.values()).map(r => r.serviceName));
    const activeServices = services.size;
    
    let healthyServices = 0;
    for (const serviceName of services) {
      const isHealthy = await this.healthCheck.isServiceHealthy(serviceName);
      if (isHealthy) {
        healthyServices++;
      }
    }

    // 这里应该从监控系统获取实际的请求统计
    const totalRequests = 0; // 待实现
    const errorRate = 0; // 待实现

    return {
      totalRoutes,
      activeServices,
      healthyServices,
      totalRequests,
      errorRate,
    };
  }

  /**
   * 开始健康检查
   */
  private async startHealthChecking(): Promise<void> {
    // 获取所有唯一的服务名
    const services = new Set(Array.from(this.routes.values()).map(r => r.serviceName));
    
    // 为每个服务启动健康检查
    for (const serviceName of services) {
      this.healthCheck.startHealthCheck(serviceName);
    }

    this.logger.log(`开始监控 ${services.size} 个服务的健康状态`);
  }
}
