import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, In } from 'typeorm';
import { SceneTemplate, TemplateStatus } from './entities/scene-template.entity';
import { TemplateCategory } from '../categories/entities/template-category.entity';
import { TemplateParameter } from '../parameters/entities/template-parameter.entity';
import { CreateTemplateDto, UpdateTemplateDto, TemplateQueryDto } from './dto/create-template.dto';
import { CacheService } from '../../common/services/cache.service';
import { LoggerService } from '../../common/services/logger.service';
import { StorageService } from '../../common/services/storage.service';

export interface TemplateSearchResult {
  templates: SceneTemplate[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface TemplateInstantiationResult {
  sceneData: Record<string, any>;
  appliedParameters: Record<string, any>;
  metadata: Record<string, any>;
}

@Injectable()
export class TemplatesService {
  constructor(
    @InjectRepository(SceneTemplate)
    private readonly templateRepository: Repository<SceneTemplate>,
    @InjectRepository(TemplateCategory)
    private readonly categoryRepository: Repository<TemplateCategory>,
    @InjectRepository(TemplateParameter)
    private readonly parameterRepository: Repository<TemplateParameter>,
    private readonly cacheService: CacheService,
    private readonly logger: LoggerService,
    private readonly storageService: StorageService,
  ) {}

  /**
   * 创建模板
   */
  async create(createTemplateDto: CreateTemplateDto, userId: string): Promise<SceneTemplate> {
    const { categoryId, baseTemplateId, parameters, ...templateData } = createTemplateDto;

    // 验证分类是否存在
    const category = await this.categoryRepository.findOne({
      where: { id: categoryId },
    });
    if (!category) {
      throw new NotFoundException('分类不存在');
    }

    // 验证基础模板（如果指定）
    let baseTemplate: SceneTemplate | null = null;
    if (baseTemplateId) {
      baseTemplate = await this.templateRepository.findOne({
        where: { id: baseTemplateId },
      });
      if (!baseTemplate) {
        throw new NotFoundException('基础模板不存在');
      }
    }

    // 创建模板
    const template = this.templateRepository.create({
      ...templateData,
      category,
      baseTemplate,
      creator: { id: userId } as any,
    });

    const savedTemplate = await this.templateRepository.save(template);

    // 创建参数
    if (parameters && parameters.length > 0) {
      const templateParameters = parameters.map(paramData => 
        this.parameterRepository.create({
          ...paramData,
          template: savedTemplate,
        })
      );
      await this.parameterRepository.save(templateParameters);
    }

    // 清除相关缓存
    await this.clearRelatedCache(savedTemplate);

    this.logger.log(`模板创建成功: ${savedTemplate.id}`, 'TemplatesService');
    return await this.findOne(savedTemplate.id);
  }

  /**
   * 查找所有模板（分页）
   */
  async findAll(query: TemplateQueryDto): Promise<TemplateSearchResult> {
    const cacheKey = `templates:list:${JSON.stringify(query)}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const queryBuilder = this.createQueryBuilder(query);
    
    // 计算总数
    const total = await queryBuilder.getCount();
    
    // 分页查询
    const { page = 1, limit = 20 } = query;
    const skip = (page - 1) * limit;
    
    const templates = await queryBuilder
      .skip(skip)
      .take(limit)
      .getMany();

    const result: TemplateSearchResult = {
      templates,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };

    // 缓存结果
    await this.cacheService.set(cacheKey, result, 300); // 5分钟缓存

    return result;
  }

  /**
   * 根据ID查找模板
   */
  async findOne(id: string): Promise<SceneTemplate> {
    const cacheKey = `template:${id}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const template = await this.templateRepository.findOne({
      where: { id },
      relations: ['category', 'parameters', 'creator', 'baseTemplate', 'versions'],
    });

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 增加查看次数
    await this.incrementViewCount(id);

    // 缓存结果
    await this.cacheService.set(cacheKey, template, 600); // 10分钟缓存

    return template;
  }

  /**
   * 更新模板
   */
  async update(id: string, updateTemplateDto: UpdateTemplateDto, userId: string): Promise<SceneTemplate> {
    const template = await this.findOne(id);

    // 权限检查
    if (template.creator.id !== userId) {
      throw new ForbiddenException('无权限修改此模板');
    }

    const { categoryId, ...updateData } = updateTemplateDto;

    // 更新分类
    if (categoryId) {
      const category = await this.categoryRepository.findOne({
        where: { id: categoryId },
      });
      if (!category) {
        throw new NotFoundException('分类不存在');
      }
      template.category = category;
    }

    // 更新其他字段
    Object.assign(template, updateData);

    const updatedTemplate = await this.templateRepository.save(template);

    // 清除相关缓存
    await this.clearRelatedCache(updatedTemplate);

    this.logger.log(`模板更新成功: ${updatedTemplate.id}`, 'TemplatesService');
    return updatedTemplate;
  }

  /**
   * 删除模板（软删除）
   */
  async remove(id: string, userId: string): Promise<void> {
    const template = await this.findOne(id);

    // 权限检查
    if (template.creator.id !== userId) {
      throw new ForbiddenException('无权限删除此模板');
    }

    // 软删除
    template.deletedAt = new Date();
    await this.templateRepository.save(template);

    // 清除相关缓存
    await this.clearRelatedCache(template);

    this.logger.log(`模板删除成功: ${id}`, 'TemplatesService');
  }

  /**
   * 实例化模板
   */
  async instantiate(
    id: string,
    parameters: Record<string, any> = {},
  ): Promise<TemplateInstantiationResult> {
    const template = await this.findOne(id);

    if (!template.isPublished) {
      throw new BadRequestException('模板未发布，无法实例化');
    }

    // 验证参数
    const validationResult = await this.validateParameters(template, parameters);
    if (!validationResult.isValid) {
      throw new BadRequestException(`参数验证失败: ${validationResult.errors.join(', ')}`);
    }

    // 合并默认参数和用户参数
    const appliedParameters = {
      ...template.defaultParameters,
      ...parameters,
    };

    // 应用参数到场景数据
    const sceneData = this.applyParametersToScene(template.sceneData, appliedParameters);

    // 增加使用次数
    await this.incrementUseCount(id);

    return {
      sceneData,
      appliedParameters,
      metadata: {
        templateId: template.id,
        templateName: template.name,
        templateVersion: template.currentVersion,
        instantiatedAt: new Date().toISOString(),
      },
    };
  }

  /**
   * 获取热门模板
   */
  async getPopularTemplates(limit: number = 10): Promise<SceneTemplate[]> {
    const cacheKey = `templates:popular:${limit}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const templates = await this.templateRepository.find({
      where: {
        status: TemplateStatus.PUBLISHED,
        isPublic: true,
        deletedAt: null,
      },
      relations: ['category', 'creator'],
      order: {
        downloadCount: 'DESC',
        rating: 'DESC',
      },
      take: limit,
    });

    await this.cacheService.set(cacheKey, templates, 1800); // 30分钟缓存
    return templates;
  }

  /**
   * 获取最新模板
   */
  async getLatestTemplates(limit: number = 10): Promise<SceneTemplate[]> {
    const cacheKey = `templates:latest:${limit}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const templates = await this.templateRepository.find({
      where: {
        status: TemplateStatus.PUBLISHED,
        isPublic: true,
        deletedAt: null,
      },
      relations: ['category', 'creator'],
      order: {
        publishedAt: 'DESC',
      },
      take: limit,
    });

    await this.cacheService.set(cacheKey, templates, 600); // 10分钟缓存
    return templates;
  }

  /**
   * 获取精选模板
   */
  async getFeaturedTemplates(limit: number = 10): Promise<SceneTemplate[]> {
    const cacheKey = `templates:featured:${limit}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const templates = await this.templateRepository.find({
      where: {
        status: TemplateStatus.PUBLISHED,
        isPublic: true,
        isFeatured: true,
        deletedAt: null,
      },
      relations: ['category', 'creator'],
      order: {
        rating: 'DESC',
        downloadCount: 'DESC',
      },
      take: limit,
    });

    await this.cacheService.set(cacheKey, templates, 3600); // 1小时缓存
    return templates;
  }

  /**
   * 增加下载次数
   */
  async incrementDownloadCount(id: string): Promise<void> {
    await this.templateRepository.increment({ id }, 'downloadCount', 1);
    await this.cacheService.del(`template:${id}`);
  }

  /**
   * 增加使用次数
   */
  async incrementUseCount(id: string): Promise<void> {
    await this.templateRepository.increment({ id }, 'useCount', 1);
    await this.templateRepository.update({ id }, { lastUsedAt: new Date() });
    await this.cacheService.del(`template:${id}`);
  }

  /**
   * 增加查看次数
   */
  private async incrementViewCount(id: string): Promise<void> {
    await this.templateRepository.increment({ id }, 'viewCount', 1);
  }

  /**
   * 创建查询构建器
   */
  private createQueryBuilder(query: TemplateQueryDto): SelectQueryBuilder<SceneTemplate> {
    const queryBuilder = this.templateRepository
      .createQueryBuilder('template')
      .leftJoinAndSelect('template.category', 'category')
      .leftJoinAndSelect('template.creator', 'creator')
      .where('template.deletedAt IS NULL');

    // 公开模板过滤
    if (query.publicOnly) {
      queryBuilder.andWhere('template.isPublic = :isPublic', { isPublic: true });
      queryBuilder.andWhere('template.status = :status', { status: TemplateStatus.PUBLISHED });
    }

    // 精选模板过滤
    if (query.featuredOnly) {
      queryBuilder.andWhere('template.isFeatured = :isFeatured', { isFeatured: true });
    }

    // 搜索条件
    if (query.search) {
      queryBuilder.andWhere(
        '(template.name ILIKE :search OR template.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // 类型过滤
    if (query.type) {
      queryBuilder.andWhere('template.type = :type', { type: query.type });
    }

    // 分类过滤
    if (query.categoryId) {
      queryBuilder.andWhere('category.id = :categoryId', { categoryId: query.categoryId });
    }

    // 复杂度过滤
    if (query.complexity) {
      queryBuilder.andWhere('template.complexity = :complexity', { complexity: query.complexity });
    }

    // 许可证过滤
    if (query.license) {
      queryBuilder.andWhere('template.license = :license', { license: query.license });
    }

    // 标签过滤
    if (query.tags && query.tags.length > 0) {
      queryBuilder.andWhere('template.tags && :tags', { tags: query.tags });
    }

    // 排序
    const { sortBy = 'createdAt', sortOrder = 'DESC' } = query;
    queryBuilder.orderBy(`template.${sortBy}`, sortOrder);

    return queryBuilder;
  }

  /**
   * 验证模板参数
   */
  private async validateParameters(
    template: SceneTemplate,
    parameters: Record<string, any>,
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    for (const param of template.parameters) {
      const value = parameters[param.key];
      const validation = param.validateValue(value);
      
      if (!validation.isValid) {
        errors.push(...validation.errors);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 应用参数到场景数据
   */
  private applyParametersToScene(
    sceneData: Record<string, any>,
    parameters: Record<string, any>,
  ): Record<string, any> {
    // 深拷贝场景数据
    const result = JSON.parse(JSON.stringify(sceneData));

    // 递归替换参数占位符
    const replaceParameters = (obj: any): any => {
      if (typeof obj === 'string') {
        // 替换 ${parameterName} 格式的占位符
        return obj.replace(/\$\{([^}]+)\}/g, (match, paramName) => {
          return parameters[paramName] !== undefined ? parameters[paramName] : match;
        });
      } else if (Array.isArray(obj)) {
        return obj.map(replaceParameters);
      } else if (obj && typeof obj === 'object') {
        const newObj: any = {};
        for (const [key, value] of Object.entries(obj)) {
          newObj[key] = replaceParameters(value);
        }
        return newObj;
      }
      return obj;
    };

    return replaceParameters(result);
  }

  /**
   * 清除相关缓存
   */
  private async clearRelatedCache(template: SceneTemplate): Promise<void> {
    const patterns = [
      `template:${template.id}`,
      'templates:list:*',
      'templates:popular:*',
      'templates:latest:*',
      'templates:featured:*',
      `category:${template.category.id}:templates`,
    ];

    for (const pattern of patterns) {
      await this.cacheService.del(pattern);
    }
  }

  /**
   * 发布模板
   */
  async publish(id: string, userId: string): Promise<SceneTemplate> {
    const template = await this.findOne(id);

    // 权限检查
    if (template.creator.id !== userId) {
      throw new ForbiddenException('无权限发布此模板');
    }

    if (template.status !== TemplateStatus.APPROVED) {
      throw new BadRequestException('只有已审核的模板才能发布');
    }

    template.status = TemplateStatus.PUBLISHED;
    template.publishedAt = new Date();

    const publishedTemplate = await this.templateRepository.save(template);
    await this.clearRelatedCache(publishedTemplate);

    this.logger.log(`模板发布成功: ${id}`, 'TemplatesService');
    return publishedTemplate;
  }

  /**
   * 克隆模板
   */
  async clone(id: string, userId: string, name?: string): Promise<SceneTemplate> {
    const originalTemplate = await this.findOne(id);

    if (!originalTemplate.isPublic && originalTemplate.creator.id !== userId) {
      throw new ForbiddenException('无权限克隆此模板');
    }

    const clonedTemplate = this.templateRepository.create({
      name: name || `${originalTemplate.name} (副本)`,
      description: originalTemplate.description,
      instructions: originalTemplate.instructions,
      type: originalTemplate.type,
      complexity: originalTemplate.complexity,
      license: originalTemplate.license,
      sceneData: originalTemplate.sceneData,
      defaultParameters: originalTemplate.defaultParameters,
      metadata: {
        ...originalTemplate.metadata,
        clonedFrom: originalTemplate.id,
        clonedAt: new Date().toISOString(),
      },
      category: originalTemplate.category,
      baseTemplate: originalTemplate,
      creator: { id: userId } as any,
      tags: [...originalTemplate.tags],
      status: TemplateStatus.DRAFT,
      isPublic: false,
    });

    const savedTemplate = await this.templateRepository.save(clonedTemplate);

    // 克隆参数
    if (originalTemplate.parameters && originalTemplate.parameters.length > 0) {
      const clonedParameters = originalTemplate.parameters.map(param =>
        this.parameterRepository.create({
          ...param,
          id: undefined,
          template: savedTemplate,
        })
      );
      await this.parameterRepository.save(clonedParameters);
    }

    this.logger.log(`模板克隆成功: ${originalTemplate.id} -> ${savedTemplate.id}`, 'TemplatesService');
    return await this.findOne(savedTemplate.id);
  }
}
