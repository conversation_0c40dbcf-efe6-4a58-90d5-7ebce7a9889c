/**
 * InteractionPromptComponent.ts
 * 
 * 交互提示组件，用于显示交互提示
 */

import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector3 } from 'three';

/**
 * 提示位置类型枚举
 */
export enum PromptPositionType {
  /** 世界空间 */
  WORLD = 'world',
  /** 屏幕空间 */
  SCREEN = 'screen',
  /** 跟随对象 */
  FOLLOW = 'follow'
}

/**
 * 交互提示组件配置
 */
export interface InteractionPromptComponentConfig {
  /** 提示文本 */
  text?: string;
  /** 提示图标 */
  icon?: string;
  /** 提示位置类型 */
  positionType?: PromptPositionType;
  /** 提示位置偏移 */
  offset?: Vector3;
  /** 提示显示时间（毫秒，0表示一直显示） */
  duration?: number;
  /** 提示淡入时间（毫秒） */
  fadeInTime?: number;
  /** 提示淡出时间（毫秒） */
  fadeOutTime?: number;
  /** 提示背景颜色 */
  backgroundColor?: string;
  /** 提示文本颜色 */
  textColor?: string;
  /** 提示边框颜色 */
  borderColor?: string;
  /** 提示边框宽度 */
  borderWidth?: number;
  /** 提示边框圆角 */
  borderRadius?: number;
  /** 提示字体大小 */
  fontSize?: number;
  /** 提示字体 */
  fontFamily?: string;
  /** 提示内边距 */
  padding?: number;
  /** 是否自动隐藏 */
  autoHide?: boolean;
  /** 是否可见 */
  visible?: boolean;
}

/**
 * 交互提示组件
 * 用于显示交互提示
 */
export class InteractionPromptComponent extends Component {
  /** 提示文本 */
  private _text: string;

  /** 提示图标 */
  private _icon?: string;

  /** 提示位置类型 */
  private _positionType: PromptPositionType;

  /** 提示位置偏移 */
  private _offset: Vector3;

  /** 提示显示时间（毫秒，0表示一直显示） */
  private _duration: number;

  /** 提示淡入时间（毫秒） */
  private _fadeInTime: number;

  /** 提示淡出时间（毫秒） */
  private _fadeOutTime: number;

  /** 提示背景颜色 */
  private _backgroundColor: string;

  /** 提示文本颜色 */
  private _textColor: string;

  /** 提示边框颜色 */
  private _borderColor: string;

  /** 提示边框宽度 */
  private _borderWidth: number;

  /** 提示边框圆角 */
  private _borderRadius: number;

  /** 提示字体大小 */
  private _fontSize: number;

  /** 提示字体 */
  private _fontFamily: string;

  /** 提示内边距 */
  private _padding: number;

  /** 是否自动隐藏 */
  private _autoHide: boolean;

  /** 是否可见 */
  private _visible: boolean;

  /** HTML元素 */
  private element?: HTMLElement;

  /** 显示计时器 */
  private showTimer?: number;

  /** 当前不透明度 */
  private opacity: number = 0;

  /** 是否正在淡入 */
  private isFadingIn: boolean = false;

  /** 是否正在淡出 */
  private isFadingOut: boolean = false;

  /** 淡入开始时间 */
  private fadeInStartTime: number = 0;

  /** 淡出开始时间 */
  private fadeOutStartTime: number = 0;

  /**
   * 构造函数
   * @param entity 关联的实体
   * @param config 组件配置
   */
  constructor(entity: Entity, config: InteractionPromptComponentConfig = {}) {
    // 调用基类构造函数，传入组件类型名称
    super('InteractionPrompt');
    
    // 设置实体引用
    this.setEntity(entity);
    
    // 初始化属性
    this._text = config.text || '';
    this._icon = config.icon;
    this._positionType = config.positionType || PromptPositionType.FOLLOW;
    this._offset = config.offset || new Vector3(0, 1, 0);
    this._duration = config.duration !== undefined ? config.duration : 0;
    this._fadeInTime = config.fadeInTime || 200;
    this._fadeOutTime = config.fadeOutTime || 200;
    this._backgroundColor = config.backgroundColor || 'rgba(0, 0, 0, 0.7)';
    this._textColor = config.textColor || '#ffffff';
    this._borderColor = config.borderColor || '#ffffff';
    this._borderWidth = config.borderWidth !== undefined ? config.borderWidth : 1;
    this._borderRadius = config.borderRadius !== undefined ? config.borderRadius : 5;
    this._fontSize = config.fontSize || 14;
    this._fontFamily = config.fontFamily || 'Arial, sans-serif';
    this._padding = config.padding !== undefined ? config.padding : 10;
    this._autoHide = config.autoHide !== undefined ? config.autoHide : true;
    this._visible = config.visible !== undefined ? config.visible : false;
    
    // 创建HTML元素
    this.createHTMLElement();
    
    // 如果初始可见，则显示
    if (this._visible) {
      this.show();
    }
  }

  /**
   * 获取提示文本
   */
  get text(): string {
    return this._text;
  }

  /**
   * 设置提示文本
   */
  set text(value: string) {
    this._text = value;
    this.updateText();
  }

  /**
   * 获取提示图标
   */
  get icon(): string | undefined {
    return this._icon;
  }

  /**
   * 设置提示图标
   */
  set icon(value: string | undefined) {
    this._icon = value;
    this.updateIcon();
  }

  /**
   * 获取提示位置类型
   */
  get positionType(): PromptPositionType {
    return this._positionType;
  }

  /**
   * 设置提示位置类型
   */
  set positionType(value: PromptPositionType) {
    this._positionType = value;
    this.updatePosition();
  }

  /**
   * 获取提示位置偏移
   */
  get offset(): Vector3 {
    return this._offset;
  }

  /**
   * 设置提示位置偏移
   */
  set offset(value: Vector3) {
    this._offset = value;
    this.updatePosition();
  }

  /**
   * 获取是否可见
   */
  get visible(): boolean {
    return this._visible;
  }

  /**
   * 设置是否可见
   */
  set visible(value: boolean) {
    if (this._visible === value) return;
    
    this._visible = value;
    
    if (value) {
      this.show();
    } else {
      this.hide();
    }
  }

  /**
   * 创建HTML元素
   */
  private createHTMLElement(): void {
    // 如果已经存在，则返回
    if (this.element) return;
    
    // 创建元素
    this.element = document.createElement('div');
    this.element.className = 'interaction-prompt';
    this.element.style.position = 'absolute';
    this.element.style.pointerEvents = 'none';
    this.element.style.opacity = '0';
    this.element.style.transition = `opacity ${this._fadeInTime}ms ease-in-out`;
    this.element.style.backgroundColor = this._backgroundColor;
    this.element.style.color = this._textColor;
    this.element.style.border = `${this._borderWidth}px solid ${this._borderColor}`;
    this.element.style.borderRadius = `${this._borderRadius}px`;
    this.element.style.padding = `${this._padding}px`;
    this.element.style.fontSize = `${this._fontSize}px`;
    this.element.style.fontFamily = this._fontFamily;
    this.element.style.zIndex = '1000';
    this.element.style.display = 'none';
    
    // 创建图标元素（如果有）
    if (this._icon) {
      const iconElement = document.createElement('img');
      iconElement.src = this._icon;
      iconElement.style.marginRight = '5px';
      iconElement.style.verticalAlign = 'middle';
      this.element.appendChild(iconElement);
    }
    
    // 创建文本元素
    const textElement = document.createElement('span');
    textElement.textContent = this._text;
    this.element.appendChild(textElement);
    
    // 添加到文档
    document.body.appendChild(this.element);
  }

  /**
   * 更新文本
   */
  private updateText(): void {
    if (!this.element) return;
    
    // 查找文本元素
    const textElement = this.element.querySelector('span');
    if (textElement) {
      textElement.textContent = this._text;
    }
  }

  /**
   * 更新图标
   */
  private updateIcon(): void {
    if (!this.element) return;
    
    // 查找图标元素
    let iconElement = this.element.querySelector('img');
    
    // 如果有图标但没有图标元素，则创建
    if (this._icon && !iconElement) {
      iconElement = document.createElement('img');
      iconElement.style.marginRight = '5px';
      iconElement.style.verticalAlign = 'middle';
      this.element.insertBefore(iconElement, this.element.firstChild);
    }
    
    // 如果有图标元素，则更新
    if (iconElement) {
      if (this._icon) {
        (iconElement as HTMLImageElement).src = this._icon;
      } else {
        // 如果没有图标，则移除图标元素
        iconElement.parentElement?.removeChild(iconElement);
      }
    }
  }

  /**
   * 更新位置
   */
  private updatePosition(): void {
    if (!this.element) return;
    
    // 根据位置类型更新位置
    switch (this._positionType) {
      case PromptPositionType.WORLD:
        // TODO: 实现世界空间位置
        break;
      
      case PromptPositionType.SCREEN:
        // 屏幕空间位置
        this.element.style.left = `${this._offset.x}px`;
        this.element.style.top = `${this._offset.y}px`;
        break;
      
      case PromptPositionType.FOLLOW:
        // TODO: 实现跟随对象位置
        break;
    }
  }

  /**
   * 显示提示
   */
  show(): void {
    if (!this.element) return;
    
    // 清除之前的计时器
    if (this.showTimer !== undefined) {
      clearTimeout(this.showTimer);
      this.showTimer = undefined;
    }
    
    // 显示元素
    this.element.style.display = 'block';
    
    // 开始淡入
    this.startFadeIn();
    
    // 如果设置了持续时间且自动隐藏，则设置计时器
    if (this._duration > 0 && this._autoHide) {
      this.showTimer = window.setTimeout(() => {
        this.hide();
      }, this._duration);
    }
  }

  /**
   * 隐藏提示
   */
  hide(): void {
    if (!this.element) return;
    
    // 清除之前的计时器
    if (this.showTimer !== undefined) {
      clearTimeout(this.showTimer);
      this.showTimer = undefined;
    }
    
    // 开始淡出
    this.startFadeOut();
  }

  /**
   * 开始淡入
   */
  private startFadeIn(): void {
    this.isFadingIn = true;
    this.isFadingOut = false;
    this.fadeInStartTime = Date.now();
  }

  /**
   * 开始淡出
   */
  private startFadeOut(): void {
    this.isFadingIn = false;
    this.isFadingOut = true;
    this.fadeOutStartTime = Date.now();
  }

  /**
   * 更新组件
   * @param deltaTime 时间增量（秒）
   */
  update(deltaTime: number): void {
    if (!this.element) return;
    
    // 更新位置
    this.updatePosition();
    
    // 处理淡入淡出
    const now = Date.now();
    
    if (this.isFadingIn) {
      const elapsed = now - this.fadeInStartTime;
      const progress = Math.min(elapsed / this._fadeInTime, 1);
      this.opacity = progress;
      
      if (progress >= 1) {
        this.isFadingIn = false;
      }
    } else if (this.isFadingOut) {
      const elapsed = now - this.fadeOutStartTime;
      const progress = Math.min(elapsed / this._fadeOutTime, 1);
      this.opacity = 1 - progress;
      
      if (progress >= 1) {
        this.isFadingOut = false;
        this.element.style.display = 'none';
      }
    }
    
    // 更新不透明度
    this.element.style.opacity = this.opacity.toString();
  }

  /**
   * 销毁组件
   */
  dispose(): void {
    // 清除计时器
    if (this.showTimer !== undefined) {
      clearTimeout(this.showTimer);
      this.showTimer = undefined;
    }
    
    // 移除HTML元素
    if (this.element && this.element.parentElement) {
      this.element.parentElement.removeChild(this.element);
    }
    
    this.element = undefined;
    
    // 调用基类的销毁方法
    super.dispose();
  }
}
