/**
 * Cannon.js物理引擎封装
 * 用于物理驱动动画
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
/**
 * 物理对象类型
 */
export declare enum PhysicsObjectType {
    /** 刚体 */
    RIGID_BODY = "rigid_body",
    /** 软体 */
    SOFT_BODY = "soft_body",
    /** 布料 */
    CLOTH = "cloth",
    /** 绳索 */
    ROPE = "rope",
    /** 流体 */
    FLUID = "fluid"
}
/**
 * 物理对象配置
 */
export interface PhysicsObjectConfig {
    /** 对象类型 */
    type: PhysicsObjectType;
    /** 质量 */
    mass?: number;
    /** 位置 */
    position?: THREE.Vector3;
    /** 旋转 */
    quaternion?: THREE.Quaternion;
    /** 形状 */
    shape?: CANNON.Shape;
    /** 材质 */
    material?: CANNON.Material;
    /** 是否固定 */
    isKinematic?: boolean;
    /** 线性阻尼 */
    linearDamping?: number;
    /** 角阻尼 */
    angularDamping?: number;
    /** 碰撞组 */
    collisionGroup?: number;
    /** 碰撞掩码 */
    collisionMask?: number;
    /** 用户数据 */
    userData?: any;
}
/**
 * 物理约束类型
 */
export declare enum PhysicsConstraintType {
    /** 点对点 */
    POINT_TO_POINT = "point_to_point",
    /** 铰链 */
    HINGE = "hinge",
    /** 距离 */
    DISTANCE = "distance",
    /** 锁定 */
    LOCK = "lock",
    /** 弹簧 */
    SPRING = "spring"
}
/**
 * 物理约束配置
 */
export interface PhysicsConstraintConfig {
    /** 约束类型 */
    type: PhysicsConstraintType;
    /** 物体A */
    bodyA: CANNON.Body;
    /** 物体B */
    bodyB: CANNON.Body;
    /** 局部点A */
    pivotA?: CANNON.Vec3;
    /** 局部点B */
    pivotB?: CANNON.Vec3;
    /** 局部轴A */
    axisA?: CANNON.Vec3;
    /** 局部轴B */
    axisB?: CANNON.Vec3;
    /** 最大力 */
    maxForce?: number;
    /** 碰撞启用 */
    collideConnected?: boolean;
    /** 用户数据 */
    userData?: any;
}
/**
 * 物理引擎配置
 */
export interface PhysicsEngineConfig {
    /** 重力 */
    gravity?: CANNON.Vec3;
    /** 默认材质 */
    defaultMaterial?: CANNON.Material;
    /** 是否允许休眠 */
    allowSleep?: boolean;
    /** 迭代次数 */
    iterations?: number;
    /** 是否使用四元数归一化 */
    quatNormalizeFast?: boolean;
    /** 是否使用四元数插值 */
    quatNormalizeSkip?: number;
    /** 是否启用调试 */
    debug?: boolean;
}
/**
 * Cannon.js物理引擎封装
 */
export declare class CannonPhysicsEngine {
    /** 物理世界 */
    private world;
    /** 物理对象映射 */
    private bodies;
    /** 物理约束映射 */
    private constraints;
    /** 物理材质映射 */
    private materials;
    /** 事件发射器 */
    private eventEmitter;
    /** 是否启用调试 */
    private debug;
    /** 累积时间 */
    private accumulator;
    /** 固定时间步长 */
    private fixedTimeStep;
    /** 最大子步数 */
    private maxSubSteps;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: PhysicsEngineConfig);
    /**
     * 创建物理对象
     * @param id 对象ID
     * @param config 配置
     * @returns 物理对象
     */
    createBody(id: string, config: PhysicsObjectConfig): CANNON.Body;
    /**
     * 移除物理对象
     * @param id 对象ID
     * @returns 是否成功移除
     */
    removeBody(id: string): boolean;
    /**
     * 获取物理对象
     * @param id 对象ID
     * @returns 物理对象
     */
    getBody(id: string): CANNON.Body | null;
    /**
     * 创建物理约束
     * @param id 约束ID
     * @param config 配置
     * @returns 物理约束
     */
    createConstraint(id: string, config: PhysicsConstraintConfig): CANNON.Constraint;
    /**
     * 移除物理约束
     * @param id 约束ID
     * @returns 是否成功移除
     */
    removeConstraint(id: string): boolean;
    /**
     * 获取物理约束
     * @param id 约束ID
     * @returns 物理约束
     */
    getConstraint(id: string): CANNON.Constraint | null;
    /**
     * 创建物理材质
     * @param id 材质ID
     * @param friction 摩擦系数
     * @param restitution 弹性系数
     * @returns 物理材质
     */
    createMaterial(id: string, friction?: number, restitution?: number): CANNON.Material;
    /**
     * 获取物理材质
     * @param id 材质ID
     * @returns 物理材质
     */
    getMaterial(id: string): CANNON.Material | null;
    /**
     * 更新物理世界
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    addEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    removeEventListener(event: string, callback: (data: any) => void): void;
    /**
     * 获取物理世界
     * @returns 物理世界
     */
    getWorld(): CANNON.World;
    /**
     * 清理物理世界
     */
    clear(): void;
    /**
     * 销毁物理引擎
     */
    dispose(): void;
}
