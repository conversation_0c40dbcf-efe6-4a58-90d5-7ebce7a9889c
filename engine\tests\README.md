# DL（Digital Learning）引擎单元测试

本目录包含DL（Digital Learning）引擎底层引擎部分的单元测试。这些测试确保引擎的各个组件和系统按预期工作，并帮助防止回归错误。

## 测试结构

测试按照引擎的模块结构组织，每个主要模块都有对应的测试目录：

- `core/` - 核心模块测试
- `rendering/` - 渲染模块测试
- `physics/` - 物理模块测试
- `animation/` - 动画模块测试
- `particles/` - 粒子系统测试
- `input/` - 输入系统测试
- `scene/` - 场景管理系统测试
- `network/` - 网络系统测试
- `visualscript/` - 视觉脚本系统测试
- `interaction/` - 交互系统测试
- `avatar/` - 头像系统测试
- `mocap/` - 动作捕捉系统测试

## 运行测试

可以使用以下命令运行测试：

```bash
# 运行所有测试
npm test

# 运行特定模块的测试
npm test -- --testPathPattern=core
npm test -- --testPathPattern=physics

# 运行特定测试文件
npm test -- physics/PhysicsSystem.test.ts

# 监视模式
npm test -- --watch
```

## 测试框架

我们使用以下工具进行测试：

- **Vitest** - 测试运行器和断言库
- **Three.js测试工具** - 用于测试3D渲染功能
- **Mock对象** - 用于模拟外部依赖

## 编写测试指南

编写测试时，请遵循以下准则：

1. 每个测试文件应该对应一个源文件
2. 使用描述性的测试名称
3. 测试应该是独立的，不依赖于其他测试的状态
4. 使用`beforeEach`和`afterEach`设置和清理测试环境
5. 测试应该覆盖正常情况和边缘情况
6. 使用模拟对象隔离被测试的代码

## 测试覆盖率目标

我们的目标是达到以下测试覆盖率：

- 核心模块: 90%+
- 物理系统: 85%+
- 渲染系统: 80%+
- 其他模块: 75%+

## 待完成的测试

以下是需要实现或完善的测试：

### 核心模块
- [ ] Engine.test.ts
- [ ] World.test.ts
- [ ] Entity.test.ts
- [ ] Component.test.ts
- [ ] System.test.ts

### 物理模块
- [ ] PhysicsSystem.test.ts
- [ ] PhysicsBody.test.ts
- [ ] PhysicsCollider.test.ts
- [ ] SoftBodySystem.test.ts

### 动画模块
- [x] AnimationClip.test.ts
- [ ] Animator.test.ts
- [ ] AnimationStateMachine.test.ts
- [ ] FacialAnimationSystem.test.ts

### 粒子模块
- [ ] ParticleSystem.test.ts
- [ ] ParticleEmitter.test.ts

### 输入模块
- [x] InputAction.test.ts
- [x] InputDevice.test.ts
- [x] InputManager.test.ts
- [ ] InputSystem.test.ts

### 场景模块
- [ ] Scene.test.ts
- [ ] SceneManager.test.ts
- [ ] Transform.test.ts

### 网络模块
- [ ] NetworkSystem.test.ts
- [ ] NetworkManager.test.ts
- [ ] EntitySyncManager.test.ts

### 视觉脚本模块
- [ ] VisualScriptSystem.test.ts
- [ ] VisualScriptEngine.test.ts
- [ ] NodeRegistry.test.ts

### 交互模块
- [x] InteractionSystem.test.ts
- [x] GrabSystem.test.ts
- [ ] InteractableComponent.test.ts

### 头像模块
- [ ] AvatarSystem.test.ts
- [ ] FacialAnimationSystem.test.ts
- [ ] LipSyncSystem.test.ts

### 动作捕捉模块
- [ ] MotionCaptureSystem.test.ts
- [ ] MotionCaptureComponent.test.ts
- [ ] PoseEvaluator.test.ts
