/**
 * 项目服务
 */
import { Injectable, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class ProjectService {
  constructor(
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
  ) {}

  /**
   * 获取项目信息
   * @param projectId 项目ID
   * @returns 项目信息
   */
  async getProject(projectId: string): Promise<any> {
    return firstValueFrom(
      this.projectService.send({ cmd: 'getProject' }, { projectId }),
    );
  }

  /**
   * 获取场景信息
   * @param sceneId 场景ID
   * @returns 场景信息
   */
  async getScene(sceneId: string): Promise<any> {
    return firstValueFrom(
      this.projectService.send({ cmd: 'getScene' }, { sceneId }),
    );
  }

  /**
   * 检查用户对项目的权限
   * @param userId 用户ID
   * @param projectId 项目ID
   * @param requiredRoles 所需角色
   * @returns 是否有权限
   */
  async checkPermission(
    userId: string,
    projectId: string,
    requiredRoles: string[],
  ): Promise<boolean> {
    try {
      const result = await firstValueFrom(
        this.projectService.send(
          { cmd: 'checkProjectPermission' },
          { userId, projectId, roles: requiredRoles },
        ),
      );
      
      return result;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取项目成员
   * @param projectId 项目ID
   * @returns 项目成员列表
   */
  async getProjectMembers(projectId: string): Promise<any[]> {
    return firstValueFrom(
      this.projectService.send({ cmd: 'getProjectMembers' }, { projectId }),
    );
  }
}
