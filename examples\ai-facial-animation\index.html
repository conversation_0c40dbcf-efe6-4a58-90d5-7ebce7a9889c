<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI面部动画生成示例</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      background-color: #1890ff;
      color: white;
      padding: 20px;
      text-align: center;
      margin-bottom: 20px;
      border-radius: 4px;
    }
    
    h1 {
      margin: 0;
      font-size: 28px;
    }
    
    .description {
      margin-bottom: 20px;
      line-height: 1.6;
    }
    
    .main-content {
      display: flex;
      gap: 20px;
    }
    
    .preview-container {
      flex: 1;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 20px;
      min-height: 500px;
    }
    
    .controls-container {
      width: 300px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }
    
    .preview-canvas {
      width: 100%;
      height: 400px;
      background-color: #000;
      margin-bottom: 20px;
      border-radius: 4px;
    }
    
    .control-group {
      margin-bottom: 20px;
    }
    
    .control-group h3 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 16px;
      color: #1890ff;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    input, select, textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      margin-bottom: 10px;
      font-family: inherit;
    }
    
    textarea {
      min-height: 100px;
      resize: vertical;
    }
    
    button {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }
    
    button:hover {
      background-color: #40a9ff;
    }
    
    button:disabled {
      background-color: #d9d9d9;
      cursor: not-allowed;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
    }
    
    .status {
      margin-top: 10px;
      padding: 10px;
      border-radius: 4px;
      background-color: #f0f0f0;
      font-size: 14px;
    }
    
    .status.success {
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      color: #52c41a;
    }
    
    .status.error {
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      color: #ff4d4f;
    }
    
    .status.loading {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
      color: #1890ff;
    }
    
    .examples {
      margin-top: 20px;
    }
    
    .example-item {
      margin-bottom: 10px;
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .example-item:hover {
      background-color: #e0e0e0;
    }
    
    footer {
      margin-top: 20px;
      text-align: center;
      color: #999;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>AI面部动画生成示例</h1>
    </header>
    
    <div class="description">
      <p>本示例展示了如何使用AI面部动画生成功能，通过情感分析和AI模型生成自然的面部动画。您可以输入情感描述文本，选择不同的AI模型和参数，生成面部动画并应用到角色上。</p>
    </div>
    
    <div class="main-content">
      <div class="preview-container">
        <h2>预览</h2>
        <div class="preview-canvas" id="preview-canvas"></div>
        
        <div class="status" id="status">
          准备就绪，请输入情感描述并点击"生成"按钮。
        </div>
      </div>
      
      <div class="controls-container">
        <div class="control-group">
          <h3>情感描述</h3>
          <textarea id="prompt" placeholder="请输入情感描述，例如：角色感到非常开心，然后突然惊讶，最后回到平静状态"></textarea>
        </div>
        
        <div class="control-group">
          <h3>参数设置</h3>
          
          <label for="duration">持续时间（秒）</label>
          <input type="range" id="duration" min="1" max="10" step="0.5" value="5">
          <span id="duration-value">5.0</span>秒
          
          <label for="model-type">模型类型</label>
          <select id="model-type">
            <option value="basic">基础模型</option>
            <option value="advanced" selected>高级模型</option>
            <option value="bert">BERT模型</option>
          </select>
          
          <label for="intensity">强度</label>
          <input type="range" id="intensity" min="0.1" max="1" step="0.1" value="0.8">
          <span id="intensity-value">0.8</span>
          
          <label for="loop">循环播放</label>
          <input type="checkbox" id="loop" checked>
          
          <label for="advanced-features">高级特性</label>
          <input type="checkbox" id="advanced-features" checked>
        </div>
        
        <div class="button-group">
          <button id="generate-btn">生成</button>
          <button id="apply-btn" disabled>应用</button>
          <button id="preview-btn" disabled>预览</button>
        </div>
        
        <div class="examples">
          <h3>示例</h3>
          <div class="example-item" data-prompt="角色感到非常开心，然后突然惊讶，最后回到平静状态">
            开心 → 惊讶 → 平静
          </div>
          <div class="example-item" data-prompt="角色先是平静地思考，然后逐渐变得兴奋，最后露出满意的微笑">
            思考 → 兴奋 → 满意
          </div>
          <div class="example-item" data-prompt="角色表现出复杂的情感变化，从悲伤到愤怒再到释然">
            悲伤 → 愤怒 → 释然
          </div>
          <div class="example-item" data-prompt="我感到非常高兴，但同时也有一点担忧">
            高兴 + 担忧（混合情感）
          </div>
        </div>
      </div>
    </div>
    
    <footer>
      <p>© 2023 AI面部动画生成示例 | 基于newsystem引擎</p>
    </footer>
  </div>
  
  <script type="module">
    import { World } from '../../engine/src/core/World.js';
    import { createAIFacialAnimationExample } from './AIFacialAnimationExample.js';
    
    // 初始化
    const world = new World();
    const example = createAIFacialAnimationExample(world);
    
    // 获取DOM元素
    const promptInput = document.getElementById('prompt');
    const durationInput = document.getElementById('duration');
    const durationValue = document.getElementById('duration-value');
    const modelTypeSelect = document.getElementById('model-type');
    const intensityInput = document.getElementById('intensity');
    const intensityValue = document.getElementById('intensity-value');
    const loopCheckbox = document.getElementById('loop');
    const advancedFeaturesCheckbox = document.getElementById('advanced-features');
    const generateBtn = document.getElementById('generate-btn');
    const applyBtn = document.getElementById('apply-btn');
    const previewBtn = document.getElementById('preview-btn');
    const statusElement = document.getElementById('status');
    const exampleItems = document.querySelectorAll('.example-item');
    
    // 更新值显示
    durationInput.addEventListener('input', () => {
      durationValue.textContent = durationInput.value;
    });
    
    intensityInput.addEventListener('input', () => {
      intensityValue.textContent = intensityInput.value;
    });
    
    // 示例点击事件
    exampleItems.forEach(item => {
      item.addEventListener('click', () => {
        promptInput.value = item.dataset.prompt;
      });
    });
    
    // 生成按钮点击事件
    generateBtn.addEventListener('click', () => {
      // 获取参数
      const prompt = promptInput.value.trim();
      const duration = parseFloat(durationInput.value);
      const modelType = modelTypeSelect.value;
      const intensity = parseFloat(intensityInput.value);
      const loop = loopCheckbox.checked;
      const useAdvancedFeatures = advancedFeaturesCheckbox.checked;
      
      // 验证参数
      if (!prompt) {
        setStatus('请输入情感描述', 'error');
        return;
      }
      
      // 设置状态
      setStatus('正在生成动画...', 'loading');
      
      // 禁用按钮
      generateBtn.disabled = true;
      
      // 生成动画
      setTimeout(() => {
        // 模拟生成完成
        setStatus('动画生成成功！', 'success');
        
        // 启用按钮
        generateBtn.disabled = false;
        applyBtn.disabled = false;
        previewBtn.disabled = false;
      }, 2000);
    });
    
    // 设置状态
    function setStatus(message, type = '') {
      statusElement.textContent = message;
      statusElement.className = 'status';
      
      if (type) {
        statusElement.classList.add(type);
      }
    }
  </script>
</body>
</html>
