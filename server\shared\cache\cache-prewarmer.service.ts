/**
 * 缓存预热服务
 * 用于在应用启动时预加载常用数据
 */
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DistributedCacheService } from './distributed-cache.service';

/**
 * 预热项配置
 */
export interface PrewarmItem {
  /**
   * 缓存键
   */
  key: string;
  
  /**
   * 数据获取函数
   */
  fetchFn: () => Promise<any>;
  
  /**
   * 缓存TTL（毫秒）
   */
  ttl?: number;
  
  /**
   * 优先级（0-100），数字越大优先级越高
   */
  priority?: number;
  
  /**
   * 描述
   */
  description?: string;
}

/**
 * 缓存预热配置
 */
export interface CachePrewarmerConfig {
  /**
   * 是否启用预热
   */
  enabled?: boolean;
  
  /**
   * 预热项列表
   */
  items?: PrewarmItem[];
  
  /**
   * 预热超时（毫秒）
   */
  timeout?: number;
  
  /**
   * 并发预热数量
   */
  concurrency?: number;
  
  /**
   * 是否在预热失败时继续
   */
  continueOnError?: boolean;
  
  /**
   * 是否启用调试日志
   */
  debug?: boolean;
}

@Injectable()
export class CachePrewarmerService implements OnModuleInit {
  private readonly logger = new Logger(CachePrewarmerService.name);
  
  // 默认配置
  private readonly defaultConfig: Required<CachePrewarmerConfig> = {
    enabled: true,
    items: [],
    timeout: 30000, // 30秒
    concurrency: 5,
    continueOnError: true,
    debug: false,
  };
  
  // 合并后的配置
  private readonly config: Required<CachePrewarmerConfig>;
  
  constructor(
    private readonly cacheService: DistributedCacheService,
    config: CachePrewarmerConfig = {},
  ) {
    // 合并配置
    this.config = {
      ...this.defaultConfig,
      ...config,
      items: [...(this.defaultConfig.items || []), ...(config.items || [])],
    };
  }
  
  /**
   * 模块初始化
   */
  async onModuleInit() {
    if (!this.config.enabled || this.config.items.length === 0) {
      this.logger.log('缓存预热已禁用或没有预热项');
      return;
    }
    
    // 启动预热
    await this.prewarm();
  }
  
  /**
   * 添加预热项
   * @param item 预热项
   */
  addPrewarmItem(item: PrewarmItem): void {
    this.config.items.push(item);
  }
  
  /**
   * 添加多个预热项
   * @param items 预热项列表
   */
  addPrewarmItems(items: PrewarmItem[]): void {
    this.config.items.push(...items);
  }
  
  /**
   * 执行预热
   */
  async prewarm(): Promise<void> {
    if (!this.config.enabled || this.config.items.length === 0) {
      return;
    }
    
    this.logger.log(`开始缓存预热，预热项数量: ${this.config.items.length}`);
    const startTime = Date.now();
    
    // 按优先级排序
    const sortedItems = [...this.config.items].sort((a, b) => 
      (b.priority || 0) - (a.priority || 0)
    );
    
    // 创建预热任务
    const tasks = sortedItems.map(item => this.prewarmItem(item));
    
    // 设置超时
    const timeoutPromise = new Promise<void>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`缓存预热超时，超过 ${this.config.timeout}ms`));
      }, this.config.timeout);
    });
    
    try {
      // 执行预热任务，带并发限制
      await Promise.race([
        this.runWithConcurrencyLimit(tasks, this.config.concurrency),
        timeoutPromise,
      ]);
      
      const duration = Date.now() - startTime;
      this.logger.log(`缓存预热完成，耗时: ${duration}ms`);
    } catch (error) {
      this.logger.error(`缓存预热失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 预热单个项
   * @param item 预热项
   */
  private async prewarmItem(item: PrewarmItem): Promise<void> {
    if (this.config.debug) {
      this.logger.debug(`预热缓存项: ${item.key}${item.description ? ` (${item.description})` : ''}`);
    }
    
    const startTime = Date.now();
    
    try {
      // 获取数据并缓存
      await this.cacheService.get(item.key, item.fetchFn, item.ttl);
      
      const duration = Date.now() - startTime;
      if (this.config.debug) {
        this.logger.debug(`预热缓存项完成: ${item.key}, 耗时: ${duration}ms`);
      }
    } catch (error) {
      this.logger.warn(`预热缓存项失败: ${item.key}, 错误: ${error.message}`);
      
      if (!this.config.continueOnError) {
        throw error;
      }
    }
  }
  
  /**
   * 使用并发限制运行任务
   * @param tasks 任务列表
   * @param concurrency 并发数量
   */
  private async runWithConcurrencyLimit(tasks: Promise<void>[], concurrency: number): Promise<void> {
    // 如果任务数量小于并发数量，直接全部执行
    if (tasks.length <= concurrency) {
      await Promise.all(tasks);
      return;
    }
    
    // 否则，分批执行
    const taskQueue = [...tasks];
    const runningTasks: Promise<void>[] = [];
    
    // 初始化运行任务
    for (let i = 0; i < concurrency && taskQueue.length > 0; i++) {
      const task = taskQueue.shift()!;
      runningTasks.push(task);
    }
    
    // 等待任务完成并添加新任务
    while (runningTasks.length > 0) {
      // 等待任意一个任务完成
      await Promise.race(runningTasks);
      
      // 移除已完成的任务
      const completedIndex = await Promise.race(
        runningTasks.map(async (task, index) => {
          try {
            await Promise.resolve(task);
            return index;
          } catch {
            return index;
          }
        })
      );
      
      runningTasks.splice(completedIndex, 1);
      
      // 添加新任务
      if (taskQueue.length > 0) {
        const task = taskQueue.shift()!;
        runningTasks.push(task);
      }
    }
  }
}
