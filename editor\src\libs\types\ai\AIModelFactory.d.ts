/**
 * AI模型工厂
 * 用于创建和管理AI模型实例
 */
import { AIModelType } from './AIModelType';
import { AIModelConfig } from './AIModelConfig';
import { IAIModel } from './models/IAIModel';
/**
 * AI模型工厂配置
 */
export interface AIModelFactoryConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否使用本地模型 */
    useLocalModels?: boolean;
    /** 模型API密钥 */
    apiKeys?: Record<string, string>;
    /** 模型基础URL */
    baseUrls?: Record<string, string>;
    /** 模型版本 */
    modelVersions?: Record<string, string>;
}
/**
 * AI模型工厂
 */
export declare class AIModelFactory {
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 模型缓存 */
    private modelCache;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: AIModelFactoryConfig);
    /**
     * 创建模型
     * @param modelType 模型类型
     * @param config 模型配置
     * @returns 模型实例
     */
    createModel(modelType: AIModelType, config?: AIModelConfig): IAIModel | null;
    /**
     * 生成模型ID
     * @param modelType 模型类型
     * @param config 模型配置
     * @returns 模型ID
     */
    private generateModelId;
    /**
     * 获取模型
     * @param modelId 模型ID
     * @returns 模型实例
     */
    getModel(modelId: string): IAIModel | null;
    /**
     * 获取所有模型
     * @returns 模型实例映射
     */
    getAllModels(): Map<string, IAIModel>;
    /**
     * 释放模型
     * @param modelId 模型ID
     * @returns 是否成功
     */
    releaseModel(modelId: string): boolean;
    /**
     * 释放所有模型
     */
    releaseAllModels(): void;
    /**
     * 销毁
     */
    dispose(): void;
}
