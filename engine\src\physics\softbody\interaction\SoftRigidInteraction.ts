/**
 * 软体与刚体交互系统
 * 实现软体与刚体之间的双向交互
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { SoftBodyComponent } from '../SoftBodyComponent';
import { PhysicsBodyComponent   } from '../../components/PhysicsBodyComponent';
import type { Entity } from '../../../core/Entity';
import { SpatialPartitioning } from '../optimization/SpatialPartitioning';

/**
 * 软体与刚体交互系统选项
 */
export interface SoftRigidInteractionOptions {
  /** 是否启用交互 */
  enabled?: boolean;
  /** 碰撞检测半径 */
  collisionRadius?: number;
  /** 碰撞响应强度 */
  collisionResponse?: number;
  /** 摩擦系数 */
  friction?: number;
  /** 是否使用空间分区 */
  useSpatialPartitioning?: boolean;
}

/**
 * 软体与刚体交互系统
 * 处理软体与刚体之间的碰撞和交互
 */
export class SoftRigidInteraction {
  /** 是否启用交互 */
  private enabled: boolean;
  
  /** 碰撞检测半径 */
  private collisionRadius: number;
  
  /** 碰撞响应强度 */
  private collisionResponse: number;
  
  /** 摩擦系数 */
  private friction: number;
  
  /** 是否使用空间分区 */
  private useSpatialPartitioning: boolean;
  
  /** 空间分区系统 */
  private spatialPartitioning: SpatialPartitioning | null = null;
  
  /** 软体组件列表 */
  private softBodies: SoftBodyComponent[] = [];
  
  /** 刚体组件列表 */
  private rigidBodies: PhysicsBodyComponent[] = [];
  
  /** 碰撞对列表 */
  private collisionPairs: Array<{
    softBodyParticle: CANNON.Body;
    rigidBody: CANNON.Body;
    penetration: number;
    normal: CANNON.Vec3;
  }> = [];
  
  /**
   * 创建软体与刚体交互系统
   * @param options 软体与刚体交互系统选项
   */
  constructor(options: SoftRigidInteractionOptions = {}) {
    this.enabled = options.enabled !== undefined ? options.enabled : true;
    this.collisionRadius = options.collisionRadius || 0.1;
    this.collisionResponse = options.collisionResponse || 1.0;
    this.friction = options.friction || 0.3;
    this.useSpatialPartitioning = options.useSpatialPartitioning !== undefined ? options.useSpatialPartitioning : true;
    
    // 如果启用空间分区，创建空间分区系统
    if (this.useSpatialPartitioning) {
      this.spatialPartitioning = new SpatialPartitioning();
    }
  }
  
  /**
   * 添加软体组件
   * @param softBody 软体组件
   */
  public addSoftBody(softBody: SoftBodyComponent): void {
    if (!this.softBodies.includes(softBody)) {
      this.softBodies.push(softBody);
    }
  }
  
  /**
   * 移除软体组件
   * @param softBody 软体组件
   */
  public removeSoftBody(softBody: SoftBodyComponent): void {
    const index = this.softBodies.indexOf(softBody);
    if (index !== -1) {
      this.softBodies.splice(index, 1);
    }
  }
  
  /**
   * 添加刚体组件
   * @param rigidBody 刚体组件
   */
  public addRigidBody(rigidBody: PhysicsBodyComponent): void {
    if (!this.rigidBodies.includes(rigidBody)) {
      this.rigidBodies.push(rigidBody);
    }
  }
  
  /**
   * 移除刚体组件
   * @param rigidBody 刚体组件
   */
  public removeRigidBody(rigidBody: PhysicsBodyComponent): void {
    const index = this.rigidBodies.indexOf(rigidBody);
    if (index !== -1) {
      this.rigidBodies.splice(index, 1);
    }
  }
  
  /**
   * 更新交互
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled) return;
    
    // 清空碰撞对列表
    this.collisionPairs.length = 0;
    
    // 更新空间分区
    if (this.useSpatialPartitioning && this.spatialPartitioning) {
      this.updateSpatialPartitioning();
    }
    
    // 检测碰撞
    this.detectCollisions();
    
    // 解决碰撞
    this.resolveCollisions(deltaTime);
  }
  
  /**
   * 更新空间分区
   */
  private updateSpatialPartitioning(): void {
    if (!this.spatialPartitioning) return;
    
    // 清空空间分区
    this.spatialPartitioning.clear();
    
    // 添加所有粒子到空间分区
    for (const softBody of this.softBodies) {
      const particles = softBody.getParticles();
      if (particles) {
        for (const particle of particles) {
          this.spatialPartitioning.addParticle(particle);
        }
      }
    }
  }
  
  /**
   * 检测碰撞
   */
  private detectCollisions(): void {
    // 遍历所有刚体
    for (const rigidBody of this.rigidBodies) {
      const cannonBody = rigidBody.getCannonBody();
      if (!cannonBody) continue;
      
      // 获取刚体的碰撞形状
      const shapes = cannonBody.shapes;
      if (!shapes || shapes.length === 0) continue;
      
      // 遍历所有软体
      for (const softBody of this.softBodies) {
        const particles = softBody.getParticles();
        if (!particles) continue;
        
        // 使用空间分区或暴力检测
        if (this.useSpatialPartitioning && this.spatialPartitioning) {
          // 获取刚体附近的粒子
          const nearbyParticles = this.spatialPartitioning.getNearbyParticles(
            cannonBody.position,
            cannonBody.boundingRadius + this.collisionRadius
          );
          
          // 检测碰撞
          for (const particle of nearbyParticles) {
            this.checkParticleRigidBodyCollision(particle, cannonBody);
          }
        } else {
          // 暴力检测
          for (const particle of particles) {
            this.checkParticleRigidBodyCollision(particle, cannonBody);
          }
        }
      }
    }
  }
  
  /**
   * 检测粒子与刚体的碰撞
   * @param particle 粒子
   * @param rigidBody 刚体
   */
  private checkParticleRigidBodyCollision(particle: CANNON.Body, rigidBody: CANNON.Body): void {
    // 简化的碰撞检测：使用球体近似
    const distance = particle.position.distanceTo(rigidBody.position);
    const minDistance = this.collisionRadius + rigidBody.boundingRadius;
    
    if (distance < minDistance) {
      // 计算碰撞法线
      const normal = new CANNON.Vec3();
      normal.copy(particle.position);
      normal.vsub(rigidBody.position, normal);
      
      // 归一化法线
      const length = normal.length();
      if (length < 0.0001) return;
      
      normal.scale(1 / length, normal);
      
      // 计算穿透深度
      const penetration = minDistance - distance;
      
      // 添加到碰撞对列表
      this.collisionPairs.push({
        softBodyParticle: particle,
        rigidBody,
        penetration,
        normal
      });
    }
  }
  
  /**
   * 解决碰撞
   * @param deltaTime 帧间隔时间（秒）
   */
  private resolveCollisions(deltaTime: number): void {
    // 遍历所有碰撞对
    for (const pair of this.collisionPairs) {
      const { softBodyParticle, rigidBody, penetration, normal } = pair;
      
      // 计算相对速度
      const relativeVelocity = new CANNON.Vec3();
      relativeVelocity.copy(softBodyParticle.velocity);
      relativeVelocity.vsub(rigidBody.velocity, relativeVelocity);
      
      // 计算法线方向的相对速度
      const normalVelocity = normal.dot(relativeVelocity);
      
      // 如果物体正在分离，跳过
      if (normalVelocity > 0) continue;
      
      // 计算反弹冲量
      const restitution = 0.3; // 恢复系数
      const j = -(1 + restitution) * normalVelocity;
      const impulse = new CANNON.Vec3();
      impulse.copy(normal);
      impulse.scale(j * this.collisionResponse, impulse);
      
      // 应用冲量
      if (softBodyParticle.mass > 0) {
        const softBodyImpulse = new CANNON.Vec3();
        softBodyImpulse.copy(impulse);
        softBodyImpulse.scale(1 / softBodyParticle.mass, softBodyImpulse);
        softBodyParticle.velocity.vadd(softBodyImpulse, softBodyParticle.velocity);
      }
      
      if (rigidBody.mass > 0) {
        const rigidBodyImpulse = new CANNON.Vec3();
        rigidBodyImpulse.copy(impulse);
        rigidBodyImpulse.scale(-1 / rigidBody.mass, rigidBodyImpulse);
        rigidBody.velocity.vadd(rigidBodyImpulse, rigidBody.velocity);
      }
      
      // 应用位置校正
      const percent = 0.2; // 位置校正系数
      const correction = new CANNON.Vec3();
      correction.copy(normal);
      correction.scale(percent * penetration, correction);
      
      if (softBodyParticle.mass > 0) {
        softBodyParticle.position.vadd(correction, softBodyParticle.position);
      }
      
      if (rigidBody.mass > 0) {
        const rigidBodyCorrection = new CANNON.Vec3();
        rigidBodyCorrection.copy(correction);
        rigidBodyCorrection.scale(-1, rigidBodyCorrection);
        rigidBody.position.vadd(rigidBodyCorrection, rigidBody.position);
      }
      
      // 应用摩擦力
      if (this.friction > 0) {
        // 计算切线方向
        const tangent = new CANNON.Vec3();
        const relVelTangent = new CANNON.Vec3();
        relVelTangent.copy(relativeVelocity);
        
        const normalVelocityVec = new CANNON.Vec3();
        normalVelocityVec.copy(normal);
        normalVelocityVec.scale(normalVelocity, normalVelocityVec);
        
        relVelTangent.vsub(normalVelocityVec, relVelTangent);
        
        const tangentLength = relVelTangent.length();
        if (tangentLength > 0.0001) {
          tangent.copy(relVelTangent);
          tangent.scale(1 / tangentLength, tangent);
          
          // 计算摩擦冲量
          const jt = -tangentLength * this.friction;
          
          // 应用摩擦冲量
          const frictionImpulse = new CANNON.Vec3();
          frictionImpulse.copy(tangent);
          frictionImpulse.scale(jt, frictionImpulse);
          
          if (softBodyParticle.mass > 0) {
            const softBodyFrictionImpulse = new CANNON.Vec3();
            softBodyFrictionImpulse.copy(frictionImpulse);
            softBodyFrictionImpulse.scale(1 / softBodyParticle.mass, softBodyFrictionImpulse);
            softBodyParticle.velocity.vadd(softBodyFrictionImpulse, softBodyParticle.velocity);
          }
          
          if (rigidBody.mass > 0) {
            const rigidBodyFrictionImpulse = new CANNON.Vec3();
            rigidBodyFrictionImpulse.copy(frictionImpulse);
            rigidBodyFrictionImpulse.scale(-1 / rigidBody.mass, rigidBodyFrictionImpulse);
            rigidBody.velocity.vadd(rigidBodyFrictionImpulse, rigidBody.velocity);
          }
        }
      }
    }
  }
  
  /**
   * 启用交互
   */
  public enable(): void {
    this.enabled = true;
  }
  
  /**
   * 禁用交互
   */
  public disable(): void {
    this.enabled = false;
  }
  
  /**
   * 设置碰撞检测半径
   * @param radius 碰撞检测半径
   */
  public setCollisionRadius(radius: number): void {
    this.collisionRadius = radius;
  }
  
  /**
   * 设置碰撞响应强度
   * @param response 碰撞响应强度
   */
  public setCollisionResponse(response: number): void {
    this.collisionResponse = response;
  }
  
  /**
   * 设置摩擦系数
   * @param friction 摩擦系数
   */
  public setFriction(friction: number): void {
    this.friction = friction;
  }
}
