/**
 * 增强版动画遮罩
 * 提供更高级的遮罩功能，包括骨骼层次结构遮罩和动态遮罩
 */
import * as THREE from 'three';
import { AnimationMask, AnimationMaskConfig } from './AnimationMask';
/**
 * 动态遮罩类型
 */
export declare enum DynamicMaskType {
    /** 距离 - 基于与目标点的距离 */
    DISTANCE = "distance",
    /** 方向 - 基于与目标方向的夹角 */
    DIRECTION = "direction",
    /** 速度 - 基于骨骼的运动速度 */
    VELOCITY = "velocity",
    /** 时间 - 基于动画时间 */
    TIME = "time",
    /** 参数 - 基于外部参数 */
    PARAMETER = "parameter"
}
/**
 * 增强版动画遮罩配置
 */
export interface EnhancedMaskConfig extends AnimationMaskConfig {
    /** 动态遮罩类型 */
    dynamicType?: DynamicMaskType;
    /** 动态遮罩参数 */
    dynamicParams?: {
        /** 目标点（用于距离和方向） */
        target?: THREE.Vector3;
        /** 最大距离（用于距离） */
        maxDistance?: number;
        /** 最小距离（用于距离） */
        minDistance?: number;
        /** 方向向量（用于方向） */
        direction?: THREE.Vector3;
        /** 最大角度（用于方向） */
        maxAngle?: number;
        /** 速度阈值（用于速度） */
        velocityThreshold?: number;
        /** 时间曲线（用于时间） */
        timeCurve?: (time: number) => number;
        /** 参数名称（用于参数） */
        paramName?: string;
        /** 参数范围（用于参数） */
        paramRange?: [number, number];
    };
    /** 是否启用骨骼层次结构缓存 */
    enableHierarchyCache?: boolean;
    /** 是否启用权重插值 */
    enableWeightInterpolation?: boolean;
    /** 权重插值速度 */
    weightInterpolationSpeed?: number;
}
/**
 * 增强版动画遮罩事件类型
 */
export declare enum EnhancedMaskEventType {
    /** 动态更新 */
    DYNAMIC_UPDATE = "dynamicUpdate",
    /** 层次结构更新 */
    HIERARCHY_UPDATE = "hierarchyUpdate",
    /** 权重插值更新 */
    WEIGHT_INTERPOLATION_UPDATE = "weightInterpolationUpdate"
}
/**
 * 增强版动画遮罩
 */
export declare class AnimationMaskEnhanced extends AnimationMask {
    /** 动态遮罩类型 */
    private dynamicType?;
    /** 动态遮罩参数 */
    private dynamicParams;
    /** 是否启用骨骼层次结构缓存 */
    private enableHierarchyCache;
    /** 骨骼层次结构缓存 */
    private hierarchyCache;
    /** 是否启用权重插值 */
    private enableWeightInterpolation;
    /** 权重插值速度 */
    private weightInterpolationSpeed;
    /** 目标权重映射 */
    private targetBoneWeights;
    /** 事件发射器 */
    private enhancedEventEmitter;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: EnhancedMaskConfig);
    /**
     * 获取动态遮罩类型
     * @returns 动态遮罩类型
     */
    getDynamicType(): DynamicMaskType | undefined;
    /**
     * 设置动态遮罩类型
     * @param type 动态遮罩类型
     */
    setDynamicType(type?: DynamicMaskType): void;
    /**
     * 获取动态遮罩参数
     * @returns 动态遮罩参数
     */
    getDynamicParams(): any;
    /**
     * 设置动态遮罩参数
     * @param params 动态遮罩参数
     */
    setDynamicParams(params: any): void;
    /**
     * 更新动态遮罩
     * @param skeleton 骨骼对象
     * @param time 当前时间
     * @param params 外部参数
     */
    updateDynamicMask(skeleton?: THREE.Skeleton, time?: number, params?: Map<string, number>): void;
    /**
     * 更新距离遮罩
     * @param skeleton 骨骼对象
     */
    private updateDistanceMask;
    /**
     * 更新方向遮罩
     * @param skeleton 骨骼对象
     */
    private updateDirectionMask;
    /**
     * 更新速度遮罩
     * @param skeleton 骨骼对象
     */
    private updateVelocityMask;
    /**
     * 更新时间遮罩
     * @param time 当前时间
     */
    private updateTimeMask;
    /**
     * 更新参数遮罩
     * @param params 外部参数
     */
    private updateParameterMask;
    /**
     * 设置目标骨骼权重
     * @param boneName 骨骼名称
     * @param weight 权重
     */
    private setTargetBoneWeight;
    /**
     * 更新权重插值
     * @param deltaTime 时间增量
     */
    private updateWeightInterpolation;
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    addEnhancedEventListener(event: EnhancedMaskEventType, listener: (data: any) => void): void;
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器
     */
    removeEnhancedEventListener(event: EnhancedMaskEventType, listener: (data: any) => void): void;
}
