import { Component } from '../core/Component';
import { FacialExpressionType } from './FacialExpressionType';
export { FacialExpressionType };
/**
 * 口型类型
 */
export declare enum VisemeType {
    SILENT = "silent",
    PP = "pp",
    FF = "ff",
    TH = "th",
    DD = "dd",
    KK = "kk",
    CH = "ch",
    SS = "ss",
    NN = "nn",
    RR = "rr",
    AA = "aa",
    EE = "ee",
    IH = "ih",
    OH = "oh",
    OU = "ou"
}
/**
 * 面部动画配置
 */
export interface FacialAnimationConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** 是否自动检测音频 */
    autoDetectAudio?: boolean;
    /** 是否使用网络摄像头 */
    useWebcam?: boolean;
    /** 是否使用AI预测 */
    useAIPrediction?: boolean;
    /** 混合速度 */
    blendSpeed?: number;
    /** 平滑因子 */
    smoothingFactor?: number;
}
/**
 * 面部动画组件
 */
export declare class FacialAnimationComponent extends Component {
    /** 组件类型 */
    static readonly type = "FacialAnimation";
    /** 当前表情 */
    private currentExpression;
    /** 目标表情 */
    private targetExpression;
    /** 表情权重 */
    private expressionWeight;
    /** 表情混合速度 */
    private expressionBlendSpeed;
    /** 当前口型 */
    private currentViseme;
    /** 目标口型 */
    private targetViseme;
    /** 口型权重 */
    private visemeWeight;
    /** 口型混合速度 */
    private visemeBlendSpeed;
    /** 表情混合映射 */
    private expressionBlendMap;
    /** 口型混合映射 */
    private visemeBlendMap;
    /** 是否使用平滑 */
    private useSmoothing;
    /** 平滑因子 */
    private smoothingFactor;
    /** 事件发射器 */
    private eventEmitter;
    /**
     * 构造函数
     */
    constructor();
    /**
     * 设置表情
     * @param expression 表情类型
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     */
    setExpression(expression: FacialExpressionType, weight?: number, blendTime?: number): void;
    /**
     * 获取当前表情
     * @returns 当前表情和权重
     */
    getCurrentExpression(): {
        expression: FacialExpressionType;
        weight: number;
    };
    /**
     * 设置口型
     * @param viseme 口型类型
     * @param weight 权重
     * @param blendTime 混合时间（秒）
     */
    setViseme(viseme: VisemeType, weight?: number, blendTime?: number): void;
    /**
     * 获取当前口型
     * @returns 当前口型和权重
     */
    getCurrentViseme(): {
        viseme: VisemeType;
        weight: number;
    };
    /**
     * 获取表情混合映射
     * @returns 表情混合映射的副本
     */
    getExpressionBlendMap(): Map<string, number>;
    /**
     * 获取口型混合映射
     * @returns 口型混合映射的副本
     */
    getVisemeBlendMap(): Map<string, number>;
    /**
     * 重置表情
     */
    resetExpression(): void;
    /**
     * 重置口型
     */
    resetViseme(): void;
    /**
     * 启用组件
     */
    enable(): void;
    /**
     * 禁用组件
     */
    disable(): void;
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): void;
    /**
     * 更新表情混合
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateExpressionBlend;
    /**
     * 更新口型混合
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateVisemeBlend;
}
