/**
 * 性能分析器
 * 用于分析场景性能并提供优化建议
 */
export class PerformanceAnalyzer {
  /**
   * 构造函数
   * @param {World} world 世界实例
   * @param {PerformanceMonitor} performanceMonitor 性能监控器
   */
  constructor(world, performanceMonitor) {
    this.world = world;
    this.performanceMonitor = performanceMonitor;
    this.analysisResults = null;
  }

  /**
   * 分析性能
   * @returns {Object} 分析结果
   */
  analyze() {
    // 获取性能数据
    const metrics = this.performanceMonitor.getMetrics();
    const stats = this.world.renderer.getStatistics();
    
    // 分析FPS
    const fpsAnalysis = this.analyzeFPS(metrics.fps);
    
    // 分析绘制调用
    const drawCallsAnalysis = this.analyzeDrawCalls(stats.drawCalls);
    
    // 分析三角形数量
    const trianglesAnalysis = this.analyzeTriangles(stats.triangles);
    
    // 分析内存使用
    const memoryAnalysis = this.analyzeMemory(metrics.memory);
    
    // 分析纹理内存
    const textureMemoryAnalysis = this.analyzeTextureMemory(metrics.textureMemory);
    
    // 分析几何体内存
    const geometryMemoryAnalysis = this.analyzeGeometryMemory(metrics.geometryMemory);
    
    // 生成总体评分和建议
    const overallScore = this.calculateOverallScore([
      fpsAnalysis.score,
      drawCallsAnalysis.score,
      trianglesAnalysis.score,
      memoryAnalysis.score,
      textureMemoryAnalysis.score,
      geometryMemoryAnalysis.score
    ]);
    
    const recommendations = this.generateRecommendations([
      fpsAnalysis,
      drawCallsAnalysis,
      trianglesAnalysis,
      memoryAnalysis,
      textureMemoryAnalysis,
      geometryMemoryAnalysis
    ]);
    
    // 保存分析结果
    this.analysisResults = {
      timestamp: Date.now(),
      overallScore,
      metrics: {
        fps: metrics.fps,
        drawCalls: stats.drawCalls,
        triangles: stats.triangles,
        memory: metrics.memory,
        textureMemory: metrics.textureMemory,
        geometryMemory: metrics.geometryMemory
      },
      analyses: {
        fps: fpsAnalysis,
        drawCalls: drawCallsAnalysis,
        triangles: trianglesAnalysis,
        memory: memoryAnalysis,
        textureMemory: textureMemoryAnalysis,
        geometryMemory: geometryMemoryAnalysis
      },
      recommendations
    };
    
    return this.analysisResults;
  }
  
  /**
   * 分析FPS
   * @param {number} fps 当前FPS
   * @returns {Object} 分析结果
   */
  analyzeFPS(fps) {
    let status, message, score;
    
    if (fps >= 58) {
      status = 'excellent';
      message = 'FPS非常稳定，性能良好。';
      score = 100;
    } else if (fps >= 45) {
      status = 'good';
      message = 'FPS良好，但仍有优化空间。';
      score = 80;
    } else if (fps >= 30) {
      status = 'average';
      message = 'FPS一般，建议进行优化。';
      score = 60;
    } else if (fps >= 20) {
      status = 'poor';
      message = 'FPS较低，需要优化。';
      score = 40;
    } else {
      status = 'critical';
      message = 'FPS非常低，严重影响用户体验，需要立即优化。';
      score = 20;
    }
    
    return {
      status,
      message,
      score,
      recommendations: this.getFPSRecommendations(fps)
    };
  }
  
  /**
   * 获取FPS优化建议
   * @param {number} fps 当前FPS
   * @returns {Array} 优化建议
   */
  getFPSRecommendations(fps) {
    const recommendations = [];
    
    if (fps < 58) {
      recommendations.push('考虑减少场景复杂度或启用更多优化选项。');
    }
    
    if (fps < 45) {
      recommendations.push('启用LOD系统减少远处物体的细节。');
      recommendations.push('使用实例化渲染减少绘制调用。');
    }
    
    if (fps < 30) {
      recommendations.push('合并静态几何体减少绘制调用。');
      recommendations.push('优化着色器减少GPU负载。');
      recommendations.push('减少场景中的光源数量。');
    }
    
    if (fps < 20) {
      recommendations.push('大幅减少场景复杂度。');
      recommendations.push('禁用或简化后期处理效果。');
      recommendations.push('检查是否有CPU密集型操作阻塞主线程。');
    }
    
    return recommendations;
  }
  
  /**
   * 分析绘制调用
   * @param {number} drawCalls 绘制调用数量
   * @returns {Object} 分析结果
   */
  analyzeDrawCalls(drawCalls) {
    let status, message, score;
    
    if (drawCalls <= 100) {
      status = 'excellent';
      message = '绘制调用数量很低，性能良好。';
      score = 100;
    } else if (drawCalls <= 500) {
      status = 'good';
      message = '绘制调用数量合理，但可以进一步优化。';
      score = 80;
    } else if (drawCalls <= 1000) {
      status = 'average';
      message = '绘制调用数量较多，建议优化。';
      score = 60;
    } else if (drawCalls <= 2000) {
      status = 'poor';
      message = '绘制调用数量过多，需要优化。';
      score = 40;
    } else {
      status = 'critical';
      message = '绘制调用数量极高，严重影响性能，需要立即优化。';
      score = 20;
    }
    
    return {
      status,
      message,
      score,
      recommendations: this.getDrawCallsRecommendations(drawCalls)
    };
  }
  
  /**
   * 获取绘制调用优化建议
   * @param {number} drawCalls 绘制调用数量
   * @returns {Array} 优化建议
   */
  getDrawCallsRecommendations(drawCalls) {
    const recommendations = [];
    
    if (drawCalls > 100) {
      recommendations.push('使用实例化渲染减少相同物体的绘制调用。');
    }
    
    if (drawCalls > 500) {
      recommendations.push('合并静态几何体减少绘制调用。');
      recommendations.push('使用纹理图集减少材质切换。');
    }
    
    if (drawCalls > 1000) {
      recommendations.push('检查是否有过多的动态物体，考虑使用合批处理。');
      recommendations.push('减少场景中的光源数量，或使用烘焙光照。');
    }
    
    if (drawCalls > 2000) {
      recommendations.push('大幅减少场景中的物体数量。');
      recommendations.push('实现视锥体剔除和遮挡剔除。');
      recommendations.push('将复杂场景分割为多个子场景，按需加载。');
    }
    
    return recommendations;
  }
  
  // 其他分析方法...
  
  /**
   * 计算总体评分
   * @param {Array} scores 各项评分
   * @returns {number} 总体评分
   */
  calculateOverallScore(scores) {
    const sum = scores.reduce((total, score) => total + score, 0);
    return Math.round(sum / scores.length);
  }
  
  /**
   * 生成优化建议
   * @param {Array} analyses 各项分析结果
   * @returns {Array} 优化建议
   */
  generateRecommendations(analyses) {
    // 找出得分最低的三个方面
    const sortedAnalyses = [...analyses].sort((a, b) => a.score - b.score);
    const worstAreas = sortedAnalyses.slice(0, 3);
    
    // 收集这些方面的建议
    const recommendations = [];
    worstAreas.forEach(area => {
      if (area.recommendations && area.recommendations.length > 0) {
        recommendations.push(...area.recommendations);
      }
    });
    
    // 去重
    return [...new Set(recommendations)];
  }
  
  /**
   * 获取最近的分析结果
   * @returns {Object} 分析结果
   */
  getLatestResults() {
    return this.analysisResults;
  }
}
