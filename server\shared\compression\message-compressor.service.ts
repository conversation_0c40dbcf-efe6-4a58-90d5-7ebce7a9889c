/**
 * 消息压缩服务
 * 提供消息压缩和解压功能
 */
import { Injectable, Logger } from '@nestjs/common';
import { CompressionAlgorithm, CompressionOptions, CompressionStats } from './interfaces/compression-config.interface';
import * as zlib from 'zlib';
import { promisify } from 'util';

/**
 * 压缩结果
 */
interface CompressionResult {
  /**
   * 压缩后的数据
   */
  data: Buffer;

  /**
   * 使用的压缩算法
   */
  algorithm: CompressionAlgorithm;
}

/**
 * 缓存条目
 */
interface CacheEntry {
  /**
   * 压缩后的数据
   */
  data: Buffer;

  /**
   * 使用的压缩算法
   */
  algorithm: CompressionAlgorithm;

  /**
   * 最后访问时间
   */
  lastAccessed: number;

  /**
   * 访问次数
   */
  accessCount: number;
}

@Injectable()
export class MessageCompressorService {
  private readonly logger = new Logger(MessageCompressorService.name);
  private readonly compressionCache: Map<string, CacheEntry> = new Map();
  private readonly stats: CompressionStats;

  // zlib方法的Promise版本
  private readonly deflateAsync = promisify(zlib.deflate);
  private readonly inflateAsync = promisify(zlib.inflate);
  private readonly gzipAsync = promisify(zlib.gzip);
  private readonly gunzipAsync = promisify(zlib.gunzip);
  private readonly brotliCompressAsync = promisify(zlib.brotliCompress);
  private readonly brotliDecompressAsync = promisify(zlib.brotliDecompress);

  // 默认选项
  private readonly defaultOptions: Required<CompressionOptions> = {
    algorithm: CompressionAlgorithm.DEFLATE,
    level: 6,
    minSize: 100,
    enableAdaptive: true,
    enableCache: true,
    cacheSize: 1000,
    collectStats: true,
    messageTypeField: 'type',
    messageTypeAlgorithmMap: {},
  };

  // 合并后的选项
  private readonly options: Required<CompressionOptions>;

  /**
   * 构造函数
   * @param options 压缩选项
   */
  constructor(options?: Partial<CompressionOptions>) {
    // 合并选项
    this.options = {
      ...this.defaultOptions,
      ...options,
    };

    // 初始化统计信息
    this.stats = {
      compressedCount: 0,
      uncompressedCount: 0,
      compressionRatio: 0,
      bytesSaved: 0,
      avgCompressionTime: 0,
      avgDecompressionTime: 0,
      cacheHits: 0,
      cacheMisses: 0,
      cacheHitRate: 0,
      algorithmStats: {
        [CompressionAlgorithm.NONE]: { count: 0, avgRatio: 0, avgTime: 0 },
        [CompressionAlgorithm.DEFLATE]: { count: 0, avgRatio: 0, avgTime: 0 },
        [CompressionAlgorithm.GZIP]: { count: 0, avgRatio: 0, avgTime: 0 },
        [CompressionAlgorithm.BROTLI]: { count: 0, avgRatio: 0, avgTime: 0 },
      },
    };
  }

  /**
   * 压缩消息
   * @param message 消息对象
   * @returns 压缩结果
   */
  async compress(message: any): Promise<CompressionResult> {
    // 将消息转换为JSON字符串
    const jsonString = JSON.stringify(message);
    const originalSize = Buffer.byteLength(jsonString, 'utf-8');

    // 如果消息太小，不进行压缩
    if (originalSize < this.options.minSize) {
      if (this.options.collectStats) {
        this.stats.uncompressedCount++;
      }
      return {
        data: Buffer.from(jsonString, 'utf-8'),
        algorithm: CompressionAlgorithm.NONE,
      };
    }

    // 检查缓存
    if (this.options.enableCache) {
      const cacheKey = this.generateCacheKey(jsonString);
      const cachedResult = this.compressionCache.get(cacheKey);

      if (cachedResult) {
        // 更新缓存条目
        cachedResult.lastAccessed = Date.now();
        cachedResult.accessCount++;

        if (this.options.collectStats) {
          this.stats.cacheHits++;
          this.updateCompressionStats(
            cachedResult.algorithm,
            originalSize,
            cachedResult.data.length,
            0
          );
        }

        return {
          data: cachedResult.data,
          algorithm: cachedResult.algorithm,
        };
      } else if (this.options.collectStats) {
        this.stats.cacheMisses++;
      }
    }

    // 确定压缩算法
    let algorithm = this.options.algorithm;

    // 如果启用自适应压缩，根据消息类型选择算法
    if (this.options.enableAdaptive && message && typeof message === 'object') {
      const messageType = message[this.options.messageTypeField];
      if (messageType && this.options.messageTypeAlgorithmMap[messageType]) {
        algorithm = this.options.messageTypeAlgorithmMap[messageType];
      }
    }

    // 压缩消息
    const startTime = Date.now();
    let compressedData: Buffer;

    try {
      switch (algorithm) {
        case CompressionAlgorithm.DEFLATE:
          compressedData = await this.deflateAsync(jsonString, { level: this.options.level });
          break;
        case CompressionAlgorithm.GZIP:
          compressedData = await this.gzipAsync(jsonString, { level: this.options.level });
          break;
        case CompressionAlgorithm.BROTLI:
          compressedData = await this.brotliCompressAsync(jsonString, {
            params: {
              [zlib.constants.BROTLI_PARAM_QUALITY]: this.options.level,
            },
          });
          break;
        default:
          compressedData = Buffer.from(jsonString, 'utf-8');
          algorithm = CompressionAlgorithm.NONE;
          break;
      }
    } catch (error) {
      this.logger.warn(`压缩消息失败: ${error.message}`);
      compressedData = Buffer.from(jsonString, 'utf-8');
      algorithm = CompressionAlgorithm.NONE;
    }

    const compressionTime = Date.now() - startTime;
    const compressedSize = compressedData.length;

    // 如果压缩后的大小大于原始大小，使用未压缩的数据
    if (compressedSize >= originalSize && algorithm !== CompressionAlgorithm.NONE) {
      if (this.options.collectStats) {
        this.stats.uncompressedCount++;
      }
      return {
        data: Buffer.from(jsonString, 'utf-8'),
        algorithm: CompressionAlgorithm.NONE,
      };
    }

    // 更新统计信息
    if (this.options.collectStats) {
      this.updateCompressionStats(algorithm, originalSize, compressedSize, compressionTime);
    }

    // 缓存压缩结果
    if (this.options.enableCache) {
      this.cacheCompressionResult(jsonString, compressedData, algorithm);
    }

    return {
      data: compressedData,
      algorithm,
    };
  }

  /**
   * 解压消息
   * @param data 压缩数据
   * @param algorithm 压缩算法
   * @returns 解压后的消息对象
   */
  async decompress(data: Buffer, algorithm: CompressionAlgorithm): Promise<any> {
    // 如果没有压缩，直接返回
    if (algorithm === CompressionAlgorithm.NONE) {
      return JSON.parse(data.toString('utf-8'));
    }

    // 解压消息
    const startTime = Date.now();
    let decompressedData: Buffer;

    try {
      switch (algorithm) {
        case CompressionAlgorithm.DEFLATE:
          decompressedData = await this.inflateAsync(data);
          break;
        case CompressionAlgorithm.GZIP:
          decompressedData = await this.gunzipAsync(data);
          break;
        case CompressionAlgorithm.BROTLI:
          decompressedData = await this.brotliDecompressAsync(data);
          break;
        default:
          throw new Error(`不支持的压缩算法: ${algorithm}`);
      }
    } catch (error) {
      this.logger.error(`解压消息失败: ${error.message}`);
      throw error;
    }

    const decompressionTime = Date.now() - startTime;

    // 更新统计信息
    if (this.options.collectStats) {
      this.updateDecompressionStats(decompressionTime);
    }

    // 解析JSON
    return JSON.parse(decompressedData.toString('utf-8'));
  }

  /**
   * 获取压缩统计信息
   * @returns 压缩统计信息
   */
  getStats(): CompressionStats {
    return { ...this.stats };
  }

  /**
   * 重置压缩统计信息
   */
  resetStats(): void {
    this.stats.compressedCount = 0;
    this.stats.uncompressedCount = 0;
    this.stats.compressionRatio = 0;
    this.stats.bytesSaved = 0;
    this.stats.avgCompressionTime = 0;
    this.stats.avgDecompressionTime = 0;
    this.stats.cacheHits = 0;
    this.stats.cacheMisses = 0;
    this.stats.cacheHitRate = 0;

    Object.values(this.stats.algorithmStats).forEach(stat => {
      stat.count = 0;
      stat.avgRatio = 0;
      stat.avgTime = 0;
    });
  }

  /**
   * 清除压缩缓存
   */
  clearCache(): void {
    this.compressionCache.clear();
  }

  /**
   * 生成缓存键
   * @param jsonString JSON字符串
   * @returns 缓存键
   */
  private generateCacheKey(jsonString: string): string {
    // 简单的哈希函数
    let hash = 0;
    for (let i = 0; i < jsonString.length; i++) {
      const char = jsonString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return `compress:${hash}`;
  }

  /**
   * 缓存压缩结果
   * @param jsonString JSON字符串
   * @param compressedData 压缩数据
   * @param algorithm 压缩算法
   */
  private cacheCompressionResult(jsonString: string, compressedData: Buffer, algorithm: CompressionAlgorithm): void {
    // 检查缓存大小
    if (this.compressionCache.size >= this.options.cacheSize) {
      this.evictCache();
    }

    // 缓存压缩结果
    const cacheKey = this.generateCacheKey(jsonString);
    this.compressionCache.set(cacheKey, {
      data: compressedData,
      algorithm,
      lastAccessed: Date.now(),
      accessCount: 0,
    });
  }

  /**
   * 驱逐缓存
   */
  private evictCache(): void {
    // 如果缓存为空，直接返回
    if (this.compressionCache.size === 0) return;

    // 计算要驱逐的项数
    const evictCount = Math.max(1, Math.floor(this.compressionCache.size * 0.1));

    // 按最后访问时间和访问次数排序
    const sortedItems = Array.from(this.compressionCache.entries())
      .sort(([, a], [, b]) => {
        // 首先按访问次数排序
        const countDiff = a.accessCount - b.accessCount;
        if (countDiff !== 0) return countDiff;

        // 然后按最后访问时间排序
        return a.lastAccessed - b.lastAccessed;
      });

    // 驱逐最不常用的项
    for (let i = 0; i < evictCount && i < sortedItems.length; i++) {
      this.compressionCache.delete(sortedItems[i][0]);
    }
  }

  /**
   * 更新压缩统计信息
   * @param algorithm 压缩算法
   * @param originalSize 原始大小
   * @param compressedSize 压缩后大小
   * @param compressionTime 压缩时间
   */
  private updateCompressionStats(
    algorithm: CompressionAlgorithm,
    originalSize: number,
    compressedSize: number,
    compressionTime: number,
  ): void {
    // 更新总体统计
    if (algorithm !== CompressionAlgorithm.NONE) {
      this.stats.compressedCount++;
      const bytesSaved = originalSize - compressedSize;
      this.stats.bytesSaved += bytesSaved;
      this.stats.compressionRatio = this.stats.compressedCount > 0
        ? 1 - (this.stats.bytesSaved / (this.stats.bytesSaved + this.stats.compressedCount * compressedSize))
        : 0;
    } else {
      this.stats.uncompressedCount++;
    }

    // 更新算法统计
    const algorithmStat = this.stats.algorithmStats[algorithm];
    algorithmStat.count++;

    if (algorithm !== CompressionAlgorithm.NONE) {
      const ratio = compressedSize / originalSize;
      algorithmStat.avgRatio = (algorithmStat.avgRatio * (algorithmStat.count - 1) + ratio) / algorithmStat.count;
    }

    if (compressionTime > 0) {
      algorithmStat.avgTime = (algorithmStat.avgTime * (algorithmStat.count - 1) + compressionTime) / algorithmStat.count;
      this.stats.avgCompressionTime = (this.stats.avgCompressionTime * (this.stats.compressedCount - 1) + compressionTime) / this.stats.compressedCount;
    }

    // 更新缓存命中率
    const totalCacheAccesses = this.stats.cacheHits + this.stats.cacheMisses;
    this.stats.cacheHitRate = totalCacheAccesses > 0 ? this.stats.cacheHits / totalCacheAccesses : 0;
  }

  /**
   * 更新解压统计信息
   * @param decompressionTime 解压时间
   */
  private updateDecompressionStats(decompressionTime: number): void {
    this.stats.avgDecompressionTime = (this.stats.avgDecompressionTime * (this.stats.compressedCount - 1) + decompressionTime) / this.stats.compressedCount;
  }
}
