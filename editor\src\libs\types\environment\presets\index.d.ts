/**
 * 环境预设模块
 *
 * 提供各种环境预设，包括天气、地形、光照等
 */
import { EnvironmentType, WeatherType, TerrainType } from '../components/EnvironmentAwarenessComponent';
import { EnvironmentResponseRule } from '../components/EnvironmentResponseComponent';
/**
 * 环境预设接口
 */
export interface EnvironmentPreset {
    /** 预设ID */
    id: string;
    /** 预设名称 */
    name: string;
    /** 预设描述 */
    description?: string;
    /** 环境类型 */
    environmentType: EnvironmentType;
    /** 天气类型 */
    weatherType: WeatherType;
    /** 地形类型 */
    terrainType?: TerrainType;
    /** 光照参数 */
    lighting?: {
        /** 环境光颜色 */
        ambientColor?: number;
        /** 环境光强度 */
        ambientIntensity?: number;
        /** 方向光颜色 */
        directionalColor?: number;
        /** 方向光强度 */
        directionalIntensity?: number;
        /** 雾颜色 */
        fogColor?: number;
        /** 雾密度 */
        fogDensity?: number;
    };
    /** 粒子系统参数 */
    particles?: {
        /** 粒子类型 */
        type: string;
        /** 粒子数量 */
        count: number;
        /** 粒子大小 */
        size: number;
        /** 粒子颜色 */
        color: number;
        /** 粒子生命周期 */
        lifetime: number;
        /** 粒子速度 */
        speed: number;
    }[];
    /** 声音参数 */
    sounds?: {
        /** 声音ID */
        id: string;
        /** 声音URL */
        url: string;
        /** 声音音量 */
        volume: number;
        /** 是否循环 */
        loop: boolean;
    }[];
    /** 响应规则 */
    responseRules?: EnvironmentResponseRule[];
}
/**
 * 创建雨天天气预设
 * @returns 雨天天气预设
 */
export declare function createRainyWeatherPreset(): EnvironmentPreset;
/**
 * 创建雪天天气预设
 * @returns 雪天天气预设
 */
export declare function createSnowyWeatherPreset(): EnvironmentPreset;
/**
 * 创建炎热天气预设
 * @returns 炎热天气预设
 */
export declare function createHotWeatherPreset(): EnvironmentPreset;
