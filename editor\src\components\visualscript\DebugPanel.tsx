 /**
 * 视觉脚本调试面板
 * 提供视觉脚本的调试功能界面
 */
import React, { useState } from 'react';
import { Button, Tabs, Table, Input, Switch, Select, Form, Space, Tooltip, Modal, Tag, Timeline, Typography, Divider } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  StepForwardOutlined,
  FastForwardOutlined,
  StepBackwardOutlined,
  BugOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  EyeOutlined,
  HistoryOutlined,
  CodeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// 调试相关类型定义
/**
 * 执行状态
 */
export enum ExecutionState {
  /**
   * 运行中
   */
  RUNNING = 'running',

  /**
   * 暂停
   */
  PAUSED = 'paused',

  /**
   * 停止
   */
  STOPPED = 'stopped'
}

/**
 * 断点类型
 */
export enum BreakpointType {
  /**
   * 普通断点
   */
  NORMAL = 'normal',

  /**
   * 条件断点
   */
  CONDITIONAL = 'conditional'
}

/**
 * 执行步骤类型
 */
export enum StepType {
  /**
   * 单步执行（进入子图）
   */
  STEP_INTO = 'step_into',

  /**
   * 单步执行（不进入子图）
   */
  STEP_OVER = 'step_over',

  /**
   * 单步执行（跳出当前子图）
   */
  STEP_OUT = 'step_out'
}

/**
 * 断点信息
 */
export interface Breakpoint {
  /**
   * 断点ID
   */
  id: string;

  /**
   * 节点ID
   */
  nodeId: string;

  /**
   * 图ID
   */
  graphId: string;

  /**
   * 断点类型
   */
  type: BreakpointType;

  /**
   * 条件表达式（仅条件断点）
   */
  condition?: string;

  /**
   * 是否启用
   */
  enabled: boolean;
}

/**
 * 执行路径项
 */
export interface ExecutionPathItem {
  /**
   * 节点ID
   */
  nodeId: string;

  /**
   * 图ID
   */
  graphId: string;

  /**
   * 时间戳
   */
  timestamp: number;
}

const { Text } = Typography;
const { Option } = Select;

/**
 * 调试面板属性
 */
interface DebugPanelProps {
  /**
   * 当前图ID
   */
  currentGraphId: string;
  
  /**
   * 当前执行状态
   */
  executionState: ExecutionState;
  
  /**
   * 断点列表
   */
  breakpoints: Breakpoint[];
  
  /**
   * 执行路径
   */
  executionPath: ExecutionPathItem[];
  
  /**
   * 监视变量列表
   */
  watches: { name: string; value: any }[];
  
  /**
   * 变量列表
   */
  variables: { name: string; value: any }[];
  
  /**
   * 当前节点ID
   */
  currentNodeId: string | null;
  
  /**
   * 添加断点回调
   */
  onAddBreakpoint: (nodeId: string, type: BreakpointType, condition?: string) => void;
  
  /**
   * 移除断点回调
   */
  onRemoveBreakpoint: (id: string) => void;
  
  /**
   * 更新断点回调
   */
  onUpdateBreakpoint: (id: string, enabled: boolean, condition?: string) => void;
  
  /**
   * 添加监视回调
   */
  onAddWatch: (variableName: string) => void;
  
  /**
   * 移除监视回调
   */
  onRemoveWatch: (variableName: string) => void;
  
  /**
   * 设置变量值回调
   */
  onSetVariableValue: (variableName: string, value: any) => void;
  
  /**
   * 继续执行回调
   */
  onContinue: () => void;
  
  /**
   * 暂停执行回调
   */
  onPause: () => void;
  
  /**
   * 停止执行回调
   */
  onStop: () => void;
  
  /**
   * 单步执行回调
   */
  onStep: (type: StepType) => void;
  
  /**
   * 清除执行路径回调
   */
  onClearExecutionPath: () => void;
}

/**
 * 调试面板组件
 */
const DebugPanel: React.FC<DebugPanelProps> = ({
  currentGraphId,
  executionState,
  breakpoints,
  executionPath,
  watches,
  variables,
  currentNodeId,
  onAddBreakpoint,
  onRemoveBreakpoint,
  onUpdateBreakpoint,
  onAddWatch,
  onRemoveWatch,
  onSetVariableValue,
  onContinue,
  onPause,
  onStop,
  onStep,
  onClearExecutionPath
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('breakpoints');
  const [editingBreakpoint, setEditingBreakpoint] = useState<Breakpoint | null>(null);
  const [breakpointModalVisible, setBreakpointModalVisible] = useState(false);
  const [newWatchName, setNewWatchName] = useState('');
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  
  // 过滤当前图的断点
  const currentGraphBreakpoints = breakpoints.filter(bp => bp.graphId === currentGraphId);
  
  // 过滤当前图的执行路径
  const currentGraphExecutionPath = executionPath.filter(item => item.graphId === currentGraphId);
  
  /**
   * 添加断点
   */
  const handleAddBreakpoint = () => {
    if (selectedNodeId) {
      setEditingBreakpoint({
        id: '',
        nodeId: selectedNodeId,
        graphId: currentGraphId,
        type: BreakpointType.NORMAL,
        enabled: true
      });
      setBreakpointModalVisible(true);
    }
  };
  
  /**
   * 编辑断点
   */
  const handleEditBreakpoint = (breakpoint: Breakpoint) => {
    setEditingBreakpoint({ ...breakpoint });
    setBreakpointModalVisible(true);
  };
  
  /**
   * 保存断点
   */
  const handleSaveBreakpoint = () => {
    if (editingBreakpoint) {
      if (editingBreakpoint.id) {
        // 更新断点
        onUpdateBreakpoint(
          editingBreakpoint.id,
          editingBreakpoint.enabled,
          editingBreakpoint.type === BreakpointType.CONDITIONAL ? editingBreakpoint.condition : undefined
        );
      } else {
        // 添加断点
        onAddBreakpoint(
          editingBreakpoint.nodeId,
          editingBreakpoint.type,
          editingBreakpoint.type === BreakpointType.CONDITIONAL ? editingBreakpoint.condition : undefined
        );
      }
      
      setBreakpointModalVisible(false);
      setEditingBreakpoint(null);
    }
  };
  
  /**
   * 添加监视
   */
  const handleAddWatch = () => {
    if (newWatchName) {
      onAddWatch(newWatchName);
      setNewWatchName('');
    }
  };
  
  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <Space className="debug-toolbar" style={{ marginBottom: 16 }}>
      {executionState === ExecutionState.RUNNING ? (
        <Tooltip title={t('调试.暂停')}>
          <Button 
            icon={<PauseCircleOutlined />} 
            onClick={onPause}
            type="primary"
          />
        </Tooltip>
      ) : (
        <Tooltip title={t('调试.继续')}>
          <Button 
            icon={<PlayCircleOutlined />} 
            onClick={onContinue}
            type="primary"
            disabled={executionState === ExecutionState.STOPPED}
          />
        </Tooltip>
      )}
      
      <Tooltip title={t('调试.停止')}>
        <Button 
          icon={<StopOutlined />} 
          onClick={onStop}
          danger
          disabled={executionState === ExecutionState.STOPPED}
        />
      </Tooltip>
      
      <Divider type="vertical" />
      
      <Tooltip title={t('调试.单步执行')}>
        <Button 
          icon={<StepForwardOutlined />} 
          onClick={() => onStep(StepType.STEP_INTO)}
          disabled={executionState !== ExecutionState.PAUSED}
        />
      </Tooltip>
      
      <Tooltip title={t('调试.单步跳过')}>
        <Button
          icon={<FastForwardOutlined />}
          onClick={() => onStep(StepType.STEP_OVER)}
          disabled={executionState !== ExecutionState.PAUSED}
        />
      </Tooltip>
      
      <Tooltip title={t('调试.单步跳出')}>
        <Button 
          icon={<StepBackwardOutlined />} 
          onClick={() => onStep(StepType.STEP_OUT)}
          disabled={executionState !== ExecutionState.PAUSED}
        />
      </Tooltip>
      
      <Divider type="vertical" />
      
      <Text type="secondary">
        {t(`调试.状态.${executionState}`)}
        {currentNodeId && executionState === ExecutionState.PAUSED && (
          <Tag color="blue" style={{ marginLeft: 8 }}>
            {t('调试.当前节点')}: {currentNodeId}
          </Tag>
        )}
      </Text>
    </Space>
  );
  
  /**
   * 渲染断点列表
   */
  const renderBreakpointsList = () => {
    const columns = [
      {
        title: t('调试.断点.启用'),
        dataIndex: 'enabled',
        key: 'enabled',
        width: 80,
        render: (enabled: boolean, record: Breakpoint) => (
          <Switch 
            checked={enabled} 
            onChange={(checked) => onUpdateBreakpoint(record.id, checked, record.condition)}
            size="small"
          />
        )
      },
      {
        title: t('调试.断点.节点'),
        dataIndex: 'nodeId',
        key: 'nodeId'
      },
      {
        title: t('调试.断点.类型'),
        dataIndex: 'type',
        key: 'type',
        width: 120,
        render: (type: BreakpointType) => (
          <Tag color={type === BreakpointType.NORMAL ? 'blue' : 'orange'}>
            {t(`调试.断点.类型.${type}`)}
          </Tag>
        )
      },
      {
        title: t('调试.断点.条件'),
        dataIndex: 'condition',
        key: 'condition',
        render: (condition: string) => condition || '-'
      },
      {
        title: t('调试.操作'),
        key: 'action',
        width: 120,
        render: (_: any, record: Breakpoint) => (
          <Space>
            <Button 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEditBreakpoint(record)}
            />
            <Button 
              icon={<DeleteOutlined />} 
              size="small"
              danger
              onClick={() => onRemoveBreakpoint(record.id)}
            />
          </Space>
        )
      }
    ];
    
    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Select
              style={{ width: 200 }}
              placeholder={t('调试.断点.选择节点') as string}
              onChange={(value) => setSelectedNodeId(value)}
              value={selectedNodeId}
            >
              {/* 这里应该从编辑器获取节点列表 */}
              <Option value="node1">节点1</Option>
              <Option value="node2">节点2</Option>
              <Option value="node3">节点3</Option>
            </Select>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleAddBreakpoint}
              disabled={!selectedNodeId}
            >
              {t('调试.断点.添加')}
            </Button>
          </Space>
        </div>
        
        <Table 
          dataSource={currentGraphBreakpoints} 
          columns={columns} 
          rowKey="id"
          size="small"
          pagination={false}
        />
      </div>
    );
  };
  
  /**
   * 渲染监视列表
   */
  const renderWatchesList = () => {
    const columns = [
      {
        title: t('调试.监视.名称'),
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: t('调试.监视.值'),
        dataIndex: 'value',
        key: 'value',
        render: (value: any) => (
          <div style={{ maxWidth: 300, overflow: 'auto' }}>
            {typeof value === 'object' ? JSON.stringify(value) : String(value)}
          </div>
        )
      },
      {
        title: t('调试.操作'),
        key: 'action',
        width: 80,
        render: (_: any, record: { name: string }) => (
          <Button 
            icon={<DeleteOutlined />} 
            size="small"
            danger
            onClick={() => onRemoveWatch(record.name)}
          />
        )
      }
    ];
    
    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Input 
              style={{ width: 200 }}
              placeholder={t('调试.监视.变量名') as string}
              value={newWatchName}
              onChange={(e) => setNewWatchName(e.target.value)}
              onPressEnter={handleAddWatch}
            />
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleAddWatch}
              disabled={!newWatchName}
            >
              {t('调试.监视.添加')}
            </Button>
          </Space>
        </div>
        
        <Table 
          dataSource={watches} 
          columns={columns} 
          rowKey="name"
          size="small"
          pagination={false}
        />
      </div>
    );
  };
  
  /**
   * 渲染变量列表
   */
  const renderVariablesList = () => {
    const columns = [
      {
        title: t('调试.变量.名称'),
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: t('调试.变量.值'),
        dataIndex: 'value',
        key: 'value',
        render: (value: any, record: { name: string }) => (
          <Input 
            value={typeof value === 'object' ? JSON.stringify(value) : String(value)}
            onChange={(e) => onSetVariableValue(record.name, e.target.value)}
            disabled={executionState !== ExecutionState.PAUSED}
          />
        )
      }
    ];
    
    return (
      <Table 
        dataSource={variables} 
        columns={columns} 
        rowKey="name"
        size="small"
        pagination={false}
      />
    );
  };
  
  /**
   * 渲染执行路径
   */
  const renderExecutionPath = () => {
    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Button 
            icon={<DeleteOutlined />}
            onClick={onClearExecutionPath}
          >
            {t('调试.执行路径.清除')}
          </Button>
        </div>
        
        <Timeline mode="left" style={{ maxHeight: 400, overflow: 'auto' }}>
          {currentGraphExecutionPath.map((item, index) => (
            <Timeline.Item 
              key={index}
              color={item.nodeId === currentNodeId ? 'blue' : 'gray'}
            >
              <div>
                <Text strong>{item.nodeId}</Text>
                <div>
                  <Text type="secondary">
                    {new Date(item.timestamp).toLocaleTimeString()}
                  </Text>
                </div>
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      </div>
    );
  };
  
  return (
    <div className="debug-panel">
      {renderToolbar()}
      
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'breakpoints',
            label: <span><BugOutlined />{t('调试.断点')}</span>,
            children: renderBreakpointsList()
          },
          {
            key: 'watches',
            label: <span><EyeOutlined />{t('调试.监视')}</span>,
            children: renderWatchesList()
          },
          {
            key: 'variables',
            label: <span><CodeOutlined />{t('调试.变量')}</span>,
            children: renderVariablesList()
          },
          {
            key: 'executionPath',
            label: <span><HistoryOutlined />{t('调试.执行路径')}</span>,
            children: renderExecutionPath()
          }
        ]}
      />
      
      <Modal
        title={editingBreakpoint?.id ? t('调试.断点.编辑') : t('调试.断点.添加')}
        open={breakpointModalVisible}
        onOk={handleSaveBreakpoint}
        onCancel={() => setBreakpointModalVisible(false)}
      >
        <Form layout="vertical">
          <Form.Item label={t('调试.断点.节点')}>
            <Input value={editingBreakpoint?.nodeId} disabled />
          </Form.Item>
          
          <Form.Item label={t('调试.断点.类型')}>
            <Select
              value={editingBreakpoint?.type}
              onChange={(value) => setEditingBreakpoint(prev => prev ? { ...prev, type: value } : null)}
            >
              <Option value={BreakpointType.NORMAL}>{t('调试.断点.类型.normal')}</Option>
              <Option value={BreakpointType.CONDITIONAL}>{t('调试.断点.类型.conditional')}</Option>
            </Select>
          </Form.Item>
          
          {editingBreakpoint?.type === BreakpointType.CONDITIONAL && (
            <Form.Item label={t('调试.断点.条件')}>
              <Input
                value={editingBreakpoint?.condition}
                onChange={(e) => setEditingBreakpoint(prev => prev ? { ...prev, condition: e.target.value } : null)}
                placeholder={t('调试.断点.条件提示') as string}
              />
            </Form.Item>
          )}
          
          <Form.Item label={t('调试.断点.启用')}>
            <Switch
              checked={editingBreakpoint?.enabled}
              onChange={(checked) => setEditingBreakpoint(prev => prev ? { ...prev, enabled: checked } : null)}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DebugPanel;
