/**
 * 软体组件
 * 基于粒子和约束实现软体物理
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Component } from '../../core/Component';
/**
 * 软体类型
 */
export declare enum SoftBodyType {
    /** 布料 */
    CLOTH = "cloth",
    /** 绳索 */
    ROPE = "rope",
    /** 体积软体 */
    VOLUME = "volume",
    /** 气球 */
    BALLOON = "balloon",
    /** 果冻 */
    JELLY = "jelly"
}
/**
 * 软体约束
 */
interface SoftBodyConstraint {
    /** 粒子索引A */
    particleA: number;
    /** 粒子索引B */
    particleB: number;
    /** 休息长度 */
    restLength: number;
    /** 刚度 */
    stiffness: number;
}
/**
 * 软体组件选项
 */
export interface SoftBodyComponentOptions {
    /** 软体类型 */
    type: SoftBodyType;
    /** 质量 */
    mass?: number;
    /** 刚度 */
    stiffness?: number;
    /** 阻尼 */
    damping?: number;
    /** 是否固定角落（仅布料） */
    fixedCorners?: boolean;
    /** 是否固定边缘（仅绳索） */
    fixedEnds?: boolean;
    /** 网格对象（可选） */
    mesh?: THREE.Mesh;
    /** 材质名称 */
    materialName?: string;
    /** 自定义参数 */
    params?: any;
}
/**
 * 软体组件
 */
export declare class SoftBodyComponent extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** 软体类型 */
    private softBodyType;
    /** 粒子质量 */
    private mass;
    /** 刚度 */
    private stiffness;
    /** 阻尼 */
    private damping;
    /** 是否固定角落（仅布料） */
    private fixedCorners;
    /** 是否固定边缘（仅绳索） */
    private fixedEnds;
    /** 物理材质 */
    private material;
    /** 网格对象 */
    private mesh;
    /** 粒子物理体数组 */
    private particles;
    /** 约束数组 */
    private constraints;
    /** 物理约束数组 */
    private physicsConstraints;
    /** 是否已初始化 */
    private initialized;
    /** 自定义参数 */
    private params;
    /** 物理世界引用 */
    private world;
    /**
     * 创建软体组件
     * @param options 软体组件选项
     */
    constructor(options: SoftBodyComponentOptions);
    /**
     * 初始化软体
     * @param world 物理世界
     */
    initialize(world: CANNON.World): void;
    /**
     * 获取软体类型
     * @returns 软体类型
     */
    getType(): SoftBodyType;
    /**
     * 获取粒子数组
     * @returns 粒子数组
     */
    getParticles(): CANNON.Body[] | null;
    /**
     * 应用外力到所有粒子
     * @param force 力向量
     */
    applyForce(force: THREE.Vector3): void;
    /**
     * 应用冲量到所有粒子
     * @param impulse 冲量向量
     */
    applyImpulse(impulse: THREE.Vector3): void;
    /**
     * 获取约束数组
     * @returns 约束数组
     */
    getConstraints(): SoftBodyConstraint[] | null;
    /**
     * 移除约束
     * @param constraintIndices 约束索引数组
     * @returns 是否成功移除
     */
    removeConstraints(constraintIndices: number[]): boolean;
    /**
     * 设置参数
     * @param name 参数名
     * @param value 参数值
     */
    setParameter(name: string, value: any): void;
    /**
     * 创建布料
     */
    private createCloth;
    /**
     * 创建绳索
     */
    private createRope;
    /**
     * 创建体积软体
     */
    private createVolumeSoftBody;
    /**
     * 创建物理约束
     */
    private createPhysicsConstraints;
    /**
     * 求解约束
     * @param deltaTime 时间步长
     */
    solveConstraints(deltaTime: number): void;
    /**
     * 更新网格
     */
    updateMesh(): void;
    /**
     * 更新布料网格
     */
    private updateClothMesh;
    /**
     * 更新绳索网格
     */
    private updateRopeMesh;
    /**
     * 更新体积软体网格
     */
    private updateVolumeMesh;
    /**
     * 创建气球
     */
    private createBalloon;
    /**
     * 创建果冻
     */
    private createJelly;
    /**
     * 添加约束
     * @param particleA 粒子A索引
     * @param particleB 粒子B索引
     * @param stiffness 刚度（可选）
     */
    private addConstraint;
    /**
     * 更新气球网格
     */
    private updateBalloonMesh;
    /**
     * 应用气球内部压力
     */
    private applyBalloonPressure;
    /**
     * 更新果冻网格
     */
    private updateJellyMesh;
    /**
     * 创建调试网格
     * @returns 调试网格
     */
    createDebugMesh(): THREE.Object3D | null;
    /**
     * 是否已初始化
     * @returns 是否已初始化
     */
    isInitialized(): boolean;
    /**
     * 销毁软体
     */
    destroy(): void;
}
export {};
