/**
 * MotionCaptureSystem类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as THREE from 'three';
import { MotionCaptureSystem } from '../../src/mocap/MotionCaptureSystem';
import { MotionCaptureComponent } from '../../src/mocap/MotionCaptureComponent';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { AvatarComponent } from '../../src/avatar/AvatarComponent';
import { PoseData, JointType } from '../../src/mocap/types';

describe('MotionCaptureSystem', () => {
  let engine: Engine;
  let world: World;
  let motionCaptureSystem: MotionCaptureSystem;
  let entity: Entity;
  let avatarComponent: AvatarComponent;
  let motionCaptureComponent: MotionCaptureComponent;
  
  // 在每个测试前创建一个新的动作捕捉系统实例
  beforeEach(() => {
    // 创建引擎和世界
    engine = new Engine({
      autoStart: false,
      debug: true
    });
    world = engine.getWorld();
    
    // 创建动作捕捉系统
    motionCaptureSystem = new MotionCaptureSystem();
    
    // 添加动作捕捉系统到引擎
    engine.addSystem(motionCaptureSystem);
    
    // 创建实体
    entity = new Entity(world);
    entity.name = '头像实体';
    
    // 添加变换组件
    const transform = new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity.addComponent(transform);
    
    // 创建头像组件
    avatarComponent = new AvatarComponent(entity, {
      modelUrl: 'models/avatar.glb',
      skeletonMapping: {
        hips: 'Hips',
        spine: 'Spine',
        head: 'Head',
        leftArm: 'LeftArm',
        rightArm: 'RightArm',
        leftHand: 'LeftHand',
        rightHand: 'RightHand',
        leftLeg: 'LeftLeg',
        rightLeg: 'RightLeg'
      }
    });
    entity.addComponent(avatarComponent);
    
    // 创建动作捕捉组件
    motionCaptureComponent = new MotionCaptureComponent(entity, {
      source: 'webcam',
      smoothing: 0.5,
      jointMapping: {
        [JointType.HEAD]: 'Head',
        [JointType.NECK]: 'Neck',
        [JointType.SPINE]: 'Spine',
        [JointType.HIPS]: 'Hips',
        [JointType.LEFT_SHOULDER]: 'LeftShoulder',
        [JointType.LEFT_ELBOW]: 'LeftElbow',
        [JointType.LEFT_WRIST]: 'LeftWrist',
        [JointType.RIGHT_SHOULDER]: 'RightShoulder',
        [JointType.RIGHT_ELBOW]: 'RightElbow',
        [JointType.RIGHT_WRIST]: 'RightWrist',
        [JointType.LEFT_HIP]: 'LeftHip',
        [JointType.LEFT_KNEE]: 'LeftKnee',
        [JointType.LEFT_ANKLE]: 'LeftAnkle',
        [JointType.RIGHT_HIP]: 'RightHip',
        [JointType.RIGHT_KNEE]: 'RightKnee',
        [JointType.RIGHT_ANKLE]: 'RightAnkle'
      }
    });
    entity.addComponent(motionCaptureComponent);
    
    // 添加实体到世界
    world.addEntity(entity);
    
    // 注册动作捕捉组件
    motionCaptureSystem.registerMotionCaptureComponent(entity, motionCaptureComponent);
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后销毁引擎实例
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });
  
  // 测试动作捕捉系统初始化
  it('应该正确初始化动作捕捉系统', () => {
    expect(motionCaptureSystem).toBeDefined();
    expect(motionCaptureSystem['motionCaptures'].size).toBe(1);
    expect(motionCaptureSystem['motionCaptures'].get(entity.getId())).toBe(motionCaptureComponent);
  });
  
  // 测试注册和取消注册动作捕捉组件
  it('应该能够注册和取消注册动作捕捉组件', () => {
    // 创建另一个实体
    const entity2 = new Entity(world);
    entity2.name = '头像实体2';
    
    // 添加变换组件
    const transform2 = new Transform({
      position: { x: 5, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
    entity2.addComponent(transform2);
    
    // 创建动作捕捉组件
    const motionCaptureComponent2 = new MotionCaptureComponent(entity2, {
      source: 'webcam',
      smoothing: 0.3
    });
    entity2.addComponent(motionCaptureComponent2);
    
    // 添加实体到世界
    world.addEntity(entity2);
    
    // 注册动作捕捉组件
    motionCaptureSystem.registerMotionCaptureComponent(entity2, motionCaptureComponent2);
    
    // 验证组件已注册
    expect(motionCaptureSystem['motionCaptures'].has(entity2.getId())).toBe(true);
    expect(motionCaptureSystem['motionCaptures'].get(entity2.getId())).toBe(motionCaptureComponent2);
    
    // 取消注册动作捕捉组件
    motionCaptureSystem.unregisterMotionCaptureComponent(entity2);
    
    // 验证组件已取消注册
    expect(motionCaptureSystem['motionCaptures'].has(entity2.getId())).toBe(false);
  });
  
  // 测试更新姿势数据
  it('应该能够更新姿势数据', () => {
    // 创建姿势数据
    const poseData: PoseData = {
      timestamp: Date.now(),
      joints: {
        [JointType.HEAD]: {
          position: { x: 0, y: 1.7, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.NECK]: {
          position: { x: 0, y: 1.5, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.SPINE]: {
          position: { x: 0, y: 1.2, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.HIPS]: {
          position: { x: 0, y: 1.0, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.LEFT_SHOULDER]: {
          position: { x: -0.2, y: 1.5, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.LEFT_ELBOW]: {
          position: { x: -0.4, y: 1.3, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.LEFT_WRIST]: {
          position: { x: -0.6, y: 1.1, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        }
      }
    };
    
    // 更新姿势数据
    motionCaptureComponent.updatePoseData(poseData);
    
    // 验证姿势数据已更新
    expect(motionCaptureComponent['currentPose']).toBe(poseData);
  });
  
  // 测试应用姿势到骨骼
  it('应该能够应用姿势到骨骼', () => {
    // 模拟头像模型
    const mockSkeleton = new THREE.Skeleton([
      new THREE.Bone(), // Hips
      new THREE.Bone(), // Spine
      new THREE.Bone(), // Head
      new THREE.Bone(), // LeftShoulder
      new THREE.Bone(), // LeftElbow
      new THREE.Bone(), // LeftWrist
      new THREE.Bone(), // RightShoulder
      new THREE.Bone(), // RightElbow
      new THREE.Bone()  // RightWrist
    ]);
    
    // 设置骨骼名称
    mockSkeleton.bones[0].name = 'Hips';
    mockSkeleton.bones[1].name = 'Spine';
    mockSkeleton.bones[2].name = 'Head';
    mockSkeleton.bones[3].name = 'LeftShoulder';
    mockSkeleton.bones[4].name = 'LeftElbow';
    mockSkeleton.bones[5].name = 'LeftWrist';
    mockSkeleton.bones[6].name = 'RightShoulder';
    mockSkeleton.bones[7].name = 'RightElbow';
    mockSkeleton.bones[8].name = 'RightWrist';
    
    // 创建骨骼映射
    const boneMap = new Map<string, THREE.Bone>();
    mockSkeleton.bones.forEach(bone => {
      boneMap.set(bone.name, bone);
    });
    
    // 设置头像模型和骨骼
    (avatarComponent as any).skeleton = mockSkeleton;
    (motionCaptureComponent as any).boneMap = boneMap;
    
    // 创建姿势数据
    const poseData: PoseData = {
      timestamp: Date.now(),
      joints: {
        [JointType.HEAD]: {
          position: { x: 0, y: 1.7, z: 0 },
          rotation: { x: 0.1, y: 0.2, z: 0.3, w: 0.9 },
          confidence: 0.9
        },
        [JointType.SPINE]: {
          position: { x: 0, y: 1.2, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.HIPS]: {
          position: { x: 0, y: 1.0, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        }
      }
    };
    
    // 更新姿势数据
    motionCaptureComponent.updatePoseData(poseData);
    
    // 应用姿势到骨骼
    motionCaptureComponent.applyPoseToSkeleton();
    
    // 验证骨骼旋转已更新
    const headBone = mockSkeleton.bones[2];
    expect(headBone.quaternion.x).toBeCloseTo(0.1);
    expect(headBone.quaternion.y).toBeCloseTo(0.2);
    expect(headBone.quaternion.z).toBeCloseTo(0.3);
    expect(headBone.quaternion.w).toBeCloseTo(0.9);
  });
  
  // 测试姿势平滑
  it('应该能够平滑姿势数据', () => {
    // 创建初始姿势数据
    const initialPose: PoseData = {
      timestamp: Date.now() - 100,
      joints: {
        [JointType.HEAD]: {
          position: { x: 0, y: 1.7, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        }
      }
    };
    
    // 设置初始姿势
    (motionCaptureComponent as any).currentPose = initialPose;
    (motionCaptureComponent as any).previousPose = initialPose;
    
    // 创建新姿势数据
    const newPose: PoseData = {
      timestamp: Date.now(),
      joints: {
        [JointType.HEAD]: {
          position: { x: 0.1, y: 1.8, z: 0.1 },
          rotation: { x: 0.1, y: 0.1, z: 0.1, w: 0.9 },
          confidence: 0.9
        }
      }
    };
    
    // 更新姿势数据
    motionCaptureComponent.updatePoseData(newPose);
    
    // 设置平滑系数
    motionCaptureComponent.setSmoothing(0.5);
    
    // 平滑姿势数据
    motionCaptureComponent.smoothPose();
    
    // 验证姿势数据已平滑
    const smoothedPose = motionCaptureComponent['currentPose'];
    const headJoint = smoothedPose.joints[JointType.HEAD];
    
    // 位置应该是初始位置和新位置的平均值
    expect(headJoint.position.x).toBeCloseTo(0.05);
    expect(headJoint.position.y).toBeCloseTo(1.75);
    expect(headJoint.position.z).toBeCloseTo(0.05);
    
    // 旋转应该是初始旋转和新旋转的平均值
    expect(headJoint.rotation.x).toBeCloseTo(0.05);
    expect(headJoint.rotation.y).toBeCloseTo(0.05);
    expect(headJoint.rotation.z).toBeCloseTo(0.05);
    expect(headJoint.rotation.w).toBeCloseTo(0.95);
  });
  
  // 测试姿势评估
  it('应该能够评估姿势', () => {
    // 创建姿势数据
    const poseData: PoseData = {
      timestamp: Date.now(),
      joints: {
        [JointType.HEAD]: {
          position: { x: 0, y: 1.7, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.SPINE]: {
          position: { x: 0, y: 1.2, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.HIPS]: {
          position: { x: 0, y: 1.0, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.LEFT_SHOULDER]: {
          position: { x: -0.2, y: 1.5, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.LEFT_ELBOW]: {
          position: { x: -0.4, y: 1.3, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.LEFT_WRIST]: {
          position: { x: -0.6, y: 1.1, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        }
      }
    };
    
    // 更新姿势数据
    motionCaptureComponent.updatePoseData(poseData);
    
    // 创建目标姿势
    const targetPose: PoseData = {
      timestamp: Date.now(),
      joints: {
        [JointType.HEAD]: {
          position: { x: 0, y: 1.7, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.SPINE]: {
          position: { x: 0, y: 1.2, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.HIPS]: {
          position: { x: 0, y: 1.0, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.LEFT_SHOULDER]: {
          position: { x: -0.2, y: 1.5, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.LEFT_ELBOW]: {
          position: { x: -0.4, y: 1.3, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        },
        [JointType.LEFT_WRIST]: {
          position: { x: -0.6, y: 1.1, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          confidence: 0.9
        }
      }
    };
    
    // 评估姿势
    const score = motionCaptureComponent.evaluatePose(targetPose);
    
    // 验证评估分数
    expect(score).toBeCloseTo(1.0); // 完全匹配
  });
});
