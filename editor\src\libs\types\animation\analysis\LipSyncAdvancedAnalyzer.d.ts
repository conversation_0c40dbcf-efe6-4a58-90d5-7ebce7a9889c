/**
 * 高级口型同步分析器
 * 提供更高级的音频分析算法，用于提高口型识别的准确性
 */
import { VisemeType } from '../FacialAnimation';
/**
 * 高级分析器配置
 */
export interface AdvancedAnalyzerConfig {
    /** 是否启用调试 */
    debug?: boolean;
    /** FFT大小 */
    fftSize?: number;
    /** 采样频率 */
    sampleRate?: number;
    /** 音量阈值 */
    volumeThreshold?: number;
    /** 平滑因子 */
    smoothingFactor?: number;
    /** 是否使用MFCC分析 */
    useMFCC?: boolean;
    /** 是否使用LPC分析 */
    useLPC?: boolean;
    /** 是否使用上下文预测 */
    useContextPrediction?: boolean;
    /** 上下文窗口大小 */
    contextWindowSize?: number;
    /** 频率带数量 */
    numFrequencyBands?: number;
    /** 梅尔滤波器数量 */
    numMelFilters?: number;
    /** 倒谱系数数量 */
    numCepstralCoeffs?: number;
    /** 是否使用AI预测 */
    useAIPrediction?: boolean;
    /** 是否使用频谱图分析 */
    useSpectrogram?: boolean;
    /** 是否使用音素识别 */
    usePhonemeRecognition?: boolean;
    /** 是否使用语音模式识别 */
    useSpeechPatternRecognition?: boolean;
    /** 是否使用在线学习 */
    useOnlineLearning?: boolean;
}
/**
 * 高级口型同步分析器
 */
export declare class LipSyncAdvancedAnalyzer {
    /** 配置 */
    private config;
    /** 默认配置 */
    private static readonly DEFAULT_CONFIG;
    /** 频率带边界 */
    private frequencyBandBoundaries;
    /** 频率带能量 */
    private frequencyBands;
    /** 梅尔滤波器组 */
    private melFilterBank;
    /** 上下文历史 */
    private visemeHistory;
    /** 音素到口型映射 */
    private phonemeToVisemeMap;
    /** 口型转换矩阵 - 用于上下文预测 */
    private visemeTransitionMatrix;
    /** AI预测器 */
    private aiPredictor;
    /** 频谱图历史 */
    private spectrogramHistory;
    /** 音素识别结果历史 */
    private phonemeHistory;
    /** 语音模式识别器 */
    private speechPatternRecognizer;
    /** 在线学习数据 */
    private onlineLearningData;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: Partial<AdvancedAnalyzerConfig>);
    /**
     * 初始化分析器
     */
    private initialize;
    /**
     * 初始化AI预测器
     */
    private initAIPredictor;
    /**
     * 初始化频谱图分析
     */
    private initSpectrogramAnalysis;
    /**
     * 初始化音素识别
     */
    private initPhonemeRecognition;
    /**
     * 初始化语音模式识别
     */
    private initSpeechPatternRecognition;
    /**
     * 初始化频率带边界
     */
    private initFrequencyBands;
    /**
     * 初始化梅尔滤波器组
     */
    private initMelFilterBank;
    /**
     * 初始化音素到口型映射
     */
    private initPhonemeToVisemeMap;
    /**
     * 初始化口型转换矩阵
     */
    private initVisemeTransitionMatrix;
    /**
     * 分析音频数据
     * @param spectrum 频谱数据
     * @returns 口型类型
     */
    analyzeAudio(spectrum: Float32Array): VisemeType;
    /**
     * 更新频谱图历史
     * @param spectrum 频谱数据
     */
    private updateSpectrogramHistory;
    /**
     * 识别音素
     * @param spectrum 频谱数据
     * @returns 识别的音素
     */
    private recognizePhoneme;
    /**
     * 提取MFCC特征
     * @param spectrum 频谱数据
     * @returns MFCC特征
     */
    private extractMFCC;
    /**
     * 收集训练数据
     * @param spectrum 频谱数据
     * @param viseme 口型
     */
    private collectTrainingData;
    /**
     * 计算RMS（均方根）
     * @param spectrum 频谱数据
     * @returns RMS值
     */
    private calculateRMS;
    /**
     * 计算频率带能量
     * @param spectrum 频谱数据
     */
    private calculateFrequencyBands;
    /**
     * 分析频率带并确定口型
     * @returns 口型类型
     */
    private analyzeFrequencyBands;
    /**
     * 使用MFCC分析音频
     * @param spectrum 频谱数据
     * @returns 口型类型
     */
    private analyzeMFCC;
    /**
     * 将MFCC映射到口型
     * @param mfcc MFCC特征
     * @returns 口型类型
     */
    private mapMFCCToViseme;
    /**
     * 使用LPC分析音频
     * @param spectrum 频谱数据
     * @returns 口型类型
     */
    private analyzeLPC;
    /**
     * 基于上下文预测口型
     * @param currentViseme 当前口型
     * @returns 预测的口型
     */
    private predictVisemeFromContext;
    /**
     * 更新口型历史
     * @param viseme 口型
     */
    private updateVisemeHistory;
    /**
     * 将Hz转换为梅尔刻度
     * @param hz 频率(Hz)
     * @returns 梅尔刻度值
     */
    private hzToMel;
    /**
     * 将梅尔刻度转换为Hz
     * @param mel 梅尔刻度值
     * @returns 频率(Hz)
     */
    private melToHz;
}
