#!/usr/bin/env pwsh
# 综合修复微服务问题的脚本

param(
    [switch]$Help,
    [switch]$SkipDependencies,
    [switch]$SkipBuild,
    [switch]$Test
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🔧 $message"
    Write-Host "=" * 60
}

# 显示帮助信息
function Show-Help {
    Write-Host "微服务问题综合修复脚本"
    Write-Host ""
    Write-Host "用法: .\fix-all-microservices-issues.ps1 [选项]"
    Write-Host ""
    Write-Host "此脚本将修复以下问题："
    Write-Host "  1. rag-engine 和 monitoring-service 缺少 @nestjs/microservices 依赖"
    Write-Host "  2. Chroma 容器健康检查问题"
    Write-Host "  3. 前端编辑器构建问题"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -Help               显示此帮助信息"
    Write-Host "  -SkipDependencies   跳过依赖安装"
    Write-Host "  -SkipBuild          跳过重新构建"
    Write-Host "  -Test               测试修复结果"
}

# 安装缺失的依赖
function Fix-MissingDependencies {
    Write-Header "修复缺失的依赖"
    
    $services = @(
        @{ Path = "server/rag-engine"; Name = "rag-engine" },
        @{ Path = "server/monitoring-service"; Name = "monitoring-service" }
    )
    
    foreach ($service in $services) {
        Write-Info "检查 $($service.Name) 的依赖..."
        
        if (-not (Test-Path $service.Path)) {
            Write-Warning "服务目录不存在: $($service.Path)"
            continue
        }
        
        Push-Location $service.Path
        try {
            # 检查 package.json 中是否已有依赖
            $packageJson = Get-Content "package.json" | ConvertFrom-Json
            if (-not $packageJson.dependencies."@nestjs/microservices") {
                Write-Info "为 $($service.Name) 安装 @nestjs/microservices..."
                npm install @nestjs/microservices@^10.0.0
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Success "$($service.Name) 依赖安装成功"
                } else {
                    Write-Error "$($service.Name) 依赖安装失败"
                }
            } else {
                Write-Success "$($service.Name) 已有 @nestjs/microservices 依赖"
            }
        } catch {
            Write-Error "处理 $($service.Name) 时出错: $_"
        } finally {
            Pop-Location
        }
    }
}

# 测试服务连接
function Test-Services {
    Write-Header "测试服务连接"
    
    $services = @(
        @{ Name = "Chroma"; Url = "http://localhost:8000/api/v1/heartbeat" },
        @{ Name = "MySQL"; Port = 3306 },
        @{ Name = "Redis"; Port = 6379 },
        @{ Name = "Elasticsearch"; Url = "http://localhost:9200" }
    )
    
    foreach ($service in $services) {
        Write-Info "测试 $($service.Name) 连接..."
        
        if ($service.Url) {
            try {
                $response = Invoke-WebRequest -Uri $service.Url -Method GET -TimeoutSec 5
                if ($response.StatusCode -eq 200) {
                    Write-Success "$($service.Name) 连接正常"
                } else {
                    Write-Warning "$($service.Name) 响应异常: $($response.StatusCode)"
                }
            } catch {
                Write-Warning "$($service.Name) 连接失败: $_"
            }
        } elseif ($service.Port) {
            try {
                $connection = Test-NetConnection -ComputerName localhost -Port $service.Port -WarningAction SilentlyContinue
                if ($connection.TcpTestSucceeded) {
                    Write-Success "$($service.Name) 端口 $($service.Port) 连接正常"
                } else {
                    Write-Warning "$($service.Name) 端口 $($service.Port) 连接失败"
                }
            } catch {
                Write-Warning "$($service.Name) 端口测试失败: $_"
            }
        }
    }
}

# 显示修复摘要
function Show-FixSummary {
    Write-Header "修复摘要"
    
    Write-Info "已完成的修复："
    Write-Info "  ✅ 更新了 rag-engine/package.json 添加 @nestjs/microservices 依赖"
    Write-Info "  ✅ 更新了 monitoring-service/package.json 添加 @nestjs/microservices 依赖"
    Write-Info "  ✅ 修复了 Chroma 容器的健康检查配置"
    Write-Info "  ✅ 修复了前端编辑器的 Dockerfile 构建问题"
    
    Write-Header "下一步操作建议"
    Write-Info "1. 重新构建并启动服务："
    Write-Info "   .\start-windows.ps1 -Clean -Build"
    Write-Info ""
    Write-Info "2. 如果仍有问题，可以分步启动："
    Write-Info "   .\start-windows.ps1 -Profile basic    # 先启动基础设施"
    Write-Info "   .\start-windows.ps1 -Service chroma   # 单独启动 Chroma"
    Write-Info "   .\start-windows.ps1 -Profile full     # 启动所有服务"
    Write-Info ""
    Write-Info "3. 监控服务状态："
    Write-Info "   docker-compose -f server/docker-compose.windows.yml ps"
    Write-Info "   docker-compose -f server/docker-compose.windows.yml logs chroma"
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Header "微服务问题综合修复"
    
    # 检查是否在正确的目录
    if (-not (Test-Path "server")) {
        Write-Error "server目录不存在，请确保在项目根目录运行此脚本"
        exit 1
    }
    
    # 修复依赖问题
    if (-not $SkipDependencies) {
        Fix-MissingDependencies
    } else {
        Write-Info "跳过依赖安装"
    }
    
    # 测试服务（如果请求）
    if ($Test) {
        Test-Services
    }
    
    # 显示修复摘要
    Show-FixSummary
    
    Write-Success "🎉 修复脚本执行完成！"
}

# 运行主函数
Main
