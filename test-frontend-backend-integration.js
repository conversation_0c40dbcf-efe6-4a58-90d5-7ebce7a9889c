/**
 * 前后端联调测试脚本
 * 测试前端与后端微服务的连接是否正常
 */

const axios = require('axios');

// 测试配置
const config = {
  apiGateway: 'http://localhost:3000/api',
  timeout: 10000,
  retries: 3
};

// 测试结果
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

/**
 * 执行HTTP请求测试
 */
async function testRequest(name, url, method = 'GET', data = null) {
  console.log(`\n🧪 测试: ${name}`);
  console.log(`📡 请求: ${method} ${url}`);
  
  try {
    const response = await axios({
      method,
      url,
      data,
      timeout: config.timeout,
      validateStatus: (status) => status < 500 // 接受4xx状态码
    });
    
    console.log(`✅ 成功: ${response.status} ${response.statusText}`);
    if (response.data) {
      console.log(`📄 响应数据类型: ${typeof response.data}`);
      if (Array.isArray(response.data)) {
        console.log(`📊 数组长度: ${response.data.length}`);
      }
    }
    testResults.passed++;
    return true;
  } catch (error) {
    console.log(`❌ 失败: ${error.message}`);
    if (error.response) {
      console.log(`📄 状态码: ${error.response.status}`);
      console.log(`📄 错误信息: ${error.response.data?.message || error.response.statusText}`);
    }
    testResults.failed++;
    testResults.errors.push({ name, error: error.message });
    return false;
  }
}

/**
 * 测试API网关健康状态
 */
async function testApiGatewayHealth() {
  console.log('\n🏥 测试API网关健康状态');
  return await testRequest(
    'API网关健康检查',
    `${config.apiGateway}/health`
  );
}

/**
 * 测试用户服务
 */
async function testUserService() {
  console.log('\n👤 测试用户服务');
  
  // 测试用户注册（可能会失败，因为用户可能已存在）
  await testRequest(
    '用户注册',
    `${config.apiGateway}/auth/register`,
    'POST',
    {
      username: 'testuser_' + Date.now(),
      email: `test_${Date.now()}@example.com`,
      password: 'testpassword123'
    }
  );
  
  // 测试用户登录（使用默认测试用户）
  const loginResult = await testRequest(
    '用户登录',
    `${config.apiGateway}/auth/login`,
    'POST',
    {
      email: '<EMAIL>',
      password: 'admin123'
    }
  );
  
  return loginResult;
}

/**
 * 测试项目服务
 */
async function testProjectService() {
  console.log('\n📁 测试项目服务');
  
  // 测试获取项目列表
  await testRequest(
    '获取项目列表',
    `${config.apiGateway}/projects`
  );
  
  // 测试创建项目
  await testRequest(
    '创建项目',
    `${config.apiGateway}/projects`,
    'POST',
    {
      name: '测试项目_' + Date.now(),
      description: '这是一个测试项目',
      isPublic: false
    }
  );
}

/**
 * 测试资产服务
 */
async function testAssetService() {
  console.log('\n🎨 测试资产服务');
  
  // 测试获取资产列表
  await testRequest(
    '获取资产列表',
    `${config.apiGateway}/assets`
  );
  
  // 测试获取资产文件夹
  await testRequest(
    '获取资产文件夹',
    `${config.apiGateway}/assets/folders`
  );
}

/**
 * 测试渲染服务
 */
async function testRenderService() {
  console.log('\n🎬 测试渲染服务');
  
  // 测试获取渲染任务列表
  await testRequest(
    '获取渲染任务列表',
    `${config.apiGateway}/render/jobs`
  );
  
  // 测试创建渲染任务
  await testRequest(
    '创建渲染任务',
    `${config.apiGateway}/render/jobs`,
    'POST',
    {
      name: '测试渲染任务_' + Date.now(),
      type: 'image',
      settings: {
        width: 1920,
        height: 1080,
        quality: 'high'
      }
    }
  );
}

/**
 * 测试示例服务
 */
async function testExampleService() {
  console.log('\n📚 测试示例服务');
  
  // 测试获取示例列表
  await testRequest(
    '获取示例列表',
    `${config.apiGateway}/examples`
  );
}

/**
 * 测试材质服务
 */
async function testMaterialService() {
  console.log('\n🎨 测试材质服务');
  
  // 测试获取材质列表
  await testRequest(
    '获取材质列表',
    `${config.apiGateway}/materials`
  );
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始前后端联调测试...\n');
  console.log(`🔗 API网关地址: ${config.apiGateway}`);
  console.log(`⏱️  超时时间: ${config.timeout}ms`);
  console.log(`🔄 重试次数: ${config.retries}`);
  
  // 执行各项测试
  await testApiGatewayHealth();
  await testUserService();
  await testProjectService();
  await testAssetService();
  await testRenderService();
  await testExampleService();
  await testMaterialService();
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 通过: ${testResults.passed}`);
  console.log(`❌ 失败: ${testResults.failed}`);
  console.log(`📈 成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.name}: ${error.error}`);
    });
  }
  
  console.log('\n🏁 测试完成!');
  
  // 如果有失败的测试，退出码为1
  if (testResults.failed > 0) {
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = { runTests, testResults };
