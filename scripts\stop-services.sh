#!/bin/bash

# DL引擎服务停止脚本
# 用于安全地停止所有微服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务函数
stop_service() {
    local service_name=$1
    log_info "停止 $service_name..."
    
    if docker-compose stop $service_name 2>/dev/null; then
        log_success "$service_name 已停止"
    else
        log_warning "$service_name 停止失败或未运行"
    fi
}

# 停止并删除服务
stop_and_remove_service() {
    local service_name=$1
    log_info "停止并删除 $service_name..."
    
    if docker-compose rm -f $service_name 2>/dev/null; then
        log_success "$service_name 已停止并删除"
    else
        log_warning "$service_name 删除失败或未运行"
    fi
}

# 优雅停止所有服务
graceful_stop() {
    log_info "开始优雅停止所有服务..."
    
    # 第一层：停止前端和API网关
    log_info "停止前端和API网关..."
    stop_service "editor"
    stop_service "api-gateway"
    
    # 第二层：停止协作服务
    log_info "停止协作服务..."
    stop_service "collaboration-load-balancer"
    stop_service "collaboration-service-1"
    stop_service "collaboration-service-2"
    
    # 第三层：停止业务微服务
    log_info "停止业务微服务..."
    stop_service "render-service"
    stop_service "asset-service"
    stop_service "project-service"
    stop_service "user-service"
    
    # 第四层：停止服务注册中心
    log_info "停止服务注册中心..."
    stop_service "service-registry"
    
    # 第五层：停止基础设施（可选）
    if [ "$1" = "--with-infrastructure" ]; then
        log_info "停止基础设施服务..."
        stop_service "redis"
        stop_service "mysql"
    fi
    
    log_success "所有服务已优雅停止"
}

# 强制停止所有服务
force_stop() {
    log_warning "强制停止所有服务..."
    docker-compose down --remove-orphans
    
    if [ "$1" = "--with-volumes" ]; then
        log_warning "删除所有数据卷..."
        docker-compose down -v --remove-orphans
    fi
    
    log_success "所有服务已强制停止"
}

# 停止监控服务
stop_monitoring() {
    log_info "停止监控服务..."
    if docker-compose -f docker-compose.monitoring.yml down 2>/dev/null; then
        log_success "监控服务已停止"
    else
        log_warning "监控服务停止失败或未运行"
    fi
}

# 清理Docker资源
cleanup_docker() {
    log_info "清理Docker资源..."
    
    # 清理停止的容器
    if [ "$(docker ps -aq -f status=exited)" ]; then
        docker rm $(docker ps -aq -f status=exited) 2>/dev/null || true
        log_success "已清理停止的容器"
    fi
    
    # 清理未使用的镜像
    if [ "$1" = "--cleanup-images" ]; then
        docker image prune -f
        log_success "已清理未使用的镜像"
    fi
    
    # 清理未使用的网络
    docker network prune -f 2>/dev/null || true
    log_success "已清理未使用的网络"
}

# 备份数据
backup_data() {
    if [ "$1" = "--backup" ]; then
        log_info "备份数据..."
        
        # 创建备份目录
        backup_dir="backup/$(date +%Y%m%d_%H%M%S)"
        mkdir -p $backup_dir
        
        # 备份数据库
        if docker ps | grep -q dl-engine-mysql; then
            log_info "备份MySQL数据库..."
            docker exec dl-engine-mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD:-dl_engine_password_2024} --all-databases > $backup_dir/mysql_backup.sql 2>/dev/null || true
            log_success "数据库备份完成: $backup_dir/mysql_backup.sql"
        fi
        
        # 备份上传文件
        if docker ps | grep -q dl-engine-asset-service; then
            log_info "备份上传文件..."
            docker cp dl-engine-asset-service:/app/uploads $backup_dir/uploads 2>/dev/null || true
            log_success "上传文件备份完成: $backup_dir/uploads"
        fi
        
        # 备份渲染输出
        if docker ps | grep -q dl-engine-render-service; then
            log_info "备份渲染输出..."
            docker cp dl-engine-render-service:/app/renders $backup_dir/renders 2>/dev/null || true
            log_success "渲染输出备份完成: $backup_dir/renders"
        fi
        
        log_success "数据备份完成: $backup_dir"
    fi
}

# 显示运行状态
show_status() {
    log_info "当前服务状态："
    docker-compose ps
    
    echo
    log_info "Docker容器状态："
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# 显示帮助信息
show_help() {
    echo "DL引擎服务停止脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --graceful              优雅停止服务（默认）"
    echo "  --force                 强制停止所有服务"
    echo "  --with-infrastructure   同时停止基础设施（MySQL, Redis）"
    echo "  --with-volumes          删除数据卷（注意：会丢失数据）"
    echo "  --with-monitoring       同时停止监控服务"
    echo "  --backup                停止前备份数据"
    echo "  --cleanup-images        清理未使用的Docker镜像"
    echo "  --status                显示当前服务状态"
    echo "  --help, -h              显示帮助信息"
    echo
    echo "示例:"
    echo "  $0                      # 优雅停止服务"
    echo "  $0 --force              # 强制停止所有服务"
    echo "  $0 --backup --graceful  # 备份数据后优雅停止"
    echo "  $0 --force --with-volumes # 强制停止并删除数据"
}

# 主函数
main() {
    echo "=========================================="
    echo "    DL引擎服务停止脚本"
    echo "=========================================="
    
    # 解析参数
    local graceful=true
    local force=false
    local with_infrastructure=false
    local with_volumes=false
    local with_monitoring=false
    local backup=false
    local cleanup_images=false
    local show_status_only=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --graceful)
                graceful=true
                force=false
                shift
                ;;
            --force)
                force=true
                graceful=false
                shift
                ;;
            --with-infrastructure)
                with_infrastructure=true
                shift
                ;;
            --with-volumes)
                with_volumes=true
                shift
                ;;
            --with-monitoring)
                with_monitoring=true
                shift
                ;;
            --backup)
                backup=true
                shift
                ;;
            --cleanup-images)
                cleanup_images=true
                shift
                ;;
            --status)
                show_status_only=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果只是查看状态
    if [ "$show_status_only" = true ]; then
        show_status
        exit 0
    fi
    
    # 备份数据（如果需要）
    if [ "$backup" = true ]; then
        backup_data --backup
    fi
    
    # 停止监控服务（如果需要）
    if [ "$with_monitoring" = true ]; then
        stop_monitoring
    fi
    
    # 停止服务
    if [ "$force" = true ]; then
        if [ "$with_volumes" = true ]; then
            force_stop --with-volumes
        else
            force_stop
        fi
    else
        if [ "$with_infrastructure" = true ]; then
            graceful_stop --with-infrastructure
        else
            graceful_stop
        fi
    fi
    
    # 清理Docker资源
    if [ "$cleanup_images" = true ]; then
        cleanup_docker --cleanup-images
    else
        cleanup_docker
    fi
    
    echo
    log_success "服务停止完成！"
    
    # 显示最终状态
    show_status
}

# 脚本入口
main "$@"
