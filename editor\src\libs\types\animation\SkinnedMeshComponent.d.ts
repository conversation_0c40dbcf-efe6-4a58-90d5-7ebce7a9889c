/**
 * 蒙皮网格组件
 * 用于管理蒙皮网格对象
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
export declare class SkinnedMeshComponent extends Component {
    static readonly type: string;
    private skinnedMesh;
    private skeleton;
    private bones;
    constructor();
    setSkinnedMesh(mesh: THREE.SkinnedMesh): void;
    getSkinnedMesh(): THREE.SkinnedMesh | null;
    getSkeleton(): THREE.Skeleton | null;
    getBones(): THREE.Bone[];
    getBone(name: string): THREE.Bone | null;
    getBoneByIndex(index: number): THREE.Bone | null;
    update(deltaTime: number): void;
    dispose(): void;
}
