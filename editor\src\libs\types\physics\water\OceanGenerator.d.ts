/**
 * 海洋生成器
 * 用于生成大型海洋水体
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { TerrainComponent } from '../../terrain/components/TerrainComponent';
/**
 * 海洋生成配置
 */
export interface OceanGeneratorConfig {
    /** 海洋位置 */
    position: THREE.Vector3;
    /** 海洋尺寸 */
    size: {
        width: number;
        depth: number;
    };
    /** 海洋深度 */
    depth: number;
    /** 分辨率 */
    resolution?: number;
    /** 是否跟随地形 */
    followTerrain?: boolean;
    /** 地形偏移 */
    terrainOffset?: number;
    /** 是否生成海床 */
    generateSeabed?: boolean;
    /** 海床材质 */
    seabedMaterial?: any;
    /** 是否生成水下植被 */
    generateUnderwaterVegetation?: boolean;
    /** 水下植被密度 */
    underwaterVegetationDensity?: number;
    /** 是否生成水下粒子 */
    generateUnderwaterParticles?: boolean;
    /** 水下粒子数量 */
    underwaterParticleCount?: number;
    /** 是否生成波浪 */
    generateWaves?: boolean;
    /** 波浪高度 */
    waveHeight?: number;
    /** 波浪频率 */
    waveFrequency?: number;
    /** 波浪速度 */
    waveSpeed?: number;
    /** 波浪方向 */
    waveDirection?: THREE.Vector2;
    /** 是否生成海岸线 */
    generateCoastline?: boolean;
    /** 海岸线宽度 */
    coastlineWidth?: number;
    /** 海岸线高度 */
    coastlineHeight?: number;
    /** 是否生成海洋泡沫 */
    generateFoam?: boolean;
    /** 泡沫密度 */
    foamDensity?: number;
    /** 是否生成反射 */
    generateReflection?: boolean;
    /** 反射强度 */
    reflectionStrength?: number;
    /** 是否生成折射 */
    generateRefraction?: boolean;
    /** 折射强度 */
    refractionStrength?: number;
    /** 是否生成因果波纹 */
    generateCaustics?: boolean;
    /** 因果波纹强度 */
    causticsStrength?: number;
}
/**
 * 海洋生成器
 */
export declare class OceanGenerator {
    /** 世界 */
    private world;
    /** 配置 */
    private config;
    /** 地形组件 */
    private terrainComponent;
    /** 海洋实体 */
    private oceanEntity;
    /** 海洋水体组件 */
    private oceanWaterBody;
    /** 海洋几何体 */
    private oceanGeometry;
    /** 海床几何体 */
    private seabedGeometry;
    /** 海岸线几何体 */
    private coastlineGeometry;
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    constructor(world: World, config: OceanGeneratorConfig);
    /**
     * 设置地形组件
     * @param terrainComponent 地形组件
     */
    setTerrainComponent(terrainComponent: TerrainComponent): void;
    /**
     * 生成海洋
     * @returns 海洋实体
     */
    generate(): Entity;
    /**
     * 创建海洋几何体
     */
    private createOceanGeometry;
    /**
     * 创建海洋水体
     */
    private createOceanWaterBody;
    /**
     * 创建海床
     */
    private createSeabed;
    /**
     * 创建海岸线
     */
    private createCoastline;
    /**
     * 获取海洋边缘点
     * @returns 边缘点数组
     */
    private getOceanEdgePoints;
    /**
     * 创建水下植被
     */
    private createUnderwaterVegetation;
    /**
     * 获取指定位置的地形高度
     * @param x X坐标
     * @param z Z坐标
     * @returns 地形高度，如果无法获取则返回null
     */
    private getTerrainHeight;
    /**
     * 创建水下粒子
     */
    private createUnderwaterParticles;
    /**
     * 基于种子的随机数生成器
     * @param seed 种子
     * @returns 随机数生成函数
     */
    private seededRandom;
}
