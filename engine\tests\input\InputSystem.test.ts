/**
 * InputSystem类单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { InputSystem } from '../../src/input/InputSystem';
import { InputManager } from '../../src/input/InputManager';
import { KeyboardDevice } from '../../src/input/devices/KeyboardDevice';
import { MouseDevice } from '../../src/input/devices/MouseDevice';
import { TouchDevice } from '../../src/input/devices/TouchDevice';
import { GamepadDevice } from '../../src/input/devices/GamepadDevice';
import { ButtonInputAction } from '../../src/input/InputAction';
import { InputBinding } from '../../src/input/InputBinding';
import { InputMapping } from '../../src/input/InputMapping';
import { KeyCode } from '../../src/input/KeyCode';
import { MouseButton } from '../../src/input/MouseButton';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';

// 模拟DOM事件
function createKeyboardEvent(type: string, key: string, code: string): KeyboardEvent {
  return new KeyboardEvent(type, { key, code, bubbles: true });
}

function createMouseEvent(type: string, button: number = 0): MouseEvent {
  return new MouseEvent(type, { button, bubbles: true });
}

describe('InputSystem', () => {
  let engine: Engine;
  let world: World;
  let inputSystem: InputSystem;
  let inputManager: InputManager;
  let mockCanvas: HTMLCanvasElement;
  
  // 在每个测试前创建一个新的输入系统
  beforeEach(() => {
    // 创建模拟的canvas元素
    mockCanvas = document.createElement('canvas');
    document.body.appendChild(mockCanvas);
    
    // 创建引擎和世界
    engine = new Engine({ 
      canvas: mockCanvas,
      autoStart: false 
    });
    world = engine.getWorld();
    
    // 创建输入系统
    inputSystem = new InputSystem({
      element: mockCanvas,
      enableKeyboard: true,
      enableMouse: true,
      enableTouch: true,
      enableGamepad: true
    });
    
    // 添加输入系统到引擎
    engine.addSystem(inputSystem);
    
    // 获取输入管理器
    inputManager = InputManager.getInstance();
    
    // 初始化引擎
    engine.initialize();
  });
  
  // 在每个测试后清理
  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
    
    // 清理DOM
    if (mockCanvas && mockCanvas.parentNode) {
      mockCanvas.parentNode.removeChild(mockCanvas);
    }
    
    // 重置InputManager单例
    (InputManager as any).instance = null;
  });
  
  // 测试输入系统初始化
  it('应该正确初始化输入系统', () => {
    expect(inputSystem).toBeDefined();
    expect(inputSystem['inputManager']).toBeDefined();
    expect(inputSystem['initialized']).toBe(true);
  });
  
  // 测试键盘输入
  it('应该处理键盘输入', () => {
    // 创建键盘动作
    const jumpAction = new ButtonInputAction('jump');
    
    // 创建键盘映射
    const jumpMapping = new InputMapping('jump', 'keyboard', 'keydown', KeyCode.SPACE);
    
    // 创建键盘绑定
    const jumpBinding = new InputBinding(jumpAction, jumpMapping);
    
    // 添加动作、映射和绑定到输入管理器
    inputManager.addAction(jumpAction);
    inputManager.addMapping(jumpMapping);
    inputManager.addBinding(jumpBinding);
    
    // 创建事件监听器
    const jumpListener = vi.fn();
    
    // 添加事件监听器
    inputManager.on('jump', jumpListener);
    
    // 模拟键盘按下事件
    mockCanvas.dispatchEvent(createKeyboardEvent('keydown', ' ', 'Space'));
    
    // 更新输入系统
    inputSystem.update(0.016);
    
    // 验证动作状态
    expect(jumpAction.getValue()).toBe(true);
    expect(jumpAction.isJustPressed()).toBe(true);
    
    // 验证事件监听器被调用
    expect(jumpListener).toHaveBeenCalled();
    
    // 模拟键盘释放事件
    mockCanvas.dispatchEvent(createKeyboardEvent('keyup', ' ', 'Space'));
    
    // 更新输入系统
    inputSystem.update(0.016);
    
    // 验证动作状态
    expect(jumpAction.getValue()).toBe(false);
    expect(jumpAction.isJustReleased()).toBe(true);
  });
  
  // 测试鼠标输入
  it('应该处理鼠标输入', () => {
    // 创建鼠标动作
    const clickAction = new ButtonInputAction('click');
    
    // 创建鼠标映射
    const clickMapping = new InputMapping('click', 'mouse', 'mousedown', MouseButton.LEFT);
    
    // 创建鼠标绑定
    const clickBinding = new InputBinding(clickAction, clickMapping);
    
    // 添加动作、映射和绑定到输入管理器
    inputManager.addAction(clickAction);
    inputManager.addMapping(clickMapping);
    inputManager.addBinding(clickBinding);
    
    // 创建事件监听器
    const clickListener = vi.fn();
    
    // 添加事件监听器
    inputManager.on('click', clickListener);
    
    // 模拟鼠标按下事件
    mockCanvas.dispatchEvent(createMouseEvent('mousedown', 0));
    
    // 更新输入系统
    inputSystem.update(0.016);
    
    // 验证动作状态
    expect(clickAction.getValue()).toBe(true);
    expect(clickAction.isJustPressed()).toBe(true);
    
    // 验证事件监听器被调用
    expect(clickListener).toHaveBeenCalled();
    
    // 模拟鼠标释放事件
    mockCanvas.dispatchEvent(createMouseEvent('mouseup', 0));
    
    // 更新输入系统
    inputSystem.update(0.016);
    
    // 验证动作状态
    expect(clickAction.getValue()).toBe(false);
    expect(clickAction.isJustReleased()).toBe(true);
  });
  
  // 测试输入录制和回放
  it('应该能够录制和回放输入', () => {
    // 启用输入录制
    inputSystem.startRecording();
    
    // 创建键盘动作
    const jumpAction = new ButtonInputAction('jump');
    
    // 创建键盘映射
    const jumpMapping = new InputMapping('jump', 'keyboard', 'keydown', KeyCode.SPACE);
    
    // 创建键盘绑定
    const jumpBinding = new InputBinding(jumpAction, jumpMapping);
    
    // 添加动作、映射和绑定到输入管理器
    inputManager.addAction(jumpAction);
    inputManager.addMapping(jumpMapping);
    inputManager.addBinding(jumpBinding);
    
    // 模拟键盘按下事件
    mockCanvas.dispatchEvent(createKeyboardEvent('keydown', ' ', 'Space'));
    
    // 更新输入系统
    inputSystem.update(0.016);
    
    // 模拟键盘释放事件
    mockCanvas.dispatchEvent(createKeyboardEvent('keyup', ' ', 'Space'));
    
    // 更新输入系统
    inputSystem.update(0.016);
    
    // 停止录制
    const recording = inputSystem.stopRecording();
    
    // 验证录制结果
    expect(recording).toBeDefined();
    expect(recording.events.length).toBeGreaterThan(0);
    
    // 重置动作状态
    jumpAction.reset();
    
    // 创建事件监听器
    const jumpListener = vi.fn();
    
    // 添加事件监听器
    inputManager.on('jump', jumpListener);
    
    // 开始回放
    inputSystem.startPlayback(recording);
    
    // 更新输入系统（模拟时间流逝）
    for (let i = 0; i < 10; i++) {
      inputSystem.update(0.016);
    }
    
    // 验证事件监听器被调用
    expect(jumpListener).toHaveBeenCalled();
    
    // 停止回放
    inputSystem.stopPlayback();
  });
  
  // 测试输入设备管理
  it('应该能够管理输入设备', () => {
    // 验证默认设备已注册
    expect(inputManager.getDevice('keyboard')).toBeInstanceOf(KeyboardDevice);
    expect(inputManager.getDevice('mouse')).toBeInstanceOf(MouseDevice);
    expect(inputManager.getDevice('touch')).toBeInstanceOf(TouchDevice);
    expect(inputManager.getDevice('gamepad')).toBeInstanceOf(GamepadDevice);
    
    // 禁用键盘设备
    inputSystem.disableDevice('keyboard');
    
    // 验证键盘设备已禁用
    expect(inputManager.getDevice('keyboard').isEnabled()).toBe(false);
    
    // 启用键盘设备
    inputSystem.enableDevice('keyboard');
    
    // 验证键盘设备已启用
    expect(inputManager.getDevice('keyboard').isEnabled()).toBe(true);
  });
});
