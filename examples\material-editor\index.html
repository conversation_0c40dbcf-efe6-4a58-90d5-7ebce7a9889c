<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>材质编辑器演示</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      font-family: 'Microsoft YaHei', sans-serif;
    }
    #canvas-container {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    #ui-container {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 10px;
      border-radius: 5px;
      width: 300px;
      z-index: 10;
    }
    h2 {
      margin-top: 0;
      color: #333;
    }
    .material-group {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 5px;
    }
    .material-title {
      font-weight: bold;
      color: #2196F3;
      margin-bottom: 5px;
    }
    .material-description {
      font-size: 14px;
      color: #555;
      margin-bottom: 10px;
    }
    .control-group {
      margin-bottom: 10px;
    }
    label {
      display: block;
      margin-bottom: 3px;
      font-size: 14px;
    }
    input[type="range"] {
      width: 100%;
    }
    input[type="color"] {
      width: 50px;
      height: 25px;
      vertical-align: middle;
    }
    .color-value {
      display: inline-block;
      margin-left: 10px;
      font-size: 12px;
      color: #666;
    }
    .material-selector {
      margin-bottom: 15px;
    }
    select {
      width: 100%;
      padding: 5px;
      border-radius: 3px;
      border: 1px solid #ccc;
    }
    .info-box {
      background-color: #e3f2fd;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      font-size: 14px;
      color: #0d47a1;
      border-left: 4px solid #2196F3;
    }
  </style>
</head>
<body>
  <div id="canvas-container"></div>
  <div id="ui-container">
    <h2>材质编辑器演示</h2>
    
    <div class="info-box">
      本示例展示了如何使用材质编辑器创建和编辑不同类型的材质。
      选择不同的材质类型，并调整参数来查看效果。
    </div>
    
    <div class="material-selector">
      <label for="material-type">材质类型:</label>
      <select id="material-type">
        <option value="standard">标准材质 (PBR)</option>
        <option value="basic">基础材质</option>
        <option value="phong">Phong材质</option>
        <option value="toon">卡通材质</option>
        <option value="physical">物理材质</option>
      </select>
    </div>
    
    <div id="standard-controls" class="material-group">
      <div class="material-title">标准材质 (PBR)</div>
      <div class="material-description">基于物理的渲染材质，适用于大多数真实世界的物体。</div>
      
      <div class="control-group">
        <label for="standard-color">颜色:</label>
        <input type="color" id="standard-color" value="#4285f4">
        <span class="color-value" id="standard-color-value">#4285f4</span>
      </div>
      
      <div class="control-group">
        <label for="standard-metalness">金属度: <span id="standard-metalness-value">0.5</span></label>
        <input type="range" id="standard-metalness" min="0" max="1" step="0.01" value="0.5">
      </div>
      
      <div class="control-group">
        <label for="standard-roughness">粗糙度: <span id="standard-roughness-value">0.5</span></label>
        <input type="range" id="standard-roughness" min="0" max="1" step="0.01" value="0.5">
      </div>
      
      <div class="control-group">
        <label for="standard-emissive">自发光颜色:</label>
        <input type="color" id="standard-emissive" value="#000000">
        <span class="color-value" id="standard-emissive-value">#000000</span>
      </div>
      
      <div class="control-group">
        <label for="standard-emissive-intensity">自发光强度: <span id="standard-emissive-intensity-value">1</span></label>
        <input type="range" id="standard-emissive-intensity" min="0" max="5" step="0.1" value="1">
      </div>
    </div>
    
    <div id="basic-controls" class="material-group" style="display: none;">
      <div class="material-title">基础材质</div>
      <div class="material-description">简单的不受光照影响的材质，适用于简单的彩色对象。</div>
      
      <div class="control-group">
        <label for="basic-color">颜色:</label>
        <input type="color" id="basic-color" value="#4285f4">
        <span class="color-value" id="basic-color-value">#4285f4</span>
      </div>
      
      <div class="control-group">
        <label for="basic-opacity">不透明度: <span id="basic-opacity-value">1.0</span></label>
        <input type="range" id="basic-opacity" min="0" max="1" step="0.01" value="1.0">
      </div>
    </div>
    
    <div id="phong-controls" class="material-group" style="display: none;">
      <div class="material-title">Phong材质</div>
      <div class="material-description">经典的Phong光照模型材质，适用于塑料、陶瓷等物体。</div>
      
      <div class="control-group">
        <label for="phong-color">颜色:</label>
        <input type="color" id="phong-color" value="#4285f4">
        <span class="color-value" id="phong-color-value">#4285f4</span>
      </div>
      
      <div class="control-group">
        <label for="phong-specular">高光颜色:</label>
        <input type="color" id="phong-specular" value="#ffffff">
        <span class="color-value" id="phong-specular-value">#ffffff</span>
      </div>
      
      <div class="control-group">
        <label for="phong-shininess">光泽度: <span id="phong-shininess-value">30</span></label>
        <input type="range" id="phong-shininess" min="0" max="100" step="1" value="30">
      </div>
    </div>
    
    <div id="toon-controls" class="material-group" style="display: none;">
      <div class="material-title">卡通材质</div>
      <div class="material-description">卡通渲染材质，适用于动画和游戏风格的对象。</div>
      
      <div class="control-group">
        <label for="toon-color">颜色:</label>
        <input type="color" id="toon-color" value="#4285f4">
        <span class="color-value" id="toon-color-value">#4285f4</span>
      </div>
      
      <div class="control-group">
        <label for="toon-steps">色阶数: <span id="toon-steps-value">3</span></label>
        <input type="range" id="toon-steps" min="1" max="10" step="1" value="3">
      </div>
    </div>
    
    <div id="physical-controls" class="material-group" style="display: none;">
      <div class="material-title">物理材质</div>
      <div class="material-description">高级物理材质，适用于玻璃、金属等复杂材质。</div>
      
      <div class="control-group">
        <label for="physical-color">颜色:</label>
        <input type="color" id="physical-color" value="#4285f4">
        <span class="color-value" id="physical-color-value">#4285f4</span>
      </div>
      
      <div class="control-group">
        <label for="physical-metalness">金属度: <span id="physical-metalness-value">0.5</span></label>
        <input type="range" id="physical-metalness" min="0" max="1" step="0.01" value="0.5">
      </div>
      
      <div class="control-group">
        <label for="physical-roughness">粗糙度: <span id="physical-roughness-value">0.5</span></label>
        <input type="range" id="physical-roughness" min="0" max="1" step="0.01" value="0.5">
      </div>
      
      <div class="control-group">
        <label for="physical-clearcoat">清漆强度: <span id="physical-clearcoat-value">0</span></label>
        <input type="range" id="physical-clearcoat" min="0" max="1" step="0.01" value="0">
      </div>
      
      <div class="control-group">
        <label for="physical-clearcoat-roughness">清漆粗糙度: <span id="physical-clearcoat-roughness-value">0</span></label>
        <input type="range" id="physical-clearcoat-roughness" min="0" max="1" step="0.01" value="0">
      </div>
    </div>
  </div>

  <script type="module">
    import { Engine, World, Entity, Scene, Transform, Camera, Mesh, Material, Light } from '/engine/dist/index.js';

    // 初始化引擎
    const engine = new Engine({
      container: document.getElementById('canvas-container'),
      antialias: true,
      shadows: true,
    });

    // 创建世界
    const world = new World(engine);

    // 创建场景
    const scene = new Scene(world, {
      name: '材质编辑器演示场景',
      background: { type: 'color', value: '#87CEEB' },
    });

    // 创建相机
    const camera = new Entity(world)
      .addComponent(new Camera({
        type: 'perspective',
        fov: 60,
        near: 0.1,
        far: 1000,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 2, z: 5 },
        rotation: { x: -0.2, y: 0, z: 0 },
      }));

    // 添加相机到场景
    scene.addEntity(camera);

    // 创建环境光
    const ambientLight = new Entity(world)
      .addComponent(new Light({
        type: 'ambient',
        color: '#ffffff',
        intensity: 0.3,
      }));

    // 添加环境光到场景
    scene.addEntity(ambientLight);

    // 创建平行光
    const directionalLight = new Entity(world)
      .addComponent(new Light({
        type: 'directional',
        color: '#ffffff',
        intensity: 0.8,
        castShadow: true,
      }))
      .addComponent(new Transform({
        position: { x: 5, y: 10, z: 5 },
        rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
      }));

    // 添加平行光到场景
    scene.addEntity(directionalLight);

    // 创建地面
    const ground = new Entity(world)
      .addComponent(new Mesh({
        geometry: { type: 'plane', width: 20, height: 20 },
        receiveShadow: true,
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#cccccc',
        roughness: 0.9,
        metalness: 0.1,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: -Math.PI / 2, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      }));

    // 添加地面到场景
    scene.addEntity(ground);

    // 创建展示对象
    const sphere = new Entity(world)
      .addComponent(new Mesh({
        geometry: { type: 'sphere', radius: 1, widthSegments: 64, heightSegments: 64 },
        castShadow: true,
      }))
      .addComponent(new Material({
        type: 'standard',
        color: '#4285f4',
        roughness: 0.5,
        metalness: 0.5,
      }))
      .addComponent(new Transform({
        position: { x: 0, y: 1, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
      }));

    // 添加球体到场景
    scene.addEntity(sphere);

    // 启动引擎
    engine.start();

    // 材质控制
    const materialType = document.getElementById('material-type');
    const materialGroups = {
      standard: document.getElementById('standard-controls'),
      basic: document.getElementById('basic-controls'),
      phong: document.getElementById('phong-controls'),
      toon: document.getElementById('toon-controls'),
      physical: document.getElementById('physical-controls')
    };

    // 切换材质类型
    materialType.addEventListener('change', () => {
      const selectedType = materialType.value;
      
      // 隐藏所有材质控制组
      for (const type in materialGroups) {
        materialGroups[type].style.display = 'none';
      }
      
      // 显示选中的材质控制组
      materialGroups[selectedType].style.display = 'block';
      
      // 更新材质
      updateMaterial();
    });

    // 更新材质函数
    function updateMaterial() {
      const selectedType = materialType.value;
      let materialConfig = {};
      
      switch (selectedType) {
        case 'standard':
          materialConfig = {
            type: 'standard',
            color: document.getElementById('standard-color').value,
            roughness: parseFloat(document.getElementById('standard-roughness').value),
            metalness: parseFloat(document.getElementById('standard-metalness').value),
            emissive: document.getElementById('standard-emissive').value,
            emissiveIntensity: parseFloat(document.getElementById('standard-emissive-intensity').value)
          };
          break;
        case 'basic':
          materialConfig = {
            type: 'basic',
            color: document.getElementById('basic-color').value,
            opacity: parseFloat(document.getElementById('basic-opacity').value),
            transparent: parseFloat(document.getElementById('basic-opacity').value) < 1.0
          };
          break;
        case 'phong':
          materialConfig = {
            type: 'phong',
            color: document.getElementById('phong-color').value,
            specular: document.getElementById('phong-specular').value,
            shininess: parseFloat(document.getElementById('phong-shininess').value)
          };
          break;
        case 'toon':
          materialConfig = {
            type: 'toon',
            color: document.getElementById('toon-color').value,
            steps: parseInt(document.getElementById('toon-steps').value)
          };
          break;
        case 'physical':
          materialConfig = {
            type: 'physical',
            color: document.getElementById('physical-color').value,
            metalness: parseFloat(document.getElementById('physical-metalness').value),
            roughness: parseFloat(document.getElementById('physical-roughness').value),
            clearcoat: parseFloat(document.getElementById('physical-clearcoat').value),
            clearcoatRoughness: parseFloat(document.getElementById('physical-clearcoat-roughness').value)
          };
          break;
      }
      
      // 更新球体材质
      sphere.getComponent(Material).setProperties(materialConfig);
    }

    // 添加所有控制器的事件监听
    const controls = document.querySelectorAll('input[type="range"], input[type="color"]');
    controls.forEach(control => {
      control.addEventListener('input', (e) => {
        // 更新显示值
        if (e.target.type === 'range') {
          const valueDisplay = document.getElementById(`${e.target.id}-value`);
          if (valueDisplay) {
            valueDisplay.textContent = e.target.value;
          }
        } else if (e.target.type === 'color') {
          const valueDisplay = document.getElementById(`${e.target.id}-value`);
          if (valueDisplay) {
            valueDisplay.textContent = e.target.value;
          }
        }
        
        // 更新材质
        updateMaterial();
      });
    });

    // 旋转展示对象
    engine.onUpdate = (deltaTime) => {
      const transform = sphere.getComponent(Transform);
      transform.rotation.y += deltaTime * 0.5;
    };
  </script>
</body>
</html>
