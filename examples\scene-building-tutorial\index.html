<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>场景构建教程 - DL（Digital Learning）引擎</title>
  <link rel="stylesheet" href="styles/main.css">
</head>
<body>
  <div id="app">
    <div id="canvas-container"></div>
    <div id="tutorial-container">
      <div class="tutorial-panel">
        <div class="tutorial-header">
          <h1>场景构建教程</h1>
          <div class="tutorial-controls">
            <button id="prev-step" disabled>上一步</button>
            <span id="step-indicator">步骤 1/9</span>
            <button id="next-step">下一步</button>
          </div>
        </div>
        <div class="tutorial-content">
          <div class="step-title">
            <h2>步骤 1: 创建新场景</h2>
          </div>
          <div class="step-description">
            <p>在本步骤中，我们将创建一个新的场景，并设置基本参数。</p>
            <ol>
              <li>点击右侧的"创建场景"按钮</li>
              <li>设置场景名称为"山谷村庄"</li>
              <li>设置场景大小为1000x1000</li>
              <li>点击"确认"按钮创建场景</li>
            </ol>
            <div class="tip">
              <strong>提示：</strong> 场景大小应根据项目需求设置，过大的场景可能会影响性能。
            </div>
          </div>
          <div class="step-actions">
            <button id="create-scene-btn" class="action-button">创建场景</button>
          </div>
        </div>
      </div>
      <div class="tools-panel">
        <h2>工具箱</h2>
        <div class="tool-group">
          <h3>地形工具</h3>
          <button class="tool-button" data-tool="raise">抬高</button>
          <button class="tool-button" data-tool="lower">降低</button>
          <button class="tool-button" data-tool="smooth">平滑</button>
          <button class="tool-button" data-tool="flatten">平整</button>
          <div class="tool-settings">
            <label>
              笔刷大小:
              <input type="range" id="brush-size" min="1" max="50" value="10">
              <span id="brush-size-value">10</span>
            </label>
            <label>
              笔刷强度:
              <input type="range" id="brush-strength" min="1" max="100" value="50">
              <span id="brush-strength-value">50</span>
            </label>
          </div>
        </div>
        <div class="tool-group">
          <h3>纹理工具</h3>
          <div class="texture-selector">
            <div class="texture-item" data-texture="grass">
              <img src="assets/textures/grass_thumb.jpg" alt="草地">
              <span>草地</span>
            </div>
            <div class="texture-item" data-texture="rock">
              <img src="assets/textures/rock_thumb.jpg" alt="岩石">
              <span>岩石</span>
            </div>
            <div class="texture-item" data-texture="sand">
              <img src="assets/textures/sand_thumb.jpg" alt="沙地">
              <span>沙地</span>
            </div>
            <div class="texture-item" data-texture="snow">
              <img src="assets/textures/snow_thumb.jpg" alt="雪地">
              <span>雪地</span>
            </div>
          </div>
        </div>
        <div class="tool-group">
          <h3>对象工具</h3>
          <button class="tool-button" data-tool="select">选择</button>
          <button class="tool-button" data-tool="move">移动</button>
          <button class="tool-button" data-tool="rotate">旋转</button>
          <button class="tool-button" data-tool="scale">缩放</button>
        </div>
        <div class="tool-group">
          <h3>对象库</h3>
          <div class="object-categories">
            <button class="category-button active" data-category="buildings">建筑</button>
            <button class="category-button" data-category="vegetation">植被</button>
            <button class="category-button" data-category="props">道具</button>
            <button class="category-button" data-category="effects">特效</button>
          </div>
          <div class="object-list" id="object-list">
            <!-- 对象列表将通过JavaScript动态生成 -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import { Engine, World, Entity, Scene, Transform, Camera, Mesh, Material, Light, TerrainComponent, WaterComponent, Sky, VegetationComponent } from '/engine/dist/index.js';
    import { TutorialManager } from './scripts/TutorialManager.js';
    import { TerrainEditor } from './scripts/TerrainEditor.js';
    import { ObjectLibrary } from './scripts/ObjectLibrary.js';
    import { SceneBuilder } from './scripts/SceneBuilder.js';

    // 初始化引擎
    const engine = new Engine({
      container: document.getElementById('canvas-container'),
      antialias: true,
      shadows: true,
    });

    // 创建世界
    const world = new World(engine);

    // 创建场景构建器
    const sceneBuilder = new SceneBuilder(world);

    // 创建地形编辑器
    const terrainEditor = new TerrainEditor(world, sceneBuilder);

    // 创建对象库
    const objectLibrary = new ObjectLibrary();

    // 创建教程管理器
    const tutorialManager = new TutorialManager(sceneBuilder, terrainEditor, objectLibrary);

    // 启动引擎
    engine.start();

    // 初始化教程
    tutorialManager.init();
  </script>
</body>
</html>
