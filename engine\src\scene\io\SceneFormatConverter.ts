/**
 * 场景格式转换器
 * 用于在不同格式之间转换场景
 */
import * as THREE from 'three';
import { Scene } from '../Scene';
import type { Entity } from '../../core/Entity';
import { SceneSerializer } from '../SceneSerializer';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

// 导入Three.js加载器
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';

// 导入Three.js导出器（注意：某些导出器可能不存在）
// import { GLTFExporter } from 'three/examples/jsm/exporters/GLTFExporter';

/**
 * 支持的场景格式
 */
export enum SceneFormat {
  /** JSON格式 */
  JSON = 'json',
  /** GLTF格式 */
  GLTF = 'gltf',
  /** GLB格式 */
  GLB = 'glb',
  /** FBX格式 */
  FBX = 'fbx',
  /** OBJ格式 */
  OBJ = 'obj',
  /** DAE格式（Collada） */
  DAE = 'dae'
}

/**
 * 场景导入选项
 */
export interface SceneImportOptions {
  /** 是否合并到现有场景 */
  mergeWithExisting?: boolean;
  /** 是否保留现有实体 */
  keepExistingEntities?: boolean;
  /** 是否导入材质 */
  importMaterials?: boolean;
  /** 是否导入纹理 */
  importTextures?: boolean;
  /** 是否导入动画 */
  importAnimations?: boolean;
  /** 是否导入相机 */
  importCameras?: boolean;
  /** 是否导入灯光 */
  importLights?: boolean;
  /** 是否自动计算法线 */
  computeNormals?: boolean;
  /** 是否自动计算切线 */
  computeTangents?: boolean;
  /** 是否优化几何体 */
  optimizeGeometry?: boolean;
  /** 是否转换为实例化渲染 */
  convertToInstanced?: boolean;
  /** 是否生成LOD级别 */
  generateLOD?: boolean;
  /** 是否应用变换 */
  applyTransforms?: boolean;
  /** 自定义元数据 */
  metadata?: Record<string, any>;
}

/**
 * 场景导出选项
 */
export interface SceneExportOptions {
  /** 导出格式 */
  format?: SceneFormat;
  /** 是否导出材质 */
  exportMaterials?: boolean;
  /** 是否导出纹理 */
  exportTextures?: boolean;
  /** 是否导出动画 */
  exportAnimations?: boolean;
  /** 是否导出相机 */
  exportCameras?: boolean;
  /** 是否导出灯光 */
  exportLights?: boolean;
  /** 是否二进制格式 */
  binary?: boolean;
  /** 是否压缩 */
  compressed?: boolean;
  /** 是否美化输出 */
  prettyPrint?: boolean;
  /** 是否嵌入资源 */
  embedResources?: boolean;
  /** 自定义元数据 */
  metadata?: Record<string, any>;
}

/**
 * 场景格式转换器
 */
export class SceneFormatConverter extends EventEmitter {
  /** 场景序列化器 */
  private sceneSerializer: SceneSerializer;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景格式转换器
   * @param sceneSerializer 场景序列化器
   */
  constructor(sceneSerializer: SceneSerializer) {
    super();
    this.sceneSerializer = sceneSerializer;
  }

  /**
   * 初始化转换器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    this.initialized = true;
  }

  /**
   * 导入场景
   * @param data 场景数据
   * @param format 场景格式
   * @param targetScene 目标场景
   * @param options 导入选项
   * @returns Promise，解析为导入的场景
   */
  public async importScene(
    data: string | ArrayBuffer,
    format: SceneFormat,
    targetScene: Scene,
    options: SceneImportOptions = {}
  ): Promise<Scene> {
    try {
      switch (format) {
        case SceneFormat.JSON:
          return this.importFromJSON(data as string, targetScene, options);
        case SceneFormat.GLTF:
        case SceneFormat.GLB:
          return await this.importFromGLTF(data, targetScene, options);
        case SceneFormat.FBX:
          return await this.importFromFBX(data, targetScene, options);
        case SceneFormat.OBJ:
          return await this.importFromOBJ(data as string, targetScene, options);
        case SceneFormat.DAE:
          return await this.importFromDAE(data as string, targetScene, options);
        default:
          throw new Error(`不支持的场景格式: ${format}`);
      }
    } catch (error) {
      Debug.error('导入场景失败:', error);
      throw error;
    }
  }

  /**
   * 导出场景
   * @param scene 场景
   * @param options 导出选项
   * @returns Promise，解析为导出的数据
   */
  public async exportScene(
    scene: Scene,
    options: SceneExportOptions = {}
  ): Promise<string | ArrayBuffer> {
    try {
      const format = options.format || SceneFormat.JSON;

      switch (format) {
        case SceneFormat.JSON:
          return this.exportToJSON(scene, options);
        case SceneFormat.GLTF:
        case SceneFormat.GLB:
          return await this.exportToGLTF(scene, options);
        case SceneFormat.FBX:
          return await this.exportToFBX(scene, options);
        case SceneFormat.OBJ:
          return await this.exportToOBJ(scene, options);
        case SceneFormat.DAE:
          return await this.exportToDAE(scene, options);
        default:
          throw new Error(`不支持的场景格式: ${format}`);
      }
    } catch (error) {
      Debug.error('导出场景失败:', error);
      throw error;
    }
  }

  /**
   * 从GLTF导入场景
   * @param data 场景数据
   * @param targetScene 目标场景
   * @param options 导入选项
   * @returns Promise，解析为导入的场景
   */
  private async importFromGLTF(
    data: string | ArrayBuffer,
    targetScene: Scene,
    options: SceneImportOptions
  ): Promise<Scene> {
    return new Promise((resolve, reject) => {
      const loader = new GLTFLoader();

      const onLoad = (gltf: any) => {
        try {
          this.processGLTFScene(gltf, targetScene, options);
          resolve(targetScene);
        } catch (error) {
          reject(error);
        }
      };

      const onError = (error: any) => {
        reject(new Error(`GLTF导入失败: ${error.message || error}`));
      };

      if (typeof data === 'string') {
        // 如果是URL字符串
        loader.load(data, onLoad, undefined, onError);
      } else {
        // 如果是ArrayBuffer，需要转换为URL
        const blob = new Blob([data]);
        const url = URL.createObjectURL(blob);
        loader.load(url, (gltf) => {
          URL.revokeObjectURL(url);
          onLoad(gltf);
        }, undefined, (error) => {
          URL.revokeObjectURL(url);
          onError(error);
        });
      }
    });
  }

  /**
   * 从FBX导入场景
   * @param data 场景数据
   * @param targetScene 目标场景
   * @param options 导入选项
   * @returns Promise，解析为导入的场景
   */
  private async importFromFBX(
    data: string | ArrayBuffer,
    targetScene: Scene,
    options: SceneImportOptions
  ): Promise<Scene> {
    return new Promise((resolve, reject) => {
      const loader = new FBXLoader();

      const onLoad = (fbx: THREE.Object3D) => {
        try {
          this.processFBXScene(fbx, targetScene, options);
          resolve(targetScene);
        } catch (error) {
          reject(error);
        }
      };

      const onError = (error: any) => {
        reject(new Error(`FBX导入失败: ${error.message || error}`));
      };

      if (typeof data === 'string') {
        // 如果是URL字符串
        loader.load(data, onLoad, undefined, onError);
      } else {
        // FBXLoader不支持直接解析ArrayBuffer，需要转换为URL
        const blob = new Blob([data]);
        const url = URL.createObjectURL(blob);
        loader.load(url, (fbx) => {
          URL.revokeObjectURL(url);
          onLoad(fbx);
        }, undefined, (error) => {
          URL.revokeObjectURL(url);
          onError(error);
        });
      }
    });
  }

  /**
   * 从OBJ导入场景
   * @param data 场景数据
   * @param targetScene 目标场景
   * @param options 导入选项
   * @returns Promise，解析为导入的场景
   */
  private async importFromOBJ(
    data: string,
    targetScene: Scene,
    options: SceneImportOptions
  ): Promise<Scene> {
    return new Promise((resolve, reject) => {
      const loader = new OBJLoader();

      try {
        // OBJLoader的parse方法确实存在，但可能类型定义有问题
        const obj = (loader as any).parse(data) as THREE.Group;
        this.processOBJScene(obj, targetScene, options);
        resolve(targetScene);
      } catch (error) {
        reject(new Error(`OBJ导入失败: ${error.message || error}`));
      }
    });
  }

  /**
   * 从DAE导入场景
   * @param data 场景数据
   * @param targetScene 目标场景
   * @param options 导入选项
   * @returns Promise，解析为导入的场景
   */
  private async importFromDAE(
    _data: string,
    _targetScene: Scene,
    _options: SceneImportOptions
  ): Promise<Scene> {
    // DAE (Collada) 导入功能暂未实现，ColladaLoader在当前Three.js版本中可能不可用
    throw new Error('DAE导入功能暂未实现');
  }

  /**
   * 从JSON导入场景
   * @param json JSON字符串
   * @param targetScene 目标场景
   * @param options 导入选项
   * @returns 导入的场景
   */
  private importFromJSON(
    json: string,
    targetScene: Scene,
    options: SceneImportOptions
  ): Scene {
    return this.sceneSerializer.deserializeFromJSON(json, targetScene, {
      keepExistingEntities: options.keepExistingEntities,
      mergeWithExisting: options.mergeWithExisting
    });
  }

  /**
   * 导出场景为JSON
   * @param scene 场景
   * @param options 导出选项
   * @returns JSON字符串
   */
  private exportToJSON(scene: Scene, options: SceneExportOptions): string {
    return this.sceneSerializer.serializeToJSON(scene, {
      prettyPrint: options.prettyPrint,
      metadata: options.metadata
    });
  }

  /**
   * 导出场景为GLTF
   * @param scene 场景
   * @param options 导出选项
   * @returns Promise，解析为导出的数据
   */
  private async exportToGLTF(_scene: Scene, _options: SceneExportOptions): Promise<ArrayBuffer | string> {
    // GLTF导出功能暂未实现，GLTFExporter在当前Three.js版本中可能不可用
    throw new Error('GLTF导出功能暂未实现');
  }

  /**
   * 导出场景为FBX
   * @param scene 场景
   * @param options 导出选项
   * @returns Promise，解析为导出的数据
   */
  private async exportToFBX(_scene: Scene, _options: SceneExportOptions): Promise<string> {
    // FBX导出功能暂未实现，Three.js没有内置的FBX导出器
    throw new Error('FBX导出功能暂未实现');
  }

  /**
   * 导出场景为OBJ
   * @param scene 场景
   * @param options 导出选项
   * @returns Promise，解析为导出的数据
   */
  private async exportToOBJ(scene: Scene, _options: SceneExportOptions): Promise<string> {
    // 简单的OBJ导出实现
    const threeScene = scene.getThreeScene();
    let objContent = '# OBJ file generated by DL Engine\n';
    let vertexIndex = 1;

    threeScene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.geometry) {
        const geometry = object.geometry;
        const position = geometry.attributes.position;

        if (position) {
          objContent += `o ${object.name || 'Object'}\n`;

          // 导出顶点
          for (let i = 0; i < position.count; i++) {
            const x = position.getX(i);
            const y = position.getY(i);
            const z = position.getZ(i);
            objContent += `v ${x} ${y} ${z}\n`;
          }

          // 导出面
          if (geometry.index) {
            const index = geometry.index;
            for (let i = 0; i < index.count; i += 3) {
              const a = index.getX(i) + vertexIndex;
              const b = index.getX(i + 1) + vertexIndex;
              const c = index.getX(i + 2) + vertexIndex;
              objContent += `f ${a} ${b} ${c}\n`;
            }
          } else {
            for (let i = 0; i < position.count; i += 3) {
              const a = i + vertexIndex;
              const b = i + 1 + vertexIndex;
              const c = i + 2 + vertexIndex;
              objContent += `f ${a} ${b} ${c}\n`;
            }
          }

          vertexIndex += position.count;
        }
      }
    });

    return objContent;
  }

  /**
   * 导出场景为DAE
   * @param scene 场景
   * @param options 导出选项
   * @returns Promise，解析为导出的数据
   */
  private async exportToDAE(_scene: Scene, _options: SceneExportOptions): Promise<string> {
    // DAE导出功能暂未实现，Three.js没有内置的DAE导出器
    throw new Error('DAE导出功能暂未实现');
  }

  /**
   * 处理GLTF场景
   * @param gltf GLTF数据
   * @param targetScene 目标场景
   * @param options 导入选项
   */
  private processGLTFScene(gltf: any, targetScene: Scene, options: SceneImportOptions): void {
    if (!options.mergeWithExisting && !options.keepExistingEntities) {
      // 清空现有场景
      targetScene.clear();
    }

    // 添加GLTF场景到目标场景
    const threeScene = targetScene.getThreeScene();
    if (gltf.scene) {
      threeScene.add(gltf.scene);
    }

    // 处理动画
    if (options.importAnimations && gltf.animations && gltf.animations.length > 0) {
      // 这里可以添加动画处理逻辑
      Debug.log(`导入了 ${gltf.animations.length} 个动画`);
    }

    // 处理相机
    if (options.importCameras && gltf.cameras && gltf.cameras.length > 0) {
      // 这里可以添加相机处理逻辑
      Debug.log(`导入了 ${gltf.cameras.length} 个相机`);
    }

    this.emit('gltfImported', { gltf, targetScene });
  }

  /**
   * 处理FBX场景
   * @param fbx FBX数据
   * @param targetScene 目标场景
   * @param options 导入选项
   */
  private processFBXScene(fbx: THREE.Object3D, targetScene: Scene, options: SceneImportOptions): void {
    if (!options.mergeWithExisting && !options.keepExistingEntities) {
      // 清空现有场景
      targetScene.clear();
    }

    // 添加FBX对象到目标场景
    const threeScene = targetScene.getThreeScene();
    threeScene.add(fbx);

    // 处理动画
    if (options.importAnimations && fbx.animations && fbx.animations.length > 0) {
      // 这里可以添加动画处理逻辑
      Debug.log(`导入了 ${fbx.animations.length} 个动画`);
    }

    this.emit('fbxImported', { fbx, targetScene });
  }

  /**
   * 处理OBJ场景
   * @param obj OBJ数据
   * @param targetScene 目标场景
   * @param options 导入选项
   */
  private processOBJScene(obj: THREE.Group, targetScene: Scene, options: SceneImportOptions): void {
    if (!options.mergeWithExisting && !options.keepExistingEntities) {
      // 清空现有场景
      targetScene.clear();
    }

    // 添加OBJ对象到目标场景
    const threeScene = targetScene.getThreeScene();
    threeScene.add(obj);

    this.emit('objImported', { obj, targetScene });
  }


}
