/**
 * 角色控制器
 * 基于Cannon.js实现的角色控制器，提供类似于Rapier3D的角色控制器功能
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import type { PhysicsBody } from '../PhysicsBody';
import { PhysicsCollider } from '../PhysicsCollider';

/**
 * 角色控制器选项
 */
export interface CharacterControllerOptions {
  /** 偏移量 - 用于防止角色与地面发生穿透 */
  offset?: number;
  /** 最大爬坡角度（弧度） */
  maxSlopeClimbAngle?: number;
  /** 最小滑坡角度（弧度） */
  minSlopeSlideAngle?: number;
  /** 自动台阶设置 */
  autoStep?: {
    /** 最大台阶高度 */
    maxHeight: number;
    /** 最小台阶宽度 */
    minWidth: number;
    /** 是否可以踏上动态物体 */
    stepOverDynamic: boolean;
  };
  /** 地面吸附距离（设为false禁用） */
  enableSnapToGround?: number | false;
}

/**
 * 角色控制器
 * 提供角色移动、碰撞检测、斜坡爬升、自动台阶等功能
 */
export class CharacterController {
  /** 关联的实体 */
  private entity: Entity;

  /** 物理世界 */
  private world: CANNON.World;

  /** 物理体 */
  private body: PhysicsBody | null = null;

  /** 碰撞器 */
  private collider: PhysicsCollider | null = null;

  /** 偏移量 */
  private offset: number;

  /** 最大爬坡角度（弧度） */
  private maxSlopeClimbAngle: number;

  /** 最小滑坡角度（弧度） */
  private minSlopeSlideAngle: number;

  /** 自动台阶设置 */
  private autoStep: {
    enabled: boolean;
    maxHeight: number;
    minWidth: number;
    stepOverDynamic: boolean;
  };

  /** 地面吸附 */
  private snapToGround: {
    enabled: boolean;
    distance: number;
  };

  /** 计算出的移动向量 */
  private computedMovement: THREE.Vector3 = new THREE.Vector3();

  /** 是否在地面上 */
  private isGrounded: boolean = false;

  /** 上一次接触的地面法线 */
  private groundNormal: THREE.Vector3 = new THREE.Vector3(0, 1, 0);

  /** 射线检测起点偏移 */
  private raycastOffset: number = 0.05;

  /** 射线检测结果缓存 */
  private raycastResults: CANNON.RaycastResult[] = [];

  /**
   * 创建角色控制器
   * @param entity 关联的实体
   * @param world 物理世界
   * @param options 控制器选项
   */
  constructor(entity: Entity, world: CANNON.World, options: CharacterControllerOptions = {}) {
    this.entity = entity;
    this.world = world;

    // 设置选项
    this.offset = options.offset !== undefined ? options.offset : 0.01;
    this.maxSlopeClimbAngle = options.maxSlopeClimbAngle !== undefined ? options.maxSlopeClimbAngle : (60 * Math.PI) / 180;
    this.minSlopeSlideAngle = options.minSlopeSlideAngle !== undefined ? options.minSlopeSlideAngle : (30 * Math.PI) / 180;

    // 自动台阶设置
    this.autoStep = {
      enabled: !!options.autoStep,
      maxHeight: options.autoStep?.maxHeight || 0.5,
      minWidth: options.autoStep?.minWidth || 0.01,
      stepOverDynamic: options.autoStep?.stepOverDynamic || true
    };

    // 地面吸附设置
    this.snapToGround = {
      enabled: options.enableSnapToGround !== false && options.enableSnapToGround !== undefined,
      distance: typeof options.enableSnapToGround === 'number' ? options.enableSnapToGround : 0.1
    };

    // 获取物理体和碰撞器
    this.body = entity.getComponent<PhysicsBody>('PhysicsBody');
    this.collider = entity.getComponent<PhysicsCollider>('PhysicsCollider');

    if (!this.body) {
      console.warn('角色控制器需要PhysicsBody组件');
    }

    if (!this.collider) {
      console.warn('角色控制器需要PhysicsCollider组件');
    }
  }

  /**
   * 计算碰撞器移动
   * @param desiredTranslation 期望的移动向量
   * @param filterGroups 碰撞组过滤
   * @param filterPredicate 碰撞过滤谓词函数
   */
  public computeColliderMovement(
    desiredTranslation: THREE.Vector3,
    filterGroups?: number,
    filterPredicate?: (body: CANNON.Body) => boolean
  ): void {
    if (!this.body || !this.body.getCannonBody()) {
      return;
    }

    // 重置计算的移动向量
    this.computedMovement.copy(desiredTranslation);

    // 检查是否在地面上
    this.updateGroundedState();

    // 处理斜坡
    if (this.isGrounded) {
      this.handleSlopes(desiredTranslation);
    }

    // 处理自动台阶
    if (this.autoStep.enabled && this.isGrounded && desiredTranslation.y <= 0) {
      this.handleAutoStep(desiredTranslation, filterGroups, filterPredicate);
    }

    // 处理地面吸附
    if (this.snapToGround.enabled && this.isGrounded && desiredTranslation.y <= 0) {
      this.handleSnapToGround();
    }

    // 处理碰撞
    this.handleCollisions(this.computedMovement, filterGroups, filterPredicate);
  }

  /**
   * 获取计算出的移动向量
   * @returns 计算出的移动向量
   */
  public getComputedMovement(): THREE.Vector3 {
    return this.computedMovement.clone();
  }

  /**
   * 获取偏移量
   * @returns 偏移量
   */
  public getOffset(): number {
    return this.offset;
  }

  /**
   * 设置最大爬坡角度
   * @param angle 角度（弧度）
   */
  public setMaxSlopeClimbAngle(angle: number): void {
    this.maxSlopeClimbAngle = angle;
  }

  /**
   * 获取最大爬坡角度
   * @returns 角度（弧度）
   */
  public getMaxSlopeClimbAngle(): number {
    return this.maxSlopeClimbAngle;
  }

  /**
   * 设置最小滑坡角度
   * @param angle 角度（弧度）
   */
  public setMinSlopeSlideAngle(angle: number): void {
    this.minSlopeSlideAngle = angle;
  }

  /**
   * 获取最小滑坡角度
   * @returns 角度（弧度）
   */
  public getMinSlopeSlideAngle(): number {
    return this.minSlopeSlideAngle;
  }

  /**
   * 启用自动台阶
   * @param maxHeight 最大台阶高度
   * @param minWidth 最小台阶宽度
   * @param stepOverDynamic 是否可以踏上动态物体
   */
  public enableAutoStep(maxHeight: number, minWidth: number, stepOverDynamic: boolean): void {
    this.autoStep = {
      enabled: true,
      maxHeight,
      minWidth,
      stepOverDynamic
    };
  }

  /**
   * 禁用自动台阶
   */
  public disableAutoStep(): void {
    this.autoStep.enabled = false;
  }

  /**
   * 启用地面吸附
   * @param distance 吸附距离
   */
  public enableSnapToGround(distance: number): void {
    this.snapToGround = {
      enabled: true,
      distance
    };
  }

  /**
   * 禁用地面吸附
   */
  public disableSnapToGround(): void {
    this.snapToGround.enabled = false;
  }

  /**
   * 是否启用自动台阶
   * @returns 是否启用
   */
  public isAutoStepEnabled(): boolean {
    return this.autoStep.enabled;
  }

  /**
   * 是否启用地面吸附
   * @returns 是否启用
   */
  public isSnapToGroundEnabled(): boolean {
    return this.snapToGround.enabled;
  }

  /**
   * 获取自动台阶最大高度
   * @returns 最大高度
   */
  public getAutoStepMaxHeight(): number {
    return this.autoStep.maxHeight;
  }

  /**
   * 获取自动台阶最小宽度
   * @returns 最小宽度
   */
  public getAutoStepMinWidth(): number {
    return this.autoStep.minWidth;
  }

  /**
   * 是否可以踏上动态物体
   * @returns 是否可以
   */
  public canStepOverDynamic(): boolean {
    return this.autoStep.stepOverDynamic;
  }

  /**
   * 是否在地面上
   * @returns 是否在地面上
   */
  public isOnGround(): boolean {
    return this.isGrounded;
  }

  /**
   * 获取地面法线
   * @returns 地面法线
   */
  public getGroundNormal(): THREE.Vector3 {
    return this.groundNormal.clone();
  }

  /**
   * 更新是否在地面上的状态
   * @private
   */
  private updateGroundedState(): void {
    if (!this.body || !this.body.getCannonBody()) {
      this.isGrounded = false;
      return;
    }

    const cannonBody = this.body.getCannonBody()!;
    const position = cannonBody.position;

    // 创建向下的射线
    const start = new CANNON.Vec3(position.x, position.y, position.z);
    const end = new CANNON.Vec3(position.x, position.y - (this.offset + this.raycastOffset), position.z);

    // 执行射线检测
    this.raycastResults.length = 0;
    this.world.raycastAll(start, end, {}, (result) => {
      // 排除自身
      if (result.body === cannonBody) return;
      this.raycastResults.push(result);
    });

    // 检查是否有碰撞
    if (this.raycastResults.length > 0) {
      // 按距离排序
      this.raycastResults.sort((a, b) => a.distance - b.distance);

      // 获取最近的碰撞
      const result = this.raycastResults[0];

      // 更新状态
      this.isGrounded = true;
      this.groundNormal.set(result.hitNormalWorld.x, result.hitNormalWorld.y, result.hitNormalWorld.z);
    } else {
      this.isGrounded = false;
      this.groundNormal.set(0, 1, 0);
    }
  }

  /**
   * 处理斜坡
   * @param desiredTranslation 期望的移动向量
   * @private
   */
  private handleSlopes(desiredTranslation: THREE.Vector3): void {
    // 计算斜坡角度
    const slopeAngle = Math.acos(this.groundNormal.dot(new THREE.Vector3(0, 1, 0)));

    // 如果斜坡角度大于最大爬坡角度，则滑下
    if (slopeAngle > this.maxSlopeClimbAngle) {
      // 计算滑下方向
      const slideDirection = new THREE.Vector3();
      slideDirection.copy(this.groundNormal);
      slideDirection.y = 0;
      slideDirection.normalize();

      // 应用滑下
      this.computedMovement.y = -Math.sin(slopeAngle) * desiredTranslation.length();
      return;
    }

    // 如果斜坡角度大于最小滑坡角度，则部分滑下
    if (slopeAngle > this.minSlopeSlideAngle) {
      // 计算滑下比例
      const slideRatio = (slopeAngle - this.minSlopeSlideAngle) / (this.maxSlopeClimbAngle - this.minSlopeSlideAngle);

      // 应用部分滑下
      this.computedMovement.y = -Math.sin(slopeAngle) * desiredTranslation.length() * slideRatio;
    }

    // 沿着斜坡方向移动
    if (desiredTranslation.y <= 0 && slopeAngle > 0.01) {
      // 计算沿斜坡方向的分量
      const slopeDirection = new THREE.Vector3();
      slopeDirection.copy(desiredTranslation);
      slopeDirection.y = 0;
      slopeDirection.normalize();

      // 计算沿斜坡向上的分量
      const upComponent = slopeDirection.dot(this.groundNormal) * Math.sin(slopeAngle);

      // 应用沿斜坡向上的分量
      this.computedMovement.y = Math.max(this.computedMovement.y, upComponent);
    }
  }

  /**
   * 处理自动台阶
   * @param desiredTranslation 期望的移动向量
   * @param filterGroups 碰撞组过滤
   * @param filterPredicate 碰撞过滤谓词函数
   * @private
   */
  private handleAutoStep(
    desiredTranslation: THREE.Vector3,
    filterGroups?: number,
    filterPredicate?: (body: CANNON.Body) => boolean
  ): void {
    if (!this.body || !this.body.getCannonBody()) {
      return;
    }

    const cannonBody = this.body.getCannonBody()!;
    const position = cannonBody.position;

    // 计算移动方向
    const moveDir = new THREE.Vector3(desiredTranslation.x, 0, desiredTranslation.z).normalize();

    // 如果没有水平移动，则不需要处理台阶
    if (moveDir.lengthSq() < 0.0001) {
      return;
    }

    // 创建向前的射线
    const forwardStart = new CANNON.Vec3(
      position.x,
      position.y + this.raycastOffset,
      position.z
    );
    const forwardEnd = new CANNON.Vec3(
      position.x + moveDir.x * this.autoStep.minWidth,
      position.y + this.raycastOffset,
      position.z + moveDir.z * this.autoStep.minWidth
    );

    // 执行向前的射线检测
    this.raycastResults.length = 0;
    this.world.raycastAll(forwardStart, forwardEnd, {}, (result) => {
      // 排除自身
      if (result.body === cannonBody) return;

      // 如果不能踏上动态物体，则排除动态物体
      if (!this.autoStep.stepOverDynamic && result.body.type !== CANNON.BODY_TYPES.STATIC) return;

      this.raycastResults.push(result);
    });

    // 如果没有碰撞，则不需要处理台阶
    if (this.raycastResults.length === 0) {
      return;
    }

    // 按距离排序
    this.raycastResults.sort((a, b) => a.distance - b.distance);

    // 获取最近的碰撞
    const forwardHit = this.raycastResults[0];

    // 创建向上的射线
    const upStart = new CANNON.Vec3(
      forwardHit.hitPointWorld.x,
      forwardHit.hitPointWorld.y,
      forwardHit.hitPointWorld.z
    );
    const upEnd = new CANNON.Vec3(
      forwardHit.hitPointWorld.x,
      forwardHit.hitPointWorld.y + this.autoStep.maxHeight,
      forwardHit.hitPointWorld.z
    );

    // 执行向上的射线检测
    this.raycastResults.length = 0;
    this.world.raycastAll(upStart, upEnd, {}, (result) => {
      // 排除自身
      if (result.body === cannonBody) return;
      this.raycastResults.push(result);
    });

    // 如果向上有碰撞，则不能踏上台阶
    if (this.raycastResults.length > 0) {
      return;
    }

    // 创建向前向下的射线
    const downStart = new CANNON.Vec3(
      forwardHit.hitPointWorld.x + moveDir.x * this.autoStep.minWidth,
      forwardHit.hitPointWorld.y + this.autoStep.maxHeight,
      forwardHit.hitPointWorld.z + moveDir.z * this.autoStep.minWidth
    );
    const downEnd = new CANNON.Vec3(
      downStart.x,
      forwardHit.hitPointWorld.y - this.raycastOffset,
      downStart.z
    );

    // 执行向前向下的射线检测
    this.raycastResults.length = 0;
    this.world.raycastAll(downStart, downEnd, {}, (result) => {
      // 排除自身
      if (result.body === cannonBody) return;
      this.raycastResults.push(result);
    });

    // 如果没有向下的碰撞，则不能踏上台阶
    if (this.raycastResults.length === 0) {
      return;
    }

    // 按距离排序
    this.raycastResults.sort((a, b) => a.distance - b.distance);

    // 获取最近的碰撞
    const downHit = this.raycastResults[0];

    // 计算台阶高度
    const stepHeight = downHit.hitPointWorld.y - position.y;

    // 如果台阶高度在范围内，则踏上台阶
    if (stepHeight > 0 && stepHeight <= this.autoStep.maxHeight) {
      this.computedMovement.y = stepHeight;
    }
  }

  /**
   * 处理地面吸附
   * @private
   */
  private handleSnapToGround(): void {
    if (!this.body || !this.body.getCannonBody()) {
      return;
    }

    const cannonBody = this.body.getCannonBody()!;
    const position = cannonBody.position;

    // 创建向下的射线
    const start = new CANNON.Vec3(position.x, position.y, position.z);
    const end = new CANNON.Vec3(position.x, position.y - this.snapToGround.distance, position.z);

    // 执行射线检测
    this.raycastResults.length = 0;
    this.world.raycastAll(start, end, {}, (result) => {
      // 排除自身
      if (result.body === cannonBody) return;
      this.raycastResults.push(result);
    });

    // 检查是否有碰撞
    if (this.raycastResults.length > 0) {
      // 按距离排序
      this.raycastResults.sort((a, b) => a.distance - b.distance);

      // 获取最近的碰撞
      const result = this.raycastResults[0];

      // 计算吸附距离
      const snapDistance = result.distance - this.offset;

      // 应用吸附
      if (snapDistance > 0 && snapDistance < this.snapToGround.distance) {
        this.computedMovement.y = -snapDistance;
      }
    }
  }

  /**
   * 处理碰撞
   * @param movement 移动向量
   * @param filterGroups 碰撞组过滤
   * @param filterPredicate 碰撞过滤谓词函数
   * @private
   */
  private handleCollisions(
    movement: THREE.Vector3,
    filterGroups?: number,
    filterPredicate?: (body: CANNON.Body) => boolean
  ): void {
    // 这里实现碰撞检测和响应
    // 由于Cannon.js没有内置的连续碰撞检测，我们需要手动实现
    // 这是一个简化的实现，实际应用中可能需要更复杂的算法

    if (!this.body || !this.body.getCannonBody()) {
      return;
    }

    const cannonBody = this.body.getCannonBody()!;
    const position = cannonBody.position;

    // 获取碰撞器的尺寸
    let radius = 0.5; // 默认半径
    if (this.collider) {
      const shapes = this.collider.getShapes();
      if (shapes.length > 0) {
        const shape = shapes[0];
        if (shape instanceof CANNON.Sphere) {
          radius = shape.radius;
        } else if (shape instanceof CANNON.Box) {
          // 使用最小的半宽作为半径
          radius = Math.min(shape.halfExtents.x, shape.halfExtents.y, shape.halfExtents.z);
        }
      }
    }

    // 创建射线检测
    const start = new CANNON.Vec3(position.x, position.y, position.z);
    const end = new CANNON.Vec3(
      position.x + movement.x,
      position.y + movement.y,
      position.z + movement.z
    );

    // 执行射线检测
    this.raycastResults.length = 0;
    this.world.raycastAll(start, end, {}, (result) => {
      // 排除自身
      if (result.body === cannonBody) return;

      // 应用过滤器
      if (filterPredicate && !filterPredicate(result.body)) return;

      this.raycastResults.push(result);
    });

    // 如果没有碰撞，则不需要处理
    if (this.raycastResults.length === 0) {
      return;
    }

    // 按距离排序
    this.raycastResults.sort((a, b) => a.distance - b.distance);

    // 获取最近的碰撞
    const result = this.raycastResults[0];

    // 计算碰撞点到移动终点的距离
    const hitToEnd = new THREE.Vector3(
      end.x - result.hitPointWorld.x,
      end.y - result.hitPointWorld.y,
      end.z - result.hitPointWorld.z
    );

    // 计算碰撞法线
    const normal = new THREE.Vector3(
      result.hitNormalWorld.x,
      result.hitNormalWorld.y,
      result.hitNormalWorld.z
    );

    // 计算沿法线的分量
    const normalComponent = hitToEnd.dot(normal);

    // 如果沿法线的分量小于等于0，则不需要调整
    if (normalComponent <= 0) {
      return;
    }

    // 计算调整后的移动向量
    const adjustment = new THREE.Vector3();
    adjustment.copy(normal).multiplyScalar(normalComponent + this.offset);

    // 应用调整
    movement.sub(adjustment);
  }
}
