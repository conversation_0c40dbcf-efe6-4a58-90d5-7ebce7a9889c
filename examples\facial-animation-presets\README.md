# 面部动画预设和模板功能

本示例展示了面部动画预设和模板功能的使用方法，包括预设库展示、预设管理、模板导入导出以及预设预览等功能。

## 功能概述

面部动画预设和模板功能主要包括以下几个部分：

1. **预设库展示**：展示系统内置的面部动画预设，包括标准表情、文化特定表情、情感组合和动画序列等。
2. **预设管理**：支持创建、编辑、删除和导入导出预设。
3. **模板库展示**：展示系统内置的面部动画模板，如待机、说话、情感对话等。
4. **模板管理**：支持创建、编辑、删除和导入导出模板。
5. **预设预览**：实时预览预设效果，并可以应用到角色上。
6. **模板参数调整**：调整模板参数，实时预览效果，并应用到角色上。

## 预设类型

系统支持以下几种预设类型：

1. **标准表情**：基础的面部表情，如开心、悲伤、愤怒等。
2. **文化特定表情**：特定文化背景下的表情，如中国传统含蓄微笑。
3. **情感组合**：多种表情的组合，可以表达更复杂的情感。
4. **动画序列**：一系列表情的时间序列，可以表达动态的情感变化。
5. **自定义**：用户自定义的预设。

## 模板参数

模板支持以下几种参数类型：

1. **数值型**：如持续时间、表情强度等。
2. **布尔型**：如是否循环、是否启用物理等。
3. **枚举型**：如表情类型、口型类型等。
4. **字符串型**：如自定义文本等。

## 使用方法

### 预设使用

1. 在预设标签页中，选择预设类型和文化（如果适用）。
2. 搜索或浏览预设列表，点击预设卡片可以预览预设效果。
3. 在预览界面中，可以查看预设详情，并点击"应用"按钮将预设应用到角色上。

### 模板使用

1. 在模板标签页中，选择模板类别。
2. 搜索或浏览模板列表，点击模板卡片可以预览模板效果。
3. 在预览界面中，可以调整模板参数，实时预览效果，并点击"应用"按钮将模板应用到角色上。

### 预设管理

1. 点击"创建预设"按钮，填写预设信息，创建新的预设。
2. 点击预设卡片上的编辑按钮，可以编辑预设信息。
3. 点击预设卡片上的删除按钮，可以删除预设。
4. 点击"导入预设"按钮，可以导入预设文件。
5. 点击"导出预设"按钮，可以导出预设文件。

### 模板管理

1. 点击"创建模板"按钮，填写模板信息，创建新的模板。
2. 点击模板卡片上的编辑按钮，可以编辑模板信息。
3. 点击模板卡片上的删除按钮，可以删除模板。
4. 点击"导入模板"按钮，可以导入模板文件。
5. 点击"导出模板"按钮，可以导出模板文件。

## 开发指南

### 预设系统

预设系统由以下几个部分组成：

1. **FacialAnimationPresetSystem**：面部动画预设系统，负责管理预设和模板。
2. **FacialAnimationPresetManager**：面部动画预设管理器组件，提供预设管理界面。
3. **FacialAnimationTemplateManager**：面部动画模板管理器组件，提供模板管理界面。
4. **FacialAnimationPresetPanel**：面部动画预设面板，集成预设管理器和模板管理器。

### 预设数据结构

```typescript
interface FacialAnimationPreset {
  id: string;
  name: string;
  type: FacialAnimationPresetType;
  description?: string;
  tags?: string[];
  culture?: string;
  expression?: FacialExpressionType;
  weight?: number;
  expressionCombos?: { expression: FacialExpressionType, weight: number }[];
  animationSequence?: { expression: FacialExpressionType, weight: number, duration: number }[];
  author?: string;
  createdAt?: Date;
  updatedAt?: Date;
  thumbnail?: string;
}
```

### 模板数据结构

```typescript
interface FacialAnimationTemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  tags?: string[];
  parameters: FacialAnimationTemplateParameter[];
  thumbnail?: string;
  previewGif?: string;
  author?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface FacialAnimationTemplateParameter {
  id: string;
  name: string;
  type: 'number' | 'boolean' | 'enum' | 'string';
  defaultValue: any;
  min?: number;
  max?: number;
  step?: number;
  options?: { value: any; label: string }[];
}
```

### 预设应用

```typescript
// 应用预设
facialAnimationPresetSystem.applyPreset(entity, presetId);

// 应用模板
facialAnimationPresetSystem.applyTemplate(entity, templateId, parameters);
```

### 预设导入导出

```typescript
// 导出预设
const presets = facialAnimationPresetSystem.exportPresets();
const json = JSON.stringify(presets);

// 导入预设
const presets = JSON.parse(json);
facialAnimationPresetSystem.importPresets(presets);
```

## 示例代码

```typescript
// 创建面部动画预设系统
const presetSystem = new FacialAnimationPresetSystem(world, {
  debug: true,
  autoLoadPresets: true,
  defaultCulture: 'chinese'
});

// 添加系统到世界
world.addSystem(presetSystem);

// 应用预设
presetSystem.applyPreset(characterEntity, 'happy');

// 应用模板
presetSystem.applyTemplate(characterEntity, 'talk', {
  duration: 5.0,
  visemeIntensity: 1.0,
  speed: 1.0,
  randomness: 0.3,
  loop: true
});
```

## 注意事项

1. 预设和模板功能需要面部动画系统和面部动画模型适配器系统的支持。
2. 不同的模型可能需要不同的预设和模板，请确保使用适合当前模型的预设和模板。
3. 导入预设和模板时，请确保数据格式正确，否则可能导致导入失败或系统异常。
4. 预设和模板的缩略图和预览图需要手动设置，系统不会自动生成。

## 扩展功能

1. **预设组合**：支持多个预设的组合，可以创建更复杂的表情。
2. **预设序列**：支持预设的时间序列，可以创建动态的表情变化。
3. **预设分享**：支持预设的分享和社区功能，用户可以分享自己创建的预设。
4. **AI生成预设**：支持使用AI生成预设，可以根据文本描述生成面部动画。
5. **预设评分**：支持对预设进行评分和评论，帮助用户选择高质量的预设。
