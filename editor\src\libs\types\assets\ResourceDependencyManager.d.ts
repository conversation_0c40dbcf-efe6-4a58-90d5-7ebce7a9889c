/**
 * 资源依赖管理器
 * 用于管理资源之间的依赖关系
 */
import { EventEmitter } from '../utils/EventEmitter';
/**
 * 依赖类型
 */
export declare enum DependencyType {
    /** 强依赖（必须加载） */
    STRONG = "strong",
    /** 弱依赖（可选加载） */
    WEAK = "weak"
}
/**
 * 依赖信息
 */
export interface DependencyInfo {
    /** 依赖资源ID */
    id: string;
    /** 依赖类型 */
    type: DependencyType;
}
/**
 * 资源依赖管理器
 */
export declare class ResourceDependencyManager extends EventEmitter {
    /** 依赖映射（资源ID -> 依赖资源ID数组） */
    private dependencies;
    /** 反向依赖映射（被依赖资源ID -> 依赖它的资源ID数组） */
    private reverseDependencies;
    /** 是否已初始化 */
    private initialized;
    /**
     * 创建资源依赖管理器实例
     */
    constructor();
    /**
     * 初始化资源依赖管理器
     */
    initialize(): void;
    /**
     * 添加依赖关系
     * @param resourceId 资源ID
     * @param dependencyId 依赖资源ID
     * @param type 依赖类型
     */
    addDependency(resourceId: string, dependencyId: string, type?: DependencyType): void;
    /**
     * 移除依赖关系
     * @param resourceId 资源ID
     * @param dependencyId 依赖资源ID
     * @returns 是否成功移除
     */
    removeDependency(resourceId: string, dependencyId: string): boolean;
    /**
     * 获取资源的依赖
     * @param resourceId 资源ID
     * @returns 依赖信息数组
     */
    getDependencies(resourceId: string): DependencyInfo[];
    /**
     * 获取资源的强依赖
     * @param resourceId 资源ID
     * @returns 依赖资源ID数组
     */
    getStrongDependencies(resourceId: string): string[];
    /**
     * 获取资源的弱依赖
     * @param resourceId 资源ID
     * @returns 依赖资源ID数组
     */
    getWeakDependencies(resourceId: string): string[];
    /**
     * 获取依赖资源的资源
     * @param dependencyId 依赖资源ID
     * @returns 资源ID数组
     */
    getReverseDependencies(dependencyId: string): string[];
    /**
     * 获取资源的所有依赖（包括间接依赖）
     * @param resourceId 资源ID
     * @param includeWeak 是否包含弱依赖
     * @returns 依赖资源ID数组
     */
    getAllDependencies(resourceId: string, includeWeak?: boolean): string[];
    /**
     * 检查是否存在循环依赖
     * @param resourceId 资源ID
     * @returns 是否存在循环依赖
     */
    hasCircularDependency(resourceId: string): boolean;
    /**
     * 获取资源的依赖树
     * @param resourceId 资源ID
     * @param includeWeak 是否包含弱依赖
     * @returns 依赖树
     */
    getDependencyTree(resourceId: string, includeWeak?: boolean): any;
    /**
     * 获取资源的依赖排序（拓扑排序）
     * @param resourceId 资源ID
     * @param includeWeak 是否包含弱依赖
     * @returns 排序后的资源ID数组（从依赖到被依赖）
     */
    getDependencyOrder(resourceId: string, includeWeak?: boolean): string[];
    /**
     * 清空所有依赖关系
     */
    clear(): void;
    /**
     * 销毁资源依赖管理器
     */
    dispose(): void;
}
