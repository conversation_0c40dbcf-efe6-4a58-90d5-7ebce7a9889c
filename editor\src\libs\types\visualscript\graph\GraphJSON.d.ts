/**
 * 视觉脚本图形JSON数据结构
 */
/**
 * 节点元数据
 */
export interface NodeMetadata {
    /** 节点在编辑器中的X坐标 */
    positionX: number;
    /** 节点在编辑器中的Y坐标 */
    positionY: number;
    /** 节点在编辑器中的宽度 */
    width?: number;
    /** 节点在编辑器中的高度 */
    height?: number;
    /** 节点在编辑器中的颜色 */
    color?: string;
    /** 节点在编辑器中的标签 */
    label?: string;
    /** 节点在编辑器中的描述 */
    description?: string;
    /** 节点在编辑器中的折叠状态 */
    collapsed?: boolean;
    /** 节点在编辑器中的锁定状态 */
    locked?: boolean;
    /** 节点在编辑器中的可见性 */
    visible?: boolean;
    /** 节点在编辑器中的层级 */
    zIndex?: number;
    /** 自定义元数据 */
    [key: string]: any;
}
/**
 * 节点参数值
 */
export interface NodeParameterValue {
    /** 参数值 */
    value?: any;
    /** 链接到其他节点的输出 */
    link?: {
        /** 目标节点ID */
        nodeId: string;
        /** 目标节点输出插槽名称 */
        socket: string;
    };
}
/**
 * 节点参数
 */
export interface NodeParameters {
    /** 参数名称到参数值的映射 */
    [paramName: string]: NodeParameterValue;
}
/**
 * 节点流程连接
 */
export interface NodeFlowConnection {
    /** 目标节点ID */
    nodeId: string;
    /** 目标节点输入插槽名称 */
    socket: string;
}
/**
 * 节点流程
 */
export interface NodeFlows {
    /** 流程名称到流程连接的映射 */
    [flowName: string]: NodeFlowConnection;
}
/**
 * 节点JSON数据
 */
export interface NodeJSON {
    /** 节点ID */
    id: string;
    /** 节点类型 */
    type: string;
    /** 节点元数据 */
    metadata: NodeMetadata;
    /** 节点参数 */
    parameters?: NodeParameters;
    /** 节点流程 */
    flows?: NodeFlows;
}
/**
 * 变量JSON数据
 */
export interface VariableJSON {
    /** 变量ID */
    id: string;
    /** 变量名称 */
    name: string;
    /** 变量类型 */
    type: string;
    /** 变量值 */
    value: any;
    /** 变量描述 */
    description?: string;
    /** 变量是否为常量 */
    constant?: boolean;
    /** 变量是否为全局变量 */
    global?: boolean;
}
/**
 * 自定义事件JSON数据
 */
export interface CustomEventJSON {
    /** 事件ID */
    id: string;
    /** 事件名称 */
    name: string;
    /** 事件参数类型 */
    parameterTypes?: string[];
    /** 事件描述 */
    description?: string;
}
/**
 * 图形JSON数据
 */
export interface GraphJSON {
    /** 图形版本 */
    version?: string;
    /** 图形名称 */
    name?: string;
    /** 图形描述 */
    description?: string;
    /** 图形节点列表 */
    nodes: NodeJSON[];
    /** 图形变量列表 */
    variables: VariableJSON[];
    /** 图形自定义事件列表 */
    customEvents: CustomEventJSON[];
    /** 图形元数据 */
    metadata?: {
        /** 创建时间 */
        createdAt?: string;
        /** 最后修改时间 */
        updatedAt?: string;
        /** 作者 */
        author?: string;
        /** 标签 */
        tags?: string[];
        /** 缩略图 */
        thumbnail?: string;
        /** 自定义元数据 */
        [key: string]: any;
    };
}
