import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { WaterBodyComponent } from './WaterBodyComponent';
/**
 * 水体物理系统配置
 */
export interface WaterPhysicsSystemConfig {
    /** 是否启用 */
    enabled?: boolean;
    /** 是否自动更新 */
    autoUpdate?: boolean;
    /** 更新频率 */
    updateFrequency?: number;
    /** 是否启用浮力 */
    enableBuoyancy?: boolean;
    /** 是否启用阻力 */
    enableDrag?: boolean;
    /** 是否启用水流 */
    enableFlow?: boolean;
    /** 是否启用波动 */
    enableWaves?: boolean;
    /** 是否启用水体与物体碰撞 */
    enableCollision?: boolean;
    /** 是否启用水体粒子效果 */
    enableParticles?: boolean;
    /** 是否启用调试可视化 */
    enableDebugVisualization?: boolean;
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean;
    /** 是否启用空间分区 */
    enableSpatialPartitioning?: boolean;
    /** 空间分区网格大小 */
    spatialGridSize?: number;
    /** 是否启用自适应更新频率 */
    enableAdaptiveUpdate?: boolean;
    /** 最小更新频率 */
    minUpdateFrequency?: number;
    /** 最大更新频率 */
    maxUpdateFrequency?: number;
    /** 是否启用多线程计算 */
    enableMultithreading?: boolean;
    /** 工作线程数量 */
    workerCount?: number;
    /** 是否启用水流冲击力 */
    enableFlowImpact?: boolean;
    /** 是否启用水体分裂 */
    enableWaterSplitting?: boolean;
}
/**
 * 水体物理系统事件类型
 */
export declare enum WaterPhysicsSystemEventType {
    /** 物体进入水体 */
    OBJECT_ENTER_WATER = "object_enter_water",
    /** 物体离开水体 */
    OBJECT_EXIT_WATER = "object_exit_water",
    /** 水体波动 */
    WATER_WAVE = "water_wave",
    /** 水体流动 */
    WATER_FLOW = "water_flow",
    /** 水体碰撞 */
    WATER_COLLISION = "water_collision",
    /** 水流冲击 */
    WATER_FLOW_IMPACT = "water_flow_impact",
    /** 水体分裂 */
    WATER_SPLITTING = "water_splitting"
}
/**
 * 水体物理系统
 */
export declare class WaterPhysicsSystem extends System {
    /** 配置 */
    private config;
    /** 物理系统 */
    private physicsSystem;
    /** 水体组件映射 */
    private waterBodies;
    /** 帧计数器 */
    private frameCount;
    /** 事件发射器 */
    private eventEmitter;
    /** 性能监视器 */
    private performanceMonitor;
    /** 调试渲染器 */
    private debugRenderer;
    /** 空间分区网格 */
    private spatialGrid;
    /** 上一帧的性能数据 */
    private lastFramePerformance;
    /** 当前更新频率 */
    private currentUpdateFrequency;
    /** 工作线程池 */
    private workerPool;
    /** 是否支持Web Worker */
    private supportsWorkers;
    /**
     * 构造函数
     * @param config 配置
     */
    constructor(config?: WaterPhysicsSystemConfig);
    /**
     * 初始化系统
     */
    initialize(): void;
    /**
     * 初始化工作线程池
     */
    private initializeWorker;
    /**
     * 初始化空间网格
     */
    private initializeSpatialGrid;
    /**
     * 获取网格坐标
     * @param position 位置
     * @param size 大小
     * @returns 网格坐标数组
     */
    private getGridCoordinates;
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    update(deltaTime: number): Promise<void>;
    /**
     * 调整更新频率
     */
    private adjustUpdateFrequency;
    /**
     * 添加水体组件
     * @param entity 实体
     * @param component 水体组件
     */
    addWaterBody(entity: Entity, component: WaterBodyComponent): void;
    /**
     * 移除水体组件
     * @param entity 实体
     */
    removeWaterBody(entity: Entity): void;
    /**
     * 添加水体到空间网格
     * @param entityId 实体ID
     * @param waterBody 水体组件
     */
    private addWaterBodyToSpatialGrid;
    /**
     * 从空间网格中移除水体
     * @param entityId 实体ID
     */
    private removeWaterBodyFromSpatialGrid;
    /**
     * 根据水体组件获取实体ID
     * @param waterBody 水体组件
     * @returns 实体ID
     */
    private getEntityIdByWaterBody;
    /**
     * 更新所有水体
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterBodies;
    /**
     * 使用空间分区更新水体
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterBodiesWithSpatialPartitioning;
    /**
     * 更新水体
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterBody;
    /**
     * 更新水体波动
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterWaves;
    /**
     * 更新高度图
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateHeightMap;
    /**
     * 在主线程中更新高度图
     * @param waterBody 水体组件
     * @param heightMap 高度图
     * @param waveParams 波动参数
     * @param size 尺寸
     * @param resolution 分辨率
     * @param time 时间
     */
    private updateHeightMapInMainThread;
    /**
     * 更新法线图
     * @param waterBody 水体组件
     */
    private updateNormalMap;
    /**
     * 在主线程中更新法线图
     * @param waterBody 水体组件
     * @param heightMap 高度图
     * @param normalMap 法线图
     * @param resolution 分辨率
     */
    private updateNormalMapInMainThread;
    /**
     * 更新水流
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterFlow;
    /**
     * 在主线程中更新水流
     * @param waterBody 水体组件
     * @param velocityMap 速度图
     * @param flowDirection 流向
     * @param flowSpeed 流速
     * @param resolution 分辨率
     */
    private updateWaterFlowInMainThread;
    /**
     * 更新浮力和阻力
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateBuoyancyAndDrag;
    /**
     * 更新水体碰撞
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterCollision;
    /**
     * 创建水花
     * @param waterBody 水体组件
     * @param position 位置
     * @param speed 速度
     * @param objectSize 物体尺寸
     */
    private createWaterSplash;
    /**
     * 创建水波纹
     * @param waterBody 水体组件
     * @param position 位置
     * @param speed 速度
     * @param objectSize 物体尺寸
     */
    private createWaterRipple;
    /**
     * 创建水体分裂效果
     * @param waterBody 水体组件
     * @param position 位置
     * @param speed 速度
     * @param objectSize 物体尺寸
     */
    private createWaterSplitting;
    /**
     * 更新水体粒子效果
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间（秒）
     */
    private updateWaterParticles;
    /**
     * 获取水体范围内的物体
     * @param waterBody 水体组件
     * @returns 物体数组
     */
    private getObjectsInWater;
    /**
     * 应用浮力
     * @param obj 物体
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间
     */
    private applyBuoyancy;
    /**
     * 应用阻力
     * @param obj 物体
     * @param waterBody 水体组件
     * @param deltaTime 帧间隔时间
     */
    private applyDrag;
    /**
     * 检测水体碰撞
     * @param waterBody 水体组件
     * @returns 碰撞数组
     */
    private detectWaterCollisions;
    /**
     * 处理水体碰撞
     * @param collision 碰撞信息
     * @param deltaTime 帧间隔时间
     */
    private handleWaterCollision;
    /**
     * 计算法线图
     * @param heightMap 高度图
     * @param resolution 分辨率
     * @returns 法线图
     */
    private calculateNormalMap;
}
